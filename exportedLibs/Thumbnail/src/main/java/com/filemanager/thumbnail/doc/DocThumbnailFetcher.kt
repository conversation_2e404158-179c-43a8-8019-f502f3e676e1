/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.doc
 ** Version: 1.0
 ** Date: 2021/5/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.doc

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Rect
import android.net.Uri
import android.os.SystemClock
import com.bumptech.glide.Priority
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.data.DataFetcher
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.utils.Log
import com.filemanager.thumbnail.utils.PreferencesUtils
import com.filemanager.thumbnail.utils.logInfo
import java.io.FileDescriptor
import java.io.IOException
import kotlin.jvm.Throws
import kotlin.math.max

/**
 * the real fetcher of Extracting doc's thumbnail
 */
internal class DocThumbnailFetcher(
    private val context: Context,
    private val thumbModel: DocThumbnail,
    private val width: Int,
    private val height: Int
) : DataFetcher<Bitmap> {

    private companion object {
        private const val TAG = "DocThumbnailFetcher"
        private const val ONLY_RETRIEVE_FROM_CACHE = "OnlyRetrieveFromCache"
        private const val SP_KEY_FIRST_TIME_CLEAR_RETRIEVE_CACHE =
            "sp_key_first_time_clear_retrieve_cache"
    }

    private val loaderImpl by lazy { DocThumbnailLoaderFactory.getInstance(context) }

    @Volatile
    private var isCancelled = false
    private var bitmap: Bitmap? = null
    private var firstTimeLoad: Boolean = true

    override fun getDataClass(): Class<Bitmap> {
        return Bitmap::class.java
    }

    override fun cleanup() {
        bitmap?.recycle()
    }

    override fun getDataSource(): DataSource {
        return DataSource.REMOTE
    }

    override fun cancel() {
        isCancelled = true
        loaderImpl?.cancel(thumbModel)
        bitmap = null
    }

    override fun loadData(priority: Priority, callback: DataFetcher.DataCallback<in Bitmap>) {
        val loader = loaderImpl ?: run {
            callback.onLoadFailed(UnsupportedOperationException("Thumbnail is not supported"))
            return
        }
        if (isCancelled) {
            Log.d(TAG, "loadData: isCancelled: ${thumbModel.uri.logInfo()}")
            //return null when request has been cancelled
            callback.onDataReady(null)
            return
        }
        if (!onShortcutCheck(thumbModel, callback)) {
            Log.d(TAG, "loadData: shortcut check false: ${thumbModel.uri.logInfo()}")
            callback.onDataReady(null)
            return
        }
        realLoadData(loader, callback)
    }

    private fun onShortcutCheck(
        thumbModel: DocThumbnail,
        callback: DataFetcher.DataCallback<in Bitmap>
    ): Boolean {
        if (!thumbModel.isShortcut) {
            // 后续缓存判断逻辑仅针对快捷方式，非快捷方式的缩略图获取忽略
            return true
        }
        val firstTimeClearRetrieveCache = PreferencesUtils.getBoolean(
            context,
            ONLY_RETRIEVE_FROM_CACHE,
            SP_KEY_FIRST_TIME_CLEAR_RETRIEVE_CACHE,
            true
        )
        if (firstTimeClearRetrieveCache) {
            /**
             * 1. DocThumbnail 混淆后的类名可能会发生改变，导致计算出的缓存key和已存在的不一致，
             *    重写 DocThumbnail 的 toString，确保计算生成的缓存key不受类名影响；
             * 2. 修改后，新版本的缓存key和之前版本的不一致，需要对之前保存已加载的sp数据做一次清除，否则不会重新加载。
             */
            PreferencesUtils.clear(context, ONLY_RETRIEVE_FROM_CACHE)
            PreferencesUtils.putBoolean(
                context,
                ONLY_RETRIEVE_FROM_CACHE,
                SP_KEY_FIRST_TIME_CLEAR_RETRIEVE_CACHE,
                false
            )
        }
        val logMsg =
            "firstTimeLoad=$firstTimeLoad, firstTimeClearRetrieveCache=$firstTimeClearRetrieveCache"
        Log.d(TAG, "onShortcutCheck: $logMsg")
        val onlyRetrieveFromCache = PreferencesUtils.getBoolean(
            context,
            ONLY_RETRIEVE_FROM_CACHE,
            thumbModel.hashCode().toString(),
            false
        )
        //非首次加载 或已加载过， 就不再执行调用接口获取缩略图。
        if (firstTimeLoad.not() || onlyRetrieveFromCache) {
            callback.onDataReady(null)
            return false
        }
        firstTimeLoad = false
        PreferencesUtils.putBoolean(
            context,
            ONLY_RETRIEVE_FROM_CACHE,
            thumbModel.hashCode().toString(),
            true
        )
        return true
    }

    private fun realLoadData(
        loader: IDocThumbnailLoader,
        callback: DataFetcher.DataCallback<in Bitmap>
    ) {
        Log.d(TAG, "realLoadData: ${thumbModel.uri.logInfo()}")
        val startLoadTime = SystemClock.uptimeMillis()
        loader.loadThumbnail(thumbModel, width, height, object : IDocThumbnailCallback {
            override fun onDataReady(uri: Uri) {
                if (isCancelled) {
                    Log.d(TAG, "realLoadData: onDataReady: cancelled")
                    callback.onDataReady(null)
                    return
                }
                val loadTime = SystemClock.uptimeMillis() - startLoadTime
                Log.d(TAG, "realLoadData: onDataReady: loadTime=$loadTime, ${uri.logInfo()}")
                kotlin.runCatching {
                    val startReadTime = SystemClock.uptimeMillis()
                    context.contentResolver.openFileDescriptor(uri, "r")?.use { pfd ->
                        val fd = pfd.fileDescriptor
                        var sampleSize: Int? = null
                        if (loaderImpl?.shallSampleBitmap == true) {
                            sampleSize = calculateSampleSize(fd)
                        }
                        Log.d(TAG, "realLoadData: onDataReady: sampleSize=$sampleSize")
                        bitmap = obtainThumbnail(fd, sampleSize)
                        if (bitmap != null) {
                            val readTime = SystemClock.uptimeMillis() - startReadTime
                            val sizeMsg = bitmap?.run { "bitmap.size=${width}x$height" }
                            Log.d(TAG, "realLoadData: onDataReady: readTime=$readTime ,$sizeMsg")
                            callback.onDataReady(bitmap)
                        } else {
                            callback.onLoadFailed(IOException("$TAG: Get Bitmap from uri is null"))
                        }
                    }
                }.onFailure {
                    Log.d(TAG, "realLoadData: onDataReady: decode ERROR! $it")
                    callback.onLoadFailed(IOException(it))
                }
            }

            override fun onLoadFailed(e: Throwable) {
                if (isCancelled) {
                    Log.d(TAG, "realLoadData: onLoadFailed: cancelled")
                    callback.onDataReady(null)
                    return
                }
                Log.e(TAG, "realLoadData: onLoadFailed: $e")
                callback.onLoadFailed(e as? Exception ?: Exception(e))
            }
        })
    }

    /**
     * 永中缩略图方案不支持设置尺寸参数，默认返回一个分辨率比较大的图像，超出了一般缩略图需求的尺寸。
     * 因此需要先计算采样率，减小读出来的位图大小。
     *
     * @param fd 返回的永中缩略图的读取FD
     * @return [BitmapFactory.Options.inSampleSize]
     */
    @Throws(IOException::class)
    private fun calculateSampleSize(fd: FileDescriptor): Int {
        val boundsOpt =
            BitmapFactory.Options().apply { inJustDecodeBounds = true }
        BitmapFactory.decodeFileDescriptor(fd, null, boundsOpt)
        val scale = boundsOpt.run {
            if (outWidth > outHeight) (outWidth / width) else (outHeight / height)
        }
        return max(1, scale)
    }

    /**
     * 永中缩略图方案不支持按指定的分辨率比例适应填充缩略图，返回的图像的尺寸比例可能与需求的不同。
     * 因此在尺寸比例不符的情况下，需要重新以适应填充的方式绘制为需求尺寸的图像后再继续使用。
     *
     * @param fd 返回的永中缩略图的读取FD
     * @param sampleSize 永中缩略图的读取采样率，为null时直接使用原图（使用WPS方案时）
     * @return 缩略图Bitmap
     */
    @Throws(IOException::class)
    private fun obtainThumbnail(fd: FileDescriptor, sampleSize: Int?): Bitmap? {
        val decodeOpt = sampleSize?.let {
            BitmapFactory.Options().apply { inSampleSize = it }
        }
        val decodeBmp = BitmapFactory.decodeFileDescriptor(fd, null, decodeOpt) ?: return null
        val isSizeMatched =
            sampleSize == null || (decodeBmp.width == width && decodeBmp.height == height)
        if (!ThumbnailManager.DocThumbnailConfigs.fillToFullRect || isSizeMatched) {
            // 未启用填充，非永中方案，或当前尺寸已符合需求，则直接使用原图
            return decodeBmp
        }
        val redrawBmp = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(redrawBmp)
        // 以居中适应的方式重新绘制
        val srcRect = Rect(0, 0, decodeBmp.width, decodeBmp.height)
        val dstRect = if (decodeBmp.width >= decodeBmp.height) {
            val inCanvasHeight = decodeBmp.height * width / decodeBmp.width
            val inCanvasTop = (height - inCanvasHeight) / 2
            val inCanvasBottom = inCanvasTop + inCanvasHeight
            Rect(0, inCanvasTop, width, inCanvasBottom)
        } else {
            val inCanvasWidth = decodeBmp.width * height / decodeBmp.height
            val inCanvasLeft = (width - inCanvasWidth) / 2
            val inCanvasRight = inCanvasLeft + inCanvasWidth
            Rect(inCanvasLeft, 0, inCanvasRight, height)
        }
        canvas.drawColor(ThumbnailManager.DocThumbnailConfigs.fillEmptyColor)
        canvas.drawBitmap(decodeBmp, srcRect, dstRect, null)
        decodeBmp.recycle()
        Log.d(TAG, "obtainThumbnail: redraw, $srcRect to $dstRect")
        return redrawBmp
    }
}
