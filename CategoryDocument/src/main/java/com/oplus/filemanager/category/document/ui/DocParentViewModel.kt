/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DocParentViewModel
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2022/10/27       1      create
 ***********************************************************************/
package com.oplus.filemanager.category.document.ui

import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.StatisticsUtils

class DocParentViewModel : BaseViewModel() {

    companion object {
        private const val DOC_SCAN_MODE_SP_KEY = "doc_scan_mode"
    }

    val mBrowseModeState by lazy {
        val lastScanMode = ConfigSharedPreferenceUtils.getInt(DOC_SCAN_MODE_SP_KEY, 0)
        MutableLiveData(
            if (lastScanMode == 0) {
                KtConstants.SCAN_MODE_LIST
            } else {
                lastScanMode
            }
        )
    }

    fun clickScanModeItem(context: Context? = null) {
        if (mBrowseModeState.value == KtConstants.SCAN_MODE_LIST) {
            mBrowseModeState.value = KtConstants.SCAN_MODE_GRID
            StatisticsUtils.onCommon(context, StatisticsUtils.SCAN_MODE_DOC_SWITCH, hashMapOf(StatisticsUtils.SCAN_MODE_DOC_SWITCH to "0"))
        } else {
            mBrowseModeState.value = KtConstants.SCAN_MODE_LIST
            StatisticsUtils.onCommon(context, StatisticsUtils.SCAN_MODE_DOC_SWITCH, hashMapOf(StatisticsUtils.SCAN_MODE_DOC_SWITCH to "1"))
        }
        mBrowseModeState.value?.apply {
            ConfigSharedPreferenceUtils.putInt(DOC_SCAN_MODE_SP_KEY, this)
        }
    }
}