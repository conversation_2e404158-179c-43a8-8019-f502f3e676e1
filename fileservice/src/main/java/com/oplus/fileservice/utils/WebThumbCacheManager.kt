/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WebThumbCacheManager
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/5 16:31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/5       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.utils

import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import androidx.core.graphics.scale
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.signature.ObjectKey
import com.filemanager.common.DiskLruCache
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.imageloader.glide.DrmCover
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Md5Utils
import com.filemanager.common.utils.Utils
import com.oplus.fileservice.R
import com.oplus.fileservice.bean.WebAlbumSetBean
import com.oplus.fileservice.bean.WebFileBean
import kotlinx.coroutines.CancellationException
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.supervisorScope
import kotlin.coroutines.CoroutineContext

object WebThumbCacheManager {
    const val TAG = "WebThumbCacheManager"
    private const val SIZE_WITH_HEIGHT = 68
    private const val PNG_QUALITY = 100
    private const val CACHE_JPG = ".jpg"
    private const val CACHE_DIRECTORY = ".WebThumbnail"
    private const val THUMBNAIL_MAX_SIZE = (20 * 1024 * 1024).toLong()

    @Volatile
    var mInternalPath: String? = null
        get() {
            if (field == null) {
                field = VolumeEnvironment.getInternalSdPath(appContext)
            }
            return field
        }

    @Synchronized
    fun setThumbnailData(
        webFileBeans: List<WebFileBean>,
        context: CoroutineContext = Dispatchers.IO
    ) {
        val diskLruCache = getDiskLruCache(appContext)
        if (diskLruCache == null) {
            Log.d(TAG, "storeWebThumbnail diskLruCache is null")
            return
        }
        runBlocking {
            webFileBeans.map { webFileBean ->
                async(context) {
                    webFileBean.mThumbnailData = obtainThumbnailBytes(diskLruCache, webFileBean)
                }
            }.awaitAll()
        }
        try {
            diskLruCache.close()
        } catch (e: IOException) {
            Log.e(TAG, "storeWebThumbnails diskLruCache close fail $e ")
        }
    }

    fun setAlbumSetThumbnailData(
        webAlbumSetBeans: List<WebAlbumSetBean>,
        context: CoroutineContext = Dispatchers.IO
    ) {
        val diskLruCache = getDiskLruCache(appContext)
        if (diskLruCache == null) {
            Log.d(TAG, "setAlbumSetThumbnailData diskLruCache is null")
            return
        }
        runBlocking {
            webAlbumSetBeans.map { webAlbumSetBean ->
                async(context) {
                    val fileBean = covertAlbumItemToWebFileBean(webAlbumSetBean, appContext)
                    Log.d(TAG, "setAlbumSetThumbnailData -> fileBean = $fileBean")
                    webAlbumSetBean.mThumbnailData = obtainThumbnailBytes(diskLruCache, fileBean)
                }
            }.awaitAll()
        }
        try {
            diskLruCache.close()
        } catch (e: IOException) {
            Log.e(TAG, "storeWebThumbnails diskLruCache close fail $e ")
        }
    }

    suspend fun preGeneratedThumbnails(webFileBeans: List<WebFileBean>, context: CoroutineContext = Dispatchers.IO) {
        try {
            supervisorScope {
                val diskLruCache = getDiskLruCache(appContext)
                if (diskLruCache == null) {
                    Log.d(TAG, "storeWebThumbnail diskLruCache is null")
                    return@supervisorScope
                }
                webFileBeans.map { webFileBean ->
                    async(context) {
                        webFileBean.mFileName?.let {
                            val cacheName = getCacheName(
                                fileId = webFileBean.mFileSize,
                                fileName = it,
                                lastModifyTime = webFileBean.mDateModified
                            )
                            val cacheFile = getCacheThumbnail(cacheName)
                            val cacheExist = cacheFile.exists()
                            if (!cacheExist && isSupportThumbnail(webFileBean)) {
                                getThumbnailBitmapToGlide(webFileBean)?.let { bitmap ->
                                    addToDisk(cacheName, bitmap, diskLruCache)
                                } ?: Log.d(TAG, "preGenerated thumbnail failure $it")
                            } else {
                                Log.i(TAG, "cacheFile is not exist or not SupportThumbnail!,cacheName = $cacheName, " +
                                        "mLocalType = ${webFileBean.mLocalType}")
                            }
                        } ?: Log.d(TAG, "$webFileBean fileName is null")
                    }
                }.awaitAll()
                Log.d(TAG, "preGeneratedThumbnails -> end")
            }
        } catch (e: CancellationException) {
            Log.e("PreGenerated Thumbnails error: ", e)
        }
    }

    private fun isSupportThumbnail(webFileBean: WebFileBean): Boolean {
        return webFileBean.mLocalType == MimeTypeHelper.VIDEO_TYPE || webFileBean.mLocalType == MimeTypeHelper.IMAGE_TYPE
    }

    private fun covertAlbumItemToWebFileBean(file: WebAlbumSetBean, context: Context): WebFileBean {
        return WebFileBean().apply {
            if (file.key == context.getString(R.string.album_set_card_case_path)) {
                mFileName = context.resources.getString(com.filemanager.common.R.string.card_case)
                mLocalType = MimeTypeHelper.ALBUM_SET_TYPE_CARD_CASE
                file.bucketName = mFileName
            } else {
                mFileName = file.bucketName
                mLocalType = file.type
            }
            mFilePath = file.coverPath
            mFileSize = file.coverPath?.let { File(it).length() } ?: 0L
            mDateModified = file.dataModified
        }
    }

    private fun obtainThumbnailBytes(diskLruCache: DiskLruCache, webFileBean: WebFileBean): ByteArray? {
        webFileBean.mFileName?.let {
            val cacheName = getCacheName(
                webFileBean.mFileSize,
                it,
                webFileBean.mDateModified
            )
            val cacheFile = getCacheThumbnail(cacheName)
            val cacheFileExist = cacheFile.exists()
            val result = getThumbnailData(saveBitmap = !cacheFileExist, cacheName, webFileBean, diskLruCache)
            if (result == null) {
                Log.d(TAG, "setThumbnail -> cacheName = $cacheName; ${cacheFile.name}; $cacheFileExist")
            }
            return result
        } ?: return null
    }

    private fun getThumbnailData(
        saveBitmap: Boolean = false,
        cacheName: String,
        webFileBean: WebFileBean,
        diskLruCache: DiskLruCache
    ): ByteArray? {
        var buffer: ByteArray? = null
        if (saveBitmap) {
            when (webFileBean.mLocalType) {
                MimeTypeHelper.VIDEO_TYPE, MimeTypeHelper.IMAGE_TYPE -> {
                    val bitmap = getThumbnailBitmapToGlide(webFileBean)
                    if (bitmap != null) {
                        buffer = addToDisk(cacheName, bitmap, diskLruCache)
                    }
                }
                MimeTypeHelper.APPLICATION_TYPE -> {
                    if (!webFileBean.mFilePath.isNullOrEmpty()) {
                        buffer =
                            saveWebApkThumbnail(webFileBean.mFilePath!!, cacheName, diskLruCache)
                    }
                }
            }
        } else {
            buffer = getThumbnailDataFromDiskCache(cacheName, diskLruCache)
        }
        return buffer
    }

    private fun getCacheThumbnail(cacheName: String): File {
        val directory = getCacheDirectory()
        return File(directory, Md5Utils.toKey(cacheName) + "." + 0)
    }

    private fun getCacheName(fileId: Long?, fileName: String, lastModifyTime: Long): String {
        return "${fileId}_${fileName}_$lastModifyTime$CACHE_JPG"
    }

    private fun getDiskLruCache(context: Context): DiskLruCache? {
        val directory = getCacheDirectory()
        if (!directory.exists() || directory.isFile) {
            return null
        }
        val version = getAppVersion(context)
        val diskLruCache: DiskLruCache? = try {
            DiskLruCache.open(directory, version, 1, THUMBNAIL_MAX_SIZE)
        } catch (e: IOException) {
            Log.e(TAG, e.message)
            null
        }
        return diskLruCache
    }

    @SuppressLint("SdCardPath")
    private fun getCacheDirectory(): File {
        val path = VolumeEnvironment.getDataDirPath(appContext, CACHE_DIRECTORY)
        val file = File(path + File.separator + WebFileConstant.WEB_THUMBNAIL_DIRECTORY)
        if ((!file.exists()) && (!file.mkdirs())) {
            Log.e(TAG, "mkdirs error")
        }
        return file
    }

    private fun getAppVersion(context: Context): Int {
        val manager = context.packageManager
        try {
            val info = manager.getPackageInfo(context.packageName, 0)
            return info.longVersionCode.toInt()
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, e.message)
        }
        return 1
    }

    @Suppress("TooGenericExceptionCaught")
    private fun addToDisk(path: String?, bmp: Bitmap, diskLruCache: DiskLruCache): ByteArray? {
        var outputStream: ByteArrayOutputStream? = null
        var buf: ByteArray? = null
        var out: OutputStream? = null
        try {
            outputStream = ByteArrayOutputStream()
            bmp.compress(Bitmap.CompressFormat.PNG, PNG_QUALITY, outputStream)
            buf = outputStream.toByteArray()
            val editor = diskLruCache.edit(Md5Utils.toKey(path))
            if (editor != null) {
                out = editor.newOutputStream(0)
                out.write(buf)
                out.flush()
                editor.commit()
            }
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        } finally {
            try {
                outputStream?.close()
                out?.close()
            } catch (e: Exception) {
                Log.e(TAG, e.message)
            }
        }
        return buf
    }

    @Suppress("TooGenericExceptionCaught")
    private fun saveWebApkThumbnail(
        path: String,
        cacheName: String,
        diskLruCache: DiskLruCache
    ): ByteArray? {
        var buffer: ByteArray? = null
        try {
            val appInfo = getAppInfoByPathIgnoreDarkTheme(appContext, path)
            var thumbnail = appInfo.icon

            Log.d(TAG, "addToDisk cacheName$cacheName ,path=$path")
            if (thumbnail != null) {
                thumbnail = thumbnail.scale(SIZE_WITH_HEIGHT, SIZE_WITH_HEIGHT, true)
                buffer = addToDisk(cacheName, thumbnail, diskLruCache)
            } else {
                Log.d(TAG, "addToDisk fail, getApp Icon is null")
            }
        } catch (e: Exception) {
            Log.d(TAG, "e ${e.message}")
        }
        return buffer
    }

    @Suppress("TooGenericExceptionCaught")
    private fun getThumbnailBitmapToGlide(webFileBean: WebFileBean): Bitmap? {
        if (webFileBean.mFilePath.isNullOrEmpty()) {
            return null
        }
        var options = RequestOptions()
        val stringSignature =
            webFileBean.mDateModified.toString() + webFileBean.mFileSize + "" + Utils.isRtl()
        val context = appContext
        options = options.diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .signature(ObjectKey(stringSignature))
        var drmPath: String? = null
        if (webFileBean.mLocalType == MimeTypeHelper.DRM_TYPE) {
            drmPath = webFileBean.mFilePath
        }
        val path = Uri.fromFile(File(webFileBean.mFilePath!!))
        options = options.override(SIZE_WITH_HEIGHT).transform(CenterCrop())

        var bitmap: Bitmap? = null
        try {
            Log.d(TAG, "getThumbnailBitmapToGlide -> drmPath = $drmPath")
            val loadBuilder = Glide.with(context)
                .asBitmap()
                .load(
                    if (drmPath.isNullOrEmpty()) {
                        path
                    } else {
                        DrmCover(drmPath)
                    }
                )
            val loaderFeature =
                loadBuilder.apply(options).submit(SIZE_WITH_HEIGHT, SIZE_WITH_HEIGHT)
            bitmap = loaderFeature.get()
            Glide.with(context).clear(loaderFeature)
        } catch (e: Exception) {
            Log.w(TAG, "display: Glide with " + e.message)
        }
        return bitmap
    }

    private fun getThumbnailDataFromDiskCache(
        cacheName: String,
        diskLruCache: DiskLruCache
    ): ByteArray? {
        var inputStream: InputStream? = null
        try {
            val snapshot = diskLruCache.get(Md5Utils.toKey(cacheName))
            if (snapshot != null) {
                inputStream = snapshot.getInputStream(0)
                if (inputStream != null) {
                    val bytes = ByteArray(inputStream.available())
                    inputStream.read(bytes)
                    snapshot.close()
                    return bytes
                } else {
                    snapshot.close()
                }
            }
        } catch (e: IOException) {
            Log.e(TAG, e.message)
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close()
                } catch (e: IOException) {
                    // do nothing
                }
            }
        }
        return null
    }
}