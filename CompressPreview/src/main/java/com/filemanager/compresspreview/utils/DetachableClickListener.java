/**************************************************************
* Copyright (c)  2016- 2017  Oplus. All rights reserved.
*File       : DetachableClickListener.java
*Description: OnClickListener for Compress and Decompress, Used to avoid OOM of the OnClickListener.
*Version   : 1.0
*Date      : 2017-8-28
*Author    : <PERSON>.<EMAIL>
*---------------- Revision History: --------------------------
*   <version>   <date>      < author >  <desc>
* Revision 1.0 2017-8-28,  <EMAIL>  First Version of this File.
*
****************************************************************/
package com.filemanager.compresspreview.utils;

import android.app.Dialog;
import android.content.DialogInterface;
import android.view.ViewTreeObserver.OnWindowAttachListener;

public final class DetachableClickListener implements DialogInterface.OnClickListener {
    private DialogInterface.OnClickListener mDelegateOrNull;

    public static DetachableClickListener wrap(DialogInterface.OnClickListener delegate) {
        return new DetachableClickListener(delegate);
    }

    private DetachableClickListener(DialogInterface.OnClickListener delegate) {
        mDelegateOrNull = delegate;
    }

    @Override
    public void onClick(DialogInterface dialog, int which) {
        if (mDelegateOrNull != null) {
            mDelegateOrNull.onClick(dialog, which);
        }
    }

    public void clearOnDetach(Dialog dialog) {
        dialog.getWindow().getDecorView().getViewTreeObserver().addOnWindowAttachListener(new OnWindowAttachListener() {
            @Override
            public void onWindowAttached() {
                // do nothing
            }

            @Override
            public void onWindowDetached() {
                mDelegateOrNull = null;
            }
        });
    }
}