/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MainCategoryViewModelTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/4/20 14:20
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/4/20       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.main.ui.category

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.CommonConstants.CLEANUP_FUNCTION_SHOW
import com.filemanager.common.constants.KtConstants.STATE_CHECKING
import com.filemanager.common.constants.KtConstants.STATE_MOUNTED
import com.filemanager.common.constants.KtConstants.STATE_UNMOUNTED
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.EncryptViewUtils
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.interfaze.clouddrive.ICloudDrive
import com.oplus.filemanager.main.ui.uistate.Storage
import com.oplus.filemanager.main.ui.uistate.StorageUiState
import com.oplus.filemanager.main.ui.usecase.GetApkCountInfoUseCase
import io.mockk.InternalPlatformDsl
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.unmockkStatic
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.launch
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestResult
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import kotlin.time.Duration.Companion.seconds

@OptIn(ExperimentalCoroutinesApi::class)
class MainCategoryViewModelTest {

    @MockK
    lateinit var context: Context
    private val dispatcher = StandardTestDispatcher()

    private val cloudDrive = mockk<ICloudDrive>()

    private val koinApp = koinApplication {
        modules(module {
            single { cloudDrive }
        })
    }

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { contentResolver }.returns(mockk(relaxed = true))
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
        mockkStatic(PreferencesUtils::class)
        Dispatchers.setMain(dispatcher)
        startKoin(koinApp)
    }

    @After
    fun tearDown() {
        unmockkAll()
        unmockkStatic(PreferencesUtils::class)
        Dispatchers.resetMain()
        stopKoin()
    }

    @Test
    fun testReportApkCountInfo() {
        val getApkCountInfo = mockk<GetApkCountInfoUseCase>()
        mockkStatic(StatisticsUtils::class)
        val mainCategoryViewModel = MainCategoryViewModel()
        every { PreferencesUtils.getLong(any(), any(), any()) }.returns(System.currentTimeMillis())
        mainCategoryViewModel.reportApkCountInfo(false)
        verify(inverse = true) { PreferencesUtils.getLong(any(), any(), any()) }
        every { getApkCountInfo.invoke() }.returns(Pair(1, 1))
        mainCategoryViewModel.reportApkCountInfo(true)
        verify(inverse = true) { getApkCountInfo.invoke() }
        unmockkStatic(StatisticsUtils::class)
    }

    @Test
    fun testUploadReloadData(): TestResult = runTest(timeout = testCaseTimeout) {
        val mainCategoryViewModel = MainCategoryViewModel()
        Assert.assertFalse(mainCategoryViewModel.reloadData.value)
        mainCategoryViewModel.updateReloadData()
        Assert.assertTrue(mainCategoryViewModel.reloadData.value)
    }

    @Test
    fun testFetchStorageInfos() = runTest(timeout = testCaseTimeout) {
        val mainCategoryViewModel = MainCategoryViewModel()
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.isInternalSdMounted(context) }.returns(true)
        every { VolumeEnvironment.isSingleSdcard(context) }.returns(true)
        every { VolumeEnvironment.getInternalSdPath(context) }.returns(INTERNAL_PATH)
        every { VolumeEnvironment.getExternalSdPath(context) }.returns(EXTERNAL_SD_PATH)
        every { VolumeEnvironment.getOTGPath(context) }.returns(mutableListOf())
        every { VolumeEnvironment.getExternalOTGState(context, false) }.returns(true)
        mockkStatic(Utils::class)
        every { Utils.getStorageTotalSizeAfterFormat(any()) }.returns(TOTAL_SIZE)
        every { Utils.getStorageAvailableSize(any()) }.returns(AVAILABLE_SIZE)
        every { Utils.isContainerUser(context) }.returns(true)
        every { cloudDrive.supportCloudDisk() }.returns(false)
        mockkStatic(EncryptViewUtils::class)
        every { EncryptViewUtils.isShowEncryptView() }.returns(false)
        mockkStatic(SortRecordModeFactory::class)
        val result = mutableListOf<StorageUiState>()
        val job = launch(StandardTestDispatcher(testScheduler)) {
            mainCategoryViewModel.storageUiState.toList(result)
        }
        mainCategoryViewModel.fetchStorages()
        advanceUntilIdle()
        Assert.assertEquals(STORAGE_ITEM_SIZE, result.last().storageItems.size)
        advanceUntilIdle()
        every { VolumeEnvironment.isInternalSdMounted(context) }.returns(false)
        mainCategoryViewModel.fetchStorages()
        Assert.assertEquals(STORAGE_ITEM_SIZE_WITH_OUT_PHONE, result.last().storageItems.size)
        job.cancel()
        unmockkStatic(EncryptViewUtils::class)
        unmockkStatic(SortRecordModeFactory::class)
        unmockkStatic(Utils::class)
        unmockkStatic(VolumeEnvironment::class)
    }

    @Test
    fun testUpdateOtgItem() = runTest(timeout = testCaseTimeout) {
        val mainCategoryViewModel = MainCategoryViewModel()
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.isInternalSdMounted(context) }.returns(true)
        every { VolumeEnvironment.isSingleSdcard(context) }.returns(true)
        every { VolumeEnvironment.getInternalSdPath(context) }.returns(INTERNAL_PATH)
        every { VolumeEnvironment.getExternalOTGState(context, false) }.returns(true)
        every { VolumeEnvironment.getExternalSdPath(context) }.returns(EXTERNAL_SD_PATH)
        every { VolumeEnvironment.getOTGPath(context) }.returns(mutableListOf())
        mockkStatic(Utils::class)
        every { Utils.getStorageTotalSizeAfterFormat(any()) }.returns(TOTAL_SIZE)
        every { Utils.getStorageAvailableSize(any()) }.returns(AVAILABLE_SIZE)
        every { Utils.isContainerUser(context) }.returns(true)
        every { cloudDrive.supportCloudDisk() }.returns(false)
        mockkStatic(EncryptViewUtils::class)
        every { EncryptViewUtils.isShowEncryptView() }.returns(false)
        val result = mutableListOf<StorageUiState>()
        val job = launch(StandardTestDispatcher(testScheduler)) {
            mainCategoryViewModel.storageUiState.toList(result)
        }
        mockkStatic(SortRecordModeFactory::class)
        mainCategoryViewModel.fetchStorages()
        advanceUntilIdle()
        val storageItems = result.last().storageItems
        storageItems.forEach { storage ->
            if (storage is Storage.OTGStorage) {
                Assert.assertEquals(STATE_UNMOUNTED, storage.state)
            }
        }
        mainCategoryViewModel.updateOtgItem()
        advanceUntilIdle()
        val updateStorageItem = result.last().storageItems
        updateStorageItem.forEach { storage ->
            if (storage is Storage.OTGStorage) {
                Assert.assertEquals(STATE_CHECKING, storage.state)
            }
        }
        job.cancel()
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(Utils::class)
        unmockkStatic(EncryptViewUtils::class)
        unmockkStatic(SortRecordModeFactory::class)
    }

    @Test
    fun testSetStoragePath() {
        val mainCategoryViewModel = spyk<MainCategoryViewModel>(recordPrivateCalls = true)
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getExternalSdPath(any()) } returns EXTERNAL_SD_PATH
        val storageItems = mutableListOf<Storage>()
        InternalPlatformDsl.dynamicCall(mainCategoryViewModel, "setStoragePath", arrayOf(storageItems)) {
            mockk()
        }
        Assert.assertNull(mainCategoryViewModel.mOtgPaths)
        storageItems.apply {
            add(Storage.PhoneStorage(TOTAL_SIZE, AVAILABLE_SIZE))
        }
        InternalPlatformDsl.dynamicCall(mainCategoryViewModel, "setStoragePath", arrayOf(storageItems)) {
            mockk()
        }
        Assert.assertNull(mainCategoryViewModel.mOtgPaths)
        Assert.assertEquals(EXTERNAL_SD_PATH, mainCategoryViewModel.mExternalPath)
        val otgPath = listOf(OTG_PATH)
        storageItems.apply {
            add(Storage.OTGStorage(otgPath, Pair(TOTAL_SIZE, AVAILABLE_SIZE), STATE_UNMOUNTED))
        }
        InternalPlatformDsl.dynamicCall(mainCategoryViewModel, "setStoragePath", arrayOf(storageItems)) {
            mockk()
        }
        Assert.assertNotNull(mainCategoryViewModel.mOtgPaths)
        Assert.assertEquals(otgPath, mainCategoryViewModel.mOtgPaths)
        Assert.assertEquals(EXTERNAL_SD_PATH, mainCategoryViewModel.mExternalPath)
        val sdCardStorage = Storage.SDCardStorage(TOTAL_SIZE, AVAILABLE_SIZE, STATE_MOUNTED)
        storageItems.apply {
            add(sdCardStorage)
        }
        InternalPlatformDsl.dynamicCall(mainCategoryViewModel, "setStoragePath", arrayOf(storageItems)) {
            mockk()
        }
        Assert.assertEquals(EXTERNAL_SD_PATH, mainCategoryViewModel.mExternalPath)
        storageItems.remove(sdCardStorage)
        storageItems.add(Storage.SDCardStorage(TOTAL_SIZE, AVAILABLE_SIZE, STATE_UNMOUNTED))
        InternalPlatformDsl.dynamicCall(mainCategoryViewModel, "setStoragePath", arrayOf(storageItems)) {
            mockk()
        }
        Assert.assertNotNull(mainCategoryViewModel.mExternalPath)
        Assert.assertEquals(EXTERNAL_SD_PATH, mainCategoryViewModel.mExternalPath)
    }

    @Test
    fun getCleanupGarbageVisibilityTest() {
        val mainCategoryViewModel = MainCategoryViewModel()
        mockkStatic(FeatureCompat::class)
        mockkObject(KtAppUtils)
        mockkStatic(PreferencesUtils::class)

        every { FeatureCompat.sPhoneManagerStartInfo  }.returns(null)
        every { KtAppUtils.hasCleanupFunction }.returns(false)
        Assert.assertEquals(false, mainCategoryViewModel.getCleanupGarbageVisibility())

        every { FeatureCompat.sPhoneManagerStartInfo  }.returns(Pair("test1", "test2"))
        every { KtAppUtils.hasCleanupFunction }.returns(false)
        Assert.assertEquals(false, mainCategoryViewModel.getCleanupGarbageVisibility())

        every { FeatureCompat.sPhoneManagerStartInfo  }.returns(Pair("test1", "test2"))
        every { KtAppUtils.hasCleanupFunction }.returns(true)
        every { PreferencesUtils.getBoolean(key = CLEANUP_FUNCTION_SHOW, default = true) }.returns(true)
        Assert.assertEquals(true, mainCategoryViewModel.getCleanupGarbageVisibility())

        every { FeatureCompat.sPhoneManagerStartInfo  }.returns(Pair("test1", "test2"))
        every { KtAppUtils.hasCleanupFunction }.returns(true)
        every { PreferencesUtils.getBoolean(key = CLEANUP_FUNCTION_SHOW, default = true) }.returns(false)
        Assert.assertEquals(false, mainCategoryViewModel.getCleanupGarbageVisibility())
    }
    @Test
    fun getOWorkVisibilityTest() {
        mockkStatic(AppUtils::class)
        mockkStatic(PreferencesUtils::class)
        mockkStatic(MainCategoryViewModel::class)

        every { AppUtils.isAppInstalledByPkgName(appContext, CommonConstants.OWORK)  }.returns(true)
        every { PreferencesUtils.getBoolean(key = CommonConstants.OWORK_FUNCTION_SHOW, default = true) }.returns(true)
        Assert.assertEquals(true, MainCategoryViewModel.getOWorkVisibility())

        every { AppUtils.isAppInstalledByPkgName(appContext, CommonConstants.OWORK)  }.returns(true)
        every { PreferencesUtils.getBoolean(key = CommonConstants.OWORK_FUNCTION_SHOW, default = true) }.returns(false)
        Assert.assertEquals(false, MainCategoryViewModel.getOWorkVisibility())

        every { AppUtils.isAppInstalledByPkgName(appContext, CommonConstants.OWORK)  }.returns(false)
        every { PreferencesUtils.getBoolean(key = CommonConstants.OWORK_FUNCTION_SHOW, default = true) }.returns(false)
        Assert.assertEquals(false, MainCategoryViewModel.getOWorkVisibility())

        unmockkStatic(AppUtils::class)
        unmockkStatic(PreferencesUtils::class)
        unmockkStatic(MainCategoryViewModel::class)
    }

    companion object {
        private const val STORAGE_ITEM_SIZE = 5
        private const val STORAGE_ITEM_SIZE_WITH_OUT_PHONE = 4
        private const val TOTAL_SIZE = 1000L
        private const val AVAILABLE_SIZE = 900L
        private const val EXTERNAL_SD_PATH = "external_sd_path"
        private const val INTERNAL_PATH = "/test/InternalSdPath"
        private const val OTG_PATH = "otg_path"

        private val testCaseTimeout = 30.seconds
    }
}