/**************************************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: -  * * File: - PersonalInfoListActivity.kt
 * .kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/11/01
 * * Author: ********
 * *
 * * --------------------------Revision History: --------------------------
 * *  <author>                         <data>        <version >    <desc>
 * *  ********      2024/11/01          1.0
 **************************************************************************/
package com.coloros.filemanager.export

import android.annotation.SuppressLint
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.Typeface
import android.icu.text.SimpleDateFormat
import android.net.Uri
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.format.DateUtils
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.text.style.URLSpan
import android.view.Gravity
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewTreeObserver
import android.view.WindowInsets
import android.widget.TextView
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.core.text.HtmlCompat
import com.coui.appcompat.clickablespan.COUIClickableSpan
import com.coui.appcompat.theme.COUIThemeOverlay
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.edge.EdgeToEdgeActivity
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.dp2px
import com.filemanager.common.utils.isNetworkAvailable
import com.google.android.material.appbar.AppBarLayout
import com.oplus.filemanager.interfaze.setting.ISetting
import java.text.DateFormat
import java.util.Locale

class UserAgreementActivity : EdgeToEdgeActivity(), UIConfigMonitor.OnUIConfigChangeListener {
    companion object {
        private const val TAG = "UserAgreementActivity"
        private const val CHINESE_DATE = "2024年10月17日"
        private const val CHINESE_DATE_GDPR = "2025年3月11日"
        private const val CHINESE_DATE_FORMAT = "yyyy年M月d日"
        private const val TAI_WAN_DATE_FORMAT = "yyyy 年 M 月 d 日"


        private const val CLICK_PERSONAL_INFO_PROTECT_POLICY = "personalInfoProtectPolicy"
        private const val SPACE_5 = 5f
        private const val SPACE_8 = 8f
        private const val SPACE_12 = 12f
        private const val SPACE_14 = 14f
        private const val SPACE_16 = 16f
        private const val SPACE_24 = 24f
        private const val SPACE_34 = 34f
        private const val ALPHA = 0.85f
    }

    private lateinit var appBarLayout: AppBarLayout
    private lateinit var toolbar: COUIToolbar
    private lateinit var rootView: View
    private lateinit var scrollView: View

    private val black = Color.argb(ALPHA, 0f, 0f, 0f)
    private var isTh = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        StatusBarUtil.setStatusBarTransparentAndBlackFont(this)
        COUIThemeOverlay.getInstance().applyThemeOverlays(this)
        setContentView(getLayoutResId())
        initView()
    }

    private fun getLayoutResId(): Int {
        return R.layout.activity_user_agreement
    }

    private fun initView() {
        rootView = findViewById(R.id.rootView)
        appBarLayout = findViewById(com.filemanager.common.R.id.appbar_layout)
        toolbar = findViewById(com.filemanager.common.R.id.toolbar)
        scrollView = findViewById(R.id.scrollView)
        toolbar.title = resources.getString(com.filemanager.common.R.string.user_agreement_title_top)
        isTh = checkLocalTh()
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        initToolbar()
        loadView()
        rootView.setOnApplyWindowInsetsListener(rootWindowInsetsListener)
        val setting = Injector.injectFactory<ISetting>()
        setting?.setBarBackgroundColor(
            toolbar,
            rootView,
            window.decorView,
            resources.configuration,
            this
        )
        setting?.setActionCloseFlexibleActivity(window.decorView, this)
    }

    private fun initToolbar() {
        var appBarTopPadding = StatusBarUtil.getStatusBarHeight()
        if (FeatureCompat.isApplicableForFlexibleWindow && WindowUtils.isMiddleAndLargeScreen(this)) {
            appBarTopPadding =
                appContext.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_16dp)
        }
        appBarLayout.setPadding(0, appBarTopPadding, 0, 0)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    private fun loadView() {
        findViewById<LinearLayoutCompat>(R.id.container).apply {
            removeAllViews()
            showViewFirst()
            showViewSecond()
        }
    }

    private fun formatDate(): String {
        return DateUtils.formatDateTime(
            appContext,
            dateStrToTimestamp(),
            DateUtils.FORMAT_SHOW_DATE or DateUtils.FORMAT_SHOW_YEAR
        )
    }

    private fun dateStrToTimestamp(): Long {
        val formatter = SimpleDateFormat("yyyy年MM月dd日", Locale.CHINA) // 调整格式以匹配你的日期字符串
        var timestamp: Long? = null
        val dateStr = getProtocolUpdateDate()
        val date = formatter.parse(dateStr)
        if (date != null) {
            timestamp = date.time // 获取时间戳
        }

        return timestamp ?: 0L // 如果解析失败，默认返回0L
    }
    @SuppressLint("SimpleDateFormat")
    @Suppress("TooGenericExceptionCaught")
    private fun dateString(): String {
        val dateStr = getProtocolUpdateDate()
        try {
            val date = SimpleDateFormat(CHINESE_DATE_FORMAT).parse(dateStr)
            Log.i(TAG, "$date")
            return  DateFormat.getDateInstance(DateFormat.MEDIUM, Locale.ENGLISH).format(date)
        } catch (e: Exception) {
            Log.e(TAG, e)
        }
        return dateStr
    }

    /**
     * 获取协议更新时间
     */
    private fun getProtocolUpdateDate(): String {
        return if (PropertyCompat.sIsGDPR) {
            CHINESE_DATE_GDPR
        } else {
            CHINESE_DATE
        }
    }

    private fun LinearLayoutCompat.showViewFirst() {
        var fileViewProtocol = R.string.user_agreement_title_1_1_1
        var fileManagerProtocol = R.string.user_agreement_title_1_1_2
        var advertServiceProtocol = R.string.user_agreement_title_1_1_3
        if (PropertyCompat.sIsGDPR) { // 用户协议 1. 关于本服务中，欧盟和其他不一样的地方
            fileViewProtocol = R.string.user_agreement_title_1_1_1_new
            fileManagerProtocol = R.string.user_agreement_title_1_1_2_new
            advertServiceProtocol = R.string.user_agreement_title_1_1_3_new_1
        }
        addSpace(SPACE_8).addTitleView { center()
            bold()
            sp16()
            append(com.filemanager.common.R.string.user_agreement_title_top) }.addSpace(SPACE_24).addTextView {
            appendHtml(R.string.user_agreement_update_time, formatDate())
            sp12() }.addSpace(SPACE_24).addTextView {
            when {
                Utils.isRealmePhone() -> appendHtml(R.string.user_agreement_realme)
                Utils.isOnePlusPhone() -> appendHtml(R.string.user_agreement_1plus)
                else -> appendHtml(R.string.user_agreement_oppo)
            }
            appendHtml(R.string.user_agreement_common)
            appendHtml(R.string.user_agreement_common_bold) }.addSpace(SPACE_24).addTextView {
            appendHtml(R.string.user_agreement_content_1) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_content_2) }.addSpace(SPACE_24).addTextView {
            appendHtml(R.string.user_agreement_content_3) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_title_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_1_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(fileViewProtocol) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(fileManagerProtocol) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(advertServiceProtocol) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_1_2) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_title_2) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_2_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_2_2) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_title_3) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_3_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_3_2) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_3_3) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_title_4) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_2) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_3) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_4) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_5) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_6) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_7) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_8) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_9) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_10) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_11) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_12) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_13) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_14) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_15) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_16) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_17) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_18) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_19) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_1_20) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_2) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_3) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_4_4) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_title_5) }
    }

    private fun LinearLayoutCompat.showViewSecond() {
        addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_5_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_5_2) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_title_6) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_6_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_6_2) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_title_7) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_7_1) { clickCloudServiceAndAccount(it) } }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_title_8) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_2) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_2_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_2_2) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_2_3) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_2_4) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_2_5) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_3) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_3_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_3_2) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_3_3) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_3_4) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_3_5) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_4) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_5) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_6) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_7) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_8_8) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_title_9) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_9_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_9_1_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_9_1_2) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_9_1_3) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_9_1_4) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_9_1_5) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_9_2) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_9_3) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_9_4) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_title_10) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_10_1) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_title_11) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_11_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_11_2) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_title_12) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_12_1) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_12_2) }.addSpace(SPACE_24).addTextView { appendSpace()
            appendHtml(R.string.user_agreement_title_12_3) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.user_agreement_title_13_contact) }.addSpace(SPACE_24).addTextView {
            when {
                Utils.isRealmePhone() -> appendHtml(R.string.user_agreement_title_13_contact_rm)
                Utils.isOnePlusPhone() -> appendHtml(R.string.user_agreement_title_13_contact_1plus) { openHttpLink(it) }
                else -> appendHtml(R.string.user_agreement_title_13_contact_oppo) { openHttpLink(it) }
            }
        }
    }

    private fun clickCloudServiceAndAccount(it: String) {
        if (!isNetworkAvailable(this)) {
            CustomToast.showShort(com.filemanager.common.R.string.no_internet_connection)
            return
        }
        when (it) {
            CLICK_PERSONAL_INFO_PROTECT_POLICY -> PermissionUtils.openPrivacyPolicy(this)
        }
    }

    private fun openHttpLink(httpLink: String) {
        val uri = Uri.parse(httpLink)
        val intent = Intent(Intent.ACTION_VIEW, uri)
        startActivity(intent)
    }

    private fun LinearLayoutCompat.addTextView(callback: TextView.() -> Unit): LinearLayoutCompat {
        addView(TextView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            gravity = Gravity.START
            textSize = SPACE_14
            setTextColor(black)
            includeFontPadding = if (isTh) {
                true
            } else {
                false
            }
            lineSpace(SPACE_5)
            callback()
        })
        return this
    }

    private fun checkLocalTh(): Boolean {
        var isTh = false
        runCatching {
            val local = resources.configuration.locale
            isTh = local.language.contains("th")
            Log.i(TAG, "checkLocalTh language ${local.language}, isTh $isTh")
        }.onFailure {
            Log.e(TAG, "checkLocalTh error", it)
        }
        return isTh
    }

    private fun TextView.center() {
        gravity = Gravity.CENTER
    }

    private fun TextView.sp16() {
        textSize = SPACE_16
    }

    private fun TextView.sp12() {
        textSize = SPACE_12
    }

    private fun TextView.bold() {
        typeface = Typeface.create(typeface, Typeface.BOLD)
    }

    private fun LinearLayoutCompat.addSpace(space: Float = SPACE_16): LinearLayoutCompat {
        addView(View(context).apply {
            layoutParams = ViewGroup.LayoutParams(1, dp2px(appContext, space.toInt()))
        })
        return this
    }

    private fun LinearLayoutCompat.addTitleView(
        callback: TextView.() -> Unit
    ): LinearLayoutCompat {
        addView(TextView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            gravity = Gravity.START
            sp16()
            black()
            includeFontPadding = false
            callback()
        })
        return this
    }

    private fun TextView.append(res: Int, vararg values: String) {
        append(context.resources.getString(res, *values))
    }

    private fun TextView.appendHtml(
        res: Int,
        vararg values: String,
        onClick: ((String) -> Unit)? = null
    ) {
        val string = resources.getString(res, *values)
        val output = HtmlCompat.fromHtml(string, HtmlCompat.FROM_HTML_MODE_LEGACY, null, null) as SpannableStringBuilder
        val boldSpans = output.getSpans(
            0, output.length, StyleSpan::class.java
        ).filter { it.style == Typeface.BOLD }
        boldSpans.forEach {
            var start = output.getSpanStart(it)
            val end = output.getSpanEnd(it)
            val flags = output.getSpanFlags(it)
            val colorSpan = ForegroundColorSpan(black)
            val urlSpans = output.getSpans(start, end, ClickableSpan::class.java)
            if (urlSpans.isEmpty()) {
                output.setSpan(colorSpan, start, end, flags)
            } else {
                urlSpans.forEach { span ->
                    val s = output.getSpanStart(span)
                    val e = output.getSpanEnd(span)
                    output.setSpan(colorSpan, start, s, flags)
                    start = e
                }
                output.setSpan(colorSpan, start, end, flags)
            }
        }
        val urlSpans = output.getSpans(0, output.length, URLSpan::class.java)
        urlSpans.forEach {
            val start = output.getSpanStart(it)
            val end = output.getSpanEnd(it)
            val flags = output.getSpanFlags(it)
            val clickSpan = COUIClickableSpan(context).apply {
                setStatusBarClickListener {
                    onClick?.invoke(it.url)
                }
            }
            output.removeSpan(it)
            output.setSpan(clickSpan, start, end, flags)
        }
        append(output)
        movementMethod = LinkMovementMethod.getInstance()
    }

    private fun TextView.black() {
        setTextColor(black)
    }

    private fun TextView.appendLine() {
        append("\n")
    }

    private fun TextView.appendSpace() {
        append("        ")
    }

    private fun TextView.lineSpace(sp: Float) {
        val fontScale = resources.configuration.fontScale
        val density = resources.displayMetrics.density
        setLineSpacing(sp * density * fontScale, 1f)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        UIConfigMonitor.instance.onActivityConfigChanged(newConfig)
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        initToolbar()
        Injector.injectFactory<ISetting>()?.setBarBackgroundColor(
            toolbar,
            rootView,
            window.decorView,
            resources.configuration,
            this
        )
    }

    private val rootWindowInsetsListener = View.OnApplyWindowInsetsListener { _, insets ->
        rootView.viewTreeObserver?.addOnGlobalLayoutListener(globalLayoutListener)
        if (SdkUtils.isAtLeastR()) {
            WindowInsets.CONSUMED
        } else {
            insets
        }
    }

    private val globalLayoutListener = object : ViewTreeObserver.OnGlobalLayoutListener {
        override fun onGlobalLayout() {
            rootView.viewTreeObserver?.removeOnGlobalLayoutListener(this)
            updateScrollViewMargin()
        }
    }

    private fun updateScrollViewMargin() {
        val widthDp = ViewHelper.px2dip(this@UserAgreementActivity, rootView.width)
        val margin = when (WindowUtils.getScreenWindowType(widthDp, 0)) {
            WindowUtils.SMALL -> resources.getDimensionPixelSize(R.dimen.user_info_horizontal_margin)
            WindowUtils.MIDDLE -> resources.getDimensionPixelSize(R.dimen.user_info_horizontal_margin_middle)
            WindowUtils.LARGE -> resources.getDimensionPixelSize(R.dimen.user_info_horizontal_margin_large)
            else -> resources.getDimensionPixelSize(R.dimen.user_info_horizontal_margin)
        }
        val params = scrollView.layoutParams as? MarginLayoutParams
        params?.let {
            params.marginStart = margin
            params.marginEnd = margin
            scrollView.layoutParams = it
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        rootView.setOnApplyWindowInsetsListener(null)
    }
}