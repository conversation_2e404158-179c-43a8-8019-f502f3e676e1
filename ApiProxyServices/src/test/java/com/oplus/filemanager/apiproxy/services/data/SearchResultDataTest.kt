package com.oplus.filemanager.apiproxy.services.data

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.base.UriFileWrapper
import com.filemanager.common.constants.DmpConst.RECALL_TYPE_CONTENT
import com.filemanager.common.constants.DmpConst.RECALL_TYPE_FUZZY
import com.filemanager.common.constants.DmpConst.RECALL_TYPE_TITLE
import com.filemanager.common.constants.DmpConst.RECALL_TYPE_TITLE_CONTENT
import com.filemanager.common.utils.Log
import com.oplus.filemanager.apiproxy.services.utils.SearchChecker
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File

/**
 * SearchResultData 的单元测试类
 * 用于测试 SearchResultData 类的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(maxSdk = 33)
class SearchResultDataTest {

    // 测试对象
    private lateinit var searchResultData: SearchResultData

    /**
     * 在每个测试方法执行前初始化测试环境
     * 1. 创建 SearchResultData 实例
     * 2. 模拟 File 和 Log 类的静态方法
     */
    @Before
    fun setUp() {
        searchResultData = SearchResultData()
        // 使用 mockk 模拟静态类
        mockkStatic(File::class)
        mockkStatic(Log::class)
        // 配置 Log 的模拟行为
        every { Log.i(any(), any()) } returns Unit
        every { Log.d(any(), any()) } returns Unit
    }

    /**
     * 在每个测试方法执行后清理测试环境
     * 解除所有模拟
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试 classifyDMPSearchItem 方法
     * 当传入 null 项时应该不做任何操作
     */
    @Test
    fun testClassifyDMPSearchItem_WithNullItem_ShouldDoNothing() {
        searchResultData.classifyDMPSearchItem(null, "test")
        // 验证目录列表应该为空
        assertTrue(searchResultData.listDirectory!!.isEmpty())
    }

    /**
     * 测试 classifyDMPSearchItem 方法
     * 当传入非 UriFileWrapper 类型的项时应该不做任何操作
     */
    @Test
    fun testClassifyDMPSearchItem_WithNotUriFileWrapper_ShouldDoNothing() {
        // 创建一个 BaseFileBean 的模拟对象
        val item = mockk<BaseFileBean>()
        // 配置模拟对象的 recallType 属性
        every { item.recallType } returns -1
        searchResultData.classifyDMPSearchItem(item, "test")
        // 验证目录列表应该为空
        assertTrue(searchResultData.listDirectory!!.isEmpty())
    }

    /**
     * 测试 classMediaSearchItem 方法
     * 当传入 null 项时应该不做任何操作
     */
    @Test
    fun testClassMediaSearchItem_WithNullItem_ShouldDoNothing() {
        searchResultData.classMediaSearchItem(null, false)
        // 验证媒体查询列表应该为空
        assertTrue(searchResultData.listMediaQuery!!.isEmpty())
    }

    /**
     * 测试 classifyRemoteFileSearchList 方法
     * 应该添加所有传入的远程文件项
     */
    @Test
    fun testClassifyRemoteFileSearchList_ShouldAddAllItems() {
        // 创建包含两个模拟 RemoteFileBean 的列表
        val list = listOf(mockk<RemoteFileBean>(), mockk<RemoteFileBean>())
        searchResultData.classifyRemoteFileSearchList(list)
        // 验证模糊匹配ID列表应该包含两个项
        assertEquals(2, searchResultData.listTitleFuzzyIds!!.size)
    }

    /**
     * 测试 setResultCode 方法
     * 当当前状态码不是OK时不应该改变状态码
     */
    @Test
    fun testSetResultCode_WhenCurrentCodeIsNotOK_ShouldNotChange() {
        // 先设置一个非OK的状态码
        searchResultData.setResultCode(SearchChecker.CODE_OK + 1)
        // 尝试设置为OK状态码
        searchResultData.setResultCode(SearchChecker.CODE_OK)
        // 验证状态码应该不是OK
        assertNotEquals(SearchChecker.CODE_OK, searchResultData.getResultCode())
    }

    /**
     * 测试 setResultCode 方法
     * 当当前状态码是OK时可以改变状态码
     */
    @Test
    fun testSetResultCode_WhenCurrentCodeIsOK_ShouldChange() {
        // 设置一个非OK的状态码
        searchResultData.setResultCode(SearchChecker.CODE_OK + 1)
        // 验证状态码应该被成功修改
        assertEquals(SearchChecker.CODE_OK + 1, searchResultData.getResultCode())
    }
}