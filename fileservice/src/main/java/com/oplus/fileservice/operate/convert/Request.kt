/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Request
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/1 17:06
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/1       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.convert

import com.oplus.fileservice.operate.internal.ConflictPolicy
import com.oplus.fileservice.operate.internal.FileInfo

open class Request {
    var requestId: String? = null
}

data class OperateRequest(
    var operateType: Int = -1,
    var sourceFileInfo: List<FileInfo>? = listOf(),
    var targetFileInfo: FileInfo? = null,
    var conflictPolicy: ConflictPolicy = ConflictPolicy.KEEP_BOTH,
    var option: Any? = null
) : Request()