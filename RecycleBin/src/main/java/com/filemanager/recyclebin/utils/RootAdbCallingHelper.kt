/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - RootAdbCallingHelper.kt
 * Description:
 *     Help to handle invoking provider actions from root adb
 *
 * Version: 1.0
 * Date: 2024-04-09
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-04-09   1.0    Create this module
 *********************************************************************************/
package com.filemanager.recyclebin.utils

import android.content.ContentProvider
import android.content.ContentValues
import android.os.Bundle
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.constructBundle

internal object RootAdbCallingHelper {
    private const val TAG = "RootAdbCallingHelper"
    private const val ROOT_ADB_PACKAGE = "root"

    @JvmStatic
    fun isCallingFromRootAdb(calledProvider: ContentProvider): Boolean = calledProvider.run {
        val caller = if (SdkUtils.isAtLeastR()) {
            runCatching { callingPackage }.getOrDefault(callingPackageUnchecked)
        } else {
            runCatching { callingPackage }.getOrDefault("")
        }
        return caller == ROOT_ADB_PACKAGE
    }

    @JvmStatic
    fun convertContentValuesToBundle(values: ContentValues): Bundle {
        val bundle = Bundle()
        values.keySet().forEach {
            com.oplus.wrapper.os.Bundle(bundle).putObject(it, values.get(it))
        }
        return bundle
    }

    @JvmStatic
    fun obtainCallProviderResult(
        calledProvider: ContentProvider,
        resultKey: String,
        resultValue: ContentValues
    ): Bundle {
        if (isCallingFromRootAdb(calledProvider)) {
            Log.w(TAG, "WARNING! Call from root adb, provider=$calledProvider")
            return convertContentValuesToBundle(resultValue)
        }
        return constructBundle().apply { putParcelable(resultKey, resultValue) }
    }
}