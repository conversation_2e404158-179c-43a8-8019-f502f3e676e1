/*********************************************************************************
 * Copyright (C), 2008-2024, Oplus, All rights reserved.
 *
 * File: - DocPreviewContainerManager.kt
 * Description:
 *     Manage container ui and loading ui for previewing doc file
 *
 * Version: 1.0
 * Date: 2024-12-10
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-12-10   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.doc

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.Log
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewContainerManager
import com.oplus.filemanager.preview.utils.PreviewScrollAreaHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite

internal class DocPreviewContainerManager(rootLayout: ViewGroup) : AbsPreviewContainerManager(),
    IDocPreviewContainerManager {

    private companion object {
        private const val TAG = "DocPreviewContainerManager"
    }

    override val thumbnailView: ImageView = rootLayout.findViewById(R.id.preview_image_view)
    override val defaultContainer: PreviewFileInfoSuite =
        rootLayout.findViewById(R.id.preview_container_default)
    override val mDateTv: AppCompatTextView = rootLayout.findViewById(R.id.preview_remote_time_info)
    override val mSizeTv: AppCompatTextView = rootLayout.findViewById(R.id.preview_remote_size_info)
    override val nameContainer: TextViewSnippet =
        rootLayout.findViewById(R.id.preview_remote_title)
    private val docContainer: ViewGroup = rootLayout.findViewById(R.id.preview_container_success)
    private val loadingLayout: View = rootLayout.findViewById(R.id.loading_layout)
    private val scrollAreaHelper = PreviewScrollAreaHelper(
        scrollView = rootLayout.findViewById(R.id.preview_audio_scroll_area),
        operationsBar = rootLayout.findViewById(R.id.preview_operations_bar)
    )

    override fun showAsLoading() {
        super.showAsLoading()
        Log.d(TAG, "showAsLoading")
        loadingLayout.visibility = View.VISIBLE
        docContainer.visibility = View.VISIBLE
        thumbnailView.visibility = View.INVISIBLE
        defaultContainer.visibility = View.GONE
        scrollAreaHelper.hideDivider()
    }

    override fun showAsFileContainer(isDefault: Boolean) {
        Log.d(TAG, "showAsFileContainer: $isDefault")
        if (isDefault) {
            loadingLayout.visibility = View.GONE
            docContainer.visibility = View.GONE
            defaultContainer.visibility = View.VISIBLE
            defaultContainer.setFilePathVisible(false)
            defaultContainer.setFileNameVisible(false)
        } else {
            loadingLayout.visibility = View.GONE
            docContainer.visibility = View.VISIBLE
            thumbnailView.visibility = View.VISIBLE
            defaultContainer.visibility = View.GONE
        }
        if (isDefault) {
            scrollAreaHelper.hideDivider()
        } else {
            scrollAreaHelper.checkShowDivider()
        }
        super.showAsFileContainer(isDefault)
    }

    override fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        scrollAreaHelper.checkShowDivider()
    }
}