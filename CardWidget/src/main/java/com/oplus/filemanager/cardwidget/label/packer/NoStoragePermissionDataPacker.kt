/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : NoStoragePermissionDataPacker
 * * Description : 文管未获取到特殊权限页面，显示文本，点击跳转到文管标签列表页面
 * * Version     : 1.0
 * * Date        : 2022/12/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.packer

import android.content.Context
import com.filemanager.common.utils.Log
import com.oplus.cardwidget.domain.pack.BaseDataPack
import com.oplus.filemanager.cardwidget.util.StartActivityUtil
import com.oplus.filemanager.cardwidget.util.TextUtil
import com.oplus.smartenginehelper.dsl.DSLCoder

class NoStoragePermissionDataPacker(
    private val context: Context
) : BaseDataPack() {

    override fun onPack(coder: DSLCoder): Boolean {
        Log.d(DATA_PACK_TAG, "onPack -> coder = $coder")
        TextUtil.setTextSize(coder, context, ID_NO_PERMISSION_TIP, TextUtil.TEXT_SIZE_16)
        TextUtil.setTextSize(coder, context, ID_NO_PERMISSION_ACTION, TextUtil.TEXT_SIZE_12)
        val start = StartActivityUtil().startFileManagerMainActivityLabelTab()
        coder.setTextViewText(ID_NO_PERMISSION_TIP, RES_ID_NO_PERMISSION_TIP_RES_ID)
        coder.setTextViewText(ID_NO_PERMISSION_ACTION, RES_ID_NO_PERMISSION_ACTION_RES_ID)
        coder.setOnClick(ID_NO_PERMISSION_TIP_CONTAINER, start)
        coder.setOnClick(ID_NO_PERMISSION_ACTION, start)
        return true
    }

    companion object {
        private const val DATA_PACK_TAG = "NoStoragePermissionDataPacker"
        private const val RES_ID_NO_PERMISSION_TIP_RES_ID = "@string/manage_files_permission_title"
        private const val RES_ID_NO_PERMISSION_ACTION_RES_ID = "@string/set_button_text"
        private const val ID_NO_PERMISSION_TIP = "tips"
        private const val ID_NO_PERMISSION_ACTION = "action"
        private const val ID_NO_PERMISSION_TIP_CONTAINER = "container"
    }
}