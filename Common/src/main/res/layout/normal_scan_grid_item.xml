<?xml version="1.0" encoding="utf-8"?>
<com.filemanager.common.view.HoverAwareConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/empty_data_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.filemanager.common.view.GridThumbView
        android:id="@+id/file_grid_item_icon"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:forceDarkAllowed="false"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/file_duration_tv"
        android:layout_width="@dimen/dimen_36dp"
        android:layout_height="wrap_content"
        android:background="@drawable/item_video_duration_view_bg"
        android:gravity="center"
        android:textColor="@color/coui_color_label_on_color"
        android:fontFamily="sans-serif-medium"
        android:textSize="@dimen/duration_text_size"
        android:visibility="gone"
        android:layout_marginBottom="@dimen/dimen_5dp"
        android:layout_marginStart="@dimen/dimen_6dp"
        app:layout_constraintBottom_toBottomOf="@+id/file_grid_item_icon"
        app:layout_constraintStart_toStartOf="@+id/file_grid_item_icon"/>
</com.filemanager.common.view.HoverAwareConstraintLayout>