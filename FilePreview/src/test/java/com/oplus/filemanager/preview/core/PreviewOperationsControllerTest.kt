/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - PreviewOperationsControllerTest.kt
 * Description:
 * The test cases for PreviewOperationsController
 *
 * Version: 1.0
 * Date: 2024-10-11
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-11   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.core

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.oplus.filemanager.preview.R
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class PreviewOperationsControllerTest : Assert() {

    private lateinit var normalController: IFileOperate
    private lateinit var operationsController: PreviewOperationsController
    private lateinit var testActivity: FragmentActivity

    @Before
    fun setUp() {
        testActivity = mockk()
        val previewModel = mockk<FilePreviewViewModel> {
            every { pickPreviewFile() } returns mockk(relaxed = true)
        }
        val fragment = mockk<Fragment> {
            every { activity } returns testActivity
        }
        normalController = mockk(relaxed = true)
        operationsController = PreviewOperationsController(
            fragment = fragment,
            previewModel = previewModel,
            selectPathsCache = mutableMapOf(),
            normalController = normalController
        )
    }


    @Test
    fun `should onSelectCutDir when onClickMoreMenuItem if click move`() {
        // Given
        val itemId = R.id.preview_operation_move

        // When
        operationsController.onClickMoreMenuItem(itemId)

        // Then
        verify(exactly = 1) { normalController.onSelectCutDir(testActivity) }
    }

    @Test
    fun `should onDelete when onClickMoreMenuItem if click delete`() {
        // Given
        val itemId = R.id.preview_operation_delete

        // When
        operationsController.onClickMoreMenuItem(itemId)

        // Then
        verify(exactly = 1) { normalController.onDelete(testActivity) }
    }

    @Test
    fun `should onSelectCopyDir when onClickMoreMenuItem if click copy`() {
        // Given
        val itemId = R.id.preview_operation_copy

        // When
        operationsController.onClickMoreMenuItem(itemId)

        // Then
        verify(exactly = 1) { normalController.onSelectCopyDir(testActivity) }
    }

    @Test
    fun `should onRename when onClickMoreMenuItem if click rename`() {
        // Given
        val itemId = R.id.preview_operation_rename

        // When
        operationsController.onClickMoreMenuItem(itemId)

        // Then
        verify(exactly = 1) { normalController.onRename(testActivity) }
    }

    @Test
    fun `should onDetail when onClickMoreMenuItem if click detail`() {
        // Given
        val itemId = R.id.preview_operation_detail

        // When
        operationsController.onClickMoreMenuItem(itemId)

        // Then
        verify(exactly = 1) { normalController.onDetail(testActivity) }
    }

    @Test
    fun `should onUploadCloudDisk when onClickMoreMenuItem if click cloud disk`() {
        // Given
        val itemId = R.id.preview_operation_cloud_disk

        // When
        operationsController.onClickMoreMenuItem(itemId)

        // Then
        verify(exactly = 1) { normalController.onUploadCloudDisk(testActivity) }
    }

    @Test
    fun `should onEncrypt when onClickMoreMenuItem if click encryption`() {
        // Given
        val itemId = R.id.preview_operation_encryption

        // When
        operationsController.onClickMoreMenuItem(itemId)

        // Then
        verify(exactly = 1) { normalController.onEncrypt(testActivity) }
    }

    @Test
    fun `should createShortCut when onClickMoreMenuItem if click shortcut`() {
        // Given
        val itemId = R.id.preview_operation_shortcut

        // When
        operationsController.onClickMoreMenuItem(itemId)

        // Then
        verify(exactly = 1) { normalController.createShortCut(testActivity) }
    }
}