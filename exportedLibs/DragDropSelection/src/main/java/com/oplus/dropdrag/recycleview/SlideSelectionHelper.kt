/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * File:  - SlideSelectionHelper.kt
 * Description: Slide Selection Helper
 * Version: 1.0
 * Date : 2020/05/15
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/05/15    1.0     create
 ***********************************************************************/
package com.oplus.dropdrag.recycleview

import android.graphics.Point
import android.view.MotionEvent
import android.view.ViewConfiguration
import androidx.core.view.MotionEventCompat
import androidx.core.view.MotionEventCompat.getPointerId
import androidx.core.view.ViewConfigurationCompat.getScaledPagingTouchSlop
import androidx.recyclerview.widget.RecyclerView
import com.oplus.dropdrag.DragDropSdkManager
import com.oplus.dropdrag.MotionEvents
import com.oplus.dropdrag.OnSlideSelectionStateListener
import com.oplus.dropdrag.SelectionTracker
import com.oplus.dropdrag.SelectionTracker.Companion.DEBUG
import com.oplus.dropdrag.utils.Log

/**
 * SlideSelectionHelper provides logic that interprets a combination
 * of motions and gestures in order to provide gesture driven selection support
 * when used in conjunction with RecyclerView and other classes in the ReyclerView
 * selection support package.
 */
internal class SlideSelectionHelper<K> private constructor(
    private val mSelectionTracker: SelectionTracker<K>,
    private val mDetailsLookup: ItemDetailsLookup<K>?,
    private val mView: ViewDelegate,
    private val mScrollerHost: ScrollHost
) : RecyclerView.OnItemTouchListener {
    companion object {
        private const val TAG = "SlideSelectionHelper"

        private const val INVALID_POINTER = -1
        // Y轴移动方向
        private const val DIRECTION_NULL = -1 //刚开始-无方向
        private const val DIRECTION_FORWARD = 0 //正向-从上到下
        private const val DIRECTION_REVERSE = 1 //反向-从下到上
        // Y轴未开始前的坐标
        private const val LAST_SLIDE_Y_NOT_START = -1F
        private const val AVOID_TINGLE_DISTANCE = 30F

        fun <K> create(
            selectionMgr: SelectionTracker<K>,
            detailsLookup: ItemDetailsLookup<K>?,
            recyclerView: RecyclerView,
            scrollerHost: ScrollHost
        ): SlideSelectionHelper<K> {
            return SlideSelectionHelper(selectionMgr, detailsLookup,
                    RecyclerViewDelegate(recyclerView, selectionMgr.layoutType), scrollerHost)
        }

        private val GRID_TOUCH_SLOP by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            val configuration = ViewConfiguration.get(DragDropSdkManager.applicationContext)
            getScaledPagingTouchSlop(configuration)
        }
    }


    // started state for slide selection
    private var mIsSlideStarted = false

    // Y开始移动的坐标
    private var mLastSlideStartedY = LAST_SLIDE_Y_NOT_START
    // 上次选择的方向--默认方向是无方向
    private var mLastSlideStartedDirection = DIRECTION_NULL
    // 当前选择的方向--默认方向是无方向
    private var mTempSlideStartedDirection = DIRECTION_NULL

    // can slide selection only for grid layout type, if slide distance in certain area,set it true
    private var mIsCanSlide = false

    private var mLastInterceptedPoint: Point? = null
    private var mActivePointerId: Int = INVALID_POINTER
    private var mDownPointX = 0f
    private var mDownPointY = 0f
    private var mSlideStateListener: OnSlideSelectionStateListener? = null

    fun setSlideStateListener(listener: OnSlideSelectionStateListener?) {
        mSlideStateListener = listener
    }

    private var mScroller: AutoScroller? = null

    override fun onInterceptTouchEvent(unused: RecyclerView, e: MotionEvent): Boolean {
        if (mDetailsLookup == null) {
            return false
        } else if (!mSelectionTracker.canSlideSelection()) {
            return false
        }

        when (e.actionMasked) {
            // NOTE: Unlike events with other actions, RecyclerView eats
            // "DOWN" events. So even if we return true here we'll
            // never see an event w/ ACTION_DOWN passed to onTouchEvent.
            MotionEvent.ACTION_DOWN -> {
                mSelectionTracker.longPressIndex = RecyclerView.NO_POSITION
                return handleInterceptedDownEvent(e)
            }
            MotionEvent.ACTION_MOVE -> {
                return handleInterceptedMoveEvent(e)
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                mActivePointerId = INVALID_POINTER
                mSelectionTracker.longPressIndex = RecyclerView.NO_POSITION
                if (DEBUG) {
                    Log.d(TAG, "onInterceptTouchEvent longPressIndex: ${mSelectionTracker.longPressIndex}")
                }
            }
        }
        return false
    }

    override fun onTouchEvent(unused: RecyclerView, e: MotionEvent) {
        if (DEBUG) {
            Log.d(TAG, "onTouchEvent")
        }

        if (mDetailsLookup == null) {
            return
        } else if (!mSelectionTracker.canSlideSelection()) {
            return
        }

        when (e.actionMasked) {
            // NOTE: Unlike events with other actions, RecyclerView eats
            // "DOWN" events. So even if we return true here we'll
            // never see an event w/ ACTION_DOWN passed to onTouchEvent.
            MotionEvent.ACTION_MOVE -> {
                if (mSelectionTracker.layoutType == SelectionTracker.LAYOUT_TYPE.LIST) {
                    handleMoveEventList(e)
                } else {
                    handleMoveEvent(e)
                }
            }
            MotionEvent.ACTION_UP -> handleUpEvent(e)
            MotionEvent.ACTION_CANCEL -> handleCancelEvent(e)
        }
    }

    override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {}

    /**
     * Called when an ACTION_DOWN event is intercepted.
     *  If down event happens on an item, we mark that item's position as last started.
     */
    private fun handleInterceptedDownEvent(ev: MotionEvent): Boolean {
        // just key down point,and must return false to make sure scroll success
        if (DEBUG) {
            Log.d(TAG, "handleInterceptedDownEvent ")
        }

        mActivePointerId = getPointerId(ev, 0)
        mDownPointX = ev.x
        mDownPointY = ev.y

        return false
    }

    /**
     * Called when an ACTION_MOVE event is intercepted.
     *  If down event happens on an item, we mark that item's position as last started.
     */
    private fun handleInterceptedMoveEvent(ev: MotionEvent): Boolean {
        if (canSlideSelection() && isInSlideHotspot(ev)) {
            val activePointerId: Int = mActivePointerId
            if (activePointerId == INVALID_POINTER) {
                Log.e(TAG, "handleInterceptedMoveEvent INVALID_POINTER")
                return false
            }

            if (canInterceptedMoveEvent()) {
                return true
            }

            val pointerIndex: Int = MotionEventCompat.findPointerIndex(ev, activePointerId)
            val x: Float = MotionEventCompat.getX(ev, pointerIndex)
            val xDiff: Float = Math.abs(x - mDownPointX)
            val y: Float = MotionEventCompat.getY(ev, pointerIndex)
            val yDiff: Float = Math.abs(y - mDownPointY)
            if (DEBUG) {
                Log.d(TAG, "handleInterceptedMoveEvent xDiff : $xDiff, yDiff: $yDiff")
            }

            if ((xDiff > GRID_TOUCH_SLOP) && (xDiff > yDiff)) {
                Log.d(TAG, "handleInterceptedMoveEvent true")
                mIsCanSlide = true
                return true
            }
        }
        return false
    }

    /**
     * Called when ACTION_UP event is to be handled.
     * Essentially, since this means all gesture movement is over, reset everything and apply
     * provisional selection.
     */
    private fun handleUpEvent(e: MotionEvent) {
        if (DEBUG) {
            Log.d(TAG, "handleUpEvent ")
        }
        mSelectionTracker.mergeProvisionalSelection()
        endSelection()
        mIsSlideStarted = false
        mTempSlideStartedDirection = DIRECTION_NULL
        mLastSlideStartedDirection = DIRECTION_NULL
        mLastSlideStartedY = LAST_SLIDE_Y_NOT_START
        mIsCanSlide = false
        mSlideStateListener?.onSlideSelectionEnd()
    }

    /**
     * Called when ACTION_CANCEL event is to be handled.
     * This means this gesture selection is aborted, so reset everything and abandon provisional
     * selection.
     */
    private fun handleCancelEvent(unused: MotionEvent) {
        if (DEBUG) {
            Log.d(TAG, "handleCancelEvent ")
        }
        mSelectionTracker.clearProvisionalSelection()
        endSelection()
        mIsSlideStarted = false
        mTempSlideStartedDirection = DIRECTION_NULL
        mLastSlideStartedDirection = DIRECTION_NULL
        mLastSlideStartedY = LAST_SLIDE_Y_NOT_START
        mIsCanSlide = false
        mSlideStateListener?.onSlideSelectionEnd()
    }

    private fun endSelection() {
        Log.d(TAG, "endSelection ")
        mScroller?.reset()
        mScroller = null
        mSelectionTracker.endRange()
        mDownPointY = -1f
        mDownPointX = -1f
        mActivePointerId = INVALID_POINTER
    }

    /**
     * Call when an intercepted ACTION_MOVE event is passed down.
     * At this point, we are sure user wants to gesture multi-select.
     */
    private fun handleMoveEvent(e: MotionEvent) {
        if (DEBUG) {
            Log.d(TAG, "handleMoveEvent ")
        }

        if (!changeSelectedStateAndSlideStart(e)) {
            return
        }

        if (!mIsSlideStarted) {
            Log.d(TAG, "handleMoveEvent SLIDE NOT START")
            return
        }
        handleExtendSelectionAndScroll(e)
    }

    private fun handleExtendSelectionAndScroll(e: MotionEvent) {
        val lastGlidedItemPos = mView.getLastGlidedItemPosition(e)
        if (DEBUG) {
            Log.d(TAG, "handleMoveEvent lastGlidedItemPos : $lastGlidedItemPos")
        }
        if (lastGlidedItemPos != RecyclerView.NO_POSITION) {
            extendSelection(lastGlidedItemPos)
            // scroll view if slide in selection region
            mLastInterceptedPoint = MotionEvents.getOrigin(e)
            mLastInterceptedPoint?.let {
                mScroller?.scroll(it)
            }
        } else {
            // #Update for BugID.21295, 2020/8/21, Modify
            // During the execution of [handleMoveEvent()], when the finger position exceeds the position of the list item,
            // if the RecyclerView has not been slid to the top, continue to slide to the top
            if (!mView.viewCanScrollUp()) {
                mScroller?.stopScroll()
            } else {
                if (DEBUG) {
                    Log.d(TAG, "handleMoveEvent not scroll to top, continue scroll")
                }
                // scroll view if slide in selection region
                mLastInterceptedPoint = MotionEvents.getOrigin(e)
                mLastInterceptedPoint?.let {
                    mScroller?.scroll(it)
                }
            }
            // #Update end. BugID.21295
        }
    }

    private fun handleMoveEventList(e: MotionEvent) {
        if (DEBUG) Log.d(TAG, "handleMoveEvent in listMode")
        // 如果未抖动，才进行是否同方向的判断
        if (!isUserTingleInList(e) && !isSameDirectionInList(e)) {
            handleDiffDirectionEvent()
        }
        if (!changeSelectedStateAndSlideStart(e)) {
            return
        }
        if (!mIsSlideStarted) {
            Log.d(TAG, "handleMoveEvent SLIDE NOT START")
            return
        }
        if (!isUserTingleInList(e)) {
            updateLastSlideStartedYAndDirectionInList(e)
        }
        handleExtendSelectionAndScroll(e)
    }

    /**
     * 判断ItemSelected状态和开始Slide
     * 返回值：
     * ture代表代码可向下执行
     * false代表有异常return
     */
    private fun changeSelectedStateAndSlideStart(e: MotionEvent): Boolean {
        // 如果mIsSlideStarted=true,代表已开始，则不执行判断逻辑
        if (mIsSlideStarted) {
            return true
        }
        val item = mDetailsLookup?.getItemDetails(e) ?: return false
        Log.d(TAG, "handleMoveEvent KICK OFF position : ${item.position}")
        val key = item.selectionKey ?: return false
        val position = item.position
        val selected = if ((mSelectionTracker.longPressIndex != RecyclerView.NO_POSITION)
            && (position == mSelectionTracker.longPressIndex)
        ) {
            mSelectionTracker.isItemSelected(key)
        } else {
            !mSelectionTracker.isItemSelected(key)
        }
        Log.d(TAG, "handleMoveEvent KICK OFF SLIDE selected : $selected")
        if (selected) {
            mSelectionTracker.selectItem(key)
        } else {
            mSelectionTracker.deselectItem(key)
        }
        mSelectionTracker.anchorRange(position, key)
        Log.d(TAG, "handleMoveEvent layoutType: ${mSelectionTracker.layoutType}")
        mScroller = AutoScroller.createAutoScroll(mScrollerHost, mSelectionTracker.layoutType, mSelectionTracker.scrollThresholdRatio)
        mIsSlideStarted = true
        mSlideStateListener?.onSlideSelectionStart()
        return true
    }

    /**
     * 更新并记录:Y和Direction
     */
    private fun updateLastSlideStartedYAndDirectionInList(e: MotionEvent) {
        if (mLastSlideStartedY != LAST_SLIDE_Y_NOT_START && e.y - mLastSlideStartedY > 0) {
            // 如果位移>0 则向下移动
            mLastSlideStartedDirection = DIRECTION_FORWARD
        } else if (mLastSlideStartedY != LAST_SLIDE_Y_NOT_START && e.y - mLastSlideStartedY < 0) {
            // 如果位移<0 则向上移动
            mLastSlideStartedDirection = DIRECTION_REVERSE
        } else {
            // 初始未移动
            mLastSlideStartedDirection = DIRECTION_NULL
        }
        // 开始后用户手指的Y值
        mLastSlideStartedY = e.y
    }

    /**
     * 判断是否是相同方向：反向动作时，则把mIsSlideStarted置成false，重新开始，相当于抬手
     */
    private fun isSameDirectionInList(e: MotionEvent): Boolean {
        if (mLastSlideStartedY != LAST_SLIDE_Y_NOT_START) {
            // 如果位移>0 则向下移动
            if ((e.y - mLastSlideStartedY) > 0) {
                mTempSlideStartedDirection = DIRECTION_FORWARD
            }
            //如果位移<0 则向上移动
            if ((e.y - mLastSlideStartedY) < 0) {
                mTempSlideStartedDirection = DIRECTION_REVERSE
            }
        } else {
            Log.d(TAG, "mLastSlideStartedY is LAST_SLIDE_Y_NOT_START, begin move")
        }
        if (DEBUG) {
            Log.d(TAG, "isSameDirectionInList -> mLastSlideStartedDirection $mLastSlideStartedDirection")
            Log.d(TAG, "isSameDirectionInList -> mTempSlideStartedDirection $mTempSlideStartedDirection")
        }
        /**
         *  重点：判断方向相同
         *  不相同：则把mIsSlideStarted设置成false,相当于抬手了
         *  判断相同1：mTempSlideStartedDirection == mLastSlideStartedDirection
         *  判断相同2：上次-初始未移动 && 本次-已经有移动方向
         */
        val isSameDirection = mTempSlideStartedDirection == mLastSlideStartedDirection
                || (mLastSlideStartedDirection == DIRECTION_NULL && mTempSlideStartedDirection != DIRECTION_NULL)
        if (DEBUG) {
            // 同向
            if (isSameDirection && mTempSlideStartedDirection == DIRECTION_FORWARD) {
                Log.d(TAG, "isSameDirectionInList -> 方向相同，向下滑动")
            }
            if (isSameDirection && mTempSlideStartedDirection == DIRECTION_REVERSE) {
                Log.d(TAG, "isSameDirectionInList -> 方向相同，向上滑动")
            }
            if (isSameDirection && mTempSlideStartedDirection == DIRECTION_NULL) {
                Log.d(TAG, "isSameDirectionInList -> 方向相同，刚开始，未滑动")
            }
            // 不同向
            if (!isSameDirection) {
                if (mTempSlideStartedDirection == DIRECTION_FORWARD) {
                    Log.d(TAG, "isSameDirectionInList -> 方向相反-new往下")
                }
                if (mTempSlideStartedDirection == DIRECTION_REVERSE) {
                    Log.d(TAG, "isSameDirectionInList -> 方向相反-new往上")
                }
                // 反的动作时，则把mIsSlideStarted置成false，重新开始，相当于抬手
                handleDiffDirectionEvent()
            }
        }
        return isSameDirection
    }

    /**
     * 判断用户手指是否抖动，防止用户手指抖动干扰判断
     */
    private fun isUserTingleInList(e: MotionEvent): Boolean {
        if (mLastSlideStartedY != LAST_SLIDE_Y_NOT_START) {
            val moveY = Math.abs(e.y - mLastSlideStartedY)
            if (moveY < AVOID_TINGLE_DISTANCE) {
                Log.d(TAG, "handleMoveEvent -> isUserTingleInList true")
                return true
            }
        }
        Log.d(TAG, "handleMoveEvent -> isUserTingleInList false")
        return false
    }

    private fun handleDiffDirectionEvent() {
        if (DEBUG) {
            Log.d(TAG, "handleDiffDirectionEvent")
        }
        mSelectionTracker.clearProvisionalSelection()
        endSelection()
        mIsSlideStarted = false
        mTempSlideStartedDirection = DIRECTION_NULL
        mLastSlideStartedDirection = DIRECTION_NULL
        mLastSlideStartedY = LAST_SLIDE_Y_NOT_START
        mIsCanSlide = false
        mSlideStateListener?.onSlideSelectionEnd()
    }

    /**
     * exported interface for delete file or folder
     * Given the end position, select everything in-between.
     * @param endPos  The adapter position of the end item.
     */
    private fun extendSelection(endPos: Int) {
        mSelectionTracker.extendProvisionalRange(endPos)
    }

    private fun isInSlideHotspot(e: MotionEvent): Boolean {
        if (DEBUG) {
            Log.d(TAG, "inSelectionHotspot e.y: ${e.y} ")
        }
        val item = mDetailsLookup?.getItemDetails(e)
        item?.let {
            if (DEBUG) {
                Log.d(TAG, "inSelectionHotspot ITEM")
            }
            // check if or not in hotspot when item is not null
            return it.inSelectionHotspot(e)
        }

        // check if or not beyond all item region when item is null
        return mView.getLastGlidedItemPosition(e) != RecyclerView.NO_POSITION
    }

    private fun canSlideSelection(): Boolean {
        return mSelectionTracker.isInSelectionMode()
                && mSelectionTracker.canMultipleSelect()
    }

    /**
     * @return check if or not Intercepted MoveEvent, alway return true for list layout type
     */
    private fun canInterceptedMoveEvent(): Boolean {
        if (!isGridLayout) {
            // list layout just return true
            return true
        }

        return mIsCanSlide
    }

    private val isGridLayout: Boolean
        get() {
            return mSelectionTracker.layoutType == SelectionTracker.LAYOUT_TYPE.GRID
        }
}