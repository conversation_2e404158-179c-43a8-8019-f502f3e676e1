/***************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * Description:Robolectric shadow
 * All rights reserved
 *
 *
 * Version: 1.0
 * Date : 2019/8/12
 * <EMAIL>
 *
 * -------------------Revision History: ----------------------
 *     <author>    <data>     <version >     <desc>
 *   lanjiabo    2019/8/12   1.0            build this moudle
 ****************************************************************/
package shadow.mapping;

import android.os.UserHandle;

import com.oplus.compat.os.UserHandleNative;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import shadow.mapping.annotation.Mapping;
import shadow.mapping.annotation.MappingConstructor;

@Mapping(UserHandleNative.class)
public class MappingUserHandleNative {
    @MappingConstructor
    public MappingUserHandleNative(ClassInitializer ci) {
        try {
            Method method = UserHandle.class.getDeclaredMethod("myUserId");
            method.setAccessible(true);
            int userId = (int)method.invoke(null);
            Method method1 = UserHandle.class.getDeclaredMethod("of",int.class);
            method1.setAccessible(true);
            UserHandleNative.OWNER = (UserHandle)method1.invoke(null,userId);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        UserHandleNative.USER_CURRENT = -2;
        UserHandleNative.USER_ALL = -1;
        UserHandleNative.CURRENT = UserHandleNative.OWNER;
    }
}