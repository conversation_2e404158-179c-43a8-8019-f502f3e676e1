plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

if (org.gradle.internal.os.OperatingSystem.current().isWindows()) {
    apply from: project.file("scripts/exportedApiTest.gradle")
}


final class RecycleBinSharedConstants {
    static final RECYCLE_BIN_EXPORTER_VERSION = 1
    static final RECYCLE_BIN_DB_VERSION = 1
}

android {
    namespace "com.oplus.recyclebin"

    buildFeatures {
        buildConfig = true
    }

    defaultConfig {
        buildConfigField("int", "RECYCLE_BIN_EXPORTER_VERSION", "${RecycleBinSharedConstants.RECYCLE_BIN_EXPORTER_VERSION}")
        buildConfigField("int", "RECYCLE_BIN_DB_VERSION", "${RecycleBinSharedConstants.RECYCLE_BIN_DB_VERSION}")

        manifestPlaceholders = [
                recycle_bin_exporter_version: "${RecycleBinSharedConstants.RECYCLE_BIN_EXPORTER_VERSION}",
                recycle_bin_database_version: "${RecycleBinSharedConstants.RECYCLE_BIN_DB_VERSION}"
        ]
    }
}

dependencies {
    compileOnly(libs.oplus.addon.sdk) { artifact { type = "aar" } }

    implementation libs.androidx.appcompat
    implementation libs.androidx.lifecycle.runtime.ktx
    implementation libs.google.material
    implementation libs.apache.commons.io

    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.dialog
    implementation libs.oplus.appcompat.progressbar
    implementation libs.oplus.appcompat.recyclerview
    implementation libs.oplus.appcompat.scrollbar
    implementation libs.oplus.appcompat.toolbar
    implementation libs.oplus.appcompat.cardview
    implementation libs.oplus.appcompat.poplist
    implementation libs.oplus.appcompat.snackbar
    implementation libs.oplus.appcompat.sidenavigationbar
    implementation libs.oplus.appcompat.bottomnavigation
    implementation libs.oplus.appcompat.tips
    implementation libs.oplus.appcompat.panel
    implementation libs.oplus.tool.trackinglib
    implementation libs.koin.android

    implementation project(':Common')
    implementation project(':FileOperate')
    implementation project(':Provider')
    implementation project(':framework:DFM')

    if (prop_use_prebuilt_drap_drop_lib.toBoolean()) {
        implementation libs.oplus.filemanager.dragDrop
    } else {
        implementation project(':exportedLibs:DragDropSelection')
    }
}