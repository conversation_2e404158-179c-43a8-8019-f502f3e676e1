/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : NoAgreePrivacyDataPacker
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/7 11:40
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.packer

import android.content.Context
import com.filemanager.common.utils.Log
import com.oplus.cardwidget.domain.pack.BaseDataPack
import com.oplus.filemanager.cardwidget.util.StartActivityUtil
import com.oplus.filemanager.cardwidget.util.TextUtil
import com.oplus.smartenginehelper.dsl.DSLCoder

class NoAgreePrivacyDataPacker(
    private val context: Context
) : BaseDataPack() {

    override fun onPack(coder: DSLCoder): Boolean {
        Log.d(DATA_PACKER_TAG, "onPack -> coder = $coder")
        TextUtil.setTextSize(coder, context, ID_CARD_TIPS, TextUtil.TEXT_SIZE_16)
        TextUtil.setTextSize(coder, context, ID_CARD_VIEW, TextUtil.TEXT_SIZE_12)
        val start = StartActivityUtil().startFileManagerMainActivityLabelTab()
        coder.setTextViewText(ID_CARD_TIPS, RES_ID_NO_AGREE_PRIVACY)
        coder.setTextViewText(ID_CARD_VIEW, RES_ID_CARD_VIEW)
        coder.setOnClick(ID_CARD_CONTAINER, start)
        coder.setOnClick(ID_CARD_VIEW, start)
        return true
    }

    companion object {
        private const val DATA_PACKER_TAG = "NoAgreePrivacyDataPacker"
        private const val ID_CARD_CONTAINER = "container"
        private const val ID_CARD_TIPS = "tips"
        private const val ID_CARD_VIEW = "view"
        private const val RES_ID_NO_AGREE_PRIVACY = "@string/card_agree_privacy"
        private const val RES_ID_CARD_VIEW = "@string/card_view"
    }
}