/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : BaseOpenEntity.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * * 飞书中intent中带入的数据结构，在单聊群聊中发送本地文件，intent中的extras中会带入这个类的子类
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.bytedance.ee.bear.contract.drive.sdk.entity.open;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.util.Log;

import androidx.annotation.NonNull;

import com.bytedance.ee.bear.contract.drive.sdk.entity.menu.BaseMoreMenuAction;


import java.util.ArrayList;

public class BaseOpenEntity implements Parcelable {


    public static final Creator<BaseOpenEntity> CREATOR = new Creator<BaseOpenEntity>() {
        @Override
        public BaseOpenEntity createFromParcel(Parcel in) {
            return new BaseOpenEntity(in);
        }

        @Override
        public BaseOpenEntity[] newArray(int size) {
            return new BaseOpenEntity[size];
        }
    };

    public static final String TAG = "BaseOpenEntity";

    private String mAppId;
    private Bundle mExtras;
    private String mFileExt;
    private String mFileId;
    private String mFileName;
    private String mUniqueId;

    protected BaseOpenEntity(Parcel parcel) {
        try {
            this.mAppId = parcel.readString();
            this.mFileId = parcel.readString();
            this.mFileName = parcel.readString();
            this.mUniqueId = parcel.readString();
            ArrayList arrayList = new ArrayList<>();
            parcel.readList(arrayList, BaseMoreMenuAction.class.getClassLoader());
            parcel.readStrongBinder();
            parcel.readInt();
            parcel.readString();
            parcel.readString();
            this.mFileExt = parcel.readString();
            this.mExtras = parcel.readBundle();
            parcel.readInt();
            parcel.readByte();
            parcel.readString();
            parcel.readString();
            parcel.readByte();
            parcel.readString();
            parcel.readLong();
            parcel.readStrongBinder();
        } catch (Exception e) {
            Log.e("HYW", "BaseOpenEntity from parcel error", e);
        }
    }


    public String getFileName() {
        return mFileName;
    }

    public String getFileExt() {
        return mFileExt;
    }

    public String getAppId() {
        return mAppId;
    }

    public String getFileId() {
        return mFileId;
    }

    public String getUniqueId() {
        return mUniqueId;
    }

    public Bundle getExtras() {
        return mExtras;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        Log.i(TAG, "writeToParcel");
    }
}
