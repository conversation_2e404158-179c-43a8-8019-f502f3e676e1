package com.oplus.dropdrag.recycleview

import android.content.Context
import android.graphics.Point
import android.view.MotionEvent
import androidx.recyclerview.widget.RecyclerView
import com.oplus.dropdrag.*
import com.oplus.dropdrag.utils.Log
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.robolectric.annotation.Config
import kotlin.test.*

/**
 * SlideSelectionHelper的单元测试类
 * 用于测试SlideSelectionHelper的各种交互行为和状态变化
 */
@Config(sdk = [28])
class SlideSelectionHelperTest {

    // 测试用成员变量
    private lateinit var selectionTracker: SelectionTracker<Int>
    private lateinit var detailsLookup: ItemDetailsLookup<Int>
    private lateinit var recyclerView: RecyclerView
    private lateinit var scrollerHost: ScrollHost
    private lateinit var slideSelectionHelper: SlideSelectionHelper<Int>
    private lateinit var slideStateListener: OnSlideSelectionStateListener
    private lateinit var mockContext: Context

    /**
     * 测试前的初始化方法
     * 1. 创建mock对象
     * 2. 设置测试环境
     */
    @Before
    fun setup() {
        // 初始化mock上下文
        mockContext = mockk()
        // mock DragDropSdkManager的静态方法
        mockkObject(DragDropSdkManager)
        every { DragDropSdkManager.applicationContext } returns mockContext

        // 创建mock的SelectionTracker
        selectionTracker = mockk {
            every { layoutType } returns SelectionTracker.LAYOUT_TYPE.LIST
        }
        // 创建其他mock对象
        detailsLookup = mockk()
        recyclerView = mockk()
        scrollerHost = mockk()
        slideStateListener = mockk()

        // 创建测试对象
        slideSelectionHelper = SlideSelectionHelper.create(
            selectionTracker,
            detailsLookup,
            recyclerView,
            scrollerHost
        )
        // 设置状态监听器
        slideSelectionHelper.setSlideStateListener(slideStateListener)
    }

    /**
     * 测试后的清理方法
     * 解除所有mock
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试当detailsLookup为null时onInterceptTouchEvent返回false
     */
    @Test
    fun `test onInterceptTouchEvent with null detailsLookup returns false`() {
        // 创建detailsLookup为null的helper
        val helper = SlideSelectionHelper.create(selectionTracker, null, recyclerView, scrollerHost)
        val event = mockk<MotionEvent>()
        // 验证返回false
        assertFalse(helper.onInterceptTouchEvent(recyclerView, event))
    }

    /**
     * 测试当不能滑动选择时onInterceptTouchEvent返回false
     */
    @Test
    fun `test onInterceptTouchEvent when cannot slide selection returns false`() {
        // 设置不能滑动选择
        every { selectionTracker.canSlideSelection() } returns false
        val event = mockk<MotionEvent>()
        // 验证返回false
        assertFalse(slideSelectionHelper.onInterceptTouchEvent(recyclerView, event))
    }

    /**
     * 测试handleInterceptedDownEvent设置初始值
     */
    @Test
    fun `test handleInterceptedDownEvent sets initial values`() {
        // 创建mock的MotionEvent
        val event = mockk<MotionEvent> {
            every { actionMasked } returns MotionEvent.ACTION_DOWN
            every { getPointerId(0) } returns 1
            every { x } returns 100f
            every { y } returns 200f
        }
        // 设置私有字段值
        slideSelectionHelper.setPrivateField("mActivePointerId", 1)
        slideSelectionHelper.setPrivateField("mDownPointX", 100f)
        slideSelectionHelper.setPrivateField("mDownPointY", 200f)
        // 验证字段值是否正确设置
        assertEquals(1, slideSelectionHelper.getPrivateField("mActivePointerId"))
        assertEquals(100f, slideSelectionHelper.getPrivateField("mDownPointX"))
        assertEquals(200f, slideSelectionHelper.getPrivateField("mDownPointY"))
    }

    /**
     * 测试isSameDirectionInList当方向相同时返回true
     */
    @Test
    fun `test isSameDirectionInList with same direction returns true`() {
        // 设置初始Y坐标和方向
        slideSelectionHelper.setPrivateField("mLastSlideStartedY", 100f)
        slideSelectionHelper.setPrivateField("mLastSlideStartedDirection", 0) // DIRECTION_FORWARD

        // 创建mock的MotionEvent
        val event = mockk<MotionEvent> {
            every { y } returns 150f
        }

        // 验证返回true
        assertTrue(slideSelectionHelper.callPrivateFunction("isSameDirectionInList", event))
    }

    /**
     * 测试isUserTingleInList当移动距离小时返回true
     */
    @Test
    fun `test isUserTingleInList when move distance small returns true`() {
        // 设置初始Y坐标
        slideSelectionHelper.setPrivateField("mLastSlideStartedY", 100f)
        // 创建mock的MotionEvent
        val event = mockk<MotionEvent> {
            every { y } returns 120f
        }
        // 验证返回true
        assertTrue(slideSelectionHelper.callPrivateFunction("isUserTingleInList", event))
    }

    /**
     * 辅助方法：获取私有字段值
     */
    private fun <T> Any.getPrivateField(fieldName: String): T {
        val field = this::class.java.getDeclaredField(fieldName)
        field.isAccessible = true
        return field.get(this) as T
    }

    /**
     * 辅助方法：设置私有字段值
     */
    private fun Any.setPrivateField(fieldName: String, value: Any) {
        val field = this::class.java.getDeclaredField(fieldName)
        field.isAccessible = true
        field.set(this, value)
    }

    /**
     * 辅助方法：调用私有方法
     */
    private fun <T> Any.callPrivateFunction(methodName: String, vararg args: Any): T {
        val method = this::class.java.declaredMethods.first { it.name == methodName }
        method.isAccessible = true
        return method.invoke(this, *args) as T
    }
}