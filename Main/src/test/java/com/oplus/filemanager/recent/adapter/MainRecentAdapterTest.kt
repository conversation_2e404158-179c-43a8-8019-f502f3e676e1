/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.recent.adapter.MainRecentAdapterTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/30
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.recent.adapter

import android.app.Activity
import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.isActivityAndInvalid
import com.oplus.filemanager.recent.adapter.viewholder.RecentFileGridVH
import com.oplus.filemanager.recent.adapter.viewholder.RecentFileListVH
import com.oplus.filemanager.recent.entity.recent.BaseRecentEntity
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class MainRecentAdapterTest {

    @MockK
    lateinit var mParent: ViewGroup

    @MockK
    lateinit var mContext: Context

    @MockK
    lateinit var mAdapter: MainRecentAdapter

    @MockK
    lateinit var mView: View

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        every { mParent.context } returns mContext
        mockkStatic(WindowUtils::class)
        every { WindowUtils.supportLargeScreenLayout(any()) }.returns(false)
    }

    @After
    fun after() {
        unmockkStatic(WindowUtils::class)
    }

    @Test
    fun `should return foot view when onCreateViewHolder if view type is footer`() {
        val groupFootViewHolder = spyk(MainRecentAdapter.GroupFootViewHolder(mView))
        every {
            mAdapter.onCreateViewHolder(mParent, BaseRecentEntity.VIEW_TYPE_ITEM_FOOTER)
        } returns groupFootViewHolder
        assertEquals(groupFootViewHolder, mAdapter.onCreateViewHolder(mParent, BaseRecentEntity.VIEW_TYPE_ITEM_FOOTER))
    }

    @Test
    fun `should return group view when onCreateViewHolder if view type is group`() {
        val groupViewHolder = mockk<MainRecentAdapter.GroupViewHolder>()
        every {
            mAdapter.onCreateViewHolder(mParent, BaseRecentEntity.VIEW_TYPE_ITEM_GROUP)
        } returns groupViewHolder
        assertEquals(groupViewHolder, mAdapter.onCreateViewHolder(mParent, BaseRecentEntity.VIEW_TYPE_ITEM_GROUP))
    }

    @Test
    fun `should return RecentFileGridVH when onCreateViewHolder if scan mode is grid`() {
        val viewHolder = mockk<RecentFileGridVH>()
        every {
            mAdapter.onCreateViewHolder(mParent, KtConstants.SCAN_MODE_GRID)
        } returns viewHolder
        assertEquals(viewHolder, mAdapter.onCreateViewHolder(mParent, KtConstants.SCAN_MODE_GRID))
    }

    @Test
    fun `should return RecentFileListVH when onCreateViewHolder if scan mode is list`() {
        val viewHolder = mockk<RecentFileListVH>()
        every {
            mAdapter.onCreateViewHolder(mParent, KtConstants.SCAN_MODE_LIST)
        } returns viewHolder
        assertEquals(viewHolder, mAdapter.onCreateViewHolder(mParent, KtConstants.SCAN_MODE_LIST))
    }

    @Test
    fun `should return RecentFileListVH when onCreateViewHolder if else`() {
        val viewHolder = mockk<RecentFileListVH>()
        every { mAdapter.onCreateViewHolder(mParent, 0) } returns viewHolder
        assertEquals(viewHolder, mAdapter.onCreateViewHolder(mParent, 0))
    }

    @Test
    fun `should mPreSpanIndex is 0 when onBindViewHolder if activity is finishing and destroyed`() {
        val position = 1
        val activity = mockk<Activity> {
            every { isFinishing } returns true
            every { isDestroyed } returns true
            every { isActivityAndInvalid() } returns true
        }
        every { mAdapter.mContext } returns activity
        val holder = mockk<RecyclerView.ViewHolder>()
        every { mAdapter.getItemViewType(position) } returns 1
        every { mAdapter.onBindViewHolder(holder, position) } answers { callOriginal() }
        mAdapter.onBindViewHolder(holder, position)
        verify(inverse = true) { mAdapter.getItemViewType(position) }
    }

    @Test
    fun `should mPreSpanIndex is 0 when onBindViewHolder if activity is finishing`() {
        val position = 1
        val activity = mockk<Activity> {
            every { isFinishing } returns true
            every { isDestroyed } returns false
            every { isActivityAndInvalid() } returns true
        }
        every { mAdapter.mContext } returns activity
        val holder = mockk<RecyclerView.ViewHolder>()
        every { mAdapter.getItemViewType(position) } returns 1
        every { mAdapter.onBindViewHolder(holder, position) } answers { callOriginal() }
        mAdapter.onBindViewHolder(holder, position)
        verify(inverse = true) { mAdapter.getItemViewType(position) }
    }

    @Test
    fun `should mPreSpanIndex is 0 when onBindViewHolder if activity is destroyed`() {
        val position = 1
        val activity = mockk<Activity> {
            every { isFinishing } returns false
            every { isDestroyed } returns true
            every { isActivityAndInvalid() } returns true
        }
        every { mAdapter.mContext } returns activity
        val holder = mockk<RecyclerView.ViewHolder>()
        every { mAdapter.getItemViewType(position) } returns 1
        every { mAdapter.onBindViewHolder(holder, position) } answers { callOriginal() }
        mAdapter.onBindViewHolder(holder, position)
        verify(inverse = true) { mAdapter.getItemViewType(position) }
    }
}