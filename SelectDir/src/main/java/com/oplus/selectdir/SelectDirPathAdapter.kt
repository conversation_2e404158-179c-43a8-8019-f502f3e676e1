/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.adapter
 * * Version     : 1.0
 * * Date        : 2020/7/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir

import android.content.Context
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseFolderAnimAdapter
import com.filemanager.common.base.BaseSelectionViewHolder
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.HiddenFileHelper.isNeedShowHiddenFile
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.isActivityAndInvalid
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.interfaze.cloudconfig.ICloudConfigApi
import com.oplus.selectdir.vh.SelectShortcutFolderViewHolder
import java.lang.ref.WeakReference
import java.util.Locale

private const val TAG = "SelectDirPathAdapter"

class SelectDirPathAdapter(
    content: Context,
    lifecycle: Lifecycle,
    private val actionCode: Int,
    private val mViewModel: SelectDirViewModel?
) :
    BaseFolderAnimAdapter<RecyclerView.ViewHolder, BaseFileBean>(content), LifecycleObserver {

    companion object {
        const val TEST_FILENAME = ".test"  // this name is set for hidden file alpha
    }
    private val mSizeCache = HashMap<String, String>()
    private var mOnRecyclerItemClickListener: OnRecyclerItemClickListener? = null
    private var mThreadManager: ThreadManager
    private val mImgRadius = MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.file_list_bg_radius)

    init {
        mThreadManager = ThreadManager(lifecycle)
        lifecycle.addObserver(this)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        mSizeCache?.clear()
        mUiHandler?.removeCallbacksAndMessages(null)
    }

    fun setOnRecyclerItemClickListener(onRecyclerItemClickListener: OnRecyclerItemClickListener) {
        mOnRecyclerItemClickListener = onRecyclerItemClickListener
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        if (viewType == MessageConstant.MSG_ADD_SHORTCUT_FOLDER) {
            val v = LayoutInflater.from(parent.context).inflate(SelectShortcutFolderViewHolder.layoutId(), parent, false)
            return SelectShortcutFolderViewHolder(v)
        } else {
            val v = LayoutInflater.from(parent.context).inflate(R.layout.selectdir_path_recycler_item, parent, false)
            return SelectPathViewHolder(v)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return actionCode
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (mContext.isActivityAndInvalid()) {
            Log.d(TAG, "onBindViewHolder: Activity is Destroyed!")
            return
        }
        val file = mFiles[position]
        if (holder is SelectPathViewHolder) {
            doData(file, holder, position)
        } else if (holder is SelectShortcutFolderViewHolder) {
            val key = getItemKeyByPosition(position)
            holder.bindData(file, key, position, mFiles.size, mIsDarkModel, mViewModel)
            holder.mCheckBox?.let {
                updateCheckBoxState(it, position)
            }
        }
    }

    @Suppress("LongMethod")
    private fun doData(file: BaseFileBean?, holder: SelectPathViewHolder, position: Int) {
        if (file == null) {
            Log.d(TAG, "doData() file null")
            return
        }
        holder.apply {
            val alpha = HiddenFileHelper.getAlphaWithHidden(file.mDisplayName, mIsDarkModel)
            mTitle?.alpha = alpha
            mDetail?.alpha = alpha
            mImg?.alpha = alpha
            mAnotherName?.alpha = alpha
        }
        holder.mTitle?.visibility = View.VISIBLE
        val path = file.mData
        val type = file.mLocalType
        if (path == null) {
            Log.d(TAG, "doData() path null")
            return
        }
        var titleMaxWidth = mNormalTitleMaxSize
        if (type == MimeTypeHelper.DIRECTORY_TYPE) {
            if (mFolderTitleMaxSize > 0) {
                titleMaxWidth = mFolderTitleMaxSize
            }
            holder.mAnotherName?.tag = path
            holder.mAnotherName?.visibility = View.VISIBLE
            if (!FeatureCompat.sIsExpRom) {
                val cloudConfigApi = Injector.injectFactory<ICloudConfigApi>()
                cloudConfigApi?.updateViewByAlias(holder.mAnotherName, path)
            }
            holder.mJumpImg?.visibility = View.VISIBLE
            holder.mJumpImg?.setTag(com.filemanager.common.R.id.mark_dir, true)
        } else {
            holder.mAnotherName?.visibility = View.GONE
            holder.mJumpImg?.visibility = View.INVISIBLE
            holder.mJumpImg?.setTag(com.filemanager.common.R.id.mark_dir, false)
        }
        holder.mTitle?.text = file.mDisplayName
        holder.mTitle?.maxWidth = titleMaxWidth
        holder.mTitle?.tag = path
        holder.mDetail?.tag = path
        holder.mImg?.let {
            if (file.mLocalType == MimeTypeHelper.IMAGE_TYPE || file.mLocalType == MimeTypeHelper.VIDEO_TYPE) {
                it.setStrokeStyle(FileThumbView.STROKE_4DP)
            } else {
                it.setStrokeStyle(FileThumbView.STROKE_NONE)
            }
            val borderSize = when (file.mLocalType) {
                MimeTypeHelper.IMAGE_TYPE, MimeTypeHelper.VIDEO_TYPE -> KtViewUtils.getBorderSize()
                else -> 0F
            }
            it.setBorderStyle(mImgRadius.toFloat(), borderSize)
            FileImageLoader.sInstance.clear(mContext, it)
            FileImageLoader.sInstance.displayDefault(file, it, 0, mImgRadius)
            holder.mDetail?.visibility = View.VISIBLE
        }
        showDetail(file, holder, path)
        if (type != MimeTypeHelper.DIRECTORY_TYPE) {
            holder.itemView.isEnabled = false
            holder.mTitle?.alpha = HiddenFileHelper.getAlphaWithHidden(TEST_FILENAME, mIsDarkModel)
            holder.mDetail?.alpha = HiddenFileHelper.getAlphaWithHidden(TEST_FILENAME, mIsDarkModel)
            holder.mImg?.alpha = HiddenFileHelper.getAlphaWithHidden(TEST_FILENAME, mIsDarkModel)
        } else {
            val alpha = HiddenFileHelper.getAlphaWithHidden(file.mDisplayName, mIsDarkModel)
            holder.itemView.isEnabled = true
            holder.mTitle?.alpha = alpha
            holder.mDetail?.alpha = alpha
            holder.mImg?.alpha = alpha
        }
        if (position < mFiles.size - 1) {
            holder.mDivider?.visibility = View.VISIBLE
        } else {
            holder.mDivider?.visibility = View.INVISIBLE
        }
    }

    private fun showDetail(file: BaseFileBean, holder: SelectPathViewHolder, path: String) {
        val lastModified = file.mDateModified
        val size = mSizeCache[path + lastModified + isNeedShowHiddenFile()]
        if (size.isNullOrEmpty().not()) {
            val dateAndTime = Utils.getDateFormat(mContext, lastModified)
            holder.mDetail?.text = Utils.formatDetail(mContext, size, dateAndTime)
        } else {
            //ViewHolder reuse causes display exception,holder.mDetail need to set default display text
            holder.mDetail?.text = ""
            setDetail(holder.mDetail, path, file)
        }
    }

    class SelectDirRunnable : FileRunnable {
        constructor(weakTextView: WeakReference<TextView?>, sizeCache: HashMap<String, String>, file: BaseFileBean, uiHandler: Handler, path: String)
                : super(Runnable {
            val formatStorageDetail = if (file.mIsDirectory) {
                val fileCount = JavaFileHelper.listFilesCount(file, excludeHideFile = isNeedShowHiddenFile().not())
                MyApplication.sAppContext.resources.getQuantityString(com.filemanager.common.R.plurals.text_x_items, fileCount, fileCount)
            } else {
                KtUtils.formatSize(file)
            }
            uiHandler.post {
                val textView = weakTextView.get()
                if (textView != null) {
                    val currentPath = textView.tag as? String
                    val lastModified = file.mDateModified
                    if (path == currentPath) {
                        val dateAndTime = Utils.getDateFormat(textView.context, lastModified)
                        sizeCache[path + lastModified + isNeedShowHiddenFile()] = formatStorageDetail
                        textView.text = Utils.formatDetail(textView.context, formatStorageDetail, dateAndTime)
                    }
                }
            }
        }, TAG)
    }

    private fun setDetail(mDetail: TextView?, mPath: String, mFile: BaseFileBean) {
        mThreadManager.execute(SelectDirRunnable(WeakReference(mDetail), mSizeCache, mFile, mUiHandler, mPath))
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getItemKey(item: BaseFileBean, position: Int): Int? {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return position.hashCode()
        }
        return path.toLowerCase(Locale.getDefault()).hashCode()
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        setChoiceModeAnimFlag(flag)
    }

    private class SelectPathViewHolder(convertView: View) : BaseSelectionViewHolder(convertView),
        COUIRecyclerView.ICOUIDividerDecorationInterface {
        var mImg: FileThumbView? = null
        var mJumpImg: ImageView? = null
        var mTitle: TextViewSnippet? = null
        var mDetail: TextView? = null
        var mAnotherName: TextViewSnippet? = null
        var mDivider: View? = null

        init {
            mImg = convertView.findViewById(R.id.file_list_item_icon)
            mJumpImg = convertView.findViewById(R.id.jump_mark)
            mTitle = convertView.findViewById(R.id.file_list_item_title)
            mDetail = convertView.findViewById(R.id.mark_file_list_item_detail)
            mAnotherName = convertView.findViewById(R.id.another_name_view)
            mDivider = convertView.findViewById(R.id.divider_line)
        }
        override fun drawDivider(): Boolean {
            return true
        }

        override fun getDividerStartAlignView(): View {
            return mTitle as View
        }

        override fun getDividerEndInset(): Int {
            return MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp)
        }
    }
}
