/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchCursor
 ** Description : Search Cursor
 ** Version     : 1.0
 ** Date        : 2024/06/24 11:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/06/24       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.cursor

import android.database.AbstractCursor
import android.database.Cursor
import android.os.Bundle
import com.filemanager.common.utils.Log
import com.oplus.filemanager.apiproxy.services.utils.SearchChecker
import org.json.JSONArray
import org.json.JSONObject

class SearchCursor(jsonStr: String) : AbstractCursor() {

    companion object {
        private const val TAG = "SearchCursor"
        private const val CAPACITY = 4

        fun create(code: Int): Cursor {
            val cursor = SearchCursor("[]")
            cursor.extras = Bundle().apply {
                this.putInt(SearchChecker.CODE, code)
            }
            return cursor
        }
    }

    private val jsonArray = JSONArray(jsonStr)
    private val columnNames: Array<String>
    private var curObject: JSONObject? = null
    private var extras: Bundle = Bundle(CAPACITY)

    init {
        val nameList = mutableListOf<String>()
        if (jsonArray.length() > 0) {
            val jsonObj = jsonArray.get(0) as JSONObject

            jsonObj.keys().forEach {
                nameList.add(it)
            }
        }
        columnNames = Array(nameList.size) {
            nameList[it]
        }
        Log.d(TAG, "init columnNames $nameList")
    }

    override fun getCount(): Int {
        return jsonArray.length()
    }

    override fun getColumnNames(): Array<String> {
        return columnNames
    }

    override fun onMove(oldPosition: Int, newPosition: Int): Boolean {
        val moveResult = super.onMove(oldPosition, newPosition)
        if (moveResult) {
            curObject = jsonArray.get(newPosition) as JSONObject
        }
        return moveResult
    }

    override fun getString(column: Int): String {
        return curObject?.getString(columnNames[column]) ?: ""
    }

    override fun getShort(column: Int): Short {
        return curObject?.getInt(columnNames[column])?.toShort() ?: 0
    }

    override fun getInt(column: Int): Int {
        return curObject?.getInt(columnNames[column]) ?: 0
    }

    override fun getLong(column: Int): Long {
        return curObject?.getLong(columnNames[column]) ?: 0
    }

    override fun getFloat(column: Int): Float {
        return curObject?.getDouble(columnNames[column])?.toFloat() ?: 0F
    }

    override fun getDouble(column: Int): Double {
        return curObject?.getDouble(columnNames[column]) ?: 0.0
    }

    override fun isNull(column: Int): Boolean {
        return curObject?.getString(columnNames[column])?.isNotEmpty() ?: true
    }

    override fun getExtras(): Bundle {
        return extras
    }
}