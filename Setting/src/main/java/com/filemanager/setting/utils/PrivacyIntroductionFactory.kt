/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PrivacyIntroductionFactory
 * * Description: 隐私介绍详情的工具类
 * * Version: 1.0
 * * Date : 2024/08/22
 * * Author:W9060445
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     W9060445         2024/08/22      1.0            create
 ****************************************************************/
package com.filemanager.setting.utils

import android.content.Context
import androidx.annotation.StringRes
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.privacy.ICollectPrivacyApi

object PrivacyIntroductionFactory {
    private const val TAG = "PrivacyIntroductionFactory"

    /**
     * 手机号码
     */
    private val PhoneNumberDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(ICollectPrivacyApi.PHONE_NUMBER,
            com.filemanager.common.R.string.use_purpose_feedback_bug,
            com.filemanager.common.R.string.use_scene_feedback
        )
    }

    /**
     * 邮箱地址
     */
    private val EmailAddressDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.EMAIL_ADDRESS,
            com.filemanager.common.R.string.use_purpose_feedback_bug,
            com.filemanager.common.R.string.use_scene_feedback
        )
    }

    /**
     * 图像数据
     */
    private val ImageDataDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.IMAGE_DATA,
            com.filemanager.common.R.string.use_image_data_purpose,
            com.filemanager.common.R.string.use_scene_manager_local_file
        )
    }

    /**
     * 音频数据
     */
    private val AudioDataDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(ICollectPrivacyApi.AUDIO_DATA,
            com.filemanager.common.R.string.use_audio_data_purpose,
            com.filemanager.common.R.string.use_scene_manager_local_file
        )
    }

    /**
     * 视频数据
     */
    private val VideoDataDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(ICollectPrivacyApi.VIDEO_DATA,
            com.filemanager.common.R.string.use_video_data_purpose,
            com.filemanager.common.R.string.use_scene_manager_local_file
        )
    }

    /**
     * 文档数据
     */
    private val DocumentDataDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(ICollectPrivacyApi.DOCUMENT_DATA,
            com.filemanager.common.R.string.use_document_data_purpose,
            com.filemanager.common.R.string.use_scene_manager_local_file
        )
    }

    /**
     * 压缩包数据
     */
    private val CompressDataDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(ICollectPrivacyApi.COMPRESS_DATA,
            com.filemanager.common.R.string.use_compress_data_purpose,
            com.filemanager.common.R.string.use_scene_manager_local_file
        )
    }

    /**
     * 安装包数据
     */
    private val ApkDataDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(ICollectPrivacyApi.APK_DATA,
            com.filemanager.common.R.string.use_apk_data_purpose,
            com.filemanager.common.R.string.use_scene_manager_local_file
        )
    }

    /**
     * DUID
     */
    private val DUIDDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.DUID,
            com.filemanager.common.R.string.use_purpose_auth_account_bind_device,
            com.filemanager.common.R.string.use_scene_view_cloud_file
        )
    }

    /**
     * 设备型号
     */
    private val DeviceModelDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.DEVICE_MODEL,
            com.filemanager.common.R.string.use_purpose_advertising_feedback_bug,
            com.filemanager.common.R.string.use_scene_personal_recommend_feedback
        )
    }

    /**
     * 设备品牌
     */
    private val DeviceBrandDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.DEVICE_BRAND,
            com.filemanager.common.R.string.use_purpose_feedback_bug,
            com.filemanager.common.R.string.use_scene_feedback
        )
    }

    /**
     * 软件OS版本
     */
    private val OsVersionDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.OS_VERSION,
            com.filemanager.common.R.string.use_purpose_advertising_feedback_bug,
            com.filemanager.common.R.string.use_scene_personal_recommend_feedback
        )
    }

    /**
     * 配置信息
     */
    private val ConfigInfoDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.CONFIG_INFO,
            com.filemanager.common.R.string.use_purpose_advertising,
            com.filemanager.common.R.string.use_scene_personal_recommend
        )
    }

    /**
     * SIM卡信息
     */
    private val SimCardInfoDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.SIM_CARD_INFO,
            com.filemanager.common.R.string.use_purpose_advertising,
            com.filemanager.common.R.string.use_scene_personal_recommend
        )
    }

    /**
     * IMSI信息
     */
    private val IMSIInfoDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.IMSI_INFO,
            com.filemanager.common.R.string.use_purpose_advertising,
            com.filemanager.common.R.string.use_scene_personal_recommend
        )
    }

    /**
     * IMEI
     */
    private val IMEIDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.IMEI,
            com.filemanager.common.R.string.use_purpose_advertising_feedback_bug,
            com.filemanager.common.R.string.use_scene_personal_recommend_feedback
        )
    }

    /**
     * SN码
     */
    private val SNCodeDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.SN_CODE,
            com.filemanager.common.R.string.use_purpose_advertising,
            com.filemanager.common.R.string.use_scene_personal_recommend
        )
    }

    /**
     * Open ID
     */
    private val OpenIdDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.OPEN_ID,
            com.filemanager.common.R.string.use_purpose_advertising_feedback_bug,
            com.filemanager.common.R.string.use_scene_personal_recommend_feedback
        )
    }

    /**
     * Android ID
     */
    private val AndroidIdDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.ANDROID_ID,
            com.filemanager.common.R.string.use_purpose_advertising,
            com.filemanager.common.R.string.use_scene_personal_recommend
        )
    }

    /**
     * IDFA
     */
    private val IDFADetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.IDFA,
            com.filemanager.common.R.string.use_purpose_advertising,
            com.filemanager.common.R.string.use_scene_personal_recommend
        )
    }

    /**
     * 应用使用记录
     */
    private val AppUsageRecordsDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.APP_USAGE_RECORDS,
            com.filemanager.common.R.string.use_purpose_advertising,
            com.filemanager.common.R.string.use_scene_personal_recommend
        )
    }

    /**
     * IP地址
     */
    private val IPAddressDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.IP_ADDRESS,
            com.filemanager.common.R.string.use_purpose_advertising,
            com.filemanager.common.R.string.use_scene_personal_recommend
        )
    }

    /**
     * 已安装应用列表
     */
    private val InstalledAppListDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.INSTALLED_APP_LIST,
            com.filemanager.common.R.string.use_purpose_advertising_query_apk,
            com.filemanager.common.R.string.use_scene_personal_recommend_manager_local_file
        )
    }

    /**
     * 本应用软件签名
     */
    private val AppSignDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.APP_SIGN,
            com.filemanager.common.R.string.use_purpose_advertising,
            com.filemanager.common.R.string.use_scene_personal_recommend
        )
    }

    /**
     * 软件包名
     */
    private val SoftPkgDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.SOFT_PKG,
            com.filemanager.common.R.string.use_purpose_advertising,
            com.filemanager.common.R.string.use_scene_personal_recommend
        )
    }

    /**
     * 错误日志报告
     */
    private val ErrorLogReportDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.ERROR_LOG_REPORT,
            com.filemanager.common.R.string.use_purpose_feedback_bug,
            com.filemanager.common.R.string.use_scene_feedback
        )
    }

    /**
     * 埋点信息
     */
    private val BuriedPointInfoDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.BURIED_POINT_INFO,
            com.filemanager.common.R.string.use_purpose_feedback_bug,
            com.filemanager.common.R.string.use_scene_feedback
        )
    }

    /**
     * 增加反馈内容附件
     */
    private val FeedbackContentAttachmentDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.FEEDBACK_CONTENT_ATTACHMENT,
            com.filemanager.common.R.string.use_purpose_feedback_bug,
            com.filemanager.common.R.string.use_scene_feedback
        )
    }

    /**
     * 电信运营商
     */
    private val TelecomOperatorDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.TELECOM_OPERATOR,
            com.filemanager.common.R.string.use_purpose_advertising,
            com.filemanager.common.R.string.use_scene_personal_recommend
        )
    }

    /**
     * 网络环境
     */
    private val NetworkEnvironmentDetail: PrivacyCollectIntroduction by lazy {
        PrivacyCollectIntroduction(
            ICollectPrivacyApi.NETWORK_ENVIRONMENT,
            com.filemanager.common.R.string.use_purpose_advertising,
            com.filemanager.common.R.string.use_scene_personal_recommend
        )
    }

    fun create(category: String): PrivacyCollectIntroduction {
        val detail = when (category) {
            ICollectPrivacyApi.PHONE_NUMBER -> PhoneNumberDetail
            ICollectPrivacyApi.EMAIL_ADDRESS -> EmailAddressDetail
            ICollectPrivacyApi.IMAGE_DATA -> ImageDataDetail
            ICollectPrivacyApi.AUDIO_DATA -> AudioDataDetail
            ICollectPrivacyApi.VIDEO_DATA -> VideoDataDetail
            ICollectPrivacyApi.DOCUMENT_DATA -> DocumentDataDetail
            ICollectPrivacyApi.COMPRESS_DATA -> CompressDataDetail
            ICollectPrivacyApi.APK_DATA -> ApkDataDetail
            ICollectPrivacyApi.DUID -> DUIDDetail
            ICollectPrivacyApi.DEVICE_MODEL -> DeviceModelDetail
            ICollectPrivacyApi.DEVICE_BRAND -> DeviceBrandDetail
            ICollectPrivacyApi.OS_VERSION -> OsVersionDetail
            ICollectPrivacyApi.CONFIG_INFO -> ConfigInfoDetail
            ICollectPrivacyApi.SIM_CARD_INFO -> SimCardInfoDetail
            ICollectPrivacyApi.IMSI_INFO -> IMSIInfoDetail
            ICollectPrivacyApi.IMEI -> IMEIDetail
            ICollectPrivacyApi.SN_CODE -> SNCodeDetail
            ICollectPrivacyApi.OPEN_ID -> OpenIdDetail
            ICollectPrivacyApi.ANDROID_ID -> AndroidIdDetail
            ICollectPrivacyApi.IDFA -> IDFADetail
            ICollectPrivacyApi.APP_USAGE_RECORDS -> AppUsageRecordsDetail
            ICollectPrivacyApi.IP_ADDRESS -> IPAddressDetail
            ICollectPrivacyApi.INSTALLED_APP_LIST -> InstalledAppListDetail
            ICollectPrivacyApi.APP_SIGN -> AppSignDetail
            ICollectPrivacyApi.SOFT_PKG -> SoftPkgDetail
            ICollectPrivacyApi.ERROR_LOG_REPORT -> ErrorLogReportDetail
            ICollectPrivacyApi.BURIED_POINT_INFO -> BuriedPointInfoDetail
            ICollectPrivacyApi.FEEDBACK_CONTENT_ATTACHMENT -> FeedbackContentAttachmentDetail
            ICollectPrivacyApi.TELECOM_OPERATOR -> TelecomOperatorDetail
            ICollectPrivacyApi.NETWORK_ENVIRONMENT -> NetworkEnvironmentDetail
            else -> PhoneNumberDetail
        }
        Log.d(TAG, "get $category->$detail")
        return detail
    }
}


/**
 * 隐私搜索的介绍
 */
data class PrivacyCollectIntroduction(
    var category: String, // 分类
    @StringRes var usePurpose: Int, // 使用目的
    @StringRes var useScene: Int, // 使用场景
    var collectCount: Int = 0, // 收集次数
    var info: CollectContent? = null // 收集的内容
) {

    fun getInfoContent(context: Context, encryptFun: ((String) -> String)? = null): String {
        if (info is ListCollectContent) {
            val listInfo = info as ListCollectContent
            val builder = StringBuilder()
            val lastIndex = listInfo.content.size - 1
            listInfo.content.forEachIndexed { index, item ->
                if ((encryptFun != null) && listInfo.encrypt) {
                    builder.append(encryptFun.invoke(item))
                } else {
                    builder.append(item)
                }
                if (index < lastIndex) {
                    builder.append(";")
                }
            }
            return builder.toString()
        } else if (info is NoneCollectContent) {
            return (info as NoneCollectContent).content
        } else {
            return "/"
        }
    }
}

/**
 * 收集信息
 */
sealed class CollectContent()

/**
 * 收集信息：空
 */
data class NoneCollectContent(val content: String = "/") : CollectContent()

/**
 * 收集信息：有实际内容
 */
data class ListCollectContent(var content: List<String> = mutableListOf(), var encrypt: Boolean = false) : CollectContent()