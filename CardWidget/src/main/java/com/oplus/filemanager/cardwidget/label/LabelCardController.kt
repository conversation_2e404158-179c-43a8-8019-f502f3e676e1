/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.cardwidget.label.LabelCardController
 * * Description :  标签卡片逻辑控制层
 * * Version     : 1.0
 * * Date        : 2022/12/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.controller.PersonalizedServiceController
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.noMoreAction
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.cardwidget.domain.action.CardWidgetAction
import com.oplus.filemanager.cardwidget.label.data.LabelCardCacheData
import com.oplus.filemanager.cardwidget.label.data.LabelCardData
import com.oplus.filemanager.cardwidget.label.packer.LabelCardDataPacker
import com.oplus.filemanager.cardwidget.label.packer.NoAgreePrivacyDataPacker
import com.oplus.filemanager.cardwidget.label.packer.NoLabelDataPacker
import com.oplus.filemanager.cardwidget.label.packer.NoSelectedLabelDataPacker
import com.oplus.filemanager.cardwidget.label.packer.NoStoragePermissionDataPacker
import com.oplus.filemanager.cardwidget.label.packer.SelectedLabelNoFileDataPack
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class LabelCardController {

    companion object {
        private const val TAG = "LabelCardController"
        private const val LAYOUT_NAME_2_TO_4 = "label_card_widget_layout_2_to_4.json"
        private const val TABLET_LAYOUT_NAME_2_TO_4 = "tablet_label_card_widget_layout_2_to_4.json"
        private const val LAYOUT_NO_PERMISSION = "no_permission_layout.json"
        private const val LAYOUT_LABEL_CARD_TIPS_WITH_ACTION = "label_card_tips_with_action.json"
        private const val LAYOUT_LABEL_WITH_TIPS = "label_with_tips_layout.json"
        private const val LAYOUT_NO_AGREE_PRIVACY = "no_agree_privacy_layout.json"
    }

    @VisibleForTesting
    val widgetCodeUtils = LabelCardWidgetCodeUtils.instance
        get() = field.noMoreAction()

    /**
     * 获取布局json名称。因不会每次固定调用，更新布局根据刷新时数据修改
     */
    fun getCardLayoutName(widgetCode: String): String {
        return LAYOUT_NO_PERMISSION
    }

    private val personalizedServiceController: PersonalizedServiceController by lazy { PersonalizedServiceController() }

    /**
     * 更新卡片
     *
     * 1. Agree privacy.
     *
     * 2. Check storage permission.
     *
     * 3. Obtain label list, determine whether there is currently a tag list and selected label.
     *
     * 4. load selected label data.
     */
    fun refreshOnResume(context: Context, widgetCode: String) {
        // 1. check agree privacy
        val hasAgreePrivacy = PrivacyPolicyController.hasAgreePrivacy(context)
        var isNeedAgreeAdPersonalService = false
        if (FeatureCompat.sIsExpRom) {
            isNeedAgreeAdPersonalService = personalizedServiceController.isNeedAgreeAdPersonalService(context)
        }
        Log.d(TAG, "refreshOnResume -> hasAgreePrivacy = $hasAgreePrivacy, isNeedAgreeAdPersonalService = $isNeedAgreeAdPersonalService")
        if (!hasAgreePrivacy || isNeedAgreeAdPersonalService) {
            refreshCardNotAgreePrivacy(context, widgetCode)
            return
        }

        // 2. check storage permission
        val isPermissionGranted = PermissionUtils.hasStoragePermission(context)
        Log.d(TAG, "refreshOnResume -> permissionGranted = $isPermissionGranted")
        if (!isPermissionGranted) {
            refreshCardNoPermission(context, widgetCode)
            return
        }

        // 3. check label lists size
        val cache = widgetCodeUtils.getCacheByWidgetCode(context, widgetCode)
        Log.d(TAG, "refreshOnResume getCacheByWidgetCode:$cache")
        val hasLabel: Boolean = FileLabelDBHelper.getAllLabels().isNotEmpty()
        if (cache == null || cache.cardStatus == LabelCardCacheData.STATUS_INIT) {
            //无标签，显示去添加页面，若有，显示标签卡片
            Log.d(TAG, "refreshOnResume hasLabel:$hasLabel")
            refreshCardNoLabels(widgetCode, context, hasLabel)
            return
        }

        // 4. check selected label state
        labelCardScope.launch(Dispatchers.IO) {
            val cardData = findLabelById(cache.labelId)
            Log.d(TAG, "refreshOnResume findLabelById:$cardData")
            cardData?.fileList = cardData?.fileList?.filter {
                it.checkExist()
            }
            cardData?.fileCount = cardData?.fileList?.size ?: 0
            Log.d(TAG, "refreshOnResume findLabelById after:$cardData")
            cardData?.let {
                if (cardData.fileCount == 0) {
                    //无文件
                    refreshCardEmptyFiles(context, cardData, widgetCode)
                } else {
                    refreshCardLabels(context, cardData, widgetCode)
                }
                return@launch
            }
            Log.d(TAG, "refreshOnResume cardData == null")
            //删除后的标签
            refreshCardNoLabels(widgetCode, context, hasLabel)
        }
    }

    @VisibleForTesting
    fun refreshCardLabels(
        context: Context,
        cardData: LabelCardData,
        widgetCode: String
    ) {
        val layoutName = if (ModelUtils.isTablet()) TABLET_LAYOUT_NAME_2_TO_4 else LAYOUT_NAME_2_TO_4
        CardWidgetAction.postUpdateCommand(LabelCardDataPacker(context, cardData), widgetCode, layoutName)
        Log.d(TAG, "refreshCardLabels end")
    }

    @VisibleForTesting
    fun refreshCardEmptyFiles(context: Context, cardData: LabelCardData, widgetCode: String) {
        CardWidgetAction.postUpdateCommand(SelectedLabelNoFileDataPack(context, cardData), widgetCode, LAYOUT_LABEL_CARD_TIPS_WITH_ACTION)
        Log.d(TAG, "refreshCardEmptyFiles end")
    }

    @VisibleForTesting
    fun refreshCardNoLabels(widgetCode: String, context: Context, hasLabel:Boolean) {
        val dataPack = if (hasLabel) {
            NoSelectedLabelDataPacker(context, widgetCode)
        } else {
            NoLabelDataPacker(context)
        }
        CardWidgetAction.postUpdateCommand(dataPack, widgetCode, LAYOUT_LABEL_WITH_TIPS)
        Log.d(TAG, "refreshCardNoLabels end")
    }

    @VisibleForTesting
    fun refreshCardNoPermission(context: Context, widgetCode: String) {
        CardWidgetAction.postUpdateCommand(NoStoragePermissionDataPacker(context), widgetCode, LAYOUT_NO_PERMISSION)
        Log.d(TAG, "refreshCardNoPermission end")
    }

    @VisibleForTesting
    fun refreshCardNotAgreePrivacy(context: Context, widgetCode: String) {
        CardWidgetAction.postUpdateCommand(NoAgreePrivacyDataPacker(context), widgetCode, LAYOUT_NO_AGREE_PRIVACY)
    }

    private fun findLabelById(labelId: Long?): LabelCardData? {
        labelId ?: return null
        val label = FileLabelDBHelper.getFileLabelById(labelId)
        label ?: return null
        return getLabelCardData(label)
    }

    /**
     * 添加卡片
     */
    fun addCard(widgetCode: String) {
        widgetCodeUtils.getCacheList().forEach { cache ->
            //有相同的卡片ID，排除掉
            if (cache.widgetCode == widgetCode) {
                return
            }
        }
        widgetCodeUtils.saveCard(widgetCode, LabelCardCacheData(widgetCode, null, LabelCardCacheData.STATUS_INIT))
    }

    private fun getLabelCardData(label: FileLabelEntity): LabelCardData {
        val files = FileLabelMappingDBHelper.getFileListByLabelId(label.id)
        files?.let { files ->
            val fileList = files.map { mapping->
                val baseFile = BaseFileBean()
                PathFileWrapper(mapping.filePath).apply {
                    baseFile.mDisplayName = mDisplayName
                    baseFile.mSize = mSize
                    baseFile.mDateModified = mDateModified
                    baseFile.mLocalType = mLocalType
                    baseFile.mData = mData
                    baseFile.mMediaDuration = mapping.duration
                }
                baseFile
            }
            val currentSort = SortModeUtils.getSharedSortMode(MyApplication.sAppContext, SortRecordModeFactory.getLabelKey())
            val isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getLabelKey())
            val mLastSort = SortModeUtils.getSharedSortMode(MyApplication.sAppContext, SortModeUtils.BROWSER_LAST_SORT_RECORD)
            SortHelper.sortFiles(fileList, currentSort, mLastSort, false, isDesc)
            return LabelCardData(label.id, label.name, files.size, fileList.filter { it.mData != null })
        }
        return LabelCardData(label.id, label.name, 0, null)
    }

    /**
     * 移除卡片
     */
    fun removeCard(widgetCode: String) {
        widgetCodeUtils.deleteCard(widgetCode)
    }
}