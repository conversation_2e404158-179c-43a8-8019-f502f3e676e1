/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RemoteFileSearchMethodImpl
 ** Description : remote file Search method impl
 ** Version     : 1.0
 ** Date        : 2025/02/19 17:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2025/02/19       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.loader

import android.content.ContentResolver
import android.content.Context
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.bean.remotedevice.RemoteFileData
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.apiproxy.services.data.SearchResultData
import com.oplus.filemanager.apiproxy.services.loader.SearchDocLoader.Companion.LIMIT_COUNT
import com.oplus.filemanager.apiproxy.services.utils.SearchFileType
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking

class RemoteFileSearchMethodImpl(context: Context) : SearchMethod(context) {

    companion object {
        private const val TAG = "RemoteFileSearchMethodImpl"
    }

    private val remoteApi: IRemoteDevice? by lazy {
        Injector.injectFactory<IRemoteDevice>()
    }

    private var deviceId: String = ""


    override fun isSupport(keyword: String, fileType: Int): Boolean {
        // 判断是否连接
        deviceId = remoteApi?.getCurrentLinkedRemoteDiceInfo()?.deviceId ?: ""
        if (deviceId.isEmpty()) {
            Log.e(TAG, "isSupport device hasn't connected")
            return false
        }
        return true
    }

    override fun handleSearch(contentResolver: ContentResolver, keyword: String, fileType: Int): List<BaseFileBean> {
        // 获取deviceId
        val deviceId = remoteApi?.getCurrentLinkedRemoteDiceInfo()?.deviceId
        if (deviceId.isNullOrEmpty()) {
            Log.e(TAG, "handleSearch device hasn't connected")
            return emptyList()
        }
        val suffixList = SearchFileType.getFilterSuffixList(fileType)
        val data = batchSearch(deviceId, keyword, suffixList)
        Log.d(TAG, "search $keyword type:$fileType result size:${data.size}")
        return data.map {
            RemoteFileBean(it)
        }
    }

    /**
     * 批量搜索
     * @param deviceId 远程设备id
     * @param keyword 搜索的关键词
     * @param suffixList 要搜索文件的后缀列表
     * @return 返回搜索的文件列表
     */
    private fun batchSearch(deviceId: String, keyword: String, suffixList: List<String>): List<RemoteFileData> {
        if (suffixList.isEmpty()) {
            return emptyList()
        }
        val list = ArrayList<RemoteFileData>()
        runBlocking {
            val jobList = mutableListOf<Deferred<List<RemoteFileData>>>()
            suffixList.forEach { suffix ->
                /**
                 * 采用通配符的方式来进行搜索
                 * 例如：外部传入keyword:测试，fileType：1（word文档），转换后的搜索关键词为："*测试*.doc"
                 */
                val searchKey = "*$keyword*$suffix"
                val job = async(Dispatchers.IO) {
                    remoteApi?.query(deviceId, searchKey, 1, LIMIT_COUNT) ?: emptyList()
                }
                jobList.add(job)
            }
            jobList.forEach { job ->
                val data = job.await()
                list.addAll(data)
            }
        }
        return list.filterNot {
            it.isDir
        }.distinctBy { it.path }
    }

    override fun classifyItem(list: List<BaseFileBean>, searchResult: SearchResultData) {
        val data = list.filterIsInstance<RemoteFileBean>()
        // 排序
        val sort = SortHelper.FILE_TIME_REVERSE_ORDER
        Injector.injectFactory<IDocumentExtensionType>()?.sortFiles(data, sort, sort, true, true)
        searchResult.classifyRemoteFileSearchList(data)
    }
}