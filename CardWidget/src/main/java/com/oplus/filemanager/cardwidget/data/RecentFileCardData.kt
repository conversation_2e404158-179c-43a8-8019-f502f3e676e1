/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentFileCardData.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/9
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/9      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.data

class RecentFileCardData {

    companion object {
        const val UI_STATE_NO_STORAGE_PERMISSION = 0
        const val UI_STATE_NO_PRIVACY = 1
        const val UI_STATE_LOADING_SUCCESS = 2
        const val UI_STATE_LOADING_EMPTY = 3
        const val UI_STATE_LOADING_FAIL = 4
    }

    var uiState = 0
    var recentFiles: MutableList<RecentFileCardItemData> = mutableListOf()
}