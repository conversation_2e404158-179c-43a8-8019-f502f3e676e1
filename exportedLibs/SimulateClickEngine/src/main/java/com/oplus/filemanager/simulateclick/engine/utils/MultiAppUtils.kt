/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - MultiAppUtils.kt
 * Description:
 *     The utils to check multi app mode.
 *
 * Version: 1.0
 * Date: 2024-06-19
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-06-19   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.utils

import androidx.annotation.VisibleForTesting
import com.oplus.multiapp.OplusMultiAppManager

internal object MultiAppUtils {

    private const val TAG = "MultiAppUtils"
    private const val ACCESS_MODE_UNKNOWN = -1
    private const val ACCESS_MODE_CHOOSE = OplusMultiAppManager.DEFAULT_ACCESS
    private const val METHOD_GET_ACCESS_MODE = "getMultiAppAccessMode"

    @JvmStatic
    fun isChooseCloneApp(packageName: String): Boolean {
        if (!SdkUtils.isAtLeastOS12() || !SdkUtils.isAtLeastS()) {
            return false
        }
        val multiAppMgs = OplusMultiAppManager.getInstance()
        if (!multiAppMgs.isAppHasClone(packageName)) {
            return false
        }
        return multiAppMgs.readMultiAppAccessMode(packageName) == ACCESS_MODE_CHOOSE
    }

    @JvmStatic
    private fun OplusMultiAppManager.isAppHasClone(packageName: String): Boolean {
        if (!isMultiAppSupport) {
            return false
        }
        val multiAppList = getMultiAppList(OplusMultiAppManager.LIST_TYPE_CREATED)
        Log.d(TAG, "isAppHasClone: packageName=$packageName, multiAppList=$multiAppList")
        return multiAppList?.contains(packageName) ?: false
    }

    /**
     * OplusMultiAppManager#getMultiAppAccessMode存在于框架层但未添加至addon SDK
     * 故反射调用该方法以实现读取对应分身应用的快捷功能状态设置，以便判断是否会在拉起时弹出分身选择框
     */
    @VisibleForTesting
    @JvmStatic
    internal fun OplusMultiAppManager.readMultiAppAccessMode(packageName: String): Int {
        val result = kotlin.runCatching {
            val clazz = OplusMultiAppManager::class.java
            val method = clazz.getMethod(METHOD_GET_ACCESS_MODE, String::class.java)
            return@runCatching method.invoke(this@readMultiAppAccessMode, packageName) as? Int
        }.onFailure {
            Log.e(TAG, "readMultiAppAccessMode: ERROR!", it)
        }.getOrNull()
        Log.d(TAG, "readMultiAppAccessMode: packageName=$packageName, accessMode=$result")
        return result ?: ACCESS_MODE_UNKNOWN
    }
}