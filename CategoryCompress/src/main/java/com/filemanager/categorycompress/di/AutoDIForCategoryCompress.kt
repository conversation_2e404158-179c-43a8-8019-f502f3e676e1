/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: AutoDIForCategoryCompress.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2024/6/6
 ** Author: yangqichang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.categorycompress.di

import com.filemanager.categorycompress.CategoryCompressApi
import com.oplus.filemanager.interfaze.categorycompress.ICategoryCompressApi
import org.koin.dsl.module

object AutoDIForCategoryCompress {

    val categoryCompressApi = module {
        single<ICategoryCompressApi>(createdAtStart = true) {
            CategoryCompressApi
        }
    }
}