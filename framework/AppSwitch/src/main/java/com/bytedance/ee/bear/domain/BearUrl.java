/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : BearUrl.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * * 飞书中intent中带入的数据结构，在查看云文档（我的空间，共享空间，收藏，离线等）中的云端上传的云文档，intent中的extras中会带入这个类的类
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.bytedance.ee.bear.domain;


import android.os.Parcel;
import android.os.Parcelable;
import android.util.Log;

import androidx.annotation.NonNull;


import java.util.HashMap;
import java.util.Map;

public class BearUrl implements Parcelable {

    public static final Creator<BearUrl> CREATOR = new Creator<BearUrl>() {
        @Override
        public BearUrl createFromParcel(Parcel in) {
            return new BearUrl(in);
        }

        @Override
        public BearUrl[] newArray(int size) {
            return new BearUrl[size];
        }
    };

    public static final String TAG = "BearUrl";
    public Map<String, String> mE = new HashMap();
    private String mA;
    private String mB;
    private String mC;
    private String mD;

    private String mF;
    private String mG;
    private String mH;
    private String mI;
    private String mJ;
    private String mK;
    private String mL;
    private String mM;
    private String mN;
    private String mO;
    private String mP;
    private String mQ;
    private String mR;
    private String mS;
    private String mT;


    protected BearUrl(Parcel parcel) {
        this.mB = parcel.readString();
        this.mC = parcel.readString();
        this.mD = parcel.readString();
        this.mF = parcel.readString();
        this.mG = parcel.readString();
        this.mI = parcel.readString();
        this.mJ = parcel.readString();
        this.mK = parcel.readString();
        this.mH = parcel.readString();
        this.mL = parcel.readString();
        this.mM = parcel.readString();
        this.mN = parcel.readString();
        this.mO = parcel.readString();
        this.mP = parcel.readString();
        this.mR = parcel.readString();
        this.mS = parcel.readString();
        //
        this.mE = parcel.readHashMap(getClass().getClassLoader());
        this.mT = parcel.readString();
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        Log.i(TAG, "writeToParcel");
    }


    @Override
    public String toString() {
        return "BearUrl{"
                + "a='" + mA + '\''
                + ", b='" + mB + '\''
                + ", c='" + mC + '\''
                + ", d='" + mD + '\''
                + ", e=" + mE
                + ", f='" + mF + '\''
                + ", g='" + mG + '\''
                + ", h='" + mH + '\''
                + ", i='" + mI + '\''
                + ", j='" + mJ + '\''
                + ", k='" + mK + '\''
                + ", l='" + mL + '\''
                + ", m='" + mM + '\''
                + ", n='" + mN + '\''
                + ", o='" + mO + '\''
                + ", p='" + mP + '\''
                + ", q='" + mQ + '\''
                + ", r='" + mR + '\''
                + ", s='" + mS + '\''
                + ", t='" + mT + '\''
                + '}';
    }
}
