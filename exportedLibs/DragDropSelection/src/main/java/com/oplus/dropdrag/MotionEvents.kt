/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - MotionEvents.kt
 * Description: MotionEvents Util
 * Version: 1.0
 * Date : 2020/05/15
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/05/15    1.0     create
 ***********************************************************************/
package com.oplus.dropdrag

import android.graphics.Point
import android.view.KeyEvent
import android.view.MotionEvent

/**
 * Utility methods for working with [MotionEvent] instances.
 */
internal object MotionEvents {
    @JvmStatic
    fun isMouseEvent(e: MotionEvent): Boolean {
        return e.getToolType(0) == MotionEvent.TOOL_TYPE_MOUSE
    }

    @JvmStatic
    fun isTouchEvent(e: MotionEvent): Boolean {
        return e.getToolType(0) == MotionEvent.TOOL_TYPE_FINGER
    }

    @JvmStatic
    fun isActionMove(e: MotionEvent): Boolean {
        return e.actionMasked == MotionEvent.ACTION_MOVE
    }

    @JvmStatic
    fun isActionDown(e: MotionEvent): Boolean {
        return e.actionMasked == MotionEvent.ACTION_DOWN
    }

    @JvmStatic
    fun isActionUp(e: MotionEvent): Boolean {
        return e.actionMasked == MotionEvent.ACTION_UP
    }

    @JvmStatic
    fun isActionPointerUp(e: MotionEvent): Boolean {
        return e.actionMasked == MotionEvent.ACTION_POINTER_UP
    }

    @JvmStatic
    fun isActionPointerDown(e: MotionEvent): Boolean {
        return e.actionMasked == MotionEvent.ACTION_POINTER_DOWN
    }

    @JvmStatic
    fun isActionCancel(e: MotionEvent): Boolean {
        return e.actionMasked == MotionEvent.ACTION_CANCEL
    }

    @JvmStatic
    fun getOrigin(e: MotionEvent): Point {
        return Point(e.x.toInt(), e.y.toInt())
    }

    @JvmStatic
    fun isPrimaryMouseButtonPressed(e: MotionEvent): Boolean {
        return isButtonPressed(e, MotionEvent.BUTTON_PRIMARY)
    }

    @JvmStatic
    fun isSecondaryMouseButtonPressed(e: MotionEvent): Boolean {
        return isButtonPressed(e, MotionEvent.BUTTON_SECONDARY)
    }

    @JvmStatic
    fun isTertiaryMouseButtonPressed(e: MotionEvent): Boolean {
        return isButtonPressed(e, MotionEvent.BUTTON_TERTIARY)
    }

    @JvmStatic
    // NOTE: Can replace this with MotionEvent.isButtonPressed once targeting 21 or higher.
    private fun isButtonPressed(e: MotionEvent, button: Int): Boolean {
        return if (button == 0) {
            false
        } else {
            e.buttonState and button == button
        }
    }

    @JvmStatic
    fun isShiftKeyPressed(e: MotionEvent): Boolean {
        return hasBit(e.metaState, KeyEvent.META_SHIFT_ON)
    }

    @JvmStatic
    fun isCtrlKeyPressed(e: MotionEvent): Boolean {
        return hasBit(e.metaState, KeyEvent.META_CTRL_ON)
    }

    @JvmStatic
    fun isAltKeyPressed(e: MotionEvent): Boolean {
        return hasBit(e.metaState, KeyEvent.META_ALT_ON)
    }

    @JvmStatic
    fun isTouchpadScroll(e: MotionEvent): Boolean {
        // Touchpad inputs are treated as mouse inputs, and when scrolling, there are no buttons
        // returned.
        return isMouseEvent(e) && isActionMove(e) && e.buttonState == 0
    }

    /**
     * Returns true if the event is a drag event (which is presumbaly, but not
     * explicitly required to be a mouse event).
     * @param e
     */
    @JvmStatic
    fun isPointerDragEvent(e: MotionEvent): Boolean {
        return (isPrimaryMouseButtonPressed(e)
                && isActionMove(e))
    }

    @JvmStatic
    private fun hasBit(metaState: Int, bit: Int): Boolean {
        return metaState and bit != 0
    }
}