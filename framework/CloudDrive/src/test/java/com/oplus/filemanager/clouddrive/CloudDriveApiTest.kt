/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : CloudDriveApiTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/9 11:35
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/9       1.0      create
 **********************************************************************/
package com.oplus.filemanager.clouddrive

import android.content.Context
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import kotlin.time.Duration.Companion.seconds

class CloudDriveApiTest {

    private lateinit var context: Context

    @Before
    fun setup() {
        MockKAnnotations.init()
        context = mockk(relaxed = true)
    }

    @After
    fun teardown() {
        unmockkAll()
    }

    @Test
    fun testGetCloudPackageName() {
        mockkStatic(CloudDriveManager::class)
        every { CloudDriveManager.getCloudPackageName(context) }.returns(CLOUD_PACKAGE_NAME)
        val packageName = CloudDriveApi().getCloudPackageName(context)
        Assert.assertEquals(CLOUD_PACKAGE_NAME, packageName)
        unmockkStatic(CloudDriveManager::class)
    }

    @Test
    fun testSupportCloudDisk() {
        runTest(timeout = testCaseTimeout) {
            mockkObject(GlobalSwitcherUtils)
            every { GlobalSwitcherUtils.supportCloudDisk }.returns(true)
            val support = CloudDriveApi().supportCloudDisk()
            Assert.assertTrue(support)
            unmockkObject(GlobalSwitcherUtils)
        }
    }

    @Test
    fun testSupportCloudDiskAndIsPepProject() {
        mockkStatic(GlobalSwitcherUtils::class)
        every { GlobalSwitcherUtils.supportCloudDisk() }.returns(true)
        val support = CloudDriveApi().isSupportCloudDisk()
        Assert.assertTrue(support)
        unmockkStatic(GlobalSwitcherUtils::class)
    }

    @Test
    fun testUploadFiles() {
        mockkStatic(CloudDriveManager::class)
        justRun { CloudDriveManager.uploadFiles(context, any()) }
        CloudDriveApi().uploadFiles(context, arrayListOf())
        verify { CloudDriveManager.uploadFiles(context, any()) }
        unmockkStatic(CloudDriveManager::class)
    }

    @Test
    fun testStartCloudDiskActivity() {
        mockkStatic(CloudDriveManager::class)
        justRun { CloudDriveManager.startCloudDiskActivity(context) }
        CloudDriveApi().startCloudDiskActivity(context)
        verify { CloudDriveManager.startCloudDiskActivity(context) }
        unmockkStatic(CloudDriveManager::class)
    }

    companion object {
        private const val CLOUD_PACKAGE_NAME = "com.heytap.cloud"
        private val testCaseTimeout = 30.seconds
    }
}