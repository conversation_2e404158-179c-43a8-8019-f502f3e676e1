/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScreenRotationFlag.kt
 * Description:
 *     The flag for configuring rotation type in Script.
 *
 * Version: 1.0
 * Date: 2024-06-05
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><EMAIL>    2024-06-05   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.value

import com.google.gson.GsonBuilder
import com.oplus.filemanager.simulateclick.engine.utils.convertAliasToFlagValue
import com.oplus.filemanager.simulateclick.engine.utils.convertFlagValueToAlias

internal data class ScreenRotationFlag(override val flag: Int = DEFAULT) : IScriptFlagValue {

    constructor(aliasValue: String?) : this(fromAlias(aliasValue))

    override fun toAlias(): String = toAlias(flag)

    override fun toString(): String = "ScreenRotationFlag(flag=${toAlias(flag)})"

    companion object {
        const val ROTATION_0 = 0x0001
        const val ROTATION_90 = 0x0002
        const val ROTATION_180 = 0x0004
        const val ROTATION_270 = 0x0008
        const val ROTATION_ALL = ROTATION_0 or ROTATION_90 or ROTATION_180 or ROTATION_270
        private const val DEFAULT = ROTATION_ALL

        private const val ALIAS_0 = "0"
        private const val ALIAS_90 = "90"
        private const val ALIAS_180 = "180"
        private const val ALIAS_270 = "270"
        private const val ALIAS_ALL = "all"

        @JvmStatic
        private fun fromAlias(aliasValue: String?): Int = convertAliasToFlagValue(
            aliasValue,
            DEFAULT,
            ALIAS_0 to ROTATION_0,
            ALIAS_90 to ROTATION_90,
            ALIAS_180 to ROTATION_180,
            ALIAS_270 to ROTATION_270,
            ALIAS_ALL to ROTATION_ALL
        )

        @JvmStatic
        private fun toAlias(flag: Int): String = convertFlagValueToAlias(
            flag,
            ALIAS_0 to ROTATION_0,
            ALIAS_90 to ROTATION_90,
            ALIAS_180 to ROTATION_180,
            ALIAS_270 to ROTATION_270
        )

        @JvmStatic
        fun registerJsonAdapter(gsonBuilder: GsonBuilder) {
            gsonBuilder.registerTypeAdapter(ScreenRotationFlag::class.java, JsonAdapter())
        }
    }

    class JsonAdapter : ScriptJsonAdapter<ScreenRotationFlag>() {
        override fun onWrite(value: ScreenRotationFlag): String = value.toAlias()
        override fun onRead(jsonStr: String): ScreenRotationFlag = ScreenRotationFlag(jsonStr)
    }
}