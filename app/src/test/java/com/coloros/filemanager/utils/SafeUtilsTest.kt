package com.coloros.filemanager.utils

import android.content.Context
import com.filemanager.common.constants.SafeConstants
import com.filemanager.common.helper.FileWrapper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.SafeUtils
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import io.mockk.*
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SafeUtilsTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun should_verify_FileList() {
        SafeUtils.cleanEncryptionFileList()

        var list = arrayListOf(mockk<FileWrapper>(relaxed = true))
        SafeUtils.saveEncryptionFileList(list)
        Assert.assertEquals(list, SafeUtils.getEncryptionFileList())

        SafeUtils.cleanEncryptionFileList()
    }

    @Test
    fun should_verify_getRootPath() {
        var context = mockk<Context>(relaxed = true)

        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getInternalSdPath(context) }.returns("test")

        Assert.assertEquals("test", SafeUtils.getRootPath(context))

        every { VolumeEnvironment.getInternalSdPath(context) }.returns(null)
        every { VolumeEnvironment.getExternalSdPath(context) }.returns("test")
        Assert.assertEquals("test", SafeUtils.getRootPath(context))
    }

    @Test
    fun should_verify_isStorageEnable() {
        Assert.assertTrue(SafeUtils.isStorageEnable(0, 0, null))

        mockkStatic(Utils::class)
        mockkStatic(SafeUtils::class)
        every { SafeUtils.isDirectlyEncryption(0) }.returns(true)
        every { Utils.getStorageAvailableSize("test") }.returns(10)
        Assert.assertFalse(SafeUtils.isStorageEnable(0, 0, "test"))

        every { SafeUtils.isDirectlyEncryption(0) }.returns(false)
        every { Utils.getStorageAvailableSize("test") }.returns((SafeConstants.THRESHOLD + 100))
        Assert.assertTrue(SafeUtils.isStorageEnable(0, 0, "test"))
    }

    @Test
    fun should_verify_isDirectlyEncryption() {
        var type = MimeTypeHelper.IMAGE_TYPE
        Assert.assertTrue(SafeUtils.isDirectlyEncryption(type))
        type = MimeTypeHelper.VIDEO_TYPE
        Assert.assertTrue(SafeUtils.isDirectlyEncryption(type))
        type = MimeTypeHelper.ZIP_TYPE
        Assert.assertTrue(SafeUtils.isDirectlyEncryption(type))
        type = MimeTypeHelper.RAR_TYPE
        Assert.assertTrue(SafeUtils.isDirectlyEncryption(type))
        type = MimeTypeHelper.UNKNOWN_TYPE
        Assert.assertFalse(SafeUtils.isDirectlyEncryption(type))
    }

    @Test
    fun should_verify_notifySyncGalleryDB() {
        SafeUtils.notifySyncGalleryDB(null)

        var context = mockk<Context>(relaxed = true) {
            every { sendBroadcast(any()) }.returns(Unit)
        }

        mockkStatic(SdkUtils::class)
        every { SdkUtils.getSDKVersion() }.returns(29)
        SafeUtils.notifySyncGalleryDB(context)

        mockkStatic(Utils::class)
        every { Utils.isNeededSdk27() }.returns(true)
        every { SdkUtils.getSDKVersion() }.returns(27)
        SafeUtils.notifySyncGalleryDB(context)
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}