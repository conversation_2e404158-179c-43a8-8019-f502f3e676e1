/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.cardwidget.label
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.utils.PermissionUtils
import com.oplus.cardwidget.domain.action.CardWidgetAction
import com.oplus.filemanager.cardwidget.label.data.LabelCardCacheData
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import com.oplus.filemanager.room.model.FileLabelMappingEntity
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class LabelCardControllerTest {

    private lateinit var mContext: Context

    companion object {
        private const val WIDGET_CODE = "20221216"
    }

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        mContext = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(mContext)
        mockkObject(FileLabelDBHelper)
        mockkObject(FileLabelMappingDBHelper)
        mockkObject(SortHelper)
        mockkObject(PrivacyPolicyController.Companion)
        mockkStatic(SortModeUtils::class)
        mockkObject(CardWidgetAction)
        mockkObject(PermissionUtils)
    }

    @After
    fun tearDown() {
        unmockkObject(FileLabelDBHelper)
        unmockkObject(FileLabelMappingDBHelper)
        unmockkObject(SortHelper)
        unmockkStatic(SortModeUtils::class)
        unmockkObject(CardWidgetAction)
        unmockkObject(PermissionUtils)
        unmockkObject(PrivacyPolicyController.Companion)
    }

    @Test
    fun shouldNoAgreePrivacyWhenRefreshOnResumeIfNoAgreePrivacy() {
        val controller = spyk(LabelCardController())
        every { PrivacyPolicyController.hasAgreePrivacy(mContext) }.returns(false)
        justRun { controller.refreshCardNotAgreePrivacy(mContext, WIDGET_CODE) }

        controller.refreshOnResume(mContext, WIDGET_CODE)
        verify { controller.refreshCardNotAgreePrivacy(mContext, WIDGET_CODE) }
    }

    @Test
    fun `should no permission when refreshOnResume if no permission`() {
        val controller = spyk(LabelCardController())
        every { PrivacyPolicyController.hasAgreePrivacy(mContext) }.returns(true)
        every { PermissionUtils.hasStoragePermission(mContext) } returns false
        justRun { controller.refreshCardNoPermission(mContext, WIDGET_CODE) }

        controller.refreshOnResume(mContext, WIDGET_CODE)

        verify { controller.refreshCardNoPermission(mContext, WIDGET_CODE) }
    }

    @Test
    fun `should no labels when refreshOnResume if no labels`() {
        val controller = spyk(LabelCardController())
        val codeUtils = mockk<LabelCardWidgetCodeUtils>()
        every { controller.widgetCodeUtils } returns codeUtils
        every { codeUtils.getCacheByWidgetCode(mContext, WIDGET_CODE) } returns null
        every { PrivacyPolicyController.hasAgreePrivacy(mContext) }.returns(true)

        every { PermissionUtils.hasStoragePermission() } returns true
        justRun { controller.refreshCardNoLabels(WIDGET_CODE, mContext, false) }
        val cacheList = arrayListOf<LabelCardCacheData>()
        every { controller.widgetCodeUtils } returns codeUtils
        every { codeUtils.getCacheList() } returns cacheList
        val allLabels = arrayListOf<FileLabelEntity>()
        every { FileLabelDBHelper.getAllLabels() } returns allLabels
        every { FileLabelMappingDBHelper.getFileListByLabelId(any()) } returns arrayListOf()

        controller.refreshOnResume(mContext, WIDGET_CODE)

        verify { controller.refreshCardNoLabels(WIDGET_CODE, mContext, false) }
    }

    @Test
    fun shouldNoLabelsWhenRefreshOnResumeIfCardStateInit() {
        val controller = spyk(LabelCardController())
        val codeUtils = mockk<LabelCardWidgetCodeUtils>()
        every { controller.widgetCodeUtils } returns codeUtils
        every { codeUtils.getCacheByWidgetCode(mContext, WIDGET_CODE) } returns LabelCardCacheData(
            WIDGET_CODE,
            1L,
            LabelCardCacheData.STATUS_INIT
        )
        every { PrivacyPolicyController.hasAgreePrivacy(mContext) }.returns(true)

        every { PermissionUtils.hasStoragePermission() } returns true
        justRun { controller.refreshCardNoLabels(WIDGET_CODE, mContext, false) }
        val cacheList = arrayListOf<LabelCardCacheData>()
        every { controller.widgetCodeUtils } returns codeUtils
        every { codeUtils.getCacheList() } returns cacheList
        val allLabels = arrayListOf<FileLabelEntity>()
        every { FileLabelDBHelper.getAllLabels() } returns allLabels
        every { FileLabelMappingDBHelper.getFileListByLabelId(any()) } returns arrayListOf()

        controller.refreshOnResume(mContext, WIDGET_CODE)

        verify { controller.refreshCardNoLabels(WIDGET_CODE, mContext, false) }
    }

    @Test
    fun `should empty files when refreshOnResume if label no file`() {
        val controller = spyk(LabelCardController())
        val codeUtils = mockk<LabelCardWidgetCodeUtils>()
        every { PrivacyPolicyController.hasAgreePrivacy(mContext) }.returns(true)
        every { controller.widgetCodeUtils } returns codeUtils
        val labelId = 1L
        val cache = LabelCardCacheData(WIDGET_CODE, labelId, 1)
        every { codeUtils.getCacheByWidgetCode(mContext, WIDGET_CODE) } returns cache
        every { PermissionUtils.hasStoragePermission() } returns true
        every { controller.widgetCodeUtils } returns codeUtils
        val allLabels = arrayListOf<FileLabelEntity>()
        every { FileLabelDBHelper.getAllLabels() } returns allLabels
        val cacheList = arrayListOf<LabelCardCacheData>()
        cacheList.add(cache)
        every { codeUtils.getCacheList() } returns cacheList
        every { FileLabelDBHelper.getFileLabelById(labelId) } returns FileLabelEntity(labelId,"name", 0,0, 0, 0)
        every { FileLabelMappingDBHelper.getFileListByLabelId(any()) } returns arrayListOf()
        justRun { controller.refreshCardEmptyFiles(mContext, any(), WIDGET_CODE) }

        controller.refreshOnResume(mContext, WIDGET_CODE)
    }

    @Test
    fun `should files when refreshOnResume if label has file`() {
        val controller = spyk(LabelCardController())
        val codeUtils = mockk<LabelCardWidgetCodeUtils>()
        every { PrivacyPolicyController.hasAgreePrivacy(mContext) }.returns(true)
        every { controller.widgetCodeUtils } returns codeUtils
        val labelId = 1L
        val cache = LabelCardCacheData(WIDGET_CODE, labelId, 1)
        every { codeUtils.getCacheByWidgetCode(mContext, WIDGET_CODE) } returns cache
        every { PermissionUtils.hasStoragePermission() } returns true
        every { controller.widgetCodeUtils } returns codeUtils
        val allLabels = arrayListOf<FileLabelEntity>()
        every { FileLabelDBHelper.getAllLabels() } returns allLabels
        val cacheList = arrayListOf<LabelCardCacheData>()
        cacheList.add(cache)
        every { codeUtils.getCacheList() } returns cacheList
        every { FileLabelDBHelper.getFileLabelById(labelId) } returns FileLabelEntity(labelId,"name", 0,0, 0, 0)
        val mappingList = arrayListOf<FileLabelMappingEntity>()
        mappingList.add(FileLabelMappingEntity(labelId, labelId, "", 1, "", 0, 0))
        every { FileLabelMappingDBHelper.getFileListByLabelId(any()) } returns mappingList
        justRun { controller.refreshCardLabels(mContext, any(), WIDGET_CODE) }

        controller.refreshOnResume(mContext, WIDGET_CODE)
    }

    @Test
    fun `should execute save card when label id is null if useful list is 0`() {
        val controller = spyk(LabelCardController())
        val cacheList = arrayListOf<LabelCardCacheData>()
        val codeUtils = mockk<LabelCardWidgetCodeUtils>()
        every { controller.widgetCodeUtils } returns codeUtils
        every { codeUtils.getCacheList() } returns cacheList
        val allLabels = arrayListOf<FileLabelEntity>()
        every { FileLabelDBHelper.getAllLabels() } returns allLabels
        val mappingList = arrayListOf<FileLabelMappingEntity>()
        every { SortHelper.sortFiles(any(), any(), any(), any(), any()) }
        every { FileLabelMappingDBHelper.getFileListByLabelId(any()) } returns mappingList
        val cacheData = LabelCardCacheData(WIDGET_CODE, null, 0)
        justRun { codeUtils.saveCard(WIDGET_CODE, cacheData) }

        controller.addCard(WIDGET_CODE)

        verify { codeUtils.saveCard(WIDGET_CODE, cacheData) }
    }

    @Test
    fun `should execute save card when label id is not null if useful list is not 0`() {
        val controller = spyk(LabelCardController())
        val cacheList = arrayListOf<LabelCardCacheData>()
        val codeUtils = mockk<LabelCardWidgetCodeUtils>()
        every { controller.widgetCodeUtils } returns codeUtils
        every { codeUtils.getCacheList() } returns cacheList
        val cacheData = LabelCardCacheData(WIDGET_CODE, null, 0)
        justRun { codeUtils.saveCard(WIDGET_CODE, cacheData) }

        controller.addCard(WIDGET_CODE)

        verify { codeUtils.saveCard(WIDGET_CODE, cacheData) }
    }

    @Test
    fun `should remove card`() {
        val controller = spyk(LabelCardController())
        val codeUtils = mockk<LabelCardWidgetCodeUtils>()
        every { controller.widgetCodeUtils } returns codeUtils
        justRun { codeUtils.deleteCard(WIDGET_CODE) }

        controller.removeCard(WIDGET_CODE)

        verify { codeUtils.deleteCard(WIDGET_CODE) }
    }

    @Test
    fun `should return layout name`() {
        val controller = LabelCardController()
        assertEquals("no_permission_layout.json", controller.getCardLayoutName(""))
    }
}