/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IVideoFilePreview.kt
 * Description:
 *     The interface to play video to preview it.
 *
 * Version: 1.0
 * Date: 2024-09-19
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-09-19   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.video

import android.content.Context
import androidx.appcompat.widget.AppCompatTextView
import androidx.lifecycle.LifecycleOwner
import com.oplus.filemanager.preview.core.IPreviewContainerManager
import com.oplus.filemanager.preview.widget.PreviewVideoPlaySuite

internal interface IVideoFilePreview {

    /**
     * Attach to container views and play video.
     */
    fun attachToContainer(containerManager: IVideoPreviewContainerManager)

    /**
     * Release when not use it anymore.
     */
    fun release()

    companion object {

        @JvmStatic
        fun obtain(
            context: Context,
            lifecycleOwner: LifecycleOwner,
            previewModel: VideoPreviewViewModel
        ): IVideoFilePreview = VideoFilePreviewImpl(context, lifecycleOwner, previewModel)
    }
}

internal interface IVideoPreviewContainerManager : IPreviewContainerManager {
    val videoPlayerSuit: PreviewVideoPlaySuite
}