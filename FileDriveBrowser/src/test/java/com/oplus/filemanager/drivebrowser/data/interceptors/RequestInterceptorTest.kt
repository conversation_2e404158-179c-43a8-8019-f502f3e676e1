/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : RequestInterceptorTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/30 14:15
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/30       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.data.interceptors

import android.content.Context
import com.oplus.filemanager.drivebrowser.data.utils.BuildHeader
import com.oplus.filemanager.drivebrowser.data.utils.formatUrlMap
import com.oplus.filemanager.drivebrowser.data.utils.generateKey
import com.oplus.filemanager.drivebrowser.data.utils.hmacSignInfoWithGenerateKey
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import okhttp3.HttpUrl
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class RequestInterceptorTest {

    private lateinit var context: Context

    @Before
    fun setup() {
        context = mockk()
        every { context.packageName }.returns(PACKAGE_NAME)
    }

    @After
    fun teardown() {
        unmockkAll()
    }

    @Test
    fun testQueryString() {
        val interceptor = RequestInterceptor(context)
        val httpUrl = mockk<HttpUrl>()
        val queryParamNames = setOf<String>()
        every { httpUrl.queryParameterNames }.returns(queryParamNames)
        every { httpUrl.queryParameter(any()) }.returns(QUERY_PARAM_VALUE)
        mockkStatic("com.oplus.filemanager.drivebrowser.data.utils.SignUtilKt")
        every { formatUrlMap(any(), any(), any()) }.returns(QUERY_STRING)
        val queryString = interceptor.queryString(httpUrl)
        Assert.assertEquals(QUERY_STRING, queryString)
        unmockkStatic("com.oplus.filemanager.drivebrowser.data.utils.SignUtilKt")
    }

    @Test
    fun testCreateRequestHeader() {
        mockkObject(BuildHeader)
        val header = hashMapOf<String, String>()
        every { BuildHeader.generateHeader(context, any()) }.returns(header)
        mockkStatic("com.oplus.filemanager.drivebrowser.data.utils.SignUtilKt")
        every { generateKey(any(), any(), any()) }.returns(ByteArray(0))
        every { hmacSignInfoWithGenerateKey(any(), any()) }.returns(SIGN)
        val interceptor = RequestInterceptor(context)
        val headerResult = interceptor.createRequestHeader(context, "", "", "", "", "")
        Assert.assertNotNull(headerResult[BuildHeader.SIGN])
        Assert.assertEquals(SIGN, headerResult[BuildHeader.SIGN])
        unmockkStatic("com.oplus.filemanager.drivebrowser.data.utils.SignUtilKt")
        unmockkObject(BuildHeader)
    }

    companion object {
        private const val PACKAGE_NAME = "com.coloros.filemanager"
        private const val QUERY_PARAM_VALUE = "param"
        private const val QUERY_STRING = "query_string"
        private const val SIGN = "1C33BDAD40EE03C33F58A2291FB99B09B4FA53E3E127CB2B7B9547D0A1114F8A"
    }
}