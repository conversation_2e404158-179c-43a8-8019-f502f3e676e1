#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes for Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:110), pid=15252, tid=13772
#
# JRE version: Java(TM) SE Runtime Environment (17.0.8+9) (build 17.0.8+9-LTS-211)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\framework\RemoteDevice\build\20250901_8624138175653727837.compiler.options

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Sep  1 12:32:52 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 2.357885 seconds (0d 0h 0m 2s)

---------------  T H R E A D  ---------------

Current thread (0x0000021c3d3039d0):  JavaThread "main" [_thread_in_vm, id=13772, stack(0x0000009fcf500000,0x0000009fcf600000)]

Stack: [0x0000009fcf500000,0x0000009fcf600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x677d0a]
V  [jvm.dll+0x7d8c54]
V  [jvm.dll+0x7da3fe]
V  [jvm.dll+0x7daa63]
V  [jvm.dll+0x245c5f]
V  [jvm.dll+0x7d4d5b]
V  [jvm.dll+0x61dcf6]
V  [jvm.dll+0x61dd5a]
V  [jvm.dll+0x62057d]
V  [jvm.dll+0x61e6b6]
V  [jvm.dll+0x62f459]
V  [jvm.dll+0x628774]
V  [jvm.dll+0x37e0b1]
C  0x0000021c4c00c727

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.OutOfMemoryError.<init>()V+0 java.base@17.0.8
v  ~StubRoutines::call_stub
J 525  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (0 bytes) @ 0x0000021c4c773e63 [0x0000021c4c773da0+0x00000000000000c3]
J 619 c1 java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class; java.base@17.0.8 (43 bytes) @ 0x0000021c44cbc6dc [0x0000021c44cbc380+0x000000000000035c]
J 664 c1 java.security.SecureClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class; java.base@17.0.8 (16 bytes) @ 0x0000021c44cd498c [0x0000021c44cd48c0+0x00000000000000cc]
J 591 c1 jdk.internal.loader.BuiltinClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class; java.base@17.0.8 (121 bytes) @ 0x0000021c44cad2f4 [0x0000021c44cac0c0+0x0000000000001234]
J 485 c1 jdk.internal.loader.BuiltinClassLoader.findClassOnClassPathOrNull(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (64 bytes) @ 0x0000021c44c6f064 [0x0000021c44c6dfe0+0x0000000000001084]
J 226 c1 jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class; java.base@17.0.8 (143 bytes) @ 0x0000021c44bfb8d4 [0x0000021c44bfa9c0+0x0000000000000f14]
J 344 c1 jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class; java.base@17.0.8 (40 bytes) @ 0x0000021c44c2ce8c [0x0000021c44c2c860+0x000000000000062c]
J 343 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (7 bytes) @ 0x0000021c44c2b94c [0x0000021c44c2b840+0x000000000000010c]
v  ~StubRoutines::call_stub
j  org.jetbrains.kotlin.backend.jvm.JvmLowerKt.<clinit>()V+1256
v  ~StubRoutines::call_stub
j  org.jetbrains.kotlin.backend.jvm.JvmPhasesKt.getJvmPhases()Lorg/jetbrains/kotlin/backend/common/phaser/NamedCompilerPhase;+0
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(Lorg/jetbrains/kotlin/cli/common/arguments/K2JVMCompilerArguments;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/utils/KotlinPaths;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+63
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(Lorg/jetbrains/kotlin/cli/common/arguments/CommonCompilerArguments;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/utils/KotlinPaths;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+9
j  org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonCompilerArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+220
j  org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonToolArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+7
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonToolArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+76
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Ljava/io/PrintStream;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;[Ljava/lang/String;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+239
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Ljava/io/PrintStream;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;[Ljava/lang/String;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+25
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMainNoExit(Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+39
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMainNoExit$default(Lorg/jetbrains/kotlin/cli/common/CLITool$Companion;Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;ILjava/lang/Object;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+16
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMain(Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;)V+54
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler$Companion.main([Ljava/lang/String;)V+20
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.main([Ljava/lang/String;)V+4
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000021c6a668b60, length=15, elements={
0x0000021c3d3039d0, 0x0000021c69b8d650, 0x0000021c69b8e3d0, 0x0000021c69ba1c50,
0x0000021c69ba2510, 0x0000021c69ba2dd0, 0x0000021c69ba3690, 0x0000021c69ba4330,
0x0000021c69ba4ce0, 0x0000021c69baa1b0, 0x0000021c6a35b520, 0x0000021c6a367e70,
0x0000021c6a36dae0, 0x0000021c6a36dff0, 0x0000021c6a36e500
}

Java Threads: ( => current thread )
=>0x0000021c3d3039d0 JavaThread "main" [_thread_in_vm, id=13772, stack(0x0000009fcf500000,0x0000009fcf600000)]
  0x0000021c69b8d650 JavaThread "Reference Handler" daemon [_thread_blocked, id=26020, stack(0x0000009fcfc00000,0x0000009fcfd00000)]
  0x0000021c69b8e3d0 JavaThread "Finalizer" daemon [_thread_blocked, id=8348, stack(0x0000009fcfd00000,0x0000009fcfe00000)]
  0x0000021c69ba1c50 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=9904, stack(0x0000009fcfe00000,0x0000009fcff00000)]
  0x0000021c69ba2510 JavaThread "Attach Listener" daemon [_thread_blocked, id=22360, stack(0x0000009fcff00000,0x0000009fd0000000)]
  0x0000021c69ba2dd0 JavaThread "Service Thread" daemon [_thread_blocked, id=20004, stack(0x0000009fd0000000,0x0000009fd0100000)]
  0x0000021c69ba3690 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=16304, stack(0x0000009fd0100000,0x0000009fd0200000)]
  0x0000021c69ba4330 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=23676, stack(0x0000009fd0200000,0x0000009fd0300000)]
  0x0000021c69ba4ce0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=10140, stack(0x0000009fd0300000,0x0000009fd0400000)]
  0x0000021c69baa1b0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=23652, stack(0x0000009fd0400000,0x0000009fd0500000)]
  0x0000021c6a35b520 JavaThread "Notification Thread" daemon [_thread_blocked, id=23568, stack(0x0000009fd0500000,0x0000009fd0600000)]
  0x0000021c6a367e70 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=16172, stack(0x0000009fd0700000,0x0000009fd0800000)]
  0x0000021c6a36dae0 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=23592, stack(0x0000009fd0800000,0x0000009fd0900000)]
  0x0000021c6a36dff0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=21580, stack(0x0000009fd0900000,0x0000009fd0a00000)]
  0x0000021c6a36e500 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=332, stack(0x0000009fd0a00000,0x0000009fd0b00000)]

Other Threads:
  0x0000021c69b85f40 VMThread "VM Thread" [stack: 0x0000009fcfb00000,0x0000009fcfc00000] [id=20236]
  0x0000021c6a363a20 WatcherThread [stack: 0x0000009fd0600000,0x0000009fd0700000] [id=29244]
  0x0000021c3d3b1340 GCTaskThread "GC Thread#0" [stack: 0x0000009fcf600000,0x0000009fcf700000] [id=14956]
  0x0000021c3d3c2f60 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000009fcf700000,0x0000009fcf800000] [id=680]
  0x0000021c3d3c3880 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000009fcf800000,0x0000009fcf900000] [id=22332]
  0x0000021c69abe370 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000009fcf900000,0x0000009fcfa00000] [id=12376]
  0x0000021c69abeca0 ConcurrentGCThread "G1 Service" [stack: 0x0000009fcfa00000,0x0000009fcfb00000] [id=25944]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000021c3d2fd310] Metaspace_lock - owner thread: 0x0000021c3d3039d0

Heap address: 0x0000000604800000, size: 8120 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000021c6b000000-0x0000021c6bbd0000-0x0000021c6bbd0000), size 12386304, SharedBaseAddress: 0x0000021c6b000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000021c6c000000-0x0000021cac000000, reserved size: 1073741824
Narrow klass base: 0x0000021c6b000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32479M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8120M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 520192K, used 16384K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 0 survivors (0K)
 Metaspace       used 5454K, committed 5504K, reserved 1114112K
  class space    used 533K, committed 576K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%|HS|  |TAMS 0x0000000604800000, 0x0000000604800000| Complete 
|   1|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   2|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|   3|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|   4|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|   5|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|   6|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|   7|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|   8|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|   9|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  10|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  11|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  12|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  13|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
|  14|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  15|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  16|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  17|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  18|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  19|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  20|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  21|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Untracked 
|  22|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|  23|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000, 0x000000060a400000| Untracked 
|  24|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000, 0x000000060a800000| Untracked 
|  25|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000, 0x000000060ac00000| Untracked 
|  26|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000, 0x000000060b000000| Untracked 
|  27|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000, 0x000000060b400000| Untracked 
|  28|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000, 0x000000060b800000| Untracked 
|  29|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000, 0x000000060bc00000| Untracked 
|  30|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|  31|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000, 0x000000060c400000| Untracked 
|  32|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000, 0x000000060c800000| Untracked 
|  33|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000, 0x000000060cc00000| Untracked 
|  34|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000, 0x000000060d000000| Untracked 
|  35|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000, 0x000000060d400000| Untracked 
|  36|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000, 0x000000060d800000| Untracked 
|  37|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000, 0x000000060dc00000| Untracked 
|  38|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|  39|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000, 0x000000060e400000| Untracked 
|  40|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000, 0x000000060e800000| Untracked 
|  41|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000, 0x000000060ec00000| Untracked 
|  42|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000, 0x000000060f000000| Untracked 
|  43|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000, 0x000000060f400000| Untracked 
|  44|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000, 0x000000060f800000| Untracked 
|  45|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000, 0x000000060fc00000| Untracked 
|  46|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|  47|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000, 0x0000000610400000| Untracked 
|  48|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000, 0x0000000610800000| Untracked 
|  49|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000, 0x0000000610c00000| Untracked 
|  50|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000, 0x0000000611000000| Untracked 
|  51|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000, 0x0000000611400000| Untracked 
|  52|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000, 0x0000000611800000| Untracked 
|  53|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000, 0x0000000611c00000| Untracked 
|  54|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
|  55|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000, 0x0000000612400000| Untracked 
|  56|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000, 0x0000000612800000| Untracked 
|  57|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000, 0x0000000612c00000| Untracked 
|  58|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000, 0x0000000613000000| Untracked 
|  59|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000, 0x0000000613400000| Untracked 
|  60|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000, 0x0000000613800000| Untracked 
|  61|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000, 0x0000000613c00000| Untracked 
|  62|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
|  63|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000, 0x0000000614400000| Untracked 
|  64|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000, 0x0000000614800000| Untracked 
|  65|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000, 0x0000000614c00000| Untracked 
|  66|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000, 0x0000000615000000| Untracked 
|  67|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000, 0x0000000615400000| Untracked 
|  68|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000, 0x0000000615800000| Untracked 
|  69|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000, 0x0000000615c00000| Untracked 
|  70|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
|  71|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000, 0x0000000616400000| Untracked 
|  72|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000, 0x0000000616800000| Untracked 
|  73|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000, 0x0000000616c00000| Untracked 
|  74|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000, 0x0000000617000000| Untracked 
|  75|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000, 0x0000000617400000| Untracked 
|  76|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000, 0x0000000617800000| Untracked 
|  77|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000, 0x0000000617c00000| Untracked 
|  78|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
|  79|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000, 0x0000000618400000| Untracked 
|  80|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000, 0x0000000618800000| Untracked 
|  81|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000, 0x0000000618c00000| Untracked 
|  82|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000, 0x0000000619000000| Untracked 
|  83|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000, 0x0000000619400000| Untracked 
|  84|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000, 0x0000000619800000| Untracked 
|  85|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000, 0x0000000619c00000| Untracked 
|  86|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000, 0x000000061a000000| Untracked 
|  87|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000, 0x000000061a400000| Untracked 
|  88|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000, 0x000000061a800000| Untracked 
|  89|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000, 0x000000061ac00000| Untracked 
|  90|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000, 0x000000061b000000| Untracked 
|  91|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000, 0x000000061b400000| Untracked 
|  92|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000, 0x000000061b800000| Untracked 
|  93|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000, 0x000000061bc00000| Untracked 
|  94|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000, 0x000000061c000000| Untracked 
|  95|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000, 0x000000061c400000| Untracked 
|  96|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000, 0x000000061c800000| Untracked 
|  97|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000, 0x000000061cc00000| Untracked 
|  98|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000, 0x000000061d000000| Untracked 
|  99|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000, 0x000000061d400000| Untracked 
| 100|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000, 0x000000061d800000| Untracked 
| 101|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000, 0x000000061dc00000| Untracked 
| 102|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000, 0x000000061e000000| Untracked 
| 103|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000, 0x000000061e400000| Untracked 
| 104|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000, 0x000000061e800000| Untracked 
| 105|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000, 0x000000061ec00000| Untracked 
| 106|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000, 0x000000061f000000| Untracked 
| 107|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000, 0x000000061f400000| Untracked 
| 108|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000, 0x000000061f800000| Untracked 
| 109|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000, 0x000000061fc00000| Untracked 
| 110|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000, 0x0000000620000000| Untracked 
| 111|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000, 0x0000000620400000| Untracked 
| 112|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000, 0x0000000620800000| Untracked 
| 113|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000, 0x0000000620c00000| Untracked 
| 114|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 115|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000, 0x0000000621400000| Untracked 
| 116|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000, 0x0000000621800000| Untracked 
| 117|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000, 0x0000000621c00000| Untracked 
| 118|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000, 0x0000000622000000| Untracked 
| 119|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000, 0x0000000622400000| Untracked 
| 120|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000, 0x0000000622800000| Untracked 
| 121|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000, 0x0000000622c00000| Untracked 
| 122|0x0000000623000000, 0x00000006233631d0, 0x0000000623400000| 84%| E|  |TAMS 0x0000000623000000, 0x0000000623000000| Complete 
| 123|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000, 0x0000000623400000| Complete 
| 124|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000, 0x0000000623800000| Complete 
| 125|0x0000000623c00000, 0x0000000624000000, 0x0000000624000000|100%| E|  |TAMS 0x0000000623c00000, 0x0000000623c00000| Complete 
| 126|0x0000000624000000, 0x0000000624400000, 0x0000000624400000|100%| E|CS|TAMS 0x0000000624000000, 0x0000000624000000| Complete 

Card table byte_map: [0x0000021c55430000,0x0000021c56410000] _byte_map_base: 0x0000021c5240c000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000021c3d3b2870, (CMBitMap*) 0x0000021c3d3b28b0
 Prev Bits: [0x0000021c573f0000, 0x0000021c5f2d0000)
 Next Bits: [0x0000021c5f2d0000, 0x0000021c671b0000)

Polling page: 0x0000021c3b2a0000

Metaspace:

Usage:
  Non-class:      4.81 MB used.
      Class:    533.88 KB used.
       Both:      5.33 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       4.81 MB (  8%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     576.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       5.38 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  8.38 MB
       Class:  15.31 MB
        Both:  23.69 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 14.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 86.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 176.
num_chunk_merges: 0.
num_chunk_splits: 137.
num_chunks_enlarged: 126.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=182Kb max_used=182Kb free=118985Kb
 bounds [0x0000021c4c750000, 0x0000021c4c9c0000, 0x0000021c53bb0000]
CodeHeap 'profiled nmethods': size=119104Kb used=1189Kb max_used=1189Kb free=117914Kb
 bounds [0x0000021c44bb0000, 0x0000021c44e20000, 0x0000021c4c000000]
CodeHeap 'non-nmethods': size=7488Kb used=2835Kb max_used=2849Kb free=4653Kb
 bounds [0x0000021c4c000000, 0x0000021c4c2d0000, 0x0000021c4c750000]
 total_blobs=1112 nmethods=675 adapters=347
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.580 Thread 0x0000021c6a36e500 nmethod 665 0x0000021c44cd5610 code [0x0000021c44cd5800, 0x0000021c44cd5d88]
Event: 1.580 Thread 0x0000021c69ba4ce0 nmethod 666 0x0000021c44cd5f90 code [0x0000021c44cd6180, 0x0000021c44cd65a8]
Event: 1.588 Thread 0x0000021c6a36dae0  667       3       java.util.HashSet::add (20 bytes)
Event: 1.588 Thread 0x0000021c6a36dae0 nmethod 667 0x0000021c44cd6790 code [0x0000021c44cd6940, 0x0000021c44cd6b88]
Event: 1.627 Thread 0x0000021c69ba4330  668 %     4       java.lang.StringCoding::hasNegatives @ 2 (25 bytes)
Event: 1.631 Thread 0x0000021c69ba4330 nmethod 668% 0x0000021c4c77cf10 code [0x0000021c4c77d0a0, 0x0000021c4c77d338]
Event: 1.637 Thread 0x0000021c6a36dff0  669       3       java.util.AbstractSet::<init> (5 bytes)
Event: 1.637 Thread 0x0000021c6a36dff0 nmethod 669 0x0000021c44cd6c90 code [0x0000021c44cd6e40, 0x0000021c44cd6ff8]
Event: 1.688 Thread 0x0000021c6a36e500  670       3       java.util.HashMap::tableSizeFor (32 bytes)
Event: 1.688 Thread 0x0000021c6a36e500 nmethod 670 0x0000021c44cd7110 code [0x0000021c44cd72c0, 0x0000021c44cd74e8]
Event: 1.713 Thread 0x0000021c6a36dae0  671       3       java.util.concurrent.ConcurrentHashMap$Node::<init> (14 bytes)
Event: 1.713 Thread 0x0000021c6a36dae0 nmethod 671 0x0000021c44cd7590 code [0x0000021c44cd7740, 0x0000021c44cd7a38]
Event: 1.722 Thread 0x0000021c69ba4330  672       4       java.util.concurrent.ConcurrentHashMap::setTabAt (20 bytes)
Event: 1.723 Thread 0x0000021c69ba4330 nmethod 672 0x0000021c4c77d490 code [0x0000021c4c77d600, 0x0000021c4c77d758]
Event: 1.725 Thread 0x0000021c6a36dff0  673       3       kotlin.collections.SetsKt__SetsKt::emptySet (7 bytes)
Event: 1.725 Thread 0x0000021c6a36dff0 nmethod 673 0x0000021c44cd7b10 code [0x0000021c44cd7ca0, 0x0000021c44cd7d98]
Event: 1.737 Thread 0x0000021c6a36dff0  674       3       java.lang.String::newStringUTF8NoRepl (276 bytes)
Event: 1.738 Thread 0x0000021c69ba4330  675       4       kotlin.jvm.internal.Intrinsics::checkNotNullParameter (9 bytes)
Event: 1.738 Thread 0x0000021c69ba4330 nmethod 675 0x0000021c4c77d810 code [0x0000021c4c77d980, 0x0000021c4c77d9f8]
Event: 1.738 Thread 0x0000021c6a36dff0 nmethod 674 0x0000021c44cd7e10 code [0x0000021c44cd80c0, 0x0000021c44cd90a8]

GC Heap History (0 events):
No events

Deoptimization events (20 events):
Event: 0.630 Thread 0x0000021c3d3039d0 DEOPT PACKING pc=0x0000021c44bc238c sp=0x0000009fcf5fdb50
Event: 0.630 Thread 0x0000021c3d3039d0 DEOPT UNPACKING pc=0x0000021c4c052b43 sp=0x0000009fcf5fcfe0 mode 0
Event: 0.630 Thread 0x0000021c3d3039d0 DEOPT PACKING pc=0x0000021c44bc238c sp=0x0000009fcf5fdb50
Event: 0.630 Thread 0x0000021c3d3039d0 DEOPT UNPACKING pc=0x0000021c4c052b43 sp=0x0000009fcf5fcfe0 mode 0
Event: 0.630 Thread 0x0000021c3d3039d0 DEOPT PACKING pc=0x0000021c44bc238c sp=0x0000009fcf5fdb50
Event: 0.630 Thread 0x0000021c3d3039d0 DEOPT UNPACKING pc=0x0000021c4c052b43 sp=0x0000009fcf5fcfe0 mode 0
Event: 0.630 Thread 0x0000021c3d3039d0 DEOPT PACKING pc=0x0000021c44bc238c sp=0x0000009fcf5fdb50
Event: 0.630 Thread 0x0000021c3d3039d0 DEOPT UNPACKING pc=0x0000021c4c052b43 sp=0x0000009fcf5fcfe0 mode 0
Event: 0.631 Thread 0x0000021c3d3039d0 DEOPT PACKING pc=0x0000021c44bc238c sp=0x0000009fcf5fdb50
Event: 0.631 Thread 0x0000021c3d3039d0 DEOPT UNPACKING pc=0x0000021c4c052b43 sp=0x0000009fcf5fcfe0 mode 0
Event: 0.687 Thread 0x0000021c3d3039d0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000021c4c754d28 relative=0x0000000000000748
Event: 0.687 Thread 0x0000021c3d3039d0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000021c4c754d28 method=java.util.zip.ZipFile$Source.checkAndAddEntry(II)I @ 50 c2
Event: 0.687 Thread 0x0000021c3d3039d0 DEOPT PACKING pc=0x0000021c4c754d28 sp=0x0000009fcf5fe6f0
Event: 0.687 Thread 0x0000021c3d3039d0 DEOPT UNPACKING pc=0x0000021c4c0523a3 sp=0x0000009fcf5fe660 mode 2
Event: 0.698 Thread 0x0000021c3d3039d0 DEOPT PACKING pc=0x0000021c44bb0c02 sp=0x0000009fcf5fe220
Event: 0.698 Thread 0x0000021c3d3039d0 DEOPT UNPACKING pc=0x0000021c4c052b43 sp=0x0000009fcf5fd698 mode 0
Event: 0.790 Thread 0x0000021c3d3039d0 DEOPT PACKING pc=0x0000021c44bc9ee7 sp=0x0000009fcf5fccb0
Event: 0.790 Thread 0x0000021c3d3039d0 DEOPT UNPACKING pc=0x0000021c4c052b43 sp=0x0000009fcf5fc0d0 mode 0
Event: 1.737 Thread 0x0000021c3d3039d0 DEOPT PACKING pc=0x0000021c44c7143b sp=0x0000009fcf5fc5b0
Event: 1.737 Thread 0x0000021c3d3039d0 DEOPT UNPACKING pc=0x0000021c4c052b43 sp=0x0000009fcf5fba10 mode 0

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (2 events):
Event: 1.519 Thread 0x0000021c3d3039d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006230190b0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006230190b0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.544 Thread 0x0000021c3d3039d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006230970a8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x00000006230970a8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (8 events):
Event: 0.653 Executing VM operation: HandshakeAllThreads
Event: 0.653 Executing VM operation: HandshakeAllThreads done
Event: 1.358 Executing VM operation: HandshakeAllThreads
Event: 1.358 Executing VM operation: HandshakeAllThreads done
Event: 1.358 Executing VM operation: Cleanup
Event: 1.366 Executing VM operation: Cleanup done
Event: 1.531 Executing VM operation: HandshakeAllThreads
Event: 1.531 Executing VM operation: HandshakeAllThreads done

Events (20 events):
Event: 1.510 loading class java/util/stream/MatchOps$MatchOp
Event: 1.510 loading class java/util/stream/MatchOps$MatchOp done
Event: 1.510 loading class java/util/stream/MatchOps$BooleanTerminalSink
Event: 1.510 loading class java/util/stream/MatchOps$BooleanTerminalSink done
Event: 1.510 loading class java/util/stream/MatchOps$1MatchSink
Event: 1.510 loading class java/util/stream/MatchOps$1MatchSink done
Event: 1.519 loading class java/lang/invoke/DirectMethodHandle$Special
Event: 1.520 loading class java/lang/invoke/DirectMethodHandle$Special done
Event: 1.546 loading class java/lang/ThreadLocal$SuppliedThreadLocal
Event: 1.546 loading class java/lang/ThreadLocal$SuppliedThreadLocal done
Event: 1.689 loading class java/lang/NoSuchFieldError
Event: 1.689 loading class java/lang/NoSuchFieldError done
Event: 1.689 loading class java/util/concurrent/atomic/AtomicReferenceFieldUpdater
Event: 1.689 loading class java/util/concurrent/atomic/AtomicReferenceFieldUpdater done
Event: 1.689 loading class java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl
Event: 1.689 loading class java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl done
Event: 1.689 loading class java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl$1
Event: 1.689 loading class java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl$1 done
Event: 1.690 loading class sun/reflect/misc/ReflectUtil
Event: 1.690 loading class sun/reflect/misc/ReflectUtil done


Dynamic libraries:
0x00007ff708300000 - 0x00007ff708310000 	E:\JAVA\bin\java.exe
0x00007ff827030000 - 0x00007ff827228000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8255d0000 - 0x00007ff825692000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff824d50000 - 0x00007ff825046000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8246c0000 - 0x00007ff8247c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff81fde0000 - 0x00007ff81fdf9000 	E:\JAVA\bin\jli.dll
0x00007ff826e80000 - 0x00007ff826f31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff826340000 - 0x00007ff8263de000 	C:\Windows\System32\msvcrt.dll
0x00007ff825e80000 - 0x00007ff825f1f000 	C:\Windows\System32\sechost.dll
0x00007ff825b10000 - 0x00007ff825c33000 	C:\Windows\System32\RPCRT4.dll
0x00007ff824a20000 - 0x00007ff824a47000 	C:\Windows\System32\bcrypt.dll
0x00007ff825050000 - 0x00007ff8251ed000 	C:\Windows\System32\USER32.dll
0x00007ff8248a0000 - 0x00007ff8248c2000 	C:\Windows\System32\win32u.dll
0x00007ff825da0000 - 0x00007ff825dcb000 	C:\Windows\System32\GDI32.dll
0x00007ff824a50000 - 0x00007ff824b69000 	C:\Windows\System32\gdi32full.dll
0x00007ff824980000 - 0x00007ff824a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff81fdc0000 - 0x00007ff81fddb000 	E:\JAVA\bin\VCRUNTIME140.dll
0x00007ff81dfe0000 - 0x00007ff81e27a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff823400000 - 0x00007ff82340a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8253f0000 - 0x00007ff82541f000 	C:\Windows\System32\IMM32.DLL
0x00007ff81fd50000 - 0x00007ff81fd5c000 	E:\JAVA\bin\vcruntime140_1.dll
0x00007ffffb0c0000 - 0x00007ffffb14e000 	E:\JAVA\bin\msvcp140.dll
0x00007fffe0350000 - 0x00007fffe0f2e000 	E:\JAVA\bin\server\jvm.dll
0x00007ff826f40000 - 0x00007ff826f48000 	C:\Windows\System32\PSAPI.DLL
0x00007fffeb340000 - 0x00007fffeb349000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff826e10000 - 0x00007ff826e7b000 	C:\Windows\System32\WS2_32.dll
0x00007ff81e3c0000 - 0x00007ff81e3e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff823360000 - 0x00007ff823372000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff81c4f0000 - 0x00007ff81c4fa000 	E:\JAVA\bin\jimage.dll
0x00007ff821e10000 - 0x00007ff822011000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff80fef0000 - 0x00007ff80ff24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff824810000 - 0x00007ff824892000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff818d20000 - 0x00007ff818d45000 	E:\JAVA\bin\java.dll
0x00007fffef390000 - 0x00007fffef467000 	E:\JAVA\bin\jsvml.dll
0x00007ff826570000 - 0x00007ff826cde000 	C:\Windows\System32\SHELL32.dll
0x00007ff8222d0000 - 0x00007ff822a74000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff825fd0000 - 0x00007ff826323000 	C:\Windows\System32\combase.dll
0x00007ff8240f0000 - 0x00007ff82411b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff826d40000 - 0x00007ff826e0d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff825f20000 - 0x00007ff825fcd000 	C:\Windows\System32\SHCORE.dll
0x00007ff825d40000 - 0x00007ff825d9b000 	C:\Windows\System32\shlwapi.dll
0x00007ff8245f0000 - 0x00007ff824614000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff811d70000 - 0x00007ff811d89000 	E:\JAVA\bin\net.dll
0x00007ff81ede0000 - 0x00007ff81eeea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff823e50000 - 0x00007ff823eba000 	C:\Windows\system32\mswsock.dll
0x00007ff810fb0000 - 0x00007ff810fc6000 	E:\JAVA\bin\nio.dll
0x00007ff81bc20000 - 0x00007ff81bc38000 	E:\JAVA\bin\zip.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\JAVA\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;E:\JAVA\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\framework\RemoteDevice\build\20250901_8624138175653727837.compiler.options
java_class_path (initial): E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.22\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\kotlin-compiler-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.22\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\kotlin-script-runtime-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.22\20e2c5df715f3240c765cfc222530e2796542021\kotlin-daemon-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8514437120                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8514437120                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\JAVA
CLASSPATH=.;E:\JAVA\lib\dt.jar;E:\JAVA\lib\tools.jar;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\dotnet\;E:\git\Git\cmd;D:\Users\W9096060\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\dotnet\;E:\1.8java\lib\dt.jar;E:\1.8java\lib\tools.jar;E:\1.8java\bin;E:\JAVA\bin;E:\JAVA\lib;D:\Users\W9096060\AppData\Local\Microsoft\WindowsApps;
USERNAME=W9096060
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 19:56 hours

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 32479M (2633M free)
TotalPageFile size 47840M (AvailPageFile size 285M)
current process WorkingSet (physical memory assigned to process): 67M, peak: 67M
current process commit charge ("private bytes"): 626M, peak: 626M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211) for windows-amd64 JRE (17.0.8+9-LTS-211), built on Jun 14 2023 10:34:31 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
