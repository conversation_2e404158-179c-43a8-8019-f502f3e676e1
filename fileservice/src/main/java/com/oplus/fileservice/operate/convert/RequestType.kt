/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RequestType
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/10 14:43
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/9/10       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.convert

import android.os.Bundle

enum class RequestType {
    OPERATE,
    LOAD,
    DO_NOTHING
}

private const val OPERATE_KEY = "operateInfo"

fun converterRequestByType(request: Bundle, requestType: Int): Request? {
    if (requestType == RequestType.OPERATE.ordinal) {
        return convertOperateInfoByRequest(request.get(OPERATE_KEY))
    } else if (requestType == RequestType.LOAD.ordinal) {
        return null
    }
    return null
}

fun converterOperateRequest(request: Bundle): OperateRequest {
    return convertOperateInfoByRequest(request.get(OPERATE_KEY))
}