/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - FileStore.java
 * Description: abstract class for File Store
 * Version: 1.0
 * Date : 2020/02/24
 * Author: Jiafei.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/02/24    1.0     create
 ****************************************************************/

package com.filemanager.recyclebin.retriever

import android.content.ContentValues
import android.content.Context
import android.net.Uri
import android.text.TextUtils
import com.filemanager.common.RecycleStore
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTraceUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.recyclebin.utils.RecycleBinFileHelper
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_ERROR
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_NOT_ENOUGH_SPACE
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_PROTECTED_DIR
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_SUCCESS
import java.io.File
import java.io.IOException
import java.nio.file.Files
import java.nio.file.StandardCopyOption
import java.util.*
import java.util.regex.Pattern

abstract class DataRetriever {

    protected abstract fun extractContentValues(uri: Uri): ContentValues?

    protected abstract fun extractContentValues(path: String): ContentValues?

    abstract fun isInternalPath(path: String?): Boolean

    abstract fun isInternalUri(uri: Uri?): Boolean

    abstract fun moveToRecycleBin(originPath: String, recyclePath: String): Int

    abstract fun moveToRecycleBin(originUri: Uri, recyclePath: String): Int

    abstract fun restoreToOriginPath(values: ContentValues): Int

    abstract fun deleteQuietly(originPath: String): Boolean

    abstract fun deleteQuietly(originUri: Uri): Boolean

    fun deleteRecycle(recyclePath: String?): Boolean {
        if (recyclePath.isNullOrEmpty()) {
            return false
        }

        return delete(recyclePath)
    }

    fun ensureRecycleDirectory(): Boolean {
        val recyclerRootPath = RecycleStore.getRecycleDirectory() ?: return false
        try {
            val recyclerRootFile = File(recyclerRootPath)
            return recyclerRootFile.exists()
        } catch (ex: Exception) {
            Log.w(TAG, "ensureRecycleDirectory error: $ex")
        }
        return false
    }

    fun extractRequiredColumns(uri: Uri?): ContentValues? {
        if (uri == null) {
            Log.w(TAG, "extractRequiredColumns Uri is null")
            return null
        }
        return extractContentValues(uri)
    }

    fun extractRequiredColumns(path: String): ContentValues? {
        if (TextUtils.isEmpty(path)) {
            Log.w(TAG, "extractRequiredColumns path is null")
            return null
        }
        return extractContentValues(path)
    }

    companion object {
        private const val TAG = "DataRetriever"

        // add for file access
        private const val mIsUseSAF = false

        /**
         * Regex that matches Android/obb or Android/data path to distinguish different mount points
         * NOTEl:Regex that matches file and child file in Android/obb or Android/data directory
         */
        private val PATTERN_RESTRICTED_DIR by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            Pattern.compile("(?i)^/storage/[^/]+/(?:[0-9]+/)?Android/(?:data|obb)($|/)")
        }

        /**
         * RESTRICTED system's Android Directory
         */
        private val RESTRICTED_ANDROID_DIR by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            Pattern.compile("(?i)^/storage/[^/]+/(?:[0-9]+/)?Android$")
        }

        val INSTANCE: DataRetriever by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            if (mIsUseSAF) {
                ModernDataRetriever()
            } else {
                LegacyDataRetriever()
            }
        }

        fun exists(file: File): Boolean {
            return file.exists()
        }

        fun isDirectory(file: File): Boolean {
            return file.isDirectory
        }

        fun delete(file: File): Boolean {
            return if (mIsUseSAF) {
                false
            } else {
                val result = RecycleBinFileHelper.delete(file)
                if (result) {
                    FileTraceUtil.getInstance().traceAction(FileTraceUtil.TraceAction.MARK_RECYCLE_DELETE,
                            MimeTypeHelper.getTypeFromPath(file.absolutePath), file.absolutePath)
                }
                return result
            }
        }

        fun delete(path: String) = delete(File(path))

        fun getParentFile(file: File): File? {
            return if (mIsUseSAF) {
                null
            } else {
                file.parentFile
            }
        }

        fun makeDir(dir: File): Boolean {
            return if (mIsUseSAF) {
                false
            } else {
                dir.mkdirs()
            }
        }

        /**
         * inner interface to move file for recyclerbin
         * @param srcFile souce File to move
         * @param dstFile destination File to move to
         * @return return true if have enough space for restoring file
         */
        fun innerRecyclerRenameTo(src: File, dst: File, isRestore: Boolean): Int {
            if (mIsUseSAF) {
                Log.w(TAG, "renameTo UseSAF failed, not implement")
                return STATUS_ERROR
            } else {
                val statusCode = if (SdkUtils.isAtLeastR()) {
                    innerRecycleRenameToCompatR(src, dst, isRestore)
                } else {
                    innerRecycleRenameToCompatQ(src, dst, isRestore)
                }
                Log.d(TAG, "innerRecyclerRenameTo result: $statusCode")
                return statusCode
            }
        }

        private fun innerRecycleRenameToCompatQ(src: File, dst: File, isRestore: Boolean): Int {
            when {
                (!isRestore && (isAndroidDirectory(src))) -> {
                    // Note: except that restore file in old recycle directory,
                    // copy or move restricted android directory always forbind on Android Q.
                    Log.d(TAG, "innerRecycleRenameToCompatQ MOVE the restricted android directory is forbind")
                    return STATUS_PROTECTED_DIR
                }
                (!isRestore && (isRestrictedDirectory(src))) -> {
                    // Note: except that restore file in old recycle directory,
                    // copy or move restricted directory always forbind on Android Q.
                    Log.d(TAG, "innerRecycleRenameToCompatQ MOVE the restricted directory is forbind")
                    return STATUS_PROTECTED_DIR
                }
            }
            val destParent = dst.parentFile
            if (!destParent.exists()) {
                Log.d(TAG, "innerRecycleRenameToCompatQ not exist")
                destParent.mkdirs()
            }

            return if (src.renameTo(dst)) {
                STATUS_SUCCESS
            } else {
                STATUS_ERROR
            }
        }

        private fun innerRecycleRenameToCompatR(src: File, dst: File, isRestore: Boolean): Int {
            when {
                // return right now, if dst path is Restricted directory.
                // NOTE: src is Restricted directory only if file in old recycler directory on Android
                isRestrictedDirectory(dst) -> {
                    // Note: copy or move restricted directory is forbind on Android R.
                    Log.d(TAG, "innerRecycleRenameToCompatR MOVE to restricted directory is forbind")
                    return STATUS_PROTECTED_DIR
                }
                (!isRestore && (isAndroidDirectory(src))) -> {
                    // Note: except that restore file in old recycle directory,
                    // copy or move restricted android directory always forbind on Android R.
                    Log.d(TAG, "innerRecycleRenameToCompatR MOVE the restricted android directory is forbind")
                    return STATUS_PROTECTED_DIR
                }
                (!isRestore && (isRestrictedDirectory(src))) -> {
                    // Note: except that restore file in old recycle directory,
                    // copy or move restricted directory always forbind on Android R.
                    Log.d(TAG, "innerRecycleRenameToCompatR MOVE the restricted directory is forbind")
                    return STATUS_PROTECTED_DIR
                }
            }

            val destParent = dst.parentFile
            if (isDiffMountPoint(src, dst)) {
                try {
                    // make parent directory if dst directory is not Restricted
                    if (!destParent.exists()) {
                        Log.d(TAG, "innerRecycleRenameToCompatR not exist")
                        destParent.mkdirs()
                    }

                    if (recursiveCopyFile(src, dst, destParent)) {
                        recoveryFileLastModified(src, dst)
                        val result = recursiveDeleteFile(src)
                        Log.d(TAG, "innerRecycleRenameToCompatR: copy file success, delete src file $result")
                        // Even if delete source file fails, it still should be considered recycle successful,
                        // Otherwise the user will think the data is lost
                        return STATUS_SUCCESS
                    } else {
                        val result = recursiveDeleteFile(dst)
                        Log.d(TAG, "innerRecycleRenameToCompatR: copy file failed, delete dest file $result")
                        return STATUS_ERROR
                    }
                } catch (ex: Exception) {
                    Log.e(TAG, "innerRecycleRenameToCompatR use copy file failed: ${ex.message}")
                    // check lowspace error
                    val lowSpaceMessage = ex.message?.contains("No space left") ?: false
                    return if (lowSpaceMessage) {
                        STATUS_NOT_ENOUGH_SPACE
                    } else {
                        STATUS_ERROR
                    }
                }
            } else {
                // make parent directory first if have the same mount point
                if (!destParent.exists()) {
                    Log.d(TAG, "innerRecycleRenameToCompatR not exist")
                    destParent.mkdirs()
                }

                return if (src.renameTo(dst)) {
                    STATUS_SUCCESS
                } else {
                    STATUS_ERROR
                }
            }
        }

        /**
         * check mount status on Android R
         * @param srcFile souce File to move
         * @param dstFile destination File to move to
         * @return return true if srcFile and dstFile have the same mount point, or return false
         */
        private fun isDiffMountPoint(srcFile: File, dstFile: File): Boolean {
            // Only support with the support same volume
            if (!SdkUtils.isAtLeastR()) {
                return false
            }
            val findSrc = PATTERN_RESTRICTED_DIR.matcher(srcFile.absolutePath).find()
            val findDst = PATTERN_RESTRICTED_DIR.matcher(dstFile.absolutePath).find()
            return !((findSrc && findDst) || (!findSrc && !findDst))
        }

        /**
         * check Restricted status on Android R and Android Q
         * @param targerPath target path to check
         * @return return true if targetPath is Restricted, or return false
         */
        fun isRestrictedDirectoryR(targerPath: String): Boolean {
            if (!SdkUtils.isAtLeastR()) {
                return false
            }

            try {
                return PATTERN_RESTRICTED_DIR.matcher(targerPath).find()
            } catch (ex: Exception) {
                Log.w(TAG, "isRestrictedDirectory error: $ex")
            }
            return false
        }

        /**
         * check Restricted status on Android R and Android Q
         * @param targetFile target file to check
         * @return return true if targetfile is Restricted, or return false
         */
        private fun isRestrictedDirectory(targetFile: File): Boolean {
            try {
                return PATTERN_RESTRICTED_DIR.matcher(targetFile.absolutePath).find()
            } catch (ex: Exception) {
                Log.w(TAG, "isRestrictedDirectory error: $ex")
            }
            return false
        }

        fun renameTo(src: File, dst: File): Boolean {
            if (mIsUseSAF) {
                Log.w(TAG, "renameTo UseSAF failed, not implement")
                return false
            } else {
                val destParent = dst.parentFile
                if (!destParent.exists()) {
                    Log.d(TAG, "renameTo not exist")
                    destParent.mkdirs()
                }
                var isRenameToSucc = when {
                    SdkUtils.isAtLeastR() -> {
                        if (src.isDirectory) {
                            // In R, renameTo not work for non-empty directory
                            Log.d(TAG, "renameTo(): use copy file to move file")
                            try {
                                if (recursiveCopyFile(src, dst, destParent)) {
                                    Log.d(TAG, "renameTo(): directory, recoveryFileLastModified")
                                    recoveryFileLastModified(src, dst)
                                    val result = recursiveDeleteFile(src)
                                    Log.d(TAG, "renameTo(): copy file success, delete src file $result")
                                    // Even if delete source file fails, it still should be considered recycle successful,
                                    // Otherwise the user will think the data is lost
                                    true
                                } else {
                                    val result = recursiveDeleteFile(dst)
                                    Log.d(TAG, "renameTo(): copy file failed, delete dest file $result")
                                    false
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "renameTo use copy file failed: ${e.message}")
                                false
                            }
                        } else {
                            src.renameTo(dst)
                        }
                    }
                    else -> {
                        Log.d(TAG, "renameTo(): use renameTo to move file")
                        src.renameTo(dst)
                    }
                }
                Log.w(TAG, "renameTo result: $isRenameToSucc")
                return isRenameToSucc
            }
        }

        /**
         * check Android Directory on Android R
         * @param targetFile target file to check
         * @return return true if target file is Restricted, or return false
         */
        private fun isAndroidDirectory(targetFile: File): Boolean {
            try {
                return RESTRICTED_ANDROID_DIR.matcher(targetFile.absolutePath).find()
            } catch (ex: Exception) {
                Log.w(TAG, "isAndroidDirectory error: $ex")
            }
            return false
        }

        /**
         * check Android Directory on Android R
         * @param targetPath target path to check
         * @return return true if target path is Restricted, or return false
         */
        fun isAndroidDirectoryR(targetPath: String): Boolean {
            if (!SdkUtils.isAtLeastR()) {
                return false
            }
            return isAndroidDirectory(File(targetPath))
        }


        @Throws(Exception::class)
        private fun recursiveCopyFile(srcFile: File, dstFile: File?, dstParent: File?): Boolean {
            var innerDstFile = dstFile
            if (innerDstFile == null) {
                innerDstFile = File(dstParent, srcFile.name)
            }
            if (srcFile.isDirectory) {
                if (!(innerDstFile.exists() || innerDstFile.mkdir())) {
                    return false
                }
                val listFiles = JavaFileHelper.listFiles(srcFile)
                if (listFiles != null) {
                    for (f in listFiles) {
                        if (!recursiveCopyFile(f, null, innerDstFile)) {
                            return false
                        }
                    }
                }
                return true
            } else {
                val result = Files.copy(srcFile.toPath(), innerDstFile.toPath(), StandardCopyOption.REPLACE_EXISTING) != null
                recoveryFileLastModified(srcFile, innerDstFile)
                return result
            }
        }

        private fun recoveryFileLastModified(src: File?, dst: File?) {
            //Copying needs to keep the file lastModified unchanged
            src?.let {
                dst?.setLastModified(src.lastModified())
            }
        }

        @Throws(Exception::class)
        private fun recursiveDeleteFile(srcFile: File): Boolean {
            if (srcFile.exists()) {
                if (srcFile.isDirectory) {
                    val listFiles = JavaFileHelper.listFiles(srcFile)
                    if (listFiles != null) {
                        for (f in listFiles) {
                            if (!recursiveDeleteFile(f)) {
                                return false
                            }
                        }
                    }
                }
                return srcFile.delete()
            }
            return true
        }

        @Throws(IOException::class)
        fun createNewFile(context: Context, file: File): Boolean {
            if (mIsUseSAF) {
                return false
            } else {
                if (exists(file)) {
                    return true
                } else {
                    val parentFile = file.parentFile
                    if (!parentFile.exists()) {
                        parentFile.mkdirs()
                    }
                    return file.createNewFile()
                }
            }
        }
    }
///////////////////////////////////////////////////////////////////////////////////////
}
