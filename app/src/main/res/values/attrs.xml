<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="coui_state_allSelect" format="boolean" />
    <attr name="coui_state_partSelect" format="boolean" />
    <attr name="COUICheckBoxStyle" format="reference" />
    <declare-styleable name="COUICheckBox">
        <attr name="couiButton" format="reference" />
        <attr name="couiCheckBoxState">
            <enum name="unSelected" value="0" />
            <enum name="partSelected" value="1" />
            <enum name="allSelected" value="2" />
        </attr>
    </declare-styleable>
</resources>
