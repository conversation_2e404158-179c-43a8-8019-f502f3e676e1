package com.oplus.dropdrag.recycleview

import android.view.MotionEvent
import androidx.recyclerview.widget.RecyclerView
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * EventRouterTest 是用于测试 EventRouter 相关功能的单元测试类
 * 主要测试 ToolHandlerRegistry、GestureRouter 和 TouchEventRouter 的功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class EventRouterTest {

    // 定义测试所需的成员变量
    private lateinit var toolHandlerRegistry: ToolHandlerRegistry<ExtendGestureListener>
    private lateinit var gestureRouter: GestureRouter
    private lateinit var touchEventRouter: TouchEventRouter<Any>
    private lateinit var mockDefaultDelegate: ExtendGestureListener
    private lateinit var mockDelegate: ExtendGestureListener
    private lateinit var mockMotionEvent: MotionEvent
    private lateinit var mockRecyclerView: RecyclerView
    private lateinit var mockDetector: DragSelectionDetector<Any>
    private lateinit var mockItemTouchListener: RecyclerView.OnItemTouchListener

    /**
     * 在每个测试方法执行前的初始化方法
     * 创建所有需要的mock对象和测试实例
     */
    @Before
    fun setUp() {
        // 创建mock对象
        mockDefaultDelegate = mockk()
        mockDelegate = mockk()
        mockMotionEvent = mockk()
        mockRecyclerView = mockk()
        mockDetector = mockk()
        mockItemTouchListener = mockk()

        // 初始化测试实例
        toolHandlerRegistry = ToolHandlerRegistry(mockDefaultDelegate)
        gestureRouter = GestureRouter(mockDefaultDelegate)
        touchEventRouter = TouchEventRouter(mockDetector, mockItemTouchListener)
    }

    /**
     * 测试GestureRouter的注册和委托调用功能
     * 1. 测试注册的委托是否能正确接收事件
     * 2. 测试未注册的工具类型是否能正确调用默认委托
     */
    @Test
    fun testGestureRouter_RegisterAndDelegateCalls() {
        // 注册委托到手指工具类型
        gestureRouter.register(MotionEvent.TOOL_TYPE_FINGER, mockDelegate)

        // 测试委托调用 - 模拟手指工具类型事件
        every { mockMotionEvent.getToolType(0) } returns MotionEvent.TOOL_TYPE_FINGER
        every { mockDelegate.onSingleTapConfirmed(mockMotionEvent) } returns true
        // 验证委托方法被正确调用
        assertTrue(gestureRouter.onSingleTapConfirmed(mockMotionEvent))
        verify { mockDelegate.onSingleTapConfirmed(mockMotionEvent) }

        // 测试默认委托调用 - 模拟触笔工具类型事件
        every { mockMotionEvent.getToolType(0) } returns MotionEvent.TOOL_TYPE_STYLUS
        every { mockDefaultDelegate.onDoubleTap(mockMotionEvent) } returns true
        // 验证默认委托方法被正确调用
        assertTrue(gestureRouter.onDoubleTap(mockMotionEvent))
        verify { mockDefaultDelegate.onDoubleTap(mockMotionEvent) }
    }

    /**
     * 测试TouchEventRouter的拦截触摸事件功能
     * 1. 测试注册的委托是否能正确拦截事件
     * 2. 测试检测器是否能正确拦截事件
     */
    @Test
    fun testTouchEventRouter_InterceptTouchEvent() {
        // 注册委托到手指工具类型
        touchEventRouter.register(MotionEvent.TOOL_TYPE_FINGER, mockItemTouchListener)

        // 测试委托拦截 - 模拟手指工具类型事件
        every { mockMotionEvent.getToolType(0) } returns MotionEvent.TOOL_TYPE_FINGER
        every { mockItemTouchListener.onInterceptTouchEvent(mockRecyclerView, mockMotionEvent) } returns true
        every { mockDetector.onDetectInterceptTouchEvent(mockMotionEvent) } returns false
        // 验证委托拦截方法被正确调用
        assertTrue(touchEventRouter.onInterceptTouchEvent(mockRecyclerView, mockMotionEvent))
        verify { mockItemTouchListener.onInterceptTouchEvent(mockRecyclerView, mockMotionEvent) }
        verify { mockDetector.onDetectInterceptTouchEvent(mockMotionEvent) }

        // 测试检测器拦截 - 模拟检测器返回true的情况
        every { mockItemTouchListener.onInterceptTouchEvent(mockRecyclerView, mockMotionEvent) } returns false
        every { mockDetector.onDetectInterceptTouchEvent(mockMotionEvent) } returns true
        // 验证检测器拦截方法被正确调用
        assertTrue(touchEventRouter.onInterceptTouchEvent(mockRecyclerView, mockMotionEvent))
    }
}