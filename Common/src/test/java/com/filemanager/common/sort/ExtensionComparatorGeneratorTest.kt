/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ExtensionComparatorGeneratorTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/10/8
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  hank.zhou      2022/10/8      1.0        create
 ***********************************************************************/
package com.filemanager.common.sort

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.FileTypeUtils
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class ExtensionComparatorGeneratorTest {

    @Before
    fun setup() {
        mockkStatic(FileTypeUtils::class)
        every { FileTypeUtils.getExtension(any()) }.answers { callOriginal() }
    }

    @After
    fun teardown() {
        unmockkStatic(FileTypeUtils::class)
    }

    @Test
    fun testGetComparator() {
        val compare = ExtensionComparatorGenerator.getComparator(true)
        val file1 = BaseFileBean()
        val file2 = BaseFileBean()
        file1.mDisplayName = "test.txt"
        file2.mDisplayName = "test.doc"
        Assert.assertTrue(compare.compare(file1, file2) > 0)
        val compare2 = ExtensionComparatorGenerator.getComparator(false)
        Assert.assertTrue(compare2.compare(file1, file2) < 0)
    }
}