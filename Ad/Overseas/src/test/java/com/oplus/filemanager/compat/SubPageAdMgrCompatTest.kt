/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : SubPageAdMgrCompatTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/3/16 20:25
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2023/3/16       1.0      create
 *********************************************************************/
package com.oplus.filemanager.compat

import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.Utils
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SubPageAdMgrCompatTest {

    @Before
    fun setUp() {
        mockkStatic(ModelUtils::class)
        mockkStatic(Utils::class)
    }

    @After
    fun tearDown() {
        unmockkStatic(ModelUtils::class)
        unmockkStatic(Utils::class)
    }

    @Test
    fun testSubPageOnOppo() {
        every { ModelUtils.isTablet() } returns false
        every { Utils.isRealmePhone() } returns false
        Assert.assertFalse(SubPageAdMgrCompat.isSupportDocAd())
        Assert.assertTrue(SubPageAdMgrCompat.isSupportApkAd())
        Assert.assertTrue(SubPageAdMgrCompat.isHideApkInstallAd())
        Assert.assertTrue(SubPageAdMgrCompat.isSupportAudioAd())
        Assert.assertTrue(SubPageAdMgrCompat.isSupportAlbumSetAd())
        Assert.assertTrue(SubPageAdMgrCompat.isSupportPhotoStorageAd())
    }

    @Test
    fun testSubPageOnRealme() {
        every { ModelUtils.isTablet() } returns false
        every { Utils.isRealmePhone() } returns true
        Assert.assertTrue(SubPageAdMgrCompat.isSupportDocAd())
        Assert.assertTrue(SubPageAdMgrCompat.isSupportApkAd())
        Assert.assertFalse(SubPageAdMgrCompat.isHideApkInstallAd())
        Assert.assertTrue(SubPageAdMgrCompat.isSupportAudioAd())
        Assert.assertFalse(SubPageAdMgrCompat.isSupportAlbumSetAd())
        Assert.assertFalse(SubPageAdMgrCompat.isSupportPhotoStorageAd())
    }

    @Test
    fun testSubPageOnNull() {
        every { ModelUtils.isTablet() } returns true
        Assert.assertFalse(SubPageAdMgrCompat.isSupportDocAd())
        Assert.assertFalse(SubPageAdMgrCompat.isSupportApkAd())
        Assert.assertFalse(SubPageAdMgrCompat.isHideApkInstallAd())
        Assert.assertFalse(SubPageAdMgrCompat.isSupportAudioAd())
        Assert.assertFalse(SubPageAdMgrCompat.isSupportAlbumSetAd())
        Assert.assertFalse(SubPageAdMgrCompat.isSupportPhotoStorageAd())
    }
}