/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileServiceHandler
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/1 18:01
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/1       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate

import android.app.Service
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.os.RemoteException
import com.filemanager.common.utils.Log
import com.oplus.fileservice.FileServiceManager
import com.oplus.fileservice.operate.convert.converterOperateRequest
import java.util.UUID

/**
 * [FileOperateServiceHandler] is the processing class
 * when Service Platform uses [Service.bindService] to call [FileOperateService].
 *
 * Its internal logic mainly has three steps:
 * - First, generate a requestId and return it to the caller through [Messenger.send].
 * - Then, enqueue the operation request and execute request, obtains the operation result.
 * - Finally, use the requestId to update the result or status of the operation.
 */
class FileOperateServiceHandler(
    private val context: Context
) : Handler(Looper.getMainLooper()) {

    private var mClientMessenger: Messenger? = null

    override fun handleMessage(msg: Message) {
        try {
            replyMessage(msg)
        } catch (e: RemoteException) {
            Log.e(TAG, "handleMessage RemoteException: ${e.message}")
        }
    }

    private fun replyMessage(msg: Message): Message {
        val replyMsg = Message.obtain()
        Log.d(TAG, "replyMessage -> msg = ${msg.what}")
        when (msg.what) {
            MESSAGE_REQUEST_ID -> {
                try {
                    mClientMessenger = msg.replyTo
                    val requestId = generateRequestId()
                    executeRequest(context, msg.data, requestId, replyMsg)
                    replyClient(replyMsg, requestId)
                } catch (e: RemoteException) {
                    Log.e(TAG, "onBind -> replyTo error: ${e.message}")
                }
            }
            else -> super.handleMessage(msg)
        }
        return replyMsg
    }

    private fun generateRequestId(): String {
        return "${System.currentTimeMillis()}_${UUID.randomUUID()}"
    }

    private fun executeRequest(context: Context, request: Bundle?, requestId: String, replyMsg: Message) {
        request?.let {
            val operateRequest = converterOperateRequest(it)
            Log.d(TAG, "executeRequest -> operateRequest = $operateRequest")
            operateRequest.requestId = requestId
            FileServiceManager.getInstance().enqueueAutoReplyRequest(context, operateRequest) {
                Log.d(TAG, "executeRequest -> send msg to stop FileOperateService")
                val data = Bundle()
                replyMsg.what = MESSAGE_WHAT_REPLY_UNBIND
                replyMsg.data = data
                try {
                    mClientMessenger?.send(replyMsg)
                } catch (e: RemoteException) {
                    Log.e(TAG, "executeRequest -> send message error: $e")
                }
            }
        }
    }

    private fun replyClient(replyMsg: Message, requestId: String) {
        Log.d(TAG, "replyClient -> requestId = $requestId")
        val data = Bundle()
        data.putString(RESULT, requestId)
        replyMsg.data = data
        replyMsg.what = MESSAGE_WHAT_REPLY_RESULT
        try {
            mClientMessenger?.send(replyMsg)
        } catch (e: RemoteException) {
            Log.e(TAG, "replyClient -> error: $e")
        }
    }

    companion object {
        private const val TAG = "FileOperateHandler"
        private const val RESULT = "result"
        private const val MESSAGE_REQUEST_ID = 0x0002

        private const val MESSAGE_WHAT_REPLY_RESULT = 1
        private const val MESSAGE_WHAT_REPLY_UNBIND = 2
    }
}