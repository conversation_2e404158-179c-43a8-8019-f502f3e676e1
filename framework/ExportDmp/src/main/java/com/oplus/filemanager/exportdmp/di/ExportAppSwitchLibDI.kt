/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: ExportAppSwitchLibDI.kt
 ** Description: appSwitch的注入类
 ** Version: 1.0
 ** Date: 2024/8/28
 ** Author: muning
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.exportdmp.di

import androidx.annotation.Keep
import com.oplus.filemanager.exportdmp.ExportAppSwitchApi
import com.oplus.filemanager.interfaze.appswitch.IAppSwitchApi
import org.koin.dsl.module

@Keep
class ExportAppSwitchLibDI {

    val appSwitchLibModule = module {
        single<IAppSwitchApi>(createdAtStart = true) {
            ExportAppSwitchApi
        }
    }
}