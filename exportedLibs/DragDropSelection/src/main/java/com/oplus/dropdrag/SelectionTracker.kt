/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * File:  - SelectionTracker.kt
 * Description: abstract base for Selection Tracker
 * Version: 1.0
 * Date : 2020/05/15
 * Author: Jiafei.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * Ji<PERSON><PERSON>.<EMAIL>      2020/05/15    1.0     create
 ***********************************************************************/
package com.oplus.dropdrag

import android.view.HapticFeedbackConstants
import android.view.MotionEvent
import androidx.annotation.Keep
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnItemTouchListener
import com.oplus.dropdrag.recycleview.AutoScroller.Companion.DEFAULT_SCROLL_THRESHOLD_RATIO
import com.oplus.dropdrag.utils.Log
import com.oplus.dropdrag.recycleview.*
import com.oplus.dropdrag.recycleview.GestureRouter
import com.oplus.dropdrag.recycleview.TouchEventRouter

@Keep
abstract class SelectionTracker<K> {
    @Keep
    companion object {
        const val DEBUG = true

        // INVALID Long Item ID
        const val NO_LONG_ITEM_ID = -1L

        // INVALID Item Int key
        const val NO_INT_ITEM_ID = -1

        // key for Item to skip selection checked
        const val SKIP_INT_ITEM_ID = -2

        // key for Item to skip selection checked
        private const val SKIP_LONG_ITEM_ID = -2L

        @JvmStatic
        internal fun <K> isValidKey(key: K): Boolean {
            if ((key is Int) && (key == NO_INT_ITEM_ID)) {
                return false
            } else if ((key is Int) && (key == SKIP_INT_ITEM_ID)) {
                return false
            } else if ((key is Long) && (key == NO_LONG_ITEM_ID)) {
                return false
            } else if ((key is Long) && (key == SKIP_LONG_ITEM_ID)) {
                return false
            }
            return true
        }

        @JvmStatic
        internal fun <K> isSkipKey(key: K): Boolean {
            if (key is Int) {
                return (key == SKIP_INT_ITEM_ID)
            } else if (key is Long) {
                return (key == SKIP_LONG_ITEM_ID)
            } else {
                return false
            }
        }
    }

    @Keep
    enum class LAYOUT_TYPE {
        LIST, GRID
    }

    /*
     @return true if has a selection
     */
    abstract fun hasSelection(): Boolean

    /** @return true if has a selection
     */
    abstract fun isInSelectionMode(): Boolean

    /**
     * @return true if the item specified by its id is selected. Shorthand for
     */
    abstract fun enterSelectMode(key: K): Boolean

    /**
     * @return true if the item specified by its id is selected. Shorthand for
     */
    abstract fun isItemSelected(key: K): Boolean

    /**
     * Attempts to select an item.
     * @return true if the item was selected. False if the item could not be selected, or was
     * was already selected.
     */
    abstract fun selectItem(key: K): Boolean

    /**
     * Attempts to deselect an item.
     * @return true if the item was deselected. False if the item could not be deselected, or was
     * was already un-selected.
     */
    abstract fun deselectItem(key: K): Boolean

    /**
     * @return true if the item was deselected. False if the item could not be deselected, or was
     */
    abstract fun getSelectionList(): List<K>

    /**
     * @return true if support MultipleSelect, or false
     */
    abstract fun canMultipleSelect(): Boolean

    abstract val adapterDataObserver: RecyclerView.AdapterDataObserver

    /**
     * Sets the end point for the active range selection.
     * This function should only be called when a range selection is active
     * (see [.isRangeActive]. Items in the range [anchor, end] will be
     * selected after consulting SelectionPredicate.
     * @param position The new end position for the selection range.
     * must have been started by a call to [.startRange].
     */
    abstract fun extendRange(position: Int)

    /**
     * Clears an in-progress range selection. Provisional range selection established
     * using [.extendProvisionalRange] will be cleared (unless
     * [.mergeProvisionalSelection] is called first.)
     */
    abstract fun endRange()

    /**
     * @return Whether or not there is a current range selection active.
     */
    abstract val isRangeActive: Boolean

    /**
     * Establishes the "anchor" at which a selection range begins. This "anchor" is consulted
     * when determining how to extend, and modify selection ranges. Calling this when a
     * range selection is active will reset the range selection.
     * @param position the anchor position. Must already be selected.
     */
    abstract fun anchorRange(position: Int, key: K)

    /**
     * Creates a provisional selection from anchor to `position`.
     *
     * @param position the end point.
     */
    abstract fun extendProvisionalRange(position: Int)

    /**
     * Clears any existing provisional selection
     */
    abstract fun clearProvisionalSelection()

    /**
     * Converts the provisional selection into primary selection, then clears
     * provisional selection.
     */
    abstract fun mergeProvisionalSelection()

    /**
     * @return true if can drag and drop.or false
     */
    abstract fun canDragDrop(): Boolean

    /**
     * @return true if can slide selection.or false
     */
    abstract fun canSlideSelection(): Boolean

    abstract var longPressIndex: Int

    abstract var layoutType: LAYOUT_TYPE

    abstract var scrollThresholdRatio: Float
}

@Keep
class RecycleSelectionBuilder<K>(
    trackerTag: String,
    recyclerView: RecyclerView,
    selectionDelegate: SelectionDelegate<K>,
    keyProvider: ItemKeyProvider<K>?,
    detailsLookup: ItemDetailsLookup<K>?
) {

    private companion object {
        private const val TAG = "RecycleSelectionBuilder"

        private val TOUCH_TOOL_TYPES = intArrayOf(
                MotionEvent.TOOL_TYPE_FINGER,
                MotionEvent.TOOL_TYPE_UNKNOWN,
                MotionEvent.TOOL_TYPE_STYLUS
        )

        private val MOUSE_TOOL_TYPES = intArrayOf(
                MotionEvent.TOOL_TYPE_MOUSE
        )
    }

    private var mIsMultipleSelected = true
    private var mCanSlideSelection = true
    private var mScrollThresholdRatio: Float = DEFAULT_SCROLL_THRESHOLD_RATIO

    private var mOnItemClickListener: OnItemClickListener<K>? = null
    private var mOnDragStartListener: OnDragStartListener? = null
    private var mOnItemTouchListener: OnItemTouchListener? = null
    private var mOnSlideSelectionStateListener: OnSlideSelectionStateListener? = null
    private var mOnContextClickListener: OnContextClickListener? = null

    private val mRecyclerView: RecyclerView = recyclerView
    private val mTrackerTag: String = trackerTag
    private val mKeyProvider: ItemKeyProvider<K>? = keyProvider
    private val mDetailsLookup: ItemDetailsLookup<K>? = detailsLookup
    private val mSelectionDelegate: SelectionDelegate<K> = selectionDelegate
    private var mSelectionPredicate: SelectionPredicates.SelectionPredicate<K>

    init {
        mSelectionPredicate = if (mIsMultipleSelected) {
            SelectionPredicates.createMultiplesSelect()
        } else {
            SelectionPredicates.createSingleSelect()
        }
    }

    /**
     * set Multiple selection.
     * @param isMultipleSelected is support multiple selection.
     * @return this
     */
    fun withSelectMode(isMultipleSelected: Boolean): RecycleSelectionBuilder<K> {
        mIsMultipleSelected = isMultipleSelected
        return this
    }


    /**
     * set SelectionPredicates.
     * @param predicate predicate control when items can be selected or unselected.
     * @return this
     */
    fun withSelectionPredicates(predicate: SelectionPredicates.SelectionPredicate<K>): RecycleSelectionBuilder<K> {
        mSelectionPredicate = predicate
        return this
    }

    /**
     * Adds an item activation listener. Respond to taps/enter/double-click on items.
     * @param listener the listener to be used
     * @return this
     */
    fun withOnItemClickListener(
            listener: OnItemClickListener<K>
    ): RecycleSelectionBuilder<K> {
        mOnItemClickListener = listener
        return this
    }

    /**
     * Adds a context click listener. Respond to right-click.
     * @param listener the listener to be used
     * @return this
     */
    fun withOnContextClickListener(
            listener: OnContextClickListener
    ): RecycleSelectionBuilder<K> {
        mOnContextClickListener = listener
        return this
    }

    /**
     * Adds a drag initiated listener. Add support for drag and drop.
     * @param listener the listener to be used
     * @return this
     */
    fun withOnDragStartListener(
            listener: OnDragStartListener
    ): RecycleSelectionBuilder<K> {
        mOnDragStartListener = listener
        return this
    }

    /**
     * Adds a touch initiated listener.
     * @param listener the listener to be used
     * @return this
     */
    fun withOnItemTouchListener(
            listener: OnItemTouchListener
    ): RecycleSelectionBuilder<K> {
        mOnItemTouchListener = listener
        return this
    }

    /**
     * set slide selection state listener.
     * @param listener  slide selection start or end will call the listener
     */
    fun withSlideSelectionStateListener(listener: OnSlideSelectionStateListener?): RecycleSelectionBuilder<K> {
        mOnSlideSelectionStateListener = listener
        return this
    }

    /**
     * set slide selection state.
     * @param canSlideSelection support slide selection if true.
     */
    fun withSlideSelection(canSlideSelection: Boolean): RecycleSelectionBuilder<K> {
        mCanSlideSelection = canSlideSelection
        return this
    }

    /**
     * set ScrollThresholdRatio to scroll the view,if touch position in ThresholdRatio.
     * @param scrollThresholdRatio layoutType to set.
     */
    fun withScrollThresholdRatio(scrollThresholdRatio: Float): RecycleSelectionBuilder<K> {
        if ((mScrollThresholdRatio <= 0) || (mScrollThresholdRatio >= 1)) {
            Log.w(TAG, "withScrollThresholdRatio INVALID scrollThresholdRatio: $scrollThresholdRatio")
            return this
        }

        mScrollThresholdRatio = scrollThresholdRatio
        return this
    }


    fun build(): SelectionTracker<K> {
        val tracker = RecycleSelectionTracker(mTrackerTag, mSelectionDelegate, mKeyProvider,
                mSelectionPredicate, mCanSlideSelection)
        try {
            mRecyclerView.adapter?.registerAdapterDataObserver(tracker.adapterDataObserver)
            val scrollHost = ScrollHost.createScrollHost(mRecyclerView)

            val gestureRouter = GestureRouter()
            val gestureDetector = DragSelectionDetector.create(
                mRecyclerView.context,
                tracker,
                gestureRouter,
                mDetailsLookup,
                mSelectionPredicate
            )
            val eventRouter = object : TouchEventRouter<K>(gestureDetector) {
                override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
                    if (mOnItemTouchListener?.onInterceptTouchEvent(rv, e) == true) {
                        return true
                    }
                    return super.onInterceptTouchEvent(rv, e)
                }

                override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {
                    mOnItemTouchListener?.onTouchEvent(rv, e)
                    super.onTouchEvent(rv, e)
                }
            }
            val slideSelectionHelper = SlideSelectionHelper.create(tracker, mDetailsLookup, mRecyclerView, scrollHost)
            slideSelectionHelper.setSlideStateListener(mOnSlideSelectionStateListener)
            // Finally hook the framework up to listening to recycle view events.
            mRecyclerView.addOnItemTouchListener(eventRouter)

            mOnDragStartListener = if (mOnDragStartListener != null) {
                mOnDragStartListener
            } else {
                object : OnDragStartListener {
                    override fun onDragStart(e: MotionEvent): Boolean {
                        return false
                    }
                }
            }

            mOnItemClickListener = if (mOnItemClickListener != null) {
                mOnItemClickListener
            } else {
                object : OnItemClickListener<K> {
                    override fun onItemClick(
                        item: ItemDetailsLookup.ItemDetails<K>,
                        e: MotionEvent): Boolean {
                        return false
                    }
                }
            }

            mOnContextClickListener = if (mOnContextClickListener != null) {
                mOnContextClickListener
            } else {
                object : OnContextClickListener {
                    override fun onContextClick(e: MotionEvent): Boolean {
                        return false
                    }
                }
            }

            // Provides high level glue for binding touch events
            // and gestures to selection framework.
            val touchHandler: TouchInputHandler<K> = TouchInputHandler(
                    tracker,
                    mDetailsLookup,
                    mSelectionPredicate,
                    mOnDragStartListener,
                    mOnItemClickListener,
                    Runnable { mRecyclerView.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS) }
            )

            for (toolType in TOUCH_TOOL_TYPES) {
                gestureRouter.register(toolType, touchHandler)
                eventRouter.register(toolType, slideSelectionHelper)
            }

            // Provides high level glue for binding mouse events and gestures
            // to selection framework.
            val mouseHandler = MouseInputHandler(
                    tracker,
                    mDetailsLookup,
                    mSelectionPredicate,
                    mOnDragStartListener,
                    mOnItemClickListener)

            for (toolType in MOUSE_TOOL_TYPES) {
                gestureRouter.register(toolType, mouseHandler)
                eventRouter.register(toolType, slideSelectionHelper)
            }
        } catch (ex: Exception) {
            Log.w(TAG, "build error: $ex")
        }
        return tracker
    }
}