/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForFileService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.wechatsdk.di

import androidx.annotation.Keep
import com.oplus.filemanager.interfaze.wechat.IWechat
import com.oplus.filemanager.wechatsdk.WeChatSdkApi
import org.koin.dsl.module

@Keep
class AutoDIForWechat {

    val wechatModule = module {
        single<IWechat>(createdAtStart = true) {
            WeChatSdkApi
        }
    }
}