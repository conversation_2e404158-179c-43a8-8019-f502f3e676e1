<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="com.oplus.permission.safe.BACKUP" />

    <application>
        <service
            android:name="com.oplus.filemanager.backuprestore.FileManagerBRPluginService"
            android:permission="com.oplus.permission.safe.BACKUP"
            android:exported="true">
            <!-- uniqueID : String类型，插件ID，业务的唯一标识。 -->
            <meta-data
                android:name="uniqueID"
                android:value="1430" />
            <!-- backup_name_resId : res/string类型，插件显示的名称。如果设置了父插件，可不写。 -->
            <meta-data
                android:name="version"
                android:value="1">
            </meta-data>
            <meta-data
                android:name="backup_name_resId"
                android:value="@string/app_name" />
            <!-- backup_icon_resId : res/drawable类型，插件显示的图标。如果设置了父插件，可不写 -->
            <meta-data
                android:name="backup_icon_resId"
                android:value="@drawable/ic_launcher_filemanager" />
            <meta-data
                android:name="isVendorSupport"
                android:value="true"/>
            <intent-filter>
                <action android:name="com.oplus.br.service" />
                <category android:name="android.intent.category.default" />
            </intent-filter>
        </service>
    </application>

</manifest>