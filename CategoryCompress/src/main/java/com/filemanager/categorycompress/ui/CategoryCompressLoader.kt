/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.superapp
 * * Version     : 1.0
 * * Date        : 2020/4/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.categorycompress.ui

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import android.text.TextUtils
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.loader.BaseUriLoader
import com.filemanager.common.compat.MediaStoreCompat.getMediaStoreSqlQuery
import com.filemanager.common.constants.KtConstants.SECONDS_TO_MILLISECONDS
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MediaHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.BlacklistParser
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.interfaze.main.IMain
import java.io.File

class CategoryCompressLoader(context: Context, compressType: String) :
    BaseUriLoader<Int, MediaFileWrapper>(context) {

    companion object {
        private const val TAG = "CategoryCompressLoader"
        private const val INDEX_ID = 0
        private const val INDEX_DATA = 1
        private const val INDEX_DISPLAY_NAME = 2
        private const val INDEX_SIZE = 3
        private const val INDEX_DATE_MODIFIED = 4
        private const val INDEX_DATE_MIME_TYPE = 5
        private const val INDEX_DATE_MEDIA_FORMAT = 6

        @JvmStatic
        private val MEDIA_PROJECT = arrayOf(
                MediaStore.Files.FileColumns._ID,
                MediaStore.Files.FileColumns.DATA,
                MediaStore.Files.FileColumns.DISPLAY_NAME,
                MediaStore.Files.FileColumns.SIZE,
                MediaStore.Files.FileColumns.DATE_MODIFIED,
                MediaStore.Files.FileColumns.MIME_TYPE,
                MediaHelper.COLUMN_MEDIA_FORMAT
        )
    }

    private var mCompressType: String = compressType
    private var mBaseQuerySelection: String = ""
    private val mSizeFilterState: Boolean
    private val mFilterSize: Int
    private var tempSort = -1
    private var tempDesc = -1

    init {
        initData()
        initSelection()
        mFilterSize = BlacklistParser.getFilterSizeByType(CategoryHelper.CATEGORY_COMPRESS)
        mSizeFilterState = mFilterSize > 0
    }

    private fun initSelection() {
        val sql = StringBuilder()
        when (mCompressType) {
            CategoryCompressViewModel.TYPE_ALL -> {
                sql.append(getMediaStoreSqlQuery(CategoryHelper.CATEGORY_COMPRESS, CategoryCompressViewModel.COMPRESS_EXT_ARRAY))
            }
            else -> {
                val selectionArgs = ArrayList<String?>()
                selectionArgs.add(mCompressType)
                sql.append(getMediaStoreSqlQuery(CategoryHelper.CATEGORY_COMPRESS, selectionArgs))
            }
        }
        mBaseQuerySelection = sql.toString()
    }

    override fun getUri(): Uri? = MediaHelper.FILE_URI

    override fun getSelection(): String = mBaseQuerySelection

    override fun getSelectionArgs(): Array<String>? = null

    override fun getObserverUri(): Array<Uri>? = null

    override fun getProjection(): Array<String> {
        return MEDIA_PROJECT
    }

    override fun createFromCursor(cursor: Cursor, uri: Uri?): MediaFileWrapper? {
        val path = cursor.getString(INDEX_DATA)
        val displayName = cursor.getString(INDEX_DISPLAY_NAME)
        if (TextUtils.isEmpty(path) || TextUtils.isEmpty(displayName)) {
            return null
        }
        val file = File(path)
        //file interface is slowly on Android R,So need not to call exists on Android R
        if (!SdkUtils.isAtLeastR() && (!file.exists() || file.isDirectory)) {
            return null
        } else {
            val isDir = if (cursor.isNull(INDEX_DATE_MEDIA_FORMAT)) {
                Log.d(TAG, "createFromCursor: format is null")
                file.isDirectory
            } else {
                val format = cursor.getInt(INDEX_DATE_MEDIA_FORMAT)
                (format == MediaHelper.MEDIA_FORMAT_DIR)
            }
            if (isDir) {
                return null
            }
        }
        val id = cursor.getInt(INDEX_ID)
        val size = cursor.getLong(INDEX_SIZE)
        var dateModified = cursor.getLong(INDEX_DATE_MODIFIED) * SECONDS_TO_MILLISECONDS
        val mimeType = cursor.getString(INDEX_DATE_MIME_TYPE)
        if (dateModified == 0L) {
            Log.d(TAG, "dateModified is 0")
            dateModified = FileTimeUtil.getFileTime(path) ?: 0
        }
        return MediaFileWrapper(id, path, displayName, mimeType, size, dateModified, MediaHelper.FILE_URI)
    }

    override fun preHandleResultBackground(list: MutableList<MediaFileWrapper>): MutableList<MediaFileWrapper> {
        if (tempSort != -1) {
            SortHelper.sortCategoryFiles(list, tempSort, CategoryHelper.CATEGORY_COMPRESS, tempDesc == 0)
        } else {
            val sort = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_COMPRESS))
            val isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_COMPRESS))
            SortHelper.sortCategoryFiles(list, sort, CategoryHelper.CATEGORY_COMPRESS, isDesc)
        }
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.findFileLabelIfHad(list)
        return list
    }

    override fun getItemKey(item: MediaFileWrapper): Int? = item.id

    fun setSort(sort: Int, d: Int) {
        tempSort = sort
        tempDesc = d
    }
}