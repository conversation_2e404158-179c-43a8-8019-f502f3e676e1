/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CommonUtil
 ** Description : 公共工具类，完成一些基本逻辑
 ** Version     : 1.0
 ** Date        : 2024/05/22 10:24
 ** Author      : <PERSON><PERSON><EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/06       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.simulateclick.ui.util

import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.Drawable
import android.text.TextUtils
import com.coui.appcompat.contextutil.COUIContextUtil
import com.oplus.filemanager.simulateclick.engine.utils.CompatUtils
import com.oplus.filemanager.simulateclick.engine.utils.Log
import com.oplus.filemanager.simulateclick.sdk.R

object CommonUtil {


    const val TAG = "CommonUtil"

    //各个目标应用的包名
    const val FEISHU_PACKAGE = "com.ss.android.lark"
    const val WECHAT_PACKAGE = "com.tencent.mm"
    const val DINGTALK_PACKAGE = "com.alibaba.android.rimet"
    const val WEWORK_PACKAGE = "com.tencent.wework"
    const val QQ_PACKAGE = "com.tencent.mobileqq"


    @JvmStatic
    fun getBitmapFromDrawable(drawable: Drawable?): Bitmap? {
        if ((drawable == null) || (drawable.intrinsicWidth <= 0) || (drawable.intrinsicHeight <= 0)) {
            return null
        } else {
            val width = drawable.intrinsicWidth
            val height = drawable.intrinsicHeight
            if ((width <= 0) || (height <= 0)) {
                return null
            }
            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)
            drawable.setBounds(0, 0, canvas.width, canvas.height)
            drawable.draw(canvas)
            return bitmap
        }
    }

    @JvmStatic
    fun getDialogColorButtonColors(context: Context, sourcePackage: String): Int {
        Log.i(TAG, "getDialogColorTheme sourcePackage $sourcePackage")
        val attrId = if (sourcePackage.contentEquals(FEISHU_PACKAGE, true)) {
            //飞书botton颜色
            com.support.appcompat.R.attr.couiColorMint
        } else if (sourcePackage.contentEquals(WECHAT_PACKAGE, true)) {
            //微信botton颜色
            com.support.appcompat.R.attr.couiColorGreen
        } else if (sourcePackage.contentEquals(QQ_PACKAGE, true)) {
            //qq的botton颜色
            com.support.appcompat.R.attr.couiColorYellow
        } else if (sourcePackage.contentEquals(WEWORK_PACKAGE, true)) {
            //企业微信的botton颜色
            com.support.appcompat.R.attr.couiColorBlue
        } else if (sourcePackage.contentEquals(DINGTALK_PACKAGE, true)) {
            //钉钉的botton颜色
            com.support.appcompat.R.attr.couiColorAzure
        } else {
            com.support.appcompat.R.attr.couiColorBlue
        }
        val colorInt = COUIContextUtil.getAttrColor(context, attrId)
        Log.i(TAG, "getSourceIcon Drawable $attrId, colorId $colorInt")
        return colorInt
    }


    @JvmStatic
    fun getThirdAppNameForPackage(context: Context, packageName: String): String? {
        var result: String? = ""
        return if (TextUtils.isEmpty(packageName)) {
            Log.e(TAG, "getThirdAppNameForPackage packageName empty")
            result
        } else {
            result = if (packageName.equals(FEISHU_PACKAGE, ignoreCase = true)) {
                context.getString(R.string.feishu_app_name)
            } else if (packageName.equals(WECHAT_PACKAGE, ignoreCase = true)) {
                context.getString(R.string.wechat_app_name)
            } else if (packageName.equals(DINGTALK_PACKAGE, ignoreCase = true)) {
                context.getString(R.string.dingtalk_app_name)
            } else if (packageName.equals(WEWORK_PACKAGE, ignoreCase = true)) {
                context.getString(R.string.wework_app_name)
            } else if (packageName.equals(QQ_PACKAGE, ignoreCase = true)) {
                context.getString(R.string.qq_app_name)
            } else {
                getApplicationNameForPackage(context, packageName)
            }
            result
        }
    }

    @JvmStatic
    @SuppressLint("UseCompatLoadingForDrawables")
    fun getSourceIcon(context: Context, sourcePackage: String): Drawable? {
        Log.i(TAG, "getSourceIcon sourcePackage $sourcePackage")
        val drawable = if (sourcePackage.contentEquals(FEISHU_PACKAGE, true)) {
            //飞书的botton的icon
            context.resources.getDrawable(R.drawable.feishu_botton, null)
        } else if (sourcePackage.contentEquals(WECHAT_PACKAGE, true)) {
            //微信的botton的icon
            context.resources.getDrawable(R.drawable.wechat_botton, null)
        } else if (sourcePackage.contentEquals(QQ_PACKAGE, true)) {
            //qq的botton的icon
            context.resources.getDrawable(R.drawable.qq_botton, null)
        } else if (sourcePackage.contentEquals(WEWORK_PACKAGE, true)) {
            //企业微信的botton的icon
            context.resources.getDrawable(R.drawable.wework_botton, null)
        } else if (sourcePackage.contentEquals(DINGTALK_PACKAGE, true)) {
            //钉钉的botton的icon
            context.resources.getDrawable(R.drawable.dingtalk_botton, null)
        } else {
            getApplicationIconForPackage(context, sourcePackage)
        }
        Log.i(TAG, "getSourceIcon Drawable $drawable")
        return drawable
    }

    @JvmStatic
    fun getApplicationIconForPackage(context: Context, packageName: String?): Drawable? {
        var result: Drawable? = null
        try {
            val pm = context.packageManager
            val packageInfo = pm.getPackageInfo(packageName!!, PackageManager.GET_META_DATA)
            if (packageInfo != null) {
                val applicationInfo = packageInfo.applicationInfo
                if (applicationInfo != null) {
                    result = applicationInfo.loadIcon(pm)
                }
            }
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "getApplicationIconForPackage error, ", e)
        }
        return result
    }


    @JvmStatic
    fun getApplicationNameForPackage(context: Context, packageName: String?): String? {
        var result: String? = null
        try {
            val pm = context.packageManager
            val packageInfo = pm.getPackageInfo(packageName!!, 0)
            if (packageInfo != null) {
                val applicationInfo = packageInfo.applicationInfo
                if (applicationInfo != null) {
                    result = pm.getApplicationLabel(applicationInfo).toString()
                }
            }
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "getApplicationIconForPackage error, ", e)
        }
        return result
    }


    /**
     * 检测应用是否被隐藏，参考文档：
     * https://hio.oppo.com/app/kb/center/view?source=index&enc_kbi_id=158022275_157670004&tt_&zone=%208&tt_zone=%208&tt_control=666666&tt_versionCode=800&new_client=1&mobileMsgShowStyle=1&pcMsgShowStyle=1&lang=zh-CN
     */
    @JvmStatic
    fun checkAppInHideMode(packageName: String): Boolean {
        var result = false
        runCatching {
            val hideMap = getAccessControlMap()
            if (hideMap != null && hideMap.isNotEmpty()) {
                val flag = hideMap[packageName]
                result = (flag != null && flag > 0)
                Log.i(TAG, "checkAppInHideMode packageName $packageName, flag $flag, result $result")
            }
        }.onFailure {
            Log.e(TAG, "checkAppInHideMode error", it)
        }
        return result
    }

    @JvmStatic
    private fun getAccessControlMap(): Map<String, Int> {
        return CompatUtils.compactSApi({
            com.oplus.app.OPlusAccessControlManager.getInstance().getAccessControlAppsInfo(
                com.oplus.app.OPlusAccessControlManager.TYPE_HIDE,
                com.oplus.app.OPlusAccessControlManager.USER_CURRENT
            )
        }, {
            com.heytap.addon.app.OPlusAccessControlManager.getInstance().getAccessControlAppsInfo(
                com.heytap.addon.app.OPlusAccessControlManager.TYPE_HIDE,
                com.heytap.addon.app.OPlusAccessControlManager.USER_CURRENT
            )
        })
    }


    /**
     * Check app is installed or not by pkgName
     *
     * @param context
     * @param pkgName
     * @return
     */
    @JvmStatic
    fun isAppInstalledByPkgName(context: Context, pkgName: String): Boolean {
        var packageInfo: PackageInfo? = null
        try {
            packageInfo = context.packageManager.getPackageInfo(pkgName, 0)
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "isPkgInstalled " + pkgName + "Not Found")
            packageInfo = null
        }
        return if (packageInfo == null) {
            false
        } else {
            true
        }
    }
}