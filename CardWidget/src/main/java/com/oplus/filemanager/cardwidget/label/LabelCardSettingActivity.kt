/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SelectLabelDialog
 ** Description : 标签卡片的设置界面
 ** Version     : 1.0
 ** Date        : 2022/11/25
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue      2022/11/25      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label

import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatusBarUtil
import com.oplus.filemanager.cardwidget.R
import com.oplus.filemanager.cardwidget.label.dialog.LabelCardSettingPanelFragment
import com.oplus.filemanager.cardwidget.label.dialog.LabelCardSettingVM
import org.json.JSONException
import org.json.JSONObject

class LabelCardSettingActivity : AppCompatActivity() {

    companion object {

        private const val TAG = "LabelCardSettingActivity"
        private const val TAG_SETTINGS = "settings"
        private const val TAG_SELECT_LABELS = "select_labels"

        const val CARD_TYPE = "cardType"  // 卡片type
        const val CARD_ID = "cardId"      // 卡片Id，默认为1，当重复添加卡片时，Id递增
        const val HOST_ID = "hostId"      // 0：添加在负一屏的卡片；1：添加在桌面的卡片
    }

    private var settingVM: LabelCardSettingVM? = null
    private var widgetCode: String? = null

    private val dialogFragment: COUIBottomSheetDialogFragment by lazy {
        COUIBottomSheetDialogFragment()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setBackgroundDrawable(
            ColorDrawable(
                COUIContextUtil.getAttrColor(
                    this,
                    com.support.appcompat.R.attr.couiColorMask
                )
            )
        )

        StatusBarUtil.setNavigationBarColor(this, R.color.card_setting_bg_color)
        initViewModel()
        window.decorView.post {
            if (widgetCode.isNullOrEmpty()) {
                StatisticsUtils.onCommon(MyApplication.sAppContext, StatisticsUtils.LONG_PRESS_CARD_TO_ENTER_EDIT_MODE)
            } else {
                StatisticsUtils.onCommon(MyApplication.sAppContext, StatisticsUtils.CLICK_CRAD_UPPER_LEFT_CORNER_TO_ENTER_EDIT_MODE)
            }
            showSettingsDialog()
        }
    }

    private fun initViewModel() {
        val intent = intent
        val cardType = IntentUtils.getInt(intent, CARD_TYPE, 0)
        val cardId = IntentUtils.getInt(intent, CARD_ID, 0)
        val hostId = IntentUtils.getInt(intent, HOST_ID, -1)
        Log.e(TAG, "initViewModel cardType:$cardType cardId:$cardId hostId:$hostId")
        fromLabelCardData()
        settingVM = ViewModelProvider(this).get(LabelCardSettingVM::class.java)
        if (widgetCode.isNullOrEmpty()) {
            settingVM?.setCardWidgetCode(cardType, cardId, hostId)
        } else {
            settingVM?.setWidgetCode(widgetCode!!)
        }
    }

    /**
     * 从标签卡片传来的打开数据，使用Json解析
     */
    private fun fromLabelCardData() {
        val paramsFromLabelCard = IntentUtils.getString(intent, Constants.TITLE_AND_LABEL_ID)
        if (!paramsFromLabelCard.isNullOrEmpty()) {
            try {
                val json = JSONObject(paramsFromLabelCard)
                widgetCode = json.getString(Constants.TITLE)
            } catch (e: JSONException) {
                Log.d(TAG, "fromLabelCardData json $e")
            }
        }
        Log.d(TAG, "fromLabelCardData widgetCode:$widgetCode paramsFromLabelCard:$paramsFromLabelCard")
    }

    /**
     * 显示标签卡片的设置dialog
     */
    private fun showSettingsDialog() {
        val fragment = supportFragmentManager.findFragmentByTag(TAG_SETTINGS)
        if ((fragment as? COUIBottomSheetDialogFragment)?.isVisible == true) {
            Log.d(TAG, "showSettingsDialog had exist")
            return
        }
        val settingDialog = LabelCardSettingPanelFragment()
        dialogFragment.setMainPanelFragment(settingDialog)
        dialogFragment.setIsShowInMaxHeight(false)
        dialogFragment.setHeight(resources.getDimensionPixelSize(R.dimen.card_label_dialog_height))
        if (supportFragmentManager.isStateSaved.not()) {
            dialogFragment.show(supportFragmentManager, TAG_SETTINGS)
        }
    }
}