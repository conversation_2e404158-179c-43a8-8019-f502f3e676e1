/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : HomePageAdMgrProxyTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/3/22 19:29
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2023/3/22       1.0      create
 *********************************************************************/
package com.oplus.filemanager.compat.proxy

import android.app.Activity
import android.view.View
import android.widget.RelativeLayout
import com.oplus.filemanager.compat.interfaces.IHomePageAdMgr
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.Assert
import org.junit.Test

class HomePageAdMgrProxyTest {

    @Test
    fun testHomePageAdMgrProxy() {
        val fakeAdMgr = spyk<FakeAdMgr>()
        val homePageAdMgrProxy = HomePageAdMgrProxy(fakeAdMgr)
        val view = mockk<View>()
        homePageAdMgrProxy.setContainer(view, 1)
        verify { fakeAdMgr.setContainer(any(), any()) }

        homePageAdMgrProxy.eventOnDestroy()
        verify { fakeAdMgr.eventOnDestroy() }

        val activity = mockk<Activity>()
        homePageAdMgrProxy.requestMainAd(activity)
        verify { fakeAdMgr.requestMainAd(any()) }

        val result = homePageAdMgrProxy.getEntryView("")
        verify { fakeAdMgr.getEntryView(any()) }
        Assert.assertNull(result)
    }
}

class FakeAdMgr : IHomePageAdMgr {
    override fun setContainer(rootView: View, rootViewId: Int) {
        // do nothing
    }

    override fun requestMainAd(activity: Activity) {
        // do nothing
    }

    override fun getEntryView(name: String): RelativeLayout? {
        // do nothing
        return null
    }

    override fun eventOnDestroy() {
        // do nothing
    }
}