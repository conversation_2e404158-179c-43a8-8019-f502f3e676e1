/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved.
 * * COLOROS_EDIT
 * * File: KeyMoveChoiceViewModel
 * * Description: the view model for KeyMoveChoiceFragment
 * * Version: 1.0
 * * Date : 2020/7/14
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/7/14       1.0         the view model for KeyMoveChoiceFragment
 ****************************************************************/
package com.oplus.filemanager.keymove.ui.keymovechoice

import android.content.Context
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import androidx.annotation.WorkerThread
import androidx.lifecycle.MutableLiveData
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.interfaces.LoadingLoaderListener
import com.filemanager.common.utils.Log
import com.oplus.filemanager.keymove.R
import com.oplus.filemanager.keymove.factor.LoaderFactor
import com.oplus.filemanager.keymove.ui.AKeyToMoveActivity
import com.oplus.filemanager.keymove.ui.KeyMoveLoader
import com.oplus.filemanager.keymove.ui.model.ItemChild
import com.oplus.filemanager.keymove.ui.model.ListPrimary
import com.oplus.filemanager.keymove.ui.model.ListSecond
import com.oplus.filemanager.keymove.ui.model.Visitable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class KeyMoveChoiceViewModel : BaseViewModel() {

    companion object {
        private const val TAG = "KeyMoveChoiceViewModel"
        private const val KEY_MOVE_LOADER_ID = 1
        private const val ILLEGAL_RESULT = -1
        private const val SWITCH_ON_RESULT = 1
        private const val KEY_MODULE = "module"
        private const val KEY_KEY = "key"
        private const val KEY_GALLERY_SLIMMING = "key_gallery_slimming"
        private const val MODULE_GALLERY_SLIMMING = "album"
        private const val METHOD_GALLERY_SLIMMING = "get_cloud_status"
        private const val CONTENT_AUTHORITY_CLOUD_SLASH = "content://ocloudstatus/cloud_status"
    }

    var mUiState = MutableLiveData<KeyMoveUiModel>()
    // Data is one of STATE_START/STATE_DONE/STATE_CANCEL in OnLoaderListener.
    val mDataLoadState = MutableLiveData<Int>()
    private var mPhotoSlimmingState = false
    private val mLoaderCallBack = KeyMoveCallBack(this)

    fun initLoader(mLoaderController: LoaderController?, isNeedUpgradeData: Boolean) {
        if (mLoaderCallBack.getLoader() == null) {
            mLoaderController?.initLoader(KEY_MOVE_LOADER_ID, mLoaderCallBack)
        } else if (isNeedUpgradeData) {
            mLoaderCallBack.getLoader()!!.forceLoad()
        }
    }

    class KeyMoveCallBack :
        LoadingLoaderListener<KeyMoveChoiceViewModel, KeyMoveLoader, KeyMoveLoader.KeyMoveLoadResult> {

        constructor(viewModel: KeyMoveChoiceViewModel): super(viewModel, viewModel.mDataLoadState)

        override fun onCreateLoader(viewModel: KeyMoveChoiceViewModel?): KeyMoveLoader? {
            return LoaderFactor.getKeyMoveLoader()
        }

        override fun onLoadComplete(viewModel: KeyMoveChoiceViewModel?, data: KeyMoveLoader.KeyMoveLoadResult?) {
            Log.d(TAG, "onLoadFinished size" + data?.getResultSize())
            data?.let {
                viewModel?.handleResult(data)
            }
        }
    }

    fun handleResult(data: KeyMoveLoader.KeyMoveLoadResult) {
        val groupItems = ArrayList<ListPrimary>()
        val albums = ArrayList<ListSecond>()
        val context = MyApplication.sAppContext
        launch {
            withContext(Dispatchers.Default) {
                mDataLoadState.postValue(OnLoaderListener.STATE_START)
                mPhotoSlimmingState = getPhotosSlimming()
                if (!mPhotoSlimmingState) {
                    data.mAlbums[KeyMoveLoader.BEFORE_SIX_MONTH]?.let {
                        albums.add(ListSecond(context.getString(com.filemanager.common.R.string.string_six_months_ago), it))
                    }
                    data.mAlbums[KeyMoveLoader.BETWEEN_THREE_TO_SIX_MONTH]?.let {
                        albums.add(ListSecond(context.getString(com.filemanager.common.R.string.string_three_to_six_months), it))
                    }
                    data.mAlbums[KeyMoveLoader.BETWEEN_ONE_TO_THREE_MONTH]?.let {
                        albums.add(ListSecond(context.getString(com.filemanager.common.R.string.string_one_to_three_months), it))
                    }
                    data.mAlbums[KeyMoveLoader.IN_ONE_MONTH]?.let {
                        albums.add(ListSecond(context.getString(com.filemanager.common.R.string.string_one_month), it))
                    }
                    groupItems.add(ListPrimary(context.getString(com.filemanager.common.R.string.string_photos), albums))
                }
                groupItems.add(ListPrimary(context.getString(com.filemanager.common.R.string.string_videos), data.mVideos))
                groupItems.add(ListPrimary(context.getString(com.filemanager.common.R.string.string_audio), data.mMusics))
                groupItems.add(ListPrimary(context.getString(com.filemanager.common.R.string.string_documents), data.mDocuments))
                groupItems.add(ListPrimary(context.getString(com.filemanager.common.R.string.string_apk), data.mApplications))
                // Because of the bug of COUIExpandableRecyclerView, if click the last item, probabilistically
                // produce a same item in RecyclerView
                groupItems.add(ListPrimary("", ArrayList()))
                setRelativeDataChange(groupItems)
                mDataLoadState.postValue(OnLoaderListener.STATE_DONE)
            }
        }
    }

    fun getTotalSelectedSize(): Long {
        var size = 0L
        mUiState.value?.let {
            for (group in it.mGroupItems) {
                size += group.getSelectedSize()
            }
        }
        return size
    }

    fun onItemClick(activity: BaseVMActivity?, groupPosition: Int, childPosition: Int): Boolean {
        mUiState.value?.mChildItems?.get(groupPosition)?.get(childPosition)?.let {
            return when (it) {
                is ListSecond -> {
                    launch {
                        withContext(Dispatchers.Default) {
                            if ((it.mChild.isNotEmpty()) && (activity is AKeyToMoveActivity)) {
                                activity.switchToGalleryFragment(copyListSecond(it))
                            } else {
                                it.setChosenState(if (it.getChosenState() == Visitable.ALL_BE_CHOSEN) {
                                    Visitable.NOTHING_BE_CHOSEN
                                } else {
                                    Visitable.ALL_BE_CHOSEN
                                })
                                setListSecond(it)
                            }
                        }
                    }
                    true
                }
                is ItemChild -> {
                    launch {
                        withContext(Dispatchers.Default) {
                            it.setChosenState(if (it.getChosenState() == Visitable.ALL_BE_CHOSEN) {
                                Visitable.NOTHING_BE_CHOSEN
                            } else {
                                Visitable.ALL_BE_CHOSEN
                            })
                            setRelativeDataChange(listPrimary = ListPrimary(
                                    mUiState.value!!.mGroupItems[groupPosition].mTitle, mUiState.value!!.mChildItems[groupPosition]))
                        }
                    }
                    true
                }
                else -> false
            }
        }
        return false
    }

    fun onGroupCheckBoxClick(groupPosition: Int): Boolean {
        mUiState.value?.mGroupItems?.get(groupPosition)?.let {
            launch {
                withContext(Dispatchers.Default) {
                    val checkState = it.getChosenState()
                    it.setChosenState(
                            if (checkState == COUICheckBox.SELECT_ALL) {
                                Visitable.NOTHING_BE_CHOSEN
                            } else {
                                Visitable.ALL_BE_CHOSEN
                            })
                    setRelativeDataChange(mUiState.value?.mGroupItems)
                }
            }
            return true
        }
        return false
    }

    fun onChildCheckBoxClick(groupPosition: Int, childPosition: Int): Boolean {
        mUiState.value?.mChildItems?.get(groupPosition)?.get(childPosition)?.let {
            launch {
                withContext(Dispatchers.Default) {
                    val checkState = it.getChosenState()
                    it.setChosenState(
                            if (checkState == COUICheckBox.SELECT_ALL) {
                                Visitable.NOTHING_BE_CHOSEN
                            } else {
                                Visitable.ALL_BE_CHOSEN
                            })
                    setRelativeDataChange(listPrimary = ListPrimary(
                            mUiState.value!!.mGroupItems[groupPosition].mTitle, mUiState.value!!.mChildItems[groupPosition]))
                }
            }
            return true
        }
        return false
    }

    fun setListSecond(listSecond: ListSecond) {
        val albums = ArrayList<ListSecond>()
        mUiState.value?.mChildItems?.get(0)?.let {
            launch {
                withContext(Dispatchers.Default) {
                    for (child in it) {
                        if (child is ListSecond) {
                            if (child.mTitle == listSecond.mTitle) {
                                albums.add(listSecond)
                            } else {
                                albums.add(child)
                            }
                        }
                    }
                    val listPrimary = ListPrimary(mUiState.value!!.mGroupItems[0].mTitle, albums)
                    setRelativeDataChange(listPrimary = listPrimary)
                }
            }
        }
    }

    @WorkerThread
    private fun copyListSecond(listSecond: ListSecond): ListSecond {
        val child = ArrayList<ItemChild>()
        for (item in listSecond.mChild) {
            val temp = item.copy(mFile = item.mFile)
            temp.setChosenState(item.getChosenState())
            child.add(temp)
        }
        return listSecond.copy(mTitle = listSecond.mTitle, mChild = child)
    }

    private fun setRelativeDataChange(group: ArrayList<ListPrimary>? = null,
                                      listPrimary: ListPrimary? = null) {
        group?.let {
            val childItems = ArrayList<ArrayList<out Visitable>>()
            for (i in it.indices) {
                childItems.add(it[i].mChild)
            }
            mUiState.postValue(KeyMoveUiModel(it, childItems))
            return
        }
        listPrimary?.let {
            mUiState.value?.mGroupItems?.let {
                val groupItems = ArrayList<ListPrimary>()
                for (groupItem in it) {
                    if (listPrimary.mTitle == groupItem.mTitle) {
                        groupItems.add(listPrimary)
                    } else {
                        groupItems.add(groupItem)
                    }
                }
                setRelativeDataChange(groupItems)
                return
            }
        }
    }

    private fun getPhotosSlimming(): Boolean {
        var result = ILLEGAL_RESULT
        try {
            result = queryCloudSwitchStatus(MyApplication.sAppContext, MODULE_GALLERY_SLIMMING, KEY_GALLERY_SLIMMING)
        } catch (e: Exception) {
            Log.e(TAG, "getPhotosSlimming(): ${e.message}")
        }
        return result == SWITCH_ON_RESULT
    }

    /**
     * Selection Cloud Service Switch Status
     *
     * @param module moduleName
     * @param key    switchName
     * @return switch status, default value -1 , status on return 1 , status off return 0
     */
    private fun queryCloudSwitchStatus(context: Context?, module: String, key: String): Int {
        Log.d(TAG, "queryCloudSwitchStatus module : $module  key : $key")
        var status = ILLEGAL_RESULT
        if (null == context || TextUtils.isEmpty(key)) {
            Log.e(TAG, "queryCloudSwitchStatus context null or key null")
            return status
        }
        val uri = Uri.parse(CONTENT_AUTHORITY_CLOUD_SLASH) ?: return status
        val params = Bundle()
        params.putString(KEY_MODULE, module)
        params.putString(KEY_KEY, key)
        val contentResolver = context.contentResolver
        val bundle: Bundle? = try {
            contentResolver.call(uri, METHOD_GALLERY_SLIMMING, null, params)
        } catch (ex: Exception) {
            Log.e(TAG, "queryCloudSwitchStatus query error : " + ex.message)
            return status
        }
        if (null != bundle) {
            status = bundle.getInt(key)
        }
        Log.d(TAG, "queryCloudSwitchStatus value : $status")
        return status
    }

    data class KeyMoveUiModel(
            val mGroupItems: ArrayList<ListPrimary>,
            val mChildItems: ArrayList<ArrayList<out Visitable>>
    )
}