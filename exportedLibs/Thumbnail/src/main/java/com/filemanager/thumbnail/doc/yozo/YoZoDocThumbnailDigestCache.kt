/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - YoZoDocThumbnailCache.kt
 * Description:
 *     Cache doc thumbnail results and reuse for same contents doc files.
 *
 * Version: 1.0
 * Date: 2024-08-09
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Bixia<PERSON>.<EMAIL>    2024-08-09   1.0    Create this module
 *********************************************************************************/
package com.filemanager.thumbnail.doc.yozo

import android.content.Context
import android.net.Uri
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.doc.DocThumbnail
import com.filemanager.thumbnail.utils.Log
import java.io.IOException
import java.io.InputStream
import java.security.MessageDigest
import java.util.Collections
import java.util.WeakHashMap
import kotlin.jvm.Throws

internal class YoZoDocThumbnailDigestCache(
    initDocDigestCache: MutableMap<Uri, String> = WeakHashMap(),
    initDocLastModifiedCache: MutableMap<Uri, Long> = WeakHashMap(),
    initThumbnailDigestCache: MutableMap<String, Uri> = WeakHashMap()
) {
    private val enabled = ThumbnailManager.YoZoDocConfigs.enableDocDigestCache
    private val algorithm = ThumbnailManager.YoZoDocConfigs.docDigestAlgorithm
    private val maxDocSize = ThumbnailManager.YoZoDocConfigs.maxDigestDocSize
    private val docDigestCache = Collections.synchronizedMap(initDocDigestCache)
    private val docLastModifiedCache = Collections.synchronizedMap(initDocLastModifiedCache)
    private val thumbnailDigestCache = Collections.synchronizedMap(initThumbnailDigestCache)

    fun pickThumbnailCache(context: Context, doc: DocThumbnail, appendDigest: Boolean = false): Uri? {
        if (!enabled) {
            return null
        }
        var digestKey = getDocDigest(context, doc, true) ?: return null
        if (appendDigest) {
            digestKey = digestKey.plus(USE_EXPORT_FIRST_PAGE_APPEND_DIGEST)
        }
        return thumbnailDigestCache[digestKey]
    }

    fun putThumbnailCache(context: Context, doc: DocThumbnail, thumbnailUri: Uri, appendDigest: Boolean = false) {
        if (!enabled) {
            return
        }
        var digestKey = getDocDigest(context, doc, false) ?: return
        if (appendDigest) {
            digestKey = digestKey.plus(USE_EXPORT_FIRST_PAGE_APPEND_DIGEST)
        }
        thumbnailDigestCache[digestKey] = thumbnailUri
    }

    private fun getDocDigest(context: Context, doc: DocThumbnail, printLog: Boolean): String? {
        if (doc.size >= maxDocSize) {
            if (printLog) {
                Log.d(TAG, "getDocDigest: too large size: ${doc.printString()}")
            }
            return null
        }
        val docUri = doc.uri
        val lastModified = doc.lastModified
        val cachedLastModified = docLastModifiedCache[docUri]
        if (lastModified == cachedLastModified) {
            docDigestCache[docUri].takeUnless { it.isNullOrEmpty() }?.let { return it }
        } else if (printLog && (cachedLastModified != null)) {
            Log.d(TAG, "getDocDigest: doc is modified: ${doc.printString()}")
        }
        val digestKey = readDocDigest(context, docUri) ?: return null
        docDigestCache[docUri] = digestKey
        docLastModifiedCache[docUri] = lastModified
        return digestKey
    }

    @OptIn(ExperimentalStdlibApi::class)
    private fun readDocDigest(context: Context, docUri: Uri): String? {
        val digestImpl = kotlin.runCatching {
            MessageDigest.getInstance(algorithm)
        }.onFailure {
            Log.e(TAG, "readDocDigest: ERROR! $it")
        }.getOrNull() ?: return null
        kotlin.runCatching {
            context.contentResolver.openInputStream(docUri)?.use {
                it.readAllBytesIntoDigest(digestImpl)
                val digestBytes = digestImpl.digest()
                return digestBytes.toHexString()
            }
        }.onFailure {
            Log.e(TAG, "readDocDigest: ERROR! $it")
        }
        return null
    }

    @Throws(IOException::class)
    private fun InputStream.readAllBytesIntoDigest(digestImpl: MessageDigest) {
        val buffer = ByteArray(READ_FILE_BUFFER_SIZE)
        var readBytes = 0
        while (read(buffer).also { readBytes = it } != -1) {
            digestImpl.update(buffer, 0, readBytes)
        }
    }

    private companion object {
        private const val TAG = "YoZoDocThumbnailDigestCache"
        private const val READ_FILE_BUFFER_SIZE = 16384
        private const val USE_EXPORT_FIRST_PAGE_APPEND_DIGEST = "FIRST_PAGE_DIGEST"
    }
}