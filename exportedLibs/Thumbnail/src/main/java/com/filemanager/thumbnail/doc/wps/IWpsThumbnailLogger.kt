/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IWpsThumbnailLogger.kt
 * Description:
 *     The logger for the loader of WPS thumbnail
 *
 * Version: 1.0
 * Date: 2024-08-27
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Bixia<PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-08-27   1.0    Create this module
 *********************************************************************************/
package com.filemanager.thumbnail.doc.wps

import androidx.annotation.Keep

@Keep
interface IWpsThumbnailLogger {

    /**
     * Whether log is enabled
     */
    val isEnabled: Boolean

    /**
     * Output log with level V
     *
     * @param tag Log tag
     * @param msg log msg
     */
    fun v(tag: String, msg: String)

    /**
     * Output log with level D
     *
     * @param tag Log tag
     * @param msg log msg
     */
    fun d(tag: String, msg: String)

    /**
     * Output log with level I
     *
     * @param tag Log tag
     * @param msg log msg
     */
    fun i(tag: String, msg: String)

    /**
     * Output log with level W
     *
     * @param tag Log tag
     * @param msg log msg
     * @param tr throwable in log
     */
    fun w(tag: String, msg: String, tr: Throwable? = null)

    /**
     * Output log with level E
     *
     * @param tag Log tag
     * @param msg log msg
     * @param tr throwable in log
     */
    fun e(tag: String, msg: String, tr: Throwable? = null)
}