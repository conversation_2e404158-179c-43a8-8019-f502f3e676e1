package com.oplus.filemanager.compat.brand.realme

import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.AudioFileWrapper
import com.filemanager.common.wrapper.MediaFileWrapper
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.util.ArrayList
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * MultiFmAdEntityItem 的单元测试类
 * 用于测试 MultiFmAdEntityItem 类的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class MultiFmAdEntityItemTest {

    // 测试类中使用的成员变量
    private lateinit var multiFmAdEntityItem: MultiFmAdEntityItem
    private lateinit var mockAdvertEntry: AdvertEntry
    private lateinit var mockAdapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>
    private lateinit var fileList: ArrayList<MediaFileWrapper>
    private lateinit var mockMediaFileWrapper: MediaFileWrapper
    private lateinit var mockAudioFileWrapper: AudioFileWrapper

    /**
     * 测试前的初始化方法
     * 用于创建测试所需的模拟对象和初始化测试环境
     */
    @Before
    fun setUp() {
        // 创建模拟对象
        mockAdvertEntry = mockk(relaxed = true)
        mockAdapter = mockk(relaxed = true)
        fileList = ArrayList()
        mockMediaFileWrapper = mockk(relaxed = true)
        mockAudioFileWrapper = mockk(relaxed = true)
        
        // 模拟静态方法 Utils.isRealmePhone() 返回 false
        mockkStatic(Utils::class)
        every { Utils.isRealmePhone() } returns false
    }

    /**
     * 测试后的清理方法
     * 用于释放资源和清理测试环境
     */
    @After
    fun tearDown() {
        multiFmAdEntityItem.onDestroy()
    }

    /**
     * 测试构造函数的功能
     * 验证构造函数是否正确初始化了所有字段
     */
    @Test
    fun testConstructor() {
        multiFmAdEntityItem = MultiFmAdEntityItem(
            mockAdvertEntry, 
            "testName", 
            mockAdapter, 
            fileList, 
            false
        )
        
        // 验证各字段是否被正确初始化
        assertEquals(mockAdvertEntry, multiFmAdEntityItem.mAdvertEntry)
        assertEquals("testName", multiFmAdEntityItem.mName)
        assertEquals(mockAdapter, multiFmAdEntityItem.mAdapter)
        assertEquals(fileList, multiFmAdEntityItem.mFileList)
        assertFalse(multiFmAdEntityItem.mNeedHideAD)
    }

    /**
     * 测试 onDestroy 方法的功能
     * 验证 onDestroy 方法是否正确释放了所有资源
     */
    @Test
    fun testOnDestroy() {
        multiFmAdEntityItem = MultiFmAdEntityItem(
            mockAdvertEntry, 
            "testName", 
            mockAdapter, 
            fileList, 
            false
        )
        
        multiFmAdEntityItem.onDestroy()
        
        // 验证 destroyAd 方法被调用
        verify { mockAdvertEntry.destroyAd() }
        // 验证各字段是否被置为 null
        assertNull(multiFmAdEntityItem.mAdvertEntry)
        assertNull(multiFmAdEntityItem.mName)
        assertNull(multiFmAdEntityItem.mFileList)
        assertNull(multiFmAdEntityItem.mAdapter)
    }

    /**
     * 测试 onLoadSuccess 方法的功能
     * 验证广告加载成功时是否正确设置了广告类型
     */
    @Test
    fun testOnLoadSuccess() {
        multiFmAdEntityItem = MultiFmAdEntityItem(
            mockAdvertEntry, 
            "testName", 
            mockAdapter, 
            fileList, 
            false
        )
        
        // 设置模拟对象的返回值
        every { mockAudioFileWrapper.mFileWrapperViewType } returns BaseFileBean.TYPE_FILE_AD
        multiFmAdEntityItem.onLoadSuccess()
        
        // 验证广告类型是否正确设置
        assertEquals(BaseFileBean.TYPE_FILE_AD, mockAudioFileWrapper.mFileWrapperViewType)
    }

    /**
     * 测试 dismiss 方法的功能
     * 验证广告关闭时是否正确清空了文件列表
     */
    @Test
    fun testDismiss() {
        multiFmAdEntityItem = MultiFmAdEntityItem(
            mockAdvertEntry, 
            "testName", 
            mockAdapter, 
            fileList, 
            false
        )
        
        multiFmAdEntityItem.dismiss()
        
        // 验证文件列表是否为空
        assertTrue(fileList.isEmpty())
    }

    /**
     * 测试 setNeedHideAD 方法的功能
     * 验证设置隐藏广告标志是否生效
     */
    @Test
    fun testSetNeedHideAD() {
        multiFmAdEntityItem = MultiFmAdEntityItem(
            mockAdvertEntry, 
            "testName", 
            mockAdapter, 
            fileList, 
            false
        )
        
        multiFmAdEntityItem.setNeedHideAD(true)
        
        // 验证隐藏广告标志是否被正确设置
        assertTrue(multiFmAdEntityItem.mNeedHideAD)
    }
}