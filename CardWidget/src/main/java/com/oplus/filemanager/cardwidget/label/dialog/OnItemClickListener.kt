/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OnItemClickListener
 ** Description : item 点击事件的回调
 ** Version     : 1.0
 ** Date        : 2022/11/30
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue      2022/11/30      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.dialog

import android.view.View

interface OnItemClickListener<T> {

    fun onItemClick(view: View, position: Int, data: T)

}