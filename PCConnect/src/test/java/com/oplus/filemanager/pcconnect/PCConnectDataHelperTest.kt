/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PCConnectDataHelperTest
 * * Description: unit test for PCConnectDataHelper
 * * Version: 1.0
 * * Date : 2022/1/17
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>              <data>      <version >        <desc>
 * * <EMAIL>    2022/1/17       1.0         unit test for PCConnectDataHelper
 ****************************************************************/
package com.oplus.filemanager.pcconnect

import android.view.MotionEvent
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.interfaces.OnGetUIInfoListener
import com.filemanager.common.utils.Log
import com.oplus.cast.service.sdk.util.CommonUtil
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module

class PCConnectDataHelperTest {

    private val recycleBin = mockk<IRecycleBin>()
    private val mainAction = mockk<IMain>()
    private val categoryGlobalSearchApi = mockk<ICategoryGlobalSearchApi>()

    private val koinApp = koinApplication {
        modules(module {
            single { recycleBin }
            single { mainAction }
            single { categoryGlobalSearchApi }
        })
    }

    @Before
    fun setUp() {
        startKoin(koinApp)
    }

    @After
    fun teardown() {
        stopKoin()
    }


    @Test
    fun `should return true touch from pad when check motion is true and is screen is true and device is PAD_UIBC`() {
        val checkMotion = true
        val isScreenCast = true
        val result = true
        testFromPad(checkMotion, PCConnectDataHelper.PAD_UIBC, isScreenCast, result)
    }

    @Test
    fun `should return false touch from pad when check motion is false and is screen is true and device is PAD_UIBC`() {
        val checkMotion = false
        val isScreenCast = true
        val result = false
        testFromPad(checkMotion, PCConnectDataHelper.PAD_UIBC, isScreenCast, result)
    }

    @Test
    fun `should return false touch from pad when check motion is true and is screen is false and device is PAD_UIBC`() {
        val checkMotion = true
        val isScreenCast = false
        val result = false
        testFromPad(checkMotion, PCConnectDataHelper.PAD_UIBC, isScreenCast, result)
    }

    @Test
    fun `should return true touch from synergy when check motion is true and is screen is false and device is not PAD_UIBC`() {
        val checkMotion = true
        val isScreenCast = true
        val result = true
        testFromPad(checkMotion, "", isScreenCast, result)
    }

    private fun testFromPad(
        checkMotion: Boolean,
        device: String,
        isScreenCast: Boolean,
        result: Boolean
    ) {
        testFrom(checkMotion, device, isScreenCast, result, true)
    }

    @Test
    fun `should return true touch from pc when check motion is true and is screen is true and device is PC_UIBC`() {
        val checkMotion = true
        val isScreenCast = true
        val result = true
        testFromPC(checkMotion, PCConnectDataHelper.PC_UIBC, isScreenCast, result)
    }

    @Test
    fun `should return false touch from pc when check motion is false and is screen is true and device is PC_UIBC`() {
        val checkMotion = false
        val isScreenCast = true
        val result = false
        testFromPC(checkMotion, PCConnectDataHelper.PC_UIBC, isScreenCast, result)
    }

    @Test
    fun `should return false touch from pc when check motion is true and is screen is false and device is PC_UIBC`() {
        val checkMotion = true
        val isScreenCast = false
        val result = false
        testFromPC(checkMotion, PCConnectDataHelper.PC_UIBC, isScreenCast, result)
    }

    @Test
    fun `should return false touch from pc when check motion is true and is screen is false and device is not PC_UIBC`() {
        val checkMotion = true
        val isScreenCast = true
        val result = false
        testFromPC(checkMotion, "", isScreenCast, result)
    }

    private fun testFromPC(
        checkMotion: Boolean,
        device: String,
        isScreenCast: Boolean,
        result: Boolean
    ) {
        testFrom(checkMotion, device, isScreenCast, result, false)
    }

    private fun testFrom(
        checkMotion: Boolean,
        device: String,
        isScreenCast: Boolean,
        result: Boolean,
        isForPad: Boolean
    ) {
        //mock object
        val event = mockk<MotionEvent>()
        mockkObject(PCConnectController)
        mockkObject(MyApplication)
        mockkStatic(CommonUtil::class)
        mockkStatic(Log::class)
        every { appContext } returns mockk()
        every { PCConnectController.sInstance } returns mockk()
        every { Log.d(any(), any()) } returns Unit
        every { CommonUtil.getCastVersion(any()) } returns 1
        // when
        every { CommonUtil.checkMotionEvent(any(), event) }.returns(checkMotion)
        every { CommonUtil.getUIBCDevice(event) }.returns(device)
        every { PCConnectController.sInstance.isPCScreenCast() }.returns(isScreenCast)
        every { PCConnectController.sInstance.isSynergyEvent(any()) }.returns(checkMotion)
        //then
        if (isForPad) {
            assertEquals(result, PCConnectDataHelper.isTouchEventFromSynergy(event))
        } else {
            assertEquals(result, PCConnectDataHelper.isTouchEventFromPC(event))
        }
        unmockkObject(MyApplication)
        unmockkObject(PCConnectController)
    }

    @Test
    fun `should call right when call parseDataSource if isRecycleBinFragment`() {
        //given
        val owner = mockk<LifecycleOwner>()
        every { recycleBin.isRecycleBinFragment(owner) } returns true
        val pcConnectDataHelper = spyk(PCConnectDataHelper(), recordPrivateCalls = true)
        every { pcConnectDataHelper.setRecyclerViewOverScrollState() } just runs
        //when
        pcConnectDataHelper.parseDataSource(owner)
        //then
        Assert.assertTrue(pcConnectDataHelper.mViewModel == null)
        Assert.assertTrue(pcConnectDataHelper.mRecycleView == null)
        verify { pcConnectDataHelper.setRecyclerViewOverScrollState() }
    }

    @Test
    fun `should call right when call parseDataSource if isMainCategoryFragment`() {
        //given
        val owner = mockk<LifecycleOwner>()
        every { recycleBin.isRecycleBinFragment(owner) } returns false
        every { mainAction.isMainCategoryFragment(owner) } returns true
        val viewModel = mockk<ViewModel>()
        every { mainAction.getMainCategoryViewModel(owner) } returns viewModel
        val pcConnectDataHelper = spyk(PCConnectDataHelper(), recordPrivateCalls = true)
        every { pcConnectDataHelper.setRecyclerViewOverScrollState() } just runs
        //when
        pcConnectDataHelper.parseDataSource(owner)
        //then
        Assert.assertTrue(pcConnectDataHelper.mViewModel == viewModel)
        Assert.assertTrue(pcConnectDataHelper.mRecycleView == null)
        verify { pcConnectDataHelper.setRecyclerViewOverScrollState() }
    }

    @Test
    fun `should call right when call parseDataSource if isGlobalSearchFragment`() {
        //given
        val owner = mockk<LifecycleOwner>()
        every { recycleBin.isRecycleBinFragment(owner) } returns false
        every { mainAction.isMainCategoryFragment(owner) } returns false
        every { categoryGlobalSearchApi.isGlobalSearchFragment(owner) } returns true
        val viewModel = mockk<ViewModel>()
        every { categoryGlobalSearchApi.getGlobalSearchFragmentViewModel(owner, true) } returns viewModel
        val pcConnectDataHelper = spyk(PCConnectDataHelper(), recordPrivateCalls = true)
        every { pcConnectDataHelper.setRecyclerViewOverScrollState() } just runs
        //when
        pcConnectDataHelper.parseDataSource(owner)
        //then
        Assert.assertTrue(pcConnectDataHelper.mViewModel == viewModel)
        Assert.assertTrue(pcConnectDataHelper.mRecycleView == null)
        verify { pcConnectDataHelper.setRecyclerViewOverScrollState() }
    }

    @Test
    fun `should call right when call parseDataSource if OnGetUIInfoListener`() {
        //given
        val owner = TestClass()
        every { recycleBin.isRecycleBinFragment(owner) } returns false
        every { mainAction.isMainCategoryFragment(owner) } returns false
        every { categoryGlobalSearchApi.isGlobalSearchFragment(owner) } returns false
        val viewModel = mockk<ViewModel>()
        every { categoryGlobalSearchApi.getGlobalSearchFragmentViewModel(owner, false) } returns viewModel
        val pcConnectDataHelper = spyk(PCConnectDataHelper(), recordPrivateCalls = true)
        every { pcConnectDataHelper.setRecyclerViewOverScrollState() } just runs
        //when
        pcConnectDataHelper.parseDataSource(owner)
        //then
        Assert.assertTrue(pcConnectDataHelper.mViewModel != null)
        Assert.assertTrue(pcConnectDataHelper.mRecycleView != null)
        verify { pcConnectDataHelper.setRecyclerViewOverScrollState() }
    }

    @Test
    fun `should call right when call parseDataSource if null`() {
        //given
        val owner = null
        every { recycleBin.isRecycleBinFragment(owner) } returns false
        every { mainAction.isMainCategoryFragment(owner) } returns false
        every { categoryGlobalSearchApi.isGlobalSearchFragment(owner) } returns false
        val viewModel = mockk<ViewModel>()
        every { categoryGlobalSearchApi.getGlobalSearchFragmentViewModel(owner, false) } returns viewModel
        val pcConnectDataHelper = spyk(PCConnectDataHelper(), recordPrivateCalls = true)
        every { pcConnectDataHelper.setRecyclerViewOverScrollState() } just runs
        //when
        pcConnectDataHelper.parseDataSource(owner)
        //then
        Assert.assertTrue(pcConnectDataHelper.mViewModel == null)
        Assert.assertTrue(pcConnectDataHelper.mRecycleView == null)
        verify { pcConnectDataHelper.setRecyclerViewOverScrollState() }
    }

    class TestClass : OnGetUIInfoListener, LifecycleOwner {

        override fun getViewModel(): ViewModel {
            return mockk<ViewModel>()
        }

        override fun getRecyclerView(): RecyclerView {
            return mockk()
        }

        override val lifecycle: Lifecycle
            get() = mockk()
    }
}