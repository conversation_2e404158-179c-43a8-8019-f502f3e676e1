/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AlbumFilesService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/26 9:57
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/26       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist

import android.content.Intent
import android.os.IBinder
import android.os.Messenger
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Log
import com.oplus.fileservice.filelist.scanner.AlbumFilesScanner
import com.oplus.fileservice.filelist.scanner.BaseScanner
import com.filemanager.common.utils.HansFreezeManager
import com.oplus.fileservice.utils.WebFileConstant

class AlbumFilesService : BaseService() {
    companion object {
        const val TAG = "AlbumFilesService"
    }

    private var mFileListServiceHandler: FileListServiceHandler? = null

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "onCreate()")
    }

    override fun onBind(intent: Intent): IBinder? {
        Log.d(
            TAG,
            "onBind() getAction = ${intent.action} , getExtras =${intent.extras}"
        )
        HansFreezeManager.instance.keepBackgroundRunning()
        val extras = intent.extras
        extras?.apply {
            mPageNo = getInt(PAGE_NO, WebFileConstant.DEFAULT_PAGE_NO)
            mPageSize = getInt(PAGE_SIZE, WebFileConstant.DEFAULT_PAGE_SIZE)
            mSortOrder = getInt(SORT_ORDER, SortHelper.FILE_NAME_ORDER)
            isNeedThumbnail = getBoolean(NEED_THUMBNAIL, DEFAULT_NEED_THUMBNAIL)
        }
        Log.d(TAG, "mPageNo = $mPageNo, mPageSize = $mPageSize, mSortOrder =$mSortOrder isNeedThumbnail:$isNeedThumbnail")
        val albumFilesScanner: BaseScanner = AlbumFilesScanner(mPageNo, mPageSize, mSortOrder, isNeedThumbnail)
        mFileListServiceHandler = FileListServiceHandler(albumFilesScanner)
        return Messenger(mFileListServiceHandler).binder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Log.d(TAG, "onUnbind")
        HansFreezeManager.instance.cancelFrozenDelay()
        return super.onUnbind(intent)
    }
}