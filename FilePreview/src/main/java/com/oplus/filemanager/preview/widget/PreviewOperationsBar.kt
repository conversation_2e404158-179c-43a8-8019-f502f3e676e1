/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - PreviewOperationsBar.kt
 * Description:
 *     The operations bar widget on file preview page.
 *
 * Version: 1.0
 * Date: 2024-09-04
 * Author: Bixia<PERSON>.<EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-09-04   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.WindowInsets
import android.widget.LinearLayout
import androidx.annotation.MenuRes
import androidx.annotation.StringRes
import androidx.appcompat.view.menu.MenuBuilder
import androidx.appcompat.widget.AppCompatImageView
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.poplist.COUIContextMenu
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SystemBarUtils
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.preview.R

@SuppressLint("RestrictedApi")
internal class PreviewOperationsBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : LinearLayout(context, attrs) {

    private companion object {
        private const val TAG = "PreviewOperationsBar"

        private val androidMarginResArr = intArrayOf(
            android.R.attr.layout_margin,
            android.R.attr.layout_marginStart,
            android.R.attr.layout_marginLeft,
            android.R.attr.layout_marginEnd,
            android.R.attr.layout_marginRight,
            android.R.attr.layout_marginHorizontal
        )
        private val layoutMarginIndex = androidMarginResArr.indexOf(android.R.attr.layout_margin)
        private val layoutMarginStartIndex =
            androidMarginResArr.indexOf(android.R.attr.layout_marginStart)
        private val layoutMarginLeftIndex =
            androidMarginResArr.indexOf(android.R.attr.layout_marginLeft)
        private val layoutMarginEndIndex =
            androidMarginResArr.indexOf(android.R.attr.layout_marginEnd)
        private val layoutMarginRightIndex =
            androidMarginResArr.indexOf(android.R.attr.layout_marginRight)
        private val layoutMarginHorizontalIndex =
            androidMarginResArr.indexOf(android.R.attr.layout_marginHorizontal)
    }

    private val labelButton: AppCompatImageView
    private val sendButton: AppCompatImageView
    private val moreButton: AppCompatImageView
    private val moreMenu: MoreOperationsMenu
    private val openButton: COUIButton
    private val dividerLine: View
    private val barContentLayout: ViewGroup
    private val extendNavGestureMargin: View

    private var clickLabelListener: OnClickButtonListener? = null
    private var clickSendListener: OnClickButtonListener? = null
    private var clickMoreMenuListener: OnClickMoreMenuItemListener? = null
    private var clickOpenListener: OnClickButtonListener? = null
    private var onWinInsetsChanged: OnWindowInsetsChangedListener? = null
    private var isNavGesture: Boolean? = null
    private var requestUpdateLayout = true
    private var isDividerEnabled = false
    private var initMarginStart = 0
    private var initMarginEnd = 0

    init {
        LayoutInflater.from(context).inflate(R.layout.widget_preview_operations_bar, this)
        orientation = VERTICAL
        labelButton = findViewById(R.id.preview_operation_label)
        labelButton.setOnClickListener {
            clickLabelListener?.onButtonClick(it)
        }
        sendButton = findViewById(R.id.preview_operation_send)
        sendButton.setOnClickListener {
            clickSendListener?.onButtonClick(it)
        }
        moreButton = findViewById(R.id.preview_operation_more)
        moreMenu = MoreOperationsMenu(context, moreButton)
        moreMenu.popup.apply {
            setOnItemClickListener { _, _, position, _ -> onMoreMenuItemClick(position) }
        }
        moreButton.setOnClickListener {
            moreMenu.popup.show()
        }
        openButton = findViewById(R.id.preview_open_button)
        openButton.setOnClickListener {
            clickOpenListener?.onButtonClick(it)
        }
        dividerLine = findViewById(R.id.preview_operations_divider)
        barContentLayout = findViewById(R.id.preview_operations_bar_layout)
        extendNavGestureMargin = findViewById(R.id.preview_operations_extend_nav_gesture_margin)
        applyPreviewAttrs(context, attrs)
    }

    private fun applyPreviewAttrs(context: Context, attrs: AttributeSet?) {
        attrs ?: return
        val typedArr = context.obtainStyledAttributes(attrs, R.styleable.PreviewOperationsBar)
        isDividerEnabled =
            typedArr.getBoolean(R.styleable.PreviewOperationsBar_previewEnableDivider, false)
        typedArr.recycle()
        if (isDividerEnabled) {
            dividerLine.visibility = INVISIBLE
        } else {
            dividerLine.visibility = GONE
        }
        val isRtl = Utils.isRtl()
        val androidTypedArr = context.obtainStyledAttributes(attrs, androidMarginResArr)
        initMarginStart = androidTypedArr.getDimensionPixelOffset(
            layoutMarginStartIndex,
            androidTypedArr.getDimensionPixelOffset(
                if (isRtl) layoutMarginRightIndex else layoutMarginLeftIndex,
                androidTypedArr.getDimensionPixelOffset(
                    layoutMarginHorizontalIndex,
                    androidTypedArr.getDimensionPixelOffset(layoutMarginIndex, 0)
                )
            )
        )
        initMarginEnd = androidTypedArr.getDimensionPixelOffset(
            layoutMarginEndIndex,
            androidTypedArr.getDimensionPixelOffset(
                if (isRtl) layoutMarginLeftIndex else layoutMarginRightIndex,
                androidTypedArr.getDimensionPixelOffset(
                    layoutMarginHorizontalIndex,
                    androidTypedArr.getDimensionPixelOffset(layoutMarginIndex, 0)
                )
            )
        )
        androidTypedArr.recycle()
    }

    private fun COUIPopupListWindow.onMoreMenuItemClick(position: Int) {
        val menuItem = moreMenu.menu?.getItem(position) ?: return
        Log.d(TAG, "onMoreMenuItemClick: position=$position, menuItem=$menuItem")
        clickMoreMenuListener?.onMoreMenuItemClick(menuItem)
        if (isShowing) {
            dismiss()
        }
    }

    fun setOpenButtonText(@StringRes textRes: Int): PreviewOperationsBar = apply {
        openButton.setText(textRes)
    }

    fun setOpenButtonText(text: CharSequence): PreviewOperationsBar = apply {
        openButton.text = text
    }

    fun inflateMoreMenu(@MenuRes menuRes: Int, removeItemIds: Collection<Int>) {
        val menuBuilder = MenuBuilder(context)
        MenuInflater(context).inflate(menuRes, menuBuilder)
        removeItemIds.forEach {
            menuBuilder.removeItem(it)
        }
        moreMenu.registerForContextMenu(moreButton, menuBuilder)
    }

    fun setOnClickLabelButtonListener(
        listener: OnClickButtonListener
    ): PreviewOperationsBar = apply {
        clickLabelListener = listener
    }

    fun setOnClickSendButtonListener(
        listener: OnClickButtonListener
    ): PreviewOperationsBar = apply {
        clickSendListener = listener
    }

    fun setOnClickMoreMenuItemListener(
        listener: OnClickMoreMenuItemListener
    ): PreviewOperationsBar = apply {
        clickMoreMenuListener = listener
    }

    fun setOnClickOpenButtonListener(
        listener: OnClickButtonListener
    ): PreviewOperationsBar = apply {
        clickOpenListener = listener
    }

    fun setOnWindowInsetsChangedListener(
        listener: OnWindowInsetsChangedListener
    ): PreviewOperationsBar = apply {
        onWinInsetsChanged = listener
    }

    fun release() {
        clickLabelListener = null
        clickSendListener = null
        clickMoreMenuListener = null
        clickOpenListener = null
        onWinInsetsChanged = null
    }

    fun getSendButtonRect(): Rect = ViewHelper.getViewRect(sendButton)

    fun setDividerVisible(isVisible: Boolean, requestLayout: Boolean = true) {
        try {
            if (!isDividerEnabled) {
                dividerLine.visibility = GONE
                return
            }
            dividerLine.visibility = if (isVisible) VISIBLE else INVISIBLE
        } finally {
            if (requestLayout) {
                requestUpdateLayout = true
                invalidate()
            }
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        if (requestUpdateLayout) {
            layoutNavigationGestureSpace()
            layoutDividerLine()
        }
        requestUpdateLayout = false
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    }

    private fun layoutDividerLine() {
        if (!isDividerEnabled) {
            return
        }
        val parentView = parent as? ViewGroup ?: return
        val parentPaddingStart = parentView.paddingStart
        val parentPaddingEnd = parentView.paddingEnd
        val extendStart = parentPaddingStart + initMarginStart
        val extendEnd = parentPaddingEnd + initMarginEnd
        extendMargin(parentPaddingStart, parentPaddingEnd)
        barContentLayout.extendMargin(-extendStart, -extendEnd)
        dividerLine.extendMargin(extendStart, extendEnd)
    }

    private fun layoutNavigationGestureSpace() {
        val isGesture = isNavigationGesture()
        extendNavGestureMargin.visibility = if (isGesture) VISIBLE else GONE
    }

    private fun isNavigationGesture(): Boolean {
        isNavGesture?.let { return it }
        val isGesture = loadNavigationGesture()
        return isGesture
    }

    private fun loadNavigationGesture(): Boolean {
        val isGesture = isNavGestureWithoutTaskBar()
        isNavGesture = isGesture
        return isGesture
    }

    private fun isNavGestureWithoutTaskBar(): Boolean {
        val isGestureMode = SystemBarUtils.isSystemNavigationGestureMode(context)
        if (!isGestureMode) {
            return false
        }
        return !SystemBarUtils.isTaskBarShowing(this)
    }

    fun checkNavigationGestureSpace() {
        loadNavigationGesture()
        requestUpdateLayout = true
        invalidate()
    }

    override fun onApplyWindowInsets(insets: WindowInsets?): WindowInsets {
        checkNavigationGestureSpace()
        onWinInsetsChanged?.onWindowInsetsChanged(insets)
        return super.onApplyWindowInsets(insets)
    }

    private fun View.extendMargin(extendStart: Int, extendEnd: Int) {
        val lp = layoutParams as? MarginLayoutParams ?: return
        lp.marginStart = -extendStart
        lp.marginEnd = -extendEnd
        layoutParams = lp
    }

    fun interface OnClickButtonListener {
        fun onButtonClick(button: View)
    }

    fun interface OnClickMoreMenuItemListener {
        fun onMoreMenuItemClick(item: MenuItem)
    }

    fun interface OnWindowInsetsChangedListener {
        fun onWindowInsetsChanged(insets: WindowInsets?)
    }

    private class MoreOperationsMenu(context: Context, anchor: View) {
        val popup: COUIPopupListWindow
            get() = contextMenu.popupListWindow

        private val contextMenu: COUIContextMenu = COUIContextMenu(context, anchor)

        var menu: MenuBuilder? = null
            private set

        fun registerForContextMenu(moreButton: View, menuBuilder: MenuBuilder) {
            menu = menuBuilder
            contextMenu.registerForContextMenu(moreButton, menuBuilder)
            moreButton.isLongClickable = false
        }
    }
}