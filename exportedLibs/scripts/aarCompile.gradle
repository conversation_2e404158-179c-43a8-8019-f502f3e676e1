final isLibModule = project.plugins.hasPlugin("com.android.library")
if (!isLibModule) {
    final errMsg = "${project.path}: aarCompile.gradle must be applied to an " +
            "Android library module. Please apply com.android.library first."
    System.err.println(errMsg)
    throw new GradleException(errMsg)
}
final hasOBuildPlugin = project.plugins.hasPlugin("obuildplugin")
if (!hasOBuildPlugin) {
    final errMsg = "${project.path}: pipelinePublish.gradle is depended on obuildplugin, " +
            "please apply obuildplugin and configure OBuildConfig first"
    System.err.println(errMsg)
    throw new GradleException(errMsg)
}

def readGitHeadHash() {
    final gitRoot = rootProject.file(".git")
    if (!gitRoot.exists() || !gitRoot.isDirectory()) {
        return null
    }
    final gitHead = new File(gitRoot, "HEAD")
    if (!gitHead.exists() || !gitHead.isFile()) {
        return null
    }
    final headRef = gitHead.text.readLines().first()
            .replace("ref:", "").replace(" ", "").replace("/", File.separator)
    final gitRef = new File(gitRoot, headRef)
    if (!gitRef.exists() || !gitRef.isFile()) {
        return null
    }
    final gitHash = gitRef.text.readLines().first()
    if (gitHash.isEmpty()) {
        return null
    }
    return gitHash.substring(0, 7)
}

def aarVersionSuffix = versionSuffix
if (aarVersionSuffix == "-alpha01") {
    final gitHash = readGitHeadHash()
    if (gitHash != null) {
        aarVersionSuffix = "-alpha$gitHash-SNAPSHOT"
    }
}

version = "$mainVersionName$aarVersionSuffix"

android {
    final confNameBuilder = new StringBuilder()
    flavorDimensions.eachWithIndex { String flavor, int i ->
        if (i == 0) {
            confNameBuilder.append(flavor)
        } else {
            confNameBuilder.append(flavor.capitalize())
        }
    }
    def publishConf
    if (confNameBuilder.length() == 0) {
        publishConf = "release"
    } else {
        publishConf = "${confNameBuilder.toString()}Release"
    }
    publishing {
        singleVariant(publishConf)
    }
}

android {
    final outputBaseName = "${OBuildConfig.groupId}-${OBuildConfig.sdkArtifactId}"
    libraryVariants.configureEach { variant ->
        variant.outputs.configureEach { output ->
            if (output.outputFileName.endsWith(".aar")) {
                final variantVersion = "${variant.buildType.name}-${defaultConfig.versionName}"
                output.outputFileName = "${outputBaseName}-${variantVersion}.aar"
            }
        }
    }
}

tasks.configureEach {
    if (it.name.toLowerCase().endsWith("sourcesjar")) {
        // 升级Gradle 8.2及AGP 8.2.2后，默认打包出了sources.jar，存在信息安全问题，需要禁用
        println("${project.path}: disbale sources jar task: ${it.name}")
        it.enabled = false
    }
}

/**
 * 升级Gradle 8.2及AGP 8.2.2后，默认打包出了sources.jar，存在信息安全问题。
 * 此处强制移除publication中的对应sources.jar的artifact，二重确保不会发布sources.jar
 */
afterEvaluate {
    publishing.publications.configureEach { pub ->
        if (pub instanceof MavenPublication) {
            def foundSourcesJar = null
            pub.artifacts.each {
                if (it.extension == "jar" && it.file.name.endsWith("-sources.jar")) {
                    foundSourcesJar = it
                }
            }
            if (foundSourcesJar != null) {
                println("${project.path}: remove sources jar artifact: ${foundSourcesJar.file.name}")
                pub.artifacts.remove(foundSourcesJar)
            }
        }
    }
}

apply from: rootProject.file("exportedLibs/scripts/aarMetadata.gradle")

aarMetadata {
    final INFO_FILE_NAME = "${OBuildConfig.groupId}_${OBuildConfig.sdkArtifactId}.version"

    metadata {
        "main" {
            configure(INFO_FILE_NAME) {
                put("versionName", "$mainVersionName")
                put("versionCode", "$mainVersionCode")
                put("versionCommit", "$prop_versionCommit")
                put("versionDate", "$prop_versionDate")
                put("artifactVersion", "$version")
            }
        }
        "debug" {
            value(INFO_FILE_NAME, "buildType", "debug")
        }
        "release" {
            value(INFO_FILE_NAME, "buildType", "release")
        }
    }

    deployMetadataConfigs()
}