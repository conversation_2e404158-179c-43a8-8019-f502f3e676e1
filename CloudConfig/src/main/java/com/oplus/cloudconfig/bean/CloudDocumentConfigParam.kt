/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudDocumentConfigParam
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/8 9:44
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/8       1.0      create
 ***********************************************************************/
package com.oplus.cloudconfig.bean

import androidx.annotation.Keep
import com.oplus.nearx.cloudconfig.anotation.FieldIndex

@Keep
data class CloudDocumentConfigParam(
    @FieldIndex(index = 1) val showTencentCloudDocument: Boolean = true,
    @FieldIndex(index = 2) val showKDocs: Boolean = true
)