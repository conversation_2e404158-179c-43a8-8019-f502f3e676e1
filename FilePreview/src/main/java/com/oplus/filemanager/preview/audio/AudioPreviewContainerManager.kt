/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AudioPreviewContainerManager.kt
 * Description:
 *     Manage container ui and loading ui for audio archive file
 *
 * Version: 1.0
 * Date: 2024-10-25
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-25   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.audio

import android.view.View
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.Log
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewContainerManager
import com.oplus.filemanager.preview.core.IPreviewContainerManager
import com.oplus.filemanager.preview.utils.PreviewScrollAreaHelper
import com.oplus.filemanager.preview.widget.PreviewFileIntroduceView

internal class AudioPreviewContainerManager(rootLayout: View) : AbsPreviewContainerManager(),
    IPreviewContainerManager {

    private companion object {
        private const val TAG = "AudioPreviewContainerManager"
    }

    override val defaultContainer: PreviewFileIntroduceView =
        rootLayout.findViewById(R.id.preview_audio_def_info)
    override val mDateTv: AppCompatTextView = rootLayout.findViewById(R.id.preview_remote_time_info)
    override val mSizeTv: AppCompatTextView = rootLayout.findViewById(R.id.preview_remote_size_info)
    override val nameContainer: TextViewSnippet =
        rootLayout.findViewById(R.id.preview_remote_title)
    private val audioCard = rootLayout.findViewById<View>(R.id.preview_audio_play_card)
    private val loadingLayout = rootLayout.findViewById<View>(R.id.loading_layout)
    private val scrollAreaHelper = PreviewScrollAreaHelper(
        scrollView = rootLayout.findViewById(R.id.preview_audio_scroll_area),
        operationsBar = rootLayout.findViewById(R.id.preview_operations_bar)
    )

    override fun showAsLoading() {
        super.showAsLoading()
        Log.d(TAG, "showAsLoading")
        audioCard.visibility = View.INVISIBLE
        defaultContainer.visibility = View.GONE
        loadingLayout.visibility = View.VISIBLE
        scrollAreaHelper.hideDivider()
    }

    override fun showAsFileContainer(isDefault: Boolean) {
        Log.d(TAG, "showAsDefaultContainer: $isDefault")
        loadingLayout.visibility = View.GONE
        if (isDefault) {
            defaultContainer.visibility = View.VISIBLE
            defaultContainer.setFilePathVisible(false)
            audioCard.visibility = View.INVISIBLE
            scrollAreaHelper.disableScrollViewportMinHeight()
            scrollAreaHelper.hideDivider()
        } else {
            defaultContainer.visibility = View.GONE
            audioCard.visibility = View.VISIBLE
            scrollAreaHelper.enableScrollViewportMinHeight()
            scrollAreaHelper.checkShowDivider()
        }
        super.showAsFileContainer(isDefault)
    }

    override fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        val lp = audioCard.layoutParams as? ConstraintLayout.LayoutParams ?: return
        val maxWidth = audioCard.resources.getDimensionPixelOffset(R.dimen.preview_audio_max_width)
        lp.matchConstraintMaxWidth = maxWidth
        audioCard.layoutParams = lp
        if (audioCard.visibility == View.VISIBLE) {
            scrollAreaHelper.checkShowDivider()
        }
    }
}