/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - StableIdKeyProvider.kt
 ** Description: map class between adapter position and key
 ** Version: 1.0
 ** Date : 2020/06/02
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  Jiafei.<PERSON>@Apps.FileManager      2020/06/02    1.0     create
 ****************************************************************/

package com.oplus.dropdrag.base

import androidx.annotation.Keep
import androidx.recyclerview.widget.RecyclerView
import com.oplus.dropdrag.SelectionTracker
import com.oplus.dropdrag.recycleview.ItemKeyProvider

@Keep
class DefaultKeyProvider(private val mRecyclerView: RecyclerView)
    : ItemKeyProvider<Int>() {

    override fun getKey(position: Int): Int? {
        if (position == RecyclerView.NO_POSITION) {
            return SelectionTracker.NO_INT_ITEM_ID
        }
        val adapter = mRecyclerView.adapter ?: return SelectionTracker.NO_INT_ITEM_ID
        return adapter.getItemId(position).toInt()
    }

    override fun getPosition(key: Int): Int {
        if (!SelectionTracker.isValidKey(key)) {
            return RecyclerView.NO_POSITION
        }

        val layoutManager = mRecyclerView.layoutManager ?: return RecyclerView.NO_POSITION
        val adapter = mRecyclerView.adapter ?: return RecyclerView.NO_POSITION
        val firstChild = mRecyclerView.getChildAt(0) ?: return RecyclerView.NO_POSITION
        val firstPosition = layoutManager.getPosition(firstChild)
        val childCount = mRecyclerView.layoutManager?.childCount ?: return RecyclerView.NO_POSITION
        var itemId : Int
        for (position in firstPosition until childCount) {
            itemId = adapter.getItemId(position).toInt()
            if (key == itemId) {
                return position
            }
        }
        return RecyclerView.NO_POSITION
    }
}
