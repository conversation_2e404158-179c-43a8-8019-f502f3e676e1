/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - DeleteOperation.java
 * Description: Operation for Remove file completely in recycle bin
 * Version: 1.0
 * Date : 2020/02/26
 * Author: <PERSON><PERSON><PERSON>.<PERSON>
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/02/26    1.0     create
 ****************************************************************/

package com.filemanager.recyclebin.operation

import android.app.Activity
import android.content.ContentUris
import android.content.ContentValues
import android.net.Uri
import android.text.TextUtils
import androidx.activity.ComponentActivity
import com.filemanager.common.wrapper.RecycleFileWrapper
import com.filemanager.common.wrapper.RecycleBinLiveData.RECYCLE_DELETE
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.RecycleStore
import com.filemanager.common.RecycleStore.Files.INTERNAL_CONTENT_URI
import com.filemanager.common.RecycleStore.Files.isInternalVolume
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.getAllFilePathFromDirectory
import com.filemanager.common.helper.FileWrapper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.recyclebin.operation.action.DoubleDeleteAction
import com.filemanager.recyclebin.operation.action.QueryBatchAction
import com.filemanager.recyclebin.operation.action.QueryBatchAction.Companion.QUERY_BATCH_ID_COUNT
import com.filemanager.recyclebin.retriever.DataRetriever
import com.filemanager.recyclebin.utils.RecycleBinUtils
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.tool.trackinglib.MediaType
import com.oplus.tool.trackinglib.OpTracker
import com.oplus.tool.trackinglib.OpTrackerMaker
import com.oplus.tool.trackinglib.OpType
import java.io.File

class EraseOperation(activity: ComponentActivity, listener: ProgressListener?)
    : BaseOperation<Activity>(activity, listener) {

    companion object {
        private const val TAG = "EraseOperation"
    }

    private val mEraseBulk: DoubleDeleteAction
    private var mUris: ArrayList<Uri>? = null
    private var mPaths: ArrayList<String>? = null
    private var mFiles: ArrayList<FileWrapper>? = null
    private var mFileBeans: ArrayList<BaseFileBean>? = null
    private val allFilePath = ArrayList<String>()
    private val allFilePathForLabels = ArrayList<String>()

    init {
        activity.runOnUiThread {
            activity.lifecycle.addObserver(this)
        }
        mEraseBulk = DoubleDeleteAction(mColumnName1 = RecycleStore.Files.FileColumns.RECYCLE_ID,
                mColumnName2 = RecycleStore.Files.FileColumns.RECYCLE_PATH)
    }

    override fun addPaths(paths: ArrayList<String>?): Boolean {
        throw UnsupportedOperationException()
    }

    override fun addFileBeans(files: ArrayList<BaseFileBean>?): Boolean {
        if (mFileBeans != null) {
            Log.w(TAG, "addFiles already add a task")
            return false
        }

        files?.let {
            mFileBeans = ArrayList()
            mFileBeans?.addAll(files)
            return true
        }
        return false
    }

    override fun addUris(uris: ArrayList<Uri>?): Boolean {
        if (mUris != null) {
            Log.w(TAG, "addUris already add a task")
            return false
        }

        uris?.let {
            mUris = ArrayList()
            mUris?.addAll(uris)
            return true
        }
        return false
    }

    override fun onTotalCountCallback(): Int {
        return when {
            mUris != null -> {
                mUris?.size ?: 0
            }
            mPaths != null -> {
                mPaths?.size ?: 0
            }
            mFiles != null -> {
                mFiles?.size ?: 0
            }
            mFileBeans != null -> {
                mFileBeans?.size ?: 0
            }
            else -> 0
        }
    }

    override fun onParametersCallback(): OperationParameters? {
        return null
    }

    override fun doInBackground(vararg voids: Void): OperationResult {
        storeFilesForBuriedPoint()
        RecycleBinUtils.storeFilesForDeleteLabels(allFilePath, mPaths, mUris, mFileBeans)
        try {
            when {
                mUris != null -> {
                    return eraseUris()
                }
                mFileBeans != null -> {
                    return eraseFiles()
                }
                else -> {
                    Log.w(TAG, "doInBackground nothing to do")
                }
            }
        } catch (ex: Exception) {
            Log.w(TAG, "doInBackground error: $ex")
        } finally {
            mEraseBulk.flush()
            deleteFileLabelsByPathList()
            buriedPointForMedia()
        }
        val totalCount = onTotalCountCallback()
        return OperationResult(totalCount, totalCount, STATUS_ERROR)
    }

    private fun deleteFileLabelsByPathList() {
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.onDeleteFilePaths(allFilePathForLabels)
    }

    private fun eraseFiles(): OperationResult {
        if (mFileBeans.isNullOrEmpty()) {
            Log.w(TAG, "eraseFiles file is null or empty")
            return OperationResult(0, 0, STATUS_ERROR)
        }

        val totalSize = mFileBeans?.size ?: 0
        val internalRoot = VolumeEnvironment.getInternalSdPath(appContext)
        if (TextUtils.isEmpty(internalRoot)) {
            Log.w(TAG, "eraseFiles  getInternalPath failed")
            return OperationResult(totalSize, totalSize, STATUS_ERROR)
        }

        val queryAction = QueryBatchAction(INTERNAL_CONTENT_URI, RecycleStore.Files.FileColumns.RECYCLE_ID, QUERY_BATCH_ID_COUNT)
        var failedCount = 0
        val ids = ArrayList<String>()
        var id: String?
        var path: String?
        for (file in mFileBeans!!) {
            if (file is RecycleFileWrapper) {
                path = (file.mRecyclePath ?: file.mData)
                id = file.mRecycleId
            } else {
                Log.w(TAG, "eraseFiles file is not recycle bin file")
                failedCount++
                continue
            }

            if (path.isNullOrEmpty()) {
                Log.w(TAG, "eraseFiles path is empty")
                failedCount++
                continue
            } else if (!path.startsWith(internalRoot)) {
                Log.w(TAG, "eraseFiles external path")
                failedCount++
                continue
            }

            if (id.isNullOrEmpty()) {
                Log.w(TAG, "eraseFiles recycleId is null")
                failedCount++
                continue
            }

            queryAction.add(id)
            ids.add(id)
        }

        queryAction.flush()

        val valuesList = queryAction.getBatchResult()
        var index = 0
        var values: ContentValues?
        val store = DataRetriever.INSTANCE
        for (id1 in ids) {
            values = valuesList[id1]
            if (!internalErase(store, values)) {
                failedCount++
                index++
                Log.w(TAG, "eraseFiles  index: $index failedCount: $failedCount")
                continue
            }
            index++
            publishProgress((index / totalSize.toFloat() * 100).toInt())
        }
        mEraseBulk.flush()

        failedCount += (ids.size - index)
        return OperationResult(totalSize, failedCount,
                if (failedCount > 0) {
                    STATUS_ERROR
                } else {
                    STATUS_SUCCESS
                }
        )
    }

    override fun onPostExecute(result: OperationResult) {
        super.onPostExecute(result)
        onDestroy()
    }

    override fun onDestroy() {
        mTarget.get()?.let {
            it.runOnUiThread {
                (it as? ComponentActivity)?.lifecycle?.removeObserver(this)
            }
        }
        super.onDestroy()
    }

    private fun eraseUris(): OperationResult {
        if (mUris.isNullOrEmpty()) {
            Log.w(TAG, "eraseUris file is null or empty")
            return OperationResult(0, 0, STATUS_ERROR)
        }

        val totalSize = mUris?.size ?: 0
        var id: Long
        val queryAction = QueryBatchAction(column = RecycleStore.Files.FileColumns.RECYCLE_ID, bulkCount = QUERY_BATCH_ID_COUNT)
        val ids = ArrayList<String>()
        for (uri in mUris!!) {
            // only support internal recycle now
            if (!isInternalVolume(uri)) {
                Log.w(TAG, "deleteRecyleWithUri  parseId uri failed:")
                continue
            }

            id = try {
                ContentUris.parseId(uri)
            } catch (ex: Exception) {
                Log.w(TAG, "deleteRecyleWithUri  parseId uri failed:")
                RecycleStore.INVALID_ID
            }

            if (id == RecycleStore.INVALID_ID) {
                continue
            }

            queryAction.add(id.toString())
            ids.add(id.toString())
        }

        queryAction.flush()

        val valuesList = queryAction.getBatchResult()
        var failedCount = totalSize - valuesList.size
        var index = 0
        var values: ContentValues?
        val store = DataRetriever.INSTANCE
        for (id2 in ids) {
            values = valuesList[id2]
            if (!internalErase(store, values)) {
                failedCount++
                index++
                Log.w(TAG, "eraseUris  index: $index failedCount: $failedCount")
                continue
            }
            index++
            publishProgress((index / totalSize.toFloat() * 100).toInt())
        }

        mEraseBulk.flush()

        failedCount += (ids.size - index)
        return OperationResult(totalSize, failedCount,
                if (failedCount > 0) {
                    STATUS_ERROR
                } else {
                    STATUS_SUCCESS
                }
        )
    }

    private fun internalErase(retrieve: DataRetriever?, values: ContentValues?): Boolean {
        try {
            val recyclePath = values!!.getAsString(RecycleStore.Files.FileColumns.RECYCLE_PATH)
            val originPath = values.getAsString(RecycleStore.Files.FileColumns.ORIGIN_PATH)
            if (retrieve!!.deleteRecycle(recyclePath)) {
                val recycleId = values.getAsString(RecycleStore.Files.FileColumns.RECYCLE_ID)
                val file = File(recyclePath)
                if (file.isDirectory) {
                    // delete child directory file and folder
                    val path = recyclePath + File.separator + "/%"
                    mEraseBulk.add(recycleId, path)
                } else {
                    mEraseBulk.add(recycleId, null)
                }
                val reycleDate = values.getAsLong(RecycleStore.Files.FileColumns.RECYCLE_DATE)
                allFilePathForLabels.add(originPath)
                statisticsDurationDate(RECYCLE_DELETE, reycleDate)
                return true
            }
        } catch (ex: Exception) {
            Log.w(TAG, "internalRestore error: $ex")
        }
        return false
    }

    private fun storeFilesForBuriedPoint() {
        allFilePath.clear()
        if (mUris != null) {
            mUris!!.forEach {
                val file = File(it.path!!)
                if (file.isDirectory) {
                    allFilePath.addAll(getAllFilePathFromDirectory(file))
                } else {
                    if (file.name.startsWith(".").not()) {
                        allFilePath.add(it.path!!)
                    }
                }
            }
        } else if (mFileBeans != null) {
            mFileBeans!!.forEach {
                it.mData?.let { data ->
                    val file = File(data)
                    if (file.isDirectory) {
                        allFilePath.addAll(getAllFilePathFromDirectory(file))
                    } else {
                        if (it.mDisplayName!!.startsWith(".").not()) {
                            allFilePath.add(data)
                        }
                    }
                }
            }
        }
    }

    private fun buriedPointForMedia() {
        val opTracker = OpTracker(appContext)
        Log.d(TAG, "buriedPointForMedia")
        val pointList: List<Map<String, String>> = opTracker.convert(
            OpTrackerMaker.Builder().apply {
                setOp(OpType.DELETE)
                setOpTime(System.currentTimeMillis().toString())
                setOpPath("erase")
                setFilePaths(allFilePath)
                setMediaType(MediaType.MEDIA_TYPE_IMAGE)
            }.build()
        )
        pointList.forEach {
            StatisticsUtils.onCommon(appContext, StatisticsUtils.FILE_OPERATION, it)
        }
    }
}