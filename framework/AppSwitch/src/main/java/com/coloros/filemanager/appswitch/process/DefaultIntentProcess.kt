/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : DefaultIntentProcess.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.process

import android.content.Intent
import com.coloros.filemanager.appswitch.reg.BaseRegexProcessor
import com.coloros.filemanager.appswitch.reg.DefaultRegexProcessor

open class DefaultIntentProcess : IntentProcess {

    companion object {
        const val TAG = "DefaultIntentProcess"
    }

    open val regexProcessor: BaseRegexProcessor = DefaultRegexProcessor()

    override fun processDataBeanByViewExtraString(
        viewExactInfo: IViewExactProcess.ViewExactInfo,
        inputDataBean: IViewExactProcess.KeyDataBean
    ): List<IViewExactProcess.KeyDataBean> {
        //这里使用默认的正则表达式匹配方式匹配规则从界面中提取所需要的
        return regexProcessor.processAllDataBean(viewExactInfo, inputDataBean)
    }

    override fun getKeyDataBeanFromIntent(inputIntent: Intent): IViewExactProcess.KeyDataBean {
        //默认的intentProcess这里针对intent中的数据不做任何抽取处理
        return IViewExactProcess.KeyDataBean()
    }
}