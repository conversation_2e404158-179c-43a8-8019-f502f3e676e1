package com.oplus.filemanager.category.apk.provider

import android.content.ContentValues
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import com.filemanager.common.base.FileLoader
import com.filemanager.common.base.loader.UriLoadResult
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.category.apk.ui.ApkLoader
import com.oplus.filemanager.category.apk.ui.ApkParentFragment
import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import kotlinx.coroutines.runBlocking
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * ApkFileProvider的单元测试类
 * 用于测试Apk文件提供者的相关功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class ApkFileProviderTest {

    /**
     * 模拟的LoaderController对象
     * 用于控制文件加载过程
     */
    @MockK
    private lateinit var mockLoaderController: LoaderController

    /**
     * 模拟的ApkLoader对象
     * 使用@RelaxedMockK注解，可以自动提供默认实现
     */
    @RelaxedMockK
    private lateinit var mockApkLoader: ApkLoader

    /**
     * 模拟的LoadCallback回调接口
     * 使用@RelaxedMockK注解，可以自动提供默认实现
     */
    @RelaxedMockK
    private lateinit var mockLoadCallback: LoadCallback

    /**
     * 测试前的初始化方法
     * 使用MockK框架初始化所有模拟对象
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        // 模拟UriHelper类的静态方法
        mockkObject(UriHelper)
        // 模拟HiddenFileHelper类的静态方法
        mockkObject(HiddenFileHelper)
        // 模拟MediaStoreCompat类的静态方法
        mockkObject(MediaStoreCompat)
    }

    /**
     * 测试LoadCallback回调接口的方法
     * 验证loadSuc和loadCancelled方法能否被正确调用
     */
    @Test
    fun testLoadCallback() {
        // Arrange - 准备测试数据
        // 创建一个空的UriLoadResult对象作为测试数据
        val testResult = UriLoadResult<Int, MediaFileWrapper>(mutableListOf(), hashMapOf())

        // Act & Assert - 执行并验证
        // 调用回调方法
        mockLoadCallback.loadSuc(testResult)
        mockLoadCallback.loadCancelled()

        // 验证回调方法是否被正确调用
        verify { mockLoadCallback.loadSuc(testResult) }
        verify { mockLoadCallback.loadCancelled() }
    }
}