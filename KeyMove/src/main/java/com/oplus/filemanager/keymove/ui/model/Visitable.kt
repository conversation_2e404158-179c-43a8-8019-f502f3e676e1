/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved.
 * * COLOROS_EDIT
 * * File: Visitable
 * * Description: an interface for model which showed in adapter
 * * Version: 1.0
 * * Date : 2020/7/17
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/7/17       1.0         an interface for model which showed in adapter
 ****************************************************************/
package com.oplus.filemanager.keymove.ui.model

import com.filemanager.common.base.BaseFileBean


interface Visitable {

    companion object {
        const val NOTHING_BE_CHOSEN = 0
        const val SOME_BE_CHOSEN = 1
        const val ALL_BE_CHOSEN = 2
    }

    fun getChosenState(): Int

    fun getSize(): Long

    fun getSelectedSize() : Long

    fun setChosenState(state: Int)

    fun getSelectedFile(): ArrayList<BaseFileBean>
}