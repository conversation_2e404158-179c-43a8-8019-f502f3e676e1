/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : AdvertManagerTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/3/22 20:19
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2023/3/22       1.0      create
 **********************************************************************/
package com.oplus.filemanager.ad

import android.content.Context
import android.widget.RelativeLayout
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.compat.AdBrand
import com.oplus.filemanager.compat.AdvertManagerCompat
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AdvertManagerTest {

    @MockK
    lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init()
        context = mockk()
    }

    @Test
    fun testAdBrand() {
        mockkStatic(ModelUtils::class)
        mockkStatic(Utils::class)
        mockkObject(AdvertManagerCompat)
        every { ModelUtils.isTablet() } returns false
        every { Utils.isRealmePhone() } returns false
        every { AdvertManagerCompat.isAdEnabled(any()) } returns true
        justRun { AdvertManagerCompat.initAdLoader(any()) }
        AdvertManager.initAdLoader(context)
        val oppoAd = AdvertManager.adBrand
        Assert.assertEquals(AdBrand.OPPO, oppoAd)

        every { ModelUtils.isTablet() } returns true
        AdvertManager.initAdLoader(context)
        Assert.assertEquals(AdBrand.NULL, AdvertManager.adBrand)
        unmockkStatic(Utils::class)
        unmockkStatic(ModelUtils::class)
        unmockkObject(AdvertManagerCompat)
    }

    @Test
    fun testInitAdLoader() {
        mockkStatic(ModelUtils::class)
        mockkStatic(Utils::class)
        mockkObject(AdvertManagerCompat)
        every { ModelUtils.isTablet() } returns false
        every { Utils.isRealmePhone() } returns false
        every { AdvertManagerCompat.isAdEnabled(any()) } returns true
        justRun { AdvertManagerCompat.initAdLoader(any()) }
        AdvertManager.initAdLoader(context)
        verify { AdvertManagerCompat.initAdLoader(any()) }

        unmockkStatic(Utils::class)
        unmockkStatic(ModelUtils::class)
        unmockkObject(AdvertManagerCompat)
    }

    @Test
    fun testIsAdEnabled() {
        mockkStatic(ModelUtils::class)
        mockkStatic(Utils::class)
        mockkObject(AdvertManagerCompat)
        every { ModelUtils.isTablet() } returns false
        every { Utils.isRealmePhone() } returns false
        every { AdvertManagerCompat.isAdEnabled(any()) } returns true
        justRun { AdvertManagerCompat.initAdLoader(any()) }
        AdvertManager.initAdLoader(context)
        Assert.assertTrue(AdvertManager.isAdEnabled())

        every { ModelUtils.isTablet() } returns true
        every { AdvertManagerCompat.isAdEnabled(any()) } returns false
        AdvertManager.initAdLoader(context)
        val isAdEnabled = AdvertManager.isAdEnabled()
        Assert.assertFalse(isAdEnabled)
        unmockkStatic(Utils::class)
        unmockkStatic(ModelUtils::class)
        unmockkObject(AdvertManagerCompat)
    }

    @Test
    fun testGetAdViewCount() {
        Assert.assertEquals(0, AdvertManager.getAdViewCount(arrayListOf()))
        mockkStatic(ModelUtils::class)
        mockkStatic(Utils::class)
        mockkObject(AdvertManagerCompat)
        every { ModelUtils.isTablet() } returns false
        every { Utils.isRealmePhone() } returns false
        every { AdvertManagerCompat.isAdEnabled(any()) } returns true
        justRun { AdvertManagerCompat.initAdLoader(any()) }
        AdvertManager.initAdLoader(context)
        val fileList = arrayListOf<MediaFileWrapper>()
        fileList.add(MediaFileWrapper().apply { mFileWrapperViewType = BaseFileBean.TYPE_FILE_AD })
        fileList.add(MediaFileWrapper())
        Assert.assertEquals(1, AdvertManager.getAdViewCount(fileList))
        unmockkObject(AdvertManagerCompat)
    }

    @Test
    fun testGetAdView() {
        val fakeAdvertManager = FakeAdvertManager()
        val adView = fakeAdvertManager.getAdView("")
        Assert.assertNull(adView)
        val adViewWithName = fakeAdvertManager.getAdView("fake")
        Assert.assertNull(adViewWithName)
    }
}

class FakeAdvertManager : AdvertManager() {
    override fun getEntryView(name: String): RelativeLayout? {
        return null
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        // do nothing
    }
}