/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:/DocumentFragment.kt
 * * Description:
 * * Version:
 * * Date :2020/6/9
 * * Author:********
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>          <data>        <version>     <desc>
 * *  ZeJiang.Duan      2020/6/9        1.0           Create
 ****************************************************************/
package com.oplus.filemanager.category.document.ui

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import android.os.Bundle
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.PCConsumeOnGenericMotionListener
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.BrowserPathBar.Companion.FILE_BROWSER_FOLDER_ANIM_TIME
import com.filemanager.common.wrapper.MediaFileWrapper
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.category.document.R
import com.oplus.filemanager.category.document.adapter.DocListAdapter
import com.oplus.filemanager.category.document.sortgroup.FileGroupProxy
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier
import java.lang.ref.WeakReference

class DocumentFragment : RecyclerSelectionVMFragment<DocumentViewModel>(), OnBackPressed,
        NavigationBarView.OnItemSelectedListener {
    companion object {
        private const val TAG = "DocumentFragment"
        const val KEY_LAST_OPEN_TIME = "lastOpenTime"
    }

    private var sortEntryView: SortEntryView? = null
    private var mLayoutManager: FileGridLayoutManager? = null
    private var mAdapter: DocListAdapter? = null
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val mSortPopupController by lazy { SortPopupController(lifecycle) }
    private val mSpacesItemDecoration by lazy {
        ItemDecorationFactory.getGridItemDecoration(ItemDecorationFactory.GRID_ITEM_DECORATION_DOC)
    }
    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    internal var mFileOperateController: NormalFileOperateController? = null
        private set
    private var mLoadingController: LoadingController? = null
    private var mTabActivityListener: TabActivityListener<MediaFileWrapper>? = null

    private var mTitle: String? = null
    private var mExtension: String? = null
    private var mDocArray: ArrayList<String>? = null
    private var mTempSortType: Int = -1
    private var mTabPosition = Constants.TAB_ALL
    private var mUri: Uri? = null
    private var mSql: String? = null
    private var mNeedLoadData = false
    private var hasShowEmpty: Boolean = false
    private var isLastOpenTimeOrder = false
    private val mFolderTransformAnimator by lazy { FolderTransformAnimator() }

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }

    private var mAdEntryName: String? = null

    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? = null

    override fun getLayoutResId(): Int {
        return R.layout.document_fragment
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            val bundle = arguments ?: return
            mTabPosition = bundle.getInt(KtConstants.P_TAB_POSITION)
            mTempSortType = bundle.getInt(Constants.TEMP_SORT_TYPE, -1)
            mSql = bundle.getString(KtConstants.P_SQL, MediaStoreCompat.getMediaCountSqlQuery(CategoryHelper.CATEGORY_DOC))
            mExtension = bundle.getString(KtConstants.P_EXTENSION)
            mDocArray = bundle.getStringArrayList(Constants.DOCUMENT_FORMAT_ARRAY)
            val uri = bundle.getString(KtConstants.P_URI, UriHelper.geCategoryUri(CategoryHelper.CATEGORY_DOC).toString())
            Log.d(TAG, "onAttach mTabPosition = $mTabPosition, mTempSortType = $mTempSortType,\n mSql = $mSql")
            if (null == uri || null == mSql) {
                return
            }
            mUri = Uri.parse(uri)
            Log.d(TAG, "onAttach mUri = $mUri")
            mAdapter = DocListAdapter(it, WeakReference(parentFragment), <EMAIL>)
            mAdapter?.setHasStableIds(true)
            mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
            isLastOpenTimeOrder = bundle.getBoolean(KEY_LAST_OPEN_TIME, false)
        }
    }

    override fun initView(view: View) {
        rootView = view.findViewById(R.id.root_view)
        fragmentFastScroller = view.findViewById(R.id.fastScroller)
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        fragmentRecyclerView?.setOnGenericMotionListener(PCConsumeOnGenericMotionListener())
        mGridSpanAnimationHelper = GridSpanAnimationHelper(fragmentRecyclerView!!)
        sortEntryView = getDocParentFragment()?.sortEntryView
        if (isLastOpenTimeOrder) {
            sortEntryView?.setSortOrder(SortHelper.FILE_LAST_OPEN_TIME_ORDER, true)
        } else {
            sortEntryView?.setDefaultOrder(SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC))
        }
    }

    override fun createViewModel(): DocumentViewModel {
        val vm = ViewModelProvider(this)[mTabPosition.toString(), DocumentViewModel::class.java]
        mFileOperateController = NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_DOC, vm).also {
            it.setResultListener(FileOperatorListenerImpl(vm))
        }
        return vm
    }

    override fun initData(savedInstanceState: Bundle?) {
        fragmentRecyclerView?.let {
            it.addItemDecoration(mSpacesItemDecoration)
            it.isNestedScrollingEnabled = true
            it.clipToPadding = false
            mLayoutManager = FileGridLayoutManager(baseVMActivity, ItemDecorationFactory.GRID_ITEM_COUNT_1).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                        override fun getSpanSize(position: Int): Int {
                            val viewType = mAdapter?.getItemViewType(position)
                            val isAdViewType = (viewType == BaseFileBean.TYPE_FILE_AD)
                            val isGroupTitleViewType = (viewType == BaseFileBean.TYPE_DOC_GROUP_TITLE)
                            return if ((AdvertManager.isAdEnabled() && isAdViewType) || isGroupTitleViewType) spanCount else 1
                        }
                    }
                }
            it.layoutManager = mLayoutManager
            it.itemAnimator = mFolderTransformAnimator
            it.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }
            mAdapter?.let { ad ->
                it.adapter = ad
            }
            toolbar?.post {
                if (isAdded) {
                    val paddingBottom = if (it.paddingBottom == 0) {
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    } else {
                        it.paddingBottom
                    }
                    val appbar = (parentFragment as? DocParentFragment)?.mAppBarLayout
                    it.setPadding(it.paddingLeft, KtViewUtils.getRecyclerViewTopPadding(appbar, 0), it.paddingRight, paddingBottom)
                }
            }
            it.setLoadStateForScroll(this)
        }

        if (mNeedLoadData) {
            onResumeLoadData()
        }
        if (actionCheckPermission().not()) {
            sortEntryView?.setFileCount(0)
        }
        TouchShareSupplier.attach(this, mFileOperateController)
    }

    override fun startObserve() {
        fragmentRecyclerView?.post {
            if (isAdded) {
                startListSelectModeObserver()
                startUIDataStateObserver()
                startScrollToPositionObserver()
                startScanModeObserver()
                startObserveLoadState()
            }
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            mLoadingController = LoadingController(it, this).apply {
                observe(fragmentViewModel?.dataLoadState, rootView) {
                    (fragmentViewModel?.getRealFileSize() ?: 0) > 0
                }
                //这里将LoadingController和FolderTransformAnimator通过接口方式关联起来
                mFolderTransformAnimator.registerNeedSkipAnimator(this)
            }
        }
    }

    private fun startScrollToPositionObserver() {
        fragmentViewModel?.mPositionModel?.observe(this) { positionModel ->
            positionModel?.let {
                mLayoutManager?.scrollToPosition(it)
            }
        }
    }

    private fun startListSelectModeObserver() {
        val viewModule = fragmentViewModel ?: return
        viewModule.mModeState.listModel.observe(this, object : Observer<Int> {
            override fun onChanged(value: Int) {
                if (!viewModule.mModeState.initState || !isCurrentFragment()) {
                    toolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                    return
                }
                Log.d(TAG, "startNormalModeObserver: mListModel=$value")
                val selectModel = (value == KtConstants.LIST_SELECTED_MODE)
                mAdapter?.setSelectEnabled(selectModel)
                if (selectModel) {
                    getDocParentFragment()?.disableViewPager()
                    (parentFragment as? DocParentFragment)?.previewEditedFiles(
                        fragmentViewModel?.getSelectItems()
                    )
                } else {
                    (parentFragment as? DocParentFragment)?.previewClickedFile(
                        fragmentViewModel?.previewClickedFileLiveData?.value,
                        fragmentViewModel?.previewClickedFileLiveData
                    )
                }
                fragmentRecyclerView?.let {
                    var navigationView: View? = null
                    baseVMActivity?.apply {
                        navigationView = if (baseVMActivity is DocumentActivity) {
                            this.findViewById(R.id.navigation_tool)
                        } else {
                            val mainAction = Injector.injectFactory<IMain>()
                            mainAction?.getNavigationView(this)
                        }
                    }
                    val bottom = if (selectModel) {
                        KtViewUtils.getSelectModelPaddingBottom(it, navigationView)
                    } else {
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    }
                    it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, bottom)
                    fragmentFastScroller?.apply { trackMarginBottom = bottom }
                }
                (baseVMActivity as? NavigationInterface)?.let {
                    if (selectModel) {
                        it.showNavigation()
                        viewModule.setNavigateItemAble(it)
                    } else {
                        it.hideNavigation()
                    }
                }

                toolbar?.let {
                    changeActionModeAnim(it, {
                        if (selectModel) {
                            mTabActivityListener?.initToolbarSelectedMode(
                                    true,
                                viewModule.getRealFileSize(),
                                viewModule.uiState.value?.selectedList?.size ?: 0,
                                viewModule.getSelectItems())
                        } else {
                            val empty = (viewModule.uiState.value?.fileList?.isNotEmpty() != true)
                            mTabActivityListener?.initToolbarNormalMode(true, empty)
                            mTabActivityListener?.refreshScanModeItemIcon()
                        }
                    } , (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true))
                    it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                }
            }
        })
    }

    private fun startUIDataStateObserver() {
        val viewModule = fragmentViewModel ?: return
        viewModule.uiState.observe(this) { uiModel ->
            Log.d(
                TAG, "CategoryCompressUiModel mUiState: total=${uiModel.fileList.size},"
                        + "select=${uiModel.selectedList.size}, keyword=${uiModel.keyWord}"
            )
            val fileSize = viewModule.getRealFileSize()
            sortEntryView?.setFileCount(fileSize)
            val empty = (viewModule.uiState.value?.fileList?.isNotEmpty() != true)
            if (empty) {
                showEmptyView()
                sortEntryView?.visibility = View.GONE
                fragmentFastScroller?.visibility = View.INVISIBLE
            } else {
                hasShowEmpty = false
                mFileEmptyController.hideFileEmptyView()
                sortEntryView?.visibility = View.VISIBLE
                fragmentFastScroller?.visibility = View.VISIBLE
            }
            if (uiModel.stateModel.listModel.value == KtConstants.LIST_SELECTED_MODE) {
                mTabActivityListener?.initToolbarSelectedMode(
                    false, fileSize,
                    uiModel.selectedList.size,
                    viewModule.getSelectItems()
                )
                if (uiModel.fileList is ArrayList<MediaFileWrapper>) {
                    val groupFileList =
                        convertGroupFileList(uiModel.fileList as ArrayList<MediaFileWrapper>) as ArrayList<MediaFileWrapper>
                    mAdapter?.setData(
                        groupFileList,
                        uiModel.selectedList,
                        viewModule.isLastOpenTimeOrder
                    )
                    (parentFragment as? DocParentFragment)?.previewEditedFiles(
                        fragmentViewModel?.getSelectItems()
                    )
                }
                if ((baseVMActivity is NavigationInterface)) {
                    (baseVMActivity as NavigationInterface).setNavigateItemAble(
                        (uiModel.selectedList.isNotEmpty() && !DragUtils.isDragging),
                        hasDrmFile(viewModule.getSelectItems())
                    )
                }
            } else {
                (parentFragment as? DocParentFragment)?.previewClickedFile(
                    fragmentViewModel?.previewClickedFileLiveData?.value,
                    fragmentViewModel?.previewClickedFileLiveData
                )
                mTabActivityListener?.apply {
                    initToolbarNormalMode(false, empty)
                    refreshScanModeItemIcon()
                }
                if (uiModel.fileList is ArrayList<MediaFileWrapper>) {
                    val groupFileList =
                        convertGroupFileList(uiModel.fileList as ArrayList<MediaFileWrapper>) as ArrayList<MediaFileWrapper>
                    mAdapter?.let {
                        it.setData(
                            groupFileList,
                            uiModel.selectedList,
                            viewModule.isLastOpenTimeOrder
                        )
                        startRequestAd(uiModel.fileList as ArrayList<MediaFileWrapper>)
                    }
                }
            }
        }
        viewModule?.previewClickedFileLiveData?.observe(this) {
            mAdapter?.setPreviewClickedFile(it)
        }
    }

    private fun convertGroupFileList(fileList: MutableList<MediaFileWrapper>): MutableList<MediaFileWrapper> {
        val sort = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC))
        Log.d(TAG, "convertGroupFileList: sort: $sort")
        return FileGroupProxy(sort).convertGroupFileList(fileList)
    }

    override fun getDragHoldDownFile(position: Int): BaseFileBean? {
        return mAdapter?.getItem(position)
    }

    private fun startScanModeObserver() {
        getDocParentFragment()?.mDocParentViewModel?.mBrowseModeState?.observe(this) { scanMode -> updateScanModeView(scanMode) }
    }

    private fun updateScanModeView(scanMode: Int) {
        fragmentViewModel?.mScanModeValue = scanMode
        val needSkipAnimation = mNeedSkipAnimation
        if (needSkipAnimation) {
            refreshScanModeAdapter(scanMode)
        } else {
            fragmentRecyclerView?.let { recyclerView ->
                recyclerView.mTouchable = false
                recyclerView.stopScroll()
            }
            mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                override fun onSpanChangeCallback() {
                    (fragmentRecyclerView?.layoutManager as? GridLayoutManager)?.scrollToPosition(0)
                    refreshScanModeAdapter(scanMode)
                }
            }, object : OnAnimatorEndListener {
                override fun onAnimatorEnd() {
                    fragmentRecyclerView?.mTouchable = true
                }
            })
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            val scanMode = getDocParentFragment()?.mDocParentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            if (scanMode == KtConstants.SCAN_MODE_GRID) {
                refreshScanModeAdapter(scanMode)
            }
            baseVMActivity?.let {
                mFileEmptyController.changeEmptyFileIcon()
            }
            mSortPopupController.hideSortPopUp()
            mFileOperateController?.onConfigurationChanged(context?.resources?.configuration)
            if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
            }
            updateLeftRightMargin()
        }
    }

    fun updateLeftRightMargin() {
        val scanMode = getDocParentFragment()?.mDocParentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            mAdapter?.notifyDataSetChanged()
        }
        sortEntryView?.updateLeftRightMargin()
    }

    fun isEmptyList(): Boolean {
        return hasShowEmpty
    }

    fun getFileList(): List<MediaFileWrapper>? {
        return mAdapter?.mFiles
    }

    fun getSelectItems(): List<MediaFileWrapper>? {
        return fragmentViewModel?.getSelectItems()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getGridItemCount(activity, scanMode,
                                                               ItemDecorationFactory.GRID_ITEM_DECORATION_DOC
        )
        (fragmentRecyclerView?.layoutManager as? GridLayoutManager)?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mAdapter?.apply {
            mFolderTransformAnimator.mSkipAddRemoveAnimation = true
            mScanViewModel = scanMode
            notifyDataSetChanged()
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        if (fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true) {
            showEmptyView()
        }
        if (getDocParentFragment()?.mDocParentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_GRID) {
            refreshScanModeAdapter(KtConstants.SCAN_MODE_GRID)
        }
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    private fun showEmptyView() {
        if (hasShowEmpty) return
        if ((baseVMActivity != null) && (rootView != null)) {
            mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!)
            hasShowEmpty = true
            (parentFragment as? DocParentFragment)?.listEmptyFile()
        }
        Log.d(TAG, "showEmptyView")
    }

    override fun onResumeLoadData() {
        if (!isAdded) {
            Log.e(TAG, "onResumeLoadData fragment don't add")
            return
        }
        if (checkShowPermissionEmpty()) {
            sortEntryView?.setFileCount(0)
            return
        }
        fragmentViewModel?.initLoader(
            LoaderViewModel.getLoaderController(parentFragment ?: this),
            mTabPosition,
            mExtension,
            mUri,
            mSql,
            mDocArray,
            isLastOpenTimeOrder
        )
    }

    fun onMenuItemSelected(item: MenuItem?): Boolean {
        if ((null == item) || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }

        return when (item.itemId) {
            android.R.id.home -> {
                baseVMActivity?.onBackPressed()
                true
            }
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(activity, CategoryHelper.CATEGORY_DOC)
                OptimizeStatisticsUtil.pageSearch(OptimizeStatisticsUtil.DOCUMENT)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, Constants.PAGE_DOC)
                true
            }
            R.id.actionbar_edit -> {
                fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                OptimizeStatisticsUtil.pageEdit(OptimizeStatisticsUtil.DOCUMENT)
                true
            }
            R.id.navigation_sort -> {
                baseVMActivity?.let {
                    StatisticsUtils.nearMeStatistics(it, StatisticsUtils.SEQUENCE_ACTION)
                    OptimizeStatisticsUtil.pageSort(OptimizeStatisticsUtil.DOCUMENT)
                    //是显示打开时间时，弹的Popup显示同步，不以recordMode为准
                    val tempSort = if (fragmentViewModel?.isLastOpenTimeOrder == true) {
                        SortHelper.FILE_LAST_OPEN_TIME_ORDER
                    } else {
                        -1
                    }
                    val anchorView: View? = parentFragment?.view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
                    mSortPopupController.showSortPopUp(it, tempSort, anchorView,
                        SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC), object : SelectItemListener {
                            override fun onDismiss() {
                                sortEntryView?.rotateArrow()
                            }

                            override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                                if (flag) {
                                    //重新选择排序后，不再显示固定打开时间
                                    isLastOpenTimeOrder = false
                                    fragmentViewModel?.isLastOpenTimeOrder = false
                                    sortEntryView?.setSortOrder(sortMode, isDesc)
                                    fragmentViewModel?.sortReload()
                                }
                            }
                        })
                }
                true
            }
            R.id.actionbar_scan_mode -> {
                mTabActivityListener?.updateNeedSkipAnimation(true)
                getDocParentFragment()?.mDocParentViewModel?.clickScanModeItem(baseVMActivity)
                true
            }
            R.id.action_setting -> {
                Injector.injectFactory<ISetting>()?.startSettingActivity(activity)
                OptimizeStatisticsUtil.pageSetting(OptimizeStatisticsUtil.DOCUMENT)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, Constants.PAGE_DOC)
                true
            }
            R.id.action_select_all -> {
                fragmentViewModel?.clickToolbarSelectAll()
                true
            }
            com.filemanager.common.R.id.action_select_cancel -> {
                if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                }
                true
            }
            else -> {
                false
            }
        }
    }

    fun setTabActivityListener(tabListener: TabActivityListener<MediaFileWrapper>) {
        mTabActivityListener = tabListener
    }

    fun setToolbarNew(toolbarParam: COUIToolbar?, title: String) {
        toolbar = toolbarParam
        mTitle = title
    }

    private fun isCurrentFragment(): Boolean {
        val fragment = getDocParentFragment() ?: return false
        return fragment.getCurrentTabPosition() == mTabPosition
    }

    private fun getDocParentFragment(): DocParentFragment? {
        return parentFragment as? DocParentFragment
    }

    fun fromSelectPathResult(requestCode: Int, path: List<String>?) {
        activity?.let { mFileOperateController?.onSelectPathReturn(it, requestCode, path) }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun pressBack(): Boolean {
        return fragmentViewModel?.pressBack() ?: false
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return activity?.let {
            mFileOperateController?.onNavigationItemSelected(it, item)
        } ?: false
    }

    override fun onItemClick(item: com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if (uiModel.stateModel.listModel.value != KtConstants.LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.keyMap[item.selectionKey].apply {
                Log.d(TAG, "onItemClick baseFile=$this")
            } ?: return true
            activity?.let {
                val previewResult = (parentFragment as? DocParentFragment)?.previewClickedFile(
                    baseFile, fragmentViewModel?.previewClickedFileLiveData
                )
                if (previewResult != true) {
                    mFileOperateController?.onFileClick(it, baseFile, e)
                }
            }
        }
        return true
    }

    private fun startRequestAd(data: java.util.ArrayList<MediaFileWrapper>) {
        val fragment = parentFragment
        if (fragment is DocParentFragment) {
            fragment.mAdManager?.let {
                if (mAdEntryName.isNullOrEmpty()) {
                    mAdEntryName = it.makeName(TAG)
                    mAdapter?.mEntryName = mAdEntryName
                }
                fragment.activity?.let { act ->
                    it.requestSubAd(act, mAdEntryName!!, mAdapter!!, data, false)
                }
            }
        }
    }

    fun setToolbarAndTabListener(toolbarParam: COUIToolbar?, titleTemp: String, tabListener: TabActivityListener<MediaFileWrapper>) {
        toolbar = toolbarParam
        mTabActivityListener = tabListener
        mTitle = titleTemp
    }

    /**
     * 判断当前是否处于选中编辑模式
     */
    fun isInSelectMode(): Boolean {
        return fragmentViewModel?.isInSelectMode() == true
    }

    fun exitSelectionMode() {
        if (isInSelectMode()) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        if (getDocParentFragment()?.mDocParentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val slideWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            slideWidth,
            ItemDecorationFactory.GRID_ITEM_DECORATION_DOC
        )
        return true
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            fragmentRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController = SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }

    override fun bringToFront(visible: Int) {
        //显示的时候在appbarlayout初始化后在执行 因为高度依赖appbarlayout
        if (View.GONE == visible) {
            super.bringToFront(visible)
        }
    }

    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        super.updatePermissionEmptyViewPaddingForChild()
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_DOC
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
    }

    override fun onDestroyView() {
        Log.i(TAG, "onDestroyView")
        //这里调用反注册关系，将loader和FolderTransformAnimator两者解除关系
        mFolderTransformAnimator.unRegisterNeddSkipAnimator()
        super.onDestroyView()
    }
}