/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - MediaPlayerHelper.kt
 * Description:
 *     The helper to init media player sdk.
 *
 * Version: 1.0
 * Date: 2024-09-13
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><EMAIL>    2024-09-13   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.utils

import android.app.Application
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.BuildConfig
import com.oplus.tblplayer.TBLPlayerManager
import com.oplus.tblplayer.config.GlobalsConfig
import com.oplus.tblplayer.logger.ILoggerAdapter

internal object MediaPlayerHelper {

    const val ENABLE_REQUIRE_FOCUS_WHEN_PLAY_AUDIO = true
    const val ENABLE_REQUIRE_FOCUS_WHEN_PLAY_VIDEO = true

    private const val DEFAULT_PREPARE_TIMEOUT = 5000L
    private const val DEFAULT_VIDEO_SIZE_DECODE_TIMEOUT = 1000L
    private const val UNSUPPORTED_FILE_PREPARE_TIMEOUT = 500L
    private const val FILE_MP2_EXTENSION = "mp2"
    private const val FILE_APE_EXTENSION = "ape"
    private const val FILE_WMV_EXTENSION = "wmv"
    private const val FILE_WMA_EXTENSION = "wma"
    private const val FILE_AVI_EXTENSION = "avi"
    private const val FILE_MOV_EXTENSION = "mov"
    private const val FILE_FLV_EXTENSION = "flv"

    @JvmStatic
    fun initGlobalConfigs(application: Application) {
        val globalConfigs = GlobalsConfig.Builder(application)
            .setDebug(BuildConfig.DEBUG)
            .setEnableAssertions(false)
            .setRemoteEnable(false)
            .setOkhttpEnable(false)
            .setPreCacheEnable(false)
            .addLoggerAdapter(MediaLogAdapter())
            .build()
        TBLPlayerManager.initGlobals(application, globalConfigs)
    }

    @JvmStatic
    fun getPrepareTimeout(fileBean: BaseFileBean): Long {
        val extName = FileTypeUtils.getExtension(fileBean.mData)
        return getPrepareTimeout(extName)
    }

    @JvmStatic
    fun getPrepareTimeout(extName: String?): Long = when (extName) {
        FILE_MP2_EXTENSION -> UNSUPPORTED_FILE_PREPARE_TIMEOUT
        FILE_APE_EXTENSION -> UNSUPPORTED_FILE_PREPARE_TIMEOUT
        FILE_WMV_EXTENSION -> UNSUPPORTED_FILE_PREPARE_TIMEOUT
        FILE_WMA_EXTENSION -> UNSUPPORTED_FILE_PREPARE_TIMEOUT
        FILE_AVI_EXTENSION -> UNSUPPORTED_FILE_PREPARE_TIMEOUT
        FILE_MOV_EXTENSION -> UNSUPPORTED_FILE_PREPARE_TIMEOUT
        FILE_FLV_EXTENSION -> UNSUPPORTED_FILE_PREPARE_TIMEOUT
        else -> DEFAULT_PREPARE_TIMEOUT
    }

    @JvmStatic
    fun getVideoSizeDecodeTimeout(fileBean: BaseFileBean): Long {
        val extName = FileTypeUtils.getExtension(fileBean.mData)
        return getVideoSizeDecodeTimeout(extName)
    }

    @JvmStatic
    fun getVideoSizeDecodeTimeout(extName: String?): Long {
        return DEFAULT_VIDEO_SIZE_DECODE_TIMEOUT
    }

    private class MediaLogAdapter : ILoggerAdapter {
        override fun isLoggable(priority: Int): Boolean {
            return priority >= Log.getLevel()
        }

        override fun println(priority: Int, tag: String?, msg: String): Int {
            Log.println(priority, tag, msg)
            return 0
        }
    }
}