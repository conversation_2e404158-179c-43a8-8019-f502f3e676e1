/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - DragSelectionDetector.kt
 * Description: Drag Selection Detector
 * Version: 1.0
 * Date : 2020/05/14
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/05/14    1.0     create
 ***********************************************************************/

package com.oplus.dropdrag.recycleview

import android.content.Context
import android.os.Handler
import android.os.Message
import android.view.GestureDetector
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.MotionEvent
import android.view.ViewConfiguration
import com.oplus.dropdrag.DragDropSdkManager
import com.oplus.dropdrag.SelectionTracker
import com.oplus.dropdrag.SelectionTracker.Companion.DEBUG
import com.oplus.dropdrag.utils.Log

internal class DragSelectionDetector<K>(
    context: Context,
    private val selectionTracker: SelectionTracker<K>,
    private val gestureRouter: GestureRouter,
    private val detailsLookup: ItemDetailsLookup<K>?,
    private val selectionPredicate: SelectionPredicates.SelectionPredicate<K>
) {
    companion object {
        private const val TAG = "DragSelectionDetector"
        private const val MSG_DRAG_PRESS = 1000
        private const val DRAG_TIMEOUT = 400L//500L//600L//700L//500
        private const val DRAG_TIMEOUT_WHEN_SELECTED = 100L

        fun <K> create(
            context: Context,
            selectionTracker: SelectionTracker<K>,
            gestureRouter: GestureRouter,
            detailsLookup: ItemDetailsLookup<K>?,
            selectionPredicate: SelectionPredicates.SelectionPredicate<K>
        ): DragSelectionDetector<K> {
            return DragSelectionDetector(context, selectionTracker, gestureRouter,
                detailsLookup, selectionPredicate)
        }

        private val TOUCH_SLOP_SQUARE by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            val configuration = ViewConfiguration.get(DragDropSdkManager.applicationContext)
            val touchSlop = configuration.scaledTouchSlop
            touchSlop * touchSlop
        }
    }

    private val mDetector: GestureDetector
    private val mHandler: Handler

    // DRAG_TIMEOUT AND LongPressTimeout
    private val mLongDragTimeout: Long
    private var mCurrentDownEvent: MotionEvent? = null

    init {
        mHandler = DragSelectionHandler()
        mDetector = GestureDetector(context.applicationContext,
                InnerSimpleOnGestureListener(), mHandler)
        mDetector.setOnDoubleTapListener(null)
        mLongDragTimeout = ViewConfiguration.getLongPressTimeout() + DRAG_TIMEOUT
    }

    fun onDetectInterceptTouchEvent(ev: MotionEvent): Boolean {
        val action = ev.action
        if (DEBUG) {
            Log.d(TAG, "onDetectInterceptTouchEvent action: $action")
        }

        when (action and MotionEvent.ACTION_MASK) {
            MotionEvent.ACTION_DOWN -> {
                if (canDragDrop()) {
                    mCurrentDownEvent?.recycle()
                    mCurrentDownEvent = MotionEvent.obtain(ev)
                    Log.d(TAG, "onDetectInterceptTouchEvent mCurrentDownEvent: $mCurrentDownEvent mLongDragTimeout: $mLongDragTimeout")
                    mHandler.removeMessages(MSG_DRAG_PRESS)
                    mCurrentDownEvent?.let {
                        mHandler.sendEmptyMessageDelayed(MSG_DRAG_PRESS, mLongDragTimeout)
                    }
                }
            }
            MotionEvent.ACTION_UP -> {
                // MAKE SURE remove DRAG MESSAGE AWLAYS
                mHandler.removeMessages(MSG_DRAG_PRESS)
                gestureRouter.onUp(ev)
            }
            MotionEvent.ACTION_CANCEL -> {
                // MAKE SURE remove DRAG MESSAGE AWLAYS
                mHandler.removeMessages(MSG_DRAG_PRESS)
                gestureRouter.onCancel(ev)
            }
            MotionEvent.ACTION_MOVE -> {
                handMoveEvent(ev)
            }
        }

        return mDetector.onTouchEvent(ev)
    }

    fun onDetectTouchEvent(ev: MotionEvent): Boolean {
        if (DEBUG) {
            Log.d(TAG, "onDetectTouchEvent action: ${ev.action}")
        }
        val action = ev.action and MotionEvent.ACTION_MASK
        if (MotionEvent.ACTION_UP == action) {
            mHandler.removeMessages(MSG_DRAG_PRESS)
        }
        return mDetector.onTouchEvent(ev)
    }

    private fun handMoveEvent(ev: MotionEvent) {
        if (!canDragDrop()) {
            return
        } else if (!mHandler.hasMessages(MSG_DRAG_PRESS)) {
            return
        }

        // remove drag drop message if scroll too much
        mCurrentDownEvent?.let {
            val deltaX = (ev.x - it.x).toInt()
            val deltaY = (ev.y - it.y).toInt()
            val distance = deltaX * deltaX + deltaY * deltaY
            if (distance > TOUCH_SLOP_SQUARE) {
                mHandler.removeMessages(MSG_DRAG_PRESS)
                Log.d(TAG, "handMoveEvent REMOVE DRAG")
            }
        }
    }

    private fun dispatchDragPress() {
        Log.d(TAG, "dispatchDragPress")
        if (!canDragDrop()) {
            Log.d(TAG, "dispatchDragPress CAN'T DRAG")
            return
        }

        // check DragRegion and drag state
        mCurrentDownEvent?.let {
            gestureRouter.onDragStart(it)
        }
    }

    private fun canDragDrop(): Boolean {
        return selectionTracker.canDragDrop()
    }

    private inner class InnerSimpleOnGestureListener : SimpleOnGestureListener() {
        override fun onSingleTapUp(e: MotionEvent): Boolean {
            return gestureRouter.onSingleTapUp(e)
        }

        override fun onLongPress(e: MotionEvent) {
            val isSelection = selectionTracker.isInSelectionMode()
            if (isCurrentItemSelect(e)) {
                val view = detailsLookup?.getItemView(e) ?: return
                view.isPressed = false
                if (canDragDrop()) {
                    Log.d(TAG, "onLongPress canDragDrop execute immediately")
                    mHandler.removeMessages(MSG_DRAG_PRESS)
                    mCurrentDownEvent?.let {
                        mHandler.sendEmptyMessageDelayed(MSG_DRAG_PRESS, 1)
                    }
                }
                return
            }
            gestureRouter.onLongPress(e)
            if (canDragDrop()) {
                Log.d(TAG, "onLongPress canDragDrop")
                mHandler.removeMessages(MSG_DRAG_PRESS)
                mCurrentDownEvent?.let {
                    if (isSelection) {
                        mHandler.sendEmptyMessageDelayed(MSG_DRAG_PRESS, 1)
                    } else {
                        mHandler.sendEmptyMessageDelayed(MSG_DRAG_PRESS, DRAG_TIMEOUT)
                    }
                }
            }
        }

        override fun onScroll(e1: MotionEvent?, e2: MotionEvent,
                              distanceX: Float, distanceY: Float): Boolean {
            mHandler.removeMessages(MSG_DRAG_PRESS)
            return gestureRouter.onScroll(e1, e2, distanceX, distanceY)
        }

        override fun onFling(e1: MotionEvent?, e2: MotionEvent,
                             velocityX: Float, velocityY: Float): Boolean {
            return gestureRouter.onFling(e1, e2, velocityX, velocityY)
        }

        override fun onShowPress(e: MotionEvent) {
            gestureRouter.onShowPress(e)
        }

        override fun onDown(e: MotionEvent): Boolean {
            return gestureRouter.onDown(e)
        }

        override fun onDoubleTap(e: MotionEvent): Boolean {
            return gestureRouter.onDoubleTap(e)
        }

        override fun onDoubleTapEvent(e: MotionEvent): Boolean {
            return gestureRouter.onDoubleTapEvent(e)
        }

        override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
            return gestureRouter.onSingleTapConfirmed(e)
        }

        override fun onContextClick(e: MotionEvent): Boolean {
            return gestureRouter.onContextClick(e)
        }
    }

    private fun isCurrentItemSelect(e: MotionEvent): Boolean {
        val item = detailsLookup?.getItemDetails(e) ?: return false
        val key = item.selectionKey ?: return false
        return (selectionTracker.isInSelectionMode()
                && selectionPredicate.canSetStateForKey(key, true)
                && selectionTracker.isItemSelected(key)
                )
    }

    private inner class DragSelectionHandler internal constructor() : Handler() {
        override fun handleMessage(msg: Message) {
            Log.d(TAG, "handleMessage what : " + msg.what)
            super.handleMessage(msg)
            when (msg.what) {
                MSG_DRAG_PRESS -> dispatchDragPress()
            }
        }
    }
}