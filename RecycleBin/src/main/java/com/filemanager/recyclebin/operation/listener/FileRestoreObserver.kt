/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:/FileRestoreObserver.kt
 * * Description:
 * * Version:
 * * Date :2020/6/18
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>          <data>        <version>     <desc>
 * *  ZeJiang.Duan      2020/6/18        1.0           Create
 ****************************************************************/
package com.filemanager.recyclebin.operation.listener

import android.app.Activity
import android.content.Context
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.oplus.recyclebin.R

open class FileRestoreObserver(context: Activity) : BaseFileActionObserver(context) {
    companion object{
        private const val TAG = "FileRestoreObserver"
    }
    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        return when (result.first) {
            ACTION_DONE -> {
                Log.d(TAG,"onChanged ACTION_DONE")
                false
            }
            ACTION_FAILED -> {
                CustomToast.showShort(com.filemanager.common.R.string.toast_delete_file_error)
                false
            }
            else -> false
        }
    }
}