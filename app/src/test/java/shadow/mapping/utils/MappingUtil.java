/************************************************************
 * Copyright 2010-2019 Oplus. All rights reserved.
 * FileName       : ShadowUtil.java
 * Version Number : 1.0
 * Description    : Solve some problems that robolectric and powermockito when they can't coexist runner
 * Author         : zhenggang
 * Date           : 2019-07-12
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019-07-12, zhenggang, create
 ************************************************************/

package shadow.mapping.utils;

import java.lang.reflect.Method;

import javassist.CannotCompileException;
import javassist.ClassPool;
import javassist.CtClass;
import javassist.CtConstructor;
import javassist.CtField;
import javassist.CtMethod;
import javassist.CtPrimitiveType;
import javassist.Modifier;
import javassist.NotFoundException;
import shadow.mapping.ClassInitializer;
import shadow.mapping.annotation.MappingConstructor;
import shadow.mapping.annotation.MappingField;
import shadow.mapping.annotation.MappingMethod;

public class MappingUtil {

    public static final int FLAG_CONSTRUCTRO = 1;
    public static final int FLAG_STATIC_METHOD = 1 << 1;
    public static final int FLAG_METHOD = 1 << 2;
    public static final int FLAG_FIELD = 1 << 3;
    public static final int FLAG_CLASS_INITI = 1 << 4;
    public static final int FLAG_ALL = FLAG_CONSTRUCTRO | FLAG_STATIC_METHOD
            | FLAG_METHOD | FLAG_FIELD | FLAG_CLASS_INITI;

    public static void init(Class<?> mappingClass, int flag) {
        String primaryClassName = null;
        try {
            primaryClassName = AnnotationUtil.getAnnotationClassName(mappingClass);
        } catch (ClassNotFoundException | NotFoundException e) {
            e.printStackTrace();
            return;
        }

        ClassPool cp = ClassPool.getDefault();
        CtClass cc = null;
        try {
            cc = cp.get(primaryClassName);
        } catch (NotFoundException e) {
            e.printStackTrace();
        }
        if (cc == null) {
            return;
        }
        if (cc.isFrozen()) {
            cc.defrost();
        }

        CtClass shadowCt = null;
        try {
            shadowCt = cp.get(mappingClass.getName());
        } catch (NotFoundException e) {
            e.printStackTrace();
        }
        if (shadowCt == null) {
            return;
        }

        if ((flag & FLAG_FIELD) != 0) {
            try {
                addFields(shadowCt, cc);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if ((flag & FLAG_CLASS_INITI) != 0) {
            try {
                mappingClassInitializers(shadowCt, cc);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if ((flag & FLAG_CONSTRUCTRO) != 0) {
            try {
                mappingConstructors(shadowCt, cc);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        CtMethod[] shadowMethods = shadowCt.getDeclaredMethods();
        for (CtMethod shadowMethod : shadowMethods) {
            if (Modifier.isStatic(shadowMethod.getModifiers())) {
                if ((flag & FLAG_STATIC_METHOD) != 0) {
                    try {
                        mappingMethods(shadowMethod, cc, mappingClass);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } else {
                if ((flag & FLAG_METHOD) != 0) {
                    try {
                        mappingMethods(shadowMethod, cc, mappingClass);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        try {
            cc.toClass();
        } catch (CannotCompileException e) {
            e.printStackTrace();
        }
    }

    private static void mappingClassInitializers(CtClass shadowCt, CtClass primaryCtClass) throws Exception {
        ClassPool cp = ClassPool.getDefault();
        CtConstructor clinit = shadowCt.getDeclaredConstructor(new CtClass[]{cp.get(ClassInitializer.class.getName())});
        CtConstructor ctConstructor = primaryCtClass.getClassInitializer();
        MappingConstructor annotation = (MappingConstructor) clinit.getAnnotation(MappingConstructor.class);
        boolean isMapping = annotation != null;
        if (isMapping) {
            String body = annotation.body();
            ctConstructor.setBody("{" + body + "}");
        }
    }

    private static void mappingConstructors(CtClass shadowCt, CtClass primaryCtClass) throws Exception {
        CtConstructor[] shadowCtConstructors = shadowCt.getConstructors();
        for (CtConstructor shadowCtConstructor : shadowCtConstructors) {
            CtConstructor ctConstructor = primaryCtClass.getDeclaredConstructor(shadowCtConstructor.getParameterTypes());
            if (ctConstructor != null) {
                MappingConstructor annotation = (MappingConstructor) shadowCtConstructor.getAnnotation(MappingConstructor.class);
                boolean isMapping = annotation != null;
                if (isMapping) {
                    String body = annotation.body();
                    ctConstructor.setBody("{" + body + "}");
                }
            }
        }
    }

    private static void addFields(CtClass shadowCt, CtClass primaryCtClass) throws Exception {
        CtField[] shadowFields = shadowCt.getFields();
        for (CtField ctField : shadowFields) {
            MappingField mappingField = (MappingField) ctField.getAnnotation(MappingField.class);
            if (mappingField != null) {
                CtField cf = new CtField(ctField.getType(), ctField.getName(), primaryCtClass);
                primaryCtClass.addField(cf);
            }
        }
    }

    private static void mappingMethods(CtMethod shadowMethod, CtClass ctCls, Class<?> shadowClass)
            throws Exception {
        CtMethod ctMethod = ctCls.getDeclaredMethod(shadowMethod.getName(), shadowMethod.getParameterTypes());
        CtClass[] ctClassArray = shadowMethod.getParameterTypes();

        Class[] classArray = new Class[ctClassArray.length];
        for (int i = 0, size = ctClassArray.length; i < size; i++) {
            if (ctClassArray[i] instanceof CtPrimitiveType) {
                String wrapperName = ((CtPrimitiveType)ctClassArray[i]).getWrapperName();
                if (wrapperName == Integer.class.getName()) {
                    classArray[i] = int.class;
                } else if (wrapperName == Float.class.getName()) {
                    classArray[i] = float.class;
                } else if (wrapperName == Double.class.getName()) {
                    classArray[i] = double.class;
                } else if (wrapperName == Short.class.getName()) {
                    classArray[i] = short.class;
                } else if (wrapperName == Byte.class.getName()) {
                    classArray[i] = byte.class;
                } else if (wrapperName == Long.class.getName()) {
                    classArray[i] = long.class;
                } else if (wrapperName == Boolean.class.getName()) {
                    classArray[i] = boolean.class;
                } else if (wrapperName == Character.class.getName()) {
                    classArray[i] = char.class;
                } else {
                    classArray[i] = Class.forName(wrapperName);
                }
            } else {
                CtClass ctClass = ctClassArray[i];
                String name = ctClass.getName();
                classArray[i] = Class.forName(name);
            }
        }

        Method method = shadowClass.getDeclaredMethod(shadowMethod.getName(), classArray);
        MappingMethod anni = method.getAnnotation(MappingMethod.class);
        if (anni != null) {
            String body = anni.body();
            ctMethod.setBody("{" + body + "}");
        }
    }
}