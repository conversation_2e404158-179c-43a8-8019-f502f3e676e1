package com.oplus.filemanager.parentchild.drag

import com.filemanager.common.utils.Log
import com.oplus.filemanager.parentchild.viewholder.BaseOptionHolder
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * ItemDisableDragAnimator的单元测试类
 * 用于测试ItemDisableDragAnimator类的各种行为和功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])  // 将SDK版本从28改为29以解决兼容性问题
class ItemDisableDragAnimatorTest {

    private lateinit var animator: ItemDisableDragAnimator
    private lateinit var mockAlphaChangedCallback: (Float, <PERSON><PERSON><PERSON>) -> Unit

    /**
     * 测试前的初始化方法
     * 1. 模拟Log类的静态方法
     * 2. 创建ItemDisableDragAnimator实例
     * 3. 创建模拟的回调函数
     */
    @Before
    fun setUp() {
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } returns Unit

        animator = ItemDisableDragAnimator()
        mockAlphaChangedCallback = mockk(relaxed = true)
    }

    /**
     * 测试后的清理方法
     * 1. 确保所有动画停止
     * 2. 清理所有mock对象
     */
    @After
    fun tearDown() {
        animator.onDragEnd(false) { _, _ -> } // 确保所有动画停止
        unmockkAll() // 清理所有mock
    }

    /**
     * 测试非编辑模式下开始拖拽时的动画行为
     * 验证:
     * 1. 回调函数被调用
     * 2. 日志被正确记录
     */
    @Test
    fun `onDragStart should start animation when not in edit mode`() {
        animator.onDragStart(false, mockAlphaChangedCallback)

        verify { mockAlphaChangedCallback.invoke(any(), true) }
        verify { Log.d("ItemDisableDragAnimator", "onDragStart isEdit false") }
    }

    /**
     * 测试编辑模式下开始拖拽时的行为
     * 验证:
     * 1. 回调函数不应被调用
     * 2. 日志被正确记录
     */
    @Test
    fun `onDragStart should not start animation when in edit mode`() {
        animator.onDragStart(true, mockAlphaChangedCallback)

        verify(exactly = 0) { mockAlphaChangedCallback.invoke(any(), any()) }
        verify { Log.d("ItemDisableDragAnimator", "onDragStart isEdit true") }
    }

    /**
     * 测试在结束动画运行时开始新动画的行为
     * 验证:
     * 1. 结束动画的回调被调用
     * 2. 开始动画的回调被调用
     */
    @Test
    fun `onDragStart should cancel end animation if running`() {
        animator.onDragEnd(false, mockAlphaChangedCallback)
        animator.onDragStart(false, mockAlphaChangedCallback)

        verify { mockAlphaChangedCallback.invoke(any(), false) }
        verify { mockAlphaChangedCallback.invoke(any(), true) }
    }

    /**
     * 测试非编辑模式下结束拖拽时的动画行为
     * 验证:
     * 1. 回调函数被调用
     * 2. 日志被正确记录
     */
    @Test
    fun `onDragEnd should start animation when not in edit mode`() {
        animator.onDragEnd(false, mockAlphaChangedCallback)

        verify { mockAlphaChangedCallback.invoke(any(), false) }
        verify { Log.d("ItemDisableDragAnimator", "onDragEnd isEdit false") }
    }

    /**
     * 测试编辑模式下结束拖拽时的行为
     * 验证:
     * 1. 回调函数不应被调用
     * 2. 日志被正确记录
     */
    @Test
    fun `onDragEnd should not start animation when in edit mode`() {
        animator.onDragEnd(true, mockAlphaChangedCallback)

        verify(exactly = 0) { mockAlphaChangedCallback.invoke(any(), any()) }
        verify { Log.d("ItemDisableDragAnimator", "onDragEnd isEdit true") }
    }

    /**
     * 测试在开始动画运行时结束动画的行为
     * 验证:
     * 1. 开始动画的回调被调用
     * 2. 结束动画的回调被调用
     */
    @Test
    fun `onDragEnd should cancel start animation if running`() {
        animator.onDragStart(false, mockAlphaChangedCallback)
        animator.onDragEnd(false, mockAlphaChangedCallback)

        verify { mockAlphaChangedCallback.invoke(any(), true) }
        verify { mockAlphaChangedCallback.invoke(any(), false) }
    }

    /**
     * 测试开始动画的alpha值计算是否正确
     * 验证:
     * 1. alpha值在有效范围内(EDIT_VIEW_ALPHA到1f)
     * 2. alpha值是递减的
     */
    @Test
    fun `startDragAnimator should calculate correct alpha values`() {
        var lastAlpha = 1f
        var isValid = true
        animator.onDragStart(false) { alpha, _ -> 
            isValid = isValid && (alpha >= BaseOptionHolder.EDIT_VIEW_ALPHA && alpha <= 1f)
            isValid = isValid && (alpha <= lastAlpha) // 确保alpha是递减的
            lastAlpha = alpha
        }
        assertTrue(isValid, "Alpha values should be valid and decreasing")
    }

    /**
     * 测试结束动画的alpha值计算是否正确
     * 验证:
     * 1. alpha值在有效范围内(EDIT_VIEW_ALPHA到1f)
     * 2. alpha值是递增的
     */
    @Test
    fun `endDragAnimator should calculate correct alpha values`() {
        var lastAlpha = BaseOptionHolder.EDIT_VIEW_ALPHA
        var isValid = true
        animator.onDragEnd(false) { alpha, _ -> 
            isValid = isValid && (alpha >= BaseOptionHolder.EDIT_VIEW_ALPHA && alpha <= 1f)
            isValid = isValid && (alpha >= lastAlpha) // 确保alpha是递增的
            lastAlpha = alpha
        }
        assertTrue(isValid, "Alpha values should be valid and increasing")
    }

    /**
     * 测试动画不能同时运行
     * 验证:
     * 1. 开始动画和结束动画的回调各只被调用一次
     */
    @Test
    fun `animators should not run concurrently`() {
        animator.onDragStart(false, mockAlphaChangedCallback)
        animator.onDragEnd(false, mockAlphaChangedCallback)

        verify(exactly = 1) { mockAlphaChangedCallback.invoke(any(), true) }
        verify(exactly = 1) { mockAlphaChangedCallback.invoke(any(), false) }
    }
}