package com.oplus.filemanager.preview.widget

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.test.platform.app.InstrumentationRegistry
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29], manifest = Config.NONE)
class PreviewScrollViewTest {

    private lateinit var context: Context
    private lateinit var previewScrollView: PreviewScrollView
    private lateinit var scrollRoot: ViewGroup
    private lateinit var fillViewportLayout: ViewGroup
    private lateinit var otherView: View

    @Before
    fun setUp() {
        context = InstrumentationRegistry.getInstrumentation().targetContext
        previewScrollView = PreviewScrollView(context)
        
        // Setup test views
        scrollRoot = FrameLayout(context)
        fillViewportLayout = FrameLayout(context)
        otherView = View(context)
        
        scrollRoot.addView(otherView)
        scrollRoot.addView(fillViewportLayout)
    }

    @After
    fun tearDown() {
        // Clear any mock state
        previewScrollView.setPrivateField("scrollRoot", null)
        previewScrollView.setPrivateField("fillViewportLayout", null)
        previewScrollView.setPrivateField("forceUpdateViewport", false)
    }

    @Test
    fun `test setScrollRoot should update scrollRootId`() {
        val testId = 123
        previewScrollView.setScrollRoot(testId)
        assertEquals(testId, previewScrollView.getPrivateField("scrollRootId"))
        assertNull(previewScrollView.getPrivateField("scrollRoot"))
    }

    @Test
    fun `test getScrollRoot should return null when id is 0`() {
        previewScrollView.setScrollRoot(0)
        assertNull(previewScrollView.getPrivateMethod("getScrollRoot")?.invoke(previewScrollView))
    }

    @Test
    fun `test setFillViewportLayout should update fillViewportLayoutId`() {
        val testId = 456
        previewScrollView.setFillViewportLayout(testId)
        assertEquals(testId, previewScrollView.getPrivateField("fillViewportLayoutId"))
        assertNull(previewScrollView.getPrivateField("fillViewportLayout"))
    }

    @Test
    fun `test getFillViewportLayout should return null when id is 0`() {
        previewScrollView.setFillViewportLayout(0)
        assertNull(previewScrollView.getPrivateMethod("getFillViewportLayout")?.invoke(previewScrollView))
    }

    @Test
    fun `test setFillViewportMinHeight should set positive value`() {
        previewScrollView.setFillViewportMinHeight(-100)
        assertEquals(0, previewScrollView.getPrivateField("fillViewportMinHeight"))
        
        previewScrollView.setFillViewportMinHeight(200)
        assertEquals(200, previewScrollView.getPrivateField("fillViewportMinHeight"))
        assertTrue(previewScrollView.getPrivateField("forceUpdateViewport") as Boolean)
    }

    @Test
    fun `test onLayout should not update when not changed and not forced`() {
        val mockFillLayout = mockk<ViewGroup>(relaxed = true)
        previewScrollView.setPrivateField("fillViewportLayout", mockFillLayout)
        previewScrollView.setPrivateField("forceUpdateViewport", false)
        
        previewScrollView.callOnLayout(false, 0, 0, 100, 100)
        
        verify(exactly = 0) { mockFillLayout.layoutParams = any() }
    }

    @Test
    fun `test onLayout should update fill viewport height`() {
        // Setup
        previewScrollView.setPrivateField("scrollRoot", scrollRoot)
        previewScrollView.setPrivateField("fillViewportLayout", fillViewportLayout)
        previewScrollView.setPrivateField("forceUpdateViewport", true)
        previewScrollView.setPrivateField("fillViewportMinHeight", 50)
        
        // Set view dimensions
        otherView.layout(0, 0, 100, 100)
        fillViewportLayout.layout(0, 100, 100, 200)
        previewScrollView.layout(0, 0, 100, 300)
        
        previewScrollView.callOnLayout(true, 0, 0, 100, 300)
        
        assertEquals(200, fillViewportLayout.layoutParams.height) // 300 - 100 = 200
    }

    @Test
    fun `test onLayout should respect min height`() {
        // Setup
        previewScrollView.setPrivateField("scrollRoot", scrollRoot)
        previewScrollView.setPrivateField("fillViewportLayout", fillViewportLayout)
        previewScrollView.setPrivateField("forceUpdateViewport", true)
        previewScrollView.setPrivateField("fillViewportMinHeight", 500)
        
        // Set view dimensions
        otherView.layout(0, 0, 100, 100)
        fillViewportLayout.layout(0, 100, 100, 200)
        previewScrollView.layout(0, 0, 100, 300)
        
        previewScrollView.callOnLayout(true, 0, 0, 100, 300)
        
        assertEquals(500, fillViewportLayout.layoutParams.height) // min height is 500
    }

    @Test
    fun `test isContentScrollable should return false when no content`() {
        assertFalse(previewScrollView.isContentScrollable())
    }

    @Test
    fun `test isContentScrollable should return true when content is taller`() {
        previewScrollView.setPrivateField("scrollRoot", scrollRoot)
        scrollRoot.layout(0, 0, 100, 500)
        previewScrollView.layout(0, 0, 100, 300)
        
        assertTrue(previewScrollView.isContentScrollable())
    }

    @Test
    fun `test isContentScrollable should return false when content is shorter`() {
        previewScrollView.setPrivateField("scrollRoot", scrollRoot)
        scrollRoot.layout(0, 0, 100, 200)
        previewScrollView.layout(0, 0, 100, 300)
        
        assertFalse(previewScrollView.isContentScrollable())
    }
}

// Helper extension functions to access private members
private fun Any.getPrivateField(fieldName: String): Any? {
    return javaClass.getDeclaredField(fieldName).apply { isAccessible = true }.get(this)
}

private fun Any.setPrivateField(fieldName: String, value: Any?) {
    javaClass.getDeclaredField(fieldName).apply { isAccessible = true }.set(this, value)
}

private fun Any.getPrivateMethod(methodName: String): java.lang.reflect.Method? {
    return try {
        javaClass.getDeclaredMethod(methodName).apply { isAccessible = true }
    } catch (e: NoSuchMethodException) {
        null
    }
}

private fun PreviewScrollView.callOnLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
    val method = javaClass.getDeclaredMethod("onLayout", Boolean::class.java, Int::class.java, Int::class.java, Int::class.java, Int::class.java)
    method.isAccessible = true
    method.invoke(this, changed, l, t, r, b)
}