/*********************************************************************************
 * Copyright (C), 2008-2024, Oplus, All rights reserved.
 *
 * File: - SelectionConstants.kt
 * Description:
 *     Some config constants in selections heap up mode.
 *
 * Version: 1.0
 * Date: 2024-11-25
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-11-25   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection.utils

internal object SelectionConstants {
    /**
     * 当前的最大堆叠显示数量，按最近被选中的顺序堆叠显示，直至该上线
     */
    const val MAX_HEAP_UP_COUNT = 3

    /**
     * 超出堆叠上限或取消选中后，依旧缓存在内存中的已加载堆叠项目的数量
     */
    const val MAX_KEEP_IN_MEMORY_COUNT = 3

    /**
     * 多选堆叠，扫描统计文件时，若数量太多，首次通知UI刷新中途结果的已扫描文件数量阈值
     */
    const val NOTIFY_IN_SCANNING_1ST_COUNT = 16

    /**
     * 多选堆叠，扫描统计文件时，若数量太多，第2次及之后通知UI刷新中途结果的已扫描文件数量阈值
     */
    const val NOTIFY_IN_SCANNING_2ND_COUNT = 128
}