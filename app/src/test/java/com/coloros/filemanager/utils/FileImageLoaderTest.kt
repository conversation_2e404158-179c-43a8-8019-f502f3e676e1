/*********************************************************************
 * * Copyright (C), 2010-2025, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.GlobalSearchFilterParseTest
 * * Version     : 1.0
 * * Date        : 2024/04/07
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.utils

import android.app.Activity
import android.content.Context
import android.view.View
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.FileImageLoader
import io.mockk.*
import org.junit.Before
import org.junit.Test

class FileImageLoaderTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        MyApplication.init(mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        })
    }

    @Test
    fun `should without call glide clear when call clear if activity is finish`() {
        //given
        val activity = mockk<Activity>(relaxed = true)
        every { activity.isFinishing }.returns(true)
        val view = mockk<ImageView>()
        mockkStatic(Glide::class)
        val glide = mockk<RequestManager>()
        every { Glide.with(activity) } returns glide
        every { glide.clear(any<View>()) } just runs

        val fileImageLoader = FileImageLoader()
        //when
        fileImageLoader.clear(activity, view)
        //then
        verify(inverse = true) { glide.clear(any<View>()) }

        //teardown
        unmockkStatic(Glide::class)
    }

    @Test
    fun `should without call glide clear when call clear if view drawable is null`() {
        //given
        val activity = mockk<Activity>(relaxed = true)
        every { activity.isFinishing }.returns(false)
        every { activity.isDestroyed }.returns(false)
        val view = mockk<ImageView>()
        every { view.drawable } returns null
        mockkStatic(Glide::class)
        val glide = mockk<RequestManager>()
        every { Glide.with(activity) } returns glide
        every { glide.clear(any<View>()) } just runs

        val fileImageLoader = FileImageLoader()
        //when
        fileImageLoader.clear(activity, view)
        //then
        verify(inverse = true) { glide.clear(any<View>()) }

        //teardown
        unmockkStatic(Glide::class)
    }

    @Test
    fun `should call glide clear when call clear`() {
        //given
        val activity = mockk<Activity>(relaxed = true)
        every { activity.isFinishing }.returns(false)
        every { activity.isDestroyed }.returns(false)
        val view = mockk<ImageView>()
        every { view.drawable } returns mockk()
        mockkStatic(Glide::class)
        val glide = mockk<RequestManager>()
        every { Glide.with(any<Context>()) } returns glide

        every { glide.clear(any<View>()) } just runs

        val fileImageLoader = FileImageLoader()
        //when
        fileImageLoader.clear(activity, view)
        //then
        verify { glide.clear(any<View>()) }

        //teardown
        unmockkStatic(Glide::class)
    }
}