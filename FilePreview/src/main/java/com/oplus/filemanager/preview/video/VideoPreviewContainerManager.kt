/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - VideoPreviewContainerManager.kt
 * Description:
 *     Manage container ui and loading ui for video archive file
 *
 * Version: 1.0
 * Date: 2024-10-25
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-25   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.video

import android.graphics.drawable.ColorDrawable
import android.view.View
import androidx.appcompat.widget.AppCompatTextView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.Log
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewContainerManager
import com.oplus.filemanager.preview.utils.PreviewScrollAreaHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite
import com.oplus.filemanager.preview.widget.PreviewVideoPlaySuite

internal class VideoPreviewContainerManager(rootLayout: View) : AbsPreviewContainerManager(),
    IVideoPreviewContainerManager {

    private companion object {
        private const val TAG = "VideoPreviewContainerManager"
    }

    override val defaultContainer: PreviewFileInfoSuite =
        rootLayout.findViewById(R.id.preview_video_def_info)
    override val nameContainer: TextViewSnippet =
        rootLayout.findViewById(R.id.preview_remote_title)
    override val videoPlayerSuit: PreviewVideoPlaySuite =
        rootLayout.findViewById(R.id.preview_video_play_suite)
    private val videoContainer = rootLayout.findViewById<View>(R.id.preview_video_container)
    private val loadingLayout = rootLayout.findViewById<View>(R.id.loading_layout)
    override val mDateTv: AppCompatTextView = rootLayout.findViewById(R.id.preview_remote_time_info)
    override val mSizeTv: AppCompatTextView = rootLayout.findViewById(R.id.preview_remote_size_info)
    private val scrollAreaHelper = PreviewScrollAreaHelper(
        scrollView = rootLayout.findViewById(R.id.preview_audio_scroll_area),
        operationsBar = rootLayout.findViewById(R.id.preview_operations_bar)
    )

    /**
     * 设置[videoContainer]为[View.INVISIBLE]会导致TBL MediaPlayer加载视频文件时无法立刻为TextureView
     * 创建Surface，在退出加载状态改为[View.VISIBLE]时，因需要重新创建Surface而无法立刻显示出已加载视频内容。
     * 故加载视频期间需保持[videoContainer]状态为[View.VISIBLE]，同时设置前景为背景色来达到视觉上隐藏的效果。
     */
    override fun showAsLoading() {
        super.showAsLoading()
        Log.d(TAG, "showAsLoading")
        defaultContainer.visibility = View.GONE
        nameContainer.visibility = View.VISIBLE
        videoContainer.foreground = ColorDrawable(getVideoCoverForeground(videoContainer))
        videoContainer.visibility = View.VISIBLE
        loadingLayout.visibility = View.VISIBLE
        loadingLayout.z = 1f
        scrollAreaHelper.hideDivider()
    }

    override fun showAsFileContainer(isDefault: Boolean) {
        Log.d(TAG, "showAsFileContainer: $isDefault")
        loadingLayout.visibility = View.GONE
        if (isDefault) {
            defaultContainer.visibility = View.VISIBLE
            defaultContainer.setFilePathVisible(false)
            nameContainer.visibility = View.INVISIBLE
            videoContainer.visibility = View.INVISIBLE
            videoContainer.foreground = null
            scrollAreaHelper.disableScrollViewportMinHeight()
            scrollAreaHelper.hideDivider()
        } else {
            defaultContainer.visibility = View.GONE
            nameContainer.visibility = View.VISIBLE
            videoContainer.visibility = View.VISIBLE
            videoContainer.foreground = null
            scrollAreaHelper.enableScrollViewportMinHeight(
                defaultContainer.resources
                    .getDimensionPixelOffset(R.dimen.preview_remote_scroll_layout_min_height)
            )
            scrollAreaHelper.checkShowDivider()
        }
        super.showAsFileContainer(isDefault)
    }

    private fun getVideoCoverForeground(view: View): Int =
        COUIContextUtil.getAttrColor(
            view.context,
            com.support.appcompat.R.attr.couiColorBackgroundWithCard
        )

    override fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        scrollAreaHelper.checkShowDivider()
    }
}