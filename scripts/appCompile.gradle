/**
 * appCompile.gradle用于进行所有直接集成至app的module的通用编译配置。
 * 非针对所有Module生效的配置禁止在该gradle文件中处理。
 */
final isAppModule = project.plugins.hasPlugin("com.android.application")
final isLibModule = project.plugins.hasPlugin("com.android.library")
if (!(isAppModule || isLibModule)) {
    final errMsg = "${project.path}: appCompile.gradle must be applied to " +
            "an Android application or library module. Please apply " +
            "com.android.application or com.android.library first."
    System.err.println(errMsg)
    throw new GradleException(errMsg)
}
if (ext.has("applyBaseCompileFrom")) {
    final String alreadyApply = ext.applyBaseCompileFrom
    if (alreadyApply != null && !alreadyApply.isEmpty()) {
        final errMsg = "${project.path}: Can not apply appCompile.gradle with $alreadyApply together."
        System.err.println(errMsg)
        throw new GradleException(errMsg)
    }
}
ext.applyBaseCompileFrom = "scripts/appCompile.gradle"
apply from: rootProject.file("scripts/baseCompile.gradle")

/**
 * 此处可导入所有仅集成至APP的Module都需要引用的公共依赖，
 * 但原则上各个Module需在各自的build.gradle中按需导入依赖，非必要不得在此配置导入所有module
 */
dependencies {
    implementation libs.androidx.core.ktx
    // OLint codes check:
    implementation libs.oplus.check.olint
}

//AccountAgent.reqSignInAccount调用时在个别机型上会导致闪退，增加此处理以解决
configurations.configureEach {
    it.resolutionStrategy {
        force libs.taphttp
        force libs.findbugs
        force libs.okio
    }
}

configurations.configureEach {
    exclude group: 'com.squareup.okhttp3', module: 'okhttp'
}