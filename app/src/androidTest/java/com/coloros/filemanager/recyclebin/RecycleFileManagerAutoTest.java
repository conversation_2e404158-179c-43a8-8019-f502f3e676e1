/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * File        :  -
 * * Description : com.coloros.filemanager
 * * Version     : 1.0
 * * Date        : 2020/11/13
 * * Author      : W9006071
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.recyclebin;

import android.content.Context;
import android.os.SystemClock;

import androidx.activity.ComponentActivity;
import androidx.test.filters.SmallTest;
import androidx.test.platform.app.InstrumentationRegistry;
import androidx.test.rule.ActivityTestRule;
import androidx.test.uiautomator.UiDevice;

import com.coloros.filemanager.utils.TestUtils;
import com.filemanager.common.base.BaseFileBean;
import com.filemanager.common.helper.CategoryHelper;
import com.filemanager.common.wrapper.PathFileWrapper;
import com.filemanager.recyclebin.RecycleFileManager;
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization;
import com.oplus.filebrowser.FileBrowserActivity;
import com.oplus.filemanager.main.ui.MainActivity;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;

public class RecycleFileManagerAutoTest {
    private static Context sContext = null;

    private static UiDevice sUiDevice = null;
    private RecycleFileManager.RecycleFileParameters recycleFileParameters = null;

    @BeforeClass
    public static void classSetUp() {
        //@Rule 初始化ActivityTestRule 不能同时初始两个及以上，会照成launch启动互斥，导致activity启动超时，甚至卡死。
        sContext = InstrumentationRegistry.getInstrumentation().getTargetContext();
        sUiDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());
    }

    @AfterClass
    public static void classTearDown() {
        sContext = null;
        sUiDevice = null;
    }


    /**
     * story id 233041
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_folders_when_delete_button_with_Search() {
        var activityTestRule =
                new ActivityTestRule<>(MainActivity.class, false, false);
        activityTestRule.launchActivity(null);
        ComponentActivity activity = (ComponentActivity) activityTestRule.getActivity();
        String folderName_1 = "/sdcard/SDET_TEST/233041";
        TestUtils.createFolder(folderName_1);
        String folderName_2 = "/sdcard/SDET_TEST/233041";
        TestUtils.createFolder(folderName_2);
        SystemClock.sleep(100);
        File folder1 = new File(folderName_1);
        Assert.assertTrue("folder Does not exist.", folder1.exists());
        File folder2 = new File(folderName_2);
        Assert.assertTrue("folder Does not exist.", folder2.exists());
        ArrayList<BaseFileBean> folder = new ArrayList<>();
        PathFileWrapper pathFileWrapper_1 = new PathFileWrapper(folderName_1);
        PathFileWrapper pathFileWrapper_2 = new PathFileWrapper(folderName_2);

        folder.add(pathFileWrapper_1);
        folder.add(pathFileWrapper_2);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(activity, folder, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(100);
        Assert.assertFalse("folder exist.", folder1.exists());
        Assert.assertFalse("folder exist.", folder2.exists());
    }

    /**
     * story id 233040
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_file_when_delete_button_with_Search() {
        var activityTestRule =
                new ActivityTestRule<>(MainActivity.class, false, false);
        activityTestRule.launchActivity(null);
        ComponentActivity activity = (ComponentActivity) activityTestRule.getActivity();
        String fileName1 = "/sdcard/SDET_TEST/FileManager_0079_0338_1.txt";
        TestUtils.createNewFile(fileName1);
        String fileName2 = "/sdcard/SDET_TEST/FileManager_0079_0338_2.txt";
        TestUtils.createNewFile(fileName2);
        SystemClock.sleep(100);
        File file1 = new File(fileName1);
        Assert.assertTrue("folder Does not exist.", file1.exists());
        File file2 = new File(fileName2);
        Assert.assertTrue("folder Does not exist.", file2.exists());
        ArrayList<BaseFileBean> folder = new ArrayList<>();
        PathFileWrapper pathFileWrapper_1 = new PathFileWrapper(fileName1);
        PathFileWrapper pathFileWrapper_2 = new PathFileWrapper(fileName2);
        folder.add(pathFileWrapper_1);
        folder.add(pathFileWrapper_2);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);

        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(activity, folder, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(100);
        Assert.assertFalse("folder exist.", file1.exists());
        Assert.assertFalse("folder exist.", file2.exists());
    }


    /**
     * story id 185399
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_image_when_recycleFiles_with_CategoryAlbumBrowser() {
        var activityTestRule =
                new ActivityTestRule<>(MainActivity.class, false, false);
        activityTestRule.launchActivity(null);
        ComponentActivity mainActivity = (ComponentActivity) activityTestRule.getActivity();
        String path = "/sdcard/185399.png";
        File file = new File(path);
        sUiDevice.takeScreenshot(file);
        SystemClock.sleep(100);
        Assert.assertTrue("Screen Shot Error", file.exists());
        ArrayList<BaseFileBean> folder = new ArrayList<>();
        PathFileWrapper pathFileWrapper = new PathFileWrapper(path);
        folder.add(pathFileWrapper);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);
        mainActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(mainActivity, folder, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(500);
        Assert.assertFalse("Image Delete Error", file.exists());
    }

    /**
     * story id 188538
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_images_when_recycleFiles_with_CategoryAlbumBrowser() {
        var activityTestRule =
                new ActivityTestRule<>(MainActivity.class, false, false);
        activityTestRule.launchActivity(null);
        ComponentActivity mainActivity = (ComponentActivity) activityTestRule.getActivity();

        ArrayList<BaseFileBean> list = new ArrayList<>();
        PathFileWrapper pathFileWrapper = null;
        File images = null;
        String[] paths = {"/sdcard/FileManager_0079_0032_test1.png", "/sdcard/FileManager_0079_0032_test2.png"};
        for (int i = 0; i < paths.length; i++) {
            String screenShotName = paths[i];
            images = new File(screenShotName);
            sUiDevice.takeScreenshot(images);
            Assert.assertTrue("Screen Shot Error", images.exists());
            pathFileWrapper = new PathFileWrapper(screenShotName);
            list.add(pathFileWrapper);
        }
        SystemClock.sleep(200);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);
        mainActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(mainActivity, list, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(500);
        Assert.assertFalse("Images Delete Error", images.exists());
    }

    /**
     * story id 239775
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_file_when_recycleFiles_with_file() {
        var mFileBrowserActivity =
                new ActivityTestRule<>(FileBrowserActivity.class, false, false);
        mFileBrowserActivity.launchActivity(null);
        ComponentActivity activity = (ComponentActivity) mFileBrowserActivity.getActivity();
        String fileName = "/sdcard/aaa.txt";
        File file1 = new File(fileName);
        TestUtils.createNewFile(fileName);
        if (!file1.exists()) {
            Assert.fail("Create_File_Failed");
        }

        ArrayList<BaseFileBean> list = new ArrayList<>();
        PathFileWrapper pathFileWrapper = new PathFileWrapper(fileName);
        list.add(pathFileWrapper);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(activity, list, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(500);
        if (file1.exists()) {
            Assert.fail("Delete_Failed");
        }
    }

    /**
     * story id 112031
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_Folder_when_recycleFiles_with_Folder() {
        var mFileBrowserActivity =
                new ActivityTestRule<>(FileBrowserActivity.class, false, false);
        mFileBrowserActivity.launchActivity(null);
        ComponentActivity activity = (ComponentActivity) mFileBrowserActivity.getActivity();
        String folderName = "/sdcard/SDET_TEST/FileManager_0079_0196";
        File folderPath = new File(folderName);
        TestUtils.createFolder(folderName);
        SystemClock.sleep(500);
        if (!folderPath.exists()) {
            Assert.fail("Create_File_Failed");
        }
        String fileName = folderName + "/test.txt";
        File filename = new File(fileName);
        TestUtils.createNewFile(fileName);
        SystemClock.sleep(500);
        if (!filename.exists()) {
            Assert.fail("Create_File_Failed");
        }
        ArrayList<BaseFileBean> folder = new ArrayList<>();
        PathFileWrapper pathFileWrapper = new PathFileWrapper(folderName);
        folder.add(pathFileWrapper);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(activity, folder, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(500);
        if (folderPath.exists()) {
            Assert.fail("Delete_Failed");
        }
    }

    /**
     * story id 239881
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_empty_folder_when_recycleFiles_with_Folder() {
        var mFileBrowserActivity =
                new ActivityTestRule<>(FileBrowserActivity.class, false, false);
        mFileBrowserActivity.launchActivity(null);
        ComponentActivity activity = (ComponentActivity) mFileBrowserActivity.getActivity();

        String folderName = "/sdcard/SDET_TEST/FileManager_0079_0197";
        File folderPath = new File(folderName);
        TestUtils.createFolder(folderName);
        SystemClock.sleep(500);
        if (!folderPath.exists()) {
            Assert.fail("Create_File_Failed");
        }

        ArrayList<BaseFileBean> folder = new ArrayList<>();
        PathFileWrapper pathFileWrapper = new PathFileWrapper(folderName);
        folder.add(pathFileWrapper);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);

        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(activity, folder, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(500);
        if (folderPath.exists()) {
            Assert.fail("Delete_Failed");
        }
    }

    /**
     * story id 357473
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_success_when_MusicActivity_with_AUDIO() {
        var activityTestRule =
                new ActivityTestRule<>(MainActivity.class, false, false);
        activityTestRule.launchActivity(null);
        ComponentActivity activity = (ComponentActivity) activityTestRule.getActivity();
        TestUtils.startCategoryActivity(activity, CategoryHelper.CATEGORY_AUDIO);
        SystemClock.sleep(500);
        String currentActivity = "com.oplus.filemanager.category.audiovideo.ui.CategoryAudioActivity";
        String topActivity = TestUtils.getActivityName(sContext);
        Assert.assertEquals("Open the value of the current Activity", currentActivity, topActivity);
        String testMp3 = "/sdcard/357473.mp3";
        TestUtils.createNewFile(testMp3);
        SystemClock.sleep(500);
        File file = new File(testMp3);
        if (!file.exists()) {
            Assert.fail("can not find file");
        }

        ArrayList<BaseFileBean> list = new ArrayList<>();
        PathFileWrapper pathFileWrapper = new PathFileWrapper(testMp3);
        list.add(pathFileWrapper);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(activity, list, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(500);
        if (file.exists()) {
            Assert.fail("File Delete Error");
        }
    }


    /**
     * story id 396800
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_success_when_videoActivity_with_Video() {
        var activityTestRule =
                new ActivityTestRule<>(MainActivity.class, false, false);
        activityTestRule.launchActivity(null);
        ComponentActivity activity = (ComponentActivity) activityTestRule.getActivity();
        TestUtils.startCategoryActivity(activity, CategoryHelper.CATEGORY_VIDEO);
        SystemClock.sleep(500);
        String currentActivity = "com.oplus.filemanager.category.audiovideo.ui.CategoryAudioActivity";
        String topActivity = TestUtils.getActivityName(sContext);
        Assert.assertEquals("Open the value of the current Activity", currentActivity, topActivity);
        String testMp4 = "/sdcard/396800.mp4";
        TestUtils.createNewFile(testMp4);
        SystemClock.sleep(500);
        File file = new File(testMp4);
        if (!file.exists()) {
            Assert.fail("can not find file");
        }
        ArrayList<BaseFileBean> list = new ArrayList<>();
        PathFileWrapper pathFileWrapper = new PathFileWrapper(testMp4);
        list.add(pathFileWrapper);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(activity, list, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(500);
        if (file.exists()) {
            Assert.fail("File Delete Error");
        }
    }

    /**
     * story id 405671
     * 存量用例报错，暂时Ignore
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_success_when_DocActivity_with_Doc() {
        var activityTestRule =
                new ActivityTestRule<>(MainActivity.class, false, false);
        activityTestRule.launchActivity(null);
        ComponentActivity activity = (ComponentActivity) activityTestRule.getActivity();
        TestUtils.startCategoryActivity(activity, CategoryHelper.CATEGORY_DOC);
        SystemClock.sleep(500);
        String currentActivity = "com.oplus.filemanager.category.document.ui.DocumentActivity";
        String topActivity = TestUtils.getActivityName(sContext);
        Assert.assertEquals("Open the value of the current Activity", currentActivity, topActivity);
        String testpdf = "/sdcard/405671.pdf";
        TestUtils.createNewFile(testpdf);
        SystemClock.sleep(500);
        File file = new File(testpdf);
        if (!file.exists()) {
            Assert.fail("can not find file");
        }
        ArrayList<BaseFileBean> list = new ArrayList<>();
        PathFileWrapper pathFileWrapper = new PathFileWrapper(testpdf);
        list.add(pathFileWrapper);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(activity, list, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(500);
        if (file.exists()) {
            Assert.fail("File Delete Error");
        }
    }
}
