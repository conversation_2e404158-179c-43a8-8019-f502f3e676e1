/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : AdConstantsTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/3/22 20:17
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2023/3/22       1.0      create
 **********************************************************************/
package com.oplus.filemanager.utils

import org.junit.Assert
import org.junit.Test

class AdConstantsTest {

    @Test
    fun testConstant() {
        Assert.assertEquals("108", AdConstants.OAPS_SKEY_R)
        Assert.assertEquals("553EAB197A87B87985ECCF47CB11C6C9", AdConstants.OAPS_ORIGIN_R)
        Assert.assertEquals("92865", AdConstants.CATEGORY_FILE_SINGLE_POS_ID)
        Assert.assertEquals("92863", AdConstants.CATEGORY_MAIN_POS_ID)
        Assert.assertEquals("59558", AdConstants.AD_ID)
    }
}