/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowZipFile.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/10/17
 ** Author: JiaBo Lan
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/10/17      1.0     create
 ****************************************************************/
package shadow;

import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.exception.ZipException;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(ZipFile.class)
public class ShadowZipFile {
    @Implementation
    public boolean isValidZipFile() {
        return true;
    }

    @Implementation
    public boolean isEncrypted() throws ZipException {
        return true;
    }

    @Implementation
    private void readZipInfo() throws ZipException {

    }

    @Implementation
    public void setPassword(char[] password) throws ZipException {

    }

    @Implementation
    public void extractAll(String destPath) throws ZipException {

    }
}
