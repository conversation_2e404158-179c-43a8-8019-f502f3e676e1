/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GetCloudDiskStorageUseCaseTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/3/7 15:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/3/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.main.ui.usecase

import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.utils.PreferencesUtils
import com.oplus.filemanager.interfaze.clouddrive.ICloudDrive
import com.oplus.filemanager.main.ui.uistate.Storage
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module

class GetCloudDiskStorageUseCaseTest {

    private val cloudDrive = mockk<ICloudDrive>()

    private val koinApp = koinApplication {
        modules(module {
            single { cloudDrive }
        })
    }

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        startKoin(koinApp)
    }

    @After
    fun tearDown() {
        stopKoin()
    }

    @Test
    fun `get cloud disk storage info when not support cloud disk`() = runBlocking {
        val getCloudDiskStorageUseCase = GetCloudDiskStorageUseCase()
        val storageItems = mutableListOf<Storage>()
        every { cloudDrive.supportCloudDisk() }.returns(false)
        mockkStatic(PreferencesUtils::class)
        every { PreferencesUtils.getBoolean(key = CommonConstants.CLOUD_FUNCTION_SHOW, default = true) }.returns(false)
        getCloudDiskStorageUseCase.invoke(storageItems)
        Assert.assertEquals(1, storageItems.size)
        val storage = storageItems[0]
        Assert.assertTrue(storage is Storage.CloudDiskStorage)
        storage as Storage.CloudDiskStorage
        Assert.assertFalse(storage.isSingedIn)
        unmockkStatic(PreferencesUtils::class)
    }

    @Test
    fun `get cloud disk storage info when support cloud disk`() = runBlocking {
        val getCloudDiskStorageUseCase = GetCloudDiskStorageUseCase()
        val storageItems = mutableListOf<Storage>()
        every { cloudDrive.supportCloudDisk() }.returns(true)
        mockkObject(PreferencesUtils)
        every { PreferencesUtils.getBoolean(key = CommonConstants.CLOUD_FUNCTION_SHOW, default = true) }.returns(true)
        getCloudDiskStorageUseCase.invoke(storageItems)
        Assert.assertEquals(1, storageItems.size)
        val storage = storageItems[0]
        Assert.assertTrue(storage is Storage.CloudDiskStorage)
        storage as Storage.CloudDiskStorage
        Assert.assertTrue(storage.isSingedIn)
        unmockkStatic(PreferencesUtils::class)
    }

    @Test
    fun `get cloud disk storage info when support cloud disk close switch`() = runBlocking {
        val getCloudDiskStorageUseCase = GetCloudDiskStorageUseCase()
        val storageItems = mutableListOf<Storage>()
        every { cloudDrive.supportCloudDisk() }.returns(true)
        mockkObject(PreferencesUtils)
        every { PreferencesUtils.getBoolean(key = CommonConstants.CLOUD_FUNCTION_SHOW, default = true) }.returns(false)
        getCloudDiskStorageUseCase.invoke(storageItems)
        Assert.assertEquals(1, storageItems.size)
        val storage = storageItems[0]
        Assert.assertTrue(storage is Storage.CloudDiskStorage)
        storage as Storage.CloudDiskStorage
        Assert.assertFalse(storage.isSingedIn)
        unmockkStatic(PreferencesUtils::class)
    }
}