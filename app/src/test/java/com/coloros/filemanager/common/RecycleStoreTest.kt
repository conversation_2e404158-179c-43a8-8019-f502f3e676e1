/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : recyclestore test
 * * Version     : 1.0
 * * Date        : 2022/2/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.common

import com.coloros.filemanager.BaseTest
import com.filemanager.common.MyApplication
import com.filemanager.common.RecycleStore
import com.filemanager.common.RecycleStore.getRecycleAuthority
import com.filemanager.common.utils.FileTypeUtils
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test

@Ignore
class RecycleStoreTest : BaseTest() {

    @Test
    fun test_extractMediaTypeByPath() {
        mockkStatic(FileTypeUtils::class)
        every { FileTypeUtils.getExtension(any()) }.answers { callOriginal() }
        every { FileTypeUtils.isApkXType(any()) }.answers { callOriginal() }
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath(""),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_NONE
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.ico"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_IMAGE
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.wav"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_AUDIO
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.amr"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_AUDIO
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.rar"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_ARCHIVE
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.zip"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_ARCHIVE
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.7z"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_ARCHIVE
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.jar"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_ARCHIVE
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.apk"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_APK
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.doc"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_DOCUMENT
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.docx"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_DOCUMENT
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.ppt"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_DOCUMENT
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.pptx"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_DOCUMENT
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.pdf"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_DOCUMENT
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.xls"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_DOCUMENT
        )
        Assert.assertEquals(
            RecycleStore.Files.extractMediaTypeByPath("/sdcard/0/test.xlsx"),
            RecycleStore.Files.FileColumns.MEDIA_TYPE_DOCUMENT
        )
        unmockkStatic(FileTypeUtils::class)
    }

    @Test
    fun test_getFileSize() {
        Assert.assertEquals(RecycleStore.Files.getFileSize(""), 0)
        Assert.assertEquals(RecycleStore.Files.getFileSize("/sdcard/0/test"), -1)
    }

    @Test
    fun test_getRecycleAuthority() {
        MyApplication.flavorBrand = "oppo"
        val result1 = getRecycleAuthority()
        Assert.assertEquals(result1, "com.coloros.filemanager.recycleprovider")

        val result2 = getRecycleAuthority("oppo", "domestic")
        Assert.assertEquals(result2, "com.coloros.filemanager.recycleprovider")
        val result3 = getRecycleAuthority("oppo", "export")
        Assert.assertEquals(result3, "com.coloros.filemanager.recycleprovider")
        val result4 = getRecycleAuthority("oppo", "gdpr")
        Assert.assertEquals(result4, "com.coloros.filemanager.recycleprovider")
        val result5 = getRecycleAuthority("oneplus", "domestic")
        Assert.assertEquals(result5, "com.coloros.filemanager.recycleprovider")
        val result6 = getRecycleAuthority("oneplus", "export")
        Assert.assertEquals(result6, "com.oneplus.filemanager.recycleprovider")
        val result7 = getRecycleAuthority("oneplus", "gdpr")
        Assert.assertEquals(result7, "com.oneplus.filemanager.recycleprovider")

        MyApplication.flavorBrand = ""
    }
}