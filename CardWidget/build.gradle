plugins {
    id 'com.android.library'
    id 'com.google.protobuf'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/variant.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace "com.oplus.filemanager.cardwidget"
}

android.sourceSets.configureEach { sourceSet ->
    if(sourceSet.name.contains('androidTest')){
        return
    }
    if (sourceSet.name.contains('oneplus') && (sourceSet.name.contains('Export') || sourceSet.name.contains('Gdpr'))) {
        manifest.srcFile 'src/main/AndroidManifest-oneplusExp.xml'
        assets.srcDirs += ['src/main/assets-oneplusExp']
        res.srcDirs += ['src/main/res-oneplusExp']
    }
    if (sourceSet.name.contains('oppo') && sourceSet.name.contains('Export')) {
        assets.srcDirs += ['src/main/assets-oppoExp']
        res.srcDirs += ['src/main/res-oppoExp']
    }
}

// Business code dependencies
dependencies {
    implementation libs.apache.commons.io
    implementation libs.androidx.appcompat
    implementation libs.androidx.lifecycle.viewmodel.ktx
    implementation libs.google.gson
    implementation libs.google.material
    implementation libs.google.protobuf.java
    implementation(libs.squareup.retrofit2.protobuf) {
        exclude group: 'com.google.protobuf', module: 'protobuf-java'
    }

    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.recyclerview
    implementation libs.oplus.appcompat.panel
    implementation libs.oplus.appcompat.scrollbar
    implementation libs.oplus.appcompat.card
    implementation libs.oplus.appcompat.preference
    implementation libs.oplus.appcompat.toolbar
    implementation libs.oplus.appcompat.poplist
    implementation libs.oplus.appcompat.dialog
    implementation libs.oplus.pantanal.card.seedling
    implementation libs.koin.android

    implementation project(':Common')
    implementation project(':Main')
    implementation project(':FileOperate')
    implementation project(':Provider')
    implementation project(':framework:DFM')
    if (prop_use_prebuilt_drap_drop_lib.toBoolean()) {
        implementation libs.oplus.filemanager.dragDrop
    } else {
        implementation project(':exportedLibs:DragDropSelection')
    }

    if (prop_use_prebuilt_thumbnail_lib.toBoolean()) {
        implementation libs.oplus.filemanager.thumbnail
    } else {
        implementation project(':exportedLibs:Thumbnail')
    }
}

protobuf {
    protoc {
        final protocModule = libs.google.protobuf.protoc.get().module
        final protocVer = libs.google.protobuf.protoc.get().versionConstraint.requiredVersion
        artifact = "${protocModule.group}:${protocModule.name}:$protocVer"
    }
    generateProtoTasks {
        all().each { task ->
            task.builtins {
                remove java
            }
            task.builtins {
                java {}
            }
        }
    }
}

// Test code dependencies:
dependencies {
    testImplementation libs.json.base
}