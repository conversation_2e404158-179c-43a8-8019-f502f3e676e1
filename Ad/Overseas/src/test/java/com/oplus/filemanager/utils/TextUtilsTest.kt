package com.oplus.filemanager.utils

import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.oplus.filemanager.carrier.CarrierEntity
import org.junit.Assert.*
import org.junit.Before
import org.junit.After
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized
import org.junit.runners.Parameterized.Parameters

/**
 * TextUtils工具类的单元测试类
 * 使用Parameterized参数化测试框架，可以运行多组测试数据
 */
@RunWith(Parameterized::class)
class TextUtilsTest {

    // 用于存储原始的Gson实例
    private lateinit var originalGson: Gson

    /**
     * 测试前置方法，每个测试方法执行前都会调用
     * 初始化Gson实例
     */
    @Before
    fun setUp() {
        originalGson = Gson()
    }

    /**
     * 测试后置方法，每个测试方法执行后都会调用
     * 当前为空实现
     */
    @After
    fun tearDown() {
    }

    /**
     * 测试handleValue方法
     * 验证不同分隔符(换行符、斜杠、中文逗号)的字符串是否能正确分割
     */
    @Test
    fun testHandleValue() {
        // 测试换行符和中文逗号混合的情况
        assertEquals(listOf("a", "b", "c"), TextUtils.handleValue("a\nb，c"))
        // 测试斜杠和换行符混合的情况
        assertEquals(listOf("a", "b", "c"), TextUtils.handleValue("a/b\nc"))
        // 测试中文逗号和斜杠混合的情况
        assertEquals(listOf("a", "b", "c"), TextUtils.handleValue("a，b/c"))
        // 测试空字符串的情况
        assertEquals(emptyList<String>(), TextUtils.handleValue(""))
    }

    /**
     * 参数化测试的伴生对象
     * 提供多组测试数据
     */
    companion object {
        /**
         * 提供handleSupportValue方法的测试数据
         * @return 返回包含多组输入和预期结果的集合
         */
        @JvmStatic
        @Parameters
        fun supportValueData(): Collection<Array<Any>> = listOf(
            arrayOf("支持", true),          // 包含"支持"的情况
            arrayOf("部分支持", true),      // 包含"部分支持"的情况
            arrayOf("完全支持", true),      // 包含"完全支持"的情况
            arrayOf("", true),             // 空字符串的情况
            arrayOf("不支持", false),       // 包含"不支持"的情况
            arrayOf("当前不支持", false),   // 包含"当前不支持"的情况
            arrayOf("系统不支持", false)    // 包含"系统不支持"的情况
        )
    }

    // 参数化测试的第一个参数：输入内容
    @Parameterized.Parameter(0)
    @JvmField
    var content: String = ""

    // 参数化测试的第二个参数：预期结果
    @Parameterized.Parameter(1)
    @JvmField
    var expected: Boolean = false

    /**
     * 测试handleSupportValue方法
     * 验证不同输入内容是否能正确判断是否支持
     */
    @Test
    fun testHandleSupportValue() {
        assertEquals(expected, TextUtils.handleSupportValue(content))
    }

    /**
     * 测试fromJson方法
     * 验证JSON字符串是否能正确转换为CarrierEntity对象列表
     */
    @Test
    fun testFromJson() {
        // 准备测试用的JSON数据
        val json = """
            [
                {
                    "line": 1,
                    "region": "Asia",
                    "regionCode": ["AS"],
                    "carrier": ["China Mobile"],
                    "channelName": ["CMCC"],
                    "pipelineKey": ["key1"],
                    "isSupport": true
                }
            ]
        """.trimIndent()
        
        // 调用被测试方法
        val result = TextUtils.fromJson(json)
        
        // 验证转换结果
        assertEquals(1, result.size)  // 验证列表大小
        assertEquals(1, result[0].line)  // 验证line字段
        assertEquals("Asia", result[0].region)  // 验证region字段
        assertEquals(listOf("AS"), result[0].regionCode)  // 验证regionCode字段
        assertEquals(listOf("China Mobile"), result[0].carrier)  // 验证carrier字段
        assertEquals(listOf("CMCC"), result[0].channelName)  // 验证channelName字段
        assertEquals(listOf("key1"), result[0].pipelineKey)  // 验证pipelineKey字段
        assertTrue(result[0].isSupport)  // 验证isSupport字段

        // 测试空数组的情况
        assertEquals(emptyList<CarrierEntity>(), TextUtils.fromJson("[]"))
    }

    /**
     * 测试fromJson方法处理非法JSON的情况
     * 预期会抛出JsonSyntaxException异常
     */
    @Test(expected = JsonSyntaxException::class)
    fun testFromJson_InvalidJson() {
        TextUtils.fromJson("{invalid}")
    }
}