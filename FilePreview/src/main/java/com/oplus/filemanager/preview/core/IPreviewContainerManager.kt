/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IPreviewContainerManager.kt
 * Description:
 *     The base interface to manage container ui and loading ui for previewing file
 *
 * Version: 1.0
 * Date: 2024-10-25
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-25   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.core

import androidx.appcompat.widget.AppCompatTextView
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.TextViewSnippet

internal interface IPreviewContainerManager : IUiConfigChangeObserver {
    /**
     * The container ui for previewing file as default icon
     */
    val defaultContainer: IWidgetDefFileIntroduce

    /**
     * Record the timestamp of changing to loading state.
     * Null is not loading state.
     */
    val startLoadingTime: Long?

    val mDateTv: AppCompatTextView
    val mSizeTv: AppCompatTextView
    val nameContainer: TextViewSnippet


    /**
     * Show page as loading state
     */
    fun showAsLoading()

    /**
     * Show page as preview state with its container.
     */
    fun showAsFileContainer(isDefault: Boolean)

    override fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        // Default do nothing
    }

    fun fillFileDetailInfo(imageBean: BaseFileBean?) {
        imageBean?.let {
            nameContainer?.text = it.mDisplayName
            mDateTv?.text = Utils.getDateFormat(MyApplication.sAppContext, it.mDateModified)
            mSizeTv?.text = Utils.byteCountToDisplaySize(it.mSize)
        }
    }
}