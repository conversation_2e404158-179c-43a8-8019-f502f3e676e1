/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File:UnknownFilesDialog.kt
 * * Description: open unknow files.
 * * Version:1.0
 * * Date :2017/11/28
 * * Author:dengzhicheng
 * *
 * * ---------------------Revision History: ---------------------
 ** <author>    <data>    <version >    <desc>
 ** <EMAIL> 2017/11/28  1.0 open unknow files.
 ****************************************************************/
package com.filemanager.fileoperate.open;

import static com.filemanager.common.helper.DialogHelperKt.getBottomAlertDialogWindowAnimStyle;
import static com.filemanager.common.helper.DialogHelperKt.getBottomAlertDialogWindowGravity;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Message;

import androidx.annotation.VisibleForTesting;
import androidx.appcompat.app.AlertDialog;

import com.coui.appcompat.dialog.COUIAlertDialogBuilder;
import com.filemanager.common.MyApplication;
import com.filemanager.common.helper.uiconfig.UIConfigMonitor;
import com.filemanager.common.utils.CustomToast;
import com.filemanager.common.utils.Log;
import com.filemanager.common.utils.StaticHandler;
import com.filemanager.common.utils.StatisticsUtils;
import com.filemanager.common.utils.Utils;

import org.apache.commons.io.FilenameUtils;

import java.util.HashMap;
import java.util.Map;

public class UnknownFilesDialog {
    private static final int SHOW_UNKNOWN_FILES_DIALOG_MSG = 0;
    private static final int SHOW_UNKNOWN_FILES_DIALOG_TIMEOUT = 150;

    private static final int ITEM_AUDIO = 0;
    private static final int ITEM_VIDEO = 1;
    private static final int ITEM_PHOTO = 2;
    private static final int ITEM_DOCUMENT = 3;
    private static final String TAG = "UnknownFilesDialog";
    private static final String SELECT_TYPE_CANCEL = "cancel";
    private Intent mIntent;
    private Context mContext;
    private DialogHandler mHandler = new DialogHandler(this);

    private static class DialogHandler extends StaticHandler<UnknownFilesDialog> {
        public DialogHandler(UnknownFilesDialog a) {
            super(a);
        }

        @Override
        protected void handleMessage(Message msg, UnknownFilesDialog t) {
            if (t != null) {
                t.startActivity();
            }
        }
    }

    public AlertDialog showUnknownFilesDialog(final Context context, FileOpenObserver.UnKnownFileBean fileBean) {
        Log.d(TAG, "showUnknownFileDialog " + fileBean);
        final String fileExt = FilenameUtils.getExtension(fileBean.getPath());
        mContext = context;
        COUIAlertDialogBuilder dialogBuilder = new COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_BottomAssignment)
                .setWindowGravity(getBottomAlertDialogWindowGravity(context, null))
                .setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(context, false, null))
                .setTitle(com.filemanager.common.R.string.string_please_select_the_file_type)
                .setNegativeButton(com.filemanager.common.R.string.alert_dialog_no, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        statisticsUnKnownFileOption(fileExt, SELECT_TYPE_CANCEL);
                    }
                });
        dialogBuilder.setBlurBackgroundDrawable(true);
        CharSequence[] menuItemArray = new CharSequence[]{
                context.getString(com.filemanager.common.R.string.string_audio),
                context.getString(com.filemanager.common.R.string.string_videos),
                context.getString(com.filemanager.common.R.string.string_photos),
                context.getString(com.filemanager.common.R.string.string_documents)};
        dialogBuilder.setItems(menuItemArray, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (dialog != null) {
                    dialog.dismiss();
                }
                startToSelectActivity(fileBean, which, fileExt);
            }

        });
        dialogBuilder.setOnDismissListener(dialogInterface -> {
            if (fileBean.isFromRecentCardWidget()) {
                ((Activity) mContext).finish();
            }
        });
        return dialogBuilder.show();
    }

    private void startToSelectActivity(FileOpenObserver.UnKnownFileBean fileBean, int which, String fileExt) {
        mIntent = new Intent();
        String selectType = "text/plain";
        switch (which) {
            case ITEM_AUDIO:
                selectType = "audio/*";
                break;
            case ITEM_VIDEO:
                selectType = "video/*";
                break;
            case ITEM_PHOTO:
                selectType = "image/*";
                break;
            case ITEM_DOCUMENT:
                selectType = "text/plain";
                break;
            default:
                selectType = "text/plain";
                break;
        }
        if (Utils.isNeededSdk28()) {
            mIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            mIntent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
        }
        mIntent.putExtra(Utils.OPEN_FLAG, fileBean.getOpenFlag());
        mIntent.setAction(Intent.ACTION_VIEW);
        mIntent.setDataAndType(fileBean.getUri(), selectType);

        if (UIConfigMonitor.isZoomWindowShow()) {
            CustomToast.showLong(com.filemanager.common.R.string.toast_opened_without_window_mode);
        }

        statisticsUnKnownFileOption(fileExt, selectType);

        if (which == ITEM_VIDEO) {
            mHandler.sendEmptyMessageDelayed(SHOW_UNKNOWN_FILES_DIALOG_MSG, SHOW_UNKNOWN_FILES_DIALOG_TIMEOUT);
        } else {
            startActivity();
        }
    }

    private void startActivity() {
        if (mContext != null) {
            try {
                mContext.startActivity(mIntent);
            } catch (Exception e) {
                CustomToast.showShort(com.filemanager.common.R.string.no_support_open);
            }
        }
    }

    /**
     * 统计未知文件弹窗点击的选项
     *
     * @param extension 文件后缀名
     * @param type      选择的文件类型
     */
    @VisibleForTesting
    void statisticsUnKnownFileOption(String extension, String type) {
        Map<String, String> map = new HashMap<>();
        map.put(StatisticsUtils.FILE_EXTENSION, extension);
        map.put(StatisticsUtils.FILE_TYPE, type);
        Log.d(TAG, "统计 " + map);
        StatisticsUtils.onCommon(MyApplication.getSAppContext(), StatisticsUtils.TAG_SATISFACTION, StatisticsUtils.CLICK_FILE_TYPE_OPTION, map);
    }
}
