/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/6/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.cpu.osense

import org.junit.Test

class OsenseManagerTest {

    @Test
    fun getDefaultScene() {
        OsenseManager.INSTANCE.getDefaultScene()
    }

    @Test
    fun getCopyScene() {
        OsenseManager.INSTANCE.getCopyScene()
    }

    @Test
    fun getUnzipScene() {
        OsenseManager.INSTANCE.getUnzipScene()
    }
}