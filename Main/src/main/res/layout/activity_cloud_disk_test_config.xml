<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundWithCard"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:splitMotionEvents="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:splitMotionEvents="false">

        <TextView android:id="@+id/tvSupportCloudDisk"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_24dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:text="支持云盘拖拽"/>

        <CheckBox android:id="@+id/cbSupportCloudDisk"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_50dp"
            app:layout_constraintStart_toEndOf="@id/tvSupportCloudDisk"
            app:layout_constraintTop_toTopOf="@id/tvSupportCloudDisk"
            app:layout_constraintBottom_toBottomOf="@id/tvSupportCloudDisk"/>

        <TextView
            android:id="@+id/tvError"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="云盘上传错误: "
            android:layout_marginTop="@dimen/dimen_16dp"
            app:layout_constraintStart_toStartOf="@id/tvSupportCloudDisk"
            app:layout_constraintTop_toBottomOf="@id/tvSupportCloudDisk"/>

        <Spinner
            android:id="@+id/spinner"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="@id/cbSupportCloudDisk"
            app:layout_constraintTop_toTopOf="@id/tvError"
            app:layout_constraintBottom_toBottomOf="@id/tvError"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.appbar.COUIDividerAppBarLayout
        android:id="@+id/appbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="?attr/couiColorBackgroundWithCard"
        app:elevation="0dp">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            style="@style/COUIToolBarInAppBarLayoutStyle"
            app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

    </com.google.android.material.appbar.COUIDividerAppBarLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>