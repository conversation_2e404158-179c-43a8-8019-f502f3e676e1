<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.coui.appcompat.edittext.COUIInputView
        android:id="@+id/input_first"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/common_margin"
        android:layout_marginEnd="@dimen/common_margin"
        android:layout_marginBottom="18dp"
        app:couiEnableInputCount="true"
        app:couiInputMaxCount="20"
        app:couiEnableError="true"
        app:couiEnablePassword="true"
        app:couiPasswordType="close"
        app:couiHint="@string/dialog_input_password_hint" />

</ScrollView>