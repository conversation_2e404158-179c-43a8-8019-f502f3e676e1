/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DeleteAction
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 21:27
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/21       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.delete

import android.content.ContentResolver
import android.net.Uri
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils

class DeleteAction(
    private val resolver: ContentResolver,
    uri: Uri,
    private val columnNames: String,
    bulkCount: Int = BATCH_DELETE_COUNT
) : InnerOperation(uri, bulkCount) {

    private val mWhereClause: StringBuilder = StringBuilder()

    override fun add(pending: String) {
        if (pending.isNotEmpty()) {
            if (mWhereClause.isNotEmpty()) {
                mWhereClause.append(",")
            }
            mWhereClause.append("?")
        }
        super.add(pending)
    }

    @Suppress("TooGenericExceptionCaught")
    override fun flush() {
        val size = mPending.size
        if (size <= 0) {
            return
        }

        try {
            val foo = mPending.toTypedArray()
            val numRows = resolver.delete(uri, "$columnNames IN ($mWhereClause)", foo)
            Log.d(TAG, "flush -> size: $size numRows: $numRows")
        } catch (e: Exception) {
            Log.w(TAG, "flush -> failed ${e.message}")
        } finally {
            //make sure reset args only when success
            mWhereClause.setLength(0)
            mWhereClause.clear()
            if (mHasDir) {
                Log.d(TAG, "has dir , send media scan")
                MediaScannerCompat.sendMultiDirMediaScanner(mPending, Utils.MEDIA_SCAN_RECYCLE_DELETE)
            }
            mPending.clear()
        }
    }

    companion object {
        private const val TAG = "DeleteAction"
        private const val BATCH_DELETE_COUNT = 50
    }
}