/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: WpsDocThumbnailLoader
 ** Description: com.coloros.filemanager.thumbnail.doc.wps
 ** Version: 1.0
 ** Date: 2021/5/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.doc.wps

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.SystemClock
import cn.wps.moffice.SnapShotConstant
import cn.wps.moffice.ThumbnailImageLoader
import cn.wps.moffice.ThumbnailRequestData
import cn.wps.moffice.ThumbnailToLoad
import com.filemanager.thumbnail.doc.DocThumbnail
import com.filemanager.thumbnail.doc.IDocThumbnailLoader
import com.filemanager.thumbnail.doc.IDocThumbnailCallback
import java.util.concurrent.ConcurrentHashMap

/**
 * get the thumbnail from doc by the wps service
 */
internal class WpsDocThumbnailLoader(
    private val context: Context,
    private val logger: IWpsThumbnailLogger
) : IDocThumbnailLoader {

    private companion object {
        private const val TAG = "WpsDocThumbnailLoader"
    }

    override val shallSampleBitmap: Boolean = false

    private val isSupportSnapShot by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        getThumbnailInstance().isSupport && isSupportSnapShotVersion
    }
    private val isSupportSnapShotVersion by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        isVersionNameAbove("1.4.7")
    }

    /**
     * 引入永中方案后，通用流程DocThumbnailFetcher等需与WPS解耦，而[IDocThumbnailLoader]会被单例管理。
     * 故建立[DocThumbnail]到[ThumbnailRequestData]的映射，用于记录每个缩略图对应的请求数据以用于执行取消。
     */
    private val requestDataMap = ConcurrentHashMap<DocThumbnail, ThumbnailRequestData>()

    @Volatile
    private var thumbnailInstance: ThumbnailImageLoader? = null

    private fun getThumbnailInstance(): ThumbnailImageLoader =
        thumbnailInstance ?: synchronized(ThumbnailImageLoader::class.java) {
            thumbnailInstance ?: ThumbnailImageLoader.getInstance(context).apply {
                logOpen(logger.isEnabled)
                useExecutorService(false)
                useMemoryCache(false)
            }.also {
                thumbnailInstance = it
            }
        }

    private fun isVersionNameAbove(
        version: String,
        packageName: String = SnapShotConstant.PACKAGE_WPS_LITE
    ): Boolean {
        val versionName = getWpsVersionName(packageName)
        logger.d(TAG, "isVersionNameAbove: version is $versionName, package is $packageName")
        return if (versionName.isNullOrEmpty()) {
            false
        } else {
            versionName >= version
        }
    }

    private fun getWpsVersionName(
        packageName: String = SnapShotConstant.PACKAGE_WPS_LITE
    ): String? = runCatching {
        val packageInfo = context.packageManager.getPackageInfo(packageName, 0)
        packageInfo.versionName
    }.onFailure {
        logger.e(TAG, "getWpsVersionName: ERROR! $it")
    }.getOrNull()

    override fun loadThumbnail(
        docThumbnail: DocThumbnail?,
        width: Int,
        height: Int,
        snapshotCallback: IDocThumbnailCallback
    ) {
        if (docThumbnail?.uri == null) {
            snapshotCallback.onLoadFailed(Exception("uri cannot be null"))
            return
        }
        logger.d(TAG, "loadThumbnail: start, doc=${docThumbnail.uri.logInfo()}")
        context.grantUriPermission(
            SnapShotConstant.PACKAGE_WPS_LITE,
            docThumbnail.uri,
            Intent.FLAG_GRANT_READ_URI_PERMISSION
        )
        val mimeType =
            getThumbnailInstance().getMimeTypeFromExtension(getNameExtension(docThumbnail.uri))
        val startTime = SystemClock.uptimeMillis()
        val requestData = ThumbnailRequestData()
            .setFileUri(docThumbnail.uri)
            .setMimeType(mimeType)
            .setResolution(getResolution(width, height))
            .setLastModified(docThumbnail.lastModified)
            .setFileSize(docThumbnail.size)
        requestDataMap[docThumbnail] = requestData
        getThumbnailInstance().load(requestData, object : ThumbnailToLoad.OnSnapshotCallback {
            override fun onOldSnapshot(p0: String?, p1: String?, p2: Bitmap?) {
                //do nothing, recommended by wps
            }

            override fun onNewSnapshot(inputUri: Uri?, outputUri: Uri?, bitmap: Bitmap?) {
                val useTime = SystemClock.uptimeMillis() - startTime
                val uriMsg = "inputUri=${inputUri?.logInfo()}, outputUri=${outputUri?.logInfo()}"
                logger.d(TAG, "loadThumbnail: onNewSnapshot, useTime=$useTime, $uriMsg")
                if (outputUri != null) {
                    snapshotCallback.onDataReady(outputUri)
                } else {
                    snapshotCallback.onLoadFailed(Exception("Get snapshot failed by wps service"))
                }
                context.revokeUriPermission(
                    requestData.fileUri,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION
                )
                requestDataMap.remove(docThumbnail)
            }
        }).start()
    }

    /**
     * can be low, medium, high, or the real ${width}X${height}

     * SnapShotConstant.SnapShotSize.LOW(equals to the size "960X540")
     * SnapShotConstant.SnapShotSize.MEDIUM(equals to the size "1280X720")
     * SnapShotConstant.SnapShotSize.HEIGHT(equals to the size "1920X1080")
     * you can also pass the real size, like "960X540"，but we recommend to use SnapShotConstant.SnapShotSize.LOW
     */
    private fun getResolution(width: Int, height: Int): String {
        //show in vertical mode and 100:150, considering phone behavior
        return "${width}X$height"
    }

    private fun getNameExtension(uri: Uri?): String? {
        var extension: String? = null
        val fileName = uri?.toString()
        if (!fileName.isNullOrEmpty() && fileName.contains(".")) {
            val nameParts = fileName.split("\\.".toRegex()).toTypedArray()
            if (nameParts.isNotEmpty()) {
                extension = nameParts[nameParts.size - 1]
            }
        }
        return extension
    }

    override fun cancel(thumbModel: DocThumbnail) {
        thumbnailInstance?.run {
            val requestData = requestDataMap[thumbModel] ?: return
            logger.d(TAG, "cancel: doc=${thumbModel.uri.logInfo()}")
            cancelSnapshot(requestData)
            requestDataMap.remove(thumbModel)
        }
    }

    override fun isSupported(): Boolean = isSupportSnapShot

    override fun release() {
        thumbnailInstance?.run {
            logger.d(TAG, "release")
            dispose()
        }
        thumbnailInstance = null
        requestDataMap.clear()
    }

    private fun Uri.logInfo(): String = "${hashCode()}_${getNameExtension(this)}"
}