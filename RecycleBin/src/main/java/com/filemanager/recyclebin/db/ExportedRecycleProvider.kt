/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ExportedRecycleProvider.kt
 * Description:
 *     Export some provider interfaces for recycle bin.
 *
 * Version: 1.0
 * Date: 2024-04-07
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-04-07   1.0    Create this module
 *********************************************************************************/
package com.filemanager.recyclebin.db

import android.content.ContentProvider
import android.content.ContentResolver
import android.content.ContentValues
import android.database.Cursor
import android.net.Uri
import android.os.Binder
import android.os.Bundle
import androidx.annotation.StringDef
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.utils.Log
import org.jetbrains.annotations.VisibleForTesting

class ExportedRecycleProvider : ContentProvider() {

    private val exportedMethods = mutableMapOf<String, IExportedRecycleMethod>()

    private var contentResolver: ContentResolver? = null

    override fun onCreate(): Boolean {
        contentResolver = context?.contentResolver
        registerExportedMethods(
            MethodGetRecycleDirectory,
            MethodObtainRecycleContent,
            MethodBulkObtainRecycleContent,
        )
        return true
    }

    private fun registerExportedMethods(vararg methods: IExportedRecycleMethod) {
        methods.forEach {
            exportedMethods[it.methodName] = it
        }
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        if (!ensureApproveExported(PROVIDER_METHOD_QUERY)) {
            return null
        }
        val redirectUri = redirectUriToInnerProvider("query", uri) ?: return null
        if (RecycleProvider.matchUriForQuery(redirectUri) == null) {
            return null
        }
        return contentResolver?.execInInnerProcess {
            query(redirectUri, projection, selection, selectionArgs, sortOrder)
        }
    }

    override fun getType(uri: Uri): String? {
        if (!ensureApproveExported(PROVIDER_METHOD_GET_TYPE)) {
            return null
        }
        val redirectUri = redirectUriToInnerProvider("getType", uri) ?: return null
        return RecycleProvider.getProviderType(redirectUri)
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        if (!ensureApproveExported(PROVIDER_METHOD_INSERT)) {
            return null
        }
        values ?: run {
            Log.e(TAG, "insert: ERROR! values is null")
            return null
        }
        val redirectUri = redirectUriToInnerProvider("insert", uri) ?: return null
        if (!RecycleProvider.ensureUriMatchForInsert(redirectUri)) {
            Log.e(TAG, "insert: ERROR! can not match in inner RecycleProvider")
            return null
        }
        if (!checkValuesForInsert("insert", values)) {
            return null
        }
        return contentResolver?.execInInnerProcess {
            insert(redirectUri, values)
        }
    }

    override fun bulkInsert(uri: Uri, values: Array<out ContentValues>): Int {
        if (!ensureApproveExported(PROVIDER_METHOD_BULK_INSERT)) {
            return -1
        }
        val redirectUri = redirectUriToInnerProvider("bulkInsert", uri) ?: return 0
        if (!RecycleProvider.ensureUriMatchForBulkInsert(redirectUri)) {
            return 0
        }
        val checkedValues = mutableListOf<ContentValues>()
        values.forEach {
            if (checkValuesForInsert("bulkInsert", it)) {
                checkedValues.add(it)
            }
        }
        Log.d(TAG, "bulkInsert: values.size=${values.size}, checked.size=${checkedValues.size}")
        if (checkedValues.isEmpty()) {
            return 0
        }
        return contentResolver?.execInInnerProcess {
            bulkInsert(redirectUri, checkedValues.toTypedArray())
        } ?: -1
    }

    private fun checkValuesForInsert(methodTag: String, values: ContentValues): Boolean {
        RecycleBinExportHelper.markCallFromExterior(values)
        val checkResult = RecycleBinExportHelper.checkValuesForExportedInsert(values)
        if (!checkResult) {
            Log.e(TAG, "$methodTag: ERROR! incorrect content values")
        }
        return checkResult
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        if (!ensureApproveExported(PROVIDER_METHOD_DELETE)) {
            return -1
        }
        val redirectUri = redirectUriToInnerProvider("delete", uri) ?: return 0
        if (RecycleProvider.matchUriForModify(redirectUri) == null) {
            Log.e(TAG, "delete: ERROR! can not match in inner RecycleProvider")
            return 0
        }
        return contentResolver?.execInInnerProcess {
            delete(redirectUri, selection, selectionArgs)
        } ?: -1
    }

    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<out String>?
    ): Int {
        if (!ensureApproveExported(PROVIDER_METHOD_UPDATE)) {
            return -1
        }
        values?.takeUnless { it.size() <= 0 } ?: run {
            Log.e(TAG, "update: ERROR! values is null or empty")
            return 0
        }
        val redirectUri = redirectUriToInnerProvider("update", uri) ?: return 0
        if (RecycleProvider.matchUriForModify(redirectUri) == null) {
            Log.e(TAG, "update: ERROR! can not match in inner RecycleProvider")
            return 0
        }
        return contentResolver?.execInInnerProcess {
            update(redirectUri, values, selection, selectionArgs)
        } ?: -1
    }

    override fun call(authority: String, method: String, arg: String?, extras: Bundle?): Bundle? {
        if (!ensureApproveExported(PROVIDER_METHOD_CALL)) {
            return null
        }
        if (authority != AUTHORITY) {
            Log.e(TAG, "call ERROR! incorrect authority $authority")
            return null
        }
        return call(method, arg, extras)
    }

    override fun call(method: String, arg: String?, extras: Bundle?): Bundle? {
        if (!ensureApproveExported(PROVIDER_METHOD_CALL)) {
            return null
        }
        val extrasMsg = if (extras == null) " null" else "not null"
        Log.d(TAG, "call: method=$method, arg=$arg, extras is $extrasMsg")
        return exportedMethods[method]?.callMethod(this, arg, extras)
    }

    /**
     * The default token is caller's UID and PID which has no permission to access inner Provider.
     * So switch to FileManger's UID and PID to invoke when need to redirect exterior accessing.
     */
    private inline fun <R> ContentResolver.execInInnerProcess(action: ContentResolver.() -> R): R {
        val callingToken = Binder.clearCallingIdentity()
        try {
            return action()
        } finally {
            Binder.restoreCallingIdentity(callingToken)
        }
    }

    internal companion object {
        private const val TAG = "ExportedRecycleProvider"

        @VisibleForTesting
        internal const val AUTHORITY = "com.oplus.filemanager.recyclebin.db.exported.recycleprovider"
        private const val PROVIDER_METHOD_QUERY = "query"
        private const val PROVIDER_METHOD_GET_TYPE = "getType"
        private const val PROVIDER_METHOD_INSERT = "insert"
        private const val PROVIDER_METHOD_BULK_INSERT = "bulkInsert"
        private const val PROVIDER_METHOD_DELETE = "delete"
        private const val PROVIDER_METHOD_UPDATE = "update"
        private const val PROVIDER_METHOD_CALL = "call"

        private val approveExportedMethods = setOf(
            PROVIDER_METHOD_INSERT,
            PROVIDER_METHOD_BULK_INSERT,
            PROVIDER_METHOD_CALL
        )

        @JvmStatic
        private fun redirectUriToInnerProvider(methodTag: String, uri: Uri): Uri? {
            if (uri.authority != AUTHORITY) {
                Log.e(TAG, "$methodTag: ERROR! incorrect authority ${uri.authority}")
                return null
            }
            return Uri.Builder().apply {
                scheme(uri.scheme)
                authority(RecycleProvider.RECYCLE_AUTHORITY)
                uri.path?.takeIf { it.isNotEmpty() }?.let(::path)
                uri.query?.takeIf { it.isNotEmpty() }?.let(::query)
                uri.fragment?.takeIf { it.isNotEmpty() }?.let(::fragment)
            }.build()
        }

        @JvmStatic
        @VisibleForTesting
        fun ensureApproveExported(@ProviderMethod method: String): Boolean {
            // Recycle Bin is unsupported at light OS at current.
            if (FeatureCompat.sIsLightVersion) {
                Log.w(TAG, "$method: Deny calling on light OS")
                return false
            }
            val isApproved = approveExportedMethods.contains(method)
            if (!isApproved) {
                Log.e(TAG, "$method: ERROR! Deny calling from exterior")
            }
            return isApproved
        }
    }

    @StringDef(
        PROVIDER_METHOD_QUERY,
        PROVIDER_METHOD_GET_TYPE,
        PROVIDER_METHOD_INSERT,
        PROVIDER_METHOD_BULK_INSERT,
        PROVIDER_METHOD_DELETE,
        PROVIDER_METHOD_UPDATE,
        PROVIDER_METHOD_CALL
    )
    @Retention(AnnotationRetention.SOURCE)
    private annotation class ProviderMethod
}