/***********************************************************
 ** Copyright (C), 2008-2025 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.doc
 ** Version: 1.0
 ** Date: 2023/9/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 *   hank.zhou    2023/9/19   1.0           des
 ****************************************************************/
package com.filemanager.thumbnail.appicon

import android.graphics.Bitmap
import androidx.annotation.Keep
import com.bumptech.glide.load.Options
import com.bumptech.glide.load.model.ModelLoader
import com.bumptech.glide.load.model.ModelLoaderFactory
import com.bumptech.glide.load.model.MultiModelLoaderFactory
import com.bumptech.glide.signature.ObjectKey

/**
 * the model loader for doc to load InputStream from original file source
 */
internal class AppIconThumbnailModelLoader : ModelLoader<AppIconThumbnail, Bitmap> {

    override fun buildLoadData(
        model: AppIconThumbnail,
        width: Int,
        height: Int,
        options: Options
    ): ModelLoader.LoadData<Bitmap> {
        return ModelLoader.LoadData(ObjectKey(model), AppIconThumbnailFetcher(model, width, height))
    }

    override fun handles(model: AppIconThumbnail): Boolean {
        return true
    }
}

@Keep
class AppIconThumbnailLoaderFactory : ModelLoaderFactory<AppIconThumbnail, Bitmap> {

    override fun build(multiFactory: MultiModelLoaderFactory): ModelLoader<AppIconThumbnail, Bitmap> {
        return AppIconThumbnailModelLoader()
    }

    override fun teardown() {
        //do nothing
    }
}