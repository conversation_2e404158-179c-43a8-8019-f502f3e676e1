/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - MultiAppUtilsTest.kt
 * Description:
 * The test cases for MultiAppUtils
 *
 * Version: 1.0
 * Date: 2024-06-19
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-06-19   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.utils

import com.oplus.filemanager.simulateclick.engine.utils.MultiAppUtils.readMultiAppAccessMode
import com.oplus.multiapp.OplusMultiAppManager
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class MultiAppUtilsTest : Assert() {

    @Before
    fun setUp() {
        mockkStatic(SdkUtils::class)
    }

    @After
    fun tearDown() {
        unmockkStatic(SdkUtils::class)
    }

    @Test
    fun `should false when isChooseCloneApp if is older than OS12`() {
        // Given
        every { SdkUtils.isAtLeastOS12() } returns false
        every { SdkUtils.isAtLeastS() } returns true

        // When
        val result = MultiAppUtils.isChooseCloneApp("com.test.pkg")

        // Then
        assertFalse(result)
    }

    @Test
    fun `should false when isChooseCloneApp if is older than AndroidS`() {
        // Given
        every { SdkUtils.isAtLeastOS12() } returns true
        every { SdkUtils.isAtLeastS() } returns false

        // When
        val result = MultiAppUtils.isChooseCloneApp("com.test.pkg")

        // Then
        assertFalse(result)
    }

    @Test
    fun `should false when isChooseCloneApp if multi app is unsupported`() {
        // SetUp
        mockkStatic(OplusMultiAppManager::class)

        // Given
        every { SdkUtils.isAtLeastOS12() } returns true
        every { SdkUtils.isAtLeastS() } returns true
        every { OplusMultiAppManager.getInstance() } returns mockk {
            every { isMultiAppSupport } returns false
        }

        // When
        val result = MultiAppUtils.isChooseCloneApp("com.test.pkg")

        // Then
        assertFalse(result)

        // TearDown
        unmockkStatic(OplusMultiAppManager::class)
    }

    @Test
    fun `should false when isChooseCloneApp if is not enable clone app`() {
        // SetUp
        mockkStatic(OplusMultiAppManager::class)

        // Given
        every { SdkUtils.isAtLeastOS12() } returns true
        every { SdkUtils.isAtLeastS() } returns true
        every { OplusMultiAppManager.getInstance() } returns mockk {
            every { isMultiAppSupport } returns true
            every { getMultiAppList(OplusMultiAppManager.LIST_TYPE_CREATED) } returns emptyList()
        }

        // When
        val result = MultiAppUtils.isChooseCloneApp("com.test.pkg")

        // Then
        assertFalse(result)

        // TearDown
        unmockkStatic(OplusMultiAppManager::class)
    }

    @Test
    fun `should false when isChooseCloneApp if is not choose clone`() {
        // SetUp
        mockkStatic(OplusMultiAppManager::class, MultiAppUtils::class)

        // Given
        every { SdkUtils.isAtLeastOS12() } returns true
        every { SdkUtils.isAtLeastS() } returns true
        val testPkg = "com.test.pkg"
        every { OplusMultiAppManager.getInstance() } returns mockk {
            every { isMultiAppSupport } returns true
            every { getMultiAppList(OplusMultiAppManager.LIST_TYPE_CREATED) } returns listOf(testPkg)
            every { readMultiAppAccessMode(testPkg) } returns 1
        }

        // When
        val result = MultiAppUtils.isChooseCloneApp(testPkg)

        // Then
        assertFalse(result)

        // TearDown
        unmockkStatic(OplusMultiAppManager::class, MultiAppUtils::class)
    }

    @Test
    fun `should true when isChooseCloneApp if is choose clone`() {
        // SetUp
        mockkStatic(OplusMultiAppManager::class, MultiAppUtils::class)

        // Given
        every { SdkUtils.isAtLeastOS12() } returns true
        every { SdkUtils.isAtLeastS() } returns true
        val testPkg = "com.test.pkg"
        every { OplusMultiAppManager.getInstance() } returns mockk {
            every { isMultiAppSupport } returns true
            every { getMultiAppList(OplusMultiAppManager.LIST_TYPE_CREATED) } returns listOf(testPkg)
            every { readMultiAppAccessMode(testPkg) } returns 0
        }

        // When
        val result = MultiAppUtils.isChooseCloneApp(testPkg)

        // Then
        assertTrue(result)

        // TearDown
        unmockkStatic(OplusMultiAppManager::class, MultiAppUtils::class)
    }
}