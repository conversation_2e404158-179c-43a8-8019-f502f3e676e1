plugins {
    id 'com.android.library'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace 'com.oplus.filemanager.category.remotedevice'
}

dependencies {
    implementation libs.androidx.lifecycle.viewmodel.ktx
    implementation libs.androidx.fragment.ktx
    // Lifecycle
    implementation libs.androidx.lifecycle.service
    implementation libs.androidx.lifecycle.runtime.ktx
    implementation libs.androidx.workmanager.ktx

    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.recyclerview
    implementation libs.oplus.appcompat.clickablespan
    implementation libs.oplus.appcompat.dialog
    implementation libs.oplus.appcompat.poplist
    implementation libs.oplus.appcompat.grid
    implementation libs.oplus.appcompat.progressbar
    implementation libs.oplus.appcompat.toolbar
    implementation libs.oplus.appcompat.scrollbar
    implementation libs.oplus.appcompat.snackbar
    implementation libs.oplus.appcompat.sidenavigationbar
    implementation libs.oplus.appcompat.bottomnavigation
    implementation libs.oplus.appcompat.panel
    implementation libs.oplus.appcompat.responsiveui
    implementation libs.oplus.appcompat.input
    implementation libs.oplus.appcompat.component
    implementation libs.koin.android
    kapt libs.bumptech.glide.compiler
    implementation libs.bumptech.glide.base
    implementation libs.oplus.appcompat.preference
    implementation project(':Common')
    implementation project(':framework:RemoteDevice')
    implementation project(':Encrypt')
    implementation project(':FileOperate')
    implementation project(':SelectDir')
    if (prop_use_prebuilt_drap_drop_lib.toBoolean()) {
        implementation libs.oplus.filemanager.dragDrop
    } else {
        implementation project(':exportedLibs:DragDropSelection')
    }
}