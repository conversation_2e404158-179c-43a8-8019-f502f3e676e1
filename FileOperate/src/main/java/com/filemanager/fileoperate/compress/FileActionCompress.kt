/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileActionCompress.kt
 ** Description: File compress function
 ** Version: 1.0
 ** Date: 2020/3/9
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.compress

import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.fileutils.checkDestStorageSpace
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.base.*
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.fileservice.IFileService.Companion.OPERATE_TYPE_COMPRESS
import java.io.File

class FileActionCompress(
    lifecycle: LifecycleOwner,
    selectFiles: List<BaseFileBean>,
    destParentFile: BaseFileBean,
    compressFileName: String,
    compressType: Int = CompressHelperFactory.ZIP
) : BaseFileAction<FileCompressObserver>(lifecycle) {
    companion object {
        private const val TAG = "FileActionCompress"
        private const val MAX_COMPRESS_SIZE = Int.MAX_VALUE.toLong()
        private const val MAX_COMPRESS_MULTIPLE: Long = 32
        private const val MAX_COMPRESS = 100

        fun getCompressFileName(fileName: String?): String? {
            // Now only support compress files to zip file
            return if (fileName.isNullOrEmpty() || fileName.isBlank()) {
                null
            } else {
                fileName.plus(".zip")
            }
        }
    }

    private val mLockObj = Object()
    private var mOperateFiles: ArrayList<BaseFileBean> = ArrayList(selectFiles)
    private var mDestParentFile: BaseFileBean = destParentFile
    private var mCompressFileName: String = compressFileName
    private var mCompressType = compressType
    private var mCompressHelper: CompressHelper? = null
    private var mFileTotalSize: Long = 0

    override fun onCancelled() {
        notifyLockReleased()
        mCompressHelper?.cancelCompress()
        super.onCancelled()
    }

    private fun notifyLockReleased() {
        try {
            synchronized(mLockObj) {
                mLockObj.notify()
            }
        } catch (e: Exception) {
            Log.d(TAG, "notifyLockReleased: ${e.message}")
        }
    }

    override fun run(): Boolean {
        if (mOperateFiles.isEmpty() || mDestParentFile.mData.isNullOrEmpty()
                || ((mOperateFiles.size == 1) && !JavaFileHelper.exists(mOperateFiles[0]))) {
            Log.d(TAG, "Failed check condition, selectedSize: ${mOperateFiles.size}, destPath: ${mDestParentFile.mData}")
            notifyObserver(NOTICE_SOURCE_FILE_NOT_EXIST)
            return false
        }
        // Check total size first
        if (validateFileSizeTooLarge()) {
            return false
        }

        Log.d(TAG, "Continue to execute: isCancelled=${isCancelled()}, filename=$mCompressFileName")
        if (isCancelled().not()) {
            return reallyCompressFile(getCompressFileName())
        }
        return false
    }

    private fun reallyCompressFile(fileName: String): Boolean {
        if (!File(mDestParentFile.mData).exists()) {
            File(mDestParentFile.mData).mkdirs()
        }
        val file = File(mDestParentFile.mData, fileName)
        Log.d(TAG, "Start to compress file: filename=${file.absolutePath}")
        notifyObserver(DISMISS_COMPRESS_FILENAME_DIALOG)

        var compressResult = false
        val baseFileBean = PathFileWrapper(file.absolutePath)
        mCompressHelper = CompressHelperFactory.getCompressHelper(mCompressType)
        mCompressHelper?.compress(mOperateFiles, baseFileBean, object : CompressHelper.OnCompressListener {
            override fun onProgressUpdate(currentSize: Long) {
                if (currentSize == 0L) {
                    notifyObserver(
                        SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                            mContext.getString(com.filemanager.common.R.string.compressing), false))
                } else {
                    val level = (currentSize * MAX_COMPRESS / mFileTotalSize).toInt()
                    notifyObserver(UPDATE_PROGRESS, level)
                }
            }

            override fun onCompressDone(result: Boolean) {
                compressResult = result
                if (result) {
                    if (Utils.isOperateDatabase(appContext, baseFileBean.mData)) {
                        MediaScannerCompat.sendMediaScanner(baseFileBean.mData, Utils.MEDIA_SCAN_COMPRESS)
                    }
                    val fileServiceAction = Injector.injectFactory<IFileService>()
                    fileServiceAction?.syncOperate(OPERATE_TYPE_COMPRESS, hashSetOf(file.parent))
                } else {
                    notifyObserver(SHOW_COMPRESS_FAILED_TOAST)
                    deleteFileAfterFailed()
                }
            }

            override fun onCompressCancelled() {
                deleteFileAfterFailed()
            }

            private fun deleteFileAfterFailed() {
                if (JavaFileHelper.delete(baseFileBean)) {
                    FileMediaHelper.deleteMediaDBFile(baseFileBean)
                }
            }
        })
        return compressResult
    }

    private fun validateFileSizeTooLarge(): Boolean {
        notifyObserver(
            SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                mContext.getString(com.filemanager.common.R.string.string_being_calculated), true
        ), delayInMill = 300)
        mFileTotalSize = 0
        mOperateFiles.forEach {
            mFileTotalSize += JavaFileHelper.fileTotalSize(it)
        }
        cancelNotifyObserver(SHOW_PROGRESS)
        notifyObserver(DISMISS_PROGRESS)

        val result = checkDestStorageSpace(mDestParentFile, mFileTotalSize)
        if (result.first) {
            notifyObserver(NOTICE_COMPRESS_SPACE_NOT_ENOUGH, result.second)
            return true
        }

        val multiple: Long = mFileTotalSize / MAX_COMPRESS_SIZE
        Log.d(TAG, "validateFileTotalSize: multiple=$multiple, allMaxMultiple=$MAX_COMPRESS_MULTIPLE")
        return if (multiple > MAX_COMPRESS_MULTIPLE) {
            notifyObserver(NOTICE_COMPRESS_FILESIZE_TOO_LARGE)
            true
        } else {
            false
        }
    }

    private fun getCompressFileName(): String {
        // Now only support compress files to zip file
        return mCompressFileName.plus(".zip")
    }

    override fun afterRun(result: Boolean) {
        if (result) {
            // If compress in the same dir, just need to show a normal toast
            val sameDir = mOperateFiles.isNotEmpty() && (mOperateFiles[0].mData?.let { File(it).parentFile?.equals(
                mDestParentFile.mData?.let { it1 -> File(it1) }) } ?: false)
            notifyObserver(ACTION_DONE, if (sameDir) null else mDestParentFile.mData!!)
        } else {
            notifyObserver(ACTION_FAILED)
        }
    }

    override fun recycle() {
        mCompressHelper = null
        mOperateFiles.clear()
    }
}