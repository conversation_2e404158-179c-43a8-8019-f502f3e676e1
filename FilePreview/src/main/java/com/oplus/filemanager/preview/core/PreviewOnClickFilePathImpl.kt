/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - PreviewOnClickFilePathImpl.kt
 * Description:
 *     The common implementation for the action of click file path on preview page.
 *
 * Version: 1.0
 * Date: 2024-10-17
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-17   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.core

import android.app.Activity
import android.content.Intent
import android.view.View
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.detail.FileDetailDialogAdapter
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.filepreview.IFilePreviewFragment
import com.oplus.filemanager.interfaze.main.IMain
import java.io.File

/**
 * Refer to FileDetailDialogAdapter in FileOperate module
 */
internal class PreviewOnClickFilePathImpl(val activity: Activity) :
    IFilePreviewFragment.OnClickFilePathListener {

    private companion object {
        private const val TAG = "PreviewOnClickFilePathImpl"
        private const val FILE_BROWSER_ACTION = "oplus.intent.action.filemanager.BROWSER_FILE"
        private const val OTG_ACTIVITY_NAME = "com.oplus.filebrowser.otg.OtgFileBrowserActivity"
        private const val FILE_BROWSER_ACTIVITY_NAME = "com.oplus.filebrowser.FileBrowserActivity"
    }

    private val mainAction: IMain? by lazy { Injector.injectFactory<IMain>() }

    override fun onClickFilePath(view: View, path: String?) {
        path ?: return
        Log.d(TAG, "onClickFilePath: act=${activity.componentName}")
        if (mainAction?.let { it.isParentChildActivity(activity) && !it.isRecentFragment(activity) } != true) {
            val name = activity.componentName.className
            if (name == FILE_BROWSER_ACTIVITY_NAME || name == OTG_ACTIVITY_NAME) {
                Log.d(TAG, "onClickFilePath: ignore since already in file browser page")
                return
            }
            Log.d(TAG, "onClickFilePath: enterFileBrowserActivity")
            enterFileBrowserActivity(path)
            return
        }
        if (mainAction?.isStorageFragment(activity)?.not() == true) {
            Log.d(TAG, "onClickFilePath: toFileBrowserActivity")
            val fileBrowser = Injector.injectFactory<IFileBrowser>()
            fileBrowser?.toFileBrowserActivity(
                activity,
                path.substring(0, path.lastIndexOf(File.separator)),
                true
            )
            return
        }
        Log.d(TAG, "onClickFilePath: ignore since can not jump")
    }

    private fun enterFileBrowserActivity(myPath: String) {
        val intent = Intent()
        val isOTGPath = VolumeEnvironment.isOTGPath(activity, myPath)
        val isSdcardPath = VolumeEnvironment.isSdcardPath(activity, myPath)
        if (isOTGPath || isSdcardPath) {
            val otgList: ArrayList<String> = ArrayList()
            intent.setClassName(activity.packageName, OTG_ACTIVITY_NAME)
            otgList.add(myPath.substring(0, myPath.lastIndexOf(File.separator)))
            intent.putStringArrayListExtra(FileDetailDialogAdapter.OTG_LIST_PATH, otgList)
            intent.putExtra(Constants.TITLE_RES_ID, com.filemanager.common.R.string.storage_otg)
            intent.putExtra(
                Constants.TITLE,
                activity.getString(com.filemanager.common.R.string.storage_otg)
            )
        } else {
            intent.action = FILE_BROWSER_ACTION
            intent.putExtra(
                KtConstants.P_CURRENT_PATH,
                myPath.substring(0, myPath.lastIndexOf(File.separator))
            )
        }
        intent.putExtra(KtConstants.FROM_DETAIL, true)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        activity.startActivity(intent)
    }
}