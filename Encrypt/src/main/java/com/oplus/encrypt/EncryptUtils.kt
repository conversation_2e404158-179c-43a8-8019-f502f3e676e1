/*********************************************************************
 * * Copyright (C), 2020, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/5/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.encrypt

import android.app.Activity
import com.filemanager.common.R
import com.filemanager.common.constants.EncryptConstants
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.SdkUtils.isAtLeastS

object EncryptUtils {
    val sUseOplusEcryptManager = isAtLeastS()

    fun checkPrivateSafeEnabled(activity: Activity): Boolean {
        val packageName = if (AppUtils.checkApkInstalledByPackageName(
                activity,
                EncryptConstants.FILE_ENCRYPTION_PACKGE_OPLUS
            )
        ) {
            EncryptConstants.FILE_ENCRYPTION_PACKGE_OPLUS
        } else {
            EncryptConstants.FILE_ENCRYPTION_PACKGE
        }
        return KtAppUtils.checkAppEnabledWithDialog(
            activity,
            packageName,
            R.string.private_safe_disable_message
        )
    }
}