/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ServerConfig
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/2/20 11:15
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/2/20       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.utils

import com.filemanager.common.utils.Log
import com.oplus.filemanager.drivebrowser.BuildConfig
import org.jetbrains.annotations.VisibleForTesting

class ServerConfig {

    var apiUrl = HOST_API_URL_RELEASE
        private set

    var apiKey = API_KEY_RELEASE
        private set

    var apiSecret = API_SECRET_RELEASE
        private set

    var kdocsOpenUrl = HOST_KDOCS_OPEN_URL_RELEASE
        private set

    var kdocsRedirectUrl = HOST_KDOCS_AUTHORIZATION_REDIRECT_URL_RELEASE
        private set

    /**
     * Init docs environment.
     * When in debug mode use test environment, else use pre-release environment.
     *
     * **replace pre-release to release environment**.
     */
    fun initEnvironment() {
        val debugMode = BuildConfig.DEBUG
        Log.d(TAG, "initEnvironment -> debugMode = $debugMode")
        if (debugMode) {
            initPreReleaseEnvironment()
        } else {
            initReleaseEnvironment()
        }
    }

    @VisibleForTesting
    fun initPreReleaseEnvironment() {
        apiUrl = HOST_API_URL_PRE_RELEASE
        apiKey = API_KEY_PRE_RELEASE
        apiSecret = API_SECRET_PRE_RELEASE
        kdocsOpenUrl = HOST_KDOCS_OPEN_URL_PRE_RELEASE
        kdocsRedirectUrl = HOST_KDOCS_AUTHORIZATION_REDIRECT_URL_PRE_RELEASE
    }

    @VisibleForTesting
    fun initReleaseEnvironment() {
        apiUrl = HOST_API_URL_RELEASE
        apiKey = API_KEY_RELEASE
        apiSecret = API_SECRET_RELEASE
        kdocsOpenUrl = HOST_KDOCS_OPEN_URL_RELEASE
        kdocsRedirectUrl = HOST_KDOCS_AUTHORIZATION_REDIRECT_URL_RELEASE
    }

    @VisibleForTesting
    fun initTestEnvironment() {
        apiUrl = HOST_API_URL_TEST
        apiKey = API_KEY_TEST
        apiSecret = API_SECRET_TEST
        kdocsOpenUrl = HOST_KDOCS_OPEN_URL_TEST
        kdocsRedirectUrl = HOST_KDOCS_AUTHORIZATION_REDIRECT_URL_TEST
    }

    companion object {
        private const val TAG = "ServerConfig"

        // Release
        @VisibleForTesting
        const val HOST_API_URL_RELEASE = "https://owork-api-cn.allawntech.com"
        @VisibleForTesting
        const val API_KEY_RELEASE = "prd_key_d620908158cb1fdt"
        @VisibleForTesting
        const val API_SECRET_RELEASE = "6be39848de317bf95baeb3d4ff08b879326a8a0b3a4a5ec13792c5fc90ebe5po"
        @VisibleForTesting
        const val HOST_KDOCS_OPEN_URL_RELEASE = "$HOST_API_URL_RELEASE/mobile-third-doc/work"
        @VisibleForTesting
        const val HOST_KDOCS_AUTHORIZATION_REDIRECT_URL_RELEASE = "$HOST_API_URL_RELEASE/mobile-third-doc/login"

        // Pre-Release
        @VisibleForTesting
        const val HOST_API_URL_PRE_RELEASE = "https://preowork-api-cn.allawntech.com"
        @VisibleForTesting
        const val API_KEY_PRE_RELEASE = "pre_key_2b7382b1b3a4a5er"
        @VisibleForTesting
        const val API_SECRET_PRE_RELEASE = "6be39848de31f646d0fa7aa00597b1b2b7382b1b3a4a5ec13792c5fc90ebe53b"
        @VisibleForTesting
        const val HOST_KDOCS_OPEN_URL_PRE_RELEASE = "$HOST_API_URL_PRE_RELEASE/mobile-third-doc/work"
        @VisibleForTesting
        const val HOST_KDOCS_AUTHORIZATION_REDIRECT_URL_PRE_RELEASE =
            "$HOST_API_URL_PRE_RELEASE/mobile-third-doc/login"

        // Test
        @VisibleForTesting
        const val HOST_API_URL_TEST = "https://owork-api-cn-test.wanyol.com"
        @VisibleForTesting
        const val API_KEY_TEST = "test2108afed4eefac6dcaf2b2ded7bcfa22cb1ef8ac640cd96c794f568505ca"
        @VisibleForTesting
        const val API_SECRET_TEST = "c25af00b8a8872d17acb4e273adf7afac9e7f3e60b66f033c93c44f27d35d3a0"
        @VisibleForTesting
        const val HOST_KDOCS_OPEN_URL_TEST = "https://owork-test.wanyol.com/mobile-third-doc/work"
        @VisibleForTesting
        const val HOST_KDOCS_AUTHORIZATION_REDIRECT_URL_TEST =
            "https://owork-test.wanyol.com/mobile-third-doc/login"

        private var instance: ServerConfig? = null

        @Synchronized
        fun getInstance(): ServerConfig {
            if (instance == null) {
                instance = ServerConfig()
            }
            return instance!!
        }
    }
}