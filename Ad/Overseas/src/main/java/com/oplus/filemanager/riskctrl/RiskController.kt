/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: RiskController.kt
 ** Description: Risk Controller
 ** Version: 1.0
 ** Date : 2025/04/02
 ** Author: chao.xue
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 *  chao.xue 2025/04/02   1.0    create
 ****************************************************************/
package com.oplus.filemanager.riskctrl

import android.app.Application
import android.os.Looper
import androidx.annotation.MainThread
import com.filemanager.common.BuildConfig
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.heytap.cli.riskctrl.api.CommandResultCode
import com.heytap.cli.riskctrl.api.ParseResultCode
import com.heytap.cli.riskctrl.api.RiskControlAction
import com.heytap.cli.riskctrl.api.RiskControlCommand
import com.heytap.cli.riskctrl.api.RiskControlListener
import com.heytap.cli.riskctrl.api.RiskControlSdk
import com.oplus.filemanager.compat.brand.oppo.AdvertInitHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

object RiskController {

    private const val TAG = "file_ad_RiskController"
    private const val AD_ID = "ad_"

    /**
     * 初始化
     */
    @JvmStatic
    fun init(app: Application) {
        if (isMainThread()) {
            initSdk(app)
        } else {
            MainScope().launch(Dispatchers.Main) {
                initSdk(app)
            }
        }
    }

    /**
     * 初始化sdk
     *
     */
    @MainThread
    @JvmStatic
    private fun initSdk(app: Application) {
        val isDebug = BuildConfig.DEBUG || PropertyCompat.sLogEnable
        Log.d(TAG, "initSdk debug:$isDebug")
        val riskCtlSdk = RiskControlSdk.obtain()
        val region = AdvertInitHelper().getRegion(app)
        riskCtlSdk.initSdk(app, object : RiskControlListener {

            override fun onParseResult(code: ParseResultCode, result: Map<String, RiskControlAction>?) {
                super.onParseResult(code, result)
                Log.d(TAG, "onParseResult code:$code result:$result")
            }

            /**
             * 网页广告屏蔽规则更新
             */
            override fun onAdBlockRuleUpdate(blockRuleMap: Map<String, String>) {
                Log.d(TAG, "onAdBlockRuleUpdate rule:$blockRuleMap")
            }

            /**
             * 缓存被清理
             */
            override fun onCacheClear(commandList: List<RiskControlCommand>) {
                Log.d(TAG, "onCacheClear :$commandList")
                commandList.forEach {
                    riskCtlSdk.notifyCommandResult(it.commandId, CommandResultCode.HANDLED, null)
                }
            }

            override fun onPushRecall(pushIds: List<String>, command: RiskControlCommand) {
                Log.d(TAG, "onPushRecall ids:$pushIds command:$command")
                riskCtlSdk.notifyCommandResult(command.commandId, CommandResultCode.HANDLED, null)
            }

            override fun onStrategyExpired() {
                Log.w(TAG, "onStrategyExpired")
            }
        }, region, isDebug, StatisticsUtils.APP_CODE_VALUE)
    }

    /**
     * 解析风控策略的json文件
     * @param strategyJson 协议的json字符串，参考 https://odocs.myoas.com/docs/Wr3DVe0LO0u2lxkJ
     */
    @JvmStatic
    fun parseStrategy(strategyJson: String) {
        Log.d(TAG, "parseStrategy")
        val riskCtlSdk = RiskControlSdk.obtain()
        if (isMainThread()) {
            MainScope().launch(Dispatchers.IO) {
                riskCtlSdk.parseStrategy(strategyJson)
            }
        } else {
            riskCtlSdk.parseStrategy(strategyJson)
        }
    }

    /**
     * 是否在主线程
     */
    private fun isMainThread(): Boolean {
        return Thread.currentThread() == Looper.getMainLooper().thread
    }

    /**
     * 是否处于风险控制当中
     */
    fun isRiskControl(moduleName: String): Boolean {
        val riskCtlSdk = RiskControlSdk.obtain()
        val action = riskCtlSdk.getRiskControlAction(moduleName)
        Log.d(TAG, "isRiskControl module:$moduleName action:$action")
        return RiskControlAction.UNDEFINED != action
    }

    /**
     * 是否广告被拦截了
     */
    fun isBlockAd(moduleName: String, adUrl: String?): Boolean {
        val riskCtlSdk = RiskControlSdk.obtain()
        val action = riskCtlSdk.getRiskControlAction(moduleName)
        if (action != RiskControlAction.BLOCK_AD) {
            Log.w(TAG, "isBlockAd module:$moduleName action:$action")
            return false
        }
        if (adUrl.isNullOrEmpty()) {
            Log.w(TAG, "isBlockAd url is null")
            return true
        }
        val ruleJson = riskCtlSdk.getAdBlockRule(moduleName) ?: return false
        val rules = RiskStrategyFactory.parseBlockAdRules(ruleJson)
        rules.forEach { rule ->
            if (rule.isBlock(adUrl)) {
                Log.w(TAG, "isBlockAd block rule:$rule")
                return true
            }
        }
        return false
    }

    /**
     * 是否广告模块被下架
     */
    @JvmStatic
    fun isAdModuleRemoved(adId: String): Boolean {
        val riskCtl = RiskControlSdk.obtain()
        val action = riskCtl.getRiskControlAction("$AD_ID$adId")
        Log.w(TAG, "isBlockAd $adId : $action")
        return RiskControlAction.BLOCK_MODULE == action
    }


    /**
     * 清理策略
     */
    @JvmStatic
    fun clearStrategy() {
        Log.w(TAG, "clearStrategy")
        val riskCtlSdk = RiskControlSdk.obtain()
        riskCtlSdk.clearStrategy()
    }

    /**
     * 释放
     */
    @JvmStatic
    fun release() {
        Log.w(TAG, "release")
        val riskCtlSdk = RiskControlSdk.obtain()
        riskCtlSdk.destroy()
    }
}