/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.adapter
 * * Version     : 1.0
 * * Date        : 2020/5/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.compresspreview.ui

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import com.filemanager.common.base.BaseFolderAnimAdapter
import com.filemanager.common.base.BaseSelectionViewHolder
import com.filemanager.common.base.CheckBoxAnimateInput
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.TextViewSnippet
import com.filemanager.compresspreview.R
import com.filemanager.fileoperate.decompress.BaseDecompressFile

class CompressPreviewAdapter(
    content: Context,
    lifecycle: Lifecycle,
    private val overridePaddingStart: Int? = null
) : BaseFolderAnimAdapter<CompressPreviewAdapter.ViewHolder, BaseDecompressFile>(content),
    LifecycleObserver {

    private var recyclerItemClickListener: OnRecyclerItemClickListener? = null

    init {
        lifecycle.addObserver(this)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        super.onRemoveCallBack()
    }

    fun setOnRecyclerItemClickListener(onRecyclerItemClickListener: OnRecyclerItemClickListener) {
        recyclerItemClickListener = onRecyclerItemClickListener
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = LayoutInflater.from(parent.context).inflate(R.layout.decompress_preview_item, parent, false)
        return ViewHolder(v).also(::overrideItemLayout)
    }

    private fun overrideItemLayout(itemHolder: ViewHolder) {
        val overrideStart = overridePaddingStart ?: return
        val lp = itemHolder.img.layoutParams as ViewGroup.MarginLayoutParams
        lp.marginStart = overrideStart
        itemHolder.img.layoutParams = lp
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val file = mFiles[position]
        holder.updateKey(getItemKey(file, position))
        holder.updateDividerVisible(mFiles.size - 1, position)
        if (recyclerItemClickListener != null) {
            holder.itemView.setOnClickListener {
                recyclerItemClickListener?.onItemClick(holder.itemView, holder.layoutPosition)
            }
            holder.itemView.setOnLongClickListener {
                recyclerItemClickListener?.onItemLongClick(holder.itemView, holder.layoutPosition)
                true
            }
        }
        doData(file, holder, position)
    }

    private fun doData(file: BaseDecompressFile?, holder: ViewHolder, position: Int) {
        holder.title.visibility = View.VISIBLE
        if (file == null) {
            return
        }
        holder.title.text = file.mDisplayName
        when (file.mLocalType) {
            MimeTypeHelper.IMAGE_TYPE -> {
                val imageIconRes = KtThumbnailHelper.getClassifyResId(MimeTypeHelper.IMAGE_TYPE)
                holder.img.setImageResource(imageIconRes)
            }
            MimeTypeHelper.VIDEO_TYPE -> {
                val videoIconRes = KtThumbnailHelper.getClassifyResId(MimeTypeHelper.VIDEO_TYPE)
                holder.img.setImageResource(videoIconRes)
            }
            MimeTypeHelper.COMPRESSED_TYPE -> {
                val compressedType = MimeTypeHelper.getCompressedTypeByPath(file.mDisplayName)
                val compressIcon = KtThumbnailHelper.getClassifyDrawable(mContext, compressedType)
                holder.img.setImageDrawable(compressIcon)
            }
            else -> {
                val autoIcon = KtThumbnailHelper.getClassifyDrawable(mContext, file.mLocalType)
                holder.img.setImageDrawable(autoIcon)
            }
        }
        if (file.mIsDirectory) {
            //file.childCount.toString().plus(mFileNumbers)
            val counts = mContext.resources.getQuantityString(com.filemanager.common.R.plurals.text_x_items, file.childCount, file.childCount)
            if (file.mDateModified <= 0) {
                holder.detail.text = counts
            } else {
                val dateAndTime = Utils.getDateFormat(mContext, file.mDateModified)
                holder.detail.text = Utils.formatDetail(mContext, counts, dateAndTime)
            }
        } else {
            val size = Utils.byteCountToDisplaySize(file.mSize)
            val dateAndTime = Utils.getDateFormat(mContext, file.mDateModified)
            if (!TextUtils.isEmpty(size)) {
                holder.detail.text = Utils.formatDetail(mContext, size, dateAndTime)
            } else {
                holder.detail.text = Utils.formatDetail(mContext, "", dateAndTime)
            }
        }
        holder.mCheckBox?.let { setCheckBoxAnim(CheckBoxAnimateInput(file.mIsDirectory, mChoiceMode, holder.jumpImg, it, position)) }
        if (mChoiceMode) {
            if (!mIsAnimRunning) {
                holder.jumpImg.setVisibility(View.INVISIBLE)
            }
        } else {
            if (file.mIsDirectory) {
                holder.jumpImg.setVisibility(View.VISIBLE)
            } else {
                if (!mIsAnimRunning) {
                    holder.jumpImg.setVisibility(View.INVISIBLE)
                }
            }
        }
    }

    override fun getItemId(position: Int): Long {
        return getItemKeyByPosition(position)?.toLong() ?: com.oplus.dropdrag.SelectionTracker.NO_LONG_ITEM_ID
    }

    override fun getItemKey(item: BaseDecompressFile, position: Int): Int? {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return null
        }

        return path.hashCode()
    }

    override fun getItemKeyByPosition(position: Int): Int? {
        val item = getItem(position) ?: return null
        return getItemKey(item, position)
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        if (mScanViewModel == KtConstants.SCAN_MODE_LIST) {
            setChoiceModeAnimFlag(flag)
        }
    }

    class ViewHolder(convertView: View) : BaseSelectionViewHolder(convertView) {
        val img: ImageView = convertView.findViewById(R.id.file_list_item_icon)
        val jumpImg: ImageView = convertView.findViewById(R.id.jump_mark)
        val title: TextViewSnippet = convertView.findViewById(R.id.file_list_item_title)
        val detail: TextView = convertView.findViewById(R.id.mark_file_list_item_detail)

        init {
            mCheckBox = convertView.findViewById(R.id.listview_scrollchoice_checkbox)
            dividerLine = convertView.findViewById(R.id.divider_line)
        }

        override fun isInDragRegionImpl(event: MotionEvent): Boolean = false
    }
}