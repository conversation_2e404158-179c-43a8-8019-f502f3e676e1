/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AuthorizationState
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/5 19:40
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2023/12/5       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.model

import com.google.gson.annotations.SerializedName

data class AuthorizationState(@SerializedName("auth") var auth: Boolean? = false)