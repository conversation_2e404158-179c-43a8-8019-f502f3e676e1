package com.oplus.filemanager.preview.apk

import android.content.Context
import android.view.View
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewFragment
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import com.oplus.filemanager.preview.utils.PreviewScrollAreaHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite
import com.oplus.filemanager.preview.widget.PreviewFilePathItem
import java.lang.reflect.Method
import org.junit.Assert.assertNull
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertTrue

/**
 * ApkPreviewFragment的单元测试类
 * 用于测试ApkPreviewFragment的各个功能点
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class ApkPreviewFragmentTest {

    // 测试用的协程调度器
    private val testDispatcher = TestCoroutineDispatcher()
    // 上下文对象
    private lateinit var context: Context
    // 被测试的Fragment实例
    private lateinit var fragment: ApkPreviewFragment
    // 模拟的文件Bean对象
    private lateinit var mockFileBean: BaseFileBean
    // 模拟的文件类型检查帮助类
    private lateinit var mockTypeCheckHelper: FilePreviewTypeCheckHelper
    // 模拟的滚动区域帮助类
    private lateinit var mockScrollHelper: PreviewScrollAreaHelper
    // 模拟的文件信息展示组件
    private lateinit var mockFileInfoSuite: PreviewFileInfoSuite
    // 模拟的文件路径展示项
    private lateinit var mockFilePathItem: PreviewFilePathItem

    /**
     * 测试前的初始化方法
     * 1. 设置主线程调度器
     * 2. 初始化上下文
     * 3. 创建模拟对象
     * 4. 实例化被测试的Fragment
     */
    @Before
    fun setUp() {
        Dispatchers.setMain(testDispatcher)
        context = RuntimeEnvironment.getApplication()
        mockFileBean = mockk(relaxed = true)
        mockTypeCheckHelper = mockk(relaxed = true)
        mockScrollHelper = mockk(relaxed = true)
        mockFileInfoSuite = mockk(relaxed = true)
        mockFilePathItem = mockk(relaxed = true)
        fragment = ApkPreviewFragment()
    }

    /**
     * 测试后的清理方法
     * 1. 清除所有模拟对象
     * 2. 重置主线程调度器
     * 3. 清理测试协程
     * 4. 调用Fragment的销毁方法
     */
    @After
    fun tearDown() {
        clearAllMocks()
        Dispatchers.resetMain()
        testDispatcher.cleanupTestCoroutines()
        fragment.onDestroyView()
    }

    /**
     * 测试onCreateFilePathItem方法
     * 验证该方法会调用setLabelGone()隐藏标签
     */
    @Test
    fun `onCreateFilePathItem should set label gone`() {
        // 模拟View对象
        val mockView: View = mockk(relaxed = true)
        // 设置当调用findViewById时返回模拟的路径项
        every { mockView.findViewById<PreviewFilePathItem>(R.id.preview_remote_location_info) } returns mockFilePathItem

        // 通过反射获取私有方法
        val method: Method = ApkPreviewFragment::class.java.getDeclaredMethod(
            "onCreateFilePathItem", 
            View::class.java
        )
        method.isAccessible = true

        // 调用被测试方法
        method.invoke(fragment, mockView)

        // 验证setLabelGone方法被调用了一次
        verify(exactly = 1) { mockFilePathItem.setLabelGone() }
    }

    /**
     * 测试onUpdateUIWhenConfigChange方法
     * 验证该方法会调用滚动帮助类的checkShowDivider方法
     */
    @Test
    fun `onUpdateUIWhenConfigChange should invoke scroll helper`() {
        // 通过反射设置Fragment的scrollAreaHelper字段为模拟对象
        fragment.javaClass.getDeclaredField("scrollAreaHelper").apply {
            isAccessible = true
            set(fragment, mockScrollHelper)
        }
        // 创建模拟的配置集合
        val mockConfigs: MutableCollection<IUIConfig> = mutableListOf()

        // 通过反射获取私有方法
        val method: Method = ApkPreviewFragment::class.java.getDeclaredMethod(
            "onUpdateUIWhenConfigChange", 
            MutableCollection::class.java
        )
        method.isAccessible = true

        // 调用被测试方法
        method.invoke(fragment, mockConfigs)

        // 验证checkShowDivider方法被调用了一次
        verify(exactly = 1) { mockScrollHelper.checkShowDivider() }
    }
}