/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudFileSortTypeUtils
 ** Description : 云文档排序类型的工具类：腾讯文档和金山文档的排序类型和排序字段都不一样，用于处理排序类型的
 ** Version     : 1.0
 ** Date        : 2024/01/12 11:12
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/01/12       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.utils

import com.filemanager.common.MyApplication
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory

object CloudFileSortTypeUtils {

    /**
     * 腾讯文档最近浏览
     */
    const val TENCENT_DOCS_BROWSE = "browse"

    /**
     * 腾讯文档 最近编辑
     */
    const val TENCENT_DOCS_MODIFY = "modify"

    /**
     * 腾讯文档 按标题
     */
    const val TENCENT_DOCS_TITLE = "title"

    /**
     * 金山文档按照修改时间
     */
    const val KDOCS_MODIFY_TIME = "mtime"

    /**
     * 金山文档按照文件名称
     */
    const val KDOCS_NAME = "fname"

    /**
     * 金山文档按照文件大小
     */
    const val KDOCS_SIZE = "fsize"

    private const val DEFAULT_ASC = 1

    private const val DEFAULT_DESC = 0

    /**
     * 获取排序类型
     */
    @JvmStatic
    fun getSortType(category: Int, sortMode: Int): String {
        return if (category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
            getTencentDocSortType(sortMode)
        } else {
            getKingSoftDocSortType(sortMode)
        }
    }

    /**
     * 获取腾讯文档的排序类型
     */
    @JvmStatic
    fun getTencentDocSortType(sortMode: Int): String {
        return when (sortMode) {
            SortHelper.FILE_LAST_OPEN_TIME_ORDER -> TENCENT_DOCS_BROWSE // 打开时间
            SortHelper.FILE_TIME_REVERSE_ORDER -> TENCENT_DOCS_MODIFY // 修改时间
            SortHelper.FILE_NAME_ORDER -> TENCENT_DOCS_TITLE // 名称
            else -> TENCENT_DOCS_BROWSE
        }
    }

    /**
     * 获取金山文档的排序类型
     */
    @JvmStatic
    fun getKingSoftDocSortType(sortMode: Int): String {
        return when (sortMode) {
            SortHelper.FILE_TIME_REVERSE_ORDER -> KDOCS_MODIFY_TIME // 修改时间
            SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER -> KDOCS_SIZE // 大小
            SortHelper.FILE_NAME_ORDER -> KDOCS_NAME // 名称
            else -> KDOCS_MODIFY_TIME
        }
    }

    /**
     * 获取默认的sortType
     * 腾讯文档默认是 browser
     * 金山文档默认是 mtime
     * @param category 类型，值为腾讯文档还是金山文档
     * @return Pair first代表排序类型，second代表正序还是倒序
     */
    @JvmStatic
    fun getDefaultSortType(category: Int): Pair<String, Int> {
        val recordMode = SortRecordModeFactory.getCloudFileKey(category)
        val sort = SortModeUtils.getSharedSortMode(MyApplication.sAppContext, recordMode)
        val isDesc = SortModeUtils.getSharedSortOrder(recordMode)
        val sortMode = getSortType(category, sort)
        return if (isDesc) {
            Pair(sortMode, DEFAULT_DESC)
        } else {
            Pair(sortMode, DEFAULT_ASC)
        }
    }
}