<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundWithCard"
    android:clipChildren="false"
    android:orientation="vertical"
    android:splitMotionEvents="false">

    <com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller
        android:id="@+id/fastScroller"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:common_track_marginBottom="@dimen/ftp_text_margin_bottom"
        app:common_track_marginEnd="@dimen/base_album_fastscroller_margin_end"
        app:common_track_marginTop="@dimen/base_album_recyclerview_padding_top">

        <com.filemanager.common.view.FileManagerRecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller>

    <ViewStub
        android:id="@+id/common_permission_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/permission_common_view_layout" />

    <include layout="@layout/appbar_with_pathbar_sort_layout_secondary" />


    <com.filemanager.common.view.FeedbackFloatingButton
        android:id="@+id/add_folder_fab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginStart="@dimen/dimen_20dp"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:layout_marginEnd="@dimen/dimen_24dp"
        android:layout_marginBottom="@dimen/dimen_40dp"
        android:clipChildren="false"
        android:contentDescription="@string/menu_file_list_new_folder"
        android:elevation="0dp"
        android:forceDarkAllowed="false"
        android:transitionName="shared_element_fab"
        android:visibility="gone"
        app:mainFloatingButtonSrc="@drawable/ic_fab_add_label" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>