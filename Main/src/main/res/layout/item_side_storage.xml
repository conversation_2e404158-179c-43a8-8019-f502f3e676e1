<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/dimen_76dp"
    android:paddingStart="@dimen/dimen_12dp"
    android:paddingEnd="@dimen/dimen_12dp">

    <ImageView
        android:layout_marginStart="@dimen/dimen_12dp"
        android:id="@+id/icon_item"
        android:layout_width="@dimen/dimen_24dp"
        android:layout_height="@dimen/dimen_24dp"
        android:contentDescription="@null"
        android:src="@drawable/ic_storage_pad"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title_item"
        style="?android:attr/textAppearanceLarge"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_18dp"
        android:textAlignment="viewStart"
        app:layout_constraintBottom_toTopOf="@id/desc_item"
        app:layout_constraintEnd_toStartOf="@id/barrier"
        app:layout_constraintStart_toEndOf="@id/icon_item"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/desc_item"
        style="@style/COUIPreferenceSummaryStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_6dp"
        android:layout_marginBottom="@dimen/dimen_15dp"
        android:textAlignment="viewStart"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/title_item"
        app:layout_constraintStart_toStartOf="@id/title_item"
        app:layout_constraintTop_toBottomOf="@id/space_progress" />

    <View
        android:id="@+id/anchor_view"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="-6dp"
        android:layout_marginEnd="-6dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="start"
        app:constraint_referenced_ids="right_item" />

    <com.oplus.filemanager.main.view.HorizontalProgressBar
        android:id="@+id/space_progress"
        android:layout_width="0dp"
        android:layout_height="@dimen/dimen_4dp"
        android:layout_marginTop="@dimen/dimen_8dp"
        android:forceDarkAllowed="false"
        app:couiHorizontalProgressBarBackgroundColor="?attr/couiColorDivider"
        app:couiHorizontalProgressBarProgressColor="?attr/couiColorPrimary"
        app:layout_constraintEnd_toEndOf="@id/title_item"
        app:layout_constraintStart_toStartOf="@id/title_item"
        app:layout_constraintTop_toBottomOf="@id/title_item" />

    <com.oplus.filemanager.recent.view.PressFeedBackImage
        android:background="@drawable/hover_bg"
        android:layout_marginEnd="@dimen/dimen_12dp"
        android:id="@+id/right_item"
        android:layout_width="@dimen/dimen_30dp"
        android:layout_height="@dimen/dimen_30dp"
        android:forceDarkAllowed="false"
        android:scaleType="center"
        android:src="@drawable/bg_clear_parent"
        android:contentDescription="@string/garbage_cleanup"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>