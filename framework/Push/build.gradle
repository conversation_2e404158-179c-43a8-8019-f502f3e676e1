plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace 'com.oplus.filemanager.framework.push'
}

dependencies {
    implementation project(":Common")
    implementation libs.koin.android

    implementation libs.oplus.push
    implementation libs.apache.commons.codec
    implementation libs.google.gson
}