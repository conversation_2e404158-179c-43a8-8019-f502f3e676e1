/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : KDocsFileDriveRepository
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/25 17:00
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/25       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.repository.kdocs

import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.drivebrowser.data.api.KDocsFileDriveService
import com.oplus.filemanager.drivebrowser.data.model.AuthorizationPath
import com.oplus.filemanager.drivebrowser.data.model.BaseResponse
import com.oplus.filemanager.drivebrowser.data.model.kdocs.DocumentListsRequest
import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsBatchDeleteRequest
import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsDocumentItem
import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsDocumentListsResponse
import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsFileEditUrlResp
import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsRenameRequest
import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsSearchRequest
import com.oplus.filemanager.drivebrowser.data.repository.DocumentDownloadErrorCode
import com.oplus.filemanager.drivebrowser.data.utils.ServerConfig
import com.oplus.filemanager.drivebrowser.di.NetworkModule
import com.oplus.filemanager.drivebrowser.di.NetworkModule.BASE_URL
import com.oplus.filemanager.drivebrowser.domain.model.AuthorizationData
import com.oplus.filemanager.drivebrowser.domain.model.CloudDocumentItem
import com.oplus.filemanager.drivebrowser.domain.model.DocumentPageData
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.drivebrowser.download.DownloadProgressListener
import com.oplus.filemanager.drivebrowser.utils.DownloadController
import com.oplus.filemanager.drivebrowser.utils.doGetWithRequestInterceptor
import com.oplus.filemanager.drivebrowser.utils.writeFileToDisk
import org.apache.commons.io.FilenameUtils

class KDocsFileDriveRepository(
    private val service: KDocsFileDriveService
) : FileDriveRepository {

    private val controller = DownloadController()

    override suspend fun checkAuthorizationState(): Boolean {
        val isAuthorization = runCatching {
            service.checkAuthorizationState()
        }.getOrNull()
        Log.d(TAG, "checkAuthorizationState data = ${isAuthorization?.data}")
        return isAuthorization?.data?.auth ?: false
    }

    override suspend fun getAuthUrl(): AuthorizationData? {
        var exception: Throwable? = null
        val authInfo = runCatching {
            service.buildAuthorizationPath()
        }.onFailure {
            exception = it
        }.getOrNull()
        if (authInfo?.data == null) {
            return AuthorizationData(false, "", exception)
        }
        return authInfo.data?.let {
            AuthorizationData(it.auth, it.authUrl, exception)
        }
    }

    override suspend fun saveAuthorizationResult(code: String, state: String): Pair<Boolean, String> {
        var error = ""
        val result = runCatching {
            service.callbackAuth(code, state).apply {
                error = this.msg ?: ""
            }.code == 0
        }.getOrDefault(false)
        return Pair(result, error)
    }

    override suspend fun cancelAuth(): Pair<Boolean, String> {
        var error = ""
        val result = runCatching {
            service.cancelAuth().apply {
                error = this.msg ?: ""
            }.code == 0
        }.getOrDefault(false)
        return Pair(result, error)
    }

    override suspend fun getWebViewAuthUrl(): AuthorizationData? {
        var exception: Throwable? = null
        val authInfo = runCatching {
            service.buildWebViewAuthorizationPath(ServerConfig.getInstance().kdocsRedirectUrl).data
        }.onFailure {
            exception = it
        }.getOrDefault(AuthorizationPath(false, ""))
        return authInfo?.let {
            Log.d(TAG, "getWebViewAuthUrl it.auth = ${it.auth}, ${it.authUrl}, $exception")
            AuthorizationData(it.auth, it.authUrl, exception)
        }
    }

    override suspend fun getDocumentLists(
        sortType: String,
        asc: Int,
        start: Int,
        limit: Int,
        folderId: String
    ): BaseResponse<DocumentPageData> {
        val respData = BaseResponse<DocumentPageData>()
        val result = DocumentPageData()
        val orderBy = DocumentListsRequest.orderBy(asc)
        val request = DocumentListsRequest(limit, start, orderBy, sortType, folderId, "")
        val data = runCatching {
            service.getDocumentLists(request).apply {
                respData.codeMsg(this.code, this.msg)
            }.data
        }.getOrDefault(KDocsDocumentListsResponse())
        data?.let {
            val list = mutableListOf<CloudDocumentItem>()
            it.list.forEach { document ->
                if (filterDocumentFile(document)) {
                    val item = convertCloudDocumentItem(document, folderId)
                    list.add(item)
                } else {
                    Log.i(TAG, "not document! document = ${document.fname}")
                }
            }
            result.lists = list
            result.isLastPage = (it.nextOffset == -1)
            result.nextPageOffset = it.nextOffset
        }
        respData.data = result
        return respData
    }

    /**
     * 1,过滤掉非文档的文件，要不然会显示很对奇怪的文件，图片、压缩包等
     * 2,过滤掉拓展格式文档，不然金山会打不开文件
     */
    @VisibleForTesting
    fun filterDocumentFile(item: KDocsDocumentItem): Boolean {
        val type = item.ftype
        if (type == KDocsDocumentItem.KDOCS_TYPE_FOLDER) {
            return true
        }
        if (type == KDocsDocumentItem.KDOCS_TYPE_SHARE_FOLDER) {
            return true
        }

        val extension = FilenameUtils.getExtension(item.fname)
        if (extension == KDocsDocumentItem.KDOCS_TYPE_OTL
            || extension == KDocsDocumentItem.KDOCS_TYPE_KSHEET
            || extension == KDocsDocumentItem.KDOCS_TYPE_DBT
        ) {
            return true
        }

        val localType = MimeTypeHelper.getTypeFromExtension(extension) ?: MimeTypeHelper.UNKNOWN_TYPE
        Log.d(TAG, "filterFDocumentFile type = $extension, localType = $localType")
        return MimeTypeHelper.isDocType(localType)
    }

    override suspend fun renameDocument(fileId: String, title: String): BaseResponse<String> {
        val result = runCatching {
            val request = KDocsRenameRequest(fileId, title)
            service.renameDocument(request)
        }.getOrDefault(null)
        val respData = BaseResponse<String>()
        result?.let {
            respData.data = it.data as? String
            respData.codeMsg(it.code, it.msg)
        }
        return respData
    }

    override suspend fun renameFolder(folderId: String, title: String): BaseResponse<String> {
        val result = runCatching {
            val request = KDocsRenameRequest(folderId, title)
            service.renameDocument(request)
        }.getOrDefault(null)
        val respData = BaseResponse<String>()
        result?.let {
            respData.data = it.data as? String
            respData.codeMsg(it.code, it.msg)
        }
        return respData
    }

    override suspend fun deleteFile(fileId: String, listType: String): Pair<Boolean, String> {
        var error = ""
        val result = runCatching {
            service.deleteFile(fileId).apply {
                error = this.msg ?: ""
            }.code == 0
        }.getOrDefault(false)
        return Pair(result, error)
    }

    override suspend fun deleteFiles(files: List<DriveFileWrapper>): Pair<Boolean, String> {
        var error = ""
        var result = true
        runCatching {
            files.chunked(MAX_DELETE_SIZE).forEach { sublist ->
                val fileIds = sublist.map { it.id }
                val batchDeleteRequest = KDocsBatchDeleteRequest(fileIds)
                result = service.deleteFiles(batchDeleteRequest).apply {
                    error = this.msg ?: ""
                }.code == 0
                if (!result) {
                    return@runCatching
                }
            }
        }.getOrDefault(false)
        return Pair(result, error)
    }

    override suspend fun deleteFolder(folderId: String): Pair<Boolean, String> {
        var error = ""
        val result = runCatching {
            service.deleteFile(folderId).apply {
                error = this.msg ?: ""
            }.code == 0
        }.getOrDefault(false)
        return Pair(result, error)
    }

    override suspend fun getEditUrl(fileId: String): String {
        val result = runCatching {
            service.getCustomEditUrl(fileId).data
        }.onFailure {
            Log.e(TAG, "getEditUrl = ${it.message}")
        }.getOrDefault(KDocsFileEditUrlResp())
        result?.let {
            return NetworkModule.buildKingDocsOpenUrl(it.token?.value ?: "", it.url)
        }
        return ""
    }

    suspend fun getEditUrlResponse(fileId: String): BaseResponse<KDocsFileEditUrlResp>? {
        return runCatching {
            service.getCustomEditUrl(fileId)
        }.onFailure {
            Log.e(TAG, "getEditUrlResponse = ${it.message}")
        }.getOrDefault(null)
    }

    private fun convertCloudDocumentItem(it: KDocsDocumentItem, folderId: String): CloudDocumentItem {
        return CloudDocumentItem(folderId, it.id?.openId ?: "", it.fname, it.ftype, "", it.ctime, it.mtime)
    }

    override suspend fun downloadFile(
        fileId: String,
        targetPath: String,
        exportType: String?,
        listener: DownloadProgressListener
    ) {
        Log.d(TAG, "downloadFile -> fileId = $fileId, targetPath: $targetPath")
        val filePermission = runCatching {
            service.checkFilePermission(fileId).code == 0
        }.getOrDefault(false)
        if (!filePermission) {
            Log.d(TAG, "downloadFile -> no permission")
            listener.onError(DocumentDownloadErrorCode.NO_PERMISSION_ERROR)
            return
        }
        download(fileId, targetPath, listener)
    }

    override fun cancelDownload() {
        controller.pause()
    }

    private fun download(fileId: String, targetPath: String, listener: DownloadProgressListener) {
        runCatching {
            val params = mutableMapOf<String, String>()
            params[KEY_FILE_ID] = fileId
            params[KEY_FILE_NAME] = FilenameUtils.getName(targetPath)
            doGetWithRequestInterceptor(
                appContext,
                DOCUMENT_DOWNLOAD_URL,
                params,
                null,
                CONNECT_TIMEOUT,
                READ_TIMEOUT,
                listener,
                controller
            )?.let { response ->
                Log.d(TAG, "download -> isSuccessful = ${response.isSuccessful}")
                if (response.isSuccessful) {
                    response.use {
                        it.body?.let { responseBody ->
                            val writeResult = writeFileToDisk(responseBody, targetPath)
                            if (!writeResult) {
                                listener.onError(DocumentDownloadErrorCode.SAVE_ERROR)
                            }
                        }
                    }
                }
            }
        }.onFailure {
            listener.onError(DocumentDownloadErrorCode.UNKNOWN_ERROR)
        }
    }

    override suspend fun searchFile(searchKey: String, page: Int): DocumentPageData {
        val res = DocumentPageData()
        val request = KDocsSearchRequest.create(searchKey, page)
        val data = runCatching {
            service.searchFile(request).data
        }.onFailure {
            Log.w(TAG, "searchFile error", it)
        }.getOrDefault(KDocsDocumentListsResponse())
        data?.let {
            val result = mutableListOf<CloudDocumentItem>()
            it.list.forEach { listItem ->
                if (filterDocumentFile(listItem)) {
                    val item = convertCloudDocumentItem(listItem, listItem.parent?.openId ?: "")
                    result.add(item)
                } else {
                    Log.i(TAG, "not document! document = ${listItem.fname}")
                }
            }
            // 金山的返回值中只有list, nextOffset 一直为0
            res.lists = result
        }
        return res
    }

    override suspend fun isFileAuth(fileId: String): Boolean {
        return runCatching {
            service.getPermissionFile(fileId).code != AUTH_EXPIRED
        }.getOrDefault(true)
    }

    companion object {
        private const val TAG = "KDocsFileDriveRepository"
        private val DOCUMENT_DOWNLOAD_URL =
            "$BASE_URL/owork-server/mobile/king-docs/v1/person-space/download-file/forward"
        private const val CONNECT_TIMEOUT = 30000L
        private const val READ_TIMEOUT = 30000L
        private const val KEY_FILE_ID = "fileId"
        private const val KEY_FILE_NAME = "fileName"
        private const val MAX_DELETE_SIZE = 99

        //error code
        const val FILE_NOT_EXISTS = 2030012
        const val FILE_NAME_CONFLICT = 2030015
        //用户授权过期
        const val AUTH_EXPIRED = 10013
    }
}