/*********************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : TestUiViewUtils
 * Description :
 * Version     : 1.0
 * Date        : 2023/08/01
 * Author      : W9002848
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * W9002848        2023/08/09       1.0      create
 **********************************************************************/
package com.coloros.filemanager.utils

import android.os.RemoteException
import android.text.TextUtils
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiObject
import androidx.test.uiautomator.UiObjectNotFoundException
import androidx.test.uiautomator.UiSelector
import com.coloros.filemanager.utils.TestConstants.TIME_WAIT_ONE_SEC
import org.junit.Assert

object TestUiViewUtils {
    private val TAG = TestUiViewUtils::class.java.simpleName

    private val uiDevice: UiDevice by lazy( mode = LazyThreadSafetyMode.SYNCHRONIZED) {
        UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
    }

    /**
     * 唤醒屏幕, 并滑动解锁
     */
    fun screenWakeUp() {
        try {
            Assert.assertNotNull("The sDevice should be not null!", uiDevice)
            uiDevice?.wakeUp()
            uiDevice?.swipe(540, 2196, 540, 1196, 10)
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    /**
     * 获取Ui view 节点数据
     * @param msg
     * @param index
     * @param text
     * @param resourceId
     * @param className
     * @param pkgName
     * @param contentDesc
     * @return
     * @throws InterruptedException
     */
    @Throws(InterruptedException::class)
    fun getUiObject(
        msg: String?, index: Int, text: String?, resourceId: String?,
        className: String?, pkgName: String?, contentDesc: String?
    ): UiObject {
        Thread.sleep(TIME_WAIT_ONE_SEC)
        TestLog.d(
            TAG, String.format(
                "getUiObject: msg:%s, index:%s, text:%s, resourceId:%s, className:%s, pkgName:%s, contentDesc:%s",
                msg, index, text, resourceId, className, pkgName, contentDesc
            )
        )
        val uiSelector: UiSelector = getUiSelector(index, text, resourceId, className, pkgName, contentDesc)
        return uiDevice.findObject(uiSelector)
    }

    private fun getUiSelector(
        index: Int, text: String?, resourceId: String?,
        className: String?, pkgName: String?, contentDesc: String?
    ): UiSelector {
        val uiSelector = UiSelector()
        if (index != -1) {
            uiSelector.index(index)
        }
        if (!TextUtils.isEmpty(text)) {
            uiSelector.text(text)
        }
        if (!TextUtils.isEmpty(resourceId)) {
            uiSelector.resourceId(resourceId)
        }
        if (!TextUtils.isEmpty(className)) {
            uiSelector.className(className)
        }
        if (!TextUtils.isEmpty(pkgName)) {
            uiSelector.className(pkgName)
        }
        if (!TextUtils.isEmpty(contentDesc)) {
            uiSelector.description(contentDesc)
        }
        return uiSelector
    }

    /**
     * 封装UI view 节点点击事件
     * @param pkgName
     * @param viewClassName
     * @param resourceId
     * @param textContains
     * @param contentDesc
     * @param isMastExist
     * @throws Exception
     */
    @Throws(Exception::class)
    fun clickViewId(
        pkgName: String?, viewClassName: String?, resourceId: String?,
        textContains: String?, contentDesc: String?, isMastExist: Boolean
    ) {
        val uiDevice: UiDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
        val uiObject: UiObject
        uiObject = if (TextUtils.isEmpty(contentDesc)) {
            uiDevice.findObject(
                UiSelector()
                    .packageName(pkgName).className(viewClassName)
                    .resourceId(resourceId).textContains(textContains)
            )
        } else {
            uiDevice.findObject(
                UiSelector()
                    .packageName(pkgName).className(viewClassName)
                    .resourceId(resourceId).textContains(textContains).description(contentDesc)
            )
        }
        if (uiObject.exists()) {
            val click: Boolean = uiObject.click()
            Assert.assertTrue(
                String.format(
                    "The viewId is exists, but click failure. And pkgName:%s,"
                            + " viewClassName:%s, resourceId:%s, textContains:%s, contentDesc:%s",
                    pkgName, viewClassName, resourceId, textContains, contentDesc
                ), click
            )
            Thread.sleep(TIME_WAIT_ONE_SEC)
        } else {
            if (isMastExist) {
                Assert.fail(
                    String.format(
                        "The viewId is not exists, and pkgName:%s, "
                                + "viewClassName:%s, resourceId:%s, textContains:%s, contentDesc:%s",
                        pkgName, viewClassName, resourceId, textContains, contentDesc
                    )
                )
            }
        }
    }


    fun findTextOnClick(text: String?): Boolean {
        var click = false
        try {
            Thread.sleep(TIME_WAIT_ONE_SEC)
            val uiObject: UiObject = uiDevice.findObject(UiSelector().text(text))
            if (uiObject != null) {
                click = uiObject.click()
            }
        } catch (e: InterruptedException) {
            e.printStackTrace()
            TestLog.e(TAG, String.format("findTextOnClick %s click exception: %s", text, e.toString()))
        } catch (e: UiObjectNotFoundException) {
            e.printStackTrace()
            TestLog.e(TAG, String.format("findTextOnClick %s click exception: %s", text, e.toString()))
        }
        TestLog.e(TAG, String.format("findTextOnClick %s click is %s", text, click))
        return click
    }

    fun findContainsTextOnClick(text: String?): Boolean {
        var click = false
        try {
            Thread.sleep(TIME_WAIT_ONE_SEC)
            val uiObject: UiObject = uiDevice.findObject(UiSelector().textContains(text))
            if (uiObject != null) {
                click = uiObject.click()
            }
        } catch (e: InterruptedException) {
            e.printStackTrace()
            TestLog.e(TAG, String.format("findContainsTextOnClick %s click exception: %s", text, e.toString()))
        } catch (e: UiObjectNotFoundException) {
            e.printStackTrace()
            TestLog.e(TAG, String.format("findContainsTextOnClick %s click exception: %s", text, e.toString()))
        }
        TestLog.e(TAG, String.format("findContainsTextOnClick %s click is %s", text, click))
        return click
    }

    fun findTextExist(text: String?): Boolean {
        var isExist = false
        try {
            Thread.sleep(TIME_WAIT_ONE_SEC)
            val uiObject: UiObject = uiDevice.findObject(UiSelector().text(text))
            if (uiObject != null) {
                isExist = uiObject.exists()
            }
        } catch (e: InterruptedException) {
            e.printStackTrace()
            TestLog.e(TAG, String.format("findTextExist %s click exception: %s", text, e.toString()))
        }
        TestLog.e(TAG, String.format("findTextExist %s click is %s", text, isExist))
        return isExist
    }

    fun findContainsTextExist(text: String?): Boolean {
        var isExist = false
        try {
            Thread.sleep(TIME_WAIT_ONE_SEC)
            val uiObject: UiObject = uiDevice.findObject(UiSelector().textContains(text))
            if (uiObject != null) {
                isExist = uiObject.exists()
            }
        } catch (e: InterruptedException) {
            e.printStackTrace()
            TestLog.e(TAG, String.format("findContainsTextExist %s click exception: %s", text, e.toString()))
        }
        TestLog.e(TAG, String.format("findContainsTextExist %s click is %s", text, isExist))
        return isExist
    }

    fun findTextContentDescOnClick(text: String?): Boolean {
        var click = false
        try {
            Thread.sleep(TIME_WAIT_ONE_SEC)
            val uiObject: UiObject = uiDevice.findObject(UiSelector().descriptionContains(text))
            if (uiObject != null) {
                click = uiObject.click()
            }
        } catch (e: InterruptedException) {
            e.printStackTrace()
            TestLog.e(TAG, String.format("findTextContentDescOnClick %s click exception: %s", text, e.toString()))
        } catch (e: UiObjectNotFoundException) {
            e.printStackTrace()
            TestLog.e(TAG, String.format("findTextContentDescOnClick %s click exception: %s", text, e.toString()))
        }
        TestLog.e(TAG, String.format("findTextContentDescOnClick %s click is %s", text, click))
        return click
    }

    fun findEditorResIdSetText(resId: String?, text: String?): Boolean {
        var isSetTest = false
        try {
            Thread.sleep(TIME_WAIT_ONE_SEC)
            val uiObject: UiObject = uiDevice.findObject(UiSelector().resourceId(resId))
            if (uiObject != null) {
                isSetTest = uiObject.setText(text)
            }
        } catch (e: InterruptedException) {
            e.printStackTrace()
            TestLog.e(TAG, String.format("findEditorResIdSetText %s set text exception: %s", resId, e.toString()))
        } catch (e: UiObjectNotFoundException) {
            e.printStackTrace()
            TestLog.e(TAG, String.format("findEditorResIdSetText %s set text exception: %s", resId, e.toString()))
        }
        TestLog.e(TAG, String.format("findEditorResIdSetText %s set text is %s", resId, isSetTest))
        return isSetTest
    }

    fun findResourceIdOnClick(resId: String?): Boolean {
        var click = false
        try {
            Thread.sleep(TIME_WAIT_ONE_SEC)
            val uiObject: UiObject = uiDevice.findObject(UiSelector().resourceId(resId))
            if (uiObject != null) {
                click = uiObject.click()
            }
        } catch (e: InterruptedException) {
            e.printStackTrace()
            TestLog.e(TAG, String.format("findResourceIdOnClick %s click exception: %s", resId, e.toString()))
        } catch (e: UiObjectNotFoundException) {
            e.printStackTrace()
            TestLog.e(TAG, String.format("findResourceIdOnClick %s click exception: %s", resId, e.toString()))
        }
        TestLog.e(TAG, String.format("findResourceIdOnClick %s click is %s", resId, click))
        return click
    }

    /**
     * 拉起otest应用
     * 注意：鉴于OTest应用启动后还会出现crash的风险，故每个类在 setUp 中都必须要调用一次
     *
     * @param typeMsg     The type msg to log
     * @param index       The view index
     * @param text        The view content text
     * @param resourceId  The view resource id
     * @param className   The view class
     * @param pkgName     The permission package name
     * @param contentDesc The view content desc
     * @throws Exception
     */
    @Throws(Exception::class)
    fun clickView(
        typeMsg: String?, index: Int, text: String?, resourceId: String?,
        className: String?, pkgName: String?, contentDesc: String?
    ) {
        Thread.sleep(TIME_WAIT_ONE_SEC)
        val uiObject: UiObject = getUiObject(typeMsg, index, text, resourceId, className, pkgName, contentDesc)
        TestLog.d(TAG, "clickView: " + uiObject.getText())
        if (uiObject.exists()) {
            val click: Boolean = uiObject.click()
            Assert.assertTrue(
                String.format(
                    "The viewId is exists, but click failure. And pkgName:%s,"
                            + " viewClassName:%s, resourceId:%s, textContains:%s, contentDesc:%s",
                    typeMsg, index, text, pkgName, className, resourceId, contentDesc
                ), click
            )
        }
    }

    /**
     * @param typeMsg     The type msg to log
     * @param text        The view content text
     * @param pkgName     The permission package name
     * @param className   The view class
     * @param resourceId  The view resource id
     * @param contentDesc The view content desc
     * @throws Exception UiObjectNotFoundException
     */
    @Throws(Exception::class)
    fun clickPermissionDialog(
        typeMsg: String?, text: String?, pkgName: String?,
        className: String?, resourceId: String?, contentDesc: String?
    ) {
        Thread.sleep(TIME_WAIT_ONE_SEC)
        val uiSelector = UiSelector()
        uiSelector.textContains(text).packageName(pkgName).className(className).resourceId(resourceId)
        if (!TextUtils.isEmpty(contentDesc)) {
            uiSelector.description(contentDesc)
        }
        val uiObject: UiObject = uiDevice.findObject(uiSelector)
        if (uiObject.exists()) {
            val click: Boolean = uiObject.click()
            if (click) {
                TestLog.e(
                    TAG, String.format(
                        "The clickPermissionDialog %s permission is authorized success, "
                                + "pakName:%s, text:%s, resourceId:%s, contentDesc:%s",
                        typeMsg, pkgName, text, resourceId, contentDesc
                    )
                )
            } else {
                Assert.fail(
                    String.format(
                        "The clickPermissionDialog %s permission is authorized failure, "
                                + "pakName:%s, text:%s, resourceId:%s, contentDesc：%s",
                        typeMsg, pkgName, text, resourceId, contentDesc
                    )
                )
            }
        }
    }
}