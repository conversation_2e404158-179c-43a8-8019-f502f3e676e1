/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : CategoryAppHelper.kt
 * * Description : CategoryAppHelper cache
 * * Version     : 1.0
 * * Date        : 2020/9/7
 * * Author      : W9060445
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.cloudconfig

import com.filemanager.common.utils.Log

object CategoryAppHelper {
    private const val TAG = "CategoryAppHelper"

    var mCategoryListStr: String? = null

    fun addCategories(categoryListStr: String?) {
        mCategoryListStr = categoryListStr
        Log.d(TAG, "add categories is null :${categoryListStr == null}")
    }
}