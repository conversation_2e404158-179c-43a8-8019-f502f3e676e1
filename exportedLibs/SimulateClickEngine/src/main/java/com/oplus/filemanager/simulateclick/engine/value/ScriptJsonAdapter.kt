/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScriptJsonAdapter.kt
 * Description:
 *     The base adapter for convert some value type in Script.
 *
 * Version: 1.0
 * Date: 2024-06-04
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-06-04   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.value

import com.google.gson.TypeAdapter
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonToken
import com.google.gson.stream.JsonWriter

/**
 * The common implementation of gson TypeAdapter.
 * Implement to convert between custom value and json.
 */
internal abstract class ScriptJsonAdapter<T> : TypeAdapter<T>() {

    final override fun write(writer: JsonWriter, value: T?) {
        if (value == null) {
            writer.nullValue()
            return
        }
        writer.value(onWrite(value))
    }

    /**
     * Handle convert custom value to json string.
     */
    abstract fun onWrite(value: T): String

    final override fun read(reader: JsonReader): T? {
        if (reader.peek() == JsonToken.NULL) {
            reader.nextNull()
            return null
        }
        return onRead(reader.nextString())
    }

    /**
     * Handle convert jsonStr to custom value.
     */
    abstract fun onRead(jsonStr: String): T?
}