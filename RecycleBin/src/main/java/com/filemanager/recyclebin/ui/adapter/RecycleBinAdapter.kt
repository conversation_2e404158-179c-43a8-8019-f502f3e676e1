/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:/RecycleBinAdapter.kt
 * * Description:
 * * Version:1.0
 * * Date :2020/6/16
 * * Author:********
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/6/16,        v1.0,           Create
 ****************************************************************/
package com.filemanager.recyclebin.ui.adapter

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.isVisible
import androidx.core.view.setPadding
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.coroutineScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.base.BaseSelectionViewHolder
import com.filemanager.common.base.CheckBoxAnimateInput
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.GetMediaDurationUtil
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.LabelVHUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.ViewUtils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.isActivityAndInvalid
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.TextViewSnippet
import com.filemanager.common.wrapper.RecycleFileWrapper
import com.filemanager.recyclebin.utils.RecycleBinUtils
import com.oplus.recyclebin.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

class RecycleBinAdapter(content: Context, val lifecycle: Lifecycle) :
    BaseSelectionRecycleAdapter<BaseSelectionViewHolder, RecycleFileWrapper>(content),
    LifecycleObserver {
    companion object {
        private const val TAG = "RecycleBinAdapter"
        const val VIEW_TYPE_FILE = 0
        const val VIEW_TYPE_LABEL = 1
        const val VIEW_TYPE_HEADER_LABEL = 2
        const val VIEW_TYPE_FOOTER_TIPS = 3
        private const val SECOND = 1000L
    }

    private var mKeyWord: String? = null
    private var mIsRtl = Utils.isRtl()
    private val mDetailCache = HashMap<String, String>()
    private var mUiHandler: Handler = Handler(Looper.getMainLooper())
    private var mOnRecyclerItemClickListener: OnRecyclerItemClickListener? = null
    private var mLayoutManager: LinearLayoutManager? = null
    private var mThreadManager: ThreadManager = ThreadManager(lifecycle)
    private val mImgRadius = MyApplication.appContext.resources
        .getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_2dp)
    private var mRealFileSize = 0

    init {
        lifecycle.addObserver(this)
    }

    override fun getItemCount(): Int {
        return mFiles.size
    }

    override fun getItemViewType(position: Int): Int {
        val fileWrapper = getItem(position)
        return when (fileWrapper?.mFileWrapperViewType) {
            BaseFileBean.TYPE_LABEL_FILE -> {
                VIEW_TYPE_LABEL
            }
            BaseFileBean.TYPE_FILE_LIST_HEADER -> {
                VIEW_TYPE_HEADER_LABEL
            }
            VIEW_TYPE_FOOTER_TIPS -> VIEW_TYPE_FOOTER_TIPS
            else -> {
                if (WindowUtils.supportLargeScreenLayout(mContext)) {
                    KtConstants.SCAN_MODE_LIST_LARGE
                } else {
                    VIEW_TYPE_FILE
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseSelectionViewHolder {
        return if ((VIEW_TYPE_HEADER_LABEL == viewType) || (VIEW_TYPE_LABEL == viewType)) {
            val v = LayoutInflater.from(parent.context).inflate(R.layout.recycle_bin_list_label_item, parent, false)
            LabelItemViewHolder(v)
        } else if (VIEW_TYPE_FOOTER_TIPS == viewType) {
            val v = LayoutInflater.from(parent.context).inflate(R.layout.recycle_file_list_footer_tips_item, parent, false)
            HeaderViewHolder(v)
        } else if (KtConstants.SCAN_MODE_LIST_LARGE == viewType) {
            RecycleBinLargeListVH.create(parent)
        } else {
            val v = LayoutInflater.from(parent.context).inflate(R.layout.recycle_bin_file_list_item, parent, false)
            FileItemViewHolder(v)
        }
    }

    override fun onBindViewHolder(holder: BaseSelectionViewHolder, position: Int) {
        if (mContext.isActivityAndInvalid()) {
            Log.w(TAG, "onBindViewHolder: Activity is Destroyed!")
            return
        }
        if ((position < 0) || (position >= mFiles.size)) {
            Log.w(TAG, "onBindViewHolder: position:$position out range")
            return
        }
        val file =  mFiles[position]
        when (holder) {
            is FileItemViewHolder -> {
                holder.itemCount = mRealFileSize
                holder.updateKey(getItemKeyByPosition(position))
                onBindFileHolder(holder, position)
            }
            is RecycleBinLargeListVH -> {
                holder.itemCount = mRealFileSize
                holder.loadData(
                    mContext, getItemKey(file, position),
                    file, mChoiceMode, mSelectionArray, mDetailCache, mThreadManager, this)
            }
            is LabelItemViewHolder -> {
                holder.updateKey(com.oplus.dropdrag.SelectionTracker.SKIP_INT_ITEM_ID)
                onBindLabelHolder(holder, position)
            }
            is HeaderViewHolder -> {
                holder.updateKey(com.oplus.dropdrag.SelectionTracker.NO_INT_ITEM_ID)
                onBindHeaderViewHolder(holder)
            }
        }
    }

    private fun onBindHeaderViewHolder(holder: HeaderViewHolder) {
        if (!mKeyWord.isNullOrEmpty()) {
            holder.itemView.visibility = View.GONE
            return
        }
    }

    private fun onBindLabelHolder(holder: LabelItemViewHolder, position: Int) {
        val recWrapper = mFiles[position]
        if (recWrapper.mFileWrapperViewType == BaseFileBean.TYPE_LABEL_FILE) {
            holder.mHeaderLayout.visibility = View.VISIBLE
            holder.mHeaderLayout.setOnClickListener(null)
            holder.mHeaderText.text =
                recWrapper.mFileWrapperLabel?.let { mContext.getString(it) } + " " + recWrapper.mFileWrapperTypeNum
        } else if (recWrapper.mFileWrapperViewType == BaseFileBean.TYPE_FILE_LIST_HEADER) {
            holder.mHeaderLayout.visibility = View.VISIBLE
            holder.mHeaderLayout.setOnClickListener(null)
            if (position == 0) {
                holder.mHeaderText.text = mContext.getString(
                    com.filemanager.common.R.string.string_search_result_files,
                    recWrapper.mFileWrapperTypeNum
                )
            } else {
                holder.mHeaderText.text = mContext.getString(
                    com.filemanager.common.R.string.string_related_files,
                    recWrapper.mFileWrapperTypeNum
                )
            }
        } else {
            holder.mHeaderLayout.visibility = View.GONE
        }
    }

    private fun onBindFileHolder(holder: FileItemViewHolder, position: Int) {
        if (mContext.isActivityAndInvalid()) {
            Log.d(TAG, "onBindViewHolder: Activity is Destroyed!")
            return
        }
        holder.updateLeftRightMargin()
        val file = mFiles[position]
        if (mIsRtl) {
            if (!mChoiceMode) {
                holder.mTitle.setPadding(mContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_24dp),
                        holder.mTitle.paddingTop, holder.mTitle.paddingRight, holder.mTitle.paddingBottom)
                holder.mDetail.setPadding(mContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_24dp),
                        holder.mDetail.paddingTop, holder.mTitle.paddingRight, holder.mDetail.paddingBottom)
            } else {
                holder.mTitle.setPadding(mContext.resources.getDimensionPixelSize(R.dimen.category_edit_mode_padding_end),
                        holder.mTitle.paddingTop, holder.mDetail.paddingRight, holder.mTitle.paddingBottom)
                holder.mDetail.setPadding(mContext.resources.getDimensionPixelSize(R.dimen.category_edit_mode_padding_end),
                        holder.mDetail.paddingTop, holder.mDetail.paddingRight, holder.mDetail.paddingBottom)
            }
        } else {
            if (!mChoiceMode) {
                holder.mTitle.setPadding(holder.mTitle.paddingLeft, holder.mTitle.paddingTop,
                        mContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_24dp), holder.mTitle.paddingBottom)
                holder.mDetail.setPadding(holder.mDetail.paddingLeft, holder.mDetail.paddingTop,
                        mContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_24dp), holder.mDetail.paddingBottom)
            } else {
                holder.mTitle.setPadding(holder.mTitle.paddingLeft, holder.mTitle.paddingTop,
                        mContext.resources.getDimensionPixelSize(R.dimen.category_edit_mode_padding_end), holder.mTitle.paddingBottom)
                holder.mDetail.setPadding(holder.mDetail.paddingLeft, holder.mDetail.paddingTop,
                        mContext.resources.getDimensionPixelSize(R.dimen.category_edit_mode_padding_end), holder.mDetail.paddingBottom)
            }
        }
        holder.itemView.setOnClickListener {
            mOnRecyclerItemClickListener?.onItemClick(holder.itemView, holder.layoutPosition)
        }

        holder.itemView.setOnLongClickListener {
            mOnRecyclerItemClickListener?.onItemLongClick(holder.itemView, holder.layoutPosition)
            true
        }
        holder.mCheckBox?.let { setCheckBoxAnim(CheckBoxAnimateInput(false, mChoiceMode, null, it, position)) }
        doData(file, holder)
    }

    private fun doData(file: RecycleFileWrapper?, holder: FileItemViewHolder) {
        if (file == null) {
            return
        }
        var isMoreOneLine = false
        holder.mTitle.visibility = View.VISIBLE
        val path = file.mRecyclePath
        val type = file.mLocalType
        if (path == null) {
            return
        }
        showFileIcon(holder, file, path, type)
        showFileTitle(holder, file, path)
        showDurationIfNeed(holder, file)
        holder.mTitle.post {
            val title = file.mDisplayName ?: return@post
            if (isMoreOneLine) return@post
            isMoreOneLine = holder.mTitle.isMoreThanOneLine(title)
            setIconConstraintSet(holder.rootView, isMoreOneLine)
        }
        showDetail(holder, file, path) {
            holder.mDetail.post {
                if (isMoreOneLine) return@post
                isMoreOneLine = ViewUtils.isMoreOneLine(holder.mDetail, it)
                setIconConstraintSet(holder.rootView, isMoreOneLine)
            }
        }
    }

    private fun showFileTitle(holder: FileItemViewHolder, file: RecycleFileWrapper, path: String) {
        holder.mTitle.tag = path
        LabelVHUtils.displayLabelFlag(file, holder.mTitle.context, holder.mTitle)
        if (TextUtils.isEmpty(mKeyWord)) {
            holder.mTitle.text = file.mDisplayName
        } else {
            if (Utils.isNeededTargetLanguage(Constants.SNIPPET_LANGUAGE)) {
                holder.mTitle.text = file.mDisplayName
            } else {
                holder.mTitle.setTextWithPost(file.mDisplayName, mKeyWord)
            }
        }
        holder.mTitle.setTextViewStyleForFixedWidth()
    }

    private fun setIconConstraintSet(rootView: ConstraintLayout, isMoreThanOneLine: Boolean) {
        val constraintSet = ConstraintSet()
        constraintSet.apply {
            clone(rootView)
            clear(R.id.file_list_item_icon, ConstraintSet.TOP)
            clear(R.id.file_list_item_icon, ConstraintSet.BOTTOM)
            if (isMoreThanOneLine) {
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.TOP,
                    R.id.file_list_item_title,
                    ConstraintSet.TOP
                )
            } else {
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )
            }
            applyTo(rootView)
        }
    }

    private fun showFileIcon(holder: FileItemViewHolder, file: RecycleFileWrapper, path: String, type: Int) {
        showDrmIcon(type, holder)
        holder.mImg.apply {
            FileImageVHUtils.updateFileListImgSize(mContext, this, file)
            if (file.mLocalType == MimeTypeHelper.IMAGE_TYPE || file.mLocalType == MimeTypeHelper.VIDEO_TYPE) {
                // 特殊：这里需要用STROKE_2DP，否则图片圆角与描边不吻合
                setStrokeStyle(FileThumbView.STROKE_2DP)
            } else {
                setStrokeStyle(FileThumbView.STROKE_NONE)
            }
            setTag(com.filemanager.common.R.id.glide_tag_id, null)
            val padding = when (type) {
                MimeTypeHelper.VIDEO_TYPE, MimeTypeHelper.AUDIO_TYPE -> {
                    MyApplication.sAppContext.resources.getDimension(com.filemanager.common.R.dimen.file_list_image_padding).toInt()
                }
                else -> 0
            }
            setPadding(padding)
            FileImageLoader.sInstance.clear(mContext, this)
            FileImageLoader.sInstance.displayDefault(file, this, 0, mImgRadius)
        }
    }

    private fun showDrmIcon(type: Int, holder: FileItemViewHolder) {
        holder.mImg.setDrmState(type == MimeTypeHelper.DRM_TYPE)
    }

    private fun showDetail(holder: FileItemViewHolder, file: RecycleFileWrapper, path: String, block: ((String) -> Unit)) {
        holder.mDetail.tag = path
        val recycleDate = file.mRecycelDate
        val sizeStr = mDetailCache[path + recycleDate]
        val dateAndTime = Utils.getDateFormat(mContext, recycleDate)
        val timeLeft = RecycleBinUtils.getRecycleTimeLeftString(System.currentTimeMillis(), recycleDate)
        if ((sizeStr != null) && sizeStr.isNotEmpty()) {
            val content = RecycleBinUtils.formatRecycleDetail(mContext, sizeStr, dateAndTime, timeLeft)
            holder.mDetail.text = content
            holder.mDetail.visibility = View.VISIBLE
            block.invoke(content.toString())
        } else {
            holder.mDetail.visibility = View.INVISIBLE
            //for bugId:496967 ,Set the default information before setting the real information to ensure that the detail row height will not change
            holder.mDetail.text = RecycleBinUtils.formatRecycleDetail(mContext, mContext.resources.getQuantityString(
                com.filemanager.common.R.plurals.text_x_items, 0), dateAndTime, timeLeft)
            setDetail(holder.mDetail, path, file) {
                block.invoke(it)
            }
        }
    }

    private fun showDurationIfNeed(holder: FileItemViewHolder, file: BaseFileBean) {
        lifecycle.coroutineScope.launch(Dispatchers.IO) {
            val duration = if (MimeTypeHelper.VIDEO_TYPE == file.mLocalType && file.mMediaDuration == 0L) {
                file.mMediaDuration = GetMediaDurationUtil.getDuration(file)
                KtUtils.formatVideoTime(file.mMediaDuration / SECOND)
            } else {
                null
            }
            withContext(Dispatchers.Main) {
                holder.duration.isVisible = duration?.isNotEmpty() ?: false
                holder.duration.text = duration
            }
        }
    }

    class RecycleBinRunnable : FileRunnable {
        constructor(
            weakContext: WeakReference<Context?>,
            weakTextView: WeakReference<TextView?>,
            mDetailCache: HashMap<String, String>,
            file: RecycleFileWrapper,
            uiHandler: Handler?,
            path: String,
            block: ((String) -> Unit)
        ) : super(Runnable {
            val recycleDate = file.mRecycelDate
            val timeLeft = RecycleBinUtils.getRecycleTimeLeftString(recycleDate, System.currentTimeMillis())
            val formatFileDetail = if (file.mIsDirectory) {
                val fileCount = JavaFileHelper.listFilesCount(file, false)
                MyApplication.sAppContext.resources.getQuantityString(com.filemanager.common.R.plurals.text_x_items, fileCount, fileCount)
            } else {
                RecycleBinUtils.formatRecycleSize(file)
            }
            uiHandler?.post {
                val textView = weakTextView.get()
                val context = weakContext.get()
                if (textView != null && context != null) {
                    val dateAndTime = Utils.getDateFormat(textView.context, recycleDate)
                    val currentPath = textView.tag as? String
                    if (path == currentPath) {
                        mDetailCache[path + recycleDate] = formatFileDetail
                        val content = RecycleBinUtils.formatRecycleDetail(context, formatFileDetail, dateAndTime, timeLeft)
                        textView.text = content
                        textView.visibility = View.VISIBLE
                        block.invoke(content.toString())
                    }
                }
            }
        }, TAG)
    }

    private fun setDetail(detailView: TextView?, path: String, file: RecycleFileWrapper, block: ((String) -> Unit)) {
        mThreadManager.execute(RecycleBinRunnable(WeakReference(mContext), WeakReference(detailView), mDetailCache, file, mUiHandler, path) {
            block.invoke(it)
        })
    }


    fun setOnRecyclerItemClickListener(listener: OnRecyclerItemClickListener) {
        mOnRecyclerItemClickListener = listener
    }

    fun setData(fileList: ArrayList<RecycleFileWrapper>, selectedList: ArrayList<Int>, realFileSize: Int) {
        mFiles = fileList
        mSelectionArray = selectedList
        mRealFileSize = realFileSize
        checkComputingAndExecute {
            notifyDataSetChanged()
        }
        mIsRtl = Utils.isRtl()
    }

    fun setKeyWord(keyWord: String) {
        mKeyWord = keyWord
    }

    fun setRecycleViewLayoutManager(layoutManager: LinearLayoutManager?) {
        mLayoutManager = layoutManager
    }

    override fun getHeadViewCount(): Int {
        var count = 0
        //only search result is not empty will show header view
        if (mKeyWord.isNullOrEmpty()) {
            return count
        }
        for (file in mFiles) {
            if (file.mFileWrapperViewType == BaseFileBean.TYPE_FILE_LIST_HEADER) {
                count++
            }
        }
        return count
    }

    override fun getLabelViewCount(): Int {
        var count = 0
        val sort = SortModeUtils.getSharedSortMode(mContext, SortRecordModeFactory.getRecycleBinKey())
        if (sort == SortHelper.FILE_TYPE_ORDER) {
            for (file in mFiles) {
                if (file.mFileWrapperViewType == BaseFileBean.TYPE_LABEL_FILE) {
                    count++
                }
            }
        }
        return count
    }

    override fun getFooterViewCount(): Int {
        //in search mode, will not show footer tip
        if (!mKeyWord.isNullOrEmpty()) {
            return 0
        }
        return 1
    }

    class HeaderViewHolder(var view: View) : BaseSelectionViewHolder(view, false) {
        var mHeaderTipText: TextView? = view.findViewById(R.id.header_tips_count_view)
    }

    internal class LabelItemViewHolder(var view: View) : BaseSelectionViewHolder(view, false) {
        var mHeaderLayout: RelativeLayout = view.findViewById(R.id.header_layout)
        var mHeaderText: TextView = view.findViewById(R.id.header_view)
    }

    internal class FileItemViewHolder(var view: View) : BaseSelectionViewHolder(view) {
        var mImg: FileThumbView = view.findViewById(R.id.file_list_item_icon)
        var mTitle: TextViewSnippet = view.findViewById(R.id.file_list_item_title)
        var mDetail: TextView = view.findViewById(R.id.file_list_item_detail)
        var rootView: ConstraintLayout = view.findViewById(R.id.recyle_bin_list_item_root)
        var duration: TextView = view.findViewById(R.id.file_duration_tv)
        var iconContainer: View = view.findViewById(R.id.file_list_item_icon_container)

        init {
            mCheckBox = view.findViewById(R.id.listview_scrollchoice_checkbox)
        }

        override fun drawDivider(): Boolean {
            return itemCount - 1 != bindingAdapterPosition
        }

        override fun getDividerStartAlignView(): View {
            return mTitle
        }

        override fun getDividerEndInset(): Int {
            return FileImageVHUtils.getListRightMargin()
        }

        fun updateLeftRightMargin() {
            iconContainer.updateLayoutParams<ConstraintLayout.LayoutParams> {
                this.marginStart = FileImageVHUtils.getListLeftMargin()
            }
            mTitle.updateLayoutParams<ConstraintLayout.LayoutParams> {
                this.marginEnd = FileImageVHUtils.getListRightMargin()
            }
        }
    }

    //getItemKey must return different code,position hashcode may return same as path hashcode
    override fun getItemKey(item: RecycleFileWrapper, position: Int): Int? {
        return if (item.mId == null) {
            position.hashCode()
        } else {
            item.mId
        }
    }

    override fun getItemId(position: Int): Long {
        return getItemKeyByPosition(position)?.toLong() ?: com.oplus.dropdrag.SelectionTracker.NO_LONG_ITEM_ID
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        mDetailCache.clear()
        mUiHandler.removeCallbacksAndMessages(null)
    }

    fun onConfigurationChanged() {
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        setChoiceModeAnimFlag(flag)
    }
}
