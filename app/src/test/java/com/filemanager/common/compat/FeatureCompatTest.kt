/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        :
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/7
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.compat

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.ModelUtils
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class FeatureCompatTest {

    @MockK
    lateinit var context: Context

    @Before
    fun setUp() {
        context = mockk(relaxed = true) {
            every { contentResolver }.returns(mockk(relaxed = true))
            every { applicationContext }.returns(this)
        }
        mockkStatic(MyApplication::class).apply {
            every { MyApplication.sAppContext } returns context
        }
    }

    @Test
    fun should_isSmallScreenPhoneNew_return_false_when_table() {
        mockkStatic(ModelUtils::class).apply {
            every { ModelUtils.isTablet() } returns true
        }
        val smallScreenPhoneNew = FeatureCompat.isSmallScreenPhoneNew()
        Assert.assertFalse(smallScreenPhoneNew)
    }

    @Test
    fun should_isSmallScreenPhoneNew_return_false_when_fold_open() {
        mockkStatic(ModelUtils::class).apply {
            every { ModelUtils.isTablet() } returns false
        }
        val monitor = mockk<UIConfigMonitor>().apply {
            every { getFoldState(any()) } returns 1
        }
        mockkObject(UIConfigMonitor.Companion).apply {
            every { UIConfigMonitor.instance } returns monitor
            every { UIConfigMonitor.isCurrentSmallScreen() } returns false
        }

        val smallScreenPhoneNew = FeatureCompat.isSmallScreenPhoneNew()
        Assert.assertFalse(smallScreenPhoneNew)
    }

    @Test
    fun should_isSmallScreenPhoneNew_return_false_when_fold_close() {
        mockkStatic(ModelUtils::class).apply {
            every { ModelUtils.isTablet() } returns false
        }
        val monitor = mockk<UIConfigMonitor>().apply {
            every { getFoldState(any()) } returns 0
        }
        mockkObject(UIConfigMonitor.Companion).apply {
            every { UIConfigMonitor.instance } returns monitor
            every { UIConfigMonitor.isCurrentSmallScreen() } returns false
        }

        val smallScreenPhoneNew = FeatureCompat.isSmallScreenPhoneNew()
        Assert.assertFalse(smallScreenPhoneNew)
    }

    @Test
    fun should_isSmallScreenPhoneNew_return_true_when_fold_open_small() {
        mockkStatic(ModelUtils::class).apply {
            every { ModelUtils.isTablet() } returns false
        }
        val monitor = mockk<UIConfigMonitor>().apply {
            every { getFoldState(any()) } returns 1
        }
        mockkObject(UIConfigMonitor.Companion).apply {
            every { UIConfigMonitor.instance } returns monitor
            every { UIConfigMonitor.isCurrentSmallScreen() } returns true
        }

        val smallScreenPhoneNew = FeatureCompat.isSmallScreenPhoneNew()
        Assert.assertTrue(smallScreenPhoneNew)
    }

    @Test
    fun should_isSmallScreenPhoneNew_return_true_when_fold_close_small() {
        mockkStatic(ModelUtils::class).apply {
            every { ModelUtils.isTablet() } returns true
        }
        val monitor = mockk<UIConfigMonitor>().apply {
            every { getFoldState(any()) } returns 0
        }
        mockkObject(UIConfigMonitor.Companion).apply {
            every { UIConfigMonitor.instance } returns monitor
            every { UIConfigMonitor.isCurrentSmallScreen() } returns true
        }

        val smallScreenPhoneNew = FeatureCompat.isSmallScreenPhoneNew()
        Assert.assertFalse(smallScreenPhoneNew)
    }

    @Test
    fun should_isSmallScreenPhoneNew_return_true_when_unfold() {
        mockkStatic(ModelUtils::class).apply {
            every { ModelUtils.isTablet() } returns false
        }
        val monitor = mockk<UIConfigMonitor>().apply {
            every { getFoldState(any()) } returns -1
        }
        mockkObject(UIConfigMonitor.Companion).apply {
            every { UIConfigMonitor.instance } returns monitor
            every { UIConfigMonitor.isCurrentSmallScreen() } returns true
        }

        val smallScreenPhoneNew = FeatureCompat.isSmallScreenPhoneNew()
        Assert.assertTrue(smallScreenPhoneNew)
    }
}