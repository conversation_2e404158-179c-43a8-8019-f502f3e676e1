plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/sdkCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")

android {
    namespace "com.filemanager.thumbnail.doc.wps"

    defaultConfig {
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            //config enable proGuard
            minifyEnabled true
            //proGuard rules files
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation libs.androidx.core.ktx

    //used for Extracting thumbnail of doc, supported by wps
    implementation libs.wps.sdk.snapshot

    implementation project(':exportedLibs:Thumbnail')
}

OBuildConfig {
    standAloneSdk = true
    groupId = prop_archivesGroupName
    sdkArtifactId = prop_artifactId_thumbnailWpsCompat
    sdkExecuteTask = "publishAllPublicationsToReleaseRepository"
    moduleDescription = "The function module for thumbnail aar."
}

apply from: rootProject.file("exportedLibs/scripts/aarCompile.gradle")
apply from: rootProject.file("exportedLibs/scripts/mappingCollect.gradle")

mappingCollect {
    publishTaskName = "publishAllPublicationsToReleaseRepository"
    buildType = "release"
    currentPrebuilt libs.oplus.filemanager.thumbnailWpsCompat
}
