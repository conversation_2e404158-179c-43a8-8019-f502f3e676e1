<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/category_content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical">

    <ViewStub
        android:id="@+id/view_stub_cdp_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/layout_cdp_view_container" />

    <include
        layout="@layout/main_storage_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <RelativeLayout
        android:id="@+id/main_category_recycler_view_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.recyclerview.widget.COUIRecyclerView
            android:id="@+id/main_category_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:focusableInTouchMode="false"
            android:nestedScrollingEnabled="false" />
    </RelativeLayout>

    <ViewStub
        android:id="@+id/main_ad_vsub"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/item_main_ad" />

</LinearLayout>