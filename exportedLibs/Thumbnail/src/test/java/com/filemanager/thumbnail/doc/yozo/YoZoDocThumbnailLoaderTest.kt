/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - YoZoDocThumbnailLoaderTest.kt
 * Description:
 * The test cases for YoZoDocThumbnailLoader
 *
 * Version: 1.0
 * Date: 2024-08-06
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-08-06   1.0    Create this module
 */
package com.filemanager.thumbnail.doc.yozo

import android.net.Uri
import com.filemanager.thumbnail.ThumbnailConstant
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.doc.DocThumbnail
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class YoZoDocThumbnailLoaderTest : Assert() {

    private lateinit var thumbnailLoader: YoZoDocThumbnailLoader

    @Before
    fun setUp() {
        mockkObject(
            YoZoDocThumbService.SERVICE_4,
            YoZoDocThumbService.SERVICE_5,
            YoZoDocThumbService.SERVICE_6
        )
        justRun { YoZoDocThumbService.SERVICE_4.loadThumb(any(), any(), any(), any()) }
        justRun { YoZoDocThumbService.SERVICE_5.loadThumb(any(), any(), any(), any()) }
        justRun { YoZoDocThumbService.SERVICE_6.loadThumb(any(), any(), any(), any()) }
        every { YoZoDocThumbService.SERVICE_4.cancel(any()) } returns false
        every { YoZoDocThumbService.SERVICE_5.cancel(any()) } returns false
        every { YoZoDocThumbService.SERVICE_6.cancel(any()) } returns false
        thumbnailLoader = spyk(
            YoZoDocThumbnailLoader(
                mockk(),
                minIdleServicesNum = 1,
                occupyThumbServices = setOf(
                    ThumbnailConstant.YO_ZO_THUMB_SERVICE_4,
                    ThumbnailConstant.YO_ZO_THUMB_SERVICE_5,
                    ThumbnailConstant.YO_ZO_THUMB_SERVICE_6
                )
            )
        ) {
            justRun { executeWithService(any(), any()) }
        }
    }

    @After
    fun tearDown() {
        unmockkObject(
            YoZoDocThumbService.SERVICE_4,
            YoZoDocThumbService.SERVICE_5,
            YoZoDocThumbService.SERVICE_6
        )
    }

    @Test
    fun `should execute request when onPollAndExecuteRequest if has enough service`() {
        // Given
        val mockDocUri = mockk<Uri>()
        val mockDoc = DocThumbnail(
            uri = mockDocUri,
            lastModified = 123L,
            size = 1024L,
            docFilePath = "testFilePath"
        )
        val request = YoZoDocThumbnailLoader.LoadRequest(mockDoc, 100, 100, mockk())
        request.retryCount = ThumbnailManager.YoZoDocConfigs.maxRetryCount - 1
        thumbnailLoader.offerRequest(request)

        // When
        thumbnailLoader.onPollAndExecuteRequest()

        // Then
        assertTrue(thumbnailLoader.loadRequestQueue.isEmpty())
        assertEquals(ThumbnailManager.YoZoDocConfigs.maxRetryCount, request.retryCount)
        verify {
            thumbnailLoader.executeWithService(request, YoZoDocThumbService.SERVICE_4)
        }
    }

    @Test
    fun `should not execute request when onPollAndExecuteRequest if no enough service`() {
        // Given
        val mockDocUri = mockk<Uri>()
        val mockDoc = DocThumbnail(
            uri = mockDocUri,
            lastModified = 123L,
            size = 1024L,
            docFilePath = "testFilePath"
        )
        val request = YoZoDocThumbnailLoader.LoadRequest(mockDoc, 100, 100, mockk())
        thumbnailLoader.offerRequest(request)
        thumbnailLoader.idleServiceQueue.clear()
        thumbnailLoader.idleServiceQueue.offer(YoZoDocThumbService.SERVICE_4)

        // When
        thumbnailLoader.onPollAndExecuteRequest()

        // Then
        assertTrue(thumbnailLoader.loadRequestQueue.isNotEmpty())
        verify(inverse = true) {
            thumbnailLoader.executeWithService(request, YoZoDocThumbService.SERVICE_4)
        }
    }

    @Test
    fun `should not execute request when onPollAndExecuteRequest if is cancelled`() {
        // Given
        val mockDocUri = mockk<Uri>()
        val mockDoc = DocThumbnail(
            uri = mockDocUri,
            lastModified = 123L,
            size = 1024L,
            docFilePath = "testFilePath"
        )
        val request = YoZoDocThumbnailLoader.LoadRequest(mockDoc, 100, 100, mockk())
        thumbnailLoader.offerRequest(request)
        thumbnailLoader.requestIndexMap[mockDoc]?.isCancelled = true

        // When
        thumbnailLoader.onPollAndExecuteRequest()

        // Then
        assertTrue(thumbnailLoader.loadRequestQueue.isEmpty())
        verify(inverse = true) {
            thumbnailLoader.executeWithService(request, YoZoDocThumbService.SERVICE_4)
            thumbnailLoader.executeWithService(request, YoZoDocThumbService.SERVICE_5)
            thumbnailLoader.executeWithService(request, YoZoDocThumbService.SERVICE_6)
        }
    }

    @Test
    fun `should not execute request when onPollAndExecuteRequest if is retryLimit`() {
        // Given
        val mockDocUri = mockk<Uri>()
        val mockDoc = DocThumbnail(
            uri = mockDocUri,
            lastModified = 123L,
            size = 1024L,
            docFilePath = "testFilePath"
        )
        val request = YoZoDocThumbnailLoader.LoadRequest(mockDoc, 100, 100, mockk())
        request.retryCount = ThumbnailManager.YoZoDocConfigs.maxRetryCount
        thumbnailLoader.offerRequest(request)
        thumbnailLoader.requestIndexMap[mockDoc]?.isCancelled = true

        // When
        thumbnailLoader.onPollAndExecuteRequest()

        // Then
        assertTrue(thumbnailLoader.loadRequestQueue.isEmpty())
        verify(inverse = true) {
            thumbnailLoader.executeWithService(request, YoZoDocThumbService.SERVICE_4)
            thumbnailLoader.executeWithService(request, YoZoDocThumbService.SERVICE_5)
            thumbnailLoader.executeWithService(request, YoZoDocThumbService.SERVICE_6)
        }
    }

    @Test
    fun `should back to queue when onReleaseThumbnailService`() {
        // Given
        justRun { thumbnailLoader.scheduleExecuteRequest() }
        thumbnailLoader.idleServiceQueue.clear()
        thumbnailLoader.idleServiceQueue.offer(YoZoDocThumbService.SERVICE_6)

        // When
        thumbnailLoader.onReleaseThumbnailService(YoZoDocThumbService.SERVICE_4)

        // Then
        assertEquals(
            setOf(YoZoDocThumbService.SERVICE_6, YoZoDocThumbService.SERVICE_4),
            thumbnailLoader.idleServiceQueue.toSet()
        )
    }

    @Test
    fun `should callbackCancelled when cancel if not executed`() {
        // Given
        val mockDocUri = mockk<Uri>()
        val mockDoc = DocThumbnail(
            uri = mockDocUri,
            lastModified = 123L,
            size = 1024L,
            docFilePath = "testFilePath"
        )
        val request = YoZoDocThumbnailLoader.LoadRequest(mockDoc, 100, 100, mockk())
        thumbnailLoader.run {
            offerRequest(request)
            justRun { request.callbackCancelled() }
        }


        // When
        thumbnailLoader.cancel(mockDoc)

        // Then
        assertEquals(true, thumbnailLoader.requestIndexMap[mockDoc]?.isCancelled)
        verify {
            thumbnailLoader.run { request.callbackCancelled() }
        }
    }

    @Test
    fun `should exec cancel when cancel if executed`() {
        // Given
        val mockDocUri = mockk<Uri>()
        val mockDoc = DocThumbnail(
            uri = mockDocUri,
            lastModified = 123L,
            size = 1024L,
            docFilePath = "testFilePath"
        )
        val request = YoZoDocThumbnailLoader.LoadRequest(mockDoc, 100, 100, mockk())
        thumbnailLoader.offerRequest(request)
        every { YoZoDocThumbService.SERVICE_4.cancel(mockDocUri) } returns true

        // When
        thumbnailLoader.cancel(mockDoc)

        // Then
        assertEquals(true, thumbnailLoader.requestIndexMap[mockDoc]?.isCancelled)
        verify {
            YoZoDocThumbService.SERVICE_4.cancel(mockDocUri)
        }
        verify(inverse = true) {
            thumbnailLoader.run { request.callbackCancelled() }
        }
    }
}