/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : AppMarketManagerApiTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/5/6 17:58
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2023/5/6       1.0      create
 **********************************************************************/
package com.oplus.filemanager.appmarketmanager

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.Utils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AppMarketManagerApiTest {

    @Before
    fun setUp() {
        val context = mockk<Context>(relaxed = true)
        mockkObject(MyApplication)
        every { MyApplication.sAppContext }.returns(context)
        mockkStatic(Utils::class)
        mockkStatic(AppUtils::class)
    }

    @After
    fun tearDown() {
        unmockkObject(MyApplication)
        unmockkStatic(Utils::class)
        unmockkStatic(AppUtils::class)
    }

    @Test
    fun testAppMarketManagerEnabled() {
        mockkStatic("com.oplus.filemanager.appmarketmanager.PlatformManagerKt")
        val mContext = mockk<Context>(relaxed = true)
        every { MyApplication.sAppContext }.returns(mContext)

        every { isHighEndMachine(mContext) }.returns(true)
        Assert.assertFalse(AppMarketManagerApi.isAppMarketManagerEnabled(mContext))

        every { isHighEndMachine(mContext) }.returns(false)

        every { AppUtils.getAppVersionCode(any()) }.returns(FAKE_VERSION_CODE_NOT_SUPPORT)
        Assert.assertFalse(AppMarketManagerApi.isAppMarketManagerEnabled(mContext))

        every { AppUtils.getAppVersionCode(any()) }.returns(FAKE_VERSION_CODE_SUPPORT)
        Assert.assertTrue(AppMarketManagerApi.isAppMarketManagerEnabled(mContext))

        every { Utils.checkAppStateEnable(mContext, any()) }.returns(false)
        Assert.assertFalse(AppMarketManagerApi.isAppMarketManagerEnabled(mContext))

        every { Utils.checkAppStateEnable(mContext, any()) }.returns(true)
        every { AppUtils.getAppVersionCode(any()) }.returns(FAKE_VERSION_CODE_SUPPORT)
        Assert.assertTrue(AppMarketManagerApi.isAppMarketManagerEnabled(mContext))
        unmockkStatic("com.oplus.filemanager.appmarketmanager.PlatformManagerKt")
    }

    companion object {
        private const val FAKE_VERSION_CODE_SUPPORT = 100700
        private const val FAKE_VERSION_CODE_NOT_SUPPORT = 90000
    }
}