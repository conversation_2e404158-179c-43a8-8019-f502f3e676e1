/*********************************************************************
 * * Copyright (C), 2010-2024 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AddLabelPanelActivity.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/03/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   keweiwei                 2024/03/04       1      create
 ***********************************************************************/
package com.oplus.labelmanager

import android.os.Bundle
import androidx.fragment.app.FragmentManager
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.PermissionUtils
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AddLabelPanelActivityTest {

    private lateinit var addLabelPanelActivity: AddLabelPanelActivity

    @Before
    fun setup() {
        addLabelPanelActivity = mockk()
        mockkObject(PrivacyPolicyController)
        mockkStatic(PermissionUtils::class)
        mockkStatic(IntentUtils::class)
        mockkStatic(CustomToast::class)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `should return false when call canShowAddLabelPanel if none hasAgreePrivacy`() {
        //given
        every { PrivacyPolicyController.hasAgreePrivacy() } returns false
        every { PermissionUtils.hasStoragePermission(addLabelPanelActivity) } returns true
        every { addLabelPanelActivity.canShowAddLabelPanel() } answers { callOriginal() }
        every { addLabelPanelActivity.hasShowPanel } answers { callOriginal() }
        every { addLabelPanelActivity.hasShowPanel } returns false
        //when
        val result = addLabelPanelActivity.canShowAddLabelPanel()
        //then
        Assert.assertEquals(result, false)
    }

    @Test
    fun `should return false when call canShowAddLabelPanel if none hasStoragePermission`() {
        //given
        every { PrivacyPolicyController.hasAgreePrivacy() } returns true
        every { PermissionUtils.hasStoragePermission(addLabelPanelActivity) } returns false
        every { addLabelPanelActivity.canShowAddLabelPanel() } answers { callOriginal() }
        every { addLabelPanelActivity.hasShowPanel } answers { callOriginal() }
        every { addLabelPanelActivity.hasShowPanel } returns false
        //when
        val result = addLabelPanelActivity.canShowAddLabelPanel()
        //then
        Assert.assertEquals(result, false)
    }

    @Test
    fun `should return false when call canShowAddLabelPanel if hasShowPanel is true`() {
        //given
        every { PrivacyPolicyController.hasAgreePrivacy() } returns true
        every { PermissionUtils.hasStoragePermission(addLabelPanelActivity) } returns true
        every { addLabelPanelActivity.canShowAddLabelPanel() } answers { callOriginal() }
        every { addLabelPanelActivity.hasShowPanel } answers { callOriginal() }
        every { addLabelPanelActivity.hasShowPanel } returns true
        //when
        val result = addLabelPanelActivity.canShowAddLabelPanel()
        //then
        Assert.assertEquals(result, false)
    }

    @Test
    fun `should return true when call canShowAddLabelPanel`() {
        //given
        every { PrivacyPolicyController.hasAgreePrivacy() } returns true
        every { PermissionUtils.hasStoragePermission(addLabelPanelActivity) } returns true
        every { addLabelPanelActivity.canShowAddLabelPanel() } answers { callOriginal() }
        every { addLabelPanelActivity.hasShowPanel } answers { callOriginal() }
        every { addLabelPanelActivity.hasShowPanel } returns false
        //when
        val result = addLabelPanelActivity.canShowAddLabelPanel()
        //then
        Assert.assertEquals(result, true)
    }

    @Test
    fun `should call Bundle getStringArrayList when call loadData if from save instance`() {
        //given
        val savedInstanceState = mockk<Bundle>()
        val list = mockk<ArrayList<String>>()
        every { savedInstanceState.getBoolean(any()) } returns true
        every { savedInstanceState.getStringArrayList(any()) } returns list
        every { addLabelPanelActivity.loadData(any()) } answers { callOriginal() }
        every { addLabelPanelActivity.fileUris } answers { callOriginal() }
        //when
        addLabelPanelActivity.loadData(savedInstanceState)
        //then
        verify { savedInstanceState.getStringArrayList(any()) }
        Assert.assertEquals(addLabelPanelActivity.fileUris, list)
    }

    @Test
    fun `should call IntentUtils getStringArrayList when call loadData if new create`() {
        //given
        val savedInstanceState = mockk<Bundle>()
        val list = mockk<ArrayList<String>>()
        every { savedInstanceState.getBoolean(any()) } returns false
        every { savedInstanceState.getStringArrayList(any()) } returns list
        every { addLabelPanelActivity.loadData(any()) } answers { callOriginal() }
        every { addLabelPanelActivity.fileUris } answers { callOriginal() }
        every { IntentUtils.getStringArrayList(any(), any()) } returns list
        every { addLabelPanelActivity.intent } returns mockk()
        //when
        addLabelPanelActivity.loadData(savedInstanceState)
        //then
        verify { IntentUtils.getStringArrayList(any(), any()) }
        Assert.assertEquals(addLabelPanelActivity.fileUris, list)
    }

    @Test
    fun `should call call IntentUtils getStringArrayList when bundle is null`() {
        //given
        val savedInstanceState = null
        val list = mockk<ArrayList<String>>()
        every { addLabelPanelActivity.loadData(any()) } answers { callOriginal() }
        every { addLabelPanelActivity.fileUris } answers { callOriginal() }
        every { IntentUtils.getStringArrayList(any(), any()) } returns list
        every { addLabelPanelActivity.intent } returns mockk()
        //when
        addLabelPanelActivity.loadData(savedInstanceState)
        //then
        verify { IntentUtils.getStringArrayList(any(), any()) }
        Assert.assertEquals(addLabelPanelActivity.fileUris, list)
    }

    @Test
    fun `should call finish when call onPermissionReject`() {
        //given
        every { addLabelPanelActivity.onPermissionReject(any()) } answers { callOriginal() }
        every { addLabelPanelActivity.finish() } just runs
        every { addLabelPanelActivity.hasPermissionReject } answers { callOriginal() }
        //when
        addLabelPanelActivity.onPermissionReject(false)
        //then
        verify { addLabelPanelActivity.finish() }
        Assert.assertEquals(addLabelPanelActivity.hasPermissionReject, true)
    }

    @Test
    fun `should call showSettingGuildDialog when call handleNoStoragePermission if hasPermissionReject is false`() {
        //given
        every { addLabelPanelActivity.handleNoStoragePermission() } answers { callOriginal() }
        every { addLabelPanelActivity.showSettingGuildDialog() } just runs
        every { addLabelPanelActivity.hasPermissionReject } answers { callOriginal() }
        every { addLabelPanelActivity.hasPermissionReject } returns false
        addLabelPanelActivity.handleNoStoragePermission()
        //then
        verify { addLabelPanelActivity.showSettingGuildDialog() }
    }

    @Test
    fun `should none call showSettingGuildDialog when call handleNoStoragePermission if hasPermissionReject is true`() {
        //given
        every { addLabelPanelActivity.handleNoStoragePermission() } answers { callOriginal() }
        every { addLabelPanelActivity.showSettingGuildDialog() } just runs
        every { addLabelPanelActivity.hasPermissionReject } answers { callOriginal() }
        every { addLabelPanelActivity.hasPermissionReject } returns true
        //when
        addLabelPanelActivity.handleNoStoragePermission()
        //then
        verify(inverse = true) { addLabelPanelActivity.showSettingGuildDialog() }
    }

    @Test
    fun `should call addFileLabelController setOnDismissListener when call showAddLabelPanel if fromSaveInstance is true`() {
        //given
        every { addLabelPanelActivity.fromSaveInstance } answers { callOriginal() }
        every { addLabelPanelActivity.fromSaveInstance } returns true

        val addFileLabelController: AddFileLabelController = mockk()
        every { addLabelPanelActivity.addFileLabelController } returns addFileLabelController
        every { addFileLabelController.setOnDismissListener(any(), any()) } just runs
        every { addFileLabelController.hasShowPanel(any()) } returns false
        every { addLabelPanelActivity.showAddLabelPanel() } answers { callOriginal() }
        every { addLabelPanelActivity.hasShowPanel } answers { callOriginal() }

        val supportFragmentManager = mockk<FragmentManager>()
        every { addLabelPanelActivity.supportFragmentManager } returns supportFragmentManager

        //when
        addLabelPanelActivity.showAddLabelPanel()
        //then
        verify { addFileLabelController.setOnDismissListener(any(), any()) }
        verify { addFileLabelController.hasShowPanel(any()) }
        Assert.assertEquals(addLabelPanelActivity.hasShowPanel, false)
    }


    @Test
    fun `should call none when call showAddLabelPanel if fromSaveInstance is false and none canShowAddLabelPanel`() {
        //given
        every { addLabelPanelActivity.fromSaveInstance } answers { callOriginal() }
        every { addLabelPanelActivity.fromSaveInstance } returns false
        every { addLabelPanelActivity.canShowAddLabelPanel() } returns false

        val addFileLabelController: AddFileLabelController = mockk()
        every { addLabelPanelActivity.addFileLabelController } returns addFileLabelController
        every {
            addFileLabelController.showAddLabelFragment(
                any(),
                any<ArrayList<AddFileLabelFileBean>>(),
                any()
            )
        } just runs
        every { addFileLabelController.hasShowPanel(any()) } returns false
        every { addLabelPanelActivity.showAddLabelPanel() } answers { callOriginal() }
        every { addLabelPanelActivity.hasShowPanel } answers { callOriginal() }

        val supportFragmentManager = mockk<FragmentManager>()
        every { addLabelPanelActivity.supportFragmentManager } returns supportFragmentManager

        //when
        addLabelPanelActivity.showAddLabelPanel()
        //then
        verify(inverse = true) {
            addFileLabelController.showAddLabelFragment(
                any(),
                any<ArrayList<AddFileLabelFileBean>>(),
                any()
            )
        }
        verify(inverse = true) { addFileLabelController.setOnDismissListener(any(), any()) }
        Assert.assertEquals(addLabelPanelActivity.hasShowPanel, false)
    }

    @Test
    fun `should call none when call showAddLabelPanel if fromSaveInstance is false and fileUris is null`() {
        //given
        every { addLabelPanelActivity.fromSaveInstance } answers { callOriginal() }
        every { addLabelPanelActivity.fromSaveInstance } returns false
        every { addLabelPanelActivity.canShowAddLabelPanel() } returns true

        val addFileLabelController: AddFileLabelController = mockk()
        every { addLabelPanelActivity.addFileLabelController } returns addFileLabelController
        every {
            addFileLabelController.showAddLabelFragment(
                any(),
                any<ArrayList<AddFileLabelFileBean>>(),
                any()
            )
        } just runs
        every { addLabelPanelActivity.fileUris } answers { callOriginal() }

        val fileUris = mockk<ArrayList<String>>()
        every { addLabelPanelActivity.fileUris } returns fileUris
        every { fileUris.isNullOrEmpty() } returns true
        every { addLabelPanelActivity.finish() } just runs

        every { addLabelPanelActivity.showAddLabelPanel() } answers { callOriginal() }
        every { addLabelPanelActivity.hasShowPanel } answers { callOriginal() }

        every { CustomToast.showShort(any<Int>()) } just runs

        val supportFragmentManager = mockk<FragmentManager>()
        every { addLabelPanelActivity.supportFragmentManager } returns supportFragmentManager

        //when
        addLabelPanelActivity.showAddLabelPanel()
        //then
        verify(inverse = true) {
            addFileLabelController.showAddLabelFragment(
                any(),
                any<ArrayList<AddFileLabelFileBean>>(),
                any()
            )
        }
        verify { addLabelPanelActivity.finish() }
        Assert.assertEquals(addLabelPanelActivity.hasShowPanel, false)
    }

    @Test
    fun `should call none when call showAddLabelPanel if fromSaveInstance is false and fileList is null`() {
        //given
        every { addLabelPanelActivity.fromSaveInstance } answers { callOriginal() }
        every { addLabelPanelActivity.fromSaveInstance } returns false
        every { addLabelPanelActivity.canShowAddLabelPanel() } returns true

        val addFileLabelController: AddFileLabelController = mockk()
        every { addLabelPanelActivity.addFileLabelController } returns addFileLabelController
        every {
            addFileLabelController.showAddLabelFragment(
                any(),
                any<ArrayList<AddFileLabelFileBean>>(),
                any()
            )
        } just runs

        val fileList = mockk<ArrayList<AddFileLabelFileBean>>()
        val addLabelPanelModel = mockk<AddLabelPanelModel>()
        every { addLabelPanelActivity.addLabelPanelModel } returns addLabelPanelModel
        every { addLabelPanelModel.getFileList(any()) } returns fileList
        every { fileList.isEmpty() } returns true

        val fileUris = mockk<ArrayList<String>>()
        every { addLabelPanelActivity.fileUris } returns fileUris
        every { fileUris.isNullOrEmpty() } returns false
        every { addLabelPanelActivity.finish() } just runs

        every { addLabelPanelActivity.showAddLabelPanel() } answers { callOriginal() }
        every { addLabelPanelActivity.hasShowPanel } answers { callOriginal() }

        every { CustomToast.showShort(any<Int>()) } just runs

        val supportFragmentManager = mockk<FragmentManager>()
        every { addLabelPanelActivity.supportFragmentManager } returns supportFragmentManager

        //when
        addLabelPanelActivity.showAddLabelPanel()
        //then
        verify(inverse = true) {
            addFileLabelController.showAddLabelFragment(
                any(),
                any<ArrayList<AddFileLabelFileBean>>(),
                any()
            )
        }
        verify { addLabelPanelActivity.finish() }
        Assert.assertEquals(addLabelPanelActivity.hasShowPanel, false)
    }

    @Test
    fun `should call showAddLabelFragment when call showAddLabelPanel if fromSaveInstance is false`() {
        //given
        every { addLabelPanelActivity.fromSaveInstance } answers { callOriginal() }
        every { addLabelPanelActivity.fromSaveInstance } returns false
        every { addLabelPanelActivity.canShowAddLabelPanel() } returns true

        val addFileLabelController: AddFileLabelController = mockk()
        every { addLabelPanelActivity.addFileLabelController } returns addFileLabelController
        every {
            addFileLabelController.showAddLabelFragment(
                any(),
                any<ArrayList<AddFileLabelFileBean>>(),
                any()
            )
        } just runs

        val fileList = mockk<ArrayList<AddFileLabelFileBean>>()
        val addLabelPanelModel = mockk<AddLabelPanelModel>()
        every { addLabelPanelActivity.addLabelPanelModel } returns addLabelPanelModel
        every { addLabelPanelModel.getFileList(any()) } returns fileList
        every { fileList.isEmpty() } returns false

        val fileUris = mockk<ArrayList<String>>()
        every { addLabelPanelActivity.fileUris } returns fileUris
        every { fileUris.isNullOrEmpty() } returns false
        every { addLabelPanelActivity.finish() } just runs

        every { addLabelPanelActivity.showAddLabelPanel() } answers { callOriginal() }
        every { addLabelPanelActivity.hasShowPanel } answers { callOriginal() }

        every { CustomToast.showShort(any<Int>()) } just runs

        val supportFragmentManager = mockk<FragmentManager>()
        every { addLabelPanelActivity.supportFragmentManager } returns supportFragmentManager

        //when
        addLabelPanelActivity.showAddLabelPanel()
        //then
        verify {
            addFileLabelController.showAddLabelFragment(
                any(),
                any<ArrayList<AddFileLabelFileBean>>(),
                any()
            )
        }
        verify(inverse = true) { addLabelPanelActivity.finish() }
        Assert.assertEquals(addLabelPanelActivity.hasShowPanel, true)
    }
}