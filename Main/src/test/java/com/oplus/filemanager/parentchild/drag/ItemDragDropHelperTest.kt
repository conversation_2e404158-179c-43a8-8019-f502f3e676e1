package com.oplus.filemanager.parentchild.drag

import android.app.Activity
import android.content.ClipDescription
import android.content.Context
import android.os.PersistableBundle
import android.view.DragEvent
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.*
import com.filemanager.common.dragselection.action.DropDispatchAction
import com.filemanager.common.dragselection.action.DropFolderAction
import com.filemanager.common.dragselection.util.DropHandleHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.view.SelectDragItemViewParent
import com.oplus.filemanager.parentchild.view.SideNavigationItemContainer
import com.oplus.filemanager.parentchild.view.SideNavigationStorageContainer
import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.impl.annotations.SpyK
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * ItemDragDropHelper的单元测试类
 * 用于测试ItemDragDropHelper的各种拖放处理逻辑
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class ItemDragDropHelperTest {

    // 模拟Activity对象
    @RelaxedMockK
    private lateinit var mockActivity: Activity

    // 模拟根视图
    @MockK
    private lateinit var mockRootView: View

    // 模拟检测视图
    @MockK
    private lateinit var mockDetectView: View

    // 模拟拖拽事件
    @MockK
    private lateinit var mockDragEvent: DragEvent

    // 模拟剪贴板描述
    @MockK
    private lateinit var mockClipDescription: ClipDescription

    // 被测对象 - ItemDragDropHelper
    @SpyK
    private var helper = ItemDragDropHelper(RuntimeEnvironment.getApplication())

    // 模拟拖拽父视图
    @MockK
    private lateinit var mockSelectDragParent: SelectDragItemViewParent

    // 模拟项目视图
    @MockK
    private lateinit var mockItemView: View

    // 模拟导航项容器
    @MockK
    private lateinit var mockNavigationItem: SideNavigationItemContainer

    // 模拟存储容器
    @MockK
    private lateinit var mockStorageContainer: SideNavigationStorageContainer

    // 模拟装饰视图
    @MockK
    private lateinit var mockDecorView: ViewGroup

    /**
     * 测试前的初始化方法
     * 用于设置模拟对象和准备测试环境
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkObject(DragUtils)
        mockkObject(CustomToast)
        mockkObject(KtViewUtils)
        mockkObject(DropDispatchAction)
        mockkObject(DropFolderAction)
        mockkStatic(CategoryHelper::class)
        mockkStatic(Log::class)

        // 设置模拟对象的返回值
        every { mockActivity.window.decorView } returns mockDecorView
        every { mockDragEvent.clipDescription } returns mockClipDescription
        every { mockClipDescription.extras } returns PersistableBundle().apply {
            putBoolean(CommonConstants.KEY_HAS_ANDROID_DATA_FILE, true)
        }
        every { Log.d(any(), any()) } just Runs
    }

    /**
     * 测试后的清理方法
     * 用于释放资源和重置模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试处理拖放事件 - 当目标视图是MAC_FRAGMENT_VIEW时应返回false
     * 验证当拖放目标是不允许的MAC片段视图时，处理结果是否正确
     */
    @Test
    fun `handleDragEvent ACTION_DROP with MAC_FRAGMENT_VIEW should return false`() {
        // Given - 准备测试数据
        val dropTag = DropTag(-1, DropTag.Type.MAC_FRAGMENT_VIEW)
        every { mockDetectView.tag } returns dropTag
        every { mockDragEvent.action } returns DragEvent.ACTION_DROP

        // When - 执行测试方法
        val result = helper.handleDragEvent(mockRootView, mockDetectView, mockDragEvent, mockActivity, false)

        // Then - 验证结果
        assertFalse(result)
        assertFalse(helper.dropResult)
        verify { DragUtils.isDragging = false }
    }
}