/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.superapp
 * * Version     : 1.0
 * * Date        : 2020/9/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.filerefactor.ui.superapp

import com.coloros.filemanager.BaseTest
import com.filemanager.common.constants.KtConstants
import com.filemanager.superapp.ui.superapp.SuperAppParentFragmentViewModel
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class SuperAppParentViewModelTest : BaseTest() {
    private val mSuperAppParentViewModel = SuperAppParentFragmentViewModel()

    @Test
    @Throws(Exception::class)
    fun should_change_scan_view_model_when_click_scanModeItem() {
        var initScanMode = mSuperAppParentViewModel.mBrowseModeState.value
        Assert.assertEquals(KtConstants.SCAN_MODE_GRID, initScanMode)
        mSuperAppParentViewModel.clickScanModeItem()
        initScanMode = mSuperAppParentViewModel.mBrowseModeState.value
        Assert.assertEquals(KtConstants.SCAN_MODE_LIST, initScanMode)
        mSuperAppParentViewModel.clickScanModeItem()
        initScanMode = mSuperAppParentViewModel.mBrowseModeState.value
        Assert.assertEquals(KtConstants.SCAN_MODE_GRID, initScanMode)
    }
}