/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DownloadProgressSeedlingCardProvider
 * * Description : 下载进度的流体云 CardProvider
 * * Version     : 1.0
 * * Date        : 2024/12/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.download

import android.content.Context
import android.os.Bundle
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.remotedevice.DownloadTransferCard
import com.oplus.pantanal.seedling.SeedlingCardWidgetProvider
import com.oplus.pantanal.seedling.bean.SeedlingCard
import com.oplus.pantanal.seedling.update.SeedlingCardOptions

class DownloadSeedlingCardProvider : SeedlingCardWidgetProvider() {

    companion object {
        private const val TAG = "DownloadProgressSeedlingProvider"
        var card: SeedlingCard? = null
    }


    override fun onCardCreate(context: Context, card: SeedlingCard) {
        Log.e(TAG, "onCardCreate $card")
        DownloadSeedlingCardProvider.card = card
    }

    override fun onCardObserve(context: Context, cards: List<SeedlingCard>) {
        Log.e(TAG, "onCardObserve $cards")
    }

    override fun onDestroy(context: Context, card: SeedlingCard) {
        Log.e(TAG, "onDestroy $card")
        DownloadSeedlingCardProvider.card = null
    }

    override fun onHide(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onHide $card")
    }

    override fun onShow(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onShow $card")
    }

    override fun onSubscribed(context: Context, card: SeedlingCard) {
        Log.e(TAG, "onSubscribed $card")
    }

    override fun onUnSubscribed(context: Context, card: SeedlingCard) {
        Log.e(TAG, "onUnSubscribed $card")
    }

    override fun onUpdateData(context: Context, card: SeedlingCard, data: Bundle) {
        Log.d(TAG, "onUpdateData data:$data")
        val downloadCard = createDownloadCard(data)
        val cardOptions = SeedlingCardOptions().apply {
            pageId = downloadCard.pageId()
            requestHideStatusBar = !DownloadTransferCard.showFluidCloud
            notificationIdList = FluidCloudHelper.notifyIds
        }
        updateData(card, downloadCard.toJSON(), cardOptions)
    }
}