/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.apk
 * * Version     : 1.0
 * * Date        : 2020/7/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.filerefactor.ui.apk

import com.coloros.filemanager.BaseTest
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.dropdrag.SelectionTracker
import com.oplus.filemanager.ad.AdvertManager
import io.mockk.every
import io.mockk.mockkObject
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.Ignore
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class ApkViewModelTest : BaseTest() {
    private val mApkViewModel = com.oplus.filemanager.category.apk.ui.ApkViewModel()

    @Before
    override fun setUp() {
        mContext = RuntimeEnvironment.application
        val file1 = MediaFileWrapper()
        file1.mId = 1
        val file2 = MediaFileWrapper()
        file2.mId = 2
        val file3 = MediaFileWrapper()
        file3.mId = 3
        val list = ArrayList<MediaFileWrapper>()
        list.add(file1)
        list.add(file2)
        list.add(file3)
        mApkViewModel.mApkDataList = list
        mApkViewModel.mUiState.value = BaseUiModel(list, mApkViewModel.mModeState,
                ArrayList(), HashMap())
    }


    @Test
    @Ignore("Ignore temporarily, solve oppoPallDomesticAplLevelAllDebugUnitTest error")
    @Throws(Exception::class)
    fun should_clickToolbarSelectAll_when_view_model_fun_clickToolbarSelectAll() {
        mApkViewModel.mUiState.value?.mSelectedList?.clear()
        mApkViewModel.clickToolbarSelectAll()
        Assert.assertEquals(mApkViewModel.mUiState.value?.mFileList?.size, mApkViewModel.mUiState.value?.mSelectedList?.size)
        mApkViewModel.clickToolbarSelectAll()
        Assert.assertEquals(0, mApkViewModel.mUiState.value?.mSelectedList?.size)
        mApkViewModel.mUiState.value?.mSelectedList?.add(mApkViewModel.mUiState.value?.mFileList?.get(0)?.mId.hashCode())
        mApkViewModel.clickToolbarSelectAll()
        Assert.assertEquals(mApkViewModel.mUiState.value?.mFileList?.size, mApkViewModel.mUiState.value?.mSelectedList?.size)
    }

    @Test
    fun testGetRealFileSize() {
        mApkViewModel.mUiState.value = null
        Assert.assertEquals(0, mApkViewModel.getRealFileSize())

        val file1 = MediaFileWrapper()
        file1.mId = 1
        val file2 = MediaFileWrapper()
        file2.mId = 2
        val file3 = MediaFileWrapper()
        file3.mId = 3
        val list = ArrayList<MediaFileWrapper>()
        list.add(file1)
        list.add(file2)
        list.add(file3)
        val fakeUiModel = BaseUiModel(list, mApkViewModel.mModeState, ArrayList(), HashMap())
        mApkViewModel.mUiState.value = fakeUiModel
        mockkObject(AdvertManager)
        every { AdvertManager.getAdViewCount(any()) } returns 1
        Assert.assertEquals(2, mApkViewModel.getRealFileSize())
    }

    @Test
    fun testGetRecyclerViewScanMode() {
        Assert.assertEquals(SelectionTracker.LAYOUT_TYPE.LIST, mApkViewModel.getRecyclerViewScanMode())
        mApkViewModel.mScanModeValue = KtConstants.SCAN_MODE_GRID
        Assert.assertEquals(SelectionTracker.LAYOUT_TYPE.GRID, mApkViewModel.getRecyclerViewScanMode())
    }

    @After
    override fun tearDown() {
        mContext = null
    }
}
