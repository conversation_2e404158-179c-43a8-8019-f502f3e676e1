/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileServiceApi
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/13 16:49
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/13       1.0      create
 ***********************************************************************/
package com.oplus.fileservice

import android.content.Context
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.reply.OperateSyncTask
import java.io.File

object FileServiceApi : IFileService {

    override fun syncOperate(operateType: Int, paths: HashSet<String?>) {
        val operateSyncTask = OperateSyncTask(operateType, paths)
        FileServiceManager.getInstance().runAsync(operateSyncTask)
    }

    override fun renameTo(context: Context, oldFile: File, newFile: File): Int {
        val sourceFileInfo = FileInfo(oldFile.name, oldFile.absolutePath)
        val targetFileInfo = FileInfo(newFile.name, newFile.absolutePath)
        return FileServiceManager.getInstance().renameTo(context, sourceFileInfo, targetFileInfo)
    }

    override fun cutTo(context: Context, oldFiles: List<File>, newFile: File): Int {
        val targetFileInfo = FileInfo(newFile.name, newFile.absolutePath)
        val sourceFileInfos = mutableListOf<FileInfo>()
        oldFiles.forEach { oldFile ->
            sourceFileInfos.add(FileInfo(oldFile.name, oldFile.absolutePath))
        }
        return FileServiceManager.getInstance().cutTo(context, sourceFileInfos, targetFileInfo)
    }
}