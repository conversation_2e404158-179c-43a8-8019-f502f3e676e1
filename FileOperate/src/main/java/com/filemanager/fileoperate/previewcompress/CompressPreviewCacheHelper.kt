/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileActionDecompress.kt
 ** Description: File decompress function
 ** Version: 1.0
 ** Date: 2020/5/25
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.previewcompress

import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.sort.LastModifiedComparatorGenerator
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Md5Utils
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.decompress.BaseDecompressFile
import org.apache.commons.io.FileUtils
import java.io.File
import java.util.*

class CompressPreviewCacheHelper {
    companion object {
        private const val TAG = "CompressPreviewCacheHelper"
        private const val STANDARD_SIZE = 350 * 1024 * 1024
        private const val MAX_LARGE_CACHE_NUM = 3
        private const val PREVIEW_CACHE_DIR = "preview_cache"
        private const val SCRAP_FILE_DIR = "scraps"
        private const val LARGE_FILE_DIR = "large"

        // Just used to store the temp file fore UI-a to UI-b
        private var mTransferPreviewFile: Map<String, MutableList<out BaseDecompressFile>?>? = null

        fun fetchTransferPreviewFile(): Map<String, MutableList<out BaseDecompressFile>?>? {
            val t = mTransferPreviewFile
            mTransferPreviewFile = null
            return t
        }

        fun storeTransferPreviewFile(fileList: Map<String, MutableList<out BaseDecompressFile>?>?) {
            mTransferPreviewFile = fileList
        }

        fun getCacheFile(rarFile: BaseFileBean, file: BaseDecompressFile): BaseFileBean? {
            val cacheFile = File(getCacheDir(file), getCacheFileName(rarFile, file))
            return if (cacheFile.exists()) PathFileWrapper(cacheFile.absolutePath) else null
        }

        fun saveCacheFile(rarFile: BaseFileBean, file: BaseDecompressFile): BaseFileBean? {
            val rootFile = getCacheDir(file)
            val cacheFile = File(rootFile, getCacheFileName(rarFile, file))
            val sourceFile = File(rootFile, file.mData)
            Log.d(TAG, "saveCacheFiled sourceFile = $sourceFile,cacheFile=$cacheFile")
            return if (sourceFile.renameTo(cacheFile)) PathFileWrapper(cacheFile.absolutePath) else null
        }

        fun removeCacheFile(rarFile: BaseFileBean, file: BaseDecompressFile) {
            val rootFile = getCacheDir(file)
            val cacheFile = File(rootFile, getCacheFileName(rarFile, file))
            val sourceFile = File(rootFile, file.mDisplayName)
            cacheFile.delete()
            sourceFile.delete()
        }

        fun getCacheDir(file: BaseDecompressFile): File {
            return internalGetCacheDir(isScrapFile(file))
        }

        private fun internalGetCacheDir(scrap: Boolean): File {
            return if (scrap) {
                File(MyApplication.sAppContext.cacheDir, PREVIEW_CACHE_DIR + File.separator + SCRAP_FILE_DIR)
            } else {
                File(MyApplication.sAppContext.cacheDir, PREVIEW_CACHE_DIR + File.separator + LARGE_FILE_DIR)
            }
        }

        fun beforeOpenFile(file: BaseDecompressFile) {
            if (isScrapFile(file)) {
                internalGetCacheDir(true).apply {
                    if (this.exists() && this.isDirectory) {
                        FileUtils.deleteQuietly(this)
                    }
                }
            } else {
                removeLargeFile()
            }
        }

        private fun isScrapFile(file: BaseDecompressFile): Boolean {
            return file.mSize < STANDARD_SIZE
        }

        private fun removeLargeFile() {
            val largeCacheDir = internalGetCacheDir(false)
            if (largeCacheDir.exists() && largeCacheDir.isDirectory) {
                // Remove all directory in this dir
                largeCacheDir.listFiles()?.filter { it.isDirectory }?.forEach {
                    FileUtils.deleteQuietly(it)
                }

                val largeCaches = largeCacheDir.listFiles()?.filter { it.isFile }?.map { PathFileWrapper(it.absolutePath) }
                if ((largeCaches?.size ?: 0) < MAX_LARGE_CACHE_NUM) {
                    return
                }
                //if the length is equal to MAX_LARGE_CACHE_NUM, then delete one file for next cache file
                val needDeleteNum = largeCaches!!.size - MAX_LARGE_CACHE_NUM + 1
                Collections.sort(largeCaches, LastModifiedComparatorGenerator.comparator)
                for (i in 0 until needDeleteNum) {
                    JavaFileHelper.delete(largeCaches[i])
                }
            }
        }

        private fun getCacheFileName(sourceFile: BaseFileBean, compressFile: BaseDecompressFile): String {
            val keyStr = sourceFile.mData + "-" + sourceFile.mDateModified + "-" + compressFile.mDisplayName
            return Md5Utils.toKey(keyStr) + getFileSuffix(compressFile)
        }

        private fun getFileSuffix(compressFile: BaseDecompressFile): String? {
            var index = -1
            if (!compressFile.mIsDirectory) {
                index = compressFile.mDisplayName?.lastIndexOf(".") ?: -1
            }
            return when {
                index > 0 -> {
                    compressFile.mDisplayName!!.substring(index)
                }
                //is the hidden file And  no file extension
                index == 0 -> {
                    compressFile.mDisplayName
                }
                else -> ""
            }
        }
    }
}