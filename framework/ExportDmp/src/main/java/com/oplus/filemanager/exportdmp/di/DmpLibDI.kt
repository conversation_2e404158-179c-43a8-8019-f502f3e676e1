/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: DmpLibDI.kt
 ** Description: dmpLib的注入类
 ** Version: 1.0
 ** Date: 2024/8/28
 ** Author: muning
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.exportdmp.di

import androidx.annotation.Keep
import com.oplus.filemanager.exportdmp.DmpSearchApi
import com.oplus.filemanager.interfaze.dmpsearch.IExportDmpSearchApi
import org.koin.dsl.module

@Keep
class DmpLibDI {

    val dmpLibModule = module {
        single<IExportDmpSearchApi>(createdAtStart = true) {
            DmpSearchApi
        }
    }
}