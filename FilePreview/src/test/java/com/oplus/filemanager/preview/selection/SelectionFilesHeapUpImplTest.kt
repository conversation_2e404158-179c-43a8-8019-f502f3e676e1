package com.oplus.filemanager.preview.selection

import android.content.Context
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.preview.selection.data.HeadUpImageData
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat
import com.oplus.filemanager.preview.selection.data.SelectionItem
import com.oplus.filemanager.preview.selection.loader.AbsHeapUpImageTarget
import com.oplus.filemanager.preview.selection.loader.HeapUpImageLoader
import com.oplus.filemanager.preview.selection.utils.SelectionConstants.MAX_KEEP_IN_MEMORY_COUNT
import com.oplus.filemanager.preview.selection.utils.SelectionStatStringHelper
import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentLinkedDeque
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * SelectionFilesHeapUpImpl 的单元测试类
 * 用于测试 SelectionFilesHeapUpImpl 类的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SelectionFilesHeapUpImplTest {

    // 使用 MockK 框架模拟 Fragment 对象
    @MockK
    private lateinit var mockFragment: Fragment

    // 使用 MockK 框架模拟 ViewModel 对象
    @MockK
    private lateinit var mockViewModel: SelectionHeapUpViewModel

    // 使用 RelaxedMockK 模拟容器接口，自动提供默认实现
    @RelaxedMockK
    private lateinit var mockContainer: ISelectionHeapUpContainer

    // 使用 RelaxedMockK 模拟文件对象
    @RelaxedMockK
    private lateinit var mockFileBean: BaseFileBean

    // 使用 RelaxedMockK 模拟图片加载目标对象
    @RelaxedMockK
    private lateinit var mockImageTarget: AbsHeapUpImageTarget<Any>

    // 使用 RelaxedMockK 模拟图片加载器
    @RelaxedMockK
    private lateinit var mockImageLoader: HeapUpImageLoader

    // 被测对象
    private lateinit var selectionFilesHeapUp: SelectionFilesHeapUpImpl

    /**
     * 测试前的初始化方法
     * 1. 初始化 MockK 注解
     * 2. 设置 Fragment 的模拟行为
     * 3. 设置 ViewModel 的模拟行为
     * 4. 创建被测对象实例
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        // 模拟 Fragment 返回 Context 和 LifecycleOwner
        every { mockFragment.requireContext() } returns mockk(relaxed = true)
        every { mockFragment.viewLifecycleOwner } returns mockk(relaxed = true)
        // 设置 ViewModel 的启动和释放方法不做任何操作
        every { mockViewModel.launchSelectionStat(any(), any()) } just Runs
        every { mockViewModel.launchSelectionItems(any(), any()) } just Runs
        every { mockViewModel.releaseSelectionStatLauncher(any()) } just Runs
        every { mockViewModel.releaseSelectionItemsLauncher(any()) } just Runs
        // 设置容器返回堆叠视图
        every { mockContainer.heapUpView } returns mockk(relaxed = true)
        // 创建被测对象实例
        selectionFilesHeapUp = SelectionFilesHeapUpImpl(mockFragment, mockViewModel)
    }

    /**
     * 测试后的清理方法
     * 清除所有 MockK 模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试 release 方法
     * 验证是否正确地释放了所有资源
     * 1. 先附加到容器
     * 2. 调用 release 方法
     * 3. 验证 ViewModel 的释放方法被调用
     */
    @Test
    fun `test release should clear all resources`() {
        // 附加到容器
        selectionFilesHeapUp.attachToContainer(mockContainer)
        // 调用释放方法
        selectionFilesHeapUp.release()

        // 验证 ViewModel 的释放方法被调用
        verify {
            mockViewModel.releaseSelectionStatLauncher(any())
            mockViewModel.releaseSelectionItemsLauncher(any())
        }
    }

    /**
     * 测试 updatePaddingBottom 方法
     * 验证是否正确更新了容器的底部内边距
     * 1. 先附加到容器
     * 2. 调用 updatePaddingBottom 方法
     * 3. 验证容器的 setPadding 方法被调用且底部内边距正确
     */
    @Test
    fun `test updatePaddingBottom should update container padding`() {
        // 附加到容器
        selectionFilesHeapUp.attachToContainer(mockContainer)
        // 调用更新内边距方法
        selectionFilesHeapUp.updatePaddingBottom(100)

        // 验证容器的 setPadding 方法被调用且底部内边距为100
        verify { mockContainer.containerRoot.setPadding(any(), any(), any(), 100) }
    }
}