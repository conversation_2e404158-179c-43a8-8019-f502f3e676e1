/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ArchivePreviewCardUiImpl.kt
 * Description:
 *     Implement to load preview card UI when can preview archive file.
 *
 * Version: 1.0
 * Date: 2024-10-15
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-15   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.archive

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.GridLayoutManager
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.BrowserPathBar
import com.filemanager.common.view.FileManagerRecyclerView
import com.filemanager.compresspreview.ui.CompressPreviewAdapter
import com.filemanager.compresspreview.ui.CompressPreviewViewModel
import com.filemanager.fileoperate.decompress.BaseDecompressFile
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.IUiConfigChangeObserver
import com.oplus.filemanager.preview.utils.ArchiveIconHelper
import java.io.File

internal class ArchivePreviewCardUiImpl(
    private val fragment: ArchivePreviewFragment,
    private val archiveModel: ArchivePreviewViewModel,
    private val previewCardLayout: ConstraintLayout
) : IArchiveUiPreviewCard, IUiConfigChangeObserver, DefaultLifecycleObserver,
    OnRecyclerItemClickListener {

    private companion object {
        private const val TAG = "ArchivePreviewCardUiImpl"
    }

    private val fileIconView =
        previewCardLayout.findViewById<AppCompatImageView>(R.id.preview_archive_file_icon)
    private val fileNameView =
        previewCardLayout.findViewById<AppCompatTextView>(R.id.preview_archive_file_name)
    private val fileInfoView =
        previewCardLayout.findViewById<AppCompatTextView>(R.id.preview_archive_file_info)
    private val archivePathBar =
        previewCardLayout.findViewById<BrowserPathBar>(R.id.preview_archive_path_bar)
    private val archiveContentsView =
        previewCardLayout.findViewById<FileManagerRecyclerView>(R.id.preview_archive_file_contents)
    private val archiveBodyView =
        previewCardLayout.findViewById<ViewGroup>(R.id.preview_archive_body)
    private val previewAdapter = CompressPreviewAdapter(
        fragment.requireActivity(),
        fragment.lifecycle,
        context.resources.getDimensionPixelOffset(R.dimen.preview_archive_item_padding_start)
    )
    private val layoutManager = GridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1)
    private val folderTransformAnimator = FolderTransformAnimator()

    private val fileEmptyController by lazy { FileEmptyController(fragment.lifecycle) }

    private val context: Context
        get() = previewCardLayout.context
    private val vmActivity: BaseVMActivity?
        get() = fragment.activity as? BaseVMActivity

    init {
        previewAdapter.setHasStableIds(true)
        previewAdapter.setSelectEnabled(false)
        previewAdapter.setChoiceModeAnimFlag(false)
        previewAdapter.setOnRecyclerItemClickListener(this)
        archiveContentsView.isNestedScrollingEnabled = true
        archiveContentsView.clipToPadding = false
        archiveContentsView.isVerticalFadingEdgeEnabled = true
        archiveContentsView.adapter = previewAdapter
        archiveContentsView.layoutManager = layoutManager
        archiveContentsView.itemAnimator = folderTransformAnimator.apply {
            changeDuration = BrowserPathBar.FILE_BROWSER_FOLDER_ANIM_TIME
            addDuration = BrowserPathBar.FILE_BROWSER_FOLDER_ANIM_TIME
            removeDuration = BrowserPathBar.FILE_BROWSER_FOLDER_ANIM_TIME
            moveDuration = BrowserPathBar.FILE_BROWSER_FOLDER_ANIM_TIME
        }
        fragment.addUIConfigChangeObserver(this)
        fragment.lifecycle.addObserver(this)
    }

    override fun setVisible(visible: Boolean) {
        if (visible) {
            previewCardLayout.visibility = View.VISIBLE
        } else {
            previewCardLayout.visibility = View.GONE
        }
    }

    override fun setPreviewFile(archiveFile: BaseFileBean) {
        fileNameView.text = archiveFile.mDisplayName
        val iconRes = ArchiveIconHelper.getArchiveIconId(archiveFile)
        fileIconView.setImageResource(iconRes)
        val size = Utils.byteCountToDisplaySize(archiveFile.mSize)
        val dateAndTime = Utils.getDateFormat(context, archiveFile.mDateModified)
        fileInfoView.text = Utils.formatDetail(context, size, dateAndTime)
    }

    override fun setPreviewContents(compressModel: CompressPreviewViewModel) {
        setPreviewPath(compressModel)
        setPreviewList(compressModel)
    }

    private fun setPreviewPath(compressModel: CompressPreviewViewModel) {
        val pathHelper = compressModel.mPathHelp ?: kotlin.run {
            Log.e(TAG, "setPreviewPath: ERROR! no FilePathHelper")
            return
        }
        pathHelper.getRootPath()?.let(archivePathBar::showRootPathString)
        archivePathBar.setPathHelper(pathHelper)
        archivePathBar.setOnPathClickListener { index, _ ->
            compressModel.clickPathBar(index)
        }
    }

    private fun setPreviewList(compressModel: CompressPreviewViewModel) {
        checkDirInPreview(compressModel)
        val lifecycleOwner = fragment.viewLifecycleOwner
        compressModel.uiState.value?.also {
            onFileUiModelChanged(compressModel, it)
        }
        compressModel.uiState.observe(lifecycleOwner) { fileUiModel ->
            onFileUiModelChanged(compressModel, fileUiModel)
        }
        compressModel.mPositionModel.value?.let {
            onPositionModelChanged(compressModel, it)
        }
        compressModel.mPositionModel.observe(lifecycleOwner) { positionModel ->
            onPositionModelChanged(compressModel, positionModel)
        }
    }

    private fun checkDirInPreview(compressModel: CompressPreviewViewModel) {
        val hasDir = ((compressModel.mPreviewMap?.size ?: 0) > 1)
        Log.d(TAG, "checkDirInPreview: hasDir=$hasDir")
        if (hasDir) {
            archivePathBar.visibility = View.VISIBLE
        } else {
            archivePathBar.visibility = View.GONE
        }
    }

    private fun onFileUiModelChanged(
        compressModel: CompressPreviewViewModel,
        fileUiModel: CompressPreviewViewModel.CompressPreviewUiModel
    ) {
        if (!compressModel.mModeState.initState) {
            return
        }
        Log.d(TAG, "onFileUiModelChanged: fileList.size=${fileUiModel.fileList.size}")
        if (fileUiModel.fileList.isEmpty()) {
            showEmptyView()
        } else {
            hideEmptyView()
        }
        (fileUiModel.fileList as? MutableList<BaseDecompressFile>)?.let { list ->
            folderTransformAnimator.mIsFolderInAnimation = compressModel.mIsFolderIn
            previewAdapter.setData(list, fileUiModel.selectedList, compressModel.mNeedScroll)
        }
    }

    private fun onPositionModelChanged(
        compressModel: CompressPreviewViewModel,
        positionModel: CompressPreviewViewModel.PositionModel
    ) {
        if (!compressModel.mModeState.initState) {
            return
        }
        var currentPath =
            compressModel.mPathHelp?.getRootPath()?.takeIf { it.isNotEmpty() } ?: return
        Log.d(TAG, "onPositionModelChanged")
        val updatePath = positionModel.mCurrentPath
        if (updatePath.isNotEmpty()) {
            currentPath = currentPath + File.separator + updatePath
        }
        if (currentPath.endsWith(File.separator)) {
            currentPath = currentPath.run { substring(0, lastIndexOf(File.separator)) }
        }
        if (archivePathBar.getCurrentPath() != currentPath) {
            archivePathBar.setCurrentPath(currentPath)
        }
    }

    private fun showEmptyView() {
        val activity = vmActivity ?: return
        Log.d(TAG, "showEmptyView")
        fileEmptyController.showFileEmptyView(activity, archiveBodyView)
        fileEmptyController.setFileEmptyTitle(com.filemanager.common.R.string.empty_file)
    }

    private fun hideEmptyView() {
        fileEmptyController.hideFileEmptyView()
    }

    override fun onItemClick(view: View, position: Int) {
        val activity = vmActivity ?: run {
            Log.e(TAG, "onItemClick: ERROR! not BaseVMActivity")
            return
        }
        val key = previewAdapter.getItemKeyByPosition(position) ?: run {
            Log.e(TAG, "onItemClick: ERROR! no item key")
            return
        }
        val firstPosition = layoutManager.findFirstVisibleItemPosition()
        val contentsPaddingTop = archiveContentsView.paddingTop
        val offset =
            layoutManager.findViewByPosition(firstPosition)?.top ?: contentsPaddingTop
        archiveModel.compressModel.onItemClick(
            activity,
            key,
            firstPosition,
            offset - contentsPaddingTop
        )
    }

    override fun onItemLongClick(view: View, position: Int) {
        onItemClick(view, position)
    }

    override fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        fileEmptyController.changeEmptyFileIcon()
        archiveModel.compressModel.onConfigurationChanged()
    }

    override fun onStart(owner: LifecycleOwner) {
        fragment.addUIConfigChangeObserver(this)
    }

    override fun onStop(owner: LifecycleOwner) {
        fragment.removeUIConfigChangeObserver(this)
    }
}