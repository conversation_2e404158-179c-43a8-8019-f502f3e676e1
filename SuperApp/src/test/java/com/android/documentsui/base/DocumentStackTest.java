package com.android.documentsui.base;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;

import android.os.Parcel;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.ProtocolException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedList;
import java.util.Objects;

@RunWith(RobolectricTestRunner.class)
@Config(sdk = 29)
public class DocumentStackTest {

    private DocumentStack stack;
    private RootInfo mockRoot;
    private DocumentInfo mockDoc1;
    private DocumentInfo mockDoc2;

    @Before
    public void setUp() {
        mockRoot = new RootInfo();
        mockRoot.title = "RootTitle";
        mockDoc1 = new DocumentInfo();
        mockDoc1.displayName = "Doc1";
        mockDoc1.mimeType = "mime/type1"; // 修复NPE问题
        mockDoc2 = new DocumentInfo();
        mockDoc2.displayName = "Doc2";
        mockDoc2.mimeType = "mime/type2"; // 修复NPE问题
    }

    @Test
    public void testConstructor_emptyStack() {
        stack = new DocumentStack();
        assertFalse(stack.isInitialized());
        assertNull(stack.getRoot());
        assertTrue(stack.isEmpty());
        assertEquals(0, stack.size());
    }

    @Test
    public void testConstructor_withRootAndDocs() {
        stack = new DocumentStack(mockRoot, mockDoc1, mockDoc2);
        assertTrue(stack.isInitialized());
        assertEquals(mockRoot, stack.getRoot());
        assertFalse(stack.isEmpty());
        assertEquals(2, stack.size());
        assertEquals(mockDoc2, stack.peek());
    }

    @Test
    public void testConstructor_withRootAndList() {
        ArrayList<DocumentInfo> docs = new ArrayList<>();
        docs.add(mockDoc1);
        docs.add(mockDoc2);
        
        stack = new DocumentStack(mockRoot, docs);
        assertTrue(stack.isInitialized());
        assertEquals(mockRoot, stack.getRoot());
        assertEquals(2, stack.size());
        assertEquals(mockDoc2, stack.peek());
    }

    @Test
    public void testGet() {
        stack = new DocumentStack(mockRoot, mockDoc1, mockDoc2);
        assertEquals(mockDoc1, stack.get(0));
        assertEquals(mockDoc2, stack.get(1));
    }

    @Test
    public void testPop() {
        stack = new DocumentStack(mockRoot, mockDoc1, mockDoc2);
        DocumentInfo popped = stack.pop();
        assertEquals(mockDoc2, popped);
        assertEquals(1, stack.size());
        assertEquals(mockDoc1, stack.peek());
    }

    @Test
    public void testGetTitle_singleDocWithRoot() {
        stack = new DocumentStack(mockRoot, mockDoc1);
        assertEquals("RootTitle", stack.getTitle());
    }

    @Test
    public void testGetTitle_multipleDocs() {
        stack = new DocumentStack(mockRoot, mockDoc1, mockDoc2);
        assertEquals("Doc2", stack.getTitle());
    }

    @Test
    public void testGetTitle_singleDocWithoutRoot() {
        stack = new DocumentStack(null, mockDoc1);
        assertNull(stack.getTitle());
    }

    @Test
    public void testGetTitle_emptyStack() {
        stack = new DocumentStack();
        assertNull(stack.getTitle());
    }

    @Test
    public void testGetTitle_topDocNull() {
        mockDoc2.displayName = null;
        stack = new DocumentStack(mockRoot, mockDoc1, mockDoc2);
        assertNull(stack.getTitle());
    }

    @Test
    public void testReset() {
        stack = new DocumentStack(mockRoot, mockDoc1, mockDoc2);
        stack.reset();
        assertNull(stack.getRoot());
        assertTrue(stack.isEmpty());
    }

    @Test
    public void testEquals_sameInstance() {
        stack = new DocumentStack(mockRoot, mockDoc1);
        assertTrue(stack.equals(stack));
    }

    @Test
    public void testEquals_equalStacks() {
        DocumentStack stack1 = new DocumentStack(mockRoot, mockDoc1);
        DocumentStack stack2 = new DocumentStack(mockRoot, mockDoc1);
        assertTrue(stack1.equals(stack2));
        assertEquals(stack1.hashCode(), stack2.hashCode());
    }

    @Test
    public void testEquals_differentRoot() {
        RootInfo otherRoot = new RootInfo();
        otherRoot.title = "OtherRoot";
        otherRoot.authority = "otherAuthority"; // 添加唯一标识字段
        otherRoot.rootId = "otherRootId";       // 添加唯一标识字段
        
        DocumentStack stack1 = new DocumentStack(mockRoot, mockDoc1);
        DocumentStack stack2 = new DocumentStack(otherRoot, mockDoc1);
        assertFalse(stack1.equals(stack2));
        assertNotEquals(stack1.hashCode(), stack2.hashCode());
    }

    @Test
    public void testEquals_differentDocs() {
        DocumentStack stack1 = new DocumentStack(mockRoot, mockDoc1);
        DocumentStack stack2 = new DocumentStack(mockRoot, mockDoc2);
        assertFalse(stack1.equals(stack2));
    }

    @Test
    public void testEquals_differentClass() {
        stack = new DocumentStack(mockRoot, mockDoc1);
        assertFalse(stack.equals(new Object()));
    }

    @Test
    public void testParcelable_emptyStack() throws Exception {
        stack = new DocumentStack();
        Parcel parcel = Parcel.obtain();
        stack.writeToParcel(parcel, 0);
        parcel.setDataPosition(0);
        
        DocumentStack recreated = DocumentStack.CREATOR.createFromParcel(parcel);
        assertNull(recreated.getRoot());
        assertTrue(recreated.isEmpty());
        parcel.recycle();
    }

    @Test
    public void testParcelable_withData() throws Exception {
        stack = new DocumentStack(mockRoot, mockDoc1, mockDoc2);
        Parcel parcel = Parcel.obtain();
        stack.writeToParcel(parcel, 0);
        parcel.setDataPosition(0);
        
        DocumentStack recreated = DocumentStack.CREATOR.createFromParcel(parcel);
        assertNotNull(recreated.getRoot());
        assertEquals(2, recreated.size());
        parcel.recycle();
    }

    @Test
    public void testDurableReadWrite() throws IOException {
        stack = new DocumentStack(mockRoot, mockDoc1, mockDoc2);
        ByteArrayOutputStream outBuffer = new ByteArrayOutputStream();
        DataOutputStream out = new DataOutputStream(outBuffer);
        stack.write(out);
        out.close();
        
        ByteArrayInputStream inBuffer = new ByteArrayInputStream(outBuffer.toByteArray());
        DataInputStream in = new DataInputStream(inBuffer);
        DocumentStack recreated = new DocumentStack();
        recreated.read(in);
        
        assertEquals(stack.size(), recreated.size());
        assertNotNull(recreated.getRoot());
    }

    @Test(expected = ProtocolException.class)
    public void testRead_unsupportedVersion() throws IOException {
        ByteArrayOutputStream outBuffer = new ByteArrayOutputStream();
        DataOutputStream out = new DataOutputStream(outBuffer);
        out.writeInt(999); // Unsupported version
        out.close();
        
        ByteArrayInputStream inBuffer = new ByteArrayInputStream(outBuffer.toByteArray());
        DataInputStream in = new DataInputStream(inBuffer);
        new DocumentStack().read(in);
    }

    @Test(expected = FileNotFoundException.class)
    public void testUpdateRoot_noMatchingRoot() throws Exception {
        stack = new DocumentStack(mockRoot, mockDoc1);
        RootInfo otherRoot = new RootInfo();
        otherRoot.title = "OtherTitle";
        otherRoot.authority = "otherAuthority"; // 添加唯一标识字段
        otherRoot.rootId = "otherRootId";       // 添加唯一标识字段
        
        Collection<RootInfo> roots = Collections.singleton(otherRoot);
        
        Method method = DocumentStack.class.getDeclaredMethod("updateRoot", Collection.class);
        method.setAccessible(true);
        try {
            method.invoke(stack, roots);
        } catch (InvocationTargetException e) {
            throw (FileNotFoundException) e.getCause();
        }
    }
}