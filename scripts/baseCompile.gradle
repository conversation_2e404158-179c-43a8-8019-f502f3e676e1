/**
 * baseCompile.gradle用于进行包含SDK在内的所有module的通用编译配置。
 * 非针对所有Module(尤其需要注意是否包含SDK)生效的配置禁止在该gradle文件中处理。
 *
 * baseCompile.gradle原则上只能通过appCompile.gradle或sdkCompile.gradle去引用。
 * 有其他配置定制需求时，在引用baseCompile.gradle前配置ext.applyBaseCompileFrom，
 * 其值为进行引用的gradle脚本相对rootProject的路径。
 */
final String applyBaseCompileFrom = ext.applyBaseCompileFrom
File applyFromGradle = null
if (applyBaseCompileFrom != null && applyBaseCompileFrom.endsWith(".gradle")) {
    applyFromGradle = rootProject.file(applyBaseCompileFrom)
}
if (applyFromGradle == null || !applyFromGradle.isFile()) {
    final errMsg = "${project.path}: Can not directly apply baseCompile.gradle. " +
            "Please apply with appCompile.gradle or sdkCompile.gradle"
    System.err.println(errMsg)
    throw new GradleException(errMsg)
}
ext.appliedBaseCompile = true

apply plugin: 'kotlin-android'
apply plugin: 'obuildplugin'

def getLocalSdkPath() {
    final hasLocalProperties = project.rootProject.file('local.properties').exists()
    if (!hasLocalProperties) {
        return null
    }
    Properties localProperties = new Properties()
    localProperties.load(project.rootProject.file('local.properties').newDataInputStream())
    return localProperties.get("sdk.dir")
}

/**
 * Some preview sdk version names on CI are different with Google official preview sdk.
 * So if the configured compile sdk version name is preview version.
 * Auto obtain the actual available preview sdk version name when locally building.
 *
 * @param propCompileSdkVersion the configured compile sdk version in gradle.properties
 * @return the compile sdk version which can be actually used.
 */
def obtainCompileSdkVersion(String propCompileSdkVersion) {
    final sdkPath = getLocalSdkPath()
    if (sdkPath == null) {
        return propCompileSdkVersion
    }
    if (propCompileSdkVersion.replace("android-", "").isInteger()) {
        return propCompileSdkVersion
    }
    final compileSdkDir = file("$sdkPath${File.separator}platforms")
    if (!compileSdkDir.exists() || !compileSdkDir.isDirectory()) {
        return propCompileSdkVersion
    }
    def useCompileSdkVersion = propCompileSdkVersion
    for (platform in compileSdkDir.list()) {
        if (platform == propCompileSdkVersion) {
            return propCompileSdkVersion
        }
        if (platform.startsWith(propCompileSdkVersion)) {
            useCompileSdkVersion = platform
        }
    }
    return useCompileSdkVersion
}

/**
 * After release new rc build tools versions.
 * The old rc build tools versions are hardly to be downloaded via SDK manager.
 * So if configured build tools version is rc version.
 * Auto obtain the actual available build tools version when locally building.
 *
 * @param propBuildToolsVersion the configured build tools version in gradle.properties
 * @return the build tools version which can be actually used.
 */
def obtainBuildToolsVersion(String propBuildToolsVersion) {
    final sdkPath = getLocalSdkPath()
    if (sdkPath == null) {
        return propBuildToolsVersion
    }
    if (!propBuildToolsVersion.contains("-rc")) {
        return propBuildToolsVersion
    }
    final buildToolDir = file("$sdkPath${File.separator}build-tools")
    if (!buildToolDir.exists() || !buildToolDir.isDirectory()) {
        return propBuildToolsVersion
    }
    final toolBaseVer = propBuildToolsVersion.split("-rc")[0]
    def useBuildToolsVersion = propBuildToolsVersion
    for (tool in buildToolDir.list()) {
        if (tool == toolBaseVer) {
            useBuildToolsVersion = tool
            break
        } else if (tool.startsWith(toolBaseVer)) {
            useBuildToolsVersion = tool
        }
    }
    return useBuildToolsVersion
}

final useCompileSdkVersion = obtainCompileSdkVersion(prop_compileSdkVersion)
final useBuildToolsVersion = obtainBuildToolsVersion(prop_buildToolsVersion)
println("${project.name}: compileSdkVersion=$useCompileSdkVersion")
println("${project.name}: buildToolsVersion=$useBuildToolsVersion")

android {
    final compileSdkArg = useCompileSdkVersion.replace("android-", "")
    if (compileSdkArg.isInteger()) {
        compileSdk compileSdkArg.toInteger()
    } else {
        compileSdkPreview compileSdkArg
    }

    buildToolsVersion = useBuildToolsVersion

    buildFeatures {
        viewBinding = false
        buildConfig = false
        aidl = false
    }

    defaultConfig {
        if (prop_minSdkVersion.isInteger()) {
            minSdk prop_minSdkVersion.toInteger()
        } else {
            minSdkPreview prop_minSdkVersion
        }

        if (prop_targetSdkVersion.isInteger()) {
            targetSdk prop_targetSdkVersion.toInteger()
        } else {
            targetSdkPreview prop_targetSdkVersion
        }

        versionCode mainVersionCode.toInteger()
        versionName mainVersionName
    }

    compileOptions {
        encoding 'UTF-8'
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }
}

/**
 * 此处仅可导入APP及其相关module和SDK都需要的最基础的依赖。
 * 原则上各个module因在各自的build.gradle中导入各自需要的依赖，非必要不得全局导入。
 * 若需导入依赖至所有仅集成至APP的module，在appCompile.gradle中处理。
 * 若需导入依赖至所有SDK，在sdkCompile.gradle中处理。
 *
 * 警告：
 * 此处配置导入依赖会影响到文管代码中所有SDK出包的依赖配置关系，会影响到文管之外的其他业务模块，严禁胡乱配置
 */
dependencies {
    implementation libs.bundles.kotlin
}

configurations.implementation {
    exclude group: 'com.oplus.sdk', module: 'oplus-addon-sdk'
}