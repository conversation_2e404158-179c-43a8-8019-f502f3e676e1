/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : IRecentCardControl
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/18 11:31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/18       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent

import android.content.Context
import android.os.Bundle
import com.oplus.pantanal.seedling.bean.SeedlingCard

interface IRecentCardControl {
    /**
     * Seedling card create.
     */
    fun cardCreate(context: Context, card: SeedlingCard)

    /**
     * Seedling card observe.
     */
    fun cardObserve(context: Context, cards: List<SeedlingCard>)

    /**
     * Seedling card destroy
     */
    fun destroy(context: Context, card: SeedlingCard)

    /**
     * Seedling card hide.
     */
    fun hide(context: Context, card: SeedlingCard)

    /**
     * Seedling card show.
     */
    fun show(context: Context, card: SeedlingCard)

    /**
     * Seedling card subscribed.
     */
    fun subscribed(context: Context, card: SeedlingCard)

    /**
     * Seedling card unSubscribed.
     */
    fun unSubscribed(context: Context, card: SeedlingCard)

    /**
     * Seeding card updateData.
     */
    fun updateData(context: Context, card: SeedlingCard, data: Bundle)
}