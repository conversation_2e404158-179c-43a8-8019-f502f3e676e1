<?xml version="1.0" encoding="utf-8"?>
<com.filemanager.common.view.SmoothRoundedCornersConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/main_category_oaps"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_main_card"
    app:default_background="true"
    android:paddingTop="@dimen/dimen_9dp">

    <ImageView
        android:id="@+id/list_item_icon"
        android:layout_width="@dimen/main_category_item_icon_size_width"
        android:layout_height="@dimen/main_category_item_icon_size_height"
        android:forceDarkAllowed="false"
        android:importantForAccessibility="no"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/list_item_title"
        style="@style/mainCategoryItemTitleStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dimen_18dp"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/font_line_space_1"
        android:minHeight="@dimen/main_category_item_title_size_height"
        android:textColor="@color/black_85_percent"
        android:textSize="@dimen/text_size_twelve_sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/list_item_icon"
        app:layout_constraintWidth_default="spread" />
</com.filemanager.common.view.SmoothRoundedCornersConstraintLayout>