package com.coloros.filemanager.common.controller.navigation

import android.view.View
import android.view.ViewGroup
import com.filemanager.common.controller.navigation.BottomMarginDecorator
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class BottomMarginDecoratorTest {

    lateinit var mBottomMarginDecorator: BottomMarginDecorator

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mBottomMarginDecorator = BottomMarginDecorator()

    }

    @Test
    fun `should verify when add view without`() {
        Assert.assertNotNull(mBottomMarginDecorator.addView(mockk(relaxed = true)))
    }

    @Test
    fun `should verify when set bottom margin without`() {
        mBottomMarginDecorator.setBottomMargin(100)


        var view = mockk<View>(relaxed = true)
        mBottomMarginDecorator.addView(view)
        mBottomMarginDecorator.setBottomMargin(100)

    }

    //
    @Test
    fun `should verify when get bottom margin without`() {
        Assert.assertEquals(0, mBottomMarginDecorator.getBottomMargin())


        var view = mockk<View>(relaxed = true) {
            every { layoutParams }.returns(null)
        }
        mBottomMarginDecorator.addView(view)
        Assert.assertEquals(0, mBottomMarginDecorator.getBottomMargin())

    }

    @Test
    fun `should verify when simple method without`() {
        var view = mockk<View>(relaxed = true) {
            every { layoutParams }.returns(mockk<ViewGroup.MarginLayoutParams>(relaxed = true))
        }

        mBottomMarginDecorator.addView(view)
        mBottomMarginDecorator.setBottomMargin(111)
        Assert.assertEquals(111, mBottomMarginDecorator.getBottomMargin())

    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}