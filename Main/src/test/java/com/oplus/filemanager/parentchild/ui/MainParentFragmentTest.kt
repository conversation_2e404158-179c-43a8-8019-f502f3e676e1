package com.oplus.filemanager.parentchild.ui

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.filelabel.ui.MainLabelViewModel
import com.oplus.filemanager.main.ui.MainActivity
import com.oplus.filemanager.main.ui.category.MainCategoryViewModel
import com.oplus.filemanager.main.ui.uistate.Storage
import com.oplus.filemanager.parentchild.adapter.SideExpandableAdapter
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.main.R
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.parentchild.viewmodel.MainParentViewModel
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import kotlinx.coroutines.flow.MutableStateFlow
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import kotlin.reflect.full.declaredMemberProperties
import kotlin.reflect.jvm.isAccessible
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import java.lang.reflect.Field
import org.robolectric.android.controller.ActivityController
import androidx.fragment.app.FragmentActivity
import kotlin.reflect.KProperty1

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class MainParentFragmentTest {

    // 测试类成员变量
    private lateinit var fragment: MainParentFragment  // 被测Fragment实例
    private lateinit var parentViewModel: MainParentViewModel  // Mock的父ViewModel
    private lateinit var labelViewModel: MainLabelViewModel  // Mock的标签ViewModel
    private val mockContext: Context = RuntimeEnvironment.application  // 模拟的Android上下文

    @Before
    fun setUp() {
        // 模拟Log类的静态方法
        mockkStatic(Log::class)
        every { Log.d(any<String>(), any<String>()) } returns Unit
        every { Log.e(any<String>(), any<String>()) } returns Unit

        // 创建Mock的ViewModel实例
        parentViewModel = mockk<MainParentViewModel>(relaxed = true)
        labelViewModel = mockk<MainLabelViewModel>(relaxed = true)

        // 初始化Fragment参数Bundle
        val bundle = Bundle().apply {
            putInt(KtConstants.P_SELECT_CATEGORY_TYPE, CategoryHelper.CATEGORY_RECENT)  // 设置初始分类类型为"最近"
            putBoolean(KtConstants.P_NEED_LOAD_DATA, true)  // 设置需要加载数据
        }

        // 创建并启动测试Activity
        val controller: ActivityController<FragmentActivity> = Robolectric.buildActivity(FragmentActivity::class.java)
        controller.setup() 
        val activity = controller.get()

        // 创建Fragment实例并添加到Activity
        fragment = MainParentFragment()
        fragment.arguments = bundle
        activity.supportFragmentManager.beginTransaction()
            .add(android.R.id.content, fragment)
            .commitNow()
        
        // 使用反射设置私有ViewModel字段
        val parentViewModelField = MainParentFragment::class.java.getDeclaredField("parentViewModel")
        parentViewModelField.isAccessible = true
        parentViewModelField.set(fragment, parentViewModel)

        val labelViewModelField = MainParentFragment::class.java.getDeclaredField("labelViewModel")
        labelViewModelField.isAccessible = true
        labelViewModelField.set(fragment, labelViewModel)
    }

    @After
    fun tearDown() {
        // 清理所有Mock对象
        unmockkAll()
    }

    @Test
    fun `test changeEditState triggers viewmodel`() {
        // 测试changeEditState方法是否调用了ViewModel的对应方法
        
        // 使用反射调用私有方法changeEditState
        fragment::class.java.getDeclaredMethod("changeEditState").apply {
            isAccessible = true
            invoke(fragment)
        }
        
        // 验证ViewModel的changeEditState方法被调用
        verify { parentViewModel.changeEditState() }
    }

    @Test
    fun `test completeEditState when in edit mode`() {
        // 测试在编辑模式下completeEditState方法的行为
        
        // 设置ViewModel处于编辑模式
        every { parentViewModel.isEdit() } returns true
        
        // 使用反射调用私有方法completeEditState
        fragment::class.java.getDeclaredMethod("completeEditState").apply {
            isAccessible = true
            invoke(fragment)
        }
        
        // 验证ViewModel的changeEditState方法被调用
        verify { parentViewModel.changeEditState() }
    }

    @Test
    fun `test onAttach sets initial category type`() {
        // 测试onAttach方法是否正确设置初始分类类型
        
        // 准备测试数据
        val bundle = Bundle().apply {
            putInt(KtConstants.P_SELECT_CATEGORY_TYPE, CategoryHelper.CATEGORY_DOC)  // 设置分类类型为"文档"
        }
        fragment.arguments = bundle

        // 调用onAttach方法
        fragment.onAttach(mockContext)

        // 使用反射获取私有字段selectedCategoryParams
        val field: Field = MainParentFragment::class.java.getDeclaredField("selectedCategoryParams")
        field.isAccessible = true
        val selectedCategoryParams = field.get(fragment) as Int
        
        // 验证分类类型是否正确设置
        assertEquals(CategoryHelper.CATEGORY_DOC, selectedCategoryParams)
    }

    @Test
    fun `test onSaveInstanceState stores current category`() {
        // 测试onSaveInstanceState方法是否正确保存当前分类类型
        
        // 创建Mock的Adapter并设置返回值
        val mockAdapter = mockk<SideExpandableAdapter>()
        every { mockAdapter.getCurrentCategoryType() } returns CategoryHelper.CATEGORY_VIDEO  // 设置当前分类为"视频"
        fragment.categoryAdapter = mockAdapter

        // 调用onSaveInstanceState方法
        val outState = Bundle()
        fragment.onSaveInstanceState(outState)

        // 验证Bundle中保存的分类类型是否正确
        assertTrue(outState.getInt("key_category_type") == CategoryHelper.CATEGORY_VIDEO)
    }
}