/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : UniqueIdUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/11 21:10
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/11       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import android.content.Context
import com.filemanager.common.utils.Log
import com.oplus.stdid.sdk.StdIDSDK
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

object UniqueIdUtils {
    private const val TAG = "UniqueIdUtils"

    private var sDUID = ""

    @JvmStatic
    fun init(context: Context) {
        Log.d(TAG, "init")
        GlobalScope.launch {
            StdIDSDK.init(context)
            val isSupport = StdIDSDK.isSupported()
            Log.d(TAG, "init -> isSupport = $isSupport")
            if (isSupport) {
                sDUID = StdIDSDK.getDUID(context)
            }
            StdIDSDK.clear(context)
        }
    }

    @JvmStatic
    fun getDeviceId(): String {
        return sDUID
    }
}