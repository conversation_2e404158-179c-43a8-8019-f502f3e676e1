package com.filemanager.common.path

import android.content.Context
import android.content.res.Resources
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.back.PredictiveBackPathChecker
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.KtUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File

/**
 * FilePathHelper的单元测试类
 * 用于测试FilePathHelper类的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class FilePathHelperTest {

    // 测试用的文件路径帮助类实例
    private lateinit var filePathHelper: FilePathHelper
    // 模拟的Context对象
    private lateinit var mockContext: Context
    // 模拟的Resources对象
    private lateinit var mockResources: Resources
    // 测试用的内部存储路径
    private val testInternalPath = "/storage/emulated/0"
    // 测试用的外部存储路径
    private val testExternalPath = "/storage/sdcard1"
    // 测试用的OTG存储路径列表
    private val testOtgPath = listOf("/otg1", "/otg2")
    // 测试用的DFM存储路径
    private val testDfmPath = "/dfm"
    // 测试用的远程Mac存储路径
    private val testRemoteMacPath = "/remote/mac"

    /**
     * 测试前的初始化方法
     * 1. 创建模拟对象
     * 2. 设置模拟行为
     * 3. 初始化测试对象
     */
    @Before
    fun setUp() {
        // 创建模拟的Context对象
        mockContext = mockk(relaxed = true)
        // 创建模拟的Resources对象
        mockResources = mockk(relaxed = true)
        // 设置模拟Context返回模拟Resources
        every { mockContext.resources } returns mockResources
        // 设置模拟字符串资源返回值
        every { mockResources.getString(R.string.string_all_files) } returns "All Files"
        every { mockResources.getString(R.string.storage_external) } returns "External Storage"
        every { mockResources.getString(R.string.storage_otg) } returns "OTG Storage"
        // 模拟MyApplication单例
        mockkObject(MyApplication)
        every { MyApplication.sAppContext } returns mockContext

        // 创建测试用的FilePathHelper实例
        filePathHelper = createFilePathHelper(testInternalPath)
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 创建FilePathHelper测试实例的辅助方法
     * @param initialPath 初始路径
     * @param isShortcut 是否来自快捷方式
     * @param shortcutPath 快捷方式路径
     * @return 配置好的FilePathHelper实例
     */
    private fun createFilePathHelper(initialPath: String, isShortcut: Boolean = false, shortcutPath: String? = null): FilePathHelper {
        return object : FilePathHelper(initialPath, isShortcut, shortcutPath) {
            // 重写获取外部存储路径方法
            override fun getExternalPath(): String? = testExternalPath
            // 重写获取内部存储路径方法
            override fun getInternalPath(): String? = testInternalPath
            // 重写获取OTG存储路径方法
            override fun getOtgPath(): List<String>? = testOtgPath
            // 重写获取DFM根路径方法
            override fun getDfmRootPath(): String? = testDfmPath
            // 重写获取远程Mac根路径方法
            override fun getRemoteMacRootPath(): String? = testRemoteMacPath
            // 重写获取DFM设备名称方法
            override fun getDfmDeviceName(): String? = "DFM Device"
            // 重写判断是否是外部存储根路径方法
            override fun isRootExternalPath(path: String): Boolean = path == testExternalPath
            // 重写判断是否是内部存储根路径方法
            override fun isRootInternalPath(path: String): Boolean = path == testInternalPath
            // 重写判断是否是OTG存储根路径方法
            override fun isRootOtgPath(path: String): Boolean = testOtgPath.contains(path)
            // 重写判断是否是DFM存储根路径方法
            override fun isRootDfmPath(path: String): Boolean = path == testDfmPath
            // 重写获取父路径方法
            override fun getParentPath(path: String): String = path.substringBeforeLast(File.separator)
        }
    }

    /**
     * 测试PathInfo的equals和hashCode方法
     * 验证路径信息对象的相等性判断和哈希值计算
     */
    @Test
    fun testPathInfoEqualsAndHashCode() {
        // 创建三个测试路径信息对象
        val path1 = FilePathHelper.PathInfo(testInternalPath)
        val path2 = FilePathHelper.PathInfo(testInternalPath)
        val path3 = FilePathHelper.PathInfo(testExternalPath)

        // 验证相同路径的对象应该相等
        assertEquals(path1, path2)
        // 验证不同路径的对象应该不相等
        assertFalse(path1 == path3)
        // 验证相同路径的对象哈希值应该相同
        assertEquals(path1.hashCode(), path2.hashCode())
    }
}