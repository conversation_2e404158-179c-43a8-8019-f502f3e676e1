<?xml version="1.0" encoding="utf-8"?>
<CheckedTextView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:checkMark="@drawable/sort_popup_icon_selector"
    android:ellipsize="marquee"
    android:gravity="center_vertical"
    android:minHeight="?android:attr/listPreferredItemHeightSmall"
    android:paddingStart="@dimen/default_margin"
    android:paddingEnd="@dimen/sort_item_padding_right"
    android:textAppearance="?android:attr/textAppearanceLarge"
    android:textSize="@dimen/sort_item_text_size"
    android:textColor="@color/black_has_night"
    android:forceDarkAllowed="true"
    />
