package com.oplus.filemanager.parentchild.viewholder

import android.content.Context
import android.view.View
import androidx.activity.ComponentActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.COUIRecyclerView
import com.oplus.filemanager.parentchild.adapter.EditItemTouchCallback
import com.oplus.filemanager.parentchild.adapter.SideClassPanelAdapter
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * SideListPanelClassViewHolder的单元测试类
 * 用于测试SideListPanelClassViewHolder的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SideListPanelClassViewHolderTest {

    // 测试用成员变量
    private lateinit var viewHolder: SideListPanelClassViewHolder
    private lateinit var mockView: View
    private lateinit var mockContext: Context
    private lateinit var mockActivity: ComponentActivity
    private lateinit var mockRecyclerView: COUIRecyclerView
    private lateinit var mockAdapter: SideClassPanelAdapter
    private lateinit var mockItemTouchHelper: ItemTouchHelper
    private lateinit var animationHelper: ItemAnimationHelper
    // 模拟数据列表
    private val mockList = mutableListOf(
        CategoryListBean(1, 1).apply { name = "Test1"; subTitle = "10" },
        CategoryListBean(2, 2).apply { name = "Test2"; subTitle = "20" }
    )

    /**
     * 测试前的初始化方法
     * 创建所有mock对象和测试环境
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        // 创建mock对象
        mockView = mockk(relaxed = true)
        mockContext = mockk(relaxed = true)
        mockActivity = mockk(relaxed = true)
        mockRecyclerView = mockk(relaxed = true)
        mockAdapter = mockk(relaxed = true)
        mockItemTouchHelper = mockk(relaxed = true)
        animationHelper = ItemAnimationHelper()

        // 设置mock行为
        every { mockView.findViewById<COUIRecyclerView>(any()) } returns mockRecyclerView
        every { mockRecyclerView.adapter = any() } just Runs
        every { mockRecyclerView.layoutManager = any() } just Runs
        every { mockRecyclerView.isLongClickable = any() } just Runs
        every { mockRecyclerView.isNestedScrollingEnabled = any() } just Runs

        // 创建测试对象
        viewHolder = spyk(SideListPanelClassViewHolder(
            itemView = mockView,
            context = mockContext,
            activity = mockActivity,
            onItemViewCallback = { _, _ -> }
        ))
        // 使用反射设置私有属性editTouchHelper
        viewHolder.javaClass.getDeclaredField("editTouchHelper").apply {
            isAccessible = true
            set(viewHolder, mockItemTouchHelper)
        }
    }

    /**
     * 测试后的清理方法
     * 释放所有mock资源
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试setData方法是否正确地委托给adapter
     */
    @Test
    fun `setData should delegate to adapter`() {
        // 设置mock adapter
        viewHolder.listAdapter = mockAdapter
        every { mockAdapter.setData(mockList) } just Runs

        // 执行测试方法
        viewHolder.setData(ArrayList(mockList))

        // 验证adapter的setData方法被调用
        verify { mockAdapter.setData(mockList) }
    }

    /**
     * 测试当adapter为null时，setData方法不会抛出异常
     */
    @Test
    fun `setData with null adapter should not throw exception`() {
        viewHolder.listAdapter = null
        viewHolder.setData(ArrayList(mockList))
        // 验证没有抛出异常
    }

    /**
     * 测试updateCount方法是否正确地委托给adapter
     */
    @Test
    fun `updateCount should delegate to adapter`() {
        // 设置mock adapter
        viewHolder.listAdapter = mockAdapter
        every { mockAdapter.updateClassPanel(1, 100L) } just Runs

        // 执行测试方法
        viewHolder.updateCount(1, 100L)

        // 验证adapter的updateClassPanel方法被调用
        verify { mockAdapter.updateClassPanel(1, 100L) }
    }

    /**
     * 测试当adapter为null时，updateCount方法不会抛出异常
     */
    @Test
    fun `updateCount with null adapter should not throw exception`() {
        viewHolder.listAdapter = null
        viewHolder.updateCount(1, 100L)
        // 验证没有抛出异常
    }

    /**
     * 测试当adapter为null时，setItemViewAlpha方法不会抛出异常
     */
    @Test
    fun `setItemViewAlpha with null adapter should not throw exception`() {
        viewHolder.listAdapter = null
        viewHolder.setItemViewAlpha(0.5f, true)
        // 验证没有抛出异常
    }
}