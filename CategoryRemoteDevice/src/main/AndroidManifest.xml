<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <application>
        <meta-data
            android:name="filemanager.remote_device.version"
            android:value="1" />
        <activity
            android:name=".download.DownloadActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan" />

        <service android:name=".download.service.DownloadRemoteFileService"
            android:exported="false"
            android:foregroundServiceType="dataSync"
            />
    </application>

</manifest>