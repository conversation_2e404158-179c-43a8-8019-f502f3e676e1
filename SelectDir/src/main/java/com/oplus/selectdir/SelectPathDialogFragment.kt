/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.select
 * * Version     : 1.0
 * * Date        : 2020/7/6
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir

import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.os.Environment
import android.text.TextUtils
import android.view.View
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.edge.EdgeToEdgeDialogFragment
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.stringResource
import com.oplus.filemanager.dfm.DFMManager
import com.oplus.selectdir.SelectDirPathPanelFragment.Companion.KEY_ACTION_CODE
import com.oplus.selectdir.SelectDirPathPanelFragment.Companion.SELECT_DIALOG_FRAGMENT_TAG
import com.oplus.selectdir.SelectDirPathRenamePanelFragment.Companion.PARAMETER_BTN_CONFIRM
import com.oplus.selectdir.SelectDirPathRenamePanelFragment.Companion.PARAMETER_IS_OVERRIDE
import com.oplus.selectdir.SelectDirPathRenamePanelFragment.Companion.PARAMETER_PANEL_TITLE
import com.oplus.selectdir.SelectPathController.Companion.SELECT_FILE_TYPE_FILL_NAME_LIST
import com.oplus.selectdir.morestorage.SelectMoreStoragePanelFragment
import com.oplus.selectdir.morestorage.SelectMoreStoragePanelFragment.Companion.MORE_DIALOG_FRAGMENT_TAG
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class SelectPathDialogFragment : EdgeToEdgeDialogFragment(), SelectPathDialogInterface {
    companion object {
        private const val TAG = "SelectPathDialogFragment"
        private const val CANCEL_ACTIVITY_DELAY = 300L
        private const val SELECT_PATH_UPDATE_DELAY = 100L
        const val SELECT_DIALOG_FRAGMENT_CLOSE_TAG = "SELECT_DIALOG_FRAGMENT_CLOSE_TAG"
        private val DOWNLOAD_PATH by lazy { Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DOWNLOADS).absolutePath }

        const val SHARE_ACTIVITY = "com.oplus.filemanager.filechoose.ui.share.ShareActivity"
    }

    private var mActivity: BaseVMActivity? = null
    private var mSelectPathPanelFragment: SelectDirPathPanelFragment? = null
    private var mSelectMoreStorageFragment: SelectMoreStoragePanelFragment? = null
    private var mActionCode = 0
    private var mContext: Context? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mContext = context
        mActivity = activity as? BaseVMActivity
    }

    fun showPanelFragment(
        manager: FragmentManager,
        tag: String,
        actionCode: Int,
        path: String? = null,
        bundle: Bundle? = null,
        hasStoragePermission: Boolean = true
    ) {
        val pathList = VolumeEnvironment.getVolumePathList(false)
        if (actionCode == MessageConstant.MSG_EDITOR_COPY || actionCode == MessageConstant.MSG_EDITOR_CUT
            || actionCode == MessageConstant.MSG_EDITOR_DECOMPRESS) {
            val dfsMountPath = DFMManager.getDFSMountPath()
            dfsMountPath?.let {
                pathList.add(it)
            }
        }
        Log.d(TAG, "showPanelFragment pathList ${pathList?.size} actionCode $actionCode")
        mActionCode = actionCode
        pathList?.apply {
            if ((size > 1) && isSupportMoreStorages(actionCode)) {
                if (mSelectMoreStorageFragment == null) {
                    mSelectMoreStorageFragment = SelectMoreStoragePanelFragment()
                }
                mSelectMoreStorageFragment?.let {
                    it.arguments = Bundle().apply {
                        setMoreStorageFragmentArguments(pathList, bundle)
                    }
                    it.setSelectPathDialogInterface(this@SelectPathDialogFragment)
                    setMainPanelFragment(it)
                    show(manager, tag)
                }
            } else {
                if (size > 0) {
                    val currentPath =
                        if (TextUtils.isEmpty(path)) {
                            if (mActionCode == MessageConstant.MSG_SAVE) {
                                DOWNLOAD_PATH
                            } else {
                                pathList[0]
                            }
                        } else if (path?.startsWith(KtConstants.DFM_MOUNT_PATH_SUFFIX) == true
                            && mActionCode == MessageConstant.MSG_SAVE_AND_RENAME
                        ) {
                            DOWNLOAD_PATH
                        } else {
                            path
                        }
                    showPanelFragment(manager, tag, currentPath, bundle, hasStoragePermission)
                }
            }
        }
    }

    /**
     * 判断是否支持其他设备存储
     */
    private fun isSupportMoreStorages(actionCode: Int): Boolean {
        Log.d(TAG, "isSupportMoreStorages action:$actionCode")
        return actionCode != MessageConstant.MSG_ADD_SHORTCUT_FOLDER
    }

    private fun Bundle.setMoreStorageFragmentArguments(pathList: ArrayList<String>?, bundle: Bundle?) {
        putStringArrayList(KtConstants.P_PATH_LIST, pathList)
        putBoolean(KtConstants.HAS_STORAGE_PERMISSION, true)
        bundle?.let { bun ->
            putString(KtConstants.FILE_NAME, bun.getString(KtConstants.FILE_NAME))
            putStringArrayList(
                    KtConstants.FILE_EXT,
                    bun.getStringArrayList(KtConstants.FILE_EXT)
            )
            putBoolean(PARAMETER_IS_OVERRIDE, bun.getBoolean(PARAMETER_IS_OVERRIDE))
            putString(PARAMETER_PANEL_TITLE, bun.getString(PARAMETER_PANEL_TITLE))
            putString(PARAMETER_BTN_CONFIRM, bun.getString(PARAMETER_BTN_CONFIRM))
        }
    }

    override fun showPanelFragment(
        manager: FragmentManager,
        tag: String,
        path: String?,
        bundle: Bundle?,
        hasStoragePermission: Boolean
    ) {
        Log.d(TAG, "showPanelFragment $path, code = $mActionCode tag:$tag path:$path")
        if (mSelectPathPanelFragment == null) {
            mSelectPathPanelFragment = if (mActionCode == MessageConstant.MSG_SAVE_AND_RENAME) {
                SelectDirPathRenamePanelFragment()
            } else {
                SelectDirPathPanelFragment()
            }
        }
        mSelectPathPanelFragment?.setPanelDragListener {
            performCancel(SELECT_DIALOG_FRAGMENT_CLOSE_TAG)
            false
        }
        if (mSelectPathPanelFragment?.isNotEmpty(mSelectPathPanelFragment?.getSelectDirCurrentPath()) == true && path != null) {
            mSelectPathPanelFragment?.setSelectDirCurrentPath(path)
        }
        val arguments = obtainPanelFragmentArguments(path, hasStoragePermission, bundle)
        mSelectPathPanelFragment?.let {
            it.setSelectPathDialogInterface(this)
            it.arguments = arguments
        }
        kotlin.runCatching {
            if (!isAdded) {
                setMainPanelFragment(mSelectPathPanelFragment)
                show(manager, tag)
            } else {
                replacePanelFragment(mSelectPathPanelFragment)
            }
        }.onFailure {
            Log.e(TAG, "showPanelFragment error", it)
        }
        notifyUIConfigChanged()
    }

    private fun obtainPanelFragmentArguments(
        path: String?,
        hasStoragePermission: Boolean,
        bundle: Bundle?
    ): Bundle {
        val arguments = Bundle()
        arguments.putString(KtConstants.P_CURRENT_PATH, path)
        when (mActionCode) {
            MessageConstant.MSG_SAVE -> {
                arguments.putString(
                    KtConstants.P_TITLE,
                    MyApplication.appContext.getString(com.filemanager.common.R.string.doc_viewer_save_file_dialog_title)
                )
                arguments.putInt(
                    KtConstants.P_TITLE_ID,
                    com.filemanager.common.R.string.doc_viewer_save_file_dialog_title
                )
            }
            MessageConstant.MSG_CLOUD_DRIVE_DOWNLOAD -> {
                arguments.putString(
                    KtConstants.P_TITLE,
                    stringResource(com.filemanager.common.R.string.choose_path)
                )
                arguments.putInt(
                    KtConstants.P_TITLE_ID,
                    com.filemanager.common.R.string.choose_path
                )
            }
            MessageConstant.MSG_ADD_SHORTCUT_FOLDER -> {
                arguments.putString(KtConstants.P_TITLE, stringResource(com.filemanager.common.R.string.select_target_path))
                arguments.putInt(KtConstants.P_TITLE_ID, com.filemanager.common.R.string.select_target_path)
            }
            else -> {
                arguments.putString(
                    KtConstants.P_TITLE,
                    MyApplication.appContext.getString(com.filemanager.common.R.string.select_target_folder)
                )
                arguments.putInt(
                    KtConstants.P_TITLE_ID,
                    com.filemanager.common.R.string.select_target_folder
                )
            }
        }
        arguments.putInt(KEY_ACTION_CODE, mActionCode)
        arguments.putBoolean(KtConstants.HAS_STORAGE_PERMISSION, hasStoragePermission)
        bundle?.let {
            arguments.putString(KtConstants.FILE_NAME, it.getString(KtConstants.FILE_NAME))
            arguments.putStringArrayList(
                KtConstants.FILE_EXT,
                it.getStringArrayList(KtConstants.FILE_EXT)
            )
            arguments.putStringArrayList(
                SELECT_FILE_TYPE_FILL_NAME_LIST,
                it.getStringArrayList(SELECT_FILE_TYPE_FILL_NAME_LIST)
            )
            arguments.putBoolean(PARAMETER_IS_OVERRIDE, it.getBoolean(PARAMETER_IS_OVERRIDE))
            arguments.putString(PARAMETER_PANEL_TITLE, it.getString(PARAMETER_PANEL_TITLE))
            arguments.putString(PARAMETER_BTN_CONFIRM, it.getString(PARAMETER_BTN_CONFIRM))
        }
        return arguments
    }

    private fun notifyUIConfigChanged() {
        lifecycleScope.launch(Dispatchers.Main) {
            if (mSelectPathPanelFragment?.isAdded == false) {
                delay(SELECT_PATH_UPDATE_DELAY) // show之后立即调用onUIConfigChanged，fragment还没有add，延迟处理
            }
            onUIConfigChanged()
        }
    }

    fun onUIConfigChanged() {
        mSelectPathPanelFragment?.onUIConfigChanged(bottomSheetDialog)
    }

    fun updatePanelFragmentHeight() {
        if (dialog?.isShowing == true) {
            mSelectPathPanelFragment?.updateHeightAndRequestLayout()
            view?.apply {
                val tintColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackgroundElevated)
                intArrayOf(com.support.panel.R.id.first_panel_container).forEach {
                    context.getDrawable(com.support.panel.R.drawable.coui_panel_bg_without_shadow)?.let { drawable ->
                        drawable.setTint(tintColor)
                        findViewById<View>(it)?.background = drawable
                    }
                }
            }
            val context = context ?: MyApplication.sAppContext
            (dialog as? COUIBottomSheetDialog)?.updateLayoutWhileConfigChange(context.resources.configuration)
        }
    }

    override fun performDismiss() {
        dialog?.dismiss()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putInt(KEY_ACTION_CODE, mActionCode)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        savedInstanceState?.let {
            mActionCode = savedInstanceState.getInt(KEY_ACTION_CODE)
        }
    }

    override fun onCancel(dialog: DialogInterface) {
        Log.d(TAG, "onCancel")
        super.onCancel(dialog)
        activity?.let { activity ->
            if (activity.componentName.className == SHARE_ACTIVITY) {
                //点击取消的时候，不能直接finish方法，会导致Dialog的动画没有执行完
                activity.lifecycleScope.launch(Dispatchers.Default) {
                    delay(CANCEL_ACTIVITY_DELAY)
                    activity.finish()
                }
            }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        Log.d(TAG, "onDismiss")
        super.onDismiss(dialog)
    }

    override fun performCancel(fragmentTag: String) {
        Log.d(TAG, "performCancel fragmentTag $fragmentTag ${mSelectMoreStorageFragment != null}")
        when (fragmentTag) {
            SELECT_DIALOG_FRAGMENT_TAG -> {
                if (showMoreStorageFragment()) {
                    backToFirstPanel()
                } else {
                    dialog?.cancel()
                }
            }

            MORE_DIALOG_FRAGMENT_TAG -> dialog?.cancel()
            else -> dialog?.cancel()
        }
    }

    override fun showMoreStorageFragment(): Boolean {
        if (!isSupportMoreStorages(mActionCode)) {
            return false
        }
        val pathList = VolumeEnvironment.getVolumePathList(false)
        if (mActionCode == MessageConstant.MSG_EDITOR_COPY || mActionCode == MessageConstant.MSG_EDITOR_CUT
            || mActionCode == MessageConstant.MSG_EDITOR_DECOMPRESS
        ) {
            val dfsMountPath = DFMManager.getDFSMountPath()
            dfsMountPath?.let {
                pathList.add(it)
            }
        }
        if (pathList.size > 1 && mSelectMoreStorageFragment == null) {
            val bundle = Bundle()
            mSelectMoreStorageFragment = SelectMoreStoragePanelFragment()
            mSelectMoreStorageFragment?.let {
                it.arguments = Bundle().apply {
                    setMoreStorageFragmentArguments(pathList, bundle)
                }
                it.setSelectPathDialogInterface(this@SelectPathDialogFragment)
                addFragmentToBackStack()
            }
        }
        return mSelectMoreStorageFragment != null
    }

    private fun addFragmentToBackStack(): Int? {
        val beginTransaction = mActivity?.supportFragmentManager?.beginTransaction()
        beginTransaction?.addToBackStack(null)
        return beginTransaction?.commit()
    }
}

interface SelectPathDialogInterface {
    fun performDismiss()

    fun showPanelFragment(
        manager: FragmentManager,
        tag: String,
        path: String?,
        bundle: Bundle?,
        hasStoragePermission: Boolean
    )

    fun performCancel(fragmentTag: String)

    fun showMoreStorageFragment(): Boolean
}