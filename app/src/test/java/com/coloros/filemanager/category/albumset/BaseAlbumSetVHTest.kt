/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  com.coloros.filemanager.BaseAlbumSetVHTest
 * * Description : BaseAlbumSetVH的单元测试
 * * Version     : 1.0
 * * Date        : 2022/4/18
 * * Author      : Yan<PERSON><EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.category.albumset

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.AlbumItem
import com.oplus.filemanager.category.albumset.adapter.viewholder.BaseAlbumSetVH
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkObject
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class BaseAlbumSetVHTest {

    @MockK
    lateinit var mContext: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        mContext = mockk(relaxed = true) {
            every { resources }.returns(mockk {
                every { getString(any()) } returns "card case"
            })
        }
        MyApplication.init(mContext)
    }

    @Test
    fun `should return card case type when covert to file bean if album item key is card case`() {
        val albumSetVH = mockk<BaseAlbumSetVH>()
        mockkObject(MimeTypeHelper.Companion)
        every { MimeTypeHelper.getTypeFromPath(any()) } returns MimeTypeHelper.IMAGE_TYPE
        every { albumSetVH.covertAlbumItemToFileBean(any(), mContext) } answers { callOriginal() }
        every { mContext.getString(any()) } returns "DCIM/MyAlbums/随身卡包（Card case）/"
        val albumItem = AlbumItem("covertPath", 3, "bucket",
            "DCIM/MyAlbums/随身卡包（Card case）/", "bucketId", 1, 100L)
        val fileBean = albumSetVH.covertAlbumItemToFileBean(albumItem, mContext)
        print("album item key : ${albumItem.key}")
        Assert.assertEquals(MimeTypeHelper.ALBUM_SET_TYPE_CARD_CASE, fileBean.mLocalType)
        Assert.assertEquals("card case", fileBean.mDisplayName)
    }

    @Test
    fun `should return image type when covert to file bean if album item key is not card case`() {
        val albumSetVH = mockk<BaseAlbumSetVH>()
        mockkObject(MimeTypeHelper.Companion)
        every { MimeTypeHelper.getTypeFromPath(any()) } returns MimeTypeHelper.IMAGE_TYPE
        every { albumSetVH.covertAlbumItemToFileBean(any(), mContext) } answers { callOriginal() }

        val albumItem = AlbumItem("covertPath", 3, "bucket",
            "key", "bucketId", 1, 100L)
        val fileBean = albumSetVH.covertAlbumItemToFileBean(albumItem, mContext)
        Assert.assertEquals(MimeTypeHelper.IMAGE_TYPE, fileBean.mLocalType)
        Assert.assertEquals("bucket", fileBean.mDisplayName)
    }
}