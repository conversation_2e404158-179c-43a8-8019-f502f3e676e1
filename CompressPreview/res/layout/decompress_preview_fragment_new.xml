<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundWithCard"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:splitMotionEvents="false">

    <com.filemanager.common.view.FileManagerRecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ScrollView
        android:id="@+id/scrollview_over_error"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="100dp"
        android:fillViewport="true"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dimen_24dp"
            android:gravity="center"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/zip_type_iv"
                    android:layout_width="@dimen/dimen_72dp"
                    android:layout_height="@dimen/dimen_72dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/ic_file_compress_zip" />

                <TextView
                    android:id="@+id/zip_name_tv"
                    style="@style/couiTextHeadlineXS"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/zip_type_iv"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="8dp"
                    android:textColor="@color/coui_color_label_primary" />

                <TextView
                    android:id="@+id/over_msg_tv"
                    style="@style/couiTextBodyM"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/zip_name_tv"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="16dp"
                    android:textColor="@color/coui_color_label_secondary" />
            </RelativeLayout>

        </LinearLayout>
    </ScrollView>

    <FrameLayout android:layout_height="@dimen/operation_btn_background_height"
        android:layout_width="match_parent"
        android:layout_gravity="bottom"
        android:id="@+id/button_parent"
        android:background="?attr/couiColorBackgroundWithCard">
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_background_height"
            android:layout_gravity="top"
            android:alpha="0"
            android:background="?attr/couiColorDivider"
            android:id="@+id/button_divider"
            android:forceDarkAllowed="false" />
        <com.coui.appcompat.button.COUIButton
            android:id="@+id/decompress_button"
            style="?attr/couiButtonColorfulDefaultStyle"
            android:layout_width="@dimen/operation_btn_width"
            android:layout_height="@dimen/operation_btn_height"
            android:layout_gravity="bottom|center_horizontal"
            android:layout_marginBottom="@dimen/dimen_40dp"
            android:paddingVertical="@dimen/dimen_2dp"
            android:contentDescription="@string/menu_file_list_decompress"
            android:text="@string/menu_file_list_decompress" />
    </FrameLayout>

    <include layout="@layout/appbar_with_pathbar_layout_secondary" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>