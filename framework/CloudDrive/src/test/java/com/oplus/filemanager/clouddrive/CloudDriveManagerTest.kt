/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : CloudDriveManagerTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/9 10:34
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/9       1.0      create
 **********************************************************************/
package com.oplus.filemanager.clouddrive

import android.content.Context
import android.net.Uri
import com.heytap.sdk.clouddisk.manager.CloudDiskShareManager
import com.heytap.sdk.clouddisk.util.CloudDiskUtils
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class CloudDriveManagerTest {

    private lateinit var context: Context

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
    }

    @After
    fun teardown() {
        unmockkAll()
    }

    @Test
    fun testCloudPackageNameWhenFailure() {
        mockkStatic(CloudDiskUtils::class)
        every { CloudDiskUtils.getCloudPackageName(context) }.throws(Exception())
        val packageName = CloudDriveManager.getCloudPackageName(context)
        Assert.assertEquals("", packageName)
        unmockkStatic(CloudDiskUtils::class)
    }

    @Test
    fun testCloudPackageName() {
        mockkStatic(CloudDiskUtils::class)
        every { CloudDiskUtils.getCloudPackageName(context) }.returns(CLOUD_PACKAGE_NAME)
        val packageName = CloudDriveManager.getCloudPackageName(context)
        Assert.assertEquals(CLOUD_PACKAGE_NAME, packageName)
        unmockkStatic(CloudDiskUtils::class)
    }

    @Test
    fun testSupportCloudDisk() {
        mockkStatic(CloudDiskShareManager::class)
        every { CloudDiskShareManager.isSupportCloudDisk(context) }.returns(true)
        val support = CloudDriveManager.isSupportCloudDisk(context)
        Assert.assertTrue(support)
        unmockkStatic(CloudDiskShareManager::class)
    }

    @Test
    fun testSupportCloudDiskWhenFailure() {
        mockkStatic(CloudDiskShareManager::class)
        every { CloudDiskShareManager.isSupportCloudDisk(context) }.throws(Exception())
        val support = CloudDriveManager.isSupportCloudDisk(context)
        Assert.assertFalse(support)
        unmockkStatic(CloudDiskShareManager::class)
    }

    @Test
    fun testUploadFiles() {
        val paths = arrayListOf<Uri>()
        mockkStatic(CloudDiskShareManager::class)
        justRun { CloudDiskShareManager.uploadFiles(context, paths) }
        CloudDriveManager.uploadFiles(context, paths)
        verify { CloudDiskShareManager.uploadFiles(context, paths) }
        unmockkStatic(CloudDiskShareManager::class)
    }

    @Test
    fun testStartCloudDiskActivity() {
        mockkStatic(CloudDiskShareManager::class)
        justRun { CloudDiskShareManager.startCloudDiskActivity(context) }
        CloudDriveManager.startCloudDiskActivity(context)
        verify { CloudDiskShareManager.startCloudDiskActivity(context) }
        unmockkStatic(CloudDiskShareManager::class)
    }

    companion object {
        private const val CLOUD_PACKAGE_NAME = "com.heytap.cloud"
    }
}