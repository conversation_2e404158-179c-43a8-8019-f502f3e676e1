package com.coloros.filemanager.utils

import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.AlbumItem
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AlbumItemTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun should_verify_when_simple_method_without() {
        mockkObject(MimeTypeHelper.Companion)
        every { MimeTypeHelper.getTypeFromPath("test") }.returns(0)

        val item = AlbumItem("test", 0, "test", "test", "test", 0, 0)
        Assert.assertEquals(0, item.dateModified)
        item.count = 100
        Assert.assertEquals(100, item.count.toLong())
        item.orientation = 100
        Assert.assertEquals(100, item.orientation.toLong())
        item.setCoverPath("test2")
        Assert.assertEquals("test2", item.coverPath)
        item.name = "test2"
        Assert.assertEquals("test2", item.name)
        item.key = "test2"
        Assert.assertEquals("test2", item.key)
        Assert.assertEquals("test", item.bucketId)
        Assert.assertEquals(0, item.type)
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}