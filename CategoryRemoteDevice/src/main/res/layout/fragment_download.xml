<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundWithCard"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:splitMotionEvents="false"
    tools:context=".download.DownloadFragment"
    >

<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    >

    <ViewStub
        android:id="@+id/single_file_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/layout_download_single_file"
        app:layout_constraintBottom_toTopOf="@id/bottom_area_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ViewStub
        android:id="@+id/multi_file_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout="@layout/layout_download_multi_file"
        app:layout_constraintBottom_toTopOf="@id/bottom_area_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="0.33dp"
        android:background="?attr/couiColorDivider"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/bottom_area_layout" />

    <LinearLayout
        android:id="@+id/bottom_area_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dimen_24dp"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingTop="@dimen/dimen_16dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tv_download_tips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/download_tips_margin"
            android:layout_marginBottom="@dimen/dimen_16dp"
            android:gravity="center"
            android:text="@string/open_remote_file_need_download_tips"
            android:textColor="?attr/couiColorLabelSecondary"
            android:textSize="@dimen/font_size_14" />

        <com.coui.appcompat.progressbar.COUIInstallLoadProgress
            android:id="@+id/progress_download"
            style="@style/Widget.COUI.COUILoadProgress.InstallDownloadLarge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|bottom"
            app:couiInstallTextsize="@dimen/coui_btn_text_size"
            app:couiInstallTextview="@string/start_download"
            app:couiInstallViewWidth="@dimen/download_file_button_width" />

        <TextView
            android:id="@+id/btn_cancel_download"
            android:layout_width="@dimen/download_file_button_width"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/coui_double_btn_text_margin_top"
            android:clickable="true"
            android:gravity="center"
            android:text="@string/cancel_download"
            android:textAppearance="@style/couiTextButtonL"
            android:textColor="@color/coui_text_btn_text_color"
            android:visibility="gone" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/toolbar_layout"
        layout="@layout/appbar_with_divider_layout_secondary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>