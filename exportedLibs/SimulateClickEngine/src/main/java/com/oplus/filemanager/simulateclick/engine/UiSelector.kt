/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - UiSelector.kt
 * Description:
 *     The utils to search page UI node.
 *
 * Version: 1.0
 * Date: 2024-05-23
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-05-23   2.0    Porting from LLMSimulateClick
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine

import android.accessibilityservice.AccessibilityService
import android.view.accessibility.AccessibilityNodeInfo
import androidx.annotation.VisibleForTesting
import com.oplus.filemanager.simulateclick.engine.utils.FindNodeUtil
import com.oplus.filemanager.simulateclick.engine.utils.InputUtil
import com.oplus.filemanager.simulateclick.engine.utils.Log
import com.oplus.filemanager.simulateclick.service.AccessibilityServiceBridge
import kotlin.math.abs

internal class UiSelector(private val serviceBridge: AccessibilityServiceBridge) {

    private var webContainerTarget: AccessibilityNodeInfo? = null

    private fun findByIdOrText(
        accessibilityNodeInfo: AccessibilityNodeInfo,
        action: Action
    ): List<AccessibilityNodeInfo>? {
        val hasWidgetId = (!action.widgetID.isNullOrEmpty() || !action.widgetIDList.isNullOrEmpty())
        val hasWidgetText = !action.widgetText.isNullOrEmpty()
        if (hasWidgetId && hasWidgetText) {
            return if (action.actionType == ACTION_TYPE_INPUT) {
                findById(accessibilityNodeInfo, action)
            } else {
                findByIdAndText(accessibilityNodeInfo, action)
            }
        }
        if (hasWidgetId) {
            return findById(accessibilityNodeInfo, action)
        }
        return if (hasWidgetText) {
            findByText(accessibilityNodeInfo, action)
        } else null
    }

    private fun findByIdOrText(
        node: AccessibilityNodeInfo,
        widgetType: String,
        widgetId: String,
        widgetText: String,
        txtDirectly: Boolean
    ): List<AccessibilityNodeInfo>? {
        if (widgetId.isNotEmpty() && widgetText.isNotEmpty()) {
            return findByIdAndText(node, widgetType, widgetId, widgetText)
        }
        if (widgetId.isNotEmpty()) {
            return findById(node, widgetType, widgetId)
        }
        return if (widgetText.isNotEmpty()) {
            findByText(node, widgetType, widgetText, txtDirectly)
        } else null
    }

    private fun findByContentBack(
        accessibilityNodeInfo: AccessibilityNodeInfo,
        str: String?
    ): Boolean {
        var nodeList = accessibilityNodeInfo.findAccessibilityNodeInfosByText(str)
        if (nodeList.isNullOrEmpty()) {
            nodeList = FindNodeUtil.findNodesById(accessibilityNodeInfo, str.toString())
        }
        Log.d(TAG, "findByContentBack: nodeList=$nodeList")
        if (nodeList.isEmpty()) {
            return false
        }
        val performGlobalAction = serviceBridge.pickService()
            ?.performGlobalAction(AccessibilityService.GLOBAL_ACTION_BACK) ?: false
        Log.d(TAG, "findByContentBack: performGlobalAction=$performGlobalAction")
        return performGlobalAction
    }

    fun find(action: Action?): List<AccessibilityNodeInfo>? {
        val service = serviceBridge.pickService()
        if ((action == null) || (service == null)) {
            Log.e(TAG, "find: ERROR! accessibility service or action is null")
            return null
        }
        //先从活动窗口中找
        val rootInActiveWindow = service.rootInActiveWindow ?: run {
            Log.e(TAG, "find: ERROR! rootInActiveWindow is null")
            return null
        }
        var list = findInWindow(rootInActiveWindow, action)
        if (!list.isNullOrEmpty()) return list
        //找不到再从非活动窗口找目标控件
        val windowsList = service.windows ?: run {
            Log.e(TAG, "find: ERROR! windows is null")
            return null
        }
        for (window in windowsList) {
            val windowRoot = window.root ?: continue
            list = findInWindow(windowRoot, action)
            if (!list.isNullOrEmpty()) return list
        }
        Log.d(TAG, "find: not find any node in window")
        return null
    }

    private fun findInWindow(
        rootInWindow: AccessibilityNodeInfo,
        action: Action
    ): List<AccessibilityNodeInfo>? {
        action.takeIf {
            !it.contentBack.isNullOrEmpty() && findByContentBack(rootInWindow, it.contentBack)
        }?.let {
            it.contentBack = ""
        }
        //先查找父id
        if (action.matchRules == MATCH_RULE_SELECT_IN_PARENT) {
            return findNeedParent(rootInWindow, action)
        }
        var list = findByIdOrText(rootInWindow, action)
        //不用查找子节点
        if (action.widgetIndex.isNullOrEmpty()) {
            return list
        }
        if (list.isNullOrEmpty() && !action.isInWeb) {
            return list
        }
        //针对h5,必须先找到WebView,需要在根节点
        if (action.isInWeb) {
            list = findByTypeInNode(rootInWindow, action)
        }
        if (list.isNullOrEmpty()) {
            return list
        }
        val results = mutableListOf<AccessibilityNodeInfo>()
        list.forEach {
            val child = findChild(it, action)
            if (child != null) {
                results.add(child)
            }
        }
        return results
    }

    fun findScrollableParentNode(action: Action): AccessibilityNodeInfo? {
        val service = serviceBridge.pickService()
        if (service == null) {
            Log.e(TAG, "findScrollableParentNode: ERROR! service is null")
            return null
        }
        if (action.scrollableParentWidgetType == null) {
            return null
        }
        if (action.scrollableParentWidgetId == null && action.scrollableParentWidgetText == null) {
            return null
        }
        val widgetType = action.scrollableParentWidgetType ?: ""
        val widgetId = action.scrollableParentWidgetId ?: ""
        val widgetText = action.scrollableParentWidgetText ?: ""
        //先从活动窗口中找
        val rootInActiveWindow = service.rootInActiveWindow ?: run {
            Log.e(TAG, "findScrollableParentNode: ERROR! rootInActiveWindow is null")
            return null
        }
        var list = findByIdOrText(
            rootInActiveWindow,
            widgetType,
            widgetId,
            widgetText,
            action.findWidgetByTxtDirectly
        )
        if (!list.isNullOrEmpty()) return list[0]
        //找不到再从非活动窗口找目标控件
        val windowsList = service.windows ?: run {
            Log.e(TAG, "findScrollableParentNode: ERROR! windows is null")
            return null
        }
        for (window in windowsList) {
            val windowRoot = window.root ?: continue
            list = findByIdOrText(
                windowRoot,
                widgetType,
                widgetId,
                widgetText,
                action.findWidgetByTxtDirectly
            )
            if (!list.isNullOrEmpty()) return list[0]
        }
        return null
    }

    /**
     * 在node中递归查找type
     */
    private fun findByTypeInNode(
        accessibilityNodeInfo: AccessibilityNodeInfo,
        action: Action
    ): List<AccessibilityNodeInfo>? {
        if (!action.isInWeb) {
            return null
        }
        findByTypeInNode(accessibilityNodeInfo, action.widgetType)
        val list = mutableListOf<AccessibilityNodeInfo>()
        webContainerTarget?.let {
            list.add(it)
            webContainerTarget = null
            return list
        }
        return null
    }

    /**
     * 在node中递归查找type
     */
    private fun findByTypeInNode(node: AccessibilityNodeInfo?, webClass: String?) {
        if (node == null || webClass.isNullOrEmpty()) {
            return
        }
        if (node.childCount <= 0) {
            return
        }
        for (i in 0 until node.childCount) {
            val child = node.getChild(i) ?: continue
            // 有时 child 为空
            val className = child.className.toString()
            if (webClass == className) {
                Log.d(TAG, "findByTypeInNode: findWeb")
                webContainerTarget = child
                if (child.childCount == 1 && (webClass == child.getChild(0).className.toString())) {
                    webContainerTarget = child.getChild(0)
                }
                break
            }
            // 递归调用
            findByTypeInNode(child, webClass)
        }
    }

    /**
     * 先找到父控件再查子控件
     */
    private fun findNeedParent(
        accessibilityNodeInfo: AccessibilityNodeInfo,
        action: Action
    ): List<AccessibilityNodeInfo>? {
        var i2 = 0
        val parentList = mutableListOf<AccessibilityNodeInfo>()
        val nodeInfoByViewId =
            FindNodeUtil.findNodesById(accessibilityNodeInfo, action.parentWidgetId.toString())
        if (nodeInfoByViewId.isNotEmpty()) {
            for (next in nodeInfoByViewId) {
                if (action.parentWidgetType == next.className) {
                    parentList.add(next)
                }
            }
        }
        if (!action.selectIndex.isNullOrEmpty()) {
            i2 = action.selectIndex?.toInt() ?: 0
        }
        if (parentList.size <= 0 || parentList.size <= i2) {
            return null
        }
        val parentNode = parentList[i2]
        var list = findByIdOrText(parentNode, action)
        //增加类型查找
        if (list.isNullOrEmpty() && action.isInWeb) {
            list = findByTypeInNode(parentNode, action)
        }
        return list
    }

    /**
     * id,type都满足
     */
    private fun findById(
        accessibilityNodeInfo: AccessibilityNodeInfo,
        action: Action
    ): List<AccessibilityNodeInfo>? {
        val list = findNodesByIds(accessibilityNodeInfo, action) { nextWidgetID ->
            Log.d(TAG, "findById: view empty, find next id $nextWidgetID")
        }
        if (list.isNullOrEmpty()) {
            return null
        }
        val results = mutableListOf<AccessibilityNodeInfo>()
        for (next in list) {
            if (action.widgetType == next.className) {
                results.add(next)
            }
        }
        return results
    }

    private fun findById(
        accessibilityNodeInfo: AccessibilityNodeInfo,
        widgetType: String,
        widgetId: String
    ): List<AccessibilityNodeInfo>? {
        if (widgetId.isEmpty()) {
            return null
        }
        val list = FindNodeUtil.findNodesById(accessibilityNodeInfo, widgetId)
        if (list.isEmpty()) {
            return null
        }
        val results = mutableListOf<AccessibilityNodeInfo>()
        for (next in list) {
            if (widgetType == next.className) {
                results.add(next)
            }
        }
        return results
    }

    /**
     * text,type都满足
     */
    private fun findByText(
        accessibilityNodeInfo: AccessibilityNodeInfo,
        action: Action
    ): List<AccessibilityNodeInfo> {
        val results = mutableListOf<AccessibilityNodeInfo>()
        val widgetText = InputUtil.textParse(action.inputMethod?.get(0), action.widgetText ?: "")
        val list = FindNodeUtil.findNodesByText(
            accessibilityNodeInfo,
            widgetText,
            action.findWidgetByTxtDirectly
        )
        findCommon(widgetText, list, action, results)
        return results
    }

    private fun findByText(
        node: AccessibilityNodeInfo,
        widgetType: String,
        widgetText: String,
        directly: Boolean
    ): List<AccessibilityNodeInfo> {
        val results = mutableListOf<AccessibilityNodeInfo>()
        val list = FindNodeUtil.findNodesByText(node, widgetText, directly)
        findCommon(widgetText, list, widgetType, results)
        return results
    }

    /**
     * id,text,type三个都满足
     */
    private fun findByIdAndText(
        accessibilityNodeInfo: AccessibilityNodeInfo,
        action: Action
    ): List<AccessibilityNodeInfo> {
        val list = findNodesByIds(accessibilityNodeInfo, action) { nextWidgetID ->
            Log.d(TAG, "findByIdAndText: view empty find next id $nextWidgetID")
        }
        val results = mutableListOf<AccessibilityNodeInfo>()
        val widgetText = InputUtil.textParse(action.inputMethod?.get(0), action.widgetText ?: "")
        findCommon(widgetText, list, action, results)
        return results
    }

    @VisibleForTesting
    internal fun findNodesByIds(
        nodeInfo: AccessibilityNodeInfo,
        action: Action,
        leftWidgetIDList: MutableList<String>? = null,
        onFindByNextIdInList: (String) -> Unit
    ): List<AccessibilityNodeInfo>? {
        val widgetID = if (leftWidgetIDList.isNullOrEmpty()) {
            action.widgetID
        } else {
            leftWidgetIDList[0]
        }
        val list = if (widgetID.isNullOrEmpty()) {
            null
        } else {
            FindNodeUtil.findNodesById(nodeInfo, widgetID)
        }
        val widgetIDList = leftWidgetIDList ?: action.widgetIDList?.toMutableList()
        if (list.isNullOrEmpty() && !widgetIDList.isNullOrEmpty()) {
            if (leftWidgetIDList != null) {
                widgetIDList.removeAt(0)
                if (widgetIDList.isEmpty()) {
                    return null
                }
            }
            onFindByNextIdInList(widgetIDList[0])
            return findNodesByIds(nodeInfo, action, widgetIDList, onFindByNextIdInList)
        }
        return list
    }

    private fun findByIdAndText(
        node: AccessibilityNodeInfo,
        widgetType: String,
        widgetId: String,
        widgetText: String
    ): List<AccessibilityNodeInfo> {
        val results = mutableListOf<AccessibilityNodeInfo>()
        if (widgetId.isEmpty()) {
            return results
        }
        val list = FindNodeUtil.findNodesById(node, widgetId)
        findCommon(widgetText, list, widgetType, results)
        return results
    }

    /**
     * type与node的text和contentDescription比较
     */
    private fun findCommon(
        widgetText: String?,
        list: List<AccessibilityNodeInfo>?,
        action: Action,
        results: MutableList<AccessibilityNodeInfo>
    ) {
        if (widgetText.isNullOrEmpty() || list.isNullOrEmpty()) {
            return
        }
        for (next in list) {
            if (action.widgetType != next.className) {
                continue
            }
            val text =
                InputUtil.textParse(action.inputMethod?.get(0), next.text?.toString() ?: "")
            val contentDescription = InputUtil.textParse(
                action.inputMethod?.get(0),
                next.contentDescription?.toString() ?: ""
            )
            val isEqualsType = InputUtil.isCheckTypeEquals(action)
            //text为空contentDescription不为空比较
            if (text.isEmpty() || !InputUtil.isEqualsIgnoreSpace(widgetText, text)) {
                if (isContentDescriptionMatched(widgetText, contentDescription)) {
                    if (!isEqualsType || !InputUtil.isEqualsIgnoreSpace(widgetText, text, action)) {
                        results.add(next)
                    } else {
                        results.add(next)
                    }
                }
            } else if (!isEqualsType || InputUtil.isEqualsIgnoreSpace(widgetText, text, action)) {
                //text比较
                results.add(next)
            }
        }
    }

    private fun isContentDescriptionMatched(widgetText: String, description: String?): Boolean =
        !description.isNullOrEmpty() && InputUtil.isEqualsIgnoreSpace(widgetText, description)

    private fun findCommon(
        widgetText: String?,
        list: List<AccessibilityNodeInfo>?,
        widgetType: String,
        results: MutableList<AccessibilityNodeInfo>
    ) {
        if (widgetText.isNullOrEmpty() || list.isNullOrEmpty()) {
            return
        }
        for (next in list) {
            val className = next.className ?: continue
            if (!className.contains(widgetType)) {
                continue
            }
            if (FindNodeUtil.isWidgetTextMatched(next, widgetText)) {
                results.add(next)
            }
        }
    }

    private fun findChild(
        accessibilityNodeInfo: AccessibilityNodeInfo?,
        action: Action
    ): AccessibilityNodeInfo? {
        val widgetIndex = action.widgetIndex
        if (accessibilityNodeInfo == null || widgetIndex.isNullOrEmpty()) {
            return null
        }
        val iterator = widgetIndex.iterator()
        var i = 0
        var nodeInfo = accessibilityNodeInfo
        while (iterator.hasNext()) {
            val index = iterator.next()
            nodeInfo = obtainChildNodeInfo(index, nodeInfo)
            //以上三个条件都不满足，action.childWidgetText为空可返回自身
            if (i == widgetIndex.size - 1) {
                return findByChildWidget(action, nodeInfo)
            } else {
                i++
            }
        }
        return null
    }

    private fun obtainChildNodeInfo(
        index: Int,
        nodeInfo: AccessibilityNodeInfo?
    ): AccessibilityNodeInfo? {
        if (nodeInfo == null) {
            return null
        }
        val parent = nodeInfo.parent
        var result = nodeInfo
        if ((index in 0 until MAX_FIND_CHILD_INDEX) && (nodeInfo.childCount > index)) {
            result = nodeInfo.getChild(index)
        } else if ((index == MAX_FIND_CHILD_INDEX) && (parent != null)) {
            result = parent
        } else if ((index < 0) && (nodeInfo.childCount > abs(nodeInfo.childCount + index))) {
            result = nodeInfo.getChild(abs(index + nodeInfo.childCount))
        }
        return result
    }

    private fun findByChildWidget(
        action: Action,
        nodeInfo: AccessibilityNodeInfo?
    ): AccessibilityNodeInfo? {
        if (nodeInfo == null) {
            return null
        }
        val childFeature = ChildFeature(action)
        return if (!childFeature.isAllFeatureNotEmpty()) {
            childFeature.checkAllChildFeature(action, nodeInfo)
        } else if (childFeature.isWidgetIdOrTypeEmpty()) {
            childFeature.checkPartialChildFeature(action, nodeInfo)
        } else if (childFeature.isNotMatchNodeInfo(nodeInfo)) {
            null
        } else {
            nodeInfo
        }
    }

    private fun ChildFeature.isAllFeatureNotEmpty(): Boolean =
        !childWidgetID.isNullOrEmpty() && !childWidgetText.isNullOrEmpty()
                && !childWidgetType.isNullOrEmpty()

    private fun ChildFeature.checkAllChildFeature(
        action: Action,
        nodeInfo: AccessibilityNodeInfo
    ): AccessibilityNodeInfo? {
        if (checkAllForNodeText(action, nodeInfo)) return nodeInfo
        if (checkAllForNodeDescription(action, nodeInfo)) return nodeInfo
        return null
    }

    private fun ChildFeature.checkAllForNodeText(
        action: Action,
        nodeInfo: AccessibilityNodeInfo
    ): Boolean = checkAllForNodeString(action, nodeInfo, nodeInfo.text)

    private fun ChildFeature.checkAllForNodeDescription(
        action: Action,
        nodeInfo: AccessibilityNodeInfo
    ): Boolean = checkAllForNodeString(action, nodeInfo, nodeInfo.contentDescription)

    private fun ChildFeature.checkAllForNodeString(
        action: Action,
        nodeInfo: AccessibilityNodeInfo,
        nodeString: CharSequence?
    ): Boolean {
        nodeString ?: return false
        if (childWidgetID != nodeInfo.viewIdResourceName) {
            return false
        }
        if (childWidgetType != nodeInfo.className) {
            return false
        }
        return InputUtil.isEqualsIgnoreSpace(
            widgetText,
            InputUtil.textParse(inputMethod, nodeString.toString()),
            action
        )
    }

    private fun ChildFeature.checkPartialChildFeature(
        action: Action,
        nodeInfo: AccessibilityNodeInfo
    ): AccessibilityNodeInfo? {
        if (action.childOnlyCheckText) {
            return checkChildOnlyWithText(action, nodeInfo)
        }
        if (childWidgetText.isNullOrEmpty() || childWidgetType.isNullOrEmpty()) {
            return nodeInfo
        }
        if (checkTypeAndTextForNodeString(action, nodeInfo, nodeInfo.text)) {
            return nodeInfo
        }
        if (checkTypeAndTextForNodeString(action, nodeInfo, nodeInfo.contentDescription)) {
            return nodeInfo
        }
        return null
    }

    private fun ChildFeature.checkChildOnlyWithText(
        action: Action,
        nodeInfo: AccessibilityNodeInfo
    ): AccessibilityNodeInfo? {
        if (checkOnlyTextForNodeString(action, nodeInfo.text)) {
            return nodeInfo
        }
        if (checkOnlyTextForNodeString(action, nodeInfo.contentDescription)) {
            return nodeInfo
        }
        return null
    }

    private fun ChildFeature.checkOnlyTextForNodeString(
        action: Action,
        nodeString: CharSequence?
    ): Boolean {
        nodeString ?: return false
        return InputUtil.isEqualsIgnoreSpace(
            widgetText,
            InputUtil.textParse(inputMethod, nodeString.toString()),
            action
        )
    }

    private fun ChildFeature.checkTypeAndTextForNodeString(
        action: Action,
        nodeInfo: AccessibilityNodeInfo,
        nodeString: CharSequence?
    ): Boolean {
        nodeString ?: return false
        if (childWidgetType != nodeInfo.className.toString()) {
            return false
        }
        return InputUtil.isEqualsIgnoreSpace(
            widgetText,
            InputUtil.textParse(inputMethod, nodeString.toString()),
            action
        )
    }

    private fun ChildFeature.isWidgetIdOrTypeEmpty(): Boolean =
        childWidgetID.isNullOrEmpty() || childWidgetType.isNullOrEmpty()

    private fun ChildFeature.isNotMatchNodeInfo(
        nodeInfo: AccessibilityNodeInfo
    ): Boolean = nodeInfo.run {
        (childWidgetID != viewIdResourceName) || (childWidgetType != className)
    }

    private companion object {
        private const val TAG = "UiSelector"
        private const val MAX_FIND_CHILD_INDEX = 100
    }

    private data class ChildFeature(
        val childWidgetID: String?,
        val childWidgetText: String?,
        val childWidgetType: String?,
        val inputMethod: Int?,
        val widgetText: String
    ) {
        constructor(action: Action) : this(
            childWidgetID = action.childWidgetID,
            childWidgetText = action.childWidgetText,
            childWidgetType = action.childWidgetType,
            inputMethod = action.inputMethod?.get(0),
            widgetText = InputUtil.textParse(
                action.inputMethod?.get(0),
                action.childWidgetText ?: ""
            ) ?: ""
        )
    }
}