/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ArchivePreviewFragment.kt
 * Description:
 *     The fragment to preview archive file.
 *
 * Version: 1.0
 * Date: 2024-10-15
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-10-15   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.archive

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewFragment
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.preview.core.PreviewOperationsController
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite
import com.oplus.filemanager.preview.widget.PreviewFilePathItem

internal class ArchivePreviewFragment :
    AbsPreviewFragment<ArchivePreviewViewModel>(R.layout.fragmen_preview_archive) {

    private companion object {
        private const val TAG = "ArchivePreviewFragment"
    }

    override val logTag: String = TAG
    override val viewModelClass: Class<ArchivePreviewViewModel> =
        ArchivePreviewViewModel::class.java
    override val operationsBarId: Int = R.id.preview_operations_bar
    override val openButtonTextId: Int = com.filemanager.common.R.string.unzip
    override val fragmentInstance: Fragment = this

    private var filePreview: IArchiveFilePreview? = null

    override fun isPreviewFileApproved(context: Context, fileBean: BaseFileBean): Boolean =
        FilePreviewTypeCheckHelper(context).isArchiveFileType(fileBean, TAG)

    override fun onViewModelCreated(view: View, viewModel: ArchivePreviewViewModel) {
        filePreview = prepareFilePreview(view, viewModel)
    }

    private fun prepareFilePreview(
        view: View,
        viewModel: ArchivePreviewViewModel
    ): IArchiveFilePreview {
        val containerManager = ArchivePreviewContainerManager(this, viewModel, view)
        val previewImpl = IArchiveFilePreview.obtain(viewLifecycleOwner, viewModel)
        previewImpl.bindOperationsBar(operationsBar)
        previewImpl.attachToContainer(containerManager)
        return previewImpl
    }

    override fun putPreviewFileToViewModel(
        viewModel: ArchivePreviewViewModel,
        fileBean: BaseFileBean
    ) {
        val vmActivity = activity as? BaseVMActivity ?: run {
            Log.e(TAG, "putPreviewFileToViewModel: ERROR! activity is not BaseVMActivity")
            return
        }
        viewModel.putPreviewFile(vmActivity, fileBean)
    }

    override fun onCreateFilePathItem(view: View): PreviewFilePathItem {
        val item: PreviewFilePathItem = view.findViewById(R.id.preview_remote_location_info)
        item.setLabelGone()
        return item
    }

    override fun onCreatePreviewOperationsController(
        viewModel: FilePreviewViewModel
    ): PreviewOperationsController = ArchiveOperationsController(this, viewModel, selectPathsCache)

    override fun onDestroyView() {
        super.onDestroyView()
        filePreview?.release()
        filePreview = null
    }

    override fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        val archiveCard =
            view?.findViewById<ViewGroup>(R.id.preview_archive_card) ?: return
        val lp = archiveCard.layoutParams as? ConstraintLayout.LayoutParams ?: return
        val res = requireContext().resources
        lp.matchConstraintMaxWidth = res.getDimensionPixelOffset(R.dimen.preview_archive_max_width)
        archiveCard.layoutParams = lp
    }

    override fun pressBack(): Boolean {
        return previewModel?.compressModel?.pressBack() ?: false
    }
}