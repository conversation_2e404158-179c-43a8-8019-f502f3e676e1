/*********************************************************************************
 * Copyright (C), 2008-2024, Oplus, All rights reserved.
 *
 * File: - PreviewScrollAreaHelper.kt
 * Description:
 *     The helper to update UI for PreviewScrollView
 *
 * Version: 1.0
 * Date: 2024-11-14
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-11-14   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.utils

import android.view.WindowInsets
import androidx.core.view.postDelayed
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.widget.PreviewOperationsBar
import com.oplus.filemanager.preview.widget.PreviewScrollView

internal class PreviewScrollAreaHelper(
    private val scrollView: PreviewScrollView,
    private val operationsBar: PreviewOperationsBar
) : PreviewOperationsBar.OnWindowInsetsChangedListener {

    private companion object {
        private const val AUTO_UPDATE_DIVIDER_DELAY = 150L
    }

    private var requireHideDivider = true

    init {
        operationsBar.setOnWindowInsetsChangedListener(this)
    }

    fun checkShowDivider() {
        requireHideDivider = false
        operationsBar.setDividerVisible(scrollView.isContentScrollable())
    }

    fun hideDivider() {
        requireHideDivider = true
        operationsBar.setDividerVisible(false)
    }

    fun enableScrollViewportMinHeight() {
        updateScrollViewportMinHeight(
            scrollView.context.resources
                .getDimensionPixelOffset(R.dimen.preview_audio_scroll_layout_min_height)
        )
    }

    fun enableScrollViewportMinHeight(height: Int) {
        updateScrollViewportMinHeight(height)
    }

    fun disableScrollViewportMinHeight() {
        updateScrollViewportMinHeight(0)
    }

    private fun updateScrollViewportMinHeight(minHeight: Int) {
        scrollView.setFillViewportMinHeight(minHeight)
        scrollView.requestLayout()
    }

    override fun onWindowInsetsChanged(insets: WindowInsets?) {
        operationsBar.postDelayed(AUTO_UPDATE_DIVIDER_DELAY) {
            if (requireHideDivider) {
                operationsBar.setDividerVisible(false, false)
            } else {
                operationsBar.setDividerVisible(scrollView.isContentScrollable(), false)
            }
        }
    }
}