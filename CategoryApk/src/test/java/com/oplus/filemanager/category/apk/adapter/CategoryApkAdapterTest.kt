package com.oplus.filemanager.category.apk.adapter

import android.content.Context
import android.os.Looper
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.viewholder.BaseFileBrowserVH
import com.filemanager.common.viewholder.NormalListVH
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.ad.AdViewHolder
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.ad.SubPageAdMgr
import com.oplus.filemanager.interfaze.oaps.IOapsLib
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * CategoryApkAdapter的单元测试类
 * 用于测试CategoryApkAdapter的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class CategoryApkAdapterTest {

    // 定义测试所需的成员变量
    private lateinit var adapter: CategoryApkAdapter
    private lateinit var mockContext: Context
    private lateinit var mockLifecycle: Lifecycle
    private lateinit var mockAdManager: SubPageAdMgr
    private lateinit var mockHolder: RecyclerView.ViewHolder
    private lateinit var mockFile: MediaFileWrapper
    private lateinit var mockAdViewHolder: AdViewHolder
    private lateinit var mockFondMoreViewHolder: CategoryApkAdapter.FondMoreViewHolder

    /**
     * 测试前的初始化方法
     * 创建所有mock对象和测试适配器实例
     */
    @Before
    fun setUp() {
        // 创建mock的Context对象
        mockContext = mockk(relaxed = true) {
            every { applicationContext } returns mockk(relaxed = true)
        }
        // 创建mock的Lifecycle对象
        mockLifecycle = mockk(relaxed = true)
        // 创建mock的广告管理器对象
        mockAdManager = mockk(relaxed = true)
        // 创建mock的ViewHolder对象
        mockHolder = mockk(relaxed = true)
        // 创建mock的文件对象
        mockFile = mockk(relaxed = true)
        // 创建mock的广告ViewHolder对象
        mockAdViewHolder = mockk(relaxed = true)
        // 创建mock的"发现更多"ViewHolder对象
        mockFondMoreViewHolder = mockk(relaxed = true)

        // 初始化测试用的适配器实例
        adapter = CategoryApkAdapter(
            mockContext,
            false,
            true,
            mockLifecycle,
            mockAdManager
        )
    }

    /**
     * 测试后的清理方法
     * 释放所有mock对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试setData方法是否正确更新文件和选择数组
     */
    @Test
    fun `test setData updates files and selection array`() {
        // 准备测试数据
        val testData = arrayListOf(mockFile)
        val selectionArray = arrayListOf(1)

        // 调用测试方法
        adapter.setData(testData, selectionArray)

        // 验证结果
        assertEquals(testData, adapter.mFiles)
        assertEquals(selectionArray, adapter.mSelectionArray)
    }

    /**
     * 测试getItemCount方法返回正确的项目数量
     */
    @Test
    fun `test getItemCount returns correct count`() {
        // 设置测试数据
        adapter.mFiles = arrayListOf(mockFile)
        // 验证当hasFoundMoreApp为true时，数量+1
        assertEquals(2, adapter.itemCount)

        // 重新创建适配器，设置hasFoundMoreApp为false
        adapter = CategoryApkAdapter(
            mockContext,
            false,
            false,
            mockLifecycle,
            mockAdManager
        )
        adapter.mFiles = arrayListOf(mockFile)
        // 验证当hasFoundMoreApp为false时，数量不变
        assertEquals(1, adapter.itemCount)
    }

    /**
     * 测试setKeyWord方法是否正确更新关键字
     */
    @Test
    fun `test setKeyWord updates keyword`() {
        val testKey = "test"
        adapter.setKeyWord(testKey)
        // 由于mKeyWord是private的，这里主要验证方法调用不抛异常
    }

    /**
     * 测试getItemKey方法返回正确的文件ID
     */
    @Test
    fun `test getItemKey returns file id`() {
        // 设置mock行为
        every { mockFile.id } returns 123
        // 验证返回的文件ID是否正确
        assertEquals(123, adapter.getItemKey(mockFile, 0))
    }

    /**
     * 测试FondMoreViewHolder的bindData方法是否正确设置点击监听器
     */
    @Test
    fun `test FondMoreViewHolder bindData sets click listener`() {
        // mock静态Injector类
        mockkObject(Injector)
        // 创建mock的IOapsLib对象
        val mockOapsLib = mockk<IOapsLib>(relaxed = true)
        // 设置Injector的mock行为
        every { Injector.injectFactory<IOapsLib>() } returns mockOapsLib

        // 创建mock的View和TextView
        val view = mockk<View>(relaxed = true)
        val textView = mockk<TextView>(relaxed = true)
        every { view.findViewById<TextView>(any()) } returns textView

        // 创建ViewHolder并调用测试方法
        val holder = CategoryApkAdapter.FondMoreViewHolder(view)
        holder.bindData(true)

        // 验证是否设置了点击监听器
        verify { textView.setOnClickListener(any()) }
    }
}