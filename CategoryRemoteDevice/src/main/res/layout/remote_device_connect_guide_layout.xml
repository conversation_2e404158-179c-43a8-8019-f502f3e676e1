<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/remote_device_connect_guide_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@+id/guide_center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guide_center">

        <LinearLayout
            android:id="@+id/content_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/device_icon"
                android:layout_width="@dimen/empty_content_img_width"
                android:layout_height="@dimen/empty_content_img_height"
                android:layout_gravity="center"
                android:forceDarkAllowed="false"
                android:src="@drawable/ic_remote_device_connectable"/>

            <TextView
                android:id="@+id/connect_status"
                android:layout_width="@dimen/empty_content_img_width"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/device_can_be_connected"
                android:textAppearance="?attr/couiTextHeadlineXS"
                android:textColor="?attr/couiColorLabelPrimary" />
            <TextView
                android:id="@+id/status_des"
                android:layout_width="@dimen/empty_content_img_width"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_2dp"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/device_connected_tips"
                android:textAppearance="?attr/couiTextBodyXS"
                android:textColor="?attr/couiColorLabelSecondary"/>

            <TextView
                android:id="@+id/guide_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dimen_12dp"
                android:layout_marginBottom="@dimen/dimen_4dp"
                android:fontFamily="sans-serif-medium"
                android:clickable="true"
                android:gravity="center"
                android:textAppearance="?attr/couiTextButtonM"
                android:textColor="?attr/couiColorLabelTheme"/>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/account_input_box_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_marginTop="@dimen/dimen_32dp">

                <com.coui.appcompat.edittext.COUICardSingleInputView
                    android:id="@+id/device_code"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:couiHint="@string/remote_control_device_code_hint"
                    app:couiTitle="@string/remote_control_device_code"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.coui.appcompat.edittext.COUICardSingleInputView
                    android:id="@+id/device_passwd"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:couiTitle="@string/connection_password"
                    app:couiHint="@string/remote_control_device_password_hint"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/device_code" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="@dimen/toolbar_divider_height"
                    android:layout_marginStart="@dimen/coui_appbar_divider_expanded_margin_horizontal"
                    android:layout_marginEnd="@dimen/coui_appbar_divider_expanded_margin_horizontal"
                    android:background="?attr/couiColorDivider"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/device_code" />

                <ImageView
                    android:id="@+id/remote_edit"
                    android:layout_width="@dimen/dimen_22dp"
                    android:layout_height="@dimen/dimen_22dp"
                    android:src="@drawable/remote_passwd_edit"
                    android:clickable="false"
                    android:layout_marginEnd="@dimen/dimen_32dp"
                    android:layout_marginBottom="@dimen/dimen_11dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </ScrollView>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.45" />

    <com.coui.appcompat.button.COUIButton
        android:id="@+id/refresh_button"
        style="@style/Widget.COUI.Button.Large.ButtonNew.Secondary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dimen_24dp"
        android:textAppearance="@style/couiTextAppearanceButtonL"
        android:gravity="center_horizontal|center_vertical"
        android:text="@string/string_refresh"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.coui.appcompat.button.COUIButton
        android:id="@+id/connect_button"
        style="@style/Widget.COUI.Button.Large.ButtonNew"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dimen_24dp"
        android:textAppearance="@style/couiTextAppearanceButtonL"
        android:gravity="center_horizontal|center_vertical"
        android:text="@string/connect"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>