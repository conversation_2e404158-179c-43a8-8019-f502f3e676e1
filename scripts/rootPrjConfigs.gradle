import com.sun.management.OperatingSystemMXBean
import java.lang.management.ManagementFactory

static printCiRuntimeInfo() {
    final cpuCoreNum = Runtime.runtime.availableProcessors()
    final mem = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean()
    final long memSize = (mem.getTotalPhysicalMemorySize() / 1024 / 1024)
    println("CI runtime info: cpuLogicCore=$cpuCoreNum, physicalMemory=${memSize}MB")
}

printCiRuntimeInfo()

// Should use "apply from:" to be included into root build.gradle
apply plugin: 'ocoverage'

CoveragePlugin {
    testVariants = "oppoPallDomesticApilevelallDebug,debug"
    blackProjects = prop_unitTestBlackProjects
    enableJacoco = true
    jacocoVersion = libs.versions.jacoco.get()
}

static void checkBaseCompileConfig(Project prj) {
    final isAppModule = prj.plugins.hasPlugin("com.android.application")
    final isLibModule = prj.plugins.hasPlugin("com.android.library")
    if (!isAppModule && !isLibModule) {
        return
    }
    boolean hasBaseCompile = false
    if (prj.ext.has("appliedBaseCompile")) {
        hasBaseCompile = prj.ext.appliedBaseCompile.toBoolean()
    }
    if (!hasBaseCompile) {
        final errMsg = "${prj.path}: Must apply baseCompile.gradle to align base compile configs."
        System.err.println(errMsg)
        throw new GradleException(errMsg)
    }
}

/**
 * 确保所有Android代码module进行了统一的基础编译配置，避免不同module配置不一致引起各种问题
 */
rootProject.allprojects { Project prj ->
    prj.afterEvaluate {
        checkBaseCompileConfig(prj)
    }
}