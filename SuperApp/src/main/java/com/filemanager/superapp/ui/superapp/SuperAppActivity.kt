/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.superapp
 * * Version     : 1.0
 * * Date        : 2020/4/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.superapp.ui.superapp

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.DragEvent
import android.view.Menu
import android.view.MenuItem
import android.view.View
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.dragselection.DragDropInterface
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.superapp.R
import com.filemanager.superappsource.helper.SuperAppHelper
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.encrypt.EncryptActivity
import com.oplus.labelmanager.AddFileLabelController
import com.oplus.selectdir.SelectPathController

class SuperAppActivity : EncryptActivity(), NavigationInterface, NavigationBarView.OnItemSelectedListener,
    TransformNextFragmentListener, BaseVMActivity.PermissonCallBack, DragDropInterface, IDraggingActionOperate {
    companion object {
        private const val TAG = "SuperAppActivity"
        //AndroidManifest中的Action，通过指定路径打开当前页面，并显示指定路径文件夹
        private const val ACTION_SOURCES_DOC = "oplus.intent.action.filemanager.SOURCES_DOC"
        private const val ACTION_SUPER_APP = "oplus.intent.action.filemanager.SUPER_APP"
        const val ACTION_SOURCES_DOC_KEY_SOURCE_PATH = "sourcePath"
        const val ACTION_EXTERNAL_APP_KEY_PACKAGE_NAME = "pkgName"
        const val TAG_SUPER_APP_PARENT_FRAGMENT = "superAppParentFragment"
    }

    private var mSuperAppParentFragment: SuperAppParentFragment? = null

    private var mActionActivityResultListener: ActionActivityResultListener? = null
    private val mNavigationController by lazy { NavigationController(lifecycle, id = R.id.navigation_tool) }
    private val mSelectPathController by lazy { SelectPathController(lifecycle) }
    private val mAddFileLabelController by lazy { AddFileLabelController(lifecycle) }

    override fun getLayoutResId(): Int {
        return R.layout.super_app_activity
    }

    @SuppressLint("RestrictedApi")
    override fun initView() {
        registerVmChangedReceiver(null)
        mSuperAppParentFragment = (supportFragmentManager.findFragmentByTag(TAG_SUPER_APP_PARENT_FRAGMENT) as? SuperAppParentFragment)
            ?: SuperAppParentFragment()
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container_view, mSuperAppParentFragment!!, TAG_SUPER_APP_PARENT_FRAGMENT).commit()
    }

    override fun initData() {
        Log.d(TAG, "initData -> action = ${intent.action}")
        if (intent.action == ACTION_SOURCES_DOC || intent.action == ACTION_SUPER_APP) {
            //open activity from other app by action, find the source bean from path
            mSuperAppParentFragment?.findAndCheckSourceBeanByIntent(intent)
        } else if (intent.hasExtra(ACTION_EXTERNAL_APP_KEY_PACKAGE_NAME)) {
            //open activity from other app by action, find the source bean from package
            mSuperAppParentFragment?.findSourceBean(intent)
        } else {
            val titleResId = IntentUtils.getInt(intent, Constants.TITLE_RES_ID, -1)
            var title = if (titleResId > 0) {
                getString(titleResId)
            } else {
                IntentUtils.getString(intent, Constants.TITLE)
            }
            try {
                val pathArray = intent.getStringArrayExtra(KtConstants.P_SUPER_PATH_LIST)
                val dirDepth = intent.getIntExtra(KtConstants.SUPER_DIR_DEPTH, SuperLoader.DEFAULT_DIR_DEPTH)
                val superAppPackage = intent.getStringExtra(KtConstants.P_PACKAGE)
                superAppPackage?.let { packageName ->
                    SuperAppHelper.getCategoryItems(appContext).find {
                        it.packageName == packageName
                    }?.let { item ->
                        title = item.name
                    }
                }
                val externalPath = intent.getStringExtra(KtConstants.FILE_PATH)
                val sideCategoryType = IntentUtils.getInt(intent, Constants.SIDE_CATEGORY_TYPE, -1)

                val bundle = Bundle().apply {
                    putString(Constants.TITLE, title)
                    putStringArray(KtConstants.P_SUPER_PATH_LIST, pathArray)
                    putInt(KtConstants.SUPER_DIR_DEPTH, dirDepth)
                    putString(KtConstants.P_PACKAGE, superAppPackage)
                    putInt(Constants.TITLE_RES_ID, titleResId)
                    putInt(Constants.SIDE_CATEGORY_TYPE, sideCategoryType)
                    putString(KtConstants.FILE_PATH, externalPath)
                }
                mSuperAppParentFragment?.arguments = bundle
                mSuperAppParentFragment?.updateIntentData(title, pathArray, dirDepth, superAppPackage, externalPath)
            } catch (e: Exception) {
                Log.e(TAG, "initData: ${e.message}")
            }
        }
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        mSuperAppParentFragment?.refreshCurrentFragment()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent ?: return
        if (intent.action == ACTION_SOURCES_DOC) {
            //open activity from other app by action, find the source bean from path
            val sourceBean = mSuperAppParentFragment?.findAndCheckSourceBeanByIntent(intent)
            sourceBean ?: return
            mSuperAppParentFragment?.refreshByNewIntent()
        }
    }


    override fun startObserve() {
    }

    override fun onRefreshData() {
        mSuperAppParentFragment?.onResumeLoadData()
    }

    override fun onPermissionSuccess(isInstallPermission: Boolean?) {
        super.onPermissionSuccess(isInstallPermission)
        window.decorView.post {
            mSuperAppParentFragment?.refreshCurrentFragment()
        }
    }

    override fun showNavigation() {
        mNavigationController.showNavigation(this)
        updateNavigationToolPadding()
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean, hasAndroidData: Boolean) {
        mNavigationController.setNavigateItemAble(isEnable = isEnable, mHasDrm = mHasDrm, hasAndroidData = hasAndroidData)
    }

    override fun hideNavigation() {
        mNavigationController.hideNavigation(this)
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
        mActionActivityResultListener = actionActivityResultListener
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        mSuperAppParentFragment?.onCreateOptionsMenu(menu, menuInflater)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return mSuperAppParentFragment?.onMenuItemSelected(item) ?: super.onOptionsItemSelected(item)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mActionActivityResultListener?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onNavigationItemSelected(p0: MenuItem): Boolean {
        return mSuperAppParentFragment?.onNavigationItemSelected(p0) ?: false
    }

    override fun onBackPressed() {
        if (mSuperAppParentFragment?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
        }
    }

    override fun backtoTop() {
        super.backtoTop()
        mSuperAppParentFragment?.fastSmoothScrollToTop()
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mSelectPathController.updateDialogHeightIfNeed(supportFragmentManager)
        mSuperAppParentFragment?.onUIConfigChanged(configList)
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
        mSelectPathController.onDestroy()
    }

    override fun transformToNextFragment(path: String?) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        mAddFileLabelController.showAddLabelFragment(supportFragmentManager, fileList)
    }

    override fun onUpdatedLabel() {
        mSuperAppParentFragment?.onResumeLoadData()
    }

    override fun hasShowPanel(): Boolean {
        return mSelectPathController.hasShowPanel() || mAddFileLabelController.hasShowPanel(supportFragmentManager)
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, code)
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        mSelectPathController.onDestroy()
        mSuperAppParentFragment?.fromSelectPathResult(code, paths)
    }

    override fun handleNoStoragePermission() {
        mSuperAppParentFragment?.setPermissionEmptyVisible(View.VISIBLE)
    }

    override fun updateNavigationToolPadding() {
        mNavigationController.updateNavigationToolPadding(navPaddingBottom)
    }

    fun getShowSuperAppFilePath(): Array<String>? {
        return mSuperAppParentFragment?.getFilePaths()
    }

    override fun handleDragEvent(event: DragEvent?): Boolean? {
        return event?.let { mSuperAppParentFragment?.handleDragScroll(it) }
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        return mSuperAppParentFragment?.getSelectedItemView()
    }

    override fun setNavigateItemAble() {
        mSuperAppParentFragment?.setNavigateItemAble()
    }

    override fun getDragCurrentPath(): String? {
        return null
    }
}
