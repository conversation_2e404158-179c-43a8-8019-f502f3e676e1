/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LoadMediaDBTaskQTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/29       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.recent.task

import android.content.Context
import android.net.Uri
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.BlacklistParser
import com.filemanager.common.utils.WhiteListParser
import com.oplus.filemanager.recent.task.LoadMediaDBTaskQ
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class LoadMediaDBTaskQTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getExternalSdPath(context) }.returns(null)
        every { VolumeEnvironment.getInternalSdPath(context) }.returns("/test/InternalSdPath")
        every { VolumeEnvironment.getOTGPath(context) }.returns(null)
        mockkStatic(Uri::class)
        val uri = mockk<Uri>()
        every { Uri.parse(any()) } returns uri
        mockkStatic(BlacklistParser::class)
        every { BlacklistParser.getsRecentFilterFileType(context) } returns emptyArray()
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun testDocumentFilterCondition() {
        val loadMediaDBTask = LoadMediaDBTaskQ()
        mockkStatic(WhiteListParser::class)
        every { WhiteListParser.getDocumentList(context) } returns null
        Assert.assertEquals("", loadMediaDBTask.getDocumentFilterCondition(context))
        every { WhiteListParser.getDocumentList(context) } returns ".doc:.xls:.ppt:.pdf"
        val result = loadMediaDBTask.getDocumentFilterCondition(context)
        Assert.assertEquals(
            "( _data LIKE '%.doc%' OR " +
                    "_data LIKE '%.xls%' OR " +
                    "_data LIKE '%.ppt%' OR " +
                    "_data LIKE '%.pdf%') ",
            result)
        every { WhiteListParser.getDocumentList(context) } returns ".doc:.xls:.ppt:.pdf:.txt"
        val txtResult = loadMediaDBTask.getDocumentFilterCondition(context)
        Assert.assertEquals(
            "( _data LIKE '%.doc%' OR " +
                    "_data LIKE '%.xls%' OR " +
                    "_data LIKE '%.ppt%' OR " +
                    "_data LIKE '%.pdf%' OR " +
                    "_data LIKE '%.txt%') ",
            txtResult)
    }
}