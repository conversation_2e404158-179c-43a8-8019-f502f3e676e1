/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: CompressHelperFactory.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/3/10
 ** Author: LiHao(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.compress

import android.util.ArrayMap
import com.filemanager.fileoperate.decompress.*

class CompressHelperFactory {

    companion object {
        private val FILE_HEADER_RAR = "526172"
        private val FILE_HEADER_ZIP = "504b3"
        private val FILE_HEADER_TAR = "757374"
        val FILE_HEADER_MAP = ArrayMap<String, Int>().let {
            it[FILE_HEADER_RAR] = RAR
            it[FILE_HEADER_ZIP] = ZIP
            it[FILE_HEADER_TAR] = TAR
            it
        }
        const val ZIP = 1
        const val JAR = 2
        const val TAR = 3
        const val RAR = 4
        const val P7ZIP = 5
        const val UNKNOWN_TYPE = 100

        fun getCompressHelper(compressType: Int): CompressHelper? {
            return when (compressType) {
                ZIP -> ZipCompressHelper()
                else -> null
            }
        }

        fun getDecompressHelper(compressType: Int): DecompressHelper<out BaseDecompressFile>? {
            return when (compressType) {
                ZIP -> ZipDecompressHelper()
                JAR -> JarDecompressHelper()
                RAR -> RarDecompressHelper()
                P7ZIP -> P7ZipDecompressHelper()
                else -> null
            }
        }
    }
}