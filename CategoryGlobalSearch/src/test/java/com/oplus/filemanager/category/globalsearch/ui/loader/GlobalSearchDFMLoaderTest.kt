/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GlobalSearchDFMLoaderTest
 ** Description : GlobalSearchDFMLoaderTest Unit Test
 ** Version     : 1.0
 ** Date        : 2023/2/21
 ** Author      : wanghonglei
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  wanghonglei     2023/2/21      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui.loader

import android.content.ContentProviderClient
import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.util.SparseArray
import com.filemanager.common.MyApplication
import com.filemanager.common.base.UriFileWrapper
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.DmpConst
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.oplus.filemanager.category.globalsearch.bean.SearchDFMFileWrapper
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchViewModel
import com.oplus.filemanager.dfm.DFMManager
import com.oplus.filemanager.provider.FileLabelDBHelper
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class GlobalSearchDFMLoaderTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
        mockkStatic(FeatureCompat::class)
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getInternalSdPath(context) } returns ""
        mockkStatic(CategoryHelper::class)
        every {
            CategoryHelper.setIgnoredPath(context, CategoryHelper.CATEGORY_DOC)
        } returns SparseArray(8)
        mockkObject(FileLabelDBHelper)
        mockkStatic(DFMManager::class)
        every { DFMManager.getDFSMountPath() } returns "dfs/xxx/"
    }

    @After
    fun tearDown() {
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(CategoryHelper::class)
        unmockkObject(FileLabelDBHelper)
    }

    @Test
    fun `should addItem when call if method is addDMPSearchItem`() {
        // Given
        val globalSearchDFMLoader = GlobalSearchDFMLoader()
        val searchResultData = GlobalSearchViewModel.SearchResultData()
        searchResultData.listDirectory = mutableListOf()
        val file1 = mockk<UriFileWrapper>(relaxed = true)
        every { file1.mLocalType } returns MimeTypeHelper.DIRECTORY_TYPE
        val file2 = mockk<UriFileWrapper>(relaxed = true)
        every { file2.recallType } returns 0
        val file3 = mockk<UriFileWrapper>(relaxed = true)
        every { file3.recallType } returns 1
        val file4 = mockk<UriFileWrapper>(relaxed = true)
        every { file4.recallType } returns 2
        val file5 = mockk<UriFileWrapper>(relaxed = true)
        every { file5.recallType } returns 3
        val file6 = mockk<UriFileWrapper>(relaxed = true)
        every { file6.recallType } returns 4
        // When
        globalSearchDFMLoader.addDMPSearchItem(file1, searchResultData)
        println("file2.recallType${file2.recallType}")
        globalSearchDFMLoader.addDMPSearchItem(file2, searchResultData)
        globalSearchDFMLoader.addDMPSearchItem(file3, searchResultData)
        globalSearchDFMLoader.addDMPSearchItem(file4, searchResultData)
        globalSearchDFMLoader.addDMPSearchItem(file5, searchResultData)
        globalSearchDFMLoader.addDMPSearchItem(file6, searchResultData)
        // Then
        Assert.assertEquals(false, searchResultData.listDirectory == null)
    }


    @Test
    fun `should addItem when call if method is handleNormalSearchItem`() {
        // Given
        val globalSearchDFMLoader = spyk(GlobalSearchDFMLoader())
        val searchResultData = GlobalSearchViewModel.SearchResultData()
        searchResultData.listDirectoryIds = mutableSetOf()
        searchResultData.listNameAndTitleIds = mutableSetOf()
        val file1 = mockk<UriFileWrapper>(relaxed = true)
        every { file1.mLocalType } returns MimeTypeHelper.DIRECTORY_TYPE
        every { file1.isDir() } returns true
        every { file1.mIsDirectory } returns true
        every { globalSearchDFMLoader.isFileExit(file1) } returns true
        every { file1.id } returns 1
        val file2 = mockk<UriFileWrapper>(relaxed = true)
        every { file2.mData } returns ""
        every { globalSearchDFMLoader.isFileExit(file2) } returns false
        every { file2.id } returns 2
        val normalResultData = GlobalSearchViewModel.NormalResultData()
        // When
        globalSearchDFMLoader.handleNormalSearchItem(normalResultData, file1, searchResultData)
        globalSearchDFMLoader.handleNormalSearchItem(normalResultData, file2, searchResultData)
        // Then
        Assert.assertEquals(false, normalResultData.listDirectory.isEmpty())
        Assert.assertEquals(true, normalResultData.listFileBean.isEmpty())
    }

    @Test
    fun `should verify when call if method is handleDmpSearch`() {
        val globalSearchDFMLoader = spyk(GlobalSearchDFMLoader())
        every {
            globalSearchDFMLoader.isFileExit(any())
        } returns true
        every {
            globalSearchDFMLoader.checkPathInCommonPath(any())
        } returns true
        every {
            globalSearchDFMLoader.checkDfmSupportDmp()
        } returns true
        every {
            globalSearchDFMLoader.isCancelled()
        } returns false
        val mockResoler: ContentResolver = mockk<ContentResolver>()
        every { context.contentResolver } returns mockResoler
        val mockProviderClient = mockk<ContentProviderClient>()
        every { mockResoler.acquireUnstableContentProviderClient(globalSearchDFMLoader.getDmpUri()) } returns mockProviderClient
        val mockCursor = mockk<Cursor>()
        val dmpSelection = globalSearchDFMLoader.getDmpSelection()
        val sortOrder = globalSearchDFMLoader.getSortOrder()
        every {
            mockProviderClient.query(any(), any(), dmpSelection, null, sortOrder)
        } answers {
            println("query return new mockCursor $mockCursor")
            mockCursor
        }
        every {
            mockCursor.count
        } returns 1
        every {
            mockCursor.moveToNext()
            //第一次调用返回为true，第2次调用吗，返回为true，第三次调用返回weifalse
        } returnsMany listOf(true, true, false)
        val spyFileBean = spyk(SearchDFMFileWrapper(mockCursor, globalSearchDFMLoader.getDmpUri()))
        every {
            spyFileBean.checkFileUri(any())
        } returns false andThen false andThen true
        every {
            spyFileBean.checkDmpUri(any())
        } returns true andThen true andThen false
        every {
            spyFileBean.mIsDirectory
        } returns false
        every {
            spyFileBean.recallType
        } returns DmpConst.RECALL_TYPE_TITLE_CONTENT
        every {
            globalSearchDFMLoader.createFromCursor(any(), any())
        } returns spyFileBean
        val searchResultData = GlobalSearchViewModel.SearchResultData()
        searchResultData.listTitleAndContent = mutableListOf()
        // When
        globalSearchDFMLoader.handleDmpSearch(searchResultData)
        // Then
        Assert.assertEquals(false, searchResultData.listTitleAndContent.isNullOrEmpty())
    }

    @Test
    fun `should addItem when call if method is handleNormalSearch`() {
        // Given
        val globalSearchDFMLoader = spyk(GlobalSearchDFMLoader())
        every {
            globalSearchDFMLoader.isFileExit(any())
        } returns true
        every {
            globalSearchDFMLoader.checkPathInCommonPath(any())
        } returns true
        every {
            globalSearchDFMLoader.checkDfmSupportDmp()
        } returns true
        every {
            globalSearchDFMLoader.isCancelled()
        } returns false
        val mockResoler: ContentResolver = mockk<ContentResolver>()
        every { context.contentResolver } returns mockResoler
        val mockProviderClient = mockk<ContentProviderClient>()
        every { mockResoler.acquireUnstableContentProviderClient(globalSearchDFMLoader.getFileUri()) } returns mockProviderClient
        every { mockResoler.acquireUnstableContentProviderClient(globalSearchDFMLoader.getMediaUri()) } returns mockProviderClient
        val mockCursor = mockk<Cursor>()
        val sortOrder = globalSearchDFMLoader.getSortOrder()
        every {
            mockProviderClient.query(any(), any(), any(), null, sortOrder)
        } answers {
            println("query return new mockCursor $mockCursor")
            mockCursor
        }
        every {
            mockCursor.count
        } returns 1
        every {
            mockCursor.moveToNext()
            //第一次调用返回为true，第2次调用吗，返回为true，第三次调用返回weifalse
        } returnsMany listOf(true, true, false)
        val spyFileBean =
            spyk(SearchDFMFileWrapper(mockCursor, globalSearchDFMLoader.getMediaUri()))
        every {
            spyFileBean.checkFileUri(any())
        } returns false andThen false andThen true
        every {
            spyFileBean.checkDmpUri(any())
        } returns true andThen true andThen false
        every {
            spyFileBean.mIsDirectory
        } returns true andThen false andThen false
        every {
            globalSearchDFMLoader.createFromCursor(any(), any())
        } returns spyFileBean
        val searchResultData = GlobalSearchViewModel.SearchResultData()
        searchResultData.listDirectoryIds = mutableSetOf()
        searchResultData.listNameAndTitleIds = mutableSetOf()
        // When
        val normalResultData = globalSearchDFMLoader.handleNormalSearch(searchResultData, true)
        // Then
        Assert.assertEquals(false, normalResultData.listDirectory.isEmpty())
        Assert.assertEquals(false, normalResultData.listFileBean.isEmpty())
    }
}