/*********************************************************************
 ** Copyright  = C 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DFMP2PConflictType
 ** Description : DFM P2P Conflict Type
 ** Version     : 1.0
 ** Date        : 2024/03/09 10:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/03/09       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.categorydfm.dfm

object DFMP2PConflictType {
    /**
     * 手机投屏的协议，以10x开头
     */
    const val MOBILE_TYPE_CODE = 100

    /**
     * pad投屏的协议，以20x开头
     */
    const val PAD_TYPE_CODE = 200

    /**
     * pc投屏的协议，以30x开头
     */
    const val PC_TYPE_CODE = 300

    const val TYPE_RANGE = 100

    /**
     * 手机投屏，自研协议
     */
    const val MOBILE_SELF_STUDY_CONFLICT = 101

    /**
     * 手机投屏，miracast = WFD)协议
     */
    const val MOBILE_WFD_CONFLICT = 102

    /**
     * 手机投屏，googlecast协议
     */
    const val MOBILE_GOOGLECAST_CONFLICT = 103

    /**
     * 手机投屏，乐播协议
     */
    const val MOBILE_HAPPYCAST_CONFLICT = 104

    /**
     * 手机投屏，DLNA协议
     */
    const val MOBILE_DLNA_CONFLICT = 105

    /**
     * pad投屏，一个跨屏镜像pad
     */
    const val PAD_CROSS_SCREEN_MIRROR_CONFLICT = 201

    /**
     * pad投屏，一个pad应用接力
     */
    const val PAD_APP_REPLY_CONFLICT = 202

    /**
     * pc投屏，个位0:一个应用接力
     * 十位1：表示一个主镜像屏
     */
    const val PC_ONE_APP_REPLY_CONFLICT = 310

    /**
     * pc投屏，个位1:一个应用接力
     * 十位1：表示一个主镜像屏
     */
    const val PC_ONE_APP_REPLY_ONE_CONFLICT = 311

    /**
     * pc投屏，个位2:2个应用接力
     * 十位1：表示一个主镜像屏
     */
    const val PC_ONE_APP_REPLY_TWO_CONFLICT = 312

    /**
     * pc投屏，个位0:1个应用接力
     * 十位2：表示一个主镜像屏,一个多开屏
     */
    const val PC_TWO_APP_REPLY_ONE_CONFLICT = 320

    /**
     * pc投屏，个位1:2个应用接力
     * 十位2：表示一个主镜像屏,一个多开屏
     */
    const val PC_TWO_APP_REPLY_TWO_CONFLICT = 321

    /**
     * pc投屏，个位0:1个应用接力
     * 十位3：表示一个主镜像屏,两个多开屏
     */
    const val PC_THREE_APP_REPLY_ONE_CONFLICT = 330

    /**
     * 未知协议冲突
     */
    const val UNDEFINED_CONFLICT = -1

    /**
     * 是否是手机投屏：百位为1表示手机投屏
     */
    @JvmStatic
    fun isMobile(code: Int): Boolean {
        return code / MOBILE_TYPE_CODE == 1
    }

    /**
     * 是否是Pad投屏：百位为2表示pad投屏
     */
    @JvmStatic
    fun isPad(code: Int): Boolean {
        return code in PAD_TYPE_CODE until PAD_TYPE_CODE + TYPE_RANGE
    }

    /**
     * 是否是PC投屏：百位为3表示PC投屏
     */
    @JvmStatic
    fun isPC(code: Int): Boolean {
        return code in PC_TYPE_CODE until PC_TYPE_CODE + TYPE_RANGE
    }
}