package com.oplus.filemanager.preview.core

import android.content.Context
import androidx.appcompat.widget.AppCompatTextView
import androidx.test.platform.app.InstrumentationRegistry
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.view.TextViewSnippet
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertNull

/**
 * IPreviewContainerManager接口的单元测试类
 * 用于测试文件预览容器管理器的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])  // 将SDK版本从28改为29以解决错误
class IPreviewContainerManagerTest {
    
    // 被测试的接口实例
    private lateinit var previewContainerManager: IPreviewContainerManager
    // 模拟的默认容器组件
    private lateinit var mockDefaultContainer: IWidgetDefFileIntroduce
    // 模拟的文件名容器组件
    private lateinit var mockNameContainer: TextViewSnippet
    // 模拟的日期显示组件
    private lateinit var mockDateTv: AppCompatTextView
    // 模拟的文件大小显示组件
    private lateinit var mockSizeTv: AppCompatTextView
    
    /**
     * 测试前的初始化方法
     * 1. 模拟MyApplication的上下文
     * 2. 创建各个模拟组件
     * 3. 创建被测试的IPreviewContainerManager实现实例
     */
    @Before
    fun setUp() {
        // 模拟MyApplication单例
        mockkObject(MyApplication)
        // 设置模拟的应用程序上下文
        every { MyApplication.sAppContext } returns InstrumentationRegistry.getInstrumentation().targetContext
        
        // 创建各个模拟组件
        mockDefaultContainer = mockk(relaxed = true)
        mockNameContainer = mockk(relaxed = true)
        mockDateTv = mockk(relaxed = true)
        mockSizeTv = mockk(relaxed = true)
        
        // 创建被测试的IPreviewContainerManager匿名实现类
        previewContainerManager = object : IPreviewContainerManager {
            override val defaultContainer: IWidgetDefFileIntroduce = mockDefaultContainer
            override val startLoadingTime: Long? = null
            override val mDateTv: AppCompatTextView = mockDateTv
            override val mSizeTv: AppCompatTextView = mockSizeTv
            override val nameContainer: TextViewSnippet = mockNameContainer
            
            override fun showAsLoading() {}
            override fun showAsFileContainer(isDefault: Boolean) {}
        }
    }
    
    /**
     * 测试后的清理方法
     * 解除所有模拟对象的绑定
     */
    @After
    fun tearDown() {
        unmockkAll()
    }
    
    /**
     * 测试fillFileDetailInfo方法在传入有效文件对象时的行为
     * 1. 准备测试数据
     * 2. 调用被测方法
     * 3. 验证各UI组件是否正确更新
     */
    @Test
    fun `test fillFileDetailInfo with valid file bean`() {
        // Given - 准备测试数据
        val testFileBean = BaseFileBean().apply {
            mDisplayName = "test.jpg"
            mSize = 1024L
            mDateModified = 1234567890L
        }
        
        // When - 调用被测方法
        previewContainerManager.fillFileDetailInfo(testFileBean)
        
        // Then - 验证结果
        verify { mockNameContainer.text = "test.jpg" }  // 验证文件名是否正确设置
        verify { mockDateTv.text = any<String>() }  // 验证日期文本是否被设置(不关心具体值)
        verify { mockSizeTv.text = any<String>() }  // 验证大小文本是否被设置(不关心具体值)
    }
    
    /**
     * 测试fillFileDetailInfo方法在传入null文件对象时的行为
     * 验证UI组件不会被更新
     */
    @Test
    fun `test fillFileDetailInfo with null file bean`() {
        // When - 传入null调用被测方法
        previewContainerManager.fillFileDetailInfo(null)
        
        // Then - 验证UI组件没有被更新
        verify(exactly = 0) { mockNameContainer.text = any<String>() }
        verify(exactly = 0) { mockDateTv.text = any<String>() }
        verify(exactly = 0) { mockSizeTv.text = any<String>() }
    }
    
    /**
     * 测试onUpdateUIWhenConfigChange方法的默认实现
     * 验证默认实现不会抛出异常
     */
    @Test
    fun `test onUpdateUIWhenConfigChange does nothing by default`() {
        // Given - 准备模拟的配置列表
        val mockConfigList = mockk<MutableCollection<IUIConfig>>(relaxed = true)
        
        // When - 调用被测方法
        previewContainerManager.onUpdateUIWhenConfigChange(mockConfigList)
        
        // Then - 验证没有抛出异常(默认实现不做任何操作)
    }
    
    /**
     * 测试startLoadingTime属性的可空性
     * 验证默认实现返回null
     */
    @Test
    fun `test startLoadingTime is nullable`() {
        // When - 获取加载时间属性
        val result = previewContainerManager.startLoadingTime
        
        // Then - 验证返回值为null
        assertNull(result)
    }
}