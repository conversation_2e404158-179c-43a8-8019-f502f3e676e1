package shadow;

import android.app.Activity;

//import com.color.app.COUIStatusBarResponseUtil;
import com.coui.appcompat.statusbar.COUIStatusBarResponseUtil;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(COUIStatusBarResponseUtil.class)
public class ShadowColorStatusBarResponseUtil {
    @Implementation
    public void __constructor__(Activity var1) {
    }

    @Implementation
    public void setStatusBarClickListener(COUIStatusBarResponseUtil.StatusBarClickListener var1) {

    }

    @Implementation
    public void onResume() {

    }


    @Implementation
    public void onPause() {

    }
}
