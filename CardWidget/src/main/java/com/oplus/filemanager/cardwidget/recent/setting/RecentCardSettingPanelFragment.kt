/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentCardSettingPanelFragment
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/14 15:25
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.setting

import android.annotation.SuppressLint
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.stringResource
import com.oplus.filemanager.cardwidget.R
import com.oplus.filemanager.cardwidget.label.dialog.OnItemClickListener
import com.oplus.filemanager.cardwidget.recent.data.RecentPreviewItem

class RecentCardSettingPanelFragment : COUIPanelFragment() {

    private var recentCardSettingViewModel: RecentCardSettingViewModel? = null

    private var lastClickTime = 0L

    private var allCategoryRecyclerView: COUIRecyclerView? = null
    private var allListAdapter: RecentListAdapter? = null
    private var isStop = false

    override fun initView(panelView: View?) {
        super.initView(panelView)
        Log.d(TAG, "initView")
        val rootView = LayoutInflater.from(activity).inflate(R.layout.recent_card_setting_layout, null, false)
        (contentView as? ViewGroup)?.addView(rootView)
        initData()
        initContentView(rootView)
        startObserver()
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)?.setPanelBackgroundTintColor(
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackgroundElevatedWithCard)
        )
    }

    private fun initData() {
        val activity = activity ?: return
        recentCardSettingViewModel = ViewModelProvider(activity)[RecentCardSettingViewModel::class.java]
        recentCardSettingViewModel?.loadRecentItems()
    }

    private fun initContentView(view: View) {
        initPanelView(view)
        initToolbar()
        initOutSideViewClickListener()
        initOnBackKeyListener()
        initDismissListener()
        initRecycler()
    }

    private fun initPanelView(view: View) {
        allCategoryRecyclerView = view.findViewById(R.id.all_recycler_view)
    }

    private fun initToolbar() {
        dragView.visibility = View.INVISIBLE
        toolbar = toolbar.apply {
            visibility = View.VISIBLE
            title = context.getString(com.filemanager.common.R.string.label_add_recent_file_title)
            isTitleCenterStyle = true
            inflateMenu(R.menu.menu_lable_setting)
            menu.findItem(R.id.cancel).apply {
                setOnMenuItemClickListener {
                    dismissPanel()
                    true
                }
            }
            menu.findItem(R.id.save).apply {
                setOnMenuItemClickListener {
                    recentCardSettingViewModel?.saveRecentCardMapping()
                    dismissPanel()
                    true
                }
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initOutSideViewClickListener() {
        setOutSideViewOnTouchListener { _, event ->
            if (event.actionMasked == MotionEvent.ACTION_UP) {
                if (System.currentTimeMillis() - lastClickTime > DOUBLE_ACTION_INTERVAL) {
                    Toast.makeText(context, stringResource(com.filemanager.common.R.string.panel_click_outside_view_toast), Toast.LENGTH_SHORT)
                        .show()
                    (parentFragment as? COUIBottomSheetDialogFragment)?.doFeedbackAnimation()
                    lastClickTime = System.currentTimeMillis()
                } else {
                    dismissPanel()
                }
            }
            true
        }
    }

    private fun initOnBackKeyListener() {
        setDialogOnKeyListener { _, keyCode, event ->
            Log.d(TAG, "onKey keyCode:$keyCode event:$event")
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                if (System.currentTimeMillis() - lastClickTime > DOUBLE_ACTION_INTERVAL) {
                    Toast.makeText(context, stringResource(com.filemanager.common.R.string.panel_back_toast), Toast.LENGTH_SHORT).show()
                    (parentFragment as? COUIBottomSheetDialogFragment)?.doFeedbackAnimation()
                    lastClickTime = System.currentTimeMillis()
                } else {
                    dismissPanel()
                }
            }
            true
        }
        PredictiveBackUtils.registerOnBackInvokedCallback(this)
    }

    private fun initDismissListener() {
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)?.setOnDismissListener {
            Log.d(TAG, "Setting dialog dismiss,so activity finish")
            activity?.finish()
        }
    }

    private fun initRecycler() {
        val activity = activity ?: return
        val list = mutableListOf<RecentPreviewItem>()
        allListAdapter = RecentListAdapter(activity, recentCardSettingViewModel?.selectItem?.value, list)
        allListAdapter?.onItemClickListener = object : OnItemClickListener<RecentPreviewItem> {
            override fun onItemClick(view: View, position: Int, data: RecentPreviewItem) {
                recentCardSettingViewModel?.selectRecentItem(data)
            }
        }

        allCategoryRecyclerView?.apply {
            layoutManager = LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
            addItemDecoration(RecentPreviewItemDecoration())
            adapter = allListAdapter
            setOverScrollEnable(false)
        }
    }

    private fun startObserver() {
        recentCardSettingViewModel?.allItems?.observe(viewLifecycleOwner) {
            allListAdapter?.previewItem = recentCardSettingViewModel?.selectItem?.value
            allListAdapter?.setLabelList(it)
        }
    }

    private fun removeObservers() {
        recentCardSettingViewModel?.allItems?.removeObservers(viewLifecycleOwner)
    }

    private fun dismissPanel() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    override fun onStart() {
        super.onStart()
        if (isStop) {
            recentCardSettingViewModel?.loadRecentItems()
        }
        isStop = false
    }

    override fun onStop() {
        super.onStop()
        isStop = true
    }

    override fun onDestroyView() {
        super.onDestroyView()
        removeObservers()
    }

    companion object {
        private const val TAG = "RecentCardSettingPanelFragment"
        const val MAX_COUNT = 4
        private const val SPAN_COUNT = 2
        private const val DOUBLE_ACTION_INTERVAL = 2000L
    }
}