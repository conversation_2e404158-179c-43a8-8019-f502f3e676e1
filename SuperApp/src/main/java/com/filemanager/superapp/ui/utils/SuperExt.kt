/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OWorkUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/2/6 20:33
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/2/6       1.0      create
 ***********************************************************************/
package com.filemanager.superapp.ui.utils

import android.content.Context
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.widget.TextView
import androidx.annotation.StringRes
import com.coui.appcompat.clickablespan.COUIClickableSpan
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean

private const val RTL_SYMBOL = "\u200E"

private const val TAG = "SuperExt"
const val SUPER_APP_QQ_NAME_LOWERCASE = "qq"

fun obtainCategoryItem(
    categoryItems: List<MainCategoryItemsBean>,
    categoryId: Int,
    categoryName: String = ""
): MainCategoryItemsBean? {
    Log.d(TAG, "obtainCategoryItem -> = $categoryId ; categoryName = $categoryName ; ")
    return categoryItems.find {
        if (categoryId == CategoryHelper.CATEGORY_QQ) {
            Log.d(TAG, "obtainCategoryItem -> categoryInfo = $it")
            if (categoryName.isEmpty()) {
                return@find false
            }
            if (categoryName.contains(".")) { // 外部传入的是包名
                return@find it.packageName == categoryName
            }
            if (categoryName.equals(it.name, ignoreCase = true)) { // 外部传入的是应用名称
                return@find true
            }
            if (categoryName.lowercase() == SUPER_APP_QQ_NAME_LOWERCASE) {
                it.packageName == MainCategoryItemsBean.QQ
            } else {
                it.packageName == MainCategoryItemsBean.WECHAT
            }
        } else {
            it.itemType == categoryId
        }
    }
}

fun TextView.hyperlinkText(
    context: Context,
    @StringRes hyperlinkTextId: Int,
    @StringRes all: Int,
    @StringRes extra: Int = -1,
    action: () -> Unit
) {
    val hyperlinkText = context.getString(hyperlinkTextId)
    var textDisplay = context.getString(all, hyperlinkText)
    if (Utils.isRtl()) {
        textDisplay = RTL_SYMBOL + textDisplay + RTL_SYMBOL
    }
    val termsIndex = textDisplay.indexOf(hyperlinkText)
    val termsLength = hyperlinkText.length
    val span = COUIClickableSpan(context)
    span.setStatusBarClickListener {
        action.invoke()
    }
    val spannableStringBuilder = SpannableStringBuilder(textDisplay)
    spannableStringBuilder.setSpan(span, termsIndex, termsIndex + termsLength, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
    if (extra != -1) {
        var extraDisplay = context.getString(extra)
        if (Utils.isRtl()) {
            extraDisplay = RTL_SYMBOL + extraDisplay + RTL_SYMBOL
        }
        spannableStringBuilder.append("\n")
        spannableStringBuilder.append(extraDisplay)
    }
    this.movementMethod = LinkMovementMethod.getInstance()
    this.text = spannableStringBuilder
}

fun TextView.hyperlinkTextWithApp(
    context: Context,
    @StringRes hyperlinkTextId: Int,
    @StringRes all: Int,
    @StringRes app: Int,
    @StringRes extra: Int = -1,
    action: () -> Unit
) {
    val hyperlinkText = context.getString(hyperlinkTextId)
    val appText = context.getString(app)
    /*例子：若没有想要的文件，可在“%1$s”内重新保存，或尝试在%2$s中查找*/
    var textDisplay = context.getString(all, appText, hyperlinkText)
    if (Utils.isRtl()) {
        textDisplay = RTL_SYMBOL + textDisplay + RTL_SYMBOL
    }
    val termsIndex = textDisplay.indexOf(hyperlinkText)
    val termsLength = hyperlinkText.length
    val span = COUIClickableSpan(context)
    span.setStatusBarClickListener {
        action.invoke()
    }
    val spannableStringBuilder = SpannableStringBuilder(textDisplay)
    spannableStringBuilder.setSpan(span, termsIndex, termsIndex + termsLength, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
    if (extra != -1) {
        var extraDisplay = context.getString(extra)
        if (Utils.isRtl()) {
            extraDisplay = RTL_SYMBOL + extraDisplay + RTL_SYMBOL
        }
        spannableStringBuilder.append("\n")
        spannableStringBuilder.append(extraDisplay)
    }
    this.movementMethod = LinkMovementMethod.getInstance()
    this.text = spannableStringBuilder
}