<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android">


    <declare-styleable name="PINImageView">
        <attr name="lineIcon" format="reference" />
        <attr name="circleIcon" format="reference" />
        <attr name="translationX" format="integer" />
    </declare-styleable>

    <style name="ActivityDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/coui_open_slide_enter</item>
        <item name="android:windowExitAnimation">@anim/coui_close_slide_exit</item>
    </style>

    <style name="ActivityDialogAnimation.UpDown">
        <item name="android:windowEnterAnimation">@anim/coui_push_up_enter_activitydialog</item>
        <item name="android:windowExitAnimation">@anim/coui_push_down_exit_activitydialog</item>
    </style>

    <style name="ActivityDialog.UpDown" parent="@style/BaseTheme">
        <item name="android:windowAnimationStyle">@style/ActivityDialogAnimation.UpDown</item>
    </style>

    <style name="BackgroundMaskStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:background">@color/search_view_window_mask</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="StatementAndGuideTheme" parent="Theme.COUI">
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0</item>
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="selectSystemForceDarkType">0</item>
        <item name="android:windowExitAnimation">@anim/coui_fade_out_fast</item>
    </style>

</resources>