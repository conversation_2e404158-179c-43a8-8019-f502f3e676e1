/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : MimeTypeUtil.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/6/6
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/6/6       1      create
 ***********************************************************************/
package com.filemanager.fileoperate

import androidx.annotation.WorkerThread
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import java.io.File
import java.io.FileInputStream
import org.apache.tika.metadata.HttpHeaders
import org.apache.tika.metadata.Metadata
import org.apache.tika.metadata.TikaCoreProperties
import org.apache.tika.mime.MediaType
import org.apache.tika.parser.AutoDetectParser
import org.apache.tika.parser.ParseContext
import org.apache.tika.parser.Parser
import org.xml.sax.helpers.DefaultHandler

object MimeTypeUtil {

    private const val TAG = "MimeTypeUtil"

    /**
     * 获取文件的MimeType
     */
    @Suppress("TooGenericExceptionCaught")
    @WorkerThread
    @JvmStatic
    fun getMimeType(file: File): String? {
        var stream: FileInputStream? = null
        var mimeType = ""
        try {
            val parser = AutoDetectParser()
            parser.parsers = HashMap<MediaType, Parser>()
            val metadata = Metadata()
            metadata.add(TikaCoreProperties.RESOURCE_NAME_KEY, file.name)
            stream = FileInputStream(file)
            parser.parse(stream, DefaultHandler(), metadata, ParseContext())
            mimeType = metadata.get(HttpHeaders.CONTENT_TYPE)
        } catch (e: Throwable) {
            Log.e(TAG, "Exception: ${e.message}", e)
        } finally {
            stream?.close()
        }
        return mapMimeType(mimeType)
    }

    @JvmStatic
    private fun mapMimeType(mimeType: String?): String? {
        if (mimeType.isNullOrEmpty()) return null
        val resultType = when (mimeType) {
            MimeTypeHelper.MimeType.MIMETYPE_X_XMIND, MimeTypeHelper.MimeType.MIMETYPE_FREE_MIND -> MimeTypeHelper.MimeType.MIMETYPE_XMIND
            MimeTypeHelper.MimeType.MIMETYPE_AUTOCAD, MimeTypeHelper.MimeType.MIMETYPE_DXF, MimeTypeHelper.MimeType.MIMETYPE_X_DWG,
            MimeTypeHelper.MimeType.MIMETYPE_X_DXF -> MimeTypeHelper.MimeType.MIMETYPE_DWG
            else -> mimeType
        }
        Log.d(TAG, "mapMimeType mimeType:$mimeType resultType:$resultType")
        return resultType
    }
}