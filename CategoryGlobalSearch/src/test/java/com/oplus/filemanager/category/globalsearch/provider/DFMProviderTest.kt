package com.oplus.filemanager.category.globalsearch.provider

import android.content.ContentValues
import android.database.MatrixCursor
import android.net.Uri
import android.provider.MediaStore
import com.filemanager.common.compat.FeatureCompat
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import com.oplus.filemanager.interfaze.dmpsearch.IExportDmpSearchApi
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * DFMProvider的单元测试类
 * 用于测试DFMProvider的查询功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class DFMProviderTest {

    // 测试类中使用的成员变量
    private lateinit var dfmProvider: DFMProvider // 被测试的DFMProvider实例
    private lateinit var mockDmpSearchApi: IDmpSearchApi // 模拟的DMP搜索API
    private lateinit var mockExportDmpSearchApi: IExportDmpSearchApi // 模拟的导出DMP搜索API
    private lateinit var mockMediaCursor: MatrixCursor // 模拟的媒体库Cursor
    private lateinit var mockDmpCursor: MatrixCursor // 模拟的DMP Cursor

    /**
     * 测试前的准备工作
     * 1. 模拟FeatureCompat类
     * 2. 初始化被测试对象
     * 3. 创建模拟对象
     */
    @Before
    fun setUp() {
        // 模拟FeatureCompat类
        mockkObject(FeatureCompat)
        // 设置模拟返回值，表示不是实验版ROM
        every { FeatureCompat.sIsExpRom } returns false
        
        // 初始化被测试的DFMProvider
        dfmProvider = DFMProvider()
        // 创建模拟的DMP搜索API
        mockDmpSearchApi = mockk(relaxed = true)
        // 创建模拟的导出DMP搜索API
        mockExportDmpSearchApi = mockk(relaxed = true)
        // 创建模拟的媒体库Cursor，使用DFMProvider中定义的媒体库字段
        mockMediaCursor = MatrixCursor(DFMProvider.MEDIA_PROJECT)
        // 创建模拟的DMP Cursor，包含DMP查询所需的所有字段
        mockDmpCursor = MatrixCursor(
            arrayOf(
                "identification",
                "absolutePath",
                "filename",
                "size",
                "lastModified",
                "highlight",
                "type",
                "recallType"
            )
        )

        // 调用被测试对象的onCreate方法进行初始化
        dfmProvider.onCreate()
    }

    /**
     * 测试后的清理工作
     * 解除所有模拟对象的绑定
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试当DMP不应加载时的查询情况
     * 1. 设置模拟返回值为不应加载DMP
     * 2. 执行查询
     * 3. 验证返回结果应为null
     */
    @Test
    fun testQueryDmpWithShouldNotLoad() {
        // 设置模拟返回值，表示不应加载DMP
        every { mockDmpSearchApi.isShouldLoadDMP() } returns false
        // 创建测试URI
        val uri = Uri.parse("content://com.oneplus.filemanager.dmpprovider/dmp")
        // 执行查询
        val result = dfmProvider.query(uri, null, "test", null, null)
        // 验证结果应为null
        assertNull(result)
    }
}