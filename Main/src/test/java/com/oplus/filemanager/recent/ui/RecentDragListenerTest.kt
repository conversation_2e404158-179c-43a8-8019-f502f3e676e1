package com.oplus.filemanager.recent.ui

import android.app.Activity
import android.content.Context
import android.graphics.drawable.Drawable
import android.view.MotionEvent
import android.view.View
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.dragselection.DefaultDropListener
import com.filemanager.common.dragselection.FileDragDropScanner
import com.filemanager.common.dragselection.NewFileDragDropShadow
import com.oplus.dropdrag.dragdrop.DragScanResult
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

/**
 * RecentDragListener的单元测试类
 * 用于测试RecentDragListener类的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RecentDragListenerTest {

    // 测试所需的模拟对象
    private lateinit var activity: Activity
    private lateinit var view: View
    private lateinit var dragHoldDrawable: Drawable
    private lateinit var dragHoldTitle: String
    private lateinit var dragHoldDetail: String
    private lateinit var event: MotionEvent
    private lateinit var viewMode: com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE
    private lateinit var recentDragListener: RecentDragListener

    /**
     * 测试前的初始化方法
     * 创建所有测试所需的模拟对象和测试实例
     */
    @Before
    fun setUp() {
        activity = mockk(relaxed = true)
        view = mockk(relaxed = true)
        dragHoldDrawable = mockk(relaxed = true)
        dragHoldTitle = "Test Title"
        dragHoldDetail = "Test Detail"
        event = mockk(relaxed = true)
        viewMode = mockk(relaxed = true)
        recentDragListener = RecentDragListener(
            activity,
            view,
            dragHoldDrawable,
            dragHoldTitle,
            dragHoldDetail,
            event,
            viewMode
        )
    }

    /**
     * 测试addSelectedView方法 - 当所有视图都不可见时
     * 验证方法是否能正确处理不可见视图的情况
     */
    @Test
    fun `test addSelectedView with all invisible views`() {
        // 创建一个不可见的模拟视图
        val view1 = mockk<View> {
            every { isShown } returns false
            every { getVisibility() } returns View.INVISIBLE
        }
        val inputList = arrayListOf(view1)

        // 调用测试方法
        val result = recentDragListener.addSelectedView(inputList)

        // 验证结果
        assertEquals(recentDragListener, result)
        assertEquals(0, recentDragListener.selectedViewList.value?.size)
        assertEquals(null, recentDragListener.selectedViewList.value?.get(0))
        assertNull(DefaultDropListener.selectedItemViews?.value)
    }

    /**
     * 测试addSelectedView方法 - 当传入null列表时
     * 验证方法是否能正确处理null输入
     */
    @Test
    fun `test addSelectedView with null list`() {
        // 调用测试方法并传入null
        val result = recentDragListener.addSelectedView(null)

        // 验证结果
        assertEquals(recentDragListener, result)
        assertNull(recentDragListener.selectedViewList.value)
        assertNull(DefaultDropListener.selectedItemViews?.value)
    }

    /**
     * 测试updateShaderUI方法 - 当状态码为STATUS_LIST时
     * 验证方法是否能正确更新阴影UI
     */
    @Test
    fun `test updateShaderUI with STATUS_LIST`() {
        // 创建模拟对象
        val shaderBuilder = mockk<NewFileDragDropShadow>(relaxed = true)
        val result = mockk<DragScanResult> {
            every { statusCode } returns FileDragDropScanner.STATUS_LIST
            every { itemCount } returns 5
        }
        val context = mockk<Context>(relaxed = true)

        // 调用测试方法
        recentDragListener.updateShaderUI(shaderBuilder, result, context)

        // 验证是否调用了正确的方法
        verify {
            shaderBuilder.updateShadowRedDot(5)
        }
    }

    /**
     * 测试updateShaderUI方法 - 当状态码为STATUS_RECENT_IMG时
     * 验证方法是否能正确更新阴影UI
     */
    @Test
    fun `test updateShaderUI with STATUS_RECENT_IMG`() {
        // 创建模拟对象
        val shaderBuilder = mockk<NewFileDragDropShadow>(relaxed = true)
        val result = mockk<DragScanResult> {
            every { statusCode } returns FileDragDropScanner.STATUS_RECENT_IMG
            every { itemCount } returns 3
        }
        val context = mockk<Context>(relaxed = true)

        // 调用测试方法
        recentDragListener.updateShaderUI(shaderBuilder, result, context)

        // 验证是否调用了正确的方法
        verify {
            shaderBuilder.updateShadowRedDot(3)
        }
    }

    /**
     * 测试updateShaderUI方法 - 当状态码为STATUS_RECENT_SINGLE_IMG时
     * 验证方法是否能正确更新阴影UI
     */
    @Test
    fun `test updateShaderUI with STATUS_RECENT_SINGLE_IMG`() {
        // 创建模拟对象
        val shaderBuilder = mockk<NewFileDragDropShadow>(relaxed = true)
        val result = mockk<DragScanResult> {
            every { statusCode } returns FileDragDropScanner.STATUS_RECENT_SINGLE_IMG
            every { itemCount } returns 1
        }
        val context = mockk<Context>(relaxed = true)

        // 调用测试方法
        recentDragListener.updateShaderUI(shaderBuilder, result, context)

        // 验证是否调用了正确的方法
        verify {
            shaderBuilder.updateShadowRedDot(1)
        }
    }

    /**
     * 测试updateShaderUI方法 - 当状态码未知时
     * 验证方法是否能正确处理未知状态码
     */
    @Test
    fun `test updateShaderUI with unknown status code`() {
        // 创建模拟对象，使用未知状态码
        val shaderBuilder = mockk<NewFileDragDropShadow>(relaxed = true)
        val result = mockk<DragScanResult> {
            every { statusCode } returns 999
            every { itemCount } returns 0
        }
        val context = mockk<Context>(relaxed = true)

        // 调用测试方法
        recentDragListener.updateShaderUI(shaderBuilder, result, context)

        // 验证只调用了updateShadowRedDot方法
        verify(exactly = 1) {
            shaderBuilder.updateShadowRedDot(0)
        }
    }
}