package com.oplus.filemanager.utils

import android.content.Context
import android.os.Looper
import com.filemanager.common.compat.PropertyCompat
import com.oplus.filemanager.carrier.CarrierEntity
import io.mockk.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import java.io.ByteArrayInputStream
import java.io.InputStream
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * CarrierUtils 的单元测试类
 * 用于测试 CarrierUtils 工具类的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class CarrierUtilsTest {

    private lateinit var context: Context
    private val testDispatcher = StandardTestDispatcher()

    /**
     * 测试前的初始化方法
     * 1. 设置主调度器为测试调度器
     * 2. 创建模拟的 Context 对象
     * 3. 模拟 PropertyCompat、FileUtils 和 TextUtils 对象
     */
    @Before
    fun setUp() {
        Dispatchers.setMain(testDispatcher)
        context = mockk()
        mockkObject(PropertyCompat)
        mockkObject(FileUtils)
        mockkObject(TextUtils)
    }

    /**
     * 测试后的清理方法
     * 1. 清除所有模拟对象
     * 2. 重置主调度器
     * 3. 重置 CarrierUtils 的单例状态
     */
    @After
    fun tearDown() {
        clearAllMocks()
        Dispatchers.resetMain()
        // 通过反射重置单例状态
        try {
            val field = CarrierUtils::class.java.getDeclaredField("isSupport")
            field.isAccessible = true
            field.set(null, null)
        } catch (e: Exception) {
            // 忽略字段不存在或其他反射问题
        }
    }

    /**
     * 测试 isCarrierSupport 方法
     * 当 channelName 和 channelCountry 匹配时应该返回 true
     */
    @Test
    fun `isCarrierSupport with list should return true when channelName and channelCountry match`() {
        val list = listOf(
            CarrierEntity(
                channelName = listOf("testChannel"),
                regionCode = listOf("testCountry"),
                isSupport = true
            )
        )

        val result = CarrierUtils.isCarrierSupport(list, "testChannel", "testCountry", "", "")

        assertTrue(result)
    }

    /**
     * 测试 isCarrierSupport 方法
     * 当 channelName 和 channelCountry 匹配但 isSupport 为 false 时应该返回 false
     */
    @Test
    fun `isCarrierSupport with list should return false when channelName and channelCountry match but isSupport is false`() {
        val list = listOf(
            CarrierEntity(
                channelName = listOf("testChannel"),
                regionCode = listOf("testCountry"),
                isSupport = false
            )
        )

        val result = CarrierUtils.isCarrierSupport(list, "testChannel", "testCountry", "", "")

        assertFalse(result)
    }

    /**
     * 测试 isCarrierSupport 方法
     * 当 carrier 匹配时应该返回 true
     */
    @Test
    fun `isCarrierSupport with list should return true when carrier matches`() {
        val list = listOf(
            CarrierEntity(
                pipelineKey = listOf("testCarrier"),
                isSupport = true
            )
        )

        val result = CarrierUtils.isCarrierSupport(list, "", "", "testCarrier", "")

        assertTrue(result)
    }

    /**
     * 测试 isCarrierSupport 方法
     * 当 carrier 匹配但 isSupport 为 false 时应该返回 false
     */
    @Test
    fun `isCarrierSupport with list should return false when carrier matches but isSupport is false`() {
        val list = listOf(
            CarrierEntity(
                pipelineKey = listOf("testCarrier"),
                isSupport = false
            )
        )

        val result = CarrierUtils.isCarrierSupport(list, "", "", "testCarrier", "")

        assertFalse(result)
    }

    /**
     * 测试 isCarrierSupport 方法
     * 当 pipelineRegion 匹配时应该返回 true
     */
    @Test
    fun `isCarrierSupport with list should return true when pipelineRegion matches`() {
        val list = listOf(
            CarrierEntity(
                regionCode = listOf("testRegion"),
                isSupport = true
            )
        )

        val result = CarrierUtils.isCarrierSupport(list, "", "", "", "testRegion")

        assertTrue(result)
    }

    /**
     * 测试 isCarrierSupport 方法
     * 当 pipelineRegion 匹配但 isSupport 为 false 时应该返回 false
     */
    @Test
    fun `isCarrierSupport with list should return false when pipelineRegion matches but isSupport is false`() {
        val list = listOf(
            CarrierEntity(
                regionCode = listOf("testRegion"),
                isSupport = false
            )
        )

        val result = CarrierUtils.isCarrierSupport(list, "", "", "", "testRegion")

        assertFalse(result)
    }

    /**
     * 测试 isCarrierSupport 方法
     * 当所有输入参数为 null 或空时应该返回 true
     */
    @Test
    fun `isCarrierSupport with list should return true when all inputs are null or empty`() {
        val list = listOf<CarrierEntity>()

        val result = CarrierUtils.isCarrierSupport(list, "", "", "", "")

        assertTrue(result)
    }

    /**
     * 模拟 parseJson 方法的返回结果
     * @param returnValue 要返回的 CarrierEntity 列表
     */
    private fun mockParseJson(returnValue: List<CarrierEntity>) {
        val json = "[]"
        every { context.assets.open("carrier.json") } returns ByteArrayInputStream(json.toByteArray())
        every { FileUtils.read(any<InputStream>()) } returns json
        every { TextUtils.fromJson(json) } returns returnValue
    }
    
    /**
     * 为缓存测试模拟 parseJson 方法的返回结果
     * 创建一个可以多次读取的 InputStream
     * @param returnValue 要返回的 CarrierEntity 列表
     */
    private fun mockParseJsonForCacheTest(returnValue: List<CarrierEntity>) {
        val json = "[]"
        // 创建一个可以多次读取的 InputStream
        every { context.assets.open("carrier.json") } answers {
            ByteArrayInputStream(json.toByteArray())
        }
        every { FileUtils.read(any<InputStream>()) } returns json
        every { TextUtils.fromJson(json) } returns returnValue
    }
}