package com.oplus.filemanager.categorydfm.ui

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.Observer
import com.filemanager.common.MyApplication
import com.filemanager.common.base.DFMMediaFile
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.categorydfm.dfm.DFMMediaHelper
import com.oplus.filemanager.categorydfm.dfm.DFMUriLoader
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File

/**
 * DFMParentViewModel的单元测试类
 * 用于测试DFMParentViewModel的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class DFMParentViewModelTest {

    /**
     * 用于确保LiveData的更新是同步执行的规则
     */
    @get:Rule
    val instantExecutorRule = InstantTaskExecutorRule()

    // 测试对象
    private lateinit var viewModel: DFMParentViewModel
    // 模拟的Application上下文
    private val context = mockk<Application>(relaxed = true)
    // 用于观察showRecycleView LiveData的模拟Observer
    private val showRecycleViewObserver = mockk<Observer<Boolean>>(relaxed = true)
    // 用于观察loadResult LiveData的模拟Observer
    private val loadResultObserver = mockk<Observer<Boolean>>(relaxed = true)

    /**
     * 测试前的准备工作
     * 1. 模拟各种依赖对象
     * 2. 初始化ViewModel
     * 3. 设置LiveData的观察者
     */
    @Before
    fun setUp() {
        // 模拟ConfigSharedPreferenceUtils单例
        mockkObject(ConfigSharedPreferenceUtils)
        // 模拟SortRecordModeFactory单例
        mockkObject(SortRecordModeFactory)
        // 模拟Injector单例
        mockkObject(Injector)
        // 模拟MyApplication单例
        mockkObject(MyApplication)
        // 设置MyApplication的appContext返回模拟的context
        every { MyApplication.appContext } returns context
        // 设置context的applicationContext返回自身
        every { context.applicationContext } returns context
        // 初始化ViewModel
        viewModel = DFMParentViewModel()
        // 为showRecycleView LiveData添加观察者
        viewModel.showRecycleView.observeForever(showRecycleViewObserver)
        // 为loadResult LiveData添加观察者
        viewModel.loadResult.observeForever(loadResultObserver)
    }

    /**
     * 测试后的清理工作
     * 1. 移除LiveData的观察者
     * 2. 解除所有模拟对象
     */
    @After
    fun tearDown() {
        // 移除showRecycleView的观察者
        viewModel.showRecycleView.removeObserver(showRecycleViewObserver)
        // 移除loadResult的观察者
        viewModel.loadResult.removeObserver(loadResultObserver)
        // 解除所有模拟对象
        unmockkAll()
    }

    /**
     * 测试getScanMode方法
     * 当SharedPreferences中存储的值为0时，应返回SCAN_MODE_LIST
     */
    @Test
    fun `getScanMode should return SCAN_MODE_LIST when preference is 0`() {
        // 模拟ConfigSharedPreferenceUtils.getInt返回0
        every { ConfigSharedPreferenceUtils.getInt(any(), any()) } returns 0

        // 调用被测方法
        val result = viewModel.getScanMode()

        // 验证结果应为SCAN_MODE_LIST
        assert(result == KtConstants.SCAN_MODE_LIST)
    }

    /**
     * 测试getScanMode方法
     * 当SharedPreferences中存储的值不为0时，应返回存储的值
     */
    @Test
    fun `getScanMode should return stored value when preference is not 0`() {
        // 模拟ConfigSharedPreferenceUtils.getInt返回SCAN_MODE_GRID
        every { ConfigSharedPreferenceUtils.getInt(any(), any()) } returns KtConstants.SCAN_MODE_GRID

        // 调用被测方法
        val result = viewModel.getScanMode()

        // 验证结果应为SCAN_MODE_GRID
        assert(result == KtConstants.SCAN_MODE_GRID)
    }

    /**
     * 测试clickScanModeItem方法
     * 当当前模式为LIST时，点击后应切换为GRID模式
     */
    @Test
    fun `clickScanModeItem should switch from LIST to GRID mode`() {
        // 模拟ConfigSharedPreferenceUtils.putInt方法
        every { ConfigSharedPreferenceUtils.putInt(any(), any()) } just runs
        // 设置当前模式为LIST
        viewModel.browseModeState.value = KtConstants.SCAN_MODE_LIST

        // 调用被测方法
        viewModel.clickScanModeItem(context)

        // 验证模式已切换为GRID
        assert(viewModel.browseModeState.value == KtConstants.SCAN_MODE_GRID)
        // 验证SharedPreferences已保存GRID模式
        verify { ConfigSharedPreferenceUtils.putInt(any(), KtConstants.SCAN_MODE_GRID) }
    }

    /**
     * 测试clickScanModeItem方法
     * 当当前模式为GRID时，点击后应切换为LIST模式
     */
    @Test
    fun `clickScanModeItem should switch from GRID to LIST mode`() {
        // 模拟ConfigSharedPreferenceUtils.putInt方法
        every { ConfigSharedPreferenceUtils.putInt(any(), any()) } just runs
        // 设置当前模式为GRID
        viewModel.browseModeState.value = KtConstants.SCAN_MODE_GRID

        // 调用被测方法
        viewModel.clickScanModeItem(context)

        // 验证模式已切换为LIST
        assert(viewModel.browseModeState.value == KtConstants.SCAN_MODE_LIST)
        // 验证SharedPreferences已保存LIST模式
        verify { ConfigSharedPreferenceUtils.putInt(any(), KtConstants.SCAN_MODE_LIST) }
    }

    /**
     * 测试loadData方法
     * 应调用LoaderController的initLoader方法
     */
    @Test
    fun `loadData should call loader callback`() {
        // 模拟LoaderController
        val loaderController = mockk<LoaderController>(relaxed = true)
        // 用于捕获LoaderCallback的插槽
        val loaderCallbackSlot = slot<DFMParentViewModel.DFMLoaderCallback>()
        // 模拟initLoader方法
        every { loaderController.initLoader<DFMUriLoader.DFMUriResult>(any(), capture(loaderCallbackSlot)) } just runs

        // 调用被测方法
        viewModel.loadData(loaderController)

        // 验证initLoader方法被调用
        verify { loaderController.initLoader(0, any<DFMParentViewModel.DFMLoaderCallback>()) }
    }

    /**
     * 测试forcedReload方法
     * 当forceReload为false时，应将其设置为true
     */
    @Test
    fun `forcedReload should set forceReload to true when false`() {
        // 设置初始值为false
        viewModel.forceReload.value = false

        // 调用被测方法
        viewModel.forcedReload()

        // 验证值已变为true
        assert(viewModel.forceReload.value == true)
    }

    /**
     * 测试forcedReload方法
     * 当forceReload已经为true时，不应改变其值
     */
    @Test
    fun `forcedReload should not change forceReload when already true`() {
        // 设置初始值为true
        viewModel.forceReload.value = true

        // 调用被测方法
        viewModel.forcedReload()

        // 验证值仍为true
        assert(viewModel.forceReload.value == true)
        // 不需要验证赋值操作，因为当值已经为true时，方法会直接返回
    }

    /**
     * 测试resetForcedReload方法
     * 应将forceReload设置为false
     */
    @Test
    fun `resetForcedReload should set forceReload to false`() {
        // 设置初始值为true
        viewModel.forceReload.value = true

        // 调用被测方法
        viewModel.resetForcedReload()

        // 验证值已变为false
        assert(viewModel.forceReload.value == false)
    }

    /**
     * 测试setAllData方法
     * 应更新allData和isPartialData
     */
    @Test
    fun `setAllData should update allData and isPartialData`() {
        // 模拟两个DFMMediaFile对象
        val files = listOf(mockk<DFMMediaFile>(), mockk<DFMMediaFile>())

        // 调用被测方法，设置partialData为true
        viewModel.setAllData(files, true)

        // 验证isPartialData已更新为true
        assert(viewModel.isPartialData == true)
    }

    /**
     * 测试filterImage方法
     * 对于image类型文件应返回true
     */
    @Test
    fun `filterImage should return true for image type`() {
        // 模拟DFMMediaFile对象
        val file = mockk<DFMMediaFile>()
        // 设置文件类型为IMAGE_TYPE
        every { file.mLocalType } returns MimeTypeHelper.IMAGE_TYPE
        // 设置MIME类型为image/jpeg
        every { file.mMimeType } returns "image/jpeg"
        every { file.mData } returns "test_data"

        // 通过反射获取filterImage方法
        val filterMethod = DFMParentViewModel::class.java.getDeclaredMethod("filterImage", DFMMediaFile::class.java)
        filterMethod.isAccessible = true
        // 调用方法并获取结果
        val result = filterMethod.invoke(viewModel, file) as Boolean

        // 验证结果为true
        assert(result == true)
    }

    /**
     * 测试filterImage方法
     * 对于被忽略的MIME类型应返回false
     */
    @Test
    fun `filterImage should return false for ignored mime types`() {
        // 模拟DFMMediaFile对象
        val file = mockk<DFMMediaFile>()
        // 设置文件类型为非图像类型
        every { file.mLocalType } returns 0
        // 设置MIME类型为被忽略的类型
        every { file.mMimeType } returns "image/x-photoshop"
        every { file.mData } returns "test_data"

        // 通过反射获取filterImage方法
        val filterMethod = DFMParentViewModel::class.java.getDeclaredMethod("filterImage", DFMMediaFile::class.java)
        filterMethod.isAccessible = true
        // 调用方法并获取结果
        val result = filterMethod.invoke(viewModel, file) as Boolean

        // 验证结果为false
        assert(result == false)
    }

    /**
     * 测试filterVideo方法
     * 对于video类型文件应返回true
     */
    @Test
    fun `filterVideo should return true for video type`() {
        // 模拟DFMMediaFile对象
        val file = mockk<DFMMediaFile>()
        // 设置文件类型为VIDEO_TYPE
        every { file.mLocalType } returns MimeTypeHelper.VIDEO_TYPE
        // 设置MIME类型为video/mp4
        every { file.mMimeType } returns "video/mp4"
        every { file.mData } returns "test_data"

        // 通过反射获取filterVideo方法
        val filterMethod = DFMParentViewModel::class.java.getDeclaredMethod("filterVideo", DFMMediaFile::class.java)
        filterMethod.isAccessible = true
        // 调用方法并获取结果
        val result = filterMethod.invoke(viewModel, file) as Boolean

        // 验证结果为true
        assert(result == true)
    }

    /**
     * 测试filterAudio方法
     * 对于audio类型文件应返回true
     */
    @Test
    fun `filterAudio should return true for audio type`() {
        // 模拟DFMMediaFile对象
        val file = mockk<DFMMediaFile>()
        // 设置文件类型为AUDIO_TYPE
        every { file.mLocalType } returns MimeTypeHelper.AUDIO_TYPE
        // 设置MIME类型为audio/mp3
        every { file.mMimeType } returns "audio/mp3"
        every { file.mData } returns "test_data"

        // 通过反射获取filterAudio方法
        val filterMethod = DFMParentViewModel::class.java.getDeclaredMethod("filterAudio", DFMMediaFile::class.java)
        filterMethod.isAccessible = true
        // 调用方法并获取结果
        val result = filterMethod.invoke(viewModel, file) as Boolean

        // 验证结果为true
        assert(result == true)
    }

    /**
     * 测试filterDocument方法
     * 对于document类型文件应返回true
     */
    @Test
    fun `filterDocument should return true for document types`() {
        // 模拟DFMMediaFile对象
        val file = mockk<DFMMediaFile>()
        // 设置文件类型为TXT_TYPE(文档类型)
        every { file.mLocalType } returns MimeTypeHelper.TXT_TYPE
        // 设置MIME类型为text/plain
        every { file.mMimeType } returns "text/plain"
        every { file.mData } returns "test_data"

        // 通过反射获取filterDocument方法
        val filterMethod = DFMParentViewModel::class.java.getDeclaredMethod("filterDocument", DFMMediaFile::class.java)
        filterMethod.isAccessible = true
        // 调用方法并获取结果
        val result = filterMethod.invoke(viewModel, file) as Boolean

        // 验证结果为true
        assert(result == true)
    }

    /**
     * 测试filterApk方法
     * 对于application类型文件应返回true
     */
    @Test
    fun `filterApk should return true for application type`() {
        // 模拟DFMMediaFile对象
        val file = mockk<DFMMediaFile>()
        // 设置文件类型为APPLICATION_TYPE
        every { file.mLocalType } returns MimeTypeHelper.APPLICATION_TYPE
        every { file.mData } returns "test_data"

        // 通过反射获取filterApk方法
        val filterMethod = DFMParentViewModel::class.java.getDeclaredMethod("filterApk", DFMMediaFile::class.java)
        filterMethod.isAccessible = true
        // 调用方法并获取结果
        val result = filterMethod.invoke(viewModel, file) as Boolean

        // 验证结果为true
        assert(result == true)
    }

    /**
     * 测试filterCompress方法
     * 对于compressed类型文件应返回true
     */
    @Test
    fun `filterCompress should return true for compressed types`() {
        // 模拟DFMMediaFile对象
        val file = mockk<DFMMediaFile>()
        // 设置文件类型为COMPRESSED_TYPE
        every { file.mLocalType } returns MimeTypeHelper.COMPRESSED_TYPE
        every { file.mData } returns "test_data"

        // 通过反射获取filterCompress方法
        val filterMethod = DFMParentViewModel::class.java.getDeclaredMethod("filterCompress", DFMMediaFile::class.java)
        filterMethod.isAccessible = true
        // 调用方法并获取结果
        val result = filterMethod.invoke(viewModel, file) as Boolean

        // 验证结果为true
        assert(result == true)
    }
}