/***************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * Description:Unit test
 * Version: 1.0
 * Date : 2019/7/22
 * <EMAIL>
 *
 * -------------------Revision History: ----------------------
 *     <author>    <data>     <version >     <desc>
 *   lanjiabo    2019/7/22   1.0            build this moudle
 ****************************************************************/
package shadow;

import com.filemanager.common.constants.Constants;
import com.filemanager.common.utils.Log;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(Log.class)
public class ShadowLog {
    private static final String TAG = "ShadowLog";

    private static int sLevel = Constants.LOG_LEVEL;
    @Implementation
    public static void println(int priority, String tag, String msg) {
        if (priority >= sLevel) {
            android.util.Log.println(priority, tag, msg);
            System.out.println(android.util.Log.println(priority, tag, msg));
        }
    }
    @Implementation
    public static void v(String tag, String msg) {
        if (sLevel <= android.util.Log.VERBOSE) {
            android.util.Log.v(tag, msg);
            System.out.println(android.util.Log.v(tag, msg));
        }
    }
    @Implementation
    public static void v(String msg) {
        if (sLevel <= android.util.Log.VERBOSE) {
            android.util.Log.v(TAG, msg);
        }
    }
    @Implementation
    public static void d(String tag, String msg) {
        if (sLevel <= android.util.Log.DEBUG) {
            android.util.Log.d(tag, msg);
        }
    }
    @Implementation
    public static void d(String msg) {
        if (sLevel <= android.util.Log.DEBUG) {
            android.util.Log.d(TAG, msg);
        }
    }
    @Implementation
    public static void i(String tag, String msg) {
        if (sLevel <= android.util.Log.INFO) {
            android.util.Log.i(tag, msg);
        }
    }
    @Implementation
    public static void i(String msg) {
        if (sLevel <= android.util.Log.INFO) {
            android.util.Log.i(TAG, msg);
        }
    }
    @Implementation
    public static void w(String tag, String msg) {
        if (sLevel <= android.util.Log.WARN) {
            android.util.Log.w(tag, msg);
        }
    }
    @Implementation
    public static void w(String msg) {
        if (sLevel <= android.util.Log.WARN) {
            android.util.Log.w(TAG, msg);
        }
    }
    @Implementation
    public static void w(String tag, String msg, Throwable ex) {
        if (sLevel <= android.util.Log.WARN) {
            android.util.Log.w(tag, msg, ex);
        }
    }
    @Implementation
    public static void e(String tag, String msg) {
        if (sLevel <= android.util.Log.ERROR) {
            android.util.Log.e(tag, msg);
        }
    }
    @Implementation
    public static void e(String msg) {
        if (sLevel <= android.util.Log.ERROR) {
            android.util.Log.e(TAG, msg);
        }
    }
    @Implementation
    public static void e(String msg, Throwable ex) {
        if (sLevel <= android.util.Log.ERROR) {
            android.util.Log.e(TAG, msg, ex);
        }
    }
    @Implementation
    public static void e(String tag, String msg, Throwable ex) {
        if (sLevel <= android.util.Log.ERROR) {
            android.util.Log.e(tag, msg, ex);
        }
    }
}
