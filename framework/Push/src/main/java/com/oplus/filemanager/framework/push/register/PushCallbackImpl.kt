/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: PushCallbackImpl.kt
 ** Description: push 回调的实现类
 ** Version: 1.0
 ** Date : 2025/04/09
 ** Author: chao.xue
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 *  chao.xue 2025/04/09   1.0    create
 ****************************************************************/
package com.oplus.filemanager.framework.push.register

import com.filemanager.common.utils.Log
import com.heytap.msp.push.callback.ICallBackResultService
import com.heytap.msp.push.mode.ErrorCode
import com.oplus.filemanager.framework.push.di.PushApi

class PushCallbackImpl : ICallBackResultService {

    companion object {
        private const val TAG = "PushCallbackImpl"

        /**
         * 回调结果的成功code
         * 错误码定义：https://open.oppomobile.com/new/developmentDoc/info?id=11224
         */
        private const val RESP_CODE_SUCCESS = 0

        /**
         * 当前的push状态
         */
        const val PUSH_STATUS_START: Int = 0
        const val PUSH_STATUS_PAUSE: Int = 1
        const val PUSH_STATUS_STOP: Int = 2

        /**
         * 通知状态
         */
        const val NOTIFY_STATUS_OPEN: Int = 0
        const val NOTIFY_STATUS_CLOSE: Int = 1

        /**
         * 所有回调都需要根据responseCode来判断操作是否成功，0 代表成功,其他代码失败，失败具体原因可以查阅附录中的错误码列表。
         */
        fun isSuccess(code: Int): Boolean {
            return ErrorCode.SUCCESS == code
        }
    }

    /**
     * 注册的结果,如果注册成功,registerID就是客户端的唯一身份标识
     *
     * @param responseCode 接口执行结果码，0表示接口执行成功
     * @param registerID   注册id/token
     * @param packageName 如果当前执行注册的应用是常规应用，则通过packageName返回当前应用对应的包名
     * @param miniPackageName  如果当前是快应用进行push registerID的注冊，则通过miniPackageName进行标识快应用包名
     */
    override fun onRegister(responseCode: Int, registerID: String?, packageName: String?, miniPackageName: String?) {
        Log.w(TAG, "onRegister respCode:$responseCode registerID:$registerID pkg:$packageName")
        PushApi.registed = true
    }


    /**
     * 应用注销结果回调接口，将应用请求服务端的注销接口进行结果传达
     * @param responseCode 接口执行结果码，0表示接口执行成功
     * @param packageName  当前注销的应用的包名
     * @param miniPackageName  如果是快应用注销，则会将快应用的包名一起返回给业务方(一般是快应用中心，由快应用中心进行分发)
     */
    override fun onUnRegister(responseCode: Int, packageName: String?, miniPackageName: String?) {
        Log.w(TAG, "onUnRegister respCode:$responseCode")
        PushApi.registed = false
    }

    /**
     * 获取设置推送时间的执行结果
     */
    override fun onSetPushTime(responseCode: Int, pushTime: String?) {
        Log.w(TAG, "onSetPushTime respCode:$responseCode pushTime:$pushTime")
    }

    /**
     * 获取当前的push状态返回,根据返回码判断当前的push状态,返回码具体含义可以参考
     */
    override fun onGetPushStatus(responseCode: Int, status: Int) {
        Log.w(TAG, "onGetPushStatus respCode:$responseCode status:$status")
    }

    /**
     * 获取当前通知状态
     */
    override fun onGetNotificationStatus(responseCode: Int, status: Int) {
        Log.w(TAG, "onGetNotificationStatus respCode:$responseCode status:$status")
    }


    /**
     * 异常处理的回调
     * @param errorCode   错误码
     * @param message     错误信息
     * @param packageName 当前注册失败的应用包名，如果是应用注册，则返回应用注册包名，如果是为快应用做接口请求，则这里返回的是快应用中心的包名
     * @param miniProgramPkg 当前注册失败的快应用包名
     */
    override fun onError(errorCode: Int, message: String?, packageName: String?, miniProgramPkg: String?) {
        Log.d(TAG, "onError code:$errorCode message:$message")
    }
}