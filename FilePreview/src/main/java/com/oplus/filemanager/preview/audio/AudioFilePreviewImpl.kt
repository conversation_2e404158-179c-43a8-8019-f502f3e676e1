/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AudioFilePreviewImpl.kt
 * Description:
 *     The implementation to play audio to preview it.
 *
 * Version: 1.0
 * Date: 2024-10-09
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-10-09   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.audio

import android.content.Context
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayErrorListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayPreparedListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayProgressUpdateListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayStateChangedListener
import com.oplus.filemanager.preview.core.AbsPreviewLoadingScheduler
import com.oplus.filemanager.preview.core.IPreviewContainerManager
import com.oplus.filemanager.preview.utils.MediaPlayerHelper
import com.oplus.filemanager.preview.utils.getFileUri
import com.oplus.filemanager.preview.widget.PreviewAudioCard
import com.oplus.filemanager.preview.widget.PreviewAudioCard.OnClickPlayButtonListener
import com.oplus.filemanager.preview.widget.PreviewAudioCard.OnSeekPlayProgressListener

internal class AudioFilePreviewImpl(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner,
    private val previewModel: AudioPreviewViewModel
) : IAudioFilePreview, OnPlayPreparedListener, OnPlayProgressUpdateListener,
    OnSeekPlayProgressListener, OnClickPlayButtonListener, OnPlayErrorListener,
    OnPlayStateChangedListener, DefaultLifecycleObserver {

    private companion object {
        private const val TAG = "AudioFilePreviewImpl"
    }

    private val loadingScheduler = PreviewLoadingScheduler()
    private val onAudioFileLoaded = Observer<BaseFileBean?> { playAudioByPlayer(it) }
    private val onAudioMetadataLoaded = Observer<AudioFileMetadata?> { updateAudioCard(it) }

    private var audioContainer: PreviewAudioCard? = null
    private var containerManager: IPreviewContainerManager? = null
    private var isPlayingWhenUiOnPause = false

    override fun release() {
        previewModel.releaseLoader(onAudioFileLoaded)
        previewModel.releaseAudioMetadataLoader(onAudioMetadataLoaded)
        loadingScheduler.resetLoadingScheduler()
        audioContainer?.release()
        audioContainer = null
        containerManager = null
        lifecycleOwner.lifecycle.removeObserver(this)
    }

    override fun attachToContainer(
        audioContainer: PreviewAudioCard,
        containerManager: IPreviewContainerManager
    ) {
        lifecycleOwner.lifecycle.addObserver(this)
        this.audioContainer = audioContainer
        this.containerManager = containerManager
        loadingScheduler.requireToShowLoading()
        containerManager.defaultContainer.setFileIcon(
            KtThumbnailHelper.getClassifyResId(MimeTypeHelper.AUDIO_TYPE)
        )
        audioContainer.setOnClickPlayButtonListener(this)
        audioContainer.setOnSeekPlayProgressListener(this)
        previewModel.loadAudioMetadata(lifecycleOwner, onAudioMetadataLoaded)
        val player = previewModel.obtainAudioPlayer(context)
            .setOnPlayPreparedListener(this)
            .setOnPlayStateChangedListener(this)
            .setOnPlayProgressUpdateListener(this)
            .setOnPlayErrorListener(this)
        previewModel.loadPreviewFile(lifecycleOwner, onAudioFileLoaded)
        if (player.isPlaying()) {
            onPrepared(true)
        }
    }

    private fun playAudioByPlayer(audioFile: BaseFileBean?) {
        audioContainer?.setAudioFileName(audioFile?.mDisplayName)
        containerManager?.defaultContainer?.setFileName(audioFile?.mDisplayName)
        containerManager?.fillFileDetailInfo(audioFile)
        val player = previewModel.obtainAudioPlayer(context)
        val uri = audioFile?.getFileUri() ?: kotlin.run {
            Log.e(TAG, "playAudioByPlayer: ERROR! No video file to play")
            return
        }
        player.setPrepareTimeout(MediaPlayerHelper.getPrepareTimeout(audioFile)).prepareAndPlay(uri)
    }

    private fun updateAudioCard(audioMetadata: AudioFileMetadata?) {
        val audioCard = audioContainer ?: return
        audioCard.setAudioArtist(audioMetadata?.artist)
        audioCard.setAudioCover(audioMetadata?.coverImage)
    }

    override fun onPrepared(isPlayStarted: Boolean) {
        val playBar = audioContainer ?: return
        loadingScheduler.dismissLoading(PreparedResult(false, isPlayStarted))
        playBar.setAudioPlayState(isPlayStarted)
        Log.d(TAG, "onPrepared: isPlayStarted=$isPlayStarted")
        isPlayingWhenUiOnPause = isPlayStarted
    }

    override fun onPlayStateChanged(isPlaying: Boolean) {
        val playBar = audioContainer ?: return
        Log.d(TAG, "onPlayStatChanged: isPlaying=$isPlaying")
        playBar.setAudioPlayState(isPlaying)
    }

    override fun onProgressUpdate(position: Long, duration: Long) {
        val playBar = audioContainer ?: return
        playBar.setDuration(duration)
        playBar.setProgress(position)
    }

    override fun onSeekPlayProgress(position: Long, isTouching: Boolean) {
        previewModel.obtainAudioPlayer(context).seekTo(position, isTouching)
    }

    override fun onClickPlayButton(requireToPause: Boolean) {
        val player = previewModel.obtainAudioPlayer(context)
        if (requireToPause) {
            player.pause()
        } else {
            player.resume()
        }
    }

    override fun onError(errorType: Int, errorCode: Int, extra: String?) {
        Log.e(TAG, "onError: type=$errorType, code=$errorCode, extra=$extra")
        loadingScheduler.dismissLoading(PreparedResult(true))
        previewModel.releaseAudioPlayer()
        audioContainer?.release()
    }

    override fun onResume(owner: LifecycleOwner) {
        val player = previewModel.obtainAudioPlayer(context)
        Log.d(TAG, "onResume: isPlaying=$isPlayingWhenUiOnPause")
        if (isPlayingWhenUiOnPause) {
            player.resume()
        }
    }

    override fun onPause(owner: LifecycleOwner) {
        val player = previewModel.obtainAudioPlayer(context)
        isPlayingWhenUiOnPause = player.isPlaying()
        Log.d(TAG, "onPause: isPlaying=$isPlayingWhenUiOnPause")
        if (isPlayingWhenUiOnPause) {
            player.pause()
        }
    }

    override fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        containerManager?.onUpdateUIWhenConfigChange(configList)
    }

    private inner class PreviewLoadingScheduler : AbsPreviewLoadingScheduler() {
        override val subTag: String = TAG

        override fun onShowLoading() {
            containerManager?.showAsLoading()
        }

        override fun onGetStartLoadingTime(): Long? = containerManager?.startLoadingTime

        override fun onDismissLoading(extraObj: Any?) {
            val preparedResult = extraObj as? PreparedResult ?: return
            containerManager?.showAsFileContainer(preparedResult.isDefault)
            if (!preparedResult.isDefault) {
                val player = previewModel.obtainAudioPlayer(context)
                player.resume()
            }
        }
    }

    internal data class PreparedResult(
        val isDefault: Boolean,
        val isPlayStarted: Boolean = false
    )
}