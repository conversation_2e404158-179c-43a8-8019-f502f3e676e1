/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : FeishuIntentProcess.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.process

import android.content.ComponentName
import android.content.Intent
import android.net.Uri
import android.os.BadParcelableException
import android.os.Build
import android.os.Bundle
import android.util.Log
import com.bytedance.ee.bear.contract.drive.sdk.entity.open.BaseOpenEntity
import com.bytedance.ee.bear.domain.BearUrl
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.CheckActivityItem.Companion.SOURCE_TYPE_CLOUD_FAVORATE
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.CheckActivityItem.Companion.SOURCE_TYPE_CLOUD_OFFLINE
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.CheckActivityItem.Companion.SOURCE_TYPE_CLOUD_PERSONAL
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.CheckActivityItem.Companion.SOURCE_TYPE_CLOUD_SHARED
import com.filemanager.common.constants.ThirdAppConstants.FEISHU_PACKAGE
import java.lang.ClassCastException

class FeishuIntentProcess : DefaultIntentProcess() {

    companion object {
        const val TAG = "FeishuIntentProcess"

        const val EXTRA_DRIVE_SDK_OPEN_ENTITY = "extra_drive_sdk_open_entity"
        const val EXTRA_URL = "url"
        const val EXTRA_BEAR_URL = "bear_url"
        const val EXTRA_NODE_NAME = "node_name"
    }


    private fun getUidFromIntent(keyInfo: KeyInfoFromIntent): String {
        return if (keyInfo.isRealFile()) {
            keyInfo.intentComponent.toShortString() + "_fileId:" + keyInfo.fileId
        } else if (keyInfo.isCloudFile()) {
            keyInfo.intentComponent.toShortString() + "_url:" + keyInfo.url
        } else {
            val result = keyInfo.intentComponent.toShortString()
            Log.e(TAG, "getUidFromIntent no real file, no cloud File, generate uuid $result as uid")
            result
        }
    }

    private fun getFileNameFromIntent(keyInfo: KeyInfoFromIntent): String {
        var result = keyInfo.fileName
        if (keyInfo.isCloudFile()) {
            result = keyInfo.nodeName
        }
        //默认本地的文件使用fileName，云端创建的云文档使用nodeName
        Log.i(
            TAG,
            "getFileNameFromIntent input KeyInfoFromIntent $KeyInfoFromIntent result $result"
        )
        return result
    }


    private fun getSourceTypeFromIntent(keyInfo: KeyInfoFromIntent): Int {
        var result = -1
        if (keyInfo.isCloudFile()) {
            if (keyInfo.isCloudFileFromFavorate()) {
                result = SOURCE_TYPE_CLOUD_FAVORATE
            } else if (keyInfo.isCloudFileFromPersonal()) {
                result = SOURCE_TYPE_CLOUD_PERSONAL
            } else if (keyInfo.isCloudFileFromShare()) {
                result = SOURCE_TYPE_CLOUD_SHARED
            } else if (keyInfo.isCloudFileFromOffLine()) {
                result = SOURCE_TYPE_CLOUD_OFFLINE
            }
        }
        //非云文档的不做处理，这里只有云文档的部分可以明确，其他的不明确无法从intent中获取有效信息，需要在下一个步骤图文提取框架的结果中来做处理
        return result
    }





    private fun processFileSizeFromExactString() {
        val regexString = "\\n\\S*(.xlsx|.pptx)\\n"
        val inputString = "按名称\n" +
                "5701厂幼儿园幼儿假期行踪登记表.xlsx\n" +
                "最近修改于 昨天 14:49\n" +
                "\uD83D\uDCCB待办事项\n" +
                "最近修改于 昨天 14:27\n" +
                "测试使用的.pptx\n" +
                "最近修改于 昨天 14:49\n" +
                "会议记录 (简洁版)\n" +
                "最近修改于  4月23日 20:10\n" +
                "我的空间\n"
        val regex = Regex(regexString)
        val matched = regex.matches(inputString)
        val matchEntire = regex.matchEntire(inputString)
        val resultSequence = regex.findAll(inputString)
        val sequenceIterator = resultSequence.iterator()
        var resultIndex = 0
        if (sequenceIterator.hasNext()) {
            do {
                val result = sequenceIterator.next()
                val groups = result.groups
                val groupSize = groups.size
                groups.forEach { groupItem ->
                    val itemRange = groupItem?.range
                    val itemValue = groupItem?.value
                    Log.i(
                        TAG,
                        "processFileSizeFromExactString foreach size $groupSize groupItem $groupItem, itemRange $itemRange, itemValue $itemValue"
                    )
                }
                resultIndex++
                Log.i(TAG, "result value ${result.value} range ${result.range}, index $resultIndex")
            } while (sequenceIterator.hasNext())
        }
        Log.i(
            TAG,
            "processFileSizeFromExactString matched $matched, matchEntire $matchEntire find ${resultIndex != 0}"
        )
    }


    override fun getKeyDataBeanFromIntent(inputIntent: Intent): IViewExactProcess.KeyDataBean {
        val keyInfo = getKeyInfoFromIntent(inputIntent)
        val uid = getUidFromIntent(keyInfo)
        val fileName = getFileNameFromIntent(keyInfo)
        val sourceType = getSourceTypeFromIntent(keyInfo)
        val result = IViewExactProcess.KeyDataBean()
        result.uid = uid
        result.fileName = fileName
        result.sourceType = sourceType
        Log.i(TAG, "getKeyDataBeanFromIntent intent $inputIntent, result $result")
        return result
    }


    private fun getKeyInfoFromIntent(inputIntent: Intent): KeyInfoFromIntent {
        val intentData = inputIntent.data
        val extras = inputIntent.extras
        val sb = StringBuilder()
        val result = KeyInfoFromIntent()
        result.intentComponent = inputIntent.component ?: ComponentName(FEISHU_PACKAGE, "")
        if (extras != null) {
            extras.classLoader = BaseOpenEntity::class.java.classLoader
            val extrasKeys = extras.keySet()
            Log.i(TAG, "getUidFromIntent start extra keys ${extrasKeys.toList()}")
            if (extrasKeys != null) {
                for (key in extrasKeys) {
                    try {
                        if (EXTRA_DRIVE_SDK_OPEN_ENTITY.contentEquals(key, true)) {
                            //聊天，单聊，群聊，云文档时 传入的本地文件带有这些参数
                            extractedOpenEntity(extras, key, sb, result)
                        } else {
                            //云文档时，传入的一些云端创建文档的数据
                            extractedOtherField(key, extras, sb, result)
                        }
                    } catch (e: BadParcelableException) {
                        Log.e(TAG, "getEntityFromIntent error", e)
                    } catch (e: ClassCastException) {
                        Log.e(TAG, "getEntityFromIntent error", e)
                    }
                }
            }
        }
        Log.i(TAG, "getUidFromIntent end itent: $inputIntent intentData $intentData, extras: $extras, sb $sb, result: $result")
        return result
    }

    private fun extractedOtherField(
        key: String?,
        extras: Bundle,
        sb: StringBuilder,
        result: KeyInfoFromIntent
    ) {
        val urlVal: String?
        val nodeNameVal: String?
        val bearUrl: BearUrl?
        if (EXTRA_URL.contentEquals(key, true)) {
            urlVal = extras.getString(key)
            sb.append("key $key, value $urlVal\n")
            result.url = urlVal ?: ""
        } else if (EXTRA_NODE_NAME.contentEquals(key, true)) {
            nodeNameVal = extras.getString(key)
            sb.append("key $key, value $nodeNameVal\n")
            result.nodeName = nodeNameVal ?: ""
        } else if (EXTRA_BEAR_URL.contentEquals(key, true)) {
            bearUrl = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                extras.getParcelable(key, BearUrl::class.java)
            } else {
                extras.getParcelable(key)
            }
            sb.append("key $key, value ${bearUrl}\n")
        }
    }


    private fun extractedOpenEntity(
        extras: Bundle,
        key: String?,
        sb: StringBuilder,
        result: KeyInfoFromIntent
    ) {
        val entitys = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            extras.getParcelableArrayList(key, BaseOpenEntity::class.java)
        } else {
            extras.getParcelableArrayList(key)
        }
        if (entitys != null) {
            sb.append("\n")
            for (entity in entitys) {
                sb.append("BaseOpenEntity fileName ${entity.fileName} appId ${entity.appId}, fileExt ${entity.fileExt},")
                sb.append("fileId ${entity.fileId}, uniqueId ${entity.uniqueId}")
                result.fileName = entity.fileName ?: ""
                result.fileExt = entity.fileExt ?: ""
                result.fileId = entity.fileId ?: ""
            }
            sb.append("\n")
        }
    }


    data class KeyInfoFromIntent(
        var intentComponent: ComponentName = ComponentName("", ""),
        var fileName: String = "",
        var fileExt: String = "",
        var fileId: String = "",
        //url ps:https://l6bkksmmpt.feishu.cn/docx/JGe7dYRCVowHoMx753acjMlHnnh?from=tab_offline&ccm_open_type=lark_docs_offline
        var url: String = "",
        var nodeName: String = "",
    ) {

        companion object {
            const val TAG = "KeyInfoFromIntent"


            const val URL_CHAT_TYPE = "chat_type"
            const val URL_KEY_FROM = "from"
            const val URL_KEY_CCM_OPEN_TYPE = "ccm_open_type"

            //我的空间点击进入from=tab_personal
            const val URL_VALUE_FROM_TAB_PERSONAL = "tab_personal"

            //共享空间点击进入from=tab_personal
            const val URL_VALUE_FROM_TAB_SHARED = "tab_shared"

            //收藏点击进入from=tab_favorites
            const val URL_VALUE_FROM_TAB_FAVORITES = "tab_favorites"

            //离线点击进入from=tab_offline
            const val URL_VALUE_FROM_TAB_OFFLINE = "tab_offline"

            //对话框中点击云文档信息from=message
            const val URL_VALUE_FROM_MESSAGE = "message"

            //我的空间点击进入ccm_open_type=lark_docs_personal_Own
            const val URL_VALUE_CCM_OPEN_TYPE_PERSONAL = "lark_docs_personal_Own"

            //共享空间点击进入ccm_open_type=lark_docs_shared_ShareToMe
            const val URL_VALUE_CCM_OPEN_TYPE_SHARED = "lark_docs_shared_ShareToMe"

            //收藏点击进入ccm_open_type=lark_docs_favorites
            const val URL_VALUE_CCM_OPEN_TYPE_FAVORITES = "lark_docs_favorites"

            //离线点击进入ccm_open_type=lark_docs_offline
            const val URL_VALUE_CCM_OPEN_TYPE_OFFLINE = "lark_docs_offline"

            //对话框中点击云文档信息ccm_open_type=message
            const val URL_VALUE_CCM_OPEN_TYPE_MESSAGE = "message"

            const val URL_VALUE_CHAT_SINGLE = "single"
        }


        fun isRealFile(): Boolean {
            return fileName.isNotEmpty() || fileExt.isNotEmpty() || fileId.isNotEmpty()
        }

        fun isCloudFile(): Boolean {
            return url.isNotEmpty() || nodeName.isNotEmpty()
        }

        fun isCloudFileFromChat(): Boolean {
            if (url.isEmpty()) {
                return false
            } else {
                val fromValueEqual =
                    getQueryValueFromUrl(URL_KEY_FROM)?.contains(URL_VALUE_FROM_MESSAGE, true)
                        ?: false
                val ccmValueEqual = getQueryValueFromUrl(URL_KEY_CCM_OPEN_TYPE)?.contains(
                    URL_VALUE_CCM_OPEN_TYPE_MESSAGE,
                    true
                ) ?: false
                Log.i(
                    TAG,
                    "isCloudFileFromChat fromValueEqual $fromValueEqual, ccmValueEqual $ccmValueEqual"
                )
                return fromValueEqual || ccmValueEqual
            }
        }


        fun isCloudFileFromPersonal(): Boolean {
            if (url.isEmpty()) {
                return false
            } else {
                val fromValueEqual =
                    getQueryValueFromUrl(URL_KEY_FROM)?.contains(URL_VALUE_FROM_TAB_PERSONAL, true)
                        ?: false
                val ccmValueEqual = getQueryValueFromUrl(URL_KEY_CCM_OPEN_TYPE)?.contains(
                    URL_VALUE_CCM_OPEN_TYPE_PERSONAL,
                    true
                ) ?: false
                Log.i(
                    TAG,
                    "isCloudFileFromChat fromValueEqual $fromValueEqual, ccmValueEqual $ccmValueEqual"
                )
                return fromValueEqual || ccmValueEqual
            }
        }

        fun isCloudFileFromShare(): Boolean {
            if (url.isEmpty()) {
                return false
            } else {
                val fromValueEqual =
                    getQueryValueFromUrl(URL_KEY_FROM)?.contains(URL_VALUE_FROM_TAB_SHARED, true)
                        ?: false
                val ccmValueEqual = getQueryValueFromUrl(URL_KEY_CCM_OPEN_TYPE)?.contains(
                    URL_VALUE_CCM_OPEN_TYPE_SHARED,
                    true
                ) ?: false
                Log.i(
                    TAG,
                    "isCloudFileFromChat fromValueEqual $fromValueEqual, ccmValueEqual $ccmValueEqual"
                )
                return fromValueEqual || ccmValueEqual
            }
        }

        fun isCloudFileFromFavorate(): Boolean {
            if (url.isEmpty()) {
                return false
            } else {
                val fromValueEqual =
                    getQueryValueFromUrl(URL_KEY_FROM)?.contains(URL_VALUE_FROM_TAB_FAVORITES, true)
                        ?: false
                val ccmValueEqual = getQueryValueFromUrl(URL_KEY_CCM_OPEN_TYPE)?.contains(
                    URL_VALUE_CCM_OPEN_TYPE_FAVORITES,
                    true
                ) ?: false
                Log.i(
                    TAG,
                    "isCloudFileFromChat fromValueEqual $fromValueEqual, ccmValueEqual $ccmValueEqual"
                )
                return fromValueEqual || ccmValueEqual
            }
        }


        fun isCloudFileFromOffLine(): Boolean {
            if (url.isEmpty()) {
                return false
            } else {
                val fromValueEqual =
                    getQueryValueFromUrl(URL_KEY_FROM)?.contains(URL_VALUE_FROM_TAB_OFFLINE, true)
                        ?: false
                val ccmValueEqual = getQueryValueFromUrl(URL_KEY_CCM_OPEN_TYPE)?.contains(
                    URL_VALUE_CCM_OPEN_TYPE_OFFLINE,
                    true
                ) ?: false
                Log.i(
                    TAG,
                    "isCloudFileFromChat fromValueEqual $fromValueEqual, ccmValueEqual $ccmValueEqual"
                )
                return fromValueEqual || ccmValueEqual
            }
        }

        private fun getQueryValueFromUrl(queryKey: String): String? {
            val uri = Uri.parse(url)
            val path = uri.path
            val authority = uri.authority
            val query = uri.query
            Log.i(TAG, "getFromFromUrl uri $url, path $path, authority $authority, query $query")
            val regexFrom = Regex("$queryKey=\\w")
            return if (regexFrom.matches(url)) {
                regexFrom.find(url, 0)?.value
            } else {
                null
            }
        }
    }
}