/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemotePicFetcher.kt
 ** Description: RemotePicFetcher
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.category.remotedevice.glide.pic

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import com.bumptech.glide.Priority
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.data.DataFetcher
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.interfaze.remotedevice.IRemotePicListener
import com.oplus.filemanager.interfaze.remotedevice.IRemoteThumbnail
import com.oplus.filemanager.remotedevice.provider.ThumbnailProvider
import java.io.File
import java.io.IOException
import java.util.UUID

class RemotePicFetcher(
    private val context: Context,
    private val remoteFileBean: RemoteFileBean,
    private val width: Int,
    private val height: Int
) : DataFetcher<Uri> {

    private companion object {
        private const val TAG = "RemoteThumbnailFetcher"
    }

    @Volatile
    private var isCancelled = false
    private var bitmap: Bitmap? = null
    override fun loadData(priority: Priority, callback: DataFetcher.DataCallback<in Uri>) {
        Log.i(TAG, "RemotePicFecher")
        if (isCancelled) {
            //return null when request has been cancelled
            callback.onDataReady(null)
            return
        }
        val patch = remoteFileBean.originalPath
        val devicesId = getDeviceId()
        val picFile = createFile(context)
        val picUri = createUri(context, picFile)
        val remoteWidth: Int =
            context.resources.getDimensionPixelSize(com.oplus.filemanager.category.remotedevice.R.dimen.preview_remote_max_width)
        val remoteHeight =
            context.resources.getDimensionPixelSize(com.oplus.filemanager.category.remotedevice.R.dimen.preview_remote_max_height)
        val packageName = context.packageName
        if (remoteFileBean.remotePicUri != null) {
            callback.onDataReady(picUri)
        }
        context.grantUriPermission(
            "com.oplus.remotecontrol",
            picUri,
            Intent.FLAG_GRANT_WRITE_URI_PERMISSION
        )
        Injector.injectFactory<IRemoteThumbnail>()
            ?.getPicThumbnails(
                devicesId, patch, picUri, packageName, remoteWidth, remoteHeight,
                object : IRemotePicListener {
                    override fun onResult(code: Int, picUri: Uri) {
                        remoteFileBean.remotePicUri = picUri
                        context.revokeUriPermission(
                            "com.oplus.remotecontrol",
                            picUri,
                            Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                        )

                        callback.onDataReady(picUri)
                    }
                }
            )
    }

    override fun cleanup() {
        bitmap?.recycle()
    }

    override fun cancel() {
        isCancelled = true
        bitmap = null
    }

    override fun getDataClass(): Class<Uri> {
        return Uri::class.java
    }

    override fun getDataSource(): DataSource {
        return DataSource.REMOTE
    }

    private fun createFile(context: Context): File {
        val pictureName = UUID.randomUUID().toString() //需要增加其他标志位
        val picFile = File(File(context.cacheDir, "remotePic"), "$pictureName.png")
        createDir(picFile)
        try {
            picFile.createNewFile()
        } catch (e: IOException) {
            Log.e(TAG, "createUri error " + e.message)
        }
        return picFile
    }

    private fun createUri(context: Context, picFile: File): Uri {
        return ThumbnailProvider.getUriForFile(context, "filemanager.thumbnail.provider", picFile)
    }

    private fun getDeviceId(): String {
        var devicesId = ""
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        val currentLinkInfo = remoteDeviceApi?.getCurrentLinkedRemoteDiceInfo()
        if (currentLinkInfo != null) {
            devicesId = currentLinkInfo.deviceId.toString()
        }
        return devicesId
    }

    private fun createDir(picFile: File) {
        if (picFile.parentFile?.exists()?.not() == true) {
            picFile.parentFile?.mkdirs()
        }
    }
}