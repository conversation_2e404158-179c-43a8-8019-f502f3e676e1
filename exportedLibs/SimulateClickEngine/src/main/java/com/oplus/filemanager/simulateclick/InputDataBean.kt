/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : InputDataBean
 ** Description : 封装跳转时的传入的数据结构
 ** Version     : 1.0
 ** Date        : 2024/05/22 10:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/06       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.simulateclick

import androidx.annotation.Keep

@Keep
data class InputDataBean(val fileName: String, val packageName: String)