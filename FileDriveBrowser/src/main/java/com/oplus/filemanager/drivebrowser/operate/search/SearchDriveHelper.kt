/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchDriveHelper
 ** Description : Search Drive Helper
 ** Version     : 1.0
 ** Date        : 2024/04/25 11:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/04/25       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.operate.search

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.CommonConstants.DISABLE_KING_DOC
import com.filemanager.common.constants.CommonConstants.DISABLE_TENCENT_DOC
import com.filemanager.common.constants.CommonConstants.KEY_SUPPORT_K_DOCS
import com.filemanager.common.constants.CommonConstants.KEY_SUPPORT_TENCENT_DOCS
import com.filemanager.common.constants.CommonConstants.PREF_NAME_FILE_CLOUD_DOCS
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsDocumentItem
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem
import com.oplus.filemanager.drivebrowser.data.repository.kdocs.KDocsFileDriveRepository
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.di.NetworkModule
import com.oplus.filemanager.drivebrowser.domain.model.CloudDocumentItem
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils
import com.oplus.filemanager.room.model.DriveFileEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking

object SearchDriveHelper {

    private const val TAG = "SearchDriveHelper"

    private val tencentRepository: FileDriveRepository by lazy {
        val service = NetworkModule.providerTencentFileDriveService()
        TencentFileDriveRepository(service)
    }

    private val kDocsRepository: FileDriveRepository by lazy {
        val service = NetworkModule.provideKDocsFileDriveService()
        KDocsFileDriveRepository(service)
    }

    fun search(searchKey: String, page: Int): List<BaseFileBean> {
        val start = System.currentTimeMillis()
        Log.d(TAG, "search $searchKey start...")
        val result = runBlocking(Dispatchers.IO) {
            val tencentJob = async { searchTencentFile(searchKey, page) }
            val kDocsJob = async { searchKDocsFile(searchKey, page) }
            tencentJob.await() + kDocsJob.await()
        }
        val filter = filter(searchKey, result)
        Log.d(TAG, "search $searchKey end -> search: ${result.size} filter:${filter.size} ,speed ${System.currentTimeMillis() - start} ms")
        return filter
    }

    private fun filter(searchKey: String, list: List<DriveFileWrapper>): List<BaseFileBean> {
        return list.filter {
            it.title.contains(searchKey, ignoreCase = true)
        }
    }

    suspend fun searchTencentFile(searchKey: String, page: Int): List<DriveFileWrapper> {
        val openSwitch = isOpenDriveFileSwitch(CategoryHelper.CATEGORY_TENCENT_DOCS) && PreferencesUtils.getBoolean(
            PREF_NAME_FILE_CLOUD_DOCS, KEY_SUPPORT_TENCENT_DOCS, DISABLE_TENCENT_DOC
        )
        Log.d(TAG, "searchTencentFile openSwitch:$openSwitch")
        if (!openSwitch) {
            return emptyList()
        }
        val isAuth = FileDriveStateUtils.isAuthed(CategoryHelper.CATEGORY_TENCENT_DOCS) || tencentRepository.checkAuthorizationState()
        Log.d(TAG, "searchTencentFile isAuth:$isAuth")
        if (!isAuth) {
            return emptyList()
        }
        val list = tencentRepository.searchFile(searchKey, page)
        return list.lists.map {
            CloudDocumentItem.map(it, DriveFileWrapper.SOURCE_TYPE_TENCENT)
        }.map {
            map(it)
        }
    }

    suspend fun searchKDocsFile(searchKey: String, page: Int): List<DriveFileWrapper> {
        val openSwitch = isOpenDriveFileSwitch(CategoryHelper.CATEGORY_K_DOCS) && PreferencesUtils.getBoolean(
            PREF_NAME_FILE_CLOUD_DOCS, KEY_SUPPORT_K_DOCS, DISABLE_KING_DOC
        )
        Log.d(TAG, "searchKDocsFile openSwitch:$openSwitch")
        if (!openSwitch) {
            return emptyList()
        }
        val isAuth = FileDriveStateUtils.isAuthed(CategoryHelper.CATEGORY_K_DOCS) || kDocsRepository.checkAuthorizationState()
        Log.d(TAG, "searchKDocsFile isAuth:$isAuth")
        if (!isAuth) {
            return emptyList()
        }
        val list = kDocsRepository.searchFile(searchKey, page)
        Log.d(TAG, "searchKDocsFile result size:${list.lists.size}")
        return list.lists.map {
            CloudDocumentItem.map(it, DriveFileWrapper.SOURCE_TYPE_KINGSSOFT)
        }.map {
            map(it)
        }
    }

    /**
     * 判断设置项中金山或者腾讯文档的开关是否打开
     */
    private fun isOpenDriveFileSwitch(category: Int): Boolean {
        val spKey = if (category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
            CommonConstants.TENCENT_DOCS_FUNCTION_SHOW
        } else {
            CommonConstants.K_DOCS_FUNCTION_SHOW
        }
        return PreferencesUtils.getBoolean(key = spKey, default = false)
    }

    fun map(data: DriveFileEntity): DriveFileWrapper {
        val item = DriveFileWrapper()
        item.source = data.source
        item.id = data.fileId
        item.mDisplayName = data.name
        item.mMimeType = data.mimeType
        item.mLocalType = when (data.mimeType) {
            DriveFileEntity.TYPE_FOLDER -> MimeTypeHelper.DIRECTORY_TYPE
            DocumentListItem.TENCENT_FILE_TYPE_DOC -> MimeTypeHelper.DOC_TYPE
            DocumentListItem.TENCENT_FILE_TYPE_SLIDE -> MimeTypeHelper.PPT_TYPE
            DocumentListItem.TENCENT_FILE_TYPE_SHEET -> MimeTypeHelper.XLS_TYPE
            DocumentListItem.TENCENT_FILE_TYPE_FORM -> MimeTypeHelper.TENCENT_FORM_TYPE
            DocumentListItem.TENCENT_FILE_TYPE_PDF -> MimeTypeHelper.PDF_TYPE
            DocumentListItem.TENCENT_FILE_TYPE_MIND -> MimeTypeHelper.TENCENT_MIND_TYPE
            DocumentListItem.TENCENT_FILE_TYPE_FLOWCHART -> MimeTypeHelper.TENCENT_FLOWCHART_TYPE
            DocumentListItem.TENCENT_FILE_TYPE_SMART_SHEET -> MimeTypeHelper.TENCENT_SMART_SHEET_TYPE // 智能表格
            DocumentListItem.TENCENT_FILE_TYPE_SMART_CANVAS -> MimeTypeHelper.TENCENT_SMART_CANVAS_TYPE // 智能文档
            DocumentListItem.TENCENT_FILE_TYPE_DRIVE -> MimeTypeHelper.TXT_TYPE // drive类型的文件显示样式为txt
            KDocsDocumentItem.KDOCS_TYPE_XLSX -> MimeTypeHelper.XLSX_TYPE
            KDocsDocumentItem.KDOCS_TYPE_DOCS -> MimeTypeHelper.DOCX_TYPE
            KDocsDocumentItem.KDOCS_TYPE_PPTX -> MimeTypeHelper.PPTX_TYPE
            KDocsDocumentItem.KDOCS_TYPE_OTL -> MimeTypeHelper.KDOCS_OTL_TYPE // 在线文档
            KDocsDocumentItem.KDOCS_TYPE_KSHEET -> MimeTypeHelper.KDOCS_KSHEET_TYPE // 在线表格
            KDocsDocumentItem.KDOCS_TYPE_DBT -> MimeTypeHelper.KDOCS_DBT_TYPE // 轻维表
            KDocsDocumentItem.KDOCS_TYPE_SHARE_FOLDER -> MimeTypeHelper.KDOCS_SHARE_FOLDER_TYPE // 共享文件夹
            else -> MimeTypeHelper.getTypeFromPath(data.name)
        }
        item.type = data.mimeType ?: ""
        item.title = data.name
        item.url = data.uri
        item.mData = data.uri
        item.mDateModified = data.lastModifyTime.toLong() * DriveFileWrapper.TIME_CONVERT_VALUE
        item.lastOpenTime = data.lastBrowserTime.toLong() * DriveFileWrapper.TIME_CONVERT_VALUE
        return item
    }
}