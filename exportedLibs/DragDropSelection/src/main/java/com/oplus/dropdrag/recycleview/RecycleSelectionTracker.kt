/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - RecycleSelectionTracker.kt
 ** Description: Selection Tracker fore recycler view
 ** Version: 1.0
 ** Date : 2020/05/15
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  Jiafei.<PERSON>@Apps.FileManager      2020/05/15    1.0     create
 ****************************************************************/

package com.oplus.dropdrag.recycleview

import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.AdapterDataObserver
import com.oplus.dropdrag.SelectionDelegate
import com.oplus.dropdrag.SelectionTracker
import com.oplus.dropdrag.recycleview.SelectionPredicates.SelectionPredicate
import com.oplus.dropdrag.utils.Log
import java.util.*

internal class RecycleSelectionTracker<K>(
    private val mTrackerTag: String,
    private val mSelectionDelegate: SelectionDelegate<K>,
    private val mKeyProvider: ItemKeyProvider<K>?,
    private val mSelectionPredicate: SelectionPredicate<K>,
    private val mCanSlideSeletion: Boolean = true,
    private val mScrollThresholdRatio: Float = AutoScroller.DEFAULT_SCROLL_THRESHOLD_RATIO
) : SelectionTracker<K>() {

    private companion object {
        private const val EXTRA_SELECTION_PREFIX = "androidx.recyclerview.selection"
        private const val TAG = "RecycleSelectionTracker"
    }

    private var mRange: Range? = null

    private val mProvisionalSelection: LinkedHashSet<K> by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        LinkedHashSet<K>()
    }

    private val mRangeCallbacks by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        RangeCallbacks()
    }

    private val mAdapterObserver: AdapterObserver by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        AdapterObserver(this)
    }

    override val adapterDataObserver: AdapterDataObserver
        get() {
            return mAdapterObserver
        }

    override fun hasSelection(): Boolean {
        return mSelectionDelegate.isAnyItemSelected()
    }

    override fun isInSelectionMode(): Boolean {
        return mSelectionDelegate.isInSelectMode()
    }

    override var longPressIndex: Int = -1
        get() = field
        set(value) {
            field = value
        }

    override var layoutType: LAYOUT_TYPE = LAYOUT_TYPE.LIST
        get() {
            return mSelectionDelegate.getLayoutType()
        }

    override var scrollThresholdRatio: Float = AutoScroller.DEFAULT_SCROLL_THRESHOLD_RATIO
        get() {
            return mScrollThresholdRatio
        }

    override fun canSlideSelection(): Boolean {
        return mCanSlideSeletion
    }

    override fun isItemSelected(key: K): Boolean {
        return isInSelectionMode() && mSelectionDelegate.isItemSelected(key)
    }

    override fun selectItem(key: K): Boolean {
        if (mSelectionDelegate.isItemSelected(key)) {
            return true
        }

        if (!canSetState(key, true)) {
            if (DEBUG) {
                Log.d(TAG, "selectItem Select cancelled by selection predicate test")
            }
            return false
        }

        // Enforce single selection mode.
        if (!mSelectionPredicate.canSelectMultiple() && mSelectionDelegate.isAnyItemSelected()) {
            clearSelectionQuietly()
        }

        if (mSelectionDelegate.selectItem(key)) {
            return true
        }
        return false
    }

    override fun deselectItem(key: K): Boolean {
        if (mSelectionDelegate.isItemSelected(key)) {
            if (!canSetState(key, false)) {
                if (DEBUG) {
                    Log.d(TAG, "deselectItem Deselect cancelled by selection predicate test.")
                }
                return false
            }

            if (mSelectionDelegate.deselectItem(key)) {
                if (isRangeActive && !mSelectionDelegate.isAnyItemSelected()) {
                    // if there's nothing in the selection and there is an active ranger it results
                    // in unexpected behavior when the user tries to start range selection: the item
                    // which the ranger 'thinks' is the already selected anchor becomes unselectable
                    endRange()
                }
                return true
            }
        }
        return false
    }

    override fun getSelectionList(): List<K> {
        return mSelectionDelegate.getSelectionList()
    }

    private fun canSetState(key: K, nextState: Boolean): Boolean {
        return mSelectionPredicate.canSetStateForKey(key, nextState)
    }

    private fun clearSelectionQuietly(): List<K>? {
        mRange = null
        val prevSelectionList = ArrayList<K>()
        if (mSelectionDelegate.isAnyItemSelected()) {
            val originSelection = mSelectionDelegate.getSelectionList()
            prevSelectionList.addAll(originSelection)
            for (key in originSelection) {
                mSelectionDelegate.deselectItem(key)
            }
        }
        return prevSelectionList
    }

    override fun extendRange(position: Int) {
        if (!isRangeActive) {
            Log.d(TAG, "extendRange, Range start point not set.")
            return
        }

        mRange?.extendRange(position)
    }

    override fun endRange() {
        mRange = null
        mProvisionalSelection.clear()
    }

    override val isRangeActive: Boolean
        get() {
            return mRange != null
        }

    override fun anchorRange(position: Int, key: K) {
        if (position == RecyclerView.NO_POSITION) {
            Log.d(TAG, "anchorRange position is invalid")
            return
        }

        key?.let {
            val isSelected = mSelectionDelegate.isItemSelected(key)
            mRange = if (layoutType == LAYOUT_TYPE.GRID) {
                GridRange(position, mRangeCallbacks, isSelected)
            } else {
                ListRange(position, mRangeCallbacks, isSelected)
            }
            if (DEBUG) {
                Log.d(TAG, "anchorRange START RANGE position: $position, isSelected: $isSelected")
            }
        }
    }

    override fun enterSelectMode(key: K): Boolean {
        return mSelectionDelegate.enterSelectionMode(key)
    }

    override fun extendProvisionalRange(position: Int) {
        if (!mSelectionPredicate.canSelectMultiple()) {
            return
        }

        if (DEBUG) {
            Log.i(TAG, "Extending provision range to position: $position")
        }
        if (!isRangeActive) {
            Log.d(TAG, "Range start point not set.")
            return
        }

        extendRange(position)
    }

    override fun clearProvisionalSelection() {
        mProvisionalSelection.clear()
    }

    override fun mergeProvisionalSelection() {
        val provisionalList = ArrayList(mProvisionalSelection.toList())
        if (!provisionalList.isNullOrEmpty()) {
            mSelectionDelegate.selectItems(provisionalList)
            mProvisionalSelection.clear()
        }
    }

    override fun canMultipleSelect(): Boolean {
        return mSelectionPredicate.canSelectMultiple()
    }

    override fun canDragDrop(): Boolean {
        return mSelectionDelegate.canDragDrop()
    }

    fun getInstanceStateKey(): String {
        return EXTRA_SELECTION_PREFIX + ":" + mTrackerTag
    }

    private inner class RangeCallbacks : Callbacks() {
        override fun updateForRange(begin: Int, end: Int, anchor: Int, selected: Boolean) {
            updateForRegularRange(begin, end, anchor, selected)
        }
    }

    private fun updateForRegularRange(begin: Int, end: Int, anchor: Int, selected: Boolean) {
        if (mKeyProvider == null) {
            Log.d(TAG, "updateForRegularRange SKIP keyProvider is null")
            return
        }

        if (end < begin) {
            Log.d(TAG, "updateForRegularRange begin less than end")
            return
        }

        for (i in begin..end) {
            if (i == anchor) {
                continue
            }
            val key = mKeyProvider.getKey(i)
            if (key == null) {
                Log.d(TAG, "updateForRegularRange key is NULL")
                continue
            } else if (!isValidKey(key)) {
                Log.d(TAG, "updateForRegularRange key NO_ID")
                continue
            }

            if (selected) {
                selectItem(key)
            } else {
                deselectItem(key)
            }
        }
    }

    private class AdapterObserver(
        val mSelectionTracker: RecycleSelectionTracker<*>
    ) : AdapterDataObserver() {

        override fun onChanged() {
        }

        override fun onItemRangeChanged(startPosition: Int, itemCount: Int, payload: Any?) {
        }

        override fun onItemRangeInserted(startPosition: Int, itemCount: Int) {
            mSelectionTracker.endRange()
        }

        override fun onItemRangeRemoved(startPosition: Int, itemCount: Int) {
            mSelectionTracker.endRange()
        }

        override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
            mSelectionTracker.endRange()
        }
    }
}
