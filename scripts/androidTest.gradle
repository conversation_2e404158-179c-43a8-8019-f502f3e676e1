final isAppModule = project.plugins.hasPlugin("com.android.application")
final isLibModule = project.plugins.hasPlugin("com.android.library")
if (!(isAppModule || isLibModule)) {
    final errMsg = "${project.name}: androidTest.gradle must be applied to " +
            "an Android application or library module. Please apply " +
            "com.android.application or com.android.library first."
    System.err.println(errMsg)
    throw new GradleException(errMsg)
}

dependencies {
    androidTestCompileOnly(libs.oplus.addon.sdk) { artifact { type = "aar" } }
    androidTestImplementation libs.bundles.android.test.espresso
    androidTestImplementation libs.bundles.android.test.suit
    androidTestImplementation libs.google.gson
    androidTestImplementation(libs.otest.testLib) { artifact { type = "aar" } }
    androidTestImplementation libs.mockito.android
    androidTestImplementation libs.androidx.test.uiautomator
}