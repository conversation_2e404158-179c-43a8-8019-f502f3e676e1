/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileDecompressPasswordDialog.kt
 ** Description: Input file name when file compress
 ** Version: 1.0
 ** Date: 2020/3/9
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.decompress

import android.content.Context
import android.content.DialogInterface
import android.text.InputFilter
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.helper.getBottomAlertDialogWindowAnimStyle
import com.filemanager.common.helper.getBottomAlertDialogWindowGravity
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.fileoperate.R
import com.filemanager.fileoperate.base.BaseFileNameDialog

class FileDecompressPasswordDialog(val context: Context, val compressBean: FileDecompressObserver.PasswordDialogBean)
    : BaseFileNameDialog(context) {


    override fun show() {
        mDialogBuilder = COUIAlertDialogBuilder(context, KtViewUtils.getDialogStyle()).apply {
            setTitle(com.filemanager.common.R.string.dialog_input_password_title)
            setMessage(com.filemanager.common.R.string.preview_need_password)
            setNegativeButton(com.filemanager.common.R.string.dialog_cancel) { _, _ -> callbackWhenCloseDialog() }
            setOnCancelListener { callbackWhenCloseDialog() }
            setPositiveButton(com.filemanager.common.R.string.confirm, null)
            setWindowGravity(getBottomAlertDialogWindowGravity(context))
            setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(context))
            setBlurBackgroundDrawable(true)
        }
        mDialog = mDialogBuilder!!.show()
        initViews {
            compressBean.resultListener?.onClick(mDialog!!, DialogInterface.BUTTON_POSITIVE,
                getInputValue())
        }
        mInputView?.setEnablePassword(true)
        setPositiveButtonEnabled(false)
        mOppoEditText?.filters?.let {
            val tmpFilterList = arrayListOf<InputFilter>()
            it.forEach { filter ->
                if ((filter != null) && (filter != mIllegalFilter)) {
                    tmpFilterList.add(filter)
                }
            }
            val filterArray: Array<InputFilter?> = arrayOfNulls(tmpFilterList.size)
            mOppoEditText?.filters = tmpFilterList.toArray(filterArray)
        }
        mOppoEditText?.setHint(com.filemanager.common.R.string.dialog_input_password_hint)
        checkInputText()
    }

    private fun callbackWhenCloseDialog() {
        mDialog?.let { dialog ->
            compressBean.resultListener?.onClick(dialog)
            mOppoEditText?.let { editText ->
                hideSoftInput(editText)
            }
        }
    }

    override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
        setPositiveButtonEnabled(s.isEmpty().not())
    }

    fun showPasswordErrorNotice() {
        mInputView?.showError(mContext.getString(com.filemanager.common.R.string.dialog_decompress_password_incorrect))
    }

    fun setInputText(text: String?) {
        mInputView?.editText?.setText(text)
    }
}