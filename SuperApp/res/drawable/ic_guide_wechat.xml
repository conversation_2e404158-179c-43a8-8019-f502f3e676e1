<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="328dp"
    android:height="275dp"
    android:viewportWidth="328"
    android:viewportHeight="275">
  <group>
    <clip-path
        android:pathData="M1,50h326v219.14h-326z"/>
    <path
        android:pathData="M98.46,-166.22L229.54,-166.22A29.96,29.96 0,0 1,259.5 -136.26L259.5,206.42A29.96,29.96 0,0 1,229.54 236.38L98.46,236.38A29.96,29.96 0,0 1,68.5 206.42L68.5,-136.26A29.96,29.96 0,0 1,98.46 -166.22z"
        android:fillColor="#E4E7EB"/>
    <path
        android:pathData="M98.46,-166.42L229.54,-166.42A30.16,30.16 0,0 1,259.7 -136.26L259.7,206.41A30.16,30.16 0,0 1,229.54 236.57L98.46,236.57A30.16,30.16 0,0 1,68.31 206.41L68.31,-136.26A30.16,30.16 0,0 1,98.46 -166.42z"
        android:strokeAlpha="0.12"
        android:strokeWidth="0.389602"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M98.46,-160.6L229.54,-160.6A24.34,24.34 0,0 1,253.88 -136.26L253.88,206.42A24.34,24.34 0,0 1,229.54 230.76L98.46,230.76A24.34,24.34 0,0 1,74.12 206.42L74.12,-136.26A24.34,24.34 0,0 1,98.46 -160.6z"
        android:fillColor="#FAFAFA"/>
    <path
        android:pathData="M58.62,45.39h209.82v26.34h-209.82z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="163.53"
            android:startY="45.39"
            android:endX="163.53"
            android:endY="71.73"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="0.64" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M74,93.4C74,85.56 74,81.64 75.53,78.64C76.87,76.01 79.01,73.87 81.64,72.53C84.64,71 88.56,71 96.4,71H231.6C239.44,71 243.36,71 246.36,72.53C248.99,73.87 251.13,76.01 252.47,78.64C254,81.64 254,85.56 254,93.4V192.6C254,206.04 254,212.76 251.38,217.9C249.08,222.41 245.41,226.08 240.9,228.38C235.76,231 229.04,231 215.6,231H112.4C98.96,231 92.24,231 87.1,228.38C82.59,226.08 78.92,222.41 76.62,217.9C74,212.76 74,206.04 74,192.6V93.4Z"
        android:fillColor="#FEFEFE"/>
    <path
        android:pathData="M231.6,70.81H96.4H96.39C92.48,70.81 89.53,70.81 87.19,71C84.84,71.19 83.09,71.57 81.56,72.35C78.88,73.71 76.71,75.88 75.35,78.56C74.57,80.09 74.19,81.84 74,84.19C73.81,86.53 73.81,89.48 73.81,93.39V93.4V192.6V192.61C73.81,199.32 73.81,204.37 74.13,208.38C74.46,212.39 75.12,215.38 76.44,217.98C78.76,222.54 82.46,226.24 87.02,228.56C89.61,229.88 92.61,230.54 96.62,230.87C100.63,231.2 105.68,231.2 112.39,231.2H112.4H215.6H215.61C222.32,231.2 227.37,231.2 231.38,230.87C235.39,230.54 238.38,229.88 240.98,228.56C245.54,226.24 249.24,222.54 251.56,217.98C252.88,215.38 253.54,212.39 253.87,208.38C254.2,204.37 254.2,199.32 254.2,192.61V192.6V93.4V93.39C254.2,89.48 254.2,86.53 254,84.19C253.81,81.84 253.43,80.09 252.65,78.56C251.29,75.88 249.12,73.71 246.44,72.35C244.91,71.57 243.16,71.19 240.81,71C238.47,70.81 235.52,70.81 231.61,70.81H231.6Z"
        android:strokeAlpha="0.3"
        android:strokeWidth="0.389602"
        android:fillColor="#00000000"
        android:strokeColor="#D0D0D0"/>
    <path
        android:pathData="M120.93,217.66L207.07,217.66A1.87,1.87 0,0 1,208.94 219.53L208.94,219.53A1.87,1.87 0,0 1,207.07 221.41L120.93,221.41A1.87,1.87 0,0 1,119.06 219.53L119.06,219.53A1.87,1.87 0,0 1,120.93 217.66z"
        android:fillColor="#DFE2E5"/>
    <path
        android:pathData="M97,105L111,105A6,6 0,0 1,117 111L117,125A6,6 0,0 1,111 131L97,131A6,6 0,0 1,91 125L91,111A6,6 0,0 1,97 105z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M137,105L151,105A6,6 0,0 1,157 111L157,125A6,6 0,0 1,151 131L137,131A6,6 0,0 1,131 125L131,111A6,6 0,0 1,137 105z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M177,105L191,105A6,6 0,0 1,197 111L197,125A6,6 0,0 1,191 131L177,131A6,6 0,0 1,171 125L171,111A6,6 0,0 1,177 105z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M217,105L231,105A6,6 0,0 1,237 111L237,125A6,6 0,0 1,231 131L217,131A6,6 0,0 1,211 125L211,111A6,6 0,0 1,217 105z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M97,151L111,151A6,6 0,0 1,117 157L117,171A6,6 0,0 1,111 177L97,177A6,6 0,0 1,91 171L91,157A6,6 0,0 1,97 151z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M100.72,160.24H98.9C98.57,160.24 98.3,160.51 98.3,160.84V170.53C98.3,170.86 98.57,171.13 98.9,171.13H109.19C109.53,171.13 109.8,170.86 109.8,170.53V160.84C109.8,160.51 109.53,160.24 109.19,160.24H107.38V161.39H108.64V169.98H99.45V161.39H100.72V160.24Z"
        android:fillColor="#8C8C8C"
        android:fillType="evenOdd"/>
    <group>
      <clip-path
          android:pathData="M100.72,160.24H98.9C98.57,160.24 98.3,160.51 98.3,160.84V170.53C98.3,170.86 98.57,171.13 98.9,171.13H109.19C109.53,171.13 109.8,170.86 109.8,170.53V160.84C109.8,160.51 109.53,160.24 109.19,160.24H107.38V161.39H108.64V169.98H99.45V161.39H100.72V160.24Z"
          android:fillType="evenOdd"/>
      <path
          android:pathData="M100.72,160.24H100.82V160.14H100.72V160.24ZM107.38,160.24V160.14H107.28V160.24H107.38ZM107.38,161.39H107.28V161.49H107.38V161.39ZM108.64,161.39H108.74V161.3H108.64V161.39ZM108.64,169.98V170.07H108.74V169.98H108.64ZM99.45,169.98H99.36V170.07H99.45V169.98ZM99.45,161.39V161.3H99.36V161.39H99.45ZM100.72,161.39V161.49H100.82V161.39H100.72ZM100.72,160.14H98.9V160.33H100.72V160.14ZM98.9,160.14C98.52,160.14 98.2,160.46 98.2,160.84H98.4C98.4,160.56 98.62,160.33 98.9,160.33V160.14ZM98.2,160.84V170.53H98.4V160.84H98.2ZM98.2,170.53C98.2,170.92 98.52,171.23 98.9,171.23V171.04C98.62,171.04 98.4,170.81 98.4,170.53H98.2ZM98.9,171.23H109.19V171.04H98.9V171.23ZM109.19,171.23C109.58,171.23 109.9,170.92 109.9,170.53H109.7C109.7,170.81 109.48,171.04 109.19,171.04V171.23ZM109.9,170.53V160.84H109.7V170.53H109.9ZM109.9,160.84C109.9,160.46 109.58,160.14 109.19,160.14V160.33C109.48,160.33 109.7,160.56 109.7,160.84H109.9ZM109.19,160.14H107.38V160.33H109.19V160.14ZM107.28,160.24V161.39H107.48V160.24H107.28ZM108.64,161.3H107.38V161.49H108.64V161.3ZM108.74,169.98V161.39H108.55V169.98H108.74ZM99.45,170.07H108.64V169.88H99.45V170.07ZM99.36,161.39V169.98H99.55V161.39H99.36ZM100.72,161.3H99.45V161.49H100.72V161.3ZM100.82,161.39V160.24H100.62V161.39H100.82Z"
          android:fillColor="#EBEDF0"/>
    </group>
    <path
        android:pathData="M104.46,164.18L105.91,162.72C105.98,162.65 106.09,162.65 106.16,162.72L106.54,163.1C106.61,163.17 106.61,163.28 106.54,163.35L104.33,165.55C104.25,165.64 104.14,165.68 104.02,165.68C103.9,165.68 103.79,165.64 103.71,165.55L101.5,163.35C101.43,163.28 101.43,163.17 101.5,163.1L101.87,162.72C101.94,162.65 102.05,162.65 102.12,162.72L103.58,164.18L103.58,156.78C103.58,156.68 103.65,156.6 103.75,156.6L104.28,156.6C104.38,156.6 104.46,156.68 104.46,156.78L104.46,164.18Z"
        android:fillColor="#8C8C8C"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M104.46,164.18L104.41,164.18C104.41,164.19 104.42,164.21 104.44,164.22C104.46,164.23 104.48,164.22 104.49,164.21L104.46,164.18ZM105.91,162.72L105.88,162.69L105.88,162.69L105.91,162.72ZM106.16,162.72L106.2,162.69L106.2,162.69L106.16,162.72ZM106.54,163.1L106.57,163.06L106.57,163.06L106.54,163.1ZM106.54,163.35L106.57,163.38L106.57,163.38L106.54,163.35ZM104.33,165.55L104.3,165.52L104.3,165.52L104.33,165.55ZM104.02,165.68L104.02,165.63L104.02,165.63L104.02,165.68ZM103.71,165.55L103.67,165.59L103.67,165.59L103.71,165.55ZM101.5,163.35L101.53,163.31L101.53,163.31L101.5,163.35ZM101.5,163.1L101.46,163.06L101.46,163.06L101.5,163.1ZM101.87,162.72L101.91,162.76L101.91,162.76L101.87,162.72ZM102.12,162.72L102.16,162.69L102.16,162.69L102.12,162.72ZM103.58,164.18L103.54,164.21C103.56,164.22 103.58,164.23 103.6,164.22C103.61,164.21 103.63,164.2 103.63,164.18L103.58,164.18ZM103.58,156.78L103.62,156.78L103.62,156.78L103.58,156.78ZM103.75,156.6L103.75,156.55L103.75,156.55L103.75,156.6ZM104.28,156.6L104.28,156.65L104.28,156.65L104.28,156.6ZM104.46,156.78L104.51,156.78L104.51,156.78L104.46,156.78ZM104.49,164.21L105.95,162.76L105.88,162.69L104.43,164.14L104.49,164.21ZM105.95,162.76C106,162.71 106.08,162.71 106.13,162.76L106.2,162.69C106.11,162.6 105.96,162.6 105.88,162.69L105.95,162.76ZM106.13,162.76L106.5,163.13L106.57,163.06L106.2,162.69L106.13,162.76ZM106.5,163.13C106.55,163.18 106.55,163.26 106.5,163.31L106.57,163.38C106.66,163.29 106.66,163.15 106.57,163.06L106.5,163.13ZM106.5,163.31L104.3,165.52L104.36,165.59L106.57,163.38L106.5,163.31ZM104.3,165.52C104.22,165.59 104.12,165.63 104.02,165.63L104.02,165.73C104.15,165.73 104.27,165.68 104.36,165.59L104.3,165.52ZM104.02,165.63C103.91,165.63 103.81,165.59 103.74,165.52L103.67,165.59C103.76,165.68 103.89,165.73 104.02,165.73L104.02,165.63ZM103.74,165.52L101.53,163.31L101.46,163.38L103.67,165.59L103.74,165.52ZM101.53,163.31C101.48,163.26 101.48,163.18 101.53,163.13L101.46,163.06C101.38,163.15 101.38,163.29 101.46,163.38L101.53,163.31ZM101.53,163.13L101.91,162.76L101.84,162.69L101.46,163.06L101.53,163.13ZM101.91,162.76C101.96,162.71 102.04,162.71 102.09,162.76L102.16,162.69C102.07,162.6 101.93,162.6 101.84,162.69L101.91,162.76ZM102.09,162.76L103.54,164.21L103.61,164.14L102.16,162.69L102.09,162.76ZM103.63,164.18L103.62,156.78L103.53,156.78L103.53,164.18L103.63,164.18ZM103.62,156.78C103.62,156.71 103.68,156.65 103.75,156.65L103.75,156.55C103.63,156.55 103.53,156.65 103.53,156.78L103.62,156.78ZM103.75,156.65L104.28,156.65L104.28,156.55L103.75,156.55L103.75,156.65ZM104.28,156.65C104.35,156.65 104.41,156.71 104.41,156.78L104.51,156.78C104.51,156.65 104.41,156.55 104.28,156.55L104.28,156.65ZM104.41,156.78L104.41,164.18L104.51,164.18L104.51,156.78L104.41,156.78Z"
        android:fillColor="#8C8C8C"/>
    <path
        android:pathData="M137,151L151,151A6,6 0,0 1,157 157L157,171A6,6 0,0 1,151 177L137,177A6,6 0,0 1,131 171L131,157A6,6 0,0 1,137 151z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M177,151L191,151A6,6 0,0 1,197 157L197,171A6,6 0,0 1,191 177L177,177A6,6 0,0 1,171 171L171,157A6,6 0,0 1,177 151z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M217,151L231,151A6,6 0,0 1,237 157L237,171A6,6 0,0 1,231 177L217,177A6,6 0,0 1,211 171L211,157A6,6 0,0 1,217 151z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M103.64,163.64m-19.84,0a19.84,19.84 0,1 1,39.67 0a19.84,19.84 0,1 1,-39.67 0"
        android:strokeWidth="3.60674"
        android:fillColor="#00000000"
        android:strokeColor="#006AFF"/>
    <path
        android:pathData="M73.68,197.81L254.33,197.81"
        android:strokeWidth="0.376355"
        android:fillColor="#00000000"
        android:strokeColor="#E7E7E7"/>
  </group>
</vector>
