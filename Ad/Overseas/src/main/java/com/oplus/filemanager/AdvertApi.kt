/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: AdvertApi.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2024/6/6
 ** Author: keweiwei
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager

import android.app.Activity
import android.app.Application
import android.content.Context
import com.filemanager.common.utils.Log
import com.oplus.filemanager.compat.AdvertSwitchHelp
import com.oplus.filemanager.compat.brand.oppo.HotSplashAdvertController
import com.oplus.filemanager.compat.brand.oppo.AdvertController
import com.oplus.filemanager.interfaze.ad.IAdvertApi
import com.oplus.filemanager.riskctrl.RiskController
import com.oplus.filemanager.utils.AdHelper
import com.oplus.filemanager.utils.CarrierUtils
import com.opos.ad.overseas.base.BuildConfig

object AdvertApi : IAdvertApi {
    private const val TAG = "AdvertApi"

    override fun showAdSwitch(context: Context): Boolean {
        Log.d(TAG, "showAdSwitch")
        return AdvertSwitchHelp.showAdSwitch(context)
    }

    override fun getAdSwitchStatus(context: Context): Boolean {
        Log.d(TAG, "getAdSwitchStatus")
        return AdvertSwitchHelp.getAdSwitchStatus()
    }

    override fun setAdSwitchStatus(status: Boolean) {
        AdvertSwitchHelp.setAdSwitchStatus(status)
    }

    override fun getAdCloudShowAdSwitch(): Boolean {
        Log.d(TAG, "getAdCloudShowAdSwitch")
        return AdvertSwitchHelp.getAdCloudShowAdSwitch()
    }

    override fun updateAdCloud() {
        AdvertSwitchHelp.updateAdCloud()
    }

    override fun getRegion(context: Context): String {
        return AdvertSwitchHelp.getRegion(context)
    }

    override fun getRegionFromCloud(context: Context): String {
        return AdHelper.noAd
    }

    override fun isCarrierSupport(context: Context): Boolean {
        return CarrierUtils.isCarrierSupport(context)
    }

    override fun isAdInit(context: Context): Boolean {
        return AdvertController.isAdInit()
    }

    override fun isSwitchAndCloudConfigEnabled(isSplashAd: Boolean): Boolean {
        return com.oplus.filemanager.utils.isSwitchAndCloudConfigEnabled(isSplashAd)
    }

    override fun getAdSdkVersion(): String {
        return BuildConfig.SDK_VER_NAME
    }

    override fun initRiskSdk(app: Application) {
        RiskController.init(app)
    }

    override fun processAdStrategy(json: String) {
        RiskController.parseStrategy(json)
    }

    override fun isAdRemoved(advertId: String): Boolean {
        return RiskController.isAdModuleRemoved(advertId)
    }

    override fun onAppForeground(activity: Activity?) {
        HotSplashAdvertController.onAppForeground(activity)
    }

    override fun onAppBackground(activity: Activity?) {
        HotSplashAdvertController.onAppBackground(activity)
    }
}