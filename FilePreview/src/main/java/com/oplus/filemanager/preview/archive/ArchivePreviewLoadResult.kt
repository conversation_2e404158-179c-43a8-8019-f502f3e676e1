/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ArchivePreviewLoadResult.kt
 * Description:
 *     The result of loading archive file preview contents.
 *
 * Version: 1.0
 * Date: 2024-10-15
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-10-15   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.archive

import androidx.annotation.IntDef
import com.filemanager.common.base.BaseFileBean

internal data class ArchivePreviewLoadResult(
    @LoadResultCodes val resultCode: Int,
    val previewFile: BaseFileBean,
    val errorMessage: String? = null
) {
    companion object {
        const val PREVIEW_LOAD_RESULT_DONE = 0
        const val PREVIEW_LOAD_RESULT_IS_OTA = 1
        const val PREVIEW_LOAD_RESULT_OVER_LIMIT = 2
        const val PREVIEW_LOAD_RESULT_ENCRYPTED = 3
        const val PREVIEW_LOAD_RESULT_ERR = 4
    }

    @IntDef(
        PREVIEW_LOAD_RESULT_DONE,
        PREVIEW_LOAD_RESULT_IS_OTA,
        PREVIEW_LOAD_RESULT_OVER_LIMIT,
        PREVIEW_LOAD_RESULT_ENCRYPTED,
        PREVIEW_LOAD_RESULT_ERR
    )
    @Retention(AnnotationRetention.SOURCE)
    annotation class LoadResultCodes
}