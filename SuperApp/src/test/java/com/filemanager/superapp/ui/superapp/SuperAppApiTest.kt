package com.filemanager.superapp.ui.superapp

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.SparseArray
import android.view.MenuItem
import androidx.fragment.app.Fragment
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.utils.Injector
import com.filemanager.superappsource.helper.MainCategoryItemsBeanFactory
import com.filemanager.superappsource.helper.SuperAppHelper
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * SuperAppApi 的单元测试类
 * 用于测试 SuperAppApi 对象的各种功能方法
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SuperAppApiTest {

    // 定义测试所需的mock对象
    private lateinit var mockActivity: Activity
    private lateinit var mockContext: Context
    private lateinit var mockApplication: Application
    private lateinit var mockFragment: Fragment
    private lateinit var mockPreviewFragment: PreviewCombineFragment
    private lateinit var mockMain: IMain
    private lateinit var mockCategoryItem: MainCategoryItemsBean

    /**
     * 测试前的初始化方法
     * 创建所有需要的mock对象并初始化
     */
    @Before
    fun setUp() {
        clearAllMocks()
        mockActivity = mockk(relaxed = true)
        mockContext = mockk(relaxed = true)
        mockApplication = mockk(relaxed = true)
        mockFragment = mockk(relaxed = true)
        mockPreviewFragment = mockk(relaxed = true)
        mockMain = mockk(relaxed = true)
        mockCategoryItem = mockk(relaxed = true)

        // 模拟静态对象
        mockkObject(SuperAppHelper)
        mockkObject(Injector)
    }

    /**
     * 测试后的清理方法
     * 释放所有mock对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试 startSuperApp 方法
     * 验证是否能正确启动SuperApp Activity
     */
    @Test
    fun testStartSuperApp() {
        // 设置mock对象的返回值
        every { mockCategoryItem.name } returns "Test"
        every { mockCategoryItem.nameResId } returns 123
        every { mockCategoryItem.fileList } returns arrayOf("path1", "path2")
        every { mockCategoryItem.packageName } returns "com.test"
        every { mockCategoryItem.externalPath } returns "/storage/test"
        every { mockCategoryItem.sideCategoryType } returns 1
        every { mockCategoryItem.itemType } returns CategoryHelper.CATEGORY_DOWNLOAD

        // 调用被测试方法
        SuperAppApi.startSuperApp(mockActivity, mockCategoryItem)

        // 验证是否调用了startActivity方法
        verify {
            mockActivity.startActivity(any<Intent>())
        }
    }

    /**
     * 测试 startSuperApp 方法在Activity为null时的行为
     * 验证不会调用startActivity方法
     */
    @Test
    fun testStartSuperAppWithNullActivity() {
        SuperAppApi.startSuperApp(null, mockCategoryItem)
        verify(exactly = 0) { mockActivity.startActivity(any()) }
    }

    /**
     * 测试 startSuperAppFragment 方法
     * 验证是否能正确启动SuperApp Fragment
     */
    @Test
    fun testStartSuperAppFragment() {
        // 设置mock对象的返回值
        every { mockCategoryItem.name } returns "Test"
        every { mockCategoryItem.nameResId } returns 123
        every { mockCategoryItem.fileList } returns arrayOf("path1", "path2")
        every { mockCategoryItem.packageName } returns "com.test"
        every { mockCategoryItem.externalPath } returns "/storage/test"
        every { mockCategoryItem.sideCategoryType } returns 1
        every { mockCategoryItem.itemType } returns CategoryHelper.CATEGORY_DOWNLOAD
        every { Injector.injectFactory<IMain>() } returns mockMain
        every { mockMain.startFragment(any(), any(), any()) } returns true

        // 调用被测试方法
        SuperAppApi.startSuperAppFragment(mockActivity, mockCategoryItem)

        // 验证是否调用了startFragment方法
        verify {
            mockMain.startFragment(mockActivity, 1, any())
        }
    }

    /**
     * 测试 getDirDepthByItem 方法
     * 验证是否能根据itemType返回正确的目录深度
     */
    @Test
    fun testGetDirDepthByItem() {
        // 测试默认情况
        every { mockCategoryItem.itemType } returns CategoryHelper.CATEGORY_IMAGE
        assertEquals(0, SuperAppApi.getDirDepthByItem(mockCategoryItem))

        // 测试下载类型的情况
        every { mockCategoryItem.itemType } returns CategoryHelper.CATEGORY_DOWNLOAD
        assertEquals(2, SuperAppApi.getDirDepthByItem(mockCategoryItem))
    }

    /**
     * 测试 updateSupperAppPaths 方法
     * 验证是否能正确更新SuperApp路径
     */
    @Test
    fun testUpdateSupperAppPaths() {
        val expectedPaths = arrayListOf<String?>("path1", "path2")
        every { SuperAppHelper.updateSupperAppPaths(mockContext) } returns expectedPaths

        val result = SuperAppApi.updateSupperAppPaths(mockContext)
        assertEquals(expectedPaths, result)
    }

    /**
     * 测试 preloadSuperSharePreferences 方法
     * 验证是否调用了SuperAppHelper的对应方法
     */
    @Test
    fun testPreloadSuperSharePreferences() {
        SuperAppApi.preloadSuperSharePreferences(mockApplication)
        verify { SuperAppHelper.preloadSuperSharePreferences(mockApplication) }
    }

    /**
     * 测试 getMainSuperInitList 方法
     * 验证是否能正确获取主SuperApp初始化列表
     */
    @Test
    fun testGetMainSuperInitList() {
        val expectedList = arrayListOf<MainCategoryItemsBean>()
        every { SuperAppHelper.getMainSuperInitList() } returns expectedList

        val result = SuperAppApi.getMainSuperInitList()
        assertEquals(expectedList, result)
    }

    /**
     * 测试 isIgnoredPath 方法
     * 验证是否能正确判断路径是否被忽略
     */
    @Test
    fun testIsIgnoredPath() {
        val mockIgnoredPaths = mockk<SparseArray<String?>>()
        val mockCategoryItems = mockk<List<MainCategoryItemsBean>>()
        every {
            SuperAppHelper.isIgnoredPath(
                mockContext,
                1,
                "path",
                "internalPath",
                mockIgnoredPaths,
                mockCategoryItems
            )
        } returns true

        val result = SuperAppApi.isIgnoredPath(
            mockContext,
            1,
            "path",
            "internalPath",
            mockIgnoredPaths,
            mockCategoryItems
        )
        assertTrue(result)
    }

    /**
     * 测试 getFragment 方法
     * 验证是否能正确返回PreviewCombineFragment实例
     */
    @Test
    fun testGetFragment() {
        val fragment = SuperAppApi.getFragment(mockActivity)
        assertTrue(fragment is PreviewCombineFragment)
    }

    /**
     * 测试 onMenuItemSelected 方法
     * 验证菜单项选择事件是否能正确处理
     */
    @Test
    fun testOnMenuItemSelected() {
        val mockMenuItem = mockk<MenuItem>()
        every { mockPreviewFragment.onMenuItemSelected(mockMenuItem) } returns true

        // 测试PreviewCombineFragment的情况
        val result = SuperAppApi.onMenuItemSelected(mockPreviewFragment, mockMenuItem)
        assertTrue(result)

        // 测试非PreviewCombineFragment的情况
        val result2 = SuperAppApi.onMenuItemSelected(mockFragment, mockMenuItem)
        assertFalse(result2)
    }

    /**
     * 测试 pressBack 方法
     * 验证返回按钮事件是否能正确处理
     */
    @Test
    fun testPressBack() {
        every { mockPreviewFragment.pressBack() } returns true

        // 测试PreviewCombineFragment的情况
        val result = SuperAppApi.pressBack(mockPreviewFragment)
        assertTrue(result)

        // 测试非PreviewCombineFragment的情况
        val result2 = SuperAppApi.pressBack(mockFragment)
        assertFalse(result2)
    }
}