/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : OPPOHomePageAdMgrTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/3/22 20:09
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2023/3/22       1.0      create
 **********************************************************************/
package com.oplus.filemanager.compat.brand.oppo

import android.app.Activity
import android.view.View
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test

class OPPOHomePageAdMgrTest {

    @Test
    fun testSetContainer() {
        val homePageAdMgr = spyk<OPPOHomePageAdMgr>()
        val view = mockk<View>()
        homePageAdMgr.setContainer(view, 0)
        verify { homePageAdMgr.setContainer(any(), any()) }
    }

    @Ignore
    fun testRequestMainAd() {
        val homePageAdMgr = spyk<OPPOHomePageAdMgr>()
        val activity = mockk<Activity>()
        homePageAdMgr.requestMainAd(activity)
        verify { homePageAdMgr.requestMainAd(any()) }
    }

    @Test
    fun testGetEntryView() {
        val homePageAdMgr = OPPOHomePageAdMgr()
        Assert.assertNull(homePageAdMgr.getEntryView(""))
    }

    @Test
    fun testEventOnDestroy() {
        val homePageAdMgr = spyk<OPPOHomePageAdMgr>()
        homePageAdMgr.eventOnDestroy()
        verify { homePageAdMgr.eventOnDestroy() }
    }
}