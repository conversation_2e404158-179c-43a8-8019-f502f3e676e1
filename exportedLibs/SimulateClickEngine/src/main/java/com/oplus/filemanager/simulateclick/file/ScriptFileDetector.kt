/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScriptFileDetector.kt
 * Description:
 *     To detect script files and choose for request.
 *
 * Version: 1.0
 * Date: 2024-06-04
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-06-04   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.file

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.oplus.filemanager.simulateclick.engine.Script
import com.oplus.filemanager.simulateclick.engine.ScriptGsonBuilder
import com.oplus.filemanager.simulateclick.engine.utils.Log
import com.oplus.filemanager.simulateclick.engine.utils.matchAppVersion
import com.oplus.filemanager.simulateclick.engine.utils.matchHasSkill
import com.oplus.filemanager.simulateclick.engine.utils.matchScreenDisplay
import com.oplus.filemanager.simulateclick.engine.utils.matchScreenOrientation
import com.oplus.filemanager.simulateclick.engine.utils.matchScreenRotation
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStream
import java.io.InputStreamReader

private const val BUILTIN_ROOT_TAG = "builtin!/assets"

internal sealed class ScriptFileDetector {

    protected abstract val logTag: String

    private val gson by lazy { ScriptGsonBuilder().create() }

    fun detectScript(context: Context, packageName: String, skillName: String): Script? {
        val found = findScriptsForApp(context, packageName)
        Log.d(logTag, "detectScript: found=$found")
        val detectScripts = mutableSetOf<Script>()
        found.forEach {
            val script = readScript(context, it) ?: return@forEach
            if (!script.matchFlow(context, packageName, skillName)) {
                return@forEach
            }
            Log.d(logTag, "detectScript: detect=$it")
            detectScripts.add(script)
        }
        return detectScripts.maxByOrNull { it.scriptVersion }.also {
            Log.d(logTag, "detectScript: choose=${it?.filePath}")
        }
    }

    @VisibleForTesting
    internal fun findScriptsForApp(context: Context, packageName: String): Collection<String> {
        val dirPath = ScriptFilePathUtils.getAppScriptDirPath(packageName)
        val fileNames = kotlin.runCatching {
            listScriptFileInDir(context, dirPath)
        }.onFailure {
            Log.e(logTag, "findScriptsForApp: failed to list scripts, packageName=$packageName")
        }.getOrNull() ?: emptyArray()
        val filePaths = mutableSetOf<String>()
        fileNames.forEach {
            val filePath = "$dirPath/$it".replace('/', File.separatorChar)
            filePaths.add(filePath)
        }
        return filePaths
    }

    @VisibleForTesting
    internal fun readScript(context: Context, scriptPath: String): Script? {
        val scriptInput = runCatching {
            openScriptFile(context, scriptPath)
        }.onFailure {
            Log.e(logTag, "readScript: failed to read script, path=$scriptPath")
        }.getOrNull() ?: return null
        val scriptReader = InputStreamReader(scriptInput)
        return runCatching {
            scriptReader.use {
                gson.fromJson(it, Script::class.java)
            }
        }.onFailure {
            Log.e(logTag, "readScript: failed to from JSON, path=$scriptPath")
        }.getOrNull()?.also {
            it.filePath = getFullScriptPath(context, scriptPath)
        }
    }

    @VisibleForTesting
    internal fun Script.matchFlow(
        context: Context,
        packageName: String,
        skillName: String
    ): Boolean {
        if (!matchHasSkill(skillName)) {
            return false
        }
        if (!matchAppVersion(context, packageName)) {
            return false
        }
        if (!matchScreenDisplay(context)) {
            return false
        }
        if (!matchScreenOrientation(context)) {
            return false
        }
        if (!matchScreenRotation(context)) {
            return false
        }
        return true
    }

    /**
     * Implement how to list file names in [dirPath]
     */
    @Throws(IOException::class)
    protected abstract fun listScriptFileInDir(context: Context, dirPath: String): Array<String>?

    /**
     * Implement how to open file which path is [filePath]
     */
    @Throws(IOException::class)
    protected abstract fun openScriptFile(context: Context, filePath: String): InputStream?

    /**
     * Implement to get full path for script file with [scriptPath]
     * Only for print info or log.
     */
    protected abstract fun getFullScriptPath(context: Context, scriptPath: String): String
}

internal class BuiltinScriptDetector : ScriptFileDetector() {
    override val logTag: String = "BuiltinScriptDetector"

    @Throws(IOException::class)
    override fun listScriptFileInDir(context: Context, dirPath: String): Array<String>? =
        context.assets.list(dirPath)

    @Throws(IOException::class)
    override fun openScriptFile(context: Context, filePath: String): InputStream =
        context.assets.open(filePath)

    override fun getFullScriptPath(context: Context, scriptPath: String): String =
        "$BUILTIN_ROOT_TAG/$scriptPath".replace('/', File.separatorChar)
}

internal open class RemoteScriptDetector : ScriptFileDetector() {
    override val logTag: String = "RemoteScriptDetector"

    @Throws(IOException::class)
    override fun listScriptFileInDir(context: Context, dirPath: String): Array<String>? {
        val dir = getScriptFileOrDir(context, dirPath)
        if (!dir.run { exists() && isDirectory }) {
            return null
        }
        return dir.list()
    }

    @Throws(IOException::class)
    override fun openScriptFile(context: Context, filePath: String): InputStream? {
        val scriptFile = getScriptFileOrDir(context, filePath)
        if (!scriptFile.run { exists() && isFile }) {
            return null
        }
        return FileInputStream(scriptFile)
    }

    override fun getFullScriptPath(context: Context, scriptPath: String): String =
        getScriptFileOrDir(context, scriptPath).absolutePath

    protected open fun getScriptFileOrDir(context: Context, path: String): File =
        ScriptFilePathUtils.getRemoteScriptFileOrDir(context, path)
}

internal class DebugScriptDetector : RemoteScriptDetector() {
    override val logTag: String = "DebugScriptDetector"

    override fun getScriptFileOrDir(context: Context, path: String): File =
        ScriptFilePathUtils.getDebugScriptFileOrDir(context, path)
}