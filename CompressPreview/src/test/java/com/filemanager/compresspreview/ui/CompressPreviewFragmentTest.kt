package com.filemanager.compresspreview.ui

import android.content.Context
import android.os.Bundle
import android.view.MenuItem
import androidx.fragment.app.FragmentActivity
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.compresspreview.R
import com.filemanager.compresspreview.utils.DetachableClickListener
import com.filemanager.fileoperate.compress.CompressConfirmDialog
import com.filemanager.fileoperate.compress.CompressConfirmType
import com.filemanager.fileoperate.decompress.BaseDecompressFile
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.slot
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import org.robolectric.annotation.LooperMode
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import java.lang.reflect.Field
import com.filemanager.common.utils.Utils
import io.mockk.mockkStatic
import io.mockk.unmockkConstructor
import io.mockk.verify
import io.mockk.mockkConstructor
import kotlin.test.assertEquals

/**
 * CompressPreviewFragment的单元测试类
 * 用于测试压缩预览Fragment的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(manifest = Config.NONE, sdk = [29])
@LooperMode(LooperMode.Mode.PAUSED)
class CompressPreviewFragmentTest {

    // 被测Fragment实例
    private lateinit var fragment: CompressPreviewFragment
    // 模拟的ViewModel
    private lateinit var mockViewModel: CompressPreviewViewModel
    // 测试用的文件路径
    private val testPath = "/test/archive.zip"
    // 测试用的Activity
    private lateinit var activity: FragmentActivity

    /**
     * 测试前的初始化方法
     * 创建测试环境并初始化测试对象
     */
    @Before
    fun setup() {
        // 创建模拟的ViewModel
        mockViewModel = mockk(relaxed = true)
        // 创建并启动测试Activity
        activity = Robolectric.buildActivity(FragmentActivity::class.java).create().start().resume().get()

        // 准备测试参数Bundle
        val args = Bundle().apply {
            putString(KtConstants.P_CURRENT_PATH, testPath)  // 设置当前路径
            putString(KtConstants.P_PREVIEW_ROOT_TITLE, "Archive")  // 设置预览根标题
            putBoolean(KtConstants.P_INIT_LOAD, true)  // 设置初始加载标志
        }

        // 创建Fragment实例并设置参数
        fragment = CompressPreviewFragment()
        fragment.arguments = args
        
        // 使用反射设置Fragment中的ViewModel字段
        val superClass = CompressPreviewFragment::class.java.superclass
        val viewModelField: Field = superClass
            .getDeclaredField("fragmentViewModel")
            .apply { isAccessible = true }
        viewModelField.set(fragment, mockViewModel)
        
        // 模拟Fragment附加到Activity
        fragment.onAttach(activity as Context)

        // 使用反射设置baseVMActivity字段
        val baseVMActivityField = fragment.javaClass.superclass.superclass?.getDeclaredField("baseVMActivity")
        baseVMActivityField?.apply {
            isAccessible = true
            set(fragment, mockk<BaseVMActivity>(relaxed = true))
        }
    }

    /**
     * 测试后的清理方法
     * 释放资源并清理测试环境
     */
    @After
    fun tearDown() {
        // 清理可能的静态状态和Mock对象
    }

    /**
     * 测试pressBack方法在普通模式下的行为
     * 验证当不在选择模式时，返回false并结束Activity
     */
    @Test
    fun `pressBack in normal mode should finish activity`() {
        // 模拟ViewModel的pressBack方法返回false，表示不在选择模式
        every { mockViewModel.pressBack() } returns false

        // 执行pressBack方法
        val result = fragment.pressBack()

        // 验证：根据源文件逻辑，当activity不是CompressPreviewActivity时应返回true
        assertTrue(result)
    }

    /**
     * 测试onMenuItemSelected方法处理编辑模式的情况
     * 验证点击编辑菜单项会切换到选择模式
     */
    @Test
    fun `onMenuItemSelected should handle edit mode`() {
        // 准备：模拟静态方法Utils.isQuickClick返回false，避免快速点击检查
        mockkStatic(Utils::class)
        every { Utils.isQuickClick(any()) } returns false

        // 创建模拟的菜单项，设置ID为编辑按钮
        val editItem = mockk<MenuItem>().apply {
            every { itemId } returns R.id.actionbar_edit
        }

        // 执行菜单项选择方法
        val result = fragment.onMenuItemSelected(editItem)

        // 验证返回true表示处理了事件，并验证调用了切换到选择模式的方法
        assertTrue(result)
        verify { mockViewModel.changeListMode(KtConstants.LIST_SELECTED_MODE) }
    }
}