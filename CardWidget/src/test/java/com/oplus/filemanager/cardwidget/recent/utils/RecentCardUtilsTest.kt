package com.oplus.filemanager.cardwidget.recent.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.test.platform.app.InstrumentationRegistry
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.PreferencesUtils
import com.google.gson.Gson
import com.oplus.filemanager.cardwidget.recent.data.RecentCard
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * RecentCardUtils的单元测试类
 * 使用Robolectric和MockK框架进行测试
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RecentCardUtilsTest {

    // 模拟的Context对象
    @MockK
    private lateinit var mockContext: Context

    // 模拟的SharedPreferences对象
    @MockK
    private lateinit var mockSharedPreferences: SharedPreferences

    // 模拟的SharedPreferences.Editor对象
    @MockK
    private lateinit var mockEditor: SharedPreferences.Editor

    // 被测试的RecentCardUtils实例
    private lateinit var recentCardUtils: RecentCardUtils
    // 测试用的RecentCard数据
    private val testCard = RecentCard("testId", 10, "testName", 1)

    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 模拟MyApplication和PreferencesUtils
     * 3. 设置各种模拟行为
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkObject(MyApplication)
        mockkObject(PreferencesUtils)
        mockkStatic(PreferencesUtils::class)

        // 设置MyApplication的模拟行为
        every { MyApplication.sAppContext } returns mockContext
        // 设置PreferencesUtils的模拟行为
        every { PreferencesUtils.getPreferences(any()) } returns mockSharedPreferences
        // 设置SharedPreferences的模拟行为
        every { mockSharedPreferences.edit() } returns mockEditor
        every { mockEditor.remove(any()) } returns mockEditor
        every { mockEditor.clear() } returns mockEditor
        every { mockEditor.apply() } just Runs
        every { mockSharedPreferences.all } returns mapOf("testId" to Gson().toJson(testCard))
        every { mockSharedPreferences.getString(any(), any()) } returns Gson().toJson(testCard)

        // 获取RecentCardUtils的单例实例
        recentCardUtils = RecentCardUtils.instance
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试saveCard方法
     * 验证是否调用了PreferencesUtils.put方法保存数据
     */
    @Test
    fun testSaveCard() {
        recentCardUtils.saveCard(testCard)
        verify { PreferencesUtils.put("filemanager_recent_card", testCard.cardId, Gson().toJson(testCard)) }
    }

    /**
     * 测试deleteCardById方法
     * 验证是否调用了Editor.remove和apply方法
     */
    @Test
    fun testDeleteCardById() {
        recentCardUtils.deleteCardById("testId")
        verify { mockEditor.remove("testId") }
        verify { mockEditor.apply() }
    }

    /**
     * 测试getCacheCardDataByCardId方法
     * 验证返回的RecentCard数据是否正确
     */
    @Test
    fun testGetCacheCardDataByCardId() {
        every { PreferencesUtils.getString(mockContext, any(), any(), any()) } returns Gson().toJson(testCard)
        val result = recentCardUtils.getCacheCardDataByCardId("testId")
        assertEquals(testCard.cardId, result?.cardId)
        assertEquals(testCard.name, result?.name)
    }

    /**
     * 测试getCacheCardDataByCardId方法
     * 当返回的json为null时，验证是否返回null
     */
    @Test
    fun testGetCacheCardDataByCardId_WithNullJson() {
        every { PreferencesUtils.getString(mockContext, any(), any(), any()) } returns null
        val result = recentCardUtils.getCacheCardDataByCardId("testId")
        assertNull(result)
    }

    /**
     * 测试getCacheLists方法
     * 当缓存中有数据时，验证返回的列表是否正确
     */
    @Test
    fun testGetCacheLists_WithCache() {
        recentCardUtils.saveCard(testCard)
        val result = recentCardUtils.getCacheLists()
        assertTrue(result.isNotEmpty())
        assertEquals(testCard.cardId, result[0].cardId)
    }

    /**
     * 测试getCacheLists方法
     * 当缓存为空时，验证是否能从SharedPreferences中重新加载数据
     */
    @Test
    fun testGetCacheLists_WithoutCache() {
        recentCardUtils.clearAll()
        every { mockSharedPreferences.all } returns mapOf("testId" to Gson().toJson(testCard))
        val result = recentCardUtils.getCacheLists()
        assertTrue(result.isNotEmpty())
        assertEquals(testCard.cardId, result[0].cardId)
    }

    /**
     * 测试switchCategory方法
     * 验证是否调用了PreferencesUtils.put方法更新数据
     */
    @Test
    fun testSwitchCategory() {
        every { PreferencesUtils.getString(mockContext, any(), any(), any()) } returns Gson().toJson(testCard)
        recentCardUtils.switchCategory("testId", 2, "newName")
        verify { PreferencesUtils.put("filemanager_recent_card", "testId", any<String>()) }
    }

    /**
     * 测试clearAll方法
     * 验证是否调用了Editor.clear和apply方法
     * 并验证缓存列表是否为空
     */
    @Test
    fun testClearAll() {
        recentCardUtils.clearAll()
        verify { mockEditor.clear() }
        verify { mockEditor.apply() }
        every { mockSharedPreferences.all } returns emptyMap()
        assertTrue(recentCardUtils.getCacheLists().isEmpty())
    }
}