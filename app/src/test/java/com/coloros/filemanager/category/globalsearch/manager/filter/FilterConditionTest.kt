/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FilterConditionTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/29       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.category.globalsearch.manager.filter

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.FILTER_DOC_AI
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.FILTER_DOC_CAD
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.FILTER_DOC_IWORK
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.FILTER_DOC_MD
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.FILTER_DOC_OFD
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.FILTER_DOC_PSD
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.FILTER_DOC_TXT
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.FILTER_DOC_VISIO
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.FILTER_DOC_XMIND
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterCondition
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module

class FilterConditionTest {

    private lateinit var context: Context

    private val documentExtensionType = mockk<IDocumentExtensionType>()
    private val koinApp = koinApplication {
        modules(module {
            single { documentExtensionType }
        })
    }

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)

        startKoin(koinApp)
    }

    @After
    fun tearDown() {
        unmockkAll()
        stopKoin()
    }

    @Test
    fun testParseCompressFilterItems() {
        val searchFilterCompressExt = arrayOf("ZIP", "RAR", "7Z", "JAR")
        every {
            appContext.resources.getStringArray(com.filemanager.common.R.array.search_filter_compress_ext)
        } returns searchFilterCompressExt
        val filterItemAllCondition = FilterCondition(FilterConstants.FILTER_COMPRESS, "")
        val resultItems = mutableListOf<FilterItem>().apply {
            add(FilterItem(FilterConstants.FILTER_COMPRESS_ZIP, FilterCondition(FilterConstants.FILTER_COMPRESS, ""), "ZIP"))
            add(FilterItem(FilterConstants.FILTER_COMPRESS_RAR, FilterCondition(FilterConstants.FILTER_COMPRESS, ""), "RAR"))
            add(FilterItem(FilterConstants.FILTER_COMPRESS_7ZIP, FilterCondition(FilterConstants.FILTER_COMPRESS, ""), "7Z"))
            add(FilterItem(FilterConstants.FILTER_COMPRESS_JAR, FilterCondition(FilterConstants.FILTER_COMPRESS, ""), "JAR"))
        }
        filterItemAllCondition.parseFilterItems(FilterConstants.FILTER_ITEM_ALL)
        Assert.assertEquals(resultItems, filterItemAllCondition.items)

        // filter zip
        val filterZipCondition = FilterCondition(FilterConstants.FILTER_COMPRESS, "")
        val zipItemResult = mutableListOf<FilterItem>().apply {
            add(FilterItem(FilterConstants.FILTER_COMPRESS_ZIP, FilterCondition(FilterConstants.FILTER_COMPRESS, ""), "ZIP"))
        }
        filterZipCondition.parseFilterItems(FilterConstants.FILTER_COMPRESS_ZIP)
        Assert.assertEquals(zipItemResult, filterZipCondition.items)


        // filter rar
        val filterRarCondition = FilterCondition(FilterConstants.FILTER_COMPRESS, "")
        val rarItemResult = mutableListOf<FilterItem>().apply {
            add(FilterItem(FilterConstants.FILTER_COMPRESS_RAR, FilterCondition(FilterConstants.FILTER_COMPRESS, ""), "RAR"))
        }
        filterRarCondition.parseFilterItems(FilterConstants.FILTER_COMPRESS_RAR)
        Assert.assertEquals(rarItemResult, filterRarCondition.items)

        // filter jar
        val filterJarCondition = FilterCondition(FilterConstants.FILTER_COMPRESS, "")
        val jarItemResult = mutableListOf<FilterItem>().apply {
            add(FilterItem(FilterConstants.FILTER_COMPRESS_JAR, FilterCondition(FilterConstants.FILTER_COMPRESS, ""), "JAR"))
        }
        filterJarCondition.parseFilterItems(FilterConstants.FILTER_COMPRESS_JAR)
        Assert.assertEquals(jarItemResult, filterJarCondition.items)

        // filter 7z
        val filter7zCondition = FilterCondition(FilterConstants.FILTER_COMPRESS, "")
        val p7zItemResult = mutableListOf<FilterItem>().apply {
            add(FilterItem(FilterConstants.FILTER_COMPRESS_7ZIP, FilterCondition(FilterConstants.FILTER_COMPRESS, ""), "7Z"))
        }
        filter7zCondition.parseFilterItems(FilterConstants.FILTER_COMPRESS_7ZIP)
        Assert.assertEquals(p7zItemResult, filter7zCondition.items)
    }

    @Test
    fun testParseDocFilterItems() {
        val searchFilterDocExt =
            arrayOf("DOC", "XLS", "PPT", "PDF", "OFD", "IWORK", "XMIND", "VISIO", "TXT", "CAD", "PSD", "AI", "MD")
        val array = intArrayOf(
            FilterConstants.FILTER_DOC_DOC,
            FilterConstants.FILTER_DOC_XLS,
            FilterConstants.FILTER_DOC_PPT,
            FilterConstants.FILTER_DOC_PDF,
            FilterConstants.FILTER_DOC_OFD,
            FILTER_DOC_IWORK,
            FILTER_DOC_XMIND,
            FILTER_DOC_VISIO,
            FILTER_DOC_TXT,
            FILTER_DOC_CAD,
            FILTER_DOC_PSD,
            FILTER_DOC_AI,
            FILTER_DOC_MD
        )
        every { documentExtensionType.getSearchArrayForDoc(any()) }.returns(Pair(array, searchFilterDocExt))
        val filterItemAllCondition = FilterCondition(FilterConstants.FILTER_DOC, "")
        val resultItems = mutableListOf<FilterItem>().apply {
            add(FilterItem(FilterConstants.FILTER_DOC_DOC, FilterCondition(FilterConstants.FILTER_DOC, ""), "DOC"))
            add(FilterItem(FilterConstants.FILTER_DOC_XLS, FilterCondition(FilterConstants.FILTER_DOC, ""), "XLS"))
            add(FilterItem(FilterConstants.FILTER_DOC_PPT, FilterCondition(FilterConstants.FILTER_DOC, ""), "PPT"))
            add(FilterItem(FilterConstants.FILTER_DOC_PDF, FilterCondition(FilterConstants.FILTER_DOC, ""), "PDF"))
            add(FilterItem(FILTER_DOC_OFD, FilterCondition(FilterConstants.FILTER_DOC, ""), "OFD"))
            add(FilterItem(FILTER_DOC_IWORK, FilterCondition(FilterConstants.FILTER_DOC, ""), "IWORK"))
            add(FilterItem(FILTER_DOC_XMIND, FilterCondition(FilterConstants.FILTER_DOC, ""), "XMIND"))
            add(FilterItem(FILTER_DOC_VISIO, FilterCondition(FilterConstants.FILTER_DOC, ""), "VISIO"))
            add(FilterItem(FILTER_DOC_TXT, FilterCondition(FilterConstants.FILTER_DOC, ""), "TXT"))
            add(FilterItem(FILTER_DOC_CAD, FilterCondition(FilterConstants.FILTER_DOC, ""), "CAD"))
            add(FilterItem(FILTER_DOC_PSD, FilterCondition(FilterConstants.FILTER_DOC, ""), "PSD"))
            add(FilterItem(FILTER_DOC_AI, FilterCondition(FilterConstants.FILTER_DOC, ""), "AI"))
            add(FilterItem(FILTER_DOC_MD, FilterCondition(FilterConstants.FILTER_DOC, ""), "MD"))
        }
        filterItemAllCondition.parseFilterItems(FilterConstants.FILTER_ITEM_ALL)
        Assert.assertEquals(resultItems, filterItemAllCondition.items)

        // filter doc
        val filterDocCondition = FilterCondition(FilterConstants.FILTER_DOC, "")
        val docItemResult = mutableListOf<FilterItem>().apply {
            add(FilterItem(FilterConstants.FILTER_DOC_DOC, FilterCondition(FilterConstants.FILTER_DOC, ""), "DOC"))
        }
        filterDocCondition.parseFilterItems(FilterConstants.FILTER_DOC_DOC)
        Assert.assertEquals(docItemResult, filterDocCondition.items)

        // filter xls
        val filterXlsCondition = FilterCondition(FilterConstants.FILTER_DOC, "")
        val xlsItemResult = mutableListOf<FilterItem>().apply {
            add(FilterItem(FilterConstants.FILTER_DOC_XLS, FilterCondition(FilterConstants.FILTER_DOC, ""), "XLS"))
        }
        filterXlsCondition.parseFilterItems(FilterConstants.FILTER_DOC_XLS)
        Assert.assertEquals(xlsItemResult, filterXlsCondition.items)

        // filter ppt
        val filterPPTCondition = FilterCondition(FilterConstants.FILTER_DOC, "")
        val pptItemResult = mutableListOf<FilterItem>().apply {
            add(FilterItem(FilterConstants.FILTER_DOC_PPT, FilterCondition(FilterConstants.FILTER_DOC, ""), "PPT"))
        }
        filterPPTCondition.parseFilterItems(FilterConstants.FILTER_DOC_PPT)
        Assert.assertEquals(pptItemResult, filterPPTCondition.items)

        // filter pdf
        val filterPdfCondition = FilterCondition(FilterConstants.FILTER_DOC, "")
        val pdfItemResult = mutableListOf<FilterItem>().apply {
            add(FilterItem(FilterConstants.FILTER_DOC_PDF, FilterCondition(FilterConstants.FILTER_DOC, ""), "PDF"))
        }
        filterPdfCondition.parseFilterItems(FilterConstants.FILTER_DOC_PDF)
        Assert.assertEquals(pdfItemResult, filterPdfCondition.items)

        // filter txt
        val filterTxtCondition = FilterCondition(FilterConstants.FILTER_DOC, "")
        val txtItemResult = mutableListOf<FilterItem>().apply {
            add(FilterItem(FilterConstants.FILTER_DOC_TXT, FilterCondition(FilterConstants.FILTER_DOC, ""), "TXT"))
        }
        filterTxtCondition.parseFilterItems(FilterConstants.FILTER_DOC_TXT)
        Assert.assertEquals(txtItemResult, filterTxtCondition.items)
    }
}