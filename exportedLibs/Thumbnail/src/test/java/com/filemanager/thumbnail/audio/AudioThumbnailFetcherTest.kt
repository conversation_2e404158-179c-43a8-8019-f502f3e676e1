package com.filemanager.thumbnail.audio

import android.media.MediaMetadataRetriever
import android.util.Log
import com.bumptech.glide.Priority
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.data.DataFetcher
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.robolectric.annotation.Config

/**
 * AudioThumbnailFetcher的单元测试类
 * 用于测试音频缩略图获取器的各项功能
 */
@Config(sdk = [28])
class AudioThumbnailFetcherTest {

    // 测试用变量声明
    private lateinit var fetcher: AudioThumbnailFetcher
    private lateinit var mockModel: AudioThumbnailNew
    private lateinit var mockCallback: DataFetcher.DataCallback<AudioThumbnailResultByteArray>
    private lateinit var mockRetriever: MediaMetadataRetriever

    /**
     * 测试前的初始化方法
     * 创建所有测试所需的mock对象
     */
    @Before
    fun setUp() {
        // 创建mock对象
        mockModel = mockk()
        mockCallback = mockk(relaxed = true)
        mockRetriever = mockk()
        // mock静态方法和类
        mockkStatic(MediaMetadataRetriever::class)
        mockkStatic("com.filemanager.thumbnail.utils.Log")
    }

    /**
     * 测试后的清理方法
     * 解除所有mock
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试getDataClass方法
     * 验证返回的数据类是否为AudioThumbnailResultByteArray
     */
    @Test
    fun `getDataClass should return AudioThumbnailResultByteArray`() {
        fetcher = AudioThumbnailFetcher(mockModel, 100, 100)
        assertEquals(AudioThumbnailResultByteArray::class.java, fetcher.dataClass)
    }

    /**
     * 测试getDataSource方法
     * 验证返回的数据源是否为REMOTE
     */
    @Test
    fun `getDataSource should return REMOTE`() {
        fetcher = AudioThumbnailFetcher(mockModel, 100, 100)
        assertEquals(DataSource.REMOTE, fetcher.dataSource)
    }

    /**
     * 测试cancel方法
     * 验证调用cancel后mIsCancelled标志是否被设置为true
     */
    @Test
    fun `cancel should set mIsCancelled to true`() {
        fetcher = AudioThumbnailFetcher(mockModel, 100, 100)
        fetcher.cancel()
        assertEquals(true, fetcher.getIsCancelled())
    }

    /**
     * 辅助扩展方法
     * 用于访问AudioThumbnailFetcher中的私有字段mIsCancelled
     */
    private fun AudioThumbnailFetcher.getIsCancelled(): Boolean {
        // 通过反射获取私有字段
        val field = this.javaClass.getDeclaredField("mIsCancelled")
        field.isAccessible = true
        return field.getBoolean(this)
    }
}