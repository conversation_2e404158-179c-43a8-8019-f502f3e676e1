/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DownloadStatusCard
 * * Description : 下载传输状态的卡片，包括下载中，下载失败，下载完成
 * * Version     : 1.0
 * * Date        : 2024/12/30
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.download

import android.os.Bundle
import androidx.annotation.Keep
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.GsonUtil
import com.oplus.filemanager.cardwidget.ext.getFileIconPNG
import com.oplus.filemanager.interfaze.remotedevice.DownloadTransferCard

/**
 * 下载完成
 */
@Keep
class DownloadCompleteCard(files: List<String>, val count: Int, val destPath: String) :
    DownloadTransferCard(STATUS_DOWNLOADING, PROGRESS_MAX, files) {

    init {
        fileIcon = getFileIconPNG(files)
        val resources = MyApplication.appContext.resources
        title = resources.getString(com.filemanager.common.R.string.download_success_notify)
        subTitle = resources.getQuantityString(com.filemanager.common.R.plurals.downloaded_file_count, count, count)
    }

    override fun pageId(): String {
        return "pages/download_completed"
    }
}

/**
 * 下载失败的卡片
 */
@Keep
class DownloadFailCard(files: List<String>, val failMsg: String) : DownloadTransferCard(STATUS_FAIL, 0, files) {

    init {
        fileIcon = getFileIconPNG(files)
        val resources = MyApplication.appContext.resources
        title = resources.getString(com.filemanager.common.R.string.download_failure_toast)
        subTitle = failMsg
    }

    override fun pageId(): String {
        return "pages/download_fail"
    }
}

/**
 * 下载中的卡片
 */
@Keep
class DownloadingCard(val taskId: Int, progress: Int, files: List<String>, val count: Int) :
    DownloadTransferCard(STATUS_DOWNLOADING, progress, files) {

    init {
        fileIcon = getFileIconPNG(files)
        val resources = MyApplication.appContext.resources
        title = resources.getString(com.filemanager.common.R.string.downloading_dialog_title)
        subTitle = resources.getQuantityString(com.filemanager.common.R.plurals.download_file_count, count, count)
    }

    override fun pageId(): String {
        return "pages/downloading"
    }
}

/**
 * 根据bundle构造 卡片数据
 */
fun createDownloadCard(data: Bundle): DownloadTransferCard {
    if (!data.containsKey("status") || !data.containsKey("progress")) {
        throw IllegalArgumentException("data is wrong")
    }
    val status = data.getInt("status")
    val progress = data.getInt("progress", 0)
    val fileStr = data.getString("files")
    val files = GsonUtil.toList<String>(fileStr, String::class.java)
    when (status) {
        DownloadTransferCard.STATUS_DOWNLOADING -> {
            val count = data.getInt("count", files.size)
            if (progress == DownloadTransferCard.PROGRESS_MAX) {
                val destPath = data.getString("destPath") ?: ""
                return DownloadCompleteCard(files, count, destPath)
            } else {
                val taskId = data.getInt("taskId", 0)
                return DownloadingCard(taskId, progress, files, count)
            }
        }

        DownloadTransferCard.STATUS_FAIL -> {
            val failMsg = data.getString("failMsg", "")
            return DownloadFailCard(files, failMsg)
        }
    }
    return DownloadingCard(0, progress, files, files.size)
}