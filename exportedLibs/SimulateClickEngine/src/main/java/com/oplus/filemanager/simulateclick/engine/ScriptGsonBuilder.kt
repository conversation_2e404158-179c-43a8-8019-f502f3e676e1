/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScriptGsonBuilder.kt
 * Description:
 *     The customized GsonBuilder for convert script json
 *
 * Version: 1.0
 * Date: 2024-06-04
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON>ia<PERSON>.<EMAIL>    2024-06-04   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.oplus.filemanager.simulateclick.engine.value.AccessibilityActionJsonAdapter
import com.oplus.filemanager.simulateclick.engine.value.ScreenDisplayFlag
import com.oplus.filemanager.simulateclick.engine.value.ScreenOrientationFlag
import com.oplus.filemanager.simulateclick.engine.value.ScreenRotationFlag

internal class ScriptGsonBuilder {

    private val builder = GsonBuilder()

    init {
        ScreenDisplayFlag.registerJsonAdapter(builder)
        ScreenOrientationFlag.registerJsonAdapter(builder)
        ScreenRotationFlag.registerJsonAdapter(builder)
        AccessibilityActionJsonAdapter.registerJsonAdapter(builder)
    }

    fun create(): Gson = builder.create()
}