# NewFileDragDropShadow 放大淡出动画使用说明

## 功能概述

为 `NewFileDragDropShadow` 添加了放大淡出动画效果，在拖拽结束时提供视觉反馈。动画包含：
- **放大效果**：从 1.0 倍缩放到 1.2 倍
- **淡出效果**：从完全不透明渐变到完全透明
- **动画时长**：300ms

## 主要方法

### 1. 启动动画
```kotlin
// 基础启动方法
shadowBuilder?.startScaleFadeAnimation()

// 拖拽结束时启动（推荐）
shadowBuilder?.startScaleFadeOnDragEnd()
```

### 2. 停止动画
```kotlin
shadowBuilder?.stopScaleFadeAnimation()
```

### 3. 检查动画状态
```kotlin
val isRunning = shadowBuilder?.isScaleFadeAnimationRunning() ?: false
```

### 4. 重置动画状态
```kotlin
shadowBuilder?.resetAnimationState()
```

## 使用场景

### 场景1：拖拽成功完成
```kotlin
// 在 ACTION_DROP 处理中
DragEvent.ACTION_DROP -> {
    // 处理拖拽逻辑
    handleDropAction(event)
    
    // 启动放大淡出动画
    DragUtils.getShadowBuild()?.startScaleFadeOnDragEnd()
}
```

### 场景2：拖拽被取消
```kotlin
// 在 ACTION_DRAG_ENDED 处理中
DragEvent.ACTION_DRAG_ENDED -> {
    if (!event.result) {
        // 拖拽失败，启动动画提供反馈
        DragUtils.getShadowBuild()?.startScaleFadeOnDragEnd()
    }
}
```

### 场景3：在 DragUtils 中集成
```kotlin
// 在 DragUtils.resetAllState() 中添加
@JvmStatic
fun resetAllState() {
    // 在清理前启动动画
    getShadowBuild()?.startScaleFadeOnDragEnd()
    
    // 延迟清理，等待动画完成
    Handler(Looper.getMainLooper()).postDelayed({
        destoryShadow()
        destorySelectedFiles()
        // ... 其他清理操作
    }, 300) // 等待动画完成
}
```

## 动画参数

可以通过修改常量来调整动画效果：

```kotlin
companion object {
    // 动画时长
    private const val SCALE_FADE_DURATION = 300L
    
    // 缩放范围
    private const val SCALE_START = 1.0f
    private const val SCALE_END = 1.2f
    
    // 透明度范围
    private const val ALPHA_START = 1.0f
    private const val ALPHA_END = 0.0f
}
```

## 注意事项

1. **动画状态管理**：动画运行时会设置 `isScaleFadeAnimRunning` 标志
2. **画布变换**：动画通过 Canvas 的 scale 和 saveLayerAlpha 实现
3. **性能考虑**：动画期间会频繁调用 `updateDragShadow`
4. **状态重置**：创建新拖拽前建议调用 `resetAnimationState()`

## 实现原理

动画通过以下方式实现：
1. 使用 `AnimatorSet` 同时执行缩放和透明度动画
2. 在 `onDrawShadow` 中应用 Canvas 变换
3. 通过 `updateDragShadow` 触发重绘
4. 动画结束后自动清理状态
