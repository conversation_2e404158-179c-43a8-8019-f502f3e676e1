/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:/RecycleBinViewModel.kt
 * * Description:
 * * Version:1.0
 * * Date :2020/6/16
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/6/16,        v1.0,           Create
 ****************************************************************/
package com.filemanager.recyclebin.ui

import androidx.lifecycle.MutableLiveData
import com.filemanager.common.wrapper.RecycleFileWrapper
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.base.loader.UriLoadResult
import com.filemanager.common.compat.PrivacyPasswordSettingCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.interfaces.LoadingLoaderListener
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.GetMediaDurationUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.recyclebin.ui.adapter.RecycleBinAdapter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext


class RecycleBinViewModel :
    SelectionViewModel<RecycleFileWrapper, BaseUiModel<RecycleFileWrapper>>() {
    private companion object {
        private const val TAG = "RecycleBinViewModel"
        private const val CATEGORY_AUDIO_FRAGMENT_LOADER_ID = 1
    }

    val modeState = BaseStateModel(MutableLiveData<Int>(KtConstants.LIST_NORMAL_MODE))
    val positionModel = MutableLiveData<Int>()

    private val loaderCallBack = LoaderCallBack(this)
    private var needScroll = false
    var isPrivacyPasswordSetting = false
    var isPrivacyPasswordTip = false


    fun initLoader(loaderController: LoaderController) {
        if (loaderCallBack.getLoader() == null) {
            loaderController.initLoader(CATEGORY_AUDIO_FRAGMENT_LOADER_ID, loaderCallBack)
        } else {
            loadData()
        }
    }

    override fun loadData() {
        loaderCallBack.getLoader()?.forceLoad()
    }

    fun sortReload() {
        loaderCallBack.getLoader()?.setSort(-1)
        loadData()
    }

    fun getPrivacyPasswordShow() {
        if (!PrivacyPasswordSettingCompat.isSupportPrivacyPWD) {
            return
        }
        isPrivacyPasswordSetting =
            PreferencesUtils.getBoolean(key = CommonConstants.PRIVACY_PASSWORD_SHOW, default = false)
        isPrivacyPasswordTip =
            PreferencesUtils.getBoolean(key = CommonConstants.PRIVACY_PASSWORD_TIP_SHOW, default = false)
    }

    fun updatePrivacyPasswordShow() {
        PreferencesUtils.put(key = CommonConstants.PRIVACY_PASSWORD_SHOW, value = true)
        isPrivacyPasswordSetting = true
    }

    fun updatePrivacyPasswordTipShow() {
        PreferencesUtils.put(key = CommonConstants.PRIVACY_PASSWORD_TIP_SHOW, value = true)
        isPrivacyPasswordTip = true
    }

    fun onClickSelectAllOrDeSelectAll(adapter: RecycleBinAdapter?) {
        uiState.value?.run {
            //all Deselect
            if (adapter?.getRealFileItemCount() == selectedList.size) {
                selectedList.clear()
                return@run
            }
            selectedList.clear()
            //all select
            fileList.forEach {
                it.id?.let(selectedList::add)
            }
        }
        uiState.value = uiState.value
    }

    fun resetState() {
        changeListMode(KtConstants.LIST_NORMAL_MODE)
    }

    fun pressBack(): Boolean = when (modeState.listModel.value) {
        KtConstants.LIST_SELECTED_MODE -> {
            changeListMode(KtConstants.LIST_NORMAL_MODE)
            true
        }

        else -> false
    }

    class LoaderCallBack(
        viewModel: RecycleBinViewModel
    ) : LoadingLoaderListener<RecycleBinViewModel, RecycleBinLoader, UriLoadResult<Int, RecycleFileWrapper>>(
        viewModel,
        viewModel.dataLoadState
    ) {

        override fun onCreateLoader(viewModel: RecycleBinViewModel?): RecycleBinLoader {
            return RecycleBinLoader(MyApplication.appContext)
        }

        override fun onLoadComplete(
            viewModel: RecycleBinViewModel?,
            result: UriLoadResult<Int, RecycleFileWrapper>?
        ) {
            val resultList = result?.resultList
            Log.d(TAG, "onLoadFinished size  ${resultList?.size}")
            viewModel ?: return
            resultList ?: return
            viewModel.modeState.initState = true
            viewModel.launch {
                viewModel.onLoadCompleteAction(result, resultList)
            }
        }

        private suspend fun RecycleBinViewModel.onLoadCompleteAction(
            result: UriLoadResult<Int, RecycleFileWrapper>,
            resultList: MutableList<RecycleFileWrapper>
        ) {
            val selectedList = ArrayList<Int>()
            uiState.value?.selectedList.takeUnless {
                it.isNullOrEmpty()
            }?.let { uiSelectedList ->
                withContext(Dispatchers.IO) {
                    uiSelectedList.forEach { selectedKey ->
                        if (result.resultMap.containsKey(selectedKey)) {
                            selectedList.add(selectedKey)
                        }
                    }
                }
            }
            modeState.listModel.run {
                if (resultList.isEmpty() && (value == KtConstants.LIST_SELECTED_MODE)) {
                    Log.d(TAG, "onLoadComplete mResultList is empty change to normal mode")
                    value = KtConstants.LIST_NORMAL_MODE
                }
            }
            uiState.postValue(BaseUiModel(resultList, modeState, selectedList, result.resultMap))
            if (needScroll) {
                positionModel.value = 0
                needScroll = false
            }
        }
    }

    override fun canDragDrop(): Boolean {
        return false
    }

    override fun getRealFileSize(): Int {
        var size = 0
        uiState.value?.fileList?.forEach {
            if (it.fileWrapperLabel == null) {
                size++
            }
        }
        Log.d(TAG, "getRealFileSize:  $size")
        return size
    }

    override fun getRecyclerViewScanMode(): com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE {
        return com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.LIST
    }
}