package com.oplus.filemanager.filechoose.ui.folderpicker

import android.content.Context
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.wrapper.PathFileWrapper
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File
import java.util.Locale

/**
 * FolderLoader的单元测试类
 * 用于测试FolderLoader类的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class FolderLoaderTest {

    // 测试上下文对象
    private lateinit var context: Context
    // 被测试的FolderLoader实例
    private lateinit var folderLoader: FolderLoader
    // 测试路径
    private val testPath = "/test/path"

    /**
     * 测试前的初始化方法
     * 创建mock的Context对象和FolderLoader实例
     */
    @Before
    fun setUp() {
        // 创建mock的Context对象
        context = mockk(relaxed = true)
        // 初始化FolderLoader实例
        folderLoader = FolderLoader(context, testPath)
    }

    /**
     * 测试后的清理方法
     * 清除所有mock对象
     */
    @After
    fun tearDown() {
        clearAllMocks()
        unmockkAll()
    }

    /**
     * 测试构造函数是否正确初始化加载路径
     */
    @Test
    fun `constructor should initialize load path`() {
        val loader = FolderLoader(context, testPath)
        assertNotNull(loader)
    }

    /**
     * 测试setPath方法是否能正确更新加载路径
     */
    @Test
    fun `setPath should update load path`() {
        val newPath = "/new/path"
        folderLoader.setPath(newPath)
        val paths = folderLoader.getPath()
        assertEquals(newPath, paths[0])
    }

    /**
     * 测试getPath方法是否能正确返回包含加载路径的数组
     */
    @Test
    fun `getPath should return array with load path`() {
        folderLoader.setPath(testPath)
        val paths = folderLoader.getPath()
        assertEquals(1, paths.size)
        assertEquals(testPath, paths[0])
    }

    /**
     * 测试getVolume方法是否返回null
     */
    @Test
    fun `getVolume should return null`() {
        val volume = folderLoader.getVolume()
        assertNull(volume)
    }

    /**
     * 测试getFilterList方法是否返回包含DIRECTORY_TYPE的列表
     */
    @Test
    fun `getFilterList should return list with DIRECTORY_TYPE`() {
        val filterList = folderLoader.getFilterList()
        assertNotNull(filterList)
        assertEquals(1, filterList?.size)
        assertEquals(MimeTypeHelper.DIRECTORY_TYPE, filterList?.get(0))
    }

    /**
     * 测试当item的mData为null时，getItemKey方法是否返回null
     */
    @Test
    fun `getItemKey should return null when item mData is null`() {
        val item = BaseFileBean().apply { mData = null }
        val key = folderLoader.getItemKey(item)
        assertNull(key)
    }

    /**
     * 测试当item的mData为空字符串时，getItemKey方法是否返回null
     */
    @Test
    fun `getItemKey should return null when item mData is empty`() {
        val item = BaseFileBean().apply { mData = "" }
        val key = folderLoader.getItemKey(item)
        assertNull(key)
    }

    /**
     * 测试getItemKey方法是否能正确返回路径小写后的hashCode
     */
    @Test
    fun `getItemKey should return hashcode of lowercase path`() {
        val path = "/TEST/PATH"
        val item = BaseFileBean().apply { mData = path }
        val expectedKey = path.lowercase(Locale.ROOT).hashCode()
        val key = folderLoader.getItemKey(item)
        assertEquals(expectedKey, key)
    }
}