<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_60dp"
    android:layout_marginHorizontal="@dimen/download_file_item_horizontal_margin">

    <TextView
        android:id="@+id/tv_multi_file_count"
        style="?attr/couiTextHeadlineM"
        tools:text="18个文件"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:textColor="?attr/couiColorLabelPrimary" />

    <TextView
        android:id="@+id/tv_multi_file_size"
        style="?attr/couiTextBodyS"
        tools:text="18个文件"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:textColor="?attr/couiColorLabelSecondary" />

    <View
        android:id="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="0.33dp"
        android:layout_alignStart="@id/tv_multi_file_count"
        android:layout_alignEnd="@id/tv_multi_file_size"
        android:layout_alignParentBottom="true"
        android:background="?attr/couiColorDivider" />
</RelativeLayout>

