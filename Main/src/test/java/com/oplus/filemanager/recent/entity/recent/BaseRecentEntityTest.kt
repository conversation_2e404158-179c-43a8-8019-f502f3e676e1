/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BaseRecentEntityTest
 ** Description : BaseRecentEntity Unit Test
 ** Version     : 1.0
 ** Date        : 2024/6/21
 ** Author      : W9059186
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2024/6/21      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.recent.entity.recent

import org.junit.Assert
import org.junit.Test

class BaseRecentEntityTest {

    @Test
    fun should_when_equals() {
        val baseRecentEntity = BaseRecentEntity()
        val baseRecentEntityB = BaseRecentEntity()
        Assert.assertTrue(baseRecentEntity == baseRecentEntityB)
    }

    @Test
    fun should_when_hashCode() {
        val baseRecentEntity = BaseRecentEntity()
        val baseRecentEntityB = BaseRecentEntity()
        Assert.assertTrue(baseRecentEntity.hashCode() == baseRecentEntityB.hashCode())
    }
}