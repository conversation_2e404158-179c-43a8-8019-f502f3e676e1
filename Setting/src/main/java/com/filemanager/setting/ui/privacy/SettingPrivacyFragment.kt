package com.filemanager.setting.ui.privacy

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.preference.COUIJumpPreference
import com.coui.appcompat.preference.COUIPreference
import com.coui.appcompat.preference.COUIPreferenceCategory
import com.coui.appcompat.preference.COUIPreferenceWithAppbarFragment
import com.coui.appcompat.preference.COUISwitchPreference
import com.coui.appcompat.statement.COUIStatementClickableSpan
import com.coui.appcompat.statement.COUIUserStatementDialog
import com.coui.appcompat.statusbar.COUIStatusbarTintUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.KtConstants.ACTION_AGREEMENT_PAGE
import com.filemanager.common.constants.KtConstants.ACTION_PERSONAL_INFO
import com.filemanager.common.constants.KtConstants.ACTION_USER_INFO
import com.filemanager.common.controller.PersonalizedServiceController
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.Utils
import com.filemanager.setting.R
import com.filemanager.setting.utils.FlexibleWindowUtil
import com.oplus.filemanager.interfaze.ad.IAdvertApi
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import java.util.Locale

class SettingPrivacyFragment : COUIPreferenceWithAppbarFragment()  {
    companion object {
        private const val TAG = "SettingPrivacyFragment"
        private const val PREF_PRIVACY_STATE = "setting_pref_privacy_state"
        private const val PREF_USER_AGREEMENT = "setting_pref_user_agreement"
        private const val PREF_PERSONAL_INFO = "setting_pref_personal_info"
        private const val PREF_PRIVACY_CLEAR = "setting_pref_clear_data"
        private const val PREF_AD_SWITCH = "ad_switch"
        private const val PREF_CATEGORY_AD = "category_ad"
        const val SP_KEY_SHOW_STATEMENT = "show_statement"
        const val KEY_PAGE_TYPE = "pageType"
        const val TYPE_PRIVACY_INFO = 1
    }
    private val mPersonalizedServiceController: PersonalizedServiceController by lazy { PersonalizedServiceController() }
    override fun getTitle(): String {
        activity?.let {
            return it.title.toString()
        }
        return ""
    }

    private fun getLayoutResource(): Int {
        return R.xml.setting_privacy
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        (view as ViewGroup).removeView(view.findViewById<View>(com.filemanager.common.R.id.appbar_layout))
        addPreferencesFromResource(getLayoutResource())
        initView()
        return view
    }

    private fun initView() {
        initPrivacy()
        initUserAgreement()
        initPersonalInfo()
        initClearPermission()
        initAdSwitch()
        shouldShowAdUi()
    }
    /**
     * 内销和外销欧盟不显示用户协议、明示清单和个性化服务开关
     * */
    private fun shouldShowAdUi() {
        Log.d(TAG, "expRom：${FeatureCompat.sIsExpRom}, eu：${PropertyCompat.sIsGDPR}")
        if (!FeatureCompat.sIsExpRom
            || mPersonalizedServiceController.isNoAdRegionFromCloud()
            || !mPersonalizedServiceController.carriersSupported(context as Activity)
        ) {
            findPreference<COUIJumpPreference>(PREF_USER_AGREEMENT)?.isVisible = false
            findPreference<COUIJumpPreference>(PREF_PERSONAL_INFO)?.isVisible = false
            findPreference<COUISwitchPreference>(PREF_AD_SWITCH)?.isVisible = false
        }
        if (PropertyCompat.sIsGDPR) {
            findPreference<COUIJumpPreference>(PREF_PERSONAL_INFO)?.isVisible = false
        }
    }

    private fun initPrivacy() {
        findPreference<COUIJumpPreference>(PREF_PRIVACY_STATE)?.setOnPreferenceClickListener {
            openPrivacyPolicy()
            true
        }
    }
    private fun initUserAgreement() {
        findPreference<COUIJumpPreference>(PREF_USER_AGREEMENT)?.setOnPreferenceClickListener {
            openUserAgreement()
            true
        }
    }
    private fun initPersonalInfo() {
        findPreference<COUIJumpPreference>(PREF_PERSONAL_INFO)?.setOnPreferenceClickListener {
            openPersonalInfo()
            true
        }
    }
    private fun startPrivacyActivity(activity: FragmentActivity, type: Int) {
        try {
            val intent = Intent(ACTION_USER_INFO)
            intent.setPackage(activity.packageName)
            intent.putExtra(KEY_PAGE_TYPE, type)
            FlexibleWindowUtil.startActivityForFlexibleWindow(activity, intent)
        } catch (e: Exception) {
            Log.d(TAG, "start: ${e.message}")
        }
    }

    private fun initClearPermission() {
        val preference = findPreference<COUIPreference>(PREF_PRIVACY_CLEAR)
        if (FeatureCompat.sIsExpRom) {
            preference?.isVisible = false
        }
        val isEnabled = PrivacyPolicyController.isEnableWithDraw()
        preference?.isEnabled = isEnabled
        if (isEnabled.not()) {
            preference?.layoutResource = com.support.preference.R.layout.coui_preference
        }
        preference?.setOnPreferenceClickListener {
            if (Utils.isQuickClick().not()) {
                activity?.let {
                    createBasicDialog(it) {
                        preference.isEnabled = false
                        preference.layoutResource = com.support.preference.R.layout.coui_preference
                    }
                }
            }
            true
        }
    }

    private fun createBasicDialog(activity: Activity, withdrawInvoke: (() -> Unit)? = null) {
        Log.d(TAG, "createPolicyDialog")
        val dialog = object : COUIUserStatementDialog(activity) {
            @Deprecated("Deprecated in Java")
            override fun onBackPressed() {
                super.onBackPressed()
                dismiss()
            }
        }
        dialog.apply {
            setCanceledOnTouchOutside(false)
            hideDragView()
            logoDrawable = ContextCompat.getDrawable(activity, com.filemanager.common.R.drawable.ic_launcher_filemanager)
            titleText = resources.getString(com.filemanager.common.R.string.withdraw_personal_information_protection_policy)
            addBasicPrivacyPolicyLinkSpan(this,  activity)
            bottomButtonText = resources.getString(com.filemanager.common.R.string.dont_withdraw)
            exitButtonText = resources.getString(com.filemanager.common.R.string.withdraw)
            onButtonClickListener = object : COUIUserStatementDialog.OnButtonClickListener {
                override fun onExitButtonClick() {
                    dialog.dismiss()
                    PreferencesUtils.put(key = SP_KEY_SHOW_STATEMENT, value = false)
                    val preference = findPreference<COUIPreference>(PREF_PRIVACY_CLEAR)
                    preference?.isEnabled = false
                    preference?.layoutResource = com.support.preference.R.layout.coui_preference
                    disagreeThirdAppSearch()
                    dissagreeKdocAndTencentDoc()
                    PrivacyPolicyController.saveAgreeAdditionalFunctions(false)
                    PrivacyPolicyController.saveFeedBackAgree(false)
                    dialog.dismiss()
                    withdrawInvoke?.invoke()
                }

                override fun onBottomButtonClick() {
                    dialog.dismiss()
                }
            }
            setOnKeyListener { _, keyCode, event ->
                Log.d(TAG, "onKey keyCode:$keyCode event:$event")
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                    onBackPressed()
                    return@setOnKeyListener true
                }
                false
            }
            PredictiveBackUtils.registerOnBackInvokedCallback(this)
        }
        setDialogStatusBarTransparentAndBlackFont(activity, dialog, false)
        dialog.show()
    }

    private fun initAdSwitch() {
        if (PropertyCompat.sIsGDPR) {
            Log.w(TAG, "initAdSwitch in eu region")
            return
        }
        val categoryAd = findPreference<COUIPreferenceCategory>(PREF_CATEGORY_AD)
        val advertSwitchHelp = Injector.injectFactory<IAdvertApi>()
        val cloudShowSwitch = advertSwitchHelp?.getAdCloudShowAdSwitch() ?: true
        var showAdSwitch = true
        activity?.let {
            showAdSwitch = advertSwitchHelp?.showAdSwitch(it) ?: false
        }

        if (!cloudShowSwitch) {
            Log.d(TAG, "advert switch is hide")
            showAdSwitch = false
        }

        if (!showAdSwitch) {
            categoryAd?.let {
                preferenceScreen.removePreference(it)
            }
        } else {
            val adSwitchPreference = findPreference<COUISwitchPreference>(PREF_AD_SWITCH)
            adSwitchPreference?.apply {
                activity?.let {
                    setSwitchBarCheckedColor(ContextCompat.getColor(it,
                        com.filemanager.common.R.color.switch_color_green_99))
                    isVisible = advertSwitchHelp?.showAdSwitch(it) ?: false
                    isChecked = advertSwitchHelp?.getAdSwitchStatus(it) ?: false
                }
                setOnPreferenceClickListener {
                    Log.d(TAG, "ad switch is $isChecked")
                    advertSwitchHelp?.setAdSwitchStatus(isChecked)
                    true
                }
            }
        }
    }

    private fun disagreeThirdAppSearch() {
        PreferencesUtils.put(key = CommonConstants.THIRD_APP_SEARCH_INTRODUCE_DIALOG_SHOW, value = false)
        PreferencesUtils.put(key = CommonConstants.THIRD_APP_SEARCH_FUNCTION_SHOW, value = false)
        val dmpApi = Injector.injectFactory<IDmpSearchApi>()
        dmpApi?.updateSwitchSpToDmp()
    }

    private fun dissagreeKdocAndTencentDoc() {
        Log.i(TAG, "dissagreeKdocAndTencentDoc ")
        PreferencesUtils.put(key = CommonConstants.TENCENT_DOCS_FUNCTION_SHOW, value = false)
        PreferencesUtils.put(key = CommonConstants.K_DOCS_FUNCTION_SHOW, value = false)
    }


    private fun exitApp() {
        val activities = MyApplication.activities
        for (itemActivity in activities) {
            itemActivity.finish()
        }
    }

    private fun addBasicPrivacyPolicyLinkSpan(dialog: COUIUserStatementDialog, activity: Activity) {
        val resources = activity.resources
        val statementString: String
        val linkString2 = resources.getString(com.filemanager.common.R.string.personal_information_protection_policy)
        statementString = String.format(
            Locale.getDefault(),
            resources.getString(com.filemanager.common.R.string.withdraw_protection_policy_content_no_exit_1),
            linkString2)

        val spannableString = SpannableStringBuilder(statementString)
        val startIndex = statementString.indexOf(linkString2)
        if (startIndex > 0) {
            val endIndex = startIndex + linkString2.length
            spannableString.setSpan(object : COUIStatementClickableSpan(activity) {
                override fun onClick(widget: View) {
                    openPrivacyPolicy()
                    widget.clearFocus()
                    widget.isPressed = false
                    widget.isSelected = false
                }

            }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
            spannableString.setSpan(
                ForegroundColorSpan(activity.getColor(com.filemanager.common.R.color.text_color_black_alpha_60)),
                0, startIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
        }
        dialog.statement = spannableString
    }

    private fun openPrivacyPolicy() {
        activity?.let {
            if (FeatureCompat.sIsExpRom) {
                PermissionUtils.openPrivacyPolicy(it)
            } else {
                startPrivacyActivity(it, TYPE_PRIVACY_INFO)
            }
        }
    }
    /**
     * 打开用户协议界面
     * */
    private fun openUserAgreement() {
        try {
            val intent = Intent(ACTION_AGREEMENT_PAGE)
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open privacy page: ${e.message}")
        }
    }
    /**
     * 打开个人信息明示清单界面
     * */
    private fun openPersonalInfo() {
        try {
            val intent = Intent(ACTION_PERSONAL_INFO)
            startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open privacy page: ${e.message}")
        }
    }
    private fun setDialogStatusBarTransparentAndBlackFont(context: Context, dialog: Dialog, isFullScreen: Boolean) {
        if (dialog.window != null) {
            val window = dialog.window
            val decorView = window?.decorView
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                if (isFullScreen) {
                    decorView?.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                }
            }
            var flag = decorView?.systemUiVisibility
            val versionCode = ViewHelper.getRomVersionCode()
            val white = false//context.resources.getBoolean(R.bool.is_status_white)
            if (versionCode >= ViewHelper.COLOR_OS_3_0 || versionCode == 0) {
                window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                if (COUIDarkModeUtil.isNightMode(dialog.context)) {
                    flag = flag?.and(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv())
                    flag = flag?.and(View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR.inv())
                } else {
                    flag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        if (!white) {
                            flag?.or(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR)
                        } else {
                            flag?.or(View.SYSTEM_UI_FLAG_LAYOUT_STABLE)
                        }
                    } else {
                        flag?.or(COUIStatusbarTintUtil.SYSTEM_UI_FLAG_OP_STATUS_BAR_TINT)
                    }
                }
                flag?.let {
                    decorView?.systemUiVisibility = it
                }
            }
        }
    }
}
