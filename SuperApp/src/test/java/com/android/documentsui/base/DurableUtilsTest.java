package com.android.documentsui.base;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import android.os.BadParcelableException;
import android.os.Parcel;
import android.util.Log;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;

@RunWith(RobolectricTestRunner.class)
@Config(sdk = 29)
public class DurableUtilsTest {

    @Mock
    private Durable mockDurable;
    @Mock
    private Parcel mockParcel;
    @Mock
    private DataOutputStream mockDataOutputStream;
    @Mock
    private DataInputStream mockDataInputStream;
    @Mock
    private Log mockLog;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testWriteToArray() throws IOException {
        byte[] result = DurableUtils.writeToArray(mockDurable);
        assertNotNull(result);
        verify(mockDurable).write(any(DataOutputStream.class));
    }

    @Test(expected = IOException.class)
    public void testWriteToArray_ThrowsIOException() throws IOException {
        doThrow(new IOException()).when(mockDurable).write(any(DataOutputStream.class));
        DurableUtils.writeToArray(mockDurable);
    }

    @Test
    public void testReadFromArray() throws IOException {
        byte[] testData = "test".getBytes();
        Durable result = DurableUtils.readFromArray(testData, mockDurable);
        assertEquals(mockDurable, result);
        verify(mockDurable).reset();
        verify(mockDurable).read(any(DataInputStream.class));
    }

    @Test(expected = IOException.class)
    public void testReadFromArray_NullData() throws IOException {
        DurableUtils.readFromArray(null, mockDurable);
    }

    @Test(expected = IOException.class)
    public void testReadFromArray_ThrowsIOException() throws IOException {
        byte[] testData = "test".getBytes();
        doThrow(new IOException()).when(mockDurable).read(any(DataInputStream.class));
        DurableUtils.readFromArray(testData, mockDurable);
        verify(mockDurable).reset();
    }

    @Test
    public void testWriteToArrayOrNull_Success() {
        byte[] result = DurableUtils.writeToArrayOrNull(mockDurable);
        assertNotNull(result);
    }

    @Test
    public void testWriteToArrayOrNull_Failure() throws IOException {
        doThrow(new IOException()).when(mockDurable).write(any(DataOutputStream.class));
        byte[] result = DurableUtils.writeToArrayOrNull(mockDurable);
        assertNull(result);
    }

    @Test
    public void testReadFromArrayOrNull_Success() {
        byte[] testData = "test".getBytes();
        Durable result = DurableUtils.readFromArrayOrNull(testData, mockDurable);
        assertEquals(mockDurable, result);
    }

    @Test
    public void testReadFromArrayOrNull_Failure() throws IOException {
        byte[] testData = "test".getBytes();
        doThrow(new IOException()).when(mockDurable).read(any(DataInputStream.class));
        Durable result = DurableUtils.readFromArrayOrNull(testData, mockDurable);
        assertNull(result);
    }

    @Test
    public void testWriteToParcel() {
        DurableUtils.writeToParcel(mockParcel, mockDurable);
        verify(mockParcel).writeByteArray(any(byte[].class));
    }

    @Test(expected = BadParcelableException.class)
    public void testWriteToParcel_ThrowsIOException() throws IOException {
        doThrow(new IOException()).when(mockDurable).write(any(DataOutputStream.class));
        DurableUtils.writeToParcel(mockParcel, mockDurable);
    }

    @Test
    public void testReadFromParcel() {
        when(mockParcel.createByteArray()).thenReturn("test".getBytes());
        Durable result = DurableUtils.readFromParcel(mockParcel, mockDurable);
        assertEquals(mockDurable, result);
    }

    @Test(expected = BadParcelableException.class)
    public void testReadFromParcel_ThrowsIOException() throws IOException {
        when(mockParcel.createByteArray()).thenReturn("test".getBytes());
        doThrow(new IOException()).when(mockDurable).read(any(DataInputStream.class));
        DurableUtils.readFromParcel(mockParcel, mockDurable);
    }

    @Test
    public void testWriteNullableString_WithValue() throws IOException {
        DurableUtils.writeNullableString(mockDataOutputStream, "test");
        verify(mockDataOutputStream).write(1);
        verify(mockDataOutputStream).writeUTF("test");
    }

    @Test
    public void testWriteNullableString_NullValue() throws IOException {
        DurableUtils.writeNullableString(mockDataOutputStream, null);
        verify(mockDataOutputStream).write(0);
        verify(mockDataOutputStream, never()).writeUTF(anyString());
    }

    @Test
    public void testReadNullableString_WithValue() throws IOException {
        when(mockDataInputStream.read()).thenReturn(1);
        when(mockDataInputStream.readUTF()).thenReturn("test");
        String result = DurableUtils.readNullableString(mockDataInputStream);
        assertEquals("test", result);
    }

    @Test
    public void testReadNullableString_NullValue() throws IOException {
        when(mockDataInputStream.read()).thenReturn(0);
        String result = DurableUtils.readNullableString(mockDataInputStream);
        assertNull(result);
    }
}