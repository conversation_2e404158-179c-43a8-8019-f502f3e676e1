/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileServiceRunnable
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/13 14:40
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/13       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.impl

import com.filemanager.common.utils.Log

abstract class FileServiceRunnable(
    val name: String
) : Runnable {

    override fun run() {
        Log.d(TAG, "run -> thread info = ${Thread.currentThread().name}")
        runInternal()
    }

    /**
     * Runnable internal execution method.
     */
    abstract fun runInternal()

    companion object {
        private const val TAG = "FileServiceRunnable"
    }
}