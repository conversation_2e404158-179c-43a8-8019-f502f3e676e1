package com.oplus.filemanager.filechoose.adapter

import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.SelectItemLayout
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.filechoose.R
import com.oplus.filemanager.interfaze.cloudconfig.ICloudConfigApi
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * SinglePickerAdapter的单元测试类
 * 用于测试SinglePickerAdapter的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SinglePickerAdapterTest {

    // 测试上下文环境
    private lateinit var context: Context
    // 被测适配器实例
    private lateinit var adapter: SinglePickerAdapter
    // 模拟的ViewHolder
    private lateinit var mockViewHolder: SinglePickerAdapter.PickerViewHolder
    // 模拟的View
    private lateinit var mockView: View

    /**
     * 测试前的准备工作
     * 初始化测试环境和模拟对象
     */
    @Before
    fun setUp() {
        // 创建Robolectric测试环境
        context = Robolectric.buildActivity(Activity::class.java).get()
        
        // 模拟MyApplication单例
        mockkObject(MyApplication)
        every { MyApplication.appContext } returns context

        // 模拟KtViewUtils工具类
        mockkObject(KtViewUtils)
        every { KtViewUtils.getWindowSize(any()) } returns android.graphics.Point(1080, 1920)

        // 模拟文件图片加载器
        mockkObject(FileImageLoader)
        every { FileImageLoader.sInstance } returns mockk(relaxed = true)

        // 创建被测适配器实例
        adapter = SinglePickerAdapter(context)

        // 创建模拟的View和ViewHolder
        mockView = mockk(relaxed = true)
        
        // 预先定义mockView.findViewById的返回值
        val mockImg = mockk<FileThumbView>(relaxed = true)
        val mockJumpImg = mockk<ImageView>(relaxed = true)
        val mockTitle = mockk<TextViewSnippet>(relaxed = true)
        val mockAnotherName = mockk<TextView>(relaxed = true)
        val mockCheckBox = mockk<CheckBox>(relaxed = true)
        val mockSelectLayout = mockk<SelectItemLayout>(relaxed = true)
        val mockDivider = mockk<View>(relaxed = true)
        
        // 设置findViewById的模拟返回值
        every { mockView.findViewById<FileThumbView>(R.id.mark_file_list_item_icon) } returns mockImg
        every { mockView.findViewById<ImageView>(R.id.jump_mark) } returns mockJumpImg
        every { mockView.findViewById<TextViewSnippet>(R.id.mark_file_list_item_title) } returns mockTitle
        every { mockView.findViewById<TextView>(R.id.another_name_view) } returns mockAnotherName
        every { mockView.findViewById<CheckBox>(R.id.mark_radio_button) } returns mockCheckBox
        every { mockView.findViewById<SelectItemLayout>(R.id.select_item_layout) } returns mockSelectLayout
        every { mockView.findViewById<View>(R.id.divider_line) } returns mockDivider
        
        // 创建真实的PickerViewHolder实例
        mockViewHolder = SinglePickerAdapter.PickerViewHolder(mockView)
    }

    /**
     * 测试后的清理工作
     * 释放模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试构造函数是否正确初始化上下文和尺寸
     */
    @Test
    fun `constructor should initialize context and dimensions`() {
        assertNotNull(adapter)
    }

    /**
     * 测试updateFolderTitleMaxSize方法
     * 验证在Activity上下文下能正确计算文件夹标题最大宽度
     */
    @Test
    fun `updateFolderTitleMaxSize with activity context should calculate folder title max width`() {
        adapter.updateFolderTitleMaxSize(context)
        // 验证通过不抛出异常
    }

    /**
     * 测试supportMultipleSelection方法
     * 验证是否能正确设置多选支持标志
     */
    @Test
    fun `supportMultipleSelection should set support flag`() {
        adapter.supportMultipleSelection(true)
        // 无法直接访问私有字段，但可以通过其他方法测试行为
    }

    /**
     * 测试onBindViewHolder方法
     * 验证在无效位置时不会绑定数据
     */
    @Test
    fun `onBindViewHolder with invalid position should not bind data`() {
        val files = mutableListOf<BaseFileBean>().apply {
            add(createTestFileBean("file1", false))
        }
        adapter.setData(files)

        adapter.onBindViewHolder(mockViewHolder, -1)
        // 不应抛出异常，直接返回
    }

    /**
     * 测试onBindViewHolder方法
     * 验证绑定目录文件时能正确配置视图
     */
    @Test
    fun `onBindViewHolder with directory file should configure views correctly`() {
        val file = createTestFileBean("folder1", true)
        val files = mutableListOf(file)
        adapter.setData(files)

        // 模拟ICloudConfigApi的Injector
        val mockCloudConfigApi = mockk<ICloudConfigApi>(relaxed = true)
        mockkObject(Injector)
        every { Injector.injectFactory<ICloudConfigApi>() } returns mockCloudConfigApi

        adapter.onBindViewHolder(mockViewHolder, 0)

        // 验证目录文件对应的视图设置
        verify { mockViewHolder.mAnotherName.visibility = View.VISIBLE }
        verify { mockViewHolder.mJumpImg.visibility = View.VISIBLE }
        verify { mockViewHolder.mCheckBox.visibility = View.GONE }
        verify(exactly = 1) { mockCloudConfigApi.updateViewByAlias(any(), any()) }
    }

    /**
     * 测试onBindViewHolder方法
     * 验证绑定普通文件时能正确配置视图
     */
    @Test
    fun `onBindViewHolder with regular file should configure views correctly`() {
        val file = createTestFileBean("file1", false)
        val files = mutableListOf(file)
        adapter.setData(files)

        adapter.onBindViewHolder(mockViewHolder, 0)

        // 验证普通文件对应的视图设置
        verify { mockViewHolder.mAnotherName.visibility = View.GONE }
        verify { mockViewHolder.mJumpImg.visibility = View.GONE }
        verify { mockViewHolder.mCheckBox.visibility = View.VISIBLE }
    }

    /**
     * 测试itemViewOnClickListener方法
     * 验证点击目录时会清空选择
     */
    @Test
    fun `itemViewOnClickListener for directory should clear selection`() {
        val file = createTestFileBean("folder1", true)
        val files = mutableListOf(file)
        adapter.setData(files)

        val mockClickListener = mockk<OnRecyclerItemClickListener>(relaxed = true)
        adapter.setOnRecyclerItemClickListener(mockClickListener)

        // 模拟点击事件
        val slot = slot<View.OnClickListener>()
        every { mockView.setOnClickListener(capture(slot)) } just runs
        adapter.onBindViewHolder(mockViewHolder, 0)

        slot.captured.onClick(mockView)

        // 验证点击事件处理
        verify { mockClickListener.onItemClick(mockView, 0) }
    }

    /**
     * 测试getItemCount方法
     * 验证能正确返回项目数量
     */
    @Test
    fun `getItemCount should return correct count`() {
        assertEquals(0, adapter.itemCount)

        val files = mutableListOf<BaseFileBean>().apply {
            add(createTestFileBean("file1", false))
        }
        adapter.setData(files)
        assertEquals(1, adapter.itemCount)
    }

    /**
     * 测试SelectedItems类的setChecked方法
     * 验证能正确更新按钮和布局状态
     */
    @Test
    fun `SelectedItems setChecked should update button and layout`() {
        val checkBox = mockk<CheckBox>(relaxed = true)
        val layout = mockk<SelectItemLayout>(relaxed = true)
        val selectedItems = SinglePickerAdapter.SelectedItems(checkBox, layout)

        selectedItems.setChecked(true)
        verify { checkBox.isChecked = true }
        verify { layout.setChecked(true) }

        selectedItems.setChecked(false)
        verify { checkBox.isChecked = false }
        verify { layout.setChecked(false) }
    }

    /**
     * 测试SelectedItems类
     * 验证当按钮或布局为null时不会抛出异常
     */
    @Test
    fun `SelectedItems with null button or layout should not throw exception`() {
        val selectedItems = SinglePickerAdapter.SelectedItems(null, null)
        selectedItems.setChecked(true) // 不应抛出异常
    }

    /**
     * 创建测试用的BaseFileBean对象
     * @param name 文件名
     * @param isDirectory 是否为目录
     * @return 配置好的BaseFileBean实例
     */
    private fun createTestFileBean(name: String, isDirectory: Boolean): BaseFileBean {
        return BaseFileBean().apply {
            mData = name
            mIsDirectory = isDirectory
            mDisplayName = name
            mLocalType = if (isDirectory) MimeTypeHelper.DIRECTORY_TYPE else MimeTypeHelper.UNKNOWN_TYPE
        }
    }

    /**
     * 通过反射获取适配器内部的mSelectPosition列表
     * 仅用于测试目的
     * @param adapter 被测适配器实例
     * @return 选择位置列表
     */
    private fun getInternalSelectPositionList(adapter: SinglePickerAdapter): ArrayList<Int> {
        // 使用反射访问私有lazy字段
        val field = SinglePickerAdapter::class.java.getDeclaredField("mSelectPosition")
        field.isAccessible = true
        // 由于mSelectPosition是lazy初始化的，我们需要获取其value
        val lazyDelegate = field.get(adapter)
        // 获取Lazy实例的value属性
        val valueField = lazyDelegate.javaClass.getDeclaredField("value")
        valueField.isAccessible = true
        return valueField.get(lazyDelegate) as ArrayList<Int>
    }
}