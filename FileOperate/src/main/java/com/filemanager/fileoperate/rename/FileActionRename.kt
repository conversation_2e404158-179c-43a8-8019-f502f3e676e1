/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File: FileActionRename.kt
 ** Description: Rename File/Directory function
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.rename

import android.app.Activity
import android.content.DialogInterface
import android.media.MediaScannerConnection.OnScanCompletedListener
import android.net.Uri
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.base.BaseFileNameDialog
import com.filemanager.fileoperate.rename.FileRenameObserver.FileRenameBean.Companion.TYPE_RENAME_FILE
import com.oplus.filemanager.interfaze.fileopentime.IFileOpenTime
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.fileservice.IFileService.Companion.OPERATE_TYPE_RENAME
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.tool.trackinglib.MediaType
import com.oplus.tool.trackinglib.OpTracker
import com.oplus.tool.trackinglib.OpTrackerMaker
import com.oplus.tool.trackinglib.OpType
import java.io.File
import java.lang.ref.WeakReference

open class FileActionRename(lifecycle: LifecycleOwner, file: BaseFileBean) :
    BaseFileAction<FileRenameObserver>(lifecycle) {
    companion object {
        protected const val TAG = "FileActionRename"
    }
    protected var mFileRenameBean: FileRenameObserver.FileRenameBean? = null
    protected var mOperateFile: BaseFileBean = file
    protected var mInputFileName: String? = null

    private val mLockObj = Object()
    private var mBeforeInputFileName: String? = null
    private var mLifecycleOwner: WeakReference<LifecycleOwner> = WeakReference(lifecycle)
    private val allFilePath = ArrayList<String>()
    private val allDirectoryPath = ArrayList<String>()

    protected open fun initFileRenameBean() {
        mFileRenameBean = FileRenameObserver.FileRenameBean(TYPE_RENAME_FILE, mOperateFile)
        mFileRenameBean?.resultListener = object : BaseFileNameDialog.OnButtonClickListener {
            override fun onClick(dialog: AlertDialog, buttonId: Int, inputValue: String?) {
                when (buttonId) {
                    DialogInterface.BUTTON_POSITIVE -> {
                        mInputFileName = inputValue
                        Log.d(TAG, "Positive button clicked: filename=$mInputFileName")
                        notifyLockReleased()
                    }
                    else -> {
                        mInputFileName = null
                        Log.d(TAG, "Negative button clicked")
                        cancel()
                    }
                }
            }
        }
    }

    open fun isFileExist(file: BaseFileBean): Boolean {
        return JavaFileHelper.exists(file)
    }

    override fun run(): Boolean {
        if (!isFileExist(mOperateFile)) {
            if (KtUtils.checkDfmFileAndDfmDisconnected(mFileRenameBean?.srcOrParentFile?.mData)) {
                notifyObserver(NOTICE_FILE_NOT_EXIST_DFS_DISCONNECT)
            } else {
                notifyObserver(NOTICE_FILE_NOT_EXIST)
            }
            return false
        }
        initFileRenameBean()
        notifyObserver(SHOW_RENAME_DIALOG, mFileRenameBean)
        while (!isCancelled()) {
            try {
                synchronized(mLockObj) {
                    mLockObj.wait()
                }
            } catch (e: InterruptedException) {
                Log.d(TAG, "Action interrupted")
                return false
            }
            Log.d(TAG, "Continue to execute: isCancelled=${isCancelled()}, filename=$mInputFileName")
            if (isCancelled()) {
                return false
            }

            if (validateFileName(mInputFileName)) {
                return reallyExecuteAction(mOperateFile, mInputFileName!!)
            } else {
                mBeforeInputFileName = mInputFileName
            }
        }
        return false
    }

    override fun onCancelled() {
        notifyLockReleased()
        super.onCancelled()
    }

    private fun notifyLockReleased() {
        synchronized(mLockObj) {
            mLockObj.notify()
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    open fun reallyExecuteAction(file: BaseFileBean, newFileName: String): Boolean {
        storeFilesForBuriedPoint()
        if (JavaFileHelper.renameTo(file, newFileName)) {
            val pathList = ArrayList<String>(2)
            val fileData = mOperateFile.mData
            if (fileData?.isEmpty()?.not() == true && (mInputFileName.isNullOrEmpty().not())) {
                pathList.add(File(File(fileData).parent, mInputFileName!!).absolutePath)
                pathList.add(fileData)
            }
            when {
                SdkUtils.isAtLeastR() -> {
                    // Dir rename maybe cause a long time scan, so not support here
                    if (pathList.isNotEmpty() && mOperateFile.mIsDirectory.not()) {
                        Log.d(TAG, "reallyExecuteAction: sendMultiDirMediaScanner")
                        MediaScannerCompat.sendMultiDirMediaScanner(pathList, Utils.MEDIA_SCAN_RENAME,
                                ScanResultListener(pathList.size, mLockObj))
                        try {
                            synchronized(mLockObj) {
                                mLockObj.wait(2000)
                            }
                        } catch (e: Exception) {
                            Log.d(TAG, "reallyExecuteAction interrupted")
                        }
                    } else {
                        MediaScannerCompat.sendMultiDirMediaScanner(pathList, Utils.MEDIA_SCAN_RENAME)
                    }
                }
                else -> {
                    if (file.mIsDirectory.not()) {
                        file.mData?.let {
                            val oldFile = File(it)
                            val newFile = File(oldFile.parent, newFileName)
                            val newFileBean = PathFileWrapper(newFile.absolutePath)
                            FileMediaHelper.updateFileNameInMediaDB(mContext, file, newFileBean)
                        }
                    }
                    MediaScannerCompat.sendMultiDirMediaScanner(pathList, Utils.MEDIA_SCAN_RENAME)
                }
            }
            buriedPointForMedia()
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.onUpdateFilePath(pathList[1], pathList[0])
            /**重命名文件时，更新文件路径，最近打开时间不更新*/
            file.mData?.let {
                val oldFile = File(it)
                val newFile = File(oldFile.parent, newFileName)
                val fileOpenTimeAction = Injector.injectFactory<IFileOpenTime>()
                fileOpenTimeAction?.renameFilePath(oldFile.path, newFile.path)
                Log.d(TAG, "oldFilePath: ${oldFile.path} newFilePath: ${newFile.path}")
                return true
            }
        }
        return false
    }

    protected open fun validateFileName(inputFileName: String?): Boolean {
        Log.d(TAG, "validateFileName, filename=$inputFileName")
        if (inputFileName.isNullOrEmpty()) {
            notifyObserver(NOTICE_FILENAME_EMPTY)
            return false
        }
        return true
    }

    override fun afterRun(result: Boolean) {
        Log.d(TAG, "Rename file: $result")
        val data = mOperateFile.mData ?: return
        val inputFileName = mInputFileName ?: return
        if (result) {
            val newFile = File(File(data).parent, inputFileName)
            val sourceExtension = FileTypeUtils.getExtension(mOperateFile.mData)
            val targetExtension = FileTypeUtils.getExtension(newFile.absolutePath)
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.RENAME_FILE,
                mapOf(
                    StatisticsUtils.SOURCE_NAME to mOperateFile.mDisplayName,
                    StatisticsUtils.SOURCE_FILE_TYPE to
                            if (mOperateFile.mIsDirectory) StatisticsUtils.DIRECTORY_FILE_TYPE else sourceExtension,
                    StatisticsUtils.TARGET_NAME to newFile.name,
                    StatisticsUtils.TARGET_FILE_TYPE to
                            if (newFile.isDirectory) StatisticsUtils.DIRECTORY_FILE_TYPE else targetExtension
                )
            )
            val fileServiceAction = Injector.injectFactory<IFileService>()
            fileServiceAction?.syncOperate(OPERATE_TYPE_RENAME, hashSetOf(newFile.parent))
            notifyObserver(ACTION_DONE, Pair(newFile.absolutePath, mOperateFile))
        } else {
            if (KtUtils.checkDfmFileAndDfmDisconnected(data)) {
                notifyObserver(NOTICE_FILE_NOT_EXIST_DFS_DISCONNECT)
            } else {
                notifyObserver(ACTION_FAILED, mOperateFile.mIsDirectory)
            }
        }
    }

    override fun recycle() {
        mFileRenameBean?.recycle()
    }

    private fun storeFilesForBuriedPoint() {
        allFilePath.clear()
        allDirectoryPath.clear()
        if (mOperateFile.mDisplayName!!.startsWith(".").not()) {
            mOperateFile.mData?.let {
                val dest = File(it)
                if (!dest.isDirectory) {
                    allFilePath.add(it)
                } else {
                    allDirectoryPath.add(it)
                }
            }
        }
    }

    private fun buriedPointForMedia() {
        if (allFilePath.size == 0 && allDirectoryPath.size == 0) {
            return
        }
        val opTracker = OpTracker(appContext)
        val life: LifecycleOwner? = mLifecycleOwner.get()
        var path = "default"
        if ((life != null) && (life is Activity)) {
            path = life.javaClass.name
        }
        Log.d(TAG, "buriedPointForMedia path = $path")
        val pointList: List<Map<String, String>> = opTracker.convert(
            OpTrackerMaker.Builder().apply {
                setOp(OpType.RENAME)
                setOpTime(System.currentTimeMillis().toString())
                setOpPath(path)
                if (allFilePath.size == 0) {
                    setDirPath(mOperateFile.mData!!)
                    setMediaType(MediaType.MEDIA_TYPE_IMAGE)
                } else {
                    setFilePaths(allFilePath)
                }
            }.build()
        )
        pointList.forEach {
            StatisticsUtils.onCommon(appContext, StatisticsUtils.FILE_OPERATION, it)
        }
    }

    private class ScanResultListener(private var mTotalCount: Int, private val mObj: Object) : OnScanCompletedListener {

        override fun onScanCompleted(path: String?, uri: Uri?) {
            mTotalCount--
            if (mTotalCount == 0) {
                Log.d("FileActionRename", "onScanCompleted done")
                try {
                    synchronized(mObj) {
                        mObj.notify()
                    }
                } catch (e: Exception) {
                    Log.w("FileActionRename", "onScanCompleted failed, ${e.message}")
                }
            }
        }
    }
}