/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: AdvertisingIdClientTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/8/29
 ** Author: <PERSON><PERSON><PERSON><PERSON>
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/8/29      1.0     create
 ****************************************************************/
package com.coloros.filemanager.utils;

import android.content.Context;
import android.content.pm.PackageManager;

import com.coloros.filemanager.BaseTest;
import com.filemanager.common.utils.AdvertisingIdClient;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;
import org.robolectric.shadows.ShadowLog;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@Ignore("stupid test")
@RunWith(RobolectricTestRunner.class)
public class AdvertisingIdClientTest extends BaseTest {

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
    }

    @Test
    @Config(shadows = {ShadowApplication.class})
    public void should_verify_when_getGoogleAdId_with_MainLooper() throws Exception {
        mContext = mock(Context.class);
        PackageManager mPackageManager = mock(PackageManager.class);
        when(mContext.getPackageManager()).thenReturn(mPackageManager);
        when(mContext.bindService(any(), any(), anyInt())).thenReturn(true);
        assertEquals("", AdvertisingIdClient.getGoogleAdId());
        new Thread(){
            @Override
            public void run() {
                try {
                    assertEquals("", AdvertisingIdClient.getGoogleAdId());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }.start();
        Thread.sleep(1000);
    }
}