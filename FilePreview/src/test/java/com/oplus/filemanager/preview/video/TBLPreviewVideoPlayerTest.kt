package com.oplus.filemanager.preview.video

import android.content.Context
import android.media.AudioAttributes
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.SurfaceView
import android.view.TextureView
import com.oplus.tblplayer.IMediaPlayer
import com.oplus.tblplayer.monitor.ErrorCode
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * TBLPreviewVideoPlayer的单元测试类
 * 用于测试TBLPreviewVideoPlayer类的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class TBLPreviewVideoPlayerTest {

    // 测试所需的mock对象
    private lateinit var context: Context
    private lateinit var player: TBLPreviewVideoPlayer
    private lateinit var mockMediaPlayer: IMediaPlayer
    private lateinit var mockSurfaceView: SurfaceView
    private lateinit var mockTextureView: TextureView
    private lateinit var mockHandler: Handler

    /**
     * 测试前的初始化方法
     * 创建所有需要的mock对象和测试实例
     */
    @Before
    fun setUp() {
        // 创建mock对象
        context = mockk(relaxed = true)
        mockMediaPlayer = mockk(relaxed = true)
        mockSurfaceView = mockk(relaxed = true)
        mockTextureView = mockk(relaxed = true)
        mockHandler = mockk(relaxed = true)

        // 创建待测试的player实例
        player = TBLPreviewVideoPlayer(context)
        clearAllMocks()  // 清除所有mock记录
    }

    /**
     * 测试后的清理方法
     * 释放所有mock资源
     */
    @After
    fun tearDown() {
        unmockkAll()  // 释放所有mock对象
    }

    /**
     * 测试设置SurfaceView的方法
     * 验证setVideoSurfaceView方法是否能正确调用
     */
    @Test
    fun testSetVideoSurfaceView() {
        // 创建spy对象以便验证方法调用
        player = spyk(player)
        // 设置mock行为
        every { player.setVideoSurfaceView(mockSurfaceView) } returns player
        // 调用测试方法
        player.setVideoSurfaceView(mockSurfaceView)
        // 验证方法是否被调用
        verify { player.setVideoSurfaceView(mockSurfaceView) }
    }

    /**
     * 测试设置TextureView的方法
     * 验证setVideoTextureView方法是否能正确调用
     */
    @Test
    fun testSetVideoTextureView() {
        // 创建spy对象以便验证方法调用
        player = spyk(player)
        // 设置mock行为
        every { player.setVideoTextureView(mockTextureView) } returns player
        // 调用测试方法
        player.setVideoTextureView(mockTextureView)
        // 验证方法是否被调用
        verify { player.setVideoTextureView(mockTextureView) }
    }

    /**
     * 测试设置视频尺寸变化监听器的方法
     * 验证setOnVideoSizeChangedListener方法是否能正确调用
     */
    @Test
    fun testSetOnVideoSizeChangedListener() {
        // 创建spy对象以便验证方法调用
        player = spyk(player)
        // 创建mock监听器
        val mockListener = mockk<IPreviewVideoPlayer.OnVideoSizeChangedListener>()
        // 设置mock行为
        every { player.setOnVideoSizeChangedListener(mockListener) } returns player
        // 调用测试方法
        player.setOnVideoSizeChangedListener(mockListener)
        // 验证方法是否被调用
        verify { player.setOnVideoSizeChangedListener(mockListener) }
    }

    /**
     * 测试设置视频尺寸解码超时时间的方法
     * 验证setVideoSizeDecodeTimeout方法是否能正确调用
     */
    @Test
    fun testSetVideoSizeDecodeTimeout() {
        // 创建spy对象以便验证方法调用
        player = spyk(player)
        // 设置mock行为
        every { player.setVideoSizeDecodeTimeout(1000L) } returns player
        // 调用测试方法
        player.setVideoSizeDecodeTimeout(1000L)
        // 验证方法是否被调用
        verify { player.setVideoSizeDecodeTimeout(1000L) }
    }

    /**
     * 测试类的伴生对象
     * 包含测试中使用的消息常量
     */
    companion object {
        // 更新SurfaceView的消息ID
        private const val MSG_UPDATE_SURFACE_VIEW = 201
        // 更新TextureView的消息ID
        private const val MSG_UPDATE_TEXTURE_VIEW = 202
        // 视频尺寸解码错误的消息ID
        private const val MSG_DECODE_VIDEO_SIZE_ERROR = 203
        // 解码超时的错误原因码
        private const val ERROR_DECODE_REASON_TIMEOUT = -2000
    }
}