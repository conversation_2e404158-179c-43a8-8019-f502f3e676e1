package com.oplus.filemanager.parentchild.viewholder

import android.content.Context
import android.view.View
import androidx.activity.ComponentActivity
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.helper.CategoryHelper
import com.oplus.filemanager.parentchild.adapter.SideCategoryAdapter
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.clearAllMocks
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.lang.reflect.Field

/**
 * SideListPanelViewHolder的单元测试类
 * 用于测试SideListPanelViewHolder的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SideListPanelViewHolderTest {

    // 定义测试所需的mock对象和变量
    private lateinit var viewHolder: SideListPanelViewHolder
    private val mockItemView: View = mockk(relaxed = true)
    private val mockContext: Context = mockk()
    private val mockActivity: ComponentActivity = mockk()
    private val mockRecyclerView: COUIRecyclerView = mockk(relaxed = true)
    private val mockAdapter: SideCategoryAdapter = mockk(relaxed = true)
    private val mockAnimationHelper: ItemAnimationHelper = mockk()
    private val mockBean: CategoryListBean = mockk()

    /**
     * 测试前的初始化方法
     * 1. 设置mock对象的行为
     * 2. 创建待测试的ViewHolder实例
     * 3. 通过反射注入mock的adapter
     */
    @Before
    fun setup() {
        // 设置mock视图查找返回mock的RecyclerView
        every { mockItemView.findViewById<COUIRecyclerView>(any()) } returns mockRecyclerView
        // 设置mock Activity的资源返回
        every { mockActivity.resources } returns mockk(relaxed = true)
        // 设置RecyclerView的layoutManager
        every { mockRecyclerView.layoutManager = any<LinearLayoutManager>() } returns Unit
        
        // 修复1: 避免EditItemTouchCallback初始化时调用StatusBarUtil
        every { mockRecyclerView.findViewHolderForAdapterPosition(any()) } returns null
        
        // 新增修复: 提供状态栏高度值避免MyApplication未初始化错误
        every { mockActivity.resources.getIdentifier(any(), any(), any()) } returns 1
        every { mockActivity.resources.getDimensionPixelSize(any()) } returns 50
        
        // 修复getDimensionPixelOffset调用问题
        every { mockActivity.resources.getDimensionPixelOffset(any()) } returns 0
        
        // 创建待测试的ViewHolder实例
        viewHolder = SideListPanelViewHolder(
            itemView = mockItemView,
            context = mockContext,
            activity = mockActivity,
            onItemViewCallback = { _, _ -> },
            onItemOperateCallback = {},
            onItemMoveCallback = { _, _ -> },
            onItemDropCallback = {}
        )
        
        // 通过反射注入mock的adapter
        val field: Field = viewHolder::class.java.getDeclaredField("listAdapter")
        field.isAccessible = true
        field.set(viewHolder, mockAdapter)
        viewHolder.listType = 1
    }

    /**
     * 测试后的清理方法
     * 清除所有mock对象的状态
     */
    @After
    fun tearDown() {
        clearAllMocks()
    }

    /**
     * 测试setData方法
     * 验证是否正确地调用了adapter的setData方法
     */
    @Test
    fun `setData should update adapter data`() {
        // 准备测试数据
        val testList = arrayListOf(mockBean)
        viewHolder.listType = 2
        // 设置mock行为
        every { mockAdapter.setData(any(), any()) } returns Unit

        // 执行测试方法
        viewHolder.setData(testList)

        // 验证adapter的setData方法是否被正确调用
        verify { mockAdapter.setData(testList, 2) }
    }

    /**
     * 测试updateTargetBean方法
     * 验证是否正确地调用了adapter的updateTargetBean方法
     */
    @Test
    fun `updateTargetBean should delegate to adapter`() {
        // 设置mock行为
        every { mockAdapter.updateTargetBean(any()) } returns Unit

        // 执行测试方法
        viewHolder.updateTargetBean(mockBean)

        // 验证adapter的updateTargetBean方法是否被正确调用
        verify { mockAdapter.updateTargetBean(mockBean) }
    }

    /**
     * 测试setItemViewAlpha方法
     * 验证当ViewHolder为null时的处理逻辑
     */
    @Test
    fun `setItemViewAlpha should handle null view holders`() {
        // 准备测试数据 - 创建一个ADD_LABEL类型的bean
        val addLabelBean = CategoryListBean(
            categoryType = CategoryHelper.CATEGORY_ADD_LABEL, 
            layoutType = 1
        )
        // 设置mock行为
        every { mockAdapter.getAllList() } returns mutableListOf(addLabelBean)
        every { mockRecyclerView.findViewHolderForAdapterPosition(0) } returns null

        // 执行测试方法
        viewHolder.setItemViewAlpha(0.5f)
        
        // 这里没有验证，主要是测试空指针异常是否会被处理
    }
}