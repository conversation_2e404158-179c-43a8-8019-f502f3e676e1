/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : InitHelper
 ** Description : 初始化类，从MainApplication中的初始化逻辑抽取而来
 ** Version     : 1.0
 ** Date        : 2024/06/22 15:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/21       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.appswitch.util

import android.content.Context
import android.util.Log
import androidx.annotation.WorkerThread
import com.coloros.filemanager.appswitch.AppSwitchManager
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.ThirdAppSearchUtil
import com.filemanager.common.utils.UserManagerUtils
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi

object InitHelper {

    const val TAG = "InitHelper"

    const val INIT_STATUS_DEFALUT = 0
    const val INIT_STATUS_ING = 1
    const val INIT_STATUS_SUC = 2
    const val INIT_STATUS_FAILED = 3

    @Volatile
    private var initStatus = INIT_STATUS_DEFALUT
    private var notSupportReason = mutableListOf<Int>()

    private var initStatusListener: DmpInitStatusListener? = null

    @WorkerThread
    fun initDmp() {
        if (MyApplication.dmpAndIndexInitSuc) {
            Log.w(TAG, "initDmpAndAppSwitch dmpAndIndexInitSuc TRUE, no need to init again")
            return
        }
        notSupportReason.clear()
        val context = MyApplication.appContext
        val needInit = checkNeedInit(context)
        if (needInit) {
            initDmpSdkAndIndexAndCheckV3(context)
        } else {
            initStatus = INIT_STATUS_FAILED
            initStatusListener?.onStateChange(initStatus)
            Log.w(TAG, "initDmpAndAppSwitch viewExactSupport scenceServiceSupport isSystemUser not supported, no need to initAppSwitch")
            val featureOn = AppSwitchManager.currentThirdAppListenConfigParam?.isFeatureOn() ?: false
            StaticUtil.uploadInitSucStatic(context, false, featureOn)
        }
    }


    fun checkNeedInit(context: Context): Boolean {
        //这里子用户和系统分身下isSystemUser=false，功能不体现
        val isSystemUser = UserManagerUtils.checkIsSystemUser(context)
        val viewExactSupport = ThirdAppSearchUtil.isViewExtractSupported()
        val scenceServiceSupport = ThirdAppSearchUtil.checkSmartSceneSupportAppSwitch(context)
        val scenceServiceSwitchOn = ThirdAppSearchUtil.checkSmartSceneSwitchKeyOn(context)
        val overOs15 = SdkUtils.isAtLeastOS15()
        val lightOS = FeatureCompat.sIsNormalLightOS
        Log.i(TAG, "checkNeedInit viewExactSupport $viewExactSupport, scenceServiceSupport $scenceServiceSupport ," +
                    "isSysteUser $isSystemUser, lightOs $lightOS, scenceServiceSwitchOn $scenceServiceSwitchOn")
        val result = viewExactSupport && scenceServiceSupport && isSystemUser && !lightOS && scenceServiceSwitchOn && overOs15
        return result
    }


    @WorkerThread
    @Synchronized
    fun initDmpSdkAndIndexAndCheckV3(context: Context): Boolean {
        val dmpSearchApi = Injector.injectFactory<IDmpSearchApi>()
        if (dmpSearchApi == null) {
            Log.w(TAG, "initDmpSdkAndIndexAndCheckV3 dmpSearchApi NULL, return")
            return false
        }
        if (initStatus != INIT_STATUS_DEFALUT) {
            Log.w(TAG, "initDmpSdkAndIndexAndCheckV3 , initStatus $initStatus, not init Again")
            return initStatus == INIT_STATUS_SUC
        }
        initStatus = INIT_STATUS_ING
        initStatusListener?.onStateChange(initStatus)
        val dmpSdkInitResult = dmpSearchApi.initDmpSdk(context)
        val indexInitResult = dmpSearchApi.initIndex()
        val dmpAndIndexInitSuc = dmpSdkInitResult && indexInitResult
        val dmpIsV3Version = dmpSearchApi.checkDmpV3()
        Log.i(TAG, "initDmpAndIndexConfig dmpSdkInitResult $dmpSdkInitResult, indexInitResult $indexInitResult, v3 $dmpIsV3Version")
        if (dmpAndIndexInitSuc && dmpIsV3Version) {
            initStatus = INIT_STATUS_SUC
            initStatusListener?.onStateChange(initStatus)
            MyApplication.dmpAndIndexInitSuc = true
            val featureOn = AppSwitchManager.currentThirdAppListenConfigParam?.isFeatureOn() ?: false
            StaticUtil.uploadInitSucStatic(context, true, featureOn)
        } else {
            Log.w(TAG, "initDmpAndIndexConfig dmpSdk and index init failed, no need to initAppSwitch")
            initStatus = INIT_STATUS_FAILED
            initStatusListener?.onStateChange(initStatus)
            val featureOn = AppSwitchManager.currentThirdAppListenConfigParam?.isFeatureOn() ?: false
            StaticUtil.uploadInitSucStatic(context, false, featureOn)
        }
        return (initStatus == INIT_STATUS_SUC)
    }

    fun resetStatus() {
        Log.i(TAG, "resetStatus")
        val dmpSearchApi = Injector.injectFactory<IDmpSearchApi>()
        dmpSearchApi?.resetDmpInitStatus()
        initStatus = INIT_STATUS_DEFALUT
    }


    fun isInInitProgress(): Boolean {
        Log.i(TAG, "isInInitProgress $initStatus")
        return initStatus == INIT_STATUS_ING || initStatus == INIT_STATUS_DEFALUT
    }

    fun isInitDone(): Boolean {
        Log.i(TAG, "isInitDone $initStatus")
        return initStatus == INIT_STATUS_SUC || initStatus == INIT_STATUS_FAILED
    }

    fun isInitSuc(): Boolean {
        Log.i(TAG, "isInitSuc $initStatus")
        return initStatus == INIT_STATUS_SUC
    }

    fun isInitFailed(): Boolean {
        Log.i(TAG, "isInitFailed $initStatus")
        return initStatus == INIT_STATUS_FAILED
    }


    fun registerStatusChangeListerner(listener: DmpInitStatusListener) {
        Log.i(TAG, "registerStatusChangeListerner listener $listener")
        initStatusListener = listener
    }

    fun unregisterStatusChangeLister() {
        Log.i(TAG, "unregisterStatusChangeLister")
        initStatusListener = null
    }


    interface DmpInitStatusListener {

        /**
         * 初始化状态变化时的回调
         */
        fun onStateChange(status: Int)
    }
}