/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : CloudConfigUtils.kt
 * * Description : CloudConfig Utils
 * * Version     : 1.0
 * * Date        : 2020/9/7
 * * Author      : W9001165
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.cloudconfig

import android.annotation.SuppressLint
import android.widget.TextView
import androidx.annotation.MainThread
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.GsonUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.WpsManager
import com.filemanager.common.utils.isNetworkAvailable
import com.filemanager.common.view.TextViewSnippet
import com.oplus.cloudconfig.bean.AppConfigParam
import com.filemanager.common.utils.Injector
import com.oplus.cloudconfig.service.AliasService
import com.oplus.cloudconfig.service.AppConfigParamService
import com.oplus.cloudconfig.service.CategoryAppService
import com.oplus.cloudconfig.service.CloudDocumentService
import com.oplus.cloudconfig.service.SimulateClickScriptService
import com.oplus.cloudconfig.service.ThirdAppListenConfigParamService
import com.oplus.common.LogLevel
import com.oplus.filemanager.interfaze.appswitch.IAppSwitchApi
import com.oplus.filemanager.interfaze.cloudconfig.ICloudConfigApi
import com.oplus.filemanager.interfaze.simulateclick.ISimulateClickApi
import com.oplus.nearx.cloudconfig.CloudConfigCtrl
import com.oplus.nearx.cloudconfig.Env
import com.oplus.nearx.cloudconfig.api.AreaCode
import com.oplus.nearx.cloudconfig.device.ApkBuildInfo
import com.oplus.nearx.cloudconfig.observable.Disposable
import com.oplus.nearx.cloudconfig.observable.Scheduler
import com.oplus.nearx.cloudconfig.retry.CustomRetryPolicy
import org.apache.commons.io.FileUtils
import java.io.File
import java.util.Locale

object CloudConfigApi : ICloudConfigApi {
    private const val TAG = "CloudConfigApi"
    private const val PRODUCT_ID = "66452"
    private const val BUILD_NO = "2020090912"

    /**
     * folder_alias_2020090912 is downloaded from the cloud server
     * folder_alias : database table name
     * 2020090912 : version code
     */
    private const val LOCAL_ASSET_DB_PATH = "alias/folder_alias_2020090912"

    private const val CONFIG_ALIAS = "CONFIG_ALIAS"
    private const val CONFIG_PARAM = "PARAM_CONFIG"
    private const val CONFIG_FILE_CLOUD_DOCUMENT = "FILE_CLOUD_DOCUMENT_CONFIG"
    private const val CONFIG_THIRDAPPSWITCHLISTEN = "THIRD_APP_SWITCH_LISTEN"
    private const val CONFIG_SIMULATE_CLICK_SCRIPT = "SIMULATE_CLICK_SCRIPT"
    private const val CONFIG_CATEGORY_APP = "CATEGORY_APP"

    private val region: String
        get() = PropertyCompat.sSystemRegion

    private val cloudConfigServices by lazy {
        mutableMapOf<String, Class<*>>().apply {
            put(CONFIG_ALIAS, AliasService::class.java)
            put(CONFIG_PARAM, AppConfigParamService::class.java)
            put(CONFIG_FILE_CLOUD_DOCUMENT, CloudDocumentService::class.java)
            put(CONFIG_THIRDAPPSWITCHLISTEN, ThirdAppListenConfigParamService::class.java)
            put(CONFIG_SIMULATE_CLICK_SCRIPT, SimulateClickScriptService::class.java)
            if (!FeatureCompat.sIsExpRom) {
                put(CONFIG_CATEGORY_APP, CategoryAppService::class.java)
            }
        }
    }

    @SuppressLint("StaticFieldLeak")
    private var cloudConfigCtrl: CloudConfigCtrl? = null

    /**
     * [cloudConfigCtrl] can only be initialized once, at this time download cloud server data
     */
    @MainThread
    override fun initCloudConfig() {
        if (FeatureCompat.sIsExpRom || PrivacyPolicyController.hasAgreeAdditionalFunctions().not()) {
            return
        }
        if (cloudConfigCtrl != null) {
            return
        }
        ThreadManager.sThreadManager.execute(FileRunnable(::initCloudConfigImpl, TAG))
    }

    @Suppress("SpreadOperator")
    @JvmStatic
    private fun initCloudConfigImpl() {
        runCatching {
            cloudConfigCtrl = CloudConfigCtrl.Builder().run {
                Log.d(TAG, "CloudConfigCtrl init")
                apiEnv(if (BuildConfig.CLOUD_CONFIG_TEST_ENV) Env.TEST else Env.RELEASE)
                productId(PRODUCT_ID)
                localAssetConfigs(LOCAL_ASSET_DB_PATH)//local database
                logLevel(LogLevel.LEVEL_ERROR)
                defaultConfigs(*cloudConfigServices.values.toTypedArray())
                areaCode(getAreaCode())
                setBuildInfo(
                    ApkBuildInfo(
                        channelId = PRODUCT_ID,
                        buildNo = BUILD_NO,
                        region = region
                    )
                )
                setRetryPolicy(CustomRetryPolicy(3, 30))
                build(MyApplication.appContext)
            }
            updateAll()
        }.onFailure {
            Log.e(TAG, "initCloudConfig: ERROR!", it)
        }
    }

    override fun getAlias(path: String?): String? = path?.takeIf {
        path.isNotEmpty() && KtUtils.isChineseLanguage()
    }?.let {
        AliasHelper.mAliasMap[it.lowercase(Locale.getDefault())]?.mAlias?.also { alias ->
            Log.d(TAG, "getAlias = $path, alias = $alias")
        }
    }

    override fun getCategoryListGson(): String? = CategoryAppHelper.mCategoryListStr

    override fun updateViewByAlias(view: TextView?, path: String?, keyWord: String?) {
        val alias = getAlias(path)
        if (FeatureCompat.sIsExpRom || alias.isNullOrEmpty()) {
            view?.text = ""
        } else {
            if (keyWord.isNullOrEmpty() || (view !is TextViewSnippet)) {
                view?.text = if (AliasHelper.mRtlState) {
                    "$alias${AliasHelper.STRING_FOUR_SPACES}"
                } else {
                    "${AliasHelper.STRING_FOUR_SPACES}$alias"
                }
            } else {
                if (AliasHelper.mRtlState) {
                    view.setText(alias, "$keyWord    ")
                } else {
                    view.setText("    $alias", keyWord)
                }
            }
        }
    }

    /**
     * download data by the content of mConfigs
     */
    @JvmStatic
    private fun updateAll() {
        Log.d(TAG, "updateAll start")
        if (isNetworkAvailable(MyApplication.sAppContext).not()) {
            Log.w(TAG, "updateAll(), network not available")
            return
        }
        cloudConfigServices.keys.forEach { key ->
            when (key) {
                CONFIG_ALIAS -> updateAliases()
                CONFIG_PARAM -> updateAppParam()
                CONFIG_FILE_CLOUD_DOCUMENT -> updateCloudDocsParams()
                CONFIG_THIRDAPPSWITCHLISTEN -> updateThirdAppListenConfigParams()
                CONFIG_SIMULATE_CLICK_SCRIPT -> updateSimulateClickScript()
                CONFIG_CATEGORY_APP -> updateCategoryApp()
            }
        }
    }

    @JvmStatic
    private fun updateAliases(): Disposable? = cloudConfigCtrl.takeUnless {
        FeatureCompat.sIsExpRom
    }?.let {
        it.create(AliasService::class.java)
            .getAllAliases()
            .observeOn(Scheduler.io())
            .subscribe { alias ->
                Log.d(TAG, "getAllAliases finished size:${alias.size}")
                AliasHelper.addAlias(alias)
            }
    }

    @JvmStatic
    private fun updateCategoryApp(): Disposable? = cloudConfigCtrl.takeUnless {
        FeatureCompat.sIsExpRom
    }?.let {
        Log.d(TAG, "updateCategoryApp")
        it.create(CategoryAppService::class.java)
            .getAllCategoryApp()
            .observeOn(Scheduler.io())
            .subscribe { categories ->
                Log.d(TAG, "getAllCategoryApp finished size:${categories.size}")
                val categoryListStr = GsonUtil.toJsonSafeCall(categories)
                CategoryAppHelper.addCategories(categoryListStr)
                PreferencesUtils.put(
                    CommonConstants.PREF_CN_CATEGORY_APP_LIST,
                    CommonConstants.KEY_CATEGORY_APP_LIST,
                    categoryListStr
                )
            }
    }

    @JvmStatic
    private fun updateAppParam(): Disposable? = cloudConfigCtrl.takeUnless {
        FeatureCompat.sIsExpRom
    }?.let {
        Log.d(TAG, "updateAppParam")
        it.create(AppConfigParamService::class.java)
            .getFile()
            .observeOn(Scheduler.io())
            .map { file ->
                try {
                    val content = FileUtils.readFileToString(file)
                    AppConfigParam.parse(content)
                } catch (e: Exception) {
                    Log.e(TAG, "updateAppParam", e)
                    null
                }
            }
            .subscribeOn(Scheduler.main())
            .subscribe { config ->
                Log.d(TAG, "updateAppParam subscribe ${config?.isWpsThumbnailClose},${config?.isViewAndroidDataClose}")
                WpsManager.setCloseWpsThumbnail(config?.isWpsThumbnailClose == 1)
                AndroidDataHelper.canViewAndroidDataFiles = config?.isViewAndroidDataClose != 1
            }
    }

    @JvmStatic
    private fun updateCloudDocsParams(): Disposable? = cloudConfigCtrl.takeUnless {
        FeatureCompat.sIsExpRom
    }?.let {
        Log.d(TAG, "updateCloudDocsParam")
        it.create(CloudDocumentService::class.java)
            .getData()
            .observeOn(Scheduler.io())
            .subscribe { params ->
                Log.d(TAG, "updateCloudDocsParam -> params = $params")
                PreferencesUtils.put(
                    CommonConstants.PREF_NAME_FILE_CLOUD_DOCS,
                    CommonConstants.KEY_SUPPORT_TENCENT_DOCS,
                    params.showTencentCloudDocument
                )
                PreferencesUtils.put(
                    CommonConstants.PREF_NAME_FILE_CLOUD_DOCS,
                    CommonConstants.KEY_SUPPORT_K_DOCS,
                    params.showKDocs
                )
            }
    }

    @JvmStatic
    private fun updateThirdAppListenConfigParams(): Disposable? {
        Log.d(TAG, "updateThirdAppListenConfigParams")
        return cloudConfigCtrl.takeUnless {
            FeatureCompat.sIsExpRom
        }?.let {
            Log.d(TAG, "updateThirdAppListenConfigParams: start check cloud config")
            it.create(ThirdAppListenConfigParamService::class.java)
                .getFile()
                .observeOn(Scheduler.io())
                .map { file ->
                    convertFileToConfigJsonString(file)
                }
                .subscribe { configString ->
                    Log.d(TAG, "updateThirdAppListenConfigParams subscribe $configString")
                    //更新AppSwitch中的三方监听配置数据
                    configString?.let {
                        processConfigFromCloud(configString)
                    }
                }
        }
    }

    private fun processConfigFromCloud(configString: String) {
        runCatching {
            val appSwitchApi = Injector.injectFactory<IAppSwitchApi>()
            appSwitchApi?.updateThirdAppListenConfig(configString)
            Log.i(TAG, "processConfigFromCloud appSwitchApi $appSwitchApi, configString $configString")
        }.onFailure {
            Log.e(TAG, "processConfigFromCloud error", it)
        }
    }

    private fun convertFileToConfigJsonString(file: File): String? {
        Log.d(TAG, "convertFileToConfig: file=$file")
        return runCatching {
            val content = FileUtils.readFileToString(file)
            Log.d(TAG, "convertFileToConfig: mapFile config $content")
            content
        }.onFailure {
            Log.e(TAG, "convertFileToConfig", it)
        }.getOrNull()
    }

    @JvmStatic
    private fun updateSimulateClickScript(): Disposable? {
        val simulateClickApi = Injector.injectFactory<ISimulateClickApi>() ?: return null
        Log.d(TAG, "updateSimulateClickScript: trigger")
        val receiver = simulateClickApi.prepareRemoteReceiver(MyApplication.appContext)
        receiver.checkReceiveDebugRemoteScripts()
        return cloudConfigCtrl.takeUnless {
            FeatureCompat.sIsExpRom
        }?.let {
            Log.d(TAG, "updateSimulateClickScript: start check cloud config")
            it.create(SimulateClickScriptService::class.java)
                .getScriptFile()
                .subscribe { file ->
                    Log.d(TAG, "updateSimulateClickScript: receive cloud config $file")
                    runCatching {
                        receiver.receiveRemoteScripts(file)
                    }.onFailure { err ->
                        Log.e(TAG, "updateSimulateClickScript: ERROR! $err")
                    }
                }
        }
    }

    private fun getAreaCode(): AreaCode = when (region) {
        "EUEX" -> AreaCode.EU
        "IN" -> AreaCode.SA
        "CN" -> AreaCode.CN
        else -> AreaCode.SEA
    }
}