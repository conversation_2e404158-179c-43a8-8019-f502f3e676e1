/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : StartActivityUtilTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/4/26
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  maxiao         2023/4/26      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.util

import android.content.Context
import android.content.Intent
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.Constants
import com.oplus.smartenginehelper.entity.StartActivityClickEntity
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import org.json.JSONObject
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class StartActivityUtilTest {
    private lateinit var util: StartActivityUtil

    @Before
    fun setUp() {
        util = mockk()
    }

    @Test
    fun should_when_startFileManagerLabelListShowPopWindow() {
        every { util.startLabelListShowAddFileDialog(any(), any()) }.answers { callOriginal() }
        val start = mockk<StartActivityClickEntity>()
        val context = mockk<Context>() {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
        every { context.packageName }.returns("test")
        justRun { start.setPackageName(any()) }
        justRun { start.setAction(any()) }
        val paramsJson = mockk<JSONObject>()
        every { paramsJson.put(Constants.TITLE, "title") }.returns(paramsJson)
        every { paramsJson.put(Constants.LABEL_ID, 1L) }.returns(paramsJson)
        justRun { start.setParams(Constants.TITLE_AND_LABEL_ID, "json") }
        justRun { start.setCategory(any()) }
        justRun { start.addFlag(Intent.FLAG_ACTIVITY_CLEAR_TOP) }
        justRun { start.addFlag(Intent.FLAG_ACTIVITY_NEW_TASK) }

        Assert.assertNotNull(util.startLabelListShowAddFileDialog("title", 1L))
    }
}