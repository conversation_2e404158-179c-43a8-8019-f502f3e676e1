/***********************************************************
 * Copyright (C), 2008-2017 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File:
 * Description:
 * Version:
 * Date :
 * Author:
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
</desc></version></data></author> */
package com.oplus.ota.upgrade

import android.text.TextUtils
import com.filemanager.common.compat.PropertyCompat.sOTAInfo
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import java.io.*
import java.nio.charset.Charset
import java.nio.charset.StandardCharsets
import java.text.DecimalFormat
import java.util.*
import java.util.zip.ZipException
import java.util.zip.ZipFile

class UpgradeVersionCheck(isEncryptSupportOnly: Boolean) {
    private val mDeviceVersion: Version?
    private val mEncryptSupportOnly: Boolean
    fun scanningPackage(path: String): PackageInfo? {
        val file = File(path)
        if (file.isHidden || !file.isFile || !file.canRead()) {
            if (LOCAL_LOGV) {
                Log.v(TAG, "scanningPackage $path")
            }
            return null
        }
        val isZipSuffix = path.endsWith(".zip") || path.endsWith(".ozip")
        if (LOCAL_LOGV) {
            Log.v(TAG, "scanningPackage: isZipSuffix = $isZipSuffix")
        }
        if (!isZipSuffix) {
            return null
        }
        var info = isNewEncryptOtaFile(path, file)
        if (info != null) {
            return info
        }
        if (mEncryptSupportOnly) {
            Log.d(TAG, "package encrypt support only")
            return null
        }
        var zis: ZipFile? = null
        try {
            zis = ZipFile(file)
            val metaEntry = zis.getEntry(METADATA_PATH)
            if (metaEntry == null) {
                if (LOCAL_LOGV) {
                    Log.v(TAG, "not a upgrade package")
                }
                return null
            }
            val mis = zis.getInputStream(metaEntry)
            info = parseVersion(mis)
            if (info == null) {
                mis.close()
                return null
            }
            info.mPath = path
            mis.close()
            val scriptEntryMtk = zis.getEntry(METADATA_PATH)
                    ?: return null
            val sisMtk = zis.getInputStream(scriptEntryMtk)
            info.mWipe = detectPackageWipeFlag(sisMtk)
            sisMtk.close()
            val result = verifyPackage(info)
            info.mValid = result >= 0
            info.mSize = byteCountToDisplaySize(file.length())
            Log.w(TAG, "scanningPackage info.size = " + info.mSize)
        } catch (e: ZipException) {
            info = null
            if (LOCAL_LOGV) {
                Log.v(TAG, String.format(Locale.getDefault(), "not a zip(%s) -> %s", e.message, path))
            }
        } catch (e: IOException) {
            info = null
            Log.i(TAG, e.message)
        } catch (e: ArrayIndexOutOfBoundsException) {
            // for bugly Underlying library file exception
        } finally {
            if (zis != null) {
                try {
                    zis.close()
                } catch (e: IOException) {
                    Log.i(TAG, e.message)
                }
            }
        }
        return if (null != info && !verifyDeviceType(info)) {
            null
        } else info
    }

    private fun isNewEncryptOtaFile(path: String, file: File): PackageInfo? {
        /*
         * 1-16 tag file type, the size of the 17-32 file, 33-80 file SHA1
         * value, metadata 81-4176 file data
         */
        val headLength = 4176 /* 80+4096 oppo ota encrypt head length */
        val otaTag = 16 /* OPPOENCRYPT string buffer length */
        val header = 80
        val headerSize = 4096 /* metadata block length */
        var buffer: ByteArray? = null
        if (file.length() >= headLength) {
            var `is`: InputStream? = null
            try {
                `is` = FileInputStream(file)
                buffer = readBuffer(`is`, otaTag)
                var head: String? = null
                try {
                    head = String(buffer, Charset.forName("UTF-8"))
                } catch (e: UnsupportedEncodingException) {
                    Log.e(TAG, e.message)
                }
                Log.v(TAG, "scanningPackage: head $head")
                if (!TextUtils.isEmpty(head) && head!!.startsWith("OPPOENCRYPT!")) {
                    // what gui, don't care this chunk
                    //need to check return value. Coverity Id:11101
                    `is`.skip((header - otaTag).toLong())
                    buffer = readBuffer(`is`, headerSize)
                    try {
                        `is`.close()
                    } catch (e: IOException) {
                        // ignore.
                    }
                    `is` = ByteArrayInputStream(buffer)
                    val info = parseVersion(`is`) ?: return null
                    info.mPath = path
                    if (!info.mWipe) {
                        info.mWipe = detectPackageWipeFlag(`is`)
                    }
                    val result = verifyPackage(info)
                    info.mValid = result >= 0
                    if (!info.mValid) {
                        return null
                    }
                    Log.v(TAG, "isNewEncryptOtaFile: PackageInfo $info")
                    return info
                }
            } catch (e1: IOException) {
                return null
            } finally {
                if (`is` != null) {
                    try {
                        `is`.close()
                    } catch (e: IOException) {
                        // ignore.
                    }
                }
            }
        }
        return null
    }

    @Throws(IOException::class)
    private fun readBuffer(`is`: InputStream, length: Int): ByteArray {
        val buffer = ByteArray(length)
        var read = 0
        while (read < length) {
            read += `is`.read(buffer, read, length - read)
        }
        return buffer
    }

    private fun verifyDeviceType(info: PackageInfo): Boolean {
        val deviceVersion = mDeviceVersion
        val packageVersion = info.mPackageVersion
        return if (deviceVersion == null || !deviceVersion.mMachineType.equals(packageVersion!!.mMachineType, ignoreCase = true)) {
            // #ifdef ODM_CM_EDIT
            // hua.huang@ODM_CM.BAT.FileManager.2779744, 2020/7/1, Add for realmePhone specialVersion
            if (isRealmeSpecialMachine(deviceVersion, packageVersion)) {
                true
            } else {
                false
            }
            // #endif /* ODM_CM_EDIT */
        } else true
    }

    /**
     * Verify package from version.
     *
     * @param info
     * @return
     */
    private fun verifyPackage(info: PackageInfo): Int {
        val deviceVersion = mDeviceVersion
        val packageVersion = info.mPackageVersion
        if (deviceVersion == null || !deviceVersion.mMachineType.equals(packageVersion!!.mMachineType, ignoreCase = true)) {
            // #ifdef ODM_CM_EDIT
            // hua.huang@ODM_CM.BAT.FileManager.2779744, 2020/7/1, Add for realmePhone specialVersion
            if (isRealmeSpecialMachine(deviceVersion, packageVersion)) {
                //not return, continue to verify the package
            } else {
                return -1
            }
            // #endif /* ODM_CM_EDIT */
        }
        var wipeFlag = 0
        if (deviceVersion!!.mExtendType != null
                && !deviceVersion.mExtendType.equals(packageVersion!!.mExtendType, ignoreCase = true)) {
            wipeFlag = 1
        } else if (packageVersion!!.mExtendType != null
                && !packageVersion.mExtendType.equals(deviceVersion.mExtendType, ignoreCase = true)) {
            wipeFlag = 1
        } else if (deviceVersion.mSoftwareVersion!!.compareTo(packageVersion.mSoftwareVersion!!) > 0) {
            wipeFlag = 1
        }
        return wipeFlag
    }

    /** format as X907_11_4.04_120806_Beta, display in settings  */
    private fun deviceVersion(): Version? {
        // String sections[] = Build.DISPLAY.split("_");
        val sections = sOTAInfo.split("_".toRegex()).toTypedArray()
        if (sections == null) {
            Log.e(TAG, "deviceVersion: sections = null")
            return null
        }
        Log.d(TAG, "sections.length:" + sections.size)
        if (sections.size < DEVICE_LAST_INDEX) {
            return null
        }
        val version = Version()
        if (sections.size == LAST_INDEX) {
            version.mMachineType = sections[MACHINE_TYPE_INDEX]
            version.mHardwareVersion = sections[HARDWARE_VERSION_INDEX]
            version.mSoftwareVersion = sections[SOFTWARE_VERSION_INDEX]
            version.mDate = parseDateFromString(sections[DEVICE_DATE_INDEX])
            if (sections.size > DEVICE_LAST_INDEX) {
                version.mExtendType = sections[DEVICE_LAST_INDEX]
            }
        } else {
            val versions = sections[0].split("-".toRegex()).toTypedArray()
            if (versions.size > 1) {
                version.mMachineType = versions[0]
            } else {
                version.mMachineType = sections[0]
            }
            val hardSoftString = sections[1].split("\\.".toRegex()).toTypedArray()
            val len = hardSoftString.size
            val pos = 3
            if (len >= pos) {
                version.mHardwareVersion = hardSoftString[0]
                version.mSoftwareVersion = hardSoftString[1] + "." + hardSoftString[2]
            }
            version.mDate = parseDateFromString(sections[3])
            if (versions.size > 1) {
                version.mExtendType = versions[1]
            }
        }
        return version
    }

    private fun detectPackageWipeFlag(`is`: InputStream): Boolean {
        var wipe = false
        val reader: Reader = InputStreamReader(`is`, StandardCharsets.UTF_8)
        val buf = BufferedReader(reader)
        try {
            var line: String? = null
            while (buf.readLine().also { line = it } != null) {
                if (line!!.compareTo(USERDATA_PARTITION_OLD) == 0
                        || line!!.compareTo(USERDATA_PARTITION_NEW) == 0) {
                    Log.d(TAG, "detectPackageWipeFlag wipe = true")
                    wipe = true
                    break
                }
            }
        } catch (e: IOException) {
            Log.i(TAG, e.message)
        } finally {
            try {
                buf.close()
                reader.close()
            } catch (e: IOException) {
                Log.e(TAG, e.message)
            }
        }
        return wipe
    }

    private fun parseVersion(`is`: InputStream): PackageInfo? {
        var version: PackageInfo? = null
        val reader: Reader = InputStreamReader(`is`, StandardCharsets.UTF_8)
        val buf = BufferedReader(reader)
        var wipe = false
        try {
            var line: String? = null
            while (buf.readLine().also { line = it } != null) {
                if (line!!.startsWith(TAG_OTA_ID)) {
                    version = parseId(line)
                } else if (line!!.compareTo(USERDATA_PARTITION_OLD) == 0
                        || line!!.compareTo(USERDATA_PARTITION_NEW) == 0) {
                    Log.d(TAG, "parseVersion wipe = true")
                    wipe = true
                    if (version != null) {
                        break
                    }
                }
            }
            if (version != null) {
                version.mWipe = wipe
            }
        } catch (e: IOException) {
            Log.i(TAG, e.message)
        } finally {
            try {
                buf.close()
                reader.close()
            } catch (e: IOException) {
                Log.e(TAG, e.message)
            }
        }
        return version
    }

    /**
     * Parse format as: ota-id=X907_12_4.03_000_120718 or
     * ota-id=X907-beta_12_4.04_006_120802
     *
     * @return
     */
    private fun parseId(line: String?): PackageInfo? {
        if (LOCAL_LOGV) {
            Log.v(TAG, "parseId line: $line")
        }
        var equalIndex = line!!.indexOf('=')
        if (equalIndex == -1) {
            return null
        }
        equalIndex++
        if (equalIndex >= line.length) {
            return null
        }
        val id = line.substring(equalIndex)
        val sections = id.split("_".toRegex()).toTypedArray()
        if (DATE_INDEX > sections.size) {
            return null
        }
        val version = Version()
        val type = sections[MACHINE_TYPE_INDEX].split("-".toRegex()).toTypedArray()
        version.mMachineType = type[0]

        // section after treat as extend type
        if (type.size > 1) {
            version.mExtendType = type[1]
        }
        val info = PackageInfo()
        if (sections.size == LAST_INDEX) {
            version.mHardwareVersion = sections[HARDWARE_VERSION_INDEX]
            version.mSoftwareVersion = sections[SOFTWARE_VERSION_INDEX]
            version.mDate = parseDateFromString(sections[DATE_INDEX])
            info.mVersionOrder = sections[VERSION_ORDER_INDEX]
        } else {
            val hardSoftVersion = sections[1]
            if (LOCAL_LOGV) {
                Log.v(TAG, "hardSoftVersion = $hardSoftVersion")
            }
            val hardSoftString = hardSoftVersion.split("\\.".toRegex()).toTypedArray()
            version.mHardwareVersion = hardSoftString[0]
            version.mSoftwareVersion = hardSoftString[1] + "." + hardSoftString[2]
            version.mDate = parseDateFromString(sections[VERSION_ORDER_INDEX])
            info.mVersionOrder = sections[SOFTWARE_VERSION_INDEX]
        }
        info.mPackageVersion = version
        return info
    }

    private fun byteCountToDisplaySize(size: Long): String? {
        var displaySize: String? = null
        var tmp = 0f
        if (size / ONE_GB.also { tmp = it } > 1) {
            displaySize = SIZE_FORMAT.format(tmp.toDouble()) + "GB"
            displaySize = format(displaySize, "GB")
        } else if (size / ONE_MB.also { tmp = it } > 1) {
            displaySize = SIZE_FORMAT.format(tmp.toDouble()) + "MB"
            displaySize = format(displaySize, "MB")
        } else if (size / ONE_KB.also { tmp = it } > 1) {
            displaySize = SIZE_FORMAT.format(tmp.toDouble()) + "KB"
            displaySize = format(displaySize, "KB")
        } else {
            displaySize = (if (size == 0L) "0.00" else size.toString()) + "B"
            displaySize = format(displaySize, "B")
        }
        return displaySize
    }

    private fun format(display: String, str: String): String {
        var display = display
        if (!display.contains(".")) {
            display = display.substring(0, display.indexOf(str)) + ".00" + str
        }
        return display
    }

    // #ifdef ODM_CM_EDIT
    // hua.huang@ODM_CM.BAT.FileManager.2779744, 2020/7/1, Add for realmePhone specialVersion
    private fun isRealmeSpecialMachine(deviceVersion: Version?, packageVersion: Version?): Boolean {
        return if (Utils.isRealmePhone() && deviceVersion != null) {
            ("RMX1911EX".equals(deviceVersion.mMachineType, ignoreCase = true)
                    && "RMX2030EX".equals(packageVersion!!.mMachineType, ignoreCase = true))
        } else {
            false
        }
    } // #endif /* ODM_CM_EDIT */

    companion object {
        private const val TAG = "UpgradeVersionCheck"
        private const val LOCAL_LOGV = true
        private const val METADATA_PATH = "META-INF/com/android/metadata"
        private const val TAG_OTA_ID = "ota-id="
        private const val USERDATA_PARTITION_OLD = "wipe-data=1"
        private const val USERDATA_PARTITION_NEW = "wipe=1"
        private const val SIZE_FORMAT_STR = "#0.00"
        private const val MACHINE_TYPE_INDEX = 0
        private const val HARDWARE_VERSION_INDEX = 1
        private const val SOFTWARE_VERSION_INDEX = 2
        private const val VERSION_ORDER_INDEX = 3
        private const val DATE_INDEX = 4
        private const val LAST_INDEX = 5
        private const val DEVICE_DATE_INDEX = 3
        private const val DEVICE_LAST_INDEX = 4
        private const val ONE_KB = 1024f
        private const val ONE_MB = ONE_KB * ONE_KB
        private const val ONE_GB = ONE_KB * ONE_MB
        private val SIZE_FORMAT = DecimalFormat(SIZE_FORMAT_STR)

        /**
         * format as yymmdd
         *
         * @param sdate
         * @return time in milliseconds
         */
        private fun parseDateFromString(sdate: String): Long {
            val len = 6
            if (sdate.length < len) {
                return -1
            }
            val years = 2000
            val pos4 = 4
            val pos6 = 6
            val yy = years + sdate.substring(0, 2).toInt()
            val mm = sdate.substring(2, pos4).toInt()
            val dd = sdate.substring(pos4, pos6).toInt()
            val calendar = GregorianCalendar(Locale.CHINA)
            calendar[yy, mm] = dd
            return calendar.timeInMillis
        }
    }

    init {
        mDeviceVersion = deviceVersion()
        mEncryptSupportOnly = isEncryptSupportOnly
    }
}