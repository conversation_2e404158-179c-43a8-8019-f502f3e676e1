/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowContentResolver.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/8/29
 ** Author: JiaBo Lan
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/8/29      1.0     create
 ****************************************************************/
package shadow;

import android.content.ContentResolver;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.CancellationSignal;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(ContentResolver.class)
public class ShadowContentResolver {
    private static Cursor sCursor;
    @Implementation
    public final Cursor query(Uri uri,  String[] projection,  String selection,  String[] selectionArgs,  String sortOrder) {
        return sCursor;
    }
    @Implementation
    public final Cursor query(Uri uri, String[] projection, String selection, String[] selectionArgs, String sortOrder, CancellationSignal cancellationSignal) {
        return sCursor;
    }
    @Implementation
    public final Cursor query(Uri uri, String[] projection, Bundle queryArgs, CancellationSignal cancellationSignal) {
        return sCursor;
    }

    public static void setsCursor(Cursor cursor) {
        sCursor = cursor;
    }
}
