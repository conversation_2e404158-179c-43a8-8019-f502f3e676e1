/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DocumentFilesScanner
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/23 15:36
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/23       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.scanner

import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.FileLoader
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.WhiteListParser
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.category.document.DocumentExtensionTypeApi
import com.oplus.fileservice.bean.WebFileBean
import com.oplus.fileservice.bean.WebScannerResult
import com.oplus.fileservice.filelist.FileSelectPathService
import com.oplus.fileservice.filelist.loader.WebDocumentLoader
import com.oplus.fileservice.filelist.utils.getDateAndTimeByDefaultFormat
import com.oplus.fileservice.utils.WebFileConstant
import com.oplus.fileservice.utils.WebMimeTypeHelper
import java.util.Locale
import org.apache.commons.io.FilenameUtils
import kotlin.collections.ArrayList

class DocumentFilesScanner(pageNo: Int, pageSize: Int, sortOrderType: Int) : BaseUriScanner() {
    companion object {
        const val TAG = "ApkFilesScanner"
    }

    private var mPageNo = pageNo
    private var mPageSize = pageSize
    private var mTotal = 1
    private var mSortOrderType = sortOrderType
    private val mLoaderController = LoaderController()
    override fun scannerFiles(): WebScannerResult? {
        mLoaderController.initLoader(TAG.hashCode(), ServiceDocumentCallBack(0))
        return null
    }

    inner class ServiceDocumentCallBack(documentType: Int) :
        OnLoaderListener<MutableList<MediaFileWrapper>> {
        private var mDocumentType = documentType
        override fun onCreateLoader(): FileLoader<MutableList<MediaFileWrapper>>? {
            val extension = arrayListOf<String>()
            val docArray = WhiteListParser.getDocumentFormat()
            extension.add(MyApplication.sAppContext.resources.getString(com.filemanager.common.R.string.total))
            if ((docArray != null) && (docArray!!.size > 0)) {
                for (name in docArray!!) {
                    var ext = FilenameUtils.getExtension(name)
                    ext = if (TextUtils.isEmpty(ext)) {
                        name.toUpperCase(Locale.getDefault())
                    } else {
                        ext.toUpperCase(Locale.getDefault())
                    }
                    extension.add(ext)
                }
            }
            val documentLoader = WebDocumentLoader(
                appContext,
                UriHelper.geCategoryUri(CategoryHelper.CATEGORY_DOC),
                DocumentExtensionTypeApi.getDocumentCountSqlQuery(appContext),
                mDocumentType,
                extension[mDocumentType],
                WhiteListParser.getDocumentFormat()
            )
            documentLoader.setSort(mSortOrderType)
            return documentLoader
        }

        override fun onLoadComplete(result: MutableList<MediaFileWrapper>?) {
            val pageMediaFiles = pageData(result)
            val webFileBeans = convertMediaFileToWebFileBean(pageMediaFiles)
            mScannerCallback?.scannerSuc(WebScannerResult(mPageNo, mTotal, webFileBeans))
        }
    }

    @VisibleForTesting
    fun pageData(result: List<MediaFileWrapper>?): List<MediaFileWrapper>? {
        mTotal = result?.size ?: 0
        if (mPageSize < 1) {
            mPageSize = WebFileConstant.DEFAULT_PAGE_SIZE
            Log.d(TAG, "Service modify mPageSize $mPageSize")
        }
        var pageTotal = mTotal / mPageSize
        if (mTotal % mPageSize > 0) {
            pageTotal++
        }

        if (mPageNo > pageTotal) {
            Log.d(FileSelectPathService.TAG, "mPageNo > pageTotal is true, mPageNo = $mPageNo")
            mPageNo = pageTotal
        }
        if (mPageNo < 1) {
            mPageNo = 1
        }
        val start = mPageSize * (mPageNo - 1)
        val end = if (mPageSize * mPageNo > mTotal) {
            mTotal
        } else {
            mPageSize * mPageNo
        }

        return result?.subList(start, end)
    }

    @VisibleForTesting
    fun convertMediaFileToWebFileBean(resultFileList: List<MediaFileWrapper>?): ArrayList<WebFileBean> {
        val resultWebFileBean = ArrayList<WebFileBean>()
        if (resultFileList != null) {
            for (mediaFileWrapper in resultFileList) {
                mediaFileWrapper.apply {
                    val webFileBean = WebFileBean()
                    webFileBean.mFileName = FilenameUtils.getBaseName(mDisplayName)
                    webFileBean.mFilePath = mData
                    webFileBean.mFileId = mId ?: 0
                    webFileBean.mFileSize = mSize
                    webFileBean.mFileFormat = FilenameUtils.getExtension(mDisplayName)
                    webFileBean.mFileType =
                        WebMimeTypeHelper.getFileTypeFromExtension(webFileBean.mFileFormat)
                    webFileBean.mLastModifyDate = getDateAndTimeByDefaultFormat(mDateModified)
                    webFileBean.mDateModified = mDateModified
                    resultWebFileBean.add(webFileBean)
                }
            }
        }
        return resultWebFileBean
    }
}