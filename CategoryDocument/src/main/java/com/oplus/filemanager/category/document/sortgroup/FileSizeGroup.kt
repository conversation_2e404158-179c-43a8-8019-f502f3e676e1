/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/14
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
package com.oplus.filemanager.category.document.sortgroup

import androidx.annotation.StringRes
import com.filemanager.common.MyApplication
import com.filemanager.common.wrapper.MediaFileWrapper

class FileSizeGroup : AbsFileGroup() {

    companion object {
        const val SIZE_1_GB = 1024 * 1024 * 1024
        const val SIZE_100_MB = 100 * 1024 * 1024
        const val SIZE_10_MB = 10 * 1024 * 1024
        const val SIZE_1_MB = 1024 * 1024
    }

    override fun createGroupTitle(fileWrapper: MediaFileWrapper): String =
        MyApplication.appContext.resources.getString(fileWrapper.getSizeGroupTitleId())

    @StringRes
    private fun MediaFileWrapper.getSizeGroupTitleId(): Int = when {
        mSize < SIZE_1_MB -> com.filemanager.common.R.string.document_sort_string_memory_below_1MB
        mSize < SIZE_10_MB -> com.filemanager.common.R.string.document_sort_string_memory_1MB_10MB
        mSize < SIZE_100_MB -> com.filemanager.common.R.string.document_sort_string_memory_10MB_100MB
        mSize < SIZE_1_GB -> com.filemanager.common.R.string.document_sort_string_memory_100MB_1GB
        else -> com.filemanager.common.R.string.document_sort_string_memory_over_1GB
    }
}