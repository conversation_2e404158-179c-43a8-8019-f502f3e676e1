/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileUtils.kt
 ** Description: methods about File.java
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.cut

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.dfm.DFMManager
import java.io.File

object FileCutHelper {
    private const val TAG = "FileCutHelper"
    private val mInternalPath = VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)

    fun cutFile(sourceFile: File, destFile: File, checkParentSuccess: (path: String) -> Boolean): Boolean {
        fun internalCutFile(sourceFile: File, destFile: File): Boolean {
            return return try {
                if (!sourceFile.exists()) {
                    return checkParentSuccess.invoke(sourceFile.absolutePath)
                } else {
                    sourceFile.renameTo(destFile).let {
                        if (!it) {
                            Log.d(TAG, "internalCutFile: failed[source_exist=${sourceFile.exists()}," +
                                    " dest_exist=${destFile.exists()}]: ${sourceFile.absolutePath}")
                        }
                        it
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "internalCutFile: failed to rename file: ${destFile.name}, ${e.message}")
                false
            }
        }

        if (sourceFile.isDirectory) {
            if (destFile.exists()) {
                JavaFileHelper.listFiles(sourceFile)?.forEach {
                    if (!cutFile(it, File(destFile, it.name), checkParentSuccess)) {
                        return false
                    }
                }
                sourceFile.delete()
                return true
            } else {
                return internalCutFile(sourceFile, destFile)
            }
        } else {
            if (destFile.exists()) {
                Log.d(TAG, "internalCutFile: delete dest: ${destFile.absolutePath}")
                destFile.delete()
            }
            return internalCutFile(sourceFile, destFile)
        }
    }

    fun isDirectoriesSameDisk(source: List<BaseFileBean>, target: String): Boolean {
        //这个地方需要修改
        val sourceContainsDfmPath = containsDfm(source)
        val destIsDfmPath = KtUtils.checkIsDfmPath(target)
        //只要选择的数据中含有dfm或移动到的目的地中有dfm，这里都返回false，让移动操作走先copy+delete流程，而不是rename流程
        if (sourceContainsDfmPath || destIsDfmPath) {
            return false
        }

        val storageList = VolumeEnvironment.getVolumePathList()?.let { volumeList ->
            Log.d(TAG, "isDirectoriesSameDisk volumeList $volumeList")
            val specialNode = if (SdkUtils.isAtLeastR()) {
                arrayOf("Android/data", "Android/obb")
            } else {
                arrayOf("Android/obb")
            }
            val newList = mutableListOf<String>()
            volumeList.forEach {
                var tmpVolume = it
                var oldVolume = it
                if (it.endsWith(File.separator)) {
                    oldVolume = it.substring(0, it.length - 1)
                } else {
                    tmpVolume = it.plus(File.separator)
                }
                newList.add(oldVolume)
                specialNode.forEach { node ->
                    // NOTE: Must add it into index 0, to make sure the special node
                    // can be checked first in the next logic
                    newList.add(0, tmpVolume.plus(node))
                }
            }
            newList
        } ?: return true

        fun getVolume(path: String?): String? {
            if (path.isNullOrEmpty().not()) {
                for (volume in storageList) {
                    if (path!!.startsWith(volume, true)) {
                        return volume
                    }
                }
            }
            return null
        }

        val destVolume = getVolume(target)
        if (destVolume.isNullOrEmpty().not()) {
            source.forEach {
                if (getVolume(it.mData) != destVolume) {
                    return false
                }
            }
        }
        return true
    }

    // Pair.first is size, second is mean has image in internal storage or not
    fun checkSizeAndInternalStorageImage(file: File): Pair<Long, Boolean> {
        var size = 0L
        var hasImg = false

        fun innerCheckSizeAndImage(file: File) {
            size += file.length() // include the self of this dir
            if (file.isDirectory) {
                JavaFileHelper.listFiles(file)?.forEach {
                    innerCheckSizeAndImage(it)
                }
            } else if (!hasImg) {
                hasImg = (MimeTypeHelper.getTypeFromPath(file.name) == MimeTypeHelper.IMAGE_TYPE)
                        && file.absolutePath.startsWith(mInternalPath)
            }
        }

        innerCheckSizeAndImage(file)
        return Pair(size, hasImg)
    }

    /**
     * 根据confirmBean中的目的地路径，获取目的地的名称
     */
    fun getDestDevicesName(
        context: Context,
        confirmBean: FileCutObserver.CutConfirmBean,
    ): String {
        val destStorageType =
            KtUtils.getStorageByPath(MyApplication.appContext, confirmBean.destPath)
        val deviceName = when (destStorageType) {
            KtUtils.STORAGE_EXTERNAL -> context.getString(R.string.storage_external)
            KtUtils.STORAGE_DMF -> DFMManager.getDFSDeviceName() ?: KtConstants.DFM_MOUNT_PATH_SUFFIX
            KtUtils.STORAGE_OTG -> context.getString(R.string.storage_otg)
            else -> context.getString(R.string.string_all_files)
        }
        return deviceName
    }


    /**
     * 根据confirmBean中的目的地路径，获取目的地的名称
     */
    fun getDestShowPath(
        context: Context,
        confirmBean: FileCutObserver.CutConfirmBean,
    ): String {
        val showVirtualPath = Utils.getVirtualPathString(context, confirmBean.destPath)
        Log.d(TAG, "getDestShowPath input ${confirmBean.destPath}, output $showVirtualPath")
        return showVirtualPath
    }

    /**
     * 判定这次移动是否是处于跨设备之间
     */
    @JvmStatic
    fun checkTransBetweenDevices(source: List<BaseFileBean>, target: String): Boolean {
        val destinationType = KtUtils.getStorageByPath(MyApplication.appContext, target)
        val sourceTypeList = getPathTypeList(source)
        val sourceIsSingleType = sourceTypeList.size == 1
        val result = if (sourceIsSingleType) {
            val sourceType = sourceTypeList[0]
            sourceType != destinationType
        } else {
            true
        }
        Log.d(
            TAG,
            "checkMoveBetweenDevices target:$target, source:$source destinationType:$destinationType," +
                    "sourceTypeList:$sourceTypeList, sourceIsSingleType:$sourceIsSingleType, result:$result"
        )
        return result
    }

    /**
     * 判定列表中是否包含跨端dfm文件
     */
    @JvmStatic
    fun containsDfm(source: List<BaseFileBean>): Boolean {
        val dfmList = source.filter {
            val path = it.mData
            val isDfmPath = KtUtils.checkIsDfmPath(path)
            isDfmPath
        }
        return dfmList.isNotEmpty()
    }


    /**
     * 判定列表中是否包含本地的文件（OTG,设备存储，sdcard）
     */
    @JvmStatic
    private fun getPathTypeList(source: List<BaseFileBean>): List<Int> {
        var destStorageType = -1
        val typeSet: MutableSet<Int> = mutableSetOf()
        source.forEach {
            destStorageType = KtUtils.getStorageByPath(MyApplication.appContext, it.mData)
            when (destStorageType) {
                KtUtils.STORAGE_EXTERNAL -> typeSet.add(destStorageType)
                KtUtils.STORAGE_DMF -> typeSet.add(destStorageType)
                KtUtils.STORAGE_OTG -> typeSet.add(destStorageType)
                KtUtils.STORAGE_INTERNAL -> typeSet.add(destStorageType)
                else -> Log.w(TAG, "getPathTypeList input path ${it.mData} not qualify, ignore")
            }
        }
        val result = typeSet.toList()
        Log.d(TAG, "getPathTypeList resultSize:${result.size}, result:$result, input:$source")
        return result
    }
}