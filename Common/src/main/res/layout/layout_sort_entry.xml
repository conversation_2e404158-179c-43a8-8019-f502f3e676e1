<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/sort_entry_file_count_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/sort_entry_margin"
        android:ellipsize="end"
        android:gravity="start"
        android:singleLine="true"
        android:textAppearance="?android:attr/textAppearanceLarge"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textSize="@dimen/font_size_12"
        app:layout_constraintBottom_toBottomOf="@id/sort_entry_order_img"
        app:layout_constraintEnd_toStartOf="@id/sort_entry_order_text"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/sort_entry_order_img"
        tools:text="共 12 项" />

    <TextView
        android:id="@+id/sort_entry_order_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="middle"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:textAppearance="?attr/couiTextBodyM"
        android:textColor="?attr/couiColorLabelPrimary"
        app:layout_constraintStart_toEndOf="@id/sort_entry_file_count_text"
        app:layout_constraintBottom_toBottomOf="@id/sort_entry_order_img"
        app:layout_constraintEnd_toStartOf="@id/sort_entry_order_img"
        app:layout_constraintTop_toTopOf="@id/sort_entry_order_img"
        tools:text="修改时间" />

    <ImageView
        android:id="@+id/sort_entry_order_img"
        android:layout_width="@dimen/dimen_24dp"
        android:layout_height="@dimen/dimen_24dp"
        android:layout_marginTop="@dimen/dimen_6dp"
        android:layout_marginEnd="@dimen/sort_entry_margin"
        android:layout_marginBottom="@dimen/dimen_6dp"
        android:src="@drawable/icon_sort_desc"
        android:contentDescription="@string/menu_file_list_sort"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/sort_entry_anchor"
        android:layout_width="@dimen/dimen_1dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>