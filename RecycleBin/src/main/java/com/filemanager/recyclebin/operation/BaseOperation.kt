/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - BaseOperation.java
 * Description: Base Operation class for recyclebin function
 * Version: 1.0
 * Date : 2020/02/26
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/02/26    1.0     create
 ****************************************************************/

package com.filemanager.recyclebin.operation

import android.net.Uri
import android.os.AsyncTask
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.controller.BaseLifeController
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.recyclebin.operation.listener.OperationListener
import java.lang.ref.WeakReference
import kotlin.math.ceil

abstract class BaseOperation<WeakTarget>(target: WeakTarget,
                                         private var mProgressListener: ProgressListener?)
    : AsyncTask<Void, Int, BaseOperation.OperationResult>(), BaseLifeController {
    protected val mTarget: WeakReference<WeakTarget> = WeakReference(target)

    companion object {
        private const val TAG = "BaseOperation"
        private const val MILLIS_ONE_DAY = 24 * 60 * 60 * 1000L

        /**
         * status code for all operation in recyclebin,if statusCode is Positive, represent failed count
         */
        // status code for success
        internal const val STATUS_SUCCESS = 0

        // status code for common exception
        internal const val STATUS_ERROR = -1

        // status code for low space exception
        internal const val STATUS_NOT_ENOUGH_SPACE = -2

        // status code for protected directory exception
        internal const val STATUS_PROTECTED_DIR = -3

        // status code for cancel opertion in alertdialog after recycle failed
        internal const val STATUS_ERROR_CANCEL = -4

        // status code for total file size reach limit size
        internal const val STATUS_REACH_LIMIT = -5

        // status code for delete failed when the dfs device is disconnected
        internal const val STATUS_DELETE_ERROR_DFM_DISCONNECT = -6

        internal const val PARAM_DELETE_DATA = "deletedata"

        fun statisticsDurationDate(scene: Int, recycleDate: Long) {
            val duration: Double = ((System.currentTimeMillis() - recycleDate.toDouble()) / MILLIS_ONE_DAY)
            val saveDays = ceil(duration).toLong()
            Log.v(TAG, "duration = $duration saveDays = $saveDays")
            // ignore nagitive value
            if (duration > 0) {
                StatisticsUtils.nearMeStatisticsRecycleBinFileSaveDate(MyApplication.sAppContext, scene, saveDays)
            } else {
                Log.d(TAG, "statisticsDurationDate nagitive value")
            }
        }
    }

    abstract fun addPaths(paths: ArrayList<String>?): Boolean

    abstract fun addUris(uris: ArrayList<Uri>?): Boolean

    abstract fun addFileBeans(files: ArrayList<BaseFileBean>?): Boolean

    protected abstract fun onTotalCountCallback(): Int

    protected abstract fun onParametersCallback(): OperationParameters?

    override fun onPreExecute() {
        super.onPreExecute()
        mProgressListener?.onProgressStart()
        Utils.setOperatingFlag(true)
    }

    override fun onPostExecute(result: OperationResult) {
        mProgressListener?.onProgressComplete(onParametersCallback(), result)
        mProgressListener = null
        Utils.setOperatingFlag(false)
    }

    override fun onProgressUpdate(vararg integers: Int?) {
        super.onProgressUpdate(*integers)
        mProgressListener?.onProgressUpdate(integers[0])
    }

    override fun onDestroy() {
        mTarget.clear()
        if (mProgressListener is OperationListener) {
            (mProgressListener as OperationListener).recycle()
        }
        mProgressListener = null
    }

    /**
     * base class for operation parameters
     * @param mUris uri list to operation
     * @param mPaths path list to operation
     * @param mBaseFiles filebean list to operation
     */
    open class OperationParameters(val mUris: ArrayList<Uri>? = null,
                                   val mPaths: ArrayList<String>? = null,
                                   val mBaseFiles: ArrayList<BaseFileBean>? = null) {
        constructor(mBaseFiles: ArrayList<BaseFileBean>?) : this(null, null, mBaseFiles)
    }

    /**
     * base class for operation result data
     * @param mTotalCount size of request list
     */
    open class OperationResult(val mTotalCount: Int, val mFailedCount: Int, val mStatusCode: Int) {
        override fun toString(): String {
            return "[mTotalCount: $mTotalCount,  mFailedCount: $mFailedCount, mStatusCode: $mStatusCode]"
        }
    }

    interface ProgressListener {
        fun onProgressStart()

        fun onProgressUpdate(progress: Int?)

        fun onProgressComplete(parameters: OperationParameters?, result: OperationResult)
    }
}