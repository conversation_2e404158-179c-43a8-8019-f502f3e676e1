<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <path
      android:pathData="M16,16m-16,0a16,16 0,1 1,32 0a16,16 0,1 1,-32 0"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M25.659,14.972L21.75,8.06C21.248,7.171 20.355,6.725 19.462,6.725V6.725V6.725H12.537C13.43,6.725 14.319,7.171 14.824,8.06L16,10.141L16.206,10.507L18.733,14.972C19.188,15.777 19.188,16.758 18.733,17.559L16.206,22.023L16,22.39L17.175,24.471C17.676,25.36 18.569,25.806 19.462,25.806V25.806C20.355,25.806 21.244,25.36 21.75,24.471L25.659,17.559C26.114,16.758 26.114,15.777 25.659,14.972Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="12.537"
          android:startY="6.725"
          android:endX="12.537"
          android:endY="25.806"
          android:type="linear">
        <item android:offset="0" android:color="#FF27DDFF"/>
        <item android:offset="0.506" android:color="#FF10DAFF"/>
        <item android:offset="1" android:color="#FF0AC7E9"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M17.175,24.471L16,22.39L15.794,22.024L13.266,17.559C12.811,16.754 12.811,15.773 13.266,14.973L15.794,10.508L16,10.141L14.825,8.06C14.324,7.172 13.431,6.725 12.538,6.725V6.725V6.725C11.644,6.725 10.756,7.172 10.25,8.06L6.341,14.973C5.886,15.777 5.886,16.759 6.341,17.559L10.25,24.471C10.752,25.36 11.644,25.806 12.538,25.806H19.463C18.57,25.806 17.681,25.36 17.175,24.471Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="6"
          android:startY="6.725"
          android:endX="6"
          android:endY="25.806"
          android:type="linear">
        <item android:offset="0" android:color="#FF0658EF"/>
        <item android:offset="0.51" android:color="#FF0D62FE"/>
        <item android:offset="1" android:color="#FF2974FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16,16m-15.835,0a15.835,15.835 0,1 1,31.67 0a15.835,15.835 0,1 1,-31.67 0"
      android:strokeAlpha="0.12"
      android:strokeWidth="0.33"
      android:fillColor="#00000000"
      android:strokeColor="#000000"/>
</vector>
