/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.coloros.filemanager.AlbumSetItemDecorationTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/5/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.common.decoration

import android.content.Context
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.decoration.AlbumSetItemDecoration
import com.filemanager.common.utils.WindowUtils
import com.oplus.filemanager.category.albumset.adapter.AlbumSetAdapter
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class AlbumSetItemDecorationTest {
    @MockK
    lateinit var mContext: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        mContext = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
            every { resources }.returns(mockk {
                every { getDimensionPixelSize(R.dimen.album_set_grid_item_horizontal_spacing_col_3) } returns 3
                every { getDimensionPixelSize(R.dimen.album_set_grid_padding_left_col_3) } returns 3
                every { getDimensionPixelSize(R.dimen.album_set_grid_item_horizontal_spacing_col_5) } returns 5
                every { getDimensionPixelSize(R.dimen.album_set_grid_padding_left_col_5) } returns 5
                every { getDimensionPixelSize(R.dimen.album_set_grid_item_horizontal_spacing_col_6) } returns 6
                every { getDimensionPixelSize(R.dimen.album_set_grid_padding_left_col_6) } returns 6
                every { getDimensionPixelSize(R.dimen.album_set_grid_item_margin_top) } returns 16
                every { getDimensionPixelSize(R.dimen.album_set_grid_padding_bottom) } returns 16
            })
        }
        MyApplication.init(mContext)
        mockkStatic(WindowUtils::class)
        every { WindowUtils.supportLargeScreenLayout(any()) }.returns(false)
    }

    @After
    fun teardown() {
        unmockkStatic(WindowUtils::class)
    }

    @Test
    fun `should return 3 update span count 3`() {
        val decoration = AlbumSetItemDecoration(3)
        assertEquals(3, decoration.mEdgeSpace)
        assertEquals(3, decoration.mSpace)
    }

    @Test
    fun `should return 5 update span count 5`() {
        val decoration = AlbumSetItemDecoration(5)
        assertEquals(5, decoration.mEdgeSpace)
        assertEquals(5, decoration.mSpace)
    }

    @Test
    fun `should return 6 update span count 6`() {
        val decoration = AlbumSetItemDecoration(6)
        assertEquals(6, decoration.mEdgeSpace)
        assertEquals(6, decoration.mSpace)
    }

    @Test
    fun `should return 5 update span count else`() {
        val decoration = AlbumSetItemDecoration(5)
        assertEquals(5, decoration.mEdgeSpace)
        assertEquals(5, decoration.mSpace)
    }

    @Test
    fun `should set offset when different item type`() {
        val decoration = AlbumSetItemDecoration(3)
        val recyclerView = mockk<RecyclerView>()
        val adapter = mockk<AlbumSetAdapter>()
        mockkStatic(WindowUtils::class)
        every { WindowUtils.getScreenHeight(any()) } answers { 1000 }
        val view = View(mContext)
        view.layoutParams = ViewGroup.LayoutParams(200, 100)
        val rect = Rect()
        every { recyclerView.adapter } answers { adapter }
        every { recyclerView.getChildAdapterPosition(any()) } answers { 0 }
        every { adapter.getItemViewType(any()) } answers { BaseFileBean.TYPE_FILE_AD }
        decoration.getItemOffsets(rect, view, recyclerView, RecyclerView.State())
        unmockkStatic(WindowUtils::class)
    }
}