/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: CategoryAppConfigTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/8/30
 ** Author: JiaB<PERSON>n
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/8/30      1.0     create
 ****************************************************************/
package com.coloros.filemanager.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.database.Cursor;

import com.coloros.filemanager.BaseTest;
import com.filemanager.common.utils.CategoryAppConfig;

import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import shadow.ShadowContentResolver;
import shadow.ShadowJsonReader;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@Ignore("stupid test")
@RunWith(RobolectricTestRunner.class)
public class CategoryAppConfigTest extends BaseTest {
    public static final String APP_CONFIG_VERSION = "app_config_version";
    public static final String APP_CONFIG = "filemanager_category_view_app_config";
    private CategoryAppConfig mCategoryAppConfig;
    private Cursor mCursor;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = RuntimeEnvironment.application;
        mCategoryAppConfig = new CategoryAppConfig();
    }

    @After
    public void tearDown() {
        mCategoryAppConfig = null;
        mCursor = null;
    }

    @Ignore
    @Test
    @Config(shadows = {ShadowContentResolver.class, ShadowJsonReader.class})
    public void should_verify_when_initialize_without() {
        byte[] data = new byte[] {1,2};
        mCursor = mock(Cursor.class);
        when(mCursor.moveToFirst()).thenReturn(true);
        when(mCursor.getBlob(1)).thenReturn(data);
        when(mCursor.getInt(0)).thenReturn(1);
        ShadowContentResolver.setsCursor(mCursor);
        mCategoryAppConfig.initialize(mContext);
        SharedPreferences sharedPreferences = mContext.getSharedPreferences(APP_CONFIG, Context.MODE_PRIVATE);
        assertEquals(1, sharedPreferences.getInt(APP_CONFIG_VERSION, -1));
    }
}