/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ShareDataFactory
 * * Description : 分享数据 工厂
 * * Version     : 1.0
 * * Date        : 2025/05/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.framework.touchshare.data

import android.net.Uri
import androidx.annotation.WorkerThread
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import com.oplus.interconnect.share.sdk.data.MimeType
import com.oplus.interconnect.share.sdk.data.SharedData
import com.oplus.interconnect.share.sdk.data.SharedRecord

object ShareDataFactory {

    private const val TAG = "ShareDataFactory"

    private val fileManagerPkg = mutableListOf(
        "com.coloros.filemanager",
        "com.oneplus.filemanager"
    )

    /**
     * 构造 分享数据
     * @param from 分享来源
     * @param files 文件列表
     * @param isReShare 是否是二次分享
     */
    fun create(from: Int, files: List<BaseFileBean>, isReShare: Boolean = false): SharedData {
        val recordList = createShareRecords(files)
        val sharedData = SharedData(recordList, fileManagerPkg, "category_$from", isReShare)
        return sharedData
    }

    private fun createShareRecords(files: List<BaseFileBean>): MutableList<SharedRecord> {
        val list = mutableListOf<SharedRecord>()
        files.forEach { file ->
            val mimeType = getMimeType(file)
            val uri = UriHelper.getFileUri(file, null, file.mLocalType)
            val record = SharedRecord(mimeType, null, uri.toString(), null)
            list.add(record)
        }
        return list
    }

    private fun getMimeType(file: BaseFileBean): MimeType {
        val localType = file.mLocalType
        val mimeType = when (localType) {
            MimeTypeHelper.IMAGE_TYPE -> MimeType.IMAGE
            MimeTypeHelper.VIDEO_TYPE -> MimeType.VIDEO
            MimeTypeHelper.AUDIO_TYPE -> MimeType.AUDIO
            MimeTypeHelper.TXT_TYPE -> MimeType.TEXT
            else -> MimeType.FILE
        }
        Log.d(TAG, "getMimeType ${file.mData} local:$localType mimeType:$mimeType")
        return mimeType
    }

    /**
     * 解析分享数据中的文件信息
     */
    @WorkerThread
    fun parse(sharedData: SharedData?): List<BaseFileBean> {
        if (sharedData == null) {
            Log.w(TAG, "parse share data is null")
            return emptyList()
        }
        Log.d(TAG, "parse data:$sharedData")
        val context = MyApplication.appContext
        val records = sharedData.getRecords()
        return records.map {
            val uri = Uri.parse(it.uri)
            val file = UriHelper.getBaseFileBeanFromUri(context, uri)
            file
        }.filterNotNull().toList()
    }

    /**
     * 简单解析分享数据中的文件信息
     * @param sharedData 分享数据
     * @return pair first: 文件个数，second: 第一个文件的信息
     */
    @WorkerThread
    fun parseSimple(sharedData: SharedData?): Pair<Int, BaseFileBean?> {
        if (sharedData == null) {
            Log.w(TAG, "parseSimple share data is null")
            return Pair(0, null)
        }
        Log.d(TAG, "parseSimple data:$sharedData")
        val records = sharedData.getRecords()
        if (records.isEmpty()) {
            return Pair(0, null)
        }
        val context = MyApplication.appContext
        val record = records.get(0)
        val uri = Uri.parse(record.uri)
        val file = UriHelper.getBaseFileBeanFromUri(context, uri)
        return Pair(records.size, file)
    }
}