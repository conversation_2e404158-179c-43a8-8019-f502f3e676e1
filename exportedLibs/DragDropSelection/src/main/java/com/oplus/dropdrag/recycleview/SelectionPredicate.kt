/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - SelectionPredicates.kt
 ** Description: Selection Predicates
 ** Version: 1.0
 ** Date : 2020/05/16
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/16    1.0     create
 ****************************************************************/

package com.oplus.dropdrag.recycleview

import androidx.annotation.Keep

/**
 * Implement SelectionPredicate to control when items can be selected or unselected.
 * See [Builder.withSelectionPredicate].
 */
@Keep
object SelectionPredicates {

    @Keep
    abstract class SelectionPredicate<K> {
        /**
         * Validates a change to selection for a specific key.
         * @param key the item key
         * @param nextState the next potential selected/unselected state
         * @return true if the item at `id` can be set to `nextState`.
         */
        abstract fun canSetStateForKey(key: K, nextState: Boolean): Boolean

        /**
         * Validates a change to selection for a specific position. If necessary
         * use [ItemKeyProvider] to identy associated key.
         * @param position the item position
         * @param nextState the next potential selected/unselected state
         * @return true if the item at `id` can be set to `nextState`.
         */
        abstract fun canSetStateAtPosition(position: Int, nextState: Boolean): Boolean

        /**
         * Permits restriction to single selection mode. Single selection mode has
         * unique behaviors in that it'll deselect an item already selected
         * in order to select the new item.
         *
         *
         *
         * In order to limit the number of items that can be selected,
         * use [.canSetStateForKey] and
         * [.canSetStateAtPosition].
         *
         * @return true if more than a single item can be selected.
         */
        abstract fun canSelectMultiple(): Boolean
    }

    /**
     * Returns a selection predicate that allows multiples items to be selected, without
     * any restrictions on which items can be selected.
     */
    fun <K> createMultiplesSelect(): SelectionPredicate<K> {
        return object : SelectionPredicate<K>() {
            override fun canSetStateForKey(key: K, nextState: Boolean): Boolean {
                return true
            }

            override fun canSetStateAtPosition(position: Int, nextState: Boolean): Boolean {
                return true
            }

            override fun canSelectMultiple(): Boolean {
                return true
            }
        }
    }

    /**
     * Returns a selection predicate that allows a single item to be selected, without
     * any restrictions on which item can be selected.
     */
    fun <K> createSingleSelect(): SelectionPredicate<K> {
        return object : SelectionPredicate<K>() {
            override fun canSetStateForKey(key: K, nextState: Boolean): Boolean {
                return true
            }

            override fun canSetStateAtPosition(position: Int, nextState: Boolean): Boolean {
                return true
            }

            override fun canSelectMultiple(): Boolean {
                return false
            }
        }
    }
}


