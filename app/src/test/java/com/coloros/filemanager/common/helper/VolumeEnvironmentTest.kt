/*
 * ********************************************************************
 *  * * Copyright (C), 2019 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  VolumeEnvironmentTest.java
 *  * * Description : VolumeEnvironmentTest.java
 *  * * Version     : 1.0
 *  * * Date        : 12-13-23 下午5:34
 *  * * Author      : W9010863
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.coloros.filemanager.common.helper

import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.Environment
import android.os.storage.StorageManager
import android.os.storage.StorageVolume
import android.text.TextUtils
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.OplusUsbEnvironmentCompat
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.oplus.compat.os.storage.StorageVolumeNative
import com.oplus.compat.utils.util.UnSupportedApiVersionException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.After
import java.io.File

class VolumeEnvironmentTest {
    lateinit var mContext: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        mContext = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }

        MyApplication.init(mContext)
        mockkStatic(SdkUtils::class)
        every { SdkUtils.getOSVersion() }.returns(25)
    }

    @Test
    fun `should verify when getDataDirPath without`() {
        var info = ApplicationInfo()
        info.dataDir = "test"

        every { mContext.packageName }.returns("test")
        every { mContext.packageManager }.returns(mockk(relaxed = true) {
            every { getApplicationInfo("test", 0) }.returns(info)
        })

        Assert.assertEquals("test", VolumeEnvironment.getDataDirPath(mContext))

        try {
            every { mContext.packageName }.throws(PackageManager.NameNotFoundException("test-error"))
            Assert.assertTrue(VolumeEnvironment.getDataDirPath(mContext).contains("/data/user/"))
        } catch (e: Exception) {
            println("test!!!")
        }
    }

    @Test
    fun `should verify when getDataDirPath2 without`() {
        Assert.assertEquals("", VolumeEnvironment.getDataDirPath(mContext, null))

        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getDataDirPath(mContext) }.returns("test")

        Assert.assertEquals("test" + File.separator + "pass", VolumeEnvironment.getDataDirPath(mContext, "pass"))
    }

    @Test
    fun `should verify when isSingleSdcard without`() {
        Assert.assertTrue(VolumeEnvironment.isSingleSdcard(null))
    }

    @Test
    fun `should verify when isInternalSdMounted without`() {
        mockkStatic(VolumeEnvironment::class)

        every { VolumeEnvironment.getInternalSdState(null) }.returns(null)
        Assert.assertFalse(VolumeEnvironment.isInternalSdMounted(null))

        every { VolumeEnvironment.getInternalSdState(null) }.returns(Environment.MEDIA_MOUNTED_READ_ONLY)
        Assert.assertTrue(VolumeEnvironment.isInternalSdMounted(null))

        every { VolumeEnvironment.getInternalSdState(null) }.returns(Environment.MEDIA_MOUNTED)
        Assert.assertTrue(VolumeEnvironment.isInternalSdMounted(null))
    }

    @Ignore
    fun `should verify when isExternalSdMounted without`() {
        mockkStatic(VolumeEnvironment::class)

        every { VolumeEnvironment.getExternalSdState(null) }.returns(null)
        Assert.assertFalse(VolumeEnvironment.isExternalSdMounted(null))

        every { VolumeEnvironment.getExternalSdState(null) }.returns(Environment.MEDIA_MOUNTED_READ_ONLY)
        Assert.assertTrue(VolumeEnvironment.isExternalSdMounted(null))

        every { VolumeEnvironment.getExternalSdState(null) }.returns(Environment.MEDIA_MOUNTED)
        Assert.assertTrue(VolumeEnvironment.isExternalSdMounted(null))
    }

    @Ignore
    fun `should verify when getExternalSdState without`() {
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(OplusUsbEnvironmentCompat::class)

        every { OplusUsbEnvironmentCompat.getExternalPath(mContext) }.returns("test")
        every { VolumeEnvironment.isOTGVersion(mContext) }.returns(true)

        VolumeEnvironment.getExternalSdState(mContext)

        every { VolumeEnvironment.isOTGVersion(mContext) }.returns(false)
        VolumeEnvironment.getExternalSdState(mContext)
    }


    @Test
    fun `should verify when isNeedLoadPath without`() {
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(TextUtils::class)
        every { TextUtils.isEmpty(null) }.returns(true)
        every { VolumeEnvironment.isMountedByPath(null, null) }.returns(false)

        Assert.assertTrue(VolumeEnvironment.isNeedLoadPath(null, null))
    }

    @Test
    fun `should verify when isMountedByPath without`() {
        VolumeEnvironment.clearValue()
        Assert.assertFalse(VolumeEnvironment.isMountedByPath(null, null))

        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(null)
        Assert.assertFalse(VolumeEnvironment.isMountedByPath(mContext, "test"))


        var storageManagerMockk = mockk<StorageManager>(relaxed = true)
        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(storageManagerMockk)
        every { storageManagerMockk.getStorageVolume(any<File>()) }.returns(mockk {
            every { state }.returns(Environment.MEDIA_CHECKING)
        })
        Assert.assertTrue(VolumeEnvironment.isMountedByPath(mContext, "test"))
    }

    @Test
    fun `should verify when getVolumePathList without`() {
        VolumeEnvironment.clearValue()
        mockkStatic(FeatureCompat::class)
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(StorageVolumeNative::class)
        mockkStatic(TextUtils::class)

        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(null)
        Assert.assertNull(VolumeEnvironment.getVolumePathList())

        var storageManagerMockk = mockk<StorageManager>(relaxed = true) {
            every { storageVolumes }.returns(arrayListOf())
        }
        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(storageManagerMockk)

        Assert.assertNull(VolumeEnvironment.getVolumePathList())

        every { storageManagerMockk.storageVolumes }.returns(arrayListOf(mockk {
            every { state }.returns(Environment.MEDIA_MOUNTED)
            every { directory!!.absolutePath }.returns("test")
        }))

        every { TextUtils.isEmpty("test") }.returns(false)
        every { VolumeEnvironment.getInternalSdPath(mContext) }.returns("test")

        every { FeatureCompat.sIsNotSupportSD }.returns(false)
        Assert.assertFalse(VolumeEnvironment.getVolumePathList(false).isEmpty())
    }

    @Test
    fun `should verify when upDate without`() {
        VolumeEnvironment.getInternalSdState(null)

        var storageManager = mockk<StorageManager>(relaxed = true)
        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(storageManager)

        every { storageManager.storageVolumes }.returns(arrayListOf())
        VolumeEnvironment.getInternalSdState(mContext)

        var storageVolumeMockk = mockk<StorageVolume>(relaxed = true)

        mockkStatic(StorageVolumeNative::class)
        every { storageVolumeMockk.directory!!.absolutePath }.returns("test")

        every { storageVolumeMockk.isRemovable }.returns(true)
        every { storageManager.storageVolumes }.returns(arrayListOf(storageVolumeMockk))
        VolumeEnvironment.getInternalSdState(mContext)

        every { storageVolumeMockk.isRemovable }.returns(false)
        every { storageManager.storageVolumes }.returns(arrayListOf(storageVolumeMockk))
        VolumeEnvironment.getInternalSdState(mContext)

    }

    @Test
    fun `should verify when getVolumeCount without`() {
        Assert.assertTrue(VolumeEnvironment.isSingleSdcard(null))

        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(null)
        Assert.assertTrue(VolumeEnvironment.isSingleSdcard(mContext))

        var storageManagerMockk = mockk<StorageManager>(relaxed = true)
        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(storageManagerMockk)
        var storageVolumeMockk = mockk<StorageVolume>(relaxed = true)
        every { storageManagerMockk.storageVolumes }.returns(arrayListOf(storageVolumeMockk))

        every { storageVolumeMockk.isRemovable }.returns(false)
        Assert.assertTrue(VolumeEnvironment.isSingleSdcard(mContext))

        every { storageVolumeMockk.isRemovable }.returns(true)

        mockkStatic(StorageVolumeNative::class)
        every { storageVolumeMockk.directory!!.absolutePath }.returns(null)
        Assert.assertTrue(VolumeEnvironment.isSingleSdcard(mContext))

        every { storageVolumeMockk.directory!!.absolutePath }.returns("test")
        every { storageVolumeMockk.state }.returns(Environment.MEDIA_CHECKING)
        Assert.assertFalse(VolumeEnvironment.isSingleSdcard(mContext))
    }

    @Test
    fun `should verify when getVolumeState without`() {
        VolumeEnvironment.clearValue()

        var storageManager = mockk<StorageManager>(relaxed = true)


        var storageVolumeMockk = mockk<StorageVolume>(relaxed = true)

        every { storageManager.storageVolumes }.returns(arrayListOf(storageVolumeMockk))

        every { storageVolumeMockk.isRemovable }.returns(false)

        every { storageManager.getStorageVolume(any<File>()) }.returns(mockk {
            every { state }.returns("test")
            every { directory!!.absolutePath }.returns("test")
        })

        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(null)
        Assert.assertEquals(null, VolumeEnvironment.getInternalSdState(mContext))

        mockkStatic(StorageVolumeNative::class)
        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(storageManager)

        Assert.assertEquals("test", VolumeEnvironment.getInternalSdState(mContext))
    }

    @Test
    fun `should verify when getVolumeList without`() {
        VolumeEnvironment.clearValue()

        mockkStatic(StorageVolumeNative::class)
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getInternalSdPath(mContext) }.returns("test")

        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(null)
        Assert.assertNull(VolumeEnvironment.getVolumeList())

        var storageManager = mockk<StorageManager>(relaxed = true)
        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(storageManager)
        every { storageManager.storageVolumes }.returns(arrayListOf(mockk(relaxed = true) {
            every { state }.returns(Environment.MEDIA_CHECKING)
            every { directory!!.absolutePath }.returns("test")
        }))
        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.sIsNotSupportSD }.returns(true)
        Assert.assertNotNull(VolumeEnvironment.getVolumeList())

        every { FeatureCompat.sIsNotSupportSD }.returns(false)
        Assert.assertNotNull(VolumeEnvironment.getVolumeList())

        try {
            every { FeatureCompat.sIsNotSupportSD }.throws(UnSupportedApiVersionException("test-error"))
            Assert.assertNotNull(VolumeEnvironment.getVolumeList())
        } catch (e: Exception) {
            println("test!!!")
        }
    }

    @Ignore
    fun `should verify when getOtgTotalAndAvailableSize without`() {
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(TextUtils::class)
        every { TextUtils.isEmpty(any()) }.returns(false)
        every { VolumeEnvironment.isMountedByPath(mContext, any()) }.returns(true)

        every { VolumeEnvironment.getOTGPath(mContext) }.returns(null)
        val otgTotalZero = VolumeEnvironment.getOtgTotalAndAvailableSize(mContext)
        Assert.assertEquals(0, otgTotalZero.first)

        mockkStatic(Utils::class)
        every { Utils.getStorageAvailableSize(any()) }.returns(10)
        every { Utils.getStorageTotalSize(any()) }.returns(10)
        every { VolumeEnvironment.getOTGPath(mContext) }.returns(arrayListOf("test"))
        val otgTotalTen = VolumeEnvironment.getOtgTotalAndAvailableSize(mContext)
        Assert.assertEquals(10, otgTotalTen.first)
    }

    @Ignore
    fun `should verify when getOTGPath without`() {
        Assert.assertNull(VolumeEnvironment.getOTGPath(null))

        mockkStatic(OplusUsbEnvironmentCompat::class)
        every { OplusUsbEnvironmentCompat.getOtgPath(mContext) }.returns(arrayListOf("test"))
        Assert.assertEquals("test", VolumeEnvironment.getOTGPath(mContext)[0])
    }

    @Ignore
    fun `should verify when getExternalSdPath without`() {
        VolumeEnvironment.clearValue()
        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(null)

        Assert.assertNull(VolumeEnvironment.getExternalSdPath(null))

        mockkStatic(VolumeEnvironment::class)
        mockkStatic(OplusUsbEnvironmentCompat::class)
        every { OplusUsbEnvironmentCompat.getExternalPath(mContext) }.returns("test")

        every { VolumeEnvironment.isOTGVersion(mContext) }.returns(true)
        Assert.assertEquals("test", VolumeEnvironment.getExternalSdPath(mContext))

        every { VolumeEnvironment.isOTGVersion(mContext) }.returns(false)

        VolumeEnvironment.clearValue()
        Assert.assertNull(VolumeEnvironment.getExternalSdPath(mContext))
    }

    @Ignore
    fun `should verify when getExternalOTGState without`() {
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.isMounted(mContext, any()) }.returns(true)
        every { VolumeEnvironment.isMountedByPath(mContext, any()) }.returns(true)

        every { VolumeEnvironment.getOTGPath(mContext) }.returns(null)
        Assert.assertFalse(VolumeEnvironment.getExternalOTGState(mContext, true))

        every { VolumeEnvironment.getOTGPath(mContext) }.returns(arrayListOf("test"))
        mockkStatic(TextUtils::class)
        every { TextUtils.isEmpty(any()) }.returns(false)
        Assert.assertTrue(VolumeEnvironment.getExternalOTGState(mContext, true))

        every { VolumeEnvironment.isMounted(mContext, any()) }.returns(false)
        Assert.assertFalse(VolumeEnvironment.getExternalOTGState(mContext, true))

        Assert.assertTrue(VolumeEnvironment.getExternalOTGState(mContext, false))

        every { VolumeEnvironment.isMountedByPath(mContext, any()) }.returns(false)
        Assert.assertFalse(VolumeEnvironment.getExternalOTGState(mContext, false))
    }

    @Test
    fun `should verify when isMounted without`() {
        Assert.assertFalse(VolumeEnvironment.isMounted(null, null))

        mockkStatic(TextUtils::class)
        every { TextUtils.isEmpty(any()) }.returns(true)
        Assert.assertFalse(VolumeEnvironment.isMounted(mContext, null))

        every { TextUtils.isEmpty(any()) }.returns(false)
        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(null)
        Assert.assertFalse(VolumeEnvironment.isMounted(mContext, "test"))
    }

    @Ignore
    fun `should verify when isOTGPath without`() {
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(TextUtils::class)

        every { VolumeEnvironment.getOTGPath(mContext) }.returns(null)
        Assert.assertFalse(VolumeEnvironment.isOTGPath(mContext, null))

        every { VolumeEnvironment.getOTGPath(mContext) }.returns(arrayListOf())
        Assert.assertFalse(VolumeEnvironment.isOTGPath(mContext, null))

        every { VolumeEnvironment.getOTGPath(mContext) }.returns(arrayListOf("test"))
        every { TextUtils.isEmpty(any()) }.returns(true)
        Assert.assertFalse(VolumeEnvironment.isOTGPath(mContext, null))

        every { TextUtils.isEmpty(any()) }.returns(false)
        Assert.assertTrue(VolumeEnvironment.isOTGPath(mContext, "testPass"))
    }

    @Ignore
    fun `should verify when isSdcardPath without`() {
        mockkStatic(OplusUsbEnvironmentCompat::class)
        mockkStatic(TextUtils::class)
        every { TextUtils.isEmpty(any()) }.returns(false)

        every { OplusUsbEnvironmentCompat.getExternalPath(mContext) }.returns("test")
        Assert.assertTrue(VolumeEnvironment.isSdcardPath(mContext, "test"))
    }

    @Test
    fun `should verify when isOTGVersion without`() {
        Assert.assertFalse(VolumeEnvironment.isOTGVersion(null))

        Assert.assertFalse(VolumeEnvironment.isOTGVersion(mContext))
    }

    @Test
    fun `should verify when getPathFromStorageVolume without`() {
        mockkStatic(SdkUtils::class)
        val volume: StorageVolume = mockk(relaxed = true)
        every { SdkUtils.isAtLeastR() } returns true
        Assert.assertEquals(VolumeEnvironment.getPathFromStorageVolume(volume), "")
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}