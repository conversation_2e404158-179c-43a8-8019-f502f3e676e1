/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : ViewExtractHelper.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.viewextract

import android.app.assist.AssistStructure
import android.app.assist.AssistStructure.ViewNode
import android.content.ComponentName
import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.OplusWindowManager
import androidx.annotation.RequiresApi
import com.coloros.filemanager.appswitch.AppSwitchManager
import com.coloros.filemanager.appswitch.util.ConvertUtil
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper.SingleRequestProcessInfo.Companion.STATUS_FAILED
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper.SingleRequestProcessInfo.Companion.STATUS_INIT
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper.SingleRequestProcessInfo.Companion.STATUS_SUC
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper.ViewExactResultCallBack.Companion.RESULT_CODE_DEFAULT
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper.ViewExactResultCallBack.Companion.RESULT_CODE_FAILED_ERROR
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper.ViewExactResultCallBack.Companion.RESULT_CODE_FAILED_NOTARGET_FOUND
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper.ViewExactResultCallBack.Companion.RESULT_CODE_FAILED_UNKOWN
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper.ViewExactResultCallBack.Companion.RESULT_CODE_SUC
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper.ViewExtracRequest.Companion.REQUEST_TYPE_NOT_VISIBLE_WINDOW
import com.filemanager.common.utils.ThirdAppSearchUtil
import com.oplus.app.OplusWindowInfo
import com.oplus.viewextract.ViewExtractSDK
import org.json.JSONArray
import org.json.JSONObject
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap
import java.util.regex.PatternSyntaxException

class ViewExtractHelper {

    companion object {
        const val TAG = "ViewExtractHelper"

        const val GET_BOUNDS_ON_SCREEN = "getBoundsOnScreen"
        const val IS_VISIBLE_TO_USER = "isVisibleToUser"
        const val EXTRA_CLASS_NAME = "extraClassName"
    }


    val requestInfos: ConcurrentHashMap<String, AllRequestProcessInfo> = ConcurrentHashMap<String, AllRequestProcessInfo>()

    fun tryExtractViewFromWindow(request: ViewExtracRequest) {
        if (!ThirdAppSearchUtil.isViewExtractSupported()) {
            Log.i(TAG, "tryExtractViewFromWindow, viewExtractSdk not support, return")
            return
        }
        val requestId = request.requestId
        val inputPreviewActivity = request.preViewActivity
        val callBack = request.callBack
        val captureTarget = AppSwitchManager.currentThirdAppListenConfigParam?.getAllCheckActivitiesForPreViewActivity(inputPreviewActivity)
        Log.i(TAG, "tryExtractViewFromWindow, requestId $requestId, previewActivity $inputPreviewActivity captureTarget ${listOf(captureTarget)}")
        runCatching {
            val targetWindowInfo = findTargetWindow(captureTarget)
            if (targetWindowInfo != null) {
                exactViewSdkInTargetWindow(request, winfo = targetWindowInfo)
            } else {
                if (captureTarget.isNullOrEmpty()) {
                    processCapterTargetListEmpty(request)
                } else {
                    processNoTargetWindowFound(request, captureTarget)
                }
            }
        }.onFailure {
            val result = ConvertUtil.convertRequestToResult(request)
            callBack.onViewExactCompleted(
                requestId,
                failResults = mutableListOf(result),
                false,
                true,
                RESULT_CODE_FAILED_UNKOWN
            )
            clearRequestInfo(request)
            Log.e(TAG, "tryExtractViewFromWindow RemoteException error, request $requestId", it)
        }
    }

    private fun processNoTargetWindowFound(originalRequest: ViewExtracRequest, captureTargetList: List<String>) {
        initRequestInfo(originalRequest, captureTargetList.size)
        originalRequest.callBack.onViewExactStarted(originalRequest.requestId, captureTargetList.size)
        Log.i(TAG, "processNoTargetWindowFound START, size ${captureTargetList.size}")
        for (targetActivity in captureTargetList) {
            exactViewSdkInTargetListItem(originalRequest, targetActivity)
        }
        Log.i(TAG, "processNoTargetWindowFound END, size ${captureTargetList.size}")
    }

    private fun processCapterTargetListEmpty(originalRequest: ViewExtracRequest) {
        Log.e(TAG, "processNoTargetWindowFound captureTargetList empty or null, return")
        val callBack = originalRequest.callBack
        val requestId = originalRequest.requestId
        val result = ConvertUtil.convertRequestToResult(originalRequest)
        callBack.onViewExactCompleted(
            requestId,
            failResults = mutableListOf(result),
            false,
            true,
            RESULT_CODE_FAILED_NOTARGET_FOUND
        )
    }


    private fun exactViewSdkInTargetWindow(
        originalRequest: ViewExtracRequest,
        winfo: OplusWindowInfo
    ) {
        initRequestInfo(originalRequest, 1)
        originalRequest.callBack.onViewExactStarted(originalRequest.requestId, 1)
        val innerRequest = originalRequest.copy()
        val requestId = innerRequest.requestId
        innerRequest.callBack = originalRequest.callBack
        innerRequest.targetActivity = winfo.componentName.className
        Log.i(TAG, "exactViewSdkInTargetWindow innerRequest $innerRequest")
        putOrUpdateInnerRequest(innerRequest)
        ViewExtractSDK.requestViewExtractTree(
            OnExtractViewTreeCallbackImpl(innerRequest), Bundle().apply {
                putString("requestId", requestId)
            },
            winfo.componentName.toShortString()
        )
    }

    private fun exactViewSdkInTargetListItem(
        originalRequest: ViewExtracRequest,
        targetActivity: String
    ) {
        val innerRequest = originalRequest.copy()
        val requestId = innerRequest.requestId
        val packageName = innerRequest.packageName
        innerRequest.callBack = originalRequest.callBack
        innerRequest.targetActivity = targetActivity
        innerRequest.requestType = REQUEST_TYPE_NOT_VISIBLE_WINDOW
        val componentName = ComponentName(packageName, targetActivity)
        val componentShortString = componentName.toShortString()
        Log.i(
            TAG,
            "exactViewSdkInTargetListItem innerRequest $innerRequest, targetActivity $targetActivity, componentShortString $componentShortString"
        )
        putOrUpdateInnerRequest(innerRequest)
        ViewExtractSDK.requestViewExtractTree(
            OnExtractViewTreeCallbackImpl(innerRequest), Bundle().apply {
                putString("requestId", requestId)
            },
            componentShortString
        )
    }


    private fun findTargetWindow(captureTargetList: List<String>?): OplusWindowInfo? {
        return runCatching {
            val oplusWindowManager = OplusWindowManager()
            val windowInfos: List<OplusWindowInfo> = oplusWindowManager.allVisibleWindowInfo
            if (windowInfos.isEmpty()) {
                Log.i(TAG, "findTargetWindow windowInfo empty, return null")
                return null
            }
            Log.i(TAG, "findTargetWindow, windowInfo size ${windowInfos.size}")
            var result: OplusWindowInfo? = null
            for (f in windowInfos.indices) {
                val winfo = windowInfos[f]
                Log.d(TAG, "findTargetWindow winfo.componentName ${winfo.componentName} winfo.package ${winfo.packageName} " +
                            "bound ${winfo.mBounds}, frame ${winfo.contentFrame}}")
                if (winfo.componentName != null && (captureTargetList?.contains(winfo.componentName.className) == true)) {
                    result = winfo
                    break
                }
            }
            result
        }.onFailure {
            Log.e(TAG, "findTargetWindow error", it)
        }.getOrNull()
    }


    inner class OnExtractViewTreeCallbackImpl(private var innerRequest: ViewExtracRequest) : ViewExtractSDK.OnExtractViewTreeCallback {

        override fun onHandleAssistData(bundle: Bundle?) {
            Log.i(TAG, "onHandleAssistData bundle $bundle, requestId ${bundle?.getString("requestId")}")
            super.onHandleAssistData(bundle)
        }

        override fun onError(errorCode: Int) {
            Log.e(TAG, "onError errorCode $errorCode, request $innerRequest")
            super.onError(errorCode)
            val requestId = innerRequest.requestId
            val callBack = innerRequest.callBack
            val result = ConvertUtil.convertRequestToResult(innerRequest)
            callBack.onSingleViewExactResult(
                requestId,
                result,
                RESULT_CODE_FAILED_ERROR
            )
            putOrUpdateInnerRequest(innerRequest, STATUS_FAILED)
            checkAndInvokeCompleteCallback(callBack, requestId)
        }

        @RequiresApi(Build.VERSION_CODES.TIRAMISU)
        override fun onViewAssistStructureCallBack(assistStructure: AssistStructure?) {
            if (assistStructure == null) {
                Log.i(TAG, "onViewAssistStructureCallBack assistStructure null, return")
                return
            }
            val windowNodeCount = assistStructure.windowNodeCount
            Log.i(TAG, "onViewAssistStructureCallBack start, windowNodeCount $windowNodeCount, request $innerRequest " +
                    "\\n assistStructure $assistStructure")
            val requestId = innerRequest.requestId
            val callBack = innerRequest.callBack
            val targetActivity = innerRequest.targetActivity
            val preViewActivity = innerRequest.preViewActivity
            val packageVersion = innerRequest.packageVersion
            val viewExactConfig = getViewExactConfig(targetActivity, preViewActivity, packageVersion)
            for (i in (0 until windowNodeCount)) {
                val windowNode = assistStructure.getWindowNodeAt(i)
                val rootViewNode = windowNode.rootViewNode
                val jsb = JSONObject()
                val restr = StringBuilder()
                parseNode(rootViewNode, jsb, restr, 0, viewExactConfig)
                innerRequest.viewExactString = restr.toString()
                val result = ConvertUtil.convertRequestToResult(innerRequest)
                callBack.onSingleViewExactResult(requestId, result, RESULT_CODE_SUC)
                Log.i(TAG, "onViewAssistStructureCallBack ing $i, callback $callBack requestId $requestId, resultStr $restr")
            }
            putOrUpdateInnerRequest(innerRequest, STATUS_SUC)
            checkAndInvokeCompleteCallback(callBack, requestId)
            Log.i(TAG, "onViewAssistStructureCallBack end, requestId $requestId  assistStructure $assistStructure")
        }

        private fun checkAndInvokeCompleteCallback(
            callBack: ViewExactResultCallBack,
            requestId: String
        ) {
            val hasReachMax = checkReachMaxAndStatusDone(innerRequest)
            if (hasReachMax) {
                val hasFailed = checkHasFailed(innerRequest)
                val allSuc = checkAllSuc(innerRequest)
                callBack.onViewExactCompleted(
                    requestId,
                    getFailedResults(innerRequest),
                    allSuc,
                    hasFailed,
                    RESULT_CODE_DEFAULT
                )
                clearRequestInfo(innerRequest)
            }
        }
    }


    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun parseNode(
        rootViewNode: ViewNode,
        jsb: JSONObject,
        sb: StringBuilder,
        recLevel: Int,
        viewExactConfig: ViewExactConfig?
    ) {
        val rect = getBoundsOnScreen(rootViewNode)
        Log.d(
            TAG,
            "parseNode rect: $rect rootViewNode.class ${rootViewNode.className}, viewExactConfig $viewExactConfig" +
                    "rootViewNode text ${rootViewNode.text}, recLevel $recLevel, sb $sb"
        )
        jsb.put("rect", rect)
        if (rect.width() == 0 || rect.height() == 0) {
            Log.d(TAG, "parseNode end because rect height or width 0: $rect")
            return
        }
        if (rootViewNode.width == 0 || rootViewNode.height == 0) {
            Log.d(TAG, "parseNode end because rootViewNode width 0: ${rootViewNode.width} height 0: ${rootViewNode.height}")
            return
        }
        val ignoreVisibleToUser = viewExactConfig?.ignoreVisibleToUser ?: false
        if (ignoreVisibleToUser) {
            parseNodeInner(rootViewNode, viewExactConfig, jsb, sb, recLevel)
        } else {
            if (!isVisibleToUser(rootViewNode)) {
                Log.d(TAG, "parseNode end because is not visible to user: $rootViewNode")
                return
            }
            parseNodeInner(rootViewNode, viewExactConfig, jsb, sb, recLevel)
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun parseNodeInner(
        rootViewNode: ViewNode,
        viewExactConfig: ViewExactConfig?,
        jsb: JSONObject,
        sb: StringBuilder,
        recLevel: Int
    ) {
        val childCount = rootViewNode.childCount
        val ignoreRegexString = viewExactConfig?.ignoreRegexString
        val jar = JSONArray()
        jsb.put("class", rootViewNode.className)
        jsb.put("childCount", childCount)
        var value = rootViewNode.text
        var add = false
        if (!value.isNullOrEmpty()) {
            add = true
            val overrideString = procssViewExactIngoreString(value, ignoreRegexString)
            sb.append(overrideString)
        }
        jsb.put("txt", rootViewNode.text)
        value = rootViewNode.hint
        if (!value.isNullOrEmpty()) {
            val overrideString = procssViewExactIngoreString(value, ignoreRegexString)
            sb.append("|$overrideString")
            add = true
        }
        if (add) {
            sb.append("\n")
        }
        jsb.put("hint", rootViewNode.hint)
        for (i in 0 until childCount) {
            val obj = JSONObject()
            parseNode(rootViewNode.getChildAt(i), obj, sb, recLevel + 1, viewExactConfig)
            jar.put(obj)
        }
        if (childCount > 0 || rootViewNode.className!!.lowercase(Locale.getDefault())
                .contains("textview")
        ) {
            jsb.put("childs", jar)
        }
    }

    private fun getViewExactConfig(
        viewExactActivity: String,
        preViewActivity: String,
        packageVersion: Int
    ): ViewExactConfig? {
        val thirdAppListenConfigParam = AppSwitchManager.currentThirdAppListenConfigParam
        if (viewExactActivity.isEmpty() || thirdAppListenConfigParam == null) {
            Log.w(TAG, "getViewExactConfig viewExactActivity $viewExactActivity empty or thirdAppListenConfigParam empty, return false")
            return null
        }
        val checkActivityItem = thirdAppListenConfigParam.getCheckActivityItem(viewExactActivity, preViewActivity, packageVersion)
        if (checkActivityItem == null) {
            Log.w(TAG, "getViewExactConfig find no checkActivity for $viewExactActivity return false")
            return null
        }
        val ignoreRegexString = checkActivityItem.viewExactIgnoreSpecialChar
        val ignoreVisibleToUser = checkActivityItem.viewExactIgnoreVisibleToUser ?: false
        val result = ViewExactConfig(ignoreRegexString, ignoreVisibleToUser)
        Log.i(TAG, "getViewExactConfig activity $viewExactActivity, result $result")
        return result
    }

    private fun procssViewExactIngoreString(
        inputString: CharSequence,
        ignoreRegexString: String?
    ): String {
        var overrideString = inputString
        if (ignoreRegexString == null) {
            return overrideString.toString()
        }
        val regex: Regex?
        try {
            regex = Regex(ignoreRegexString)
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, "processEmptyFileNameRegex regex constuctor error, IllegalArgumentException $ignoreRegexString", e)
            return overrideString.toString()
        } catch (e: PatternSyntaxException) {
            Log.e(TAG, "processEmptyFileNameRegex regex constuctor error, IllegalArgumentException $ignoreRegexString", e)
            return overrideString.toString()
        }
        overrideString =  regex.replace(inputString, "")
        Log.i(TAG, "procssViewExactIngoreString input $inputString, ignoreRegexString $ignoreRegexString, result $overrideString")
        return overrideString
    }


    private fun isVisibleToUser(viewNode: ViewNode?): Boolean {
        return if (viewNode == null || viewNode.extras == null) {
            false
        } else viewNode.extras?.getBoolean(IS_VISIBLE_TO_USER, false) ?: false
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun getBoundsOnScreen(viewNode: ViewNode?): Rect {
        return if (viewNode == null || viewNode.extras == null) {
            Rect()
        } else viewNode.extras?.getParcelable<Rect>(GET_BOUNDS_ON_SCREEN, Rect::class.java) ?: Rect()
    }


    data class AllRequestProcessInfo(
        val originalRequest: ViewExtracRequest,
        val innerRequestCnt: Int,
        val requestInfos: MutableList<SingleRequestProcessInfo>
    ) {
        fun putOrUpdateRequestInfo(innerRequest: ViewExtracRequest, status: Int) {
            Log.i(TAG, "putOrUpdateRequestInfo innerRequest $innerRequest, status $status, currentCnt ${requestInfos.size}")
            var singleInfo = requestInfos.find { innerRequest.targetActivity == it.innerRequest.targetActivity }
            if (singleInfo == null) {
                singleInfo = SingleRequestProcessInfo(innerRequest, status)
                requestInfos.add(singleInfo)
            } else {
                singleInfo.requestStatus = status
                singleInfo.innerRequest = innerRequest
            }
            Log.i(TAG, "putOrUpdateRequestInfo currentCnt ${requestInfos.size}")
        }

        fun checkCntReachLimit(): Boolean {
            Log.i(TAG, "checkCntReachLimit innerRequestCnt $innerRequestCnt, requestInfoSize ${requestInfos.size}")
            return innerRequestCnt == requestInfos.size
        }

        fun checkAllStatusDone(): Boolean {
            var allStatusDone = true
            for (requestInfo in requestInfos) {
                val currentStatusDone =
                    requestInfo.requestStatus == STATUS_SUC || requestInfo.requestStatus == STATUS_FAILED
                allStatusDone = allStatusDone && currentStatusDone
            }
            Log.i(TAG, "checkAllStatusDone allStatusDone $allStatusDone, requestInfoSize ${requestInfos.size}")
            return allStatusDone
        }


        fun getFailedResults(): List<ViewExtractResult> {
            val result: MutableList<ViewExtractResult> = mutableListOf()
            var allNotDefaultFailed = true
            requestInfos.forEach {
                //当request为正常的Request的时候，将failed的request放在result中
                if (it.innerRequest.isDefaultType() && it.requestStatus == STATUS_FAILED) {
                    result.add(ConvertUtil.convertRequestToResult(it.innerRequest))
                } else {
                    //判断如果时兜底逻辑时，兜底逻辑中所有的非default版本的request都状态都为false
                    allNotDefaultFailed = allNotDefaultFailed && (it.requestStatus == STATUS_FAILED)
                }
            }
            if (allNotDefaultFailed) {
                //所有兜底的request失败时，返回一个总体的失败bean
                result.add(ConvertUtil.convertRequestToResult(originalRequest))
            }
            Log.i(TAG, "getFailedResults size ${result.size}")
            return result
        }


        fun isAllSuc(): Boolean {
            var isAllSuc = true
            for (info in requestInfos) {
                isAllSuc = isAllSuc && info.requestStatus == STATUS_SUC
            }
            Log.i(TAG, "isAllSuc $isAllSuc")
            return isAllSuc
        }

        fun hasRequestFailed(): Boolean {
            var defaultHasFailed = false
            var noDefaultHasFailed = true
            for (info in requestInfos) {
                if (info.innerRequest.isDefaultType()) {
                    //默认type的request中只有一个状态是failed，就返回为true
                    defaultHasFailed = info.requestStatus == STATUS_FAILED
                    if (defaultHasFailed) {
                        break
                    }
                } else {
                    //非默认type的request中所有的状态都是failed，noDefaultHasFailed才返回true
                    noDefaultHasFailed = noDefaultHasFailed && (info.requestStatus == STATUS_FAILED)
                }
            }
            val result = defaultHasFailed || noDefaultHasFailed
            Log.i(TAG, "hasRequestFailed defaultHasFailed $defaultHasFailed, noDefaultHasFailed $noDefaultHasFailed")
            return result
        }
    }

    data class SingleRequestProcessInfo(var innerRequest: ViewExtracRequest, var requestStatus: Int = STATUS_INIT) {
        companion object {
            const val STATUS_INIT = 0
            const val STATUS_SUC = 1
            const val STATUS_FAILED = 2
        }
    }

    private fun initRequestInfo(originalRequest: ViewExtracRequest, cnt: Int) {
        val requestId = originalRequest.requestId
        val requestInfo = AllRequestProcessInfo(originalRequest, cnt, mutableListOf())
        requestInfos.put(requestId, requestInfo)
    }

    private fun putOrUpdateInnerRequest(innerRequest: ViewExtracRequest, status: Int = STATUS_INIT) {
        Log.i(TAG, "putOrUpdateInnerRequest input $innerRequest, status $status")
        val requestId = innerRequest.requestId
        requestInfos[requestId]?.putOrUpdateRequestInfo(innerRequest, status)
    }

    private fun getRequestInfo(innerRequest: ViewExtracRequest): AllRequestProcessInfo? {
        val requestId = innerRequest.requestId
        val requestInfo = requestInfos[requestId]
        return requestInfo
    }


    private fun checkReachMaxAndStatusDone(innerRequest: ViewExtracRequest): Boolean {
        val requestInfo = getRequestInfo(innerRequest)
        if (requestInfo == null) {
            Log.i(TAG, "checkReachMax requestInfo null, return false")
            return false
        } else {
            val reachMaxCnt = requestInfo.checkCntReachLimit()
            val allStatusDone = requestInfo.checkAllStatusDone()
            Log.i(TAG, "checkReachMax reachMax $reachMaxCnt, allStatusDone $allStatusDone")
            return reachMaxCnt && allStatusDone
        }
    }

    private fun checkAllSuc(innerRequest: ViewExtracRequest): Boolean {
        val requestInfo = getRequestInfo(innerRequest)
        if (requestInfo == null) {
            Log.i(TAG, "checkAllSuc requestInfo null, return false")
            return false
        } else {
            val isAllSuc = requestInfo.isAllSuc()
            Log.i(TAG, "checkAllSuc isAllSuc $isAllSuc")
            return isAllSuc
        }
    }


    private fun checkHasFailed(innerRequest: ViewExtracRequest): Boolean {
        val requestInfo = getRequestInfo(innerRequest)
        if (requestInfo == null) {
            Log.i(TAG, "checkHasFailed requestInfo null, return false")
            return false
        } else {
            val hasFailed = requestInfo.hasRequestFailed()
            Log.i(TAG, "checkHasFailed hasFailed $hasFailed")
            return hasFailed
        }
    }

    private fun getFailedResults(innerRequest: ViewExtracRequest): List<ViewExtractResult> {
        val requestInfo = getRequestInfo(innerRequest)
        if (requestInfo == null) {
            Log.i(TAG, "checkHasFailed requestInfo null, return false")
            return emptyList()
        } else {
            val failedResult = requestInfo.getFailedResults()
            Log.i(TAG, "getFailedResults ${failedResult.size}")
            return failedResult
        }
    }


    private fun clearRequestInfo(innerRequest: ViewExtracRequest) {
        val requestId = innerRequest.requestId
        requestInfos.remove(requestId)
    }

    interface ViewExactResultCallBack {

        companion object {
            const val TAG = "ViewExactResultCallBack"

            const val RESULT_CODE_SUC = 0
            const val RESULT_CODE_FAILD_NO_WINDOWINFO = 1
            const val RESULT_CODE_FAILED_UNKOWN = 2
            const val RESULT_CODE_FAILED_NOTARGET_FOUND = 3
            const val RESULT_CODE_FAILED_ERROR = 4
            const val RESULT_CODE_DEFAULT = 0
        }

        fun onViewExactStarted(requestId: String, innerRequestCnt: Int)

        fun onSingleViewExactResult(requestId: String, result: ViewExtractResult, resultCode: Int)

        fun onViewExactCompleted(
            requestId: String,
            failResults: List<ViewExtractResult>,
            isAllSuc: Boolean,
            hasFailedRequest: Boolean,
            resultCode: Int
        )
    }


    data class ViewExtracRequest(
        val requestId: String,
        val packageName: String,
        val packageVersion: Int,
        val preViewActivity: String,
        var callBack: ViewExactResultCallBack
    ) {

        companion object {
            const val TAG = "ViewExtracRequest"
            const val REQUEST_TYPE_DEFALT = 0
            const val REQUEST_TYPE_NOT_VISIBLE_WINDOW = 1
        }

        var targetActivity: String = ""
        var requestType = REQUEST_TYPE_DEFALT
        var viewExactString: String = ""
        override fun toString(): String {
            return "ViewExtracRequest(requestId='$requestId', packagename='$packageName', preViewActivity='$preViewActivity', " +
                    "packageVersion='$packageVersion', callBack=$callBack, targetActivity='$targetActivity', " +
                    "requestType=$requestType, viewExactString=$viewExactString)"
        }

        fun isDefaultType(): Boolean {
            return requestType == REQUEST_TYPE_DEFALT
        }
    }

    data class ViewExtractResult(
        var viewExactString: String,
        var viewExactActivity: String,
        val preViewActivity: String,
        val packageName: String,
        val packageVersion: Int
    ) {
        fun isDataValid(): Boolean {
            return viewExactString.isNotEmpty() && viewExactActivity.isNotEmpty()
        }

        var isDefault: Boolean = true
    }


    data class ViewExactConfig(val ignoreRegexString: String?, val ignoreVisibleToUser: Boolean)
}