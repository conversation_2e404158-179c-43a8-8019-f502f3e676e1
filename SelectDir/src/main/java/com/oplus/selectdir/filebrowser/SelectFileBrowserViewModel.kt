/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.filebrowser
 * * Version     : 1.0
 * * Date        : 2020/5/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir.filebrowser

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.MyApplication
import com.filemanager.common.base.*
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.interfaces.LoadingLoaderListener
import com.filemanager.common.path.FilePathHelper
import com.filemanager.common.path.FileBrowPathHelper
import com.filemanager.common.utils.*
import com.filemanager.common.utils.StatisticsUtils.EVENT_CLICK_ANDROID_DATA
import com.oplus.selectdir.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.util.*
import kotlin.collections.ArrayList

class SelectFileBrowserViewModel : SelectionViewModel<BaseFileBean, SelectFileBrowserViewModel.FileBrowserUiModel>() {
    companion object {
        private const val TAG = "SelectFileBrowserViewModel"
        private const val FILE_BROWSER_LOADER_ID = 1
        private const val FILE_BROWSER_SCAN_MODE_SP_KEY = "file_browser_scan_mode"

        @JvmStatic
        val sInternalAndroidDataPath by lazy {
            "${VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)}${File.separator}" +
                    "android${File.separator}data"
        }

        @JvmStatic
        val sInternalAndroidObbPath by lazy {
            "${VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)}${File.separator}" +
                    "android${File.separator}obb"
        }

        private fun handleLoadComplete(viewModel: SelectFileBrowserViewModel, loadData: List<out BaseFileBean>, keyMap: HashMap<Int, BaseFileBean>) {
            loadData.let {
                viewModel.mModeState.mInitState = true
                viewModel.launch {
                    val selectedList = ArrayList<Int>()
                    if ((viewModel.mUiState.value?.mSelectedList?.size ?: 0) > 0) {
                        withContext(Dispatchers.IO) {
                            for (selectedFile in viewModel.mUiState.value!!.mSelectedList) {
                                if (keyMap.containsKey(selectedFile)) {
                                    selectedList.add(selectedFile)
                                }
                            }
                        }
                    }
                    if (viewModel.mPushPathInfo != null) {
                        viewModel.mPositionModel.value?.mCurrentPath?.apply {
                            viewModel.mPathHelp?.push(viewModel.mPushPathInfo)
                        }
                        viewModel.mPushPathInfo = null
                    }
                    if (it.isEmpty() && (viewModel.mModeState.mListModel.value == KtConstants.LIST_SELECTED_MODE)) {
                        Log.d(TAG, "onLoadComplete mResultList is empty change to normal mode")
                        viewModel.mModeState.mListModel.value = KtConstants.LIST_NORMAL_MODE
                    }
                    viewModel.mUiState.value = FileBrowserUiModel(loadData, viewModel.mModeState, selectedList, viewModel.mPositionModel, keyMap)
                    Log.d(TAG, "handleLoadComplete mNeedScroll=${viewModel.mNeedScroll},mPositionModel=${viewModel.mPositionModel.value}")
                    if (viewModel.mNeedScroll) {
                        viewModel.mPositionModel.postValue(viewModel.mPositionModel.value)
                    }
                    setCreateFolderPosition(viewModel, loadData)
                }
            }
        }

        private fun setCreateFolderPosition(viewModel: SelectFileBrowserViewModel, loadData: List<BaseFileBean>) {
            if (viewModel.createFolderPath.isEmpty()) {
                return
            }
            val position = loadData
                .map { it.mData }
                .indexOf(viewModel.createFolderPath)
            if (position != -1) {
                viewModel.createFolderPosition.postValue(position)
                viewModel.createFolderPath = ""
            }
        }
    }

    val mModeState = BaseStateModel(MutableLiveData<Int>(KtConstants.LIST_NORMAL_MODE))
    var mPathHelp: FileBrowPathHelper? = null
    var mNeedScroll = false

    //In order to distinguish between entering and exiting folders because of the folder animation
    var mIsFolderIn = true
    val mPositionModel = MutableLiveData<PositionModel>()
    var mPushPathInfo: FilePathHelper.PathInfo? = null

    private val _browseModeState by lazy {
        val lastScanMode = ConfigSharedPreferenceUtils.getInt(FILE_BROWSER_SCAN_MODE_SP_KEY, 0)
        MutableLiveData(if (lastScanMode == 0) {
            KtConstants.SCAN_MODE_LIST
        } else {
            lastScanMode
        })
    }
    val mBrowseModeState: MutableLiveData<Int> = _browseModeState

    val mFileBrowserCallBack = FileBrowserCallBack(this)

    var isFromDetail = false
    var mCurrentPath = ""

    var createFolderPath = ""
    var createFolderPosition = MutableLiveData(-1)

    fun initLoader(mLoaderController: LoaderController?, path: String) {
        if (mFileBrowserCallBack.getLoader() == null) {
            mPositionModel.value = PositionModel(path, 0, 0)
            mPathHelp?.pushTo(path)
            mLoaderController?.initLoader(FILE_BROWSER_LOADER_ID, mFileBrowserCallBack)
        } else {
            mFileBrowserCallBack.loadData()
        }
    }

    fun initPathHelper(currentPath: String) {
        if (mPathHelp == null) {
            mPathHelp = FileBrowPathHelper(currentPath)
        } else if (currentPath.contains(mPathHelp?.getRootPath() ?: "*", true).not()) {
            mPathHelp!!.updateRootPath(currentPath)
        }
    }

    class FileBrowserCallBack(
        viewModel: SelectFileBrowserViewModel
    ) : LoadingLoaderListener<SelectFileBrowserViewModel, SelectFileBrowserLoader, PathLoadResult<Int, BaseFileBean>>(
        viewModel, viewModel.mDataLoadState
    ) {

        private var mLoadNewPath = true

        internal fun loadData(path: String? = null, loadNewPath: Boolean = false) {
            mLoadNewPath = loadNewPath
            getLoader()?.apply {
                if (path.isNullOrEmpty().not()) {
                    setPath(path!!)
                }
                forceLoad()
            }
        }

        fun isLoadNewPath() = mLoadNewPath

        override fun onCreateLoader(viewModel: SelectFileBrowserViewModel?): SelectFileBrowserLoader? {
            return if (viewModel != null) {
                SelectFileBrowserLoader(MyApplication.sAppContext, viewModel.mPositionModel.value?.mCurrentPath
                        ?: "")
            } else null
        }

        override fun onLoadComplete(viewModel: SelectFileBrowserViewModel?, result: PathLoadResult<Int, BaseFileBean>?) {
            Log.d(TAG, "onLoadComplete in browser: size=${result?.mResultList?.size}")
            result?.let {
                if (viewModel != null) {
                    handleLoadComplete(viewModel, result.mResultList, result.mResultMap)
                } else {
                    Log.d(TAG, "onLoadComplete viewModel is null")
                }
            }
        }
    }

    fun sortReload() {
        mFileBrowserCallBack.loadData()
    }

    fun resetState() {
        changeListMode(KtConstants.LIST_NORMAL_MODE)
    }

    /**
     * isNeedSearch is used for force loading data when search_mode turn to normal_mode ,
     * isNeedSearch is false when after onItemClick()
     */
    fun clickToolbarSelectAll() {
        if (getRealFileSize() == mUiState.value?.mSelectedList?.size) {
            mUiState.value?.mSelectedList?.clear()
            mUiState.value = mUiState.value
        } else {
            mUiState.value?.mSelectedList?.clear()
            mUiState.value?.mFileList?.let {
                var key: Int?
                for (baseFileBean in it) {
                    if (baseFileBean.mFileWrapperLabel == null) {
                        key = baseFileBean.mData?.lowercase(Locale.getDefault())?.hashCode() ?: continue
                        mUiState.value?.mSelectedList?.add(key)
                    }
                }
            }
            mUiState.value = mUiState.value
        }
    }

    fun clickPathBar(index: Int) {
        if (mModeState.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
            changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
        val pathLeft = mPathHelp?.getPathLeft() ?: index + 1
        val pathIndex = pathLeft - index - 1
        val pathInfo = mPathHelp?.setTopPath(pathIndex)
        Log.d(TAG, "clickPathBar pathInfo=${pathInfo}")
        pathInfo?.let {
            mIsFolderIn = false
            mPositionModel.value?.mCurrentPath = it.path
            mNeedScroll = true
            resetPushPathInfo()
            mFileBrowserCallBack.loadData(it.path, true)
        }

    }

    fun onDirClick(activity: BaseVMActivity?, baseFile: BaseFileBean, firstVisibleItemPosition: Int, offset: Int) {
        launch {
            val isExits = baseFile.checkExist()
            if (!isExits) {
                CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
                return@launch
            } else if ((activity != null) && baseFile.mIsDirectory) {
                baseFile.mData?.let {
                    mPositionModel.value?.mCurrentPath = it
                    mIsFolderIn = true
                    mNeedScroll = true
                    resetPushPathInfo()
                    mPushPathInfo = FilePathHelper.PathInfo(it, firstVisibleItemPosition, offset)
                    if (sInternalAndroidDataPath.equals(it, true)) {
                        StatisticsUtils.onCommon(MyApplication.sAppContext, EVENT_CLICK_ANDROID_DATA)
                    }
                    mFileBrowserCallBack.loadData(it, true)
                }
            }
        }
    }

    fun setCurrentFromOtherSide(path: String) {
        Log.d(TAG, "setCurrentFromOtherSide path $path")
        if (mModeState.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
            changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
        mPathHelp?.pushTo(path)
        mPositionModel.value?.mCurrentPath = path
        mIsFolderIn = true
        mNeedScroll = true
        resetPushPathInfo()
        mFileBrowserCallBack.loadData(path, true)
    }

    fun pressBack(): Boolean {
        mModeState.let {
            if (it.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
                changeListMode(KtConstants.LIST_NORMAL_MODE)
                return true
            } else {
                if (isFromDetail && mCurrentPath.isNotEmpty()) {
                    val path = mPositionModel.value?.mCurrentPath
                    if (!path.isNullOrEmpty() && mCurrentPath.contains(path)) {
                        return false
                    }
                }

                if (mPathHelp?.pop() != null) {
                    val info = mPathHelp?.getTopPathInfo()
                    info?.path?.let { path ->
                        mIsFolderIn = false
                        mPositionModel.value?.mCurrentPath = path
                        mPositionModel.value?.mPosition = info.position
                        mPositionModel.value?.mOffset = info.y
                        mNeedScroll = true
                        resetPushPathInfo()
                        mFileBrowserCallBack.loadData(path, true)
                        return true
                    }
                }
                return false
            }
        }
    }

    private fun resetPushPathInfo() {
        mPushPathInfo = null
    }

    override fun loadData() {
        mFileBrowserCallBack.loadData()
    }

    fun clickScanModeItem(context: Context? = null) {
        if (_browseModeState.value == KtConstants.SCAN_MODE_LIST) {
            _browseModeState.value = KtConstants.SCAN_MODE_GRID
            StatisticsUtils.onCommon(context, StatisticsUtils.SCAN_MODE_SDCARD_SWITCH, hashMapOf(StatisticsUtils.SCAN_MODE_SDCARD_SWITCH to "0"))
        } else {
            _browseModeState.value = KtConstants.SCAN_MODE_LIST
            StatisticsUtils.onCommon(context, StatisticsUtils.SCAN_MODE_SDCARD_SWITCH, hashMapOf(StatisticsUtils.SCAN_MODE_SDCARD_SWITCH to "1"))
        }
        _browseModeState.value?.apply {
            ConfigSharedPreferenceUtils.putInt(FILE_BROWSER_SCAN_MODE_SP_KEY, this)
        }
    }

    class FileBrowserUiModel(
        fileList: List<BaseFileBean>,
        stateModel: BaseStateModel,
        selectedList: ArrayList<Int>,
        positionModel: MutableLiveData<PositionModel>?,
        keyMap: HashMap<Int, BaseFileBean>
    ) : BaseUiModel<BaseFileBean>(fileList, stateModel, selectedList, keyMap) {
        var mPositionModel: MutableLiveData<PositionModel>? = positionModel
    }

    data class PositionModel(
            var mCurrentPath: String,
            var mPosition: Int,
            var mOffset: Int
    )

    override fun getRealFileSize(): Int {
        var size = 0
        mUiState.value?.mFileList?.apply {
            for (baseFileBean in this) {
                if (baseFileBean.mFileWrapperLabel == null) {
                    size++
                }
            }
        }
        Log.d(TAG, "getRealFileSize $size")
        return size
    }

    override fun getRecyclerViewScanMode(): com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE {
        return if (_browseModeState.value == KtConstants.SCAN_MODE_LIST) {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.LIST
        } else {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.GRID
        }

    }

    override fun onCreateFolderPath(path: String) {
        createFolderPath = path
    }
}