package com.filemanager.superapp.ui.superapp

import android.content.Context
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.StatisticsUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * SuperAppParentFragmentViewModel的单元测试类
 * 用于测试SuperAppParentFragmentViewModel的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)  // 使用Robolectric测试框架运行测试
@Config(sdk = [29])  // 配置测试环境为Android API 29
class SuperAppParentFragmentViewModelTest {

    /**
     * 用于确保LiveData的更新立即执行而不是异步执行的规则
     */
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    // 被测ViewModel实例
    private lateinit var viewModel: SuperAppParentFragmentViewModel
    // 模拟的LoaderController
    private val mockLoaderController = mockk<LoaderController>(relaxed = true)
    // 模拟的ConfigSharedPreferenceUtils
    private val mockConfigSharedPreferenceUtils = mockk<ConfigSharedPreferenceUtils.Companion>(relaxed = true)

    /**
     * 在每个测试方法执行前的初始化方法
     */
    @Before
    fun setUp() {
        // 创建ViewModel实例并设置模拟对象
        viewModel = SuperAppParentFragmentViewModel().apply {
            mLoaderController = mockLoaderController
            mSuperAppPackage = "test.package"  // 设置测试用的包名
        }
        // 配置模拟对象的默认行为
        every { mockConfigSharedPreferenceUtils.getInt(any(), any()) } returns 0
        every { mockConfigSharedPreferenceUtils.putInt(any(), any()) } returns Unit
    }

    /**
     * 测试getScanMode方法
     * 当SharedPreferences中存储的lastScanMode为0时，应返回SCAN_MODE_GRID
     */
    @Test
    fun `getScanMode should return SCAN_MODE_GRID when lastScanMode is 0`() {
        // 配置模拟对象返回0
        every { mockConfigSharedPreferenceUtils.getInt(any(), any()) } returns 0

        // 调用被测方法
        val result = viewModel.getScanMode()

        // 验证返回结果是否为SCAN_MODE_GRID
        assertEquals(KtConstants.SCAN_MODE_GRID, result)
    }

    /**
     * 测试getScanMode方法
     * 当SharedPreferences中存储的lastScanMode不为0时，应返回该值
     */
    @Test
    fun `getScanMode should return lastScanMode when it is not 0`() {
        val expectedMode = 2
        // 配置模拟对象返回非0值
        every { mockConfigSharedPreferenceUtils.getInt(any(), any()) } returns expectedMode

        // 调用被测方法
        val result = viewModel.getScanMode()

        // 验证返回结果是否为预期值
        assertEquals(expectedMode, result)
    }

    /**
     * 测试refreshBrowseModeState方法
     * 应能正确更新浏览模式状态
     */
    @Test
    fun `refreshBrowseModeState should update browse mode state`() {
        val expectedMode = 2
        // 配置模拟对象返回预期模式值
        every { mockConfigSharedPreferenceUtils.getInt(any(), any()) } returns expectedMode

        // 调用被测方法
        viewModel.refreshBrowseModeState()

        // 验证LiveData的值是否更新为预期值
        assertEquals(expectedMode, viewModel.mBrowseModeState.value)
    }

    /**
     * 测试onCleared方法
     * 应能正确调用LoaderController的onDestroy方法
     */
    @Test
    fun `onCleared should call loaderController onDestroy`() {
        // 调用被测方法
        viewModel.onCleared()

        // 验证LoaderController的onDestroy方法是否被调用
        verify { mockLoaderController.onDestroy() }
    }
}