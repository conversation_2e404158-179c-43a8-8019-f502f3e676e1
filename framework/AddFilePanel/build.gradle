plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace 'com.oplus.filemanager.addfilepanel'
}

dependencies {
    implementation project(':Common')
    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.androidx.lifecycle.viewmodel.ktx
    implementation libs.androidx.recyclerview
    implementation libs.oplus.sau.coui
    implementation libs.androidx.constraintlayout
    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.recyclerview
    implementation libs.oplus.appcompat.button
    implementation libs.oplus.appcompat.poplist
    implementation libs.oplus.appcompat.panel
    implementation libs.oplus.appcompat.scrollbar
    implementation libs.oplus.appcompat.tablayout
    implementation libs.oplus.appcompat.toolbar
    implementation libs.koin.android

    if (prop_use_prebuilt_drap_drop_lib.toBoolean()) {
        implementation libs.oplus.filemanager.dragDrop
    } else {
        implementation project(':exportedLibs:DragDropSelection')
    }
}