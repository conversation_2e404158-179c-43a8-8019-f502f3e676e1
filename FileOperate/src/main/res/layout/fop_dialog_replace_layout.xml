<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/replace_file_cb"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/fop_dialog_recent_delete_tv_top"
            android:layout_marginBottom="@dimen/fop_replace_button_padding"
            android:text="@string/do_for_similar_files"
            android:textAlignment="center"
            android:textColor="@color/fop_black_55_percent"
            android:textSize="@dimen/fop_font_size_12" />

        <Button
            android:id="@+id/button1"
            style="@style/fop_ReplaceDialogButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:maxLines="2"
            android:paddingTop="@dimen/fop_replace_button_padding_top"
            android:paddingBottom="@dimen/fop_replace_button_padding"
            android:textColor="@color/fop_black"
            android:visibility="gone" />

        <Button
            android:id="@+id/button2"
            style="@style/fop_ReplaceDialogButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:maxLines="2"
            android:paddingTop="@dimen/fop_replace_button_padding"
            android:paddingBottom="@dimen/fop_replace_button_padding"
            android:textColor="@color/fop_black"
            android:visibility="gone" />

        <Button
            android:id="@+id/button3"
            style="@style/fop_ReplaceDialogButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:maxLines="2"
            android:paddingTop="@dimen/fop_replace_button_padding"
            android:paddingBottom="@dimen/fop_replace_button_padding_bottom"
            android:textColor="@color/fop_black"
            android:visibility="gone" />

    </LinearLayout>
</ScrollView>
