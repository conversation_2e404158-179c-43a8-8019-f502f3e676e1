/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CompressFileScanner
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/23 14:48
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/23       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.scanner

import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.base.FileLoader
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.fileservice.bean.WebFileBean
import com.oplus.fileservice.bean.WebScannerResult
import com.oplus.fileservice.filelist.FileSelectPathService
import com.oplus.fileservice.filelist.loader.WebCategoryCompressLoader
import com.oplus.fileservice.filelist.utils.getDateAndTimeByDefaultFormat
import com.oplus.fileservice.utils.WebFileConstant
import com.oplus.fileservice.utils.WebMimeTypeHelper
import org.apache.commons.io.FilenameUtils

class CompressFileScanner(pageNo: Int, pageSize: Int, sortOrderType: Int) : BaseUriScanner() {
    companion object {
        const val TAG = "CompressFileScanner"
        const val TYPE_ALL = "all"
    }

    @VisibleForTesting
    var mPageNo = pageNo

    @VisibleForTesting
    var mPageSize = pageSize
    private var mTotal = 1
    private var mSortOrderType = sortOrderType
    private val mLoaderController = LoaderController()

    override fun scannerFiles(): WebScannerResult? {
        mLoaderController.initLoader(TAG.hashCode(), ServiceCompressCallBack(TYPE_ALL))
        return null
    }

    inner class ServiceCompressCallBack(compressType: String) :
        OnLoaderListener<MutableList<MediaFileWrapper>> {
        private var mCompressType = compressType
        override fun onCreateLoader(): FileLoader<MutableList<MediaFileWrapper>> {
            val webCategoryCompressLoader =
                WebCategoryCompressLoader(MyApplication.sAppContext, mCompressType)
            webCategoryCompressLoader.mSortOrderType = mSortOrderType
            return webCategoryCompressLoader
        }

        override fun onLoadComplete(result: MutableList<MediaFileWrapper>?) {
            val webFileBeans = pageData(result)
            mScannerCallback?.scannerSuc(WebScannerResult(mPageNo, mTotal, webFileBeans))
        }
    }

    @VisibleForTesting
    fun pageData(result: List<MediaFileWrapper>?): ArrayList<WebFileBean> {
        mTotal = result?.size ?: 0
        if (mPageSize < 1) {
            mPageSize = WebFileConstant.DEFAULT_PAGE_SIZE
            Log.d(TAG, "Service modify mPageSize $mPageSize")
        }
        var pageTotal = mTotal / mPageSize
        if (mTotal % mPageSize > 0) {
            pageTotal++
        }

        if (mPageNo > pageTotal) {
            Log.d(FileSelectPathService.TAG, "mPageNo > pageTotal is true, mPageNo = $mPageNo")
            mPageNo = pageTotal
        }
        if (mPageNo < 1) {
            mPageNo = 1
        }
        val start = mPageSize * (mPageNo - 1)
        val end = if (mPageSize * mPageNo > mTotal) {
            mTotal
        } else {
            mPageSize * mPageNo
        }

        val resultFileList = result?.subList(start, end)
        val resultWebFileBean = ArrayList<WebFileBean>()
        if (resultFileList != null) {
            for (mediaFileWrapper in resultFileList) {
                mediaFileWrapper.apply {
                    val webFileBean = WebFileBean()
                    webFileBean.mFileName = FilenameUtils.getBaseName(mDisplayName)
                    webFileBean.mFilePath = mData
                    webFileBean.mFileId = mId ?: 0
                    webFileBean.mFileSize = mSize
                    webFileBean.mFileFormat = FilenameUtils.getExtension(mDisplayName)
                    webFileBean.mFileType =
                        WebMimeTypeHelper.getFileTypeFromExtension(webFileBean.mFileFormat)
                    webFileBean.mLastModifyDate = getDateAndTimeByDefaultFormat(mDateModified)
                    webFileBean.mDateModified = mDateModified
                    resultWebFileBean.add(webFileBean)
                }
            }
        }
        return resultWebFileBean
    }
}