/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: BaseTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/9/03
 ** Author: <PERSON><PERSON><PERSON><PERSON>
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/9/03      1.0     create
 ****************************************************************/
package com.coloros.filemanager;

import android.content.Context;
import android.os.Build;

import org.junit.After;
import org.junit.Before;
import org.junit.BeforeClass;
import org.robolectric.annotation.Config;

import shadow.ShadowUtils;
import shadow.mapping.MappingUserHandleNative;
import shadow.mapping.utils.MappingUtil;

@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowUtils.class})
public class BaseTest {
    public Context mContext;
    @BeforeClass
    public static void classSetUp() {
        MappingUtil.init(MappingUserHandleNative.class, MappingUtil.FLAG_CLASS_INITI);
    }

    @Before
    public void setUp() throws Exception {

    }

    @After
    public void tearDown() throws Exception {

    }
}
