/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : PreferencesUtils.kt.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/4/2
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  <EMAIL>        2024/4/2       1.0      create
 ***********************************************************************/
package com.filemanager.thumbnail.utils

import android.content.Context
import android.content.SharedPreferences
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout

internal object PreferencesUtils {
    private const val TAG = "PreferencesUtils"
    private const val PREFERENCES_TIME_OUT = 300L

    @JvmStatic
    private fun getPreferences(context: Context, preferencesName: String): SharedPreferences? {
        return context.getSharedPreferences(preferencesName, Context.MODE_PRIVATE)
    }

    @JvmStatic
    fun getBoolean(
        context: Context,
        preferencesName: String,
        key: String,
        default: Boolean = false
    ): Boolean {
        var result = default
        kotlin.runCatching {
            runBlocking {
                withTimeout(PREFERENCES_TIME_OUT) {
                    getPreferences(context, preferencesName)?.also {
                        result = it.getBoolean(key, default)
                    }
                }
            }
        }.onFailure {
            Log.w(TAG, "get failed: $preferencesName: [$key, $default], ${it.message}")
            result = default
        }
        Log.d(TAG, "get: $preferencesName: [$key, $result, $default]")
        return result
    }

    @JvmStatic
    fun putBoolean(
        context: Context,
        preferencesName: String,
        key: String,
        value: Boolean
    ) {
        Log.d("PreferencesUtils", "put: $preferencesName: [$key, $value]")
        kotlin.runCatching {
            runBlocking {
                withTimeout(PREFERENCES_TIME_OUT) {
                    getPreferences(context, preferencesName)?.edit()?.also {
                        it.putBoolean(key, value)
                        it.apply()
                    }
                }
            }
        }.onFailure {
            Log.w(TAG, "put failed: $preferencesName: [$key, $value], ${it.message}")
        }
    }

    @JvmStatic
    fun clear(context: Context, preferencesName: String) {
        Log.d(TAG, "clear: $preferencesName")
        kotlin.runCatching {
            runBlocking {
                withTimeout(PREFERENCES_TIME_OUT) {
                    getPreferences(context, preferencesName)?.edit()?.clear()?.apply()
                }
            }
        }.onFailure {
            Log.w(TAG, "clear failed: $preferencesName: ${it.message}")
        }
    }
}