ifneq ($(IS_EU_VERSION),true)
LOCAL_PATH:= $(call my-dir)

SINGLE_TARGET := FileManager.apk

include $(CLEAR_VARS)
LOCAL_MODULE       := $(SINGLE_TARGET)
LOCAL_MODULE_TAGS  := optional
LOCAL_MODULE_CLASS := APPS
LOCAL_DEX_PREOPT_ABORT_OFF := true
ifeq ($(TARGET_ARCH),arm64)
LOCAL_PREBUILT_JNI_LIBS := $(shell aapt l ${LOCAL_PATH}/$(SINGLE_TARGET) | grep lib/arm64.*/.*so | sort | sed 's/^/@/' | xargs)
LOCAL_PREBUILT_JNI_LIBS_arm := $(shell aapt l ${LOCAL_PATH}/$(SINGLE_TARGET) | grep lib/armeabi.*/.*so | sort | sed 's/^/@/' | xargs)
LOCAL_MULTILIB := 64
else
LOCAL_PREBUILT_JNI_LIBS := $(shell aapt l ${LOCAL_PATH}/$(SINGLE_TARGET) | grep lib/armeabi.*/.*so | sort | sed 's/^/@/' | xargs)
endif
#system/app
IS_OS_12_2 := $(shell if [[ "$(OPLUS_OS_API_LEVEL)" -eq 25 ]]; then echo true; else echo false; fi)
IS_OS_GREAT_THAN_13_1 := $(shell if [[ "$(OPLUS_OS_API_LEVEL)" -gt 26 ]]; then echo true; else echo false; fi)
ifeq ($(IS_OS_GREAT_THAN_13_1),true)
  LOCAL_SYS_OPPOAPP := false
  LOCAL_MODULE_PATH := $(TARGET_OUT)/del-app
else ifeq ($(IS_OS_12_2),true)
  LOCAL_MODULE_PATH := $(TARGET_OUT)/del-app
else
  LOCAL_SYS_OPPOAPP := true
  LOCAL_MODULE_PATH := $(TARGET_OUT)/app
endif
ifneq (1,$(filter 1,$(shell echo "$$(( $(PLATFORM_SDK_VERSION) <= 22 ))" )))
LOCAL_CERTIFICATE  := oppo_data_app_std
else
LOCAL_CERTIFICATE  := PRESIGNED
endif
LOCAL_SRC_FILES    := $(SINGLE_TARGET)            
include $(BUILD_PREBUILT)
endif