/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: SettingAboutFragment
 * * Description: the fragment for setting_pref_about
 * * Version: 1.0
 * * Date : 2021/2/8
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2021/2/8       1.0         the fragment for setting_pref_about
 ****************************************************************/
package com.filemanager.setting.ui.about

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.preference.COUIJumpPreference
import com.coui.appcompat.preference.COUIPreferenceCategory
import com.coui.appcompat.preference.COUIPreferenceWithAppbarFragment
import com.coui.appcompat.preference.COUISwitchPreference
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.setting.R
import com.filemanager.setting.ui.opensourcelicense.OpenSourceActivity
import com.oplus.filemanager.interfaze.ad.IAdvertApi

class SettingAboutFragment : COUIPreferenceWithAppbarFragment() {

    companion object {
        private const val TAG = "SettingAboutFragment"
        private const val PREF_OPEN_SOURCE = "setting_pref_open_source"
    }

    private fun getLayoutResource(): Int {
        return R.xml.setting_about
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        (view as ViewGroup).removeView(view.findViewById(com.filemanager.common.R.id.appbar_layout))
        addPreferencesFromResource(getLayoutResource())
        initView()
        return view
    }

    override fun getTitle(): String {
        activity?.let {
            return it.title.toString()
        }
        return ""
    }

    private fun initView() {
        initOpenSourceStatement()
    }

    private fun initOpenSourceStatement() {
        findPreference<COUIJumpPreference>(PREF_OPEN_SOURCE)?.setOnPreferenceClickListener {
            OpenSourceActivity.start(activity)
            true
        }
    }
}