/***********************************************************
 * * Copyright (C), 2010 - 2022 Oplus. All rights reserved..
 * * File: SettingFunctionFragment
 * * Description: the fragment for settingfunction switch
 * * Version: 1.0
 * * Date : 2022/6/10
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2022/6/10       1.0         the fragment for setting function
 ****************************************************************/
package com.filemanager.setting.ui.function

import android.app.Dialog
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.lifecycleScope
import androidx.preference.Preference
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.preference.COUIPreferenceWithAppbarFragment
import com.coui.appcompat.preference.COUISwitchPreference
import com.coui.appcompat.statement.COUIUserStatementDialog
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.CommonConstants.CLEANUP_FUNCTION_SHOW
import com.filemanager.common.constants.CommonConstants.CLOUD_FUNCTION_SHOW
import com.filemanager.common.constants.CommonConstants.DISABLE_KING_DOC
import com.filemanager.common.constants.CommonConstants.DISABLE_TENCENT_DOC
import com.filemanager.common.constants.CommonConstants.K_DOCS_FUNCTION_SHOW
import com.filemanager.common.constants.CommonConstants.OWORK_FUNCTION_SHOW
import com.filemanager.common.constants.CommonConstants.TENCENT_DOCS_FUNCTION_SHOW
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.databus.EventConstants
import com.filemanager.common.databus.LiteEventBus
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.AndroidDataHelper.PREF_ANDROID_DATA_ACCESS
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.EncryptViewUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatisticsUtils.THIRD_FILE_SWITCH_STATUS_CLOSE
import com.filemanager.common.utils.StatisticsUtils.THIRD_FILE_SWITCH_STATUS_OPEN
import com.filemanager.common.utils.UserManagerUtils
import com.filemanager.common.utils.Utils
import com.filemanager.setting.R
import com.filemanager.setting.ui.PrivacyPolicyUtils
import com.oplus.filemanager.interfaze.appswitch.IAppSwitchApi
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.clouddrive.ICloudDrive
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import com.oplus.filemanager.interfaze.filecloudbrowser.IFileCloudBrowser
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.function.Consumer

class SettingFunctionFragment : COUIPreferenceWithAppbarFragment(), Preference.OnPreferenceChangeListener {

    companion object {
        private const val TAG = "SettingFunctionFragment"
        private const val PREF_CLOUD = "setting_pref_cloud"
        private const val PREF_CLEANUP = "setting_pref_cleanup"
        private const val PREF_OWORK = "setting_pref_owork"
        private const val SHOW_ENCRYPT_BOX = "setting_pref_private_safe"
        private const val PREF_TENCENT_DOCS = "setting_pref_tencent_docs"
        private const val PREF_K_DOCS = "setting_pref_kdocs"
        private const val PREF_THIRD_APP_SEARCH = "setting_pref_third_app_search"
        private const val PREF_ANDROID_DATA = "setting_pref_android_data"
        private const val PREF_CLOUD_INDEX = 0
        private const val PREF_CLEANUP_INDEX = 1
        private const val PREF_TENCENT_DOCS_INDEX = 3
        private const val PREF_K_DOCS_INDEX = 4
    }
    private var mCloudSwitch: COUISwitchPreference? = null
    private var mCleanupSwitch: COUISwitchPreference? = null
    private var mOWorkSwitch: COUISwitchPreference? = null
    private var tencentDocsSwitch: COUISwitchPreference? = null
    private var showEncryptBoxSwitch: COUISwitchPreference? = null
    private var kDocsSwitch: COUISwitchPreference? = null
    private var thirdAppSearchSwitch: ThirdAppSearchSwitchPreference? = null
    private var androidDataSwitch: COUISwitchPreference? = null
    private var mBottomAlertDialog: AlertDialog? = null

    private var isAnim: Boolean = false

    private var thirdAppReadyConsumer: Consumer<Boolean>? = null

    private fun getLayoutResource(): Int {
        return R.xml.setting_function
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        (view as ViewGroup).removeView(view.findViewById(com.filemanager.common.R.id.appbar_layout))
        addPreferencesFromResource(getLayoutResource())
        isAnim = arguments?.getBoolean(SettingFunctionActivity.IS_ANIM, false) ?: false
        initView()
        return view
    }

    private fun initView() {
        mCloudSwitch = findPreference(PREF_CLOUD)
        refreshCloudPreference()
        mCleanupSwitch = findPreference(PREF_CLEANUP)
        refreshCleanupPreference()
        mOWorkSwitch = findPreference(PREF_OWORK)
        mOWorkSwitch?.title = KtUtils.getOWorkName(KtUtils.OWORK_NAME_TYPE_3)
        showEncryptBoxSwitch = findPreference(SHOW_ENCRYPT_BOX)
        initShowEncryptBoxEntrySwitch()
        tencentDocsSwitch = findPreference(PREF_TENCENT_DOCS)
        refreshTencentDocsPreference()
        kDocsSwitch = findPreference(PREF_K_DOCS)
        refreshKDocsPreference()
        refreshOWorkPreference()
        thirdAppSearchSwitch = findPreference(PREF_THIRD_APP_SEARCH)
        refreshThirdAppSearchPreference()
        androidDataSwitch = findPreference(PREF_ANDROID_DATA)
        refreshAndroidDataPreference()
    }

    override fun onResume() {
        super.onResume()
        lifecycleScope.launch(Dispatchers.IO) {
            val support = Injector.injectFactory<ICloudDrive>()?.supportCloudDisk() ?: false
            withContext(Dispatchers.Main) {
                mCloudSwitch?.isVisible = support
            }
        }
        mCleanupSwitch?.isVisible = KtAppUtils.hasCleanupFunction
        val isVisibleOWork = isVisibleOWorkPreference()
        Log.d(TAG, "onResume -> isVisibleOWork = $isVisibleOWork")
        mOWorkSwitch?.isVisible = isVisibleOWork
        showEncryptBoxSwitch?.isChecked = EncryptViewUtils.isNeedShowEncryptBox()
    }

    override fun getTitle(): String {
        activity?.let {
            return it.title.toString()
        }
        return ""
    }

    override fun onPreferenceChange(preference: Preference?, newValue: Any?): Boolean {
        when (preference?.key) {
            PREF_CLOUD -> {
                if (mBottomAlertDialog?.isShowing == true) {
                    return false
                }
                mCloudSwitch?.apply {
                    val checkState = isChecked
                    if (checkState) {
                        createDeleteDialogWithTip(getString(com.filemanager.common.R.string.settings_close_cloud_notice), PREF_CLOUD_INDEX)
                        return false
                    } else {
                        PreferencesUtils.put(key = CLOUD_FUNCTION_SHOW, value = true)
                        isChecked = true
                        statisticsSwitchEvent(StatisticsUtils.CLOUD_SHOW, StatisticsUtils.SWITCH_ON)
                    }
                }
            }
            PREF_CLEANUP -> {
                if (mBottomAlertDialog?.isShowing == true) {
                    return false
                }
                mCleanupSwitch?.apply {
                    val checkState = isChecked
                    if (checkState) {
                        createDeleteDialogWithTip(getString(com.filemanager.common.R.string.settings_close_cleanup_notice), PREF_CLEANUP_INDEX)
                        return false
                    } else {
                        PreferencesUtils.put(key = CLEANUP_FUNCTION_SHOW, value = true)
                        isChecked = true
                        statisticsSwitchEvent(StatisticsUtils.CLEANUP_SHOW, StatisticsUtils.SWITCH_ON)
                    }
                }
            }
            PREF_OWORK -> {
                mOWorkSwitch?.apply {
                    val checkState = isChecked
                    PreferencesUtils.put(key = OWORK_FUNCTION_SHOW, value = checkState.not())
                    isChecked = checkState.not()
                }
            }
            SHOW_ENCRYPT_BOX -> clickEncryptBox()
            PREF_TENCENT_DOCS -> {
                tencentDocsSwitch?.apply {
                    return switchCloudDocsSwitch(
                        this,
                        TENCENT_DOCS_FUNCTION_SHOW,
                        StatisticsUtils.TENCENT_DOCS_SHOW,
                        PREF_TENCENT_DOCS_INDEX
                    )
                }
            }

            PREF_K_DOCS -> {
                kDocsSwitch?.apply {
                    return switchCloudDocsSwitch(this, K_DOCS_FUNCTION_SHOW, StatisticsUtils.KING_DOCS_SHOW, PREF_K_DOCS_INDEX)
                }
            }

            PREF_THIRD_APP_SEARCH -> {
                thirdAppSearchSwitch?.apply {
                    if (!PreferencesUtils.getBoolean(key = CommonConstants.THIRD_APP_SEARCH_INTRODUCE_DIALOG_SHOW, default = false)) {
                        if (Utils.isQuickClick().not()) {
                            createThirdAppSearchDialog(this)
                        }
                        return false
                    } else {
                        val checkState = isChecked
                        PreferencesUtils.put(key = CommonConstants.THIRD_APP_SEARCH_FUNCTION_SHOW, value = checkState.not())
                        val dmpApi = Injector.injectFactory<IDmpSearchApi>()
                        dmpApi?.updateSwitchSpToDmp()
                        val globalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                        globalSearchApi?.notifyThirdAppSearchSwitchStatus(checkState.not())
                        isChecked = checkState.not()
                        val checkStatus = if (isChecked) THIRD_FILE_SWITCH_STATUS_OPEN else THIRD_FILE_SWITCH_STATUS_CLOSE
                        StatisticsUtils.statisticsThirdFileSwitch(context, checkStatus)
                    }
                }
            }
            PREF_ANDROID_DATA -> {
                androidDataSwitch?.apply {
                    val checkState = isChecked
                    isChecked = checkState.not()
                    PreferencesUtils.put(key = PREF_ANDROID_DATA_ACCESS, value = isChecked)
                    AndroidDataHelper.openAndroidData = isChecked
                }
            }
        }
        return true
    }

    private fun switchCloudDocsSwitch(preference: COUISwitchPreference, key: String, eventId: String, index: Int): Boolean {
        Log.d(TAG, "switchCloudDocsSwitch -> key = $key ; eventId = $eventId ; index = $index")
        val checkState = preference.isChecked
        Log.d(TAG, "switchCloudDocsSwitch -> checkState = $checkState")
        if (checkState) {
            PreferencesUtils.put(key = key, value = false)
            preference.isChecked = false
            statisticsSwitchEvent(eventId, false)
        } else {
            if (PrivacyPolicyController.hasAgreeAdditionalFunctions()) {
                PreferencesUtils.put(key = key, value = true)
                preference.isChecked = true
                statisticsSwitchEvent(eventId, true)
            } else {
                Log.d(TAG, "switchCloudDocsSwitch -> not agree additional functions")
                createUsageInstructionsDialog(index)
                return false
            }
        }
        return true
    }

    private fun clickEncryptBox() {
        showEncryptBoxSwitch?.apply {
            val checkState = isChecked
            PreferencesUtils.put(key = CommonConstants.NEED_SHOW_ENCRYPT_BOX, value = checkState.not())
            isChecked = checkState.not()
            EncryptViewUtils.mIsNeedShowEncryptBox = isChecked
            statisticsSwitchEvent(StatisticsUtils.SHOW_ENCRYPT_BOX, isChecked)
        }
    }

    /**
     * 统计开关的切换事件
     */
    private fun statisticsSwitchEvent(eventId: String, isOpen: Boolean) {
        val switchState = if (isOpen) {
            StatisticsUtils.SWITCH_ON
        } else {
            StatisticsUtils.SWITCH_OFF
        }
        statisticsSwitchEvent(eventId, switchState)
    }

    /**
     * 统计开关切换的事件
     */
    private fun statisticsSwitchEvent(eventId: String, switchState: String) {
        StatisticsUtils.onCommon(
            appContext,
            eventId,
            mapOf(eventId to switchState)
        )
    }

    private fun initShowEncryptBoxEntrySwitch() {
        showEncryptBoxSwitch?.apply {
            // 内销（才可能显示功能项）+(Os13 || oneplus) + 支持--在二级页面显示
            isVisible = EncryptViewUtils.isSupportEncryption(KtAppUtils.isOnePlus)
            onPreferenceChangeListener = this@SettingFunctionFragment
            isChecked = EncryptViewUtils.isNeedShowEncryptBox()
        }
    }

    private fun createDeleteDialogWithTip(tips: String, prefIndex: Int) {
        val activity = activity ?: return
        val bottomAlertDialogBuilder =
            COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Bottom).apply {
                setMessage(tips)
                setNeutralButton(com.filemanager.common.R.string.settings_close_button) { dialog, _ ->
                    dialog.dismiss()
                    val eventId: String
                    val keyId: String
                    if (prefIndex == PREF_CLOUD_INDEX) {
                        mCloudSwitch?.isChecked = false
                        eventId = StatisticsUtils.CLOUD_SHOW
                        keyId = CLOUD_FUNCTION_SHOW
                    } else {
                        mCleanupSwitch?.isChecked = false
                        eventId = StatisticsUtils.CLEANUP_SHOW
                        keyId = CLEANUP_FUNCTION_SHOW
                    }
                    PreferencesUtils.put(key = keyId, value = false)
                    statisticsSwitchEvent(eventId, StatisticsUtils.SWITCH_OFF)
                }
                setNegativeButton(com.filemanager.common.R.string.dialog_cancel) { dialog, _ ->
                    dialog.dismiss()
                }
                setWindowAnimStyle(com.support.dialog.R.style.Animation_COUI_Dialog)
            }
        mBottomAlertDialog = bottomAlertDialogBuilder.show()
        mBottomAlertDialog?.apply {
            setOnCancelListener {
                mBottomAlertDialog = null
            }
            show()
        }
    }

    /**
     * 腾讯文档，金山文档使用说明弹窗，这里调整了使用控件以及退出按钮词条
     */
    private fun createUsageInstructionsDialog(prefIndex: Int) {
        val context = activity ?: return
        val usageInstructionsDialog = object : COUIUserStatementDialog(context) {
            @Deprecated("Deprecated in Java")
            override fun onBackPressed() {
                super.onBackPressed()
                dismiss()
            }
        }
        usageInstructionsDialog.apply {
            setIsShowInMaxHeight(false)
            setCanceledOnTouchOutside(false)
            hideDragView()
            titleText = resources.getString(com.filemanager.common.R.string.cloud_docs_user_statement_title)
            statement = PrivacyPolicyUtils.addUsageInstructionsPrivacyPolicyLinkSpan(context, com.filemanager.common.R.string
                .agree_cloud_docs_user_statement_content)
            bottomButtonText = resources.getString(com.filemanager.common.R.string.basic_function_ok)
            exitButtonText = resources.getString(com.filemanager.common.R.string.dialog_cancel)
            onButtonClickListener = object : COUIUserStatementDialog.OnButtonClickListener {
                override fun onExitButtonClick() {
                    usageInstructionsDialog.dismiss()
                }

                override fun onBottomButtonClick() {
                    usageInstructionsDialog.dismiss()
                    PrivacyPolicyController.saveAgreeAdditionalFunctions(true)
                    PrivacyPolicyController.saveAgreeUseNet(true)
                    var eventId = ""
                    var keyId = ""
                    if (prefIndex == PREF_TENCENT_DOCS_INDEX) {
                        tencentDocsSwitch?.isChecked = true
                        eventId = StatisticsUtils.CLOUD_SHOW
                        keyId = TENCENT_DOCS_FUNCTION_SHOW
                    } else if (prefIndex == PREF_K_DOCS_INDEX) {
                        kDocsSwitch?.isChecked = true
                        eventId = StatisticsUtils.CLEANUP_SHOW
                        keyId = K_DOCS_FUNCTION_SHOW
                    }
                    PreferencesUtils.put(key = keyId, value = true)
                    statisticsSwitchEvent(eventId, true)
                }
            }
            setOnKeyListener { _, keyCode, event ->
                Log.d(TAG, "onKey keyCode:$keyCode event:$event")
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                    onBackPressed()
                    return@setOnKeyListener true
                }
                false
            }
            PredictiveBackUtils.registerOnBackInvokedCallback(this)
        }
        PrivacyPolicyUtils.setDialogStatusBarTransparentAndBlackFont(context, usageInstructionsDialog, false)
        usageInstructionsDialog.show()
    }



    /**
     * 三方应用搜索介绍弹窗
     */
    private fun createThirdAppSearchDialog(switchPref: COUISwitchPreference) {
        val context = activity ?: return
        val thirdAppSearchDialog = object : COUIUserStatementDialog(context) {
            @Deprecated("Deprecated in Java")
            override fun onBackPressed() {
                super.onBackPressed()
                dismiss()
            }
        }
        thirdAppSearchDialog.apply {
            setIsShowInMaxHeight(false)
            setCanceledOnTouchOutside(false)
            hideDragView()
            titleText = resources.getString(com.filemanager.common.R.string.third_app_search_dialog_title)
            statement = PrivacyPolicyUtils.addUsageInstructionsPrivacyPolicyLinkSpan(context, com.filemanager.common.R.string
                .agree_third_app_search_dialog_content)
            bottomButtonText = resources.getString(com.filemanager.common.R.string.basic_function_ok)
            exitButtonText = resources.getString(com.filemanager.common.R.string.dialog_cancel)
            onButtonClickListener = object : COUIUserStatementDialog.OnButtonClickListener {
                override fun onExitButtonClick() {
                    thirdAppSearchDialog.dismiss()
                }

                override fun onBottomButtonClick() {
                    thirdAppSearchDialog.dismiss()
                    switchPref.isChecked = true
                    // 搜索开关
                    PreferencesUtils.put(key = CommonConstants.THIRD_APP_SEARCH_FUNCTION_SHOW, value = true)
                    val dmpApi = Injector.injectFactory<IDmpSearchApi>()
                    dmpApi?.updateSwitchSpToDmp()
                    val globalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                    globalSearchApi?.notifyThirdAppSearchSwitchStatus(true)
                    PreferencesUtils.put(key = CommonConstants.THIRD_APP_SEARCH_INTRODUCE_DIALOG_SHOW, value = true)
                    StatisticsUtils.statisticsThirdFileSwitch(context, THIRD_FILE_SWITCH_STATUS_OPEN)
                }
            }
            setOnKeyListener { _, keyCode, event ->
                Log.d(TAG, "onKey keyCode:$keyCode event:$event")
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                    onBackPressed()
                    return@setOnKeyListener true
                }
                false
            }
            PredictiveBackUtils.registerOnBackInvokedCallback(this)
        }
        PrivacyPolicyUtils.setDialogStatusBarTransparentAndBlackFont(context, thirdAppSearchDialog, false)
        thirdAppSearchDialog.show()
    }

    private fun refreshCloudPreference() {
        mCloudSwitch?.apply {
            lifecycleScope.launch(Dispatchers.IO) {
                val support = Injector.injectFactory<ICloudDrive>()?.supportCloudDisk() ?: false
                withContext(Dispatchers.Main) {
                    isVisible = support
                }
            }
            onPreferenceChangeListener = this@SettingFunctionFragment
            isChecked = PreferencesUtils.getBoolean(key = CLOUD_FUNCTION_SHOW, default = true)
            if (!FeatureCompat.sIsExpRom) {
                PreferencesUtils.put(key = CLOUD_FUNCTION_SHOW, value = isChecked)
            }
        }
    }

    private fun refreshCleanupPreference() {
        mCleanupSwitch?.apply {
            isVisible = KtAppUtils.hasCleanupFunction
            onPreferenceChangeListener = this@SettingFunctionFragment
            isChecked = PreferencesUtils.getBoolean(key = CLEANUP_FUNCTION_SHOW, default = true)
            if (!FeatureCompat.sIsExpRom) {
                PreferencesUtils.put(key = CLEANUP_FUNCTION_SHOW, value = isChecked)
            }
        }
    }

    private fun refreshTencentDocsPreference() {

        tencentDocsSwitch?.apply {
            val hasComponent = (Injector.injectFactory<IFileCloudBrowser>() != null)
            isVisible = hasComponent && PreferencesUtils.getBoolean(
                CommonConstants.PREF_NAME_FILE_CLOUD_DOCS,
                CommonConstants.KEY_SUPPORT_TENCENT_DOCS,
                DISABLE_TENCENT_DOC
            )
            onPreferenceChangeListener = this@SettingFunctionFragment
            isChecked = PrivacyPolicyController.hasAgreeAdditionalFunctions() && PreferencesUtils.getBoolean(
                key = TENCENT_DOCS_FUNCTION_SHOW,
                default = DISABLE_TENCENT_DOC
            )
        }
    }

    private fun refreshKDocsPreference() {
        kDocsSwitch?.apply {
            val hasComponent = (Injector.injectFactory<IFileCloudBrowser>() != null)
            isVisible = hasComponent && PreferencesUtils.getBoolean(
                CommonConstants.PREF_NAME_FILE_CLOUD_DOCS,
                CommonConstants.KEY_SUPPORT_K_DOCS,
                DISABLE_KING_DOC
            )
            onPreferenceChangeListener = this@SettingFunctionFragment
            isChecked = PrivacyPolicyController.hasAgreeAdditionalFunctions() && PreferencesUtils.getBoolean(
                key = K_DOCS_FUNCTION_SHOW,
                default = DISABLE_KING_DOC
            )
        }
    }

    private fun refreshOWorkPreference() {
        mOWorkSwitch?.apply {
            val visible = isVisibleOWorkPreference()
            Log.d(TAG, "refreshOWorkPreference -> visible = $visible")
            isVisible = visible
            onPreferenceChangeListener = this@SettingFunctionFragment
            isChecked = PreferencesUtils.getBoolean(key = OWORK_FUNCTION_SHOW, default = true)
            PreferencesUtils.put(key = OWORK_FUNCTION_SHOW, value = isChecked)
        }
    }

    private fun refreshThirdAppSearchPreference() {
        // 监听避免外部跳转时初始化延迟导致
        LiteEventBus.instance.with(
            EventConstants.EVENT_THIRD_APP_SEARCH_IS_FEATURE_ON,
            EventConstants.OBSERVERID_THIRD_APP_SEARCH_FEATURE_CHANGE_APP
        )?.observe(viewLifecycleOwner) { isFeatureOn ->
            Log.d(TAG, "eventChange EVENT_THIRD_APP_SEARCH_IS_OPEN")
            thirdAppSearchSwitch?.apply {
                isVisible = isFeatureOn as Boolean
            }
        }
        //注册状态回调监听
        registerConsumer()
        thirdAppSearchSwitch?.apply {
            val visible = isVisibleThirdAppSearch()
            Log.d(TAG, "refreshThirdAppSearchPreference -> visible = $visible isAnim:$isAnim")
            needAnim = isAnim
            isVisible = visible
            onPreferenceChangeListener = this@SettingFunctionFragment
            // 首次进来，默认开关是关闭
            isChecked = PreferencesUtils.getBoolean(key = CommonConstants.THIRD_APP_SEARCH_FUNCTION_SHOW, default = false)
        }
    }

    private fun refreshAndroidDataPreference() {
        androidDataSwitch?.apply {
            isVisible = AndroidDataHelper.canViewAndroidDataFiles && AndroidDataHelper.showAndroidDataSettingSwitch && AndroidDataHelper
                .checkSupportViewAndroidData()
            onPreferenceChangeListener = this@SettingFunctionFragment
            isChecked = AndroidDataHelper.openAndroidData ?: true
        }
    }



    private fun isVisibleOWorkPreference(): Boolean {
        return AppUtils.isAppInstalledByPkgName(appContext, CommonConstants.OWORK) &&
                JavaFileHelper.checkAnyPathExist(CommonConstants.OWORK_DEFAULT_PATHS) &&
                UserManagerUtils.checkIsSystemUser(appContext)
    }

    private fun isVisibleThirdAppSearch(): Boolean {
        if (FeatureCompat.sIsExpRom) {
            return false
        }
        val appSwitchApi = Injector.injectFactory<IAppSwitchApi>()
        val isThirdAppConfigFeatureOn = appSwitchApi?.isCurrentThirdAppListenConfigFeatureOn()
        Log.i(TAG, "isVisibleThirdAppSearch config featureOn $isThirdAppConfigFeatureOn")
        return isThirdAppConfigFeatureOn ?: false
    }

    private fun registerConsumer() {
        if (FeatureCompat.sIsExpRom) {
            return
        }
        if (thirdAppReadyConsumer == null) {
            thirdAppReadyConsumer = Consumer {
                thirdAppSearchSwitch?.apply {
                    val visible = isVisibleThirdAppSearch()
                    Log.d(TAG, " registerConsumer accept -> visible = $visible isAnim:$isAnim")
                    needAnim = isAnim
                    isVisible = visible
                    onPreferenceChangeListener = this@SettingFunctionFragment
                    // 首次进来，默认开关是关闭
                    isChecked = PreferencesUtils.getBoolean(key = CommonConstants.THIRD_APP_SEARCH_FUNCTION_SHOW, default = false)
                }
            }
            val appSwitchApi = Injector.injectFactory<IAppSwitchApi>()
            appSwitchApi?.setAppListConfigReadyCallback(thirdAppReadyConsumer)
            Log.d(TAG, "registerConsumer")
        }
    }

    private fun releaseConsumer() {
        if (FeatureCompat.sIsExpRom) {
            return
        }
        if (thirdAppReadyConsumer != null) {
            thirdAppReadyConsumer = null
            val appSwitchApi = Injector.injectFactory<IAppSwitchApi>()
            appSwitchApi?.setAppListConfigReadyCallback(null)
            Log.d(TAG, "releaseConsumer")
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        dismissDialog(mBottomAlertDialog)
        releaseConsumer()
    }

    private fun dismissDialog(dialog: Dialog?) {
        if (dialog?.isShowing == true) {
            dialog.dismiss()
        }
    }
}