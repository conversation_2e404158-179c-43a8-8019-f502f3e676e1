/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : ConvertUtil.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/27
 * * Author      : huangyuanwang
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.util

import android.content.Context
import android.util.Log
import com.coloros.filemanager.appswitch.bean.AppSwitchConfig
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.Range.Companion.DEVICE_TYPE_COMMON
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.Range.Companion.DEVICE_TYPE_PHONE
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.Range.Companion.DEVICE_TYPE_TABLET
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper.ViewExtracRequest.Companion.REQUEST_TYPE_DEFALT
import com.filemanager.common.utils.ModelUtils

object ConvertUtil {

    const val TAG = "ConvertUtil"

    private var isTablet = ModelUtils.isTablet()


    @JvmStatic
    fun getAppSwitchConfigByThirdAppConfigWithNoCheck(
        switchOpen: Boolean,
        thirdAppListenConfigParam: ThirdAppListenConfigParam
    ): AppSwitchConfig {
        val newAppSwitchConfig = AppSwitchConfig()
        if (thirdAppListenConfigParam.isOn == ThirdAppListenConfigParam.FEATURE_OFF || !switchOpen) {
            newAppSwitchConfig.switchOn = false
            newAppSwitchConfig.activities.clear()
            newAppSwitchConfig.packages.clear()
        } else if (thirdAppListenConfigParam.isOn == ThirdAppListenConfigParam.FEATURE_ON) {
            newAppSwitchConfig.switchOn = true
            val allPackageList = mutableListOf<String>()
            val allPreViewActivityList = mutableListOf<String>()
            if (thirdAppListenConfigParam.rules.size > 0) {
                for (rule in thirdAppListenConfigParam.rules) {
                    allPackageList.add(rule.mPackage)
                    allPreViewActivityList.addAll(rule.getAllPreviewActivitys())
                }
            }
            newAppSwitchConfig.packages.addAll(allPackageList.distinct())
            newAppSwitchConfig.activities.addAll(allPreViewActivityList.distinct())
        }
        Log.i(TAG, "getAppSwitchConfigByThirdAppConfigWithNoCheck switchOpen $switchOpen thirdAppListenConfigParam: $thirdAppListenConfigParam, " +
                "resultAppSwitchConfig: $newAppSwitchConfig")
        return newAppSwitchConfig
    }

    @JvmStatic
    fun getSwitchOffAppSwitchConfig(): AppSwitchConfig {
        val newAppSwitchConfig = AppSwitchConfig()
        newAppSwitchConfig.switchOn = false
        newAppSwitchConfig.activities.clear()
        newAppSwitchConfig.packages.clear()
        return newAppSwitchConfig
    }


    @JvmStatic
    fun checkAndgetAppSwitchConfigByThirdAppConfig(
        context: Context,
        thirdAppListenConfigParam: ThirdAppListenConfigParam
    ): AppSwitchConfig {
        val newAppSwitchConfig = AppSwitchConfig()
        if (thirdAppListenConfigParam.isOn == ThirdAppListenConfigParam.FEATURE_OFF) {
            newAppSwitchConfig.switchOn = false
            newAppSwitchConfig.activities.clear()
            newAppSwitchConfig.packages.clear()
        } else if (thirdAppListenConfigParam.isOn == ThirdAppListenConfigParam.FEATURE_ON) {
            newAppSwitchConfig.switchOn = true
            val allPackageList = mutableListOf<String>()
            val allPreViewActivityList = mutableListOf<String>()
            if (thirdAppListenConfigParam.rules.size > 0) {
                for (rule in thirdAppListenConfigParam.rules) {
                    if (checkRulesValidate(context, rule)) {
                        allPackageList.add(rule.mPackage)
                        allPreViewActivityList.addAll(rule.getAllPreviewActivitys())
                    }
                }
            }
            newAppSwitchConfig.packages.addAll(allPackageList.distinct())
            newAppSwitchConfig.activities.addAll(allPreViewActivityList.distinct())
        }
        Log.i(TAG, "checkAndgetAppSwitchConfigByThirdAppConfig, thirdAppListenConfigParam: $thirdAppListenConfigParam, " +
                    "resultAppSwitchConfig: $newAppSwitchConfig")
        return newAppSwitchConfig
    }

    @JvmStatic
    fun checkRuleListValidate(
        context: Context,
        rules: List<ThirdAppListenConfigParam.DetectRule>
    ): Boolean {
        var result = false
        for (rule in rules) {
            if (checkRulesValidate(context, rule)) {
                result = true
                break
            }
        }
        Log.i(TAG, "checkRuleListValidate rules size ${rules.size}, result $result")
        return result
    }


    @JvmStatic
    fun checkRulesValidate(
        context: Context,
        rule: ThirdAppListenConfigParam.DetectRule?
    ): Boolean {
        if (rule == null) {
            Log.i(TAG, "checkRulesValidate input rule is empty, return false")
            return false
        }
        val packageName = rule.mPackage
        var packageInstalled = false
        var versionCodeMeet = false
        var deviceTypeMeet = false
        if (!packageName.isNullOrEmpty()) {
            try {
                //判断App是否安装成功
                val packageInfo = context.packageManager.getPackageInfo(packageName, 0)
                packageInstalled = true
                val installAppVersionCode = packageInfo.longVersionCode
                versionCodeMeet = versionCodeMeet(installAppVersionCode, rule.mRange)
                val deviceType = rule.mRange.deviceType
                //判定规则的设备类型是否符合
                deviceTypeMeet = if (isTablet) {
                    (deviceType == DEVICE_TYPE_TABLET) || (deviceType == DEVICE_TYPE_COMMON)
                } else {
                    (deviceType == DEVICE_TYPE_PHONE) || (deviceType == DEVICE_TYPE_COMMON)
                }
                Log.i(
                    TAG,
                    "checkRulesValidate packageName $packageName, installAppVersionCode $installAppVersionCode, " +
                            "versionCodeMeet $versionCodeMeet, " +
                            "deviceType $deviceType, isTablet $isTablet"
                )
            } catch (e: android.content.pm.PackageManager.NameNotFoundException) {
                Log.e(TAG, "checkRulesValidate packageName $packageName not installed")
                packageInstalled = false
            }
        }
        Log.d(
            TAG,
            "checkRulesValidate packageName $packageName, packageInstalled $packageInstalled, " +
                    "versionCodeMeet $versionCodeMeet, deviceMeet $deviceTypeMeet"
        )
        return packageInstalled && versionCodeMeet && deviceTypeMeet
    }


    @JvmStatic
    fun convertRequestToResult(innerRequest: ViewExtractHelper.ViewExtracRequest): ViewExtractHelper.ViewExtractResult {
        val result = ViewExtractHelper.ViewExtractResult(
            innerRequest.viewExactString,
            innerRequest.targetActivity,
            innerRequest.preViewActivity,
            innerRequest.packageName,
            innerRequest.packageVersion
        )
        result.isDefault = innerRequest.requestType == REQUEST_TYPE_DEFALT
        return result
    }

    @JvmStatic
    fun versionCodeMeet(versionCode: Long, range: ThirdAppListenConfigParam.Range): Boolean {
        var versionCodeMeet = false
        val ruleMaxVersionCode = range.maxVersion
        val ruleMinVersionCode = range.minVersion
        //判断本地的app版本号是否符合规则版本号范围
        if (ruleMaxVersionCode == null && ruleMinVersionCode == null) {
            versionCodeMeet = true
        } else if (ruleMaxVersionCode == null && ruleMinVersionCode != null) {
            versionCodeMeet = (versionCode >= ruleMinVersionCode)
        } else if (ruleMaxVersionCode != null && ruleMinVersionCode == null) {
            versionCodeMeet = (versionCode <= ruleMaxVersionCode)
        } else if (ruleMaxVersionCode != null && ruleMinVersionCode != null) {
            versionCodeMeet =
                (versionCode <= ruleMaxVersionCode) && (versionCode >= ruleMinVersionCode)
        }
        Log.i(TAG, "versionCodeMeet versionCode $versionCode, rang $range, result $versionCodeMeet")
        return versionCodeMeet
    }
}