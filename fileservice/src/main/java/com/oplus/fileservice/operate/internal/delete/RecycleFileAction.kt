/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecycleFileAction
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 20:35
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/21       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.delete

import com.filemanager.common.batch.SingleBatchAction
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.utils.Utils

class RecycleFileAction(
    bulkCount: Int = BULK_SCAN_SIZE
) : SingleBatchAction<String>(bulkCount) {

    override fun flush() {
        val size = mPending.size
        if (size > 0) {
            MediaScannerCompat.sendMultiDirMediaScanner(mPending, Utils.MEDIA_SCAN_RECYCLE_RESTORE)
            mPending.clear()
        }
    }

    companion object {
        private const val BULK_SCAN_SIZE = 50
    }
}