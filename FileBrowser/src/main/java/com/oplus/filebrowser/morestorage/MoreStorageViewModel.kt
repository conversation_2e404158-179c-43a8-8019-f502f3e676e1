/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : ViewModel of MoreStorage
 * * Version     : 1.0
 * * Date        : 2020/5/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser.morestorage

import androidx.lifecycle.MutableLiveData
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filebrowser.R
import com.oplus.filemanager.dfm.DFMManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

private const val TAG = "MoreStorageViewModel"

class MoreStorageViewModel : BaseViewModel() {
    val mUiState = MutableLiveData<ArrayList<FileBrowserRootFileBean>>()
    fun initLoader(paths: ArrayList<String>?) {
        if (paths != null) {
            launch {
                withContext(Dispatchers.IO) {
                    val internalPath = VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)
                    val externalPath = VolumeEnvironment.getExternalSdPath(MyApplication.sAppContext)
                    val dfsDeviceInfo = DFMManager.getDFSDevice()
                    val pathList = ArrayList<FileBrowserRootFileBean>()
                    for (path in paths) {
                        val internalBeanFileBrowser: FileBrowserRootFileBean
                        if (path.equals(internalPath, ignoreCase = true)) {
                            val resId = if (ModelUtils.isTablet()) {
                                com.filemanager.common.R.drawable.ic_storage_pad
                            } else {
                                com.filemanager.common.R.drawable.ic_storage_phone
                            }
                            val deviceName = MyApplication.sAppContext.resources.getString(com.filemanager.common.R.string.string_all_files)
                            internalBeanFileBrowser = FileBrowserRootFileBean(internalPath, deviceName, resId)
                        } else if (KtUtils.checkIsDfmPath(path) && dfsDeviceInfo != null) {
                            val deviceName = dfsDeviceInfo.getString(KtConstants.DFM_DEVICE_NAME)
                            val deviceType = dfsDeviceInfo.getInt(KtConstants.DFM_DEVICE_TYPE, 0)
                            val resId = if (deviceType == KtConstants.DFM_PAD_TYPE) {
                                com.filemanager.common.R.drawable.ic_dfs_storage_pad
                            } else {
                                com.filemanager.common.R.drawable.ic_dfs_storage_phone
                            }
                            internalBeanFileBrowser = if (deviceName != null) {
                                FileBrowserRootFileBean(path, deviceName, resId)
                            } else {
                                FileBrowserRootFileBean(path, PathFileWrapper(path).mDisplayName ?: "", resId)
                            }
                        } else if ((externalPath != null) && (path.equals(externalPath, ignoreCase = true))) {
                            val deviceName = MyApplication.sAppContext.resources.getString(com.filemanager.common.R.string.storage_external)
                            internalBeanFileBrowser = FileBrowserRootFileBean(externalPath, deviceName, com.filemanager.common.R.drawable.ic_otg_icon)
                        } else {
                            internalBeanFileBrowser = FileBrowserRootFileBean(path, PathFileWrapper(path).mDisplayName
                                    ?: "", com.filemanager.common.R.drawable.ic_otg_icon)
                            Log.d(TAG, "initLoader else: path=${path}, name=${internalBeanFileBrowser.mDisplayName}")
                        }
                        pathList.add(internalBeanFileBrowser)
                    }
                    mUiState.postValue(pathList)
                }
            }
        }
    }
}
