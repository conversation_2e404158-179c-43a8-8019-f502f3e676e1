/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RealmeHomePageAdMgr
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/3/21 15:33
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/3/21       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat.brand.realme

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.RelativeLayout
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.Constants
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.utils.Log
import com.oplus.filemanager.ad.HomePageAdMgr
import com.oplus.filemanager.ad.R
import com.oplus.filemanager.compat.interfaces.IHomePageAdMgr
import com.oplus.filemanager.utils.AdConstants

class RealmeHomePageAdMgr : IHomePageAdMgr, AdvertCallback {

    private var mMainFragmentContainer: ViewGroup? = null
    private var mAdvertEntry: AdvertEntry? = null

    override fun setContainer(rootView: View, rootViewId: Int) {
        try {
            val adRootView = (rootView.findViewById(rootViewId) as? ViewStub)?.inflate()
            mMainFragmentContainer = adRootView?.findViewById(com.filemanager.common.R.id.ad_container_top)
        } catch (e: Exception) {
            Log.d(AdvertEntry.AD_TAG, "init ad container fail:" + e.message)
        }
    }

    override fun requestMainAd(activity: Activity) {
        if (!PrivacyPolicyController.hasAgreePrivacy()) {
            Log.d(AdvertEntry.AD_TAG, "privacy check fail")
            return
        }
        val entry = getEntry(HomePageAdMgr.ENTRY_NAME, AdConstants.CATEGORY_MAIN_POS_ID, activity)
        Log.d(AdvertEntry.AD_TAG, "request main fragment page ad")
        entry.startWork(Constants.PAGE_MAIN)
    }

    override fun getEntryView(name: String): RelativeLayout? {
        return mAdvertEntry?.getAdView()
    }

    private fun getEntry(name: String, posId: String, activity: Activity): AdvertEntry {
        if (mAdvertEntry == null) {
            val adLayout = RelativeLayout(MyApplication.sAppContext)
            adLayout.layoutParams = RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.MATCH_PARENT)
            mAdvertEntry = AdvertEntry(activity, adLayout, this, posId)
        } else {
            mAdvertEntry?.setCallBack(this)
        }
        return mAdvertEntry as AdvertEntry
    }

    override fun onLoadSuccess() {
        Log.d(AdvertEntry.AD_TAG, "mainFragment ad load success")
        mMainFragmentContainer?.let { container ->
            val adView = mAdvertEntry?.getAdView()
            adView?.let {
                container.removeAllViews()
                container.addView(it)
                container.visibility = View.VISIBLE
            }
        }
    }

    override fun dismiss() {
        Log.d(AdvertEntry.AD_TAG, "mainFragment ad dismiss")
        mMainFragmentContainer?.let {
            it.removeAllViews()
            it.visibility = View.GONE
        }
    }

    override fun eventOnDestroy() {
        mAdvertEntry?.destroyAd()
    }
}