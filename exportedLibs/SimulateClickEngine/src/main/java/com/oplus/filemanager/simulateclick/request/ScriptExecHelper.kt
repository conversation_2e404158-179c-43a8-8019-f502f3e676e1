/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScriptExecHelper.kt
 * Description:
 *     Help to execute script with engine
 *
 * Version: 1.0
 * Date: 2024-05-28
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-05-28   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.request

import android.content.Context
import android.os.HandlerThread
import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread
import com.oplus.filemanager.simulateclick.engine.Script
import com.oplus.filemanager.simulateclick.engine.SimulateClickEngine
import com.oplus.filemanager.simulateclick.engine.utils.Log
import com.oplus.filemanager.simulateclick.engine.utils.MultiAppUtils
import com.oplus.filemanager.simulateclick.file.BuiltinScriptDetector
import com.oplus.filemanager.simulateclick.file.DebugScriptDetector
import com.oplus.filemanager.simulateclick.file.RemoteScriptDetector
import com.oplus.filemanager.simulateclick.file.ScriptFilePathUtils
import com.oplus.filemanager.simulateclick.service.AccessibilityServiceBridge

internal class ScriptExecHelper(
    private val context: Context,
    private val serviceBridge: AccessibilityServiceBridge
) {

    @VisibleForTesting
    @Volatile
    internal var executingCount = 0

    @WorkerThread
    @Synchronized
    fun executeScript(handlerThread: HandlerThread, request: ScriptExecRequest) {
        Log.d(TAG, "executeScript: request=$request")
        val script = readScript(request)
        if (script?.checkScriptEnableStatus(request) != true) {
            Log.d(TAG, "executeScript: no enabled script for request.")
            request.callbackDetected(false)
            return
        }
        if (MultiAppUtils.isChooseCloneApp(request.packageName)) {
            Log.d(TAG, "executeScript: unsupported when need to choose clone app")
            request.callbackDetected(false)
            return
        }
        request.callbackDetected(true)
        val engine = SimulateClickEngine(context, serviceBridge, handlerThread)
        engine.dealWithStep(
            packageName = request.packageName,
            skillName = request.skillName,
            script = script,
            replacedMap = request.replacedMap
        )
        executingCount++
        if (executingCount > MAX_EXECUTE_TRACK_COUNT) {
            executingCount = MAX_EXECUTE_TRACK_COUNT
        }
    }

    @Synchronized
    fun checkHasExecuting(): Boolean {
        executingCount--
        if (executingCount < 0) {
            executingCount = 0
        }
        return executingCount > 0
    }

    @VisibleForTesting
    internal fun readScript(request: ScriptExecRequest): Script? {
        val detected = mutableListOf<Script>()
        BuiltinScriptDetector()
            .detectScript(context, request.packageName, request.skillName)
            ?.let(detected::add)
        RemoteScriptDetector()
            .detectScript(context, request.packageName, request.skillName)
            ?.let(detected::add)
        if (ScriptFilePathUtils.isDebugPathEnabled) {
            DebugScriptDetector()
                .detectScript(context, request.packageName, request.skillName)
                ?.let(detected::add)
        }
        return detected.maxByOrNull { it.scriptVersion }.also {
            val scriptInfo = it?.run { "ver=$scriptVersion, enable=$scriptEnable, path=$filePath" }
            if (scriptInfo == null) {
                Log.d(TAG, "readScript: no script for request")
            } else {
                Log.d(TAG, "readScript: use script, $scriptInfo")
            }
        }
    }

    @VisibleForTesting
    internal fun Script.checkScriptEnableStatus(request: ScriptExecRequest): Boolean {
        if (!scriptEnable) {
            return false
        }
        val skill = skills?.firstOrNull { it.skillName == request.skillName } ?: return false
        return skill.skillEnable
    }

    private fun ScriptExecRequest.callbackDetected(hasScriptDetected: Boolean) {
        context.mainExecutor.execute { detectedCallback?.onScriptDetected(hasScriptDetected) }
    }

    private companion object {
        private const val TAG = "ScriptExecHelper"
        private const val MAX_EXECUTE_TRACK_COUNT = 2
    }
}