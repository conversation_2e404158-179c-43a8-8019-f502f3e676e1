/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : DefaultOpenEntity.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * * 飞书中intent中带入的数据结构，intent中的extras中会带入这个类的类
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.bytedance.ee.bear.contract.drive.sdk.entity.open;

import android.os.Parcel;

public class DefaultOpenEntity extends BaseOpenEntity {

    public static final Creator<DefaultOpenEntity> CREATOR = new Creator<DefaultOpenEntity>() {
        @Override
        public DefaultOpenEntity createFromParcel(Parcel in) {
            return new DefaultOpenEntity(in);
        }

        @Override
        public DefaultOpenEntity[] newArray(int size) {
            return new DefaultOpenEntity[size];
        }
    };
    protected DefaultOpenEntity(Parcel parcel) {
        super(parcel);
    }
}
