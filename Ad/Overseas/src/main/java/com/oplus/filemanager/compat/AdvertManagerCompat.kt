/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AdvertManagerCompat
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/3/13 20:36
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/3/13       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat

import android.content.Context
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.oplus.filemanager.compat.brand.oppo.AdvertController
import com.oplus.filemanager.compat.brand.realme.AdvertEntry

object AdvertManagerCompat {

    private const val TAG = "AdvertManagerCompat"

    fun initAdLoader(context: Context) {
        when (initAdBrand()) {
            AdBrand.OPPO -> AdvertController.initAdLoader(context)
            AdBrand.REALME -> AdvertEntry.initAdSDK(context)
            AdBrand.NULL -> Log.d(TAG, "initAdLoader -> AdBrand NULL")
        }
    }

    fun isAdEnabled(context: Context): Boolean {
        return when (initAdBrand()) {
            AdBrand.OPPO -> AdvertController.isAdEnabled(context)
            AdBrand.REALME -> AdvertEntry.isAdEnabled(context)
            AdBrand.NULL -> false
        }
    }
}