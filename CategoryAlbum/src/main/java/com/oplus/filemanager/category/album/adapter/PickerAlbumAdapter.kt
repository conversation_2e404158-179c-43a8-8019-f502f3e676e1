/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.adapter
 * * Version     : 1.0
 * * Date        : 2020/6/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.album.adapter

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.children
import androidx.lifecycle.Lifecycle
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.imageloader.glide.GlideLoader
import com.filemanager.common.viewholder.BaseNormalVH
import com.filemanager.common.viewholder.FileBrowserLargeListVH
import com.filemanager.common.viewholder.NormalFooterVH
import com.filemanager.common.viewholder.NormalListVH
import com.filemanager.common.viewholder.PickerNormalGridVH
import com.filemanager.common.viewholder.listener.PickerRecycleViewItemHelper
import com.oplus.filemanager.category.album.R
import kotlin.sequences.forEach

class PickerAlbumAdapter(
    context: Context,
    lifecycle: Lifecycle
) : AlbumAdapter(context, lifecycle) {

    override var mScanViewModel = KtConstants.SCAN_MODE_GRID
        set(value) {
            field = value
            if (value == KtConstants.SCAN_MODE_GRID) {
                (mContext as? Activity)?.let {
                    mItemWith = ItemDecorationFactory.getPickerGridItemWidth(it, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM, true)
                }
            }
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseNormalVH {
        return when (viewType) {
            BaseFileBean.TYPE_FILE_LIST_FOOTER -> {
                NormalFooterVH(
                    LayoutInflater.from(parent.context).inflate(R.layout.album_gire_footer_item, parent, false)
                )
            }
            KtConstants.SCAN_MODE_GRID -> {
                PickerNormalGridVH(LayoutInflater.from(parent.context).inflate(PickerNormalGridVH.getLayoutId(), parent, false))
                    .apply { setBorderRoundCornerType(true, GlideLoader.ROUND_CONNER_ALL) }
            }
            KtConstants.SCAN_MODE_LIST -> {
                NormalListVH(LayoutInflater.from(parent.context).inflate(NormalListVH.getLayoutId(), parent, false), mImgRadius)
                    .apply { setBorderRoundCornerType(true, GlideLoader.ROUND_CONNER_ALL) }
            }
            KtConstants.SCAN_MODE_LIST_LARGE -> FileBrowserLargeListVH.create(parent, mImgRadius)
            else -> {
                PickerNormalGridVH(LayoutInflater.from(parent.context).inflate(PickerNormalGridVH.getLayoutId(),
                    parent, false)).apply { setBorderRoundCornerType(true) }
                    .apply { setBorderRoundCornerType(true, GlideLoader.ROUND_CONNER_ALL) }
            }
        }
    }

    @SuppressLint("Range")
    override fun onBindViewHolder(holder: BaseNormalVH, position: Int) {
        if ((position < 0) || (position >= mFiles.size)) {
            return
        }
        if (holder is PickerNormalGridVH) {
            holder.setItemWidth(mItemWith)
        }
        val file = mFiles[position]
        holder.loadData(
            mContext,
            getItemKey(file, position),
            file,
            mChoiceMode,
            mSelectionArray,
            mSizeCache,
            mThreadManager,
            this
        )
        if (holder is NormalListVH) {
            holder.updateDividerVisible(mFiles.size - 2, position)
            holder.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
        } else if (holder is FileBrowserLargeListVH) {
            holder.updateDividerVisible(mFiles.size - 2, position)
            holder.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
        }
        (holder.itemView as? ViewGroup)?.children?.forEach {
            it.alpha = 1.0f
        }
        mOnPickerRecyclerItemClickListener?.let { listener ->
            PickerRecycleViewItemHelper.registerItemTouchClickListener(holder, listener)
        }
    }
}