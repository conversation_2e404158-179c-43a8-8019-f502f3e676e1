/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DFMP2PConflictTypeTest
 ** Description : DFM P2P Conflict Type Unit Test
 ** Version     : 1.0
 ** Date        : 2024/04/09 10:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/04/09       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.categorydfm.dfm

import org.junit.Assert
import org.junit.Test

class DFMP2PConflictTypeTest {

    @Test
    fun should_return_boolean_when_isMobile() {
        Assert.assertTrue(DFMP2PConflictType.isMobile(DFMP2PConflictType.MOBILE_SELF_STUDY_CONFLICT))
        Assert.assertTrue(DFMP2PConflictType.isMobile(DFMP2PConflictType.MOBILE_WFD_CONFLICT))
        Assert.assertTrue(DFMP2PConflictType.isMobile(DFMP2PConflictType.MOBILE_GOOGLECAST_CONFLICT))
        Assert.assertTrue(DFMP2PConflictType.isMobile(DFMP2PConflictType.MOBILE_HAPPYCAST_CONFLICT))
        Assert.assertTrue(DFMP2PConflictType.isMobile(DFMP2PConflictType.MOBILE_DLNA_CONFLICT))

        Assert.assertFalse(DFMP2PConflictType.isMobile(DFMP2PConflictType.PAD_CROSS_SCREEN_MIRROR_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isMobile(DFMP2PConflictType.PAD_APP_REPLY_CONFLICT))

        Assert.assertFalse(DFMP2PConflictType.isMobile(DFMP2PConflictType.PC_ONE_APP_REPLY_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isMobile(DFMP2PConflictType.PC_ONE_APP_REPLY_ONE_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isMobile(DFMP2PConflictType.PC_ONE_APP_REPLY_TWO_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isMobile(DFMP2PConflictType.PC_TWO_APP_REPLY_ONE_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isMobile(DFMP2PConflictType.PC_TWO_APP_REPLY_TWO_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isMobile(DFMP2PConflictType.PC_THREE_APP_REPLY_ONE_CONFLICT))

        Assert.assertFalse(DFMP2PConflictType.isMobile(DFMP2PConflictType.UNDEFINED_CONFLICT))
    }

    @Test
    fun should_return_boolean_when_isPad() {
        Assert.assertFalse(DFMP2PConflictType.isPad(DFMP2PConflictType.MOBILE_SELF_STUDY_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPad(DFMP2PConflictType.MOBILE_WFD_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPad(DFMP2PConflictType.MOBILE_GOOGLECAST_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPad(DFMP2PConflictType.MOBILE_HAPPYCAST_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPad(DFMP2PConflictType.MOBILE_DLNA_CONFLICT))

        Assert.assertTrue(DFMP2PConflictType.isPad(DFMP2PConflictType.PAD_CROSS_SCREEN_MIRROR_CONFLICT))
        Assert.assertTrue(DFMP2PConflictType.isPad(DFMP2PConflictType.PAD_APP_REPLY_CONFLICT))

        Assert.assertFalse(DFMP2PConflictType.isPad(DFMP2PConflictType.PC_ONE_APP_REPLY_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPad(DFMP2PConflictType.PC_ONE_APP_REPLY_ONE_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPad(DFMP2PConflictType.PC_ONE_APP_REPLY_TWO_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPad(DFMP2PConflictType.PC_TWO_APP_REPLY_ONE_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPad(DFMP2PConflictType.PC_TWO_APP_REPLY_TWO_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPad(DFMP2PConflictType.PC_THREE_APP_REPLY_ONE_CONFLICT))

        Assert.assertFalse(DFMP2PConflictType.isMobile(DFMP2PConflictType.UNDEFINED_CONFLICT))
    }

    @Test
    fun should_return_boolean_when_isPC() {
        Assert.assertFalse(DFMP2PConflictType.isPC(DFMP2PConflictType.MOBILE_SELF_STUDY_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPC(DFMP2PConflictType.MOBILE_WFD_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPC(DFMP2PConflictType.MOBILE_GOOGLECAST_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPC(DFMP2PConflictType.MOBILE_HAPPYCAST_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPC(DFMP2PConflictType.MOBILE_DLNA_CONFLICT))

        Assert.assertFalse(DFMP2PConflictType.isPC(DFMP2PConflictType.PAD_CROSS_SCREEN_MIRROR_CONFLICT))
        Assert.assertFalse(DFMP2PConflictType.isPC(DFMP2PConflictType.PAD_APP_REPLY_CONFLICT))

        Assert.assertTrue(DFMP2PConflictType.isPC(DFMP2PConflictType.PC_ONE_APP_REPLY_CONFLICT))
        Assert.assertTrue(DFMP2PConflictType.isPC(DFMP2PConflictType.PC_ONE_APP_REPLY_ONE_CONFLICT))
        Assert.assertTrue(DFMP2PConflictType.isPC(DFMP2PConflictType.PC_ONE_APP_REPLY_TWO_CONFLICT))
        Assert.assertTrue(DFMP2PConflictType.isPC(DFMP2PConflictType.PC_TWO_APP_REPLY_ONE_CONFLICT))
        Assert.assertTrue(DFMP2PConflictType.isPC(DFMP2PConflictType.PC_TWO_APP_REPLY_TWO_CONFLICT))
        Assert.assertTrue(DFMP2PConflictType.isPC(DFMP2PConflictType.PC_THREE_APP_REPLY_ONE_CONFLICT))

        Assert.assertFalse(DFMP2PConflictType.isPC(400))
        Assert.assertFalse(DFMP2PConflictType.isMobile(DFMP2PConflictType.UNDEFINED_CONFLICT))
    }
}