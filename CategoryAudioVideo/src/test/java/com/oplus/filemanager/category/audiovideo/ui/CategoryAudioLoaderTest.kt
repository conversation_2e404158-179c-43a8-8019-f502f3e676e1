/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CategoryAudioLoaderTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/27
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  hank.zhou      2022/9/27      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.category.audiovideo.ui

import android.content.Context
import android.net.Uri
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.BlacklistParser
import com.filemanager.common.wrapper.AudioFileWrapper
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class CategoryAudioLoaderTest {

    lateinit var mCategoryAudioLoader: CategoryAudioLoader
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        val context = mockk<Context>(relaxed = true)
        mockkObject(MyApplication)
        every { MyApplication.sAppContext }.returns(context)
        mockkStatic(SortModeUtils::class)
        every {
            SortModeUtils.getSharedSortMode(context, SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_AUDIO))
        }.returns(SortHelper.FILE_NAME_ORDER)
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getInternalSdPath(context) }.returns("test")
        every { VolumeEnvironment.getExternalSdPath(context) }.returns("test")
        mockkStatic(BlacklistParser::class)
        every { BlacklistParser.getFilterSizeByType(CategoryHelper.CATEGORY_AUDIO) }.returns(0)
        val uri = mockk<Uri>(relaxed = true)
        mCategoryAudioLoader = CategoryAudioLoader(context, uri, null, CategoryHelper.CATEGORY_AUDIO, false)
    }

    @Test
    fun testPreHandleResultBackground() {
        val file1 = AudioFileWrapper()
        file1.mDisplayName = "test1"
        val file2 = AudioFileWrapper()
        file2.mDisplayName = "test2"
        val list = ArrayList<AudioFileWrapper>()
        list.add(file1)
        list.add(file2)
        every { SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_AUDIO)) }.returns(false)
        val listResult = mCategoryAudioLoader.preHandleResultBackground(list)
        Assert.assertEquals(listResult.size, 2)
        Assert.assertEquals(listResult[0].mDisplayName, "test2")
        every { SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_AUDIO)) }.returns(true)
        val listResult2 = mCategoryAudioLoader.preHandleResultBackground(list)
        Assert.assertEquals(listResult2.size, 2)
        Assert.assertEquals(listResult2[0].mDisplayName, "test1")
    }

    @After
    fun tearDown() {
        unmockkObject(MyApplication)
        unmockkStatic(SortModeUtils::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(BlacklistParser::class)
    }
}