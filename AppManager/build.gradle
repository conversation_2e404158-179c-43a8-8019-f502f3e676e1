plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace "com.oplus.filemanager.appmanager"

    sourceSets {
        main {
            res.srcDirs += ['res']
        }
    }
}

dependencies {
    implementation libs.koin.android

    implementation project(':Common')
    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.reddot
}