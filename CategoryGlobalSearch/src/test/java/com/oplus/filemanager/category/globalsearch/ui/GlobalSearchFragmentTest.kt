/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GlobalSearchFragmentTest
 ** Description : GlobalSearchFragment Unit Test
 ** Version     : 1.0
 ** Date        : 2023/2/21
 ** Author      : wanghonglei
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  wanghonglei     2023/2/21      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui

import io.mockk.every
import io.mockk.mockk
import org.junit.Test

class GlobalSearchFragmentTest {

    @Test
    fun hideKeyboardByListModelTest() {
        val globalSearchFragment = mockk<GlobalSearchFragment>(relaxed = true)
        every { globalSearchFragment.mLastListMode = 1 }
        every { globalSearchFragment.hideKeyboardByListModel(2) }.answers { callOriginal() }
        globalSearchFragment.hideKeyboardByListModel(2)
    }
}
