package com.filemanager.common.view.fastscrolll

import android.view.MotionEvent
import android.view.View
import com.filemanager.common.utils.VibratorUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * FullScreenFastScroller的单元测试类
 * 用于测试FullScreenFastScroller的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(maxSdk = 33)  // 添加此注解解决targetSdkVersion问题
class FullScreenFastScrollerTest {

    // 待测试的FullScreenFastScroller实例
    private lateinit var scroller: FullScreenFastScroller
    // 测试上下文环境
    private val context = RuntimeEnvironment.getApplication()

    /**
     * 测试前的初始化方法
     * 在每个测试方法执行前调用
     */
    @Before
    fun setup() {
        // 创建FullScreenFastScroller实例
        scroller = FullScreenFastScroller(context)
        // 模拟VibratorUtil对象，用于测试震动相关功能
        mockkObject(VibratorUtil)
    }

    /**
     * 测试后的清理方法
     * 在每个测试方法执行后调用
     */
    @After
    fun tearDown() {
        // 移除对私有方法的调用
    }

    /**
     * 测试setVisibilityWithoutAnimation方法
     * 验证不带动画的可见性设置功能
     */
    @Test
    fun testSetVisibilityWithoutAnimation() {
        // 调用不带动画的可见性设置方法
        scroller.setVisibilityWithoutAnimation(View.VISIBLE)
        // 验证视图可见性是否正确设置
        assertEquals(View.VISIBLE, scroller.visibility)
        // 验证透明度是否正确设置
        assertEquals(1f, scroller.alpha)
    }
}