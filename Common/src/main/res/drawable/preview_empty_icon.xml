<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="280dp"
    android:height="200dp"
    android:viewportWidth="280"
    android:viewportHeight="200">
  <path
      android:pathData="M91.76,73.38L96.38,73.86L99.33,73.56"
      android:strokeLineJoin="round"
      android:strokeWidth="0.471367"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M91.37,74.65L96.09,74.98L99.33,74.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.471367"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.14,72.85C99.46,73.81 99.26,74.55 99.25,76.27"
      android:strokeLineJoin="round"
      android:strokeWidth="0.471367"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M100.44,71.56C101.97,72.15 103.59,72.44 105.22,72.42"
      android:strokeLineJoin="round"
      android:strokeWidth="0.471367"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.55,76.28C100.86,75.35 102.4,74.82 103.99,74.73"
      android:strokeLineJoin="round"
      android:strokeWidth="0.471367"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.41,76.27L98.56,76.46"
      android:strokeLineJoin="round"
      android:strokeWidth="0.471367"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M73.05,150.22L68.46,180.76H74.22L82.24,150.22"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M179.65,154.32L178.56,150.17M181.09,159.82L185.66,177.24M188.31,153.91L187.75,150.17M189.2,159.83L189.42,161.25"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M187.58,92.65H189.19M153.56,92.65H68.4C67.1,92.65 65.8,92.93 64.62,93.47C63.43,94.02 62.37,94.81 61.52,95.8C60.66,96.79 60.03,97.96 59.67,99.21C59.31,100.47 59.22,101.79 59.42,103.08L59.44,103.24"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M190.62,107.43C190.36,108.03 190.18,108.68 190.11,109.35L187.24,135.55H72.98L70.06,109.3C69.88,107.67 69.11,106.15 67.88,105.06C66.66,103.96 65.07,103.35 63.42,103.35H59.58C58.57,103.34 57.58,103.57 56.67,104C55.76,104.44 54.96,105.08 54.34,105.87C53.71,106.66 53.27,107.58 53.05,108.56C52.83,109.54 52.83,110.56 53.06,111.54L60.54,144.98C60.92,146.64 61.91,148.09 63.31,149.04C64.72,149.99 66.43,150.37 68.11,150.1C68.41,150.14 68.71,150.16 69.02,150.16H191.16C191.47,150.16 191.77,150.14 192.08,150.1C192.41,150.16 192.76,150.19 193.1,150.19C194.62,150.18 196.09,149.67 197.28,148.72C198.46,147.77 199.29,146.44 199.63,144.96L207.11,111.54C207.33,110.57 207.33,109.55 207.1,108.58C206.88,107.6 206.44,106.68 205.81,105.9C205.59,105.63 205.35,105.37 205.09,105.13"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.85,80.69C134.62,80.68 134.4,80.66 134.17,80.62L134.85,80.69Z"
      android:fillColor="#EBD196"/>
  <path
      android:pathData="M150.35,102.57H147.49C141.74,102.57 137.08,107.23 137.08,112.98V125.14C137.08,130.89 141.74,135.55 147.49,135.55H177.3C183.05,135.55 187.71,130.89 187.71,125.14V112.98C187.71,110.36 186.75,107.97 185.15,106.14"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.78,116.71L96.8,126.22C96.8,126.22 91.27,129.37 89.41,130.55C87.93,131.49 84.83,132.95 84.83,132.95L82.5,133.2C82.2,133.23 81.92,133.37 81.72,133.6C81.52,133.82 81.41,134.11 81.41,134.42C81.4,134.58 81.44,134.74 81.5,134.89C81.56,135.04 81.65,135.18 81.77,135.3C81.89,135.41 82.03,135.5 82.18,135.56C82.33,135.62 82.49,135.65 82.66,135.65L90.59,135.42L104.2,135.51C104.62,135.52 105.04,135.43 105.43,135.25C105.82,135.07 106.16,134.81 106.43,134.48C106.7,134.15 106.9,133.77 107,133.35C107.11,132.94 107.12,132.51 107.03,132.09L106.6,126.97L112.19,117.38L102.78,116.71Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.58,122.92L100.86,118.62C100.3,118.35 99.62,118.58 99.35,119.14L98.23,121.41C97.95,121.97 98.18,122.64 98.74,122.92L107.46,127.22C108.02,127.49 108.7,127.26 108.97,126.7L110.09,124.43C110.36,123.88 110.14,123.2 109.58,122.92Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M118.38,81.25C118.16,81.56 117.97,81.89 117.79,82.24L117.82,82.23L99.14,113.88C96.52,118.1 99.06,120.03 99.91,120.36L109.63,124.4C109.87,124.62 110.16,124.79 110.47,124.9C110.79,125.01 111.12,125.06 111.45,125.04C111.78,125.03 112.11,124.94 112.41,124.8C112.71,124.65 112.97,124.45 113.19,124.2C116.02,120.5 124.33,106.84 127.14,100.01C129.51,94.19 131.39,90.02 131.39,90.02C131.44,89.95 131.48,89.88 131.52,89.8C131.54,89.75 131.57,89.71 131.6,89.66C132.12,88.75 132.46,87.74 132.59,86.7C132.72,85.66 132.64,84.6 132.35,83.59C132.23,83.17 132.08,82.76 131.89,82.37C129.45,82.21 126.88,82 124.68,81.81C122.52,81.64 120.72,81.49 119.78,81.45C119.26,81.42 118.79,81.36 118.38,81.25Z"
      android:fillColor="#5966A9"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M100.23,119.91L109.63,124.39"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#595050"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M98.33,165.11L92.18,176.08C92.18,176.08 87.68,179.4 85.82,180.58C84.32,181.51 81.18,183.01 81.18,183.01L78.86,183.25C78.55,183.29 78.28,183.43 78.07,183.65C77.87,183.88 77.75,184.17 77.75,184.47C77.75,184.64 77.78,184.8 77.85,184.95C77.91,185.1 78,185.24 78.12,185.35C78.24,185.47 78.38,185.56 78.53,185.62C78.68,185.68 78.85,185.71 79.01,185.7L86.94,185.48L100.55,185.57C100.97,185.57 101.4,185.48 101.78,185.3C102.17,185.12 102.51,184.86 102.78,184.53C103.05,184.2 103.24,183.82 103.35,183.41C103.45,182.99 103.46,182.56 103.38,182.15L101.8,177.26L106.92,167.63L98.33,165.11Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M160.22,135.97C159.71,136.01 159.21,136.01 158.7,135.97L121,136.27C119.02,136.26 117.11,135.5 115.66,134.14C114.21,132.78 113.33,130.92 113.2,128.93C113.07,126.95 113.7,124.99 114.96,123.46C116.22,121.92 118.01,120.91 119.98,120.64L156.87,115.71C157.69,115.44 158.55,115.29 159.42,115.26C161.95,115.38 164.36,116.41 166.18,118.17C168.01,119.93 169.13,122.29 169.34,124.82C169.55,127.35 168.83,129.87 167.31,131.9C165.8,133.93 163.59,135.35 161.11,135.87C160.78,135.93 160.5,135.96 160.22,135.97Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M172.29,117.74L169.04,129.32L136.53,130.75L148.56,110.79L172.29,117.74Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M118.69,81.32C118.4,81.69 118.15,82.09 117.94,82.52C117.02,84.32 116.84,86.4 117.43,88.33L129.79,125.77C129.89,126.29 130.01,126.8 130.17,127.31C130.26,127.59 130.37,127.87 130.48,128.13C130.48,128.13 134.47,136.9 143.25,134.01C148.7,132.19 153.3,125.76 151.54,120.29C151.26,119.42 150.87,118.6 150.38,117.84L132.2,83.14C132.1,82.87 131.98,82.61 131.84,82.36C129.41,82.2 126.86,81.99 124.68,81.81C122.52,81.63 120.73,81.49 119.78,81.45C119.39,81.43 119.02,81.39 118.69,81.32Z"
      android:fillColor="#5966A9"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M113.71,124.85L92.8,164.93C92.56,165.39 92.42,165.89 92.38,166.41C92.34,166.92 92.4,167.44 92.56,167.93C92.73,168.42 92.98,168.87 93.32,169.26C93.67,169.65 94.08,169.96 94.54,170.19L105.25,175.01C106.17,175.44 107.22,175.5 108.18,175.17C109.14,174.84 109.93,174.13 110.38,173.22C113.97,165.9 121.79,150.22 124.22,143.97C125.58,140.42 127.14,136.95 128.9,133.59C128.9,133.59 131.1,124.02 124.27,121.41C120.19,119.88 115.66,120.99 113.71,124.85Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M127.24,119.7L150.09,117.35"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#595050"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.22,172.49L95.34,168.51C94.78,168.25 94.11,168.51 93.86,169.07L92.82,171.38C92.57,171.95 92.82,172.62 93.39,172.87L102.26,176.85C102.83,177.11 103.5,176.85 103.75,176.29L104.79,173.98C105.04,173.41 104.79,172.74 104.22,172.49Z"
      android:fillColor="#5966A9"/>
  <path
      android:pathData="M94.66,169.58L103.69,174.13"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#595050"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M149.93,107.32C149.5,108.55 149.04,109.72 148.56,110.79L177.81,119.31C177.81,119.31 178.5,117.32 179.51,114.3"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M158.03,79.69C158.03,79.69 143.54,82.42 138.67,82.59C133.79,82.75 122.7,81.56 119.78,81.45C117.27,81.33 116.07,80.23 116.05,78.38L115.92,68.94L137.59,69.47C142.82,68.78 154.86,65.03 160.05,64.8C163.52,64.64 165.71,65.14 166.98,65.64M159.76,75.26C156.29,83.74 149,107.03 149,107.03L154.82,108.55C154.82,108.55 158.63,92.86 162.91,79.96C163.46,78.36 164.04,76.76 164.65,75.18"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M156.58,65.25C156.25,70.03 156.78,74.83 158.15,79.42"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M167.59,64.83C166.57,66.11 165.8,67.58 165.33,69.16C164.9,71.18 164.65,73.24 164.6,75.31C164.6,75.31 167.72,71.31 170.03,68.75C170.38,68.39 170.75,68.03 171.13,67.69"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.14,52.2C170.79,50.55 171.98,49.18 173.54,48.31C175.23,47.37 177.22,47.12 179.1,47.59L182.46,48.54V48.63C182.92,48.7 183.36,48.81 183.79,48.96C185.1,49.43 186.25,50.23 187.14,51.26C187.19,50.61 186.89,50.12 186.52,49.53C186.19,48.99 185.81,48.36 185.57,47.43C185.46,46.97 185.37,46.51 185.31,46.04C185.25,45.68 185.31,45.3 185.48,44.97C185.07,45.06 184.71,45.29 184.45,45.6C184.3,45.85 184.2,46.13 184.15,46.42C184.11,46.13 183.84,45.77 183.64,45.5C183.59,45.42 183.54,45.35 183.5,45.3C183.15,44.83 182.74,44.41 182.28,44.06C181.34,43.32 180.21,42.88 179.03,42.78C179.1,43.04 179.21,43.3 179.31,43.55C179.41,43.78 179.51,44.01 179.57,44.25C179.37,43.89 179.07,43.58 178.7,43.37C178.34,43.16 177.92,43.06 177.5,43.06C176.68,43.05 175.88,43.23 175.15,43.61C174.85,43.74 174.55,43.88 174.26,44.03C173.82,44.24 173.38,44.46 172.92,44.63C172.16,44.93 171.31,44.96 170.52,44.71C170.46,45.05 170.49,45.39 170.61,45.71C170.73,46.03 170.93,46.31 171.19,46.53C169.78,47.55 169.19,49.58 170.14,52.2Z"
      android:fillColor="#323739"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M185.47,63.26C186.96,62.29 188.08,60.86 188.65,59.18V59.09C188.71,58.96 188.78,58.82 188.78,58.68C189.33,56.73 189.13,54.64 188.2,52.84C187.28,51.03 185.7,49.65 183.8,48.96C183.36,48.81 182.92,48.7 182.46,48.63V48.54L179.1,47.59C177.23,47.12 175.24,47.37 173.54,48.31C171.88,49.24 170.63,50.74 170.02,52.54M169.37,54.27C168.45,55.02 167.25,55.93 166.92,56.14C166.57,56.35 166.51,56.78 166.83,56.97C167.32,57.28 167.76,57.67 168.14,58.11C167.66,59.49 167.26,60.47 166.76,61.73C166.61,62.09 166.54,62.47 166.56,62.85C166.58,63.23 166.68,63.6 166.85,63.94C167.02,64.28 167.27,64.58 167.57,64.81C167.87,65.05 168.22,65.21 168.59,65.3C169.36,65.5 170.11,65.67 170.85,65.81L171.62,70.37C171.51,70.52 171.44,70.69 171.41,70.86C171.38,71.04 171.39,71.22 171.44,71.4C171.66,71.97 172.45,72.29 173.64,72.22"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.74,50.76L170.74,50.76L170.74,50.76C170.72,50.78 170.7,50.8 170.68,50.82C169.47,48.77 171.75,46.88 173.98,46.13C180.94,43.64 186.28,47.04 187.3,50.58C192.04,55.45 188.82,60.79 187.61,62.16C186.41,63.5 185,64.46 183.77,65.02C183.49,64.82 183.19,64.64 182.87,64.5C182.27,64.24 181.62,64.09 180.96,64.08C180.3,64.07 179.65,64.19 179.04,64.43C179.02,64.44 178.99,64.44 178.97,64.45C178.7,64.22 178.48,63.92 178.33,63.59C177.99,62.96 177.7,62.29 177.48,61.6C177.49,61.67 177.48,61.73 177.45,61.79C177.43,61.85 177.38,61.9 177.32,61.93C177.03,62.06 176.42,61.22 176.18,60.71C176.16,60.65 176.13,60.45 176.12,60.17C176.47,60.03 176.76,59.85 176.82,59.82C177.16,59.63 177.53,59.34 177.84,58.99V60.45L178.65,60.42V57.49C178.68,57.33 178.68,57.18 178.65,57.04V56.97L178.64,56.96C178.62,56.89 178.59,56.81 178.56,56.74C178.35,56.38 178.03,56.09 177.64,55.92C177.25,55.75 176.82,55.71 176.41,55.81L176.38,55.82L174.82,55.02C175.23,53.56 175.86,52.16 176.71,50.89C177.11,50.49 177.61,50.21 178.16,50.06C177.68,50 177.2,50.03 176.74,50.15C176.54,50.24 176.33,50.29 176.11,50.29L175.87,50.24C175.53,50.14 175.19,50.07 174.84,50.02C173.49,49.83 172.11,50.04 170.88,50.63C170.83,50.67 170.78,50.71 170.74,50.76L170.74,50.76L170.74,50.76ZM174.69,56.05L175.45,56.42C175.28,56.6 175.13,56.82 175,57.04C174.82,56.87 174.72,56.44 174.69,56.05Z"
      android:fillColor="#323739"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M167.88,59.22C168.35,59.43 168.84,59.55 169.35,59.58C169.85,59.62 170.36,59.57 170.84,59.45"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M170.84,51.85C171.42,51.93 171.97,52.16 172.43,52.52C172.76,52.78 173.03,53.1 173.24,53.45"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M178.65,56.97V60.42L177.84,60.45V59.34V59.24V59.15M176.35,55.8L169.52,52.34C169.45,52.31 169.38,52.29 169.3,52.3C169.22,52.31 169.15,52.35 169.09,52.4C169.04,52.45 168.99,52.51 168.97,52.58C168.95,52.66 168.95,52.74 168.97,52.81L170.11,56.44C170.13,56.53 170.19,56.61 170.27,56.66C170.36,56.71 170.45,56.73 170.55,56.71L174.65,56.03L175.26,56.33"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M177.62,57.71C177.54,57.53 177.41,57.38 177.24,57.3C177.07,57.21 176.87,57.18 176.68,57.22C176.3,57.31 175.95,57.5 175.66,57.76"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M209.42,127.61H207.72C207.62,127.61 207.53,127.57 207.46,127.5C207.39,127.43 207.35,127.33 207.34,127.23L207.62,124.49C207.62,124.39 207.82,124.3 207.91,124.3H209.14C209.33,124.3 209.42,124.39 209.42,124.49L209.71,127.14C209.9,127.43 209.71,127.61 209.42,127.61Z"
      android:fillColor="#595050"/>
  <path
      android:pathData="M208.86,124.2H208.24C208.2,124.2 208.15,124.18 208.11,124.14C208.08,124.11 208.06,124.06 208.05,124.01L208.24,122.55C208.24,122.26 208.63,122.26 208.72,122.55L209,124.07C209.04,124.11 208.95,124.2 208.86,124.2Z"
      android:fillColor="#595050"/>
  <path
      android:pathData="M208.86,124.6H208.25C208.05,124.6 207.96,124.51 207.96,124.32C207.95,124.28 207.96,124.24 207.97,124.21C207.98,124.17 208,124.13 208.03,124.1C208.06,124.08 208.09,124.06 208.13,124.04C208.17,124.03 208.21,124.03 208.25,124.04H208.91C209.1,124.04 209.19,124.13 209.19,124.32C209.19,124.52 209.05,124.49 208.86,124.6Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M211.99,152.93L208.1,152.46C207.72,152.36 207.34,151.98 207.44,151.6L211.8,117.95C211.89,117.57 212.27,117.19 212.65,117.28L216.54,117.75C216.91,117.85 217.29,118.23 217.2,118.61L212.84,152.27C212.84,152.37 212.82,152.48 212.77,152.57C212.73,152.67 212.66,152.75 212.58,152.82C212.5,152.88 212.4,152.93 212.3,152.95C212.19,152.97 212.09,152.96 211.99,152.93Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M208.24,145.44V145.65C208.34,145.65 208.43,145.57 208.44,145.47L208.24,145.44ZM210.04,131.54L210.24,131.57C210.24,131.56 210.24,131.55 210.24,131.54L210.04,131.54ZM210,127.81L210.2,127.81C210.2,127.8 210.2,127.79 210.2,127.79L210,127.81ZM209.81,127.46L209.68,127.61L209.81,127.46ZM209.42,127.34V127.54C209.43,127.54 209.43,127.54 209.44,127.54L209.42,127.34ZM207.53,127.34V127.13C207.52,127.13 207.51,127.13 207.51,127.13L207.53,127.34ZM207.18,127.52L207.33,127.65L207.18,127.52ZM207.06,127.9L207.26,127.9C207.26,127.89 207.26,127.89 207.26,127.88L207.06,127.9ZM207.24,144.97L207.04,144.97C207.04,144.98 207.04,144.99 207.04,144.99L207.24,144.97ZM207.82,145.44V145.24C207.81,145.24 207.8,145.24 207.8,145.24L207.82,145.44ZM208.44,145.47L210.24,131.57L209.84,131.52L208.03,145.42L208.44,145.47ZM209.79,127.81L209.83,131.55L210.24,131.54L210.2,127.81L209.79,127.81ZM209.68,127.61C209.74,127.67 209.78,127.75 209.79,127.83L210.2,127.79C210.18,127.59 210.09,127.42 209.94,127.3L209.68,127.61ZM209.44,127.54C209.53,127.53 209.61,127.56 209.68,127.61L209.94,127.3C209.79,127.18 209.6,127.12 209.41,127.13L209.44,127.54ZM207.53,127.54H209.42V127.13H207.53V127.54ZM207.33,127.65C207.39,127.59 207.46,127.55 207.55,127.54L207.51,127.13C207.32,127.15 207.14,127.24 207.02,127.39L207.33,127.65ZM207.26,127.88C207.25,127.8 207.28,127.72 207.33,127.65L207.02,127.39C206.9,127.54 206.84,127.73 206.85,127.92L207.26,127.88ZM207.45,144.97L207.26,127.9L206.85,127.9L207.04,144.97L207.45,144.97ZM207.56,145.16C207.5,145.11 207.45,145.03 207.45,144.95L207.04,144.99C207.06,145.18 207.15,145.36 207.3,145.48L207.56,145.16ZM207.8,145.24C207.71,145.25 207.63,145.22 207.56,145.16L207.3,145.48C207.45,145.6 207.64,145.66 207.83,145.65L207.8,145.24ZM208.24,145.24H207.82V145.65H208.24V145.24Z"
      android:fillColor="#808080"/>
  <path
      android:pathData="M227.7,153.95H181.1C179.62,153.95 178.43,155.14 178.43,156.62V157.12C178.43,158.6 179.62,159.8 181.1,159.8H227.7C229.18,159.8 230.38,158.6 230.38,157.12V156.62C230.38,155.14 229.18,153.95 227.7,153.95Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M217.28,153.95H204.89C204.87,153.93 204.85,153.91 204.83,153.89C204.57,153.62 204.41,153.27 204.4,152.89V135.01C204.41,134.64 204.57,134.28 204.83,134.01C205.1,133.75 205.45,133.6 205.83,133.59H216.35C216.72,133.6 217.08,133.75 217.34,134.02C217.61,134.28 217.76,134.64 217.76,135.01V152.93C217.75,153.3 217.59,153.65 217.33,153.9C217.31,153.92 217.3,153.93 217.28,153.95Z"
      android:fillColor="#5966A9"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M189.78,159.8V159.59C189.68,159.59 189.6,159.66 189.58,159.75L189.78,159.8ZM219.15,159.8H219.35C219.35,159.68 219.26,159.59 219.15,159.59V159.8ZM219.15,180.69V180.9C219.26,180.9 219.35,180.8 219.35,180.69H219.15ZM184.85,180.69L184.65,180.65C184.64,180.71 184.65,180.77 184.69,180.82C184.73,180.87 184.79,180.9 184.85,180.9V180.69ZM189.78,160H219.15V159.59H189.78V160ZM218.94,159.8V180.69H219.35V159.8H218.94ZM219.15,180.49H184.85V180.9H219.15V180.49ZM185.05,180.74L189.98,159.85L189.58,159.75L184.65,180.65L185.05,180.74Z"
      android:fillColor="#808080"/>
  <path
      android:pathData="M172.89,76.09C172.89,76.09 168.88,90.07 167.89,94.94C167.45,97.14 166.4,100.52 165.38,103.57M164.37,106.5C163.58,108.79 162.97,110.42 162.97,110.42L165.94,111.11M167.8,111.54L167.26,111.42M168.76,111.77L183.47,115.2L185.33,104.88M187.98,90.23L187.19,94.59"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M190.24,94.61H197.41"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M165.91,110.42L165.34,105.92L167.89,103.11"
      android:strokeLineJoin="round"
      android:strokeWidth="0.471367"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M167.7,106.01L169.43,103.91"
      android:strokeLineJoin="round"
      android:strokeWidth="0.471367"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M179.32,95.77L175.28,95.93C174.75,95.89 174.21,95.98 173.71,96.17C173.21,96.37 172.76,96.67 172.39,97.06C170.17,98.92 164.32,104.61 164.32,104.61C164.21,104.71 164.13,104.83 164.09,104.98C164.05,105.12 164.06,105.27 164.1,105.41L165.51,110.25C165.53,110.35 165.58,110.43 165.65,110.5C165.72,110.57 165.8,110.62 165.9,110.65L165.98,111.27C166,111.43 166.09,111.57 166.21,111.68C166.33,111.78 166.49,111.84 166.65,111.83C166.82,111.82 166.98,111.74 167.1,111.62C167.21,111.49 167.27,111.32 167.26,111.15L167.03,106.54L167.71,106.01L167.47,106.32C167.42,106.39 167.38,106.47 167.35,106.56C167.33,106.64 167.32,106.73 167.33,106.82L167.76,111.38C167.77,111.53 167.84,111.66 167.95,111.75C168.06,111.85 168.2,111.9 168.34,111.9C168.5,111.9 168.65,111.84 168.75,111.74C168.86,111.63 168.93,111.48 168.93,111.33V107.14L170.66,105.47L169.63,106.95C169.56,107.06 169.52,107.18 169.51,107.3V110.46C169.51,110.6 169.56,110.74 169.66,110.84C169.75,110.95 169.88,111.02 170.02,111.03C170.17,111.05 170.32,111 170.44,110.91C170.55,110.81 170.62,110.68 170.64,110.53L170.93,107.62L172.76,105.36C174.07,104.77 175.23,103.92 176.17,102.85L180.75,103.22"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M179.33,95.91L180.97,104.04C181.01,104.21 181.1,104.36 181.23,104.46C181.36,104.57 181.53,104.62 181.7,104.62L185.2,104.53L182.28,94.98L180.01,95.05C179.9,95.05 179.8,95.08 179.7,95.12C179.61,95.17 179.53,95.24 179.46,95.32C179.4,95.4 179.35,95.5 179.33,95.6C179.31,95.7 179.31,95.81 179.33,95.91Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M184.2,82.86L190.24,94.61H182.26L185.04,104.11C185.32,105.02 185.87,105.82 186.62,106.4C187.37,106.98 188.29,107.31 189.24,107.34C193.34,107.49 200.03,107.66 202.07,107.34C205.06,106.83 207.11,102.02 206.33,99.49C205.55,96.97 196.78,72.7 191.35,68.75C189.35,67.31 187.23,67.09 185.53,67.26"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M190.24,94.61H197.41"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M172.9,76.09C172.9,76.09 174.01,66.11 179.04,64.43C179.65,64.19 180.31,64.07 180.96,64.08C181.62,64.09 182.27,64.24 182.87,64.5C183.47,64.77 184.01,65.15 184.46,65.63C184.91,66.11 185.26,66.67 185.49,67.29C183.31,67.81 181.25,68.76 179.44,70.09C177.15,71.96 174.96,73.97 172.9,76.09Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M184.68,83.68C186.51,78.68 191,74.77 195.54,74.77"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.43,80.17L113.22,79.92C113.08,79.9 112.95,79.83 112.86,79.72C112.76,79.61 112.7,79.47 112.69,79.33L112.15,70.11C112.14,69.93 112.2,69.77 112.32,69.64C112.43,69.51 112.59,69.43 112.76,69.42L115.86,69.27"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.14,99.98L123.64,107.13"
      android:strokeLineJoin="round"
      android:strokeWidth="0.409884"
      android:fillColor="#00000000"
      android:strokeColor="#595050"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.13,71.16L108.62,71.24C108.46,71.14 107.74,68.27 101.51,69.54C99.76,69.9 97.3,69.9 97.02,70.11C96.75,70.31 96.83,71.13 97.55,71.46C97.93,71.61 99.71,71.46 100.46,71.56L100.21,72.29L97.14,72.66L92.24,72.39C92.09,72.38 91.95,72.43 91.84,72.52C91.72,72.61 91.65,72.74 91.62,72.88C91.61,72.97 91.62,73.06 91.65,73.15C91.68,73.24 91.73,73.31 91.8,73.37H91.38C91.21,73.35 91.04,73.4 90.9,73.5C90.76,73.6 90.67,73.76 90.65,73.93C90.63,74.1 90.67,74.27 90.78,74.41C90.88,74.54 91.03,74.63 91.2,74.66H91.41C91.25,74.66 91.1,74.72 90.99,74.83C90.88,74.94 90.81,75.09 90.81,75.25C90.81,75.41 90.88,75.56 90.99,75.67C91.1,75.78 91.25,75.84 91.41,75.84L96.53,76.51L98.6,76.44L97.13,76.76L93.4,76.29C93.25,76.28 93.11,76.31 92.99,76.4C92.88,76.49 92.8,76.61 92.77,76.76C92.76,76.83 92.76,76.9 92.77,76.98C92.79,77.05 92.82,77.12 92.86,77.18C92.9,77.24 92.95,77.29 93.02,77.33C93.08,77.37 93.15,77.4 93.22,77.41C93.22,77.41 97.18,78.16 97.24,78.14C97.24,78.14 107.16,76.99 108.99,77.11L112.44,77.57"
      android:strokeLineJoin="round"
      android:strokeWidth="0.471367"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.64,23.44V24.46C106.43,24.32 106.22,24.19 105.99,24.07C105.24,23.71 104.42,23.49 103.59,23.42C102.81,23.36 101.9,23.36 100.8,23.36H57.31C56.2,23.36 55.26,23.36 54.51,23.42C53.67,23.44 52.85,23.67 52.11,24.07C51.89,24.19 51.67,24.32 51.47,24.46V19.68C51.47,18.89 51.62,18.12 51.92,17.39C52.22,16.67 52.65,16.01 53.21,15.45C53.76,14.9 54.42,14.46 55.15,14.16C55.87,13.86 56.65,13.7 57.43,13.7H69.38C70.77,13.73 72.12,14.21 73.21,15.07L77.31,18.44H100.95C102.33,18.5 103.64,19.02 104.68,19.93C105.71,20.84 106.41,22.08 106.64,23.44Z"
      android:fillColor="#8D65AC"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M109.15,29.41C109.15,30.19 109.09,31.16 109.02,32.2V32.27L107.1,57.65V57.77C107.06,58.59 106.97,59.42 106.83,60.24C106.72,60.98 106.48,61.71 106.13,62.38C105.53,63.42 104.62,64.26 103.53,64.78C102.82,65.07 102.08,65.24 101.32,65.3C100.61,65.36 99.76,65.36 98.85,65.36H59.2C58.22,65.36 57.44,65.36 56.73,65.3C55.97,65.25 55.22,65.07 54.51,64.78C53.44,64.24 52.54,63.41 51.92,62.38C51.57,61.71 51.33,60.98 51.2,60.24C51.07,59.42 50.99,58.59 50.95,57.77V57.7L49.13,32.27V32.2C49.06,31.12 49,30.19 49,29.41C48.99,28.57 49.14,27.73 49.45,26.94C49.88,25.99 50.55,25.16 51.4,24.54C51.6,24.39 51.82,24.26 52.05,24.16C52.8,23.79 53.62,23.57 54.45,23.51C55.23,23.44 56.14,23.44 57.25,23.44H100.74C101.83,23.44 102.79,23.44 103.53,23.51C104.36,23.53 105.19,23.75 105.92,24.16C106.13,24.29 106.38,24.41 106.58,24.54C107.46,25.15 108.17,25.98 108.63,26.94C108.99,27.71 109.17,28.56 109.15,29.41Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.614826"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M89.3,42.08H68.81C67.26,42.08 66.01,43.33 66.01,44.87C66.01,46.42 67.26,47.67 68.81,47.67H89.3C90.85,47.67 92.1,46.42 92.1,44.87C92.1,43.33 90.85,42.08 89.3,42.08Z"
      android:fillColor="#B3B0CF"/>
</vector>
