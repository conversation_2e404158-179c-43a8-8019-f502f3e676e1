/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentCardInfo
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/18 16:27
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/18       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.data

import com.filemanager.common.helper.CategoryHelper
import com.oplus.filemanager.cardwidget.util.START_FILE_MANAGER_ACTION

const val ACTION_PREFIX = "nativeapp://"
const val ACTION_RECENT = "${ACTION_PREFIX}$START_FILE_MANAGER_ACTION"
private const val ACTION_DOCUMENT_WITHOUT_PREFIX = "oplus.intent.action.filemanager.FILE_DOCUMENT_VIEW"
const val ACTION_DOCUMENT = "$ACTION_PREFIX$ACTION_DOCUMENT_WITHOUT_PREFIX"
private const val ACTION_MEDIA_VIEW_WITHOUT_PREFIX = "oplus.intent.action.filemanager.FILE_MEDIA_VIEW"
const val ACTION_FILE_MEDIA_VIEW = "$ACTION_PREFIX$ACTION_MEDIA_VIEW_WITHOUT_PREFIX"
private const val ACTION_FILE_GALLERY_VIEW_WITHOUT_PREFIX = "oplus.intent.action.filemanager.FILE_GALLERT_VIEW"
const val ACTION_FILE_GALLERY_VIEW = "$ACTION_PREFIX$ACTION_FILE_GALLERY_VIEW_WITHOUT_PREFIX"
private const val ACTION_FILE_APK_VIEW_WITHOUT_PREFIX = "oplus.intent.action.filemanager.FILE_APK_VIEW"
const val ACTION_FILE_APK_VIEW = "$ACTION_PREFIX$ACTION_FILE_APK_VIEW_WITHOUT_PREFIX"
private const val ACTION_FILE_COMPRESS_VIEW_WITHOUT_PREFIX = "oppo.intent.action.FILE_COMPRESS_VIEW"
const val ACTION_FILE_COMPRESS_VIEW = "$ACTION_PREFIX$ACTION_FILE_COMPRESS_VIEW_WITHOUT_PREFIX"
private const val ACTION_SUPER_APP_WITHOUT_PREFIX = "oplus.intent.action.filemanager.SUPER_APP"
const val ACTION_SUPER_APP = "$ACTION_PREFIX$ACTION_SUPER_APP_WITHOUT_PREFIX"

internal val RECENT_CARD_INFO = mutableMapOf(
    CategoryHelper.CATEGORY_RECENT to ACTION_RECENT,
    CategoryHelper.CATEGORY_DOC to ACTION_DOCUMENT,
    CategoryHelper.CATEGORY_AUDIO to ACTION_FILE_MEDIA_VIEW,
    CategoryHelper.CATEGORY_VIDEO to ACTION_FILE_MEDIA_VIEW,
    CategoryHelper.CATEGORY_IMAGE to ACTION_FILE_GALLERY_VIEW,
    CategoryHelper.CATEGORY_APK to ACTION_FILE_APK_VIEW,
    CategoryHelper.CATEGORY_COMPRESS to ACTION_FILE_COMPRESS_VIEW,
)

internal val RECENT_CARD_INFO_WITHOUT_PREFIX = mutableMapOf(
    CategoryHelper.CATEGORY_RECENT to START_FILE_MANAGER_ACTION,
    CategoryHelper.CATEGORY_DOC to ACTION_DOCUMENT_WITHOUT_PREFIX,
    CategoryHelper.CATEGORY_AUDIO to ACTION_MEDIA_VIEW_WITHOUT_PREFIX,
    CategoryHelper.CATEGORY_VIDEO to ACTION_MEDIA_VIEW_WITHOUT_PREFIX,
    CategoryHelper.CATEGORY_IMAGE to ACTION_FILE_GALLERY_VIEW_WITHOUT_PREFIX,
    CategoryHelper.CATEGORY_APK to ACTION_FILE_APK_VIEW_WITHOUT_PREFIX,
    CategoryHelper.CATEGORY_COMPRESS to ACTION_FILE_COMPRESS_VIEW_WITHOUT_PREFIX,
)

fun getActionByType(type: Int): String {
    return RECENT_CARD_INFO_WITHOUT_PREFIX[type] ?: ACTION_SUPER_APP_WITHOUT_PREFIX
}