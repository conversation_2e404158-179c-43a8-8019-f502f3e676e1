/*
 * ********************************************************************
 *  * * Copyright (C), 2021 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  RecycleBinUtilsTest.java
 *  * * Description : RecycleBinUtilsTest.java
 *  * * Version     : 1.0
 *  * * Date        : 21-12-14 下午5:34
 *  * * Author      : W9010863
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.coloros.filemanager.filerefactor.fileutils

import android.content.ContentResolver
import android.content.ContentUris
import android.content.Context
import android.net.Uri
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.wrapper.MediaFileWrapper
import io.mockk.InternalPlatformDsl
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.spyk
import io.mockk.impl.annotations.MockK
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class UriHelperTest2 {

    @MockK
    lateinit var mContext: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        mContext = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }

        MyApplication.init(mContext)
    }

    @Test
    fun test_getFileUri() {
        mockkObject(MimeTypeHelper.Companion)
        mockkStatic(FeatureCompat::class)
        mockkObject(UriHelper)

        val file = BaseFileBean()
        file.mData = "test.jpg"

        every {
            MimeTypeHelper.getTypeFromPath(any())
        }.returns(MimeTypeHelper.UNKNOWN_TYPE)

        every {
            MimeTypeHelper.getMediaType(any())
        }.returns(MimeTypeHelper.UNKNOWN_TYPE)

        every {
            MimeTypeHelper.getTypeFromDrm(mContext, any(), any())
        }.returns(MimeTypeHelper.VIDEO_TYPE)

        var uri = mockk<Uri>(relaxed = true)
        every { UriHelper.getFileProviderUri(any()) }.returns(uri)
        Assert.assertEquals(uri, UriHelper.getFileUri(file, null, null))

        every {
            MimeTypeHelper.getTypeFromDrm(mContext, any(), any())
        }.returns(MimeTypeHelper.UNKNOWN_TYPE)

        every { FeatureCompat.sIsExpRom }.returns(false)
        Assert.assertEquals(uri, UriHelper.getFileUri(file, null, null))

        every { FeatureCompat.sIsExpRom }.returns(true)
        every { UriHelper["innerGetFileUri"](file, false) }.returns(null)
        every { UriHelper.getFileProviderUri(any()) }.returns(uri)
        Assert.assertNull(UriHelper.getFileUri(file, null, null))
        unmockkObject(MimeTypeHelper.Companion)
        unmockkStatic(FeatureCompat::class)
        unmockkObject(UriHelper)
    }

    @Test
    fun test_getFileUriForDragDrop() {
        mockkObject(MimeTypeHelper.Companion)
        mockkStatic(FeatureCompat::class)
        mockkObject(UriHelper)

        val file = BaseFileBean()
        file.mData = "test.jpg"

        every {
            MimeTypeHelper.getTypeFromPath(any())
        }.returns(MimeTypeHelper.UNKNOWN_TYPE)

        every {
            MimeTypeHelper.getMediaType(any())
        }.returns(MimeTypeHelper.UNKNOWN_TYPE)

        every {
            MimeTypeHelper.getTypeFromDrm(mContext, any(), any())
        }.returns(MimeTypeHelper.VIDEO_TYPE)

        var uri = mockk<Uri>(relaxed = true)
        every { UriHelper["innerGetFileUri"](file, true) }.returns(uri)
        every { UriHelper.getFileProviderUri(any()) }.returns(uri)
        Assert.assertEquals(uri, UriHelper.getFileUriForDragDrop(file, null, null))

        every {
            MimeTypeHelper.getTypeFromDrm(mContext, any(), any())
        }.returns(MimeTypeHelper.AUDIO_TYPE)
        Assert.assertEquals(uri, UriHelper.getFileUriForDragDrop(file, null, null))

        every {
            MimeTypeHelper.getTypeFromDrm(mContext, any(), any())
        }.returns(MimeTypeHelper.UNKNOWN_TYPE)
        every { FeatureCompat.sIsExpRom }.returns(false)
        Assert.assertEquals(uri, UriHelper.getFileUriForDragDrop(file, null, null))

        every { FeatureCompat.sIsExpRom }.returns(true)
        every { UriHelper["innerGetFileUri"](file, false) }.returns(null)
        every { UriHelper.getFileProviderUri(any()) }.returns(null)
        Assert.assertNull(UriHelper.getFileUri(file, null, null))
        unmockkObject(MimeTypeHelper.Companion)
        unmockkStatic(FeatureCompat::class)
        unmockkObject(UriHelper)
    }

    @Test
    fun test_innerGetFileUri() {
        mockkObject(UriHelper)
        mockkObject(MimeTypeHelper.Companion)
        mockkStatic(ContentUris::class)

        var spykMockk = spyk(UriHelper, recordPrivateCalls = true)
        var uri = mockk<Uri>(relaxed = true)
        val file = mockk<MediaFileWrapper>(relaxed = true)
        file.mData = "test"

        every { spykMockk.getFileProviderUri(any()) }.returns(uri)
        Assert.assertEquals(uri, InternalPlatformDsl.dynamicCall(spykMockk,
                "innerGetFileUri", arrayOf(file, false)) { mockk() })

        every { spykMockk.getFileProviderUri(any()) }.returns(null)
        every { file.mLocalFileUri }.returns(uri)
        Assert.assertEquals(uri, InternalPlatformDsl.dynamicCall(spykMockk,
                "innerGetFileUri", arrayOf(file, false)) { mockk() })

        every { file.mLocalFileUri }.returns(null)

        every { MimeTypeHelper.getTypeFromPath(any()) }.returns(MimeTypeHelper.AUDIO_TYPE)
        var cursorMockk = mockk<ContentResolver>(relaxed = true)
        every { mContext.contentResolver }.returns(cursorMockk)
        every { cursorMockk.query(any(), any(), any(), any(), null) }.returns(null)
        every { spykMockk.getFileProviderUri(any()) }.returns(null)
        Assert.assertEquals(null, InternalPlatformDsl.dynamicCall(spykMockk,
                "innerGetFileUri", arrayOf(file, true)) { mockk() })

        every { MimeTypeHelper.getTypeFromPath(any()) }.returns(MimeTypeHelper.VIDEO_TYPE)
        every { cursorMockk.query(any(), any(), any(), any(), null) }.returns(mockk(relaxed = true) {
            every { count }.returns(1)
        })
        every { ContentUris.withAppendedId(any(), any()) }.returns(uri)
        Assert.assertEquals(uri, InternalPlatformDsl.dynamicCall(spykMockk,
                "innerGetFileUri", arrayOf(file, true)) { mockk() })

        try {
            every { cursorMockk.query(any(), any(), any(), any(), null) }.throws(Exception("test-error"))
            Assert.assertEquals(null, InternalPlatformDsl.dynamicCall(spykMockk,
                    "innerGetFileUri", arrayOf(file, true)) { mockk() })
        } catch (e: Exception) {
            println("test!!!")
        }
        unmockkObject(MimeTypeHelper.Companion)
        unmockkStatic(FeatureCompat::class)
        unmockkObject(UriHelper)
    }

    @Test
    fun test_getFileProviderUri() {
        Assert.assertEquals(null, UriHelper.getFileProviderUri(null))
    }
}