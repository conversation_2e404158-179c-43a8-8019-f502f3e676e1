/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/23, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.oplus.filemanager.parentchild.drag

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Rect
import android.view.DragEvent
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.state.COUIMaskRippleDrawable
import com.coui.appcompat.state.COUIMaskRippleDrawable.RIPPLE_TYPE_ICON_RADIUS
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.DefaultDropListener
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.dragselection.FragmentPageDropHelper
import com.filemanager.common.dragselection.MacDragUtil
import com.filemanager.common.dragselection.action.DropDispatchAction
import com.filemanager.common.dragselection.action.DropDispatchAction.NORMAL_STATE
import com.filemanager.common.dragselection.action.DropDispatchAction.PROHIBIT_STATE
import com.filemanager.common.dragselection.action.DropFolderAction
import com.filemanager.common.dragselection.util.DropHandleHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.helper.CategoryHelper.CATEGORY_FOLDER
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import com.filemanager.common.view.SelectDragItemViewParent
import com.filemanager.common.view.SelectItemLayout
import com.filemanager.common.view.SmoothRoundedCornersConstraintLayout
import com.oplus.filemanager.parentchild.view.SideNavigationItemContainer
import com.oplus.filemanager.parentchild.view.SideNavigationStorageContainer

class ItemDragDropHelper(val context: Context) {

    companion object {
        const val TAG = "ItemDragDropHelper"
        const val EDIT_VIEW_ALPHA = 0.26f
        const val DRAGGING_VIEW_ALPHA = 0.85f
        const val DISPERSION_ANIMATION_LIMIT_COUNT = 5
        const val SEND_BROADCAST_TIME = 500
    }

    private val handleHelper = DropHandleHelper()
    private var cornerMarkStateCode: Int = -1
    private var dragEvent: DragEvent? = null
    private var pendingDragStartCount = 0
    var getSelectedCategoryType: () -> Int = { -1 }
    var getEditState: () -> Boolean = { false }
    var dragStartCallback: (dragEvent: DragEvent) -> Unit = {}
    var dragEndCallback: (dragEvent: DragEvent) -> Unit = {}
    private var selectDragItemViewParent: SelectDragItemViewParent? = null
    var dropResult: Boolean = true
    private var execAnimation: Boolean = false

    init {
        handleHelper.dragInCallback = { view, tag ->
            if (tag?.type == DropTag.Type.ITEM_VIEW) {
                setItemDragIn(view, tag.categoryType)
            }
            if (tag?.type == DropTag.Type.ITEM_VIEW_FOLDER) {
                setFolderItemDragIn(view, context)
            }
            if (tag?.type == DropTag.Type.TOOLBAR_MENU || tag?.type == DropTag.Type.TOOLBAR_MENU_BACK) {
                view?.let { setMenuDragIn(it, tag.type) }
            }
        }
        handleHelper.dragOutCallback = { view, tag ->
            if (tag?.type == DropTag.Type.ITEM_VIEW) {
                setItemDragOut(view, tag.categoryType)
            }
            if (tag?.type == DropTag.Type.ITEM_VIEW_FOLDER) {
                setFolderItemDragOut(view, context)
            }
            if (tag?.type == DropTag.Type.TOOLBAR_MENU || tag?.type == DropTag.Type.TOOLBAR_MENU_BACK) {
                setMenuDragOut(view)
            }
        }
    }

    fun handleDragEvent(rootView: View?, detectView: View?, event: DragEvent?, activity: Activity, isScrolling: Boolean): Boolean {
        when (event?.action) {
            DragEvent.ACTION_DROP -> {
                Log.d(TAG, "handleDragEvent ACTION_DROP itemDropTag ${handleHelper.itemDropTag} fragmentDropTag ${handleHelper.fragmentDropTag}")
                DragUtils.isDragging = false
                //禁止拖入到mac
                val dropTag = detectView?.tag as? DropTag
                if (dropTag?.type == DropTag.Type.MAC_FRAGMENT_VIEW) {
                    Log.d(TAG, "$dropTag -> cannot drop in mac")
                    dropResult = false
                    execAnimation(activity, event)
                    return false
                }
                handleDragEventStateChange(event, detectView, true)
                execAnimation(activity, event)
            }

            DragEvent.ACTION_DRAG_STARTED -> {
                Log.d(TAG, "handleDragEvent ACTION_DRAG_STARTED")
                if (activity is IDraggingActionOperate) {
                    DragUtils.isDragging = true
                    DragUtils.dragStartPath = activity.getDragCurrentPath()
                    DragUtils.hasAndroidDataFile = event.clipDescription?.extras
                        ?.getBoolean(CommonConstants.KEY_HAS_ANDROID_DATA_FILE) ?: false
                    activity.setNavigateItemAble()
                }
                dropResult = true
                pendingDragStartCount++
                this.dragEvent = event
                dragStartCallback.invoke(event)
            }

            DragEvent.ACTION_DRAG_ENTERED -> {
                Log.d(TAG, "handleDragEvent ACTION_DRAG_ENTERED")
                DragUtils.actionDragEntered()
                handleHelper.removeCallbackTask()
            }

            DragEvent.ACTION_DRAG_LOCATION -> {
                if (pendingDragStartCount > 0) {
                    pendingDragStartCount--
                    buildAnimationLayout(event, 0, activity)
                }
                //更新跟手坐标动效
                selectDragItemViewParent?.updateTargetPosition(event.x, event.y)
                if (context is Activity) {
                    //处理事件穿透
                    handleHelper.handleDropLocation(rootView, detectView, event, context, isScrolling, true)
                    //处理角标
                    handleDragEventStateChange(event, detectView, false)
                }
            }

            DragEvent.ACTION_DRAG_EXITED -> {
                Log.d(TAG, "handleDragEvent ACTION_DRAG_EXITED")
                handleHelper.removeCallbackTask()
                updateItemView()
                DragUtils.lastCornerMarkStateCode = NORMAL_STATE
                DragUtils.setCornerMarkStateCode(NORMAL_STATE, activity.window.decorView)
                DragUtils.actionDragExited()
                selectDragItemViewParent?.stopDragDropAnimation()
                handleHelper.resetView()
            }

            DragEvent.ACTION_DRAG_ENDED -> {
                Log.d(TAG, "handleDragEvent ACTION_DRAG_ENDED")
                dragEnd(event, activity)
            }
            else -> {}
        }
        return dropResult
    }

    private fun dragEnd(event: DragEvent, activity: Activity) {
        handleHelper.removeCallbackTask()
        dragEndCallback.invoke(event)
        if (dropResult) {
            DefaultDropListener.notifyRecyclerItemAlpha(false)
        }
        if (activity is IDraggingActionOperate && !execAnimation) {
            activity.setNavigateItemAble()
            execAnimation = false
        }
        execAnimation = false
        DragUtils.actionDragEnded(event, activity)
        DragUtils.resetAllState()
    }

    private fun updateItemView() {
        if (handleHelper.itemDropTag?.type == DropTag.Type.ITEM_VIEW) {
            setItemDragOut(handleHelper.targetItemView, handleHelper.itemDropTag?.categoryType)
        }
        if (handleHelper.itemDropTag?.type == DropTag.Type.ITEM_VIEW_FOLDER) {
            setFolderItemDragOut(handleHelper.targetItemView, context)
        }
    }

    private fun handleDragEventStateChange(event: DragEvent?, detectView: View?, isActionDrop: Boolean) {
        if (!DragUtils.isSingleShadowComplete || !DragUtils.isWholeShadowComplete) {
            Log.d(FragmentPageDropHelper.TAG, "updateDragShadowBadge, doing shadow animation and return")
            return
        }
        if (handleHelper.itemDropTag != null) {
            //识别到目标view
            handleDragItemChange(event, isActionDrop)
        } else if (handleHelper.fragmentDropTag != null) {
            //识别到目标fragment
            handleDragFragmentChange(event, isActionDrop)
        } else {
            //没有识别到目标
            if (isActionDrop) {
                CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
                dropResult = false
                detectView?.postDelayed(
                    { (context as? BaseVMActivity)?.onRefreshData() },
                    KtConstants.SECONDS_TO_MILLISECONDS
                )
                Log.d(TAG, "dropActionOperateFile dropActionOperateFile else")
            } else {
                cornerMarkStateCode = PROHIBIT_STATE
            }
        }
        if (isActionDrop) {
            handleHelper.resetView()
        } else {
            Log.d(TAG, "updateCornerMarkState current:$cornerMarkStateCode last:${DragUtils.lastCornerMarkStateCode}")
            if (DragUtils.lastCornerMarkStateCode != cornerMarkStateCode) {
                DragUtils.lastCornerMarkStateCode = cornerMarkStateCode
                DragUtils.sendCornerMarkBroadcast(cornerMarkStateCode, context)
            }
        }
    }

    private fun handleDragItemChange(event: DragEvent?, isActionDrop: Boolean) {
        val tag = handleHelper.itemDropTag
        val categoryType = tag?.categoryType ?: -1
        when (tag?.type) {
            DropTag.Type.MAIN_TAB,
            DropTag.Type.TOOLBAR_MENU,
            DropTag.Type.TOOLBAR_MENU_BACK -> {
                if (!isActionDrop) {
                    cornerMarkStateCode = NORMAL_STATE
                }
            }
            DropTag.Type.RECENT_TAB,
            DropTag.Type.ITEM_VIEW_NOTRESPONSE -> {
                if (!isActionDrop) {
                    cornerMarkStateCode = PROHIBIT_STATE
                }
            }
            DropTag.Type.ITEM_VIEW -> {
                if (isActionDrop && handleHelper.itemDropTag?.isMac == false) {
                    dropResult = DropDispatchAction.handleDragDrop(
                        (context as Activity),
                        categoryType, event, true
                    )
                    setItemDragOut(handleHelper.targetItemView, categoryType)
                } else {
                    cornerMarkStateCode = DropDispatchAction.getCornerMarkStateCode(context as Activity, categoryType, event)
                }
            }

            DropTag.Type.ITEM_VIEW_FOLDER -> {
                if (isActionDrop && handleHelper.itemDropTag?.isMac == false) {
                    MacDragUtil.MacDragObject.targetCategoryType = CATEGORY_FOLDER
                    dropResult = DropFolderAction.handleDropFolderAction(
                        (context as Activity),
                        event, tag.filePath
                    )
                    setFolderItemDragOut(handleHelper.targetItemView, context)
                } else {
                    cornerMarkStateCode = DropDispatchAction.getCornerMarkStateCode(context as Activity, categoryType, event)
                }
            }

            DropTag.Type.ITEM_VIEW_PROHIBIT -> {
                if (isActionDrop) {
                    CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
                    dropResult = false
                } else {
                    cornerMarkStateCode = PROHIBIT_STATE
                }
            }

            else -> {
                if (!isActionDrop) {
                    cornerMarkStateCode = DropDispatchAction.getCornerMarkStateCode(context as Activity, categoryType, event)
                }
            }
        }
    }

    private fun handleDragFragmentChange(event: DragEvent?, isActionDrop: Boolean) {
        val categoryType = handleHelper.fragmentDropTag?.categoryType ?: -1
        if (isActionDrop) {
            dropResult = DropDispatchAction.handleDragDrop(
                (context as Activity),
                categoryType,
                event,
                false
            )
        } else {
            cornerMarkStateCode =
                DropDispatchAction.getCornerMarkStateCode(context as Activity, categoryType, event)
        }
    }

    private fun execAnimation(activity: Activity, event: DragEvent) {
        if (activity !is IDraggingActionOperate) return
        activity.setNavigateItemAble()
        if (selectDragItemViewParent == null) {
            val selectedView = activity.getSelectedItemView()
            val selectedItemView = selectedView?.filter {
                KtViewUtils.isViewVisible(it)
            }?.toCollection(ArrayList())
            if (selectedItemView != null && selectedItemView.size > 0 && selectedItemView.size <= DISPERSION_ANIMATION_LIMIT_COUNT && !dropResult) {
                execAnimation = true
                DefaultDropListener.selectedItemViews?.value = selectedItemView
                buildAnimationLayout(event, 1, activity)
                DragUtils.preventWindowAnim(activity)
                dropResult = true
            } else  {
                if (selectedItemView.isNullOrEmpty()) {
                    DefaultDropListener.selectedItemViews?.value?.forEach { itemView ->
                        itemView.alpha = 1f
                        itemView.foreground = null
                    }
                } else {
                    selectedItemView.forEach { itemView ->
                        itemView.alpha = 1f
                        itemView.foreground = null
                    }
                }
                DefaultDropListener.notifyRecyclerItemAlpha(false)
            }
        } else {
            execAnimation = false
            dropResult = true
            selectDragItemViewParent?.cancelUpAnimation()
            DragUtils.preventWindowAnim(activity)
        }
    }

    private fun setMenuDragIn(view: View, type: DropTag.Type) {
        val maskRippleDrawable = COUIMaskRippleDrawable(view.context)
        when (type) {
            DropTag.Type.TOOLBAR_MENU -> maskRippleDrawable.setCustomRippleMask()

            DropTag.Type.TOOLBAR_MENU_BACK -> {
                maskRippleDrawable.setCircleRippleMask(COUIMaskRippleDrawable.getMaskRippleRadiusByType(view.context, RIPPLE_TYPE_ICON_RADIUS))
            }

            else -> {}
        }
        view.background = maskRippleDrawable
        COUIDarkModeUtil.setForceDarkAllow(view, false)
    }

    private fun setMenuDragOut(itemView: View?) {
        itemView?.setBackgroundColor(context.resources.getColor(com.support.appcompat.R.color.coui_color_background_with_card))
    }

    private fun setItemDragIn(itemView: View?, categoryType: Int?) {
        setItemDragState(itemView, true, categoryType)
        if (categoryType == getSelectedCategoryType()) {
            setItemSelected(itemView, false)
        }
    }

    private fun setItemDragOut(itemView: View?, categoryType: Int?) {
        setItemDragState(itemView, false, categoryType)
        if (categoryType == getSelectedCategoryType() && !getEditState()) {
            setItemSelected(itemView, true)
        }
    }

    private fun setFolderItemDragIn(itemView: View?, context: Context) {
        //列表
        if (itemView is SelectItemLayout) {
            itemView.isPressed = true
            return
        }

        //宫格
        itemView?.setBackgroundColor(context.resources.getColor(com.support.appcompat.R.color.coui_color_hover))
    }

    private fun setFolderItemDragOut(itemView: View?, context: Context) {
        //列表
        if (itemView is SelectItemLayout) {
            itemView.isPressed = false
            return
        }

        //宫格
        itemView?.setBackgroundColor(context.resources.getColor(com.support.appcompat.R.color.coui_color_background_with_card))
    }

    private fun setItemDragState(itemView: View?, dragIn: Boolean, categoryType: Int?) {
        val alpha = if (dragIn) {
            1.0f
        } else {
            if (getEditState() && !checkEditEnable(categoryType)) {
                EDIT_VIEW_ALPHA
            } else {
                1.0f
            }
        }
        itemView?.alpha = alpha
        (itemView as? SideNavigationItemContainer)?.setIsHover(dragIn, true)
        (itemView as? SideNavigationStorageContainer)?.setIsHover(dragIn, true)
        (itemView as? COUICardListSelectedItemLayout)?.setIsSelected(dragIn, true)

        if (dragIn) {
            (itemView as? SmoothRoundedCornersConstraintLayout)?.setHoverBackground()
        } else {
            (itemView as? SmoothRoundedCornersConstraintLayout)?.setRoundCornerBg()
        }
    }

    private fun setItemSelected(itemView: View?, selected: Boolean) {
        (itemView as? SideNavigationItemContainer)?.setIsSelected(selected = selected, animated = true)
        (itemView as? SideNavigationStorageContainer)?.setIsSelected(selected = selected, animated = true)
    }

    /**
     * 检查在编辑状态下，view是否可用
     */
    private fun checkEditEnable(categoryType: Int?): Boolean {
        if (categoryType == null) return false
        return CategoryHelper.isShortcutFolderType(categoryType) || CategoryHelper.isLabelType(categoryType)
    }

    @SuppressLint("Range")
    private fun buildAnimationLayout(event: DragEvent, state: Int, activity: Activity) {
        if (UIConfigMonitor.isZoomWindowShow()) {
            val shadowBuild = DragUtils.getShadowBuild()
            shadowBuild?.setShadowVisible(true)
            activity.window.decorView.updateDragShadow(shadowBuild)
            DragUtils.lastCornerMarkStateCode = DropDispatchAction.NORMAL_STATE
            DragUtils.setCornerMarkStateCode(DropDispatchAction.NORMAL_STATE, activity.window.decorView)
            DefaultDropListener.notifyRecyclerItemAlpha(true)
            return
        }
        event.localState ?: return
        val rect = Rect()
        activity.window.decorView.getWindowVisibleDisplayFrame(rect)
        if (selectDragItemViewParent != null) {
            removeAnimationParentLayout(activity)
        }
        selectDragItemViewParent = if (state == 0) {
            SelectDragItemViewParent(context, event.x, event.y, object : SelectDragItemViewParent.IStartDragAndDrop {
                override fun startDragAndDrop() {
                    animationEndCallback(activity)
                }
            }, DefaultDropListener.viewMode)
        } else {
            SelectDragItemViewParent(context, event.x, event.y, object : SelectDragItemViewParent.IStartDragAndDrop {
                override fun startDragAndDrop() {
                    DefaultDropListener.selectedItemViews?.value?.forEach { view ->
                        view.alpha = 1f
                        cornerMarkStateCode = -1
                        view.foreground = null
                    }
                    DefaultDropListener.notifyRecyclerItemAlpha(false)
                    removeAnimationParentLayout(activity)
                }
            }, DefaultDropListener.viewMode)
        }
        selectDragItemViewParent?.visibility = View.VISIBLE
        val layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        layoutParams.gravity = Gravity.CENTER
        val decorView = activity.window?.decorView
        if (decorView is ViewGroup) {
            decorView.addView(selectDragItemViewParent)
        }
        DefaultDropListener.selectedItemViews?.value?.let { viewList ->
            if (viewList.isNotEmpty()) {
                selectDragItemViewParent?.addTargetView(viewList, state)
            } else {
                removeAnimationParentLayout(activity)
            }
        }
    }

    private fun animationEndCallback(activity: Activity) {
        val shadowBuild = DragUtils.getShadowBuild()
        selectDragItemViewParent?.visibility = View.GONE
        removeAnimationParentLayout(activity)
        shadowBuild?.setShadowVisible(true)
        activity.window.decorView.updateDragShadow(shadowBuild)
        shadowBuild?.startShadowAnim()
        DefaultDropListener.selectedItemViews?.value?.size?.let { size ->
            if (size > 1) {
                shadowBuild?.startSingleShadowAnim()
            }
        }
        if (shadowBuild == null) {
            DefaultDropListener.notifyRecyclerItemAlpha(false)
        } else {
            DefaultDropListener.notifyRecyclerItemAlpha(true)
        }
    }

    private fun removeAnimationParentLayout(activity: Activity) {
        Log.d(TAG, "removeAnimationParentLayout")
        val decorView = activity.window?.decorView
        if (decorView is ViewGroup) {
            decorView.removeView(selectDragItemViewParent)
            selectDragItemViewParent = null
        }
    }
}
