/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchMediaBean
 ** Description : Search Media Bean
 ** Version     : 1.0
 ** Date        : 2024/06/27 16:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/06/27       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.data

import android.provider.MediaStore
import androidx.annotation.Keep
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.base.UriFileWrapper
import com.google.gson.annotations.SerializedName
import com.oplus.filemanager.apiproxy.services.loader.SearchDocLoader

@Keep
class SearchMediaBean {

    companion object {
        /**
         * 毫秒和秒之间的进制
         */
        const val MILL_SECOND_TIME = 1000
    }

    @SerializedName(MediaStore.Files.FileColumns._ID)
    private var id: Int? = 0

    @SerializedName(MediaStore.Files.FileColumns.DATA)
    private var data: String? = null

    @SerializedName(MediaStore.Files.FileColumns.DISPLAY_NAME)
    private var displayName: String? = null

    @SerializedName(MediaStore.Files.FileColumns.SIZE)
    private var size: Long? = null

    @SerializedName(MediaStore.Files.FileColumns.DATE_MODIFIED)
    private var dateModified: Long? = null

    @SerializedName(MediaStore.Files.FileColumns.MIME_TYPE)
    private var mimeType: String? = null

    constructor(file: UriFileWrapper) {
        this.id = file.id
        this.data = file.mData
        this.displayName = file.mDisplayName
        this.size = file.mSize
        this.dateModified = file.mDateModified / MILL_SECOND_TIME
        this.mimeType = file.mMimeType ?: ""
    }

    constructor(file: RemoteFileBean) {
        this.id = file.remoteId.hashCode()
        this.data = file.mData
        this.displayName = file.mDisplayName
        this.size = file.mSize
        this.dateModified = file.mDateModified
        this.mimeType = file.mMimeType
    }

    fun getColumnNames(): Array<String> {
        return SearchDocLoader.PROJECTION
    }
}