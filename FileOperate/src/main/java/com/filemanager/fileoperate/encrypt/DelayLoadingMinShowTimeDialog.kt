/*****************************************************************************
 * * Copyright (C), 2020-2030, oplus Mobile Comm Corp., Ltd.
 * * OPLUS_EDIT
 * * File: - DelayLoadingMinShowDialog.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2021/8/24
 * * Author: puziling
 * *
 * ************************** Revision History: *******************************
 * *     <author>       <data>      <version >     <desc>
 * *     puziling        2021/8/24       1.0            build this module
 *****************************************************************************/
package com.filemanager.fileoperate.encrypt

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.content.DialogInterface
import android.os.Handler
import android.os.Looper
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.progressbar.COUIHorizontalProgressBar
import com.filemanager.common.MyApplication
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.fileoperate.R
import com.oplus.anim.EffectiveAnimationView
import kotlinx.coroutines.*
import java.lang.Runnable

class DelayLoadingMinShowTimeDialog(
    val context: Context,
    style: Int,
    val clickAction: (() -> Unit)?
) {

    companion object {
        private const val TAG = "DelayLoadingMinShowTimeDialog"
        private const val MIN_SHOW_ANIM_TIME = 700L //ms
        private const val ANIM_SHOW_DIALOG_TIME = 100L
        private const val MIN_PROGRESS_BAR_TIME = MIN_SHOW_ANIM_TIME + ANIM_SHOW_DIALOG_TIME
        private const val FAIL_DWELL_TIME = 750L
        private const val FINISH_DWELL_TIME = 400L
        private const val ANIM_DURATION_300 = 300L
        private const val ANIM_DURATION_150 = 150L
        const val ANIM_MAX_FRAME = 146
        const val DELAY_START_TIME_MIN = 48L
        const val DELAY_START_TIME_MID = 400L
        const val DELAY_START_TIME_LONG = 600L
        const val PERCENT_99 = 99
        const val PERCENT_MAX = 100
        const val INTERVAL = MIN_SHOW_ANIM_TIME / PERCENT_MAX
        private var setPrePercent: Int = 0
        private var prePercent: Int = 0
    }

    private var dialogStyle = style
    private var fileNum = 0
    private var startShowTime = 0L
    private var failedCount = 0
    private var overSizeCount = 0
    private var animationView: EffectiveAnimationView? = null
    private var dialogTitle: TextView? = null
    private var dialogSubTitle: TextView? = null
    private var dialogCancel: TextView? = null
    private var animView: LinearLayout? = null
    private var bodyView: LinearLayout? = null
    private var isUserCancel = false
    private var isChipDialogShowing = false
    private val dialogHandler = Handler(Looper.getMainLooper())
    private var dialog = COUIAlertDialogBuilder(context, style).apply {
        if ((style == com.support.dialog.R.style.COUIAlertDialog_Progress_Cancelable) || isSecurityChipDailog()) {
            prePercent = 0
            setPrePercent = 0
            if (isSecurityChipDailog()) {
                isChipDialogShowing = true
                setView(getSecurityChipAnimView())
                setOnKeyListener(DialogInterface.OnKeyListener { _, keyCode, keyEvent ->
                    Log.d(TAG, "onKey keyCode:$keyCode keyEvent:$keyEvent")
                    if ((clickAction != null) && (keyEvent.action == KeyEvent.ACTION_UP)
                        && ((keyCode == KeyEvent.KEYCODE_BACK) || (keyCode == KeyEvent.KEYCODE_SEARCH))) {
                        Log.d(TAG, "DelayLoadingMinShowTimeDialog OnKey()  action = ${keyEvent.action}, keyCode = $keyCode")
                        clickAction.invoke()
                        return@OnKeyListener true
                    }
                    return@OnKeyListener false
                })
            } else {
                setNegativeButton(android.R.string.cancel) { dialogInterface, _ -> dialogInterface?.cancel() }
                setOnCancelListener {
                    clickAction?.invoke()
                }
            }
        }
    }.create()
    private var delayDismissRunnable = Runnable {
        if (!isSecurityChipDailog()) {
            dialog.dismiss()
            startShowTime = 0
        } else {
            dismissSecurityChipDailog()
        }
    }

    private var delayDismissSeChipRunnable = Runnable {
        isChipDialogShowing = false
        dialog.dismiss()
        startShowTime = 0
    }

    private var showingAction: (() -> Unit)? = null

    private var delayShowRunnable = Runnable {
        if (!dialog.isShowing) {
            prePercent = 0
            startShowTime = System.currentTimeMillis()
            PredictiveBackUtils.registerOnBackInvokedCallback(dialog)
            dialog.show()
            showingAction?.invoke()
        }
    }

    private fun isSecurityChipDailog(): Boolean {
        return dialogStyle == R.style.fop_SecurityChip_AlertDialog_Progress_Cancelable
    }

    private fun getProgressText(progress: Int): String {
        val finishFile = (fileNum * (progress / PERCENT_MAX.toFloat())).toInt()
        return context.resources.getString(
            com.filemanager.common.R.string.file_encrypt_process,
            finishFile,
            fileNum
        )
    }

    fun setTitle(titleId: Int): DelayLoadingMinShowTimeDialog {
        if (isSecurityChipDailog()) {
            dialogTitle?.setText(titleId)
        } else {
            dialog.setTitle(titleId)
        }
        return this
    }

    fun setTitle(title: String): DelayLoadingMinShowTimeDialog {
        if (isSecurityChipDailog()) {
            dialogTitle?.text = title
        } else {
            dialog.setTitle(title)
        }
        return this
    }

    fun setFileNum(size: Int): DelayLoadingMinShowTimeDialog {
        fileNum = size
        dialogSubTitle?.text = getProgressText(0)
        return this
    }

    fun getSecurityChipAnimView(): View {
        val view: View = LayoutInflater.from(context).inflate(R.layout.dialog_security_chip, null)
        bodyView = view.findViewById(R.id.body)
        animView = view.findViewById(R.id.anim_view)
        dialogCancel = view.findViewById(R.id.dialog_cancel)
        dialogCancel?.setOnClickListener { v ->
            isUserCancel = true
            if (isShowFailedHint()) {
                isChipDialogShowing = false
                dialog.dismiss()
            } else {
                clickAction?.invoke()
            }
        }
        dialogTitle = view.findViewById(R.id.dialog_title)
        dialogSubTitle = view.findViewById(R.id.dialog_sub_title)
        animationView = view.findViewById(R.id.progress_anim)
        animationView?.apply {
            if (Utils.isNightMode(MyApplication.sAppContext)) {
                setAnimation(R.raw.security_chip_encryption_loading_night)
            } else {
                setAnimation(R.raw.security_chip_encryption_loading)
            }
            setMinAndMaxFrame(0, ANIM_MAX_FRAME)
            progress = 0f
        }
        return view
    }

    @SuppressLint("CutPasteId")
    fun setProgress(process: Int) {
        if (isSecurityChipDailog()) {
            updataAnimProgress(process)
        } else {
            (dialog.window?.findViewById<View>(com.support.dialog.R.id.progress) as? COUIHorizontalProgressBar)?.progress =
                process
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun updataAnimProgress(percent: Int) {
        Log.d(TAG, "[updataAnimProgress] setPrePercent=$setPrePercent, percent=$percent")
        if (setPrePercent == percent) {
            return
        }
        if (prePercent == 0 && percent > 0) {
            startShowTime = System.currentTimeMillis()
        }
        GlobalScope.launch(Dispatchers.IO) {
            dialog.apply {
                animationView?.apply {
                    if ((percent <= 0) || (percent > PERCENT_MAX)) {
                        Log.d(TAG, "[updataAnimProgress] percent returns an exception, not in the range of 1 ~ 100")
                        return@launch
                    }
                    if (percent == prePercent) {
                        Log.d(TAG, "[updataAnimProgress] percent repeated update")
                        return@launch
                    }
                    setPrePercent = percent
                    val costTime = System.currentTimeMillis() - startShowTime
                    val leftTime = percent * INTERVAL - costTime
                    if (leftTime <= 0) {
                        updateProgressAnim(percent)
                    } else {
                        val intervalCount = (leftTime / INTERVAL).toInt()
                        if (intervalCount <= 0) {
                            updateProgressAnim(percent)
                        } else {
                            val percentInterval = (percent - prePercent) / intervalCount
                            val completedProgress = prePercent
                            for (i in 1 until intervalCount) {
                                updateProgressAnim(completedProgress + (i * percentInterval))
                                delay(INTERVAL)
                                if (setPrePercent != percent) {
                                    return@launch
                                }
                            }
                            updateProgressAnim(percent)
                        }
                    }
                }
            }
        }
    }

    private fun updateProgressAnim(progress: Int) {
        Log.d(TAG, "[updateProgressAnim] progress=$progress")
        GlobalScope.launch(Dispatchers.Main) {
            if (prePercent < progress) {
                prePercent = progress
                animationView?.setMinFrame(progress)
                dialogSubTitle?.text = getProgressText(progress)
            }
        }
    }

    fun setCancelOutside(value: Boolean): DelayLoadingMinShowTimeDialog {
        dialog.setCanceledOnTouchOutside(value)
        return this
    }

    fun setCancelable(value: Boolean): DelayLoadingMinShowTimeDialog {
        dialog.setCancelable(value)
        return this
    }

    fun setShowingCallback(showingAction: (() -> Unit)?): DelayLoadingMinShowTimeDialog {
        this.showingAction = showingAction
        return this
    }

    fun show() {
        delayShowRunnable.run()
    }

    /**
     * need call {@link forceDismiss()} at onDestroy
     */
    fun delayShow(delayTime: Long = DELAY_START_TIME_MIN) {
        if (!dialogHandler.hasCallbacks(delayShowRunnable) && !dialog.isShowing) {
            dialogHandler.postDelayed(delayShowRunnable, delayTime)
        }
    }

    fun recoverDialog() {
        if (isShowFailedHint()) {
            dialogTitle?.text = getSeChipTitleText()
            dialogSubTitle?.text = getSeChipSubTitleText()
        }
        dialog.show()
    }

    fun dismiss() {
        dialogHandler.removeCallbacks(delayShowRunnable)
        val diffTime = System.currentTimeMillis() - startShowTime
        if (diffTime > MIN_PROGRESS_BAR_TIME) {
            if (!isSecurityChipDailog()) {
                isChipDialogShowing = false
                dialog.dismiss()
                startShowTime = 0
            } else {
                dismissSecurityChipDailog()
            }
        } else {
            dialogHandler.postDelayed(delayDismissRunnable, MIN_PROGRESS_BAR_TIME - diffTime)
        }
    }

    private fun dismissSecurityChipDailog() {
        Log.d(
            TAG,
            "[dismissSecurityChipDailog] isUserCancel = $isUserCancel, isShowFailedHint = ${isShowFailedHint()}"
        )
        playTitlelAnim(dialogTitle)
        playSubTitlelAnim(dialogSubTitle)

        if (!isShowFailedHint() || isUserCancel) {
            if (!isShowFailedHint() && (setPrePercent >= PERCENT_99)) {
                prePercent = PERCENT_99
                updateProgressAnim(PERCENT_MAX)
            }
            playCancelAnim()
            playChipMoveAnim()
        }
    }

    private fun isShowFailedHint(): Boolean {
        return failedCount > 0
    }

    private fun playCancelAnim() {
        dialogCancel?.apply {
            if ((tag as? Boolean) != true) {
                tag = true
                startAlphaAnim(dialogCancel, 1f, 0f)
            }
        }
    }

    private fun playTitlelAnim(title: TextView?) {
        title?.apply {
            if (isAnimated(this)) {
                return
            }
            title.tag = true
            val animatorListener: Animator.AnimatorListener =
                object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        text = getSeChipTitleText()
                        startAlphaAnim(this@apply, 0f, 1f)
                    }
                }
            startAlphaAnim(this, 1f, 0f, animatorListener)
        }
    }

    private fun isAnimated(view: View?): Boolean {
        return ((view?.tag as? Boolean) == true)
    }

    private fun playSubTitlelAnim(subTitle: TextView?) {
        subTitle?.apply {
            if (!isShowFailedHint()) {
                return
            }
            if (isAnimated(this)) {
                return
            }
            subTitle.tag = true
            val animatorListener: Animator.AnimatorListener =
                object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        getSeChipSubTitleText()?.apply {
                            subTitle.text = this
                        }
                        startAlphaAnim(subTitle, 0f, 1f)
                    }
                }
            startAlphaAnim(subTitle, 1f, 0f, animatorListener)
        }
    }

    private fun getSeChipTitleText(): String {
        return if (isShowFailedHint()) {
            context.resources.getQuantityString(
                com.filemanager.common.R.plurals.file_encrypt_failed_toast,
                failedCount,
                failedCount
            )
        } else {
            context.getString(com.filemanager.common.R.string.file_encrypt_complete)
        }
    }

    private fun getSeChipSubTitleText(): String? {
        return if (isShowFailedHint()) {
            when {
                isOversizedFileError() -> context.getString(com.filemanager.common.R.string.encrypt_size_too_large)
                else -> ""
            }
        } else {
            null
        }
    }

    private fun isOversizedFileError(): Boolean {
        return overSizeCount == failedCount
    }

    @SuppressLint("ObjectAnimatorBinding")
    private fun startAlphaAnim(
        view: View?,
        fromAlpha: Float,
        toAlpha: Float,
        animatorListener: Animator.AnimatorListener? = null
    ) {
        view?.apply {
            AnimatorSet().apply {
                duration = ANIM_DURATION_150
                interpolator = COUIMoveEaseInterpolator()
                playTogether(
                    ObjectAnimator.ofFloat(view, "alpha", fromAlpha, toAlpha)
                )
                if (animatorListener != null) {
                    addListener(animatorListener)
                }
                startDelay = 0
                start()
            }
        }
    }

    fun playChipMoveAnim() {
        bodyView?.let {
            if (isAnimated(it)) {
                return
            }
            it.tag = true
            val bodyViewH = it.height
            animView?.apply {
                val animViewH = this.height
                val params = this.layoutParams as? LinearLayout.LayoutParams
                params?.let {
                    startMoveAnim(this, (bodyViewH - animViewH) / 2 - it.topMargin)
                }
            }
        }
    }

    @SuppressLint("ObjectAnimatorBinding")
    fun startMoveAnim(view: LinearLayout, translationY: Int) {
        // translation
        AnimatorSet().apply {
            duration = ANIM_DURATION_300
            interpolator = COUIMoveEaseInterpolator()
            playTogether(
                ObjectAnimator.ofFloat(view, "translationY", 0f, translationY.toFloat())
            )
            val animatorListener: Animator.AnimatorListener = object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    super.onAnimationEnd(animation)
                    removeAllListeners()
                    if (!isShowFailedHint()) {
                        playLockAnim()
                    } else {
                        dialogHandler.postDelayed(delayDismissSeChipRunnable, FAIL_DWELL_TIME)
                    }
                }
            }
            addListener(animatorListener)
            startDelay = 0
            start()
        }
    }

    fun playLockAnim() {
        animationView?.apply {
            val animatorListener: Animator.AnimatorListener = object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    super.onAnimationEnd(animation)
                    removeAllAnimatorListeners()
                    dialogHandler.postDelayed(delayDismissSeChipRunnable, FINISH_DWELL_TIME)
                }
            }
            addAnimatorListener(animatorListener)
            playAnimation()
        }
        animationView?.playAnimation()
    }

    fun isShowing(): Boolean {
        return dialog.isShowing
    }

    fun setOnDismissListener(listener: DialogInterface.OnDismissListener?): DelayLoadingMinShowTimeDialog {
        dialog.setOnDismissListener(listener)
        return this
    }

    /**
     * Need to call at onDestroy, otherwise it may cause memory leak
     */
    fun forceDismiss() {
        dialogHandler.removeCallbacks(delayShowRunnable)
        dialogHandler.removeCallbacks(delayDismissRunnable)
        prePercent = PERCENT_MAX
        isChipDialogShowing = false
        dialog.dismiss()
        startShowTime = 0
    }

    fun setFailedCount(failedCount: Int) {
        this.failedCount = failedCount
    }

    fun setOverSizeCount(overSizeCount: Int) {
        this.overSizeCount = overSizeCount
    }

    fun isChipDialogShowing(): Boolean {
        return isChipDialogShowing
    }

    fun setChipDialogShowing(b: Boolean) {
        this.isChipDialogShowing = b
    }

    fun setPrePercent(i: Int) {
        prePercent = i
    }

    fun getprePercent(): Int {
        return prePercent
    }
}