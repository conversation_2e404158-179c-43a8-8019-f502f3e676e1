/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchDocLoader
 ** Description : 搜索Doc的Loader
 ** Version     : 1.0
 ** Date        : 2024/06/20 10:41
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/06/20       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.loader

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.os.Bundle
import android.provider.MediaStore
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.base.UriFileWrapper
import com.filemanager.common.utils.GsonUtil
import com.filemanager.common.utils.Log
import com.oplus.filemanager.apiproxy.services.cache.SearchFileSizeCache
import com.oplus.filemanager.apiproxy.services.cursor.SearchCursor
import com.oplus.filemanager.apiproxy.services.data.SearchMediaBean
import com.oplus.filemanager.apiproxy.services.data.SearchResultData
import com.oplus.filemanager.apiproxy.services.utils.DeeplinkUtils
import com.oplus.filemanager.apiproxy.services.utils.SearchChecker

class SearchDocLoader(val context: Context) : ILoader {

    companion object {

        private const val TAG = "SearchDocLoader"

        /**
         * 需要区分本地搜索还是全部搜索
         */
        private const val SEARCH_FILE_ONLY_LOCAL = "search_file_only_local"

        /**
         * 表示搜索本地.只需要判断是否有结果
         */
        private const val SEARCH_RESULT_SAMPLE = 0

        /**
         * 表示需要搜索实际内容并返回内容
         */
        private const val SEARCH_RESULT_DETAIL = 1

        /**
         * 分类类型，目前默认为doc
         */
        private const val KEY_CATEGORY = "key_category"

        /**
         * 需要搜索文件的关键字
         */
        const val SEARCH_FILE_KEYWORD = "search_file_keyword"

        /**
         * 要搜索的文件类型（int)
         */
        const val SEARCH_FILE_TYPE = "search_file_type"

        private const val METHOD_SEARCH_LOCAL_DOCUMENT = "search_local_document"


        const val LIMIT_COUNT = 10


        val PROJECTION = arrayOf(
            MediaStore.Files.FileColumns._ID,
            MediaStore.Files.FileColumns.DATA,
            MediaStore.Files.FileColumns.DISPLAY_NAME,
            MediaStore.Files.FileColumns.SIZE,
            MediaStore.Files.FileColumns.DATE_MODIFIED,
            MediaStore.Files.FileColumns.MIME_TYPE
        )
    }

    /**
     * 搜索关键词
     */
    private var keyword: String? = null

    /**
     * 搜索的类型(0~13)
     */
    private var fileType: Int = 0

    private var searchResult: SearchResultData = SearchResultData()

    private var searchMethodList = mutableListOf<SearchMethod>()

    init {
        searchMethodList.add(DmpSearchMethodImpl(context))
        searchMethodList.add(MediaSearchMethodImpl(context))
        searchMethodList.add(RemoteFileSearchMethodImpl(context))
    }

    @Suppress("TooGenericExceptionCaught")
    override fun query(contentResolver: ContentResolver, queryArgs: Bundle?): Cursor? {
        val args = queryArgs ?: return null
        keyword = args.getString(SEARCH_FILE_KEYWORD)
        fileType = args.getInt(SEARCH_FILE_TYPE, 0)
        Log.d(TAG, "query -> keyword:$keyword filetype:$fileType")
        // 检查fileType是否合法
        var code = SearchChecker.checkFileType(fileType)
        if (!SearchChecker.isOK(code)) {
            Log.e(TAG, "query -> keyword:$keyword filetype:$fileType, fileType out range[0,13]")
            return SearchCursor.create(code)
        }
        // 开始搜索
        searchResult.resetResultCode()
        searchMethodList.forEach {
            it.search(contentResolver, keyword, fileType, searchResult)
        }
        // 检查搜索结果是否有异常
        code = searchResult.getResultCode()
        if (!SearchChecker.isOK(code)) {
            return SearchCursor.create(code)
        }
        // 根据结果拼装cursor
        val searchList = searchResult.getSearchList()
        val result = searchList.subList(0, LIMIT_COUNT.coerceAtMost(searchList.size))
        // 缓存文件路径和size
        SearchFileSizeCache.cache(result)
        val cursor = createCursor(result)
        cursor.apply {
            this.extras.putInt(SearchChecker.CODE, SearchChecker.CODE_OK)
            this.extras.putString(DeeplinkUtils.SEARCH_DEEP_LINK, "${DeeplinkUtils.DEEP_LINK_SEARCH}=$keyword")
            this.extras.putString(DeeplinkUtils.DOC_DEEP_LINK, DeeplinkUtils.DEEP_LINK_DOC)
            this.extras.putString(DeeplinkUtils.OPEN_DEEP_LINK, DeeplinkUtils.DEEP_LINK_OPEN)
        }
        return cursor
    }

    fun createCursor(list: List<BaseFileBean>): Cursor {
        val docList = mutableListOf<SearchMediaBean>()
        list.forEach {
            if (it is UriFileWrapper) {
                docList.add(SearchMediaBean(it))
            } else if (it is RemoteFileBean) {
                docList.add(SearchMediaBean(it))
            }
        }
        val json = GsonUtil.toJson(docList)
        Log.d(TAG, "createCursor $json")
        return SearchCursor(json)
    }
}
