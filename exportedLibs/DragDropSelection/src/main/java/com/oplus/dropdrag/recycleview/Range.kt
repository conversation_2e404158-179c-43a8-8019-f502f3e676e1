/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - Range.kt
 ** Description: Class providing support for managing range selections.
 ** Version: 1.0
 ** Date : 2020/05/15
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/15    1.0     create
 ****************************************************************/

package com.oplus.dropdrag.recycleview

import androidx.recyclerview.widget.RecyclerView
import com.oplus.dropdrag.SelectionTracker.Companion.DEBUG
import com.oplus.dropdrag.utils.Log

/*
 * @see {@link DefaultSelectionTracker#updateForRange(int, int , boolean, int)}.
 */
internal abstract class Callbacks {
    /**
     * @param begin    Adapter position for range start (inclusive).
     * @param end      Adapter position for range end (inclusive).
     * @param anchor   anchor position for range select (inclusive).
     * @param selected New selection state.
     */
    abstract fun updateForRange(begin: Int, end: Int, anchor: Int, selected: Boolean)
}

/*
 * Class providing support for managing range selections.
 */
internal abstract class Range(
    protected val mBegin: Int,
    protected val mCallbacks: Callbacks
) {
    private companion object {
        private const val TAG = "Range"
    }

    protected var mEnd = RecyclerView.NO_POSITION

    abstract fun extendRange(position: Int)

    /**
     * Try to set selection state for all elements in range. Not that callbacks can cancel
     * selection of specific items, so some or even all items may not reflect the desired state
     * after the update is complete.
     * @param begin    Adapter position for range start (inclusive).
     * @param end      Adapter position for range end (inclusive).
     * @param anchor   anchor position for range select (inclusive).
     * @param selected New selection state.
     */
    protected fun updateRange(begin: Int, end: Int, anchor: Int, selected: Boolean) {
        if (DEBUG) {
            Log.d(TAG, "Range{begin=$begin, end=$end, selected=$selected}")
        }
        mCallbacks.updateForRange(begin, end, anchor, selected)
    }

    override fun toString(): String {
        return "Range{begin=$mBegin, end=$mEnd}"
    }
}

/*
 * range selections keep the same check state,apply to linear layout of recyclerview
 */
internal class ListRange(
    begin: Int,
    callbacks: Callbacks,
    private val mIsSelected: Boolean = false
) : Range(begin, callbacks) {

    private companion object {
        private const val TAG = "ListRange"
    }

    override fun extendRange(position: Int) {
        if (DEBUG) {
            Log.d(TAG, "extendRange position: $position, mBegin: $mBegin, mEnd: $mEnd, isSelected : $mIsSelected")
        }

        if (position == RecyclerView.NO_POSITION) {
            Log.d(TAG, "extendRange Position cannot be NO_POSITION.")
            return
        }

        if ((mEnd == RecyclerView.NO_POSITION) || (mEnd == mBegin)) {
            // Reset mEnd so it can be established in establishRange.
            mEnd = RecyclerView.NO_POSITION
            establishRange(position)
        } else {
            reviseRange(position)
        }
    }

    private fun establishRange(position: Int) {
        if (mEnd != RecyclerView.NO_POSITION) {
            Log.d(TAG, "extendRange End has already been set.")
            return
        }

        mEnd = position
        if (position > mBegin) {
            if (DEBUG) {
                Log.d(TAG, "establishRange1 Establishing initial range at @ $position")
            }
            updateRange(mBegin + 1, position, mBegin, mIsSelected)
        } else if (position < mBegin) {
            if (DEBUG) {
                Log.d(TAG, "establishRange2 Establishing initial range at @ $position")
            }
            updateRange(position, mBegin - 1, mBegin, mIsSelected)
        } else {
            if (DEBUG) {
                Log.d(TAG, "establishRange3 Establishing initial range at @ $position")
            }
            updateRange(position, mBegin, mBegin, mIsSelected)
        }
    }

    private fun reviseRange(position: Int) {
        if (mEnd == RecyclerView.NO_POSITION) {
            Log.d(TAG, "reviseRange End must already be set.")
            return
        }

        if (mBegin == mEnd) {
            Log.d(TAG, "reviseRange Beging and end point to same position.")
            return
        }

        if (position == mEnd) {
            if (DEBUG) {
                Log.d(TAG, "reviseRange Ignoring no-op revision for range @ $position")
            }
        }
        if (mEnd > mBegin) {
            reviseAscending(position)
        } else if (mEnd < mBegin) {
            reviseDescending(position)
        }
        // the "else" case is covered by checkState at beginning of method.
        mEnd = position
    }

    /**
     * Updates an existing ascending selection.
     */
    private fun reviseAscending(position: Int) {
        if (DEBUG) {
            Log.d(TAG, "reviseAscending *ascending* Revising range @ $position")
        }

        if (position < mEnd) {
            if (position < mBegin) {
                updateRange(mBegin + 1, mEnd, mBegin, mIsSelected)
                updateRange(position, mBegin - 1, mBegin, mIsSelected)
            } else {
                updateRange(position + 1, mEnd, mBegin, mIsSelected)
            }
        } else if (position > mEnd) {
            // Extending the range...
            updateRange(mEnd + 1, position, mBegin, mIsSelected)
        }
    }

    private fun reviseDescending(position: Int) {
        if (DEBUG) {
            Log.d(TAG, "reviseDescending *descending* Revising range @ $position")
        }

        if (position > mEnd) {
            if (position > mBegin) {
                updateRange(mEnd, mBegin - 1, mBegin, mIsSelected)
                updateRange(mBegin + 1, position, mBegin, mIsSelected)
            } else {
                updateRange(mEnd, position - 1, mBegin, mIsSelected)
            }
        } else if (position < mEnd) {
            // Extending the range...
            updateRange(position, mEnd - 1, mBegin, mIsSelected)
        }
    }
}

/*
 * range selections reference to starting point check state,apply to grid layout of recyclerview
 */
internal class GridRange(
    begin: Int,
    callbacks: Callbacks,
    private val mIsSelected: Boolean = false
) : Range(begin, callbacks) {
    private companion object {
        private const val TAG = "GridRange"
        private const val INVALID_ORIENTATION = -1
        private const val POSITIVE_ORIENTATION = 1
        private const val NEGATIVE_ORIENTATION = 2
    }

    private var mOrientation = INVALID_ORIENTATION

    override fun extendRange(position: Int) {
        if (DEBUG) {
            Log.d(TAG, "extendRange position: $position, mBegin: $mBegin, mEnd: $mEnd, isSelected : $mIsSelected")
        }

        if (position == RecyclerView.NO_POSITION) {
            Log.d(TAG, "extendRange Position cannot be NO_POSITION.")
            return
        }

        if (mIsSelected) {
            positiveRange(position)
        } else {
            negativeRange(position)
        }
    }

    private fun negativeRange(position: Int) {
        if ((mEnd == RecyclerView.NO_POSITION) || (mBegin == mEnd)) {
            // Reset mEnd so it can be established in establishRange.
            mEnd = RecyclerView.NO_POSITION
            negativeEstablishRange(position)
        } else {
            negativeReviseRange(position)
        }
    }

    private fun negativeEstablishRange(position: Int) {
        if (mEnd != RecyclerView.NO_POSITION) {
            return
        }

        if (position > mBegin) {
            if (DEBUG) {
                Log.d(TAG, "negativeEstablishRange Establishing initial range at @ $position")
            }

            updateRange(mBegin + 1, position, mBegin, mIsSelected)
        } else if (position < mBegin) {
            if (DEBUG) {
                Log.d(TAG, "negativeEstablishRange Establishing initial range at @ $position")
            }

            updateRange(position, mBegin - 1, mBegin, mIsSelected)
        }
        mEnd = position
    }

    private fun negativeReviseRange(position: Int) {
        if (position > mEnd) {
            if (mEnd > mBegin) {
                // mBegin >> mEnd >> position
                updateRange(mEnd, position, mBegin, false)
            } else if (mEnd < mBegin) {
                // mEnd >> position >> mBegin
                if (position < mBegin) {
                    updateRange(mEnd, position, mBegin, false)
                } else if (position > mBegin) {
                    // mEnd >> mBegin >> position
                    updateRange(mEnd, position, mBegin, false)
                }
            }
        } else {
            if (mEnd > mBegin) {
                // position << mBegin << mEnd
                if (position < mBegin) {
                    updateRange(position, mEnd, mBegin, false)
                } else if (position > mBegin) {
                    // mBegin << position << mEnd
                    updateRange(position, mEnd, mBegin, false)
                }
            } else if (mEnd < mBegin) {
                // position << mEnd << mBegin
                updateRange(position, mEnd, mBegin, false)
            }
        }
        mEnd = position
    }

    private fun positiveRange(position: Int) {
        if (mOrientation == INVALID_ORIENTATION) {
            if (position > mBegin) {
                // start with Ascending expanding >> >>
                mOrientation = POSITIVE_ORIENTATION
            } else if (position < mBegin) {
                // start with descending expanding << <<
                mOrientation = NEGATIVE_ORIENTATION
            } else {
                return
            }
        }

        if (DEBUG) {
            Log.d(TAG, "extendRange position: $position, mOrientation: $mOrientation")
        }

        if ((mEnd == RecyclerView.NO_POSITION) || (mBegin == mEnd)) {
            // Reset mEnd so it can be established in establishRange.
            mEnd = RecyclerView.NO_POSITION
            positiveEstablishRange(position)
        } else {
            positiveReviseRange(position)
        }
    }

    private fun positiveEstablishRange(position: Int) {
        if (mEnd != RecyclerView.NO_POSITION) {
            return
        }

        if (position > mBegin) {
            if (DEBUG) {
                Log.d(TAG, "establishRange1 Establishing initial range at @ $position")
            }
            // begin >> >> position  (begin = end)
            if (mOrientation == POSITIVE_ORIENTATION) {
                updateRange(mBegin + 1, position, mBegin, mIsSelected)
            } else {
                updateRange(mBegin + 1, position, mBegin, !mIsSelected)
            }
        } else if (position < mBegin) {
            if (DEBUG) {
                Log.d(TAG, "establishRange2 Establishing initial range at @ $position")
            }
            // position >> >> begin   (begin = end)
            if (mOrientation == POSITIVE_ORIENTATION) {
                updateRange(position, mBegin - 1, mBegin, !mIsSelected)
            } else {
                updateRange(position, mBegin - 1, mBegin, mIsSelected)
            }
        }
        mEnd = position
    }

    private fun positiveReviseRange(position: Int) {
        if (mEnd == RecyclerView.NO_POSITION) {
            Log.d(TAG, "reviseRange End must already be set.")
            return
        }

        if (mBegin == mEnd) {
            Log.d(TAG, "reviseRange Beging and end point to same position.")
            return
        }

        if (position == mEnd) {
            if (DEBUG) {
                Log.d(TAG, "reviseRange Ignoring no-op revision for range @ $position")
            }
        }

        when (mOrientation) {
            POSITIVE_ORIENTATION -> {
                reviseAscending(position)
            }
            NEGATIVE_ORIENTATION -> {
                reviseDescending(position)
            }
        }
    }

    private fun reviseAscending(position: Int) {
        if (DEBUG) {
            Log.d(TAG, "reviseAscending position: $position")
        }

        if (position > mEnd) {
            // begin >> >> mEnd >> >> position
            if (position > mBegin) {
                updateRange(mEnd, position, mBegin, mIsSelected)
            } else if (position <= mBegin) {
                // mEnd >> >> position >> >> begin
                updateRange(mEnd, position, mBegin, mIsSelected)
            }
        } else if (position < mEnd) {
            // begin << << position << << mEnd
            if (position > mBegin) {
                updateRange(position, mEnd, mBegin, !mIsSelected)
            } else if (position <= mBegin) {
                // position << << mEnd << << begin
                updateRange(position, mEnd, mBegin, !mIsSelected)
            }
        }
        mEnd = position
    }

    private fun reviseDescending(position: Int) {
        if (DEBUG) {
            Log.d(TAG, "reviseDescending position: $position")
        }

        if (position > mEnd) {
            // begin >> >> mEnd >> >> position
            if (position > mBegin) {
                updateRange(mEnd, position, mBegin, !mIsSelected)
            } else if (position <= mBegin) {
                // mEnd >> >> position >> >> begin
                updateRange(mEnd, position, mBegin, !mIsSelected)
            }
        } else if (position < mEnd) {
            //   begin << << position << << mEnd
            if (position > mBegin) {
                updateRange(position, mEnd, mBegin, mIsSelected)
            } else if (position <= mBegin) {
                //  position << << mEnd << << begin
                updateRange(position, mEnd, mBegin, mIsSelected)
            }
        }
        mEnd = position
    }
}