/***************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * Description:Unit test shadow class
 * All rights reserved
 *
 *
 * Version: 1.0
 * Date : 2019/7/22
 * <EMAIL>
 *
 * -------------------Revision History: ----------------------
 *     <author>    <data>     <version >     <desc>
 *   lanjiabo    2019/7/22   1.0            build this moudle
 ****************************************************************/
package shadow;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.io.File;

@Implements(File.class)
public class ShadowFile {
    private static boolean sIsExists;
    @Implementation
    public boolean exists() {
        return sIsExists;
    }

    public static void setIsExists(boolean isExists) {
        sIsExists = isExists;
    }
}
