/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FilePathListsService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/18 11:12
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********        2022/7/18       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist

import android.content.Intent
import android.os.Messenger
import android.os.Handler
import android.os.Bundle
import android.os.Looper
import android.os.Message
import android.os.IBinder
import android.os.RemoteException
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Log
import com.oplus.fileservice.bean.FileResponseStatusCode
import com.oplus.fileservice.filelist.scanner.FilePathScanner
import com.filemanager.common.utils.HansFreezeManager
import com.oplus.fileservice.utils.WebFileConstant
import com.oplus.fileservice.utils.WebFileConstant.DEFAULT_PAGE_NO
import com.oplus.fileservice.utils.WebFileConstant.DEFAULT_PAGE_SIZE
import com.oplus.fileservice.utils.WebFileHelper
import com.oplus.fileservice.utils.WebGZipUtils

class FilePathListsService : BaseService() {
    companion object {
        private const val TAG = "FilePathListsService"
        private const val LOAD_FILE_PATH = "filePath"
    }

    private var mFileListServiceHandler: FileListServiceHandler? = null

    override fun onCreate() {
        Log.d(TAG, "onCreate")
        super.onCreate()
    }

    override fun onBind(intent: Intent): IBinder? {
        Log.d(TAG, "onBind() getAction = ${intent.action} , getExtras =${intent.extras}")
        HansFreezeManager.instance.keepBackgroundRunning()
        val extras = intent.extras
        extras?.apply {
            mFilePath = getString(LOAD_FILE_PATH)
            mPageNo = getInt(PAGE_NO, DEFAULT_PAGE_NO)
            mPageSize = getInt(PAGE_SIZE, DEFAULT_PAGE_SIZE)
            mSortOrder = getInt(SORT_ORDER, SortHelper.FILE_NAME_ORDER)
            isNeedThumbnail = getBoolean(NEED_THUMBNAIL, DEFAULT_NEED_THUMBNAIL)
        }
        Log.d(
            TAG,
            "mFilePath =$mFilePath, mPageNo = $mPageNo, mPageSize = $mPageSize, mSortOrder =$mSortOrder needThumbnail:$isNeedThumbnail"
        )
        if (!WebFileHelper.isLegalPathString(mFilePath)) {
            mFilePath = mInternalPath
        }
        val bundle = Bundle()
        if (!WebFileHelper.sortOrderVerify(mSortOrder)) {
            var responseResult = mGson.toJson(FileResponseStatusCode.getSortInputErrorResponseData())
            var gZipByte = WebGZipUtils.compress(responseResult)
            Log.d(TAG, "gZipByte  size =${gZipByte?.size}}")
            gZipByte?.apply {
                if (size > WebFileConstant.RESULT_MAX_SIZE) {
                    responseResult =
                        mGson.toJson(FileResponseStatusCode.getResultOverSizedResponseData())
                    gZipByte = WebGZipUtils.compress(responseResult)
                }
            }
            gZipByte?.let {
                bundle.putByteArray(WebFileConstant.DATA, it)
            }
            return Messenger(Handler(
                Looper.getMainLooper()
            ) { msg: Message ->
                val replyMsg = Message.obtain()
                replyMsg.data = bundle
                try {
                    msg.replyTo.send(replyMsg)
                } catch (e: RemoteException) {
                    e.printStackTrace()
                }
                true
            }).binder
        } else {
            val filePathLoader = FilePathScanner(mFilePath!!, mSortOrder, mPageNo, mPageSize, isNeedThumbnail)
            mFileListServiceHandler = FileListServiceHandler(filePathLoader)
            return Messenger(mFileListServiceHandler).binder
        }
    }

    override fun onUnbind(intent: Intent): Boolean {
        Log.d(TAG, "onUnbind")
        HansFreezeManager.instance.cancelFrozenDelay()
        return super.onUnbind(intent)
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy")
        super.onDestroy()
    }
}