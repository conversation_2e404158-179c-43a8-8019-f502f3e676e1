/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudFileDeleteObserver
 ** Description : 云端文件删除的Observer
 ** Version     : 1.0
 ** Date        : 2024/01/10 10:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/01/10       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.operate.delete

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import androidx.annotation.VisibleForTesting
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.base.ACTION_CANCELLED
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.oplus.filemanager.room.model.DriveFileEntity

const val SHOW_DELETE_CONFIRM_DIALOG = 0

open class CloudFileDeleteObserver(activity: Activity) : BaseFileActionObserver(activity) {

    companion object {
        private const val TAG = "CloudFileDeleteObserver"
    }

    @VisibleForTesting
    var deleteConfirmDialog: Dialog? = null

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        Log.d(TAG, "onChanged $result")
        when (result.first) {
            SHOW_DELETE_CONFIRM_DIALOG -> {
                if (result.second is FileDeleteBean) {
                    showDeleteConfirmDialog(context, result.second as FileDeleteBean)
                }
            }

            ACTION_FAILED -> {
                actionFailed(result)
                return false
            }

            ACTION_DONE, ACTION_CANCELLED -> {
                dismissDialog()
                return false
            }

            else -> return false
        }
        return true
    }

    @VisibleForTesting
    fun actionFailed(result: Pair<Any, Any>) {
        dismissDialog()
        // 删除失败的提示
        val isAuth = result.second as? Boolean ?: true
        if (isAuth) {
            CustomToast.showShort(com.filemanager.common.R.string.delete_docs_failure)
        }
    }

    /**
     * 显示删除确认弹窗
     */
    @VisibleForTesting
    fun showDeleteConfirmDialog(context: Context, deleteBean: FileDeleteBean) {
        Log.d(TAG, "showDeleteConfirmDialog $deleteBean")
        deleteConfirmDialog = COUIAlertDialogBuilder(context)
            .setCancelable(false)
            .setOnCancelListener { deleteBean.onClickListener }
            .setTitle(getDeleteDialogTitle(context, deleteBean.count))
            .setMessage(getDeleteDialogMessage(context, deleteBean))
            .setNeutralButton(com.filemanager.common.R.string.menu_file_list_delete, deleteBean.onClickListener)
            .setNegativeButton(com.filemanager.common.R.string.alert_dialog_cancel, deleteBean.onClickListener)
            .show()
    }

    /**
     * 获取删除弹窗的标题
     */
    private fun getDeleteDialogTitle(context: Context, count: Int): String {
        return if (count == 1) {
            context.getString(com.filemanager.common.R.string.delete_docs_confirm_title)
        } else {
            context.resources.getQuantityString(com.filemanager.common.R.plurals.delete_several_docs_confirm_title, count, count)
        }
    }

    /**
     * 获取删除弹窗的message
     */
    private fun getDeleteDialogMessage(context: Context, deleteBean: FileDeleteBean): String {
        val app = if (deleteBean.isTencentDoc()) {
            context.getString(com.filemanager.common.R.string.tencent_docs)
        } else {
            context.getString(com.filemanager.common.R.string.kdocs)
        }
        return if (deleteBean.count == 1) {
            context.getString(com.filemanager.common.R.string.delete_docs_confirm_desc, app)
        } else {
            context.resources.getQuantityString(
                com.filemanager.common.R.plurals.delete_several_docs_confirm_desc,
                deleteBean.count,
                "${deleteBean.count}", app
            )
        }
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || deleteConfirmDialog?.isShowing ?: false
    }

    override fun recycle() {
        dismissDialog()
        super.recycle()
    }

    @VisibleForTesting
    fun dismissDialog() {
        if (deleteConfirmDialog?.isShowing == true) {
            deleteConfirmDialog?.dismiss()
        }
    }

    data class FileDeleteBean(var count: Int, var source: String, var onClickListener: DialogInterface.OnClickListener?) {
        /**
         * 是否是腾讯文档
         */
        fun isTencentDoc(): Boolean {
            return DriveFileEntity.SOURCE_TYPE_TENCENT == source
        }
    }
}