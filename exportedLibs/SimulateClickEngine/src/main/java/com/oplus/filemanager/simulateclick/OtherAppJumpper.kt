/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OtherAppJumpper
 ** Description : 封装了跳转3方应用的弹窗+自动模拟点击逻辑
 ** Version     : 1.0
 ** Date        : 2024/05/22 10:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/06       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.simulateclick

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.ComponentName
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.view.View
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.oplus.filemanager.simulateclick.ui.util.CommonUtil
import com.oplus.filemanager.simulateclick.engine.utils.Log
import com.oplus.filemanager.simulateclick.sdk.R
import com.oplus.filemanager.simulateclick.ui.IconWrapSpan
import com.oplus.filemanager.simulateclick.ui.util.CommonUtil.DINGTALK_PACKAGE
import com.oplus.filemanager.simulateclick.ui.util.CommonUtil.FEISHU_PACKAGE
import com.oplus.filemanager.simulateclick.ui.util.CommonUtil.QQ_PACKAGE
import com.oplus.filemanager.simulateclick.ui.util.CommonUtil.WECHAT_PACKAGE
import com.oplus.filemanager.simulateclick.ui.util.CommonUtil.WEWORK_PACKAGE
import com.oplus.filemanager.simulateclick.ui.util.Util

class OtherAppJumpper {

    companion object {
        const val TAG = "OtherAppJumpper"
        private const val EXTRACHAR = 4
    }

    fun onClickThirdAppFile(activity: Context, dataBean: InputDataBean) {
        val positiveItemString = activity.getString(R.string.common_app_jump_dialog_positive_button)
        val thirdAppName = CommonUtil.getThirdAppNameForPackage(activity, dataBean.packageName)
        val buttonString = String.format(positiveItemString, thirdAppName)
        val bottomSpanString = getButtonSpanString(activity, buttonString, dataBean.packageName)
        Log.d(TAG, "onClickThirdAppFile dataBean $dataBean, thirdAppName $thirdAppName, button:$buttonString span:$bottomSpanString")
        val positiveButtonClick = DialogInterface.OnClickListener { dialog, which ->
            processCopyAndJumpThirdApp(
                activity,
                dataBean,
                thirdAppName
            )
        }
        val dialog = COUIAlertDialogBuilder(activity).run {
            setTitle(R.string.common_app_jump_dialog_title)
            setMessage(R.string.common_app_jump_dialog_description)
            setPositiveButton(bottomSpanString, positiveButtonClick, true)
            setNegativeButton(R.string.common_app_jump_dialog_cancel_button, null)
            setBlurBackgroundDrawable(true)
            show()
        }
        val positiveButton = dialog.findViewById<COUIButton>(android.R.id.button1)
        val negativeButton = dialog.findViewById<Button>(android.R.id.button2)
        val colorId = CommonUtil.getDialogColorButtonColors(activity, dataBean.packageName)
        Log.i(TAG, "onClickThirdAppFile positiveButton $positiveButton, colorInt $colorId")
        positiveButton?.setDrawableColor(colorId)
        positiveButton?.setStrokeColor(colorId)
        positiveButton?.invalidate()
        negativeButton?.setTextColor(colorId)
    }


    private fun getButtonSpanString(
        context: Context,
        inputString: String,
        packageName: String
    ): Spannable {
        val span = SpannableString(inputString)
        val sourceIcon = CommonUtil.getSourceIcon(context, packageName)
        val paddingPx = context.resources.getDimensionPixelSize(R.dimen.dialog_padding_between_icon_text)
        val bitmap = CommonUtil.getBitmapFromDrawable(sourceIcon) ?: return span
        val imgSpan = IconWrapSpan(bitmap, paddingPx)
        val ssb = SpannableStringBuilder(inputString).apply {
            setSpan(imgSpan, 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        return ssb
    }

    private fun processCopyAndJumpThirdApp(
        context: Context,
        fileItem: InputDataBean,
        thirdAppName: String?
    ) {
        val packageName = fileItem.packageName
        val isThirdAppHide = CommonUtil.checkAppInHideMode(packageName)
        val isThirdAppNotExist = !CommonUtil.isAppInstalledByPkgName(context, packageName)
        if (isThirdAppHide || isThirdAppNotExist) {
            showNotFoundText(context)
            Log.w(TAG, "processCopyAndJumpThirdApp App hide $isThirdAppHide, not Install $isThirdAppNotExist, packageName $packageName")
        } else {
            saveDataToClipBoard(context, fileItem.fileName)
            jumpToThirdAppAuto(context, fileItem.packageName, fileItem.fileName ?: "")
            showToast(context, thirdAppName)
        }
    }

    private fun showNotFoundText(context: Context) {
        val appNotFoundString = context.getString(R.string.common_app_jump_toast_app_not_found)
        val sToast = Toast.makeText(context, appNotFoundString, Toast.LENGTH_SHORT)
        sToast.show()
    }

    private fun showToast(context: Context, thirdAppName: String?) {
        val positiveItemString = context.getString(R.string.common_app_jump_toast)
        thirdAppName?.let {
            val toastString = String.format(positiveItemString, thirdAppName)
            val sToast = Toast.makeText(context, toastString, Toast.LENGTH_SHORT)
            sToast.show()
        }
    }


    @SuppressLint("ClipboardManagerDetector")
    private fun saveDataToClipBoard(context: Context, fileName: String?) {
        fileName?.let {
            val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clipData = ClipData.newPlainText("label", fileName)
            clipboardManager.setPrimaryClip(clipData)
        }
    }


    private fun initCumstomLayout(
        customLayout: View,
        fileName: String
    ) {
        val titltTv = customLayout.findViewById<TextView>(R.id.title_tv)
        titltTv.post {
            titltTv.text = Util.setDisplayNameTextByLine(fileName, titltTv, EXTRACHAR) ?: ""
        }
    }

    private fun jumpToThirdAppAuto(context: Context, packageName: String, fileName: String) {
        Log.i(TAG, "jumpToThirdAppAuto $packageName, fileName $fileName")
        SimulateClickManager.callOnApp(packageName)
            .openSearchFile(fileName)
            .onDetectedCallback { hasScriptDetected ->
                Log.i(TAG, "jumpToThirdAppAuto: onScriptDetected=$hasScriptDetected")
                if (!hasScriptDetected) {
                    jumpToThirdAppLauncher(context, packageName)
                }
            }.execute(context)
    }

    private fun jumpToThirdAppLauncher(context: Context, packageName: String) {
        when (packageName) {
            FEISHU_PACKAGE -> jumpToFeishu(context)
            QQ_PACKAGE -> jumpToQQ(context)
            WECHAT_PACKAGE -> jumpToWechat(context)
            WEWORK_PACKAGE -> jumpToWeWork(context)
            DINGTALK_PACKAGE -> jumpToDingTalk(context)
        }
    }


    private fun jumpToWeWork(context: Context) {
        //后面调试的时候新增
        val intent = Intent(Intent.ACTION_MAIN)
        val component =
            ComponentName(WEWORK_PACKAGE, "com.tencent.wework.launch.LaunchSplashActivity")
        intent.addCategory(Intent.CATEGORY_LAUNCHER)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.component = component
        runCatching {
            context.startActivity(intent)
        }.onFailure {
            Log.e(TAG, "jumpToWeWork error", it)
        }
    }

    private fun jumpToDingTalk(context: Context) {
        //后面调试的时候新增
        val intent = Intent(Intent.ACTION_MAIN)
        val component =
            ComponentName(DINGTALK_PACKAGE, "com.alibaba.android.rimet.biz.LaunchHomeActivity")
        intent.addCategory(Intent.CATEGORY_LAUNCHER)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.component = component
        runCatching {
            context.startActivity(intent)
        }.onFailure {
            Log.e(TAG, "jumpToDingTalk error", it)
        }
    }

    private fun jumpToFeishu(context: Context) {
        val intent = Intent(Intent.ACTION_MAIN)
        val component = ComponentName(FEISHU_PACKAGE, "com.ss.android.lark.main.app.MainActivity")
        intent.addCategory(Intent.CATEGORY_LAUNCHER)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.component = component
        runCatching {
            context.startActivity(intent)
        }.onFailure {
            Log.e(TAG, "jumpToFeishu error", it)
        }
    }


    private fun jumpToQQ(context: Context) {
        val intent = Intent()
        intent.action = "com.tencent.mobileqq.action.MAINACTIVITY"
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        runCatching {
            context.startActivity(intent)
        }.onFailure {
            Log.e(TAG, "jumpToQQ error", it)
        }
    }


    private fun jumpToWechat(context: Context) {
        val intent = Intent(Intent.ACTION_MAIN)
        val component = ComponentName(WECHAT_PACKAGE, "com.tencent.mm.ui.LauncherUI")
        intent.addCategory(Intent.CATEGORY_LAUNCHER)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.component = component
        runCatching {
            context.startActivity(intent)
        }.onFailure {
            Log.e(TAG, "jumpToWechat error", it)
        }
    }
}