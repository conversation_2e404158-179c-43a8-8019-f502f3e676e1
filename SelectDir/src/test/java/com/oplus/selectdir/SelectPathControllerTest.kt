/*********************************************************************
 * * Copyright (C), 2010-2029 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.selectdir.SelectPathControllerTest
 * * Description : SelectPathControllerTest
 * * Version     : 1.0
 * * Date        : 2023/11/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import com.filemanager.common.base.BaseVMPanelFragment
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.Assert
import org.junit.Test

class SelectPathControllerTest {

    @Test
    fun `should return null when call onStoragePermissionChange if supportFragmentManager is null`() {
        //given
        val lifecycle = mockk<Lifecycle>(relaxed = true)
        val selectPathController = SelectPathController(lifecycle)
        selectPathController.mSelectionPathFragment = null
        //when
        selectPathController.onStoragePermissionChange(true, null)
        //then
        Assert.assertEquals(selectPathController.mSelectionPathFragment, null)
    }

    @Test
    fun `should return null when call onStoragePermissionChange if supportFragmentManager fragments is null`() {
        //given
        val lifecycle = mockk<Lifecycle>(relaxed = true)
        val selectPathController = SelectPathController(lifecycle)
        val supportFragmentManager = mockk<FragmentManager>()
        val fragments = mockk<List<Fragment>>()
        every { supportFragmentManager.fragments } returns fragments
        every { fragments.isEmpty() } returns true
        selectPathController.mSelectionPathFragment = null
        //when
        selectPathController.onStoragePermissionChange(true, supportFragmentManager)
        //then
        Assert.assertEquals(selectPathController.mSelectionPathFragment, null)
    }

    @Test
    fun `should return null when call onStoragePermissionChange if supportFragmentManager fragments is not null`() {
        //given
        val lifecycle = mockk<Lifecycle>(relaxed = true)
        val selectPathController = SelectPathController(lifecycle)
        val supportFragmentManager = mockk<FragmentManager>()
        val fragments = mockk<List<Fragment>>()
        every { supportFragmentManager.fragments } returns fragments
        every { fragments.isEmpty() } returns false
        val selectPathDialogFragment = mockk<SelectPathDialogFragment>()
        every { fragments[0] } returns selectPathDialogFragment
        selectPathController.mSelectionPathFragment = null
        every { selectPathDialogFragment.isAdded } returns false
        //when
        selectPathController.onStoragePermissionChange(true, supportFragmentManager)
        //then
        Assert.assertEquals(selectPathController.mSelectionPathFragment, selectPathDialogFragment)
    }

    @Test
    fun `should return null when call onStoragePermissionChange if mSelectionPathFragment valid`() {
        //given
        val lifecycle = mockk<Lifecycle>(relaxed = true)
        val selectPathController = SelectPathController(lifecycle)
        val selectPathDialogFragment = mockk<SelectPathDialogFragment>()
        every { selectPathDialogFragment.isAdded } returns true
        selectPathController.mSelectionPathFragment = selectPathDialogFragment
        val childFragmentManager = mockk<FragmentManager>()
        every { selectPathDialogFragment.childFragmentManager } returns childFragmentManager
        val fragments = mockk<List<Fragment>>()
        every { childFragmentManager.fragments } returns fragments
        every { fragments.isEmpty() } returns false
        val baseVMPanelFragment = mockk<BaseVMPanelFragment<*>>()
        every { fragments[0] } returns baseVMPanelFragment
        every { baseVMPanelFragment.isAdded } returns true
        every { baseVMPanelFragment.isShowPermissionEmptyView } returns true
        every { baseVMPanelFragment.setPermissionEmptyVisible(any()) } just runs
        every { baseVMPanelFragment.onResumeLoadData() } just runs

        //when
        selectPathController.onStoragePermissionChange(true, null)
        //then
        verify { baseVMPanelFragment.setPermissionEmptyVisible(any()) }
        verify { baseVMPanelFragment.onResumeLoadData() }
    }

    @Test
    fun `should return null when call onStoragePermissionChange if mSelectionPathFragment valid and hasStoragePermission is false`() {
        //given
        val lifecycle = mockk<Lifecycle>(relaxed = true)
        val selectPathController = SelectPathController(lifecycle)
        val selectPathDialogFragment = mockk<SelectPathDialogFragment>()
        every { selectPathDialogFragment.isAdded } returns true
        selectPathController.mSelectionPathFragment = selectPathDialogFragment
        val childFragmentManager = mockk<FragmentManager>()
        every { selectPathDialogFragment.childFragmentManager } returns childFragmentManager
        val fragments = mockk<List<Fragment>>()
        every { childFragmentManager.fragments } returns fragments
        every { fragments.isEmpty() } returns false
        val baseVMPanelFragment = mockk<BaseVMPanelFragment<*>>()
        every { fragments[0] } returns baseVMPanelFragment
        every { baseVMPanelFragment.isAdded } returns true
        every { baseVMPanelFragment.isShowPermissionEmptyView } returns false
        every { baseVMPanelFragment.setPermissionEmptyVisible(any()) } just runs
        every { baseVMPanelFragment.onResumeLoadData() } just runs

        //when
        selectPathController.onStoragePermissionChange(false, null)
        //then
        verify { baseVMPanelFragment.setPermissionEmptyVisible(any()) }
    }
}