/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.adapter
 * * Version     : 1.0
 * * Date        : 2020/4/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.superapp.ui.superapp

import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.RecyclerView
import com.android.documentsui.DocumentUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NewFunctionSwitch.SUPPORT_PRIVATE_DIRECTORY_ENTRY_PROMPT
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.isActivityAndInvalid
import com.filemanager.common.viewholder.FileBrowserGridVH
import com.filemanager.common.viewholder.FileBrowserLargeListVH
import com.filemanager.common.viewholder.FileDocGridVH
import com.filemanager.common.viewholder.NormalGridVH
import com.filemanager.common.viewholder.NormalListVH
import com.filemanager.superapp.R
import com.filemanager.superapp.ui.utils.hyperlinkText
import com.filemanager.superapp.ui.utils.hyperlinkTextWithApp
import com.filemanager.superappsource.helper.SuperAppHelper
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.QQ
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.WECHAT
import java.io.File
import java.util.Locale

private const val TAG = "SuperListAdapter"

class SuperListAdapter : BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>, LifecycleObserver {
    companion object {
        const val VIEW_TYPE_ITEM_FOOTER = 999
        private const val ALPHA = 31
        private const val ANIM_DURATION = 1500L
    }
    var mScanViewModel = KtConstants.SCAN_MODE_GRID
        set(value) {
            field = value
            if (value == KtConstants.SCAN_MODE_GRID) {
                (mContext as? Activity)?.let {
                    mImgItemWith = ItemDecorationFactory.getGridItemWidth(it, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM)
                    mDocItemWith = ItemDecorationFactory.getGridItemWidthForDocWithWps(it)
                }
            }
        }
    private var mIsRtl = false
    @VisibleForTesting
    var mKeyWord: String? = null
    @VisibleForTesting
    val mSizeCache = HashMap<String, String>()
    @VisibleForTesting
    val mUiHandler: Handler
    @VisibleForTesting
    var mThreadManager: ThreadManager
    @VisibleForTesting
    val mTabPosition: Int
    private var mImgItemWith: Int = 0
    private var mDocItemWith: Int = 0
    @VisibleForTesting
    val mImgRadius = MyApplication.appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.file_list_bg_radius)
    @VisibleForTesting
    var isAddFootView: Boolean = false
    var mSuperAppPackage: String? = null
        set(value) {
            field = value
            checkFootViewByPackage(mContext)
        }
    var isOWork = false
    private var externalPath: String? = null

    constructor(content: Context, tabPosition: Int, lifecycle: Lifecycle, superAppPackage: String?, oWork: Boolean) : super(content) {
        mIsRtl = Utils.isRtl()
        mUiHandler = Handler(Looper.getMainLooper())
        mThreadManager = ThreadManager(lifecycle)
        mTabPosition = tabPosition
        lifecycle.addObserver(this)
        mSuperAppPackage = superAppPackage
        isOWork = oWork
        checkFootViewByPackage(content)
    }

    /** 通过包名判断是否显示底部跳转到DocumentUI连接 */
    private fun checkFootViewByPackage(content: Context) {
        isAddFootView = SuperAppHelper.getAppFlag(content, mSuperAppPackage) != null || isOWork
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(
        data: ArrayList<BaseFileBean>,
        selectionArray: ArrayList<Int>,
        externalPath: String?
    ) {
        mFiles = data
        mSelectionArray = selectionArray
        this.externalPath = externalPath
        notifyDataSetChanged()
        mIsRtl = Utils.isRtl()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_ITEM_FOOTER ->
                FootViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.super_app_footer_item, parent, false))
            KtConstants.SCAN_MODE_LIST -> {
                NormalListVH(LayoutInflater.from(parent.context).inflate(NormalListVH.getLayoutId(), parent, false), mImgRadius)
            }
            KtConstants.SCAN_MODE_LIST_LARGE -> FileBrowserLargeListVH.create(parent, mImgRadius)

            (KtConstants.SCAN_MODE_GRID + Constants.TAB_ALL) -> {
                FileBrowserGridVH(LayoutInflater.from(parent.context).inflate(FileBrowserGridVH.getLayoutId(), parent, false))
            }
            (KtConstants.SCAN_MODE_GRID + Constants.TAB_IMAGE) -> {
                NormalGridVH(LayoutInflater.from(parent.context).inflate(NormalGridVH.getLayoutId(), parent, false))
            }
            (KtConstants.SCAN_MODE_GRID + Constants.TAB_VIDEO) -> {
                NormalGridVH(LayoutInflater.from(parent.context).inflate(NormalGridVH.getLayoutId(), parent, false))
            }
            (KtConstants.SCAN_MODE_GRID + Constants.TAB_AUDIO) -> {
                return FileBrowserGridVH(LayoutInflater.from(parent.context).inflate(FileBrowserGridVH.getLayoutId(), parent, false))
            }
            (KtConstants.SCAN_MODE_GRID + Constants.TAB_DOCUMENT) -> {
                FileBrowserGridVH(LayoutInflater.from(parent.context).inflate(FileBrowserGridVH.getLayoutId(), parent, false))
            }
            (KtConstants.SCAN_MODE_GRID + Constants.TAB_OTHER) -> {
                FileBrowserGridVH(LayoutInflater.from(parent.context).inflate(FileBrowserGridVH.getLayoutId(), parent, false))
            }
            else -> {
                NormalListVH(LayoutInflater.from(parent.context).inflate(NormalListVH.getLayoutId(), parent, false), mImgRadius)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if ((position < 0) || (position >= itemCount)) {
            return
        }
        if (mContext.isActivityAndInvalid()) {
            Log.d(TAG, "onBindViewHolder: Activity is Destroyed!")
            return
        }
        if (holder is FootViewHolder) {
            holder.loadData(mContext, mScanViewModel, isOWork, mSuperAppPackage, mTabPosition)
            return
        }
        val file = mFiles[position]
        when (holder) {
            is NormalListVH -> {
                holder.updateDividerVisible(mFiles.size - 1, position)
                holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
                holder.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
            }
            is FileBrowserLargeListVH -> {
                holder.updateDividerVisible(mFiles.size - 1, position)
                holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
                holder.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
            }
            is FileBrowserGridVH -> {
                holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
                externalPath?.let {
                    holder.setExternalPath(externalPath)
                    externalPath = null
                }
            }
            is FileDocGridVH -> {
                holder.adaptItemSize(mDocItemWith)
                holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
            }
            is NormalGridVH -> {
                holder.setItemWidth(mImgItemWith)
                holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
            }
        }
        if (position == 0 && externalPath != null) {
            animateBackground(holder.itemView)
            externalPath = null
        }
        holder.itemView?.let {
            if (DragUtils.getSelectedFiles()?.contains(file) == true && DragUtils.isDragging) {
                it.alpha = DragUtils.SELECTED_ITEMVIEW_ALPHA
            } else {
                it.alpha = 1f
            }
        }
    }


    private fun animateBackground(view: View) {
        // 原始颜色
        val originalColor = (view.background as? ColorDrawable)?.color ?: Color.TRANSPARENT

        // 目标颜色：#000000 透明度 12%
        val targetColor = Color.argb(ALPHA, 0, 0, 0) // 31 对应透明度为 12%（255 * 0.12）

        // 创建颜色渐变动画
        val colorAnimator =
            ValueAnimator.ofObject(ArgbEvaluator(), originalColor, targetColor, originalColor)
                .apply {
                    duration = ANIM_DURATION // 动画持续时间为1秒
                    addUpdateListener { animator ->
                        view.setBackgroundColor(animator.animatedValue as Int)
                    }
                }

        colorAnimator.start()
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        mSizeCache.clear()
        mUiHandler.removeCallbacksAndMessages(null)
    }

    fun setKeyWord(key: String) {
        mKeyWord = key
    }

    override fun getItemKey(item: BaseFileBean, position: Int): Int {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return position
        }

        val key = path.lowercase(Locale.getDefault()).hashCode()
        Log.d(TAG, "getItemKey key: $key ")
        return key
    }

    override fun getItemId(position: Int): Long {
        return getItemKeyByPosition(position)?.toLong() ?: com.oplus.dropdrag.SelectionTracker.NO_LONG_ITEM_ID
    }

    override fun getItemViewType(position: Int): Int {
        if (position >= mFiles.size) {
            return VIEW_TYPE_ITEM_FOOTER
        }
        return if (mScanViewModel == KtConstants.SCAN_MODE_LIST) {
            if (WindowUtils.supportLargeScreenLayout(mContext)) {
                KtConstants.SCAN_MODE_LIST_LARGE
            } else {
                KtConstants.SCAN_MODE_LIST
            }
        } else {
            mScanViewModel + mTabPosition
        }
    }

    override fun getItemCount(): Int {
        if (isAddFootView) {
            return super.getItemCount() + 1
        }
        return super.getItemCount()
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        if (mScanViewModel == KtConstants.SCAN_MODE_LIST) {
            setChoiceModeAnimFlag(flag)
        }
    }

    internal class FootViewHolder(
        itemView: View,
    ) : RecyclerView.ViewHolder(itemView) {
        companion object {
            private const val PRIVATE_PATH = "Android"
            private const val PRIVATE_PATH2 = "data"
        }

        fun loadData(context: Context, scanViewMode: Int, isOWork: Boolean, superAppPackage: String?, tabPosition: Int) {
            if (superAppPackage == null) {
                return
            }
            val footTextView: TextView? = itemView.findViewById(R.id.footer_view)
            if (isOWork) {
                footTextView?.hyperlinkText(
                    context,
                    hyperlinkTextId = KtUtils.getOWorkNameResId(KtUtils.OWORK_NAME_TYPE_1),
                    all = com.filemanager.common.R.string.owork_space_desc_one,
                    extra = com.filemanager.common.R.string.owork_space_desc_two
                ) {
                    KtAppUtils.startOWork(context, KtAppUtils.ENTRANCE_FILE_MANAGER_WORK_SPACE)
                }
                footTextView?.gravity = Gravity.START
                footTextView?.let {
                    resetItemViewPadding(it, scanViewMode == KtConstants.SCAN_MODE_GRID, tabPosition)
                }
            } else {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
                    return
                }
                // 功能关闭时，使用旧功能
                if (!SUPPORT_PRIVATE_DIRECTORY_ENTRY_PROMPT) {
                    footTextView?.hyperlinkText(
                        context,
                        com.filemanager.common.R.string.all_system_files,
                        com.filemanager.common.R.string.file_not_found_tips
                    ) {
                        clickAction(context, superAppPackage)
                    }
                    return
                }
                val appStringRes = when (superAppPackage) {
                    WECHAT -> com.filemanager.common.R.string.string_wechat
                    QQ -> com.filemanager.common.R.string.string_qq
                    else -> 0
                }
                if (appStringRes == 0) {
                    footTextView?.hyperlinkText(
                        context,
                        com.filemanager.common.R.string.all_system_files,
                        com.filemanager.common.R.string.file_not_found_tips
                    ) {
                        clickAction(context, superAppPackage)
                    }
                } else {
                    footTextView?.hyperlinkTextWithApp(
                        context,
                        com.filemanager.common.R.string.all_system_files,
                        com.filemanager.common.R.string.privacy_file_not_found_tips,
                        appStringRes
                    ) {
                        clickAction(context, superAppPackage)
                    }
                }
            }
        }

        private fun clickAction(context: Context, superAppPackage: String) {
            val privatePath = SuperAppHelper.getPrivatePath(superAppPackage)
            var path = Environment.getExternalStorageDirectory().absolutePath + File.separator +
                    PRIVATE_PATH + File.separator + PRIVATE_PATH2 + File.separator + superAppPackage
            if (privatePath.isNotEmpty()) {
                path = path + File.separator + privatePath
            }
            val intent = DocumentUtil.getIntent(path)
            DocumentUtil.startDocumentsUIIntent(context, intent)
            OptimizeStatisticsUtil.clickAllSystemFile(OptimizeStatisticsUtil.signalToPage(superAppPackage))
        }

        private fun resetItemViewPadding(textView: TextView, isGrid: Boolean, tabPosition: Int) {
            Log.d(TAG, "resetItemViewPadding -> isGrid = $isGrid")
            textView.let {
                /**
                 * In the video and image tabs, cause [RecyclerView.ItemDecoration] modifies the padding effect,
                 * we need add a 16dp start margin at footView to keep the tip positions consistent.
                 */
                if (isGrid && !isImageOrVideoTab(tabPosition)) {
                    it.setPadding(0, it.paddingTop, it.paddingRight, it.paddingBottom)
                } else {
                    it.setPadding(
                        MyApplication.appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp),
                        it.paddingTop,
                        it.paddingRight,
                        it.paddingBottom
                    )
                }
            }
        }

        @VisibleForTesting
        fun isImageOrVideoTab(tabPosition: Int): Boolean {
            return tabPosition == Constants.TAB_VIDEO || tabPosition == Constants.TAB_IMAGE
        }
    }
}
