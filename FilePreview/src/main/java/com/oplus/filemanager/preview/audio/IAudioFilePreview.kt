/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IAudioFilePreview.kt
 * Description:
 *     The interface to play audio to preview it.
 *
 * Version: 1.0
 * Date: 2024-10-09
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-10-09   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.audio

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.oplus.filemanager.preview.core.IPreviewContainerManager
import com.oplus.filemanager.preview.core.IUiConfigChangeObserver
import com.oplus.filemanager.preview.widget.PreviewAudioCard

internal interface IAudioFilePreview : IUiConfigChangeObserver {

    /**
     * Attach to container views and play audio.
     */
    fun attachToContainer(
        audioContainer: PreviewAudioCard,
        containerManager: IPreviewContainerManager
    )

    /**
     * Release when not use it anymore.
     */
    fun release()

    companion object {

        @JvmStatic
        fun obtain(
            context: Context,
            lifecycleOwner: LifecycleOwner,
            previewModel: AudioPreviewViewModel
        ): IAudioFilePreview = AudioFilePreviewImpl(context, lifecycleOwner, previewModel)
    }
}