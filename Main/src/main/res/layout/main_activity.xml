<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:splitMotionEvents="false">

    <com.oplus.filemanager.main.view.ViewPager2Container
        android:id="@+id/view_pager_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/navigation_tab"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?attr/couiColorBackgroundWithCard"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:orientation="horizontal"
            android:overScrollMode="never" />
    </com.oplus.filemanager.main.view.ViewPager2Container>

    <com.filemanager.common.view.NavigationView
        android:id="@+id/navigation_tab"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:couiNaviMenu="@menu/navigation_tab_home"
        app:couiNaviTextColor="@color/navigation_label_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:navigationType="tab" />

    <com.filemanager.common.view.NavigationView
        android:id="@+id/navigation_tool_for_recent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:navigationType="tool" />

    <com.filemanager.common.view.NavigationView
        android:id="@+id/navigation_tool_for_label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:navigationType="tool" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_cover_anima_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/anima_view_background"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

        </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>