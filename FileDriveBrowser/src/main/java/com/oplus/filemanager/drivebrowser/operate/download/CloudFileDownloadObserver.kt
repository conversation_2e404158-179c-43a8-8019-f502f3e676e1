/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudFileDownloadObserver
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/15 15:59
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/15       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.operate.download

import android.app.Activity
import android.content.Context
import com.filemanager.common.utils.COUISnackBarUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser

const val DOWNLOAD_CANCEL = 7
const val DOWNLOAD_ERROR = 8
const val DOWNLOAD_FILE_ERROR = 9
const val ACTION_DOWNLOAD = 100

open class CloudFileDownloadObserver(activity: Activity) : BaseFileActionObserver(activity) {

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        Log.d(TAG, "onChanged -> result = ${result.first} ; ${result.second}")
        when (result.first) {
            ACTION_FAILED -> {
                when (result.second) {
                    DOWNLOAD_ERROR -> CustomToast.showShort(com.filemanager.common.R.string.download_failure_toast)
                    DOWNLOAD_FILE_ERROR -> CustomToast.showShort(com.filemanager.common.R.string.download_file_error)
                }
            }

            ACTION_DONE -> {
                Log.d(TAG, "onChanged -> isActivity = ${context is Activity}, isString = ${result.second is String}")
                if ((context is Activity) && (result.second is String)) {
                    COUISnackBarUtils.show(context, com.filemanager.common.R.string.download_success_toast) {
                        val fileBrowser = Injector.injectFactory<IFileBrowser>()
                        fileBrowser?.toFileBrowserActivity(context, result.second as String)
                    }
                } else {
                    CustomToast.showShort(com.filemanager.common.R.string.download_success_toast)
                }
            }

            ACTION_DOWNLOAD -> onActionDownload((result.second as? Boolean) ?: false)

            else -> return false
        }
        return false
    }

    open fun onActionDownload(isAuth: Boolean) {
        //do nothing
    }

    companion object {
        private const val TAG = "CloudFileDownloadObserver"
    }
}