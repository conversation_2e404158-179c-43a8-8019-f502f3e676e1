package com.oplus.filemanager.apiproxy.services.loader

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.base.UriFileWrapper
import com.oplus.filemanager.apiproxy.services.cache.SearchFileSizeCache
import com.oplus.filemanager.apiproxy.services.cursor.SearchCursor
import com.oplus.filemanager.apiproxy.services.data.SearchMediaBean
import com.oplus.filemanager.apiproxy.services.data.SearchResultData
import com.oplus.filemanager.apiproxy.services.utils.DeeplinkUtils
import com.oplus.filemanager.apiproxy.services.utils.SearchChecker
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

/**
 * SearchDocLoader的单元测试类
 * 用于测试SearchDocLoader的各种功能场景
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SearchDocLoaderTest {

    // 模拟的Context对象
    private lateinit var context: Context
    // 模拟的ContentResolver对象
    private lateinit var contentResolver: ContentResolver
    // 被测试的SearchDocLoader实例
    private lateinit var searchDocLoader: SearchDocLoader

    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 创建模拟的Context和ContentResolver
     * 3. 创建被测试的SearchDocLoader实例
     * 4. 清空文件大小缓存
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true)
        contentResolver = mockk(relaxed = true)
        searchDocLoader = SearchDocLoader(context)
        SearchFileSizeCache.clear() // 确保缓存是干净的
    }

    /**
     * 测试后的清理方法
     * 1. 解除所有MockK的mock
     * 2. 清除所有mock对象
     */
    @After
    fun tearDown() {
        unmockkAll()
        clearAllMocks()
    }

    /**
     * 测试query方法在queryArgs为null时的行为
     * 预期: 返回null
     */
    @Test
    fun `query should return null when queryArgs is null`() {
        val result = searchDocLoader.query(contentResolver, null)
        assertNull(result)
    }

    /**
     * 测试当fileType无效时query方法的行为
     * 1. 创建包含无效fileType的Bundle
     * 2. mock SearchChecker的检查方法返回错误码
     * 3. 验证返回的错误Cursor
     * 4. 验证相关方法被正确调用
     */
    @Test
    fun `query should return error cursor when fileType is invalid`() {
        val bundle = Bundle().apply {
            putString(SearchDocLoader.SEARCH_FILE_KEYWORD, "test")
            putInt(SearchDocLoader.SEARCH_FILE_TYPE, -1)
        }

        mockkObject(SearchChecker)
        every { SearchChecker.checkFileType(any()) } returns -1
        every { SearchChecker.isOK(any()) } returns false

        val result = searchDocLoader.query(contentResolver, bundle)

        assertNotNull(result)
        verify { SearchChecker.checkFileType(-1) }
        verify { SearchChecker.isOK(-1) }
    }

    /**
     * 测试当搜索结果有错误时query方法的行为
     * 1. 创建测试Bundle
     * 2. mock SearchChecker返回初始检查通过
     * 3. mock SearchResultData返回错误码
     * 4. 通过反射修改被测试对象的searchResult字段
     * 5. 验证返回的错误Cursor
     * 6. 验证相关方法被正确调用
     */
    @Test
    fun `query should return error cursor when search result has error`() {
        val bundle = Bundle().apply {
            putString(SearchDocLoader.SEARCH_FILE_KEYWORD, "test")
            putInt(SearchDocLoader.SEARCH_FILE_TYPE, 1)
        }

        mockkObject(SearchChecker)
        every { SearchChecker.checkFileType(any()) } returns SearchChecker.CODE_OK
        every { SearchChecker.isOK(any()) } returnsMany listOf(true, false)

        val searchResultData = mockk<SearchResultData>(relaxed = true)
        every { searchResultData.getResultCode() } returns -1

        val searchResultField = SearchDocLoader::class.java.getDeclaredField("searchResult")
        searchResultField.isAccessible = true
        searchResultField.set(searchDocLoader, searchResultData)

        val result = searchDocLoader.query(contentResolver, bundle)

        assertNotNull(result)
        verify(exactly = 2) { SearchChecker.isOK(any()) }
        verify { searchResultData.getResultCode() }
    }

    /**
     * 测试createCursor方法处理混合类型列表的能力
     * 1. 创建UriFileWrapper和RemoteFileBean的mock对象
     * 2. mock SearchMediaBean的构造方法
     * 3. mock GsonUtil的toJson方法
     * 4. 验证返回的Cursor不为null
     * 5. 验证SearchMediaBean构造方法未被调用(因为使用了mock)
     */
    @Test
    fun `createCursor should handle mixed list of UriFileWrapper and RemoteFileBean`() {
        val uriFile = mockk<UriFileWrapper>(relaxed = true).apply {
            every { id } returns 1
            every { mData } returns "/path/to/uriFile"
            every { mDisplayName } returns "uriFile.txt"
            every { mSize } returns 1024L
            every { mDateModified } returns 1234567890L
            every { mMimeType } returns "text/plain"
        }

        val remoteFile = mockk<RemoteFileBean>(relaxed = true).apply {
            every { mData } returns "/path/to/remoteFile"
            every { mDisplayName } returns "remoteFile.txt"
            every { mSize } returns 2048L
            every { mDateModified } returns 1234567891L
            every { mMimeType } returns "text/plain"
        }

        mockkConstructor(SearchMediaBean::class)
        mockkObject(com.filemanager.common.utils.GsonUtil)
        every { com.filemanager.common.utils.GsonUtil.toJson<List<SearchMediaBean>>(any()) } returns "[{\"id\":1,\"data\":\"/path/to/uriFile\"},{\"data\":\"/path/to/remoteFile\"}]"

        val result = searchDocLoader.createCursor(listOf(uriFile, remoteFile))

        assertNotNull(result)
        verify(exactly = 2) { anyConstructed<SearchMediaBean>() wasNot Called }
    }
}