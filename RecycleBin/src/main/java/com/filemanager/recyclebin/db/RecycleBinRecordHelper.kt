/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - RecycleBinRecordHelper.kt
 * Description:
 *     Help to handle the records for recycle bin database
 *
 * Version: 1.0
 * Date: 2024-04-08
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON>ia<PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-04-08   1.0    Create this module
 *********************************************************************************/
package com.filemanager.recyclebin.db

import android.content.ContentValues
import com.filemanager.common.RecycleStore
import com.filemanager.common.RecycleStore.ANDROID_MAX_NAME_BYTE_LENGTH
import com.filemanager.common.RecycleStore.Files.FileColumns
import com.filemanager.common.RecycleStore.RECYCLE_NAME_SEPARATION
import com.filemanager.common.RecycleStore.RECYCLE_NAME_SUFFIX
import com.filemanager.common.compat.MediaFileCompat
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.constructContentValues
import org.jetbrains.annotations.VisibleForTesting
import java.io.File
import java.util.Locale

internal object RecycleBinRecordHelper {

    private const val TAG = "RecycleBinRecordHelper"
    private const val TO_SECOND_SCALE = 1000L
    private const val STR_BUILDER_CAP = 100

    private val PATTERN_VOLUME_NAME by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
        "(?i)^/storage/([^/]+)".toPattern()
    }

    private val PATTERN_RELATIVE_PATH by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
        "(?i)^/storage/[^/]+/(?:[0-9]+/)?(Android/sandbox/([^/]+)/)?".toPattern()
    }

    @JvmStatic
    @JvmOverloads
    fun extractContentValues(
        originPath: String,
        lastModified: Long,
        existsRecycledFileNames: Collection<String>? = null
    ): ContentValues? {
        if (existsRecycledFileNames != null) {
            Log.d(TAG, "extractContentValues: is called from exterior")
        }
        val values = constructContentValues()
        // put origin path
        values.put(FileColumns.ORIGIN_PATH, originPath)

        // put volume name
        val volumeName = extractVolumeName(originPath)
        if (volumeName.isNullOrEmpty()) {
            Log.w(TAG, "extractContentValues: extract volume name failed")
            return null
        } else {
            values.put(FileColumns.VOLUME_NAME, volumeName)
        }

        // put relative path
        val relativePath = extractRelativeName(originPath)
        if (relativePath.isNullOrEmpty()) {
            Log.w(TAG, "extractContentValues: extract relative path failed")
            return null
        } else {
            values.put(FileColumns.RELATIVE_PATH, relativePath)
        }

        // put display name
        val displayName = extractDisplayName(originPath)
        if (displayName.isNullOrEmpty()) {
            Log.w(TAG, "extractContentValues: extract display name failed")
            return null
        } else {
            values.put(FileColumns.DISPLAY_NAME, displayName)
        }

        // put last modified
        values.put(FileColumns.DATE_MODIFIED, lastModified / TO_SECOND_SCALE)

        // put recycle date
        val recycleDate = currentTimeMillis()
        values.put(FileColumns.RECYCLE_DATE, recycleDate)

        // put recycle path
        val recycleName = ensureRecycleName(values, recycleDate)
        if (recycleName.isNullOrEmpty()) {
            Log.w(TAG, "extractContentValues: extract recycle name failed")
            return null
        }
        val recyclePath = ensureRecyclePath(recycleName, existsRecycledFileNames)
        if (recyclePath.isEmpty()) {
            Log.w(TAG, "extractContentValues: extract recycle path failed")
            return null
        }
        Log.d(TAG, "extractContentValues: recyclePath=$recyclePath")
        values.put(FileColumns.RECYCLE_PATH, recyclePath)
        return values
    }

    @VisibleForTesting
    @JvmStatic
    internal fun currentTimeMillis(): Long = System.currentTimeMillis()

    @JvmStatic
    private fun extractVolumeName(data: String?): String? {
        if (data == null) return null
        val matcher = PATTERN_VOLUME_NAME.matcher(data)
        if (!matcher.find()) {
            return RecycleStore.VOLUME_EXTERNAL_PRIMARY
        }
        val volumeName = matcher.group(1)
        return if (volumeName == "emulated") {
            RecycleStore.VOLUME_EXTERNAL_PRIMARY
        } else {
            //StorageVolume.normalizeUuid(volumeName)
            volumeName?.lowercase(Locale.US)
        }
    }

    @JvmStatic
    private fun extractRelativeName(data: String?): String? {
        if (data == null) {
            return null
        }
        val matcher = PATTERN_RELATIVE_PATH.matcher(data)
        if (!matcher.find()) {
            return null
        }
        val lastSlash = data.lastIndexOf('/')
        return if ((lastSlash == -1) || (lastSlash < matcher.end())) {
            /*
             * This is a file in the top-level directory, so relative path is "/"
             * which is different than null, which means unknown path
             */
            "/"
        } else {
            data.substring(matcher.end(), lastSlash + 1)
        }
    }

    @JvmStatic
    private fun extractDisplayName(data: String?): String? {
        var path = data ?: return null
        if (path.endsWith("/")) {
            path = data.substring(0, data.length - 1)
        }
        return path.substring(data.lastIndexOf('/') + 1)
    }

    @JvmStatic
    private fun ensureRecycleName(values: ContentValues?, recycleDate: Long): String? {
        values ?: return null
        val builder = StringBuilder(STR_BUILDER_CAP)
        val nameSegment = recycleDate.toString() + RECYCLE_NAME_SEPARATION +
                values.getAsString(FileColumns.RELATIVE_PATH)
        builder.append(nameSegment.hashCode())
        builder.append(RECYCLE_NAME_SEPARATION)

        val originDisplayName = values.getAsString(FileColumns.DISPLAY_NAME)
        if (originDisplayName.isNullOrEmpty()) {
            return null
        } else {
            builder.append(originDisplayName)
        }

        builder.append(RECYCLE_NAME_SUFFIX)

        var ensureName = builder.toString()
        if (ensureName.toByteArray().size >= ANDROID_MAX_NAME_BYTE_LENGTH) {
            val extPosi = originDisplayName.lastIndexOf(".")
            var ensureNameExt = RECYCLE_NAME_SUFFIX
            if (extPosi > 0) {
                ensureNameExt = originDisplayName.substring(extPosi) + RECYCLE_NAME_SUFFIX
            }
            var isCrop = false
            val maxLength = (ANDROID_MAX_NAME_BYTE_LENGTH - ensureNameExt.toByteArray().size)
            while (ensureName.toByteArray().size >= maxLength) {
                if (ensureName.isEmpty()) {
                    //avoid endless loop
                    break
                }
                ensureName = ensureName.substring(0, ensureName.length - 1)
                isCrop = true
            }
            if (isCrop) {
                ensureName += ensureNameExt
                Log.d(TAG, "ensureRecycleName fileName to long, cut out result $ensureName")
            }
        }
        return ensureName
    }

    @JvmStatic
    private fun ensureRecyclePath(
        recycleName: String,
        existsRecycledFileNames: Collection<String>?
    ): String {
        val builder = StringBuilder(STR_BUILDER_CAP)
        builder.append(RecycleStore.getRecycleDirectory(false))
        builder.append(File.separator)
        val recyclePath = builder.toString() + recycleName
        if (checkAlreadyExists(recyclePath, existsRecycledFileNames)) {
            // recycle path conflict
            builder.append(RECYCLE_NAME_SEPARATION)
            builder.append(currentTimeMillis())
            builder.append(recycleName)
        } else {
            builder.append(recycleName)
        }
        return builder.toString()
    }

    @JvmStatic
    private fun checkAlreadyExists(
        recyclePath: String,
        existsRecycledFileNames: Collection<String>?
    ): Boolean {
        val recycleFile = File(recyclePath)
        if (existsRecycledFileNames == null) {
            return runCatching { recycleFile.exists() }.getOrDefault(false)
        }
        existsRecycledFileNames.forEach {
            if (recycleFile.name == it) {
                return true
            }
        }
        return false
    }

    @JvmStatic
    fun ensureNonNulFileColumns(values: ContentValues): Boolean {
        if (!values.containsKey(FileColumns.RECYCLE_DATE)) {
            Log.e(TAG, "ensureNonNulFileColumns: recycle_date missed")
            return false
        } else if (!values.containsKey(FileColumns.RECYCLE_PATH)) {
            Log.e(TAG, "ensureNonNulFileColumns: recycle_path missed")
            return false
        } else if (!values.containsKey(FileColumns.RELATIVE_PATH)) {
            Log.e(TAG, "ensureNonNulFileColumns: relative_path missed")
            return false
        } else if (!values.containsKey(FileColumns.DISPLAY_NAME)) {
            Log.e(TAG, "ensureNonNulFileColumns: display_name missed")
            return false
        } else if (!values.containsKey(FileColumns.VOLUME_NAME)) {
            Log.e(TAG, "ensureNonNulFileColumns: volume_name missed")
            return false
        } else if (!values.containsKey(FileColumns.DATE_MODIFIED)) {
            // make sure last modify time set from origin path
            Log.e(TAG, "ensureNonNulFileColumns: date_modified missed")
            return false
        }
        return true
    }

    @JvmStatic
    fun computerMediaTypeValues(values: ContentValues?, recyclePath: String) {
        values ?: return
        if (!values.containsKey(FileColumns.MEDIA_TYPE)) {
            val isExterior =
                values.containsKey(RecycleBinExportHelper.ExportParams.IS_CALL_FROM_EXTERIOR)
            values.put(
                FileColumns.MEDIA_TYPE,
                RecycleStore.Files.extractMediaTypeByPath(recyclePath, isExterior)
            )
        }
        if (!values.containsKey(FileColumns.SIZE)) {
            val fileSize =
                RecycleStore.Files.getFileSize(values.getAsString(FileColumns.ORIGIN_PATH))
            values.put(FileColumns.SIZE, if (fileSize == -1L) null else fileSize)
        }
        if (!values.containsKey(FileColumns.IS_DRM) && RecycleStore.Files.isDrmType(recyclePath)) {
            values.put(FileColumns.IS_DRM, 1)
        }
        if (values.containsKey(FileColumns.MIME_TYPE)) {
            return
        }
        if (RecycleBinExportHelper.checkIsFileForInsert(values, recyclePath)) {
            val mimeType = MediaFileCompat.getMimeTypeForFile(recyclePath)
            if (mimeType.isNotEmpty()) {
                values.put(FileColumns.MIME_TYPE, mimeType)
            }
        }
    }

    @JvmStatic
    fun computerDataValues(
        helper: DatabaseOpenHelper,
        values: ContentValues?,
        recyclePath: String
    ) {
        if (values == null) {
            Log.e(TAG, "computerDataValues values null")
            return
        }
        // Worst case we have to assume no bucket details
        values.remove(FileColumns.RECYCLE_BUCKET_ID)
        values.remove(FileColumns.RECYCLE_PARENT)
        val fileLower = File(recyclePath.lowercase())
        // Buckets are the parent directory
        val parent = fileLower.parent
        if (parent != null) {
            values.put(FileColumns.RECYCLE_BUCKET_ID, parent.hashCode())
        }
        // parent id
        values.put(FileColumns.RECYCLE_PARENT, helper.getParent(values, recyclePath))
    }
}