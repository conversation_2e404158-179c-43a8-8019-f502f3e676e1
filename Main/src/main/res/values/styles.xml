<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="NewApi">

    <style name="RecentImageLayoutStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:background">@drawable/coui_preference_bg_selector</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:padding">@dimen/dimen_2dp</item>
        <item name="android:visibility">invisible</item>
    </style>

    <style name="RecentImageLayoutCheckBoxStyle">
        <item name="android:layout_width">@dimen/recent_icon_size</item>
        <item name="android:layout_height">@dimen/recent_icon_size</item>
        <item name="android:layout_gravity">bottom|end</item>
        <item name="android:clickable">false</item>
        <item name="android:focusable">false</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="RecentImageLayoutImgStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:scaleType">centerCrop</item>
    </style>

    <style name="RecentImageLayoutCheckBoxMaskStyle">
        <item name="android:layout_width">@dimen/grid_check_box_size</item>
        <item name="android:layout_height">@dimen/grid_check_box_size</item>
        <item name="android:layout_gravity">bottom|end</item>
        <item name="android:src">@drawable/grid_checkbox_select_none_bg</item>
        <item name="android:visibility">gone</item>
    </style>

    <style name="MainMenuTextStyle">
        <item name="android:textAppearance">?android:attr/textAppearanceLarge</item>
        <item name="android:textSize">@dimen/main_menu_text_size</item>
        <item name="android:textColor">@color/black_font</item>
        <item name="android:lineSpacingExtra">@dimen/font_line_space_2</item>
    </style>

    <style name="mainCateTypeTitleStyleWithoutMargin">
        <item name="android:gravity">center_vertical</item>
        <item name="android:minHeight">@dimen/main_category_type_title_min_height</item>
        <item name="android:textColor">@color/black_55_percent</item>
        <item name="android:textSize">@dimen/font_size_12</item>
    </style>

    <style name="mainCateTypeTitleStyle" parent="mainCateTypeTitleStyleWithoutMargin">
        <item name="android:layout_marginTop">@dimen/main_category_content_margin</item>
    </style>

    <style name="MainMenuSubTextStyle">
        <item name="android:textAppearance">?android:attr/textAppearanceLarge</item>
        <item name="android:textSize">@dimen/main_menu_sub_text_size</item>
        <item name="android:textColor">@color/black_55_percent</item>
        <item name="android:lineSpacingExtra">@dimen/font_line_space_2</item>
    </style>

    <style name="ToolbarLargeTitleStyle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/textAppearanceLargestTitle</item>
        <item name="android:textSize">@dimen/default_large_title_size</item>
    </style>

    <style name="mainCateSubDescStyle">
        <item name="android:gravity">center_vertical</item>
        <item name="android:minHeight">@dimen/main_category_content_margin</item>
        <item name="android:textColor">@color/black_55_percent</item>
        <item name="android:textSize">@dimen/font_size_12</item>
        <item name="android:layout_marginTop">@dimen/main_category_sub_desc_margin_top</item>
    </style>

    <style name="mainCategoryItemTitleStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:lineSpacingMultiplier">1.2</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="mainCategoryItemSubStyle">
        <item name="android:textSize">12sp</item>
        <item name="android:lineSpacingMultiplier">1.15</item>
        <item name="android:fontFamily">sans-serif-regular</item>
    </style>

    <style name="COUICollapsingToolbarLayoutStyle.WithSearch">
        <item name="android:layout_height">@dimen/coui_appbar_title_expanded_with_search_height</item>
        <item name="expandedTitleMarginBottom">@dimen/coui_appbar_title_expanded_with_search_margin_bottom</item>
        <item name="expandedTitleMarginTop">@dimen/coui_appbar_title_expanded_with_search_margin_top</item>
    </style>

    <style name="Expanded_13_2">
        <item name="android:textAppearance">@style/textAppearanceLargestTitle</item>
        <item name="android:textSize">@dimen/dimen_40dp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:singleLine">true</item>
    </style>

    <!--FontStyle.FONT_WEIGHT_SEMI_BOLD-->
    <style name="storageCardTextAppearance" parent="couiTextButtonS">
        <item name="android:textFontWeight">600</item>
    </style>
    <style name="storageLocationTextAppearance" parent="couiTextAppearance">
        <item name="android:textFontWeight">300</item>
    </style>
    <style name="Theme.Transparent" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

</resources>