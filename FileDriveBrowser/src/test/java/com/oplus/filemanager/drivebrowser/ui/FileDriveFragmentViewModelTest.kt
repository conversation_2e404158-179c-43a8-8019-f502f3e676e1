/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : FileDriveFragmentViewModelTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/31 11:42
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/31       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.ui

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.CustomToast
import com.oplus.filemanager.drivebrowser.data.api.KDocsFileDriveService
import com.oplus.filemanager.drivebrowser.data.api.TencentFileDriveService
import com.oplus.filemanager.drivebrowser.data.model.BaseResponse
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem
import com.oplus.filemanager.drivebrowser.data.repository.kdocs.KDocsFileDriveRepository
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.data.utils.CloudFileSortTypeUtils
import com.oplus.filemanager.drivebrowser.di.NetworkModule
import com.oplus.filemanager.drivebrowser.domain.model.AuthorizationData
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.drivebrowser.source.CloudDocsQuery
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils
import io.mockk.coEvery
import io.mockk.every
import io.mockk.just
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.TestResult
import kotlinx.coroutines.test.runTest
import org.junit.Assert
import org.junit.Rule
import org.junit.Test
import kotlin.time.Duration.Companion.seconds

class FileDriveFragmentViewModelTest {

    @Rule
    @JvmField
    val instantExecutorRule = InstantTaskExecutorRule()

    @Test
    fun testGetScanMode() {
        mockkObject(ConfigSharedPreferenceUtils)
        every { ConfigSharedPreferenceUtils.getInt(any(), 0) }.returns(1)
        val viewModel = FileDriveFragmentViewModel()
        Assert.assertEquals(1, viewModel.getScanMode())
        every { ConfigSharedPreferenceUtils.getInt(any(), 0) }.returns(0)
        Assert.assertEquals(KtConstants.SCAN_MODE_LIST, viewModel.getScanMode())
        unmockkObject(ConfigSharedPreferenceUtils)
    }

    @Test
    fun testSaveAuthorizationResult(): TestResult = runTest(timeout = testCaseTimeout) {
        mockkStatic(FileDriveStateUtils::class)
        justRun { FileDriveStateUtils.saveAuthorizationValue(any(), any(), any()) }
        val viewModel = FileDriveFragmentViewModel()
        viewModel.saveAuthorizationResult(AUTH_CODE, AUTH_STATE)
        verify { FileDriveStateUtils.saveAuthorizationValue(any(), any(), any()) }
        unmockkStatic(FileDriveStateUtils::class)
    }

    @Test
    fun testDownloadCloudFiles() {
        val activity = mockk<FileDriveActivity>()
        justRun { activity.showSelectPathFragmentDialog(any()) }
        val files = arrayListOf<CloudDocsItem>()
        files.add(CloudDocsItem())
        files.add(CloudDocsItem())
        val service = mockk<TencentFileDriveService>()
        mockkStatic(NetworkModule::class)
        mockkStatic(CustomToast::class)
        mockkStatic(CloudFileSortTypeUtils::class)
        mockkObject(FileDriveActivity)
        every { CloudFileSortTypeUtils.getDefaultSortType(any()) }.returns(Pair("", 0))
        justRun { CustomToast.showShort(any() as Int) }
        every { NetworkModule.providerTencentFileDriveService() }.returns(service)
        val viewModel = FileDriveFragmentViewModel()
        viewModel.initUiState(CategoryHelper.CATEGORY_TENCENT_DOCS, "")
        viewModel.downloadCloudFiles(activity, files)
        verify(inverse = true) { activity.showSelectPathFragmentDialog(any(), any()) }
        verify(inverse = true) { CustomToast.showShort(any() as Int) }

        val dirFiles = arrayListOf<CloudDocsItem>()
        dirFiles.add(CloudDocsItem().apply { mLocalType = MimeTypeHelper.DIRECTORY_TYPE })

        viewModel.downloadCloudFiles(activity, dirFiles)
        verify { CustomToast.showShort(any() as Int) }
        verify(inverse = true) { activity.showSelectPathFragmentDialog(any(), any()) }

        val unsupportedFormatFiles = arrayListOf<CloudDocsItem>()
        unsupportedFormatFiles.add(CloudDocsItem().apply {
            mLocalType = MimeTypeHelper.DOC_TYPE
            type = DocumentListItem.TENCENT_FILE_TYPE_DRIVE
        })
        viewModel.downloadCloudFiles(activity, unsupportedFormatFiles)
        verify { CustomToast.showShort(any() as Int) }
        verify(inverse = true) { activity.showSelectPathFragmentDialog(any(), any()) }

        val supportedFiles = arrayListOf<CloudDocsItem>()
        supportedFiles.add(CloudDocsItem().apply { type = DocumentListItem.TENCENT_FILE_TYPE_PDF })
        every { FileDriveActivity.downloadPath } returns "Download"
        every { activity.showSelectPathFragmentDialog(any(), any()) } just runs
        viewModel.downloadCloudFiles(activity, supportedFiles)
        verify { activity.showSelectPathFragmentDialog(any(), any()) }
        unmockkStatic(NetworkModule::class)
        unmockkStatic(CustomToast::class)
        unmockkStatic(CloudFileSortTypeUtils::class)
        unmockkObject(FileDriveActivity)
    }

    @Test
    fun should_return_boolean_when_isSameData() {
        val newData = mockk<BaseUiModel<CloudDocsItem>>()
        val oldData = mockk<BaseUiModel<CloudDocsItem>>()
        val viewModel = FileDriveFragmentViewModel()
        var isSame = viewModel.isSameData(newData, newData, true)
        Assert.assertFalse(isSame)

        isSame = viewModel.isSameData(newData, null, false)
        Assert.assertFalse(isSame)

        val list = listOf(CloudDocsItem(), CloudDocsItem())
        every { newData.fileList }.returns(list)
        every { oldData.fileList }.returns(listOf())
        isSame = viewModel.isSameData(newData, oldData, false)
        Assert.assertFalse(isSame)

        every { newData.fileList }.returns(list)
        every { oldData.fileList }.returns(
            listOf(
                CloudDocsItem().apply { id = "1" },
                CloudDocsItem()
            )
        )
        isSame = viewModel.isSameData(newData, oldData, false)
        Assert.assertFalse(isSame)

        every { newData.fileList }.returns(list)
        every { oldData.fileList }.returns(list)
        val selectList = arrayListOf(1, 2, 3)
        every { newData.selectedList }.returns(selectList)
        every { oldData.selectedList }.returns(arrayListOf())
        isSame = viewModel.isSameData(newData, oldData, false)
        Assert.assertFalse(isSame)

        every { newData.selectedList }.returns(selectList)
        every { oldData.selectedList }.returns(arrayListOf(4, 5, 6))
        isSame = viewModel.isSameData(newData, oldData, false)
        Assert.assertFalse(isSame)

        every { newData.selectedList }.returns(selectList)
        every { oldData.selectedList }.returns(selectList)
        isSame = viewModel.isSameData(newData, oldData, false)
        Assert.assertTrue(isSame)
    }

    @Test
    fun `should call right when call doRenameActionDone if kdoc FILE_NOT_EXISTS`() {
        //setup
        mockkStatic(CustomToast::class)
        //given
        val result = false
        val isDirectory = false
        val response = BaseResponse<String>()
        response.codeMsg(KDocsFileDriveRepository.FILE_NOT_EXISTS, "error")
        response.data = "error"
        val data = Pair(isDirectory, response)
        val viewModel = FileDriveFragmentViewModel()
        val service = mockk<KDocsFileDriveService>()
        val repository = KDocsFileDriveRepository(service)
        viewModel.repository = repository
        every { CustomToast.showShort(any<Int>()) } just runs
        //when
        viewModel.doRenameActionDone(result, data)
        //then
        verify { CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist) }

        //teardown
        unmockkStatic(CustomToast::class)
    }

    @Test
    fun `should call right when call doRenameActionDone if kdoc FILE_NAME_CONFLICT`() {
        //setup
        mockkStatic(CustomToast::class)
        //given
        val result = false
        val isDirectory = false
        val response = BaseResponse<String>()
        response.codeMsg(KDocsFileDriveRepository.FILE_NAME_CONFLICT, "error")
        response.data = "error"
        val data = Pair(isDirectory, response)
        val viewModel = FileDriveFragmentViewModel()
        val service = mockk<KDocsFileDriveService>()
        val repository = KDocsFileDriveRepository(service)
        viewModel.repository = repository
        every { CustomToast.showShort(any<Int>()) } just runs
        //when
        viewModel.doRenameActionDone(result, data)
        //then
        verify { CustomToast.showShort(com.filemanager.common.R.string.toast_file_exist) }

        //teardown
        unmockkStatic(CustomToast::class)
    }

    @Test
    fun `should call right when call doRenameActionDone if kdoc FILE_NAME_CONFLICT and isDirectory`() {
        //setup
        mockkStatic(CustomToast::class)
        //given
        val result = false
        val isDirectory = true
        val response = BaseResponse<String>()
        response.codeMsg(KDocsFileDriveRepository.FILE_NAME_CONFLICT, "error")
        response.data = "error"
        val data = Pair(isDirectory, response)
        val viewModel = FileDriveFragmentViewModel()
        val service = mockk<KDocsFileDriveService>()
        val repository = KDocsFileDriveRepository(service)
        viewModel.repository = repository
        every { CustomToast.showShort(any<Int>()) } just runs
        //when
        viewModel.doRenameActionDone(result, data)
        //then
        verify { CustomToast.showShort(com.filemanager.common.R.string.toast_folder_exist) }

        //teardown
        unmockkStatic(CustomToast::class)
    }

    @Test
    fun `should call right when call doRenameActionDone if tencent FILE_NOT_EXISTS`() {
        //setup
        mockkStatic(CustomToast::class)
        //given
        val result = false
        val isDirectory = false
        val response = BaseResponse<String>()
        response.codeMsg(TencentFileDriveRepository.FILE_NOT_EXISTS, "error")
        response.data = "error"
        val data = Pair(isDirectory, response)
        val viewModel = FileDriveFragmentViewModel()
        val service = mockk<TencentFileDriveService>()
        val repository = TencentFileDriveRepository(service)
        viewModel.repository = repository
        every { CustomToast.showShort(any<Int>()) } just runs
        //when
        viewModel.doRenameActionDone(result, data)
        //then
        verify { CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist) }

        //teardown
        unmockkStatic(CustomToast::class)
    }

    @Test
    fun `should call right when call doRenameActionDone if tencent FOLDER_NOT_EXISTS`() {
        //setup
        mockkStatic(CustomToast::class)
        //given
        val result = false
        val isDirectory = true
        val response = BaseResponse<String>()
        response.codeMsg(TencentFileDriveRepository.FOLDER_NOT_EXISTS, "error")
        response.data = "error"
        val data = Pair(isDirectory, response)
        val viewModel = FileDriveFragmentViewModel()
        val service = mockk<TencentFileDriveService>()
        val repository = TencentFileDriveRepository(service)
        viewModel.repository = repository
        every { CustomToast.showShort(any<Int>()) } just runs
        //when
        viewModel.doRenameActionDone(result, data)
        //then
        verify { CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist) }

        //teardown
        unmockkStatic(CustomToast::class)
    }

    @Test
    fun `should call right when call doRenameActionDone if tencent FOLDER_NAME_CONFLICT`() {
        //setup
        mockkStatic(CustomToast::class)
        //given
        val result = false
        val isDirectory = true
        val response = BaseResponse<String>()
        response.codeMsg(TencentFileDriveRepository.FOLDER_NAME_CONFLICT, "error")
        response.data = "error"
        val data = Pair(isDirectory, response)
        val viewModel = FileDriveFragmentViewModel()
        val service = mockk<TencentFileDriveService>()
        val repository = TencentFileDriveRepository(service)
        viewModel.repository = repository
        every { CustomToast.showShort(any<Int>()) } just runs
        //when
        viewModel.doRenameActionDone(result, data)
        //then
        verify { CustomToast.showShort(com.filemanager.common.R.string.toast_folder_exist) }

        //teardown
        unmockkStatic(CustomToast::class)
    }

    @Test
    fun `should call right when call doRenameActionDone if normal error`() {
        //setup
        mockkStatic(CustomToast::class)
        //given
        val result = false
        val isDirectory = true
        val response = BaseResponse<String>()
        response.codeMsg(100, "error")
        response.data = "error"
        val data = Pair(isDirectory, response)
        val viewModel = FileDriveFragmentViewModel()
        val service = mockk<TencentFileDriveService>()
        val repository = TencentFileDriveRepository(service)
        viewModel.repository = repository
        every { CustomToast.showShort(any<Int>()) } just runs
        //when
        viewModel.doRenameActionDone(result, data)
        //then
        verify { CustomToast.showShort(com.filemanager.common.R.string.rename_docs_failure) }

        //teardown
        unmockkStatic(CustomToast::class)
    }

    @Test
    fun `should call right when call doRenameActionDone if result is true`() {
        //setup
        mockkStatic(CustomToast::class)
        //given
        val result = true
        val isDirectory = true
        val response = BaseResponse<String>()
        response.codeMsg(100, "error")
        response.data = "error"
        val data = Pair(isDirectory, response)
        val viewModel = FileDriveFragmentViewModel()
        val service = mockk<TencentFileDriveService>()
        val repository = TencentFileDriveRepository(service)
        viewModel.repository = repository
        every { CustomToast.showShort(any<Int>()) } just runs
        //when
        viewModel.doRenameActionDone(result, data)
        //then
        verify(inverse = true) { CustomToast.showShort(any<Int>()) }

        //teardown
        unmockkStatic(CustomToast::class)
    }

    @Test
    fun `should return false when call supportDownload if select is null`() {
        //given
        val viewModel = mockk<FileDriveFragmentViewModel>()
        every { viewModel.supportDownload() } answers { callOriginal() }
        val uiState = mockk<MutableLiveData<BaseUiModel<CloudDocsItem>>>()
        every { viewModel.uiState } returns uiState
        every { uiState.value } returns null
        //when
        val result = viewModel.supportDownload()
        //then
        Assert.assertEquals(false, result)
    }

    @Test
    fun `should return false when call supportDownload if select size is empty`() {
        //given
        val viewModel = mockk<FileDriveFragmentViewModel>()
        every { viewModel.supportDownload() } answers { callOriginal() }
        val uiState = mockk<MutableLiveData<BaseUiModel<CloudDocsItem>>>()
        every { viewModel.uiState } returns uiState
        val baseUiModel = mockk<BaseUiModel<CloudDocsItem>>()
        every { uiState.value } returns baseUiModel
        val selectedList = mockk<ArrayList<Int>>()
        every { baseUiModel.selectedList } returns selectedList
        every { selectedList.size } returns 0
        //when
        val result = viewModel.supportDownload()
        //then
        Assert.assertEquals(false, result)
    }

    @Test
    fun `should return false when call supportDownload if keymap is not contain`() {
        //given
        val viewModel = mockk<FileDriveFragmentViewModel>()
        every { viewModel.supportDownload() } answers { callOriginal() }
        val uiState = mockk<MutableLiveData<BaseUiModel<CloudDocsItem>>>()
        every { viewModel.uiState } returns uiState
        val baseUiModel = mockk<BaseUiModel<CloudDocsItem>>()
        every { uiState.value } returns baseUiModel
        val selectedList = mockk<ArrayList<Int>>()
        every { baseUiModel.selectedList } returns selectedList
        every { selectedList.size } returns 1
        every { selectedList[any()] } returns 100

        val keyMap = mockk<HashMap<Int, CloudDocsItem>>()
        every { baseUiModel.keyMap } returns keyMap
        every { keyMap[any()] } returns null

        //when
        val result = viewModel.supportDownload()
        //then
        Assert.assertEquals(false, result)
    }

    @Test
    fun `should return true when call supportDownload if keymap is not contain`() {
        //given
        val viewModel = mockk<FileDriveFragmentViewModel>()
        every { viewModel.supportDownload() } answers { callOriginal() }
        val uiState = mockk<MutableLiveData<BaseUiModel<CloudDocsItem>>>()
        every { viewModel.uiState } returns uiState
        val baseUiModel = mockk<BaseUiModel<CloudDocsItem>>()
        every { uiState.value } returns baseUiModel
        val selectedList = mockk<ArrayList<Int>>()
        every { baseUiModel.selectedList } returns selectedList
        every { selectedList.size } returns 1
        every { selectedList[any()] } returns 100

        val keyMap = mockk<HashMap<Int, CloudDocsItem>>()
        every { baseUiModel.keyMap } returns keyMap
        val item = mockk<CloudDocsItem>()
        every { keyMap[any()] } returns item
        every { item.isSupportDownload(any()) } returns true

        //when
        val result = viewModel.supportDownload()
        //then
        Assert.assertEquals(true, result)
    }

    @Test
    fun `should call changeListMode when call doActionDelete if result is true`() {
        //given
        val result = true
        val data = false
        val viewModel = mockk<FileDriveFragmentViewModel>()
        every { viewModel.doActionDelete(any(), any()) } answers { callOriginal() }
        every { viewModel.changeListMode(any()) } just runs
        every { viewModel.updateAuthState(any()) } just runs
        every { viewModel.doDeleteFilesDone() } just runs
        //when
        viewModel.doActionDelete(result, data)
        //then
        verify { viewModel.doDeleteFilesDone() }
        verify(inverse = true) { viewModel.updateAuthState(any()) }
    }

    @Test
    fun `should call updateAuthState when call doActionDelete if result is false`() {
        //given
        val result = false
        val data = false
        val viewModel = mockk<FileDriveFragmentViewModel>()
        every { viewModel.doActionDelete(any(), any()) } answers { callOriginal() }
        every { viewModel.changeListMode(any()) } just runs
        every { viewModel.updateAuthState(any()) } just runs
        every { viewModel.doDeleteFilesDone() } just runs
        //when
        viewModel.doActionDelete(result, data)
        //then
        verify { viewModel.changeListMode(KtConstants.LIST_NORMAL_MODE) }
        verify(inverse = true) { viewModel.doDeleteFilesDone() }
        verify { viewModel.updateAuthState(false) }
    }

    @Test
    fun `should call updateAuthState when call doActionDelete if result is false and data is true`() {
        //given
        val result = false
        val data = true
        val viewModel = mockk<FileDriveFragmentViewModel>()
        every { viewModel.doActionDelete(any(), any()) } answers { callOriginal() }
        every { viewModel.changeListMode(any()) } just runs
        every { viewModel.updateAuthState(any()) } just runs
        every { viewModel.doDeleteFilesDone() } just runs
        //when
        viewModel.doActionDelete(result, data)
        //then
        verify(inverse = true) { viewModel.changeListMode(KtConstants.LIST_NORMAL_MODE) }
        verify(inverse = true) { viewModel.doDeleteFilesDone() }
        verify { viewModel.updateAuthState(true) }
    }

    @Test
    fun `should return true when call checkAuth if FileDriveStateUtils isAuthed`(): Unit =
        runBlocking {
            //setup
            mockkStatic(FileDriveStateUtils::class)
            //given
            every { FileDriveStateUtils.isAuthed(any()) } returns true
            val viewModel = mockk<FileDriveFragmentViewModel>()
            coEvery { viewModel.checkAuth(any()) } answers { callOriginal() }
            val category = CategoryHelper.CATEGORY_K_DOCS
            //when
            val result = viewModel.checkAuth(category)
            //then
            Assert.assertTrue(result)
            //teardown
            unmockkStatic(FileDriveStateUtils::class)
        }

    @Test
    fun `should return true when call checkAuth if network is auth`(): Unit =
        runBlocking {
            //setup
            mockkStatic(FileDriveStateUtils::class)
            //given
            every { FileDriveStateUtils.isAuthed(any()) } returns false
            val viewModel = mockk<FileDriveFragmentViewModel>()
            coEvery { viewModel.checkAuth(any()) } answers { callOriginal() }
            val category = CategoryHelper.CATEGORY_K_DOCS
            val repository = mockk<FileDriveRepository>()
            every { viewModel.repository } returns repository
            val authorizationData = mockk<AuthorizationData>()
            coEvery { repository.getWebViewAuthUrl() } returns authorizationData
            every { authorizationData.auth } returns true
            //when
            val result = viewModel.checkAuth(category)
            //then
            Assert.assertTrue(result)
            //teardown
            unmockkStatic(FileDriveStateUtils::class)
        }

    @Test
    fun `should return false when call checkAuth if network is auth`(): Unit =
        runBlocking {
            //setup
            mockkStatic(FileDriveStateUtils::class)
            //given
            every { FileDriveStateUtils.isAuthed(any()) } returns false
            val viewModel = mockk<FileDriveFragmentViewModel>()
            coEvery { viewModel.checkAuth(any()) } answers { callOriginal() }
            val category = CategoryHelper.CATEGORY_K_DOCS
            val repository = mockk<FileDriveRepository>()
            every { viewModel.repository } returns repository
            val authorizationData = mockk<AuthorizationData>()
            coEvery { repository.getWebViewAuthUrl() } returns authorizationData
            every { authorizationData.auth } returns false
            //when
            val result = viewModel.checkAuth(category)
            //then
            Assert.assertFalse(result)
            //teardown
            unmockkStatic(FileDriveStateUtils::class)
        }

    @Test
    fun `should return false when call checkAuth if tencent`(): Unit =
        runBlocking {
            //setup
            mockkStatic(FileDriveStateUtils::class)
            //given
            every { FileDriveStateUtils.isAuthed(any()) } returns false
            val viewModel = mockk<FileDriveFragmentViewModel>()
            coEvery { viewModel.checkAuth(any()) } answers { callOriginal() }
            val category = CategoryHelper.CATEGORY_TENCENT_DOCS
            val repository = mockk<FileDriveRepository>()
            every { viewModel.repository } returns repository
            val authorizationData = mockk<AuthorizationData>()
            coEvery { repository.getWebViewAuthUrl() } returns authorizationData
            every { authorizationData.auth } returns false
            //when
            val result = viewModel.checkAuth(category)
            //then
            Assert.assertFalse(result)
            //teardown
            unmockkStatic(FileDriveStateUtils::class)
        }

    @Test
    fun `should call right when call doDeleteFilesDone if input if null`() {
        //given
        val result = mutableListOf<String>()
        result.add("test")
        val forceReload = mockk<MutableLiveData<Boolean>>()
        val viewModel = mockk<FileDriveFragmentViewModel>()
        val queryLiveData = mockk<MutableLiveData<CloudDocsQuery>>()
        every { viewModel.doDeleteFilesDone() } answers { callOriginal() }
        every { viewModel.changeListMode(any()) } just runs
        every { viewModel.forceReload } returns forceReload
        every { viewModel.queryLiveData } returns queryLiveData
        every { forceReload.setValue(any()) } just runs
        every { viewModel.queryLiveData.setValue(any()) } just runs
        val value = mockk<CloudDocsQuery>()
        every { value.userRemote = false } just runs
        every { viewModel.queryLiveData.getValue() } returns value
        //when
        viewModel.doDeleteFilesDone()
        //then
        verify { viewModel.changeListMode(KtConstants.LIST_NORMAL_MODE) }
        verify { forceReload.setValue(any()) }
    }

    companion object {
        private const val AUTH_CODE = "04MRUFDSOAKRSPHJ4J-QLW"
        private const val AUTH_STATE = "ggxUDizrENKNC6HAoXm0rE0EPyROkcdm"

        private val testCaseTimeout = 30.seconds
    }
}