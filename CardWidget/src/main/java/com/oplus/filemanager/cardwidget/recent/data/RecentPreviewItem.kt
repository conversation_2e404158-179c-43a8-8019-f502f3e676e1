/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentPreviewItem
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/15 10:15
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/15       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.data

import androidx.annotation.Keep
import com.filemanager.common.helper.CategoryHelper

const val SUPER_APP_QQ_NAME_LOWERCASE = "qq"

@Keep
data class RecentPreviewItem(
    var type: Int,
    var name: String
)

fun RecentPreviewItem.isSamePreviewItem(item: RecentPreviewItem): Boolean {
    return if (this.type == CategoryHelper.CATEGORY_QQ && item.type == CategoryHelper.CATEGORY_QQ) {
        (this.name.equals(SUPER_APP_QQ_NAME_LOWERCASE, true)
                xor item.name.equals(SUPER_APP_QQ_NAME_LOWERCASE, true)).not()
    } else {
        this.type == item.type
    }
}