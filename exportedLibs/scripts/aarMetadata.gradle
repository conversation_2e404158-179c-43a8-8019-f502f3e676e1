@SuppressWarnings("unused")
final class MetadataLog {
    private static final BASE_TAG = "AarMetadataPlugin"

    private static String printMsg(Project target, String subTag, String msg) {
        return "$BASE_TAG: ${target.path}: $subTag: $msg"
    }

    private static String printMsg(Project target, String msg) {
        return "$BASE_TAG: ${target.path}: $msg"
    }

    private static String printMsg(String subTag, String msg) {
        return "$BASE_TAG: $subTag: $msg"
    }

    private static String printMsg(String msg) {
        return "$BASE_TAG: $msg"
    }

    static void info(Project target, String subTag, String msg) {
        println(printMsg(target, subTag, msg))
    }

    static void info(Project target, String msg) {
        println(printMsg(target, msg))
    }

    static void info(String subTag, String msg) {
        println(printMsg(subTag, msg))
    }

    static void info(String msg) {
        println(printMsg(msg))
    }

    static void error(Project target, String subTag, String msg) {
        System.err.println(printMsg(target, subTag, "ERROR: $msg"))
    }

    static void error(Project target, String msg) {
        System.err.println(printMsg(target, "ERROR: $msg"))
    }

    static void error(String subTag, String msg) {
        System.err.println(printMsg(subTag, "ERROR: $msg"))
    }

    static void error(String msg) {
        System.err.println(printMsg("ERROR: $msg"))
    }
}

/**
 * 暂时仅支持以键值对的形式向自定义metadata文件中写入内容。
 * 后续有需求需要添加原始文件内容到自定义metadata中时再考虑进行扩展
 */
@SuppressWarnings("unused")
class AarMetadataDomain {
    final String name

    final Map<String, Map<String, String>> metadataKVFiles = new LinkedHashMap<>()
    private final AarMetadataExtensions metadataExt

    AarMetadataDomain(AarMetadataExtensions metadataExt, String variantType) {
        this.name = variantType
        this.metadataExt = metadataExt
    }

    void value(String metadataFileName, String key, String value) {
        metadataExt.ensureTransitive("putMetadataValue")
        pickKVFile(metadataFileName).put(key, value)
    }

    void configure(String metadataFileName, Closure configureAction) {
        metadataExt.ensureTransitive("configureMetadata")
        configureAction.delegate = pickKVFile(metadataFileName)
        configureAction.call()
    }

    private Map<String, String> pickKVFile(String metadataFileName) {
        if (metadataFileName == null || metadataFileName.isEmpty()) {
            throw new IllegalArgumentException("ERROR: metadata file name must not null or empty!")
        }
        // 兼容不同系统下的路径写法
        final useFileName = metadataFileName.replace("/", File.separator).replace("\\", File.separator)
        def kVFile = metadataKVFiles.get(useFileName)
        if (kVFile == null) {
            kVFile = new LinkedHashMap<String, String>()
            metadataKVFiles.put(useFileName, kVFile)
        }
        return kVFile
    }
}

/**
 * 每个buildType和全局类型"main"均单独对应一个domain。
 * 暂时不支持按productFlavor配置，只能针对全局类型"main"或单个buildType进行配置。
 * 后续若有需求需要按productFlavor时再进行适配扩展。
 */
@SuppressWarnings("unused")
class AarMetadataExtensions {
    final NamedDomainObjectContainer<AarMetadataDomain> metadataDomains

    private Project targetProject = null
    private boolean isTransitive = true

    AarMetadataExtensions(Project target) {
        targetProject = target
        metadataDomains =
                target.container(AarMetadataDomain, name -> new AarMetadataDomain(this, name))
        isTransitive = true
    }

    void ensureTransitive(String method) {
        if (isTransitive) {
            return
        }
        final errMsg = "can not configure aarMetadata after call deployMetadataConfigs"
        MetadataLog.error(targetProject, method, errMsg)
        throw new GradleException(errMsg)
    }

    void metadata(Action<NamedDomainObjectContainer<AarMetadataDomain>> configureAction) {
        configureAction.execute(metadataDomains)
    }

    void deployMetadataConfigs() {
        isTransitive = false
        final target = targetProject
        metadataDomains.each {
            MetadataWriteTaskHelper.applyMetadataDir(target, it.name)
        }
    }

    void ensureDeployed() {
        if (!isTransitive) {
            return
        }
        final errMsg = "missing call deployMetadataConfigs! " +
                "must call deployMetadataConfigs after configure aarMetadata."
        MetadataLog.error(target, errMsg)
        throw new GradleException(errMsg)
    }
}

/**
 * 生成自定义metadata文件至build/aar_metadata_plugin/<buildType>/META-INF下。
 * 再将build/aar_metadata_plugin配置到sourceSets的resources路径中(java资源路径，不是Android的res)。
 * 编译后，自定义metadata文件将被打包至aar/classes.jar/META-INF中。
 */
class MetadataWriteTaskHelper {
    private static final RESET_TEMP_TASK = "resetAllExtrasMetadataTemp"
    private static final WRITE_TASK_NAME_PREFIX = "generate"
    private static final WRITE_TASK_NAME_SUFFIX = "ExtrasMetadata"
    private static final BUILD_METADATA_ROOT = "aar_metadata_plugin"
    private static final BUILD_METADATA_DIR = "META-INF"
    private static final MAIN_TYPE = "main"

    private final Project target
    private final AarMetadataExtensions metadataExt
    private final mainTypeFiles = new HashSet<String>()

    MetadataWriteTaskHelper(Project target, AarMetadataExtensions metadataExt) {
        this.target = target
        this.metadataExt = metadataExt
    }

    static void applyMetadataDir(Project target, String buildType) {
        final extraMetadataDir = new File(target.buildDir, getMetadataDirPath(buildType))
        final extraResourcesDir = extraMetadataDir.parentFile.absolutePath
        MetadataLog.info(target, "applyMetadataDir", "add $buildType resources path: $extraResourcesDir")
        // 必须在Evaluate阶段中更新sourceSets设置，否则可能不生效
        target.android.sourceSets."$buildType".resources.srcDirs += [extraResourcesDir]
    }

    private static String getMetadataDirPath(String buildType) {
        return "$BUILD_METADATA_ROOT${File.separator}$buildType${File.separator}$BUILD_METADATA_DIR"
    }

    void prepareWriteTask() {
        final extraMetadataRoot = new File(target.buildDir, BUILD_METADATA_ROOT)
        MetadataLog.info(target, "prepareWriteTask", "create $RESET_TEMP_TASK")
        target.tasks.register(RESET_TEMP_TASK) {
            it.doFirst {
                MetadataLog.info(target, RESET_TEMP_TASK, "clear $extraMetadataRoot")
                target.delete extraMetadataRoot
            }
            it.doLast {
                if (!extraMetadataRoot.exists()) {
                    extraMetadataRoot.mkdirs()
                }
            }
        }
        metadataExt.metadataDomains.each {
            if (it.name == MAIN_TYPE) {
                collectMainTypeFiles(it.metadataKVFiles)
            }
            prepareWriteTaskForType(it.name, it.metadataKVFiles)
        }
    }

    private void collectMainTypeFiles(Map<String, Map<String, String>> kVFiles) {
        kVFiles.forEach { key, value ->
            mainTypeFiles.add(key)
        }
    }

    private void prepareWriteTaskForType(String buildType, Map<String, Map<String, String>> kVFiles) {
        final isMainType = (buildType == null || buildType == MAIN_TYPE)
        def writeTaskName
        if (isMainType) {
            writeTaskName = "$WRITE_TASK_NAME_PREFIX$WRITE_TASK_NAME_SUFFIX"
        } else {
            writeTaskName = "$WRITE_TASK_NAME_PREFIX${buildType.capitalize()}$WRITE_TASK_NAME_SUFFIX"
        }
        MetadataLog.info(target, "prepareWriteTask", "create $writeTaskName")
        target.tasks.register(writeTaskName) {
            it.dependsOn(RESET_TEMP_TASK)
            it.doLast {
                writeAllMetadataFiles(buildType, kVFiles)
            }
        }
        target.tasks.configureEach {
            if (isMainType && it.name == "preBuild") {
                it.dependsOn(writeTaskName)
            } else if (it.name.startsWith("pre") && it.name.endsWith("${buildType.capitalize()}Build")) {
                it.dependsOn(writeTaskName)
            }
        }
    }

    /**
     * 如果针对buildType设置的metadata文件名同时也被设置在mainType中，
     * 则直接追加写入mainType路径下的metadata文件。
     *
     * @param buildType 配置的buildType
     * @param fileName 待写入的metadata文件名
     * @return 待写入的File
     */
    private File getMetadataFile(String buildType, String fileName) {
        def useBuildType = buildType
        if (buildType == MAIN_TYPE || mainTypeFiles.contains(fileName)) {
            useBuildType = MAIN_TYPE
        }
        final extraMetadataDir = new File(target.buildDir, getMetadataDirPath(useBuildType))
        return new File(extraMetadataDir, fileName)
    }

    private void writeAllMetadataFiles(String buildType, Map<String, Map<String, String>> kVFiles) {
        kVFiles.forEach { fileName, metadata ->
            final metadataFile = getMetadataFile(buildType, fileName)
            MetadataLog.info(target, "writeAllMetadataFiles", "buildType=$buildType, metadataFile=$metadataFile")
            final metadataParent = metadataFile.parentFile
            if (!metadataParent.exists()) {
                metadataParent.mkdirs()
            }
            PrintWriter writer = null
            try {
                writer = new PrintWriter(new FileWriter(metadataFile, true))
                metadata.each { key, value ->
                    writer.println("$key=$value")
                }
                writer.flush()
            } catch (IOException err) {
                throw new GradleException("ERROR when wirte metadata to $metadataFile", err)
            } finally {
                if (writer != null) {
                    try {
                        writer.close()
                    } catch (IOException ignored) {
                    }
                }
            }
        }
    }
}

class AarMetadataPlugin implements Plugin<Project> {
    private static final METADATA_EXTENSIONS_NAME = "aarMetadata"

    @Override
    void apply(Project target) {
        ensureIsAndroidLib(target)
        MetadataLog.info(target, "apply", "create extension $METADATA_EXTENSIONS_NAME")
        final metadataExt = target.extensions.create(METADATA_EXTENSIONS_NAME, AarMetadataExtensions, target)
        target.afterEvaluate {
            metadataExt.ensureDeployed()
            new MetadataWriteTaskHelper(target, metadataExt).prepareWriteTask()
        }
    }

    private static void ensureIsAndroidLib(Project target) {
        final isLibModule = target.plugins.hasPlugin("com.android.library")
        if (!isLibModule) {
            final errMsg = "must be applied to an Android library module. " +
                    "Please apply com.android.library first."
            MetadataLog.error(target, "apply", errMsg)
            throw new GradleException(errMsg)
        }
    }
}

project.plugins.apply(AarMetadataPlugin)