/*********************************************************************
 * * Copyright (C), 2010-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : COUICardSingleInputSaveView
 * * Version     : 1.0
 * * Date        : 2023/10/10
 * * Author      : wanghonglei
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir.view

import android.content.Context
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.AttributeSet
import android.widget.ImageButton
import com.coui.appcompat.edittext.COUICardSingleInputView
import com.oplus.selectdir.R

class COUICardSingleInputSaveView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : COUICardSingleInputView(context, attrs) {
    private var btDelete: ImageButton? = null
    private var hashVerticalPadding = true
    override fun nowInit(context: Context?, attrs: AttributeSet?) {
        super.nowInit(context, attrs)
        btDelete = findViewById(R.id.input_delete)
        btDelete?.let {
            it.setOnClickListener {
                editText?.setText("")
            }
        }
        editText?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                hideDelete()
                editText?.setHint(com.filemanager.common.R.string.enter_file_name)
            }
        })
    }

    /**
     * 无内容时隐藏删除按钮，有内容时展示删除按钮
     */
    private fun hideDelete() {
        editText?.let {
            btDelete?.visibility = if (TextUtils.isEmpty(it.text)) {
                GONE
            } else {
                VISIBLE
            }
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.coui_single_input_card_view_save
    }

    override fun getEdittextPaddingBottom(): Int {
        return if (hashVerticalPadding) {
            super.getEdittextPaddingBottom()
        } else {
            0
        }
    }

    override fun getEdittextPaddingTop(): Int {
        return if (hashVerticalPadding) {
            super.getEdittextPaddingTop()
        } else {
            0
        }
    }

    fun setEditVerticalPadding(hasPadding: Boolean) {
        this.hashVerticalPadding = hasPadding
        editText?.apply {
            setPadding(paddingLeft, edittextPaddingTop, paddingRight, edittextPaddingBottom)
        }
    }
}