/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AlbumSetScanner
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/25 17:47
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/25       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.scanner

import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.utils.Log
import com.oplus.fileservice.R
import com.oplus.fileservice.bean.WebAlbumSetBean
import com.oplus.fileservice.bean.WebScannerResult
import com.oplus.fileservice.fileServiceScope
import com.oplus.fileservice.filelist.FileSelectPathService
import com.oplus.fileservice.utils.WebFileConstant.DEFAULT_PAGE_SIZE
import com.oplus.fileservice.utils.WebThumbCacheManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlin.coroutines.CoroutineContext

class AlbumSetScanner(pageNo: Int, pageSize: Int, private var isNeedThumbnail: Boolean = true) : BaseUriScanner() {

    @VisibleForTesting
    var mPageNo = pageNo

    @VisibleForTesting
    var mPageSize = pageSize
    private var mTotal = 0

    override fun scannerFiles(): WebScannerResult? {
        val albumSet = MediaStoreCompat.getAlbumSet()
        val pageResult = pageData(albumSet)
        val resultWebFileBean = ArrayList<WebAlbumSetBean>()
        if (pageResult != null) {
            for (albumItem in pageResult) {
                val webAlbumSetBean = convertAlbumItemToWebAlbumSetBean(albumItem)
                Log.d(TAG, "pageData -> webAlbumSetBean = $webAlbumSetBean")
                resultWebFileBean.add(webAlbumSetBean)
            }
        }
        fileServiceScope.launch(Dispatchers.IO) {
            val startTime = System.currentTimeMillis()
            decorateResult(resultWebFileBean)
            Log.d(TAG, "decorateResult const time = ${System.currentTimeMillis() - startTime}")
            mScannerCallback?.scannerSuc(WebScannerResult(mPageNo, mTotal, resultWebFileBean, isAlbumSet = true))
        }
        return null
    }

    private suspend fun decorateResult(
        resultWebFileBeans: ArrayList<WebAlbumSetBean>,
        context: CoroutineContext = Dispatchers.IO
    ) = runBlocking {
        resultWebFileBeans.map {
            async(context) {
                it.count = MediaStoreCompat.getAlbumItemCount(it.key).toLong()
                Log.d(TAG, "pageData -> ${it.count} ; ${it.mThumbnailData == null}")
            }
        }.plus(
            async(context) {
                if (isNeedThumbnail) {
                    WebThumbCacheManager.setAlbumSetThumbnailData(resultWebFileBeans)
                }
            }
        ).awaitAll()
    }

    @VisibleForTesting
    fun pageData(result: List<AlbumItem>?): List<AlbumItem>? {
        mTotal = result?.size ?: 0
        if (mPageSize < 1) {
            mPageSize = DEFAULT_PAGE_SIZE
            Log.d(TAG, "Service modify mPageSize $mPageSize")
        }
        var pageTotal = mTotal / mPageSize
        if (mTotal % mPageSize > 0) {
            pageTotal++
        }

        if (mPageNo > pageTotal) {
            Log.d(FileSelectPathService.TAG, "mPageNo > pageTotal is true, mPageNo = $mPageNo")
            mPageNo = pageTotal
        }
        if (mPageNo < 1) {
            mPageNo = 1
        }
        val start = mPageSize * (mPageNo - 1)
        val end = if (mPageSize * mPageNo > mTotal) {
            mTotal
        } else {
            mPageSize * mPageNo
        }
        return result?.subList(start, end)
    }

    @VisibleForTesting
    fun convertAlbumItemToWebAlbumSetBean(albumItem: AlbumItem): WebAlbumSetBean {
        val webAlbumSetBean = WebAlbumSetBean()
        albumItem.apply {
            webAlbumSetBean.key = key
            if (key == MyApplication.sAppContext.getString(R.string.album_set_card_case_path)) {
                webAlbumSetBean.bucketName = MyApplication.sAppContext.resources.getString(com.filemanager.common.R.string.card_case)
                webAlbumSetBean.type = MimeTypeHelper.ALBUM_SET_TYPE_CARD_CASE
            } else {
                webAlbumSetBean.bucketName = name
                webAlbumSetBean.type = type
            }
            webAlbumSetBean.coverPath = getCoverPath()
            webAlbumSetBean.bucketId = bucketId
            webAlbumSetBean.dataModified = dateModified
        }
        return webAlbumSetBean
    }

    companion object {
        private const val TAG = "AlbumSetScanner"
    }
}