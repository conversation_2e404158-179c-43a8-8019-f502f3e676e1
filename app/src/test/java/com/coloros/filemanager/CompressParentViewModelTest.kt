/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.filemanager.categorycompress.ui.compress
 * * Version     : 1.0
 * * Date        : 2020/9/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager

import com.filemanager.categorycompress.ui.CompressParentViewModel
import com.filemanager.common.constants.KtConstants
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class CompressParentViewModelTest : BaseTest() {
    private val mCompressActivityViewModel = CompressParentViewModel()

    @Test
    @Throws(Exception::class)
    fun should_change_scan_view_model_when_click_scanModeItem() {
        var initScanMode = mCompressActivityViewModel.mBrowseModeState.value
        Assert.assertEquals(KtConstants.SCAN_MODE_LIST, initScanMode)
        mCompressActivityViewModel.clickScanModeItem()
        initScanMode = mCompressActivityViewModel.mBrowseModeState.value
        Assert.assertEquals(KtConstants.SCAN_MODE_GRID, initScanMode)
        mCompressActivityViewModel.clickScanModeItem()
        initScanMode = mCompressActivityViewModel.mBrowseModeState.value
        Assert.assertEquals(KtConstants.SCAN_MODE_LIST, initScanMode)
    }
}