/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.category.globalsearch.GlobalSearchApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/5/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.album

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.fragment.app.Fragment
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewListFragmentCreator
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.category.album.ui.AlbumActivity
import com.oplus.filemanager.category.album.ui.AlbumFragment
import com.oplus.filemanager.category.album.ui.PickerAlbumFragment
import com.oplus.filemanager.interfaze.categoryalbum.ICategoryAlbumApi

object CategoryAlbumApi : ICategoryAlbumApi {
    private const val TAG = "CategoryAlbumApi"

    override fun startAlbumActivity(activity: Activity, uri: Uri?, bucketDate: String?, albumSetName: String?, coverPath: String?) {
        Log.d(TAG, "startAlbumActivity")
        val intent = Intent()
        intent.setClass(activity.baseContext, AlbumActivity::class.java)
        intent.data = uri
        intent.putExtra(KtConstants.KEY_IMAGE_RELATIVE_PATH, bucketDate)
        intent.putExtra(KtConstants.KEY_IMAGE_COVER_PATH, coverPath)
        intent.putExtra(Constants.TITLE, albumSetName)
        activity.startActivity(intent)
    }

    override fun getFragment(activity: Activity): Fragment {
        Log.d(TAG, "getFragment")
        val fragment = PreviewCombineFragment()
        fragment.setPreviewListFragmentCreator(object : IPreviewListFragmentCreator {
            override fun create(): IPreviewListFragment {
                return AlbumFragment()
            }
        })
        return fragment
    }
    override fun getPickerFragment(activity: Activity?): Fragment {
        Log.d(TAG, "getPickerFragment")
        return PickerAlbumFragment()
    }
    override fun onResumeLoadData(fragment: Fragment) {
        Log.d(TAG, "onResumeLoadData")
        if (fragment is PreviewCombineFragment) {
            fragment.onResumeLoadData()
        }
    }

    override fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater) {
        Log.d(TAG, "onCreateOptionsMenu")
        if (fragment is PreviewCombineFragment) {
            fragment.onCreateOptionsMenu(menu, inflater)
        }
    }

    override fun onMenuItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onMenuItemSelected")
        if (fragment is PreviewCombineFragment) {
            return fragment.onMenuItemSelected(item)
        }
        return false
    }

    override fun pressBack(fragment: Fragment): Boolean {
        Log.d(TAG, "pressBack")
        if (fragment is PreviewCombineFragment) {
            return fragment.pressBack()
        }
        return false
    }

    override fun onNavigationItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onNavigationItemSelected")
        if (fragment is PreviewCombineFragment) {
            return fragment.onNavigationItemSelected(item)
        }
        return false
    }

    override fun setToolbarAndTabListener(
        fragment: Fragment,
        toolbar: COUIToolbar?,
        title: String,
        tabListener: TabActivityListener<MediaFileWrapper>
    ) {
        Log.d(TAG, "setToolbarAndTabListener")
    }

    override fun fromSelectPathResult(fragment: Fragment, requestCode: Int, paths: List<String>?) {
        Log.d(TAG, "fromSelectPathResult")
        if (fragment is PreviewCombineFragment) {
            fragment.fromSelectPathResult(requestCode, paths)
        }
    }

    override fun updateLabels(fragment: Fragment) {
        Log.d(TAG, "updateLabels")
    }

    override fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        if (fragment is PreviewCombineFragment) {
            fragment.setIsHalfScreen(isHalfScreen)
        }
    }

    override fun permissionSuccess(fragment: Fragment) {
        Log.d(TAG, "permissionSuccess")
    }

    override fun setCurrentFilePath(fragment: Fragment, path: String?) {
        Log.d(TAG, "setCurrentFilePath")
    }

    override fun getCurrentPath(fragment: Fragment): String {
        Log.d(TAG, "getCurrentPath")
        return ""
    }

    override fun exitSelectionMode(fragment: Fragment) {
        if (fragment is PreviewCombineFragment) {
            fragment.exitSelectionMode()
        }
    }

    override fun onSideNavigationClicked(fragment: Fragment, isOpen: Boolean): Boolean {
        if (fragment is PreviewCombineFragment) {
            return fragment.onSideNavigationClicked(isOpen)
        }
        return false
    }

    override fun backToTop(fragment: Fragment) {
        Log.d(TAG, "backToTop")
        if (fragment is PreviewCombineFragment) {
            fragment.backToTop()
        }
    }
}