/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.audio
 ** Version: 1.0
 ** Date: 2021/5/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.audio

import androidx.annotation.Keep
import com.bumptech.glide.load.Options
import com.bumptech.glide.load.model.ModelLoader
import com.bumptech.glide.load.model.ModelLoaderFactory
import com.bumptech.glide.load.model.MultiModelLoaderFactory
import com.bumptech.glide.signature.ObjectKey

internal class AudioThumbnailModelLoader :
    ModelLoader<AudioThumbnailNew, AudioThumbnailResultByteArray> {

    override fun buildLoadData(
        model: AudioThumbnailNew,
        width: Int,
        height: Int,
        options: Options
    ): ModelLoader.LoadData<AudioThumbnailResultByteArray>? {
        return ModelLoader.LoadData(ObjectKey(model), AudioThumbnailFetcher(model, width, height))
    }

    override fun handles(model: AudioThumbnailNew): Boolean {
        return true
    }

}

@Keep
class AudioThumbnailLoaderFactory :
    ModelLoaderFactory<AudioThumbnailNew, AudioThumbnailResultByteArray> {

    override fun build(
        multiFactory: MultiModelLoaderFactory
    ): ModelLoader<AudioThumbnailNew, AudioThumbnailResultByteArray> {
        return AudioThumbnailModelLoader()
    }

    override fun teardown() {
        //do nothing
    }
}