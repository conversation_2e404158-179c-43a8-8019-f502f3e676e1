/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved.
 * * COLOROS_EDIT
 * * File: AKeyToMoveActivity
 * * Description: the activity for a key to move
 * * Version: 1.0
 * * Date : 2020/7/14
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/7/14       1.0         the activity for a key to move
 ****************************************************************/
package com.oplus.filemanager.keymove.ui

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.fragment.app.FragmentTransaction
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.utils.Log
import com.oplus.filemanager.keymove.R
import com.oplus.filemanager.keymove.ui.keymovechoice.KeyMoveChoiceFragment
import com.oplus.filemanager.keymove.ui.keymovegallery.KeyMoveGalleryFragment
import com.oplus.filemanager.keymove.ui.keymoveguide.KeyMoveGuideFragment
import com.oplus.filemanager.keymove.ui.model.ListSecond

class AKeyToMoveActivity : BaseVMActivity(), BaseVMActivity.PermissonCallBack {
    companion object {
        private const val TAG = "AKeyToMoveActivity"
        private const val TAG_GUIDE = "KeyMoveGuide"
        private const val TAG_CHOICE = "KeyMoveChoice"
        private const val TAG_GALLERY = "KeyMoveGallery"
        const val EXIST_FRAGMENT_COUNT = "existFragmentCount"
        const val FROM_SYSTEM_CONFIG_MODE_CHANGE = "fromSystemConfigModeChange"
        const val ExistFragmentCountOne = 1
        const val ExistFragmentCountTwo = 2
        const val ExistFragmentCountThree = 3
    }

    private var mCurrentFragment: BaseVMFragment<*>? = null
    private var mChoiceFragment: KeyMoveChoiceFragment? = null
    private var mGalleryFragment: KeyMoveGalleryFragment? = null
    private var mGuideFragment: KeyMoveGuideFragment? = null
    var mExistFragmentCount: Int = ExistFragmentCountOne
    var mFromSystemConfigModeChange = false

    override fun getLayoutResId(): Int {
        return R.layout.key_move_activity
    }

    override fun initView() {
        registerVmChangedReceiver(null)
        setFragment()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        mExistFragmentCount = 1
        mFromSystemConfigModeChange = false
        if ((savedInstanceState != null) && savedInstanceState.containsKey(FROM_SYSTEM_CONFIG_MODE_CHANGE)) {
            mFromSystemConfigModeChange = savedInstanceState.get(FROM_SYSTEM_CONFIG_MODE_CHANGE) as Boolean
            if (mFromSystemConfigModeChange && savedInstanceState.containsKey(EXIST_FRAGMENT_COUNT)) {
                mExistFragmentCount = savedInstanceState.get(EXIST_FRAGMENT_COUNT) as Int
                if (mExistFragmentCount < ExistFragmentCountTwo) {
                    mFromSystemConfigModeChange = false
                }
            }
        }
        super.onCreate(savedInstanceState)
    }

    private fun setFragment() {
        var fragment = supportFragmentManager.findFragmentByTag(TAG_GUIDE)
        if ((fragment == null) || (fragment !is KeyMoveGuideFragment)) {
            fragment = KeyMoveGuideFragment()
        }

        val ft = supportFragmentManager.beginTransaction()
        ft.replace(R.id.content, fragment, TAG_GUIDE)
        ft.show(fragment)
        ft.commitAllowingStateLoss()
        mGuideFragment = fragment
        mCurrentFragment = mGuideFragment
    }

    override fun startObserve() {}

    override fun initData() {
    }

    override fun onBackPressed() {
        if ((mCurrentFragment as? OnBackPressed)?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
        }
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        mCurrentFragment?.onResumeLoadData()
    }

    override fun onDestroy() {
        unregisterVmChangedReceiver()
        super.onDestroy()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        if (mCurrentFragment === mGalleryFragment) {
            mGalleryFragment?.onCreateOptionsMenu(menu, menuInflater)
        }
        return true
    }

    fun switchToGalleryFragment(files: ListSecond) {
        val ft = supportFragmentManager.beginTransaction()
        var fragment = supportFragmentManager.findFragmentByTag(TAG_GALLERY)
        if ((fragment == null) || (fragment !is KeyMoveGalleryFragment)) {
            fragment = KeyMoveGalleryFragment()
            ft.add(R.id.content, fragment, TAG_GALLERY)
        }
        mGalleryFragment = fragment
        mGalleryFragment?.let {
            mCurrentFragment?.let { fragment ->
                ft.hide(fragment)
            }
            mCurrentFragment = it
            (mCurrentFragment as KeyMoveGalleryFragment).setSources(files)
            mCurrentFragment?.onResumeLoadData()
            ft.show(it)
            ft.commitAllowingStateLoss()
        }
        if (mExistFragmentCount == ExistFragmentCountTwo) {
            mExistFragmentCount = ExistFragmentCountThree
        }
    }

    fun switchToChoiceFragment(needResumeLoad: Boolean = false, listSecond: ListSecond? = null) {
        val ft = supportFragmentManager.beginTransaction()
        var fragment = supportFragmentManager.findFragmentByTag(TAG_CHOICE)
        if ((fragment == null) || (fragment !is KeyMoveChoiceFragment)) {
            fragment = KeyMoveChoiceFragment()
            ft.add(R.id.content, fragment, TAG_CHOICE)
        }
        mChoiceFragment = fragment
        mChoiceFragment?.let {
            mCurrentFragment?.let { fragment ->
                ft.hide(fragment)
            }
            showCurrentFragment(ft, it)
            if (needResumeLoad) {
                mChoiceFragment!!.setIsNeedOnResumeLoadData()
                mCurrentFragment?.onResumeLoadData()
            } else {
                listSecond?.let {
                    mChoiceFragment!!.setListSecond(listSecond)
                }
            }
        }
        if (mExistFragmentCount == ExistFragmentCountOne) {
            mExistFragmentCount = ExistFragmentCountTwo
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return if (mCurrentFragment == null) {
            super.onOptionsItemSelected(item)
        } else if (mCurrentFragment == mGalleryFragment) {
            if (mGalleryFragment != null) {
                mGalleryFragment!!.onMenuItemSelected(item)
            } else {
                super.onOptionsItemSelected(item)
            }
        } else {
            if (item.itemId == android.R.id.home) {
                when (mCurrentFragment) {
                    mChoiceFragment -> {
                        mChoiceFragment?.pressBack() ?: super.onOptionsItemSelected(item)
                    }
                    mGuideFragment -> {
                        finish()
                        true
                    }
                    else -> {
                        super.onOptionsItemSelected(item)
                    }
                }
            } else {
                super.onOptionsItemSelected(item)
            }
        }
    }

    fun switchToGuideFragment() {
        val ft = supportFragmentManager.beginTransaction()
        if (mGuideFragment == null) {
            mGuideFragment = KeyMoveGuideFragment()
            ft.replace(R.id.content, mGuideFragment!!, TAG_CHOICE)
        }
        mGuideFragment?.let {
            mCurrentFragment?.let { fragment ->
                ft.hide(fragment)
            }
            showCurrentFragment(ft, it)
            mCurrentFragment?.onResumeLoadData()
        }
        supportFragmentManager.findFragmentByTag(TAG_CHOICE)?.let {
            ft.remove(it)
        }
    }

    private fun showCurrentFragment(ft: FragmentTransaction, fragment: BaseVMFragment<*>) {
        ft.show(fragment)
        ft.commitAllowingStateLoss()
        mCurrentFragment = fragment
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mGalleryFragment?.onUIConfigChanged(configList)
        mGuideFragment?.onUIConfigChanged(configList)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean(FROM_SYSTEM_CONFIG_MODE_CHANGE, true)
        outState.putInt(EXIST_FRAGMENT_COUNT, mExistFragmentCount)
    }

    override fun isAdaptNavigationBar() = false
    override fun handleNoStoragePermission() {
        Log.d(TAG, "handleNoStoragePermission")
        showSettingGuildDialog()
    }

    override fun onPermissionReject(alwaysReject: Boolean) {
        finish()
    }
}