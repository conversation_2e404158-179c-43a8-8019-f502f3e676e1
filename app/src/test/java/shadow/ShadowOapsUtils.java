package shadow;

import com.oplus.filemanager.oaps.OapsUtils;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(OapsUtils.class)
public class ShadowOapsUtils {
    private static boolean mIsShowHomeEntrance;
    @Implementation
    public static boolean isShowHomeEntrance() {
        return mIsShowHomeEntrance;
    }

    public static void setmIsShowHomeEntrance(boolean isShowHomeEntrance) {
        mIsShowHomeEntrance = isShowHomeEntrance;
    }
}
