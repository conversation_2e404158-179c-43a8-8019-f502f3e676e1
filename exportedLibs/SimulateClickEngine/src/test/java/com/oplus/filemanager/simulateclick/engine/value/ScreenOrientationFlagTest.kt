/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScreenOrientationFlagTest.kt
 * Description:
 * The test cases for ScreenOrientationFlag
 *
 * Version: 1.0
 * Date: 2024-06-06
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><EMAIL>    2024-06-06   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.value

import org.junit.Assert
import org.junit.Test

class ScreenOrientationFlagTest : Assert() {

    @Test
    fun `should to target flag when fromAlias with single alias`() {
        assertEquals(ScreenOrientationFlag.PORTRAIT, ScreenOrientationFlag("portrait").flag)
        assertEquals(ScreenOrientationFlag.LANDSCAPE, ScreenOrientationFlag("landscape").flag)
    }

    @Test
    fun `should to multi flags when fromAlias with multi alias`() {
        assertEquals(
            ScreenOrientationFlag.PORTRAIT or ScreenOrientationFlag.LANDSCAPE,
            ScreenOrientationFlag("portrait|landscape").flag
        )
    }

    @Test
    fun `should to target alias when toAlias with single flag`() {
        assertEquals("portrait", ScreenOrientationFlag(ScreenOrientationFlag.PORTRAIT).toAlias())
        assertEquals("landscape", ScreenOrientationFlag(ScreenOrientationFlag.LANDSCAPE).toAlias())
    }

    @Test
    fun `should to multi alias when toAlias with multi flags`() {
        assertEquals(
            "portrait|landscape",
            ScreenOrientationFlag(ScreenOrientationFlag.PORTRAIT or ScreenOrientationFlag.LANDSCAPE).toAlias()
        )
    }
}