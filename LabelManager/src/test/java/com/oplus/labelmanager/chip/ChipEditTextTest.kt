/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.labelmanager.chip.ChipEditText
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.labelmanager.chip

import android.content.Context
import android.graphics.Color
import android.text.Editable
import android.text.TextPaint
import android.text.util.Rfc822Token
import android.text.util.Rfc822Tokenizer
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import com.android.ex.chips.RecipientEntry
import com.filemanager.common.MyApplication
import com.oplus.labelmanager.R
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class ChipEditTextTest {

    @MockK
    lateinit var mChipEditText: ChipEditText

    @MockK(relaxed = true)
    lateinit var context: Context

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        every { context.applicationContext }.returns(context)
        every { context.resources } returns mockk()
        MyApplication.init(context)
    }

    @Test
    fun `test onEditorAction`() {
        val view = mockk<TextView>()
        every { mChipEditText.onEditorAction(view, any(), any()) } answers { callOriginal() }
        assertFalse(mChipEditText.onEditorAction(view, 1, mockk()))

        val editable = mockk<Editable> {
            every { length } returns 10
        }
        every { mChipEditText.text } returns editable
        every { mChipEditText.commitDefault(any()) } returns false
        assertFalse(mChipEditText.onEditorAction(view, EditorInfo.IME_ACTION_DONE, mockk()))

        every { mChipEditText.commitDefault(any()) } returns true
        justRun { mChipEditText.setSelection(10) }
        assertTrue(mChipEditText.onEditorAction(view, EditorInfo.IME_ACTION_DONE, mockk()))
        verify { mChipEditText.setSelection(10) }
    }

    @Test
    fun getChipCharTest() {
        every { mChipEditText.getChipChar(any()) } answers { callOriginal() }
        assertEquals("<AAA>, <BBB>, <CCC>, ", mChipEditText.getChipChar("<AAA>, <BBB>, <CCC>, "))
    }

    @Test
    fun getChipCharListTest() {
        every { mChipEditText.text.toString() } returns "<AAA>, <BBB>, <CCC>, "
        every { mChipEditText.getChipChar(any()) } answers { callOriginal() }
        every { mChipEditText.getChipText(any()) } answers { callOriginal() }
        every { mChipEditText.getChipCharList() } answers { callOriginal() }
        assertEquals(arrayListOf("AAA", "BBB", "CCC"), mChipEditText.getChipCharList())
    }

    @Test
    fun getNoneChipCharTest() {
        every { mChipEditText.getNoneChipChar(any()) } answers { callOriginal() }
        assertEquals("DDD", mChipEditText.getNoneChipChar("<AAA>, <BBB>, <CCC>, DDD"))
    }

    @Test
    fun getChipTextTest() {
        every { mChipEditText.getChipText(any()) } answers { callOriginal() }
        assertEquals("BBB", mChipEditText.getChipText("<BBB>, "))
        assertEquals("BBB", mChipEditText.getChipText("<BBB>"))
        assertEquals("BBB", mChipEditText.getChipText(" <BBB>"))
    }

    @Test
    fun chipTokenizerFindTokenEndTest() {
        val chipTokenizer = mockk<ChipEditText.ChipTokenizer>()
        every { chipTokenizer.findTokenEnd(any(), any()) } answers { callOriginal() }
        assertEquals(12, chipTokenizer.findTokenEnd("<AAA>, <BBB>, <CCC>, DDD", 10))
    }

    @Test
    fun chipTokenizerFindTokenStartTest() {
        val chipTokenizer = mockk<ChipEditText.ChipTokenizer>()
        every { chipTokenizer.findTokenStart(any(), any()) } answers { callOriginal() }
        assertEquals(7, chipTokenizer.findTokenStart("<AAA>, <BBB>, <CCC>, DDD", 10))
    }

    @Test
    fun chipTokenizerTerminateTokenTest() {
        val chipTokenizer = mockk<ChipEditText.ChipTokenizer>()
        every { chipTokenizer.terminateToken(any()) } answers { callOriginal() }
        assertEquals("<AAA>, <BBB>, <CCC>, DDD, ", chipTokenizer.terminateToken("<AAA>, <BBB>, <CCC>, DDD"))
    }

    @Test
    fun createChipWithSelectedStateTest() {
        every { mChipEditText.createChipWithSelectedState(any(), any()) } answers { callOriginal() }
        every { mChipEditText.createAddressText(any()) } answers { "<AAA>," }
        every { mChipEditText.createTokenizedEntry(any()) } answers { callOriginal() }
        every { mChipEditText.constructChipSpan(any(), true) } answers { callOriginal() }
        mockkStatic(Rfc822Tokenizer::class)
        mockkStatic(RecipientEntry::class)
        val tokens = arrayOf(Rfc822Token("AAA", "AAA", "AAA"))
        every { Rfc822Tokenizer.tokenize(any()) } answers { tokens }
        every { RecipientEntry.constructGeneratedEntry(any(), any(), true) } answers { callOriginal() }
        val paint = TextPaint()
        paint.textSize = 16f
        every { mChipEditText.paint } answers { paint }
        every { mChipEditText.createSelectedChipBitmap(any(), any()) } answers { callOriginal() }
        every { mChipEditText.createSelectedBitmap(any(), any()) } answers { callOriginal() }
        every { mChipEditText.getChipTextColor(true) } answers { Color.BLACK }
        every { mChipEditText.obtainChipBitmapContainer() } answers { callOriginal() }
        every { mChipEditText.getDeleteRectF(any()) } answers { callOriginal() }
        every { mChipEditText.mChipSelectedBackground} answers { context.resources.getDrawable(R.drawable.compose_chip_select_bg) }
        every { mChipEditText.mChipDelete} answers { context.resources.getDrawable(R.drawable.ic_chip_delete) }
        assertEquals(mChipEditText.createChipWithSelectedState("AAA", true)?.originalText.toString(), "null")
    }
}