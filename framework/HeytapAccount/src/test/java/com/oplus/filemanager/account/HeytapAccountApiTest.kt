/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : HeytapAccountApiTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/9 11:45
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/9       1.0      create
 **********************************************************************/
package com.oplus.filemanager.account

import android.content.Context
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class HeytapAccountApiTest {

    private lateinit var context: Context

    @Before
    fun setup() {
        MockKAnnotations.init()
        context = mockk(relaxed = true)
    }

    @After
    fun teardown() {
        unmockkAll()
    }

    @Test
    fun testInitAccountClient() {
        mockkStatic(AccountManager::class)
        justRun { AccountManager.initSdk(context) }
        HeytapAccountApi().initAccountClient(context)
        verify { AccountManager.initSdk(context) }
        unmockkStatic(AccountManager::class)
    }

    @Test
    fun testIsLogin() {
        mockkStatic(AccountManager::class)
        every { AccountManager.isLogin(context) }.returns(true)
        val isLogin = HeytapAccountApi().isLogin(context)
        Assert.assertTrue(isLogin)
        unmockkStatic(AccountManager::class)
    }

    @Test
    fun testLogin() {
        mockkStatic(AccountManager::class)
        val callback: (Boolean) -> Unit = spyk()
        justRun { AccountManager.login(context, true, callback) }
        HeytapAccountApi().login(context, true, callback)
        verify { AccountManager.login(context, true, callback) }
        unmockkStatic(AccountManager::class)
    }

    @Test
    fun testGetUserToken() {
        mockkStatic(AccountManager::class)
        every { AccountManager.getUserToken(context) }.returns(USER_TOKEN)
        val token = HeytapAccountApi().getUserToken(context)
        Assert.assertEquals(USER_TOKEN, token)
        unmockkStatic(AccountManager::class)
    }

    companion object {
        private const val USER_TOKEN = "123456"
    }
}