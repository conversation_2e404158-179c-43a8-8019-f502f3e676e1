/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: AdvertManager.kt
 ** Description: ad manager
 ** Version: 4.0
 ** Date : 2020/9/27
 ** Author: Hui<PERSON><PERSON> Teng
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * HuiHua.Teng 2020/9/27      4.0    modify
 ****************************************************************/

package com.oplus.filemanager.ad

import android.content.Context
import android.widget.RelativeLayout
import com.filemanager.common.wrapper.MediaFileWrapper

@Suppress("AdvertManager")
open class AdvertManager {

    companion object {
        fun initAdLoader(context: Context) {
        }

        fun isAdEnabled(): <PERSON><PERSON><PERSON> {
            return false
        }

        fun getAdViewCount(filelist : ArrayList<MediaFileWrapper>): Int {
            return 0
        }
    }

    fun getAdView(name: String?): RelativeLayout? {
        return null
    }
}