package com.oplus.filemanager.recent.utils

import android.content.Context
import android.database.MatrixCursor
import android.os.Build
import android.provider.MediaStore
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.recent.entity.recent.BaseRecentEntity
import com.oplus.filemanager.recent.entity.recent.MediaEntity
import com.oplus.filemanager.recent.entity.recent.RecentFileEntity
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * RecentUtils 工具类的单元测试类
 * 用于测试 RecentUtils 中的各种功能方法
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [Build.VERSION_CODES.Q])
class RecentUtilsTest {

    // 模拟的 Context 对象
    private lateinit var mockContext: Context
    // 模拟的 ContentResolver 对象
    private lateinit var mockContentResolver: android.content.ContentResolver
    // 模拟的文档扩展类型接口
    private lateinit var mockDocumentExtensionType: IDocumentExtensionType

    /**
     * 测试前的初始化方法
     * 用于创建和配置模拟对象
     */
    @Before
    fun setUp() {
        // 创建模拟的 Context 对象
        mockContext = mockk(relaxed = true)
        // 创建模拟的 ContentResolver 对象
        mockContentResolver = mockk(relaxed = true)
        // 创建模拟的文档扩展类型接口
        mockDocumentExtensionType = mockk(relaxed = true)

        // 配置模拟对象的行为
        every { mockContext.contentResolver } returns mockContentResolver
        // 模拟 Injector 的静态方法
        mockkObject(Injector)
        every { Injector.injectFactory<IDocumentExtensionType>() } returns mockDocumentExtensionType
        // 配置文档扩展类型的模拟行为
        every { mockDocumentExtensionType.getAllSelectionArgs(null) } returns arrayListOf(".pdf", ".doc")
        every { mockDocumentExtensionType.getDocumentSqlQuery(any()) } returns "document_query"
    }

    /**
     * 测试后的清理方法
     * 用于释放模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试 hasDrmFileRecentEntity 方法
     * 当传入 null 列表时应该返回 false
     */
    @Test
    fun testHasDrmFileRecentEntity_NullList_ReturnsFalse() {
        assertFalse(RecentUtils.hasDrmFileRecentEntity(null))
    }

    /**
     * 测试 hasDrmFileRecentEntity 方法
     * 当传入空列表时应该返回 false
     */
    @Test
    fun testHasDrmFileRecentEntity_EmptyList_ReturnsFalse() {
        assertFalse(RecentUtils.hasDrmFileRecentEntity(emptyList()))
    }

    /**
     * 测试 hasDrmFileRecentEntity 方法
     * 当列表包含 DRM 文件时应该返回 true
     */
    @Test
    fun testHasDrmFileRecentEntity_ContainsDrmFile_ReturnsTrue() {
        // 创建模拟的 RecentFileEntity 对象
        val file = mockk<RecentFileEntity>()
        // 配置模拟对象的 mType 属性为 DRM 类型
        every { file.mType } returns MimeTypeHelper.DRM_TYPE
        // 创建包含 DRM 文件的列表
        val list = listOf<BaseRecentEntity>(file)
        // 验证方法返回 true
        assertTrue(RecentUtils.hasDrmFileRecentEntity(list))
    }

    /**
     * 测试 sortListResultQ 方法
     * 当传入 null 时应该返回 null
     */
    @Test
    fun testSortListResultQ_NullInput_ReturnsNull() {
        assertNull(RecentUtils.sortListResultQ(null))
    }

    /**
     * 测试 sortListResultR 方法
     * 当传入 null 时应该返回 null
     */
    @Test
    fun testSortListResultR_NullInput_ReturnsNull() {
        assertNull(RecentUtils.sortListResultR(null, hashMapOf()))
    }

    /**
     * 创建一个用于测试的 MatrixCursor 对象
     * 包含媒体文件的基本信息
     */
    private fun createTestCursor(): MatrixCursor {
        // 定义游标的列名
        val columns = arrayOf(
            MediaStore.MediaColumns.RELATIVE_PATH,
            MediaStore.MediaColumns.VOLUME_NAME,
            MediaStore.MediaColumns.DATE_MODIFIED,
            MediaStore.MediaColumns.SIZE,
            MediaStore.MediaColumns.DISPLAY_NAME,
            MediaStore.MediaColumns.DATA,
            MediaStore.Files.FileColumns.MEDIA_TYPE,
            MediaStore.Files.FileColumns.ORIENTATION,
            MediaStore.MediaColumns.DURATION
        )
        // 创建 MatrixCursor 并添加一行测试数据
        val cursor = MatrixCursor(columns)
        cursor.addRow(arrayOf("path", "volume", 123456L, 1024L, "test.jpg", "/storage/test.jpg", 1, 0, 0L))
        return cursor
    }
}