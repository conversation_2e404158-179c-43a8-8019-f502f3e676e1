/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WebGZipUtils.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/12 15:22
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/12       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.utils

import com.filemanager.common.utils.Log
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.util.zip.GZIPOutputStream

object WebGZipUtils {
    private const val TAG = "WebGZipUtils"
    private const val UTF_8 = "UTF-8"

    /**
     * String compressed to GZIP byte array
     * @param str
     * @param encoding
     * @return
     */
    @JvmStatic
    fun compress(str: String, encoding: String = UTF_8): ByteArray? {
        if (str.isEmpty()) {
            return null
        }
        val out = ByteArrayOutputStream()
        val gzip: GZIPOutputStream
        try {
            gzip = GZIPOutputStream(out)
            gzip.write(str.toByteArray(charset(encoding)))
            gzip.close()
        } catch (e: IOException) {
            Log.e(TAG, "gzip compress error. ${e.message}")
        }
        return out.toByteArray()
    }
}