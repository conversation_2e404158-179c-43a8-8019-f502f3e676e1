/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ArchiveDefaultInfoUiImpl.kt
 * Description:
 *     Implement to load default info UI when can not preview archive file.
 *
 * Version: 1.0
 * Date: 2024-10-15
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-15   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.archive

import android.view.View
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.preview.utils.ArchiveIconHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite

internal class ArchiveDefaultInfoUiImpl(
    private val defaultInfo: PreviewFileInfoSuite
) : IArchiveUiDefaultInfo {

    init {
        defaultInfo.setFileIcon(com.filemanager.common.R.drawable.ic_file_compress_zip)
        setNotifyMessage(null)
    }

    override fun setVisible(visible: Boolean) {
        if (visible) {
            defaultInfo.visibility = View.VISIBLE
            defaultInfo.setFilePathVisible(false)
        } else {
            defaultInfo.visibility = View.GONE
        }
    }

    override fun setPreviewFile(archiveFile: BaseFileBean) {
        defaultInfo.setFileName(archiveFile.mDisplayName)
        val iconRes = ArchiveIconHelper.getArchiveIconId(archiveFile)
        defaultInfo.setFileIcon(iconRes)
    }

    override fun setNotifyMessage(msg: String?) {
        defaultInfo.setFileMessage(msg)
    }
}