/*********************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : AlevelSuiteTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/08/01
 * Author      : W9002848
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * W9002848        2023/08/07       1.0      create
 **********************************************************************/
package com.testsuite;

import com.oplus.autotest.olt.testlib.common.OplusCategory;

import org.junit.runner.RunWith;

@RunWith(OplusCategory.class)
@OplusCategory.IncludeCasePrioritization("A")
public class AlevelSuiteTest {
    //adb shell am instrument -e test_package com.oplus -e class com.testsuite.AlevelSuiteTest
    // -w com.coloros.gallery3d.test/com.oplus.autotest.olt.testlib.common.AndroidRunner
    //会执行 所有A级的 test()
}
