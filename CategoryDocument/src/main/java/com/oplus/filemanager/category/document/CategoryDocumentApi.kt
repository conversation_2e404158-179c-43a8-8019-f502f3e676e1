/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.category.globalsearch.GlobalSearchApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/5/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.document

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.fragment.app.Fragment
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewListFragmentCreator
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.WhiteListParser
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.category.document.ui.DocParentFragment
import com.oplus.filemanager.category.document.ui.DocumentActivity
import com.oplus.filemanager.category.document.ui.DocumentFragment
import com.oplus.filemanager.category.document.ui.DocumentLoader
import com.oplus.filemanager.interfaze.categorydoc.ICategoryDocumentApi
import com.oplus.filemanager.interfaze.main.IMain

object CategoryDocumentApi : ICategoryDocumentApi {

    private const val TAG = "CategoryDocumentApi"

    override fun getAllSelectionArgs(fileExts: ArrayList<String>?): ArrayList<String?> {
        return DocumentLoader.getAllSelectionArgs(fileExts)
    }

    override fun startCategoryDocumentActivity(
        activity: Activity,
        name: String?,
    ) {
        val intent = Intent()
        intent.data = UriHelper.geCategoryUri(CategoryHelper.CATEGORY_DOC)
        intent.putExtra(Constants.TITLE_RES_ID, com.filemanager.common.R.string.string_documents)
        intent.putExtra(Constants.TITLE, name)
        intent.putExtra(Constants.CATEGORY_TYPE, CategoryHelper.CATEGORY_DOC)
        intent.setClass(activity, DocumentActivity::class.java)
        intent.putStringArrayListExtra(Constants.DOCUMENT_FORMAT_ARRAY, DocumentExtensionTypeApi.getDocumentFormat(activity))
        intent.putExtra(Constants.SQL, DocumentExtensionTypeApi.getDocumentCountSqlQuery(activity))
        activity.startActivity(intent)
    }

    override fun startCategoryDocumentFragment(activity: Activity, name: String?) {
        Log.d(TAG, "startCategoryDocumentFragment")
        val bundle = Bundle()
        bundle.putString(Constants.SQL, MediaStoreCompat.getMediaCountSqlQuery(CategoryHelper.CATEGORY_DOC))
        bundle.putInt(Constants.TITLE_RES_ID, com.filemanager.common.R.string.string_documents)
        bundle.putString(Constants.TITLE, name)
        bundle.putInt(Constants.CATEGORY_TYPE, CategoryHelper.CATEGORY_DOC)
        bundle.putStringArrayList(Constants.DOCUMENT_FORMAT_ARRAY, WhiteListParser.getDocumentFormat())
        Injector.injectFactory<IMain>()?.startFragment(activity, CategoryHelper.CATEGORY_DOC, bundle)
    }

    override fun getFragment(activity: Activity): Fragment {
        Log.d(TAG, "getFragment")
        val fragment = PreviewCombineFragment()
        fragment.setPreviewListFragmentCreator(object : IPreviewListFragmentCreator {
            override fun create(): IPreviewListFragment {
                return DocParentFragment()
            }
        })
        return fragment
    }

    override fun onResumeLoadData(fragment: Fragment) {
        Log.d(TAG, "onResumeLoadData")
        if (fragment is PreviewCombineFragment) {
            fragment.onResumeLoadData()
        }
    }

    override fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater) {
        if (fragment is PreviewCombineFragment) {
            fragment.onCreateOptionsMenu(menu, inflater)
        }
    }

    override fun onMenuItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onMenuItemSelected")
        if (fragment is PreviewCombineFragment) {
            return fragment.onMenuItemSelected(item)
        }
        return false
    }

    override fun pressBack(fragment: Fragment): Boolean {
        Log.d(TAG, "pressBack")
        if (fragment is PreviewCombineFragment) {
            return fragment.pressBack()
        }
        return false
    }

    override fun onNavigationItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onNavigationItemSelected")
        if (fragment is PreviewCombineFragment) {
            return fragment.onNavigationItemSelected(item)
        }
        return false
    }

    override fun setToolbarAndTabListener(
        fragment: Fragment,
        toolbar: COUIToolbar?,
        title: String,
        tabListener: TabActivityListener<MediaFileWrapper>
    ) {
        if (fragment is DocumentFragment) {
            fragment.setToolbarAndTabListener(toolbar, title, tabListener)
        }
    }

    override fun fromSelectPathResult(fragment: Fragment, requestCode: Int, paths: List<String>?) {
        Log.d(TAG, "fromSelectPathResult")
        if (fragment is PreviewCombineFragment) {
            fragment.fromSelectPathResult(requestCode, paths)
        }
    }

    override fun updateLabels(fragment: Fragment) {
        Log.d(TAG, "updateLabels")
        if (fragment is PreviewCombineFragment) {
            fragment.updatedLabel()
        }
    }

    override fun backToTop(fragment: Fragment) {
        Log.d(TAG, "backToTop")
        if (fragment is PreviewCombineFragment) {
            fragment.backToTop()
        }
    }

    override fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        if (fragment is PreviewCombineFragment) {
            fragment.setIsHalfScreen(isHalfScreen)
        }
    }

    override fun permissionSuccess(fragment: Fragment) {
        Log.d(TAG, "permissionSuccess")
    }

    override fun setCurrentFilePath(fragment: Fragment, path: String?) {
        Log.d(TAG, "setCurrentFilePath")
    }

    override fun getCurrentPath(fragment: Fragment): String {
        Log.d(TAG, "getCurrentPath")
        return ""
    }

    override fun exitSelectionMode(fragment: Fragment) {
        if (fragment is PreviewCombineFragment) {
            fragment.exitSelectionMode()
        }
    }

    override fun onSideNavigationClicked(fragment: Fragment, isOpen: Boolean): Boolean {
        if (fragment is PreviewCombineFragment) {
            return fragment.onSideNavigationClicked(isOpen)
        }
        return false
    }
}