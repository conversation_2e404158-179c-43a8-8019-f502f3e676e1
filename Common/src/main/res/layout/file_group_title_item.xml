<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_group_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_8dp"
        android:paddingVertical="@dimen/dimen_9dp"
        android:layout_marginStart="@dimen/file_list_margin"
        android:layout_marginEnd="@dimen/file_list_margin"
        android:textAlignment="viewStart"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textAppearance="@style/couiTextHeadlineXS"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/document_sort_string_today" />
</androidx.constraintlayout.widget.ConstraintLayout>