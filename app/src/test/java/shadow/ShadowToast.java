package shadow;

import android.content.Context;
import android.content.res.Resources;
import android.widget.Toast;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(Toast.class)
public class ShadowToast {
    private static Toast mToast;
    @Implementation
    public static Toast makeText(Context context, CharSequence text, int duration) {
        return mToast;
    }

    @Implementation
    public static Toast makeText(Context context, int resId, int duration) throws Resources.NotFoundException {
        return mToast;
    }

    public static void setmToast(Toast toast) {
        mToast = toast;
    }
}
