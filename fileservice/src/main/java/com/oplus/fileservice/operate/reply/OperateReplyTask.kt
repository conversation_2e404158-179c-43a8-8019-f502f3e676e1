/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OperateReplyTask
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/13 11:48
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/13       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.reply

import android.content.Context
import com.filemanager.common.utils.Log
import com.oplus.fileservice.operate.convert.OperateRequest
import com.oplus.fileservice.operate.convert.Response
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.convert.Request
import com.oplus.oworksdk.MsgSyncClient
import com.oplus.oworksdk.OworkSyncClientBuilder
import com.oplus.oworksdk.callback.SyncMessageResponseListener
import com.oplus.oworksdk.callback.SyncStateCallback
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import java.util.function.Consumer

/**
 * [OperateReplyTask] reply operation result to caller.
 *
 * Using [MsgSyncClient] sync single msg. Open the client and register sync state.
 * when the client is open and the [MsgSyncClient.getConnectState] is [WEB_CONNECTED],
 * Sync the operation result.
 *
 */
class OperateReplyTask(
    private val context: Context,
    private val requestId: String,
    private val request: Request
) : Consumer<Response> {

    private val countDown = CountDownLatch(1)

    override fun accept(response: Response) {
        Log.d(TAG, "accept -> requestId = $requestId response = $response")
        syncResult(response)
    }

    private fun syncResult(response: Response) {
        val msgSyncClient = OworkSyncClientBuilder.getInstance()?.build(context) as? MsgSyncClient
        if (msgSyncClient == null) {
            Log.d(TAG, "syncResult -> msgSyncClient is null.")
            return
        }
        msgSyncClient.open()
        msgSyncClient.registSyncStateCallback(object : SyncStateCallback {
            override fun onClose() {
                Log.d(TAG, "syncResult -> register SyncState onClose")
                countDown.countDown()
            }

            override fun onOpen() {
                Log.d(TAG, "syncResult -> register SyncState onOpen")
                /**
                 *  For test when the switch is open, send the message.
                 */
                if (msgSyncClient.getConnectState() < CONNECT_WAIT_WEB) {
                    Log.d(TAG, "syncResult -> sync client is not ready.")
                    countDown.countDown()
                    return
                }
                if (response is OperationResult && request is OperateRequest) {
                    val syncInfo = generateReplyInfoByOperateResult(requestId, request, response)
                    msgSyncClient.syncSingle(requestId, syncInfo, object : SyncMessageResponseListener {
                        override fun onFinished(state: Int) {
                            Log.d(TAG, "syncResult -> state = $state")
                            countDown.countDown()
                        }
                    })
                }
            }
        })

        try {
            countDown.await(SYNC_RESULT_TIMEOUT, TimeUnit.SECONDS)
        } catch (e: InterruptedException) {
            Log.d(TAG, "syncResult -> await error = ${e.message}")
        }
    }

    companion object {
        private const val TAG = "OperateReplyTask"

        // Web Connected
        private const val WEB_CONNECTED = 3
        private const val CONNECT_WAIT_WEB = 2
        private const val SYNC_RESULT_TIMEOUT = 10L
    }
}