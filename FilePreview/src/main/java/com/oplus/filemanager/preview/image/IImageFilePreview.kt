/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IImageFilePreview.kt
 * Description:
 *     The interface to show image to preview it.
 *
 * Version: 1.0
 * Date: 2024-09-05
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-09-05   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.image

import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.lifecycle.LifecycleOwner
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.preview.core.IPreviewContainerManager

internal interface IImageFilePreview {

    /**
     * Attach to container views and show image.
     */
    fun attachToContainer(containerManager: IImagePreviewContainerManager)

    /**
     * Release when not use it anymore.
     */
    fun release()

    companion object {

        @JvmStatic
        fun obtain(
            lifecycleOwner: LifecycleOwner,
            previewModel: FilePreviewViewModel
        ): IImageFilePreview = ImageFilePreviewImpl(lifecycleOwner, previewModel)
    }
}

internal interface IImagePreviewContainerManager : IPreviewContainerManager {
    val imageView: AppCompatImageView
}