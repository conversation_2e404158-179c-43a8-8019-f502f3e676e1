package com.oplus.filemanager.parentchild.adapter

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.activity.ComponentActivity
import androidx.recyclerview.widget.RecyclerView
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper
import com.oplus.filemanager.parentchild.viewholder.ClassPanelBaseOptionHolder
import com.oplus.filemanager.parentchild.viewholder.ClassPanelEditOptionHolder
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.clearAllMocks
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * SideClassPanelAdapter的单元测试类
 * 用于测试SideClassPanelAdapter的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SideClassPanelAdapterTest {

    // 定义测试所需的成员变量
    private lateinit var adapter: SideClassPanelAdapter
    private val mockContext: Context = mockk(relaxed = true)
    private val mockActivity: ComponentActivity = mockk(relaxed = true)
    private val mockAnimationHelper: ItemAnimationHelper = mockk(relaxed = true)
    private val mockItemViewCallback: (View, CategoryListBean) -> Unit = mockk(relaxed = true)
    private val mockItemDragCallback: (RecyclerView.ViewHolder) -> Unit = mockk(relaxed = true)
    private val mockClassPanelCountCallback: (Int, Long) -> Unit = mockk(relaxed = true)
    private val mockClassPanelDataList: (MutableList<CategoryListBean>) -> Unit = mockk(relaxed = true)

    /**
     * 测试前的初始化方法
     * 在每个测试方法执行前调用
     */
    @Before
    fun setUp() {
        // 清除所有mock对象的状态
        clearAllMocks()
        // 初始化待测试的adapter
        adapter = SideClassPanelAdapter(
            mockContext,
            mockActivity,
            mockAnimationHelper,
            mockItemViewCallback,
            mockItemDragCallback
        )
        // 设置adapter的回调函数
        adapter.onClassPanelCountCallback = mockClassPanelCountCallback
        adapter.onClassPanelDataList = mockClassPanelDataList
    }

    /**
     * 测试后的清理方法
     * 在每个测试方法执行后调用
     */
    @After
    fun tearDown() {
        // 清除所有mock对象的状态
        clearAllMocks()
    }

    /**
     * 测试onBindViewHolder方法对编辑类型数据的绑定
     */
    @Test
    fun `onBindViewHolder should bind data correctly for edit type`() {
        // 创建mock的ViewHolder和测试数据
        val mockHolder: ClassPanelEditOptionHolder = mockk(relaxed = true)
        val testBean = CategoryListBean(1, CategoryListBean.TYPE_EDIT_OPTIONS)
        adapter.setData(mutableListOf(testBean))

        // 调用待测试方法
        adapter.onBindViewHolder(mockHolder, 0)
        
        // 验证bindData方法是否被正确调用
        verify(exactly = 1) {
            mockHolder.bindData(
                testBean,
                mockAnimationHelper.isEdit,
                false,
                mockClassPanelCountCallback,
                mockItemViewCallback,
                mockItemDragCallback
            )
        }
    }

    /**
     * 测试onBindViewHolder方法对基础类型数据的绑定
     */
    @Test
    fun `onBindViewHolder should bind data correctly for base type`() {
        // 创建mock的ViewHolder和测试数据
        val mockHolder: ClassPanelBaseOptionHolder = mockk(relaxed = true)
        val testBean = CategoryListBean(1, 0)
        adapter.setData(mutableListOf(testBean))

        // 调用待测试方法
        adapter.onBindViewHolder(mockHolder, 0)
        
        // 验证bindData方法是否被正确调用
        verify(exactly = 1) {
            mockHolder.bindData(
                testBean,
                mockAnimationHelper.isEdit,
                false,
                mockClassPanelCountCallback,
                mockItemViewCallback
            )
        }
    }

    /**
     * 测试getItemViewType方法返回正确的视图类型
     */
    @Test
    fun `getItemViewType should return correct type`() {
        // 创建测试数据
        val testBean = CategoryListBean(1, CategoryListBean.TYPE_EDIT_OPTIONS)
        adapter.setData(mutableListOf(testBean))

        // 验证返回的类型是否正确
        assertEquals(CategoryListBean.TYPE_EDIT_OPTIONS, adapter.getItemViewType(0))
    }

    /**
     * 测试getItemCount方法返回正确的数据数量
     */
    @Test
    fun `getItemCount should return correct size`() {
        // 创建测试数据列表
        val testList = mutableListOf(
            CategoryListBean(1, 0),
            CategoryListBean(2, CategoryListBean.TYPE_EDIT_OPTIONS)
        )
        adapter.setData(testList)
        
        // 验证返回的数量是否正确
        assertEquals(2, adapter.getItemCount())
    }

    /**
     * 测试setData方法传入null时不会更新列表
     */
    @Test
    fun `setData with null should not update list`() {
        // 初始化数据
        val initialList = mutableListOf(CategoryListBean(1, 0))
        adapter.setData(initialList)
        
        // 传入null测试
        adapter.setData(null)
        
        // 验证列表未被修改
        assertEquals(initialList, adapter.getClassPanelData())
    }

    /**
     * 测试setData方法传入新列表时会更新列表
     */
    @Test
    fun `setData with new list should update list`() {
        // 创建新列表
        val newList = mutableListOf(CategoryListBean(2, 0))
        
        // 调用setData方法
        adapter.setData(newList)
        
        // 验证列表已更新
        assertEquals(newList, adapter.getClassPanelData())
    }

    /**
     * 测试setIsDragging方法能正确更新拖拽状态
     */
    @Test
    fun `setIsDragging should update dragging state`() {
        // 设置拖拽状态为true
        adapter.setIsDragging(true)
        
        // 验证状态已更新
        assertTrue(adapter.isDragging)
    }

    /**
     * 测试updateClassPanel方法能正确更新分类面板计数并通知变更
     */
    @Test
    fun `updateClassPanel should update count and notify change`() {
        // 创建测试数据
        val testBean = CategoryListBean(1, 0).apply { subTitle = "0" }
        adapter.setData(mutableListOf(testBean))

        // 调用updateClassPanel方法更新计数
        adapter.updateClassPanel(1, 5L)
        
        // 验证计数已更新
        assertEquals("5", adapter.getClassPanelData()[0].subTitle)
        // 验证回调被调用
        verify(exactly = 1) { mockClassPanelCountCallback.invoke(1, 5L) }
    }

    /**
     * 测试getClassPanelData方法返回当前数据列表
     */
    @Test
    fun `getClassPanelData should return current data list`() {
        // 创建测试数据
        val testList = mutableListOf(CategoryListBean(1, 0))
        adapter.setData(testList)
        
        // 验证返回的列表正确
        assertEquals(testList, adapter.getClassPanelData())
    }

    /**
     * 测试onViewAttachedToWindow方法能正确添加动画监听器
     */
    @Test
    fun `onViewAttachedToWindow should add listener for animation`() {
        // 创建mock的ViewHolder
        val mockHolder: ClassPanelBaseOptionHolder = mockk(relaxed = true)
        // 设置mock行为
        every { mockAnimationHelper.isEdit } returns true
        
        // 调用待测试方法
        adapter.onViewAttachedToWindow(mockHolder)
        
        // 验证添加监听器方法被调用
        verify(exactly = 1) { mockAnimationHelper.addListener(mockHolder, true) }
    }

    /**
     * 测试onViewRecycled方法能正确移除动画监听器
     */
    @Test
    fun `onViewRecycled should remove listener`() {
        // 创建mock的ViewHolder
        val mockHolder: ClassPanelBaseOptionHolder = mockk(relaxed = true)
        
        // 调用待测试方法
        adapter.onViewRecycled(mockHolder)
        
        // 验证移除监听器方法被调用
        verify(exactly = 1) { mockAnimationHelper.removeListener(mockHolder) }
    }
}