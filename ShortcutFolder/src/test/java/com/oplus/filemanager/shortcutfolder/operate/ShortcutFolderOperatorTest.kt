package com.oplus.filemanager.shortcutfolder.operate

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.provider.ShortcutFolderDBHelper
import com.oplus.filemanager.provider.ShortcutFolderMappingRecycleDBHelper
import com.oplus.filemanager.room.model.ShortcutFolderEntity
import com.oplus.filemanager.room.model.ShortcutFolderRecycleEntity
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File
import java.sql.SQLException
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * ShortcutFolderOperator的单元测试类
 * 用于测试ShortcutFolderOperator中的各种快捷文件夹操作功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class ShortcutFolderOperatorTest {

    /**
     * 在每个测试方法执行前初始化
     * 1. 使用MockK框架模拟ShortcutFolderDBHelper和ShortcutFolderMappingRecycleDBHelper
     */
    @Before
    fun setup() {
        mockkObject(ShortcutFolderDBHelper)
        mockkObject(ShortcutFolderMappingRecycleDBHelper)
    }

    /**
     * 在每个测试方法执行后清理
     * 1. 解除所有MockK模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试queryFolders方法
     * 验证从数据库查询所有快捷文件夹并转换为MainCategoryItemsBean列表的功能
     */
    @Test
    fun testQueryFolders() {
        // Given - 准备测试数据：模拟数据库返回两个快捷文件夹实体
        val mockEntities = listOf(
            ShortcutFolderEntity(1L, "Folder1", "/path1"),
            ShortcutFolderEntity(2L, "Folder2", "/path2")
        )
        every { ShortcutFolderDBHelper.queryAll() } returns mockEntities

        // When - 执行测试方法
        val result = ShortcutFolderOperator.queryFolders()

        // Then - 验证结果
        assertEquals(2, result.size)  // 验证返回列表大小
        assertEquals(1L, result[0].dbID)  // 验证第一个文件夹的ID
        assertEquals("Folder1", result[0].name)  // 验证第一个文件夹的名称
        assertEquals("/path1", result[0].path)  // 验证第一个文件夹的路径
        assertEquals(2L, result[1].dbID)  // 验证第二个文件夹的ID
        assertEquals("Folder2", result[1].name)  // 验证第二个文件夹的名称
        assertEquals("/path2", result[1].path)  // 验证第二个文件夹的路径
    }

    /**
     * 测试addShortcutFolders方法传入空路径列表的情况
     * 验证当传入空路径列表时返回包含-1的列表
     */
    @Test
    fun testAddShortcutFoldersWithEmptyPaths() {
        // When - 执行测试方法，传入null路径列表
        val result = ShortcutFolderOperator.addShortcutFolders(null)

        // Then - 验证返回结果
        assertEquals(listOf(-1L), result)  // 验证返回包含-1的列表
    }

    /**
     * 测试deleteShortcutFolder方法成功删除的情况
     * 验证删除快捷文件夹并清理相关回收站记录的功能
     */
    @Test
    fun testDeleteShortcutFolderSuccess() {
        // Given - 准备测试数据：模拟数据库查询和删除操作
        val id = 1L
        val mockEntity = ShortcutFolderEntity(id, "Folder1", "/path1")
        every { ShortcutFolderDBHelper.queryShortFolderById(id) } returns mockEntity
        every { ShortcutFolderDBHelper.delete(id) } returns 1
        every { ShortcutFolderMappingRecycleDBHelper.deleteByDeletePath(any()) } returns 1

        // When - 执行测试方法
        val result = ShortcutFolderOperator.deleteShortcutFolder(id)

        // Then - 验证结果
        assertTrue(result)  // 验证删除成功
        verify { ShortcutFolderMappingRecycleDBHelper.deleteByDeletePath("/path1") }  // 验证清理回收站记录
    }

    /**
     * 测试deleteShortcutFolder方法删除失败的情况
     * 验证当快捷文件夹不存在时返回false
     */
    @Test
    fun testDeleteShortcutFolderFailure() {
        // Given - 准备测试数据：模拟数据库查询返回null
        val id = 1L
        every { ShortcutFolderDBHelper.queryShortFolderById(id) } returns null

        // When - 执行测试方法
        val result = ShortcutFolderOperator.deleteShortcutFolder(id)

        // Then - 验证结果
        assertFalse(result)  // 验证删除失败
    }

    /**
     * 测试updateShortcutFolderUseTime方法
     * 验证更新多个快捷文件夹使用时间的功能
     */
    @Test
    fun testUpdateShortcutFolderUseTime() {
        // Given - 准备测试数据：模拟数据库更新操作
        val ids = listOf(1L, 2L)
        every { ShortcutFolderDBHelper.updateModifyTime(any(), any()) } returns Unit

        // When - 执行测试方法
        ShortcutFolderOperator.updateShortcutFolderUseTime(ids)

        // Then - 验证结果
        verify(exactly = 2) { ShortcutFolderDBHelper.updateModifyTime(any(), any()) }  // 验证更新调用次数
    }

    /**
     * 测试isAddShortcutFolder方法
     * 验证检查文件是否已添加快捷文件夹标记的功能
     */
    @Test
    fun testIsAddShortcutFolder() {
        // Given - 准备测试数据：模拟两个文件和一个快捷文件夹记录
        val fileBeans = listOf(
            BaseFileBean().apply { mData = "/path1" },
            BaseFileBean().apply { mData = "/path2" }
        )
        val mockEntities = listOf(
            ShortcutFolderEntity(1L, "Folder1", "/path1")
        )
        every { ShortcutFolderDBHelper.queryShortFolderByPaths(any()) } returns mockEntities

        // When - 执行测试方法
        ShortcutFolderOperator.isAddShortcutFolder(fileBeans)

        // Then - 验证结果
        assertTrue(fileBeans[0].mHasLabel)  // 验证第一个文件有快捷文件夹标记
        assertFalse(fileBeans[1].mHasLabel)  // 验证第二个文件没有快捷文件夹标记
    }

    /**
     * 测试updateFilePath方法
     * 验证更新快捷文件夹路径的功能
     */
    @Test
    fun testUpdateFilePath() {
        // Given - 准备测试数据：模拟数据库更新操作
        every { ShortcutFolderDBHelper.updatePath(any(), any()) } returns Unit

        // When - 执行测试方法
        ShortcutFolderOperator.updateFilePath("/old", "/new")

        // Then - 验证结果
        verify { ShortcutFolderDBHelper.updatePath("/old", "/new") }  // 验证路径更新调用
    }

    /**
     * 测试restoreFiles方法在没有回收项的情况
     * 验证当没有回收记录时不执行恢复操作
     */
    @Test
    fun testRestoreFilesWithoutRecycleItems() {
        // Given - 准备测试数据：模拟回收站查询返回空列表
        val path = "/path1"
        every { ShortcutFolderMappingRecycleDBHelper.queryByDeleteFilePath(any()) } returns emptyList()

        // When - 执行测试方法
        ShortcutFolderOperator.restoreFiles(listOf(path))

        // Then - 验证结果
        verify(exactly = 0) { ShortcutFolderDBHelper.insert(any<List<ShortcutFolderEntity>>()) }  // 验证未执行插入
        verify(exactly = 0) { ShortcutFolderMappingRecycleDBHelper.deleteByDeletePath(any()) }  // 验证未执行删除
    }

    /**
     * 测试copyFile方法在没有对应源文件夹的情况
     * 验证当源路径没有对应快捷文件夹时不执行复制操作
     */
    @Test
    fun testCopyFileWithoutExistingFolder() {
        // Given - 准备测试数据：模拟数据库查询返回null
        val sourcePath = "/source"
        val destPath = "/dest"
        every { ShortcutFolderDBHelper.queryShortFolderByPath(sourcePath) } returns null

        // When - 执行测试方法
        ShortcutFolderOperator.copyFile(sourcePath, destPath, 0, "type")

        // Then - 验证结果
        verify(exactly = 0) { ShortcutFolderDBHelper.insert(any<List<ShortcutFolderEntity>>()) }  // 验证未执行插入
    }
}