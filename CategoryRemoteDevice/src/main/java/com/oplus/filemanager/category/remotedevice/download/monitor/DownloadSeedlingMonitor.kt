/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DownloadSeedlingMonitor
 * * Description : 下载进度的监视器，以流体云作为UI展示
 * * Version     : 1.0
 * * Date        : 2025/01/07
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download.monitor

import android.app.Service
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean
import com.oplus.filemanager.category.remotedevice.download.service.DownloadTaskInfo
import com.oplus.filemanager.interfaze.cardwidget.ICardWidgetApi

class DownloadSeedlingMonitor(context: Service) : DownloadProgressMonitor(context) {

    private val cardWidgetApi: ICardWidgetApi? by lazy {
        Injector.injectFactory<ICardWidgetApi>()
    }

    fun isSupport(): Boolean {
        return cardWidgetApi?.isSupportFluidCloud(context) ?: false
    }

    override fun startMonitor(taskInfo: DownloadTaskInfo) {
        super.startMonitor(taskInfo)
        val downloadTask = taskInfo ?: return
        cardWidgetApi?.let {
            val card = it.downloadingCard(downloadTask.taskId, 0, downloadTask.localPaths())
            it.startDownloadSeedling(context, card)
        }
    }

    override fun cancelMonitor() {
        cardWidgetApi?.stopDownloadSeedling(context)
    }

    override fun onDownloadSuccess(taskInfo: DownloadTaskInfo?) {
        val downloadTask = taskInfo ?: return
        cardWidgetApi?.let {
            val card = it.downloadCompleteCard(taskInfo.downloadFiles.get(0).first, taskInfo.downloadFiles.get(0).first)
            it.updateDownloadSeeding(card)
        }
    }

    override fun onDownloadFail(taskInfo: DownloadTaskInfo?, failMsg: String) {
        val downloadTask = taskInfo ?: return
        cardWidgetApi?.let {
            val card = it.downloadFailCard(downloadTask.downloadFiles.get(0).first, failMsg)
            it.updateDownloadSeeding(card)
        }
    }

    override fun onUpdateDownloadProgress(progress: Float, taskInfo: DownloadTaskInfo?) {
        val downloadTask = taskInfo ?: return
        cardWidgetApi?.let {
            val card = it.downloadingCard(downloadTask.taskId, progress.toInt(), downloadTask.localPaths())
            it.updateDownloadSeeding(card)
        }
    }

    override fun onSingleFileProgress(path: String?, progress: Float, taskInfo: DownloadTaskInfo?) {
        super.onSingleFileProgress(path, progress, taskInfo)
        val downloadTask = taskInfo ?: return
        if (progress == DownloadRemoteFileBean.PROGRESS_100) { // 下载完成一个文件
            cardWidgetApi?.let {
                val card = it.downloadCompleteCard(path ?: "", path ?: "")
                it.updateDownloadSeeding(card)
            }
        }
    }
}