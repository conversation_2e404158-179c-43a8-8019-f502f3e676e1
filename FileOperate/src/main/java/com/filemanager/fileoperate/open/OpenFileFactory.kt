/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: ${OpenFileFactory}
 * * Description: the Factory with methods to open file
 * * Version: 1.0
 * * Date : 2020/06/23
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/06/23   1.0       the Factory with methods to open file
 ****************************************************************/
package com.filemanager.fileoperate.open

import android.annotation.SuppressLint
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.ContactsContract
import androidx.core.content.FileProvider
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.AUTHORITIES
import com.filemanager.common.fileutils.UriHelper.getFileUri
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NavigateUtils
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import org.apache.commons.io.FilenameUtils
import java.io.File
import java.util.Locale

object OpenFileFactory {

    private const val TAG = "OpenFileFactory"

    fun intentToOpenImage(path: String?, uri: Uri?, intent: Intent): Intent {
        intent.action = Intent.ACTION_VIEW
        if (FeatureCompat.sIsExpRom) {
            IntentUtils.processIntent(intent, uri, path, "image/*")
        } else {
            intent.setDataAndType(uri, "image/*")
        }
        intent.putExtra(NavigateUtils.NAVIGATE_UP_PACKAGE, KtAppUtils.getApplicationId())
        intent.putExtra(NavigateUtils.NAVIGATE_UP_TITLE_ID, com.filemanager.common.R.string.string_back)
        return intent
    }

    fun intentToOpenVideo(uri: Uri?, intent: Intent): Intent {
        intent.action = Intent.ACTION_VIEW
        intent.setDataAndType(uri, "video/*")
        return intent
    }

    fun intentToOpenAudio(uri: Uri?, intent: Intent): Intent {
        Log.v("listen one song")
        if (!SdkUtils.isAtLeastR() && FeatureCompat.sIsOnlyUseBuildInMusicPlay) {
            intent.setPackage("com.oppo.music")
            CollectPrivacyUtils.collectInstalledAppList("com.oppo.music")
        }
        intent.action = Intent.ACTION_VIEW
        intent.setDataAndType(uri, "audio/*")
        return intent
    }

    fun intentToOpenOther(context: Context, type: Int, path: String?, uri: Uri?, intent: Intent): Intent {
        var innerUri = uri
        when (type) {
            MimeTypeHelper.HTML_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "text/html")
            }
            MimeTypeHelper.TXT_TYPE, MimeTypeHelper.LRC_TYPE, MimeTypeHelper.UMD_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "text/plain")
            }
            MimeTypeHelper.EPUB_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/epub+zip")
            }
            MimeTypeHelper.EBK_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/x-expandedbook")
            }
            MimeTypeHelper.CHM_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/x-chm")
            }
            MimeTypeHelper.APPLICATION_TYPE -> {
                if (Utils.isNeededSdk28() && (path != null)) {
                    innerUri = FileProvider.getUriForFile(context, AUTHORITIES, File(path))
                }
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/vnd.android.package-archive")
            }
            MimeTypeHelper.CSV_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                if (SdkUtils.isAtLeastR()) {
                    if (!path.isNullOrEmpty()) {
                        intent.type = ContactsContract.Contacts.CONTENT_VCARD_TYPE
                        innerUri = FileProvider.getUriForFile(context, AUTHORITIES, File(path))
                        intent.data = innerUri
                        intent.putExtra(Intent.EXTRA_STREAM, innerUri?.toString() ?: path)
                    } else {
                        Log.d(TAG, "CSV_TYPE open error, path is null!")
                    }
                } else {
                    intent.setDataAndType(innerUri, "text/comma-separated-values")
                    intent.putExtra(Intent.EXTRA_STREAM, path)
                }
            }
            MimeTypeHelper.VCS_TYPE, MimeTypeHelper.ICS_TYPE -> {
                Log.d(TAG, "VCS_TYPE")
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "text/calendar")
                intent.putExtra(Intent.EXTRA_STREAM, path)
            }
            MimeTypeHelper.VCF_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.type = ContactsContract.Contacts.CONTENT_VCARD_TYPE
                if (SdkUtils.isAtLeastR() && !path.isNullOrEmpty()) {
                    innerUri = FileProvider.getUriForFile(context, AUTHORITIES, File(path))
                    intent.data = innerUri
                    intent.putExtra(Intent.EXTRA_STREAM, innerUri?.toString() ?: path)
                    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                } else {
                    intent.putExtra(Intent.EXTRA_STREAM, path)
                }
            }
            MimeTypeHelper.P12_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/x-pkcs12")
            }
            MimeTypeHelper.CER_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/pkix-cert")
            }
            MimeTypeHelper.TORRENT_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/x-bittorrent")
            }
            MimeTypeHelper.THEME_TYPE -> {
                if (Utils.isNeededSdk27()) {
                    if (SdkUtils.isAtLeastOS12()) {
                        if (FeatureCompat.sIsExpRom) {
                            intent.action = "com.oplus.themestore.action.PREVIEW_THEME"
                            intent.putExtra("oplus_preview_theme_path", path)
                        } else {
                            intent.action = "oppo.intent.action.OPPO_PREVIEW_THEME"
                            intent.putExtra("oppo_preview_theme_path", path)
                        }

                    } else {
                        intent.action = "oppo.intent.action.OPPO_PREVIEW_THEME"
                        intent.putExtra("oppo_preview_theme_path", path)
                    }
                } else {
                    intent.action = "android.intent.action.OPPO_PREVIEW_THEME"
                    intent.putExtra("oppo_preview_theme_path", path)
                }
            }
            MimeTypeHelper.DOC_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/msword")
            }
            MimeTypeHelper.DOCX_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
            }
            MimeTypeHelper.XLS_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/vnd.ms-excel")
            }
            MimeTypeHelper.XLSX_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            }
            MimeTypeHelper.PPT_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/vnd.ms-powerpoint")
            }
            MimeTypeHelper.PPTX_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/vnd.openxmlformats-officedocument.presentationml.presentation")
            }
            MimeTypeHelper.PDF_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/pdf")
            }
            MimeTypeHelper.OFD_TYPE -> {
                intent.action = Intent.ACTION_VIEW
                intent.setDataAndType(innerUri, "application/ofd")
            }
            else -> {
            }
        }
        return intent
    }

    fun getOpenFileIntent(
        context: Context,
        localType: Int,
        localFileUri: Uri,
        filePath: String
    ): Intent {
        var intent = Intent()
        intent.putExtra(FileActionOpen.OPEN_FLAG, true)
        intent.putExtra(FileActionOpen.NEW_OPEN_FLAG, true)
        when (localType) {
            MimeTypeHelper.IMAGE_TYPE -> {
                intentToOpenImage(filePath, localFileUri, intent)
            }
            MimeTypeHelper.VIDEO_TYPE -> {
                intentToOpenVideo(localFileUri, intent)
            }
            MimeTypeHelper.AUDIO_TYPE -> {
                intentToOpenAudio(localFileUri, intent)
            }
            else -> {
                intent = if (Injector.injectFactory<IDocumentExtensionType>()?.canOpenByFileManager(filePath) == true) {
                    obtainAnyFileIntent(filePath, localFileUri, intent)
                } else {
                    intentToOpenOther(context, localType, filePath, localFileUri, intent)
                }
            }
        }
        return intent
    }

    private fun obtainAnyFileIntent(path: String?, uri: Uri?, intent: Intent): Intent {
        val type = MimeTypeHelper.getTypeFromPath(path)
        val mimeType = MimeTypeHelper.getMimeTypeByFileType(type)
        Log.d(TAG, "intentToOpenOther -> mimeType = $mimeType")
        intent.action = Intent.ACTION_VIEW
        intent.setDataAndType(uri, mimeType)
        return intent
    }

    fun obtainIntentForDefaultApp(type: Int, uri: Uri?, intent: Intent): Intent {
        val mimeType = MimeTypeHelper.getMimeTypeForDefaultApp(type)
        Log.d(TAG, "obtainIntentForDefaultApp -> mimeType = $mimeType")
        intent.action = Intent.ACTION_VIEW
        intent.setDataAndType(uri, mimeType)
        return intent
    }

    fun openAudioFiles(context: Context, file: BaseFileBean, name: Array<String>?): Boolean? {

        val intent = Intent().apply {
            action = Intent.ACTION_VIEW
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        val uri = file.mLocalFileUri?.apply {
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    .addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
        } ?: getFileUri(file, intent) ?: return false
        intent.setDataAndType(uri, "audio/*")
        if (FileActionOpen.UnknownFileManager.isForNameFindApp(context, name)) {
            try {
                if (UIConfigMonitor.isZoomWindowShow()) {
                    CustomToast.showLong(com.filemanager.common.R.string.toast_opened_without_window_mode)
                }
                context.startActivity(intent)
            } catch (e: Exception) {
                Log.w(TAG, "openAudioFiles : ${e.message}")
                return false
            }
        } else {
            return null
        }
        return true
    }

    fun openVideoFiles(context: Context, file: BaseFileBean, name: Array<String>?): Boolean? {
        val intent = Intent().apply {
            action = Intent.ACTION_VIEW
        }
        val uri = file.mLocalFileUri?.apply {
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    .addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
        } ?: getFileUri(file, intent) ?: return false
        val ext = FilenameUtils.getExtension(file.mData)
        if (ext == null || ext.isEmpty()) {
            return false
        }
        var fileType = ".$ext"
        fileType = fileType.lowercase(Locale.getDefault())
        var state = false
        if (fileType.equals(".rmvb", ignoreCase = true) || fileType.equals(".rm", ignoreCase = true)
                || fileType.equals(".ac3", ignoreCase = true)) {
            intent.setDataAndType(uri, "video/x-pn-realaudio")
        } else {
            state = true
        }
        if (state) {
            return false
        }
        if (FileActionOpen.UnknownFileManager.isForNameFindApp(context, name)) {
            try {
                if (UIConfigMonitor.isZoomWindowShow()) {
                    CustomToast.showLong(com.filemanager.common.R.string.toast_opened_without_window_mode)
                }
                context.startActivity(intent)
            } catch (e: Exception) {
                Log.w(TAG, "openVideoFiles : ${e.message}")
                return false
            }
        } else {
            return null
        }
        return true
    }

    fun openOfficeFiles(context: Context, file: BaseFileBean, name: Array<String>?): Boolean? {
        val intent = Intent()
        intent.action = Intent.ACTION_VIEW
        val uri = file.mLocalFileUri?.apply {
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    .addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
        } ?: getFileUri(file, intent, null) ?: return false
        val ext = FilenameUtils.getExtension(file.mData)
        if (ext == null || ext.isEmpty()) {
            return false
        }
        var fileType = ".$ext"
        fileType = fileType.lowercase(Locale.getDefault())
        var state = false
        if (fileType.equals(".doc", ignoreCase = true) || fileType.equals(".docx", ignoreCase = true) || fileType.equals(".dot", ignoreCase = true)
                || fileType.equals(".dotx", ignoreCase = true)) {
            intent.setDataAndType(uri, "application/vnd.ms-word")
        } else if (fileType.equals(".pdf", ignoreCase = true) || fileType.equals(".pdfx", ignoreCase = true)) {
            intent.setDataAndType(uri, "application/pdf")
        } else if (fileType.equals(".ofd", ignoreCase = true)) {
            intent.setDataAndType(uri, "application/ofd")
        } else if (fileType.equals(".ppt", ignoreCase = true) || fileType.equals(".pptx", ignoreCase = true)
                || fileType.equals(".pps", ignoreCase = true) || fileType.equals(".ppsx", ignoreCase = true)
                || fileType.equals(".pot", ignoreCase = true) || fileType.equals(".potx", ignoreCase = true)) {
            intent.setDataAndType(uri, "application/vnd.ms-powerpoint")
        } else if (fileType.equals(".xls", ignoreCase = true) || fileType.equals(".xlsx", ignoreCase = true)
                || fileType.equals(".xlt", ignoreCase = true) || fileType.equals(".xltx", ignoreCase = true)) {
            intent.setDataAndType(uri, "application/vnd.ms-excel")
        } else {
            state = true
        }
        if (state) {
            return false
        }
        if (FileActionOpen.UnknownFileManager.isForNameFindApp(context, name)) {
            try {
                if (UIConfigMonitor.isZoomWindowShow()) {
                    CustomToast.showLong(com.filemanager.common.R.string.toast_opened_without_window_mode)
                }
                context.startActivity(intent)
            } catch (e: Exception) {
                Log.w(TAG, "openOfficeFiles : ${e.message}")
                return false
            }
        } else {
            return null
        }
        return true
    }

    fun openTorrentFiles(context: Context, file: BaseFileBean, name: Array<String>?): Boolean? {
        val intent = Intent()
        intent.action = Intent.ACTION_VIEW
        val uri = file.mLocalFileUri?.apply {
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    .addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
        } ?: getFileUri(file, intent, null) ?: return false
        val ext = FilenameUtils.getExtension(file.mData)
        if (ext == null || ext.isEmpty()) {
            return false
        }
        var fileType = ".$ext"
        fileType = fileType.lowercase(Locale.getDefault())
        var state = false
        if (fileType.equals(".torrent", ignoreCase = true)) {
            intent.setDataAndType(uri, "application/x-bittorrent")
        } else {
            state = true
        }
        if (state) {
            return false
        }
        if (FileActionOpen.UnknownFileManager.isForNameFindApp(context, name)) {
            try {
                if (UIConfigMonitor.isZoomWindowShow()) {
                    CustomToast.showLong(com.filemanager.common.R.string.toast_opened_without_window_mode)
                }
                context.startActivity(intent)
            } catch (e: Exception) {
                Log.w(TAG, "openTorrentFiles : ${e.message}")
                return false
            }
        } else {
            return null
        }
        return true
    }

    @SuppressLint("QueryPermissionsNeeded")
    @JvmStatic
    fun getChooseIntentExcludeQuickPreview(context: Context, intent: Intent): Intent {
        val thisPackage = KtConstants.QUICK_PREVIEW_PACKAGE
        val targets = ArrayList<ComponentName>()
        val activities = context.packageManager.queryIntentActivities(intent, 0)
        activities.forEach {
            val packageName = it?.activityInfo?.packageName ?: return@forEach
            val name = it.activityInfo?.name ?: return@forEach
            Log.d(TAG, "getQuickPreviewComponentName name = $name")
            if (packageName == thisPackage) {
                targets.add(ComponentName(packageName, name))
            }
            CollectPrivacyUtils.collectInstalledAppList(packageName)
        }
        return Intent.createChooser(intent, null).apply {
            putExtra(Intent.EXTRA_EXCLUDE_COMPONENTS, targets.toTypedArray())
        }
    }

    @SuppressLint("QueryPermissionsNeeded")
    @JvmStatic
    fun getExcludeComponent(context: Context, intent: Intent): Array<ComponentName> {
        val targets = ArrayList<ComponentName>()
        val activities = context.packageManager.queryIntentActivities(intent, 0)
        activities.forEach {
            val packageName = it?.activityInfo?.packageName ?: return@forEach
            val name = it.activityInfo?.name ?: return@forEach
            Log.d(TAG, "getExcludeComponent name = $name")
            if (packageName == context.packageName) {
                targets.add(ComponentName(packageName, name))
            }
        }
        return targets.toTypedArray()
    }
}
