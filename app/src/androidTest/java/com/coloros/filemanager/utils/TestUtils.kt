/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * File        :  -
 * * Description : com.coloros.filemanager
 * * Version     : 1.0
 * * Date        : 2020/11/13
 * * Author      : W9006071
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
package com.coloros.filemanager.utils

import android.app.Activity
import android.app.ActivityManager
import com.filemanager.common.fileutils.UriHelper.geCategoryUri
import com.filemanager.common.compat.MediaStoreCompat.getMediaCountSqlQuery
import androidx.test.uiautomator.UiDevice
import androidx.test.uiautomator.UiObject2
import androidx.test.uiautomator.By
import android.app.ActivityManager.RunningTaskInfo
import android.content.Context
import android.content.Intent
import android.os.ConditionVariable
import android.os.SystemClock
import androidx.test.platform.app.InstrumentationRegistry
import com.coloros.filemanager.R
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.compat.MediaStoreCompat
import com.oplus.filemanager.category.apk.ui.ApkActivity
import com.oplus.filemanager.category.albumset.ui.AlbumSetActivity
import com.oplus.filemanager.category.audiovideo.ui.CategoryAudioActivity
import com.oplus.filemanager.category.document.ui.DocumentActivity
import com.filemanager.common.utils.WhiteListParser
import com.filemanager.categorycompress.ui.CategoryCompressActivity
import com.coloros.filemanager.utils.TestActivityLifecycleCallbacksImpl
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.Log
import com.oplus.filemanager.main.ui.MainActivity
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.RandomAccessFile
import java.lang.Exception
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import org.junit.Assert

object TestUtils {
    private const val TAG = "TestUtils"
    private const val APK_TITLE = "安装包"
    private const val AUDIO_TITLE = "音频"
    private const val VIDEO_TITLE = "视屏"
    private const val IMAGE_TITLE = "图片"
    private const val COMPRESS_TITLE = "压缩包"
    const val APK_ITEM_TYPE = 4
    private val mUiDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())

    fun initDialog(context: Context) {
        val Agree = context.resources.getString(com.filemanager.common.R.string.color_runtime_dialog_ok_r)
        val object2 = mUiDevice.findObject(By.text(Agree))
        if (object2 != null) {
            object2.click()
            SystemClock.sleep(500)
        }
    }

    @Throws(IOException::class)
    fun createBigFile(file: File, length: Long) {
        if (file.exists()) {
            file.delete()
        }
        val start = System.currentTimeMillis()
        var randomAccessFile: RandomAccessFile? = null
        try {
            randomAccessFile = RandomAccessFile(file, "rw")
            randomAccessFile.setLength(length * 1024 * 1024)
        } finally {
            if (randomAccessFile != null) {
                try {
                    randomAccessFile.close()
                } catch (e: IOException) {
                    Assert.fail(e.toString())
                }
            }
        }
        val end = System.currentTimeMillis()
        val createTime = end - start
        TestLog.d(TAG, "create time = $createTime, create big file success！")
    }

    //createFile
    @JvmStatic
    fun createNewFile(fileName: String?) {
        val file = File(fileName)
        if (file.exists()) {
            val delete = file.delete()
            Assert.assertTrue("File Delete Failed", delete)
            createNewFile(fileName)
        } else if (!file.isDirectory) {
            var fileOutputStream: FileOutputStream? = null
            val string = "Test"
            val bytes = string.toByteArray()
            try {
                fileOutputStream = FileOutputStream(file)
                fileOutputStream.write(bytes)
            } catch (e: IOException) {
                Assert.fail("mOutputStream.write Error :$e")
            } finally {
                try {
                    fileOutputStream?.close()
                } catch (e: IOException) {
                    Assert.fail(" mOutputStream.close Error :$e")
                }
            }
        }
    }

    //createFolder
    @JvmStatic
    fun createFolder(filePath: String?) {
        val file = File(filePath)
        if (!file.exists()) {
            file.mkdirs()
            TestLog.d(TAG, "create file success: $filePath")
        } else if (!file.isDirectory) {
            val delete = file.delete()
            Assert.assertTrue("File Delete Failed", delete)
            createFolder(filePath)
        } else {
            deleteFile(file)
            createFolder(filePath)
        }
    }

    //DeleteFile
    @JvmStatic
    fun deleteFile(file: File) {
        if (!file.exists()) {
            TestLog.d(TAG, "File Does Not Exist: ${file.absolutePath}")
            return
        } else {
            TestLog.d(TAG, "Delete file success: ${file.absolutePath}")
            if (file.isFile) {
                val delete = file.delete()
                Assert.assertTrue("File Delete Failed", delete)
                return
            }
            if (file.isDirectory) {
                val childFile = file.listFiles()
                if (childFile.isNullOrEmpty()) {
                    val delete = file.delete()
                    Assert.assertTrue("File Delete Failed", delete)
                    return
                }
                for (f in childFile) {
                    deleteFile(f)
                }
            }
            file.delete()
        }
    }

    //CreateZip
    @JvmStatic
    fun createZip(file: File) {
        var zipOutputStream: ZipOutputStream? = null
        if (!file.exists()) {
            try {
                file.createNewFile()
                val string = "TestZip"
                val bytes = string.toByteArray()
                val fileOutputStream = FileOutputStream(file)
                zipOutputStream = ZipOutputStream(fileOutputStream)
                val zipEntry = ZipEntry("Test.txt")
                zipOutputStream.putNextEntry(zipEntry)
                zipOutputStream.write(bytes)
            } catch (e: IOException) {
                Assert.fail(" fileOutputStream Error :$e")
            } finally {
                try {
                    if (zipOutputStream != null) {
                        zipOutputStream.closeEntry()
                        zipOutputStream.close()
                    }
                } catch (e: IOException) {
                    Assert.fail(" mZipOutputStream。close Error :$e")
                }
            }
        } else {
            val delete = file.delete()
            Assert.assertTrue("File Delete Failed", delete)
        }
    }

    //Get ActivityName for the current stack
    @JvmStatic
    fun getActivityName(context: Context): String? {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val forGroundActivity = activityManager.getRunningTasks(1)
        return if (forGroundActivity != null) {
            val currentActivity: RunningTaskInfo
            currentActivity = forGroundActivity[0]
            currentActivity.topActivity!!.className
        } else {
            Assert.fail("forGroundActivity is null")
            null
        }
    }

    @JvmStatic
    fun startCategoryActivity(activity: Activity, position: Int) {
        val intent = Intent()
        when (position) {
            CategoryHelper.CATEGORY_APK -> {
                intent.data = geCategoryUri(CategoryHelper.CATEGORY_APK)
                intent.putExtra(Constants.SQL, getMediaCountSqlQuery(CategoryHelper.CATEGORY_APK))
                intent.setClass(activity, ApkActivity::class.java)
                intent.putExtra(Constants.TITLE, APK_TITLE)
                intent.putExtra(Constants.CATEGORY_TYPE, CategoryHelper.CATEGORY_APK)
            }
            CategoryHelper.CATEGORY_IMAGE -> {
                intent.setClass(activity, AlbumSetActivity::class.java)
                intent.putExtra(Constants.TITLE, IMAGE_TITLE)
                intent.putExtra(Constants.CATEGORY_TYPE, CategoryHelper.CATEGORY_IMAGE)
            }
            CategoryHelper.CATEGORY_AUDIO -> {
                intent.setClass(activity, CategoryAudioActivity::class.java)
                intent.putExtra(Constants.TITLE, AUDIO_TITLE)
                intent.putExtra(Constants.CATEGORY_TYPE, CategoryHelper.CATEGORY_AUDIO)
            }
            CategoryHelper.CATEGORY_VIDEO -> {
                intent.setClass(activity, CategoryAudioActivity::class.java)
                intent.putExtra(Constants.TITLE, VIDEO_TITLE)
                intent.putExtra(Constants.CATEGORY_TYPE, CategoryHelper.CATEGORY_VIDEO)
            }
            CategoryHelper.CATEGORY_DOC -> {
                intent.data = geCategoryUri(CategoryHelper.CATEGORY_DOC)
                intent.putExtra(Constants.SQL, getMediaCountSqlQuery(CategoryHelper.CATEGORY_DOC))
                intent.setClass(activity, DocumentActivity::class.java)
                intent.putStringArrayListExtra(Constants.DOCUMENT_FORMAT_ARRAY, WhiteListParser.getDocumentFormat())
            }
            CategoryHelper.CATEGORY_COMPRESS -> {
                intent.setClass(activity, CategoryCompressActivity::class.java)
                intent.putExtra(Constants.TITLE, COMPRESS_TITLE)
                intent.putExtra(Constants.CATEGORY_TYPE, CategoryHelper.CATEGORY_COMPRESS)
                Assert.fail("startCategoryActivity fail")
            }
            else -> Assert.fail("startCategoryActivity fail")
        }
        activity.startActivity(intent)
    }

    fun getApkActivity(activity: Activity): ApkActivity? {
        val application = activity.application
        val variable = ConditionVariable()
        val curActivites = arrayOfNulls<Activity>(1)
        var curActivity: ApkActivity? = null
        var callbacks: TestActivityLifecycleCallbacksImpl? = null
        try {
            callbacks = object : TestActivityLifecycleCallbacksImpl() {
                override fun onActivityResumed(activity: Activity) {
                    if (activity is ApkActivity) {
                        curActivites[0] = activity
                        variable.open()
                    }
                }
            }
        } catch (e: Exception) {
            Assert.fail("getApkActivity Error")
        } finally {
            application.registerActivityLifecycleCallbacks(callbacks)
            startApkActivity(activity)
            variable.close()
            variable.block(5000)
            Assert.assertNotNull(curActivites[0])
            curActivity = curActivites[0] as ApkActivity?
            application.unregisterActivityLifecycleCallbacks(callbacks)
        }
        return curActivity
    }

    fun getMainActivity(activity: Activity): MainActivity? {
        val application = activity.application
        val variable = ConditionVariable()
        val curActivites = arrayOfNulls<Activity>(1)
        var callbacks: TestActivityLifecycleCallbacksImpl? = null
        var curActivity: MainActivity? = null
        try {
            callbacks = object : TestActivityLifecycleCallbacksImpl() {
                override fun onActivityResumed(activity: Activity) {
                    if (activity is MainActivity) {
                        curActivites[0] = activity
                        variable.open()
                    }
                }
            }
        } catch (e: Exception) {
            Assert.fail("GetMainActivity Error")
        } finally {
            application.registerActivityLifecycleCallbacks(callbacks)
            startMainActivity(activity)
            variable.close()
            variable.block(5000)
            Assert.assertNotNull(curActivites[0])
            curActivity = curActivites[0] as MainActivity?
            application.unregisterActivityLifecycleCallbacks(callbacks)
        }
        return curActivity
    }

    private fun startMainActivity(activity: Activity) {
        val intent = Intent()
        intent.setClass(activity, MainActivity::class.java)
        activity.startActivity(intent)
    }

    private fun startApkActivity(activity: Activity) {
        val intent = Intent()
        intent.data = geCategoryUri(CategoryHelper.CATEGORY_APK)
        intent.putExtra(Constants.SQL, getMediaCountSqlQuery(CategoryHelper.CATEGORY_APK))
        intent.setClass(activity, ApkActivity::class.java)
        intent.putExtra(Constants.TITLE, APK_TITLE)
        intent.putExtra(Constants.CATEGORY_TYPE, CategoryHelper.CATEGORY_APK)
        activity.startActivity(intent)
    }
}