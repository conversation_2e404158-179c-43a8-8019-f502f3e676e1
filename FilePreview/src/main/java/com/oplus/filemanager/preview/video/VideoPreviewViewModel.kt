/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - VideoPreviewViewModel.kt
 * Description:
 *     The view model for preview video file.
 *
 * Version: 1.0
 * Date: 2024-09-19
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-09-19   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.video

import android.content.Context
import com.oplus.filemanager.preview.core.FilePreviewViewModel

internal class VideoPreviewViewModel : FilePreviewViewModel() {

    @Volatile
    var videoPlayer: IPreviewVideoPlayer? = null
        private set

    @Synchronized
    fun obtainVideoPlayer(context: Context): IPreviewVideoPlayer {
        videoPlayer?.let { return it }
        val newPlayer = IPreviewVideoPlayer.obtain(context)
        videoPlayer = newPlayer
        return newPlayer
    }

    @Synchronized
    fun releaseVideoPlayer() {
        val player = videoPlayer ?: return
        videoPlayer = null
        player.release()
    }

    override fun onCleared() {
        super.onCleared()
        releaseVideoPlayer()
    }
}