/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : LocalOpenEntity.java
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * *
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.bytedance.ee.bear.contract.drive.sdk.entity.open;

import android.os.Parcel;

public class LocalOpenEntity extends BaseOpenEntity {

    public static final Creator<LocalOpenEntity> CREATOR = new Creator<LocalOpenEntity>() {
        @Override
        public LocalOpenEntity createFromParcel(Parcel in) {
            return new LocalOpenEntity(in);
        }

        @Override
        public LocalOpenEntity[] newArray(int size) {
            return new LocalOpenEntity[size];
        }
    };

    private String mBizContentProviderAuthority;
    private String mFileType;
    private String mLocalFilePath;
    private String mThirdPartyAppID;

    protected LocalOpenEntity(Parcel parcel) {
        super(parcel);
        this.mLocalFilePath = parcel.readString();
        this.mFileType = parcel.readString();
        this.mThirdPartyAppID = parcel.readString();
        this.mBizContentProviderAuthority = parcel.readString();
    }


    public String getBizContentProviderAuthority() {
        return this.mBizContentProviderAuthority;
    }

    public String getFileType() {
        return this.mFileType;
    }

    public String getLocalFilePath() {
        return this.mLocalFilePath;
    }

    public String getLocalPath() {
        return this.mLocalFilePath;
    }

    public String getThirdPartyAppID() {
        return this.mThirdPartyAppID;
    }
}
