/***********************************************************
 * * Copyright (C), 2008-2017 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:ReplaceDialog.kt
 * * Description:Replace Dialog for copy/cut file
 * * Version: 1.0
 * * Date :2020/05/20
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 *     lijin       2020/05/20  1.0          Replace Dialog for copy/cut file
 ****************************************************************/
package com.filemanager.fileoperate.copy

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.CheckBox
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.helper.getBottomAlertDialogWindowAnimStyle
import com.filemanager.common.helper.getBottomAlertDialogWindowGravity
import com.filemanager.fileoperate.R

class FileReplaceDialog(private val context: Context, private val replaceBean: FileCopyObserver.FileReplaceBean) {
    companion object {
        const val CHOOSE_NONE = -1
        const val CHOOSE_KEEP_BOTH = 0
        const val CHOOSE_REPLACE = 1
        const val CHOOSE_SKIP = 2
    }

    private var mDialog: AlertDialog
    private var mLastChoose = CHOOSE_NONE

    init {
        val view = LayoutInflater.from(context).inflate(R.layout.fop_dialog_replace_layout, null)
        initViews(view)
        mDialog = COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
                .setWindowGravity(getBottomAlertDialogWindowGravity(context))
                .setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(context))
                .setView(view)
                .setCancelable(false)
                .setOnDismissListener {
                    val checkBox = view.findViewById<CheckBox>(R.id.replace_file_cb)
                    replaceBean.chooseListener?.onChoose(mLastChoose, checkBox?.isChecked
                            ?: false)
                }
                .setMessage(if (replaceBean.mIsReplace) {
                    context.getString(com.filemanager.common.R.string.directory_exist_error_format, replaceBean.mFileName)
                } else {
                    context.getString(com.filemanager.common.R.string.file_move_exception, replaceBean.mFileName)
                })
                .setNegativeButton(com.filemanager.common.R.string.alert_dialog_cancel, null)
                .create()
    }

    private fun initViews(view: View) {
        val items = if (replaceBean.mIsReplace) {
            arrayOf(context.getString(com.filemanager.common.R.string.keep_double),
                    context.getString(com.filemanager.common.R.string.replace_this_file),
                    context.getString(com.filemanager.common.R.string.skip_continue_button_text))
        } else {
            arrayOf(context.getString(com.filemanager.common.R.string.skip_continue_button_text))
        }
        val chooses = if (replaceBean.mIsReplace) {
            arrayOf(CHOOSE_KEEP_BOTH, CHOOSE_REPLACE, CHOOSE_SKIP)
        } else {
            arrayOf(CHOOSE_SKIP)
        }
        val buttons = arrayOf(
                view.findViewById(R.id.button1),
                view.findViewById(R.id.button2),
                view.findViewById<Button>(R.id.button3)
        )
        for (i in buttons.indices) {
            buttons[i]?.let {
                if (i < items.size) {
                    it.visibility = View.VISIBLE
                    it.text = items[i]
                    it.setOnClickListener {
                        mLastChoose = chooses[i]
                        dismiss()
                    }
                } else {
                    it.visibility = View.GONE
                }
            }
        }
    }

    fun isShowing() = mDialog.isShowing

    fun show() {
        try {
            mDialog.show()
        } catch (e: Exception) {
        }
    }

    fun dismiss() {
        try {
            mDialog.dismiss()
        } catch (e: Exception) {
        }
    }

    interface OnChooseListener {
        fun onChoose(chooseId: Int, rememberChoose: Boolean)
    }
}
