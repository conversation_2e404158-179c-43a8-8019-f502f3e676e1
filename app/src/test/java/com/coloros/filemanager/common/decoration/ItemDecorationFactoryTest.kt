package com.coloros.filemanager.common.decoration

import android.content.res.Resources
import android.graphics.Point
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.decoration.AlbumSetItemDecoration
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_COUNT_10
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_COUNT_11
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_COUNT_12
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_COUNT_14
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_COUNT_3
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_COUNT_4
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_COUNT_6
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_COUNT_8
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_COUNT_9
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.WIDTH_IN_DP_1080
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.WIDTH_IN_DP_1200
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.WIDTH_IN_DP_455
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.WIDTH_IN_DP_566
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.WIDTH_IN_DP_677
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.WIDTH_IN_DP_840
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.WIDTH_IN_DP_841
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.WIDTH_IN_DP_914
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.getMainCategoryGridCount
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.KtViewUtils
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class ItemDecorationFactoryTest {
    @MockK
    lateinit var res: Resources

    @MockK
    lateinit var activity: BaseVMActivity

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        res = mockk(relaxed = true)
        activity = mockk(relaxed = true) {
            every { resources } returns res
        }
        mockkStatic(ViewHelper::class)
        mockkObject(ItemDecorationFactory)
    }

    @After
    fun tearDown() {
        unmockkStatic(ViewHelper::class)
        unmockkObject(ItemDecorationFactory)
        unmockkAll()
    }

    @Test
    fun `should return 1 count when activity is null`() {
        assertEquals(1,
            ItemDecorationFactory.getGridItemCount(null, KtConstants.SCAN_MODE_LIST, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET))
    }

    @Test
    fun `should return 1 count when mode is list`() {
        assertEquals(1, ItemDecorationFactory.getGridItemCount(activity,
            KtConstants.SCAN_MODE_LIST,
            ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET)
                    )
    }


    //-------album set category ------------
    @Test
    fun `should return 3 when grid and album set category if screen width between 0 until 640`() {
        every { ViewHelper.px2dip(activity, any()) } returns 0
        assertEquals(3,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET))
        every { ViewHelper.px2dip(activity, any()) } returns 320
        assertEquals(3,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET))
        every { ViewHelper.px2dip(activity, any()) } returns 639
        assertEquals(3,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET))
    }

    @Test
    fun `should return 5 when grid and album set category if screen width between 640 until 840`() {
        every { ViewHelper.px2dip(activity, any()) } returns 640
        assertEquals(5,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET))
        every { ViewHelper.px2dip(activity, any()) } returns 655
        assertEquals(5,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET))
        every { ViewHelper.px2dip(activity, any()) } returns 839
        assertEquals(5,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET))
    }

    @Test
    fun `should return 6 when grid and album set category if screen width more than 840`() {
        every { ViewHelper.px2dip(activity, any()) } returns 840
        assertEquals(6,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET))
        every { ViewHelper.px2dip(activity, any()) } returns 849
        assertEquals(6,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET))
        every { ViewHelper.px2dip(activity, any()) } returns 1696
        assertEquals(6,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET))
    }
    //-------album set category ------------


    //-------image and video category  ------------
    @Suppress("LongMethod")
    @Test
    fun `should return 4 when grid and image and video if screen width between 302 until 482`() {
        val column = 4
        every { ViewHelper.px2dip(activity, any()) } returns 302
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 320
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 360
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 482
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
    }

    @Suppress("LongMethod")
    @Test
    fun `should return 5 when grid and image and video if screen width between 483 until 604`() {
        val column = 5
        every { ViewHelper.px2dip(activity, any()) } returns 483
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 565
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 603
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
    }

    @Suppress("LongMethod")
    @Test
    fun `should return 6 when grid and image and video if screen width between 605 until 724`() {
        val column = 6
        every { ViewHelper.px2dip(activity, any()) } returns 605
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 700
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 724
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
    }

    @Suppress("LongMethod")
    @Test
    fun `should return 7 when grid and image and video if screen width between 725 until 845`() {
        val column = 7
        every { ViewHelper.px2dip(activity, any()) } returns 725
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 800
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 844
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
    }

    @Suppress("LongMethod")
    @Test
    fun `should return 8 when grid and image and video if screen width between 846 until 966`() {
        val column = 8
        every { ViewHelper.px2dip(activity, any()) } returns 846
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 900
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 966
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
    }

    @Suppress("LongMethod")
    @Test
    fun `should return 9 when grid and image and video if screen width between 967 until 1088`() {
        val column = 9
        every { ViewHelper.px2dip(activity, any()) } returns 967
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 1000
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 1087
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
    }

    @Suppress("LongMethod")
    @Test
    fun `should return 10 when grid and image and video if screen width more than 1089 `() {
        val column = 10
        every { ViewHelper.px2dip(activity, any()) } returns 1089
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 1100
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
        every { ViewHelper.px2dip(activity, any()) } returns 1208
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_VIDEO))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER,
                Constants.TAB_IMAGE))
        assertEquals(column,
            ItemDecorationFactory.getGridItemCount(activity,
                KtConstants.SCAN_MODE_GRID,
                ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO,
                CategoryHelper.CATEGORY_VIDEO))
    }
    //-------image and video category ------------

    //-------common category   ------------
    @Test
    fun `test default column`() {
        every { ViewHelper.px2dip(activity, any()) } returns 100
        assertEquals(3,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER))
        every { ViewHelper.px2dip(activity, any()) } returns 454
        assertEquals(3,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER))
        every { ViewHelper.px2dip(activity, any()) } returns 455
        assertEquals(4,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER))
        every { ViewHelper.px2dip(activity, any()) } returns 565
        assertEquals(4,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER))
        every { ViewHelper.px2dip(activity, any()) } returns 566
        assertEquals(5,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER))
        every { ViewHelper.px2dip(activity, any()) } returns 676
        assertEquals(5,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER))
        every { ViewHelper.px2dip(activity, any()) } returns 677
        assertEquals(6,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER))
        every { ViewHelper.px2dip(activity, any()) } returns 839
        assertEquals(6,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER))
        every { ViewHelper.px2dip(activity, any()) } returns 840
        assertEquals(8,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER))
        every { ViewHelper.px2dip(activity, any()) } returns 913
        assertEquals(8,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER))
        every { ViewHelper.px2dip(activity, any()) } returns 914
        assertEquals(9,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER))
        every { ViewHelper.px2dip(activity, any()) } returns 2000
        assertEquals(9,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER))
    }
    //-------common category  ------------


    @Test
    fun getRecentGridCountTest() {
        every { ViewHelper.px2dip(activity, any()) } returns 200
        assertEquals(
            GRID_ITEM_COUNT_4,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_RECENT))

        every { ViewHelper.px2dip(activity, any()) } returns 500
        assertEquals(ItemDecorationFactory.GRID_ITEM_COUNT_6,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_RECENT))

        every { ViewHelper.px2dip(activity, any()) } returns 600
        assertEquals(ItemDecorationFactory.GRID_ITEM_COUNT_8,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_RECENT))

        every { ViewHelper.px2dip(activity, any()) } returns 700
        assertEquals(ItemDecorationFactory.GRID_ITEM_COUNT_8,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_RECENT))

        every { ViewHelper.px2dip(activity, any()) } returns 900
        assertEquals(ItemDecorationFactory.GRID_ITEM_COUNT_10,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_RECENT))

        every { ViewHelper.px2dip(activity, any()) } returns 1100
        assertEquals(ItemDecorationFactory.GRID_ITEM_COUNT_12,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_RECENT))
        every { ViewHelper.px2dip(activity, any()) } returns 1300
        assertEquals(ItemDecorationFactory.GRID_ITEM_COUNT_14,
            ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, ItemDecorationFactory.GRID_ITEM_DECORATION_RECENT))
    }

    @Test
    fun `test get grid item decoration`() {
        every { ItemDecorationFactory.getGridItemCount(activity, any(), any()) } returns 3
        val result = mockk<AlbumSetItemDecoration>()
        every { ItemDecorationFactory.getGridItemDecoration(ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET, 0, activity) } returns result
        assertEquals(result, ItemDecorationFactory.getGridItemDecoration(ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET, 0, activity))
    }

    @Test
    fun getMainCategoryGridCountTest() {
        // 0 ~ 455 > 3
        // 455 ~ 566 > 4
        // others > 6
        assertEquals(GRID_ITEM_COUNT_3, getMainCategoryGridCount(100))
        assertEquals(GRID_ITEM_COUNT_3, getMainCategoryGridCount(400))
        assertEquals(GRID_ITEM_COUNT_4, getMainCategoryGridCount(550))
        assertEquals(GRID_ITEM_COUNT_6, getMainCategoryGridCount(800))
    }

    @Test
    fun `should verify when setRecentGridCount`() {
        ItemDecorationFactory.setRecentGridCount(null)
        assertEquals(ItemDecorationFactory.recentItemCount, 1)
        val point = Point(1, 1)
        mockkObject(KtViewUtils)
        every { KtViewUtils.getWindowSize(activity) }.returns(point)
        every { ViewHelper.px2dip(activity, point.x) }.returns(100)
        every { ItemDecorationFactory.getRecentGridCount(100) }.returns(2)
        ItemDecorationFactory.setRecentGridCount(activity)
        assertEquals(ItemDecorationFactory.recentItemCount, 2)
    }

    @Test
    fun `should verify when getRecentGridCount`() {
        assertEquals(ItemDecorationFactory.getRecentGridCount(WIDTH_IN_DP_455 - 100), GRID_ITEM_COUNT_4)
        assertEquals(ItemDecorationFactory.getRecentGridCount(WIDTH_IN_DP_455), GRID_ITEM_COUNT_6)
        assertEquals(ItemDecorationFactory.getRecentGridCount(WIDTH_IN_DP_455 + 10), GRID_ITEM_COUNT_6)
        assertEquals(ItemDecorationFactory.getRecentGridCount(WIDTH_IN_DP_566), GRID_ITEM_COUNT_8)
        assertEquals(ItemDecorationFactory.getRecentGridCount(WIDTH_IN_DP_566 + 10), GRID_ITEM_COUNT_8)
        assertEquals(ItemDecorationFactory.getRecentGridCount(WIDTH_IN_DP_841), GRID_ITEM_COUNT_10)
        assertEquals(ItemDecorationFactory.getRecentGridCount(WIDTH_IN_DP_841 + 10), GRID_ITEM_COUNT_10)
        assertEquals(ItemDecorationFactory.getRecentGridCount(WIDTH_IN_DP_1080), GRID_ITEM_COUNT_12)
        assertEquals(ItemDecorationFactory.getRecentGridCount(WIDTH_IN_DP_1080 + 10), GRID_ITEM_COUNT_12)
        assertEquals(ItemDecorationFactory.getRecentGridCount(WIDTH_IN_DP_1200), GRID_ITEM_COUNT_14)
        assertEquals(ItemDecorationFactory.getRecentGridCount(WIDTH_IN_DP_1200 + 100), GRID_ITEM_COUNT_14)
    }

    @Test
    fun `should verify when setRecentItemImageWidth`() {
        ItemDecorationFactory.recentImgWidth = -1
        ItemDecorationFactory.setRecentItemImageWidth(null)
        assertEquals(ItemDecorationFactory.recentImgWidth, 0)
        mockkObject(KtViewUtils)
        every { res.getDimensionPixelSize(any()) }.returns(1)
        every { ItemDecorationFactory.getHorizontalMargin(activity) }.returns(4)
        ItemDecorationFactory.recentItemCount = 1
        every { KtViewUtils.getGridItemWidth(activity, 1, 1, 4) }.returns(100)
        ItemDecorationFactory.setRecentItemImageWidth(activity)
        assertEquals(ItemDecorationFactory.recentImgWidth, 0)
    }

    @Test
    fun `should verify when getRecentItemImageWidth`() {
        ItemDecorationFactory.recentImgWidth = 100
        assertEquals(ItemDecorationFactory.getRecentItemImageWidth(null), 100)
        assertEquals(ItemDecorationFactory.getRecentItemImageWidth(activity), 100)
        ItemDecorationFactory.recentImgWidth = 0
        assertEquals(ItemDecorationFactory.getRecentItemImageWidth(null), 0)
        mockkObject(KtViewUtils)
        every { res.getDimensionPixelSize(any()) }.returns(1)
        every { ItemDecorationFactory.getHorizontalMargin(activity) }.returns(4)
        ItemDecorationFactory.recentItemCount = 1
        every { KtViewUtils.getGridItemWidth(activity, 1, 1, 4) }.returns(50)
        assertEquals(ItemDecorationFactory.getRecentItemImageWidth(activity), 0)
    }

    @Test
    fun `should verify when getRecentGridCount2`() {
        ItemDecorationFactory.recentItemCount = 4
        assertEquals(ItemDecorationFactory.getRecentGridCount(null), 4)
        assertEquals(ItemDecorationFactory.getRecentGridCount(activity), 4)
        ItemDecorationFactory.recentItemCount = 0
        assertEquals(ItemDecorationFactory.getRecentGridCount(null), 1)
        ItemDecorationFactory.recentItemCount = 0
        val point = Point(1, 1)
        mockkObject(KtViewUtils)
        every { KtViewUtils.getWindowSize(activity) }.returns(point)
        every { ViewHelper.px2dip(activity, point.x) }.returns(100)
        every { ItemDecorationFactory.getRecentGridCount(100) }.returns(2)
        assertEquals(ItemDecorationFactory.getRecentGridCount(activity), 2)
    }

    @Test
    fun getMainLabelFileGridCountTest() {
        assertEquals(3, ItemDecorationFactory.getMainLabelFileGridCount(0))
        assertEquals(3, ItemDecorationFactory.getMainLabelFileGridCount(320))
        assertEquals(3, ItemDecorationFactory.getMainLabelFileGridCount(354))
        assertEquals(4, ItemDecorationFactory.getMainLabelFileGridCount(355))
        assertEquals(4, ItemDecorationFactory.getMainLabelFileGridCount(430))
        assertEquals(5, ItemDecorationFactory.getMainLabelFileGridCount(431))
        assertEquals(5, ItemDecorationFactory.getMainLabelFileGridCount(528))
        assertEquals(6, ItemDecorationFactory.getMainLabelFileGridCount(529))
        assertEquals(6, ItemDecorationFactory.getMainLabelFileGridCount(579))
        assertEquals(7, ItemDecorationFactory.getMainLabelFileGridCount(580))
        assertEquals(7, ItemDecorationFactory.getMainLabelFileGridCount(651))
        assertEquals(8, ItemDecorationFactory.getMainLabelFileGridCount(652))
        assertEquals(8, ItemDecorationFactory.getMainLabelFileGridCount(711))
        assertEquals(9, ItemDecorationFactory.getMainLabelFileGridCount(712))
        assertEquals(9, ItemDecorationFactory.getMainLabelFileGridCount(839))
        assertEquals(10, ItemDecorationFactory.getMainLabelFileGridCount(840))
        assertEquals(10, ItemDecorationFactory.getMainLabelFileGridCount(913))
        assertEquals(11, ItemDecorationFactory.getMainLabelFileGridCount(914))
        assertEquals(11, ItemDecorationFactory.getMainLabelFileGridCount(979))
        assertEquals(12, ItemDecorationFactory.getMainLabelFileGridCount(980))
        assertEquals(12, ItemDecorationFactory.getMainLabelFileGridCount(1137))
        assertEquals(13, ItemDecorationFactory.getMainLabelFileGridCount(1138))
        assertEquals(13, ItemDecorationFactory.getMainLabelFileGridCount(1200))
    }
}