/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DFMMediaHelperTest
 ** Description : DFMMediaHelperTest
 ** Version     : 1.0
 ** Date        : 2024/05/28 15:05
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/05/28       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.categorydfm.dfm

import android.content.Context
import io.mockk.mockk
import org.junit.Assert
import org.junit.Test

class DFMMediaHelperTest {

    private val context: Context = mockk()

    @Test
    fun `should return null if selectionArg is empty`() {
        //given
        val selectionArg = ArrayList<Pair<String?, String?>>()
        val helper = DFMMediaHelper(context)
        //when
        val result = helper.getArchiveSqlQuery(selectionArg)
        //then
        Assert.assertTrue(result == "")
    }

    @Test
    fun `should return right if selectionArg`() {
        //given
        val selectionArg = ArrayList<Pair<String?, String?>>()
        selectionArg.add(Pair("sdcard/path/test.png", "test.png"))
        selectionArg.add(Pair("sdcard/path/test1.png", "test1.png"))
        val helper = DFMMediaHelper(context)
        val expect = "(relative_path = 'sdcard/path/test.png' AND _display_name = 'test.png') " +
                    "OR (relative_path = 'sdcard/path/test1.png' AND _display_name = 'test1.png')"
        //when
        val result = helper.getArchiveSqlQuery(selectionArg)
        //then

        Assert.assertTrue(result == expect)
    }
}