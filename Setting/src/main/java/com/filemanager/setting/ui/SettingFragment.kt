/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: SettingFragment
 * * Description: the fragment for setting
 * * Version: 1.0
 * * Date : 2020/9/5
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/9/5       1.0         the fragment for setting
 ****************************************************************/
package com.filemanager.setting.ui

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.preference.Preference
import com.coui.appcompat.preference.COUIJumpPreference
import com.coui.appcompat.preference.COUIPreference
import com.coui.appcompat.preference.COUIPreferenceWithAppbarFragment
import com.coui.appcompat.preference.COUISwitchPreference
import com.coui.appcompat.statement.COUIUserStatementDialog
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.CommonConstants.NEED_SHOW_RECENT_CAMERA
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.HiddenFileHelper.isNeedShowHiddenFile
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.AndroidDataHelper.PREF_ANDROID_DATA_ACCESS
import com.filemanager.common.utils.EncryptViewUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatisticsUtils.CLICK_STATE_KEY
import com.filemanager.common.utils.StatisticsUtils.SHOW_HIDDEN_FILE
import com.filemanager.common.utils.StatisticsUtils.SWITCH_OFF
import com.filemanager.common.utils.StatisticsUtils.SWITCH_ON
import com.filemanager.common.utils.StatisticsUtils.onCommon
import com.filemanager.setting.R
import com.filemanager.setting.ui.about.SettingAboutActivity
import com.filemanager.setting.ui.function.SettingFunctionActivity
import com.filemanager.setting.ui.privacy.SettingPrivacyActivity
import com.filemanager.setting.ui.privacy.personal.PersonalListActivity
import com.oplus.filemanager.interfaze.ad.IAdvertApi
import com.oplus.filemanager.interfaze.feedback.IFeedback

class SettingFragment : COUIPreferenceWithAppbarFragment(), Preference.OnPreferenceChangeListener {

    companion object {
        private const val TAG = "SettingFragment"
        const val NEED_SHOW_HIDDEN_FILES = "need_show_hidden_files"
        private const val HELP_AND_FEEDBACK = "setting_pref_help_feedback"
        private const val APP_VERSION = "setting_pref_app_version"
        private const val SETTING_ABOUT = "setting_pref_about"
        private const val SHOW_HIDDEN_FILES = "setting_pref_hidden_files"
        private const val SHOW_RECENT_CAMERA = "setting_pref_recent_camera_screenshot"
        private const val SETTING_FUNCTION = "setting_pref_function"
        private const val SETTING_FILE_OPEN_MODE = "setting_pref_file_open_mode"
        private const val SETTING_PRIVACY = "setting_pref_privacy"
        private const val PREF_PRIVACY_LIST = "setting_pref_data_list"
        private const val PREF_ADVERT_SERVICE = "setting_pref_advert_service_switch"

        private const val DEFAULT_APP = "com.android.permissioncontroller"
        private const val DEFAULT_APP_CLASSNAME = "com.oplusos.permissioncontroller.role.ui.DefaultAppListActivity"
        private const val SHOW_ENCRYPT_BOX = "setting_pref_encrypt_box_switch"
        private const val PREF_ANDROID_DATA = "setting_pref_android_data"
    }

    private var mShowHiddenFileSwitch: COUISwitchPreference? = null
    private var mShowEncryptBoxSwitch: COUISwitchPreference? = null
    private var androidDataSwitch: COUISwitchPreference? = null
    private var mShowRecentCameraSwitch: COUISwitchPreference? = null
    private var advertServiceSwitch: COUISwitchPreference? = null
    private var feedbackAppSearchDialog: AlertDialog? = null
    private val advertApi by lazy {
        Injector.injectFactory<IAdvertApi>()
    }

    private fun getLayoutResource(): Int {
        return R.xml.filemanager_settings
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        (view as ViewGroup).removeView(view.findViewById(com.filemanager.common.R.id.appbar_layout))
        addPreferencesFromResource(getLayoutResource())
        initView()
        return view
    }

    override fun onResume() {
        super.onResume()
        mShowHiddenFileSwitch?.isChecked = isNeedShowHiddenFile()
        mShowRecentCameraSwitch?.isChecked = HiddenFileHelper.mIsHideCameraScreenshotInRecentOpen?.not() ?: true
        mShowEncryptBoxSwitch?.isChecked = EncryptViewUtils.isNeedShowEncryptBox()
        androidDataSwitch?.isChecked = AndroidDataHelper.openAndroidData ?: true
    }

    private fun initView() {
        initShowHiddenFileSwitch()
        initShowRecentCameraScreenShotSwitch()
        initShowEncryptBoxEntrySwitch()
        initExpAndroidDataSwitch()
        initAppVersion()
        initAbout()
        initDataList()
        initFeedBack()
        initSettingFunction()
        initFileOpenMode()
        initPrivacy()
        initAdvertServiceSwitch()
    }

    private fun initDataList() {
        val preference = findPreference<COUIJumpPreference>(PREF_PRIVACY_LIST)
        preference?.isVisible = !FeatureCompat.sIsExpRom
        preference?.setOnPreferenceClickListener {
            activity?.let { activity -> PersonalListActivity.start(activity) }
            true
        }
    }

    private fun initFeedBack() {
        val feedBack = Injector.injectFactory<IFeedback>()

        findPreference<COUIJumpPreference>(HELP_AND_FEEDBACK)?.apply {
            isVisible = feedBack != null
            if (isVisible) {
                setOnPreferenceClickListener {
                    activity?.let { it1 ->
                        if (!PreferencesUtils.getBoolean(key = CommonConstants.FEED_BACK_FUNCTION_SHOW, default = false)) {
                            feedBack?.apply {
                                createFeedBackDialog(this)
                            }
                        } else {
                            feedBack?.launchFeedBack(it1)
                        }
                    }
                    return@setOnPreferenceClickListener true
                }
            }
        }
    }

    private fun createFeedBackDialog(feedBack: IFeedback) {
        val context = activity ?: return
        val usageInstructionsDialog = object : COUIUserStatementDialog(context) {
            @Deprecated("Deprecated in Java")
            override fun onBackPressed() {
                super.onBackPressed()
                dismiss()
            }
        }
        usageInstructionsDialog.apply {
            setIsShowInMaxHeight(false)
            setCanceledOnTouchOutside(false)
            hideDragView()
            titleText = resources.getString(com.filemanager.common.R.string.third_app_search_dialog_title)
            statement = PrivacyPolicyUtils.addUsageInstructionsPrivacyPolicyLinkSpan(
                context, com.filemanager.common.R.string
                    .agree_protection_tips_for_feedback_network
            )
            bottomButtonText = resources.getString(com.filemanager.common.R.string.basic_function_ok)
            exitButtonText = resources.getString(com.filemanager.common.R.string.dialog_cancel)
            onButtonClickListener = object : COUIUserStatementDialog.OnButtonClickListener {
                override fun onExitButtonClick() {
                    usageInstructionsDialog.dismiss()
                }

                override fun onBottomButtonClick() {
                    usageInstructionsDialog.dismiss()
                    PreferencesUtils.put(key = CommonConstants.FEED_BACK_FUNCTION_SHOW, value = true)
                    feedBack.launchFeedBack(context)
                }
            }
            setOnKeyListener { _, keyCode, event ->
                Log.d(TAG, "onKey keyCode:$keyCode event:$event")
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                    onBackPressed()
                    return@setOnKeyListener true
                }
                false
            }
            PredictiveBackUtils.registerOnBackInvokedCallback(this)
        }
        PrivacyPolicyUtils.setDialogStatusBarTransparentAndBlackFont(context, usageInstructionsDialog, false)
        usageInstructionsDialog.show()
    }


    private fun initFileOpenMode() {
        val preference = findPreference<COUIJumpPreference>(SETTING_FILE_OPEN_MODE)
        val showFileOpenMode = !FeatureCompat.sIsExpRom
        Log.d(TAG, "initFileOpenMode -> showFileOpenMode = $showFileOpenMode")
        preference?.isVisible = showFileOpenMode
        preference?.apply {
            setOnPreferenceClickListener {
                startFileOpenModeActivity(activity)
                clickFileOpenModeBurying()
                true
            }
        }
    }

    private fun initAppVersion() {
        val versionPreference = findPreference<COUIPreference>(APP_VERSION)
        versionPreference?.summary = try {
            appContext.let {
                it.packageManager.getPackageInfo(it.packageName, 0).versionName
            }
        } catch (e: Exception) {
            Log.w(TAG, "initAppVersion: ${e.message}")
            ""
        }
    }

    private fun initAbout() {
        findPreference<COUIJumpPreference>(SETTING_ABOUT)?.setOnPreferenceClickListener {
            SettingAboutActivity.start(activity)
            true
        }
    }

    private fun initShowHiddenFileSwitch() {
        mShowHiddenFileSwitch = findPreference(SHOW_HIDDEN_FILES)
        mShowHiddenFileSwitch?.apply {
            onPreferenceChangeListener = this@SettingFragment
        }
    }

    private fun initShowRecentCameraScreenShotSwitch() {
        mShowRecentCameraSwitch = findPreference(SHOW_RECENT_CAMERA)
        mShowRecentCameraSwitch?.apply {
            onPreferenceChangeListener = this@SettingFragment
        }
    }

    private fun initShowEncryptBoxEntrySwitch() {
        //  外销 + (Os13|| oneplus) + 支持--在一级页面显示
        if (FeatureCompat.sIsExpRom && EncryptViewUtils.isSupportEncryption(KtAppUtils.isOnePlus)) {
            mShowEncryptBoxSwitch = findPreference(SHOW_ENCRYPT_BOX)
            mShowEncryptBoxSwitch?.apply {
                isVisible = true
                onPreferenceChangeListener = this@SettingFragment
            }
        }
    }

    private fun initExpAndroidDataSwitch() {
        if (FeatureCompat.sIsExpRom) {
            androidDataSwitch = findPreference(PREF_ANDROID_DATA)
            androidDataSwitch?.apply {
                onPreferenceChangeListener = this@SettingFragment
                isVisible = AndroidDataHelper.canViewAndroidDataFiles && AndroidDataHelper.showAndroidDataSettingSwitch && AndroidDataHelper
                    .checkSupportViewAndroidData()
                isChecked = AndroidDataHelper.openAndroidData ?: true
            }
        }
    }

    private fun initAdvertServiceSwitch() {
        if (!PropertyCompat.sIsGDPR) {
            Log.w(TAG, "initAdvertServiceSwitch is not in eu region")
            return
        }
        val cloudShowSwitch = advertApi?.getAdCloudShowAdSwitch() ?: true
        Log.d(TAG, "initAdvertServiceSwitch cloud switch:$cloudShowSwitch")
        if (!cloudShowSwitch) {
            return
        }
        val showAdSwitch = advertApi?.showAdSwitch(MyApplication.appContext) ?: false
        Log.d(TAG, "initAdvertServiceSwitch show:$showAdSwitch")
        if (showAdSwitch) {
            advertServiceSwitch = findPreference<COUISwitchPreference>(PREF_ADVERT_SERVICE)
            advertServiceSwitch?.apply {
                isVisible = true
                isChecked = advertApi?.getAdSwitchStatus(context) ?: false
                onPreferenceChangeListener = this@SettingFragment
            }
        }
    }

    override fun onPreferenceChange(preference: Preference?, newValue: Any?): Boolean {
        when (preference?.key) {
            SHOW_HIDDEN_FILES -> {
                mShowHiddenFileSwitch?.apply {
                    val checkState = isChecked
                    PreferencesUtils.put(key = NEED_SHOW_HIDDEN_FILES, value = checkState.not())
                    isChecked = checkState.not()
                    HiddenFileHelper.mIsNeedShowHiddenFile = isChecked
                    if (isChecked) {
                        onCommon(appContext, SHOW_HIDDEN_FILE, mapOf(SHOW_HIDDEN_FILE to SWITCH_ON))
                    } else {
                        onCommon(appContext, SHOW_HIDDEN_FILE, mapOf(SHOW_HIDDEN_FILE to SWITCH_OFF))
                    }
                }
            }
            SHOW_RECENT_CAMERA -> {
                mShowRecentCameraSwitch?.apply {
                    val checkState = isChecked
                    PreferencesUtils.put(key = NEED_SHOW_RECENT_CAMERA, value = checkState)
                    isChecked = checkState.not()
                    HiddenFileHelper.mIsHideCameraScreenshotInRecentOpen = isChecked.not()
                    HiddenFileHelper.forceReloadRecentDataOnce = true
                    onCommon(
                        appContext, StatisticsUtils.PAD_SHOW_RECENT_CAMERA_EVENT, mapOf(
                            CLICK_STATE_KEY to if (isChecked) {
                                StatisticsUtils.OPEN_VALUE
                            } else {
                                StatisticsUtils.CLOSE_VALUE
                            }
                        )
                    )
                }
            }
            SHOW_ENCRYPT_BOX -> {
                mShowEncryptBoxSwitch?.apply {
                    val checkState = isChecked
                    PreferencesUtils.put(key = CommonConstants.NEED_SHOW_ENCRYPT_BOX, value = checkState.not())
                    isChecked = checkState.not()
                    EncryptViewUtils.mIsNeedShowEncryptBox = isChecked
                    if (isChecked) {
                        onCommon(
                            appContext,
                            StatisticsUtils.SHOW_ENCRYPT_BOX,
                            mapOf(StatisticsUtils.SHOW_ENCRYPT_BOX to StatisticsUtils.ENCRYPT_SWITCH_ON)
                        )
                    } else {
                        onCommon(
                            appContext,
                            StatisticsUtils.SHOW_ENCRYPT_BOX,
                            mapOf(StatisticsUtils.SHOW_ENCRYPT_BOX to StatisticsUtils.ENCRYPT_SWITCH_OFF)
                        )
                    }
                }
            }

            PREF_ADVERT_SERVICE -> {
                advertServiceSwitch?.apply {
                    val checkState = isChecked
                    isChecked = checkState.not()
                    advertApi?.setAdSwitchStatus(isChecked)
                    OptimizeStatisticsUtil.adSwitch(isChecked)
                }
            }

            PREF_ANDROID_DATA -> {
                androidDataSwitch?.apply {
                    val checkState = isChecked
                    isChecked = checkState.not()
                    PreferencesUtils.put(key = PREF_ANDROID_DATA_ACCESS, value = isChecked)
                    AndroidDataHelper.openAndroidData = isChecked
                }
            }
        }
        return true
    }

    private fun initSettingFunction() {
        findPreference<COUIJumpPreference>(SETTING_FUNCTION)?.apply {
            isVisible = (!FeatureCompat.sIsExpRom)
            if (isVisible) {
                setOnPreferenceClickListener {
                    activity?.let { it1 -> SettingFunctionActivity.start(it1) }
                    return@setOnPreferenceClickListener true
                }
            }
        }
    }

    private fun initPrivacy() {
        val preference = findPreference<COUIJumpPreference>(SETTING_PRIVACY)
        preference?.apply {
            setOnPreferenceClickListener {
                activity?.let { SettingPrivacyActivity.start(it) }
                return@setOnPreferenceClickListener true
            }
        }
    }

    override fun getTitle(): String {
        activity?.let {
            return it.title.toString()
        }
        return ""
    }

    private fun startFileOpenModeActivity(activity: Activity?) {
        Log.d(TAG, "startFileOpenModeActivity")
        runCatching {
            val intent = Intent()
            intent.setClassName(DEFAULT_APP, DEFAULT_APP_CLASSNAME)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            activity?.startActivity(intent)
        }.onFailure {
            Log.e(TAG, "startFileOpenModeActivity error: ${it.message}")
        }
    }

    private fun clickFileOpenModeBurying() {
        StatisticsUtils.onCommonByOpenAnyFile(
            appContext,
            StatisticsUtils.EVENT_CLICK_FILE_OPEN_MODE,
            mapOf(StatisticsUtils.CLICK_FILE_OPEN_MODE to "1")
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        feedbackAppSearchDialog?.apply {
            if (isShowing) {
                dismiss()
            }
        }
    }
}