/*********************************************************************
 * * Copyright (C), 2024, OPlus. All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/7/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser.adapter

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.children
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseFolderAnimAdapter
import com.filemanager.common.base.BaseSelectionViewHolder
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.DragUtils.SELECTED_ITEMVIEW_ALPHA
import com.filemanager.common.interfaces.OnPickerRecyclerItemClickListener
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.isActivityAndInvalid
import com.filemanager.common.view.TextViewSnippet
import com.filemanager.common.viewholder.BaseFileBrowserVH
import com.filemanager.common.viewholder.FileBrowserGridVH
import com.filemanager.common.viewholder.FileBrowserListVH
import com.filemanager.common.viewholder.listener.PickerRecycleViewItemHelper
import com.oplus.dropdrag.SelectionTracker
import com.oplus.filebrowser.R
import com.oplus.filemanager.interfaze.cloudconfig.ICloudConfigApi
import java.util.HashMap
import java.util.Locale

class PickerFileBrowserAdapter(
    content: Context,
    lifecycle: Lifecycle,
    private val isMultipleSelect: Boolean,
    private var mPickerMultipleType: Array<String>? = null
) :
    BaseFolderAnimAdapter<RecyclerView.ViewHolder, BaseFileBean>(content), LifecycleObserver {
    companion object {
        private const val TAG = "PickerFileBrowserAdapter"
    }
    private val mSizeCache = HashMap<String, String>()
    private var mThreadManager = ThreadManager(lifecycle)
    private val mImgRadius = MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.file_list_bg_radius)
    var isCreateOrAuthorize = false
    private var mOnPickerRecyclerItemClickListener: OnPickerRecyclerItemClickListener? = null
    init {
        lifecycle.addObserver(this)
        mChoiceMode = isMultipleSelect
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        super.onRemoveCallBack()
        mSizeCache.clear()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if ((BaseFileBean.TYPE_LABEL_FILE == viewType) || (BaseFileBean.TYPE_FILE_LIST_HEADER == viewType)) {
            LabelViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.filebrowser_lable_item, parent, false))
        } else if (KtConstants.SCAN_MODE_GRID == viewType) {
            FileBrowserGridVH(
                LayoutInflater.from(parent.context).inflate(FileBrowserGridVH.getLayoutId(), parent, false),
                filterDir = FileTypeUtils.pickerFilerDirList, mPickerMultipleType)
        } else {
            FileBrowserListVH(
                LayoutInflater.from(parent.context).inflate(FileBrowserListVH.getLayoutId(), parent, false),
                mImgRadius,
                filterDir = FileTypeUtils.pickerFilerDirList,
                mPickerMultipleType = mPickerMultipleType
            ).apply {
                setUpdateViewList(object : FileBrowserListVH.UpdateViewListener {
                    override fun updateViewByAlias(textViewSnippet: TextViewSnippet, path: String) {
                        val cloudConfigApi = Injector.injectFactory<ICloudConfigApi>()
                        cloudConfigApi?.updateViewByAlias(textViewSnippet, path)
                    }
                })
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        if ((mFiles.size == 0) && (position >= 0) && (position < mOldFiles.size)) {
            return mOldFiles[position].mFileWrapperViewType ?: mScanViewModel
        }
        return if ((position >= 0) && (position < mFiles.size)) {
            mFiles[position].mFileWrapperViewType ?: mScanViewModel
        } else {
            mScanViewModel
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (mContext.isActivityAndInvalid()) {
            Log.d(TAG, "onBindViewHolder: Activity is Destroyed!")
            return
        }
        if ((position < 0) || (position >= mFiles.size)) {
            return
        }
        if (holder.itemView.alpha == 0f) {
            holder.itemView.alpha = 1f
        }
        val file = mFiles[position]
        if (holder is BaseFileBrowserVH) {
            val choiceMode = !file.mIsDirectory && isMultipleSelect && !isCreateOrAuthorize
            holder.loadData(
                mContext, getItemKey(file, position),
                mFiles[position], choiceMode, mSelectionArray, mSizeCache, mThreadManager, this
            )
            if (isCreateOrAuthorize) {
                if (!file.mIsDirectory) {
                    (holder.itemView as? ViewGroup)?.children?.forEach {
                        it.alpha = SELECTED_ITEMVIEW_ALPHA
                    }
                } else {
                    (holder.itemView as? ViewGroup)?.children?.forEach {
                        it.alpha = 1f
                    }
                }
                holder.itemView?.setOnClickListener {
                    mOnPickerRecyclerItemClickListener?.onItemClick(holder.itemView, holder.layoutPosition, 0)
                }
            } else {
                (holder.itemView as? ViewGroup)?.children?.forEach {
                    it.alpha = 1.0f
                }
                mOnPickerRecyclerItemClickListener?.let { listener ->
                    PickerRecycleViewItemHelper.registerItemTouchClickListener(holder, listener)
                }
            }
        } else if (holder is LabelViewHolder) {
            holder.updateKey(SelectionTracker.SKIP_INT_ITEM_ID)
            if (file.mFileWrapperViewType == BaseFileBean.TYPE_FILE_LIST_HEADER) {
                if (file.mFileWrapperLabel != null) {
                    holder.mLabel?.text = mContext.getString(file.mFileWrapperLabel!!, file.mFileWrapperTypeNum)
                } else {
                    holder.mLabel?.text = ""
                }
            } else if (file.mFileWrapperViewType == BaseFileBean.TYPE_LABEL_FILE) {
                if (file.mFileWrapperLabel != null) {
                    holder.mLabel?.text = mContext.getString(file.mFileWrapperLabel!!) + " " + file.mFileWrapperTypeNum
                } else {
                    holder.mLabel?.text = ""
                }
            }
        }
    }

    override fun getItemId(position: Int): Long {
        return getItemKeyByPosition(position)?.toLong() ?: SelectionTracker.NO_LONG_ITEM_ID
    }

    override fun getItemKey(item: BaseFileBean, position: Int): Int {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return position.hashCode()
        }

        return path.toLowerCase(Locale.getDefault()).hashCode()
    }

    private class LabelViewHolder(convertView: View) : BaseSelectionViewHolder(convertView, false) {
        var mLabel: TextView? = null

        init {
            mLabel = convertView.findViewById(R.id.label_view)
        }
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        if (mScanViewModel == KtConstants.SCAN_MODE_LIST) {
            setChoiceModeAnimFlag(flag)
        }
    }

    fun setOnPickerRecyclerItemClickListener(onPickerRecyclerItemClickListener: OnPickerRecyclerItemClickListener) {
        mOnPickerRecyclerItemClickListener = onPickerRecyclerItemClickListener
    }
}