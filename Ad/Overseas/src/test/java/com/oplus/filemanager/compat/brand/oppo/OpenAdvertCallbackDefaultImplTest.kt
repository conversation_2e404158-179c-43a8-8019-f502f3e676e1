package com.oplus.filemanager.compat.brand.oppo

import android.app.Application
import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.AdResourceConstants
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.utils.AdHelper
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * OpenAdvertCallbackDefaultImpl 的单元测试类
 * 用于测试开屏广告回调默认实现类的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class OpenAdvertCallbackDefaultImplTest {

    // 待测试的 OpenAdvertCallbackDefaultImpl 实例
    private lateinit var openAdvertCallback: OpenAdvertCallbackDefaultImpl
    // 模拟的 Context 对象
    private val mockContext = mockk<Context>(relaxed = true)
    // 测试用的广告位ID
    private val testPosId = "test_pos_id"
    // 测试用的媒体请求ID
    private val testMediaReqId = "test_media_req_id"
    // 测试用的广告请求ID
    private val testAdReqId = "test_ad_req_id"
    // 测试用的追踪ID
    private val testTraceId = "test_trace_id"

    /**
     * 测试前的初始化方法
     * 1. 模拟 MyApplication 的静态方法
     * 2. 模拟 StatisticsUtils 的静态方法
     * 3. 模拟 AdHelper 的静态方法
     */
    @Before
    fun setUp() {
        mockkStatic(MyApplication::class)
        every { MyApplication.appContext } returns mockContext
        mockkStatic(StatisticsUtils::class)
        mockkStatic(AdHelper::class)
    }

    /**
     * 测试初始化时传入 REQUEST_ID_SPLASH_HOT 的情况
     * 验证 pageName 是否被正确设置为 PAGE_SPLASH_HOT
     */
    @Test
    fun `test init with REQUEST_ID_SPLASH_HOT should set pageName to PAGE_SPLASH_HOT`() {
        openAdvertCallback = OpenAdvertCallbackDefaultImpl(AdResourceConstants.REQUEST_ID_SPLASH_HOT)
        assert(openAdvertCallback.getPageNameForTest() == Constants.PAGE_SPLASH_HOT)
    }

    /**
     * 测试初始化时传入其他 posId 的情况
     * 验证 pageName 是否被正确设置为默认的 PAGE_SPLASH
     */
    @Test
    fun `test init with other posId should set pageName to PAGE_SPLASH`() {
        openAdvertCallback = OpenAdvertCallbackDefaultImpl("other_pos_id")
        assert(openAdvertCallback.getPageNameForTest() == Constants.PAGE_SPLASH)
    }
}

/**
 * 扩展函数：用于测试时获取 OpenAdvertCallbackDefaultImpl 的私有字段 pageName
 * 通过反射机制访问私有字段
 */
fun OpenAdvertCallbackDefaultImpl.getPageNameForTest() = this::class.java.getDeclaredField("pageName").apply { isAccessible = true }.get(this) as String