/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SignUtil
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/8/3 14:22
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/8/3       1.0      create
 ***********************************************************************/
@file:JvmName("SignUtilKt")
package com.oplus.filemanager.drivebrowser.data.utils

import com.allawn.cryptography.algorithm.HkdfUtil
import com.allawn.cryptography.algorithm.HmacUtil
import com.filemanager.common.utils.Log
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.util.Locale

private const val TAG = "SignUtil"

private const val AND_SYMBOL = "&"
private const val EQUAL_SYMBOL = "="

private const val KEY_LEN = 32

fun ByteArray.toHex(): String = joinToString(separator = "") { eachByte -> "%02x".format(eachByte) }

fun generateKey(duid: String, salt: ByteArray, packageName: String): ByteArray {
    return runCatching {
        HkdfUtil.hkdfWithSha256(duid.toByteArray(), salt, packageName.toByteArray(), KEY_LEN)
    }.onFailure {
        Log.e(TAG, "generateKey -> error = ${it.message}")
    }.getOrDefault(ByteArray(0))
}

fun hmacSignInfoWithGenerateKey(signInfo: SignInfo, generateKey: ByteArray): String {
    return runCatching {
        val signStr = splicingSignInfoByJoinToString(signInfo)
        val byteArray = HmacUtil.hmacSha256(signStr.toByteArray(), generateKey)
        byteArray.toHex().uppercase()
    }.onFailure {
        Log.e(TAG, "hmacSignInfoWithGenerateKey -> error = ${it.message}")
    }.getOrDefault("")
}

internal fun splicingSignInfoByJoinToString(signInfo: SignInfo): String {
    val stringA = if (signInfo.stringA.isNullOrBlank()) {
        null
    } else {
        signInfo.stringA
    }
    val list = listOf(
        signInfo.appKey,
        signInfo.timestamp,
        signInfo.invokeMethodName,
        signInfo.jsonStr,
        stringA,
        signInfo.secret
    )
    return list.joinToString(AND_SYMBOL)
}

fun hexSignInfo(signInfo: SignInfo): String {
    val tempSign = splicingSignInfoByJoinToString(signInfo)
    val md = MessageDigest.getInstance("SHA-256")
    val digest = md.digest(tempSign.toByteArray())
    return digest.fold("") { initial, byte -> initial + "%02x".format(byte) }
}

fun formatUrlMap(
    paramMap: Map<String, String>,
    urlEncode: Boolean,
    keyToLower: Boolean,
    includeEmptyValue: Boolean = false
): String {
    var buff: String
    val tempMap: MutableMap<String, String> = LinkedHashMap()
    paramMap.entries.sortedBy { it.key }.forEach { tempMap[it.key] = it.value }
    val builder = StringBuilder()
    tempMap.entries.forEach { info ->
        if (info.key.trim().isNotEmpty()) {
            val key = info.key
            var value = info.value
            if (!includeEmptyValue && info.value.trim().isEmpty()) {
                return@forEach
            }
            if (urlEncode) {
                value = URLEncoder.encode(value, StandardCharsets.UTF_8.toString())
            }
            if (keyToLower) {
                builder.append(key.lowercase(Locale.getDefault())).append(EQUAL_SYMBOL).append(value)
            } else {
                builder.append(key).append(EQUAL_SYMBOL).append(value)
            }
            builder.append(AND_SYMBOL)
        }
    }
    buff = builder.toString()
    if (buff.isNotEmpty()) {
        buff = buff.substring(0, buff.length - 1)
    }
    return buff
}