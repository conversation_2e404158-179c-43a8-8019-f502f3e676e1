/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IOfficeAidlInterface.aidl
 * Description:
 *     YoZo thumbnail service AIDL
 *
 * Version: 1.0
 * Date: 2024-07-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-07-29   1.0    Create this module
 *********************************************************************************/
package com.yozo.aidl;

import com.yozo.aidl.IOfficeCallbackAidlInterface;

interface IOfficeAidlInterface {

    boolean registerCallback(IOfficeCallbackAidlInterface cb);

    boolean unregisterCallback(IOfficeCallbackAidlInterface cb);

    void exportOfficeThumbnail(String uri);

    void actionEvent(int event, String value1, String value2, String value3);
    void exportExcelFirstPage(String uri, int pageWidth, int pageHeight);
    void exportOfficeThumbnailFirstPage(String uri,int pageWidth);
}
