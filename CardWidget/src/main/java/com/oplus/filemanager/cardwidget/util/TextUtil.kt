/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.cardwidget.util
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/3/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.util

import android.content.Context
import com.oplus.smartenginehelper.dsl.DSLCoder

object TextUtil {

    private const val TEXT_SIZE = "textSize"

    const val TEXT_SIZE_16 = 16F
    const val TEXT_SIZE_14 = 14F
    const val TEXT_SIZE_12 = 12F
    const val TEXT_SIZE_10 = 10F
    const val TEXT_SIZE_9 = 9F

    private const val LABEL_NAME_LENGTH = 10
    private const val THREE_POINT = "..."

    /**
     *  标签卡片，为了不响应显示大小的切换，特为此设置可适配大小suitable font size
     */
    @JvmStatic
    fun setTextSize(coder: DSLCoder, context: Context, resId: String, fontSize: Float) {
        coder.setCustomData(resId, TEXT_SIZE, getTextSize(context, fontSize))
    }

    @JvmStatic
    fun getTextSize(context: Context, fontSize: Float): Float {
        val fontScale = context.resources.configuration.fontScale
        return getNotScaleFontSize(fontSize, fontScale)
    }

    @JvmStatic
    private fun getNotScaleFontSize(textSize: Float, scale: Float): Float {
        return Math.round(textSize / scale).toFloat()
    }

    /**
     * 标签卡片的标签标题，超过长度后，要显示三个点
     * 因设置最大长度后ellipsize属性不生效，故手动添加三个点（英文字符）
     */
    @JvmStatic
    fun getLabelNameForLabelCard(labelName: String): String {
        if (checkChineseLength(labelName)) {
            return getLabelNameByLength(labelName, LABEL_NAME_LENGTH)
        }
        return getLabelNameByLength(labelName, LABEL_NAME_LENGTH * 2)
    }

    /**
     * 判断是否有中文
     * True 为没有中文
     */
    @JvmStatic
    private fun checkChineseLength(str: String): Boolean {
        return str.replace("[^(\\u4e00-\\u9fa5)]".toRegex(), "").isNotEmpty()
    }

    /**
     * 标签名称，超过指定长度，在超过的长度后面加三个点
     */
    @JvmStatic
    private fun getLabelNameByLength(labelName: String, length: Int): String {
        if (labelName.length > length) {
            return "${labelName.substring(0, length)}$THREE_POINT"
        }
        return labelName
    }
}