/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.coloros.filemanager.filerefactor.controller.fileoper.RecycleFileOperController
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/1/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.recyclebin.controller

import android.app.Activity
import android.content.res.Configuration
import android.view.MenuItem
import android.view.MotionEvent
import androidx.annotation.VisibleForTesting
import androidx.activity.ComponentActivity
import androidx.lifecycle.Lifecycle
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.controller.BaseLifeController
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.fileoprate.IFileActionObserver
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.interfaces.fileoprate.IFileOperateAction
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.detail.FileActionDetail
import com.filemanager.fileoperate.detail.FileDetailObserver
import com.filemanager.recyclebin.operation.action.FileActionRestore
import com.filemanager.recyclebin.operation.listener.FileRestoreObserver
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin

/**
 * This file is used to unify package the recycle file operations. The operation result will
 * delivery by the OperateResultListener.
 */
class RecycleFileOperatorController(lifecycle: Lifecycle,
                                    viewModel: SelectionViewModel<out BaseFileBean, out BaseUiModel<out BaseFileBean>>)
    : BaseLifeController, IFileOperate {

    companion object {
        private const val TAG = "RecycleFileOperatorController"
    }

    init {
        lifecycle.addObserver(this)
    }

    private var mViewModel: SelectionViewModel<out BaseFileBean, out BaseUiModel<out BaseFileBean>>? = viewModel
    private var mListener: IFileOperate.OperateResultListener? = null
    @VisibleForTesting
    var mInterceptor: IFileOperate? = null
    private var mOperateAction: IFileOperateAction<IFileActionObserver>? = null
    private var fileDetailObserver: FileDetailObserver? = null
    private var action: BaseFileAction<*>? = null

    @VisibleForTesting
    fun isRecycled(activity: Activity): Boolean {
        return ((mViewModel == null) || activity.isFinishing || activity.isDestroyed).apply {
            if (this) {
                Log.w(TAG, "isRecycled: mViewModel=$mViewModel")
            }
        }
    }

    override fun setInterceptor(interceptor: IFileOperate) {
        mInterceptor = interceptor
    }

    override fun setResultListener(listener: IFileOperate.OperateResultListener) {
        mListener = listener
    }

    override fun onNavigationItemSelected(activity: ComponentActivity, item: MenuItem): Boolean {
        when (item.itemId) {
            com.filemanager.common.R.id.recycle_bin_navigation_details -> onDetail(activity)
            com.filemanager.common.R.id.recycle_bin_navigation_restore -> onRestore(activity)
            com.filemanager.common.R.id.recycle_bin_navigation_delete_forever -> onDelete(activity)
            com.filemanager.common.R.id.recycle_bin_navigation_delete_all -> onDeleteAll(activity)
        }
        return true
    }

    @VisibleForTesting
    fun onDeleteAll(activity: ComponentActivity): Boolean {
        fun getRealFiles(): ArrayList<BaseFileBean> {
            val fileList = ArrayList<BaseFileBean>()
            mViewModel!!.uiState.value?.fileList?.apply {
                for (baseFileBean in this) {
                    if (baseFileBean.fileWrapperLabel == null) {
                        fileList.add(baseFileBean)
                    }
                }
            }
            Log.d(TAG, "getRealFileSize:  ${fileList.size}")
            return fileList
        }
        if (isRecycled(activity) || (mInterceptor?.onDetail(activity) == true)) {
            return false
        }
        val recycleBinAction = Injector.injectFactory<IRecycleBin>()
        mOperateAction = recycleBinAction?.getFileActionDelete(
                activity,
                getRealFiles(),
                true,
                externalCategory = CategoryHelper.CATEGORY_RECYCLE_BIN
            )
        mOperateAction?.let {
            StatisticsUtils.onCommon(activity, StatisticsUtils.RECYCLE_BIN_DELETE_ALL, StatisticsUtils.MODEL_CODE_RECYCLE_BIN)
            it.execute(object : IFileActionObserver {

                override fun onActionDone(result: Boolean, data: Any?) {
                    mListener?.onActionDone(IFileOperate.OP_DELETE_FOREVER, result, data)
                    if (activity is BaseVMActivity) {
                        activity.onRefreshData()
                    }
                }

                override fun onActionCancelled() {
                    mListener?.onActionCancelled(IFileOperate.OP_DELETE_FOREVER)
                }

                override fun onActionReloadData() {
                    mListener?.onActionReloadData(IFileOperate.OP_DELETE_FOREVER)
                }

                override fun onActionReShowDialog() {
                }

                override fun isShowDialog(): Boolean {
                    return false
                }
            })
        }
        if (mOperateAction == null) {
            Log.w(TAG, "onDeleteAll failed: action get null")
        }
        return true
    }

    override fun onDetail(activity: ComponentActivity): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onDetail(activity) == true)) {
            return false
        }
        StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.DETAIL_ACTION)
        fileDetailObserver = object : FileDetailObserver(activity) {

            override fun onActionDone(result: Boolean, data: Any?) {
                mListener?.onActionDone(IFileOperate.OP_DETAIL, result, data)
            }

            override fun onActionCancelled() {
                mListener?.onActionCancelled(IFileOperate.OP_DETAIL)
            }

            override fun onActionReloadData() {
                mListener?.onActionReloadData(IFileOperate.OP_DETAIL)
            }
        }
        fileDetailObserver?.let {
            action = FileActionDetail(activity, mViewModel!!.getSelectItems(), true).execute(it)
        }
        return true
    }

    override fun onDelete(activity: ComponentActivity): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onDelete(activity) == true)) {
            return false
        }
        mViewModel!!.getSelectItems().let { selectList ->
            val recycleBinAction = Injector.injectFactory<IRecycleBin>()
            mOperateAction =
                recycleBinAction?.getFileActionDelete(
                    activity,
                    selectList,
                    selectList.size == mViewModel!!.getRealFileSize(),
                    externalCategory = CategoryHelper.CATEGORY_RECYCLE_BIN
                )
            mOperateAction?.let {
                StatisticsUtils.onCommon(activity, StatisticsUtils.RECYCLE_BIN_DELETE, StatisticsUtils.MODEL_CODE_RECYCLE_BIN)
                it.execute(object : IFileActionObserver {

                    override fun onActionDone(result: Boolean, data: Any?) {
                        mListener?.onActionDone(IFileOperate.OP_DELETE_FOREVER, result, data)
                        if (activity is BaseVMActivity) {
                            activity.onRefreshData()
                        }
                    }

                    override fun onActionCancelled() {
                        mListener?.onActionCancelled(IFileOperate.OP_DELETE_FOREVER)
                    }

                    override fun onActionReloadData() {
                        mListener?.onActionReloadData(IFileOperate.OP_DELETE_FOREVER)
                    }

                    override fun onActionReShowDialog() {
                    }

                    override fun isShowDialog(): Boolean {
                        return false
                    }
                })
            }
            if (mOperateAction == null) {
                Log.w(TAG, "onDelete failed: action get null")
            }
        }
        return true
    }

    override fun onRestore(activity: ComponentActivity): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onRestore(activity) == true)) {
            return false
        }
        StatisticsUtils.onCommon(activity,
                StatisticsUtils.RECYCLE_BIN_RESTORE, StatisticsUtils.MODEL_CODE_RECYCLE_BIN)
        FileActionRestore(activity, mViewModel!!.getSelectItems(), mViewModel!!.getRealFileSize())
                .executeRecycleBinRestore(object : FileRestoreObserver(activity) {

                    override fun onActionDone(result: Boolean, data: Any?) {
                        mListener?.onActionDone(IFileOperate.OP_RESTORE, result, data)
                        if (activity is BaseVMActivity) {
                            activity.onRefreshData()
                        }
                    }

                    override fun onActionCancelled() {
                        mListener?.onActionCancelled(IFileOperate.OP_RESTORE)
                    }

                    override fun onActionReloadData() {
                        mListener?.onActionReloadData(IFileOperate.OP_RESTORE)
                    }
                })
        return true
    }

    override fun onFileClick(
        activity: ComponentActivity,
        file: BaseFileBean,
        event: MotionEvent?,
        mediaIds: ArrayList<String>?
    ): Boolean {
        if (isRecycled(activity) || (mInterceptor?.onFileClick(activity, file, event) == true)) {
            return false
        }
        CustomToast.showShort(com.filemanager.common.R.string.toast_file_has_deleted_view_after_restore)
        return true
    }

    override fun onDestroy() {
        mListener = null
        mViewModel = null
        fileDetailObserver?.release()
    }

    override fun onConfigurationChanged(newConfig: Configuration?) {
        mOperateAction?.hideDialog()
        fileDetailObserver?.onConfigurationChanged(newConfig)
    }

    fun hideDialog() {
        mOperateAction?.hideDialog()
    }

    override fun isShowDialog(): Boolean {
        if (mInterceptor != null) {
            return mInterceptor!!.isShowDialog()
        }
        return mOperateAction?.isShowDialog() ?: false || action?.isShowDialog() ?: false
    }
}