/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DFMConflictDialogFactory
 ** Description : DFM Conflict Dialog Factory
 ** Version     : 1.0
 ** Date        : 2024/03/09 10:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/03/09       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.categorydfm.utils

import android.app.Activity
import android.content.DialogInterface
import android.content.DialogInterface.OnClickListener
import androidx.annotation.StringRes
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.fileoperate.R

object DFMConflictDialogFactory {

    /**
     * 手机投屏中断的弹窗
     */
    @JvmStatic
    fun showMobileScreencastInterruptDialog(activity: Activity, listener: DialogInterface.OnClickListener): AlertDialog {
        return showFuncInterruptDialog(activity, com.filemanager.common.R.string.mobile_screencast, listener)
    }

    /**
     * 无线投屏中断的弹窗
     */
    @JvmStatic
    fun showWirelessScreencastInterruptDialog(activity: Activity, listener: DialogInterface.OnClickListener): AlertDialog {
        return showFuncInterruptDialog(activity, com.filemanager.common.R.string.wireless_screencast, listener)
    }

    /**
     * 个人热点中断的弹窗
     */
    @JvmStatic
    fun showPersonalHotspotInterruptDialog(activity: Activity, listener: DialogInterface.OnClickListener): AlertDialog {
        return showFuncInterruptDialog(activity, com.filemanager.common.R.string.personal_hotspot, listener)
    }

    /**
     * 跨屏镜像中断的弹窗
     */
    @JvmStatic
    fun showCrossScreenMirrorInterruptDialog(activity: Activity, listener: DialogInterface.OnClickListener): AlertDialog {
        return showFuncInterruptDialog(activity, com.filemanager.common.R.string.cross_screen_mirror, listener)
    }

    /**
     * 传输通道被占用的弹窗
     */
    @JvmStatic
    fun showTransferChannelOccupiedDialog(activity: Activity, listener: DialogInterface.OnClickListener): AlertDialog {
        return showChannelOccupiedDialog(
            activity,
            com.filemanager.common.R.string.transfer_channel_occupied,
            com.filemanager.common.R.string.transfer_channel_occupied_solve_tips,
            listener
        )
    }

    /**
     * 投屏通道被占用的弹窗
     */
    @JvmStatic
    fun showScreencastChannelOccupiedDialog(activity: Activity, listener: OnClickListener): AlertDialog {
        return showChannelOccupiedDialog(
            activity,
            com.filemanager.common.R.string.screencast_channel_occupied,
            com.filemanager.common.R.string.screencast_channel_occupied_solve_tips,
            listener
        )
    }

    /**
     * 通道被占用的弹窗
     */
    @JvmStatic
    @VisibleForTesting
    fun showChannelOccupiedDialog(activity: Activity, @StringRes title: Int, @StringRes msg: Int, listener: OnClickListener): AlertDialog {
        val builder = COUIAlertDialogBuilder(activity)
            .setCancelable(false)
            .setOnCancelListener { dialog -> listener.onClick(dialog, DialogInterface.BUTTON_NEUTRAL) }
            .setTitle(activity.resources.getString(title))
            .setMessage(activity.resources.getString(msg))
            .setPositiveButton(com.filemanager.common.R.string.positive_ok, listener)

        return builder.show()
    }

    /**
     * 显示功能中断弹窗
     * @param funcNameRes 中断功能的名称
     */
    @JvmStatic
    @VisibleForTesting
    fun showFuncInterruptDialog(activity: Activity, @StringRes funcNameRes: Int, listener: DialogInterface.OnClickListener): AlertDialog {
        val funcName = activity.resources.getString(funcNameRes)
        val builder = COUIAlertDialogBuilder(activity)
            .setCancelable(false)
            .setOnCancelListener { dialog -> listener.onClick(dialog, DialogInterface.BUTTON_NEUTRAL) }
            .setTitle(activity.resources.getString(com.filemanager.common.R.string.interrupt_dialog_title, funcName))
            .setMessage(activity.resources.getString(com.filemanager.common.R.string.interrupt_dialog_message, funcName))
            .setNegativeButton(com.filemanager.common.R.string.alert_dialog_cancel, listener)
            .setPositiveButton(com.filemanager.common.R.string.cut_dialog_confirm_message_continue, listener)

        return builder.show()
    }
}