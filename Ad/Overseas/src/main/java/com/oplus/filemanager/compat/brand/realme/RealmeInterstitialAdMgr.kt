/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RealmeInterstitialAdMgr
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/8/8 17:08
 ** Author      : ********
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********        2024/8/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat.brand.realme

import android.app.Activity
import com.filemanager.common.MyApplication
import com.filemanager.common.crashhandler.SdkCrashCatchUtils
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.Log
import com.oplus.filemanager.utils.AdConstants
import com.oplus.filemanager.utils.AdHelper

class RealmeInterstitialAdMgr() {
    companion object {
        private const val TAG = "RealmeInterstitialAdMgr"
        const val ONE_DAY = 1000 * 60 * 60 * 24
        const val INDEX_RECENT = 1
        const val INDEX_LABEL = 2
    }

    private var interstitialAdManagerMap = HashMap<Int, InterstitialAdManager?>()

    init {
        Log.d(AdvertEntry.AD_TAG, "init MaxInterstitialAdManager ")
        initMaxManagerByIndex(INDEX_RECENT, AdConstants.MAX_RECENT_POS_ID)
        initMaxManagerByIndex(INDEX_LABEL, AdConstants.MAX_LABEL_POS_ID)
    }

    private fun initMaxManagerByIndex(index: Int, posId: String) {
        interstitialAdManagerMap[index] = MaxInterstitialAdManager().apply {
            initInterstitialAd(posId)
        }
    }

    fun requestInterstitialAd(index: Int, isIgoreReqesting: Boolean) {
        if (!SdkCrashCatchUtils.isCanCallADApi()) {
            Log.i(AdvertEntry.AD_TAG, "requestInterstitialAd, ad sdk crashed 3 times,disable request ad")
            return
        }
        if (AdHelper.isInterstitialAdRequesting && !isIgoreReqesting) {
            Log.e(AdvertEntry.AD_TAG, "InterstitialAd requesting, ignore")
            return
        }

        if (!AdHelper.recentInterstitialAdSwitch && index == INDEX_RECENT) {
            // 云控关闭了广告
            Log.e(AdvertEntry.AD_TAG, "mdp recent interstitial ad switch is closed")
            return
        }

        if (!AdHelper.labelInterstitialAdSwitch && index == INDEX_LABEL) {
            Log.e(AdvertEntry.AD_TAG, "mdp label interstitial ad switch is closed")
            return
        }

        val lastRequestTime = ConfigSharedPreferenceUtils.getLong(AdConstants.KEY_INTERSTITIALAD_TIME + index, 0)
        val timeInterval = System.currentTimeMillis() - lastRequestTime
        val adShowTimes = ConfigSharedPreferenceUtils.getLong(AdConstants.KEY_INTERSTITIALAD_SHOW_TIMES + index, 0)

        if (timeInterval in 1 until ONE_DAY && adShowTimes >= AdHelper.interstitialAdLimitedTimes) {
            Log.d(AdvertEntry.AD_TAG, "Time interval is less than one day, ignore")
            return
        }

        if (timeInterval > ONE_DAY) {
            ConfigSharedPreferenceUtils.putLong(AdConstants.KEY_INTERSTITIALAD_SHOW_TIMES + index, 0)
        }

        interstitialAdManagerMap[index]?.requestInterstitialAd()
    }

    fun showAd(activity: Activity, index: Int) {
        if (!AdvertEntry.isAdEnabled(MyApplication.sAppContext)) {
            Log.d(TAG, "ad enable false")
            return
        }
        interstitialAdManagerMap[index]?.showInterstitialAd(activity, index)
    }

    fun destroy() {
        interstitialAdManagerMap[INDEX_RECENT]?.destroy()
        interstitialAdManagerMap[INDEX_LABEL]?.destroy()
        interstitialAdManagerMap.clear()
    }
}