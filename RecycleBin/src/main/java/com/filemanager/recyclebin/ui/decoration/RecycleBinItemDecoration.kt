/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File: FeatureCompat.kt
 * * Description:Query and save the value of common features and query whether the feature exists
 * * Version:1.0
 * * Date :2020/8/17
 * * Author:W9060445
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * *  W9060445     ,  2024/8/23,        v1.0,           Create
 ****************************************************************/
package com.filemanager.recyclebin.ui.decoration

import android.content.Context
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.recyclebin.ui.adapter.RecycleBinAdapter

class RecycleBinItemDecoration(private val adapter: RecycleBinAdapter?, context: Context?) :
    COUIRecyclerView.COUIRecyclerViewItemDecoration(context) {

    override fun shouldDrawDivider(parent: RecyclerView?, index: Int): Boolean {
        val childCount = adapter?.itemCount ?: 0
        val lastIndex = childCount - 1
        if (index >= 0 && index == lastIndex) {
            return false
        }
        return super.shouldDrawDivider(parent, index)
    }
}