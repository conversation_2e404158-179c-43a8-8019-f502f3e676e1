/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:/RecycleBinActivity.kt
 * * Description:
 * * Version:
 * * Date :2020/6/16
 * * Author:********
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *
 ****************************************************************/
package com.filemanager.recyclebin.ui

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.controller.navigation.NavigationType
import com.filemanager.common.dragselection.DragDropInterface
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.utils.*
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.recyclebin.R

class RecycleBinActivity : BaseVMActivity(), NavigationInterface,
        NavigationBarView.OnItemSelectedListener, BaseVMActivity.PermissonCallBack, DragDropInterface {

    private var titleId: Int = -1
    private var mTitle: String? = null
    private var mRootLayout: ViewGroup? = null
    private var mFragment: Fragment? = null
    private val mSelectNavController by lazy { NavigationController(lifecycle, NavigationType.RECYCLE_EDIT, R.id.navigation_tool) }
    private val mNormalNavController by lazy { NavigationController(lifecycle, NavigationType.RECYCLE_NORMAL, R.id.navigation_def) }
    private var mActionActivityResultListener: ActionActivityResultListener? = null

    companion object {
        private const val TAG = "RecycleBinActivity"
    }

    override fun getLayoutResId(): Int {
        return R.layout.activity_recycle_bin
    }

    override fun initView() {
        registerVmChangedReceiver(null)
        handleIntentData()

        mRootLayout = findViewById(R.id.root_layout)
        initFragment()
    }

    private fun handleIntentData() {
        Log.d(TAG, "handleIntentData from router")
        titleId = IntentUtils.getInt(intent, Constants.TITLE_RES_ID, -1)
        mTitle = if (titleId > 0) getString(titleId) else ""
        if (mTitle.isNullOrEmpty()) {
            mTitle = IntentUtils.getString(intent, Constants.TITLE)
        }
    }

    private fun initFragment() {
        var fragment = supportFragmentManager.findFragmentByTag(TAG)
        if (fragment == null) {
            fragment = RecycleBinNewFragment()
        }
        if (fragment is RecycleBinNewFragment) {
            fragment.setTitle(mTitle ?: "")
        }
        val bundle = Bundle()
        bundle.putString(Constants.TITLE, mTitle)
        bundle.putInt(Constants.TITLE_RES_ID, titleId)
        fragment.arguments = bundle
        val ft = supportFragmentManager.beginTransaction()
        ft.replace(R.id.main_frame, fragment, TAG)
        ft.show(fragment)
        ft.commitAllowingStateLoss()
        mFragment = fragment
    }

    override fun initData() {
        StatisticsUtils.onCommon(MyApplication.sAppContext, StatisticsUtils.RECYCLE_BIN_OPEN, StatisticsUtils.MODEL_CODE_RECYCLE_BIN)
    }

    override fun startObserve() {

    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        Log.d(TAG, "refreshCurrentPage")
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        getCurrentFragment()?.onCreateOptionsMenu(menu, menuInflater)
        return true
    }

    override fun onPermissionSuccess(isInstallPermission: Boolean?) {
        super.onPermissionSuccess(isInstallPermission)
        mRootLayout?.post {
			getCurrentFragment()?.onResumeLoadData()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return getCurrentFragment()?.onMenuItemSelected(item) ?: super.onOptionsItemSelected(item)
    }

    override fun onNavigationItemSelected(menuItem: MenuItem): Boolean {
        return getCurrentFragment()?.onNavigationItemSelected(menuItem) ?: false
    }

    /**
     * @throws  UnSupportedApiVersionException not impl, please use method [showNavigation(uiMode)]
     */
    override fun showNavigation() {
        Log.w(TAG, "showNavigation not impl, please use showNavigation(uiMode: Int)")
        showNavigation(KtConstants.LIST_SELECTED_MODE)
        updateNavigationToolPadding()
    }

    fun showNavigation(uiMode: Int) {
        if (uiMode == KtConstants.LIST_NORMAL_MODE) {
            if (mSelectNavController.isShowNavigation()) {
                mSelectNavController.hideNavigation(this)
            }
            if (mNormalNavController.isShowNavigation().not()) {
                mNormalNavController.showNavigation(this)
            }
            mNormalNavController.setNavigateItemAble(isEnable = true, mHasDrm = false)
        } else {
            if (mNormalNavController.isShowNavigation()) {
                mNormalNavController.hideNavigation(this)
            }
            if (mSelectNavController.isShowNavigation().not()) {
                mSelectNavController.showNavigation(this)
            }
        }
        updateNavigationToolPadding()
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean, hasAndroidData: Boolean) {
        Log.d(TAG, "setNavigateItemEnable isEnable = $isEnable mHasDrm = $mHasDrm")
        mSelectNavController.setNavigateItemAble(isEnable, mHasDrm)
    }

    override fun hideNavigation() {
        if (mSelectNavController.isShowNavigation()) {
            mSelectNavController.hideNavigation(this, true)
        } else {
            mSelectNavController.hideNavigationDirectly(this)
        }
        if (mNormalNavController.isShowNavigation()) {
            mNormalNavController.hideNavigation(this, true)
        } else {
            mNormalNavController.hideNavigationDirectly(this)
        }
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
        mActionActivityResultListener = actionActivityResultListener

    }

    override fun onBackPressed() {
        if ((getCurrentFragment() as? OnBackPressed)?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
        }
    }

    override fun backtoTop() {
        super.backtoTop()
        getCurrentFragment()?.let {
            it.getRecyclerView()?.fastSmoothScrollToTop()
        }
    }

    override fun onSaveInstanceState(savedInstanceState: Bundle) {
        super.onSaveInstanceState(savedInstanceState)
        savedInstanceState.putBoolean(Constants.NEED_RELOAD, true)
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
        unregisterVmChangedReceiver()
    }

    private fun getCurrentFragment(): RecycleBinNewFragment? {
        return (if (mFragment is RecycleBinNewFragment) {
            mFragment
        } else {
            null
        }) as RecycleBinNewFragment?
    }

    override fun handleNoStoragePermission() {
        (mFragment as? RecycleBinNewFragment)?.setPermissionEmptyVisible(View.VISIBLE)
    }

    override fun updateNavigationToolPadding() {
        mSelectNavController.updateNavigationToolPadding(navPaddingBottom)
        mNormalNavController.updateNavigationToolPadding(navPaddingBottom)
    }
}
