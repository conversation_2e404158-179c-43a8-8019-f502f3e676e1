/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LabelCardDataPackerTest.kt
 ** Description : LabelCardDataPacker Unit Test
 ** Version     : 1.0
 ** Date        : 2023/11/22
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue       2023/11/22      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.packer

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.view.View
import android.webkit.MimeTypeMap
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.ImageFileWrapper
import com.oplus.filemanager.cardwidget.ext.IC_7Z
import com.oplus.filemanager.cardwidget.ext.IC_AUDIO
import com.oplus.filemanager.cardwidget.ext.IC_DOC
import com.oplus.filemanager.cardwidget.ext.IC_FOLDER
import com.oplus.filemanager.cardwidget.ext.IC_JAR
import com.oplus.filemanager.cardwidget.ext.IC_PDF
import com.oplus.filemanager.cardwidget.ext.IC_PIC
import com.oplus.filemanager.cardwidget.ext.IC_PPT
import com.oplus.filemanager.cardwidget.ext.IC_RAR
import com.oplus.filemanager.cardwidget.ext.IC_VIDEO
import com.oplus.filemanager.cardwidget.ext.IC_XLS
import com.oplus.filemanager.cardwidget.ext.IC_ZIP
import com.oplus.filemanager.cardwidget.label.data.LabelCardData
import com.oplus.filemanager.cardwidget.label.packer.LabelCardDataPacker
import com.oplus.filemanager.cardwidget.util.TextUtil
import com.oplus.smartenginehelper.dsl.DSLCoder
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import java.io.File

class LabelCardDataPackerTest {

    private lateinit var context: Context
    private lateinit var resources: Resources
    private lateinit var coder: DSLCoder
    private lateinit var labelCard: LabelCardData
    private lateinit var packer: LabelCardDataPacker

    @Before
    fun setup() {
        context = mockk()
        resources = mockk()
        mockkObject(MyApplication)
        mockkStatic(ModelUtils::class)
        every { MyApplication.sAppContext }.returns(context)
        every { MyApplication.appContext }.returns(context)
        every { context.packageName }.returns("FileManager")
        every { context.filesDir }.returns(File("/data/user/0/com.xxx.filemanager/files/"))
        every { context.resources }.returns(resources)
        every { resources.getDimensionPixelSize(com.oplus.filemanager.cardwidget.R.dimen.card_widget_thumbnail_corner_radius) }.returns(4)
        every { resources.getDimensionPixelSize(com.oplus.filemanager.cardwidget.R.dimen.label_card_icon_size) }.returns(40)
        coder = mockk()
        labelCard = LabelCardData(1, "最近文件", 1, mutableListOf())
        packer = LabelCardDataPacker(context, labelCard)
        every { MyApplication.appContext.getColor(any()) }.returns(Color.RED)
        every { MyApplication.appContext.resources.getDimensionPixelSize(any()) }.returns(0)
        val file = File("/data/user/0/com.coloros.filemanager/")
        every { MyApplication.appContext.filesDir }.returns(file)
        every { ModelUtils.isTablet() } returns false
        every { MyApplication.appContext.packageName }.returns("test")
    }

    @After
    fun teardown() {
        unmockkObject(MyApplication)
        unmockkStatic(ModelUtils::class)
    }

    @Test
    fun should_call_setOnClick_when_onPack() {
        mockkStatic(TextUtil::class)
        mockkStatic(Utils::class)
        justRun { TextUtil.setTextSize(any(), any(), any(), any()) }
        every { Utils.getDateFormat(any(), any()) }.returns("10:20:30")
        justRun { coder.setVisibility(any(), any()) }
        justRun { coder.setTextViewText(any(), any()) }
        justRun { coder.setOnClick(any(), any()) }
        justRun { coder.setTextViewText(any(), any()) }
        justRun { coder.setVisibility(any(), any()) }

        labelCard.fileCount = 2
        packer.onPack(coder)
        verify { coder.setOnClick("label_name", any()) }

        unmockkStatic(TextUtil::class)
        unmockkStatic(Utils::class)
    }

    @Test
    fun should_call_setVisibility_when_onPack_fileCount_great_4() {
        mockkStatic(TextUtil::class)
        mockkStatic(Utils::class)
        justRun { TextUtil.setTextSize(any(), any(), any(), any()) }
        every { Utils.getDateFormat(any(), any()) }.returns("10:20:30")
        justRun { coder.setVisibility(any(), any()) }
        justRun { coder.setTextViewText(any(), any()) }
        justRun { coder.setOnClick(any(), any()) }
        justRun { coder.setTextViewText(any(), any()) }
        justRun { coder.setVisibility(any(), any()) }

        labelCard.fileCount = 2
        packer.onPack(coder)
        verify { coder.setVisibility("label_files_more", View.GONE) }
        verify { coder.setVisibility("label_files_more_icon", View.GONE) }

        labelCard.fileCount = 4
        packer.onPack(coder)
        verify { coder.setVisibility("label_files_more", View.GONE) }
        verify { coder.setVisibility("label_files_more_icon", View.GONE) }

        labelCard.fileCount = 5
        packer.onPack(coder)
        verify { coder.setVisibility("label_files_more", View.VISIBLE) }
        verify { coder.setVisibility("label_files_more_icon", View.VISIBLE) }

        unmockkStatic(TextUtil::class)
        unmockkStatic(Utils::class)
    }

    private fun createFileBean(name: String): BaseFileBean {
        val fileBean = BaseFileBean()
        fileBean.mData = "/sdcard/document/$name"
        fileBean.mDisplayName = name
        fileBean.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
        fileBean.mIsDirectory = true
        fileBean.mSize = 1024
        fileBean.mDateModified = 1024000
        return fileBean
    }

    @Test
    fun should_call_showIcon_when_onPack_fileList() {
        mockkStatic(TextUtil::class)
        mockkStatic(Utils::class)
        justRun { TextUtil.setTextSize(any(), any(), any(), any()) }
        every { Utils.getDateFormat(any(), any()) }.returns("10:20:30")
        justRun { coder.setVisibility(any(), any()) }
        justRun { coder.setTextViewText(any(), any()) }
        justRun { coder.setOnClick(any(), any()) }
        justRun { coder.setTextViewText(any(), any()) }
        justRun { coder.setVisibility(any(), any()) }
        justRun { coder.setTextViewLineSpacingExtra(any(), any()) }
        justRun { coder.setLayoutWidth(any(), any()) }
        justRun { coder.setLayoutHeight(any(), any()) }
        justRun { coder.setImageViewResource(any(), any<String>()) }
        justRun { coder.setImageScaleType(any(), any()) }

        val list = mutableListOf<BaseFileBean>()
        list.add(createFileBean("1"))
        labelCard.fileCount = 1
        labelCard.fileList = list

        packer.onPack(coder)
        verify(atMost = 1) { coder.setImageViewResource(any(), IC_FOLDER) }

        labelCard.fileCount = 2
        list.add(createFileBean("2"))
        packer.onPack(coder)
        verify(atMost = 3) { coder.setImageViewResource(any(), IC_FOLDER) }

        labelCard.fileCount = 3
        list.add(createFileBean("3"))
        packer.onPack(coder)
        verify(atMost = 6) { coder.setImageViewResource(any(), IC_FOLDER) }

        labelCard.fileCount = 4
        list.add(createFileBean("4"))
        packer.onPack(coder)
        verify(atMost = 10) { coder.setImageViewResource(any(), IC_FOLDER) }

        labelCard.fileCount = 5
        list.add(createFileBean("5"))
        packer.onPack(coder)
        verify(atMost = 14) { coder.setImageViewResource(any(), IC_FOLDER) }

        unmockkStatic(TextUtil::class)
        unmockkStatic(Utils::class)
    }

    @Test
    fun should_call_setTextViewText_when_showFileName() {
        mockkStatic(TextUtil::class)
        justRun { coder.setTextViewText(any(), any()) }
        justRun { coder.setTextViewLineSpacingExtra(any(), any()) }
        justRun { coder.setOnClick(any(), any()) }
        justRun { TextUtil.setTextSize(any(), any(), any(), any()) }

        val fileBean = ImageFileWrapper()
        fileBean.mData = "/sdcard/images/1.png"
        fileBean.mDisplayName = "1"
        packer.showFileName(coder, "item_file_name_1", fileBean)
        verify { coder.setTextViewText("item_file_name_1", "1") }
        verify { coder.setTextViewLineSpacingExtra("item_file_name_1", -4.0f) }
        unmockkStatic(TextUtil::class)
    }

    @Test
    fun should_call_setTextViewText_when_showDuration() {
        mockkStatic(TextUtil::class)
        mockkStatic(Utils::class)

        justRun { TextUtil.setTextSize(any(), any(), any(), any()) }
        every { Utils.getDateFormat(any(), any()) }.returns("10:20:30")
        justRun { coder.setVisibility(any(), any()) }
        justRun { coder.setTextViewText(any(), any()) }
        justRun { coder.setOnClick(any(), any()) }
        val fileBean = ImageFileWrapper()
        fileBean.mDateModified = 1024000

        packer.showDuration(coder, "item_file_duration_1", fileBean)
        verify { coder.setTextViewText("item_file_duration_1", "10:20:30") }
        verify { coder.setVisibility("item_file_duration_1", View.VISIBLE) }

        unmockkStatic(Utils::class)
        unmockkStatic(TextUtil::class)
    }

    @Test
    fun should_call_setImageResource_Folder_when_showIcon_directoryType() {
        justRun { coder.setOnClick(any(), any()) }
        justRun { coder.setLayoutWidth(any(), any()) }
        justRun { coder.setLayoutHeight(any(), any()) }
        justRun { coder.setImageScaleType(any(), any()) }
        justRun { coder.setImageViewResource(any(), any<String>()) }

        val fileBean = ImageFileWrapper()
        fileBean.mData = "/sdcard/images/1"
        fileBean.mDisplayName = "1"
        fileBean.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
        fileBean.mIsDirectory = true
        fileBean.mSize = 1024
        fileBean.mDateModified = 1024000

        packer.showIcon(coder, "item_file_icon_1", fileBean)
        verify { coder.setImageViewResource(any(), IC_FOLDER) }
    }

    @Test
    fun should_call_setImageResource_Folder_when_showIcon_compressedType() {
        justRun { coder.setOnClick(any(), any()) }
        justRun { coder.setLayoutWidth(any(), any()) }
        justRun { coder.setLayoutHeight(any(), any()) }
        justRun { coder.setImageScaleType(any(), any()) }
        justRun { coder.setImageViewResource(any(), any<String>()) }

        val fileBean = ImageFileWrapper()
        fileBean.mData = "/sdcard/document/1.zip"
        fileBean.mDisplayName = "1"
        fileBean.mLocalType = MimeTypeHelper.COMPRESSED_TYPE
        fileBean.mSize = 1024
        fileBean.mDateModified = 1024000

        packer.showIcon(coder, "item_file_icon_1", fileBean)
        verify { coder.setImageViewResource(any(), IC_ZIP) }

        fileBean.mData = "/sdcard/document/1.7z"
        packer.showIcon(coder, "item_file_icon_1", fileBean)
        verify { coder.setImageViewResource(any(), IC_7Z) }

        fileBean.mData = "/sdcard/document/1.rar"
        packer.showIcon(coder, "item_file_icon_1", fileBean)
        verify { coder.setImageViewResource(any(), IC_RAR) }

        fileBean.mData = "/sdcard/document/1.jar"
        packer.showIcon(coder, "item_file_icon_1", fileBean)
        verify { coder.setImageViewResource(any(), IC_JAR) }
    }

    @Test
    fun should_call_setImageResource_Folder_when_showIcon_wordType() {
        justRun { coder.setOnClick(any(), any()) }
        justRun { coder.setLayoutWidth(any(), any()) }
        justRun { coder.setLayoutHeight(any(), any()) }
        justRun { coder.setImageScaleType(any(), any()) }
        justRun { coder.setImageViewResource(any(), any<String>()) }

        val fileBean = ImageFileWrapper()
        fileBean.mData = "/sdcard/document/1.doc"
        fileBean.mDisplayName = "1"
        fileBean.mLocalType = MimeTypeHelper.DOC_TYPE
        fileBean.mSize = 1024
        fileBean.mDateModified = 1024000

        packer.showIcon(coder, "item_file_icon_1", fileBean)
        verify { coder.setImageViewResource(any(), IC_DOC) }

        fileBean.mData = "/sdcard/document/1.pdf"
        fileBean.mLocalType = MimeTypeHelper.PDF_TYPE
        packer.showIcon(coder, "item_file_icon_1", fileBean)
        verify { coder.setImageViewResource(any(), IC_PDF) }

        fileBean.mData = "/sdcard/document/1.ppt"
        fileBean.mLocalType = MimeTypeHelper.PPT_TYPE
        packer.showIcon(coder, "item_file_icon_1", fileBean)
        verify { coder.setImageViewResource(any(), IC_PPT) }

        fileBean.mData = "/sdcard/document/1.xls"
        fileBean.mLocalType = MimeTypeHelper.XLS_TYPE
        packer.showIcon(coder, "item_file_icon_1", fileBean)
        verify { coder.setImageViewResource(any(), IC_XLS) }
    }

    @Test
    fun should_call_setImageResource_Folder_when_showIcon_mediaType() {
        mockkStatic(MimeTypeMap::class)
        val mimeTypeMap = mockk<MimeTypeMap>()
        every { MimeTypeMap.getSingleton() }.returns(mimeTypeMap)
        every { mimeTypeMap.getMimeTypeFromExtension(any()) }.answers {
            val extension = firstArg<String>()
            val type = when (extension) {
                "png" -> "image/png"
                "jpg" -> "image/jpeg"
                "mp3" -> "audio/mp3"
                "mp4" -> "video/mp4"
                else -> ""
            }
            type
        }
        justRun { coder.setOnClick(any(), any()) }
        justRun { coder.setLayoutWidth(any(), any()) }
        justRun { coder.setLayoutHeight(any(), any()) }
        justRun { coder.setImageScaleType(any(), any()) }
        justRun { coder.setImageViewResource(any(), any<String>()) }
        justRun { coder.setPaddingTop(any(), any()) }
        justRun { coder.setPaddingStart(any(), any()) }
        justRun { coder.setPaddingEnd(any(), any()) }
        justRun { coder.setPaddingBottom(any(), any()) }

        val fileBean = ImageFileWrapper()
        fileBean.mData = "/sdcard/images/1.png"
        fileBean.mDisplayName = "1"
        fileBean.mLocalType = MimeTypeHelper.IMAGE_TYPE
        fileBean.mSize = 1024
        fileBean.mDateModified = 1024000

        packer.showIcon(coder, "item_file_icon_1", fileBean)
        verify { coder.setImageViewResource(any(), IC_PIC) }

        fileBean.mData = "/sdcard/images/1.mp4"
        fileBean.mLocalType = MimeTypeHelper.VIDEO_TYPE
        packer.showIcon(coder, "item_file_icon_1", fileBean)
        verify { coder.setImageViewResource(any(), IC_VIDEO) }

        fileBean.mData = "/sdcard/images/1.mp3"
        fileBean.mLocalType = MimeTypeHelper.AUDIO_TYPE
        packer.showIcon(coder, "item_file_icon_1", fileBean)
        verify { coder.setImageViewResource(any(), IC_AUDIO) }
        unmockkStatic(MimeTypeMap::class)
    }

    @Test
    fun should_call_setOnClick_when_setClickEventForLabelItem() {
        justRun { coder.setOnClick(any(), any()) }
        val fileBean = ImageFileWrapper()
        fileBean.mData = "/sdcard/images/1.png"
        fileBean.mDisplayName = "1.png"
        fileBean.mLocalType = MimeTypeHelper.IMAGE_TYPE
        fileBean.mSize = 1024
        fileBean.mDateModified = 1024000
        packer.setClickEventForLabelItem(coder, "item_file_name_1", fileBean)
        verify { coder.setOnClick("item_file_name_1", any()) }
    }

    @Test
    fun should_call_setImageScaleType_when_setImageSizeAndScaleType() {
        justRun { coder.setLayoutWidth(any(), any()) }
        justRun { coder.setLayoutHeight(any(), any()) }
        justRun { coder.setImageScaleType(any(), any()) }

        packer.setImageSizeAndScaleType(coder, "label_name_icon", "fitXY")
        verify { coder.setImageScaleType("label_name_icon", "fitXY") }
    }


    @Test
    fun should_call_setVisibility_when_displayVisibility() {
        justRun { coder.setVisibility(any(), any()) }

        labelCard.fileCount = 0
        packer.displayVisibility(coder)
        verify(inverse = true) { coder.setVisibility("item_file_name_1", View.VISIBLE) }
        verify(inverse = true) { coder.setVisibility("item_file_name_2", View.INVISIBLE) }
        verify(inverse = true) { coder.setVisibility("item_file_name_3", View.INVISIBLE) }
        verify(inverse = true) { coder.setVisibility("item_file_name_4", View.INVISIBLE) }

        labelCard.fileCount = 1
        packer.displayVisibility(coder)
        verify(atMost = 1) { coder.setVisibility("item_file_name_1", View.VISIBLE) }
        verify(atLeast = 1) { coder.setVisibility("item_file_name_2", View.INVISIBLE) }
        verify(atLeast = 1) { coder.setVisibility("item_file_name_3", View.INVISIBLE) }
        verify(atLeast = 1) { coder.setVisibility("item_file_name_4", View.INVISIBLE) }

        labelCard.fileCount = 2
        packer.displayVisibility(coder)
        verify(atLeast = 2) { coder.setVisibility("item_file_name_1", View.VISIBLE) }
        verify(atLeast = 1) { coder.setVisibility("item_file_name_2", View.VISIBLE) }
        verify(atLeast = 2) { coder.setVisibility("item_file_name_3", View.INVISIBLE) }
        verify(atLeast = 2) { coder.setVisibility("item_file_name_4", View.INVISIBLE) }

        labelCard.fileCount = 3
        packer.displayVisibility(coder)
        verify(atLeast = 3) { coder.setVisibility("item_file_name_1", View.VISIBLE) }
        verify(atLeast = 2) { coder.setVisibility("item_file_name_2", View.VISIBLE) }
        verify(atMost = 1) { coder.setVisibility("item_file_name_3", View.VISIBLE) }
        verify(atLeast = 3) { coder.setVisibility("item_file_name_4", View.INVISIBLE) }

        labelCard.fileCount = 4
        packer.displayVisibility(coder)
        verify(atLeast = 4) { coder.setVisibility("item_file_name_1", View.VISIBLE) }
        verify(atLeast = 3) { coder.setVisibility("item_file_name_2", View.VISIBLE) }
        verify(atLeast = 2) { coder.setVisibility("item_file_name_3", View.VISIBLE) }
        verify(atMost = 1) { coder.setVisibility("item_file_name_4", View.VISIBLE) }
    }
}