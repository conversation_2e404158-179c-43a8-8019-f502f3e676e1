package com.filemanager.compresspreview.ui

import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.path.FilePathHelper
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.decompress.BaseDecompressFile
import com.filemanager.fileoperate.decompress.P7ZipDecompressHelper
import com.filemanager.fileoperate.previewcompress.CompressPreviewCacheHelper
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.verify
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * CompressPreviewViewModel的单元测试类
 * 用于测试CompressPreviewViewModel的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class CompressPreviewViewModelTest {

    // 被测ViewModel实例
    private lateinit var viewModel: CompressPreviewViewModel
    // 模拟的Activity对象
    private lateinit var mockActivity: BaseVMActivity
    // 模拟的路径帮助类对象
    private lateinit var mockPathHelper: FilePathHelper
    // 模拟的预览文件映射表
    private lateinit var mockPreviewMap: Map<String, MutableList<out BaseDecompressFile>?>

    /**
     * 测试前的初始化方法
     * 在每个测试用例执行前调用，用于准备测试环境
     */
    @Before
    fun setUp() {
        // 创建模拟的Activity对象
        mockActivity = mockk(relaxed = true)
        // 创建模拟的路径帮助类对象
        mockPathHelper = mockk(relaxed = true)
        // 创建被测ViewModel实例并注入模拟的路径帮助类
        viewModel = CompressPreviewViewModel().apply {
            mPathHelp = mockPathHelper
        }
        // 初始化模拟的预览文件映射表
        mockPreviewMap = mapOf(
            "" to mutableListOf(createTestFile("file1", false), createTestFile("folder1", true)),
            "folder1/" to mutableListOf(createTestFile("file2", false))
        )
        // 模拟CompressPreviewCacheHelper单例对象
        mockkObject(CompressPreviewCacheHelper)
        // 设置模拟对象的行为：当调用fetchTransferPreviewFile时返回mockPreviewMap
        every { CompressPreviewCacheHelper.fetchTransferPreviewFile() } returns mockPreviewMap
    }

    /**
     * 创建测试用的BaseDecompressFile对象
     * @param name 文件名
     * @param isDirectory 是否是目录
     * @return 创建的BaseDecompressFile对象
     */
    private fun createTestFile(name: String, isDirectory: Boolean): BaseDecompressFile {
        return BaseDecompressFile().apply {
            mDisplayName = name
            mIsDirectory = isDirectory
            mData = name
        }
    }

    /**
     * 测试previewData方法在previewMap为null且需要检查完成时应该结束Activity
     */
    @Test
    fun `previewData should finish activity when previewMap is null and need check finish`() {
        // 设置模拟对象的行为：当调用fetchTransferPreviewFile时返回null
        every { CompressPreviewCacheHelper.fetchTransferPreviewFile() } returns null
        // 调用被测方法
        viewModel.previewData(mockActivity, "testPath", true)
        // 验证mockActivity的finish方法被调用
        verify { mockActivity.finish() }
    }

    /**
     * 测试previewData方法在previewMap不为null时不应该结束Activity
     */
    @Test
    fun `previewData should not finish activity when previewMap is not null`() {
        // 调用被测方法
        viewModel.previewData(mockActivity, "testPath", true)
        // 验证mockActivity的finish方法没有被调用
        verify(exactly = 0) { mockActivity.finish() }
    }

    /**
     * 测试listCompressFile方法在previewMap为null时应该返回false
     */
    @Test
    fun `listCompressFile should return false when previewMap is null`() {
        // 设置ViewModel的previewMap为null
        viewModel.mPreviewMap = null
        // 调用被测方法
        viewModel.previewData(mockActivity, "testPath", false)
        // 验证uiState的值为null
        assertNull(viewModel.uiState.value)
    }
}