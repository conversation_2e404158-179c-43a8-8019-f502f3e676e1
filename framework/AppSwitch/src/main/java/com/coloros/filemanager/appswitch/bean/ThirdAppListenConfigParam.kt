/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File: ThirdAppListenConfigParam.kt
 ** Description: for 6.0 GUI.
 ** Version: V 1.0
 ** Date : 2024-3-27
 ** Author: Yuanwang.Huang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 **
 **     * 针对云控配置的解析java类，云控配置示意如下：
 **
 **     {
 **           "isOn": 0,//功能是否开启
 **           "version": 202201,//配置版本号
 **           "detect_fuffix":[".docx",".pdf"],
 **           "rules": [{
 **                       "package": "com.tencent.mm",//检测的应用包名
 **                       "range": {//适用版本范围
 **                               "minVersion": "适用最小版本号",//最小版本号
 **                               "maxVersion": "适用最大版本号",//最大版本号
 **                               "deviceType": 1//设备类型，0x1平板 0x10手机，0x11平板手机通用
 **                               },
 **                       "previewActivitys": [{
 **                               "preview": "com.tencent.mm.ui.tools.MiniQBReaderUI",
 **                               //拉起预览页面，此时需检测checkActivity中的activity页面信息
 **                               "checkActivity": [{
 **                                   "ac": "com.tencent.mm.ui.LauncherUI",//被点击的也没按
 **                                   "parse_reg": ["send{source}send","{filename}\n {time}"]//数据计策规则
 **                                   "forceUpdate":0,//是否强制重新检测，
 **                                   "sourceType":0//来源类型，单聊、群聊、云文档
 **                                   }, {
 **                                   "ac": "com.tencent.mm.plugin.fav.ui.FavoriteIndexUI",
 **                                   "parse_reg": "{filename}\n{size}\n{source}"
 **                                   }]
 **                               }]
 **                       }]
 **       }
 **
 **  这下面这个类是针对该云控配置项的java对象，并包含从json字符串解析为java对象的业务逻辑
 **
 ****************************************************************/


package com.coloros.filemanager.appswitch.bean

import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.CheckActivityItem.Companion.FORCE_UPDATE_VALUE_TRUE
import com.coloros.filemanager.appswitch.util.ConvertUtil
import com.filemanager.common.utils.Log
import com.google.gson.GsonBuilder
import com.google.gson.annotations.SerializedName
import java.io.Reader
import java.io.Serializable

class ThirdAppListenConfigParam : Serializable {

    companion object {

        private const val serialVersionUID: Long = -6913074979402730400L

        const val TAG = "ThirdAppListenConfigParam"

        //isOnd的取值,整个功能关闭
        const val FEATURE_OFF = 0

        //isOnd的取值,整个功能打开
        const val FEATURE_ON = 1

        fun parseFromJsonString(jsonString: String?): ThirdAppListenConfigParam {
            val gb = GsonBuilder().setPrettyPrinting()
                .serializeNulls().create()
            val result = gb.fromJson(jsonString, ThirdAppListenConfigParam::class.java)
            return result
        }

        fun parseFromReader(streamReader: Reader?): ThirdAppListenConfigParam {
            val gb = GsonBuilder().setPrettyPrinting()
                .serializeNulls().create()
            val result = gb.fromJson(streamReader, ThirdAppListenConfigParam::class.java)
            return result
        }
    }

    //三方文档界面监听开关
    var isOn: Int = FEATURE_OFF

    //该文件云控配置版本号
    var version: Int = 0

    //检测的文件名称后缀
    @SerializedName("detect_fuffix")
    var detectSuffix: MutableList<String> = mutableListOf()

    //检测规则
    var rules: MutableList<DetectRule> = mutableListOf()

    //每天可以更新dmp的次数
    var maxDayExactCnt: Int? = null

    fun getAllPackages(): MutableList<String> {
        val result = mutableListOf<String>()
        rules.forEach {
            result.add(it.mPackage)
        }
        return result
    }


    fun getAllPreviewActivitys(): MutableList<String> {
        val result = mutableListOf<String>()
        rules.forEach { detectRule ->
            detectRule.preViewActivitys.forEach {
                result.add(it.preview)
            }
        }
        return result
    }

    fun isFeatureOn(): Boolean {
        return isOn == FEATURE_ON
    }

    fun getRulesForPackage(packageName: String): List<DetectRule> {
        return rules.filter { packageName.contentEquals(it.mPackage, true) }
    }

    fun getAllCheckActivitys(): MutableList<String> {
        val result = mutableListOf<String>()
        rules.forEach { detectRule ->
            detectRule.preViewActivitys.forEach { previewActivityItem ->
                previewActivityItem.checkActivityList.forEach {
                    result.add(it.activity)
                }
            }
        }
        return result
    }


    fun toJsonString(): String {
        val gb = GsonBuilder().setPrettyPrinting()
            .serializeNulls().create()
        val result = gb.toJson(this)
        Log.i(TAG, "toJsonString result $result")
        return result
    }

    fun getAllCheckActivitiesForPreViewActivity(previewActivity: String): MutableList<String> {
        val result = mutableListOf<String>()
        rules.forEach { detectRule ->
            detectRule.preViewActivitys.forEach { previewActivityItem ->
                val previewActivityMatch =
                    previewActivity.contentEquals(previewActivityItem.preview, true)
                if (previewActivityMatch) {
                    previewActivityItem.checkActivityList.forEach {
                        result.add(it.activity)
                    }
                }
            }
        }
        Log.i(TAG, "getAllCheckActivitiesForPreViewActivity previewActivity $previewActivity, result $result")
        return result
    }



    fun getParseRegListForCheckActivity(checkActivity: String, previewActivity: String = "", packageVersion: Int): List<ParseRegItem> {
        val result = mutableListOf<ParseRegItem>()
        val checkActivityItem = getCheckActivityItem(checkActivity, previewActivity, packageVersion)
        if (checkActivityItem != null) {
            result.addAll(checkActivityItem.parseReg)
        }
        return result
    }


    fun getAllForceUpdateCheckActivitys(): List<CheckActivityItem> {
        val result: MutableList<CheckActivityItem> = mutableListOf()
        Log.i(TAG, "getAllForceUpdateCheckActivitys start")
        outerLoop1@ for (detectRule in rules) {
            innerLoop1@ for (previewActivityItem in detectRule.preViewActivitys) {
                val tmpItemList = previewActivityItem.checkActivityList.filter { item ->
                    item.forceUpdate == FORCE_UPDATE_VALUE_TRUE
                }
                Log.i(TAG, "getCheckActivityItem ing, tmpItemList ${tmpItemList.map { it.activity }}")
                if (tmpItemList.isNotEmpty()) {
                    result.addAll(tmpItemList)
                }
            }
        }
        Log.i(TAG, "getAllForceUpdateCheckActivitys  end result ${result.map { it.activity }}")
        return result
    }

    fun getCheckActivityItem(checkActivity: String, previewActivity: String = "", packageVersion: Int = -1): CheckActivityItem? {
        var result: CheckActivityItem? = null
        Log.i(TAG, "getCheckActivityItem activity $checkActivity, previeActivity $previewActivity start")
        outerLoop1@ for (detectRule in rules) {
            val preViewActivitys = detectRule.preViewActivitys
            val filterPreviewActivities = preViewActivitys.filter { preveiwActivityItem ->
                if (previewActivity.isEmpty()) {
                    true
                } else {
                    preveiwActivityItem.preview.contentEquals(previewActivity, true)
                }
            }
            innerLoop1@ for (previewActivityItem in filterPreviewActivities) {
                result = previewActivityItem.checkActivityList.find {
                    val targetActivityMeet = checkActivity.contentEquals(it.activity, ignoreCase = true)
                    //判定是否需要检测apk版本号是否符合当前要求
                    val versionCodeMeet = if (packageVersion == -1) {
                        true
                    } else {
                        ConvertUtil.versionCodeMeet(packageVersion.toLong(), detectRule.mRange)
                    }
                    val allMeet = targetActivityMeet && versionCodeMeet
                    allMeet
                }
                Log.i(TAG, "getCheckActivityItem ing $checkActivity, result $result")
                if (result != null) {
                    break@outerLoop1
                }
            }
        }
        Log.i(TAG, "getCheckActivityItem activity $checkActivity, previeActivity $previewActivity end result $result")
        return result
    }


    fun getSourceTypeForCheckActivity(activity: String): Int? {
        Log.i(TAG, "getSourceTypeForCheckActivity activity $activity")
        rules.forEach { detectRule ->
            detectRule.preViewActivitys.forEach outerloop@{ previewActivityItem ->
                previewActivityItem.checkActivityList.forEach innerloop@{ activityItem ->
                    if (activity.contentEquals(activityItem.activity, ignoreCase = true)) {
                        Log.i(TAG, "getSourceTypeForCheckActivity sourceType ${activityItem.sourceType}")
                        return activityItem.sourceType
                    }
                }
            }
        }
        return  -1
    }

    /**
     * rules
     * mPackage：代表针对该条规则中针对该app的包名
     * mRange：代表针对该条规则中app的版本范围以及机型范围
     * preViewActivitys：代表针对该条规则中针对的app中具体页面的解析规则
     */
    data class DetectRule(
        @SerializedName("package") var mPackage: String = "",
        @SerializedName("range") var mRange: Range = Range(),
        @SerializedName("previewActivitys") var preViewActivitys: MutableList<PreveiwActivityItem> = mutableListOf()
    ) {

        companion object {
            const val TAG = "DetectRule"
        }


        fun getAllPreviewActivitys(): MutableList<String> {
            val result = mutableListOf<String>()
            preViewActivitys.forEach {
                result.add(it.preview)
            }
            return result
        }


        fun getAllCheckActivitys(): MutableList<String> {
            val result = mutableListOf<String>()
            preViewActivitys.forEach { previewActivityItem ->
                previewActivityItem.checkActivityList.forEach {
                    result.add(it.activity)
                }
            }
            return result
        }
    }


    /**
     * rules中的适用范围
     * minVersion：代表针对该条规则中针对该app的最小版本号
     * maxVersion：代表针对该条规则中针对该app的最大版本号
     * devicetype：代表针对该条规则中
     */
    data class Range(
        @SerializedName("minVersion") var minVersion: Int? = null,
        @SerializedName("maxVersion") var maxVersion: Int? = null,
        @SerializedName("deviceType") var deviceType: Int = DEVICE_TYPE_PHONE
    ) {

        companion object {

            const val TAG = "Range"

            //deviceType中值的定义，设备类型，平板
            const val DEVICE_TYPE_TABLET = 0b01

            //deviceType中值的定义，设备类型，手机
            const val DEVICE_TYPE_PHONE = 0b10

            //deviceType中值的定义，设备类型，手机平板通用
            const val DEVICE_TYPE_COMMON = 0b11
        }
    }


    /**
     * rules中的 preViewActivity列表中的每个activity的item
     * preview -> 具体的activity的组件名称
     * checkActivityList -> 针对该activity的每种action的解析规则，主要关注规则中的正则表达式
     */

    data class PreveiwActivityItem(
        @SerializedName("preview") var preview: String = "",
        @SerializedName("checkActivity") var checkActivityList: MutableList<CheckActivityItem> = mutableListOf()
    ) {

        companion object {
            const val TAG = "PreveiwActivityItem"
        }
    }


    /**
     * rules中的 preViewActivity列表中的一个activity对应的一条针对特定action的解析规则
     * activity -> 启动这个previewActivity之前的activity
     * parseReg -> 解析该activiyt中intent中页面数据的正则表达式
     * forceUpate -> 该条规则是否需要强制更新
     * sourceType -> 这条解析数据的信息来源，单聊，群聊，云端文件？
     */
    data class CheckActivityItem(
        @SerializedName("ac") var activity: String = "",
        @SerializedName("parse_reg") var parseReg: MutableList<ParseRegItem> = mutableListOf(),
        @SerializedName("forceUpdate") var forceUpdate: Int = FORCE_UPDATE_VALUE_FALSE,
        @SerializedName("sourceType") var sourceType: Int?,
        @SerializedName("defaultSourceType") var defaultSourceType: Int?,
        @SerializedName("sourceName") var sourceName: String?,
        @SerializedName("noParseFromIntent") var noParseFromIntent: String?,
        @SerializedName("ignoreSpecialCharForViewExact") var viewExactIgnoreSpecialChar: String?,
        @SerializedName("ignoreVisibleToUserForViewExact") var viewExactIgnoreVisibleToUser: Boolean?
    ) {


        companion object {

            const val TAG = "CheckActivityItem"

            //forceUpdate值的定义，代表规则不需要强制更新
            const val FORCE_UPDATE_VALUE_FALSE = 0

            //forceUpdate值的定义，代表规则需要强制更新
            const val FORCE_UPDATE_VALUE_TRUE = 1

            //sourceType值的定义，表明文档来源为单聊
            const val SOURCE_TYPE_SINGLE_CHAT = 0

            //sourceType值的定义，文档来源为群聊
            const val SOURCE_TYPE_GROUP_CHAT = 1

            //sourceType值的定义，文档来源为云文档
            const val SOURCE_TYPE_CLOUD_FILE = 2

            //sourceType值的定义，来源于聊天记录详情页面
            const val SOURCE_TYPE_CHAT_RECORD_MSG = 3

            //sourceType值的定义，来源于聊天记录搜索页面
            const val SOURCE_TYPE_CHAT_RECORD_SEARCH = 4

            //sourceType值的定义，文档来源为云文档中的个人空间
            const val SOURCE_TYPE_CLOUD_PERSONAL = 5

            //sourceType值的定义，文档来源为云文档中的共享空间
            const val SOURCE_TYPE_CLOUD_SHARED = 6

            //sourceType值的定义，文档来源为收藏
            const val SOURCE_TYPE_CLOUD_FAVORATE = 7

            //sourceType值的定义，文档来源为离线
            const val SOURCE_TYPE_CLOUD_OFFLINE = 8
        }
    }

    data class ParseRegItem(
        @SerializedName("target_type") var targetType: String = "",
        @SerializedName("top_level_reg") var originalTopLevelReg: String = "",
        //这个不参与序列化和反序列化，本地逻辑使用, 用于存储文件名称特殊处理（替换了）之后的正则表达式字符串
        @Transient var processedTopLevelReg: String = "",
        @SerializedName("top_level_reg_index_type") var topLevelRegIndexType: Int = 0,
        @SerializedName("top_level_reg_index_value") var topLevelRegIndex: Int = 0,
        //只有微信在聊天页面时，对size的处理中配置这个值，0代表默认处理，1代表匹配多个则抛弃
        @SerializedName("top_level_reg_match_mode") var regMatchMode: Int? = null,
        //当targetType = sourceType的时候可能配置，代表当前正则表达式匹配成功之后对应的sourceType的值
        @SerializedName("reg_match_source_type") var regMatchSourceType: Int? = null,
        //当targetType = sourceType的时候可能配置, 代表当前正则表达式没有匹配成功对应的sourceType的值
        @SerializedName("reg_nomatch_source_type") var regNomatchSourceType: Int? = null,
        @SerializedName("second_level_regs") var secondLevelRegs: MutableList<SecondLevelRegItem>? = null
    ) {
        companion object {

            const val TAG = "ParseRegItem"
            const val TARGET_TYPE_VALUE_SOURCENAME = "sourcename"
            const val TARGET_TYPE_VALUE_FILENAME = "filename"
            const val TARGET_TYPE_VALUE_SIZE = "size"
            const val TARGET_TYPE_VALUE_FILETIME = "time"
            const val TARGET_TYPE_VALUE_SOURCETYPE = "sourcetype"
            const val TARGET_TYPE_VALUE_DEFAULT_EMPTY_FILENAME = "emptyfilename"
            const val TARGET_TYPE_VALUE_BASETIME = "basetime"
            const val TARGET_TYPE_VALUE_DELTATIME = "deltatime"

            const val INDEX_TYPE_GROUP_INDEX = 0
            const val INDEX_TYPE_CHARATER_POSITION_INDEX = 1

            const val REG_MATCH_MODE_DEFAULT = 0
            const val REG_MATCH_MODE_MULTIRESULT_ABANDON = 1
        }

        fun isMatchModeMultiAbandon(): Boolean {
            return regMatchMode == REG_MATCH_MODE_MULTIRESULT_ABANDON
        }



        fun isTopLevelIndexTypeGroup(): Boolean {
            return topLevelRegIndexType == INDEX_TYPE_GROUP_INDEX
        }

        fun isTopLevelIndexTypePosition(): Boolean {
            return topLevelRegIndexType == INDEX_TYPE_CHARATER_POSITION_INDEX
        }


        fun isFileNameRegex(): Boolean {
            return targetType.contentEquals(TARGET_TYPE_VALUE_FILENAME, true)
        }

        fun isFileSizeRegex(): Boolean {
            return targetType.contentEquals(TARGET_TYPE_VALUE_SIZE, true)
        }

        fun isSourceNameRegex(): Boolean {
            return targetType.contentEquals(TARGET_TYPE_VALUE_SOURCENAME, true)
        }

        fun isFileTimeRegex(): Boolean {
            return targetType.contentEquals(TARGET_TYPE_VALUE_FILETIME, true)
        }

        fun isSourceTypeRegex(): Boolean {
            return targetType.contentEquals(TARGET_TYPE_VALUE_SOURCETYPE, true)
        }

        fun hasSecondLevelRegs(): Boolean {
            return !secondLevelRegs.isNullOrEmpty()
        }
    }


    data class SecondLevelRegItem(
        @SerializedName("extract_reg") var regex: String = "",
        @SerializedName("extract_reg_index_type") var indexType: Int? = null,
        @SerializedName("extract_reg_index_value") var index: Int? = null,
        @SerializedName("int_index") var unitIntIndex: Int? = null,
        @SerializedName("decimal_index") var unitDecimalIndex: Int? = null,
        @SerializedName("unit") var unit: Int? = null, //大小单位时才会有
        @SerializedName("day_adjust") var dayAdjust: Int? = null, //解析时间时才会有 (今天，昨天，前天)
        @SerializedName("day_adjust_index") var dayAdjustIndex: Int? = null, //解析时间时才会有 (3天前)
        @SerializedName("year_index") var yearIndex: Int? = null, //解析时间
        @SerializedName("month_index") var monthIndex: Int? = null,
        @SerializedName("day_index") var dayIndex: Int? = null,
        @SerializedName("hour_index") var hourIndex: Int? = null,
        @SerializedName("minute_index") var minuteIndex: Int? = null,
        @SerializedName("second_index") var secondIndex: Int? = null,
    ) {

        fun isSizeUnit(): Boolean {
            return (unitIntIndex != null || unitDecimalIndex != null) && unit != null
        }

        fun isTime(): Boolean {
            return dayAdjust != null || yearIndex != null || monthIndex != null || dayIndex != null || hourIndex != null || minuteIndex != null
        }
    }
}