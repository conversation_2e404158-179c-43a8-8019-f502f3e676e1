/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CreateDirOperate
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/7 17:52
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/7       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.create

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.InvalidSourceFileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.OK
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.OperateFailure
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.internal.rename.RenameFileOperate
import com.oplus.fileservice.operate.internal.utils.converterFileBean
import java.io.File

class CreateDirOperate(
    private val context: Context
) : RenameFileOperate(context) {

    private var mOperateFile: BaseFileBean? = null

    override fun executeAction(sourceFileInfo: FileInfo, targetFileInfo: FileInfo): OperationResult {
        Log.d(TAG, "executeAction -> source = $sourceFileInfo ; target = $targetFileInfo")
        if (sourceFileInfo.data.isNullOrEmpty()) {
            return OperationResult(false, InvalidSourceFileInfo)
        }
        return sourceFileInfo.data?.let {  sourceFileData ->
            val sourceFile = constructorFile(sourceFileData)
            if (sourceFile.isDirectory) {
                sourceFileInfo.data = sourceFileInfo.data.plus(File.separator + targetFileInfo.displayName)
            } else {
                return OperationResult(false, InvalidSourceFileInfo)
            }
            mOperateFile = sourceFileInfo.converterFileBean()
            mOperateFile?.let {
                return if (JavaFileHelper.mkdir(it)) {
                    OperationResult(true, OK)
                } else {
                    OperationResult(false, OperateFailure)
                }
            } ?: return OperationResult(false, InvalidSourceFileInfo)
        } ?: OperationResult(false, InvalidSourceFileInfo)
    }

    @VisibleForTesting
    fun constructorFile(sourceFileData: String) = File(sourceFileData)

    override fun recycle() {
        // Do Nothing
    }

    override fun afterRun(result: OperationResult) {
        if (result.success) {
            mOperateFile?.mData?.let {
                FileMediaHelper.insertFileMediaDB(context, it, Utils.MEDIA_SCAN_CREATE_FOLDER)
            }
        }
    }

    companion object {
        private const val TAG = "CreateDirOperate"
    }
}