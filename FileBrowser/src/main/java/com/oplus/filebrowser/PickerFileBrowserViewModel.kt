/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved.
 * * File: PickerFileBrowserViewModel
 * * Description: pick shortcut file viewmodel
 * * Version: 1.0
 * * Date : 2025/7/17
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * w9072055    2025/7/17       1.0         pick shortcut file viewmodel
 ****************************************************************/

package com.oplus.filebrowser

import androidx.lifecycle.MutableLiveData
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.Log

class PickerFileBrowserViewModel : FileBrowserViewModel() {
    companion object {
        private const val TAG = "PickerFileBrowserViewModel"
        private const val PICKER_FILE_BROWSER_SCAN_MODE_SP_KEY = "picker_file_browser_scan_mode"
    }

    override val mBrowseModeState by lazy {
        val lastScanMode = ConfigSharedPreferenceUtils.getInt(PICKER_FILE_BROWSER_SCAN_MODE_SP_KEY, 0)
        MutableLiveData<Int>(
            if (lastScanMode == 0) {
                KtConstants.SCAN_MODE_LIST
            } else {
                lastScanMode
            }
        )
    }
    override fun checkShortcutFolderRootPath(currentPath: String?): String? {
        Log.d(TAG, "mShortcutFolderRootPath=$mShortcutFolderRootPath currentPath=$currentPath isFromShortcutFolder=$isFromShortcutFolder")
        return currentPath
    }
}