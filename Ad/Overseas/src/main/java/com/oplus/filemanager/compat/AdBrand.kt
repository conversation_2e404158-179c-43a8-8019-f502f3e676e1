/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AdRegion
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/3/16 19:34
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/3/16       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat

import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.Utils

enum class AdBrand {
    OPPO, REALME, NULL
}

fun initAdBrand(): AdBrand {
    return if (ModelUtils.isTablet()) {
        AdBrand.NULL
    } else if (Utils.isRealmePhone()) {
        AdBrand.REALME
    } else {
        AdBrand.OPPO
    }
}