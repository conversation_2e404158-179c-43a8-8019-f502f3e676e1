/*********************************************************************
 * * Copyright (C), 2010-2023 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AddFileControllerTest
 * * Description : AddFileController unit test
 * * Version     : 1.0
 * * Date        : 2023/5/12
 * * Author      : W9001702
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    W9001702                  2023/5/12     1.0     AddFileController unit test
 *    W9008821                  2023/6/30     1.1     modify AddFileController test
 *  ***********************************************************************/
package com.oplus.filemanager.filelabel.controller

import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import org.junit.Before
import org.junit.Test

class AddFileControllerTest {
    private lateinit var mAddFileController: AddFileController
    private lateinit var mAddFileDialogFragment: AddFileDialogFragment
    private lateinit var mAddFileClickListener: AddFileClickListener

    @Before
    fun setup() {
        mAddFileController = mockk()
        mAddFileDialogFragment = mockk()
        mAddFileClickListener = mockk()
        every { mAddFileController.addFileDialogFragment }.returns(mAddFileDialogFragment)
    }

    @Test
    fun should_when_showAddFileDialog() {
        every { mAddFileController.showAddFileDialog(any(), any(), any()) }.answers { callOriginal() }
        justRun { mAddFileDialogFragment.showAddFilPanelFragment(any(), any(), any()) }
        val fragmentManager: FragmentManager = mockk()
        val lifecycle: Lifecycle = mockk()
        mAddFileDialogFragment.showAddFilPanelFragment("", fragmentManager, lifecycle)
        verify { mAddFileDialogFragment.showAddFilPanelFragment(any(), any(), any()) }
    }

    @Test
    fun should_when_setData() {
        every { mAddFileController.setData(any(), any()) }.answers { callOriginal() }
        justRun { mAddFileDialogFragment.setData(any(), any()) }
        mAddFileController.setData(mutableListOf(), mutableListOf())
        verify { mAddFileDialogFragment.setData(any(), any()) }
    }

    @Test
    fun should_when_showNoFileView() {
        every { mAddFileController.showNoFileView() }.answers { callOriginal() }
        justRun { mAddFileDialogFragment.showNoFileView() }
        mAddFileDialogFragment.showNoFileView()
        verify { mAddFileDialogFragment.showNoFileView() }
    }

    @Test
    fun should_when_hideNoFileView() {
        every { mAddFileController.hideNoFileView() }.answers { callOriginal() }
        justRun { mAddFileDialogFragment.hideNoFileView() }
        mAddFileController.hideNoFileView()
        verify { mAddFileDialogFragment.hideNoFileView() }
    }

    @Test
    fun should_when_setAddFileClickListener() {
        every { mAddFileController.setAddFileClickListener(any()) }.answers { callOriginal() }
        justRun { mAddFileDialogFragment.setAddFileClickListener(any()) }
        mAddFileController.setAddFileClickListener(mAddFileClickListener)
        verify { mAddFileDialogFragment.setAddFileClickListener(any()) }
    }
}