/*********************************************************************
 * * Copyright (C), 2010-2024 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AddLabelPanelModelTest.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/03/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   keweiwei                 2024/02/29       1      create
 ***********************************************************************/
package com.oplus.labelmanager

import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTypeUtils
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AddLabelPanelModelTest {

    private lateinit var addLabelPanelModel: AddLabelPanelModel

    @Before
    fun setUp() {
        addLabelPanelModel = spyk(AddLabelPanelModel())
        mockkObject(MimeTypeHelper)
        mockkStatic(FileTypeUtils::class)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `should return right when call getFileList if file is file isDirectory`() {
        //given
        val file1 = "sdcard/1"
        val file2 = "sdcard/2"
        val file3 = "sdcard/3"
        val filePaths = ArrayList<String>()
        filePaths.add(file1)
        filePaths.add(file2)
        filePaths.add(file3)
        every { addLabelPanelModel.fileIsDirectory(any()) } returns true
        //when
        val result = addLabelPanelModel.getFileList(filePaths)
        //then
        Assert.assertTrue(result.isEmpty())
    }

    @Test
    fun `should return right when call getFileList if file is not exists`() {
        //given
        val file1 = "sdcard/1"
        val file2 = "sdcard/2"
        val file3 = "sdcard/3"
        val filePaths = ArrayList<String>()
        filePaths.add(file1)
        filePaths.add(file2)
        filePaths.add(file3)
        every { addLabelPanelModel.fileIsDirectory(any()) } returns false
        every { addLabelPanelModel.fileIsNotExists(any()) } returns true
        //when
        val result = addLabelPanelModel.getFileList(filePaths)
        //then
        Assert.assertTrue(result.isEmpty())
    }

    @Test
    fun `should return right when call getFileList`() {
        //given
        val file1 = "sdcard/1"
        val file2 = "sdcard/2"
        val file3 = "sdcard/3"
        val filePaths = ArrayList<String>()
        filePaths.add(file1)
        filePaths.add(file2)
        filePaths.add(file3)
        every { addLabelPanelModel.fileIsDirectory(any()) } returns false
        every { addLabelPanelModel.fileIsNotExists(any()) } returns false
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns MimeTypeHelper.UNKNOWN_TYPE
        every { MimeTypeHelper.getMimeTypeByFileType(any()) } returns MimeTypeHelper.MimeType.MIMETYPE_IMAGE
        //when
        val result = addLabelPanelModel.getFileList(filePaths)
        //then
        Assert.assertTrue(result.size == 3)
    }
}