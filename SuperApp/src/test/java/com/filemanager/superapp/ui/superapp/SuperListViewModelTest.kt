/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SuperListViewModelTest
 * * Description : SuperListViewModel Unit Test
 * * Version     : 1.0
 * * Date        : 2022/12/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.superapp.ui.superapp

import android.content.Context
import androidx.arch.core.executor.ArchTaskExecutor
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.helper.VolumeEnvironment
import com.oplus.dropdrag.SelectionTracker
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SuperListViewModelTest {

    private lateinit var viewModel: SuperListViewModel

    @Before
    fun setup() {
        viewModel = mockk<SuperListViewModel>().apply {
            every { mTabPosition }.answers { callOriginal() }
            every { mTabPosition = any() }.answers { callOriginal() }
            every { mPath }.answers { callOriginal() }
            every { mPath = any() }.answers { callOriginal() }
            every { mDirDepth }.answers { callOriginal() }
            every { mDirDepth = any() }.answers { callOriginal() }
        }
        val context = mockk<Context>().apply {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)

        mockkStatic(ArchTaskExecutor::class)
        val executor = mockk<ArchTaskExecutor>()
        every { ArchTaskExecutor.getInstance() }.returns(executor)
        every { executor.isMainThread }.returns(true)

        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getInternalSdPath(any()) }.returns("/internal")
        every { VolumeEnvironment.getExternalSdPath(any()) }.returns("/external")

    }

    @After
    fun teardown() {
        unmockkStatic(ArchTaskExecutor::class)
        unmockkStatic(VolumeEnvironment::class)
    }

    @Test
    fun should_when_initLoader() {
        every { viewModel.initLoader(any(), any(), any(), any()) }.answers { callOriginal() }
        justRun { viewModel.loadData() }
        val loader = mockk<SuperListViewModel.SuperLoaderCallBack>()
        every { viewModel.mSuperLoaderCallBack }.returns(loader)
        val loaderController = mockk<LoaderController>().apply {
            justRun { initLoader(any(), loader) }
        }
        every { loader.getLoader() }.returns(null)
        viewModel.initLoader(loaderController, 0, null, 10)
        verify { loaderController.initLoader(0, loader) }

        every { loader.getLoader() }.returns(mockk())
        viewModel.initLoader(loaderController, 0, null, 10)
        verify { viewModel.loadData() }
    }

    @Test
    fun should_call_forceLoad_when_loadData() {
        every { viewModel.loadData() }.answers { callOriginal() }
        val superLoader = mockk<SuperLoader>()
        justRun { superLoader.forceLoad() }
        val callback = mockk<SuperListViewModel.SuperLoaderCallBack>()
        every { callback.getLoader() }.returns(superLoader)
        every { viewModel.mSuperLoaderCallBack }.returns(callback)

        every { callback.getLoader() }.returns(null)
        viewModel.loadData()
        verify(inverse = true) { superLoader.forceLoad() }

        every { callback.getLoader() }.returns(superLoader)
        viewModel.loadData()
        verify { superLoader.forceLoad() }
    }

    @Test
    fun should_call_forceLoad_when_sortReload() {
        every { viewModel.sortReload() }.answers { callOriginal() }
        val superLoader = mockk<SuperLoader>()
        justRun { superLoader.forceLoad() }
        val callback = mockk<SuperListViewModel.SuperLoaderCallBack>()
        every { callback.getLoader() }.returns(superLoader)
        every { viewModel.mSuperLoaderCallBack }.returns(callback)

        every { callback.getLoader() }.returns(null)
        viewModel.sortReload()
        verify(inverse = true) { superLoader.forceLoad() }

        every { callback.getLoader() }.returns(superLoader)
        viewModel.sortReload()
        verify { superLoader.forceLoad() }
    }

    @Test
    fun should_when_resetState() {
        every { viewModel.resetState() }.answers { callOriginal() }
        justRun { viewModel.changeListMode(any()) }

        viewModel.resetState()
        verify { viewModel.changeListMode(KtConstants.LIST_NORMAL_MODE) }
    }

    @Test
    fun should_when_clickToolbarSelectAll() {
        val uiModel = mockk<BaseUiModel<BaseFileBean>>()
        val fileList = ArrayList<BaseFileBean>()
        every { uiModel.fileList }.returns(fileList)
        val selectList = arrayListOf<Int>()
        every { uiModel.selectedList }.returns(selectList)
        val uiState = MutableLiveData(uiModel)
        every { viewModel.uiState }.returns(uiState)
        every { viewModel.clickToolbarSelectAll() }.answers { callOriginal() }

        viewModel.clickToolbarSelectAll()
        Assert.assertTrue(selectList.isEmpty())

        fileList.add(BaseFileBean())
        fileList.add(BaseFileBean().apply {
            mData = ""
        })
        fileList.add(BaseFileBean().apply {
            mData = "/sdcard/0"
        })
        viewModel.clickToolbarSelectAll()
        Assert.assertEquals(1, selectList.size)
    }

    @Test
    fun should_when_selectItem() {
        every { viewModel.selectItem(any()) }.answers { callOriginal() }
        val uiModel = mockk<BaseUiModel<BaseFileBean>>()
        val listModel = MutableLiveData<Int>()
        val stateModel = mockk<BaseStateModel>().apply {
            every { <EMAIL> }.returns(listModel)
        }
        every { uiModel.stateModel }.returns(stateModel)
        val selectList = arrayListOf<Int>()
        every { uiModel.selectedList }.returns(selectList)
        val uiState = mockk<MutableLiveData<BaseUiModel<BaseFileBean>>>()
        every { uiState.value }.returns(uiModel)
        justRun { uiState.value = any() }

        every { viewModel.uiState }.returns(uiState)

        listModel.value = KtConstants.LIST_NORMAL_MODE
        viewModel.selectItem(0)
        verify(inverse = true) { viewModel.uiState.setValue(any()) }

        listModel.value = KtConstants.LIST_SELECTED_MODE
        selectList.add(0)
        selectList.add(1)
        selectList.add(2)
        selectList.add(3)

        viewModel.selectItem(0)
        Assert.assertEquals(3, selectList.size)

        viewModel.selectItem(5)
        Assert.assertEquals(4, selectList.size)
    }

    @Test
    fun should_return_boolean_when_pressBack() {
        every { viewModel.pressBack() }.answers { callOriginal() }
        every { viewModel.changeListMode(any()) }.answers { callOriginal() }
        val listModel = MutableLiveData<Int>()
        val stateModel = mockk<BaseStateModel>().apply {
            every { <EMAIL> }.returns(listModel)
        }
        every { viewModel.mModeState }.returns(stateModel)

        listModel.value = KtConstants.LIST_SELECTED_MODE
        Assert.assertTrue(viewModel.pressBack())
        verify { viewModel.changeListMode(KtConstants.LIST_NORMAL_MODE) }

        listModel.value = KtConstants.LIST_NORMAL_MODE
        Assert.assertFalse(viewModel.pressBack())
    }

    @Test
    fun should_return_int_when_getRealFileSize() {
        every { viewModel.getRealFileSize() }.answers { callOriginal() }
        val uiModel = mockk<BaseUiModel<BaseFileBean>>()
        val fileList = ArrayList<BaseFileBean>()
        every { uiModel.fileList }.returns(fileList)
        val uiState = MutableLiveData(uiModel)
        every { viewModel.uiState }.returns(uiState)

        fileList.add(BaseFileBean())
        fileList.add(BaseFileBean())
        fileList.add(BaseFileBean())
        fileList.add(BaseFileBean())
        Assert.assertEquals(4, viewModel.getRealFileSize())
    }

    @Test
    fun should_return_int_when_getCurrentSpanCount() {
        every { viewModel.getCurrentSpanCount() }.answers { callOriginal() }

        every { viewModel.mBrowseModeState }.returns(KtConstants.SCAN_MODE_LIST)
        Assert.assertEquals(1, viewModel.getCurrentSpanCount())

        every { viewModel.mBrowseModeState }.returns(KtConstants.SCAN_MODE_GRID)
        every { viewModel.mTabPosition }.returns(Constants.TAB_ALL)
        Assert.assertEquals(3, viewModel.getCurrentSpanCount())

        every { viewModel.mTabPosition }.returns(Constants.TAB_IMAGE)
        Assert.assertEquals(4, viewModel.getCurrentSpanCount())

        every { viewModel.mTabPosition }.returns(Constants.TAB_VIDEO)
        Assert.assertEquals(4, viewModel.getCurrentSpanCount())

        every { viewModel.mTabPosition }.returns(Constants.TAB_DOCUMENT)
        Assert.assertEquals(3, viewModel.getCurrentSpanCount())

        every { viewModel.mTabPosition }.returns(Constants.TAB_AUDIO)
        Assert.assertEquals(3, viewModel.getCurrentSpanCount())
    }

    @Test
    fun should_when_getRecyclerViewScanMode() {
        every { viewModel.getRecyclerViewScanMode() }.answers { callOriginal() }
        every { viewModel.mBrowseModeState }.returns(KtConstants.SCAN_MODE_LIST)
        Assert.assertEquals(SelectionTracker.LAYOUT_TYPE.LIST, viewModel.getRecyclerViewScanMode())

        every { viewModel.mBrowseModeState }.returns(KtConstants.SCAN_MODE_GRID)
        Assert.assertEquals(SelectionTracker.LAYOUT_TYPE.GRID, viewModel.getRecyclerViewScanMode())
    }

    @Test
    fun should_when_updateLoaderPath() {
        every { viewModel.updateLoaderPath(any()) }.answers { callOriginal() }

        val superLoader = mockk<SuperLoader>()
        justRun { superLoader.updatePath(any()) }
        val callback = mockk<SuperListViewModel.SuperLoaderCallBack>()
        every { viewModel.mSuperLoaderCallBack }.returns(callback)

        every { callback.getLoader() }.returns(null)
        viewModel.updateLoaderPath(null)
        verify(inverse = true) { superLoader.updatePath(any()) }

        every { callback.getLoader() }.returns(superLoader)
        viewModel.updateLoaderPath(null)
        verify(inverse = true) { superLoader.updatePath(any()) }

        viewModel.updateLoaderPath(arrayOf("/sdcard"))
        verify { superLoader.updatePath(any()) }
    }

    @Test
    fun should_when_onCreateLoader() {
        val callback = mockk<SuperListViewModel.SuperLoaderCallBack>()
        every { callback.onCreateLoader(any()) }.answers { callOriginal() }
        every { viewModel.mTabPosition }.returns(Constants.TAB_ALL)
        every { viewModel.mPath }.returns(arrayOf("/sdcard"))
        every { viewModel.mDirDepth }.returns(10)

        Assert.assertNull(callback.onCreateLoader(null))

        Assert.assertNotNull(callback.onCreateLoader(viewModel))
    }
}