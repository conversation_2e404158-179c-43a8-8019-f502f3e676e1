/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/14
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
package com.oplus.filemanager.category.document.sortgroup

import com.filemanager.common.MyApplication
import com.filemanager.common.utils.HanziToPinyin
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.category.document.R

class FilePinyinLetterGroup : AbsFileGroup() {

    private val otherString by lazy {
        MyApplication.sAppContext.resources.getString(com.filemanager.common.R.string.doc_sort_other)
    }

    override fun createGroupTitle(fileWrapper: MediaFileWrapper): String {
        val displayName = fileWrapper.mDisplayName
        return if (displayName.isNullOrEmpty()) {
            otherString
        } else {
            val letter = fileWrapper.letter ?: HanziToPinyin.getPinYinFirstLetter(displayName)
            val first = letter.firstOrNull() ?: return otherString
            if (first.isLetter()) {
                first.toString()
            } else {
                otherString
            }
        }
    }
}