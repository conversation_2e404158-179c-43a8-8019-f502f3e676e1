package com.filemanager.thumbnail.doc.yozo

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.HandlerThread
import com.filemanager.thumbnail.ThumbnailConstant
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.doc.DocThumbnail
import com.filemanager.thumbnail.doc.IYoZoDocThumbnailCallback
import com.filemanager.thumbnail.utils.Log
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * YoZoDocThumbService的单元测试类
 * 测试YoZo文档缩略图服务的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class YoZoDocThumbServiceTest {

    // 测试用mock对象
    private lateinit var context: Context
    private lateinit var handlerThread: HandlerThread
    private lateinit var docThumbnail: DocThumbnail
    private lateinit var snapshotCallback: IYoZoDocThumbnailCallback
    private val testUri = mockk<Uri>()
    private val testFilePath = "/test/path/file.doc"
    
    /**
     * 测试前准备方法
     * 初始化mock对象和测试环境
     */
    @Before
    fun setUp() {
        // 创建mock对象
        context = mockk(relaxed = true)
        handlerThread = mockk(relaxed = true)
        // 创建测试用的文档缩略图对象
        docThumbnail = DocThumbnail(testUri, 123456L, 1024L, testFilePath)
        snapshotCallback = mockk(relaxed = true)
        
        // mock日志工具
        mockkObject(Log)
        
        // 重置所有服务的连接状态
        YoZoDocThumbService.values().forEach { service ->
            service.javaClass.getDeclaredField("serviceConnection").apply {
                isAccessible = true
                set(service, null)
            }
        }
    }
    
    /**
     * 测试后清理方法
     * 解除所有mock
     */
    @After
    fun tearDown() {
        unmockkAll()
    }
    
    /**
     * 测试getServiceIntent方法
     * 验证是否能正确创建Intent对象
     */
    @Test
    fun `getServiceIntent should create correct intent`() {
        // Given - 准备测试数据
        val fromPkg = "com.test.app"
        val service = YoZoDocThumbService.SERVICE_1
        // 使用反射设置私有属性yoZoPackage
        val field = ThumbnailManager.YoZoDocConfigs.javaClass.getDeclaredField("yoZoPackage")
        field.isAccessible = true
        field.set(null, "com.yozo.office")
        
        // When - 执行测试方法
        val intent = service.getServiceIntent(fromPkg)
        
        // Then - 验证结果
        assertEquals("com.yozo.oppo.officethumbnailservice", intent.action)
        assertEquals("com.yozo.office", intent.`package`)
        assertEquals(fromPkg, intent.extras?.getString("StartFrom"))
        assertTrue(intent.hasExtra("ActionTime"))
    }
    
    /**
     * 测试loadThumb方法
     * 验证服务绑定成功的情况
     */
    @Test
    fun `loadThumb should bind service successfully`() {
        // Given
        val service = YoZoDocThumbService.SERVICE_1
        val mockConnection = mockk<LoadDocThumbServiceConnection>(relaxed = true)
        mockkConstructor(LoadDocThumbServiceConnection::class)
        // 模拟绑定成功
        every { anyConstructed<LoadDocThumbServiceConnection>().doBind(service) } returns true
        every { anyConstructed<LoadDocThumbServiceConnection>().docUri } returns testUri
        
        // When
        service.loadThumb(context, handlerThread, docThumbnail, snapshotCallback)
        
        // Then
        verify { anyConstructed<LoadDocThumbServiceConnection>().doBind(service) }
    }
    
    /**
     * 测试loadThumb方法
     * 验证服务绑定失败的情况
     */
    @Test
    fun `loadThumb should call onLoadFailed when binding fails`() {
        // Given
        val service = YoZoDocThumbService.SERVICE_1
        val exceptionSlot = slot<Exception>()
        mockkConstructor(LoadDocThumbServiceConnection::class)
        // 模拟绑定失败
        every { anyConstructed<LoadDocThumbServiceConnection>().doBind(service) } returns false
        
        // When
        service.loadThumb(context, handlerThread, docThumbnail, snapshotCallback)
        
        // Then
        verify(exactly = 1) { snapshotCallback.onLoadFailed(capture(exceptionSlot)) }
        assertTrue(exceptionSlot.captured is IllegalStateException)
        assertTrue(exceptionSlot.captured.message?.contains("loadThumb bind YoZo failure") == true)
    }
    
    /**
     * 测试cancel方法
     * 验证uri为null且无服务连接时返回true
     */
    @Test
    fun `cancel should return true when uri is null and no service connection`() {
        // Given
        val service = YoZoDocThumbService.SERVICE_1
        
        // When
        val result = service.cancel(null)
        
        // Then
        assertTrue(result)
    }
    
    /**
     * 测试cancel方法
     * 验证uri不为null但无服务连接时返回false
     */
    @Test
    fun `cancel should return false when uri is not null but no service connection`() {
        // Given
        val service = YoZoDocThumbService.SERVICE_1
        
        // When
        val result = service.cancel(testUri)
        
        // Then
        assertFalse(result)
    }
    
    /**
     * 测试cancel方法
     * 验证uri不匹配当前连接时返回false
     */
    @Test
    fun `cancel should return false when uri does not match current connection`() {
        // Given
        val service = YoZoDocThumbService.SERVICE_1
        val differentUri = mockk<Uri>()
        val mockConnection = mockk<LoadDocThumbServiceConnection>(relaxed = true)
        service.javaClass.getDeclaredField("serviceConnection").apply {
            isAccessible = true
            set(service, mockConnection)
        }
        every { mockConnection.docUri } returns differentUri
        
        // When
        val result = service.cancel(testUri)
        
        // Then
        assertFalse(result)
    }
    
    /**
     * 测试cancel方法
     * 验证uri匹配时执行取消并返回true
     */
    @Test
    fun `cancel should execute cancel and return true when uri matches`() {
        // Given
        val service = YoZoDocThumbService.SERVICE_1
        val mockConnection = mockk<LoadDocThumbServiceConnection>(relaxed = true)
        service.javaClass.getDeclaredField("serviceConnection").apply {
            isAccessible = true
            set(service, mockConnection)
        }
        every { mockConnection.docUri } returns testUri
        
        // When
        val result = service.cancel(testUri)
        
        // Then
        verify(exactly = 1) { mockConnection.cancel() }
        assertTrue(result)
    }
    
    /**
     * 测试cancel方法
     * 验证uri为null且存在连接时执行取消并返回true
     */
    @Test
    fun `cancel should execute cancel and return true when uri is null and connection exists`() {
        // Given
        val service = YoZoDocThumbService.SERVICE_1
        val mockConnection = mockk<LoadDocThumbServiceConnection>(relaxed = true)
        service.javaClass.getDeclaredField("serviceConnection").apply {
            isAccessible = true
            set(service, mockConnection)
        }
        
        // When
        val result = service.cancel(null)
        
        // Then
        verify(exactly = 1) { mockConnection.cancel() }
        assertTrue(result)
    }
    
    /**
     * 测试onConnectionFinish方法
     * 验证匹配时清除服务连接
     */
    @Test
    fun `onConnectionFinish should clear service connection when matches`() {
        // Given
        val service = YoZoDocThumbService.SERVICE_1
        val mockConnection = mockk<LoadDocThumbServiceConnection>()
        service.javaClass.getDeclaredField("serviceConnection").apply {
            isAccessible = true
            set(service, mockConnection)
        }
        
        // When
        service.onConnectionFinish(mockConnection)
        
        // Then
        val field = service.javaClass.getDeclaredField("serviceConnection")
        field.isAccessible = true
        assertNull(field.get(service))
    }
    
    /**
     * 测试onConnectionFinish方法
     * 验证不匹配时不清除服务连接
     */
    @Test
    fun `onConnectionFinish should not clear service connection when does not match`() {
        // Given
        val service = YoZoDocThumbService.SERVICE_1
        val mockConnection1 = mockk<LoadDocThumbServiceConnection>()
        val mockConnection2 = mockk<LoadDocThumbServiceConnection>()
        service.javaClass.getDeclaredField("serviceConnection").apply {
            isAccessible = true
            set(service, mockConnection1)
        }
        
        // When
        service.onConnectionFinish(mockConnection2)
        
        // Then
        val field = service.javaClass.getDeclaredField("serviceConnection")
        field.isAccessible = true
        assertEquals(mockConnection1, field.get(service))
    }
    
    /**
     * 测试getOccupyThumbServices方法
     * 验证根据输入返回正确的服务集合
     */
    @Test
    fun `getOccupyThumbServices should return mapped services based on input`() {
        // Given
        val occupyServices = setOf(
            ThumbnailConstant.YO_ZO_THUMB_SERVICE_1,
            ThumbnailConstant.YO_ZO_THUMB_SERVICE_3,
            ThumbnailConstant.YO_ZO_THUMB_SERVICE_5
        )
        every { context.packageName } returns "com.other.app"
        
        // When
        val result = YoZoDocThumbService.getOccupyThumbServices(context, occupyServices)
        
        // Then
        assertEquals(3, result.size)
        assertTrue(result.contains(YoZoDocThumbService.SERVICE_1))
        assertTrue(result.contains(YoZoDocThumbService.SERVICE_3))
        assertTrue(result.contains(YoZoDocThumbService.SERVICE_5))
    }
    
    /**
     * 测试getOccupyThumbServices方法
     * 验证文件管理器包返回特定的服务集合
     */
    @Test
    fun `getOccupyThumbServices should return file manager services for file manager packages`() {
        // Given
        val occupyServices = emptySet<Int>()
        every { context.packageName } returns "com.coloros.filemanager"
        
        // When
        val result = YoZoDocThumbService.getOccupyThumbServices(context, occupyServices)
        
        // Then
        assertEquals(3, result.size)
        assertTrue(result.contains(YoZoDocThumbService.SERVICE_4))
        assertTrue(result.contains(YoZoDocThumbService.SERVICE_5))
        assertTrue(result.contains(YoZoDocThumbService.SERVICE_6))
    }
    
    /**
     * 测试getOccupyThumbServices方法
     * 验证非文件管理器包返回默认服务集合
     */
    @Test
    fun `getOccupyThumbServices should return default services for non-file manager packages`() {
        // Given
        val occupyServices = emptySet<Int>()
        every { context.packageName } returns "com.other.app"
        
        // When
        val result = YoZoDocThumbService.getOccupyThumbServices(context, occupyServices)
        
        // Then
        assertEquals(3, result.size)
        assertTrue(result.contains(YoZoDocThumbService.SERVICE_1))
        assertTrue(result.contains(YoZoDocThumbService.SERVICE_2))
        assertTrue(result.contains(YoZoDocThumbService.SERVICE_3))
    }
    
    /**
     * 测试mapToThumbService扩展函数
     * 验证常量到服务的正确映射
     */
    @Test
    fun `mapToThumbService should map constants to correct services`() {
        // Given & When & Then
        ThumbnailConstant.YO_ZO_THUMB_SERVICE_1.mapToThumbService {
            assertEquals(YoZoDocThumbService.SERVICE_1, it)
        }
        ThumbnailConstant.YO_ZO_THUMB_SERVICE_2.mapToThumbService {
            assertEquals(YoZoDocThumbService.SERVICE_2, it)
        }
        ThumbnailConstant.YO_ZO_THUMB_SERVICE_3.mapToThumbService {
            assertEquals(YoZoDocThumbService.SERVICE_3, it)
        }
        ThumbnailConstant.YO_ZO_THUMB_SERVICE_4.mapToThumbService {
            assertEquals(YoZoDocThumbService.SERVICE_4, it)
        }
        ThumbnailConstant.YO_ZO_THUMB_SERVICE_5.mapToThumbService {
            assertEquals(YoZoDocThumbService.SERVICE_5, it)
        }
        ThumbnailConstant.YO_ZO_THUMB_SERVICE_6.mapToThumbService {
            assertEquals(YoZoDocThumbService.SERVICE_6, it)
        }
        (-1).mapToThumbService {
            // 不应该被调用
            throw AssertionError("Should not map invalid constant")
        }
    }
    
    /**
     * 定义mapToThumbService扩展函数
     * 用于测试的辅助函数
     */
    private inline fun Int.mapToThumbService(block: (YoZoDocThumbService) -> Unit) {
        when (this) {
            ThumbnailConstant.YO_ZO_THUMB_SERVICE_1 -> YoZoDocThumbService.SERVICE_1
            ThumbnailConstant.YO_ZO_THUMB_SERVICE_2 -> YoZoDocThumbService.SERVICE_2
            ThumbnailConstant.YO_ZO_THUMB_SERVICE_3 -> YoZoDocThumbService.SERVICE_3
            ThumbnailConstant.YO_ZO_THUMB_SERVICE_4 -> YoZoDocThumbService.SERVICE_4
            ThumbnailConstant.YO_ZO_THUMB_SERVICE_5 -> YoZoDocThumbService.SERVICE_5
            ThumbnailConstant.YO_ZO_THUMB_SERVICE_6 -> YoZoDocThumbService.SERVICE_6
            else -> null
        }?.let(block)
    }
}