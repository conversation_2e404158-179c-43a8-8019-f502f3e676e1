/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/14
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.document.sortgroup

import com.oplus.filemanager.category.document.utils.FileExtensionUtils
import com.filemanager.common.wrapper.MediaFileWrapper

class FileTypeGroup : AbsFileGroup() {

    override fun createGroupTitle(fileWrapper: MediaFileWrapper): String {
        val displayName = fileWrapper.mDisplayName
        return if (displayName.isNullOrEmpty()) {
            ""
        } else {
            fileWrapper.letter ?: FileExtensionUtils.findIndexByExtension(fileWrapper).second
        }
    }
}