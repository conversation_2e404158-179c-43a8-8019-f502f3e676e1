package com.oplus.filemanager.addfilepanel.ui

import android.content.Context
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.test.platform.app.InstrumentationRegistry
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.addfilepanel.bean.AddFileBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * SelectedFileListAdapter的单元测试类
 * 用于测试SelectedFileListAdapter的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])  // 将SDK版本从28改为29以解决兼容性问题
class SelectedFileListAdapterTest {

    private lateinit var context: Context
    private lateinit var lifecycle: Lifecycle
    private lateinit var adapter: SelectedFileListAdapter
    private lateinit var mockListener: SelectedFileListAdapter.OnItemSelectListener

    /**
     * 测试前的初始化方法
     * 在每个测试方法执行前调用
     */
    @Before
    fun setUp() {
        // 获取测试上下文
        context = InstrumentationRegistry.getInstrumentation().targetContext
        // 创建模拟的生命周期对象
        lifecycle = LifecycleRegistry(mockk<LifecycleOwner>())
        // 初始化待测试的适配器
        adapter = SelectedFileListAdapter(context, lifecycle)
        // 创建模拟的监听器
        mockListener = mockk(relaxed = true)
        // 设置监听器
        adapter.mOnItemSelectListener = mockListener
    }

    /**
     * 测试后的清理方法
     * 在每个测试方法执行后调用
     */
    @After
    fun tearDown() {
        unmockkAll() // 清理所有mock对象
        adapter.onDestroy() // 确保每次测试后清理状态
    }

    /**
     * 测试onDestroy方法是否清除了缓存和选择项
     */
    @Test
    fun `onDestroy should clear cache and selections`() {
        // 添加测试数据
        adapter.listSelect.add(123L)
        // 使用反射访问私有字段mSizeCache
        val sizeCacheField = SelectedFileListAdapter::class.java.getDeclaredField("mSizeCache")
        sizeCacheField.isAccessible = true
        val sizeCache = sizeCacheField.get(adapter) as MutableMap<String, String>
        sizeCache["test"] = "value"

        // 调用待测试方法
        adapter.onDestroy()

        // 验证选择列表是否被清空
        assertTrue(adapter.listSelect.isEmpty())
        // 验证缓存是否被清空
        assertTrue(sizeCache.isEmpty())
    }

    /**
     * 测试getItemKey方法在路径为null时返回位置哈希值
     */
    @Test
    fun `getItemKey should return position hash when path is null`() {
        // 创建测试文件对象
        val file = AddFileBean().apply { mData = null }
        // 调用待测试方法
        val result = adapter.getItemKey(file, 1)
        // 验证返回结果是否符合预期
        assertEquals(1.hashCode(), result)
    }

    /**
     * 测试getItemKey方法返回小写路径的哈希值
     */
    @Test
    fun `getItemKey should return lowercase path hash`() {
        // 创建测试文件对象
        val file = AddFileBean().apply { mData = "TEST" }
        // 调用待测试方法
        val result = adapter.getItemKey(file, 1)
        // 验证返回结果是否符合预期
        assertEquals("test".hashCode(), result)
    }

    /**
     * 测试onBindViewHolder方法在位置越界时不进行绑定
     */
    @Test
    fun `onBindViewHolder should not bind when position out of bounds`() {
        // 创建模拟的ViewHolder
        val holder = mockk<SelectedFileListAdapter.AddFileViewHolder>(relaxed = true)
        // 设置测试数据
        adapter.mFiles = mutableListOf(AddFileBean())

        // 调用待测试方法(传入越界位置)
        adapter.onBindViewHolder(holder, -1)
        adapter.onBindViewHolder(holder, 1)

        // 验证loadData方法没有被调用
        verify(exactly = 0) { holder.loadData(any(), any(), any(), any(), any(), any(), any(), any()) }
    }
}