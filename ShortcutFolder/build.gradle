plugins {
    id "com.android.library"
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace  "com.oplus.filemanager.shortcutfolder"
    sourceSets {
        main {
            res.srcDirs += ['res']
        }
    }
}

dependencies {

    implementation libs.androidx.appcompat
    implementation libs.google.material

    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.recyclerview
    implementation libs.oplus.appcompat.poplist
    implementation libs.oplus.appcompat.toolbar
    implementation libs.oplus.appcompat.responsiveui
    implementation libs.oplus.appcompat.sidenavigationbar
    implementation libs.oplus.appcompat.bottomnavigation
    implementation libs.oplus.appcompat.panel
    implementation libs.oplus.appcompat.floatingactionbutton
    implementation libs.koin.android

    implementation project(':Common')
    implementation project(':SelectDir')
    implementation project(':FileOperate')
    implementation project(':Provider')
    implementation project(':Encrypt')
    implementation project(':LabelManager')
    implementation project(':framework:AddFilePanel')

    if (prop_use_prebuilt_drap_drop_lib.toBoolean()) {
        implementation libs.oplus.filemanager.dragDrop
    } else {
        implementation project(':exportedLibs:DragDropSelection')
    }
}