/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemotePicFactory.kt
 ** Description: RemotePicFactory
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.category.remotedevice.glide.pic

import android.content.Context
import android.net.Uri
import com.bumptech.glide.load.model.ModelLoader
import com.bumptech.glide.load.model.ModelLoaderFactory
import com.bumptech.glide.load.model.MultiModelLoaderFactory
import com.filemanager.common.base.RemoteFileBean

class RemotePicFactory(context: Context) :
    ModelLoaderFactory<RemoteFileBean, Uri> {

    private val appContext = context.applicationContext

    override fun build(multiFactory: MultiModelLoaderFactory): ModelLoader<RemoteFileBean, Uri> {
        return RemotePicLoader(appContext)
    }

    override fun teardown() {
        //do nothing
    }
}