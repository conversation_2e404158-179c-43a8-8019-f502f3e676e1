package com.coloros.filemanager.utils

import android.app.Activity
import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.OplusPackageManager
import android.content.pm.PackageInfo
import android.os.Bundle
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.oplus.compat.content.pm.ApplicationInfoNative
import com.oplus.compat.content.pm.PackageManagerNative
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class KtAppUtilsTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        var context = mockk<Context>(relaxed = true) {
            every { applicationContext }.returns(this)
        }

        MyApplication.init(context)
        mockkStatic(SdkUtils::class)
        every { SdkUtils.getOSVersion() }.returns(25)
    }

    @Test
    fun `should verify when get installed map without`() {
        mockkObject(PermissionUtils)
        every { PermissionUtils.hasGetInstalledAppsPermission() }.returns(false)
        Assert.assertTrue(KtAppUtils.getInstalledMap().isEmpty())

        every { PermissionUtils.hasGetInstalledAppsPermission() }.returns(true)
        var context = MyApplication.sAppContext
        var packageInfo = PackageInfo()
        packageInfo.packageName = "test"
        every { context.packageManager }.returns(mockk(relaxed = true) {
            every { getInstalledPackages(0) }.returns(List(1) { packageInfo })
        })
        Assert.assertTrue(KtAppUtils.getInstalledMap().isNotEmpty())
    }

    @Test
    fun `should verify when get app name without`() {

        var context = MyApplication.sAppContext

        every { context.packageManager }.throws(Exception("test-error"))
        Assert.assertEquals("", KtAppUtils.getAppName("test"))

        every { context.packageManager }.returns(mockk(relaxed = true) {
            every { getApplicationLabel(any()) }.returns("test")
        })
        Assert.assertEquals("test", KtAppUtils.getAppName("test"))
    }

    @Test
    fun `should verify when check app enabled with dialog without`() {
        mockkObject(JavaFileHelper)
        every { JavaFileHelper.safeCheck(any(), KtAppUtils.ConnectionResult.MISSED) }.returns(KtAppUtils.ConnectionResult.DISABLED)
        Assert.assertFalse(KtAppUtils.checkAppEnabledWithDialog(MyApplication.sAppContext, "test", 0))

        every { JavaFileHelper.safeCheck(any(), KtAppUtils.ConnectionResult.MISSED) }.returns(KtAppUtils.ConnectionResult.SUCCESS)
        Assert.assertTrue(KtAppUtils.checkAppEnabledWithDialog(MyApplication.sAppContext, "test", 0))

        every { JavaFileHelper.safeCheck(any(), KtAppUtils.ConnectionResult.MISSED) }.returns(KtAppUtils.ConnectionResult.MISSED)
        Assert.assertFalse(KtAppUtils.checkAppEnabledWithDialog(MyApplication.sAppContext, "test", 0))
    }

    //
    @Test
    fun `should verify when check app enabled with dialog return without`() {
        mockkObject(JavaFileHelper)
        every { JavaFileHelper.safeCheck(any(), KtAppUtils.ConnectionResult.MISSED) }.returns(KtAppUtils.ConnectionResult.DISABLED)
        Assert.assertNull(KtAppUtils.checkAppEnabledWithDialogReturn(MyApplication.sAppContext, "test", 0))

        every { JavaFileHelper.safeCheck(any(), KtAppUtils.ConnectionResult.MISSED) }.returns(KtAppUtils.ConnectionResult.SUCCESS)
        Assert.assertNull(KtAppUtils.checkAppEnabledWithDialogReturn(MyApplication.sAppContext, "test", 0))

        every { JavaFileHelper.safeCheck(any(), KtAppUtils.ConnectionResult.MISSED) }.returns(KtAppUtils.ConnectionResult.MISSED)
        Assert.assertNull(KtAppUtils.checkAppEnabledWithDialogReturn(MyApplication.sAppContext, "test", 0))
    }


    @Test
    fun `should verify when show app enabled with dialog without`() {
        mockkObject(KtAppUtils)
        every { KtAppUtils.getAppName(any()) }.returns("test")

        Assert.assertNull(KtAppUtils.showAppEnabledWithDialog(MyApplication.sAppContext, "test", 0))

        Assert.assertNull(KtAppUtils.showAppEnabledWithDialog(mockk<Activity>(relaxed = true), "test", 0))
    }

    @Test
    fun `should verify when check app force enabled without`() {
        Assert.assertEquals(KtAppUtils.ConnectionResult.MISSED, KtAppUtils.checkAppForceEnabled(""))

        var context = MyApplication.sAppContext
        var applicationInfo = mockk<ApplicationInfo>(relaxed = true)
        applicationInfo.enabled = true
        every { context.packageManager }.returns(mockk(relaxed = true) {
            every { getApplicationInfo("test", 0) }.returns(applicationInfo)
        })
        Assert.assertEquals(KtAppUtils.ConnectionResult.SUCCESS, KtAppUtils.checkAppForceEnabled("test"))

        applicationInfo.enabled = false
        Assert.assertEquals(KtAppUtils.ConnectionResult.DISABLED, KtAppUtils.checkAppForceEnabled("test"))

        every { context.packageManager }.throws(Exception("test-error"))
        Assert.assertEquals(KtAppUtils.ConnectionResult.MISSED, KtAppUtils.checkAppForceEnabled("test"))
    }

    @Test
    fun `should verify when get application id without`() {
        Assert.assertFalse(KtAppUtils.getApplicationId().isEmpty())
    }

    @Test
    fun `should verify when start phone manager without`() {
        var context = MyApplication.sAppContext

        KtAppUtils.startPhoneManager(context)

        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.sPhoneManagerStartInfo }.returns(Pair("test1", "test2"))
        mockkObject(KtAppUtils)
        every { KtAppUtils.checkAppEnabledWithDialog(context, any(), any()) }.returns(false)
        KtAppUtils.startPhoneManager(context)

        every { KtAppUtils.checkAppEnabledWithDialog(context, any(), any()) }.returns(true)
        KtAppUtils.startPhoneManager(context, Bundle())

        verify { context.startActivity(any()) }
    }

    @Test
    fun should_return_boolean_isAppFrozen_user_oldApi() {
        val applicationInfo = ApplicationInfo()
        mockkStatic(ApplicationInfoNative::class)
        every { SdkUtils.getSDKVersion() }.returns(32)
        every { ApplicationInfoNative.getOplusFreezeState(any()) }.returns(PackageManagerNative.OPLUS_STATE_FREEZE_FREEZED + 1)
        Assert.assertFalse(KtAppUtils.isAppFrozen(applicationInfo))

        every { ApplicationInfoNative.getOplusFreezeState(any()) }.returns(PackageManagerNative.OPLUS_STATE_FREEZE_FREEZED)
        Assert.assertTrue(KtAppUtils.isAppFrozen(applicationInfo))

        every { ApplicationInfoNative.getOplusFreezeState(any()) }.throws(Exception("test"))
        Assert.assertFalse(KtAppUtils.isAppFrozen(applicationInfo))
    }

    @Test
    fun should_return_boolean_isAppFrozen_user_newApi() {
        mockkStatic(Utils::class)
        every { Utils.getUserId() }.returns(0)

        val applicationInfo = ApplicationInfo()
        applicationInfo.packageName = "com.coloros.filemanager"
        every { SdkUtils.getOSVersion() }.returns(30)
        every { SdkUtils.getOSSdkVersion() }.returns(30)
        every { SdkUtils.getOSSdkSubVersion() }.returns(0)
        mockkStatic(OplusPackageManager::class)
        val pkgMgr = mockk<OplusPackageManager>()
        every { OplusPackageManager.getOplusPackageManager(any()) }.returns(pkgMgr)

        every { pkgMgr.getOplusFreezePackageState(any(), any()) }.returns(OplusPackageManager.STATE_OPLUS_FREEZE_FREEZED + 1)
        Assert.assertFalse(KtAppUtils.isAppFrozen(applicationInfo))

        every { pkgMgr.getOplusFreezePackageState(any(), any()) }.returns(OplusPackageManager.STATE_OPLUS_FREEZE_FREEZED)
        Assert.assertTrue(KtAppUtils.isAppFrozen(applicationInfo))

        every { pkgMgr.getOplusFreezePackageState(any(), any()) }.throws(Exception("test"))
        Assert.assertFalse(KtAppUtils.isAppFrozen(applicationInfo))

        unmockkStatic(Utils::class)
        unmockkStatic(OplusPackageManager::class)
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}