/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : ThirdAppPackageChangeHelper.kt
 * * Description : 监听dmp和场景智能apk安装更新类
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.util.Log
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.coloros.filemanager.appswitch.ThirdAppPackageChangeReceiver.Companion.TARGET_PACKAGE_DMP
import com.coloros.filemanager.appswitch.ThirdAppPackageChangeReceiver.IPackageChangeCallback.Companion.OPEARTION_CLEARED
import com.coloros.filemanager.appswitch.ThirdAppPackageChangeReceiver.IPackageChangeCallback.Companion.OPERATION_UPDATE
import com.coloros.filemanager.appswitch.util.DataSyncUtil
import com.coloros.filemanager.appswitch.util.InitHelper
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.SdkUtils
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class ThirdAppPackageChangeHelper(lifecycle: Lifecycle? = null) : DefaultLifecycleObserver,
    ThirdAppPackageChangeReceiver.IPackageChangeCallback {

    companion object {
        const val TAG = "ThirdAppPackageChangeHelper"

        const val DELAY_TIEM_10S = 10 * 1000L
        const val OPPO_COMPONENT_SAFE = "oppo.permission.OPPO_COMPONENT_SAFE"
    }

    private var receiver: ThirdAppPackageChangeReceiver? = null

    init {
        lifecycle?.addObserver(this)
        Log.i(TAG, "init lifecycle $lifecycle")
    }


    fun initHeler(context: Context) {
        registerReceiver(context, true)
    }

    fun releaseHelper(context: Context) {
        unregisterRecever(context)
    }


    private fun registerReceiver(context: Context, useApplcation: Boolean = false) {
        Log.i(TAG, "registerReceiver START, receiver $receiver useApplcation $useApplcation")
        if (receiver == null) {
            val intentFilter = IntentFilter()
            intentFilter.addAction(Intent.ACTION_PACKAGE_REPLACED)
            intentFilter.addAction(Intent.ACTION_PACKAGE_ADDED)
            intentFilter.addAction(Intent.ACTION_PACKAGE_REMOVED)
            intentFilter.addAction(Intent.ACTION_PACKAGE_CHANGED)
            intentFilter.addAction(Intent.ACTION_PACKAGE_DATA_CLEARED)
            intentFilter.addDataScheme("package")
            receiver = ThirdAppPackageChangeReceiver()
            receiver?.packageChangeCallback = this
            if (useApplcation && SdkUtils.isAtLeastT()) {
                context.applicationContext.registerReceiver(
                    receiver,
                    intentFilter,
                    OPPO_COMPONENT_SAFE,
                    null,
                    Context.RECEIVER_EXPORTED
                )
            } else {
                context.registerReceiver(receiver, intentFilter, OPPO_COMPONENT_SAFE, null)
            }
            Log.i(TAG, "registerReceiver")
        }
        Log.i(TAG, "registerReceiver END")
    }

    private fun unregisterRecever(context: Context) {
        if (receiver != null) {
            context.unregisterReceiver(receiver)
            receiver?.packageChangeCallback = null
            receiver = null
            Log.i(TAG, "unRegistReceiver")
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        if (owner is Activity) {
            Log.i(TAG, "onCreate")
            registerReceiver(owner)
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        if (owner is Activity) {
            Log.i(TAG, "onDestroy")
            unregisterRecever(owner)
        }
    }

    override fun onPackageChanged(operation: Int, packageName: String) {
        Log.i(TAG, "onPackageChanged, operation $operation, packageName $packageName")
        if (operation == OPEARTION_CLEARED && packageName.contentEquals(TARGET_PACKAGE_DMP)) {
            GlobalScope.launch() {
                delay(DELAY_TIEM_10S)
                Log.i(TAG, "onPackageChanged doSyncDataBetweenDmpAndFileManager ")
                DataSyncUtil.doSyncDataBetweenDmpAndFileManager(MyApplication.appContext)
                //dmp被清楚数据之后，需要将开关状态也重新写入dmp
                val dmpApi = Injector.injectFactory<IDmpSearchApi>()
                dmpApi?.updateSwitchSpToDmp()
            }
        } else if (operation == OPERATION_UPDATE) {
            //检测到由dmp或场景智能的apk发生变化时，触发重新初始化
            MyApplication.dmpAndIndexInitSuc = false
            InitHelper.resetStatus()
            AppSwitchManager.initDmpAndAppSwitch(true)
        }
    }
}