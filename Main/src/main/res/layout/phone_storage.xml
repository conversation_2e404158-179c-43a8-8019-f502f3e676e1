<?xml version="1.0" encoding="utf-8"?>
<com.filemanager.common.view.HoverSmoothRoundedConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/action_to_phone_space"
    android:layout_width="@dimen/main_phone_storage_width"
    android:layout_height="@dimen/main_storage_height"
    android:background="@drawable/bg_card_common"
    android:minHeight="@dimen/main_storage_height"
    app:hover_radius="?attr/couiRoundCornerM"
    app:corner_radius="?attr/couiRoundCornerM"
    app:default_background="true"
    app:solid_color="?attr/couiColorBackground">

    <com.oplus.filemanager.main.view.ScaleSizeTextView
        android:id="@+id/phone_storage_title"
        android:textAppearance="@style/couiTextButtonM"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:gravity="start"
        android:maxLines="2"
        android:ellipsize="end"
        android:textSize="16sp"
        android:text="@string/string_all_files"
        android:textColor="?attr/couiColorLabelPrimary"
        app:layout_constraintBottom_toTopOf="@id/phone_storage_space_desc"
        app:layout_constraintEnd_toStartOf="@+id/action_cleanup_garbage"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.oplus.filemanager.recent.view.PressFeedBackImage
        android:forceDarkAllowed="false"
        android:background="@drawable/clean_up_hover_bg"
        android:id="@+id/action_cleanup_garbage"
        android:layout_width="@dimen/dimen_30dp"
        android:layout_height="@dimen/dimen_30dp"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:contentDescription="@string/settings_function_menu_cleanup"
        android:src="@drawable/bg_clear_parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="@dimen/dimen_16dp" />

    <com.oplus.filemanager.main.view.FormatTextView
        android:id="@+id/phone_storage_space_desc"
        android:textAppearance="@style/couiTextButtonS"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:layout_marginBottom="@dimen/storage_space_desc_margin_bottom"
        android:ellipsize="end"
        android:maxLines="1"
        android:textSize="@dimen/store_large_font_size"
        android:layout_marginLeft="@dimen/dimen_16dp"
        android:textColor="?attr/couiColorLabelPrimary"
        app:layout_constraintBottom_toTopOf="@+id/phone_storage_space_progress"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.oplus.filemanager.main.view.HorizontalProgressBar
        android:id="@+id/phone_storage_space_progress"
        android:layout_width="0dp"
        android:layout_height="@dimen/dimen_8dp"
        android:layout_marginBottom="@dimen/dimen_18dp"
        android:forceDarkAllowed="false"
        android:secondaryProgress="100"
        app:couiHorizontalProgressBarBackgroundColor="?attr/couiColorDivider"
        app:couiHorizontalProgressBarProgressColor="?attr/couiColorPrimary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/phone_storage_space_desc"
        app:layout_constraintStart_toStartOf="@id/phone_storage_space_desc" />
</com.filemanager.common.view.HoverSmoothRoundedConstraintLayout>