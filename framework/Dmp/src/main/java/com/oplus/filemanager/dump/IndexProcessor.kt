/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : IndexProcessor.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.dump

import android.util.Log
import androidx.annotation.WorkerThread
import com.filemanager.common.bean.IndexOperationResult
import com.filemanager.common.bean.SearchIndexBean
import com.oplus.dmp.sdk.analyzer.tokenizer.IRemoteTokenizer
import com.oplus.dmp.sdk.analyzer.tokenizer.Normalizer
import com.oplus.dmp.sdk.analyzer.tokenizer.Segmenter
import com.oplus.dmp.sdk.index.Document
import com.oplus.dmp.sdk.index.IndexError
import com.oplus.dmp.sdk.index.IndexProtocol
import com.oplus.dmp.sdk.index.IndexProxy
import com.oplus.dmp.sdk.index.IndexState
import com.oplus.dmp.sdk.index.config.ConfigException
import com.oplus.dmp.sdk.index.config.DataType
import com.oplus.dmp.sdk.index.config.FieldConfig
import com.oplus.dmp.sdk.index.config.IndexConfig
import com.oplus.filemanager.dump.SearchManagerUtil.getClientWrapper

object IndexProcessor {

    const val TAG = "IndexProcessor"

    //索引资源名称，类似SQLite中的数据库的table名称
    const val CLIENT_NAME = "third_app_file"

    //索引配置的版本号
    private const val INDEX_CONFIG_VERSION = 1

    //索引配置中最大存储空间设置
    private const val INDEX_CONFIG_MAX_STORAGE_SIZE = 500 * 1024 * 1024L

    //索引配置中最大存储doc数量设置
    private const val INDEX_CONFIG_MAX_DOC_CNT = 200000

    //索引中文件title，参与搜索
    const val INDEX_FIELD_NAME = "name"

    //索引中文件title对应的拼音字段，参与搜索
    const val INDEX_FIELD_NAME_PINGYIN = "name_pingyin"

    //索引中文件来源的应用包名
    const val INDEX_FIELD_PACKAGE = "package"

    //索引中文件来源，参与搜索
    const val INDEX_FIELD_SOURCE_NAME = "source_name"

    //索引中文件title对应的拼音字段，参与搜索
    const val INDEX_FIELD_SOURCE_NAME_PINGYIN = "source_name_pingyin"

    //索引中文件时间字符串
    const val INDEX_FIELD_FILE_TIME = "file_time"

    //索引中的这条记录的检测时间
    const val INDEX_FIELD_DETECT_TIME = "detect_time"

    //索引中文件大小
    const val INDEX_FIELD_FILE_SIZE = "size"

    //索引中文件
    const val INDEX_FIELD_SOURCE_TYPE = "source_type"


    @Volatile
    private var indexProxy: IndexProxy? = null


    @WorkerThread
    fun getIndexProxy(): IndexProxy? {
        if (indexProxy == null) {
            // 指定资源名，每个资源都有独立的数据库
            indexProxy = getClientWrapper(CLIENT_NAME)
                ?.indexProxy
        }
        return indexProxy
    }

    fun resetProxy() {
        Log.i(TAG, "resetProxy")
        indexProxy = null
    }

    @WorkerThread
    fun getCurrentIndexProxy(): IndexProxy? {
        indexProxy = getClientWrapper(CLIENT_NAME)
            ?.indexProxy
        return indexProxy
    }

    @WorkerThread
    fun checkAndProcessIndexState(): Boolean {
        val indexProxy = getCurrentIndexProxy()
        if (indexProxy == null) {
            Log.i(TAG, "checkAndProcessIndexState indexProxy is null, return false")
            return false
        }
        val indexState = indexProxy.state
        Log.i(TAG, "checkAndProcessIndexState indexState $indexState")
        if (indexState != null) {
            if (checkIndexStateOk(indexState)) {
                return true
            }
            if (checkIndexStateNeedClearAndReinit(indexState)) {
                val initIndex = initIndexProxy()
                Log.e(TAG, "checkAndProcessIndexState clear and reinit index, init result $initIndex")
                return true
            } else {
                Log.e(TAG, "checkAndProcessIndexState indexState $indexState, return false")
                return false
            }
        } else {
            return false
        }
    }

    private fun checkIndexStateOk(indexState: IndexState): Boolean {
        val indexOk =
            (indexState == IndexState.INDEX_STATE_OK || indexState == IndexState.INDEX_STATE_INDEX_CORRUPTED)
        Log.i(TAG, "checkIndexStateOk indexState $indexState result $indexOk")
        return indexOk
    }


    private fun checkIndexStateNeedClearAndReinit(indexState: IndexState): Boolean {
        val needClear =
            (indexState == IndexState.INDEX_STATE_CLIENT_NOT_MATCH_INDEX || indexState == IndexState.INDEX_STATE_INDEX_STRUCTURE_CHANGED)
        Log.i(TAG, "checkIndexStateNeedClearAndReinit indexState $indexState result $needClear")
        return needClear
    }


    /**
     * 初始化索引，返回值表明初始化成功
     */
    @WorkerThread
    fun initIndexProxy(): Boolean {
        //服务端配置
        var result = false
        val indexProxy = getCurrentIndexProxy()
        if (indexProxy == null) {
            Log.i(TAG, "initIndexProxy indexProxy is null, return false")
            return false
        }
        val serviceIndexConfig = indexProxy.config
        //现有配置
        val newIndexConfig: IndexConfig
        try {
            newIndexConfig = createIndexConfigInternal()
        } catch (e: ConfigException) {
            Log.e(TAG, "initIndexProxy newIndexConfig error", e)
            return false
        }
        //对比新老的config，看是否需要清楚老的config或设置新的config
        val compareResult = compareConfigs(serviceIndexConfig, newIndexConfig, indexProxy)
        result = if (compareResult.first && compareResult.second) {
            val clearResult = indexProxy.clear()
            Log.i(TAG, "initIndexProxy clear old and setNew $clearResult")
            indexProxy.setConfig(newIndexConfig)
        } else if (compareResult.second) {
            Log.i(TAG, "initIndexProxy only setNew $newIndexConfig")
            indexProxy.setConfig(newIndexConfig)
        } else {
            Log.i(TAG, "initIndexProxy no update and no clear old")
            true
        }
        Log.i(TAG, "initIndexProxy result $result")
        return result
    }

    @Throws(ConfigException::class)
    private fun createIndexConfigInternal(): IndexConfig {
        return IndexConfig.Builder()
            .setVersion(INDEX_CONFIG_VERSION)
            .setMaxStorageSize(INDEX_CONFIG_MAX_STORAGE_SIZE)
            .setMaxDocumentCount(INDEX_CONFIG_MAX_DOC_CNT)
            //设置文件名称（简历.doc），参与搜索
            .addField(fieldConfigName())
            //设置文件名称的拼音配置（简历.doc），参与搜索
            .addField(fieldConfigNamePingyin())
            //设置文件来源（微信/钉钉/qq/飞书中的群名，单聊名称），参与收索
            .addField(fieldConfigSourceName())
            //设置文件来源的拼音配置（微信/钉钉/qq/飞书中的群名，单聊名称），参与收索
            .addField(fieldConfigSourceNamePingyin())
            //记录这一个文件来源对应的app的包名（微信：com.tencent.mm）
            .addField(fieldConfigPackage())
            //记录检测到文件的发送/接收时间。
            .addField(fieldConfigFileTime())
            //记录这条index记录的检测时间
            .addField(fieldConfigDetectTime())
            //记录文件的大小
            .addField(fieldConfigSize())
            //记录文件的来源类型（群聊，单聊，云端收藏等）
            .addField(fieldConfigSourceType())
            .build()
    }

    @Throws(ConfigException::class)
    private fun fieldConfigFileTime(): FieldConfig? = FieldConfig.Builder()
        .setName(INDEX_FIELD_FILE_TIME)
        .setType(DataType.DATA_TYPE_LONG)
        .setStored()
        .build()

    @Throws(ConfigException::class)
    private fun fieldConfigDetectTime(): FieldConfig? = FieldConfig.Builder()
        .setName(INDEX_FIELD_DETECT_TIME)
        .setType(DataType.DATA_TYPE_LONG)
        .setStored()
        .setStoreDocValue()
        .build()

    @Throws(ConfigException::class)
    private fun fieldConfigPackage(): FieldConfig? = FieldConfig.Builder()
        .setName(INDEX_FIELD_PACKAGE)
        .setType(DataType.DATA_TYPE_STRING)
        .setStored()
        .build()

    @Throws(ConfigException::class)
    private fun fieldConfigSourceName(): FieldConfig? = FieldConfig.Builder()
        .setName(INDEX_FIELD_SOURCE_NAME)
        .setType(DataType.DATA_TYPE_STRING)
        .setStored()
        .setSearched(
            mutableListOf(
                Normalizer.TO_LOWER,
                Normalizer.TO_SIMPLIFIED_CHINESE,
                Segmenter.CUT_FOR_INDEX
            ) as? List<IRemoteTokenizer>
        )
        .setNullable()
        .build()

    @Throws(ConfigException::class)
    private fun fieldConfigSourceNamePingyin(): FieldConfig? = FieldConfig.Builder()
        .setName(INDEX_FIELD_SOURCE_NAME_PINGYIN)
        .setType(DataType.DATA_TYPE_STRING)
        .setSearched(
            mutableListOf(
                Normalizer.TO_LOWER,
                Normalizer.TO_SIMPLIFIED_CHINESE,
                Segmenter.CUT_FOR_INDEX_PINYIN,
            ) as? List<IRemoteTokenizer>
        )
        .setNullable()
        .build()

    @Throws(ConfigException::class)
    private fun fieldConfigName(): FieldConfig? = FieldConfig.Builder()
        .setName(INDEX_FIELD_NAME)
        .setType(DataType.DATA_TYPE_STRING)
        .setStored()
        .setSearched(
            mutableListOf(
                Normalizer.TO_LOWER,
                Normalizer.TO_SIMPLIFIED_CHINESE,
                Segmenter.CUT_FOR_INDEX,
            ) as? List<IRemoteTokenizer>
        )
        .build()

    @Throws(ConfigException::class)
    private fun fieldConfigNamePingyin(): FieldConfig? = FieldConfig.Builder()
        .setName(INDEX_FIELD_NAME_PINGYIN)
        .setType(DataType.DATA_TYPE_STRING)
        .setSearched(
            mutableListOf(
                Normalizer.TO_LOWER,
                Normalizer.TO_SIMPLIFIED_CHINESE,
                Segmenter.CUT_FOR_INDEX_PINYIN,
            ) as? List<IRemoteTokenizer>
        )
        .build()


    @Throws(ConfigException::class)
    private fun fieldConfigSourceType(): FieldConfig? = FieldConfig.Builder()
        .setName(INDEX_FIELD_SOURCE_TYPE)
        .setType(DataType.DATA_TYPE_INT)
        .setStored()
        .build()

    private fun fieldConfigSize(): FieldConfig? = FieldConfig.Builder()
        .setName(INDEX_FIELD_FILE_SIZE)
        .setType(DataType.DATA_TYPE_LONG)
        .setStored()
        .build()





    /**
     * 对比新老配置，得到的结果是是否需要清楚老配置，更新新配置
     */
    private fun compareConfigs(
        oldConfig: IndexConfig?,
        newConfig: IndexConfig,
        indexProxy: IndexProxy
    ): Pair<Boolean, Boolean> {
        var needSetNew = false
        var needClearOld = false
        if (oldConfig == null) {
            //老的为空，说明索引dmp那边被清除数据，需要重新setNew，
            needSetNew = true
            val state = indexProxy.state
            if (checkIndexStateNeedClearAndReinit(state)) {
                needClearOld = true
            }
        } else {
            val configEqual = (oldConfig == newConfig)
            //新配置和老配置相同，则不需要更新新配置
            needSetNew = !configEqual
            val oldField = oldConfig.fieldConfigs
            val newField = newConfig.fieldConfigs
            val fieldEqual = oldField.equals(newField)
            val indexNeedClear = indexProxy.isRebuildRequired
            //新的配置和老配置中field列表内容不相同，则需要清除索引中的数据，
            needClearOld = !fieldEqual || indexNeedClear
            Log.i(
                TAG,
                "compareConfigs, configEqual $configEqual, fieldEqual $fieldEqual, indexNeedClear $indexNeedClear"
            )
        }
        Log.i(
            TAG,
            "compareConfigs needSetNew $needSetNew, needClearOld $needClearOld, " +
                    "oldConfig $oldConfig, newCofig $newConfig"
        )
        return Pair(needClearOld, needSetNew)
    }


    @WorkerThread
    fun clearIndex(): Boolean {
        var result = false
        val indexProxy = getIndexProxy()
        indexProxy?.let {
            result = it.clear()
        }
        Log.i(TAG, "clearIndex index $indexProxy, result: $result")
        return result
    }


    @WorkerThread
    fun addDocument(indexBean: SearchIndexBean): IndexOperationResult {
        return processSingleOperation(indexBean, IndexOperationResult.OPERATION_TYPE_ADD)
    }

    @WorkerThread
    fun updateDocument(indexBean: SearchIndexBean): IndexOperationResult {
        return processSingleOperation(indexBean, IndexOperationResult.OPERATION_TYPE_UPDATE)
    }

    @WorkerThread
    fun deleteDocument(indexBean: SearchIndexBean): IndexOperationResult {
        return processSingleOperation(indexBean, IndexOperationResult.OPERATION_TYPE_DELETE)
    }

    @WorkerThread
    private fun processSingleOperation(
        indexBean: SearchIndexBean,
        operationType: Int
    ): IndexOperationResult {
        val indexProxy = getIndexProxy()
        val operationResult = IndexOperationResult(
            indexBean = indexBean,
            operationType = operationType
        )
        if (indexProxy == null) {
            Log.i(TAG, "processSingleOperation indexProxy null, return")
            operationResult.errorCode = IndexOperationResult.ERROR_CODE_INDEX_PROXY_NULL
            return operationResult
        }
        val medianResult = when (operationType) {
            IndexOperationResult.OPERATION_TYPE_ADD -> indexProxy.addDocument(convertSearchIndexToDoc(indexBean))
            IndexOperationResult.OPERATION_TYPE_UPDATE -> indexProxy.updateDocument(convertSearchIndexToDoc(indexBean))
            IndexOperationResult.OPERATION_TYPE_DELETE -> indexProxy.deleteDocument(indexBean.identification)
            else -> null
        }
        medianResult?.let { indexError ->
            Log.i(
                TAG,
                "processSingleOperation result.isSuccess ${indexError.isSuccess}, indexError.errorSize ${indexError.errors.size}, " +
                        "operationType : $operationType"
            )
            if (!indexError.isSuccess) {
                val error = indexError.errors[0]
                val code = error.errorCode
                operationResult.errorCode = code
                Log.i(TAG, "processSingleOperation , indexError.firstError.ErrorCode $code")
            } else {
                operationResult.errorCode = IndexOperationResult.ERROR_CODE_SUCCESS
            }
        }
        Log.i(TAG, "processSingleOperation input $indexBean, output $operationResult")
        return operationResult
    }


    @WorkerThread
    fun addDocuments(indexBeans: List<SearchIndexBean>): List<IndexOperationResult> {
        return processBatchOperation(
            indexBeans,
            operationType = IndexOperationResult.OPERATION_TYPE_ADD
        )
    }

    @WorkerThread
    fun updateDocuments(indexBeans: List<SearchIndexBean>): List<IndexOperationResult> {
        return processBatchOperation(
            indexBeans,
            operationType = IndexOperationResult.OPERATION_TYPE_UPDATE
        )
    }

    @WorkerThread
    fun deleteDocuments(indexBeans: List<SearchIndexBean>): List<IndexOperationResult> {
        return processBatchOperation(
            indexBeans,
            operationType = IndexOperationResult.OPERATION_TYPE_DELETE
        )
    }

    private fun processBatchOperation(
        indexBeans: List<SearchIndexBean>,
        operationType: Int
    ): List<IndexOperationResult> {
        val indexProxy = getIndexProxy()
        val returnResultList: MutableList<IndexOperationResult> = mutableListOf()
        indexProxy?.let { proxy ->
            when (operationType) {
                IndexOperationResult.OPERATION_TYPE_ADD -> {
                    val docMap = getMapOfSearchBeans(indexBeans) {
                        convertSearchIndexToDoc(it)
                    }
                    val medianResult = proxy.addDocuments(docMap.keys.toMutableList())
                    medianResult?.let { indexError ->
                        processOperationResultList(
                            indexError,
                            docMap,
                            returnResultList,
                            IndexOperationResult.OPERATION_TYPE_ADD
                        )
                    }
                }

                IndexOperationResult.OPERATION_TYPE_UPDATE -> {
                    val docMap = getMapOfSearchBeans(indexBeans) {
                        convertSearchIndexToDoc(it)
                    }
                    val medianResult = proxy.updateDocuments(docMap.keys.toMutableList())
                    medianResult?.let { indexError ->
                        processOperationResultList(
                            indexError,
                            docMap,
                            returnResultList,
                            IndexOperationResult.OPERATION_TYPE_UPDATE
                        )
                    }
                }

                IndexOperationResult.OPERATION_TYPE_DELETE -> {
                    val docMap = getMapOfSearchBeans(indexBeans) {
                        it.identification
                    }
                    val medianResult = proxy.deleteDocuments(docMap.keys.toMutableList())
                    medianResult?.let { indexError ->
                        processOperationResultList(
                            indexError,
                            docMap,
                            returnResultList,
                            IndexOperationResult.OPERATION_TYPE_DELETE
                        )
                    }
                }

                else -> {}
            }
        }
        return returnResultList
    }

    private fun <T> processOperationResultList(
        indexError: IndexError<T>,
        docMap: MutableMap<T, SearchIndexBean>,
        returnResultList: MutableList<IndexOperationResult>,
        operationType: Int
    ) {
        if (!indexError.isSuccess) {
            //处理未成功的列表,对相应的searchBean进行result的失败的标记
            processMapFailedOperationResult(
                indexError,
                docMap,
                returnResultList,
                operationType
            )
        } else {
            //所有的都添加成功了，这里手动设置相应参数
            Log.i(
                TAG,
                "addDocuments add all success, add docs: ${listOf(docMap.keys)}"
            )
            processMapKeySuccessOperationResult(
                docMap,
                returnResultList,
                operationType
            )
        }
    }


    private fun <T> processMapFailedOperationResult(
        indexError: IndexError<T>,
        docMap: MutableMap<T, SearchIndexBean>,
        returnResultList: MutableList<IndexOperationResult>,
        operationType: Int
    ) {
        //遍历没有添加成功的error列表，找到error列表中和docMap中相同的searchIndexBean，标记为失败
        val errorIterator = indexError.errors.iterator()
        while (errorIterator.hasNext()) {
            val error = errorIterator.next()
            val errorCode = error.errorCode
            val docItemIterator = error.errorData.iterator()
            while (docItemIterator.hasNext()) {
                val docItem = docItemIterator.next()
                if (docMap.keys.contains(docItem)) {
                    //找到并remove掉之前出出现错误的请求
                    val searchIndexBean = docMap.remove(docItem)
                    if (searchIndexBean != null) {
                        returnResultList.add(
                            IndexOperationResult(
                                errorCode,
                                searchIndexBean,
                                operationType
                            )
                        )
                    }
                }
            }
        }
        Log.i(
            TAG,
            "processMapFailedOperationResult errors ${indexError.errors}, mediaResult ${
                listOf(returnResultList)
            }, operationType $operationType"
        )
        //docMap中剩余的都是添加成功的结果，这里手动设置相应的设置为成功标记
        if (docMap.isNotEmpty()) {
            processMapKeySuccessOperationResult(
                docMap,
                returnResultList,
                operationType
            )
            Log.i(
                TAG,
                "processMapFailedOperationResult , remainSuccessResult ${listOf(docMap.keys)}"
            )
        }
    }


    private fun <T> processMapKeySuccessOperationResult(
        map: Map<T, SearchIndexBean>,
        returnResultList: MutableList<IndexOperationResult>,
        operationType: Int
    ) {
        map.forEach { mapEntry ->
            returnResultList.add(
                IndexOperationResult(
                    IndexOperationResult.ERROR_CODE_SUCCESS,
                    mapEntry.value,
                    operationType
                )
            )
        }
    }


    private fun <T> getMapOfSearchBeans(
        searchIndexBeanList: List<SearchIndexBean>,
        convertIndexBeanToT: (bean: SearchIndexBean) -> T
    ): MutableMap<T, SearchIndexBean> {
        val docMap = mutableMapOf<T, SearchIndexBean>()
        if (searchIndexBeanList.isNotEmpty()) {
            for (indexBean in searchIndexBeanList) {
                val doc = convertIndexBeanToT(indexBean)
                docMap[doc] = indexBean
            }
        }
        return docMap
    }


    private fun convertSearchIndexToDoc(indexBean: SearchIndexBean): Document<String> {
        return Document<String>()
            .add(IndexProtocol.DOC_IDENTIFICATION, indexBean.identification)
            .add(IndexProtocol.DOC_CHECKSUM, indexBean.checkSum)
            .add(INDEX_FIELD_NAME, indexBean.searchDataBean.fileName)
            .add(INDEX_FIELD_NAME_PINGYIN, indexBean.searchDataBean.fileName)
            .add(INDEX_FIELD_PACKAGE, indexBean.searchDataBean.sourcePackage)
            .add(INDEX_FIELD_SOURCE_NAME, indexBean.searchDataBean.sourceName)
            .add(INDEX_FIELD_SOURCE_NAME_PINGYIN, indexBean.searchDataBean.sourceName)
            .add(INDEX_FIELD_FILE_TIME, indexBean.searchDataBean.fileTime)
            .add(INDEX_FIELD_DETECT_TIME, indexBean.searchDataBean.detectTime)
            .add(INDEX_FIELD_FILE_SIZE, indexBean.searchDataBean.fileSize)
            .add(INDEX_FIELD_SOURCE_TYPE, indexBean.searchDataBean.sourceType)
    }
}