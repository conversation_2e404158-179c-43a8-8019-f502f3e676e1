/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : TypeSortPopupFactoryTest
 ** Description : Type Sort Popup Factory Unit Test
 ** Version     : 1.0
 ** Date        : 2024/04/09 15:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/04/09       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.categorydfm.view

import android.content.Context
import android.content.res.Resources
import android.view.View
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.filemanager.common.sort.SortModeUtils
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test

class TypeSortPopupFactoryTest {

    private lateinit var context: Context
    private lateinit var resource: Resources
    private lateinit var anchorView: View
    private lateinit var itemListener: TypeSortPopupFactory.PopupItemClickListener

    @Before
    fun setup() {
        context = mockk()
        resource = mockk()
        anchorView = mockk()
        itemListener = object : TypeSortPopupFactory.PopupItemClickListener {
            override fun onItemClick(position: Int, item: String) {
            }

            override fun onDismiss() {
            }
        }
        every { context.resources }.returns(resource)
        every { resource.getStringArray(com.filemanager.common.R.array.dfm_doc_type) }.returns(arrayOf("全部", "DOC", "XLS", "PPT", "PDF"))
        mockkStatic(TypeSortPopupFactory::class)
        mockkStatic(SortModeUtils::class)
    }

    @After
    fun teardown() {
        unmockkStatic(TypeSortPopupFactory::class)
        unmockkStatic(SortModeUtils::class)
    }

    @Test
    fun should_call_show_when_showDocumentTypeSortPopup() {
        every { SortModeUtils.getDocSortType(any()) }.returns(0)
        every { TypeSortPopupFactory.showDocumentTypeSortPopup(any(), any(), any()) }.answers { callOriginal() }
        justRun { TypeSortPopupFactory.show(any(), any(), any(), any(), any(), any()) }

        TypeSortPopupFactory.showDocumentTypeSortPopup(context, anchorView, itemListener)
        verify { TypeSortPopupFactory.show(context, anchorView, any(), 0, any(), any()) }
    }

    @Test
    @Ignore("NoClassDefFoundError: com/oplus/view/ViewRootManager")
    fun should_call_showPopupWindow_when_show() {
        every { TypeSortPopupFactory.show(any(), any(), any(), any(), any(), any()) }.answers { callOriginal() }
        val popupWindow = mockk<COUIPopupListWindow>()
        justRun { popupWindow.itemList = any() }
        justRun { popupWindow.show(any()) }
        TypeSortPopupFactory.popupListWindow = popupWindow

        TypeSortPopupFactory.show(context, anchorView, listOf("全部", "DOC"), 0, { parent, view, position, id -> }, {})
        verify { popupWindow.show(anchorView) }
    }

    @Test
    fun should_call_dismiss_when_release() {
        val popupWindow = mockk<COUIPopupListWindow>()
        justRun { popupWindow.dismiss() }
        every { TypeSortPopupFactory.release() }.answers { callOriginal() }

        TypeSortPopupFactory.popupListWindow = null
        TypeSortPopupFactory.release()
        verify(inverse = true) { popupWindow.dismiss() }

        TypeSortPopupFactory.popupListWindow = popupWindow
        every { popupWindow.isShowing }.returns(false)
        TypeSortPopupFactory.release()
        verify(inverse = true) { popupWindow.dismiss() }
        Assert.assertNull(TypeSortPopupFactory.popupListWindow)

        TypeSortPopupFactory.popupListWindow = popupWindow
        every { popupWindow.isShowing }.returns(true)
        TypeSortPopupFactory.release()
        verify { popupWindow.dismiss() }
        Assert.assertNull(TypeSortPopupFactory.popupListWindow)
    }

    @Test
    fun should_return_string_when_getSelectSortTitle() {
        every { TypeSortPopupFactory.getSelectSortTitle(any()) }.answers { callOriginal() }
        every { TypeSortPopupFactory.getSelectSortTitle(any(), any()) }.returns("全部")
        every { SortModeUtils.getDocSortType(any()) }.returns(0)
        val title = TypeSortPopupFactory.getSelectSortTitle(context)
        verify { TypeSortPopupFactory.getSelectSortTitle(context, 0) }
        Assert.assertEquals("全部", title)
    }

    @Test
    fun should_return_string_when_getSelectSortTitle_with_index() {
        every { resource.getString(com.filemanager.common.R.string.filtrate) }.returns("筛选")
        every { TypeSortPopupFactory.getSelectSortTitle(any(), any()) }.answers { callOriginal() }
        var title = TypeSortPopupFactory.getSelectSortTitle(context, 0)
        Assert.assertEquals("筛选", title)

        title = TypeSortPopupFactory.getSelectSortTitle(context, -1)
        Assert.assertEquals("", title)

        title = TypeSortPopupFactory.getSelectSortTitle(context, 10)
        Assert.assertEquals("", title)

        title = TypeSortPopupFactory.getSelectSortTitle(context, 1)
        Assert.assertEquals("DOC", title)
    }
}