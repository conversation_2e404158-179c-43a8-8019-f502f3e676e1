/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - PreviewVideoPlaySuite.kt
 * Description:
 *     The integration widget for play video and control it.
 *
 * Version: 1.0
 * Date: 2024-09-20
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-09-20   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ColorFilter
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PixelFormat
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.TextureView
import android.view.View
import android.widget.FrameLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Guideline
import androidx.core.graphics.toRectF
import com.coui.appcompat.contextutil.COUIContextUtil
import com.filemanager.common.imageloader.glide.RoundRectUtil
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.R

@SuppressLint("ClickableViewAccessibility")
internal class PreviewVideoPlaySuite @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : ConstraintLayout(context, attrs) {

    private companion object {
        private const val TAG = "PreviewVideoPlaySuite"
    }

    val videoSurface: TextureView
    val videoPlayBar: PreviewVideoPlayBar

    private val videoLayout: FrameLayout
    private val videoLeft: Guideline
    private val videoTop: Guideline
    private val videoRight: Guideline
    private val videoBottom: Guideline
    private val surfaceLayout: ConstraintLayout

    private val videoCoverForeground = RoundedCornersForeground(context)

    init {
        LayoutInflater.from(context).inflate(R.layout.widget_preview_video_play_suite, this)
        videoSurface = findViewById(R.id.preview_video_surface_view)
        videoSurface.setOnClickListener(::onClickVideo)
        videoPlayBar = findViewById(R.id.preview_video_play_bar)
        videoLayout = findViewById(R.id.preview_video_play_layout)
        videoLayout.foreground = videoCoverForeground
        videoLayout.setBackgroundColor(Color.BLACK)
        videoLeft = findViewById(R.id.preview_video_widget_left)
        videoTop = findViewById(R.id.preview_video_widget_top)
        videoRight = findViewById(R.id.preview_video_widget_right)
        videoBottom = findViewById(R.id.preview_video_widget_bottom)
        surfaceLayout = findViewById(R.id.preview_video_surface_layout)
        surfaceLayout.setOnClickListener(::onClickVideo)
    }

    fun setVideoSize(videoWidth: Int, videoHeight: Int) {
        Log.d(TAG, "setVideoSize: videoWidth=$videoWidth, videoHeight=$videoHeight")
        if (videoWidth >= videoHeight) {
            val heightScale = videoHeight / videoWidth.toFloat()
            val topPadding = (1f - heightScale) / 2
            videoLeft.setGuidelinePercent(0f)
            videoRight.setGuidelinePercent(1f)
            videoTop.setGuidelinePercent(topPadding)
            videoBottom.setGuidelinePercent(1f - topPadding)
            videoSurface.setConstraintRatio(false, videoWidth, videoHeight)
        } else {
            val widthScale = videoWidth / videoHeight.toFloat()
            val leftPadding = (1f - widthScale) / 2
            videoLeft.setGuidelinePercent(leftPadding)
            videoRight.setGuidelinePercent(1f - leftPadding)
            videoTop.setGuidelinePercent(0f)
            videoBottom.setGuidelinePercent(1f)
            videoSurface.setConstraintRatio(true, videoWidth, videoHeight)
        }
        videoCoverForeground.fullCover = false
    }

    private fun View.setConstraintRatio(widthRatio: Boolean, width: Int, height: Int) {
        val lp = layoutParams as? ConstraintLayout.LayoutParams ?: return
        lp.dimensionRatio = if (widthRatio) "W,$width:$height" else "H,$width:$height"
        layoutParams = lp
    }

    private fun onClickVideo(view: View?) {
        if (videoPlayBar.visibility == VISIBLE) {
            Log.d(TAG, "onClickVideo: hide VideoPlayBar")
            videoPlayBar.visibility = INVISIBLE
        } else {
            Log.d(TAG, "onClickVideo: show VideoPlayBar")
            videoPlayBar.visibility = VISIBLE
        }
    }

    private class RoundedCornersForeground(context: Context) : Drawable() {

        private val roundRadius = context.resources.getDimension(R.dimen.preview_image_round_radius)
        private val backgroundColor = COUIContextUtil.getAttrColor(
            context,
            com.support.appcompat.R.attr.couiColorBackgroundWithCard
        )
        private val cornerPaint = Paint().apply {
            style = Paint.Style.FILL
            color = backgroundColor
            isAntiAlias = true
        }
        private val borderPaint = Paint().apply {
            style = Paint.Style.STROKE
            strokeWidth = context.resources.getDimension(R.dimen.preview_image_stroke_width)
            color = context.getColor(R.color.preview_image_border_color)
            isAntiAlias = true
        }

        var fullCover: Boolean = true

        override fun draw(canvas: Canvas) {
            if (fullCover) {
                canvas.drawColor(backgroundColor)
                return
            }
            val drawableBounds = bounds.toRectF()
            val strokeWidth = borderPaint.strokeWidth
            val rectWidth = drawableBounds.width()
            val rectHeight = drawableBounds.height()
            val centerX = drawableBounds.centerX()
            val centerY = drawableBounds.centerY()
            val borderPath = getRoundRectPath(drawableBounds)
            val cornerPath = Path(borderPath).apply { fillType = Path.FillType.INVERSE_WINDING }
            // Draw corner
            val saveCorner = canvas.save()
            val cornerScaleX = (rectWidth - (strokeWidth * 2)) / rectWidth
            val cornerScaleY = (rectHeight - (strokeWidth * 2)) / rectHeight
            canvas.scale(cornerScaleX, cornerScaleY, centerX, centerY)
            canvas.drawPath(cornerPath, cornerPaint)
            canvas.restoreToCount(saveCorner)
            // Draw border
            val saveBorder = canvas.save()
            val borderScaleX = (rectWidth - (strokeWidth / 2)) / rectWidth
            val borderScaleY = (rectHeight - (strokeWidth / 2)) / rectHeight
            canvas.scale(borderScaleX, borderScaleY, centerX, centerY)
            canvas.drawPath(borderPath, borderPaint)
            canvas.restoreToCount(saveBorder)
        }

        override fun setAlpha(alpha: Int) {
            // Do nothing
        }

        override fun setColorFilter(colorFilter: ColorFilter?) {
            // Do nothing
        }

        @Deprecated("Deprecated in Java")
        override fun getOpacity(): Int = PixelFormat.TRANSLUCENT

        private fun getRoundRectPath(drawableBounds: RectF): Path =
            RoundRectUtil.getPath(
                drawableBounds.left,
                drawableBounds.top,
                drawableBounds.right,
                drawableBounds.bottom,
                roundRadius,
                true,
                true,
                true,
                true
            )
    }
}