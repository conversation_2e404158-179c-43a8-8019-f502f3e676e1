/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.superapp
 * * Version     : 1.0
 * * Date        : 2020/4/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.categorycompress.ui

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.DragEvent
import android.view.Menu
import android.view.MenuItem
import android.view.View
import com.filemanager.categorycompress.R
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.Constants
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.dragselection.DragDropInterface
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.StatisticsUtils
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.encrypt.EncryptActivity
import com.oplus.labelmanager.AddFileLabelController
import com.oplus.selectdir.SelectPathController

class CategoryCompressActivity : EncryptActivity(), NavigationInterface, NavigationBarView.OnItemSelectedListener,
    TransformNextFragmentListener, BaseVMActivity.PermissonCallBack, DragDropInterface, IDraggingActionOperate {
    companion object {
        private const val TAG = "CategoryCompressActivity"
        private const val TAG_COMPRESS_PARENT_FRAGMENT = "doc_parent_fragment"
    }

    private var mParentFragment: CompressParentFragment? = null

    private var mActionActivityResultListener: ActionActivityResultListener? = null
    private val mNavigationController by lazy { NavigationController(lifecycle, id = R.id.navigation_tool) }
    private val mSelectPathController by lazy { SelectPathController(lifecycle) }
    private val mAddFileLabelController by lazy { AddFileLabelController(lifecycle) }

    override fun getLayoutResId(): Int {
        return R.layout.category_compress_activity
    }

    @SuppressLint("RestrictedApi")
    override fun initView() {
        registerVmChangedReceiver(null)
        mParentFragment =
            (supportFragmentManager.findFragmentByTag(TAG_COMPRESS_PARENT_FRAGMENT) as? CompressParentFragment) ?: CompressParentFragment()
        supportFragmentManager.beginTransaction().replace(R.id.fragment_container_view, mParentFragment!!, TAG_COMPRESS_PARENT_FRAGMENT).commit()
//        initIntentData(mParentFragment!!)
    }

    override fun initData() {
        if (IntentUtils.getString(intent, Constants.KEY_SOURCE_APP_NAME).isNullOrEmpty().not()) {
            intent.putExtra(Constants.TEMP_SORT_TYPE, SortHelper.FILE_TIME_REVERSE_ORDER)
            intent.putExtra(Constants.TEMP_SORT_DESC, 0)
            if (mParentFragment?.arguments == null) {
                mParentFragment?.arguments = Bundle()
            }
            mParentFragment?.arguments?.putInt(Constants.TEMP_SORT_TYPE, IntentUtils.getInt(intent, Constants.TEMP_SORT_TYPE, -1))
            mParentFragment?.arguments?.putInt(Constants.TEMP_SORT_DESC, IntentUtils.getInt(intent, Constants.TEMP_SORT_DESC, -1))
            StatisticsUtils.onCommon(MyApplication.appContext, StatisticsUtils.RECENT_FILE_CARD_CLICK)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        mParentFragment?.onCreateOptionsMenu(menu, menuInflater)
        return true
    }

    override fun startObserve() {
    }

    override fun onPermissionSuccess(isInstallPermission: Boolean?) {
        super.onPermissionSuccess(isInstallPermission)
        refreshCurrentPage()
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        mParentFragment?.refreshCurrentFragment()
    }

    override fun showNavigation() {
        mNavigationController.showNavigation(this)
        updateNavigationToolPadding()
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean, hasAndroidData: Boolean) {
        mNavigationController.setNavigateItemAble(isEnable, mHasDrm)
    }

    override fun hideNavigation() {
        mNavigationController.hideNavigation(this)
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
        mActionActivityResultListener = actionActivityResultListener
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return mParentFragment?.onOptionsItemSelected(item) ?: super.onOptionsItemSelected(item)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mActionActivityResultListener?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        return mParentFragment?.onNavigationItemSelected(item) ?: false
    }

    override fun onBackPressed() {
        if (mParentFragment?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
        }
    }

    override fun backtoTop() {
        super.backtoTop()
        mParentFragment?.backToTop()
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mParentFragment?.onUIConfigChanged(configList)
        mSelectPathController.updateDialogHeightIfNeed(supportFragmentManager)
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
        mSelectPathController.onDestroy()
    }

    override fun transformToNextFragment(path: String?) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        mAddFileLabelController.showAddLabelFragment(supportFragmentManager, fileList)
    }

    override fun onUpdatedLabel() {
        mParentFragment?.updatedLabel()
    }

    override fun hasShowPanel(): Boolean {
        return mSelectPathController.hasShowPanel() || mAddFileLabelController.hasShowPanel(supportFragmentManager)
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        mParentFragment?.mViewModel?.let {
            mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, code)
        }
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        mSelectPathController.onDestroy()
        mParentFragment?.getCurrentFragment()?.fromSelectPathResult(code, paths)
    }

    override fun onRefreshData() {
        mParentFragment?.onResumeLoadData()
    }

    override fun handleNoStoragePermission() {
        mParentFragment?.setPermissionEmptyVisible(View.VISIBLE)
    }

    override fun updateNavigationToolPadding() {
        mNavigationController.updateNavigationToolPadding(navPaddingBottom)
    }

    override fun handleDragEvent(event: DragEvent?): Boolean? {
        return event?.let { mParentFragment?.handleDragScroll(it) }
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        return mParentFragment?.getSelectedItemView()
    }

    override fun setNavigateItemAble() {
        mParentFragment?.setNavigateItemAble()
    }

    override fun getDragCurrentPath(): String? {
        return null
    }
}