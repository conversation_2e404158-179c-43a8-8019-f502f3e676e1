<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:importantForAccessibility="no"
    android:background="?attr/couiColorBackgroundWithCard">

    <com.filemanager.common.filepreview.PreviewParentChildLayout
        android:id="@+id/parentChildLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="?attr/couiColorBackgroundWithCard"
        app:elevation="0dp">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            style="@style/COUIToolBarInAppBarLayoutStyle"
            app:supportTitleTextAppearance="@style/textAppearanceSecondTitle"
            />
        <View android:id="@+id/divider_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/toolbar_divider_height"
            android:background="?attr/couiColorDivider"
            android:forceDarkAllowed="false"
            android:visibility="gone"/>
    </com.google.android.material.appbar.AppBarLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>