package com.filemanager.common.back

import android.window.OnBackInvokedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.utils.Log
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.lang.reflect.Field
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * PredictiveBackActivityMonitor的单元测试类
 * 用于测试预测性返回Activity生命周期的监控功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class PredictiveBackActivityMonitorTest {

    // 测试用的Activity实例
    private lateinit var activity: AppCompatActivity
    // 模拟的返回回调接口
    private lateinit var backInvoker: OnBackInvokedCallback
    // 被测试的监控器实例
    private lateinit var monitor: PredictiveBackActivityMonitor

    /**
     * 测试前的初始化方法
     * 创建测试所需的Activity和模拟对象
     */
    @Before
    fun setUp() {
        // 使用Robolectric创建模拟Activity
        activity = Robolectric.buildActivity(AppCompatActivity::class.java).get()
        // 创建模拟的返回回调
        backInvoker = mockk(relaxed = true)
        // 创建被测试的监控器实例
        monitor = PredictiveBackActivityMonitor("TestActivity", backInvoker)
    }

    /**
     * 测试后的清理方法
     * 清除所有模拟对象并重置Activity栈
     */
    @After
    fun tearDown() {
        // 清除所有MockK创建的模拟对象
        clearAllMocks()
        // 重置Activity栈
        resetActivityStack()
        // 取消所有MockK的模拟
        unmockkAll()
    }

    /**
     * 重置Activity栈的私有方法
     * 通过反射清空静态的activityStack
     */
    private fun resetActivityStack() {
        // 获取activityStack的反射字段
        val field: Field = PredictiveBackActivityMonitor::class.java.getDeclaredField("activityStack")
        // 设置字段可访问
        field.isAccessible = true
        // 获取栈实例并清空
        val stack = field.get(null) as java.util.Stack<*>
        stack.clear()
    }

    /**
     * 测试getActivityTag方法
     * 当Activity类名没有"Activity"后缀时应该自动添加
     */
    @Test
    fun `getActivityTag should append Activity suffix if not present`() {
        // 创建一个没有"Activity"后缀的Activity
        val activityWithoutSuffix = Robolectric.buildActivity(AppCompatActivity::class.java).get()
        // 使用反射调用私有方法getActivityTag
        val method = PredictiveBackActivityMonitor.Companion::class.java.getDeclaredMethod("getActivityTag", AppCompatActivity::class.java)
        method.isAccessible = true
        // 调用方法并获取结果
        val tag = method.invoke(PredictiveBackActivityMonitor.Companion, activityWithoutSuffix) as String
        // 验证结果应该等于原始类名(因为AppCompatActivity本身没有Activity后缀)
        assertEquals(activityWithoutSuffix::class.java.simpleName, tag)
    }

    /**
     * 测试getActivityTag方法
     * 当Activity类名已经有"Activity"后缀时不应该重复添加
     */
    @Test
    fun `getActivityTag should not append Activity suffix if already present`() {
        // 创建一个有"Activity"后缀的测试Activity
        val activityWithSuffix = Robolectric.buildActivity(TestActivity::class.java).get()
        // 使用反射调用私有方法getActivityTag
        val method = PredictiveBackActivityMonitor.Companion::class.java.getDeclaredMethod("getActivityTag", AppCompatActivity::class.java)
        method.isAccessible = true
        // 调用方法并获取结果
        val tag = method.invoke(PredictiveBackActivityMonitor.Companion, activityWithSuffix) as String
        // 验证结果应该保持原样
        assertEquals("TestActivity", tag)
    }

    /**
     * 测试isActivityTag方法
     * 验证字符串是否以"Activity"结尾的判断
     */
    @Test
    fun `isActivityTag should return true when ends with Activity`() {
        // 验证以"Activity"结尾的字符串返回true
        assertTrue(PredictiveBackActivityMonitor.isActivityTag("MainActivity"))
        // 验证不以"Activity"结尾的字符串返回false
        assertFalse(PredictiveBackActivityMonitor.isActivityTag("MainFragment"))
    }

    /**
     * 测试getCurrentActivity方法
     * 当Activity栈为空时应该返回null
     */
    @Test
    fun `getCurrentActivity should return null when stack is empty`() {
        // 重置Activity栈确保为空
        resetActivityStack()
        // 获取当前Activity应该为null
        val current = PredictiveBackActivityMonitor.getCurrentActivity()
        assertNull(current)
    }

    /**
     * 测试getCurrentActivity方法
     * 当Activity栈不为空时应该返回栈顶元素
     */
    @Test
    fun `getCurrentActivity should return top of stack when not empty`() {
        // 重置Activity栈
        resetActivityStack()
        // 创建两个监控器实例
        val monitor1 = PredictiveBackActivityMonitor("FirstActivity", backInvoker)
        val monitor2 = PredictiveBackActivityMonitor("SecondActivity", backInvoker)

        // 模拟两个Activity的创建生命周期
        monitor1.onCreate(activity)
        monitor2.onCreate(activity)

        // 获取当前Activity应该是最后添加的monitor2
        val current = PredictiveBackActivityMonitor.getCurrentActivity()
        assertEquals(monitor2, current)
    }

    /**
     * 测试check方法
     * 当viewModel为null时不应该抛出异常
     */
    @Test
    fun `check should do nothing when viewModel is null`() {
        // 使用反射将viewModel设置为null
        monitor.javaClass.getDeclaredField("viewModel").apply {
            isAccessible = true
            set(monitor, null)
        }

        // 调用check方法不应该抛出异常
        monitor.check(mockk(relaxed = true))
    }

    /**
     * 获取私有activityStack的辅助方法
     * 用于测试内部状态
     */
    private fun getPrivateActivityStack(): java.util.Stack<PredictiveBackActivityMonitor> {
        // 获取activityStack的反射字段
        val field: Field = PredictiveBackActivityMonitor::class.java.getDeclaredField("activityStack")
        field.isAccessible = true
        // 返回栈实例
        return field.get(null) as java.util.Stack<PredictiveBackActivityMonitor>
    }

    /**
     * 用于测试的内部Activity类
     * 类名包含"Activity"后缀
     */
    class TestActivity : AppCompatActivity()
}