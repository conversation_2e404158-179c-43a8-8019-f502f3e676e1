/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : OpenDeeplinkActivity
 * * Description : deeplink 跳转分发的Activity
 * * Version     : 1.0
 * * Date        : 2025/01/06
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.activity

import android.content.Intent
import android.os.Bundle
import android.os.Environment
import androidx.appcompat.app.AppCompatActivity
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.DEEPLINK_REMOTE_PC
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.ArrayUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PathUtils
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.dfm.DFMManager
import com.oplus.filemanager.interfaze.categoryremotedevice.ICategoryRemoteDeviceApi
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import java.io.File

class OpenDeeplinkActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "OpenDeeplinkActivity"

        /**
         * 跳转到DownloadActivity
         * 传递参数是key为 P_task_id，value为可以转化为int值的String值
         */
        private const val DEEPLINK_DOWNLOAD = "filemanager://deeplink.remotedevice.download"
    }

    private val internalPath: String? by lazy {
        VolumeEnvironment.getInternalSdPath(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        dispatchJumpActivity()
    }

    private fun dispatchJumpActivity() {
        val intent = intent ?: return
        if (Intent.ACTION_VIEW != intent.action) {
            Log.w(TAG, "dispatchJumpActivity action must be ACTION_VIEW")
            return
        }
        val uri = intent.data.toString()
        val data = intent.extras ?: return
        Log.log(TAG, data)
        if (DEEPLINK_DOWNLOAD == uri) {
            jumpDownloadActivity(intent)
        } else if (DEEPLINK_REMOTE_PC == uri) {
            jumpRemotePCControlActivity(intent)
        }
        finish()
    }

    /**
     * 跳转到远程电脑文件的下载界面
     */
    private fun jumpDownloadActivity(intent: Intent) {
        val taskIdStr = IntentUtils.getString(intent, KtConstants.P_TASK_ID) ?: ""
        val from = IntentUtils.getString(intent, KtConstants.FROM) ?: ""
        var taskId = -1
        try {
            taskId = taskIdStr.toInt()
        } catch (e: NumberFormatException) {
            Log.e(TAG, "jumpDownloadActivity format error", e)
        }
        Log.d(TAG, "jumpDownloadActivity from:$from taskId:$taskId")
        val api = Injector.injectFactory<ICategoryRemoteDeviceApi>()
        api?.jumpDownloadActivity(this, taskId)
        staticsLaunchEntry(Constants.PAGE_REMOTE_FILE_DOWNLOAD)
    }

    /**
     * 跳转到FileBrowserActivity
     */
    private fun jumpFileBrowserActivity(filePath: String) {
        Log.d(TAG, "jumpFileBrowserActivity filePath:$filePath")
        val api = Injector.injectFactory<IFileBrowser>()
        api?.toFileBrowserActivity(this, filePath)
        staticsLaunchEntry(Constants.PAGE_FILE_BROWSER)
    }

    /**
     * 跳转到远程电脑文件 界面
     */
    private fun jumpRemotePCControlActivity(intent: Intent) {
        val filePath = IntentUtils.getString(intent, KtConstants.FILE_PATH) ?: ""
        val isDfm = intent.getBooleanExtra(KtConstants.IS_DFM, false)
        val from = IntentUtils.getString(intent, KtConstants.FROM) ?: ""
        Log.d(TAG, "jumpRemotePCControlActivity from:$from filePath:$filePath")
        val dir = File(filePath).parent
        if (dir == internalPath) {
            jumpFileBrowserActivity(dir)
            return
        } else if (isDfm) {
            val dfsRootPath = DFMManager.getDFSMountPath()
            val destPath = dfsRootPath + File.separator + Environment.DIRECTORY_DOWNLOADS
            val api = Injector.injectFactory<IFileBrowser>()
            api?.startFileBrowserActivity(this, destPath)
            return
        }
        val relativePath = PathUtils.getRelativePath(this, dir) + File.separator
        val superApp = Injector.injectFactory<ISuperApp>() ?: return
        val data = superApp.getCategoryItems(this).find { it.itemType == CategoryHelper.CATEGORY_REMOTE_PC_CONTROL } ?: return
        val defaultList = data.fileList
        data.externalPath = filePath
        data.fileList = ArrayUtils.add(defaultList, relativePath)
        if (FeatureCompat.isSmallScreenPhone) {
            superApp.startSuperApp(this, data)
            staticsLaunchEntry(Constants.PAGE_SUPER_REMOTE_FILE)
        } else {
            jumpMainActivity(filePath)
        }
    }


    private fun jumpMainActivity(path: String) {
        val intent = Intent()
        intent.setAction(Constants.ACTION_MAIN_CATEGORY_SUPER)
        intent.putExtra(KtConstants.P_CATEGORY_TYPE, CategoryHelper.CATEGORY_REMOTE_PC_CONTROL)
        intent.putExtra(KtConstants.FILE_PATH, path)
        intent.setPackage(this.packageName)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        startActivity(intent)
        staticsLaunchEntry(Constants.PAGE_MAIN)
    }

    private fun staticsLaunchEntry(landingPage: String) {
        StatisticsUtils.statisticsEntryLaunch(this, this.packageName, "", landingPage)
    }
}