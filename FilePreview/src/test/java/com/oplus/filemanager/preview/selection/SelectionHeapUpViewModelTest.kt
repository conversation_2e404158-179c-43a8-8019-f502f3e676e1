package com.oplus.filemanager.preview.selection

import android.content.Context
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.Observer
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat
import com.oplus.filemanager.preview.selection.data.SelectionItem
import com.oplus.filemanager.preview.selection.utils.SelectionConstants
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import com.oplus.filemanager.preview.utils.getMediaType
import io.mockk.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * SelectionHeapUpViewModel的单元测试类
 * 用于测试SelectionHeapUpViewModel的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
@OptIn(ExperimentalCoroutinesApi::class)
class SelectionHeapUpViewModelTest {

    // 使用InstantTaskExecutorRule确保LiveData的同步执行
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private lateinit var viewModel: SelectionHeapUpViewModel
    private lateinit var context: Context
    // 使用StandardTestDispatcher作为测试协程调度器
    private val testDispatcher = StandardTestDispatcher()
    
    /**
     * 测试前的初始化方法
     * 设置主调度器为测试调度器，并创建ViewModel和mock的Context
     */
    @Before
    fun setUp() {
        Dispatchers.setMain(testDispatcher)
        viewModel = SelectionHeapUpViewModel()
        context = mockk(relaxed = true)
    }
    
    /**
     * 测试后的清理方法
     * 重置主调度器
     */
    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }
    
    /**
     * 测试releaseSelectionItemsLauncher方法
     * 验证是否能正确移除观察者
     */
    @Test
    fun `releaseSelectionItemsLauncher should remove observer`() {
        // Given - 准备一个mock的观察者
        val observer = mockk<Observer<List<SelectionItem>>>()
        every { observer.onChanged(any()) } just runs
        
        // When - 调用移除观察者方法
        viewModel.releaseSelectionItemsLauncher(observer)
        
        // Then - 没有抛出异常即表示成功
    }
    
    /**
     * 测试launchSelectionStat方法
     * 验证是否能正确观察并通知初始值
     */
    @Test
    fun `launchSelectionStat should observe and notify initial value`() {
        // Given - 准备生命周期所有者和mock的观察者
        val lifecycleOwner = createLifecycleOwner()
        val observer = mockk<Observer<SelectionFilesStat?>>()
        every { observer.onChanged(any()) } just runs
        
        // When - 调用启动统计观察方法
        viewModel.launchSelectionStat(lifecycleOwner, observer)
        
        // Then - 验证观察者被调用一次且传入null值
        verify(exactly = 1) { observer.onChanged(null) }
    }
    
    /**
     * 测试updateSelectedFiles方法
     * 验证当传入相同选择时不会更新
     */
    @Test
    fun `updateSelectedFiles with same selection should not update`() {
        // Given - 准备两个相同内容的文件列表
        val fileBean1 = BaseFileBean().apply { mData = "/path/file1" }
        val initialSelection = listOf(fileBean1)
        val sameSelection = listOf(BaseFileBean().apply { mData = "/path/file1" })
        
        // 第一次调用设置当前选择
        viewModel.updateSelectedFiles(context, initialSelection)
        
        // When - 再次调用相同数据
        viewModel.updateSelectedFiles(context, sameSelection)
        
        // Then - 验证只更新了一次(大小仍为1)
        assertEquals(1, viewModel.getSelectionItemsForTest()?.size)
    }
    
    /**
     * 测试updateSelectedFiles方法
     * 验证是否能正确限制最大显示数量
     */
    @Test
    fun `updateSelectedFiles should limit items to MAX_HEAP_UP_COUNT`() {
        // Given - 准备超过最大限制数量的文件列表
        val files = (1..SelectionConstants.MAX_HEAP_UP_COUNT + 5).map { i ->
            BaseFileBean().apply { mData = "/path/file$i" }
        }
        
        // When - 调用更新选择方法
        viewModel.updateSelectedFiles(context, files)
        
        // Then - 验证结果数量不超过最大限制
        assertEquals(SelectionConstants.MAX_HEAP_UP_COUNT, viewModel.getSelectionItemsForTest()?.size)
    }
    
    /**
     * 测试updateSelectedFiles方法
     * 验证是否能跳过mData为null或空的项
     */
    @Test
    fun `updateSelectedFiles should skip items with null or empty mData`() {
        // Given - 准备包含无效数据的文件列表
        val validFile = BaseFileBean().apply { mData = "/valid/path" }
        val nullDataFile = BaseFileBean().apply { mData = null }
        val emptyDataFile = BaseFileBean().apply { mData = "" }
        val selection = listOf(nullDataFile, emptyDataFile, validFile)
        
        // When - 调用更新选择方法
        viewModel.updateSelectedFiles(context, selection)
        
        // Then - 验证只有有效数据被保留
        assertEquals(1, viewModel.getSelectionItemsForTest()?.size)
        assertEquals(validFile.mData, viewModel.getSelectionItemsForTest()?.firstOrNull()?.fileBean?.mData)
    }
    
    /**
     * 测试辅助方法 - 获取selectionItems的私有字段值
     */
    private fun SelectionHeapUpViewModel.getSelectionItemsForTest(): List<SelectionItem>? {
        val field = this::class.java.getDeclaredField("selectionItems")
        field.isAccessible = true
        @Suppress("UNCHECKED_CAST")
        return (field.get(this) as androidx.lifecycle.MutableLiveData<List<SelectionItem>>).value
    }
    
    /**
     * 测试辅助方法 - 获取selectionStat的私有字段值
     */
    private fun SelectionHeapUpViewModel.getSelectionStatForTest(): SelectionFilesStat? {
        val field = this::class.java.getDeclaredField("selectionStat")
        field.isAccessible = true
        @Suppress("UNCHECKED_CAST")
        return (field.get(this) as androidx.lifecycle.MutableLiveData<SelectionFilesStat?>).value
    }
    
    /**
     * 测试辅助方法 - 创建mock的生命周期所有者
     */
    private fun createLifecycleOwner(): LifecycleOwner {
        val lifecycleOwner = mockk<LifecycleOwner>()
        val lifecycle = LifecycleRegistry(lifecycleOwner)
        lifecycle.handleLifecycleEvent(Lifecycle.Event.ON_RESUME)
        every { lifecycleOwner.lifecycle } returns lifecycle
        return lifecycleOwner
    }
}