/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.audio
 ** Version: 1.0
 ** Date: 2021/7/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.audio

import android.content.Context
import android.graphics.Bitmap
import androidx.annotation.Keep
import com.bumptech.glide.Glide
import com.bumptech.glide.load.Transformation
import com.bumptech.glide.load.engine.Resource
import com.bumptech.glide.load.resource.bitmap.BitmapResource
import com.bumptech.glide.util.Preconditions
import com.filemanager.thumbnail.utils.Log
import java.security.MessageDigest

@Keep
class AudioThumbnailTransformation(wrapped: Transformation<Bitmap>) :
    Transformation<AudioThumbnailResult> {

    companion object {
        const val TAG = "AudioThumbnailTransformation"
    }

    private val wrapped: Transformation<Bitmap> = Preconditions.checkNotNull(wrapped)

    override fun transform(
        context: Context,
        resource: Resource<AudioThumbnailResult>,
        outWidth: Int,
        outHeight: Int
    ): Resource<AudioThumbnailResult> {
        val audioThumbnailResult = resource.get()

        // The drawable needs to be initialized with the correct width and height in order for a view
        // displaying it to end up with the right dimensions. Since our transformations may arbitrarily
        // modify the dimensions of our GIF, here we create a stand in for a frame and pass it to the
        // transformation to see what the final transformed dimensions will be so that our drawable can
        // report the correct intrinsic width and height.

        // The drawable needs to be initialized with the correct width and height in order for a view
        // displaying it to end up with the right dimensions. Since our transformations may arbitrarily
        // modify the dimensions of our GIF, here we create a stand in for a frame and pass it to the
        // transformation to see what the final transformed dimensions will be so that our drawable can
        // report the correct intrinsic width and height.
        return if (audioThumbnailResult.mBitmap != null) {
            val bitmapPool = Glide.get(context).bitmapPool
            val bitmapResource = BitmapResource(audioThumbnailResult.mBitmap!!, bitmapPool)
            val transformed = wrapped.transform(context, bitmapResource, outWidth, outHeight)
            if (bitmapResource != transformed) {
                bitmapResource.recycle()
            }

            Log.d(TAG, "transform result is ${transformed.get()}")
            AudioThumbnailResultResource(AudioThumbnailResult(transformed.get()))
        } else {
            resource
        }
    }

    override fun equals(other: Any?): Boolean {
        if (other is AudioThumbnailTransformation) {
            return wrapped == other.wrapped
        }
        return false
    }

    override fun hashCode(): Int {
        return wrapped.hashCode()
    }

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {
        wrapped.updateDiskCacheKey(messageDigest)
    }
}