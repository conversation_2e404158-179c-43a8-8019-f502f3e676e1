<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="ThemeTransparent" parent="Theme.COUI.Blue">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="AppNoTitleThemeTranslucent" parent="AppNoTitleTheme">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="windowPreviewType">0</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="AppNoTitleThemeTranslucent2" parent="AppNoTitleThemeTranslucent">
        <item name="android:windowIsFloating">true</item>
    </style>

</resources>