package com.coloros.filemanager.filerefactor

import android.content.Context
import android.provider.MediaStore
import androidx.core.content.ContextCompat
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.MediaFileCompat
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.KtThumbnailHelper
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Test

class MimeTypeHelperTest {
    @Test
    fun should_return_type_when_getMimeTypeFromPath_with_path() {
        Assert.assertEquals("*/*", MimeTypeHelper.getMimeTypeFromPath(null))
    }

    @Test
    fun should_return_type_when_getTypeFromPath_with_path() {
        Assert.assertEquals(MimeTypeHelper.UNKNOWN_TYPE, MimeTypeHelper.getTypeFromPath(null))
    }

    @Test
    fun should_return_type_when_getTypeByMimeType_with_mimeType() {
        var mimeType = "image/*"
        Assert.assertEquals(MimeTypeHelper.IMAGE_TYPE, MimeTypeHelper.getTypeByMimeType(mimeType))
        mimeType = "video/*"
        Assert.assertEquals(MimeTypeHelper.VIDEO_TYPE, MimeTypeHelper.getTypeByMimeType(mimeType))
        mimeType = "audio/*"
        Assert.assertEquals(MimeTypeHelper.AUDIO_TYPE, MimeTypeHelper.getTypeByMimeType(mimeType))
        mimeType = ""
        Assert.assertEquals(MimeTypeHelper.UNKNOWN_TYPE, MimeTypeHelper.getTypeByMimeType(mimeType))
        mimeType = "photoshop"
        Assert.assertEquals(MimeTypeHelper.PSD_TYPE, MimeTypeHelper.getTypeByMimeType(mimeType))
    }

    @Test
    fun should_verify_isAudioType() {
        Assert.assertTrue(MimeTypeHelper.isAudioType(MimeTypeHelper.AUDIO_TYPE))
    }

    @Test
    fun should_verify_isDocType() {
        val mimeTypes = listOf(
            MimeTypeHelper.TXT_TYPE, MimeTypeHelper.DOC_TYPE,
            MimeTypeHelper.DOCX_TYPE, MimeTypeHelper.XLS_TYPE, MimeTypeHelper.PPTX_TYPE,
            MimeTypeHelper.PDF_TYPE, MimeTypeHelper.OFD_TYPE, MimeTypeHelper.XLSX_TYPE, MimeTypeHelper.PPT_TYPE
        )
        for (mimeType in mimeTypes) {
            Assert.assertTrue(MimeTypeHelper.isDocType(mimeType))
        }
        Assert.assertFalse(MimeTypeHelper.isDocType(MimeTypeHelper.UNKNOWN_TYPE))
    }

    @Test
    fun should_verify_isCompressType() {
        val mimeTypes = arrayOf(
            MimeTypeHelper.COMPRESSED_TYPE, MimeTypeHelper.ZIP_TYPE,
            MimeTypeHelper.RAR_TYPE, MimeTypeHelper.JAR_TYPE, MimeTypeHelper.P7ZIP_TYPE
        )
        for (mimeType in mimeTypes) {
            Assert.assertTrue(MimeTypeHelper.isCompressType(mimeType))
        }
    }

    @Test
    fun should_return_mediaType_when_getMediaTypeByType_with_originType() {
        var originType = MimeTypeHelper.IMAGE_TYPE
        Assert.assertEquals(MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE, MimeTypeHelper.getMediaTypeByType(originType))
        originType = MimeTypeHelper.VIDEO_TYPE
        Assert.assertEquals(MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO, MimeTypeHelper.getMediaTypeByType(originType))
        originType = MimeTypeHelper.AUDIO_TYPE
        Assert.assertEquals(MediaStore.Files.FileColumns.MEDIA_TYPE_AUDIO, MimeTypeHelper.getMediaTypeByType(originType))

        val mimeTypes = listOf(
            MimeTypeHelper.PDF_TYPE, MimeTypeHelper.DOC_TYPE,
            MimeTypeHelper.DOCX_TYPE, MimeTypeHelper.XLS_TYPE,
            MimeTypeHelper.XLSX_TYPE, MimeTypeHelper.PPT_TYPE,
            MimeTypeHelper.PPTX_TYPE, MimeTypeHelper.TXT_TYPE,
            MimeTypeHelper.HTML_TYPE, MimeTypeHelper.OFD_TYPE
        )
        for (mimeType in mimeTypes) {
            Assert.assertEquals(MediaFileCompat.MEDIA_TYPE_DOC, MimeTypeHelper.getMediaTypeByType(mimeType))
        }

        originType = MimeTypeHelper.APPLICATION_TYPE
        Assert.assertEquals(MediaFileCompat.MEDIA_TYPE_APK, MimeTypeHelper.getMediaTypeByType(originType))
        originType = MimeTypeHelper.COMPRESSED_TYPE
        Assert.assertEquals(MediaFileCompat.MEDIA_TYPE_COMPRESS, MimeTypeHelper.getMediaTypeByType(originType))
        originType = -1
        Assert.assertEquals(MediaStore.Files.FileColumns.MEDIA_TYPE_NONE, MimeTypeHelper.getMediaTypeByType(originType))
    }

    @Test
    fun should_return_type_when_getMimeTypeByFileType_with_mimeType() {
        var mimeType = MimeTypeHelper.IMAGE_TYPE
        Assert.assertEquals("image/*", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.VIDEO_TYPE
        Assert.assertEquals("video/*", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.AUDIO_TYPE
        Assert.assertEquals("audio/*", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.PDF_TYPE
        Assert.assertEquals("application/pdf", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.OFD_TYPE
        Assert.assertEquals("application/ofd", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.DOC_TYPE
        Assert.assertEquals("application/vnd.ms-word", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.DOCX_TYPE
        Assert.assertEquals("application/vnd.ms-word", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.XLS_TYPE
        Assert.assertEquals("application/vnd.ms-excel", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.XLSX_TYPE
        Assert.assertEquals("application/vnd.ms-excel", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.PPT_TYPE
        Assert.assertEquals("application/vnd.ms-powerpoint", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.PPTX_TYPE
        Assert.assertEquals("application/vnd.ms-powerpoint", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.TXT_TYPE
        Assert.assertEquals("text/plain", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.CHM_TYPE
        Assert.assertEquals("text/plain", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.EBK_TYPE
        Assert.assertEquals("text/plain", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.LRC_TYPE
        Assert.assertEquals("application/lrc", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.EPUB_TYPE
        Assert.assertEquals("application/lrc", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.APPLICATION_TYPE
        Assert.assertEquals("application/vnd.android.package-archive", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.HTML_TYPE
        Assert.assertEquals("text/html", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.VCF_TYPE
        Assert.assertEquals("text/x-vcard", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.P12_TYPE
        Assert.assertEquals("application/x-pkcs12", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.CER_TYPE
        Assert.assertEquals("application/pkix-cert", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.UNKNOWN_TYPE
        Assert.assertEquals("*/*", MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = -1
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_UNKNOWN, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.DWT_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_DWT, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.PSD_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_PSD, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.AI_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_AI, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.VSDX_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_VISIO, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.VSDM_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_VISIO, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.VSTX_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_VISIO, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.VSTM_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_VISIO, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.VSSX_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_VISIO, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.VSSM_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_VISIO, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.VSD_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_VISIO, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.VSS_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_VISIO, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.VST_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_VISIO, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.VDW_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_VISIO, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.KEYNOTE_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_KEYNOTE, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.PAGES_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_PAGES, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.NUMBERS_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_NUMBERS, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.MARKDOWN_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_MARKDOWN, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.DWG_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_DWG, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.DXF_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_DXF, MimeTypeHelper.getMimeTypeByFileType(mimeType))
        mimeType = MimeTypeHelper.XMIND_TYPE
        Assert.assertEquals(MimeTypeHelper.MimeType.MIMETYPE_XMIND, MimeTypeHelper.getMimeTypeByFileType(mimeType))
    }

    @Test
    fun should_return_type_when_getCompressedTypeByPath_with_path() {
        mockkStatic(FileTypeUtils::class)
        every { FileTypeUtils.getExtension(any()) }.answers { callOriginal() }
        Assert.assertEquals(MimeTypeHelper.UNKNOWN_TYPE, MimeTypeHelper.getCompressedTypeByPath(null))
        Assert.assertEquals(MimeTypeHelper.UNKNOWN_TYPE, MimeTypeHelper.getCompressedTypeByPath(""))
        Assert.assertEquals(MimeTypeHelper.RAR_TYPE, MimeTypeHelper.getCompressedTypeByPath("aaa.rar"))
        Assert.assertEquals(MimeTypeHelper.ZIP_TYPE, MimeTypeHelper.getCompressedTypeByPath("aaa.zip"))
        Assert.assertEquals(MimeTypeHelper.P7ZIP_TYPE, MimeTypeHelper.getCompressedTypeByPath("aaa.7z"))
        Assert.assertEquals(MimeTypeHelper.JAR_TYPE, MimeTypeHelper.getCompressedTypeByPath("aaa.jar"))
        unmockkStatic(FileTypeUtils::class)
    }

    @Test
    fun should_not_null_when_getIconByType() {
        val context = mockk<Context>()
        every { context.applicationContext }.returns(context)
        MyApplication.init(context)

        mockkObject(KtThumbnailHelper)
        every { KtThumbnailHelper.getClassifyDrawable(any(), any(), any()) }.returns(mockk())
        mockkStatic(ContextCompat::class)
        every { ContextCompat.getDrawable(any(), any()) }.returns(mockk())

        Assert.assertNotNull(MimeTypeHelper.getIconByType(MimeTypeHelper.KEYNOTE_TYPE))
        verify { ContextCompat.getDrawable(any(), com.filemanager.common.R.drawable.ic_file_keynote) }

        Assert.assertNotNull(MimeTypeHelper.getIconByType(MimeTypeHelper.PAGES_TYPE))
        verify { ContextCompat.getDrawable(any(), com.filemanager.common.R.drawable.ic_file_pages) }

        Assert.assertNotNull(MimeTypeHelper.getIconByType(MimeTypeHelper.NUMBERS_TYPE))
        verify { ContextCompat.getDrawable(any(), com.filemanager.common.R.drawable.ic_file_numbers) }

        Assert.assertNotNull(MimeTypeHelper.getIconByType(MimeTypeHelper.MARKDOWN_TYPE))
        verify { ContextCompat.getDrawable(any(), com.filemanager.common.R.drawable.ic_file_markdown) }

        Assert.assertNotNull(MimeTypeHelper.getIconByType(MimeTypeHelper.DWG_TYPE))
        verify { ContextCompat.getDrawable(any(), com.filemanager.common.R.drawable.ic_file_dwg) }

        Assert.assertNotNull(MimeTypeHelper.getIconByType(MimeTypeHelper.DWT_TYPE))
        verify(atLeast = 2) { ContextCompat.getDrawable(any(), com.filemanager.common.R.drawable.ic_file_dwg) }

        Assert.assertNotNull(MimeTypeHelper.getIconByType(MimeTypeHelper.DXF_TYPE))
        verify(atLeast = 3) { ContextCompat.getDrawable(any(), com.filemanager.common.R.drawable.ic_file_dwg) }

        Assert.assertNotNull(MimeTypeHelper.getIconByType(MimeTypeHelper.IMAGE_TYPE))
        verify { KtThumbnailHelper.getClassifyDrawable(any(), MimeTypeHelper.IMAGE_TYPE) }

        unmockkStatic(ContextCompat::class)
        unmockkObject(KtThumbnailHelper)
    }

    @Test
    fun `should return boolean when is other doc type`() {
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.KEYNOTE_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.PAGES_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.NUMBERS_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.MARKDOWN_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.DWG_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.DWT_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.DXF_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.XMIND_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.PSD_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.AI_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.VSDX_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.VSDM_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.VSTX_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.VSTM_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.VSSX_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.VSSM_TYPE))
        Assert.assertEquals(false, MimeTypeHelper.isOtherDocType(MimeTypeHelper.PDF_TYPE))
        Assert.assertEquals(false, MimeTypeHelper.isOtherDocType(MimeTypeHelper.OFD_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.VSD_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.VSS_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.VST_TYPE))
        Assert.assertEquals(true, MimeTypeHelper.isOtherDocType(MimeTypeHelper.VDW_TYPE))
    }
}