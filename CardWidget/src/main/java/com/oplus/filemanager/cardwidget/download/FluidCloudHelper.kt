/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : FluidCloudHelper
 * * Description : 流体云 帮助类
 * * Version     : 1.0
 * * Date        : 2024/12/31
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.download

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.os.Message
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.remotedevice.DownloadTransferCard
import com.oplus.pantanal.seedling.bean.SeedlingIntent
import com.oplus.pantanal.seedling.bean.SeedlingIntentFlagEnum
import com.oplus.pantanal.seedling.intent.IIntentResultCallBack
import com.oplus.pantanal.seedling.update.SeedlingCardOptions
import com.oplus.pantanal.seedling.util.SeedlingTool

object FluidCloudHelper {
    private const val TAG = "FluidCloudHelper"
    private const val UPDATE_DELAY = 1 * 1000L
    private const val NOTIFY_DOWNLOADING_ID = 2000
    private const val NOTIFY_DOWNLOAD_FAILURE_ID = 2001
    private const val NOTIFY_DOWNLOAD_SUCCESS_ID = 2002
    private const val NOTIFY_DOWNLOAD_PAUSE_ID = 2003
    val notifyIds = listOf(NOTIFY_DOWNLOADING_ID, NOTIFY_DOWNLOAD_FAILURE_ID, NOTIFY_DOWNLOAD_SUCCESS_ID, NOTIFY_DOWNLOAD_PAUSE_ID)

    /**
     * 下载流体云的意图
     */
    private const val ACTION_FILE_TRANSFER = "pantanal.intent.business.app.system.FILEMANAGER_FILE_DOWNLOAD"

    /**
     * 测试环境的service id
     */
    private const val TEST_SERVICE_ID_DEFAULT = "268451935"
    private const val TEST_SERVICE_ID_ONEPLUS = "268451952"

    /**
     * 流体云暂时先不上
     */
    private const val SUPPORT = false

    private val handler by lazy {
        IntervalUpdateHandler()
    }

    /**
     * 判断是否支持流体云
     */
    @JvmStatic
    fun isSupport(context: Context): Boolean {
        if (!SUPPORT) {
            return false
        }
        val isSupportSeedling = SeedlingTool.isSupportSeedlingCard(context)
        val isSupportFluidCloud = SeedlingTool.isSupportFluidCloud(context)
        Log.d(TAG, "isSupport -> seedlingCard:$isSupportSeedling fluidCloud:$isSupportFluidCloud")
        return isSupportSeedling && isSupportFluidCloud
    }

    /**
     * 启动下载进度流体云
     */
    @JvmStatic
    fun startDownloadSeedling(context: Context, card: DownloadTransferCard) {
        if (!DownloadTransferCard.showFluidCloud) {
            Log.w(TAG, "startDownloadSeedling current don't need show!!")
            return
        }
        Log.w(TAG, "startDownloadSeedling $card")
        val cardOption = SeedlingCardOptions().apply {
            pageId = card.pageId()
            requestHideStatusBar = !DownloadTransferCard.showFluidCloud //是否需要临时隐藏状态栏胶囊或者面板,默认为false，表示显示胶囊或面板。
            remindType = getCardRemindType(card) //事件提醒类型,系统默认是支持强提醒,一直显示面板
            // 取消通知栏上通知id的通知，防止和泛在卡一同出现
            notificationIdList = notifyIds
        }
        val intent = SeedlingIntent(action = ACTION_FILE_TRANSFER, data = card.toJSON(), cardOptions = cardOption)
        SeedlingTool.sendSeedling(context, intent, object : IIntentResultCallBack {
            override fun onIntentResultCodeCallBack(action: String, flag: Int, resultCode: Int) {
                super.onIntentResultCodeCallBack(action, flag, resultCode)
                Log.d(TAG, "startDownloadSeedling <-- action:$action, flag:$flag, resultCode:$resultCode")
            }
        })
    }

    /**
     * 停止下载进度的流体云
     */
    @JvmStatic
    fun stopDownloadSeedling(context: Context) {
        Log.w(TAG, "stopDownloadSeedling")
        val intent = SeedlingIntent(action = ACTION_FILE_TRANSFER, flag = SeedlingIntentFlagEnum.END)
        SeedlingTool.sendSeedling(context, intent, object : IIntentResultCallBack {
            override fun onIntentResultCodeCallBack(action: String, flag: Int, resultCode: Int) {
                super.onIntentResultCodeCallBack(action, flag, resultCode)
                Log.d(TAG, "stopDownloadSeedling <-- action:$action, flag:$flag, resultCode:$resultCode")
            }
        })
        handler.removeCallbacksAndMessages(null)
    }

    /**
     * 更新下载进度, 1s 更新一次
     */
    @JvmStatic
    fun updateDownloadSeeding(card: DownloadTransferCard) {
        if (!DownloadTransferCard.showFluidCloud) {
            Log.w(TAG, "updateDownloadSeeding current don't need show!!")
            return
        }
        val seedlingCard = DownloadSeedlingCardProvider.card
        Log.d(TAG, "updateDownloadSeeding card:$seedlingCard data:$card")
        if (seedlingCard != null) {
            handler.sendMessageDelay(card, UPDATE_DELAY)
        } else {
            startDownloadSeedling(MyApplication.appContext, card)
        }
    }

    @JvmStatic
    fun handleUpdateDownloadData(card: DownloadTransferCard) {
        val cardOption = SeedlingCardOptions().apply {
            pageId = card.pageId()
            requestHideStatusBar = !DownloadTransferCard.showFluidCloud //是否需要临时隐藏状态栏胶囊或者面板,默认为false，表示显示胶囊或面板。
            remindType = getCardRemindType(card) //事件提醒类型,系统默认是支持强提醒,一直显示面板
            // 取消通知栏上通知id的通知，防止和泛在卡一同出现
            notificationIdList = notifyIds
        }
        val seedlingCard = DownloadSeedlingCardProvider.card
        Log.w(TAG, "handleUpdateDownloadData card:$seedlingCard option:$cardOption data: ${card.toJSON()}")
        if (seedlingCard != null) {
            SeedlingTool.updateAllCardData(seedlingCard, card.toJSON(), cardOption)
        }
    }


    /**
     * 事件提醒类型
     * 系统默认是支持强提醒
     * @return
     * SeedlingCardOptions.REMIND_TYPE_NORMAL值为0（默认）   不显示面板,退到后台后显示胶囊
     * SeedlingCardOptions.REMIND_TYPE_STRONG_SHORT值为11   面板显示3秒
     * SeedlingCardOptions.REMIND_TYPE_STRONG_LONG值为12    面板显示5秒
     * SeedlingCardOptions.REMIND_TYPE_STRONG_ALWAYS值为13  一直显示面板
     */
    @JvmStatic
    private fun getCardRemindType(card: DownloadTransferCard): Int {
        var remindType = SeedlingCardOptions.REMIND_TYPE_NORMAL
        val status = card.status
        val progress = card.progress
        when (status) {
            DownloadTransferCard.STATUS_DOWNLOADING -> {
                if (progress == DownloadTransferCard.PROGRESS_MAX) { // 下载完成，显示5s
                    remindType = SeedlingCardOptions.REMIND_TYPE_STRONG_LONG
                } else {
                    remindType = SeedlingCardOptions.REMIND_TYPE_STRONG_ALWAYS
                }
            }

            DownloadTransferCard.STATUS_WAIT -> { // 等待中，一直显示
                remindType = SeedlingCardOptions.REMIND_TYPE_STRONG_ALWAYS
            }

            DownloadTransferCard.STATUS_FAIL -> { // 下载失败,显示5s
                remindType = SeedlingCardOptions.REMIND_TYPE_STRONG_LONG
            }

            else -> remindType = SeedlingCardOptions.REMIND_TYPE_NORMAL
        }
        Log.i(TAG, "getCardRemindType status:$status progress:$progress -> remindType:$remindType")
        return remindType
    }


    /**
     * 间隔更新的handler
     */
    private class IntervalUpdateHandler : Handler(Looper.getMainLooper()) {

        companion object {
            const val UPDATE_WHAT = 1001
        }

        private var updateTime = 0L

        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == UPDATE_WHAT) {
                val card = msg.obj as DownloadTransferCard
                handleUpdateDownloadData(card)
                updateTime = System.currentTimeMillis()
            }
        }

        fun obtain(card: DownloadTransferCard): Message {
            val msg = Message.obtain(this)
            msg.what = UPDATE_WHAT
            msg.obj = card
            return msg
        }

        fun sendMessageDelay(card: DownloadTransferCard, delayTime: Long) {
            val time = System.currentTimeMillis()
            if (handler.hasMessages(UPDATE_WHAT)) {
                handler.removeMessages(UPDATE_WHAT)
            }
            val msg = handler.obtain(card)
            handler.sendMessageDelayed(msg, delayTime - (time - updateTime))
        }
    }
}