/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentCardSettingViewModel
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/14 15:26
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.setting

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.stringResource
import com.oplus.filemanager.cardwidget.recent.data.RecentCard
import com.oplus.filemanager.cardwidget.recent.data.RecentPreviewItem
import com.oplus.filemanager.cardwidget.recent.usecase.GetAllCategoryItemsUseCase
import com.oplus.filemanager.cardwidget.recent.utils.RecentCardUtils
import com.oplus.filemanager.cardwidget.recent.utils.obtainTargetItem

class RecentCardSettingViewModel : BaseViewModel() {

    private var originRecentType = RecentCard.DEFAULT_TYPE
    private var originRecentName = ""
    private var currCardId = ""

    private val _selectItem = MutableLiveData(RecentPreviewItem(RecentCard.DEFAULT_TYPE, ""))
    val selectItem: LiveData<RecentPreviewItem> = _selectItem

    private val _allItems = MutableLiveData<List<RecentPreviewItem>>()
    val allItems: LiveData<List<RecentPreviewItem>> = _allItems

    private val _recentName = MutableLiveData<String>()
    val recentName: LiveData<String> = _recentName

    fun setCardId(cardId: String?) {
        cardId?.let { id ->
            currCardId = id
            val data = RecentCardUtils.instance.getCacheCardDataByCardId(cardId)
            originRecentType = data?.type ?: RecentCard.DEFAULT_TYPE
            originRecentName = data?.name ?: ""
            _selectItem.value = RecentPreviewItem(originRecentType, data?.name ?: "")
        }
    }

    fun saveRecentCardMapping() {
        val recentType = selectItem.value?.type ?: RecentCard.DEFAULT_TYPE
        val cardName = selectItem.value?.name ?: ""
        if (recentType == CategoryHelper.CATEGORY_QQ) {
            if (originRecentName == cardName) {
                Log.d(TAG, "saveRecentCardMapping -> same selected item")
            }
        } else if (originRecentType == recentType) {
            Log.d(TAG, "saveRecentCardMapping -> no need to save")
            return
        }
        RecentCardUtils.instance.switchCategory(currCardId, recentType, cardName)
    }

    fun loadRecentItems() {
        val items = GetAllCategoryItemsUseCase().invoke()
        _allItems.postValue(items)
        val item = obtainTargetItem(originRecentType, originRecentName, items)
        _recentName.postValue(item.name)
    }

    fun getCurrSelectedItemType(): Int {
        return selectItem.value?.type ?: RecentCard.DEFAULT_TYPE
    }

    fun selectRecentItem(date: RecentPreviewItem) {
        val curItem = _selectItem.value
        Log.d(TAG, "selectRecentItem -> date = $date ; current = $curItem")
        if (date.type == curItem?.type && date.name == curItem.name) {
            Log.d(TAG, "selectRecentItem -> same item")
            return
        }
        _selectItem.postValue(date)
        _recentName.postValue(
            if (date.type == CategoryHelper.CATEGORY_RECENT) {
                stringResource(com.filemanager.common.R.string.label_add_recent_file_title)
            } else {
                date.name
            }
        )
    }

    companion object {
        private const val TAG = "RecentCardSettingViewModel"
        private const val DEFAULT_SIZE = 4
    }
}