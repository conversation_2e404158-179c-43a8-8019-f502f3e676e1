/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LabelSelectAdapter
 ** Description : 标签列表的Adapter
 ** Version     : 1.0
 ** Date        : 2022/11/29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue      2022/11/29      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.dialog

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.cardlist.COUICardListHelper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.cardwidget.R
import com.oplus.filemanager.cardwidget.label.data.LabelData

class LabelListAdapter(var context: Context, var selectLabel: Long, var list: List<LabelData>) :
    RecyclerView.Adapter<LabelListAdapter.LabelListVH>() {

    @VisibleForTesting
    val layoutInflater: LayoutInflater by lazy {
        LayoutInflater.from(context)
    }

    @VisibleForTesting
    var selectIndex = -1

    var onItemClickListener: OnItemClickListener<LabelData>? = null

    fun setLabelList(list: List<LabelData>) {
        this.list = list
        findSelectIndex()
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LabelListVH {
        val view = layoutInflater.inflate(R.layout.card_item_select_label, parent, false)
        return LabelListVH(view)
    }

    override fun onBindViewHolder(holder: LabelListVH, position: Int) {
        // 设置数据
        val label = list.get(position)
        holder.bindData(label)
        // 设置点击事件
        holder.setClickEvent(position, label)
        // 设置圆角
        COUICardListHelper.setItemCardBackground(holder.itemView, COUICardListHelper.getPositionInGroup(itemCount, position))
        holder.updateDividerVisible(list.size - 1, position)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    @VisibleForTesting
    fun findSelectIndex() {
        this.list.forEachIndexed { index, labelData ->
            if (labelData.id == selectLabel) {
                selectIndex = index
            }
        }
    }

    @VisibleForTesting
    fun selectLabel(position: Int, label: LabelData) {
        Log.d(TAG, "selectLabel -> position = $position ; label = $label ; selectIndex = $selectIndex")
        if (selectIndex == position) {
            return
        }
        val last = selectIndex
        selectLabel = label.id
        selectIndex = position

        notifyItemChanged(last)
        notifyItemChanged(position)
    }


    class LabelListVH(itemView: View) : RecyclerView.ViewHolder(itemView) {

        @VisibleForTesting
        val nameTv: TextView = itemView.findViewById(R.id.label_name)
        @VisibleForTesting
        val radio: RadioButton = itemView.findViewById(R.id.label_radio)
        @VisibleForTesting
        val dividerLine: View = itemView.findViewById(R.id.divider_line)

        fun bindData(label: LabelData) {
            val adapter = bindingAdapter as? LabelListAdapter ?: return
            nameTv.text = label.name
            radio.isChecked = adapter.selectLabel == label.id
        }

        fun setClickEvent(position: Int, label: LabelData) {
            val adapter = bindingAdapter as? LabelListAdapter ?: return
            itemView.setOnClickListener {
                adapter.selectLabel(position, label)
                adapter.onItemClickListener?.onItemClick(it, position, label)
            }

            radio.setOnClickListener {
                adapter.selectLabel(position, label)
                adapter.onItemClickListener?.onItemClick(it, position, label)
            }
        }

        fun updateDividerVisible(lastItemIndex: Int, position: Int) {
            if (lastItemIndex == position) {
                dividerLine.visibility = View.GONE
            } else {
                dividerLine.visibility = View.VISIBLE
            }
        }
    }

    companion object {
        private const val TAG = "LabelListAdapter"
    }
}