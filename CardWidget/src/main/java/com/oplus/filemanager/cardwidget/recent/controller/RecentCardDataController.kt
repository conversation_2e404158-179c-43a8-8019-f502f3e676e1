/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentCardDataController
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/13 20:48
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/13       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.controller

import android.text.TextUtils
import android.view.View
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.stringResource
import com.google.gson.Gson
import com.oplus.filemanager.cardwidget.ext.getIconPNGByType
import com.oplus.filemanager.cardwidget.ext.writeFileProvider
import com.oplus.filemanager.cardwidget.recent.controller.loader.CategoryRecentLoader
import com.oplus.filemanager.cardwidget.recent.controller.loader.LoadCallback
import com.oplus.filemanager.cardwidget.recent.data.ACTION_SUPER_APP
import com.oplus.filemanager.cardwidget.recent.data.RECENT_CARD_INFO
import com.oplus.filemanager.cardwidget.recent.data.RecentCard
import com.oplus.filemanager.cardwidget.recent.data.RecentCardItemData
import com.oplus.filemanager.cardwidget.recent.data.RecentPreviewItem
import com.oplus.filemanager.cardwidget.recent.utils.obtainTargetItem
import com.oplus.filemanager.cardwidget.util.ANDROID_LAUNCHER_PKG
import com.oplus.filemanager.cardwidget.util.ASSISTANT_SCREEN_PKG
import com.oplus.filemanager.cardwidget.util.CardWidgetThumbnailGenerator
import com.oplus.filemanager.cardwidget.util.OPPO_LAUNCHER_PKG
import com.oplus.filemanager.cardwidget.util.isSupportThumbnailForCardWidget
import com.oplus.filemanager.cardwidget.util.ruleForThumbnailFileName
import kotlinx.coroutines.suspendCancellableCoroutine
import org.apache.commons.io.FilenameUtils
import org.json.JSONArray
import org.json.JSONObject
import java.io.File
import java.util.Locale
import kotlin.coroutines.resume

class RecentCardDataController(private val size: Int) {

    suspend fun loadData(data: RecentCard, allCategoryItem: List<RecentPreviewItem>): Pair<Boolean, JSONObject> =
        suspendCancellableCoroutine {
            val currType = obtainTargetItem(data.type, data.name, allCategoryItem)
            val loader = CategoryRecentLoader(currType.type, size + 1, data.name)
            loader.loadDatas(object : LoadCallback {
                override fun loadSuccess(result: List<BaseFileBean>) {
                    val jsonObject = if (result.isEmpty()) {
                        convertEmptyResultToJsonObject(data, allCategoryItem)
                    } else {
                        convertResultToJsonObject(data, allCategoryItem, result)
                    }
                    if (it.isCancelled) return
                    if (!it.isActive) {
                        Log.d(TAG, "loadData -> loadSuccess but not isActive.")
                        return
                    }
                    it.resume(Pair(result.isEmpty(), jsonObject))
                }

                override fun loadFailure() {
                    val jsonObject = convertEmptyResultToJsonObject(data, allCategoryItem)
                    it.resume(Pair(true, jsonObject))
                }
            })
        }

    private fun convertEmptyResultToJsonObject(
        card: RecentCard,
        allCategoryItem: List<RecentPreviewItem>,
    ): JSONObject {
        val currType = obtainTargetItem(card.type, card.name, allCategoryItem)
        val isRecent = currType.type == CategoryHelper.CATEGORY_RECENT
        val cardTitle = stringResource(com.filemanager.common.R.string.label_add_recent_file_title)
        val sourceAppName = if (isRecent) {
            stringResource(com.filemanager.common.R.string.label_add_recent_file_title)
        } else {
            currType.name
        }
        val actionUri = RECENT_CARD_INFO[currType.type] ?: ACTION_SUPER_APP
        val jsonObject = JSONObject()
        jsonObject.apply {
            jsonObject.put(KEY_ICON, IC_RECENT_ICON)
            jsonObject.put(KEY_IS_RECENT, isRecent)
            jsonObject.put(KEY_CARD_TITLE, cardTitle)
            jsonObject.put(KEY_ACTION_URI, actionUri)
            jsonObject.put(KEY_CARD_TYPE, currType.type)
            jsonObject.put(KEY_SOURCE_APP_NAME, sourceAppName)
        }
        Log.d(TAG, "convertEmptyResultToJsonObject -> jsonObject = $jsonObject")
        return jsonObject
    }

    private fun convertResultToJsonObject(
        card: RecentCard,
        allCategoryItem: List<RecentPreviewItem>,
        resultList: List<BaseFileBean>
    ): JSONObject {
        val showMore = resultList.size > size
        val currType = obtainTargetItem(card.type, card.name, allCategoryItem)
        val isRecent = currType.type == CategoryHelper.CATEGORY_RECENT
        val cardTitle = stringResource(com.filemanager.common.R.string.label_add_recent_file_title)
        val sourceAppName = if (isRecent) {
            stringResource(com.filemanager.common.R.string.label_add_recent_file_title)
        } else {
            currType.name
        }
        val actionUri = RECENT_CARD_INFO[currType.type] ?: ACTION_SUPER_APP
        val list = convertResult(resultList)
        val isRtl = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL
        val titleJustifyType = if (isRtl) TITLE_JUSTIFY_TYPE_RTL else TITLE_JUSTIFY_TYPE_LTR
        val jsonObject = JSONObject()
        jsonObject.apply {
            jsonObject.put(KEY_IS_RECENT, isRecent)
            jsonObject.put(KEY_ICON, IC_RECENT_ICON)
            jsonObject.put(KEY_CARD_TITLE, cardTitle)
            jsonObject.put(KEY_ACTION_URI, actionUri)
            jsonObject.put(KEY_CARD_TYPE, currType.type)
            jsonObject.put(KEY_SOURCE_APP_NAME, sourceAppName)
            val jsonArray = JSONArray(Gson().toJson(list))
            jsonObject.put(KEY_ITEMS, jsonArray)
            jsonObject.put(KEY_SHOW_MORE, showMore)
            jsonObject.put(KEY_MORE_ICON, IC_MORE_ICON)
            val moreText = stringResource(com.filemanager.common.R.string.label_card_more)
            put(KEY_MORE_TEXT, moreText)
            put(KEY_TITLE_JUSTIFY_TYPE, titleJustifyType)
        }
        Log.d(TAG, "convertResultToJsonObject -> jsonObject = $jsonObject")
        return jsonObject
    }

    private fun convertResult(resultList: List<BaseFileBean>): List<RecentCardItemData> {
        val list = if (resultList.size > size) {
            resultList.subList(0, size)
        } else {
            resultList
        }
        val result = mutableListOf<RecentCardItemData>()
        list.forEach {
            val duration = Utils.getDateFormat(MyApplication.appContext, it.mDateModified)
            var img: String? = null
            var imageType = IMAGE_TYPE_ICON
            if (isSupportThumbnailForCardWidget(it.mLocalType)) {
                it.mData?.let { path ->
                    val file = File(ruleForThumbnailFileName(path, it.mDateModified, it.mSize))
                    if (file.exists().not()) {
                        CardWidgetThumbnailGenerator.generate(it.mLocalType, path, it.mDateModified, it.mSize)
                    }
                    if (file.exists() && file.length() > 0) {
                        img = writeFileProvider(
                            MyApplication.appContext,
                            file,
                            arrayOf(ASSISTANT_SCREEN_PKG, ANDROID_LAUNCHER_PKG, OPPO_LAUNCHER_PKG)
                        ).toString()
                        if (it.mLocalType != MimeTypeHelper.APPLICATION_TYPE) {
                            imageType = IMAGE_TYPE_THUMBNAIL
                        }
                        Log.d(TAG, "convertResult -> img = $img")
                    } else {
                        img = getIconPNGByType(it.mLocalType)
                    }
                }
            } else {
                val fileType = MimeTypeHelper.getTypeFromPath(it.mData ?: "")
                val type = if (fileType == MimeTypeHelper.COMPRESSED_TYPE) {
                    MimeTypeHelper.getCompressedTypeByPath(it.mData ?: "")
                } else {
                    fileType
                }
                img = getIconPNGByType(type)
            }
            var fileName = it.mDisplayName ?: ""
            var fileExt = ""
            val index = FilenameUtils.indexOfExtension(it.mDisplayName)
            if (index != -1 && index > FILE_NAME_LENGTH_MIN) {
                fileName = it.mDisplayName?.substring(0, index - FILE_NAME_LENGTH_MIN) ?: ""
                fileExt = it.mDisplayName?.substring(index - FILE_NAME_LENGTH_MIN) ?: ""
            }
            val item = RecentCardItemData(it.mDisplayName!!, duration, img ?: "", it.mData ?: "", fileName, fileExt, imageType, true)
            Log.d(TAG, "convertResult -> item = $item")
            result.add(item)
        }
        if (result.size < size) {
            for (i in 0 until size - result.size) {
                result.add(RecentCardItemData("", "", "", "", "", "", "", false))
            }
        }
        return result
    }

    companion object {
        private const val TAG = "RecentCardDataController"
        const val FILE_NAME_LENGTH_MIN = 2
        @VisibleForTesting
        const val KEY_ICON = "icon"
        @VisibleForTesting
        const val KEY_IS_RECENT = "isRecent"
        @VisibleForTesting
        const val KEY_CARD_TITLE = "cardTitle"
        @VisibleForTesting
        const val KEY_CARD_TYPE = "cardType"
        @VisibleForTesting
        const val KEY_SOURCE_APP_NAME = "sourceAppName"

        @VisibleForTesting
        const val KEY_ITEMS = "items"
        @VisibleForTesting
        const val KEY_SHOW_MORE = "showMore"
        @VisibleForTesting
        const val KEY_ACTION_URI = "actionUri"
        const val KEY_MORE_TEXT = "moreText"
        const val KEY_MORE_ICON = "moreIcon"
        const val KEY_TITLE_JUSTIFY_TYPE = "titleJustifyType"

        const val IC_RECENT_ICON = "@drawable/ic_recent_icon"
        const val IC_MORE_ICON = "@drawable/ic_more"

        const val IMAGE_TYPE_THUMBNAIL = "image_type_thumbnail"
        const val IMAGE_TYPE_ICON = "image_type_icon"

        const val TITLE_JUSTIFY_TYPE_LTR = "title_ltr_justify"
        const val TITLE_JUSTIFY_TYPE_RTL = "title_rtl_justify"
    }
}