/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentCardWidgetApi.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/27
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/6/27      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.api

import android.content.Context
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.cardwidget.download.DownloadCompleteCard
import com.oplus.filemanager.cardwidget.download.DownloadFailCard
import com.oplus.filemanager.cardwidget.download.DownloadingCard
import com.oplus.filemanager.cardwidget.download.FluidCloudHelper
import com.oplus.filemanager.cardwidget.provider.RecentFilesCardWidgetProvider
import com.oplus.filemanager.interfaze.cardwidget.ICardWidgetApi
import com.oplus.filemanager.interfaze.remotedevice.DownloadTransferCard

object CardWidgetApi : ICardWidgetApi {

    override fun getRecentCardClickedFile(): BaseFileBean? {
        return RecentFilesCardWidgetProvider.mOpenFile
    }

    override fun isSupportFluidCloud(context: Context): Boolean {
        return FluidCloudHelper.isSupport(context)
    }

    override fun downloadingCard(taskId: Int, progress: Int, files: List<String>): DownloadTransferCard {
        return DownloadingCard(taskId, progress, files, files.size)
    }

    override fun downloadCompleteCard(originPath: String, destPath: String): DownloadTransferCard {
        return DownloadCompleteCard(listOf(originPath), 1, destPath)
    }

    override fun downloadFailCard(filePath: String, failMsg: String): DownloadTransferCard {
        return DownloadFailCard(listOf(filePath), failMsg)
    }

    override fun startDownloadSeedling(context: Context, card: DownloadTransferCard) {
        FluidCloudHelper.startDownloadSeedling(context, card)
    }

    override fun stopDownloadSeedling(context: Context) {
        FluidCloudHelper.stopDownloadSeedling(context)
    }

    override fun updateDownloadSeeding(card: DownloadTransferCard) {
        FluidCloudHelper.updateDownloadSeeding(card)
    }
}