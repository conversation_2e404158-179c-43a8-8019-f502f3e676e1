/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileCutObserver.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(Liu<PERSON><EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.cut

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.R
import com.filemanager.common.helper.getBottomAlertDialogWindowAnimStyle
import com.filemanager.common.helper.getBottomAlertDialogWindowGravity
import com.filemanager.common.utils.COUISnackBarUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ShortCutUtils
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.copy.ERROR_COPY
import com.filemanager.fileoperate.copy.FileCopyObserver
import com.filemanager.fileoperate.cut.FileCutObserver.CutConfirmBean.Companion.TYPE_CUT_IMAGE_IN_STORAGE
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser

const val ERROR_CUT = ERROR_COPY
const val SHOW_MOVE_CONFIRM_DIALOG = 100

open class FileCutObserver(activity: Activity) : FileCopyObserver(activity) {
    override val TAG = "FileCutObserver"
    private var mConfirmDialog: Dialog? = null

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        return when (result.first) {
            SHOW_MOVE_CONFIRM_DIALOG -> {
                if (result.second is CutConfirmBean) {
                    showConfirmDialog(context, result.second as CutConfirmBean)
                }
                true
            }
            ACTION_FAILED -> {
                return when (result.second) {
                    ERROR_CUT -> {
                        // Do nothing currently
                        // CustomToast.showShort(R.string.toast_cut_file_error)
                        false
                    }
                    else -> super.onChanged(context, result)
                }
            }
            ACTION_DONE -> {
                val pair = result.second as? Pair<*, *>
                val destPath = pair?.first
                val dragToCut = pair?.second as? Boolean
                val resId = if (dragToCut == true) {
                    R.string.drag_in_success
                } else {
                    R.string.move_success
                }
                if ((context is Activity) && (destPath is String)) {
                    COUISnackBarUtils.show(context, resId) {
                        val fileBrowser = Injector.injectFactory<IFileBrowser>()
                        fileBrowser?.toFileBrowserActivity(context, destPath)
                    }
                } else {
                    CustomToast.showLong(resId)
                }
                // 移动文件成功后，check下快捷方式
                false
            }
            else -> super.onChanged(context, result)
        }
    }

    private fun showConfirmDialog(context: Context, confirmBean: CutConfirmBean) {
        mConfirmDialog?.dismiss()
        mConfirmDialog = DialogFactory.createConfirmDialog(context, confirmBean)
        mConfirmDialog?.show()
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || mConfirmDialog?.isShowing ?: false
    }

    override fun recycle() {
        runCatching {
            mConfirmDialog?.dismiss()
        }.onFailure {
            Log.e(TAG, "recycle ${it.message}")
        }
        mConfirmDialog = null
        super.recycle()
    }

    data class CutConfirmBean(val type: Int, val moveFileSize: Int, var destPath: String?) {
        companion object {
            const val TYPE_CUT_IMAGE_IN_STORAGE = 0
            const val TYPE_CUT_BETWEEN_DFM_AND_DEVICE = 1
        }

        var positiveListener: DialogInterface.OnClickListener? = null
        var negativeListener: DialogInterface.OnClickListener? = null

        fun recycle() {
            positiveListener = null
            negativeListener = null
        }
    }
}

private object DialogFactory {
    fun createConfirmDialog(context: Context, confirmBean: FileCutObserver.CutConfirmBean)
            : Dialog {
        if (confirmBean.type == TYPE_CUT_IMAGE_IN_STORAGE) {
            return createCutImageInStorageDialog(confirmBean, context)
        } else {
            return createCutBetweenDfmAndDeviceDialog(confirmBean, context)
        }
    }

    /**
     * 创建跨设备移动的确认弹窗
     */
    private fun createCutBetweenDfmAndDeviceDialog(
        confirmBean: FileCutObserver.CutConfirmBean,
        context: Context
    ): AlertDialog {
        val virtualPath = FileCutHelper.getDestShowPath(context, confirmBean)
        val fileCount = confirmBean.moveFileSize
        val message = if (fileCount <= 1) {
            context.resources.getString(
                R.string.dialog_move_between_devices_message_1,
                virtualPath
            )
        } else {
            context.resources.getString(
                R.string.dialog_move_between_devices_message_many,
                fileCount,
                virtualPath
            )
        }
        val title = if (fileCount <= 1) {
            context.resources.getString(R.string.dialog_move_between_devices_title_1)
        } else {
            context.resources.getString(R.string.dialog_move_between_devices_title_many)
        }
        val messageContinue = context.resources.getString(R.string.confirm)
        val builder = COUIAlertDialogBuilder(context)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton(messageContinue, confirmBean.positiveListener)
            .setBlurBackgroundDrawable(true)
            .setNegativeButton(context.resources.getString(R.string.dialog_cancel), confirmBean.negativeListener)
        val dialog = builder.show()
        dialog.setCancelable(false)
        return dialog
    }

    private fun createCutImageInStorageDialog(
        confirmBean: FileCutObserver.CutConfirmBean,
        context: Context
    ): AlertDialog {
        val deviceName = FileCutHelper.getDestDevicesName(context, confirmBean)
        val message = context.getString(R.string.move_file_confirm_msg, deviceName)
        val fileCount = confirmBean.moveFileSize
        val title = if (fileCount > 1) {
            context.resources.getQuantityString(
                R.plurals.move_some_file_confirm_title,
                fileCount,
                fileCount
            )
        } else {
            context.resources.getString(R.string.move_one_file_confirm_title)
        }
        val messageContinue = context.resources.getString(R.string.menu_file_list_move)
        val builder = COUIAlertDialogBuilder(context)
            .setWindowGravity(getBottomAlertDialogWindowGravity(context))
            .setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(context))
            .setTitle(title)
            .setMessage(message)
        val dialog = builder.create()
        dialog.setCancelable(false)
        dialog.setButton(
            DialogInterface.BUTTON_POSITIVE,
            messageContinue,
            confirmBean.positiveListener
        )
        dialog.setButton(
            DialogInterface.BUTTON_NEGATIVE,
            context.resources.getString(R.string.alert_dialog_no), confirmBean.negativeListener
        )
        return dialog
    }
}