/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CategoryUiStateTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/4/21 9:25
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/4/21       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.main.ui.uistate

import com.oplus.filemanager.interfaze.superapp.ISuperApp
import io.mockk.every
import io.mockk.mockk
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module

class CategoryUiStateTest {
    private val superApp = mockk<ISuperApp>()

    private val koinApp = koinApplication {
        modules(module {
            single { superApp }
        })
    }

    @Before
    fun setUp() {
        startKoin(koinApp)
        every { superApp.getCategoryIconRes(any()) } returns 1
    }

    @After
    fun tearDown() {
        stopKoin()
    }

    @Test
    fun testCategoryUiStateEquals() {
        val categoryUiState = CategoryUiState()
        val fakeUiState = Any()
        Assert.assertFalse(categoryUiState == fakeUiState)
        Assert.assertFalse(categoryUiState.hashCode() == fakeUiState.hashCode())
        val picItem = CategoryItem.CategoryPic()
        val videoItem = CategoryItem.CategoryVideo()
        val audioItem = CategoryItem.CategoryAudio()
        val categoryItems = mutableListOf<CategoryItem>()
        categoryItems.add(picItem)
        categoryItems.add(videoItem)
        categoryUiState.categoryItems = categoryItems
        val fakeCategoryUiState = CategoryUiState()
        val fakeCategoryItems = mutableListOf<CategoryItem>()
        fakeCategoryItems.add(picItem)
        fakeCategoryUiState.categoryItems = fakeCategoryItems
        Assert.assertFalse(categoryUiState == fakeCategoryUiState)
        fakeCategoryItems.add(audioItem)
        fakeCategoryUiState.categoryItems = fakeCategoryItems
        Assert.assertFalse(categoryUiState == fakeCategoryUiState)
        fakeCategoryItems.remove(audioItem)
        val videoItemWithCountOne = CategoryItem.CategoryVideo().apply {
            categoryCount = 1
        }
        fakeCategoryItems.add(videoItemWithCountOne)
        fakeCategoryUiState.categoryItems = fakeCategoryItems
        Assert.assertFalse(categoryUiState == fakeCategoryUiState)
        fakeCategoryItems.remove(videoItemWithCountOne)
        fakeCategoryItems.add(videoItem)
        fakeCategoryUiState.categoryItems = fakeCategoryItems
        Assert.assertFalse(categoryUiState == fakeCategoryUiState)
        fakeCategoryUiState.categoryItems = categoryItems
        Assert.assertFalse(categoryUiState == fakeCategoryUiState)
    }
}