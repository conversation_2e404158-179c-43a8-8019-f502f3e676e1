/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RootFilesService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/4 16:01
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********        2022/7/4       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist

import android.app.Service
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.os.RemoteException
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.HansFreezeManager
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin
import com.oplus.filemanager.interfaze.recyclebin.RecycleBinTotalSizeCallback
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.main.ui.category.MainCategoryHelper
import com.oplus.fileservice.bean.FileResponseStatusCode
import com.oplus.fileservice.bean.ResponseData
import com.oplus.fileservice.utils.WebFileConstant
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume

class RootFilesService : Service() {
    companion object {
        private const val TAG = "RootFilesService"
    }
    private val categoryHelper by lazy { MainCategoryHelper() }
    private val mGson = Gson()

    private val superApp: ISuperApp? by lazy {
        Injector.injectFactory<ISuperApp>()
    }

    override fun onCreate() {
        Log.d(TAG, "onCreate()")
        super.onCreate()
    }

    override fun onBind(intent: Intent?): IBinder {
        Log.d(
            TAG,
            "onBind() getAction = ${intent?.action} , getExtras =${intent?.extras}"
        )
        HansFreezeManager.instance.keepBackgroundRunning()

        return Messenger(Handler(Looper.getMainLooper()) { msg ->
            val bundle = Bundle()
            // 保存 msg 的关键信息到局部变量
            val what = msg.what
            val replyTo = msg.replyTo
            GlobalScope.launch(Dispatchers.Main) {
                val data = withContext(Dispatchers.IO) {
                    getRootFile()
                }
                bundle.putString(
                    WebFileConstant.DATA,
                    mGson.toJson(
                        ResponseData(
                            FileResponseStatusCode.OK.code,
                            data,
                            FileResponseStatusCode.OK.description
                        )
                    )
                )
                // 创建回复消息
                val replyMsg = Message.obtain().apply {
                    this.what = what
                    this.data = bundle
                }

                // 发送回复消息
                try {
                    Log.d(TAG, "replyMsg -> ${replyMsg.data}")
                    replyTo?.send(replyMsg)
                } catch (e: RemoteException) {
                    e.printStackTrace()
                }
            }
            true
        }).binder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Log.d(TAG, "onUnbid")
        HansFreezeManager.instance.cancelFrozenDelay()
        return super.onUnbind(intent)
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy")
        super.onDestroy()
    }

    private suspend fun getRootFile(): ArrayList<RootFile> {
        val data: ArrayList<RootFile> = ArrayList()

        data.add(RootFile(CategoryHelper.CATEGORY_FILL_RECENTLY, getString(com.filemanager.common.R.string.all_file)))
        data.add(RootFile(CategoryHelper.CATEGORY_FILE_ALL, getString(com.filemanager.common.R.string.recently_file)))

        val categoryItemText =
            appContext.resources.getStringArray(com.filemanager.common.R.array.category_activity_title_new)
        for (i in 0..CategoryHelper.ITEM_FIX_LAST_INDEX) {
            superApp?.getCategory(i)?.apply {
                data.add(
                    RootFile(
                        id = this,
                        name = categoryItemText[i],
                        count = categoryHelper.getCountByCategory(this, false)
                    )
                )
            }
        }

        superApp?.getCategoryItems(appContext)?.forEach {
            data.add(
                RootFile(
                    it.itemType,
                    it.name,
                    it.packageName,
                    count = categoryHelper.getCountByPathArray(it.fileList, false)
                )
            )
        }

        if (!FeatureCompat.sIsLightVersion) {
            loadRecycleBinSize(data)
        }
        return data
    }

    data class RootFile(
        @SerializedName("id") val id: Int,
        @SerializedName("name") val name: String,
        @SerializedName("pkgName") var pkgName: String = "",
        @SerializedName("count") var count: Long = -1
    )

    private suspend fun loadRecycleBinSize(data: ArrayList<RootFile>) {
        val recycleBinAction = Injector.injectFactory<IRecycleBin>()
        suspendCancellableCoroutine { continuation ->
            recycleBinAction?.loadRecycleBinSize(
                TAG,
                null,
                object : RecycleBinTotalSizeCallback {
                    override fun onTotalSizeReturn(size: Pair<Long, Long>) {
                        Log.d(TAG, "onTotalSizeReturn  $size")
                        data.add(
                            RootFile(
                                CategoryHelper.CATEGORY_RECYCLE_BIN,
                                resources.getString(com.filemanager.common.R.string.text_recycle_bin),
                                count = size.second
                            )
                        )
                        continuation.resume(Unit) // 恢复协程
                    }
                }
            )
        }
    }
}