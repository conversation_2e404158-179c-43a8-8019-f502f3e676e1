<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.filemanager.setting.ui.about.AppIconPreference
        android:key="setting_app_icon"
        android:persistent="true"
        android:selectable="false"
        app:couiShowDivider="false"
        android:layout="@layout/app_icon_version"/>

    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="setting_pref_open_source"
            android:persistent="true"
            android:title="@string/open_source_statement"/>
    </com.coui.appcompat.preference.COUIPreferenceCategory>


</PreferenceScreen>