/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: ZipCompressHelper.kt
 ** Description: Decompress zip file
 ** Version: 1.0
 ** Date: 2020/3/10
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.decompress

import android.os.Build
import android.util.ArrayMap
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.Log
import net.lingala.zip4j.ZipFile
import net.lingala.zip4j.exception.ZipException
import net.lingala.zip4j.model.FileHeader
import java.io.*
import java.nio.charset.Charset
import java.util.*
import java.util.zip.ZipEntry

/**
 * Encrypted zip file: Use net.lingala.zip4j.ZipFile
 * Normal zip file: Use java.util.zip.ZipFile
 */
class ZipDecompressHelper : DecompressHelper<ZipDecompressFile>() {
    companion object {
        private const val TAG = "ZipDecompressHelper"
        const val COPY_BUF_SIZE = 512 * 1024
        const val CHAR_SET_FIRST = 1
        const val CHAR_SET_SECOND = 2
        const val CHAR_SET_UTF_8 = "UTF_8"
        const val CHAR_SET_GBK = "gbk"
    }

    override fun createDecompressDirFile(path: String): ZipDecompressFile {
        return ZipDecompressFile(zipEntry = null).easyCreateDirFile(path) as ZipDecompressFile
    }

    override fun internalPreview(sourceFile: BaseFileBean): Pair<Int, MutableMap<String, MutableList<ZipDecompressFile>?>?> {
        try {
            val isEncrypt = isEncrypted(sourceFile)
            if (isEncrypt) {
                // java.util.zip.ZipFile not support preview the encrypted file
                val zFile = get4JZipFile(sourceFile.mData!!) ?: return Pair(PREVIEW_FAILED, null)
                val fileHeaders: MutableList<FileHeader?>? = zFile.fileHeaders
                if ((fileHeaders?.size ?: 0) > MAX_COMPRESS_FILE_NUM) {
                    return Pair(PREVIEW_OVERCOUNT, null)
                }

                var compressFiles = ArrayMap<String, MutableList<ZipDecompressFile>?>()
                fileHeaders?.forEach {
                    if (it is FileHeader) {
                        it.isFileNameUTF8Encoded = false
                        if (isValidPreviewFileName(it.fileName)) {
                            val file = ZipDecompressFile(it)
                            createPreviewFileTree(file, compressFiles)
                        }
                    }
                }
                return Pair(PREVIEW_SUCCESS, compressFiles)
            } else {
                var result = getJavaZipPreviewFiles(sourceFile, getCharset(CHAR_SET_FIRST))
                if (result == null) {
                    result = getJavaZipPreviewFiles(sourceFile, getCharset(CHAR_SET_SECOND))
                }
                if (result != null) return result
            }
        } catch (e: Exception) {
            Log.e(TAG, "preview failedL ${e.message}")
        }
        return Pair(PREVIEW_FAILED, null)
    }

    private fun getJavaZipPreviewFiles(sourceFile: BaseFileBean, charset: String?)
            : Pair<Int, MutableMap<String, MutableList<ZipDecompressFile>?>?>? {
        var zipFile: java.util.zip.ZipFile? = null
        try {
            zipFile = getJavaZipFile(sourceFile.mData!!, charset)
            getJavaZipFileEntries(zipFile)?.let { entries ->
                if (entries.size > MAX_COMPRESS_FILE_NUM) {
                    return Pair(PREVIEW_OVERCOUNT, null)
                }

                var compressFiles = ArrayMap<String, MutableList<ZipDecompressFile>?>()
                entries.forEach {
                    if (isValidPreviewFileName(it.name)) {
                        val previewFile = ZipDecompressFile(it)
                        createPreviewFileTree(previewFile, compressFiles)
                    }
                }
                return Pair(PREVIEW_SUCCESS, compressFiles)
            }
        } catch (e: Exception) {
            Log.e(TAG, "getJavaZipPreviewFiles failed: charset=$charset, error=${e.message}")
        } finally {
            quietClose(zipFile)
        }
        return null
    }

    override fun internalDecompress(sourceFile: BaseFileBean, destParentFile: BaseFileBean, password: String?,
                                    selectFiles: MutableList<ZipDecompressFile>?, listener: OnDecompressListener?): Boolean? {
        Log.d(TAG, "start zip decompress: targetPath=${destParentFile.mData}")
        return if (isEncrypted(sourceFile)) {
            decompressEncryptFile(sourceFile, destParentFile, password, selectFiles)
        } else {
            decompressNormalFile(sourceFile, destParentFile, selectFiles)
        }
    }

    private fun decompressEncryptFile(sourceFile: BaseFileBean, destParentFile: BaseFileBean, password: String?,
                                      selectFiles: MutableList<ZipDecompressFile>?): Boolean {
        Log.d(TAG, "start to decompressEncryptFile")
        var zipFile = get4JZipFile(sourceFile.mData!!, password) ?: run {
            Log.d(TAG, "decompressEncryptFile: Get zip file null")
            return false
        }
        try {
            Log.d(TAG, "decompressEncryptFile: start to decompress selected files")
            if (zipFile.fileHeaders?.isNotEmpty() != true) {
                Log.e(TAG, "internalDecompress failed: file head is empty")
                return false
            }

            val hasSelectFile = selectFiles?.isNotEmpty() ?: false
            val dealList = arrayListOf<FileHeader>()
            var totalSize = 0L
            zipFile.fileHeaders?.let { headList ->
                headList.forEach {
                    if (isCancelled()) {
                        Log.d(TAG, "Failed to decompress: cancelled when calculate")
                        return false
                    }

                    if (it is FileHeader) {
                        if (!hasSelectFile || checkCanDecompress(it.fileName, it.isDirectory, selectFiles!!)) {
                            dealList.add(it)
                            totalSize++
                        }
                    }
                }
            }
            if (zipFile.isEncrypted && password.isNullOrEmpty().not()) {
                zipFile.setPassword(password!!.toCharArray())
            }
            saveDecompressTotalSize(totalSize)
            dealList.forEach {
                if (isCancelled()) {
                    Log.d(TAG, "Failed to decompress: cancelled when decompress")
                    return false
                }
                zipFile.extractFile(it, destParentFile.mData)
                updateDecompressProgress(1)
            }
            return true
        } catch (e: Exception) {
            Log.d(TAG, "Failed to decompress: ${e.message}")
        }
        return false
    }

    private fun decompressNormalFile(sourceFile: BaseFileBean, destParentFile: BaseFileBean,
                                     selectFiles: MutableList<ZipDecompressFile>?): Boolean {
        Log.d(TAG, "start to decompressNormalFile")
        forceMkdirs(File(destParentFile.mData))
        var zipFile: java.util.zip.ZipFile? = null
        var zipEntrys: MutableList<ZipEntry>? = null
        try {
            zipFile = getJavaZipFile(sourceFile.mData!!, getCharset(CHAR_SET_FIRST))
            zipEntrys = getJavaZipFileEntries(zipFile)
            if (zipEntrys == null) {
                Log.d(TAG, "decompressNormalFile: failed open file by ${getCharset(CHAR_SET_FIRST)}, try " +
                        "${getCharset(CHAR_SET_SECOND)}")
                quietClose(zipFile)
                zipFile = getJavaZipFile(sourceFile.mData!!, getCharset(CHAR_SET_SECOND))
                zipEntrys = getJavaZipFileEntries(zipFile)
                if (zipEntrys == null) {
                    Log.d(TAG, "decompressNormalFile: failed open file by ${getCharset(CHAR_SET_SECOND)}, exit")
                    quietClose(zipFile)
                    zipFile = null
                    return false
                }
            }

            val hasSelectFile = selectFiles?.isNotEmpty() ?: false
            val dealList = arrayListOf<ZipEntry>()
            var totalSize = 0L
            zipEntrys!!.forEach {
                if (!hasSelectFile || checkCanDecompress(it.name, it.isDirectory, selectFiles!!)) {
                    dealList.add(it)
                    totalSize += it.size
                }
                if (isCancelled()) {
                    Log.d(TAG, "decompressNormalFile failed: cancelled when calculate")
                    return false
                }
            }
            saveDecompressTotalSize(totalSize)

            dealList.forEach {
                if (it.isDirectory) {
                    if (!forceMkdirs(File(destParentFile.mData, it.name))) {
                        Log.d(TAG, "forceMkdirs failed,destParentFile.mData=${destParentFile.mData},name=${it.name}")
                        return false
                    }
                } else {
                    decompressCopy(it, zipFile!!, destParentFile)
                }
                if (isCancelled()) {
                    Log.d(TAG, "decompressNormalFile failed: cancelled when decompress")
                    return false
                }
            }
            return true
        } catch (e: Exception) {
            Log.d(TAG, "decompressNormalFile failed: exception=${e.message}")
        } finally {
            quietClose(zipFile)
        }
        Log.d(TAG, "decompressNormalFile return false")
        return false
    }

    private fun getCharset(charSetType: Int): String {
        return if (charSetType == CHAR_SET_FIRST) {
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                CHAR_SET_GBK
            } else {
                CHAR_SET_UTF_8
            }
        } else {
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                CHAR_SET_UTF_8
            } else {
                CHAR_SET_GBK
            }
        }
    }

    @Throws(IOException::class)
    private fun decompressCopy(zipEntry: ZipEntry, zipFile: java.util.zip.ZipFile, destParentFile: BaseFileBean) {
        val zipInputStream = zipFile.getInputStream(zipEntry)
        val file = File(destParentFile.mData, zipEntry.name)
        forceMkdirs(file.parentFile)
        if (!(file.exists() || file.createNewFile())) {
            Log.d(TAG, "decompressCopy: createNewFile failed")
            return
        }
        val zipOutputStream = FileOutputStream(file)
        if (zipInputStream == null) {
            return
        }
        val size = zipEntry.size.toInt()
        val buffer = ByteArray(COPY_BUF_SIZE)
        var bufferedInputStream: BufferedInputStream? = null
        var bufferedOutputStream: BufferedOutputStream? = null
        if (size > COPY_BUF_SIZE) {
            bufferedInputStream = BufferedInputStream(zipInputStream, COPY_BUF_SIZE)
            bufferedOutputStream = BufferedOutputStream(zipOutputStream, COPY_BUF_SIZE)
        } else {
            bufferedInputStream = BufferedInputStream(zipInputStream)
            bufferedOutputStream = BufferedOutputStream(zipOutputStream)
        }
        var n = 0
        while (0 < bufferedInputStream.read(buffer).also { n = it }) {
            if (isCancelled()) {
                val delResult = file.delete()
                Log.d(TAG, "decompressCopy: Delete file when cancel: [$delResult], ${file.name}")
                break
            } else {
                bufferedOutputStream.write(buffer, 0, n)
                updateDecompressProgress(n.toLong())
            }
        }
        quietClose(bufferedInputStream)
        quietClose(bufferedOutputStream)
        quietClose(zipInputStream)
        quietClose(zipOutputStream)
    }

    private fun getJavaZipFileEntries(zipFile: java.util.zip.ZipFile?): MutableList<ZipEntry>? {
        if (zipFile == null) {
            return null
        }
        val zipEntrys: MutableList<ZipEntry> = ArrayList()
        val entries: Enumeration<*> = zipFile.entries()
        while (entries.hasMoreElements()) {
            try {
                zipEntrys.add(entries.nextElement() as ZipEntry)
            } catch (e: Exception) {
                return null
            }
        }
        return zipEntrys
    }

    override fun internalIsEncrypted(sourceFile: BaseFileBean): Boolean {
        try {
            return ZipFile(File(sourceFile.mData)).isEncrypted
        } catch (e: Exception) {
            Log.e(TAG, "isEncrypted error: ${e.message})")
        }
        return false
    }

    override fun internalVerifyPassword(sourceFile: BaseFileBean, password: String?): Boolean {
        if (sourceFile.mData.isNullOrEmpty()) {
            return false
        }
        try {
            var zFile: ZipFile? = get4JZipFile(sourceFile.mData!!, password)
                    ?: return false
            val fileHeaders: MutableList<FileHeader?>? = zFile!!.fileHeaders
            if (fileHeaders.isNullOrEmpty()) {
                return false
            }
            var fileHeader: FileHeader? = null
            var tmpHeader: FileHeader? = null
            loop@ for (i in (fileHeaders.size - 1) downTo 0) {
                val header = fileHeaders[i]
                if ((header is FileHeader) && !header.isDirectory) {
                    if (tmpHeader == null) {
                        tmpHeader = header
                    }
                    if (header.uncompressedSize > 0) {
                        fileHeader = header
                        break@loop
                    }
                }
            }
            if (fileHeader == null) {
                fileHeader = tmpHeader
            }
            if ((fileHeader?.isEncrypted == true) && verifyZipFileHeaderPassword(zFile, fileHeader)) {
                return true
            }
            return false
        } catch (e: ZipException) {
            Log.e(TAG, "verifyPassword error: ${e.message})")
            return false
        }
    }

    private fun verifyZipFileHeaderPassword(zFile: ZipFile, fileHeader: FileHeader): Boolean {
        var isPass = true
        var input: InputStream? = null
        try {
            input = zFile.getInputStream(fileHeader)
            val b = ByteArray(4 * 4096)
            val readNum = input.read(b)
            if (readNum == -1) {
                Log.v(TAG, "verifyZipFileHeaderPsd check finish")
            }
        } catch (e: Exception) { //Most probably wrong password
            isPass = false
            Log.e(TAG, "verifyZipFileHeaderPsd failed: ${e.message}")
        } finally {
            try {
                input?.close()
            } catch (e: Exception) {
            }
        }
        return isPass
    }

    private fun get4JZipFile(path: String, password: String? = null): ZipFile? {
        try {
            val zFile = ZipFile(path)
            zFile.charset = Charset.forName("UTF8")
            if (zFile.isValidZipFile) {
                if (password.isNullOrEmpty().not() && zFile.isEncrypted) {
                    zFile.setPassword(password!!.toCharArray())
                }
                return zFile
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get4JZipFile: ${e.message})")
        }
        return null
    }

    private fun getJavaZipFile(path: String, charset: String? = null): java.util.zip.ZipFile? {
        try {
            var nameCharset = charset
            if (charset.isNullOrEmpty()) {
                nameCharset = "gbk"
            }
            return java.util.zip.ZipFile(path, Charset.forName(nameCharset))
        } catch (e: Exception) {
            Log.e(TAG, "Failed to getJavaZipFile: ${e.message})")
        }
        return null
    }

    override fun checkIsSupport(sourceFile: BaseFileBean): Boolean {
        val zipFile = ZipFile(File(sourceFile.mData))
        var unCompresssize = 0L
        for (i in (zipFile.fileHeaders.size - 1) downTo 0) {
            val header = zipFile.fileHeaders[i]
            if ((header is FileHeader) && !header.isDirectory) {
                unCompresssize += header.uncompressedSize
            }
        }
        if (unCompresssize == 0L) {
            return false
        }
        return true
    }
}