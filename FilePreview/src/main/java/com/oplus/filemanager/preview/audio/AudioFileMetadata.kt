/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AudioFileMetadata.kt
 * Description:
 *     The loaded media metadata from audio file.
 *
 * Version: 1.0
 * Date: 2024-10-09
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-10-09   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.audio

import android.graphics.Bitmap

internal data class AudioFileMetadata(
    val fileName: String?,
    val artist: String?,
    val coverImage: Bitmap?
)