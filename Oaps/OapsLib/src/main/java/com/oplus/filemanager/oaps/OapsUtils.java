/***********************************************************
 * * Copyright (C), 2008-2019 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:OapsUtils.java
 * * Description:
 * * Version:1.0
 * * Date :2019/5/23
 * * Author:********
 * * OPLUS Java File Skip Rule:MethodLength,NestedBranchDepth,IllegalCatch
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *
 ****************************************************************/
package com.oplus.filemanager.oaps;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.text.BidiFormatter;
import android.text.TextUtils;

import com.cdo.oaps.OapsConstants;
import com.cdo.oaps.api.Oaps;
import com.cdo.oaps.api.callback.Callback;
import com.filemanager.common.MyApplication;
import com.filemanager.common.R;
import com.filemanager.common.compat.FeatureCompat;
import com.filemanager.common.compat.PropertyCompat;
import com.filemanager.common.thread.FileRunnable;
import com.filemanager.common.thread.ThreadManager;
import com.filemanager.common.thread.ThreadPriority;
import com.filemanager.common.thread.ThreadType;
import com.filemanager.common.utils.AppUtils;
import com.filemanager.common.utils.HttpUtils;
import com.filemanager.common.utils.IdentifierManager;
import com.filemanager.common.utils.KtAppUtils;
import com.filemanager.common.utils.Log;
import com.filemanager.common.utils.PreferencesUtils;
import com.filemanager.common.utils.StatisticsUtils;
import com.google.protobuf.InvalidProtocolBufferException;

import java.util.List;
import java.util.Locale;

public class OapsUtils {
    private static final String TAG = "OapsUtils";

    /**
     * OAPS define , oaps sdk key
     */
    public static final String OAPS_SKEY = "75";
    /**
     * OAPS define , oaps sdk secret
     */
    public static final String OAPS_ORIGIN = "0028cc114f5c021239a4ce21f80c2203";
    /**
     * OAPS define , oaps default open link
     */
    public static final String OAPS_HOME = "oaps://mk/home";
    /**
     * OAPS topic , oaps default open link
     */
    public static final String OAPS_TOPIC = "oaps://mk/recapp";

    /**
     * Server define , Home page entrance ad id
     */
    public static final String OAPS_HOME_ID = "59570";
    /**
     * Server define , category apk page entrance ad id
     */
    public static final String OAPS_APK_ID = "59571";
    public static final int LOCAL_ERROR = -99;

    private static final int RESULT_SUCCESS = 0;
    /**
     * Server define , Server Service API Version
     */
    private static final int SERVER_API_VER = 1;
    /**
     * Server define , Server Service Version Code
     */
    private static final int SERVER_SDK_VCODE = 100;
    /**
     * Server define , Server Service Version Name
     */
    private static final String SERVER_SDK_VNAME = "1.0.0";
    private static final String SP_OAPS_CONFIG = "filemanager_oaps_config";
    private static final String SP_OAPS_CONFIG_OPEN_ID = "open_entry_id";
    private static final String SP_OAPS_CONFIG_HOME_OPEN_URL = "open_home_entry_url";
    private static final String SP_OAPS_CONFIG_APK_OPEN_URL = "open_apk_entry_url";
    /**
     * OAPS define , oaps package name
     */
    private static final String OPPO_APPSTORE_PKG = "com.oppo.market";
    private static final String OPPO_APPSTORE_PKG_NEW = "com.heytap.market";
    private static final String OPPO_SYSTEM_ID = "20016";
    /**
     * Server define , FileManager app id
     */
    private static final String OPPO_AD_ID = "59558";
    private static final String URL_PRAMS_START = "&t=";
    private static final String NET_NO = "NO";
    private static final String NET_WIFI = "WIFI";
    private static final String NET_2G = "2G";
    private static final String NET_3G = "3G";
    private static final String NET_4G = "4G";
    private static final String NET_5G = "5G";
    private static final String NET_OTHER = "UNKNOWN";


    public static void startHome(Context context, Callback inputCallback) {
        Callback callback = getCallback(inputCallback);
        Oaps.newBuilder()
                .setContext(context)
                .setCallback(callback)
                .setScheme(Oaps.SCHEME_OAPS)
                .setHost(Oaps.HOST_MK)
                .setPath(Oaps.PATH_HOME)
                .setModule(OapsConstants.MK_HOME)
                .setGoBack()
                .build()
                .request();
    }

    /**
     * used Oaps to open appstore
     */
    public static void startOaps(Context context, String oaps, Callback inputCallback) {
        Callback callback = getCallback(inputCallback);
        if (callback == null) {
            return;
        }
        Callback.Response err = new Callback.Response();
        err.setCode(LOCAL_ERROR);
        err.setData(null);
        if (null == context) {
            Log.w(TAG, "cancel, context cannot empty " + oaps);
            callback.onResponse(err);
            return;
        }
        if (!checkAppStoreEnabled(context)) {
            return;
        }
        if (Oaps.support(context, oaps)) {
            Log.v(TAG, "oaps = " + oaps);
            Oaps.newBuilder().setContext(context).setRequestUrl(oaps).setCallback(callback).build().request();
        } else {
            Log.w(TAG, "not support oaps = " + oaps);
            callback.onResponse(err);
            startHome(context, getCallback(null));
        }
    }

    public static void requestData(DevScreenParam screenParam, HttpUtils.MyHttpCallback callback) {
        Log.d(TAG, "request start");
        sendOapsRequest(getADSRequestUrl(null, PropertyCompat.getSSystemRegion()),
                prepareReqData(screenParam), callback);
    }

    private static Callback getCallback(Callback inputCallback) {
        if (inputCallback == null) {
            return new Callback() {
                @Override
                public void onResponse(Response response) {
                    int respCode = response.getCode();
                    Log.i(TAG, "Oaps Callback onResponse:" + respCode);
                    if (respCode == OapsConstants.SUCCESS) {
                        Log.i(TAG, "Oaps Callback success");
                    } else {
                        Log.w(TAG, "Oaps Callback failed");
                    }
                }
            };
        }
        return inputCallback;
    }

    private static String getADSRequestUrl(String inputUrl, String region) {
        String baseUrl = "";
        switch (region) {
            case "IN":
                baseUrl = MultiChannelApi.getInAdsUri();
                break;
            case "ID":
            case "PH":
            case "MY":
            case "TH":
            case "VN":
                baseUrl = MultiChannelApi.getIdAdsUri();
                break;
            default:
                break;
        }
        if (TextUtils.isEmpty(baseUrl)) {
            return "";
        } else {
            String url = ((null == inputUrl) ? "" : inputUrl);
            return baseUrl + url;
        }
    }

    @SuppressLint({"MethodLength", "IllegalCatch"})
    private static byte[] prepareReqData(DevScreenParam screenParam) {
        try {
            Adstrategy.PkgInfo pkgInfo = Adstrategy.PkgInfo.newBuilder()
                    .setPkgName(AppUtils.getPackageName())
                    .setVerCode(AppUtils.getVersionCode())
                    .setVerName(AppUtils.getVersionName())
                    .build();
            Adstrategy.AppInfo appinfo = Adstrategy.AppInfo.newBuilder()
                    .setAppId(OPPO_AD_ID)
                    .setSystemId(OPPO_SYSTEM_ID)
                    .setPkgInfo(pkgInfo)
                    .build();


            Adstrategy.SdkInfo sdkInfo = Adstrategy.SdkInfo.newBuilder()
                    .setVerCode(SERVER_SDK_VCODE)
                    .setVerName(SERVER_SDK_VNAME)
                    .build();


            Adstrategy.LocalInfo localInfo = Adstrategy.LocalInfo.newBuilder()
                    .setCountry(Locale.getDefault().getCountry())
                    .setLanguage(Locale.getDefault().getLanguage())
                    .setRegion(PropertyCompat.getSSystemRegion())
                    .build();

            IdentifierManager.initSdk(MyApplication.getAppContext());
            IdentifierManager.clearSdk(MyApplication.getAppContext());

            Adstrategy.DevOs devOs = Adstrategy.DevOs.newBuilder()
                    .setOsVer(PropertyCompat.getSColorOSVersion())
                    .setAnVer(AppUtils.getVersionRelease())
                    .setRomVer(BidiFormatter.getInstance().unicodeWrap(Build.DISPLAY))
                    .build();
            Adstrategy.DevScreen devScreen = Adstrategy.DevScreen.newBuilder()
                    .setDensity(screenParam.mDensity)
                    .setHeight(screenParam.mDisplayHeight)
                    .setWidth(screenParam.mDisplayWidth)
                    .build();
            Adstrategy.DevStatus devStatus = Adstrategy.DevStatus.newBuilder()
                    .setNetType(getNetType())
                    .build();
            Adstrategy.DevInfo devInfo = Adstrategy.DevInfo.newBuilder()
                    .setDevOs(devOs)
                    .setDevScreen(devScreen)
                    .setDevStatus(devStatus)
                    .setBrand(AppUtils.getBrand())
                    .setMaker(AppUtils.getMaker())
                    .setModel(AppUtils.getModel())
                    .build();


            Adstrategy.AppStoreInfo.Builder appInfoBuilder = Adstrategy.AppStoreInfo.newBuilder();
            String verName = "";
            int verCode = 0;
            verName = AppUtils.getAppVersionNameByPkgName(OPPO_APPSTORE_PKG_NEW);
            verCode = AppUtils.getAppVersionCode(OPPO_APPSTORE_PKG_NEW);
            if (TextUtils.isEmpty(verName)) {
                verName = AppUtils.getAppVersionNameByPkgName(OPPO_APPSTORE_PKG);
                verCode = AppUtils.getAppVersionCode(OPPO_APPSTORE_PKG);
            }
            if (!TextUtils.isEmpty(verName)) {
                appInfoBuilder.setVerName(verName);
                appInfoBuilder.setVerCode(verCode);
            }
            Adstrategy.AppStoreInfo appStoreInfo = appInfoBuilder.build();


            Adstrategy.StrategyRequest requestData = Adstrategy.StrategyRequest.newBuilder()
                    .setAppInfo(appinfo)
                    .setApiVer(SERVER_API_VER)
                    .setAppStoreInfo(appStoreInfo)
                    .setDevInfo(devInfo)
                    .setLocalInfo(localInfo)
                    .setSdkInfo(sdkInfo)
                    .build();

            return requestData.toByteArray();
        } catch (NullPointerException e) {
            Log.e(TAG, e.getMessage());
        }
        return null;
    }

    @SuppressLint({"NestedBranchDepth", "MethodLength"})
    private static void sendOapsRequest(String netUrl, byte[] pbbyte, HttpUtils.MyHttpCallback myHttpCallback) {
        if ((pbbyte == null) || (TextUtils.isEmpty(netUrl))) {
            return;
        }
        HttpUtils.HttpCallbackBytesListener callbackListener = new HttpUtils.HttpCallbackBytesListener() {
            @Override
            public void onFinish(byte[] response) {
                if (response != null) {
                    Adstrategy.StrategyResponse respModel = null;
                    try {
                        respModel = Adstrategy.StrategyResponse.parseFrom(response);
                    } catch (InvalidProtocolBufferException e) {
                        Log.e(TAG, "response Error");
                        StatisticsUtils.onCommon(MyApplication.getAppContext(), StatisticsUtils.OAPS_REQUEST_SERVER_CONFIG_ERROR_COUNT);
                        if (myHttpCallback != null) {
                            myHttpCallback.onRequestError();
                        }
                    }
                    if (respModel != null) {
                        Log.d(TAG, "response Success " + respModel.getCode());
                        if (respModel.getCode() == RESULT_SUCCESS) {
                            StatisticsUtils.onCommon(MyApplication.getAppContext(), StatisticsUtils.OAPS_REQUEST_SERVER_CONFIG_SUCC_COUNT);
                            List<Adstrategy.PosIdInfo> infoList = respModel.getPosIdInfoListList();//respModel.getPosIdInfoList(0);
                            StringBuilder posIdList = new StringBuilder();
                            if (!infoList.isEmpty()) {
                                for (Adstrategy.PosIdInfo info : infoList) {
                                    List<Adstrategy.ChannelPosInfo> posinfoList = info.getChannelPosInfoListList();
                                    for (Adstrategy.ChannelPosInfo posInfo : posinfoList) {
                                        OapsUtils.saveOapsOpenUrl(posInfo.getPosId(), posInfo.getUrl());
                                    }
                                    posIdList.append(info.getAdPosId());
                                    posIdList.append(";");
                                }
                                OapsUtils.saveOpenAdPosId(posIdList.toString());
                            } else {
                                posIdList.append("NA");
                                OapsUtils.saveOpenAdPosId(posIdList.toString());
                            }
                            if (myHttpCallback != null) {
                                myHttpCallback.onRequestSuccess();
                            }
                        } else {
                            Log.w(TAG, "response Error");
                            StatisticsUtils.onCommon(MyApplication.getAppContext(), StatisticsUtils.OAPS_REQUEST_SERVER_CONFIG_ERROR_COUNT);
                            if (myHttpCallback != null) {
                                myHttpCallback.onRequestError();
                            }
                        }
                    }
                } else {
                    Log.w(TAG, "response null is error");
                    StatisticsUtils.onCommon(MyApplication.getAppContext(), StatisticsUtils.OAPS_REQUEST_SERVER_CONFIG_ERROR_COUNT);
                    if (myHttpCallback != null) {
                        myHttpCallback.onRequestError();
                    }
                }
            }

            @Override
            public void onError(Exception e) {
                Log.w(TAG, "request onFailure");
                StatisticsUtils.onCommon(MyApplication.getAppContext(), StatisticsUtils.OAPS_REQUEST_SERVER_CONFIG_FAIL_COUNT);
                if (myHttpCallback != null) {
                    myHttpCallback.onRequestFailed();
                }
                if (myHttpCallback != null) {
                    myHttpCallback.onRequestFinished();
                }
            }
        };
        HttpUtils.doPostByPB(netUrl, callbackListener, pbbyte);
    }

    @SuppressLint("IllegalCatch")
    public static boolean isOapsEnable() {
        if (!FeatureCompat.getSIsExpRom()) {
            return false;
        }
        //Determine whether the region supports
        if (TextUtils.isEmpty(getADSRequestUrl(null, PropertyCompat.getSSystemRegion()))) {
            return false;
        }

        PackageManager pm = MyApplication.getAppContext().getPackageManager();
        try {
            ApplicationInfo appInfo2 = pm.getApplicationInfo(OPPO_APPSTORE_PKG_NEW, 0);
            if (appInfo2.enabled) {
                return true;
            }
        } catch (Throwable t) {
            Log.e(TAG, t.getMessage());
        }
        try {
            ApplicationInfo appInfo = pm.getApplicationInfo(OPPO_APPSTORE_PKG, 0);
            if (appInfo.enabled) {
                return true;
            }
        } catch (Throwable t) {
            Log.e(TAG, t.getMessage());
        }
        return false;
    }

    public static boolean isShowApkEntrance() {
        return checkShowEntrance(1);
    }

    public static boolean isShowHomeEntrance() {
        return checkShowEntrance(0);
    }

    private static boolean checkShowEntrance(int entranceType) {
        if (isOapsEnable()) {
            String openEntranceid = PreferencesUtils.getString(SP_OAPS_CONFIG, SP_OAPS_CONFIG_OPEN_ID, OAPS_APK_ID);
            if (openEntranceid == null) {
                return false;
            }
            switch (entranceType) {
                //home entry
                case 0:
                    if (openEntranceid.contains(OAPS_HOME_ID)) {
                        return true;
                    }
                    break;
                //apk entry
                case 1:
                    if (openEntranceid.contains(OAPS_APK_ID)) {
                        return true;
                    }
                    break;
                default:
            }
        }

        return false;
    }


    public static void saveOpenAdPosId(final String id) {
        if (null != id) {
            Runnable runnable = () -> PreferencesUtils.put(SP_OAPS_CONFIG, SP_OAPS_CONFIG_OPEN_ID, id);
            ThreadManager.getSThreadManager().execute(new FileRunnable(runnable, TAG + "_saveOpenAdPosId", null),
                    ThreadType.NORMAL_THREAD, ThreadPriority.HIGH);
        }
    }


    public static String getOapsOpenUrl(String posId, String title) {
        StringBuilder openUrl = new StringBuilder();
        switch (posId) {
            case OAPS_HOME_ID:
                openUrl.append(PreferencesUtils.getString(SP_OAPS_CONFIG,
                        SP_OAPS_CONFIG_HOME_OPEN_URL, OapsUtils.OAPS_HOME));
                break;
            case OAPS_APK_ID:
                openUrl.append(PreferencesUtils.getString(SP_OAPS_CONFIG,
                        SP_OAPS_CONFIG_APK_OPEN_URL, OapsUtils.OAPS_HOME));
                break;
            default:
                openUrl.append(OapsUtils.OAPS_HOME);
                break;
        }
        return preparePrams(title, openUrl);
    }

    private static String preparePrams(String title, StringBuilder openUrl) {
        if (openUrl.toString().endsWith(URL_PRAMS_START)) {
            if ((null != title) && (!title.isEmpty())) {
                openUrl.append(title);
            }
        }
        return openUrl.toString();
    }

    public static void saveOapsOpenUrl(String posId, final String link) {
        if (null != link) {
            Runnable runnable = () -> {
                switch (posId) {
                    case OAPS_HOME_ID:
                        PreferencesUtils.put(SP_OAPS_CONFIG, SP_OAPS_CONFIG_HOME_OPEN_URL, link);
                        break;
                    case OAPS_APK_ID:
                        PreferencesUtils.put(SP_OAPS_CONFIG, SP_OAPS_CONFIG_APK_OPEN_URL, link);
                        break;
                    default:
                        break;
                }
            };

            ThreadManager.getSThreadManager().execute(new FileRunnable(runnable, TAG + "_saveOapsOpenUrl", null),
                    ThreadType.NORMAL_THREAD, ThreadPriority.HIGH);
        }
    }

    public static class DevScreenParam {
        public float mDensity;
        public int mDisplayHeight;
        public int mDisplayWidth;

        public DevScreenParam(float density, int displayHeight, int displayWidth) {
            this.mDensity = density;
            this.mDisplayHeight = displayHeight;
            this.mDisplayWidth = displayWidth;
        }
    }

    public static Adstrategy.DevStatus.ConnectionType getNetType() {
        switch (getNetworkType()) {
            case NET_WIFI:
                return Adstrategy.DevStatus.ConnectionType.WIFI;
            case NET_2G:
                return Adstrategy.DevStatus.ConnectionType.CELL_2G;
            case NET_3G:
                return Adstrategy.DevStatus.ConnectionType.CELL_3G;
            case NET_4G:
                return Adstrategy.DevStatus.ConnectionType.CELL_4G;
            case NET_OTHER:
                return Adstrategy.DevStatus.ConnectionType.NEW_TYPE;
            default:
                return Adstrategy.DevStatus.ConnectionType.CONNECTION_UNKNOWN;
        }
    }

    @SuppressLint("MissingPermission")
    public static String getNetworkType() {
        Context context = MyApplication.getAppContext();
        ConnectivityManager connManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (null == connManager) {
            return NET_NO;
        }
        NetworkInfo activeNetInfo = connManager.getActiveNetworkInfo();
        if ((activeNetInfo == null) || (!activeNetInfo.isAvailable())) {
            return NET_NO;
        }
        NetworkInfo wifiInfo = connManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
        if (null != wifiInfo) {
            NetworkInfo.State state = wifiInfo.getState();
            if (null != state) {
                if ((state == NetworkInfo.State.CONNECTED) || (state == NetworkInfo.State.CONNECTING)) {
                    return NET_WIFI;
                }
            }
        }
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
        int networkType = telephonyManager.getNetworkType();
        switch (networkType) {
            case TelephonyManager.NETWORK_TYPE_GPRS:
            case TelephonyManager.NETWORK_TYPE_CDMA:
            case TelephonyManager.NETWORK_TYPE_EDGE:
            case TelephonyManager.NETWORK_TYPE_1xRTT:
            case TelephonyManager.NETWORK_TYPE_IDEN:
                return NET_2G;
            case TelephonyManager.NETWORK_TYPE_EVDO_A:
            case TelephonyManager.NETWORK_TYPE_UMTS:
            case TelephonyManager.NETWORK_TYPE_EVDO_0:
            case TelephonyManager.NETWORK_TYPE_HSDPA:
            case TelephonyManager.NETWORK_TYPE_HSUPA:
            case TelephonyManager.NETWORK_TYPE_HSPA:
            case TelephonyManager.NETWORK_TYPE_EVDO_B:
            case TelephonyManager.NETWORK_TYPE_EHRPD:
            case TelephonyManager.NETWORK_TYPE_HSPAP:
                return NET_3G;
            case TelephonyManager.NETWORK_TYPE_LTE:
                return NET_4G;
            default:
                return NET_OTHER;
        }
    }

    public static boolean checkAppStoreEnabled(Context context) {
        if (AppUtils.isAppInstalledByPkgName(context, OPPO_APPSTORE_PKG)) {
            return KtAppUtils.INSTANCE.checkAppEnabledWithDialog(context,
                    OPPO_APPSTORE_PKG,
                    R.string.app_store_disable_message);
        } else if (AppUtils.isAppInstalledByPkgName(context, OPPO_APPSTORE_PKG_NEW)) {
            return KtAppUtils.INSTANCE.checkAppEnabledWithDialog(context,
                    OPPO_APPSTORE_PKG_NEW,
                    R.string.app_store_disable_message);
        } else {
            return false;
        }
    }
}