/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :
 * * Description : 文档排序规则
 * * Version     : 1.0
 * * Date        : 2024/3/21
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.document.utils

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.sort.LastModifiedComparatorGenerator
import com.filemanager.common.sort.LastOpenTimeComparatorGenerator
import com.filemanager.common.sort.NameToPinyinComparatorGenerator
import com.filemanager.common.sort.SizeComparatorGenerator
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.TypeComparatorGenerator
import com.filemanager.common.utils.Log
import java.util.Collections

object DocumentSortHelper {

    const val TAG = "DocumentSortHelper"

    fun sortFiles(files: List<BaseFileBean>, order: Int, isDesc: Boolean) {
        try {
            Collections.sort(files, getComparator(order, isDesc))
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, e.message)
        }
    }

    private fun getComparator(order: Int, isDesc: Boolean = true): Comparator<BaseFileBean>? {
        Log.d(TAG, "getComparatorCategory order = $order isDesc = $isDesc")
        when (order) {
            SortHelper.FILE_NAME_ORDER -> return NameToPinyinComparatorGenerator.getCategoryComparator(isDesc)

            SortHelper.FILE_TYPE_ORDER -> {
                TypeComparatorGenerator.setCategoryType(CategoryHelper.CATEGORY_DOC)
                return TypeComparatorGenerator.getComparator(FileExtensionUtils.tabTitleSuffixList, isDesc)
            }

            SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER -> return SizeComparatorGenerator.getComparator(isDesc)

            SortHelper.FILE_TIME_REVERSE_ORDER -> return LastModifiedComparatorGenerator.getComparator(isDesc)

            SortHelper.FILE_LAST_OPEN_TIME_ORDER -> return LastOpenTimeComparatorGenerator.getComparator(isDesc)
        }
        return null
    }
}