/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentCardUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/13 15:02
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/13       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.utils

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.google.gson.Gson
import com.oplus.filemanager.cardwidget.recent.data.RecentCard

class RecentCardUtils {

    private val cacheRecentLists = arrayListOf<RecentCard>()

    fun saveCard(data: RecentCard) {
        Log.d(TAG, "saveCard -> data = $data")
        PreferencesUtils.put(SP_NAME, data.cardId, Gson().toJson(data))
        refreshCacheList()
    }

    fun deleteCardById(id: String) {
        Log.d(TAG, "deleteCardById -> id = $id")
        val sp = PreferencesUtils.getPreferences(SP_NAME)
        val editor = sp?.edit()
        editor?.remove(id)
        editor?.apply()
        refreshCacheList()
    }

    fun getCacheCardDataByCardId(cardId: String): RecentCard? {
        return getCacheCardDataByCardId(MyApplication.sAppContext, cardId)
    }

    fun getCacheCardDataByCardId(context: Context, cardId: String): RecentCard? {
        val json = PreferencesUtils.getString(context, SP_NAME, cardId)
        Log.d(TAG, "getCacheCardDataByCardId -> cardId = $cardId; json = $json")
        if (json.isNullOrEmpty()) {
            return null
        }
        return Gson().fromJson(json, RecentCard::class.java).apply {
            this.cardId = cardId
        }
    }

    fun getCacheLists(): List<RecentCard> {
        if (cacheRecentLists.isNotEmpty()) {
            Log.d(TAG, "getCacheLists -> cacheRecentLists = $cacheRecentLists")
            return cacheRecentLists
        }
        refreshCacheList()
        return cacheRecentLists
    }

    fun switchCategory(cardId: String, type: Int, name: String) {
        val recentCard = getCacheCardDataByCardId(cardId)
        Log.d(TAG, "switchCategory -> cardId = $cardId ; type = $type ; recentData = $recentCard")
        recentCard?.type = type
        recentCard?.name = name
        PreferencesUtils.put(SP_NAME, cardId, Gson().toJson(recentCard))
        refreshCacheList()
    }

    private fun refreshCacheList() {
        cacheRecentLists.clear()
        val sp = PreferencesUtils.getPreferences(SP_NAME)
        val all = sp?.all ?: return
        all.forEach { entry ->
            val dataJson = sp.getString(entry.key, null)
            Log.d(TAG, "refreshCacheList -> all = ${all.size} ; key = ${entry.key}; dataJson = $dataJson")
            if (dataJson.isNullOrEmpty().not()) {
                val data = Gson().fromJson(dataJson, RecentCard::class.java)
                data.cardId = entry.key
                cacheRecentLists.add(data)
            }
        }
    }

    fun clearAll() {
        Log.d(TAG, "clearAll")
        cacheRecentLists.clear()
        val sp = PreferencesUtils.getPreferences(SP_NAME)
        sp?.edit()?.clear()?.apply()
    }

    companion object {
        private const val TAG = "RecentCardUtils"
        private const val SP_NAME = "filemanager_recent_card"
        val instance: RecentCardUtils by lazy { RecentCardUtils() }
    }
}