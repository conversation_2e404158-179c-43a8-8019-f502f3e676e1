/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.album
 * * Version     : 1.0
 * * Date        : 2020/7/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.filerefactor.ui.album

import com.coloros.filemanager.BaseTest
import com.coloros.filemanager.TestUtils
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.wrapper.ImageFileWrapper
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class AlbumFragmentViewModelTest : BaseTest() {
    private val mAlbumFragmentViewModel = com.oplus.filemanager.category.album.ui.AlbumFragmentViewModel()

    @Before
    override fun setUp() {
        mContext = RuntimeEnvironment.application
        val file1 = ImageFileWrapper(0, "", 0, 0, 1, 0, 0, "")
        val file2 = ImageFileWrapper(0, "", 0, 0, 2, 0, 0, "")
        val file3 = ImageFileWrapper(0, "", 0, 0, 3, 0, 0, "")
        val list = ArrayList<ImageFileWrapper>()
        list.add(file1)
        list.add(file2)
        list.add(file3)
        mAlbumFragmentViewModel.mUiState.value = BaseUiModel(list, mAlbumFragmentViewModel.mModeState,
                ArrayList(), HashMap())

    }

    @Test
    @Throws(Exception::class)
    fun should_clickToolbarSelectAll_when_view_model_fun_clickToolbarSelectAll() {
        mAlbumFragmentViewModel.mUiState.value?.mSelectedList?.clear()
        mAlbumFragmentViewModel.clickToolbarSelectAll()
        Assert.assertEquals(mAlbumFragmentViewModel.mUiState.value?.mFileList?.size, mAlbumFragmentViewModel.mUiState.value?.mSelectedList?.size)
        mAlbumFragmentViewModel.clickToolbarSelectAll()
        Assert.assertEquals(0, mAlbumFragmentViewModel.mUiState.value?.mSelectedList?.size)
        mAlbumFragmentViewModel.mUiState.value?.mSelectedList?.add(mAlbumFragmentViewModel.mUiState.value?.mFileList?.get(0)?.getId()
                ?: 0)
        mAlbumFragmentViewModel.clickToolbarSelectAll()
        Assert.assertEquals(mAlbumFragmentViewModel.mUiState.value?.mFileList?.size, mAlbumFragmentViewModel.mUiState.value?.mSelectedList?.size)
    }

    @Test
    @Throws(Exception::class)
    fun should_pressBack_when_view_model_fun_pressBack() {
        mAlbumFragmentViewModel.mModeState.mListModel.value = KtConstants.LIST_SELECTED_MODE
        TestUtils.resetChangeModeClickTime()
        var result = mAlbumFragmentViewModel.pressBack()
        Assert.assertEquals(true, result)
        Assert.assertEquals(KtConstants.LIST_NORMAL_MODE, mAlbumFragmentViewModel.mModeState.mListModel.value)
        result = mAlbumFragmentViewModel.pressBack()
        Assert.assertEquals(false, result)
        mAlbumFragmentViewModel.mModeState.mListModel.value = KtConstants.LIST_NORMAL_MODE
        result = mAlbumFragmentViewModel.pressBack()
        Assert.assertEquals(false, result)
        Assert.assertEquals(KtConstants.LIST_NORMAL_MODE, mAlbumFragmentViewModel.mModeState.mListModel.value)
    }

    @Test
    @Throws(Exception::class)
    fun should_change_scan_view_model_when_click_scanModeItem() {
        var initScanMode = mAlbumFragmentViewModel.mBrowseModeState.value
        Assert.assertEquals(KtConstants.SCAN_MODE_GRID, initScanMode)
        mAlbumFragmentViewModel.clickScanModeItem()
        initScanMode = mAlbumFragmentViewModel.mBrowseModeState.value
        Assert.assertEquals(KtConstants.SCAN_MODE_LIST, initScanMode)
        mAlbumFragmentViewModel.clickScanModeItem()
        initScanMode = mAlbumFragmentViewModel.mBrowseModeState.value
        Assert.assertEquals(KtConstants.SCAN_MODE_GRID, initScanMode)
    }

    @After
    override fun tearDown() {
        mContext = null
    }

}