/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : ServerConfigTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/2/20 17:09
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/2/20       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.data.utils

import org.junit.Assert
import org.junit.Test

class ServerConfigTest {

    @Test
    fun testInitEnvironment() {
        ServerConfig.getInstance().initReleaseEnvironment()
        val url = ServerConfig.getInstance().apiUrl
        Assert.assertEquals(ServerConfig.HOST_API_URL_RELEASE, url)

        val key = ServerConfig.getInstance().apiKey
        Assert.assertEquals(ServerConfig.API_KEY_RELEASE, key)

        val secret = ServerConfig.getInstance().apiSecret
        Assert.assertEquals(ServerConfig.API_SECRET_RELEASE, secret)

        val openUrl = ServerConfig.getInstance().kdocsOpenUrl
        Assert.assertEquals(ServerConfig.HOST_KDOCS_OPEN_URL_RELEASE, openUrl)

        val redirectUrl = ServerConfig.getInstance().kdocsRedirectUrl
        Assert.assertEquals(ServerConfig.HOST_KDOCS_AUTHORIZATION_REDIRECT_URL_RELEASE, redirectUrl)
    }

    @Test
    fun testInitPreReleaseEnvironment() {
        ServerConfig.getInstance().initPreReleaseEnvironment()
        val url = ServerConfig.getInstance().apiUrl
        Assert.assertEquals(ServerConfig.HOST_API_URL_PRE_RELEASE, url)

        val key = ServerConfig.getInstance().apiKey
        Assert.assertEquals(ServerConfig.API_KEY_PRE_RELEASE, key)

        val secret = ServerConfig.getInstance().apiSecret
        Assert.assertEquals(ServerConfig.API_SECRET_PRE_RELEASE, secret)

        val openUrl = ServerConfig.getInstance().kdocsOpenUrl
        Assert.assertEquals(ServerConfig.HOST_KDOCS_OPEN_URL_PRE_RELEASE, openUrl)

        val redirectUrl = ServerConfig.getInstance().kdocsRedirectUrl
        Assert.assertEquals(ServerConfig.HOST_KDOCS_AUTHORIZATION_REDIRECT_URL_PRE_RELEASE, redirectUrl)
    }

    @Test
    fun testInitTestEnvironment() {
        ServerConfig.getInstance().initTestEnvironment()
        val url = ServerConfig.getInstance().apiUrl
        Assert.assertEquals(ServerConfig.HOST_API_URL_TEST, url)

        val key = ServerConfig.getInstance().apiKey
        Assert.assertEquals(ServerConfig.API_KEY_TEST, key)

        val secret = ServerConfig.getInstance().apiSecret
        Assert.assertEquals(ServerConfig.API_SECRET_TEST, secret)

        val openUrl = ServerConfig.getInstance().kdocsOpenUrl
        Assert.assertEquals(ServerConfig.HOST_KDOCS_OPEN_URL_TEST, openUrl)

        val redirectUrl = ServerConfig.getInstance().kdocsRedirectUrl
        Assert.assertEquals(ServerConfig.HOST_KDOCS_AUTHORIZATION_REDIRECT_URL_TEST, redirectUrl)
    }

    @Test
    fun testGetInstance() {
        val instance = ServerConfig.getInstance()
        Assert.assertNotNull(instance)
        Assert.assertEquals(instance, ServerConfig.getInstance())
    }
}