/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.coloros.filemanager.common.viewholder
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/5/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.common.viewholder

import android.content.Context
import android.view.ViewGroup
import com.filemanager.common.MyApplication
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.viewholder.FileDocGridVH
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class FileDocGridVHTest {

    lateinit var mContext: Context

    @Before
    fun setup() {
        mContext = mockk {
            every { resources }.returns(mockk(relaxed = true) {
                every { getDimension(any()) } returns 1.0f
                every { getDimensionPixelSize(any()) } returns 1
            })
            every { getColor(any()) } returns 1
            every { applicationContext }.returns(this)
        }
        MyApplication.init(mContext)
    }

    @Test
    fun `should return 0 when adapt item size if current width is 0`() {
        val lp = mockk<ViewGroup.LayoutParams>()
        val imageView = mockk<FileThumbView>(relaxed = true) {
            every { layoutParams } returns lp
        }
        val fileDocGridVH = mockk<FileDocGridVH>() {
            every { mImg } returns imageView
            every { mImgDefaultWidth } returns 10
            every { adaptItemSize(any()) } answers { callOriginal() }
        }
        fileDocGridVH.adaptItemSize(0)
        assertEquals(0, lp.width)
    }

    @Test
    fun `should return 15 when adapt item size if current width is 10`() {
        val lp = mockk<ViewGroup.LayoutParams>()
        val imageView = mockk<FileThumbView>(relaxed = true) {
            every { layoutParams } returns lp
        }
        val fileDocGridVH = mockk<FileDocGridVH>() {
            every { mImg } returns imageView
            every { mImgDefaultWidth } returns 10
            every { adaptItemSize(any()) } answers { callOriginal() }
        }
        fileDocGridVH.adaptItemSize(10)
        assertEquals(10, lp.width)
        assertEquals(15, lp.height)
    }


    @Test
    fun `should return 15 when adapt item size if current width is 10 and layout params is null`() {
        val lp = mockk<ViewGroup.LayoutParams> {
            width = 10
            height = 15
        }
        val imageView = mockk<FileThumbView>(relaxed = true) {
            every { layoutParams = any() } answers { callOriginal() }
        }
        val fileDocGridVH = mockk<FileDocGridVH>() {
            every { mImg } returns imageView
            every { mImgDefaultWidth } returns 10
            every { adaptItemSize(any()) } answers { callOriginal() }
            every { constructLayoutParams(any(), any()) } returns lp
        }
        fileDocGridVH.adaptItemSize(10)
        assertEquals(10, imageView.layoutParams.width)
        assertEquals(15, imageView.layoutParams.height)
    }
    @Test
    fun `should return 0 when adapt item size if current width is 12 and layout params is null`() {
        val lp = mockk<ViewGroup.LayoutParams> {
            width = 10
            height = 15
        }
        val imageView = mockk<FileThumbView>(relaxed = true) {
            every { layoutParams = any() } answers { callOriginal() }
        }
        val fileDocGridVH = mockk<FileDocGridVH>() {
            every { mImg } returns imageView
            every { mImgDefaultWidth } returns 10
            every { adaptItemSize(any()) } answers { callOriginal() }
            every { constructLayoutParams(any(), any()) } returns lp
        }
        fileDocGridVH.adaptItemSize(12)
        assertEquals(0, imageView.layoutParams.width)
        assertEquals(0, imageView.layoutParams.height)
    }
}