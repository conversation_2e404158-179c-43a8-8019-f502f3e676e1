/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WXEntryActivity
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/18 19:16
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/18       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.wxapi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.filecloudbrowser.IFileCloudBrowser
import com.oplus.filemanager.wechatsdk.WeChatUtils
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import org.json.JSONObject

class WXEntryActivity : Activity(), IWXAPIEventHandler {


    private val fileCloudBrowserAction: IFileCloudBrowser? by lazy {
        Injector.injectFactory<IFileCloudBrowser>()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.w(TAG, "onCreate")
        runCatching {
            if (!WeChatUtils.api.handleIntent(intent, this)) {
                Log.d(TAG, "handleIntent false")
                actionToDrive()
            }
        }.onFailure {
            Log.e(TAG, "handleIntent -> error cause ${it.message}")
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)
        runCatching {
            WeChatUtils.api.handleIntent(intent, this)
        }
    }

    override fun onReq(req: BaseReq?) {
        Log.d(TAG, "onReq -> req = $req")
    }

    override fun onResp(resp: BaseResp?) {
        Log.d(TAG, "onResp -> resp = ${resp.toString()}")
        resp?.let {
            if (it.type == ConstantsAPI.COMMAND_LAUNCH_WX_MINIPROGRAM) {
                val launchMiniProResp = resp as? WXLaunchMiniProgram.Resp
                val extraData = launchMiniProResp?.extMsg
                Log.d(TAG, "onResp -> extraData = $extraData")
                extraData?.let { data ->
                    val respObj = JSONObject(data)
                    val code = respObj.getString(KEY_CODE)
                    val state = respObj.getString(KEY_STATE)
                    Log.d(TAG, "onResp -> code = $code; state = $state")
                    if (code.isNotEmpty() && state.isNotEmpty()) {
                        fileCloudBrowserAction?.saveAuthorizationResult(code, state)
                    }
                    actionToDrive()
                } ?: actionToDrive()
            }
        }
    }

    private fun actionToDrive() {
        Log.d(TAG, "actionToDrive")
        fileCloudBrowserAction?.startCloudDrive(this, CategoryHelper.CATEGORY_TENCENT_DOCS, auth = true)
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.w(TAG, "onDestroy")
    }

    companion object {
        private const val TAG = "WXEntryActivity"
        private const val KEY_CODE = "code"
        private const val KEY_STATE = "state"
    }
}