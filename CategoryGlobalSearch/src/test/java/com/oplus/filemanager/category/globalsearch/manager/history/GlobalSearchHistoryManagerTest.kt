/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.category.globalsearch.manager.history.GlobalSearchHistoryManager
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.manager.history

import android.content.Context
import com.filemanager.common.MyApplication
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchViewModel
import com.oplus.filemanager.provider.SearchHistoryDBHelper
import com.oplus.filemanager.room.model.SearchHistoryEntity
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class GlobalSearchHistoryManagerTest {

    @Before
    fun setup() {
        mockkObject(MyApplication)
        val context: Context = mockk(relaxed = true)
        every { MyApplication.sAppContext }.returns(context)
        mockkStatic(GlobalSearchHistoryManager::class)
        mockkObject(SearchHistoryDBHelper)
    }

    @After
    fun after() {
        unmockkObject(MyApplication)
        unmockkStatic(GlobalSearchHistoryManager::class)
        unmockkObject(SearchHistoryDBHelper)
    }

    @Test
    fun `test loadHistory`() {
        every { GlobalSearchHistoryManager.loadHistory() } answers { callOriginal() }
        every { SearchHistoryDBHelper.getAllSearchHistory() } returns null
        assertEquals(0, GlobalSearchHistoryManager.loadHistory().size)

        val historyList = arrayListOf<SearchHistoryEntity>()
        val history = SearchHistoryEntity()
        historyList.add(history)
        every { SearchHistoryDBHelper.getAllSearchHistory() } returns historyList
        assertEquals(1, GlobalSearchHistoryManager.loadHistory().size)
    }

    @Test
    fun `test addHistory`() {
        every { GlobalSearchHistoryManager.addHistory(any()) } answers { callOriginal() }
        every { SearchHistoryDBHelper.addSearchHistory(any()) } returns 1
        assertEquals(1, GlobalSearchHistoryManager.addHistory(""))

        every { SearchHistoryDBHelper.addSearchHistory(any()) } returns -1
        assertEquals(-1, GlobalSearchHistoryManager.addHistory(""))

        every { SearchHistoryDBHelper.addSearchHistory(any()) } returns null
        assertEquals(-1, GlobalSearchHistoryManager.addHistory(""))
    }

    @Test
    fun `test removeHistory`() {
        every { GlobalSearchHistoryManager.removeHistory(any()) } answers { callOriginal() }
        assertEquals(-1, GlobalSearchHistoryManager.removeHistory(arrayListOf()))

        every { SearchHistoryDBHelper.deleteSearchHistory(any()) } returns 1
        val list = arrayListOf<GlobalSearchViewModel.SearchHistoryModel>()
        list.add(GlobalSearchViewModel.SearchHistoryModel(1, "key", 0))
        assertEquals(1, GlobalSearchHistoryManager.removeHistory(list))
    }

    @Test
    fun `test updateHistory`() {
        every { GlobalSearchHistoryManager.updateHistory(any()) } answers { callOriginal() }

        every { SearchHistoryDBHelper.updateSearchHistory(any(), any()) } returns 1
        val model = GlobalSearchViewModel.SearchHistoryModel(1, "key", 0)
        assertEquals(1, GlobalSearchHistoryManager.updateHistory(model))
    }

    @Test
    fun `test clearHistory`() {
        every { GlobalSearchHistoryManager.clearHistory() } answers { callOriginal() }

        every { SearchHistoryDBHelper.clearHistory() } returns 1
        assertEquals(1, GlobalSearchHistoryManager.clearHistory())
    }
}