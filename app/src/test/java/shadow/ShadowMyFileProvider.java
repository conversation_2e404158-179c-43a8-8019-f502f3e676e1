/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowMyFileProvider.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/8/28
 ** Author: JiaBo Lan
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/8/28      1.0     create
 ****************************************************************/
package shadow;

import android.content.Context;
import android.net.Uri;

import com.oplus.filemanager.provider.MyFileProvider;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.io.File;

@Implements(MyFileProvider.class)
public class ShadowMyFileProvider {
    @Implementation
    public static Uri getUriForFile(Context context, String authority, File file) {
        return null;
    }
}
