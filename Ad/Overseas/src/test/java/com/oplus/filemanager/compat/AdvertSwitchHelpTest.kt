/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AdvertSwitchHelpTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/6/13 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/13       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat

import android.content.Context
import android.provider.Settings
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.compat.AdvertSwitchHelp.DOMESTIC
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test

class AdvertSwitchHelpTest {

    @Test
    fun `should return true when call isShowAd`() {
        //setUp
        mockkStatic(Utils::class)
        mockkStatic(AdvertSwitchHelp::class)
        mockkStatic(Settings.System::class)
        val context = mockk<Context>()
        //given
        every { Utils.isRealmePhone() } returns true
        every { AdvertSwitchHelp.getAdSwitchStatus() } returns true
        every { context.contentResolver } returns mockk()
        every { Settings.System.getInt(any(), any<String>(), any<Int>()) } returns 1
        every { AdvertSwitchHelp.isSupportMachine(context) } returns true
        //when
        val result = AdvertSwitchHelp.isShowAd(context)
        //then
        Assert.assertEquals(true, result)
        //teardown
        unmockkStatic(Utils::class)
        unmockkStatic(AdvertSwitchHelp::class)
        unmockkStatic(Settings.System::class)
    }

    @Test
    fun `should return true when call isShowAd if oppo phone`() {
        //setUp
        mockkStatic(Utils::class)
        mockkStatic(AdvertSwitchHelp::class)
        mockkStatic(Settings.System::class)
        val context = mockk<Context>()
        //given
        every { Utils.isRealmePhone() } returns false
        every { AdvertSwitchHelp.getAdSwitchStatus() } returns true
        every { context.contentResolver } returns mockk()
        every { Settings.System.getInt(any(), any<String>(), any<Int>()) } returns 1
        every { AdvertSwitchHelp.isSupportMachine(context) } returns true
        //when
        val result = AdvertSwitchHelp.isShowAd(context)
        //then
        Assert.assertEquals(true, result)
        //teardown
        unmockkStatic(Utils::class)
        unmockkStatic(AdvertSwitchHelp::class)
        unmockkStatic(Settings.System::class)
    }

    @Test
    fun `should return false when call isShowAd if realme phone`() {
        //setUp
        mockkStatic(Utils::class)
        mockkStatic(AdvertSwitchHelp::class)
        mockkStatic(Settings.System::class)
        val context = mockk<Context>()
        //given
        every { Utils.isRealmePhone() } returns true
        every { AdvertSwitchHelp.getAdSwitchStatus() } returns false
        every { context.contentResolver } returns mockk()
        every { Settings.System.getInt(any(), any<String>(), any<Int>()) } returns 1
        every { AdvertSwitchHelp.isSupportMachine(context) } returns true
        //when
        val result = AdvertSwitchHelp.isShowAd(context)
        //then
        Assert.assertEquals(false, result)
        //teardown
        unmockkStatic(Utils::class)
        unmockkStatic(AdvertSwitchHelp::class)
        unmockkStatic(Settings.System::class)
    }

    @Ignore
    fun `should return true when call getAdSwitchStatus`() {
        //setUp
        mockkStatic(Settings.System::class)
        mockkStatic(PreferencesUtils::class)
        val context = mockk<Context>()
        //given
        every { context.contentResolver } returns mockk()
        every { Settings.System.getInt(any(), any<String>(), any<Int>()) } returns 1
        every { PreferencesUtils.haveKey(any(), any()) } returns false
        mockkObject(AdvertSwitchHelp)
        every { AdvertSwitchHelp.setAdSwitchStatus(any()) } just runs
        //when
        val result = AdvertSwitchHelp.getAdSwitchStatus()
        //then
        Assert.assertEquals(true, result)
        verify { AdvertSwitchHelp.setAdSwitchStatus(any()) }
        //teardown
        unmockkStatic(Settings.System::class)
        unmockkStatic(PreferencesUtils::class)
    }

    @Ignore
    fun `should return right when call getAdSwitchStatus`() {
        //setUp
        mockkStatic(Settings.System::class)
        mockkStatic(PreferencesUtils::class)
        val context = mockk<Context>()
        //given
        every { context.contentResolver } returns mockk()
        every { Settings.System.getInt(any(), any<String>(), any<Int>()) } returns 1
        every { PreferencesUtils.haveKey(any(), any()) } returns true
        mockkObject(AdvertSwitchHelp)
        every { AdvertSwitchHelp.setAdSwitchStatus(any()) } just runs
        every { PreferencesUtils.getBoolean(any(), any()) } returns true
        //when
        val result = AdvertSwitchHelp.getAdSwitchStatus()
        //then
        Assert.assertEquals(true, result)
        verify(inverse = true) { AdvertSwitchHelp.setAdSwitchStatus(any()) }
        //teardown
        unmockkStatic(Settings.System::class)
        unmockkStatic(PreferencesUtils::class)
    }

    @Test
    fun `should return right when call showAdSwitch`() {
        //setUp
        mockkStatic(Utils::class)
        mockkStatic(AdvertSwitchHelp::class)
        val context = mockk<Context>()
        //given
        MyApplication.flavorRegion = DOMESTIC
        every { Utils.isRealmePhone() } returns true
        every { AdvertSwitchHelp.isSupportMachine(context) } returns true
        every { AdvertSwitchHelp.isSupportAdRegion(context) } returns true
        //when
        val result = AdvertSwitchHelp.showAdSwitch(context)
        //then
        Assert.assertEquals(false, result)
        //teardown
        unmockkStatic(Utils::class)
        unmockkStatic(AdvertSwitchHelp::class)
    }

    @Test
    fun `should return false when call showAdSwitch`() {
        //setUp
        mockkStatic(Utils::class)
        mockkStatic(AdvertSwitchHelp::class)
        val context = mockk<Context>()
        //given
        MyApplication.flavorRegion = "Export"
        every { Utils.isRealmePhone() } returns true
        every { AdvertSwitchHelp.isSupportMachine(context) } returns true
        every { AdvertSwitchHelp.isSupportAdRegion(context) } returns true
        //when
        val result = AdvertSwitchHelp.showAdSwitch(context)
        //then
        Assert.assertEquals(true, result)
        //teardown
        unmockkStatic(Utils::class)
        unmockkStatic(AdvertSwitchHelp::class)
    }
}