/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.labelmanager.AddLabelViewModelTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/31
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.labelmanager

import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.dropdrag.SelectionTracker
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import io.mockk.MockKAnnotations
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.coJustRun
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runBlockingTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@Suppress("ExternalMustSpecifyReturnTypeRule")
@RunWith(JUnit4::class)
class AddLabelViewModelTest {

    @MockK
    lateinit var mContext: Context

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        every { mContext.applicationContext }.returns(mContext)
        MyApplication.init(mContext)
        mockkObject(FileLabelDBHelper)
        mockkObject(FileLabelMappingDBHelper)
        mockkStatic(StatisticsUtils::class)
        Dispatchers.setMain(TestCoroutineDispatcher())
    }

    @After
    fun tearDown() {
        unmockkObject(FileLabelDBHelper)
        unmockkObject(FileLabelMappingDBHelper)
        unmockkStatic(StatisticsUtils::class)
        Dispatchers.resetMain()
    }

    @Test
    fun `should execute postValue when showAllLabelsForOther`() = runBlockingTest {
        val viewModel = spyk(AddLabelViewModel())
        val uiValue = mockk<AddLabelViewModel.AddLabelUiModel> {
            coJustRun { otherLabels = arrayListOf() }
            coJustRun { isFromInit = false }
            coEvery { isFromInit } coAnswers { callOriginal() }
        }
        val liveData = mockk<MutableLiveData<AddLabelViewModel.AddLabelUiModel>> {
            coEvery { value } returns uiValue
            coJustRun { postValue(uiValue) }
        }
        coEvery { viewModel.mUiState } returns liveData
        val allLabels = arrayListOf<FileLabelEntity>()
        coEvery { FileLabelDBHelper.getAllLabels() } returns allLabels

        viewModel.showAllLabelsForOther()
        assertFalse(uiValue.isFromInit)
    }

    @Test
    fun `should return 0 when showAllLabelsForOther if source is null`() = runBlockingTest {
        val viewModel = spyk(AddLabelViewModel())
        val uiValue = mockk<AddLabelViewModel.AddLabelUiModel> {
            coEvery { otherLabels = arrayListOf() } coAnswers { callOriginal() }
            coEvery { otherLabels } returns arrayListOf()
            coJustRun { isFromInit = false }
        }
        val liveData = mockk<MutableLiveData<AddLabelViewModel.AddLabelUiModel>> {
            coEvery { value } returns uiValue
            coJustRun { postValue(uiValue) }
        }
        coEvery { viewModel.mUiState } returns liveData
        coEvery { FileLabelDBHelper.getAllLabels() } returns arrayListOf()

        viewModel.showAllLabelsForOther()

        assertEquals(0, uiValue.otherLabels.size)
    }

    @Test
    fun `should execute postValue when showFuzzyMatchedLabelsForOther`() {
        val viewModel = spyk(AddLabelViewModel())
        val name = "search"
        val uiValue = mockk<AddLabelViewModel.AddLabelUiModel> {
            coJustRun { otherLabels = arrayListOf() }
            coJustRun { isFromInit = false }
            coEvery { isFromInit } coAnswers { callOriginal() }
        }
        val liveData = mockk<MutableLiveData<AddLabelViewModel.AddLabelUiModel>> {
            coEvery { value } returns uiValue
            coJustRun { postValue(uiValue) }
        }
        coEvery { viewModel.mUiState } returns liveData
        val allLabels = arrayListOf<FileLabelEntity>()
        coEvery { FileLabelDBHelper.getFileLabelByNameFuzzy(name) } returns allLabels

        viewModel.showFuzzyMatchedLabelsForOther(name)
        assertFalse(uiValue.isFromInit)
    }

    @Test
    fun `should return 0 when showFuzzyMatchedLabelsForOther if source is null`() {
        val viewModel = spyk(AddLabelViewModel())
        val name = "search"
        val uiValue = mockk<AddLabelViewModel.AddLabelUiModel> {
            coEvery { otherLabels = arrayListOf() } coAnswers { callOriginal() }
            coEvery { otherLabels } returns arrayListOf()
            coJustRun { isFromInit = false }
        }
        val liveData = mockk<MutableLiveData<AddLabelViewModel.AddLabelUiModel>> {
            coEvery { value } returns uiValue
            coJustRun { postValue(uiValue) }
        }
        coEvery { viewModel.mUiState } returns liveData
        coEvery { FileLabelDBHelper.getFileLabelByNameFuzzy(name) } returns null

        viewModel.showFuzzyMatchedLabelsForOther(name)

        assertEquals(0, uiValue.otherLabels.size)
    }

    @Test
    fun `should return grid when getRecyclerViewScanMode`() {
        val viewModel = AddLabelViewModel()
        assertEquals(SelectionTracker.LAYOUT_TYPE.GRID, viewModel.getRecyclerViewScanMode())
    }

    @Test
    fun `should return 0 when getRealFileSize`() {
        val viewModel = AddLabelViewModel()
        assertEquals(0, viewModel.getRealFileSize())
    }

    @Ignore
    @Test
    fun `should execute postValue 2 when mappingFileToLabel if checkIfInputLabelChanged is true`() = runBlockingTest {
        val viewModel = spyk(AddLabelViewModel())
        val liveData = mockk<MutableLiveData<Int>> {
            coEvery { value } returns 1
            coJustRun { postValue(any()) }
        }
        coEvery { viewModel.mUpdateLabels } returns liveData
        val labelNames = arrayListOf<String>()
        val filePaths = arrayListOf<BaseFileBean>()
        coEvery { viewModel.mappingFileToLabel(labelNames, filePaths) } coAnswers { callOriginal() }
        viewModel.mappingFileToLabel(arrayListOf(), arrayListOf())
        coVerify { liveData.postValue(1) }
        coVerify { liveData.postValue(2) }
    }

    @Ignore
    @Test
    fun `should execute postValue when mappingFileToLabel if checkIfInputLabelChanged is false`() = runBlockingTest {
        coEvery { FileLabelDBHelper.getAllLabels() } returns arrayListOf()
        val viewModel = spyk(AddLabelViewModel())
        val liveData = mockk<MutableLiveData<Int>> {
            coEvery { value } returns 1
            coJustRun { postValue(any()) }
        }
        coEvery { viewModel.mUpdateLabels } returns liveData
        val labelNames = arrayListOf<String>()
        val filePaths = arrayListOf<BaseFileBean>()
        val inputLabels = arrayListOf<FileLabelEntity>()
        val name = "name"
        val labelEntity = FileLabelEntity(1, name, 1, 1, 0, 0)
        inputLabels.add(labelEntity)

        coEvery { FileLabelMappingDBHelper.getCommonFileLabelsByPaths(labelNames) } returns inputLabels

        coEvery { viewModel.initData(filePaths) } coAnswers { callOriginal() }
        coEvery { viewModel.mappingFileToLabel(labelNames, filePaths) } coAnswers { callOriginal() }

        coEvery { FileLabelDBHelper.getFileLabelByName(name) } returns labelEntity

        viewModel.initData(filePaths)
        viewModel.mappingFileToLabel(labelNames, filePaths)
        coVerify { liveData.postValue(1) }
        coVerify { liveData.postValue(2) }
    }

    @Test
    fun `should execute label click when mappingFileToLabel if checkIfInputLabelChanged is true`() = runBlockingTest {
        val viewModel = spyk(AddLabelViewModel())
        val liveData = mockk<MutableLiveData<Int>> {
            coEvery { value } returns 1
            coJustRun { postValue(any()) }
        }
        coEvery { StatisticsUtils.onCommon(MyApplication.sAppContext, any()) } just Runs
        coEvery { viewModel.mUpdateLabels } returns liveData
        val labelNames = arrayListOf<String>()
        labelNames.add("newName")
        val filePaths = arrayListOf<BaseFileBean>()
        coEvery { viewModel.mappingFileToLabel(labelNames, filePaths) } coAnswers { callOriginal() }

        viewModel.mappingFileToLabel(labelNames, filePaths)
    }

    @Test
    fun `should execute insertFileLabelMappingEntities when mappingFileToNewLabel if newLabels is not empty`() {
        val addLabelMock = mockk<AddLabelViewModel>()
        val labelName = "labelName"
        val filePaths = arrayListOf<BaseFileBean>()
        for (index in 0..10) {
            val file = BaseFileBean()
            filePaths.add(file)
        }
        every { FileLabelDBHelper.insertFileLabel(any()) } returns 1
        every { FileLabelMappingDBHelper.insertFileLabelMappingEntities(any()) } just Runs
        every { addLabelMock.mappingFileToNewLabel(labelName, filePaths) } answers { callOriginal() }
        addLabelMock.mappingFileToNewLabel(labelName, filePaths)

        verify { FileLabelMappingDBHelper.insertFileLabelMappingEntities(any()) }
    }
}