/***********************************************************
 ** Copyright (C), 2010-2023 Oplus. All rights reserved.
 ** File:  - RecentDragListener.kt
 ** Description: RecentDragListener
 ** Version: 1.0
 ** Date : 2023/07/19
 ** Author: W9001702
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  W9001702     2023/07/19    1.0        create
 ****************************************************************/
package com.oplus.filemanager.recent.ui

import android.app.Activity
import android.content.Context
import android.graphics.drawable.Drawable
import android.view.MotionEvent
import android.view.View
import com.filemanager.common.dragselection.DefaultDragListener
import com.filemanager.common.dragselection.DefaultDropListener
import com.filemanager.common.dragselection.NewFileDragDropShadow
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.view.SelectDragItemViewParent.Companion.DRAG_ITEM_OPACITY
import com.oplus.dropdrag.dragdrop.DragScanResult

class RecentDragListener(
    activity: Activity,
    view: View,
    private val dragHoldDrawable: Drawable?,
    private val dragHoldTitle: String?,
    private val dragHoldDetail: String?,
    private val event: MotionEvent,
    private val viewMode: com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE?
) : DefaultDragListener(activity, view, null, dragHoldDrawable, CategoryHelper.CATEGORY_RECENT, event, viewMode) {

    companion object {
        const val TAG = "RecentDragListener"
    }

    override fun addSelectedView(list: ArrayList<View>?): DefaultDragListener {
        list?.let { list ->
            val visibleList = list.filter {
                val visible = KtViewUtils.isViewVisible(it)
                if (!visible) {
                    it.alpha = DRAG_ITEM_OPACITY
                }
                visible
            }.toCollection(ArrayList())
            Log.d(TAG, "buildAnimationLayout visibleList:${visibleList.size}")
            if (visibleList.size > 0) {
                selectedViewList.value = visibleList
            } else {
                selectedViewList.value = list
            }
            selectedViewList.value?.let { list -> DefaultDropListener.setSelectedItemViewList(list) }
            viewMode?.let { viewMode -> DefaultDropListener.viewMode = viewMode }
        }
        return this@RecentDragListener
    }

    override fun updateShaderUI(
        shaderBuilder: NewFileDragDropShadow,
        result: DragScanResult,
        context: Context
    ) {
        shaderBuilder.updateShadowRedDot(result.itemCount)
    }
}