#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 846752 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:189), pid=21660, tid=22672
#
# JRE version: Java(TM) SE Runtime Environment (17.0.8+9) (build 17.0.8+9-LTS-211)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\PCConnect\build\20250901_17269828003212677598.compiler.options

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Sep  1 11:29:08 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 1.419675 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x00000291a3627660):  JavaThread "C2 CompilerThread4" daemon [_thread_in_native, id=22672, stack(0x000000b7cce00000,0x000000b7ccf00000)]


Current CompileTask:
C2:   1419 1192       4       org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ArchiveHandler::directoryEntry (144 bytes)

Stack: [0x000000b7cce00000,0x000000b7ccf00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x677d0a]
V  [jvm.dll+0x7d8c54]
V  [jvm.dll+0x7da3fe]
V  [jvm.dll+0x7daa63]
V  [jvm.dll+0x245c5f]
V  [jvm.dll+0xaaa6b]
V  [jvm.dll+0xab00c]
V  [jvm.dll+0x361e8e]
V  [jvm.dll+0x32c551]
V  [jvm.dll+0x32b9ea]
V  [jvm.dll+0x217fff]
V  [jvm.dll+0x217431]
V  [jvm.dll+0x1a3af0]
V  [jvm.dll+0x2270be]
V  [jvm.dll+0x22535b]
V  [jvm.dll+0x78e7bc]
V  [jvm.dll+0x788bba]
V  [jvm.dll+0x676b35]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002915dec72b0, length=19, elements={
0x000002912e4a27c0, 0x000002915cb0f0d0, 0x000002915cb0ff40, 0x000002915cb2edf0,
0x000002915cb2f7a0, 0x000002915cb30150, 0x000002915cb30b00, 0x000002915cb327f0,
0x000002915cb33290, 0x000002915cb3a5a0, 0x000002915d44dc80, 0x000002915d4521a0,
0x000002915d45af00, 0x000002915d4661f0, 0x000002915d9ca9c0, 0x000002915df6ce90,
0x000002915d4ec290, 0x00000291a3025940, 0x00000291a3627660
}

Java Threads: ( => current thread )
  0x000002912e4a27c0 JavaThread "main" [_thread_in_native, id=19448, stack(0x000000b7caa00000,0x000000b7cab00000)]
  0x000002915cb0f0d0 JavaThread "Reference Handler" daemon [_thread_blocked, id=17576, stack(0x000000b7cb100000,0x000000b7cb200000)]
  0x000002915cb0ff40 JavaThread "Finalizer" daemon [_thread_blocked, id=9876, stack(0x000000b7cb200000,0x000000b7cb300000)]
  0x000002915cb2edf0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=18548, stack(0x000000b7cb300000,0x000000b7cb400000)]
  0x000002915cb2f7a0 JavaThread "Attach Listener" daemon [_thread_blocked, id=9804, stack(0x000000b7cb400000,0x000000b7cb500000)]
  0x000002915cb30150 JavaThread "Service Thread" daemon [_thread_blocked, id=15976, stack(0x000000b7cb500000,0x000000b7cb600000)]
  0x000002915cb30b00 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=14820, stack(0x000000b7cb600000,0x000000b7cb700000)]
  0x000002915cb327f0 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=15884, stack(0x000000b7cb700000,0x000000b7cb800000)]
  0x000002915cb33290 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=18588, stack(0x000000b7cb800000,0x000000b7cb900000)]
  0x000002915cb3a5a0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=29184, stack(0x000000b7cb900000,0x000000b7cba00000)]
  0x000002915d44dc80 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=19072, stack(0x000000b7cba00000,0x000000b7cbb00000)]
  0x000002915d4521a0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=9972, stack(0x000000b7cbb00000,0x000000b7cbc00000)]
  0x000002915d45af00 JavaThread "Notification Thread" daemon [_thread_blocked, id=28332, stack(0x000000b7cbc00000,0x000000b7cbd00000)]
  0x000002915d4661f0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=23560, stack(0x000000b7cbe00000,0x000000b7cbf00000)]
  0x000002915d9ca9c0 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=13956, stack(0x000000b7cc000000,0x000000b7cc100000)]
  0x000002915df6ce90 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=26940, stack(0x000000b7cbf00000,0x000000b7cc000000)]
  0x000002915d4ec290 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=3184, stack(0x000000b7ccc00000,0x000000b7ccd00000)]
  0x00000291a3025940 JavaThread "C2 CompilerThread3" daemon [_thread_blocked, id=16004, stack(0x000000b7ccd00000,0x000000b7cce00000)]
=>0x00000291a3627660 JavaThread "C2 CompilerThread4" daemon [_thread_in_native, id=22672, stack(0x000000b7cce00000,0x000000b7ccf00000)]

Other Threads:
  0x000002915cb0b4e0 VMThread "VM Thread" [stack: 0x000000b7cb000000,0x000000b7cb100000] [id=25524]
  0x000002915d460310 WatcherThread [stack: 0x000000b7cbd00000,0x000000b7cbe00000] [id=1424]
  0x000002912e5513d0 GCTaskThread "GC Thread#0" [stack: 0x000000b7cab00000,0x000000b7cac00000] [id=25240]
  0x000002915dac3450 GCTaskThread "GC Thread#1" [stack: 0x000000b7cc100000,0x000000b7cc200000] [id=29648]
  0x000002915dac3700 GCTaskThread "GC Thread#2" [stack: 0x000000b7cc200000,0x000000b7cc300000] [id=23944]
  0x000002915da67a50 GCTaskThread "GC Thread#3" [stack: 0x000000b7cc300000,0x000000b7cc400000] [id=18076]
  0x000002915daf34b0 GCTaskThread "GC Thread#4" [stack: 0x000000b7cc400000,0x000000b7cc500000] [id=7608]
  0x000002915daf3b70 GCTaskThread "GC Thread#5" [stack: 0x000000b7cc500000,0x000000b7cc600000] [id=15608]
  0x000002915daf4430 GCTaskThread "GC Thread#6" [stack: 0x000000b7cc600000,0x000000b7cc700000] [id=21312]
  0x000002915da6e820 GCTaskThread "GC Thread#7" [stack: 0x000000b7cc700000,0x000000b7cc800000] [id=20152]
  0x000002915da6ead0 GCTaskThread "GC Thread#8" [stack: 0x000000b7cc800000,0x000000b7cc900000] [id=20848]
  0x000002915daf9320 GCTaskThread "GC Thread#9" [stack: 0x000000b7cc900000,0x000000b7cca00000] [id=9852]
  0x000002915dae5330 GCTaskThread "GC Thread#10" [stack: 0x000000b7cca00000,0x000000b7ccb00000] [id=2804]
  0x000002915dae6350 GCTaskThread "GC Thread#11" [stack: 0x000000b7ccb00000,0x000000b7ccc00000] [id=22028]
  0x000002912e5620d0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000b7cac00000,0x000000b7cad00000] [id=15128]
  0x000002912e563280 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000b7cad00000,0x000000b7cae00000] [id=24272]
  0x000002915ca406d0 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000b7cae00000,0x000000b7caf00000] [id=22196]
  0x000002915ca40f00 ConcurrentGCThread "G1 Service" [stack: 0x000000b7caf00000,0x000000b7cb000000] [id=22568]

Threads with active compile tasks:
C2 CompilerThread1     1479 1199       4       org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ArchiveHandler::processEntry (225 bytes)
C2 CompilerThread4     1479 1192       4       org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ArchiveHandler::directoryEntry (144 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000604800000, size: 8120 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000002915e000000-0x000002915ebd0000-0x000002915ebd0000), size 12386304, SharedBaseAddress: 0x000002915e000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002915f000000-0x000002919f000000, reserved size: 1073741824
Narrow klass base: 0x000002915e000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32479M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8120M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 520192K, used 9307K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 15316K, committed 15488K, reserved 1114112K
  class space    used 2020K, committed 2112K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%|HS|  |TAMS 0x0000000604800000, 0x0000000604800000| Complete 
|   1|0x0000000604c00000, 0x0000000604eb8a00, 0x0000000605000000| 68%| O|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   2|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|   3|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|   4|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|   5|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|   6|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|   7|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|   8|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|   9|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  10|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  11|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  12|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  13|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
|  14|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  15|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  16|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  17|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  18|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  19|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  20|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  21|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Untracked 
|  22|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|  23|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000, 0x000000060a400000| Untracked 
|  24|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000, 0x000000060a800000| Untracked 
|  25|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000, 0x000000060ac00000| Untracked 
|  26|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000, 0x000000060b000000| Untracked 
|  27|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000, 0x000000060b400000| Untracked 
|  28|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000, 0x000000060b800000| Untracked 
|  29|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000, 0x000000060bc00000| Untracked 
|  30|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|  31|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000, 0x000000060c400000| Untracked 
|  32|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000, 0x000000060c800000| Untracked 
|  33|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000, 0x000000060cc00000| Untracked 
|  34|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000, 0x000000060d000000| Untracked 
|  35|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000, 0x000000060d400000| Untracked 
|  36|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000, 0x000000060d800000| Untracked 
|  37|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000, 0x000000060dc00000| Untracked 
|  38|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|  39|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000, 0x000000060e400000| Untracked 
|  40|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000, 0x000000060e800000| Untracked 
|  41|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000, 0x000000060ec00000| Untracked 
|  42|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000, 0x000000060f000000| Untracked 
|  43|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000, 0x000000060f400000| Untracked 
|  44|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000, 0x000000060f800000| Untracked 
|  45|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000, 0x000000060fc00000| Untracked 
|  46|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|  47|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000, 0x0000000610400000| Untracked 
|  48|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000, 0x0000000610800000| Untracked 
|  49|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000, 0x0000000610c00000| Untracked 
|  50|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000, 0x0000000611000000| Untracked 
|  51|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000, 0x0000000611400000| Untracked 
|  52|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000, 0x0000000611800000| Untracked 
|  53|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000, 0x0000000611c00000| Untracked 
|  54|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
|  55|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000, 0x0000000612400000| Untracked 
|  56|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000, 0x0000000612800000| Untracked 
|  57|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000, 0x0000000612c00000| Untracked 
|  58|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000, 0x0000000613000000| Untracked 
|  59|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000, 0x0000000613400000| Untracked 
|  60|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000, 0x0000000613800000| Untracked 
|  61|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000, 0x0000000613c00000| Untracked 
|  62|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
|  63|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000, 0x0000000614400000| Untracked 
|  64|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000, 0x0000000614800000| Untracked 
|  65|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000, 0x0000000614c00000| Untracked 
|  66|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000, 0x0000000615000000| Untracked 
|  67|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000, 0x0000000615400000| Untracked 
|  68|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000, 0x0000000615800000| Untracked 
|  69|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000, 0x0000000615c00000| Untracked 
|  70|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
|  71|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000, 0x0000000616400000| Untracked 
|  72|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000, 0x0000000616800000| Untracked 
|  73|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000, 0x0000000616c00000| Untracked 
|  74|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000, 0x0000000617000000| Untracked 
|  75|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000, 0x0000000617400000| Untracked 
|  76|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000, 0x0000000617800000| Untracked 
|  77|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000, 0x0000000617c00000| Untracked 
|  78|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
|  79|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000, 0x0000000618400000| Untracked 
|  80|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000, 0x0000000618800000| Untracked 
|  81|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000, 0x0000000618c00000| Untracked 
|  82|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000, 0x0000000619000000| Untracked 
|  83|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000, 0x0000000619400000| Untracked 
|  84|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000, 0x0000000619800000| Untracked 
|  85|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000, 0x0000000619c00000| Untracked 
|  86|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000, 0x000000061a000000| Untracked 
|  87|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000, 0x000000061a400000| Untracked 
|  88|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000, 0x000000061a800000| Untracked 
|  89|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000, 0x000000061ac00000| Untracked 
|  90|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000, 0x000000061b000000| Untracked 
|  91|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000, 0x000000061b400000| Untracked 
|  92|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000, 0x000000061b800000| Untracked 
|  93|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000, 0x000000061bc00000| Untracked 
|  94|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000, 0x000000061c000000| Untracked 
|  95|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000, 0x000000061c400000| Untracked 
|  96|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000, 0x000000061c800000| Untracked 
|  97|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000, 0x000000061cc00000| Untracked 
|  98|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000, 0x000000061d000000| Untracked 
|  99|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000, 0x000000061d400000| Untracked 
| 100|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000, 0x000000061d800000| Untracked 
| 101|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000, 0x000000061dc00000| Untracked 
| 102|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000, 0x000000061e000000| Untracked 
| 103|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000, 0x000000061e400000| Untracked 
| 104|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000, 0x000000061e800000| Untracked 
| 105|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000, 0x000000061ec00000| Untracked 
| 106|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000, 0x000000061f000000| Untracked 
| 107|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000, 0x000000061f400000| Untracked 
| 108|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000, 0x000000061f800000| Untracked 
| 109|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000, 0x000000061fc00000| Untracked 
| 110|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000, 0x0000000620000000| Untracked 
| 111|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000, 0x0000000620400000| Untracked 
| 112|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000, 0x0000000620800000| Untracked 
| 113|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000, 0x0000000620c00000| Untracked 
| 114|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 115|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000, 0x0000000621400000| Untracked 
| 116|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000, 0x0000000621800000| Untracked 
| 117|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000, 0x0000000621c00000| Untracked 
| 118|0x0000000622000000, 0x000000062225e420, 0x0000000622400000| 59%| S|CS|TAMS 0x0000000622000000, 0x0000000622000000| Complete 
| 119|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000, 0x0000000622400000| Untracked 
| 120|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000, 0x0000000622800000| Untracked 
| 121|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000, 0x0000000622c00000| Untracked 
| 122|0x0000000623000000, 0x0000000623000000, 0x0000000623400000|  0%| F|  |TAMS 0x0000000623000000, 0x0000000623000000| Untracked 
| 123|0x0000000623400000, 0x0000000623400000, 0x0000000623800000|  0%| F|  |TAMS 0x0000000623400000, 0x0000000623400000| Untracked 
| 124|0x0000000623800000, 0x0000000623800000, 0x0000000623c00000|  0%| F|  |TAMS 0x0000000623800000, 0x0000000623800000| Untracked 
| 125|0x0000000623c00000, 0x0000000623c00000, 0x0000000624000000|  0%| F|  |TAMS 0x0000000623c00000, 0x0000000623c00000| Untracked 
| 126|0x0000000624000000, 0x0000000624400000, 0x0000000624400000|100%| E|  |TAMS 0x0000000624000000, 0x0000000624000000| Complete 

Card table byte_map: [0x00000291484c0000,0x00000291494a0000] _byte_map_base: 0x000002914549c000

Marking Bits (Prev, Next): (CMBitMap*) 0x000002912e5519e0, (CMBitMap*) 0x000002912e551a20
 Prev Bits: [0x000002914a480000, 0x0000029152360000)
 Next Bits: [0x0000029152360000, 0x000002915a240000)

Polling page: 0x000002912e580000

Metaspace:

Usage:
  Non-class:     12.98 MB used.
      Class:      1.97 MB used.
       Both:     14.96 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      13.06 MB ( 20%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       2.06 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      15.12 MB (  1%) committed. 

Chunk freelists:
   Non-Class:  656.00 KB
       Class:  13.88 MB
        Both:  14.52 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 108.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 241.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 615.
num_chunk_merges: 0.
num_chunk_splits: 482.
num_chunks_enlarged: 435.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=489Kb max_used=489Kb free=118679Kb
 bounds [0x000002913f0c0000, 0x000002913f330000, 0x0000029146520000]
CodeHeap 'profiled nmethods': size=119104Kb used=1959Kb max_used=1959Kb free=117144Kb
 bounds [0x0000029137520000, 0x0000029137790000, 0x000002913e970000]
CodeHeap 'non-nmethods': size=7488Kb used=2878Kb max_used=2913Kb free=4609Kb
 bounds [0x000002913e970000, 0x000002913ec50000, 0x000002913f0c0000]
 total_blobs=1694 nmethods=1203 adapters=401
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.381 Thread 0x000002915d4ec290 1186       4       java.util.zip.ZipFile$ZipEntryIterator::hasMoreElements (5 bytes)
Event: 1.382 Thread 0x000002915d4ec290 nmethod 1186 0x000002913f135a90 code [0x000002913f135c00, 0x000002913f135c98]
Event: 1.382 Thread 0x000002915d4ec290 1184       4       java.lang.invoke.DirectMethodHandle$Holder::newInvokeSpecial (24 bytes)
Event: 1.383 Thread 0x000002915df6ce90 nmethod 1198 0x000002913f135d90 code [0x000002913f135f00, 0x000002913f136118]
Event: 1.383 Thread 0x000002915df6ce90 1185       4       java.lang.invoke.Invokers$Holder::linkToTargetMethod (10 bytes)
Event: 1.383 Thread 0x000002915df6ce90 nmethod 1185 0x000002913f136210 code [0x000002913f1363a0, 0x000002913f136478]
Event: 1.383 Thread 0x000002915df6ce90 1199       4       org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ArchiveHandler::processEntry (225 bytes)
Event: 1.384 Thread 0x000002915d4ec290 nmethod 1184 0x000002913f136590 code [0x000002913f136720, 0x000002913f136948]
Event: 1.391 Thread 0x000002915cb327f0 nmethod 1190 0x000002913f136b10 code [0x000002913f136d20, 0x000002913f1379a0]
Event: 1.396 Thread 0x00000291a3025940 nmethod 1191 0x000002913f138310 code [0x000002913f138500, 0x000002913f1392e0]
Event: 1.405 Thread 0x000002915cb33290 1201       3       java.lang.Math::addExact (32 bytes)
Event: 1.405 Thread 0x000002915d4521a0 1202       3       java.lang.Math::multiplyExact (74 bytes)
Event: 1.405 Thread 0x000002915d9ca9c0 1203       3       java.nio.file.attribute.FileTime::from (18 bytes)
Event: 1.406 Thread 0x000002915cb33290 nmethod 1201 0x0000029137708b10 code [0x0000029137708cc0, 0x0000029137708ec8]
Event: 1.406 Thread 0x000002915d9ca9c0 nmethod 1203 0x0000029137708f90 code [0x0000029137709160, 0x0000029137709548]
Event: 1.406 Thread 0x000002915d4521a0 nmethod 1202 0x0000029137709710 code [0x00000291377098e0, 0x0000029137709e08]
Event: 1.406 Thread 0x000002915cb327f0 1204       4       org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ZipHandlerBase$$Lambda$126/0x000002915f21a8a0::<init> (15 bytes)
Event: 1.407 Thread 0x00000291a3025940 1205       4       java.lang.StringLatin1::charAt (28 bytes)
Event: 1.407 Thread 0x00000291a3025940 nmethod 1205 0x000002913f139c90 code [0x000002913f139e00, 0x000002913f139e98]
Event: 1.407 Thread 0x000002915cb327f0 nmethod 1204 0x000002913f139f90 code [0x000002913f13a100, 0x000002913f13a358]

GC Heap History (4 events):
Event: 0.584 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 520192K, used 24576K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 6841K, committed 6976K, reserved 1114112K
  class space    used 708K, committed 768K, reserved 1048576K
}
Event: 0.586 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 7099K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 6841K, committed 6976K, reserved 1114112K
  class space    used 708K, committed 768K, reserved 1048576K
}
Event: 1.081 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 35771K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 15291K, committed 15488K, reserved 1114112K
  class space    used 2020K, committed 2112K, reserved 1048576K
}
Event: 1.084 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 9307K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 15291K, committed 15488K, reserved 1114112K
  class space    used 2020K, committed 2112K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 0.727 Thread 0x000002912e4a27c0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002913f101a60 relative=0x00000000000003a0
Event: 0.727 Thread 0x000002912e4a27c0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002913f101a60 method=java.util.HashMap.getNode(Ljava/lang/Object;)Ljava/util/HashMap$Node; @ 63 c2
Event: 0.727 Thread 0x000002912e4a27c0 DEOPT PACKING pc=0x000002913f101a60 sp=0x000000b7caafe700
Event: 0.727 Thread 0x000002912e4a27c0 DEOPT UNPACKING pc=0x000002913e9c23a3 sp=0x000000b7caafe668 mode 2
Event: 0.811 Thread 0x000002912e4a27c0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002913f0ed094 relative=0x0000000000000274
Event: 0.811 Thread 0x000002912e4a27c0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002913f0ed094 method=java.lang.String.startsWith(Ljava/lang/String;I)Z @ 1 c2
Event: 0.811 Thread 0x000002912e4a27c0 DEOPT PACKING pc=0x000002913f0ed094 sp=0x000000b7caafdf00
Event: 0.811 Thread 0x000002912e4a27c0 DEOPT UNPACKING pc=0x000002913e9c23a3 sp=0x000000b7caafde80 mode 2
Event: 1.105 Thread 0x000002912e4a27c0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002913f11d3e8 relative=0x0000000000000868
Event: 1.105 Thread 0x000002912e4a27c0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002913f11d3e8 method=org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ZipEntryMap.isTheOne(Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;Ljava/lang/CharSequence;
Event: 1.105 Thread 0x000002912e4a27c0 DEOPT PACKING pc=0x000002913f11d3e8 sp=0x000000b7caafe300
Event: 1.105 Thread 0x000002912e4a27c0 DEOPT UNPACKING pc=0x000002913e9c23a3 sp=0x000000b7caafe1e0 mode 2
Event: 1.105 Thread 0x000002912e4a27c0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002913f115dac relative=0x00000000000004cc
Event: 1.105 Thread 0x000002912e4a27c0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002913f115dac method=org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ZipEntryMap.isTheOne(Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;Ljava/lang/CharSequence;
Event: 1.105 Thread 0x000002912e4a27c0 DEOPT PACKING pc=0x000002913f115dac sp=0x000000b7caafe210
Event: 1.105 Thread 0x000002912e4a27c0 DEOPT UNPACKING pc=0x000002913e9c23a3 sp=0x000000b7caafe1c0 mode 2
Event: 1.105 Thread 0x000002912e4a27c0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002913f11bd68 relative=0x0000000000000868
Event: 1.105 Thread 0x000002912e4a27c0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002913f11bd68 method=org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ZipEntryMap.isTheOne(Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;Ljava/lang/CharSequence;
Event: 1.105 Thread 0x000002912e4a27c0 DEOPT PACKING pc=0x000002913f11bd68 sp=0x000000b7caafe270
Event: 1.105 Thread 0x000002912e4a27c0 DEOPT UNPACKING pc=0x000002913e9c23a3 sp=0x000000b7caafe1c8 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.599 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006240c9ea8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x00000006240c9ea8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.631 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000624287658}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000624287658) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.653 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623c93318}: 'long java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623c93318) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.654 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623c9ebf8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623c9ebf8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.654 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623ca28e8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623ca28e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.655 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623ca5f50}: 'int java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623ca5f50) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.656 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623cb54d8}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x0000000623cb54d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.657 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623cbd110}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x0000000623cbd110) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.659 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623ccc4d0}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object, java.lang.Object)'> (0x0000000623ccc4d0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.661 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623cdb060}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int, int)'> (0x0000000623cdb060) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.663 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623ce9ac0}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, long, long)'> (0x0000000623ce9ac0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.664 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623cf80d8}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int)'> (0x0000000623cf80d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.676 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623e2f988}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623e2f988) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.680 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623e59120}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623e59120) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.693 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623f37ef0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623f37ef0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.745 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623a5c2d8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623a5c2d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.796 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062361feb0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x000000062361feb0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.865 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623321fc0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000623321fc0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.873 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062339d1e0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x000000062339d1e0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.888 Thread 0x000002912e4a27c0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c58d88}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c58d88) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (10 events):
Event: 0.102 Executing VM operation: HandshakeAllThreads
Event: 0.103 Executing VM operation: HandshakeAllThreads done
Event: 0.386 Executing VM operation: HandshakeAllThreads
Event: 0.386 Executing VM operation: HandshakeAllThreads done
Event: 0.448 Executing VM operation: HandshakeAllThreads
Event: 0.448 Executing VM operation: HandshakeAllThreads done
Event: 0.583 Executing VM operation: G1CollectForAllocation
Event: 0.586 Executing VM operation: G1CollectForAllocation done
Event: 1.081 Executing VM operation: G1CollectForAllocation
Event: 1.085 Executing VM operation: G1CollectForAllocation done

Events (20 events):
Event: 0.881 loading class java/util/function/UnaryOperator done
Event: 0.979 loading class java/net/MalformedURLException
Event: 0.979 loading class java/net/MalformedURLException done
Event: 0.979 loading class java/nio/file/InvalidPathException
Event: 0.979 loading class java/nio/file/InvalidPathException done
Event: 0.991 loading class java/util/DualPivotQuicksort
Event: 0.991 loading class java/util/DualPivotQuicksort done
Event: 1.020 loading class java/util/zip/ZipFile$ZipEntryIterator
Event: 1.020 loading class java/util/zip/ZipFile$ZipEntryIterator done
Event: 1.037 loading class java/util/AbstractMap$SimpleEntry
Event: 1.037 loading class java/util/AbstractMap$SimpleEntry done
Event: 1.085 Thread 0x00000291a300d8e0 Thread added: 0x00000291a300d8e0
Event: 1.095 Thread 0x000002915df6ce90 Thread added: 0x000002915df6ce90
Event: 1.347 Thread 0x000002915df6ce90 Thread exited: 0x000002915df6ce90
Event: 1.347 Thread 0x00000291a300d8e0 Thread exited: 0x00000291a300d8e0
Event: 1.347 Thread 0x000002915d4ec340 Thread exited: 0x000002915d4ec340
Event: 1.353 Thread 0x000002915df6ce90 Thread added: 0x000002915df6ce90
Event: 1.361 Thread 0x000002915d4ec290 Thread added: 0x000002915d4ec290
Event: 1.366 Thread 0x00000291a3025940 Thread added: 0x00000291a3025940
Event: 1.366 Thread 0x00000291a3627660 Thread added: 0x00000291a3627660


Dynamic libraries:
0x00007ff708300000 - 0x00007ff708310000 	E:\JAVA\bin\java.exe
0x00007ff827030000 - 0x00007ff827228000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8255d0000 - 0x00007ff825692000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff824d50000 - 0x00007ff825046000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8246c0000 - 0x00007ff8247c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff81fde0000 - 0x00007ff81fdf9000 	E:\JAVA\bin\jli.dll
0x00007ff81fdc0000 - 0x00007ff81fddb000 	E:\JAVA\bin\VCRUNTIME140.dll
0x00007ff826e80000 - 0x00007ff826f31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff826340000 - 0x00007ff8263de000 	C:\Windows\System32\msvcrt.dll
0x00007ff825e80000 - 0x00007ff825f1f000 	C:\Windows\System32\sechost.dll
0x00007ff825b10000 - 0x00007ff825c33000 	C:\Windows\System32\RPCRT4.dll
0x00007ff824a20000 - 0x00007ff824a47000 	C:\Windows\System32\bcrypt.dll
0x00007ff825050000 - 0x00007ff8251ed000 	C:\Windows\System32\USER32.dll
0x00007ff81dfe0000 - 0x00007ff81e27a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8248a0000 - 0x00007ff8248c2000 	C:\Windows\System32\win32u.dll
0x00007ff825da0000 - 0x00007ff825dcb000 	C:\Windows\System32\GDI32.dll
0x00007ff824a50000 - 0x00007ff824b69000 	C:\Windows\System32\gdi32full.dll
0x00007ff824980000 - 0x00007ff824a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff823400000 - 0x00007ff82340a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8253f0000 - 0x00007ff82541f000 	C:\Windows\System32\IMM32.DLL
0x00007ff81fd50000 - 0x00007ff81fd5c000 	E:\JAVA\bin\vcruntime140_1.dll
0x00007ffffb0c0000 - 0x00007ffffb14e000 	E:\JAVA\bin\msvcp140.dll
0x00007fffe0350000 - 0x00007fffe0f2e000 	E:\JAVA\bin\server\jvm.dll
0x00007ff826f40000 - 0x00007ff826f48000 	C:\Windows\System32\PSAPI.DLL
0x00007fffeb340000 - 0x00007fffeb349000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff81e3c0000 - 0x00007ff81e3e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff826e10000 - 0x00007ff826e7b000 	C:\Windows\System32\WS2_32.dll
0x00007ff823360000 - 0x00007ff823372000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff81c4f0000 - 0x00007ff81c4fa000 	E:\JAVA\bin\jimage.dll
0x00007ff821e10000 - 0x00007ff822011000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff80fef0000 - 0x00007ff80ff24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff824810000 - 0x00007ff824892000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff818d20000 - 0x00007ff818d45000 	E:\JAVA\bin\java.dll
0x00007fffef390000 - 0x00007fffef467000 	E:\JAVA\bin\jsvml.dll
0x00007ff826570000 - 0x00007ff826cde000 	C:\Windows\System32\SHELL32.dll
0x00007ff8222d0000 - 0x00007ff822a74000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff825fd0000 - 0x00007ff826323000 	C:\Windows\System32\combase.dll
0x00007ff8240f0000 - 0x00007ff82411b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff826d40000 - 0x00007ff826e0d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff825f20000 - 0x00007ff825fcd000 	C:\Windows\System32\SHCORE.dll
0x00007ff825d40000 - 0x00007ff825d9b000 	C:\Windows\System32\shlwapi.dll
0x00007ff8245f0000 - 0x00007ff824614000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff811d70000 - 0x00007ff811d89000 	E:\JAVA\bin\net.dll
0x00007ff81ede0000 - 0x00007ff81eeea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff823e50000 - 0x00007ff823eba000 	C:\Windows\system32\mswsock.dll
0x00007ff810fb0000 - 0x00007ff810fc6000 	E:\JAVA\bin\nio.dll
0x00007ff81bc20000 - 0x00007ff81bc38000 	E:\JAVA\bin\zip.dll
0x00007ff81c4d0000 - 0x00007ff81c4e0000 	E:\JAVA\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\JAVA\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;E:\JAVA\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\PCConnect\build\20250901_17269828003212677598.compiler.options
java_class_path (initial): E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.22\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\kotlin-compiler-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.22\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\kotlin-script-runtime-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.22\20e2c5df715f3240c765cfc222530e2796542021\kotlin-daemon-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8514437120                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8514437120                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\JAVA
CLASSPATH=.;E:\JAVA\lib\dt.jar;E:\JAVA\lib\tools.jar;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\dotnet\;E:\git\Git\cmd;D:\Users\W9096060\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\dotnet\;E:\1.8java\lib\dt.jar;E:\1.8java\lib\tools.jar;E:\1.8java\bin;E:\JAVA\bin;E:\JAVA\lib;D:\Users\W9096060\AppData\Local\Microsoft\WindowsApps;
USERNAME=W9096060
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 18:52 hours

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 32479M (2951M free)
TotalPageFile size 47840M (AvailPageFile size 4M)
current process WorkingSet (physical memory assigned to process): 114M, peak: 114M
current process commit charge ("private bytes"): 653M, peak: 654M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211) for windows-amd64 JRE (17.0.8+9-LTS-211), built on Jun 14 2023 10:34:31 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
