/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: BlacklistParserTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/8/29
 ** Author: <PERSON><PERSON><PERSON><PERSON>
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/8/29      1.0     create
 ****************************************************************/
package com.coloros.filemanager.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

import android.database.Cursor;

import com.coloros.filemanager.BaseTest;
import com.filemanager.common.helper.CategoryHelper;
import com.filemanager.common.utils.BlacklistParser;
import com.filemanager.common.utils.FileTypeUtils;

import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.invocation.InvocationOnMock;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.shadows.ShadowLog;

import utils.ReflectionUtils;

@Ignore("stupid test")
@RunWith(RobolectricTestRunner.class)
public class BlacklistParserTest extends BaseTest {
    private static final String BLACKLIST_VERSION = "blacklist_version";
    private BlacklistParser mBlacklistParser;
    private Cursor mCursor;
    private static String sRegion;
    private static String[] sSevenList;
    private static String[] sEightList;
    private static String[] sNineList;
    private static String[] sTenList;
    private static String[] sElevenList;
    private static String[] sRecentFilterFilelist;
    private static String[] sUnknownFileList;
    private static String[] sDocFilterTypes;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = RuntimeEnvironment.application;
        mBlacklistParser = new BlacklistParser();
    }

    @After
    public void tearDown() {
        mBlacklistParser = null;
        mCursor = null;
        sRegion = null;
        sSevenList = null;
        sEightList = null;
        sNineList = null;
        sTenList = null;
        sElevenList = null;
        sRecentFilterFilelist = null;
        sUnknownFileList = null;
        sDocFilterTypes = null;
    }

    @Ignore
    @Test
    public void should_returnList_when_getsRecentFilterFileType_with_arrayNotNull()
            throws NoSuchFieldException, IllegalAccessException {
        sRecentFilterFilelist = null;
        ReflectionUtils.setPrivateField(mBlacklistParser,
                "sRecentFilterFilelist", sRecentFilterFilelist);
        assertNull(mBlacklistParser.getsRecentFilterFileType(mContext));
        sRecentFilterFilelist = new String[]{"1", "2"};
        ReflectionUtils.setPrivateField(mBlacklistParser,
                "sRecentFilterFilelist", sRecentFilterFilelist);
        assertEquals(sRecentFilterFilelist, mBlacklistParser.getsRecentFilterFileType(mContext));
    }

    @Ignore
    @Test
    public void should_returnList_when_getUnknownFiles_with_arrayNotNull()
            throws NoSuchFieldException, IllegalAccessException {
        sUnknownFileList = null;
        ReflectionUtils.setPrivateField(mBlacklistParser, "sUnknownFileList", sUnknownFileList);
        assertNull(mBlacklistParser.getUnknownFiles(mContext));
        sUnknownFileList = new String[]{"1", "2"};
        ReflectionUtils.setPrivateField(mBlacklistParser, "sUnknownFileList", sUnknownFileList);
        assertEquals(sUnknownFileList, mBlacklistParser.getUnknownFiles(mContext));
    }


    @Test
    public void should_returnBoolean_when_isDocFilterTypes_with_arrayNotNull()
            throws NoSuchFieldException, IllegalAccessException {
        assertFalse(mBlacklistParser.isDocFilterTypes(null));
        sDocFilterTypes = new String[]{"test", "type"};
        System.out.println(sDocFilterTypes.length);
        ReflectionUtils.setPrivateField(mBlacklistParser, "sDocFilterTypes", sDocFilterTypes);
        assertTrue(mBlacklistParser.isDocFilterTypes(null));
        assertFalse(mBlacklistParser.isDocFilterTypes("test"));
    }

    @Test
    public void should_return_app_path_when_getOneAppPaths_with_paths() {
        assertEquals(0, BlacklistParser.getOneAppPaths(null).size());

        assertEquals(0, BlacklistParser.getOneAppPaths(new String[]{}).size());

        assertEquals(1, BlacklistParser.getOneAppPaths(new String[]{"path"}).size());
    }

    @Test
    public void should_return_size_when_getFilterSizeByType() {
        assertEquals(0, BlacklistParser.getFilterSizeByType(CategoryHelper.CATEGORY_VIDEO));
        assertEquals(0, BlacklistParser.getFilterSizeByType(CategoryHelper.CATEGORY_AUDIO));
        assertEquals(0, BlacklistParser.getFilterSizeByType(CategoryHelper.CATEGORY_IMAGE));
        assertEquals(0, BlacklistParser.getFilterSizeByType(CategoryHelper.CATEGORY_DOC));
        assertEquals(0, BlacklistParser.getFilterSizeByType(CategoryHelper.CATEGORY_APK));
        assertEquals(0, BlacklistParser.getFilterSizeByType(CategoryHelper.CATEGORY_COMPRESS));
        assertEquals(0, BlacklistParser.getFilterSizeByType(-1));
    }

    @Test
    public void should_return_state_when_isFilterTypes_with_type() {
        MockedStatic<FileTypeUtils> fileTypeUtilsMock = mockStatic(FileTypeUtils.class);
        fileTypeUtilsMock.when(() -> FileTypeUtils.isApkType(any())).thenAnswer(InvocationOnMock::callRealMethod);
        assertFalse(BlacklistParser.isFilterTypes(CategoryHelper.CATEGORY_VIDEO, null));
        assertFalse(BlacklistParser.isFilterTypes(CategoryHelper.CATEGORY_AUDIO, null));
        assertFalse(BlacklistParser.isFilterTypes(CategoryHelper.CATEGORY_IMAGE, null));
        assertFalse(BlacklistParser.isFilterTypes(CategoryHelper.CATEGORY_DOC, null));
        assertTrue(BlacklistParser.isFilterTypes(CategoryHelper.CATEGORY_APK, null));
        assertFalse(BlacklistParser.isFilterTypes(CategoryHelper.CATEGORY_COMPRESS, null));
        assertFalse(BlacklistParser.isFilterTypes(CategoryHelper.CATEGORY_QQ, null));
        assertFalse(BlacklistParser.isFilterTypes(CategoryHelper.CATEGORY_MICROMSG, null));
        assertFalse(BlacklistParser.isFilterTypes(-1, null));
        fileTypeUtilsMock.close();
    }

    @Test
    public void should_return_when_isFilterNoSuffixFile_with_path() {
        assertTrue(BlacklistParser.isFilterNoSuffixFile(null));
        assertFalse(BlacklistParser.isFilterNoSuffixFile("/path/file.ext"));
    }

    @Test
    public void should_return_when_isApkFilterType() {
        MockedStatic<FileTypeUtils> fileTypeUtilsMock = mockStatic(FileTypeUtils.class);
        fileTypeUtilsMock.when(() -> FileTypeUtils.isApkType(any())).thenAnswer(InvocationOnMock::callRealMethod);
        assertTrue(BlacklistParser.isApkFilterType(null));
        assertFalse(BlacklistParser.isApkFilterType("demo.apk"));
        assertFalse(BlacklistParser.isApkFilterType("demo.apk.1"));
        assertTrue(BlacklistParser.isApkFilterType("demo.apk.jpg"));
        assertTrue(BlacklistParser.isApkFilterType("demo.jpg"));
        fileTypeUtilsMock.close();
    }
}