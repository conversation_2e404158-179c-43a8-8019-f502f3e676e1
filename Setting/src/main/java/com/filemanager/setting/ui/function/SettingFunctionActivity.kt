/***********************************************************
 * * Copyright (C), 2010 - 2022 Oplus. All rights reserved..
 * * File: SettingFunctionActivity
 * * Description: the activity for settings function switch
 * * Version: 1.0
 * * Date : 2022/6/10
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2022/6/10       1.0         the activity for settings function switch
 ****************************************************************/
package com.filemanager.setting.ui.function

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.WindowUtils
import com.filemanager.setting.R
import com.filemanager.setting.utils.FlexibleWindowUtil
import com.filemanager.setting.utils.FlexibleWindowUtil.FLEXIALE_WINDOW_POSITION
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.oplus.flexiblewindow.FlexibleWindowManager

class SettingFunctionActivity : BaseVMActivity() {

    companion object {
        private const val TAG = "SettingFunctionActivity"
        const val IS_ANIM = "is_anim"

        fun start(activity: Activity?, isAnim: Boolean = false) {
            try {
                activity?.apply {
                    val intent = Intent(activity, SettingFunctionActivity::class.java)
                    intent.putExtra(IS_ANIM, isAnim)
                    if (FeatureCompat.isApplicableForFlexibleWindow && PreferencesUtils.getInt(key = FLEXIALE_WINDOW_POSITION) == 0) {
                        val flexibleWindowPosition = FlexibleWindowUtil.checkOrientation(activity.window.decorView as ViewGroup)
                        Log.d(TAG, "flexibleWindowPosition: $flexibleWindowPosition")
                        PreferencesUtils.put(key = FlexibleWindowUtil.FLEXIALE_WINDOW_POSITION, value = flexibleWindowPosition)
                    }
                    FlexibleWindowUtil.startActivityForFlexibleWindow(activity, intent)
                }
            } catch (e: ActivityNotFoundException) {
                Log.w(TAG, "start: ${e.message}")
            }
        }
    }
    private lateinit var mAppBarLayout: COUIDividerAppBarLayout
    private lateinit var mToolbar: COUIToolbar
    private lateinit var mContainerView: View
    private var isAnim = false

    override fun getLayoutResId(): Int {
        return R.layout.activity_setting_function
    }

    override fun initView() {
        isAnim = IntentUtils.getBoolean(intent, IS_ANIM, false)
        registerVmChangedReceiver(null)
        mAppBarLayout = findViewById(R.id.appbar)
        mToolbar = findViewById(R.id.toolbar)
        mContainerView = findViewById(R.id.fragment_container)
        mToolbar.title = resources.getString(com.filemanager.common.R.string.settings_function)
        mToolbar.isTitleCenterStyle = false
        setSupportActionBar(mToolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        initToolbar()
        FlexibleWindowUtil.setBarBackgroundColor(mToolbar, mAppBarLayout, window.decorView, resources.configuration, this)
        FlexibleWindowUtil.setActionCloseFlexibleActivity(window.decorView, this)
    }

    private fun initToolbar() {
        val res = MyApplication.appContext.resources
        var appBarTopPadding = StatusBarUtil.getStatusBarHeight()
        if (FeatureCompat.isApplicableForFlexibleWindow && WindowUtils.isMiddleAndLargeScreen(this)
            && FlexibleWindowManager.isFlexibleActivitySuitable(resources.configuration)) {
            appBarTopPadding =
                res.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_16dp)
            mToolbar.post {
                mContainerView.setPadding(
                    mContainerView.paddingLeft,
                    mToolbar.height + res.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_12dp),
                    mContainerView.paddingRight,
                    0
                )
                initFragment()
            }
        } else {
            mToolbar.post {
                mContainerView.setPadding(
                    mContainerView.paddingLeft,
                    getViewTopPadding(),
                    mContainerView.paddingRight,
                    0
                )
                initFragment()
            }
        }
        mAppBarLayout.setPadding(0, appBarTopPadding, 0, 0)
    }

    private fun getViewTopPadding(): Int {
        return StatusBarUtil.getStatusBarHeight() + mToolbar.height
    }

    override fun startObserve() {
        Log.d(TAG, "startObserve")
    }

    override fun initData() {
        Log.d(TAG, "initData")
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        Log.d(TAG, "refreshCurrentPage")
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
    }

    private fun initFragment() {
        var fragment = supportFragmentManager.findFragmentByTag(TAG)
        if ((fragment == null) || (fragment !is SettingFunctionFragment)) {
            fragment = SettingFunctionFragment()
        }
        if (fragment.isStateSaved) {
            Log.d(TAG, "initFragment fragment isStateSaved")
            return
        }
        fragment.arguments = Bundle().apply {
            putBoolean(IS_ANIM, isAnim)
        }
        val ft = supportFragmentManager.beginTransaction()
        ft.replace(R.id.fragment_container, fragment, TAG)
        ft.show(fragment)
        ft.commitAllowingStateLoss()
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        initToolbar()
        FlexibleWindowUtil.setBarBackgroundColor(mToolbar, mAppBarLayout, window.decorView, resources.configuration, this)
    }
}