/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/25, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.action

import android.app.Activity
import android.view.DragEvent
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.CommonConstants.DEFAULT_DOWNLOAD_PATH
import com.filemanager.common.dragselection.MacDragUtil
import com.filemanager.common.dragselection.MacDragUtil.Companion.parseFiles
import com.filemanager.common.dragselection.util.DropUtil
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi
import com.oplus.filemanager.interfaze.main.IMain
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

object DropLabelAction : DropListener {

    private const val TAG = "DropLabelAction"

    override fun handleDropAction(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Boolean {
        Log.d(TAG, "handleDropAction dragTag $dragTag dropSideItem $dropSideItem categoryType $targetCategoryType")

        //分布式文管文件，不支持加标签
        val dfmFragmentTag = CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_DFM
        if (dfmFragmentTag == dragTag) {
            CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
            return false
        }

        val parseData = DropUtil.parseDragFile(dragEvent)
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            dragEvent.clipData?.apply {
                val onMacDownloadCallBack: (Boolean, ArrayList<RemoteFileBean>) -> Unit = { result, files ->
                        if (result) {
                            Log.d(TAG, "onDragMacResult true")
                            val list = ArrayList<String>()
                            files.forEach { bean ->
                                list.add(DEFAULT_DOWNLOAD_PATH + File.separator + bean.mDisplayName)
                            }
                            parseData.noneMediaPathList.clear()
                            parseData.mediaPathList.addAll(list)
                            addLabel(activity, targetCategoryType, parseData, dropSideItem)
                        } else {
                            Log.d(TAG, "onDragMacResult false")
                        }
                }
                dragEvent.clipData?.apply {
                    parseFiles(
                        activity = activity,
                        onMacDownloadCallBack = onMacDownloadCallBack,
                        description = description
                    )
                }
            }
            return true
        }
        //内部文件，添加标签
        if (dragTag != null) {
            addLabel(activity, targetCategoryType, parseData, dropSideItem)
            return true
        }

        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //如果私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty()) {
                CustomToast.showShort(com.filemanager.common.R.string.drag_cloud_not_support_position)
                Log.d(TAG, "drag out private files, return!")
                return false
            }
            //公有媒体库文件
            if (data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty()) {
                addLabel(activity, targetCategoryType, data, dropSideItem)
                return true
            }
            return false
        }

        val privacyFileCallback: () -> Boolean = {
            CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
            false
        }
        //外部的媒体库文件，添加标签
        val mediaFileCallback = {
            addLabel(activity, targetCategoryType, parseData, dropSideItem)
            true
        }
        return DropUtil.checkIsMediaFiles(parseData, activity, mediaFileCallback, privacyFileCallback)
    }

    override fun getCornerMarkStateCode(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Int {
        Log.d(TAG, "handleDropAction dragTag $dragTag dropSideItem $dropSideItem categoryType $targetCategoryType")

        //分布式文管文件，不支持加标签
        val dfmFragmentTag = CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_DFM
        if (dfmFragmentTag == dragTag) {
            return DropDispatchAction.PROHIBIT_STATE
        }

        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            return DropDispatchAction.PROHIBIT_STATE
        }
        //内部文件，添加标签
        if (dragTag != null) {
            return DropDispatchAction.NORMAL_STATE
        }

        val parseData = DropUtil.parseDragFile(dragEvent)
        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //如果私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty()) {
                return DropDispatchAction.PROHIBIT_STATE
            }
            //公有媒体库文件
            if (data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty()) {
                return DropDispatchAction.NORMAL_STATE
            }
            return DropDispatchAction.PROHIBIT_STATE
        }

        return DropDispatchAction.NORMAL_STATE
    }

    private fun addLabel(activity: Activity, categoryType: Int, parseData: DragParseData, dropSideItem: Boolean) {
        Log.d(TAG, "addLabel categoryType $categoryType dropSideItem $dropSideItem")
        (activity as BaseVMActivity).lifecycleScope.launch(Dispatchers.IO) {
            val fileOperateApi = Injector.injectFactory<IFileOperateApi>()
            val fileBeanList = fileOperateApi?.getFileBeanList(activity, parseData, isMediaFiles = true) ?: arrayListOf()
            val finalFileBeanList = Injector.injectFactory<IFileOperateApi>()?.findMinLayerFolderListBeans(fileBeanList) ?: fileBeanList
            val labelId = if (dropSideItem) {
                Injector.injectFactory<IMain>()?.getLabelIdByCategoryType(activity, categoryType)
            } else {
                Injector.injectFactory<IMain>()?.getShowingPageLabelId(activity)
            }
            if (labelId == null) {
                Log.d(TAG, "addLabel -> labelId is null ,return!")
                return@launch
            }
            Log.d(TAG, "addLabel labelId $labelId")
            Injector.injectFactory<IFileOperateApi>()?.addLabel(activity, labelId, finalFileBeanList)
        }
    }
}