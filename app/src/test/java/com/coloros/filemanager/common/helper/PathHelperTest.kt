/*
 * ********************************************************************
 *  * * Copyright (C), 2019 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  PathHelperTest.java
 *  * * Description : PathHelperTest.java
 *  * * Version     : 1.0
 *  * * Date        : 12-13-23 下午5:34
 *  * * Author      : W9010863
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.coloros.filemanager.common.helper

import android.content.Context
import android.text.TextUtils
import com.filemanager.common.helper.PathHelper
import com.filemanager.common.helper.VolumeEnvironment
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import junit.framework.Assert
import org.junit.After
import org.junit.Before
import org.junit.Test

class PathHelperTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should verify when getInternalPath without`() {
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(TextUtils::class)

        var context = mockk<Context>(relaxed = true)
        every { VolumeEnvironment.getExternalSdPath(context) }.returns(null)
        every { VolumeEnvironment.getInternalSdPath(context) }.returns("/test/InternalSdPath")
        every { VolumeEnvironment.getOTGPath(context) }.returns(null)

        var pathHelper = PathHelper(context)

        Assert.assertEquals("/test/InternalSdPath", pathHelper.internalPath)

        every { TextUtils.isEmpty(any()) }.returns(true)
        Assert.assertEquals("/test/InternalSdPath", pathHelper.rootPath)

        every { TextUtils.isEmpty(any()) }.returns(false)
        Assert.assertEquals("/test", pathHelper.rootPath)

        Assert.assertEquals(1, pathHelper.count)

        Assert.assertEquals("/test", pathHelper.getPath(0).path)

        Assert.assertEquals("/test", pathHelper.pop().path)

        Assert.assertEquals("/test", pathHelper.pop().path)

        Assert.assertEquals(null, pathHelper.getPath(0))
    }

    @Test
    fun `should verify when PathHelper without`() {
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(TextUtils::class)

        var context = mockk<Context>(relaxed = true)
        every { VolumeEnvironment.getExternalSdPath(context) }.returns(null)
        every { VolumeEnvironment.getInternalSdPath(context) }.returns("/test/InternalSdPath")
        every { VolumeEnvironment.getOTGPath(context) }.returns(null)

        every { TextUtils.isEmpty(any()) }.returns(true)
        PathHelper(context)

        every { VolumeEnvironment.getOTGPath(context) }.returns(arrayListOf())
        PathHelper(context)
    }


    @After
    fun afterTests() {
        unmockkAll()
    }
}