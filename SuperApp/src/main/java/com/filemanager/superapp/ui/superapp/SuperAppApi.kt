/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: SuperAppApi.kt
 ** Description:  SuperAppApi
 ** Version: 1.0
 ** Date: 2021/5/8
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.superapp.ui.superapp

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.SparseArray
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.fragment.app.Fragment
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewListFragmentCreator
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.filemanager.superappsource.helper.MainCategoryItemsBeanFactory
import com.filemanager.superappsource.helper.SuperAppHelper
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean

object SuperAppApi : ISuperApp {

    const val TAG = "SuperAppApi"
    private const val DIR_DEPTH_DEFAULT = 0
    private const val DIR_DEPTH_DOWNLOAD = 2
    private const val SUPER_APP_ACTIVITY = "com.filemanager.superapp.ui.superapp.SuperAppActivity"

    override fun startSuperApp(activity: Activity?, data: MainCategoryItemsBean) {
        activity?.let {
            val dirDepth = getDirDepthByItem(data)
            val intent = Intent().apply {
                putExtra(Constants.TITLE, data.name)
                putExtra(Constants.TITLE_RES_ID, data.nameResId)
                putExtra(KtConstants.P_SUPER_PATH_LIST, data.fileList)
                putExtra(KtConstants.P_PACKAGE, data.packageName)
                putExtra(KtConstants.SUPER_DIR_DEPTH, dirDepth)
                putExtra(KtConstants.FILE_PATH, data.externalPath)
                putExtra(Constants.SIDE_CATEGORY_TYPE, data.sideCategoryType)
                setClassName(it.packageName, SUPER_APP_ACTIVITY)
            }
            it.startActivity(intent)
        }
    }

    override fun startSuperAppFragment(activity: Activity, data: MainCategoryItemsBean) {
        val bundle = Bundle()
        val dirDepth = getDirDepthByItem(data)
        bundle.putString(Constants.TITLE, data.name)
        bundle.putInt(Constants.TITLE_RES_ID, data.nameResId)
        bundle.putStringArray(KtConstants.P_SUPER_PATH_LIST, data.fileList)
        bundle.putString(KtConstants.P_PACKAGE, data.packageName)
        bundle.putInt(KtConstants.SUPER_DIR_DEPTH, dirDepth)
        bundle.putString(KtConstants.FILE_PATH, data.externalPath)
        bundle.putInt(Constants.SIDE_CATEGORY_TYPE, data.sideCategoryType)
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.startFragment(activity, data.sideCategoryType, bundle)
    }

    override fun getDirDepthByItem(data: MainCategoryItemsBean): Int {
        val dirDepth = when (data.itemType) {
            CategoryHelper.CATEGORY_DOWNLOAD -> DIR_DEPTH_DOWNLOAD
            else -> DIR_DEPTH_DEFAULT
        }
        return dirDepth
    }

    override fun getCategoryItems(context: Context): MutableList<MainCategoryItemsBean> {
        return SuperAppHelper.getCategoryItems(context)
    }

    override fun updateSupperAppPaths(context: Context): ArrayList<String?> {
        return SuperAppHelper.updateSupperAppPaths(context)
    }

    override fun preloadSuperSharePreferences(application: Application) {
        SuperAppHelper.preloadSuperSharePreferences(application)
    }

    override fun getMainSuperInitList(): ArrayList<MainCategoryItemsBean>? {
        return SuperAppHelper.getMainSuperInitList()
    }

    override fun isIgnoredPath(
        context: Context,
        type: Int,
        path: String?,
        internalPath: String?,
        ignoredPaths: SparseArray<String?>?,
        categoryItems: List<MainCategoryItemsBean>?
    ): Boolean {
        return SuperAppHelper.isIgnoredPath(
            context,
            type,
            path,
            internalPath,
            ignoredPaths,
            categoryItems
        )
    }

    override fun getCategory(category: Int): Int {
        return MainCategoryItemsBeanFactory.CATEGORY[category]
    }

    override fun getCategoryList(): IntArray {
        return MainCategoryItemsBeanFactory.CATEGORY
    }

    override fun getCategoryItemIcon(category: Int): Int {
        return MainCategoryItemsBeanFactory.CATEGORY_ITEM_ICON[category]
    }

    override fun getCategoryBgItemIcon(category: Int): Int {
        return MainCategoryItemsBeanFactory.CATEGORY_BG_ITEM_ICON[category]
    }

    override fun getCategoryIconRes(category: Int): Int {
        return MainCategoryItemsBeanFactory.getCategoryIcon(category)
    }

    override fun getParentCategoryItemIcon(category: Int): Int {
        return MainCategoryItemsBeanFactory.PARENT_CATEGORY_ITEM_ICON[category]
    }

    override fun isNotQQAndWeChatFile(
        path: String?,
        context: Context?,
        items: List<MainCategoryItemsBean>
    ): Boolean {
        return SuperAppHelper.isNotQQAndWeChatFile(path, context, items)
    }

    override fun checkOWorkSuperAppCondition(context: Context): Boolean {
        return com.filemanager.superappsource.helper.checkOWorkSuperAppCondition(context)
    }

    override fun lastRecordCount(category: Int): String {
        return MainCategoryItemsBeanFactory.LAST_RECORD_COUNT[category]
    }

    override fun lastRecordSize(category: Int): String {
        return MainCategoryItemsBeanFactory.LAST_RECORD_SIZE[category]
    }

    override fun getCategoryItemLastData(position: Int, context: Context): LongArray {
        return MainCategoryItemsBeanFactory.getCategoryItemLastData(position, context)
    }

    override fun getFragment(activity: Activity): Fragment {
        Log.d(TAG, "getFragment")
        val fragment = PreviewCombineFragment()
        fragment.setPreviewListFragmentCreator(object : IPreviewListFragmentCreator {
            override fun create(): IPreviewListFragment {
                return SuperAppParentFragment()
            }
        })
        return fragment
    }

    override fun onResumeLoadData(fragment: Fragment) {
        Log.d(TAG, "onResumeLoadData")
        if (fragment is PreviewCombineFragment) {
            fragment.onResumeLoadData()
        }
    }

    override fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater) {
        if (fragment is PreviewCombineFragment) {
            fragment.onCreateOptionsMenu(menu, inflater)
        }
    }

    override fun onMenuItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onMenuItemSelected")
        if (fragment is PreviewCombineFragment) {
            return fragment.onMenuItemSelected(item)
        }
        return false
    }

    override fun pressBack(fragment: Fragment): Boolean {
        Log.d(TAG, "pressBack")
        if (fragment is PreviewCombineFragment) {
            return fragment.pressBack()
        }
        return false
    }

    override fun onNavigationItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onNavigationItemSelected")
        if (fragment is PreviewCombineFragment) {
            return fragment.onNavigationItemSelected(item)
        }
        return false
    }

    override fun updateLabels(fragment: Fragment) {
        Log.d(TAG, "updateLabels")
        if (fragment is PreviewCombineFragment) {
            fragment.onResumeLoadData()
        }
    }

    override fun fromSelectPathResult(fragment: Fragment, requestCode: Int, paths: List<String>?) {
        Log.d(TAG, "fromSelectPathResult")
        if (fragment is PreviewCombineFragment) {
            fragment.fromSelectPathResult(requestCode, paths)
        }
    }

    override fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        if (fragment is PreviewCombineFragment) {
            fragment.setIsHalfScreen(isHalfScreen)
        }
    }

    override fun backToTop(fragment: Fragment) {
        Log.d(TAG, "backToTop")
        if (fragment is PreviewCombineFragment) {
            fragment.backToTop()
        }
    }

    override fun setToolbarAndTabListener(
        fragment: Fragment,
        toolbar: COUIToolbar?,
        title: String,
        tabListener: TabActivityListener<MediaFileWrapper>
    ) {
        //do nothing
    }

    override fun permissionSuccess(fragment: Fragment) {
        //do nothing
    }

    override fun setCurrentFilePath(fragment: Fragment, path: String?) {
        //do nothing
    }

    override fun getCurrentPath(fragment: Fragment): String {
        return ""
    }

    override fun exitSelectionMode(fragment: Fragment) {
        if (fragment is PreviewCombineFragment) {
            fragment.exitSelectionMode()
        }
    }

    override fun onSideNavigationClicked(fragment: Fragment, isOpen: Boolean): Boolean {
        if (fragment is PreviewCombineFragment) {
            return fragment.onSideNavigationClicked(isOpen)
        }
        return false
    }

    override fun getShowingSuperAppFilePaths(fragment: IPreviewListFragment?): Array<String>? {
        if (fragment is SuperAppParentFragment) {
            return fragment.getFilePaths()
        }
        return null
    }

    override fun getShowingSuperAppFilePaths(activity: Activity): Array<String>? {
        if (activity is SuperAppActivity) {
            return activity.getShowSuperAppFilePath()
        }
        return null
    }
}