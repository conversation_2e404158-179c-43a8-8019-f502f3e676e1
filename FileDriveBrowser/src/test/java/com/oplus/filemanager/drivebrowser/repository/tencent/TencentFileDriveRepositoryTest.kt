/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : TencentFileDriveRepositoryTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/12/12 14:53
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2023/12/12       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.repository.tencent

import com.oplus.filemanager.drivebrowser.data.api.TencentFileDriveService
import com.oplus.filemanager.drivebrowser.data.model.AuthorizationPath
import com.oplus.filemanager.drivebrowser.data.model.BaseResponse
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListsResponse
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.repository.FakeTencentFileDriveService
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.TestResult
import kotlinx.coroutines.test.runTest
import org.junit.Assert
import org.junit.Test
import kotlin.time.Duration.Companion.seconds

class TencentFileDriveRepositoryTest {

    @Test
    fun testCheckAuthorizationStateWhenReturnNull(): Unit = runBlocking {
        val service = FakeTencentFileDriveService()
        val repository = TencentFileDriveRepository(service)
        val authState = repository.checkAuthorizationState()
        Assert.assertFalse(authState)
    }

    @Test
    fun testCheckAuthorizationStateWhenNotAuth(): Unit = runBlocking {
        val service = FakeTencentFileDriveService(authState = false)
        val repository = TencentFileDriveRepository(service)
        val authState = repository.checkAuthorizationState()
        Assert.assertFalse(authState)
    }

    @Test
    fun testCheckAuthorizationStateWhenHasAuth(): Unit = runBlocking {
        val service = FakeTencentFileDriveService(authState = true)
        val repository = TencentFileDriveRepository(service)
        val authState = repository.checkAuthorizationState()
        Assert.assertTrue(authState)
    }

    @Test
    fun testGetAuthUrlWhenReturnNotNull(): Unit = runBlocking {
        val service = FakeTencentFileDriveService()
        val repository = TencentFileDriveRepository(service)
        val authUrl = repository.getAuthUrl()
        Assert.assertNotNull(authUrl)
    }

    @Test
    fun testGetAuthUrlWhenReturnAuthUrl(): Unit = runBlocking {
        val authPathData = AuthorizationPath(false, AUTH_URL)
        val service = FakeTencentFileDriveService(authorizationPath = authPathData)
        val repository = TencentFileDriveRepository(service)
        val authUrl = repository.getAuthUrl()
        Assert.assertNotNull(authUrl)
        Assert.assertEquals(AUTH_URL, authUrl?.authUrl)
    }

    @Test
    fun testSaveAuthorizationResult(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<TencentFileDriveService>()
        coEvery { service.callbackAuth(any(), any()) }.throws(Exception())
        val repository = TencentFileDriveRepository(service)
        val saveErrorResult = repository.saveAuthorizationResult(AUTH_CODE, AUTH_STATE)
        Assert.assertFalse(saveErrorResult.first)

        // save code failure
        coEvery { service.callbackAuth(any(), any()) }.returns(BaseResponse(code = -1, msg = ERROR_MSG))
        val saveInvalidResult = repository.saveAuthorizationResult(AUTH_CODE, AUTH_STATE)
        Assert.assertFalse(saveInvalidResult.first)
        Assert.assertEquals(ERROR_MSG, saveInvalidResult.second)

        // save success
        coEvery { service.callbackAuth(any(), any()) }.returns(BaseResponse(code = 0))
        val saveSuccessResult = repository.saveAuthorizationResult(AUTH_CODE, AUTH_STATE)
        Assert.assertTrue(saveSuccessResult.first)
    }

    @Test
    fun testCancelAuth(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<TencentFileDriveService>()
        coEvery { service.cancelAuth() }.throws(Exception())
        val repository = TencentFileDriveRepository(service)
        val cancelErrorResult = repository.cancelAuth()
        Assert.assertFalse(cancelErrorResult.first)

        // cancel code failure
        coEvery { service.cancelAuth() }.returns(BaseResponse(code = -1))
        val cancelFailureResult = repository.cancelAuth()
        Assert.assertFalse(cancelFailureResult.first)

        // cancel success
        coEvery { service.cancelAuth() }.returns(BaseResponse(code = 0, msg = TencentFileDriveRepository.SUCCESS))
        val cancelSuccessResult = repository.cancelAuth()
        Assert.assertTrue(cancelSuccessResult.first)
        Assert.assertEquals(TencentFileDriveRepository.SUCCESS, cancelSuccessResult.second)
    }

    @Test
    fun testGetWebViewAuthUrl(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<TencentFileDriveService>()
        coEvery { service.buildWebViewAuthorizationPath(any()) }.throws(Exception())
        val repository = TencentFileDriveRepository(service)
        val webViewAuthUrlErrorResult = repository.getWebViewAuthUrl()
        Assert.assertFalse(webViewAuthUrlErrorResult!!.auth)
        Assert.assertEquals("", webViewAuthUrlErrorResult.authUrl)
        coEvery { service.buildWebViewAuthorizationPath(any()) }.returns(BaseResponse(data = null))
        val webViewAuthUrlNullResult = repository.getWebViewAuthUrl()
        Assert.assertNull(webViewAuthUrlNullResult)
        coEvery { service.buildWebViewAuthorizationPath(any()) }.returns(
            BaseResponse(data = AuthorizationPath(true, AUTH_URL))
        )
        val webViewAuthUrl = repository.getWebViewAuthUrl()
        Assert.assertTrue(webViewAuthUrl!!.auth)
        Assert.assertEquals(AUTH_URL, webViewAuthUrl.authUrl)
    }

    @Test
    fun testGetDocumentLists(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<TencentFileDriveService>()
        val lists = mutableListOf<DocumentListItem>()
        lists.add(DocumentListItem())
        coEvery { service.filterDocumentLists(any()) }.returns(
            BaseResponse(
                code = 0,
                data = DocumentListsResponse(
                    list = lists,
                    next = 1
                )
            )
        )
        val repository = TencentFileDriveRepository(service)
        val documentLists = repository.getDocumentLists("desc", 1, 0, 20, FILE_ID)
        Assert.assertEquals(0, documentLists.code)
        Assert.assertEquals(1, documentLists.data?.lists?.size)
    }

    @Test
    fun testRenameDocument(): Unit = runBlocking {
        val service = FakeTencentFileDriveService()
        val repository = TencentFileDriveRepository(service)
        val renameResult = repository.renameDocument(FILE_ID, TITLE)
        Assert.assertFalse(renameResult.code == 0)
        service.code = 0
        val renameSuccessResult = repository.renameDocument(FILE_ID, TITLE)
        Assert.assertTrue(renameSuccessResult.code == 0)
    }

    @Test
    fun testRenameFolder(): Unit = runBlocking {
        val service = FakeTencentFileDriveService()
        val repository = TencentFileDriveRepository(service)
        val renameResult = repository.renameFolder(FILE_ID, TITLE)
        Assert.assertFalse(renameResult.code == 0)
        service.code = 0
        val renameSuccessResult = repository.renameFolder(FILE_ID, TITLE)
        Assert.assertTrue(renameSuccessResult.code == 0)
    }

    @Test
    fun testDeleteFile(): Unit = runBlocking {
        val service = FakeTencentFileDriveService()
        val repository = TencentFileDriveRepository(service)
        val deleteResult = repository.deleteFile(FILE_ID, LIST_TYPE)
        Assert.assertFalse(deleteResult.first)
        service.code = 0
        val deleteSuccessResult = repository.deleteFile(FILE_ID, LIST_TYPE)
        Assert.assertTrue(deleteSuccessResult.first)
    }

    @Test
    fun testDeleteFolder(): Unit = runBlocking {
        val service = FakeTencentFileDriveService()
        val repository = TencentFileDriveRepository(service)
        val deleteResult = repository.deleteFolder(FILE_ID)
        Assert.assertFalse(deleteResult.first)
        service.code = 0
        val deleteSuccessResult = repository.deleteFolder(FILE_ID)
        Assert.assertTrue(deleteSuccessResult.first)
    }

    @Test
    fun testGetEditUrl(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<TencentFileDriveService>()
        val repository = TencentFileDriveRepository(service)
        val editUrl = repository.getEditUrl(FILE_ID)
        Assert.assertEquals("", editUrl)
    }

    companion object {
        private const val AUTH_URL = "packages/miniapp-open/open-auth/open-auth?scope=all&" +
                "client_id=92ff2e63e7e74614b6479ac4e5bfadc9&state=LTy5hGy4uE3eyC9TUCiuxlKwldtsD2O5"
        private const val FILE_ID = "bujcpEiObopu"
        private const val TITLE = "title"
        private const val LIST_TYPE = "origin"
        private const val AUTH_CODE = "04MRUFDSOAKRSPHJ4J-QLW"
        private const val AUTH_STATE = "ggxUDizrENKNC6HAoXm0rE0EPyROkcdm"
        private const val ERROR_MSG = "invalid params"

        private val testCaseTimeout = 30.seconds
    }
}