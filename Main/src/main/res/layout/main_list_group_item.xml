<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:layout_toStartOf="@id/iv_arrow"
        android:id="@+id/category_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_32dp"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_32dp"
        android:layout_marginBottom="@dimen/dimen_8dp"
        android:gravity="center_vertical"
        android:text="@string/text_file_source"
        android:textAppearance="@style/couiTextButtonL"
        android:textColor="?attr/couiColorLabelSecondary"
        android:fontFamily="sans-serif-medium" />

    <com.coui.appcompat.rotateview.COUIRotateView
        android:id="@+id/iv_arrow"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_alignParentEnd="true"
        android:layout_gravity="center_vertical"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_32dp"
        android:src="@drawable/line_arrow"
        app:supportExpanded="false" />
</RelativeLayout>