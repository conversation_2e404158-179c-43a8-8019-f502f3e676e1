/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileActionDecompress.kt
 ** Description: File decompress function
 ** Version: 1.0
 ** Date: 2020/3/9
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.previewcompress

import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.*
import com.filemanager.fileoperate.base.*
import com.filemanager.fileoperate.compress.CompressHelperFactory
import com.filemanager.fileoperate.decompress.*

class FileActionOpenCompressFile(lifecycle: LifecycleOwner, sourceFile: BaseFileBean, needShowCheckButton: Boolean, private var mDecompressFile: BaseDecompressFile)
    : FileActionDecompress(lifecycle, sourceFile, BaseFileBean(), needShowCheckButton, arrayListOf(mDecompressFile)) {
    companion object {
        private const val TAG = "FileActionOpenCompressFile"
        private const val DELAY_OPEN_FILE = 200L
    }

    private var mDecompressDiskFile: BaseFileBean? = null

    override fun run(): Boolean {
        when (mDecompressFile) {
            is RarDecompressFile -> {
                if ((mDecompressFile as RarDecompressFile).rarHeader == null) {
                    Log.d(TAG, "invalid RarDecompressFile")
                    return false
                }
            }
            is ZipDecompressFile -> {
                val file = mDecompressFile as ZipDecompressFile
                if ((file.zipEntry == null) && (file.zipHeader == null)) {
                    Log.d(TAG, "invalid ZipDecompressFile")
                    return false
                }
            }
            is JarDecompressFile -> {
                if ((mDecompressFile as JarDecompressFile).jarArchiveEntry == null) {
                    Log.d(TAG, "invalid JarDecompressFile")
                    return false
                }
            }
            is P7ZipDecompressFile -> {
                Log.d(TAG, "is P7ZipDecompressFile")
            }
            else -> {
                Log.d(TAG, "unsupported DecompressFile")
                return false
            }
        }
        mDestParentFile = PathFileWrapper(CompressPreviewCacheHelper.getCacheDir(mDecompressFile).absolutePath)
        return super.run()
    }

    override fun reallyDecompressFile(): Boolean {
        if (!isCancelled()) {
            if (MimeTypeHelper.getTypeFromPath(mDecompressFile.mData) == MimeTypeHelper.COMPRESSED_TYPE) {
                notifyObserver(NOT_SUPPORT_PREVIEW_COMPRESS)
                return false
            }

            notifyObserver(
                    SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                    mContext.getString(com.filemanager.common.R.string.dialog_open_compress_file), true), DELAY_OPEN_FILE)
            mDecompressDiskFile = CompressPreviewCacheHelper.getCacheFile(mSourceFile, mDecompressFile)
            if (mDecompressDiskFile == null) {
                // Notice that decompress work may be in multi-thread
                CompressPreviewCacheHelper.beforeOpenFile(mDecompressFile)
                val callback = object : DecompressHelper.OnDecompressListener {
                    override fun onInPreparation() {
                        mInDecompressing = true
                    }

                    override fun onFinished(result: Boolean) {
                        Log.d(TAG, "callback onFinished result = $result,mSourceFile=$mSourceFile,mDecompressFile=$mDecompressFile")
                        if (result) {
                            mDecompressDiskFile = CompressPreviewCacheHelper.saveCacheFile(mSourceFile, mDecompressFile)
                        }
                        if (mDecompressDiskFile != null) {
                            notifyObserver(ACTION_DONE, mDecompressDiskFile!!)
                        } else {
                            Log.d(TAG, "callback onFinished notifyObserver failed")
                            notifyObserver(ACTION_FAILED)
                        }
                        mInDecompressing = false
                    }

                    override fun onCancelled() {
                        Log.d(TAG, "callback onCancelled")
                        CompressPreviewCacheHelper.removeCacheFile(mSourceFile, mDecompressFile)
                        notifyObserver(ACTION_CANCELLED)
                        cancelNotifyObserver(SHOW_PROGRESS)
                        mInDecompressing = false
                    }

                    override fun onTryAgain() {
                        Log.d(TAG, "reallyDecompressFile: decompress onTryAgain")
                        mDecompressHelper = CompressHelperFactory.getDecompressHelper(CompressHelperFactory.P7ZIP)
                        (mDecompressHelper as? P7ZipDecompressHelper)?.decompress(
                            mSourceFile, mDestParentFile, passwordQueue.getVerifyPassword(),
                            mSelectDecompressFiles as? MutableList<P7ZipDecompressFile>, this
                        )
                    }
                }
                mDecompressHelper!!.let {
                    val password = passwordQueue.getVerifyPassword()
                    when (it) {
                        is ZipDecompressHelper -> {
                            it.decompress(mSourceFile, mDestParentFile, password,
                                mSelectDecompressFiles as MutableList<ZipDecompressFile>?, callback)
                        }
                        is JarDecompressHelper -> {
                            it.decompress(mSourceFile, mDestParentFile, password,
                                mSelectDecompressFiles as MutableList<JarDecompressFile>?, callback)
                        }
                        is RarDecompressHelper -> {
                            it.decompress(mSourceFile, mDestParentFile, password,
                                mSelectDecompressFiles as MutableList<RarDecompressFile>?, callback)
                        }
                        is P7ZipDecompressHelper -> {
                            it.decompress(mSourceFile, mDestParentFile, password,
                                mSelectDecompressFiles as? MutableList<P7ZipDecompressFile>, callback)
                        }
                    }
                }
            } else {
                notifyObserver(ACTION_DONE, mDecompressDiskFile!!)
            }
        }
        return true
    }

    override fun afterRun(result: Boolean) {
        // Do nothing, this result value no mean the result of the decompress work
    }
}