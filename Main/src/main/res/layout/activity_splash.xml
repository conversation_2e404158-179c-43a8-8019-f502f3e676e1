<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    tools:viewBindingIgnore="true"
    tools:context=".ui.SplashActivity">
        <ImageView
            android:layout_marginTop="@dimen/dimen_292dp"
            android:layout_marginBottom="@dimen/common_margin"
            android:id="@+id/splash_logo"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:contentDescription="@string/app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_splash_logo"/>
        <androidx.appcompat.widget.AppCompatTextView
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/splash_logo"
            android:id="@+id/splash_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:text="@string/app_name"
            android:alpha="0.7"
            android:textSize="@dimen/splash_text_size"
            android:textColor="@color/black" />
</androidx.constraintlayout.widget.ConstraintLayout>