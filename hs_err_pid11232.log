#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 226492416 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3550), pid=11232, tid=5032
#
# JRE version: Java(TM) SE Runtime Environment (17.0.8+9) (build 17.0.8+9-LTS-211)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\FileBrowser\build\20250901_10351852868768062266.compiler.options

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Sep  1 12:12:01 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 16.927363 seconds (0d 0h 0m 16s)

---------------  T H R E A D  ---------------

Current thread (0x0000024138eb8440):  VMThread "VM Thread" [stack: 0x00000051b3b00000,0x00000051b3c00000] [id=5032]

Stack: [0x00000051b3b00000,0x00000051b3c00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x677d0a]
V  [jvm.dll+0x7d8c54]
V  [jvm.dll+0x7da3fe]
V  [jvm.dll+0x7daa63]
V  [jvm.dll+0x245c5f]
V  [jvm.dll+0x674bb9]
V  [jvm.dll+0x6694f2]
V  [jvm.dll+0x3031d6]
V  [jvm.dll+0x30a756]
V  [jvm.dll+0x359f9e]
V  [jvm.dll+0x35a1cf]
V  [jvm.dll+0x2da3e8]
V  [jvm.dll+0x2d87f5]
V  [jvm.dll+0x2d7dfc]
V  [jvm.dll+0x31b4cb]
V  [jvm.dll+0x7df26b]
V  [jvm.dll+0x7dffa4]
V  [jvm.dll+0x7e04bd]
V  [jvm.dll+0x7e0894]
V  [jvm.dll+0x7e0960]
V  [jvm.dll+0x788bba]
V  [jvm.dll+0x676b35]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]

VM_Operation (0x00000051b35fc540): G1CollectForAllocation, mode: safepoint, requested by thread 0x000002410a826180


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000024101f9e810, length=18, elements={
0x000002410a826180, 0x0000024138ebeeb0, 0x0000024138ec1a40, 0x0000024138ed43a0,
0x0000024138ed4c60, 0x0000024138ed5520, 0x0000024138ed5de0, 0x0000024138ed69d0,
0x0000024138ee4400, 0x000002413973dbe0, 0x000002413980cc30, 0x0000024139813420,
0x00000241004aa030, 0x0000024100dab350, 0x00000241005823c0, 0x0000024101428010,
0x00000241033a7820, 0x0000024101f9dfe0
}

Java Threads: ( => current thread )
  0x000002410a826180 JavaThread "main" [_thread_blocked, id=4248, stack(0x00000051b3500000,0x00000051b3600000)]
  0x0000024138ebeeb0 JavaThread "Reference Handler" daemon [_thread_blocked, id=25024, stack(0x00000051b3c00000,0x00000051b3d00000)]
  0x0000024138ec1a40 JavaThread "Finalizer" daemon [_thread_blocked, id=21200, stack(0x00000051b3d00000,0x00000051b3e00000)]
  0x0000024138ed43a0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=3964, stack(0x00000051b3e00000,0x00000051b3f00000)]
  0x0000024138ed4c60 JavaThread "Attach Listener" daemon [_thread_blocked, id=3948, stack(0x00000051b3f00000,0x00000051b4000000)]
  0x0000024138ed5520 JavaThread "Service Thread" daemon [_thread_blocked, id=19136, stack(0x00000051b4000000,0x00000051b4100000)]
  0x0000024138ed5de0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=23936, stack(0x00000051b4100000,0x00000051b4200000)]
  0x0000024138ed69d0 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=25932, stack(0x00000051b4200000,0x00000051b4300000)]
  0x0000024138ee4400 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=20140, stack(0x00000051b4300000,0x00000051b4400000)]
  0x000002413973dbe0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=4128, stack(0x00000051b4400000,0x00000051b4500000)]
  0x000002413980cc30 JavaThread "Notification Thread" daemon [_thread_blocked, id=28416, stack(0x00000051b4600000,0x00000051b4700000)]
  0x0000024139813420 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=18592, stack(0x00000051b4800000,0x00000051b4900000)]
  0x00000241004aa030 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=10820, stack(0x00000051b4500000,0x00000051b4600000)]
  0x0000024100dab350 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=4476, stack(0x00000051b4900000,0x00000051b4a00000)]
  0x00000241005823c0 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=5668, stack(0x00000051b4a00000,0x00000051b4b00000)]
  0x0000024101428010 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=28704, stack(0x00000051b5800000,0x00000051b5900000)]
  0x00000241033a7820 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=11576, stack(0x00000051b5900000,0x00000051b5a00000)]
  0x0000024101f9dfe0 JavaThread "C2 CompilerThread3" daemon [_thread_blocked, id=5192, stack(0x00000051b5c00000,0x00000051b5d00000)]

Other Threads:
=>0x0000024138eb8440 VMThread "VM Thread" [stack: 0x00000051b3b00000,0x00000051b3c00000] [id=5032]
  0x0000024139811b40 WatcherThread [stack: 0x00000051b4700000,0x00000051b4800000] [id=16140]
  0x000002410a8d5b80 GCTaskThread "GC Thread#0" [stack: 0x00000051b3600000,0x00000051b3700000] [id=9556]
  0x000002417f265430 GCTaskThread "GC Thread#1" [stack: 0x00000051b4b00000,0x00000051b4c00000] [id=24460]
  0x000002417f260930 GCTaskThread "GC Thread#2" [stack: 0x00000051b4c00000,0x00000051b4d00000] [id=26916]
  0x000002417f260be0 GCTaskThread "GC Thread#3" [stack: 0x00000051b4d00000,0x00000051b4e00000] [id=20728]
  0x000002417f290b10 GCTaskThread "GC Thread#4" [stack: 0x00000051b4e00000,0x00000051b4f00000] [id=23848]
  0x000002417f290dc0 GCTaskThread "GC Thread#5" [stack: 0x00000051b4f00000,0x00000051b5000000] [id=6600]
  0x000002417f291070 GCTaskThread "GC Thread#6" [stack: 0x00000051b5000000,0x00000051b5100000] [id=24920]
  0x000002417f277820 GCTaskThread "GC Thread#7" [stack: 0x00000051b5100000,0x00000051b5200000] [id=19384]
  0x000002417f277ad0 GCTaskThread "GC Thread#8" [stack: 0x00000051b5200000,0x00000051b5300000] [id=6632]
  0x000002417f278590 GCTaskThread "GC Thread#9" [stack: 0x00000051b5300000,0x00000051b5400000] [id=22040]
  0x000002417f2bd630 GCTaskThread "GC Thread#10" [stack: 0x00000051b5400000,0x00000051b5500000] [id=27720]
  0x000002417f2bc8c0 GCTaskThread "GC Thread#11" [stack: 0x00000051b5500000,0x00000051b5600000] [id=2404]
  0x000002413986bc00 GCTaskThread "GC Thread#12" [stack: 0x00000051b5a00000,0x00000051b5b00000] [id=28188]
  0x000002410a8e76a0 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000051b3700000,0x00000051b3800000] [id=9428]
  0x000002410a8e7ec0 ConcurrentGCThread "G1 Conc#0" [stack: 0x00000051b3800000,0x00000051b3900000] [id=20828]
  0x000002417f2bd380 ConcurrentGCThread "G1 Conc#1" [stack: 0x00000051b5600000,0x00000051b5700000] [id=19636]
  0x000002417f2bdb90 ConcurrentGCThread "G1 Conc#2" [stack: 0x00000051b5700000,0x00000051b5800000] [id=29540]
  0x0000024138defed0 ConcurrentGCThread "G1 Refine#0" [stack: 0x00000051b3900000,0x00000051b3a00000] [id=11800]
  0x0000024100a3cb10 ConcurrentGCThread "G1 Refine#1" [stack: 0x00000051b5b00000,0x00000051b5c00000] [id=29388]
  0x0000024138df0700 ConcurrentGCThread "G1 Service" [stack: 0x00000051b3a00000,0x00000051b3b00000] [id=3328]

Threads with active compile tasks:
C2 CompilerThread0    16943 4396   !   4       org.jetbrains.kotlin.metadata.ProtoBuf$ValueParameter::<init> (513 bytes)
C2 CompilerThread1    16943 4415       4       org.jetbrains.kotlin.metadata.ProtoBuf$Function::isInitialized (251 bytes)
C2 CompilerThread2    16943 4124       4       java.lang.ClassLoader::loadClass (7 bytes)
C2 CompilerThread3    16943 4417   !   4       org.jetbrains.kotlin.metadata.ProtoBuf$Type::<init> (875 bytes)

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000002410a823a80] Threads_lock - owner thread: 0x0000024138eb8440
[0x000002410a823600] Heap_lock - owner thread: 0x000002410a826180

Heap address: 0x0000000604800000, size: 8120 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000002413a000000-0x000002413abd0000-0x000002413abd0000), size 12386304, SharedBaseAddress: 0x000002413a000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002413b000000-0x000002417b000000, reserved size: 1073741824
Narrow klass base: 0x000002413a000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32479M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8120M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 81920K, used 33113K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 36876K, committed 37120K, reserved 1114112K
  class space    used 4440K, committed 4544K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%|HS|  |TAMS 0x0000000604c00000, 0x0000000604800000| Complete 
|   1|0x0000000604c00000, 0x0000000605000000, 0x0000000605000000|100%| O|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   2|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605400000, 0x0000000605000000| Untracked 
|   3|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%| O|  |TAMS 0x0000000605800000, 0x0000000605400000| Untracked 
|   4|0x0000000605800000, 0x0000000605c00000, 0x0000000605c00000|100%| O|  |TAMS 0x0000000605c00000, 0x0000000605800000| Untracked 
|   5|0x0000000605c00000, 0x0000000606000000, 0x0000000606000000|100%| O|  |TAMS 0x0000000605e40a00, 0x0000000605c00000| Untracked 
|   6|0x0000000606000000, 0x0000000606400000, 0x0000000606400000|100%| O|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|   7|0x0000000606400000, 0x0000000606490c00, 0x0000000606800000| 14%| O|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|   8|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|   9|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  10|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  11|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  12|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  13|0x0000000607c00000, 0x0000000607fc5a00, 0x0000000608000000| 94%| S|CS|TAMS 0x0000000607c00000, 0x0000000607c00000| Complete 
|  14|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  15|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
| 111|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000, 0x0000000620400000| Untracked 
| 112|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000, 0x0000000620800000| Untracked 
| 113|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000, 0x0000000620c00000| Untracked 
| 126|0x0000000624000000, 0x0000000624000000, 0x0000000624400000|  0%| F|  |TAMS 0x0000000624000000, 0x0000000624000000| Untracked 

Card table byte_map: [0x0000024124870000,0x0000024125850000] _byte_map_base: 0x000002412184c000

Marking Bits (Prev, Next): (CMBitMap*) 0x000002410a8d6fb0, (CMBitMap*) 0x000002410a8d6ff0
 Prev Bits: [0x0000024126830000, 0x000002412e710000)
 Next Bits: [0x000002412e710000, 0x00000241365f0000)

Polling page: 0x000002410a900000

Metaspace:

Usage:
  Non-class:     31.68 MB used.
      Class:      4.34 MB used.
       Both:     36.01 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      31.81 MB ( 50%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.44 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      36.25 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  14.72 MB
       Class:  11.57 MB
        Both:  26.28 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 55.94 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 170.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 578.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 1446.
num_chunk_merges: 6.
num_chunk_splits: 1175.
num_chunks_enlarged: 1047.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=2539Kb max_used=2539Kb free=116628Kb
 bounds [0x000002411b470000, 0x000002411b6f0000, 0x00000241228d0000]
CodeHeap 'profiled nmethods': size=119104Kb used=10254Kb max_used=10254Kb free=108850Kb
 bounds [0x00000241138d0000, 0x00000241142e0000, 0x000002411ad20000]
CodeHeap 'non-nmethods': size=7488Kb used=2957Kb max_used=3071Kb free=4530Kb
 bounds [0x000002411ad20000, 0x000002411b030000, 0x000002411b470000]
 total_blobs=5112 nmethods=4529 adapters=492
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 16.863 Thread 0x0000024100dab350 4534       3       org.jetbrains.kotlin.resolve.scopes.BaseHierarchicalScope::getContributedClassifier (14 bytes)
Event: 16.864 Thread 0x0000024100dab350 nmethod 4534 0x00000241142d1310 code [0x00000241142d15a0, 0x00000241142d1fd8]
Event: 16.865 Thread 0x00000241004aa030 4535       3       kotlin.jvm.internal.FunctionReference::<init> (11 bytes)
Event: 16.865 Thread 0x00000241004aa030 nmethod 4535 0x00000241142d2390 code [0x00000241142d2540, 0x00000241142d26a8]
Event: 16.869 Thread 0x0000024101428010 nmethod 4421 0x000002411b6e5e90 code [0x000002411b6e61a0, 0x000002411b6e7d20]
Event: 16.869 Thread 0x0000024101428010 4512       4       org.jetbrains.kotlin.parsing.AbstractKotlinParsing::tokenMatches (48 bytes)
Event: 16.870 Thread 0x0000024138ee4400 4536       3       org.jetbrains.kotlin.metadata.ProtoBuf$Type::hasClassName (18 bytes)
Event: 16.870 Thread 0x0000024138ee4400 nmethod 4536 0x00000241142d2790 code [0x00000241142d2920, 0x00000241142d2a98]
Event: 16.870 Thread 0x0000024138ee4400 4538       3       org.jetbrains.kotlin.protobuf.GeneratedMessageLite$ExtendableMessage::getExtension (32 bytes)
Event: 16.871 Thread 0x00000241005823c0 4537       1       org.jetbrains.kotlin.serialization.deserialization.DeserializationComponents::getTypeAttributeTranslators (5 bytes)
Event: 16.871 Thread 0x00000241005823c0 nmethod 4537 0x000002411b6e9110 code [0x000002411b6e92a0, 0x000002411b6e9378]
Event: 16.871 Thread 0x0000024138ee4400 nmethod 4538 0x00000241142d2b10 code [0x00000241142d2d40, 0x00000241142d35a8]
Event: 16.871 Thread 0x00000241005823c0 4539       1       org.jetbrains.kotlin.metadata.ProtoBuf$Type::getArgumentList (5 bytes)
Event: 16.871 Thread 0x0000024138ee4400 4540       1       org.jetbrains.kotlin.metadata.ProtoBuf$Type::getNullable (5 bytes)
Event: 16.871 Thread 0x0000024138ee4400 nmethod 4540 0x000002411b6e9410 code [0x000002411b6e95a0, 0x000002411b6e9678]
Event: 16.871 Thread 0x00000241005823c0 nmethod 4539 0x000002411b6e9710 code [0x000002411b6e98a0, 0x000002411b6e9978]
Event: 16.872 Thread 0x0000024101428010 nmethod 4512 0x000002411b6e9a10 code [0x000002411b6e9bc0, 0x000002411b6e9d78]
Event: 16.872 Thread 0x0000024101428010 4406       4       java.lang.String::replace (42 bytes)
Event: 16.886 Thread 0x0000024101428010 nmethod 4406 0x000002411b6ea290 code [0x000002411b6ea440, 0x000002411b6eab48]
Event: 16.886 Thread 0x0000024101428010 4415       4       org.jetbrains.kotlin.metadata.ProtoBuf$Function::isInitialized (251 bytes)

GC Heap History (15 events):
Event: 2.470 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 520192K, used 24576K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 6779K, committed 6912K, reserved 1114112K
  class space    used 700K, committed 768K, reserved 1048576K
}
Event: 2.510 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 6926K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 6779K, committed 6912K, reserved 1114112K
  class space    used 700K, committed 768K, reserved 1048576K
}
Event: 5.330 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 35598K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 15275K, committed 15488K, reserved 1114112K
  class space    used 2017K, committed 2112K, reserved 1048576K
}
Event: 5.333 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 9083K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 15275K, committed 15488K, reserved 1114112K
  class space    used 2017K, committed 2112K, reserved 1048576K
}
Event: 10.715 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 50043K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 13 young (53248K), 1 survivors (4096K)
 Metaspace       used 21370K, committed 21504K, reserved 1114112K
  class space    used 2881K, committed 2944K, reserved 1048576K
}
Event: 10.720 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 520192K, used 15454K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 21370K, committed 21504K, reserved 1114112K
  class space    used 2881K, committed 2944K, reserved 1048576K
}
Event: 12.426 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 81920K, used 52318K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 3 survivors (12288K)
 Metaspace       used 28542K, committed 28736K, reserved 1114112K
  class space    used 3582K, committed 3648K, reserved 1048576K
}
Event: 12.432 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 81920K, used 19019K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 28542K, committed 28736K, reserved 1114112K
  class space    used 3582K, committed 3648K, reserved 1048576K
}
Event: 12.732 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 81920K, used 23115K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 29556K, committed 29760K, reserved 1114112K
  class space    used 3659K, committed 3712K, reserved 1048576K
}
Event: 12.742 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 81920K, used 20025K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 29556K, committed 29760K, reserved 1114112K
  class space    used 3659K, committed 3712K, reserved 1048576K
}
Event: 14.783 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 81920K, used 52793K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 9 young (36864K), 1 survivors (4096K)
 Metaspace       used 34134K, committed 34304K, reserved 1114112K
  class space    used 4192K, committed 4288K, reserved 1048576K
}
Event: 14.791 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 81920K, used 25588K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 34134K, committed 34304K, reserved 1114112K
  class space    used 4192K, committed 4288K, reserved 1048576K
}
Event: 15.740 GC heap before
{Heap before GC invocations=8 (full 0):
 garbage-first heap   total 81920K, used 50164K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 9 young (36864K), 2 survivors (8192K)
 Metaspace       used 34930K, committed 35200K, reserved 1114112K
  class space    used 4260K, committed 4416K, reserved 1048576K
}
Event: 15.759 GC heap after
{Heap after GC invocations=9 (full 0):
 garbage-first heap   total 81920K, used 29079K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 34930K, committed 35200K, reserved 1114112K
  class space    used 4260K, committed 4416K, reserved 1048576K
}
Event: 16.909 GC heap before
{Heap before GC invocations=9 (full 0):
 garbage-first heap   total 81920K, used 53655K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 36876K, committed 37120K, reserved 1114112K
  class space    used 4440K, committed 4544K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 16.863 Thread 0x000002410a826180 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002411b6928c0 relative=0x0000000000000480
Event: 16.863 Thread 0x000002410a826180 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002411b6928c0 method=org.jetbrains.kotlin.com.intellij.psi.impl.source.CharTableImpl$StringHashToCharSequencesMap.charSequenceSubSequenceEquals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;II
Event: 16.863 Thread 0x000002410a826180 DEOPT PACKING pc=0x000002411b6928c0 sp=0x00000051b35fcbb0
Event: 16.863 Thread 0x000002410a826180 DEOPT UNPACKING pc=0x000002411ad723a3 sp=0x00000051b35fcb88 mode 2
Event: 16.863 Thread 0x000002410a826180 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002411b6ada5c relative=0x00000000000055dc
Event: 16.863 Thread 0x000002410a826180 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002411b6ada5c method=org.jetbrains.kotlin.com.intellij.psi.impl.source.CharTableImpl$StringHashToCharSequencesMap.getSubSequenceWithHashCode(ILjava/lang/CharSequence;II)Ljava/lang/CharSequen
Event: 16.863 Thread 0x000002410a826180 DEOPT PACKING pc=0x000002411b6ada5c sp=0x00000051b35fced0
Event: 16.863 Thread 0x000002410a826180 DEOPT UNPACKING pc=0x000002411ad723a3 sp=0x00000051b35fcc28 mode 2
Event: 16.863 Thread 0x000002410a826180 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000002411b6cf738 relative=0x0000000000000278
Event: 16.863 Thread 0x000002410a826180 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000002411b6cf738 method=org.jetbrains.kotlin.com.intellij.psi.impl.source.tree.CompositeElement.drillDown(Lorg/jetbrains/kotlin/com/intellij/psi/impl/source/tree/TreeEleme
Event: 16.863 Thread 0x000002410a826180 DEOPT PACKING pc=0x000002411b6cf738 sp=0x00000051b35fcfe0
Event: 16.863 Thread 0x000002410a826180 DEOPT UNPACKING pc=0x000002411ad723a3 sp=0x00000051b35fcf00 mode 2
Event: 16.863 Thread 0x000002410a826180 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002411b6a280c relative=0x000000000000058c
Event: 16.863 Thread 0x000002410a826180 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002411b6a280c method=org.jetbrains.kotlin.storage.StorageKt.getValue(Lorg/jetbrains/kotlin/storage/NotNullLazyValue;Ljava/lang/Object;Lkotlin/reflect/KProperty;)Ljava/lang/Object; @ 13 c2
Event: 16.863 Thread 0x000002410a826180 DEOPT PACKING pc=0x000002411b6a280c sp=0x00000051b35fe160
Event: 16.863 Thread 0x000002410a826180 DEOPT UNPACKING pc=0x000002411ad723a3 sp=0x00000051b35fe140 mode 2
Event: 16.878 Thread 0x000002410a826180 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002411b621fac relative=0x000000000000020c
Event: 16.878 Thread 0x000002410a826180 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002411b621fac method=org.jetbrains.kotlin.name.Name.hashCode()I @ 16 c2
Event: 16.878 Thread 0x000002410a826180 DEOPT PACKING pc=0x000002411b621fac sp=0x00000051b35fd500
Event: 16.878 Thread 0x000002410a826180 DEOPT UNPACKING pc=0x000002411ad723a3 sp=0x00000051b35fd488 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 2.734 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d58380}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, long, long)'> (0x0000000623d58380) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.742 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d66998}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int)'> (0x0000000623d66998) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.798 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623e57968}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623e57968) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.803 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623e807b0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623e807b0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.834 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623f60480}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623f60480) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.066 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623a843a0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623a843a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.506 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623649d28}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623649d28) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.723 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623307b30}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000623307b30) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.798 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233826d0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006233826d0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.823 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c3d938}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c3d938) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 9.801 Thread 0x000002410a826180 Implicit null exception at 0x000002411b4971f2 to 0x000002411b49784c
Event: 10.199 Thread 0x000002410a826180 Implicit null exception at 0x000002411b498172 to 0x000002411b4987ec
Event: 10.367 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x000000062192f380}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062192f380) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 12.442 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x000000062403f908}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x000000062403f908) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 12.616 Thread 0x000002410a826180 Implicit null exception at 0x000002411b5652d1 to 0x000002411b565488
Event: 12.630 Thread 0x000002410a826180 Implicit null exception at 0x000002411b565d50 to 0x000002411b565e88
Event: 13.028 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x000000062088b4e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000062088b4e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.028 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x000000062088fb70}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000062088fb70) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 13.029 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x00000006208939e0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006208939e0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 16.877 Thread 0x000002410a826180 Exception <a 'java/lang/NoSuchMethodError'{0x00000006083276c8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006083276c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 14.791 Executing VM operation: G1CollectForAllocation done
Event: 14.806 Executing VM operation: G1Concurrent
Event: 14.838 Executing VM operation: G1Concurrent done
Event: 14.842 Executing VM operation: G1Concurrent
Event: 14.845 Executing VM operation: G1Concurrent done
Event: 15.416 Executing VM operation: ICBufferFull
Event: 15.416 Executing VM operation: ICBufferFull done
Event: 15.740 Executing VM operation: G1CollectForAllocation
Event: 15.759 Executing VM operation: G1CollectForAllocation done
Event: 15.783 Executing VM operation: HandshakeAllThreads
Event: 15.783 Executing VM operation: HandshakeAllThreads done
Event: 15.808 Executing VM operation: HandshakeAllThreads
Event: 15.808 Executing VM operation: HandshakeAllThreads done
Event: 15.813 Executing VM operation: HandshakeAllThreads
Event: 15.813 Executing VM operation: HandshakeAllThreads done
Event: 16.335 Executing VM operation: HandshakeAllThreads
Event: 16.335 Executing VM operation: HandshakeAllThreads done
Event: 16.472 Executing VM operation: ICBufferFull
Event: 16.472 Executing VM operation: ICBufferFull done
Event: 16.909 Executing VM operation: G1CollectForAllocation

Events (20 events):
Event: 13.935 loading class java/util/AbstractList$SubList$1 done
Event: 14.007 loading class java/util/stream/IntPipeline$1
Event: 14.007 loading class java/util/stream/IntPipeline$1 done
Event: 14.014 loading class java/util/stream/IntPipeline$1$1
Event: 14.014 loading class java/util/stream/IntPipeline$1$1 done
Event: 14.145 Thread 0x00000241007e5500 Thread exited: 0x00000241007e5500
Event: 14.145 Thread 0x00000241007e4ff0 Thread exited: 0x00000241007e4ff0
Event: 14.285 loading class java/util/ArrayList$SubList$1
Event: 14.285 loading class java/util/ArrayList$SubList$1 done
Event: 14.356 loading class java/text/StringCharacterIterator
Event: 14.356 loading class java/text/CharacterIterator
Event: 14.356 loading class java/text/CharacterIterator done
Event: 14.356 loading class java/text/StringCharacterIterator done
Event: 14.795 Thread 0x0000024101428010 Thread added: 0x0000024101428010
Event: 14.795 Thread 0x0000024101afe5f0 Thread added: 0x0000024101afe5f0
Event: 14.795 Thread 0x0000024100eedd10 Thread added: 0x0000024100eedd10
Event: 15.539 Thread 0x0000024100eedd10 Thread exited: 0x0000024100eedd10
Event: 15.539 Thread 0x0000024101afe5f0 Thread exited: 0x0000024101afe5f0
Event: 15.709 Thread 0x00000241033a7820 Thread added: 0x00000241033a7820
Event: 16.069 Thread 0x0000024101f9dfe0 Thread added: 0x0000024101f9dfe0


Dynamic libraries:
0x00007ff708300000 - 0x00007ff708310000 	E:\JAVA\bin\java.exe
0x00007ff827030000 - 0x00007ff827228000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8255d0000 - 0x00007ff825692000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff824d50000 - 0x00007ff825046000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8246c0000 - 0x00007ff8247c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff81fde0000 - 0x00007ff81fdf9000 	E:\JAVA\bin\jli.dll
0x00007ff826e80000 - 0x00007ff826f31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff826340000 - 0x00007ff8263de000 	C:\Windows\System32\msvcrt.dll
0x00007ff825e80000 - 0x00007ff825f1f000 	C:\Windows\System32\sechost.dll
0x00007ff81fdc0000 - 0x00007ff81fddb000 	E:\JAVA\bin\VCRUNTIME140.dll
0x00007ff825b10000 - 0x00007ff825c33000 	C:\Windows\System32\RPCRT4.dll
0x00007ff824a20000 - 0x00007ff824a47000 	C:\Windows\System32\bcrypt.dll
0x00007ff825050000 - 0x00007ff8251ed000 	C:\Windows\System32\USER32.dll
0x00007ff8248a0000 - 0x00007ff8248c2000 	C:\Windows\System32\win32u.dll
0x00007ff825da0000 - 0x00007ff825dcb000 	C:\Windows\System32\GDI32.dll
0x00007ff824a50000 - 0x00007ff824b69000 	C:\Windows\System32\gdi32full.dll
0x00007ff824980000 - 0x00007ff824a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff823400000 - 0x00007ff82340a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff81dfe0000 - 0x00007ff81e27a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8253f0000 - 0x00007ff82541f000 	C:\Windows\System32\IMM32.DLL
0x00007ff81fd50000 - 0x00007ff81fd5c000 	E:\JAVA\bin\vcruntime140_1.dll
0x00007ffffb0c0000 - 0x00007ffffb14e000 	E:\JAVA\bin\msvcp140.dll
0x00007fffe0350000 - 0x00007fffe0f2e000 	E:\JAVA\bin\server\jvm.dll
0x00007ff826f40000 - 0x00007ff826f48000 	C:\Windows\System32\PSAPI.DLL
0x00007fffeb340000 - 0x00007fffeb349000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff826e10000 - 0x00007ff826e7b000 	C:\Windows\System32\WS2_32.dll
0x00007ff81e3c0000 - 0x00007ff81e3e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff823360000 - 0x00007ff823372000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff81c4f0000 - 0x00007ff81c4fa000 	E:\JAVA\bin\jimage.dll
0x00007ff821e10000 - 0x00007ff822011000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff80fef0000 - 0x00007ff80ff24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff824810000 - 0x00007ff824892000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff818d20000 - 0x00007ff818d45000 	E:\JAVA\bin\java.dll
0x00007fffef390000 - 0x00007fffef467000 	E:\JAVA\bin\jsvml.dll
0x00007ff826570000 - 0x00007ff826cde000 	C:\Windows\System32\SHELL32.dll
0x00007ff8222d0000 - 0x00007ff822a74000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff825fd0000 - 0x00007ff826323000 	C:\Windows\System32\combase.dll
0x00007ff8240f0000 - 0x00007ff82411b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff826d40000 - 0x00007ff826e0d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff825f20000 - 0x00007ff825fcd000 	C:\Windows\System32\SHCORE.dll
0x00007ff825d40000 - 0x00007ff825d9b000 	C:\Windows\System32\shlwapi.dll
0x00007ff8245f0000 - 0x00007ff824614000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff811d70000 - 0x00007ff811d89000 	E:\JAVA\bin\net.dll
0x00007ff81ede0000 - 0x00007ff81eeea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff823e50000 - 0x00007ff823eba000 	C:\Windows\system32\mswsock.dll
0x00007ff810fb0000 - 0x00007ff810fc6000 	E:\JAVA\bin\nio.dll
0x00007ff81bc20000 - 0x00007ff81bc38000 	E:\JAVA\bin\zip.dll
0x00007ff81c4d0000 - 0x00007ff81c4e0000 	E:\JAVA\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\JAVA\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;E:\JAVA\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\FileBrowser\build\20250901_10351852868768062266.compiler.options
java_class_path (initial): E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.22\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\kotlin-compiler-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.22\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\kotlin-script-runtime-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.22\20e2c5df715f3240c765cfc222530e2796542021\kotlin-daemon-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8514437120                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8514437120                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\JAVA
CLASSPATH=.;E:\JAVA\lib\dt.jar;E:\JAVA\lib\tools.jar;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\dotnet\;E:\git\Git\cmd;D:\Users\W9096060\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\dotnet\;E:\1.8java\lib\dt.jar;E:\1.8java\lib\tools.jar;E:\1.8java\bin;E:\JAVA\bin;E:\JAVA\lib;D:\Users\W9096060\AppData\Local\Microsoft\WindowsApps;
USERNAME=W9096060
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 19:35 hours

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 32479M (783M free)
TotalPageFile size 47840M (AvailPageFile size 130M)
current process WorkingSet (physical memory assigned to process): 234M, peak: 234M
current process commit charge ("private bytes"): 287M, peak: 678M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211) for windows-amd64 JRE (17.0.8+9-LTS-211), built on Jun 14 2023 10:34:31 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
