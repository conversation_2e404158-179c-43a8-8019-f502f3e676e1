/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileOperateUtil.kt
 ** Description: operate util
 ** Version: 1.0
 ** Date: 2023/6/20
 ** Author: hank.zhou(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate

import android.app.Activity
import android.net.Uri
import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.fileutils.FileDeleteHelper
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.fileutils.checkDestStorageSpace
import com.filemanager.common.fileutils.sDFMToFileDirectoryPath
import com.filemanager.common.utils.COUISnackBarUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.copy.FileCopyHelper
import com.filemanager.fileoperate.decompress.DecompressHelper
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_FAILED
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_NOT_ENOUGH
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.OutputStream
import java.nio.file.Files
import java.nio.file.Path
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

object FileOperateUtil {
    const val TAG = "FileOperateUtil"
    const val MIN_FILE_SIZE = 10 * 1024 * 1024L
    const val FILE_SIZE_GAP = 1024 * 1024L
    private val ILLEGAL_PATTERN = Regex("[/:*?\"<>|]")
    private const val HIDDEN = "."
    private const val UNDERLINE = "_"
    private const val FILE_MANAGER_URI_PREFIX = "content://com.coloros.filemanager/root"

    @JvmStatic
    fun saveText(text: String, destPath: Path, listener: FileCopyHelper.OnDealFileListener) {
        val fileSize = text.toByteArray().size.toLong()
        val file = destPath.toFile()
        var outputStream: OutputStream? = null
        try {
            outputStream = FileOutputStream(file)
            outputStream.write(text.toByteArray())
            MediaScannerCompat.sendMediaScanner(file.absolutePath, Utils.MEDIA_SCAN_SAVE)
            listener.addProgress(fileSize)
            listener.onSuccess(destPath.toFile(), destPath.toFile(), false)
        } catch (e: IOException) {
            listener.onError(destPath.toFile(), destPath.toFile())
            Log.d(FileOperateApi.TAG, "saveFile e = $e")
        } finally {
            try {
                outputStream?.close()
            } catch (e: IOException) {
                Log.d(FileOperateApi.TAG, "handleSendTextFile e = $e")
            }
        }
    }

    @JvmStatic
    fun savedFileNotice(activity: Activity, path: String?) {
        val textId = com.filemanager.common.R.string.drag_saved_success
        if (path != null) {
            val panel = (activity as? COUISnackBarUtils.ShowPanel)?.getPanelView()
            if (panel == null) {
                COUISnackBarUtils.show(activity, textId, com.filemanager.common.R.string.drag_preview) {
                    val fileBrowser = Injector.injectFactory<IFileBrowser>()
                    fileBrowser?.toFileBrowserActivity(activity, path)
                }
            } else {
                panel.let {
                    COUISnackBarUtils.showWithView(activity, panel, textId, com.filemanager.common.R.string.drag_preview) {
                        val fileBrowser = Injector.injectFactory<IFileBrowser>()
                        fileBrowser?.toFileBrowserActivity(activity, path)
                    }
                }
            }
        } else {
            CustomToast.showLong(textId)
        }
    }

    @JvmStatic
    fun saveFileFailed() {
        CustomToast.showLong(com.filemanager.common.R.string.drag_saved_fail)
    }

    @JvmStatic
    fun dropFileNotice(activity: Activity, path: String?) {
        val textId = com.filemanager.common.R.string.drag_in_success
        if (path != null) {
            COUISnackBarUtils.show(activity, textId, com.filemanager.common.R.string.drag_preview) {
                val fileBrowser = Injector.injectFactory<IFileBrowser>()
                fileBrowser?.toFileBrowserActivity(activity, path)
            }
        } else {
            CustomToast.showLong(textId)
        }
    }

    @JvmStatic
    fun dropFileFailed() {
        CustomToast.showLong(com.filemanager.common.R.string.drag_not_support_position)
    }

    @JvmStatic
    fun createSpaceNotEnoughDialog(activity: Activity): AlertDialog {
        val dialog = COUIAlertDialogBuilder(activity).setTitle(activity.getString(com.filemanager.common.R.string.phone_storage_can_not_save))
            .setPositiveButton(com.filemanager.common.R.string.garbage_cleanup) { _, _ ->
                KtAppUtils.startPhoneManager(activity)
            }
            .setNegativeButton(com.filemanager.common.R.string.alert_dialog_cancel, null)
            .create()
        dialog.setCanceledOnTouchOutside(false)
        return dialog
    }

    @JvmStatic
    fun checkDestPathIsEnoughSpace(dest: String, fileSize: Long): Boolean {
        val baseFileBean = BaseFileBean()
        baseFileBean.mData = dest
        val state = checkDestStorageSpace(baseFileBean, fileSize)
        return state.first.not()
    }

    @JvmStatic
    fun showSpaceNotEnoughTips(activity: Activity) {
        if (FeatureCompat.sPhoneManagerStartInfo != null) {
            createSpaceNotEnoughDialog(activity).show()
        } else {
            CustomToast.showShort(activity.getString(com.filemanager.common.R.string.phone_storage_can_not_save))
        }
    }

    @JvmStatic
    fun getDateFileName(index: Int, ext: String): String {
        val date = Date()
        val f = SimpleDateFormat(FileOperateApi.TIME_PATTERN, Locale.CHINA)
        return if (index == 0) {
            FileOperateApi.FILE_NAME_PREFIX + f.format(date) + ext
        } else {
            FileOperateApi.FILE_NAME_PREFIX + f.format(date) + "_" + index.toString() + ext
        }
    }

    @JvmStatic
    fun getBaseFileBeanForString(text: String, activity: Activity, fileName: String): BaseFileBean {
        val bean = BaseFileBean()
        val hashCode = text.hashCode()
        bean.mData = activity.cacheDir.absolutePath + File.separator + hashCode + File.separator + fileName
        bean.mSize = text.toByteArray().size.toLong()
        bean.mDisplayName = fileName
        bean.virtualFileHashCode = hashCode.toString()
        bean.isString = true
        bean.stringData = text
        return bean
    }

    @JvmStatic
    fun getBaseFileBeanForUri(activity: Activity, uri: Uri): BaseFileBean {
        val property = FileMediaHelper.getOpenableProperty(activity, uri)
        val bean = BaseFileBean()
        val hashCode = uri.hashCode()
        val displayName = formatRiskName(property.mDisplayName ?: "")
        bean.mData = activity.cacheDir.absolutePath + File.separator + hashCode + File.separator + displayName
        bean.mLocalFileUri = uri
        bean.mSize = property.mSize
        bean.mDisplayName = displayName
        bean.virtualFileHashCode = hashCode.toString()
        Log.d(TAG, "getBaseFileBeanForUri name = ${property.mDisplayName}, $displayName, size = ${property.mSize} uri = $uri")
        return bean
    }

    @JvmStatic
    fun getMediaFileBeanForUri(activity: Activity, uri: Uri): BaseFileBean {
        val property = FileMediaHelper.getOpenableProperty(activity, uri)
        val bean = BaseFileBean()
        val hashCode = uri.hashCode()
        val displayName = formatRiskName(property.mDisplayName ?: "")
        bean.mData = property.mData ?: ""
        bean.mLocalFileUri = uri
        bean.mSize = property.mSize
        bean.mDisplayName = displayName
        bean.virtualFileHashCode = hashCode.toString()
        Log.d(TAG, "getMediaFileBeanForUri name = ${property.mDisplayName}, $displayName, size = ${property.mSize} uri = $uri")
        return bean
    }

    @JvmStatic
    fun getFileBean(path: String): BaseFileBean {
        val bean = BaseFileBean()
        val file = File(path)
        bean.mData = path
        bean.mIsDirectory = false
        bean.mSize = file.length()
        bean.mDisplayName = file.name
        return bean
    }

    @JvmStatic
    fun getFolderFileBean(folderPath: String): BaseFileBean {
        val bean = BaseFileBean()
        val file = File(folderPath)
        bean.mData = folderPath
        bean.mIsDirectory = true
        bean.mSize = file.length()
        bean.mDisplayName = file.name
        return bean
    }

    @VisibleForTesting
    @JvmStatic
    fun formatRiskName(name: String): String {
        var result = name.replace(regex = ILLEGAL_PATTERN, "")
        if (result.isEmpty()) {
            return UNDERLINE
        }
        if (result.startsWith(HIDDEN)) {
            result = if (result.length == 1) {
                UNDERLINE
            } else {
                UNDERLINE + result.substring(1)
            }
        }
        return result
    }

    @WorkerThread
    @JvmStatic
    fun copyToTempFileByDfm(sourceBaseFile: BaseFileBean, destBaseFile: BaseFileBean): Triple<Boolean, BaseFileBean?, Int> {
        Log.d(TAG, "copyToTempFileByDfm sourceBaseFile = $sourceBaseFile,destBaseFile=$destBaseFile")
        val tempFile = File(sDFMToFileDirectoryPath.plus(sourceBaseFile.mData ?: "temp"))
        if (!tempFile.exists()) {
            sDFMToFileDirectoryPath?.apply {
                Log.d(TAG, "deleteDfmTempDirectory sDFMToFileDirectoryPath=$this")
                FileDeleteHelper().delete(PathFileWrapper(this))
            }
            val storageState = checkDestStorageSpace(destBaseFile, sourceBaseFile.mSize)
            if (!storageState.first) {
                val sourceFile = File(sourceBaseFile.mData)
                val mDealFileListener = object : FileCopyHelper.OnDealFileListener() {
                    override var isCancel: () -> Boolean = { Thread.currentThread().isInterrupted }
                    override fun onProgressChanged(progress: Long) {
                    }

                    override fun onSuccess(sourceFile: File, destFile: File, isDirectory: Boolean) {
                    }

                    // return true means skip this failed file, false means cancel copy file
                    override fun onError(sourceFile: File, destFile: File): Boolean {
                        Log.d(TAG, "onError cancel $isCancel")
                        isCancel = { true }
                        return false
                    }
                }
                if (!Files.isDirectory(tempFile.toPath().parent)) {
                    Files.createDirectories(tempFile.toPath().parent)
                }
                FileCopyHelper.copyFile(sourceFile, tempFile, false, mDealFileListener)
                val baseFileBean = BaseFileBean()
                baseFileBean.mData = tempFile.toString()
                baseFileBean.mDisplayName = tempFile.name
                baseFileBean.mSize = sourceBaseFile.mSize
                return Triple(true, baseFileBean, DecompressHelper.PREVIEW_SUCCESS)
            } else {
                return Triple(false, null, PREVIEW_NOT_ENOUGH)
            }
        } else {
            val baseFileBean = BaseFileBean()
            baseFileBean.mData = tempFile.toString()
            baseFileBean.mDisplayName = tempFile.name
            baseFileBean.mSize = sourceBaseFile.mSize
            return Triple(true, baseFileBean, DecompressHelper.PREVIEW_SUCCESS)
        }
        return Triple(false, null, PREVIEW_FAILED)
    }
}