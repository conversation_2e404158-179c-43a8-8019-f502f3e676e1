package com.oplus.filemanager.appmarketmanager

import android.content.Context
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.SdkUtils
import com.oplus.util.OplusPlatformLevelUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [33])  // 将SDK版本从28改为33以匹配SdkUtils.isAtLeastT()的要求
class PlatformManagerTest {

    private lateinit var mockContext: Context
    private lateinit var mockPlatformLevelUtils: OplusPlatformLevelUtils

    @Before
    fun setUp() {
        mockContext = mockk(relaxed = true)
        mockPlatformLevelUtils = mockk()
        mockkStatic(AppUtils::class)
        mockkStatic(SdkUtils::class)
        mockkStatic(PropertyCompat::class)
        mockkStatic(OplusPlatformLevelUtils::class)
        
        // 确保每次测试前静态变量重置
        every { PropertyCompat.sAnimLevel } returns "0"
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `isHighEndMachine should return true when model is in HIGH_END_MACHINE list`() {
        // Given
        val highEndModel = "PAFT10"
        every { AppUtils.getModel() } returns highEndModel

        // When
        val result = isHighEndMachine(mockContext)

        // Then
        assertTrue(result)
    }

    @Test
    fun `isHighEndMachine should return false when model is not in HIGH_END_MACHINE list and SDK below T`() {
        // Given
        val normalModel = "NORMAL_MODEL"
        every { AppUtils.getModel() } returns normalModel
        every { SdkUtils.isAtLeastT() } returns false

        // When
        val result = isHighEndMachine(mockContext)

        // Then
        assertFalse(result)
    }

    @Test
    fun `isHighEndMachine should return true when platform level is HIGH and SDK at least T`() {
        // Given
        val normalModel = "NORMAL_MODEL"
        every { AppUtils.getModel() } returns normalModel
        every { SdkUtils.isAtLeastT() } returns true
        every { OplusPlatformLevelUtils.getInstance(any()) } returns mockPlatformLevelUtils
        every { mockPlatformLevelUtils.getPlatformLevel(OplusPlatformLevelUtils.LEVEL_TYPE_FOR_CPU) } returns OplusPlatformLevelUtils.LEVEL_HIGH

        // When
        val result = isHighEndMachine(mockContext)

        // Then
        assertTrue(result)
    }

    @Test
    fun `isHighEndMachine should return false when platform level is LOW and SDK at least T`() {
        // Given
        val normalModel = "NORMAL_MODEL"
        every { AppUtils.getModel() } returns normalModel
        every { SdkUtils.isAtLeastT() } returns true
        every { OplusPlatformLevelUtils.getInstance(any()) } returns mockPlatformLevelUtils
        every { mockPlatformLevelUtils.getPlatformLevel(OplusPlatformLevelUtils.LEVEL_TYPE_FOR_CPU) } returns OplusPlatformLevelUtils.LEVEL_LOW

        // When
        val result = isHighEndMachine(mockContext)

        // Then
        assertFalse(result)
    }

    @Test
    fun `isHighEndMachine should return true when platform level is UNDEF and anim level is high`() {
        // Given
        val normalModel = "NORMAL_MODEL"
        every { AppUtils.getModel() } returns normalModel
        every { SdkUtils.isAtLeastT() } returns true
        every { OplusPlatformLevelUtils.getInstance(any()) } returns mockPlatformLevelUtils
        every { mockPlatformLevelUtils.getPlatformLevel(OplusPlatformLevelUtils.LEVEL_TYPE_FOR_CPU) } returns OplusPlatformLevelUtils.LEVEL_UNDEF
        every { PropertyCompat.sAnimLevel } returns "1"

        // When
        val result = isHighEndMachine(mockContext)

        // Then
        assertTrue(result)
    }

    @Test
    fun `isHighEndMachine should return false when platform level is UNDEF and anim level is not high`() {
        // Given
        val normalModel = "NORMAL_MODEL"
        every { AppUtils.getModel() } returns normalModel
        every { SdkUtils.isAtLeastT() } returns true
        every { OplusPlatformLevelUtils.getInstance(any()) } returns mockPlatformLevelUtils
        every { mockPlatformLevelUtils.getPlatformLevel(OplusPlatformLevelUtils.LEVEL_TYPE_FOR_CPU) } returns OplusPlatformLevelUtils.LEVEL_UNDEF
        every { PropertyCompat.sAnimLevel } returns "0"

        // When
        val result = isHighEndMachine(mockContext)

        // Then
        assertFalse(result)
    }

    @Test
    fun `getPlatformLevelByCPU should return platform level from OplusPlatformLevelUtils`() {
        // Given
        val expectedLevel = 2
        every { OplusPlatformLevelUtils.getInstance(any()) } returns mockPlatformLevelUtils
        every { mockPlatformLevelUtils.getPlatformLevel(OplusPlatformLevelUtils.LEVEL_TYPE_FOR_CPU) } returns expectedLevel

        // When
        val result = OplusPlatformLevelUtils.getInstance(mockContext).getPlatformLevel(OplusPlatformLevelUtils.LEVEL_TYPE_FOR_CPU)

        // Then
        assertEquals(expectedLevel, result)
    }
}