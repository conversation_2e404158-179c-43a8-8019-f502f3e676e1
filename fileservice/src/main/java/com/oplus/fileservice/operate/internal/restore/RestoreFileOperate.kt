/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RestoreFileOperate
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 11:26
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/21       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.restore

import android.content.ContentValues
import android.content.Context
import com.filemanager.common.RecycleStore
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.recyclebin.operation.action.QueryBatchAction
import com.filemanager.recyclebin.retriever.DataRetriever
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.fileservice.operate.convert.OperateRequest
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.internal.BaseFileOperate
import com.oplus.fileservice.operate.internal.OperateStatusCode
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.InvalidSourceFileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.NotEnoughSpace

class RestoreFileOperate(
    context: Context
) : BaseFileOperate() {

    private val mRestoreBulk: RestoreAction = RestoreAction(context = context, columnName = RecycleStore.Files.FileColumns.RECYCLE_ID)

    private val allFilePathForLabels = ArrayList<String>()


    @Suppress("TooGenericExceptionCaught")
    override fun run(operateRequest: OperateRequest): OperationResult {
        Log.i(TAG, "run -> operateRequest = $operateRequest")
        var operateResult = OperationResult(false)
        try {
            operateResult = restoreFiles(operateRequest)
        } catch (e: Exception) {
            Log.e(TAG, "run -> restoreFiles has error: ${e.message}")
        } finally {
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.onRestoreFilePaths(allFilePathForLabels)
            mRestoreBulk.flush()
        }
        return operateResult
    }

    override fun recycle() {
        // Do Nothing
    }

    override fun afterRun(result: OperationResult) {
        // Do Nothing
        Log.d(TAG, "afterRun -> result = $result")
    }

    @Suppress("LongMethod")
    private fun restoreFiles(operateRequest: OperateRequest): OperationResult {
        val sourceFileInfos = operateRequest.sourceFileInfo
        if (sourceFileInfos.isNullOrEmpty()) {
            return OperationResult(false, InvalidSourceFileInfo)
        }
        val totalSize = sourceFileInfos.size
        val queryAction = QueryBatchAction(
            column = RecycleStore.Files.FileColumns.RECYCLE_ID,
            bulkCount = QUERY_BATCH_ID_COUNT
        )
        val ids = ArrayList<String>()
        var id: String?
        for (file in sourceFileInfos) {
            id = if (file.recycleId != null) {
                file.recycleId
            } else {
                Log.d(TAG, "restoreFiles file is not recycle bin file")
                continue
            }
            if (id.isNullOrEmpty()) {
                Log.d(TAG, "restoreFiles recycleId is null")
                continue
            }
            queryAction.add(id)
            ids.add(id)
        }
        queryAction.flush()
        val valuesList = queryAction.getBatchResult()
        // count failedCount in batch query
        var failedCount = totalSize - valuesList.size
        val store = DataRetriever.INSTANCE
        var index = 0
        var values: ContentValues?
        var statusCode: Int
        for (tempId in ids) {
            values = valuesList[tempId]
            statusCode = values?.let { internalRestore(store, it) } ?: STATUS_ERROR
            Log.d(TAG, "restoreFiles -> id:$tempId statusCode: $statusCode")
            if (statusCode == STATUS_NOT_ENOUGH_SPACE) {
                index++
                failedCount++
                return OperationResult(
                    false,
                    NotEnoughSpace,
                    "fail count is ${failedCount + (totalSize - index)}"
                )
            }

            if (statusCode != STATUS_SUCCESS) {
                failedCount++
                index++
                Log.d(TAG, "restoreFiles -> index: $index ; failedCount: $failedCount")
                continue
            }
            index++
        }
        mRestoreBulk.flush()
        failedCount += (ids.size - index)
        return if (failedCount > 0) {
            OperationResult(false, OperateStatusCode.OperateFailure)
        } else {
            OperationResult(true, OperateStatusCode.OK)
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun internalRestore(retriever: DataRetriever, values: ContentValues): Int {
        try {
            val statusCode = retriever.restoreToOriginPath(values)
            if (statusCode == STATUS_SUCCESS) {
                val realOriginPath = values.getAsString(RecycleStore.Files.FileColumns.ORIGIN_PATH)
                val id = values.getAsString(RecycleStore.Files.FileColumns.RECYCLE_ID)
                allFilePathForLabels.add(realOriginPath)
                mRestoreBulk.add(id, realOriginPath)
            }
            return statusCode
        } catch (e: Exception) {
            Log.w(TAG, "internalRestore error: ${e.message}")
        }
        return STATUS_ERROR
    }

    companion object {
        private const val TAG = "RestoreFileOperate"
        private const val QUERY_BATCH_ID_COUNT = 3000

        /**
         * status code for all operation in recycleBin,if statusCode is Positive, represent failed count
         */
        // status code for success
        private const val STATUS_SUCCESS = 0

        // status code for common exception
        private const val STATUS_ERROR = -1

        // status code for low space exception
        private const val STATUS_NOT_ENOUGH_SPACE = -2
    }
}