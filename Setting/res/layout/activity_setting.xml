<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:splitMotionEvents="false">

    <LinearLayout
        android:id="@+id/activity_setting_ll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>

    <com.google.android.material.appbar.COUIDividerAppBarLayout
        android:id="@+id/appbar"
        android:background="?attr/couiColorBackgroundWithCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:orientation="vertical"
        app:elevation="@dimen/toolbar_elevation">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            style="@style/COUIToolBarInAppBarLayoutStyle"
            app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

    </com.google.android.material.appbar.COUIDividerAppBarLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
