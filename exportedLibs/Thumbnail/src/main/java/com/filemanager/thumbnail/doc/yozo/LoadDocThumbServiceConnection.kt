/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - LoadDocThumbServiceConnection.kt
 * Description:
 *     The connection to connect YoZo thumbnail service
 *
 * Version: 1.0
 * Date: 2024-07-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-07-29   1.0    Create this module
 *********************************************************************************/
package com.filemanager.thumbnail.doc.yozo

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.net.Uri
import android.os.DeadObjectException
import android.os.Handler
import android.os.HandlerThread
import android.os.IBinder
import android.os.Message
import android.os.SystemClock
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.doc.DocThumbnail
import com.filemanager.thumbnail.doc.IYoZoDocThumbnailCallback
import com.filemanager.thumbnail.utils.Log
import com.filemanager.thumbnail.utils.logInfo
import com.yozo.aidl.IOfficeAidlInterface
import com.yozo.aidl.IOfficeCallbackAidlInterface
import java.lang.reflect.InvocationTargetException

internal class LoadDocThumbServiceConnection(
    private val context: Context,
    handlerThread: HandlerThread,
    private val docThumbnail: DocThumbnail,
    @Volatile private var thumbnailCallback: IYoZoDocThumbnailCallback?,
    private val width: Int
) : IOfficeCallbackAidlInterface.Stub(), ServiceConnection, Handler.Callback {

    val docUri: Uri
        get() = docThumbnail.uri

    private val connectionHandler = Handler(handlerThread.looper, this)
    private val connectionTimeout = ThumbnailManager.YoZoDocConfigs.thumbnailTimeout
    private val rebindDelay = ThumbnailManager.YoZoDocConfigs.rebindDelay
    private val rebindMaxCount = ThumbnailManager.YoZoDocConfigs.rebindMaxCount

    @Volatile
    private var isBind = false

    @Volatile
    private var isCancel = false

    @Volatile
    private var isPendingRebind = false

    private var startTime = 0L
    private var rebindCount = 0
    private var serviceIntent: Intent? = null
    private var targetService: YoZoDocThumbService? = null

    fun doBind(service: YoZoDocThumbService): Boolean {
        if (isBind || isCancel) {
            Log.e(TAG, "doBind: already bind")
            return false
        }
        val bindIntent = service.getServiceIntent(context.packageName)
        docThumbnail.docFilePath?.let {
            bindIntent.putExtra(YoZoDocThumbService.EXTRA_FILE_PATH, it)
        }
        targetService = service
        return bindService("bindService", bindIntent)
    }

    private fun bindService(logTag: String, bindIntent: Intent): Boolean {
        startTime = 0L
        kotlin.runCatching {
            startTime = SystemClock.uptimeMillis()
            serviceIntent = bindIntent
            Log.d(TAG, "$logTag: ${bindIntent.action}")
            isBind = context.bindService(bindIntent, this, Context.BIND_AUTO_CREATE)
        }.onFailure {
            Log.e(TAG, "$logTag: ERROR! $it")
        }
        return isBind
    }

    override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
        Log.d(TAG, "onServiceConnected: $name, binder=$service")
        kotlin.runCatching {
            val thumbSDK = IOfficeAidlInterface.Stub.asInterface(service)
            context.grantUriPermission(
                ThumbnailManager.YoZoDocConfigs.yoZoPackage,
                docUri,
                Intent.FLAG_GRANT_READ_URI_PERMISSION
            )
            thumbSDK.registerCallback(this)
            exportOfficeThumbnailInternal(thumbSDK, docUri.toString())
        }.onFailure {
            Log.e(TAG, "onServiceConnected: ERROR! $it")
        }

        /**
         * 永中的缩略图服务可能出现异常长时间无响应的情况。
         * 故增加超时限制，耗时过长时直接取消当前的缩略图服务请求。
         */
        connectionHandler.sendEmptyMessageDelayed(MSG_CONNECTION_TIMEOUT, connectionTimeout)
    }

    private fun exportOfficeThumbnailInternal(thumbSDK: IOfficeAidlInterface, uri: String) {
        if (isExportOfficeThumbnailFirstPage(width, context)) {
            thumbSDK.exportOfficeThumbnailFirstPage(uri, width)
        } else {
            thumbSDK.exportOfficeThumbnail(uri)
        }
    }

    override fun onServiceDisconnected(name: ComponentName?) {
        connectionHandler.removeMessages(MSG_CONNECTION_TIMEOUT)
        if (isCancel) {
            return
        }
        Log.d(TAG, "onServiceDisconnected: $name")
        if (!tryRebind()) {
            callBindFailure(IllegalStateException("YoZo service disconnect"))
            finish()
        }
    }

    override fun onBindingDied(name: ComponentName?) {
        connectionHandler.removeMessages(MSG_CONNECTION_TIMEOUT)
        if (isCancel) {
            return
        }
        Log.e(TAG, "onBindingDied: $name")
        if (!tryRebind()) {
            callBindFailure(IllegalStateException("YoZo service died"))
            finish()
        }
    }

    /**
     * 单个永中缩略图服务每次bind仅能处理一个文档缩略图，需要unbind后重新bind才能处理下一个。
     * 但永中缩略图服务在onDestroy时会通过杀进程的方式释放资源，
     * 导致unbind后立刻重新bind容易出现[onBindingDied]等异常。
     * 故在连接异常中断的场景下，会尝试进行延迟重试bind。
     */
    @Synchronized
    private fun tryRebind(): Boolean {
        val bindIntent = serviceIntent ?: return false
        if (isPendingRebind) {
            return false
        }
        if (rebindCount >= rebindMaxCount) {
            return false
        }
        rebindCount++
        isPendingRebind = true
        Log.d(TAG, "tryRebind: delay, ${bindIntent.action}, count=$rebindCount")
        connectionHandler.sendEmptyMessageDelayed(MSG_TRY_REBIND, rebindDelay)
        return true
    }

    override fun onNullBinding(name: ComponentName?) {
        connectionHandler.removeMessages(MSG_CONNECTION_TIMEOUT)
        if (isCancel) {
            return
        }
        Log.e(TAG, "onNullBinding: $name")
        callBindFailure(IllegalStateException("YoZo service bind failed: $name"))
        finish()
    }

    /**
     * @see YoZoDocThumbService.cancel
     * @param afterCallback 内部标记是否在回调[thumbnailCallback]后执行取消
     */
    @JvmOverloads
    fun cancel(afterCallback: Boolean = false) {
        if (!isBind || isCancel) {
            return
        }
        synchronized(this) {
            if (!isBind || isCancel) {
                return
            }
            isCancel = true
            kotlin.runCatching {
                Log.d(TAG, "unbindService: ${serviceIntent?.action}")
                context.unbindService(this)
            }.onFailure {
                Log.e(TAG, "cancel: unbindService ERROR! $it")
            }
            // 回调[thumbnailCallback]后执行取消相当于清理操作，无需回调[onCancel]
            if (!afterCallback) {
                thumbnailCallback?.onCancel()
            }
            finish()
        }
    }

    @Synchronized
    private fun finish() {
        targetService?.onConnectionFinish(this)
        targetService = null
        thumbnailCallback?.onFinish()
        thumbnailCallback = null
        connectionHandler.removeCallbacksAndMessages(null)
        serviceIntent = null
    }

    /**
     * @param event 回调的事件ID，为[EVENT_LOAD_THUMBNAIL]时对应缩略图生成的回调
     * @param value1 在缩略图生成回调时，为结果状态码，0为成功，其它为异常
     * @param value2 在缩略图生成回调时，为缩略图的文件名
     * @param value3 在缩略图生成回调时，为缩略图的uri
     */
    override fun notifyActionEvent(
        event: Int,
        value1: String?,
        value2: String?,
        value3: String?
    ) {
        connectionHandler.removeMessages(MSG_CONNECTION_TIMEOUT)
        if (isCancel) {
            return
        }
        try {
            val useTime = SystemClock.uptimeMillis() - startTime
            val resultCode = value1?.toIntOrNull() ?: -1
            val uri = value3?.let { Uri.parse(it) }
            val valuesMsg =
                "event=$event, useTime=$useTime, resultCode=$resultCode, uri=${uri?.logInfo()}"
            Log.d(TAG, "notifyActionEvent: action=${serviceIntent?.action}, $valuesMsg")
            if (event != EVENT_LOAD_THUMBNAIL) {
                return
            }
            kotlin.runCatching {
                callbackThumbnail(resultCode, uri)
            }.onFailure {
                Log.e(TAG, "callbackThumbnail: ERROR! $it")
            }
        } finally {
            cancel(afterCallback = true)
        }
    }

    @Synchronized
    private fun callbackThumbnail(resultCode: Int, uri: Uri?) {
        if (resultCode == 0 && uri != null) {
            //成功
            thumbnailCallback?.onDataReady(uri)
        } else {
            thumbnailCallback?.onLoadFailed(
                IllegalStateException("$TAG: YoZo load fail, result=$resultCode, uri=${uri?.logInfo()}")
            )
        }
    }

    @Synchronized
    private fun callBindFailure(failure: Throwable) {
        thumbnailCallback?.onLoadFailed(failure)
    }

    override fun handleMessage(msg: Message): Boolean {
        if (isCancel) {
            Log.e(TAG, "handleMessage: cancelled")
            return false
        }
        Log.d(TAG, "handleMessage: what=${msg.what}")
        when (msg.what) {
            MSG_TRY_REBIND -> onTryRebind()
            MSG_CONNECTION_TIMEOUT -> onConnectionTimeout()
        }
        return true
    }

    private fun onTryRebind() {
        val bindIntent = serviceIntent ?: return
        bindService("rebind", bindIntent)
        isPendingRebind = false
    }

    private fun onConnectionTimeout() {
        Log.e(TAG, "onConnectionTimeout: action=${serviceIntent?.action}")
        connectionHandler.removeCallbacksAndMessages(null)
        thumbnailCallback?.onConnectionTimeout()
        cancel(afterCallback = true)
    }

    companion object {
        private const val TAG = "LoadDocThumbServiceConnection"
        private const val MSG_TRY_REBIND = 1001
        private const val MSG_CONNECTION_TIMEOUT = 1002
        private const val EVENT_LOAD_THUMBNAIL = 10000
        private const val MAX_BUILD_IN_SIZE = 200
        private var version: Int = -1

        private const val THUMB_SERVICE_VERSION = "office.thumbnail.service.version"
        private fun getServiceVersion(context: Context): Int {
            if (version == -1) {
                try {
                    val packageInfo = context.packageManager.getApplicationInfo(
                        ThumbnailManager.YoZoDocConfigs.yoZoPackage,
                        PackageManager.GET_META_DATA
                    )
                    version = packageInfo.metaData.getInt(THUMB_SERVICE_VERSION, 0).also {
                        Log.d(TAG, "getServiceVersion = $it")
                    }
                } catch (e: PackageManager.NameNotFoundException) {
                    Log.e(TAG, "getServiceVersion not found")
                }
            }
            return version
        }

        private fun isSupportCustomVersion(context: Context): Boolean {
            return getServiceVersion(context) > 0
        }

        fun isExportOfficeThumbnailFirstPage(width: Int, context: Context): Boolean {
            return (width > MAX_BUILD_IN_SIZE) && isSupportCustomVersion(context)
        }
    }
}