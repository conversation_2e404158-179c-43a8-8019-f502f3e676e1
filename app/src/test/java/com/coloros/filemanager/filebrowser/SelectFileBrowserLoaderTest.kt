/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SelectFileBrowserLoaderTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/16
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/16       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.filebrowser

import android.content.Context
import android.os.Environment
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.selectdir.filebrowser.SelectFileBrowserLoader
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.io.File

class SelectFileBrowserLoaderTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
        mockkStatic(Environment::class)
        every { Environment.getExternalStorageDirectory().absolutePath }.returns("test")
    }

    @After
    fun tearDown() {
        unmockkStatic(Environment::class)
    }

    @Test
    fun testCreateFromPath() {
        val selectFileBrowserLoader = SelectFileBrowserLoader(context, PATH)
        val volume = "volume"
        val parentPath = "/sdcard/0/"
        val fileWrapper = PathFileWrapper(parentPath.plus(File.separator).plus(PATH))
        val fileWrapperResult = arrayListOf(fileWrapper)
        fileWrapperResult.forEachIndexed { index, pathFileWrapper ->
            Assert.assertEquals(
                pathFileWrapper.mData,
                selectFileBrowserLoader.createFromPath(volume, parentPath, PATH)[index].mData
            )
        }
    }

    @Test
    fun testGetVolume() {
        val selectFileBrowserLoader = SelectFileBrowserLoader(context, PATH)
        Assert.assertNull(selectFileBrowserLoader.getVolume())
    }

    @Test
    fun testGetPath() {
        val selectFileBrowserLoader = SelectFileBrowserLoader(context, PATH)
        val pathList = arrayOf(PATH)
        pathList.forEachIndexed { index, s ->
            Assert.assertEquals(s, selectFileBrowserLoader.getPath()[index])
        }
        selectFileBrowserLoader.setPath(SET_PATH)
        val setPathList = arrayOf(SET_PATH)
        setPathList.forEachIndexed { index, s ->
            Assert.assertEquals(s, selectFileBrowserLoader.getPath()[index])
        }
    }

    @Test
    fun testGetFilterList() {
        val selectFileBrowserLoader = SelectFileBrowserLoader(context, PATH)
        Assert.assertNull(selectFileBrowserLoader.getFilterList())
    }

    @Test
    fun testGetItemKey() {
        val selectFileBrowserLoader = SelectFileBrowserLoader(context, PATH)
        val fileBean = BaseFileBean()
        val nullData: String? = null
        fileBean.mData = nullData
        Assert.assertNull(selectFileBrowserLoader.getItemKey(fileBean))
        val emptyData = ""
        fileBean.mData = emptyData
        Assert.assertNull(selectFileBrowserLoader.getItemKey(fileBean))
        fileBean.mData = FILE_NAME
        val fileNameHashCode = FILE_NAME.hashCode()
        Assert.assertEquals(fileNameHashCode, selectFileBrowserLoader.getItemKey(fileBean))
    }

    companion object {
        private const val PATH = "/sdcard/0/path"
        private const val SET_PATH = "/sdcard/0/setPath"
        private const val FILE_NAME = "test"
    }
}