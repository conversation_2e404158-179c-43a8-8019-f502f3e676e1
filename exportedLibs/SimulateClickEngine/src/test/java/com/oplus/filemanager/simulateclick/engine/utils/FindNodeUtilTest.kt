/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - FindNodeUtilTest.kt
 * Description:
 * The test cases for FindNodeUtil
 *
 * Version: 1.0
 * Date: 2024-06-07
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-06-07   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.utils

import android.view.accessibility.AccessibilityNodeInfo
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.Assert
import org.junit.Test

class FindNodeUtilTest : Assert() {

    @Test
    fun `should call node method when findNodesByText if directly`() {
        //Given
        val widgetText = "testText"
        val subNode = mockk<AccessibilityNodeInfo>()
        val node = mockk<AccessibilityNodeInfo> {
            every { findAccessibilityNodeInfosByText(widgetText) } returns listOf(subNode)
        }

        // When
        val results = FindNodeUtil.findNodesByText(node, widgetText, true)

        // Then
        assertEquals(listOf(subNode), results)
        verify { node.findAccessibilityNodeInfosByText(widgetText) }
    }

    @Test
    fun `should call recursively when findNodesByText if not directly`() {
        // Given
        val widgetText = "testText"
        val subNode1 = mockk<AccessibilityNodeInfo> {
            every { childCount } returns 0
            every { text } returns widgetText
            every { contentDescription } returns ""
        }
        val subNode2 = mockk<AccessibilityNodeInfo> {
            every { childCount } returns 0
            every { text } returns ""
            every { contentDescription } returns widgetText
        }
        val node = mockk<AccessibilityNodeInfo> {
            every { findAccessibilityNodeInfosByText(widgetText) } returns listOf(subNode1)
            every { text } returns ""
            every { contentDescription } returns ""
            every { childCount } returns 2
            every { getChild(0) } returns subNode2
            every { getChild(1) } returns subNode1
        }

        // When
        val results = FindNodeUtil.findNodesByText(node, widgetText, false)

        // Then
        assertEquals(listOf(subNode2, subNode1), results)
        verify(inverse = true) { node.findAccessibilityNodeInfosByText(any()) }
    }

    @Test
    fun `should call recursively when findNodesById if directly method return null`() {
        // Given
        val targetId = "test:id/target"
        val subNode = mockk<AccessibilityNodeInfo> {
            every { childCount } returns 0
            every { viewIdResourceName } returns targetId
        }
        val node = mockk<AccessibilityNodeInfo> {
            every { findAccessibilityNodeInfosByViewId(targetId) } returns emptyList()
            every { viewIdResourceName } returns "test:id/base"
            every { childCount } returns 1
            every { getChild(0) } returns subNode
        }

        // When
        val results = FindNodeUtil.findNodesById(node, targetId)

        // Then
        assertEquals(listOf(subNode), results)
    }
}