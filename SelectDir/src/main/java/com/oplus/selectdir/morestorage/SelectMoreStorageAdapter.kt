/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.adapter
 * * Version     : 1.0
 * * Date        : 2020/5/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir.morestorage

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.TextViewSnippet
import com.oplus.selectdir.R
import java.lang.ref.WeakReference
import java.util.*

private const val TAG = "MoreStorageAdapter"

class SelectMoreStorageAdapter : BaseSelectionRecycleAdapter<SelectMoreStorageAdapter.ViewHolder, SelectDirRootFileBean>, LifecycleObserver {

    private val mUiHandler: Handler
    private var mOnRecyclerItemClickListener: OnRecyclerItemClickListener? = null
    private var mThreadManager: ThreadManager

    constructor(content: Context, lifecycle: Lifecycle) : super(content) {
        mUiHandler = Handler(Looper.getMainLooper())
        mThreadManager = ThreadManager(lifecycle)
        lifecycle.addObserver(this)
    }

    fun setData(data: ArrayList<SelectDirRootFileBean>) {
        mFiles = data
        notifyDataSetChanged()
    }


    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        mUiHandler?.removeCallbacksAndMessages(null)
    }

    fun setOnRecyclerItemClickListener(onRecyclerItemClickListener: OnRecyclerItemClickListener) {
        mOnRecyclerItemClickListener = onRecyclerItemClickListener
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val v = LayoutInflater.from(parent.context).inflate(R.layout.selectdir_more_storage_select_item, parent, false)
        return ViewHolder(v)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val file = mFiles?.get(position)
        holder.itemView.setOnClickListener {
            mOnRecyclerItemClickListener?.onItemClick(holder.itemView, holder.layoutPosition)
        }

        holder.itemView.setOnLongClickListener {
            mOnRecyclerItemClickListener?.onItemLongClick(holder.itemView, holder.layoutPosition)
            true
        }
        doData(file, holder, position)
    }

    private fun doData(fileSelectDir: SelectDirRootFileBean?, holder: ViewHolder, position: Int) {
        holder.mTitle?.setVisibility(View.VISIBLE)
        if ((fileSelectDir == null)) {
            return
        }
        holder.mTitle?.setText(fileSelectDir.mDisplayName)
        holder.mImg?.setImageDrawable(MyApplication.sAppContext.resources.getDrawable(fileSelectDir.mImageId))
        setDetail(holder.mDetail, fileSelectDir.mData)
        if (position < mFiles.size - 1) {
            holder.dividerLIne?.visibility = View.VISIBLE
        } else {
            holder.dividerLIne?.visibility = View.INVISIBLE
        }
    }

    class MoreStorageRunnable : FileRunnable {
        constructor(weakTextView: WeakReference<TextView?>, uiHandler: Handler, path: String)
                : super(Runnable {
            val formatStorageDetail = Utils.formatStorageDetail(MyApplication.sAppContext, path)
            uiHandler.post {
                if (path != null) {
                    val textView = weakTextView.get()
                    textView?.setText(formatStorageDetail)
                }
            }
        }, TAG)
    }

    private fun setDetail(mDetail: TextView?, mPath: String) {
        mThreadManager.execute(MoreStorageRunnable(WeakReference(mDetail), mUiHandler, mPath))
    }

    class ViewHolder : RecyclerView.ViewHolder {
        var mImg: ImageView? = null
        var mJumpImg: ImageView? = null
        var mTitle: TextViewSnippet? = null
        var mDetail: TextView? = null
        var dividerLIne: View? = null

        constructor(convertView: View) : super(convertView) {
            mImg = convertView.findViewById(R.id.file_list_item_icon)
            mJumpImg = convertView.findViewById(R.id.jump_mark)
            mTitle = convertView.findViewById(R.id.file_list_item_title)
            mDetail = convertView.findViewById(R.id.mark_file_list_item_detail)
            dividerLIne = convertView.findViewById(R.id.divider_line)
        }
    }

    override fun getItemKey(item: SelectDirRootFileBean, position: Int): Int? {
        return null
    }

    override fun getItemKeyByPosition(position: Int): Int? {
        return null
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        setChoiceModeAnimFlag(flag)
    }
}