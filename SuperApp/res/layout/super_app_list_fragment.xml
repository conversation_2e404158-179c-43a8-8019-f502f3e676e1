<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:splitMotionEvents="false">

    <com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller
        android:id="@+id/fastScroller"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:common_track_marginBottom="@dimen/ftp_text_margin_bottom"
        app:common_track_marginEnd="@dimen/base_album_fastscroller_margin_end"
        app:common_track_marginTop="@dimen/base_album_recyclerview_padding_top">

        <com.filemanager.common.view.FileManagerRecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller>

    <ViewStub
        android:id="@+id/common_permission_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/permission_common_view_layout" />

</RelativeLayout>