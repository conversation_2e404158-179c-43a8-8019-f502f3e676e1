plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace "com.filemanager.setting"

    sourceSets {
        main {
            res.srcDirs += ['res']
        }
    }
}

dependencies {
    compileOnly(libs.oplus.addon.sdk) { artifact { type = "aar" } }

    implementation libs.androidx.appcompat
    implementation libs.google.material

    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.toolbar
    implementation libs.oplus.appcompat.poplist
    implementation libs.oplus.appcompat.preference
    implementation libs.oplus.appcompat.recyclerview
    implementation libs.oplus.appcompat.scrollbar
    implementation libs.oplus.appcompat.dialog
    implementation libs.oplus.appcompat.card
    implementation libs.oplus.appcompat.statement
    implementation libs.oplus.appcompat.component
    implementation libs.oplus.appcompat.panel
    implementation libs.oplus.appcompat.snackbar
    implementation libs.koin.android

    implementation project(':Common')
    implementation project(':Provider')
}