<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rlist_item_expand_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal">

    <TextView
        android:id="@+id/rlist_item_expand_desp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dimen_16dp"
        android:text="@string/expand_all_items"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textSize="@dimen/recent_expand_item_text_size" />

    <com.coui.appcompat.rotateview.COUIRotateView
        android:id="@+id/rlist_item_expand_icon"
        android:layout_width="@dimen/recent_icon_size"
        android:layout_height="@dimen/recent_icon_size"
        android:layout_centerVertical="true"
        android:layout_toEndOf="@+id/rlist_item_expand_desp"
        android:clickable="false"
        app:supportRotateType="couiRotateAloneX"
        android:focusable="false"
        android:src="@drawable/line_arrow" />


</RelativeLayout>