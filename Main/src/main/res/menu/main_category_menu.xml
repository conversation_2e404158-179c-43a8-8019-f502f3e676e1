<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <item
        android:id="@+id/no_action"
        android:enabled="false"
        android:title=""
        android:visible="true"
        app:showAsAction="always|collapseActionView" />

    <item
        android:id="@+id/action_search"
        android:icon="@drawable/ic_search_view"
        android:title="@string/search_item"
        android:visible="false"
        app:showAsAction="always|collapseActionView" />

    <item
        android:id="@+id/action_edit"
        android:enabled="true"
        android:title="@string/menu_recent_file_edit"
        app:showAsAction="never" />

    <item
        android:id="@+id/actionbar_owork"
        android:enabled="true"
        android:title=""
        app:showAsAction="never" />

    <item
        android:id="@+id/action_setting"
        android:enabled="true"
        android:title="@string/set_button_text"
        app:showAsAction="never" />

</menu>