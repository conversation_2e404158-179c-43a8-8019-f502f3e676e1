/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - UiSelectorTest.kt
 * Description:
 * The test cases for UiSelector
 *
 * Version: 1.0
 * Date: 2024-06-11
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-06-11   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine

import android.view.accessibility.AccessibilityNodeInfo
import com.oplus.filemanager.simulateclick.engine.utils.FindNodeUtil
import com.oplus.filemanager.simulateclick.service.AccessibilityServiceBridge
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class UiSelectorTest : Assert() {

    private lateinit var serviceBridge: AccessibilityServiceBridge
    private lateinit var uiSelector: UiSelector

    @Before
    fun setUp() {
        serviceBridge = mockk(relaxed = true)
        uiSelector = UiSelector(serviceBridge)
    }

    @Test
    fun `should directly get id nodes when findNodesByIds if has reachable id and ids list`() {
        // SetUp
        mockkStatic(FindNodeUtil::class)

        // Given
        val id1 = "com.test:id/i1"
        val id2 = "com.test:id/i2"
        val id3 = "com.test:id/i3"
        val action = Action(
            widgetID = id1,
            widgetIDList = listOf(id2, id3)
        )
        val rootNode = mockk<AccessibilityNodeInfo>()
        val node1 = mockk<AccessibilityNodeInfo>()
        every { FindNodeUtil.findNodesById(rootNode, id1) } returns listOf(node1)
        every { FindNodeUtil.findNodesById(rootNode, not(id1)) } returns emptyList()
        val callback = mockk<(String) -> Unit>(relaxed = true)

        // When
        val result = uiSelector.findNodesByIds(rootNode, action) { callback(it) }

        // Then
        assertEquals(listOf(node1), result)
        verify(inverse = true) { callback(any()) }

        // TearDown
        unmockkStatic(FindNodeUtil::class)
    }

    @Test
    fun `should get list id nodes when findNodesByIds if no reachable id but has ids list`() {
        // SetUp
        mockkStatic(FindNodeUtil::class)

        // Given
        val id1 = "com.test:id/i1"
        val id2 = "com.test:id/i2"
        val id3 = "com.test:id/i3"
        val action = Action(
            widgetID = id1,
            widgetIDList = listOf(id2, id3)
        )
        val rootNode = mockk<AccessibilityNodeInfo>()
        val node1 = mockk<AccessibilityNodeInfo>()
        every { FindNodeUtil.findNodesById(rootNode, id3) } returns listOf(node1)
        every { FindNodeUtil.findNodesById(rootNode, not(id3)) } returns emptyList()
        val callback = mockk<(String) -> Unit>(relaxed = true)

        // When
        val result = uiSelector.findNodesByIds(rootNode, action) { callback(it) }

        // Then
        assertEquals(listOf(node1), result)
        verify {
            callback(id2)
            callback(id3)
        }

        // TearDown
        unmockkStatic(FindNodeUtil::class)
    }

    @Test
    fun `should get list id nodes when findNodesByIds if no id but has ids list`() {
        // SetUp
        mockkStatic(FindNodeUtil::class)

        // Given
        val id2 = "com.test:id/i2"
        val id3 = "com.test:id/i3"
        val action = Action(
            widgetID = null,
            widgetIDList = listOf(id2, id3)
        )
        val rootNode = mockk<AccessibilityNodeInfo>()
        val node1 = mockk<AccessibilityNodeInfo>()
        every { FindNodeUtil.findNodesById(rootNode, id3) } returns listOf(node1)
        every { FindNodeUtil.findNodesById(rootNode, not(id3)) } returns emptyList()
        val callback = mockk<(String) -> Unit>(relaxed = true)

        // When
        val result = uiSelector.findNodesByIds(rootNode, action) { callback(it) }

        // Then
        assertEquals(listOf(node1), result)
        verify {
            callback(id2)
            callback(id3)
        }

        // TearDown
        unmockkStatic(FindNodeUtil::class)
    }
}