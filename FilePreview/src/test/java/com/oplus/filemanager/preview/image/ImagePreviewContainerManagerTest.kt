package com.oplus.filemanager.preview.image

import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.test.platform.app.InstrumentationRegistry
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.utils.PreviewScrollAreaHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite
import com.oplus.filemanager.preview.widget.PreviewOperationsBar
import com.oplus.filemanager.preview.widget.PreviewScrollView
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.clearAllMocks
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * ImagePreviewContainerManager的单元测试类
 * 用于测试图片预览容器管理器的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class ImagePreviewContainerManagerTest {

    // 定义测试所需的mock对象
    private lateinit var manager: ImagePreviewContainerManager
    private lateinit var mockRootLayout: View
    private lateinit var mockNameContainer: TextViewSnippet
    private lateinit var mockImageView: AppCompatImageView
    private lateinit var mockDefaultContainer: PreviewFileInfoSuite
    private lateinit var mockImageContainer: View
    private lateinit var mockLoadingLayout: View
    private lateinit var mockDateTv: AppCompatTextView
    private lateinit var mockSizeTv: AppCompatTextView
    private lateinit var mockScrollAreaHelper: PreviewScrollAreaHelper
    private lateinit var mockScrollView: PreviewScrollView
    private lateinit var mockOperationsBar: PreviewOperationsBar

    /**
     * 测试前的初始化方法
     * 创建所有mock对象并设置预期行为
     */
    @Before
    fun setUp() {
        // 创建所有mock对象
        mockRootLayout = mockk(relaxed = true)
        mockNameContainer = mockk(relaxed = true)
        mockImageView = mockk(relaxed = true)
        mockDefaultContainer = mockk(relaxed = true)
        mockImageContainer = mockk(relaxed = true)
        mockLoadingLayout = mockk(relaxed = true)
        mockDateTv = mockk(relaxed = true)
        mockSizeTv = mockk(relaxed = true)
        mockScrollAreaHelper = mockk(relaxed = true)
        mockScrollView = mockk(relaxed = true)
        mockOperationsBar = mockk(relaxed = true)

        // 设置mock对象的预期行为
        every { mockRootLayout.findViewById<TextViewSnippet>(R.id.preview_remote_title) } returns mockNameContainer
        every { mockRootLayout.findViewById<AppCompatImageView>(R.id.preview_image_view) } returns mockImageView
        every { mockRootLayout.findViewById<PreviewFileInfoSuite>(R.id.preview_image_def_info) } returns mockDefaultContainer
        every { mockRootLayout.findViewById<View>(R.id.preview_image_container) } returns mockImageContainer
        every { mockRootLayout.findViewById<View>(R.id.loading_layout) } returns mockLoadingLayout
        every { mockRootLayout.findViewById<AppCompatTextView>(R.id.preview_remote_time_info) } returns mockDateTv
        every { mockRootLayout.findViewById<AppCompatTextView>(R.id.preview_remote_size_info) } returns mockSizeTv
        every { mockRootLayout.findViewById<PreviewScrollView>(R.id.preview_audio_scroll_area) } returns mockScrollView
        every { mockRootLayout.findViewById<PreviewOperationsBar>(R.id.preview_operations_bar) } returns mockOperationsBar

        // 创建待测试的manager对象
        manager = ImagePreviewContainerManager(mockRootLayout)
    }

    /**
     * 测试后的清理方法
     * 清除所有mock对象的状态
     */
    @After
    fun tearDown() {
        clearAllMocks()
    }

    /**
     * 测试showAsLoading方法
     * 验证在加载状态下各UI元素的可见性设置是否正确
     */
    @Test
    fun testShowAsLoading() {
        manager.showAsLoading()

        // 验证各UI元素的可见性设置是否符合预期
        verify(exactly = 1) {
            mockDefaultContainer.visibility = View.GONE
            mockNameContainer.visibility = View.VISIBLE
            mockImageContainer.visibility = View.INVISIBLE
            mockLoadingLayout.visibility = View.VISIBLE
        }
    }

    /**
     * 测试showAsFileContainer方法(默认容器情况)
     * 验证在默认容器状态下各UI元素的可见性设置是否正确
     */
    @Test
    fun testShowAsFileContainer_Default() {
        manager.showAsFileContainer(true)

        // 验证各UI元素的可见性设置是否符合预期
        verify(exactly = 1) {
            mockLoadingLayout.visibility = View.GONE
            mockNameContainer.visibility = View.INVISIBLE
            mockImageContainer.visibility = View.INVISIBLE
            mockDefaultContainer.visibility = View.VISIBLE
            mockDefaultContainer.setFilePathVisible(false)
        }
    }

    /**
     * 测试showAsFileContainer方法(非默认容器情况)
     * 验证在非默认容器状态下各UI元素的可见性设置是否正确
     */
    @Test
    fun testShowAsFileContainer_NotDefault() {
        // 模拟资源对象
        val mockResources = mockk<android.content.res.Resources>()
        every { mockDefaultContainer.resources } returns mockResources
        every { mockResources.getDimensionPixelOffset(R.dimen.preview_remote_scroll_layout_min_height) } returns 100

        manager.showAsFileContainer(false)

        // 验证各UI元素的可见性设置是否符合预期
        verify(exactly = 1) {
            mockLoadingLayout.visibility = View.GONE
            mockDefaultContainer.visibility = View.GONE
            mockNameContainer.visibility = View.VISIBLE
            mockImageContainer.visibility = View.VISIBLE
        }
    }

    /**
     * 测试onUpdateUIWhenConfigChange方法
     * 验证在配置变更时UI更新的行为
     */
    @Test
    fun testOnUpdateUIWhenConfigChange() {
        val mockConfigList = mockk<MutableCollection<IUIConfig>>()

        manager.onUpdateUIWhenConfigChange(mockConfigList)
    }
}