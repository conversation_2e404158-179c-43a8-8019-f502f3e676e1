/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FeedbackApi.kt
 ** Description:  FeedbackApi
 ** Version: 1.0
 ** Date: 2021/5/7
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.feedback

import android.app.Activity
import android.content.pm.ActivityInfo
import com.customer.feedback.sdk.FeedbackHelper
import com.customer.feedback.sdk.model.RequestData
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.utils.IdentifierManager.clearSdk
import com.filemanager.common.utils.IdentifierManager.getVAID
import com.filemanager.common.utils.IdentifierManager.initSdk
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.interfaze.feedback.IFeedback
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object FeedbackApi : IFeedback {
    private const val TAG = "FeedBackApi"

    override fun launchFeedBack(activity: Activity) {
        val fbHelper = FeedbackHelper.getInstance(activity)
        runCatching {
            FeedbackHelper.setNetworkUserAgree(true)
            FeedbackHelper.setRequestMadeListener(object : FeedbackHelper.RequestMadeCallback {
                override fun onRequestMade(list: List<RequestData>) {
                    list.forEach {
                        when (it) {
                            is RequestData.OpenId -> {
                                CollectPrivacyUtils.collectOpenId(it.content)
                                CollectPrivacyUtils.collectIMEI(it.content)
                            }
                            is RequestData.Brand -> CollectPrivacyUtils.collectDeviceBrand(it.content)
                            is RequestData.Model -> CollectPrivacyUtils.collectDeviceModel(it.content)
                            is RequestData.Os -> CollectPrivacyUtils.collectOsVersion(it.content)
                            is RequestData.Contact -> {
                                if ("${it.content}".contains("@")) {
                                    CollectPrivacyUtils.collectEmailAddress(it.content)
                                } else {
                                    CollectPrivacyUtils.collectPhoneNumber(it.content)
                                }
                            }
                            is RequestData.Log -> CollectPrivacyUtils.collectErrorLogReport(it.content)
                            is RequestData.Statistics -> CollectPrivacyUtils.collectBuriedPointInfo(it.content)
                            is RequestData.Feedback -> CollectPrivacyUtils.collectFeedbackContentAttachment(it.content)
                            else -> Log.e(TAG, "onRequestMade: $it")
                        }
                    }
                }
            })
			if (ModelUtils.isTablet()) {
                FeedbackHelper.getInstance(activity.applicationContext)?.setCommonOrientationType(
                    ActivityInfo.SCREEN_ORIENTATION_USER
                )
            }
            FeedbackHelper.setDataSavedCountry(FeedbackHelper.FbAreaCode.CN)
            MainScope().launch(Dispatchers.Default) {
                initSdk(appContext)
                val dUid = getVAID(appContext)
                clearSdk(appContext)
                withContext(Dispatchers.Main) {
                    dUid?.let { FeedbackHelper.setId(it) }
                    fbHelper?.openFeedback(activity)
                }
            }
        }.onFailure {
            Log.e(TAG, "launchFeedBack: ${it.message}")
        }
        StatisticsUtils.onCommon(activity, StatisticsUtils.IN_HELP_AND_FEEDBACK)
    }
}