/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - OnGestureExtendListener.java
 * Description: Extend class of SimpleOnGestureListener
 * Version: 1.0
 * Date : 2020/05/14
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/05/14    1.0     create
 ****************************************************************/
package com.oplus.dropdrag.recycleview

import android.view.GestureDetector.SimpleOnGestureListener
import android.view.MotionEvent

internal open class ExtendGestureListener : SimpleOnGestureListener() {
    open fun onDragStart(e: MotionEvent): Boolean {
        return false
    }

    open fun onCancel(e: MotionEvent): Boolean {
        return false
    }

    open fun onUp(e: MotionEvent): Boolean {
        return false
    }
}