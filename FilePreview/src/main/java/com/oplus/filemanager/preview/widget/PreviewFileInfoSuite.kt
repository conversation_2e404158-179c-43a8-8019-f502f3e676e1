/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - PreviewFileInfoSuite.kt
 * Description:
 *     The widget to show common file info.
 *
 * Version: 1.0
 * Date: 2024-10-18
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-10-18   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.widget

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.IWidgetDefFileIntroduce

internal class PreviewFileInfoSuite @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : ConstraintLayout(context, attrs), IWidgetDefFileIntroduce {

    val filePathView: PreviewFilePathItem

    override val fileIconView: AppCompatImageView
        get() = introduceView.fileIconView

    private val introduceView: PreviewFileIntroduceView

    init {
        LayoutInflater.from(context).inflate(R.layout.widget_preview_file_info_suite, this)
        introduceView = findViewById(R.id.preview_file_introduce_view)
        filePathView = findViewById(R.id.preview_file_path)
    }

    override fun setFileIcon(@DrawableRes iconRes: Int) {
        introduceView.setFileIcon(iconRes)
    }

    override fun setFileIcon(drawable: Drawable?) {
        introduceView.setFileIcon(drawable)
    }

    override fun setFileIcon(bitmap: Bitmap?) {
        introduceView.setFileIcon(bitmap)
    }

    override fun setFileName(@StringRes nameRes: Int) {
        introduceView.setFileName(nameRes)
    }

    override fun setFileName(name: String?) {
        introduceView.setFileName(name)
    }

    override fun setFileNameVisible(visible: Boolean) {
        introduceView.setFileNameVisible(visible)
    }

    override fun setFileMessage(@StringRes msgRes: Int) {
        introduceView.setFileMessage(msgRes)
    }

    override fun setFileMessage(msg: String?) {
        introduceView.setFileMessage(msg)
    }

    override fun setFilePathVisible(visible: Boolean) {
        if (visible) {
            filePathView.visibility = View.VISIBLE
        } else {
            filePathView.visibility = View.GONE
        }
    }
}