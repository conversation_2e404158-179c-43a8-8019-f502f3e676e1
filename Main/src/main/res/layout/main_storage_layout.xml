<?xml version="1.0" encoding="utf-8"?>
<com.oplus.filemanager.main.view.LimitSlideOutHorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_storage_scroll_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true"
    android:overScrollMode="never">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/main_storage_layout"
        android:layout_width="wrap_content"
        android:layout_height="match_parent">

        <include
            android:id="@+id/phone_storage"
            layout="@layout/phone_storage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:minHeight="@dimen/main_storage_height"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/otg_storage"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintHorizontal_weight="194"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view_placeholder"
            android:layout_width="@dimen/main_phone_storage_width"
            android:layout_height="@dimen/main_storage_height"
            android:minHeight="@dimen/main_storage_height"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/phone_storage"
            app:layout_constraintTop_toTopOf="parent" />

        <include
            android:id="@+id/otg_storage"
            layout="@layout/otg_card_external_storage"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dimen_8dp"
            android:minHeight="@dimen/main_storage_height"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/sd_card_storage"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_weight="185"
            app:layout_constraintStart_toEndOf="@+id/phone_storage"
            app:layout_constraintTop_toTopOf="@+id/phone_storage"
            app:layout_constraintWidth_min="@dimen/main_otg_and_sd_storage_width"
            app:layout_goneMarginEnd="@dimen/dimen_16dp" />

        <include
            android:id="@+id/sd_card_storage"
            layout="@layout/sd_card_external_storage"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dimen_8dp"
            android:minHeight="@dimen/main_storage_height"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/dfm_storage"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_weight="185"
            app:layout_constraintStart_toEndOf="@+id/otg_storage"
            app:layout_constraintTop_toTopOf="@+id/phone_storage"
            app:layout_constraintWidth_min="@dimen/main_otg_and_sd_storage_width"
            app:layout_goneMarginEnd="@dimen/dimen_16dp" />

        <include
            android:id="@+id/dfm_storage"
            layout="@layout/dfm_external_storage"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dimen_8dp"
            android:minHeight="@dimen/main_storage_height"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/remote_device"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_weight="185"
            app:layout_constraintStart_toEndOf="@+id/sd_card_storage"
            app:layout_constraintTop_toTopOf="@+id/phone_storage"
            app:layout_constraintWidth_min="@dimen/main_otg_and_sd_storage_width"
            app:layout_goneMarginEnd="@dimen/dimen_16dp" />

        <View
            android:id="@+id/dfm_anchor_view"
            android:layout_width="1dp"
            android:layout_height="0dp"
            android:layout_marginStart="66dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@id/dfm_storage"
            app:layout_constraintTop_toTopOf="parent" />

        <include
            android:id="@+id/remote_device"
            layout="@layout/remote_device_list_wrapper"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_8dp"
            android:minHeight="@dimen/main_storage_height"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/cloud_disk_storage"
            app:layout_constraintStart_toEndOf="@+id/dfm_storage"
            app:layout_constraintTop_toTopOf="@+id/phone_storage"
            app:layout_goneMarginEnd="@dimen/dimen_16dp" />

        <include
            android:id="@+id/cloud_disk_storage"
            layout="@layout/cloud_disk_external_storage"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/dimen_8dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:minHeight="@dimen/dimen_72dp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/private_safe"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_weight="126"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@+id/remote_device"
            app:layout_constraintWidth_min="@dimen/main_cloud_disk_storage_min_width"
            app:layout_constraintEnd_toEndOf="parent"
            tools:visibility="visible" />

        <include
            android:id="@+id/private_safe"
            layout="@layout/private_safe_external"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:minHeight="@dimen/dimen_72dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_weight="126"
            app:layout_constraintStart_toStartOf="@+id/cloud_disk_storage"
            app:layout_constraintTop_toBottomOf="@+id/cloud_disk_storage"
            app:layout_constraintWidth_min="@dimen/main_cloud_disk_storage_min_width"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dimen_8dp"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.oplus.filemanager.main.view.LimitSlideOutHorizontalScrollView>