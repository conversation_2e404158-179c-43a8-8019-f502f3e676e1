/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : TaskExecutor
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/1 10:30
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/1       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.utils.taskexecutor

import com.oplus.fileservice.utils.SerialExecutor
import java.util.concurrent.Executor

interface TaskExecutor {
    fun postToMainThread(runnable: Runnable)

    fun getMainThreadExecutor(): Executor

    fun executeOnBackgroundThread(runnable: Runnable)

    fun getBackgroundExecutor(): SerialExecutor
}