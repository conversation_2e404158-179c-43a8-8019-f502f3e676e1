/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - GestureResultAdapter.kt
 * Description:
 *     The adapter to simply the usage of AccessibilityService.GestureResultCallback
 *
 * Version: 1.0
 * Date: 2024-05-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-05-29   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.accessibility

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription

internal open class GestureResultAdapter : AccessibilityService.GestureResultCallback() {

    override fun onCancelled(gestureDescription: GestureDescription?) {
        super.onCancelled(gestureDescription)
    }

    override fun onCompleted(gestureDescription: GestureDescription?) {
        super.onCompleted(gestureDescription)
    }
}