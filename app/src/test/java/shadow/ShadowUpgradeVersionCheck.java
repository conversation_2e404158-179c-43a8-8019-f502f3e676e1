/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowUpgradeVersionCheck.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/8/28
 ** Author: JiaBo Lan
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/8/28      1.0     create
 ****************************************************************/
package shadow;

import com.oplus.ota.upgrade.PackageInfo;
import com.oplus.ota.upgrade.UpgradeVersionCheck;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(UpgradeVersionCheck.class)
public class ShadowUpgradeVersionCheck {
    @Implementation
    public void __constructor__(final boolean isEncryptSupportOnly) {
    }

    @Implementation
    public PackageInfo scanningPackage(String path) {
        return new PackageInfo();
    }

}
