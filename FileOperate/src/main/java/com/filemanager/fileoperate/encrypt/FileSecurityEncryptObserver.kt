/***********************************************************
 ** Copyright (C), 2008-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileSecurityEncryptObserver.kt
 ** Description: File upload to cloud
 ** Version: 1.0
 ** Date: 2023/8/2
 ** Author: hank.zhou(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 *    hank.zhou  2023/8/2     1.0         security observer
 ****************************************************************/
package com.filemanager.fileoperate.encrypt

import android.content.Context
import android.content.DialogInterface
import android.view.ContextThemeWrapper
import android.view.KeyEvent
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUISecurityAlertDialogBuilder
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NewFunctionSwitch
import com.filemanager.fileoperate.R
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.SHOW_PROGRESS
import com.filemanager.fileoperate.base.UPDATE_PROGRESS

const val SHOW_CONFIRM_DIALOG = -4

open class FileSecurityEncryptObserver(context: ContextThemeWrapper) : FileEncryptObserver(context) {

    private var progressDialog: DelayLoadingMinShowTimeDialog? = null
    private var confirmDialog: AlertDialog? = null

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        if (!NewFunctionSwitch.SUPPORT_SECURITY_ENCRYPT) {
            return super.onChanged(context, result)
        }
        when (result.first) {
            ERROR_ENCRYPT_PERMISSION -> {
                CustomToast.showLong(com.filemanager.common.R.string.unable_to_encrypted)
                onActionDone(false)
                return true
            }
            ACTION_DONE -> encryptDone()
            ACTION_FAILED -> {
                onActionDone(false)
                val failedCount = if (result.second is Int) result.second as Int else -1
                if (failedCount > 0) {
                    encryptFail(failedCount)
                } else {
                    progressDialog?.forceDismiss()
                }
            }
            SHOW_CONFIRM_DIALOG -> showConfirmDialog(context, result.second as DialogBean)
            SHOW_PROGRESS -> {
                showProgressDialog(context, result.second as Int)
                return true
            }
            UPDATE_PROGRESS -> {
                updateProgressDialog(result.second as Int)
                return true
            }
        }
        return false
    }

    private fun showConfirmDialog(context: Context, bean: DialogBean) {
        COUISecurityAlertDialogBuilder(context).apply {
            setTitle(context.resources.getQuantityString(com.filemanager.common.R.plurals.file_encrypt_dialog_title, bean.fileCount, bean.fileCount))
            setMessage(com.filemanager.common.R.string.file_encrypt_dialog_describe)
            setChecked(false)
            setHasCheckBox(true)
            setCheckBoxString(com.filemanager.common.R.string.file_encrypt_checkbox_describe)
            setShowStatementText(false)
            setNegativeString(com.filemanager.common.R.string.dialog_cancel)
            setPositiveString(com.filemanager.common.R.string.dialog_ok)
            setOnSelectedListener(bean.listener)
            setOnKeyListener(object : DialogInterface.OnKeyListener {
                override fun onKey(dialog: DialogInterface?, keyCode: Int, keyEvent: KeyEvent?): Boolean {
                    Log.d(TAG, "onKey keyCode:$keyCode event:$keyEvent")
                    if (keyCode == KeyEvent.KEYCODE_BACK && keyEvent?.action == KeyEvent.ACTION_UP) {
                        dialog?.dismiss()
                        bean.listener.onSelected(DialogInterface.BUTTON_NEGATIVE, false)
                        return true
                    }
                    return false
                }
            })
            setOnCancelListener {
                bean.listener.onSelected(DialogInterface.BUTTON_NEGATIVE, false)
            }
            confirmDialog = show()
            confirmDialog?.let {
                PredictiveBackUtils.registerOnBackInvokedCallback(it)
            }
        }
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || progressDialog?.isShowing() ?: false || confirmDialog?.isShowing ?: false
    }

    data class DialogBean(val fileCount: Int, val listener: COUISecurityAlertDialogBuilder.OnSelectedListener)

    private fun createDialog(context: Context, num: Int) {
        val cancelAction = {
            mLifecycle?.onProgressDialogCancel()
            progressDialog?.forceDismiss()
            progressDialog = null
        }
        progressDialog = DelayLoadingMinShowTimeDialog(context, R.style.fop_SecurityChip_AlertDialog_Progress_Cancelable, cancelAction)
        progressDialog?.setTitle(context.resources.getQuantityString(com.filemanager.common.R.plurals.security_chip_encrypting_files, num, num))
        progressDialog?.setFileNum(num)
        progressDialog?.setCancelOutside(false)
    }

    private fun showProgressDialog(context: Context, num: Int) {
        if (progressDialog?.isShowing() == true) {
            progressDialog?.forceDismiss()
        }
        createDialog(context, num)
    }

    private fun updateProgressDialog(progress: Int) {
        if (progressDialog?.isShowing() == false) {
            progressDialog?.show()
        }
        progressDialog?.setProgress(progress)
    }

    private fun encryptDone() {
        if (progressDialog?.isShowing() == false) {
            progressDialog?.show()
        }
        progressDialog?.dismiss()
    }

    private fun encryptFail(failCount: Int) {
        if (progressDialog?.isShowing() == false) {
            progressDialog?.show()
        }
        progressDialog?.setFailedCount(failCount)
        progressDialog?.dismiss()
    }
}