/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SerialExecutor
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/1 10:18
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/1       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.utils

import androidx.annotation.VisibleForTesting
import com.filemanager.common.utils.Log
import java.util.concurrent.Executor

class SerialExecutor(
    @get:VisibleForTesting val delegatedExecutor: Executor
) : Executor {

    private val mTasks: ArrayDeque<Task> = ArrayDeque()
    private val mLock: Any = Any()

    @Volatile
    private var mActive: Runnable? = null

    override fun execute(runnable: Runnable) {
        Log.d(TAG, "execute -> Task Size = ${mTasks.size}")
        synchronized(mLock) {
            mTasks.add(Task(this, runnable))
            if (mActive == null) {
                scheduleNext()
            }
        }
    }

    fun scheduleNext() {
        synchronized(mLock) {
            if (mTasks.removeFirstOrNull().also { mActive = it } != null) {
                delegatedExecutor.execute(mActive)
            }
        }
    }

    fun hasPendingTasks(): Boolean {
        synchronized(mLock) {
            return !mTasks.isEmpty()
        }
    }

    internal class Task(
        private val mSerialExecutor: SerialExecutor,
        private val mRunnable: Runnable
    ) : Runnable {
        override fun run() {
            try {
                mRunnable.run()
            } finally {
                mSerialExecutor.scheduleNext()
            }
        }
    }

    companion object {
        private const val TAG = "SerialExecutor"
    }
}