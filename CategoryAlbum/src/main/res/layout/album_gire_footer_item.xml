<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/footer_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignWithParentIfMissing="true"
    android:layout_alignParentStart="true"
    android:paddingTop="@dimen/dimen_8dp"
    android:paddingBottom="@dimen/dimen_8dp">

    <TextView
        android:id="@+id/footer_view"
        android:layout_width="wrap_content"
        android:layout_height="48dp"
        android:layout_gravity="center"
        android:gravity="center"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="@color/black_30_percent"
        android:textSize="@dimen/file_list_item_detail_text_size" />
</FrameLayout>
