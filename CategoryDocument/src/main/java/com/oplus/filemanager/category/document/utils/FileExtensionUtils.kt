/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/15
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.document.utils

import android.text.TextUtils
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import org.apache.commons.io.FilenameUtils
import org.jetbrains.annotations.VisibleForTesting
import java.util.Locale

object FileExtensionUtils {

    @VisibleForTesting
    val tabTitleSuffixList = mutableMapOf<String, ArrayList<String?>>()

    init {
        val tabTitles = mutableListOf<String>()
        val mDocArray = Injector.injectFactory<IDocumentExtensionType>()?.getDocumentFormat(
            appContext
        )
        if (mDocArray != null) {
            for (name in mDocArray) {
                var ext = FilenameUtils.getExtension(name)
                ext = if (TextUtils.isEmpty(ext)) {
                    name.uppercase(Locale.getDefault())
                } else {
                    ext.uppercase(Locale.getDefault())
                }
                tabTitles.add(ext)
            }
        }
        for (element in tabTitles) {
            val list = tabTitleSuffixList.getOrPut(element) { ArrayList() }
            val fileExtensions = arrayListOf(".${element.lowercase(Locale.getDefault())}")
            list.addAll(Injector.injectFactory<IDocumentExtensionType>()?.getAllSelectionArgs(fileExtensions) ?: ArrayList())
        }
    }

    @JvmStatic
    fun findIndexByExtension(file: BaseFileBean): Pair<Int, String> {
        val locale = Locale.getDefault()
        val extension = FileTypeUtils.getExtension(file.mDisplayName)?.lowercase(locale) ?: ""
        val fileSuffix = ".$extension"
        tabTitleSuffixList.onEachIndexed { index, entry ->
            val suffixList = entry.value
            for (suffix in suffixList) {
                if (fileSuffix == suffix) {
                    file.letter = entry.key
                    return Pair(index, entry.key)
                }
            }
        }
        return Pair(0, "")
    }
}