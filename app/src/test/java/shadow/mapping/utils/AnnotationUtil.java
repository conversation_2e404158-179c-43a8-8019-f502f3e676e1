/************************************************************
 * Copyright 2010-2019 Oplus. All rights reserved.
 * FileName       : AnnotationUtil.java
 * Version Number : 1.0
 * Description    : Get the class mName and value of the Shadow class annotation
 * Author         : zhenggang
 * Date           : 2019-07-15
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019-07-15, zhenggang, create
 ************************************************************/

package shadow.mapping.utils;

import org.robolectric.annotation.Implements;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;

import javassist.ClassPool;
import javassist.CtClass;
import javassist.NotFoundException;
import javassist.bytecode.annotation.Annotation;
import javassist.bytecode.annotation.AnnotationImpl;
import javassist.bytecode.annotation.ClassMemberValue;
import javassist.bytecode.annotation.MemberValue;
import javassist.bytecode.annotation.StringMemberValue;
import shadow.mapping.annotation.Mapping;

public class AnnotationUtil {

    private static final String PARAM = "value";
    private static final String STR_EMPTY = "";
    private static final String FIELD = "h";

    /**
     * Get the class mName of the Shadow class {@linkplain Implements} annotation
     *
     * @param clazz
     * @return
     * @throws ClassNotFoundException
     * @throws NotFoundException
     */
    public static String getAnnotationClassName(Class clazz) throws ClassNotFoundException, NotFoundException {
        ClassPool pool = ClassPool.getDefault();
        CtClass cc = pool.get(clazz.getName());
        Mapping mapping = (Mapping) cc.getAnnotation(Mapping.class);
        String className = mapping.className();

        if ((className == null) || className.equals(STR_EMPTY)) {
            // Get the Implements value
            className = getValue(mapping, PARAM);
        }
        return className;
    }

    /**
     * Get annotated a parameter value
     */
    private static String getValue(Object obj, String param) {
        AnnotationImpl annotationImpl = (AnnotationImpl) getAnnotationImpl(obj);
        Annotation annotation = annotationImpl.getAnnotation();
        MemberValue memberValue = annotation.getMemberValue(param);

        if (memberValue instanceof ClassMemberValue) {
            return ((ClassMemberValue) memberValue).getValue();
        } else if (memberValue instanceof StringMemberValue) {
            return ((StringMemberValue) memberValue).getValue();
        }
        return STR_EMPTY;
    }

    private static InvocationHandler getAnnotationImpl(Object obj) {
        Class clz = obj.getClass().getSuperclass();
        try {
            Field field = clz.getDeclaredField(FIELD);
            field.setAccessible(true);
            InvocationHandler annotation = (InvocationHandler) field.get(obj);
            return annotation;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}