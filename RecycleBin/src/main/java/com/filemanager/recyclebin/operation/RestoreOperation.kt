/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - RestoreOperation.java
 * Description: operation for restore file or folder to origin path
 * Version: 1.0
 * Date : 2020/02/26
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/02/26    1.0     create
 ****************************************************************/

package com.filemanager.recyclebin.operation

import android.app.Activity
import android.content.ContentUris
import android.content.ContentValues
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import android.net.Uri
import androidx.activity.ComponentActivity
import com.filemanager.common.RecycleStore
import com.filemanager.common.RecycleStore.Files.INTERNAL_CONTENT_URI
import com.filemanager.common.RecycleStore.Files.isInternalVolume
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.batch.DoubleBatchAction
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.RecycleBinLiveData
import com.filemanager.common.wrapper.RecycleFileWrapper
import com.filemanager.recyclebin.operation.action.QueryBatchAction
import com.filemanager.recyclebin.operation.action.QueryBatchAction.Companion.QUERY_BATCH_ID_COUNT
import com.filemanager.recyclebin.retriever.DataRetriever
import com.filemanager.recyclebin.utils.OperationDBUtils
import com.oplus.filemanager.interfaze.fileopentime.IFileOpenTime
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.fileservice.IFileService.Companion.OPERATE_TYPE_RESTORE
import com.oplus.filemanager.interfaze.main.IMain
import java.io.File

class RestoreOperation(activity: ComponentActivity, listener: ProgressListener)
    : BaseOperation<Activity>(activity, listener) {
    companion object {
        private const val TAG = "RestoreOperation"
        private const val SCANFILE_BATCH_COUNT = 100
        private const val DELETEFILE_BATCH_COUNT = 100
    }

    private val mRestoreBulk: RestoreAction
    private var mPaths: ArrayList<String>? = null
    private var mFileBeans: ArrayList<BaseFileBean>? = null
    private var mUris: ArrayList<Uri>? = null
    private val mFileOperateSyncFiles = hashSetOf<String?>()

    private val allFilePathForLabels = ArrayList<String>()

    private val fileServiceAction: IFileService? by lazy {
        Injector.injectFactory<IFileService>()
    }

    init {
        activity.runOnUiThread {
            activity.lifecycle.addObserver(this)
        }
        mRestoreBulk = RestoreAction(mColumnName1 = RecycleStore.Files.FileColumns.RECYCLE_ID)
    }

    override fun addPaths(paths: ArrayList<String>?): Boolean {
        // query with path take too long time, so not support path as parameters
        throw UnsupportedOperationException()
    }

    override fun addUris(uris: ArrayList<Uri>?): Boolean {
        if (mUris != null) {
            Log.w(TAG, "addUris already add a task")
            return false
        }

        uris?.let {
            mUris = ArrayList()
            mUris?.addAll(it)
            return true
        }
        return false
    }

    override fun addFileBeans(files: ArrayList<BaseFileBean>?): Boolean {
        if (mFileBeans != null) {
            Log.w(TAG, "addFiles already add a task")
            return false
        }

        files?.let {
            mFileBeans = ArrayList()
            mFileBeans?.addAll(it)
            return true
        }
        return false
    }

    override fun onTotalCountCallback(): Int {
        return when {
            mUris != null -> {
                mUris?.size ?: 0
            }
            mPaths != null -> {
                mPaths?.size ?: 0
            }
            mFileBeans != null -> {
                mFileBeans?.size ?: 0
            }
            else -> 0
        }
    }

    override fun onParametersCallback(): OperationParameters? {
        return OperationParameters(mFileBeans)
    }

    override fun doInBackground(vararg voids: Void): OperationResult {
        Log.d(TAG, "doInBackground")
        try {
            when {
                mUris != null -> {
                    Log.d(TAG, "doInBackground mUris")
                    return restoreUris()
                }

                mFileBeans != null -> {
                    Log.d(TAG, "doInBackground mFileBeans")
                    return restoreFiles()
                }
            }
        } catch (ex: Exception) {
            Log.w(TAG, "doInBackground failed: $ ex")
        } finally {
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.onRestoreFilePaths(allFilePathForLabels)
            mRestoreBulk.flush()
        }
        val totalCount = onTotalCountCallback()
        return OperationResult(totalCount, totalCount, STATUS_ERROR)
    }

    private fun restoreFiles(): OperationResult {
        Log.d(TAG, "restoreFiles")
        if (mFileBeans.isNullOrEmpty()) {
            Log.d(TAG, "restoreFiles isNullOrEmpty")
            return OperationResult(0, 0, STATUS_ERROR)
        }

        val totalSize = mFileBeans?.size ?: 0
        val queryAction = QueryBatchAction(column = RecycleStore.Files.FileColumns.RECYCLE_ID, bulkCount = QUERY_BATCH_ID_COUNT)
        val ids = ArrayList<String>()
        var id: String?

        for (file in mFileBeans!!) {
            id = if (file is RecycleFileWrapper) {
                file.mRecycleId
            } else {
                Log.d(TAG, "restoreFiles file is not recycle bin file")
                continue
            }

            if (id.isNullOrEmpty()) {
                Log.d(TAG, "restoreFiles recycleId is null")
                continue
            }

            Log.d(TAG, "restoreFiles -> id = $id")
            queryAction.add(id)
            ids.add(id)
        }
        queryAction.flush()

        val valuesList = queryAction.getBatchResult()
        // count failedCount in batch query
        var failedCount = totalSize - valuesList.size

        val store = DataRetriever.INSTANCE
        var index = 0
        var values: ContentValues?
        var statusCode: Int
        for (tempId in ids) {
            values = valuesList[tempId]
            statusCode = internalRestore(store, values)
            Log.d(TAG, "restoreFiles statusCode $statusCode")
            if (statusCode == STATUS_NOT_ENOUGH_SPACE) {
                Log.d(TAG, "restoreFiles STATUS_NOT_ENOUGH_SPACE")
                index++
                failedCount++
                return OperationResult(totalSize, failedCount + (totalSize - index), STATUS_NOT_ENOUGH_SPACE)
            }

            if (statusCode != STATUS_SUCCESS) {
                failedCount++
                index++
                Log.w(TAG, "restorePaths  index: $index failedCount: $failedCount")
                continue
            }
            index++
            publishProgress((index / totalSize.toFloat() * 100).toInt())
        }
        mRestoreBulk.flush()
        failedCount += (ids.size - index)
        return OperationResult(totalSize, failedCount,
                if (failedCount > 0) {
                    STATUS_ERROR
                } else {
                    STATUS_SUCCESS
                }
        )
    }

    override fun onPostExecute(result: OperationResult) {
        super.onPostExecute(result)
        uploadOperateResult()
        onDestroy()
    }

    override fun onDestroy() {
        mTarget.get()?.let {
            it.runOnUiThread {
                (it as? ComponentActivity)?.lifecycle?.removeObserver(this)
            }
        }
        super.onDestroy()
    }

    private fun restoreUris(): OperationResult {
        if (mUris.isNullOrEmpty()) {
            return OperationResult(0, 0, STATUS_ERROR)
        }

        val totalSize = mUris?.size ?: 0
        val queryAction = QueryBatchAction(column = RecycleStore.Files.FileColumns.RECYCLE_ID, bulkCount = QUERY_BATCH_ID_COUNT)
        val ids = ArrayList<String>()
        var id: Long
        for (uri in mUris!!) {
            try {
                if (!isInternalVolume(uri)) {
                    continue
                }

                id = try {
                    ContentUris.parseId(uri)
                } catch (ex: Exception) {
                    Log.w(TAG, "restoreUris  parseId uri failed:")
                    RecycleStore.INVALID_ID
                }

                if (id == RecycleStore.INVALID_ID) {
                    continue
                }

                queryAction.add(id.toString())
                ids.add(id.toString())
            } catch (ex: Exception) {
                Log.w(TAG, "restoreUris parseId error: $ex uri: $uri")
            }
        }
        queryAction.flush()

        val valuesList = queryAction.getBatchResult()
        var failedCount = totalSize - valuesList.size
        var index = 0
        val store = DataRetriever.INSTANCE
        var values: ContentValues?
        var statusCode: Int
        for (tempId in ids) {
            values = valuesList[tempId]
            statusCode = internalRestore(store, values)
            if (statusCode == STATUS_NOT_ENOUGH_SPACE) {
                index++
                failedCount++
                return OperationResult(totalSize, failedCount + (totalSize - index), STATUS_NOT_ENOUGH_SPACE)
            }

            if (statusCode != STATUS_SUCCESS) {
                failedCount++
                index++
                Log.w(TAG, "restoreUris  index: $index failedCount: $failedCount")
                continue
            }
            index++
            publishProgress((index / totalSize.toFloat() * 100).toInt())
        }
        mRestoreBulk.flush()
        failedCount += (ids.size - index)
        return OperationResult(totalSize, failedCount,
                if (failedCount > 0) {
                    STATUS_ERROR
                } else {
                    STATUS_SUCCESS
                }
        )
    }

    private fun internalRestore(retrieve: DataRetriever?, values: ContentValues?): Int {
        Log.d(TAG, "internalRestore")
        try {
            val statusCode = retrieve!!.restoreToOriginPath(values!!)
            Log.d(TAG, "internalRestore statusCode $statusCode")
            // clean item record only if store success
            if (statusCode == STATUS_SUCCESS) {
                val realOriginPath = values.getAsString(RecycleStore.Files.FileColumns.ORIGIN_PATH)
                val id = values.getAsString(RecycleStore.Files.FileColumns.RECYCLE_ID)
                val recycleDate = values.getAsLong(RecycleStore.Files.FileColumns.RECYCLE_DATE)
                statisticsDurationDate(RecycleBinLiveData.RECYCLE_RESTORE, recycleDate)
                allFilePathForLabels.add(realOriginPath)
                mRestoreBulk.add(id, realOriginPath)
                queueFiles(realOriginPath)
                /**恢复文件时，更新最近打开时间*/
                val currTime = System.currentTimeMillis()
                val file = File(realOriginPath)
                val fileOpenTimeAction = Injector.injectFactory<IFileOpenTime>()
                val addLine = fileOpenTimeAction?.addFileOpenTime(realOriginPath, currTime, file.lastModified())
                Log.d(TAG, "Restore addLine = $addLine")
            }
            return statusCode
        } catch (ex: Exception) {
            Log.w(TAG, "internalRestore error: $ex")
        }
        return STATUS_ERROR
    }
    private class RestoreAction(private val mUri: Uri = INTERNAL_CONTENT_URI,
                                private val mColumnName1: String)
        : DoubleBatchAction<String, String>(DELETEFILE_BATCH_COUNT, SCANFILE_BATCH_COUNT) {

        private val mWhereIdClause: StringBuilder = StringBuilder()
        var sqliteOpenHelper: SQLiteOpenHelper? = null
        var db: SQLiteDatabase? = null

        override fun add(pending1: String?, pending2: String?) {
            if (pending1?.isNotEmpty() == true) {
                if (mWhereIdClause.isNotEmpty()) {
                    mWhereIdClause.append(",")
                }
                mWhereIdClause.append("?")
            }

            super.add(pending1, pending2)
        }

        override fun flushParams1() {
            val size = mPending1.size
            if (size <= 0) {
                return
            }
            Log.d(TAG, "flushParams1 delete recycle bin db data $size")
            try {
                if (db == null) {
                    sqliteOpenHelper = OperationDBUtils.createSQLiteOpenHelper(mUri)
                    db = OperationDBUtils.beginTransaction(sqliteOpenHelper?.writableDatabase)
                }
                val foo = mPending1.toTypedArray()
                val rowsNum = OperationDBUtils.delete(mUri,
                    mColumnName1 + " IN ("
                            + mWhereIdClause.toString() + ")", foo, db, sqliteOpenHelper)
                Log.d(TAG, "flush size: $size rowsNum: $rowsNum")
            } catch (ex: Exception) {
                Log.w(TAG, "flush Failed $ex")
            } finally {
                //make sure reset args only when sucess
                mWhereIdClause.setLength(0)
                mPending1.clear()
                OperationDBUtils.commit(db, INTERNAL_CONTENT_URI)
                sqliteOpenHelper = null
                db = null
            }
        }

        override fun flushParams2() {
            if (mPending2.size > 0) {
                Log.d(TAG, "flushParams2 notify MediaStore update ")
                MediaScannerCompat.sendMultiDirMediaScanner(mPending2, Utils.MEDIA_SCAN_RECYCLE_RESTORE)
                mPending2.clear()
            }
        }
    }

    private fun queueFiles(originPath: String?) {
        if (fileServiceAction != null) {
            originPath?.let {
                val tempPath = it.trim()
                val originParentPath = tempPath.substring(0, tempPath.lastIndexOf("/"))
                mFileOperateSyncFiles.add(originParentPath)
            }
        }
    }

    private fun uploadOperateResult() {
        fileServiceAction?.syncOperate(OPERATE_TYPE_RESTORE, mFileOperateSyncFiles)
    }
}
