/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - PreviewVideoPlayBar.kt
 * Description:
 *     The widget of play video bar(control buttons and progress)
 *
 * Version: 1.0
 * Date: 2024-09-18
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-09-18   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.RelativeLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.coui.appcompat.seekbar.COUISeekBar
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.utils.formatToMediaPlayTime
import com.oplus.filemanager.preview.utils.setContentDescription
import com.oplus.filemanager.preview.utils.toMediaPlayPosition
import com.oplus.filemanager.preview.utils.toMediaPlayProgress

internal class PreviewVideoPlayBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : RelativeLayout(context, attrs), COUISeekBar.OnSeekBarChangeListener {

    private val playButton: AppCompatImageView
    private val volumeButton: AppCompatImageView
    private val positionTextView: AppCompatTextView
    private val durationTextView: AppCompatTextView
    private val progressSeekBar: COUISeekBar

    private var seekProgressListener: OnSeekPlayProgressListener? = null
    private var clickPlayListener: OnClickPlayButtonListener? = null
    private var clickVolumeListener: OnClickVolumeButtonListener? = null
    private var isTouchingSeekBar = false
    private var isVolumeMute = true
    private var isVideoPlaying = false

    init {
        LayoutInflater.from(context).inflate(R.layout.widget_preview_video_play_bar, this)
        setBackgroundColor(context.getColor(R.color.preview_video_play_bar_bg))
        playButton = findViewById(R.id.preview_video_play_button)
        playButton.setOnClickListener(::onClickPlayButton)
        volumeButton = findViewById(R.id.preview_video_volume_button)
        volumeButton.setOnClickListener(::onClickVolumeButton)
        positionTextView = findViewById(R.id.preview_video_play_position)
        durationTextView = findViewById(R.id.preview_video_play_duration)
        progressSeekBar = findViewById(R.id.preview_video_play_seek)
        progressSeekBar.min = 0
        progressSeekBar.setOnSeekBarChangeListener(this)
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        super.dispatchTouchEvent(ev)
        // 防止点击到播放控制控件的背景区域穿透到底层的视频TextureView导致非预期的隐藏播放控制控件
        return true
    }

    fun setDuration(duration: Long) {
        val progressMax = duration.toMediaPlayProgress()
        if (progressSeekBar.max == progressMax) {
            return
        }
        Log.d(TAG, "setDuration: duration=$duration")
        progressSeekBar.max = progressMax
        durationTextView.text = duration.formatToMediaPlayTime()
    }

    fun setProgress(position: Long) {
        if (isTouchingSeekBar) {
            return
        }
        val progressPosition = position.toMediaPlayProgress()
        if (progressSeekBar.progress == progressPosition) {
            return
        }
        progressSeekBar.progress = progressPosition
    }

    fun setOnSeekPlayProgressListener(listener: OnSeekPlayProgressListener) {
        seekProgressListener = listener
    }

    override fun onProgressChanged(seekBar: COUISeekBar?, progress: Int, fromUser: Boolean) {
        val videoPosition = progress.toMediaPlayPosition()
        positionTextView.text = videoPosition.formatToMediaPlayTime()
        if (fromUser) {
            seekProgressListener?.onSeekPlayProgress(videoPosition, isTouchingSeekBar)
        }
    }

    override fun onStartTrackingTouch(seekBar: COUISeekBar?) {
        isTouchingSeekBar = true
    }

    override fun onStopTrackingTouch(seekBar: COUISeekBar?) {
        isTouchingSeekBar = false
        val videoPosition = seekBar?.progress?.toMediaPlayPosition() ?: return
        seekProgressListener?.onSeekPlayProgress(videoPosition, false)
    }

    fun setVideoPlayState(isPlaying: Boolean) {
        isVideoPlaying = isPlaying
        if (isPlaying) {
            playButton.setImageResource(R.drawable.preview_icon_media_pause)
            playButton.setContentDescription(com.filemanager.common.R.string.pause)
        } else {
            playButton.setImageResource(R.drawable.preview_icon_media_play)
            playButton.setContentDescription(com.filemanager.common.R.string.media_play)
        }
    }

    private fun onClickPlayButton(view: View) {
        clickPlayListener?.onClickPlayButton(isVideoPlaying)
        setVideoPlayState(!isVideoPlaying)
    }

    fun setOnClickPlayButtonListener(listener: OnClickPlayButtonListener) {
        clickPlayListener = listener
    }

    fun setVideoVolumeState(isMute: Boolean) {
        isVolumeMute = isMute
        if (isMute) {
            volumeButton.setImageResource(R.drawable.preview_icon_media_volume_mute)
            volumeButton.setContentDescription(com.filemanager.common.R.string.media_unmute)
        } else {
            volumeButton.setImageResource(R.drawable.preview_icon_media_volume_voice)
            volumeButton.setContentDescription(com.filemanager.common.R.string.media_mute)
        }
    }

    private fun onClickVolumeButton(view: View) {
        clickVolumeListener?.onClickVolumeButton(!isVolumeMute)
        setVideoVolumeState(!isVolumeMute)
    }

    fun setOnClickVolumeButtonListener(listener: OnClickVolumeButtonListener) {
        clickVolumeListener = listener
    }

    fun release() {
        seekProgressListener = null
        clickPlayListener = null
        clickVolumeListener = null
    }

    fun interface OnSeekPlayProgressListener {
        fun onSeekPlayProgress(position: Long, isTouching: Boolean)
    }

    fun interface OnClickPlayButtonListener {
        fun onClickPlayButton(requireToPause: Boolean)
    }

    fun interface OnClickVolumeButtonListener {
        fun onClickVolumeButton(requireToMute: Boolean)
    }

    private companion object {
        private const val TAG = "PreviewVideoPlayBar"
    }
}