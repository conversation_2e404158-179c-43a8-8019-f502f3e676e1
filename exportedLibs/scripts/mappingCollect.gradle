final hasOBuildPlugin = project.plugins.hasPlugin("obuildplugin")
if (!hasOBuildPlugin) {
    final errMsg = "${project.path}: mappingCollect.gradle is depended on obuildplugin, " +
            "please apply obuildplugin and configure OBuildConfig first"
    System.err.println(errMsg)
    throw new GradleException(errMsg)
}

class PrebuiltInfo {
    final String groupId
    final String artifactId
    final String version

    PrebuiltInfo(String groupId, String artifactId, String version) {
        this.groupId = groupId
        this.artifactId = artifactId
        this.version = version
    }

    @Override
    String toString() {
        return "${groupId}:${artifactId}:${version}"
    }
}

@SuppressWarnings("unused")
class MappingCollectExtensions {
    String publishTaskName = "publishAllPublicationsToReleaseRepository"
    String buildType = "release"
    PrebuiltInfo currentPrebuilt = null
    String mappingRecordTag = "mapping"
    String mappingRecordFormat = "zip"

    void currentPrebuilt(Provider<MinimalExternalModuleDependency> toml) {
        final groupId = toml.get().group
        final artifactId = toml.get().name
        final version = toml.get().version
        currentPrebuilt = new PrebuiltInfo(groupId, artifactId, version)
    }

    void currentPrebuilt(String dependencyNotation) {
        final notationParts = dependencyNotation.split(":")
        if (notationParts.size() != 3) {
            throw new IllegalArgumentException("Incorrect dependency notation: $dependencyNotation")
        }
        final groupId = notationParts[0]
        final artifactId = notationParts[1]
        final version = notationParts[2]
        currentPrebuilt = new PrebuiltInfo(groupId, artifactId, version)
    }
}

class MappingCollectPlugin implements Plugin<Project> {
    private static final EXTENSION_NAME = "mappingCollect"
    private static final MAPPING_RECORD_DIR = "mappingRecords"
    private static final COLLECT_MAPPING_TASK_NAME = "publishCollectMapping"
    private static final SNAPSHOT_SUFFIX = "-SNAPSHOT"

    @Override
    void apply(Project target) {
        final mappingExt = target.extensions.create(EXTENSION_NAME, MappingCollectExtensions)
        target.afterEvaluate {
            checkPrebuiltMappingRecords(target, mappingExt)
            createCollectTask(target, mappingExt)
        }
    }

    private static void createCollectTask(Project target, MappingCollectExtensions mappingExt) {
        final srcPath = "outputs/mapping/${mappingExt.buildType}".replace("/", File.separator)
        final srcDir = new File(target.buildDir, srcPath)
        final dstPath = "libs/${target.name}".replace("/", File.separator)
        final dstDir = new File(target.rootProject.buildDir, dstPath)
        final dstZipName = getMappingZipFileName(mappingExt, target.OBuildConfig.sdkArtifactId, target.version)
        target.tasks.register(COLLECT_MAPPING_TASK_NAME, Zip) {
            it.dependsOn(mappingExt.publishTaskName)
            it.group = "publishing"
            it.setArchiveFileName(dstZipName)
            it.setDestinationDirectory(dstDir)
            it.from(srcDir.absolutePath)
        }
        target.tasks.named(mappingExt.publishTaskName).configure {
            it.finalizedBy(COLLECT_MAPPING_TASK_NAME)
        }
    }

    /**
     * maven库无法直接上传存储exportedLibs发布的SDK对应的mapping文件。
     * 流水线制品仓虽然能一并收集强行塞入发布目录的mapping文件，但snapshot制品仓不会长期存储。
     * 故先行要求更新exportedLibs的prebuilt版本时，必须把对应的mapping文件从制品仓中下载下来，
     * 按"${artifactId}_mapping_${version}.zip"的格式，打包存储到对应SDK的module中的mappingRecords目录。
     * 通过在代码仓存储的方式长期存储对应SDK版本的mapping文件，以备后续分析问题使用。
     */
    private static void checkPrebuiltMappingRecords(Project target, MappingCollectExtensions mappingExt) {
        final prebuiltInfo = mappingExt.currentPrebuilt
        if (prebuiltInfo == null) {
            return
        }
        final expectedFile = getMappingZipFileName(mappingExt, prebuiltInfo.artifactId, prebuiltInfo.version)
        final expectedPath = "${MAPPING_RECORD_DIR}/${expectedFile}".replace('/', File.separator)
        final expectedRecord = target.file(expectedPath)
        if (!expectedRecord.isFile() || expectedRecord.size() <= 0) {
            throw new IllegalStateException("${target.path}: Missing mapping record $expectedFile for $prebuiltInfo")
        }
    }

    private static String getMappingZipFileName(
            MappingCollectExtensions mappingExt,
            String artifactId,
            String version
    ) {
        final mappingTag = mappingExt.mappingRecordTag
        final fileFormat = mappingExt.mappingRecordFormat
        def versionStr = version
        if (version.endsWith(SNAPSHOT_SUFFIX)) {
            versionStr = version.substring(0, version.length() - SNAPSHOT_SUFFIX.length())
        }
        return "${artifactId}-${versionStr}.${mappingTag}.${fileFormat}"
    }
}

project.plugins.apply(MappingCollectPlugin)