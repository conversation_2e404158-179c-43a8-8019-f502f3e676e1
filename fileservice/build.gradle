plugins {
    id 'com.android.library'
    id 'kotlinx-serialization'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/variant.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace "com.oplus.fileservice"
}

dependencies {
    compileOnly(libs.oplus.addon.sdk) { artifact { type = "aar" } }

    implementation libs.androidx.appcompat
    implementation libs.google.material
    implementation libs.apache.commons.io
    implementation libs.bumptech.glide.base
    implementation libs.kotlinx.serialization.json
    implementation libs.bundles.squareup.okhttp3
    implementation libs.bundles.squareup.retrofit2

    implementation libs.heytap.addon.adapter
    implementation libs.oplus.stdid.sdk
    implementation libs.oplus.owork.sync
    implementation libs.koin.android

    implementation project(':Common')
    implementation project(':RecycleBin')
    implementation project(':Main')
    implementation project(':CategoryAudioVideo')
    implementation project(':CategoryDocument')
}