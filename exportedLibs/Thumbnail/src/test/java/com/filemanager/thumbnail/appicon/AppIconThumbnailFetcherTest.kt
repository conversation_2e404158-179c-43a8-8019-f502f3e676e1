package com.filemanager.thumbnail.appicon

import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import com.bumptech.glide.Priority
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.data.DataFetcher
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * AppIconThumbnailFetcher的单元测试类
 * 使用RobolectricTestRunner运行测试，配置SDK版本为29
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class AppIconThumbnailFetcherTest {

    // 定义测试所需的mock对象
    private lateinit var fetcher: AppIconThumbnailFetcher
    private lateinit var mockModel: AppIconThumbnail
    private lateinit var mockPackageManager: PackageManager
    private lateinit var mockCallback: DataFetcher.DataCallback<in Bitmap>
    private lateinit var mockIcon: Drawable
    private lateinit var mockBitmap: Bitmap

    /**
     * 在每个测试方法执行前初始化mock对象和测试环境
     */
    @Before
    fun setUp() {
        // 创建各个mock对象
        mockModel = mockk(relaxed = true)
        mockPackageManager = mockk(relaxed = true)
        mockCallback = mockk(relaxed = true)
        mockIcon = mockk(relaxed = true)
        mockBitmap = mockk(relaxed = true)

        // 配置mock对象的行为
        every { mockModel.context.packageManager } returns mockPackageManager
        every { mockModel.packageName } returns "com.example.app"
        every { mockIcon.intrinsicWidth } returns 100
        every { mockIcon.intrinsicHeight } returns 100
    }

    /**
     * 在每个测试方法执行后清理mock对象和测试环境
     */
    @After
    fun tearDown() {
        clearAllMocks()  // 清除所有mock对象
        fetcher.cleanup()  // 调用fetcher的清理方法
    }

    /**
     * 测试getDataClass方法应返回Bitmap类
     */
    @Test
    fun `getDataClass should return Bitmap class`() {
        fetcher = AppIconThumbnailFetcher(mockModel, 100, 100)
        assertEquals(Bitmap::class.java, fetcher.dataClass)
    }

    /**
     * 测试getDataSource方法应返回REMOTE数据源
     */
    @Test
    fun `getDataSource should return REMOTE`() {
        fetcher = AppIconThumbnailFetcher(mockModel, 100, 100)
        assertEquals(DataSource.REMOTE, fetcher.dataSource)
    }

    /**
     * 测试当fetcher被取消时，loadData应调用onDataReady(null)
     */
    @Test
    fun `loadData should call onDataReady with null when cancelled`() {
        fetcher = AppIconThumbnailFetcher(mockModel, 100, 100)
        fetcher.cancel()  // 取消fetcher
        fetcher.loadData(Priority.HIGH, mockCallback)
        // 验证回调方法被调用且参数为null
        verify(exactly = 1) { mockCallback.onDataReady(null) }
    }

    /**
     * 测试当找不到应用包时，loadData应调用onLoadFailed
     */
    @Test
    fun `loadData should call onLoadFailed when package not found`() {
        val exception = PackageManager.NameNotFoundException()
        // 配置mock PackageManager抛出异常
        every { mockPackageManager.getApplicationIcon(any<String>()) } throws exception

        fetcher = AppIconThumbnailFetcher(mockModel, 100, 100)
        fetcher.loadData(Priority.HIGH, mockCallback)

        // 验证回调方法被调用且参数为异常对象
        verify(exactly = 1) { mockCallback.onLoadFailed(exception) }
    }
}