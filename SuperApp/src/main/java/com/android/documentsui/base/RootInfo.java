/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RootInfo.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/9
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  hank.zhou      2022/9/9      1.0        create
 ***********************************************************************/
package com.android.documentsui.base;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import androidx.annotation.IntDef;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.net.ProtocolException;
import java.text.Collator;
import java.util.Objects;

public class RootInfo implements Durable, Parcelable, Comparable<RootInfo> {

    public final static Parcelable.Creator<RootInfo> CREATOR = new Creator<RootInfo>() {
        @Override
        public RootInfo createFromParcel(Parcel in) {
            final RootInfo root = new RootInfo();
            DurableUtils.readFromParcel(in, root);
            return root;
        }

        @Override
        public RootInfo[] newArray(int size) {
            return new RootInfo[size];
        }
    };
    public final static int TYPE_RECENTS = 1;
    public final static int TYPE_IMAGES = 2;
    public final static int TYPE_VIDEO = 3;
    public final static int TYPE_AUDIO = 4;
    public final static int TYPE_DOCUMENTS = 5;
    public final static int TYPE_DOWNLOADS = 6;
    public final static int TYPE_LOCAL = 7;
    public final static int TYPE_MTP = 8;
    public final static int TYPE_SD = 9;
    public final static int TYPE_USB = 10;
    public final static int TYPE_OTHER = 11;
    private static final String TAG = "RootInfo";
    private static final int VERSION_DROP_TYPE = 2;
    private static final int VERSION_SEARCH_TYPE = 3;
    private static final int VERSION_USER_ID = 4;

    @IntDef(flag = false, value = {
            TYPE_RECENTS,
            TYPE_IMAGES,
            TYPE_VIDEO,
            TYPE_AUDIO,
            TYPE_DOCUMENTS,
            TYPE_DOWNLOADS,
            TYPE_LOCAL,
            TYPE_MTP,
            TYPE_SD,
            TYPE_USB,
            TYPE_OTHER
    })
    @Retention(RetentionPolicy.SOURCE)
    public @interface RootType {

    }

    public UserId userId;
    public String authority;
    public String rootId;
    public int flags;
    public int icon;
    public String title;
    public String summary;
    public String documentId;
    public long availableBytes;
    public String mimeTypes;
    public String queryArgs;

    /** Derived fields that aren't persisted */
    public String[] derivedMimeTypes;
    public int derivedIcon;
    public @RootType int derivedType;
    public transient boolean ejecting;

    public RootInfo() {
        reset();
    }

    @Override
    public void reset() {
        userId = UserId.UNSPECIFIED_USER;
        authority = null;
        rootId = null;
        flags = 0;
        icon = 0;
        title = null;
        summary = null;
        documentId = null;
        availableBytes = -1;
        mimeTypes = null;
        ejecting = false;
        queryArgs = null;

        derivedMimeTypes = null;
        derivedIcon = 0;
        derivedType = TYPE_RECENTS;
    }

    @Override
    public void read(DataInputStream in) throws IOException {
        final int version = in.readInt();
        switch (version) {
            case VERSION_USER_ID:
                userId = UserId.read(in);
                queryArgs = DurableUtils.readNullableString(in);
                authority = DurableUtils.readNullableString(in);
                rootId = DurableUtils.readNullableString(in);
                flags = in.readInt();
                icon = in.readInt();
                title = DurableUtils.readNullableString(in);
                summary = DurableUtils.readNullableString(in);
                documentId = DurableUtils.readNullableString(in);
                availableBytes = in.readLong();
                mimeTypes = DurableUtils.readNullableString(in);
                break;
            case VERSION_SEARCH_TYPE:
                userId = UserId.CURRENT_USER;
                queryArgs = DurableUtils.readNullableString(in);
                authority = DurableUtils.readNullableString(in);
                rootId = DurableUtils.readNullableString(in);
                flags = in.readInt();
                icon = in.readInt();
                title = DurableUtils.readNullableString(in);
                summary = DurableUtils.readNullableString(in);
                documentId = DurableUtils.readNullableString(in);
                availableBytes = in.readLong();
                mimeTypes = DurableUtils.readNullableString(in);
                break;
            case VERSION_DROP_TYPE:
                authority = DurableUtils.readNullableString(in);
                rootId = DurableUtils.readNullableString(in);
                flags = in.readInt();
                icon = in.readInt();
                title = DurableUtils.readNullableString(in);
                summary = DurableUtils.readNullableString(in);
                documentId = DurableUtils.readNullableString(in);
                availableBytes = in.readLong();
                mimeTypes = DurableUtils.readNullableString(in);
                break;
            default:
                throw new ProtocolException("Unknown version " + version);
        }
    }

    @Override
    public void write(DataOutputStream out) throws IOException {
        out.writeInt(VERSION_USER_ID);
        UserId.write(out, userId);
        DurableUtils.writeNullableString(out, queryArgs);
        DurableUtils.writeNullableString(out, authority);
        DurableUtils.writeNullableString(out, rootId);
        out.writeInt(flags);
        out.writeInt(icon);
        DurableUtils.writeNullableString(out, title);
        DurableUtils.writeNullableString(out, summary);
        DurableUtils.writeNullableString(out, documentId);
        out.writeLong(availableBytes);
        DurableUtils.writeNullableString(out, mimeTypes);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        DurableUtils.writeToParcel(dest, this);
    }

    /**
     * Returns a new root info copied from the provided root info.
     */
    public static RootInfo copyRootInfo(RootInfo root) {
        final RootInfo newRoot = new RootInfo();
        newRoot.userId = root.userId;
        newRoot.authority = root.authority;
        newRoot.rootId = root.rootId;
        newRoot.flags = root.flags;
        newRoot.icon = root.icon;
        newRoot.title = root.title;
        newRoot.summary = root.summary;
        newRoot.documentId = root.documentId;
        newRoot.availableBytes = root.availableBytes;
        newRoot.mimeTypes = root.mimeTypes;
        newRoot.queryArgs = root.queryArgs;

        // derived fields
        newRoot.derivedType = root.derivedType;
        newRoot.derivedIcon = root.derivedIcon;
        newRoot.derivedMimeTypes = root.derivedMimeTypes;
        return newRoot;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null) {
            return false;
        }

        if (this == o) {
            return true;
        }

        if (o instanceof RootInfo) {
            RootInfo other = (RootInfo) o;
            return Objects.equals(userId, other.userId)
                    && Objects.equals(authority, other.authority)
                    && Objects.equals(rootId, other.rootId);
        }
        return false;
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, authority, rootId);
    }

    @Override
    public int compareTo(RootInfo other) {
        // Sort by root type, then title, then summary.
        int score = derivedType - other.derivedType;
        if (score != 0) {
            return score;
        }

        score = compareToIgnoreCaseNullable(title, other.title);
        if (score != 0) {
            return score;
        }

        return compareToIgnoreCaseNullable(summary, other.summary);
    }

    @Override
    public String toString() {
        return "Root{"
                + "userId=" + userId
                + ", authority=" + authority
                + ", rootId=" + rootId
                + ", title=" + title
                + "} @ ";
    }

    public String toDebugString() {
        return (TextUtils.isEmpty(summary))
                ? "\"" + title + "\" @ "
                : "\"" + title + " (" + summary + ")\" @ ";
    }

    public String getDirectoryString() {
        return (!TextUtils.isEmpty(summary)) ? summary : title;
    }

    public static int compareToIgnoreCaseNullable(String lhs, String rhs) {
        Collator sCollator = Collator.getInstance();
        sCollator.setStrength(Collator.SECONDARY);
        final boolean leftEmpty = TextUtils.isEmpty(lhs);
        final boolean rightEmpty = TextUtils.isEmpty(rhs);

        if (leftEmpty && rightEmpty) {
            return 0;
        }
        if (leftEmpty) {
            return -1;
        }
        if (rightEmpty) {
            return 1;
        }
        return sCollator.compare(lhs, rhs);
    }
}
