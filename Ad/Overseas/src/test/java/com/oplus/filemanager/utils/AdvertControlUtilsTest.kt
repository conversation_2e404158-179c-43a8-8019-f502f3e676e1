package com.oplus.filemanager.utils

import com.filemanager.common.utils.PreferencesUtils
import io.mockk.every
import io.mockk.mockkObject
import org.junit.Test
import java.time.ZoneId
import java.time.ZonedDateTime
import kotlin.test.assertEquals

/**
 * AdvertControlUtils的单元测试类
 * 用于测试广告控制工具类的各种功能
 */
class AdvertControlUtilsTest {

    /**
     * 测试appActiveTime方法在首次调用时的行为
     * 1. 模拟PreferencesUtils.getLong返回-1表示首次调用
     * 2. 验证当首次调用时，会记录当前时间并返回
     * 3. 验证返回的时间不小于预设的标准时间(2023-11-01)
     */
    @Test
    fun `test appActiveTime when first time`() {
        // 模拟PreferencesUtils对象
        mockkObject(PreferencesUtils)
        
        // 创建预期的标准时间(2023-11-01)的时间戳
        val expectedTime = ZonedDateTime.of(
            2023, 11, 1, 0, 0, 0, 0, ZoneId.systemDefault()
        ).toInstant().toEpochMilli()
        
        // 设置mock行为：
        // 当调用getLong方法时返回-1，模拟首次调用情况
        every { PreferencesUtils.getLong(any(), any(), any()) } returns -1L
        // 当调用put方法时不执行任何操作
        every { PreferencesUtils.put(any<String>(), any<String>(), any<Long>()) } returns Unit
        
        // 调用被测方法
        val result = appActiveTime()
        
        // 验证结果：返回的时间应该大于等于预期时间
        // 修改断言，不再比较固定时间，而是验证结果是否大于等于预期时间
        assert(result >= expectedTime)
    }
}