/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : BaseMoreMenuAction.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * * 飞书中intent中带入的数据结构
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.bytedance.ee.bear.contract.drive.sdk.entity.menu;

import android.os.Parcel;
import android.os.Parcelable;
import android.util.Log;

public class BaseMoreMenuAction implements Parcelable {

    public static final Creator<BaseMoreMenuAction> CREATOR = new Creator<BaseMoreMenuAction>() {
        @Override
        public BaseMoreMenuAction createFromParcel(Parcel in) {
            return new BaseMoreMenuAction(in);
        }

        @Override
        public BaseMoreMenuAction[] newArray(int size) {
            return new BaseMoreMenuAction[size];
        }
    };

    public static final String TAG = "BaseMoreMenuAction";

    protected BaseMoreMenuAction(Parcel parcel) {
        try {
            parcel.readStrongBinder();
            parcel.readStrongBinder();
            parcel.readInt();
            parcel.readBundle();
            parcel.readBundle();
            parcel.readString();
            parcel.readInt();

            parcel.readInt();
            parcel.readByte();

            parcel.readByte();
            parcel.readString();
            parcel.readInt();
        } catch (Exception e) {
            Log.e(TAG, "BaseMoreMenuAction from parcel error", e);
        }
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        Log.i(TAG, "writeToParcel");
    }

    @Override
    public int describeContents() {
        return 0;
    }


}
