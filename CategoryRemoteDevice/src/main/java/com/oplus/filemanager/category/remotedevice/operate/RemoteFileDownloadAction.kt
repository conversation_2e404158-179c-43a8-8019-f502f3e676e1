/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemoteFileDownloadAction
 * * Description : 远程设备下载文件 Action
 * * Version     : 1.0
 * * Date        : 2024/12/09
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.operate

import android.Manifest
import android.app.PendingIntent
import androidx.activity.ComponentActivity
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.fileutils.checkDestStorageSpace
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.isCellular
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.base.DISMISS_PROGRESS
import com.filemanager.fileoperate.base.SHOW_PROGRESS
import com.oplus.filemanager.category.remotedevice.download.DownloadViewModel
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean
import com.oplus.filemanager.category.remotedevice.download.service.RemoteFileDownloadDispatcher
import com.oplus.filemanager.interfaze.remotedevice.DownloadRemoteFileCallback
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.interfaze.remotedevice.OperateCallback
import com.oplus.filemanager.remotedevice.ResultConstant
import java.util.function.Consumer

class RemoteFileDownloadAction(
    val lifecycle: ComponentActivity,
    private val deviceId: String,
    private val downloadFiles: List<DownloadRemoteFileBean>,
    private val destPath: String,
    private val code: Int,
    private var pendingIntent: PendingIntent?
) : BaseFileAction<RemoteFileDownloadObserver>(lifecycle) {

    companion object {
        private const val TAG = "RemoteFileDownloadAction"
        const val MAX_FILE_COUNT = 99
        const val MAX_FILE_SIZE = 1024 * 1024 * 1024  //1G
        private const val TRAFFIC_REMIND_SIZE = 20 * 1024 * 1024  //20M
    }

    private var totalSize = 0L
    private val lockObj = Object()

    private val downloadCallback = object : DownloadRemoteFileCallback {
        private var progress = 0f

        override fun onProgressEachFile(path: String?, status: Int, progress: Float) {
            updateDownloadingProgress(path ?: "", status)
        }

        override fun onProgress(ratio: Int) {
            progress = ratio * 1.0f
            updateDownloadTotalProgress(DownloadViewModel.DOWNLOADING, ratio * 1.0f)
        }

        override fun onResult(operateCode: Int, resultCode: Int) {
            if (operateCode == OperateCallback.OPERATE_BEGIN_DOWNLOAD) {
                if (ResultConstant.isFinish(resultCode)) {
                    notifyLockReleased()
                }
                if (resultCode == ResultConstant.RESULT_CODE_SUCCESS) {
                    notifyObserver(ACTION_DONE)
                } else if (ResultConstant.isFail(resultCode)) {
                    if (ResultConstant.RESULT_CODE_TOO_MANY_FILES == resultCode) { // 文件总路径超长
                        notifyObserver(RemoteFileDownloadObserver.NOTICE_DOWNLOAD_FILE_PATH_OVER_LENGTH)
                    }
                    notifyObserver(ACTION_FAILED)
                } else if (ResultConstant.RESULT_CODE_CANCEL == resultCode) { // 取消
                    notifyObserver(ACTION_FAILED)
                } else if (ResultConstant.isPause(resultCode)) { // 暂停
                    updateDownloadTotalProgress(DownloadViewModel.DOWNLOAD_WAIT, progress)
                } else if (ResultConstant.isContinue(resultCode)) { // 继续
                    updateDownloadTotalProgress(DownloadViewModel.DOWNLOADING, progress)
                }
            }
        }
    }

    private fun initTrafficDialogBean(size: Long): RemoteFileDownloadObserver.MobileTrafficDialogBean {
        val formatSize = Utils.byteCountToDisplaySize(size)
        val bean = RemoteFileDownloadObserver.MobileTrafficDialogBean(formatSize)
        bean.consumer = Consumer<Boolean> { accept ->
            Log.w(TAG, "trafficDialog click -> continue download: $accept")
            if (accept) {
                notifyLockReleased()
            } else {
                resetDownloadStatus()
                bean.recycle()
                cancel()
            }
        }
        return bean
    }

    override fun onCancelled() {
        super.onCancelled()
        notifyLockReleased()
    }

    private fun notifyLockReleased() {
        Log.d(TAG, "notifyLockReleased")
        synchronized(lockObj) {
            lockObj.notify()
        }
    }

    private fun lockWait() {
        try {
            synchronized(lockObj) {
                lockObj.wait()
            }
        } catch (e: InterruptedException) {
            Log.d(TAG, "Action interrupted")
        }
    }


    override fun run(): Boolean {
        if (downloadFiles.isEmpty()) {
            Log.w(TAG, "run -> select file is empty!!!")
            return false
        }
        // 判断存储空间是否充足
        if (checkFileSizeTooLarge()) {
            Log.w(TAG, "run -> file size too large!!!")
            return false
        }
        if (isFileChannelOccupied(deviceId)) {
            Log.w(TAG, "run -> device:$deviceId file channel is occupied!!!")
            return false
        }
        // 判断是否是流量下载, 手机流量并且超过20MB
        if (checkUseMobileTraffic()) {
            Log.w(TAG, "run -> show mobile traffic dialog")
            val dialogBean = initTrafficDialogBean(totalSize)
            notifyObserver(RemoteFileDownloadObserver.SHOW_DOWNLOAD_MOBILE_TRAFFIC_DIALOG, dialogBean)
            lockWait()
        }
        var isCancel = isCancelled()
        // 判断是否弹通知权限弹窗
        if (isCancel.not() && !checkNotifyPermission()) {
            Log.w(TAG, "run -> show notify permission dialog")
            lockWait()
        }
        isCancel = isCancelled()
        Log.d(TAG, "run -> Continue to execute: isCancelled=$isCancel")
        if (isCancel.not()) {
            return reallyDownloadFile()
        }
        return false
    }

    /**
     * 判断文件size 是否太大
     * @return true: 文件太大
     */
    private fun checkFileSizeTooLarge(): Boolean {
        // 检测文件个数不能超过99个
        if (downloadFiles.size > MAX_FILE_COUNT) {
            notifyObserver(RemoteFileDownloadObserver.NOTICE_DOWNLOAD_FILE_OVER_COUNT)
            return true
        }

        // 获取总文件大小
        notifyObserver(
            SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                mContext.getString(com.filemanager.common.R.string.string_being_calculated), true
            ), delayInMill = 300
        )
        downloadFiles.forEach {
            if (it is DownloadRemoteFileBean) {
                totalSize += it.mSize
            } else {
                totalSize += JavaFileHelper.fileTotalSize(it)
            }
        }
        cancelNotifyObserver(SHOW_PROGRESS)
        notifyObserver(DISMISS_PROGRESS)

        // 检测文件总大小不能超过1G
        if (totalSize > MAX_FILE_SIZE) {
            if (MessageConstant.MSG_OPEN_REMOTE_FILE == code) {
                notifyObserver(RemoteFileDownloadObserver.NOTICE_DOWNLOAD_FILE_OVER_SIZE)
            } else {
                notifyObserver(RemoteFileDownloadObserver.NOTICE_OPEN_FILE_OVER_SIZE)
            }
            return true
        }

        // 检测存储空间是否充足
        val storageState = checkDestStorageSpace(PathFileWrapper(destPath), totalSize)
        if (storageState.first) {
            notifyObserver(RemoteFileDownloadObserver.NOTICE_DOWNLOAD_SPACE_NOT_ENOUGH)
            return true
        }
        return false
    }

    /**
     * 判断文件通道是否被占用
     */
    private fun isFileChannelOccupied(deviceId: String): Boolean {
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        val isOccupied = remoteDeviceApi?.isFileChannelOccupied(deviceId) ?: false
        Log.d(TAG, "isFileChannelOccupied ->device $deviceId is occupied: $isOccupied")
        if (isOccupied) {
            notifyObserver(RemoteFileDownloadObserver.NOTICE_FILE_CHANNEL_OCCUPIED)
        }
        return isOccupied
    }

    /**
     * 判断是否是流量下载, 手机流量并且超过20MB
     */
    private fun checkUseMobileTraffic(): Boolean {
        // 是否已经弹过流量提醒弹窗
        val hasShow = PreferencesUtils.getBoolean(key = CommonConstants.DOWNLOAD_TRAFFIC_REMIND_SHOW, default = false)
        Log.w(TAG, "checkIsMobileTraffic: hasShowDialog $hasShow fileSize:$totalSize")
        if (hasShow) {
            return false
        }
        // 流量消耗预计是否超过20M
        if (totalSize < TRAFFIC_REMIND_SIZE) {
            return false
        }
        // 是否是手机流量
        if (isCellular(mContext)) {
            return true
        }
        return false
    }

    /**
     * 判断通知权限弹窗
     * @return true: 有通知权限，false:没有通知权限
     */
    private fun checkNotifyPermission(): Boolean {
        // 是否已经显示通知权限弹窗
        val hasShow = PreferencesUtils.getBoolean(key = CommonConstants.DOWNLOAD_NOTIFY_REMIND_SHOW, default = false)
        Log.w(TAG, "checkNotifyPermission: hasShowDialog $hasShow")
        if (hasShow) {
            return true
        }
        if (!SdkUtils.isAtLeastT()) { // T以下不需要动态申请通知权限
            Log.w(TAG, "checkNotifyPermission: OS < T")
            return true
        }
        // 判断是否有通知权限
        val hasNotifyPermission = PermissionUtils.hasNotificationPermission(mContext)
        Log.w(TAG, "checkNotifyPermission -> has notify permission:$hasNotifyPermission")
        if (!hasNotifyPermission) {
            val shouldShowRequestUI = lifecycle.shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS)
            Log.w(TAG, "checkNotifyPermission -> showRequestUI:$shouldShowRequestUI")
            val bean = RemoteFileDownloadObserver.NotifyPermissionDialogBean() {
                notifyLockReleased()
            }
            notifyObserver(RemoteFileDownloadObserver.SHOW_CUSTOM_NOTIFY_PERMISSION_DIALOG, bean)
        }
        return hasNotifyPermission
    }

    /**
     * 真正开始下载文件
     */
    private fun reallyDownloadFile(): Boolean {
        notifyObserver(RemoteFileDownloadObserver.DISMISS_DOWNLOAD_MOBILE_TRAFFIC_DIALOG)
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "reallyDownloadFile -> start ....")

        val files = ArrayList<String>()
        val sizes = ArrayList<Long>()
        downloadFiles.forEach {
            files.add(it.originalPath)
            sizes.add(it.mSize)
        }
        if (false && PermissionUtils.hasNotificationPermission(mContext)) { // to
            asyncDownload(files, sizes)
        } else {
            syncDownload(files, sizes)
        }


        Log.w(TAG, "reallyDownloadFile -> end , cost ${System.currentTimeMillis() - startTime} ms")
        return true
    }

    private fun asyncDownload(files: ArrayList<String>, sizes: ArrayList<Long>) {
        Log.d(TAG, "asyncDownload")
        RemoteFileDownloadDispatcher.registerDownloadCallback(downloadCallback)
        RemoteFileDownloadDispatcher.createStartDownloadService(mContext, deviceId, files, sizes, destPath, code, pendingIntent)
    }

    private fun syncDownload(files: ArrayList<String>, sizes: ArrayList<Long>) {
        Log.d(TAG, "syncDownload")
        RemoteFileDownloadDispatcher.registerDownloadCallback(downloadCallback)
        RemoteFileDownloadDispatcher.createStartDownloadService(mContext, deviceId, files, sizes, destPath, code, pendingIntent)
        lockWait()
    }

    /**
     * 更新下载总进度
     * @param state 当前下载的文件
     * @param progress 下载的进度
     */
    private fun updateDownloadTotalProgress(state: Int, progress: Float) {
        Log.d(TAG, "updateDownloadTotalProgress $progress")
        notifyObserver(RemoteFileDownloadObserver.UPDATE_DOWNLOAD_TOTAL_PROGRESS, Pair(state, progress))
    }

    /**
     * 更新下载单个文件进度
     * @param path 当前下载的文件
     * @param status 下载的状态
     */
    private fun updateDownloadingProgress(path: String, status: Int) {
        Log.d(TAG, "updateDownloadingProgress $path->$status")
        notifyObserver(RemoteFileDownloadObserver.UPDATE_DOWNLOAD_SINGLE_PROGRESS, Pair(path, status))
    }

    /**
     * 重置下载状态
     */
    private fun resetDownloadStatus() {
        Log.d(TAG, "resetDownloadStatus")
        updateDownloadTotalProgress(DownloadViewModel.DOWNLOAD_DEFAULT, 0.0f)
    }

    override fun recycle() {
        super.recycle()
        Log.w(TAG, "recycle")
        RemoteFileDownloadDispatcher.unregisterDownloadCallback(downloadCallback)
    }
}