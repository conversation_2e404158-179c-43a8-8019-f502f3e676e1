<?xml version="1.0" encoding="utf-8"?><!--
     Copyright (C) 2006 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical">

    <com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
        android:id="@+id/single_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="32dp"
        android:paddingEnd="32dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/title"
            style="@style/COUIInputTitleStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/coui_edit_text_hint_start_padding"
            android:paddingTop="@dimen/coui_input_preference_title_padding_top"
            android:visibility="gone" />
        <!--        android:paddingTop="@dimen/coui_single_input_title_padding_top" -->

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title">

            <LinearLayout
                android:id="@+id/edittext_container"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_marginVertical="2dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"/>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginLeft="@dimen/dimen_16dp"
                android:layout_gravity="center"
                android:gravity="center_vertical"
                android:id="@+id/name_ext_frame">

                <ImageButton
                    android:id="@+id/input_delete"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingTop="4dp"
                    android:background="@color/color_transparent"
                    android:layout_marginRight="@dimen/dimen_16dp"
                    android:src="@drawable/ic_select_input_delete" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:id="@+id/name_ext"
                    android:textColor="@color/text_black"
                    android:textSize="@dimen/font_size_16" />
                <com.coui.appcompat.rotateview.COUIRotateView
                    android:id="@+id/ext_image"
                    android:clickable="false"
                    android:focusable="false"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:supportRotateType="couiRotateAloneZ"
                    android:src="@drawable/coui_app_expander_close_default"/>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/button_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:visibility="gone"
                android:layout_marginEnd="-6dp"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/checkbox_password"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:button="@drawable/coui_edittext_password_icon"
                    android:background="@null"
                    android:checked="true"
                    android:layout_marginStart="3dp"
                    android:paddingStart="0dp"
                    android:visibility="gone" />

                <CheckBox
                    android:id="@+id/checkbox_custom"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="11dp"
                    android:layout_marginTop="-2dp"
                    android:background="@null"
                    android:checked="true"
                    android:visibility="gone"/>

            </LinearLayout>

            <TextView
                android:id="@+id/input_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:textColor="?attr/couiColorLabelTertiary"
                android:textSize="10sp"
                android:visibility="gone" />
        </LinearLayout>
    </com.coui.appcompat.cardlist.COUICardListSelectedItemLayout>

    <TextView
        android:id="@+id/text_input_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="8dp"
        android:paddingStart="32dp"
        android:paddingEnd="32dp"
        android:textColor="?attr/couiColorError"
        android:textAppearance="@style/couiTextCaption"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/single_card" />
</androidx.constraintlayout.widget.ConstraintLayout>