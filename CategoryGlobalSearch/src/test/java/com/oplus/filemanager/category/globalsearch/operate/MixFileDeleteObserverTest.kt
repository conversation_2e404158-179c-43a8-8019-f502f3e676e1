package com.oplus.filemanager.category.globalsearch.operate

import android.app.Activity
import android.content.DialogInterface
import android.text.SpannableStringBuilder
import android.widget.TextView
import com.coui.appcompat.clickablespan.COUIClickableSpan
import com.filemanager.common.R
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.base.ACTION_CANCELLED
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.oplus.filemanager.interfaze.feedback.IFeedback
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.Shadows
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowDialog
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * MixFileDeleteObserver的单元测试类
 * 用于测试混合文件删除观察者的各种行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class MixFileDeleteObserverTest {

    private lateinit var activity: Activity
    private lateinit var observer: MixFileDeleteObserver
    private lateinit var mockFeedback: IFeedback
    private lateinit var mockClickListener: DialogInterface.OnClickListener

    /**
     * 测试前的准备工作
     * 1. 初始化Activity
     * 2. 创建MixFileDeleteObserver实例
     * 3. 初始化mock对象
     * 4. mock静态方法和工具类
     */
    @Before
    fun setup() {
        // 使用Robolectric创建测试Activity
        activity = Robolectric.buildActivity(Activity::class.java).setup().get()
        // 创建待测试的观察者对象
        observer = MixFileDeleteObserver(activity)
        // 创建mock的反馈接口
        mockFeedback = mockk(relaxed = true)
        // 创建mock的点击监听器
        mockClickListener = mockk(relaxed = true)
        // mock FeatureCompat工具类
        mockkObject(FeatureCompat)
        // mock Injector工具类
        mockkObject(Injector)
        // mock CustomToast的静态方法
        mockkStatic(CustomToast::class)  // 修复点：使用静态mock替代对象mock
        // mock Log工具类
        mockkStatic(Log::class)

        // 设置Injector的mock行为
        every { Injector.injectFactory<IFeedback>() } returns mockFeedback
        // 设置Log的mock行为
        every { Log.d(any(), any<String>()) } just Runs
        every { Log.e(any(), any<String>()) } just Runs
        // 设置CustomToast的mock行为
        every { CustomToast.showShort(any<Int>()) } just Runs  // 修复后不再触发MyApplication初始化
    }

    /**
     * 测试后的清理工作
     * 1. 回收观察者资源
     * 2. 解除所有mock
     */
    @After
    fun tearDown() {
        // 回收观察者资源
        observer.recycle()
        // 解除所有mock对象
        unmockkAll()
    }

    /**
     * 测试onChanged方法处理ACTION_FAILED的情况
     * 验证:
     * 1. 返回false表示不继续处理
     * 2. 显示删除失败的Toast提示
     */
    @Test
    fun `onChanged should handle ACTION_FAILED`() {
        // Given 准备测试数据
        val result = Pair(ACTION_FAILED, Any())

        // When 执行测试方法
        val handled = observer.onChanged(activity, result)

        // Then 验证结果
        assertFalse(handled)
        verify(exactly = 1) { CustomToast.showShort(R.string.delete_docs_failure) }
    }

    /**
     * 测试onChanged方法处理ACTION_DONE和ACTION_CANCELLED的情况
     * 验证:
     * 1. 两种情况下都返回false表示不继续处理
     * 2. 不会显示Toast提示
     */
    @Test
    fun `onChanged should handle ACTION_DONE and ACTION_CANCELLED`() {
        // Given 准备测试数据
        val doneResult = Pair(ACTION_DONE, Any())
        val cancelledResult = Pair(ACTION_CANCELLED, Any())

        // When 执行测试方法
        val doneHandled = observer.onChanged(activity, doneResult)
        val cancelledHandled = observer.onChanged(activity, cancelledResult)

        // Then 验证结果
        assertFalse(doneHandled)
        assertFalse(cancelledHandled)
        verify(exactly = 0) { CustomToast.showShort(any<Int>()) }
    }

    /**
     * 测试onChanged方法处理未知action的情况
     * 验证:
     * 1. 返回false表示不继续处理
     * 2. 不会显示Toast提示
     */
    @Test
    fun `onChanged should return false for unknown action`() {
        // Given 准备测试数据
        val result = Pair(999, Any())

        // When 执行测试方法
        val handled = observer.onChanged(activity, result)

        // Then 验证结果
        assertFalse(handled)
        verify(exactly = 0) { CustomToast.showShort(any<Int>()) }
    }
}