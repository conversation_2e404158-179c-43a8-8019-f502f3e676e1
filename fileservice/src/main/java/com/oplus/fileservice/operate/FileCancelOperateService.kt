/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileCancelOperateService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/13 16:31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/13       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate

import android.app.Service
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.os.RemoteException
import com.filemanager.common.utils.Log
import com.oplus.fileservice.FileServiceManager
import com.filemanager.common.utils.HansFreezeManager

/**
 * Cancel running operation request by requestId.
 * [FileCancelOperateService] must register in file_service.xml.
 */
class FileCancelOperateService : Service() {

    override fun onCreate() {
        Log.d(TAG, "onCreate")
        super.onCreate()
    }

    override fun onBind(intent: Intent?): IBinder? {
        Log.d(TAG, "onBind")
        HansFreezeManager.instance.keepBackgroundRunning()
        intent?.extras?.let { request ->
            request.getString(REQUEST_KEY)?.let { requestId ->
                Log.d(TAG, "onStartCommand -> cancel unique request $requestId")
                FileServiceManager.getInstance().cancelUniqueRequest(applicationContext, requestId) {
                    stopSelf()
                }
            }
        }
        return Messenger(Handler(
            Looper.getMainLooper()
        ) { msg ->
            val data = Bundle()
            data.putString(RESULT, REPLY_RESULT)
            val replyMsg = Message.obtain()
            replyMsg.data = data
            try {
                msg.replyTo.send(replyMsg)
            } catch (e: RemoteException) {
                e.printStackTrace()
            }
            true
        }).binder
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "onStartCommand")
        return START_NOT_STICKY
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Log.d(TAG, "onUnbind")
        HansFreezeManager.instance.cancelFrozenDelay()
        return super.onUnbind(intent)
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
    }

    companion object {
        private const val TAG = "FileCancelOperateService"
        private const val REQUEST_KEY = "requestId"
        private const val RESULT = "result"
        private const val REPLY_RESULT = "true"
    }
}