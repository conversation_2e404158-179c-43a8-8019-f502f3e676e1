/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.setting.utils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/6/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.setting.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityOptions
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.toolbar.COUIActionMenuView
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.Utils
import com.filemanager.setting.ui.SettingActivity
import com.filemanager.setting.ui.about.SettingAboutActivity
import com.filemanager.setting.ui.function.SettingFunctionActivity
import com.filemanager.setting.ui.opensourcelicense.OpenSourceActivity
import com.filemanager.setting.ui.privacy.SettingPrivacyActivity
import com.oplus.flexiblewindow.FlexibleWindowManager

/**
 * 跟手面板浮窗工具类
 * 判断跟手面板显示在左边或右边
 */
object FlexibleWindowUtil {

    private const val TAG = "FlexibleWindowUtil"
    const val FLEXIALE_WINDOW_POSITION = "flexible_Window_Position"
    private const val CLASS_USER_INFO = "UserInformationListActivity"

    private val FLEXIBLE_ACTIVITY_ARRAY = arrayOf<String>(
            SettingActivity::class.java.simpleName,
            SettingAboutActivity::class.java.simpleName,
            SettingFunctionActivity::class.java.simpleName,
            SettingPrivacyActivity::class.java.simpleName,
            OpenSourceActivity::class.java.simpleName,
            CLASS_USER_INFO
    )
    /**
     * 父子级时，有两个COUIActionMenuView时，显示在左边，只有一个时，显示在右边
     * 右到左方向时相反
     */
    @JvmStatic
    fun checkOrientation(decorView: ViewGroup): Int {
        if (FeatureCompat.isSmallScreenPhone) {
            return FlexibleWindowManager.FLEXIBLE_ACTIVITY_POSITION_LEFT
        }
        val actionViewCount = countActionViewOnWindow(decorView)
        Log.d(TAG, "findOrientation actionViewCount:$actionViewCount ")
        val orientation = if (actionViewCount == 1) {
            if (Utils.isRtl()) {
                FlexibleWindowManager.FLEXIBLE_ACTIVITY_POSITION_LEFT
            } else {
                FlexibleWindowManager.FLEXIBLE_ACTIVITY_POSITION_RIGHT
            }
        } else {
            if (Utils.isRtl()) {
                FlexibleWindowManager.FLEXIBLE_ACTIVITY_POSITION_RIGHT
            } else {
                FlexibleWindowManager.FLEXIBLE_ACTIVITY_POSITION_LEFT
            }
        }
        Log.d(TAG, "findOrientation Utils.isRtl():${Utils.isRtl()} orientation:$orientation")
        return orientation
    }

    @JvmStatic
    private fun countActionViewOnWindow(parent: ViewGroup): Int {
        Log.d(TAG, "countActionViewOnWindow parent:$parent  childCount:${parent.childCount}")
        var actionViewCount = 0
        for (i in 0 until parent.childCount) {
            val child = parent.getChildAt(i)
            if (child is COUIActionMenuView) {
                actionViewCount++
            } else if (child is ViewGroup) {
                actionViewCount += countActionViewOnWindow(child)
            }
        }
        return actionViewCount
    }

    @SuppressLint("ClickableViewAccessibility")
    @JvmStatic
    fun setActionCloseFlexibleActivity(decorView: View, activity: Activity) {
        if (!FeatureCompat.isApplicableForFlexibleWindow) {
            Log.d(TAG, "setActionCloseFlexibleActivity not support ,no need set action")
            return
        }
        activity.setFinishOnTouchOutside(false)
        decorView.setOnTouchListener { _, event ->
            if ((event?.action == MotionEvent.ACTION_DOWN) && isOutOfBounds(
                            activity,
                            event
                    )
            ) {
                val activities = MyApplication.activities
                for (itemActivity in activities) {
                    if (itemActivity::class.java.simpleName in FLEXIBLE_ACTIVITY_ARRAY) {
                        itemActivity.finish()
                    }
                }
            }
            false
        }
    }

    /**
     * 判断是否点击在当前窗口的外部区域
     * @param activity
     * @param event
     * @return
     */
    @JvmStatic
    fun isOutOfBounds(activity: Activity, event: MotionEvent): Boolean {
        val x = event.x.toInt()
        val y = event.y.toInt()
        val slop = ViewConfiguration.get(activity).scaledWindowTouchSlop
        val decorView = activity.window.decorView
        return (x < -slop || y < -slop
                || x > decorView.width + slop
                || y > decorView.height + slop)
    }

    @Suppress("ParameterStyleBracesRule")
    @JvmStatic
    fun setBarBackgroundColor(
        toolbar: COUIToolbar,
        appBarLayout: View,
        decorView: View,
        configuration: Configuration,
        activity: AppCompatActivity
    ) {
        if (!FeatureCompat.isApplicableForFlexibleWindow) {
            Log.w(TAG, "unsupport flexible window, return")
            return
        }
        if (COUIDarkModeUtil.isNightMode(activity).not()) {
            Log.w(TAG, "lightMode no need to set, return")
            return
        }
        val isFlexibleActivitySuitable = FlexibleWindowManager.isFlexibleActivitySuitable(configuration)
        toolbar.let {
            activity.setSupportActionBar(toolbar)
            Log.w(TAG, "isFlexibleActivitySuitable: $isFlexibleActivitySuitable")
            if (isFlexibleActivitySuitable) {
                setFlexibleActivityBackgroundColor(
                    toolbar,
                    appBarLayout,
                    decorView,
                    com.support.appcompat.R.attr.couiColorBackgroundElevatedWithCard,
                    activity
                )
            } else {
                setFlexibleActivityBackgroundColor(
                    toolbar,
                    appBarLayout,
                    decorView,
                    com.support.appcompat.R.attr.couiColorBackground,
                    activity
                )
            }
        }
    }

    @JvmStatic
    private fun setFlexibleActivityBackgroundColor(toolbar: COUIToolbar, appBarLayout: View, decorView: View, attr: Int, activity: Activity) {
        toolbar.setBackgroundColor(COUIContextUtil.getAttrColor(activity, attr))
        appBarLayout.setBackgroundColor(COUIContextUtil.getAttrColor(activity, attr))
        decorView.setBackgroundColor(COUIContextUtil.getAttrColor(activity, attr))
    }

    @JvmStatic
    fun startActivityForFlexibleWindow(activity: Activity, intent: Intent) {
        if (FeatureCompat.isApplicableForFlexibleWindow) {
            val exBundle = Bundle()
            val flexibleWindowPosition = PreferencesUtils.getInt(key = FLEXIALE_WINDOW_POSITION)
            exBundle.putInt(FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_POSITION, flexibleWindowPosition)
            exBundle.putBoolean(FlexibleWindowManager.KEY_FLEXIBLE_START_ACTIVITY, true)
            exBundle.putBoolean(FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_DESCENDANT, true)
            val options = ActivityOptions.makeBasic()
            activity.startActivity(intent, FlexibleWindowManager.getInstance().setExtraBundle(options, exBundle))
        } else {
            activity.startActivity(intent)
        }
    }
}