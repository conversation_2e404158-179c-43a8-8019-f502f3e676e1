/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowArchive.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/10/10
 ** Author: <PERSON><PERSON><PERSON><PERSON>n
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/10/10      1.0     create
 ****************************************************************/
package shadow;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.util.List;

import de.innosystec.unrar.Archive;
import de.innosystec.unrar.rarfile.FileHeader;

@Implements(Archive.class)
public class ShadowArchive {
    private static FileHeader mFileHeader;
    private static List<FileHeader> mList;
    private static boolean mIsEncrypted;

    @Implementation
    public FileHeader nextFileHeader() {
        return mFileHeader;
    }

    @Implementation
    public List<FileHeader> getFileHeaders() {
        return mList;
    }

    @Implementation
    public boolean isEncrypted() {
        return mIsEncrypted;
    }

    public static void setmFileHeader(FileHeader fileHeader) {
        mFileHeader = fileHeader;
    }

    public static void setmList(List<FileHeader> list) {
        mList = list;
    }

    public static void setmIsEncrypted(boolean isEncrypted) {
        mIsEncrypted = isEncrypted;
    }
}
