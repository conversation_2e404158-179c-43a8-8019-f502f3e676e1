<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.COUIDividerAppBarLayout
        android:id="@+id/appbar"
        style="@style/CommonAppBarStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:focusable="true"
        android:orientation="vertical"
        app:elevation="@dimen/toolbar_elevation"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/scroll_view">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            style="@style/COUIToolBarInAppBarLayoutStyle"
            app:supportTitleTextAppearance="@style/textAppearanceSecondTitle"
            />

        <View
            android:id="@+id/divider_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_background_height"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="@dimen/common_margin"
            android:layout_marginRight="@dimen/common_margin"
            android:alpha="0"
            android:background="?attr/couiColorDivider"
            android:forceDarkAllowed="false" />

    </com.google.android.material.appbar.COUIDividerAppBarLayout>

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/common_margin"
        android:layout_marginEnd="@dimen/common_margin"
        android:paddingBottom="@dimen/default_navgation_bar_height"
        android:scrollbars="none"
        app:layout_constraintTop_toBottomOf="@+id/appbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="18sp"
                android:layout_marginTop="@dimen/common_margin"
                android:layout_marginEnd="@dimen/common_margin"
                app:layout_constraintBottom_toTopOf="@id/statement" />

            <TextView
                android:id="@+id/statement"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/common_margin"
                android:textSize="12sp" />
        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>