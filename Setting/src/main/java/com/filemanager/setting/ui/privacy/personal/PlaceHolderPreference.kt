/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PlaceHolderPreference
 * * Description: PlaceHolderPreference
 * * Version: 1.0
 * * Date : 2024/08/22
 * * Author:W9060445
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     W9060445         2024/08/22      1.0            create
 ****************************************************************/
package com.filemanager.setting.ui.privacy.personal

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.preference.COUIPreference
import com.filemanager.common.utils.Log
import com.filemanager.setting.R

class PlaceHolderPreference : COUIPreference {

    companion object {
        private const val TAG = "EmptyPreference"
    }

    private var placeHolderEmptyView: View? = null
    private var defaultHeight = 0F
    var systemBarInsetsBottom = 0

    constructor(context: Context?) : this(context, null)

    constructor(context: Context?, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : this(context, attrs, defStyleAttr, 0)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) : super(
        context,
        attrs,
        defStyleAttr,
        defStyleRes
    ) {
        defaultHeight = getContext().resources.getDimension(com.filemanager.common.R.dimen.dimen_32dp)
    }

    @SuppressLint("RestrictedApi")
    override fun onBindViewHolder(holder: PreferenceViewHolder) {
        super.onBindViewHolder(holder)
        placeHolderEmptyView = holder?.findViewById(R.id.place_holder_empty_view)
        updatePaddingBottom()
    }

    private fun updatePaddingBottom() {
        Log.d(TAG, "updatePaddingBottom placeHolderEmptyView is null ${placeHolderEmptyView == null} $systemBarInsetsBottom")
        placeHolderEmptyView?.layoutParams?.run {
            height = (defaultHeight + systemBarInsetsBottom).toInt()
            placeHolderEmptyView?.layoutParams = this
        }
    }
}