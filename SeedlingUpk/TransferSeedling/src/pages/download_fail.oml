<template>
  <card-template entry="notification" category="modular">
    <compact>
      <leading>
        <image level="A*" src="{{fileIcon}}"></image>
        <text level="B*"></text>
      </leading>
      <trailing>
        <image level="A0" src="{{statusIcon}}"></image>
      </trailing>
    </compact>
    <expanded bg-color="#000000" border-color="#0000ff" level="F1*">
      <leading category="mirror">
        <text level="C10" color="#FFFFFF">{{appName}}</text>
        <image level="D9" src="{{appIcon}}"></image>
      </leading>
      <center category="text-highlight">
        <image level="D1" src="{{fileIcon}}" shape-style="round-rectangle"></image>
        <text level="B1*">{{title}}</text>
        <text level="C2">{{subTitle}}</text>
        <image level="D" src="{{statusIcon}}"></image>
      </center>
    </expanded>
    <voice>{{ $t('strings.voiceLabel') }}</voice>
    <!-- A1组件为服务图标，不能删除 -->
    <image level="A1*" src="assets/images/symmetry_logo.png"></image>
  </card-template>
</template>
<style>
</style>
<data>
    {
  "uiData": {
    "appName": "文件管理",
    "appIcon":"@drawable/ic_launcher_filemanager",
    "statusIcon":"@drawable/ic_status_fail",
    "fileIcon":"@drawable/ic_launcher_filemanager",
    "title":"下载完成",
    "subTitle": ""
  },
  "uiEvent": {}
}
</data>