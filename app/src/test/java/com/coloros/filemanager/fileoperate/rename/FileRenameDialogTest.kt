/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.coloros.filemanager.FileRenameDialogTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/5/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.fileoperate.rename

import android.content.Context
import android.view.Gravity
import android.widget.EditText
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.getBottomAlertDialogWindowAnimStyle
import com.filemanager.common.helper.getBottomAlertDialogWindowGravity
import com.filemanager.fileoperate.base.BaseFileNameDialog
import com.filemanager.fileoperate.rename.FileRenameDialog
import com.filemanager.fileoperate.rename.FileRenameObserver
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.io.File

class FileRenameDialogTest {

    @MockK
    lateinit var renameDialog: FileRenameDialog

    @MockK
    lateinit var mContext: Context

    @Before
    fun setup() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `test call back when close dialog`() {
        val dialogMock = mockk<AlertDialog>()
        val editText = mockk<EditText>()
        val listener = mockk<BaseFileNameDialog.OnButtonClickListener>() {
            justRun { onClick(dialogMock) }
        }
        val renameBean = mockk<FileRenameObserver.FileRenameBean> {
            every { resultListener } returns listener
        }
        every { renameDialog.mDialog } returns dialogMock
        every { renameDialog.mRenameBean } returns renameBean
        every { renameDialog.mOppoEditText } returns editText
        justRun { renameDialog.hideSoftInput(editText) }
        every { renameDialog.callbackWhenCloseDialog() } answers { callOriginal() }
        renameDialog.callbackWhenCloseDialog()
        verify { listener.onClick(dialogMock) }
        verify { renameDialog.hideSoftInput(editText) }
    }

    @Test
    fun `test call back when close dialog if mDialog is null`() {
        val editText = mockk<EditText>()
        val listener = mockk<BaseFileNameDialog.OnButtonClickListener>() {
            justRun { onClick(any()) }
        }
        val renameBean = mockk<FileRenameObserver.FileRenameBean> {
            every { resultListener } returns listener
        }
        every { renameDialog.mDialog } answers { nothing }
        every { renameDialog.mRenameBean } returns renameBean
        every { renameDialog.mOppoEditText } returns editText
        justRun { renameDialog.hideSoftInput(editText) }
        every { renameDialog.callbackWhenCloseDialog() } answers { callOriginal() }
        renameDialog.callbackWhenCloseDialog()
        verify(inverse = true) { listener.onClick(any()) }
        verify(inverse = true) { renameDialog.hideSoftInput(editText) }
    }

    @Test
    fun `test call back when close dialog if edit text is null`() {
        val dialogMock = mockk<AlertDialog>()
        val listener = mockk<BaseFileNameDialog.OnButtonClickListener>() {
            justRun { onClick(dialogMock) }
        }
        val renameBean = mockk<FileRenameObserver.FileRenameBean> {
            every { resultListener } returns listener
        }
        every { renameDialog.mDialog } returns dialogMock
        every { renameDialog.mRenameBean } returns renameBean
        every { renameDialog.mOppoEditText } answers { nothing }
        justRun { renameDialog.hideSoftInput(any()) }
        every { renameDialog.callbackWhenCloseDialog() } answers { callOriginal() }
        renameDialog.callbackWhenCloseDialog()
        verify { listener.onClick(dialogMock) }
        verify(inverse = true) { renameDialog.hideSoftInput(any()) }
    }

    @Test
    fun `test call back when close dialog if listener is null`() {
        val dialogMock = mockk<AlertDialog>()
        val editText = mockk<EditText>()
        val listener = mockk<BaseFileNameDialog.OnButtonClickListener>() {
            justRun { onClick(dialogMock) }
        }
        val renameBean = mockk<FileRenameObserver.FileRenameBean> {
            every { resultListener } answers { nothing }
        }
        every { renameDialog.mDialog } returns dialogMock
        every { renameDialog.mRenameBean } returns renameBean
        every { renameDialog.mOppoEditText } returns editText
        justRun { renameDialog.hideSoftInput(editText) }
        every { renameDialog.callbackWhenCloseDialog() } answers { callOriginal() }
        renameDialog.callbackWhenCloseDialog()
        verify(inverse = true) { listener.onClick(dialogMock) }
        verify { renameDialog.hideSoftInput(editText) }
    }

    @Test
    fun `should execute init methods when show`() {
        val dialogMockk = mockk<AlertDialog>()
        mockkStatic(::getBottomAlertDialogWindowGravity)
        mockkStatic(::getBottomAlertDialogWindowAnimStyle)
        every { getBottomAlertDialogWindowGravity(mContext) } returns Gravity.BOTTOM
        every { getBottomAlertDialogWindowAnimStyle(mContext) } returns 1
        val dialogBuilder = mockk<COUIAlertDialogBuilder> {
            every { setTitle(1) } returns this
            every { setNegativeButton(com.filemanager.common.R.string.dialog_cancel, any()) } returns this
            every { setOnCancelListener(any()) } returns this
            every { setPositiveButton(com.filemanager.common.R.string.confirm, null) } returns this
            every { setWindowGravity(Gravity.BOTTOM) } returns this
            every { setWindowAnimStyle(1) } returns this
            every { setBlurBackgroundDrawable(any()) } returns this
            every { getContext() } returns mContext
            every { getBottomAlertDialogWindowGravity(mContext) } returns Gravity.BOTTOM
            every { getBottomAlertDialogWindowAnimStyle(mContext) } returns 1
            every { show(null) } returns dialogMockk
        }
        every { renameDialog.show() } answers { callOriginal() }
        every { renameDialog.getTitleName() } returns 1
        every { renameDialog.constructorDialogBuilder() } returns dialogBuilder
        every { renameDialog.mDialogBuilder } returns dialogBuilder
        justRun { renameDialog.mDialogBuilder = dialogBuilder }
        justRun { renameDialog.mDialog = dialogMockk }
        justRun { renameDialog.initViewValues() }

        renameDialog.show()

        verify { dialogBuilder.setTitle(1) }
        verify { dialogBuilder.setNegativeButton(com.filemanager.common.R.string.dialog_cancel, any()) }
        verify { dialogBuilder.setOnCancelListener(any()) }
        verify { dialogBuilder.setPositiveButton(com.filemanager.common.R.string.confirm, null) }
        verify { dialogBuilder.setWindowGravity(Gravity.BOTTOM) }
        verify { dialogBuilder.setWindowAnimStyle(1) }
        verify { dialogBuilder.setBlurBackgroundDrawable(true) }
        verify { dialogBuilder.show(null) }
        verify { renameDialog.initViewValues() }
        unmockkStatic(::getBottomAlertDialogWindowGravity)
        unmockkStatic(::getBottomAlertDialogWindowAnimStyle)
    }


    @Test
    fun should_return_boolean_when_checkNewFileExists() {
        val fileBean = BaseFileBean()
        fileBean.mData = "test_temp.jpg"
        val renameBean = FileRenameObserver.FileRenameBean(srcOrParentFile = fileBean)
        every { renameDialog.mRenameBean } returns renameBean
        val file = File("test.jpg")
        if (file.exists()) {
            file.delete()
        }
        every { renameDialog.checkNewFileExists(file) }.answers { callOriginal() }

        var exist = renameDialog.checkNewFileExists(file)
        Assert.assertFalse(exist)

        if (!file.exists()) {
            file.createNewFile()
        }

        exist = renameDialog.checkNewFileExists(file)
        Assert.assertTrue(exist)

        fileBean.mData = file.absolutePath
        exist = renameDialog.checkNewFileExists(file)
        Assert.assertFalse(exist)

        if (file.exists()) {
            file.delete()
        }
    }
}