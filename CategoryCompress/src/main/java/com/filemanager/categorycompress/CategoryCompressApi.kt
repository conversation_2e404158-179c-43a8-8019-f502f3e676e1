/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: CategoryCompressApi.kt
 ** Description:  CategoryCompressApi
 ** Version: 1.0
 ** Date: 2021/5/11
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.categorycompress

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.fragment.app.Fragment
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.categorycompress.ui.CategoryCompressActivity
import com.filemanager.categorycompress.ui.CompressParentFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewListFragmentCreator
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.interfaze.categorycompress.ICategoryCompressApi
import com.oplus.filemanager.interfaze.main.IMain

object CategoryCompressApi : ICategoryCompressApi {

    private const val TAG = "CategoryDocumentApi"

    override fun startCategoryCompressActivity(activity: Activity, name: String?) {
        val intent = Intent(activity, CategoryCompressActivity::class.java)
        intent.putExtra(Constants.TITLE, name)
        intent.putExtra(Constants.CATEGORY_TYPE, CategoryHelper.CATEGORY_COMPRESS)
        activity.startActivity(intent)
    }

    override fun startCategoryCompressFragment(activity: Activity, name: String?) {
        Log.d(TAG, "startCategoryCompressFragment")
        val bundle = Bundle()
        bundle.putString(Constants.TITLE, name)
        bundle.putInt(Constants.CATEGORY_TYPE, CategoryHelper.CATEGORY_COMPRESS)
        Injector.injectFactory<IMain>()?.startFragment(activity, CategoryHelper.CATEGORY_COMPRESS, bundle)
    }

    override fun getFragment(activity: Activity): Fragment {
        Log.d(TAG, "getFragment")
        val fragment = PreviewCombineFragment()
        fragment.setPreviewListFragmentCreator(object : IPreviewListFragmentCreator {
            override fun create(): IPreviewListFragment {
                return CompressParentFragment()
            }
        })
        return fragment
    }

    override fun onResumeLoadData(fragment: Fragment) {
        Log.d(TAG, "onResumeLoadData")
        if (fragment is PreviewCombineFragment) {
            fragment.onResumeLoadData()
        }
    }

    override fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater) {
        if (fragment is PreviewCombineFragment) {
            fragment.onCreateOptionsMenu(menu, inflater)
        }
    }

    override fun onMenuItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onMenuItemSelected")
        if (fragment is PreviewCombineFragment) {
            return fragment.onMenuItemSelected(item)
        }
        return false
    }

    override fun pressBack(fragment: Fragment): Boolean {
        Log.d(TAG, "pressBack")
        if (fragment is PreviewCombineFragment) {
            return fragment.pressBack()
        }
        return false
    }

    override fun onNavigationItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onNavigationItemSelected")
        if (fragment is PreviewCombineFragment) {
            return fragment.onNavigationItemSelected(item)
        }
        return false
    }

    override fun setToolbarAndTabListener(
        fragment: Fragment,
        toolbar: COUIToolbar?,
        title: String,
        tabListener: TabActivityListener<MediaFileWrapper>
    ) {
        Log.d(TAG, "setToolbarAndTabListener")
    }

    override fun fromSelectPathResult(fragment: Fragment, requestCode: Int, paths: List<String>?) {
        Log.d(TAG, "fromSelectPathResult")
        if (fragment is PreviewCombineFragment) {
            fragment.fromSelectPathResult(requestCode, paths)
        }
    }

    override fun updateLabels(fragment: Fragment) {
        Log.d(TAG, "updateLabels")
        if (fragment is PreviewCombineFragment) {
            fragment.updatedLabel()
        }
    }

    override fun backToTop(fragment: Fragment) {
        Log.d(TAG, "backToTop")
        if (fragment is PreviewCombineFragment) {
            fragment.backToTop()
        }
    }

    override fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        if (fragment is PreviewCombineFragment) {
            fragment.setIsHalfScreen(isHalfScreen)
        }
    }

    override fun permissionSuccess(fragment: Fragment) {
        Log.d(TAG, "permissionSuccess")
    }

    override fun setCurrentFilePath(fragment: Fragment, path: String?) {
        Log.d(TAG, "setCurrentFilePath")
    }

    override fun getCurrentPath(fragment: Fragment): String {
        Log.d(TAG, "getCurrentPath")
        return ""
    }

    override fun exitSelectionMode(fragment: Fragment) {
        if (fragment is PreviewCombineFragment) {
            fragment.exitSelectionMode()
        }
    }

    override fun onSideNavigationClicked(fragment: Fragment, isOpen: Boolean): Boolean {
        if (fragment is PreviewCombineFragment) {
            return fragment.onSideNavigationClicked(isOpen)
        }
        return false
    }
}