package com.oplus.filemanager.categorydfm.ui

import androidx.lifecycle.MutableLiveData
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.utils.Log
import com.oplus.dropdrag.SelectionTracker
import com.filemanager.common.base.DFMMediaFile
import com.oplus.filemanager.categorydfm.dfm.DFMMediaHelper
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runBlockingTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * DFMFragmentViewModel的单元测试类
 * 使用Robolectric和MockK框架进行测试
 */
@ExperimentalCoroutinesApi
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class DFMFragmentViewModelTest {

    // 测试用的协程调度器
    private val testDispatcher = TestCoroutineDispatcher()
    // 被测ViewModel实例
    private lateinit var viewModel: DFMFragmentViewModel
    // 模拟的父ViewModel
    private lateinit var mockParentViewModel: DFMParentViewModel
    // 模拟的媒体帮助类
    private lateinit var mockMediaHelper: DFMMediaHelper

    /**
     * 测试前的初始化方法
     */
    @Before
    fun setup() {
        // 设置主调度器为测试调度器
        Dispatchers.setMain(testDispatcher)
        // 模拟Log类
        mockkStatic(Log::class)
        // 模拟MyApplication类
        mockkStatic(MyApplication::class)
        // 创建模拟的父ViewModel
        mockParentViewModel = mockk(relaxed = true)
        // 创建模拟的媒体帮助类
        mockMediaHelper = mockk(relaxed = true)
        // 初始化被测ViewModel
        viewModel = DFMFragmentViewModel()
        // 模拟应用上下文
        every { MyApplication.sAppContext } returns mockk(relaxed = true)
    }

    /**
     * 测试后的清理方法
     */
    @After
    fun tearDown() {
        // 重置主调度器
        Dispatchers.resetMain()
        // 清理测试协程
        testDispatcher.cleanupTestCoroutines()
        // 解除所有模拟
        unmockkAll()
    }

    /**
     * 测试getRealFileSize方法
     * 验证获取实际文件数量的功能
     */
    @Test
    fun `test getRealFileSize`() {
        // 准备测试数据
        val mockList = listOf(DFMMediaFile())
        // 设置ViewModel的UI状态
        viewModel.uiState.value = DFMFragmentViewModel.DFMUiModel(
            mockList,
            BaseStateModel(MutableLiveData(KtConstants.LIST_NORMAL_MODE)),
            ArrayList(),
            HashMap(),
            0
        )
        // 验证返回的文件数量是否正确
        assertEquals(1, viewModel.getRealFileSize())
    }

    /**
     * 测试pressBack方法在普通模式下的行为
     * 验证在普通模式下按返回键不会改变模式
     */
    @Test
    fun `test pressBack in normal mode`() {
        // 设置当前为普通模式
        viewModel.selectModeState.mListModel.value = KtConstants.LIST_NORMAL_MODE
        // 验证返回false表示不处理返回键
        assertFalse(viewModel.pressBack())
    }

    /**
     * 测试点击全选按钮时所有文件已选中的情况
     * 验证会取消所有选中状态
     */
    @Test
    fun `test clickToolbarSelectAll when all selected`() {
        // 准备测试数据
        val mockFile = DFMMediaFile().apply { id = 1 }
        // 设置ViewModel的UI状态(已选中状态)
        viewModel.uiState.value = DFMFragmentViewModel.DFMUiModel(
            listOf(mockFile),
            BaseStateModel(MutableLiveData(KtConstants.LIST_NORMAL_MODE)),
            arrayListOf(1),
            hashMapOf(1 to mockFile),
            1
        )
        // 执行全选操作
        viewModel.clickToolbarSelectAll()
        // 验证选中列表已清空
        assertTrue(viewModel.uiState.value?.mSelectedList?.isEmpty() ?: false)
    }

    /**
     * 测试点击全选按钮时不是所有文件都选中的情况
     * 验证会选中所有文件
     */
    @Test
    fun `test clickToolbarSelectAll when not all selected`() {
        // 准备测试数据
        val mockFile = DFMMediaFile().apply { id = 1 }
        // 设置ViewModel的UI状态(未选中状态)
        viewModel.uiState.value = DFMFragmentViewModel.DFMUiModel(
            listOf(mockFile),
            BaseStateModel(MutableLiveData(KtConstants.LIST_NORMAL_MODE)),
            ArrayList(),
            hashMapOf(1 to mockFile),
            1
        )
        // 执行全选操作
        viewModel.clickToolbarSelectAll()
        // 验证所有文件已被选中
        assertEquals(1, viewModel.uiState.value?.mSelectedList?.size)
    }

    /**
     * 测试获取列表浏览模式
     * 验证返回正确的列表布局类型
     */
    @Test
    fun `test getRecyclerViewScanMode for list mode`() {
        // 设置当前为列表模式
        viewModel.browseModeState = KtConstants.SCAN_MODE_LIST
        // 验证返回列表布局类型
        assertEquals(SelectionTracker.LAYOUT_TYPE.LIST, viewModel.getRecyclerViewScanMode())
    }

    /**
     * 测试获取网格浏览模式
     * 验证返回正确的网格布局类型
     */
    @Test
    fun `test getRecyclerViewScanMode for grid mode`() {
        // 设置当前为网格模式(假设2表示网格模式)
        viewModel.browseModeState = 2
        // 验证返回网格布局类型
        assertEquals(SelectionTracker.LAYOUT_TYPE.GRID, viewModel.getRecyclerViewScanMode())
    }

    /**
     * 测试拖放功能是否可用
     * 验证默认情况下拖放功能是启用的
     */
    @Test
    fun `test canDragDrop`() {
        // 验证返回true表示支持拖放
        assertTrue(viewModel.canDragDrop())
    }
}