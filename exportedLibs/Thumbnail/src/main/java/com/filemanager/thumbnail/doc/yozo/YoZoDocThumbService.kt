/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - YoZoDocThumbService.kt
 * Description:
 *     The service connections to obtain thumbnail
 *
 * Version: 1.0
 * Date: 2024-07-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-07-29   1.0    Create this module
 *********************************************************************************/
package com.filemanager.thumbnail.doc.yozo

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.HandlerThread
import com.filemanager.thumbnail.ThumbnailConstant
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.doc.DocThumbnail
import com.filemanager.thumbnail.doc.IYoZoDocThumbnailCallback
import com.filemanager.thumbnail.utils.Log

internal enum class YoZoDocThumbService(private val index: String) {
    SERVICE_1(""),
    SERVICE_2("1"),
    SERVICE_3("2"),
    SERVICE_4("3"),
    SERVICE_5("4"),
    SERVICE_6("5");

    @Volatile
    private var serviceConnection: LoadDocThumbServiceConnection? = null

    fun getServiceIntent(fromPkg: String): Intent =
        Intent("$THUMB_SERVICE_PREFIX$index").apply {
            `package` = ThumbnailManager.YoZoDocConfigs.yoZoPackage
            putExtra(EXTRA_START_FROM, fromPkg)
            putExtra(EXTRA_ACTION_TIME, System.currentTimeMillis())
        }

    @Synchronized
    fun loadThumb(
        context: Context,
        handlerThread: HandlerThread,
        docThumbnail: DocThumbnail,
        snapshotCallback: IYoZoDocThumbnailCallback,
        width: Int = 0
    ) {
        val serCon = LoadDocThumbServiceConnection(context, handlerThread, docThumbnail, snapshotCallback, width)
        if (serCon.doBind(this)) {
            serviceConnection = serCon
        } else {
            snapshotCallback.onLoadFailed(IllegalStateException("$TAG: loadThumb bind YoZo failure"))
        }
    }

    /**
     * 取消对应的永中缩略图服务
     *
     * @param uri 为空时直接取消并返回true，不为空时仅在服务解析的是该uri时才取消并返回true，否则返回false
     * @return true为成功执行取消，false为没有执行取消
     */
    @JvmOverloads
    fun cancel(uri: Uri? = null): Boolean {
        val currConn = serviceConnection ?: return (uri == null)
        if (uri != null && currConn.docUri != uri) {
            return false
        }
        currConn.cancel()
        return true
    }

    @Synchronized
    internal fun onConnectionFinish(connection: LoadDocThumbServiceConnection) {
        if (serviceConnection == connection) {
            serviceConnection = null
        }
    }

    companion object {
        private const val TAG = "YoZoDocThumbService"
        private const val THUMB_SERVICE_PREFIX = "com.yozo.oppo.officethumbnailservice"
        private const val EXTRA_START_FROM = "StartFrom"
        private const val EXTRA_ACTION_TIME = "ActionTime"
        internal const val EXTRA_FILE_PATH = "File_Path"

        private val fileManagerPackages = mutableSetOf(
            "com.coloros.filemanager",
            "com.oneplus.filemanager"
        )

        @JvmStatic
        fun getOccupyThumbServices(
            context: Context,
            occupyServices: Set<Int>
        ): Set<YoZoDocThumbService> {
            val thumbServices = getOccupyThumbServicesImpl(context, occupyServices)
            Log.d(TAG, "getOccupyThumbServices: $thumbServices")
            return thumbServices
        }

        @JvmStatic
        private fun getOccupyThumbServicesImpl(
            context: Context,
            occupyServices: Set<Int>
        ): Set<YoZoDocThumbService> {
            val thumbServices = mutableSetOf<YoZoDocThumbService>()
            occupyServices.forEach { service ->
                service.mapToThumbService { thumbServices.add(it) }
            }
            if (thumbServices.isNotEmpty()) {
                return thumbServices
            }
            val currentApp = context.packageName
            if (fileManagerPackages.contains(currentApp)) {
                return setOf(SERVICE_4, SERVICE_5, SERVICE_6)
            }
            return setOf(SERVICE_1, SERVICE_2, SERVICE_3)
        }

        @Suppress("UtilMustStaticRule")
        private inline fun Int.mapToThumbService(block: (YoZoDocThumbService) -> Unit) {
            when (this) {
                ThumbnailConstant.YO_ZO_THUMB_SERVICE_1 -> SERVICE_1
                ThumbnailConstant.YO_ZO_THUMB_SERVICE_2 -> SERVICE_2
                ThumbnailConstant.YO_ZO_THUMB_SERVICE_3 -> SERVICE_3
                ThumbnailConstant.YO_ZO_THUMB_SERVICE_4 -> SERVICE_4
                ThumbnailConstant.YO_ZO_THUMB_SERVICE_5 -> SERVICE_5
                ThumbnailConstant.YO_ZO_THUMB_SERVICE_6 -> SERVICE_6
                else -> null
            }?.let(block)
        }
    }
}