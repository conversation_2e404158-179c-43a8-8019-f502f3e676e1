/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : base activity supported encrypt operation
 * * Version     : 1.0
 * * Date        : 2020/4/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.encrypt

import android.os.Bundle
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.controller.FileEncryptController

abstract class EncryptActivity : BaseVMActivity() {
    companion object {
        private const val TAG = "EncryptActivity"
    }

    private var mFileEncryptController: FileEncryptController? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mFileEncryptController = FileEncryptFactor.create(this)
    }

    fun getFileEncryptController(): FileEncryptController? {
        return mFileEncryptController
    }
}