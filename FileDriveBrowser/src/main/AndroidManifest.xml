<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <application>
        <activity
            android:name="com.oplus.filemanager.drivebrowser.ui.FileDriveActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:permission="com.oplus.permission.safe.SECURITY"
            android:screenOrientation="behind"
            android:launchMode="singleTop"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.thirddrive" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.oplus.filemanager.drivebrowser.ui.DocumentPreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:permission="com.oplus.permission.safe.SECURITY"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan" />

        <receiver
            android:name="com.oplus.filemanager.drivebrowser.account.AccountLogoutReceiver"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE">
            <intent-filter>
                <action android:name="com.heytap.usercenter.account_logout" />
                <action android:name="com.usercenter.action.receive.account_logout" />
                <action android:name="oppo.intent.action.usercenter.ACCOUNT_LOGOUT" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>
        <service
            android:name="com.oplus.filemanager.drivebrowser.download.DocumentDownloadService"
            android:exported="false"
            android:foregroundServiceType="dataSync" />
    </application>
</manifest>