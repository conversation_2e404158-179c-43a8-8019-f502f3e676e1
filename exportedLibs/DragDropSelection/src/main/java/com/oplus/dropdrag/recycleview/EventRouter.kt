/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - EventRouter.java
 ** Description: Event Router
 ** Version: 1.0
 ** Date : 2020/05/16
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/16    1.0     create
 ****************************************************************/
package com.oplus.dropdrag.recycleview

import android.view.MotionEvent
import androidx.recyclerview.widget.RecyclerView
import java.util.*

internal class ToolHandlerRegistry<T>(private val defaultDelegate: T) {
    companion object {
        private const val NUM_INPUT_TYPES = MotionEvent.TOOL_TYPE_ERASER + 1
    }

    private val handlers: MutableList<T?> = Arrays.asList(null, null, null, null, null)

    init {
        // Initialize all values to null.
        for (i in 0 until NUM_INPUT_TYPES) {
            handlers[i] = null
        }
    }

    /**
     * @param toolType
     * @param delegate the delegate, or null to unregister.
     */
    operator fun set(toolType: Int, delegate: T?) {
        if (toolType >= 0 && toolType <= MotionEvent.TOOL_TYPE_ERASER) {
            handlers[toolType] = delegate
        }
    }

    operator fun get(e: MotionEvent): T {
        val d = handlers[e.getToolType(0)]
        return d ?: defaultDelegate
    }
}

/**
 * GestureRouter is responsible for routing gestures detected by a GestureDetector
 * to registered handlers. The primary function is to divide events by tool-type
 * allowing handlers to cleanly implement tool-type specific policies.
 */
internal class GestureRouter : ExtendGestureListener {
    companion object {
        private const val TAG = "GestureRouter"
    }

    private val delegates: ToolHandlerRegistry<ExtendGestureListener>

    constructor(defaultDelegate: ExtendGestureListener) {
        delegates = ToolHandlerRegistry(defaultDelegate)
    }

    constructor() {
        delegates = ToolHandlerRegistry(ExtendGestureListener())
    }

    /**
     * @param toolType
     * @param delegate the delegate, or null to unregister.
     */
    fun register(toolType: Int, delegate: ExtendGestureListener) {
        delegates[toolType] = delegate
    }

    override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
        return delegates[e].onSingleTapConfirmed(e)
    }

    override fun onDoubleTap(e: MotionEvent): Boolean {
        return delegates[e].onDoubleTap(e)
    }

    override fun onDoubleTapEvent(e: MotionEvent): Boolean {
        return delegates[e].onDoubleTapEvent(e)
    }

    override fun onDown(e: MotionEvent): Boolean {
        return delegates[e].onDown(e)
    }

    override fun onShowPress(e: MotionEvent) {
        delegates[e].onShowPress(e)
    }

    override fun onDragStart(e: MotionEvent): Boolean {
        return delegates[e].onDragStart(e)
    }

    override fun onCancel(e: MotionEvent): Boolean {
        return delegates[e].onCancel(e)
    }

    override fun onUp(e: MotionEvent): Boolean {
        return delegates[e].onUp(e)
    }

    override fun onSingleTapUp(e: MotionEvent): Boolean {
        return delegates[e].onSingleTapUp(e)
    }

    override fun onScroll(
        e1: MotionEvent?,
        e2: MotionEvent,
        distanceX: Float,
        distanceY: Float
    ): Boolean {
        return delegates[e2].onScroll(e1, e2, distanceX, distanceY)
    }

    override fun onLongPress(e: MotionEvent) {
        delegates[e].onLongPress(e)
    }

    override fun onFling(
        e1: MotionEvent?,
        e2: MotionEvent,
        velocityX: Float,
        velocityY: Float
    ): Boolean {
        return delegates[e2].onFling(e1, e2, velocityX, velocityY)
    }
}

internal open class TouchEventRouter<K> @JvmOverloads constructor(
    private val detector: DragSelectionDetector<K>,
    defaultDelegate: RecyclerView.OnItemTouchListener = object : RecyclerView.OnItemTouchListener {
        override fun onInterceptTouchEvent(unused: RecyclerView, e: MotionEvent): Boolean = false
        override fun onTouchEvent(unused: RecyclerView, e: MotionEvent) {}
        override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {}
    }
) : RecyclerView.OnItemTouchListener {

    private val delegates: ToolHandlerRegistry<RecyclerView.OnItemTouchListener>

    init {
        delegates = ToolHandlerRegistry(defaultDelegate)
    }

    /**
     * @param toolType See MotionEvent for details on available types.
     * @param delegate An [OnItemTouchListener] to receive events
     * of `toolType`.
     */
    fun register(toolType: Int, delegate: RecyclerView.OnItemTouchListener) {
        delegates[toolType] = delegate
    }

    override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
        var handled = delegates[e].onInterceptTouchEvent(rv, e)

        // Forward all events to UserInputHandler.
        // This is necessary since UserInputHandler needs to always see the first DOWN event. Or
        // else all future UP events will be tossed.
        handled = handled or detector.onDetectInterceptTouchEvent(e)
        return handled
    }

    override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {
        // Note: make sure event handle by GestureDector first in order to LongPress in front of
        // Slide selection Event
        detector.onDetectTouchEvent(e)

        delegates[e].onTouchEvent(rv, e)
    }

    override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {}
}



