<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="AlwaysShowAction">
    <item
        android:id="@+id/action_cancel"
        android:icon="@drawable/coui_menu_ic_cancel"
        android:title="@string/button_cancel_text"
        app:showAsAction="always" />

    <item
        android:id="@+id/action_select_all"
        android:checkable="true"
        android:title=""
        android:visible="true"
        app:actionViewClass="android.widget.CheckBox"
        app:showAsAction="always" />

</menu>