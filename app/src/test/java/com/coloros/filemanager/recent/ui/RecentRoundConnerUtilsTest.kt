/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentRoundConnerUtilsTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/4/6
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/4/6      1.0        create
 ***********************************************************************/
package com.coloros.filemanager.recent.ui

import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_ALL
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_LEFT_BOTTOM_ONLY
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_LEFT_TOP_BOTTOM_ONLY
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_LEFT_TOP_ONLY
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_NONE
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_RIGHT_BOTTOM_ONLY
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_RIGHT_TOP_BOTTOM_ONLY
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_RIGHT_TOP_ONLY
import com.oplus.filemanager.recent.utils.RecentRoundConnerUtils
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Test

class RecentRoundConnerUtilsTest {

    @Test
    @Suppress("LongMethod")
    fun testCalcRoundConnerType() {
        mockkStatic(RecentRoundConnerUtils::class)

        val gridCount = 4

        // total = 1
        var total = 1
        Assert.assertEquals(
            ROUND_CONNER_ALL,
            RecentRoundConnerUtils.getRecentRoundConnerType(0, total, false, gridCount)
        )

        // total = 2
        total = 2
        Assert.assertEquals(
            ROUND_CONNER_LEFT_TOP_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(0, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_TOP_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(1, total, false, gridCount)
        )

        // total = 3
        total = 3
        Assert.assertEquals(
            ROUND_CONNER_LEFT_TOP_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(0, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(1, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_TOP_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(2, total, false, gridCount)
        )

        // total = 4
        total = 4
        Assert.assertEquals(
            ROUND_CONNER_LEFT_TOP_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(0, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(1, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(2, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_TOP_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(3, total, false, gridCount)
        )

        // total = 5
        total = 5
        Assert.assertEquals(
            ROUND_CONNER_LEFT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(0, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(1, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(2, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(3, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_LEFT_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(4, total, false, gridCount)
        )

        // total = 6
        total = 6
        Assert.assertEquals(
            ROUND_CONNER_LEFT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(0, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(1, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(2, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(3, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_LEFT_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(4, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(5, total, false, gridCount)
        )

        // total = 7
        total = 7
        Assert.assertEquals(
            ROUND_CONNER_LEFT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(0, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(1, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(2, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(3, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_LEFT_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(4, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(5, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(6, total, false, gridCount)
        )

        // total = 8
        total = 8
        Assert.assertEquals(
            ROUND_CONNER_LEFT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(0, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(1, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(2, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(3, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_LEFT_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(4, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(5, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(6, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(7, total, false, gridCount)
        )

        // total = 9 expand = false
        total = 9
        Assert.assertEquals(
            ROUND_CONNER_LEFT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(0, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(1, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(2, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(3, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_LEFT_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(4, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(5, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(6, total, false, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(7, total, false, gridCount)
        )

        // total = 9 expand = true
        total = 9
        Assert.assertEquals(
            ROUND_CONNER_LEFT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(0, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(1, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(2, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(3, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(4, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(5, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(6, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(7, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_LEFT_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(8, total, true, gridCount)
        )

        // total = 10 expand = true
        total = 10
        Assert.assertEquals(
            ROUND_CONNER_LEFT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(0, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(1, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(2, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(3, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(4, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(5, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(6, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(7, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_LEFT_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(8, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(9, total, true, gridCount)
        )

        // total = 12 expand = true
        total = 12
        Assert.assertEquals(
            ROUND_CONNER_LEFT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(0, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(1, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(2, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_TOP_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(3, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(4, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(5, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(6, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(7, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_LEFT_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(8, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(9, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_NONE,
            RecentRoundConnerUtils.getRecentRoundConnerType(10, total, true, gridCount)
        )
        Assert.assertEquals(
            ROUND_CONNER_RIGHT_BOTTOM_ONLY,
            RecentRoundConnerUtils.getRecentRoundConnerType(11, total, true, gridCount)
        )
    }
}