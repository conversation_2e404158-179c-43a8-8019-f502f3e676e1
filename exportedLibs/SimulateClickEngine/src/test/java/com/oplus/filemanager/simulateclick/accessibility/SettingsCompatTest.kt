/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SettingsCompatTest.kt
 * Description:
 * The test cases for SettingsCompat
 *
 * Version: 1.0
 * Date: 2024-05-23
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><EMAIL>    2024-05-23   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.accessibility

import android.provider.Settings
import com.oplus.compat.provider.SettingsNative
import com.oplus.filemanager.simulateclick.engine.utils.SdkUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SettingsCompatTest : Assert() {

    @Before
    fun setUp() {
        mockkStatic(SdkUtils::class, SettingsNative.Secure::class, Settings.Secure::class)
        mockkObject(SettingsCompat.Secure)
        every { SettingsNative.Secure.putString(any(), any()) } returns true
        every { Settings.Secure.putString(any(), any(), any()) } returns true
        every { SettingsCompat.Secure.impl } answers { SettingsCompat.Secure.obtainImpl() }
    }

    @After
    fun tearDown() {
        unmockkStatic(SdkUtils::class, SettingsNative.Secure::class, Settings.Secure::class)
        unmockkObject(SettingsCompat.Secure)
    }

    @Test
    fun `should call legacy when Secure putString if is not at least OS13`() {
        // Given
        every { SdkUtils.isAtLeastOS13() } returns false
        val name = "testName"
        val value = "testValue"

        // When
        SettingsCompat.Secure.putString(mockk(), name, value)

        // Then
        verify { SettingsNative.Secure.putString(name, value) }
        verify(inverse = true) { Settings.Secure.putString(any(), any(), any()) }
    }

    @Test
    fun `should call addon when Secure putString if is at least OS13`() {
        // Given
        every { SdkUtils.isAtLeastOS13() } returns true
        val name = "testName"
        val value = "testValue"

        // When
        SettingsCompat.Secure.putString(mockk(), name, value)

        // Then
        verify { Settings.Secure.putString(any(), any(), any()) }
        verify(inverse = true) { SettingsNative.Secure.putString(name, value) }
    }
}