<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource">

    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="setting_pref_user_agreement"
            android:persistent="true"
            android:title="@string/user_agreement_title_top"/>
        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="setting_pref_privacy_state"
            android:persistent="true"
            android:title="@string/personal_information_protection_policy_title"/>
        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="setting_pref_personal_info"
            android:persistent="true"
            android:title="@string/collect_personal_infomation_title"/>

    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.coui.appcompat.preference.COUIPreferenceCategory android:key="category_ad">
        <com.coui.appcompat.preference.COUISwitchPreference
            android:key="ad_switch"
            android:persistent="false"
            android:summary="@string/personal_service_content"
            android:title="@string/personal_service"
            app:isPreferenceVisible="false"
            />
    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.coui.appcompat.preference.COUIPreference
            android:key="setting_pref_clear_data"
            android:persistent="true"
            android:layout="@layout/coui_preference_red"
            android:title="@string/withdraw_personal_information_protection_policy"/>

    </com.coui.appcompat.preference.COUIPreferenceCategory>
</PreferenceScreen>