/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : WechatIntentProcess.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.process

import android.content.ComponentName
import android.content.Intent
import android.os.BadParcelableException
import android.os.Bundle
import android.util.Log
import com.filemanager.common.constants.ThirdAppConstants.WECHAT_PACKAGE
import java.lang.ClassCastException

class WechatIntentProcess : DefaultIntentProcess() {

    companion object {
        const val TAG = "WechatIntentProcess"

        const val EXTRA_FILE_NAME = "file_name"
        const val EXTRA_FILE_PATH = "file_path"
        const val EXTRA_FILE_EXT = "file_ext"
    }

    override fun getKeyDataBeanFromIntent(inputIntent: Intent): IViewExactProcess.KeyDataBean {
        //后续调试微信的时候需要重写这个方法
        val keyInfo = getKeyInfoFromIntent(inputIntent)
        val uid = getUidFromIntent(keyInfo)
        val fileName = getFileNameFromIntent(keyInfo)
        val result = IViewExactProcess.KeyDataBean()
        result.uid = uid
        result.fileName = fileName
        Log.i(TAG, "getKeyDataBeanFromIntent intent $inputIntent, result $result")
        return result
    }

    private fun getKeyInfoFromIntent(inputIntent: Intent): KeyInfoFromIntent {
        val intentData = inputIntent.data
        val extras = inputIntent.extras
        val sb = StringBuilder()
        val result = KeyInfoFromIntent()
        result.intentComponent = inputIntent.component ?: ComponentName(WECHAT_PACKAGE, "")
        if (extras != null) {
            val extrasKeys = extras.keySet()
            Log.i(TAG, "getUidFromIntent start extra keys ${extrasKeys.toList()}")
            if (extrasKeys != null) {
                for (key in extrasKeys) {
                    try {
                        extractedOtherField(key, extras, sb, result)
                    } catch (e: BadParcelableException) {
                        Log.e(TAG, "getEntityFromIntent error", e)
                    } catch (e: ClassCastException) {
                        Log.e(TAG, "getEntityFromIntent error", e)
                    }
                }
            }
        }
        Log.i(TAG, "getUidFromIntent end itent: $inputIntent intentData $intentData, extras: $extras, sb $sb, result: $result")
        return result
    }

    private fun extractedOtherField(
        key: String?,
        extras: Bundle,
        sb: StringBuilder,
        result: KeyInfoFromIntent
    ) {
        val fileName: String?
        val filePath: String?
        val fileExt: String?
        if (EXTRA_FILE_NAME.contentEquals(key, true)) {
            fileName = extras.getString(key)
            sb.append("key $key, value $fileName\n")
            result.fileName = fileName ?: ""
        } else if (EXTRA_FILE_PATH.contentEquals(key, true)) {
            filePath = extras.getString(key)
            sb.append("key $key, value $filePath\n")
            result.filePath = filePath ?: ""
        } else if (EXTRA_FILE_EXT.contentEquals(key, true)) {
            fileExt = extras.getString(key)
            sb.append("key $key, value ${fileExt}\n")
            result.fileExt = fileExt ?: ""
        }
    }

    private fun getUidFromIntent(keyInfo: KeyInfoFromIntent): String {
        val sb = StringBuilder(keyInfo.intentComponent.toShortString())
        sb.append("_filePath:" + keyInfo.filePath)
        sb.append("_fileName: " + keyInfo.fileName)
        sb.append("_ext: " + keyInfo.fileExt)
        val result = sb.toString()
        Log.i(TAG, "getUidFromIntent no real file, no cloud File, generate uuid $result as uid")
        return result
    }


    private fun getFileNameFromIntent(keyInfo: KeyInfoFromIntent): String {
        var result = keyInfo.fileName
        if (result.isEmpty()) {
            val filePath = keyInfo.filePath
            val fileExt = keyInfo.fileExt
            if (filePath.isNotEmpty() && fileExt.isNotEmpty()) {
                val index = filePath.lastIndexOf("/")
                if (index != -1 && index < filePath.length - 1) {
                    val tmp = filePath.substring(index + 1)
                    if (tmp.contains(fileExt, ignoreCase = true)) {
                        result = getFileNameExceptPresuffix(tmp)
                        Log.i(TAG, "getFileNameFromIntent substring index $index, tmp $tmp")
                    }
                }
            }
        }
        Log.i(TAG, "getFileNameFromIntent $result")
        return result
    }


    /**
     * 这里为什么加上一个特殊的字符串匹配，原因是由于在微信中同一个文件经过多次转发（从一个聊天转发到另一个聊天页面）之后，
     * intent中的文件路径会变为 /data/user/0/com.tencent.mm/MicroMsg/b6f4067484c9ace15403af66a35c771e/attachment/2_22222测试用例有效性总结测试哈利波特测试测试222.docx_1.docx
     * 而真实的文件名称为22222测试用例有效性总结测试哈利波特测试测试222.docx_1.docx ， path之前会加入 “数字_文件名称”的方式。
     * 而在聊天记录搜索页面，或聊天记录页面点击进入预览时，fileName为空，只能依赖filePath获取文件名称。
     * 所以这里做了一个去前缀处理
     *
     * 这里存在一个bug，当本来文件名称就是 数字_文件名时，此时去掉前缀的文件名会跟界面上的文件名不一致。
     */
    private fun getFileNameExceptPresuffix(input: String): String {
        var result: String = input
        runCatching {
            val regex = Regex("(\\d+_)*((\\S|\\s)*\\.\\w*)")
            val matchResult = regex.find(input)
            if (matchResult != null) {
                val suffixString = matchResult.groups[2]?.value
                if (suffixString != null) {
                    result = suffixString
                }
            }
        }.onFailure {
            Log.e(TAG, "getFileNameExceptPresuffix ", it)
        }
        Log.i(TAG, "getFileNameExceptPresuffix input $input, output $result")
        return result
    }


    data class KeyInfoFromIntent(
        var intentComponent: ComponentName = ComponentName("", ""),
        var fileName: String = "",
        var fileExt: String = "",
        ///data/user/0/com.tencent.mm/MicroMsg/b6f4067484c9ace15403af66a35c771e/attachment/1_测试使用的.pptx
        var filePath: String = ""
    )
}