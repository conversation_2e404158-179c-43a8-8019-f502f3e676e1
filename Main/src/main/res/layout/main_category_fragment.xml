<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    android:orientation="vertical"
    android:splitMotionEvents="false">

    <androidx.recyclerview.widget.COUIRecyclerView
        android:id="@+id/main_expandable_recyclerview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingTop="@dimen/recyclerview_default_padding_top"
        android:paddingBottom="@dimen/recent_file_list_bottom_margin" />

    <include layout="@layout/scroll_appbar_layout" />

    <LinearLayout
        android:id="@+id/add_label_fab_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/navigation_tab"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_gravity="bottom|end"
        android:visibility="gone"
        tools:visibility="visible"
        android:clipChildren="false">

        <com.filemanager.common.view.FeedbackFloatingButton
            android:id="@+id/fba_clear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginStart="@dimen/dimen_20dp"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:layout_marginEnd="@dimen/dimen_20dp"
            android:layout_marginBottom="@dimen/dimen_24dp"
            android:clipChildren="false"
            android:elevation="0dp"
            app:fabNeedElevation="false"
            android:forceDarkAllowed="false"
            android:transitionName="shared_element_fab"
            app:mainFloatingButtonSrc="@drawable/icon_floating_btn_clear" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>