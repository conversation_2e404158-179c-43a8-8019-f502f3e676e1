<template>
  <card-template entry="desktop">
      <div class="cardContainer" onclick="clickCard">
          <div class="topContainer">
              <div class="titleContainer">
                  <image class="cardIcon" src="{{ icon }}"></image>
                  <text class="card_title_text title">{{ cardTitle }}</text>
              </div>
              <div class="moreContainer" if="{{ showMore }}">
                  <text class="more_text summary">{{moreText}}</text>
                  <image class="rightArrow" src="{{ moreIcon }}"></image>
              </div>
          </div>
          <div class="dataContainer">
              <div class="itemContainer" for="{{items}}">
                  <div class="item" onclick="clickItem" if="{{ $item.hasData }}">
                    <image class="item_image {{$item.imageType}}" src="{{ $item.img }}"></image>
                    <div class="infoContainer">
                        <div class="fileTextContainer {{titleJustifyType}}" dir="ltr">
                            <text class="item_title title file_name">{{ $item.fileName }}</text>
                            <text class="item_title title file_ext">{{ $item.fileExt }}</text>
                        </div>
                        <text class="item_date summary">{{ $item.date }}</text>
                    </div>
                  </div>
              </div>
          </div>
      </div>
  </card-template>
</template>
<data>
  {
      "uiData": {
            "icon": "",
            "cardTitle": "",
            "isRecent": true,
            "showMore": false,
            "moreText": "{{$t('strings.label_card_more')}}",
            "moreIcon": "{{$r('images.right_arrow')}}",
            "cardType": -1,
            "sourceAppName": "",
            "actionUri": "actionUrl",
            "titleJustifyType": "title_ltr_justify",
            "items": []
      },
      "uiEvent": {
            "clickItem": {
                "type": "deeplink",
                "uri": "nativeapp://com.oplus.filemanager.cardwidget.open.file",
                "params": {
                    "itemInfo": "{{$item.filePath}}",
                    "isFromRecentCardWidget": "{{isRecent}}",
                    "cardType": "{{cardType}}",
                    "sourceAppName": "{{sourceAppName}}"
                }
            },
            "clickCard": {
                "type": "deeplink",
                "uri": "{{actionUri}}",
                "params": {
                    "isFromRecentCardWidget": "{{isRecent}}",
                    "cardType": "{{cardType}}",
                    "sourceAppName": "{{sourceAppName}}"
                }
            }
        }
  }
</data>
<style>
  @media screen and (dark-mode: true) { 
      .title {
          color: rgba(255, 255, 255, 0.9);
      }
      .summary {
          color: rgba(255, 255, 255, 0.54);
      }
  }
  @media screen and (dark-mode: false) { 
      .title {
          color: rgba(0, 0, 0, 0.9);
      }
      .summary {
          color: rgba(0, 0, 0, 0.54);
      }
  }
  @media screen and (dark-mode: false) {
      .cardContainer {
          background: linear-gradient(to bottom right,#EDEFF3,#DFE3E8);
      }
  }
  @media screen and (aspect-ratio: 2*4) and (dark-mode: true) {
      .cardContainer {
          background-image: url("assets/images/background_dark.png");
      }
  }
  @media screen and (aspect-ratio: 4*4) and (dark-mode: true) {
      .cardContainer {
          background-image: url("assets/images/background_dark_large.png");
      }
  }

  .cardContainer {
      position: absolute;
      width: 100%;
      height: 100%;
      padding-top: 16px;
      padding-bottom: 16px;
      padding-left: 16px;
      flex-direction: column;
      background-position:center
  }

  .topContainer {
      width: 100%;
      height: 16px;
      padding-right: 10px;
      flex-direction: row;
      justify-content: space-between;
  }

  .titleContainer {
      width: 50%;
      height: 16px;
      flex-direction: row;
      justify-content: flex-start;
  }

  .moreContainer {
      width: 40%;
      height: 16px;
      flex-direction: row;
      justify-content: flex-end;
  }

  .dataContainer {
      width: 100%;
      padding-top: 16px;
      padding-right: 16px;
      flex-grow: 1;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: flex-start;
      align-content: space-between;
  }

  @media screen and (aspect-ratio: 2*4) {
    .itemContainer {
        width: 48%;
        height: 36px;
    }

    .item_image {
        width: 36px;
        height: 36px;
        flex-shrink: 0;
    }
    
    .image_type_thumbnail {
        padding: 3px;
        object-fit: fill;
    }

    .image_type_icon {
        padding: 0px;
        object-fit: cover;
    }

    .infoContainer {
        flex-shrink: 1;
        height: 32px;
        margin-start: 6px;
        margin-top: 2px;
        margin-bottom: 2px;
        flex-direction: column;
        align-items: flex-start;
    }
  }

  @media screen and (aspect-ratio: 4*4) {
    .itemContainer {
        width: 48%;
        height: 44px;
    }

    .item_image {
        width: 44px;
        height: 44px;
        flex-shrink: 0;
    }
    
    .image_type_thumbnail {
        padding: 4px;
        object-fit: fill;
    }

    .image_type_icon {
        padding: 2px;
        object-fit: cover;
    }

    .infoContainer {
        flex-shrink: 1;
        height: 32px;
        margin-start: 6px;
        margin-top: 6px;
        margin-bottom: 6px;
        flex-direction: column;
        align-items: flex-start;
    }
  }

  .item {
      width: 100%;
      height: 100%;
      flex-direction: row;
      justify-content: flex-start;
  }

  .fileTextContainer {
      max-width: 100%;
      height: 16px;
      flex-direction: row;
  }

  .title_ltr_justify {
    justify-content: flex-start;
  }

  .title_rtl_justify {
    justify-content: flex-end;
  }

  .item_title {
      /* min-width: 16px; */
      height: 16px;
      font-family:OPlusSans 3.5;
      font-size:12.0px;
      max-lines: 1;
      text-overflow: ellipsis;
      line-height: 16px;
      text-decoration:none;
      font-weight: 500;
      font-style: normal;
  }

  .file_name {
      flex-shrink: 1;
  }

  .file_ext {
      flex-shrink: 0;
  }

  .item_date {
      width: 100%;
      height: 16px;
      font-family:OPlusSans 3.5;
      font-size:10.0px;
      max-lines: 1;
      text-overflow: ellipsis;
      line-height: 14.0px;
      text-decoration:none;
      font-weight: 500;
      font-style: normal;
  }

  .more_text {
      height: 16px;
      font-family:OPlusSans 3.0;
      font-size:12.0px;
      max-lines: 1;
      line-height: 16px;
      text-overflow: ellipsis;
      text-decoration:none;
      font-weight: 400;
      font-style: normal
  }
  
  .rightArrow {
      margin-top: 0px;
      width: 16px;
      height: 16px;
      object-fit: contain;
  }

  .cardIcon {
      width: 15px;
      height: 15px;
      object-fit: contain;
  }

  .card_title_text {
      width: 93px;
      height: 16px;
      font-family:OPlusSans 3.5;
      font-size:14px;
      max-lines: 1;
      margin-start: 6px;
      line-height: 16px;
      text-overflow: ellipsis;
      text-decoration:none;
      font-weight: 500;
      font-style: normal;
      text-align: left;
  }
</style>