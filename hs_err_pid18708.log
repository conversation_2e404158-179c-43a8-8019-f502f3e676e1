#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes for Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:110), pid=18708, tid=12268
#
# JRE version: Java(TM) SE Runtime Environment (17.0.8+9) (build 17.0.8+9-LTS-211)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\framework\CloudDriveKit\build\20250901_6278711381230502996.compiler.options

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Sep  1 12:32:52 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 3.750653 seconds (0d 0h 0m 3s)

---------------  T H R E A D  ---------------

Current thread (0x000001d75ec039f0):  JavaThread "main" [_thread_in_vm, id=12268, stack(0x00000009aa900000,0x00000009aaa00000)]

Stack: [0x00000009aa900000,0x00000009aaa00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x677d0a]
V  [jvm.dll+0x7d8c54]
V  [jvm.dll+0x7da3fe]
V  [jvm.dll+0x7daa63]
V  [jvm.dll+0x245c5f]
V  [jvm.dll+0x7d4d5b]
V  [jvm.dll+0x61dcf6]
V  [jvm.dll+0x1c0127]
V  [jvm.dll+0x620650]
V  [jvm.dll+0x61e6b6]
V  [jvm.dll+0x628487]
V  [jvm.dll+0x24e003]
V  [jvm.dll+0x24f55f]
V  [jvm.dll+0x1e254f]
V  [jvm.dll+0x1e1add]
V  [jvm.dll+0x53dde5]
V  [jvm.dll+0x753768]
V  [jvm.dll+0x753854]
V  [jvm.dll+0x40ba4f]
V  [jvm.dll+0x411a69]
C  [java.dll+0x17ec]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
J 516  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (0 bytes) @ 0x000001d76f823e63 [0x000001d76f823da0+0x00000000000000c3]
J 616 c1 java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class; java.base@17.0.8 (43 bytes) @ 0x000001d767d69a5c [0x000001d767d69700+0x000000000000035c]
J 662 c1 java.security.SecureClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class; java.base@17.0.8 (16 bytes) @ 0x000001d767d7fe8c [0x000001d767d7fdc0+0x00000000000000cc]
J 582 c1 jdk.internal.loader.BuiltinClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class; java.base@17.0.8 (121 bytes) @ 0x000001d767d555f4 [0x000001d767d543c0+0x0000000000001234]
J 480 c1 jdk.internal.loader.BuiltinClassLoader.findClassOnClassPathOrNull(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (64 bytes) @ 0x000001d767d1be64 [0x000001d767d1ade0+0x0000000000001084]
J 226 c1 jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class; java.base@17.0.8 (143 bytes) @ 0x000001d767cac3d4 [0x000001d767cab4c0+0x0000000000000f14]
J 343 c1 jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class; java.base@17.0.8 (40 bytes) @ 0x000001d767cdc58c [0x000001d767cdbf60+0x000000000000062c]
J 342 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (7 bytes) @ 0x000001d767cdb04c [0x000001d767cdaf40+0x000000000000010c]
v  ~StubRoutines::call_stub
j  java.lang.Class.getDeclaredConstructors0(Z)[Ljava/lang/reflect/Constructor;+0 java.base@17.0.8
j  java.lang.Class.privateGetDeclaredConstructors(Z)[Ljava/lang/reflect/Constructor;+52 java.base@17.0.8
j  java.lang.Class.getConstructor0([Ljava/lang/Class;I)Ljava/lang/reflect/Constructor;+14 java.base@17.0.8
j  java.lang.Class.getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;+24 java.base@17.0.8
j  org.jetbrains.kotlin.psi.stubs.elements.KtStubElementType.<init>(Ljava/lang/String;Ljava/lang/Class;Ljava/lang/Class;)V+43
j  org.jetbrains.kotlin.psi.stubs.elements.KtUserTypeElementType.<init>(Ljava/lang/String;)V+14
j  org.jetbrains.kotlin.psi.stubs.elements.KtStubElementTypes.<clinit>()V+426
v  ~StubRoutines::call_stub
j  org.jetbrains.kotlin.psi.stubs.elements.KtTokenSets.<clinit>()V+7
v  ~StubRoutines::call_stub
j  org.jetbrains.kotlin.psi.KtFile.<clinit>()V+18
v  ~StubRoutines::call_stub
j  org.jetbrains.kotlin.parsing.KotlinParserDefinition.createFile(Lorg/jetbrains/kotlin/com/intellij/psi/FileViewProvider;)Lorg/jetbrains/kotlin/com/intellij/psi/PsiFile;+6
j  org.jetbrains.kotlin.com.intellij.psi.AbstractFileViewProvider.createFile(Lorg/jetbrains/kotlin/com/intellij/lang/Language;)Lorg/jetbrains/kotlin/com/intellij/psi/PsiFile;+36
j  org.jetbrains.kotlin.com.intellij.psi.AbstractFileViewProvider.createFile(Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFile;Lorg/jetbrains/kotlin/com/intellij/openapi/fileTypes/FileType;Lorg/jetbrains/kotlin/com/intellij/lang/Language;)Lorg/jetbrains/kotlin/com/intellij/psi/PsiFile;+96
j  org.jetbrains.kotlin.com.intellij.psi.AbstractFileViewProvider.createFile(Lorg/jetbrains/kotlin/com/intellij/openapi/project/Project;Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFile;Lorg/jetbrains/kotlin/com/intellij/openapi/fileTypes/FileType;)Lorg/jetbrains/kotlin/com/intellij/psi/PsiFile;+32
j  org.jetbrains.kotlin.com.intellij.psi.SingleRootFileViewProvider.createFile()Lorg/jetbrains/kotlin/com/intellij/psi/PsiFile;+23
j  org.jetbrains.kotlin.com.intellij.psi.SingleRootFileViewProvider.getPsiInner(Lorg/jetbrains/kotlin/com/intellij/lang/Language;)Lorg/jetbrains/kotlin/com/intellij/psi/PsiFile;+29
j  org.jetbrains.kotlin.com.intellij.psi.AbstractFileViewProvider.getPsi(Lorg/jetbrains/kotlin/com/intellij/lang/Language;)Lorg/jetbrains/kotlin/com/intellij/psi/PsiFile;+61
j  org.jetbrains.kotlin.com.intellij.psi.impl.file.impl.FileManagerImpl.findFile(Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFile;)Lorg/jetbrains/kotlin/com/intellij/psi/PsiFile;+78
j  org.jetbrains.kotlin.com.intellij.psi.impl.PsiManagerImpl.findFile(Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFile;)Lorg/jetbrains/kotlin/com/intellij/psi/PsiFile;+17
j  org.jetbrains.kotlin.cli.jvm.compiler.CoreEnvironmentUtilsKt.createSourceFilesFromSourceRoots(Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/com/intellij/openapi/project/Project;Ljava/util/List;Lorg/jetbrains/kotlin/cli/common/messages/CompilerMessageLocation;)Ljava/util/List;+496
j  org.jetbrains.kotlin.cli.jvm.compiler.CoreEnvironmentUtilsKt.createSourceFilesFromSourceRoots$default(Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/com/intellij/openapi/project/Project;Ljava/util/List;Lorg/jetbrains/kotlin/cli/common/messages/CompilerMessageLocation;ILjava/lang/Object;)Ljava/util/List;+14
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinCoreEnvironment.<init>(Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment$ProjectEnvironment;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/cli/jvm/compiler/EnvironmentConfigFiles;)V+129
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinCoreEnvironment.<init>(Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment$ProjectEnvironment;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/cli/jvm/compiler/EnvironmentConfigFiles;Lkotlin/jvm/internal/DefaultConstructorMarker;)V+4
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinCoreEnvironment$Companion.createForProduction(Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/cli/jvm/compiler/EnvironmentConfigFiles;)Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment;+52
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.createCoreEnvironment(Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Ljava/lang/String;)Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment;+19
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(Lorg/jetbrains/kotlin/cli/common/arguments/K2JVMCompilerArguments;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/utils/KotlinPaths;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+943
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(Lorg/jetbrains/kotlin/cli/common/arguments/CommonCompilerArguments;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/utils/KotlinPaths;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+9
j  org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonCompilerArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+220
j  org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonToolArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+7
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonToolArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+76
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Ljava/io/PrintStream;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;[Ljava/lang/String;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+239
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Ljava/io/PrintStream;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;[Ljava/lang/String;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+25
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMainNoExit(Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+39
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMainNoExit$default(Lorg/jetbrains/kotlin/cli/common/CLITool$Companion;Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;ILjava/lang/Object;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+16
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMain(Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;)V+54
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler$Companion.main([Ljava/lang/String;)V+20
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.main([Ljava/lang/String;)V+4
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001d751565140, length=15, elements={
0x000001d75ec039f0, 0x000001d77d5bd000, 0x000001d77d5bde70, 0x000001d77d5d4660,
0x000001d77d5d5010, 0x000001d77d5d59c0, 0x000001d77d5d6370, 0x000001d77d5d7050,
0x000001d77d5d7a40, 0x000001d77d5db450, 0x000001d7510a2040, 0x000001d7510afd50,
0x000001d7511124d0, 0x000001d751112a90, 0x000001d751113050
}

Java Threads: ( => current thread )
=>0x000001d75ec039f0 JavaThread "main" [_thread_in_vm, id=12268, stack(0x00000009aa900000,0x00000009aaa00000)]
  0x000001d77d5bd000 JavaThread "Reference Handler" daemon [_thread_blocked, id=20644, stack(0x00000009ab000000,0x00000009ab100000)]
  0x000001d77d5bde70 JavaThread "Finalizer" daemon [_thread_blocked, id=2080, stack(0x00000009ab100000,0x00000009ab200000)]
  0x000001d77d5d4660 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=10012, stack(0x00000009ab200000,0x00000009ab300000)]
  0x000001d77d5d5010 JavaThread "Attach Listener" daemon [_thread_blocked, id=26344, stack(0x00000009ab300000,0x00000009ab400000)]
  0x000001d77d5d59c0 JavaThread "Service Thread" daemon [_thread_blocked, id=29296, stack(0x00000009ab400000,0x00000009ab500000)]
  0x000001d77d5d6370 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=29508, stack(0x00000009ab500000,0x00000009ab600000)]
  0x000001d77d5d7050 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=22460, stack(0x00000009ab600000,0x00000009ab700000)]
  0x000001d77d5d7a40 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=29456, stack(0x00000009ab700000,0x00000009ab800000)]
  0x000001d77d5db450 JavaThread "Sweeper thread" daemon [_thread_blocked, id=27496, stack(0x00000009ab800000,0x00000009ab900000)]
  0x000001d7510a2040 JavaThread "Notification Thread" daemon [_thread_blocked, id=18592, stack(0x00000009ab900000,0x00000009aba00000)]
  0x000001d7510afd50 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=21740, stack(0x00000009abb00000,0x00000009abc00000)]
  0x000001d7511124d0 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=21320, stack(0x00000009abc00000,0x00000009abd00000)]
  0x000001d751112a90 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=20372, stack(0x00000009abd00000,0x00000009abe00000)]
  0x000001d751113050 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=9680, stack(0x00000009abe00000,0x00000009abf00000)]

Other Threads:
  0x000001d77d5b5fa0 VMThread "VM Thread" [stack: 0x00000009aaf00000,0x00000009ab000000] [id=29408]
  0x000001d77d5a13e0 WatcherThread [stack: 0x00000009aba00000,0x00000009abb00000] [id=25136]
  0x000001d75ecb1360 GCTaskThread "GC Thread#0" [stack: 0x00000009aaa00000,0x00000009aab00000] [id=9352]
  0x000001d751790000 GCTaskThread "GC Thread#1" [stack: 0x00000009abf00000,0x00000009ac000000] [id=19976]
  0x000001d75175c5a0 GCTaskThread "GC Thread#2" [stack: 0x00000009ac000000,0x00000009ac100000] [id=18092]
  0x000001d7516cb820 GCTaskThread "GC Thread#3" [stack: 0x00000009ac100000,0x00000009ac200000] [id=28612]
  0x000001d7516cbad0 GCTaskThread "GC Thread#4" [stack: 0x00000009ac200000,0x00000009ac300000] [id=11648]
  0x000001d7516cbd80 GCTaskThread "GC Thread#5" [stack: 0x00000009ac300000,0x00000009ac400000] [id=24024]
  0x000001d7516cc030 GCTaskThread "GC Thread#6" [stack: 0x00000009ac400000,0x00000009ac500000] [id=5748]
  0x000001d7516cc2e0 GCTaskThread "GC Thread#7" [stack: 0x00000009ac500000,0x00000009ac600000] [id=27072]
  0x000001d7517b0040 GCTaskThread "GC Thread#8" [stack: 0x00000009ac600000,0x00000009ac700000] [id=27728]
  0x000001d7517b02f0 GCTaskThread "GC Thread#9" [stack: 0x00000009ac700000,0x00000009ac800000] [id=20656]
  0x000001d7517cf310 GCTaskThread "GC Thread#10" [stack: 0x00000009ac800000,0x00000009ac900000] [id=7972]
  0x000001d7517d0890 GCTaskThread "GC Thread#11" [stack: 0x00000009ac900000,0x00000009aca00000] [id=20284]
  0x000001d75ecc2060 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000009aab00000,0x00000009aac00000] [id=12296]
  0x000001d75ecc2a70 ConcurrentGCThread "G1 Conc#0" [stack: 0x00000009aac00000,0x00000009aad00000] [id=17792]
  0x000001d77d4eda50 ConcurrentGCThread "G1 Refine#0" [stack: 0x00000009aad00000,0x00000009aae00000] [id=21900]
  0x000001d77d4ee470 ConcurrentGCThread "G1 Service" [stack: 0x00000009aae00000,0x00000009aaf00000] [id=10832]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001d75ebfe020] Metaspace_lock - owner thread: 0x000001d75ec039f0

Heap address: 0x0000000604800000, size: 8120 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001d710000000-0x000001d710bd0000-0x000001d710bd0000), size 12386304, SharedBaseAddress: 0x000001d710000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001d711000000-0x000001d751000000, reserved size: 1073741824
Narrow klass base: 0x000001d710000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32479M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8120M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 520192K, used 27408K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 13965K, committed 14144K, reserved 1114112K
  class space    used 1773K, committed 1856K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%|HS|  |TAMS 0x0000000604800000, 0x0000000604800000| Complete 
|   1|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   2|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|   3|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|   4|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|   5|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|   6|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|   7|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|   8|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|   9|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  10|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  11|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  12|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  13|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
|  14|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  15|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  16|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  17|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  18|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  19|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  20|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  21|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Untracked 
|  22|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|  23|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000, 0x000000060a400000| Untracked 
|  24|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000, 0x000000060a800000| Untracked 
|  25|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000, 0x000000060ac00000| Untracked 
|  26|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000, 0x000000060b000000| Untracked 
|  27|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000, 0x000000060b400000| Untracked 
|  28|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000, 0x000000060b800000| Untracked 
|  29|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000, 0x000000060bc00000| Untracked 
|  30|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|  31|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000, 0x000000060c400000| Untracked 
|  32|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000, 0x000000060c800000| Untracked 
|  33|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000, 0x000000060cc00000| Untracked 
|  34|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000, 0x000000060d000000| Untracked 
|  35|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000, 0x000000060d400000| Untracked 
|  36|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000, 0x000000060d800000| Untracked 
|  37|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000, 0x000000060dc00000| Untracked 
|  38|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|  39|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000, 0x000000060e400000| Untracked 
|  40|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000, 0x000000060e800000| Untracked 
|  41|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000, 0x000000060ec00000| Untracked 
|  42|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000, 0x000000060f000000| Untracked 
|  43|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000, 0x000000060f400000| Untracked 
|  44|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000, 0x000000060f800000| Untracked 
|  45|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000, 0x000000060fc00000| Untracked 
|  46|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|  47|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000, 0x0000000610400000| Untracked 
|  48|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000, 0x0000000610800000| Untracked 
|  49|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000, 0x0000000610c00000| Untracked 
|  50|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000, 0x0000000611000000| Untracked 
|  51|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000, 0x0000000611400000| Untracked 
|  52|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000, 0x0000000611800000| Untracked 
|  53|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000, 0x0000000611c00000| Untracked 
|  54|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
|  55|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000, 0x0000000612400000| Untracked 
|  56|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000, 0x0000000612800000| Untracked 
|  57|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000, 0x0000000612c00000| Untracked 
|  58|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000, 0x0000000613000000| Untracked 
|  59|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000, 0x0000000613400000| Untracked 
|  60|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000, 0x0000000613800000| Untracked 
|  61|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000, 0x0000000613c00000| Untracked 
|  62|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
|  63|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000, 0x0000000614400000| Untracked 
|  64|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000, 0x0000000614800000| Untracked 
|  65|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000, 0x0000000614c00000| Untracked 
|  66|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000, 0x0000000615000000| Untracked 
|  67|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000, 0x0000000615400000| Untracked 
|  68|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000, 0x0000000615800000| Untracked 
|  69|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000, 0x0000000615c00000| Untracked 
|  70|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
|  71|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000, 0x0000000616400000| Untracked 
|  72|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000, 0x0000000616800000| Untracked 
|  73|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000, 0x0000000616c00000| Untracked 
|  74|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000, 0x0000000617000000| Untracked 
|  75|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000, 0x0000000617400000| Untracked 
|  76|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000, 0x0000000617800000| Untracked 
|  77|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000, 0x0000000617c00000| Untracked 
|  78|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
|  79|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000, 0x0000000618400000| Untracked 
|  80|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000, 0x0000000618800000| Untracked 
|  81|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000, 0x0000000618c00000| Untracked 
|  82|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000, 0x0000000619000000| Untracked 
|  83|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000, 0x0000000619400000| Untracked 
|  84|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000, 0x0000000619800000| Untracked 
|  85|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000, 0x0000000619c00000| Untracked 
|  86|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000, 0x000000061a000000| Untracked 
|  87|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000, 0x000000061a400000| Untracked 
|  88|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000, 0x000000061a800000| Untracked 
|  89|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000, 0x000000061ac00000| Untracked 
|  90|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000, 0x000000061b000000| Untracked 
|  91|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000, 0x000000061b400000| Untracked 
|  92|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000, 0x000000061b800000| Untracked 
|  93|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000, 0x000000061bc00000| Untracked 
|  94|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000, 0x000000061c000000| Untracked 
|  95|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000, 0x000000061c400000| Untracked 
|  96|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000, 0x000000061c800000| Untracked 
|  97|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000, 0x000000061cc00000| Untracked 
|  98|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000, 0x000000061d000000| Untracked 
|  99|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000, 0x000000061d400000| Untracked 
| 100|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000, 0x000000061d800000| Untracked 
| 101|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000, 0x000000061dc00000| Untracked 
| 102|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000, 0x000000061e000000| Untracked 
| 103|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000, 0x000000061e400000| Untracked 
| 104|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000, 0x000000061e800000| Untracked 
| 105|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000, 0x000000061ec00000| Untracked 
| 106|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000, 0x000000061f000000| Untracked 
| 107|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000, 0x000000061f400000| Untracked 
| 108|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000, 0x000000061f800000| Untracked 
| 109|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000, 0x000000061fc00000| Untracked 
| 110|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000, 0x0000000620000000| Untracked 
| 111|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000, 0x0000000620400000| Untracked 
| 112|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000, 0x0000000620800000| Untracked 
| 113|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000, 0x0000000620c00000| Untracked 
| 114|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 115|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000, 0x0000000621400000| Untracked 
| 116|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000, 0x0000000621800000| Untracked 
| 117|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000, 0x0000000621c00000| Untracked 
| 118|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000, 0x0000000622000000| Untracked 
| 119|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000, 0x0000000622400000| Untracked 
| 120|0x0000000622800000, 0x0000000622ac4360, 0x0000000622c00000| 69%| S|CS|TAMS 0x0000000622800000, 0x0000000622800000| Complete 
| 121|0x0000000622c00000, 0x0000000622f3dd78, 0x0000000623000000| 81%| E|  |TAMS 0x0000000622c00000, 0x0000000622c00000| Complete 
| 122|0x0000000623000000, 0x0000000623400000, 0x0000000623400000|100%| E|CS|TAMS 0x0000000623000000, 0x0000000623000000| Complete 
| 123|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000, 0x0000000623400000| Complete 
| 124|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000, 0x0000000623800000| Complete 
| 125|0x0000000623c00000, 0x0000000624000000, 0x0000000624000000|100%| E|CS|TAMS 0x0000000623c00000, 0x0000000623c00000| Complete 
| 126|0x0000000624000000, 0x0000000624400000, 0x0000000624400000|100%| E|CS|TAMS 0x0000000624000000, 0x0000000624000000| Complete 

Card table byte_map: [0x000001d778c20000,0x000001d779c00000] _byte_map_base: 0x000001d775bfc000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001d75ecb1970, (CMBitMap*) 0x000001d75ecb19b0
 Prev Bits: [0x000001d700000000, 0x000001d707ee0000)
 Next Bits: [0x000001d707ee0000, 0x000001d70fdc0000)

Polling page: 0x000001d75ea50000

Metaspace:

Usage:
  Non-class:     11.91 MB used.
      Class:      1.73 MB used.
       Both:     13.64 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      12.00 MB ( 19%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.81 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      13.81 MB (  1%) committed. 

Chunk freelists:
   Non-Class:  1.61 MB
       Class:  14.12 MB
        Both:  15.73 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 108.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 221.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 578.
num_chunk_merges: 0.
num_chunk_splits: 453.
num_chunks_enlarged: 403.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=316Kb max_used=316Kb free=118851Kb
 bounds [0x000001d76f800000, 0x000001d76fa70000, 0x000001d776c60000]
CodeHeap 'profiled nmethods': size=119104Kb used=1590Kb max_used=1590Kb free=117513Kb
 bounds [0x000001d767c60000, 0x000001d767ed0000, 0x000001d76f0b0000]
CodeHeap 'non-nmethods': size=7488Kb used=2867Kb max_used=2888Kb free=4620Kb
 bounds [0x000001d76f0b0000, 0x000001d76f390000, 0x000001d76f800000]
 total_blobs=1449 nmethods=970 adapters=389
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 2.885 Thread 0x000001d751113050  959       3       java.lang.String::<init> (14 bytes)
Event: 2.885 Thread 0x000001d751113050 nmethod 959 0x000001d767dec610 code [0x000001d767dec7c0, 0x000001d767dec9d8]
Event: 2.886 Thread 0x000001d751113050  961       1       java.lang.invoke.MethodHandleNatives::isPullModeBSM (2 bytes)
Event: 2.886 Thread 0x000001d751113050 nmethod 961 0x000001d76f84bf10 code [0x000001d76f84c0a0, 0x000001d76f84c158]
Event: 2.888 Thread 0x000001d751113050  962       3       java.lang.invoke.ConstantCallSite::getTarget (20 bytes)
Event: 2.888 Thread 0x000001d751113050 nmethod 962 0x000001d767decb10 code [0x000001d767deccc0, 0x000001d767decee8]
Event: 2.889 Thread 0x000001d77d5d7050  963       4       java.lang.String::regionMatches (150 bytes)
Event: 2.896 Thread 0x000001d77d5d7050 nmethod 963 0x000001d76f84c210 code [0x000001d76f84c3c0, 0x000001d76f84cb98]
Event: 2.896 Thread 0x000001d77d5d7050  964       4       java.util.jar.JarFile::maybeInstantiateVerifier (42 bytes)
Event: 2.903 Thread 0x000001d77d5d7050 nmethod 964 0x000001d76f84d090 code [0x000001d76f84d2c0, 0x000001d76f84da70]
Event: 2.903 Thread 0x000001d77d5d7050  965       4       java.util.Arrays::copyOf (19 bytes)
Event: 2.905 Thread 0x000001d77d5d7050 nmethod 965 0x000001d76f84e010 code [0x000001d76f84e1a0, 0x000001d76f84e478]
Event: 3.097 Thread 0x000001d77d5d7a40  967       3       jdk.internal.reflect.ReflectionFactory::getExecutableSharedParameterTypes (11 bytes)
Event: 3.097 Thread 0x000001d77d5d7a40 nmethod 967 0x000001d767ded010 code [0x000001d767ded1c0, 0x000001d767ded428]
Event: 3.117 Thread 0x000001d77d5d7050  968       4       java.lang.StringBuilder::toString (35 bytes)
Event: 3.121 Thread 0x000001d77d5d7050 nmethod 968 0x000001d76f84e890 code [0x000001d76f84ea20, 0x000001d76f84eeb8]
Event: 3.152 Thread 0x000001d7511124d0  969       3       java.lang.ref.Reference::<init> (7 bytes)
Event: 3.152 Thread 0x000001d7511124d0 nmethod 969 0x000001d767ded590 code [0x000001d767ded740, 0x000001d767ded9f8]
Event: 3.154 Thread 0x000001d77d5d7050  970       4       java.util.concurrent.ConcurrentHashMap::putIfAbsent (8 bytes)
Event: 3.154 Thread 0x000001d77d5d7050 nmethod 970 0x000001d76f84f090 code [0x000001d76f84f220, 0x000001d76f84f2c8]

GC Heap History (2 events):
Event: 1.406 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 520192K, used 24576K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 6754K, committed 6912K, reserved 1114112K
  class space    used 700K, committed 768K, reserved 1048576K
}
Event: 1.659 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 6928K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 6754K, committed 6912K, reserved 1114112K
  class space    used 700K, committed 768K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 2.027 Thread 0x000001d75ec039f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001d76f83fdc8 relative=0x0000000000000128
Event: 2.027 Thread 0x000001d75ec039f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001d76f83fdc8 method=jdk.internal.misc.Unsafe.allocateUninitializedArray(Ljava/lang/Class;I)Ljava/lang/Object; @ 51 c2
Event: 2.027 Thread 0x000001d75ec039f0 DEOPT PACKING pc=0x000001d76f83fdc8 sp=0x00000009aa9fa440
Event: 2.027 Thread 0x000001d75ec039f0 DEOPT UNPACKING pc=0x000001d76f1023a3 sp=0x00000009aa9fa3c8 mode 2
Event: 2.027 Thread 0x000001d75ec039f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001d76f83fdc8 relative=0x0000000000000128
Event: 2.027 Thread 0x000001d75ec039f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001d76f83fdc8 method=jdk.internal.misc.Unsafe.allocateUninitializedArray(Ljava/lang/Class;I)Ljava/lang/Object; @ 51 c2
Event: 2.027 Thread 0x000001d75ec039f0 DEOPT PACKING pc=0x000001d76f83fdc8 sp=0x00000009aa9fa4e0
Event: 2.027 Thread 0x000001d75ec039f0 DEOPT UNPACKING pc=0x000001d76f1023a3 sp=0x00000009aa9fa468 mode 2
Event: 2.027 Thread 0x000001d75ec039f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001d76f83fdc8 relative=0x0000000000000128
Event: 2.027 Thread 0x000001d75ec039f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001d76f83fdc8 method=jdk.internal.misc.Unsafe.allocateUninitializedArray(Ljava/lang/Class;I)Ljava/lang/Object; @ 51 c2
Event: 2.027 Thread 0x000001d75ec039f0 DEOPT PACKING pc=0x000001d76f83fdc8 sp=0x00000009aa9fa000
Event: 2.027 Thread 0x000001d75ec039f0 DEOPT UNPACKING pc=0x000001d76f1023a3 sp=0x00000009aa9f9f88 mode 2
Event: 2.027 Thread 0x000001d75ec039f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001d76f83fdc8 relative=0x0000000000000128
Event: 2.027 Thread 0x000001d75ec039f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001d76f83fdc8 method=jdk.internal.misc.Unsafe.allocateUninitializedArray(Ljava/lang/Class;I)Ljava/lang/Object; @ 51 c2
Event: 2.027 Thread 0x000001d75ec039f0 DEOPT PACKING pc=0x000001d76f83fdc8 sp=0x00000009aa9fa0a0
Event: 2.027 Thread 0x000001d75ec039f0 DEOPT UNPACKING pc=0x000001d76f1023a3 sp=0x00000009aa9fa028 mode 2
Event: 2.478 Thread 0x000001d75ec039f0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001d76f82dc14 relative=0x0000000000000274
Event: 2.478 Thread 0x000001d75ec039f0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d76f82dc14 method=java.lang.String.startsWith(Ljava/lang/String;I)Z @ 1 c2
Event: 2.479 Thread 0x000001d75ec039f0 DEOPT PACKING pc=0x000001d76f82dc14 sp=0x00000009aa9fdb00
Event: 2.479 Thread 0x000001d75ec039f0 DEOPT UNPACKING pc=0x000001d76f1023a3 sp=0x00000009aa9fda80 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 1.693 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006241015a8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x00000006241015a8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.828 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006242bfa28}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006242bfa28) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.906 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d26f20}: 'long java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623d26f20) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.934 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d325b0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623d325b0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.935 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d362a0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623d362a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.935 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d39908}: 'int java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623d39908) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.937 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d48e90}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x0000000623d48e90) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.938 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d50f80}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x0000000623d50f80) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.941 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d60340}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object, java.lang.Object)'> (0x0000000623d60340) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.943 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d6eed0}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int, int)'> (0x0000000623d6eed0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.945 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d7d930}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, long, long)'> (0x0000000623d7d930) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.948 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d8bf48}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int)'> (0x0000000623d8bf48) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.005 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623e857b8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623e857b8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.017 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623eae600}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623eae600) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.037 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623f8f748}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623f8f748) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.228 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623ab0b10}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623ab0b10) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.442 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006236c7ac8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000006236c7ac8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.695 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623382cc8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000623382cc8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.845 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233fcf48}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006233fcf48) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.886 Thread 0x000001d75ec039f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622cb8048}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622cb8048) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (12 events):
Event: 0.424 Executing VM operation: HandshakeAllThreads
Event: 0.424 Executing VM operation: HandshakeAllThreads done
Event: 1.036 Executing VM operation: HandshakeAllThreads
Event: 1.036 Executing VM operation: HandshakeAllThreads done
Event: 1.158 Executing VM operation: HandshakeAllThreads
Event: 1.158 Executing VM operation: HandshakeAllThreads done
Event: 1.158 Executing VM operation: Cleanup
Event: 1.158 Executing VM operation: Cleanup done
Event: 1.406 Executing VM operation: G1CollectForAllocation
Event: 1.659 Executing VM operation: G1CollectForAllocation done
Event: 2.667 Executing VM operation: Cleanup
Event: 2.692 Executing VM operation: Cleanup done

Events (20 events):
Event: 2.188 loading class javax/xml/stream/XMLStreamReader done
Event: 2.230 loading class javax/swing/Icon
Event: 2.231 loading class javax/swing/Icon done
Event: 2.231 loading class sun/invoke/util/VerifyAccess$1
Event: 2.231 loading class sun/invoke/util/VerifyAccess$1 done
Event: 2.261 loading class java/util/concurrent/atomic/AtomicReferenceArray
Event: 2.261 loading class java/util/concurrent/atomic/AtomicReferenceArray done
Event: 2.494 Loaded shared library E:\JAVA\bin\verify.dll
Event: 2.578 loading class java/io/FileFilter
Event: 2.578 loading class java/io/FileFilter done
Event: 2.578 loading class java/nio/channels/WritableByteChannel
Event: 2.579 loading class java/nio/channels/Channel
Event: 2.579 loading class java/nio/channels/Channel done
Event: 2.579 loading class java/nio/channels/WritableByteChannel done
Event: 2.580 loading class jdk/internal/reflect/UnsafeStaticIntegerFieldAccessorImpl
Event: 2.580 loading class jdk/internal/reflect/UnsafeStaticIntegerFieldAccessorImpl done
Event: 2.700 loading class java/util/AbstractList$Itr
Event: 2.700 loading class java/util/AbstractList$Itr done
Event: 2.878 loading class java/util/function/UnaryOperator
Event: 2.878 loading class java/util/function/UnaryOperator done


Dynamic libraries:
0x00007ff708300000 - 0x00007ff708310000 	E:\JAVA\bin\java.exe
0x00007ff827030000 - 0x00007ff827228000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8255d0000 - 0x00007ff825692000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff824d50000 - 0x00007ff825046000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8246c0000 - 0x00007ff8247c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff81fde0000 - 0x00007ff81fdf9000 	E:\JAVA\bin\jli.dll
0x00007ff826e80000 - 0x00007ff826f31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff826340000 - 0x00007ff8263de000 	C:\Windows\System32\msvcrt.dll
0x00007ff825e80000 - 0x00007ff825f1f000 	C:\Windows\System32\sechost.dll
0x00007ff825b10000 - 0x00007ff825c33000 	C:\Windows\System32\RPCRT4.dll
0x00007ff824a20000 - 0x00007ff824a47000 	C:\Windows\System32\bcrypt.dll
0x00007ff825050000 - 0x00007ff8251ed000 	C:\Windows\System32\USER32.dll
0x00007ff8248a0000 - 0x00007ff8248c2000 	C:\Windows\System32\win32u.dll
0x00007ff825da0000 - 0x00007ff825dcb000 	C:\Windows\System32\GDI32.dll
0x00007ff824a50000 - 0x00007ff824b69000 	C:\Windows\System32\gdi32full.dll
0x00007ff824980000 - 0x00007ff824a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff81fdc0000 - 0x00007ff81fddb000 	E:\JAVA\bin\VCRUNTIME140.dll
0x00007ff81dfe0000 - 0x00007ff81e27a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff823400000 - 0x00007ff82340a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8253f0000 - 0x00007ff82541f000 	C:\Windows\System32\IMM32.DLL
0x00007ff81fd50000 - 0x00007ff81fd5c000 	E:\JAVA\bin\vcruntime140_1.dll
0x00007ffffb0c0000 - 0x00007ffffb14e000 	E:\JAVA\bin\msvcp140.dll
0x00007fffe0350000 - 0x00007fffe0f2e000 	E:\JAVA\bin\server\jvm.dll
0x00007ff826f40000 - 0x00007ff826f48000 	C:\Windows\System32\PSAPI.DLL
0x00007fffeb340000 - 0x00007fffeb349000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff826e10000 - 0x00007ff826e7b000 	C:\Windows\System32\WS2_32.dll
0x00007ff81e3c0000 - 0x00007ff81e3e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff823360000 - 0x00007ff823372000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff81c4f0000 - 0x00007ff81c4fa000 	E:\JAVA\bin\jimage.dll
0x00007ff821e10000 - 0x00007ff822011000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff80fef0000 - 0x00007ff80ff24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff824810000 - 0x00007ff824892000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff818d20000 - 0x00007ff818d45000 	E:\JAVA\bin\java.dll
0x00007fffef390000 - 0x00007fffef467000 	E:\JAVA\bin\jsvml.dll
0x00007ff826570000 - 0x00007ff826cde000 	C:\Windows\System32\SHELL32.dll
0x00007ff8222d0000 - 0x00007ff822a74000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff825fd0000 - 0x00007ff826323000 	C:\Windows\System32\combase.dll
0x00007ff8240f0000 - 0x00007ff82411b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff826d40000 - 0x00007ff826e0d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff825f20000 - 0x00007ff825fcd000 	C:\Windows\System32\SHCORE.dll
0x00007ff825d40000 - 0x00007ff825d9b000 	C:\Windows\System32\shlwapi.dll
0x00007ff8245f0000 - 0x00007ff824614000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff811d70000 - 0x00007ff811d89000 	E:\JAVA\bin\net.dll
0x00007ff81ede0000 - 0x00007ff81eeea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff823e50000 - 0x00007ff823eba000 	C:\Windows\system32\mswsock.dll
0x00007ff810fb0000 - 0x00007ff810fc6000 	E:\JAVA\bin\nio.dll
0x00007ff81bc20000 - 0x00007ff81bc38000 	E:\JAVA\bin\zip.dll
0x00007ff81c4d0000 - 0x00007ff81c4e0000 	E:\JAVA\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\JAVA\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;E:\JAVA\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\framework\CloudDriveKit\build\20250901_6278711381230502996.compiler.options
java_class_path (initial): E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.22\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\kotlin-compiler-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.22\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\kotlin-script-runtime-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.22\20e2c5df715f3240c765cfc222530e2796542021\kotlin-daemon-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8514437120                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8514437120                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\JAVA
CLASSPATH=.;E:\JAVA\lib\dt.jar;E:\JAVA\lib\tools.jar;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\dotnet\;E:\git\Git\cmd;D:\Users\W9096060\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\dotnet\;E:\1.8java\lib\dt.jar;E:\1.8java\lib\tools.jar;E:\1.8java\bin;E:\JAVA\bin;E:\JAVA\lib;D:\Users\W9096060\AppData\Local\Microsoft\WindowsApps;
USERNAME=W9096060
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 19:56 hours

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 32479M (2638M free)
TotalPageFile size 47840M (AvailPageFile size 354M)
current process WorkingSet (physical memory assigned to process): 91M, peak: 91M
current process commit charge ("private bytes"): 640M, peak: 640M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211) for windows-amd64 JRE (17.0.8+9-LTS-211), built on Jun 14 2023 10:34:31 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
