/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OperateUpdateTask
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/13 9:35
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/13       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate

import android.content.Context
import com.filemanager.common.utils.Log
import com.oplus.fileservice.impl.FileServiceTask
import com.oplus.fileservice.operate.convert.OperateRequest
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.internal.Operation
import com.oplus.fileservice.operate.internal.convertRequestToOperation

/**
 * Update file operations which performed on remote.
 */
class OperateUpdateTask(
    private val context: Context,
    private val operateRequest: OperateRequest
) : FileServiceTask(operateRequest.requestId!!, true) {

    private var operate: Operation? = null

    override fun runInternal(): OperationResult {
        val operateType = operateRequest.operateType
        Log.d(TAG, "runInternal -> curr thread = ${Thread.currentThread().name} operate type = $operateType")
        operate = convertRequestToOperation(context, operateType)
        return operate?.perform(operateRequest) ?: OperationResult(false)
    }

    override fun cancelInternal(immediately: Boolean) {
        operate?.cancel(immediately)
    }

    companion object {
        private const val TAG = "OperateTask"
    }
}