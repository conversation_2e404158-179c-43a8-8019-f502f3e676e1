<?xml version="1.0" encoding="utf-8"?>
<com.filemanager.common.view.SelectItemLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/search_item_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentTop="true"
    android:forceDarkAllowed="false"
    android:background="@drawable/select_list_item_background_selector"
    android:minHeight="?android:attr/listPreferredItemHeightSmall">

    <com.filemanager.common.view.FileThumbView
        android:id="@+id/file_list_item_icon"
        android:layout_width="@dimen/main_image_width"
        android:layout_height="@dimen/main_image_height"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:forceDarkAllowed="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/file_duration_tv"
        android:layout_width="@dimen/dimen_36dp"
        android:layout_height="wrap_content"
        android:background="@drawable/item_video_duration_view_bg"
        android:gravity="center"
        android:textColor="@color/coui_color_label_on_color"
        android:textSize="@dimen/duration_text_size"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/file_list_item_icon"
        app:layout_constraintEnd_toEndOf="@+id/file_list_item_icon"
        app:layout_constraintStart_toStartOf="@+id/file_list_item_icon" />

    <RelativeLayout
        android:id="@+id/rl_item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/file_list_item_info_margin"
        android:layout_marginTop="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginEnd="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginBottom="@dimen/file_list_right_layout_margin_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/jump_mark"
        app:layout_constraintStart_toEndOf="@+id/file_list_item_icon"
        app:layout_constraintTop_toTopOf="parent">

        <com.filemanager.common.view.TextViewSnippet
            android:id="@+id/file_list_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:maxLines="2"
            android:includeFontPadding="false"
            android:textColor="?attr/couiColorLabelPrimary"
            android:textAppearance="?attr/couiTextHeadlineXS"
            android:textSize="@dimen/file_list_item_title_text_size" />

        <TextView
            android:id="@+id/mark_file_list_item_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/file_list_item_title"
            android:layout_marginTop="@dimen/file_list_item_detail_margin_top"
            android:adjustViewBounds="true"
            android:ellipsize="marquee"
            android:singleLine="true"
            android:includeFontPadding="false"
            android:textColor="?attr/couiColorLabelSecondary"
            android:textSize="@dimen/file_list_item_detail_text_size" />

        <com.filemanager.common.view.TextViewSnippet
            android:id="@+id/another_name_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/file_list_item_title"
            android:ellipsize="end"
            android:paddingTop="@dimen/file_list_item_another_name_padding_top"
            android:singleLine="true"
            android:includeFontPadding="false"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textSize="@dimen/file_list_item_detail_text_size" />

    </RelativeLayout>

    <ImageView
        android:id="@+id/jump_mark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_24dp"
        android:contentDescription="@null"
        android:src="@drawable/coui_btn_next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.coui.appcompat.checkbox.COUICheckBox
        android:id="@+id/listview_scrollchoice_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/sort_item_padding_right_out"
        android:clickable="false"
        android:focusable="false"
        android:background="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</com.filemanager.common.view.SelectItemLayout>