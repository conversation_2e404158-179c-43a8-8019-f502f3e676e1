/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : TencentFileDriveRepository
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/7 19:50
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.repository.tencent

import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.drivebrowser.data.api.TencentFileDriveService
import com.oplus.filemanager.drivebrowser.data.model.AuthorizationPath
import com.oplus.filemanager.drivebrowser.data.model.BaseResponse
import com.oplus.filemanager.drivebrowser.data.model.tencent.AsyncExportFileRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.BaseFileItem
import com.oplus.filemanager.drivebrowser.data.model.tencent.BatchDeleteRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.DeleteFileRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.DeleteFolderRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentFilterRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListsResponse
import com.oplus.filemanager.drivebrowser.data.model.tencent.ExportFileProcessRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.RenameFileRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.RenameFolderRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.SearchListItem
import com.oplus.filemanager.drivebrowser.data.model.tencent.SearchListResp
import com.oplus.filemanager.drivebrowser.data.model.tencent.SearchRequest
import com.oplus.filemanager.drivebrowser.data.repository.DocumentDownloadErrorCode
import com.oplus.filemanager.drivebrowser.domain.model.AuthorizationData
import com.oplus.filemanager.drivebrowser.domain.model.CloudDocumentItem
import com.oplus.filemanager.drivebrowser.domain.model.DocumentPageData
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.drivebrowser.download.DownloadProgressListener
import com.oplus.filemanager.drivebrowser.utils.DownloadController
import com.oplus.filemanager.drivebrowser.utils.ExceptionHandler
import com.oplus.filemanager.drivebrowser.utils.doGetWithRequestInterceptor
import com.oplus.filemanager.drivebrowser.utils.writeFileToDisk
import kotlinx.coroutines.delay
import org.jetbrains.annotations.VisibleForTesting

class TencentFileDriveRepository(
    private val service: TencentFileDriveService
) : FileDriveRepository {

    private val controller = DownloadController()

    override suspend fun checkAuthorizationState(): Boolean {
        val isAuthorization = runCatching {
            service.checkAuthorizationState()
        }.getOrNull()
        Log.d(TAG, "checkAuthorizationState data = ${isAuthorization?.data}")
        return isAuthorization?.data?.auth ?: false
    }

    override suspend fun getAuthUrl(): AuthorizationData? {
        var exception: Throwable? = null
        val authInfo = runCatching {
            service.buildAuthorizationPath()
        }.onFailure {
            exception = it
        }.getOrNull()
        if (authInfo?.data == null) {
            return AuthorizationData(false, "", exception)
        }
        return authInfo.data?.let {
            AuthorizationData(it.auth, it.authUrl, exception)
        }
    }

    override suspend fun saveAuthorizationResult(code: String, state: String): Pair<Boolean, String> {
        Log.d(TAG, "saveAuthorizationResult code = $code, state = $state")
        var error = ""
        val result = runCatching {
            service.callbackAuth(code, state).apply {
                error = this.msg ?: ""
                Log.i(TAG, "saveAuthorizationResult error $error")
            }.code == 0
        }.getOrDefault(false)
        return Pair(result, error)
    }

    override suspend fun cancelAuth(): Pair<Boolean, String> {
        var error = ""
        val result = runCatching {
            val response = service.cancelAuth()
            Log.d(TAG, "cancelAuth -> response = $response")
            error = response.msg ?: ""
            response.code == 0 && response.msg == SUCCESS
        }.onFailure {
            Log.e(TAG, "cancelAuth -> error cause ${it.message}")
            error = it.message ?: ""
        }.getOrDefault(false)
        return Pair(result, error)
    }

    override suspend fun getWebViewAuthUrl(): AuthorizationData? {
        val authInfo = runCatching {
            service.buildWebViewAuthorizationPath(REDIRECT_URL_PATH).data
        }.getOrDefault(AuthorizationPath(false, ""))
        return authInfo?.let {
            AuthorizationData(it.auth, it.authUrl)
        }
    }

    override suspend fun getDocumentLists(
        sortType: String,
        asc: Int,
        start: Int,
        limit: Int,
        folderId: String
    ): BaseResponse<DocumentPageData> {
        val resp = BaseResponse<DocumentPageData>()
        val res = DocumentPageData()
        val request = DocumentFilterRequest(sortType = sortType, asc = asc, start = start, limit = limit, folderID = folderId)
        val data = runCatching {
            service.filterDocumentLists(request).apply {
                resp.codeMsg(this.code, this.msg)
            }.data
        }.getOrDefault(DocumentListsResponse())
        data?.let {
            val result = mutableListOf<CloudDocumentItem>()
            it.list.forEach { documentListItem ->
                val item = convertCloudDocumentItem(documentListItem, folderId)
                result.add(item)
            }
            res.lists = result
            res.isLastPage = it.next == 0
            res.nextPageOffset = it.next
        }
        resp.data = res
        return resp
    }

    override suspend fun renameDocument(fileId: String, title: String): BaseResponse<String> {
        val result = runCatching {
            val request = RenameFileRequest(fileId, title)
            service.renameDocument(request)
        }.getOrDefault(null)
        val respData = BaseResponse<String>()
        result?.let {
            respData.data = it.data as? String
            respData.codeMsg(it.code, it.msg)
        }
        return respData
    }

    override suspend fun renameFolder(folderId: String, title: String): BaseResponse<String> {
        val result = runCatching {
            val request = RenameFolderRequest(folderId, title)
            service.renameFolder(request)
        }.getOrDefault(null)
        val respData = BaseResponse<String>()
        result?.let {
            respData.data = it.data as? String
            respData.codeMsg(it.code, it.msg)
        }
        return respData
    }

    override suspend fun deleteFile(fileId: String, listType: String): Pair<Boolean, String> {
        var error = ""
        val result = runCatching {
            service.deleteFile(DeleteFileRequest(fileId, listType, RECOVERABLE)).apply {
                error = this.msg ?: ""
            }.code == 0
        }.getOrDefault(false)
        return Pair(result, error)
    }

    override suspend fun deleteFolder(folderId: String): Pair<Boolean, String> {
        var error = ""
        val result = runCatching {
            val request = DeleteFolderRequest(folderId)
            service.deleteFolder(request).apply {
                error = this.msg ?: ""
            }.code == 0
        }.getOrDefault(false)
        return Pair(result, error)
    }

    override suspend fun deleteFiles(files: List<DriveFileWrapper>): Pair<Boolean, String> {
        var error = ""
        var result = true
        runCatching {
            files.chunked(MAX_DELETE_SIZE).forEach { sublist ->
                val deleteFileRequest = createDeleteFileRequest(sublist)
                result = service.deleteSelectFiles(deleteFileRequest).apply {
                    error = this.msg ?: ""
                }.code == 0
                if (!result) {
                    return@runCatching
                }
            }
        }.getOrDefault(false)
        return Pair(result, error)
    }

    private fun createDeleteFileRequest(files: List<DriveFileWrapper>): BatchDeleteRequest {
        val deleteFileRequests = files.filter { it.mIsDirectory.not() }.map { DeleteFileRequest(it.id, DeleteFileRequest.TYPE_ORIGIN, RECOVERABLE) }
        val deleteFolderRequests = files.filter { it.mIsDirectory }.map { DeleteFolderRequest(it.id) }
        return BatchDeleteRequest(DELETE_BATCH_TYPE, deleteFileRequests, deleteFolderRequests)
    }

    override suspend fun downloadFile(fileId: String, targetPath: String, exportType: String?, listener: DownloadProgressListener) {
        var failure = false
        val exportResponse = runCatching {
            val request = AsyncExportFileRequest(fileId, null)
            service.asyncExportFile(request).data
        }.onFailure {
            failure = true
            if (ExceptionHandler.isSeverError(it)) {
                listener.onError(DocumentDownloadErrorCode.SAVE_ERROR.description(it.message))
            } else {
                listener.onError(DocumentDownloadErrorCode.UN_SUPPORT_TYPE.description(it.message))
            }
        }.getOrNull()
        val operationId = exportResponse?.operationId
        if (operationId.isNullOrEmpty()) {
            if (!failure) {
                listener.onError(DocumentDownloadErrorCode.NO_PERMISSION_ERROR)
            }
            return
        }

        var url = ""
        runCatching {
            var count = 1
            var repeat = true
            while (count < REPEAT_COUNT && repeat) {
                val request = ExportFileProcessRequest(fileId, operationId)
                val result = runCatching {
                    service.exportFileProcess(request).data
                }.getOrNull()
                result?.let {
                    if (it.progress == MAX_PROGRESS && it.url.isNotEmpty()) {
                        url = it.url
                        repeat = false
                    }
                }
                delay(POLLING_INTERVAL)
                count++
            }
        }.onFailure {
            listener.onError(DocumentDownloadErrorCode.EXPORT_FILE_ERROR.description(it.message))
        }

        if (url.isEmpty()) {
            listener.onError(DocumentDownloadErrorCode.NO_PERMISSION_ERROR)
            return
        }
        download(url, targetPath, listener)
    }

    override fun cancelDownload() {
        controller.pause()
    }

    private fun download(url: String, targetPath: String, listener: DownloadProgressListener) {
        runCatching {
            doGetWithRequestInterceptor(
                appContext,
                url,
                null,
                null,
                CONNECT_TIMEOUT,
                READ_TIMEOUT,
                listener,
                controller
            )?.let { response ->
                Log.d(TAG, "download -> isSuccessful = ${response.isSuccessful}")
                if (response.isSuccessful) {
                    response.use {
                        it.body?.let { responseBody ->
                            val writeResult = writeFileToDisk(responseBody, targetPath)
                            if (!writeResult) {
                                listener.onError(DocumentDownloadErrorCode.SAVE_ERROR)
                            }
                        }
                    }
                }
            }
        }.onFailure {
            listener.onError(DocumentDownloadErrorCode.UNKNOWN_ERROR)
        }
    }

    override suspend fun getEditUrl(fileId: String): String {
        return ""
    }

    override suspend fun searchFile(searchKey: String, page: Int): DocumentPageData {
        val res = DocumentPageData()
        val request = SearchRequest.create(searchKey, page)
        val data = runCatching {
            service.searchFile(request).data
        }.onFailure {
            Log.w(TAG, "searchFile error", it)
        }.getOrDefault(SearchListResp())
        data?.let {
            val result = mutableListOf<CloudDocumentItem>()
            it.list.forEach { listItem ->
                // folderID 默认是空
                val item = convertCloudDocumentItem(listItem, "")
                result.add(item)
            }
            res.lists = result
            res.isLastPage = !it.hasMore
            res.nextPageOffset = it.next
        }
        return res
    }

    private fun convertCloudDocumentItem(it: SearchListItem, folderId: String): CloudDocumentItem {
        return CloudDocumentItem(
            folderId,
            it.id,
            it.title,
            it.type,
            it.url,
            (it.createTime / DriveFileWrapper.TIME_CONVERT_VALUE).toInt(),
            (it.lastModifyTime / DriveFileWrapper.TIME_CONVERT_VALUE).toInt()
        )
    }

    private fun convertCloudDocumentItem(it: DocumentListItem, folderId: String): CloudDocumentItem {
        return CloudDocumentItem(folderId, it.id, it.title, it.type, it.url, it.createTime, it.lastModifyTime)
    }

    override suspend fun isFileAuth(fileId: String): Boolean {
        return runCatching {
            service.metaFile(BaseFileItem(fileId)).code != AUTH_EXPIRED
        }.getOrDefault(true)
    }

    companion object {
        private const val TAG = "TencentFileDriveRepository"
        private const val RECOVERABLE = "1"
        private const val DELETE_BATCH_TYPE = "BATCH_DELETE"
        const val REDIRECT_URL_PATH = "https://owork-test.wanyol.com/mobile-third-doc/login"
        private const val REPEAT_COUNT = 10
        private const val MAX_PROGRESS = 100
        private const val POLLING_INTERVAL = 1000L
        private const val CONNECT_TIMEOUT = 30000L
        private const val READ_TIMEOUT = 30000L
        private const val MAX_DELETE_SIZE = 99

        @VisibleForTesting
        const val SUCCESS = "success"

        //error code
        const val FILE_NOT_EXISTS = 1010102
        const val FOLDER_NOT_EXISTS = 1010202
        const val FOLDER_NAME_CONFLICT = 40518
        const val AUTH_EXPIRED = 12013
    }
}