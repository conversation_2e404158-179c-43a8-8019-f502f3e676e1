/*********************************************************************
 * * Copyright (C), 2010-2023 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AddFileControllerTest
 * * Description : AddFileController unit test
 * * Version     : 1.0
 * * Date        : 2023/6/30
 * * Author      : W9001702
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    W9008821                  2023/6/30     1.0     AddFileDialogFragmentTest unit test
 *  ***********************************************************************/
package com.oplus.filemanager.filelabel.controller

import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import org.junit.Before
import org.junit.Test

class AddFileDialogFragmentTest {
    private lateinit var mAddFileDialogFragment: AddFileDialogFragment
    private lateinit var mAddFilePanelFragment: AddFilePanelFragment
    private lateinit var mAdapter: AddFileListAdapter
    private lateinit var mAddFileClickListener: AddFileClickListener

    @Before
    fun setup() {
        mAddFileDialogFragment = mockk()
        mAddFilePanelFragment = mockk()
        mAdapter = mockk()
        mAddFileClickListener = mockk()
        every { mAddFileDialogFragment.addFilePanelFragment }.returns(mAddFilePanelFragment)
        every { mAddFilePanelFragment.adapter }.returns(mAdapter)
        every { mAddFilePanelFragment.addFileClickListener }.returns(mAddFileClickListener)
    }

    @Test
    fun should_when_showAddFilPanelFragment() {
        every { mAddFileDialogFragment.showAddFilPanelFragment(any(), any(), any()) }.answers { callOriginal() }
        val fragmentManager: FragmentManager = mockk()
        val lifecycle: Lifecycle = mockk()
        val title = ""
        justRun { mAddFilePanelFragment.title = title }
        justRun { mAddFilePanelFragment.lifeCycle = lifecycle }
        justRun {  mAddFilePanelFragment.setAddFileDialogInterface(any()) }
        justRun {  mAddFileDialogFragment.setMainPanelFragment(mAddFilePanelFragment) }
        justRun {  mAddFileDialogFragment.show(fragmentManager, any()) }
        mAddFileDialogFragment.setMainPanelFragment(mAddFilePanelFragment)
        mAddFileDialogFragment.show(fragmentManager, "")

        verify { mAddFileDialogFragment.setMainPanelFragment(any()) }
        verify { mAddFileDialogFragment.show(fragmentManager, any()) }
    }

    @Test
    fun should_when_setData() {
        every { mAddFileDialogFragment.setData(any(), any()) }.answers { callOriginal() }
        justRun { mAddFilePanelFragment.adapter?.setData(any(), any()) }
        justRun { mAddFilePanelFragment.adapter?.notifyDataSetChanged() }
        mAddFilePanelFragment.adapter?.setData(mutableListOf(), mutableListOf())

        verify { mAddFilePanelFragment.adapter?.setData(any(), any()) }
    }

    @Test
    fun should_when_setAddFileClickListener() {
        every { mAddFileDialogFragment.setAddFileClickListener(any()) }.answers { callOriginal() }
        justRun { mAddFilePanelFragment.addFileClickListener = any() }
        val addFileClickListener: AddFileClickListener = mockk()
        mAddFilePanelFragment.addFileClickListener = addFileClickListener

        verify { mAddFilePanelFragment.addFileClickListener = any() }
    }

    @Test
    fun should_when_dismissAddFileDialog() {
        every { mAddFileDialogFragment.dismissAddFileDialog() }.answers { callOriginal() }
        justRun { mAddFileDialogFragment.dialog?.dismiss() }
        mAddFileDialogFragment.dialog?.dismiss()

        verify { mAddFileDialogFragment.dialog?.dismiss() }
    }

    @Test
    fun should_when_showNoFileView() {
        every { mAddFileDialogFragment.showNoFileView() }.answers { callOriginal() }
        justRun { mAddFilePanelFragment.showNoFileView() }
        mAddFilePanelFragment.showNoFileView()

        every { mAddFilePanelFragment.showNoFileView() }
    }

    @Test
    fun should_when_hideNoFileView() {
        every { mAddFileDialogFragment.hideNoFileView() }.answers { callOriginal() }
        justRun { mAddFilePanelFragment.hideNoFileView() }
        mAddFileDialogFragment.hideNoFileView()

        every { mAddFileDialogFragment.hideNoFileView() }
    }
}