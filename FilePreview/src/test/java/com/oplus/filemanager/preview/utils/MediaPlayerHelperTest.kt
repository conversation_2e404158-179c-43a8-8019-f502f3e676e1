package com.oplus.filemanager.preview.utils

import android.app.Application
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Log
import com.oplus.tblplayer.TBLPlayerManager
import com.oplus.tblplayer.config.GlobalsConfig
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.Assert.assertEquals
import org.junit.Rule
import org.junit.rules.TestRule
import org.junit.rules.TestWatcher
import org.junit.runner.Description
import org.robolectric.annotation.Config

/**
 * MediaPlayerHelper 的单元测试类
 * 用于测试 MediaPlayerHelper 工具类的各项功能
 */
@Config(sdk = [28])
class MediaPlayerHelperTest {

    /**
     * 测试观察者规则
     * 用于在测试执行前后执行额外操作
     */
    @get:Rule
    val testWatcher: TestRule = object : TestWatcher() {
        override fun apply(base: org.junit.runners.model.Statement, description: Description): org.junit.runners.model.Statement {
            return super.apply(base, description)
        }
    }

    // 模拟的 Application 对象
    private lateinit var mockApplication: Application
    // 模拟的文件对象
    private lateinit var mockFileBean: BaseFileBean

    /**
     * 测试前置方法
     * 在每个测试方法执行前初始化模拟对象和静态mock
     */
    @Before
    fun setUp() {
        // 创建模拟的 Application 对象
        mockApplication = mockk(relaxed = true)
        // 创建模拟的文件对象
        mockFileBean = mockk()
        // 模拟 FileTypeUtils 类的静态方法
        mockkStatic(FileTypeUtils::class)
        // 模拟 Log 类的静态方法
        mockkStatic(Log::class)
        // 模拟 TBLPlayerManager 类的静态方法
        mockkStatic(TBLPlayerManager::class)
        // 初始化全局配置，确保测试环境一致
        MediaPlayerHelper.initGlobalConfigs(mockApplication)
    }

    /**
     * 测试后置方法
     * 在每个测试方法执行后清理模拟对象和静态mock
     */
    @After
    fun tearDown() {
        // 清除所有mock
        clearAllMocks()
        // 取消所有静态mock
        unmockkAll()
        // 重新模拟静态类以确保测试隔离
        mockkStatic(FileTypeUtils::class)
        mockkStatic(Log::class)
        mockkStatic(TBLPlayerManager::class)
        // 再次清除所有mock确保没有状态泄漏
        clearAllMocks()
    }

    /**
     * 测试 getPrepareTimeout 方法(使用文件对象作为参数)
     * 验证对于不同文件类型返回正确的准备超时时间
     */
    @Test
    fun `test getPrepareTimeout with file bean should return correct timeout`() {
        // Given - 准备测试数据
        val testPath = "test.mp2"
        // 设置模拟文件对象的返回路径
        every { mockFileBean.mData } returns testPath
        // 设置模拟文件扩展名获取方法返回mp2
        every { FileTypeUtils.getExtension(testPath) } returns "mp2"

        // When - 执行测试方法
        val result = MediaPlayerHelper.getPrepareTimeout(mockFileBean)

        // Then - 验证结果
        assertEquals(500L, result)
    }

    /**
     * 测试 getPrepareTimeout 方法(使用文件扩展名作为参数)
     * 验证对于不同文件扩展名返回正确的准备超时时间
     */
    @Test
    fun `test getPrepareTimeout with extension should return correct timeout`() {
        // 定义测试用例：扩展名和预期结果
        val testCases = listOf(
            "mp2" to 500L,
            "ape" to 500L,
            "wmv" to 500L,
            "wma" to 500L,
            "avi" to 500L,
            "mov" to 500L,
            "flv" to 500L,
            "mp4" to 5000L,
            null to 5000L
        )

        // 遍历所有测试用例
        testCases.forEach { (extension, expected) ->
            // 每次迭代前清除mock确保测试隔离
            clearAllMocks()
            mockkStatic(FileTypeUtils::class)
            // 验证结果是否符合预期
            assertEquals(expected, MediaPlayerHelper.getPrepareTimeout(extension))
        }
    }

    /**
     * 测试 getVideoSizeDecodeTimeout 方法
     * 验证视频尺寸解码超时时间是否总是返回默认值
     */
    @Test
    fun `test getVideoSizeDecodeTimeout should always return default timeout`() {
        // Given - 准备测试数据
        val testPath = "test.mp4"
        // 设置模拟文件对象的返回路径
        every { mockFileBean.mData } returns testPath
        // 设置模拟文件扩展名获取方法返回mp4
        every { FileTypeUtils.getExtension(testPath) } returns "mp4"

        // When - 执行测试方法
        val result = MediaPlayerHelper.getVideoSizeDecodeTimeout(mockFileBean)

        // Then - 验证结果是否为默认值1000毫秒
        assertEquals(1000L, result)
    }
}