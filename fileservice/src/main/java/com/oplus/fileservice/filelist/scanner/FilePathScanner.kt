/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FilePathLoader
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/20 20:39
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/20       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.scanner

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import java.io.File

class FilePathScanner(
    loadPath: String,
    sortOrder: Int = SortHelper.FILE_NAME_ORDER,
    pageNo: Int,
    pageSize: Int,
    isNeedThumbnail: Boolean = true
) : BasePathScanner(pageNo, pageSize, isNeedThumbnail) {
    companion object {
        const val TAG = "FilePathLoader"
    }

    private var mLoadPath: String = loadPath
    private var mLastSortOrder: Int = SortHelper.FILE_NAME_ORDER
    private var mSortOrder = sortOrder

    override fun createFromPath(
        volume: String,
        parentPath: String,
        path: String
    ): List<BaseFileBean>? {
        val file = PathFileWrapper(parentPath.plus(File.separator).plus(path))
        return arrayListOf(file)
    }

    override fun getPath(): Array<String> {
        return Array<String>(1) { mLoadPath }
    }

    override fun getVolume(): List<String>? {
        return null
    }

    override fun getFilterList(): List<Int>? {
        return null
    }

    override fun preHandleResultBackground(list: List<BaseFileBean>): List<BaseFileBean> {
        Injector.injectFactory<IDocumentExtensionType>()?.sortFileIgnoreHeadLabel(list, mSortOrder, mLastSortOrder, true)
        mLastSortOrder = mSortOrder
        return list
    }
}