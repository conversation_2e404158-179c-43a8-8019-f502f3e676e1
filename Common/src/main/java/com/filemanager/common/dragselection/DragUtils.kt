/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - DragUtils.kt
 ** Description: DragUtils
 ** Version: 1.0
 ** Date : 2020/05/29
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/29    1.0     create
 ****************************************************************/

package com.filemanager.common.dragselection

import android.annotation.SuppressLint
import android.app.Activity
import android.content.ClipData
import android.content.ClipDescription
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PixelFormat
import android.graphics.RectF
import android.graphics.Shader.TileMode
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.media.ThumbnailUtils
import android.net.Uri
import android.os.Bundle
import android.view.DragEvent
import android.view.View
import android.view.View.GONE
import android.view.View.MeasureSpec
import android.view.View.VISIBLE
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.forEach
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_GRID
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_GRID_IMG
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_RECENT_IMG
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_RECENT_SINGLE_IMG
import com.filemanager.common.dragselection.action.DropDispatchAction
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.helper.CategoryHelper.CATEGORY_FILE_REMOTE_MAC
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.view.GridThumbView
import com.oplus.dropdrag.SelectionTracker
import com.oplus.view.OplusView
import java.lang.ref.WeakReference
import java.util.Date
import kotlin.collections.ArrayList
import androidx.core.graphics.createBitmap
import com.filemanager.common.dragselection.NewFileDragDropShadow.Companion.POSITION_LIST_SIZE

object DragUtils {

    private const val TAG = "DragUtils"
    private const val BASIC_MAGNIFICATION = 1.05f
    const val SELECTED_ITEMVIEW_ALPHA = 0.3f
    const val CORNER_MARK_STATE_INVALID = -2
    const val ALPHA_VALUE_MAX = 255
    @SuppressLint("StaticFieldLeak")
    private var shadowBuilder: NewFileDragDropShadow? = null
    private var mWeakView: WeakReference<View>? = null
    private var selectedFileBean: ArrayList<out BaseFileBean>? = null
    private val cornerMarkBroadcastIntent = Intent(KtConstants.COM_OPLUS_VIEW_DRAGBADGE)
    private var dragOutSide = false
    //拖拽来源
    var dragTag: String? = null
    //拖起时的路径，当路径变化后需要可以拖拽
    var dragStartPath: String? = null
    var isDragging = false
    var lastCornerMarkStateCode: Int = -1
    //拖拽的文件是否包含Android/data目录文件
    var hasAndroidDataFile = false
    var isWholeShadowComplete: Boolean = true
    var isSingleShadowComplete: Boolean = true


    @JvmStatic
    fun createClipData(description: ClipDescription, clipItems: ArrayList<ClipData.Item>): ClipData {
        if (clipItems.isEmpty()) {
            return ClipData(description, ClipData.Item(Uri.parse("")))
        }
        val clip = ClipData(description, clipItems[0])
        for (i in 1 until clipItems.size) {
            clip.addItem(clipItems[i])
        }
        return clip
    }

    fun getMimeTypeByPath(file: BaseFileBean, mimeTypeList: ArrayList<String>): String {
        var mimeType = MimeTypeHelper.getMimeTypeFromPath(file.mData)
        if (mimeType.isNullOrEmpty()) {
            mimeType = "application/*"
        }
        mimeTypeList.add(mimeType)
        return mimeType
    }

    fun getMimeTypeByType(type: Int): String {
        return MimeTypeHelper.getMimeTypeByFileType(type)
    }

    @JvmStatic
    fun formatFileDetails(context: Context, fileBean: BaseFileBean, isList: Boolean): String {
        val fileCount = JavaFileHelper.listFilesCount(fileBean, excludeHideFile = HiddenFileHelper.isNeedShowHiddenFile().not())
        val counts = context.resources.getQuantityString(R.plurals.text_x_items, fileCount, fileCount)
        if (!isList) {
            return counts
        }
        val itemDate = Date(fileBean.mDateModified)
        val date = Utils.getDashDate(itemDate)
        return Utils.formatDetailDrag(counts, date)
    }


    fun getClassifyDrawable(context: Context, fileBean: BaseFileBean): Drawable? {
        return if (fileBean.mLocalType == MimeTypeHelper.COMPRESSED_TYPE) {
            val compressedType = MimeTypeHelper.getCompressedTypeByPath(fileBean.mData)
            KtThumbnailHelper.getClassifyDrawable(context, compressedType)
        } else {
            KtThumbnailHelper.getClassifyDrawable(context, fileBean.mLocalType)
        }
    }

    /**
     * @param source The Bitmap to reCrop.
     * @param radius The radius of the final Bitmap.
     * @return The reCrop Bitmap (will be recycled if recycled is not null).
     */
    @JvmStatic
    fun roundCorner(source: Bitmap, radius: Float): Bitmap {
        val result = Bitmap.createBitmap(source.width, source.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        val paint = Paint()
        paint.shader = BitmapShader(source, TileMode.CLAMP, TileMode.CLAMP)
        paint.isAntiAlias = true
        val rectF = RectF(0f, 0f, source.width.toFloat(), source.height.toFloat())
        canvas.drawRoundRect(rectF, radius, radius, paint)
        if (!source.isRecycled) {
            source.recycle()
        }
        return result
    }

    @JvmStatic
    fun getDragDetails(context: Context, fileBean: BaseFileBean?, isList: Boolean): String? {
        if (fileBean == null) {
            return null
        }

        return when (fileBean.mLocalType) {
            MimeTypeHelper.DIRECTORY_TYPE ->
                formatFileDetails(context, fileBean, isList)

            else -> {
                val fileSize = KtUtils.formatSize(fileBean)
                if (isList) {
                    val lastModified = fileBean.mDateModified
                    val dateAndTime = Utils.getDateFormat(context, lastModified)
                    Utils.formatDetail(context, fileSize, dateAndTime).toString()
                } else {
                    fileSize
                }
            }
        }
    }

    @JvmStatic
    fun getDragImgDrawable(
        context: Context,
        drawable: Drawable?,
        code: Int
    ): Drawable? {
        if (drawable == null) {
            return null
        }

        var iconWidth = 0
        var iconHeight = 0
        var radius = 0
        when (code) {
            STATUS_GRID_IMG, STATUS_RECENT_IMG -> {
                radius = context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_layout_background_radius)
                iconWidth =
                    context.resources.getDimensionPixelOffset(R.dimen.img_drag_shadow_one_height)
                iconHeight =
                    context.resources.getDimensionPixelOffset(R.dimen.img_drag_shadow_one_height)
            }
            STATUS_RECENT_SINGLE_IMG -> {
                radius = context.resources.getDimensionPixelOffset(R.dimen.single_img_drag_shadow_layout_background_radius)
                iconWidth =
                    context.resources.getDimensionPixelOffset(R.dimen.single_img_drag_shadow_one_width)
                iconHeight =
                    context.resources.getDimensionPixelOffset(R.dimen.single_img_drag_shadow_one_height)
            }
        }
        val bitmap = drawableToBitmap(drawable) ?: return null
        val thumbnail = ThumbnailUtils.extractThumbnail(bitmap, iconWidth, iconHeight) ?: return null
        return BitmapDrawable(context.resources, roundCorner(thumbnail, radius.toFloat()))
    }

    @JvmStatic
    fun drawableToBitmap(drawable: Drawable): Bitmap? {
        val width = drawable.intrinsicWidth
        val height = drawable.intrinsicHeight
        if (width <= 0 || height <= 0) {
            return null
        }
        val bitmap = Bitmap.createBitmap(
            width,
            height,
            if (drawable.opacity != PixelFormat.OPAQUE) {
                Bitmap.Config.ARGB_8888
            } else {
                Bitmap.Config.RGB_565
            }
        )
        val canvas = Canvas(bitmap)
        drawable.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
        drawable.draw(canvas)
        return bitmap
    }

    @JvmStatic
    fun isPhoneStorageActivity(activity: Activity): Boolean {
        Log.d(TAG, "isPhoneStorageActivity activity.javaClass.name = ${activity.javaClass.name}")
        return activity.componentName.className == DefaultDropListener.PHONE_STORAGE_ACTIVITY ||
                activity.componentName.className == DefaultDropListener.OTG_STORAGE_ACTIVITY
    }

    @JvmStatic
    fun isMainStorageFragment(activity: Activity): Boolean {
        if (activity.componentName.className == DefaultDropListener.MAIN_ACTIVITY
            || activity.componentName.className == DefaultDropListener.MAIN_EXPORT_ACTIVITY
        ) {
            val position = DragDropAction.getMainTab(activity)
            val categoryType = DragDropAction.getMainCategoryType(activity)
            Log.d(DefaultDropListener.TAG, "isMainStorageFragment position = $position categoryType = $categoryType")
            if (position == DefaultDropListener.INDEX_CATEGORY) {
                return categoryType == CategoryHelper.CATEGORY_FILE_BROWSER ||
                        categoryType == CategoryHelper.CATEGORY_OTG_BROWSER ||
                        categoryType == CategoryHelper.CATEGORY_SDCARD_BROWSER
            }
        }
        return false
    }

    @JvmStatic
    fun isRecentFragment(activity: Activity): Boolean {
        if (activity.componentName.className == DefaultDropListener.MAIN_ACTIVITY
            || activity.componentName.className == DefaultDropListener.MAIN_EXPORT_ACTIVITY
        ) {
            return DragDropAction.getMainTab(activity) == DefaultDropListener.INDEX_RECENT
        }
        return false
    }

    @JvmStatic
    fun createShadowBuild(context: Context, code: Int, view: View, viewList: ArrayList<View>, dragCount: Int): NewFileDragDropShadow? {
        view.measure(
            MeasureSpec.makeMeasureSpec(view.width, MeasureSpec.EXACTLY),
            MeasureSpec.makeMeasureSpec(view.height, MeasureSpec.EXACTLY)
        )
        shadowBuilder = NewFileDragDropShadow(context, code, view.width, view.height, dragCount)
        println("Measured width: ${view.measuredWidth}, Measured height: ${view.measuredHeight}")
        return shadowBuilder
    }

    @JvmStatic
    fun getShadowBuild(): NewFileDragDropShadow? {
        return shadowBuilder
    }

    @JvmStatic
    fun destoryShadow() {
        shadowBuilder = null
        mWeakView = null
    }

    @JvmStatic
    fun createShadowView(view: View) {
        mWeakView = WeakReference(view)
    }

    @JvmStatic
    fun getShadowView(): WeakReference<View>? {
        return mWeakView
    }

    @JvmStatic
    fun getItemSize(type: Int, dropCount: Int, viewWidth: Int, viewHeight: Int, context: Context): Pair<Int, Int> {
        val itemHeight = (viewHeight * BASIC_MAGNIFICATION).toInt()
        val itemWidth = if (type == STATUS_GRID) {
            (viewWidth * BASIC_MAGNIFICATION).toInt()
        } else {
            val screenWidth = KtViewUtils.getWindowSize(context as? Activity).x
            if (viewWidth == screenWidth) {
                screenWidth
            } else {
                (viewWidth * BASIC_MAGNIFICATION).toInt()
            }
        }
        val maxValue = if (WindowUtils.isMiddleAndLargeScreen(context) && type != STATUS_GRID) {
            context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_item_layout_middle_large_screen_height_max)
        } else {
            context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_item_layout_small_screen_height_max)
        }
        if (dropCount == 1 && type != STATUS_GRID) {
            if (itemHeight > maxValue) {
                return calculateWhenHeightGreater(itemHeight, itemWidth, maxValue)
            }
            return Pair(itemWidth, itemHeight)
        }
        if (itemHeight > maxValue && itemWidth > maxValue) {
            return if (itemHeight > itemWidth) {
                calculateWhenHeightGreater(itemHeight, itemWidth, maxValue)
            } else {
                calculateWhenWidthGreater(itemHeight, itemWidth, maxValue)
            }
        }
        if (itemWidth > maxValue) {
            return calculateWhenWidthGreater(itemHeight, itemWidth, maxValue)
        }
        if (itemHeight > maxValue) {
            return calculateWhenHeightGreater(itemHeight, itemWidth, maxValue)
        }
        return Pair(itemWidth, itemHeight)
    }

    @JvmStatic
    private fun calculateWhenHeightGreater(itemHeight: Int, itemWidth: Int, maxValue: Int): Pair<Int, Int> {
        val scale = (maxValue.toDouble()).div(itemHeight.toDouble())
        val width = (itemWidth * scale).toInt()
        return Pair(width, maxValue)
    }

    @JvmStatic
    private fun calculateWhenWidthGreater(itemHeight: Int, itemWidth: Int, maxValue: Int): Pair<Int, Int> {
        val scale = (maxValue.toDouble()).div(itemWidth.toDouble())
        val height = (itemHeight * scale).toInt()
        return Pair(maxValue, height)
    }

    @JvmStatic
    fun isDragStartFromRemoteMac(dragEvent: DragEvent): Boolean {
        val remoteMacTag = CommonConstants.DRAG_FROM_FRAGMENT + CATEGORY_FILE_REMOTE_MAC
        val localState = dragEvent.localState
        val action = dragEvent.action
        return localState == remoteMacTag && action == DragEvent.ACTION_DRAG_STARTED
    }

    @JvmStatic
    fun isDragEndFromRemoteMac(dragEvent: DragEvent): Boolean {
        val remoteMacTag = CommonConstants.DRAG_FROM_FRAGMENT + CATEGORY_FILE_REMOTE_MAC
        val localState = dragEvent.localState
        val action = dragEvent.action
        return localState == remoteMacTag && action == DragEvent.ACTION_DRAG_ENDED
    }

    @JvmStatic
    fun sendCornerMarkBroadcast(stateCode: Int, context: Context) {
        cornerMarkBroadcastIntent.putExtra(KtConstants.BADGE_STATUS, stateCode)
        cornerMarkBroadcastIntent.setFlags(Intent.FLAG_RECEIVER_FOREGROUND)
        context.sendBroadcast(cornerMarkBroadcastIntent)
    }

    @JvmStatic
    fun setCornerMarkStateCode(stateCode: Int, view: View) {
        if (!SdkUtils.isAtLeastOS16()) return
        val shadowBuild = DragUtils.getShadowBuild() ?: return
        val cornerMarkPosition = shadowBuild.getCornerMarkPosition() ?: return
        if (cornerMarkPosition.size < 1) {
            return
        }
        var state = stateCode
        val count = shadowBuild.dropCount
        var display = count.toString()
        if (count == 1) {
            //普通状态下如果只选中1个则不显示角标
            if (state == DropDispatchAction.NORMAL_STATE) {
                state = -1
            }
            display = ""
        }
        val bundle = Bundle()
        bundle.putBoolean(KtConstants.BADGE_SKIP_ANIMA, false)
        OplusView(view).updateDragShadowBadge(
            shadowBuild,
            cornerMarkPosition[0], cornerMarkPosition[1],
            display, state, bundle
        )
    }

    @JvmStatic
    fun createSelectedFileList(fileBeans: ArrayList<out BaseFileBean>) {
        val arrayList = ArrayList<BaseFileBean>()
        arrayList.addAll(fileBeans)
        selectedFileBean = arrayList
    }

    @JvmStatic
    fun destorySelectedFiles() {
        isDragging = false
        selectedFileBean = null
    }

    @JvmStatic
    fun getSelectedFiles(): ArrayList<out BaseFileBean>? {
        return selectedFileBean
    }

    @JvmStatic
    fun resetCornerMarkState() {
        lastCornerMarkStateCode = -1
    }

    @JvmStatic
    fun resetAllState() {
        destoryShadow()
        destorySelectedFiles()
        dragTag = null
        isDragging = false
        hasAndroidDataFile = false
        resetCornerMarkState()
    }

    @JvmStatic
    fun actionDragEntered(): Boolean {
        dragOutSide = false
        return true
    }

    @JvmStatic
    fun actionDragExited(): Boolean {
        dragOutSide = true
        return true
    }

    @JvmStatic
    fun actionDragEnded(e: DragEvent, activity: Activity): Boolean {
        Log.d(TAG, "actionDragEnded dragOutSide:$dragOutSide")
        if (dragOutSide) {
            dragOutSide = false
            val r = e.result
            Log.d(TAG, "actionDragEnded r:$r, ")
            if (!r) {
                CustomToast.showShort(R.string.drag_unable_share_to_app)
            }
        }
        return true
    }

    @JvmStatic
    fun getViewBitmap(view: ViewGroup, rootView: View, type: SelectionTracker.LAYOUT_TYPE?): Bitmap? {
        val arrayList = ArrayList<View>()
        view.forEach { childView ->
            Log.d(TAG, "view is child $childView")
            if (childView is ViewGroup) {
                getViewBitmap(childView, rootView, type)
            } else {
                if (childView is GridThumbView) {
                    childView.setDrawableVisible(0)
                    arrayList.add(childView)
                }
                if (childView is COUICheckBox || childView.id == R.id.divider_line) {
                    if (childView.visibility == VISIBLE) arrayList.add(childView)
                    childView.visibility = GONE
                }
            }
        }
        if (view == rootView) {
            val background = hiddenBackground(view, type)
            val bitmap = createBitmap(view.width, view.height)
            val canvas = Canvas(bitmap)
            view.draw(canvas)
            arrayList.forEach { view ->
                if (view is GridThumbView) {
                    view.setDrawableVisible(1)
                } else {
                    view.visibility = VISIBLE
                }
            }
            background?.let { view.background = ContextCompat.getDrawable(view.context, R.drawable.select_list_item_background_selector)}
            return bitmap
        }
        return null
    }
    
    private fun hiddenBackground(view: ViewGroup, type: SelectionTracker.LAYOUT_TYPE?): Drawable? {
        if (view.background == null) return null
        return if (type == SelectionTracker.LAYOUT_TYPE.LIST) {
            view.setBackgroundColor(0)
            view.background
        } else {
            null
        }
    }

    fun addShadowView() {
        val viewList = ArrayList<View>()
        DefaultDropListener.selectedItemViews?.value?.let { viewList.addAll(it) }
        val topView = getShadowView()?.get() ?: return
        val size = viewList.size
        viewList.remove(topView)
        viewList.add(topView)
        getShadowBuild()?.apply { ->
            viewList.forEachIndexed { index, _ ->
                val viewIndex = size - index - 1
                if (viewIndex < 0 || viewIndex >= viewList.size) return
                val view = viewList[viewIndex]
                val viewScaleArgs = getItemSize(statusCode, dropCount, view.width, view.height, context)
                val scaleWidth = viewScaleArgs.first.div(view.measuredWidth).toFloat()
                val scaleHeight = viewScaleArgs.second.div(view.measuredHeight).toFloat()
                Log.d("TDASA", "addShadowView index:$index view.measuredWidth:${view.measuredWidth} view.measuredHeight:${view.measuredHeight} scaleWidth:$scaleWidth scaleHeight:$scaleHeight")
                bitmapList[index] = Triple(scaleWidth, scaleHeight, view)
                if (index > POSITION_LIST_SIZE) return
            }
        }
    }
}