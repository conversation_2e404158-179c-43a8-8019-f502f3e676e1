<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/head_add_file"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="?attr/couiColorBackgroundWithCard"
    android:paddingTop="@dimen/dimen_10dp"
    android:paddingBottom="@dimen/dimen_10dp"
    android:clipToPadding="true"
    android:gravity="center"
    android:paddingStart="@dimen/dimen_24dp"
    android:paddingEnd="@dimen/dimen_24dp"
    android:visibility="visible">

    <TextView
        android:id="@+id/head_tv_add_file"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/label_files_add_file"
        android:textColor="?attr/couiColorLabelTheme"
        android:fontFamily="sans-serif-medium"
        android:textSize="@dimen/dimen_16dp"
        android:titleTextAppearance="@style/textAppearanceLargestTitle" />

    <Space
        android:id="@+id/add_file_foot_space"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        />
</LinearLayout>