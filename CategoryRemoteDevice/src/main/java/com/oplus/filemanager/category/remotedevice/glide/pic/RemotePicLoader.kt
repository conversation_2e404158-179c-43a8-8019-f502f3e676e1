/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemotePicLoader.kt
 ** Description: RemotePicLoader
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.category.remotedevice.glide.pic

import android.content.Context
import android.net.Uri
import com.bumptech.glide.load.Options
import com.bumptech.glide.load.model.ModelLoader
import com.bumptech.glide.signature.ObjectKey
import com.filemanager.common.base.RemoteFileBean

class RemotePicLoader(
    private val context: Context
) : ModelLoader<RemoteFileBean, Uri> {
    override fun buildLoadData(
        model: RemoteFileBean,
        width: Int,
        height: Int,
        options: Options
    ): ModelLoader.LoadData<Uri> = ModelLoader.LoadData(
        ObjectKey(model),
        RemotePicFetcher(context, model, width, height)
    )

    override fun handles(model: RemoteFileBean): Boolean = true
}