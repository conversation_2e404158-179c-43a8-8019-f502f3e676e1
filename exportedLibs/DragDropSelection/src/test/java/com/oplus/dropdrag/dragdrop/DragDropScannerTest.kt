package com.oplus.dropdrag.dragdrop

import android.content.Context
import android.content.ClipData
import android.graphics.drawable.Drawable
import androidx.test.platform.app.InstrumentationRegistry
import com.oplus.dropdrag.DragDropSdkManager
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * DragDropScanner的单元测试类
 * 用于测试DragDropScanner抽象类的各项功能
 */
@Config(sdk = [29])
@RunWith(RobolectricTestRunner::class)
class DragDropScannerTest {
    // 模拟的Context对象
    private lateinit var mockContext: Context
    // 模拟的扫描监听器
    private lateinit var mockListener: DragScannerListener
    // 模拟的Drawable对象
    private lateinit var mockDrawable: Drawable
    // 模拟的ClipData对象
    private lateinit var mockClipData: ClipData
    
    /**
     * 测试前的初始化方法
     * 1. 获取测试环境中的Context
     * 2. 创建模拟对象
     * 3. 初始化DragDropSdkManager
     */
    @Before
    fun setUp() {
        // 获取测试环境中的Context
        mockContext = InstrumentationRegistry.getInstrumentation().targetContext
        // 创建放松的模拟监听器(relaxed=true表示不严格验证所有调用)
        mockListener = mockk(relaxed = true)
        // 创建放松的模拟Drawable
        mockDrawable = mockk(relaxed = true)
        // 创建放松的模拟ClipData
        mockClipData = mockk(relaxed = true)
        // 初始化DragDropSdkManager
        DragDropSdkManager.init(mockContext)
        // 清除所有模拟调用记录
        clearAllMocks()
    }
    
    /**
     * 测试后的清理方法
     * 1. 解除所有模拟对象
     * 2. 重置DragDropSdkManager
     */
    @After
    fun tearDown() {
        // 解除所有模拟对象
        unmockkAll()
        // 重置DragDropSdkManager
        DragDropSdkManager.init(mockContext)
    }
    
    /**
     * 测试scanData方法是抽象的
     * 验证子类必须实现scanData方法
     */
    @Test
    fun `test scanData is abstract`() {
        // 创建一个匿名子类实现抽象方法
        val scanner = object : DragDropScanner<String>(mockContext, mockListener) {
            override fun scanData(): DragScanResult? = null
        }
        
        // 验证scanData方法返回null(因为匿名类中返回null)
        assertNull(scanner.scanData())
    }
}

/**
 * 扩展函数: 获取DragDropScanner的扫描监听器(用于测试)
 * 通过反射获取私有字段mScannerListener的值
 * @return 返回扫描监听器对象
 */
fun <T> DragDropScanner<T>.getScannerListenerForTest(): DragScannerListener? {
    // 获取mScannerListener字段
    val field = this::class.java.getDeclaredField("mScannerListener")
    // 设置可访问
    field.isAccessible = true
    // 获取字段值并转换为DragScannerListener类型
    return field.get(this) as? DragScannerListener
}

/**
 * 扩展函数: 获取DragDropScanner的数据列表(用于测试)
 * 通过反射获取私有字段mDataList的值
 * @return 返回数据列表
 */
fun <T> DragDropScanner<T>.getDataListForTest(): ArrayList<T>? {
    // 获取mDataList字段
    val field = this::class.java.getDeclaredField("mDataList")
    // 设置可访问
    field.isAccessible = true
    // 获取字段值并转换为ArrayList<T>类型
    return field.get(this) as? ArrayList<T>
}