/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForFileBrowser
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.filemanager.recyclebin.di

import com.filemanager.recyclebin.RecycleBinApi
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin
import org.koin.dsl.module

object AutoDIForRecycleBin {

    val recycleBinModule = module {
        single<IRecycleBin>(createdAtStart = true) {
            RecycleBinApi
        }
    }
}