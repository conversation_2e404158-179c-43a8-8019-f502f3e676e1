/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : AppSwitchApi.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/27
 * * Author      : huangyuanwang
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch

import android.content.Context
import android.util.Log
import com.oplus.filemanager.interfaze.appswitch.IAppSwitchApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.function.Consumer

object AppSwitchApi : IAppSwitchApi {

    const val TAG = "AppSwitchApi"

    override fun isCurrentAppSwitchConfigFeatureOn(context: Context): Boolean {
        Log.i(TAG, "getCurrentAppSwitchConfig")
        return AppSwitchManager.getCurrentConfig()?.switchOn ?: false
    }

    override fun isCurrentThirdAppListenConfigFeatureOn(): Boolean {
        val result = AppSwitchManager.currentThirdAppListenConfigParam?.isFeatureOn() ?: false
        Log.i(TAG, "getCurrentThirdAppListenConfig $result ${AppSwitchManager.currentThirdAppListenConfigParam == null}")
        return result
    }

    override fun updateThirdAppListenConfig(thirdAppConfigJsonString: String) {
        Log.i(TAG, "updateThirdAppListenConfig")
        return AppSwitchManager.updateThirdAppListenConfig(thirdAppConfigJsonString)
    }

    override fun initDmpAndAppSwitch(useService: Boolean) {
        Log.i(TAG, "initAppSwitchManager $useService")
        AppSwitchManager.initDmpAndAppSwitch(useService)
    }

    override fun trigDbSync(useWorkerThread: Boolean) {
        Log.i(TAG, "trigDbSync useWorkerThread $useWorkerThread")
        if (useWorkerThread) {
            GlobalScope.launch(Dispatchers.IO) {
                AppSwitchManager.doIndexSyncWork()
            }
        } else {
            AppSwitchManager.doIndexSyncWork()
        }
    }

    override fun getAppListConfigReady(): Boolean {
        return AppSwitchManager.currentThirdAppListenConfigParam != null
    }

    override fun setAppListConfigReadyCallback(consumer: Consumer<Boolean>?) {
        AppSwitchManager.setConsumer(consumer)
    }
}