/**************************************************************************
 * * Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 * * File: - PersonalInfoListActivity.kt
 * * Description:
 * * Version: 1.0
 * * Date : 2024/11/01
 * * Author: ********
 * *
 * * --------------------------Revision History: --------------------------
 * *  <author>                         <data>        <version >    <desc>
 * *  ********      2024/11/01          1.0
 **************************************************************************/
package com.coloros.filemanager.export

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.Typeface
import android.icu.text.SimpleDateFormat
import android.net.Uri
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.format.DateUtils
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.text.style.URLSpan
import android.view.Gravity
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewTreeObserver
import android.view.WindowInsets
import android.widget.TextView
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.core.text.HtmlCompat
import com.coui.appcompat.clickablespan.COUIClickableSpan
import com.coui.appcompat.theme.COUIThemeOverlay
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.edge.EdgeToEdgeActivity
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.LanguageUtil.checkAndReturnLocale
import com.filemanager.common.utils.LanguageUtil.getLocalizedResources
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.dp2px
import com.filemanager.common.utils.isNetworkAvailable
import com.google.android.material.appbar.AppBarLayout
import com.oplus.filemanager.interfaze.setting.ISetting
import java.text.DateFormat
import java.util.Locale

class PersonalInfoListActivity : EdgeToEdgeActivity(), UIConfigMonitor.OnUIConfigChangeListener {
    companion object {
        private const val TAG = "PersonalInfoListActivity"
        private const val CHINESE_DATE = "2024年10月17日"
        private const val CHINESE_DATE_FORMAT = "yyyy年M月d日"
        private const val TAI_WAN_DATE_FORMAT = "yyyy 年 M 月 d 日"
        private const val KEY_PAGE_TYPE = "pageType"
        private const val TYPE_PRIVACY_INFO = 1

        private const val CLICK_PERSONAL_INFO_PROTECT_POLICY = "personalInfoProtectPolicy"
        private const val SPACE_5 = 5f
        private const val SPACE_8 = 8f
        private const val SPACE_12 = 12f
        private const val SPACE_14 = 14f
        private const val SPACE_16 = 16f
        private const val SPACE_24 = 24f
        private const val SPACE_34 = 34f
        private const val ALPHA = 0.85f

        fun start(activity: Activity?, type: Int) {
            activity?.apply {
                val intent = Intent(activity, PersonalInfoListActivity::class.java)
                intent.putExtra(KEY_PAGE_TYPE, type)
                startActivity(intent)
            }
        }
    }

    private lateinit var appBarLayout: AppBarLayout
    private lateinit var toolbar: COUIToolbar
    private lateinit var rootView: View
    private lateinit var scrollView: View

    private val black = Color.argb(ALPHA, 0f, 0f, 0f)
    private var isTh = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        StatusBarUtil.setStatusBarTransparentAndBlackFont(this)
        COUIThemeOverlay.getInstance().applyThemeOverlays(this)
        setContentView(getLayoutResId())
        initView()
    }

    private fun getLayoutResId(): Int {
        return R.layout.activity_user_agreement
    }

    private fun initView() {
        rootView = findViewById(R.id.rootView)
        appBarLayout = findViewById(com.filemanager.common.R.id.appbar_layout)
        toolbar = findViewById(com.filemanager.common.R.id.toolbar)
        scrollView = findViewById(R.id.scrollView)
        val localizedResources = getLocalizedResources(this, checkAndReturnLocale())
        toolbar.title = localizedResources.getString(R.string.collect_personal_infomation_title_copy)
        isTh = checkLocalTh()
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        initToolbar()
        loadView()
        rootView.setOnApplyWindowInsetsListener(rootWindowInsetsListener)
        val setting = Injector.injectFactory<ISetting>()
        setting?.setBarBackgroundColor(
            toolbar,
            rootView,
            window.decorView,
            resources.configuration,
            this
        )
        setting?.setActionCloseFlexibleActivity(window.decorView, this)
    }

    private fun initToolbar() {
        var appBarTopPadding = StatusBarUtil.getStatusBarHeight()
        if (FeatureCompat.isApplicableForFlexibleWindow && WindowUtils.isMiddleAndLargeScreen(this)) {
            appBarTopPadding =
                appContext.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_16dp)
        }
        appBarLayout.setPadding(0, appBarTopPadding, 0, 0)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    private fun loadView() {
        val type = intent.getIntExtra(KEY_PAGE_TYPE, TYPE_PRIVACY_INFO)
        findViewById<LinearLayoutCompat>(R.id.container).apply {
            removeAllViews()
            showView()
        }
    }
    @SuppressLint("SimpleDateFormat")
    @Suppress("TooGenericExceptionCaught")
    private fun dateString(): String {
        try {
            val date = SimpleDateFormat(CHINESE_DATE_FORMAT).parse(
                CHINESE_DATE
            )
            Log.i(TAG, "$date")
            return  DateFormat.getDateInstance(DateFormat.MEDIUM, Locale.ENGLISH).format(date)
        } catch (e: Exception) {
            Log.e(TAG, e)
        }
        return CHINESE_DATE
    }
    private fun LinearLayoutCompat.showView() {
        addSpace(SPACE_8).addTitleView {
            center()
            bold()
            append(R.string.collect_personal_infomation_title_copy) }.addSpace(SPACE_24).addTextView {
            when (checkAndReturnLocale()) {
                Locale.getDefault() ->  appendHtml(R.string.collect_personal_infomation_update_time, formatDate())
                Locale.ENGLISH -> appendHtml(R.string.collect_personal_infomation_update_time, dateString())
            }
            sp12() }.addSpace(SPACE_24).addTextView {
            when {
                Utils.isRealmePhone() -> appendHtml(R.string.collect_personal_infomation_content_realme)
                Utils.isOnePlusPhone() -> appendHtml(R.string.collect_personal_infomation_content_1plus)
                else -> appendHtml(R.string.collect_personal_infomation_content_oppo)
            } }.addSpace(SPACE_24).addTextView {
            appendHtml(R.string.collect_personal_infomation_content_1) }.addSpace(SPACE_24).addTextView {
            appendHtml(R.string.collect_personal_infomation_content_2) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.collect_personal_infomation_title_1) }.addSpace(SPACE_12).addTextView {
            appendHtml(R.string.collect_personal_infomation_title_1_content) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.collect_personal_infomation_title_2) }.addSpace(SPACE_12).addTextView {
            appendHtml(R.string.collect_personal_infomation_title_2_content_1) }.addSpace(SPACE_12).addTextView {
            appendHtml(R.string.collect_personal_infomation_title_2_content_1_1) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.collect_personal_infomation_title_3) }.addSpace(SPACE_12).addTextView {
            appendHtml(R.string.collect_personal_infomation_title_3_content) }.addSpace(SPACE_12).addTextView {
            appendHtml(R.string.collect_personal_infomation_title_3_content_1) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.collect_personal_infomation_title_4) }.addSpace(SPACE_12).addTextView {
            appendHtml(R.string.collect_personal_infomation_title_4_content) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.collect_personal_infomation_title_5) }.addSpace(SPACE_12).addTextView {
            appendHtml(R.string.collect_personal_infomation_title_5_content) }.addSpace(SPACE_12).addTextView {
            appendHtml(R.string.collect_personal_infomation_title_5_content_list_1) }.addSpace(SPACE_12).addTextView {
            appendHtml(R.string.collect_personal_infomation_title_5_content_list_2) }.addSpace(SPACE_12).addTextView {
            appendHtml(R.string.collect_personal_infomation_title_5_content_list_3) }.addSpace(SPACE_24).addTextView { bold()
            appendHtml(R.string.collect_personal_infomation_title_6) }.addSpace(SPACE_12).addTextView {
            appendHtml(R.string.collect_personal_infomation_title_6_content_1) { clickCloudServiceAndAccount(it) } }.addSpace(SPACE_24).addTextView {
            appendHtml(R.string.collect_personal_infomation_title_6_content_2) }.addSpace(SPACE_24).addTextView {
            appendHtml(R.string.collect_personal_infomation_title_6_content_3) { clickCloudServiceAndAccount(it) } }.addSpace(SPACE_24).addTextView {
            when {
                Utils.isRealmePhone() -> appendHtml(R.string.collect_personal_infomation_title_6_content_3_realme) { openHttpLink(it) }
                Utils.isOnePlusPhone() -> { appendHtml(R.string.collect_personal_infomation_title_6_content_3_1plus_1)
                    addSpace(SPACE_12)
                    appendHtml(R.string.collect_personal_infomation_title_6_content_3_1plus_2) { openHttpLink(it) }
                }
                else -> appendHtml(R.string.collect_personal_infomation_title_6_content_3_oppo) { openHttpLink(it) }
            }
        }
    }

    private fun clickCloudServiceAndAccount(it: String) {
        if (!isNetworkAvailable(this)) {
            CustomToast.showShort(com.filemanager.common.R.string.no_internet_connection)
            return
        }
        when (it) {
            CLICK_PERSONAL_INFO_PROTECT_POLICY -> PermissionUtils.openPrivacyPolicy(this)
        }
    }

    private fun openHttpLink(httpLink: String) {
        val uri = Uri.parse(httpLink)
        val intent = Intent(Intent.ACTION_VIEW, uri)
        startActivity(intent)
    }

    private fun LinearLayoutCompat.addTextView(callback: TextView.() -> Unit): LinearLayoutCompat {
        addView(TextView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            gravity = Gravity.START
            textSize = SPACE_14
            setTextColor(black)
            includeFontPadding = if (isTh) {
                true
            } else {
                false
            }
            lineSpace(SPACE_5)
            callback()
        })
        return this
    }

    private fun checkLocalTh(): Boolean {
        var isTh = false
        runCatching {
            val local = resources.configuration.locale
            isTh = local.language.contains("th")
            Log.i(TAG, "checkLocalTh language ${local.language}, isTh $isTh")
        }.onFailure {
            Log.e(TAG, "checkLocalTh error", it)
        }
        return isTh
    }

    private fun TextView.center() {
        gravity = Gravity.CENTER
    }

    private fun TextView.sp16() {
        textSize = SPACE_16
    }

    private fun TextView.sp12() {
        textSize = SPACE_12
    }

    private fun TextView.bold() {
        typeface = Typeface.create(typeface, Typeface.BOLD)
    }

    private fun LinearLayoutCompat.addSpace(space: Float = SPACE_16): LinearLayoutCompat {
        addView(View(context).apply {
            layoutParams = ViewGroup.LayoutParams(1, dp2px(appContext, space.toInt()))
        })
        return this
    }

    private fun LinearLayoutCompat.addTitleView(
        callback: TextView.() -> Unit
    ): LinearLayoutCompat {
        addView(TextView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            gravity = Gravity.START
            sp16()
            black()
            includeFontPadding = false
            callback()
        })
        return this
    }

    private fun TextView.append(res: Int, vararg values: String) {
        val localizedResources = getLocalizedResources(context, checkAndReturnLocale())
        append(localizedResources.getString(res, *values))
    }

    private fun TextView.appendHtml(
        res: Int,
        vararg values: String,
        onClick: ((String) -> Unit)? = null
    ) {
        //如果是中英繁日西
        val localizedResources = getLocalizedResources(context, checkAndReturnLocale())
        val string = localizedResources.getString(res, *values)
        val output = HtmlCompat.fromHtml(string, HtmlCompat.FROM_HTML_MODE_LEGACY, null, null) as SpannableStringBuilder
        val boldSpans = output.getSpans(
            0, output.length, StyleSpan::class.java
        ).filter { it.style == Typeface.BOLD }
        boldSpans.forEach {
            var start = output.getSpanStart(it)
            val end = output.getSpanEnd(it)
            val flags = output.getSpanFlags(it)
            val colorSpan = ForegroundColorSpan(black)
            val urlSpans = output.getSpans(start, end, ClickableSpan::class.java)
            if (urlSpans.isEmpty()) {
                output.setSpan(colorSpan, start, end, flags)
            } else {
                urlSpans.forEach { span ->
                    val s = output.getSpanStart(span)
                    val e = output.getSpanEnd(span)
                    output.setSpan(colorSpan, start, s, flags)
                    start = e
                }
                output.setSpan(colorSpan, start, end, flags)
            }
        }
        val urlSpans = output.getSpans(0, output.length, URLSpan::class.java)
        urlSpans.forEach {
            val start = output.getSpanStart(it)
            val end = output.getSpanEnd(it)
            val flags = output.getSpanFlags(it)
            val clickSpan = COUIClickableSpan(context).apply {
                setStatusBarClickListener {
                    onClick?.invoke(it.url)
                }
            }
            output.removeSpan(it)
            output.setSpan(clickSpan, start, end, flags)
        }
        append(output)
        movementMethod = LinkMovementMethod.getInstance()
    }

    private fun TextView.black() {
        setTextColor(black)
    }

    private fun TextView.appendLine() {
        append("\n")
    }

    private fun TextView.appendSpace() {
        append("        ")
    }

    private fun TextView.lineSpace(sp: Float) {
        val fontScale = resources.configuration.fontScale
        val density = resources.displayMetrics.density
        setLineSpacing(sp * density * fontScale, 1f)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        UIConfigMonitor.instance.onActivityConfigChanged(newConfig)
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        initToolbar()
        Injector.injectFactory<ISetting>()?.setBarBackgroundColor(
            toolbar,
            rootView,
            window.decorView,
            resources.configuration,
            this
        )
    }

    private val rootWindowInsetsListener = View.OnApplyWindowInsetsListener { _, insets ->
        rootView.viewTreeObserver?.addOnGlobalLayoutListener(globalLayoutListener)
        if (SdkUtils.isAtLeastR()) {
            WindowInsets.CONSUMED
        } else {
            insets
        }
    }

    private val globalLayoutListener = object : ViewTreeObserver.OnGlobalLayoutListener {
        override fun onGlobalLayout() {
            rootView.viewTreeObserver?.removeOnGlobalLayoutListener(this)
            updateScrollViewMargin()
        }
    }

    private fun formatDate(): String {
        return DateUtils.formatDateTime(
            appContext,
            dateStrToTimestamp(),
            DateUtils.FORMAT_SHOW_DATE or DateUtils.FORMAT_SHOW_YEAR
        )
    }

    private fun dateStrToTimestamp(): Long {
        val formatter = SimpleDateFormat("yyyy年MM月dd日", Locale.CHINA) // 调整格式以匹配你的日期字符串
        var timestamp: Long? = null

        val date = formatter.parse(CHINESE_DATE)
        if (date != null) {
            timestamp = date.time // 获取时间戳
        }

        return timestamp ?: 0L // 如果解析失败，默认返回0L
    }

    private fun updateScrollViewMargin() {
        val widthDp = ViewHelper.px2dip(this@PersonalInfoListActivity, rootView.width)
        val margin = when (WindowUtils.getScreenWindowType(widthDp, 0)) {
            WindowUtils.SMALL -> resources.getDimensionPixelSize(R.dimen.user_info_horizontal_margin)
            WindowUtils.MIDDLE -> resources.getDimensionPixelSize(R.dimen.user_info_horizontal_margin_middle)
            WindowUtils.LARGE -> resources.getDimensionPixelSize(R.dimen.user_info_horizontal_margin_large)
            else -> resources.getDimensionPixelSize(R.dimen.user_info_horizontal_margin)
        }
        val params = scrollView.layoutParams as? MarginLayoutParams
        params?.let {
            params.marginStart = margin
            params.marginEnd = margin
            scrollView.layoutParams = it
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        rootView.setOnApplyWindowInsetsListener(null)
    }
}