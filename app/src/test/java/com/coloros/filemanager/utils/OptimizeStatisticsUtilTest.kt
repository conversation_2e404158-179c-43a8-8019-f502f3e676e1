/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : OptimizeStatisticsUtilTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/10/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/10/23       1      create
 ***********************************************************************/
package com.coloros.filemanager.utils

import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.os.Environment
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.dragselection.DefaultDropListener
import com.filemanager.common.dragselection.DragDropAction
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.TimeHelper
import io.mockk.InternalPlatformDsl
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.apache.commons.io.FilenameUtils
import java.io.File
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class OptimizeStatisticsUtilTest {

    private lateinit var context: Context
    private lateinit var optimizeUtil: OptimizeStatisticsUtil

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true)
        mockkObject(MyApplication)
        every { MyApplication.sAppContext }.returns(context)
        mockkStatic(StatisticsUtils::class)
        optimizeUtil = spyk(OptimizeStatisticsUtil, recordPrivateCalls = true)
        mockkStatic(Environment::class)
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(FeatureCompat::class)
        mockkStatic(PreferencesUtils::class)
    }

    @After
    fun tearDown() {
        unmockkObject(MyApplication)
        unmockkStatic(StatisticsUtils::class)
        unmockkStatic(Environment::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(PreferencesUtils::class)
    }

    @Test
    fun `should return bool when call isRootDir`() {
        //given
        var path = "test${File.separator}music${File.separator}a.mp3"
        every { Environment.getExternalStorageDirectory().absolutePath } returns "test"
        //when
        var result = InternalPlatformDsl.dynamicCall(optimizeUtil, "isRootDir", arrayOf(path, true), mockk())
        //then
        Assert.assertFalse(result as Boolean)

        //given
        path = "test${File.separator}music"
        //when
        result = InternalPlatformDsl.dynamicCall(optimizeUtil, "isRootDir", arrayOf(path, true), mockk())
        //then
        Assert.assertTrue(result as Boolean)

        //given
        path = "test${File.separator}"
        //when
        result = InternalPlatformDsl.dynamicCall(optimizeUtil, "isRootDir", arrayOf(path, true), mockk())
        //then
        Assert.assertTrue(result as Boolean)
    }

    @Test
    fun `should return bool when call isCommonDir`() {
        //given
        var path = ""
        every { Environment.getExternalStorageDirectory().absolutePath } returns "test"
        //when
        var result = InternalPlatformDsl.dynamicCall(optimizeUtil, "isCommonDir", arrayOf(path), mockk())
        //then
        Assert.assertFalse(result as Boolean)

        //given
        path = "test" + File.separator + Environment.DIRECTORY_DOWNLOADS
        val docPath = "test" + File.separator + Environment.DIRECTORY_DOCUMENTS
        //when
        result = InternalPlatformDsl.dynamicCall(optimizeUtil, "isCommonDir", arrayOf(path), mockk())
        val isCommon = InternalPlatformDsl.dynamicCall(optimizeUtil, "isCommonDir", arrayOf(docPath), mockk())
        //then
        Assert.assertTrue(result as Boolean)
        Assert.assertTrue(isCommon as Boolean)
    }

    @Suppress("LongMethod")
    @Test
    fun `should return page when call signalToPage`() {
        //given
        every { context.getString(R.string.string_videos) } returns "string_videos"
        //when
        var result = OptimizeStatisticsUtil.signalToPage("string_videos")
        //then
        Assert.assertEquals(OptimizeStatisticsUtil.VIDEO, result)

        every { context.getString(R.string.string_audio) } returns "string_audio"
        result = OptimizeStatisticsUtil.signalToPage("string_audio")
        Assert.assertEquals(OptimizeStatisticsUtil.AUDIO, result)

        every { context.getString(R.string.string_wechat) } returns "string_wechat"
        result = OptimizeStatisticsUtil.signalToPage("string_wechat")
        Assert.assertEquals(OptimizeStatisticsUtil.WECHAT, result)

        result = OptimizeStatisticsUtil.signalToPage("com.tencent.mm")
        Assert.assertEquals(OptimizeStatisticsUtil.WECHAT, result)

        every { context.getString(R.string.string_qq) } returns "string_qq"
        result = OptimizeStatisticsUtil.signalToPage("string_qq")
        Assert.assertEquals(OptimizeStatisticsUtil.QQ, result)

        result = OptimizeStatisticsUtil.signalToPage("com.tencent.mobileqq")
        Assert.assertEquals(OptimizeStatisticsUtil.QQ, result)

        every { context.getString(R.string.download) } returns "download"
        result = OptimizeStatisticsUtil.signalToPage("download")
        Assert.assertEquals(OptimizeStatisticsUtil.DOWNLOADS, result)

        every { context.getString(R.string.bluetooth) } returns "bluetooth"
        result = OptimizeStatisticsUtil.signalToPage("bluetooth")
        Assert.assertEquals(OptimizeStatisticsUtil.BLUETOOTH, result)

        every { context.getString(R.string.owork_space) } returns "owork_space"
        result = OptimizeStatisticsUtil.signalToPage("owork_space")
        Assert.assertEquals(OptimizeStatisticsUtil.OWORK, result)

        every { context.getString(R.string.owork_space_new) } returns "owork_space"
        result = OptimizeStatisticsUtil.signalToPage("owork_space")
        Assert.assertEquals(OptimizeStatisticsUtil.OWORK, result)

        every { context.getString(R.string.owork_space_web_page_files) } returns "owork_space"
        result = OptimizeStatisticsUtil.signalToPage("owork_space")
        Assert.assertEquals(OptimizeStatisticsUtil.OWORK, result)

        every { context.getString(R.string.owork_space_web_page) } returns "owork_space"
        result = OptimizeStatisticsUtil.signalToPage("owork_space")
        Assert.assertEquals(OptimizeStatisticsUtil.OWORK, result)

        every { context.getString(R.string.hey_pc_name) } returns "hey_pc_name"
        result = OptimizeStatisticsUtil.signalToPage("hey_pc_name")
        Assert.assertEquals(OptimizeStatisticsUtil.PC_CONNECT, result)

        every { context.getString(R.string.oneplus_share) } returns "oneplus_share"
        result = OptimizeStatisticsUtil.signalToPage("oneplus_share")
        Assert.assertEquals(OptimizeStatisticsUtil.OSHARE, result)

        every { context.getString(R.string.realme_share) } returns "realme_share"
        result = OptimizeStatisticsUtil.signalToPage("realme_share")
        Assert.assertEquals(OptimizeStatisticsUtil.OSHARE, result)

        every { context.getString(R.string.oppo_share) } returns "oppo_share"
        result = OptimizeStatisticsUtil.signalToPage("oppo_share")
        Assert.assertEquals(OptimizeStatisticsUtil.OSHARE, result)
    }

    @Test
    fun `should return page when call getOptionPage for small screen phone`() {
        //given
        every { FeatureCompat.isSmallScreenPhone } returns true
        val component = mockk<ComponentName>(relaxed = true)
        val activity = mockk<Activity>(relaxed = true) {
            every { componentName } returns component
        }
        //when
        every { component.className } returns OptimizeStatisticsUtil.FILE_BROWSER_ACTIVITY
        var result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        //then
        Assert.assertEquals(OptimizeStatisticsUtil.ALL_STORAGE, result)

        every { component.className } returns OptimizeStatisticsUtil.OTG_ACTIVITY
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        Assert.assertEquals(OptimizeStatisticsUtil.ALL_STORAGE, result)

        every { component.className } returns OptimizeStatisticsUtil.ALBUM_ACTIVITY
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        Assert.assertEquals(OptimizeStatisticsUtil.IMAGE, result)

        every { component.className } returns OptimizeStatisticsUtil.VIDEO_AUDIO_ACTIVITY
        result = OptimizeStatisticsUtil.getOptionPage(activity, "video")
        Assert.assertEquals("video", result)

        every { component.className } returns OptimizeStatisticsUtil.DOC_ACTIVITY
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        Assert.assertEquals(OptimizeStatisticsUtil.DOCUMENT, result)

        every { component.className } returns OptimizeStatisticsUtil.APK_ACTIVITY
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        Assert.assertEquals(OptimizeStatisticsUtil.APK, result)

        every { component.className } returns OptimizeStatisticsUtil.COMPRESS_ACTIVITY
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        Assert.assertEquals(OptimizeStatisticsUtil.COMPRESS, result)

        every { component.className } returns OptimizeStatisticsUtil.SUPER_ACTIVITY
        result = OptimizeStatisticsUtil.getOptionPage(activity, "source")
        Assert.assertEquals("source", result)

        every { component.className } returns OptimizeStatisticsUtil.DFM_ACTIVITY
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        Assert.assertEquals(OptimizeStatisticsUtil.DFM, result)
    }

    @Suppress("LongMethod")
    @Test
    fun `should return page when call getOptionPage for not small screen phone`() {
        //given
        mockkStatic(DefaultDropListener::class)
        mockkStatic(DragDropAction::class)
        every { FeatureCompat.isSmallScreenPhone } returns false
        val cpName = mockk<ComponentName>(relaxed = true) {
            every { className } returns DefaultDropListener.MAIN_ACTIVITY
        }
        val activity = mockk<Activity>(relaxed = true) {
            every { componentName } returns cpName
        }
        val position = -1
        var categoryType = CategoryHelper.CATEGORY_FILE_BROWSER
        every { DragDropAction.getMainTab(any()) } returns position
        every { DragDropAction.getMainCategoryType(any()) } returns categoryType
        //when
        var result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        //then
        Assert.assertEquals(OptimizeStatisticsUtil.ALL_STORAGE, result)

        //given
        categoryType = CategoryHelper.CATEGORY_OTG_BROWSER
        every { DragDropAction.getMainCategoryType(any()) } returns categoryType
        //when
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        //then
        Assert.assertEquals(OptimizeStatisticsUtil.ALL_STORAGE, result)

        //given
        categoryType = OptimizeStatisticsUtil.CATEGORY_IMAGE_DISPLAY
        every { DragDropAction.getMainCategoryType(any()) } returns categoryType
        //when
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        //then
        Assert.assertEquals(OptimizeStatisticsUtil.IMAGE, result)

        //given
        categoryType = CategoryHelper.CATEGORY_VIDEO
        every { DragDropAction.getMainCategoryType(any()) } returns categoryType
        //when
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        //then
        Assert.assertEquals(OptimizeStatisticsUtil.VIDEO, result)

        //given
        categoryType = CategoryHelper.CATEGORY_AUDIO
        every { DragDropAction.getMainCategoryType(any()) } returns categoryType
        //when
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        //then
        Assert.assertEquals(OptimizeStatisticsUtil.AUDIO, result)

        //given
        categoryType = CategoryHelper.CATEGORY_DOC
        every { DragDropAction.getMainCategoryType(any()) } returns categoryType
        //when
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        //then
        Assert.assertEquals(OptimizeStatisticsUtil.DOCUMENT, result)

        //given
        categoryType = CategoryHelper.CATEGORY_APK
        every { DragDropAction.getMainCategoryType(any()) } returns categoryType
        //when
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        //then
        Assert.assertEquals(OptimizeStatisticsUtil.APK, result)

        //given
        categoryType = CategoryHelper.CATEGORY_COMPRESS
        every { DragDropAction.getMainCategoryType(any()) } returns categoryType
        //when
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        //then
        Assert.assertEquals(OptimizeStatisticsUtil.COMPRESS, result)

        //given
        categoryType = OptimizeStatisticsUtil.CATEGORY_FILE_SOURCE
        val source = "source"
        every { DragDropAction.getMainCategoryType(any()) } returns categoryType
        //when
        result = OptimizeStatisticsUtil.getOptionPage(activity, source)
        //then
        Assert.assertEquals(source, result)

        //given
        categoryType = CategoryHelper.CATEGORY_DFM
        every { DragDropAction.getMainCategoryType(any()) } returns categoryType
        //when
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        //then
        Assert.assertEquals(OptimizeStatisticsUtil.DFM, result)

        //given
        categoryType = -1
        every { DragDropAction.getMainCategoryType(any()) } returns categoryType
        //when
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        //then
        Assert.assertEquals("", result)

        every { FeatureCompat.isSmallScreenPhone } returns false
        every { cpName.className } returns ""
        result = OptimizeStatisticsUtil.getOptionPage(activity, "")
        Assert.assertEquals("", result)
        unmockkStatic(DefaultDropListener::class)
        unmockkStatic(DragDropAction::class)
    }

    @Test
    fun `should return bool when call checkMoreThanPeriodTime`() {
        //given
        mockkObject(TimeHelper)
        val key = ""
        val periodTime = 100L
        val lastTime = 950L
        var curTime = 1000L
        every { PreferencesUtils.getLong(key = any()) } returns lastTime
        every { TimeHelper.getNow() } returns curTime
        //when
        var result = InternalPlatformDsl.dynamicCall(optimizeUtil, "checkMoreThanPeriodTime", arrayOf(key, periodTime), mockk())
        //then
        Assert.assertFalse(result as Boolean)

        //given
        curTime = 2000L
        every { TimeHelper.getNow() } returns curTime
        //when
        result = InternalPlatformDsl.dynamicCall(optimizeUtil, "checkMoreThanPeriodTime", arrayOf(key, periodTime), mockk())
        //then
        Assert.assertTrue(result as Boolean)

        //given
        curTime = 300L
        every { TimeHelper.getNow() } returns curTime
        //when
        result = InternalPlatformDsl.dynamicCall(optimizeUtil, "checkMoreThanPeriodTime", arrayOf(key, periodTime), mockk())
        //then
        Assert.assertTrue(result as Boolean)
        unmockkObject(TimeHelper)
    }

    @Test
    fun `should verify correct when call createFolder`() {
        OptimizeStatisticsUtil.createFolder(-1, -1)
        OptimizeStatisticsUtil.createFolder(-1, 3)
        mockkStatic(StatisticsUtils::class)
        mockkObject(MyApplication)
        val context = mockk<Context>(relaxed = true)
        every { MyApplication.sAppContext }.returns(context)
        every { StatisticsUtils.onCommon(context, any(), any() as Map<String, String>) }.returns(Unit)
        OptimizeStatisticsUtil.createFolder(1, 0)
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.EVENT_CREATE_FOLDER, mapOf(StatisticsUtils.CREATE_FROM to "1")) }
        OptimizeStatisticsUtil.createFolder(2, 0)
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.EVENT_CREATE_FOLDER, mapOf(StatisticsUtils.CREATE_FROM to "2")) }
        OptimizeStatisticsUtil.createFolder(2, MessageConstant.MSG_EDITOR_COPY)
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.EVENT_CREATE_FOLDER, mapOf(StatisticsUtils.CREATE_FROM to "2")) }
        OptimizeStatisticsUtil.createFolder(-1, MessageConstant.MSG_EDITOR_COPY)
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.EVENT_CREATE_FOLDER, mapOf(StatisticsUtils.CREATE_FROM to "3")) }
        OptimizeStatisticsUtil.createFolder(-1, MessageConstant.MSG_EDITOR_CUT)
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.EVENT_CREATE_FOLDER, mapOf(StatisticsUtils.CREATE_FROM to "4")) }
        OptimizeStatisticsUtil.createFolder(-1, MessageConstant.MSG_SAVE)
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.EVENT_CREATE_FOLDER, mapOf(StatisticsUtils.CREATE_FROM to "5")) }
        OptimizeStatisticsUtil.createFolder(-1, MessageConstant.MSG_SAVE_AND_RENAME)
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.EVENT_CREATE_FOLDER, mapOf(StatisticsUtils.CREATE_FROM to "6")) }
        unmockkObject(MyApplication)
        unmockkStatic(StatisticsUtils::class)
    }

    @Test
    fun `should verify correct when call clickRecentFile`() {
        mockkStatic(StatisticsUtils::class)
        mockkObject(MyApplication)
        val context = mockk<Context>(relaxed = true)
        every { MyApplication.sAppContext }.returns(context)
        every { StatisticsUtils.onCommon(context, any(), any() as Map<String, String>) }.returns(Unit)
        mockkStatic(FilenameUtils::class)
        every { FilenameUtils.getExtension(any()) }.returns("test")
        val file = BaseFileBean()
        file.mData = "test.test"
        OptimizeStatisticsUtil.clickRecentFile(file)
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.RECENT_FILE_OPEN, mapOf(StatisticsUtils.FILE_EXTENSION to "test")) }
        unmockkStatic(FilenameUtils::class)
        unmockkObject(MyApplication)
        unmockkStatic(StatisticsUtils::class)
    }

    @Test
    fun `should verify correct when call categoryFileCount`() {
        mockkStatic(StatisticsUtils::class)
        mockkObject(MyApplication)
        mockkStatic(OptimizeStatisticsUtil::class)
        val context = mockk<Context>(relaxed = true)
        every { MyApplication.sAppContext }.returns(context)
        every { StatisticsUtils.onCommon(context, any(), any() as Map<String, String>) }.returns(Unit)
        every { OptimizeStatisticsUtil.checkMoreThanPeriodTime(any(), any()) }.returns(true)
        OptimizeStatisticsUtil.categoryFileCount(CategoryHelper.CATEGORY_LABEL_FILE, "1")
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.EVENT_FILE_LABEL_COUNT, mapOf(StatisticsUtils.EVENT_FILE_LABEL_COUNT to "1")) }
        OptimizeStatisticsUtil.categoryFileCount(CategoryHelper.CATEGORY_DOC, "2")
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.EVENT_DOC_FILE_COUNT, mapOf(StatisticsUtils.EVENT_DOC_FILE_COUNT to "2")) }
        OptimizeStatisticsUtil.categoryFileCount(CategoryHelper.CATEGORY_IMAGE, "16")
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.EVENT_PIC_FILE_COUNT, mapOf(StatisticsUtils.EVENT_PIC_FILE_COUNT to "16")) }
        OptimizeStatisticsUtil.categoryFileCount(CategoryHelper.CATEGORY_VIDEO, "28")
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.EVENT_VIDEO_FILE_COUNT, mapOf(StatisticsUtils.EVENT_VIDEO_FILE_COUNT to "28")) }
        OptimizeStatisticsUtil.categoryFileCount(CategoryHelper.CATEGORY_AUDIO, "34")
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.EVENT_AUDIO_FILE_COUNT, mapOf(StatisticsUtils.EVENT_AUDIO_FILE_COUNT to "34")) }
        OptimizeStatisticsUtil.categoryFileCount(CategoryHelper.CATEGORY_APK, "5")
        verify { StatisticsUtils.onCommon(context, StatisticsUtils.EVENT_APK_FILE_COUNT, mapOf(StatisticsUtils.EVENT_APK_FILE_COUNT to "5")) }
        OptimizeStatisticsUtil.categoryFileCount(CategoryHelper.CATEGORY_COMPRESS, "13")
        verify {
            StatisticsUtils.onCommon(
                context,
                StatisticsUtils.EVENT_COMPRESS_FILE_COUNT,
                mapOf(StatisticsUtils.EVENT_COMPRESS_FILE_COUNT to "13")
            )
        }
        unmockkObject(MyApplication)
        unmockkStatic(OptimizeStatisticsUtil::class)
        unmockkStatic(StatisticsUtils::class)
    }
}