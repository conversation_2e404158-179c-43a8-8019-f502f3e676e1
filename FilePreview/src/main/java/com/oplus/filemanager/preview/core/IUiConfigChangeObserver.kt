/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IUiConfigChangeObserver.kt
 * Description:
 *     The observer to track UI config changed events in preview fragments.
 *
 * Version: 1.0
 * Date: 2024-10-16
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-16   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.core

import com.filemanager.common.helper.uiconfig.type.IUIConfig

internal interface IUiConfigChangeObserver {

    fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>)
}

internal interface IUiConfigChangeManager {
    fun addUIConfigChangeObserver(observer: IUiConfigChangeObserver)

    fun removeUIConfigChangeObserver(observer: IUiConfigChangeObserver)
}