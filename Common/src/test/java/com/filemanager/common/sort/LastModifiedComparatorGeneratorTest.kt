/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LastModifiedComparatorGeneratorTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/10/8
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  hank.zhou      2022/10/8      1.0        create
 ***********************************************************************/
package com.filemanager.common.sort

import com.filemanager.common.base.BaseFileBean
import org.junit.Assert
import org.junit.Test

class LastModifiedComparatorGeneratorTest {

    @Test
    fun testGetComparator() {
        val compare = LastModifiedComparatorGenerator.getComparator(true)
        val file1 = BaseFileBean()
        val file2 = BaseFileBean()
        file1.mDateModified = 100L
        file2.mDateModified = 200L
        Assert.assertTrue(compare.compare(file1, file2) > 0)
        val compare2 = LastModifiedComparatorGenerator.getComparator(false)
        Assert.assertTrue(compare2.compare(file1, file2) < 0)
    }
}