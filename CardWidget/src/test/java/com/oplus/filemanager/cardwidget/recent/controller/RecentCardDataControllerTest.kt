package com.oplus.filemanager.cardwidget.recent.controller

import android.content.Context
import android.content.res.Resources
import android.net.Uri
import android.os.LocaleList
import android.util.Log
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.cardwidget.ext.getIconPNGByType
import com.oplus.filemanager.cardwidget.ext.writeFileProvider
import com.oplus.filemanager.cardwidget.recent.controller.loader.CategoryRecentLoader
import com.oplus.filemanager.cardwidget.recent.controller.loader.LoadCallback
import com.oplus.filemanager.cardwidget.recent.data.RecentCard
import com.oplus.filemanager.cardwidget.recent.data.RecentCardItemData
import com.oplus.filemanager.cardwidget.recent.data.RecentPreviewItem
import com.oplus.filemanager.cardwidget.util.CardWidgetThumbnailGenerator
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.unmockkAll
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.json.JSONObject
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import java.io.File
import java.util.Locale

/**
 * RecentCardDataController的单元测试类
 * 测试RecentCardDataController的各种功能场景
 */
@ExperimentalCoroutinesApi
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RecentCardDataControllerTest {

    // 测试用的协程调度器
    private val testDispatcher = TestCoroutineDispatcher()
    // 测试上下文
    private val context: Context = RuntimeEnvironment.getApplication()
    // 测试用的卡片大小
    private val size = 3
    // 被测控制器
    private lateinit var controller: RecentCardDataController

    /**
     * 测试前的准备工作
     * 1. 设置主调度器
     * 2. 模拟各种依赖对象
     */
    @Before
    fun setup() {
        // 设置测试用的主调度器
        Dispatchers.setMain(testDispatcher)
        // 模拟MyApplication单例
        mockkObject(MyApplication)
        every { MyApplication.appContext } returns context

        // 模拟Utils工具类
        mockkStatic(Utils::class)
        every { Utils.getDateFormat(any(), any()) } returns "2023-11-13"

        // 模拟MimeTypeHelper的伴生对象
        mockkObject(MimeTypeHelper.Companion)
        every { MimeTypeHelper.Companion.getTypeFromPath(any()) } returns MimeTypeHelper.UNKNOWN_TYPE
        every { MimeTypeHelper.Companion.getCompressedTypeByPath(any()) } returns MimeTypeHelper.COMPRESSED_TYPE

        // 模拟Log类
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } returns 0

        // 初始化被测控制器
        controller = RecentCardDataController(size)
    }

    /**
     * 测试后的清理工作
     * 1. 重置主调度器
     * 2. 解除所有模拟
     */
    @After
    fun tearDown() {
        Dispatchers.resetMain()
        testDispatcher.cleanupTestCoroutines()
        unmockkAll()
    }

    /**
     * 测试加载数据时返回空列表的情况
     * 验证当加载器返回空列表时，控制器能正确处理并返回空结果
     */
    @Test
    fun `loadData should return empty result when loader returns empty list`() = runBlocking(testDispatcher) {
        // 准备测试数据
        val card = RecentCard("card1", 3, "Recent", CategoryHelper.CATEGORY_RECENT)
        val allCategoryItem = listOf(RecentPreviewItem(CategoryHelper.CATEGORY_RECENT, "Recent"))

        // 使用slot捕获回调函数
        val callbackSlot = slot<LoadCallback>()

        // 模拟CategoryRecentLoader类
        mockkConstructor(CategoryRecentLoader::class)
        every { anyConstructed<CategoryRecentLoader>().loadDatas(capture(callbackSlot)) } answers {
            // 模拟加载成功但返回空列表
            callbackSlot.captured.loadSuccess(emptyList())
        }

        // 执行测试
        val result = controller.loadData(card, allCategoryItem)

        // 验证结果
        assertTrue(result.first) // 验证isEmpty为true
        val json = result.second
        // 验证返回的JSON包含正确的图标
        assertEquals(RecentCardDataController.IC_RECENT_ICON, json.getString(RecentCardDataController.KEY_ICON))
        // 验证是最近文件类型
        assertTrue(json.getBoolean(RecentCardDataController.KEY_IS_RECENT))
        // 验证源应用名称正确
        assertEquals("Recent files", json.getString(RecentCardDataController.KEY_SOURCE_APP_NAME))
    }

    /**
     * 测试加载数据失败的情况
     * 验证当加载器失败时，控制器能正确处理并返回失败结果
     */
    @Test
    fun `loadData should return failure result when loader fails`() = runBlocking(testDispatcher) {
        // 准备测试数据
        val card = RecentCard("card1", 3, "Downloads", 1001)
        val allCategoryItem = listOf(RecentPreviewItem(1001, "Downloads"))

        // 模拟CategoryRecentLoader
        val callbackSlot = slot<LoadCallback>()
        mockkConstructor(CategoryRecentLoader::class)
        every { anyConstructed<CategoryRecentLoader>().loadDatas(capture(callbackSlot)) } answers {
            // 模拟加载失败
            callbackSlot.captured.loadFailure()
        }

        // 执行测试
        val result = controller.loadData(card, allCategoryItem)

        // 验证结果
        assertTrue(result.first) // 验证isEmpty为true
        val json = result.second
        // 验证不包含items字段
        assertFalse(json.has(RecentCardDataController.KEY_ITEMS))
        // 验证源应用名称正确
        assertEquals("Downloads", json.getString(RecentCardDataController.KEY_SOURCE_APP_NAME))
    }

    /**
     * 测试加载数据成功且返回超过size数量的项目
     * 验证控制器能正确处理并返回完整结果
     */
    @Test
    fun `loadData should return full result when loader returns more than size items`() = runBlocking(testDispatcher) {
        // 准备测试数据
        val card = RecentCard("card1", 3, "Recent", CategoryHelper.CATEGORY_RECENT)
        val allCategoryItem = listOf(RecentPreviewItem(CategoryHelper.CATEGORY_RECENT, "Recent"))
        // 创建4个测试文件(超过size=3)
        val fileList = listOf(
            createFileBean("file1.txt", "/sdcard/file1.txt", MimeTypeHelper.APPLICATION_TYPE),
            createFileBean("file2.jpg", "/sdcard/file2.jpg", MimeTypeHelper.APPLICATION_TYPE),
            createFileBean("file3.png", "/sdcard/file3.png", MimeTypeHelper.APPLICATION_TYPE),
            createFileBean("file4.pdf", "/sdcard/file4.pdf", MimeTypeHelper.APPLICATION_TYPE)
        )

        // 模拟CategoryRecentLoader
        val callbackSlot = slot<LoadCallback>()
        mockkConstructor(CategoryRecentLoader::class)
        every { anyConstructed<CategoryRecentLoader>().loadDatas(capture(callbackSlot)) } answers {
            // 模拟加载成功并返回文件列表
            callbackSlot.captured.loadSuccess(fileList)
        }

        // 模拟文件提供者和图标获取方法
        mockkStatic(::writeFileProvider, ::getIconPNGByType)
        every { writeFileProvider(any(), any(), any()) } returns Uri.parse("content://thumbnail")
        every { getIconPNGByType(any()) } returns "default_icon"

        // 执行测试
        val result = controller.loadData(card, allCategoryItem)

        // 验证结果
        assertFalse(result.first) // 验证isEmpty为false
        val json = result.second
        // 验证showMore标志为true(因为返回了4个项目而size=3)
        assertTrue(json.getBoolean(RecentCardDataController.KEY_SHOW_MORE))
        val items = json.getJSONArray(RecentCardDataController.KEY_ITEMS)
        // 验证返回的项目数量等于size
        assertEquals(size, items.length())
    }

    /**
     * 辅助方法：创建测试用的BaseFileBean对象
     * @param name 文件名
     * @param path 文件路径
     * @param type 文件类型
     * @return 配置好的BaseFileBean对象
     */
    private fun createFileBean(
        name: String,
        path: String,
        type: Int
    ): BaseFileBean {
        return BaseFileBean().apply {
            mDisplayName = name
            mData = path
            mLocalType = type
            mDateModified = System.currentTimeMillis()
            mSize = 1024
        }
    }
}