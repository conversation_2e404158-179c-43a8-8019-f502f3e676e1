/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SelectionHeapUpFragment.kt
 * Description:
 *     The fragment of heap up page.
 *
 * Version: 1.0
 * Date: 2024-10-19
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-10-19   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.filepreview.IFilesHeapUpFragment
import com.oplus.filemanager.preview.R

class SelectionHeapUpFragment : Fragment(R.layout.fragment_selection_heap_up),
    IFilesHeapUpFragment {

    private companion object {
        private const val TAG = "SelectionHeapUpFragment"
    }

    override val fragmentInstance: Fragment = this

    private var heapUpViewModel: SelectionHeapUpViewModel? = null
    private var selectionHeapUp: ISelectionFilesHeapUp? = null
    private var toHeapUpSelection: List<BaseFileBean>? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val viewModel = ViewModelProvider(this)[TAG, SelectionHeapUpViewModel::class.java]
        heapUpViewModel = viewModel
        toHeapUpSelection?.let {
            viewModel.updateSelectedFiles(view.context, it)
            toHeapUpSelection = null
        }
        val heapUpImpl = ISelectionFilesHeapUp.obtain(this, viewModel)
        val heapUpContainer =
            SelectionHeapUpContainerImpl(view.findViewById(R.id.selection_root_layout))
        heapUpImpl.attachToContainer(heapUpContainer)
        selectionHeapUp = heapUpImpl
    }

    override fun onDestroyView() {
        super.onDestroyView()
        selectionHeapUp?.release()
        selectionHeapUp = null
    }

    override fun onDetach() {
        super.onDetach()
        heapUpViewModel?.stopScanner()
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        // Do nothing at current
    }

    override fun updateSelectionFiles(selection: List<BaseFileBean>) {
        // 外部传入的是最后一个为最新选中，内部实现为第一个是最新选中，需要一次反转以匹配代码实现
        val updateSelection = selection.asReversed()
        val viewModel = heapUpViewModel
        if (viewModel == null) {
            toHeapUpSelection = updateSelection
        } else {
            val ctx = context ?: return
            viewModel.updateSelectedFiles(ctx, updateSelection)
        }
    }

    override fun updatePaddingBottom(systemBarInsetsBottom: Int) {
        Log.d(TAG, "updatePaddingBottom: $systemBarInsetsBottom")
        selectionHeapUp?.updatePaddingBottom(systemBarInsetsBottom)
    }

    override fun onDeselectionAnimationEnd(onEnd: () -> Unit) {
        selectionHeapUp?.onDeselectionAnimationEnd(onEnd)
    }
}