/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ExportFileProcessResponse
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/9 19:00
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/9       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.model.tencent

import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class ExportFileProcessResponse(
    @SerializedName("url") var url: String = "",
    @SerializedName("progress") var progress: Int = 0
) : Parcelable