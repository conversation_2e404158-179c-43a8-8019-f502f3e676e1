package com.coloros.filemanager.filerefactor.fileutils

import android.content.Context
import android.net.Uri
import android.os.Build
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.MediaFileCompat
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.helper.MediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Utils
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import java.lang.Exception

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [Build.VERSION_CODES.S])
class FileMediaHelperTest {

    var mContext: Context? = null

    @MockK
    lateinit var baseFileBean: BaseFileBean

    @MockK
    lateinit var newFileWrapper: BaseFileBean

    @MockK
    lateinit var oldFileWrapper: BaseFileBean

    @MockK
    lateinit var mFileMediaHelper: FileMediaHelper

    @MockK
    lateinit var mUri: Uri

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mContext = RuntimeEnvironment.application
        mFileMediaHelper = mockk(relaxed = true)
        baseFileBean = mockk(relaxed = true)
        newFileWrapper = mockk(relaxed = true)
        oldFileWrapper = mockk(relaxed = true)
        mUri = mockk(relaxed = true)
        MockKAnnotations.init(this)
        MyApplication.init(mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        })
    }

    @After
    @Throws(Exception::class)
    fun tearDown() {
        mContext = null
        unmockkAll()
    }

    @Test
    @Throws(Exception::class)
    fun should_return_uri_when_get_file_uri_with_file() {
        Assert.assertNotNull(mFileMediaHelper.getFileUri(mContext!!, baseFileBean))

        every { baseFileBean.mLocalFileUri }.returns(mUri)
        Assert.assertNotNull(mFileMediaHelper.getFileUri(mContext!!, baseFileBean))

        every { baseFileBean.mLocalFileUri }.returns(null)
        every { baseFileBean.mData }.returns("test")
        Assert.assertNotNull(mFileMediaHelper.getFileUri(mContext!!, baseFileBean))

        every { baseFileBean.mLocalFileUri }.returns(null)
        every { baseFileBean.mData }.returns(null)
        Assert.assertNotNull(mFileMediaHelper.getFileUri(mContext!!, baseFileBean))
    }

    @Test
    fun should_return_path_when_query_path_from_uri_with_uri() {
        Assert.assertNotNull(mFileMediaHelper.queryPathFromUri(mContext!!, mUri))
    }

    @Test
    fun should_return_when_delete_mediaDB_file_with_file() {
        Assert.assertFalse(mFileMediaHelper.deleteMediaDBFile(baseFileBean))
    }

    @Test
    fun should_return_when_updateFileNameInMediaDB() {
        mockkStatic(Utils::class)
        mockkStatic(MediaFileCompat::class)
        Assert.assertFalse(MediaHelper.updateFolderNameInMediaDB(mContext, null, null))

        every { newFileWrapper.mData }.returns("new file.jpg")
        every { Utils.isOperateDatabase(mContext, newFileWrapper.mData) }.returns(false)
        Assert.assertFalse(
            mFileMediaHelper.updateFileNameInMediaDB(
                mContext!!,
                oldFileWrapper,
                newFileWrapper
            )
        )

        every { Utils.isOperateDatabase(mContext, any()) }.returns(true)
        every { oldFileWrapper.mLocalType }.returns(MimeTypeHelper.DIRECTORY_TYPE)
        every { oldFileWrapper.mData }.returns("old file.jpg")
        Assert.assertFalse(
            mFileMediaHelper.updateFileNameInMediaDB(
                mContext!!,
                oldFileWrapper,
                newFileWrapper
            )
        )

        every { oldFileWrapper.mLocalType }.returns(MimeTypeHelper.IMAGE_TYPE)
        every { MediaFileCompat.getMimeTypeForFile(oldFileWrapper.mData) }.returns("image/*")
        every { MediaFileCompat.getMimeTypeForFile(newFileWrapper.mData) }.returns("image/*")
        every {
            Whitebox.invokeMethod<Uri>(
                mFileMediaHelper,
                "getVolumeUri",
                MimeTypeHelper.IMAGE_TYPE, "test.jpg"
            )
        }.returns(mUri)
        Assert.assertFalse(
            mFileMediaHelper.updateFileNameInMediaDB(
                mContext!!,
                oldFileWrapper,
                newFileWrapper
            )
        )
    }

    @Test
    fun should_return_when_getFileTitleInMediaDB() {
        var isflag = Whitebox.invokeMethod<String>(
            mFileMediaHelper,
            "getFileTitleInMediaDB",
            mContext!!, baseFileBean
        )
        if (isflag != null) {
            Assert.assertNotNull(isflag)
        }
    }

    @Test
    fun should_return_when_getImageFileList() {
        val file = BaseFileBean()
        file.mData = "test"
        val fileList: ArrayList<BaseFileBean> = ArrayList()
        fileList.add(file)
        Whitebox.invokeMethod<Unit>(
            mFileMediaHelper,
            "getImageFileList",
            mContext!!, fileList
        )
        Assert.assertEquals(fileList.size, 1)
    }
}