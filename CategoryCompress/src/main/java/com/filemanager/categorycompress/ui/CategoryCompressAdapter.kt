/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.adapter
 * * Version     : 1.0
 * * Date        : 2020/4/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.categorycompress.ui

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.viewholder.FileBrowserGridVH
import com.filemanager.common.viewholder.FileBrowserLargeListVH
import com.filemanager.common.viewholder.NormalListVH
import com.filemanager.common.wrapper.MediaFileWrapper
import java.util.*

private const val TAG = "CategoryCompressAdapter"

class CategoryCompressAdapter : BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>, LifecycleObserver {
    var mScanViewModel = KtConstants.SCAN_MODE_LIST
    private var mIsRtl = false
    private val mSizeCache = HashMap<String, String>()
    private val mUiHandler: Handler
    private var mThreadManager: ThreadManager

    constructor(content: Context, lifecycle: Lifecycle) : super(content) {
        mIsRtl = Utils.isRtl()
        mUiHandler = Handler(Looper.getMainLooper())
        mThreadManager = ThreadManager(lifecycle)
        lifecycle.addObserver(this)
    }

    fun setData(data: ArrayList<MediaFileWrapper>, selectionArray: ArrayList<Int>) {
        mFiles = data
        mSelectionArray = selectionArray
        notifyDataSetChanged()
        mIsRtl = Utils.isRtl()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        when (viewType) {
            KtConstants.SCAN_MODE_GRID -> {
                return FileBrowserGridVH(LayoutInflater.from(parent.context).inflate(FileBrowserGridVH.getLayoutId(), parent, false))
            }
            KtConstants.SCAN_MODE_LIST_LARGE -> return FileBrowserLargeListVH.create(parent)
            else -> {
                return NormalListVH(LayoutInflater.from(parent.context).inflate(com.filemanager.common.R.layout.normal_scan_list_item, parent, false))
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if ((position < 0) || (position >= mFiles.size)) {
            return
        }
        val file = mFiles[position]
        if (holder is NormalListVH) {
            holder.updateDividerVisible(mFiles.size - 1, position)
            holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
            holder.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
        } else if (holder is FileBrowserGridVH) {
            holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
        } else if (holder is FileBrowserLargeListVH) {
            holder.updateDividerVisible(mFiles.size - 1, position)
            holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
            holder.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
        }
        holder.itemView?.let {
            if (DragUtils.getSelectedFiles()?.contains(file) == true && DragUtils.isDragging) {
                it.alpha = DragUtils.SELECTED_ITEMVIEW_ALPHA
            } else {
                it.alpha = 1f
            }
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        mSizeCache?.clear()
        mUiHandler?.removeCallbacksAndMessages(null)
    }

    override fun getItemKey(item: MediaFileWrapper, position: Int): Int? = item.mId

    override fun getItemId(position: Int): Long {
        return getItemKeyByPosition(position)?.toLong() ?: com.oplus.dropdrag.SelectionTracker.NO_LONG_ITEM_ID
    }

    override fun getItemViewType(position: Int): Int {
        if (KtConstants.SCAN_MODE_LIST == mScanViewModel && WindowUtils.supportLargeScreenLayout(mContext)) {
            return KtConstants.SCAN_MODE_LIST_LARGE
        }
        return mScanViewModel
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        if (mScanViewModel == KtConstants.SCAN_MODE_LIST) {
            setChoiceModeAnimFlag(flag)
        }
    }
}
