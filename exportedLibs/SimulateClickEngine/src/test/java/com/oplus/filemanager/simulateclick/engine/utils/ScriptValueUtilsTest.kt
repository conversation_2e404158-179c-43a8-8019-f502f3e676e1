package com.oplus.filemanager.simulateclick.engine.utils

import com.oplus.filemanager.simulateclick.engine.value.IScriptFlagValue
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

/**
 * ScriptValueUtils工具类的单元测试类
 * 测试脚本值转换工具类的各种功能
 */
internal class ScriptValueUtilsTest {

    // 测试数据 - 使用不可变集合防止意外修改
    // 定义权限字典：READ(1), WRITE(2), EXECUTE(4), NONE(0)
    private val testDict = listOf(
        "READ" to 1,
        "WRITE" to 2,
        "EXECUTE" to 4,
        "NONE" to 0
    ).toTypedArray()

    /**
     * 每个测试方法执行前的准备工作
     * 清除所有mock对象的状态
     */
    @Before
    fun setUp() {
        // 确保每次测试前mock都是干净的
        clearAllMocks()
    }

    /**
     * 每个测试方法执行后的清理工作
     * 清除所有mock对象的状态
     */
    @After
    fun tearDown() {
        // 确保每次测试后清理mock
        clearAllMocks()
    }

    /**
     * 测试convertAliasToFlagValue方法在输入为null时的行为
     * 预期返回默认值
     */
    @Test
    fun `convertAliasToFlagValue should return default when value is null`() {
        val result = convertAliasToFlagValue(null, 99, *testDict)
        assertEquals(99, result)
    }

    /**
     * 测试convertAliasToFlagValue方法在输入为空字符串时的行为
     * 预期返回默认值
     */
    @Test
    fun `convertAliasToFlagValue should return default when value is empty`() {
        val result = convertAliasToFlagValue("", 99, *testDict)
        assertEquals(99, result)
    }

    /**
     * 测试convertAliasToFlagValue方法在字典为空时的行为
     * 预期返回默认值
     */
    @Test
    fun `convertAliasToFlagValue should return default when dict is empty`() {
        val result = convertAliasToFlagValue("READ|WRITE", 99)
        assertEquals(99, result)
    }

    /**
     * 测试convertAliasToFlagValue方法正确转换别名的情况
     * 测试各种组合情况
     */
    @Test
    fun `convertAliasToFlagValue should correctly convert aliases`() {
        val testData = listOf(
            "READ" to 1,
            "WRITE" to 2,
            "READ|WRITE" to 3,
            "READ|EXECUTE" to 5,
            "READ|WRITE|EXECUTE" to 7
        )
        
        testData.forEach { (input, expected) ->
            val result = convertAliasToFlagValue(input, 99, *testDict)
            assertEquals(expected, result)
        }
    }

    /**
     * 测试convertAliasToFlagValue方法忽略无效别名的情况
     * 预期只处理有效的别名
     */
    @Test
    fun `convertAliasToFlagValue should ignore invalid aliases`() {
        val result = convertAliasToFlagValue("READ|INVALID|WRITE", 99, *testDict)
        assertEquals(3, result) // READ(1) | WRITE(2) = 3
    }

    /**
     * 测试convertFlagValueToAlias方法在字典为空时的行为
     * 预期返回空字符串
     */
    @Test
    fun `convertFlagValueToAlias should return empty when dict is empty`() {
        val result = convertFlagValueToAlias(3)
        assertEquals("", result)
    }

    /**
     * 测试convertFlagValueToAlias方法正确转换标志值的情况
     * 测试各种标志组合
     */
    @Test
    fun `convertFlagValueToAlias should correctly convert flags`() {
        val testData = listOf(
            1 to "READ",
            3 to "READ|WRITE",
            5 to "READ|EXECUTE",
            7 to "READ|WRITE|EXECUTE",
            0 to "NONE",
            8 to "" // 未定义标志
        )
        
        testData.forEach { (input, expected) ->
            val result = convertFlagValueToAlias(input, *testDict)
            assertEquals(expected, result)
        }
    }

    /**
     * 测试convertFlagValueToAlias方法处理零标志值的情况
     * 当标志值为0且字典中有对应0值的别名时返回该别名
     */
    @Test
    fun `convertFlagValueToAlias should handle zero flag with zero value`() {
        val result = convertFlagValueToAlias(0, *testDict)
        assertEquals("NONE", result)
    }

    /**
     * 测试hasFlag方法正确检查标志是否存在
     * 使用mock对象模拟IScriptFlagValue
     */
    @Test
    fun `hasFlag should correctly check flag presence`() {
        val mockValue = mockk<IScriptFlagValue>()
        every { mockValue.flag } returns 5 // 二进制 101 (READ + EXECUTE)

        assertTrue(mockValue.hasFlag(1)) // READ
        assertTrue(mockValue.hasFlag(4)) // EXECUTE
        assertFalse(mockValue.hasFlag(2)) // WRITE
    }

    /**
     * 测试hasFlag方法使用IScriptFlagValue参数的情况
     * 使用mock对象模拟IScriptFlagValue
     */
    @Test
    fun `hasFlag should work with IScriptFlagValue parameter`() {
        val mockValue = mockk<IScriptFlagValue>()
        every { mockValue.flag } returns 5

        val mockFlag = mockk<IScriptFlagValue>()
        every { mockFlag.flag } returns 4

        assertTrue(mockValue.hasFlag(mockFlag))
    }

    /**
     * 测试contains方法正确检查是否包含另一个标志
     * 使用mock对象模拟IScriptFlagValue
     */
    @Test
    fun `contains should check if flag includes another flag`() {
        val mockValue = mockk<IScriptFlagValue>()
        every { mockValue.flag } returns 7 // 二进制 111 (所有权限)

        assertTrue(mockValue.contains(1)) // READ
        assertTrue(mockValue.contains(2)) // WRITE
        assertTrue(mockValue.contains(4)) // EXECUTE
        assertFalse(mockValue.contains(8)) // 未定义权限
    }

    /**
     * 测试isEquals方法正确检查标志是否完全相等
     * 使用mock对象模拟IScriptFlagValue
     */
    @Test
    fun `isEquals should check exact flag match`() {
        val mockValue = mockk<IScriptFlagValue>()
        every { mockValue.flag } returns 3 // READ + WRITE

        assertTrue(mockValue.isEquals(3))
        assertFalse(mockValue.isEquals(1))
        assertFalse(mockValue.isEquals(7))
    }
}