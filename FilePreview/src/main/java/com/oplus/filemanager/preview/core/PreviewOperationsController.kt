/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - PreviewOperationsController.kt
 * Description:
 *     The file operations on preview file page.
 *
 * Version: 1.0
 * Date: 2024-09-06
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-09-06   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.core

import android.content.res.Configuration
import androidx.annotation.MenuRes
import androidx.annotation.StringRes
import androidx.annotation.VisibleForTesting
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.controller.MoreItemController
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NewFunctionSwitch
import com.oplus.filemanager.interfaze.clouddrive.ICloudDrive
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.utils.logInfo
import com.oplus.filemanager.preview.widget.PreviewOperationsBar
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

internal open class PreviewOperationsController(
    private val fragment: Fragment,
    protected val previewModel: FilePreviewViewModel,
    private val selectPathsCache: MutableMap<Int, List<String>?>,
    protected val normalController: IFileOperate = Injector.injectFactory<IFileOperateApi>()
        ?.createNormalFileOperate(
            fragment.lifecycle,
            CategoryHelper.CATEGORY_FILE_PREVIEW,
            previewModel
        ) ?: throw UnsupportedOperationException("Require FileOperate module!")
) : DefaultLifecycleObserver {

    private companion object {
        private const val TAG = "PreviewOperationsController"
    }

    private var previewOperationsBar: PreviewOperationsBar? = null

    fun setOperatorResultListener(listener: IFileOperate.OperateResultListener) {
        normalController.setResultListener(listener)
    }

    fun setOperateInterceptor(interceptor: IFileOperate) {
        normalController.setInterceptor(interceptor)
    }

    fun attachToOperationsBar(
        operationsBar: PreviewOperationsBar,
        @StringRes openButtonTextId: Int,
        @MenuRes moreMenuResId: Int
    ) {
        fragment.lifecycle.addObserver(this)
        operationsBar.setOpenButtonText(openButtonTextId)
        prepareMoreMenu(operationsBar, moreMenuResId)
        operationsBar.setOnClickOpenButtonListener {
            actionOpen()
        }
        operationsBar.setOnClickLabelButtonListener {
            actionLabel()
        }
        operationsBar.setOnClickSendButtonListener {
            actionSend()
        }
        operationsBar.setOnClickMoreMenuItemListener {
            onClickMoreMenuItem(it.itemId)
        }
        previewOperationsBar = operationsBar
    }

    private fun prepareMoreMenu(operationsBar: PreviewOperationsBar, @MenuRes moreMenuResId: Int) {
        fragment.lifecycleScope.launch(Dispatchers.IO) {
            val supportCloudDisk =
                Injector.injectFactory<ICloudDrive>()?.supportCloudDisk() ?: false
            val checkHelper = MoreOperationsCheckHelper(previewModel.getSelectItems(), supportCloudDisk)
            val removeItemIds = mutableSetOf<Int>()
            if (checkHelper.isCloudDiskAvailable()) {
                Log.d(TAG, "prepareMoreMenu: cloud disk item is available")
            } else {
                removeItemIds.add(R.id.preview_operation_cloud_disk)
            }
            if (checkHelper.isEncryptionAvailable()) {
                Log.d(TAG, "prepareMoreMenu: encryption item is available")
            } else {
                removeItemIds.add(R.id.preview_operation_encryption)
            }
            if (checkHelper.isShortCutAvailable()) {
                Log.d(TAG, "prepareMoreMenu: short cut item is available")
            } else {
                removeItemIds.add(R.id.preview_operation_shortcut)
            }
            withContext(Dispatchers.Main) {
                operationsBar.inflateMoreMenu(moreMenuResId, removeItemIds)
            }
        }
    }

    @VisibleForTesting
    internal fun onClickMoreMenuItem(itemId: Int) {
        selectPathsCache.clear()
        when (itemId) {
            R.id.preview_operation_move -> actionMove()
            R.id.preview_operation_delete -> actionDelete()
            R.id.preview_operation_copy -> actionCopy()
            R.id.preview_operation_rename -> actionRename()
            R.id.preview_operation_detail -> actionDetail()
            R.id.preview_operation_cloud_disk -> actionCloudDisk()
            R.id.preview_operation_encryption -> actionEncryption()
            R.id.preview_operation_shortcut -> actionShortcut()
        }
    }

    private inline fun withActivity(block: (FragmentActivity) -> Unit) {
        val activity = fragment.activity ?: return
        block(activity)
    }

    private inline fun withActivityAndFile(
        block: (FragmentActivity, BaseFileBean) -> Unit
    ) = withActivity { activity ->
        val fileBean = previewModel.pickPreviewFile() ?: return
        block(activity, fileBean)
    }

    private fun actionOpen() = withActivityAndFile { activity, fileBean ->
        Log.d(TAG, "actionOpen: ${fileBean.logInfo()}")
        onActionOpen(activity, fileBean)
    }

    protected open fun onActionOpen(activity: FragmentActivity, fileBean: BaseFileBean) {
        normalController.onFileClick(activity, fileBean, null)
    }

    private fun actionLabel() = withActivity { activity ->
        Log.d(TAG, "actionLabel: ${previewModel.pickPreviewFile()?.logInfo()}")
        normalController.onAddLabel(activity)
    }

    private fun actionSend() = withActivity { activity ->
        Log.d(TAG, "actionSend: ${previewModel.pickPreviewFile()?.logInfo()}")
        normalController.onShare(activity, previewOperationsBar?.getSendButtonRect())
    }

    private fun actionMove() = withActivity { activity ->
        Log.d(TAG, "actionMove: ${previewModel.pickPreviewFile()?.logInfo()}")
        normalController.onSelectCutDir(activity)
    }

    private fun actionDelete() = withActivity { activity ->
        Log.d(TAG, "actionDelete: ${previewModel.pickPreviewFile()?.logInfo()}")
        normalController.onDelete(activity)
    }

    private fun actionCopy() = withActivity { activity ->
        Log.d(TAG, "actionCopy: ${previewModel.pickPreviewFile()?.logInfo()}")
        normalController.onSelectCopyDir(activity)
    }

    private fun actionRename() = withActivity { activity ->
        Log.d(TAG, "actionRename: ${previewModel.pickPreviewFile()?.logInfo()}")
        normalController.onRename(activity)
    }

    private fun actionDetail() = withActivity { activity ->
        Log.d(TAG, "actionDetail: ${previewModel.pickPreviewFile()?.logInfo()}")
        normalController.onDetail(activity)
    }

    private fun actionCloudDisk() = withActivity { activity ->
        Log.d(TAG, "actionCloudDisk: ${previewModel.pickPreviewFile()?.logInfo()}")
        normalController.onUploadCloudDisk(activity)
    }

    private fun actionEncryption() = withActivity { activity ->
        Log.d(TAG, "actionEncryption: ${previewModel.pickPreviewFile()?.logInfo()}")
        normalController.onEncrypt(activity)
    }

    private fun actionShortcut() = withActivity { activity ->
        Log.d(TAG, "actionShortcut: ${previewModel.pickPreviewFile()?.logInfo()}")
        normalController.createShortCut(activity)
    }

    fun onConfigurationChanged(newConfig: Configuration?) {
        normalController.onConfigurationChanged(newConfig)
    }

    fun onSelectPathReturn(requestCode: Int, paths: List<String>?): Unit =
        withActivity { activity ->
            selectPathsCache[requestCode] = paths
            normalController.onSelectPathReturn(activity, requestCode, paths)
        }

    override fun onResume(owner: LifecycleOwner) {
        previewOperationsBar?.checkNavigationGestureSpace()
    }

    private class MoreOperationsCheckHelper(private val selectList: List<BaseFileBean>, supportCloudDisk: Boolean = false) {

        private val supportConfig: MoreItemController.SupportConfig

        init {
            val containDynamicBeans =
                NewFunctionSwitch.isSupportDfmSearch && KtUtils.checkHasDynamicBeans(selectList)
            supportConfig = MoreItemController.SupportConfig(
                isSupportCompress = false,
                isSupportCloudDisk = supportCloudDisk,
                isSupportEncryption = !containDynamicBeans,
                isSupportShortCut = !containDynamicBeans
            )
        }

        fun isCloudDiskAvailable(): Boolean = supportConfig.run {
            isSupportCloudDisk && MoreItemController.checkIfSupportCloud(selectList)
        }

        fun isEncryptionAvailable(): Boolean = supportConfig.run {
            isSupportEncryption && MoreItemController.checkIfSupportEncryption(selectList)
        }

        fun isShortCutAvailable(): Boolean = supportConfig.run {
            isSupportShortCut && MoreItemController.checkSupportCreateShortcut(selectList)
        }
    }
}