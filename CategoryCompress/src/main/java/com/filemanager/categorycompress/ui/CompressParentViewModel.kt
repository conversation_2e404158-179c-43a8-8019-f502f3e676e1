/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.superapp
 * * Version     : 1.0
 * * Date        : 2020/4/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.categorycompress.ui

import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.StatisticsUtils

private const val COMPRESS_SCAN_MODE_SP_KEY = "compress_scan_mode"
class CompressParentViewModel : BaseViewModel() {

    val mBrowseModeState by lazy {
        val lastScanMode = ConfigSharedPreferenceUtils.getInt(COMPRESS_SCAN_MODE_SP_KEY, 0)
        MutableLiveData<Int>(if (lastScanMode == 0) {
            KtConstants.SCAN_MODE_LIST
        } else {
            lastScanMode
        })
    }

    fun clickScanModeItem(context: Context? = null) {
        if (mBrowseModeState.value == KtConstants.SCAN_MODE_LIST) {
            mBrowseModeState.value = KtConstants.SCAN_MODE_GRID
            StatisticsUtils.onCommon(context, StatisticsUtils.SCAN_MODE_ARCHIVES_SWITCH, hashMapOf(StatisticsUtils.SCAN_MODE_ARCHIVES_SWITCH to "0"))
        } else {
            mBrowseModeState.value = KtConstants.SCAN_MODE_LIST
            StatisticsUtils.onCommon(context, StatisticsUtils.SCAN_MODE_ARCHIVES_SWITCH, hashMapOf(StatisticsUtils.SCAN_MODE_ARCHIVES_SWITCH to "1"))
        }
        mBrowseModeState.value?.apply {
            ConfigSharedPreferenceUtils.putInt(COMPRESS_SCAN_MODE_SP_KEY, this)
        }
    }
}