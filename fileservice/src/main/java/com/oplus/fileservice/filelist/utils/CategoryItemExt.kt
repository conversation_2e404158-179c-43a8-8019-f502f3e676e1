/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CategoryItemExt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/12/16 18:12
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/12/16       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.filelist.utils

import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.QQ
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.WECHAT

private const val TAG = "CategoryItemExt"

const val SUPER_APP_QQ_NAME_LOWERCASE = "qq"

fun obtainCategoryItem(
    categoryItems: List<MainCategoryItemsBean>,
    categoryId: Int,
    categoryName: String = ""
): MainCategoryItemsBean? {
    Log.d(TAG, "obtainCategoryItem -> = $categoryId ; categoryName = $categoryName ; ")
    return categoryItems.find {
        if (categoryId == CategoryHelper.CATEGORY_QQ) {
            Log.d(TAG, "obtainCategoryItem -> categoryInfo = $it")
            if (categoryName.isEmpty()) {
                return@find false
            }
            if (categoryName.contains(".")) { // 外部传入的是包名
                return@find it.packageName == categoryName
            }
            if (categoryName.equals(it.name, ignoreCase = true)) { // 外部传入的是应用名称
                return@find true
            }
            if (categoryName.lowercase() == SUPER_APP_QQ_NAME_LOWERCASE) {
                it.packageName == QQ
            } else {
                it.packageName == WECHAT
            }
        } else {
            it.itemType == categoryId
        }
    }
}