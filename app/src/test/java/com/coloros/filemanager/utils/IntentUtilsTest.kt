package com.coloros.filemanager.utils

import android.content.Intent
import android.net.Uri
import android.os.Parcelable
import android.text.TextUtils
import android.webkit.MimeTypeMap
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.IntentUtils
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class IntentUtilsTest {

    @MockK
    lateinit var intent: Intent

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        intent = mockk(relaxed = true)
        mockkStatic(FileTypeUtils::class)
        every { FileTypeUtils.getExtension(any()) }.answers { callOriginal() }
    }


    @Test
    fun `should verify when process intent without`() {
        mockkObject(IntentUtils)
        every { intent.setDataAndType(any(), any()) }.returns(intent)
        IntentUtils.processIntent(intent, null, "test", "test")
        verify { intent.setDataAndType(any(), any()) }

        var uri = mockk<Uri>(relaxed = true)
        mockkStatic(TextUtils::class)
        every { TextUtils.isEmpty(null) }.returns(true)

        IntentUtils.processIntent(intent, uri, null, "test")
        verify { intent.setDataAndType(any(), any()) }

        mockkStatic(MimeTypeMap::class)
        every { MimeTypeMap.getSingleton().getMimeTypeFromExtension(any()) }.returns(null)

        IntentUtils.processIntent(intent, uri, "test", "test")
        verify { intent.setDataAndType(any(), any()) }
        every { MimeTypeMap.getSingleton().getMimeTypeFromExtension(any()) }.returns("test")

        IntentUtils.processIntent(intent, uri, "test", "test")
        verify { intent.setDataAndType(any(), any()) }
    }

    @Test
    fun `should verify when get string without`() {
        Assert.assertEquals("", IntentUtils.getString(null, null))

        every { intent.getStringExtra("test") }.throws(Exception("test-error"))
        Assert.assertEquals("", IntentUtils.getString(intent, "test"))

        every { intent.getStringExtra("test") }.returns("test")
        Assert.assertEquals("test", IntentUtils.getString(intent, "test"))
    }


    @Test
    fun `should verify when get int without`() {
        Assert.assertEquals(0, IntentUtils.getInt(null, null, 0))

        every { intent.getIntExtra("test", 0) }.throws(Exception("test-error"))
        Assert.assertEquals(0, IntentUtils.getInt(intent, "test", 0))

        every { intent.getIntExtra("test", 0) }.returns(111)
        Assert.assertEquals(111, IntentUtils.getInt(intent, "test", 0))
    }

    @Test
    fun `should verify when get boolean without`() {
        Assert.assertEquals(false, IntentUtils.getBoolean(null, "test", false))

        every { intent.getBooleanExtra("test", false) }.throws(Exception("test-error"))
        Assert.assertEquals(false, IntentUtils.getBoolean(intent, "test", false))

        every { intent.getBooleanExtra("test", true) }.returns(true)
        Assert.assertEquals(true, IntentUtils.getBoolean(intent, "test", true))
    }

    @Test
    fun `should verify when get parcelable arrayList without`() {
        Assert.assertEquals(null, IntentUtils.getParcelableArrayList(null, "test"))

        every { intent.getParcelableArrayListExtra<Parcelable>("test") }.throws(Exception("test-error"))
        Assert.assertEquals(null, IntentUtils.getParcelableArrayList(intent, "test"))

        every { intent.getParcelableArrayListExtra<Parcelable>("test") }.returns(null)
        Assert.assertEquals(null, IntentUtils.getParcelableArrayList(intent, "test"))
    }

    @Test
    fun `should verify when get string arrayList without`() {
        Assert.assertEquals(null, IntentUtils.getStringArrayList(null, "test"))

        every { intent.getStringArrayListExtra("test") }.throws(Exception("test-error"))
        Assert.assertEquals(null, IntentUtils.getStringArrayList(intent, "test"))

        every { intent.getStringArrayListExtra("test") }.returns(null)
        Assert.assertEquals(null, IntentUtils.getStringArrayList(intent, "test"))
    }

    //getIntegerArrayList
    @Test
    fun `should verify when get integer arrayList without`() {
        Assert.assertEquals(null, IntentUtils.getIntegerArrayList(null, "test"))

        every { intent.getIntegerArrayListExtra("test") }.throws(Exception("test-error"))
        Assert.assertEquals(null, IntentUtils.getIntegerArrayList(intent, "test"))

        every { intent.getIntegerArrayListExtra("test") }.returns(null)
        Assert.assertEquals(null, IntentUtils.getIntegerArrayList(intent, "test"))
    }

    @After
    fun afterTests() {
        unmockkStatic(FileTypeUtils::class)
        unmockkAll()
    }
}