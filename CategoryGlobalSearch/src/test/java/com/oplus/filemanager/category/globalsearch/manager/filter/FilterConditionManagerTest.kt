package com.oplus.filemanager.category.globalsearch.manager.filter

import android.content.ContentResolver
import android.content.ContentUris
import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.util.ArrayMap
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.oplus.filemanager.category.globalsearch.bean.CategoryFileWrapper
import com.oplus.filemanager.provider.store.GlobalSearchStore
import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.util.*

/**
 * FilterConditionManager的单元测试类
 * 用于测试FilterConditionManager中各种过滤条件管理功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class FilterConditionManagerTest {

    // 模拟Context对象
    @MockK
    private lateinit var mockContext: Context

    // 模拟ContentResolver对象，使用RelaxedMockK可以自动处理未指定的方法调用
    @RelaxedMockK
    private lateinit var mockContentResolver: ContentResolver

    // 模拟Cursor对象，使用RelaxedMockK可以自动处理未指定的方法调用
    @RelaxedMockK
    private lateinit var mockCursor: Cursor

    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 模拟MyApplication单例
     * 3. 模拟PreferencesUtils工具类
     * 4. 模拟Log静态类
     * 5. 设置模拟的ContentResolver
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkObject(MyApplication)
        mockkObject(PreferencesUtils)
        mockkStatic(Log::class)
        every { MyApplication.sAppContext } returns mockContext
        every { mockContext.contentResolver } returns mockContentResolver
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试loadFilter方法
     * 验证从数据库加载过滤条件的正确性
     */
    @Test
    fun testLoadFilter() {
        // 模拟cursor行为
        // 第一次moveToNext返回true，第二次返回true，第三次返回false
        every { mockContentResolver.query(any(), any(), any(), any(), any()) } returns mockCursor
        every { mockCursor.moveToNext() } returnsMany listOf(true, true, false)
        // 模拟两次getInt调用，分别返回1和2作为filterId
        every { mockCursor.getInt(0) } returnsMany listOf(1, 2)
        // 模拟两次getInt调用，分别返回10和20作为filterValue
        every { mockCursor.getInt(1) } returnsMany listOf(10, 20)

        // 调用被测方法
        val result = FilterConditionManager.loadFilter()
        
        // 验证结果
        assertEquals(2, result.size)  // 验证返回的map大小
        assertEquals(10, result[1])   // 验证第一个过滤条件的值
        assertEquals(20, result[2])   // 验证第二个过滤条件的值
    }

    /**
     * 测试loadFilter方法在异常情况下的处理
     * 验证当查询抛出异常时返回空map
     */
    @Test
    fun testLoadFilterWithException() {
        // 模拟查询抛出异常
        every { mockContentResolver.query(any(), any(), any(), any(), any()) } throws Exception("Test exception")
        
        // 调用被测方法
        val result = FilterConditionManager.loadFilter()
        
        // 验证结果为空
        assertTrue(result.isEmpty())
    }

    /**
     * 测试insertFilter方法在插入失败的情况
     * 验证当插入操作返回null时返回-1
     */
    @Test
    fun testInsertFilterFailed() {
        // 模拟过滤条件对象
        val condition = mockk<FilterCondition>()
        every { condition.id } returns 1
        every { condition.desc } returns "test"
        every { condition.getAllFilterItems() } returns 0
        // 模拟插入操作返回null
        every { mockContentResolver.insert(any(), any()) } returns null

        // 调用被测方法
        val result = FilterConditionManager.insertFilter(condition)
        
        // 验证返回-1表示失败
        assertEquals(-1L, result)
    }

    /**
     * 测试removeFilter方法
     * 验证删除多个过滤条件的正确性
     */
    @Test
    fun testRemoveFilter() {
        // 模拟两个过滤条件
        val conditions = listOf(mockk<FilterCondition>(), mockk<FilterCondition>())
        every { conditions[0].id } returns 1
        every { conditions[1].id } returns 2
        // 模拟删除操作返回2表示删除了2条记录
        every { mockContentResolver.delete(any(), any(), any()) } returns 2

        // 调用被测方法
        val result = FilterConditionManager.removeFilter(conditions)
        
        // 验证返回删除的记录数
        assertEquals(2, result)
    }

    /**
     * 测试removeFilter方法在空列表情况下的处理
     * 验证当传入空列表时返回-1
     */
    @Test
    fun testRemoveEmptyFilter() {
        // 调用被测方法，传入空列表
        val result = FilterConditionManager.removeFilter(emptyList())
        
        // 验证返回-1
        assertEquals(-1, result)
    }

    /**
     * 测试updateFilter方法
     * 验证更新过滤条件的正确性
     */
    @Test
    fun testUpdateFilter() {
        // 模拟过滤条件对象
        val condition = mockk<FilterCondition>()
        every { condition.id } returns 1
        every { condition.desc } returns "test"
        every { condition.getAllFilterItems() } returns 0
        // 模拟更新操作返回1表示更新了1条记录
        every { mockContentResolver.update(any(), any(), any(), any()) } returns 1

        // 调用被测方法
        val result = FilterConditionManager.updateFilter(condition)
        
        // 验证返回更新的记录数
        assertEquals(1, result)
    }

    /**
     * 测试clearFilter方法
     * 验证清空所有过滤条件的正确性
     */
    @Test
    fun testClearFilter() {
        // 模拟删除操作返回1表示删除了1条记录
        every { mockContentResolver.delete(any(), any(), any()) } returns 1

        // 调用被测方法
        val result = FilterConditionManager.clearFilter()
        
        // 验证返回删除的记录数
        assertEquals(1, result)
    }
}