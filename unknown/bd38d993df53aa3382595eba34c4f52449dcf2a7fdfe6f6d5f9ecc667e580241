<?xml version="1.0"?>
<services xmlns:xsi="http://www.oplus.com/services/2022/schemas"
    version="1"
    xmlns="http://www.oplus.com/services"
    xsi:schemaLocation="service-define_1.xsd">
    <service
        name="文件操作服务"
        description="调用该服务更新文件操作到手机端"
        domain="文件服务"
        serviceId="4000002"
        versionCode="1"
        versionName="ver.1.20220222.01">
        <!-- 服务调用的定义 -->
        <invoke>
            <!--调用协议-->
            <protocol
                apiType="ANDROID_MESSENGER"
                type="ApiProxy">
                <attributes>
                    <attribute
                        key="bindService"
                        value="com.oplus.fileservice.operate.FileOperateService" />
                    <attribute
                        key="appId"
                        value="com.coloros.filemanager" />
                    <attribute
                        key="msgType"
                        value="2" />
                    <attribute
                        key="keepBinding"
                        value="true" />
                    <attribute
                        key="invokeOnBind"
                        value="false" />
                </attributes>
            </protocol>
            <!--入参列表-->
            <inputParams>
                <param
                    defaultValue=""
                    description="文件操作信息"
                    paramName="operateInfo"
                    paramType="string"
                    required="true" />
            </inputParams>
            <!--出参列表-->
            <outputParams>
                <param
                    defaultValue=""
                    description="操作结果"
                    paramName="result"
                    paramType="string"
                    required="true" />
            </outputParams>
            <!--和标准服务的参数转换规则-->
            <paramConvertRules>
                <paramConvertRule
                    description=""
                    paramName="operate"
                    paramType="1"
                    ruleType="1"
                    standardParamName="destination" />
            </paramConvertRules>
        </invoke>
        <!--服务部署的定义 -->
        <deploy>
            <provider type="NULL" />
        </deploy>
    </service>
    <service
        name="文件操作取消服务"
        description="通过requestId取消正在执行的操作"
        domain="文件服务"
        serviceId="4000003"
        versionCode="1"
        versionName="ver.1.20220222.01">
        <!-- 服务调用的定义 -->
        <invoke>
            <!--调用协议-->
            <protocol
                apiType="ANDROID_MESSENGER"
                type="ApiProxy">
                <attributes>
                    <attribute
                        key="bindService"
                        value="com.oplus.fileservice.operate.FileCancelOperateService" />
                    <attribute
                        key="appId"
                        value="com.coloros.filemanager" />
                    <attribute
                        key="msgType"
                        value="1" />
                </attributes>
            </protocol>
            <!--入参列表-->
            <inputParams>
                <param
                    defaultValue=""
                    description="请求ID"
                    paramName="requestId"
                    paramType="string"
                    required="true" />
            </inputParams>
            <outputParams>
                <param
                    defaultValue=""
                    description="取消结果"
                    paramName="result"
                    paramType="string"
                    required="true" />
            </outputParams>
            <!--和标准服务的参数转换规则-->
            <paramConvertRules>
                <paramConvertRule
                    description=""
                    paramName="operate"
                    paramType="1"
                    ruleType="1"
                    standardParamName="destination" />
            </paramConvertRules>
        </invoke>
        <!--服务部署的定义 -->
        <deploy>
            <provider type="NULL" />
        </deploy>
    </service>
    <service
        name="文件跟目录服务"
        description="调用该服务获取文件跟目录"
        domain="文件服务"
        serviceId="4000011"
        versionCode="1"
        versionName="ver.1.20220222.01">
        <!-- 服务调用的定义 -->
        <invoke>
            <!--调用协议-->
            <protocol
                apiType="ANDROID_MESSENGER"
                type="ApiProxy">
                <attributes>
                    <attribute
                        key="msgType"
                        value="1" />
                    <attribute
                        key="appId"
                        value="com.coloros.filemanager" />
                    <attribute
                        key="bindService"
                        value="com.oplus.fileservice.filelist.RootFilesService" />
                </attributes>
            </protocol>
            <!--入参列表-->
            <!--出参列表-->
            <outputParams>
                <param
                    defaultValue=""
                    description="跟目录列表结果"
                    paramName="data"
                    paramType="string"
                    required="true" />
            </outputParams>
            <!--和标准服务的参数转换规则-->
            <paramConvertRules>
                <paramConvertRule
                    description=""
                    paramName="category"
                    paramType="1"
                    ruleType="1"
                    standardParamName="destination" />
            </paramConvertRules>
        </invoke>
        <!--服务部署的定义 -->
        <deploy>
            <provider type="NULL" />
        </deploy>
    </service>
    <service
        name="文件路径列表服务"
        description="调用该服务获取对应路径下文件列表"
        domain="文件服务"
        serviceId="4000013"
        versionCode="1"
        versionName="ver.1.20220222.01">
        <!-- 服务调用的定义 -->
        <invoke>
            <!--调用协议-->
            <protocol
                apiType="ANDROID_MESSENGER"
                type="ApiProxy">
                <attributes>
                    <attribute
                        key="msgType"
                        value="1" />
                    <attribute
                        key="appId"
                        value="com.coloros.filemanager" />
                    <attribute
                        key="bindService"
                        value="com.oplus.fileservice.filelist.FilePathListsService" />
                    <attribute
                        key="keepBinding"
                        value="false" />
                    <attribute
                        key="invokeOnBind"
                        value="true" />
                </attributes>
            </protocol>
            <!--入参列表-->
            <inputParams>
                <param
                    defaultValue=""
                    description="文件绝对路径"
                    paramName="filePath"
                    paramType="string"
                    required="true" />
                <param
                    defaultValue="1"
                    description="访问页面数"
                    paramName="pageNo"
                    paramType="int"
                    required="true" />
                <param
                    defaultValue="10"
                    description="文件列表页面大小"
                    paramName="pageSize"
                    paramType="int"
                    required="true" />
                <param
                    defaultValue="0"
                    description="文件列表排序"
                    paramName="sortOrder"
                    paramType="int"
                    required="false" />
            </inputParams>
            <!--出参列表-->
            <outputParams>
                <param
                    defaultValue=""
                    description="目录列表结果"
                    paramName="data"
                    paramType="string"
                    required="true" />
            </outputParams>
            <!--和标准服务的参数转换规则-->
            <paramConvertRules>
                <paramConvertRule
                    description=""
                    paramName="category"
                    paramType="1"
                    ruleType="1"
                    standardParamName="destination" />
            </paramConvertRules>
        </invoke>
        <!--服务部署的定义 -->
        <deploy>
            <provider type="NULL" />
        </deploy>
    </service>
    <service
        name="文件路文件夹服务"
        description="调用该服务获取对应路径下文件夹"
        domain="文件服务"
        serviceId="4000014"
        versionCode="1"
        versionName="ver.1.20220222.01">
        <!-- 服务调用的定义 -->
        <invoke>
            <!--调用协议-->
            <protocol
                apiType="ANDROID_MESSENGER"
                type="ApiProxy">
                <attributes>
                    <attribute
                        key="msgType"
                        value="1" />
                    <attribute
                        key="appId"
                        value="com.coloros.filemanager" />
                    <attribute
                        key="bindService"
                        value="com.oplus.fileservice.filelist.FileSelectPathService" />
                </attributes>
            </protocol>
            <!--入参列表-->
            <inputParams>
                <param
                    defaultValue=""
                    description="文件绝对路径"
                    paramName="filePath"
                    paramType="string"
                    required="true" />
                <param
                    defaultValue="1"
                    description="访问页面数"
                    paramName="pageNo"
                    paramType="int"
                    required="true" />
                <param
                    defaultValue="10"
                    description="文件列表页面大小"
                    paramName="pageSize"
                    paramType="int"
                    required="true" />
                <param
                    defaultValue="0"
                    description="文件列表排序"
                    paramName="sortOrder"
                    paramType="int"
                    required="false" />
            </inputParams>
            <!--出参列表-->
            <outputParams>
                <param
                    defaultValue=""
                    description="目录列表结果"
                    paramName="data"
                    paramType="string"
                    required="true" />
            </outputParams>
            <!--和标准服务的参数转换规则-->
            <paramConvertRules>
                <paramConvertRule
                    description=""
                    paramName="category"
                    paramType="1"
                    ruleType="1"
                    standardParamName="destination" />
            </paramConvertRules>
        </invoke>
        <!--服务部署的定义 -->
        <deploy>
            <provider type="NULL" />
        </deploy>
    </service>

    <service
        name="文件分类服务"
        description="调用该服务获取分类下文件"
        domain="文件服务"
        serviceId="4000015"
        versionCode="1"
        versionName="ver.1.20220222.01">
        <!-- 服务调用的定义 -->
        <invoke>
            <!--调用协议-->
            <protocol
                apiType="ANDROID_MESSENGER"
                type="ApiProxy">
                <attributes>
                    <attribute
                        key="msgType"
                        value="1" />
                    <attribute
                        key="appId"
                        value="com.coloros.filemanager" />
                    <attribute
                        key="bindService"
                        value="com.oplus.fileservice.filelist.CategoryFilesService" />
                    <attribute
                        key="keepBinding"
                        value="false" />
                    <attribute
                        key="invokeOnBind"
                        value="true" />
                </attributes>
            </protocol>
            <!--入参列表-->
            <inputParams>
                <param
                    defaultValue=""
                    description="图片集相对路径"
                    paramName="relativePath"
                    paramType="String"
                    required="false" />
                <param
                    defaultValue="1"
                    description="访问页面数"
                    paramName="pageNo"
                    paramType="int"
                    required="true" />
                <param
                    defaultValue="50"
                    description="文件列表页面大小"
                    paramName="pageSize"
                    paramType="int"
                    required="true" />
                <param
                    defaultValue="0"
                    description="文件列表排序"
                    paramName="sortOrder"
                    paramType="int"
                    required="false" />
            </inputParams>
            <!--出参列表-->
            <outputParams>
                <param
                    defaultValue=""
                    description="目录列表结果"
                    paramName="data"
                    paramType="string"
                    required="true" />
            </outputParams>
            <!--和标准服务的参数转换规则-->
            <paramConvertRules>
                <paramConvertRule
                    description=""
                    paramName="category"
                    paramType="1"
                    ruleType="1"
                    standardParamName="destination" />
            </paramConvertRules>
        </invoke>
        <!--服务部署的定义 -->
        <deploy>
            <provider type="NULL" />
        </deploy>
    </service>
    <service
        name="图片服务"
        description="调用该服务获取分类下文件"
        domain="文件服务"
        serviceId="4000016"
        versionCode="1"
        versionName="ver.1.20220222.01">
        <!-- 服务调用的定义 -->
        <invoke>
            <!--调用协议-->
            <protocol
                apiType="ANDROID_MESSENGER"
                type="ApiProxy">
                <attributes>
                    <attribute
                        key="msgType"
                        value="1" />
                    <attribute
                        key="appId"
                        value="com.coloros.filemanager" />
                    <attribute
                        key="bindService"
                        value="com.oplus.fileservice.filelist.AlbumFilesService" />
                </attributes>
            </protocol>
            <!--入参列表-->
            <inputParams>
                <param
                    defaultValue=""
                    description="文件绝对路径"
                    paramName="categoryId"
                    paramType="int"
                    required="true" />
                <param
                    defaultValue="1"
                    description="访问页面数"
                    paramName="pageNo"
                    paramType="int"
                    required="true" />
                <param
                    defaultValue="50"
                    description="文件列表页面大小"
                    paramName="pageSize"
                    paramType="int"
                    required="true" />
                <param
                    defaultValue="0"
                    description="文件列表排序"
                    paramName="sortOrder"
                    paramType="int"
                    required="false" />
            </inputParams>
            <!--出参列表-->
            <outputParams>
                <param
                    defaultValue=""
                    description="目录列表结果"
                    paramName="data"
                    paramType="string"
                    required="true" />
            </outputParams>
            <!--和标准服务的参数转换规则-->
            <paramConvertRules>
                <paramConvertRule
                    description=""
                    paramName="category"
                    paramType="1"
                    ruleType="1"
                    standardParamName="destination" />
            </paramConvertRules>
        </invoke>
        <!--服务部署的定义 -->
        <deploy>
            <provider type="NULL" />
        </deploy>
    </service>
    <service
        name="图集服务"
        description="调用该服务获取图集下图片列表数据"
        domain="文件服务"
        serviceId="4000017"
        versionCode="1"
        versionName="ver.1.20220222.01">
        <!-- 服务调用的定义 -->
        <invoke>
            <!--调用协议-->
            <protocol
                apiType="ANDROID_MESSENGER"
                type="ApiProxy">
                <attributes>
                    <attribute
                        key="msgType"
                        value="1" />
                    <attribute
                        key="appId"
                        value="com.coloros.filemanager" />
                    <attribute
                        key="bindService"
                        value="com.oplus.fileservice.filelist.AlbumSetFilesService" />
                </attributes>
            </protocol>
            <!--入参列表-->
            <inputParams>
                <param
                    defaultValue=""
                    description="图集Key"
                    paramName="bucketKey"
                    paramType="String"
                    required="true" />
                <param
                    defaultValue="1"
                    description="访问页面数"
                    paramName="pageNo"
                    paramType="int"
                    required="true" />
                <param
                    defaultValue="50"
                    description="文件列表页面大小"
                    paramName="pageSize"
                    paramType="int"
                    required="true" />
                <param
                    defaultValue="0"
                    description="文件列表排序"
                    paramName="sortOrder"
                    paramType="int"
                    required="false" />
            </inputParams>
            <!--出参列表-->
            <outputParams>
                <param
                    defaultValue=""
                    description="目录列表结果"
                    paramName="data"
                    paramType="string"
                    required="true" />
            </outputParams>
            <!--和标准服务的参数转换规则-->
            <paramConvertRules>
                <paramConvertRule
                    description=""
                    paramName="category"
                    paramType="1"
                    ruleType="1"
                    standardParamName="destination" />
            </paramConvertRules>
        </invoke>
        <!--服务部署的定义 -->
        <deploy>
            <provider type="NULL" />
        </deploy>
    </service>
</services>