/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IPreviewAudioPlayer.kt
 * Description:
 *     The interface for audio player.
 *
 * Version: 1.0
 * Date: 2024-09-14
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-09-14   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.audio

import android.content.Context
import android.net.Uri
import androidx.annotation.MainThread

internal interface IPreviewAudioPlayer {

    fun setOnPlayPreparedListener(listener: OnPlayPreparedListener): IPreviewAudioPlayer

    fun setOnPlayErrorListener(listener: OnPlayErrorListener): IPreviewAudioPlayer

    fun setOnPlayStateChangedListener(listener: OnPlayStateChangedListener): IPreviewAudioPlayer

    fun setOnPlayProgressUpdateListener(listener: OnPlayProgressUpdateListener): IPreviewAudioPlayer

    fun setPlayProgressUpdateInterval(interval: Long): IPreviewAudioPlayer

    fun setPrepareTimeout(timeout: Long): IPreviewAudioPlayer

    fun prepareAndPlay(uri: Uri)

    fun resume()

    fun pause()

    fun stop()

    fun release()

    fun seekTo(position: Long, previewMode: Boolean)

    fun setVolume(volume: Float)

    fun getVolume(): Float

    fun isPlaying(): Boolean

    fun interface OnPlayPreparedListener {
        @MainThread
        fun onPrepared(isPlayStarted: Boolean)
    }

    fun interface OnPlayProgressUpdateListener {
        @MainThread
        fun onProgressUpdate(position: Long, duration: Long)
    }

    fun interface OnPlayErrorListener {
        @MainThread
        fun onError(errorType: Int, errorCode: Int, extra: String?)
    }

    fun interface OnPlayStateChangedListener {
        @MainThread
        fun onPlayStateChanged(isPlaying: Boolean)
    }

    companion object {
        @JvmStatic
        fun obtain(context: Context): IPreviewAudioPlayer = TBLPreviewAudioPlayer(context)
    }
}