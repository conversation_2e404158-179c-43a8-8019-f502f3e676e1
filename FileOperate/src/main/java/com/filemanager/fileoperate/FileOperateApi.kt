/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.category.globalsearch.GlobalSearchApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/5/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.fileoperate

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.DELAY_LOAD_DATA_TIME
import com.filemanager.common.dragselection.action.DragParseData
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.GetMediaDurationUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.NormalFileOperateController.Companion.doOnCutActionDone
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.copy.FileActionCopy
import com.filemanager.fileoperate.copy.FileCopyObserver
import com.filemanager.fileoperate.cut.FileActionCut
import com.filemanager.fileoperate.cut.FileCutObserver
import com.filemanager.fileoperate.open.OpenFileFactory
import com.filemanager.fileoperate.save.FileActionSave
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import com.oplus.filemanager.room.model.FileLabelMappingEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.math.min

object FileOperateApi : IFileOperateApi {

    const val TAG = "FileOperateApi"
    const val TEXT_EXT = ".txt"
    const val HTML_EXT = ".html"
    const val FILE_NAME_PREFIX = "Transfer Dock_Text_"
    const val TIME_PATTERN = "yyyyMMddHHmmss"

    override fun getOpenFileIntent(
        context: Context,
        localType: Int,
        localFileUri: Uri,
        filePath: String
    ): Intent {
        return OpenFileFactory.getOpenFileIntent(context, localType, localFileUri, filePath)
    }

    override fun saveFile(activity: Activity, parseData: DragParseData, destPath: String) {
        Log.d(TAG, "saveFile uriList = ${parseData.uriList.size} textList = ${parseData.textList.size} htmlList = ${parseData.htmlList.size}")
        (activity as BaseVMActivity).lifecycleScope.launch(Dispatchers.IO) {
            val listBeans = getFileBeanList(activity, parseData, false)
            createFolderIfNotExist(destPath)
            if (listBeans.size > 0) {
                withContext(Dispatchers.Main) {
                    FileActionSave(activity, listBeans, PathFileWrapper(destPath)).execute(
                        object : FileCopyObserver(activity) {
                            override fun onActionDone(result: Boolean, data: Any?) {
                                Log.d(TAG, "onActionDone")
                                activity.onRefreshData()
                            }

                            override fun onActionCancelled() {
                                Log.d(TAG, "onActionCancelled")
                                activity.onRefreshData()
                            }

                            override fun onActionReloadData() {
                                Log.d(TAG, "onActionReloadData")
                            }

                            override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
                                val superCallback = { super.onChanged(context, result) }
                                return handleOnChanged(activity, result, superCallback)
                            }
                        })
                }
            }
        }
    }

    override fun cutMediaFile(
        activity: Activity,
        parseData: DragParseData,
        destPath: String,
        fragmentCategoryType: Int,
        dragInternal: Boolean,
        delayLoadData: Boolean
    ) {
        Log.d(TAG, "cutMediaFile uriList = ${parseData.uriList.size} , folderList = ${parseData.folderList.size} , "
                    + "destPath $destPath categoryType $fragmentCategoryType dragInternal $dragInternal")

        //在最近页面进行拖拽，执行最近页面的移动逻辑
        if (fragmentCategoryType == CategoryHelper.CATEGORY_RECENT) {
            Log.d(TAG, "cutMediaFile dragFileInRecentOperate")
            if (Injector.injectFactory<IMain>()?.dragFileInRecentOperate(activity, IFileOperate.OP_CUT, destPath) == true) return
        }

        (activity as BaseVMActivity).lifecycleScope.launch(Dispatchers.IO) {
            var listBeans = getFileBeanList(activity, parseData, true)
            listBeans = findMinLayerFolderListBeans(listBeans)
            createFolderIfNotExist(destPath)
            withContext(Dispatchers.Main) {
                val cutAction = FileActionCut(activity, listBeans, PathFileWrapper(destPath), fragmentCategoryType)
                cutAction.dragToCut = true
                cutAction.isMacDragToCut = parseData.isMacDragData
                val observer = object : FileCutObserver(activity) {
                    override fun onActionDone(result: Boolean, data: Any?) {
                        Log.d(TAG, "cutFile onActionDone result $result delayLoadData $delayLoadData")
                        doOnCutActionDone(activity, destPath, listBeans, result)
                        if (delayLoadData) {
                            activity.launch(Dispatchers.Main) {
                                delay(DELAY_LOAD_DATA_TIME)
                                activity.onRefreshData()
                            }
                        }
                        //外部拖入的文件，移动后，需要通知拖入方刷新
                        if (!dragInternal) {
                            val intent = Intent(KtConstants.ACTION_DRAG_FILE_CHANGED)
                            intent.putExtra(KtConstants.DRAG_APP_PACKAGE, activity.packageName)
                            activity.sendBroadcast(intent, KtConstants.PROTECT_PERMISSION)
                        }
                    }

                    override fun onActionCancelled() {
                        Log.d(TAG, "cutFile onActionDone")
                        activity.onRefreshData()
                    }

                    override fun onActionReloadData() {
                        Log.d(TAG, "cutFile onActionReloadData")
                    }

                    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
                        Log.d(TAG, "cutFile onChanged result $result")
                        return super.onChanged(context, result)
                    }
                }
                cutAction.execute(observer)
            }
        }
    }

    override fun copyMediaFile(activity: Activity, parseData: DragParseData, destPath: String) {
        Log.d(TAG, "copyMediaFile uriList = ${parseData.uriList.size} folderPathList = ${parseData.folderList.size} ," +
                "  destPath $destPath")
        (activity as BaseVMActivity).lifecycleScope.launch(Dispatchers.IO) {
            var listBeans = getFileBeanList(activity, parseData, true)
            listBeans = findMinLayerFolderListBeans(listBeans)
            createFolderIfNotExist(destPath)
            withContext(Dispatchers.Main) {
                val copyAction = FileActionCopy(activity, listBeans, PathFileWrapper(destPath))
                val observer =  object : FileCopyObserver(activity) {
                    override fun onActionDone(result: Boolean, data: Any?) {
                        Log.d(TAG, "copyMediaFile onActionDone")
                        activity.onRefreshData()
                    }

                    override fun onActionCancelled() {
                        Log.d(TAG, "copyMediaFile onActionCancelled")
                        activity.onRefreshData()
                    }

                    override fun onActionReloadData() {
                        Log.d(TAG, "copyMediaFile onActionReloadData")
                    }

                    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
                        val superCallback = { super.onChanged(context, result) }
                        return handleOnChanged(activity, result, superCallback)
                    }
                }
                copyAction.execute(observer)
            }
        }
    }

    private fun handleOnChanged(activity: Activity, result: Pair<Any, Any>, superCallback: () -> Boolean): Boolean {
        Log.d(TAG, "handleOnChanged result $result")
        val changed: Boolean
        when (result.first) {
            ACTION_DONE -> {
                FileOperateUtil.dropFileNotice(activity, result.second as? String)
                changed = false
            }

            ACTION_FAILED -> {
                changed = if (result.second is Pair<*, *>) {
                    superCallback()
                } else {
                    FileOperateUtil.dropFileFailed()
                    false
                }
            }

            else -> changed = superCallback()
        }
        return changed
    }

    override fun createNormalFileOperate(
        lifecycle: Lifecycle,
        categoryType: Int,
        viewModel: SelectionViewModel<out BaseFileBean, out BaseUiModel<out BaseFileBean>>,
        sortType: Int?
    ): IFileOperate = NormalFileOperateController(lifecycle, categoryType, viewModel, sortType)

    override fun addLabel(activity: Activity, labelId: Long, fileBeanList: ArrayList<BaseFileBean>) {
        (activity as BaseVMActivity).lifecycleScope.launch(Dispatchers.IO) {
            val labelEntry = FileLabelDBHelper.getFileLabelById(labelId)
            Log.d(TAG, "addLabel labelId $labelId")
            if (labelEntry == null) {
                Log.d(TAG, "addLabel -> labelEntry is null, return")
                return@launch
            }
            val added = mappingFileToExistLabel(fileBeanList, labelEntry)
            if (added) {
                labelEntry.useCount = labelEntry.useCount + 1
                labelEntry.lastUsedTime = System.currentTimeMillis()
                FileLabelDBHelper.updateFileLabel(labelEntry)
                withContext(Dispatchers.Main) {
                    CustomToast.showShort(com.filemanager.common.R.string.label_add_files_successed)
                    Injector.injectFactory<IMain>()?.updateLabelData(activity)
                }
            }
        }
    }

    private fun createFolderIfNotExist(destPath: String) {
        val folder = File(destPath)
        if (!folder.exists()) {
            folder.mkdirs()
        }
    }

    private fun mappingFileToExistLabel(fileBeanList: List<BaseFileBean>, label: FileLabelEntity): Boolean {
        val mappingFileList = ArrayList<FileLabelMappingEntity>()
        val pathList = ArrayList<String>()
        fileBeanList.forEach { bean ->
            bean.mData?.let { pathList.add(it) }
        }
        val labelPathList =
            FileLabelMappingDBHelper.getFileByLabelIdAndPathList(label.id, pathList)
        fileBeanList.forEach {
            if (labelPathList?.contains(it.mData) == true) {
                return@forEach
            }
            val fileLabelMappingEntity = FileLabelMappingEntity(
                id = 0,
                labelId = label.id,
                filePath = it.mData ?: "",
                localType = it.mLocalType,
                mimeType = it.mMimeType ?: "",
                timestamp = System.currentTimeMillis(),
                duration = GetMediaDurationUtil.getDuration(it)
            )
            val mappingEntity = FileLabelMappingDBHelper.getFileByLabelIdAndPath(fileLabelMappingEntity.labelId, fileLabelMappingEntity.filePath)
            if (mappingEntity == null) {
                mappingFileList.add(fileLabelMappingEntity)
            }
        }
        FileLabelMappingDBHelper.insertFileLabelMappingEntities(mappingFileList)
        return mappingFileList.isNotEmpty()
    }

    override fun getFileBeanList(
        activity: Activity,
        parseData: DragParseData,
        isMediaFiles: Boolean
    ): ArrayList<BaseFileBean> {
        val listBeans = ArrayList<BaseFileBean>()
        if (parseData.uriList.size > 0) {
            for (uri in parseData.uriList) {
                val bean = if (isMediaFiles) {
                    FileOperateUtil.getMediaFileBeanForUri(activity, uri)
                } else {
                    FileOperateUtil.getBaseFileBeanForUri(activity, uri)
                }
                listBeans.add(bean)
            }
        }
        if (parseData.textList.size > 0) {
            for ((index, text) in parseData.textList.withIndex()) {
                val name = FileOperateUtil.getDateFileName(index, TEXT_EXT)
                val bean = FileOperateUtil.getBaseFileBeanForString(text, activity, name)
                listBeans.add(bean)
            }
        }
        if (parseData.htmlList.size > 0) {
            for ((index, html) in parseData.htmlList.withIndex()) {
                val name = FileOperateUtil.getDateFileName(index, HTML_EXT)
                val bean = FileOperateUtil.getBaseFileBeanForString(html, activity, name)
                listBeans.add(bean)
            }
        }
        if (parseData.folderList.size > 0) {
            for (path in parseData.folderList) {
                val bean = FileOperateUtil.getFolderFileBean(path)
                listBeans.add(bean)
            }
        }
        if (parseData.noneMediaPathList.size > 0) {
            for (path in parseData.noneMediaPathList) {
                val bean = FileOperateUtil.getFileBean(path)
                listBeans.add(bean)
            }
        }
        if (parseData.mediaPathList.size > 0) {
            for (path in parseData.mediaPathList) {
                val bean = FileOperateUtil.getFileBean(path)
                listBeans.add(bean)
            }
        }
        Log.d(TAG, "getFileBeanList size: ${listBeans.size}")
        return listBeans
    }

    /**
     * 拖拽文件时，如果包含文件夹，会递归遍历当前选中的所有文件，但复制或移动时只需选中最外层的文件夹和文件
     */
    override fun findMinLayerFolderListBeans(fileBeanList: List<BaseFileBean>): ArrayList<BaseFileBean> {
        val finalFileBeanList = ArrayList<BaseFileBean>()
        //找出最外层的文件夹，即最小层级的文件夹
        val folderPathMap = HashMap<String, Int>()
        var minLayer = Int.MAX_VALUE
        for (fileBean in fileBeanList) {
            val path = fileBean.mData
            if (path.isNullOrEmpty()) {
                continue
            }
            val file = File(path)
            if (file.isDirectory) {
                val pathSplits = path.split(File.separator)
                if (pathSplits.isNotEmpty()) {
                    folderPathMap[path] = pathSplits.size
                    minLayer = min(pathSplits.size, minLayer)
                }
            }
        }
        val minLayerFolderPaths = ArrayList<String>()
        for (entry in folderPathMap) {
            val num = entry.value
            if (num == minLayer) {
                minLayerFolderPaths.add(entry.key)
            }
        }

        //如果没有找到最小层级的文件夹，说明都是同一级的文件
        if (minLayerFolderPaths.isEmpty()) {
            finalFileBeanList.addAll(fileBeanList)
            return finalFileBeanList
        }

        //找出和最外层文件夹同级的文件
        val minFolderPath = minLayerFolderPaths[0]
        val folder = File(minFolderPath)
        for (fileBean in fileBeanList) {
            val path = fileBean.mData
            if (path.isNullOrEmpty()) {
                continue
            }
            val file = File(path)
            if (file.isFile && file.parent == folder.parent) {
                finalFileBeanList.add(fileBean)
            }

            if (file.isDirectory && minLayerFolderPaths.contains(path)) {
                finalFileBeanList.add(fileBean)
            }
        }
        return finalFileBeanList
    }
}