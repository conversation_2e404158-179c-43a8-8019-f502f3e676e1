/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.recent.view.refresh.BounceLayout
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/1
 * * Author      : v-wanghonglei
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   v-wanghonglei             022/8/1     1.0       BounceLayout unit test
 ***********************************************************************/
package com.oplus.filemanager.recent.view.refresh

import android.view.MotionEvent
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Test

class BounceLayoutTest {

    @Test
    fun setPullListenerTest() {
        val bounceLayout = mockk<BounceLayout>(relaxed = true)
        every { bounceLayout.setPullListener(any()) } answers { callOriginal() }
        bounceLayout.setPullListener(object : BounceLayout.PullListener {
            override fun onPull() {
                println("setPullListener")
            }
        })
        Assert.assertEquals(true, bounceLayout.mPullListener != null)
    }

    @Test
    fun movingTest() {
        val bounceLayout = mockk<BounceLayout>(relaxed = true)
        every { bounceLayout.moving(any()) } answers { callOriginal() }
        every { bounceLayout.mYMove } returns (1f)
        every { bounceLayout.mCurrentY } returns (0f)
        val event = mockk<MotionEvent>(relaxed = true)
        bounceLayout.moving(event)
    }
}