<template>
    <card-template entry="desktop">
        <div class="cardContainer" onclick="clickCard">
            <div class="tipsContainer" >
                <text class="textTips">{{ tips }}</text>
            </div>
            <div class="settingContainer">
                <button class="settingButton" onclick="clickButton">{{ btnText }}</button>
            </div>
        </div>
    </card-template>
</template>

<data>
{
    "uiData": {
        "tips": "{{$t('strings.manage_files_permission_title')}}",
        "btnText": "{{$t('strings.goto_permission_setting')}}"
    }, 

    "uiEvent": { 
        "clickCard": {
            "type": "deeplink",
            "uri": "nativeapp://oplus.intent.action.filemanager.OPEN_FILEMANAGER",
            "params": {
                "openPermission": "true"
            }
        },
        "clickButton": {
            "type": "deeplink",
            "uri": "nativeapp://oplus.intent.action.filemanager.OPEN_FILEMANAGER",
            "params": {
                "openPermission": "true"
            }
        }
    }
}
</data>
<style>
    @media screen and (dark-mode: true) { 
        .textTips {
            color: rgba(255, 255, 255, 0.9);
        }
        .settingButton {
            background-color: rgba(36, 124, 255, 0.25);
            text-color: #5C9DFF;
        }
    }
    @media screen and (dark-mode: false) { 
        .textTips {
            color: rgba(0, 0, 0, 0.9);
        }
        .settingButton {
            background-color: rgba(00, 102, 255, 0.15);
            text-color: #0066FF;
        }
    }
    @media screen and (dark-mode: false) {
      .cardContainer {
          background: linear-gradient(to bottom right,#EDEFF3,#DFE3E8);
      }
    }
    @media screen and (aspect-ratio: 2*4) and (dark-mode: true) {
      .cardContainer {
          background-image: url("assets/images/background_dark.png");
      }
    }
    @media screen and (aspect-ratio: 4*4) and (dark-mode: true) {
        .cardContainer {
            background-image: url("assets/images/background_dark_large.png");
        }
    }
    @media screen and (aspect-ratio: 2*4) {
        .textTips {
            margin-top: 48px;
        }
    }
    @media screen and (aspect-ratio: 4*4) {
        .textTips {
            margin-top: 140px;
        }
    }
    .cardContainer {
        position: absolute;
        width: 100%;
        height: 100%;
        background-position:center
    }

    .tipsContainer {
        position: absolute;
        width: 100%;
        height: 100%;
        flex-direction: column;
        align-items: center;
    }

    .textTips {
        font-family:OPlusSans 3.5;
        font-size:16.0px;
        max-lines: 2;
        line-height: 22px;
        margin-left: 16px;
        margin-right: 16px;
        text-overflow: ellipsis;
        text-decoration:none;
        font-weight: 500;
        font-style: normal
    }

    .settingContainer {
        position: fixed;
        bottom: 16px;
    }

    .settingButton {
        width: 100%;
        height: 32px;
        margin-left: 16px;
        margin-right: 16px;
        border-radius: 37px;
        font-family:OPlusSans 3.5;
        font-size:12.0px;
        line-height: 16px;
        text-decoration:none;
        font-weight: 500;
        font-style: normal
    }
</style>