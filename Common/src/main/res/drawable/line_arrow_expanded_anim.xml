<animated-vector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:aapt="http://schemas.android.com/aapt">
  <aapt:attr name="android:drawable">
    <vector android:height="24dp" android:width="16dp" android:viewportHeight="72" android:viewportWidth="48">
      <group android:name="_R_G">
        <group android:name="_R_G_L_0_G" android:translateX="24" android:translateY="36">
          <path android:name="_R_G_L_0_G_D_0_P_0" android:strokeColor="?attr/couiColorLabelSecondary" android:strokeLineCap="butt" android:strokeLineJoin="round" android:strokeWidth="4" android:pathData=" M-16.5 -10.5 C-16.5,-10.5 3.06,9.11 3.06,9.1 C3.06,9.11 22.5,-10.5 22.5,-10.5 "/>
        </group>
      </group>
      <group android:name="time_group"/>
    </vector>
  </aapt:attr>
  <target android:name="_R_G_L_0_G_D_0_P_0">
    <aapt:attr name="android:animation">
      <set android:ordering="together">
        <objectAnimator android:propertyName="pathData" android:duration="350" android:startOffset="0" android:valueFrom="M-16.5 -10.5 C-16.5,-10.5 3.06,9.11 3.06,9.1 C3.06,9.11 22.5,-10.5 22.5,-10.5 " android:valueTo="M-16.5 10.61 C-16.5,10.61 3.06,-8.99 3.06,-8.99 C3.06,-8.99 22.5,10.61 22.5,10.61 " android:valueType="pathType">
          <aapt:attr name="android:interpolator">
            <pathInterpolator android:pathData="M 0.0,0.0 c0.3,0 0.1,1 1.0,1.0"/>
          </aapt:attr>
        </objectAnimator>
      </set>
    </aapt:attr>
  </target>
  <target android:name="time_group">
    <aapt:attr name="android:animation">
      <set android:ordering="together">
        <objectAnimator android:propertyName="translateX" android:duration="350" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType"/>
      </set>
    </aapt:attr>
  </target>
</animated-vector>