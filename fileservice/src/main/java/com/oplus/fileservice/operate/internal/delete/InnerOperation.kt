/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : InnerOperation
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 21:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/21       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.delete

import android.net.Uri
import com.filemanager.common.batch.SingleBatchAction

abstract class InnerOperation(
    protected val uri: Uri,
    bulkCount: Int
) : SingleBatchAction<String>(bulkCount) {
    var mHasDir = false
}