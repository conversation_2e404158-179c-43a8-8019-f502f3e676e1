/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AlbumFilesScanner
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/23 16:18
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/23       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.scanner

import android.provider.MediaStore.Images.ImageColumns
import androidx.annotation.VisibleForTesting
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.ImageFileWrapper
import com.filemanager.common.wrapper.WebAlbumLoadResult
import com.oplus.fileservice.bean.WebFileBean
import com.oplus.fileservice.bean.WebScannerResult
import com.oplus.fileservice.fileServiceScope
import com.oplus.fileservice.filelist.utils.getDateAndTimeByDefaultFormat
import com.oplus.fileservice.utils.WebMimeTypeHelper
import com.oplus.fileservice.utils.WebThumbCacheManager
import kotlin.collections.ArrayList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.apache.commons.io.FilenameUtils

class AlbumFilesScanner(pageNo: Int, pageSize: Int, sortOrder: Int, private val isNeedThumbnail: Boolean = true) : BaseUriScanner() {

    private var mPageNo = pageNo
    private var mPageSize = pageSize
    private var mTotal = 0
    private var mSortOrder = sortOrder

    override fun scannerFiles(): WebScannerResult? {
        val sortOrder = getOrderWithType(mSortOrder)
        Log.d(TAG, "scannerFiles -> sortOrder = $sortOrder")
        val webImageResult = MediaStoreCompat.getImageItems(mPageNo, mPageSize, sortOrder)
        handleQueryResult(webImageResult)
        return null
    }

    private fun handleQueryResult(imageResults: WebAlbumLoadResult) {
        val resultList = imageResults.mResultList
        mTotal = imageResults.mTotal
        mPageNo = imageResults.mPageNo
        val resultWebFileBean = ArrayList<WebFileBean>()
        Log.d(TAG, "transformImageFileToWebFile -> resultList Size = ${resultList.size}")
        for (imageFileWrapper in resultList) {
            val webFileBean = convertImageFileToWebFileBean(imageFileWrapper)
            resultWebFileBean.add(webFileBean)
        }

        fileServiceScope.launch(Dispatchers.IO) {
            if (isNeedThumbnail) {
                WebThumbCacheManager.setThumbnailData(resultWebFileBean)
            }
            Log.d(TAG, "mScannerCallback , mPageNo =$mPageNo, mTotal =$mTotal  result.size =${resultList.size}")
            mScannerCallback?.scannerSuc(WebScannerResult(mPageNo, mTotal, resultWebFileBean))
        }
    }

    /**
     * Convert [ImageFileWrapper] to [WebFileBean].
     */
    @VisibleForTesting
    fun convertImageFileToWebFileBean(imageFileWrapper: ImageFileWrapper): WebFileBean {
        val webFileBean = WebFileBean()
        imageFileWrapper.apply {
            webFileBean.mFileName = FilenameUtils.getBaseName(mDisplayName)
            webFileBean.mFilePath = mData
            webFileBean.mFileId = getId()
            webFileBean.mFileSize = mSize
            webFileBean.mFileFormat = FilenameUtils.getExtension(mDisplayName)
            webFileBean.mFileType = WebMimeTypeHelper.getFileTypeFromExtension(webFileBean.mFileFormat)
            webFileBean.mLastModifyDate = getDateAndTimeByDefaultFormat(mDateModified)
            webFileBean.mDateModified = mDateModified
            webFileBean.mLocalType = mLocalType
        }
        return webFileBean
    }

    /**
     * Obtain sortOrder by sort type
     */
    @VisibleForTesting
    fun getOrderWithType(sort: Int): String? {
        return when (sort) {
            SortHelper.FILE_TIME_REVERSE_ORDER -> DATE_MODIFIED
            SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER -> SIZE_DESC
            SortHelper.FILE_TYPE_ORDER -> TYPE_DESC
            SortHelper.FILE_NAME_ORDER -> NAME_DESC
            else -> null
        }
    }

    companion object {
        private const val TAG = "AlbumFilesScanner"

        @VisibleForTesting
        const val DATE_MODIFIED = "${ImageColumns.DATE_MODIFIED} DESC"

        @VisibleForTesting
        const val SIZE_DESC = "${ImageColumns.SIZE} DESC, ${ImageColumns.DATE_MODIFIED} DESC"

        @VisibleForTesting
        const val NAME_DESC = "${ImageColumns.DISPLAY_NAME} ASC, ${ImageColumns.DATE_MODIFIED} DESC"

        @VisibleForTesting
        const val TYPE_DESC = "${ImageColumns.MIME_TYPE} ASC, $NAME_DESC"
    }
}