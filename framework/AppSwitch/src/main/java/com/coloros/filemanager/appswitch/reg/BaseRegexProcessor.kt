/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : BaseRegexProcessor.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.reg

import android.util.Log
import com.coloros.filemanager.appswitch.AppSwitchManager
import com.coloros.filemanager.appswitch.process.IViewExactProcess
import com.coloros.filemanager.appswitch.util.Util
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.ParseRegItem.Companion.TARGET_TYPE_VALUE_BASETIME
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.ParseRegItem.Companion.TARGET_TYPE_VALUE_DEFAULT_EMPTY_FILENAME
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.ParseRegItem.Companion.TARGET_TYPE_VALUE_DELTATIME
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.ParseRegItem.Companion.TARGET_TYPE_VALUE_FILENAME
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.ParseRegItem.Companion.TARGET_TYPE_VALUE_FILETIME
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.ParseRegItem.Companion.TARGET_TYPE_VALUE_SIZE
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.ParseRegItem.Companion.TARGET_TYPE_VALUE_SOURCENAME
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam.ParseRegItem.Companion.TARGET_TYPE_VALUE_SOURCETYPE
import java.util.regex.PatternSyntaxException

abstract class BaseRegexProcessor {

    companion object {
        const val TAG = "BaseRegexProcessor"
    }


    fun processAllDataBean(
        viewExactInfo: IViewExactProcess.ViewExactInfo,
        inputDataBean: IViewExactProcess.KeyDataBean
    ): List<IViewExactProcess.KeyDataBean> {
        val result = mutableListOf<IViewExactProcess.KeyDataBean>()
        val fileName = inputDataBean.fileName
        if (fileName.isEmpty()) {
            val beansWithFileName = processEmptyFileName(viewExactInfo)
            beansWithFileName.forEach {
                val item = processDataBean(viewExactInfo, it)
                result.add(item)
            }
        } else {
            val singleItem = processDataBean(viewExactInfo, inputDataBean)
            result.add(singleItem)
        }
        Log.i(TAG, "processAllDataBean result $result")
        return result
    }


    private fun processDataBean(
        viewExactInfo: IViewExactProcess.ViewExactInfo,
        inputDataBean: IViewExactProcess.KeyDataBean
    ): IViewExactProcess.KeyDataBean {
        val checkActivity = viewExactInfo.viewExactActivity
        val preViewActivity = viewExactInfo.previewActivity
        val viewExtraString = viewExactInfo.viewExtraString
        val packageVersion = viewExactInfo.packageVersion
        val tmpResult = inputDataBean.copy()
        val configParam = AppSwitchManager.currentThirdAppListenConfigParam
        val parseRegItemList = configParam?.getParseRegListForCheckActivity(checkActivity, preViewActivity, packageVersion)
        val checkActivityItem = configParam?.getCheckActivityItem(checkActivity, preViewActivity, packageVersion)
        if (parseRegItemList == null || checkActivityItem == null) {
            Log.w(TAG, "processDataBean no reg for target activity $checkActivity, use input bean")
            return tmpResult
        } else {
            val fileName = inputDataBean.fileName
            val suffixList = AppSwitchManager.currentThirdAppListenConfigParam?.detectSuffix
            val fileNameMeetSuffix = Util.checkFileNameSuffixMeet(fileName, suffixList, true)
            if (!fileNameMeetSuffix) {
                Log.w(TAG, "processDataBean fileName $fileName not meet suffix, use input been")
                return tmpResult
            }
            preProcessDataBean(tmpResult, checkActivityItem)
            if (tmpResult.fileName.isEmpty()) {
                processField(viewExtraString, fileName, TARGET_TYPE_VALUE_FILENAME, parseRegItemList, tmpResult, checkActivityItem)
            }
            if (tmpResult.fileSize == null) {
                processField(viewExtraString, fileName, TARGET_TYPE_VALUE_SIZE, parseRegItemList, tmpResult, checkActivityItem)
            }
            if (tmpResult.fileTime == null) {
                processField(viewExtraString, fileName, TARGET_TYPE_VALUE_FILETIME, parseRegItemList, tmpResult, checkActivityItem)
            }
            if (tmpResult.baseFileTime == null) {
                processField(viewExtraString, fileName, TARGET_TYPE_VALUE_BASETIME, parseRegItemList, tmpResult, checkActivityItem)
            }
            if (tmpResult.deltaTime == null) {
                processField(viewExtraString, fileName, TARGET_TYPE_VALUE_DELTATIME, parseRegItemList, tmpResult, checkActivityItem)
            }
            //不采集来源名称
            /*if (tmpResult.sourceName.isEmpty()) {
                processField(viewExtraString, fileName, TARGET_TYPE_VALUE_SOURCENAME, parseRegItemList, tmpResult, checkActivityItem)
            }*/
            tmpResult.sourceName = ""
            if (tmpResult.sourceType == -1) {
                processField(viewExtraString, fileName, TARGET_TYPE_VALUE_SOURCETYPE, parseRegItemList, tmpResult, checkActivityItem)
                postProcessSourceTypeFromActivity(checkActivityItem, tmpResult)
            }
            return tmpResult
        }
    }

    private fun preProcessDataBean(
        inputDataBean: IViewExactProcess.KeyDataBean,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ) {
        preprocessNoParseFromIntent(inputDataBean, checkActivityItem)
        preprocessFieldFromCheckActivity(inputDataBean, checkActivityItem)
    }

    private fun preprocessFieldFromCheckActivity(
        inputDataBean: IViewExactProcess.KeyDataBean,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ) {
        val sourceType = checkActivityItem.sourceType
        if (sourceType != null) {
            inputDataBean.sourceType = sourceType
        }
        val sourceName = checkActivityItem.sourceName
        if (!sourceName.isNullOrEmpty()) {
            inputDataBean.sourceName = sourceName
        }
        Log.i(TAG, "preProcessDataBean sourceType $sourceType, sourceName $sourceName, dataBean $inputDataBean")
    }

    private fun preprocessNoParseFromIntent(
        inputDataBean: IViewExactProcess.KeyDataBean,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ) {
        val noParseFromIntent = checkActivityItem.noParseFromIntent
        if (noParseFromIntent != null) {
            if (noParseFromIntent.contentEquals("sourcetype", true)) {
                inputDataBean.sourceType = -1
            } else if (noParseFromIntent.contentEquals("sourcename", true)) {
                inputDataBean.sourceName = ""
            } else if (noParseFromIntent.contentEquals("filetime", true)) {
                inputDataBean.fileTime = null
                inputDataBean.deltaTime = null
                inputDataBean.baseFileTime = null
            } else if (noParseFromIntent.contentEquals("filesize", true)) {
                inputDataBean.fileSize = null
            }
        }
        Log.i(TAG, "preprocessNoParseFromIntent $noParseFromIntent, dataBean $inputDataBean")
    }

    private fun processEmptyFileName(
        viewExactInfo: IViewExactProcess.ViewExactInfo
    ): List<IViewExactProcess.KeyDataBean> {
        val nameStringList = getNameFromViewExtractString(viewExactInfo)
        val result = if (nameStringList.isEmpty()) {
            emptyList()
        } else {
            val mapResult = nameStringList.map {
                val item = IViewExactProcess.KeyDataBean()
                item.fileName = it
                item
            }
            mapResult
        }
        Log.i(TAG, "processEmptyFileName nameStringList $nameStringList, result $result")
        return result
    }


    private fun getNameFromViewExtractString(viewExtractInfo: IViewExactProcess.ViewExactInfo): List<String> {
        var result = mutableListOf<String>()
        Log.i(TAG, "getNameFromViewExtractString, viewExtractInfo $viewExtractInfo")
        val viewExtraString = viewExtractInfo.viewExtraString
        if (viewExtraString.isEmpty()) {
            Log.i(TAG, "getNameFromViewExtractString end because viewExtraString empty")
            return result
        } else {
            var nameRegexString = getEmptyNameRegexStringFromConfig(viewExtractInfo)
            if (nameRegexString.isEmpty()) {
                nameRegexString = getEmptyNameRegexString()
            }
            result = processEmptyFileNameRegex(viewExtraString, nameRegexString)
            return result
        }
    }

    private fun getEmptyNameRegexString(): String {
        val suffixList = AppSwitchManager.currentThirdAppListenConfigParam?.detectSuffix
        if (suffixList.isNullOrEmpty()) {
            Log.i(TAG, "getEmptyNameRegexString suffixList empty, return")
            return ""
        } else {
            val sb = StringBuilder("\\n*([\\S| ]+(")
            val size = suffixList.size
            suffixList.forEachIndexed { index, item ->
                sb.append(item)
                if (index != size - 1) {
                    sb.append("|")
                }
            }
            sb.append("))\\n")
            Log.i(TAG, "getEmptyNameRegexString reslut: $sb")
            return sb.toString()
        }
    }

    private fun getEmptyNameRegexStringFromConfig(viewExactInfo: IViewExactProcess.ViewExactInfo): String {
        val parseRegItemList =
            AppSwitchManager.currentThirdAppListenConfigParam?.getParseRegListForCheckActivity(
                viewExactInfo.viewExactActivity,
                viewExactInfo.previewActivity,
                viewExactInfo.packageVersion
            )
        if (parseRegItemList == null) {
            Log.w(TAG, "getEmptyNameRegexStringFromConfig no reg for target activity ${viewExactInfo.viewExactActivity}, use input bean")
            return ""
        } else {
            val targetTypeParseRegItemList = parseRegItemList.filter {
                it.targetType.contentEquals(TARGET_TYPE_VALUE_DEFAULT_EMPTY_FILENAME, ignoreCase = true)
            }
            return if (targetTypeParseRegItemList.isEmpty()) {
                Log.i(TAG, "getEmptyNameRegexStringFromConfig end no targetType empty-filename found in regex $targetTypeParseRegItemList, return")
                ""
            } else {
                val regItem = targetTypeParseRegItemList.first()
                val result = getEmptyNameRegexFromRegItem(regItem)
                Log.i(TAG, "getEmptyNameRegexStringFromConfig $result")
                result
            }
        }
    }

    private fun getEmptyNameRegexFromRegItem(parseRegItem: ThirdAppListenConfigParam.ParseRegItem): String {
        val suffixList = AppSwitchManager.currentThirdAppListenConfigParam?.detectSuffix
        if (suffixList.isNullOrEmpty()) {
            Log.i(TAG, "getEmptyNameRegexFromRegItem suffixList empty, return")
            return ""
        } else {
            val sb = StringBuilder("(")
            val size = suffixList.size
            suffixList.forEachIndexed { index, item ->
                sb.append(item)
                if (index != size - 1) {
                    sb.append("|")
                }
            }
            sb.append(")")
            val suffixString = sb.toString()
            val topLevelRegex = parseRegItem.originalTopLevelReg
            var preProcessTopLevelRegex = topLevelRegex
            if (topLevelRegex.contains("{suffix}")) {
                preProcessTopLevelRegex = topLevelRegex.replace("{suffix}", suffixString)
            }
            Log.i(TAG, "getEmptyNameRegexFromRegItem parseRegItem $parseRegItem, result $preProcessTopLevelRegex")
            return preProcessTopLevelRegex
        }
    }

    private fun processEmptyFileNameRegex(viewExtraString: String, nameRegexString: String): MutableList<String> {
        val result = mutableListOf<String>()
        val regex: Regex?
        try {
            regex = Regex(nameRegexString)
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, "processEmptyFileNameRegex regex constuctor error, IllegalArgumentException $nameRegexString", e)
            return result
        } catch (e: PatternSyntaxException) {
            Log.e(TAG, "processEmptyFileNameRegex regex constuctor error, IllegalArgumentException $nameRegexString", e)
            return result
        }
        val resultSequence = regex.findAll(viewExtraString)
        val sequenceIterator = resultSequence.iterator()
        if (sequenceIterator.hasNext()) {
            do {
                val matchResult = sequenceIterator.next()
                val fileName = matchResult.value.trim().replace("\\n", "")
                result.add(fileName)
            } while (sequenceIterator.hasNext())
        }
        Log.i(TAG, "processEmptyFileNameRegex result $result")
        return result
    }


    private fun postProcessSourceTypeFromActivity(
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem?,
        inputDataBean: IViewExactProcess.KeyDataBean
    ) {
        Log.i(TAG, "postProcessSourceTypeFromActivity targetActivity ${checkActivityItem?.activity}, inputDataBean $inputDataBean")
        if (checkActivityItem == null) {
            Log.i(TAG, "postProcessSourceTypeFromActivity checkActivityItem null, return")
            return
        }
        if (inputDataBean.sourceType == -1) {
            val sourceType = checkActivityItem.sourceType
            if (sourceType != -1 && sourceType != null) {
                inputDataBean.sourceType = sourceType
            }
            Log.i(TAG, "postProcessSourceTypeFromActivity setSourceType: $sourceType")
        }
        if (inputDataBean.sourceType == -1) {
            val defaultSourceType = checkActivityItem.defaultSourceType
            if (defaultSourceType != -1 && defaultSourceType != null) {
                inputDataBean.sourceType = defaultSourceType
            }
            Log.i(TAG, "postProcessSourceTypeFromActivity setSourceType: $defaultSourceType")
        }
    }


    private fun processField(
        viewExtraString: String,
        fileName: String,
        targetType: String,
        parseRegItemList: List<ThirdAppListenConfigParam.ParseRegItem>,
        inputDataBean: IViewExactProcess.KeyDataBean,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ) {
        Log.i(TAG, "processField start targetType $targetType, fileName $fileName, viewExtraString $viewExtraString, inputDataBean $inputDataBean")
        if (parseRegItemList.isEmpty() || viewExtraString.isEmpty() || checkTargetTypeNotSupproted(targetType)) {
            Log.i(TAG, "processField end because reglist empty ${parseRegItemList.size}, or $viewExtraString is empty, or $targetType not Supported")
            return
        } else {
            //过滤相应的目标的targetType的列表
            val targetTypeParseRegItemList = parseRegItemList.filter {
                it.targetType.contentEquals(targetType, ignoreCase = true)
            }
            if (targetTypeParseRegItemList.isEmpty()) {
                Log.i(TAG, "processField end no targetType $targetType found in regex $targetTypeParseRegItemList, return")
                return
            }
            Log.i(TAG, "processField before foreach, targetTypeParseRegItemList ${targetTypeParseRegItemList.size}")
            for (parseRegItem in targetTypeParseRegItemList) {
                processSingleRegItem(fileName, parseRegItem, viewExtraString, targetType, inputDataBean, checkActivityItem)
            }
        }
    }

    open fun processSingleRegItem(
        fileName: String,
        parseRegItem: ThirdAppListenConfigParam.ParseRegItem,
        viewExtraString: String,
        targetType: String,
        inputDataBean: IViewExactProcess.KeyDataBean,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ) {
        Log.i(TAG, "processSingleRegItem fileName $fileName, parseRegItem $parseRegItem")
        val multiResultAbandon = parseRegItem.isMatchModeMultiAbandon()
        if (multiResultAbandon) {
            processMultiAbandonRegItem(fileName, parseRegItem, viewExtraString, targetType, inputDataBean, checkActivityItem)
        } else {
            processDefaultSingleRegItem(fileName, parseRegItem, viewExtraString, targetType, inputDataBean, checkActivityItem)
        }
    }

    private fun processDefaultSingleRegItem(
        fileName: String,
        parseRegItem: ThirdAppListenConfigParam.ParseRegItem,
        viewExtraString: String,
        targetType: String,
        inputDataBean: IViewExactProcess.KeyDataBean,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ) {
        val preProcessTopLevelRegex = pressProcessRegWithFileName(fileName, parseRegItem)
        val regex: Regex?
        try {
            regex = Regex(preProcessTopLevelRegex)
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, "processField regex constuctor error, IllegalArgumentException $preProcessTopLevelRegex", e)
            return
        } catch (e: PatternSyntaxException) {
            Log.e(TAG, "processField regex constuctor error, IllegalArgumentException $preProcessTopLevelRegex ", e)
            return
        }
        val result = regex.find(viewExtraString)
        val isGroupIndex = parseRegItem.isTopLevelIndexTypeGroup()
        val hasSecondLevel = parseRegItem.hasSecondLevelRegs()
        Log.i(TAG, "processField preRegex $preProcessTopLevelRegex, result $result, isGroup $isGroupIndex, hasSecond $hasSecondLevel")
        var targetStringResult = ""
        if (result != null) {
            targetStringResult = if (isGroupIndex) {
                getTargetInputStringForGroup(result, parseRegItem)
            } else {
                getTargetInputStringForIndex(result, parseRegItem)
            }
            if (hasSecondLevel) {
                val secondLevelItems = parseRegItem.secondLevelRegs
                if (secondLevelItems != null) {
                    processSecondLevelField(targetStringResult, secondLevelItems, targetType, inputDataBean, checkActivityItem)
                } else {
                    Log.e(TAG, "processField $parseRegItem has secondLevel but secondLevelItems is null, next")
                }
            } else {
                processTopLevelField(targetStringResult, parseRegItem, targetType, inputDataBean, checkActivityItem)
            }
        } else {
            Log.w(TAG, "processField no match parseRegItem $parseRegItem, targetType $targetType, ,next")
        }
    }

    private fun processMultiAbandonRegItem(
        fileName: String,
        parseRegItem: ThirdAppListenConfigParam.ParseRegItem,
        viewExtraString: String,
        targetType: String,
        inputDataBean: IViewExactProcess.KeyDataBean,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ) {
        val preProcessTopLevelRegex = pressProcessRegWithFileName(fileName, parseRegItem)
        val regex: Regex?
        try {
            regex = Regex(preProcessTopLevelRegex)
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, "processMultiAbandonRegItem regex constuctor error, IllegalArgumentException $preProcessTopLevelRegex", e)
            return
        } catch (e: PatternSyntaxException) {
            Log.e(TAG, "processMultiAbandonRegItem regex constuctor error, IllegalArgumentException $preProcessTopLevelRegex ", e)
            return
        }
        val resultSequence = regex.findAll(viewExtraString)
        val isGroupIndex = parseRegItem.isTopLevelIndexTypeGroup()
        val hasSecondLevel = parseRegItem.hasSecondLevelRegs()

        val sequenceList = resultSequence.toList()
        if (sequenceList.size != 1) {
            Log.w(TAG, "processMultiAbandonRegItem sequenceListsize ${sequenceList.size} != 1, return")
            return
        } else {
            val result = sequenceList[0]
            Log.i(TAG, "processMultiAbandonRegItem preRegex $preProcessTopLevelRegex, result $result, " +
                    "isGroup $isGroupIndex, hasSecond $hasSecondLevel")
            var targetStringResult = ""
            targetStringResult = if (isGroupIndex) {
                getTargetInputStringForGroup(result, parseRegItem)
            } else {
                getTargetInputStringForIndex(result, parseRegItem)
            }
            if (hasSecondLevel) {
                val secondLevelItems = parseRegItem.secondLevelRegs
                if (secondLevelItems != null) {
                    processSecondLevelField(targetStringResult, secondLevelItems, targetType, inputDataBean, checkActivityItem)
                } else {
                    Log.e(TAG, "processMultiAbandonRegItem $parseRegItem has secondLevel but secondLevelItems is null, next")
                }
            } else {
                processTopLevelField(targetStringResult, parseRegItem, targetType, inputDataBean, checkActivityItem)
            }
        }
    }


    fun getTargetInputStringForGroup(
        result: MatchResult,
        parseRegItem: ThirdAppListenConfigParam.ParseRegItem,
    ): String {
        var targetSecondLevelInput = ""
        val groups = result.groups
        val groupSize = groups.size
        val currentLevelIndex = parseRegItem.topLevelRegIndex
        printeTopGroupInfo(groups, groupSize, currentLevelIndex)
        var targetSecondLeveGroup: MatchGroup? = null
        runCatching {
            //这个地方可能外部配置的currentLevelIndex超过了groupsize，导致出错
            targetSecondLeveGroup = groups[currentLevelIndex + 1]
            if (targetSecondLeveGroup != null) {
                targetSecondLevelInput = targetSecondLeveGroup?.value ?: ""
            }
        }.onFailure {
            Log.e(TAG, "getTargetInputStringForGroup error", it)
        }
        Log.i(TAG, "getTargetInputStringForGroup groupSize $groupSize, secondGroup $targetSecondLeveGroup, index $currentLevelIndex, " +
                "targetInput $targetSecondLevelInput")
        return targetSecondLevelInput
    }

    fun getTargetInputStringForIndex(result: MatchResult, parseRegItem: ThirdAppListenConfigParam.ParseRegItem): String {
        val value = result.value
        val currentLevelIndex = parseRegItem.topLevelRegIndex
        val targetStringResult = value.substring(currentLevelIndex).trim()
        Log.i(TAG, "getTargetInputStringForIndex value $value, currentLevelIndex $currentLevelIndex, targetInput $targetStringResult")
        return targetStringResult
    }


    private fun printeTopGroupInfo(
        groups: MatchGroupCollection,
        groupSize: Int,
        currentLevelIndex: Int
    ) {
        val sb = StringBuilder()
        sb.append("processField top Level groups $groups groupSize $groupSize, currentLevel $currentLevelIndex, \n")
        groups.forEach() { groupItem ->
            val itemRange = groupItem?.range
            val itemValue = groupItem?.value
            sb.append("itemRange $itemRange, itemValue $itemValue \n")
        }
        Log.i(TAG, sb.toString())
    }

    /**
     * 预处理，将fileName中的特殊字符处理掉，同时filename替换正则表达式字符串中的{filename}字段
     */
    fun pressProcessRegWithFileName(
        fileName: String,
        parseRegItem: ThirdAppListenConfigParam.ParseRegItem
    ): String {
        val processedFileName = processFileNameWithSpecialCharater(fileName)
        val fileNameWithOutSuffix = getNoSuffixFileName(processedFileName)
        val topLevelRegex = parseRegItem.originalTopLevelReg
        var preProcessTopLevelRegex = topLevelRegex
        if (topLevelRegex.contains("{filename}")) {
            preProcessTopLevelRegex = topLevelRegex.replace("{filename}", processedFileName)
        } else if (topLevelRegex.contains("{rawfilename}")) {
            preProcessTopLevelRegex = topLevelRegex.replace("{rawfilename}", fileNameWithOutSuffix)
        }
        parseRegItem.processedTopLevelReg = preProcessTopLevelRegex
        return preProcessTopLevelRegex
    }

    private fun checkTargetTypeNotSupproted(targetType: String): Boolean {
        val array = arrayOf(
            TARGET_TYPE_VALUE_SOURCENAME,
            TARGET_TYPE_VALUE_FILENAME,
            TARGET_TYPE_VALUE_SIZE,
            TARGET_TYPE_VALUE_FILETIME,
            TARGET_TYPE_VALUE_SOURCETYPE,
            TARGET_TYPE_VALUE_BASETIME,
            TARGET_TYPE_VALUE_DELTATIME
        )
        return !array.contains(targetType.lowercase())
    }

    private fun processFileNameWithSpecialCharater(inputFileName: String): String {
        val specialCharaterArray =
            arrayOf("$", "(", ")", "*", "+", ".", "[", "]", "?", "^", "{", "}", "|")
        var result = inputFileName
        specialCharaterArray.forEach {
            if (result.contains(it)) {
                val regexString = "\\$it"
                val regex = Regex(regexString)
                val tmpresult = result.replace(regex) { matchResult ->
                    "\\$it"
                }
                Log.i(TAG, "processFileNameWithSpecialCharater regexString $regexString replaced $it, input $result, result $tmpresult")
                result = tmpresult
            }
        }
        Log.i(
            TAG,
            "processFileNameWithSpecialCharater inputFileName: $inputFileName, outputFileName $result"
        )
        return result
    }

    /**
     *  输入是这种类型的字符串： 阿里编程闺房-android相关筛选\.doc，输出为：阿里编程闺房-android相关筛选
     *
     */
    private fun getNoSuffixFileName(inputFileName: String): String {
        val result = StringBuilder(inputFileName)
        val splitString = "\\."
        if (result.contains(splitString)) {
            val index = result.indexOf(splitString)
            result.replace(index, result.length, "")
            Log.i(TAG, "getNoSuffixFileName replaced $splitString, index $index, result $result")
        }
        Log.i(TAG, "getNoSuffixFileName input $inputFileName, output $result")
        return result.toString()
    }



    fun processTopLevelField(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem,
        targetType: String,
        inputDataBean: IViewExactProcess.KeyDataBean,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ) {
        Log.i(
            TAG, "processTopLevelField topLevelInputString $topLevelInputString" +
                    ", targetType $targetType, inputDataBean $inputDataBean"
        )
        if (targetType.contentEquals(TARGET_TYPE_VALUE_SOURCENAME, true)) {
            val sourceName = processTopLevelSourceName(topLevelInputString, topLevelReg)
            if (sourceName.isNotEmpty()) {
                inputDataBean.sourceName = sourceName
            }
            Log.i(TAG, "processTopLevelField sourceName $sourceName")
        } else if (targetType.contentEquals(TARGET_TYPE_VALUE_SOURCETYPE, true)) {
            val sourceType = processTopLevelSourceType(topLevelInputString, topLevelReg, checkActivityItem)
            if (sourceType != -1) {
                inputDataBean.sourceType = sourceType
            }
            Log.i(TAG, "processTopLevelField sourceType $sourceType")
        } else if (targetType.contentEquals(TARGET_TYPE_VALUE_FILETIME)) {
            val fileTime = processTopLevelFileTime(topLevelInputString, topLevelReg)
            if (fileTime != null) {
                inputDataBean.fileTime = fileTime
            }
            Log.i(TAG, "processTopLevelField fileTime $fileTime")
        } else if (targetType.contentEquals(TARGET_TYPE_VALUE_FILENAME)) {
            val fileName = processTopLevelFileName(topLevelInputString, topLevelReg)
            if (fileName.isNotEmpty()) {
                inputDataBean.fileName = fileName
            }
            Log.i(TAG, "processTopLevelField fileName $fileName")
        } else if (targetType.contentEquals(TARGET_TYPE_VALUE_SIZE)) {
            val fileSize = processTopLevelFileSize(topLevelInputString, topLevelReg)
            if (fileSize != null) {
                inputDataBean.fileSize = fileSize
            }
            Log.i(TAG, "processTopLevelField fileSize $fileSize")
        } else if (targetType.contentEquals(TARGET_TYPE_VALUE_BASETIME)) {
            val baseTime = processTopLevelBaseTime(topLevelInputString, topLevelReg, checkActivityItem)
            if (baseTime != null) {
                inputDataBean.baseFileTime = baseTime
            }
            Log.i(TAG, "processTopLevelField baseTime $baseTime")
        } else if (targetType.contentEquals(TARGET_TYPE_VALUE_DELTATIME)) {
            val deltaTime = processTopLevelDeltaTime(topLevelInputString, topLevelReg, checkActivityItem)
            if (deltaTime != null) {
                inputDataBean.deltaTime = deltaTime
            }
            Log.i(TAG, "processTopLevelField deltaTime $deltaTime")
        }
    }


    /**
     * 在第一层正则表达式处理时，获取文件时间
     *
     * @param topLevelInputString 输入第一层获取的正则表达式字符串
     * @param topLevelReg 输入第一层的正则表达式解析规则类
     */
    abstract fun processTopLevelFileTime(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem
    ): IViewExactProcess.TimeValues?


    /**
     * 在第一层正则表达式处理时，获取聊天记录基准时间
     *
     * @param topLevelInputString 输入第一层获取的正则表达式字符串
     * @param topLevelReg 输入第一层的正则表达式解析规则类
     */
    abstract fun processTopLevelBaseTime(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ): IViewExactProcess.TimeValues?



    /**
     * 在第一层正则表达式处理时，获取聊天记录基于基准时间的偏移时间
     *
     * @param topLevelInputString 输入第一层获取的正则表达式字符串
     * @param topLevelReg 输入第一层的正则表达式解析规则类
     */
    abstract fun processTopLevelDeltaTime(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ): IViewExactProcess.TimeValues?



    /**
     * 在第一层正则表达式处理时，获取文件大小
     *
     * @param topLevelInputString 输入第一层获取的正则表达式字符串
     * @param topLevelReg 输入第一层的正则表达式解析规则类
     */
    abstract fun processTopLevelFileSize(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem
    ): IViewExactProcess.SizeValues?

    /**
     * 在第一层正则表达式处理时，获取文件名称
     *
     * @param topLevelInputString 输入第一层获取的正则表达式字符串
     * @param topLevelReg 输入第一层的正则表达式解析规则类
     */
    abstract fun processTopLevelFileName(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem
    ): String

    /**
     * 在第一层正则表达式处理时，获取来源名称
     *
     * @param topLevelInputString 输入第一层获取的正则表达式字符串
     * @param topLevelReg 输入第一层的正则表达式解析规则类
     */
    abstract fun processTopLevelSourceName(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem
    ): String

    /**
     * 在第一层正则表达式处理时，获取来源类型
     *
     * @param topLevelInputString 输入第一层获取的正则表达式字符串
     * @param topLevelReg 输入第一层的正则表达式解析规则类
     */
    abstract fun processTopLevelSourceType(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ): Int


    fun processSecondLevelField(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>,
        targetType: String,
        inputDataBean: IViewExactProcess.KeyDataBean,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ) {
        Log.i(
            TAG, "processSecondLevelField secondLevelInputString $secondLevelInputString" +
                    ", targetType $targetType, regexList $regexList"
        )
        if (targetType.contentEquals(TARGET_TYPE_VALUE_SOURCENAME)) {
            val sourceName = processSecondLevelSourceName(secondLevelInputString, regexList)
            if (sourceName.isNotEmpty()) {
                inputDataBean.sourceName = sourceName
            }
            Log.i(TAG, "processSecondLevelField sourceName $sourceName")
        } else if (targetType.contentEquals(TARGET_TYPE_VALUE_SOURCETYPE)) {
            val sourceType = processSecondLevelSourceType(secondLevelInputString, regexList, checkActivityItem)
            if (sourceType != -1) {
                inputDataBean.sourceType = sourceType
            }
            Log.i(TAG, "processSecondLevelField sourceType $sourceType")
        } else if (targetType.contentEquals(TARGET_TYPE_VALUE_FILETIME)) {
            val time = processSecondLevelFileTime(secondLevelInputString, regexList)
            if (time != null) {
                inputDataBean.fileTime = time
            }
            Log.i(TAG, "processSecondLevelField time $time")
        } else if (targetType.contentEquals(TARGET_TYPE_VALUE_FILENAME)) {
            val fileName = processSecondLevelFileName(secondLevelInputString, regexList)
            if (fileName.isNotEmpty()) {
                inputDataBean.fileName = fileName
            }
            Log.i(TAG, "processSecondLevelField fileName $fileName")
        } else if (targetType.contentEquals(TARGET_TYPE_VALUE_SIZE)) {
            val fileSize = processSecondLevelFileSize(secondLevelInputString, regexList)
            if (fileSize != null) {
                inputDataBean.fileSize = fileSize
            }
            Log.i(TAG, "processSecondLevelField fileSize $fileSize")
        } else if (targetType.contentEquals(TARGET_TYPE_VALUE_BASETIME)) {
            val baseTime = processSecondLevelBaseTime(secondLevelInputString, regexList)
            if (baseTime != null) {
                inputDataBean.baseFileTime = baseTime
            }
            Log.i(TAG, "processSecondLevelField baseTime $baseTime")
        } else if (targetType.contentEquals(TARGET_TYPE_VALUE_DELTATIME)) {
            val deltaTime = processSecondLevelDeltaTime(secondLevelInputString, regexList)
            if (deltaTime != null) {
                inputDataBean.deltaTime = deltaTime
            }
            Log.i(TAG, "processSecondLevelField deltaTime $deltaTime")
        }
    }


    /**
     * 在第二层正则表达式处理时，获取文件名称
     *
     * @param secondLevelInputString 输入第二层获取需要匹配的字符串
     * @param regexList 输入第二层的正则表达式解析规则类
     */
    abstract fun processSecondLevelFileName(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>
    ): String

    /**
     * 在第二层正则表达式处理时，获取文件时间
     *
     * @param secondLevelInputString 输入第二层获取需要匹配的字符串
     * @param regexList 输入第二层的正则表达式解析规则类
     */
    abstract fun processSecondLevelFileTime(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>
    ): IViewExactProcess.TimeValues?



    /**
     * 在第二层正则表达式处理时，获取聊天记录的基准时间
     *
     * @param secondLevelInputString 输入第二层获取需要匹配的字符串
     * @param regexList 输入第二层的正则表达式解析规则类
     */
    abstract fun processSecondLevelBaseTime(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>
    ): IViewExactProcess.TimeValues?

    /**
     * 在第二层正则表达式处理时，获取聊天记录的基于基准时间的偏移时间
     *
     * @param secondLevelInputString 输入第二层获取需要匹配的字符串
     * @param regexList 输入第二层的正则表达式解析规则类
     */
    abstract fun processSecondLevelDeltaTime(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>
    ): IViewExactProcess.TimeValues?

    /**
     * 在第二层正则表达式处理时，获取文件大小
     *
     * @param secondLevelInputString 输入第二层获取需要匹配的字符串
     * @param regexList 输入第二层的正则表达式解析规则类
     */
    abstract fun processSecondLevelFileSize(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>
    ): IViewExactProcess.SizeValues?

    /**
     * 在第二层正则表达式处理时，获取来源名称
     *
     * @param secondLevelInputString 输入第二层获取需要匹配的字符串
     * @param regexList 输入第二层的正则表达式解析规则类
     */
    abstract fun processSecondLevelSourceName(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>
    ): String

    /**
     * 在第二层正则表达式处理时，获取来源类型
     *
     * @param secondLevelInputString 输入第二层获取需要匹配的字符串
     * @param regexList 输入第二层的正则表达式解析规则类
     */
    abstract fun processSecondLevelSourceType(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ): Int
}