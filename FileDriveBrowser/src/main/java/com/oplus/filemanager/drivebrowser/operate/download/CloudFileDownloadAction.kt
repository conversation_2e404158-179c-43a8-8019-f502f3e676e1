/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudFileDownloadAction
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/15 15:58
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/15       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.operate.download

import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.fileutils.fetchFileName
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.stringResource
import com.filemanager.fileoperate.NotifyMediaScannerBatchAction
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.base.SHOW_PROGRESS
import com.filemanager.fileoperate.base.UPDATE_PROGRESS
import com.oplus.filemanager.drivebrowser.data.api.KDocsFileDriveService
import com.oplus.filemanager.drivebrowser.data.api.TencentFileDriveService
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem.Companion.obtainExportType
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem.Companion.obtainExtensionByExportType
import com.oplus.filemanager.drivebrowser.data.repository.DocumentDownloadErrorCode
import com.oplus.filemanager.drivebrowser.data.repository.kdocs.KDocsFileDriveRepository
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.di.NetworkModule
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.drivebrowser.download.DocumentDownload
import com.oplus.filemanager.drivebrowser.download.DocumentDownloadDispatcher
import com.oplus.filemanager.drivebrowser.download.DownloadTaskInfo
import com.oplus.filemanager.drivebrowser.ui.CloudDocsItem
import kotlinx.coroutines.runBlocking
import org.apache.commons.io.FilenameUtils
import java.io.File

class CloudFileDownloadAction(
    private val repository: FileDriveRepository,
    lifecycle: LifecycleOwner,
    private val category: Int,
    private val files: ArrayList<CloudDocsItem>,
    private val targetPath: String
) : BaseFileAction<CloudFileDownloadObserver>(lifecycle) {

    private var currentProgress = 0L
    private var documentDownload: DocumentDownload? = null
    private var destFilePath: String? = ""
    private val mediaScannerBatchAction by lazy {
        NotifyMediaScannerBatchAction(Utils.MEDIA_SCAN_DOWNLOAD)
    }

    override fun run(): Boolean {
        val size = files.size
        if (size != DOWNLOAD_LIMIT_NUMBER) {
            Log.d(TAG, "more than download limit number.")
            return false
        }
        val item = files[0]
        return internalDownload(item)
    }

    @VisibleForTesting
    fun internalDownload(item: CloudDocsItem): Boolean {
        val auth = runBlocking {
            repository.isFileAuth(item.id)
        }
        if (auth.not()) {
            Log.i(TAG, "internalDownload check not auth")
            notifyObserver(ACTION_DOWNLOAD, false)
            return false
        }
        return if (PermissionUtils.hasNotificationPermission(appContext)) {
            asyncDownload(item)
        } else {
            syncDownload(item)
        }
    }

    @VisibleForTesting
    fun asyncDownload(item: CloudDocsItem): Boolean {
        Log.d(TAG, "asyncDownload -> item = $item")
        val fileId = item.id
        val exportType = item.type
        val path = targetPath + File.separator + item.title
        DocumentDownloadDispatcher.createEnqueueDownloadIntent(
            appContext,
            category,
            fileId,
            exportType,
            path
        )
        notifyObserver(ACTION_DOWNLOAD, true)
        return true
    }

    @VisibleForTesting
    fun syncDownload(item: CloudDocsItem): Boolean {
        Log.d(TAG, "syncDownload -> item = $item")
        notifyObserver(
            SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                title = stringResource(com.filemanager.common.R.string.downloading_dialog_title),
                indeterminate = false,
                currentProgress = currentProgress.toInt()
            )
        )
        val fileExtForType = obtainExtensionByExportType(item.type)
        val fileExtForTypeWithDot = fileExtForType?.let { ".$it" }
        val fileName = FilenameUtils.getName(item.title)
        val extForName = FilenameUtils.getExtension(fileName)
        val resultFileName = if (item.isTencentDocs()) {
            if (!extForName.isNullOrEmpty() && extForName.contentEquals(fileExtForType)) {
                item.title
            } else {
                if (fileExtForTypeWithDot == null) {
                    item.title
                } else {
                    item.title + fileExtForTypeWithDot
                }
            }
        } else {
            item.title
        }
        val resultNameWithoutExt = FilenameUtils.getBaseName(resultFileName)
        val finalExtWithDot = FilenameUtils.getExtension(resultFileName)?.let { ".$it" }
        val targetExportType = obtainExportType(item.type)
        Log.d(
            TAG,
            "syncDownload -> targetPath $targetPath title ${item.title},  fileExtension = $fileExtForType, targetExportType = $targetExportType, " +
                    "resultName $resultFileName, resultNameWithoutExt $resultNameWithoutExt, resultExt $finalExtWithDot"
        )
        val destFile = File(targetPath, resultFileName)
        var destPath = destFile.absolutePath
        if (destFile.exists()) {
            Log.d(TAG, "syncDownload -> destFile already exists.")
            // rename destFile fetchFileName
            fetchFileName(targetPath, resultNameWithoutExt, finalExtWithDot ?: "")?.let {
                destPath = it
            }
        }
        destFilePath = destPath
        onDealFile(item, destPath, targetExportType)
        return true
    }

    @VisibleForTesting
    fun onDealFile(item: CloudDocsItem, destPath: String, exportType: String?) {
        val service = NetworkModule.providerFileDriveService(category)
        val repository = if (category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
            TencentFileDriveRepository(service as TencentFileDriveService)
        } else {
            KDocsFileDriveRepository(service as KDocsFileDriveService)
        }
        documentDownload = DocumentDownload(repository)
        runBlocking {
            val downloadTaskInfo = DownloadTaskInfo()
            downloadTaskInfo.category = category
            downloadTaskInfo.fileId = item.id
            downloadTaskInfo.exportType = exportType
            downloadTaskInfo.targetPath = destPath
            val downloadResult = documentDownload?.download(downloadTaskInfo) {
                dealFileProgressChanged(it.toLong())
            }
            val errorCode = downloadResult?.errorCode ?: 0
            val failure = if (DocumentDownloadErrorCode.unSupportRetryError(errorCode)) {
                DOWNLOAD_FILE_ERROR
            } else {
                DOWNLOAD_ERROR
            }
            onDealFilesEnd(downloadResult?.success ?: false, failure)
        }
    }

    private fun dealFileProgressChanged(progress: Long) {
        Log.d(TAG, "dealFileProgressChanged -> progress = $progress")
        currentProgress = progress
        notifyObserver(UPDATE_PROGRESS, currentProgress.toInt())
    }

    override fun onCancelled() {
        super.onCancelled()
        documentDownload?.cancelDownload()
        onDealFilesEnd(false, DOWNLOAD_CANCEL)
    }

    private fun onDealFilesEnd(success: Boolean, failure: Int) {
        Log.d(TAG, "onDealFilesEnd")
        if (success) {
            mediaScannerBatchAction.add(targetPath)
            mediaScannerBatchAction.flush()
            notifyObserver(ACTION_DONE, targetPath)
        } else {
            destFilePath?.let { filePath ->
                val file = File(filePath)
                if (file.exists()) {
                    runCatching {
                        file.delete()
                    }.onFailure {
                        Log.e(TAG, "onDealFilesEnd -> delete temp file failure")
                    }
                }
            }

            notifyObserver(ACTION_FAILED, failure)
        }
    }

    companion object {
        private const val TAG = "CloudFileDownloadAction"
        private const val DOWNLOAD_LIMIT_NUMBER = 1
    }
}