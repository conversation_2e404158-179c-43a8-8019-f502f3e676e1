/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileDecompressObserver.kt
 ** Description: Monitor file decompress
 ** Version: 1.0
 ** Date: 2020/3/9
 ** Author: LiHao(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.previewcompress

import android.content.Context
import android.content.DialogInterface
import android.view.ContextThemeWrapper
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import com.filemanager.common.utils.CustomToast
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_FAILED
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_NOT_ENOUGH
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_NOT_EXIST
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_UNSUPPORT
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_OVERCOUNT
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_OVERSIZE
import com.filemanager.fileoperate.decompress.FileDecompressObserver
import com.filemanager.fileoperate.decompress.FileDecompressPasswordDialog
import com.filemanager.fileoperate.decompress.SHOW_DECOMPRESS_PASSWORD_DIALOG
import com.filemanager.fileoperate.decompress.SHOW_DECOMPRESS_PASSWORD_ERROR
import com.filemanager.fileoperate.decompress.DISMISS_DECOMPRESS_PASSWORD_DIALOG
import com.filemanager.fileoperate.decompress.DISMISS_VERIFY_PWD_LOADING_DIALOG
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_NOT_EXIST_DFM_DISCONNECTED
import com.filemanager.fileoperate.decompress.DialogFactory
import com.filemanager.fileoperate.decompress.RESHOW_DECOMPRESS_PASSWORD_DIALOG
import com.filemanager.fileoperate.decompress.SHOW_VERIFY_PWD_LOADING_DIALOG

open class FilePreviewCompressObserver(context: ContextThemeWrapper) : BaseFileActionObserver(context) {

    @VisibleForTesting
    var mFilePasswordDialog: FileDecompressPasswordDialog? = null
    @VisibleForTesting
    var loadingDialog: AlertDialog? = null

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        when (result.first) {
            // --file password start--
            SHOW_DECOMPRESS_PASSWORD_DIALOG -> {
                mFilePasswordDialog?.dismiss()
                if (result.second is FileDecompressObserver.PasswordDialogBean) {
                    mFilePasswordDialog = DialogFactory.createFilePasswordDialog(context, result.second as FileDecompressObserver.PasswordDialogBean)
                    mFilePasswordDialog?.show()
                }
            }
            SHOW_DECOMPRESS_PASSWORD_ERROR -> mFilePasswordDialog?.showPasswordErrorNotice()
            DISMISS_DECOMPRESS_PASSWORD_DIALOG -> mFilePasswordDialog?.dismiss()
            RESHOW_DECOMPRESS_PASSWORD_DIALOG -> {
                if (mFilePasswordDialog?.isShowing() == false) {
                    mFilePasswordDialog?.show()
                }
                mFilePasswordDialog?.setInputText(result.second as? String)
            }
            SHOW_VERIFY_PWD_LOADING_DIALOG -> {
                val listener = result.second
                if (listener is DialogInterface.OnCancelListener) {
                    loadingDialog = DialogFactory.showVerifyPwdLoadingDialog(context, listener)
                }
            }
            DISMISS_VERIFY_PWD_LOADING_DIALOG -> loadingDialog?.dismiss()
            // --file password end--
            PREVIEW_NOT_EXIST -> {
                CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
                onActionDone(false, PREVIEW_FAILED)
                return true
            }
            PREVIEW_NOT_EXIST_DFM_DISCONNECTED -> {
                CustomToast.showShort(com.filemanager.common.R.string.open_failed_by_device_disconnect)
                onActionDone(false, PREVIEW_FAILED)
                return true
            }
            PREVIEW_UNSUPPORT -> {
                CustomToast.showShort(com.filemanager.common.R.string.decompress_file_error)
                onActionDone(false, PREVIEW_FAILED)
                return true
            }
            PREVIEW_FAILED -> {
                CustomToast.showShort(com.filemanager.common.R.string.decompress_file_error)
                onActionDone(false, PREVIEW_FAILED)
                return true
            }
            PREVIEW_NOT_ENOUGH -> {
                onActionDone(false, PREVIEW_NOT_ENOUGH)
                return true
            }
            PREVIEW_OVERCOUNT, PREVIEW_OVERSIZE -> {
                val msg = if (result.first == PREVIEW_OVERCOUNT) {
                    context.resources.getString(com.filemanager.common.R.string.rar_file_num_beyond)
                } else {
                    context.resources.getString(com.filemanager.common.R.string.rar_file_size_beyond)
                }
                onActionPreviewOverCount(msg)
                return true
            }
        }
        return false
    }

    open fun onActionPreviewOverCount(msg: String?) {}

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || mFilePasswordDialog?.isShowing() ?: false || loadingDialog?.isShowing ?: false
    }
}
