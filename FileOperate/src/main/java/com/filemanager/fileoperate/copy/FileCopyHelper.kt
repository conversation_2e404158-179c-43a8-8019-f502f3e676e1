/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:FileCopyHelper.kt
 * * Description:the Top Function to help counting files of path
 * * Version:1.0
 * * Date :2020.1.9
 * * Author:liuzeming
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * * liuzeming    20200109   1.0       TopFunction to get files of path
 ****************************************************************/
package com.filemanager.fileoperate.copy

import android.content.Context
import android.net.Uri
import android.os.storage.StorageManager
import android.os.storage.StorageVolume
import android.text.TextUtils
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.CompatUtils
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.R
import com.oplus.compat.os.storage.StorageVolumeNative
import com.oplus.compat.utils.util.UnSupportedApiVersionException
import com.oplus.os.storage.OplusStorageVolume
import java.io.*
import java.nio.ByteBuffer
import java.nio.channels.FileChannel
import java.nio.file.Files
import java.nio.file.Path
import kotlin.io.path.deleteIfExists

object FileCopyHelper {
    private const val TAG = "FileCopyHelper"

    abstract class OnDealFileListener {
        private var mCurrentProgress = 0L

        internal fun addProgress(progress: Long) {
            mCurrentProgress += progress
            onProgressChanged(mCurrentProgress)
        }

        abstract var isCancel: () -> Boolean

        abstract fun onProgressChanged(progress: Long)

        /**
         * 复制文件成功回调
         * @param sourceFile 来源文件
         * @param destFile 目标文件
         * @param isDirectory 是否为文件夹
         */
        abstract fun onSuccess(sourceFile: File, destFile: File, isDirectory: Boolean)

        abstract fun onError(sourceFile: File, destFile: File): Boolean
    }

    @Volatile
    private var mForceInterruptState: Byte? = null

    fun init() {
        mForceInterruptState = null
    }

    fun forceInterrupt() {
        mForceInterruptState = 0
    }

    fun copyFile(sourceFile: File, destFile: File, isDirectory: Boolean, listener: OnDealFileListener): Boolean {
        fun internalCopyFile(sourceFile: File, destFile: File, isOtg: Boolean, isSdCard: Boolean, isDfm: Boolean, isDirectory: Boolean): Boolean {
            if (sourceFile.isDirectory) {
                if ((mForceInterruptState == null) && (destFile.exists() || destFile.mkdir())) {
                    listener.addProgress(sourceFile.length())
                    val fileList = JavaFileHelper.listFiles(sourceFile)
                    if (fileList == null) {
                        Log.d(TAG, "internalCopyFile ${sourceFile.absolutePath} listFiles is null")
                    } else {
                        fileList.forEach {
                            if (listener.isCancel.invoke()
                                    || !internalCopyFile(it, File(destFile, it.name), isOtg, isSdCard, isDfm, isDirectory)) {
                                return false
                            }
                        }
                    }
                    listener.onSuccess(sourceFile, destFile, isDirectory)
                    return true
                } else {
                    Log.d(TAG, "copyFile: failed to create dir: ${destFile.absolutePath}")
                    // If return true, skip this file, else cancel copy
                    return listener.onError(sourceFile, destFile)
                }
            } else {
                return copyFileData(sourceFile, destFile, isOtg, isSdCard, isDfm, isDirectory, listener)
            }
        }

        val isOTGPath = VolumeEnvironment.isOTGPath(MyApplication.sAppContext, destFile.absolutePath)
        val isSdcardPath = VolumeEnvironment.isSdcardPath(MyApplication.sAppContext, destFile.absolutePath)
        val isDfmPath = KtUtils.checkIsDfmPath(destFile.absolutePath)
        Log.d(TAG, "copyFile: sourceFile=${sourceFile.absolutePath}, destDir=${destFile.absolutePath}")
        return internalCopyFile(sourceFile, destFile, isOTGPath, isSdcardPath, isDfmPath, isDirectory)
    }

    @Suppress("TooGenericExceptionCaught")
    private fun copyFileData(
        sourceFile: File,
        destFile: File,
        isOtg: Boolean,
        isSdCard: Boolean,
        isDfm: Boolean,
        isDirectory: Boolean,
        listener: OnDealFileListener
    ): Boolean {
        Log.d(TAG, "internalCopyFile: sourceFile=${sourceFile.absolutePath}, destFile=${destFile.absolutePath}")
        var destRAF: RandomAccessFile? = null
        var srcRAF: RandomAccessFile? = null
        var srcChannel: FileChannel? = null
        var destChannel: FileChannel? = null
        var currentLen: Long = 0
        var copyInterrupt = true
        var hasException = false
        try {
            var size: Long = 0
            destRAF = RandomAccessFile(destFile, "rw")
            srcRAF = RandomAccessFile(sourceFile, "r")
            srcChannel = srcRAF.channel
            destChannel = destRAF.channel
            size = srcChannel.size()
            var length = 1024 * 128.toLong()
            val len = 1024 * 64
            val buff = ByteBuffer.allocate(len)
            if (size < Int.MAX_VALUE && !(isOtg || isSdCard || isDfm)) {
                while (!listener.isCancel.invoke()) {
                    if (size == currentLen) {
                        copyInterrupt = false
                        break
                    }
                    if (mForceInterruptState != null) { throw InterruptedException("Force interrupted by external") }
                    if (size - currentLen < length) {
                        length = size - currentLen
                    }
                    srcChannel.transferTo(currentLen, length, destChannel)
                    listener.addProgress(length)
                    currentLen += length
                }
            } else {
                while (!listener.isCancel.invoke()) {
                    val readLen = srcChannel.read(buff)
                    if (-1 == readLen) {
                        copyInterrupt = false
                        break
                    }
                    if (mForceInterruptState != null) { throw InterruptedException("Force interrupted by external") }
                    buff.flip()
                    destChannel.write(buff)
                    listener.addProgress(readLen.toLong())
                    buff.clear()
                    currentLen += readLen
                }
                Log.d(TAG, "destChannel.force start")
                if (!listener.isCancel.invoke()) {
                    destChannel.force(true)
                }
                Log.d(TAG, "destChannel.force end")
            }
        } catch (e: Exception) {
            Log.e(TAG, "copyFileData failed: ${e.message}")
            // If return true, skip this file, else cancel copy
            listener.addProgress(sourceFile.length() - currentLen)
            hasException = true
        } finally {
            fileCopyDataDoFinally(copyInterrupt, destFile, srcRAF, srcChannel, destChannel, destRAF)
        }
        return fileCopyDataReturn(listener, hasException, sourceFile, destFile, isDirectory)
    }

    private fun fileCopyDataDoFinally(
        copyInterrupt: Boolean,
        destFile: File,
        srcRAF: RandomAccessFile?,
        srcChannel: FileChannel?,
        destChannel: FileChannel?,
        destRAF: RandomAccessFile?
    ) {
        if (copyInterrupt && destFile.exists()) { // Delete the dest file when copy progress interrupt
            destFile.delete()
        }
        quietClose(srcRAF)
        quietClose(srcChannel)
        quietClose(destChannel)
        quietClose(destRAF)
    }

    private fun fileCopyDataReturn(
        listener: OnDealFileListener,
        hasException: Boolean,
        sourceFile: File,
        destFile: File,
        isDirectory: Boolean
    ): Boolean {
        return when {
            listener.isCancel.invoke() -> {
                Log.d(TAG, "copyFileData failed: listener.isCancel = true")
                if (destFile.exists()) {
                    Log.d(TAG, "fileCopyDataReturn isCancel delete")
                    destFile.delete()
                }
                false
            }
            hasException -> listener.onError(sourceFile, destFile)
            else -> {
                listener.onSuccess(sourceFile, destFile, isDirectory)
                true
            }
        }
    }

    private fun quietClose(stream: Closeable?) {
        try {
            stream?.close()
        } catch (e: Exception) {
        }
    }

    fun checkInSameFolder(sourceList: List<BaseFileBean>, destDir: BaseFileBean): Pair<Boolean, String?> {
        if (destDir.mData.isNullOrEmpty()) {
            return Pair(false, null)
        }
        var sourceFile: File
        var errorMsg: String? = null
        for (sf in sourceList) {
            if (!TextUtils.isEmpty(sf.mData) && !TextUtils.isEmpty(sf.mDisplayName)) {
                sourceFile = File(destDir.mData, sf.mDisplayName)
                errorMsg = when {
                    sourceFile.absolutePath == sf.mData -> { // 源路径和目标路径相同
                        MyApplication.sAppContext.getString(com.filemanager.common.R.string.paste_same_file_normal)
                    }
                    sf.mData == destDir.mData -> { // 目标文件夹和源文件相同
                        String.format(MyApplication.sAppContext.getString(com.filemanager.common.R.string.paste_error_source_folder), sf.mData)
                    }
                    destDir.mData!!.startsWith(sf.mData!!.plus(File.separator)) -> {
                        String.format(MyApplication.sAppContext.getString(com.filemanager.common.R.string.paste_error_sub_source_folder), sf.mData)
                    }
                    else -> null
                }
                if (!errorMsg.isNullOrEmpty()) {
                    return Pair(true, errorMsg)
                }
            }
        }
        return Pair(false, null)
    }

    fun checkIsBadSdDialog(destPath: String?): Pair<Boolean, Boolean> {
        Log.d(TAG, "checkIsBadSdDialog: $destPath")
        try {
            val manager = MyApplication.sAppContext.getSystemService(Context.STORAGE_SERVICE) as StorageManager
            manager.getStorageVolume(File(destPath))?.let { volume ->
                val type = getOplusReadOnlyType(volume)
                Log.d(TAG, "checkIsBadSdDialog: type=$type")
                var isReadOnlyType = false
                if ((type == 1) || (type == 2) || isReadOnlyType(destPath).also { isReadOnlyType = it }) {
                    return Pair(true, isReadOnlyType)
                }
            }
        } catch (e: NoSuchMethodError) { // ignored
        }
        return Pair(false, second = false)
    }


    fun getOplusReadOnlyType(volume: StorageVolume): Int {
        var type = -1
        CompatUtils.compactApi(CompatUtils.OS_14, CompatUtils.OS_14_SUB_V, {
            type = OplusStorageVolume(volume).oplusReadOnlyType
        }, {
            try {
                type = StorageVolumeNative.getOplusReadOnlyType(volume)
            } catch (e: UnSupportedApiVersionException) {
                Log.e(TAG, "getOplusReadOnlyType has error ${e.message}")
            }
        })
        return type
    }

    fun isReadOnlyType(destPath: String?): Boolean {
        if (destPath.isNullOrEmpty() || !VolumeEnvironment.isSdcardPath(MyApplication.sAppContext, destPath)) {
            return false
        }
        val paths = destPath.split("/").toTypedArray()
        val len = 3
        if (paths.size < len) {
            return false
        }
        val name = paths[2]
        if (TextUtils.isEmpty(name)) {
            return false
        }

        val file = File("/proc/mounts")
        var reader: BufferedReader? = null
        try {
            reader = BufferedReader(FileReader(file))
            var tempString: String?
            val condition1 = "/mnt/media_rw/$name"
            val condition2 = "ro,"
            while (reader.readLine().also { tempString = it } != null) {
                if (tempString!!.contains(condition1) && tempString!!.contains(condition2)) {
                    return true
                }
            }
        } catch (e: Exception) {
            Log.d(TAG, "isReadOnlyType failed: ${e.message}")
        } finally {
            quietClose(reader)
        }
        return false
    }

    fun checkTotalSize(file: File): Long {
        return if (file.isDirectory) {
            var total: Long = file.length() // include the self of this dir
            JavaFileHelper.listFiles(file)?.forEach {
                val result = checkTotalSize(it)
                total += result
            }
            total
        } else {
            file.length()
        }
    }

    fun copyToFileByUri(uri: Uri, destPath: Path, useBuffer: Boolean = false, listener: OnDealFileListener): Boolean {
        if (!Files.isDirectory(destPath.parent)) {
            Files.createDirectories(destPath.parent)
        }
        var fileOutputStream: FileOutputStream? = null
        var uriInputStream: InputStream? = null
        val buffer = ByteArray(CommonConstants.COPY_BUF_SIZE)
        var bufferedInputStream: BufferedInputStream? = null
        var bufferedOutputStream: BufferedOutputStream? = null
        try {
            fileOutputStream = FileOutputStream(destPath.toFile())
            uriInputStream = MyApplication.sAppContext.contentResolver.openInputStream(uri)
            if (useBuffer) {
                bufferedInputStream = BufferedInputStream(uriInputStream,
                    CommonConstants.COPY_BUF_SIZE
                )
                bufferedOutputStream = BufferedOutputStream(fileOutputStream,
                    CommonConstants.COPY_BUF_SIZE
                )
            } else {
                bufferedInputStream = BufferedInputStream(uriInputStream)
                bufferedOutputStream = BufferedOutputStream(fileOutputStream)
            }
            var n = 0
            while (0 < bufferedInputStream.read(buffer).also { n = it }) {
                bufferedOutputStream.write(buffer, 0, n)
                listener.addProgress(n.toLong())
            }
            listener.onSuccess(destPath.toFile(), destPath.toFile(), false)
            return true
        } catch (e: Exception) {
            Log.e(TAG, "copyToFileByUri copy e=$e")
            destPath.deleteIfExists()
        } finally {
            try {
                bufferedInputStream?.close()
            } catch (e: IOException) {
                Log.e(TAG, "copyToFileByUri close e=$e")
            }
            try {
                bufferedOutputStream?.close()
            } catch (e: IOException) {
                Log.e(TAG, "copyToFileByUri close e=$e")
            }
            try {
                fileOutputStream?.close()
            } catch (e: IOException) {
                Log.e(TAG, "copyToFileByUri close e=$e")
            }
            try {
                uriInputStream?.close()
            } catch (e: IOException) {
                Log.e(TAG, "copyToFileByUri close e=$e")
            }
        }
        return false
    }
}