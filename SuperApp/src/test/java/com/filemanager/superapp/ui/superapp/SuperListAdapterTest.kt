/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SuperListAdapterTest
 * * Description : SuperListAdapter Unit Test
 * * Version     : 1.0
 * * Date        : 2022/12/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.superapp.ui.superapp

import android.app.Activity
import android.content.res.Resources
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.isActivityAndInvalid
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.GridThumbView
import com.filemanager.common.view.MiddleMultilineTextView
import com.filemanager.common.viewholder.FileBrowserGridVH
import com.filemanager.common.viewholder.FileDocGridVH
import com.filemanager.common.viewholder.NormalGridVH
import com.filemanager.common.viewholder.NormalListVH
import com.filemanager.superapp.R
import com.oplus.dropdrag.SelectionTracker.Companion.NO_LONG_ITEM_ID
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SuperListAdapterTest {

    private lateinit var context: Activity
    private lateinit var adapter: SuperListAdapter

    @Before
    fun setup() {
        val resourcesMock = mockk<Resources>().apply {
            every { getDimension(any()) }.returns(4f)
            every { getDimensionPixelSize(any())}.returns(0)
        }
        context = mockk<Activity>().apply {
            every { applicationContext }.returns(this)
            every { resources }.returns(resourcesMock)
        }
        MyApplication.init(context)
        adapter = mockk()
        every { adapter.mImgRadius }.returns(8)
        every { adapter.mContext }.returns(context)
        every { adapter.mFiles }.answers { callOriginal() }
        every { adapter.mFiles = any() }.answers { callOriginal() }
        mockkStatic(Utils::class)
        mockkStatic(SdkUtils::class)
        every { Utils.isRtl() }.returns(false)
        mockkStatic(LayoutInflater::class)
        mockkStatic(WindowUtils::class)
        every { WindowUtils.supportLargeScreenLayout(any()) }.returns(false)
    }

    @After
    fun teardown() {
        unmockkStatic(Utils::class)
        unmockkStatic(LayoutInflater::class)
        unmockkStatic(SdkUtils::class)
        unmockkStatic(WindowUtils::class)
    }

    @Test
    fun should_call_notifyDataSetChanged_when_setData() {
        every { adapter.setData(any(), any(), any()) }.answers { callOriginal() }
        justRun { adapter.notifyDataSetChanged() }
        every { adapter.mSelectionArray = any() }.answers { callOriginal() }
        val list = ArrayList<BaseFileBean>()
        list.add(BaseFileBean())
        list.add(BaseFileBean())
        adapter.setData(list, arrayListOf(1, 2, 3), "")
        verify { adapter.notifyDataSetChanged() }
        Assert.assertNotNull(adapter.mFiles)
    }

    @Test
    fun should_when_onCreateViewHolder() {
        every { adapter.onCreateViewHolder(any(), any()) }.answers { callOriginal() }
        val itemView = mockk<View>()
        val img = mockk<FileThumbView>()
        every { itemView.findViewById<FileThumbView>(com.filemanager.common.R.id.file_list_item_icon) }.returns(img)
        every { itemView.findViewById<ImageView>(com.filemanager.common.R.id.apk_icon) }.returns(mockk())
        every { itemView.findViewById<TextView>(com.filemanager.common.R.id.file_list_item_title) }.returns(mockk())
        every { itemView.findViewById<TextView>(com.filemanager.common.R.id.file_list_item_detail) }.returns(mockk())
        every { itemView.findViewById<TextView>(com.filemanager.common.R.id.file_duration_tv) }.returns(mockk())
        every { itemView.findViewById<COUICheckBox>(com.filemanager.common.R.id.listview_scrollchoice_checkbox) }.returns(mockk())
        every { itemView.findViewById<COUICheckBox>(com.filemanager.common.R.id.gridview_scrollchoice_checkbox) }.returns(mockk())

        every { itemView.findViewById<GridThumbView>(com.filemanager.common.R.id.file_grid_item_icon) }.returns(mockk())
        every { itemView.findViewById<MiddleMultilineTextView>(com.filemanager.common.R.id.title_tv) }.returns(mockk())
        every { itemView.findViewById<TextView>(com.filemanager.common.R.id.detail_tv) }.returns(mockk())
        every { itemView.findViewById<RelativeLayout>(com.filemanager.common.R.id.file_grid_item_layout) }.returns(mockk())
        every { itemView.findViewById<View>(com.filemanager.common.R.id.divider_line) }.returns(mockk())
        every { itemView.findViewById<ConstraintLayout>(com.filemanager.common.R.id.normal_list_item_root) }.returns(mockk())
        every { itemView.findViewById<View>(com.filemanager.common.R.id.file_list_item_icon_container) }.returns(mockk())

        val inflater = mockk<LayoutInflater>()
        every { LayoutInflater.from(any()) }.returns(inflater)
        every { inflater.inflate(any<Int>(), any(), false) }.returns(itemView)
        val parent = mockk<ViewGroup>()
        every { parent.context }.returns(context)

        var holder = adapter.onCreateViewHolder(parent, SuperListAdapter.VIEW_TYPE_ITEM_FOOTER)
        Assert.assertTrue(holder is SuperListAdapter.FootViewHolder)

        holder = adapter.onCreateViewHolder(parent, KtConstants.SCAN_MODE_LIST)
        Assert.assertTrue(holder is NormalListVH)

        holder = adapter.onCreateViewHolder(parent, KtConstants.SCAN_MODE_GRID + Constants.TAB_ALL)
        Assert.assertTrue(holder is FileBrowserGridVH)

        holder = adapter.onCreateViewHolder(parent, KtConstants.SCAN_MODE_GRID + Constants.TAB_IMAGE)
        Assert.assertTrue(holder is NormalGridVH)

        holder = adapter.onCreateViewHolder(parent, KtConstants.SCAN_MODE_GRID + Constants.TAB_VIDEO)
        Assert.assertTrue(holder is NormalGridVH)

        holder = adapter.onCreateViewHolder(parent, KtConstants.SCAN_MODE_GRID + Constants.TAB_AUDIO)
        Assert.assertTrue(holder is FileBrowserGridVH)

        holder = adapter.onCreateViewHolder(parent, KtConstants.SCAN_MODE_GRID + Constants.TAB_OTHER)
        Assert.assertTrue(holder is FileBrowserGridVH)

        holder = adapter.onCreateViewHolder(parent, 100)
        Assert.assertTrue(holder is NormalListVH)
    }

    @Test
    fun should_when_onBindViewHolder() {
        every { adapter.onBindViewHolder(any(), any()) }.answers { callOriginal() }
        every { adapter.mChoiceMode }.answers { false }
        every { adapter.mSizeCache }.returns(mockk())
        every { adapter.mSelectionArray }.returns(mockk())
        every { adapter.mThreadManager }.returns(mockk())
        every { context.isFinishing }.returns(true)
        every { context.isDestroyed }.returns(true)
        every { adapter.itemCount }.returns(10)
        every { adapter.mFiles }.returns(mutableListOf(BaseFileBean()))
        every { adapter.getItemKey(any(), any()) }.returns(100)
        every { adapter.clickPreviewFile }.returns(BaseFileBean())
        val holder = mockk<SuperListAdapter.FootViewHolder>().apply {
            justRun { loadData(any(), any(), any(), any(), any()) }
        }
        adapter.onBindViewHolder(mockk(), -1)
        verify(inverse = true) { context.isActivityAndInvalid() }

        adapter.onBindViewHolder(mockk(), 11)
        verify(inverse = true) { context.isActivityAndInvalid() }

        adapter.onBindViewHolder(holder, 0)
        verify(inverse = true) { holder.loadData(any(), any(), any(), any(), any()) }

        every { context.isFinishing }.returns(false)
        every { context.isDestroyed }.returns(false)
        adapter.onBindViewHolder(holder, 0)
        verify { holder.loadData(any(), any(), any(), any(), any()) }

        val normalVH = mockk<NormalListVH>().apply {
            justRun { loadData(any(), any(), any(), any(), any(), any(), any(), any()) }
            justRun { updateDividerVisible(any(), any()) }
            justRun { setSelected(any()) }
        }
        adapter.onBindViewHolder(normalVH, 0)
        verify { normalVH.loadData(any(), any(), any(), any(), any(), any(), any(), any()) }

        val fileGridVH = mockk<FileBrowserGridVH>().apply {
            justRun { loadData(any(), any(), any(), any(), any(), any(), any(), any()) }
        }
        adapter.onBindViewHolder(fileGridVH, 0)
        verify { fileGridVH.loadData(any(), any(), any(), any(), any(), any(), any(), any()) }

        val docGridVH = mockk<FileDocGridVH>().apply {
            justRun { loadData(any(), any(), any(), any(), any(), any(), any(), any()) }
            justRun { adaptItemSize(any()) }
        }
        adapter.onBindViewHolder(docGridVH, 0)
        verify { docGridVH.loadData(any(), any(), any(), any(), any(), any(), any(), any()) }

        val normalGridVH = mockk<NormalGridVH>().apply {
            justRun { loadData(any(), any(), any(), any(), any(), any(), any(), any()) }
            justRun { setItemWidth(any()) }
        }
        adapter.onBindViewHolder(normalGridVH, 0)
        verify { normalGridVH.loadData(any(), any(), any(), any(), any(), any(), any(), any()) }
    }

    @Test
    fun should_when_onDestroy() {
        every { adapter.onDestroy() }.answers { callOriginal() }
        val map = hashMapOf<String, String>().apply {
            put("1.png", "220")
            put("1.text", "120")
        }
        every { adapter.mSizeCache }.returns(map)
        val handler = mockk<Handler>()
        justRun { handler.removeCallbacksAndMessages(any()) }
        every { adapter.mUiHandler }.returns(handler)

        adapter.onDestroy()

        Assert.assertTrue(map.isEmpty())
        verify { handler.removeCallbacksAndMessages(null) }
    }

    @Test
    fun should_when_setKeyWord() {
        every { adapter.setKeyWord(any()) }.answers { callOriginal() }
        every { adapter.mKeyWord = any() }.answers { callOriginal() }
        every { adapter.mKeyWord }.answers { callOriginal() }

        adapter.setKeyWord("png")
        Assert.assertEquals("png", adapter.mKeyWord)
    }

    @Test
    fun should_return_int_when_getItemKey() {
        every { adapter.getItemKey(any(), any()) }.answers { callOriginal() }
        val fileBean = BaseFileBean()

        var key = adapter.getItemKey(fileBean, 10)
        Assert.assertEquals(key, 10)

        fileBean.mData = "/sdcard/DCIM/1.png"
        key = adapter.getItemKey(fileBean, 10)
        Assert.assertNotNull(key)
    }

    @Test
    fun should_when_getItemId() {
        every { adapter.getItemId(any()) }.answers { callOriginal() }
        every { adapter.getItemKeyByPosition(any()) }.returns(null)
        var itemId = adapter.getItemId(10)
        Assert.assertEquals(itemId, NO_LONG_ITEM_ID)

        every { adapter.getItemKeyByPosition(any()) }.returns(10)
        itemId = adapter.getItemId(1)
        Assert.assertEquals(itemId, 10)
    }

    @Test
    fun should_return_int_when_getItemViewType() {
        every { adapter.getItemViewType(any()) }.answers { callOriginal() }
        val list = mutableListOf<BaseFileBean>()
        list.add(BaseFileBean())
        list.add(BaseFileBean())
        list.add(BaseFileBean())
        list.add(BaseFileBean())
        list.add(BaseFileBean())
        every { adapter.mFiles }.returns(list)
        var type = adapter.getItemViewType(6)
        Assert.assertEquals(type, SuperListAdapter.VIEW_TYPE_ITEM_FOOTER)

        every { adapter.mScanViewModel }.returns(KtConstants.SCAN_MODE_LIST)
        type = adapter.getItemViewType(2)
        Assert.assertEquals(type, KtConstants.SCAN_MODE_LIST)

        every { adapter.mScanViewModel }.returns(KtConstants.SCAN_MODE_GRID)
        every { adapter.mTabPosition }.returns(Constants.TAB_ALL)
        type = adapter.getItemViewType(2)
        Assert.assertEquals(type, KtConstants.SCAN_MODE_GRID + Constants.TAB_ALL)
    }

    @Test
    fun should_when_initListChoiceModeAnimFlag() {
        every { adapter.initListChoiceModeAnimFlag(any()) }.answers { callOriginal() }
        justRun { adapter.setChoiceModeAnimFlag(any()) }

        every { adapter.mScanViewModel }.returns(KtConstants.SCAN_MODE_GRID)
        adapter.initListChoiceModeAnimFlag(true)
        verify(inverse = true) { adapter.setChoiceModeAnimFlag(any()) }

        every { adapter.mScanViewModel }.returns(KtConstants.SCAN_MODE_LIST)
        adapter.initListChoiceModeAnimFlag(true)
        verify { adapter.setChoiceModeAnimFlag(any()) }
    }

    @Test
    fun should_when_FootViewHolder() {
        val itemView = mockk<View>()
        val footTextView = mockk<TextView>()
        justRun { footTextView.text = any() }
        justRun { footTextView.movementMethod = any() }
        justRun { footTextView.gravity = any() }
        justRun { footTextView.setPadding(any(), any(), any(), any()) }
        every { footTextView.paddingTop } returns 0
        every { footTextView.paddingBottom } returns 0
        every { footTextView.paddingLeft } returns 0
        every { footTextView.paddingRight } returns 0
        every { itemView.findViewById<TextView>(R.id.footer_view) }.answers { footTextView }
        val holder = spyk(SuperListAdapter.FootViewHolder(itemView))
        every { holder.loadData(any(), KtConstants.SCAN_MODE_GRID, true, "superAppPackage", 0) }.answers { callOriginal() }

        val resources = mockk<Resources>().apply {
            every { getColorStateList(any(), any()); }.returns(mockk())
        }
        val activity = mockk<SuperAppActivity>()
        every { activity.resources }.returns(resources)
        every { activity.theme }.returns(mockk())
        mockkStatic(AppCompatResources::class)
        every { AppCompatResources.getColorStateList(activity, any()) }.returns(mockk())
        every { activity.getString(any()) }.returns("全部系统文件")
        every { activity.getString(any(), any()) }.returns("文件找不到？可尝试在全部系统文件中查看")

        every { SdkUtils.isAtLeastOS16() }.returns(true)
        holder.loadData(activity, KtConstants.SCAN_MODE_GRID, true, KtConstants.PKG_OWORK, 0)
        verify { activity.getString(any()) }

        holder.loadData(activity, KtConstants.SCAN_MODE_GRID, true, KtConstants.PKG_OWORK, 0)
        verify { footTextView.text = any() }
    }

    @Test
    fun testTabPosition() {
        val itemView = mockk<View>()
        val footTextView = mockk<TextView>()
        justRun { footTextView.text = any() }
        justRun { footTextView.movementMethod = any() }
        justRun { footTextView.gravity = any() }
        justRun { footTextView.setPadding(any(), any(), any(), any()) }
        every { footTextView.paddingTop } returns 0
        every { footTextView.paddingBottom } returns 0
        every { footTextView.paddingLeft } returns 0
        every { footTextView.paddingRight } returns 0
        every { itemView.findViewById<TextView>(R.id.footer_view) }.answers { footTextView }
        val holder = spyk(SuperListAdapter.FootViewHolder(itemView))
        Assert.assertFalse(holder.isImageOrVideoTab(Constants.TAB_ALL))
        Assert.assertTrue(holder.isImageOrVideoTab(Constants.TAB_IMAGE))
        Assert.assertTrue(holder.isImageOrVideoTab(Constants.TAB_VIDEO))
    }
}