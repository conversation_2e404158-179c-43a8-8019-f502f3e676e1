/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DFMManagerTest
 ** Description : DFM manager Unit test
 ** Version     : 1.0
 ** Date        : 2024/04/11 11:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/04/11       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.dfm

import android.content.Context
import android.os.Bundle
import com.oplus.dfs.imp.RemoteDeviceInfo
import com.oplus.dfs.service.DfsManager
import com.oplus.dfs.util.inf.IDfmStatusNotify
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import java.util.function.BiConsumer
import java.util.function.Consumer

class DFMManagerTest {

    private lateinit var context: Context
    private lateinit var dfsManager: DfsManager

    @Before
    fun setup() {
        context = mockk()
        dfsManager = mockk()
        mockkStatic(DfsManager::class)
        mockkStatic(DFMManager::class)
        every { DfsManager.getInstance() }.returns(dfsManager)
    }

    @After
    fun teardown() {
        unmockkStatic(DfsManager::class)
        mockkStatic(DFMManager::class)
    }

    @Test
    fun should_call_dfs_init_when_init() {
        every { DFMManager.init(any(), any()) }.answers { callOriginal() }
        justRun { dfsManager.init(any(), any(), any()) }
        DFMManager.init(context, true)
        verify { dfsManager.init(context, any(), true) }
    }

    @Test
    fun should_call_handleRemoteDevice_when_onDfmRemoteMetaData() {
        every { DFMManager.init(any(), any()) }.answers { callOriginal() }
        justRun { DFMManager.handleRemoteDevice() }

        var notify: IDfmStatusNotify? = null
        every { dfsManager.init(any(), any(), any()) }.answers {
            notify = secondArg()
        }
        DFMManager.init(context, true)
        notify?.onDfmRemoteMetaData(null)
        verify(inverse = true) { DFMManager.handleRemoteDevice() }

        notify?.onDfmRemoteMetaData(RemoteDeviceInfo())
        verify { DFMManager.handleRemoteDevice() }

        // 重置remoteDevice为null
        notify?.onDfmRemoteNotSupport()
    }

    @Test
    fun should_call_handleDfsDisconnect_when_onDfmServiceFail() {
        every { DFMManager.init(any(), any()) }.answers { callOriginal() }
        justRun { DFMManager.handleDfsDisconnect() }

        var notify: IDfmStatusNotify? = null
        every { dfsManager.init(any(), any(), any()) }.answers {
            notify = secondArg()
        }

        DFMManager.init(context, true)
        notify?.onDfmServiceFail()
        verify { DFMManager.handleDfsDisconnect() }
    }

    @Test
    fun should_call_handleDfsDisconnect_when_onDfmDeath() {
        every { DFMManager.init(any(), any()) }.answers { callOriginal() }
        justRun { DFMManager.handleDfsDisconnect() }

        var notify: IDfmStatusNotify? = null
        every { dfsManager.init(any(), any(), any()) }.answers {
            notify = secondArg()
        }

        DFMManager.init(context, true)
        notify?.onDfmDeath()
        verify { DFMManager.handleDfsDisconnect() }
    }

    @Test
    fun should_call_handleDfsDisconnect_when_onDfmRemoteDeviceDisconnected() {
        every { DFMManager.init(any(), any()) }.answers { callOriginal() }
        justRun { DFMManager.handleDfsDisconnect() }

        var notify: IDfmStatusNotify? = null
        every { dfsManager.init(any(), any(), any()) }.answers {
            notify = secondArg()
        }

        DFMManager.init(context, true)
        notify?.onDfmRemoteDeviceDisconnected()
        verify { DFMManager.handleDfsDisconnect() }
    }

    @Test
    fun should_call_accept_when_onDfmP2PConnectionConflict() {
        every { DFMManager.init(any(), any()) }.answers { callOriginal() }
        every { DFMManager.registerConflictListener(any()) }.answers { callOriginal() }
        every { DFMManager.unregisterConflictListener(any()) }.answers { callOriginal() }

        var notify: IDfmStatusNotify? = null
        every { dfsManager.init(any(), any(), any()) }.answers {
            notify = secondArg()
        }
        val conflictListener = mockk<BiConsumer<Int, Int>>()
        justRun { conflictListener.accept(any(), any()) }
        DFMManager.registerConflictListener(conflictListener)

        DFMManager.init(context, true)
        notify?.onDfmP2PConnectionConflict(100)
        verify { conflictListener.accept(DFMManager.ACTION_CONNECT_CONFLICT, 100) }

        DFMManager.unregisterConflictListener(conflictListener)
    }

    @Test
    fun should_call_accept_when_onDfmP2PUsed() {
        every { DFMManager.init(any(), any()) }.answers { callOriginal() }
        every { DFMManager.registerConflictListener(any()) }.answers { callOriginal() }
        every { DFMManager.unregisterConflictListener(any()) }.answers { callOriginal() }

        var notify: IDfmStatusNotify? = null
        every { dfsManager.init(any(), any(), any()) }.answers {
            notify = secondArg()
        }
        val conflictListener = mockk<BiConsumer<Int, Int>>()
        justRun { conflictListener.accept(any(), any()) }
        DFMManager.registerConflictListener(conflictListener)

        DFMManager.init(context, true)
        notify?.onDfmP2PUsed()
        verify { conflictListener.accept(DFMManager.ACTION_P2P_USED, DFMManager.UNDEFINED_TYPE) }

        DFMManager.unregisterConflictListener(conflictListener)
    }

    @Test
    fun should_call_onDfmFileChangeListener_when_onDfmRemoteFileChange() {
        every { DFMManager.init(any(), any()) }.answers { callOriginal() }
        every { DFMManager.registerDfmFileChangeListener(any()) }.answers { callOriginal() }

        var notify: IDfmStatusNotify? = null
        every { dfsManager.init(any(), any(), any()) }.answers {
            notify = secondArg()
        }
        val fileChangeListener = mockk<OnDfmFileChangeListener>()
        justRun { fileChangeListener.onDfmFileChanged() }
        DFMManager.registerDfmFileChangeListener(fileChangeListener)

        DFMManager.init(context, true)
        notify?.onDfmRemoteFileChange()
        verify { fileChangeListener.onDfmFileChanged() }
    }

    private fun mockSetRemoteDevice(deviceInfo: RemoteDeviceInfo?, justRun: Boolean = true) {
        every { DFMManager.init(any(), any()) }.answers { callOriginal() }
        justRun { dfsManager.init(any(), any(), any()) }
        if (justRun) {
            justRun { DFMManager.handleRemoteDevice() }
        } else {
            every { DFMManager.handleRemoteDevice() }.answers { callOriginal() }
        }

        var notify: IDfmStatusNotify? = null
        every { dfsManager.init(any(), any(), any()) }.answers {
            notify = secondArg()
        }
        DFMManager.init(context, true)

        if (deviceInfo == null) {
            notify?.onDfmRemoteNotSupport()
        } else {
            notify?.onDfmRemoteMetaData(deviceInfo)
        }
    }

    @Test
    fun should_call_accept_when_handleRemoteDevice() {
        every { DFMManager.getDFSDevice(any()) }.answers { callOriginal() }
        every { DFMManager.handleRemoteDevice() }.answers { callOriginal() }
        val consumer = mockk<Consumer<Bundle>>()
        justRun { consumer.accept(any()) }

        DFMManager.getDFSDevice(consumer)
        mockSetRemoteDevice(null, false)
        DFMManager.handleRemoteDevice()
        verify { consumer.accept(any()) }

        mockSetRemoteDevice(RemoteDeviceInfo(), false)
        DFMManager.handleRemoteDevice()
        verify { consumer.accept(any()) }
    }

    @Test
    fun should_notNull_when_getDFSDevice_with_consumer() {
        every { DFMManager.getDFSDevice(any()) }.answers { callOriginal() }
        val consumer = Consumer<Bundle> {}

        mockSetRemoteDevice(null)
        var bundle = DFMManager.getDFSDevice(consumer)
        Assert.assertNotNull(bundle)
        Assert.assertNull(bundle.getString("device_name"))

        mockSetRemoteDevice(RemoteDeviceInfo().apply {
            this.remoteDeviceName = "测试手机"
            this.remoteDeviceType = 10
            this.remoteDeviceExternalStorageSpace = 10 * 1024
            this.remoteDeviceExternalStorageFreeSpace = 5 * 1024
        })
        bundle = DFMManager.getDFSDevice(consumer)
        Assert.assertNotNull(bundle)
    }

    @Test
    fun should_notNull_when_getDFSDevice() {
        every { DFMManager.getDFSDevice() }.answers { callOriginal() }

        mockSetRemoteDevice(null)
        var bundle = DFMManager.getDFSDevice()
        Assert.assertNull(bundle)
        Assert.assertNull(bundle?.getString("device_name"))

        mockSetRemoteDevice(RemoteDeviceInfo().apply {
            this.remoteDeviceName = "测试手机"
            this.remoteDeviceType = 10
            this.remoteDeviceExternalStorageSpace = 10 * 1024
            this.remoteDeviceExternalStorageFreeSpace = 5 * 1024
        })
        bundle = DFMManager.getDFSDevice()
        Assert.assertNotNull(bundle)
    }

    @Test
    fun should_notNull_when_getDFSMountPath() {
        every { DFMManager.getDFSMountPath() }.answers { callOriginal() }

        mockSetRemoteDevice(null)
        var path = DFMManager.getDFSMountPath()
        Assert.assertNull(path)

        mockSetRemoteDevice(RemoteDeviceInfo().apply {
            this.remoteDeviceName = "测试手机"
            this.remoteDeviceType = 10
            this.remoteDeviceExternalStorageSpace = 10 * 1024
            this.remoteDeviceExternalStorageFreeSpace = 5 * 1024
            this.remoteDeviceMountPath = "123456"
        })
        path = DFMManager.getDFSMountPath()
        Assert.assertNotNull(path)
        Assert.assertEquals(path, "/mnt/dfs/123456")
    }

    @Test
    fun should_notNull_when_getDFSDeviceName() {
        every { DFMManager.getDFSDeviceName() }.answers { callOriginal() }

        mockSetRemoteDevice(null)
        var name = DFMManager.getDFSDeviceName()
        Assert.assertNull(name)

        mockSetRemoteDevice(RemoteDeviceInfo().apply {
            this.remoteDeviceName = "测试手机"
            this.remoteDeviceType = 10
            this.remoteDeviceExternalStorageSpace = 10 * 1024
            this.remoteDeviceExternalStorageFreeSpace = 5 * 1024
            this.remoteDeviceMountPath = "123456"
        })
        name = DFMManager.getDFSDeviceName()
        Assert.assertNotNull(name)
        Assert.assertEquals(name, "测试手机")
    }

    @Ignore("Wait for correct")
    @Test
    fun should_notNull_when_getDFSAvailableSize() {
        every { DFMManager.getDFSAvailableSize() }.answers { callOriginal() }

        mockSetRemoteDevice(null)
        var size = DFMManager.getDFSAvailableSize()
        Assert.assertNull(size)

        mockSetRemoteDevice(RemoteDeviceInfo().apply {
            this.remoteDeviceName = "测试手机"
            this.remoteDeviceType = 10
            this.remoteDeviceExternalStorageSpace = 10 * 1024L
            this.remoteDeviceExternalStorageFreeSpace = 5 * 1024L
            this.remoteDeviceMountPath = "123456"
        })
        size = DFMManager.getDFSAvailableSize()
        Assert.assertNotNull(size)
        Assert.assertEquals(size, 5 * 1024L)
    }

    @Test
    fun should_notNull_when_getDFSTotalSize() {
        every { DFMManager.getDFSTotalSize() }.answers { callOriginal() }

        mockSetRemoteDevice(null)
        var size = DFMManager.getDFSTotalSize()
        Assert.assertNull(size)

        mockSetRemoteDevice(RemoteDeviceInfo().apply {
            this.remoteDeviceName = "测试手机"
            this.remoteDeviceType = 10
            this.remoteDeviceExternalStorageSpace = 10 * 1024L
            this.remoteDeviceExternalStorageFreeSpace = 5 * 1024L
            this.remoteDeviceMountPath = "123456"
        })
        size = DFMManager.getDFSTotalSize()
        Assert.assertNotNull(size)
        Assert.assertEquals(size, 10 * 1024L)
    }

    @Test
    fun should_notNull_when_getRemoteDeviceMountPath() {
        every { DFMManager.getRemoteDeviceMountPath() }.answers { callOriginal() }

        mockSetRemoteDevice(null)
        var path = DFMManager.getRemoteDeviceMountPath()
        Assert.assertNull(path)

        mockSetRemoteDevice(RemoteDeviceInfo().apply {
            this.remoteDeviceName = "测试手机"
            this.remoteDeviceType = 10
            this.remoteDeviceExternalStorageSpace = 10 * 1024
            this.remoteDeviceExternalStorageFreeSpace = 5 * 1024
            this.remoteDeviceMountPath = "123456"
        })
        path = DFMManager.getRemoteDeviceMountPath()
        Assert.assertNotNull(path)
    }

    @Test
    fun should_notNull_when_getRemoteUri() {
        every { DFMManager.getRemoteUri(any(), any()) }.answers { callOriginal() }
        every { dfsManager.getAuthorizedUriFromFile(any(), any()) }.returns(mockk())

        val uri = DFMManager.getRemoteUri("download", context)
        Assert.assertNotNull(uri)
        verify { dfsManager.getAuthorizedUriFromFile(context, "download") }
    }

    @Test
    fun should_call_unInit_when_exit() {
        every { DFMManager.exit() }.answers { callOriginal() }
        justRun { dfsManager.unInit() }

        DFMManager.exit()
        verify { dfsManager.unInit() }
    }

    @Test
    fun should_call_forcedP2PConnect_when_openConnect() {
        every { DFMManager.openConnect() }.answers { callOriginal() }
        justRun { dfsManager.forcedP2PConnect() }

        DFMManager.openConnect()
        verify { dfsManager.forcedP2PConnect() }
    }

    @Ignore("Wait for correct")
    @Test
    fun should_call_remoteDfsP2PConnect_when_openDfsP2pConnect() {
        every { DFMManager.openDfsP2pConnect() }.answers { callOriginal() }
        DFMManager.connectTime = 1L
        justRun { dfsManager.remoteDfsP2PConnect() }

        DFMManager.openDfsP2pConnect()
        verify { dfsManager.remoteDfsP2PConnect() }
    }

    @Test
    fun should_call_openDfsP2pConnect_when_openP2pConnectAndWaitDFSReady_openConnect() {
        every { DFMManager.openP2pConnectAndWaitDFSReady() }.answers { callOriginal() }
        justRun { DFMManager.sleep(any()) }
        justRun { DFMManager.openDfsP2pConnect() }

        mockSetRemoteDevice(RemoteDeviceInfo())
        DFMManager.remoteFileReady.set(false)
        DFMManager.isWaitingTimeOut.set(false)
        DFMManager.isP2PConnecting.set(true)
        DFMManager.openP2pConnectAndWaitDFSReady()
        verify(inverse = true) { DFMManager.openDfsP2pConnect() }

        DFMManager.remoteFileReady.set(false)
        DFMManager.isWaitingTimeOut.set(false)
        DFMManager.isP2PConnecting.set(false)
        DFMManager.openP2pConnectAndWaitDFSReady()
        verify { DFMManager.openDfsP2pConnect() }
    }

    @Test
    fun should_return_boolean_when_openP2pConnectAndWaitDFSReady() {
        every { DFMManager.openP2pConnectAndWaitDFSReady() }.answers { callOriginal() }
        justRun { DFMManager.sleep(any()) }
        justRun { DFMManager.openDfsP2pConnect() }

        mockSetRemoteDevice(null)
        var ready = DFMManager.openP2pConnectAndWaitDFSReady()
        Assert.assertFalse(ready)

        mockSetRemoteDevice(RemoteDeviceInfo())
        DFMManager.remoteFileReady.set(true)
        ready = DFMManager.openP2pConnectAndWaitDFSReady()
        Assert.assertTrue(ready)

        DFMManager.remoteFileReady.set(false)
        DFMManager.isWaitingTimeOut.set(true)
        ready = DFMManager.openP2pConnectAndWaitDFSReady()
        Assert.assertFalse(ready)


        DFMManager.remoteFileReady.set(false)
        DFMManager.isWaitingTimeOut.set(false)
        DFMManager.isP2PConnecting.set(true)
        ready = DFMManager.openP2pConnectAndWaitDFSReady()
        Assert.assertFalse(ready)
        verify { DFMManager.sleep(any()) }
        Assert.assertFalse(DFMManager.isWaitingTimeOut.get())
    }

    @Test
    fun should_notNull_when_registerConflictListener() {
        every { DFMManager.registerConflictListener(any()) }.answers { callOriginal() }
        every { DFMManager.unregisterConflictListener(any()) }.answers { callOriginal() }

        val listener = BiConsumer<Int, Int> { t, u -> }
        val listener2 = BiConsumer<Int, Int> { t, u -> }
        DFMManager.registerConflictListener(listener)
        Assert.assertEquals(1, DFMManager.conflictListenerList.size)

        DFMManager.registerConflictListener(listener)
        Assert.assertEquals(1, DFMManager.conflictListenerList.size)

        DFMManager.registerConflictListener(listener2)
        Assert.assertEquals(2, DFMManager.conflictListenerList.size)

        DFMManager.unregisterConflictListener(listener)
        DFMManager.unregisterConflictListener(listener2)
    }

    @Test
    fun should_call_dfs_listFileNames_when_listFileNames() {
        every { DFMManager.listFileNames(any()) }.answers { callOriginal() }
        every { dfsManager.listFileNames(any()) }.answers { arrayOf(firstArg()) }

        mockSetRemoteDevice(null)
        var fileNames = DFMManager.listFileNames("download")
        Assert.assertNull(fileNames)
        verify(inverse = true) { dfsManager.listFileNames(any()) }

        mockSetRemoteDevice(RemoteDeviceInfo())
        DFMManager.remoteFileReady.set(false)
        fileNames = DFMManager.listFileNames("download")
        Assert.assertNull(fileNames)
        verify(inverse = true) { dfsManager.listFileNames(any()) }

        DFMManager.remoteFileReady.set(true)
        fileNames = DFMManager.listFileNames("download")
        Assert.assertNotNull(fileNames)
        verify { dfsManager.listFileNames(any()) }
    }

    @Test
    fun should_setBroadCast_when_handleDfsDisconnect() {
        every { DFMManager.handleDfsDisconnect() }.answers { callOriginal() }
        every { DFMManager.init(any(), any()) }.answers { callOriginal() }
        justRun { dfsManager.init(any(), any(), any()) }
        justRun { context.sendBroadcast(any()) }

        DFMManager.init(context, true)
        DFMManager.handleDfsDisconnect()
        Assert.assertFalse(DFMManager.remoteFileReady.get())
        Assert.assertFalse(DFMManager.isP2PConnecting.get())
        Assert.assertFalse(DFMManager.isWaitingTimeOut.get())
        verify { context.sendBroadcast(any()) }
    }
}