/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WindowUtilsTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/4/21 11:16
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/4/21       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.main.utils

import android.app.Activity
import android.graphics.Rect
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class WindowUtilsTest {

    @MockK
    lateinit var activity: Activity

    @Before
    fun setUp() {
        activity = mockk(relaxed = true)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun testGetScreenWidth() {
        mockkStatic(COUIPanelMultiWindowUtils::class)
        every { COUIPanelMultiWindowUtils.getCurrentWindowVisibleRect(activity) } returns null
        Assert.assertEquals(0, getScreenWidth(activity))
        val visibleRect = mockk<Rect>()
        every { visibleRect.width() } returns 1
        every { COUIPanelMultiWindowUtils.getCurrentWindowVisibleRect(activity) } returns visibleRect
        Assert.assertEquals(1, getScreenWidth(activity))
    }
}