/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.labelmanager.recommend.aiunit
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.labelmanager.recommend.aiunit

import android.content.Context
import com.oplus.aiunit.core.AIUnit
import com.oplus.aiunit.core.callback.ConnectionCallback
import com.oplus.aiunit.nlp.detector.FileLabelDetector
import com.oplus.aiunit.nlp.slot.FileLabelInputSlot
import com.oplus.aiunit.nlp.slot.FileLabelOutputSlot
import io.mockk.CapturingSlot
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.SpyK
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkObject
import io.mockk.slot
import io.mockk.unmockkConstructor
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AiUnitUtilsTest {

    @MockK
    lateinit var context: Context

    @SpyK
    var aiUnitUtils: AiUnitUtils = AiUnitUtils()

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { contentResolver }.returns(mockk(relaxed = true))
            every { applicationContext }.returns(this)
        }
        mockkObject(AIUnit.Companion)
        mockkConstructor(FileLabelDetector::class)
    }

    @After
    fun after() {
        unmockkObject(AIUnit.Companion)
        unmockkConstructor(FileLabelDetector::class)
    }

    @Test
    fun `should not execute init when init if device is not supported`() {
        every { AIUnit.isDeviceSupported(context) } returns false
        justRun { AIUnit.init(context, any()) }
        every { aiUnitUtils.isNotUseAIUnit() } returns false
        aiUnitUtils.init(context)
        verify { AIUnit.isDeviceSupported(context) }
        verify(inverse = true) { AIUnit.init(context, any()) }
    }

    @Test
    fun `should execute init when init if device is supported`() {
        every { AIUnit.isDeviceSupported(context) } returns true
        justRun { AIUnit.init(context, any()) }
        every { aiUnitUtils.isNotUseAIUnit() } returns false
        aiUnitUtils.init(context)
        verify { AIUnit.init(context, any()) }
    }

    @Test
    fun `should init when init callback`() {
        val captureCallback = initConnected()
        every { aiUnitUtils.isNotUseAIUnit() } returns false
        verify { AIUnit.init(context, capture(captureCallback)) }
    }
    @Test
    fun `should return null when getAIUnitResult if connected is not`() {
        assertEquals(null, aiUnitUtils.getAIUnitResult(arrayListOf()))
    }

    @Test
    fun `should return null when getAIUnitResult if connected`() {
        initConnected()
        assertEquals(null, aiUnitUtils.getAIUnitResult(arrayListOf()))
    }

    @Test
    fun `should return null when getAIUnitResult if connected and detector is not supported`() {
        initConnected()
        assertEquals(null, aiUnitUtils.getAIUnitResult(arrayListOf()))
    }

    @Test
    fun `should return null when getAIUnitResult if connected and detector is supported ans start is 1`() {
        initConnected()
        every { anyConstructed<FileLabelDetector>().isSupported } returns true
        every { anyConstructed<FileLabelDetector>().start() } returns 1
        assertEquals(null, aiUnitUtils.getAIUnitResult(arrayListOf()))
    }

    @Test
    fun `should return 0 when getAIUnitResult if connected and detector is supported ans start is 0`() {
        initConnected()
        every { anyConstructed<FileLabelDetector>().isSupported } returns true
        every { anyConstructed<FileLabelDetector>().start() } returns 0
        val inputSlot = mockk<FileLabelInputSlot>()
        val outputSlot = mockk<FileLabelOutputSlot>()
        every { anyConstructed<FileLabelDetector>().createInputSlot() } returns inputSlot
        every { anyConstructed<FileLabelDetector>().createOutputSlot() } returns outputSlot
        every { anyConstructed<FileLabelDetector>().stop() } returns 1
        assertEquals(0, aiUnitUtils.getAIUnitResult(arrayListOf())?.size)
        verify { anyConstructed<FileLabelDetector>().start() }
        verify { anyConstructed<FileLabelDetector>().createInputSlot() }
        verify { anyConstructed<FileLabelDetector>().createOutputSlot() }
        verify { anyConstructed<FileLabelDetector>().stop() }
    }

    private fun initConnected(): CapturingSlot<ConnectionCallback> {
        every { AIUnit.isDeviceSupported(context) } returns true
        val captureCallback = slot<ConnectionCallback>()
        every { AIUnit.init(context, capture(captureCallback)) } answers {
            captureCallback.captured.onServiceConnect()
        }
        every { aiUnitUtils.isNotUseAIUnit() } returns false
        aiUnitUtils.init(context)
        return captureCallback
    }
}