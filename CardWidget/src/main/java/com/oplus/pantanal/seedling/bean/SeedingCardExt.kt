/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SeedlingCardExt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/8/4 14:31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2025/8/4       1.0      create
 ***********************************************************************/
package com.oplus.pantanal.seedling.bean

fun SeedlingCard.generateCardId(): String {
    val cardId = this.cardId
    val cardIndex = this.cardIndex
    return "$cardId&$cardIndex&$host"
}