/*********************************************************************
 * * Copyright (C), 2020, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/6/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.encrypt

import android.app.Activity
import android.util.Log
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.Constants
import com.filemanager.common.controller.FileEncryptController
import com.filemanager.common.utils.StatisticsUtils

class FileEncryptFactor {
    companion object {
        private const val TAG = "FileEncryptController"

        fun create(activity: BaseVMActivity): FileEncryptController {
            return if (EncryptUtils.sUseOplusEcryptManager) {
                Log.d(TAG, "create() EncryptControllerS")
                EncryptControllerS(activity)
            } else {
                Log.d(TAG, "create() EncryptControllerR")
                EncryptControllerR(activity)
            }
        }

        fun startFileSafeActivity(activity: Activity) {
            if (EncryptUtils.checkPrivateSafeEnabled(activity).not()) return
            StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_PRIVATE_SAFE, Constants.PAGE_MAIN)
            if (EncryptUtils.sUseOplusEcryptManager) {
                Log.d(TAG, "startFileSafeActivity S")
                EncryptFactorUtils.jumpToEncryptS(activity)
            } else {
                Log.d(TAG, "startFileSafeActivity R")
                EncryptFactorUtils.jumpToEncryptR(activity)
            }
        }
    }
}