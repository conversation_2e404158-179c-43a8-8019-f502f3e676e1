/*********************************************************************
 * * Copyright (C) 2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : Photo album slimming.
 * * Version     : 1.0
 * * Date        : 2017/10/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * *  <EMAIL>  2017/10/20  1.0  Photo album slimming.
 ***********************************************************************/
package com.oplus.encryption;

import android.os.Parcel;
import android.os.Parcelable;

public class ImageInfo implements Parcelable {
    private int mMatched;
    private int mDeleted;
    private long mFileLength;
    private String mPath;
    private String mMd5;
    private String mGid;
    private String mSource;

    public ImageInfo() {
    }

    public ImageInfo(Parcel source) {
        readFromParcel(source);
    }

    public static final Parcelable.Creator<ImageInfo> CREATOR = new Parcelable.Creator<ImageInfo>() {
        public ImageInfo createFromParcel(Parcel source) {
            return new ImageInfo(source);
        }

        public ImageInfo[] newArray(int size) {
            return new ImageInfo[size];
        }
    };

    public void setMatched(int matched) {
        mMatched = matched;
    }

    public void setDeleted(int deleted) {
        mDeleted = deleted;
    }

    public void setPath(String path) {
        mPath = path;
    }

    public void setMd5(String md5) {
        mMd5 = md5;
    }

    public void setGid(String gid) {
        mGid = gid;
    }

    public void setFileLength(long lenth) {
        mFileLength = lenth;
    }

    public void setSource(String source) {
        mSource = source;
    }

    public int getMatched() {
        return mMatched;
    }

    public boolean isMatched() {
        return mMatched == 1;
    }

    public int getDeleted() {
        return mDeleted;
    }

    public boolean isDeleted() {
        return mDeleted == 1;
    }

    public String getPath() {
        return mPath;
    }

    public String getMd5() {
        return mMd5;
    }

    public String getGid() {
        return mGid;
    }

    public long getFileLength() {
        return mFileLength;
    }

    public String getSource() {
        return mSource;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(mMatched);
        dest.writeInt(mDeleted);
        dest.writeString(mPath);
        dest.writeString(mMd5);
        dest.writeString(mGid);
        dest.writeLong(mFileLength);
        dest.writeString(mSource);
    }

    public void readFromParcel(Parcel source) {
        mMatched = source.readInt();
        mDeleted = source.readInt();
        mPath = source.readString();
        mMd5 = source.readString();
        mGid = source.readString();
        mFileLength = source.readLong();
        mSource = source.readString();
    }

    @Override
    public String toString() {
        return "match=" + mMatched + ", delete=" + mDeleted + ", path=" + mPath + ", md5=" + mMd5
                + ", mSource=" + mSource + ", gid=" + mGid + ", file length=" + mFileLength;
    }
}
