import groovy.json.JsonSlurper

import java.util.regex.Pattern

final class SignAppNameMatchUtils {
    static boolean isEqualsOrMatched(String pattern, String target) {
        if (isWildcardPattern(pattern)) {
            return matchedAsWildcard(pattern, target)
        }
        return pattern == target
    }

    private static boolean isWildcardPattern(String pattern) {
        return pattern.contains('*') || pattern.contains('?')
    }

    private static String wildcardToRegexString(String pattern) {
        final replaced = pattern.replace("\\", "\\\\")
                .replace("-", "\\-")
                .replace("\$", "\\\$")
                .replace(".", "\\.")
                .replace("*", ".*")
                .replace("?", ".?")
        return "^$replaced\$"
    }

    private static boolean matchedAsWildcard(String pattern, String target) {
        return Pattern.compile(wildcardToRegexString(pattern)).matcher(target).matches()
    }
}

final SIGN_CONFIG_FILE = "signInfo.json"

def readSignConfigs(String configFileName) {
    final configFile = project.rootProject.file(configFileName)
    if (!configFile.exists() || !configFile.isFile()) {
        return null
    }
    final jsonParser = new JsonSlurper()
    def signConfigs = null
    def fileReader = null
    try {
        fileReader = new FileReader(configFile)
        final data = jsonParser.parse(fileReader)
        if (data instanceof ArrayList) {
            // Only pick first since osigner plugin can only support one set of sign configs.
            signConfigs = data
        } else {
            System.err.println("LOCAL_SIGN: ERROR! No sign configs list in $configFileName")
        }
    } catch (Throwable e) {
        System.err.println("LOCAL_SIGN: ERROR! Read $configFileName error, $e")
    } finally {
        if (fileReader != null) {
            try {
                fileReader.close()
            } catch (IOException e) {
                System.err.println("LOCAL_SIGN: ERROR! close $configFileName error, $e")
            }
        }
    }
    return signConfigs
}

String getExpectedOutputName(String taskParameter) {
    if (!taskParameter.endsWith("Sign")) {
        return null
    }
    def format = null
    if (taskParameter.startsWith("assemble") || taskParameter.startsWith("install")) {
        format = "apk"
    } else if (taskParameter.startsWith("bundle")) {
        format = "aab"
    }
    if (format == null) {
        return null
    }
    def variantPart = taskParameter.replace("AndroidTest", "").replace("assemble", "")
            .replace("install", "").replace("bundle", "").replace("Sign", "")
            .replace("Oppo", "OPPO").replace("Realme", "realme").replace("Oneplus", "OnePlus")
            .replace("Domestic", "domestic").replace("Export", "export").replace("Gdpr", "GDPR")
            .replace("Pall", "-all-").replace("Apilevelall", "-all-")
            .replace("Debug", "all-debug").replace("Release", "all-release")
            .replace("Oapm", "all-oapm").replace("Coverage", "all-coverage")
    if (format == "aab") {
        variantPart = variantPart.toLowerCase(Locale.US)
    }
    final prjName = project.rootProject.name
    return "$prjName-$variantPart.$format"
}

def pickSignConfigForVariant(List<Map<String, Object>> signConfigs) {
    if (signConfigs == null || signConfigs.isEmpty()) {
        return null
    }
    for (taskRequest in gradle.startParameter.taskRequests) {
        for (taskParameter in taskRequest.args) {
            final outputName = getExpectedOutputName(taskParameter.toString())
            if (outputName == null) {
                continue
            }
            for (signConfig in signConfigs) {
                if (SignAppNameMatchUtils.isEqualsOrMatched(signConfig.appname, outputName)) {
                    return signConfig
                }
            }
        }
    }
    return null
}

def obtainSignInfoConfig(String configFileName) {
    final signConfigs = readSignConfigs(configFileName)
    def signConfig = pickSignConfigForVariant(signConfigs)
    if ((signConfig == null) || signConfig.isEmpty()) {
        signConfig = new HashMap<String, Object>()
        // Obtain default sign configs for local sign plugin
        signConfig.baseline = 'Oplus_key'
        signConfig.projectNumber = 'Oplus_prj'
        signConfig.brand = 'PSW'
        signConfig.signModule = 'v3_single'
        signConfig.signType = 'oppo_data_app_std'
    }
    return signConfig
}

final signConfig = obtainSignInfoConfig(SIGN_CONFIG_FILE)
println "LOCAL_SIGN: add local sign tasks, config=$signConfig"

// Plugin osinger is in com.inno.buildplugin:build-plugin.
// See details:
//   http://alm.adc.com/OPPO/AppCenter/_git/BuildPlugin?path=%2FREADME.md&version=GBmaster
//   https://odocs.myoas.com/docs/jqrP6cYtT9h3wjTP
apply plugin: 'osigner'
osign {
    brand = signConfig.brand
    project = signConfig.projectNumber
    platform = signConfig.baseline
    signType = signConfig.signType
    signVersion = signConfig.signModule
}
