/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : BaseSpaceOpenEntity.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * * 飞书中intent中带入的数据结构，在查看云文档（我的空间，共享空间，收藏，离线等）中的本地上传的文件，intent中的extras中会带入这个类的子类
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.bytedance.ee.bear.contract.drive.sdk.entity.open;

import android.os.Parcel;

public class BaseSpaceOpenEntity extends BaseOpenEntity {

    public static final Creator<BaseSpaceOpenEntity> CREATOR = new Creator<BaseSpaceOpenEntity>() {
        @Override
        public BaseSpaceOpenEntity createFromParcel(Parcel in) {
            return new BaseSpaceOpenEntity(in);
        }

        @Override
        public BaseSpaceOpenEntity[] newArray(int size) {
            return new BaseSpaceOpenEntity[size];
        }
    };

    private String mBusinessId = "";
    private String mDataVersion = "";
    private String mMountNodeToken = "";
    private String mMountPoint = "";
    private String mVersion = "";

    protected BaseSpaceOpenEntity(Parcel parcel) {
        super(parcel);
        this.mMountNodeToken = parcel.readString();
        this.mMountPoint = parcel.readString();
        this.mBusinessId = parcel.readString();
        this.mVersion = parcel.readString();
        this.mDataVersion = parcel.readString();
    }
}
