package com.coloros.filemanager.utils

import android.content.Context
import android.graphics.drawable.Drawable
import android.widget.TextView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.theme.COUIThemeOverlay
import com.filemanager.common.R
import com.filemanager.common.utils.ThemeColorUtils
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class ThemeColorUtilsTest {
    @MockK
    lateinit var mContext: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        mContext = mockk(relaxed = true)
    }

    @Test
    fun `should verify when get theme color without`() {
        mockkStatic(COUIContextUtil::class)
        every { COUIContextUtil.getAttrColor(mContext, com.support.appcompat.R.attr.couiColorContainerTheme, 0) }.returns(110)

        ThemeColorUtils.getThemeColor(mContext)
    }

    @Test
    fun `should verify when tint drawable with theme without`() {

        Assert.assertFalse(ThemeColorUtils.tintDrawableWithTheme(mContext, null))


        mockkStatic(COUIThemeOverlay::class)
        every { COUIThemeOverlay.getInstance() }.returns(mockk(relaxed = true) {
            every { isCOUITheme(mContext) }.returns(true)
        })

        Assert.assertTrue(ThemeColorUtils.tintDrawableWithTheme(mContext, mockk(relaxed = true)))
    }

    @Test
    fun `should verify when tint drawable without`() {
        Assert.assertNull(ThemeColorUtils.tintDrawable(mContext, null))

        mockkStatic(COUIContextUtil::class)
        every { COUIContextUtil.getAttrColor(any(), any(), 0) }.returns(1111)

        var drawable = mockk<Drawable>(relaxed = true)
        ThemeColorUtils.tintDrawable(mContext, drawable)
    }
/*
    @Test
    fun `should verify when tint text view without`() {

        ThemeColorUtils.tintTextView(null)

        var view = mockk<TextView>(relaxed = true) {
            every { context }.returns(mockk(relaxed = true))
        }

        mockkObject(ThemeColorUtils)
        every { ThemeColorUtils.getThemeColor(any(), any()) }.returns(11)
        ThemeColorUtils.tintTextView(view)
        verify { view.setTextColor(11) }

        mockkStatic(COUIThemeOverlay::class)
        every { COUIThemeOverlay.getInstance() }.returns(mockk(relaxed = true) {
            every { isCOUITheme(mContext) }.returns(false)
        })

        ThemeColorUtils.tintTextView(view, 10)
        verify { view.setTextColor(11) }
    }*/


    @After
    fun afterTests() {
        unmockkAll()
    }

}