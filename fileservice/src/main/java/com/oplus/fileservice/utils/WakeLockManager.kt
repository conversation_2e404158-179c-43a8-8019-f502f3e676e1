/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WakeLockManager
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/1/11 14:27
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/1/11       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.utils

import android.content.Context
import android.os.PowerManager
import com.filemanager.common.utils.Log

class WakeLockManager {

    fun acquireCpuWakeLock(context: Context) {
        Log.d(TAG, "acquireCpuWakeLock")
        if (sCpuWakeLock == null) {
            sCpuWakeLock = createPartialWakeLock(context)
        }
        sCpuWakeLock?.acquire(ACQUIRE_TIMEOUT)
    }

    private fun releaseCpuWakeLock() {
        Log.d(TAG, "releaseCpuWakeLock")
        sCpuWakeLock?.release()
        sCpuWakeLock = null
    }

    fun stopCpuWakeLock() {
        Log.d(TAG, "stopCpuWakeLock")
        releaseCpuWakeLock()
    }

    private fun createPartialWakeLock(context: Context): PowerManager.WakeLock? {
        Log.d(TAG, "createPartialWakeLock")
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
        return powerManager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, TAG_WAKE_LOCK)
    }

    companion object {
        private const val TAG = "WakeLockManager"

        private const val ACQUIRE_TIMEOUT = 5 * 60 * 1000L // 5min

        private const val TAG_WAKE_LOCK = "FileManager:WackLockTag"

        private var sCpuWakeLock: PowerManager.WakeLock? = null

        val instance: WakeLockManager by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            WakeLockManager()
        }
    }
}