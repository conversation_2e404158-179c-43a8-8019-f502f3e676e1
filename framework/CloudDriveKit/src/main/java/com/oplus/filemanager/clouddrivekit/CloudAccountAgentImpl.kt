/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudAccountAgentImpl
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/3/11
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798        2024/1/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.clouddrivekit

import android.content.Context
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.heytap.cloudkit.libcommon.account.CloudAccount
import com.heytap.cloudkit.libcommon.account.ICloudAccountAgent
import com.heytap.cloudkit.libcommon.account.ICloudAccountCallback
import com.oplus.filemanager.interfaze.heytapaccount.IHeytapAccount


class CloudAccountAgentImpl(private val mContext: Context) : ICloudAccountAgent {
    companion object {
        private const val TAG = "CloudAccountAgentImpl"
    }

    override fun getSignInAccount(callback: ICloudAccountCallback) {
        val heytapAccountAction = Injector.injectFactory<IHeytapAccount>()
        val account = CloudAccount().apply {
            token = heytapAccountAction?.getUserToken(mContext)
            userId = heytapAccountAction?.getUserId(mContext)
            username = heytapAccountAction?.getUserName(mContext)
            isLogin = heytapAccountAction?.isLogin(mContext) ?: false
        }
        Log.d(TAG, "getSignInAccount $account")
        callback.onComplete(account)
    }

    override fun reqSignInAccount(callback: ICloudAccountCallback) {
        val heytapAccountAction = Injector.injectFactory<IHeytapAccount>()
        val account = CloudAccount()
        heytapAccountAction?.reqSignInAccount(mContext) { userInfo ->
            account.token = userInfo.token
            account.isLogin = userInfo.isLogin
            account.resultCode = userInfo.resultCode
            account.resultMsg = userInfo.resultMsg
            account.userId = userInfo.userId
            account.username = userInfo.username
            account.avatar = userInfo.avatar
            account.status = userInfo.status
            Log.d(TAG, "reqSignInAccount $account")
            callback.onComplete(account)
        }
    }

    override fun refreshTokenWhenTokenExpire(callback: ICloudAccountCallback) {}
}