/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.superapp
 * * Version     : 1.0
 * * Date        : 2020/4/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.categorycompress.ui

import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.MyApplication
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.base.loader.UriLoadResult
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.interfaces.LoadingLoaderListener
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.LIST_NORMAL_MODE
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class CategoryCompressViewModel : SelectionViewModel<MediaFileWrapper, BaseUiModel<MediaFileWrapper>>() {
    companion object {
        private const val TAG = "CategoryCompressViewModel"

        const val TYPE_ALL = "all"
        const val TYPE_RAR = ".rar"
        const val TYPE_ZIP = ".zip"
        const val TYPE_JAR = ".jar"
        const val TYPE_7ZIP = ".7z"

        @kotlin.jvm.JvmField
        val COMPRESS_EXT_ARRAY = java.util.ArrayList<String?>().apply {
            add(".rar")
            add(".zip")
            add(".jar")
            add(".7z")
        }
    }

    val mModeState = BaseStateModel(MutableLiveData<Int>(LIST_NORMAL_MODE))
    private var mCompressType = TYPE_ALL
    var mScanModeValue: Int = KtConstants.SCAN_MODE_LIST

    private var mNeedScroll = false
    val mPositionModel = MutableLiveData<Int>()
    private val mLoaderCallBack = LoaderCallBack(this)

    override fun loadData() {
        mLoaderCallBack.getLoader()?.forceLoad()
    }

    fun initLoader(mLoaderController: LoaderController?, compressType: String) {
        mCompressType = compressType
        if (mLoaderCallBack.getLoader() == null) {
            mLoaderController?.initLoader(compressType.hashCode(), mLoaderCallBack)
        } else {
            loadData()
        }
    }

    class LoaderCallBack : LoadingLoaderListener<CategoryCompressViewModel, CategoryCompressLoader,
            UriLoadResult<Int, MediaFileWrapper>> {

        constructor(viewModel: CategoryCompressViewModel): super(viewModel, viewModel.mDataLoadState)

        override fun onCreateLoader(viewModel: CategoryCompressViewModel?): CategoryCompressLoader? {
            return if (viewModel != null) {
                CategoryCompressLoader(MyApplication.sAppContext, viewModel.mCompressType)
            } else null
        }

        override fun onLoadComplete(viewModel: CategoryCompressViewModel?, data: UriLoadResult<Int, MediaFileWrapper>?) {
            Log.d(TAG, "onLoadFinished size=${data?.mResultList?.size}")
            val resultList = data?.mResultList?.filter {
                it.mData?.let { path ->
                    HiddenFileHelper.isDisplayFile(path)
                } ?: true
            }
            resultList?.let {
                if (viewModel != null) {
                    viewModel.mModeState.mInitState = true
                    viewModel.launch {
                        val selectedList = ArrayList<Int>()
                        if ((viewModel.mUiState.value?.mSelectedList?.size ?: 0) > 0) {
                            withContext(Dispatchers.IO) {
                                for (selectedFile in viewModel.mUiState.value!!.mSelectedList) {
                                    if (data.mResultMap.containsKey(selectedFile)) {
                                        selectedList.add(selectedFile)
                                    }
                                }
                            }
                        }
                        if (it.isEmpty() && (viewModel.mModeState.mListModel.value == KtConstants.LIST_SELECTED_MODE)) {
                            Log.d(TAG, "onLoadComplete mResultList is empty change to normal mode")
                            viewModel.mModeState.mListModel.value = KtConstants.LIST_NORMAL_MODE
                        }
                        viewModel.mUiState.postValue(BaseUiModel(resultList, viewModel.mModeState, selectedList,
                                data.mResultMap))
                        if (viewModel.mNeedScroll) {
                            viewModel.mPositionModel.value = 0
                            viewModel.mNeedScroll = false
                        }
                    }
                } else {
                    Log.d(TAG, "onLoadComplete viewModel is null")
                }
            }
        }

    }

    fun resetState() {
        changeListMode(LIST_NORMAL_MODE)
    }

    fun clickToolbarSelectAll() {
        if (mUiState.value?.mFileList?.size == mUiState.value?.mSelectedList?.size) {
            mUiState.value?.mSelectedList?.clear()
        } else {
            mUiState.value?.mSelectedList?.clear()
            mUiState.value?.mFileList?.let {
                for (baseFileBean in it) {
                    baseFileBean.mId?.let { it1 -> mUiState.value?.mSelectedList?.add(it1) }
                }
            }
        }
        mUiState.value = mUiState.value
    }

    fun pressBack(): Boolean {
        mModeState?.let {
            return if (it.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
                changeListMode(LIST_NORMAL_MODE)
                true
            } else {
                false
            }
        }
        return false
    }

    override fun getRealFileSize(): Int {
        return mUiState.value?.mFileList?.size ?: 0
    }

    override fun getRecyclerViewScanMode(): com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE {
        return if (mScanModeValue == KtConstants.SCAN_MODE_LIST) {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.LIST
        } else {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.GRID
        }
    }

    fun setSort(type: Int, desc: Int) {
        mLoaderCallBack.getLoader()?.setSort(type, desc)
    }
}