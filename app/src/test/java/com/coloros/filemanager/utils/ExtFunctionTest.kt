/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ExtFunctionTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/16
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/16       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.utils

import android.app.Activity
import android.content.Context
import com.filemanager.common.utils.isActivityAndInvalid
import com.filemanager.common.utils.isInvalid
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Test

class ExtFunctionTest {

    @Test
    fun testIsActivityAndInvalid() {
        val context = mockk<Context>(relaxed = true)
        Assert.assertFalse(context.isActivityAndInvalid())

        val activity = mockk<Activity>(relaxed = true)
        // activity is finish and destroy
        every { activity.isFinishing } returns true
        every { activity.isDestroyed } returns true
        Assert.assertTrue(activity.isActivityAndInvalid())

        // activity is not finish but destroy
        every { activity.isFinishing } returns false
        every { activity.isDestroyed } returns true
        Assert.assertTrue(activity.isActivityAndInvalid())

        // activity is finish but not destroy
        every { activity.isFinishing } returns true
        every { activity.isDestroyed } returns false
        Assert.assertTrue(activity.isActivityAndInvalid())

        // activity is not finish and not destroy
        every { activity.isFinishing } returns false
        every { activity.isDestroyed } returns false
        Assert.assertFalse(activity.isActivityAndInvalid())
    }

    @Test
    fun testActivityIsInvalid() {
        val context = mockk<Activity>(relaxed = true)
        // activity is finish and destroy
        every { context.isFinishing } returns true
        every { context.isDestroyed } returns true
        Assert.assertTrue(context.isInvalid())

        // activity is not finish but destroy
        every { context.isFinishing } returns false
        every { context.isDestroyed } returns true
        Assert.assertTrue(context.isInvalid())

        // activity is finish but not destroy
        every { context.isFinishing } returns true
        every { context.isDestroyed } returns false
        Assert.assertTrue(context.isInvalid())

        // activity is not finish and not destroy
        every { context.isFinishing } returns false
        every { context.isDestroyed } returns false
        Assert.assertFalse(context.isInvalid())
    }
}