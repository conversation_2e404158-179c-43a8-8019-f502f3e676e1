/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : AdBrandKtTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/3/16 20:43
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2023/3/16       1.0      create
 **********************************************************************/
package com.oplus.filemanager.compat

import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.Utils
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.Assert
import org.junit.Test

class AdBrandKtTest {

    @Test
    fun testInitAdBrand() {
        mockkStatic(ModelUtils::class)
        mockkStatic(Utils::class)

        every { ModelUtils.isTablet() } returns false
        every { Utils.isRealmePhone() } returns false
        Assert.assertEquals(AdBrand.OPPO, initAdBrand())

        every { ModelUtils.isTablet() } returns false
        every { Utils.isRealmePhone() } returns true
        Assert.assertEquals(AdBrand.REALME, initAdBrand())

        every { ModelUtils.isTablet() } returns true
        Assert.assertEquals(AdBrand.NULL, initAdBrand())

        unmockkStatic(ModelUtils::class)
        unmockkStatic(Utils::class)
    }
}