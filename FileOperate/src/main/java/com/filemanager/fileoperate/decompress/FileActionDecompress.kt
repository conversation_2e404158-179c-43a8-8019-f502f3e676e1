/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileActionDecompress.kt
 ** Description: File decompress function
 ** Version: 1.0
 ** Date: 2020/3/9
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.decompress

import android.content.DialogInterface
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.MediaScannerCompat.sendMediaScanner
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.fileutils.checkDestStorageSpace
import com.filemanager.common.fileutils.fetchFileName
import com.filemanager.common.fileutils.getCompressFileType
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.FileOperateUtil
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.base.BaseFileNameDialog
import com.filemanager.fileoperate.base.DISMISS_PROGRESS
import com.filemanager.fileoperate.base.SHOW_PROGRESS
import com.filemanager.fileoperate.base.UPDATE_PROGRESS
import com.filemanager.fileoperate.compress.CompressHelperFactory
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.fileservice.IFileService.Companion.OPERATE_TYPE_DECOMPRESS
import java.io.File
import java.net.URLDecoder

open class FileActionDecompress(
    lifecycle: LifecycleOwner,
    sourceFile: BaseFileBean,
    destParentFile: BaseFileBean,
    needShowCheckButton: Boolean,
    selectFiles: List<BaseDecompressFile>? = null,
    noticeInDialog: String? = null,
    decompressFileName: String? = null,
) : BaseFileAction<FileDecompressObserver>(lifecycle) {
    companion object {
        private const val TAG = "FileActionDecompress"
        private const val DELAY_OPEN_FILE = 200L

        private const val STEP_DEFAULT = 0x00
        // 显示输入秘密的弹窗
        private const val STEP_SHOW_PASSWORD_DIALOG = 0x01
        // 验证密码
        private const val STEP_VERIFY_PASSWORD = 0x02
    }

    private var mLockObj = Object()
    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    var mSourceFile: BaseFileBean = sourceFile
    protected var mSelectDecompressFiles: MutableList<out BaseDecompressFile>? = null
    protected var mDestParentFile: BaseFileBean = destParentFile

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    var mDecompressHelper: DecompressHelper<out BaseDecompressFile>? = null
    private var mInputFileName: String? = null
    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    var passwordQueue = VerifyPasswordQueue()
    protected var mInDecompressing: Boolean? = null

    //Not in file browser path, should show a clickable toast after decompress success
    private var mNeedShowCheckButton = needShowCheckButton

    // 解压文件的名称，可为空，为空则走默认逻辑，使用源文件名称
    private var mDecompressFileName: String? = decompressFileName

    @VisibleForTesting
    var verifyPwdStep = STEP_DEFAULT
    // 是否正在后台验证密码
    @VisibleForTesting
    @Volatile
    var isVerifyingPwd = false

    init {
        selectFiles?.let {
            mSelectDecompressFiles = ArrayList(it)
        }
    }

    override fun onCancelled() {
        notifyLockReleased()
        if (mInDecompressing == true) {
            mDecompressHelper?.cancelDecompress()
        }
        super.onCancelled()
    }

    @VisibleForTesting
    fun notifyLockReleased() {
        runCatching {
            synchronized(mLockObj) {
                mLockObj.notify()
            }
        }.onFailure {
            Log.e(TAG, "notifyLockReleased ${it.message}")
        }
    }

    private fun waitLockRelease(): Boolean {
        try {
            synchronized(mLockObj) {
                mLockObj.wait()
            }
        } catch (e: Exception) {
            return false
        }
        return true
    }

    override fun run(): Boolean {
        if (mSourceFile.mData.isNullOrEmpty() || mDestParentFile.mData.isNullOrEmpty() || !JavaFileHelper.exists(mSourceFile)) {
            Log.d(TAG, "Select file size: ${mSelectDecompressFiles?.size}, sourceFile: ${mSourceFile.mData}," +
                    " destFile: ${mDestParentFile.mData}")
            notifyObserver(NOTICE_SOURCE_FILE_NOT_EXIST)
            return false
        }
        notifyObserver(
            SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                mContext.getString(com.filemanager.common.R.string.dialog_open_compress_file), true
        ), delayInMill = DELAY_OPEN_FILE)
        // Check destination space first
        if (validateDestSpaceNotEnough()) {
            return false
        }

        val compressType = getCompressFileType(mSourceFile)
        mDecompressHelper = CompressHelperFactory.getDecompressHelper(compressType)
        if (mDecompressHelper == null) {
            cancelShowCalculateProgress()
            notifyObserver(NOTICE_DECOMPRESS_UNSUPPORT)
            Log.d(TAG, "getDecompressHelper is null, compressType=$compressType")
            return false
        }

        if (mDecompressHelper!!.checkIsSupport(mSourceFile).not()) {
            Log.d(TAG, " checkIsSupport false")
            mDecompressHelper = CompressHelperFactory.getDecompressHelper(CompressHelperFactory.P7ZIP)
        }
        if (KtUtils.checkIsDfmPath(mSourceFile.mData) && mDecompressHelper is P7ZipDecompressHelper) {
            Log.d(TAG, "decompress dfs 7z copyToTempFileByDfm")
            val result = FileOperateUtil.copyToTempFileByDfm(mSourceFile, BaseFileBean())
            if (result.first && (result.second is BaseFileBean)) {
                mSourceFile = result.second as BaseFileBean
                Log.d(TAG, "dfs compress copy success $mSourceFile")
            } else {
                cancelShowCalculateProgress()
                notifyObserver(NOTICE_DECOMPRESS_SPACE_NOT_ENOUGH)
                Log.d(TAG, "checkDestStorageSpace false")
                return false
            }
        }
        // 1.判断是否是加密压缩，不是则直接解压
        var flag = true
        if (checkIsEncryptedFile()) {
            // 2.是加密压缩，弹出输入密码弹窗，开始验证
            doVerifyPwdStep(STEP_SHOW_PASSWORD_DIALOG)

            while (!isCancelled()) {
                if (!waitLockRelease()) {
                    Log.e(TAG, "VerifyPwd wait error")
                    flag = false
                }
                break
            }
            Log.d(TAG, "VerifyPwd finish $flag")
        }

        Log.d(TAG, "decompress File $flag")
        // 4.验证通过 ，直接解压
        if (flag && isCancelled().not()) {
            return reallyDecompressFile()
        }
        cancelShowCalculateProgress()
        return false
    }

    @VisibleForTesting
    fun cancelShowCalculateProgress() {
        cancelNotifyObserver(SHOW_PROGRESS)
        notifyObserver(DISMISS_PROGRESS)
    }

    /**
     * 如果指定了解压文件的名称，
     * 则使用指定的名称，
     * 否则使用源文件的名称
     */
    private fun getDecompressFileName(): String {
        return if (mDecompressFileName != null) {
            mDecompressFileName!!
        } else {
            URLDecoder.decode(KtUtils.getFileNameWithOutExtension(mSourceFile.mDisplayName!!), "UTF-8")
        }
    }

    protected open fun reallyDecompressFile(): Boolean {
        val fileName = getDecompressFileName()
        val newFile = fetchFileName(mDestParentFile, fileName, "")
        mInputFileName = newFile?.mDisplayName
        while (!isCancelled()) {
            /*if (!waitLockRelease()) {
                Log.d(TAG, "reallyDecompressFile: Action interrupted in reallyDecompressFile")
                return false
            }*/
            if (isCancelled()) {
                return false
            }

            val destFile = PathFileWrapper(mDestParentFile.mData.plus(File.separator).plus(mInputFileName))
            // Notice that decompress work may be in multi-thread
            val callback = object : DecompressHelper.OnDecompressListener {

                override fun onInPreparation() {
                    if (mInDecompressing == null || mInDecompressing == false) {
                        mInDecompressing = true
                        notifyObserver(
                                SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                                mContext.getString(com.filemanager.common.R.string.decompressing), false))
                    }
                }

                override fun onDecompressing(percent: Int) {
                    notifyObserver(UPDATE_PROGRESS, percent)
                }

                override fun onFinished(result: Boolean) {
                    Log.d(TAG, "reallyDecompressFile: decompress finished: $result")
                    cancelNotifyObserver(SHOW_PROGRESS)
                    if (result) {
                        // If decompress in the same dir, just need to show a normal toast
                        val sameDir = if (mNeedShowCheckButton) false else {
                            mSourceFile.mData?.let {
                                File(it).parentFile?.equals(mDestParentFile.mData?.let { it1 ->
                                    File(it1)
                                })
                            } ?: false
                        }
                        val sourcePath =
                            mSourceFile.mData?.let { File(it).parent?.plus(File.separator).plus(mInputFileName) }
                        val destPath = mDestParentFile.mData.plus(File.separator).plus(mInputFileName)
                        notifyObserver(ACTION_DONE, if (sameDir) sourcePath else destPath)
                        val fileServiceAction = Injector.injectFactory<IFileService>()
                        fileServiceAction?.syncOperate(OPERATE_TYPE_DECOMPRESS, hashSetOf(mDestParentFile.mData))
                        OptimizeStatisticsUtil.deCompressFile(mSourceFile.mData ?: "")
                    } else {
                        notifyObserver(ACTION_FAILED)
                    }
                    mInDecompressing = false
                }

                override fun onCancelled() {
                    Log.d(TAG, "reallyDecompressFile: decompress cancelled")
                    cancelNotifyObserver(SHOW_PROGRESS)
                    mInDecompressing = false
                }

                override fun onTryAgain() {
                    Log.d(TAG, "reallyDecompressFile: decompress onTryAgain")
                    mDecompressHelper = CompressHelperFactory.getDecompressHelper(CompressHelperFactory.P7ZIP)
                    (mDecompressHelper as? P7ZipDecompressHelper)?.decompress(
                        mSourceFile, destFile, passwordQueue.getVerifyPassword(),
                        mSelectDecompressFiles as? MutableList<P7ZipDecompressFile>, this)
                }
            }
            mDecompressHelper!!.let {
                val password = passwordQueue.getVerifyPassword()
                when (it) {
                    is ZipDecompressHelper -> {
                        it.decompress(mSourceFile, destFile, password,
                            mSelectDecompressFiles as MutableList<ZipDecompressFile>?, callback)
                    }
                    is JarDecompressHelper -> {
                        it.decompress(mSourceFile, destFile, password,
                            mSelectDecompressFiles as MutableList<JarDecompressFile>?, callback)
                    }
                    is RarDecompressHelper -> {
                        it.decompress(mSourceFile, destFile, password,
                            mSelectDecompressFiles as MutableList<RarDecompressFile>?, callback)
                    }
                    is P7ZipDecompressHelper -> {
                        it.decompress(mSourceFile, destFile, password,
                            mSelectDecompressFiles as? MutableList<P7ZipDecompressFile>, callback)
                    }
                    else -> return true
                }
                while (!isCancelled() && (mInDecompressing != false)) {
                    // Wait decompress result
                    try {
                        Thread.sleep(DELAY_OPEN_FILE)
                    } catch (e: Exception) {
                        Log.e(TAG, "reallyDecompressFile: Wait decompress result failed: ${e.message}")
                        break
                    }
                }
                if (Utils.isOperateDatabase(appContext, destFile.mData)) {
                    sendMediaScanner(destFile.mData, Utils.MEDIA_SCAN_DECOMPRESS)
                }
                Log.d(TAG, "reallyDecompressFile: decompress done, return")
            }
            break
        }
        return true
    }

    @VisibleForTesting
    fun doVerifyPwdStep(step: Int) {
        Log.e(TAG, "doVerifyPwdStep ${Thread.currentThread().name} step:$step")
        verifyPwdStep = step
        when (step) {
            STEP_SHOW_PASSWORD_DIALOG -> {
                // 弹输入密码框,输入密码
                showPasswordDialog()
            }
            STEP_VERIFY_PASSWORD -> {
                // 验证密码loading弹窗
                showVerifyPwdLoading {
                    // 回到输入密码弹窗
                    Log.e(TAG, "doVerifyPwdStep cancel verify password, reshow password dialog")
                    verifyPwdStep = STEP_SHOW_PASSWORD_DIALOG
                    notifyObserver(RESHOW_DECOMPRESS_PASSWORD_DIALOG, passwordQueue.getLastedPassword())
                    passwordQueue.cancel()
                }
                if (isVerifyingPwd) {
                    Log.e(TAG, "doVerifyPwdStep Current is verifying pwd, only show loading dialog")
                    return
                }
                isVerifyingPwd = true
                runOnAsyncThread {
                    // 验证密码
                    var verifyResult = false
                    while (passwordQueue.hasNext()) {
                        val password = passwordQueue.next()
                        verifyResult = verifyPassword(password) // 耗时
                        Log.d(TAG, "doVerifyPwdStep verify $password result:$verifyResult 剩余:${passwordQueue.size()}")
                    }
                    isVerifyingPwd = false
                    if (verifyResult) {
                        Log.d(TAG, "doVerifyPwdStep verify success, step should is $STEP_VERIFY_PASSWORD current:$verifyPwdStep")
                        dismissVerifyPwdLoading(false)
                        if (verifyPwdStep == STEP_VERIFY_PASSWORD) {
                            notifyLockReleased()
                        }
                    } else {
                        Log.e(TAG, "doVerifyPwdStep verify password failed")
                        dismissVerifyPwdLoading(true)
                        notifyObserver(SHOW_DECOMPRESS_PASSWORD_ERROR)
                    }
                }
            }
        }
    }

    /**
     * 判断是否是加密的压缩文件
     */
    @VisibleForTesting
    fun checkIsEncryptedFile(): Boolean {
        return mDecompressHelper!!.isEncrypted(mSourceFile) && TextUtils.isEmpty(P7ZipDecompressHelper.getPassword(mSourceFile.mData!!))
    }

    @VisibleForTesting
    fun showPasswordDialog() {
        cancelShowCalculateProgress()
        val passwordDialogBean = FileDecompressObserver.PasswordDialogBean(null)
        passwordDialogBean.resultListener = object : BaseFileNameDialog.OnButtonClickListener {
            override fun onClick(dialog: AlertDialog, buttonId: Int, inputValue: String?) {
                when (buttonId) {
                    DialogInterface.BUTTON_POSITIVE -> {
                        passwordQueue.addPassword(inputValue)
                        // 3. 验证密码，显示验证密码loading弹窗
                        doVerifyPwdStep(STEP_VERIFY_PASSWORD)
                    }
                    else -> {
                        passwordQueue.cancelAll()
                        passwordDialogBean.recycle()
                        cancel()
                    }
                }
            }
        }
        notifyObserver(SHOW_DECOMPRESS_PASSWORD_DIALOG, passwordDialogBean)
    }

    /**
     * 验证密码
     */
    @VisibleForTesting
    fun verifyPassword(password: String?): Boolean {
        return mDecompressHelper!!.verifyPassword(mSourceFile, password)
    }

    @VisibleForTesting
    fun showVerifyPwdLoading(cancelAction: DialogInterface.OnCancelListener) {
        Log.d(TAG, "showVerifyPwdLoading show loading dialog")
        notifyObserver(DISMISS_DECOMPRESS_PASSWORD_DIALOG)
        notifyObserver(SHOW_VERIFY_PWD_LOADING_DIALOG, cancelAction, DELAY_OPEN_FILE)
    }

    @VisibleForTesting
    fun dismissVerifyPwdLoading(showPwdDialog: Boolean = false) {
        Log.e(TAG, "dismissVerifyPwdLoading hide loading dialog，show password dialog：$showPwdDialog")
        cancelNotifyObserver(SHOW_VERIFY_PWD_LOADING_DIALOG)
        notifyObserver(DISMISS_VERIFY_PWD_LOADING_DIALOG)
        if (showPwdDialog) {
            notifyObserver(RESHOW_DECOMPRESS_PASSWORD_DIALOG, passwordQueue.getLastedPassword())
        }
    }

    private fun validateDestSpaceNotEnough(): Boolean {
        val hasSelectFiles = mSelectDecompressFiles.isNullOrEmpty().not()
        var totalSize: Long = 0
        if (hasSelectFiles) {
            mSelectDecompressFiles?.forEach {
                totalSize += it.mSize
            }
        } else {
            totalSize = mSourceFile.mSize
        }
        val result = checkDestStorageSpace(mDestParentFile, totalSize)
        return if (result.first) {
            cancelShowCalculateProgress()
            notifyObserver(NOTICE_DECOMPRESS_SPACE_NOT_ENOUGH, result.second)
            true
        } else {
            false
        }
    }

    override fun afterRun(result: Boolean) {
        // Do nothing, this result value no mean the result of the decompress work
    }

    override fun recycle() {
        mSelectDecompressFiles?.clear()
    }
}