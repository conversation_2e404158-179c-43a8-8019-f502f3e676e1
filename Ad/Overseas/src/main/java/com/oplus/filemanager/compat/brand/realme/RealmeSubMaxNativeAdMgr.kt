/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RealmeSubMaxNativeAdMgr
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/8/8 17:08
 ** Author      : ********
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********        2024/8/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat.brand.realme

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.recyclerview.widget.RecyclerView
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxError
import com.applovin.mediation.nativeAds.MaxNativeAdListener
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_CLICK
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_FILL
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_REQUEST
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_SHOW
import com.filemanager.common.utils.StatisticsUtils.AD_REQUEST_FAIL
import com.filemanager.common.utils.StatisticsUtils.EVENT_AD_LOAD_TAG
import com.filemanager.common.utils.StatisticsUtils.NATIVE_ADS_REQ
import com.filemanager.common.utils.StatisticsUtils.REALME_FILE_EVENT
import com.filemanager.common.utils.StatisticsUtils.SECONDARYPAGE_NATIVE_ADS_DISPLAY
import com.filemanager.common.utils.StatisticsUtils.SECONDARYPAGE_NATIVE_ADS_FILL
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.utils.AdHelper

class RealmeSubMaxNativeAdMgr : BaseSubPageAdMgr() {
    private var adLayout: RelativeLayout? = null
    private var multiFmAdEntityItem: MultiFmAdEntityItem? = null
    private var nativeAdLoader: MaxNativeAdLoader? = null
    private var nativeAd: MaxAd? = null
    companion object {
        private const val AD_POS_ID = "bd6efa7bd73b7c5f"
    }

    override fun requestSubAd(
        activity: Activity,
        name: String,
        adapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>,
        fileList: ArrayList<MediaFileWrapper>,
        needHideAd: Boolean
    ) {
        val page = this.name
        if (!AdHelper.subAdSwitch) {
            // 云控关闭了二级页面广告
            Log.e(AdvertEntry.AD_TAG, "mdp sub ad switch is closed")
            return
        }

        Log.d(AdvertEntry.AD_TAG, "request sub native ad")
        multiFmAdEntityItem = MultiFmAdEntityItem(null, name, adapter, fileList, needHideAd)
        adLayout = RelativeLayout(activity)
        adLayout?.layoutParams = RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.MATCH_PARENT)
        nativeAdLoader = MaxNativeAdLoader(AD_POS_ID, activity)
        nativeAdLoader?.setNativeAdListener(object : MaxNativeAdListener() {
            override fun onNativeAdLoaded(nativeAdView: MaxNativeAdView?, ad: MaxAd) {
                // Clean up any pre-existing native ad to prevent memory leaks.
                if (nativeAd != null) {
                    nativeAdLoader?.destroy(nativeAd)
                }

                // Save ad for cleanup.
                nativeAd = ad

                // Add ad view to view.
                adLayout?.removeAllViews()
                adLayout?.visibility = View.VISIBLE
                adLayout?.addView(nativeAdView, FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT))
                multiFmAdEntityItem?.onLoadSuccess()

                val fillMap = HashMap<String, String>()
                fillMap[SECONDARYPAGE_NATIVE_ADS_FILL] = AD_EVENT_FILL
                StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, fillMap)
                StatisticsUtils.statisticsAdReturnFlow(appContext, true, page, AdHelper.adSource, null, null)

                val map = HashMap<String, String>()
                map[SECONDARYPAGE_NATIVE_ADS_DISPLAY] = AD_EVENT_SHOW
                StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
                StatisticsUtils.statisticsAdShowFlow(appContext, page, AdHelper.adSource, null, null, null)
            }

            override fun onNativeAdLoadFailed(adUnitId: String, error: MaxError) {
                adLayout?.removeAllViews()
                adLayout?.visibility = View.GONE
                Log.e(AdvertEntry.AD_TAG, "sub onNativeAdLoadFailed error code:${error.code} message:${error.message}")

                val map = HashMap<String, String>()
                map[StatisticsUtils.NATIVE_ADS_REQ_FAIL] = AD_REQUEST_FAIL
                StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
                StatisticsUtils.statisticsAdReturnFlow(appContext, false, page, AdHelper.adSource, null, null)
            }

            override fun onNativeAdClicked(ad: MaxAd) {
                val map = HashMap<String, String>()
                map[StatisticsUtils.SECONDARYPAGE_NATIVE_ADS_CLICK] = AD_EVENT_CLICK
                StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
                StatisticsUtils.statisticsAdClick(appContext, page, AdHelper.adSource, null, null, null)
                Log.d(AdvertEntry.AD_TAG, "sub onNativeAdClicked")
            }
        })
        nativeAdLoader?.loadAd(AdHelper.createBinder(activity))
        val map = HashMap<String, String>()
        map[NATIVE_ADS_REQ] = AD_EVENT_REQUEST
        StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        StatisticsUtils.statisticsAdRequestFlow(appContext, page, AdHelper.adSource, null)
    }

    override fun scanModeChange(name: String, needHideAd: Boolean) {
        if ((adLayout?.visibility == View.VISIBLE) && (adLayout!!.childCount > 0)) {
            multiFmAdEntityItem?.setNeedHideAD(needHideAd)
            multiFmAdEntityItem?.onLoadSuccess()
        }
    }

    override fun getEntryView(name: String): RelativeLayout? {
        return adLayout
    }

    override fun eventDestroy() {
        if (nativeAd != null) {
            nativeAdLoader?.destroy(nativeAd)
        }
        adLayout?.removeAllViews()
        adLayout?.visibility = View.GONE
        nativeAdLoader?.destroy()
    }
}