/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - FilePreviewApi.kt
 * Description:
 *     The implementation for file preview router api.
 *
 * Version: 1.0
 * Date: 2024-09-02
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-09-02   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview

import android.app.Application
import android.content.Context
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.interfaze.filepreview.FILE_PREVIEW_TYPE_APPLICATION
import com.oplus.filemanager.interfaze.filepreview.FILE_PREVIEW_TYPE_ARCHIVE
import com.oplus.filemanager.interfaze.filepreview.FILE_PREVIEW_TYPE_AUDIO
import com.oplus.filemanager.interfaze.filepreview.FILE_PREVIEW_TYPE_DOC
import com.oplus.filemanager.interfaze.filepreview.FILE_PREVIEW_TYPE_IMAGE
import com.oplus.filemanager.interfaze.filepreview.FILE_PREVIEW_TYPE_NORMAL
import com.oplus.filemanager.interfaze.filepreview.FILE_PREVIEW_TYPE_REMOTE
import com.oplus.filemanager.interfaze.filepreview.FILE_PREVIEW_TYPE_VIDEO
import com.oplus.filemanager.interfaze.filepreview.FilePreviewType
import com.oplus.filemanager.interfaze.filepreview.IFilePreviewApi
import com.oplus.filemanager.interfaze.filepreview.IFilePreviewFragment
import com.oplus.filemanager.interfaze.filepreview.IFilesHeapUpFragment
import com.oplus.filemanager.preview.apk.ApkPreviewFragment
import com.oplus.filemanager.preview.archive.ArchivePreviewFragment
import com.oplus.filemanager.preview.audio.AudioPreviewFragment
import com.oplus.filemanager.preview.doc.DocPreviewFragment
import com.oplus.filemanager.preview.image.ImagePreviewFragment
import com.oplus.filemanager.preview.normal.NormalPreviewFragment
import com.oplus.filemanager.preview.remote.RemotePreviewFragment
import com.oplus.filemanager.preview.selection.SelectionHeapUpFragment
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import com.oplus.filemanager.preview.utils.MediaPlayerHelper
import com.oplus.filemanager.preview.video.VideoPreviewFragment

internal object FilePreviewApi : IFilePreviewApi {

    override fun initPreviewConfigs(application: Application) {
        MediaPlayerHelper.initGlobalConfigs(application)
    }

    override fun getPreviewType(context: Context, fileBean: BaseFileBean): Int {
        val helper = FilePreviewTypeCheckHelper(context)
        return when {
            helper.isRemoteType(fileBean) -> FILE_PREVIEW_TYPE_REMOTE
            helper.isApkFileType(fileBean) -> FILE_PREVIEW_TYPE_APPLICATION
            helper.isArchiveFileType(fileBean) -> FILE_PREVIEW_TYPE_ARCHIVE
            helper.isAudioFileType(fileBean) -> FILE_PREVIEW_TYPE_AUDIO
            helper.isImageFileType(fileBean) -> FILE_PREVIEW_TYPE_IMAGE
            helper.isVideoFileType(fileBean) -> FILE_PREVIEW_TYPE_VIDEO
            helper.isDocType(fileBean) -> FILE_PREVIEW_TYPE_DOC
            else -> FILE_PREVIEW_TYPE_NORMAL
        }
    }

    override fun obtainFilePreviewFragment(
        @FilePreviewType previewType: Int
    ): IFilePreviewFragment = when (previewType) {
        FILE_PREVIEW_TYPE_IMAGE -> ImagePreviewFragment()
        FILE_PREVIEW_TYPE_AUDIO -> AudioPreviewFragment()
        FILE_PREVIEW_TYPE_VIDEO -> VideoPreviewFragment()
        FILE_PREVIEW_TYPE_APPLICATION -> ApkPreviewFragment()
        FILE_PREVIEW_TYPE_ARCHIVE -> ArchivePreviewFragment()
        FILE_PREVIEW_TYPE_DOC -> DocPreviewFragment()
        FILE_PREVIEW_TYPE_REMOTE -> RemotePreviewFragment()
        else -> NormalPreviewFragment()
    }

    override fun obtainFileHeapUpFragment(): IFilesHeapUpFragment = SelectionHeapUpFragment()
}