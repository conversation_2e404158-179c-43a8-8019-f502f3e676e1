/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentWidgetOpenFileActivity
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/12
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/12      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.activity

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.MimeTypeHelper.Companion.COMPRESSED_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.JAR_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.P7ZIP_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.RAR_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.ZIP_TYPE
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.fileoperate.open.FileActionOpen
import com.filemanager.fileoperate.open.FileOpenObserver
import com.oplus.filemanager.cardwidget.provider.RecentFilesCardWidgetProvider.Companion.mOpenFile
import com.oplus.filemanager.cardwidget.provider.RecentFilesCardWidgetProvider.Companion.mRecentFiles
import com.oplus.filemanager.cardwidget.recent.data.getActionByType
import com.oplus.filemanager.cardwidget.util.START_FILE_BROWSER
import com.oplus.filemanager.cardwidget.util.START_FILE_MANAGER_ACTION
import com.oplus.filemanager.recent.utils.RecentDataStorage
import org.apache.commons.io.FilenameUtils
import org.json.JSONException
import org.json.JSONObject

/**
 * 从负一屏桌面卡片中打开文件Activity，目前使用于最近卡片(未上线)及标签卡片
 */
class CardWidgetOpenFileActivity : AppCompatActivity() {

    companion object {
        const val TAG = "CardWidgetOpenFileActivity"
        const val INDEX = "index"
        const val KEY_FILE_BEAN = "fileBaseBean"
        const val KEY_FILE_PATH = "filePath"
        const val KEY_DISPLAY_NAME = "displayName"
        const val KEY_LOCAL_TYPE = "localType"
        const val KEY_SIZE = "size"
        const val KEY_FILE_MODIFIED = "fileModified"
        const val KEY_CURRENT_DIR = "CurrentDir"
    }

    private var baseFile: BaseFileBean? = null
    private var isFromCard = true
    private var isFromRecentCard = false


    @SuppressLint("ImplicitIntentDetector")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        checkRecentCardFile()
        if (baseFile == null) {
            checkLabelCardFile()
        }
        if (baseFile == null) {
            checkRecentCardFileData()
        }
        if (baseFile == null ) {
            finish()
            return
        }
        baseFile?.let { file ->
            if (file.mIsDirectory) {
                val intent = Intent()
                intent.action = START_FILE_BROWSER
                intent.putExtra(KEY_CURRENT_DIR, baseFile?.mData)
                intent.putExtra(KtConstants.FROM_DETAIL, true)
                startActivity(intent)
                staticsLaunchEntry(Constants.CARD_RECENT, Constants.PAGE_FILE_BROWSER)
                finish()
            } else if (isCompressedFile(file)) {
                if (isFromRecentCard) {
                    navigateToRecentCardPage(intent)
                } else {
                    navigateToLabelPage()
                }
            } else {
                FileActionOpen.Companion.Builder(this, file)
                    .setIsFromRecent(isFromCard)
                    .setIsFromRecentCardWidget(isFromCard)
                    .build()
                    .execute(object : FileOpenObserver(this@CardWidgetOpenFileActivity) {
                        override fun onActionDone(result: Boolean, data: Any?) {
                            Log.d(TAG, "onActionDone result $result")
                            if (result && isFromCard) {
                                staticsLaunchEntry(Constants.CARD_RECENT, Constants.PAGE_RECENT)
                                RecentDataStorage.getInstance().openFile = baseFile
                            }
                            if (!result) {
                                finish()
                            }
                        }

                        override fun onActionCancelled() {
                            super.onActionCancelled()
                            finish()
                        }
                    })
            }
        }
    }

    /**
     * 检测标签卡片
     * true 关闭页面
     */
    private fun checkLabelCardFile() {
        val fileBeanText = intent.extras?.getString(KEY_FILE_BEAN)
        fileBeanText?.let { jsonText ->
            try {
                val json = JSONObject(jsonText)
                baseFile = BaseFileBean().apply {
                    mDisplayName = json.getString(KEY_DISPLAY_NAME)
                    mData = json.getString(KEY_FILE_PATH)
                    mLocalType = json.getInt(KEY_LOCAL_TYPE)
                    mSize = json.getLong(KEY_SIZE)
                    mDateModified = json.getLong(KEY_FILE_MODIFIED)
                }
            } catch (e: JSONException) {
                Log.d(TAG, "checkIsFromLabelCard json $e")
            }
        }
        isFromCard = true
        Log.d(TAG, "checkLabelCardFile fileBeanText：$fileBeanText  $baseFile")
    }

    private fun navigateToLabelPage() {
        val intent = Intent()
        intent.action = START_FILE_MANAGER_ACTION
        val paramsJson = JSONObject()
        paramsJson.put(Constants.KEY_IS_FROM_LABEL_CARD, true)
        intent.putExtra(Constants.KEY_IS_FROM_LABEL_CARD, paramsJson.toString())
        startActivity(intent)
        staticsLaunchEntry(Constants.CARD_LABEL, Constants.PAGE_LABEL)
        finish()
    }

    private fun navigateToRecentCardPage(intent: Intent) {
        val cardType = IntentUtils.getString(intent, Constants.KEY_CARD_TYPE)
        Log.d(TAG, "navigateToRecentCardPage -> cardType = $cardType")
        val type = try {
            cardType?.toInt()
        } catch (e: NumberFormatException) {
            Log.e(TAG, "navigateToRecentCardPage -> cardType parse error = $e")
            -1
        }
        if (cardType.isNullOrEmpty().not() && type != null && type != -1) {
            val action = getActionByType(type)
            val navigateIntent = Intent()
            navigateIntent.action = action
            navigateIntent.putExtra(Constants.KEY_CARD_TYPE, cardType)
            navigateIntent.putExtra(
                Constants.KEY_IS_FROM_RECENT_WIDGET,
                IntentUtils.getString(intent, Constants.KEY_IS_FROM_RECENT_WIDGET)
            )
            navigateIntent.putExtra(
                Constants.KEY_SOURCE_APP_NAME,
                IntentUtils.getString(intent, Constants.KEY_SOURCE_APP_NAME)
            )
            navigateIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(navigateIntent)
            staticsLaunchEntry(Constants.CARD_RECENT, Constants.PAGE_SUPER)
            finish()
        } else {
            val navigateIntent = Intent()
            navigateIntent.action = START_FILE_MANAGER_ACTION
            startActivity(navigateIntent)
            staticsLaunchEntry(Constants.CARD_RECENT, Constants.PAGE_MAIN)
            finish()
        }
    }

    private fun checkRecentCardFileData() {
        val filePath = IntentUtils.getString(intent, Constants.KEY_ITEM_INFO)
        Log.d(TAG, "checkRecentCardFileData -> filePath = $filePath")
        filePath?.let { path ->
            if (path.isNotEmpty()) {
                baseFile = BaseFileBean().apply {
                    mData = path
                    mLocalType = MimeTypeHelper.getTypeFromExtension(FilenameUtils.getExtension(path))
                        ?: MimeTypeHelper.UNKNOWN_TYPE
                }
                staticOpenFile(path)
            }
            isFromRecentCard = true
        }
    }

    private fun staticOpenFile(path: String) {
        val map = mutableMapOf<String, String>()
        map[StatisticsUtils.FILE_EXTENSION] = FilenameUtils.getExtension(path)
        StatisticsUtils.onCommon(
            MyApplication.sAppContext,
            StatisticsUtils.RECENT_FILE_OPEN_FROM_CARD, map)
    }

    /**
     * 检测最近卡片
     * true 关闭页面
     */
    private fun checkRecentCardFile() {
        val index = IntentUtils.getInt(intent, INDEX, 0)
        if (index >= mRecentFiles.size) {
            return
        }
        baseFile = mRecentFiles[index]
        mOpenFile = mRecentFiles[index]
        isFromCard = true
        Log.d(TAG, "checkRecentCardFile：index$index  $baseFile")
    }

    @Suppress("ComplexCondition")
    private fun isCompressedFile(baseFile: BaseFileBean) =
        (baseFile.mLocalType == COMPRESSED_TYPE || baseFile.mLocalType == ZIP_TYPE
                || baseFile.mLocalType == RAR_TYPE || baseFile.mLocalType == JAR_TYPE
                || baseFile.mLocalType == P7ZIP_TYPE)

    private fun staticsLaunchEntry(card: String, landingPage: String) {
        StatisticsUtils.statisticsEntryLaunch(this, Constants.PKG_LAUNCHER, card, landingPage)
    }
}