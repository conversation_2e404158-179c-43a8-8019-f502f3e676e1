/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PersonalInfoDetailViewModel
 * * Description: 个人信息明示清单的详情ViewModel
 * * Version: 1.0
 * * Date : 2024/08/27
 * * Author:W9060445
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     W9060445         2024/08/27      1.0            create
 ****************************************************************/
package com.filemanager.setting.ui.privacy.personal

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.filemanager.common.base.BaseViewModel
import com.filemanager.setting.utils.PersonalPrivacyCategory
import com.filemanager.setting.utils.PrivacyEncryptFuncFactory
import com.oplus.filemanager.provider.CollectPrivacyDBHelper
import com.oplus.filemanager.room.utils.CollectTimeUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PersonalInfoDetailViewModel : BaseViewModel() {

    var collectCount: MutableLiveData<Int> = MutableLiveData()
    var collectContent: MutableLiveData<String> = MutableLiveData()

    fun query(category: String, @CollectTimeUtils.TimeFilter timeFilter: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            val list = CollectPrivacyDBHelper.getAllCollectPrivacy(category, timeFilter)
            val count = if (CollectPrivacyDBHelper.isNotDisplayContentReplace(category)) {
                list?.lastOrNull()?.content?.toIntOrNull() ?: 0
            } else if (CollectPrivacyDBHelper.isNotDisplayContentIncrease(category)) {
                var temp = 0
                list?.forEach { temp += it.content?.toIntOrNull() ?: 0 }
                temp
            } else {
                list?.size ?: 0
            }
            val contentList = list?.distinctBy { it.content }?.map { it.content ?: "" } ?: emptyList()
            if (PersonalPrivacyCategory.hasActualContent(category)) {
                val encryptContent = encryptContent(category, contentList)
                collectContent.postValue(encryptContent)
            }
            collectCount.postValue(count)
        }
    }

    /**
     * 加密内容
     */
    private fun encryptContent(category: String, list: List<String>): String {
        if (list.isEmpty()) {
            return "/"
        }
        val builder = StringBuilder()
        val lastIndex = list.size - 1
        val encryptFun = PrivacyEncryptFuncFactory.get(category)
        list.forEachIndexed { index, item ->
            builder.append(encryptFun.apply(item))
            if (index < lastIndex) {
                builder.append(";")
            }
        }
        return builder.toString()
    }
}