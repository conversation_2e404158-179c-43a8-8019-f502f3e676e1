<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dialog_layout">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginBottom="@dimen/dimen_84dp">
        <com.google.android.material.appbar.COUIDividerAppBarLayout
            android:id="@+id/appbar"
            style="@style/CommonAppBarStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/color_transparent"
            android:clickable="true"
            android:focusable="true"
            android:paddingLeft="@dimen/dimen_0dp"
            android:paddingRight="@dimen/dimen_0dp"
            app:elevation="@dimen/toolbar_elevation">

            <com.coui.appcompat.toolbar.COUIToolbar
                android:id="@+id/toolbar"
                android:background="@null"
                android:layout_width="match_parent"
                style="@style/COUIToolBarInAppBarLayoutStyle"
                app:titleCenter="false"
                app:supportTitleTextAppearance="@style/textAppearanceSecondTitle"/>
        </com.google.android.material.appbar.COUIDividerAppBarLayout>

        <RelativeLayout
            android:id="@+id/content_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.filemanager.common.view.FileManagerRecyclerView
                android:id="@+id/add_file_recycle_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </RelativeLayout>

    </LinearLayout>

        <FrameLayout
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:layout_gravity="bottom"
            android:minHeight="@dimen/operation_btn_background_height"
            android:background="?attr/couiColorBackgroundElevatedWithCard">
            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_background_height"
                android:layout_gravity="top"
                android:alpha="0"
                android:background="?attr/couiColorDivider"
                android:id="@+id/button_divider"
                android:forceDarkAllowed="false" />

        <com.coui.appcompat.button.COUIButton
            style="@style/Widget.COUI.Button.Large"
            android:id="@+id/btn_add_file"
            android:layout_width="@dimen/operation_btn_width"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|bottom"
            android:layout_marginBottom="@dimen/operation_btn_margin_bottom"
            android:contentDescription="@string/label_add_recent_file"
            android:paddingHorizontal="@dimen/dimen_7dp"
            android:paddingVertical="@dimen/dimen_2dp"
            android:text="@string/label_add_recent_file" />
        </FrameLayout>

</FrameLayout>