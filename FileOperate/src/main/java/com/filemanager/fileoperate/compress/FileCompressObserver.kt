/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileCompressObserver.kt
 ** Description: Monitor file compress
 ** Version: 1.0
 ** Date: 2020/3/9
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.compress

import android.app.Activity
import android.content.Context
import android.view.ContextThemeWrapper
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.utils.COUISnackBarUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.base.BaseFileNameDialog
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser

const val SHOW_COMPRESS_FILENAME_DIALOG = 1
const val DISMISS_COMPRESS_FILENAME_DIALOG = 2
const val NOTICE_COMPRESS_FILENAME_EMPTY = 3
const val NOTICE_COMPRESS_FILE_EXISTS = 4
const val NOTICE_COMPRESS_FILESIZE_TOO_LARGE = 6
const val NOTICE_COMPRESS_SPACE_NOT_ENOUGH = 7
const val NOTICE_COMPRESS_FILE_NAME_CANT_START_WITH_POINT = 9
const val SHOW_COMPRESS_FAILED_TOAST = 10
const val NOTICE_SOURCE_FILE_NOT_EXIST = 11

open class FileCompressObserver(context: ContextThemeWrapper) : BaseFileActionObserver(context) {

    private var mFileNameDialog: FileCompressNameDialog? = null
    private var noticeDialog: AlertDialog? = null
    private var spaceNotEnoughDialog: AlertDialog? = null

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        when (result.first) {
            SHOW_COMPRESS_FILENAME_DIALOG -> {
                dismissFileNameDialog()
                if (result.second is NameDialogBean) {
                    mFileNameDialog = DialogFactory.createFileNameDialog(context, result.second as NameDialogBean)
                    mFileNameDialog?.show()
                }
            }
            DISMISS_COMPRESS_FILENAME_DIALOG -> dismissFileNameDialog()
            NOTICE_COMPRESS_FILESIZE_TOO_LARGE -> {
                dismissProgressDialog()
                showNoticeDialog(context, context.getString(com.filemanager.common.R.string.compress_too_large_detail))
                onActionDone(false)
                return true
            }
            NOTICE_COMPRESS_SPACE_NOT_ENOUGH -> {
                dismissProgressDialog()
                if (result.second is String) {
                    showSpaceNotEnoughNotice(context, result.second as String)
                }
                onActionDone(false)
                return true
            }
            NOTICE_SOURCE_FILE_NOT_EXIST ->  {
                CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
                onActionDone(false)
                return true
            }
            ACTION_DONE -> {
                if ((context is Activity) && (result.second is String)) {
                    COUISnackBarUtils.show(context, com.filemanager.common.R.string.compress_success) {
                        val fileBrowser = Injector.injectFactory<IFileBrowser>()
                        fileBrowser?.toFileBrowserActivity(context, result.second as String)
                    }
                } else {
                    CustomToast.showShort(com.filemanager.common.R.string.compress_success)
                }
                return false
            }
            SHOW_COMPRESS_FAILED_TOAST -> {
                CustomToast.showShort(com.filemanager.common.R.string.compress_error)
                return true
            }
        }
        return false
    }
    override fun onActionReShowDialog() {
        mFileNameDialog?.reShowDialog()
    }

    private fun showSpaceNotEnoughNotice(context: Context, storage: String) {
        if (storage.isEmpty()) {
            CustomToast.showShort(com.filemanager.common.R.string.storage_space_not_enough)
        } else {
            val msg = context.getString(
                com.filemanager.common.R.string.disk_space_not_enough, storage,
                    context.getString(com.filemanager.common.R.string.unable_to_compress))
            if (FeatureCompat.sPhoneManagerStartInfo != null) {
                spaceNotEnoughDialog = DialogFactory.createSpaceNotEnoughDialog(context, msg)
                spaceNotEnoughDialog?.show()
            } else {
                CustomToast.showShort(msg)
            }
        }
    }

    private fun showNoticeDialog(context: Context, msg: String) {
        noticeDialog = DialogFactory.createNoticeDialog(context, context.getString(com.filemanager.common.R.string.compress_file), msg)
        noticeDialog?.show()
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || mFileNameDialog?.isShowing() ?: false
                || noticeDialog?.isShowing ?: false || spaceNotEnoughDialog?.isShowing ?: false
    }

    override fun recycle() {
        dismissFileNameDialog()
        super.recycle()
    }

    private fun dismissFileNameDialog() {
        try {
            mFileNameDialog?.dismiss()
        } catch (e: Exception) {
            Log.w("FileCompressObserver", "Failed dismiss name dialog: ${e.message}")
        }
        mFileNameDialog = null
    }

    data class NameDialogBean(var sourceFiles: List<BaseFileBean>, var destFile: BaseFileBean) {
        var resultListener: BaseFileNameDialog.OnButtonClickListener? = null

        fun recycle() {
            resultListener = null
        }
    }
}

private object DialogFactory {
    private const val TAG = "DialogFactory"
    fun createFileNameDialog(context: Context, second: FileCompressObserver.NameDialogBean): FileCompressNameDialog {
        return FileCompressNameDialog(context, second)
    }

    fun createNoticeDialog(context: Context, title: String, msg: String): AlertDialog {
        return COUIAlertDialogBuilder(context).setTitle(title).setMessage(msg)
                .setIcon(android.R.drawable.ic_dialog_alert)
                .setPositiveButton(android.R.string.ok, null).create()
    }

    fun createSpaceNotEnoughDialog(context: Context, msg: String): AlertDialog {
        val dialog = COUIAlertDialogBuilder(context).setTitle(msg)
                .setPositiveButton(com.filemanager.common.R.string.garbage_cleanup) { _, _ ->
                    KtAppUtils.startPhoneManager(context)
                }
                .setNegativeButton(com.filemanager.common.R.string.alert_dialog_cancel, null)
                .create()
        dialog.setCanceledOnTouchOutside(false)
        return dialog
    }
}