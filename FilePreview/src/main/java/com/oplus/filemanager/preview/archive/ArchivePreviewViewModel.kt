/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ArchivePreviewViewModel.kt
 * Description:
 *     The view model for preview archive file.
 *
 * Version: 1.0
 * Date: 2024-10-14
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-10-14   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.archive

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.viewModelScope
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.path.FileBrowPathHelper
import com.filemanager.common.path.FilePathHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.compresspreview.ui.CompressPreviewViewModel
import com.filemanager.fileoperate.base.SHOW_PROGRESS
import com.filemanager.fileoperate.decompress.BaseDecompressFile
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_FAILED
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_NOT_ENOUGH
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_NOT_EXIST
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_NOT_EXIST_DFM_DISCONNECTED
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_UNSUPPORT
import com.filemanager.fileoperate.decompress.RESHOW_DECOMPRESS_PASSWORD_DIALOG
import com.filemanager.fileoperate.decompress.SHOW_DECOMPRESS_PASSWORD_DIALOG
import com.filemanager.fileoperate.previewcompress.CompressPreviewCacheHelper
import com.filemanager.fileoperate.previewcompress.FileActionPreviewCompress
import com.filemanager.fileoperate.previewcompress.FilePreviewCompressObserver
import com.oplus.filemanager.preview.archive.ArchivePreviewLoadResult.Companion.PREVIEW_LOAD_RESULT_DONE
import com.oplus.filemanager.preview.archive.ArchivePreviewLoadResult.Companion.PREVIEW_LOAD_RESULT_ENCRYPTED
import com.oplus.filemanager.preview.archive.ArchivePreviewLoadResult.Companion.PREVIEW_LOAD_RESULT_ERR
import com.oplus.filemanager.preview.archive.ArchivePreviewLoadResult.Companion.PREVIEW_LOAD_RESULT_IS_OTA
import com.oplus.filemanager.preview.archive.ArchivePreviewLoadResult.Companion.PREVIEW_LOAD_RESULT_OVER_LIMIT
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.ota.upgrade.OtaUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

internal typealias IOnArchivePreviewDataLaunched = (ArchivePreviewLoadResult, CompressPreviewViewModel) -> Unit

internal class ArchivePreviewViewModel : FilePreviewViewModel() {

    private companion object {
        private const val TAG = "ArchivePreviewViewModel"
    }

    val compressModel: CompressPreviewViewModel = CompressPreviewViewModel()

    private val previewLoadState = MutableLiveData<ArchivePreviewLoadResult>()
    private val launchObserverMap =
        mutableMapOf<IOnArchivePreviewDataLaunched, Observer<ArchivePreviewLoadResult>>()

    private var previewPathHelper: FilePathHelper?
        get() = compressModel.mPathHelp
        private set(value) = compressModel::mPathHelp.set(value)

    fun putPreviewFile(activity: BaseVMActivity?, previewFile: BaseFileBean) {
        activity?.takeUnless {
            it.isFinishing || it.isDestroyed
        } ?: run {
            Log.e(TAG, "putPreviewFile: activity is null or not available, act=$activity")
            return
        }
        previewFile.mData?.let {
            val pathHelper = FileBrowPathHelper(it)
            previewPathHelper = pathHelper
            PathFileWrapper(it).mDisplayName?.let { rootPath ->
                pathHelper.updateRootPath(rootPath)
            }
        }
        putPreviewFile(previewFile)
        viewModelScope.launch(Dispatchers.IO) {
            loadArchivePreviewData(activity, previewFile)
        }
    }

    fun launchArchivePreviewData(
        lifecycleOwner: LifecycleOwner,
        onLaunched: IOnArchivePreviewDataLaunched
    ) {
        val loadResult = previewLoadState.value
        if (loadResult != null) {
            onLaunched(loadResult, compressModel)
        }
        val observer = Observer<ArchivePreviewLoadResult> {
            onLaunched(it, compressModel)
        }
        launchObserverMap[onLaunched] = observer
        previewLoadState.observe(lifecycleOwner, observer)
    }

    fun releaseArchivePreviewDataLauncher(onLaunched: IOnArchivePreviewDataLaunched) {
        launchObserverMap[onLaunched]?.let {
            previewLoadState.removeObserver(it)
        }
        launchObserverMap.remove(onLaunched)
    }

    private suspend fun loadArchivePreviewData(
        activity: BaseVMActivity,
        previewFile: BaseFileBean
    ) {
        if (OtaUtils.isOzipFile(previewFile)) {
            Log.e(TAG, "loadArchivePreviewData: isOzipFile")
            withContext(Dispatchers.Main) {
                previewLoadState.value =
                    ArchivePreviewLoadResult(PREVIEW_LOAD_RESULT_IS_OTA, previewFile)
            }
            return
        }
        Log.e(TAG, "loadArchivePreviewData: startLoad")
        withContext(Dispatchers.Main) {
            val loadResult = suspendCoroutine { continuation ->
                ArchivePreviewDataLoader(activity).startLoad(previewFile) {
                    continuation.resume(it)
                }
            }
            Log.e(TAG, "loadArchivePreviewData: loadResult=${loadResult.resultCode}")
            previewFile.mData?.takeIf {
                loadResult.resultCode == PREVIEW_LOAD_RESULT_DONE
            }?.let { previewPath ->
                compressModel.previewData(activity, previewPath, false)
            }
            previewLoadState.value = loadResult
        }
    }

    private class ArchivePreviewDataLoader(
        private val activity: BaseVMActivity
    ) : FilePreviewCompressObserver(activity) {

        private lateinit var previewAction: FileActionPreviewCompress
        private lateinit var loadComplete: (ArchivePreviewLoadResult) -> Unit
        private lateinit var loadedPreviewFile: BaseFileBean

        fun startLoad(
            previewFile: BaseFileBean,
            onLoadComplete: (ArchivePreviewLoadResult) -> Unit
        ) {
            loadedPreviewFile = previewFile
            loadComplete = onLoadComplete
            previewAction = FileActionPreviewCompress(activity, previewFile)
            previewAction.execute(this)
        }

        override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
            when (result.first) {
                SHOW_PROGRESS -> onShowProgress()
                SHOW_DECOMPRESS_PASSWORD_DIALOG -> onRequirePassword()
                RESHOW_DECOMPRESS_PASSWORD_DIALOG -> onRequirePassword()
                PREVIEW_NOT_EXIST -> onPreviewUnsupported()
                PREVIEW_NOT_EXIST_DFM_DISCONNECTED -> onPreviewUnsupported()
                PREVIEW_UNSUPPORT -> onPreviewUnsupported()
                PREVIEW_FAILED -> onPreviewUnsupported()
                PREVIEW_NOT_ENOUGH -> onPreviewUnsupported()
                else -> return super.onChanged(context, result)
            }
            return true
        }

        @Suppress("UNCHECKED_CAST")
        override fun onActionDone(result: Boolean, data: Any?) {
            Log.d(TAG, "loadArchivePreviewData: onActionDone.result=$result")
            if (result && (data is Pair<*, *>) && (data.second is Map<*, *>?)) {
                CompressPreviewCacheHelper.storeTransferPreviewFile(
                    data.second as? Map<String, MutableList<out BaseDecompressFile>?>
                )
                loadComplete(loadResult(PREVIEW_LOAD_RESULT_DONE))
            } else {
                onPreviewUnsupported()
            }
        }

        override fun onActionPreviewOverCount(msg: String?) {
            Log.e(TAG, "loadArchivePreviewData: onActionPreviewOverCount: $msg")
            loadComplete(loadResult(PREVIEW_LOAD_RESULT_OVER_LIMIT, msg))
            previewAction.cancel(true)
        }

        private fun onShowProgress() {
            // Do not show progress when load for large screen preview model
        }

        private fun onRequirePassword() {
            Log.e(TAG, "loadArchivePreviewData: onRequirePassword")
            val msg = activity.getString(com.filemanager.common.R.string.preview_archive_encrypted)
            loadComplete(loadResult(PREVIEW_LOAD_RESULT_ENCRYPTED, msg))
            previewAction.cancel(true)
        }

        private fun onPreviewUnsupported() {
            Log.e(TAG, "loadArchivePreviewData: onPreviewUnsupported")
            val msg = activity.getString(com.filemanager.common.R.string.preview_file_unsupported)
            loadComplete(loadResult(PREVIEW_LOAD_RESULT_ERR, msg))
            previewAction.cancel(true)
        }

        private fun loadResult(
            @ArchivePreviewLoadResult.LoadResultCodes resultCode: Int,
            msg: String? = null
        ): ArchivePreviewLoadResult = if (msg == null) {
            ArchivePreviewLoadResult(resultCode, loadedPreviewFile)
        } else {
            ArchivePreviewLoadResult(resultCode, loadedPreviewFile, msg)
        }
    }
}