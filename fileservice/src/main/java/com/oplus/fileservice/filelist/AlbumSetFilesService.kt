/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AlbumSetFilesService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/2/9 9:43
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/2/9       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.filelist

import android.content.Intent
import android.os.IBinder
import android.os.Messenger
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Log
import com.oplus.fileservice.filelist.scanner.AlbumScanner
import com.filemanager.common.utils.HansFreezeManager
import com.oplus.fileservice.utils.WebFileConstant

class AlbumSetFilesService : BaseService() {

    private var mFileListServiceHandler: FileListServiceHandler? = null

    private var mBucketKey: String = ""

    override fun onCreate() {
        Log.d(TAG, "onCreate")
        super.onCreate()
    }

    override fun onBind(intent: Intent): IBinder? {
        Log.d(TAG, "onBind -> action = ${intent.action} ; extras = ${intent.extras}")
        HansFreezeManager.instance.keepBackgroundRunning()
        val extras = intent.extras
        extras?.apply {
            mBucketKey = getString(KEY_BUCKET_KEY, "")
            mPageNo = getInt(PAGE_NO, WebFileConstant.DEFAULT_PAGE_NO)
            mPageSize = getInt(PAGE_SIZE, WebFileConstant.DEFAULT_PAGE_SIZE)
            mSortOrder = getInt(SORT_ORDER, SortHelper.FILE_NAME_ORDER)
            isNeedThumbnail = getBoolean(NEED_THUMBNAIL, DEFAULT_NEED_THUMBNAIL)
        }
        Log.d(TAG, "Request params: [bucketKey: $mBucketKey ; pageNo: $mPageNo ; pageSize: $mPageSize; isNeedThumbnail:$isNeedThumbnail]")
        val albumScanner = AlbumScanner(mBucketKey, mPageNo, mPageSize, isNeedThumbnail)
        mFileListServiceHandler = FileListServiceHandler(albumScanner)
        return Messenger(mFileListServiceHandler).binder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Log.d(TAG, "onUnbind")
        HansFreezeManager.instance.cancelFrozenDelay()
        return super.onUnbind(intent)
    }

    companion object {
        private const val TAG = "AlbumSetFilesService"
        private const val KEY_BUCKET_KEY = "bucketKey"
    }
}