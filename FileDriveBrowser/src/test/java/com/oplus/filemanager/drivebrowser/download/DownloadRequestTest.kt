package com.oplus.filemanager.drivebrowser.download

import android.os.Bundle
import com.filemanager.common.helper.CategoryHelper
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.apache.commons.io.FilenameUtils
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * DownloadRequest的单元测试类
 * 用于测试DownloadRequest相关的功能
 */
@RunWith(RobolectricTestRunner::class)  // 使用Robolectric测试框架
@Config(sdk = [29])  // 配置测试的Android SDK版本为29
class DownloadRequestTest {

    // 测试用的Bundle对象
    private lateinit var requestBundle: Bundle

    /**
     * 在每个测试方法执行前的初始化方法
     */
    @Before
    fun setUp() {
        // 初始化Bundle对象
        requestBundle = Bundle()
        // 模拟FilenameUtils类的静态方法
        mockkStatic(FilenameUtils::class)
    }

    /**
     * 测试当传入的Bundle为null时，parseDownloadTaskInfo应该返回null
     */
    @Test
    fun `parseDownloadTaskInfo should return null when bundle is null`() {
        // 调用方法并验证返回值为null
        assertNull(parseDownloadTaskInfo(null))
    }

    /**
     * 测试当传入未知的source type时，parseDownloadTaskInfo应该返回null
     */
    @Test
    fun `parseDownloadTaskInfo should return null for unknown source type`() {
        // 设置一个未知的source type
        requestBundle.putInt(KEY_SOURCE_TYPE, -1)
        // 调用方法并验证返回值为null
        assertNull(parseDownloadTaskInfo(requestBundle))
    }

    /**
     * 测试正确解析KDocs文档的情况
     */
    @Test
    fun `parseDownloadTaskInfo should parse kdocs correctly`() {
        // Given - 准备测试数据
        requestBundle.apply {
            putInt(KEY_SOURCE_TYPE, CategoryHelper.CATEGORY_K_DOCS)  // 设置source type为KDocs
            putString(KEY_FILE_ID, "kdoc123")  // 设置文件ID
            putString(KEY_TARGET_PATH, "/path/to/kdoc")  // 设置目标路径
        }

        // When - 执行测试方法
        val result = parseDownloadTaskInfo(requestBundle)

        // Then - 验证结果
        assertNotNull(result)  // 验证结果不为null
        assertEquals(CategoryHelper.CATEGORY_K_DOCS, result?.category)  // 验证category正确
        assertEquals("kdoc123", result?.fileId)  // 验证fileId正确
        assertEquals("/path/to/kdoc", result?.targetPath)  // 验证targetPath正确
        assertNull(result?.exportType)  // 验证exportType为null
    }
}