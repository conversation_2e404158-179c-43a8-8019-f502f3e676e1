package com.oplus.filemanager.questionnaire

import android.app.Application
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.oplus.questionnaire.CDPSDK
import com.oplus.questionnaire.CdpView
import com.filemanager.common.utils.Log
import io.mockk.*
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.*
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.*

/**
 * 问卷API的单元测试类
 * 使用RobolectricTestRunner运行测试，配置SDK版本为29
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])  // 将SDK版本从28改为29以解决错误
class QuestionnaireApiTest {

    // 使用MockK框架模拟的Application对象
    @MockK
    private lateinit var mockApplication: Application

    // 使用MockK框架模拟的Context对象
    @MockK
    private lateinit var mockContext: Context

    // 使用MockK框架模拟的根视图对象
    @MockK
    private lateinit var mockRootView: ViewGroup

    // 使用MockK框架模拟的CdpView对象
    @MockK
    private lateinit var mockCdpView: CdpView

    // 使用MockK框架模拟的普通View对象
    @MockK
    private lateinit var mockView: View

    // 测试用的协程调度器
    private val testDispatcher = StandardTestDispatcher()

    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 设置主调度器为测试调度器
     * 3. 模拟静态类CDPSDK、LayoutInflater和Log
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        Dispatchers.setMain(testDispatcher)
        mockkStatic(CDPSDK::class)
        mockkStatic(LayoutInflater::class)
        mockkStatic(Log::class)
    }

    /**
     * 测试后的清理方法
     * 1. 重置主调度器
     * 2. 解除所有模拟
     */
    @After
    fun tearDown() {
        Dispatchers.resetMain()
        unmockkAll()
    }

    /**
     * 测试isSupportQuestionnaire方法
     * 验证是否支持问卷功能
     */
    @Test
    fun testIsSupportQuestionnaire() {
        assertTrue(QuestionnaireApi.isSupportQuestionnaire())
    }

    /**
     * 测试initSDK方法
     * 验证SDK初始化是否正确调用
     */
    @Test
    fun testInitSDK() {
        // 设置模拟行为：CDPSDK.initSdk和Log.d方法调用时不执行实际逻辑
        every { CDPSDK.initSdk(any(), any(), any(), any()) } just Runs
        every { Log.d(any(), any()) } just Runs

        // 调用被测方法
        QuestionnaireApi.initSDK(mockApplication)

        // 验证是否正确调用了CDPSDK.initSdk和Log.d方法
        verify {
            CDPSDK.initSdk(
                mockApplication,
                "39986f160c015aae1b06c77d6971b40e",
                "o248ixKU7WcdkVua+qXe3b6Bp9OdtzrVDCF+Jp5ajOQ=",
                "20016"
            )
            Log.d("QuestionnaireApi", "initSDK  :$mockApplication  ")
        }
    }

    /**
     * 测试releaseSpace方法
     * 验证释放空间功能是否正确调用
     */
    @Test
    fun testReleaseSpace() {
        // 设置模拟行为：Log.d和mockCdpView.releaseSpace方法调用时不执行实际逻辑
        every { Log.d(any(), any()) } just Runs
        every { mockCdpView.releaseSpace() } just Runs

        // 调用被测方法
        QuestionnaireApi.releaseSpace(mockCdpView)

        // 验证是否正确调用了Log.d和mockCdpView.releaseSpace方法
        verify {
            Log.d("QuestionnaireApi", "releaseSpace  :$mockCdpView  ")
            mockCdpView.releaseSpace()
        }
    }
}