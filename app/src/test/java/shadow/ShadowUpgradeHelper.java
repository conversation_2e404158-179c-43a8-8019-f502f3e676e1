/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowUpgradeHelper.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/8/28
 ** Author: JiaB<PERSON>n
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/8/28      1.0     create
 ****************************************************************/
package shadow;

import android.app.Activity;

import com.filemanager.common.helper.FileWrapper;
import com.oplus.ota.upgrade.PackageInfo;
import com.oplus.ota.upgrade.UpgradeHelper;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(UpgradeHelper.class)
public class ShadowUpgradeHelper {
    @Implementation
    public static void entryUpgradeTask(final Activity activity, FileWrapper file,
                                        final String path, final PackageInfo info) {

    }
}
