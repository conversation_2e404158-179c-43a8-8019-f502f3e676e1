<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.coui.appcompat.imageview.COUIRoundImageView
        android:id="@+id/removable_app_icon"
        android:layout_width="@dimen/dimen_44dp"
        android:layout_height="@dimen/dimen_44dp"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:layout_marginBottom="@dimen/dimen_16dp"
        app:couiBorderRadius="@dimen/dimen_11dp"
        app:couiType="round"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>