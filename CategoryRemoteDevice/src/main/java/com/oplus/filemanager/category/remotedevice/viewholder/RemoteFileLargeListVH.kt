/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RemoteFileLargeListVH
 ** Description : 远程电脑文件的大屏显示VH
 ** Version     : 1.0
 ** Date        : 2025/08/05 10:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2025/08/05       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.viewholder

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.EmojiUtils
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.view.TextViewSnippet
import com.filemanager.common.viewholder.FileBrowserLargeListVH

class RemoteFileLargeListVH(convertView: View, imgRadius: Int = 0) : FileBrowserLargeListVH(convertView, imgRadius, false) {


    companion object {

        private const val TAG = "RemoteFileLargeVH"

        @JvmStatic
        fun create(parent: ViewGroup, imgRadius: Int = 0): RemoteFileLargeListVH {
            val context = parent.context
            val itemView = LayoutInflater.from(context).inflate(FileBrowserLargeListVH.getLayoutId(), parent, false)
            val radius = if (imgRadius == 0) {
                context.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.file_list_bg_radius)
            } else {
                imgRadius
            }
            return RemoteFileLargeListVH(itemView, radius).apply {
                loadDocThumbnail = false
                showApkDetail = false
            }
        }
    }

    override fun setFileTitle(titleTv: TextViewSnippet, file: BaseFileBean, keyword: String) {
        if (file is RemoteFileBean) {
            val titleText = if (file.shouldShowAlternativeName()) file.alternativeName else file.mDisplayName
            titleTv.tag = file.mData
            titleTv.text = titleText
            titleTv.setTextViewStyle()
        } else {
            super.setFileTitle(titleTv, file, keyword)
        }
    }


    override fun setDuration(durationTv: TextView, file: BaseFileBean) {
        if (MimeTypeHelper.VIDEO_TYPE == file.mLocalType) {
            durationTv.visibility = View.VISIBLE
            val ext =  FileTypeUtils.getExtension(file.mData)
            ext?.let {
                durationTv.text = it
            }
        } else {
            durationTv.visibility = View.GONE
        }
    }

    override fun setFileDetail(detailTv: TextView, anotherNameTv: TextViewSnippet, file: BaseFileBean) {
        if (file !is RemoteFileBean) {
            return
        }
        val context = detailTv.context
        if (file.mIsDirectory) {
            val fileCount = context.resources.getQuantityString(
                R.plurals.text_x_items,
                file.folderFileNum,
                file.folderFileNum
            )
            detailTv.visibility = View.VISIBLE
            detailTv.text = fileCount
        } else {
            detailTv.visibility = View.GONE
            detailTv.text = ""
        }
    }

    override fun isSpecialFolder(file: BaseFileBean): Boolean {
        return BaseRemoteFileVH.isSpecialFolder(file)
    }

    override fun getSpecialFolderIcon(file: BaseFileBean): Int {
        return BaseRemoteFileVH.getSpecialFolderIcon(file, false)
    }

    override fun getHiddenItemAlpha(file: BaseFileBean, isDarkMode: Boolean): Float {
        val containIllegalChar = EmojiUtils.containsIllegalCharFileName(file.mDisplayName)
        val canNotSelect = containIllegalChar || file.isDir()
        val needAlpha = choiceMode && canNotSelect
        return HiddenFileHelper.getAlphaWithHidden(needAlpha, isDarkMode)
    }

    override fun setHiddenItemAlpha(file: BaseFileBean, isDarkMode: Boolean) {
        super.setHiddenItemAlpha(file, isDarkMode)
        if (file.mIsDirectory) {
            mCheckBox?.alpha = getHiddenItemAlpha(file, isDarkMode)
        }
    }
}