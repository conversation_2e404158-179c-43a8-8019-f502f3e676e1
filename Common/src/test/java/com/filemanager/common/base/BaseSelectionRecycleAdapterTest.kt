/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BaseSelectionRecycleAdapterTest
 ** Description : BaseSelectionRecycleAdapter Unit Test
 ** Version     : 1.0
 ** Date        : 2022/8/9
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2022/8/9      1.0        create
 ***********************************************************************/
package com.filemanager.common.base

import android.os.Looper
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.utils.Log
import com.filemanager.common.view.SelectItemLayout
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class BaseSelectionRecycleAdapterTest {

    @Before
    fun setup() {
        mockkStatic(Log::class)
    }

    @After
    fun tearDown() {
        unmockkStatic(Log::class)
    }

    @Test
    fun should_when_checkComputingAndExecute() {
        val recyclerView = mockk<RecyclerView>(relaxed = true)
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>>(relaxed = true)
        every { adapter.onAttachedToRecyclerView(any()) }.answers { callOriginal() }
        every { adapter.checkComputingAndExecute(any()) }.answers { callOriginal() }

        adapter.onAttachedToRecyclerView(recyclerView)

        every { recyclerView.isComputingLayout }.returns(true)
        adapter.checkComputingAndExecute {
            adapter.notifyDataSetChanged()
        }
        verify {
            recyclerView.post(any())
        }

        every { recyclerView.isComputingLayout }.returns(false)
        adapter.checkComputingAndExecute {
            adapter.notifyDataSetChanged()
        }
        verify {
            adapter.notifyDataSetChanged()
        }

        every { recyclerView.isComputingLayout }.returns(true)
        adapter.checkComputingAndExecute {
            adapter.notifyDataSetChanged()
        }

        every { recyclerView.isComputingLayout }.returns(false)
        adapter.checkComputingAndExecute {
            adapter.notifyDataSetChanged()
        }
    }

    @Test
    fun `should execute invoke when checkComputingAndExecute no exception`() {
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>>(relaxed = true)
        every { adapter.checkComputingAndExecute(any()) }.answers { callOriginal() }
        justRun { Log.e("BaseSelectionRecycleAdapter", "") }
        adapter.checkComputingAndExecute {
            adapter.notifyDataSetChanged()
        }
        verify {
            adapter.notifyDataSetChanged()
        }
        verify(inverse = true) {
            Log.e("BaseSelectionRecycleAdapter", "")
        }
    }

    @Test
    fun `should not execute invoke when checkComputingAndExecute if throw exception`() {
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>>(relaxed = true)
        every { adapter.checkComputingAndExecute(any()) }.answers { callOriginal() }
        val exception = IllegalStateException()
        justRun { Log.e("BaseSelectionRecycleAdapter", "checkComputingAndExecute UI-Thread exception: ${exception.message}") }
        every { adapter.notifyDataSetChanged() } throws exception
        adapter.checkComputingAndExecute {
            adapter.notifyDataSetChanged()
        }
        verify {
            adapter.notifyDataSetChanged()
        }
        verify {
            Log.e("BaseSelectionRecycleAdapter", "checkComputingAndExecute UI-Thread exception: ${exception.message}")
        }
    }

    @Test
    fun `should execute post when looper is not main looper`() {
        mockkStatic(Looper::class)
        every { Looper.myLooper() } returns mockk()
        val recyclerView = mockk<RecyclerView>(relaxed = true)
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>>(relaxed = true)
        every { adapter.onAttachedToRecyclerView(any()) }.answers { callOriginal() }
        every { adapter.checkComputingAndExecute(any()) }.answers { callOriginal() }

        adapter.onAttachedToRecyclerView(recyclerView)
        every { recyclerView.isComputingLayout }.returns(true)

        adapter.checkComputingAndExecute {
            adapter.notifyDataSetChanged()
        }
        verify {
            recyclerView.post(any())
        }
        unmockkStatic(Looper::class)
    }

    @Test
    fun `should execute post when looper is not main looper if throw exception`() {
        val recyclerView = mockk<RecyclerView>(relaxed = true)
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>>(relaxed = true)
        every { adapter.onAttachedToRecyclerView(any()) }.answers { callOriginal() }
        every { adapter.checkComputingAndExecute(any()) }.answers { callOriginal() }

        val exception = IllegalStateException()
        justRun { Log.e("BaseSelectionRecycleAdapter", "checkComputingAndExecute exception: ${exception.message}") }
        every { adapter.notifyDataSetChanged() } throws exception
        adapter.onAttachedToRecyclerView(recyclerView)
        every { recyclerView.isComputingLayout }.returns(true)
        adapter.checkComputingAndExecute {
            adapter.notifyDataSetChanged()
        }
        verify {
            recyclerView.post(any())
        }
        verify(inverse = true) {
            Log.e("BaseSelectionRecycleAdapter", "checkComputingAndExecute exception: ${exception.message}")
        }
    }

    @Test
    fun `should execute setChecked when updateCheckBoxState if mChoiceMode is false`() {
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>>(relaxed = true)
        val itemLayout = mockk<SelectItemLayout> {
            justRun { setChecked(any()) }
        }
        val checkBox = mockk<COUICheckBox>(relaxed = true) {
            every { parent } returns itemLayout
        }
        every { adapter.updateCheckBoxState(checkBox, any()) } answers { callOriginal() }
        every { adapter.getRootSelectItemLayout(any()) } answers { itemLayout }
        adapter.updateCheckBoxState(checkBox, 0)
        verify { itemLayout.setChecked(false) }
        assertEquals(View.VISIBLE, checkBox.visibility)
        assertEquals(COUICheckBox.SELECT_NONE, checkBox.state)
    }

    @Test
    fun `should execute update check box state when set checkBoxAnim if checkbox Tag is not null`() {
        val isDir = true
        val choiceMode = true
        val position = 1
        val isNeedAnim = true
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>>(relaxed = true)
        val checkBox = mockk<COUICheckBox>(relaxed = true) {
            every { tag } returns true
        }
        justRun { adapter.updateCheckBoxState(checkBox, any()) }
        every { adapter.setCheckBoxAnim(any()) } answers { callOriginal() }
        adapter.setCheckBoxAnim(CheckBoxAnimateInput(isDir, choiceMode, null, checkBox, position, isNeedAnim))
        verify { adapter.updateCheckBoxState(checkBox, position) }
    }

    @Test
    fun `should execute update check box state when set checkBoxAnim if right view tag is not null`() {
        val isDir = true
        val choiceMode = true
        val position = 1
        val isNeedAnim = true
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>>(relaxed = true)
        val checkBox = mockk<COUICheckBox>(relaxed = true) {
            every { tag } returns null
        }
        val rightView = mockk<COUICheckBox>(relaxed = true) {
            every { tag } returns true
        }
        justRun { adapter.updateCheckBoxState(checkBox, any()) }
        every { adapter.setCheckBoxAnim(any()) } answers { callOriginal() }
        adapter.setCheckBoxAnim(CheckBoxAnimateInput(isDir, choiceMode, rightView, checkBox, position, isNeedAnim))
        verify { adapter.updateCheckBoxState(checkBox, position) }
    }

    @Test
    fun `should execute update check box state when set checkBoxAnim if all is false`() {
        val isDir = true
        val choiceMode = false
        val position = 1
        val isNeedAnim = true
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>>(relaxed = true)
        val checkBox = mockk<COUICheckBox>(relaxed = true) {
            every { tag } returns null
        }
        val rightView = mockk<COUICheckBox>(relaxed = true) {
            every { tag } returns null
        }
        justRun { adapter.updateCheckBoxState(checkBox, any()) }
        every { adapter.setCheckBoxAnim(any()) } answers { callOriginal() }
        adapter.setCheckBoxAnim(CheckBoxAnimateInput(isDir, choiceMode, rightView, checkBox, position, isNeedAnim))
        verify { adapter.updateCheckBoxState(checkBox, position) }
    }
}