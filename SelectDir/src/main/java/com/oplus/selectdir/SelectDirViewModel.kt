/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.select
 * * Version     : 1.0
 * * Date        : 2020/7/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir

import androidx.lifecycle.MutableLiveData
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.PathLoadResult
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.interfaces.IRefreshActivityDataForCreateDir
import com.filemanager.common.interfaces.LoadingLoaderListener
import com.filemanager.common.path.FileBrowPathHelper
import com.filemanager.common.path.FilePathHelper
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.createdir.FileActionCreateDir
import com.filemanager.fileoperate.createdir.FileCreateDirObserver
import com.oplus.selectdir.filebrowser.SelectShortcutFolderLoader
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

private const val TAG = "SelectDirViewModel"
private const val SELECT_DIR_LOADER_ID = 99

class SelectDirViewModel : SelectionViewModel<BaseFileBean, BaseUiModel<BaseFileBean>>() {
    var mPathHelp: FilePathHelper? = null

    val mModeState = BaseStateModel(MutableLiveData<Int>(KtConstants.LIST_NORMAL_MODE))
    //In order to distinguish between entering and exiting folders because of the folder animation
    var mIsFolderIn = true
    var mNeedScroll = false

    // Data is one of STATE_START/STATE_DONE/STATE_CANCEL in OnLoaderListener.
    val mPositionModel = MutableLiveData<PositionModel>()
    var mPushPathInfo: FilePathHelper.PathInfo? = null
    val mSelectDirLoaderCallBack = SelectDirCallBack(this)
    private var actionCode = 0

    fun initLoader(loaderController: LoaderController?, path: String, actionCode: Int) {
        this.actionCode = actionCode
        mModeState.listModel.value = if (actionCode == MessageConstant.MSG_ADD_SHORTCUT_FOLDER) {
            KtConstants.LIST_SELECTED_MODE
        } else {
            KtConstants.LIST_NORMAL_MODE
        }
        if (mSelectDirLoaderCallBack.getLoader() == null) {
            mPositionModel.value = PositionModel(path, 0, 0)
            mPathHelp?.pushTo(path)
            loaderController?.initLoader(SELECT_DIR_LOADER_ID, mSelectDirLoaderCallBack)
        } else {
            mPathHelp?.pushTo(path)
            mPositionModel.value = PositionModel(path, 0, 0)
            mSelectDirLoaderCallBack.loadData(path, true)
        }
    }

    fun initPathHelper(currentPath: String) {
        if (mPathHelp == null) {
            mPathHelp = FileBrowPathHelper(currentPath)
        } else if (currentPath.contains(mPathHelp?.getRootPath() ?: "*", true).not()) {
            mPathHelp!!.updateRootPath(currentPath)
        }
    }

    override fun canSelectItem(key: Int, fileBean: BaseFileBean?): Boolean {
        if (actionCode != MessageConstant.MSG_ADD_SHORTCUT_FOLDER) {
            return super.canSelectItem(key, fileBean)
        }
        if (fileBean == null) {
            return false
        }
        // 添加快捷文件夹时，只能选中未添加的文件夹
        return !fileBean.mHasLabel
    }


    /**
     * 获取当前选中的路径集合
     */
    fun getSelectPaths(): List<String> {
        val selectedList = mSelectDirLoaderCallBack.getSelectedList()
        return selectedList.mapNotNull { it.mData }.toList()
    }

    class SelectDirCallBack :
        LoadingLoaderListener<SelectDirViewModel, SelectPathLoader, PathLoadResult<Int, BaseFileBean>> {

        private var mLoadNewPath = true

        /**
         * 选中的列表，key为当前的path, value为选中的文件key+文件内容
         */
        private var selectedListMap = hashMapOf<String, ArrayList<Pair<Int, BaseFileBean>>>()
        private var viewModelRef: WeakReference<SelectDirViewModel>

        constructor(viewModel: SelectDirViewModel) : super(viewModel, viewModel.mDataLoadState) {
            viewModelRef = WeakReference(viewModel)
        }

        internal fun loadData(path: String? = null, loadNewPath: Boolean = false) {
            mLoadNewPath = loadNewPath
            getLoader()?.apply {
                val lastPath = getPath().get(0)
                saveSelectedList(lastPath)
                if (path.isNullOrEmpty().not()) {
                    setPath(path!!)
                }
                forceLoad()
            }
        }

        private fun saveSelectedList(path: String) {
            val viewModel = viewModelRef.get() ?: return
            if (viewModel.actionCode == MessageConstant.MSG_ADD_SHORTCUT_FOLDER) {
                val selectedList = ArrayList<Pair<Int, BaseFileBean>>()
                viewModel.uiState.value?.let {
                    it.selectedList.forEach { key ->
                        val file = it.keyMap[key]
                        if (file != null) {
                            selectedList.add(Pair(key, file))
                        }
                    }
                }
                selectedListMap[path] = selectedList
                Log.d(TAG, "saveSelectedList $path -> ${selectedList.size}")
            }
        }

        /**
         * 添加文件夹路径到选中列表
         * @param currentPath 当前的路径
         * @param selectPath 选中的路径
         */
        fun addSelectedList(currentPath: String, selectPath: String) {
            Log.d(TAG, "addSelectedList $currentPath -> $selectPath")
            val selectedList = selectedListMap.get(currentPath) ?: arrayListOf()
            val fileBean = PathFileWrapper(selectPath)
            val key = getLoader()?.getItemKey(fileBean) ?: -1
            selectedList.add(Pair(key, fileBean))
            selectedListMap.put(currentPath, selectedList)
        }

        private fun getSelectedList(resultMap: HashMap<Int, BaseFileBean>): List<Int> {
            val viewModel = viewModelRef.get() ?: return emptyList()
            val actionCode = viewModel.actionCode
            val selectedList = ArrayList<Int>()
            if (actionCode == MessageConstant.MSG_ADD_SHORTCUT_FOLDER) { // 快捷文件夹需要添加所有选中，包括不是当前界面的
                selectedListMap.values.forEach { value ->
                    val keys = value.map { it.first }
                    selectedList.addAll(keys)
                }
                // 将选中数据中的当前路径下的数据清空，防止出现脏数据
                val path = viewModel.mPositionModel.value?.mCurrentPath ?: ""
                selectedListMap.remove(path)
            } else { // 其他情况只要当前路径下的
                val lastSelectList = viewModel.getSelectionKeyList()
                for (selectedFile in lastSelectList) {
                    if (resultMap.contains(selectedFile)) {
                        selectedList.add(selectedFile)
                    }
                }
            }
            Log.d(TAG, "getSelectedList $actionCode -> ${selectedList.size}")
            return selectedList
        }

        fun getSelectedList(): List<BaseFileBean> {
            val viewModel = viewModelRef.get() ?: return emptyList()
            val actionCode = viewModel.actionCode
            val selectedList = ArrayList<BaseFileBean>()
            // 快捷文件夹需要添加所有选中，selectedListMap只是包括之前选中的
            if (actionCode == MessageConstant.MSG_ADD_SHORTCUT_FOLDER) {
                selectedListMap.values.forEach { value ->
                    val files = value.map { it.second }
                    selectedList.addAll(files)
                }
            }
            // 添加当前选中的
            selectedList.addAll(viewModel.getSelectItems())
            // 有可能会有重复的，添加去重
            val result = selectedList.distinctBy { it.mData }
            Log.d(TAG, "getSelectedList $actionCode -> lasSelect:${selectedList.size} result:${result.size}")
            return result
        }

        internal fun isLoadNewPath() = mLoadNewPath

        override fun onCreateLoader(viewModel: SelectDirViewModel?): SelectPathLoader? {
            return if (viewModel != null) {
                val context = MyApplication.appContext
                val path = viewModel.mPositionModel.value?.mCurrentPath ?: ""
                if (viewModel.actionCode == MessageConstant.MSG_ADD_SHORTCUT_FOLDER) {
                    SelectShortcutFolderLoader(context, path)
                } else {
                    SelectPathLoader(context, path)
                }
            } else null
        }

        override fun onLoadComplete(viewModel: SelectDirViewModel?, data: PathLoadResult<Int, BaseFileBean>?) {
            Log.d(TAG, "onLoadComplete in browser: size=${data?.mResultList?.size}")
            data?.mResultList?.let {
                if (viewModel != null) {
                    viewModel.launch {
                        val selectedList = ArrayList<Int>()
                        withContext(Dispatchers.IO) {
                            val lastSelectList = getSelectedList(data.mResultMap)
                            selectedList.addAll(lastSelectList)
                        }
                        Log.d(TAG, "onLoadComplete selectedList: size=${selectedList.size}")
                        if (viewModel.mPushPathInfo != null) {
                            viewModel.mPositionModel.value?.mCurrentPath?.apply {
                                viewModel.mPathHelp?.push(viewModel.mPushPathInfo)
                            }
                            viewModel.mPushPathInfo = null
                        }
                        viewModel.mUiState.postValue(
                            SelectDirUiModel(
                                it,
                                viewModel.mModeState,
                                selectedList,
                                data.mResultMap,
                                viewModel.mPositionModel
                            )
                        )
                        if (viewModel.mNeedScroll) {
                            viewModel.mPositionModel.postValue(viewModel.mPositionModel.value)
                        }
                    }
                } else {
                    Log.d(TAG, "onLoadComplete viewModel is null")
                }
            }
        }
    }


    fun sortReload() {
        mSelectDirLoaderCallBack.loadData()
    }

    fun createNewFolder(activity: BaseVMActivity, selectPathDialogInterface: SelectPathDialogInterface?, firstVisibleItemPosition: Int, offset: Int) {
        mPositionModel?.value?.mCurrentPath?.let { currentPath ->
            val dialog: COUIBottomSheetDialog? = (selectPathDialogInterface as SelectPathDialogFragment).dialog as? COUIBottomSheetDialog?
            dialog?.setFollowWindowChange(false)
            FileActionCreateDir(activity, PathFileWrapper(currentPath)).execute(object : FileCreateDirObserver(activity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    super.onActionDone(result, data)
                    dialog?.setFollowWindowChange(true)
                    val createdDirPath = data as? String
                    if (result) {
                        (activity as? IRefreshActivityDataForCreateDir)?.onRefreshDataForDir(currentPath)
                        createdDirPath?.let { path ->
                            if (actionCode == MessageConstant.MSG_ADD_SHORTCUT_FOLDER) { // 选中当前目录中的新建文件夹
                                mSelectDirLoaderCallBack.loadData(currentPath, false)
                                mSelectDirLoaderCallBack.addSelectedList(currentPath, path)
                            } else { // 进入新建文件夹的子目录
                                mPositionModel.value?.mCurrentPath = path
                                mIsFolderIn = true
                                mNeedScroll = true
                                resetPushPathInfo()
                                mPushPathInfo = FilePathHelper.PathInfo(path, firstVisibleItemPosition, offset)
                                mSelectDirLoaderCallBack.loadData(path, true)
                            }
                        }
                    }
                }
            })
        }
    }


    fun clickPathBar(index: Int) {
        val pathLeft = mPathHelp?.getPathLeft() ?: index + 1
        val index = pathLeft - index - 1
        val pathInfo = mPathHelp?.setTopPath(index)
        Log.d(TAG, "clickPathBar pathInfo=${pathInfo}")
        pathInfo?.let {
            mPositionModel.value?.mCurrentPath = it.path
            mIsFolderIn = false
            mNeedScroll = true
            resetPushPathInfo()
            mSelectDirLoaderCallBack.loadData(it.path, true)
        }

    }

    fun onItemClick(activity: BaseVMActivity?, position: Int, firstVisibleItemPosition: Int, offset: Int) {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return
        }
        mUiState.value?.mFileList?.let {
            if (position >= it.size) {
                return
            }
            val baseFile = it.get(position)
            Log.d(TAG, "onItemClick baseFile=${baseFile}")
            launch {
                val isExits = baseFile.checkExist()
                if (!isExits) {
                    CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
                    return@launch
                } else {
                    if (activity != null && baseFile != null) {
                        if (baseFile.mIsDirectory) {
                            baseFile.mData?.let {
                                mPositionModel.value?.mCurrentPath = it
                                mIsFolderIn = true
                                mNeedScroll = true
                                resetPushPathInfo()
                                mPushPathInfo = FilePathHelper.PathInfo(it, firstVisibleItemPosition, offset)
                                mSelectDirLoaderCallBack.loadData(it, true)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 判断当前是否处于子目录
     * @return true 表示当前位于子目录
     */
    fun isChildPath(): Boolean {
        return mPathHelp?.hasUp() ?: false
    }

    fun isCurrentDfmRootPath(): Boolean {
        val dfmRootPath = mPathHelp?.getDfmRootPath()
        val currentPath = mPathHelp?.getCurrentShowPath()
        return dfmRootPath != null && dfmRootPath == currentPath
    }

    fun pressBack(): Boolean {
        if (mPathHelp?.pop() != null) {
            val info = mPathHelp?.getTopPathInfo()
            Log.d(TAG, "pressBack() path=${info?.path}")
            info?.path?.let {
                mPositionModel.value?.mCurrentPath = it
                mPositionModel.value?.mPosition = info.position
                mPositionModel.value?.mOffset = info.y
                mIsFolderIn = false
                mNeedScroll = true
                resetPushPathInfo()
                mSelectDirLoaderCallBack.loadData(it, true)
                return true
            }
        } else {
            Log.d(TAG, "pressBack() pop null")
        }
        return false
    }

    /**
     * 判断传入路径是否是根目录
     */
    fun isRootPath(path: String): Boolean {
        return mPathHelp?.isRootPath(path) ?: false
    }

    private fun resetPushPathInfo() {
        mPushPathInfo = null
    }

    private fun reloadCurrentPage() {
        mSelectDirLoaderCallBack.loadData()
    }

    class SelectDirUiModel(
        fileList: List<BaseFileBean>,
        stateModel: BaseStateModel,
        selectedList: ArrayList<Int>,
        keyMap: HashMap<Int, BaseFileBean>,
        var mPositionModel: MutableLiveData<PositionModel>?
    ) : BaseUiModel<BaseFileBean>(fileList, stateModel, selectedList, keyMap)

    data class PositionModel(
            var mCurrentPath: String,
            var mPosition: Int,
            var mOffset: Int
    )

    override fun getRealFileSize(): Int {
        var size = 0
        mUiState.value?.mFileList?.apply {
            for (baseFileBean in this) {
                if (baseFileBean.mFileWrapperLabel == null) {
                    size++
                }
                if (baseFileBean.mFileWrapperViewType == BaseFileBean.TYPE_FILE_AD) {
                    //有广告，实际数量减1
                    size--
                }
            }
        }
        Log.d(TAG, "getRealFileSize $size")
        return size
    }

    override fun loadData() {
        mSelectDirLoaderCallBack.loadData()
    }
}