<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:id="@+id/coordinator">

    <LinearLayout
        android:id="@+id/ll_activity_setting"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/personal_title_margin_top"
        android:orientation="vertical">

        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>

    <com.google.android.material.appbar.COUIDividerAppBarLayout
        android:id="@+id/appbar_layout"
        android:background="?attr/couiColorBackgroundWithCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:orientation="vertical"
        app:layout_behavior="com.filemanager.setting.ui.behavior.SecondaryTitleBehavior"
        app:elevation="@dimen/toolbar_elevation">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            style="@style/COUIToolBarInAppBarLayoutStyle"
            app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

        <View
            android:id="@+id/divider_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/toolbar_divider_height"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="@dimen/common_margin"
            android:layout_marginRight="@dimen/common_margin"
            android:alpha="0"
            android:background="?attr/couiColorDivider"
            android:forceDarkAllowed="false" />

    </com.google.android.material.appbar.COUIDividerAppBarLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>