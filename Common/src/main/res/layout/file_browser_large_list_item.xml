<?xml version="1.0" encoding="utf-8"?>
<com.filemanager.common.view.HoverAwareConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/file_browser_item_list_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentTop="true"
    android:background="@drawable/select_list_item_background_selector"
    android:paddingHorizontal="@dimen/file_list_margin"
    android:forceDarkAllowed="false"
    android:minHeight="?android:attr/listPreferredItemHeightSmall">

    <RelativeLayout
        android:id="@+id/file_list_item_icon_container"
        android:layout_width="@dimen/file_list_frame_size"
        android:layout_height="@dimen/file_list_frame_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginVertical="@dimen/dimen_8dp"
        >

        <com.filemanager.common.view.FileThumbView
            android:id="@+id/file_list_item_icon"
            android:layout_width="@dimen/file_list_video_image_size"
            android:layout_height="@dimen/file_list_video_image_size"
            android:scaleType="fitCenter"
            android:layout_centerInParent="true"
            android:forceDarkAllowed="false" />
    </RelativeLayout>

    <ImageView
        android:id="@+id/apk_icon"
        android:layout_width="@dimen/dimen_16dp"
        android:layout_height="@dimen/dimen_16dp"
        android:layout_marginBottom="@dimen/dimen_13dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/file_list_item_icon_container"
        app:layout_constraintEnd_toEndOf="@id/file_list_item_icon_container"
        app:layout_constraintStart_toStartOf="@id/file_list_item_icon_container"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/file_duration_tv"
        android:layout_width="@dimen/dimen_36dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dimen_4dp"
        android:background="@drawable/item_video_duration_view_bg"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:textColor="@color/coui_color_label_on_color"
        android:textSize="@dimen/duration_text_size"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/file_list_item_icon_container"
        app:layout_constraintEnd_toEndOf="@+id/file_list_item_icon_container"
        app:layout_constraintStart_toStartOf="@+id/file_list_item_icon_container" />

    <RelativeLayout
        android:id="@+id/rl_item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/file_list_item_info_margin"
        android:layout_marginTop="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginEnd="@dimen/dimen_24dp"
        android:layout_marginBottom="@dimen/file_list_right_layout_margin_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/file_type_text"
        app:layout_constraintStart_toEndOf="@+id/file_list_item_icon_container"
        app:layout_constraintTop_toTopOf="parent">

        <com.filemanager.common.view.TextViewSnippet
            android:id="@+id/file_list_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:includeFontPadding="false"
            android:maxLines="2"
            android:ellipsize="middle"
            android:textAppearance="?attr/couiTextHeadlineXS"
            android:textColor="?attr/couiColorLabelPrimary"
            android:textSize="@dimen/file_list_item_title_text_size" />

        <TextView
            android:id="@+id/file_list_item_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/file_list_item_title"
            android:layout_marginTop="@dimen/file_list_item_detail_margin_top"
            android:adjustViewBounds="true"
            android:ellipsize="marquee"
            android:singleLine="true"
            android:includeFontPadding="false"
            android:textColor="?attr/couiColorLabelSecondary"
            android:textSize="@dimen/file_list_item_detail_text_size" />

        <com.filemanager.common.view.TextViewSnippet
            android:id="@+id/another_name_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/file_list_item_title"
            android:ellipsize="end"
            android:paddingTop="@dimen/file_list_item_another_name_padding_top"
            android:singleLine="true"
            android:includeFontPadding="false"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textSize="@dimen/file_list_item_detail_text_size" />
    </RelativeLayout>

    <TextView
        android:id="@+id/file_type_text"
        android:layout_width="@dimen/dimen_120dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_8dp"
        android:adjustViewBounds="true"
        android:ellipsize="end"
        android:singleLine="true"
        android:textAlignment="textStart"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textAppearance="@style/couiTextBodyM"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/file_size_text"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="ppt文档" />

    <TextView
        android:id="@+id/file_size_text"
        android:layout_width="@dimen/dimen_64dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_8dp"
        android:adjustViewBounds="true"
        android:ellipsize="end"
        android:singleLine="true"
        android:textAlignment="textStart"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textAppearance="@style/couiTextBodyM"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/file_time_text"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="9.8M" />


    <TextView
        android:id="@+id/file_time_text"
        android:layout_width="@dimen/dimen_120dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_8dp"
        android:adjustViewBounds="true"
        android:ellipsize="end"
        android:singleLine="true"
        android:textAlignment="textEnd"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textAppearance="@style/couiTextBodyM"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="2024年10月23日" />


    <com.coui.appcompat.checkbox.COUICheckBox
        android:id="@+id/listview_scrollchoice_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:clickable="false"
        android:focusable="false"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/divider_line"
        android:layout_width="0dp"
        android:layout_height="0.33dp"
        android:background="?attr/couiColorDivider"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/rl_item_title" />

</com.filemanager.common.view.HoverAwareConstraintLayout>