package com.coloros.filemanager.utils

import android.content.Context
import androidx.core.content.ContextCompat
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.KtThumbnailHelper
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.Assert
import org.junit.Test

class KtThumbnailHelperTest {

    private var context: Context = mockk()

    @Test
    fun should_return_drawable_when_getClassifyDrawable_with_type(){
        mockkStatic(ContextCompat::class)
        every { ContextCompat.getDrawable(any(), any()) } returns mockk()

        Assert.assertNull(KtThumbnailHelper.getClassifyDrawable(null, MimeTypeHelper.UNKNOWN_TYPE))

        val types = arrayOf(MimeTypeHelper.DIRECTORY_TYPE,MimeTypeHelper.AUDIO_TYPE,MimeTypeHelper.VIDEO_TYPE,
            MimeTypeHelper.IMAGE_TYPE,MimeTypeHelper.HTML_TYPE,MimeTypeHelper.TXT_TYPE,
            MimeTypeHelper.APPLICATION_TYPE,MimeTypeHelper.CHM_TYPE, MimeTypeHelper.EPUB_TYPE, MimeTypeHelper.EBK_TYPE,
            MimeTypeHelper.DOC_TYPE, MimeTypeHelper.DOCX_TYPE,MimeTypeHelper.XLS_TYPE, MimeTypeHelper.XLSX_TYPE,
            MimeTypeHelper.PPT_TYPE, MimeTypeHelper.PPTX_TYPE,MimeTypeHelper.PDF_TYPE, MimeTypeHelper.OFD_TYPE,
            MimeTypeHelper.VMSG_TYPE, MimeTypeHelper.VCF_TYPE, MimeTypeHelper.CSV_TYPE,MimeTypeHelper.THEME_TYPE,
            MimeTypeHelper.ICS_TYPE, MimeTypeHelper.VCS_TYPE,MimeTypeHelper.LRC_TYPE,MimeTypeHelper.JAR_TYPE,
            MimeTypeHelper.RAR_TYPE,MimeTypeHelper.ZIP_TYPE, MimeTypeHelper.P7ZIP_TYPE, MimeTypeHelper.COMPRESSED_TYPE,MimeTypeHelper.TORRENT_TYPE,
            MimeTypeHelper.UNKNOWN_TYPE
        )
        for (type in types){
            Assert.assertNotNull(KtThumbnailHelper.getClassifyDrawable(context, type))
            Assert.assertNotNull(KtThumbnailHelper.getClassifyDrawable(context, type, true))
        }

        unmockkStatic(ContextCompat::class)
    }

}