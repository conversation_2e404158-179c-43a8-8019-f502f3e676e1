package com.oplus.filemanager.parentchild.adapter

import android.content.Context
import android.graphics.drawable.Drawable
import android.view.View
import androidx.activity.ComponentActivity
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.RemoteDeviceConstants
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper
import com.oplus.filemanager.parentchild.viewholder.BaseOptionHolder
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * SideRemoteDeviceListAdapter的单元测试类
 * 用于测试SideRemoteDeviceListAdapter的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29], application = android.app.Application::class)
class SideRemoteDeviceListAdapterTest {

    // 定义测试所需的mock对象
    private lateinit var adapter: SideRemoteDeviceListAdapter
    private lateinit var mockContext: Context
    private lateinit var mockActivity: ComponentActivity
    private lateinit var mockAnimationHelper: ItemAnimationHelper
    private lateinit var mockCallback: (View, CategoryListBean) -> Unit
    private lateinit var mockHolder: BaseOptionHolder
    private lateinit var mockDrawable: Drawable

    /**
     * 测试前的准备工作
     * 初始化所有mock对象和测试环境
     */
    @Before
    fun setup() {
        // 创建各种mock对象
        mockContext = mockk(relaxed = true)
        mockActivity = mockk(relaxed = true)
        mockAnimationHelper = mockk(relaxed = true)
        mockCallback = mockk(relaxed = true)
        mockHolder = mockk(relaxed = true, moreInterfaces = arrayOf(ItemAnimationHelper.AnimationListener::class))
        mockDrawable = mockk(relaxed = true)

        // mock MyApplication的单例对象
        mockkObject(MyApplication)
        every { MyApplication.appContext } returns mockContext

        // mock静态方法和工具类
        mockkStatic(Utils::class)
        mockkStatic(ContextCompat::class)
        every { ContextCompat.getDrawable(any(), any()) } returns mockDrawable
        
        // mock COUIContextUtil的静态方法
        mockkStatic(com.coui.appcompat.contextutil.COUIContextUtil::class)
        every { com.coui.appcompat.contextutil.COUIContextUtil.getAttrColor(any(), any()) } returns 0

        // 设置RTL布局状态为false(默认LTR)
        every { Utils.isRtl() } returns false

        // 初始化待测试的adapter
        adapter = SideRemoteDeviceListAdapter(
            mockContext,
            mockActivity,
            mockAnimationHelper,
            mockCallback
        )
    }

    /**
     * 测试后的清理工作
     * 清除所有mock对象
     */
    @After
    fun tearDown() {
        clearAllMocks()
        unmockkAll()
    }

    /**
     * 测试getItemCount方法
     * 验证返回的数据项数量是否正确
     */
    @Test
    fun `getItemCount should return correct size`() {
        // Given - 准备测试数据
        val testData = listOf(
            CategoryListBean(1, 1).apply { deviceStatus = RemoteDeviceConstants.UNDISCOVERED },
            CategoryListBean(2, 2).apply { deviceStatus = RemoteDeviceConstants.DISCOVERED }
        )
        adapter.setData(testData.toMutableList())

        // When - 调用待测试方法
        val count = adapter.itemCount

        // Then - 验证结果
        assertEquals(2, count)
    }

    /**
     * 测试setData方法
     * 验证数据设置和通知更新的功能
     */
    @Test
    fun `setData should update list and notify changes`() {
        // Given - 准备测试数据
        val testData = listOf(
            CategoryListBean(1, 1).apply { deviceStatus = RemoteDeviceConstants.CONNECTED }
        )

        // When - 调用待测试方法
        adapter.setData(testData.toMutableList())

        // Then - 验证数据是否正确更新
        assertEquals(1, adapter.getData().size)
        assertEquals(RemoteDeviceConstants.CONNECTED, adapter.getData()[0].deviceStatus)
    }

    /**
     * 测试onBindViewHolder方法
     * 验证设备名称颜色根据状态更新的功能
     */
    @Test
    fun `onBindViewHolder should update device name color based on status`() {
        // Given - 准备测试数据
        val testData = CategoryListBean(1, 1).apply {
            deviceStatus = RemoteDeviceConstants.UNDISCOVERED
            name = "TestDevice"
        }
        adapter.setData(mutableListOf(testData))

        // When - 调用待测试方法
        adapter.onBindViewHolder(mockHolder, 0)

        // Then - 验证方法调用和参数
        verify { mockHolder.optionTitle.setTextColor(any<Int>()) }
        verify { mockHolder.bindData(testData, mockAnimationHelper.isEdit, mockCallback) }
    }

    /**
     * 测试onViewAttachedToWindow方法
     * 验证视图附加到窗口时是否正确添加动画监听器
     */
    @Test
    fun `onViewAttachedToWindow should add animation listener`() {
        // Given - 设置mock行为
        every { mockAnimationHelper.isEdit } returns true

        // When - 调用待测试方法
        adapter.onViewAttachedToWindow(mockHolder)

        // Then - 验证监听器是否被正确添加
        verify { mockAnimationHelper.addListener(mockHolder as ItemAnimationHelper.AnimationListener, true) }
    }

    /**
     * 测试onViewRecycled方法
     * 验证视图回收时是否正确移除动画监听器
     */
    @Test
    fun `onViewRecycled should remove animation listener`() {
        // When - 调用待测试方法
        adapter.onViewRecycled(mockHolder)

        // Then - 验证监听器是否被正确移除
        verify { mockAnimationHelper.removeListener(mockHolder as ItemAnimationHelper.AnimationListener) }
    }

    /**
     * 测试getItemViewType方法
     * 验证返回的视图类型是否正确
     */
    @Test
    fun `getItemViewType should return correct layout type`() {
        // Given - 准备测试数据
        val testData = CategoryListBean(1, 99).apply { 
            deviceStatus = RemoteDeviceConstants.DISCOVERED 
        }
        adapter.setData(mutableListOf(testData))

        // When - 调用待测试方法
        val viewType = adapter.getItemViewType(0)

        // Then - 验证返回的视图类型
        assertEquals(99, viewType)
    }

    /**
     * 测试updateDeviceStatus方法
     * 验证RTL布局下的图标位置是否正确
     */
    @Test
    fun `updateDeviceStatus should handle RTL layout correctly`() {
        // Given - 准备测试数据并设置RTL布局
        val testData = CategoryListBean(1, 1).apply {
            deviceStatus = RemoteDeviceConstants.CONNECTED
        }
        adapter.setData(mutableListOf(testData))
        every { Utils.isRtl() } returns true

        // When - 调用待测试方法
        adapter.onBindViewHolder(mockHolder, 0)

        // Then - 验证图标位置是否正确(右侧)
        verify { 
            mockHolder.optionSubtitle.setCompoundDrawablesWithIntrinsicBounds(null, null, mockDrawable, null) 
        }
    }

    /**
     * 测试updateDeviceStatus方法
     * 验证LTR布局下的图标位置是否正确
     */
    @Test
    fun `updateDeviceStatus should handle LTR layout correctly`() {
        // Given - 准备测试数据并设置LTR布局
        val testData = CategoryListBean(1, 1).apply {
            deviceStatus = RemoteDeviceConstants.CONNECTED
        }
        adapter.setData(mutableListOf(testData))
        every { Utils.isRtl() } returns false

        // When - 调用待测试方法
        adapter.onBindViewHolder(mockHolder, 0)

        // Then - 验证图标位置是否正确(左侧)
        verify { 
            mockHolder.optionSubtitle.setCompoundDrawablesWithIntrinsicBounds(mockDrawable, null, null, null) 
        }
    }
}