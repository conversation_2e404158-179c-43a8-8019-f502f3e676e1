/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AudioHeapUpTarget.kt
 * Description:
 *     The audio file implementation about receive loaded preview image from loader
 *
 * Version: 1.0
 * Date: 2024-10-23
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-10-23   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection.loader

import android.content.Context
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import com.filemanager.common.base.BaseFileBean
import com.filemanager.thumbnail.audio.AudioThumbnailResult
import com.oplus.filemanager.preview.selection.data.HeadUpImageData

internal class AudioHeapUpTarget(
    context: Context,
    fileBean: BaseFileBean,
    defaultPlaceholderRes: Int,
    isAlwaysIcon: Boolean = false
) : AbsHeapUpImageTarget<AudioThumbnailResult>(
    context,
    fileBean,
    defaultPlaceholderRes,
    isAlwaysIcon
) {

    private companion object {
        private const val TAG = "AudioHeapUpTarget"
    }

    override val logTag: String = TAG

    override fun obtainResourceDrawable(resource: AudioThumbnailResult): Drawable? {
        val bitmap = resource.mBitmap ?: return null
        if (bitmap.isRecycled) {
            return null
        }
        return BitmapDrawable(context.resources, bitmap)
    }

    override fun obtainResourceSizeInfo(
        resource: AudioThumbnailResult
    ): HeadUpImageData.ImageSizeInfo? = resource.mBitmap?.run {
        HeadUpImageData.ImageSizeInfo(width, height)
    }
}