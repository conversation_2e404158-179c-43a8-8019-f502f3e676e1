package com.oplus.filemanager.category.globalsearch.bean

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import com.filemanager.common.MyApplication
import com.filemanager.common.base.HighLightEntity
import com.filemanager.common.base.SearchDMPFileWrapper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.GsonUtil
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.category.globalsearch.provider.DFMProvider
import com.oplus.filemanager.category.globalsearch.ui.loader.GlobalSearchDFMLoader
import com.oplus.filemanager.dfm.DFMManager
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * SearchDFMFileWrapper的单元测试类
 * 用于测试SearchDFMFileWrapper类的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SearchDFMFileWrapperTest {

    // 模拟的Cursor对象
    private lateinit var cursor: Cursor
    // 模拟的Context对象
    private lateinit var context: Context
    // 模拟的PathFileWrapper对象
    private lateinit var pathFileWrapper: PathFileWrapper

    /**
     * 测试前的初始化方法
     * 用于创建模拟对象和设置初始状态
     */
    @Before
    fun setUp() {
        // 创建模拟对象
        cursor = mockk()
        context = mockk()
        pathFileWrapper = mockk()
        
        // 模拟静态对象
        mockkObject(MyApplication)
        mockkObject(DFMManager)
        mockkObject(FileTimeUtil)
        mockkObject(GsonUtil)
        
        // 设置MyApplication的模拟返回值
        every { MyApplication.sAppContext } returns context
    }

    /**
     * 测试后的清理方法
     * 用于释放资源和取消模拟
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试isDmp方法
     * 验证当dataType为DMP类型时返回true，否则返回false
     */
    @Test
    fun testIsDmp() {
        val wrapper = SearchDFMFileWrapper(cursor, null)
        // 设置dataType为DMP类型
        wrapper.dataType = DFMProvider.DATA_TYPE_DMP
        assertTrue(wrapper.isDmp())
        
        // 设置dataType为MEDIA类型
        wrapper.dataType = DFMProvider.DATA_TYPE_MEDIA
        assertFalse(wrapper.isDmp())
    }

    /**
     * 测试isDmpMedia方法
     * 验证当dataType为MEDIA类型时返回true，否则返回false
     */
    @Test
    fun testIsDmpMedia() {
        val wrapper = SearchDFMFileWrapper(cursor, null)
        // 设置dataType为MEDIA类型
        wrapper.dataType = DFMProvider.DATA_TYPE_MEDIA
        assertTrue(wrapper.isDmpMedia())
        
        // 设置dataType为DMP类型
        wrapper.dataType = DFMProvider.DATA_TYPE_DMP
        assertFalse(wrapper.isDmpMedia())
    }

    /**
     * 测试checkFileUri方法
     * 验证当uri为老版本uri时返回true，否则返回false
     */
    @Test
    fun testCheckFileUri() {
        val wrapper = SearchDFMFileWrapper(cursor, null)
        // 创建老版本uri
        val oldUri = Uri.parse(GlobalSearchDFMLoader.DFS_MEDIA_PROVIDER)
        assertTrue(wrapper.checkFileUri(oldUri))
        
        // 创建新版本uri
        val newUri = Uri.parse(GlobalSearchDFMLoader.DFS_DMP_PROVIDER)
        assertFalse(wrapper.checkFileUri(newUri))
    }

    /**
     * 测试checkDmpUri方法
     * 验证当uri为新版本DMP或FILE_MEDIA uri时返回true，否则返回false
     */
    @Test
    fun testCheckDmpUri() {
        val wrapper = SearchDFMFileWrapper(cursor, null)
        // 创建DMP uri
        val dmpUri = Uri.parse(GlobalSearchDFMLoader.DFS_DMP_PROVIDER)
        assertTrue(wrapper.checkDmpUri(dmpUri))
        
        // 创建FILE_MEDIA uri
        val fileMediaUri = Uri.parse(GlobalSearchDFMLoader.DFS_FILE_MEDIA_PROVIDER)
        assertTrue(wrapper.checkDmpUri(fileMediaUri))
        
        // 创建老版本uri
        val oldUri = Uri.parse(GlobalSearchDFMLoader.DFS_MEDIA_PROVIDER)
        assertFalse(wrapper.checkDmpUri(oldUri))
    }
}