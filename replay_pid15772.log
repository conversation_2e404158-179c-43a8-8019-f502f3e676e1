JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 0
JvmtiExport can_post_on_exceptions 0
# 241 ciObject found
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/IReparseableElementTypeBase
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/ICompositeElementType
instanceKlass org/jetbrains/kotlin/KtNodeTypes
instanceKlass org/jetbrains/kotlin/com/intellij/lang/PsiParser
instanceKlass org/jetbrains/kotlin/kdoc/lexer/KDocTokens
instanceKlass org/jetbrains/kotlin/lexer/KtTokens
instanceKlass org/jetbrains/kotlin/diagnostics/PositioningStrategy
instanceKlass org/jetbrains/kotlin/diagnostics/PositioningStrategies
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticFactory$Companion
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticMarker
instanceKlass org/jetbrains/kotlin/diagnostics/UnboundDiagnostic
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticFactory
instanceKlass org/jetbrains/kotlin/cli/common/messages/AnalyzerWithCompilerReport$Companion
instanceKlass org/jetbrains/kotlin/cli/common/messages/AnalyzerWithCompilerReport
instanceKlass org/jetbrains/kotlin/analyzer/AbstractAnalyzerWithCompilerReport
instanceKlass org/jetbrains/kotlin/load/kotlin/ModuleVisibilityManager$SERVICE
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinToJVMBytecodeCompiler
instanceKlass org/jetbrains/kotlin/com/intellij/injected/editor/DocumentWindow
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/impl/FrozenDocument
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/ByteArrayCharSequence
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/impl/DocumentImpl$1
instanceKlass java/beans/PropertyChangeListener
instanceKlass java/beans/ChangeListenerMap
instanceKlass java/beans/PropertyChangeSupport
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/impl/IntervalTreeImpl$IntervalTreeGuide
instanceKlass org/jetbrains/kotlin/com/intellij/util/WalkingState$TreeGuide
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/longs/LongSet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/longs/LongCollection
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/longs/LongIterable
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/impl/RedBlackTree$Node
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/ex/PrioritizedDocumentListener$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/ex/PrioritizedDocumentListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/ex/DocumentEx$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileEditor/impl/LoadTextUtil$ConvertResult
instanceKlass sun/nio/cs/ThreadLocalCoders$Cache
instanceKlass sun/nio/cs/ThreadLocalCoders
instanceKlass org/jetbrains/kotlin/com/intellij/util/keyFMap/ArrayBackedFMap
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileEditor/impl/LoadTextUtil$DetectResult
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/CharsetToolkit
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileEditor/impl/LoadTextUtil
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/JavaLanguageLevelKt
instanceKlass org/jetbrains/kotlin/config/AppendJavaSourceRootsHandlerKeyKt
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/CliVirtualFileFinderFactory
instanceKlass org/jetbrains/kotlin/load/kotlin/VirtualFileFinderFactory
instanceKlass org/jetbrains/kotlin/load/kotlin/MetadataFinderFactory
instanceKlass org/jetbrains/kotlin/cli/jvm/modules/CliJavaModuleResolver$Companion
instanceKlass org/jetbrains/kotlin/cli/jvm/modules/CliJavaModuleResolver
instanceKlass org/jetbrains/kotlin/resolve/jvm/modules/JavaModuleResolver
instanceKlass org/jetbrains/kotlin/load/java/JavaModuleAnnotationsProvider
instanceKlass org/jetbrains/kotlin/cli/jvm/index/SingleJavaFileRootsIndex
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JvmDependenciesIndexImpl
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock
instanceKlass java/util/concurrent/locks/ReadWriteLock
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JvmDependenciesDynamicCompoundIndex
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JvmDependenciesIndex
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/ClasspathRootsResolver$RootsAndModules
instanceKlass kotlin/sequences/TransformingSequence$iterator$1
instanceKlass kotlin/sequences/FilteringSequence$iterator$1
instanceKlass kotlin/jvm/internal/ArrayIterator
instanceKlass kotlin/jvm/internal/ArrayIteratorKt
instanceKlass kotlin/sequences/FlatteningSequence$iterator$1
instanceKlass kotlin/sequences/FlatteningSequence
instanceKlass kotlin/collections/ArraysKt___ArraysKt$asSequence$$inlined$Sequence$1
instanceKlass kotlin/collections/CollectionsKt___CollectionsKt$asSequence$$inlined$Sequence$1
instanceKlass kotlin/sequences/FilteringSequence
instanceKlass kotlin/sequences/TransformingSequence
instanceKlass kotlin/sequences/EmptySequence
instanceKlass kotlin/sequences/DropTakeSequence
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JavaRoot$RootTypes
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JavaRoot
instanceKlass java/io/RandomAccessFile$1
instanceKlass org/jetbrains/kotlin/cli/jvm/config/JavaSourceRoot
instanceKlass java/util/AbstractMap$SimpleEntry
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/FactoryMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/CharArrayExternalizable
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/ImmutableCharSequence
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/CharArrayUtil
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileUtil$2
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileUtil$1
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/CaseInsensitiveStringHashingStrategy
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileUtilRt$SymlinkResolver
instanceKlass java/util/zip/ZipFile$ZipEntryIterator
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ShareableKey
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/hash/LinkedHashMap$Entry
instanceKlass java/util/DualPivotQuicksort
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/hash/HashUtil
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/SLRUMap
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileAttributes
instanceKlass org/jetbrains/kotlin/com/intellij/util/io/ResourceHandle
instanceKlass org/jetbrains/kotlin/com/intellij/util/io/FileAccessorCache
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/hash/EqualityPolicy
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/AddonlyKeylessHash$KeyValueMapper
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Pair
instanceKlass org/jetbrains/kotlin/cli/jvm/modules/JavaModuleGraph
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/ClasspathRootsResolver$Companion
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/ClasspathRootsResolver
instanceKlass org/jetbrains/kotlin/cli/jvm/modules/CliJavaModuleFinder
instanceKlass org/jetbrains/kotlin/resolve/jvm/modules/JavaModuleFinder
instanceKlass org/jetbrains/kotlin/psi/UserDataProperty
instanceKlass kotlin/reflect/jvm/internal/KPropertyImpl$Companion
instanceKlass kotlin/reflect/jvm/internal/KCallableImpl
instanceKlass kotlin/reflect/KMutableProperty$Setter
instanceKlass org/jetbrains/kotlin/resolve/multiplatform/IsCommonSourceKt
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/PsiUtilCore$NullPsiElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/PsiUtilCore
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/FileTrees
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinScriptStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinPlaceHolderWithTextStub
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/IReparseableLeafElementType
instanceKlass org/jetbrains/kotlin/com/intellij/psi/TokenType
instanceKlass org/jetbrains/kotlin/com/intellij/psi/LiteralTextEscaper
instanceKlass org/jetbrains/kotlin/com/intellij/psi/ContributedReferenceHost
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinCollectionLiteralExpressionStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinClassLiteralExpressionStub
instanceKlass org/jetbrains/kotlin/psi/KtDoubleColonExpression
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinConstantExpressionStub
instanceKlass org/jetbrains/kotlin/psi/stubs/elements/KtConstantExpressionElementType$Companion
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinContextReceiverStub
instanceKlass org/jetbrains/kotlin/psi/LambdaArgument
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinContractEffectStub
instanceKlass org/jetbrains/kotlin/psi/ValueArgumentName
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinValueArgumentStub
instanceKlass org/jetbrains/kotlin/psi/ValueArgument
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinEnumEntrySuperclassReferenceExpressionStub
instanceKlass org/jetbrains/kotlin/psi/KtQualifiedExpression
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinNameReferenceExpressionStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinTypeProjectionStub
instanceKlass org/jetbrains/kotlin/psi/KtSimpleNameExpression
instanceKlass org/jetbrains/kotlin/psi/KtReferenceExpression
instanceKlass org/jetbrains/kotlin/psi/stubs/impl/KotlinTypeBean
instanceKlass org/jetbrains/kotlin/types/model/KotlinTypeMarker
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinUserTypeStub
instanceKlass org/jetbrains/kotlin/psi/KtTypeElement
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinModifierListStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinImportAliasStub
instanceKlass org/jetbrains/kotlin/psi/KtImportInfo$ImportContent
instanceKlass org/jetbrains/kotlin/psi/KtImportInfo
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinImportDirectiveStub
instanceKlass org/jetbrains/kotlin/psi/KtAnnotationsContainer
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinAnnotationUseSiteTargetStub
instanceKlass org/jetbrains/kotlin/psi/KtCallElement
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinAnnotationEntryStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinTypeParameterStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinParameterStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinConstructorStub
instanceKlass org/jetbrains/kotlin/psi/KtAnonymousInitializer
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinPlaceHolderStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinObjectStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinTypeAliasStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinBackingFieldStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinPropertyAccessorStub
instanceKlass org/jetbrains/kotlin/psi/KtVariableDeclaration
instanceKlass org/jetbrains/kotlin/psi/KtValVarKeywordOwner
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinPropertyStub
instanceKlass org/jetbrains/kotlin/psi/KtFunction
instanceKlass org/jetbrains/kotlin/psi/KtDeclarationWithBody
instanceKlass org/jetbrains/kotlin/psi/KtCallableDeclaration
instanceKlass org/jetbrains/kotlin/psi/KtDeclarationWithInitializer
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinFunctionStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinCallableStubBase
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/SubstrateRef
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinClassStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinClassOrObjectStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinStubWithFqName
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/NamedStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinClassifierStub
instanceKlass org/jetbrains/kotlin/com/intellij/psi/StubBasedPsiElement
instanceKlass org/jetbrains/kotlin/psi/KtTypeParameterListOwner
instanceKlass org/jetbrains/kotlin/psi/KtPureClassOrObject
instanceKlass org/jetbrains/kotlin/psi/KtClassLikeDeclaration
instanceKlass org/jetbrains/kotlin/psi/KtNamedDeclaration
instanceKlass org/jetbrains/kotlin/psi/KtStatementExpression
instanceKlass org/jetbrains/kotlin/psi/KtNamed
instanceKlass org/jetbrains/kotlin/psi/KtDeclaration
instanceKlass org/jetbrains/kotlin/psi/KtModifierListOwner
instanceKlass org/jetbrains/kotlin/psi/KtExpression
instanceKlass org/jetbrains/kotlin/psi/stubs/elements/KtStubElementTypes
instanceKlass org/jetbrains/kotlin/psi/stubs/elements/KtTokenSets
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/TokenSet
instanceKlass org/jetbrains/kotlin/psi/KtFile$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/StubbedSpine
instanceKlass org/jetbrains/kotlin/psi/KtDeclarationContainer
instanceKlass org/jetbrains/kotlin/psi/KtAnnotated
instanceKlass org/jetbrains/kotlin/psi/KtElement
instanceKlass org/jetbrains/kotlin/psi/KtPureElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiModifiableCodeBlock
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiClassOwner
instanceKlass org/jetbrains/kotlin/com/intellij/util/keyFMap/PairElementsFMap
instanceKlass java/util/function/UnaryOperator
instanceKlass org/jetbrains/kotlin/com/intellij/psi/AbstractFileViewProvider$VirtualFileContent
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiLock
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiLargeBinaryFile
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiLargeFile
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiBinaryFile
instanceKlass org/jetbrains/kotlin/com/intellij/psi/AbstractFileViewProvider$Content
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ContainerUtilRt
instanceKlass org/jetbrains/kotlin/com/intellij/model/ModelBranch
instanceKlass org/jetbrains/kotlin/com/intellij/injected/editor/VirtualFileWindow
instanceKlass kotlin/collections/AbstractIterator$WhenMappings
instanceKlass kotlin/io/FileTreeWalk$WalkState
instanceKlass kotlin/collections/AbstractIterator
instanceKlass kotlin/io/FileTreeWalk
instanceKlass kotlin/io/FilePathComponents
instanceKlass kotlin/io/FilesKt__FilePathComponentsKt
instanceKlass org/jetbrains/kotlin/extensions/PreprocessedFileCreator
instanceKlass java/util/AbstractList$Itr
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/KeyedExtensionCollector$MyExtensionPointListener
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/CoreEnvironmentUtilsKt
instanceKlass org/jetbrains/kotlin/resolve/lazy/declarations/DeclarationProviderFactoryService$Companion
instanceKlass org/jetbrains/kotlin/resolve/lazy/declarations/DeclarationProviderFactoryService
instanceKlass org/jetbrains/kotlin/com/intellij/diagnostic/Activity
instanceKlass org/jetbrains/kotlin/com/intellij/diagnostic/StartUpMeasurer
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/CliModuleAnnotationsResolver
instanceKlass org/jetbrains/kotlin/resolve/ModuleAnnotationsResolver
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinJavaPsiFacade$2
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinJavaPsiFacade$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiModifierList
instanceKlass org/jetbrains/kotlin/resolve/jvm/NotFoundPackagesCachingStrategy$Default
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/ElementBase$ElementIconRequest
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaClass
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaTypeParameterListOwner
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaModifierListOwner
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaClassifier
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaNamedElement
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaAnnotationOwner
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaElement
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinJavaPsiFacade$KotlinPsiElementFinderWrapperImpl
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinJavaPsiFacade$KotlinPsiElementFinderWrapper
instanceKlass org/jetbrains/kotlin/resolve/jvm/NotFoundPackagesCachingStrategy
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinJavaPsiFacade
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/source/JavaElementSourceFactory
instanceKlass org/jetbrains/kotlin/cli/common/CliModuleVisibilityManagerImpl
instanceKlass org/jetbrains/kotlin/load/kotlin/ModuleVisibilityManager
instanceKlass org/jetbrains/kotlin/compiler/plugin/CompilerPluginRegistrarKt
instanceKlass org/jetbrains/kotlin/compiler/plugin/CompilerPluginRegistrar$ExtensionStorage
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass org/jetbrains/kotlin/fir/extensions/FirAnalysisHandlerExtension
instanceKlass org/jetbrains/kotlin/resolve/extensions/AssignResolutionAltererExtension
instanceKlass org/jetbrains/kotlin/extensions/AnnotationBasedExtension
instanceKlass org/jetbrains/kotlin/types/DefaultTypeAttributeTranslator
instanceKlass org/jetbrains/kotlin/types/extensions/TypeAttributeTranslators
instanceKlass org/jetbrains/kotlin/extensions/TypeAttributeTranslatorExtension
instanceKlass org/jetbrains/kotlin/types/TypeAttributeTranslator
instanceKlass org/jetbrains/kotlin/fir/extensions/FirExtensionRegistrarAdapter
instanceKlass org/jetbrains/kotlin/serialization/DescriptorSerializerPlugin
instanceKlass org/jetbrains/kotlin/extensions/internal/CallResolutionInterceptorExtension
instanceKlass org/jetbrains/kotlin/extensions/internal/CandidateInterceptor
instanceKlass org/jetbrains/kotlin/extensions/internal/TypeResolutionInterceptorExtension
instanceKlass org/jetbrains/kotlin/extensions/internal/TypeResolutionInterceptor
instanceKlass org/jetbrains/kotlin/cli/common/extensions/ShellExtension
instanceKlass org/jetbrains/kotlin/cli/common/extensions/ScriptEvaluationExtension
instanceKlass org/jetbrains/kotlin/backend/common/extensions/IrGenerationExtension
instanceKlass org/jetbrains/kotlin/ir/linkage/IrDeserializer$IrLinkerExtension
instanceKlass org/jetbrains/kotlin/resolve/extensions/ExtraImportsProviderExtension
instanceKlass org/jetbrains/kotlin/extensions/ProcessSourcesBeforeCompilingExtension
instanceKlass org/jetbrains/kotlin/extensions/CollectAdditionalSourcesExtension
instanceKlass org/jetbrains/kotlin/extensions/CompilerConfigurationExtension
instanceKlass org/jetbrains/kotlin/js/translate/extensions/JsSyntheticTranslateExtension
instanceKlass org/jetbrains/kotlin/extensions/PreprocessedVirtualFileFactoryExtension
instanceKlass org/jetbrains/kotlin/extensions/DeclarationAttributeAltererExtension
instanceKlass org/jetbrains/kotlin/extensions/StorageComponentContainerContributor
instanceKlass org/jetbrains/kotlin/resolve/jvm/extensions/PackageFragmentProviderExtension
instanceKlass org/jetbrains/kotlin/resolve/jvm/extensions/AnalysisHandlerExtension
instanceKlass org/jetbrains/kotlin/resolve/extensions/AnalysisHandlerExtension
instanceKlass org/jetbrains/kotlin/codegen/extensions/ClassFileFactoryFinalizerExtension
instanceKlass org/jetbrains/kotlin/backend/jvm/extensions/ClassGeneratorExtension
instanceKlass org/jetbrains/kotlin/codegen/extensions/ClassBuilderInterceptorExtension
instanceKlass org/jetbrains/kotlin/resolve/jvm/extensions/SyntheticJavaResolveExtension
instanceKlass org/jetbrains/kotlin/resolve/extensions/SyntheticResolveExtension
instanceKlass org/jetbrains/kotlin/extensions/ProjectExtensionDescriptor
instanceKlass org/jetbrains/kotlin/codegen/extensions/ExpressionCodegenExtension
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileUtilRt$2
instanceKlass java/util/Random
instanceKlass java/util/random/RandomGenerator
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileUtilRt$RepeatableIOOperation
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass java/io/FileFilter
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileUtilRt
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/PersistentFSConstants
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/DummyHolderFactory$DefaultFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/DummyHolderFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiImportHolder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/PsiFileWithStubSupport
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiFileEx
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/ui/Queryable
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/JavaDummyHolderFactory
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/facade/JvmFacadeImpl
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/facade/JvmFacade
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/HolderFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiConstantEvaluationHelper
instanceKlass org/jetbrains/kotlin/com/intellij/psi/JavaPsiFacade
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/MockInferredAnnotationsManager$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/InferredAnnotationsManager
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/ExternalAnnotationsListener
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/ExternalAnnotationsManager
instanceKlass org/jetbrains/kotlin/com/intellij/util/Processor
instanceKlass org/jetbrains/kotlin/asJava/finder/JavaElementFinder$Companion
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinFinderMarker
instanceKlass org/jetbrains/kotlin/asJava/KotlinAsJavaSupport$Companion
instanceKlass org/jetbrains/kotlin/asJava/KotlinAsJavaSupport
instanceKlass org/jetbrains/kotlin/asJava/classes/ImplUtilsKt
instanceKlass org/jetbrains/kotlin/asJava/LightClassGenerationSupport$Companion
instanceKlass org/jetbrains/kotlin/asJava/LightClassGenerationSupport
instanceKlass org/jetbrains/kotlin/resolve/jvm/JvmCodeAnalyzerInitializer
instanceKlass org/jetbrains/kotlin/resolve/CodeAnalyzerInitializer
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/JvmPsiConversionHelperImpl
instanceKlass org/jetbrains/kotlin/com/intellij/psi/JvmPsiConversionHelper
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/Interner
instanceKlass org/jetbrains/kotlin/utils/PlatformUtilsKt
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryClassSignatureParser
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionManager$2
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/SoftKeySoftValueHashMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/NotNullFunction
instanceKlass org/jetbrains/kotlin/com/intellij/util/NullableFunction
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/ProjectScope
instanceKlass gnu/trove/TObjectHash$NULL
instanceKlass gnu/trove/TObjectObjectProcedure
instanceKlass gnu/trove/THash
instanceKlass gnu/trove/TObjectHashingStrategy
instanceKlass gnu/trove/Equality
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinCliJavaFileManagerImpl$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/core/CoreJavaFileManager
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinCliJavaFileManager
instanceKlass org/jetbrains/kotlin/com/intellij/psi/controlFlow/ControlFlowFactory$1
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentWeakKeySoftValueHashMap$HardKey
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentWeakKeySoftValueHashMap$KeyReference
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentWeakKeySoftValueHashMap
instanceKlass org/jetbrains/kotlin/com/intellij/psi/controlFlow/ControlFlow
instanceKlass org/jetbrains/kotlin/com/intellij/psi/controlFlow/ControlFlowFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/codeStyle/SuggestedNameInfo
instanceKlass org/jetbrains/kotlin/com/intellij/psi/codeStyle/JavaCodeStyleManager
instanceKlass org/jetbrains/kotlin/com/intellij/psi/codeStyle/JavaCodeStyleSettingsFacade
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/JavaResolveCache$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/JavaResolveCache
instanceKlass org/jetbrains/kotlin/com/intellij/util/lang/JavaVersion
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentRefValueHashMap$ValueReference
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentRefValueHashMap
instanceKlass sun/util/locale/LanguageTag
instanceKlass java/util/ResourceBundle$Control
instanceKlass org/jetbrains/kotlin/com/intellij/AbstractBundle
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/roots/LanguageLevelProjectExtension$LanguageLevelChangeListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/roots/LanguageLevelProjectExtension
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionManager$CalculationStack
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionGuard
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionGuard$StackStamp
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionManager
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiInferenceHelper
instanceKlass org/jetbrains/kotlin/com/intellij/psi/scope/processor/FilterScopeProcessor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/scope/PsiScopeProcessor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/scope/NameHint
instanceKlass org/jetbrains/kotlin/com/intellij/psi/scope/ElementClassHint
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/PsiResolveHelperImpl
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiResolveHelper
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/JavaPsiImplementationHelper
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiElementFactoryImpl$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/TypeAnnotationProvider$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/TypeAnnotationProvider$Static
instanceKlass org/jetbrains/kotlin/com/intellij/psi/TypeAnnotationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiAnnotation
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/types/JvmPrimitiveTypeKind
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmAnnotation
instanceKlass org/jetbrains/kotlin/com/intellij/lang/java/parser/JavaParserUtil$ParserWrapper
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiImportStatementBase
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiTypeElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReferenceExpression
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiExpression
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiCaseLabelElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiAnnotationMemberValue
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJavaCodeReferenceElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiQualifiedReferenceElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiQualifiedReference
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJavaReference
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiPolyVariantReference
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/types/JvmReferenceType
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/types/JvmPrimitiveType
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiType
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiAnnotationOwner
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/types/JvmType
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiJavaParserFacadeImpl
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiElementFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJavaParserFacade
instanceKlass org/jetbrains/kotlin/com/intellij/psi/JVMElementFactory
instanceKlass org/jetbrains/kotlin/com/intellij/lang/injection/InjectedLanguageManager
instanceKlass org/jetbrains/kotlin/com/intellij/mock/MockDumbUtil
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/DumbUtil
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/impl/VirtualFileEnumerationAware
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/impl/VirtualFileEnumeration
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/SearchScope
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/ProjectAwareFileFilter
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiDirectory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/file/PsiDirectoryFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/CachedValue
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/ParameterizedCachedValue
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiCachedValuesFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/CachedValueProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/CachedValuesManager
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/tree/TreeElementVisitor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiFileFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiToDocumentSynchronizer
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiRecursiveVisitor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiElementVisitor
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/event/DocumentListener
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiDocumentManager
instanceKlass org/jetbrains/kotlin/com/intellij/core/MockDocumentCommitProcessor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/DocumentCommitProcessor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/SmartPointerManager
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/file/impl/FileManagerImpl$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Comparing
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileTypes/CharsetUtil
instanceKlass org/jetbrains/kotlin/com/intellij/util/LocalTimeCounter
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointPriorityListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointUtil$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointUtil
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/NonPhysicalFileSystem
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/file/impl/FileManagerImpl
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/PsiModificationTracker$SERVICE
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/NotNullFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Factory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileFilter$2
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileFilter$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileFilter
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/LowMemoryWatcher
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/ResolveCache$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/AnyPsiChangeListener
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/file/impl/FileManager
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/wm/ex/ProgressIndicatorEx
instanceKlass java/util/concurrent/atomic/AtomicReferenceArray
instanceKlass org/jetbrains/kotlin/com/intellij/psi/ResolveResult
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/ResolveCache$StrongValueReference
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentWeakKeySoftValueHashMap$ValueReference
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReference
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/ResolveCache$AbstractResolver
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/ResolveCache
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiModificationTrackerImpl$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/DumbService$DumbModeListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/DumbService
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/PsiModificationTracker$Listener
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiModificationTrackerImpl
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/PsiModificationTracker
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/facade/JvmElementProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiElementFinder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiTreeChangePreprocessor
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/roots/PackageIndex
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/file/impl/JavaFileManager
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/roots/FileIndexFacade
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/ProjectScopeBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/ResolveScopeManager
instanceKlass org/jetbrains/kotlin/com/intellij/util/CachedValuesFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/Project
instanceKlass org/jetbrains/kotlin/com/intellij/core/CoreProjectEnvironment
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment$Companion$getOrCreateApplicationEnvironment$1$2
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/TransactionGuard
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/JavaClassSupers
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinBinaryClassCache$Companion
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinBinaryClassCache
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinFileStub
instanceKlass org/jetbrains/kotlin/parsing/KotlinParserDefinition$Companion
instanceKlass org/jetbrains/kotlin/parsing/KotlinParserDefinition
instanceKlass sun/invoke/util/VerifyAccess$1
instanceKlass javax/swing/Icon
instanceKlass org/jetbrains/kotlin/com/intellij/util/keyFMap/OneElementFMap
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/impl/ExtensionProcessingHelper
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/BuildNumber
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginContentDescriptor$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginContentDescriptor
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/IdeaPluginDescriptorImpl$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/IdeaPluginDescriptorImpl
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/IdeaPluginDescriptor
instanceKlass org/jetbrains/kotlin/org/codehaus/stax2/ri/typed/ValueDecoderFactory
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/ByteBasedPNameTable$Bucket
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointDescriptor
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/util/DataUtil
instanceKlass kotlin/_Assertions
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/ElementScope
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/ByteBasedPNameFactory
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/ModuleDependenciesDescriptor$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/ModuleDependenciesDescriptor
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/ContainerDescriptor
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/RawPluginDescriptor
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/util/BufferRecycler
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/util/NameTable
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/util/XmlCharTypes
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/AttributeCollector
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/util/TextBuilder
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/FixedNsContext
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/NsBinding
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/PName
instanceKlass org/jetbrains/kotlin/org/codehaus/stax2/XMLStreamLocation2
instanceKlass org/jetbrains/kotlin/org/codehaus/stax2/typed/TypedValueDecoder
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/stax/StreamReaderImpl
instanceKlass org/jetbrains/kotlin/org/codehaus/stax2/XMLStreamReader2
instanceKlass org/jetbrains/kotlin/org/codehaus/stax2/typed/TypedXMLStreamReader
instanceKlass javax/xml/stream/XMLStreamReader
instanceKlass org/jetbrains/kotlin/org/codehaus/stax2/LocationInfo
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/XmlScanner
instanceKlass javax/xml/stream/XMLStreamConstants
instanceKlass javax/xml/namespace/NamespaceContext
instanceKlass javax/xml/stream/Location
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/InputBootstrapper
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/util/UriCanonicalizer
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/ReaderConfig$EncodingContext
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/impl/CommonConfig
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/StaxFactory
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/XmlReader
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/JavaZipFileDataLoader
instanceKlass org/jetbrains/kotlin/com/intellij/platform/util/plugins/DataLoader
instanceKlass org/jetbrains/kotlin/com/intellij/util/lang/ZipFilePool
instanceKlass sun/nio/fs/ExtendedFileSystemProvider
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/DescriptorListLoadingContext$threadLocalXmlFactory$1
instanceKlass org/jetbrains/kotlin/com/intellij/util/PlatformUtils
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginDescriptorLoader$createPluginLoadingResult$1
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginManagerCore
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginLoadingResult
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/DescriptorListLoadingContext
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/ReadModuleContext
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginXmlPathResolver$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginXmlPathResolver
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PathResolver
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginDescriptorLoader
instanceKlass org/jetbrains/kotlin/com/intellij/psi/JavaModuleSystem
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/compiled/ClsCustomNavigationPolicy
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/IdeaExtensionPoints
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/smartPointers/SmartPointerAnchorProvider
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/runner/JavaMainMethodProvider
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/NotNullLazyValue
instanceKlass org/jetbrains/kotlin/com/intellij/psi/augment/PsiAugmentProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/meta/MetaDataContributor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/FileContextProvider
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/DummyJavaFileCodeStyleFacadeFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/codeStyle/JavaFileCodeStyleFacadeFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/FileViewProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/compiled/ClsStubBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/compiled/ClassFileDecompilers$Full
instanceKlass java/util/TimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass java/lang/Class$AnnotationData
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/components/Service
instanceKlass org/jetbrains/kotlin/com/intellij/psi/compiled/ClassFileDecompilers
instanceKlass org/jetbrains/kotlin/com/intellij/psi/compiled/ClassFileDecompilers$Decompiler
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/MethodSignature
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/QueryExecutorBase
instanceKlass org/jetbrains/kotlin/com/intellij/util/QueryExecutor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/searches/SuperMethodsSearch$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/SmartExtensionPoint
instanceKlass org/jetbrains/kotlin/com/intellij/util/Query
instanceKlass org/jetbrains/kotlin/com/intellij/util/QueryFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/registry/RegistryValue
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/registry/Registry
instanceKlass org/jetbrains/kotlin/com/intellij/lang/folding/FoldingBuilderEx
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/PossiblyDumbAware
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/DumbAware
instanceKlass org/jetbrains/kotlin/com/intellij/lang/folding/FoldingBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/folding/JavaCodeFoldingSettings
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiParameter
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmParameter
instanceKlass org/jetbrains/kotlin/com/intellij/psi/presentation/java/VariablePresentationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiLocalVariable
instanceKlass org/jetbrains/kotlin/com/intellij/psi/presentation/java/FieldPresentationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiField
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiVariable
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmField
instanceKlass org/jetbrains/kotlin/com/intellij/psi/presentation/java/MethodPresentationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiMethod
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiParameterListOwner
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmMethod
instanceKlass org/jetbrains/kotlin/com/intellij/psi/presentation/java/ClassPresentationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiClass
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiTypeParameterListOwner
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJvmMember
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiTarget
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiNameIdentifierOwner
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiDocCommentOwner
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiMember
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJavaDocumentedElement
instanceKlass org/jetbrains/kotlin/com/intellij/pom/PomRenameableTarget
instanceKlass org/jetbrains/kotlin/com/intellij/pom/PomNamedTarget
instanceKlass org/jetbrains/kotlin/com/intellij/pom/PomTarget
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmClass
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmTypeParametersOwner
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmMember
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmTypeDeclaration
instanceKlass org/jetbrains/kotlin/com/intellij/navigation/ItemPresentation
instanceKlass org/jetbrains/kotlin/com/intellij/psi/presentation/java/PackagePresentationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/navigation/ItemPresentationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiPackage
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiQualifiedNamedElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJvmModifiersOwner
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiModifierListOwner
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmModifiersOwner
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiDirectoryContainer
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmPackage
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmNamedElement
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmAnnotatedElement
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmElement
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/projectRoots/JavaVersionService
instanceKlass org/jetbrains/kotlin/com/intellij/psi/JavaDirectoryService
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiSubstitutor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiSubstitutorFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/ModificationTracker$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/file/PsiPackageImplementationHelper
instanceKlass org/jetbrains/kotlin/com/intellij/util/graph/InboundSemiGraph
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/LoadingOrder
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/JavaContainerProvider
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/ContainerProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiExpressionEvaluator
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/ConstantExpressionEvaluator
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/IndexSink
instanceKlass org/jetbrains/kotlin/com/intellij/util/diff/FlyweightCapableTreeStructure
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/java/stubs/PsiJavaFileStub
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/PsiClassHolderFileStub
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/LightStubBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/PsiFileStub
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/StubElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/Stub
instanceKlass org/jetbrains/kotlin/com/intellij/psi/StubBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/ClearableLazyValue
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/StubSerializer
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/ObjectStubSerializer
instanceKlass org/jetbrains/kotlin/com/intellij/lang/java/JavaParserDefinition
instanceKlass org/jetbrains/kotlin/com/intellij/psi/javadoc/PsiDocTagValue
instanceKlass org/jetbrains/kotlin/com/intellij/psi/javadoc/PsiDocToken
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiKeyword
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiIdentifier
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJavaToken
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/compiled/ClassFileStubBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/BinaryFileStubBuilder$CompositeBinaryFileStubBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/BinaryFileStubBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/ClassFileViewProviderFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/FileViewProviderFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Condition$3
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Condition$2
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Condition$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Condition
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Conditions
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/IElementType$Predicate
instanceKlass org/jetbrains/kotlin/com/intellij/lexer/Lexer
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileTypes/PlainTextParserDefinition
instanceKlass org/jetbrains/kotlin/com/intellij/lang/LanguageUtil
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiPlainText
instanceKlass org/jetbrains/kotlin/com/intellij/lang/ASTFactory$DefaultFactoryHolder
instanceKlass org/jetbrains/kotlin/com/intellij/ide/highlighter/ArchiveFileType
instanceKlass java/util/concurrent/ExecutorService
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass org/jetbrains/kotlin/com/intellij/util/ConcurrencyUtil
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileTypes/LanguageFileType
instanceKlass org/jetbrains/kotlin/com/intellij/ide/highlighter/JavaClassFileType
instanceKlass org/jetbrains/kotlin/com/intellij/DynamicBundle$LanguageBundleEP
instanceKlass org/jetbrains/kotlin/com/intellij/util/pico/CachingConstructorInjectionComponentAdapter
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/ApplicationInfo
instanceKlass org/jetbrains/kotlin/com/intellij/util/graph/GraphAlgorithms
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/command/impl/CoreCommandProcessor$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/command/CommandListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/command/CommandToken
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/command/CommandProcessor
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/folding/CodeFoldingSettings
instanceKlass org/jetbrains/kotlin/com/intellij/util/ObjectUtils$Sentinel
instanceKlass org/jetbrains/kotlin/com/intellij/util/ObjectUtilsRt
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentIntObjectHashMap$CounterCell
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentLongObjectHashMap$Node
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentLongObjectMap$LongEntry
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentLongObjectHashMap
instanceKlass java/util/concurrent/Future
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/PerformInBackgroundOption
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/ProgressIndicator
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/Task
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/TaskInfo
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/Progressive
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReferenceService
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/ObjectStubTree
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiFile
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiFileSystemItem
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiCheckedRenameElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiNamedElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/NavigatablePsiElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/StubTreeLoader
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReferenceProviderBean
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReferenceContributor
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/doubles/Double2ObjectMap
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/doubles/Double2ObjectFunction
instanceKlass java/util/function/DoubleFunction
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReferenceRegistrar
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReferenceProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/reference/ReferenceProvidersRegistry
instanceKlass org/jetbrains/kotlin/com/intellij/lang/PsiBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/UserDataHolderUnprotected
instanceKlass org/jetbrains/kotlin/com/intellij/lang/SyntaxTreeBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/lang/PsiBuilderFactory
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorFactory
instanceKlass sun/misc/Unsafe
instanceKlass org/jetbrains/kotlin/com/intellij/util/ReflectionUtil
instanceKlass org/jetbrains/kotlin/com/intellij/psi/CommonClassNames
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/CharArrayCharSequence
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/CharSequenceBackedByArray
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/text/CharSequenceWithStringHash
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/text/Strings
instanceKlass java/util/regex/ASCII
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/text/StringUtilRt
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/CharTableImpl
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiLanguageInjectionHost
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiComment
instanceKlass org/jetbrains/kotlin/com/intellij/model/psi/UrlReferenceHost
instanceKlass org/jetbrains/kotlin/com/intellij/model/psi/PsiExternalReferenceHost
instanceKlass org/jetbrains/kotlin/com/intellij/navigation/NavigationItem
instanceKlass org/jetbrains/kotlin/com/intellij/pom/Navigatable
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiWhiteSpace
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/ILazyParseableElementTypeBase
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/IElementType
instanceKlass org/jetbrains/kotlin/com/intellij/lang/FileASTNode
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Iconable
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/ReparseableASTNode
instanceKlass org/jetbrains/kotlin/com/intellij/lang/ASTNode
instanceKlass org/jetbrains/kotlin/com/intellij/util/CharTable
instanceKlass org/jetbrains/kotlin/com/intellij/lang/ASTFactory
instanceKlass org/jetbrains/kotlin/com/intellij/lang/DefaultASTFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/encoding/EncodingRegistry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass org/jetbrains/kotlin/com/intellij/serviceContainer/LazyExtensionInstance
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/PluginAware
instanceKlass org/jetbrains/kotlin/com/intellij/util/KeyedLazyInstance
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/BulkVirtualFileListenerAdapter
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/impl/BaseBusConnection
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/impl/MessageBusImpl$MessageHandlerHolder
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/newvfs/CachingVirtualFileSystem
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass java/util/concurrent/atomic/AtomicLongFieldUpdater$CASUpdater$1
instanceKlass java/util/concurrent/atomic/AtomicLongFieldUpdater
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/newvfs/BulkFileListener
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/Topic
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileManager
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/EmptyRunnable
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointAdapter
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointAndAreaListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/impl/ExtensionComponentAdapter
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/LoadingOrder$Orderable
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/PluginId
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/DefaultPluginDescriptor
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileManagerListener
instanceKlass java/util/EventObject
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/ex/DocumentEx
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/Document
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileEditor/FileDocumentManager
instanceKlass org/jetbrains/kotlin/cli/jvm/modules/CoreJrtFileSystem$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentFactoryMap
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler
instanceKlass org/jetbrains/kotlin/com/intellij/util/Function
instanceKlass org/jetbrains/kotlin/com/intellij/util/EventDispatcher
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/BaseExtensionPointName
instanceKlass java/util/EventListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/CachedSingletonsRegistry
instanceKlass org/jetbrains/kotlin/com/intellij/util/ArrayUtil
instanceKlass org/jetbrains/kotlin/com/intellij/util/ArrayFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/Extensions
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/ObjectNode
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Disposer$CheckedDisposableImpl
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/CheckedDisposable
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/impl/ImplementationClassResolver
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/impl/ExtensionPointImpl
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPoint
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/impl/ExtensionsAreaImpl
instanceKlass org/jetbrains/kotlin/com/intellij/util/pico/DefaultPicoContainer$InstanceComponentAdapter
instanceKlass org/jetbrains/kotlin/com/intellij/util/pico/DefaultPicoContainer$LinkedHashSetWrapper
instanceKlass org/jetbrains/kotlin/org/picocontainer/ComponentAdapter
instanceKlass org/jetbrains/kotlin/com/intellij/util/ArrayUtilRt
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/impl/MessageQueue
instanceKlass java/util/concurrent/Executor
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/impl/MessageBusImpl$MessagePublisher
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/MessageBusConnection
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/SimpleMessageBusConnection
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/impl/MessageBusImpl
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/impl/MessageBusEx
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/MessageBusFactory
instanceKlass org/jetbrains/kotlin/com/intellij/util/keyFMap/DebugFMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/keyFMap/EmptyFMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/keyFMap/KeyFMap
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/ModalityState
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionsArea
instanceKlass org/jetbrains/kotlin/com/intellij/util/pico/DefaultPicoContainer
instanceKlass org/jetbrains/kotlin/org/picocontainer/MutablePicoContainer
instanceKlass org/jetbrains/kotlin/org/picocontainer/PicoContainer
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/MessageBus
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileTypes/UnknownFileType
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/FastUtilCaseInsensitiveStringHashingStrategy
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/FastUtilHashingStrategies$1
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/FastUtilHashingStrategies$SerializableHashStrategy
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/FastUtilCharSequenceHashingStrategy
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/FastUtilHashingStrategies
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/SystemInfoRt
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Getter
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/DisabledPluginsState
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreApplicationEnvironment$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/lang/ParserDefinition
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileTypes/FileType
instanceKlass org/jetbrains/kotlin/com/intellij/concurrency/JobLauncher
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/UserDataHolderEx
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/MessageBusOwner
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/ex/ApplicationEx
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/ProgressIndicatorProvider
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/KeyedExtensionCollector
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/SimpleModificationTracker
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/ModificationTracker
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileSystem
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/PluginDescriptor
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileTypes/FileTypeRegistry
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/Application
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/components/ComponentManager
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/UserDataHolder
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/AreaInstance
instanceKlass org/jetbrains/kotlin/com/intellij/core/CoreApplicationEnvironment
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Disposer$2
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment$Companion
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment
instanceKlass org/jetbrains/kotlin/cli/jvm/config/JvmContentRootsKt
instanceKlass org/jetbrains/kotlin/cli/jvm/config/JvmClasspathRoot
instanceKlass org/jetbrains/kotlin/cli/jvm/config/JvmContentRoot
instanceKlass org/jetbrains/kotlin/cli/jvm/config/JvmClasspathRootBase
instanceKlass org/jetbrains/kotlin/cli/jvm/config/JvmContentRootBase
instanceKlass org/jetbrains/kotlin/cli/common/config/KotlinSourceRoot
instanceKlass org/jetbrains/kotlin/cli/common/config/ContentRoot
instanceKlass org/jetbrains/kotlin/cli/common/config/ContentRootsKt
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinToJVMBytecodeCompilerKt
instanceKlass org/jetbrains/kotlin/cli/common/modules/ModuleChunk
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/CliCompilerUtilsKt
instanceKlass org/jetbrains/kotlin/cli/common/modules/ModuleBuilder
instanceKlass org/jetbrains/kotlin/modules/Module
instanceKlass org/jetbrains/kotlin/cli/jvm/K2JVMCompilerKt
instanceKlass org/jetbrains/kotlin/config/JvmSerializeIrMode$Companion
instanceKlass org/jetbrains/kotlin/config/JvmAbiStability$Companion
instanceKlass org/jetbrains/kotlin/compiler/plugin/CommandLineProcessor
instanceKlass org/jetbrains/kotlin/compiler/plugin/CompilerPluginRegistrar$Companion
instanceKlass org/jetbrains/kotlin/compiler/plugin/CompilerPluginRegistrar
instanceKlass org/jetbrains/kotlin/compiler/plugin/ComponentRegistrar$Companion
instanceKlass org/jetbrains/kotlin/compiler/plugin/ComponentRegistrar
instanceKlass org/jetbrains/kotlin/util/ServiceLoaderLite
instanceKlass org/jetbrains/kotlin/cli/common/CLICompilerKt
instanceKlass org/jetbrains/kotlin/cli/jvm/plugins/PluginCliParser
instanceKlass org/jetbrains/kotlin/cli/plugins/PluginsOptionsParserKt
instanceKlass kotlin/collections/ArrayAsCollection
instanceKlass org/jetbrains/kotlin/backend/common/phaser/PhaseConfig
instanceKlass org/jetbrains/kotlin/backend/common/phaser/PhaseConfigurationService
instanceKlass org/jetbrains/kotlin/backend/common/phaser/PhaseConfigKt
instanceKlass org/jetbrains/kotlin/cli/common/CreatePhaseConfigKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ReflectiveAccessKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FragmentLocalFunctionPatchLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ResolveInlineCallsKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/GenerateMultifileFacadesKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FileClassLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ScriptLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/SerializeIrPhaseKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ProcessOptionalAnnotationsKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FragmentSharedVariablesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ExternalPackageParentPatcherLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FakeLocalVariablesForIrInlinerLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FakeLocalVariablesForBytecodeInlinerLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/RenameFieldsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ReplaceNumberToCharCallSitesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/AddSuperQualifierToJavaFieldAccessLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/MakePropertyDelegateMethodsStaticLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ReplaceKFunctionInvokeWithFunctionInvokeKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/TypeOperatorLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/RecordEnclosingMethodsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/RepeatedAnnotationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/AdditionalClassAnnotationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmOptimizationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmSafeCallChainFoldingLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ToArrayLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmArgumentNullabilityAssertionsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/BridgeLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FunctionNVarargBridgeLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/UniqueLoopLabelsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/EnumExternalEntriesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/EnumClassLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/AddContinuationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/TailCallOptimizationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/InheritedDefaultMethodsOnClassesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmStringConcatenationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/common/lower/FlattenStringConcatenationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmDefaultConstructorLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/StaticCallableReferenceLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/SingletonReferencesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/MappedEnumWhenLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmInlineClassLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmMultiFieldValueClassLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmSingleAbstractMethodLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/CollectionStubMethodLoweringKt
instanceKlass org/jetbrains/kotlin/backend/common/lower/loops/ForLoopsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/common/lower/RangeContainsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmBuiltInsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/AnonymousObjectSuperConstructorLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/MoveCompanionObjectFieldsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/PropertyReferenceLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/SingletonOrConstantDelegationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/PropertyReferenceDelegationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/SuspendLambdaLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/RemoveDuplicatedInlinedLocalClassesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmInventNamesForLocalClassesKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmLateinitLoweringKt
instanceKlass org/jetbrains/kotlin/builtins/PrimitiveType$Companion
instanceKlass sun/reflect/misc/ReflectUtil
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl$1
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater
instanceKlass kotlin/SafePublicationLazyImpl$Companion
instanceKlass kotlin/SafePublicationLazyImpl
instanceKlass kotlin/LazyKt__LazyJVMKt$WhenMappings
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/VarargLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/PolymorphicSignatureLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmAnnotationImplementationTransformerKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/AnnotationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/MainMethodGenerationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmOverloadsAnnotationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/TypeAliasAnnotationMethodsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/CreateSeparateCallForInlinedLambdasLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/MarkNecessaryInlinedClassesAsRegeneratedLoweringKt
instanceKlass org/jetbrains/kotlin/ir/declarations/IrTypeParametersContainer
instanceKlass org/jetbrains/kotlin/ir/declarations/IrReturnTarget
instanceKlass org/jetbrains/kotlin/ir/declarations/IrDeclarationParent
instanceKlass org/jetbrains/kotlin/ir/declarations/IrPossiblyExternalDeclaration
instanceKlass org/jetbrains/kotlin/ir/declarations/IrOverridableDeclaration
instanceKlass org/jetbrains/kotlin/ir/declarations/IrOverridableMember
instanceKlass org/jetbrains/kotlin/ir/declarations/IrDeclarationWithVisibility
instanceKlass org/jetbrains/kotlin/ir/declarations/IrMetadataSourceOwner
instanceKlass org/jetbrains/kotlin/ir/declarations/IrMemberWithContainerSource
instanceKlass org/jetbrains/kotlin/ir/declarations/IrDeclarationWithName
instanceKlass org/jetbrains/kotlin/ir/declarations/IrAttributeContainer
instanceKlass org/jetbrains/kotlin/ir/IrElementBase
instanceKlass org/jetbrains/kotlin/ir/declarations/IrDeclaration
instanceKlass org/jetbrains/kotlin/ir/declarations/IrSymbolOwner
instanceKlass org/jetbrains/kotlin/ir/declarations/IrMutableAnnotationContainer
instanceKlass org/jetbrains/kotlin/ir/declarations/IrAnnotationContainer
instanceKlass org/jetbrains/kotlin/ir/IrStatement
instanceKlass org/jetbrains/kotlin/ir/IrElement
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmStaticAnnotationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/StaticDefaultFunctionLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ObjectClassLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/DirectInvokeLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/AssertionLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/InlineCallableReferenceToLambdaKt
instanceKlass org/jetbrains/kotlin/backend/common/lower/SharedVariablesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FunctionReferenceLoweringKt
instanceKlass org/jetbrains/kotlin/backend/common/lower/PropertiesLowering$Companion
instanceKlass org/jetbrains/kotlin/backend/common/lower/PropertiesLowering
instanceKlass org/jetbrains/kotlin/backend/common/DeclarationTransformer
instanceKlass org/jetbrains/kotlin/backend/common/FileLoweringPass
instanceKlass org/jetbrains/kotlin/backend/common/phaser/ModuleLoweringPhaseAdapter
instanceKlass org/jetbrains/kotlin/backend/common/phaser/CustomPhaseAdapter
instanceKlass org/jetbrains/kotlin/backend/jvm/JvmLowerKt
instanceKlass org/jetbrains/kotlin/backend/common/phaser/CompositePhase
instanceKlass org/jetbrains/kotlin/backend/common/phaser/PerformByIrFilePhase
instanceKlass org/jetbrains/kotlin/backend/common/phaser/PerformByIrFileKt
instanceKlass org/jetbrains/kotlin/backend/common/phaser/FileLoweringPhaseAdapter
instanceKlass org/jetbrains/kotlin/backend/common/phaser/CompilerPhaseKt
instanceKlass org/jetbrains/kotlin/backend/common/phaser/DumperVerifierKt
instanceKlass kotlin/jvm/functions/Function3
instanceKlass org/jetbrains/kotlin/backend/common/phaser/PhaseBuildersKt
instanceKlass kotlin/collections/SetsKt__SetsJVMKt
instanceKlass org/jetbrains/kotlin/backend/jvm/JvmPhasesKt$generateAdditionalClassesPhase$1
instanceKlass org/jetbrains/kotlin/backend/common/phaser/SameTypeCompilerPhase
instanceKlass org/jetbrains/kotlin/backend/common/phaser/AbstractNamedCompilerPhase
instanceKlass org/jetbrains/kotlin/backend/common/phaser/CompilerPhase
instanceKlass org/jetbrains/kotlin/backend/jvm/JvmPhasesKt
instanceKlass org/jetbrains/kotlin/cli/common/environment/UtilKt
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Disposer$1
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/CanonicalHashingStrategy
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentRefHashMap$2
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentRefHashMap$HardKey
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentRefHashMap$KeyReference
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/Object2ObjectMap$FastEntrySet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/AbstractObject2ObjectFunction
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/Object2ObjectMap
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/Object2ObjectFunction
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/RefHashMap$HardKey
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/RefHashMap$Key
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/IdentityHashingStrategy
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/HashingStrategy
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/Hash$Strategy
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/CollectionFactory
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/Reference2ObjectMap$FastEntrySet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/AbstractReference2ObjectFunction
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/Reference2ObjectMap
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/Reference2ObjectFunction
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass java/util/stream/Collectors
instanceKlass java/util/stream/Collector
instanceKlass java/util/function/BinaryOperator
instanceKlass java/util/function/BiConsumer
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectSpliterator
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectIterator
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ReferenceSet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ReferenceCollection
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/ObjectTree
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Disposer
instanceKlass org/jetbrains/kotlin/progress/ProgressIndicatorAndCompilationCanceledStatus
instanceKlass org/jetbrains/kotlin/progress/CompilationCanceledStatus
instanceKlass org/jetbrains/kotlin/com/google/common/collect/LinkedHashMultimap$1
instanceKlass java/util/stream/MatchOps$BooleanTerminalSink
instanceKlass java/util/stream/MatchOps$MatchOp
instanceKlass java/util/stream/MatchOps
instanceKlass java/util/Spliterators$IteratorSpliterator
instanceKlass org/jetbrains/kotlin/utils/KotlinPathsFromBaseDirectory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/PathManager
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass org/jetbrains/kotlin/utils/PathUtil
instanceKlass kotlin/collections/AbstractList$IteratorImpl
instanceKlass org/jetbrains/kotlin/config/JvmTarget$Companion
instanceKlass org/jetbrains/kotlin/platform/TargetPlatformVersion
instanceKlass org/jetbrains/kotlin/config/JVMConfigurationKeys
instanceKlass org/jetbrains/kotlin/cli/jvm/JvmArgumentsKt
instanceKlass org/jetbrains/kotlin/config/CommonConfigurationKeysKt
instanceKlass kotlin/Unit
instanceKlass org/jetbrains/kotlin/utils/WrappedValues$ThrowableWrapper
instanceKlass org/jetbrains/kotlin/config/LanguageVersionSettingsKt
instanceKlass org/jetbrains/kotlin/cli/common/messages/MessageCollector$Companion$NONE$1
instanceKlass org/jetbrains/kotlin/cli/common/messages/MessageCollector$Companion
instanceKlass org/jetbrains/kotlin/cli/common/arguments/JavaTypeEnhancementStateParser$Companion
instanceKlass org/jetbrains/kotlin/cli/common/arguments/JavaTypeEnhancementStateParser
instanceKlass org/jetbrains/kotlin/config/JvmAnalysisFlags$Delegates$JvmDefaultModeDisabledByDefault
instanceKlass org/jetbrains/kotlin/utils/WrappedValues$1
instanceKlass org/jetbrains/kotlin/utils/WrappedValues
instanceKlass kotlin/collections/EmptyIterator
instanceKlass org/jetbrains/kotlin/name/FqNamesUtilKt
instanceKlass org/jetbrains/kotlin/load/java/NullabilityAnnotationStates$Companion
instanceKlass org/jetbrains/kotlin/load/java/Jsr305Settings
instanceKlass org/jetbrains/kotlin/storage/LockBasedStorageManager$MapBasedMemoizedFunction
instanceKlass org/jetbrains/kotlin/storage/DefaultSimpleLock
instanceKlass org/jetbrains/kotlin/storage/SimpleLock$Companion
instanceKlass org/jetbrains/kotlin/storage/EmptySimpleLock
instanceKlass org/jetbrains/kotlin/storage/LockBasedStorageManager$ExceptionHandlingStrategy$1
instanceKlass org/jetbrains/kotlin/storage/LockBasedStorageManager$ExceptionHandlingStrategy
instanceKlass org/jetbrains/kotlin/storage/MemoizedFunctionToNullable
instanceKlass org/jetbrains/kotlin/storage/MemoizedFunctionToNotNull
instanceKlass org/jetbrains/kotlin/storage/CacheWithNotNullValues
instanceKlass org/jetbrains/kotlin/storage/NotNullLazyValue
instanceKlass org/jetbrains/kotlin/storage/NullableLazyValue
instanceKlass org/jetbrains/kotlin/storage/CacheWithNullableValues
instanceKlass org/jetbrains/kotlin/storage/SimpleLock
instanceKlass org/jetbrains/kotlin/storage/LockBasedStorageManager
instanceKlass org/jetbrains/kotlin/storage/StorageManager
instanceKlass kotlin/TuplesKt
instanceKlass kotlin/internal/ProgressionUtilKt
instanceKlass kotlin/ranges/IntRange$Companion
instanceKlass kotlin/ranges/IntProgression$Companion
instanceKlass kotlin/ranges/IntProgression
instanceKlass kotlin/ranges/OpenEndRange
instanceKlass kotlin/ranges/ClosedRange
instanceKlass kotlin/KotlinVersionCurrentValue
instanceKlass kotlin/KotlinVersion$Companion
instanceKlass kotlin/KotlinVersion
instanceKlass org/jetbrains/kotlin/load/java/ReportLevel$Companion
instanceKlass org/jetbrains/kotlin/load/java/JavaNullabilityAnnotationsStatus$Companion
instanceKlass org/jetbrains/kotlin/load/java/JavaNullabilityAnnotationsStatus
instanceKlass kotlin/Pair
instanceKlass org/jetbrains/kotlin/load/java/NullabilityAnnotationStatesImpl
instanceKlass org/jetbrains/kotlin/load/java/NullabilityAnnotationStates
instanceKlass org/jetbrains/kotlin/load/java/JavaNullabilityAnnotationSettingsKt
instanceKlass org/jetbrains/kotlin/load/java/JavaTypeEnhancementState$Companion
instanceKlass org/jetbrains/kotlin/load/java/JavaTypeEnhancementState
instanceKlass org/jetbrains/kotlin/config/JvmAnalysisFlags$Delegates$JavaTypeEnhancementStateWarnByDefault
instanceKlass org/jetbrains/kotlin/config/JvmAnalysisFlags
instanceKlass org/jetbrains/kotlin/config/AnalysisFlag$Delegates$ApiModeDisabledByDefault
instanceKlass org/jetbrains/kotlin/config/AnalysisFlag$Delegates$ListOfStrings
instanceKlass org/jetbrains/kotlin/config/AnalysisFlag
instanceKlass org/jetbrains/kotlin/config/AnalysisFlag$Delegate
instanceKlass kotlin/properties/ReadOnlyProperty
instanceKlass org/jetbrains/kotlin/config/AnalysisFlag$Delegates$Boolean
instanceKlass kotlin/reflect/KProperty$Getter
instanceKlass kotlin/reflect/KProperty$Accessor
instanceKlass org/jetbrains/kotlin/config/AnalysisFlags
instanceKlass org/jetbrains/kotlin/config/LanguageVersionSettingsImpl$Companion
instanceKlass org/jetbrains/kotlin/config/LanguageVersionSettings$Companion
instanceKlass org/jetbrains/kotlin/config/LanguageVersionSettingsImpl
instanceKlass java/math/MutableBigInteger
instanceKlass org/jetbrains/kotlin/config/MavenComparableVersion$IntegerItem
instanceKlass org/jetbrains/kotlin/config/MavenComparableVersion$Item
instanceKlass org/jetbrains/kotlin/config/MavenComparableVersion
instanceKlass org/jetbrains/kotlin/config/ApiVersion$Companion
instanceKlass org/jetbrains/kotlin/config/ApiVersion
instanceKlass org/jetbrains/kotlin/config/LanguageVersion$Companion
instanceKlass org/jetbrains/kotlin/config/IncrementalCompilation
instanceKlass org/jetbrains/kotlin/cli/common/UtilsKt
instanceKlass org/jetbrains/kotlin/config/CommonConfigurationKeys
instanceKlass org/jetbrains/kotlin/cli/common/messages/IrMessageCollector$Companion
instanceKlass org/jetbrains/kotlin/cli/common/messages/IrMessageCollector
instanceKlass org/jetbrains/kotlin/ir/util/IrMessageLogger$Companion
instanceKlass org/jetbrains/kotlin/ir/util/IrMessageLogger
instanceKlass org/jetbrains/kotlin/com/google/common/collect/AbstractMapEntry
instanceKlass org/jetbrains/kotlin/com/google/common/collect/LinkedHashMultimap$ValueSetLink
instanceKlass org/jetbrains/kotlin/com/google/common/collect/CollectPreconditions
instanceKlass org/jetbrains/kotlin/com/google/common/base/Preconditions
instanceKlass org/jetbrains/kotlin/com/google/common/collect/UnmodifiableIterator
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Maps$EntryTransformer
instanceKlass org/jetbrains/kotlin/com/google/common/base/Converter
instanceKlass org/jetbrains/kotlin/com/google/common/collect/BiMap
instanceKlass org/jetbrains/kotlin/com/google/common/collect/ImmutableMap
instanceKlass org/jetbrains/kotlin/com/google/common/base/Function
instanceKlass org/jetbrains/kotlin/com/google/common/collect/SortedMapDifference
instanceKlass org/jetbrains/kotlin/com/google/common/collect/MapDifference
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Maps
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper
instanceKlass java/util/logging/LogManager$4
instanceKlass jdk/internal/logger/BootstrapLogger$BootstrapExecutors
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass java/util/ServiceLoader$1
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass java/util/Collections$EmptyIterator
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass java/lang/System$Logger
instanceKlass java/util/stream/Streams
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/stream/Streams$AbstractStreamBuilderImpl
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/stream/FindOps
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass java/util/Spliterator
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/Properties$EntrySet
instanceKlass java/util/Collections$3
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$VisitedLoggers
instanceKlass java/util/function/Predicate
instanceKlass java/util/logging/LogManager$2
instanceKlass java/lang/System$LoggerFinder
instanceKlass java/security/Security$2
instanceKlass jdk/internal/access/JavaSecurityPropertiesAccess
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/util/Properties$LineReader
instanceKlass java/security/Security$1
instanceKlass java/security/Security
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/util/logging/LogManager$LoggingProviderAccess
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/LogManager
instanceKlass java/util/logging/Logger$ConfigurationData
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Platform
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Multiset
instanceKlass org/jetbrains/kotlin/com/google/common/collect/AbstractMultimap
instanceKlass org/jetbrains/kotlin/com/google/common/collect/SetMultimap
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Multimap
instanceKlass org/jetbrains/kotlin/cli/common/messages/GroupingMessageCollector
instanceKlass java/lang/StrictMath
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/HashCommon
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/Int2ObjectMap$FastEntrySet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectSet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntSet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntCollection
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntIterable
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectCollection
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectIterable
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/AbstractInt2ObjectFunction
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/Hash
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/Int2ObjectMap
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/Int2ObjectFunction
instanceKlass java/util/function/IntFunction
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/Function
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/IntKeyWeakValueHashMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentLongObjectMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentList
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentIntObjectMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/IntObjectMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ContainerUtil
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Key
instanceKlass org/jetbrains/kotlin/config/CompilerConfigurationKey
instanceKlass org/jetbrains/kotlin/cli/common/CLIConfigurationKeys
instanceKlass org/jetbrains/kotlin/utils/SmartList$EmptyIterator
instanceKlass org/jetbrains/kotlin/cli/common/ArgumentsKt
instanceKlass java/net/URLConnection
instanceKlass org/jetbrains/kotlin/cli/common/messages/PrintingMessageCollector
instanceKlass kotlin/jvm/internal/CollectionToArray
instanceKlass kotlin/reflect/jvm/internal/pcollections/MapEntry
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties$Val$1
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties$Val
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties
instanceKlass java/util/regex/CharPredicates
instanceKlass kotlin/text/Regex$Companion
instanceKlass kotlin/text/Regex
instanceKlass kotlin/jvm/internal/DefaultConstructorMarker
instanceKlass kotlin/reflect/jvm/internal/KDeclarationContainerImpl$Companion
instanceKlass kotlin/reflect/jvm/internal/KTypeParameterOwnerImpl
instanceKlass kotlin/reflect/jvm/internal/KClassifierImpl
instanceKlass kotlin/reflect/jvm/internal/pcollections/ConsPStack
instanceKlass kotlin/reflect/jvm/internal/pcollections/IntTree
instanceKlass kotlin/reflect/jvm/internal/pcollections/IntTreePMap
instanceKlass kotlin/reflect/jvm/internal/pcollections/HashPMap
instanceKlass kotlin/reflect/jvm/internal/KClassCacheKt
instanceKlass kotlin/reflect/jvm/internal/KDeclarationContainerImpl
instanceKlass kotlin/jvm/internal/ClassBasedDeclarationContainer
instanceKlass kotlin/reflect/KMutableProperty1
instanceKlass kotlin/reflect/KProperty1
instanceKlass kotlin/reflect/KTypeParameter
instanceKlass kotlin/reflect/KMutableProperty0
instanceKlass kotlin/reflect/KProperty0
instanceKlass kotlin/reflect/KType
instanceKlass kotlin/reflect/KMutableProperty2
instanceKlass kotlin/reflect/KMutableProperty
instanceKlass kotlin/reflect/KProperty2
instanceKlass kotlin/reflect/KProperty
instanceKlass kotlin/reflect/KClass
instanceKlass kotlin/jvm/internal/ReflectionFactory
instanceKlass kotlin/reflect/KClassifier
instanceKlass kotlin/jvm/internal/Reflection
instanceKlass kotlin/jvm/JvmClassMappingKt
instanceKlass jdk/internal/reflect/ClassDefiner$1
instanceKlass jdk/internal/reflect/ClassDefiner
instanceKlass jdk/internal/reflect/MethodAccessorGenerator$1
instanceKlass jdk/internal/reflect/Label$PatchInfo
instanceKlass jdk/internal/reflect/Label
instanceKlass jdk/internal/reflect/UTF8
instanceKlass jdk/internal/reflect/ClassFileAssembler
instanceKlass jdk/internal/reflect/ByteVectorImpl
instanceKlass jdk/internal/reflect/ByteVector
instanceKlass jdk/internal/reflect/ByteVectorFactory
instanceKlass jdk/internal/reflect/AccessorGenerator
instanceKlass jdk/internal/reflect/ClassFileConstants
instanceKlass org/jetbrains/kotlin/config/LanguageVersionSettings
instanceKlass org/jetbrains/kotlin/cli/common/messages/MessageCollector
instanceKlass org/jetbrains/kotlin/config/LanguageOrApiVersion
instanceKlass org/jetbrains/kotlin/utils/DescriptionAware
instanceKlass kotlin/collections/builders/MapBuilder$EntryRef
instanceKlass kotlin/jvm/internal/markers/KMutableMap$Entry
instanceKlass kotlin/collections/builders/MapBuilder$Itr
instanceKlass kotlin/jvm/internal/markers/KMutableIterator
instanceKlass org/jetbrains/kotlin/cli/common/arguments/ArgumentField
instanceKlass org/jetbrains/kotlin/util/capitalizeDecapitalize/CapitalizeDecapitalizeKt
instanceKlass org/jetbrains/kotlin/name/ClassId
instanceKlass org/jetbrains/kotlin/name/FqNameUnsafe$1
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass org/jetbrains/kotlin/name/Name
instanceKlass org/jetbrains/kotlin/name/FqNameUnsafe
instanceKlass org/jetbrains/kotlin/name/FqName
instanceKlass org/jetbrains/kotlin/load/java/JvmAbi
instanceKlass kotlin/Metadata
instanceKlass java/lang/annotation/Target
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass jdk/internal/org/objectweb/asm/Edge
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass java/util/StringJoiner
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass java/util/function/Consumer
instanceKlass jdk/internal/module/Checks
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass java/lang/PublicMethods
instanceKlass java/util/Collections$1
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass java/lang/reflect/Proxy
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass kotlin/annotation/Target
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass kotlin/jvm/internal/markers/KMutableSet
instanceKlass kotlin/jvm/internal/markers/KMutableCollection
instanceKlass kotlin/jvm/internal/markers/KMutableIterable
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass org/jetbrains/kotlin/cli/common/arguments/Argument
instanceKlass kotlin/collections/EmptySet
instanceKlass kotlin/collections/EmptyMap
instanceKlass kotlin/ranges/RangesKt__RangesKt
instanceKlass kotlin/collections/builders/ListBuilderKt
instanceKlass kotlin/collections/builders/MapBuilder$Companion
instanceKlass kotlin/collections/builders/MapBuilder
instanceKlass kotlin/jvm/internal/markers/KMutableMap
instanceKlass kotlin/collections/MapsKt__MapWithDefaultKt
instanceKlass java/io/FileInputStream$1
instanceKlass kotlin/io/CloseableKt
instanceKlass kotlin/text/CharsKt__CharJVMKt
instanceKlass java/lang/Character$CharacterCache
instanceKlass kotlin/sequences/GeneratorSequence$iterator$1
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/util/function/BiFunction
instanceKlass java/lang/invoke/VarHandle$1
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/VarHandles
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass kotlin/sequences/ConstrainedOnceSequence
instanceKlass kotlin/sequences/GeneratorSequence
instanceKlass kotlin/sequences/Sequence
instanceKlass kotlin/sequences/SequencesKt__SequenceBuilderKt
instanceKlass java/io/Reader
instanceKlass kotlin/text/Charsets
instanceKlass org/jetbrains/kotlin/cli/common/arguments/ArgumentParseErrors
instanceKlass org/jetbrains/kotlin/cli/common/arguments/PreprocessCommandLineArgumentsKt
instanceKlass kotlin/UNINITIALIZED_VALUE
instanceKlass kotlin/SynchronizedLazyImpl
instanceKlass kotlin/Lazy
instanceKlass kotlin/LazyKt__LazyJVMKt
instanceKlass kotlin/jvm/internal/Lambda
instanceKlass kotlin/jvm/functions/Function0
instanceKlass org/jetbrains/kotlin/cli/common/arguments/ParseCommandLineArgumentsKt
instanceKlass kotlin/collections/ArraysUtilJVM
instanceKlass kotlin/collections/ArraysKt__ArraysJVMKt
instanceKlass java/lang/ProcessEnvironment$CheckedEntry
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet$1
instanceKlass java/lang/ProcessEnvironment$EntryComparator
instanceKlass java/lang/ProcessEnvironment$NameComparator
instanceKlass org/jetbrains/kotlin/config/JvmDefaultMode$Companion
instanceKlass org/jetbrains/kotlin/config/JVMAssertionsMode$Companion
instanceKlass org/jetbrains/kotlin/config/ExplicitApiMode$Companion
instanceKlass kotlin/collections/EmptyList
instanceKlass kotlin/collections/CollectionsKt__CollectionsJVMKt
instanceKlass org/jetbrains/kotlin/cli/common/arguments/K2JVMCompilerArguments$Companion
instanceKlass org/jetbrains/kotlin/cli/common/arguments/CommonCompilerArguments$Companion
instanceKlass org/jetbrains/kotlin/cli/common/arguments/CommonToolArguments$Companion
instanceKlass org/jetbrains/kotlin/config/Services$Builder
instanceKlass org/jetbrains/kotlin/config/Services$Companion
instanceKlass org/jetbrains/kotlin/cli/common/messages/XcodeStyleMessageRenderer
instanceKlass org/jetbrains/kotlin/cli/common/messages/GradleStyleMessageRenderer
instanceKlass java/lang/Class$3
instanceKlass kotlin/text/StringsKt__AppendableKt
instanceKlass org/jetbrains/kotlin/cli/common/PropertiesKt
instanceKlass org/jetbrains/kotlin/cli/common/messages/PlainTextMessageRenderer
instanceKlass org/jetbrains/kotlin/cli/common/messages/XmlMessageRenderer
instanceKlass org/jetbrains/kotlin/cli/common/messages/MessageRenderer
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/ApplicationManager
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/util/ArrayList$Itr
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass sun/invoke/util/Wrapper$1
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass java/lang/invoke/InnerClassLambdaMetafactory$1
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass jdk/internal/org/objectweb/asm/ClassReader
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Attachment
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$2
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/invoke/Invokers
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet
instanceKlass java/lang/invoke/LambdaMetafactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger$DefaultFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger$Factory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/IdeaStandaloneExecutionSetup
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/CompatKt
instanceKlass kotlin/jvm/functions/Function2
instanceKlass kotlin/jvm/internal/CallableReference$NoReceiver
instanceKlass kotlin/reflect/KDeclarationContainer
instanceKlass kotlin/jvm/internal/CallableReference
instanceKlass kotlin/reflect/KFunction
instanceKlass kotlin/reflect/KCallable
instanceKlass kotlin/reflect/KAnnotatedElement
instanceKlass kotlin/jvm/internal/FunctionBase
instanceKlass kotlin/jvm/functions/Function1
instanceKlass kotlin/Function
instanceKlass org/jetbrains/kotlin/cli/common/CompilerSystemProperties$Companion
instanceKlass kotlin/collections/AbstractList$Companion
instanceKlass kotlin/collections/AbstractCollection
instanceKlass kotlin/enums/EnumEntries
instanceKlass kotlin/jvm/internal/markers/KMappedMarker
instanceKlass kotlin/enums/EnumEntriesKt
instanceKlass org/jetbrains/kotlin/util/PerformanceCounter$Companion
instanceKlass org/jetbrains/kotlin/util/PerformanceCounter
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass java/nio/file/Paths
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass kotlin/jvm/internal/Intrinsics
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass java/util/TreeMap$Entry
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass org/jetbrains/kotlin/cli/jvm/K2JVMCompiler$Companion
instanceKlass org/jetbrains/kotlin/cli/common/CLICompiler$Companion
instanceKlass org/jetbrains/kotlin/cli/common/CLITool$Companion
instanceKlass java/lang/Void
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass org/jetbrains/kotlin/utils/KotlinPaths
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/Disposable
instanceKlass org/jetbrains/kotlin/cli/common/CommonCompilerPerformanceManager
instanceKlass org/jetbrains/kotlin/config/Services
instanceKlass org/jetbrains/kotlin/cli/common/arguments/Freezable
instanceKlass org/jetbrains/kotlin/config/CompilerConfiguration
instanceKlass org/jetbrains/kotlin/utils/exceptions/KotlinExceptionWithAttachments
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/ExceptionWithAttachments
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/ControlFlowException
instanceKlass org/jetbrains/kotlin/cli/common/CLITool
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass jdk/internal/loader/Resource
instanceKlass sun/security/action/GetIntegerAction
instanceKlass sun/security/util/Debug
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass jdk/internal/util/jar/JarIndex
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryImpl
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/net/URI$Parser
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/Arrays$ArrayItr
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass sun/security/action/GetBooleanAction
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/net/util/URLUtil
instanceKlass sun/launcher/LauncherHelper
instanceKlass jdk/internal/vm/PostVMInitHook$1
instanceKlass jdk/internal/util/EnvUtils
instanceKlass jdk/internal/vm/PostVMInitHook$2
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass jdk/internal/vm/PostVMInitHook
instanceKlass java/lang/reflect/Array
instanceKlass java/lang/invoke/StringConcatFactory$3
instanceKlass java/lang/invoke/StringConcatFactory$2
instanceKlass java/lang/invoke/StringConcatFactory$1
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/ModuleLayer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass java/util/function/Function
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/util/AbstractMap$1$1
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/lang/module/Configuration
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass jdk/internal/util/Preconditions
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass java/util/HexFormat
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/lang/Enum
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$default
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass java/util/Collections
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/charset/CoderResult
instanceKlass java/lang/Readable
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass sun/nio/cs/GBK$EncodeHolder
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/io/Writer
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/lang/Integer$IntegerCache
instanceKlass java/lang/CharacterData
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass java/lang/StringCoding
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass java/lang/StringUTF16
instanceKlass sun/nio/cs/DoubleByte
instanceKlass sun/nio/cs/GBK$DecodeHolder
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass sun/nio/cs/DelegatableDecoder
instanceKlass java/lang/reflect/Modifier
instanceKlass java/lang/Class$1
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass java/util/Arrays
instanceKlass java/lang/ThreadLocal
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass jdk/internal/misc/VM
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass java/lang/ref/ReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/Runtime
instanceKlass java/util/HashMap$Node
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/Map$Entry
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/Math
instanceKlass jdk/internal/reflect/Reflection
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/StringLatin1
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/invoke/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Map
instanceKlass java/util/Dictionary
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 92 7 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 3 8 1 100 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Class 1 1 1611 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 7 1 8 1 10 12 1 11 100 12 1 1 1 10 12 1 1 11 8 1 18 8 1 10 12 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 7 1 100 1 10 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 100 1 100 1 10 10 12 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 1 10 10 10 12 1 1 10 12 1 1 10 12 10 12 1 1 100 1 8 1 10 10 12 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 100 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 11 100 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 10 12 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 10 12 100 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 8 1 10 12 1 11 11 12 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 100 1 10 10 12 1 1 7 1 10 12 1 1 100 11 100 1 9 12 1 1 9 12 1 100 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 10 10 12 1 1 10 12 10 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 10 7 8 1 18 8 1 8 1 10 12 1 9 12 1 9 12 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 7 1 10 10 12 1 10 7 1 9 12 1 8 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 100 1 8 1 10 7 1 4 10 10 12 11 7 12 1 1 1 10 12 1 100 1 10 12 1 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 11 12 7 1 11 7 12 1 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 9 12 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 100 1 11 12 1 10 100 12 1 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 1 10 12 1 10 11 12 1 11 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 100 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 18 12 1 1 11 12 1 1 18 11 12 1 18 12 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 8 1 10 12 1 7 1 9 12 1 1 100 1 100 1 100 1 100 1 100 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 11 12 16 1 16 15 16 15 10 12 16 16 15 10 12 16 15 16 1 15 10 12 16 1 1 1 1 1 1 1 1 100 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/io/Serializable 1 0 7 100 1 100 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 487 100 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 1 100 1 10 12 1 1 11 100 12 1 1 11 100 12 1 1 100 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 100 1 10 12 1 1 11 100 12 1 1 100 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 1 100 1 9 12 1 1 10 100 12 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 0 0 196 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 10 100 12 1 1 100 1 9 12 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/util/Iterator 1 1 53 100 1 8 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/System 1 1 803 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 100 1 10 12 1 8 1 10 12 1 10 12 1 1 100 1 10 12 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 8 1 10 10 12 1 100 1 8 1 10 8 1 10 7 12 1 1 8 1 10 12 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 18 12 1 100 1 9 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 100 1 8 1 10 9 12 1 9 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 8 1 11 12 1 10 12 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 8 1 7 1 9 7 12 1 1 1 10 12 1 7 1 9 12 10 9 12 7 1 10 12 8 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 10 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 8 1 10 8 1 8 1 8 1 8 1 10 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 1 8 1 10 10 10 12 1 1 10 12 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 7 1 10 10 12 1 10 12 1 9 12 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 1 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/BufferedInputStream
staticfield java/lang/System out Ljava/io/PrintStream; java/io/PrintStream
staticfield java/lang/System err Ljava/io/PrintStream; java/io/PrintStream
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1098 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 100 12 1 10 7 1 10 7 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 100 1 10 12 1 10 100 12 1 1 1 10 10 12 1 1 10 12 1 1 100 1 8 1 10 8 1 10 12 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 1 8 1 8 1 10 7 12 1 1 100 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 12 1 10 7 1 10 12 1 100 1 18 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 8 1 100 1 10 10 12 1 9 12 1 10 7 12 1 1 10 12 1 100 1 8 1 10 12 1 10 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 100 1 100 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 7 1 18 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 18 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 12 1 10 11 12 1 1 10 18 10 12 1 1 11 100 12 1 18 12 1 11 12 1 1 10 12 10 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 8 1 8 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 100 1 10 11 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 9 12 1 1 9 12 9 12 1 9 12 1 9 12 1 8 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 11 12 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 1 15 10 12 16 1 16 15 10 12 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 1 1 100 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 1 1 18 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1
instanceKlass java/net/URLClassLoader
instanceKlass jdk/internal/loader/BuiltinClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 102 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 7 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 7 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 737 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 100 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 7 1 10 12 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 9 12 1 1 10 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 100 12 1 1 100 1 10 7 12 1 1 1 10 12 1 100 1 8 1 10 12 1 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 11 12 1 7 1 10 11 12 1 1 11 10 12 1 1 7 1 10 12 1 10 7 12 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 11 12 1 100 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 18 12 1 1 10 12 1 10 12 1 1 18 100 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 10 12 1 11 12 1 100 1 10 12 1 7 1 100 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 8 1 10 10 12 1 8 1 8 1 10 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 1 10 12 1 7 1 10 11 12 1 1 10 12 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 10 12 16 15 10 12 16 1 1 1 100 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/security/AccessController 1 1 295 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 10 11 7 12 1 1 1 10 7 12 1 1 11 7 1 100 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 100 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 100 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
instanceKlass jdk/internal/reflect/BootstrapConstructorAccessorImpl
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor1
instanceKlass jdk/internal/reflect/DelegatingConstructorAccessorImpl
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DelegatingMethodAccessorImpl
instanceKlass jdk/internal/reflect/NativeMethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 25 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1
ciInstanceKlass java/lang/Module 1 1 959 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 9 12 1 1 11 12 1 9 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 1 10 12 1 1 11 12 1 9 12 1 11 12 10 100 12 1 1 100 1 8 1 10 7 1 11 12 1 1 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 11 12 1 1 9 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 10 12 10 7 12 1 1 10 7 12 1 1 10 7 1 18 12 1 1 11 100 12 1 1 1 18 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 4 7 1 11 12 1 7 1 7 1 10 10 7 12 1 1 1 10 11 7 12 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 11 12 1 1 10 12 10 12 1 1 9 12 1 100 1 10 10 12 1 1 11 100 1 10 12 1 1 11 12 1 10 10 12 1 11 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 18 12 1 11 12 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 10 12 1 10 12 1 9 12 1 7 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 18 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 100 1 8 1 100 1 10 100 1 100 1 3 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 1 10 100 12 1 1 8 1 10 12 1 8 1 10 12 1 10 12 10 12 1 8 1 10 10 100 12 1 1 7 1 10 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 11 12 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 7 12 1 1 1 16 15 10 12 16 16 15 10 12 16 16 15 10 16 1 15 10 12 16 1 15 10 12 16 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/Stack
instanceKlass org/jetbrains/kotlin/config/MavenComparableVersion$ListItem
ciInstanceKlass java/util/ArrayList 1 1 492 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 11 7 12 1 1 1 9 12 1 1 10 12 1 1 7 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 11 100 12 1 1 1 11 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 11 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 100 1 8 1 10 100 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1210 7 1 7 1 3 10 12 1 1 3 100 1 10 7 12 1 1 1 100 1 10 100 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 4 10 12 1 9 12 1 10 12 1 1 100 1 10 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 7 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 9 10 12 1 1 9 12 1 10 12 1 1 5 0 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 7 1 10 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 11 100 1 10 12 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 9 10 12 1 9 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 100 1 10 12 11 100 12 1 1 10 11 7 12 1 10 12 1 100 1 10 12 1 100 1 10 10 9 100 12 1 1 1 10 12 3 10 100 12 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 9 12 1 9 7 12 1 1 10 12 1 1 10 12 1 3 9 12 1 9 12 1 10 12 1 1 7 1 9 3 9 12 1 100 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 100 12 1 1 1 100 10 12 1 100 1 5 0 10 100 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 100 1 100 1 10 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 8 10 12 1 1 8 8 8 8 7 10 12 1 1 10 12 1 100 1 8 1 10 7 1 100 1 100 1 1 1 5 0 1 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 16
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
ciInstanceKlass java/lang/String 1 1 1396 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 9 7 12 1 1 3 10 7 12 1 1 1 7 1 11 12 1 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 10 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 100 1 10 12 1 1 10 12 1 1 10 12 1 100 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 11 10 12 1 10 12 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 3 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 10 100 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 11 7 1 11 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 1 10 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 1 10 10 12 1 1 10 12 10 10 12 1 10 12 10 10 12 10 10 12 1 10 12 1 10 12 10 10 12 10 12 1 10 12 10 12 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 100 12 1 1 1 11 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 100 1 100 1 8 1 10 10 10 12 1 8 1 10 12 1 3 3 7 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 11 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 10 12 10 12 1 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 1 10 10 12 1 8 1 10 12 1 1 18 12 1 1 11 100 12 1 1 1 7 1 3 18 12 1 18 12 1 8 1 10 100 12 1 1 1 11 12 1 1 10 12 10 10 12 1 10 11 12 1 1 10 12 1 1 11 12 1 18 3 11 10 12 1 11 11 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 100 12 1 100 1 100 1 10 12 1 100 1 10 10 100 12 1 1 1 100 1 10 100 1 10 10 12 1 10 10 12 1 8 1 10 10 12 1 8 1 8 1 10 12 1 10 12 1 10 10 12 10 100 12 1 1 10 100 12 1 1 10 100 12 1 1 8 1 10 12 1 10 12 1 1 10 10 12 8 1 8 1 10 8 1 8 1 8 1 8 1 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 10 10 12 10 12 7 1 9 12 1 1 7 1 10 100 1 100 1 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 10 12 15 10 12 15 10 12 1 1 1 1 100 1 100 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciInstanceKlass java/security/ProtectionDomain 1 1 324 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 7 1 9 12 1 1 9 12 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 9 100 12 1 1 10 12 1 1 10 100 1 10 12 1 1 8 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 8 1 11 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 100 12 1 1 1 10 100 1 10 12 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 11 100 12 1 1 1 10 12 1 10 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 100 12 1 1 11 12 1 10 12 8 1 8 1 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/CodeSource 1 1 395 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 8 1 8 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 10 12 1 1 10 100 12 1 1 10 12 1 100 1 10 12 10 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 100 1 8 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 10 12 10 12 1 1 11 100 12 1 1 10 10 12 1 11 10 12 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 11 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StringBuilder 1 1 409 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 100 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 183 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 7 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1285 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 5 0 5 0 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 100 1 8 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 100 1 10 10 12 1 1 8 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 1 9 100 1 9 7 1 9 100 1 9 9 100 1 9 100 1 9 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 5 0 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 3 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 5 0 10 12 1 1 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 5 0 5 0 5 0 10 12 1 1 10 12 1 10 12 1 10 12 10 100 12 1 1 8 1 100 1 11 12 1 1 8 1 11 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 12 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
ciInstanceKlass java/util/Map 1 1 259 11 7 12 1 1 1 11 12 1 1 10 100 12 1 1 11 12 1 1 11 7 12 1 1 1 11 100 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 100 1 100 1 10 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 11 12 1 10 12 1 1 11 12 1 11 100 12 1 9 7 12 1 1 1 100 1 10 12 7 1 7 1 10 12 1 7 1 10 7 1 11 12 1 1 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadGroup 1 1 293 10 7 12 1 1 1 9 7 12 1 1 1 8 1 9 12 1 1 7 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 9 12 1 9 12 1 1 10 7 12 1 1 1 100 10 12 1 1 10 7 12 1 1 1 10 100 12 1 9 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 100 1 10 10 12 1 10 12 1 10 12 1 7 10 12 1 9 12 1 1 10 12 1 1 8 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 1 100 1 9 12 1 100 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 100 1 8 1 10 8 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Properties 1 1 651 10 7 12 1 1 1 100 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 7 1 10 12 10 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 3 10 10 100 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 9 100 12 1 1 1 10 12 1 10 12 1 1 100 1 10 10 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 10 12 1 1 8 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 100 12 1 1 9 100 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 1 100 1 10 10 12 1 11 7 12 1 1 10 7 12 1 1 1 8 1 10 100 12 1 1 11 8 1 10 100 1 11 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 100 1 10 11 100 12 1 1 4 11 10 12 1 1 10 100 12 1 1 11 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 100 1 6 0 10 12 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 512 100 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 7 1 9 12 1 1 4 10 7 12 1 1 1 9 12 1 4 10 12 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 100 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 3 9 12 1 9 12 1 3 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 9 12 1 1 10 100 1 100 1 10 12 1 10 8 1 10 10 12 1 8 1 10 8 1 10 100 12 1 1 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 11 12 1 1 11 12 1 100 1 10 10 10 100 12 1 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 8 1 10 4 10 12 4 10 12 1 8 1 10 12 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/com/intellij/util/io/UnsyncByteArrayInputStream
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/MergedStream
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 184 100 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 100 1 3 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 3 100 1 8 1 10 10 100 12 1 1 1 100 1 10 11 100 12 1 1 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 100 1 10 100 12 1 1 1 5 0 10 12 1 10 12 1 1 100 1 10 8 1 10 8 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 96 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
ciInstanceKlass java/lang/Thread 1 1 610 9 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 1 3 8 1 100 1 5 0 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 100 1 8 1 10 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 10 7 12 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 9 12 1 10 12 1 1 9 12 1 100 1 10 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 100 1 11 7 12 1 1 9 100 12 1 1 1 10 12 1 10 12 1 10 12 9 12 1 1 10 9 12 1 10 12 1 100 1 10 10 12 1 1 9 12 1 10 12 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 1 10 12 1 100 1 8 1 10 10 12 1 10 12 8 1 10 12 1 8 1 10 8 1 8 1 10 100 12 1 1 10 100 12 1 1 1 100 1 8 1 10 9 12 1 9 12 1 1 10 12 1 1 10 10 12 1 1 9 12 1 10 12 1 1 100 1 10 12 11 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 100 1 10 12 1 10 12 1 1 11 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 9 12 1 10 12 1 1 11 100 12 1 1 1 10 100 12 1 1 1 11 12 1 10 12 1 7 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
ciMethod java/lang/System arraycopy (Ljava/lang/Object;ILjava/lang/Object;II)V 768 0 384 0 -1
ciInstanceKlass java/lang/StringLatin1 1 1 380 7 1 10 100 12 1 1 1 100 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 100 1 100 1 8 1 10 12 1 8 1 10 12 100 1 10 10 10 7 12 1 1 1 8 1 8 1 8 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
staticfield java/lang/StringLatin1 $assertionsDisabled Z 1
ciInstanceKlass java/lang/Math 1 1 395 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 6 0 6 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 100 1 3 3 3 10 7 12 1 1 1 100 1 5 0 5 0 5 0 5 0 5 0 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 100 1 5 0 5 0 100 1 3 5 0 3 5 0 10 12 1 10 12 1 8 1 10 12 1 8 1 9 12 1 1 9 12 1 10 12 1 1 6 0 10 12 1 9 12 1 1 100 1 10 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 6 0 10 12 1 1 10 12 1 1 10 12 10 12 1 4 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 6 0 4 6 0 4 6 0 4 10 12 1 1 9 12 1 1 10 12 1 9 12 1 10 7 12 1 1 1 4 6 0 1 1 6 0 1 6 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Math negativeZeroFloatBits J -2147483648
staticfield java/lang/Math negativeZeroDoubleBits J -9223372036854775808
staticfield java/lang/Math $assertionsDisabled Z 1
ciInstanceKlass java/util/Arrays 1 1 988 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 100 1 10 12 1 9 100 12 1 1 1 10 7 12 1 1 100 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 7 1 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 100 1 11 12 1 1 10 12 1 10 7 12 1 1 1 10 12 10 12 1 10 12 1 10 12 10 12 1 11 100 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 7 1 10 12 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 100 1 10 12 1 9 100 1 100 1 10 12 1 9 100 1 100 1 10 12 1 9 100 1 100 1 10 12 1 9 100 1 100 1 10 12 1 9 100 1 100 1 10 12 1 9 100 1 10 12 1 100 1 10 12 1 10 12 1 9 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 3 10 100 1 10 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 11 12 1 8 1 10 11 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 18 12 1 1 11 12 1 1 11 100 12 1 1 1 18 12 1 11 100 12 1 1 1 18 12 1 11 100 12 1 1 1 18 12 1 100 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 10 12 10 12 1 10 12 10 12 1 10 12 1 10 12 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 15 10 12 15 10 12 15 10 12 1 1 100 1 100 1 1 1 1 100 1 1 1 1 1 1 100 1 1 100 1 1 100 1 1 1 100 1 100 1 1
staticfield java/util/Arrays $assertionsDisabled Z 1
ciMethod java/lang/StringLatin1 hashCode ([B)I 368 6778 1160 0 352
ciMethod java/lang/StringLatin1 charAt ([BI)C 690 0 231320 0 128
ciMethod java/lang/StringLatin1 lastIndexOf ([BII)I 218 12308 830 0 416
ciMethod java/lang/StringLatin1 newString ([BII)Ljava/lang/String; 266 0 5320 0 1184
ciMethod java/lang/StringLatin1 canEncode (I)Z 638 0 39625 0 96
ciInstanceKlass java/lang/StringUTF16 1 1 598 100 1 7 1 10 100 12 1 1 1 100 1 10 7 1 3 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 9 12 1 1 9 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 3 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 10 12 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 100 1 8 1 8 1 10 12 1 1 100 1 10 10 100 12 1 1 1 10 100 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 5 0 5 0 10 12 1 10 12 10 12 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1
staticfield java/lang/StringUTF16 HI_BYTE_SHIFT I 0
staticfield java/lang/StringUTF16 LO_BYTE_SHIFT I 8
staticfield java/lang/StringUTF16 $assertionsDisabled Z 1
ciMethod java/lang/StringUTF16 hashCode ([B)I 0 0 1 0 0
ciMethod java/lang/StringUTF16 checkIndex (I[B)V 0 0 1 0 -1
ciMethod java/lang/StringUTF16 getChar ([BI)C 1024 0 48661 0 -1
ciMethod java/lang/StringUTF16 charAt ([BI)C 0 0 1 0 0
ciMethod java/lang/StringUTF16 lastIndexOf ([BII)I 0 0 1 0 -1
ciMethod java/lang/StringUTF16 newString ([BII)Ljava/lang/String; 0 0 1 0 -1
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryClassSignatureParser$ClsFormatException
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 393 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 10 10 12 1 100 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 8 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 10 12 1 100 1 10 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 8 1 8 1 9 12 1 1 10 100 12 1 1 100 1 10 11 12 1 8 1 8 1 10 7 12 1 1 8 1 10 12 1 8 1 100 1 10 12 1 9 12 1 1 10 12 1 10 100 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 8 1 10 12 1 1 8 1 10 10 9 100 12 1 1 1 8 1 10 12 1 1 10 100 1 8 1 10 11 12 1 1 8 1 9 12 1 10 100 12 1 1 11 9 12 1 1 11 12 1 1 100 10 12 1 10 12 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
instanceKlass kotlin/reflect/full/IllegalPropertyDelegateAccessException
instanceKlass kotlin/reflect/full/IllegalCallableAccessException
instanceKlass org/jetbrains/kotlin/com/intellij/psi/scope/MethodProcessorSetupFailedException
instanceKlass java/text/ParseException
instanceKlass javax/xml/stream/XMLStreamException
instanceKlass org/jetbrains/kotlin/com/intellij/util/cls/ClsFormatException
instanceKlass java/net/URISyntaxException
instanceKlass java/lang/CloneNotSupportedException
instanceKlass java/lang/InterruptedException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/InstantiationException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionManager$CachingPreventedException
instanceKlass org/jetbrains/kotlin/com/intellij/core/CoreJavaCodeStyleManager$1CancelException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/StackOverflowPreventedException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/InvalidVirtualFileAccessException
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiInvalidElementAccessException
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/UncheckedStreamException
instanceKlass org/jetbrains/kotlin/com/intellij/diagnostic/PluginException
instanceKlass java/io/UncheckedIOException
instanceKlass java/util/MissingResourceException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/SortingException
instanceKlass org/jetbrains/kotlin/com/intellij/diagnostic/ImplementationConflictException
instanceKlass java/util/EmptyStackException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/RuntimeExceptionWithAttachments
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/IndexNotReadyException
instanceKlass java/lang/SecurityException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/ReadOnlyModificationException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/ReadOnlyFragmentModificationException
instanceKlass org/jetbrains/kotlin/com/intellij/util/lang/CompoundRuntimeException
instanceKlass org/jetbrains/kotlin/org/picocontainer/PicoException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionNotApplicableException
instanceKlass java/nio/file/FileSystemNotFoundException
instanceKlass org/jetbrains/kotlin/util/ServiceLoaderLite$ServiceLoadingException
instanceKlass org/jetbrains/kotlin/compiler/plugin/PluginProcessingException
instanceKlass org/jetbrains/kotlin/compiler/plugin/CliOptionProcessingException
instanceKlass org/jetbrains/kotlin/backend/common/CompilationException
instanceKlass org/jetbrains/kotlin/com/intellij/util/IncorrectOperationException
instanceKlass org/jetbrains/kotlin/utils/WrappedValues$WrappedProcessCanceledException
instanceKlass java/util/ConcurrentModificationException
instanceKlass java/lang/reflect/UndeclaredThrowableException
instanceKlass java/util/NoSuchElementException
instanceKlass kotlin/NoWhenBranchMatchedException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/IllegalStateException
instanceKlass kotlin/UninitializedPropertyAccessException
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/CompileEnvironmentException
instanceKlass org/jetbrains/kotlin/utils/KotlinExceptionWithAttachments
instanceKlass org/jetbrains/kotlin/analyzer/CompilationErrorException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/ProcessCanceledException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/nio/charset/UnsupportedCharsetException
instanceKlass java/nio/charset/IllegalCharsetNameException
instanceKlass java/nio/file/InvalidPathException
instanceKlass java/lang/NumberFormatException
ciInstanceKlass java/lang/IllegalArgumentException 1 1 35 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/io/IOError
instanceKlass kotlin/reflect/jvm/internal/KotlinReflectionInternalError
instanceKlass kotlin/NotImplementedError
instanceKlass kotlin/jvm/KotlinReflectionNotSupportedError
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
instanceKlass java/lang/ThreadDeath
ciInstanceKlass java/lang/Error 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/UnsatisfiedLinkError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 7 12 1 1 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadDeath 0 0 21 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackTraceElement 0 0 224 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 100 12 1 1 1 100 1 9 12 1 8 1 9 12 1 9 12 1 9 12 1 1 8 1 10 12 1 1 10 12 1 100 1 10 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 100 12 1 1 10 10 12 1 1 10 12 1 10 12 1 1 100 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/AbstractObjectCollection
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/UnsafeWeakList
instanceKlass java/util/AbstractQueue
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/AbstractReferenceCollection
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Multimaps$Entries
instanceKlass java/util/IdentityHashMap$Values
instanceKlass org/jetbrains/kotlin/com/google/common/collect/AbstractMapBasedMultimap$WrappedCollection
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ImmutableList
instanceKlass java/util/HashMap$Values
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 160 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 11 12 1 11 7 1 10 12 1 10 12 1 10 100 12 1 1 1 11 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 151 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 9 12 1 1 9 12 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 9 100 12 1 1 9 12 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 32 10 7 12 1 1 1 9 7 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 0 0 132 10 100 12 1 1 1 9 100 12 1 1 1 9 100 1 9 12 1 1 11 100 12 1 1 1 9 12 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 11 12 1 11 12 1 1 11 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 100 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 10 100 1 10 12 1 100 1 10 12 1 100 1 100 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/Character 1 1 576 7 1 100 1 100 1 9 12 1 1 8 1 9 12 1 1 100 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 3 3 3 3 3 10 12 1 1 10 12 1 3 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 3 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 5 0 10 12 1 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 10 12 1 9 12 1 1 100 1 10 10 12 1 10 12 1 1 3 10 100 12 1 1 1 10 12 1 10 100 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 10 12 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
ciInstanceKlass java/lang/Float 1 1 223 7 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 4 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 4 4 4 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Double 1 1 285 7 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 100 12 1 1 1 6 0 8 1 10 12 1 1 8 1 10 12 1 1 8 1 6 0 10 12 1 1 100 1 5 0 5 0 8 1 8 1 10 100 12 1 1 1 10 100 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 6 0 6 0 6 0 10 7 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 215 7 1 100 1 10 100 12 1 1 1 9 12 1 1 8 1 9 12 1 1 100 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 100 1 100 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 224 7 1 100 1 100 1 10 100 12 1 1 1 10 12 1 1 100 1 100 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 8 1 9 12 1 1 100 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 100 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 3 3 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Integer 1 1 445 7 1 100 1 7 1 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 3 10 12 1 1 3 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 1 100 1 10 11 10 12 1 1 8 1 10 12 1 1 8 1 100 1 10 12 1 1 10 12 1 1 5 0 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 9 12 1 1 9 12 1 1 10 12 1 10 7 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 5 0 3 3 3 3 10 12 1 3 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 3 3 3 3 3 3 9 12 1 1 100 1 100 1 100 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
staticfield java/lang/Integer sizeTable [I 10
ciInstanceKlass java/lang/Long 1 1 506 7 1 100 1 7 1 100 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 10 12 1 10 12 1 10 12 1 5 0 5 0 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 5 0 5 0 9 12 1 1 9 12 1 5 0 100 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 5 0 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 1 100 1 10 11 10 12 1 1 8 1 10 12 1 1 8 1 100 1 10 12 1 1 10 12 1 8 1 8 1 11 12 1 1 10 12 1 10 12 1 10 12 1 5 0 5 0 9 100 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 100 1 9 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 5 0 10 12 1 10 12 1 5 0 5 0 5 0 10 12 1 1 5 0 5 0 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 195 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 100 1 100 1 10 12 1 9 12 1 9 12 1 100 1 10 10 12 1 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/SoftKeySoftValueHashMap$ValueReference
instanceKlass org/jetbrains/kotlin/com/intellij/reference/SoftReference
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/com/intellij/util/PatchedWeakReference
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentWeakValueHashMap$MyWeakReference
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/UnsafeWeakList$MyReference
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/IntKeyWeakValueHashMap$MyReference
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 7 12 1 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 47 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 152 9 7 12 1 1 1 10 100 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 12 1 100 1 11 100 12 1 1 100 1 10 12 1 100 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 10 12 1 10 7 12 1 1 1 7 1 10 7 1 10 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 39 10 100 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 398 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 7 12 1 1 1 11 12 1 100 1 10 12 1 7 1 100 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 7 1 10 10 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 100 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 11 100 1 100 1 8 1 10 10 12 1 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 10 100 1 8 1 10 11 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 7 1 10 12 1 10 12 1 1 10 100 1 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 8 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 9 12 1 100 1 10 7 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 7 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 548 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 100 1 8 1 8 1 10 12 1 100 1 8 1 10 12 1 8 1 11 100 12 1 1 1 100 1 10 12 1 1 11 12 1 8 1 18 8 1 10 12 1 10 12 1 1 18 8 1 10 12 1 100 1 10 12 1 10 12 1 11 100 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 100 1 10 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 3 100 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 8 1 8 1 8 1 9 12 1 10 12 1 100 1 8 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 100 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 10 10 10 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 9 12 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 15 10 100 12 1 1 1 16 15 16 1 16 1 15 10 12 16 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 429 10 100 12 1 1 1 10 100 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 100 1 8 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 100 12 1 1 10 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Method 1 1 446 9 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 8 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 11 100 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 7 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Field 1 1 437 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 1 10 100 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 10 12 1 8 1 8 1 10 11 100 1 9 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 11 7 1 10 12 1 7 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Parameter 0 0 226 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 10 100 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 100 1 10 11 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass java/lang/StringBuffer 0 0 470 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 1 10 10 100 12 1 1 1 10 10 12 1 10 8 10 100 12 1 1 1 8 10 12 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 9 100 12 1 1 1 9 100 1 9 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/CharSequence 1 1 130 11 100 12 1 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 100 12 1 1 1 10 100 12 1 1 1 100 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 1 15 11 12 16 15 11 12 1 1 100 1 100 1 1 100 1 1 100 1 100 1 1
ciMethod java/lang/CharSequence length ()I 0 0 1 0 -1
ciMethod java/lang/CharSequence charAt (I)C 0 0 1 0 -1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 547 7 1 7 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 3 3 10 12 1 10 12 1 1 11 7 1 100 1 100 1 10 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 1 100 1 10 12 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 12 1 1 10 12 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 10 12 1 10 8 1 8 1 8 1 10 10 100 1 10 12 1 100 1 10 100 1 10 100 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 15 10 100 12 1 1 1 16 1 15 10 12 16 15 10 12 1 1 1 1 100 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciInstanceKlass java/lang/SecurityManager 0 0 576 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 1 10 100 1 10 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 100 1 8 1 10 9 12 1 1 9 12 1 8 1 9 12 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 1 10 12 1 1 8 1 100 1 8 1 10 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 8 1 100 1 8 1 8 1 10 8 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 11 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 18 11 12 1 18 12 1 11 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 18 12 1 18 10 100 12 1 1 1 18 12 1 10 12 1 18 18 8 1 10 12 1 9 12 1 1 11 100 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 8 1 100 1 10 9 12 1 8 1 10 12 1 8 1 100 1 10 10 100 12 1 1 10 100 1 9 100 12 1 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 100 12 1 1 1 16 1 16 15 10 12 16 1 15 10 12 16 15 11 100 1 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 1 16 1 15 11 12 1 15 10 12 16 15 10 16 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/security/AccessControlContext 1 1 373 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 12 1 11 12 1 11 12 1 1 100 1 11 12 1 1 10 12 1 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 7 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 10 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1
ciInstanceKlass java/net/URL 1 1 743 10 7 12 1 1 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 100 1 10 10 12 1 1 8 1 10 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 1 8 1 9 12 1 10 12 1 1 8 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 8 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 10 7 12 1 1 1 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 10 12 1 100 1 10 12 1 10 12 1 1 8 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 10 12 1 9 12 1 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 10 7 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 10 12 1 100 1 10 9 12 1 1 10 7 12 1 1 8 1 10 12 1 1 100 1 10 10 100 12 1 1 1 8 9 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 10 8 8 10 12 1 8 8 8 100 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 100 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 10 10 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 100 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/util/jar/Manifest 1 1 336 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 100 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 12 1 1 100 1 10 12 1 8 1 11 12 1 100 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 100 12 1 1 1 8 1 10 12 1 1 10 9 100 12 1 1 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 9 100 12 1 1 1 8 1 10 12 1 8 1 8 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 8 1 10 10 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 11 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 10 12 1 11 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/File 1 1 645 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 100 1 10 10 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 100 1 8 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 10 12 1 10 12 100 1 8 1 10 10 12 1 10 12 1 8 1 10 12 1 7 1 10 10 12 1 1 10 12 1 10 12 1 100 1 10 100 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 100 1 100 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 100 1 10 11 100 12 1 1 1 11 100 12 1 1 11 12 1 11 12 1 1 100 1 10 12 1 10 10 10 100 1 11 100 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 10 12 1 1 10 12 1 1 100 1 5 0 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 12 1 1 8 100 1 100 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/io/File fs Ljava/io/FileSystem; java/io/WinNTFileSystem
staticfield java/io/File separatorChar C 92
staticfield java/io/File separator Ljava/lang/String; "\"staticfield java/io/File pathSeparatorChar C 59
staticfield java/io/File pathSeparator Ljava/lang/String; ";"staticfield java/io/File UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/io/File PATH_OFFSET J 16
staticfield java/io/File PREFIX_LENGTH_OFFSET J 12
staticfield java/io/File $assertionsDisabled Z 1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/hash/LinkedHashMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentRefHashMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/RefHashMap
instanceKlass java/util/EnumMap
instanceKlass java/util/TreeMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/HashMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 192 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 10 12 1 1 11 12 1 100 1 10 11 12 1 11 7 1 10 12 1 1 11 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 100 1 10 11 11 12 1 1 11 12 1 100 1 100 1 11 12 1 8 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ContainerUtilRt$EmptyList
instanceKlass java/util/ArrayList$SubList
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/DisposableWrapperList
instanceKlass org/jetbrains/kotlin/com/intellij/util/SmartList
instanceKlass java/util/AbstractList$SubList
instanceKlass java/util/Vector
instanceKlass java/util/Collections$SingletonList
instanceKlass org/jetbrains/kotlin/utils/SmartList
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 11 12 1 1 11 12 1 10 100 12 1 1 1 10 12 1 11 12 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 100 1 11 7 1 10 12 1 100 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 100 1 10 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 8 1 8 1 8 1 10 100 1 11 10 10 12 1 11 12 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/invoke/NativeEntryPoint 0 0 92 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 11 100 12 1 1 1 10 12 1 1 10 12 1 11 100 12 1 1 11 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 302 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 10 12 1 1 100 1 100 1 10 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 10 12 1 1 9 12 1 9 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 10 12 1 1 9 12 1 8 1 100 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 10 12 1 1 9 12 1 1 100 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 100 1 8 1 10 10 12 10 12 1 1 100 1 100 1 100 1 8 1 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 37 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 771 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 100 12 1 1 1 9 7 1 9 7 1 10 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 11 12 1 1 7 7 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 100 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 11 12 1 1 11 12 1 10 100 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 9 12 1 1 7 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 12 1 11 12 1 100 1 11 100 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 10 100 12 1 1 10 12 1 100 10 12 1 1 10 12 1 10 7 1 7 1 9 12 1 1 100 1 100 1 100 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 16 15 10 12 16 1 1 1 1 100 1 1 100 1 1 100 1 100 1 1
staticfield java/lang/invoke/MethodType internTable Ljava/lang/invoke/MethodType$ConcurrentWeakInternSet; java/lang/invoke/MethodType$ConcurrentWeakInternSet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/Record 0 0 22 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 96 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 119 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 42 8 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
instanceKlass kotlin/KotlinNullPointerException
ciInstanceKlass java/lang/NullPointerException 1 1 52 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/InternalError 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 224 100 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 1 100 1 8 1 10 12 1 8 1 8 1 9 12 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 100 1 10 100 1 10 100 1 10 10 100 12 1 1 1 10 11 100 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 644 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 100 12 1 1 1 9 12 1 1 100 1 10 9 100 12 1 1 1 9 100 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 10 100 12 1 1 1 100 1 11 12 1 10 100 1 11 12 1 100 1 10 12 1 11 12 1 9 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 10 12 1 1 9 12 1 11 12 1 9 12 1 9 12 1 9 12 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 10 7 12 1 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 100 12 1 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 100 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 9 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 8 10 12 1 1 8 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 100 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 0 0 17 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 1 1 126 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 1 8 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1
staticfield jdk/internal/reflect/NativeConstructorAccessorImpl U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/reflect/NativeConstructorAccessorImpl GENERATED_OFFSET J 16
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 940 7 1 7 1 100 1 7 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 7 12 1 1 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 1 9 12 9 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 7 1 9 12 1 1 10 7 12 1 10 12 1 1 10 12 1 100 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 10 12 1 8 1 9 12 1 9 12 1 10 12 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 7 1 10 12 1 9 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 8 1 8 1 8 1 8 1 10 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 8 9 12 1 1 10 12 1 1 8 1 8 8 9 12 1 8 1 8 8 8 8 8 1 8 10 12 1 10 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1052 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 100 1 10 9 12 1 10 12 1 1 9 12 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 7 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 9 12 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 12 10 10 12 1 1 9 12 1 8 10 12 1 1 100 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 1 8 1 10 100 12 1 1 10 12 1 1 100 1 100 1 10 10 12 1 1 10 12 1 1 8 1 8 1 100 1 8 1 10 12 10 12 1 10 12 1 10 12 1 1 8 1 8 1 9 100 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 8 1 100 1 8 1 100 1 8 1 100 1 8 1 10 12 1 8 1 9 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 8 1 8 1 100 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 8 1 10 12 1 9 12 1 1 7 1 10 7 12 1 1 1 8 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 10 12 1 10 10 12 1 9 9 12 1 7 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 7 1 9 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 684 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 1 9 100 12 1 1 1 8 1 10 100 12 1 1 1 100 1 10 12 100 1 100 1 8 1 7 1 10 10 12 1 7 1 9 7 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 8 1 10 100 12 1 1 1 7 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 100 1 100 1 10 12 1 10 12 1 8 1 8 1 10 10 12 1 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 100 1 100 1 10 10 100 1 100 1 10 100 1 10 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 11 7 12 1 1 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/MethodHandleNatives JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 142 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeStaticIntegerFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeQualifiedStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 59 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 1 1 254 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 1 8 1 8 1 8 1 10 12 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/reflect/UnsafeFieldAccessorImpl unsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 63 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/invoke/VarHandleReferences$Array
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
ciInstanceKlass java/lang/invoke/VarHandle 1 1 390 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 9 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 100 12 1 1 100 1 10 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 10 7 12 1 1 1 9 12 1 1 8 10 12 1 1 7 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/VarHandle AIOOBE_SUPPLIER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 757 7 1 7 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 100 12 1 1 10 12 1 100 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 1 7 1 10 10 12 1 1 100 1 100 1 10 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 1 10 12 1 9 12 1 1 3 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 8 10 12 1 1 10 12 1 1 8 1 9 100 1 8 9 100 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 8 1 8 1 100 1 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 3 10 12 1 3 10 12 1 3 3 3 3 3 3 3 100 1 10 12 1 10 7 12 1 1 1 10 12 1 3 9 12 1 10 12 1 1 3 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 100 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 10 10 12 100 1 10 10 10 12 1 1 10 12 1 1 10 10 12 1 8 10 100 1 10 12 1 10 100 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 7 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 8 1 10 10 12 1 10 12 1 8 1 8 1 10 10 12 1 8 1 10 100 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 100 1 10 8 1 8 1 8 1 8 1 10 12 1 100 1 100 1 100 1 10 100 1 10 100 1 10 100 12 1 1 1 9 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackWalker 0 0 235 9 100 12 1 1 1 10 100 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 8 1 10 10 100 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 9 100 12 1 1 11 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 0 306 100 1 100 1 3 10 100 12 1 1 1 10 100 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 9 100 12 1 1 1 10 100 12 1 1 9 12 1 8 1 5 0 8 1 8 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/module/Modules 1 1 504 10 100 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 100 12 1 1 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 11 12 1 9 12 1 1 11 100 12 1 1 1 10 12 1 1 10 10 12 1 10 9 12 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 12 1 1 18 12 1 1 11 100 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 100 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 1 11 12 1 1 10 12 1 18 18 10 12 1 1 9 12 1 1 11 100 12 1 1 1 100 1 10 11 12 1 11 12 1 1 11 12 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 10 12 1 1 100 1 10 18 12 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 18 12 1 11 11 12 10 12 1 10 10 100 1 18 12 1 10 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 1 16 16 15 10 12 1 16 1 16 1 15 10 12 1 16 1 16 1 15 10 12 16 1 15 10 16 1 15 10 12 16 1 15 10 12 16 15 10 12 16 15 10 12 1 1 1 100 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
ciMethod java/util/Arrays copyOfRange ([BII)[B 514 0 5428 0 768
ciMethod java/lang/Math min (II)I 512 0 21731 0 -1
ciMethod java/util/Map put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/util/Map get (Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/lang/StringBuilder <init> ()V 174 0 8371 0 -1
ciMethod java/lang/StringBuilder toString ()Ljava/lang/String; 182 0 9475 0 -1
ciMethod java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 358 0 24291 0 -1
ciMethod java/lang/StringBuilder append (Ljava/lang/Object;)Ljava/lang/StringBuilder; 14 0 120 0 -1
ciMethod java/lang/String isEmpty ()Z 512 0 34204 0 96
ciMethod java/lang/String charAt (I)C 576 0 231611 0 160
ciMethod java/lang/String length ()I 590 0 255043 0 96
ciMethod java/lang/String <init> ([BB)V 514 0 19468 0 0
ciMethod java/lang/String hashCode ()I 1024 0 5689 0 480
ciMethod java/lang/String coder ()B 618 0 328661 0 64
ciMethod java/lang/String isLatin1 ()Z 1024 0 302803 0 96
ciMethod java/lang/String checkBoundsBeginEnd (III)V 514 0 5390 0 160
ciMethod java/lang/String lastIndexOf (II)I 512 0 25179 0 0
ciMethod java/lang/String lastIndexOf (I)I 512 0 23118 0 0
ciMethod java/lang/String substring (II)Ljava/lang/String; 512 0 5510 0 1344
ciMethod java/lang/String substring (I)Ljava/lang/String; 592 0 15969 0 0
ciMethod java/lang/Object <init> ()V 1024 0 134958 0 64
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/DefaultLogger
ciInstanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger 1 1 409 1 7 1 7 1 1 100 1 7 1 1 7 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 100 1 1 12 10 1 1 12 10 12 9 1 7 1 1 12 10 1 12 10 1 1 12 10 1 100 1 1 12 10 1 1 12 10 1 12 10 1 100 1 12 10 1 1 1 1 1 1 1 1 1 1 1 12 10 1 100 1 1 12 9 1 7 10 1 8 1 1 12 10 1 1 12 10 1 8 1 12 10 1 100 10 1 100 1 1 12 10 1 12 10 1 100 1 1 12 10 1 1 1 1 1 12 11 1 1 1 1 1 8 1 12 10 1 1 1 1 1 1 1 12 10 1 100 1 12 10 12 10 1 1 1 1 1 1 1 1 1 1 8 1 100 1 1 12 11 1 12 10 1 8 1 12 11 1 8 1 1 12 11 1 100 1 12 11 1 1 12 11 1 8 1 1 1 1 1 1 1 10 12 10 1 1 1 12 10 12 10 1 12 10 1 12 10 1 1 1 12 10 12 10 12 10 1 12 10 1 100 1 1 12 9 1 12 10 1 1 100 1 1 12 10 12 10 1 1 12 10 1 1 12 9 1 100 1 1 12 10 16 1 100 1 12 11 15 1 16 1 7 1 1 12 10 15 1 1 12 18 1 100 1 1 12 10 100 1 1 1 1 1 1 8 1 8 12 10 1 1 1 12 10 1 1 1 1 1 100 1 8 1 12 10 1 8 12 10 1 1 12 9 1 12 10 12 10 1 1 100 1 12 10 1 12 10 1 1 1 1 12 10 12 10 15 1 12 18 1 8 1 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 12 10 1 100 10 1 100 10 1 1 1 1 1 1 1 1 1 1 1 1
staticfield org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger ATTACHMENT_TO_STRING Ljava/util/function/Function; org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger$$Lambda$1+0x0000017c8c009bc0
ciInstanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/DefaultLogger 1 1 225 1 7 1 7 1 1 100 1 100 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 100 1 1 12 9 1 100 10 1 8 1 1 12 10 1 1 12 10 1 100 1 12 10 1 100 1 1 12 10 1 1 1 1 1 12 10 1 100 1 100 1 1 12 10 1 12 10 1 100 12 10 1 1 12 10 1 8 1 8 1 1 1 1 1 1 8 1 100 1 100 1 1 12 10 1 100 1 1 12 11 1 16 1 1 12 10 15 16 1 100 1 1 12 10 15 1 1 12 18 1 100 1 1 12 11 1 1 12 9 1 12 11 1 8 1 8 1 100 1 1 12 10 1 1 12 11 1 1 12 10 1 1 12 9 1 1 12 11 1 1 12 11 1 1 1 1 8 1 8 1 100 1 100 8 8 8 1 8 8 8 8 8 1 8 1 1 12 10 1 100 12 10 1 100 10 1 100 1 1 1 1 1 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/jar/CoreJarHandler
ciInstanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipHandler 1 1 159 1 7 1 7 1 1 7 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 10 1 1 1 1 1 1 1 1 100 1 100 12 9 1 1 12 10 1 1 12 10 1 7 1 1 12 10 1 7 1 7 1 7 1 1 12 10 1 1 12 11 1 7 1 1 12 10 12 9 1 12 11 12 9 1 1 12 10 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 10 1 1 1 12 10 1 1 1 1 1 1 1 12 10 1 8 1 8 1 100 1 100 1 100 8 8 8 8 1 1 12 10 1 100 10 1 100 10 1 1 1 1 1 1 1 1 1 1 1 1
staticfield org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipHandler ourZipFileFileAccessorCache Lorg/jetbrains/kotlin/com/intellij/util/io/FileAccessorCache; org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipHandler$1
ciInstanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/jar/CoreJarHandler 1 1 258 1 7 1 7 1 1 7 1 100 1 1 7 1 7 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 12 9 1 7 1 12 10 1 1 12 10 1 16 1 1 12 10 15 16 1 7 1 1 12 10 15 1 1 12 18 1 7 1 1 12 10 1 1 12 11 1 7 1 1 12 11 1 7 1 1 12 11 1 1 12 11 1 1 12 10 1 7 1 1 12 10 1 12 11 1 7 1 1 12 11 1 8 1 1 12 10 12 9 1 1 12 11 1 7 11 1 12 11 1 12 11 1 7 1 1 12 9 1 1 12 11 7 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 1 12 9 1 1 12 9 5 0 1 100 1 1 12 9 1 12 9 1 12 10 1 1 12 11 1 1 1 1 12 10 1 1 1 1 100 1 7 10 1 1 8 1 8 1 100 1 100 1 100 8 8 8 8 8 8 8 8 8 8 1 1 12 10 1 100 10 1 100 10 1 100 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipHandlerBase
ciInstanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler 1 1 350 1 7 1 7 1 1 7 1 1 7 1 100 1 100 1 1 100 1 100 1 1 100 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 12 9 1 7 1 12 10 12 9 12 9 1 7 12 10 12 9 1 1 1 1 1 1 1 1 1 100 1 12 10 12 9 1 100 1 1 1 1 1 12 10 1 7 1 1 12 11 1 1 1 100 1 1 12 10 1 100 1 12 10 1 12 10 1 1 12 10 1 100 1 1 12 10 1 100 10 1 1 12 10 1 1 12 10 1 8 1 12 10 1 12 10 1 1 12 10 1 1 1 1 1 1 100 1 1 1 8 5 0 1 12 10 1 1 1 1 1 12 10 1 7 1 1 12 10 1 8 1 1 12 10 1 8 1 1 12 10 1 100 1 12 10 1 8 12 10 1 8 1 12 10 1 7 1 1 12 10 1 8 1 12 10 1 7 1 12 9 1 12 9 1 1 12 11 1 12 11 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 12 10 1 7 1 1 12 10 1 12 10 1 1 12 9 12 10 1 7 1 1 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 1 12 9 1 12 10 12 9 10 12 9 1 8 1 8 1 100 8 8 8 8 8 8 1 8 8 8 1 8 1 8 8 1 8 8 8 1 8 1 1 12 10 1 100 10 1 100 10 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler DIRECTORY_ATTRIBUTES Lorg/jetbrains/kotlin/com/intellij/openapi/util/io/FileAttributes; org/jetbrains/kotlin/com/intellij/openapi/util/io/FileAttributes
staticfield org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler ourKeyValueMapper Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/AddonlyKeylessHash$KeyValueMapper; org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Couple
ciInstanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Pair 1 1 155 1 7 1 1 7 1 1 100 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 12 10 10 1 1 1 1 1 12 10 1 1 1 1 1 12 9 1 1 1 12 9 1 1 1 1 12 9 1 1 12 10 1 1 1 1 1 1 1 1 100 1 1 12 10 1 1 1 12 10 1 1 1 1 1 100 10 1 8 1 1 12 10 1 12 10 1 8 1 8 12 10 1 1 1 10 1 1 10 1 12 10 1 8 1 8 1 100 1 100 8 8 8 8 8 1 1 12 10 1 100 1 12 10 1 100 10 1 100 1 1 1 1 1 1 1 1 1 1
staticfield org/jetbrains/kotlin/com/intellij/openapi/util/Pair EMPTY Lorg/jetbrains/kotlin/com/intellij/openapi/util/Pair; org/jetbrains/kotlin/com/intellij/openapi/util/Pair
ciInstanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap 1 1 228 1 7 1 1 7 1 1 100 1 100 1 1 7 1 100 1 1 1 1 1 1 1 1 1 1 12 10 12 9 1 7 1 1 12 10 12 9 1 1 1 1 1 1 1 12 10 1 7 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 12 10 3 1 1 1 12 10 1 12 10 1 1 1 100 1 7 1 12 11 1 1 12 9 1 7 1 1 12 10 1 12 9 1 1 12 11 1 1 1 1 12 10 1 1 7 12 10 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 1 1 100 10 1 1 12 9 1 12 10 1 1 1 1 1 7 1 1 12 10 1 7 1 1 12 10 1 100 1 12 10 1 12 10 1 12 10 12 10 1 1 1 1 1 8 1 8 1 100 1 100 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 12 10 1 100 1 12 10 1 100 10 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo 1 1 64 1 7 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 12 9 12 9 12 9 12 9 12 9 1 1 8 8 8 8 1 100 1 1 12 10 1 100 1 12 10 1 1 1 1 1 1 1 1
ciInstanceKlass org/jetbrains/kotlin/com/intellij/util/text/CharArrayUtil 1 1 273 1 7 1 100 1 1 1 1 1 1 12 10 1 7 1 1 12 11 1 12 10 1 1 1 1 1 1 1 12 10 1 1 100 1 12 11 1 100 10 1 7 1 12 10 1 12 10 1 1 12 10 1 7 1 1 12 11 1 12 11 1 100 10 1 100 10 100 1 1 12 11 1 1 1 1 1 1 1 1 1 1 12 11 1 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 1 7 1 1 12 10 10 10 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 100 1 1 12 10 1 8 1 1 12 10 1 12 10 1 8 1 8 1 8 1 1 12 10 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 7 1 1 12 10 1 1 8 1 8 1 100 8 8 8 8 8 8 8 8 8 1 8 1 8 1 8 1 8 1 8 8 8 1 8 8 8 8 1 8 1 8 8 1 8 8 8 1 8 8 1 8 8 1 8 1 8 1 1 12 10 1 100 10 1 100 1 1 1 1 1 1 1 1
ciMethodData java/lang/Object <init> ()V 2 134963 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 124 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String isLatin1 ()Z 2 302837 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 124 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x30007 0x0 0x58 0x49cf6 0xa0007 0x2 0x38 0x49cf4 0xe0003 0x49cf4 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 hashCode ([B)I 2 28166 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0xd0007 0x3d0 0x38 0x60c0 0x250003 0x60c0 0xffffffffffffffe0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String hashCode ()I 2 5689 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 124 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 42 0x60007 0xe2e 0x108 0x60c 0xd0007 0x2e 0xe8 0x5de 0x110005 0x5de 0x0 0x0 0x0 0x0 0x0 0x140007 0x1 0x48 0x5dd 0x1b0002 0x5dd 0x1e0003 0x5dd 0x28 0x250002 0x1 0x2a0007 0x5dd 0x38 0x1 0x320003 0x1 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xe oops 0 methods 0
ciMethodData java/lang/StringUTF16 hashCode ([B)I 1 17 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 124 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0xb0007 0x1 0x48 0x11 0x140002 0x11 0x1c0003 0x11 0xffffffffffffffd0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String coder ()B 2 328673 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x30007 0x0 0x38 0x502ac 0xa0003 0x502ac 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String length ()I 2 255071 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x60005 0x3e338 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String charAt (I)C 2 231647 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x10005 0x387bd 0x0 0x0 0x0 0x0 0x0 0x40007 0x1 0x30 0x387bc 0xc0002 0x387bc 0x150002 0x1 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 charAt ([BI)C 2 231332 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 124 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x10007 0x0 0x40 0x3864b 0x70007 0x3864b 0x30 0x0 0xf0002 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringUTF16 charAt ([BI)C 1 1 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 124 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x20002 0x1 0x70002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String isEmpty ()Z 2 34294 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x50007 0x748a 0x38 0x106c 0x90003 0x106c 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 canEncode (I)Z 2 39625 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x40007 0x0 0x38 0x998a 0x80003 0x998a 0x18 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 newString ([BII)Ljava/lang/String; 2 5320 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 124 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 19 0x10007 0x1441 0x20 0x2 0x100002 0x1441 0x140002 0x1441 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String <init> ([BB)V 2 19468 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x10002 0x4b0b 0x0 0x0 0x0 0x0 0x9 0x3 0xc 0x0 0x0 oops 0 methods 0
ciMethodData java/util/Arrays copyOfRange ([BII)[B 2 5428 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 51 0x50007 0x1433 0x120 0x0 0x100002 0x0 0x140005 0x0 0x0 0x0 0x0 0x0 0x0 0x1a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x210005 0x0 0x0 0x0 0x0 0x0 0x0 0x240002 0x0 0x370002 0x1433 0x3a0002 0x1433 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 lastIndexOf ([BII)I 2 40368 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x10002 0x2d1 0x40007 0x2d1 0x20 0x0 0xe0002 0x2d1 0x130007 0x5a 0x58 0x881d 0x1b0007 0x85a6 0x20 0x277 0x230003 0x85a6 0xffffffffffffffc0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String lastIndexOf (II)I 2 25179 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 29 0x10005 0x615b 0x0 0x0 0x0 0x0 0x0 0x40007 0x0 0x48 0x615b 0xd0002 0x615b 0x100003 0x615b 0x28 0x190002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String checkBoundsBeginEnd (III)V 2 5390 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 76 0x10007 0x0 0x60 0x140d 0x60007 0x0 0x40 0x140d 0xb0007 0x140d 0x1c8 0x0 0x160002 0x0 0x1c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x200005 0x0 0x0 0x0 0x0 0x0 0x0 0x260005 0x0 0x0 0x0 0x0 0x0 0x0 0x2a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x300005 0x0 0x0 0x0 0x0 0x0 0x0 0x340005 0x0 0x0 0x0 0x0 0x0 0x0 0x370005 0x0 0x0 0x0 0x0 0x0 0x0 0x3a0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String lastIndexOf (I)I 2 23118 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x30005 0x594e 0x0 0x0 0x0 0x0 0x0 0x80005 0x594e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String substring (II)Ljava/lang/String; 2 5510 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 46 0x10005 0x1486 0x0 0x0 0x0 0x0 0x0 0x80002 0x1486 0xc0007 0x382 0x40 0x1104 0x110007 0xe1f 0x20 0x2e5 0x1c0005 0x11a1 0x0 0x0 0x0 0x0 0x0 0x1f0007 0x0 0x48 0x11a1 0x290002 0x11a1 0x2c0003 0x11a1 0x28 0x360002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String substring (I)Ljava/lang/String; 2 16029 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x30005 0x3d75 0x0 0x0 0x0 0x0 0x0 0x60005 0x3d75 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger debug (Ljava/lang/String;)V 0 0 1 0 -1
ciMethod org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger trace (Ljava/lang/String;)V 0 0 1 0 -1
ciMethod org/jetbrains/kotlin/com/intellij/openapi/diagnostic/DefaultLogger debug (Ljava/lang/String;)V 0 0 1 0 -1
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler getFile ()Ljava/io/File; 8 0 137 0 -1
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler createRootEntry ()Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 4 0 48 0 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler directoryEntry (Ljava/util/Map;Lorg/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger;Ljava/lang/String;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 598 0 8113 0 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler split (Ljava/lang/String;)Lorg/jetbrains/kotlin/com/intellij/openapi/util/Pair; 544 0 8045 0 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler $$$reportNull$$$0 (I)V 0 0 1 0 -1
ciMethod org/jetbrains/kotlin/com/intellij/openapi/util/Pair <init> (Ljava/lang/Object;Ljava/lang/Object;)V 512 0 8189 0 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap $$$reportNull$$$0 (I)V 0 0 1 0 -1
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap get (Ljava/lang/Object;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 630 28 11014 0 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap index (Ljava/lang/String;[Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)I 520 0 19329 0 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap put (Ljava/lang/String;Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 520 0 9096 0 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap put (Ljava/lang/String;Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;[Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 520 56 9427 0 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap isTheOne (Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;Ljava/lang/CharSequence;)Z 362 940 13034 0 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap rehash ()V 2 20 22 0 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap getRelativePath (Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)Ljava/lang/String; 538 2202 310 0 -1
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 514 0 9093 0 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap get (Ljava/lang/Object;)Ljava/lang/Object; 622 0 12443 0 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo <init> (Ljava/lang/CharSequence;ZJJLorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)V 514 0 8093 0 0
ciMethod org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo $$$reportNull$$$0 (I)V 0 0 1 0 -1
ciMethod org/jetbrains/kotlin/com/intellij/util/text/CharArrayUtil regionMatches (Ljava/lang/CharSequence;IILjava/lang/CharSequence;)Z 524 1806 11197 0 832
ciMethod org/jetbrains/kotlin/com/intellij/util/text/CharArrayUtil $$$reportNull$$$0 (I)V 0 0 1 0 -1
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap index (Ljava/lang/String;[Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)I 2 19329 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 29 0x10007 0x4a7d 0x30 0x0 0x50002 0x0 0x90007 0x4a7d 0x30 0x0 0xd0002 0x0 0x110005 0x4a7d 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/util/text/CharArrayUtil regionMatches (Ljava/lang/CharSequence;IILjava/lang/CharSequence;)Z 2 28625 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 64 0x10007 0x2ab7 0x30 0x0 0x60002 0x0 0xa0007 0x2ab7 0x30 0x0 0xf0002 0x0 0x130005 0x0 0x0 0x17c8a7c3400 0x2ab7 0x0 0x0 0x1f0007 0x2ab7 0x20 0x0 0x250007 0x26af 0x20 0x408 0x310007 0x1b77 0xc8 0x7782 0x390005 0x0 0x0 0x17c8a7c3400 0x7782 0x0 0x0 0x410005 0x0 0x0 0x17c8a7c3400 0x7782 0x0 0x0 0x460007 0x6c4a 0x20 0xb38 0x4e0003 0x6c4a 0xffffffffffffff50 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 3 15 java/lang/String 34 java/lang/String 41 java/lang/String methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap get (Ljava/lang/Object;)Ljava/lang/Object; 2 12443 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x20005 0x2f64 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap get (Ljava/lang/Object;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 2 11014 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 49 0x10007 0x29cb 0x30 0x0 0x50002 0x0 0x90004 0x0 0x0 0x17c8a7c3400 0x29cb 0x0 0x0 0x120002 0x29cb 0x240007 0x15e4 0xc0 0x2924 0x2a0002 0x2924 0x2d0007 0x153d 0x38 0x13e9 0x300003 0x13e9 0x70 0x3d0007 0x1530 0x20 0xd 0x460007 0x153d 0xffffffffffffff78 0x0 0x4c0003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 9 java/lang/String methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap isTheOne (Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;Ljava/lang/CharSequence;)Z 2 22381 orig 80 1 0 0 0 1 0 0 0 0 0 0 0 124 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 103 0x10007 0x327e 0x30 0x0 0x60002 0x0 0xa0007 0x327e 0x30 0x0 0xf0002 0x0 0x130005 0x0 0x0 0x17c8a7c3400 0x327e 0x0 0x0 0x1c0007 0x1287 0x218 0x758e 0x290005 0x0 0x0 0x17c8a7c3400 0x758e 0x0 0x0 0x300005 0x0 0x0 0x17c8a7c3400 0x758e 0x0 0x0 0x370002 0x758e 0x3a0007 0x55a4 0x20 0x1fea 0x420005 0x0 0x0 0x17c8a7c3400 0x55a4 0x0 0x0 0x4d0007 0x1287 0x108 0x431d 0x570005 0x0 0x0 0x17c8a7c3400 0x431d 0x0 0x0 0x5c0007 0x1240 0xb0 0x30dd 0x600007 0x0 0x90 0x30dd 0x670005 0x0 0x0 0x17c8a7c3400 0x30dd 0x0 0x0 0x80000006006e0007 0x10 0x38 0x30d0 0x740003 0x30d0 0x18 0x7e0003 0x5597 0xfffffffffffffe00 0x820007 0x2a 0x38 0x125d 0x860003 0x125d 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 6 15 java/lang/String 26 java/lang/String 33 java/lang/String 46 java/lang/String 57 java/lang/String 72 java/lang/String methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap put (Ljava/lang/String;Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;[Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 2 9476 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 58 0x10007 0x23ff 0x30 0x0 0x50002 0x0 0x90007 0x23ff 0x30 0x0 0xd0002 0x0 0x110007 0x23ff 0x30 0x0 0x150002 0x0 0x1a0002 0x23ff 0x290007 0x23ff 0x50 0x1487 0x2f0002 0x1487 0x320007 0x1487 0x70 0x0 0x390004 0x0 0x0 0x17cd0d17a20 0x23ff 0x0 0x0 0x3a0003 0x23ff 0x50 0x440007 0x1476 0xffffffffffffff60 0x11 0x4a0003 0x11 0xffffffffffffff40 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 1 33 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/util/Pair <init> (Ljava/lang/Object;Ljava/lang/Object;)V 2 8250 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x10002 0x1f39 0x0 0x0 0x0 0x0 0x9 0x3 0x6 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo <init> (Ljava/lang/CharSequence;ZJJLorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)V 2 9012 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x10007 0x2232 0x30 0x0 0x50002 0x0 0x90002 0x2232 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x8 0x3e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 9104 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 32 0x20004 0x0 0x0 0x17c8a7c3400 0x228e 0x0 0x0 0x60004 0x0 0x0 0x17cd0d17a20 0x228e 0x0 0x0 0x90005 0x228e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 2 3 java/lang/String 10 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap put (Ljava/lang/String;Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 2 9124 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 124 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0xe0007 0x228b 0x30 0x15 0x120002 0x15 0x1b0002 0x22a0 0x200007 0x0 0x20 0x22a0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap rehash ()V 1 626 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 31 0x80007 0x0 0x38 0x16 0x120003 0x16 0x18 0x300007 0x16 0x78 0x268 0x3b0007 0xf0 0x40 0x178 0x400002 0x178 0x460002 0x178 0x4d0003 0x268 0xffffffffffffffa0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x8 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler split (Ljava/lang/String;)Lorg/jetbrains/kotlin/com/intellij/openapi/util/Pair; 2 8134 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 46 0x30005 0x1eb6 0x0 0x0 0x0 0x0 0x0 0x80007 0x63 0x70 0x1e53 0xe0005 0x1e53 0x0 0x0 0x0 0x0 0x0 0x110003 0x1e53 0x18 0x180007 0x63 0x70 0x1e53 0x1f0005 0x1e53 0x0 0x0 0x0 0x0 0x0 0x220003 0x1e53 0x18 0x2d0002 0x1eb6 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler directoryEntry (Ljava/util/Map;Lorg/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger;Ljava/lang/String;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 2 8113 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 141 0x20005 0x0 0x0 0x17cd0d18000 0x1e86 0x0 0x0 0x70104 0x0 0x0 0x17cd0d17a20 0x1b9d 0x0 0x0 0xe0007 0x2e9 0x40 0x1b9d 0x160007 0x1b9d 0x378 0x0 0x1a0007 0x0 0x1d8 0x2e9 0x1f0007 0x2e9 0x1b8 0x0 0x270002 0x0 0x2c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x300005 0x0 0x0 0x0 0x0 0x0 0x0 0x330005 0x0 0x0 0x0 0x0 0x0 0x0 0x380005 0x0 0x0 0x0 0x0 0x0 0x0 0x3c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x420005 0x0 0x0 0x0 0x0 0x0 0x0 0x460005 0x2e9 0x0 0x0 0x0 0x0 0x0 0x490007 0x2bb 0x70 0x2e 0x4d0005 0x0 0x0 0x17cd0d180b0 0x2e 0x0 0x0 0x520003 0x2e 0xb8 0x560002 0x2bb 0x630004 0x0 0x0 0x17c8a7c3400 0x2bb 0x0 0x0 0x660002 0x2bb 0x740004 0x0 0x0 0x17c8a7c3400 0x2bb 0x0 0x0 0x7e0002 0x2bb 0x870005 0x0 0x0 0x17cd0d18000 0x2e9 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 6 3 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap 10 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo 95 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/jar/CoreJarHandler 107 java/lang/String 116 java/lang/String 125 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler getFile ()Ljava/io/File; 1 137 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x50007 0x85 0x30 0x0 0x90002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger trace (Ljava/lang/String;)V 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x20005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/diagnostic/DefaultLogger debug (Ljava/lang/String;)V 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 124 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 5 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler createRootEntry ()Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 1 68 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0xc0002 0x42 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
compile org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler directoryEntry (Ljava/util/Map;Lorg/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger;Ljava/lang/String;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; -1 4 inline 188 0 -1 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler directoryEntry (Ljava/util/Map;Lorg/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger;Ljava/lang/String;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 1 2 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap get (Ljava/lang/Object;)Ljava/lang/Object; 2 2 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap get (Ljava/lang/Object;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 3 18 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap index (Ljava/lang/String;[Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)I 4 17 java/lang/String hashCode ()I 5 17 java/lang/String isLatin1 ()Z 5 27 java/lang/StringLatin1 hashCode ([B)I 3 42 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap isTheOne (Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;Ljava/lang/CharSequence;)Z 4 19 java/lang/String length ()I 5 6 java/lang/String coder ()B 4 41 java/lang/String length ()I 5 6 java/lang/String coder ()B 4 48 java/lang/String length ()I 5 6 java/lang/String coder ()B 4 55 org/jetbrains/kotlin/com/intellij/util/text/CharArrayUtil regionMatches (Ljava/lang/CharSequence;IILjava/lang/CharSequence;)Z 5 19 java/lang/String length ()I 6 6 java/lang/String coder ()B 5 57 java/lang/String charAt (I)C 6 1 java/lang/String isLatin1 ()Z 6 12 java/lang/StringLatin1 charAt ([BI)C 5 65 java/lang/String charAt (I)C 6 1 java/lang/String isLatin1 ()Z 6 12 java/lang/StringLatin1 charAt ([BI)C 4 66 java/lang/String length ()I 5 6 java/lang/String coder ()B 4 87 java/lang/String length ()I 5 6 java/lang/String coder ()B 4 103 java/lang/String charAt (I)C 5 1 java/lang/String isLatin1 ()Z 5 12 java/lang/StringLatin1 charAt ([BI)C 1 70 java/lang/String isEmpty ()Z 1 86 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler split (Ljava/lang/String;)Lorg/jetbrains/kotlin/com/intellij/openapi/util/Pair; 2 3 java/lang/String lastIndexOf (I)I 3 3 java/lang/String length ()I 4 6 java/lang/String coder ()B 3 8 java/lang/String lastIndexOf (II)I 4 1 java/lang/String isLatin1 ()Z 4 13 java/lang/StringLatin1 lastIndexOf ([BII)I 5 1 java/lang/StringLatin1 canEncode (I)Z 2 14 java/lang/String substring (II)Ljava/lang/String; 3 1 java/lang/String length ()I 4 6 java/lang/String coder ()B 3 8 java/lang/String checkBoundsBeginEnd (III)V 3 28 java/lang/String isLatin1 ()Z 3 41 java/lang/StringLatin1 newString ([BII)Ljava/lang/String; 4 16 java/util/Arrays copyOfRange ([BII)[B 4 20 java/lang/String <init> ([BB)V 5 1 java/lang/Object <init> ()V 2 31 java/lang/String substring (I)Ljava/lang/String; 3 3 java/lang/String length ()I 4 6 java/lang/String coder ()B 3 6 java/lang/String substring (II)Ljava/lang/String; 4 1 java/lang/String length ()I 5 6 java/lang/String coder ()B 4 8 java/lang/String checkBoundsBeginEnd (III)V 4 28 java/lang/String isLatin1 ()Z 4 41 java/lang/StringLatin1 newString ([BII)Ljava/lang/String; 5 16 java/util/Arrays copyOfRange ([BII)[B 5 20 java/lang/String <init> ([BB)V 6 1 java/lang/Object <init> ()V 2 45 org/jetbrains/kotlin/com/intellij/openapi/util/Pair <init> (Ljava/lang/Object;Ljava/lang/Object;)V 3 1 java/lang/Object <init> ()V 1 102 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler directoryEntry (Ljava/util/Map;Lorg/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger;Ljava/lang/String;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 2 2 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap get (Ljava/lang/Object;)Ljava/lang/Object; 3 2 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap get (Ljava/lang/Object;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 4 18 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap index (Ljava/lang/String;[Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)I 5 17 java/lang/String hashCode ()I 6 17 java/lang/String isLatin1 ()Z 6 27 java/lang/StringLatin1 hashCode ([B)I 4 42 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap isTheOne (Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;Ljava/lang/CharSequence;)Z 5 19 java/lang/String length ()I 6 6 java/lang/String coder ()B 5 41 java/lang/String length ()I 6 6 java/lang/String coder ()B 5 48 java/lang/String length ()I 6 6 java/lang/String coder ()B 5 55 org/jetbrains/kotlin/com/intellij/util/text/CharArrayUtil regionMatches (Ljava/lang/CharSequence;IILjava/lang/CharSequence;)Z 6 19 java/lang/String length ()I 7 6 java/lang/String coder ()B 6 57 java/lang/String charAt (I)C 7 1 java/lang/String isLatin1 ()Z 7 12 java/lang/StringLatin1 charAt ([BI)C 6 65 java/lang/String charAt (I)C 7 1 java/lang/String isLatin1 ()Z 7 12 java/lang/StringLatin1 charAt ([BI)C 5 66 java/lang/String length ()I 6 6 java/lang/String coder ()B 5 87 java/lang/String length ()I 6 6 java/lang/String coder ()B 5 103 java/lang/String charAt (I)C 6 1 java/lang/String isLatin1 ()Z 6 12 java/lang/StringLatin1 charAt ([BI)C 2 70 java/lang/String isEmpty ()Z 2 86 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler split (Ljava/lang/String;)Lorg/jetbrains/kotlin/com/intellij/openapi/util/Pair; 3 3 java/lang/String lastIndexOf (I)I 4 3 java/lang/String length ()I 5 6 java/lang/String coder ()B 4 8 java/lang/String lastIndexOf (II)I 5 1 java/lang/String isLatin1 ()Z 5 13 java/lang/StringLatin1 lastIndexOf ([BII)I 6 1 java/lang/StringLatin1 canEncode (I)Z 3 14 java/lang/String substring (II)Ljava/lang/String; 4 1 java/lang/String length ()I 5 6 java/lang/String coder ()B 4 8 java/lang/String checkBoundsBeginEnd (III)V 4 28 java/lang/String isLatin1 ()Z 4 41 java/lang/StringLatin1 newString ([BII)Ljava/lang/String; 5 16 java/util/Arrays copyOfRange ([BII)[B 5 20 java/lang/String <init> ([BB)V 6 1 java/lang/Object <init> ()V 3 31 java/lang/String substring (I)Ljava/lang/String; 4 3 java/lang/String length ()I 5 6 java/lang/String coder ()B 4 6 java/lang/String substring (II)Ljava/lang/String; 5 1 java/lang/String length ()I 6 6 java/lang/String coder ()B 5 8 java/lang/String checkBoundsBeginEnd (III)V 5 28 java/lang/String isLatin1 ()Z 5 41 java/lang/StringLatin1 newString ([BII)Ljava/lang/String; 6 16 java/util/Arrays copyOfRange ([BII)[B 6 20 java/lang/String <init> ([BB)V 7 1 java/lang/Object <init> ()V 3 45 org/jetbrains/kotlin/com/intellij/openapi/util/Pair <init> (Ljava/lang/Object;Ljava/lang/Object;)V 4 1 java/lang/Object <init> ()V 2 126 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo <init> (Ljava/lang/CharSequence;ZJJLorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)V 3 9 java/lang/Object <init> ()V 2 135 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 3 9 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap put (Ljava/lang/String;Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 4 27 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap put (Ljava/lang/String;Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;[Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 5 26 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap index (Ljava/lang/String;[Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)I 6 17 java/lang/String hashCode ()I 7 17 java/lang/String isLatin1 ()Z 7 27 java/lang/StringLatin1 hashCode ([B)I 5 47 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap isTheOne (Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;Ljava/lang/CharSequence;)Z 6 19 java/lang/String length ()I 7 6 java/lang/String coder ()B 6 41 java/lang/String length ()I 7 6 java/lang/String coder ()B 6 48 java/lang/String length ()I 7 6 java/lang/String coder ()B 6 55 org/jetbrains/kotlin/com/intellij/util/text/CharArrayUtil regionMatches (Ljava/lang/CharSequence;IILjava/lang/CharSequence;)Z 7 19 java/lang/String length ()I 8 6 java/lang/String coder ()B 7 57 java/lang/String charAt (I)C 8 1 java/lang/String isLatin1 ()Z 8 12 java/lang/StringLatin1 charAt ([BI)C 7 65 java/lang/String charAt (I)C 8 1 java/lang/String isLatin1 ()Z 8 12 java/lang/StringLatin1 charAt ([BI)C 6 66 java/lang/String length ()I 7 6 java/lang/String coder ()B 6 87 java/lang/String length ()I 7 6 java/lang/String coder ()B 6 103 java/lang/String charAt (I)C 7 1 java/lang/String isLatin1 ()Z 7 12 java/lang/StringLatin1 charAt ([BI)C 1 126 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo <init> (Ljava/lang/CharSequence;ZJJLorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)V 2 9 java/lang/Object <init> ()V 1 135 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 9 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap put (Ljava/lang/String;Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 3 27 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap put (Ljava/lang/String;Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;[Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; 4 26 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap index (Ljava/lang/String;[Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;)I 5 17 java/lang/String hashCode ()I 6 17 java/lang/String isLatin1 ()Z 6 27 java/lang/StringLatin1 hashCode ([B)I 4 47 org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap isTheOne (Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;Ljava/lang/CharSequence;)Z 5 19 java/lang/String length ()I 6 6 java/lang/String coder ()B 5 41 java/lang/String length ()I 6 6 java/lang/String coder ()B 5 48 java/lang/String length ()I 6 6 java/lang/String coder ()B 5 55 org/jetbrains/kotlin/com/intellij/util/text/CharArrayUtil regionMatches (Ljava/lang/CharSequence;IILjava/lang/CharSequence;)Z 6 19 java/lang/String length ()I 7 6 java/lang/String coder ()B 6 57 java/lang/String charAt (I)C 7 1 java/lang/String isLatin1 ()Z 7 12 java/lang/StringLatin1 charAt ([BI)C 6 65 java/lang/String charAt (I)C 7 1 java/lang/String isLatin1 ()Z 7 12 java/lang/StringLatin1 charAt ([BI)C 5 66 java/lang/String length ()I 6 6 java/lang/String coder ()B 5 87 java/lang/String length ()I 6 6 java/lang/String coder ()B 5 103 java/lang/String charAt (I)C 6 1 java/lang/String isLatin1 ()Z 6 12 java/lang/StringLatin1 charAt ([BI)C
