/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : PlatformManagerKtTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/5/15 15:02
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2023/5/15       1.0      create
 **********************************************************************/
package com.oplus.filemanager.appmarketmanager

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.SdkUtils
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class PlatformManagerKtTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
    }

    @Test
    fun testIsHighEndMachine() {
        mockkStatic(AppUtils::class)
        every { AppUtils.getModel() }.returns(MODEL)
        Assert.assertTrue(isHighEndMachine(context))
        mockkStatic(SdkUtils::class)
        every { AppUtils.getModel() }.returns(NOT_HIGH_END_MACHINE_MODEL)
        every { SdkUtils.isAtLeastT() }.returns(false)
        Assert.assertFalse(isHighEndMachine(context))
        unmockkStatic(SdkUtils::class)
        unmockkStatic(AppUtils::class)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    companion object {
        private const val MODEL = "PHB110"
        private const val NOT_HIGH_END_MACHINE_MODEL = "MONDAY"
    }
}