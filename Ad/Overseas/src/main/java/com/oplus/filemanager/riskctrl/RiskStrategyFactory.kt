/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: RiskStrategyFactory.kt
 ** Description: Risk Strategy Factory
 ** Version: 1.0
 ** Date : 2025/04/03
 ** Author: chao.xue
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 *  chao.xue 2025/04/03   1.0    create
 ****************************************************************/
package com.oplus.filemanager.riskctrl

import com.filemanager.common.riskctrl.BlockAdRule
import com.filemanager.common.riskctrl.StrategyConfig
import com.filemanager.common.riskctrl.StrategyItem
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.heytap.cli.riskctrl.api.RiskControlAction
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.Objects

object RiskStrategyFactory {

    private const val TAG = "RiskStrategyFactory"
    private const val EXPIRE_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss"
    private const val STRATEGY_DATE_FORMAT = "yyyyMMddHHmm"
    private const val EXPIRE_TIME_FOREVER = "-1" // 如果为空或者-1表示永久有效
    private const val MICRO_SCALE = 1000

    fun builder(): Builder {
        return Builder()
    }

    fun toJson(config: StrategyConfig): String {
        val gson = Gson()
        return gson.toJson(config)
    }

    /**
     * 解析屏蔽广告的规则
     */
    fun parseBlockAdRules(json: String): List<BlockAdRule> {
        val gson = Gson()
        val listType = object : TypeToken<List<BlockAdRule>>() {}.type
        return gson.fromJson(json, listType)
    }

    class Builder {

        private val strategies: MutableList<StrategyItem> = mutableListOf()
        private var version: String = ""

        /**
         * 失效时间（秒）：如果为空表示永久有效
         */
        private var expireTime: Long = -1L
        private var forceUpdate: Boolean = false


        fun build(): StrategyConfig {
            val strategyId = createStrategyId()
            return StrategyConfig(this.strategies, this.version, strategyId, this.expireTime, this.forceUpdate)
        }

        /**
         * 过期
         * @param version 版本
         * @param expireTime 过期时间 格式：yyyy-MM-dd HH:mm:ss，如果为空或者-1，表示永久有效
         * @param forceUpdate 是否强制更新
         */
        fun expire(version: String, expireTime: String?, forceUpdate: Boolean = false): Builder {
            this.version = version
            this.forceUpdate = forceUpdate
            this.expireTime = getExpireTime(expireTime)
            return this
        }

        /**
         * 创建普通的策略
         */
        fun customStrategy(module: String, action: RiskControlAction): Builder {
            if (action == RiskControlAction.PUSH_RECALL || action == RiskControlAction.BLOCK_AD || action == RiskControlAction.UNDEFINED) {
                throw IllegalArgumentException("createCustomStrategy action is error")
            }
            val strategy = StrategyItem(module, action.actionCode)
            strategies.add(strategy)
            return this
        }

        /**
         * 创建push召回的策略
         */
        fun pushRecallStrategy(module: String, dataIds: List<String>): Builder {
            val strategy = StrategyItem(module, RiskControlAction.PUSH_RECALL.actionCode, dataIds)
            strategies.add(strategy)
            return this
        }

        /**
         * 创建屏蔽广告的策略
         */
        fun blockAdStrategy(module: String, rules: List<BlockAdRule>? = null): Builder {
            val ruleJson = Gson().toJson(rules)
            val strategy = StrategyItem(module, RiskControlAction.BLOCK_AD.actionCode, null, ruleJson)
            strategies.add(strategy)
            return this
        }

        /**
         * 获取过期时间
         */
        private fun getExpireTime(time: String?): Long {
            if (time.isNullOrEmpty() || Objects.equals(time, EXPIRE_TIME_FOREVER)) {
                return -1
            }
            try {
                val format = SimpleDateFormat(EXPIRE_DATE_FORMAT, Locale.getDefault())
                val date = format.parse(time)
                return date?.time?.div(MICRO_SCALE) ?: -1L
            } catch (e: ParseException) {
                throw IllegalArgumentException("getExpireTime time format is error, should be : yyyy-MM-dd HH:mm:ss")
            }
        }

        /**
         * 获取策略id
         * @return 策略id，用时间做策略id
         */
        private fun createStrategyId(): String {
            val format = SimpleDateFormat(STRATEGY_DATE_FORMAT, Locale.getDefault())
            return format.format(Date())
        }
    }
}