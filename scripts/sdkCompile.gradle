/**
 * sdkCompile.gradle用于进行所有SDK module的通用编译配置。
 * 非针对所有SDK Module生效的配置禁止在该gradle文件中处理。
 */
final isLibModule = project.plugins.hasPlugin("com.android.library")
if (!isLibModule) {
    final errMsg = "${project.path}: sdkCompile.gradle must be applied to " +
            "an Android  library module. Please apply com.android.library first."
    System.err.println(errMsg)
    throw new GradleException(errMsg)
}
if (ext.has("applyBaseCompileFrom")) {
    final String alreadyApply = ext.applyBaseCompileFrom
    if (alreadyApply != null && !alreadyApply.isEmpty()) {
        final errMsg = "${project.path}: Can not apply sdkCompile.gradle with $alreadyApply together."
        System.err.println(errMsg)
        throw new GradleException(errMsg)
    }
}
ext.applyBaseCompileFrom = "scripts/sdkCompile.gradle"
apply from: rootProject.file("scripts/baseCompile.gradle")

def isPublishCompile = false
for (taskRequest in project.gradle.startParameter.taskRequests) {
    for (taskArgs in taskRequest.args) {
        final taskParameter = taskArgs.toString().split(":").last()
        if (taskParameter.startsWith("publish")) {
            isPublishCompile = true
            break
        }
    }
    if (isPublishCompile) {
        break
    }
}

/**
 * 此处可导入所有SDK都需要引用的公共依赖，但原则上各个SDK需在各自的build.gradle中按需导入依赖
 *
 * 警告：
 * 此处配置导入依赖会影响到文管代码中所有SDK出包的依赖配置关系，可能会影响多个不同SDK的接入方，严禁胡乱配置
 */
dependencies {
    // 仅在非发布SDK版本的编译任务上导入lint检查
    if (!isPublishCompile) {
        // OLint codes check:
        implementation libs.oplus.check.olint
    }
}

static boolean shallCheckConf(Configuration conf) {
    final confName = conf.name
    //不检查单元测试和自动化测试用的依赖
    if (confName.startsWith("test") || confName.startsWith("androidTest")) {
        return false
    }
    final lowerCaseName = confName.toLowerCase()
    if (lowerCaseName.endsWith("implementation") || lowerCaseName.endsWith("api")
            || lowerCaseName.endsWith("compileonly") || lowerCaseName.endsWith("runtimeonly")) {
        return true
    }
    return false
}

boolean isModuleDependency(Dependency dependency) {
    final rootProjectName = rootProject.name
    final dependencyPath = "${dependency.group}:${dependency.name}"
    if (dependencyPath.startsWith(rootProjectName)) {
        return true
    }
    return false
}

static void ensureNotAppModule(Configuration conf, Project currPrj, Project dependsPrj) {
    final isAppModule = dependsPrj.plugins.hasPlugin("com.android.application")
    final importInfo = "${conf.name} project('${dependsPrj.path}')"
    final errReason = "it is not stand alone SDK"
    if (isAppModule) {
        throw new IllegalArgumentException("${currPrj.path}: can not $importInfo since $errReason")
    }
    final isPublishableSdk = dependsPrj.plugins.hasPlugin("maven-publish")
    if (!isPublishableSdk) {
        throw new IllegalArgumentException("${currPrj.path}: can not $importInfo since $errReason")
    }
}

void checkSdkDependency(Configuration conf, Dependency dependency) {
    if (!isModuleDependency(dependency)) {
        return
    }
    final dependencyPath = "${dependency.group}:${dependency.name}"
            .replace(".", ":").replace("${rootProject.name}", "")
    final currPrj = project
    final dependsPrj = rootProject.project(dependencyPath)
    if (dependsPrj.state.executed) {
        ensureNotAppModule(conf, currPrj, dependsPrj)
    } else {
        dependsPrj.afterEvaluate {
            ensureNotAppModule(conf, currPrj, dependsPrj)
        }
    }
}

void checkSdkModuleDependencies() {
    configurations.configureEach { conf ->
        if (shallCheckConf(conf)) {
            conf.dependencies.each { dependency ->
                checkSdkDependency(conf, dependency)
            }
        }
    }
}

/**
 * 检测SDK module的依赖配置，如果导入了只能集成至APP的非独立SDK的module作为依赖，
 * 则抛出异常中断编译提醒检查build.gradle的配置
 */
afterEvaluate {
    checkSdkModuleDependencies()
}