/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.MainApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager

import android.app.Activity
import android.content.Context
import android.content.Intent
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.Constants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.wrapper.RecycleFileWrapper
import com.oplus.filemanager.filelabel.list.LabelFileListActivity
import com.oplus.filemanager.main.ui.MainActivity
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.provider.FileLabelMappingRecycleDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import com.oplus.filemanager.room.model.FileLabelMappingEntity
import com.oplus.filemanager.room.model.FileLabelMappingRecycleEntity
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class MainApiTest {

    @MockK
    lateinit var context: Context

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        every { context.applicationContext }.returns(context)
        MyApplication.init(context)
        mockkObject(MainApi)
        mockkObject(FileLabelMappingDBHelper)
        mockkObject(FileLabelMappingRecycleDBHelper)
    }

    @After
    fun after() {
        unmockkObject(MainApi)
        unmockkObject(FileLabelMappingDBHelper)
        unmockkObject(FileLabelMappingRecycleDBHelper)
    }

    @Test
    fun `should execute startActivity when startSubLabelListActivity`() {
        val activity = mockk<Activity>()
        val labelId = 1L
        val title = "title"
        val intent = mockk<Intent>(relaxed = true)
        intent.putExtra(Constants.TITLE, title)
        intent.putExtra(Constants.LABEL_ID, labelId)
        intent.putExtra(Constants.IS_FILTER_SEARCH_RESULT, false)
        intent.action = Intent.ACTION_VIEW
        justRun { activity.startActivity(any()) }

        MainApi.startSubLabelListActivity(activity, labelId, title, sideCategoryType = CategoryHelper.CATEGORY_LABEL_GROUP)
    }

    @Test
    fun `should execute updateMappingByPath when updateFileLabel`() {
        val oldPath = "oldPath"
        val newPath = "newPath"
        justRun { FileLabelMappingDBHelper.updateMappingByPath(oldPath, newPath) }
        MainApi.onUpdateFilePath(oldPath, newPath)
        verify { MainApi.onUpdateFilePath(oldPath, newPath) }
    }

    @Test
    fun `should not execute deleteFileLabelMappingEntities when deleteFileLabelByPathList if list is null`() {
        val pathList = arrayListOf("path1", "path2")
        every { FileLabelMappingDBHelper.removeMappingByPath(any()) } returns arrayListOf()
        val recycleList = arrayListOf<FileLabelMappingRecycleEntity>()
        every { FileLabelMappingRecycleDBHelper.getFileLabelsByDeleteFilePath(any()) } returns null
        justRun { FileLabelMappingRecycleDBHelper.deleteFileLabelMappingEntities(any()) }
        MainApi.onDeleteFilePaths(pathList)
        verify(inverse = true) { FileLabelMappingRecycleDBHelper.deleteFileLabelMappingEntities(recycleList) }
    }

    @Test
    fun `should execute deleteFileLabelMappingEntities when deleteFileLabelByPathList`() {
        val pathList = arrayListOf("path1", "path2")
        every { FileLabelMappingDBHelper.removeMappingByPath(any()) } returns arrayListOf()
        val recycleList = arrayListOf<FileLabelMappingRecycleEntity>()
        every { FileLabelMappingRecycleDBHelper.getFileLabelsByDeleteFilePath(any()) } returns recycleList
        justRun { FileLabelMappingRecycleDBHelper.deleteFileLabelMappingEntities(recycleList) }
        MainApi.onDeleteFilePaths(pathList)
        verify { FileLabelMappingRecycleDBHelper.deleteFileLabelMappingEntities(recycleList) }
    }

    @Test
    fun `should execute insertFileLabelMappingEntities when deleteFileLabelForRecycle`() {
        val filePath1 = "filePath1"
        val filePath2 = "filePath2"
        val pathList = arrayListOf(filePath1, filePath2)
        val mappingList = arrayListOf<FileLabelMappingEntity>()
        for (index in 1..10) {
            val label = FileLabelMappingEntity(index.toLong(), index.toLong(), "filePath$index", 1, "", 0, 0)
            mappingList.add(label)
        }
        every { FileLabelMappingDBHelper.removeMappingByPath(any()) } returns mappingList
        val mappingRecycleList = arrayListOf<FileLabelMappingRecycleEntity>()
        mappingList.forEach { entity ->
            val recycleEntity = FileLabelMappingRecycleEntity(
                0,
                entity.labelId,
                entity.filePath,
                entity.localType,
                entity.mimeType,
                entity.duration,
                entity.timestamp,
                filePath1
            )
            mappingRecycleList.add(recycleEntity)
        }
        justRun { FileLabelMappingRecycleDBHelper.insertFileLabelMappingEntities(any()) }
        MainApi.onRecycleFilePaths(pathList)
        verify { FileLabelMappingRecycleDBHelper.insertFileLabelMappingEntities(mappingRecycleList) }
    }

    @Test
    fun `should execute deleteFileLabelMappingEntities when restoreFileLabelByRecycle if list empty`() {
        val filePath1 = "filePath1"
        val filePath2 = "filePath2"
        val pathList = arrayListOf(filePath1, filePath2)
        val mappingList = arrayListOf<FileLabelMappingEntity>()
        val mappingRecycleList = arrayListOf<FileLabelMappingRecycleEntity>()
        every { FileLabelMappingRecycleDBHelper.getFileLabelsByDeleteFilePath(any()) } returns null
        justRun { FileLabelMappingDBHelper.insertFileLabelMappingEntities(any()) }
        justRun { FileLabelMappingRecycleDBHelper.deleteFileLabelMappingEntities(any()) }
        MainApi.onRestoreFilePaths(pathList)
        verify { FileLabelMappingDBHelper.insertFileLabelMappingEntities(mappingList) }
        verify(inverse = true) { FileLabelMappingRecycleDBHelper.deleteFileLabelMappingEntities(mappingRecycleList) }
    }

    @Test
    fun `should execute deleteFileLabelMappingEntities when restoreFileLabelByRecycle`() {
        val filePath1 = "filePath1"
        val filePath2 = "filePath2"
        val pathList = arrayListOf(filePath1, filePath2)
        val mappingList = arrayListOf<FileLabelMappingEntity>()
        val mappingRecycleList = arrayListOf<FileLabelMappingRecycleEntity>()
        for (index in 1..10) {
            val label = FileLabelMappingRecycleEntity(index.toLong(), index.toLong(), "filePath$index", 1, "", 0, 0, filePath1)
            mappingRecycleList.add(label)
        }
        mappingRecycleList.forEach { recycleMapping ->
            val labelMapping = FileLabelMappingEntity(
                0,
                recycleMapping.labelId,
                recycleMapping.filePath,
                recycleMapping.localType,
                recycleMapping.mimeType,
                recycleMapping.duration,
                recycleMapping.timestamp
            )
            mappingList.add(labelMapping)
        }
        every { FileLabelMappingRecycleDBHelper.getFileLabelsByDeleteFilePath(any()) } returns mappingRecycleList
        justRun { FileLabelMappingDBHelper.insertFileLabelMappingEntities(any()) }
        justRun { FileLabelMappingRecycleDBHelper.deleteFileLabelMappingEntities(any()) }
        MainApi.onRestoreFilePaths(pathList)
        verify { FileLabelMappingDBHelper.insertFileLabelMappingEntities(mappingList) }
        verify { FileLabelMappingRecycleDBHelper.deleteFileLabelMappingEntities(mappingRecycleList) }
    }

    @Test
    fun `should has not label when findFileLabelIfHad if map not null`() {
        val list = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val baseFile = BaseFileBean()
            baseFile.mHasLabel = false
            list.add(baseFile)
        }
        val resultPaths = arrayListOf<String>()
        every { FileLabelMappingDBHelper.getFilesHasLabel(any()) } returns resultPaths
        MainApi.findFileLabelIfHad(list)
        list.forEach {
            assertFalse(it.mHasLabel)
        }
    }

    @Test
    fun `should has not label when findFileLabelIfHad if getFilesHasLabel is empty`() {
        val list = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val baseFile = BaseFileBean()
            baseFile.mData = "data$index"
            baseFile.mHasLabel = false
            list.add(baseFile)
        }
        val resultPaths = arrayListOf<String>()
        every { FileLabelMappingDBHelper.getFilesHasLabel(any()) } returns resultPaths
        MainApi.findFileLabelIfHad(list)
        list.forEach {
            assertFalse(it.mHasLabel)
        }
    }

    @Test
    fun `should has not label when findFileLabelIfHad if getFilesHasLabel is not empty`() {
        val list = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val baseFile = BaseFileBean()
            baseFile.mData = "data$index"
            baseFile.mHasLabel = false
            list.add(baseFile)
        }
        val resultPaths = arrayListOf<String>()
        for (index in 1..10) {
            resultPaths.add("something another$index")
        }
        every { FileLabelMappingDBHelper.getFilesHasLabel(any()) } returns resultPaths
        MainApi.findFileLabelIfHad(list)
        list.forEach {
            assertFalse(it.mHasLabel)
        }
    }

    @Suppress("ExplicitItLambdaParameter")
    @Test
    fun `should has label when findFileLabelIfHad if getFilesHasLabel is not empty`() {
        val list = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val baseFile = BaseFileBean()
            baseFile.mData = "data$index"
            baseFile.mHasLabel = false
            list.add(baseFile)
        }
        val resultPaths = arrayListOf<String>()
        for (index in 1..3) {
            resultPaths.add("data$index")
        }
        every { FileLabelMappingDBHelper.getFilesHasLabel(any()) } returns resultPaths
        MainApi.findFileLabelIfHad(list)
        list.forEachIndexed { index, it ->
            if (index in 0..2) {
                assertTrue(it.mHasLabel)
            } else {
                assertFalse(it.mHasLabel)
            }
        }
    }

    @Suppress("ExplicitItLambdaParameter")
    @Test
    fun `should has label when findFileLabelIfHadInRecycleBin if originPath is null`() {
        val list = arrayListOf<RecycleFileWrapper>()
        for (index in 1..10) {
            val baseFile = RecycleFileWrapper()
            baseFile.mHasLabel = false
            list.add(baseFile)
        }
        val resultPaths = arrayListOf<String>()
        for (index in 1..3) {
            resultPaths.add("data$index")
        }
        every { FileLabelMappingRecycleDBHelper.getFilesHasLabel(any()) } returns resultPaths
        MainApi.findFileLabelIfHadInRecycleBin(list)
        list.forEach {
            assertFalse(it.mHasLabel)
        }
    }

    @Suppress("ExplicitItLambdaParameter")
    @Test
    fun `should has label when findFileLabelIfHadInRecycleBin if getFilesHasLabel is not empty`() {
        val list = arrayListOf<RecycleFileWrapper>()
        for (index in 1..10) {
            val baseFile = RecycleFileWrapper()
            baseFile.mOriginPath = "data$index"
            baseFile.mHasLabel = false
            list.add(baseFile)
        }
        val resultPaths = arrayListOf<String>()
        for (index in 1..3) {
            resultPaths.add("data$index")
        }
        every { FileLabelMappingRecycleDBHelper.getFilesHasLabel(any()) } returns resultPaths
        MainApi.findFileLabelIfHadInRecycleBin(list)
        list.forEachIndexed { index, it ->
            if (index in 0..2) {
                assertTrue(it.mHasLabel)
            } else {
                assertFalse(it.mHasLabel)
            }
        }
    }

    @Test
    fun `should execute insertFileLabelMappingEntities when copyFileLabel`() {
        val sourcePath = "sourcePath"
        val destPath = "destPath"
        val mimeType = "mimeType"
        val localType = 1
        val labelList = arrayListOf<FileLabelEntity>()
        for (index in 1..10) {
            val label = FileLabelEntity(index.toLong(), "name$index", 0, 0, 0, 0)
            labelList.add(label)
        }
        every { FileLabelMappingDBHelper.getFileLabelsByPath(sourcePath) } returns labelList
        val destLabelMappingList = arrayListOf<FileLabelMappingEntity>()
        labelList.forEach { label ->
            val destLabelMapping = FileLabelMappingEntity(0, label.id, destPath, localType, mimeType, 0, System.currentTimeMillis())
            destLabelMappingList.add(destLabelMapping)
        }
        justRun { FileLabelMappingDBHelper.insertFileLabelMappingEntities(any()) }
        MainApi.onCopyFile(sourcePath, destPath, localType, mimeType)
        every { FileLabelMappingDBHelper.insertFileLabelMappingEntities(destLabelMappingList) }
    }

    @Test
    fun `should execute insertFileLabelMappingEntities when copyFileLabel if list empty`() {
        val sourcePath = "sourcePath"
        val destPath = "destPath"
        val mimeType = "mimeType"
        val localType = 1
        every { FileLabelMappingDBHelper.getFileLabelsByPath(sourcePath) } returns null
        val destLabelMappingList = arrayListOf<FileLabelMappingEntity>()
        justRun { FileLabelMappingDBHelper.insertFileLabelMappingEntities(any()) }
        MainApi.onCopyFile(sourcePath, destPath, localType, mimeType)
        every { FileLabelMappingDBHelper.insertFileLabelMappingEntities(destLabelMappingList) }
    }

    @Test
    fun `should return true when isLabelListActivity if activity is LabelFileListActivity`() {
        val activity = mockk<LabelFileListActivity>()
        assertTrue(MainApi.isLabelListActivity(activity))
    }

    @Test
    fun `should return false when isLabelListActivity if activity is not LabelFileListActivity`() {
        val activity = mockk<MainActivity>()
        assertFalse(MainApi.isLabelListActivity(activity))
        assertFalse(MainApi.isLabelListActivity(null))
    }
}