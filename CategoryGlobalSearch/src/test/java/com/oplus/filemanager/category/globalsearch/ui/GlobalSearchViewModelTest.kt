/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GlobalSearchViewModelTest
 ** Description : GlobalSearchViewModel Unit Test
 ** Version     : 1.0
 ** Date        : 2022/9/2
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2022/9/2      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui

import android.content.Context
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.CategoryHelper
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterCondition
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConditionManager
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import com.oplus.filemanager.category.globalsearch.manager.history.GlobalSearchHistoryManager
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkConstructor
import io.mockk.unmockkObject
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class GlobalSearchViewModelTest {

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        mockkObject(MyApplication)
        val context: Context = mockk(relaxed = true)
        every { MyApplication.sAppContext }.returns(context)
    }

    @Test
    fun should_notNull_when_getHistoryDataModel() {
        val state = mockk<SavedStateHandle>(relaxed = true)
        val model = GlobalSearchViewModel(state)
        Assert.assertNotNull(model.getHistoryDataModel())
    }

    private fun createFilterCondition(id: Int): FilterCondition {
        val condition = FilterCondition(id, "time")
        val items = mutableListOf<FilterItem>()
        items.add(FilterItem(id, condition, "item1"))
        items.add(FilterItem(id, condition, "item2"))
        items.add(FilterItem(id, condition, "item3"))
        condition.items = items
        return condition
    }

    @Test
    fun should_when_addCurrentFolderFilter() {
        val model = mockk<GlobalSearchViewModel>(relaxed = true)
        val intent = mockk<Intent>(relaxed = true)
        val filterList = mutableListOf<FilterCondition>()
        filterList.add(createFilterCondition(FilterConstants.FILTER_TIME))
        filterList.add(createFilterCondition(FilterConstants.FILTER_FROM))
        mockkObject(FilterConditionManager)
        every { model.addCurrentFolderFilter(any()) }.answers { callOriginal() }
        every { intent.getStringExtra(any()) }.returns("")
        every { FilterConditionManager.getSupportFilter(any()) }.returns(filterList)
        every { model.mExternalCategory }.returns(GlobalSearchActivity.TAB_ALL)
        val list = FilterConditionManager.getSupportFilter(GlobalSearchActivity.TAB_ALL)
        val items = list?.get(1)?.items
        model.addCurrentFolderFilter(intent)
        Assert.assertTrue(items?.get(0)?.id != com.filemanager.common.constants.FilterConstants.FILTER_FROM_CURRENT)

        every { model.mExternalCategory }.returns(CategoryHelper.CATEGORY_FILE_BROWSER)
        model.addCurrentFolderFilter(intent)
        Assert.assertTrue(items?.get(0)?.id == com.filemanager.common.constants.FilterConstants.FILTER_FROM_CURRENT)

        every { model.mExternalCategory }.returns(CategoryHelper.CATEGORY_OTG_BROWSER)
        model.addCurrentFolderFilter(intent)
        Assert.assertTrue(items?.get(0)?.id == com.filemanager.common.constants.FilterConstants.FILTER_FROM_CURRENT)

        unmockkObject(FilterConditionManager)
    }

    @Test
    fun testAddCurrentFolderFilter() {
        val mSavedState = SavedStateHandle()
        val globalSearchViewModel = GlobalSearchViewModel(mSavedState)
        globalSearchViewModel.mExternalCategory = CategoryHelper.CATEGORY_FILE_BROWSER
        val list = ArrayList<FilterCondition>()
        val time = FilterCondition(0, "test")
        val from = FilterCondition(1, "test")
        list.add(time)
        list.add(from)
        from.items = mutableListOf()
        mockkObject(FilterConditionManager)
        mockkObject(MyApplication)
        val context = mockk<Context>(relaxed = true)
        every { MyApplication.sAppContext }.returns(context)
        every { context.getString(any()) }.returns("currentFolder")
        every { FilterConditionManager.getSupportFilter(any()) }.returns(list)
        val intent = mockk<Intent>(relaxed = true)
        every { intent.getStringExtra(KtConstants.CURRENT_DIR) }.returns("testpath")
        globalSearchViewModel.addCurrentFolderFilter(intent)
        Assert.assertTrue((from.items as MutableList<FilterItem>).size > 0)
        val item = (from.items as MutableList<FilterItem>)[0]
        Assert.assertEquals("testpath", item.packageName)
        Assert.assertEquals("currentFolder", item.desc)
        globalSearchViewModel.mExternalCategory = CategoryHelper.CATEGORY_OTG_BROWSER
        from.items = mutableListOf()
        globalSearchViewModel.addCurrentFolderFilter(intent)
        Assert.assertTrue((from.items as MutableList<FilterItem>).size > 0)
        val item2 = (from.items as MutableList<FilterItem>)[0]
        Assert.assertEquals("testpath", item2.packageName)
        Assert.assertEquals("currentFolder", item2.desc)
        globalSearchViewModel.mExternalCategory = CategoryHelper.CATEGORY_BLUETOOTH
        from.items = mutableListOf()
        globalSearchViewModel.addCurrentFolderFilter(intent)
        Assert.assertTrue((from.items as MutableList<FilterItem>).size == 0)
        unmockkObject(MyApplication)
        unmockkObject(FilterConditionManager)
        unmockkConstructor(Context::class)
        unmockkConstructor(Intent::class)
    }
}