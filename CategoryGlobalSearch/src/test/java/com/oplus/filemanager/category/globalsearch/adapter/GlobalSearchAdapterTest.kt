/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GlobalSearchAdapterTest
 ** Description : GlobalSearchAdapter Unit Test
 ** Version     : 1.0
 ** Date        : 2022/8/11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2022/8/11      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.adapter

import android.content.Context
import android.content.res.Resources
import android.widget.TextView
import com.filemanager.common.base.SearchFileWrapper
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.category.globalsearch.adapter.GlobalSearchAdapter.Companion.VIEW_TYPE_FILE_OR_FOLDER
import com.oplus.filemanager.category.globalsearch.adapter.GlobalSearchAdapter.Companion.VIEW_TYPE_LABEL
import com.oplus.filemanager.category.globalsearch.bean.SearchLabelWrapper
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Test

class GlobalSearchAdapterTest {

    @Test
    fun should_when_setTitlePadding() {
        val titleTv = mockk<TextViewSnippet>(relaxed = true)
        val detailTv = mockk<TextView>(relaxed = true)
        val context = mockk<Context>(relaxed = true)
        val resources = mockk<Resources>(relaxed = true)
        val holder = mockk<GlobalSearchAdapter.ListViewHolder>()

        every { context.resources }.returns(resources)
        every { resources.getDimensionPixelSize(any()) }.returns(20)
        every { holder.mTitle }.returns(titleTv)
        every { holder.mDetail }.returns(detailTv)
        every { holder.setTitlePadding(any()) }.answers { callOriginal() }

        holder.setTitlePadding(context)
        verify {
            titleTv.setPaddingRelative(any(), any(), any(), any())
        }
        verify {
            detailTv.setPaddingRelative(any(), any(), any(), any())
        }
    }

    @Test
    fun `should not execute setPaddingRelative when setTitlePadding if title is null`() {
        val titleTv = mockk<TextViewSnippet>(relaxed = true)
        val detailTv = mockk<TextView>(relaxed = true)
        val context = mockk<Context>(relaxed = true)
        val resources = mockk<Resources>(relaxed = true)
        val holder = mockk<GlobalSearchAdapter.ListViewHolder>()

        every { context.resources }.returns(resources)
        every { resources.getDimensionPixelSize(any()) }.returns(20)
        every { holder.setTitlePadding(any()) }.answers { callOriginal() }

        holder.setTitlePadding(context)
        verify(inverse = true) {
            titleTv.setPaddingRelative(any(), any(), any(), any())
        }
        verify(inverse = true) {
            detailTv.setPaddingRelative(any(), any(), any(), any())
        }
    }

    @Test
    fun getItemViewTypeTest() {
        mockkStatic(WindowUtils::class)
        every { WindowUtils.supportLargeScreenLayout(any()) }.returns(false)
        val adapter = mockk<GlobalSearchAdapter>(relaxed = true)
        every { adapter.getItemViewType(any()) } answers { callOriginal() }
        every { adapter.getData(0) } returns mockk<SearchLabelWrapper>()
        every { adapter.getData(1) } returns mockk<SearchFileWrapper>()
        Assert.assertEquals(VIEW_TYPE_LABEL, adapter.getItemViewType(0))
        Assert.assertEquals(VIEW_TYPE_FILE_OR_FOLDER, adapter.getItemViewType(1))
        unmockkStatic(WindowUtils::class)
    }
}