/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GetOTGStorageUseCaseTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/3/7 16:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/3/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.main.ui.usecase

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.main.ui.uistate.Storage
import io.mockk.MockKAnnotations
import io.mockk.mockk
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class GetOTGStorageUseCaseTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
    }

    @Test
    fun `get otg storage info when otg state if false`() = runBlocking {
        val getOtgStorageUseCase = GetOTGStorageUseCase()
        val storageItems = mutableListOf<Storage>()
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getExternalOTGState(context, false) }.returns(false)
        getOtgStorageUseCase.invoke(storageItems)
        Assert.assertEquals(1, storageItems.size)
        val storage = storageItems[0]
        Assert.assertEquals(true, storage is Storage.OTGStorage)
        val otgPath = (storage as Storage.OTGStorage).otgPath
        val otgSize = storage.otgSize
        Assert.assertEquals(null, otgPath)
        Assert.assertEquals(null, otgSize)
        Assert.assertEquals(KtConstants.STATE_UNMOUNTED, storage.state)
    }

    @Test
    fun `get otg storage info when user is container user`() = runBlocking {
        val getOtgStorageUseCase = GetOTGStorageUseCase()
        val storageItems = mutableListOf<Storage>()
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getExternalOTGState(context, false) }.returns(true)
        mockkStatic(Utils::class)
        every { Utils.isContainerUser(context) }.returns(true)
        getOtgStorageUseCase.invoke(storageItems)
        Assert.assertEquals(1, storageItems.size)
        val storage = storageItems[0]
        Assert.assertEquals(true, storage is Storage.OTGStorage)
        val otgPath = (storage as Storage.OTGStorage).otgPath
        val otgSize = storage.otgSize
        Assert.assertEquals(null, otgPath)
        Assert.assertEquals(null, otgSize)
        Assert.assertEquals(KtConstants.STATE_UNMOUNTED, storage.state)
    }

    @Test
    fun `get otg storage info when has otg`() = runBlocking {
        val getOtgStorageUseCase = GetOTGStorageUseCase()
        val storageItems = mutableListOf<Storage>()
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getExternalOTGState(context, false) }.returns(true)
        mockkStatic(Utils::class)
        every { Utils.isContainerUser(context) }.returns(false)
        every { VolumeEnvironment.getOTGPath(context) }.returns(OTG_PATH)
        every { VolumeEnvironment.getOtgTotalAndAvailableSize(context) }.returns(OTG_SIZE)
        getOtgStorageUseCase.invoke(storageItems)
        Assert.assertEquals(1, storageItems.size)
        val storage = storageItems[0]
        Assert.assertEquals(true, storage is Storage.OTGStorage)
        val otgPath = (storage as Storage.OTGStorage).otgPath
        val otgSize = storage.otgSize
        Assert.assertEquals(OTG_PATH, otgPath)
        Assert.assertEquals(OTG_SIZE, otgSize)
        Assert.assertEquals(KtConstants.STATE_MOUNTED, storage.state)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    companion object {
        private val OTG_PATH = arrayListOf("/sdcard/0/path")
        private val OTG_SIZE = Pair(100L, 200L)
    }
}