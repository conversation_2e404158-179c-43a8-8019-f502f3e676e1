plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/sdkCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")

android {
    namespace 'com.oplus.filemanager.simulateclick.sdk'

    buildFeatures {
        buildConfig = true
    }

    defaultConfig {
        consumerProguardFiles "consumer-rules.pro"

        buildConfigField("String", "VERSION_NAME", "\"${mainVersionName}\"")
        buildConfigField("long", "VERSION_CODE", "${mainVersionCode}L")
        buildConfigField("String", "VERSION_COMMIT", "\"${prop_versionCommit}\"")
        buildConfigField("String", "VERSION_DATE", "\"${prop_versionDate}\"")
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    compileOnly(libs.oplus.addon.sdk) { artifact { type = "aar" } }
    implementation(libs.heytap.addon.adapter)
    implementation libs.androidx.core.ktx
    implementation libs.google.gson
    implementation libs.oplus.api.adapter.oplus
    implementation libs.oplus.coreapp.appfeature
    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.dialog
    implementation libs.oplus.appcompat.scroll
    implementation libs.oplus.appcompat.scrollview
    implementation libs.oplus.appcompat.statement
    implementation libs.oplus.appcompat.component
}

OBuildConfig {
    standAloneSdk = true
    groupId = prop_archivesGroupName
    sdkArtifactId = prop_artifactId_simulateClickEngine
    sdkExecuteTask = "publishAllPublicationsToReleaseRepository"
    moduleDescription = "The function module for simulate click engine aar."
}

apply from: rootProject.file("exportedLibs/scripts/aarCompile.gradle")
apply from: rootProject.file("exportedLibs/scripts/mappingCollect.gradle")

mappingCollect {
    publishTaskName = "publishAllPublicationsToReleaseRepository"
    buildType = "release"
    currentPrebuilt libs.oplus.filemanager.simulateClickEngine
}