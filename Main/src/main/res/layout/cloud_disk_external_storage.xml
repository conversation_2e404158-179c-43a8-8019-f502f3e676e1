<?xml version="1.0" encoding="utf-8"?>
<com.filemanager.common.view.HoverSmoothRoundedConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/action_to_cloud_disk"
    android:layout_width="@dimen/main_cloud_disk_storage_width"
    android:layout_height="@dimen/main_storage_height"
    android:background="@drawable/bg_card_common"
    app:hover_radius="?attr/couiRoundCornerM"
    app:corner_radius="?attr/couiRoundCornerM"
    app:default_background="true"
    app:solid_color="?attr/couiColorBackground">

    <TextView
        android:id="@+id/cloud_disk_storage_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/string_cloud_disk"
        android:textAppearance="@style/couiTextButtonM"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/cloud_disk_image"
        android:layout_width="@dimen/dimen_18dp"
        android:layout_height="@dimen/dimen_18dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:layout_marginLeft="@dimen/dimen_6dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/cloud_disk_storage_title"
        app:layout_goneMarginEnd="@dimen/dimen_14dp" />

    <TextView
        android:includeFontPadding="false"
        android:id="@+id/cloud_disk_storage_space_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:gravity="top"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        app:layout_constraintEnd_toEndOf="@id/cloud_disk_storage_title"
        app:layout_constraintStart_toStartOf="@id/cloud_disk_storage_title"
        app:layout_constraintTop_toBottomOf="@id/cloud_disk_storage_title"
        app:layout_constraintBottom_toTopOf="@id/iv_cloud_disk"
        app:layout_constraintVertical_bias="0"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/cloud_disk_subtitle_default"
        android:textAppearance="@style/couiTextBodyXS"
        android:textSize="12sp"
        android:textColor="@color/coui_color_label_secondary" />

    <ImageView
        android:id="@+id/iv_cloud_disk"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_cloud_disk"
        android:layout_marginBottom="@dimen/dimen_16dp"
        android:contentDescription="@null"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/cloud_disk_storage_title"
        app:layout_constraintBottom_toBottomOf="parent" />

</com.filemanager.common.view.HoverSmoothRoundedConstraintLayout>