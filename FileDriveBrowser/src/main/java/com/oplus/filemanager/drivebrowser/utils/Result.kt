/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Result
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/27 20:05
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/27       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import com.oplus.filemanager.drivebrowser.data.model.BaseResponse
import java.lang.Exception

sealed class Result<out R> {
    data class Success<out T>(val data: T) : Result<T>()
    data class Error(val exception: Exception) : Result<Nothing>()
    object NoNetwork : Result<Nothing>()
}

inline val Result<*>.succeeded
    get() = this is Result.Success && data != null

inline fun <reified T> Result<T>.dataOrNull(): T? {
    if (this is Result.Success) {
        return data
    }
    return null
}

inline fun <reified T> resultFrom(block: () -> BaseResponse<T>): Result<T> {
    return runCatching {
        block.invoke().let {
            if (it.code != SUCCESS_CODE) {
                Result.Error(ServerException(it.code, it.msg ?: FALLBACK_ERROR_MESSAGE))
            } else {
                when {
                    Unit::class == T::class -> Result.Success(Unit as T)
                    null is T -> Result.Success(it.data as T)
                    else -> Result.Success(it.data!!)
                }
            }
        }
    }.onFailure {
        Result.Error(ServerException(FALLBACK_ERROR_CODE, FALLBACK_ERROR_MESSAGE))
    }.getOrDefault(Result.Error(ServerException(FALLBACK_ERROR_CODE, FALLBACK_ERROR_MESSAGE)))
}

class ServerException(val code: Int = -1, message: String) : Exception(message)

const val SUCCESS_CODE = 200

const val FALLBACK_ERROR_CODE = -1
const val FALLBACK_ERROR_MESSAGE = "Server error"