/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchLoaderFactory
 ** Description : 搜索的loader factory
 ** Version     : 1.0
 ** Date        : 2024/06/20 14:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/06/20       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.loader

import android.content.Context
import com.filemanager.common.utils.Log

object SearchLoaderFactory {

    private const val TAG = "SearchLoaderFactory"
    const val CATEGORY_DOC = "doc"

    @JvmStatic
    fun getSearchLoader(context: Context, categoryType: String): ILoader {
        val loader = when (categoryType) {
            CATEGORY_DOC -> SearchDocLoader(context)
            else -> SearchDocLoader(context)
        }
        return loader.apply {
            Log.d(TAG, "getSearchLoader category:$categoryType -> loader:$loader")
        }
    }
}