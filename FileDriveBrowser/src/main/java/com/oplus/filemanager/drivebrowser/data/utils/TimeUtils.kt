/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : TimeUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/7 14:05
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.utils

import android.os.SystemClock
import com.filemanager.common.utils.SdkUtils
import java.time.DateTimeException
import kotlin.math.abs

object TimeUtils {

    // 1 minutes
    const val VALID_TIMESTAMP_INTERVAL = 1000 * 60

    /**
     * Obtain request timestamp by [SystemClock.currentNetworkTimeClock] in android t and above.
     * Solve the problem that manually modifying the system time causes timestamp verification to fail.
     * When time difference is greater than 1 minute between network time and system time, @return network time.
     */
    @JvmStatic
    fun obtainTimestamp(): Long {
        val currentTimeStamp = System.currentTimeMillis()
        val networkTimeStamp: Long = if (SdkUtils.isAtLeastT()) {
            try {
                SystemClock.currentNetworkTimeClock().millis()
            } catch (e: DateTimeException) {
                currentTimeStamp
            }
        } else {
            currentTimeStamp
        }
        return if (abs(networkTimeStamp - currentTimeStamp) > VALID_TIMESTAMP_INTERVAL) {
            networkTimeStamp
        } else {
            currentTimeStamp
        }
    }
}