/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File: FileRenameDialog.kt
 ** Description: Rename File/Directory dialog
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.rename

import android.content.Context
import android.content.DialogInterface
import android.text.TextUtils
import android.view.WindowManager
import androidx.annotation.VisibleForTesting
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.fileutils.fetchFileName
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.interfaces.ForceDialogDimAmount
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.noMoreAction
import com.filemanager.fileoperate.R
import com.filemanager.fileoperate.base.BaseFileNameDialog
import com.filemanager.fileoperate.rename.FileRenameObserver.FileRenameBean.Companion.TYPE_CREATE_FOLDER
import com.filemanager.fileoperate.rename.FileRenameObserver.FileRenameBean.Companion.TYPE_RENAME_CLOUD_FILE
import com.filemanager.fileoperate.rename.FileRenameObserver.FileRenameBean.Companion.TYPE_RENAME_FILE
import org.apache.commons.io.FilenameUtils
import java.io.File
import java.util.Objects

class FileRenameDialog(val context: Context, renameBean: FileRenameObserver.FileRenameBean) : BaseFileNameDialog(context) {
    companion object {
        const val TAG = "FileRenameDialog"
        private const val DIM_AMOUNT_VALUE = 0.3f
    }

    private var mIsDrmFile = false
    @VisibleForTesting
    val mRenameBean: FileRenameObserver.FileRenameBean = renameBean
        get() = field.noMoreAction()
    private var mExtChangedNoticed = false
    private var mHideFileNoticed = false


    override fun show() {
        mDialogBuilder = constructorDialogBuilder().apply {
            setTitle(getTitleName())
            setNegativeButton(com.filemanager.common.R.string.dialog_cancel) { _, _ -> callbackWhenCloseDialog() }
            setOnCancelListener { callbackWhenCloseDialog() }
            setPositiveButton(com.filemanager.common.R.string.confirm, null)
            setWindowGravity(getBottomAlertDialogWindowGravity(context))
            setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(context))
            setBlurBackgroundDrawable(true)
        }
        mDialog = mDialogBuilder?.show(null)
        /*
        ShareActivity设置了backgroundDimEnabled为false导致新建dialog弹出无背景蒙层，这里判断手动添加设置蒙层。
         */
        if (context is ForceDialogDimAmount) {
            mDialog?.window?.apply {
                addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
                setDimAmount(DIM_AMOUNT_VALUE)
            }
        }
        initViewValues()
    }

    @VisibleForTesting
    fun initViewValues() {
        initViews { clickPositive() }
        mOppoEditText?.setHint(showEditTextHint())
        mOppoEditText?.ellipsize = TextUtils.TruncateAt.END
        initFileName()
    }

    override fun setMaxCount() {
        if (mRenameBean.type != TYPE_RENAME_FILE) {
            super.setMaxCount()
        } else if ((mRenameBean.srcOrParentFile.mDisplayName?.length
                ?: 0) <= getNameLengthLimit()
        ) {
            super.setMaxCount()
        }
    }

    @VisibleForTesting
    fun constructorDialogBuilder() = COUIAlertDialogBuilder(context, KtViewUtils.getDialogStyle())

    @VisibleForTesting
    fun callbackWhenCloseDialog() {
        mDialog?.let { dialog ->
            mRenameBean.resultListener?.onClick(dialog)
            mOppoEditText?.let { editText ->
                hideSoftInput(editText)
            }
        }
    }

    /**
     * 判断新文件是否存在
     * 该文件不能是自己本身
     */
    @VisibleForTesting
    fun checkNewFileExists(file: File): Boolean {
        return !Objects.equals(file.absolutePath, mRenameBean.srcOrParentFile.mData) && file.exists()
    }

    private fun clickPositive() {
        if (!TextUtils.isEmpty(getInputValue())) {
            if (!needSwitchToHideFileWarn() && !needModifyingExtensionNameWarn()) {
                val targetFile = when (mRenameBean.type) {
                    TYPE_CREATE_FOLDER -> {
                        File(mRenameBean.srcOrParentFile.mData, getInputValue())
                    }
                    TYPE_RENAME_FILE -> {
                        File(File(mRenameBean.srcOrParentFile.mData).parentFile, getInputValue())
                    }
                    else -> null
                }
                if ((targetFile != null) && JavaFileHelper.safeCheck({ checkNewFileExists(targetFile) }, true)) {
                    when (mRenameBean.type) {
                        TYPE_CREATE_FOLDER -> showWarningNotice(NOTICE_FOLDER_EXISTS)
                        TYPE_RENAME_FILE -> {
                            showWarningNotice(if (JavaFileHelper.safeCheck({ targetFile.isDirectory }, false)) {
                                NOTICE_FOLDER_EXISTS
                            } else {
                                NOTICE_FILENAME_EXISTS
                            })
                        }
                    }
                } else {
                    if (mRenameBean.resultListener != null) {
                        mDialog?.let { dialog ->
                            mRenameBean.resultListener?.onClick(dialog, DialogInterface.BUTTON_POSITIVE, getInputValue())
                        }
                    } else {
                        dismiss()
                    }
                }
            }
        } else {
            showWarningNotice(if (mRenameBean.srcOrParentFile.mIsDirectory) {
                NOTICE_FOLDERNAME_EMPTY
            } else {
                NOTICE_FILENAME_EMPTY
            })
        }
    }

    private fun initFileName() {
        if (checkInputText()) {
            return
        }
        var fileName: String? = null
        // Get the file/dir name
        when (mRenameBean.type) {
            TYPE_CREATE_FOLDER -> {
                val initName = context.resources.getString(com.filemanager.common.R.string.dialog_create_folder)
                val newFile = fetchFileName(mRenameBean.srcOrParentFile, initName, "")
                fileName = newFile?.mDisplayName
            }
            TYPE_RENAME_FILE, TYPE_RENAME_CLOUD_FILE -> {
                mRenameBean.srcOrParentFile.let {
                    fileName = it.mDisplayName
                }
            }
        }
        val nameLen = fileName?.length ?: 0

        setInitFileNameInfo(nameLen)
        mOppoEditText?.setText(fileName)
        val isDirectory = mRenameBean.srcOrParentFile.mData?.let {
            JavaFileHelper.safeCheck({ File(it).isDirectory }, true)
        } ?: false
        // Set file name selection
        if ((mRenameBean.type == TYPE_CREATE_FOLDER) || isDirectory || mRenameBean.type == TYPE_RENAME_CLOUD_FILE) {
            mOppoEditText?.selectAll()
        } else {
            val selectionStart = 0
            val selectionEnd = mOppoEditText?.text?.lastIndexOf('.') ?: -1
            if (selectionEnd > 0) {
                mOppoEditText?.setSelection(selectionStart, selectionEnd)
            } else {
                mOppoEditText?.selectAll()
            }
        }
    }

    @VisibleForTesting
    fun getTitleName(): Int {
        return when (mRenameBean.type) {
            TYPE_CREATE_FOLDER -> {
                com.filemanager.common.R.string.dialog_create_folder
            }
            TYPE_RENAME_FILE, TYPE_RENAME_CLOUD_FILE -> {
                if (mRenameBean.srcOrParentFile.mIsDirectory) {
                    com.filemanager.common.R.string.dialog_rename_folder_title
                } else {
                    com.filemanager.common.R.string.dialog_rename_file_title
                }
            }
            else -> {
                com.filemanager.common.R.string.dialog_create_folder
            }
        }
    }

    private fun showEditTextHint(): Int {
        return when (mRenameBean.type) {
            TYPE_CREATE_FOLDER -> com.filemanager.common.R.string.enter_folder_name
            else -> {
                if (mRenameBean.srcOrParentFile.mIsDirectory) {
                    com.filemanager.common.R.string.enter_folder_name
                } else {
                    com.filemanager.common.R.string.enter_file_name
                }
            }
        }
    }

    private fun needSwitchToHideFileWarn(): Boolean {
        if (mRenameBean.type != TYPE_RENAME_FILE) {
            //create dir not need toast
            return false
        }

        if (!HiddenFileHelper.isHiddenFile(mRenameBean.srcOrParentFile.mDisplayName) && HiddenFileHelper.isHiddenFile(getInputValue())) {
            return if (mHideFileNoticed) {
                false
            } else {
                mHideFileNoticed = true
                showWarningNotice(NOTICE_FILE_WILL_BE_HIDE)
                true
            }
        }
        return false
    }

    private fun needModifyingExtensionNameWarn(): Boolean {
        if (mRenameBean.type != TYPE_RENAME_FILE) {
            return false
        }

        val name = getInputValue()
        if (mRenameBean.srcOrParentFile.mIsDirectory || TextUtils.equals(mRenameBean.srcOrParentFile.mDisplayName, name)) {
            return false
        }

        val lastDotPosition = name.lastIndexOf('.')
        val modifiedExt = if ((lastDotPosition == -1) || (lastDotPosition == name.length)) {
            ""
        } else {
            name.substring(lastDotPosition + 1)
        }
        val srcExt = FilenameUtils.getExtension(mRenameBean.srcOrParentFile.mDisplayName)
        if (!modifiedExt.equals(srcExt, ignoreCase = true)) {
            Log.d(TAG, "needModifyingExtensionNameWarn: ext changed")
            val fileType = mRenameBean.srcOrParentFile.mLocalType
            if (fileType == MimeTypeHelper.DRM_TYPE) {
                showWarningNotice(NOTICE_FAIL_RENAME_DRM_FILE)
            } else {
                // Ext changed should confirm again
                if (mExtChangedNoticed) {
                    return false
                } else {
                    mExtChangedNoticed = true
                    showWarningNotice(NOTICE_FILE_EXTENSION_CHANGED)
                }
            }
            return true
        }
        return false
    }

    fun showWarningNotice(type: Int) {
        val resources = context.resources ?: return
        val notice = when (type) {
            NOTICE_FAIL_RENAME_DRM_FILE -> {
                mIsDrmFile = true
                setPositiveButtonEnabled(false)
                resources.getString(com.filemanager.common.R.string.string_drm_unable_to_modify_extension)
            }
            NOTICE_FILE_EXTENSION_CHANGED -> resources.getString(com.filemanager.common.R.string.warning_modify_extention_file_name)
            NOTICE_FILENAME_EXISTS -> resources.getString(com.filemanager.common.R.string.toast_file_exist)
            NOTICE_FOLDER_EXISTS -> resources.getString(com.filemanager.common.R.string.toast_folder_exist)
            NOTICE_FOLDER_CREATE_ERROR -> resources.getString(com.filemanager.common.R.string.toast_create_folder_error)
            NOTICE_FILENAME_DEEP -> resources.getString(com.filemanager.common.R.string.toast_file_name_deep_path)
            NOTICE_FILENAME_EMPTY -> resources.getString(com.filemanager.common.R.string.enter_file_name)
            NOTICE_FOLDERNAME_EMPTY -> resources.getString(com.filemanager.common.R.string.enter_folder_name)
            NOTICE_FILE_WILL_BE_HIDE -> resources.getString(com.filemanager.common.R.string.tips_the_file_will_be_hide_after_rename)
            else -> resources.getString(com.filemanager.common.R.string.unsupported_input_the_char)
        }
        mInputView?.showError(notice)
    }
}