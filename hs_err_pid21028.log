#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes for Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:110), pid=21028, tid=7352
#
# JRE version: Java(TM) SE Runtime Environment (17.0.8+9) (build 17.0.8+9-LTS-211)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\Encrypt\build\20250901_2202546886586593792.compiler.options

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Sep  1 12:32:52 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 6.466635 seconds (0d 0h 0m 6s)

---------------  T H R E A D  ---------------

Current thread (0x000001fc71c536e0):  JavaThread "main" [_thread_in_vm, id=7352, stack(0x0000009f60000000,0x0000009f60100000)]

Stack: [0x0000009f60000000,0x0000009f60100000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x677d0a]
V  [jvm.dll+0x7d8c54]
V  [jvm.dll+0x7da3fe]
V  [jvm.dll+0x7daa63]
V  [jvm.dll+0x245c5f]
V  [jvm.dll+0x7d4d5b]
V  [jvm.dll+0x61dcf6]
V  [jvm.dll+0x1c0127]
V  [jvm.dll+0x620650]
V  [jvm.dll+0x61e6b6]
V  [jvm.dll+0x23a29f]
V  [jvm.dll+0x628445]
V  [jvm.dll+0x1e9bdd]
V  [jvm.dll+0x1ea1a5]
V  [jvm.dll+0x1eaaeb]
V  [jvm.dll+0x1e02f1]
V  [jvm.dll+0x53ddbc]
V  [jvm.dll+0x753768]
V  [jvm.dll+0x753854]
V  [jvm.dll+0x40ba4f]
V  [jvm.dll+0x411a69]
C  [java.dll+0x17ec]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
J 513  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (0 bytes) @ 0x000001fc07bc3c63 [0x000001fc07bc3ba0+0x00000000000000c3]
J 626 c1 java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class; java.base@17.0.8 (43 bytes) @ 0x000001fc0010db5c [0x000001fc0010d800+0x000000000000035c]
J 670 c1 java.security.SecureClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class; java.base@17.0.8 (16 bytes) @ 0x000001fc0012560c [0x000001fc00125540+0x00000000000000cc]
J 1267 c1 jdk.internal.loader.BuiltinClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class; java.base@17.0.8 (121 bytes) @ 0x000001fc0020b604 [0x000001fc0020a2a0+0x0000000000001364]
J 477 c1 jdk.internal.loader.BuiltinClassLoader.findClassOnClassPathOrNull(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (64 bytes) @ 0x000001fc000b9664 [0x000001fc000b85e0+0x0000000000001084]
J 2162 c1 jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class; java.base@17.0.8 (143 bytes) @ 0x000001fc004263c4 [0x000001fc004254c0+0x0000000000000f04]
J 353 c1 jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class; java.base@17.0.8 (40 bytes) @ 0x000001fc0008208c [0x000001fc00081a60+0x000000000000062c]
J 352 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (7 bytes) @ 0x000001fc00080b4c [0x000001fc00080a40+0x000000000000010c]
v  ~StubRoutines::call_stub
j  kotlin.reflect.jvm.internal.KClassImpl$Data.<init>(Lkotlin/reflect/jvm/internal/KClassImpl;)V+20
j  kotlin.reflect.jvm.internal.KClassImpl$data$1.invoke()Lkotlin/reflect/jvm/internal/KClassImpl$Data;+8
j  kotlin.reflect.jvm.internal.KClassImpl$data$1.invoke()Ljava/lang/Object;+1
j  kotlin.reflect.jvm.internal.ReflectProperties$LazyVal.invoke()Ljava/lang/Object;+19
j  kotlin.reflect.jvm.internal.KClassImpl.getQualifiedName()Ljava/lang/String;+4
j  org.jetbrains.kotlin.util.TypeRegistry.getId(Lkotlin/reflect/KClass;)I+8
j  org.jetbrains.kotlin.util.TypeRegistry.generateNullableAccessor(Lkotlin/reflect/KClass;)Lorg/jetbrains/kotlin/util/NullableArrayMapAccessor;+12
j  org.jetbrains.kotlin.types.AnnotationsTypeAttributeKt.<clinit>()V+48
v  ~StubRoutines::call_stub
j  org.jetbrains.kotlin.types.KotlinType.getAnnotations()Lorg/jetbrains/kotlin/descriptors/annotations/Annotations;+4
j  org.jetbrains.kotlin.load.java.typeEnhancement.SignatureParts.getAnnotations(Lorg/jetbrains/kotlin/types/model/KotlinTypeMarker;)Ljava/lang/Iterable;+10
j  org.jetbrains.kotlin.load.java.typeEnhancement.AbstractSignatureParts.extractAndMergeDefaultQualifiers(Lorg/jetbrains/kotlin/types/model/KotlinTypeMarker;Lorg/jetbrains/kotlin/load/java/JavaTypeQualifiersByElementType;)Lorg/jetbrains/kotlin/load/java/JavaTypeQualifiersByElementType;+7
j  org.jetbrains.kotlin.load.java.typeEnhancement.AbstractSignatureParts.toIndexed(Lorg/jetbrains/kotlin/types/model/KotlinTypeMarker;)Ljava/util/List;+19
j  org.jetbrains.kotlin.load.java.typeEnhancement.AbstractSignatureParts.computeIndexedQualifiers(Lorg/jetbrains/kotlin/types/model/KotlinTypeMarker;Ljava/lang/Iterable;Lorg/jetbrains/kotlin/load/java/typeEnhancement/TypeEnhancementInfo;Z)Lkotlin/jvm/functions/Function1;+15
j  org.jetbrains.kotlin.load.java.typeEnhancement.SignatureEnhancement.enhance(Lorg/jetbrains/kotlin/load/java/typeEnhancement/SignatureParts;Lorg/jetbrains/kotlin/types/KotlinType;Ljava/util/List;Lorg/jetbrains/kotlin/load/java/typeEnhancement/TypeEnhancementInfo;Z)Lorg/jetbrains/kotlin/types/KotlinType;+25
j  org.jetbrains.kotlin.load.java.typeEnhancement.SignatureEnhancement.enhance$default(Lorg/jetbrains/kotlin/load/java/typeEnhancement/SignatureEnhancement;Lorg/jetbrains/kotlin/load/java/typeEnhancement/SignatureParts;Lorg/jetbrains/kotlin/types/KotlinType;Ljava/util/List;Lorg/jetbrains/kotlin/load/java/typeEnhancement/TypeEnhancementInfo;ZILjava/lang/Object;)Lorg/jetbrains/kotlin/types/KotlinType;+29
j  org.jetbrains.kotlin.load.java.typeEnhancement.SignatureEnhancement.enhanceSuperType(Lorg/jetbrains/kotlin/types/KotlinType;Lorg/jetbrains/kotlin/load/java/lazy/LazyJavaResolverContext;)Lorg/jetbrains/kotlin/types/KotlinType;+38
j  org.jetbrains.kotlin.load.java.lazy.descriptors.LazyJavaClassDescriptor$LazyJavaClassTypeConstructor.computeSupertypes()Ljava/util/Collection;+126
j  org.jetbrains.kotlin.types.AbstractTypeConstructor$supertypes$1.invoke()Lorg/jetbrains/kotlin/types/AbstractTypeConstructor$Supertypes;+8
j  org.jetbrains.kotlin.types.AbstractTypeConstructor$supertypes$1.invoke()Ljava/lang/Object;+1
J 2632 c1 org.jetbrains.kotlin.storage.LockBasedStorageManager$LockBasedLazyValue.invoke()Ljava/lang/Object; (257 bytes) @ 0x000001fc0052f5f4 [0x000001fc0052ee80+0x0000000000000774]
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$LockBasedLazyValueWithPostCompute.invoke()Ljava/lang/Object;+22
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$LockBasedNotNullLazyValueWithPostCompute.invoke()Ljava/lang/Object;+1
j  org.jetbrains.kotlin.types.AbstractTypeConstructor.computeNeighbours(Lorg/jetbrains/kotlin/types/TypeConstructor;Z)Ljava/util/Collection;+27
j  org.jetbrains.kotlin.types.AbstractTypeConstructor.access$computeNeighbours(Lorg/jetbrains/kotlin/types/AbstractTypeConstructor;Lorg/jetbrains/kotlin/types/TypeConstructor;Z)Ljava/util/Collection;+3
j  org.jetbrains.kotlin.types.AbstractTypeConstructor$supertypes$3$resultWithoutCycles$1.invoke(Lorg/jetbrains/kotlin/types/TypeConstructor;)Ljava/lang/Iterable;+12
j  org.jetbrains.kotlin.types.AbstractTypeConstructor$supertypes$3$resultWithoutCycles$1.invoke(Ljava/lang/Object;)Ljava/lang/Object;+5
j  org.jetbrains.kotlin.resolve.SupertypeLoopCheckerImpl.findLoopsInSupertypesAndDisconnect$lambda$1(Lkotlin/jvm/functions/Function1;Lorg/jetbrains/kotlin/types/TypeConstructor;)Ljava/lang/Iterable;+12
j  org.jetbrains.kotlin.resolve.SupertypeLoopCheckerImpl$$Lambda$180+0x000001fc203f37a0.getNeighbors(Ljava/lang/Object;)Ljava/lang/Iterable;+8
j  org.jetbrains.kotlin.utils.DFS.doDfs(Ljava/lang/Object;Lorg/jetbrains/kotlin/utils/DFS$Neighbors;Lorg/jetbrains/kotlin/utils/DFS$Visited;Lorg/jetbrains/kotlin/utils/DFS$NodeHandler;)V+60
j  org.jetbrains.kotlin.utils.DFS.doDfs(Ljava/lang/Object;Lorg/jetbrains/kotlin/utils/DFS$Neighbors;Lorg/jetbrains/kotlin/utils/DFS$Visited;Lorg/jetbrains/kotlin/utils/DFS$NodeHandler;)V+96
j  org.jetbrains.kotlin.utils.DFS.doDfs(Ljava/lang/Object;Lorg/jetbrains/kotlin/utils/DFS$Neighbors;Lorg/jetbrains/kotlin/utils/DFS$Visited;Lorg/jetbrains/kotlin/utils/DFS$NodeHandler;)V+96
j  org.jetbrains.kotlin.utils.DFS.dfs(Ljava/util/Collection;Lorg/jetbrains/kotlin/utils/DFS$Neighbors;Lorg/jetbrains/kotlin/utils/DFS$Visited;Lorg/jetbrains/kotlin/utils/DFS$NodeHandler;)Ljava/lang/Object;+64
j  org.jetbrains.kotlin.resolve.FindLoopsInSupertypes.isReachable(Lorg/jetbrains/kotlin/types/TypeConstructor;Lorg/jetbrains/kotlin/types/TypeConstructor;Lorg/jetbrains/kotlin/utils/DFS$Neighbors;)Z+38
j  org.jetbrains.kotlin.resolve.FindLoopsInSupertypes.access$isReachable(Lorg/jetbrains/kotlin/types/TypeConstructor;Lorg/jetbrains/kotlin/types/TypeConstructor;Lorg/jetbrains/kotlin/utils/DFS$Neighbors;)Z+3
j  org.jetbrains.kotlin.resolve.SupertypeLoopCheckerImpl.findLoopsInSupertypesAndDisconnect(Lorg/jetbrains/kotlin/types/TypeConstructor;Ljava/util/Collection;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)Ljava/util/Collection;+80
j  org.jetbrains.kotlin.types.AbstractTypeConstructor$supertypes$3.invoke(Lorg/jetbrains/kotlin/types/AbstractTypeConstructor$Supertypes;)V+52
j  org.jetbrains.kotlin.types.AbstractTypeConstructor$supertypes$3.invoke(Ljava/lang/Object;)Ljava/lang/Object;+5
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$5.doPostCompute(Ljava/lang/Object;)V+13
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$LockBasedLazyValueWithPostCompute.postCompute(Ljava/lang/Object;)V+14
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$LockBasedLazyValue.invoke()Ljava/lang/Object;+164
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$LockBasedLazyValueWithPostCompute.invoke()Ljava/lang/Object;+22
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$LockBasedNotNullLazyValueWithPostCompute.invoke()Ljava/lang/Object;+1
j  org.jetbrains.kotlin.types.AbstractTypeConstructor.getSupertypes()Ljava/util/List;+4
j  org.jetbrains.kotlin.types.AbstractTypeConstructor.getSupertypes()Ljava/util/Collection;+1
j  org.jetbrains.kotlin.resolve.descriptorUtil.DescriptorUtilsKt.getSuperClassNotAny(Lorg/jetbrains/kotlin/descriptors/ClassDescriptor;)Lorg/jetbrains/kotlin/descriptors/ClassDescriptor;+15
j  org.jetbrains.kotlin.resolve.descriptorUtil.DescriptorUtilsKt.getAllSuperclassesWithoutAny(Lorg/jetbrains/kotlin/descriptors/ClassDescriptor;)Lorg/jetbrains/kotlin/utils/SmartList;+7
j  org.jetbrains.kotlin.resolve.lazy.descriptors.ClassResolutionScopesSupport$inheritanceScopeWithoutMe$1.invoke()Lorg/jetbrains/kotlin/resolve/scopes/LexicalScope;+7
j  org.jetbrains.kotlin.resolve.lazy.descriptors.ClassResolutionScopesSupport$inheritanceScopeWithoutMe$1.invoke()Ljava/lang/Object;+1
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$LockBasedLazyValue.invoke()Ljava/lang/Object;+156
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$LockBasedNotNullLazyValue.invoke()Ljava/lang/Object;+1
j  org.jetbrains.kotlin.resolve.lazy.descriptors.ClassResolutionScopesSupport$inheritanceScopeWithMe$1.invoke()Lorg/jetbrains/kotlin/resolve/scopes/LexicalScope;+11
j  org.jetbrains.kotlin.resolve.lazy.descriptors.ClassResolutionScopesSupport$inheritanceScopeWithMe$1.invoke()Ljava/lang/Object;+1
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$LockBasedLazyValue.invoke()Ljava/lang/Object;+156
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$LockBasedNotNullLazyValue.invoke()Ljava/lang/Object;+1
j  org.jetbrains.kotlin.resolve.lazy.descriptors.ClassResolutionScopesSupport$scopeForMemberDeclarationResolution$1.invoke()Lorg/jetbrains/kotlin/resolve/scopes/LexicalScope;+11
j  org.jetbrains.kotlin.resolve.lazy.descriptors.ClassResolutionScopesSupport$scopeForMemberDeclarationResolution$1.invoke()Ljava/lang/Object;+1
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$LockBasedLazyValue.invoke()Ljava/lang/Object;+156
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$LockBasedNotNullLazyValue.invoke()Ljava/lang/Object;+1
j  org.jetbrains.kotlin.resolve.lazy.descriptors.LazyClassDescriptor.getScopeForMemberDeclarationResolution()Lorg/jetbrains/kotlin/resolve/scopes/LexicalScope;+7
j  org.jetbrains.kotlin.resolve.lazy.descriptors.LazyClassMemberScope.getScopeForMemberDeclarationResolution(Lorg/jetbrains/kotlin/psi/KtDeclaration;)Lorg/jetbrains/kotlin/resolve/scopes/LexicalScope;+14
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope.getDeclaredFunctions(Lorg/jetbrains/kotlin/name/Name;)Ljava/util/Collection;+207
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope.access$getDeclaredFunctions(Lorg/jetbrains/kotlin/resolve/lazy/descriptors/AbstractLazyMemberScope;Lorg/jetbrains/kotlin/name/Name;)Ljava/util/Collection;+2
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope$declaredFunctionDescriptors$1.invoke(Lorg/jetbrains/kotlin/name/Name;)Ljava/util/Collection;+11
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope$declaredFunctionDescriptors$1.invoke(Ljava/lang/Object;)Ljava/lang/Object;+5
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$MapBasedMemoizedFunction.invoke(Ljava/lang/Object;)Ljava/lang/Object;+182
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$MapBasedMemoizedFunctionToNotNull.invoke(Ljava/lang/Object;)Ljava/lang/Object;+2
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope.doGetFunctions(Lorg/jetbrains/kotlin/name/Name;)Ljava/util/Collection;+9
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope.access$doGetFunctions(Lorg/jetbrains/kotlin/resolve/lazy/descriptors/AbstractLazyMemberScope;Lorg/jetbrains/kotlin/name/Name;)Ljava/util/Collection;+2
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope$functionDescriptors$1.invoke(Lorg/jetbrains/kotlin/name/Name;)Ljava/util/Collection;+11
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope$functionDescriptors$1.invoke(Ljava/lang/Object;)Ljava/lang/Object;+5
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$MapBasedMemoizedFunction.invoke(Ljava/lang/Object;)Ljava/lang/Object;+182
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$MapBasedMemoizedFunctionToNotNull.invoke(Ljava/lang/Object;)Ljava/lang/Object;+2
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope.getContributedFunctions(Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Ljava/util/Collection;+25
j  org.jetbrains.kotlin.resolve.lazy.descriptors.LazyClassMemberScope.getContributedFunctions(Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Ljava/util/Collection;+17
j  org.jetbrains.kotlin.resolve.lazy.LazyDeclarationResolver$resolveToDescriptor$1.visitNamedFunction(Lorg/jetbrains/kotlin/psi/KtNamedFunction;Ljava/lang/Void;)Lorg/jetbrains/kotlin/descriptors/DeclarationDescriptor;+47
j  org.jetbrains.kotlin.resolve.lazy.LazyDeclarationResolver$resolveToDescriptor$1.visitNamedFunction(Lorg/jetbrains/kotlin/psi/KtNamedFunction;Ljava/lang/Object;)Ljava/lang/Object;+6
j  org.jetbrains.kotlin.psi.KtNamedFunction.accept(Lorg/jetbrains/kotlin/psi/KtVisitor;Ljava/lang/Object;)Ljava/lang/Object;+11
j  org.jetbrains.kotlin.resolve.lazy.LazyDeclarationResolver.resolveToDescriptor(Lorg/jetbrains/kotlin/psi/KtDeclaration;Z)Lorg/jetbrains/kotlin/descriptors/DeclarationDescriptor;+14
j  org.jetbrains.kotlin.resolve.lazy.LazyDeclarationResolver.resolveToDescriptor(Lorg/jetbrains/kotlin/psi/KtDeclaration;)Lorg/jetbrains/kotlin/descriptors/DeclarationDescriptor;+9
j  org.jetbrains.kotlin.resolve.LazyTopDownAnalyzer.createFunctionDescriptors(Lorg/jetbrains/kotlin/resolve/TopDownAnalysisContext;Ljava/util/List;)V+36
j  org.jetbrains.kotlin.resolve.LazyTopDownAnalyzer.analyzeDeclarations(Lorg/jetbrains/kotlin/resolve/TopDownAnalysisMode;Ljava/util/Collection;Lorg/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowInfo;Lorg/jetbrains/kotlin/types/expressions/ExpressionTypingContext;)Lorg/jetbrains/kotlin/resolve/TopDownAnalysisContext;+178
j  org.jetbrains.kotlin.resolve.LazyTopDownAnalyzer.analyzeDeclarations$default(Lorg/jetbrains/kotlin/resolve/LazyTopDownAnalyzer;Lorg/jetbrains/kotlin/resolve/TopDownAnalysisMode;Ljava/util/Collection;Lorg/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowInfo;Lorg/jetbrains/kotlin/types/expressions/ExpressionTypingContext;ILjava/lang/Object;)Lorg/jetbrains/kotlin/resolve/TopDownAnalysisContext;+31
j  org.jetbrains.kotlin.cli.jvm.compiler.TopDownAnalyzerFacadeForJVM.analyzeFilesWithJavaIntegration(Lorg/jetbrains/kotlin/com/intellij/openapi/project/Project;Ljava/util/Collection;Lorg/jetbrains/kotlin/resolve/BindingTrace;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;Lorg/jetbrains/kotlin/com/intellij/psi/search/GlobalSearchScope;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lorg/jetbrains/kotlin/resolve/TargetEnvironment;)Lorg/jetbrains/kotlin/analyzer/AnalysisResult;+267
j  org.jetbrains.kotlin.cli.jvm.compiler.TopDownAnalyzerFacadeForJVM.analyzeFilesWithJavaIntegration$default(Lorg/jetbrains/kotlin/com/intellij/openapi/project/Project;Ljava/util/Collection;Lorg/jetbrains/kotlin/resolve/BindingTrace;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;Lorg/jetbrains/kotlin/com/intellij/psi/search/GlobalSearchScope;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lorg/jetbrains/kotlin/resolve/TargetEnvironment;ILjava/lang/Object;)Lorg/jetbrains/kotlin/analyzer/AnalysisResult;+111
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler$analyze$1.invoke()Lorg/jetbrains/kotlin/analyzer/AnalysisResult;+273
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler$analyze$1.invoke()Ljava/lang/Object;+1
j  org.jetbrains.kotlin.cli.common.messages.AnalyzerWithCompilerReport.analyzeAndReport(Ljava/util/Collection;Lkotlin/jvm/functions/Function0;)V+16
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler.analyze(Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment;)Lorg/jetbrains/kotlin/analyzer/AnalysisResult;+160
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler.compileModules$cli(Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment;Ljava/io/File;Ljava/util/List;Z)Z+406
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler.compileModules$cli$default(Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinToJVMBytecodeCompiler;Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment;Ljava/io/File;Ljava/util/List;ZILjava/lang/Object;)Z+17
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(Lorg/jetbrains/kotlin/cli/common/arguments/K2JVMCompilerArguments;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/utils/KotlinPaths;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+1046
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(Lorg/jetbrains/kotlin/cli/common/arguments/CommonCompilerArguments;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/utils/KotlinPaths;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+9
j  org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonCompilerArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+220
j  org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonToolArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+7
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonToolArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+76
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Ljava/io/PrintStream;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;[Ljava/lang/String;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+239
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Ljava/io/PrintStream;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;[Ljava/lang/String;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+25
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMainNoExit(Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+39
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMainNoExit$default(Lorg/jetbrains/kotlin/cli/common/CLITool$Companion;Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;ILjava/lang/Object;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+16
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMain(Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;)V+54
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler$Companion.main([Ljava/lang/String;)V+20
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.main([Ljava/lang/String;)V+4
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001fc65bc26c0, length=13, elements={
0x000001fc71c536e0, 0x000001fc7f60ef80, 0x000001fc7f60fd00, 0x000001fc7f626210,
0x000001fc7f626ad0, 0x000001fc7f627390, 0x000001fc7f628050, 0x000001fc7f62c550,
0x000001fc7f634f10, 0x000001fc7f645740, 0x000001fc6017ac90, 0x000001fc6018a650,
0x000001fc60193b60
}

Java Threads: ( => current thread )
=>0x000001fc71c536e0 JavaThread "main" [_thread_in_vm, id=7352, stack(0x0000009f60000000,0x0000009f60100000)]
  0x000001fc7f60ef80 JavaThread "Reference Handler" daemon [_thread_blocked, id=2928, stack(0x0000009f60700000,0x0000009f60800000)]
  0x000001fc7f60fd00 JavaThread "Finalizer" daemon [_thread_blocked, id=17712, stack(0x0000009f60800000,0x0000009f60900000)]
  0x000001fc7f626210 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=12744, stack(0x0000009f60900000,0x0000009f60a00000)]
  0x000001fc7f626ad0 JavaThread "Attach Listener" daemon [_thread_blocked, id=19384, stack(0x0000009f60a00000,0x0000009f60b00000)]
  0x000001fc7f627390 JavaThread "Service Thread" daemon [_thread_blocked, id=1912, stack(0x0000009f60b00000,0x0000009f60c00000)]
  0x000001fc7f628050 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=12400, stack(0x0000009f60c00000,0x0000009f60d00000)]
  0x000001fc7f62c550 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=27404, stack(0x0000009f60d00000,0x0000009f60e00000)]
  0x000001fc7f634f10 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=24988, stack(0x0000009f60e00000,0x0000009f60f00000)]
  0x000001fc7f645740 JavaThread "Sweeper thread" daemon [_thread_blocked, id=21692, stack(0x0000009f60f00000,0x0000009f61000000)]
  0x000001fc6017ac90 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=21408, stack(0x0000009f61000000,0x0000009f61100000)]
  0x000001fc6018a650 JavaThread "Notification Thread" daemon [_thread_blocked, id=13592, stack(0x0000009f61100000,0x0000009f61200000)]
  0x000001fc60193b60 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=27824, stack(0x0000009f61300000,0x0000009f61400000)]

Other Threads:
  0x000001fc7f60abc0 VMThread "VM Thread" [stack: 0x0000009f60600000,0x0000009f60700000] [id=21380]
  0x000001fc71d21e10 WatcherThread [stack: 0x0000009f61200000,0x0000009f61300000] [id=24736]
  0x000001fc71d00840 GCTaskThread "GC Thread#0" [stack: 0x0000009f60100000,0x0000009f60200000] [id=21388]
  0x000001fc606e74d0 GCTaskThread "GC Thread#1" [stack: 0x0000009f61700000,0x0000009f61800000] [id=18820]
  0x000001fc606e7780 GCTaskThread "GC Thread#2" [stack: 0x0000009f61800000,0x0000009f61900000] [id=27776]
  0x000001fc606bb350 GCTaskThread "GC Thread#3" [stack: 0x0000009f61900000,0x0000009f61a00000] [id=15292]
  0x000001fc606bb600 GCTaskThread "GC Thread#4" [stack: 0x0000009f61a00000,0x0000009f61b00000] [id=28052]
  0x000001fc606bb8b0 GCTaskThread "GC Thread#5" [stack: 0x0000009f61b00000,0x0000009f61c00000] [id=26088]
  0x000001fc606bbb60 GCTaskThread "GC Thread#6" [stack: 0x0000009f61c00000,0x0000009f61d00000] [id=5552]
  0x000001fc606bbe10 GCTaskThread "GC Thread#7" [stack: 0x0000009f61d00000,0x0000009f61e00000] [id=21668]
  0x000001fc606bc0c0 GCTaskThread "GC Thread#8" [stack: 0x0000009f61e00000,0x0000009f61f00000] [id=8932]
  0x000001fc606bc370 GCTaskThread "GC Thread#9" [stack: 0x0000009f61f00000,0x0000009f62000000] [id=21456]
  0x000001fc606de5e0 GCTaskThread "GC Thread#10" [stack: 0x0000009f62000000,0x0000009f62100000] [id=10392]
  0x000001fc606df600 GCTaskThread "GC Thread#11" [stack: 0x0000009f62100000,0x0000009f62200000] [id=24776]
  0x000001fc659d3590 GCTaskThread "GC Thread#12" [stack: 0x0000009f62200000,0x0000009f62300000] [id=28392]
  0x000001fc71d12460 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000009f60200000,0x0000009f60300000] [id=20816]
  0x000001fc71d13520 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000009f60300000,0x0000009f60400000] [id=20972]
  0x000001fc606dedf0 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000009f62300000,0x0000009f62400000] [id=24036]
  0x000001fc606df0a0 ConcurrentGCThread "G1 Conc#2" [stack: 0x0000009f62400000,0x0000009f62500000] [id=28804]
  0x000001fc71d2e9f0 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000009f60400000,0x0000009f60500000] [id=16732]
  0x000001fc65a481c0 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000009f62500000,0x0000009f62600000] [id=28836]
  0x000001fc7f5414c0 ConcurrentGCThread "G1 Service" [stack: 0x0000009f60500000,0x0000009f60600000] [id=24236]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001fc71c4e3f0] Metaspace_lock - owner thread: 0x000001fc71c536e0

Heap address: 0x0000000604800000, size: 8120 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001fc1f000000-0x000001fc1fbd0000-0x000001fc1fbd0000), size 12386304, SharedBaseAddress: 0x000001fc1f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001fc20000000-0x000001fc60000000, reserved size: 1073741824
Narrow klass base: 0x000001fc1f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32479M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8120M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 69632K, used 30406K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 1 survivors (4096K)
 Metaspace       used 32615K, committed 32768K, reserved 1114112K
  class space    used 4063K, committed 4160K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%|HS|  |TAMS 0x0000000604c00000, 0x0000000604800000| Complete 
|   1|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   2|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|   3|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%| O|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|   4|0x0000000605800000, 0x0000000605c00000, 0x0000000605c00000|100%| O|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|   5|0x0000000605c00000, 0x0000000605d04c00, 0x0000000606000000| 25%| O|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|   6|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|   7|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|   8|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|   9|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  10|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  11|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  12|0x0000000607800000, 0x0000000607bdbbd0, 0x0000000607c00000| 96%| E|  |TAMS 0x0000000607800000, 0x0000000607800000| Complete 
|  13|0x0000000607c00000, 0x0000000608000000, 0x0000000608000000|100%| E|CS|TAMS 0x0000000607c00000, 0x0000000607c00000| Complete 
| 113|0x0000000620c00000, 0x0000000621000000, 0x0000000621000000|100%| E|CS|TAMS 0x0000000620c00000, 0x0000000620c00000| Complete 
| 114|0x0000000621000000, 0x00000006210acee0, 0x0000000621400000| 16%| S|CS|TAMS 0x0000000621000000, 0x0000000621000000| Complete 
| 126|0x0000000624000000, 0x0000000624400000, 0x0000000624400000|100%| E|CS|TAMS 0x0000000624000000, 0x0000000624000000| Complete 

Card table byte_map: [0x000001fc7ad80000,0x000001fc7bd60000] _byte_map_base: 0x000001fc77d5c000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001fc71d01db0, (CMBitMap*) 0x000001fc71d01d70
 Prev Bits: [0x000001fc16ee0000, 0x000001fc1edc0000)
 Next Bits: [0x000001fc0f000000, 0x000001fc16ee0000)

Polling page: 0x000001fc6f9b0000

Metaspace:

Usage:
  Non-class:     27.88 MB used.
      Class:      3.97 MB used.
       Both:     31.85 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      27.94 MB ( 44%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.06 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      32.00 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  2.36 MB
       Class:  11.94 MB
        Both:  14.30 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.31 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 140.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 511.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 3.
num_chunks_taken_from_freelist: 1229.
num_chunk_merges: 3.
num_chunk_splits: 1022.
num_chunks_enlarged: 938.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=1113Kb max_used=1113Kb free=118054Kb
 bounds [0x000001fc07ba0000, 0x000001fc07e10000, 0x000001fc0f000000]
CodeHeap 'profiled nmethods': size=119104Kb used=5456Kb max_used=5456Kb free=113647Kb
 bounds [0x000001fc00000000, 0x000001fc00560000, 0x000001fc07450000]
CodeHeap 'non-nmethods': size=7488Kb used=1810Kb max_used=3003Kb free=5677Kb
 bounds [0x000001fc07450000, 0x000001fc07750000, 0x000001fc07ba0000]
 total_blobs=3237 nmethods=2683 adapters=465
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 5.834 Thread 0x000001fc6017ac90 nmethod 2673 0x000001fc00549790 code [0x000001fc00549940, 0x000001fc00549ac8]
Event: 5.842 Thread 0x000001fc6017ac90 2674       3       kotlin.ranges.IntProgressionIterator::nextInt (51 bytes)
Event: 5.842 Thread 0x000001fc7f634f10 2675       3       gnu.trove.THashMap::put (102 bytes)
Event: 5.842 Thread 0x000001fc6017ac90 nmethod 2674 0x000001fc00549b90 code [0x000001fc00549d40, 0x000001fc00549fe8]
Event: 5.843 Thread 0x000001fc7f634f10 nmethod 2675 0x000001fc0054a110 code [0x000001fc0054a320, 0x000001fc0054ab68]
Event: 5.845 Thread 0x000001fc7f634f10 2676       3       jdk.internal.org.objectweb.asm.ClassWriter::visit (160 bytes)
Event: 5.845 Thread 0x000001fc6017ac90 2677       3       jdk.internal.org.objectweb.asm.SymbolTable::setMajorVersionAndClassName (19 bytes)
Event: 5.845 Thread 0x000001fc6017ac90 nmethod 2677 0x000001fc0054ae10 code [0x000001fc0054afc0, 0x000001fc0054b1c8]
Event: 5.845 Thread 0x000001fc6017ac90 2678       3       jdk.internal.org.objectweb.asm.ClassWriter::toByteArray (1515 bytes)
Event: 5.846 Thread 0x000001fc7f634f10 nmethod 2676 0x000001fc0054b310 code [0x000001fc0054b540, 0x000001fc0054bdb8]
Event: 5.846 Thread 0x000001fc7f634f10 2679       3       jdk.internal.org.objectweb.asm.SymbolTable::computeBootstrapMethodsSize (27 bytes)
Event: 5.846 Thread 0x000001fc7f634f10 nmethod 2679 0x000001fc0054c210 code [0x000001fc0054c3c0, 0x000001fc0054c5a8]
Event: 5.846 Thread 0x000001fc7f634f10 2680       3       jdk.internal.org.objectweb.asm.SymbolTable::getConstantPoolLength (8 bytes)
Event: 5.846 Thread 0x000001fc7f634f10 nmethod 2680 0x000001fc0054c690 code [0x000001fc0054c820, 0x000001fc0054c938]
Event: 5.846 Thread 0x000001fc7f634f10 2681       3       jdk.internal.org.objectweb.asm.ByteVector::<init> (12 bytes)
Event: 5.846 Thread 0x000001fc7f634f10 nmethod 2681 0x000001fc0054ca10 code [0x000001fc0054cbc0, 0x000001fc0054ce38]
Event: 5.846 Thread 0x000001fc7f634f10 2682       3       jdk.internal.org.objectweb.asm.SymbolTable::putConstantPool (28 bytes)
Event: 5.846 Thread 0x000001fc7f634f10 nmethod 2682 0x000001fc0054cf10 code [0x000001fc0054d0c0, 0x000001fc0054d298]
Event: 5.846 Thread 0x000001fc7f634f10 2683       3       jdk.internal.org.objectweb.asm.SymbolTable::putBootstrapMethods (56 bytes)
Event: 5.846 Thread 0x000001fc7f634f10 nmethod 2683 0x000001fc0054d390 code [0x000001fc0054d580, 0x000001fc0054d888]

GC Heap History (10 events):
Event: 0.589 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 520192K, used 24576K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 6767K, committed 6912K, reserved 1114112K
  class space    used 700K, committed 768K, reserved 1048576K
}
Event: 0.606 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 6922K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 6767K, committed 6912K, reserved 1114112K
  class space    used 700K, committed 768K, reserved 1048576K
}
Event: 1.368 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 35594K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 15280K, committed 15488K, reserved 1114112K
  class space    used 2020K, committed 2112K, reserved 1048576K
}
Event: 1.370 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 9042K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 15280K, committed 15488K, reserved 1114112K
  class space    used 2020K, committed 2112K, reserved 1048576K
}
Event: 2.542 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 50002K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 1 survivors (4096K)
 Metaspace       used 21359K, committed 21504K, reserved 1114112K
  class space    used 2889K, committed 2944K, reserved 1048576K
}
Event: 2.545 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 520192K, used 14511K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 21359K, committed 21504K, reserved 1114112K
  class space    used 2889K, committed 2944K, reserved 1048576K
}
Event: 3.447 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 69632K, used 47279K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 10 young (40960K), 2 survivors (8192K)
 Metaspace       used 27267K, committed 27456K, reserved 1114112K
  class space    used 3453K, committed 3520K, reserved 1048576K
}
Event: 3.452 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 69632K, used 17626K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 27267K, committed 27456K, reserved 1114112K
  class space    used 3453K, committed 3520K, reserved 1048576K
}
Event: 3.617 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 69632K, used 21722K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 28437K, committed 28608K, reserved 1114112K
  class space    used 3583K, committed 3648K, reserved 1048576K
}
Event: 3.624 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 69632K, used 18118K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 28437K, committed 28608K, reserved 1114112K
  class space    used 3583K, committed 3648K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 5.255 Thread 0x000001fc71c536e0 DEOPT PACKING pc=0x000001fc07c3e25c sp=0x0000009f600fbb90
Event: 5.255 Thread 0x000001fc71c536e0 DEOPT UNPACKING pc=0x000001fc074a23a3 sp=0x0000009f600fba78 mode 2
Event: 5.260 Thread 0x000001fc71c536e0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001fc07c3e25c relative=0x00000000000005fc
Event: 5.260 Thread 0x000001fc71c536e0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001fc07c3e25c method=org.jetbrains.kotlin.protobuf.CodedInputStream.tryRefillBuffer(I)Z @ 177 c2
Event: 5.260 Thread 0x000001fc71c536e0 DEOPT PACKING pc=0x000001fc07c3e25c sp=0x0000009f600fbb90
Event: 5.260 Thread 0x000001fc71c536e0 DEOPT UNPACKING pc=0x000001fc074a23a3 sp=0x0000009f600fba78 mode 2
Event: 5.282 Thread 0x000001fc71c536e0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001fc07c3e25c relative=0x00000000000005fc
Event: 5.282 Thread 0x000001fc71c536e0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001fc07c3e25c method=org.jetbrains.kotlin.protobuf.CodedInputStream.tryRefillBuffer(I)Z @ 177 c2
Event: 5.282 Thread 0x000001fc71c536e0 DEOPT PACKING pc=0x000001fc07c3e25c sp=0x0000009f600fbb10
Event: 5.282 Thread 0x000001fc71c536e0 DEOPT UNPACKING pc=0x000001fc074a23a3 sp=0x0000009f600fb9f0 mode 2
Event: 5.286 Thread 0x000001fc71c536e0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001fc07c3e25c relative=0x00000000000005fc
Event: 5.286 Thread 0x000001fc71c536e0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001fc07c3e25c method=org.jetbrains.kotlin.protobuf.CodedInputStream.tryRefillBuffer(I)Z @ 177 c2
Event: 5.286 Thread 0x000001fc71c536e0 DEOPT PACKING pc=0x000001fc07c3e25c sp=0x0000009f600fbb10
Event: 5.286 Thread 0x000001fc71c536e0 DEOPT UNPACKING pc=0x000001fc074a23a3 sp=0x0000009f600fb9f0 mode 2
Event: 5.300 Thread 0x000001fc71c536e0 DEOPT PACKING pc=0x000001fc00421348 sp=0x0000009f600fbad0
Event: 5.300 Thread 0x000001fc71c536e0 DEOPT UNPACKING pc=0x000001fc074a2b43 sp=0x0000009f600fafe8 mode 0
Event: 5.428 Thread 0x000001fc71c536e0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001fc07c3f8cc relative=0x00000000000002ec
Event: 5.428 Thread 0x000001fc71c536e0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001fc07c3f8cc method=org.jetbrains.kotlin.protobuf.CodedInputStream.tryRefillBuffer(I)Z @ 177 c2
Event: 5.428 Thread 0x000001fc71c536e0 DEOPT PACKING pc=0x000001fc07c3f8cc sp=0x0000009f600fba60
Event: 5.428 Thread 0x000001fc71c536e0 DEOPT UNPACKING pc=0x000001fc074a23a3 sp=0x0000009f600fb998 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.715 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d41ed0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x0000000623d41ed0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.717 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d51290}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object, java.lang.Object)'> (0x0000000623d51290) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.718 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d5fe20}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int, int)'> (0x0000000623d5fe20) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.719 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d6e880}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, long, long)'> (0x0000000623d6e880) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.721 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d7ce98}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int)'> (0x0000000623d7ce98) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.752 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623eb3e10}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623eb3e10) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.756 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623edcf58}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623edcf58) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.771 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623fbc3c0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623fbc3c0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.834 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623adf598}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623adf598) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.892 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006236a2e28}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000006236a2e28) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.984 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062335ed28}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x000000062335ed28) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.993 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233d8e38}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006233d8e38) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.017 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c94fb0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c94fb0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.077 Thread 0x000001fc71c536e0 Implicit null exception at 0x000001fc07bc74f2 to 0x000001fc07bc7b4c
Event: 2.295 Thread 0x000001fc71c536e0 Implicit null exception at 0x000001fc07bcaa72 to 0x000001fc07bcb0ec
Event: 2.409 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000621deb700}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000621deb700) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.724 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006241cdf68}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x00000006241cdf68) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.797 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006243e37e0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006243e37e0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.797 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006243e7e68}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006243e7e68) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.797 Thread 0x000001fc71c536e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006243ebcd8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006243ebcd8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 3.447 Executing VM operation: G1CollectForAllocation
Event: 3.452 Executing VM operation: G1CollectForAllocation done
Event: 3.617 Executing VM operation: G1CollectForAllocation
Event: 3.624 Executing VM operation: G1CollectForAllocation done
Event: 3.776 Executing VM operation: HandshakeAllThreads
Event: 3.776 Executing VM operation: HandshakeAllThreads done
Event: 3.794 Executing VM operation: HandshakeAllThreads
Event: 3.794 Executing VM operation: HandshakeAllThreads done
Event: 3.914 Executing VM operation: HandshakeAllThreads
Event: 3.914 Executing VM operation: HandshakeAllThreads done
Event: 4.061 Executing VM operation: HandshakeAllThreads
Event: 4.061 Executing VM operation: HandshakeAllThreads done
Event: 4.109 Executing VM operation: HandshakeAllThreads
Event: 4.109 Executing VM operation: HandshakeAllThreads done
Event: 5.123 Executing VM operation: Cleanup
Event: 5.123 Executing VM operation: Cleanup done
Event: 5.253 Executing VM operation: HandshakeAllThreads
Event: 5.253 Executing VM operation: HandshakeAllThreads done
Event: 5.836 Executing VM operation: HandshakeAllThreads
Event: 5.836 Executing VM operation: HandshakeAllThreads done

Events (20 events):
Event: 3.914 loading class java/util/AbstractList$ListItr
Event: 3.914 loading class java/util/AbstractList$ListItr done
Event: 4.432 Thread 0x000001fc65ba6af0 Thread exited: 0x000001fc65ba6af0
Event: 4.578 Thread 0x000001fc65ba3fc0 Thread exited: 0x000001fc65ba3fc0
Event: 4.609 Thread 0x000001fc65ba3fc0 Thread added: 0x000001fc65ba3fc0
Event: 5.217 Thread 0x000001fc65ba3fc0 Thread exited: 0x000001fc65ba3fc0
Event: 5.218 Thread 0x000001fc6054f9a0 Thread exited: 0x000001fc6054f9a0
Event: 5.218 Thread 0x000001fc6043c660 Thread exited: 0x000001fc6043c660
Event: 5.234 loading class java/security/AccessControlException
Event: 5.234 loading class java/security/AccessControlException done
Event: 5.246 loading class java/lang/annotation/ElementType
Event: 5.246 loading class java/lang/annotation/ElementType done
Event: 5.246 loading class java/lang/Override
Event: 5.246 loading class java/lang/Override done
Event: 5.428 loading class java/util/ArrayList$SubList$1
Event: 5.428 loading class java/util/ArrayList$SubList$1 done
Event: 5.634 loading class java/text/StringCharacterIterator
Event: 5.634 loading class java/text/CharacterIterator
Event: 5.634 loading class java/text/CharacterIterator done
Event: 5.634 loading class java/text/StringCharacterIterator done


Dynamic libraries:
0x00007ff708300000 - 0x00007ff708310000 	E:\JAVA\bin\java.exe
0x00007ff827030000 - 0x00007ff827228000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8255d0000 - 0x00007ff825692000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff824d50000 - 0x00007ff825046000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8246c0000 - 0x00007ff8247c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff81fde0000 - 0x00007ff81fdf9000 	E:\JAVA\bin\jli.dll
0x00007ff81fdc0000 - 0x00007ff81fddb000 	E:\JAVA\bin\VCRUNTIME140.dll
0x00007ff826e80000 - 0x00007ff826f31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff826340000 - 0x00007ff8263de000 	C:\Windows\System32\msvcrt.dll
0x00007ff825e80000 - 0x00007ff825f1f000 	C:\Windows\System32\sechost.dll
0x00007ff825b10000 - 0x00007ff825c33000 	C:\Windows\System32\RPCRT4.dll
0x00007ff824a20000 - 0x00007ff824a47000 	C:\Windows\System32\bcrypt.dll
0x00007ff825050000 - 0x00007ff8251ed000 	C:\Windows\System32\USER32.dll
0x00007ff81dfe0000 - 0x00007ff81e27a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8248a0000 - 0x00007ff8248c2000 	C:\Windows\System32\win32u.dll
0x00007ff825da0000 - 0x00007ff825dcb000 	C:\Windows\System32\GDI32.dll
0x00007ff824a50000 - 0x00007ff824b69000 	C:\Windows\System32\gdi32full.dll
0x00007ff823400000 - 0x00007ff82340a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff824980000 - 0x00007ff824a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8253f0000 - 0x00007ff82541f000 	C:\Windows\System32\IMM32.DLL
0x00007ff81fd50000 - 0x00007ff81fd5c000 	E:\JAVA\bin\vcruntime140_1.dll
0x00007ffffb0c0000 - 0x00007ffffb14e000 	E:\JAVA\bin\msvcp140.dll
0x00007fffe0350000 - 0x00007fffe0f2e000 	E:\JAVA\bin\server\jvm.dll
0x00007ff826f40000 - 0x00007ff826f48000 	C:\Windows\System32\PSAPI.DLL
0x00007fffeb340000 - 0x00007fffeb349000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff81e3c0000 - 0x00007ff81e3e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff826e10000 - 0x00007ff826e7b000 	C:\Windows\System32\WS2_32.dll
0x00007ff823360000 - 0x00007ff823372000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff81c4f0000 - 0x00007ff81c4fa000 	E:\JAVA\bin\jimage.dll
0x00007ff821e10000 - 0x00007ff822011000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff80fef0000 - 0x00007ff80ff24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff824810000 - 0x00007ff824892000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff818d20000 - 0x00007ff818d45000 	E:\JAVA\bin\java.dll
0x00007fffef390000 - 0x00007fffef467000 	E:\JAVA\bin\jsvml.dll
0x00007ff826570000 - 0x00007ff826cde000 	C:\Windows\System32\SHELL32.dll
0x00007ff8222d0000 - 0x00007ff822a74000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff825fd0000 - 0x00007ff826323000 	C:\Windows\System32\combase.dll
0x00007ff8240f0000 - 0x00007ff82411b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff826d40000 - 0x00007ff826e0d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff825f20000 - 0x00007ff825fcd000 	C:\Windows\System32\SHCORE.dll
0x00007ff825d40000 - 0x00007ff825d9b000 	C:\Windows\System32\shlwapi.dll
0x00007ff8245f0000 - 0x00007ff824614000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff811d70000 - 0x00007ff811d89000 	E:\JAVA\bin\net.dll
0x00007ff81ede0000 - 0x00007ff81eeea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff823e50000 - 0x00007ff823eba000 	C:\Windows\system32\mswsock.dll
0x00007ff810fb0000 - 0x00007ff810fc6000 	E:\JAVA\bin\nio.dll
0x00007ff81bc20000 - 0x00007ff81bc38000 	E:\JAVA\bin\zip.dll
0x00007ff81c4d0000 - 0x00007ff81c4e0000 	E:\JAVA\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\JAVA\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;E:\JAVA\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\Encrypt\build\20250901_2202546886586593792.compiler.options
java_class_path (initial): E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.22\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\kotlin-compiler-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.22\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\kotlin-script-runtime-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.22\20e2c5df715f3240c765cfc222530e2796542021\kotlin-daemon-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8514437120                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8514437120                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\JAVA
CLASSPATH=.;E:\JAVA\lib\dt.jar;E:\JAVA\lib\tools.jar;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\dotnet\;E:\git\Git\cmd;D:\Users\W9096060\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\dotnet\;E:\1.8java\lib\dt.jar;E:\1.8java\lib\tools.jar;E:\1.8java\bin;E:\JAVA\bin;E:\JAVA\lib;D:\Users\W9096060\AppData\Local\Microsoft\WindowsApps;
USERNAME=W9096060
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 19:56 hours

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 32479M (2631M free)
TotalPageFile size 47840M (AvailPageFile size 282M)
current process WorkingSet (physical memory assigned to process): 157M, peak: 177M
current process commit charge ("private bytes"): 215M, peak: 678M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211) for windows-amd64 JRE (17.0.8+9-LTS-211), built on Jun 14 2023 10:34:31 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
