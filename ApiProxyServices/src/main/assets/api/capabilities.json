{"invokeCaps": [{"id": 1000208, "key": "com.coloros.filemanager.search_local_document", "name": "文档搜索", "version": "1.0.0", "versionCode": "1000000", "tags": ["文件管理", "文档搜索"], "description": "文档搜索设置项允许用户通过关键词快速定位手机中存储的文档。在系统功能中，它通过内置索引机制，支持用户搜索TXT、PDF、Word等格式的文档。用户输入关键词后，系统将展示匹配的文档列表，便于用户直接访问或管理所需文件。这一功能特别适合处理大量文档，提高了查找和使用文档的效率。", "caller": [{"type": "BREENO_LLM", "attributes": {"setting_name": "智慧文档", "setting_type": "文档搜索", "parent_setting_name": "文件管理", "sdk_name": "com.coloros.filemanager", "api_name": "search_local_document", "api_cn_name": "文档搜索", "api_description": "文档搜索设置项允许用户通过关键词快速定位手机中存储的文档。在系统功能中，它通过内置索引机制，支持用户搜索TXT、PDF、Word等格式的文档。用户输入关键词后，系统将展示匹配的文档列表，便于用户直接访问或管理所需文件。这一功能特别适合处理大量文档，提高了查找和使用文档的效率。", "parameter": [{"parameter_name": "search_file_keyword", "parameter_type": "str", "parameter_description": "需要搜索的关键字", "is_required": "false"}, {"parameter_name": "search_file_type", "parameter_type": "int", "parameter_description": "需要搜索的文档类型", "is_required": "false"}], "usage_example": [{"query": "怎么找到我上课笔记的Word文档？", "api": "search_local_document(search_file_keyword=上课笔记,search_file_type=Word)"}, {"query": "我想搜索手机中关于旅游攻略的PDF文件", "api": "search_local_document(search_file_keyword=旅游攻略,search_file_type=PDF)"}, {"query": "我昨天保存的TXT文件找不到了，能帮忙搜索吗？", "api": "search_local_document(search_file_type=TXT)"}, {"query": "在文档中找下英语课的PPT", "api": "search_local_document(search_file_keyword=英语课,search_file_type=PPT)"}, {"query": "找一下保存的AIGC相关文档", "api": "search_local_document(search_file_keyword=AIGC)"}, {"query": "找下上次存的语文课课件", "api": "search_local_document(search_file_keyword=语文课课件)"}, {"query": "搜索我的会议纪要文档", "api": "search_local_document(search_file_keyword=会议纪要)"}, {"query": "找一下保存的线性代数PPT", "api": "search_local_document(search_file_keyword=线性代数,search_file_type=PPT)"}, {"query": "搜一下我的24年业务规划", "api": "search_local_document(search_file_keyword=24年业务规划)"}], "usage_description": "用户在需要快速定位手机中存储的特定文档时会使用文档搜索功能。应用场景包括但不限于寻找遗失的会议记录、搜索历史邮件、查找特定格式的文件等。文档搜索功能为用户提供的价值在于极大提高了查找文档的效率，用户不再需要手动浏览文件夹或记住文件的具体位置，只需输入关键词，系统即可快速检索并展示相关文档，节省了用户的时间，提升了工作效率。此外，对于存储大量文件的用户来说，文档搜索还有助于快速整理和管理个人或工作相关的文档资料。", "composition_instructions": ""}}], "invoke": {"protocol": {"type": "ApiProxy", "apiType": "ANDROID_CONTENT_PROVIDER", "attributes": {"authority": "com.coloros.filemanager.search_doc", "type": "QUERY", "permission": "com.oplus.permission.safe.SECURITY", "queryArgs": [{"key": "key_category", "type": "string", "value": "doc"}]}}, "inputParams": [], "outputParams": []}}]}