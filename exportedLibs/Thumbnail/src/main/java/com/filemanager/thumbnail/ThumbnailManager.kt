/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.filemanager.thumbnail
 ** Version: 1.0
 ** Date: 2021/6/15
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail

import android.app.Application
import android.content.Context
import android.graphics.Color
import android.net.Uri
import android.os.Build
import androidx.annotation.ColorInt
import androidx.annotation.Keep
import com.filemanager.thumbnail.ThumbnailConstant.DEFAULT_WPS_DOC_THUMBNAIL_PRIORITY
import com.filemanager.thumbnail.ThumbnailConstant.DEFAULT_YO_ZO_DOC_DIGEST_ALGORITHM
import com.filemanager.thumbnail.ThumbnailConstant.DEFAULT_YO_ZO_DOC_DIGEST_MAX_SIZE
import com.filemanager.thumbnail.ThumbnailConstant.DEFAULT_YO_ZO_DOC_RETRY_COUNT
import com.filemanager.thumbnail.ThumbnailConstant.DEFAULT_YO_ZO_DOC_THUMBNAIL_PRIORITY
import com.filemanager.thumbnail.ThumbnailConstant.DEFAULT_YO_ZO_MIN_IDLE_SERVICE
import com.filemanager.thumbnail.ThumbnailConstant.DEFAULT_YO_ZO_PACKAGE
import com.filemanager.thumbnail.ThumbnailConstant.DEFAULT_YO_ZO_REBIND_DELAY
import com.filemanager.thumbnail.ThumbnailConstant.DEFAULT_YO_ZO_REBIND_MAX_COUNT
import com.filemanager.thumbnail.ThumbnailConstant.DEFAULT_YO_ZO_SERVICE_TIMEOUT
import com.filemanager.thumbnail.doc.DocThumbnailLoaderFactory
import com.filemanager.thumbnail.doc.DocThumbnailLoaderLifecycle
import com.filemanager.thumbnail.utils.Log
import java.util.concurrent.CompletableFuture
import java.util.concurrent.TimeUnit
import java.util.function.Function
import kotlin.math.max

@Keep
object ThumbnailManager {
    private const val TAG = "ThumbnailManager"
    private const val THUMBNAIL_SUPPORTED_TIMEOUT = 2L

    @JvmStatic
    var baseLogTag: String
        get() = Log.baseLogTag
        set(value) = Log::baseLogTag.set(value)

    @JvmStatic
    internal var application: Application? = null
        private set

    private var pathToUriCallback: IPathToUriCallback? = null
    private var isDocThumbnailSupportedTimeOut = false

    @JvmStatic
    fun init(application: Application, logEnable: Boolean, pathToUriCallback: IPathToUriCallback) {
        this.application = application
        Log.enableLog = logEnable
        this.pathToUriCallback = pathToUriCallback
        DocThumbnailLoaderLifecycle.init(application)
    }

    @JvmStatic
    internal fun getFileProviderUri(path: String?): Uri? {
        return pathToUriCallback?.getFileProviderUri(path)
    }

    /**
     * 判定是否支持文档缩略图加载，永中方案或WPS方案至少支持一种则为true
     */
    @JvmStatic
    fun isDocThumbnailSupported(context: Context): Boolean {
        if (isDocThumbnailSupportedTimeOut) {
            return false
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            var result = false
            CompletableFuture.runAsync(Runnable {
                result = DocThumbnailLoaderFactory.getInstance(context) != null
            }).orTimeout(THUMBNAIL_SUPPORTED_TIMEOUT, TimeUnit.SECONDS)
                .exceptionally(Function { ex: Throwable? ->
                    Log.d(TAG, "isDocThumbnailSupported 超时或异常: ${ex?.message}")
                    isDocThumbnailSupportedTimeOut = true
                    null
                }).join()
            Log.d(TAG, "isDocThumbnailSupported: $result")
            return result
        } else {
            return DocThumbnailLoaderFactory.getInstance(context) != null
        }
    }

    /**
     * WPS文档缩略图方案的相关设置项
     */
    @Keep
    object WpsDocConfigs {

        /**
         * 是否启用WPS文档缩略图方案
         */
        @JvmStatic
        var enableThumbnail: Boolean = true

        /**
         * WPS缩略图方案的优先级，同时支持多个方案时优先级更高的方案将会被启用
         */
        @JvmStatic
        var usagePriority: Int = DEFAULT_WPS_DOC_THUMBNAIL_PRIORITY
    }

    /**
     * 永中文档缩略图方案的相关设置项
     */
    @Keep
    object YoZoDocConfigs {

        @JvmStatic
        internal val occupyThumbServices: Set<Int>
            get() = thumbServices
        private val thumbServices = mutableSetOf<Int>()

        /**
         * 是否启用永中缩略图方案
         */
        @JvmStatic
        var enableThumbnail: Boolean = true

        /**
         * 永中缩略图方案的优先级，同时支持多个方案时优先级更高的方案将会被启用
         */
        @JvmStatic
        var usagePriority: Int = DEFAULT_YO_ZO_DOC_THUMBNAIL_PRIORITY

        /**
         * 永中缩略图加载服务的超时限制，默认2秒
         */
        @JvmStatic
        var thumbnailTimeout: Long = DEFAULT_YO_ZO_SERVICE_TIMEOUT
            set(value) {
                field = max(0, value)
            }

        /**
         * 永中缩略图服务队列里至少需要的空闲服务数量，默认1个。
         * 保持一定的空闲服务数量能缓解批量加载时永中缩略图服务连续unbind再bind时的异常断开连接的现象。
         */
        @JvmStatic
        var minIdleThumbnailService: Int = DEFAULT_YO_ZO_MIN_IDLE_SERVICE
            set(value) {
                field = max(0, value)
            }

        /**
         * 永中缩略图服务异常断开连接后重连的延迟时间
         */
        @JvmStatic
        var rebindDelay: Long = DEFAULT_YO_ZO_REBIND_DELAY
            set(value) {
                field = max(0, value)
            }

        /**
         * 永中缩略图服务异常断开连接后重连的最大尝试次数
         */
        @JvmStatic
        var rebindMaxCount: Int = DEFAULT_YO_ZO_REBIND_MAX_COUNT
            set(value) {
                field = max(0, value)
            }

        /**
         * 启用根据文档文件内容的摘要值来匹配缩略图缓存，内容的摘要值一致的文件将不再重复调用缩略图服务
         */
        @JvmStatic
        var enableDocDigestCache: Boolean = true

        /**
         * 计算文档文件内容的摘要值的算法，默认SHA-1
         * @see java.security.MessageDigest.getInstance
         */
        @JvmStatic
        var docDigestAlgorithm: String = DEFAULT_YO_ZO_DOC_DIGEST_ALGORITHM

        /**
         * 进行文档摘要值索引缩略图的最大文档大小，默认100MB
         */
        @JvmStatic
        var maxDigestDocSize: Long = DEFAULT_YO_ZO_DOC_DIGEST_MAX_SIZE
            set(value) {
                field = max(0, value)
            }

        /**
         * 单个缩略图请求最大的超时重试次数
         */
        @JvmStatic
        var maxRetryCount: Int = DEFAULT_YO_ZO_DOC_RETRY_COUNT
            set(value) {
                field = max(0, value)
            }

        @JvmStatic
        internal var yoZoPackage: String = DEFAULT_YO_ZO_PACKAGE
            private set

        /**
         * 设定永中缩略图服务对应的APP包名，默认内销版本自研文档包名(andes.oplus.documentsreader)
         * 若外销版自研文档按品牌区分了包名，需要按所在渠道单独设置包名。
         */
        @JvmStatic
        fun configureYoZoPackage(packageName: String) {
            if (packageName.isEmpty()) {
                return
            }
            yoZoPackage = packageName
        }

        /**
         * 设定当前应用独占的永中缩略图服务列表。
         * 文管和文档各自占用的永中缩略图服务最好不要重叠，不然无法确保同一服务同一时间只有一方使用。
         * 默认的服务列表如下：
         * 文管：{
         *     [ThumbnailConstant.YO_ZO_THUMB_SERVICE_4]
         *     [ThumbnailConstant.YO_ZO_THUMB_SERVICE_5]
         *     [ThumbnailConstant.YO_ZO_THUMB_SERVICE_6]
         * }
         * 文档：{
         *     [ThumbnailConstant.YO_ZO_THUMB_SERVICE_1]
         *     [ThumbnailConstant.YO_ZO_THUMB_SERVICE_2]
         *     [ThumbnailConstant.YO_ZO_THUMB_SERVICE_3]
         * }
         */
        @JvmStatic
        fun requireOccupyThumbServices(@YoZoThumbServices vararg services: Int) {
            thumbServices.clear()
            thumbServices.addAll(services.toSet())
        }
    }

    @Keep
    object DocThumbnailConfigs {

        /**
         * 缩略图本身无法填充指定区域时，是否启用填充绘制补全剩余区域
         */
        @JvmStatic
        var fillToFullRect: Boolean = true

        /**
         * 缩略图本身无法填充指定区域时，剩余区域的默认填充色
         */
        @ColorInt
        @JvmStatic
        var fillEmptyColor: Int = Color.WHITE
    }
}

@Keep
interface IPathToUriCallback {
    /**
     * get file provider from the file's path
     */
    fun getFileProviderUri(path: String?): Uri?
}