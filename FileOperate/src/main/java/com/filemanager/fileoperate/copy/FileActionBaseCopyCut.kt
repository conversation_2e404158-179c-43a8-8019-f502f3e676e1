/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileActionCopy.kt
 ** Description: Copy files function
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.copy

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.cpu.PerformanceManager
import com.filemanager.common.fileutils.fetchFileName
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.registerExportedReceiver
import com.filemanager.fileoperate.R
import com.filemanager.fileoperate.base.ACTION_CANCELLED
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.base.SHOW_PROGRESS
import com.filemanager.fileoperate.cut.FileActionCut
import com.oplus.filemanager.dfm.DFMManager
import java.io.File

abstract class FileActionBaseCopyCut : BaseFileAction<FileCopyObserver> {
    protected open var TAG = "FileActionBaseCopyCut"

    companion object {
        private const val DELAY_INTERNAL = 200L
        private const val REMEMBER_CHOOSE_WHEN_FAILED = 1 shl 0
        private const val REMEMBER_CHOOSE_WHEN_EXIST = 1 shl 1
    }

    private val mLock = Object()
    protected val mOperateFiles: ArrayList<BaseFileBean> = arrayListOf()
    var mDestFile: BaseFileBean
    var isOtgOrSDCard: Boolean = false
    var hasShowKeepConnectToast: Boolean = false
    private var mFileReplaceBean: FileCopyObserver.FileReplaceBean? = null
    private var mFileDealFailBean: FileCopyObserver.FileReplaceBean? = null

    // Choose info
    private var mCurrentReplaceSelect: Int = FileReplaceDialog.CHOOSE_NONE
    private var mCurrentFailedSelect: Int = FileReplaceDialog.CHOOSE_NONE
    private var mChooseRememberInfo = 0
    private var mMediaMountReceiver: BroadcastReceiver? = null
    private var mDfsMountReceiver: BroadcastReceiver? = null
    private var cancelReason = -1

    constructor(lifecycle: LifecycleOwner, sourceFiles: List<out BaseFileBean>, destFile: BaseFileBean) : super(lifecycle) {
        mOperateFiles.addAll(sourceFiles)
        mDestFile = destFile
    }

    override fun beforeRun() {
        FileCopyHelper.init()
        if (VolumeEnvironment.isOTGPath(mContext, mDestFile.mData)
                || VolumeEnvironment.isSdcardPath(mContext, mDestFile.mData)) {
            registerMediaMountReceiver()
        }
        if (KtUtils.checkIsDfmPath(mDestFile.mData)) {
            registerDfsMountReceiver()
        }
    }

    private fun registerMediaMountReceiver() {
        Log.d(TAG, "registerMediaMountReceiver")
        mMediaMountReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                Log.d(TAG, "onReceive action = ${intent.action}, value=${intent.dataString}, dest=${mDestFile.mData}")
                when (intent.action) {
                    Intent.ACTION_MEDIA_REMOVED, Intent.ACTION_MEDIA_BAD_REMOVAL,
                    Intent.ACTION_MEDIA_UNMOUNTED, Intent.ACTION_MEDIA_EJECT -> {
                        if (mDestFile.mData.isNullOrEmpty() || intent.dataString.isNullOrEmpty()) {
                            return
                        }
                        if (mDestFile.mData!!.startsWith(intent.dataString!!.replace("file://", ""), true)) {
                            Log.d(TAG, "onReceive, the sdcard unmounted, interrupt operate")
                            // For cut operation, invoke this method will do noting
                            unregisterMediaMountReceiver()
                            FileCopyHelper.forceInterrupt()
                        }
                    }
                }
            }
        }
        try {
            MyApplication.sAppContext.registerExportedReceiver(mMediaMountReceiver!!, IntentFilter(Intent.ACTION_MEDIA_REMOVED).apply {
                addAction(Intent.ACTION_MEDIA_BAD_REMOVAL)
                addAction(Intent.ACTION_MEDIA_UNMOUNTED)
                addAction(Intent.ACTION_MEDIA_EJECT)
                addDataScheme("file")
            })
        } catch (e: Exception) {
            Log.w(TAG, "registerMediaMountReceiver failed: ${e.message}")
        }
    }

    private fun unregisterMediaMountReceiver() {
        mMediaMountReceiver?.apply {
            try {
                MyApplication.sAppContext.unregisterReceiver(this)
            } catch (e: Exception) {
            }
        }
        mMediaMountReceiver = null
    }

    private fun registerDfsMountReceiver() {
        Log.d(TAG, "registerDfsMountReceiver")
        mDfsMountReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                Log.d(TAG, "onReceive action = ${intent.action}, dest=${mDestFile.mData}")
                if (intent.action == KtConstants.ACTION_DFS_DISCONNECT) {
                    unregisterDfsMountReceiver()
                    FileCopyHelper.forceInterrupt()
                }
            }
        }
        try {
            MyApplication.sAppContext.registerExportedReceiver(mDfsMountReceiver!!, IntentFilter(KtConstants.ACTION_DFS_DISCONNECT))
        } catch (e: Exception) {
            Log.w(TAG, "registerMediaMountReceiver failed: ${e.message}")
        }
    }

    private fun unregisterDfsMountReceiver() {
        mDfsMountReceiver?.apply {
            try {
                MyApplication.sAppContext.unregisterReceiver(this)
            } catch (e: java.lang.Exception) {
                Log.e(TAG, " $e")
            }
        }
        mDfsMountReceiver = null
    }

    private fun initFileReplaceBean() {
        mFileReplaceBean = FileCopyObserver.FileReplaceBean(true, null)
        mFileReplaceBean!!.chooseListener = object : FileReplaceDialog.OnChooseListener {
            override fun onChoose(chooseId: Int, rememberChoose: Boolean) {
                mCurrentReplaceSelect = chooseId
                if (rememberChoose) {
                    saveRememberChoose(REMEMBER_CHOOSE_WHEN_EXIST)
                }
                releaseLock()
            }
        }
    }

    private fun initFileDealFailBean() {
        mFileDealFailBean = FileCopyObserver.FileReplaceBean(false, null)
        mFileDealFailBean!!.chooseListener = object : FileReplaceDialog.OnChooseListener {
            override fun onChoose(chooseId: Int, rememberChoose: Boolean) {
                mCurrentFailedSelect = chooseId
                if (rememberChoose) {
                    saveRememberChoose(REMEMBER_CHOOSE_WHEN_FAILED)
                }
                releaseLock()
            }
        }
    }

    override fun run(): Boolean {
        notifyObserver(
            SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                mContext.getString(com.filemanager.common.R.string.string_being_calculated),
                true), DELAY_INTERNAL)
        if (KtUtils.checkIsDfmPath(mDestFile.mData)) {
            DFMManager.openP2pConnectAndWaitDFSReady()
        }
        val detectResult = exceptionDetection()
        if (detectResult != null) {
            cancelNotifyObserver(SHOW_PROGRESS)
            notifyObserver(ACTION_FAILED, detectResult)
            return false
        }
        cancelNotifyObserver(SHOW_PROGRESS)
        initImproveCpu()
        val result = workRun()
        releaseImproveCpu()
        return result
    }

    protected open fun exceptionDetection(): Any? {
        // First check data is ok
        if (mOperateFiles.isNullOrEmpty() || !mDestFile.mIsDirectory || mDestFile.mData.isNullOrEmpty()) {
            Log.d(TAG, "exceptionDetection: mOperateFiles is null or mDestFile is wrong[dir=${mDestFile.mIsDirectory}]")
            return ERROR_PATH_NULL
        }
        FileCopyHelper.checkInSameFolder(mOperateFiles, mDestFile).let {
            if (it.first) {
                Log.d(TAG, "exceptionDetection: checkInSameFolder result=${it.second}")
                return Pair(ERROR_IN_SAME_DIR, it.second)
            }
        }
        MediaStoreCompat.getCShotFiles(mOperateFiles)
        return null
    }

    protected open fun workRun(): Boolean {
        Log.d(TAG, "copyRun: mOperateFiles.size = ${mOperateFiles.size}")
        onDealFileResume()

        val destDir = File(mDestFile.mData)
        var destFile: File
        var sourceFile: File
        isOtgOrSDCard = checkOtgOrSDCard(mDestFile.mData)
        hasShowKeepConnectToast = false
        for (f in mOperateFiles) {
            if (isCancelled()) {
                Log.d(TAG, "workRun: isCancelled = true")
                break
            }
            if (f.mDisplayName.isNullOrEmpty() || f.mData.isNullOrEmpty()) {
                Log.d(TAG, "workRun: file name or path is empty: ${f.mDisplayName}")
                continue
            }

            sourceFile = File(f.mData)
            destFile = File(destDir, f.mDisplayName)
            if (destFile.exists()) {
                if (isRememberChoose(REMEMBER_CHOOSE_WHEN_EXIST).not()) {
                    Log.d(TAG, "workRun: ask user choose when exit")
                    mCurrentReplaceSelect = FileReplaceDialog.CHOOSE_NONE
                    showReplaceOrFailDialog(sourceFile.name, true)
                    waitLock()

                    // Show progress dialog again
                    Log.d(TAG, "workRun: user choose [$mCurrentReplaceSelect] when exit")
                    when (mCurrentReplaceSelect) {
                        // Show progress dialog again
                        FileReplaceDialog.CHOOSE_REPLACE,
                        FileReplaceDialog.CHOOSE_KEEP_BOTH,
                        FileReplaceDialog.CHOOSE_SKIP -> onDealFileResume()
                    }
                } else {
                    Log.d(TAG, "workRun: user has been choose [$mCurrentReplaceSelect] when exist")
                }
                when (mCurrentReplaceSelect) {
                    FileReplaceDialog.CHOOSE_REPLACE -> {
                        onDealFile(sourceFile, destFile, f.mIsDirectory)
                    }
                    FileReplaceDialog.CHOOSE_KEEP_BOTH -> {
                        val newFileBean = fetchFileName(mDestFile, sourceFile.nameWithoutExtension,
                                sourceFile.extension.let {
                                    if (!it.isNullOrEmpty()) {
                                        ".".plus(it)
                                    } else it
                                })
                        Log.d(TAG, "dealFileWhenExist: keep both, new file=${newFileBean?.mData}")
                        if ((newFileBean != null) && !newFileBean.mData.isNullOrEmpty()) {
                            onDealFile(sourceFile, File(newFileBean.mData!!), f.mIsDirectory)
                        } else {
                            // Should update the progress
                        }
                    }
                    FileReplaceDialog.CHOOSE_SKIP -> {
                        onSkipDealFile(sourceFile)
                    }
                    else -> {
                        cancel()
                        return false
                    }
                }
            } else {
                onDealFile(sourceFile, destFile, f.mIsDirectory)
            }
        }

        onDealAllFilesEnd()
        return true
    }

    @VisibleForTesting
    fun checkOtgOrSDCard(dir: String?): Boolean {
        dir?.let {
            val isOTGPath = VolumeEnvironment.isOTGPath(MyApplication.sAppContext, it)
            val isSdcardPath = VolumeEnvironment.isSdcardPath(MyApplication.sAppContext, it)
            Log.d(TAG, "checkOtgOrSDCard isOTGPath$isOTGPath, isSdcardPath$isSdcardPath")
            return isOTGPath || isSdcardPath
        }
        return false
    }

    protected abstract fun onSkipDealFile(sourceFile: File)

    /**
     * 复制&移动文件成功回调
     * @param sourceFile 来源文件
     * @param destFile 目标文件
     * @param isDirectory 是否为文件夹
     */
    protected abstract fun onDealFile(sourceFile: File, destFile: File, isDirectory: Boolean)

    protected open fun onDealFileError(sourceFile: File, destFile: File): Boolean {
        Log.w(TAG, "onDealFileError Failed to deal file: ${sourceFile.absolutePath} destFile ${destFile.absolutePath}")
        FileCopyHelper.checkIsBadSdDialog(destFile.absolutePath).let {
            if (it.first) {
                notifyObserver(ACTION_FAILED, ERROR_BAD_SDCARD)
                if (it.second) {
                    notifyObserver(ACTION_FAILED, SHOW_BAD_SDCARD_NOTICE_MSG)
                }
                cancel()
                return false
            }
        }

        val isDfsDisconnect = checkDfsDisconnection(sourceFile, destFile)
        if (isDfsDisconnect) {
            Log.d(TAG, "onDealFileError: is dfs device fail")
            cancelReason = if (this is FileActionCut) {
                ERROR_CUT_DFM_DISCONNECT
            } else {
                ERROR_COPY_DFM_DISCONNECT
            }
            cancel()
            return false
        }

        if (isRememberChoose(REMEMBER_CHOOSE_WHEN_FAILED).not()) {
            Log.d(TAG, "onDealFileError: ask user choose when fail")
            mCurrentFailedSelect = FileReplaceDialog.CHOOSE_NONE
            showReplaceOrFailDialog(sourceFile.name, false)
            waitLock()

            // Show progress dialog again
            Log.d(TAG, "onDealFileError: user choose [$mCurrentFailedSelect] when fail")
            if (mCurrentFailedSelect == FileReplaceDialog.CHOOSE_SKIP) {
                onDealFileResume()
            }
        } else {
            Log.d(TAG, "onDealFileError: user has been choose [$mCurrentFailedSelect] when fail")
        }
        return when (mCurrentFailedSelect) {
            FileReplaceDialog.CHOOSE_SKIP -> {
                onSkipDealFile(sourceFile)
                true
            }
            else -> {
                cancel()
                false
            }
        }
    }

    private fun checkDfsDisconnection(sourceFile: File, destFile: File): Boolean {
        val isSourceDfmPath = KtUtils.checkIsDfmPath(sourceFile.absolutePath)
        val isDestDfmPath = KtUtils.checkIsDfmPath(destFile.absolutePath)
        var isDfsDisconnect = false
        if (isSourceDfmPath || isDestDfmPath) {
            val dfmRootPath = if (isSourceDfmPath) {
                KtUtils.getDfmRootPath(sourceFile.absolutePath)
            } else {
                KtUtils.getDfmRootPath(destFile.absolutePath)
            }
            val rootFile = File(dfmRootPath)
            if (!rootFile.exists()) {
                isDfsDisconnect = true
            }
        }
        return isDfsDisconnect
    }

    /**
     * Resume the deal file progress
     */
    protected abstract fun onDealFileResume()

    /**
     * deal file end
     */
    protected abstract fun onDealAllFilesEnd()

    private fun showReplaceOrFailDialog(fileName: String?, replace: Boolean) {
        Log.d(TAG, "showReplaceOrFailDialog: replace=$replace, file=$fileName")
        if (replace) {
            if (mFileReplaceBean == null) {
                initFileReplaceBean()
            }
            mFileReplaceBean?.mFileName = fileName
            notifyObserver(SHOW_REPLACE_OR_FAIL_DIALOG, mFileReplaceBean)
        } else {
            if (mFileDealFailBean == null) {
                initFileDealFailBean()
            }
            mFileDealFailBean?.mFileName = fileName
            notifyObserver(SHOW_REPLACE_OR_FAIL_DIALOG, mFileDealFailBean)
        }
    }

    protected fun waitLock() {
        if (isCancelled().not()) {
            try {
                synchronized(mLock) {
                    mLock.wait()
                }
            } catch (e: Exception) {
                Log.e(TAG, "waitLock exception: ${e.message}")
            }
        }
    }

    protected fun releaseLock() {
        try {
            synchronized(mLock) {
                mLock.notify()
            }
        } catch (e: Exception) {
            Log.e(TAG, "releaseLock exception: ${e.message}")
        }
    }

    private fun initImproveCpu() {
        PerformanceManager.setSceneAction(PerformanceManager.COPY_CUT_ACTION)
    }

    private fun releaseImproveCpu() {
        PerformanceManager.release()
    }

    private fun saveRememberChoose(choose: Int) {
        mChooseRememberInfo = mChooseRememberInfo or choose
    }

    private fun isRememberChoose(choose: Int): Boolean {
        return (mChooseRememberInfo and choose) > 0
    }

    override fun onCancelled() {
        releaseLock()
        onDealAllFilesEnd()
        if (cancelReason != -1) {
            notifyObserver(ACTION_CANCELLED, cancelReason)
        } else {
            super.onCancelled()
        }
    }

    override fun recycle() {
        unregisterMediaMountReceiver()
        unregisterDfsMountReceiver()
        mFileReplaceBean?.recycle()
        mFileDealFailBean?.recycle()
    }
}