package com.oplus.filemanager.drivebrowser.ui

import android.content.Context
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.drivebrowser.R
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config

/**
 * AuthorizationController的单元测试类
 * 用于测试AuthorizationController中各种对话框显示和品牌字符串获取功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class AuthorizationControllerTest {

    private lateinit var controller: AuthorizationController
    private lateinit var context: Context
    
    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 创建AuthorizationController实例
     * 3. 获取运行时上下文环境
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        controller = AuthorizationController()
        context = RuntimeEnvironment.application
    }
    
    /**
     * 测试后的清理方法
     * 1. 释放controller资源
     * 2. 清除所有mock对象
     */
    @After
    fun tearDown() {
        controller.release()
        unmockkAll()
        clearAllMocks()
    }
    
    /**
     * 测试getBrandString方法在Realme手机上的返回结果
     * 1. 模拟Utils.isRealmePhone()返回true
     * 2. 验证对话框标题设置是否正确
     */
    @Test
    fun `getBrandString should return realme when isRealmePhone is true`() {
        // Given
        mockkStatic(Utils::class)
        every { Utils.isRealmePhone() } returns true
        
        // When & Then
        val mockDialog = mockk<AlertDialog>()
        justRun { mockDialog.show() }
        mockkConstructor(COUIAlertDialogBuilder::class)
        every { anyConstructed<COUIAlertDialogBuilder>().setTitle(any<String>()) } returns mockk(relaxed = true)
        every { anyConstructed<COUIAlertDialogBuilder>().setPositiveButton(any<Int>(), any()) } returns mockk(relaxed = true)
        every { anyConstructed<COUIAlertDialogBuilder>().create() } returns mockDialog
        
        controller.showWeChatUninstalledDialog(context)
        
        verify { anyConstructed<COUIAlertDialogBuilder>().setTitle(any<String>()) }
    }
    
    /**
     * 测试getBrandString方法在OnePlus手机上的返回结果
     * 1. 模拟Utils.isRealmePhone()返回false且KtAppUtils.isOnePlus返回true
     * 2. 验证对话框标题设置是否正确
     */
    @Test
    fun `getBrandString should return oneplus when isOnePlus is true`() {
        // Given
        mockkStatic(Utils::class)
        mockkObject(KtAppUtils)
        every { Utils.isRealmePhone() } returns false
        every { KtAppUtils.isOnePlus } returns true
        
        // When & Then
        val mockDialog = mockk<AlertDialog>()
        justRun { mockDialog.show() }
        mockkConstructor(COUIAlertDialogBuilder::class)
        every { anyConstructed<COUIAlertDialogBuilder>().setTitle(any<String>()) } returns mockk(relaxed = true)
        every { anyConstructed<COUIAlertDialogBuilder>().setPositiveButton(any<Int>(), any()) } returns mockk(relaxed = true)
        every { anyConstructed<COUIAlertDialogBuilder>().create() } returns mockDialog
        
        controller.showWeChatUninstalledDialog(context)
        
        verify { anyConstructed<COUIAlertDialogBuilder>().setTitle(any<String>()) }
    }
    
    /**
     * 测试getBrandString方法在非Realme非OnePlus手机上的返回结果
     * 1. 模拟Utils.isRealmePhone()和KtAppUtils.isOnePlus都返回false
     * 2. 验证对话框标题设置是否正确
     */
    @Test
    fun `getBrandString should return oppo when neither realme nor oneplus`() {
        // Given
        mockkStatic(Utils::class)
        mockkObject(KtAppUtils)
        every { Utils.isRealmePhone() } returns false
        every { KtAppUtils.isOnePlus } returns false
        
        // When & Then
        val mockDialog = mockk<AlertDialog>()
        justRun { mockDialog.show() }
        mockkConstructor(COUIAlertDialogBuilder::class)
        every { anyConstructed<COUIAlertDialogBuilder>().setTitle(any<String>()) } returns mockk(relaxed = true)
        every { anyConstructed<COUIAlertDialogBuilder>().setPositiveButton(any<Int>(), any()) } returns mockk(relaxed = true)
        every { anyConstructed<COUIAlertDialogBuilder>().create() } returns mockDialog
        
        controller.showWeChatUninstalledDialog(context)
        
        verify { anyConstructed<COUIAlertDialogBuilder>().setTitle(any<String>()) }
    }
}