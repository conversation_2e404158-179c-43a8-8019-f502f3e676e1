/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ISelectionFilesHeapUp.kt
 * Description:
 *     The interface to heap up selections with their preview image.
 *
 * Version: 1.0
 * Date: 2024-10-22
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-10-22   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection

import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.fragment.app.Fragment
import com.oplus.filemanager.preview.widget.SelectionHeapUpView

internal interface ISelectionFilesHeapUp {

    /**
     * Attach to container views and handle heap up selections.
     */
    fun attachToContainer(container: ISelectionHeapUpContainer)

    /**
     * Update the padding bottom of heap up page if need to set extras padding.
     */
    fun updatePaddingBottom(systemBarInsetsBottom: Int)

    /**
     * To callback when end deselection animation
     */
    fun onDeselectionAnimationEnd(onEnd: () -> Unit)

    /**
     * Release when not use it anymore.
     */
    fun release()

    companion object {
        @JvmStatic
        fun obtain(
            fragment: Fragment,
            selectionViewModel: SelectionHeapUpViewModel
        ): ISelectionFilesHeapUp = SelectionFilesHeapUpImpl(fragment, selectionViewModel)
    }
}

/**
 * The interface to handle heap up view container
 */
internal interface ISelectionHeapUpContainer {
    val containerRoot: ViewGroup
    val heapUpView: SelectionHeapUpView
    val filesCountView: AppCompatTextView
    val filesStatView: AppCompatTextView
    val startLoadingTime: Long?

    /**
     * Show page as loading stat
     */
    fun showAsLoading(isLoading: Boolean)
}