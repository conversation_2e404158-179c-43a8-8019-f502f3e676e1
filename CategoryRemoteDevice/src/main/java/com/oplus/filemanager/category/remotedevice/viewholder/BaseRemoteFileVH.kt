/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : BaseRemoteFileVH
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.viewholder

import android.content.Context
import android.graphics.Rect
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.base.DynamicPressBaseSelectionViewHolder
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.VerticalImageSpan
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.utils.FileTypeUtils

abstract class BaseRemoteFileVH(convertView: View) : DynamicPressBaseSelectionViewHolder(convertView) {

    companion object {
        private const val TAG = "BaseRemoteFileVH"

        @JvmStatic
        fun isSpecialFolder(file: BaseFileBean): Boolean {
            if (file is RemoteFileBean) {
                return file.isRecentFolder()
            } else {
                return false
            }
        }

        @JvmStatic
        fun getSpecialFolderIcon(file: BaseFileBean, isGridMode: Boolean): Int {
            if (file is RemoteFileBean) {
                val isRecent = file.isRecentFolder()
                if (isRecent) {
                    if (isGridMode) {
                        return com.oplus.filemanager.category.remotedevice.R.drawable.recent_folder_icon_grid
                    } else {
                        return com.oplus.filemanager.category.remotedevice.R.drawable.recent_folder_icon_list
                    }
                }
            }
            return 0
        }
    }

    private var mBindData: BaseFileBean? = null
    private var mChoiceMode: Boolean? = false

    fun loadData(
        context: Context,
        key: Int?,
        file: BaseFileBean,
        choiceMode: Boolean,
        selectionArray: MutableList<Int>,
        adapter: BaseSelectionRecycleAdapter<*, *>
    ) {
        this.itemCount = adapter.getRealFileItemCount()
        updateKey(key)
        mBindData = file
        mChoiceMode = choiceMode
        updateViewHolder(
            context,
            key,
            file,
            choiceMode,
            selectionArray,
            adapter
        )
    }

    /**
     * 更新ViewHoder中的数据，子类复写
     */
    abstract fun updateViewHolder(
        context: Context,
        key: Int?,
        file: BaseFileBean,
        choiceMode: Boolean,
        selectionArray: MutableList<Int>,
        adapter: BaseSelectionRecycleAdapter<*, *>
    )

    protected fun showDetail(
        context: Context,
        file: BaseFileBean,
        detail: TextView,
        path: String,
        isLinearViewHolder: Boolean
    ) {
        val remoteBean = file as RemoteFileBean
        detail.text = ""
        val formatStorageDetail = if (file.mIsDirectory) {
            val fileCount = remoteBean.folderFileNum
            MyApplication.sAppContext.resources.getQuantityString(
                R.plurals.text_x_items,
                fileCount,
                fileCount
            )
        } else {
            KtUtils.formatSize(file)
        }
        val currentPath = detail.tag as? String
        val lastModified = file.mDateModified
        if (path == currentPath) {
            if (isLinearViewHolder) {
                val dateAndTime = Utils.getDateFormat(context, lastModified)
                detail.text = Utils.formatDetail(context, formatStorageDetail, dateAndTime)
            } else {
                if (!file.isDir()) {
                    // 这里在detail上文字前面显示电脑图标
                    val spanText = getDetailSpanString(context, formatStorageDetail)
                    detail.text = spanText
                } else {
                    detail.text = formatStorageDetail
                }
            }
        }
    }

    protected fun showApkIconIfNeed(
        file: BaseFileBean,
        apkIcon: ImageView,
        isGridMode: Boolean = false
    ) {
        /*Log.d(
            TAG,
            "showApkIconIfNeed apkIcon = $apkIcon file.mLocalType = ${file.mLocalType} file.originPackage = ${file.originPackage}"
        )*/
        val isSpecial = isSpecialFolder(file)
        if (file.isDir() && isSpecial) {
            apkIcon.visibility = View.VISIBLE
            apkIcon.setImageResource(getSpecialFolderIcon(file, isGridMode))
        } else {
            apkIcon.visibility = View.GONE
        }
    }


    private fun getDetailSpanString(
        context: Context,
        inputString: String,
    ): Spannable {
        val span = SpannableStringBuilder(inputString)
        val paddingPx =
            context.resources.getDimensionPixelSize(com.oplus.filemanager.category.remotedevice.R.dimen.grid_detail_icon_margin_start)
        val drawable =
            context.getDrawable(com.oplus.filemanager.category.remotedevice.R.drawable.remote_file_icon_detail)
        drawable?.let {
            //这里使用VerticalImageSpan替代IconWrapSpan，可以解决IconWrapSpan中图标始终在textView的靠左对齐，文字内容居中对齐，无法保持图标和文字固定距离的问题，
            val imgSpan = VerticalImageSpan(drawable, 0, paddingPx)
            //这里前面加一个空格是为了setSpan中start=0, end=1，start到end之间的字符串会替换为图片，所以这里用一个空格来占位
            span.insert(0, " ")
            span.apply {
                setSpan(imgSpan, 0, 1, Spanned.SPAN_EXCLUSIVE_INCLUSIVE)
            }
        }
        return span
    }

    /**
     * 这里在选择模式下滑动/点击事件，这里这里采用checkbox来反馈
     */
    override fun isInSelectRegionImpl(event: MotionEvent): Boolean {
        return if (mChoiceMode == true && mBindData?.isDir() == true) {
            Log.d(TAG, "isInSelectRegionImpl choice and dir, return false")
            false
        } else {
            val checkBoxRect = Rect()
            //这里使用checkbox的原因，主要在滑动选择时，滑动的onItemceptToucheEvent方法也会调用该方法是否进行move事件的拦截处理，如果使用itemView，滑动事件在整个itemView上就会进入判定进入滑动选择模式，而不是在checkbox区域选中进行滑动选择，初始事件的区域范围扩大
            mCheckBox?.getGlobalVisibleRect(checkBoxRect)
            val result = checkBoxRect.contains(event.rawX.toInt(), event.rawY.toInt())
            Log.d(TAG, "isInSelectRegionImpl X ${event.rawX}, Y ${event.rawY}, rect $checkBoxRect result $result")
            result
        }
    }

    /**
     * 这里控制处理在选择模式下的点击事件，up事件处理。点击时使用整个itemView来判定是否事件落入
     * 返回为true，checkbox选中，返回为false，点击事件则不做响应
     */
    override fun isInClickArea(event: MotionEvent): Boolean {
        return if (mChoiceMode == true && mBindData?.isDir() == true) {
            Log.d(TAG, "isInClickArea choice and dir, return false")
            false
        } else {
            val checkBoxRect = Rect()
            itemView.getGlobalVisibleRect(checkBoxRect)
            val result = checkBoxRect.contains(event.rawX.toInt(), event.rawY.toInt())
            Log.d(TAG, "isInClickArea X ${event.rawX}, Y ${event.rawY}, rect $checkBoxRect result $result")
            result
        }
    }


    /**
     * 重写这个方法，动态控制每个itemd额长按进入选择模式的事件
     */
    override fun canLongPressOrClick(): Boolean {
        if (mBindData?.isDir() == true) {
            return false
        } else {
            return true
        }
    }

    /**
     * 获取文件的后缀名
     */
    fun getExtForBean(file: BaseFileBean): String? {
        val remoteBean = file as RemoteFileBean
        val name = if (remoteBean.shouldShowAlternativeName()) remoteBean.alternativeName else remoteBean.mDisplayName
        val ext = FileTypeUtils.getExtension(name)
        return ext
    }
}