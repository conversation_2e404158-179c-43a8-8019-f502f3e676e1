/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - ScanOperationListener.java
 ** Description: Scan Operation Listener
 ** Version: 1.0
 ** Date : 2020/03/11
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/03/11    1.0     create
 ****************************************************************/

package com.filemanager.recyclebin.operation.listener

import android.app.Dialog
import android.content.DialogInterface
import android.os.Message
import android.view.Gravity
import androidx.activity.ComponentActivity
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.recyclebin.RecycleFileManager
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.view.AlertDialogFactory
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.getBottomAlertDialogWindowAnimStyle
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.Utils
import com.filemanager.recyclebin.utils.RecycleBinUtils
import com.filemanager.recyclebin.utils.RecycleBinUtils.innerDelete
import com.filemanager.recyclebin.utils.RecycleBinUtils.innerEraseRecycle
import com.filemanager.recyclebin.utils.RecycleBinUtils.innerRecycle
import com.filemanager.recyclebin.operation.BaseOperation.OperationParameters
import com.filemanager.recyclebin.operation.BaseOperation.OperationResult
import com.filemanager.recyclebin.operation.RecycleBinCancelListener
import com.filemanager.recyclebin.operation.RecycleBinOperationListener
import com.filemanager.recyclebin.operation.ScanParameters
import com.filemanager.recyclebin.utils.RecycleBinUtils.getDeleteDialogTitle
import com.filemanager.recyclebin.utils.RecycleBinUtils.makeDfmCountStr
import com.filemanager.recyclebin.utils.RecycleBinUtils.makeFileCountStr
import java.lang.ref.WeakReference

class ScanOperationListener(
    val activity: ComponentActivity,
    var mListener: RecycleBinOperationListener?,
    val recycleFileParameters: RecycleFileManager.RecycleFileParameters
) : OperationListener(activity) {

    companion object {
        private const val TAG = "ScanOperationListener"
        private const val MSG_SHOW_CALCULATE_DIALOG = 100
    }

    private var mAlertDialog: AlertDialog? = null
    private var mCalculateDialog: Dialog? = null
    var mRecycleBinCancelListener: RecycleBinCancelListener? = null

    override fun innerHandleMessage(message: Message, activity: ComponentActivity) {
        Log.d(TAG, "innerHandleMessage what : ${message.what}")
        when (message.what) {
            MSG_SHOW_CALCULATE_DIALOG -> {
                mWeakActivity.get()?.let {
                    mCalculateDialog = AlertDialogFactory.createCalculateDialog(it)
                }
            }
        }
    }

    override fun onProgressStart() {
        Log.d(TAG, "onProgressStart")
        mCalculateDialog?.let {
            if (it.isShowing) {
                it.dismiss()
            }
        }

        mAlertDialog?.let {
            if (it.isShowing) {
                it.dismiss()
            }
        }

        mMainHandler.sendEmptyMessageDelayed(MSG_SHOW_CALCULATE_DIALOG, MESSAGE_DELAY_TIME)
    }

    override fun onProgressUpdate(progress: Int?) {
    }

    override fun onProgressComplete(parameters: OperationParameters?, result: OperationResult) {
        Log.d(TAG, "onProgressComplete result: $result")
        mMainHandler.removeCallbacksAndMessages(null)
        mWeakActivity.get()?.let {
            // note: REACH_LIMIT means reach size limit, need to alter user
            if (it.isFinishing || it.isDestroyed) {
                Log.w(TAG, "onProgressComplete activity finished or destroy")
                return
            }
            mCalculateDialog?.let { dialog ->
                if (dialog.isShowing) {
                    dialog.dismiss()
                }
            }
            val totalSize: Long
            val hasExternal: Boolean
            if (parameters is ScanParameters) {
                hasExternal = parameters.mHasExternalFile
                totalSize = parameters.mTotalSize
            } else {
                hasExternal = false
                totalSize = 0
            }

            if (recycleFileParameters.mIsShowConfigDialog) {
                val isCategoryRecycleBin = recycleFileParameters.mCategory == CategoryHelper.CATEGORY_RECYCLE_BIN
                val isCategoryDfm = recycleFileParameters.mCategory == CategoryHelper.CATEGORY_DFM
                //这里构建相应的词条
                val fileCountStr = if (isCategoryDfm) {
                    makeDfmCountStr(result.mTotalCount, recycleFileParameters.mIsSelectedAll)
                } else {
                    makeFileCountStr(result.mTotalCount, isCategoryRecycleBin, hasExternal, recycleFileParameters.mIsSelectedAll)
                }
                val title = getDeleteDialogTitle(result.mTotalCount, recycleFileParameters.mIsSelectedAll)
                mAlertDialog = if (isCategoryRecycleBin || isCategoryDfm) {
                    createDeleteForeverConfirmDialog(
                        it,
                        parameters,
                        mListener,
                        title,
                        fileCountStr,
                        recycleFileParameters.mIsSelectedAll
                    )
                } else {
                    createDeleteConfirmDialog(
                        it,
                        parameters,
                        mListener,
                        title,
                        fileCountStr,
                        recycleFileParameters.mIsSelectedAll
                    )
                }
            } else {
                // not need dialog config, delete forever
                if (FeatureCompat.sIsLightVersion) {
                    // here only support filewrap as parameter now
                    Log.d(TAG, "isLightVersion = ${FeatureCompat.sIsLightVersion} file Delete Directly")
                    innerDelete(it, parameters, mListener, RecycleBinUtils.isMediaTypeCategory(recycleFileParameters.mCategory))
                } else {
                    // move to recycle bin
                    innerRecycle(it, parameters, mListener, RecycleBinUtils.isMediaTypeCategory(recycleFileParameters.mCategory),
                        recycleFileParameters.mIsSelectedAll,
                        recycleFileParameters.showProgress
                    )
                }
            }
        }
    }

    @VisibleForTesting
    fun createDeleteConfirmDialog(
        activity: ComponentActivity,
        parameters: OperationParameters?,
        listener: RecycleBinOperationListener?,
        title: String,
        message: String,
        isSelectedAll: Boolean
    ): AlertDialog {
        Log.d(TAG, "createDeleteConfirmDialog")
        val confirmListener =
            DeleteConfirmDialogListener(
                activity,
                parameters,
                listener,
                recycleFileParameters.mCategory,
                isSelectedAll,
                mRecycleBinCancelListener
            )
        return COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setWindowGravity(Gravity.BOTTOM)
            .setTitle(title)
            .setMessage(message)
            .setOnCancelListener(confirmListener)
            .setNeutralButton(RecycleBinUtils.getDelButtonStr(isSelectedAll), confirmListener)
            .setNegativeButton(com.filemanager.common.R.string.alert_dialog_no, confirmListener).show()
    }

    @VisibleForTesting
    fun createDeleteForeverConfirmDialog(
        activity: ComponentActivity,
        parameters: OperationParameters?,
        listener: RecycleBinOperationListener?,
        title: String,
        message: String,
        isSelectedAll: Boolean
    ): AlertDialog {
        Log.d(TAG, "createDeleteForeverConfirmDialog")
        val confirmListener =
            DeleteConfirmDialogListener(
                activity,
                parameters,
                listener,
                recycleFileParameters.mCategory,
                isSelectedAll,
                mRecycleBinCancelListener
            )
        return COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setWindowGravity(Gravity.BOTTOM)
            .setTitle(title)
            .setMessage(message)
            .setOnCancelListener(confirmListener)
            .setNeutralButton(RecycleBinUtils.getDelButtonStr(isSelectedAll), confirmListener)
            .setNegativeButton(com.filemanager.common.R.string.alert_dialog_no, confirmListener).show()
    }

    override fun recycle() {
        Log.d(TAG, "recycle")
        mListener = null
        try {
            mAlertDialog?.dismiss()
        } catch (e: Exception) {
        }
        try {
            mCalculateDialog?.dismiss()
        } catch (e: Exception) {
        }
        mAlertDialog = null
        mCalculateDialog = null
        super.recycle()
    }

    fun hideDialog() {
        Log.d(TAG, "hideDialog")
        if (mAlertDialog?.isShowing == false) {
            return
        }
        try {
            mAlertDialog?.dismiss()
        } catch (e: IllegalArgumentException) {
            Log.d(TAG, "hideDialog e = ${e.message}")
        }
    }

    fun isShowDialog(): Boolean {
        return mCalculateDialog?.isShowing ?: false || mAlertDialog?.isShowing ?: false
    }

    @Suppress("ParameterStyleBracesRule")
    private class DeleteConfirmDialogListener(
        activity: ComponentActivity,
        val mParameters: OperationParameters?,
        val mListener: RecycleBinOperationListener?,
        val mCategory: Int,
        val mIsSelectAll: Boolean = false,
        recycleBinCancelListener: RecycleBinCancelListener?
    ) : DialogInterface.OnClickListener, DialogInterface.OnCancelListener {
        private val mWeakActivity = WeakReference(activity)
        private val mWeakRecycleBinCancelListener = WeakReference(recycleBinCancelListener)

        override fun onClick(dialog: DialogInterface?, which: Int) {
            Log.w(TAG, "onClick which: $which")
            when (which) {
                DialogInterface.BUTTON_NEUTRAL -> {
                    mWeakActivity.get()?.let {
                        if (mCategory == CategoryHelper.CATEGORY_RECYCLE_BIN) {
                            innerEraseRecycle(it, mParameters, mListener)
                        } else {
                            val hasExternal = if (mParameters is ScanParameters) {
                                mParameters.mHasExternalFile
                            } else {
                                false
                            }
                            val isDfmCategory = mCategory == CategoryHelper.CATEGORY_DFM
                            if (FeatureCompat.sIsLightVersion || hasExternal || isDfmCategory) {
                                Log.d(TAG, "isLightVersion = ${FeatureCompat.sIsLightVersion} file Delete Directly  is category = $mCategory")
                                //这里开始删除
                                innerDelete(it, mParameters, mListener, RecycleBinUtils.isMediaTypeCategory(mCategory))
                            } else {
                                //这里开始放在回收站
                                innerRecycle(it, mParameters, mListener, RecycleBinUtils.isMediaTypeCategory(mCategory), mIsSelectAll)
                            }
                        }
                        val list = mParameters?.mBaseFiles ?: listOf()
                        val info = OptimizeStatisticsUtil.OperationInfo(
                            list.size.toString(),
                            opTime = Utils.getDateAndTimeFormat(it, System.currentTimeMillis()),
                            files = list
                        )
                        OptimizeStatisticsUtil.allOperation(info, true, mIsSelectAll)
                    }
                }
                DialogInterface.BUTTON_NEGATIVE -> mWeakRecycleBinCancelListener.get()?.onCancel()
            }
            dialog?.dismiss()
        }

        override fun onCancel(dialog: DialogInterface?) {
            dialog?.dismiss()
            mWeakActivity.clear()
            mWeakRecycleBinCancelListener.get()?.onCancel()
        }
    }
}