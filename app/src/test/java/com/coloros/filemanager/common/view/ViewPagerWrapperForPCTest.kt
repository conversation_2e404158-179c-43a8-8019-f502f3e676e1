/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.coloros.filemanager.ViewPagerWrapperForPCTest
 * * Description : ViewPagerWrapperForPCTest 单元测试
 * * Version     : 1.0
 * * Date        : 2022/4/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.common.view

import com.filemanager.common.view.ViewPagerWrapperForPC
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Test

class ViewPagerWrapperForPCTest {

    @Test
    fun `should return false if touch from pc when judge`() {
        val wrapper = mockk<ViewPagerWrapperForPC> {
            every { judgeUserInputEnabled(any(), any(), any()) } answers { callOriginal() }
        }
        Assert.assertEquals(false, wrapper.judgeUserInputEnabled(true, userInputEnabled = true, editMode = true))
    }

    @Test
    fun `should return true if not touch from pc when judge`() {
        val wrapper = mockk<ViewPagerWrapperForPC> {
            every { judgeUserInputEnabled(any(), any(), any()) } answers { callOriginal() }
        }
        Assert.assertEquals(true, wrapper.judgeUserInputEnabled(false, userInputEnabled = true, editMode = true))
        Assert.assertEquals(false, wrapper.judgeUserInputEnabled(false, userInputEnabled = false, editMode = true))
    }

    @Test
    fun `should return false if touch from pc and not input enable when judge`() {
        val wrapper = mockk<ViewPagerWrapperForPC> {
            every { judgeUserInputEnabled(any(), any(), any()) } answers { callOriginal() }
        }
        Assert.assertEquals(false, wrapper.judgeUserInputEnabled(true, userInputEnabled = false, editMode = true))
    }

    @Test
    fun `should return false if not touch from pc and not input enable and edit mode when judge`() {
        val wrapper = mockk<ViewPagerWrapperForPC> {
            every { judgeUserInputEnabled(any(), any(), any()) } answers { callOriginal() }
        }
        wrapper.judgeUserInputEnabled(true, userInputEnabled = true, editMode = true)
        Assert.assertEquals(false, wrapper.judgeUserInputEnabled(false, userInputEnabled = false, editMode = true))
    }

    @Test
    fun `should return true if not touch from pc and not input enable and not edit mode when judge`() {
        val wrapper = mockk<ViewPagerWrapperForPC> {
            every { judgeUserInputEnabled(any(), any(), any()) } answers { callOriginal() }
        }
        wrapper.judgeUserInputEnabled(true, userInputEnabled = true, editMode = true)
        Assert.assertEquals(true, wrapper.judgeUserInputEnabled(false, userInputEnabled = false, editMode = false))
    }
}