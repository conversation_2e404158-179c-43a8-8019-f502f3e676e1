/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : AppSwitchManager.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/27
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch

import android.content.Context
import android.os.BadParcelableException
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import androidx.lifecycle.ProcessLifecycleOwner
import com.coloros.filemanager.appswitch.index.IndexSyncHelper
import com.coloros.filemanager.appswitch.util.ConvertUtil
import com.coloros.filemanager.appswitch.util.ThirdAppFileUtil
import com.filemanager.common.MyApplication
import com.coloros.filemanager.appswitch.bean.AppSwitchConfig
import com.filemanager.common.bean.SwitchChangeEvent
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam
import com.coloros.filemanager.appswitch.util.InitHelper
import com.coloros.filemanager.appswitch.util.StaticUtil
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.databus.EventConstants
import com.filemanager.common.databus.LiteEventBus
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.ThirdAppSearchUtil
import com.oplus.app.OplusAppEnterInfo
import com.oplus.app.OplusAppExitInfo
import com.oplus.app.OplusAppSwitchConfig
import com.oplus.app.OplusAppSwitchManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.lang.ClassCastException
import java.util.function.Consumer

object AppSwitchManager : InitHelper.DmpInitStatusListener {

    const val TAG = "AppSwitchManager"

    //场景智能更新配置的url
    const val SCENESERVICE_UPDATE_URL = "content://com.oplus.sceneservice.lightprovider"

    //场景智能更新配置的url
    const val SCENESERVICE_UPDATE_AUTHORITY = "com.oplus.sceneservice.lightprovider"

    //场景智能更新配置的方法名
    const val SCENESERVICE_UPDATE_METHOD = "method_update_appswitch_config"

    //场景智能业务开关system setting值的key
    const val APPSWITCH_SETTING_KEY_SUFFIX = ".sceneservice.ability.appswitch"

    const val APP_SWITCH_SETTING = "third_app_switch_from_cloud"


    @Volatile
    private var useService = false
    @Volatile
    private var lastConfig: AppSwitchConfig? = null
    @Volatile
    private var currentConfig: AppSwitchConfig? = null
    @Volatile
    private var initThirdAppListenConfigParam: ThirdAppListenConfigParam? = null
    @Volatile
    var currentThirdAppListenConfigParam: ThirdAppListenConfigParam? = null

    private var intentAndViewProcessManager: IntentAndViewExtracProcessManager = IntentAndViewExtracProcessManager()

    private var indexSyncHelper: IndexSyncHelper = IndexSyncHelper(intentAndViewProcessManager)

    private val singleExecutorScope = CoroutineScope(Dispatchers.Default)

    private val thirdAppPackageChangeHelper = ThirdAppPackageChangeHelper()

    private var configReadyConsumer: Consumer<Boolean>? = null

    fun initDmpAndAppSwitch(useService: Boolean) {
        Log.i(TAG, "initDmpAndAppSwitch")
        singleExecutorScope.launch {
            Log.i(TAG, "initDmpAndAppSwitch start")
            initAppSwitchManager(useService)
            InitHelper.registerStatusChangeListerner(this@AppSwitchManager)
            InitHelper.initDmp()
            Log.i(TAG, "initDmpAndAppSwitch end")
            invokeConfigReady()
        }
        thirdAppPackageChangeHelper.initHeler(MyApplication.appContext)
    }

    fun setConsumer(inputConsumer: Consumer<Boolean>?) {
        configReadyConsumer = inputConsumer
    }

    private fun invokeConfigReady() {
        val configNotNull = currentThirdAppListenConfigParam != null
        configReadyConsumer?.accept(configNotNull)
    }

    fun initAppSwitchManagerAsync(inputUseService: Boolean) {
        Log.i(TAG, "init inputUseService $inputUseService ")
        singleExecutorScope.launch {
            initAppSwitchManager(inputUseService)
        }
    }

    fun initAppSwitchManager(inputUseService: Boolean) {
        initInternal(inputUseService)
        singleExecutorScope.launch(Dispatchers.Main) {
            registerObserver()
        }
    }

    fun doIndexSyncWork() {
        indexSyncHelper.doSyncWorks()
    }

    private fun initInternal(inputUseService: Boolean) {
        useService = inputUseService
        initConfigFromAssetsAndCache()
    }

    //AppSwitch的相应处理
    private val mOplusObserver: OplusAppSwitchManager.OnAppSwitchObserver =
        object : OplusAppSwitchManager.OnAppSwitchObserver {
            override fun onAppEnter(info: OplusAppEnterInfo) {
                Log.d(TAG, "OnAppSwitchObserver: onAppEnter , info = $info")
            }

            override fun onAppExit(info: OplusAppExitInfo) {
                Log.d(TAG, "OnAppSwitchObserver: onAppExit , info = $info")
            }

            override fun onActivityEnter(info: OplusAppEnterInfo) {
                Log.d(TAG, "OnAppSwitchObserver: onActivityEnter , info = $info")
                val extension = info.extension
                val keys = extension.keySet()
                if (keys != null) {
                    Log.i(TAG, "OnAppSwitchObserver start extra keys ${keys.toList()}")
                    for (key in keys) {
                        try {
                            val any = extension.get(key)
                            Log.i(TAG, "OnAppSwitchObserver key $key any $any")
                        } catch (e: BadParcelableException) {
                            Log.e(TAG, "getEntityFromIntent error", e)
                        } catch (e: ClassCastException) {
                            Log.e(TAG, "getEntityFromIntent error", e)
                        }
                    }
                }
                processAppEnterInfo(info)
            }

            override fun onActivityExit(info: OplusAppExitInfo) {
                Log.d(TAG, "OnAppSwitchObserver: onActivityExit , info = $info")
            }
        }

    fun processAppEnterInfo(info: OplusAppEnterInfo) {
        Log.i(TAG, "processAppEnterInfo $info")
        singleExecutorScope.launch {
            val intent = info.intent
            val component = intent.component
            val previewActivity = info.targetName
            val packageName = component?.packageName
            if (packageName != null) {
                val canExact = checkCanProcessAppEnter()
                if (!canExact) {
                    Log.w(TAG, "processAppEnterInfo can not exact, return")
                    return@launch
                }
                val detectRule = currentThirdAppListenConfigParam?.getRulesForPackage(packageName) ?: emptyList()
                val ruleValate = ConvertUtil.checkRuleListValidate(MyApplication.appContext, detectRule)
                if (ruleValate) {
                    intentAndViewProcessManager.processAppEnterInfo(info)
                } else {
                    Log.w(TAG, "processAppEnterInfo rule for $packageName and $previewActivity not validate no processed")
                }
            }
        }
    }

    private fun checkCanProcessAppEnter(): Boolean {
        if (InitHelper.isInitDone()) {
            val switchOpen = getOpenSwitchStatus()
            val featureOnFromConfig = currentThirdAppListenConfigParam?.isFeatureOn() ?: false
            Log.i(TAG, "checkCanProcessAppEnter switchOpen $switchOpen, featureOnFromConfig $featureOnFromConfig")
            return featureOnFromConfig && switchOpen
        } else {
            Log.i(TAG, "checkCanProcessAppEnter init progress ing, just allow viewExact ")
            checkAndInitThirdAppConfig()
            return true
        }
    }


    private fun checkAndInitThirdAppConfig() {
        if (currentThirdAppListenConfigParam == null) {
            Log.i(TAG, "checkAndInitThirdAppConfig")
            initAppSwitchManager(true)
        }
    }


    @Synchronized
    private fun initConfigFromAssetsAndCache() {
        Log.d(TAG, "initConfigFromAssetsAndCache begin")
        val config = ThirdAppFileUtil.getConfigBetweenAssetAndCache()
        config?.let {
            initThirdAppListenConfigParam = config
            setCurrentThirdAppConfig(config)
            Log.d(TAG, "initConfigFromAssetsAndCache end")
        }
    }

    fun updateThirdAppListenConfig(configJsonString: String) {
        Log.i(TAG, "updateThirdAppListenConfig start")
        singleExecutorScope.launch {
            runCatching {
                val config = ThirdAppListenConfigParam.parseFromJsonString(configJsonString)
                val newConfigOn = config.isFeatureOn()
                setCurrentThirdAppConfig(config, true)
                //将云控中的开关值写入setting值中，提供给文档,用于判断文档是否需要显示跳转文件管理的设置项
                Settings.Secure.putInt(
                    MyApplication.appContext.contentResolver,
                    APP_SWITCH_SETTING,
                    config.isOn
                )
                processDmpWhenNewConfigCome(newConfigOn)
                StaticUtil.uploadInitSucStatic(MyApplication.appContext, InitHelper.isInitSuc(), newConfigOn)
            }.onFailure {
                Log.e(TAG, "updateThirdAppListenConfig error", it)
            }
        }
    }

    private fun processDmpWhenNewConfigCome(isFeatureOn: Boolean) {
        Log.i(TAG, "processDmpWhenNewConfigCome start")
        if (isFeatureOn && !MyApplication.dmpAndIndexInitSuc) {
            InitHelper.registerStatusChangeListerner(this)
            InitHelper.initDmp()
            Log.i(TAG, "processDmpWhenNewConfigCome end")
        } else {
            Log.i(TAG, "processDmpWhenNewConfigCome isFeatureOn $isFeatureOn")
        }
    }


    @Synchronized
    private fun setCurrentThirdAppConfig(
        inputThirdAppConfig: ThirdAppListenConfigParam,
        needSaveCahe: Boolean = false
    ) {
        val currentConfigVersion = currentThirdAppListenConfigParam?.version ?: 0
        val newConfigVersion = inputThirdAppConfig.version
        Log.i(TAG, "setCurrentThirdAppConfig needSaveCahe $needSaveCahe, input: $inputThirdAppConfig, newConfigVersion $newConfigVersion, " +
                    "currentThirdAppListenConfigParam $currentThirdAppListenConfigParam, currentConfigVersion $currentConfigVersion")
        if (currentThirdAppListenConfigParam == null || (newConfigVersion > currentConfigVersion)) {
            currentThirdAppListenConfigParam = inputThirdAppConfig
            LiteEventBus.instance.send(EventConstants.EVENT_THIRD_APP_SEARCH_IS_FEATURE_ON, inputThirdAppConfig.isFeatureOn())
            if (needSaveCahe) {
                ThirdAppFileUtil.checkAndSaveConfigToCacheFile(inputThirdAppConfig)
            }
            val openSwitchStatus = getOpenSwitchStatus()
            val appSwitchConfig = ConvertUtil.getAppSwitchConfigByThirdAppConfigWithNoCheck(
                openSwitchStatus,
                inputThirdAppConfig
            )
            setConfig(appSwitchConfig)
            Log.i(TAG, "setCurrentThirdAppConfig update current")
        }
    }


    fun setConfigAsync(inputConfig: AppSwitchConfig) {
        singleExecutorScope.launch {
            setConfig(inputConfig)
        }
    }


    /**
     * 提供给外部更新当前AppConfig的方法
     */
    private fun setConfig(inputConfig: AppSwitchConfig) {
        Log.i(TAG, "setConfig input $inputConfig, last: $lastConfig")
        if (inputConfig != currentConfig) {
            lastConfig = currentConfig
            currentConfig = inputConfig
            onAppSwitchConfigChanged(inputConfig)
        }
    }

    /**
     * 提供给外部获取当前AppConfig的方法
     */
    fun getCurrentConfig(): AppSwitchConfig? {
        return currentConfig
    }

    /**
     * 获取上一次的AppSwithConfig的方法
     */
    fun getLastConfig(): AppSwitchConfig? {
        return lastConfig
    }


    /**
     * 更新场景智能的Provider，将新的需要监听的配置项（监听的app列表和界面列表）更新到场景智能App
     */
    private fun updateNewConfigToSceneService(
        context: Context,
        packageList: List<String>,
        activityList: List<String>
    ) {
        val bundle = Bundle()
        bundle.putStringArrayList(AppSwitchProvider.KEY_PACKAGE_LIST, ArrayList(packageList))
        bundle.putStringArrayList(AppSwitchProvider.KEY_ACTIVITY_LIST, ArrayList(activityList))
        var resultBundle: Bundle? = null
        try {
            resultBundle = context.contentResolver.call(
                SCENESERVICE_UPDATE_AUTHORITY,
                SCENESERVICE_UPDATE_METHOD,
                null,
                bundle
            )
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, "updateNewConfigToSceneService error", e)
        }
        Log.i(
            TAG,
            "updateNewConfigToSceneService result: $resultBundle, inputBundle packageList ${
                listOf(packageList)
            }, " +
                    "inputBundle activityList ${listOf(activityList)}"
        )
    }


    /**
     * 获取开关AppSwitch的setting名称。
     */
    private fun getSystemSettingKey(context: Context): String {
        val packageName = context.packageName
        val settingName = StringBuilder(packageName).append(APPSWITCH_SETTING_KEY_SUFFIX).toString()
        Log.i(TAG, "getSystemSettingKey $settingName")
        return settingName
    }


    /**
     * 更新开关AppSwitch的setting值，设置1时表明需要监听AppSwitch，设置0时表明不需要监听AppSwitch。
     */
    private fun updateAppSwitchSettingStatus(context: Context, open: Boolean) {
        val settingName = getSystemSettingKey(context)
        val resolver = context.contentResolver
        try {
            Settings.System.putInt(resolver, settingName, if (open) 1 else 0)
        } catch (e: SecurityException) {
            Log.e(TAG, "updateAppSwitchSettingStatus error", e)
        } finally {
            Log.i(TAG, "updateAppSwitchSettingStatus settingName $settingName, open $open")
        }
    }


    private fun registerOplusAppSwith(context: Context) {
        val appConfig = getCurrentConfig()
        appConfig?.let {
            val config = OplusAppSwitchConfig()
            // add activity switch callback.
            config.addAppConfig(OplusAppSwitchConfig.TYPE_ACTIVITY, it.activities)
            // add package switch callback.
            config.addAppConfig(OplusAppSwitchConfig.TYPE_PACKAGE, it.packages)
            Log.d(TAG, "registerOplusAppSwith config $config")
            runCatching {
                OplusAppSwitchManager.getInstance().registerAppSwitchObserver(context, mOplusObserver, config)
            }.onFailure {
                Log.e(TAG, "registerOplusAppSwith error", it)
            }
        }
    }


    private fun unregisterOplusAppSwith(context: Context) {
        runCatching {
            OplusAppSwitchManager.getInstance().unregisterAppSwitchObserver(context, mOplusObserver)
            Log.d(TAG, "unregisterOplusAppSwith mOplusObserver $mOplusObserver")
        }.onFailure {
            Log.e(TAG, "unregisterOplusAppSwith error", it)
        }
    }


    /**
     *  这里App的SwitchConfig变动了，需要触发相应逻辑
     */
    private fun onAppSwitchConfigChanged(appSwitchConfig: AppSwitchConfig) {
        val context = MyApplication.sAppContext
        if (appSwitchConfig.switchOn) {
            //开启时处理, 开启setting值，更新场景智能package列表和activity列表
            if (useService) {
                updateAppSwitchSettingStatus(context, true)
                if (ThirdAppSearchUtil.checkSmartSceneSupportAppSwitch(context)) {
                    updateNewConfigToSceneService(
                        context,
                        appSwitchConfig.packages,
                        appSwitchConfig.activities
                    )
                }
            } else {
                //开始注册监听
                registerOplusAppSwith(context)
            }
        } else {
            //解注册监听
            if (useService) {
                //关闭时处理
                updateAppSwitchSettingStatus(context, false)
                updateNewConfigToSceneService(context, mutableListOf(), mutableListOf())
            } else {
                //解注册监听
                unregisterOplusAppSwith(context)
            }
        }
    }


    private fun registerObserver() {
        LiteEventBus.instance.with(
            EventConstants.EVENT_THIRD_APP_SEARCH_SWITCH_CHANGE,
            EventConstants.OBSERVERID_SWITCH_CHANGE_APP
        )?.observe(ProcessLifecycleOwner.get()) { searchCardEvent ->
            Log.d(TAG, "eventChange OBSERVERID_SWITCH_CHANGE_APP")
            if (searchCardEvent is SwitchChangeEvent) {
                Log.d(TAG, "eventChange OBSERVERID_SWITCH_CHANGE_APP")
                onSwitchChange()
            }
        }
        Log.i(TAG, "registerObserver")
    }


    private fun unRegisterObserver(context: Context) {
        LiteEventBus.instance.releaseObserver(EventConstants.OBSERVERID_SWITCH_CHANGE_APP, ProcessLifecycleOwner.get())
        Log.i(TAG, "unRegisterObserver")
    }


    private fun onSwitchChange() {
        singleExecutorScope.launch {
            val openSwitchStatus = getOpenSwitchStatus()
            val currentConfig = currentThirdAppListenConfigParam
            if (currentConfig == null) {
                Log.i(TAG, "onChange currentConfig is null, return")
                return@launch
            }
            val appSwitchConfig = ConvertUtil.getAppSwitchConfigByThirdAppConfigWithNoCheck(openSwitchStatus, currentConfig)
            Log.i(TAG, "onChange setConfig $appSwitchConfig")
            setConfig(appSwitchConfig)
        }
    }

    private fun getOpenSwitchStatus(): Boolean {
        return PreferencesUtils.getBoolean(
            key = CommonConstants.THIRD_APP_SEARCH_FUNCTION_SHOW,
            default = false
        )
    }

    override fun onStateChange(status: Int) {
        Log.i(TAG, "onStateChange $status")
        if (status == InitHelper.INIT_STATUS_FAILED || status == InitHelper.INIT_STATUS_SUC) {
            intentAndViewProcessManager.flushPending()
        }
        if (status == InitHelper.INIT_STATUS_FAILED) {
            //初始化结果表明不支持常用应用搜索,关闭
            processDmpNotSupportAfterInit()
        }
    }

    private fun processDmpNotSupportAfterInit() {
        currentThirdAppListenConfigParam = null
        val appSwitchConfig = ConvertUtil.getSwitchOffAppSwitchConfig()
        Log.w(TAG, "processDmpNotSupportAfterInit setConfig $appSwitchConfig")
        setConfig(appSwitchConfig)
    }
}