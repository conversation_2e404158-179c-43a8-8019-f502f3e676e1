# 关于SimulateClickEngine
## 一、来源
### 1.1 原始代码：
* Git：http://alm.adc.com/OPPO/ROM%E5%BA%94%E7%94%A8%E4%B8%AD%E5%BF%83%E6%8A%80%E6%9C%AF%E5%9B%A2%E9%98%9F/_git/LLMSimulateClick  
* 分支：dev_wgf  
* 备注：FileManager中的已根据代码扫描要求和实际适配需求，做了一定程度的修改定制，与原始的代码有不少差异，但核心的无障碍脚本执行逻辑流程是基本一致的。

### 1.2 脚本生成辅助工具
* Git：http://alm.adc.com/OPPO/ROM%E5%BA%94%E7%94%A8%E4%B8%AD%E5%BF%83%E6%8A%80%E6%9C%AF%E5%9B%A2%E9%98%9F/_git/LLMSimulateClick  
* 分支：master
* 备注：  
LLMSimulateClick的master分支版本可以通过记录手动操作来自动录制无障碍脚本。  
但由于脚本引擎代码逻辑与dev_wgf和FileManager中的实际版本差异较大，仅能用于生成脚本草稿来简化人力适配维护操作。  
生成的脚本草稿还需要进行人工优化和适配，并基于FileManager中的实际版本的脚本引擎运行验证。

## 二、定制修改：
* Script节点:  
    * script_ver：增加"script_ver"字段，代表脚本本身的版本号，被选中的脚本中(含云控下发)，版本号最高的生效；  
    * app_min_ver：适配的目标三方应用的最小versionCode，默认无限制；  
    * app_max_ver：适配的目标三方应用的最大versionCode，默认无限制；  
    * screen_display：指定适配屏幕类型的Flag值，支持或操作("|")配置多个，具体类型:    
        * normal：直板机(含Fold型折叠屏折叠起来后的副屏和Flip型折叠屏展开后的直屏)  
        * unfold：Fold型折叠屏展开后的中屏  
        * pad：平板  
        * flip：Flip型折叠屏折叠起来后的小副屏  
    * screen_orientation：指定适配的屏幕布局方向，支持或操作("|")配置多个，具体类型:  
        * portrait：竖屏布局  
        * landscape：横屏布局  
    * screen_rotation：指定适配的屏幕传感器方向，支持或操作("|")配置多个，具体类型:  
        * 0 ：屏幕方向0°  
        * 90: 屏幕方向90°  
        * 180: 屏幕方向180°  
        * 270: 屏幕方向270°  
        * all: 所有屏幕方向，等价于"0|90|180|270"  
    * open_again_delay：指定拉起应用时的二次拉起延迟，默认为null代表不进行二次拉起。二次拉起时intent不会带有CREAR_TASK标志，用于适配企业微信和钉钉这种用CREAR_TASK拉起可能会导致闪退的应用；  
* Skill节点:  
    * max_execute_time: 限制脚本Skill的最大执行时间，超时直接终止；
    * step_delay_time: Skill内每个step执行的间隔时间；
* Action节点：
    * widget_id_list: 更改了原逻辑，现在这个配置为匹配上列表内任意一个widget id即可；
    * widget_contains_actions: 支持匹配控件节点的AccessibilityAction；
    * find_widget_by_txt_directly: 为true则按text查找控件时直接调用Accessibility方法(可能有缺失等)，为false则递归遍历控件节点树查找；
    * only_find_no_id_widget_by_txt: 支持针对有id的控件也可仅按text内容查找；
    * click_gesture_delay: 执行click_gesture这个action时的延迟值；
    * click_gesture_wait: 执行click_gesture这个action后的等待值;
    * scroll_parent_delay: 尝试滚动父节点时的延迟值；
    * waite_time：在原逻辑基础上扩充了作用，执行action时将等待该值的延迟时间；
    * input_method: 屏蔽了支持拼音输入的实现，该选项在文管中无效，将始终按原始text输入

## 三、适配的应用版本
### 3.1 跳转三方应用搜索脚本
* 脚本统一Skill名称：open_file
* 脚本统一被搜索文件名占位参数：$arg0
* 内置适配脚本根路径：assets/simulate_click_scripts

#### 3.1.1 QQ适配版本：
|版本|适配的脚本文件|适配的脚本版本号|自检情况|备注|
|---|---|---|---|----|
|8.9.85|com_tencent_mobileqq/open_file_generic_8.9.85.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|8.9.90|com_tencent_mobileqq/open_file_generic_8.9.85.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|8.9.93|com_tencent_mobileqq/open_file_generic_8.9.85.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|9.0.0|com_tencent_mobileqq/open_file_generic_8.9.85.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|9.0.8|com_tencent_mobileqq/open_file_generic_8.9.85.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|9.0.15|com_tencent_mobileqq/open_file_generic_8.9.85.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|9.0.17|com_tencent_mobileqq/open_file_generic_8.9.85.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|9.0.20|com_tencent_mobileqq/open_file_generic_8.9.85.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|9.0.25|com_tencent_mobileqq/open_file_generic_8.9.85.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|9.0.30|com_tencent_mobileqq/open_file_generic_9.0.30.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|9.0.35|com_tencent_mobileqq/open_file_generic_9.0.30.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|9.0.50|com_tencent_mobileqq/open_file_generic_9.0.30.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|9.0.56|com_tencent_mobileqq/open_file_generic_9.0.30.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|9.0.60|com_tencent_mobileqq/open_file_generic_9.0.30.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|

备注：  
1. QQ的widget id被混淆，且几乎每个版本都在变化，但目前能找到不依靠widget id进行准确定位相关控件的办法；  
故暂时无需每个版本单独适配，仅针对布局实现有较大差异的版本(如9.0.30起)进行另外的适配；  

#### 3.1.2 微信适配版本：
|版本|适配的脚本文件|适配的脚本版本号|自检情况|备注|
|---|---|---|---|----|
|8.0.45|com_tencent_mm/open_file_generic_8.0.45.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|8.0.46|com_tencent_mm/open_file_generic_8.0.45.json|202406110000|NA|适配时已无法登录，但前后版本均适用适配方案，推定该版本也适配|
|8.0.47|com_tencent_mm/open_file_generic_8.0.45.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|8.0.48|com_tencent_mm/open_file_generic_8.0.45.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|8.0.49|com_tencent_mm/open_file_generic_8.0.45.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|

备注：  
1. 微信对太旧的版本会限制登录，目前实测仅最新的三个版本允许登录，其余旧版本仅有先前已登录但没升级的存量用户；  
2. 微信的widget id被混淆，但各个迭代版本中的id值比较固定，暂时可以继续依靠widget id进行控件定位；  

#### 3.1.3 企业微信适配版本：
| 版本     | 适配的脚本文件                                          |适配的脚本版本号|自检情况|备注|
|--------|--------------------------------------------------|---|---|----|
| 4.1.20 | com_tencent_wework/open_file_generic_4.1.30.json |202410210000|Pass|直板机、折叠屏中屏、平板均适配|
| 4.1.22 | com_tencent_wework/open_file_generic_4.1.30.json |202410210000|Pass|直板机、折叠屏中屏、平板均适配|
| 4.1.26 | com_tencent_wework/open_file_generic_4.1.30.json |202410210000|Pass|直板机、折叠屏中屏、平板均适配|
| 4.1.27 | com_tencent_wework/open_file_generic_4.1.30.json |202410210000|Pass|直板机、折叠屏中屏、平板均适配|
| 4.1.28 | com_tencent_wework/open_file_generic_4.1.30.json |202410210000|Pass|直板机、折叠屏中屏、平板均适配|
| 4.1.30 | com_tencent_wework/open_file_generic_4.1.30.json |202410210000|Pass|直板机、折叠屏中屏、平板均适配|

备注：  
1. 企业微信的widget id被混淆，且几乎每个版本都在变化，相关控件也缺乏独有特征，导致无法不依靠widget id定位。故目前企业微信只能逐个版本适配，新版本在适配并更新脚本文件前，跳转搜索功能将不可用；  
2. 企业微信在已启动过，近期任务内有其界面栈的情况下，使用代用CLEAR_TASK标志的Intent拉起会导致其闪屏页闪退(但进程没有崩溃)。故需要配置二次延迟拉起，在闪退后用不带CLEAR_TASK标志的Intent再拉起一次；  

#### 3.1.4 钉钉适配版本：
|版本|适配的脚本文件|适配的脚本版本号|自检情况|备注|
|---|---|---|---|----|
|7.5.0|com_alibaba_android_rimet/open_file_generic_7.5.0.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|7.5.5|com_alibaba_android_rimet/open_file_generic_7.5.0.json|202406110000|NA|找不到该历史版本APK，但前后版本均适用适配方案，推定该版本也适配|
|7.5.11|com_alibaba_android_rimet/open_file_generic_7.5.0.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|7.5.16|com_alibaba_android_rimet/open_file_generic_7.5.0.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|7.5.20|com_alibaba_android_rimet/open_file_generic_7.5.0.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|7.5.30|com_alibaba_android_rimet/open_file_generic_7.5.0.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|

备注：  
1. 钉钉在已启动过，近期任务内有其界面栈的情况下，使用代用CLEAR_TASK标志的Intent拉起会导致其闪屏页闪退(但进程没有崩溃)。故需要配置二次延迟拉起，在闪退后用不带CLEAR_TASK标志的Intent再拉起一次；  

#### 3.1.5 飞书适配版本：
|版本|适配的脚本文件|适配的脚本版本号|自检情况|备注|
|---|---|---|---|----|
|7.15.7|com_ss_android_lark/open_file_generic_7.15.7.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|
|7.18.7|com_ss_android_lark/open_file_generic_7.15.7.json|202406110000|Pass|直板机、折叠屏中屏、平板均适配|

备注：  
1. 飞书暂无需要特别注意的情况；  