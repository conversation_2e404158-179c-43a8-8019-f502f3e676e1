/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : SuperAppConditionsKtTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/3/2 20:36
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2023/3/2       1.0      create
 *********************************************************************/
package com.filemanager.superappsource.helper

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.CommonConstants.OWORK
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.UserManagerUtils
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SuperAppConditionsKtTest {

    @MockK
    lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
    }

    @Test
    fun testCheckOWorkSupperAppCondition() {
        mockkStatic(AppUtils::class)
        mockkStatic(PreferencesUtils::class)
        mockkObject(JavaFileHelper)
        mockkStatic(UserManagerUtils::class)

        every { AppUtils.isAppInstalledByPkgName(appContext, OWORK) }.returns(true)
        every { PreferencesUtils.getBoolean(key = CommonConstants.OWORK_FUNCTION_SHOW, default = true) }.returns(true)
        every { JavaFileHelper.checkAnyPathExist(any()) }.returns(false)
        every { UserManagerUtils.checkIsSystemUser(context) }.returns(true)
        val dirNotExistsResult = checkOWorkSuperAppCondition(context)
        Assert.assertFalse(dirNotExistsResult)

        every { AppUtils.isAppInstalledByPkgName(appContext, OWORK) }.returns(false)
        every { PreferencesUtils.getBoolean(key = CommonConstants.OWORK_FUNCTION_SHOW, default = true) }.returns(true)
        every { JavaFileHelper.checkAnyPathExist(any()) }.returns(true)
        every { UserManagerUtils.checkIsSystemUser(context) }.returns(true)
        val notInstalledResult = checkOWorkSuperAppCondition(context)
        Assert.assertFalse(notInstalledResult)

        every { AppUtils.isAppInstalledByPkgName(appContext, OWORK) }.returns(true)
        every { PreferencesUtils.getBoolean(key = CommonConstants.OWORK_FUNCTION_SHOW, default = true) }.returns(false)
        every { JavaFileHelper.checkAnyPathExist(any()) }.returns(true)
        every { UserManagerUtils.checkIsSystemUser(context) }.returns(true)
        val funcNotShowResult = checkOWorkSuperAppCondition(context)
        Assert.assertFalse(funcNotShowResult)

        every { AppUtils.isAppInstalledByPkgName(appContext, OWORK) }.returns(true)
        every { PreferencesUtils.getBoolean(key = CommonConstants.OWORK_FUNCTION_SHOW, default = true) }.returns(true)
        every { JavaFileHelper.checkAnyPathExist(any()) }.returns(true)
        every { UserManagerUtils.checkIsSystemUser(context) }.returns(false)
        val notSystemUserResult = checkOWorkSuperAppCondition(context)
        Assert.assertFalse(notSystemUserResult)

        every { AppUtils.isAppInstalledByPkgName(appContext, OWORK) }.returns(true)
        every { PreferencesUtils.getBoolean(key = CommonConstants.OWORK_FUNCTION_SHOW, default = true) }.returns(true)
        every { JavaFileHelper.checkAnyPathExist(any()) }.returns(true)
        every { UserManagerUtils.checkIsSystemUser(context) }.returns(true)
        val result = checkOWorkSuperAppCondition(context)
        Assert.assertTrue(result)

        unmockkStatic(AppUtils::class)
        unmockkStatic(PreferencesUtils::class)
        unmockkObject(JavaFileHelper)
        unmockkObject(UserManagerUtils::class)
    }
}