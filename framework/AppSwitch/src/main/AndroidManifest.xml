<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="com.oppo.permission.safe.SETTINGS"/>
    <uses-permission android:name="com.oplus.permission.safe.WINDOW"/>

    <queries>
        <package android:name="com.coloros.sceneservice" />
        <package android:name="com.oplus.dmp" />
    </queries>

    <application>

        <meta-data
            android:name="oplus.sceneservice.ability.appswitch.SUPPORT_TYPE"
            android:value="1" />

        <meta-data
            android:name="oplus.sceneservice.ability.appswitch.NOTIFY_TYPE"
            android:value="callProvider" />

        <meta-data
            android:name="oplus.sceneservice.ability.appswitch.DELAY_TIME"
            android:value="0" />


        <provider
            android:name="com.coloros.filemanager.appswitch.AppSwitchProvider"
            android:authorities="${applicationId}.sceneservice.ability.appswitch"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT" />


    </application>


</manifest>