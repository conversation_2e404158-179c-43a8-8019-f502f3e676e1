/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DownloadInterceptor
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/8/10 9:58
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/8/10       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.interceptors

import com.oplus.filemanager.drivebrowser.data.body.DownloadResponseBody
import com.oplus.filemanager.drivebrowser.download.DownloadProgressListener
import com.oplus.filemanager.drivebrowser.utils.DownloadController
import okhttp3.Interceptor
import okhttp3.Response

class DownloadInterceptor(
    private val listener: DownloadProgressListener,
    private val controller: DownloadController
) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val originResponse = chain.proceed(chain.request())
        return if (originResponse.body == null) {
            originResponse.newBuilder().build()
        } else {
            originResponse.newBuilder()
                .body(DownloadResponseBody(originResponse.body!!, listener, controller))
                .build()
        }
    }
}