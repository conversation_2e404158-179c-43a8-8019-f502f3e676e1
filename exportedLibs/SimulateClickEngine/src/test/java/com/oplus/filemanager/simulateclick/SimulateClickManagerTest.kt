/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SimulateClickManagerTest.kt
 * Description:
 * The test cases for SimulateClickManager
 *
 * Version: 1.0
 * Date: 2024-06-06
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-06-06   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick

import com.oplus.filemanager.simulateclick.request.ScriptExecRequest
import com.oplus.filemanager.simulateclick.sdk.OnScriptDetectedCallback
import com.oplus.filemanager.simulateclick.service.ScriptServiceManager
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SimulateClickManagerTest : Assert() {

    @Before
    fun setUp() {
        mockkStatic(ScriptServiceManager::class)
    }

    @After
    fun tearDown() {
        unmockkStatic(ScriptServiceManager::class)
    }

    @Test
    fun `should executeScript with open file request when openSearchFile`() {
        // Given
        val request = slot<ScriptExecRequest>()
        justRun { ScriptServiceManager.executeScript(any(), capture(request)) }
        val testPkg = "com.test.pkg"
        val testFile = "testFileName.test"
        val callback = mockk<OnScriptDetectedCallback>()

        // When
        SimulateClickManager.callOnApp(testPkg)
            .openSearchFile(testFile)
            .onDetectedCallback(callback)
            .execute(mockk())

        // Then
        assertEquals(testPkg, request.captured.packageName)
        assertEquals("open_file", request.captured.skillName)
        assertEquals(mapOf("\$args0" to testFile), request.captured.replacedMap)
        assertEquals(callback, request.captured.detectedCallback)
    }
}