package com.oplus.filemanager.drivebrowser.utils

import com.oplus.filemanager.drivebrowser.data.model.BaseResponse
import org.junit.Assert
import org.junit.Before
import org.junit.After
import org.junit.Test
import java.lang.RuntimeException

/**
 * Result类的单元测试类
 * 用于测试Result密封类及其扩展函数的行为
 */
class ResultTest {

    /**
     * 在每个测试方法执行前调用
     * 用于重置可能存在的静态状态
     */
    @Before
    fun setUp() {
        // 重置可能存在的静态状态
    }

    /**
     * 在每个测试方法执行后调用
     * 用于清理测试状态
     */
    @After
    fun tearDown() {
        // 清理测试状态
    }

    /**
     * 测试succeeded扩展属性
     * 验证只有非空的Success结果才会返回true
     */
    @Test
    fun `succeeded should return true only for non-null Success`() {
        // 测试Success非空
        Assert.assertTrue(Result.Success("data").succeeded)
        // 测试Success空值
        Assert.assertFalse(Result.Success<String?>(null).succeeded)
        // 测试Error
        Assert.assertFalse(Result.Error(Exception()).succeeded)
        // 测试NoNetwork
        Assert.assertFalse(Result.NoNetwork.succeeded)
    }

    /**
     * 测试dataOrNull扩展函数
     * 验证只有Success结果会返回数据，其他情况返回null
     */
    @Test
    fun `dataOrNull should return data for Success and null otherwise`() {
        // 测试Success非空
        Assert.assertEquals("test", Result.Success("test").dataOrNull())
        // 测试Success空值
        Assert.assertNull(Result.Success<String?>(null).dataOrNull())
        // 测试Error
        Assert.assertNull(Result.Error(Exception()).dataOrNull<Any?>())
        // 测试NoNetwork
        Assert.assertNull(Result.NoNetwork.dataOrNull<Any?>())
    }

    /**
     * 测试resultFrom函数
     * 当T是Unit类型时应该返回包含Unit的Success结果
     */
    @Test
    fun `resultFrom should return Success with Unit when T is Unit`() {
        val response = BaseResponse<Unit>(code = SUCCESS_CODE)
        val result = resultFrom { response }
        Assert.assertTrue(result is Result.Success && result.data == Unit)
    }

    /**
     * 测试resultFrom函数
     * 当T是可空类型且数据为null时应该返回包含null的Success结果
     */
    @Test
    fun `resultFrom should return Success with null when T is nullable`() {
        val response = BaseResponse<String?>(code = SUCCESS_CODE, data = null)
        val result = resultFrom { response }
        Assert.assertTrue(result is Result.Success && result.data == null)
    }

    /**
     * 测试resultFrom函数
     * 当T是非空类型且有有效数据时应该返回包含数据的Success结果
     */
    @Test
    fun `resultFrom should return Success with data when T is non-null`() {
        val response = BaseResponse(code = SUCCESS_CODE, data = "valid")
        val result = resultFrom { response }
        Assert.assertEquals("valid", (result as Result.Success).data)
    }

    /**
     * 测试resultFrom函数
     * 当响应码不是SUCCESS_CODE时应该返回Error结果
     */
    @Test
    fun `resultFrom should return Error when code is not SUCCESS_CODE`() {
        val response = BaseResponse<String>(code = 404, msg = "Not Found")
        val result = resultFrom { response }
        Assert.assertTrue(result is Result.Error)
        val error = (result as Result.Error).exception as ServerException
        Assert.assertEquals(404, error.code)
        Assert.assertEquals("Not Found", error.message)
    }

    /**
     * 测试resultFrom函数
     * 当执行块抛出异常时应该返回回退的Error结果
     */
    @Test
    fun `resultFrom should return fallback Error when block throws exception`() {
        val result = resultFrom<String> { throw RuntimeException("Network error") }
        Assert.assertTrue(result is Result.Error)
        val error = (result as Result.Error).exception as ServerException
        Assert.assertEquals(FALLBACK_ERROR_CODE, error.code)
        Assert.assertEquals(FALLBACK_ERROR_MESSAGE, error.message)
    }

    /**
     * 测试resultFrom函数
     * 当T是非空类型但数据为null时应该抛出NPE并返回回退的Error结果
     */
    @Test
    fun `resultFrom should throw NPE and return fallback when non-null T has null data`() {
        val response = BaseResponse<String>(code = SUCCESS_CODE, data = null)
        val result = resultFrom { response }
        Assert.assertTrue(result is Result.Error)
        val error = (result as Result.Error).exception as ServerException
        Assert.assertEquals(FALLBACK_ERROR_CODE, error.code)
        Assert.assertEquals(FALLBACK_ERROR_MESSAGE, error.message)
    }

    /**
     * 测试resultFrom函数
     * 当错误消息为null时应该使用回退的错误消息
     */
    @Test
    fun `resultFrom should use fallback message when error msg is null`() {
        val response = BaseResponse<String>(code = 500, msg = null)
        val result = resultFrom { response }
        val error = (result as Result.Error).exception as ServerException
        Assert.assertEquals(FALLBACK_ERROR_MESSAGE, error.message)
    }
}