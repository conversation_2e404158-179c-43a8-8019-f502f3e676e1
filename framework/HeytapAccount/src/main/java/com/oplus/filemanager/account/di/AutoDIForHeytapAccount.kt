/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForFileBrowser
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.account.di

import androidx.annotation.Keep
import com.oplus.filemanager.account.HeytapAccountApi
import com.oplus.filemanager.interfaze.heytapaccount.IHeytapAccount
import org.koin.dsl.module

@Keep
class AutoDIForHeytapAccount {

    val heytapAccountModule = module {
        single<IHeytapAccount>(createdAtStart = true) {
            HeytapAccountApi()
        }
    }
}