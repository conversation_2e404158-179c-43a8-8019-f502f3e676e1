/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileActionDetailTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/14
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/9/14      1.0        create
 ***********************************************************************/
package com.filemanager.fileoperate.detail

import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.provider.FileLabelMappingRecycleDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.Assert
import org.junit.Test
import java.io.File
import org.junit.After
import org.junit.Before

class FileActionDetailTest {

    @Before
    fun setup() {
        mockkObject(FileLabelMappingRecycleDBHelper)
    }

    @After
    fun after() {
        unmockkObject(FileLabelMappingRecycleDBHelper)
    }

    @Test
    fun fillLabelInfoForRecycle() {
        val detail = mockk<FileActionDetail>(relaxed = true)
        every { detail.fillLabelInfo(any()) } answers { callOriginal() }

        val id = 0L
        val name = "labelName"
        val path = "sdcard${File.separator}FileName"
        val data = mockk<FileDetailObserver.FileDetailBean>(relaxed = true)
        every { data.mLabelIds } returns arrayListOf(id)
        every { data.mLabelNames } returns arrayListOf(name)
        every { data.mPath } returns path
        every { data.mOriginPath } returns path
        every { data.mIsRecycleBin } returns true

        every { FileLabelMappingRecycleDBHelper.getFileLabelsByPath(path) } returns arrayListOf(
            FileLabelEntity(
                id = id,
                name = name,
                viewCount = 0,
                useCount = 0,
                pinTimeStamp = 0L,
                lastUsedTime = 0L
            )
        )
        detail.fillLabelInfo(data)
        Assert.assertEquals(id, data.mLabelIds[0])
        Assert.assertEquals(name, data.mLabelNames[0])
    }

    @Test
    fun fillLabelInfoForNormal() {
        mockkObject(FileLabelMappingDBHelper)
        val detail = mockk<FileActionDetail>(relaxed = true)
        every { detail.fillLabelInfo(any()) } answers { callOriginal() }

        val id = 0L
        val name = "labelName"
        val path = "sdcard${File.separator}FileName"
        val data = mockk<FileDetailObserver.FileDetailBean>(relaxed = true)
        every { data.mLabelIds } returns arrayListOf(id)
        every { data.mLabelNames } returns arrayListOf(name)
        every { data.mPath } returns path
        every { data.mIsRecycleBin } returns false

        every { FileLabelMappingDBHelper.getFileLabelsByPath(path) } returns arrayListOf(
            FileLabelEntity(
                id = id,
                name = name,
                viewCount = 0,
                useCount = 0,
                pinTimeStamp = 0L,
                lastUsedTime = 0L
            )
        )
        detail.fillLabelInfo(data)
        Assert.assertEquals(id, data.mLabelIds[0])
        Assert.assertEquals(name, data.mLabelNames[0])
    }

    @Test
    fun fillLabelInfoPathIsNull() {
        mockkObject(FileLabelMappingDBHelper)
        val detail = mockk<FileActionDetail>(relaxed = true)
        every { detail.fillLabelInfo(any()) } answers { callOriginal() }

        val id = 0L
        val name = "labelName"
        val path = "sdcard${File.separator}FileName"
        val data = mockk<FileDetailObserver.FileDetailBean>(relaxed = true)
        every { data.mLabelIds } returns arrayListOf()
        every { data.mLabelNames } returns arrayListOf()
        every { data.mPath } returns null
        every { data.mIsRecycleBin } returns false

        every { FileLabelMappingDBHelper.getFileLabelsByPath(path) } returns arrayListOf(
            FileLabelEntity(
                id = id,
                name = name,
                viewCount = 0,
                useCount = 0,
                pinTimeStamp = 0L,
                lastUsedTime = 0L
            )
        )
        detail.fillLabelInfo(data)
        Assert.assertEquals(0, data.mLabelIds.size)
        Assert.assertEquals(0, data.mLabelNames.size)
    }
}