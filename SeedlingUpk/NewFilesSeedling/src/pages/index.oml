<template>
    <card-template entry="desktop" onclick="clickBtn">
        <stack class="stack-box">
            <div class="container_background"></div>
            <div class="container_folder">
                <image class="folder_icon" src="assets/images/folder_icon.png"></image>
            </div>
            <div class="container">
                <div class="init_top" if="{{ showEmpty }}">
                    <text class="text black" >{{ $t('strings.new_files_found_n') }}</text>
                </div>
                <div class="value_top" else>
                    <text class="text_top" >{{ $t('strings.new_files_found') }}</text>
                    <div class="div_h">
                        <text class="value_text" >{{ number_value }}</text>
                        <text class="text_unit black" if="{{ showUnit }}">{{ $t('strings.new_files_number_unit') }}</text>
                    </div>
                </div>
            </div>
        </stack>
    </card-template>
</template>
<style>
    .stack-box {
        width: 100%;
        height: 100%;
    }
    .folder_icon{
        width: 100%;
        height: 54%;
    }
    .container_background {
        width: 100%;
        height: 100%;
        padding: 16px 16px 16px 16px;
        flex-direction: column;
        background-image:url(assets/images/background.png);
        background-size: cover;
    }
    .container_folder {
        width: 100%;
        height: 100%;
        flex-direction: column;
        justify-content: flex-end;
    }
    .container {
        width: 100%;
        height: 100%;
        padding: 16px 16px 16px 16px;
        flex-direction: column;
        background-size: cover;
    }
    .init_top {
        height: 48px;
        flex-direction: column;
    }
    .black {
        color: rgba(0, 0, 0, 0.9);
    }
    .text {
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
   }
    .text_top {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        text-overflow: ellipsis;
        max-lines: 1;
        color: rgba(0, 0, 0, 0.54);
   }
   .text_unit {
        font-size: 14px;
        font-weight: bold;
        line-height: 20px;
        margin-top: 10px;
        margin-left: 3px;
        margin-right: 3px;
   }
   .value_text {
        font-size: 40px;
        font-weight: bold;
   }
   .value_top {
        height: 80px;
        flex-direction: column;
    }
    .div_h {
        flex-direction: row;
    }
   @media (aspect-ratio: 1*2) {
        .top,.message {
            display: none;
        }
        .container {
            padding: 0;
            background-color: #FFFFFF;
        }
   }
</style>
<data>
    {
        "uiData": {
            "number_value": "0",
            "showEmpty": true,
            "showUnit": true
        },
        "uiEvent": {
            "clickBtn": {
                "type": "deeplink",
                "uri":"nativeapp://oplus.intent.action.filemanager.OPEN_FILEMANAGER",
                "params": {
                    "from": "new_files_seedling"
                }
            }
        }
    }
</data>
