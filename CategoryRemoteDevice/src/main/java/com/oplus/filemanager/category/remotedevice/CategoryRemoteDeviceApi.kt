/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : CategoryRemoteDeviceApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice

import android.app.Activity
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.activity.ComponentActivity
import androidx.fragment.app.Fragment
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewListFragmentCreator
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.category.remotedevice.disconect.DisconnectHelper
import com.oplus.filemanager.category.remotedevice.download.DownloadActivity
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean
import com.oplus.filemanager.category.remotedevice.download.service.RemoteFileDownloadDispatcher
import com.oplus.filemanager.category.remotedevice.operate.RemoteFileDownloadAction
import com.oplus.filemanager.category.remotedevice.operate.RemoteFileDownloadObserver
import com.oplus.filemanager.interfaze.categoryremotedevice.ICategoryRemoteDeviceApi
import com.oplus.filemanager.interfaze.main.IMain
import java.util.function.Consumer

object CategoryRemoteDeviceApi : ICategoryRemoteDeviceApi {

    const val TAG = "CategoryRemoteDeviceApi"

    override fun startRemoteFragment(
        activity: Activity,
        categoryType: Int,
        deviceId: String,
        deviceName: String,
        deviceStatus: Int,
        deviceSameAccount: Int,
        fromDetail: Boolean
    ) {
        val bundle = Bundle().apply {
            putString(KtConstants.P_REMOTE_DEVICE_ID, deviceId)
            putString(KtConstants.P_REMOTE_DEVICE_NAME, deviceName)
            putInt(KtConstants.P_REMOTE_DEVICE_STATUS, deviceStatus)
            putInt(KtConstants.P_REMOTE_DEVICE_SAME_ACCOUNT, deviceSameAccount)
            putInt(Constants.SELECTED_ITEM, -1)
            if (fromDetail) {
                putBoolean(KtConstants.FROM_DETAIL, true)
            }
        }
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.startFragment(activity, categoryType, bundle)
    }

    override fun startFileBrowserActivity(
        activity: Activity,
        deviceId: String,
        deviceName: String,
        deviceStatus: Int,
        deviceSameAccount: Int,
        needClearTop: Boolean,
        fromDetail: Boolean
    ) {
        val intent = Intent().apply {
            putExtra(KtConstants.P_REMOTE_DEVICE_ID, deviceId)
            putExtra(KtConstants.P_REMOTE_DEVICE_NAME, deviceName)
            putExtra(KtConstants.P_REMOTE_DEVICE_STATUS, deviceStatus)
            putExtra(KtConstants.P_REMOTE_DEVICE_SAME_ACCOUNT, deviceSameAccount)
            putExtra(KtConstants.FROM_DETAIL, fromDetail)
            setClass(activity, CategoryRemoteFileActivity::class.java)
            if (needClearTop) {
                setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            }
        }
        Log.i(TAG, "startDFMActivity $activity, deviceName $deviceName, , fromDetail $fromDetail")
        activity.startActivity(intent)
    }

    override fun jumpDownloadActivity(activity: Activity, deviceId: String, paths: ArrayList<Pair<String, Long>>, code: Int) {
        DownloadActivity.start(activity, deviceId, paths, code, false)
    }

    override fun jumpDownloadActivity(activity: Activity, taskId: Int) {
        val task = RemoteFileDownloadDispatcher.getTask(taskId)
        Log.d(TAG, "jumpDownloadActivity taskId:$taskId task:$task")
        if (task == null) {
            Log.e(TAG, "jumpDownloadActivity current download task:$taskId is wrong")
            return
        }
        DownloadActivity.start(activity, task.deviceId, task.downloadFiles, task.code, true, task.pendingIntent)
    }

    override fun downloadRemoteFile(
        activity: ComponentActivity,
        deviceId: String,
        paths: ArrayList<Pair<String, Long>>,
        code: Int,
        pendingIntent: PendingIntent?,
        desPath: String,
        consumer: Consumer<Boolean>
    ) {


        Log.d(TAG, "paths  $paths")
        val downloadFiles = paths.map { DownloadRemoteFileBean(it) }
        RemoteFileDownloadAction(activity, deviceId, downloadFiles,
            desPath, code, pendingIntent).execute(object :
            RemoteFileDownloadObserver(activity) {
            override fun onActionDone(result: Boolean, data: Any?) {
                super.onActionDone(result, data)
                Log.d(TAG, "onActionDone -> result = $result ; data = $data")
                consumer.accept(result)
            }
        })
    }

    override fun cancelAllDownload(context: Context) {
        val task = RemoteFileDownloadDispatcher.getCurrentDownloadingTask()
        Log.w(TAG, "cancelAllDownload $task")
        task?.let {
            val intent = RemoteFileDownloadDispatcher.createCancelDownloadService(context, it.deviceId, it.taskId)
            RemoteFileDownloadDispatcher.startService(context, intent, false)
        }
    }

    override fun getFragment(activity: Activity): Fragment {
        Log.d(TAG, "getFragment")
        val fragment = PreviewCombineFragment()
        fragment.setPreviewListFragmentCreator(object : IPreviewListFragmentCreator {
            override fun create(): IPreviewListFragment {
                return CategoryRemoteFileFragment()
            }
        })
        return fragment
    }

    override fun onResumeLoadData(fragment: Fragment) {
        Log.d(TAG, "onResumeLoadData")
        if (fragment is PreviewCombineFragment) {
            fragment.onResumeLoadData()
        }
    }

    override fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater) {
        Log.d(TAG, "onCreateOptionsMenu")
        if (fragment is PreviewCombineFragment) {
            fragment.onCreateOptionsMenu(menu, inflater)
        }
    }

    override fun onMenuItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onMenuItemSelected")
        if (fragment is PreviewCombineFragment) {
            return fragment.onMenuItemSelected(item)
        }
        return false
    }

    override fun pressBack(fragment: Fragment): Boolean {
        Log.d(TAG, "pressBack")
        if (fragment is PreviewCombineFragment) {
            return fragment.pressBack()
        }
        return false
    }

    override fun onNavigationItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onNavigationItemSelected")
        if (fragment is PreviewCombineFragment) {
            return fragment.onNavigationItemSelected(item)
        }
        return false
    }

    override fun setToolbarAndTabListener(
        fragment: Fragment,
        toolbar: COUIToolbar?,
        title: String,
        tabListener: TabActivityListener<MediaFileWrapper>
    ) {
        Log.d(TAG, "setToolbarAndTabListener")
    }

    override fun fromSelectPathResult(fragment: Fragment, requestCode: Int, paths: List<String>?) {
        Log.d(TAG, "fromSelectPathResult")
        if (fragment is PreviewCombineFragment) {
            fragment.fromSelectPathResult(requestCode, paths)
        }
    }

    override fun updateLabels(fragment: Fragment) {
        Log.d(TAG, "updateLabels")
    }

    override fun backToTop(fragment: Fragment) {
        Log.d(TAG, "backToTop")
        if (fragment is PreviewCombineFragment) {
            fragment.backToTop()
        }
    }

    override fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        Log.d(
            TAG,
            "setIsHalfScreen fragment $fragment, category $category, isHalfScreen: $isHalfScreen"
        )
        if (fragment is PreviewCombineFragment) {
            fragment.setIsHalfScreen(isHalfScreen)
        }
    }

    override fun permissionSuccess(fragment: Fragment) {
        Log.d(TAG, "permissionSuccess fragment:$fragment")
        if (fragment is PreviewCombineFragment) {
            fragment.permissionSuccess()
        }
    }

    override fun setCurrentFilePath(fragment: Fragment, path: String?) {
        if (path == null) {
            return
        }
        if (fragment is PreviewCombineFragment) {
            fragment.setCurrentFromOtherSide(path)
        }
    }

    override fun getCurrentPath(fragment: Fragment): String {
        Log.d(TAG, "getCurrentPath fragment $fragment")
        if (fragment is PreviewCombineFragment) {
            return fragment.getCurrentPath()
        }
        return ""
    }

    override fun exitSelectionMode(fragment: Fragment) {
        Log.d(TAG, "exitSelectionMode fragment $fragment")
        if (fragment is PreviewCombineFragment) {
            fragment.exitSelectionMode()
        }
    }

    override fun onSideNavigationClicked(fragment: Fragment, isOpen: Boolean): Boolean {
        if (fragment is PreviewCombineFragment) {
            return fragment.onSideNavigationClicked(isOpen)
        }
        return false
    }


    override fun checkAndTrigDelayDisconnect() {
        DisconnectHelper.checkDownloadingAndTrigWork()
    }

    override fun cancelDelayDisconnect() {
        DisconnectHelper.cancelWork()
    }
}