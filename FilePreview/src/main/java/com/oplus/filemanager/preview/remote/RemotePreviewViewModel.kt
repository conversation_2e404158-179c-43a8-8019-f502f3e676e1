/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemotePreviewViewModel.kt
 ** Description: RemotePreviewViewModel
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.preview.remote

import android.content.Context
import android.net.Uri
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.remotedevice.provider.ThumbnailProvider
import java.io.File
import java.io.IOException
import java.util.UUID

internal class RemotePreviewViewModel : FilePreviewViewModel() {

    private companion object {
        private const val TAG = "RemotePreviewViewModel"
        private const val NUM = 1024
        private const val DECIMAL_NUM = 1024f
    }

    private fun createDir(picFile: File) {
        if (picFile.parentFile?.exists()?.not() == true) {
            picFile.parentFile?.mkdirs()
        }
    }

    fun releaseDir(context: Context) {
        val file = File(context.cacheDir, "remotePic")
        val filePath = file.absolutePath
        deleteRemoteDir(filePath)
    }

    private fun deleteRemoteDir(directoryPath: String): Boolean {
        val directory = File(directoryPath)
        if (directory.exists()) {
            val files = directory.listFiles()
            if (files != null) {
                for (file in files) {
                    if (file.isDirectory) {
                        deleteRemoteDir(file.absolutePath)
                    } else {
                        file.delete()
                    }
                }
            }
        }
        return directory.delete()
    }

    fun createFile(context: Context): File {
        val pictureName = UUID.randomUUID().toString() //需要增加其他标志位
        val picFile = File(File(context.cacheDir, "remotePic"), "$pictureName.png")
        createDir(picFile)
        try {
            picFile.createNewFile()
        } catch (e: IOException) {
            Log.e(TAG, "createUri error " + e.message)
        }
        return picFile
    }

    fun createUri(context: Context, picFile: File): Uri {
        return ThumbnailProvider.getUriForFile(context, "filemanager.thumbnail.provider", picFile)
    }

    fun getDeviceId(): String {
        var devicesId = ""
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        val currentLinkInfo = remoteDeviceApi?.getCurrentLinkedRemoteDiceInfo()
        if (currentLinkInfo != null) {
            devicesId = currentLinkInfo.deviceId.toString()
        }
        return devicesId
    }

    fun updateSizeFormat(size: Long): String {
        return if (size < NUM) {
            size.toString() + "B"
        } else if (size < NUM * NUM) {
            String.format("%.1f", (size / DECIMAL_NUM)) + "KB"
        } else if (size < NUM * NUM * NUM) {
            String.format("%.1f", (size / NUM / DECIMAL_NUM)) + "MB"
        } else {
            String.format("%.1f", (size / NUM / NUM / DECIMAL_NUM)) + "GB"
        }
    }
}