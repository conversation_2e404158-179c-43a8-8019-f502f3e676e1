/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - RestoreOperationTest.java
 * Description: Test case for restore Operation
 * Version: 1.0
 * Date : 2020/04/20
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/04/20    1.0     create
 ****************************************************************/
package com.coloros.filemanager.recyclebin.operation

import android.net.Uri
import androidx.activity.ComponentActivity
import com.coloros.filemanager.BaseTest
import com.filemanager.common.base.BaseFileBean
import com.filemanager.recyclebin.operation.RestoreOperation
import com.filemanager.recyclebin.operation.listener.RestoreOperationListener
import junit.framework.Assert.*
import org.junit.After
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import shadow.ShadowViewHelper
import shadow.ShadowVolumeEnvironment

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
@Config(shadows = [ShadowViewHelper::class, ShadowVolumeEnvironment::class])
class RestoreOperationTest : BaseTest() {
    private lateinit var mActivity: ComponentActivity

    @Before
    override fun setUp() {
        super.setUp()
        mActivity = Robolectric.buildActivity(ComponentActivity::class.java).get()
    }

    @After
    override fun tearDown() {
        super.tearDown()
    }

    @Test
    fun should_vertify_when_addPaths_isNull() {
        val progressListener = RestoreOperationListener(mActivity, null);
        val restoreOperation = RestoreOperation(mActivity, progressListener)
        try {
            restoreOperation.addPaths(null)
            fail("addUris1 should throw UnsupportedOperationException")
        } catch (ex: Exception) {
        }

        try {
            restoreOperation.addPaths(ArrayList<String>())
            fail("addUris2 should throw UnsupportedOperationException")
        } catch (ex: Exception) {
        }
    }

    @Test
    fun should_vertify_when_addUris_isNull() {
        val progressListener = RestoreOperationListener(mActivity, null);
        val restoreOperation = RestoreOperation(mActivity, progressListener)
        assertFalse(restoreOperation.addUris(null))
        assertTrue(restoreOperation.addUris(ArrayList<Uri>()))
        assertFalse(restoreOperation.addUris(ArrayList<Uri>()))
    }

    @Test
    fun should_vertify_when_addFileBeans_isNull() {
        val progressListener = RestoreOperationListener(mActivity, null);
        val restoreOperation = RestoreOperation(mActivity, progressListener)
        assertFalse(restoreOperation.addFileBeans(null))
        assertTrue(restoreOperation.addFileBeans(ArrayList<BaseFileBean>()))
        assertFalse(restoreOperation.addFileBeans(ArrayList<BaseFileBean>()))
    }
}