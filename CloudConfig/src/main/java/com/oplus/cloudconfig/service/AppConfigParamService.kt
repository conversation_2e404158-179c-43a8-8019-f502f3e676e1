/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.filemanager.cloudconfig.service
 ** Version: 1.0
 ** Date: 2021/5/12
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudconfig.service

import com.oplus.nearx.cloudconfig.anotation.Config
import com.oplus.nearx.cloudconfig.observable.Observable
import com.oplus.nearx.cloudconfig.stat.Const
import java.io.File

@Config(configCode = "app_config_param", type = Const.CONFIG_TYPE_FILE)
interface AppConfigParamService {
    fun getFile(): Observable<File>
}