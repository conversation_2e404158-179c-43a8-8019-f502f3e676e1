/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AlbumSetLoaderTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/2/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/2/27       1      create
 ***********************************************************************/
package com.oplus.filemanager.category.albumset

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.utils.AlbumItem
import com.oplus.filemanager.category.albumset.ui.AlbumSetLoader
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AlbumSetLoaderTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true)
        mockkObject(MyApplication)
        every { MyApplication.sAppContext }.returns(context)
        mockkStatic(MediaStoreCompat::class)
    }

    @After
    fun tearDown() {
        unmockkObject(MyApplication)
        unmockkStatic(MediaStoreCompat::class)
    }

    @Test
    fun `should execute getAlbumSet when loadInBackground`() {
        val loader = spyk(AlbumSetLoader(context))
        val resultList = ArrayList<AlbumItem>()
        every { MediaStoreCompat.getAlbumSet() } returns resultList
        loader.loadInBackground()
        verify { MediaStoreCompat.getAlbumSet() }
    }

    @Test
    fun `should return data when loadInBackground`() {
        val loader = spyk(AlbumSetLoader(context))
        val item1 = AlbumItem(1)
        val item2 = AlbumItem(2)
        val item3 = AlbumItem(3)
        val resultList = ArrayList<AlbumItem>().apply {
            add(item1)
            add(item2)
            add(item3)
        }
        every { MediaStoreCompat.getAlbumSet() } returns resultList
        val data = loader.loadInBackground()
        Assert.assertNotNull(data)
        Assert.assertEquals(3, data?.size)
    }
}