/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DisconnectHelper
 * * Description : 断开链接的Helper类，完成断开链接workManager的主要操作
 * * Version     : 1.0
 * * Date        : 2025/2/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.disconect

import android.util.Log
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import java.util.concurrent.TimeUnit

object DisconnectHelper {

    private const val TAG = "DisconnectHelper"
    private const val UNIQUE_WORK_NAME = "disconnect_remote_mac"
    private const val TIME_DELAY = 10L

    private fun getWorkManager(): WorkManager? {
        val context = MyApplication.appContext
        val initSuccess = SafeWorkManagerInitializer.safeInitializeWorkManager(context)
        if (!initSuccess) {
            Log.w(TAG, "WorkManager initialization failed, likely due to storage issues")
            return null
        }

        return SafeWorkManagerInitializer.getWorkManagerInstance()
    }

    @JvmStatic
    fun checkDownloadingAndTrigWork() {
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        val currentLinkInfo = remoteDeviceApi?.getCurrentLinkedRemoteDiceInfo()
        val remoteDeviceId = currentLinkInfo?.deviceId
        val isFileDownloading = if (remoteDeviceId != null) {
            remoteDeviceApi.isFileChannelOccupied(remoteDeviceId)
        } else {
            false
        }
        Log.d(TAG, "checkDownloadingAndTrigWork isFileDownloading $isFileDownloading, remoteDeviceId $remoteDeviceId")
        if (isFileDownloading) {
            //说明存在后台下载，延后到下载之后去触发
            Log.i(TAG, "checkAndProcessDownloading file downloading, not enqueue disconnectWork")
        } else {
            //没有后台下载，直接触发10分钟断链任务
            if (currentLinkInfo != null) {
                //如果当前已经链接
                checkAndEnqueWork()
            } else {
                Log.i(TAG, "checkAndProcessDownloading current not link, not enqueue disconnectWork")
            }
        }
    }

    @JvmStatic
    fun checkAndEnqueWork() {
        val workManager = SafeWorkManagerInitializer.getWorkManagerInstance() ?: return
        val workInfoFuture = workManager.getWorkInfosForUniqueWork(UNIQUE_WORK_NAME)
        Log.d(TAG, "checkAndEnqueWork start")
        val workInfoList = workInfoFuture.get()
        Log.d(TAG, "checkAndEnqueWork ING")
        if (workInfoList.isNullOrEmpty()) {
            //没有work在进行，需要新建
            Log.d(TAG, "checkAndEnqueWork no workInfo in workManager, just enqueue")
            buildOneTimeRequest(workManager)
        } else {
            val workInfo = workInfoList.first()
            val workId = workInfo.id
            val isFinished = workInfo.state.isFinished
            if (!isFinished) {
                //任务在RUNNING, ENQUEUED, BLOCKED 这3个状态时该怎么处理。
                Log.d(TAG, "checkAndEnqueWork work not finished, id $workId, state ${workInfo.state}, cancel and enqueue")
                cancelWork()
                buildOneTimeRequest(workManager)
            } else {
                Log.d(TAG, "checkAndEnqueWork work alreadyFinished, id $workId, state ${workInfo.state}, just enqueue")
                buildOneTimeRequest(workManager)
            }
        }
    }

    private fun buildOneTimeRequest(workManagerInstance: WorkManager) {
        runCatching {
            val request = OneTimeWorkRequestBuilder<DisconnectWorker>()
                .setInitialDelay(TIME_DELAY, TimeUnit.MINUTES)
                .build()
            val enqueueOperation =
                workManagerInstance.enqueueUniqueWork(UNIQUE_WORK_NAME, ExistingWorkPolicy.REPLACE, request)
            Log.d(TAG, "buildOneTimeRequest result ${enqueueOperation.state.value}")
        }.onFailure { exception ->
            Log.e(TAG, "buildOneTimeRequest failed: ${exception.message}", exception)
        }
    }

    @JvmStatic
    fun cancelWork() {
        val workManagerInstance = getWorkManager()
        if (workManagerInstance == null) {
            Log.e(TAG, "cancelWork: WorkManager is not available, possibly due to storage issues")
            return
        }
        runCatching {
            val cancelOperation = workManagerInstance.cancelUniqueWork(UNIQUE_WORK_NAME)
            Log.d(TAG, "cancelWork result ${cancelOperation.state.value}")
        }.onFailure { exception ->
            Log.e(TAG, "cancelWork failed: ${exception.message}", exception)
        }
    }
}