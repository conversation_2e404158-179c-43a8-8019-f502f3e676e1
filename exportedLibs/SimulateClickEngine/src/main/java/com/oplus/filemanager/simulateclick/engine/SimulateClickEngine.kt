/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SimulateClickEngine.kt
 * Description:
 *     To execute simulate click script.
 *
 * Version: 2.0
 * Date: 2024-05-22
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-05-22   2.0    Porting from LLMSimulateClick
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine

import android.content.Context
import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import android.view.accessibility.AccessibilityNodeInfo
import com.oplus.filemanager.simulateclick.engine.utils.InputUtil
import com.oplus.filemanager.simulateclick.engine.utils.Log
import com.oplus.filemanager.simulateclick.service.AccessibilityServiceBridge
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking

internal class SimulateClickEngine internal constructor(
    private val context: Context,
    internal val serviceBridge: AccessibilityServiceBridge,
    private val handlerThread: HandlerThread
) : Handler.Callback {

    private val uiSelector = UiSelector(serviceBridge)

    private val handler by lazy {
        Handler(handlerThread.looper, this@SimulateClickEngine)
    }

    private val handleList = mutableListOf<Status>()

    private var leftStepList = mutableListOf<Step>()
    private var originalStepList = listOf<Step>()

    /**
     *  当前执行的action
     */
    private var currentActionList = mutableListOf<Action>()
    private var currentStep: Step? = null

    /**
     * 最近打开的页面
     */
    private var lastCondition: String = ""
    private var nextCondition: String? = null

    /**
     * 多轮选择中的结果
     */
    private var stepSelectNum: Int? = null
    private var stepStatus = STEP_MAIN

    private var stepDelayTime = DEFAULT_STEP_DELAY_TIME

    @Volatile
    private var enableStepHandler = true

    private fun reset() {
        removeDoStepMessage()
        clearStep()
        stepDelayTime = DEFAULT_STEP_DELAY_TIME
        enableStepHandler = true
    }

    private fun setLeftStep(list: List<Step>?) {
        list ?: return
        leftStepList = list.toMutableList()
    }

    private fun setOriginalStep(list: List<Step>?) {
        list ?: return
        originalStepList = list.toList()
    }

    private fun clearStep() {
        leftStepList = mutableListOf()
        originalStepList = listOf()
        currentActionList = mutableListOf()
        currentStep = null
        lastCondition = ""
        nextCondition = null
        stepStatus = STEP_MAIN
        handleList.clear()
    }

    private fun sendDoStepMessageDelay() {
        sendDoStepMessage(stepDelayTime)
    }

    private fun sendDoStepMessage(time: Long = 0L) {
        if (!enableStepHandler) {
            return
        }
        handler.sendEmptyMessageDelayed(STEP_MAIN, time)
    }

    private fun sendDoStepSearchMessage(time: Long = 0L) {
        if (!enableStepHandler) {
            return
        }
        val msg: Message = Message.obtain()
        msg.what = STEP_SEARCH
        handler.sendMessageDelayed(msg, time)
    }

    private fun sendDoStepPreviousMessage(time: Long = 0L) {
        if (!enableStepHandler) {
            return
        }
        val msg: Message = Message.obtain()
        msg.what = STEP_PREVIOUS
        handler.sendMessageDelayed(msg, time)
    }

    private fun sendStatusMessage(status: Int, delay: Long) {
        if (!enableStepHandler) {
            return
        }
        handler.sendEmptyMessageDelayed(status, delay)
    }

    private fun removeDoStepMessage() {
        handler.removeCallbacksAndMessages(null)
    }

    private fun applyMaxStepExecuteTime(maxExecuteTime: Long?) {
        Log.d(TAG, "applyMaxStepExecuteTime: maxExecuteTime=$maxExecuteTime")
        maxExecuteTime ?: return
        val disableHandler = Runnable {
            Log.d(
                TAG,
                "applyMaxStepExecuteTime: time limit, disable handler and clear left steps."
            )
            enableStepHandler = false
            removeDoStepMessage()
        }
        handler.postDelayed(disableHandler, maxExecuteTime)
    }

    @JvmOverloads
    fun dealWithStep(
        packageName: String,
        skillName: String,
        script: Script,
        replacedMap: Map<String, String>,
        selectNum: Int? = null
    ) {
        reset()
        stepSelectNum = selectNum
        if (originalStepList.isEmpty()) {
            //操作从头开始
            startExecSteps(packageName, skillName, script, replacedMap)
        } else {
            //已经有完成一部分的步骤
            restartExecSteps(replacedMap)
        }
    }

    private fun startExecSteps(
        pkgName: String,
        skillName: String,
        script: Script,
        replacedMap: Map<String, String>,
    ) {
        Log.d(TAG, "startExecSteps: pkgName=$pkgName, skillName=$skillName")
        val targetSkill = script.skills?.firstOrNull { it.skillName == skillName }
        if (targetSkill == null) {
            Log.e(TAG, "startExecSteps: ERROR! No target skill $skillName")
            return
        }
        stepDelayTime = targetSkill.stepDelayTime()
        applyMaxStepExecuteTime(targetSkill.maxExecuteTime)
        val stepList = targetSkill.steps ?: emptyList()
        setOriginalStep(stepList)
        val tempList = mutableListOf<Step>().apply { addAll(stepList) }
        InputUtil.replaceAllTags(tempList, replacedMap)
        setLeftStep(tempList)
        UiDevice.openTargetApp(context, pkgName, "", script.openAgainDelay)
        sendDoStepMessage()
    }

    private fun restartExecSteps(replacedMap: Map<String, String>) {
        Log.d(TAG, "restartExecSteps: stepStatus=$stepStatus")
        InputUtil.replaceAllTags(leftStepList, replacedMap)
        if (stepStatus == STEP_MAIN) {
            if (currentActionList.isNotEmpty()) {
                InputUtil.replaceAction(currentActionList, replacedMap)
                Log.d(TAG, "restartExecSteps: action left=$currentActionList")
                sendDoStepMessage()
            } else if (leftStepList.isNotEmpty()) {
                Log.d(TAG, "restartExecSteps: step left=$leftStepList")
                sendDoStepMessage()
            }
        } else {
            handleList.forEach { status ->
                InputUtil.replaceAction(status.actionList, replacedMap)
                Log.d(TAG, "action status left=${status.actionList}")
            }
            sendStatusMessage(stepStatus, 0)
        }
    }

    override fun handleMessage(msg: Message): Boolean = runBlocking {
        runCatching {
            when (msg.what) {
                STEP_MAIN -> doStep()
                STEP_SEARCH -> doSearch()
                STEP_PREVIOUS -> doPrevious()
            }
        }.onFailure {
            Log.e(TAG, "handleMessage ERROR: msg=${msg.what}, err=$it")
        }
        return@runBlocking true
    }

    private suspend fun doPrevious() {
        if (handleList.isEmpty()) return
        val entry = handleList.last()
        if (entry.actionList.isEmpty()) {
            handleList.remove(entry)
            stepStatus = entry.parent
            sendStatusMessage(entry.parent, 0)
            return
        }
        val action = entry.actionList.elementAt(0)
        delay(action.waitTime())
        val nodeList = uiSelector.find(action)
        if (!nodeList.isNullOrEmpty()) {
            val node = nodeList[0]
            if (UiObject(this).dealWith(node, action)) {
                Log.d(TAG, "doPrevious: action success, node=${node.className}")
                entry.actionList.clear()
                sendDoStepPreviousMessage()
            }
        } else {
            Log.d(TAG, "doPrevious: action.skipTime=${action.skipTime}")
            if (action.skipTime <= 0) {
                entry.actionList.clear()
                sendDoStepPreviousMessage()
            } else {
                action.skipTime -= stepDelayTime
                sendDoStepPreviousMessage(stepDelayTime)
            }
        }
    }

    private suspend fun doSearch() {
        if (handleList.isEmpty()) return
        val entry = handleList.last()
        if (entry.actionList.size < 2) {
            handleList.remove(entry)
            stepStatus = entry.parent
            sendStatusMessage(entry.parent, 0)
            return
        }
        val searchAction = entry.actionList[0]
        val resultAction = entry.actionList[1]
        if (entry.moreChange == SEARCH_RESULT_OTHER_PAGE) {
            entry.moreChange = 0
            Log.d(TAG, "doSearch: other page")
            return
        }
        if (entry.moreChange == SEARCH_RESULT_PREVIOUS) {
            entry.moreChange = 0
            delay(resultAction.waitTime())
            val resultList = uiSelector.find(resultAction)
            if (resultList.isNullOrEmpty()) {
                Log.d(TAG, "doSearch: search nodeInfo after previous next is null")
                searchAction.inputMethod.takeUnless { it.isNullOrEmpty() }?.removeAt(0)
                sendDoStepSearchMessage()
                return
            }
            val input = searchAction.inputMethod?.elementAtOrNull(0)
            checkSearchResult(input, resultList, resultAction, entry.actionList)
            return
        }
        executeSearch(searchAction, resultAction, entry)
    }

    private suspend fun executeSearch(searchAction: Action, resultAction: Action, entry: Status) {
        clearCurrentStep()
        if (searchAction.inputMethod.isNullOrEmpty()) {
            Log.d(TAG, "doSearch: search all fail")
            return
        }
        if (checkQuery(searchAction)) return
        Log.d(TAG, "doSearch: search ing")
        if (searchAction.originInput.isNullOrEmpty()) {
            searchAction.originInput = searchAction.actionInput
        }
        if (resultAction.inputMethod == null) {
            resultAction.inputMethod = searchAction.inputMethod
        }
        val input = searchAction.inputMethod?.elementAtOrNull(0)
        searchAction.actionInput = InputUtil.textParse(input, searchAction.originInput ?: "")
        Log.d(TAG, "doSearch: search actionInput=${searchAction.actionInput}")
        delay(searchAction.waitTime())
        val searchList: List<AccessibilityNodeInfo>? = uiSelector.find(searchAction)
        if (searchList.isNullOrEmpty()) {
            Log.d(TAG, "doSearch: search nodeInfoList is null")
            checkTimeOut(searchAction, STEP_SEARCH)
            return
        }
        if (!UiObject(this).dealWith(searchList[0], searchAction)) {
            Log.d(TAG, "doSearch: search do fail")
            checkTimeOut(searchAction, STEP_SEARCH)
            return
        }
        Log.d(TAG, "doSearch: search do success")
        if (!resultAction.actionPrevious.isNullOrEmpty()) {
            val tempList = mutableListOf<Action>()
            resultAction.actionPrevious?.let(tempList::addAll)
            val status = Status(STEP_SEARCH, tempList)
            entry.moreChange = SEARCH_RESULT_PREVIOUS
            handleList.add(status)
            stepStatus = STEP_PREVIOUS
            sendDoStepPreviousMessage()
            return
        }
        delay(resultAction.waitTime())
        val resultList = uiSelector.find(resultAction)
        if (resultList.isNullOrEmpty()) {
            Log.d(TAG, "doSearch: search nodeInfo next is null")
            if (!searchAction.inputMethod.isNullOrEmpty()) {
                searchAction.inputMethod?.removeAt(0)
            }
            sendDoStepSearchMessage()
            return
        }
        checkSearchResult(input, resultList, resultAction, entry.actionList)
    }

    private suspend fun checkSearchResult(
        input: Int?,
        resultList: List<AccessibilityNodeInfo>,
        resultAction: Action,
        actionList: MutableList<Action>
    ) {
        if (INPUT_METHOD_SHORT_PINYIN_SPACE == input && stepSelectNum == null) {
            Log.d(TAG, "doSearch: search i==INPUT_METHOD_SHORT_PINYIN_SPACE")
            return
        }
        if (resultList.size > 1 && stepSelectNum == null) {
            val entry = handleList.last()
            entry.searchResult = SearchResult(resultList)
            Log.d(TAG, "doSearch: search list.size > 1")
            return
        }
        val selectNum = stepSelectNum?.dec() ?: 0
        stepSelectNum = null
        val node = resultList.elementAtOrNull(selectNum)
        Log.d(TAG, "doSearch: search result $node")
        if (UiObject(this).dealWith(node, resultAction)) {
            Log.d(TAG, "doSearch: done")
            actionList.clear()
            sendDoStepSearchMessage()
        } else {
            Log.d(TAG, "doSearch: search next click fail")
        }
    }

    private fun checkQuery(action: Action): Boolean {
        if (action.queryStep.isNullOrEmpty()) {
            Log.d(TAG, "checkQuery: no query step")
            return false
        }
        if (action.actionInput.isNullOrEmpty()) {
            Log.d(TAG, "checkQuery: no action input")
            action.queryStep = null
        }
        return true
    }

    private suspend fun doStep() {
        if (currentActionList.isNotEmpty()) {
            doAction(currentActionList.elementAtOrNull(0))
            return
        }
        if (leftStepList.isEmpty()) {
            Log.d(TAG, "doStep: leftStepList is empty")
            return
        }
        currentActionList = leftStepList[0].actions ?: mutableListOf()
        currentStep = leftStepList[0]
        Log.d(TAG, "doStep: condition=${leftStepList[0].condition}")
        if (leftStepList[0].condition == lastCondition) {
            Log.d(TAG, "doAction: too fast")
            sendDoStepMessageDelay()
            return
        }
        lastCondition = leftStepList[0].condition ?: ""
        nextCondition = leftStepList.elementAtOrNull(1)?.condition
        if (currentActionList.isEmpty()) {
            Log.d(TAG, "doStep: actions json error")
            return
        }
        doAction(currentActionList.elementAtOrNull(0))
    }


    private suspend fun doAction(action: Action?) {
        if (action == null) return
        val logActionInfo = action.run {
            "actionType=$actionType, widgetID=$widgetID, " +
                    "widgetText=$widgetText, widgetIDList=$widgetIDList"
        }
        Log.e(TAG, "doAction: $logActionInfo")
        if (serviceBridge.pickService() == null) {
            Log.d(TAG, "doAction: accessibility service is null")
            sendDoStepMessageDelay()
            return
        }
        //是否搜索，搜索需要多次，单独提出来
        if (checkHasSearch(action)) {
            val tempList = mutableListOf<Action>()
            tempList.addAll(currentActionList.subList(0, 2))
            handleList.add(Status(STEP_MAIN, tempList))
            currentActionList.removeAt(0)
            currentActionList.removeAt(0)
            stepStatus = STEP_SEARCH
            sendDoStepSearchMessage()
            return
        }
        //是否有previous，单独提出来
        if (!action.actionPrevious.isNullOrEmpty()) {
            Log.d(TAG, "doAction: actionPrevious")
            val tempList = mutableListOf<Action>()
            tempList.addAll(action.actionPrevious ?: emptyList())
            action.actionPrevious = null
            handleList.add(Status(STEP_MAIN, tempList))
            stepStatus = STEP_PREVIOUS
            sendDoStepPreviousMessage()
            return
        }
        delay(action.waitTime())
        if (checkQuery(action)) return
        executeAction(action)
    }

    private suspend fun executeAction(action: Action) {
        val nodeList = uiSelector.find(action)
        if (nodeList.isNullOrEmpty()) {
            Log.e(TAG, "doAction: nodeInfoList is null")
            //这里经常发生，例如打开一个页面慢,但是也有可能跳错页面
            if (action.skipTime > 0) {
                clearCurrentStep()
                successToNext(action)
            } else {
                //找不到控件的时候，不瞎等，尝试滑动屏幕再找控件
                scrollScreen(action)
                checkTimeOut(action, STEP_MAIN)
            }
            return
        }
        clearCurrentStep()
        Log.d(TAG, "executeAction: nodeList.size=${nodeList.size}")
        nodeList.forEachIndexed { i, node ->
            Log.d(TAG, "executeAction: nodeList[$i]={ $node }")
        }
        val node = if (nodeList.size == 1) {
            //只有1个的场景
            checkForSingleNode(action, nodeList[0])
        } else {
            //有2个及以上场景（搜索页面等）:因为有可能通过文本去匹配会匹配出多个节点
            checkForMultiNodes(action, nodeList)
        }
        Log.e(TAG, "executeAction: deal with node: $node")
        val result = UiObject(this).dealWith(node, action)
        dealWithResult(result, action)
    }

    private fun checkForSingleNode(
        action: Action,
        node: AccessibilityNodeInfo
    ): AccessibilityNodeInfo? {
        if (action.run { widgetID.isNullOrEmpty() && widgetIDList.isNullOrEmpty() }) {
            if (matchNoWidgetIdActionNode(action, node)) {
                return node
            }
        } else {
            if (matchWidgetIdActionNode(action, node)) {
                return node
            }
        }
        return null
    }

    private fun checkForMultiNodes(
        action: Action,
        nodeList: List<AccessibilityNodeInfo>
    ): AccessibilityNodeInfo? {
        if (action.run { widgetID.isNullOrEmpty() && widgetIDList.isNullOrEmpty() }) {
            for (item in nodeList) {
                if (matchNoWidgetIdActionNode(action, item)) {
                    return item
                }
            }
        } else {
            for (item in nodeList) {
                if (matchWidgetIdActionNode(action, item)) {
                    return item
                }
            }
        }
        return null
    }

    private fun matchNoWidgetIdActionNode(action: Action, node: AccessibilityNodeInfo): Boolean {
        // 部分情况下(onlyFindNoIdWidgetByTxt == false)，对有id的控件也需要仅按text搜索
        if (action.onlyFindNoIdWidgetByTxt && !node.viewIdResourceName.isNullOrEmpty()) {
            return false
        }
        if (!matchWidgetActions(action, node)) {
            return false
        }
        if (action.scrollableParentWidgetType.isNullOrEmpty()) {
            return true
        }
        if (isAnchor(action.scrollableParentWidgetType, node)) {
            return true
        }
        return false
    }

    private fun matchWidgetIdActionNode(action: Action, node: AccessibilityNodeInfo): Boolean {
        val nodeViewId = node.viewIdResourceName
        if (nodeViewId.isNullOrEmpty()) {
            return false
        }
        if (!matchWidgetActions(action, node)) {
            return false
        }
        if (action.widgetID == nodeViewId) {
            return true
        }
        if (action.widgetIDList?.contains(nodeViewId) == true) {
            return true
        }
        return false
    }

    private fun matchWidgetActions(action: Action, node: AccessibilityNodeInfo): Boolean {
        val containsActions =
            action.widgetContainsActions.takeUnless { it.isNullOrEmpty() } ?: return true
        return node.actionList?.containsAll(containsActions) ?: false
    }

    private fun isAnchor(
        scrollableParentWidgetType: String?,
        nodeInfo: AccessibilityNodeInfo
    ): Boolean {
        if (scrollableParentWidgetType.isNullOrEmpty()) {
            return false
        }
        if (nodeInfo.className == scrollableParentWidgetType) {
            return true
        }
        if (nodeInfo.parent == null) {
            return false
        }
        return isAnchor(scrollableParentWidgetType, nodeInfo.parent)
    }

    private suspend fun scrollScreen(action: Action) {
        val scrollableParentNode = uiSelector.findScrollableParentNode(action)
        UiObject(this).scrollParentNode(action, scrollableParentNode)
    }

    private fun dealWithResult(result: Boolean, action: Action) {
        if (result) {
            successToNext(action)
            return
        }
        Log.d(TAG, "doAction: action fail")
        if (action.skipTime > 0) {
            successToNext(action)
        } else {
            checkTimeOut(action, STEP_MAIN)
        }
    }

    private fun successToNext(action: Action) {
        currentActionList.remove(action)
        Log.d(TAG, "doAction: currentActionList.size=${currentActionList.size}")
        if (currentActionList.isNotEmpty()) {
            val next = currentActionList[0]
            val waitTime = next.waitTime()
            Log.d(TAG, "doAction: waitTime=$waitTime")
            sendDoStepMessage(waitTime)
            return
        }
        currentActionList = mutableListOf()
        Log.d(TAG, "doAction: step=$leftStepList")
        if (leftStepList.isNotEmpty()) {
            Log.d(TAG, "doAction: leftStepList.size=${leftStepList.size}")
            sendDoStepMessage()
        } else {
            Log.d(TAG, "doAction: step success")
            leftStepList = mutableListOf()
        }
    }

    private fun clearCurrentStep() {
        Log.d(TAG, "currentStep=$currentStep")
        if (currentStep != null) {
            val step = currentStep
            leftStepList.remove(step)
            currentStep = null
        }
    }

    private fun checkTimeOut(action: Action, step: Int) {
        if (action.failTime <= 0) {
            return
        }
        action.failTime -= stepDelayTime
        sendStatusMessage(step, stepDelayTime)
    }

    /**
     * 检查是否有搜索的action
     */
    private fun checkHasSearch(action: Action): Boolean {
        if (action.actionInput.isNullOrEmpty() || action.inputMethod.isNullOrEmpty()) {
            return false
        }
        val checkAction = currentActionList.elementAtOrNull(1) ?: return false
        val hasWidgetId =
            checkAction.run { !widgetID.isNullOrEmpty() || !widgetIDList.isNullOrEmpty() }
        val isWidgetNotEmpty =
            checkAction.run { hasWidgetId && !widgetText.isNullOrEmpty() }
        val isChildWidgetNotEmpty =
            checkAction.run { !childWidgetID.isNullOrEmpty() && !childWidgetText.isNullOrEmpty() }
        if (isWidgetNotEmpty || isChildWidgetNotEmpty) {
            //目前的action是搜索，下个action是否是搜索出来正确的内容，正确会点击。需要把当前action保存起来
            Log.d(TAG, "doAction: checkHasSearch")
            return true
        }
        return false
    }

    fun nextAction(action: Action): Action? {
        if (handleList.isNotEmpty()) {
            val index = handleList.last().actionList.indexOf(action)
            if (index == -1) {
                return null
            }
            return handleList.last().actionList.elementAtOrNull(index + 1)
        }
        val index = currentActionList.indexOf(action)
        if (index == -1) {
            return null
        }
        return currentActionList.elementAtOrNull(index + 1)
    }

    private companion object {
        private const val TAG = "SimulateClickEngine"
        private const val STEP_MAIN = 11
        private const val STEP_SEARCH = 12
        private const val STEP_PREVIOUS = 13

        /**
         * 搜索结果要执行previous
         */
        private const val SEARCH_RESULT_PREVIOUS = 1

        /**
         * 搜索结果选择选错或是返回
         */
        private const val SEARCH_RESULT_OTHER_PAGE = 2
    }
}