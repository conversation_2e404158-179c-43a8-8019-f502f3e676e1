/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DownloadSuccessNotificationInfo
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/18 11:25
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/18       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.download.notification

import androidx.annotation.Keep

/**
 *  * [DownloadSuccessNotificationInfo] bind the target path to NotificationId,
 *  * update the content of the same NotificationId when the path is the same.
 *  * @param notificationId    The NotificationId.
 *  * @param path              The Specified Path.
 *  * @param currPath          The display path title.
 *  * @param successCount      The number of successful download under the same target path.
 */
@Keep
data class DownloadSuccessNotificationInfo(
    var notificationId: Int,
    var path: String,
    var currName: String,
    var successCount: Int
) {
    override fun toString(): String {
        return "DownloadSuccessNotificationInfo(" +
                "notificationId=$notificationId, " +
                "currName='$currName', " +
                "successCount=$successCount" +
                ")"
    }
}