/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.utils
 * * Version     : 1.0
 * * Date        : 2020/3/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.app.Activity
import android.content.pm.ActivityInfo
import android.graphics.Point
import android.graphics.Rect
import android.util.DisplayMetrics
import android.view.View
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.AnimationSet
import android.view.animation.TranslateAnimation
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.google.android.material.appbar.AppBarLayout
import androidx.core.view.isGone
import androidx.core.view.isInvisible

object KtViewUtils {
    private const val TAG = "KtViewUtils"
    private const val ANIMATION_BASE_TIME = 200
    private const val NAVIGATION_BAR_HEIGHT_ID = "navigation_bar_height"
    private const val DEF_TYPE_DIMEN = "dimen"
    private const val DEF_PACKAGE_ANDROID = "android"

    fun hiddenAnimTopToBottomWhitFade(view: View) {
        if (view.animation?.hasStarted() == true) {
            view.animation.cancel()
        }
        val animationSet = AnimationSet(true)
        val hiddenAction = TranslateAnimation(Animation.RELATIVE_TO_SELF,
            0.0f, Animation.RELATIVE_TO_SELF, 0.0f,
            Animation.RELATIVE_TO_SELF, 0.0f, Animation.RELATIVE_TO_SELF,
            1.0f)
        animationSet.addAnimation(hiddenAction)
        animationSet.addAnimation(AlphaAnimation(1.0f, 0.0f))
        animationSet.duration = ANIMATION_BASE_TIME.toLong()
        animationSet.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {
                view.visibility = View.VISIBLE
            }

            override fun onAnimationEnd(animation: Animation) {
                view.visibility = View.GONE
            }

            override fun onAnimationRepeat(animation: Animation) {

            }
        })
        view.startAnimation(animationSet)
    }

    fun showAnimBottomToUpWithFade(view: View) {
        if (view.animation?.hasStarted() == true) {
            view.animation.cancel()
        }
        val animationSet = AnimationSet(true)
        val showAction = TranslateAnimation(Animation.RELATIVE_TO_SELF, 0.0f,
            Animation.RELATIVE_TO_SELF, 0.0f, Animation.RELATIVE_TO_SELF,
            1.0f, Animation.RELATIVE_TO_SELF, 0.0f)
        animationSet.addAnimation(showAction)
        animationSet.addAnimation(AlphaAnimation(0.0f, 1.0f))
        animationSet.duration = ANIMATION_BASE_TIME.toLong()
        animationSet.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {
                view.visibility = View.VISIBLE
            }

            override fun onAnimationEnd(animation: Animation) {

            }

            override fun onAnimationRepeat(animation: Animation) {

            }
        })
        view.startAnimation(animationSet)
    }

    /**
     * Calculate the grid item width.
     *
     * @param activity
     * @param itemSpace The interval between two items
     * @param itemCount The grid item count in each line
     * @param leftRightMargin The distance of the first item from the left and the distance of the last item from the right in each line
     * @param totalWidth the max width of the parent view, if set to 0, the window width will be used
     *
     * @return the width of each grid item
     */
    fun getGridItemWidth(activity: Activity?, itemSpace: Int, itemCount: Int, leftRightMargin: Int, totalWidth: Int = 0): Int {
        if (itemCount <= 0) {
            Log.d(TAG, "getGridItemWidth error: item count=$itemCount")
            return 0
        }
        var width = totalWidth
        if (width <= 0) {
            width = getWindowSize(activity).x
        }
        Log.d(TAG, "getGridItemWidth width=$width, $itemCount  itemSpace=$itemSpace  margin=$leftRightMargin")
        return if(width <= 0) {
            0
        } else {
            (width - leftRightMargin - itemSpace * (itemCount - 1)) / itemCount
        }
    }

    fun getWindowSize(activity: Activity?): Point {
        val point = Point()
        activity?.apply {
            if (SdkUtils.isAtLeastR()) {
                windowManager.currentWindowMetrics.bounds.let {
                    point.x = it.width()
                    point.y = it.height()
                }
            } else {
                windowManager.defaultDisplay.let {
                    point.x = it.width
                    point.y = it.height
                }
            }
            removeSoftNavigationBarSizeIfNeed(this, point)
        }
        return point
    }

    // landscape, soft navigation bar will show vertically align right or left on screen in some situation,
    // we should remove soft navigation bar size
    @JvmStatic
    private fun removeSoftNavigationBarSizeIfNeed(
        activity: Activity,
        point: Point
    ) {
        activity.window.decorView.let {
            val inset = ViewCompat.getRootWindowInsets(it.rootView)
                ?.getInsets(WindowInsetsCompat.Type.navigationBars())
            inset?.let {
                //当导航栏及应用同在左侧时，left的值是导航栏宽度需要减去,反之right是导航栏的宽度。
                //当导航栏与应用不在同一侧时，不需要计算导航栏高度，right=left=0
                Log.d(TAG, "removeSoftNavigationBarSizeIfNeed  $it")
                point.x = point.x - inset.right - inset.left
            }
        }
    }

    fun getScreenDensity(activity: Activity?): Float {
        return activity?.let {
            it.resources.configuration.densityDpi / DisplayMetrics.DENSITY_MEDIUM.toFloat()
        } ?: 0F
    }

    /**
     * get RecyclerView padding bottom when Navigation and selector tip shown
     */
    @JvmStatic
    fun getSelectModelPaddingBottom(recyclerView: RecyclerView, bottomView: View?): Int =
        if (recyclerView.childCount > 0) {
            val normalPaddingBottom =
                MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.ftp_text_margin_bottom)
            var bottomViewHeight = bottomView?.measuredHeight ?: 0
            if (bottomViewHeight <= 0) {
                bottomViewHeight = normalPaddingBottom * 2
            }
            normalPaddingBottom + bottomViewHeight
        } else {
            MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_list_bottom_padding)
        }

    fun getRecyclerViewTopPadding(appBarLayout: AppBarLayout?, paddingTopMulti: Int = 1): Int {
        return (appBarLayout?.measuredHeight ?: 0) + getRecyclerViewTopOffset(paddingTopMulti)
    }

    @JvmStatic
    fun getRecyclerViewTopOffset(paddingTopMulti: Int = 1): Int {
        return MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.sort_list_padding_top) * paddingTopMulti
    }

    /**
     * 修改UI屏幕方向
     * 手机：保持坚屏，其他应用横屏通过浮窗打开，因ScreenOrientation=behind,所以需要在此手机更改屏幕方向，否则会横屏显示
     * 孔雀屏及平板：支持横屏，可旋转
     */
    @JvmStatic
    fun updateUIOrientation(activity: Activity, outerOrientation: Int? = null) {
        val isTablet = ModelUtils.isTablet()
        val orientation = outerOrientation
            ?: if (UIConfigMonitor.instance.isScreenFold() && WindowUtils.isSmallScreen(activity) && isTablet.not()) {
                Log.d(TAG, "updateUIOrientation SCREEN_ORIENTATION_PORTRAIT")
                ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            } else {
                Log.d(TAG, "updateUIOrientation SCREEN_ORIENTATION_USER")
                ActivityInfo.SCREEN_ORIENTATION_USER
            }
        if (!UIConfigMonitor.isZoomWindowShow() && !activity.isInMultiWindowMode &&
            activity.requestedOrientation != orientation) {
            activity.requestedOrientation = orientation
        }
        if (UIConfigMonitor.instance.isFoldable(activity) && activity.isInMultiWindowMode &&
            activity.requestedOrientation != orientation) {
            activity.requestedOrientation = orientation
        }
        if (!isTablet && UIConfigMonitor.instance.isScreenFold() && activity.isInMultiWindowMode &&
            activity.requestedOrientation != orientation) {
            activity.requestedOrientation = orientation
        }
        if (UIConfigMonitor.isZoomWindowShow()) {
            activity.requestedOrientation = orientation
        }
    }

    @JvmStatic
    fun resetTabletUIOrientation(activity: Activity) {
        val isTablet = ModelUtils.isTablet()
        if (isTablet) {
            Log.d(TAG, "resetTabletUIOrientation")
            activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_USER
        }
    }

    @JvmStatic
    fun getSoftNavigationBarSize(activity: Activity): Int {
        val resourceId: Int = activity.resources.getIdentifier(NAVIGATION_BAR_HEIGHT_ID, DEF_TYPE_DIMEN, DEF_PACKAGE_ANDROID)
        return if (resourceId > 0) activity.resources.getDimensionPixelSize(resourceId) else 0
    }

    @JvmStatic
    fun getBorderSize(): Float {
        return MyApplication.sAppContext.resources.getDimension(R.dimen.divider_background_height)
    }

    @JvmStatic
    fun getDialogStyle(): Int {
        return R.style.fop_COUIAlertDialog_SingleEdit
    }

    @JvmStatic
    fun isViewVisible(view: View): Boolean {
        if (view.isGone || view.isInvisible) {
            return false
        }
        if (view.width <= 0 || view.height <= 0) {
            return false
        }
        val parent = view.parent as? View ?: return false
        val parentVisibleRect = Rect()
        parent.getLocalVisibleRect(parentVisibleRect)
        // view 在 parent 上方
        if (view.bottom < parent.paddingTop) {
            return false
        }
        // view 在parent 下方
        if (view.top > parentVisibleRect.bottom - parent.paddingBottom) {
            return false
        }
        return true
    }
}