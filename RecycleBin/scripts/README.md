# 文管回收站数据库导出API

通过adb模拟侧边栏接入文管回收站后的行为来测试回收站的导出API。  
导出API的相关代码为ExportedRecycleProvider，相关导出API如下。  

## 1. 获取回收站的被删除文件暂存目录：  
文管提供回收站的被删除文件的暂存目录路径，侧边栏根据路径读取后续需要的文件信息及在必要时创建目录。  

+ 调用ContentProvider方法：call
+ URI：content://com.oplus.filemanager.recyclebin.db.exported.recycleprovider
+ Method：getRecycleDirectory
+ Bundle返回值：
  - resultRecycleDir：String类型，返回的文管回收站的被删除文件的暂存目录路径


## 2. 生成回收站数据记录的ContentValues：  
侧边栏提供被删除文件/文件夹的必要文件信息，让文管生成用于插入数据库的数据记录ContentValues。  
该ContentValues会一并提供被删除文件在回收站暂存区的路径(含文件命名)  

+ 调用ContentProvider方法：call
+ URI：content://com.oplus.filemanager.recyclebin.db.exported.recycleprovider
+ Method：obtainRecycleContentValues
+ Extras参数：
  - originPath：String类型，完整的被删除文件/文件夹的原始文件路径；
  - isFile：bool类型，被删除的是文件还是文件夹，对应代码里的File.isFile()的值；
  - fileSize：long类型，被删除的文件大小(字节数)，被删除的为文件夹则不传该参数或传-1；
  - lastModified：long类型，被删除文件/目录的最后编辑时间，对应代码里的File.lastModified()；
  - existsRecycledFileNames：String数组类型，文管回收站的被删除文件的暂存目录路径下，已存在文件/目录的名称列表。该项主要用于文件重名场景的规避逻辑，可以通过contains匹配被删除文件/目录的文件名来筛选一次以削减数量；
+ Bundle返回值：
  - resultContentValues：android.content.ContentValues类型，用于后续插入文管回收站数据库的被删除文件数据记录，其中包含了被删除文件应当被移动到的目标路径；
    * recycle_path：String类型，返回的ContentValues中，被删除文件需要被移动到的目标路径(含被移动后的文件命名)，侧边栏在删除文件时，根据此信息来移动文件即可(父目录不存在时需一并先创建出父目录)


## 3. 插入回收站数据记录至回收站数据库：
侧边栏在完成被删除文件的移动到暂存路径下的操作后，将之前生成的回收站数据记录插入到回收站数据库中，完成整个删除文件至文管回收站的操作。  

+ 调用ContentProvider方法：insert
+ URI：content://com.oplus.filemanager.recyclebin.db.exported.recycleprovider/internal/file
+ values：由obtainRecycleContentValues方法生成的ContentValues