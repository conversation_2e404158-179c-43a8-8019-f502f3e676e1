/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentCardControl
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/13 14:31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/13       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent

import android.content.Context
import android.os.Bundle
import android.provider.Settings
import com.filemanager.common.R
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.controller.PersonalizedServiceController
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.stringResource
import com.oplus.filemanager.cardwidget.label.labelCardScope
import com.oplus.filemanager.cardwidget.recent.controller.RecentCardDataController
import com.oplus.filemanager.cardwidget.recent.data.PAGE_DATA
import com.oplus.filemanager.cardwidget.recent.data.PAGE_NOT_AGREE_PRIVACY
import com.oplus.filemanager.cardwidget.recent.data.PAGE_NO_FILES
import com.oplus.filemanager.cardwidget.recent.data.PAGE_NO_PERMISSION
import com.oplus.filemanager.cardwidget.recent.data.RecentCard
import com.oplus.filemanager.cardwidget.recent.usecase.GetAllCategoryItemsUseCase
import com.oplus.filemanager.cardwidget.recent.utils.RecentCardUtils
import com.oplus.filemanager.cardwidget.recent.utils.generateCardId
import com.oplus.pantanal.seedling.bean.SeedlingCard
import com.oplus.pantanal.seedling.bean.SeedlingCardSizeEnum
import com.oplus.pantanal.seedling.update.SeedlingCardOptions
import com.oplus.pantanal.seedling.util.SeedlingTool
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject

class RecentCardControl : IRecentCardControl {

    private val personalizedServiceController: PersonalizedServiceController by lazy { PersonalizedServiceController() }

    override fun cardCreate(context: Context, card: SeedlingCard) {
        // 判断是否拖拽触发卡片替换
        val oldCardId = Settings.Secure.getString(context.contentResolver, "${card.generateCardId()}&1")
        Log.d(TAG, "cardCreate -> cid=${card.cardId} cindex=${card.cardIndex} catdataid=${card.generateCardId()} oldCard=$oldCardId")
    }

    override fun cardObserve(context: Context, cards: List<SeedlingCard>) {
        Log.d(TAG, "cardObserve -> cards = $cards")
        cards.forEach { card ->
            val oldCardId = Settings.Secure.getString(context.contentResolver, "${card.generateCardId()}&1")
            Log.d(TAG, "cardObserve -> cid=${card.cardId} cindex=${card.cardIndex} catdataid=${card.generateCardId()} oldCard=$oldCardId")
        }
    }

    override fun destroy(context: Context, card: SeedlingCard) {
        Log.d(TAG, "destroy -> card = $card")
    }

    override fun hide(context: Context, card: SeedlingCard) {
        Log.d(TAG, "hide -> card = $card")
    }

    override fun show(context: Context, card: SeedlingCard) {
        Log.d(TAG, "show -> card = $card")
        refreshCardData(context, card)
    }

    override fun subscribed(context: Context, card: SeedlingCard) {
        Log.d(TAG, "subscribed -> card = $card")
        val cardSize = card.size.sizeCode
        statisticsSubscribed(cardSize, context)
        RecentCardUtils.instance.getCacheLists().forEach { cache ->
            if (cache.cardId == card.generateCardId()) {
                return
            }
        }
        val cardData = RecentCard(card.generateCardId(), cardSize = cardSize)
        Log.d(TAG, "subscribed -> cardData = $cardData")
        // 判断是否拖拽触发卡片替换
        val oldCardId = Settings.Secure.getString(context.contentResolver, "${cardData.cardId}&1")
        if (oldCardId.isNullOrEmpty().not()) {
            Log.d(TAG, "oldCard=$oldCardId")
            if (oldCardId.length > CARD_MIN_LENGTH) {
                RecentCardUtils.instance.getCacheCardDataByCardId(oldCardId.substring(0, oldCardId.length - CARD_MIN_LENGTH))
                    ?.also {
                        cardData.type = it.type
                        Log.d(TAG, "oldCard=$oldCardId type=${it.type}")
                    }
            }
        }
        RecentCardUtils.instance.saveCard(cardData)
    }

    override fun unSubscribed(context: Context, card: SeedlingCard) {
        Log.d(TAG, "unSubscribed -> card = $card")
        RecentCardUtils.instance.deleteCardById(card.generateCardId())
    }

    override fun updateData(context: Context, card: SeedlingCard, data: Bundle) {
        Log.d(TAG, "updateData -> card = $card ; data = $data")
        refreshCardData(context, card)
    }

    private fun refreshCardData(context: Context, card: SeedlingCard) {
        Log.d(TAG, "refreshCardData -> card = $card")
        val cardId = card.generateCardId()
        val cache = RecentCardUtils.instance.getCacheCardDataByCardId(context, cardId)
        if (cache == null) {
            Log.d(TAG, "refreshCardData -> cache data is null, save initial data.")
            val data = RecentCard(cardId, card.size.sizeCode)
            RecentCardUtils.instance.saveCard(data)
        }
        // check agree privacy
        val hasAgreePrivacy = PrivacyPolicyController.hasAgreePrivacy(context)
        var isNeedAgreeAdPersonalService = false
        if (FeatureCompat.sIsExpRom) {
            isNeedAgreeAdPersonalService = personalizedServiceController.isNeedAgreeAdPersonalService(context)
        }
        Log.d(TAG, "refreshCard -> hasAgreePrivacy = $hasAgreePrivacy, isNeedAgreeAdPersonalService = $isNeedAgreeAdPersonalService")
        if (!hasAgreePrivacy || isNeedAgreeAdPersonalService) {
            updateNoAgreePrivacy(card)
            return
        }

        // check storage permission
        val isPermissionGranted = PermissionUtils.hasStoragePermission(context)
        Log.d(TAG, "refreshCard -> permissionGranted = $isPermissionGranted")
        if (!isPermissionGranted) {
            updateNoStoragePermission(card)
            return
        }

        // check label init state
        Log.d(TAG, "refreshCard -> cache = $cache")
        if (cache == null) {
            Log.d(TAG, "refreshCard -> cache is null")
            return
        }

        labelCardScope.launch(Dispatchers.IO) {
            val size = cache.getSize()
            val categoryItem = GetAllCategoryItemsUseCase().invoke()
            val controller = RecentCardDataController(size)
            val result = controller.loadData(cache, categoryItem)
            Log.d(TAG, "refreshCardData -> loadData is $result")
            if (result.first) {
                updateEmptyData(card, result.second)
            } else {
                updateRecentCardData(card, result.second)
            }
        }
    }

    private fun updateNoAgreePrivacy(card: SeedlingCard) {
        val data = JSONObject().apply {
            val tips = stringResource(R.string.card_agree_privacy)
            val btnText = stringResource(R.string.card_view)
            put(KEY_TIPS, tips)
            put(KEY_BTN_TEXT, btnText)
        }
        val options = SeedlingCardOptions().apply {
            pageId = PAGE_NOT_AGREE_PRIVACY
        }
        SeedlingTool.updateData(card, data, options)
    }

    private fun updateNoStoragePermission(card: SeedlingCard) {
        val data = JSONObject().apply {
            val tips = stringResource(R.string.manage_files_permission_title)
            val btnText = stringResource(R.string.set_button_text)
            put(KEY_TIPS, tips)
            put(KEY_BTN_TEXT, btnText)
        }
        val options = SeedlingCardOptions().apply {
            pageId = PAGE_NO_PERMISSION
        }
        SeedlingTool.updateData(card, data, options)
    }

    private fun updateEmptyData(card: SeedlingCard, jsonObject: JSONObject) {
        val tips = stringResource(R.string.empty_file)
        val summary = stringResource(R.string.display_file_in_recent_thirty_days)
        jsonObject.put(KEY_TIPS, tips)
        jsonObject.put(KEY_SUMMARY, summary)
        val options = SeedlingCardOptions().apply {
            pageId = PAGE_NO_FILES
        }
        SeedlingTool.updateData(card, jsonObject, options)
    }

    private fun updateRecentCardData(card: SeedlingCard, jsonObject: JSONObject) {
        Log.d(TAG, "updateRecentCardData -> jsonObject = $jsonObject")
        val options = SeedlingCardOptions().apply {
            pageId = PAGE_DATA
        }
        SeedlingTool.updateData(card, jsonObject, options)
    }

    private fun statisticsSubscribed(cardSize: Int, context: Context) {
        val eventId = if (cardSize == SeedlingCardSizeEnum.FourXFour.sizeCode) {
            StatisticsUtils.RECENT_FILE_BIG_CARD_ADD
        } else {
            StatisticsUtils.RECENT_FILE_MID_CARD_ADD
        }
        StatisticsUtils.onCommon(context, eventId)
    }

    companion object {
        private const val TAG = "RecentCardController"
        const val KEY_TIPS = "tips"
        const val KEY_SUMMARY = "summary"
        const val KEY_BTN_TEXT = "btnText"
        const val CARD_MIN_LENGTH = 2
    }
}