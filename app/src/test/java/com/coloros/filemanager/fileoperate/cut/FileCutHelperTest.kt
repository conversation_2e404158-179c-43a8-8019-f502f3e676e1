/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileCutHelperTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/15
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/15       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.fileoperate.cut

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.SdkUtils
import com.filemanager.fileoperate.cut.FileCutHelper
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert.assertTrue
import org.junit.Assert.assertFalse
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import java.io.File

class FileCutHelperTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
    }

    @Test
    fun testCutFile() {
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(SdkUtils::class)
        mockkStatic(JavaFileHelper::class)
        every { VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext) } returns INTERNAL_SD_PATH
        val directory = mockk<File>()
        val destFile = mockk<File>()
        // cut directory and listFiles is null and destFile is exists
        every { directory.isDirectory } returns true
        every { directory.absolutePath } returns IMG_FILE_ABSOLUTE_PATH
        every { destFile.isDirectory } returns false
        every { destFile.exists() } returns true
        every { JavaFileHelper.listFiles(directory) } returns directoryListFiles.toList()
        every { directory.delete() } returns true
        assertTrue(FileCutHelper.cutFile(directory, destFile) { true })

        // cut directory and listFiles is not null and destFile is not exists.
        every { destFile.exists() } returns false
        every { destFile.name } returns "dest"
        every { directory.renameTo(any()) } returns true
        // sourceFile exists throws exception
        assertFalse(FileCutHelper.cutFile(directory, destFile) { true })

        // sourceFile exists return true
        every { directory.exists() } returns true
        assertTrue(FileCutHelper.cutFile(directory, destFile) { true })

        // cut directory and listFiles is not null and destFile is not exists.
        // renameTo return true
        every { directory.renameTo(any()) } returns false
        assertFalse(FileCutHelper.cutFile(directory, destFile) { true })

        // cut directory and listFiles is not null and destFile is exists
        every { destFile.exists() } returns true
        every { destFile.delete() } returns true
        every { destFile.name } returns IMG_FILE_NAME

        // file is directory and destFile is not exist and source file is not exist
        every { directory.exists() } returns false
        assertTrue(FileCutHelper.cutFile(directory, destFile) { true })

        // sourceFile is not directory and destFile is exists
        val sourceFile = mockk<File>()
        every { sourceFile.isDirectory } returns false
        every { sourceFile.name } returns sourceFileName
        every { sourceFile.exists() } returns true
        every { sourceFile.renameTo(any()) } returns true
        every { sourceFile.absolutePath } returns IMG_FILE_ABSOLUTE_PATH
        every { destFile.exists() } returns true
        every { destFile.delete() } returns true
        every { destFile.absolutePath } returns IMG_FILE_ABSOLUTE_PATH
        assertTrue(FileCutHelper.cutFile(sourceFile, destFile) { true })

        // sourceFile is not directory and destFile is not exists
        every { destFile.exists() } returns false
        assertTrue(FileCutHelper.cutFile(sourceFile, destFile) { true })
        unmockkStatic(JavaFileHelper::class)
    }

    @Test
    fun testIdDirectoriesSameDisk() {
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(SdkUtils::class)
        every { VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext) } returns INTERNAL_SD_PATH
        every { VolumeEnvironment.getVolumePathList() } returns null
        assertTrue(FileCutHelper.isDirectoriesSameDisk(source, target))

        volumePath.plus(File.separator)
        volumePathList.add(volumePath)
        every { VolumeEnvironment.getVolumePathList() } returns volumePathList
        every { SdkUtils.isAtLeastR() } returns false
        // VolumePathList is not empty and volume path is endsWith File.separator

        // target is null
        assertTrue(FileCutHelper.isDirectoriesSameDisk(source, target))

        // target is not null but not startWith volume
        target = "/Android/Data"
        assertTrue(FileCutHelper.isDirectoriesSameDisk(source, target))

        // target is not null and startWith volume but source data is null
        target = volumePath + File.separator + ANDROID_OBB
        val sourceData = BaseFileBean()
        sourceData.mData = ""
        source.add(sourceData)
        assertFalse(FileCutHelper.isDirectoriesSameDisk(source, target))

        // target is not null and startWith volume but source data is not null but not startWith volume
        sourceData.mData = "/Android/Data"
        source.clear()
        source.add(sourceData)
        assertFalse(FileCutHelper.isDirectoriesSameDisk(source, target))

        // target is not null and startWith volume and source data is same
        sourceData.mData = "data/data/target"
        source.clear()
        source.add(sourceData)
        assertFalse(FileCutHelper.isDirectoriesSameDisk(source, target))

        // target is not null and startWith volume and source data is same but volume is different
        sourceData.mData = "data/data" + File.separator + ANDROID_OBB
        source.clear()
        source.add(sourceData)
        assertTrue(FileCutHelper.isDirectoriesSameDisk(source, target))

        every { SdkUtils.isAtLeastR() } returns false
        assertTrue(FileCutHelper.isDirectoriesSameDisk(source, target))
    }

    @Test
    fun testCheckSizeAndInternalStorageImage() {
        mockkStatic(JavaFileHelper::class)
        mockkStatic(VolumeEnvironment::class)
        mockkObject(MimeTypeHelper.Companion)
        val file = mockk<File>()
        every { VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext) } returns INTERNAL_SD_PATH
        // image file and absolute path start with InternalPath
        every { file.length() } returns 100L
        every { file.isDirectory } returns false
        every { file.name } returns IMG_FILE_NAME
        every { file.absolutePath } returns IMG_FILE_ABSOLUTE_PATH
        every { MimeTypeHelper.getTypeFromPath(any()) } returns MimeTypeHelper.IMAGE_TYPE

        val imgFilePair = Pair(100L, true)
        assertEquals(imgFilePair, FileCutHelper.checkSizeAndInternalStorageImage(file))

        // video file and absolute path start with InternalPath
        every { file.name } returns VIDEO_FILE_NAME
        every { MimeTypeHelper.getTypeFromPath(any()) } returns MimeTypeHelper.VIDEO_TYPE
        val videoFilePair = Pair(100L, false)
        assertEquals(videoFilePair, FileCutHelper.checkSizeAndInternalStorageImage(file))

        // video file and absolute path not start with InternalPath
        every { file.absolutePath } returns FILE_ABSOLUTE_PATH_NOT_START_WITH_INTERNAL
        val videoNotStartWithInternalFilePair = Pair(100L, false)
        assertEquals(
            videoNotStartWithInternalFilePair,
            FileCutHelper.checkSizeAndInternalStorageImage(file)
        )

        // image file and absolute path not start with InternalPath
        every { file.name } returns IMG_FILE_NAME
        every { MimeTypeHelper.getTypeFromPath(any()) } returns MimeTypeHelper.IMAGE_TYPE
        val imgFileNotStartWithInternalFilePair = Pair(100L, false)
        assertEquals(
            imgFileNotStartWithInternalFilePair,
            FileCutHelper.checkSizeAndInternalStorageImage(file)
        )

        // directory and listFiles is null
        val directory = mockk<File>()
        every { directory.length() } returns 100L
        every { directory.isDirectory } returns true
        every { JavaFileHelper.listFiles(directory) } returns null
        val emptyListFilesDirectory = Pair(100L, false)
        assertEquals(
            emptyListFilesDirectory,
            FileCutHelper.checkSizeAndInternalStorageImage(directory)
        )

        // directory and listFiles is imgFile
        every { file.name } returns IMG_FILE_NAME
        every { file.absolutePath } returns IMG_FILE_ABSOLUTE_PATH
        every { MimeTypeHelper.getTypeFromPath(any()) } returns MimeTypeHelper.IMAGE_TYPE
        val imgFileList = arrayOf(file).toList()
        every { JavaFileHelper.listFiles(directory) } returns imgFileList
        val imgFileDirectory = Pair(200L, true)
        assertEquals(
            imgFileDirectory,
            FileCutHelper.checkSizeAndInternalStorageImage(directory)
        )
        unmockkStatic(JavaFileHelper::class)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    companion object {
        private val directoryListFiles = arrayOf<File>()
        private const val sourceFileName = "test"

        private val source = mutableListOf<BaseFileBean>()
        private var target = ""
        private val volumePathList = arrayListOf<String>()
        private const val volumePath = "data/data"

        private const val ANDROID_OBB = "Android/obb"

        private const val IMG_FILE_NAME = "test.jpg"
        private const val VIDEO_FILE_NAME = "test.mp4"
        private const val IMG_FILE_ABSOLUTE_PATH = "/test/InternalSdPath/file"
        private const val FILE_ABSOLUTE_PATH_NOT_START_WITH_INTERNAL = "/InternalSdPath/file"
        private const val INTERNAL_SD_PATH = "/test/InternalSdPath"
    }
}