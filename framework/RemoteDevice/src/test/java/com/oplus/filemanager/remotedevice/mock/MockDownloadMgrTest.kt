package com.oplus.filemanager.remotedevice.mock

import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.remotedevice.DownloadRemoteFileCallback
import com.oplus.filemanager.interfaze.remotedevice.OperateCallback
import com.oplus.filemanager.remotedevice.ResultConstant
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.Before
import org.junit.After
import org.junit.Test
import org.junit.Assert.assertEquals
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.test.assertFailsWith
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.delay

/**
 * MockDownloadMgr的单元测试类
 * 用于测试模拟下载管理器的各种功能
 */
class MockDownloadMgrTest {

    // 使用MockK框架模拟下载回调接口
    @MockK
    private lateinit var downloadCallback: DownloadRemoteFileCallback

    // 使用MockK框架模拟操作回调接口
    @MockK
    private lateinit var operateCallback: OperateCallback

    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 模拟Log类的静态方法
     * 3. 初始化MockDownloadMgr状态
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkStatic(Log::class)
        // 模拟Log.e和Log.d方法不做任何操作
        every { Log.e(any<String>(), any<String>()) } just Runs
        every { Log.d(any<String>(), any<String>()) } just Runs
        // 模拟操作回调的onResult方法不做任何操作
        every { operateCallback.onResult(any(), any()) } just Runs
        // 初始化MockDownloadMgr状态
        MockDownloadMgr.startDownload(emptyList(), null)
    }

    /**
     * 测试后的清理方法
     * 1. 解除所有MockK模拟
     * 2. 重置MockDownloadMgr状态
     */
    @After
    fun tearDown() {
        unmockkAll()
        // 重置MockDownloadMgr状态
        MockDownloadMgr.startDownload(emptyList(), null)
    }

    /**
     * 测试startDownload方法
     * 验证方法是否返回0(成功)
     * 使用协程测试上下文
     */
    @Test
    fun `test startDownload should return 0`() = runTest {
        // 调用startDownload并验证返回值
        val result = MockDownloadMgr.startDownload(listOf("path1", "path2"), downloadCallback)
        assertEquals(0, result)
    }

    /**
     * 测试pauseDownload方法
     * 验证:
     * 1. 是否将loop设置为false
     * 2. 是否正确调用回调方法
     */
    @Test
    fun `test pauseDownload should set loop to false and call callback`() {
        // 调用pauseDownload方法
        MockDownloadMgr.pauseDownload(operateCallback)
        // 验证回调方法是否被正确调用
        verify { operateCallback.onResult(OperateCallback.OPERATE_PAUSE_DOWNLOAD, ResultConstant.RESULT_CODE_SUCCESS) }
    }

    /**
     * 测试continueDownload方法
     * 验证:
     * 1. 是否将loop设置为true
     * 2. 是否解除线程阻塞
     * 3. 是否正确调用回调方法
     * 使用协程测试上下文
     */
    @Test
    fun `test continueDownload should set loop to true and unpark thread`() = runTest {
        // 先启动下载然后暂停
        MockDownloadMgr.startDownload(listOf("path1"), downloadCallback)
        MockDownloadMgr.pauseDownload(null)
        // 继续下载
        MockDownloadMgr.continueDownload(operateCallback)

        // 验证回调方法是否被正确调用
        verify { operateCallback.onResult(OperateCallback.OPERATE_CONTINUE_DOWNLOAD, ResultConstant.RESULT_CODE_SUCCESS) }
    }

    /**
     * 测试cancelDownload方法
     * 验证:
     * 1. 是否设置exitDownload为true
     * 2. 是否解除线程阻塞
     * 3. 是否正确调用回调方法
     * 使用协程测试上下文
     */
    @Test
    fun `test cancelDownload should set exitDownload to true and unpark thread`() = runTest {
        // 启动下载
        MockDownloadMgr.startDownload(listOf("path1"), downloadCallback)
        // 取消下载
        MockDownloadMgr.cancelDownload(operateCallback)

        // 验证回调方法是否被正确调用
        verify { operateCallback.onResult(OperateCallback.OPERATE_CANCEL_DOWNLOAD, ResultConstant.RESULT_CODE_SUCCESS) }
    }
}