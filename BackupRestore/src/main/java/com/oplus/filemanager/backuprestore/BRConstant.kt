/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BRConstant
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/28
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/7/28      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.backuprestore

const val FILE_MANAGER_FOLDER = "FileManager"
const val FILE_LABEL_BACKUP_FILE = "FileLabelsBackup"
const val FILE_THIRD_APP_BACKUP_FILE = "FileThirdAppDataBackup"
const val FILE_LABEL_MAPPING_BACKUP_FILE = "FileLabelsMappingBackup"
const val FILE_LABEL_CARD_MAPPING_BACKUP_FILE = "FileLabelCardMappingBackup"
const val SETTING_VALUES_BACKUP_FILE = "SettingValuesBackup"
const val SCAN_MODE_VALUES_BACKUP_FILE = "ScanModeValuesBackup"
const val RECYCLE_BIN_SORT_BACKUP_FILE = "RecycleBinSortMode"
const val PARTICULAR_SORT_BACKUP_FILE = "ParticularSortMode"
const val CATEGORY_AND_BROWSER_SORT_BACKUP_FILE = "CategoryAndBrowserSortMode"
const val ALL_SORT_BACKUP_FILE = "FileManagerAllSortMode"
const val ALL_SEARCH_HISTORY_BACKUP_FILE = "AllSearchHistoryList"