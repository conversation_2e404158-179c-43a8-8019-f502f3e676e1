/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File: FileActionDetail.kt
 ** Description: File/Directory detail function
 ** Version: 1.0
 ** Date: 2020/3/2
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.detail

import android.content.DialogInterface
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.HiddenFileHelper.isNeedShowHiddenFile
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.base.ACTION_CANCELLED
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.detail.FileDetailObserver.Companion.FILE_NOT_EXISTS
import com.filemanager.fileoperate.detail.FileDetailObserver.Companion.SHOW_DETAIL_DIALOG
import com.filemanager.fileoperate.detail.FileDetailObserver.FileDetailBean.CREATOR.TYPE_DIRECTORY
import com.filemanager.fileoperate.detail.FileDetailObserver.FileDetailBean.CREATOR.TYPE_FILE
import com.filemanager.fileoperate.detail.FileDetailObserver.FileDetailBean.CREATOR.TYPE_MULTI_FILES
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.provider.FileLabelMappingRecycleDBHelper
import java.io.File

class FileActionDetail(lifecycle: LifecycleOwner, originFiles: List<BaseFileBean>, isRecycleBin: Boolean = false)
    : BaseFileAction<FileDetailObserver>(lifecycle) {
    companion object {
        private const val TAG = "FileActionDetail"
    }

    private var mOperateFiles: ArrayList<BaseFileBean> = ArrayList(originFiles)
    private val mFileDetailBean = FileDetailObserver.FileDetailBean()

    init {
        mFileDetailBean.mDismissListener = DialogInterface.OnDismissListener {
            if (mFileDetailBean.mCalculating) {
                cancel()
            }
            mFileDetailBean.mDismissListener = null
        }
    }

    init {
        mFileDetailBean.mIsRecycleBin = isRecycleBin
    }

    override fun run(): Boolean {
        if (mOperateFiles.isNullOrEmpty()) {
            Log.d(TAG, "source file/directory is null or empty")
            return false
        }
        val count = mOperateFiles.size
        if (count == 1) {
            if (!JavaFileHelper.exists(mOperateFiles[0])) {
                notifyObserver(FILE_NOT_EXISTS)
                return false
            } else {
                mFileDetailBean.setSingleFile(mOperateFiles[0])
                fillLabelInfo(mFileDetailBean)
            }
        }
        notifyObserver(SHOW_DETAIL_DIALOG, mFileDetailBean)

        mFileDetailBean.type = if (count == 1) {
            if (mOperateFiles[0].mIsDirectory) TYPE_DIRECTORY else TYPE_FILE
        } else {
            TYPE_MULTI_FILES
        }
        for (file in mOperateFiles) {
            if (isCancelled()) {
                return false
            }
            if (file.mIsDirectory) {
                if (file.mData.isNullOrEmpty()) continue
                getDirectoryAndFileInfo(File(file.mData))
            } else {
                mFileDetailBean.mFileCount++
                mFileDetailBean.mTotalFileSize += file.mSize
            }
        }
        return true
    }

    @VisibleForTesting
    fun fillLabelInfo(fileDetailBean: FileDetailObserver.FileDetailBean) {
        fileDetailBean.mPath?.let { path ->
            val labels = if (fileDetailBean.mIsRecycleBin) {
                FileLabelMappingRecycleDBHelper.getFileLabelsByPath(fileDetailBean.mOriginPath ?: "")
            } else {
                FileLabelMappingDBHelper.getFileLabelsByPath(path)
            }
            val ids = arrayListOf<Long>()
            val names = arrayListOf<String>()
            labels?.forEach { label ->
                ids.add(label.id)
                names.add(label.name)
            }
            fileDetailBean.mLabelIds.clear()
            fileDetailBean.mLabelNames.clear()
            fileDetailBean.mLabelIds.addAll(ids)
            fileDetailBean.mLabelNames.addAll(names)
        }
    }

    private fun getDirectoryAndFileInfo(file: File) {
        if (!file.exists()) {
            return
        }
        //count folder size
        mFileDetailBean.mTotalFileSize += file.length()
        mFileDetailBean.mDirCount++
        val list = JavaFileHelper.listJavaFiles(file, !(isNeedShowHiddenFile() || mFileDetailBean.mIsRecycleBin)) ?: return
        list.forEach {
            if (isCancelled()) {
                return
            }
            if (it.isDirectory) {
                getDirectoryAndFileInfo(it)
            } else {
                mFileDetailBean.mFileCount++
                mFileDetailBean.mTotalFileSize += it.length()
            }
        }
    }

    override fun onCancelled() {
        mOperateFiles.clear()
        super.onCancelled()
    }

    override fun afterRun(result: Boolean) {
        mOperateFiles.clear()
        mFileDetailBean.mCalculating = false
        mFileDetailBean.mDismissListener = null
        if (result) {
            notifyObserver(SHOW_DETAIL_DIALOG, mFileDetailBean)
        } else {
            notifyObserver(ACTION_CANCELLED)
        }
    }

    override fun recycle() {
        mFileDetailBean.recycle()
        super.recycle()
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || mFileDetailBean.isShow
    }
}