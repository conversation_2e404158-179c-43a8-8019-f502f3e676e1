package com.android.documentsui

import android.content.Context
import android.content.Intent
import android.os.Environment
import android.os.StatFs
import android.provider.DocumentsContract
import com.android.documentsui.base.DocumentInfo
import com.android.documentsui.base.DocumentStack
import com.android.documentsui.base.RootInfo
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.view.GuideDocumentsUIView
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File
import java.util.LinkedList
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class DocumentUtilTest {

    private val mockContext: Context = mockk(relaxed = true)
    private val testRootPath = Environment.getExternalStorageDirectory().absolutePath
    private val testChildPath = "$testRootPath/Download/SubFolder"

    @Before
    fun setup() {
        mockkStatic(
            Environment::class,
            SdkUtils::class,
            AppUtils::class,
            CollectPrivacyUtils::class
        )
        every { Environment.getExternalStorageDirectory() } returns File(testRootPath)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `getDocumentStack should return root stack when path is root`() {
        // When
        val stack = DocumentUtil.getDocumentStack(testRootPath)

        // Then
        val rootInfo = stack.getField<RootInfo>("mRoot")
        assertEquals(DocumentUtil.ROOT_ID, rootInfo.rootId)
        assertEquals(DocumentUtil.AUTHORITY, rootInfo.authority)
        assertEquals(DocumentUtil.DOCUMENT_ID, rootInfo.documentId)
    }

    @Test
    fun `getDocumentStack should return nested stack for child path`() {
        // When
        val stack = DocumentUtil.getDocumentStack(testChildPath)

        // Then
        val docs = stack.getField<LinkedList<DocumentInfo>>("mList")
        assertEquals(3, docs.size) // Root + Download + SubFolder
        assertEquals("Download", docs[1].displayName)
        assertEquals("SubFolder", docs[2].displayName)
        assertEquals("${DocumentUtil.DOCUMENT_ID}Download", docs[1].documentId)  // 修复点：移除末尾斜杠
    }

    @Test
    fun `getDocumentStack should return root when path not in external storage`() {
        // When
        val stack = DocumentUtil.getDocumentStack("/invalid/path")

        // Then
        val docs = stack.getField<LinkedList<DocumentInfo>>("mList")
        assertEquals(DocumentUtil.DOCUMENT_ID, docs.first.documentId)
    }

    @Test
    fun `startDocumentsUIIntent should set mainline package for mainline project`() {
        // Given
        mockMainlineProject(true)
        val intent = mockk<Intent>(relaxed = true)

        // When
        DocumentUtil.startDocumentsUIIntent(mockContext, intent)

        // Then
        verify { intent.setPackage(KtConstants.PKG_NAME_DOCUMENTS_UI_MAINLINE) }
        verify { CollectPrivacyUtils.collectInstalledAppList(KtConstants.PKG_NAME_DOCUMENTS_UI_MAINLINE) }
    }

    @Test
    fun `startDocumentsUIIntent should set not_mainline package for non-mainline project`() {
        // Given
        mockMainlineProject(false)
        val intent = mockk<Intent>(relaxed = true)

        // When
        DocumentUtil.startDocumentsUIIntent(mockContext, intent)

        // Then
        verify { intent.setPackage(KtConstants.PKG_NAME_DOCUMENTS_UI_NOT_MAINLINE) }
        verify { CollectPrivacyUtils.collectInstalledAppList(KtConstants.PKG_NAME_DOCUMENTS_UI_NOT_MAINLINE) }
    }

    @Test
    fun `startDocumentsUIIntent should fallback when activity not found`() {
        // Given
        mockMainlineProject(false)
        val intent = mockk<Intent>(relaxed = true)
        every { mockContext.startActivity(intent) } throws android.content.ActivityNotFoundException()

        // When
        DocumentUtil.startDocumentsUIIntent(mockContext, intent)

        // Then
        verify { intent.setPackage(KtConstants.PKG_NAME_DOCUMENTS_UI_MAINLINE) }
        verify(exactly = 2) { mockContext.startActivity(intent) }
    }

    @Test
    fun `getIntent should create correct view intent with stack`() {
        // When
        val intent = DocumentUtil.getIntent(testChildPath)

        // Then
        assertEquals(Intent.ACTION_VIEW, intent.action)
        assertEquals(DocumentsContract.Root.MIME_TYPE_ITEM, intent.type)
        assertTrue(intent.hasExtra(DocumentUtil.EXTRA_STACK))
        assertTrue(intent.flags and Intent.FLAG_ACTIVITY_NEW_TASK != 0)
    }

    @Test
    fun `getAvailableSize should return 0 on invalid path`() {
        // When
        val stack = DocumentUtil.getDocumentStack("/invalid/path")
        val rootInfo = stack.getField<RootInfo>("mRoot")
        val size = rootInfo.availableBytes

        // Then
        assertEquals(0L, size)
    }

    private fun mockMainlineProject(isMainline: Boolean) {
        every { SdkUtils.isAtLeastT() } returns true
        every { AppUtils.getBuildConfigValue(GuideDocumentsUIView.FLAVOR_REGION) } returns 
            if (isMainline) "overseas" else GuideDocumentsUIView.DOMESTIC
    }
    
    private inline fun <reified T> Any.getField(fieldName: String): T {
        val field = this.javaClass.getDeclaredField(fieldName)
        field.isAccessible = true
        return field.get(this) as T
    }
}