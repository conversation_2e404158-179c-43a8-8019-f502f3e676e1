package com.oplus.filemanager.preview.core

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Observer
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.OnLoaderListener
import io.mockk.*
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * FilePreviewViewModel的单元测试类
 * 用于测试文件预览视图模型的各种功能
 */
@ExperimentalCoroutinesApi
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29], manifest = Config.NONE)
class FilePreviewViewModelTest {

    // 使用InstantTaskExecutorRule确保LiveData立即执行
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    // 测试用的协程调度器
    private val testDispatcher = TestCoroutineDispatcher()

    // 模拟的生命周期所有者
    @MockK
    private lateinit var mockLifecycleOwner: LifecycleOwner

    // 模拟的生命周期对象
    @MockK
    private lateinit var mockLifecycle: Lifecycle

    // 模拟的观察者
    @MockK
    private lateinit var mockObserver: Observer<BaseFileBean?>

    // 模拟的文件对象
    @MockK
    private lateinit var mockFileBean: BaseFileBean

    // 被测视图模型
    private lateinit var viewModel: FilePreviewViewModel

    /**
     * 测试前的初始化方法
     */
    @Before
    fun setUp() {
        // 初始化MockK注解
        MockKAnnotations.init(this)
        // 设置主调度器为测试调度器
        Dispatchers.setMain(testDispatcher)
        // 配置mock生命周期所有者的行为
        every { mockLifecycleOwner.lifecycle } returns mockLifecycle
        every { mockLifecycle.currentState } returns Lifecycle.State.RESUMED
        // 创建被测视图模型实例
        viewModel = FilePreviewViewModel()
    }

    /**
     * 测试后的清理方法
     */
    @After
    fun tearDown() {
        // 恢复主调度器
        Dispatchers.resetMain()
        // 清理测试协程
        testDispatcher.cleanupTestCoroutines()
    }

    /**
     * 测试putPreviewFile方法
     * 验证设置预览文件后数据状态是否正确更新
     */
    @Test
    fun `putPreviewFile should update preview data and ui state`() {
        // When 调用设置预览文件方法
        viewModel.putPreviewFile(mockFileBean)

        // Then 验证各项数据状态
        assert(viewModel.pickPreviewFile() == mockFileBean)
        assert(viewModel.uiState.value?.fileList?.size == 1)
        assert(viewModel.uiState.value?.fileList?.first() == mockFileBean)
        assert(viewModel.uiState.value?.selectedList?.size == 1)
        assert(viewModel.uiState.value?.selectedList?.first() == 0)
        assert(viewModel.uiState.value?.keyMap?.size == 1)
        assert(viewModel.uiState.value?.keyMap?.get(0) == mockFileBean)
        assert(viewModel.dataLoadState.value == OnLoaderListener.STATE_DONE)
    }

    /**
     * 测试pickPreviewFile方法
     * 验证当没有预览数据时返回null
     */
    @Test
    fun `pickPreviewFile should return null when no preview data`() {
        // When 调用获取预览文件方法
        val result = viewModel.pickPreviewFile()

        // Then 验证返回null
        assert(result == null)
    }

    /**
     * 测试getRealFileSize方法
     * 验证当没有预览数据时返回0
     */
    @Test
    fun `getRealFileSize should return 0 when no preview data`() {
        // When 调用获取实际文件大小方法
        val result = viewModel.getRealFileSize()

        // Then 验证返回0
        assert(result == 0)
    }

    /**
     * 测试getRealFileSize方法
     * 验证当有预览数据时返回2
     */
    @Test
    fun `getRealFileSize should return 2 when has preview data`() {
        // Given 先设置预览文件
        viewModel.putPreviewFile(mockFileBean)

        // When 调用获取实际文件大小方法
        val result = viewModel.getRealFileSize()

        // Then 验证返回2
        assert(result == 2)
    }

    /**
     * 测试selectItems方法
     * 验证总是返回true
     */
    @Test
    fun `selectItems should always return true`() {
        // When 调用选择项目方法
        val result = viewModel.selectItems(listOf(0))

        // Then 验证返回true
        assert(result)
    }

    /**
     * 测试deselectItems方法
     * 验证总是返回true
     */
    @Test
    fun `deselectItems should always return true`() {
        // When 调用取消选择项目方法
        val result = viewModel.deselectItems(listOf(0))

        // Then 验证返回true
        assert(result)
    }

    /**
     * 测试changeListMode方法
     * 验证方法调用不会抛出异常
     */
    @Test
    fun `changeListMode should do nothing`() {
        // When 调用改变列表模式方法
        viewModel.changeListMode(KtConstants.LIST_SELECTED_MODE)

        // Then 验证没有异常抛出
    }

    /**
     * 测试enterSelectionMode方法
     * 验证总是返回true
     */
    @Test
    fun `enterSelectionMode should always return true`() {
        // When 调用进入选择模式方法
        val result = viewModel.enterSelectionMode(0)

        // Then 验证返回true
        assert(result)
    }
}