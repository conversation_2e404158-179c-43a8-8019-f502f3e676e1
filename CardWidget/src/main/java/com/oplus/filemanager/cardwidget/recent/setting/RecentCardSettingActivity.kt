/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentCardSettingActivity
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/14 15:20
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.setting

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatisticsUtils.RECENT_FILE_CARD_EDIT
import com.filemanager.common.utils.StatisticsUtils.RECENT_FILE_CARD_EDIT_ENTRANCE
import com.oplus.filemanager.cardwidget.R

class RecentCardSettingActivity : AppCompatActivity() {

    private val dialogFragment: COUIBottomSheetDialogFragment by lazy {
        COUIBottomSheetDialogFragment()
    }

    private var settingViewModel: RecentCardSettingViewModel? = null
    private var widgetCode: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setBackgroundDrawableResource(R.color.card_label_setting_window_bg)
        initViewModel()
        window.decorView.post {
            showSettingsDialog()
        }
    }

    private fun initViewModel() {
        settingViewModel = ViewModelProvider(this)[RecentCardSettingViewModel::class.java]
        initCardId(intent)
    }

    private fun initCardId(intent: Intent?) {
        val serviceId = IntentUtils.getInt(intent, CARD_SERVICE_ID, 0)
        var cardType = IntentUtils.getInt(intent, CARD_TYPE, 0)
        var cardId = IntentUtils.getInt(intent, CARD_ID, 0)
        var isEditCard = true
        if (cardType == 0) {
            cardType = IntentUtils.getString(intent, CARD_TYPE)?.toInt() ?: 0
            cardId = IntentUtils.getString(intent, CARD_ID)?.toInt() ?: 0
            isEditCard = false
        }
        staticSettingEntrance(isEditCard)
        widgetCode = "$cardType&$cardId"
        Log.d(TAG, "initCardId -> widgetCode = $widgetCode ; serviceId = $serviceId")
        settingViewModel?.setCardId(widgetCode)
    }

    override fun onNewIntent(intent: Intent?) {
        Log.d(TAG, "onNewIntent")
        super.onNewIntent(intent)
        initCardId(intent)
    }

    private fun showSettingsDialog() {
        val fragment = supportFragmentManager.findFragmentByTag(TAG_SETTINGS)
        if ((fragment as? COUIBottomSheetDialogFragment)?.isVisible == true) {
            Log.d(TAG, "showSettingsDialog had exist")
            return
        }
        val settingDialog = RecentCardSettingPanelFragment()
        dialogFragment.setMainPanelFragment(settingDialog)
        dialogFragment.setIsShowInMaxHeight(true)
        Log.d(TAG, "showSettingsDialog -> stateSaved = ${supportFragmentManager.isStateSaved}")
        if (supportFragmentManager.isStateSaved.not()) {
            dialogFragment.show(supportFragmentManager, TAG_SETTINGS)
        }
    }

    private fun staticSettingEntrance(isEditCard: Boolean) {
        val entranceMap = mutableMapOf<String, String>()
        entranceMap[RECENT_FILE_CARD_EDIT_ENTRANCE] = if (isEditCard) EDIT_ENTRANCE_SETTING else EDIT_ENTRANCE_UNSELECT
        StatisticsUtils.onCommon(MyApplication.sAppContext, RECENT_FILE_CARD_EDIT, entranceMap)
    }

    companion object {
        private const val TAG = "RecentCardSettingActivity"
        private const val TAG_SETTINGS = "recent_settings"
        const val CARD_SERVICE_ID = "serviceId"
        const val CARD_TYPE = "cardType"  // 卡片type
        const val CARD_ID = "cardId"      // 卡片Id，默认为1，当重复添加卡片时，Id递增
        private const val EDIT_ENTRANCE_SETTING = "1"
        private const val EDIT_ENTRANCE_UNSELECT = "2"
    }
}