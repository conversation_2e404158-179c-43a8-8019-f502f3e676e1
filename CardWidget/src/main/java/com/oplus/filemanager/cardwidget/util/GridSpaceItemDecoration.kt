/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GridSpaceItemDecoration
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/14 19:32
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.util

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication

class GridSpaceItemDecoration(
    private val spanCount: Int,
    private val rowSpacing: Int = MyApplication.appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_9dp),
    private val columnSpacing: Int = MyApplication.appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_7dp)
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        val position = parent.getChildAdapterPosition(view)
        val column = position % spanCount
        outRect.left = column * columnSpacing / spanCount
        if (position >= spanCount) {
            outRect.top = rowSpacing
        }
    }
}