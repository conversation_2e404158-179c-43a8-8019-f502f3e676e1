/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ImagePreviewContainerManager.kt
 * Description:
 *     Manage container ui and loading ui for previewing image file
 *
 * Version: 1.0
 * Date: 2024-10-27
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-27   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.image

import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.Log
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewContainerManager
import com.oplus.filemanager.preview.utils.PreviewScrollAreaHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite

internal class ImagePreviewContainerManager(rootLayout: View) : AbsPreviewContainerManager(),
    IImagePreviewContainerManager {

    private companion object {
        private const val TAG = "ImagePreviewContainerManager"
    }

    override val nameContainer: TextViewSnippet =
        rootLayout.findViewById(R.id.preview_remote_title)
    override val imageView: AppCompatImageView = rootLayout.findViewById(R.id.preview_image_view)
    override val defaultContainer: PreviewFileInfoSuite =
        rootLayout.findViewById(R.id.preview_image_def_info)
    private val imageContainer = rootLayout.findViewById<View>(R.id.preview_image_container)
    private val loadingLayout = rootLayout.findViewById<View>(R.id.loading_layout)
    override val mDateTv: AppCompatTextView = rootLayout.findViewById(R.id.preview_remote_time_info)
    override val mSizeTv: AppCompatTextView = rootLayout.findViewById(R.id.preview_remote_size_info)
    private val scrollAreaHelper = PreviewScrollAreaHelper(
        scrollView = rootLayout.findViewById(R.id.preview_audio_scroll_area),
        operationsBar = rootLayout.findViewById(R.id.preview_operations_bar)
    )

    override fun showAsLoading() {
        super.showAsLoading()
        Log.d(TAG, "showAsLoading")
        defaultContainer.visibility = View.GONE
        nameContainer.visibility = View.VISIBLE
        imageContainer.visibility = View.INVISIBLE
        loadingLayout.visibility = View.VISIBLE
        scrollAreaHelper.hideDivider()
    }

    override fun showAsFileContainer(isDefault: Boolean) {
        Log.d(TAG, "showAsFileContainer: $isDefault")
        loadingLayout.visibility = View.GONE
        if (isDefault) {
            nameContainer.visibility = View.INVISIBLE
            imageContainer.visibility = View.INVISIBLE
            defaultContainer.visibility = View.VISIBLE
            defaultContainer.setFilePathVisible(false)
            scrollAreaHelper.disableScrollViewportMinHeight()
            scrollAreaHelper.hideDivider()
        } else {
            defaultContainer.visibility = View.GONE
            nameContainer.visibility = View.VISIBLE
            imageContainer.visibility = View.VISIBLE
            scrollAreaHelper.enableScrollViewportMinHeight(
                defaultContainer.resources
                    .getDimensionPixelOffset(R.dimen.preview_remote_scroll_layout_min_height)
            )
            scrollAreaHelper.checkShowDivider()
        }
        super.showAsFileContainer(isDefault)
    }

    override fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        scrollAreaHelper.checkShowDivider()
    }
}