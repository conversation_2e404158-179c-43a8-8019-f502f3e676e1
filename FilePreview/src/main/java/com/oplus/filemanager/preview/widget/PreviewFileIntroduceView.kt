/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - PreviewFileIntroduceView.kt
 * Description:
 *     The widget to show common file info.
 *
 * Version: 1.0
 * Date: 2024-10-25
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-10-25   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.widget

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.view.setPadding
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.IWidgetDefFileIntroduce

internal class PreviewFileIntroduceView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : LinearLayout(context, attrs), IWidgetDefFileIntroduce {

    override val fileIconView: AppCompatImageView
    private val fileNameView: AppCompatTextView
    private val fileMsgView: AppCompatTextView

    init {
        LayoutInflater.from(context).inflate(R.layout.widget_preview_file_introduce_view, this)
        fileIconView = findViewById(R.id.preview_file_icon)
        fileNameView = findViewById(R.id.preview_file_name)
        fileMsgView = findViewById(R.id.preview_file_msg)
        gravity = Gravity.CENTER
        orientation = VERTICAL
    }

    override fun setFileIcon(@DrawableRes iconRes: Int) {
        fileIconView.setImageResource(iconRes)
    }

    override fun setFileIcon(drawable: Drawable?) {
        fileIconView.setImageDrawable(drawable)
    }

    override fun setFileIcon(bitmap: Bitmap?) {
        fileIconView.setImageBitmap(bitmap)
    }

    override fun setFileName(@StringRes nameRes: Int) {
        setFileName(context.getString(nameRes))
    }

    override fun setFileName(name: String?) {
        fileNameView.text = name
    }

    override fun setFileNameVisible(visible: Boolean) {
        val padding = resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_4dp)
        if (visible) {
            fileNameView.visibility = View.VISIBLE
            fileMsgView.setPadding(0, padding, 0, padding)
        } else {
            fileNameView.visibility = View.GONE
            val top = resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_12dp)
            fileMsgView.setPadding(0, top, 0, padding)
        }
    }

    override fun setFileMessage(@StringRes msgRes: Int) {
        setFileMessage(context.getString(msgRes))
    }

    override fun setFileMessage(msg: String?) {
        if (msg.isNullOrEmpty()) {
            fileMsgView.visibility = INVISIBLE
            fileMsgView.text = " "
        } else {
            fileMsgView.text = msg
            fileMsgView.visibility = VISIBLE
        }
    }
}