<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="328dp"
    android:height="275dp"
    android:viewportWidth="328"
    android:viewportHeight="275">
  <group>
    <clip-path
        android:pathData="M327,50H1V269.14H327V50Z"/>
    <path
        android:pathData="M229.54,-166.22H98.46C81.91,-166.22 68.5,-152.81 68.5,-136.26V206.41C68.5,222.96 81.91,236.38 98.46,236.38H229.54C246.09,236.38 259.5,222.96 259.5,206.41V-136.26C259.5,-152.81 246.09,-166.22 229.54,-166.22Z"
        android:fillColor="#383838"/>
    <path
        android:pathData="M229.54,-160.6H98.46C85.02,-160.6 74.12,-149.71 74.12,-136.26V206.42C74.12,219.86 85.02,230.76 98.46,230.76H229.54C242.99,230.76 253.88,219.86 253.88,206.42V-136.26C253.88,-149.71 242.99,-160.6 229.54,-160.6Z"
        android:fillColor="#1C1C1C"/>
    <path
        android:pathData="M74,92.4C74,84.56 74,80.64 75.53,77.64C76.87,75.01 79.01,72.87 81.64,71.53C84.64,70 88.56,70 96.4,70H231.6C239.44,70 243.36,70 246.36,71.53C248.99,72.87 251.13,75.01 252.47,77.64C254,80.64 254,84.56 254,92.4V192.6C254,206.04 254,212.76 251.38,217.9C249.08,222.41 245.41,226.08 240.9,228.38C235.76,231 229.04,231 215.6,231H112.4C98.96,231 92.24,231 87.1,228.38C82.59,226.08 78.92,222.41 76.62,217.9C74,212.76 74,206.04 74,192.6V92.4Z"
        android:fillColor="#2E2E2E"/>
    <path
        android:pathData="M207.07,217.66H120.93C119.9,217.66 119.06,218.5 119.06,219.53C119.06,220.57 119.9,221.41 120.93,221.41H207.07C208.1,221.41 208.94,220.57 208.94,219.53C208.94,218.5 208.1,217.66 207.07,217.66Z"
        android:fillColor="#6A6A6A"/>
    <path
        android:pathData="M118.73,178C118.73,169.71 112.01,163 103.73,163C95.45,163 88.73,169.71 88.73,178C88.73,186.28 95.45,193 103.73,193C112.01,193 118.73,186.28 118.73,178Z"
        android:fillColor="#4C4C4C"/>
    <path
        android:pathData="M98.02,182.8V181.19C98.02,181.14 98,181.09 97.96,181.05C97.92,181.02 97.87,181 97.82,181H96.93C96.88,181 96.83,181.02 96.79,181.05C96.75,181.09 96.73,181.14 96.73,181.19V183.24C96.73,183.44 96.81,183.63 96.97,183.78C97.12,183.92 97.33,184 97.55,184H109.91C110.13,184 110.33,183.92 110.49,183.78C110.64,183.63 110.73,183.44 110.73,183.24V181.19C110.73,181.14 110.71,181.09 110.67,181.05C110.63,181.02 110.58,181 110.52,181H109.64C109.59,181 109.53,181.02 109.5,181.05C109.46,181.09 109.43,181.14 109.43,181.19V182.8H98.02Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M104.29,178.81V178.93L104.37,178.85L106.85,176.38C106.87,176.35 106.9,176.34 106.93,176.33C106.97,176.32 107,176.33 107.03,176.34L107.08,176.38L107.72,177.02C107.76,177.05 107.77,177.1 107.77,177.14C107.77,177.18 107.76,177.23 107.72,177.26L104.23,180.75C104.08,180.9 103.87,180.99 103.66,180.98C103.44,180.98 103.24,180.9 103.09,180.74L99.65,177.25C99.62,177.22 99.61,177.18 99.61,177.13C99.61,177.09 99.62,177.05 99.65,177.02L100.31,176.38C100.34,176.35 100.38,176.33 100.42,176.33C100.47,176.33 100.51,176.35 100.54,176.38V176.38L102.96,178.83L103.04,178.92V178.8V171.21C103.04,171.17 103.06,171.13 103.09,171.1C103.12,171.07 103.16,171.05 103.2,171.05H104.12C104.17,171.05 104.21,171.07 104.24,171.1C104.27,171.13 104.29,171.17 104.29,171.21V178.81Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M158.14,178C158.14,169.71 151.42,163 143.14,163C134.85,163 128.14,169.71 128.14,178C128.14,186.28 134.85,193 143.14,193C151.42,193 158.14,186.28 158.14,178Z"
        android:fillColor="#4C4C4C"/>
    <path
        android:pathData="M197.55,178C197.55,169.71 190.83,163 182.55,163C174.26,163 167.55,169.71 167.55,178C167.55,186.28 174.26,193 182.55,193C190.83,193 197.55,186.28 197.55,178Z"
        android:fillColor="#4C4C4C"/>
    <path
        android:pathData="M82.14,178.05C82.14,166.1 91.83,156.41 103.78,156.41C115.74,156.41 125.42,166.1 125.42,178.05C125.42,190.01 115.74,199.69 103.78,199.69C91.83,199.69 82.14,190.01 82.14,178.05ZM103.78,160.02C93.82,160.02 85.75,168.09 85.75,178.05C85.75,188.01 93.82,196.09 103.78,196.09C113.74,196.09 121.82,188.01 121.82,178.05C121.82,168.09 113.74,160.02 103.78,160.02Z"
        android:fillColor="#247CFF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M103.84,101.78H103.73C94.89,101.78 87.73,108.95 87.73,117.78V117.9C87.73,126.74 94.89,133.9 103.73,133.9H103.84C112.68,133.9 119.84,126.74 119.84,117.9V117.78C119.84,108.95 112.68,101.78 103.84,101.78Z"
        android:fillColor="#4C4C4C"/>
    <path
        android:pathData="M158.25,117.84C158.25,109.52 151.51,102.78 143.2,102.78C134.88,102.78 128.14,109.52 128.14,117.84C128.14,126.15 134.88,132.89 143.2,132.89C151.51,132.89 158.25,126.15 158.25,117.84Z"
        android:fillColor="#4C4C4C"/>
    <path
        android:pathData="M197.65,117.84C197.65,109.52 190.91,102.78 182.6,102.78C174.29,102.78 167.55,109.52 167.55,117.84C167.55,126.15 174.29,132.89 182.6,132.89C190.91,132.89 197.65,126.15 197.65,117.84Z"
        android:fillColor="#4C4C4C"/>
    <path
        android:pathData="M237.07,117.84C237.07,109.52 230.32,102.78 222.01,102.78C213.7,102.78 206.96,109.52 206.96,117.84C206.96,126.15 213.7,132.89 222.01,132.89C230.32,132.89 237.07,126.15 237.07,117.84Z"
        android:fillColor="#4C4C4C"/>
    <path
        android:pathData="M254.32,147H73.67V146.63H254.32V147Z"
        android:fillColor="#444444"
        android:fillType="evenOdd"/>
  </group>
  <path
      android:pathData="M267.82,48H58V74.34H267.82V48Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="162.91"
          android:startY="48"
          android:endX="162.91"
          android:endY="74.34"
          android:type="linear">
        <item android:offset="0" android:color="#FF2E2E2E"/>
        <item android:offset="0.64" android:color="#002E2E2E"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
