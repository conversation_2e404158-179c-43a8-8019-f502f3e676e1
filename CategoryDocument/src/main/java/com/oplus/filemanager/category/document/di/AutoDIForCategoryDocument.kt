/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: AutoDIForCategoryDocument.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2024/6/6
 ** Author: yangqichang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.category.document.di

import com.oplus.filemanager.category.document.CategoryDocumentApi
import com.oplus.filemanager.category.document.DocumentExtensionTypeApi
import com.oplus.filemanager.interfaze.categorydoc.ICategoryDocumentApi
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import org.koin.dsl.module

object AutoDIForCategoryDocument {

    val categoryDocApi = module {
        single<ICategoryDocumentApi>(createdAtStart = true) {
            CategoryDocumentApi
        }
    }

    val categoryExtensionApi = module {
        single<IDocumentExtensionType>(createdAtStart = true) {
            DocumentExtensionTypeApi
        }
    }
}