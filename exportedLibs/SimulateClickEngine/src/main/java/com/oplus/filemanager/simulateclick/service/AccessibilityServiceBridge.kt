/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AccessibilityServiceBridge.kt
 * Description:
 *     The bridge to get connected accessibility service
 *
 * Version: 1.0
 * Date: 2024-05-23
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-05-23   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.service

import android.accessibilityservice.AccessibilityService

internal class AccessibilityServiceBridge {

    @Volatile
    private var connectedService: AccessibilityService? = null

    @Synchronized
    fun onServiceConnected(service: AccessibilityService) {
        connectedService = service
    }

    @Synchronized
    fun onServiceDisconnected(service: AccessibilityService) {
        if (connectedService == service) {
            connectedService = null
        }
    }

    fun pickService(): AccessibilityService? = connectedService
}