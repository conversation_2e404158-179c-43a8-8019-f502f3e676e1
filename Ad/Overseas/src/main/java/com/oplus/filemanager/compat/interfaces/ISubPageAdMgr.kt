/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ISubPageAdMgr
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/3/16 15:27
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/3/16       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat.interfaces

import android.app.Activity
import android.widget.RelativeLayout
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.wrapper.MediaFileWrapper

interface ISubPageAdMgr {

    fun makeName(name: String): String

    fun requestSubAd(
        activity: Activity,
        name: String,
        adapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>,
        fileList: ArrayList<MediaFileWrapper>,
        needHideAd: Boolean
    )

    fun scanModeChange(name: String, needHideAd: Boolean)

    fun requestCommonAd(
        activity: Activity,
        adapter: BaseSelectionRecycleAdapter<*, MediaFileWrapper>,
        fileList: ArrayList<MediaFileWrapper>
    )

    fun requestSubAlbumSetAd(
        activity: Activity,
        adapter: BaseSelectionRecycleAdapter<*, AlbumItem>,
        fileList: ArrayList<AlbumItem>
    )

    fun requestPhoneStorageAd(
        activity: Activity,
        adapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>,
        fileList: ArrayList<BaseFileBean>
    )

    fun getEntryView(name: String): RelativeLayout?

    fun refreshIfListChanged()

    fun refreshByScanModeChanged()

    fun eventDestroy()
}