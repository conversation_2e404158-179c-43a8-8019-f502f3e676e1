/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - DeleteOperation.java
 * Description: Operation for delete file or folder forever and without remove to recyclebin
 * Version: 1.0
 * Date : 2020/03/13
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/03/13    1.0     create
 ****************************************************************/

package com.filemanager.recyclebin.operation

import android.app.Activity
import android.content.ContentResolver
import android.net.Uri
import android.os.Environment
import android.os.Message
import android.provider.MediaStore
import androidx.activity.ComponentActivity
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.batch.SingleBatchAction
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.constants.Constants.MAX_PROGRESS
import com.filemanager.common.fileutils.FileDeleteHelper
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.fileutils.getAllFilePathFromDirectory
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.*
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.recyclebin.retriever.DataRetriever
import com.filemanager.recyclebin.utils.RecycleBinUtils
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.tool.trackinglib.OpTracker
import com.oplus.tool.trackinglib.OpTrackerMaker
import com.oplus.tool.trackinglib.OpType
import java.io.File
import java.lang.ref.WeakReference

class DeleteOperation(activity: ComponentActivity, listener: ProgressListener?, isMediaType: Boolean = false)
    : BaseOperation<Activity>(activity, listener) {

    companion object {
        private const val TAG = "DeleteOperation"
        internal const val BATCH_DELETE_COUNT = 50
        private const val MSG_DELAY_TIME = 150L
        private const val MSG_UPDATE_PROGRESS = 0
        private var mColorOsSrcPath: String? = null
        private var mColorOsDestPath: String? = null
    }

    private var mUris: ArrayList<Uri>? = null
    private var mPaths: ArrayList<String>? = null
    private var mFiles: ArrayList<BaseFileBean>? = null
    private val mBatchAction: InnerOpertaion
    private var mTotalSize: Long = 0
    private var mMainHandler: MainHandler? = null
    private var mDeleteHelper: FileDeleteHelper? = null

    private val allFilePath = ArrayList<String>()
    private val allFilePathForLabels = ArrayList<String>()

    init {
        activity.runOnUiThread {
            activity.lifecycle.addObserver(this)
        }
        mBatchAction = if (isMediaType) {
            DeleteAction(
                appContext.contentResolver,
                    MediaStore.Files.getContentUri("external")
                            .buildUpon()
                            .appendQueryParameter(PARAM_DELETE_DATA, "false")
                            .build(),
                    MediaStore.Images.ImageColumns.DATA)
        } else {
            ScanAction()
        }
        mMainHandler = MainHandler(activity, this)
    }

    fun handleMessage(message: Message, activity: Activity) {
        when (message.what) {
            MSG_UPDATE_PROGRESS -> {
                // get total delete size first
                val deletedSize = mDeleteHelper?.getFileDeletedSize() ?: 0
                val progress = if (deletedSize > mTotalSize) {
                    MAX_PROGRESS
                } else {
                    calculateDeleteProgress(mTotalSize, deletedSize)
                }
                publishProgress(progress)

                activity.let {
                    if (!(it.isFinishing || it.isDestroyed)) {
                        mMainHandler?.sendEmptyMessageDelayed(MSG_UPDATE_PROGRESS, MSG_DELAY_TIME)
                    }
                }
            }
        }
    }

    override fun addPaths(paths: ArrayList<String>?): Boolean {
        if (mPaths != null) {
            Log.w(TAG, "addPaths already add a task")
            return false
        }

        paths?.let {
            mPaths = ArrayList()
            mPaths?.addAll(paths)
            return true
        }
        return false
    }

    override fun addFileBeans(files: ArrayList<BaseFileBean>?): Boolean {
        if (mFiles != null) {
            Log.w(TAG, "addFileBeans already add a task")
            return false
        }

        files?.let {
            mFiles = ArrayList()
            mFiles?.addAll(files)
            return true
        }
        return false
    }

    override fun addUris(uris: ArrayList<Uri>?): Boolean {
        Log.w(TAG, "addUris not support")
        return false
    }

    override fun onTotalCountCallback(): Int {
        return when {
            mUris != null -> {
                mUris?.size ?: 0
            }
            mPaths != null -> {
                mPaths?.size ?: 0
            }
            mFiles != null -> {
                mFiles?.size ?: 0
            }
            else -> 0
        }
    }

    override fun onParametersCallback(): OperationParameters? {
        return null
    }

    override fun onPreExecute() {
        super.onPreExecute()
        mDeleteHelper = FileDeleteHelper()
        mMainHandler?.sendEmptyMessageDelayed(MSG_UPDATE_PROGRESS, MSG_DELAY_TIME)
    }

    override fun doInBackground(vararg params: Void?): OperationResult {
        StatisticsUtils.nearMeStatistics(appContext, StatisticsUtils.DETETE_ACTION)
        if (mColorOsSrcPath == null) {
            mColorOsSrcPath = (Environment.getExternalStorageDirectory().absolutePath + "/ColorOS")
        }

        if (mColorOsDestPath == null) {
            mColorOsDestPath = (Environment.getExternalStorageDirectory().absolutePath + "/.ColorOS")
        }
        try {
            RecycleBinUtils.storeFilesForDeleteLabels(allFilePathForLabels, mPaths, mUris, mFiles)
            storeFilesForBuriedPoint()
            val mainAction = Injector.injectFactory<IMain>()
            if (mPaths != null) {
                mainAction?.onDeleteFilePaths(allFilePathForLabels)
                buriedPointForMedia()
                return deletePaths(mPaths!!)
            } else if (mFiles != null) {
                mainAction?.onDeleteFilePaths(allFilePathForLabels)
                buriedPointForMedia()
                return deleteFiles(mFiles!!)
            } else {
                Log.w(TAG, "doInBackground invalid parameters")
            }
        } catch (ex: Exception) {
            Log.w(TAG, "doInBackground failed $ex")
        }
        return OperationResult(onTotalCountCallback(), 0, STATUS_ERROR)
    }

    override fun onPostExecute(result: OperationResult) {
        super.onPostExecute(result)
        Log.d(TAG, "onPostExecute ${mFiles?.size}")
        onDestroy()
    }

    private fun deletePaths(paths: ArrayList<String>): OperationResult {
        val files = ArrayList<BaseFileBean>()
        var file: BaseFileBean
        for (path in paths) {
            file = PathFileWrapper(path)
            files.add(file)
        }
        return deleteFiles(files)
    }


    private fun deleteFiles(fileBeans: ArrayList<BaseFileBean>): OperationResult {
        for (file in fileBeans) {
            mTotalSize += JavaFileHelper.fileTotalSize(file)
        }

        var failedCount = 0
        var index = 0
        var traceState = false
        val totalSize = fileBeans.size
        var realDelete: Boolean
        var statusCode = STATUS_ERROR
        for (fileBean in fileBeans) {
            val path = fileBean.mData
            if (path.isNullOrEmpty()) {
                index++
                failedCount++
                continue
            }

            if ((fileBean.mFileWrapperViewType == BaseFileBean.TYPE_FILE_LIST_HEADER) || (fileBean.mFileWrapperViewType == BaseFileBean.TYPE_LABEL_FILE)) {
                index++
                failedCount++
                continue
            }
            if (!mBatchAction.mHasDir && fileBean.mIsDirectory) {
                mBatchAction.mHasDir = true
            }
            val type = fileBean.mLocalType
            if ((path == mColorOsSrcPath) && (mColorOsDestPath != null)) {
                val destFile = PathFileWrapper(mColorOsDestPath!!)
                val state = JavaFileHelper.renameTo(fileBean, mColorOsDestPath!!)
                if (state) {
                    realDelete = mDeleteHelper?.delete(destFile) ?: true
                    if (!realDelete) {
                        index++
                        failedCount++
                        continue
                    }
                    FileTraceUtil.getInstance().traceAction(FileTraceUtil.TraceAction.MARK_DELETE, type, path)
                }
            } else {
                realDelete = mDeleteHelper?.delete(fileBean) ?: true
                if (!realDelete) {
                    // check if or not is Restricted Directory
                    if (DataRetriever.isRestrictedDirectoryR(path) || DataRetriever.isAndroidDirectoryR(path)) {
                        statusCode = STATUS_PROTECTED_DIR
                    }
                    index++
                    failedCount++
                    continue
                } else {
                    if (type == MimeTypeHelper.IMAGE_TYPE) {
                        traceState = true
                    }
                    FileTraceUtil.getInstance().traceAction(FileTraceUtil.TraceAction.MARK_DELETE,
                            type, path)
                }
            }
            mBatchAction.add(path)
            index++
        }

        mBatchAction.flush()
        if (traceState) {
            FileTraceUtil.getInstance().traceAtThisMoment()
        }

        failedCount += (totalSize - index)
        val status = when {
            (statusCode == STATUS_PROTECTED_DIR) -> {
                statusCode
            }
            (failedCount > 0) -> {
                if (fileBeans.isNotEmpty() && KtUtils.checkDfmFileAndDfmDisconnected(fileBeans[0].mData)) {
                    STATUS_DELETE_ERROR_DFM_DISCONNECT
                } else {
                    STATUS_ERROR
                }
            }
            else -> {
                STATUS_SUCCESS
            }
        }
        Log.w(TAG, "deleteFiles totalSize: $totalSize, failedCount: $failedCount, index: $index, statusCode: $statusCode status $status")
        return OperationResult(totalSize, failedCount, status)
    }

    private fun calculateDeleteProgress(total: Long, surplus: Long): Int {
        var level = 0L
        if ((total > 0L) && (surplus >= 0L)) {
            level = surplus * MAX_PROGRESS / total
        }
        return level.toInt()
    }

    override fun onDestroy() {
        mMainHandler?.apply {
            recycle()
        }
        mMainHandler = null
        mTarget.get()?.let {
            it.runOnUiThread {
                (it as? ComponentActivity)?.lifecycle?.removeObserver(this)
            }
        }
        super.onDestroy()
    }

    private fun storeFilesForBuriedPoint() {
        allFilePath.clear()
        if (mPaths != null) {
            allFilePath.forEach {
                val file = File(it)
                if (file.isDirectory) {
                    allFilePath.addAll(getAllFilePathFromDirectory(file))
                } else {
                    if (file.name.startsWith(".").not()) {
                        allFilePath.add(file.absolutePath)
                    }
                }
            }
        } else if (mFiles != null) {
            mFiles!!.forEach {
                it.mData?.let { data ->
                    val file = File(data)
                    if (file.isDirectory) {
                        allFilePath.addAll(getAllFilePathFromDirectory(file))
                    } else {
                        if (it.mDisplayName!!.startsWith(".").not()) {
                            allFilePath.add(data)
                        }
                    }
                }
            }
        }
    }
    private fun buriedPointForMedia() {
        val opTracker = OpTracker(appContext)
        var path = "default"
        mTarget.get()?.let {
            path = (it as? ComponentActivity)?.javaClass!!.name
        }
        Log.d(TAG, "buriedPointForMedia")
        val pointList: List<Map<String, String>> = opTracker.convert(
            OpTrackerMaker.Builder().apply {
                setOp(OpType.DELETE)
                setOpTime(System.currentTimeMillis().toString())
                setOpPath(path)
                setFilePaths(allFilePath)
            }.build()
        )
        pointList.forEach {
            StatisticsUtils.onCommon(appContext, StatisticsUtils.FILE_OPERATION, it)
        }
    }
}

private abstract class InnerOpertaion(protected val mUri: Uri, bulkCount: Int) :
    SingleBatchAction<String>(bulkCount) {
    var mHasDir = false
}

private class DeleteAction(val mResolver: ContentResolver, uri: Uri,
                           val mColumnName: String, bulkCount: Int = DeleteOperation.BATCH_DELETE_COUNT)
    : InnerOpertaion(uri, bulkCount) {

    companion object {
        private const val TAG = "DeleteAction"
    }

    private val mWhereClause: StringBuilder = StringBuilder()

    override fun add(pending: String) {
        if (pending.isNotEmpty()) {
            if (mWhereClause.isNotEmpty()) {
                mWhereClause.append(",")
            }
            mWhereClause.append("?")
        }

        super.add(pending)
    }

    override fun flush() {
        val size = mPending.size
        if (size <= 0) {
            return
        }

        try {
            val foo = mPending.toTypedArray<String>()
            val numRows = mResolver.delete(mUri,
                    mColumnName + " IN ("
                            + mWhereClause.toString() + ")", foo)
            Log.d(TAG, "flush size: $size numrows: $numRows")
        } catch (ex: Exception) {
            Log.w(TAG, "flush Failed $ex")
        } finally {
            //make sure reset args only when sucess
            mWhereClause.setLength(0)
            mWhereClause.clear()
            if (mHasDir) {
                Log.d(TAG, "has dir , send media scan")
                MediaScannerCompat.sendMultiDirMediaScanner(mPending,
                    Utils.MEDIA_SCAN_RECYCLE_DELETE)
            }
            mPending.clear()
        }
    }
}

private class ScanAction(bulkCount: Int = BATCH_SCAN_COUNT)
    : InnerOpertaion(MediaStore.Files.getContentUri("external"), bulkCount) {
    companion object {
        private const val BATCH_SCAN_COUNT = 50
    }

    override fun flush() {
        val size = mPending.size
        if (size > 0) {
            MediaScannerCompat.sendMultiDirMediaScanner(mPending, Utils.MEDIA_SCAN_RECYCLE_DELETE)
            mPending.clear()
        }
    }
}

class MainHandler(activity: Activity, operation: DeleteOperation) : StaticHandler<Activity>(activity) {
    private val mOperation: WeakReference<DeleteOperation> = WeakReference(operation)

    override fun handleMessage(message: Message, activity: Activity) {
        mOperation.get()?.handleMessage(message, activity)
    }
}