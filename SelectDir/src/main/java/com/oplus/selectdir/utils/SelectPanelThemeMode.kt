/*********************************************************************
 * * Copyright (C), 2010-2024 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SelectPanelStyleMode
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/03/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir.utils

import androidx.annotation.VisibleForTesting
import com.oplus.selectdir.R

object SelectPanelThemeMode {

    @VisibleForTesting
    const val MODE_BLUE = 1

    @VisibleForTesting
    const val MODE_LIGHT_BLUE = 2

    @VisibleForTesting
    const val MODE_DARK_BLUE = 3

    @VisibleForTesting
    const val MODE_GREEN = 4

    @VisibleForTesting
    const val MODE_LIGHT_GREEN = 5

    @VisibleForTesting
    const val MODE_DARK_GREEN = 6

    @VisibleForTesting
    const val MODE_RED = 7

    @VisibleForTesting
    const val MODE_LIGHT_RED = 8

    @VisibleForTesting
    const val MODE_DARK_RED = 9

    @VisibleForTesting
    const val MODE_ORANGE = 10

    @VisibleForTesting
    const val MODE_LIGHT_ORANGE = 11

    @VisibleForTesting
    const val MODE_DARK_ORANGE = 12

    @VisibleForTesting
    const val MODE_PURPLE = 13

    @VisibleForTesting
    const val MODE_LIGHT_PURPLE = 14

    @VisibleForTesting
    const val MODE_DARK_PURPLE = 15

    @VisibleForTesting
    const val MODE_YELLOW = 16

    @VisibleForTesting
    const val MODE_LIGHT_YELLOW = 17

    @VisibleForTesting
    const val MODE_DARK_YELLOW = 18

    @VisibleForTesting
    const val MODE_SKY_BLUE = 19

    @VisibleForTesting
    const val MODE_LIGHT_SKY_BLUE = 20

    @VisibleForTesting
    const val MODE_DARK_SKY_BLUE = 21

    @JvmStatic
    fun getTheme(mode: Int): Int {
        return when (mode) {
            MODE_BLUE -> com.support.appcompat.R.style.Theme_COUI_Blue
            MODE_LIGHT_BLUE -> com.support.appcompat.R.style.Theme_COUI_Light_Blue
            MODE_DARK_BLUE -> com.support.appcompat.R.style.Theme_COUI_Dark_Blue

            MODE_GREEN -> com.support.appcompat.R.style.Theme_COUI_Green
            MODE_LIGHT_GREEN -> com.support.appcompat.R.style.Theme_COUI_Light_Green
            MODE_DARK_GREEN -> com.support.appcompat.R.style.Theme_COUI_Dark_Green

            MODE_RED -> com.support.appcompat.R.style.Theme_COUI_Red
            MODE_LIGHT_RED -> com.support.appcompat.R.style.Theme_COUI_Light_Red
            MODE_DARK_RED -> com.support.appcompat.R.style.Theme_COUI_Dark_Red

            MODE_ORANGE -> com.support.appcompat.R.style.Theme_COUI_Orange
            MODE_LIGHT_ORANGE -> com.support.appcompat.R.style.Theme_COUI_Light_Orange
            MODE_DARK_ORANGE -> com.support.appcompat.R.style.Theme_COUI_Dark_Orange

            MODE_PURPLE -> com.support.appcompat.R.style.Theme_COUI_Purple
            MODE_LIGHT_PURPLE -> com.support.appcompat.R.style.Theme_COUI_Light_Purple
            MODE_DARK_PURPLE -> com.support.appcompat.R.style.Theme_COUI_Dark_Purple

            MODE_YELLOW -> com.support.appcompat.R.style.Theme_COUI_Yellow
            MODE_LIGHT_YELLOW -> com.support.appcompat.R.style.Theme_COUI_Light_Yellow
            MODE_DARK_YELLOW -> com.support.appcompat.R.style.Theme_COUI_Dark_Yellow

            MODE_SKY_BLUE -> com.support.appcompat.R.style.Theme_COUI_Skyblue
            MODE_LIGHT_SKY_BLUE -> com.support.appcompat.R.style.Theme_COUI_Light_Skyblue
            MODE_DARK_SKY_BLUE -> com.support.appcompat.R.style.Theme_COUI_Dark_Skyblue

            else -> com.support.appcompat.R.style.Theme_COUI_Blue
        }
    }
}