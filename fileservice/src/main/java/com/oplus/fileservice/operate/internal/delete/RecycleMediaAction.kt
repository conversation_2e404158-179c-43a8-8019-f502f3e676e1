/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecycleMediaAction
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 20:34
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/21       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.delete

import android.content.ContentResolver
import android.content.Context
import android.provider.MediaStore
import com.filemanager.common.batch.SingleBatchAction
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils

class RecycleMediaAction(
    context: Context,
    bulkCount: Int = BULK_DELETE_SIZE
) : SingleBatchAction<String>(bulkCount) {

    private val mWhereClause: StringBuilder = StringBuilder()
    private val mResolver: ContentResolver = context.applicationContext.contentResolver
    private val mUri = MediaStore.Files.getContentUri(EXTERNAL)
        .buildUpon()
        .appendQueryParameter(PARAM_DELETE_DATA, "false")
        .build()

    override fun add(pending: String) {
        if (pending.isNotEmpty()) {
            if (mWhereClause.isNotEmpty()) {
                mWhereClause.append(",")
            }
            mWhereClause.append("?")
        }
        super.add(pending)
    }

    @Suppress("TooGenericExceptionCaught")
    override fun flush() {
        val size = mPending.size
        if (size <= 0) {
            return
        }

        try {
            val foo = mPending.toTypedArray()
            val numRows = mResolver.delete(mUri, "${MediaStore.MediaColumns.DATA} IN ($mWhereClause)", foo)
            MediaScannerCompat.sendMultiDirMediaScanner(mPending, Utils.MEDIA_SCAN_RECYCLE_RESTORE)
            Log.d(TAG, "flush size: $size num rows: $numRows")
        } catch (ex: Exception) {
            Log.w(TAG, "flush Failed $ex")
        } finally {
            //make sure reset args only when success
            mWhereClause.setLength(0)
            mWhereClause.clear()
            mPending.clear()
        }
    }

    companion object {
        private const val TAG = "RecycleMediaAction"
        private const val BULK_DELETE_SIZE = 50
        private const val PARAM_DELETE_DATA = "deletedata"
        private const val EXTERNAL = MediaStore.VOLUME_EXTERNAL
    }
}