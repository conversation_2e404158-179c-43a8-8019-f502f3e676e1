/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentPreviewItemDeco
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/15 16:43
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/15       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.setting

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Log
import com.oplus.filemanager.cardwidget.R

class RecentPreviewItemDecoration : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        val position = parent.getChildAdapterPosition(view)
        if (position == FIRST_POSITION || position == CATEGORY_LAST_POSITION) {
            Log.d(TAG, "getItemOffsets -> add bottom")
            outRect.bottom = MyApplication.appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp)
        }
    }

    companion object {
        private const val TAG = "RecentPreviewItemDecoration"
        const val FIRST_POSITION = 0
        const val CATEGORY_LAST_POSITION = 6
    }
}