/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DownloadFragment
 * * Description : 远程设备下载文件界面
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download

import android.annotation.SuppressLint
import android.app.Activity
import android.app.PendingIntent
import android.content.Context
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.TextView
import androidx.annotation.Px
import androidx.core.content.ContextCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.coui.appcompat.clickablespan.COUIClickableSpan
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.progressbar.COUIInstallLoadProgress
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo
import com.filemanager.common.constants.CommonConstants.DEFAULT_DOWNLOAD_PATH
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.constants.RemoteDeviceConstants
import com.filemanager.common.decoration.LastItemMarginBottomDecoration
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.helper.uiconfig.type.ScreenFoldConfig
import com.filemanager.common.helper.uiconfig.type.ScreenOrientationConfig
import com.filemanager.common.helper.uiconfig.type.ScreenSizeConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.isCellular
import com.filemanager.common.utils.isNetworkAvailable
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.TextViewSnippet
import com.filemanager.common.view.VerticalImageSpan
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.oplus.filemanager.category.remotedevice.R
import com.oplus.filemanager.category.remotedevice.download.adapter.DownloadFileAdapter
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean
import com.oplus.filemanager.category.remotedevice.download.service.DownloadTaskInfo
import com.oplus.filemanager.interfaze.remotedevice.DownloadTransferCard
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDeviceStatusListener
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class DownloadFragment : BaseVMFragment<DownloadViewModel>(), OnBackPressed {

    companion object {
        private const val TAG = "DownloadFragment"

        fun create(deviceId: String, downloadPaths: ArrayList<DownloadRemoteFileBean>): DownloadFragment {
            if (downloadPaths.isEmpty()) {
                throw IllegalArgumentException("download path must not be empty")
            }
            val fragment = DownloadFragment()
            fragment.arguments = Bundle().apply {
                putString(KtConstants.P_DEVICE_ID, deviceId)
                putParcelableArrayList(KtConstants.P_PATH_LIST, downloadPaths)
            }
            return fragment
        }
    }

    private var deviceId: String = ""
    private var downloadFiles = listOf<DownloadRemoteFileBean>()
    private var isOpen = false
    private var code = MessageConstant.MSG_DOWNLOAD_REMOTE_FILE
    private var continueDownload = false
    private var pendingIntent: PendingIntent? = null
    private var singleFile = false
    private var singleFileRoot: View? = null
    private var fileTitleTv: TextViewSnippet? = null
    private var fileSizeTv: TextView? = null
    private var bottomArea: ViewGroup? = null
    private var downloadTipsTv: TextView? = null
    private var downloadProgress: COUIInstallLoadProgress? = null
    private var cancelBtn: TextView? = null

    private var multiFileRecycler: COUIRecyclerView? = null
    private var multiFileAdapter: DownloadFileAdapter? = null
    private var deviceStatus: Int = RemoteDeviceConstants.CONNECTED
    private var remoteDeviceStatusListener: IRemoteDeviceStatusListener? = null

    private lateinit var viewModel: DownloadViewModel

    override fun onAttach(context: Context) {
        super.onAttach(context)
        val bundle = arguments ?: return
        deviceId = bundle.getString(KtConstants.P_DEVICE_ID, "")
        code = bundle.getInt(KtConstants.P_CODE, MessageConstant.MSG_DOWNLOAD_REMOTE_FILE)
        continueDownload = bundle.getBoolean(KtConstants.P_CONTINUE_DOWNLOAD, false)
        downloadFiles = bundle.getParcelableArrayList<DownloadRemoteFileBean>(KtConstants.P_PATH_LIST) ?: arrayListOf()
        pendingIntent = bundle.getParcelable(KtConstants.P_PENDING_INTENT)
        if (downloadFiles.isEmpty()) {
            throw IllegalArgumentException("download path must not be empty")
        }
        singleFile = downloadFiles.size == 1
        isOpen = MessageConstant.MSG_OPEN_REMOTE_FILE == code
        Log.d(TAG, "onAttach deviceId:$deviceId downloadFiles:$downloadFiles code:$code, continueDownload:$continueDownload")
        if (isOpen && !singleFile) {
            throw IllegalArgumentException("only support open one file!!!")
        }
        viewModel = ViewModelProvider(this)[DownloadViewModel::class.java]
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val superView = super.onCreateView(inflater, container, savedInstanceState)
        if (savedInstanceState != null) {
            //重建时需要重新恢复监听下载状态，修复重建之后下载进度不动的问题
            viewModel.continueMonitorDownload()
        }
        return superView
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_download
    }

    override fun initView(view: View) {
        initToolbar(view)
        if (singleFile) {
            initSingleFileLayout(view)
        } else {
            initMultiFileLayout(view)
        }
        initBottomLayout(view)
        rootView?.setOnApplyWindowInsetsListener(rootWindowInsetsListener)
    }

    override fun initData(savedInstanceState: Bundle?) {
        if (continueDownload) {
            Log.w(TAG, "initData continue download")
            downloadProgress?.state = COUIInstallLoadProgress.UPING_OR_DOWNING
            viewModel.continueMonitorDownload()
        }
    }


    override fun startObserve() {
        startSavePathObserver()
        startTotalDownloadProgressObserver()
        startDownloadCompleteFileObserver()
        registerRemoteDeviceStatusListener()
    }


    /**
     * 下载路径
     */
    private fun startSavePathObserver() {
        viewModel.savePath.observe(this) { path ->
            val activity = baseVMActivity ?: return@observe
            Log.d(TAG, "startSavePathObserver savePath:$path")
            DownloadViewModel.saveDownloadPath(path)
            downloadTipsTv?.text = createSavePathSpan(activity, path)
            downloadFiles.forEachIndexed { index, file ->
                file.updateLocalPath(path)
            }
            if (pendingIntent == null || code == MessageConstant.MSG_DOWNLOAD_REMOTE_FILE || code == MessageConstant.MSG_OPEN_REMOTE_FILE) {
                pendingIntent = DownloadTaskInfo.remotePCActivityIntent(activity, downloadFiles.get(0).mData ?: path)
            }
        }
    }

    /**
     * 下载进度
     */
    private fun startTotalDownloadProgressObserver() {
        viewModel.totalDownloadProgress.observe(this) { pair ->
            Log.d(TAG, "startTotalDownloadProgressObserver $pair")
            val state = pair.first
            val progress = pair.second
            downloadProgress?.isClickable = true
            downloadProgress?.state = state
            downloadProgress?.progress = progress
            setDownloadProgressText(state)
            handleDownloadComplete(progress)
        }
    }

    /**
     * 下载完成的文件
     */
    private fun startDownloadCompleteFileObserver() {
        if (singleFile) {
            return
        }
        viewModel.downloadCompleteFile.observe(this) { filePath ->
            Log.e(TAG, "startDownloadCompleteFileObserver $filePath")
            downloadFiles.forEachIndexed { index, file ->
                if (file.isDownloadComplete()) {
                    return@forEachIndexed
                }
                if (viewModel.isSameFile(file.mData ?: "", filePath)) {
                    file.downloadComplete()
                    multiFileAdapter?.notifyItemChanged(index + 1)
                    return@observe
                }
            }
        }
    }

    private fun registerRemoteDeviceStatusListener() {
        if (remoteDeviceStatusListener == null) {
            remoteDeviceStatusListener = object : IRemoteDeviceStatusListener {
                override fun onDeviceAdd(list: List<RemoteDeviceInfo>) {}

                override fun onDeviceRemove(list: List<RemoteDeviceInfo>) {}

                override fun onDeviceUpdate(list: List<RemoteDeviceInfo>) {
                    var newDeviceInfo: RemoteDeviceInfo? = null
                    list.forEach {
                        if (it.deviceId?.equals(deviceId) == true) {
                            newDeviceInfo = it
                            return@forEach
                        }
                    }
                    newDeviceInfo?.let {
                        if (deviceStatus != RemoteDeviceConstants.CONNECTED && it.deviceStatus == RemoteDeviceConstants.CONNECTED) {
                            //设备状态从 可连接或已离线 变成 已连接
                            onDeviceStatusChanged(RemoteDeviceConstants.CONNECTED)
                        } else if (deviceStatus == RemoteDeviceConstants.CONNECTED && it.deviceStatus != RemoteDeviceConstants.CONNECTED) {
                            //设备状态从 已连接 变成 可连接或已离线
                            onDeviceStatusChanged(it.deviceStatus)
                        } else if (deviceStatus == RemoteDeviceConstants.UNDISCOVERED && it.deviceStatus == RemoteDeviceConstants.DISCOVERED) {
                            //设备状态从 已离线 变成 可连接
                            onDeviceStatusChanged(RemoteDeviceConstants.DISCOVERED)
                        } else if (deviceStatus == RemoteDeviceConstants.DISCOVERED && it.deviceStatus == RemoteDeviceConstants.UNDISCOVERED) {
                            //设备状态从 可连接 变成 已离线
                            onDeviceStatusChanged(RemoteDeviceConstants.UNDISCOVERED)
                        }
                    }
                }
            }
        }
        remoteDeviceStatusListener?.let {
            val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
            remoteDeviceApi?.addDeviceStatusListener(it)
        }
    }

    private fun onDeviceStatusChanged(status: Int) {
        lifecycleScope.launch(Dispatchers.Main) {
            Log.d(TAG, "onDeviceStatusChanged status: $status")
            deviceStatus = status
            if (downloadProgress?.state == COUIInstallLoadProgress.DEFAULT_UP_OR_DOWN) {
                Log.w(TAG, "onDeviceStatusChanged current download status is default")
                return@launch
            }
            when (status) {
                RemoteDeviceConstants.CONNECTED -> {
                    if (isCellular(MyApplication.appContext)) { // 流量
                        downloadTipsTv?.setText(com.filemanager.common.R.string.downloading_will_consume_mobile_traffic)
                    } else {
                        downloadTipsTv?.setText(com.filemanager.common.R.string.leave_curren_page_cannot_affect_download_tips)
                    }
                }

                RemoteDeviceConstants.DISCOVERED -> { // 可连接
                    if (isNetworkAvailable(MyApplication.appContext)) {
                        downloadTipsTv?.setText(com.filemanager.common.R.string.device_to_be_connected_please_connect_device)
                    } else {
                        downloadTipsTv?.setText(com.filemanager.common.R.string.server_exception_please_retry)
                    }
                    viewModel.totalDownloadProgress.value = Pair(DownloadViewModel.DOWNLOAD_FAIL, viewModel.totalDownloadProgress.value?.second ?: 0)
                }

                RemoteDeviceConstants.UNDISCOVERED -> { //已离线
                    if (isNetworkAvailable(MyApplication.appContext)) { // 有网，设备已离线
                        downloadTipsTv?.setText(com.filemanager.common.R.string.device_offline_please_connect_device)
                    } else {
                        downloadTipsTv?.setText(com.filemanager.common.R.string.no_network_please_check_network_and_retry)
                    }
                    viewModel.totalDownloadProgress.value = Pair(DownloadViewModel.DOWNLOAD_FAIL, viewModel.totalDownloadProgress.value?.second ?: 0)
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        Log.w(TAG, "onResume")
        DownloadTransferCard.showFluidCloud = false
    }


    override fun onResumeLoadData() {
    }

    private fun initToolbar(view: View) {
        rootView = view.findViewById(R.id.coordinator_layout)
        appBarLayout = view.findViewById(R.id.toolbar_layout)
        toolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        toolbar?.apply {
            title = ""
            titleMarginStart = 0
            isTitleCenterStyle = false
        }
        baseVMActivity?.apply {
            setSupportActionBar(toolbar)
            baseVMActivity?.supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(true)
                setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
        appBarLayout?.apply {
            setPadding(
                paddingLeft,
                COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom
            )
        }
    }

    /**
     * 单个文件
     */
    private fun initSingleFileLayout(view: View) {
        val activity = baseVMActivity ?: return
        if (singleFileRoot != null) {
            Log.w(TAG, "initSingleFileLayout inflate over once")
            return
        }
        val singleFileStub = view.findViewById<ViewStub>(R.id.single_file_layout)
        singleFileRoot = singleFileStub.inflate()
        val fileIconImg = singleFileRoot?.findViewById<FileThumbView>(R.id.single_file_icon) ?: return
        val videoTypeTv = singleFileRoot?.findViewById<TextView>(R.id.video_type_tv)
        fileTitleTv = singleFileRoot?.findViewById<TextViewSnippet>(R.id.single_file_title_tv)
        fileSizeTv = singleFileRoot?.findViewById<TextView>(R.id.single_file_size_tv)
        val file = downloadFiles.get(0)
        val localType = file.mLocalType
        if (MimeTypeHelper.IMAGE_TYPE == localType || MimeTypeHelper.VIDEO_TYPE == localType) {
            fileIconImg.setStrokeStyle(FileThumbView.STROKE_2DP)
            val imageSize = activity.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_108dp)
            val placeholderResId = KtThumbnailHelper.getClassifyResId(localType)
            Glide.with(this).asBitmap().load(file).override(imageSize, imageSize)
                .error(placeholderResId)
//                .placeholder(placeholderResId)
                .into(fileIconImg)
        } else {
            fileIconImg.setStrokeStyle(FileThumbView.STROKE_NONE)
            FileImageLoader.sInstance.displayDefault(
                file,
                fileIconImg,
                0,
                0,
                FileImageLoader.THUMBNAIL_TYPE_SQUARE,
                true,
                isCoverError = true,
                isSmallDoc = true
            )
        }
        if (MimeTypeHelper.VIDEO_TYPE == file.mLocalType) {
            videoTypeTv?.visibility = View.VISIBLE
            val ext = FileTypeUtils.getExtension(file.mDisplayName)
            videoTypeTv?.text = ext
        }

        fileTitleTv?.text = file.mDisplayName
        fileTitleTv?.setTextViewStyle()
        fileSizeTv?.text = resources.getString(com.filemanager.common.R.string.single_file_size, viewModel.getFileTotalSize(downloadFiles))
    }

    /**
     * 多个文件的View
     */
    private fun initMultiFileLayout(view: View) {
        val activity = baseVMActivity ?: return
        if (multiFileRecycler != null) {
            Log.w(TAG, "initSingleFileLayout inflate over once")
            return
        }
        val viewStub = view.findViewById<ViewStub>(R.id.multi_file_layout)
        val multiFileRoot = viewStub.inflate()
        multiFileRecycler = multiFileRoot.findViewById(R.id.recycler_multi_file)
        multiFileRecycler?.layoutManager = LinearLayoutManager(context)
        val totalSizeStr = viewModel.getFileTotalSize(downloadFiles)
        multiFileAdapter = DownloadFileAdapter(activity, downloadFiles, totalSizeStr)
        multiFileAdapter?.setHasStableIds(true)
        multiFileRecycler?.addItemDecoration(LastItemMarginBottomDecoration(com.filemanager.common.R.dimen.dimen_32dp))
        multiFileRecycler?.adapter = multiFileAdapter
        (appBarLayout as? COUIDividerAppBarLayout)?.bindRecyclerView(multiFileRecycler)
        val dividerLine = view.findViewById<View>(R.id.divider_line)
        dividerLine.visibility = View.VISIBLE
    }

    private fun state(state: Int?): String {
        return when (state) {
            COUIInstallLoadProgress.DEFAULT_UP_OR_DOWN -> "默认"
            COUIInstallLoadProgress.UPING_OR_DOWNING -> "下载中"
            COUIInstallLoadProgress.UP_OR_DOWN_WAIT -> "暂停"
            COUIInstallLoadProgress.UP_OR_DOWN_FAIL -> "失败"
            else -> "默认"
        }
    }

    /**
     * 底部的按钮
     */
    private fun initBottomLayout(view: View) {
        bottomArea = view.findViewById(R.id.bottom_area_layout)
        // 下载提示
        downloadTipsTv = view.findViewById(R.id.tv_download_tips)
        downloadTipsTv?.movementMethod = LinkMovementMethod.getInstance()
        // 开始下载按钮
        downloadProgress = view.findViewById<COUIInstallLoadProgress>(R.id.progress_download)
        downloadProgress?.max = DownloadRemoteFileBean.DOWNLOAD_MAX_PROGRESS
        downloadProgress?.setOnTouchListener { v, event ->
            val isClickable = v.isClickable
            val viewState = downloadProgress?.state
            val modelState = viewModel.totalDownloadProgress.value?.first
            Log.d(TAG, "onTouch isClickable: $isClickable view:${state(viewState)} model:${state(modelState)}")
            !isClickable || (viewState != modelState)
        }
        downloadProgress?.setOnClickListener {
            handleDownload()
        }

        // 取消下载
        cancelBtn = view.findViewById<TextView>(R.id.btn_cancel_download)
        COUITextViewCompatUtil.setPressRippleDrawable(cancelBtn)
        cancelBtn?.setOnClickListener {
            cancelDownload()
        }
    }

    private fun setDownloadProgressText(state: Int) {
        when (state) {
            COUIInstallLoadProgress.DEFAULT_UP_OR_DOWN -> { // 默认状态
                val text = resources.getString(com.filemanager.common.R.string.start_download)
                downloadProgress?.setText(text)
                downloadProgress?.contentDescription = text
            }

            COUIInstallLoadProgress.UPING_OR_DOWNING -> { // 下载中
                continueDownload = false
                downloadTipsTv?.setText(com.filemanager.common.R.string.leave_curren_page_cannot_affect_download_tips)
                val progress = downloadProgress?.progress ?: 0
                downloadProgress?.setText(String.format("%.1f", progress * 1.0f) + "%")
                cancelBtn?.isVisible = progress > 0
            }

            COUIInstallLoadProgress.UP_OR_DOWN_WAIT -> { // 暂停
                val text = resources.getString(com.filemanager.common.R.string.continue_download)
                downloadProgress?.setText(text)
                downloadProgress?.contentDescription = text
            }

            COUIInstallLoadProgress.UP_OR_DOWN_FAIL -> { // 重试
                if (deviceStatus == RemoteDeviceConstants.CONNECTED) { // 当前设备处于连接中
                    downloadTipsTv?.setText(com.filemanager.common.R.string.server_exception_please_retry)
                }
                val text = resources.getString(com.filemanager.common.R.string.retry_string)
                downloadProgress?.setText(text)
                downloadProgress?.contentDescription = text
                cancelBtn?.isVisible = false
            }
        }
    }

    @SuppressLint("NewApi")
    private val rootWindowInsetsListener = View.OnApplyWindowInsetsListener { _, insets ->
        val bottom = insets.getInsets(WindowInsetsCompat.Type.systemBars()).bottom
        val paddingBottom = if (StatusBarUtil.checkIsGestureNavMode()) {
            0
        } else {
            bottom
        }
        bottomArea?.let {
            val layoutParams = it?.layoutParams as ViewGroup.MarginLayoutParams
            var marginBottom =
                it.context.resources.getDimension(com.filemanager.common.R.dimen.dimen_40dp)
            if (paddingBottom > 0) {
                marginBottom =
                    it.context.resources.getDimension(com.filemanager.common.R.dimen.dimen_24dp)
            }
            Log.d(TAG, "window inset bottomMargin:$marginBottom")
            layoutParams.bottomMargin = marginBottom.toInt()
            it.layoutParams = layoutParams
        }
        insets
    }


    /**
     * 创建下载路径的span
     */
    private fun createSavePathSpan(activity: Activity, path: String): Spannable {
        // 保存路径
        val i18nPath = viewModel.i18nSavePath(activity, path)
        // 提示
        val tipsText = if (isOpen) {
            activity.getString(com.filemanager.common.R.string.open_remote_file_need_download_tips)
        } else {
            activity.getString(com.filemanager.common.R.string.storage_location)
        }
        val spannableString: SpannableStringBuilder
        val content = String.format("%s%s", tipsText, i18nPath)
        spannableString = SpannableStringBuilder(content)
        spannableString.setSpan(object : COUIClickableSpan(activity) {
            override fun onClick(widget: View) {
                Log.d(TAG, "createSavePathSpan -> onClick $widget")
                // 打开选择路径弹窗
                if (activity is TransformNextFragmentListener) {
                    val savePath = viewModel.savePath.value ?: DEFAULT_DOWNLOAD_PATH
                    (activity as TransformNextFragmentListener).showSelectPathFragmentDialog(MessageConstant.MSG_DOWNLOAD_REMOTE_FILE, savePath)
                }
            }
        }, tipsText.length, content.length, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
        spannableString.setSpan(
            ForegroundColorSpan(COUIContextUtil.getAttrColor(activity, com.support.appcompat.R.attr.couiColorLabelPrimary)),
            tipsText.length, content.length, Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        // 向右箭头图片
        val drawable = ContextCompat.getDrawable(activity, R.drawable.ic_next_arraw)
        drawable?.let {
            it.setBounds(0, 0, it.intrinsicWidth, it.intrinsicHeight)
            val startMargin = activity.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_3dp)
            val imageSpan = VerticalImageSpan(it, startMargin, 0)
            spannableString.insert(content.length, " ")
            spannableString.setSpan(imageSpan, content.length, content.length + 1, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
        }
        return spannableString
    }


    /**
     * 开始下载
     */
    private fun handleDownload() {
        val activity = baseVMActivity ?: return
        val lastState = viewModel.totalDownloadProgress.value?.first ?: DownloadViewModel.DOWNLOAD_DEFAULT
        val state = downloadProgress?.state ?: return
        val downloadComplete = downloadProgress?.progress == downloadProgress?.max
        Log.d(TAG, "handleDownload current state:${state(state)} last:${state(lastState)} progress:${downloadProgress?.progress}")
        if (downloadComplete) { //下载完成
            if (isOpen) { //并且是打开的，跳转到打开页面
                viewModel.openFile(activity, downloadFiles.get(0))
            }
            return
        }
        val downloadingTaskId = viewModel.currentDownloadingTaskId()
        when (state) {
            COUIInstallLoadProgress.DEFAULT_UP_OR_DOWN -> { // 默认状态
                viewModel.startDownload(activity, deviceId, downloadFiles, code, pendingIntent)
                downloadProgress?.isClickable = false
            }

            COUIInstallLoadProgress.UPING_OR_DOWNING -> { // 下载中
                val progress = downloadProgress?.progress ?: 0
                if (progress == 0) {
                    if (continueDownload) {
                        Log.w(TAG, "handleDownload continue download:$downloadingTaskId")
                        viewModel.continueDownload(activity, deviceId, downloadingTaskId)
                        return
                    }
                    setDownloadProgressText(state)
                    viewModel.startDownload(activity, deviceId, downloadFiles, code, pendingIntent)
                    downloadProgress?.isClickable = false
                } else {
                    if (lastState == COUIInstallLoadProgress.UP_OR_DOWN_FAIL) { // 重试
                        if (deviceStatus == RemoteDeviceConstants.CONNECTED) {
                            viewModel.startDownload(activity, deviceId, downloadFiles, code, pendingIntent)
                            downloadProgress?.isClickable = false
                        } else {
                            cancelDownload()
                        }
                    } else if (lastState == DownloadViewModel.DOWNLOAD_WAIT) { // 继续
                        viewModel.continueDownload(activity, deviceId, downloadingTaskId)
                    }
                }
            }

            COUIInstallLoadProgress.UP_OR_DOWN_WAIT -> { // 暂停
                if (lastState == DownloadViewModel.DOWNLOADING) {
                    viewModel.pauseDownload(activity, deviceId, downloadingTaskId)
                }
            }

            COUIInstallLoadProgress.UP_OR_DOWN_FAIL -> { // 取消
                cancelDownload()
            }
        }
    }

    /**
     * 取消下载
     */
    private fun cancelDownload() {
        val activity = baseVMActivity ?: return
        val downloadingTaskId = viewModel.currentDownloadingTaskId()
        viewModel.cancelDownload(activity, deviceId, downloadingTaskId)
        activity.finish()
    }

    /**
     * 下载完成,关闭当前界面
     * 如果是打开，则根据不同的类型跳转到不同的打开界面
     * 如果是下载，则自动跳转到下载的文件目录，文件列表需要锚定这个文件的位置并高亮一次
     *
     */
    private fun handleDownloadComplete(progress: Int) {
        val downloadComplete = progress == downloadProgress?.max
        Log.d(TAG, "handleDownloadComplete complete:$downloadComplete ,isOpen:$isOpen")
        if (!downloadComplete) {
            return
        }
        val activity = baseVMActivity ?: return
        if (isOpen) { //并且是打开的，跳转到打开页面
            viewModel.openFile(activity, downloadFiles.get(0))
        } else { // 跳转到分类界面
            viewModel.jumpCategoryRemoteFileActivity(activity, downloadFiles)
        }
    }

    fun onMenuItemSelected(item: MenuItem): Boolean {
        Log.d(TAG, "onMenuItemSelected $item")
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        when (item.itemId) {
            android.R.id.home -> {
                baseVMActivity?.onBackPressed()
                return true
            }
        }
        return false
    }


    override fun pressBack(): Boolean {
        return false
    }

    fun fromSelectPathResult(code: Int, paths: List<String>?) {
        Log.d(TAG, "fromSelectPathResult $code paths:$paths")
        viewModel.savePath.value = paths?.get(0)
//        activity?.let { fileOperateController?.onSelectPathReturn(it, code, paths) }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        val activity = baseVMActivity ?: return
        val resources = activity.resources
        Log.d(TAG, "onUIConfigChanged")
        var shouldUpdate = false
        configList.forEach {
            if ((it is ScreenFoldConfig) || (it is ScreenOrientationConfig) || (it is ScreenSizeConfig)) {
                shouldUpdate = true
            }
        }
        if (shouldUpdate) {
            val marginH = resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.download_file_desc_margin)
            if (singleFile) {
                updateViewMarginHorizontal(fileTitleTv, marginH)
                updateViewMarginHorizontal(fileSizeTv, marginH)
                fileTitleTv?.text = downloadFiles.get(0).mDisplayName ?: ""
                fileTitleTv?.setTextViewStyle()
            } else {
                multiFileAdapter?.notifyDataSetChanged()
            }
            updateViewMarginHorizontal(downloadTipsTv, resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.download_tips_margin))
            downloadProgress?.setTouchModeWidth(resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.download_file_button_width))
            downloadProgress?.requestLayout()
        }
    }

    private fun updateViewMarginHorizontal(view: View?, @Px margin: Int) {
        view?.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            marginStart = margin
            marginEnd = margin
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.w(TAG, "onDestroy")
        DownloadTransferCard.showFluidCloud = true
    }

    override fun onDestroyView() {
        super.onDestroyView()
        remoteDeviceStatusListener?.let {
            val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
            remoteDeviceApi?.removeDeviceStatusListener(it)
        }
    }
}