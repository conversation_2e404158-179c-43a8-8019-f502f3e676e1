/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DefaultHeaderTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/23
 * * Author      : wanghonglei
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  wanghonglei     2022/12/23      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.recent.view.refresh

import com.oplus.filemanager.recent.view.refresh.header.DefaultHeader
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class DefaultHeaderTest {

    @Test
    fun setDragDistanceRatioTest() {
        val defaultHeader = mockk<DefaultHeader>(relaxed = true)
        defaultHeader.setDragDistanceRatio(DefaultHeader.DRAG_DISTANCE_RATIO_LABEL)
        every { defaultHeader.setDragDistanceRatio(any()) } answers { callOriginal() }
        assertEquals(0f, defaultHeader.mDragDistanceRatio)
    }
}