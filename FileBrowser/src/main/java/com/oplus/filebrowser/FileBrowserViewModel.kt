/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.filebrowser
 * * Version     : 1.0
 * * Date        : 2020/5/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.provider.DocumentsContract
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.filemanager.common.MyApplication
import com.filemanager.common.back.PredictiveBackDetailPathChecker
import com.filemanager.common.back.PredictiveBackPathChecker
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.base.*
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.interfaces.IPickerManager
import com.filemanager.common.interfaces.IRefreshActivityDataForCreateDir
import com.filemanager.common.interfaces.LoadingLoaderListener
import com.filemanager.common.path.FileBrowPathHelper
import com.filemanager.common.path.FilePathHelper
import com.filemanager.common.utils.*
import com.filemanager.common.utils.StatisticsUtils.EVENT_CLICK_ANDROID_DATA
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.createdir.FileActionCreateDir
import com.filemanager.fileoperate.createdir.FileCreateDirObserver
import com.oplus.selectdir.filebrowser.SelectFileBrowserLoader
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.*

open class FileBrowserViewModel : SelectionViewModel<BaseFileBean, FileBrowserViewModel.FileBrowserUiModel>() {
    companion object {
        private const val TAG = "FileBrowserViewModel"
        private const val FILE_BROWSER_LOADER_ID = 1
        private const val REFRESH_DELAY_TIME = 300L
        private const val HANDLER_MESSAGE_REFRESH = 1
        private const val FILE_BROWSER_SCAN_MODE_SP_KEY = "file_browser_scan_mode"
        private const val PICKER_FILE_BROWSER_SCAN_MODE_SP_KEY = "picker_file_browser_scan_mode"
        @JvmStatic
        val sInternalAndroidDataPath by lazy {
            "${VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)}${File.separator}" +
                    "android${File.separator}data"
        }

        @JvmStatic
        val sInternalAndroidObbPath by lazy {
            "${VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)}${File.separator}" +
                    "android${File.separator}obb"
        }

        @VisibleForTesting
        fun handleLoadComplete(
            viewModel: FileBrowserViewModel,
            loadData: List<out BaseFileBean>,
            keyMap: HashMap<Int, BaseFileBean>,
            isPickerMode: Boolean = false
        ) {
            var mLoadData = loadData
            mLoadData.let {
                viewModel.mModeState.mInitState = true
                viewModel.launch {
                    val selectedList = ArrayList<Int>()
                    if ((viewModel.mUiState.value?.mSelectedList?.size ?: 0) > 0) {
                        withContext(Dispatchers.IO) {
                            for (selectedFile in viewModel.mUiState.value!!.mSelectedList) {
                                if (keyMap.containsKey(selectedFile)) {
                                    selectedList.add(selectedFile)
                                }
                            }
                        }
                    }
                    viewModel.pickerSelectedKeyList?.apply {
                        Log.d(TAG, "onLoadComplete pickerSelectedKeyList $size")
                        for (selectedFile in this) {
                            if (keyMap.containsKey(selectedFile)) {
                                selectedList.add(selectedFile)
                            }
                        }
                    }
                    if (viewModel.mPushPathInfo != null) {
                        viewModel.mPositionModel.value?.mCurrentPath?.apply {
                            viewModel.mPathHelp?.push(viewModel.mPushPathInfo)
                        }
                        viewModel.mPushPathInfo = null
                    }
                    if (it.isEmpty() && (viewModel.mModeState.mListModel.value == KtConstants.LIST_SELECTED_MODE)) {
                        Log.d(TAG, "onLoadComplete mResultList is empty change to normal mode")
                        viewModel.mModeState.mListModel.value = KtConstants.LIST_NORMAL_MODE
                    }
                    viewModel.mUiState.value = FileBrowserUiModel(mLoadData, viewModel.mModeState, selectedList, viewModel.mPositionModel, keyMap)
                    Log.d(TAG, "handleLoadComplete mNeedScroll=${viewModel.mNeedScroll},mPositionModel=${viewModel.mPositionModel.value}")
                    if (viewModel.mNeedScroll) {
                        viewModel.mPositionModel.postValue(viewModel.mPositionModel.value)
                    }
                    setCreateFolderPosition(viewModel, mLoadData)
                }
            }
        }

        private fun setCreateFolderPosition(viewModel: FileBrowserViewModel, loadData: List<BaseFileBean>) {
            if (viewModel.createFolderPath.isEmpty()) {
                return
            }
            val position = loadData
                .map { it.mData }
                .indexOf(viewModel.createFolderPath)
            if (position != -1) {
                viewModel.createFolderPosition.postValue(position)
                viewModel.createFolderPath = ""
            }
        }
    }

    val mModeState = BaseStateModel(MutableLiveData<Int>(KtConstants.LIST_NORMAL_MODE))
    var mPathHelp: FileBrowPathHelper? = null
    var mNeedScroll = false

    //In order to distinguish between entering and exiting folders because of the folder animation
    var mIsFolderIn = true
    val mPositionModel = MutableLiveData<PositionModel>()
    var mPushPathInfo: FilePathHelper.PathInfo? = null
    open val mBrowseModeState by lazy {
        val lastScanMode = ConfigSharedPreferenceUtils.getInt(
            if (isPickerMode) {
                PICKER_FILE_BROWSER_SCAN_MODE_SP_KEY
            } else {
                FILE_BROWSER_SCAN_MODE_SP_KEY
            }, 0)
        MutableLiveData<Int>(if (lastScanMode == 0) {
            KtConstants.SCAN_MODE_LIST
        } else {
            lastScanMode
        })
    }
    val mFileBrowserCallBack = FileBrowserCallBack(this)
    var isFromDetail = false
    var mCurrentPath = ""
    var isFromShortcutFolder = false
    internal var mShortcutFolderRootPath = ""
    var createFolderPath = ""
    var createFolderPosition = MutableLiveData(-1)
    var pathPackages: HashMap<String, String>? = null
    var pickerSelectedKeyList: MutableList<Int>? = null
    var isPickerMode = false
    var mPickerMultipleType: Array<String>? = null
    private var checker: PredictiveBackPathChecker? = null

    fun initLoader(mLoaderController: LoaderController?, path: String) {
        if (mFileBrowserCallBack.getLoader() == null) {
            mPositionModel.value = PositionModel(path, 0, 0)
            mPathHelp?.pushTo(path)
            mLoaderController?.initLoader(FILE_BROWSER_LOADER_ID, mFileBrowserCallBack)
        } else {
            if (path != mPositionModel.value?.mCurrentPath) {
                mPositionModel.value = PositionModel(path, 0, 0)
                mPathHelp?.pushTo(path)
                mFileBrowserCallBack.loadData(path, true)
            } else {
                mFileBrowserCallBack.loadData()
            }
        }
    }

    fun initPathHelper(currentPath: String) {
        val shortcutRootPath = checkShortcutFolderRootPath(currentPath)
        if (mPathHelp == null) {
            mPathHelp = FileBrowPathHelper(currentPath, isFromShortcutFolder, shortcutRootPath)
        } else if (currentPath.contains(mPathHelp?.getRootPath() ?: "*", true).not()) {
            mPathHelp!!.updateRootPath(currentPath, shortcutRootPath)
        }
    }

    open fun checkShortcutFolderRootPath(currentPath: String?): String? {
        Log.d(TAG, "mShortcutFolderRootPath=$mShortcutFolderRootPath currentPath=$currentPath isFromShortcutFolder=$isFromShortcutFolder")
        if (currentPath.isNullOrEmpty() || !isFromShortcutFolder) return null
        if (mShortcutFolderRootPath.isEmpty()) {
            mShortcutFolderRootPath = File(currentPath).parentFile?.absolutePath ?: ""
            Log.d(TAG, "init mShortcutFolderRootPath=$mShortcutFolderRootPath")
        } else {
            val rootPathArr = mShortcutFolderRootPath.split(File.separator.toRegex()).dropWhile { it.isEmpty() }.toTypedArray()
            val currentPathArr = currentPath.split(File.separator.toRegex()).dropWhile { it.isEmpty() }.toTypedArray()
            var change = false
            if (rootPathArr.size <= currentPathArr.size) {
                for ((index, s) in rootPathArr.withIndex()) {
                    if (s != currentPathArr[index]) {
                        rootPathArr[index] = currentPathArr[index]
                        change = true
                    }
                }
            }
            if (change) {
                mShortcutFolderRootPath = File.separator + rootPathArr.joinToString(separator = File.separator)
                Log.d(TAG, "change after mShortcutFolderRootPath=$mShortcutFolderRootPath")
            }
        }
        return mShortcutFolderRootPath
    }

    class FileBrowserCallBack :
        LoadingLoaderListener<FileBrowserViewModel, SelectFileBrowserLoader, PathLoadResult<Int, BaseFileBean>> {

        private var mLoadNewPath = true

        constructor(viewModel: FileBrowserViewModel) : super(viewModel, viewModel.mDataLoadState)

        internal fun loadData(path: String? = null, loadNewPath: Boolean = false) {
            mLoadNewPath = loadNewPath
            getLoader()?.apply {
                if (path.isNullOrEmpty().not()) {
                    setPath(path!!)
                }
                forceLoad()
            }
        }

        internal fun isLoadNewPath() = mLoadNewPath

        override fun onCreateLoader(viewModel: FileBrowserViewModel?): SelectFileBrowserLoader? {
            return if (viewModel != null) {
                SelectFileBrowserLoader(MyApplication.sAppContext, viewModel.mPositionModel.value?.mCurrentPath
                        ?: "", viewModel.isPickerMode, viewModel.mPickerMultipleType)
            } else null
        }

        override fun onLoadComplete(viewModel: FileBrowserViewModel?, data: PathLoadResult<Int, BaseFileBean>?) {
             Log.d(TAG, "onLoadComplete in browser: size=${data?.mResultList?.size}")
            data?.let {
                if (viewModel != null) {
                    viewModel.pathPackages?.let {
                        for (file in data.mResultList) {
                            val packageName = it[file.mData]
                            if (packageName?.isNotEmpty() == true) {
                                file.originPackage = it[file.mData]
                            }
                        }
                    }
                    handleLoadComplete(viewModel, data.mResultList, data.mResultMap, isPickerMode = viewModel.isPickerMode)
                } else {
                    Log.d(TAG, "onLoadComplete viewModel is null")
                }
            }
        }
    }

    fun sortReload() {
        mFileBrowserCallBack.loadData()
    }

    fun resetState() {
        changeListMode(KtConstants.LIST_NORMAL_MODE)
    }

    /*
     isNeedSearch is used for forceLoad data when search_mode turn to normal_mode , isNeedSearch is false when after onItemClick()
     */

    fun clickToolbarSelectAll() {
        if (getRealFileSize() == mUiState.value?.mSelectedList?.size) {
            mUiState.value?.mSelectedList?.clear()
            mUiState.value = mUiState.value
        } else {
            mUiState.value?.mSelectedList?.clear()
            mUiState.value?.mFileList?.let {
                var key: Int? = null
                for (baseFileBean in it) {
                    if (baseFileBean.mFileWrapperLabel == null) {
                        key = baseFileBean?.mData?.toLowerCase()?.hashCode() ?: continue
                        mUiState.value?.mSelectedList?.add(key)
                    }
                }
            }
            mUiState.value = mUiState.value
        }
    }

    fun clickPathBar(index: Int) {
        if (mModeState.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
            changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
        val pathLeft = mPathHelp?.getPathLeft() ?: (index + 1)
        val index = pathLeft - index - 1
        val pathInfo = mPathHelp?.setTopPath(index)
        Log.d(TAG, "clickPathBar pathInfo=${pathInfo}")
        pathInfo?.let {
            mIsFolderIn = false
            mPositionModel.value?.mCurrentPath = it.path
            previewClickedFileLiveData.value = null
            mNeedScroll = true
            resetPushPathInfo()
            mFileBrowserCallBack.loadData(it.path, true)
            syncDetailPredictiveBackState(it.path)
        }

    }

    fun onDirClick(activity: BaseVMActivity?, baseFile: BaseFileBean, firstVisibleItemPosition: Int, offset: Int) {
        launch {
            val isExits = baseFile.checkExist()
            if (!isExits) {
                CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
                return@launch
            } else if ((activity != null) && baseFile.mIsDirectory) {
                baseFile.mData?.let {
                    mPositionModel.value?.mCurrentPath = it
                    mIsFolderIn = true
                    mNeedScroll = true
                    resetPushPathInfo()
                    mPushPathInfo = FilePathHelper.PathInfo(it, firstVisibleItemPosition, offset)
                    syncDetailPredictiveBackState(it)
                    if (sInternalAndroidDataPath.equals(it, true)) {
                        StatisticsUtils.onCommon(MyApplication.sAppContext, EVENT_CLICK_ANDROID_DATA)
                    }
                    mFileBrowserCallBack.loadData(it, true)
                    OptimizeStatisticsUtil.clickStorageFolderFile(it, "folder")
                    OptimizeStatisticsUtil.clickAppFileLogo(it, baseFile)
                }
            }
        }
    }

    fun setCurrentFromOtherSide(path: String) {
        Log.d(TAG, "setCurrentFromOtherSide path $path")
        if (mModeState.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
            changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
        mPathHelp?.pushTo(path)
        mPositionModel.value?.mCurrentPath = path
        mIsFolderIn = true
        mNeedScroll = true
        resetPushPathInfo()
        mFileBrowserCallBack.loadData(path, true)
    }

    fun pressBack(): Boolean {
        mModeState.let {
            if (it.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
                changeListMode(KtConstants.LIST_NORMAL_MODE)
                return true
            } else {
                if (isFromDetail && mCurrentPath.isNotEmpty()) {
                    val path = mPositionModel.value?.mCurrentPath
                    if (!path.isNullOrEmpty() && mCurrentPath.contains(path)) {
                        return false
                    }
                }

                if (mPathHelp?.pop() != null) {
                    val info = mPathHelp?.getTopPathInfo()
                    info?.path?.let {
                        mIsFolderIn = false
                        mPositionModel.value?.mCurrentPath = it
                        mPositionModel.value?.mPosition = info.position
                        mPositionModel.value?.mOffset = info.y
                        mNeedScroll = true
                        resetPushPathInfo()
                        mFileBrowserCallBack.loadData(it, true)
                        syncDetailPredictiveBackState(it)
                        return true
                    }
                }
                return false
            }
        }
    }

    /**
     * 只处理详情界面(路径为非根目录)的预测性返回手势状态
     * 正常情况下的全部文件界面，在 FilePathHelper中已处理
     */
    private fun syncDetailPredictiveBackState(positionPath: String) {
        if (!isFromDetail) {
            return
        }
        if (checker == null) {
            val helper = mPathHelp ?: return
            checker = PredictiveBackDetailPathChecker(helper, mCurrentPath, true)
        }
        checker?.let {
            PredictiveBackUtils.checkEnableBackAnim(it)
        }
    }

    private fun resetPushPathInfo() {
        mPushPathInfo = null
    }

    override fun loadData() {
        mFileBrowserCallBack.loadData()
    }

    fun clickScanModeItem(context: Context? = null) {
        if (mBrowseModeState.value == KtConstants.SCAN_MODE_LIST) {
            mBrowseModeState.value = KtConstants.SCAN_MODE_GRID
            StatisticsUtils.onCommon(context, StatisticsUtils.SCAN_MODE_SDCARD_SWITCH, hashMapOf(StatisticsUtils.SCAN_MODE_SDCARD_SWITCH to "0"))
        } else {
            mBrowseModeState.value = KtConstants.SCAN_MODE_LIST
            StatisticsUtils.onCommon(context, StatisticsUtils.SCAN_MODE_SDCARD_SWITCH, hashMapOf(StatisticsUtils.SCAN_MODE_SDCARD_SWITCH to "1"))
        }
        mBrowseModeState.value?.apply {
            ConfigSharedPreferenceUtils.putInt(
                if (isPickerMode) {
                    PICKER_FILE_BROWSER_SCAN_MODE_SP_KEY
                } else {
                    FILE_BROWSER_SCAN_MODE_SP_KEY
                }, this)
        }
    }

    class FileBrowserUiModel : BaseUiModel<BaseFileBean> {
        var mPositionModel: MutableLiveData<PositionModel>? = null

        constructor(fileList: List<BaseFileBean>,
                    stateModel: BaseStateModel,
                    selectedList: ArrayList<Int>,
                    positionModel: MutableLiveData<PositionModel>?, keyMap: HashMap<Int, BaseFileBean>)
                : super(fileList, stateModel, selectedList, keyMap) {
            mPositionModel = positionModel
        }
    }

    data class PositionModel(
            var mCurrentPath: String,
            var mPosition: Int,
            var mOffset: Int
    )

    override fun getRealFileSize(): Int {
        var size = 0
        mUiState.value?.mFileList?.apply {
            for (baseFileBean in this) {
                if (baseFileBean.mFileWrapperLabel == null) {
                    size++
                }
                if (baseFileBean.mFileWrapperViewType == BaseFileBean.TYPE_FILE_AD) {
                    //有广告，实际数量减1
                    size--
                }
            }
        }
        Log.d(TAG, "getRealFileSize ${size}")
        return size
    }

    override fun getRecyclerViewScanMode(): com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE {
        return if (mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.LIST
        } else {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.GRID
        }
    }

    fun loadData(path: String?, loadNewPath: Boolean) {
        mFileBrowserCallBack.loadData(path, loadNewPath)
    }

    override fun onCreateFolderPath(path: String) {
        createFolderPath = path
    }

    fun loadFolderLogo() {
        if (NewFunctionSwitch.SUPPORT_FOLDER_LOGO && PrivacyPolicyController.hasAgreePrivacy()) {
                launch {
                    withContext(Dispatchers.IO) {
                        pathPackages = FolderPackageUtil.getAllPathAndName()
                        mUiState.value?.mFileList?.let {
                            withContext(Dispatchers.Main) {
                                pathPackages?.let { ps ->
                                    for (file in it) {
                                        val packageName = ps[file.mData]
                                        if (packageName?.isNotEmpty() == true) {
                                            file.originPackage = packageName
                                        }
                                    }
                                }
                                mNeedScroll = false
                                mUiState.value = mUiState.value
                            }
                        }
                    }
                }
        }
    }

    fun togglePickerSelectItem(file: BaseFileBean, pickerManager: IPickerManager?) {
        uiState.value?.apply {
            val key = file?.mData?.toLowerCase()?.hashCode() ?: 0
            if (selectedList.contains(key).not()) {
                pickerSelectedKeyList?.add(key)
            } else {
                pickerSelectedKeyList?.remove(key)
            }
            uiState.value = uiState.value
        }
    }

    fun pickerCreateNewFolder(activity: BaseVMActivity, dialog: COUIBottomSheetDialog?, firstVisibleItemPosition: Int, offset: Int) {
        mPositionModel?.value?.mCurrentPath?.let { currentPath ->
            dialog?.setFollowWindowChange(false)
            FileActionCreateDir(activity, PathFileWrapper(currentPath)).execute(object : FileCreateDirObserver(activity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    super.onActionDone(result, data)
                    dialog?.setFollowWindowChange(true)
                    val createdDirPath = data as? String
                    if (result) {
                        createdDirPath?.let { path ->
                            mPositionModel.value?.mCurrentPath = path
                            mIsFolderIn = true
                            mNeedScroll = true
                            resetPushPathInfo()
                            mPushPathInfo = FilePathHelper.PathInfo(path, firstVisibleItemPosition, offset)
                            mFileBrowserCallBack.loadData(path, true)
                        }
                    }
                }
            })
        }
    }
}
