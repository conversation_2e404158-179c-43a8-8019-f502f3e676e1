/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.coloros.filemanager.common.helper
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/5/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.common.helper

import android.content.Context
import android.content.res.Configuration
import android.view.Gravity
import android.view.View
import com.coloros.filemanager.R
import com.filemanager.common.helper.getBottomAlertDialogWindowAnimStyle
import com.filemanager.common.helper.getBottomAlertDialogWindowGravity
import com.filemanager.common.helper.isMiddleAndLargeScreen
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class DialogHelperTest {

    @Before
    fun setup() {
        mockkStatic(::isMiddleAndLargeScreen)
        mockkStatic(::getBottomAlertDialogWindowGravity)
        mockkStatic(::getBottomAlertDialogWindowAnimStyle)
    }

    @After
    fun tearDown() {
        unmockkStatic(::isMiddleAndLargeScreen)
        unmockkStatic(::getBottomAlertDialogWindowGravity)
        unmockkStatic(::getBottomAlertDialogWindowAnimStyle)
    }

    @Test
    fun `should return true when check large screen if width more than 480`() {
        val config = mockk<Configuration>()
        val content = mockk<Context> {
            every { resources } returns mockk {
                every { configuration } returns config
            }
        }
        config.smallestScreenWidthDp =  481
        every { isMiddleAndLargeScreen(content) } answers { callOriginal() }
        assertEquals(true, isMiddleAndLargeScreen(content))
        config.smallestScreenWidthDp =  480
        every { isMiddleAndLargeScreen(content) } answers { callOriginal() }
        assertEquals(true, isMiddleAndLargeScreen(content))
    }

    @Test
    fun `should return false when check large screen if width less than 480`() {
        val config = mockk<Configuration>()
        val content = mockk<Context> {
            every { resources } returns mockk {
                every { configuration } returns config
            }
        }
        config.smallestScreenWidthDp =  479
        every { isMiddleAndLargeScreen(content) } answers { callOriginal() }
        assertEquals(false, isMiddleAndLargeScreen(content))
    }

    @Test
    fun `should return center when check gravity if is middle and large screen`() {
        val content = mockk<Context>()
        every { isMiddleAndLargeScreen(content) } returns true
        every { getBottomAlertDialogWindowGravity(content, null) } answers { callOriginal() }
        assertEquals(Gravity.CENTER, getBottomAlertDialogWindowGravity(content, null))
    }

    @Test
    fun `should return bottom when check gravity if is not middle and large screen`() {
        val content = mockk<Context>()
        every { isMiddleAndLargeScreen(content) } returns false
        every { getBottomAlertDialogWindowGravity(content, null) } answers { callOriginal() }
        assertEquals(Gravity.BOTTOM, getBottomAlertDialogWindowGravity(content, null))
    }

    @Test
    fun `should return left or top when check gravity if is middle and large screen and anchor view is not null`() {
        val content = mockk<Context>()
        val anchorView = mockk<View>()
        every { isMiddleAndLargeScreen(content) } returns true
        every { getBottomAlertDialogWindowGravity(content, anchorView) } answers { callOriginal() }
        assertEquals((Gravity.LEFT or Gravity.TOP), getBottomAlertDialogWindowGravity(content, anchorView))
    }

    @Test
    fun `should return keyboard when check style if has edit text and anchor view is null`() {
        val content = mockk<Context>()
        every { isMiddleAndLargeScreen(content) } returns true
        every { getBottomAlertDialogWindowAnimStyle(content, any(), any()) } answers { callOriginal() }
        assertEquals(com.support.appcompat.R.style.Animation_COUI_Dialog_AutoShowKeyboard, getBottomAlertDialogWindowAnimStyle(content, true, null))
    }

    @Test
    fun `should return popup list when check style if has not edit text and anchor view is null`() {
        val content = mockk<Context>()
        val anchorView = mockk<View>()
        every { isMiddleAndLargeScreen(content) } returns true
        every { getBottomAlertDialogWindowAnimStyle(content, any(), any()) } answers { callOriginal() }
        assertEquals(com.support.appcompat.R.style.Animation_COUI_PopupListWindow, getBottomAlertDialogWindowAnimStyle(content, false, anchorView))
    }

    @Test
    fun `should return alpha when check style if has not edit text and anchor view is null`() {
        val content = mockk<Context>()
        every { isMiddleAndLargeScreen(content) } returns true
        every { getBottomAlertDialogWindowAnimStyle(content, any(), any()) } answers { callOriginal() }
        assertEquals(com.support.appcompat.R.style.Animation_COUI_Dialog_Alpha, getBottomAlertDialogWindowAnimStyle(content, false, null))
    }

    @Test
    fun `should return dialog when check style if has not edit text and anchor view is null`() {
        val content = mockk<Context>()
        val anchorView = mockk<View>()
        every { isMiddleAndLargeScreen(content) } returns false
        every { getBottomAlertDialogWindowAnimStyle(content, any(), any()) } answers { callOriginal() }
        assertEquals(com.support.appcompat.R.style.Animation_COUI_Dialog, getBottomAlertDialogWindowAnimStyle(content, false, anchorView))
    }

    @Test
    fun `should return dialog when check style if has edit text and anchor view is null`() {
        val content = mockk<Context>()
        val anchorView = mockk<View>()
        every { isMiddleAndLargeScreen(content) } returns false
        every { getBottomAlertDialogWindowAnimStyle(content, any(), any()) } answers { callOriginal() }
        assertEquals(com.support.appcompat.R.style.Animation_COUI_Dialog, getBottomAlertDialogWindowAnimStyle(content, true, anchorView))
    }
}