package com.oplus.filemanager.compat.brand.realme

import android.app.Activity
import android.view.View
import android.widget.RelativeLayout
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.utils.CommonException
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.utils.AdConstants
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * RealmeSubPageAdMgr的单元测试类
 * 用于测试RealmeSubPageAdMgr类的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RealmeSubPageAdMgrTest {

    // 测试类中使用的成员变量
    private lateinit var realmeSubPageAdMgr: RealmeSubPageAdMgr
    private lateinit var mockActivity: Activity
    private lateinit var mockAdapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>
    private lateinit var mockFileList: ArrayList<MediaFileWrapper>
    private lateinit var mockAdvertEntry: AdvertEntry
    private lateinit var mockRelativeLayout: RelativeLayout

    /**
     * 在每个测试方法执行前的初始化方法
     * 1. 模拟Log类的静态方法
     * 2. 创建RealmeSubPageAdMgr实例
     * 3. 创建各种mock对象
     */
    @Before
    fun setUp() {
        // 模拟Log类的静态方法
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } just Runs
        every { Log.d(any<String>()) } just Runs

        // 初始化测试对象
        realmeSubPageAdMgr = RealmeSubPageAdMgr()
        
        // 创建mock对象
        mockActivity = mockk(relaxed = true)
        mockAdapter = mockk()
        mockFileList = arrayListOf(mockk())
        mockRelativeLayout = mockk(relaxed = true)
        mockAdvertEntry = mockk(relaxUnitFun = true)

        // 设置mock对象的行为
        every { mockAdvertEntry.getAdView() } returns mockRelativeLayout
        every { mockRelativeLayout.visibility } returns View.VISIBLE
        every { mockRelativeLayout.childCount } returns 1
        every { mockActivity.getResources() } returns mockk(relaxed = true)
    }

    /**
     * 在每个测试方法执行后的清理方法
     * 解除所有mock
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试makeName方法
     * 验证生成的名称是否符合预期格式
     */
    @Test
    fun testMakeName() {
        // 第一次调用makeName
        val name1 = realmeSubPageAdMgr.makeName("test")
        assertEquals("test1", name1)

        // 第二次调用makeName，验证计数器是否递增
        val name2 = realmeSubPageAdMgr.makeName("test")
        assertEquals("test2", name2)
    }

    /**
     * 测试requestCommonAd方法
     * 验证方法是否被正确调用并记录日志
     */
    @Test
    fun testRequestCommonAd() {
        realmeSubPageAdMgr.requestCommonAd(mockActivity, mockAdapter, mockFileList)
        // 验证Log.d方法是否被调用
        verify { Log.d(any<String>()) }
    }

    /**
     * 测试requestSubAlbumSetAd方法
     * 验证方法是否被正确调用并记录日志
     */
    @Test
    fun testRequestSubAlbumSetAd() {
        // 创建mock的Album相关对象
        val mockAlbumAdapter = mockk<BaseSelectionRecycleAdapter<*, AlbumItem>>()
        val mockAlbumList = arrayListOf<AlbumItem>()
        
        // 调用测试方法
        realmeSubPageAdMgr.requestSubAlbumSetAd(mockActivity, mockAlbumAdapter, mockAlbumList)
        
        // 验证Log.d方法是否被调用
        verify { Log.d(any<String>()) }
    }

    /**
     * 测试requestPhoneStorageAd方法
     * 验证方法是否被正确调用并记录日志
     */
    @Test
    fun testRequestPhoneStorageAd() {
        // 创建mock的BaseFileBean相关对象
        val mockBaseAdapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>>()
        val mockBaseList = arrayListOf<BaseFileBean>()
        
        // 调用测试方法
        realmeSubPageAdMgr.requestPhoneStorageAd(mockActivity, mockBaseAdapter, mockBaseList)
        
        // 验证Log.d方法是否被调用
        verify { Log.d(any<String>()) }
    }
}