/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : ThirdAppPackageChangeReceiver.kt
 * * Description : 监听dmp和场景智能apk安装跟新的广播
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.coloros.filemanager.appswitch.ThirdAppPackageChangeReceiver.IPackageChangeCallback.Companion.OPEARTION_CLEARED
import com.coloros.filemanager.appswitch.ThirdAppPackageChangeReceiver.IPackageChangeCallback.Companion.OPERATION_UPDATE
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ThirdAppSearchUtil

class ThirdAppPackageChangeReceiver : BroadcastReceiver() {

    companion object {
        const val TAG = "ThirdAppPackageChangeReceiver"

        //搜索服务中台的apk，不可卸载，只能覆盖安装新版本和在系统中卸载更新
        const val TARGET_PACKAGE_DMP = "com.oplus.dmp"
        //场景智能的apk，不可卸载，只能覆盖安装新版本和在系统中卸载更新
        const val TARGET_PACKAGE_SCENCE_SERVICE = ThirdAppSearchUtil.SCENESERVICE_PACKAGENAME
        const val REPLACE_CARD_CNT = 3
    }

    private val operationList: MutableList<PackageOperation> = mutableListOf()

    private val packageArray: Array<String> =
        arrayOf(TARGET_PACKAGE_DMP, TARGET_PACKAGE_SCENCE_SERVICE)

    var packageChangeCallback: IPackageChangeCallback? = null


    /**
     *  新安装 apk：ACTION_PACKAGE_ADDED
     *  覆盖安装 apk（包含卸载更新apk），新的apk覆盖安装老的apk，
     *  1.ACTION_PACKAGE_REMOVED
     *  2.ACTION_PACKAGE_ADDED
     *  3.ACTION_PACKAGE_REPLACED
     *  卸载: ACTION_PACKAGE_REMOVED
     */
    override fun onReceive(context: Context?, intent: Intent?) {
        if (intent == null) {
            Log.e(TAG, "onReceive intent null, return")
        }
        val action = intent?.action
        var packageName = ""
        if (action == Intent.ACTION_PACKAGE_ADDED) {
            packageName = intent.data?.schemeSpecificPart ?: ""
        } else if (action == Intent.ACTION_PACKAGE_REPLACED) {
            packageName = intent.data?.schemeSpecificPart ?: ""
        } else if (action == Intent.ACTION_PACKAGE_REMOVED) {
            packageName = intent.data?.schemeSpecificPart ?: ""
        } else if (action == Intent.ACTION_PACKAGE_DATA_CLEARED) {
            packageName = intent.data?.schemeSpecificPart ?: ""
        }
        Log.w(TAG, "onReceive action $action, packageName $packageName")
        if (packageName !in packageArray) {
            return
        }
        if (packageName != "" && action != null) {
            val inputOperation = PackageOperation(packageName, action)
            if (operationList.isEmpty()) {
                operationList.add(inputOperation)
                Log.i(TAG, "ADD FIRST $inputOperation")
            } else {
                val lastOperation = operationList.last()
                if (lastOperation.packageName.contentEquals(inputOperation.packageName)) {
                    inputOperation.pre = lastOperation
                    operationList.add(inputOperation)
                    Log.i(TAG, "APPEND $inputOperation, size ${operationList.size}")
                } else {
                    operationList.clear()
                    operationList.add(inputOperation)
                    Log.i(TAG, "CLEAR AND ADD FIST $inputOperation, size ${operationList.size}")
                }
            }
            Log.i(TAG, "")
            checkPackageOperationAndCallback()
        }
    }

    private fun checkPackageOperationAndCallback() {
        val updatgeOperation = checkReplacePackage(operationList)
        if (updatgeOperation != null) {
            packageChangeCallback?.onPackageChanged(OPERATION_UPDATE, updatgeOperation.packageName)
        }
        val dataClearedOperation = checkDataClearedPackage(operationList)
        if (dataClearedOperation != null) {
            packageChangeCallback?.onPackageChanged(
                OPEARTION_CLEARED,
                dataClearedOperation.packageName
            )
        }
    }


    private fun checkAddPackage(operationList: List<PackageOperation>): PackageOperation? {
        Log.i(TAG, "checkAddPackage START operationList ${operationList.size}")
        var result: PackageOperation? = null
        if (operationList.size == 1) {
            val item = operationList.last()
            result = if (item.action == Intent.ACTION_PACKAGE_ADDED) {
                item
            } else {
                null
            }
        }

        Log.i(TAG, "checkAddPackage END result $result")
        return result
    }

    private fun checkRemovePackage(operationList: List<PackageOperation>): PackageOperation? {
        Log.i(TAG, "checkRemovePackage START operationList ${operationList.size}")
        var result: PackageOperation? = null
        if (operationList.size == 1) {
            val item = operationList.last()
            result = if (item.action == Intent.ACTION_PACKAGE_REMOVED) {
                item
            } else {
                null
            }
        }
        Log.i(TAG, "checkRemovePackage END result $result")
        return result
    }


    private fun checkReplacePackage(operationList: List<PackageOperation>): PackageOperation? {
        Log.i(TAG, "checkReplacePackage START operationList ${operationList.size}")
        var result: PackageOperation? = null
        if (operationList.size >= REPLACE_CARD_CNT) {
            val lastItem = operationList.last()
            val preSecondItem = lastItem.pre
            val preThirdItem = preSecondItem?.pre
            val find = if (preSecondItem != null && preThirdItem != null) {
                lastItem.action == Intent.ACTION_PACKAGE_REPLACED &&
                        preSecondItem.action == Intent.ACTION_PACKAGE_ADDED &&
                        preThirdItem.action == Intent.ACTION_PACKAGE_REMOVED
            } else {
                false
            }
            if (find) {
                result = lastItem
            }
        }
        Log.i(TAG, "checkReplacePackage END result $result")
        return result
    }


    private fun checkDataClearedPackage(operationList: List<PackageOperation>): PackageOperation? {
        Log.i(TAG, "checkDataClearedPackage START operationList ${operationList.size}")
        var result: PackageOperation? = null
        if (operationList.isNotEmpty()) {
            val item = operationList.last()
            result = if (item.action == Intent.ACTION_PACKAGE_DATA_CLEARED) {
                item
            } else {
                null
            }
        }
        Log.i(TAG, "checkDataClearedPackage END result $result")
        return result
    }


    interface IPackageChangeCallback {
        companion object {
            //代表apk有更新，卸载更新或覆盖安装新版本
            const val OPERATION_UPDATE = 1

            //代表apk被清楚数据，需要处理
            const val OPEARTION_CLEARED = 2
        }

        fun onPackageChanged(operation: Int, packageName: String)
    }


    data class PackageOperation(val packageName: String, val action: String) {
        var pre: PackageOperation? = null
    }
}