/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemoteFileLoader
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.loader

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.FileTaskLoader
import com.filemanager.common.bean.remotedevice.RemoteFileData
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.isNetworkAvailable
import com.filemanager.common.bean.remotedevice.Constants
import com.filemanager.common.bean.remotedevice.Constants.ERROR_CODE_NO_ARG
import com.filemanager.common.bean.remotedevice.Constants.ERROR_CODE_NO_NETWORK
import com.filemanager.common.bean.remotedevice.Constants.ERROR_CODE_NO_SUPPORTED
import com.filemanager.common.bean.remotedevice.Constants.ERROR_CODE_SDK_QUERY_EXCEPTION
import com.filemanager.common.bean.remotedevice.Constants.ERROR_CODE_TIMEOUT
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.bean.remotedevice.Constants.ERROR_CODE_INTERRUPTED
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PathUtils
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.interfaze.remotedevice.IRemoteFileLoadCallback
import java.util.Locale
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.atomic.AtomicBoolean

class RemoteFileLoader(context: Context) : FileTaskLoader<RemoteLoadResult>(context) {

    companion object {
        const val TAG = "RemoteFileLoader"

        const val DEFAULT_PAGE_CNT = 30
        //设置了加载10s超时，10s超时之后，显示空数据
        const val WATI_TIME_OUT = 10000L
    }


    private var remoteDeviceId: String? = null
    private var loadPath: String? = null
    private var startIndex: Long = -1
    private var pageCount: Int = DEFAULT_PAGE_CNT



    fun setRemoteDeviceId(deviceId: String) {
        remoteDeviceId = deviceId
    }

    fun setPath(path: String) {
        loadPath = path
    }

    fun setStartIndex(start: Long) {
        startIndex = start
    }

    fun setPageCount(count: Int) {
        pageCount = count
    }


    private fun preHandleResultBackground(list: List<RemoteFileBean>): List<RemoteFileBean> {
        //这里在排序
        Log.d(TAG, "preHandleResultBackground start")
        printList(list)
        val currentSort = SortModeUtils.getSharedSortMode(MyApplication.appContext, SortRecordModeFactory.getRemoteMacKey())
        val isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getRemoteMacKey())
        val mLastSort = SortModeUtils.getSharedSortMode(
            MyApplication.appContext,
            SortModeUtils.BROWSER_LAST_SORT_RECORD
        )
        val recentFolder = list.find { it.isRecentFolder() }
        val subList = list.filterNot { it.isRecentFolder() }
        Injector.injectFactory<IDocumentExtensionType>()?.sortFiles(subList, currentSort, mLastSort, true, isDesc)
        //这里将最近文件置顶，其余的文件进行排序
        val result = ArrayList<RemoteFileBean>()
        recentFolder?.let {
            result.add(recentFolder)
        }
        result.addAll(subList)
        Log.d(TAG, "preHandleResultBackground end")
        printList(result)
        return result
    }


    private fun getItemKey(item: BaseFileBean): Int? {
        item.let {
            item as? RemoteFileBean
            val path = item.mData
            if (path.isNullOrEmpty()) {
                return null
            }
            return path.lowercase(Locale.getDefault()).hashCode()
        }
    }


    private fun prepareHandleBackground(result: RemoteLoadResult) {
        if (!isNetworkAvailable(MyApplication.appContext)) {
            result.errorCode = ERROR_CODE_NO_NETWORK
            Log.d(TAG, "prepareHandleBackground net work error")
        }
    }


    override fun loadInBackground(): RemoteLoadResult {
        //获取数据的逻辑：需要实现分页加载的逻辑
        val allFileList = CopyOnWriteArrayList<RemoteFileBean>()
        val arrayList = ArrayList<RemoteFileBean>()
        val keyMap = HashMap<Int, BaseFileBean>()
        Log.d(TAG, "loadInBackground() step1 remoteDeviceId $remoteDeviceId, loadPath: $loadPath")
        val deviceId = remoteDeviceId
        val inputDirpath = loadPath

        val result = RemoteLoadResult(deviceId, currentDir = inputDirpath, currentPage = startIndex)
        result.mResultMap = keyMap
        result.mResultList = arrayList
        if (deviceId == null || inputDirpath == null) {
            Log.d(TAG, "loadInBackground devicesId $deviceId or inputDir $inputDirpath null, return ")
            result.errorCode = ERROR_CODE_NO_ARG
            return result
        }
        prepareHandleBackground(result)
        if (result.isErrorCodeNetWork()) {
            Log.d(TAG, "loadInBackground prepareHandleBackground failed, return ")
            return result
        }
        //这里需要转换相应的路径，去掉前缀
        val sdkPath = PathUtils.convertUIPathToSdkPath(inputDirpath)
        Log.d(TAG, "loadInBackground inputDirpath $inputDirpath, sdkPath $sdkPath")
        getAndWaitFileList(result, deviceId, sdkPath, allFileList, keyMap)
        if (!result.isResultSuc()) {
            return result
        }
        arrayList.addAll(allFileList.toList())
        val sortItems = preHandleResultBackground(arrayList)
        Log.d(TAG, "loadInBackground() step6 order sortItems.size=${sortItems.size}, errorCode ${result.errorCode}, " +
                    "allFileList $allFileList, sortItems $sortItems")
        result.mResultList = sortItems
        return result
    }

    private fun getAndWaitFileList(
        result: RemoteLoadResult,
        deviceId: String,
        inputDirpath: String,
        allFileList: CopyOnWriteArrayList<RemoteFileBean>,
        keyMap: HashMap<Int, BaseFileBean>
    ) {
        val resultReturn = AtomicBoolean(false)
        kotlin.runCatching {
            val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
            if (remoteDeviceApi == null) {
                result.errorCode = ERROR_CODE_NO_SUPPORTED
                return@runCatching
            }
            val tmpLock = Object()
            remoteDeviceApi.getFileList(
                deviceId = deviceId,
                dirpath = inputDirpath,
                startIndex = startIndex,
                pageCnt = pageCount,
                callback = object : IRemoteFileLoadCallback {
                    override fun onRemoteFileLoaded(list: List<RemoteFileData>, errorCode: Int) {
                        if (errorCode == IRemoteFileLoadCallback.ERROR_CODE_SUC) {
                            Log.d(TAG, "loadInBackground step4 callback suc")
                            allFileList.addAll(list.map {
                                val bean = RemoteFileBean(it)
                                val key = getItemKey(bean)
                                if (key != null) {
                                    keyMap[key] = bean
                                }
                                bean
                            })
                        }
                        result.errorCode = errorCode
                        Log.d(TAG, "loadInBackground step4 onRemoteFileLoaded callback, allFileList size ${allFileList.size}")
                        resultReturn.set(true)
                        notifyLockReleased(tmpLock)
                    }
                })
            OptimizeStatisticsUtil.remoteFileOperateEvent(StatisticsUtils.REMOTE_VALUE_OPERATE_GET_FILE_LIST)
            val interupted = waitResult(tmpLock, resultReturn)
            Log.d(TAG, "loadInBackground step5 onRemoteFileLoaded callback, allFileList size ${allFileList.size}")
            if (allFileList.isEmpty()) {
                if (!resultReturn.get()) {
                    if (interupted) {
                        Log.d(TAG, "loadInBackground EMPTY, interrupted ")
                        result.errorCode = ERROR_CODE_INTERRUPTED
                    } else {
                        Log.d(TAG, "loadInBackground EMPTY, timeout ")
                        result.errorCode = ERROR_CODE_TIMEOUT
                    }
                } else {
                    Log.d(TAG, "loadInBackground EMPTY, but not timeout ")
                }
            }
        }.onFailure {
            Log.e(TAG, "loadInBackground error", it)
            result.errorCode = ERROR_CODE_SDK_QUERY_EXCEPTION
        }
    }

    /**
     * 通过wait的方式，规避while循环中至少一个500ms延时的逻辑
     * @param 等待额锁
     * @return 是否在等待过程中被打断
     */
    private fun waitResult(lock: Object, resultReturn: AtomicBoolean): Boolean {
        if (resultReturn.get()) {
            //此时数据已经返回, 不需要进入超时等待逻辑，直接返回
            Log.d(TAG, "loadInBackground step3 result is return, no need to wait")
            return false
        } else {
            kotlin.runCatching {
                Log.d(TAG, "loadInBackground step3 waiting callback start")
                synchronized(lock) {
                    lock.wait(WATI_TIME_OUT)
                }
            }.onFailure {
                Log.w(TAG, "loadInBackground step3 interrupted", it)
                if (it is InterruptedException) {
                    return true
                }
            }
            return false
        }
    }

    /**
     * 通知等待waitLock的监听方，结束等待
     */
    private fun notifyLockReleased(lock: Object) {
        synchronized(lock) {
            lock.notify()
        }
    }

    private fun printList(list: List<RemoteFileBean>) {
        Log.d(TAG, "printList start")
        list.forEachIndexed { index, bean ->
            Log.d(TAG, "index $index, bean $bean")
        }
        Log.d(TAG, "printList end")
    }
}


data class RemoteLoadResult(var deviceId: String?, var currentDir: String?, var currentPage: Long) {

    var mResultList: List<BaseFileBean> = mutableListOf()

    var mResultMap: HashMap<Int, BaseFileBean> = HashMap()

    //当前加载时的错误码
    var errorCode: Int = -1

    fun isErrorCodeNetWork(): Boolean {
        return Constants.isNetWorkError(errorCode)
    }

    fun isResultSuc(): Boolean {
        return Constants.isResultSuc(errorCode)
    }

    fun reset() {
        currentDir = null
        deviceId = null
        currentPage = -1
        errorCode = -1
    }
}
