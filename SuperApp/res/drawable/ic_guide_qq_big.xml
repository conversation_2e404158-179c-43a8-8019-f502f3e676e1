<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="360dp"
    android:height="264dp"
    android:viewportWidth="360"
    android:viewportHeight="264">
  <group>
    <clip-path
        android:pathData="M0,0h360v264h-360z"/>
    <group>
      <clip-path
          android:pathData="M17,50h326v219.14h-326z"/>
      <path
          android:pathData="M114.46,-166.22L245.54,-166.22A29.96,29.96 0,0 1,275.5 -136.26L275.5,206.42A29.96,29.96 0,0 1,245.54 236.38L114.46,236.38A29.96,29.96 0,0 1,84.5 206.42L84.5,-136.26A29.96,29.96 0,0 1,114.46 -166.22z"
          android:fillColor="#E4E7EB"/>
      <path
          android:pathData="M114.46,-166.42L245.54,-166.42A30.16,30.16 0,0 1,275.7 -136.26L275.7,206.41A30.16,30.16 0,0 1,245.54 236.57L114.46,236.57A30.16,30.16 0,0 1,84.31 206.41L84.31,-136.26A30.16,30.16 0,0 1,114.46 -166.42z"
          android:strokeAlpha="0.12"
          android:strokeWidth="0.389602"
          android:fillColor="#00000000"
          android:strokeColor="#000000"/>
      <path
          android:pathData="M114.46,-160.6L245.54,-160.6A24.34,24.34 0,0 1,269.88 -136.26L269.88,206.42A24.34,24.34 0,0 1,245.54 230.76L114.46,230.76A24.34,24.34 0,0 1,90.12 206.42L90.12,-136.26A24.34,24.34 0,0 1,114.46 -160.6z"
          android:fillColor="#FAFAFA"/>
      <path
          android:pathData="M74.62,45.39h209.82v26.34h-209.82z">
        <aapt:attr name="android:fillColor">
          <gradient 
              android:startX="179.53"
              android:startY="45.39"
              android:endX="179.53"
              android:endY="71.73"
              android:type="linear">
            <item android:offset="0" android:color="#FFFFFFFF"/>
            <item android:offset="0.64" android:color="#00FFFFFF"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M90,92.4C90,84.56 90,80.64 91.53,77.64C92.87,75.01 95.01,72.87 97.64,71.53C100.64,70 104.56,70 112.4,70H247.6C255.44,70 259.36,70 262.36,71.53C264.99,72.87 267.13,75.01 268.47,77.64C270,80.64 270,84.56 270,92.4V192.6C270,206.04 270,212.76 267.38,217.9C265.08,222.41 261.41,226.08 256.9,228.38C251.76,231 245.04,231 231.6,231H128.4C114.96,231 108.24,231 103.1,228.38C98.59,226.08 94.92,222.41 92.62,217.9C90,212.76 90,206.04 90,192.6V92.4Z"
          android:fillColor="#FEFEFE"/>
      <path
          android:pathData="M247.6,69.81H112.4H112.39C108.48,69.81 105.53,69.81 103.19,70C100.84,70.19 99.08,70.57 97.56,71.35C94.88,72.71 92.71,74.88 91.35,77.56C90.57,79.09 90.19,80.84 90,83.19C89.81,85.53 89.81,88.48 89.81,92.39V92.4V192.6V192.61C89.81,199.32 89.81,204.37 90.13,208.38C90.46,212.39 91.12,215.38 92.44,217.98C94.76,222.54 98.46,226.24 103.02,228.56C105.61,229.88 108.61,230.54 112.62,230.87C116.63,231.2 121.68,231.2 128.39,231.2H128.4H231.6H231.61C238.32,231.2 243.37,231.2 247.38,230.87C251.39,230.54 254.38,229.88 256.98,228.56C261.54,226.24 265.24,222.54 267.56,217.98C268.88,215.38 269.54,212.39 269.87,208.38C270.2,204.37 270.2,199.32 270.2,192.61V192.6V92.4V92.39C270.2,88.48 270.2,85.53 270,83.19C269.81,80.84 269.43,79.09 268.65,77.56C267.29,74.88 265.11,72.71 262.44,71.35C260.92,70.57 259.16,70.19 256.81,70C254.47,69.81 251.52,69.81 247.61,69.81H247.6Z"
          android:strokeAlpha="0.3"
          android:strokeWidth="0.389602"
          android:fillColor="#00000000"
          android:strokeColor="#D0D0D0"/>
      <path
          android:pathData="M136.93,217.66L223.07,217.66A1.87,1.87 0,0 1,224.94 219.53L224.94,219.53A1.87,1.87 0,0 1,223.07 221.41L136.93,221.41A1.87,1.87 0,0 1,135.06 219.53L135.06,219.53A1.87,1.87 0,0 1,136.93 217.66z"
          android:fillColor="#DFE2E5"/>
      <path
          android:pathData="M119.73,163L119.73,163A15,15 0,0 1,134.73 178L134.73,178A15,15 0,0 1,119.73 193L119.73,193A15,15 0,0 1,104.73 178L104.73,178A15,15 0,0 1,119.73 163z"
          android:fillColor="#EBEDF0"/>
      <path
          android:pathData="M114.02,182.8V181.19C114.02,181.14 114,181.09 113.96,181.05C113.92,181.02 113.87,181 113.82,181H112.93C112.88,181 112.83,181.02 112.79,181.05C112.75,181.09 112.73,181.14 112.73,181.19V183.24C112.73,183.44 112.81,183.63 112.97,183.78C113.12,183.92 113.33,184 113.55,184H125.91C126.13,184 126.33,183.92 126.49,183.78C126.64,183.63 126.73,183.44 126.73,183.24V181.19C126.73,181.14 126.71,181.09 126.67,181.05C126.63,181.02 126.58,181 126.52,181H125.64C125.58,181 125.53,181.02 125.5,181.05C125.46,181.09 125.43,181.14 125.43,181.19V182.8H114.02Z"
          android:fillColor="#848484"/>
      <path
          android:pathData="M120.29,178.81V178.93L120.37,178.85L122.85,176.38L122.85,176.38C122.87,176.35 122.9,176.34 122.93,176.33C122.97,176.32 123,176.33 123.03,176.34L123.08,176.38L123.72,177.02C123.72,177.02 123.72,177.02 123.72,177.02C123.76,177.05 123.77,177.1 123.77,177.14C123.77,177.18 123.76,177.23 123.72,177.26C123.72,177.26 123.72,177.26 123.72,177.26L120.23,180.75C120.23,180.75 120.23,180.75 120.23,180.75C120.08,180.9 119.87,180.99 119.66,180.98C119.44,180.98 119.24,180.9 119.09,180.74L119.09,180.74L115.65,177.25L115.65,177.25C115.62,177.22 115.61,177.18 115.61,177.13C115.61,177.09 115.62,177.05 115.65,177.02C115.65,177.02 115.65,177.02 115.65,177.02L116.31,176.38L116.31,176.38C116.34,176.35 116.38,176.33 116.42,176.33C116.47,176.33 116.51,176.35 116.54,176.38C116.54,176.38 116.54,176.38 116.54,176.38L118.95,178.83L119.04,178.92V178.8V171.21C119.04,171.17 119.06,171.13 119.09,171.1C119.12,171.07 119.16,171.05 119.2,171.05H120.12C120.17,171.05 120.21,171.07 120.24,171.1C120.27,171.13 120.29,171.17 120.29,171.21V178.81Z"
          android:strokeWidth="0.100361"
          android:fillColor="#848484"
          android:strokeColor="#EBEDF0"/>
      <path
          android:pathData="M159.14,163L159.14,163A15,15 0,0 1,174.14 178L174.14,178A15,15 0,0 1,159.14 193L159.14,193A15,15 0,0 1,144.14 178L144.14,178A15,15 0,0 1,159.14 163z"
          android:fillColor="#EBEDF0"/>
      <path
          android:pathData="M198.55,163L198.55,163A15,15 0,0 1,213.55 178L213.55,178A15,15 0,0 1,198.55 193L198.55,193A15,15 0,0 1,183.55 178L183.55,178A15,15 0,0 1,198.55 163z"
          android:fillColor="#EBEDF0"/>
      <path
          android:pathData="M119.78,178.05m-19.84,0a19.84,19.84 0,1 1,39.67 0a19.84,19.84 0,1 1,-39.67 0"
          android:strokeWidth="3.60674"
          android:fillColor="#00000000"
          android:strokeColor="#006AFF"/>
      <path
          android:pathData="M119.73,101.78L119.84,101.78A16,16 0,0 1,135.84 117.78L135.84,117.9A16,16 0,0 1,119.84 133.9L119.73,133.9A16,16 0,0 1,103.73 117.9L103.73,117.78A16,16 0,0 1,119.73 101.78z"
          android:fillColor="#EBEDF0"/>
      <path
          android:pathData="M159.19,102.78L159.19,102.78A15.05,15.05 0,0 1,174.25 117.84L174.25,117.84A15.05,15.05 0,0 1,159.19 132.89L159.19,132.89A15.05,15.05 0,0 1,144.14 117.84L144.14,117.84A15.05,15.05 0,0 1,159.19 102.78z"
          android:fillColor="#EBEDF0"/>
      <path
          android:pathData="M198.45,102.78L198.75,102.78A14.91,14.91 0,0 1,213.65 117.69L213.65,117.84A14.91,14.91 0,0 1,198.75 132.74L198.45,132.74A14.91,14.91 0,0 1,183.55 117.84L183.55,117.69A14.91,14.91 0,0 1,198.45 102.78z"
          android:fillColor="#EBEDF0"/>
      <path
          android:pathData="M198.6,102.78L198.6,102.78A15.05,15.05 0,0 1,213.65 117.84L213.65,117.84A15.05,15.05 0,0 1,198.6 132.89L198.6,132.89A15.05,15.05 0,0 1,183.55 117.84L183.55,117.84A15.05,15.05 0,0 1,198.6 102.78z"
          android:fillColor="#EBEDF0"/>
      <path
          android:pathData="M237.86,102.78L238.16,102.78A14.91,14.91 0,0 1,253.07 117.69L253.07,117.84A14.91,14.91 0,0 1,238.16 132.74L237.86,132.74A14.91,14.91 0,0 1,222.96 117.84L222.96,117.69A14.91,14.91 0,0 1,237.86 102.78z"
          android:fillColor="#EBEDF0"/>
      <path
          android:pathData="M238.01,102.78L238.01,102.78A15.05,15.05 0,0 1,253.07 117.84L253.07,117.84A15.05,15.05 0,0 1,238.01 132.89L238.01,132.89A15.05,15.05 0,0 1,222.96 117.84L222.96,117.84A15.05,15.05 0,0 1,238.01 102.78z"
          android:fillColor="#EBEDF0"/>
      <path
          android:pathData="M89.67,146.82L270.33,146.82"
          android:strokeWidth="0.376355"
          android:fillColor="#00000000"
          android:strokeColor="#E7E7E7"/>
    </group>
  </group>
</vector>
