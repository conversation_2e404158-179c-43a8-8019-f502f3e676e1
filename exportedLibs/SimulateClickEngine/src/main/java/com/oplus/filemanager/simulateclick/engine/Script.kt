/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - Script.kt
 * Description:
 *     The json script beans
 *
 * Version: 2.0
 * Date: 2024-05-23
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Bixia<PERSON>.<EMAIL>    2024-05-23   2.0    Porting from LLMSimulateClick
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine

import android.view.accessibility.AccessibilityNodeInfo
import android.view.accessibility.AccessibilityNodeInfo.AccessibilityAction
import androidx.annotation.IntDef
import androidx.annotation.StringDef
import com.google.gson.annotations.SerializedName
import com.oplus.filemanager.simulateclick.engine.value.ScreenDisplayFlag
import com.oplus.filemanager.simulateclick.engine.value.ScreenOrientationFlag
import com.oplus.filemanager.simulateclick.engine.value.ScreenRotationFlag

internal const val ACTION_TYPE_CLICK = "click"
internal const val ACTION_TYPE_INPUT = "input"
internal const val ACTION_TYPE_LONG_CLICK = "longclick"
internal const val ACTION_TYPE_SELECT = "select"
internal const val ACTION_TYPE_SINGLE_SELECT = "singleSelect"
internal const val ACTION_TYPE_SCROLL_FORWARD = "scroll_forward"
internal const val ACTION_TYPE_SCROLL_BACKWARD = "scroll_backward"
internal const val ACTION_TYPE_GET_TEXT = "get_text"
internal const val ACTION_TYPE_CLICK_GESTURE = "click_gesture"
internal const val ACTION_TYPE_NODE_GESTURE_SCROLL = "node_gesture_scroll"

internal const val INPUT_METHOD_STR_ORIGIN = 0
internal const val INPUT_METHOD_STR_SPACE = 1
internal const val INPUT_METHOD_PINYIN = 2
internal const val INPUT_METHOD_PINYIN_SPACE = 3
internal const val INPUT_METHOD_SHORT_PINYIN = 4
internal const val INPUT_METHOD_SHORT_PINYIN_SPACE = 5

internal const val MATCH_RULE_SELECT_IN_PARENT = "select_in_parent"

internal const val CHECK_TYPE_EQUALS = "equals"
internal const val CHECK_TYPE_WECHAT_EQUALS = "wechat_special_equals"

internal const val SCRIPT_ARGS_PREFIX = "\$args"

internal const val DEFAULT_STEP_DELAY_TIME = 500L

private const val DEFAULT_FAIL_TIME = 5000L
private const val DEFAULT_FIND_WIDGET_BY_TXT_DIRECTLY = true
private const val DEFAULT_ONLY_FIND_WIDGET_NO_ID_BY_TXT = false
private const val DEFAULT_CLICK_GESTURE_DELAY = 500L
private const val DEFAULT_CLICK_GESTURE_WAIT = 200L
private const val DEFAULT_SCROLL_PARENT_DELAY = 200L
private const val DEFAULT_GESTURE_HANDLE_TIME = 50L

@StringDef(
    ACTION_TYPE_CLICK,
    ACTION_TYPE_INPUT,
    ACTION_TYPE_LONG_CLICK,
    ACTION_TYPE_SELECT,
    ACTION_TYPE_SINGLE_SELECT,
    ACTION_TYPE_SCROLL_FORWARD,
    ACTION_TYPE_SCROLL_BACKWARD,
    ACTION_TYPE_GET_TEXT,
    ACTION_TYPE_CLICK_GESTURE,
    ACTION_TYPE_NODE_GESTURE_SCROLL
)
@Retention(AnnotationRetention.SOURCE)
internal annotation class ActionTypes

@IntDef(
    INPUT_METHOD_STR_ORIGIN,
    INPUT_METHOD_STR_SPACE,
    INPUT_METHOD_PINYIN,
    INPUT_METHOD_PINYIN_SPACE,
    INPUT_METHOD_SHORT_PINYIN,
    INPUT_METHOD_SHORT_PINYIN_SPACE
)
@Retention(AnnotationRetention.SOURCE)
internal annotation class InputMethods

/**
 * Mark some fields are unused when port into FileManager
 * but probably have usages in BreenoAssistant.
 */
@Retention(AnnotationRetention.SOURCE)
private annotation class Unused

internal data class Script(
    @SerializedName("script_ver") val scriptVersion: Long = 0,
    @SerializedName("script_enable") val scriptEnable: Boolean = true,
    @SerializedName("app_name") @Unused val appName: String? = null,
    @SerializedName("app_pkg") val appPkg: String? = null,
    @SerializedName("app_min_ver") val appMinVersion: Long = Long.MIN_VALUE,
    @SerializedName("app_max_ver") val appMaxVersion: Long = Long.MAX_VALUE,
    @SerializedName("launcher_ui") @Unused val launcherUI: String? = null,
    @SerializedName("os_version") @Unused val osVersion: String? = null,
    @SerializedName("device_model") @Unused val deviceModel: String? = null,
    @SerializedName("screen_display") val screenDisplay: ScreenDisplayFlag = ScreenDisplayFlag(),
    @SerializedName("screen_orientation") val screenOrientation: ScreenOrientationFlag = ScreenOrientationFlag(),
    @SerializedName("screen_rotation") val screenRotation: ScreenRotationFlag = ScreenRotationFlag(),
    @SerializedName("open_again_delay") val openAgainDelay: Long? = null,
    @SerializedName("skills") val skills: List<Skill>? = emptyList(),

    /**
     * Just record the script file path for script, not a part of script.
     * If this script is decode from local file, [filePath] is the path of source script file.
     * If this script is received remote script, [filePath] is the expected path to store.
     */
    @Transient var filePath: String? = null,

    /**
     * Just record the origin json content from remote scripts.
     * And will write this into json file when store remote scripts to local.
     *
     * Do not write by encoding [Script] since it will contains some default value fields
     * which are not existed in origin json content.
     */
    @Transient var originFileJson: String? = null
)

internal data class Skill(
    @SerializedName("skill_name") val skillName: String? = null,
    @SerializedName("skill_enable") val skillEnable: Boolean = true,
    @SerializedName("query_example") @Unused val queryExample: String? = null,
    @SerializedName("max_execute_time") val maxExecuteTime: Long? = null,
    @SerializedName("step_delay_time") private val stepDelayTime: Long = DEFAULT_STEP_DELAY_TIME,
    @SerializedName("steps") val steps: List<Step>? = emptyList()
) {
    fun stepDelayTime(): Long =
        if (stepDelayTime >= 0) stepDelayTime else DEFAULT_STEP_DELAY_TIME
}

internal data class Step(
    @SerializedName("actions") val actions: MutableList<Action>? = mutableListOf(),
    @SerializedName("condition") val condition: String? = null,
    @SerializedName("step_id") @Unused val stepId: Int = 0,
    @SerializedName("isback_stop") @Unused val isBackStop: String? = null
)

internal data class Action(
    @SerializedName("action_input") var actionInput: String? = null,
    @SerializedName("action_id") val actionID: String? = null,
    @SerializedName("child_widget_id") val childWidgetID: String? = null,
    @SerializedName("child_widget_text") var childWidgetText: String? = null,
    @SerializedName("child_widget_type") val childWidgetType: String? = null,
    @SerializedName("contentBack") var contentBack: String? = null,
    @SerializedName("endX") @Unused val endX: Float = 0f,
    @SerializedName("endY") @Unused val endY: Float = 0f,
    @SerializedName("fresh_time") val freshTime: Long = 0,
    @SerializedName("fromX") @Unused val fromX: Float = 0f,
    @SerializedName("fromY") @Unused val fromY: Float = 0f,
    @SerializedName("handle_time") private val handleTime: Long = DEFAULT_GESTURE_HANDLE_TIME,
    @SerializedName("input_method") @InputMethods var inputMethod: MutableList<Int>? = null,
    @SerializedName("isShowDialog") @Unused val isShowDialog: Boolean = false,
    @SerializedName("itemCheckType") val itemCheckType: String? = null,
    @SerializedName("match_rules") val matchRules: String? = null,
    @SerializedName("node_direct") @Unused val nodeDirect: String? = null,
    @SerializedName("parent_widget_id") val parentWidgetId: String? = null,
    @SerializedName("parent_widget_type") val parentWidgetType: String? = null,
    @SerializedName("scrollable_parent_widget_type") val scrollableParentWidgetType: String? = null,
    @SerializedName("scrollable_parent_widget_id") val scrollableParentWidgetId: String? = null,
    @SerializedName("scrollable_parent_widget_text") val scrollableParentWidgetText: String? = null,
    @SerializedName("queryStep") var queryStep: String? = null,
    @SerializedName("selectIndex") var selectIndex: String? = null,
    @SerializedName("action_select") @Unused var actionSelect: String? = null,
    @SerializedName("skip_time") var skipTime: Long = 0,
    @SerializedName("waite_time") private val waitTime: Long = 0,
    @SerializedName("widget_id") val widgetID: String? = null,
    @SerializedName("widget_id_list") val widgetIDList: List<String>? = null,
    @SerializedName("widget_txt") var widgetText: String? = null,
    @SerializedName("widget_index") val widgetIndex: List<Int>? = null,
    @SerializedName("widget_type") val widgetType: String? = null,
    @SerializedName("widget_contains_actions") val widgetContainsActions: Set<AccessibilityAction>? = null,
    @SerializedName("action_type") @ActionTypes val actionType: String? = null,
    @SerializedName("action_previous") var actionPrevious: MutableList<Action>? = null,
    @SerializedName("isInWeb") val isInWeb: Boolean = false,
    @SerializedName("child_check_text") val childOnlyCheckText: Boolean = false,
    @SerializedName("fail_time") var failTime: Long = DEFAULT_FAIL_TIME, // 默认设置5s，操作失败跳过
    @SerializedName("origin_input") var originInput: String? = null,
    @SerializedName("find_widget_by_txt_directly") val findWidgetByTxtDirectly: Boolean = DEFAULT_FIND_WIDGET_BY_TXT_DIRECTLY,
    @SerializedName("only_find_no_id_widget_by_txt") val onlyFindNoIdWidgetByTxt: Boolean = DEFAULT_ONLY_FIND_WIDGET_NO_ID_BY_TXT,
    @SerializedName("click_gesture_delay") private val clickGestureDelay: Long = DEFAULT_CLICK_GESTURE_DELAY,
    @SerializedName("click_gesture_wait") private val clickGestureWait: Long = DEFAULT_CLICK_GESTURE_WAIT,
    @SerializedName("scroll_parent_delay") private val scrollParentDelay: Long = DEFAULT_SCROLL_PARENT_DELAY,
) {
    fun clickGestureDelay(): Long =
        if (clickGestureDelay >= 0) clickGestureDelay else DEFAULT_CLICK_GESTURE_DELAY

    fun clickGestureWait(): Long =
        if (clickGestureWait >= 0) clickGestureWait else DEFAULT_CLICK_GESTURE_WAIT

    fun scrollParentDelay(): Long =
        if (scrollParentDelay >= 0) scrollParentDelay else DEFAULT_SCROLL_PARENT_DELAY

    fun handleTime(): Long =
        if (handleTime >= 0) handleTime else DEFAULT_GESTURE_HANDLE_TIME

    fun waitTime(): Long =
        if (waitTime >= 0) waitTime else 0
}

internal data class Status(
    val parent: Int,
    val actionList: MutableList<Action>,
    var moreChange: Int = 0,
    var searchResult: SearchResult? = null
)

internal data class SearchResult(
    var resultResult: List<AccessibilityNodeInfo>?,
    var select: Int = 1
)