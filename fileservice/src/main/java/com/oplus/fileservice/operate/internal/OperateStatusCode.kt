/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OperateStatusCode
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/29 14:13
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/6/8       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal

import com.google.gson.annotations.SerializedName

data class OperateStatusCode(
    @SerializedName("value") val value: Int,
    @SerializedName("description") val description: String
) {
    override fun toString(): String = "$value $description"

    override fun equals(other: Any?): Boolean = other is OperateStatusCode && other.value == value

    override fun hashCode(): Int = value.hashCode()

    fun description(value: String): OperateStatusCode = copy(description = value)

    companion object {
        val DoNothing: OperateStatusCode = OperateStatusCode(100, "Do Nothing")

        val InvalidSourceFileInfo: OperateStatusCode =
            OperateStatusCode(101, "Invalid Source File Info")

        val InvalidTargetFileInfo: OperateStatusCode =
            OperateStatusCode(102, "Invalid Target File Info")

        val SameOperationDirectory: OperateStatusCode =
            OperateStatusCode(103, "Same Operation Directory")

        val InvalidOperation: OperateStatusCode = OperateStatusCode(104, "Invalid Operation")

        val FileNotExist: OperateStatusCode = OperateStatusCode(105, "File Not Exist")

        val NotEnoughSpace: OperateStatusCode = OperateStatusCode(106, "Not Enough Space")

        val PartOfFileOperateFailure: OperateStatusCode =
            OperateStatusCode(107, "Part of File Operate Failure")

        val OK: OperateStatusCode = OperateStatusCode(200, "OK")

        // Delete
        val OperateFailure: OperateStatusCode = OperateStatusCode(300, "Operate Failure")
        val OperateCancelled: OperateStatusCode = OperateStatusCode(301, "Operate Cancelled")
        val DeleteOptionsInvalid: OperateStatusCode =
            OperateStatusCode(302, "Delete Options Invalid")

        // Create Dir
        val CreateDirFailed: OperateStatusCode = OperateStatusCode(400, "Create Dir Failed")
        val CreateDirExists: OperateStatusCode = OperateStatusCode(401, "Create Dir Exists")

        // Recycle File
        val RecycleDirectoryError: OperateStatusCode = OperateStatusCode(504, "Recycle Dir Error")
    }
}