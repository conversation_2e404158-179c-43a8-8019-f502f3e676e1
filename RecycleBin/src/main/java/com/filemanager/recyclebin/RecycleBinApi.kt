/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: CommonInterface.kt
 ** Description:  CommonInterface
 ** Version: 1.0
 ** Date: 2021/5/6
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.recyclebin

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.activity.ComponentActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.constants.Constants
import com.oplus.filemanager.interfaze.recyclebin.RecycleBinTotalSizeCallback
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.interfaces.fileoprate.IFileActionObserver
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.interfaces.fileoprate.IFileOperateAction
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.thread.ThreadPriority
import com.filemanager.common.thread.ThreadType
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.filemanager.recyclebin.controller.RecycleFileOperatorController
import com.filemanager.recyclebin.operation.AutoCleanOperation
import com.filemanager.recyclebin.operation.action.FileActionDelete
import com.filemanager.recyclebin.ui.RecycleBinActivity
import com.filemanager.recyclebin.ui.RecycleBinNewFragment
import com.filemanager.recyclebin.viewmodel.ViewDataSizeRunnable
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin

object RecycleBinApi : IRecycleBin {

    private const val TAG = "RecycleBinApi"

    override fun getFileActionDelete(
        context: Context,
        selectFiles: List<BaseFileBean>,
        isSelectedAll: Boolean,
        externalCategory: Int,
        paramState: Int,
        bgDelete: Boolean
    ): IFileOperateAction<IFileActionObserver> {
        return FileActionDelete(context, selectFiles, isSelectedAll, externalCategory, paramState, bgDelete)
    }

    override fun getRecycleFileOperatorController(
        lifecycle: Lifecycle,
        viewModel: SelectionViewModel<out BaseFileBean, out BaseUiModel<out BaseFileBean>>
    )
            : IFileOperate {
        return RecycleFileOperatorController(lifecycle, viewModel)
    }

    override fun doAutoClean(context: Context) {
        AutoCleanOperation.doAutoClean(context as ComponentActivity)
    }

    override fun startRecycleBinActivity(context: Context) {
        val intent = Intent(context, RecycleBinActivity::class.java)
        intent.putExtra(Constants.TITLE_RES_ID, com.filemanager.common.R.string.text_recycle_bin)
        intent.putExtra(
            Constants.TITLE,
            context.getString(com.filemanager.common.R.string.text_recycle_bin)
        )
        context.startActivity(intent)
    }

    override fun loadRecycleBinSize(
        requestTag: String,
        previousThreadKey: String?,
        callback: RecycleBinTotalSizeCallback
    ): String? {
        // ViewDataSizeRunnable may run long time, repeatedly enter and exit MainActivity will full thread pool,
        // so need to stop last runnable when start new one
        ThreadManager.sThreadManager.cancelThread(previousThreadKey)
        val sizeRunnable = ViewDataSizeRunnable(appContext)
        val executeRunnable = FileRunnable(sizeRunnable, requestTag)
        sizeRunnable.setQuerySizeListener(callback)
        return ThreadManager.sThreadManager.execute(
            executeRunnable,
            ThreadType.NORMAL_THREAD,
            ThreadPriority.HIGH
        )
    }

    override fun isRecycleBinFragment(fragment: Any?): Boolean {
        return fragment is RecycleBinNewFragment
    }

    override fun getFragment(activity: Activity): Fragment {
        Log.d(TAG, "getFragment")
        return RecycleBinNewFragment()
    }

    override fun onResumeLoadData(fragment: Fragment) {
        Log.d(TAG, "onResumeLoadData")
        if (fragment is RecycleBinNewFragment) {
            fragment.onResumeLoadData()
        }
    }

    override fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater) {
        Log.d(TAG, "onCreateOptionsMenu")
        if (fragment is RecycleBinNewFragment) {
            fragment.onCreateOptionsMenu(menu, inflater)
        }
    }

    override fun onMenuItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onMenuItemSelected")
        if (fragment is RecycleBinNewFragment) {
            return fragment.onMenuItemSelected(item)
        }
        return false
    }

    override fun pressBack(fragment: Fragment): Boolean {
        Log.d(TAG, "pressBack")
        if (fragment is RecycleBinNewFragment) {
            return fragment.pressBack()
        }
        return false
    }

    override fun onNavigationItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onNavigationItemSelected")
        if (fragment is RecycleBinNewFragment) {
            return fragment.onNavigationItemSelected(item)
        }
        return false
    }

    override fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        if (fragment is RecycleBinNewFragment) {
            fragment.setIsHalfScreen(isHalfScreen)
        }
    }

    override fun backToTop(fragment: Fragment) {
        Log.d(TAG, "backToTop")
        if (fragment is RecycleBinNewFragment) {
            fragment.backToTop()
        }
    }

    override fun setToolbarAndTabListener(
        fragment: Fragment,
        toolbar: COUIToolbar?,
        title: String,
        tabListener: TabActivityListener<MediaFileWrapper>
    ) {
        //do nothing
    }

    override fun fromSelectPathResult(fragment: Fragment, requestCode: Int, paths: List<String>?) {
        //do nothing
    }

    override fun updateLabels(fragment: Fragment) {
        //do nothing
    }

    override fun permissionSuccess(fragment: Fragment) {
        //do nothing
    }

    override fun setCurrentFilePath(fragment: Fragment, path: String?) {
        //do nothing
    }

    override fun getCurrentPath(fragment: Fragment): String {
        return ""
    }

    override fun exitSelectionMode(fragment: Fragment) {
        if (fragment is RecycleBinNewFragment) {
            fragment.exitSelectionMode()
        }
    }

    override fun onSideNavigationClicked(fragment: Fragment, isOpen: Boolean): Boolean {
        if (fragment is RecycleBinNewFragment) {
            return fragment.onSideNavigationClicked(isOpen)
        }
        return false
    }
}