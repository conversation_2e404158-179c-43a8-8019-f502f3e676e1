/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail
 ** Version: 1.0
 ** Date: 2021/5/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail

import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import androidx.annotation.Keep
import com.bumptech.glide.load.Encoder
import com.bumptech.glide.load.Options
import com.bumptech.glide.load.data.BufferedOutputStream
import com.bumptech.glide.load.engine.bitmap_recycle.ArrayPool
import com.bumptech.glide.load.resource.bitmap.BitmapEncoder
import com.bumptech.glide.util.LogTime
import com.bumptech.glide.util.Util
import com.filemanager.thumbnail.utils.Log
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.OutputStream

@Keep
class SourceBitmapEncoder(private val mArrayPool: ArrayPool? = null) : Encoder<Bitmap> {

    companion object {
        private const val TAG = "BitmapEncoder"
    }

    override fun encode(data: Bitmap, file: File, options: Options): Boolean {
        val format = getFormat(data, options)
        Log.d(TAG, String.format("encode: [%dx%d] %s", data.width, data.height, format))

        val start = LogTime.getLogTime()
        val quality = options.get(BitmapEncoder.COMPRESSION_QUALITY)!!
        var success = false
        var os: OutputStream? = null
        try {
            os = FileOutputStream(file)
            if (mArrayPool != null) {
                os = BufferedOutputStream(os, mArrayPool)
            }
            data.compress(format, quality, os)
            os.close()
            success = true
        } catch (e: IOException) {
            Log.e(TAG, "encode: Failed to encode Bitmap", e)
        } finally {
            if (os != null) {
                try {
                    os.close()
                } catch (e: IOException) {
                    // Do nothing.
                }
            }
        }
        Log.d(TAG, "Compressed with type: $format"
                + " of size ${Util.getBitmapByteSize(data)}"
                + " in ${LogTime.getElapsedMillis(start)}"
                + ", options format: ${options.get(BitmapEncoder.COMPRESSION_FORMAT)}"
                + ", hasAlpha: ${data.hasAlpha()}")
        return success
    }

    private fun getFormat(bitmap: Bitmap, options: Options): CompressFormat {
        val format = options.get(BitmapEncoder.COMPRESSION_FORMAT)
        return format
                ?: if (bitmap.hasAlpha()) {
                    CompressFormat.PNG
                } else {
                    CompressFormat.JPEG
                }
    }
}