package com.oplus.filemanager.cardwidget.recent.setting

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.cardlist.COUICardListHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.stringResource
import com.oplus.filemanager.cardwidget.R
import com.oplus.filemanager.cardwidget.label.dialog.OnItemClickListener
import com.oplus.filemanager.cardwidget.recent.data.RecentPreviewItem
import com.oplus.filemanager.cardwidget.recent.data.isSamePreviewItem
import io.mockk.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config

/**
 * RecentListAdapter的单元测试类
 * 用于测试RecentListAdapter的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29]) // 设置测试运行环境为Android SDK 29
class RecentListAdapterTest {

    // 测试所需的成员变量
    private lateinit var context: Context
    private lateinit var adapter: RecentListAdapter
    private lateinit var mockList: List<RecentPreviewItem>
    private lateinit var mockPreviewItem: RecentPreviewItem
    
    /**
     * 在每个测试方法执行前的初始化方法
     * 用于设置测试环境和初始化测试数据
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this) // 初始化MockK注解
        context = RuntimeEnvironment.application // 获取运行时上下文
        // 创建模拟的预览项
        mockPreviewItem = RecentPreviewItem(CategoryHelper.CATEGORY_RECENT, "Recent Files")
        // 创建模拟的列表数据
        mockList = listOf(
            RecentPreviewItem(CategoryHelper.CATEGORY_RECENT, "Recent Files"),
            RecentPreviewItem(CategoryHelper.CATEGORY_IMAGE, "Images"),
            RecentPreviewItem(CategoryHelper.CATEGORY_COMPRESS, "Compressed Files")
        )
        // 初始化被测试的适配器
        adapter = RecentListAdapter(context, mockPreviewItem, mockList)
    }
    
    /**
     * 在每个测试方法执行后的清理方法
     * 用于释放资源和清理Mock对象
     */
    @After
    fun tearDown() {
        unmockkAll() // 清除所有Mock对象
    }
    
    /**
     * 测试构造函数是否正确初始化
     */
    @Test
    fun `constructor initializes correctly`() {
        assertNotNull(adapter) // 验证适配器不为空
        assertEquals(mockList.size, adapter.itemCount) // 验证列表项数量是否正确
    }
    
    /**
     * 测试getItemCount方法返回正确的列表大小
     */
    @Test
    fun `getItemCount returns correct list size`() {
        assertEquals(mockList.size, adapter.getItemCount()) // 验证返回的列表大小与预期一致
    }
    
    /**
     * 测试selectLabel方法在选择相同位置时不执行任何操作
     */
    @Test
    fun `selectLabel does nothing when selecting same position`() {
        val samePosition = 0 // 设置相同的位置
        val spyAdapter = spyk(adapter) // 创建适配器的spy对象
        // 手动设置selectIndex以模拟当前选择
        spyAdapter.selectLabel(samePosition, mockList[samePosition])
        clearMocks(spyAdapter, answers = false) // 清除之前的mock调用记录
        
        // 再次选择相同位置
        spyAdapter.selectLabel(samePosition, mockList[samePosition])
        
        // 验证没有调用notifyItemChanged方法
        verify(exactly = 0) { spyAdapter.notifyItemChanged(any()) }
    }
    
    /**
     * 测试isSamePreviewItem方法对于相同类型返回true
     */
    @Test
    fun `isSamePreviewItem returns true for same types`() {
        // 创建两个类型相同的预览项
        val item1 = RecentPreviewItem(CategoryHelper.CATEGORY_RECENT, "Item 1")
        val item2 = RecentPreviewItem(CategoryHelper.CATEGORY_RECENT, "Item 2")
        
        // 验证返回true
        assertTrue(item1.isSamePreviewItem(item2))
    }
    
    /**
     * 测试isSamePreviewItem方法对于不同类型返回false
     */
    @Test
    fun `isSamePreviewItem returns false for different types`() {
        // 创建两个类型不同的预览项
        val item1 = RecentPreviewItem(CategoryHelper.CATEGORY_RECENT, "Item 1")
        val item2 = RecentPreviewItem(CategoryHelper.CATEGORY_IMAGE, "Item 2")
        
        // 验证返回false
        assertFalse(item1.isSamePreviewItem(item2))
    }
}