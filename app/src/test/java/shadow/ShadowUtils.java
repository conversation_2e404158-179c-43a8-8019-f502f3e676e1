/***************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * Description:Unit test
 * All rights reserved
 *
 *
 * Version: 1.0
 * Date : 2019/7/22
 * <EMAIL>
 *
 * -------------------Revision History: ----------------------
 *     <author>    <data>     <version >     <desc>
 *   lanjiabo    2019/7/22   1.0            build this moudle
 ****************************************************************/
package shadow;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import com.filemanager.common.helper.FileWrapper;
import com.filemanager.common.utils.Utils;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.util.ArrayList;
import java.util.List;

@Implements(Utils.class)
public class ShadowUtils {
    private static final String TAG = "ShadowUtils";
    private static boolean sIsPackageEncryptSupportOnly;
    private static boolean sCurrentZoomWindowState;
    private static boolean sIsOperateDatabase;
    private static List<String> sMatchedFileForPath;
    private static Uri sFileUri;
    private static boolean sIsQuickClick = false;
    private static int sUserId = 0;

    @Implementation
    public static boolean isContainerUser(Context context) {
        return false;
    }

    @Implementation
    public static boolean isInstalledGallery(Context context) {
        return false;
    }

    @Implementation
    public static boolean openRecentFile(Context context, FileWrapper file, ArrayList<FileWrapper> fileList, boolean isSerach,
                                         boolean fromEncryption, int fileType, boolean openFlag, boolean isFromRecnet) {
        return false;
    }

    @Implementation
    public static boolean isPackageEncryptSupportOnly(Context context) {
        return sIsPackageEncryptSupportOnly;
    }

    @Implementation
    public static boolean getCurrentZoomWindowState() {
        return sCurrentZoomWindowState;
    }

    @Implementation
    public static boolean isOperateDatabase(Context context, String path) {
        return sIsOperateDatabase;
    }

    @Implementation
    public static List<String> getMatchedFileForPath(String ubifocusFilePath) {
        return sMatchedFileForPath;
    }

    @Implementation
    public static Uri getFileUri(Context context, FileWrapper file, Intent intent) {
        return sFileUri;
    }

    @Implementation
    public static boolean isNightMode(Context context) {
        return true;
    }

    public static void setsMatchedFileForPath(List<String> matchedFileForPath) {
        sMatchedFileForPath = matchedFileForPath;
    }
    public static void setsIsOperateDatabase(boolean isOperateDatabase) {
        sIsOperateDatabase = isOperateDatabase;
    }

    public static void setmCurrentZoomWindowState(boolean currentZoomWindowState) {
        sCurrentZoomWindowState = currentZoomWindowState;
    }

    public static void setmIsPackageEncryptSupportOnly(boolean isPackageEncryptSupportOnly) {
        sIsPackageEncryptSupportOnly = isPackageEncryptSupportOnly;
    }

    public static boolean isQuickClick(int clickType) {
        return sIsQuickClick;
    }

    public static boolean isQuickClick() {
        return sIsQuickClick;
    }

    public static void setQuickClick(boolean isQuickClick) {
        sIsQuickClick = isQuickClick;
    }

    public static void setUserId(int userId) {
        sUserId = userId;
    }

    public static int getUserId() {
        return sUserId;
    }
}
