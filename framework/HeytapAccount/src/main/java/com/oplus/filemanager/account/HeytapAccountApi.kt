/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : HeytapAccountApi
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/8 16:26
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.account

import android.content.Context
import com.filemanager.common.bean.TransitionCloudAccount
import com.oplus.filemanager.interfaze.heytapaccount.IHeytapAccount

class HeytapAccountApi : IHeytapAccount {

    override fun initAccountClient(context: Context) {
        AccountManager.initSdk(context)
    }

    override fun isLogin(context: Context): Boolean {
        return AccountManager.isLogin(context)
    }

    override fun getUserCountry(context: Context): String? {
        return AccountManager.getUserCountry(context)
    }

    override fun login(context: Context, isShowAcPage: Boolean, callback: (Boolean) -> Unit) {
        AccountManager.login(context, isShowAcPage, callback)
    }

    override fun getUserToken(context: Context): String {
        return AccountManager.getUserToken(context)
    }

    override fun getUserId(context: Context): String? {
        return AccountManager.getUserId(context)
    }

    override fun getUserName(context: Context): String? {
        return AccountManager.getUserName(context)
    }

    override fun reqSignInAccount(context: Context, callback: (account: TransitionCloudAccount) -> Unit) {
        return AccountManager.reqSignInAccount(context, callback)
    }
}