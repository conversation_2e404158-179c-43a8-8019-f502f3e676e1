/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SelectShortcutFolderLoader
 * * Description : SelectShortcutFolderLoader
 * * Version     : 1.0
 * * Date        : 2024/10/25
 * * Author      : chao.xue
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir.filebrowser

import android.content.Context
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.Injector
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.interfaze.shortcutfolder.IShortcutFolderApi
import com.oplus.selectdir.SelectPathLoader
import java.io.File

class SelectShortcutFolderLoader(context: Context, path: String) : SelectPathLoader(context, path) {

    override fun createFromPath(volume: String, parentPath: String, path: String): List<BaseFileBean>? {
        val file = PathFileWrapper(parentPath.plus(File.separator).plus(path))
        if (file.mIsDirectory) {
            return arrayListOf(file)
        }
        return null
    }

    override fun preHandleResultBackground(list: List<BaseFileBean>): List<BaseFileBean> {
        val currentSort =
            SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getSelectPathKey())
        val isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getSelectPathKey())
        val mLastSort =
            SortModeUtils.getSharedSortMode(appContext, SortModeUtils.BROWSER_LAST_SORT_RECORD)
        Injector.injectFactory<IDocumentExtensionType>()
            ?.sortFiles(list, currentSort, mLastSort, true, isDesc)
        val shortcutFolderApi = Injector.injectFactory<IShortcutFolderApi>()
        shortcutFolderApi?.isAddShortcutFolder(list)
        return list
    }
}