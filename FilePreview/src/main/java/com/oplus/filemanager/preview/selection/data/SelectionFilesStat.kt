/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SelectionFilesStat.kt
 * Description:
 *     The stat data for selection files
 *
 * Version: 1.0
 * Date: 2024-10-21
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-10-21   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection.data

import androidx.annotation.IntDef

internal data class SelectionFilesStat(
    val sumSize: Long,
    val selectionCount: Int,
    val typesCountMap: Map<Int, Int>
) {

    fun getCountForType(@StateFileTypes type: Int): Int = typesCountMap[type] ?: -1

    companion object {
        const val TYPE_OTHER = 0
        const val TYPE_DIR = 1
        const val TYPE_IMAGE = 2
        const val TYPE_VIDEO = 3
        const val TYPE_AUDIO = 4
        const val TYPE_DOC = 5
        const val TYPE_APK = 6
        const val TYPE_ARCHIVE = 7
    }

    @IntDef(
        TYPE_OTHER,
        TYPE_DIR,
        TYPE_IMAGE,
        TYPE_VIDEO,
        TYPE_AUDIO,
        TYPE_DOC,
        TYPE_APK,
        TYPE_ARCHIVE
    )
    @Retention(AnnotationRetention.SOURCE)
    annotation class StateFileTypes
}