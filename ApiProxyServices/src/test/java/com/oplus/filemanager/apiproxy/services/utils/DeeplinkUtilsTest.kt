package com.oplus.filemanager.apiproxy.services.utils

import android.content.Intent
import android.net.Uri
import com.filemanager.common.utils.Log
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * DeeplinkUtils 的单元测试类
 * 用于测试 DeeplinkUtils 工具类的各种功能场景
 */
@Config(sdk = [29])
@RunWith(RobolectricTestRunner::class)
class DeeplinkUtilsTest {

    // 模拟的 Intent 对象
    private lateinit var mockIntent: Intent
    // 模拟的 Uri 对象
    private lateinit var mockUri: Uri

    /**
     * 测试前的初始化方法
     * 1. 模拟 Log 类的静态方法
     * 2. 初始化模拟的 Intent 和 Uri 对象
     */
    @Before
    fun setUp() {
        // 模拟 Log 类的静态方法
        mockkStatic(Log::class)
        // 设置 Log.d() 方法总是返回 Unit
        every { Log.d(any(), any()) } returns Unit
        // 创建宽松的模拟 Intent 对象
        mockIntent = mockk(relaxed = true)
        // 创建宽松的模拟 Uri 对象
        mockUri = mockk(relaxed = true)
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟对象的绑定
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试当 Intent 的 action 不是 ACTION_VIEW 时的情况
     * 预期结果: 返回 null
     */
    @Test
    fun `getValueFromDeepLink should return null when intent action is not ACTION_VIEW`() {
        // Given - 准备测试数据
        every { mockIntent.action } returns "OTHER_ACTION"
        val key = "test_key"

        // When - 执行测试方法
        val result = DeeplinkUtils.getValueFromDeepLink(mockIntent, key)

        // Then - 验证结果
        assert(result == null)
        // 验证日志是否正确打印
        verify(exactly = 1) { Log.d("DeeplinkUtils", "getValueFromDeepLink $key -> null") }
    }

    /**
     * 测试当 Uri 为 null 时的情况
     * 预期结果: 返回 null
     */
    @Test
    fun `getValueFromDeepLink should return null when uri is null`() {
        // Given
        every { mockIntent.action } returns Intent.ACTION_VIEW
        every { mockIntent.data } returns null
        val key = "test_key"

        // When
        val result = DeeplinkUtils.getValueFromDeepLink(mockIntent, key)

        // Then
        assert(result == null)
        verify(exactly = 1) { Log.d("DeeplinkUtils", "getValueFromDeepLink $key -> null") }
    }

    /**
     * 测试当 Uri 包含指定 key 时的情况
     * 预期结果: 返回对应的值
     */
    @Test
    fun `getValueFromDeepLink should return query parameter when uri has the key`() {
        // Given
        val expectedValue = "test_value"
        val key = "test_key"
        every { mockIntent.action } returns Intent.ACTION_VIEW
        every { mockIntent.data } returns mockUri
        every { mockUri.getQueryParameter(key) } returns expectedValue

        // When
        val result = DeeplinkUtils.getValueFromDeepLink(mockIntent, key)

        // Then
        assert(result == expectedValue)
        verify(exactly = 1) { Log.d("DeeplinkUtils", "getValueFromDeepLink $key -> $expectedValue") }
    }

    /**
     * 测试当 Uri 不包含指定 key 时的情况
     * 预期结果: 返回 null
     */
    @Test
    fun `getValueFromDeepLink should return null when uri does not have the key`() {
        // Given
        val key = "test_key"
        every { mockIntent.action } returns Intent.ACTION_VIEW
        every { mockIntent.data } returns mockUri
        every { mockUri.getQueryParameter(key) } returns null

        // When
        val result = DeeplinkUtils.getValueFromDeepLink(mockIntent, key)

        // Then
        assert(result == null)
        verify(exactly = 1) { Log.d("DeeplinkUtils", "getValueFromDeepLink $key -> null") }
    }

    /**
     * 测试当 key 为空字符串时的情况
     * 预期结果: 返回 null
     */
    @Test
    fun `getValueFromDeepLink should handle empty key`() {
        // Given
        val key = ""
        every { mockIntent.action } returns Intent.ACTION_VIEW
        every { mockIntent.data } returns mockUri
        every { mockUri.getQueryParameter(key) } returns null

        // When
        val result = DeeplinkUtils.getValueFromDeepLink(mockIntent, key)

        // Then
        assert(result == null)
        verify(exactly = 1) { Log.d("DeeplinkUtils", "getValueFromDeepLink $key -> null") }
    }
}