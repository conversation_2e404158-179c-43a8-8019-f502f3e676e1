/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SimulateClickService.kt
 * Description:
 *     The service to request accessibility permission.
 *
 * Version: 1.0
 * Date: 2024-05-23
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-05-23   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.service

import android.accessibilityservice.AccessibilityService
import android.content.Intent
import android.os.Handler
import android.os.HandlerThread
import android.view.accessibility.AccessibilityEvent
import com.oplus.filemanager.simulateclick.engine.utils.Log
import com.oplus.filemanager.simulateclick.request.ScriptExecHelper
import com.oplus.filemanager.simulateclick.request.ScriptExecRequest

internal class SimulateClickService : AccessibilityService() {

    private val serviceBridge = AccessibilityServiceBridge()
    private val serviceReceiver = SimulateClickReceiver(this)
    private val scriptExecHelper = ScriptExecHelper(this, serviceBridge)
    private val idleCheckRunnable = Runnable { onIdleCheck() }

    private lateinit var handlerThread: HandlerThread
    private lateinit var requestHandler: Handler

    override fun onCreate() {
        Log.d(TAG, "onCreate")
        super.onCreate()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
    }

    override fun onServiceConnected() {
        super.onServiceConnected()
        Log.d(TAG, "onServiceConnected")
        handlerThread = HandlerThread(SIMULATE_THREAD_NAME).apply { start() }
        requestHandler = Handler(handlerThread.looper)
        triggerIdleCheck()
        serviceBridge.onServiceConnected(this)
        ScriptServiceManager.onServiceConnected(this)
        serviceReceiver.register()
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Log.d(TAG, "onUnbind")
        serviceReceiver.unregister()
        ScriptServiceManager.onServiceDisconnected(this)
        serviceBridge.onServiceDisconnected(this)
        handlerThread.quitSafely()
        return super.onUnbind(intent)
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // Do nothing at current
    }

    override fun onInterrupt() {
        Log.d(TAG, "onInterrupt")
    }

    fun executeScript(request: ScriptExecRequest) {
        if (!::requestHandler.isInitialized) {
            Log.e(TAG, "executeScript: ERROR! requestHandler is not initialized")
            return
        }
        Log.d(TAG, "executeScript: post request=$request")
        cancelIdleCheck()
        requestHandler.post {
            scriptExecHelper.executeScript(handlerThread, request)
            triggerIdleCheck()
        }
    }

    private fun triggerIdleCheck() {
        Log.d(TAG, "triggerIdleCheck")
        requestHandler.postDelayed(idleCheckRunnable, IDLE_CHECK_DELAY)
    }

    private fun cancelIdleCheck() {
        Log.d(TAG, "cancelIdleCheck")
        requestHandler.removeCallbacks(idleCheckRunnable)
    }

    private fun onIdleCheck() {
        if (scriptExecHelper.checkHasExecuting()) {
            Log.d(TAG, "onIdleCheck: still has executing scripts")
            triggerIdleCheck()
            return
        }
        Log.d(TAG, "onIdleCheck: no executing scripts")
        ScriptServiceManager.stopScriptService(this)
    }

    private companion object {
        private const val TAG = "SimulateClickService"
        private const val SIMULATE_THREAD_NAME = "simulate_click"
        private const val IDLE_CHECK_DELAY = 30 * 1000L
    }
}