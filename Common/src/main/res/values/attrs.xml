<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="FileManagerRecyclerView">
        <attr format="boolean" name="fastScrollBarEnabled" />
        <attr format="reference" name="fastScrollBarVerticalThumbDrawable" />
        <attr format="reference" name="fastScrollBarVerticalTrackDrawable" />
        <attr format="reference" name="fastScrollBarHorizontalThumbDrawable" />
        <attr format="reference" name="fastScrollBarHorizontalTrackDrawable" />
    </declare-styleable>
    <declare-styleable name="PrivacyPolicyContentScrollView">
        <attr name="max_height" format="dimension" />
    </declare-styleable>
    <declare-styleable name="FileThumbView">
        <attr name="oplus_thumb_stroke_style" format="enum">
            <enum name="none" value="0" />
            <enum name="radius" value="1" />
            <enum name="rect" value="2" />
            <enum name="four" value="3" />
        </attr>
        <attr name="border_size" format="reference" />
        <attr name="border_radius" format="reference" />
        <attr name="border_color" format="color" />
    </declare-styleable>

    <declare-styleable name="SelectListItemView">
        <attr name="state_list_checked" format="boolean"/>
    </declare-styleable>
    <declare-styleable name="SmoothRoundedCornersConstraintLayout">
        <attr name="default_background" format="boolean"/>
        <attr name="solid_color" format="reference|color"/>
        <attr name="corner_radius" format="reference|dimension"/>
    </declare-styleable>

    <declare-styleable name="CommonRecyclerViewFastScroller">
        <attr name="common_scroller_enable" format="boolean" />
        <attr name="common_scroller_visible" format="boolean" />

        <attr name="common_track_drawable" format="reference" />
        <attr name="common_track_marginStart" format="dimension" />
        <attr name="common_track_marginEnd" format="dimension" />
        <attr name="common_track_marginTop" format="dimension" />
        <attr name="common_track_marginBottom" format="dimension" />

        <attr name="common_thumb_drawable" format="reference" />
        <attr name="common_thumb_width" format="dimension" />
        <attr name="common_thumb_height" format="dimension" />
        <attr name="common_thumb_visibleDuration" format="integer" />
    </declare-styleable>

    <declare-styleable name="mediaframelayout">
        <attr name="isLargeLayout" format="boolean" />
        <attr name="layout_radius" format="dimension"/>
    </declare-styleable>

    <declare-styleable name="HoverAwareLayout">
        <attr name="hover_radius" format="dimension" />
    </declare-styleable>

</resources>