package com.coloros.filemanager.utils

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.snackbar.COUISnackBar
import com.filemanager.common.utils.COUISnackBarUtils
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test

class COUISnackBarUtilsTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

    }

    @Test
    fun `should verify when show without`() {
        var activity = mockk<Activity>()
        every { activity.isFinishing }.returns(false)
        every { activity.isDestroyed }.returns(false)
        every { activity.window }.returns(null)

        var click = View.OnClickListener {
        }
        every { activity.getString(any()) }.returns("test")
        COUISnackBarUtils.show(activity, 123, click)

        every { activity.window }.returns(mockk(relaxed = true))

        every { activity.findViewById<ViewGroup>(android.R.id.content) }.returns(null)
        COUISnackBarUtils.show(activity, "test", click)


        mockkStatic(COUISnackBar::class)
        every { COUISnackBar.make(any(),"test",any()) }.returns(mockk(relaxed = true))
        every { activity.findViewById<ViewGroup>(android.R.id.content) }.returns(mockk(relaxed = true))
        COUISnackBarUtils.show(activity, "test", click)
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}