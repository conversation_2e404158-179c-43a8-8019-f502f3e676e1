/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DownloadTaskInfo
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/9 15:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/9       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.download

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class DownloadTaskInfo(
    var category: Int = 0,
    var fileId: String = "",
    var targetPath: String = "",
    var exportType: String? = null
) : Parcelable {
    override fun toString(): String {
        return "DownloadTaskInfo(category=$category, fileId='$fileId', exportType='$exportType')"
    }
}