/***************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * Description:Unit test shadow class
 * Version: 1.0
 * Date : 2019/8/03
 * <EMAIL>
 *
 * -------------------Revision History: ----------------------
 *     <author>    <data>     <version >     <desc>
 *   lanjiabo    2019/8/03   1.0            build this moudle
 ****************************************************************/
package shadow;

import com.coui.appcompat.dateutils.COUIDateUtils;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.util.Date;

@Implements(COUIDateUtils.class)
public class ShadowColorDateUtils {
    private static String sYMDDate;

    private static String sTime;

    private static String sMDDate;

    @Implementation
    public String getYMDDate(Date var1, boolean var2) {
        return sYMDDate;
    }

    @Implementation
    public String getTime(Date var1) {
        return sTime;
    }

    @Implementation
    public String getMDDate(Date var1, boolean var2) {
        return sMDDate;
    }

    public static void setsYMDDate(String date) {
        sYMDDate = date;
    }

    public static void setsTime(String time) {
        sTime = time;
    }

    public static void setsMDDate(String date) {
        sMDDate = date;
    }
}
