/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : FileBrowserApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/5/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.annotation.VisibleForTesting
import androidx.fragment.app.Fragment
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewListFragmentCreator
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filebrowser.morestorage.MoreStorageFragment
import com.oplus.filebrowser.otg.OtgFileBrowserActivity
import com.oplus.filebrowser.otg.OtgFileBrowserActivity.Companion.OTG_LIST_PATH
import com.oplus.filebrowser.otg.OtgFileFragment
import com.oplus.filemanager.dfm.DFMManager
import com.oplus.filemanager.interfaze.categorydfm.ICategoryDFMApi
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.main.IMain

object FileBrowserApi : IFileBrowser {
    private const val TAG = "FileBrowserApi"

    override fun startOtgBrowserFragment(
        activity: Activity,
        pathList: List<String>?,
        fromDetail: Boolean,
        fromOTGList: Boolean
    ) {
        val paths = pathList?.let { ArrayList(it) } ?: ArrayList()
        val bundle = Bundle().apply {
            putStringArrayList(KtConstants.P_PATH_LIST, paths)
            putInt(Constants.TITLE_RES_ID, com.filemanager.common.R.string.storage_otg)
            putString(Constants.TITLE, activity.getString(com.filemanager.common.R.string.storage_otg))
            if (fromDetail) {
                putBoolean(KtConstants.FROM_DETAIL, true)
            }
            if (fromOTGList) {
                putBoolean(KtConstants.FROM_OTG_LIST, true)
            }
        }
        val category = if (paths.size > 1) CategoryHelper.CATEGORY_MORE_STORAGE else CategoryHelper.CATEGORY_OTG_BROWSER
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.startFragment(activity, category, bundle)
    }

    override fun startSdCardBrowserFragment(
        activity: Activity,
        path: String?,
        fromDetail: Boolean
    ) {
        val bundle = Bundle().apply {
            putString(KtConstants.CURRENT_DIR, path)
            putInt(Constants.TITLE_RES_ID, com.filemanager.common.R.string.storage_external)
            putString(Constants.TITLE, activity.getString(com.filemanager.common.R.string.storage_external))
            putInt(Constants.SELECTED_ITEM, -1)
            if (fromDetail) {
                putBoolean(KtConstants.FROM_DETAIL, true)
            }
        }
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.startFragment(activity, CategoryHelper.CATEGORY_SDCARD_BROWSER, bundle)
    }

    override fun startFileBrowserFragment(activity: Activity, path: String?, fromDetail: Boolean, isFromShortcutFolder: Boolean) {
        val bundle = Bundle().apply {
            putString(KtConstants.CURRENT_DIR, path)
            if (fromDetail) {
                putBoolean(KtConstants.FROM_DETAIL, true)
            }
            if (isFromShortcutFolder) {
                putBoolean(KtConstants.FROM_SHORTCUT_FOLDER, true)
            }
        }
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.startFragment(activity, CategoryHelper.CATEGORY_FILE_BROWSER, bundle)
    }

    override fun startOtgBrowserActivity(
        activity: Activity,
        pathList: List<String>?,
        needClearTop: Boolean,
        fromDetail: Boolean
    ) {
        if (pathList.isNullOrEmpty()) {
            Log.w(TAG, "startOtgActivity failed: path list is empty")
            return
        }
        val intent = Intent(activity, OtgFileBrowserActivity::class.java)
        val otgList: ArrayList<String> = ArrayList()
        otgList.addAll(pathList)
        intent.putStringArrayListExtra(OTG_LIST_PATH, otgList)
        intent.putExtra(Constants.TITLE_RES_ID, com.filemanager.common.R.string.storage_otg)
        intent.putExtra(
            Constants.TITLE,
            activity.getString(com.filemanager.common.R.string.storage_otg)
        )
        intent.action = Intent.ACTION_VIEW
        intent.putExtra(KtConstants.FROM_DETAIL, fromDetail)
        if (needClearTop) { // Bug: http://alm.adc.com/OPPO/FileManager/_workitems/edit/485724/
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        activity.startActivity(intent)
    }

    override fun startFileBrowserActivity(
        activity: Activity,
        path: String?,
        needClearTop: Boolean,
        fromDetail: Boolean,
        isFromShortcutFolder: Boolean
    ) {
        val intent = Intent(activity, FileBrowserActivity::class.java)
        intent.putExtra(KtConstants.CURRENT_DIR, path)
        intent.action = Intent.ACTION_VIEW
        if (needClearTop) { // Bug: http://alm.adc.com/OPPO/FileManager/_workitems/edit/485724/
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        intent.putExtra(KtConstants.FROM_DETAIL, fromDetail)
        intent.putExtra(KtConstants.FROM_SHORTCUT_FOLDER, isFromShortcutFolder)
        activity.startActivity(intent)
    }

    override fun startFileLabelBrowserActivity(activity: Activity, path: String?) {
        val intent = Intent(activity, FileBrowserActivity::class.java)
        intent.putExtra(KtConstants.CURRENT_DIR, path)
        intent.action = Intent.ACTION_VIEW
        // add below means from browse activity will directly back to start page, not back to each parent directory
        intent.putExtra(KtConstants.FROM_DETAIL, true)
        activity.startActivity(intent)
    }

    override fun toFileBrowserActivity(
        activity: Activity,
        path: String,
        needClearTop: Boolean,
        fromDetail: Boolean
    ) {
        Log.d(TAG, "toFileBrowserActivity $activity, path $path, needClearTop $needClearTop, fromDetail $fromDetail")
        if (!VolumeEnvironment.isMounted(activity, path)) {
            //the file path is not exist. for example, the OTG was removed
            CustomToast.showShort(com.filemanager.common.R.string.toast_file_path_not_exist)
            return
        }

        if (isFromShortcutFolder(activity)) {
            exitShortcutFolder(activity)
        }

        val mainAction = Injector.injectFactory<IMain>()
        if (mainAction?.isMainActivity(activity) == true) {
            mainAction.exitEditMode(activity)
        }
        var toOtgFileBrowser = false
        if (KtUtils.checkIsOTGPath(activity, path)) {
            val otgList = ArrayList<String>()
            otgList.add(path)
            if ((mainAction?.isParentChildActivity(activity) == true) && mainAction.getMainTab(activity) == 1) {
                startOtgBrowserFragment(activity, otgList, fromDetail)
            } else {
                startOtgBrowserActivity(activity, otgList, needClearTop, fromDetail)
            }
            toOtgFileBrowser = true
        } else if (KtUtils.checkIsDfmPath(path)) {
            val deviceName = DFMManager.getDFSDeviceName() ?: ""
            Log.d(TAG, "toFileBrowserActivity deviceName $deviceName path $path, ")
            Injector.injectFactory<ICategoryDFMApi>()?.startDFM(activity, deviceName, path, fromDetail)
        } else {
            if ((mainAction?.isParentChildActivity(activity) == true) && mainAction.getMainTab(activity) == 1) {
                startFileBrowserFragment(activity, path, fromDetail)
            } else {
                startFileBrowserActivity(activity, path, needClearTop, fromDetail)
            }
        }
        if (isFileBrowserActivity(activity) && toOtgFileBrowser) {
            activity.finish()
        } else if (isOtgBrowserActivity(activity) && !toOtgFileBrowser) {
            activity.finish()
        }
    }

    override fun exitShortcutFolder(activity: Activity) {
        if (activity is FileBrowserActivity) {
            activity.exitShortcutFolder()
        } else {
            val mainAction = Injector.injectFactory<IMain>()
            val fragment = mainAction?.getPreviewListFragment(activity)
            if (fragment is FileBrowserFragment) {
                Log.d(TAG, "exitShortcutFolderFromFragment")
                fragment.resetIsFromShortcut()
                fragment.setCurrentFromOtherSide(fragment.getCurrentPath())
            } else {
                Log.d(TAG, "not exitShortcutFolderFromFragment")
            }
        }
    }

    override fun isFromShortcutFolder(activity: Activity): Boolean {
        if (activity is FileBrowserActivity) {
            return activity.isFromShortcutFolder
        } else {
            val mainAction = Injector.injectFactory<IMain>()
            val fragment = mainAction?.getPreviewListFragment(activity)
            if (fragment is FileBrowserFragment) {
                return fragment.isFromShortcutFolder
            }
        }
        return false
    }

    private fun isOtgBrowserActivity(activity: Activity): Boolean {
        return activity is OtgFileBrowserActivity
    }

    @VisibleForTesting
    @JvmStatic
    fun isFileBrowserActivity(activity: Activity): Boolean {
        return activity is FileBrowserActivity
    }

    override fun getOTGFragment(activity: Activity?, type: Int): Fragment {
        Log.d(TAG, "getOTGFragment type = $type")
        return if (type == CategoryHelper.CATEGORY_MORE_STORAGE) {
            MoreStorageFragment()
        } else {
            val fragment = PreviewCombineFragment()
            fragment.setPreviewListFragmentCreator(object : IPreviewListFragmentCreator {
                override fun create(): IPreviewListFragment {
                    return OtgFileFragment()
                }
            })
            return fragment
        }
    }

    override fun getPhoneActivityCurrentPath(activity: Activity): String {
        Log.d(TAG, "getPhoneActivityCurrentPath activity = $activity")
        if (activity is FileBrowserActivity) {
            return activity.getCurrentPath()
        }
        if (activity is OtgFileBrowserActivity) {
            return activity.getCurrentPath()
        }
        return ""
    }

    override fun getCurrentFragmentPath(fragment: Fragment?): String? {
        if (fragment is FileBrowserFragment) {
            return fragment.getCurrentPath()
        }
        if (fragment is OtgFileFragment) {
            return fragment.getCurrentPath()
        }
        return null
    }

    override fun createPickerFileBrowserFragment(): Fragment {
        return PickerFileBrowserFragment()
    }

    override fun getFragment(activity: Activity): Fragment {
        Log.d(TAG, "getFileBrowserFragment")
        val fragment = PreviewCombineFragment()
        fragment.setPreviewListFragmentCreator(object : IPreviewListFragmentCreator {
            override fun create(): IPreviewListFragment {
                return FileBrowserFragment()
            }
        })
        return fragment
    }

    override fun getPickerFragment(activity: Activity?): Fragment? {
        Log.d(TAG, "getPickerFragment")
        return PickerShortCutFileBrowserFragment()
    }

    override fun onResumeLoadData(fragment: Fragment) {
        Log.d(TAG, "onResumeLoadData")
        if (fragment is PreviewCombineFragment) {
            fragment.onResumeLoadData()
        } else if (fragment is OtgFileFragment) {
            fragment.onResumeLoadData()
        }
    }

    override fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater) {
        Log.d(TAG, "onCreateOptionsMenu")
        if (fragment is PreviewCombineFragment) {
            fragment.onCreateOptionsMenu(menu, inflater)
        } else if (fragment is OtgFileFragment) {
            fragment.onCreateOptionsMenu(menu, inflater)
        }
    }

    override fun onMenuItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onMenuItemSelected")
        return when (fragment) {
            is PreviewCombineFragment -> fragment.onMenuItemSelected(item)

            is OtgFileFragment -> fragment.onMenuItemSelected(item)

            is MoreStorageFragment -> fragment.onMenuItemSelected(item)

            else -> false
        }
    }

    override fun pressBack(fragment: Fragment): Boolean {
        Log.d(TAG, "pressBack")
        return when (fragment) {
            is PreviewCombineFragment -> fragment.pressBack()

            is OtgFileFragment -> fragment.pressBack()

            else -> return false
        }
    }

    override fun onNavigationItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onNavigationItemSelected")
        if (fragment is PreviewCombineFragment) {
            return fragment.onNavigationItemSelected(item)
        } else if (fragment is OtgFileFragment) {
            return fragment.onNavigationItemSelected(item)
        }
        return false
    }

    override fun setToolbarAndTabListener(
        fragment: Fragment,
        toolbar: COUIToolbar?,
        title: String,
        tabListener: TabActivityListener<MediaFileWrapper>
    ) {
        //do nothing
    }

    override fun fromSelectPathResult(fragment: Fragment, requestCode: Int, paths: List<String>?) {
        Log.d(TAG, "fromSelectPathResult")
        if (fragment is PreviewCombineFragment) {
            fragment.fromSelectPathResult(requestCode, paths)
        } else if (fragment is OtgFileFragment) {
            fragment.fromSelectPathResult(requestCode, paths)
        }
    }

    override fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        when (fragment) {
            is PreviewCombineFragment -> fragment.setIsHalfScreen(isHalfScreen)

            is MoreStorageFragment -> fragment.setIsHalfScreen(isHalfScreen)

            is OtgFileFragment -> fragment.setIsHalfScreen(isHalfScreen)
        }
    }

    override fun permissionSuccess(fragment: Fragment) {
        Log.d(TAG, "permissionSuccess fragment:$fragment")
        if (fragment is PreviewCombineFragment) {
            fragment.permissionSuccess()
        }
    }

    override fun setCurrentFilePath(fragment: Fragment, path: String?) {
        if (path == null) {
            return
        }
        if (fragment is PreviewCombineFragment) {
            fragment.setCurrentFromOtherSide(path)
        } else if (fragment is OtgFileFragment) {
            fragment.setCurrentFromOtherSide(path)
        }
    }

    override fun backToTop(fragment: Fragment) {
        Log.d(TAG, "backToTop")
        if (fragment is PreviewCombineFragment) {
            fragment.backToTop()
        }
        if (fragment is OtgFileFragment) {
            fragment.backToTop()
        }
    }

    override fun getCurrentPath(fragment: Fragment): String {
        Log.d(TAG, "getCurrentPath")
        return when (fragment) {
            is PreviewCombineFragment -> fragment.getCurrentPath()

            is OtgFileFragment -> fragment.getCurrentPath()

            else -> ""
        }
    }

    override fun updateLabels(fragment: Fragment) {
        //do nothing
    }

    override fun isParentChildActivity(activity: Activity): Boolean {
        return false
    }

    override fun isStorageFragment(activity: Activity): Boolean {
        return false
    }

    override fun isRecentFragment(activity: Activity): Boolean {
        return false
    }

    override fun exitSelectionMode(fragment: Fragment) {
        if (fragment is PreviewCombineFragment) {
            fragment.exitSelectionMode()
        }
    }

    override fun onSideNavigationClicked(fragment: Fragment, isOpen: Boolean): Boolean {
        if (fragment is PreviewCombineFragment) {
            return fragment.onSideNavigationClicked(isOpen)
        }
        return false
    }
}