package com.oplus.filemanager.parentchild.bean

import android.graphics.drawable.ColorDrawable
import com.oplus.filemanager.room.model.FileLabelEntity
import org.junit.Assert.*
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * CategoryListBean 的单元测试类
 * 使用 Robolectric 测试框架在 Android 环境中运行测试
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class CategoryListBeanTest {

    /**
     * 测试 equals 方法 - 相同实例返回 true
     */
    @Test
    fun testEquals_SameInstance_ReturnsTrue() {
        val bean = CategoryListBean(1, 2)
        assertTrue(bean.equals(bean))
    }

    /**
     * 测试 equals 方法 - 不同类型对象返回 false
     */
    @Test
    fun testEquals_DifferentType_ReturnsFalse() {
        val bean = CategoryListBean(1, 2)
        assertFalse(bean.equals("not a CategoryListBean"))
    }

    /**
     * 测试 equals 方法 - 相同属性值返回 true
     */
    @Test
    fun testEquals_SameValues_ReturnsTrue() {
        val bean1 = CategoryListBean(1, 2).apply {
            name = "test"
            iconRes = 3
            subTitle = "sub"
            expanded = true
            superAppSwitch = true
        }
        val bean2 = CategoryListBean(1, 2).apply {
            name = "test"
            iconRes = 3
            subTitle = "sub"
            expanded = true
            superAppSwitch = true
        }
        assertTrue(bean1.equals(bean2))
    }

    /**
     * 测试 equals 方法 - 不同属性值返回 false
     */
    @Test
    fun testEquals_DifferentValues_ReturnsFalse() {
        val bean1 = CategoryListBean(1, 2)
        val bean2 = CategoryListBean(2, 2) // 不同的 categoryType
        assertFalse(bean1.equals(bean2))
    }

    /**
     * 测试 hashCode 方法 - 验证与 equals 方法的一致性
     */
    @Test
    fun testHashCode_ConsistentWithEquals() {
        val bean1 = CategoryListBean(1, 2).apply {
            name = "test"
            iconRes = 3
            subTitle = "sub"
            expanded = true
        }
        val bean2 = CategoryListBean(1, 2).apply {
            name = "test"
            iconRes = 3
            subTitle = "sub"
            expanded = true
        }
        assertEquals(bean1.hashCode(), bean2.hashCode())
    }

    /**
     * 测试 toString 方法 - 验证输出包含预期字段
     */
    @Test
    fun testToString_ContainsExpectedFields() {
        val bean = CategoryListBean(1, 2).apply {
            name = "test"
            packageName = "com.test"
            expanded = true
        }
        val str = bean.toString()
        assertTrue(str.contains("categoryType=1"))
        assertTrue(str.contains("layoutType=2"))
        assertTrue(str.contains("name='test'"))
        assertTrue(str.contains("expanded=true"))
        assertTrue(str.contains("packageName=com.test"))
    }

    /**
     * 测试 labelEntry 属性的设置和获取
     */
    @Test
    fun testLabelEntity_CanBeSetAndGet() {
        val bean = CategoryListBean(1, 2)
        val label = FileLabelEntity(1L, "label", 0, 0, 0L, 0L)
        bean.labelEntry = label
        assertEquals(label, bean.labelEntry)
    }

    /**
     * 测试 drawable 属性的设置和获取
     */
    @Test
    fun testDrawable_CanBeSetAndGet() {
        val bean = CategoryListBean(1, 2)
        val drawable = ColorDrawable(0xFF0000.toInt())
        bean.drawable = drawable
        assertEquals(drawable, bean.drawable)
    }

    /**
     * 测试 childCategoryList 列表的可修改性
     */
    @Test
    fun testChildCategoryList_CanBeModified() {
        val bean = CategoryListBean(1, 2)
        val child = CategoryListBean(3, 4)
        bean.childCategoryList.add(child)
        assertEquals(1, bean.childCategoryList.size)
        assertEquals(child, bean.childCategoryList[0])
    }

    /**
     * 测试伴生对象中定义的常量值是否正确
     */
    @Test
    fun testCompanionConstants_ValuesCorrect() {
        assertEquals(1, CategoryListBean.TYPE_EXPANDABLE)
        assertEquals(2, CategoryListBean.TYPE_OPTIONS)
        assertEquals(3, CategoryListBean.TYPE_FOOTER)
        assertEquals(4, CategoryListBean.TYPE_DIVIDER)
        assertEquals(5, CategoryListBean.TYPE_STORAGE_PANEL)
        assertEquals(6, CategoryListBean.TYPE_CLASS_PANEL)
        assertEquals(7, CategoryListBean.TYPE_SOURCE_PANEL)
        assertEquals(8, CategoryListBean.TYPE_FOLDER_PANEL)
        assertEquals(9, CategoryListBean.TYPE_LABEL_PANEL)
        assertEquals(10, CategoryListBean.TYPE_STORAGE)
        assertEquals(11, CategoryListBean.TYPE_EDIT_OPTIONS)
        assertEquals(12, CategoryListBean.TYPE_MENU_PANEL)
        assertEquals(13, CategoryListBean.TYPE_QUESTIONNAIRE)
    }
}