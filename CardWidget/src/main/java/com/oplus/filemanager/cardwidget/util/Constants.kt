/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Constants
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/9
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/9      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.util

/**
 * https://odocs.myoas.com/sheets/KlkKVZErl0Tp0Gqd/MODOC
 * psw:hxaa
 *
 * test env
 * coloros 2*2：222220024 2*4：222220025 4*4：222220026
 * oneplus 2*2：222220027 2*4：222220028 4*4：222220029
 *
 * **/
const val CARD_TYPE_2_TO_2_COLOROS = 222220024
const val CARD_TYPE_2_TO_4_COLOROS = 222220025
const val CARD_TYPE_4_TO_4_COLOROS = 222220026
const val CARD_TYPE_2_TO_2_ONEPLUS = 222220027
const val CARD_TYPE_2_TO_4_ONEPLUS = 222220028
const val CARD_TYPE_4_TO_4_ONEPLUS = 222220029

const val LAYOUT_2_TO_2_MAX_ITEM_COUNT = 2
const val LAYOUT_2_TO_4_MAX_ITEM_COUNT = 4
const val LAYOUT_4_TO_4_MAX_ITEM_COUNT = 9

const val ASSISTANT_SCREEN_PKG = "com.coloros.assistantscreen"
const val ANDROID_LAUNCHER_PKG = "com.android.launcher"
const val OPPO_LAUNCHER_PKG = "com.oppo.launcher"

const val OPEN_FILE_ACTION = "com.oplus.filemanager.cardwidget.open.file"
/**
 * 打开文管
 * key_is_from_label_card = "true" 跳转到标签tab
 * */
const val START_FILE_MANAGER_ACTION = "oplus.intent.action.filemanager.OPEN_FILEMANAGER"
/**
 * 打开标签文件列表
 *  传入TITLE 以及 labelId
 * */
const val START_LABEL_FILE_LIST = "oplus.intent.action.filemanager.LABEL_FILE_LIST"
/**
 * 打开指定文件夹-手机存储页面
 *  CurrentDir
 * */
const val START_FILE_BROWSER = "oplus.intent.action.filemanager.BROWSER_FILE"

/**
 * 选择标签对话框
 */
const val LABEL_SELECT_DIALOG = "com.oplus.filemanager.card.label.settings"