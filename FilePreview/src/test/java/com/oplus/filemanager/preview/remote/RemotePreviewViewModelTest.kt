package com.oplus.filemanager.preview.remote

import android.content.Context
import android.net.Uri
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.remotedevice.provider.ThumbnailProvider
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import java.io.File
import java.util.UUID
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo

/**
 * RemotePreviewViewModel的单元测试类
 * 使用Robolectric框架进行Android环境模拟测试
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RemotePreviewViewModelTest {

    private lateinit var viewModel: RemotePreviewViewModel
    private lateinit var context: Context
    private lateinit var mockRemoteDevice: IRemoteDevice
    private lateinit var mockDeviceInfo: RemoteDeviceInfo

    /**
     * 测试前的初始化方法
     * 1. 获取应用上下文
     * 2. 清理缓存目录
     * 3. 重置所有mock对象
     * 4. 初始化被测ViewModel
     * 5. 创建mock对象
     */
    @Before
    fun setup() {
        context = RuntimeEnvironment.getApplication()
        // 确保每次测试前清理缓存目录和重置所有mock
        File(context.cacheDir, "remotePic").deleteRecursively()
        unmockkAll()
        
        viewModel = RemotePreviewViewModel()
        mockRemoteDevice = mockk(relaxed = true)
        mockDeviceInfo = mockk<RemoteDeviceInfo>(relaxed = true)
        mockkObject(Injector)
    }

    /**
     * 测试后的清理方法
     * 1. 清理缓存目录
     * 2. 重置所有mock对象
     */
    @After
    fun tearDown() {
        // 确保每次测试后清理缓存目录和重置所有mock
        File(context.cacheDir, "remotePic").deleteRecursively()
        unmockkAll()
    }

    /**
     * 测试releaseDir方法 - 当目录存在时
     * 验证: 目录及其子文件应被正确删除
     */
    @Test
    fun `test releaseDir when directory exists`() {
        val testDir = File(context.cacheDir, "remotePic/testSubDir").apply {
            mkdirs()
            File(this, "testFile.txt").createNewFile()
        }

        viewModel.releaseDir(context)

        assertFalse("目录应被删除", File(context.cacheDir, "remotePic").exists())
    }

    /**
     * 测试releaseDir方法 - 当目录不存在时
     * 验证: 方法应正常执行且不抛出异常
     */
    @Test
    fun `test releaseDir when directory not exists`() {
        File(context.cacheDir, "remotePic").takeIf { it.exists() }?.deleteRecursively()

        viewModel.releaseDir(context)

        assertFalse("目录应不存在", File(context.cacheDir, "remotePic").exists())
    }

    /**
     * 测试createFile方法 - 创建有效文件
     * 验证: 
     * 1. 文件应被成功创建
     * 2. 文件扩展名应为.png
     * 3. 文件应位于正确的目录下
     */
    @Test
    fun `test createFile creates valid file`() {
        val file = viewModel.createFile(context)

        assertTrue("文件应存在", file.exists())
        assertTrue("文件名应为png格式", file.name.endsWith(".png"))
        assertEquals("父目录应为remotePic", "remotePic", file.parentFile?.name)
    }

    /**
     * 测试createFile方法 - 当父目录不存在时
     * 验证: 方法应自动创建父目录
     */
    @Test
    fun `test createFile creates parent dir when missing`() {
        File(context.cacheDir, "remotePic").takeIf { it.exists() }?.deleteRecursively()

        val file = viewModel.createFile(context)

        assertTrue("父目录应被创建", file.parentFile?.exists() == true)
    }

    /**
     * 测试getDeviceId方法 - 当远程设备可用时
     * 验证: 应返回正确的设备ID
     */
    @Test
    fun `test getDeviceId when remote device available`() {
        every { Injector.injectFactory<IRemoteDevice>() } returns mockRemoteDevice
        every { mockRemoteDevice.getCurrentLinkedRemoteDiceInfo() } returns mockDeviceInfo
        every { mockDeviceInfo.deviceId } returns "device_123"

        val result = viewModel.getDeviceId()

        assertEquals("应返回正确的设备ID", "device_123", result)
    }

    /**
     * 测试getDeviceId方法 - 当远程设备不可用时
     * 验证: 应返回空字符串
     */
    @Test
    fun `test getDeviceId when remote device not available`() {
        every { Injector.injectFactory<IRemoteDevice>() } returns null

        val result = viewModel.getDeviceId()

        assertEquals("应返回空字符串", "", result)
    }

    /**
     * 测试getDeviceId方法 - 当设备信息不可用时
     * 验证: 应返回空字符串
     */
    @Test
    fun `test getDeviceId when device info not available`() {
        every { Injector.injectFactory<IRemoteDevice>() } returns mockRemoteDevice
        every { mockRemoteDevice.getCurrentLinkedRemoteDiceInfo() } returns null

        val result = viewModel.getDeviceId()

        assertEquals("应返回空字符串", "", result)
    }

    /**
     * 测试updateSizeFormat方法 - 字节单位测试
     * 验证: 小于1024字节的值应显示为B单位
     */
    @Test
    fun `test updateSizeFormat for bytes`() {
        assertEquals("0B", viewModel.updateSizeFormat(0))
        assertEquals("500B", viewModel.updateSizeFormat(500))
        assertEquals("1023B", viewModel.updateSizeFormat(1023))
    }

    /**
     * 测试updateSizeFormat方法 - GB单位测试
     * 验证: 大于等于1GB的值应正确转换为GB单位并格式化
     */
    @Test
    fun `test updateSizeFormat for gigabytes`() {
        assertEquals("1.0GB", viewModel.updateSizeFormat(1024 * 1024 * 1024L))
        assertEquals("3.5GB", viewModel.updateSizeFormat(3758096384))
        assertEquals("10.0GB", viewModel.updateSizeFormat(1024 * 1024 * 1024 * 10L))
    }
}