/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - PreviewFilePathItem.kt
 * Description:
 *     The widget to show file path info on preview page
 *
 * Version: 1.0
 * Date: 2024-09-04
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-09-04   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.widget

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatTextView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.interfaze.filepreview.IFilePreviewFragment.OnClickFilePathListener
import java.io.File

internal class PreviewFilePathItem @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : LinearLayout(context, attrs) {

    private val filePathTextView: AppCompatTextView
    private val clickableColor by lazy {
        COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorContainerTheme)
    }
    private val notClickableColor by lazy {
        COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelPrimary)
    }

    init {
        LayoutInflater.from(context)
            .inflate(com.oplus.filemanager.preview.R.layout.widget_preview_file_path_item, this)
        filePathTextView = findViewById(com.oplus.filemanager.preview.R.id.preview_file_path_text)
        orientation = VERTICAL
        gravity = Gravity.START or Gravity.CENTER_VERTICAL
    }

    fun setLabelGone() {
        gravity = Gravity.END or Gravity.CENTER_VERTICAL
        val text: AppCompatTextView =
            findViewById(com.oplus.filemanager.preview.R.id.preview_file_path_label)
        text.visibility = GONE
        filePathTextView.gravity = Gravity.END
        val maxLines = 2
        setFilePathLine(maxLines)
        setFilePathEllipsize(TextUtils.TruncateAt.END)
    }

    @JvmOverloads
    fun setFilePath(filePath: String, onClick: OnClickFilePathListener? = null) {
        val parentPath = File(filePath).parent ?: filePath
        val formatPath = Utils.formatPathWithRTL(Utils.getVirtualPathString(context, parentPath))
        filePathTextView.text = formatPath
        if (onClick == null) {
            filePathTextView.setTextColor(notClickableColor)
            filePathTextView.setOnClickListener(null)
        } else {
            filePathTextView.setTextColor(clickableColor)
            filePathTextView.setOnClickListener {
                onClick.onClickFilePath(it, filePath)
            }
        }
    }

    private fun setFilePathLine(lines: Int) {
        filePathTextView.maxLines = lines
    }

    private fun setFilePathEllipsize(mode: TextUtils.TruncateAt) {
        filePathTextView.ellipsize = mode
    }
}