/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : DmpSearchApi.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/8/28
 * * Author      : muning
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.exportdmp

import android.app.Activity
import android.content.Context
import android.database.Cursor
import android.util.Log
import com.oplus.filemanager.interfaze.dmpsearch.IExportDmpSearchApi

object DmpSearchApi : IExportDmpSearchApi {

    const val TAG = "DmpSearchApi"

    @Synchronized
    override fun initDmpSdk(context: Context): Boolean {
        Log.d(TAG, "initDmpSdk")
        return SearchHelper.init(context)
    }

    override fun initClient() {
        Log.i(TAG, "initClient")
        SearchHelper.initClient()
    }

    override fun getCursor(searchKey: String?): Cursor? {
        Log.i(TAG, "getCursor searchKey $searchKey")
        return SearchHelper.getCursor(searchKey)
    }

    override fun checkState(activity: Activity) {
        Log.i(TAG, "checkState $activity")
        SearchHelper.checkState(activity)
    }

    override fun isDMPCursor(cursor: Cursor): Boolean {
        return SearchHelper.isDMPCursor(cursor)
    }

    override fun isShouldLoadDMP(): Boolean {
        Log.i(TAG, "isShouldLoadDMP")
        return SearchHelper.isShouldLoadDMP()
    }

    override fun buildRewriteQueries(mSearchKey: String?): Collection<String> {
        Log.i(TAG, "buildRewriteQueries mSearchKey $mSearchKey")
        return SearchHelper.buildRewriteQueries(mSearchKey)
    }
}