/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - RecycleOperationTest.java
 * Description: Test case for Recycle Operation
 * Version: 1.0
 * Date : 2020/04/20
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/04/20    1.0     create
 ****************************************************************/

package com.coloros.filemanager.recyclebin.operation

import android.net.Uri
import androidx.activity.ComponentActivity
import com.coloros.filemanager.BaseTest
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.Utils
import com.filemanager.recyclebin.operation.RecycleOperation
import com.filemanager.recyclebin.operation.listener.RecycleOperationListener
import com.filemanager.recyclebin.retriever.DataRetriever

import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import junit.framework.Assert.assertFalse
import junit.framework.Assert.assertTrue
import org.junit.After
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import shadow.ShadowViewHelper
import shadow.ShadowVolumeEnvironment

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
@Config(shadows = [ShadowViewHelper::class, ShadowVolumeEnvironment::class])
class RecycleOperationTest : BaseTest() {
    private lateinit var mActivity: ComponentActivity

    @Before
    override fun setUp() {
        mActivity = Robolectric.buildActivity(ComponentActivity::class.java).get()
    }

    @After
    override fun tearDown() {
    }

    @Test
    fun should_vertify_when_addPaths_isNull() {
        val progressListener = RecycleOperationListener(mActivity, null);
        val recycleOperation = RecycleOperation(mActivity, progressListener)
        assertFalse(recycleOperation.addPaths(null))
        assertTrue(recycleOperation.addPaths(ArrayList<String>()))
        assertFalse(recycleOperation.addPaths(ArrayList<String>()))
    }

    @Test
    fun should_vertify_when_addUris_isNull() {
        val progressListener = RecycleOperationListener(mActivity, null);
        val recycleOperation = RecycleOperation(mActivity, progressListener)
        assertFalse(recycleOperation.addUris(null))
        assertTrue(recycleOperation.addUris(ArrayList<Uri>()))
        assertFalse(recycleOperation.addUris(ArrayList<Uri>()))
    }

    @Test
    fun should_vertify_when_addFileBeans_isNull() {
        val progressListener = RecycleOperationListener(mActivity, null);
        val recycleOperation = RecycleOperation(mActivity, progressListener)
        assertFalse(recycleOperation.addFileBeans(null))
        assertTrue(recycleOperation.addFileBeans(ArrayList<BaseFileBean>()))
        assertFalse(recycleOperation.addFileBeans(ArrayList<BaseFileBean>()))
    }

    @Test
    fun `should execute store files for buried point`() {
        mockkObject(DataRetriever.Companion)
        mockkStatic(Utils::class)
        val operationMock = mockk<RecycleOperation> {
            every { doInBackground() } answers { callOriginal() }
            justRun { storeFilesForBuriedPoint() }
            justRun { buriedPointForMedia() }
            every { onTotalCountCallback() } returns 1
            every { mRecycleBulk } returns mockk {
                justRun { flush() }
            }
        }

        every { Utils.getStorageAvailableSize(any()) } returns 11L * 1024 * 1024
        every { DataRetriever.INSTANCE } returns mockk {
            every { ensureRecycleDirectory() } returns true
        }
        operationMock.doInBackground()
        verify { operationMock.storeFilesForBuriedPoint() }
        verify { operationMock.buriedPointForMedia() }
        unmockkAll()
    }
}