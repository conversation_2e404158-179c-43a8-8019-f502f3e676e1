/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : CloudDocsItemTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/31 10:45
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/31       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.ui

import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsDocumentItem
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem
import com.oplus.filemanager.room.model.DriveFileEntity
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.Assert
import org.junit.Test

class CloudDocsItemTest {

    /*@Test
    fun testCloudDocumentItemMapCloudDocsItem() {
        val invalidItem = DriveFileWrapper()
        val invalidDocsItem = CloudDocsItem.map(invalidItem)
        Assert.assertEquals(MimeTypeHelper.UNKNOWN_TYPE, invalidDocsItem.mLocalType)

        val dirItem = DriveFileWrapper(type = TYPE_DIR)
        val dirCloudDocsItem = CloudDocsItem.map(dirItem)
        Assert.assertEquals(MimeTypeHelper.DIRECTORY_TYPE, dirCloudDocsItem.mLocalType)

        val docItem = DriveFileWrapper(type = TYPE_DOC)
        val docCloudDocsItem = CloudDocsItem.map(docItem)
        Assert.assertEquals(MimeTypeHelper.DOC_TYPE, docCloudDocsItem.mLocalType)

        val excelItem = DriveFileWrapper(type = TYPE_EXCEL)
        val excelCloudDocsItem = CloudDocsItem.map(excelItem)
        Assert.assertEquals(MimeTypeHelper.XLS_TYPE, excelCloudDocsItem.mLocalType)

        val xmindItem = DriveFileWrapper(type = TYPE_XMIND)
        val xmindCloudDocsItem = CloudDocsItem.map(xmindItem)
        Assert.assertEquals(MimeTypeHelper.XMIND_TYPE, xmindCloudDocsItem.mLocalType)

        val unknownItem = DriveFileWrapper(type = "")
        val unknownCloudDocsItem = CloudDocsItem.map(unknownItem)
        Assert.assertEquals(MimeTypeHelper.UNKNOWN_TYPE, unknownCloudDocsItem.mLocalType)

        val item = DriveFileWrapper(
            id = ID,
            title = TITLE,
            type = TYPE_DOC,
            url = URL,
            createTime = CREATE_TIME,
            lastModifyTime = LAST_MODIFY_TIME
        )
        val docsItem = CloudDocsItem.map(item)
        Assert.assertEquals(ID, docsItem.id)
        Assert.assertEquals(TITLE, docsItem.mDisplayName)
        Assert.assertEquals(TYPE_DOC, docsItem.mMimeType)
        Assert.assertEquals(MimeTypeHelper.DOC_TYPE, docsItem.mLocalType)
        Assert.assertEquals(URL, docsItem.mData)
        Assert.assertEquals(CREATE_TIME.toLong() * CloudDocsItem.TIME_CONVERT_VALUE, docsItem.mDateModified)
        Assert.assertEquals(LAST_MODIFY_TIME.toLong() * CloudDocsItem.TIME_CONVERT_VALUE, docsItem.lastOpenTime)
    }*/

    @Test
    fun testDriveFileEntityMapCloudDocsItem() {
        val data = DriveFileEntity(
            id = ENTITY_ID,
            fileId = ID,
            name = TITLE,
            mimeType = DriveFileEntity.TYPE_FOLDER,
            uri = URL,
            lastModifyTime = LAST_MODIFY_TIME,
            lastBrowserTime = LAST_MODIFY_TIME
        )
        val item = CloudDocsItem.map(data)
        Assert.assertEquals(ID, item.id)
        Assert.assertEquals(TITLE, item.mDisplayName)
        Assert.assertEquals(DriveFileEntity.TYPE_FOLDER, item.mMimeType)
        Assert.assertEquals(MimeTypeHelper.DIRECTORY_TYPE, item.mLocalType)
        Assert.assertEquals(DriveFileEntity.TYPE_FOLDER, item.type)
        Assert.assertEquals(TITLE, item.title)
        Assert.assertEquals(URL, item.url)
        Assert.assertEquals(URL, item.mData)
        Assert.assertEquals(LAST_MODIFY_TIME.toLong() * CloudDocsItem.TIME_CONVERT_VALUE, item.mDateModified)
        Assert.assertEquals(LAST_MODIFY_TIME.toLong() * CloudDocsItem.TIME_CONVERT_VALUE, item.lastOpenTime)

        data.mimeType = DocumentListItem.TENCENT_FILE_TYPE_DOC
        Assert.assertEquals(MimeTypeHelper.DOC_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = DocumentListItem.TENCENT_FILE_TYPE_SLIDE
        Assert.assertEquals(MimeTypeHelper.PPT_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = DocumentListItem.TENCENT_FILE_TYPE_SHEET
        Assert.assertEquals(MimeTypeHelper.XLS_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = DocumentListItem.TENCENT_FILE_TYPE_FORM
        Assert.assertEquals(MimeTypeHelper.TENCENT_FORM_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = DocumentListItem.TENCENT_FILE_TYPE_PDF
        Assert.assertEquals(MimeTypeHelper.PDF_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = DocumentListItem.TENCENT_FILE_TYPE_MIND
        Assert.assertEquals(MimeTypeHelper.TENCENT_MIND_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = DocumentListItem.TENCENT_FILE_TYPE_FLOWCHART
        Assert.assertEquals(MimeTypeHelper.TENCENT_FLOWCHART_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = DocumentListItem.TENCENT_FILE_TYPE_SMART_CANVAS
        Assert.assertEquals(MimeTypeHelper.TENCENT_SMART_CANVAS_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = DocumentListItem.TENCENT_FILE_TYPE_SMART_SHEET
        Assert.assertEquals(MimeTypeHelper.TENCENT_SMART_SHEET_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = DocumentListItem.TENCENT_FILE_TYPE_DRIVE
        Assert.assertEquals(MimeTypeHelper.TXT_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = KDocsDocumentItem.KDOCS_TYPE_XLSX
        Assert.assertEquals(MimeTypeHelper.XLSX_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = KDocsDocumentItem.KDOCS_TYPE_DOCS
        Assert.assertEquals(MimeTypeHelper.DOCX_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = KDocsDocumentItem.KDOCS_TYPE_PPTX
        Assert.assertEquals(MimeTypeHelper.PPTX_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = KDocsDocumentItem.KDOCS_TYPE_OTL
        Assert.assertEquals(MimeTypeHelper.KDOCS_OTL_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = KDocsDocumentItem.KDOCS_TYPE_KSHEET
        Assert.assertEquals(MimeTypeHelper.KDOCS_KSHEET_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = KDocsDocumentItem.KDOCS_TYPE_DBT
        Assert.assertEquals(MimeTypeHelper.KDOCS_DBT_TYPE, CloudDocsItem.map(data).mLocalType)

        data.mimeType = KDocsDocumentItem.KDOCS_TYPE_SHARE_FOLDER
        Assert.assertEquals(MimeTypeHelper.KDOCS_SHARE_FOLDER_TYPE, CloudDocsItem.map(data).mLocalType)

        mockkObject(MimeTypeHelper)
        every { MimeTypeHelper.getTypeFromPath(any()) }.returns(MimeTypeHelper.VIDEO_TYPE)

        data.mimeType = MimeTypeHelper.MimeType.MIMETYPE_VIDEO
        Assert.assertEquals(MimeTypeHelper.VIDEO_TYPE, CloudDocsItem.map(data).mLocalType)
        unmockkObject(MimeTypeHelper)
    }

    @Test
    fun testIsSupportDownload() {
        val cloudDocsItem = CloudDocsItem()
        Assert.assertTrue(cloudDocsItem.isSupportDownload(CategoryHelper.CATEGORY_K_DOCS))

        cloudDocsItem.type = KDocsDocumentItem.KDOCS_TYPE_OTL
        Assert.assertFalse(cloudDocsItem.isSupportDownload(CategoryHelper.CATEGORY_K_DOCS))

        cloudDocsItem.type = KDocsDocumentItem.KDOCS_TYPE_KSHEET
        Assert.assertFalse(cloudDocsItem.isSupportDownload(CategoryHelper.CATEGORY_K_DOCS))

        cloudDocsItem.type = KDocsDocumentItem.KDOCS_TYPE_DBT
        Assert.assertFalse(cloudDocsItem.isSupportDownload(CategoryHelper.CATEGORY_K_DOCS))

        cloudDocsItem.type = DriveFileEntity.TYPE_FOLDER
        Assert.assertFalse(cloudDocsItem.isSupportDownload(CategoryHelper.CATEGORY_K_DOCS))

        cloudDocsItem.type = DocumentListItem.TENCENT_FILE_TYPE_DOC
        Assert.assertTrue(cloudDocsItem.isSupportDownload(CategoryHelper.CATEGORY_TENCENT_DOCS))

        cloudDocsItem.type = DocumentListItem.TENCENT_FILE_TYPE_SHEET
        Assert.assertTrue(cloudDocsItem.isSupportDownload(CategoryHelper.CATEGORY_TENCENT_DOCS))

        cloudDocsItem.type = DocumentListItem.TENCENT_FILE_TYPE_SLIDE
        Assert.assertTrue(cloudDocsItem.isSupportDownload(CategoryHelper.CATEGORY_TENCENT_DOCS))

        cloudDocsItem.type = DocumentListItem.TENCENT_FILE_TYPE_PDF
        Assert.assertTrue(cloudDocsItem.isSupportDownload(CategoryHelper.CATEGORY_TENCENT_DOCS))

        cloudDocsItem.type = DocumentListItem.TENCENT_FILE_TYPE_DRIVE
        Assert.assertFalse(cloudDocsItem.isSupportDownload(CategoryHelper.CATEGORY_TENCENT_DOCS))
    }

    companion object {
        private const val TYPE_DIR = "dir"
        private const val TYPE_DOC = "doc"
        private const val TYPE_EXCEL = "excel"
        private const val TYPE_XMIND = "xmind"

        private const val ENTITY_ID = 100L
        private const val ID = "300000000GKvJqeggQNng"
        private const val TITLE = "铁松离合"
        private const val URL = "https://docs.qq.com/document/DR0t2SnFlZ2dRTm5n"
        private const val CREATE_TIME = 1706596863
        private const val LAST_MODIFY_TIME = 1706596863
    }
}