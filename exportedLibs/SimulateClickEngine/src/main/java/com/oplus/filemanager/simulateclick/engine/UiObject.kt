/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - UiObject.kt
 * Description:
 *     The utils to handle script actions
 *
 * Version: 2.0
 * Date: 2024-05-23
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-05-23   2.0    Porting from LLMSimulateClick
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine

import android.accessibilityservice.GestureDescription
import android.graphics.Path
import android.graphics.Point
import android.graphics.Rect
import android.os.Bundle
import android.view.accessibility.AccessibilityNodeInfo
import com.oplus.filemanager.simulateclick.accessibility.GestureResultAdapter
import com.oplus.filemanager.simulateclick.engine.utils.FindNodeUtil
import com.oplus.filemanager.simulateclick.engine.utils.Log
import com.oplus.filemanager.simulateclick.service.AccessibilityServiceBridge
import kotlinx.coroutines.delay
import kotlin.math.min

internal class UiObject(
    private val engine: SimulateClickEngine,
    private val serviceBridge: AccessibilityServiceBridge = engine.serviceBridge,
) {

    suspend fun dealWith(node: AccessibilityNodeInfo?, action: Action?): Boolean {
        if (node == null || action == null) {
            return false
        }
        Log.d(TAG, "dealWith: node resId=${node.viewIdResourceName}, text=${node.text}")
        val rect = Rect()
        node.getBoundsInScreen(rect)
        return when (action.actionType) {
            ACTION_TYPE_CLICK -> click(node)
            ACTION_TYPE_INPUT -> input(node, action)
            ACTION_TYPE_LONG_CLICK -> longClick(node)
            ACTION_TYPE_SELECT -> clickById(node, action)
            ACTION_TYPE_SINGLE_SELECT -> click(node, action)
            ACTION_TYPE_SCROLL_FORWARD -> scrollForward(node)
            ACTION_TYPE_SCROLL_BACKWARD -> scrollBackward(node)
            ACTION_TYPE_GET_TEXT -> getText(node)
            ACTION_TYPE_CLICK_GESTURE -> clickGesture(node, action)
            ACTION_TYPE_NODE_GESTURE_SCROLL -> gestureScroll(node, action)
            else -> false
        }
    }

    suspend fun scrollParentNode(action: Action, node: AccessibilityNodeInfo?) {
        if (node == null) return
        if (scrollForward(node)) {
            return
        }
        while (node.performAction(AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD)) {
            Log.d(TAG, "scrollParentNode: scrolling until to the top")
            delay(action.scrollParentDelay())
        }
    }

    @Suppress("DEPRECATION")
    private fun click(node: AccessibilityNodeInfo): Boolean {
        if (!node.isClickable) {
            return false
        }
        val performAction = node.performAction(AccessibilityNodeInfo.ACTION_CLICK)
        node.recycle()
        return performAction
    }

    private fun detectSelectIndex(action: Action): Int =
        runCatching {
            action.selectIndex?.toInt()
        }.onFailure {
            Log.d(TAG, "detectSelectIndex: incorrect selectIndex ${action.selectIndex}")
        }.getOrNull() ?: 0

    @Suppress("DEPRECATION")
    private fun click(node: AccessibilityNodeInfo, action: Action): Boolean {
        val viewList = FindNodeUtil.findNodesById(node, action.childWidgetID.toString())
        val selectIndex = detectSelectIndex(action)
        if (viewList.isEmpty()) {
            return false
        }
        var i = selectIndex
        for (i2 in viewList.indices) {
            if (i >= viewList.size) {
                i = viewList.size - 1
            }
            if ((action.childWidgetType == viewList[i2].className) && click(viewList[i])) {
                return true
            }
        }
        node.recycle()
        return false
    }

    @Suppress("DEPRECATION")
    private fun scrollForward(node: AccessibilityNodeInfo): Boolean {
        val performAction = node.performAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD)
        Log.d(TAG, "scrollForward: $performAction")
        node.recycle()
        return performAction
    }

    @Suppress("DEPRECATION")
    private fun scrollBackward(node: AccessibilityNodeInfo): Boolean {
        val performAction = node.performAction(AccessibilityNodeInfo.ACTION_SCROLL_BACKWARD)
        Log.d(TAG, "scrollBackward: $performAction")
        node.recycle()
        return performAction
    }

    @Suppress("DEPRECATION")
    private fun clickById(node: AccessibilityNodeInfo, action: Action): Boolean {
        val nodeList = FindNodeUtil.findNodesById(node, action.childWidgetID.toString())
        Log.d(TAG, "clickById: nodeList.size=${nodeList.size}")
        val selectIndex = detectSelectIndex(action)
        if (nodeList.isEmpty() || selectIndex <= 0) {
            Log.d(TAG, "clickById: nothing found")
            return false
        }
        val min = min(selectIndex, nodeList.size)
        for (i in 0 until min) {
            if (action.childWidgetType != nodeList[i].className) {
                continue
            }
            if (click(nodeList[i]) && (i == (min - 1))) {
                return true
            }
        }
        node.recycle()
        return false
    }

    @Suppress("DEPRECATION")
    private fun longClick(node: AccessibilityNodeInfo): Boolean {
        if (!node.isLongClickable) {
            return false
        }
        val performAction = node.performAction(AccessibilityNodeInfo.ACTION_LONG_CLICK)
        node.recycle()
        return performAction
    }

    @Suppress("DEPRECATION")
    private fun input(node: AccessibilityNodeInfo, action: Action): Boolean {
        val input = action.actionInput
        if (!node.isEditable) {
            return false
        }
        val bundle = Bundle().apply {
            putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, input)
        }
        val performAction = node.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, bundle)
        node.recycle()
        return performAction
    }

    private suspend fun clickGesture(
        node: AccessibilityNodeInfo,
        action: Action
    ): Boolean {
        val service = serviceBridge.pickService() ?: return false
        delay(action.clickGestureDelay())
        val handleTime = action.handleTime()
        val clickableParent = findClickableParentNode(node)
        if (clickableParent != null) {
            Log.d(
                TAG,
                "clickGesture: clickable parent，" +
                        "resId=${clickableParent.viewIdResourceName}，text=${clickableParent.text}"
            )
            clickableParent.performAction(AccessibilityNodeInfo.ACTION_CLICK)
        }
        val rect = Rect()
        node.getBoundsInScreen(rect)
        val center = getCenterPoint(rect)
        Log.d(TAG, "clickGesture: center=$center")
        val path = Path()
        path.moveTo(center.x.toFloat(), center.y.toFloat())
        val build = GestureDescription.Builder()
            .addStroke(GestureDescription.StrokeDescription(path, 0, handleTime))
            .build()
        val dispatchGesture = service.dispatchGesture(build, GestureResultAdapter(), null)
        //操作后下一个应该晚点
        delay(action.clickGestureWait())
        return dispatchGesture
    }

    private fun findClickableParentNode(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        var currentNode: AccessibilityNodeInfo? = node
        while ((currentNode != null) && !currentNode.isClickable) {
            val parent = currentNode.parent
            currentNode = parent
        }
        return currentNode
    }

    @Suppress("FunctionOnlyReturningConstant")
    private fun gestureScroll(node: AccessibilityNodeInfo?, action: Action): Boolean {
        // Unnecessary at current. Port the implementation if has requirements in the future.
        return false
    }

    private fun getText(node: AccessibilityNodeInfo?): Boolean {
        var isGetTest = false
        if (node == null) {
            return false
        }
        if (!node.text.isNullOrEmpty()) {
            isGetTest = true
        }
        if (node.contentDescription.isNullOrEmpty()) {
            return isGetTest
        }
        return true
    }

    private fun getCenterPoint(rect: Rect?): Point {
        val point = Point()
        if (rect == null) {
            return point
        }
        point.y = rect.top + (rect.bottom - rect.top) / 2
        point.x = rect.left + (rect.right - rect.left) / 2
        return point
    }

    private companion object {
        private const val TAG = "UiObject"
    }
}