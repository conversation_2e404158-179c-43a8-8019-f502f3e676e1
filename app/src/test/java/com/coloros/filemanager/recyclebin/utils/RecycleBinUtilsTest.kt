/*
 * ********************************************************************
 *  * * Copyright (C), 2021 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  RecycleBinUtilsTest.java
 *  * * Description : RecycleBinUtilsTest.java
 *  * * Version     : 1.0
 *  * * Date        : 21-12-14 下午5:34
 *  * * Author      : W9010863
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.coloros.filemanager.recyclebin.utils

import android.graphics.Color
import android.net.Uri
import android.text.SpannableString
import androidx.activity.ComponentActivity
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.Utils
import com.filemanager.recyclebin.operation.BaseOperation
import com.filemanager.recyclebin.utils.RecycleBinUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import junit.framework.Assert
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test
import java.io.File

class RecycleBinUtilsTest {

    @Before
    fun setUp() {
        MyApplication.init(mockk {
            every { applicationContext }.returns(this)
            every { resources }.returns(mockk {
                every { getQuantityString(any(), any(), any()) }.returns("test")
            })
        })
        mockkStatic(Utils::class)
        mockkObject(RecycleBinUtils)
        mockkStatic(RecycleBinUtils::class)
        mockkStatic(PreferencesUtils::class)
    }

    @Test
    fun should_verify_SharedRecycleBinSize() {
        every { PreferencesUtils.put(RecycleBinUtils.SHARED_PREFS_NAME, "recycle_bin_size", any<Long>()) }.returns(Unit)
        every { PreferencesUtils.getLong(RecycleBinUtils.SHARED_PREFS_NAME, "recycle_bin_size", 0L) }.returns(0L)

        RecycleBinUtils.saveSharedRecycleBinSize(100L)
        Assert.assertEquals(0L, RecycleBinUtils.getSharedRecycleBinSize())
    }

    @Test
    fun should_verify_getRecycleTimeLeftString() {
        Assert.assertEquals("test", RecycleBinUtils.getRecycleTimeLeftString(100 * 1000 * 3600 * 24, 0))

        Assert.assertEquals("test", RecycleBinUtils.getRecycleTimeLeftString(0, 1000 * 3600 * 24))
    }

    @Test
    fun should_verify_formatRecycleDetail() {
        Assert.assertEquals("", RecycleBinUtils.formatRecycleDetail(null, null, "test"))

        Assert.assertEquals("", RecycleBinUtils.formatRecycleDetail(null, "test", "test"))

        Assert.assertEquals("", RecycleBinUtils.formatRecycleDetail("test", null, "test"))

        every { Utils.formatMessage(any(), any()) }.returns("test")
        every { Utils.isRtl() }.returns(true)
        every { MyApplication.sAppContext.getColor(any()) }.returns(Color.RED)
        val text = "\u200Ftest  |  \u200Ftest  |  \u200Ftest"
        val spannableStringMockk = spyk(SpannableString(text))
        every { RecycleBinUtils.constructorSpannableString(text) } returns spannableStringMockk
        assertNotNull(RecycleBinUtils.formatRecycleDetail("test", "test", "test"))

        every { Utils.isRtl() }.returns(false)
        assertNotNull(RecycleBinUtils.formatRecycleDetail("test", "test", "test"))
    }

    @Test
    fun should_verify_formatRecycleSize() {
        every { Utils.formatMessage("", Utils.RTL_POSITION_DOUBLE) }.returns("test null")
        Assert.assertEquals("test null", RecycleBinUtils.formatRecycleSize(null))

        every { Utils.byteCountToDisplaySize(any()) }.returns("test")
        every { Utils.formatMessage("test", Utils.RTL_POSITION_DOUBLE) }.returns("test")
        Assert.assertEquals("test", RecycleBinUtils.formatRecycleSize(mockk(relaxed = true) {
            every { mData }.returns(null)
        }))
    }

    @Test
    fun should_verify_getFileOrFolderSize() {
        Assert.assertEquals(0, RecycleBinUtils.getFileOrFolderSize(null))

        var file = mockk<File>(relaxed = true)

        try {
            every { file.isDirectory }.throws(Exception("test-error"))
            Assert.assertEquals(0, RecycleBinUtils.getFileOrFolderSize(file))
        } catch (e: Exception) {
            println("test!!!")
        }

        every { file.isDirectory }.returns(false)
        every { file.length() }.returns(100L)
        Assert.assertEquals(100L, RecycleBinUtils.getFileOrFolderSize(file))

        every { file.isDirectory }.returns(true)
        every { file.listFiles() }.returns(null)
        Assert.assertEquals(100L, RecycleBinUtils.getFileOrFolderSize(file))

        var file2 = mockk<File>() {
            every { length() }.returns(10L)
            every { listFiles() }.returns(null)
        }
        every { file.listFiles() }.returns(arrayOf(file2))
        every { file2.isDirectory }.returns(true)
        Assert.assertEquals(110L, RecycleBinUtils.getFileOrFolderSize(file))

        every { file2.isDirectory }.returns(false)
        Assert.assertEquals(110L, RecycleBinUtils.getFileOrFolderSize(file))
    }

    @Test
    fun should_verify_innerRecycle() {
        var activity = mockk<ComponentActivity>(relaxed = true)

        RecycleBinUtils.innerRecycle(activity, null, null, false)

        var parameters = mockk<BaseOperation.OperationParameters>(relaxed = true)
        RecycleBinUtils.innerRecycle(activity, parameters, null, false)

        every { parameters.mBaseFiles }.returns(arrayListOf(BaseFileBean()))
        RecycleBinUtils.innerRecycle(activity, parameters, null, false)

        every { parameters.mPaths }.returns(arrayListOf("test"))
        RecycleBinUtils.innerRecycle(activity, parameters, null, false)
        every { parameters.mUris }.returns(arrayListOf(mockk(relaxed = true)))
        RecycleBinUtils.innerRecycle(activity, parameters, null, false)
    }


    @Test
    fun should_verify_makeFileCountAndSizeStr() {
        every { Utils.byteCountToDisplaySize(any()) }.returns("test")
        Assert.assertEquals("", RecycleBinUtils.makeFileCountStr(0, deleteForever = false,true, true))

        val context = MyApplication.sAppContext

        every { context.getString(any()) }.returns("test1")
        Assert.assertEquals("test1", RecycleBinUtils.makeFileCountStr(1, deleteForever = true, true, true))

        every { context.getString(any()) }.returns("test2")
        Assert.assertEquals("test2", RecycleBinUtils.makeFileCountStr(1, deleteForever = true, false, false))

        Assert.assertEquals("test", RecycleBinUtils.makeFileCountStr(10, deleteForever = true, false, false))

        Assert.assertEquals("test", RecycleBinUtils.makeFileCountStr(10, deleteForever = false, true, false))

        every { context.getString(any()) }.returns("test5")
        Assert.assertEquals("test5", RecycleBinUtils.makeFileCountStr(10, deleteForever = true, false, true))

        every { context.getString(any()) }.returns("test5")
        Assert.assertEquals("test5", RecycleBinUtils.makeFileCountStr(10, deleteForever = false, true, true))
    }

    @Test
    fun should_verify_getDelButtonStr() {
        val context = MyApplication.sAppContext
        every { context.getString(any()) }.returns("test")
        Assert.assertEquals("test", RecycleBinUtils.getDelButtonStr(true))
        every { context.getString(any()) }.returns("test2")
        Assert.assertEquals("test2", RecycleBinUtils.getDelButtonStr(false))
    }

    @Test
    fun should_verify_isMediaTypeCategory() {
        Assert.assertFalse(RecycleBinUtils.isMediaTypeCategory(-1))

        Assert.assertTrue(RecycleBinUtils.isMediaTypeCategory(CategoryHelper.CATEGORY_IMAGE))
        Assert.assertTrue(RecycleBinUtils.isMediaTypeCategory(CategoryHelper.CATEGORY_VIDEO))
        Assert.assertTrue(RecycleBinUtils.isMediaTypeCategory(CategoryHelper.CATEGORY_AUDIO))
        Assert.assertTrue(RecycleBinUtils.isMediaTypeCategory(CategoryHelper.CATEGORY_DOC))
        Assert.assertTrue(RecycleBinUtils.isMediaTypeCategory(CategoryHelper.CATEGORY_APK))
        Assert.assertTrue(RecycleBinUtils.isMediaTypeCategory(CategoryHelper.CATEGORY_COMPRESS))
        Assert.assertTrue(RecycleBinUtils.isMediaTypeCategory(CategoryHelper.CATEGORY_SEARCH))
        Assert.assertTrue(RecycleBinUtils.isMediaTypeCategory(CategoryHelper.CATEGORY_RECENT))
    }

    @Test
    fun `should return null when storeFilesForDeleteLabels if allFilePath is null`() {
        val allFilePath: ArrayList<String>? = null
        RecycleBinUtils.storeFilesForDeleteLabels(allFilePath, null, null, null)
        assertNull(allFilePath)
    }

    @Test
    fun `should size is 0 when storeFilesForDeleteLabels if allFilePath is not null`() {
        val allFilePath = arrayListOf<String>()
        RecycleBinUtils.storeFilesForDeleteLabels(allFilePath, null, null, null)
        assertEquals(0, allFilePath.size)
    }

    @Test
    fun `should size is 10 when storeFilesForDeleteLabels if path not null`() {
        val allFilePath = arrayListOf<String>()
        val paths = arrayListOf<String>()
        for (index in 1..10) {
            paths.add("path$index")
        }
        RecycleBinUtils.storeFilesForDeleteLabels(allFilePath, paths, null, null)
        assertEquals(10, allFilePath.size)
    }

    @Test
    fun `should size is 0 when storeFilesForDeleteLabels if uris not null`() {
        val allFilePath = arrayListOf<String>()
        val uris = arrayListOf<Uri>()
        RecycleBinUtils.storeFilesForDeleteLabels(allFilePath, null, uris, null)
        assertEquals(0, allFilePath.size)
    }

    @Test
    fun `should size is 10 when storeFilesForDeleteLabels if files not null`() {
        val allFilePath = arrayListOf<String>()
        val files = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val file = BaseFileBean()
            file.mData = "data$index"
            file.mDisplayName = "name$index"
            files.add(file)
        }
        RecycleBinUtils.storeFilesForDeleteLabels(allFilePath, null, null, files)
        assertEquals(10, allFilePath.size)
    }

    @After
    fun afterTests() {
        unmockkStatic(Utils::class)
        unmockkObject(RecycleBinUtils)
        unmockkStatic(RecycleBinUtils::class)
        unmockkStatic(PreferencesUtils::class)
    }
}