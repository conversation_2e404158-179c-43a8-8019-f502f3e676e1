/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScriptValueUtils.kt
 * Description:
 *     The utils to convert values in script
 *
 * Version: 1.0
 * Date: 2024-06-04
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-06-04   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.utils

import com.oplus.filemanager.simulateclick.engine.value.IScriptFlagValue

private const val FLAG_OR_DELIMITER = '|'

internal fun convertAliasToFlagValue(
    value: String?,
    defValue: Int,
    vararg dict: Pair<String, Int>
): Int {
    val aliasValue = value?.takeIf { it.isNotEmpty() } ?: return defValue
    return convertAliasToFlagValueImpl(aliasValue, dict) ?: defValue
}

private fun convertAliasToFlagValueImpl(value: String, dict: Array<out Pair<String, Int>>): Int? {
    val aliasList = value.split(FLAG_OR_DELIMITER).takeIf { it.isNotEmpty() } ?: return null
    val dictMap = dict.takeIf { it.isNotEmpty() }?.toMap() ?: return null
    var flagValue: Int? = null
    aliasList.forEach {
        val flag = dictMap[it] ?: return@forEach
        flagValue = (flagValue ?: 0) or flag
    }
    return flagValue
}

internal fun convertFlagValueToAlias(flag: Int, vararg dict: Pair<String, Int>): String {
    dict.takeIf { it.isNotEmpty() } ?: return ""
    val aliasBuilder = StringBuilder("")
    dict.forEach {
        if (flag and it.second > 0) {
            aliasBuilder.append(it.first).append(FLAG_OR_DELIMITER)
        } else if ((flag == 0) && (it.second == 0)) {
            return it.first
        }
    }
    if (aliasBuilder.endsWith(FLAG_OR_DELIMITER)) {
        aliasBuilder.deleteCharAt(aliasBuilder.lastIndex)
    }
    return aliasBuilder.toString()
}

internal fun IScriptFlagValue.hasFlag(expectedFlag: Int): Boolean = flag and expectedFlag > 0

internal fun IScriptFlagValue.hasFlag(expectedFlag: IScriptFlagValue): Boolean =
    hasFlag(expectedFlag.flag)

internal fun IScriptFlagValue.contains(anotherFlag: Int): Boolean = flag or anotherFlag == flag

internal fun IScriptFlagValue.contains(anotherFlag: IScriptFlagValue): Boolean =
    contains(anotherFlag.flag)

internal fun IScriptFlagValue.isEquals(anotherFlag: Int): Boolean = flag == anotherFlag

internal fun IScriptFlagValue.isEquals(anotherFlag: IScriptFlagValue): Boolean =
    isEquals(anotherFlag.flag)