/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForFileBrowser
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.filemanager.setting.di

import com.filemanager.setting.SettingApi
import com.filemanager.setting.ui.privacy.personal.CollectPrivacyApi
import com.oplus.filemanager.interfaze.privacy.ICollectPrivacyApi
import com.oplus.filemanager.interfaze.setting.ISetting
import org.koin.dsl.module

object AutoDIForSetting {

    val settingModule = module {
        single<ISetting>(createdAtStart = true) {
            SettingApi
        }
    }

    val collectPrivacyApi = module {
        single<ICollectPrivacyApi>(createdAtStart = true) {
            CollectPrivacyApi()
        }
    }
}