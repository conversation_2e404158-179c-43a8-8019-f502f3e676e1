/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudDocsRemoteMediator
 ** Description : 在 Pager 耗尽本地缓存数据或现有数据失效时，从网络加载更多数据。
 ** Version     : 1.0
 ** Date        : 2023/12/29 15:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2023/12/29       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.source

import androidx.annotation.VisibleForTesting
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadState
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.drivebrowser.data.repository.kdocs.KDocsFileDriveRepository
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.domain.model.CloudDocumentItem
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils
import com.oplus.filemanager.drivebrowser.utils.ServerException
import com.oplus.filemanager.provider.DriveFileDBHelper
import com.oplus.filemanager.room.model.DriveFileEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import retrofit2.HttpException
import java.io.IOException

@OptIn(ExperimentalPagingApi::class)
class CloudDocsRemoteMediator(
    private val query: CloudDocsQuery,
    private var networkService: FileDriveRepository
) : RemoteMediator<Int, DriveFileEntity>() {

    private var currentIndex = 0

    companion object {
        private const val TAG = "CloudDocsRemoteMediator"

        /**
         * 判断是否是服务异常的状态
         */
        fun isServerExceptionState(loadState: LoadState): Boolean {
            if (loadState is LoadState.Error) {
                val error = loadState.error
                return (error is ServerException) || (error is IOException)
            }
            return false
        }

        fun isAuthError(loadState: LoadState): Boolean {
            if (loadState is LoadState.Error) {
                val error = loadState.error
                return (error as? ServerException)?.code == KDocsFileDriveRepository.AUTH_EXPIRED
                        || (error as? ServerException)?.code == TencentFileDriveRepository.AUTH_EXPIRED
            }
            return false
        }
    }


    override suspend fun load(loadType: LoadType, state: PagingState<Int, DriveFileEntity>): MediatorResult {
        Log.d(TAG, "load -> type $loadType")
        try {
            when (loadType) {
                LoadType.REFRESH -> { // 返回空，表示加载第一页
                    Log.d(TAG, "load -> REFRESH")
                }

                LoadType.PREPEND -> {
                    /**
                     * In this example, you never need to prepend, since REFRESH will always load the first page in the list.
                     * Immediately return, reporting end of pagination.
                     */
                    return MediatorResult.Success(endOfPaginationReached = true)
                }

                LoadType.APPEND -> {
                    /**
                     * You must explicitly check if the last item is null when appending, since passing null to networkService is only
                     * valid for initial load. If lastItem is null it means no items were loaded after the initial REFRESH and there are
                     * no more items to load.
                     */
                    val lastItem = state.lastItemOrNull()
                    if (lastItem == null && !state.isEmpty()) {
                        return MediatorResult.Success(endOfPaginationReached = true)
                    }
                }
            }

            // 请求服务端
            Log.d(TAG, "load -> start: $currentIndex, limit: ${state.config.pageSize}")
            val baseResp = networkService.getDocumentLists(query.sortType, query.asc, currentIndex, state.config.pageSize, query.folderId)
            if (baseResp.isFailed()) {
                Log.i(TAG, "load -> isFailed errorCode = ${baseResp.code}, ${baseResp.msg}")
                currentIndex = 0
                return handleError(ServerException(baseResp.code, baseResp.msg ?: ""))
            }
            val resp = baseResp.data ?: kotlin.run {
                Log.i(TAG, "load -> data is null")
                currentIndex = 0
                return handleError(ServerException(message = "no data"))
            }
            // 保存数据库
            Log.d(TAG, "load -> response: isLast: ${resp.isLastPage} offset:${resp.nextPageOffset}")
            currentIndex = if (resp.isLastPage) {
                0
            } else {
                resp.nextPageOffset
            }
            saveCloudDocuments(resp.lists, loadType)
            // 保存刷新时间
            FileDriveStateUtils.saveLastRefreshTime(query.source, System.currentTimeMillis())
            return MediatorResult.Success(endOfPaginationReached = resp.isLastPage)
        } catch (e: IOException) {
            Log.e(TAG, "load error", e)
            return handleError(e)
        } catch (e: HttpException) {
            Log.e(TAG, "load error", e)
            return handleError(e)
        }
    }

    /**
     * 处理错误信息
     */
    @VisibleForTesting
    fun handleError(e: Exception): MediatorResult.Error {
        val map = hashMapOf<String, String>()
        map[StatisticsUtils.FILE_SOURCE] = query.source
        map[StatisticsUtils.ERROR] = e.message ?: ""
        StatisticsUtils.onCommon(appContext, StatisticsUtils.DRIVE_FILE_GET_LIST_FAIL, map)
        return MediatorResult.Error(e)
    }

    /**
     * 保存数据到数据库
     */
    @VisibleForTesting
    fun saveCloudDocuments(
        list: List<CloudDocumentItem>,
        loadType: LoadType
    ) {
        Log.d(TAG, "saveCloudDocuments folderId:${query.folderId} loadType:$loadType lists:${list.size}")
        val isReplace = loadType == LoadType.REFRESH
        if (list.isEmpty()) {
            if (isReplace) {
                runBlocking(Dispatchers.IO) {
                    DriveFileDBHelper.deleteDocuments(query.source, query.folderId)
                }
            }
            return
        }
        runBlocking(Dispatchers.IO) {
            val entityList = list.map {
                CloudDocumentItem.map(it, query.source)
            }
            DriveFileDBHelper.updateDocuments(entityList, query.source, query.folderId, isReplace)
        }
    }

    /**
     * 去重
     * @param list
     * @param pageData page data
     */
    @VisibleForTesting
    fun removeDuplicate(
        list: List<DriveFileEntity>,
        pageData: List<DriveFileEntity>
    ): List<DriveFileEntity> {
        // 获取所有的page data id
        val idSet = mutableSetOf<String>()
        pageData.forEach {
            idSet.add(it.fileId)
        }
        // 去重
        val result = mutableListOf<DriveFileEntity>()
        list.forEach {
            if (!idSet.contains(it.fileId)) {
                result.add(it)
            }
        }
        Log.d(TAG, "removeDuplicate list:${list.size} pager:${pageData.size} result:${result.size}")
        return result
    }
}