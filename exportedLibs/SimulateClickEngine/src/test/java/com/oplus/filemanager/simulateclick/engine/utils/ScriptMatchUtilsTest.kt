/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScriptMatchUtilsTest.kt
 * Description:
 * The test cases for ScriptMatchUtils
 *
 * Version: 1.0
 * Date: 2024-06-06
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-06-06   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.utils

import com.oplus.filemanager.simulateclick.engine.Script
import com.oplus.filemanager.simulateclick.engine.UiDevice
import com.oplus.filemanager.simulateclick.engine.value.ScreenDisplayFlag
import com.oplus.filemanager.simulateclick.engine.value.ScreenOrientationFlag
import com.oplus.filemanager.simulateclick.engine.value.ScreenRotationFlag
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.Assert
import org.junit.Test

class ScriptMatchUtilsTest : Assert() {

    @Test
    fun `should true when matchHasSkill if script contains skill`() {
        // Given
        val testSkill = "test_skill"
        val script = mockk<Script> {
            every { skills } returns listOf(
                mockk { every { skillName } returns testSkill }
            )
        }

        // When
        val result = script.matchHasSkill(testSkill)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should false when matchHasSkill if script not contains skill`() {
        // Given
        val testSkill = "test_skill"
        val script = mockk<Script> {
            every { skills } returns listOf(
                mockk { every { skillName } returns "test_skill2" }
            )
        }

        // When
        val result = script.matchHasSkill(testSkill)

        // Then
        assertFalse(result)
    }

    @Test
    fun `should true when matchAppVersion if app ver in range`() {
        // SetUp
        mockkStatic(UiDevice::class)

        // Given
        val testPkg = "com.test.pkg"
        every { UiDevice.getAppVersionCode(any(), testPkg) } returns 123
        val script = mockk<Script> {
            every { appPkg } returns "$testPkg@@1.0.2"
            every { appMinVersion } returns 100
            every { appMaxVersion } returns 200
        }

        // When
        val result = script.matchAppVersion(mockk(), testPkg)

        // Then
        assertTrue(result)

        // TearDown
        unmockkStatic(UiDevice::class)
    }

    @Test
    fun `should false when matchAppVersion if app ver out of range`() {
        // SetUp
        mockkStatic(UiDevice::class)

        // Given
        val testPkg = "com.test.pkg"
        every { UiDevice.getAppVersionCode(any(), testPkg) } returns 223
        val script = mockk<Script> {
            every { appPkg } returns "$testPkg@@1.0.2"
            every { appMinVersion } returns 100
            every { appMaxVersion } returns 200
        }

        // When
        val result = script.matchAppVersion(mockk(), testPkg)

        // Then
        assertFalse(result)

        // TearDown
        unmockkStatic(UiDevice::class)
    }

    @Test
    fun `should false when matchAppVersion if package name is different`() {
        // SetUp
        mockkStatic(UiDevice::class)

        // Given
        val testPkg = "com.test.pkg"
        every { UiDevice.getAppVersionCode(any(), testPkg) } returns 123
        val script = mockk<Script> {
            every { appPkg } returns "com.test.pkg2@@1.0.2"
            every { appMinVersion } returns 100
            every { appMaxVersion } returns 200
        }

        // When
        val result = script.matchAppVersion(mockk(), testPkg)

        // Then
        assertFalse(result)

        // TearDown
        unmockkStatic(UiDevice::class)
    }

    @Test
    fun `should true when matchScreenDisplay if has current flag`() {
        // SetUp
        mockkStatic(UiDevice::class)

        // Given
        every {
            UiDevice.getCurrentScreenDisplay(any())
        } returns ScreenDisplayFlag(ScreenDisplayFlag.PAD)
        val script = mockk<Script> {
            every { screenDisplay } returns ScreenDisplayFlag("unfold|pad")
        }

        // When
        val result = script.matchScreenDisplay(mockk())

        // Then
        assertTrue(result)

        // TearDown
        unmockkStatic(UiDevice::class)
    }

    @Test
    fun `should false when matchScreenDisplay if no current flag`() {
        // SetUp
        mockkStatic(UiDevice::class)

        // Given
        every {
            UiDevice.getCurrentScreenDisplay(any())
        } returns ScreenDisplayFlag(ScreenDisplayFlag.NORMAL)
        val script = mockk<Script> {
            every { screenDisplay } returns ScreenDisplayFlag("unfold|pad")
        }

        // When
        val result = script.matchScreenDisplay(mockk())

        // Then
        assertFalse(result)

        // TearDown
        unmockkStatic(UiDevice::class)
    }

    @Test
    fun `should true when matchScreenOrientation if has current flag`() {
        // SetUp
        mockkStatic(UiDevice::class)

        // Given
        every {
            UiDevice.getCurrentScreenOrientation(any())
        } returns ScreenOrientationFlag(ScreenOrientationFlag.LANDSCAPE)
        val script = mockk<Script> {
            every { screenOrientation } returns ScreenOrientationFlag("portrait|landscape")
        }

        // When
        val result = script.matchScreenOrientation(mockk())

        // Then
        assertTrue(result)

        // TearDown
        unmockkStatic(UiDevice::class)
    }

    @Test
    fun `should false when matchScreenOrientation if no current flag`() {
        // SetUp
        mockkStatic(UiDevice::class)

        // Given
        every {
            UiDevice.getCurrentScreenOrientation(any())
        } returns ScreenOrientationFlag(ScreenOrientationFlag.LANDSCAPE)
        val script = mockk<Script> {
            every { screenOrientation } returns ScreenOrientationFlag("portrait")
        }

        // When
        val result = script.matchScreenOrientation(mockk())

        // Then
        assertFalse(result)

        // TearDown
        unmockkStatic(UiDevice::class)
    }

    @Test
    fun `should true when matchScreenRotation if has current flag`() {
        // SetUp
        mockkStatic(UiDevice::class)

        // Given
        every {
            UiDevice.getCurrentScreenRotation(any())
        } returns ScreenRotationFlag(ScreenRotationFlag.ROTATION_90)
        val script = mockk<Script> {
            every { screenRotation } returns ScreenRotationFlag("90|270")
        }

        // When
        val result = script.matchScreenRotation(mockk())

        // Then
        assertTrue(result)

        // TearDown
        unmockkStatic(UiDevice::class)
    }

    @Test
    fun `should false when matchScreenRotation if no current flag`() {
        // SetUp
        mockkStatic(UiDevice::class)

        // Given
        every {
            UiDevice.getCurrentScreenRotation(any())
        } returns ScreenRotationFlag(ScreenRotationFlag.ROTATION_90)
        val script = mockk<Script> {
            every { screenRotation } returns ScreenRotationFlag("0|180")
        }

        // When
        val result = script.matchScreenRotation(mockk())

        // Then
        assertFalse(result)

        // TearDown
        unmockkStatic(UiDevice::class)
    }
}