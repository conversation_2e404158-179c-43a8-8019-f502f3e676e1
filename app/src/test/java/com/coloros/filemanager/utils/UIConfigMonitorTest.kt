/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : UIConfigMonitorTest
 ** Description : UIConfigMonitor Unit Test
 ** Version     : 1.0
 ** Date        : 2021/07/28
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  hao.li       2021/07/28      1.0        create
 ***********************************************************************/
package com.coloros.filemanager.utils

import androidx.activity.ComponentActivity
import androidx.lifecycle.MutableLiveData
import com.coloros.filemanager.BaseTest
import com.coui.responsiveui.config.ResponsiveUIConfig
import com.coui.responsiveui.config.UIConfig
import com.coui.responsiveui.config.UIScreenSize
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.helper.uiconfig.type.ScreenFoldConfig
import com.filemanager.common.helper.uiconfig.type.ScreenOrientationConfig
import com.filemanager.common.helper.uiconfig.type.ZoomWindowConfig
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.WindowUtils
import com.heytap.addon.zoomwindow.OplusZoomWindowInfo
import com.heytap.addon.zoomwindow.OplusZoomWindowManager
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito.`when`
import org.mockito.Mockito.mock
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowApplicationPackageManager


@RunWith(RobolectricTestRunner::class)
@Ignore("stupid test")
class UIConfigMonitorTest : BaseTest() {
    private lateinit var mUIConfigMonitor: UIConfigMonitor

    @Before
    override fun setUp() {
        mUIConfigMonitor = UIConfigMonitor.instance
        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.isSmallScreenPhone }.answers { false }
    }

    @After
    override fun tearDown() {
        mUIConfigMonitor.recycle()
        unmockkStatic(FeatureCompat::class)
    }

    @Test
    fun should_verify_ui_change_when_config_changed() {
        val configList = ArrayList<IUIConfig>()
        Assert.assertEquals(false, UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList))
        configList.add(ScreenFoldConfig(UIConfig.Status.FOLD))
        Assert.assertEquals(true, UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList))
        configList.removeIf { it is ScreenFoldConfig }
        Assert.assertEquals(false, UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList))
        configList.add(ZoomWindowConfig(true, windowPortrait = true))
        Assert.assertEquals(true, UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList))
        configList.removeIf { it is ZoomWindowConfig }
        Assert.assertEquals(false, UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList))
        configList.add(ScreenOrientationConfig(1))
        Assert.assertEquals(true, UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList))
        configList.removeIf { it is ScreenOrientationConfig }
        Assert.assertEquals(false, UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList))
    }

    @Test
    fun `test multi window mode changed`() {
        Assert.assertEquals(false, UIConfigMonitor.isMultiWindow())
        mUIConfigMonitor.onMultiWindowModeChanged(true)
        Assert.assertEquals(true, UIConfigMonitor.isMultiWindow())
        mUIConfigMonitor.onMultiWindowModeChanged(false)
        Assert.assertEquals(false, UIConfigMonitor.isMultiWindow())
    }

    @Test
    fun should_verify_screen_fold_when_config_changed() {
        val activityController = Robolectric.buildActivity(ComponentActivity::class.java).create()
        val listener = object : UIConfigMonitor.OnUIConfigChangeListener {
            override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
                Assert.assertEquals(true, configList.isNotEmpty())
            }
        }
        mUIConfigMonitor.apply {
            attachActivity(activityController.get())
            addOnUIConfigChangeListener(listener)
        }

        activityController.destroy()
        mUIConfigMonitor.removeOnUIConfigChangeListener(listener)
        Assert.assertEquals(0, mUIConfigMonitor.mAttachActivityCount)
        Assert.assertEquals(0, mUIConfigMonitor.mChangeListenerSet?.size ?: 0)
    }

    @Ignore("Ignore for unknown reasons and open it later")
    @Test
    @Config(shadows = [ShadowApplicationPackageManager::class])
    fun should_verify_zoom_window_state() {
        val zoomWindowManager = mock(OplusZoomWindowManager::class.java)
        val zoomWindowInfo = OplusZoomWindowInfo().apply {
            this.rotation = UIConfigMonitor.ZOOM_WINDOW_PORTRAIT
        }
        `when`(zoomWindowManager.currentZoomWindowState).thenReturn(zoomWindowInfo)
        Assert.assertEquals(false, UIConfigMonitor.isZoomWindowPortrait())
        Assert.assertEquals(false, UIConfigMonitor.isZoomWindowShow())

        zoomWindowInfo.zoomPkg = AppUtils.getPackageName()
        zoomWindowInfo.windowShown = true
        Assert.assertEquals(true, UIConfigMonitor.isZoomWindowPortrait())
        Assert.assertEquals(true, UIConfigMonitor.isZoomWindowShow())
    }

    @Test
    fun should_return_String_when_screenChangeState2String() {
        var desc = UIConfigMonitor.screenChangeState2String(UIConfigMonitor.SCREEN_DEFAULT)
        Assert.assertEquals(desc, "default")

        desc = UIConfigMonitor.screenChangeState2String(UIConfigMonitor.SCREEN_LAUNCH_FROM_SMALL)
        Assert.assertEquals(desc, "launch from small")

        desc = UIConfigMonitor.screenChangeState2String(UIConfigMonitor.SCREEN_LAUNCH_FROM_LARGE)
        Assert.assertEquals(desc, "launch from large")

        desc = UIConfigMonitor.screenChangeState2String(UIConfigMonitor.SCREEN_SMALL_TO_LARGE)
        Assert.assertEquals(desc, "small screen to large")

        desc = UIConfigMonitor.screenChangeState2String(UIConfigMonitor.SCREEN_LARGE_TO_SMALL)
        Assert.assertEquals(desc, "large screen to small")

        desc = UIConfigMonitor.screenChangeState2String(UIConfigMonitor.SCREEN_LARGE_TO_SMALL)
        Assert.assertEquals(desc, "large screen to small")

        desc = UIConfigMonitor.screenChangeState2String(10)
        Assert.assertEquals(desc, "10")
    }

    @Test
    fun should_return_int_when_changeScreenState() {
        UIConfigMonitor.instance.screenChangeState = UIConfigMonitor.SCREEN_DEFAULT
        var state = UIConfigMonitor.changeScreenState(WindowUtils.SMALL)
        Assert.assertEquals(state, UIConfigMonitor.SCREEN_LAUNCH_FROM_SMALL)

        state = UIConfigMonitor.changeScreenState(WindowUtils.LARGE)
        Assert.assertEquals(state, UIConfigMonitor.SCREEN_SMALL_TO_LARGE)

        state = UIConfigMonitor.changeScreenState(WindowUtils.SMALL)
        Assert.assertEquals(state, UIConfigMonitor.SCREEN_LARGE_TO_SMALL)

        UIConfigMonitor.instance.screenChangeState = UIConfigMonitor.SCREEN_DEFAULT
        state = UIConfigMonitor.changeScreenState(WindowUtils.LARGE)
        Assert.assertEquals(state, UIConfigMonitor.SCREEN_LAUNCH_FROM_LARGE)

        state = UIConfigMonitor.changeScreenState(WindowUtils.SMALL)
        Assert.assertEquals(state, UIConfigMonitor.SCREEN_LARGE_TO_SMALL)

        state = UIConfigMonitor.changeScreenState(WindowUtils.LARGE)
        Assert.assertEquals(state, UIConfigMonitor.SCREEN_SMALL_TO_LARGE)
    }

    @Test
    fun should_return_int_when_getCurrentScreenState() {
        UIConfigMonitor.instance.screenChangeState = UIConfigMonitor.SCREEN_DEFAULT
        UIConfigMonitor.changeScreenState(WindowUtils.SMALL)
        Assert.assertEquals(UIConfigMonitor.getCurrentScreenState(), UIConfigMonitor.SCREEN_LAUNCH_FROM_SMALL)
    }

    @Test
    fun should_return_boolean_when_isCurrentSmallScreen() {
        UIConfigMonitor.instance.screenChangeState = UIConfigMonitor.SCREEN_DEFAULT
        Assert.assertFalse(UIConfigMonitor.isCurrentSmallScreen())

        UIConfigMonitor.instance.screenChangeState = UIConfigMonitor.SCREEN_LARGE_TO_SMALL
        Assert.assertTrue(UIConfigMonitor.isCurrentSmallScreen())

        UIConfigMonitor.instance.screenChangeState = UIConfigMonitor.SCREEN_LAUNCH_FROM_SMALL
        Assert.assertTrue(UIConfigMonitor.isCurrentSmallScreen())
    }

    @Test
    fun should_return_int_when_getScreenWidth() {
        UIConfigMonitor.instance.mResponsiveUIConfig = null
        Assert.assertEquals(0, UIConfigMonitor.getScreenWidth())

        val uiConfig = mock(ResponsiveUIConfig::class.java)
        val screenSize = UIScreenSize(640, 710, 640)
        `when`(uiConfig.uiScreenSize).thenReturn(MutableLiveData(screenSize))
        UIConfigMonitor.instance.mResponsiveUIConfig = uiConfig
        screenSize.widthDp = 640
        screenSize.heightDp = 710
        Assert.assertEquals(640, UIConfigMonitor.getScreenWidth())

        screenSize.widthDp = 840
        screenSize.heightDp = 710
        Assert.assertEquals(840, UIConfigMonitor.getScreenWidth())
    }
}