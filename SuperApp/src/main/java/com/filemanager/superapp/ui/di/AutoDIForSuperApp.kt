/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForFileBrowser
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.filemanager.superapp.ui.di

import com.filemanager.superapp.ui.superapp.SuperAppApi
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import org.koin.dsl.module

object AutoDIForSuperApp {

    val superAppModule = module {
        single<ISuperApp>(createdAtStart = true) {
            SuperAppApi
        }
    }
}