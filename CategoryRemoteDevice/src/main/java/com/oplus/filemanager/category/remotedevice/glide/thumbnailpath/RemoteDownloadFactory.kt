/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemotePathFactory.kt
 ** Description: RemotePathFactory
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.category.remotedevice.glide.thumbnailpath

import android.content.Context
import android.graphics.Bitmap
import androidx.annotation.Keep
import com.bumptech.glide.load.model.ModelLoader
import com.bumptech.glide.load.model.ModelLoaderFactory
import com.bumptech.glide.load.model.MultiModelLoaderFactory
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean

@Keep
class RemoteDownloadFactory(context: Context) :
    ModelLoaderFactory<DownloadRemoteFileBean, Bitmap> {

    private val appContext = context.applicationContext

    override fun build(multiFactory: MultiModelLoaderFactory): ModelLoader<DownloadRemoteFileBean, Bitmap> {
        return RemoteDownloadLoader(appContext)
    }

    override fun teardown() {
        //do nothing
    }
}
