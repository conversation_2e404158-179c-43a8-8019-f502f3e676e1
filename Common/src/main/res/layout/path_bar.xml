<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:focusable="false"
    android:layout_marginStart="@dimen/browser_path_margin"
    android:layout_marginEnd="@dimen/browser_path_margin">

    <TextView
        android:id="@+id/root_path"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:background="@color/color_transparent"
        android:ellipsize="middle"
        android:focusable="false"
        android:gravity="center_vertical"
        android:maxWidth="@dimen/path_bar_root_max_width"
        android:singleLine="true"
        android:textColor="@color/black" />

    <ImageView
        android:id="@+id/root_path_mark"
        android:layout_width="@dimen/dimen_24dp"
        android:layout_height="@dimen/dimen_24dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/dimen_3dp"
        android:layout_toEndOf="@+id/root_path"
        android:src="@drawable/pathbar_middle_img" />

    <HorizontalScrollView
        android:id="@+id/path_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toEndOf="@id/root_path_mark"
        android:focusable="false"
        android:overScrollMode="never"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/path_layout"
            android:layout_width="wrap_content"
            android:minHeight="@dimen/dimen_24dp"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:overScrollMode="always" />

    </HorizontalScrollView>

    <ImageView
        android:id="@+id/path_bar_left_gradient_img"
        android:layout_width="@dimen/path_bar_gradient_width"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/root_path_mark"
        android:background="@drawable/path_bar_left_gradient"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/path_bar_right_gradient_img"
        android:layout_width="@dimen/path_bar_gradient_width"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:background="@drawable/path_bar_right_gradient"
        android:visibility="gone" />

</RelativeLayout>