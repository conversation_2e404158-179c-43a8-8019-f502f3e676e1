/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemoteFileDownloadDispatcher
 * * Description : 远程设备下载文件 Dispatch
 * * Version     : 1.0
 * * Date        : 2024/12/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download.service

import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Looper
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.Utils
import com.filemanager.fileoperate.NotifyMediaScannerBatchAction
import com.oplus.filemanager.category.remotedevice.download.monitor.DownloadNotifyMonitor
import com.oplus.filemanager.category.remotedevice.download.monitor.DownloadProgressMonitor
import com.oplus.filemanager.category.remotedevice.download.monitor.DownloadSeedlingMonitor
import com.oplus.filemanager.interfaze.remotedevice.DownloadRemoteFileCallback
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.interfaze.remotedevice.OperateCallback
import com.oplus.filemanager.remotedevice.ResultConstant
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.CopyOnWriteArrayList

class RemoteFileDownloadDispatcher(val context: Service, private val scope: CoroutineScope) {

    companion object {
        private const val TAG = "RemoteFileDownloadDispatcher"
        private const val CONCURRENT_DOWNLOAD_COUNT = 1

        const val ACTION_START_DOWNLOAD = "action_start_download"
        const val ACTION_CANCEL_DOWNLOAD = "action_cancel_download"
        const val ACTION_PAUSE_DOWNLOAD = "action_pause_download"
        const val ACTION_CONTINUE_DOWNLOAD = "action_continue_download"
        const val ACTION_STOP_SERVICE = "action_stop_service"

        private var downloadCallbacks: CopyOnWriteArrayList<DownloadRemoteFileCallback> = CopyOnWriteArrayList()
        /**
         * Current document download task.
         */
        private var currentDownloadingTask: DownloadTaskInfo? = null

        fun registerDownloadCallback(callback: DownloadRemoteFileCallback) {
            if (!this.downloadCallbacks.contains(callback)) {
                this.downloadCallbacks.add(callback)
            }
        }

        fun unregisterDownloadCallback(callback: DownloadRemoteFileCallback) {
            this.downloadCallbacks.remove(callback)
        }

        /**
         * 开始下载的service
         */
        fun createStartDownloadService(
            context: Context,
            deviceId: String,
            downloadFiles: ArrayList<String>,
            fileSizes: ArrayList<Long>,
            destPath: String,
            code: Int,
            pendingIntent: PendingIntent? = null
        ) {
            val intent = Intent(context, DownloadRemoteFileService::class.java)
            intent.action = ACTION_START_DOWNLOAD
            intent.putExtra(KtConstants.P_DEVICE_ID, deviceId)
            intent.putStringArrayListExtra(KtConstants.P_PATH_LIST, downloadFiles)
            intent.putExtra(KtConstants.P_SIZE_LIST, fileSizes)
            intent.putExtra(KtConstants.P_DEST_PATH, destPath)
            intent.putExtra(KtConstants.P_CODE, code)
            intent.putExtra(KtConstants.P_PENDING_INTENT, pendingIntent)
            startService(context, intent)
        }

        /**
         * 取消下载
         */
        fun createCancelDownloadService(context: Context, deviceId: String, taskId: Int): Intent {
            val intent = Intent(context, DownloadRemoteFileService::class.java)
            intent.action = ACTION_CANCEL_DOWNLOAD
            intent.putExtra(KtConstants.P_DEVICE_ID, deviceId)
            intent.putExtra(KtConstants.P_TASK_ID, taskId)
            return intent
        }

        /**
         * 暂停下载
         */
        fun createPauseDownloadService(context: Context, deviceId: String, taskId: Int): Intent {
            val intent = Intent(context, DownloadRemoteFileService::class.java)
            intent.action = ACTION_PAUSE_DOWNLOAD
            intent.putExtra(KtConstants.P_DEVICE_ID, deviceId)
            intent.putExtra(KtConstants.P_TASK_ID, taskId)
            return intent
        }

        /**
         * 继续下载
         */
        fun createContinueDownloadService(context: Context, deviceId: String, taskId: Int): Intent {
            val intent = Intent(context, DownloadRemoteFileService::class.java)
            intent.action = ACTION_CONTINUE_DOWNLOAD
            intent.putExtra(KtConstants.P_DEVICE_ID, deviceId)
            intent.putExtra(KtConstants.P_TASK_ID, taskId)
            return intent
        }

        fun stopDownloadService(context: Context) {
            val intent = Intent(context, DownloadRemoteFileService::class.java)
            intent.setAction(ACTION_STOP_SERVICE)
            startService(context, intent, false)
        }

        @Suppress("TooGenericExceptionCaught")
        fun startService(context: Context, intent: Intent, foreground: Boolean = true) {
            Log.d(TAG, "startService ${intent.action}")
            try {
                if (foreground && PermissionUtils.hasNotificationPermission(context)) {
                    context.startForegroundService(intent)
                } else {
                    context.startService(intent)
                }
            } catch (e: Exception) {
                Log.e(TAG, "startService error", e)
            }
        }

        fun getTask(taskId: Int): DownloadTaskInfo? {
            if (currentDownloadingTask?.taskId == taskId) {
                return currentDownloadingTask
            }
            return null
        }

        fun getCurrentDownloadingTask(): DownloadTaskInfo? {
            return currentDownloadingTask
        }
    }

    private val remoteDeviceApi: IRemoteDevice? by lazy {
        Injector.injectFactory<IRemoteDevice>()
    }

    /**
     * Ready async document download in the order
     */
    private var readyDownloadTasks = ConcurrentLinkedQueue<DownloadTaskInfo>()

    private var pausedDownloadTasks = ConcurrentLinkedQueue<DownloadTaskInfo>()

    private var runningDownloadTasks = ConcurrentLinkedQueue<DownloadTaskInfo>()

    private var failureDownloadTasks = ConcurrentLinkedQueue<DownloadTaskInfo>()

    private val mediaScannerBatchAction by lazy {
        NotifyMediaScannerBatchAction(Utils.MEDIA_SCAN_DOWNLOAD)
    }

    private val notifyMonitor: DownloadNotifyMonitor by lazy {
        DownloadNotifyMonitor(context)
    }

    private val seedlingMonitor: DownloadSeedlingMonitor by lazy {
        DownloadSeedlingMonitor(context)
    }


    fun onStartCommand(intent: Intent) {
        val action = intent.action ?: ""
        Log.d(TAG, "onStartCommand -> action=$action")
        when (action) {
            ACTION_START_DOWNLOAD -> handleEnqueueDownload(intent)
            ACTION_CANCEL_DOWNLOAD -> handleCancelDownload(intent)
            ACTION_PAUSE_DOWNLOAD -> handlePauseDownload(intent)
            ACTION_CONTINUE_DOWNLOAD -> handleContinueDownload(intent)
        }
    }

    private fun handleEnqueueDownload(intent: Intent) {
        val downloadTask = parseDownloadTaskInfo(intent)
        if (checkDownloadInfoValidity(downloadTask)) {
            readyDownloadTasks.add(downloadTask)
            registerDefaultCallback(downloadTask)
            execute()
        } else {
            Log.d(TAG, "handleStartDownload -> download info invalid")
            stopIfNoTasks()
        }
    }

    private fun registerDefaultCallback(downloadTask: DownloadTaskInfo) {
        registerDownloadCallback(notifyMonitor)
        notifyMonitor.startMonitor(downloadTask)
        if (seedlingMonitor.isSupport()) {
            Log.w(TAG, "registerDefaultCallback -> support download seedling")
            registerDownloadCallback(seedlingMonitor)
            seedlingMonitor.startMonitor(downloadTask)
        }
    }

    private fun unregisterDefaultCallbacks() {
        unregisterDownloadCallback(notifyMonitor)
        if (seedlingMonitor.isSupport()) {
            unregisterDownloadCallback(seedlingMonitor)
        }
    }

    private fun parseDownloadTaskInfo(intent: Intent): DownloadTaskInfo {
        val deviceId = IntentUtils.getString(intent, KtConstants.P_DEVICE_ID) ?: ""
        val downloadFiles = IntentUtils.getStringArrayList(intent, KtConstants.P_PATH_LIST) ?: emptyList<String>()
        val fileSizes = IntentUtils.getSerializableExtra(intent, KtConstants.P_SIZE_LIST) as? ArrayList<Long> ?: emptyList<Long>()
        val destPath = IntentUtils.getString(intent, KtConstants.P_DEST_PATH) ?: ""
        val code = IntentUtils.getInt(intent, KtConstants.P_CODE, MessageConstant.MSG_DOWNLOAD_REMOTE_FILE)
        val files = ArrayList<Pair<String, Long>>()
        downloadFiles.forEachIndexed { index, path ->
            files.add(Pair(path, fileSizes.get(index)))
        }
        val pendingIntent = IntentUtils.getParcelable(intent, KtConstants.P_PENDING_INTENT) as? PendingIntent
        return DownloadTaskInfo(deviceId, files, destPath, code, pendingIntent = pendingIntent)
    }

    fun stopIfNoTasks() {
        if (runningDownloadTasks.isEmpty() && readyDownloadTasks.isEmpty() && pausedDownloadTasks.isEmpty() && failureDownloadTasks.isEmpty()) {
            Log.d(TAG, "No running or ready task, stop self")
            stopService()
        }
    }

    private fun execute(): Boolean {
        val executeTasks = mutableListOf<DownloadTaskInfo>()
        val isRunning: Boolean
        synchronized(this) {
            val iterator = readyDownloadTasks.iterator()
            while (iterator.hasNext()) {
                val downloadTask = iterator.next()
                if (runningDownloadTasks.size >= CONCURRENT_DOWNLOAD_COUNT) {
                    Log.w(TAG, "execute has running task!!!")
//                    break
                }
                iterator.remove()
                executeTasks.add(downloadTask)
                runningDownloadTasks.add(downloadTask)
            }
            isRunning = runningDownloadTasks.size > 0
        }
        Log.d(TAG, "execute -> isRunning = $isRunning; count = ${readyDownloadTasks.size}-${runningDownloadTasks.size}")
        executeTasks.forEach { taskInfo ->
            internalDownload(taskInfo)
        }
        return isRunning
    }

    fun finished(taskInfo: DownloadTaskInfo) {
        Log.d(TAG, "finished ->$taskInfo")
        if (!runningDownloadTasks.remove(taskInfo)) {
            Log.w(TAG, "finished ->remove from running error")
        }
        val isRunning = execute()
        if (!isRunning) {
            Log.w(TAG, "finished -> no running task")
            stopIfNoTasks()
        }
    }

    private fun internalDownload(downloadTask: DownloadTaskInfo) {
        val api = remoteDeviceApi ?: return
        asyncCall {
            Log.d(TAG, "internalDownload $downloadTask")
            val taskId = api.requestDownloadFile(downloadTask.deviceId, downloadTask.paths(), downloadTask.destPath,
                object : DownloadRemoteFileCallback {
                    override fun onProgressEachFile(path: String?, status: Int, progress: Float) {
                        Log.d(TAG, "internalDownload -> onProgressEachFile:$path status:$status progress:$progress")
                        downloadCallbacks.forEach {
                            it.onProgressEachFile(path, status, progress)
                        }
                        // 下载完成一个文件，触发媒体库扫描
                        if (DownloadRemoteFileCallback.isCompleted(status)) {
                            triggerMediaFileScan(path ?: "")
                        }
                    }

                    override fun onProgress(ratio: Int) {
                        Log.d(TAG, "internalDownload -> progress:$ratio")
                        downloadCallbacks.forEach {
                            it.onProgress(ratio)
                        }
                    }

                    override fun onResult(operateCode: Int, resultCode: Int) {
                        Log.w(TAG, "internalDownload -> operate:$operateCode result:$resultCode")
                        if (ResultConstant.isSuccess(resultCode)) {
                            // 刷新媒体库
                            triggerMediaFileScan(downloadTask.destPath)
                        }
                        handleDownloadingCode(downloadTask, resultCode)
                        if (!ResultConstant.isTransferring(resultCode)) {
                            finished(downloadTask)
                        }
                        downloadCallbacks.forEach {
                            it.onResult(operateCode, resultCode)
                        }
                    }
                })
            downloadTask.taskId = taskId
            currentDownloadingTask = downloadTask
            notifyMonitor.setDownloadingTask(downloadTask)
            seedlingMonitor.setDownloadingTask(downloadTask)
            Log.e(TAG, "internalDownload taskId:$taskId")
            if (taskId < 0) { // 异常taskId
                finished(downloadTask)
                val resultCode = ResultConstant.map(taskId)
                downloadCallbacks.forEach {
                    it.onResult(OperateCallback.OPERATE_BEGIN_DOWNLOAD, resultCode)
                }
            }
        }
    }

    private fun handleDownloadingCode(downloadTask: DownloadTaskInfo, code: Int) {
        if (ResultConstant.FILE_TRANS_PAUSE == code) { // 暂停
            pausedDownloadTasks.add(downloadTask)
        } else if (ResultConstant.FILE_TRANS_CONTINUE == code) { // 继续
            readyDownloadTasks.remove(downloadTask)
            pausedDownloadTasks.remove(downloadTask)
            runningDownloadTasks.add(downloadTask)
            currentDownloadingTask = downloadTask
        } else if (ResultConstant.RESULT_CODE_CANCEL == code) { // 取消
            clearAllTask()
        } else if (ResultConstant.isFail(code)) { // 失败
            failureDownloadTasks.add(downloadTask)
        }
    }

    private fun clearAllTask() {
        Log.d(TAG, "clearAllTask")
        runningDownloadTasks.clear()
        pausedDownloadTasks.clear()
        failureDownloadTasks.clear()
        readyDownloadTasks.clear()
    }


    private fun handleCancelDownload(intent: Intent) {
        val deviceId = IntentUtils.getString(intent, KtConstants.P_DEVICE_ID) ?: ""
        val taskId = IntentUtils.getInt(intent, KtConstants.P_TASK_ID, 0)
        synchronized(this) {
            val taskList = mutableListOf<DownloadTaskInfo>()
            taskList.addAll(runningDownloadTasks)
            taskList.addAll(pausedDownloadTasks)
            taskList.addAll(failureDownloadTasks)
            val runningTask = taskList.find { it.taskId == taskId }
            if (runningTask != null) { // 正在下载
                if (deviceId != runningTask.deviceId) {
                    Log.w(TAG, "handleCancelDownload deviceId:$deviceId is not same as task:${runningTask.taskId}")
                    return
                }
                clearAllTask()
                internalCancelDownload(runningTask)
            } else {
                Log.w(TAG, "handleCancelDownload taskId:$taskId not found running task!!")
                stopIfNoTasks()
            }
        }
    }

    private fun internalCancelDownload(task: DownloadTaskInfo) {
        asyncCall {
            Log.d(TAG, "internalCancelDownload -> task:$task")
            remoteDeviceApi?.cancelDownloadFile(task.deviceId, task.taskId, object : OperateCallback {

                override fun onResult(operateCode: Int, resultCode: Int) {
                    Log.w(TAG, "cancelDownload -> result:$resultCode")
                    downloadCallbacks.forEach {
                        it.onResult(operateCode, resultCode)
                    }
                }
            })
        }
    }

    private fun handlePauseDownload(intent: Intent) {
        val deviceId = IntentUtils.getString(intent, KtConstants.P_DEVICE_ID) ?: ""
        val taskId = IntentUtils.getInt(intent, KtConstants.P_TASK_ID, 0)
        synchronized(this) {
            val runningTask = runningDownloadTasks.find { it.taskId == taskId }
            if (runningTask != null) { // 正在下载
                if (deviceId != runningTask.deviceId) {
                    Log.w(TAG, "handlePauseDownload deviceId:$deviceId is not same as task:${runningTask.taskId}")
                    return
                }
                internalPauseDownload(runningTask)
            } else {
                Log.w(TAG, "handlePauseDownload taskId:$taskId not found running task!!")
                stopIfNoTasks()
            }
        }
    }

    private fun internalPauseDownload(task: DownloadTaskInfo) {
        asyncCall {
            Log.d(TAG, "internalPauseDownload -> task:$task")
            remoteDeviceApi?.pauseDownloadFile(task.deviceId, task.taskId, object : OperateCallback {
                override fun onResult(operateCode: Int, resultCode: Int) {
                    Log.w(TAG, "pauseDownload -> result:$resultCode")
                    downloadCallbacks.forEach {
                        it.onResult(operateCode, resultCode)
                    }
                }
            })
        }
    }

    private fun handleContinueDownload(intent: Intent) {
        val deviceId = IntentUtils.getString(intent, KtConstants.P_DEVICE_ID) ?: ""
        val taskId = IntentUtils.getInt(intent, KtConstants.P_TASK_ID, 0)
        Log.d(TAG, "handleContinueDownload paused:$pausedDownloadTasks failed:$failureDownloadTasks")
        synchronized(this) {
            val taskList = mutableListOf<DownloadTaskInfo>()
            taskList.addAll(pausedDownloadTasks)
            taskList.addAll(failureDownloadTasks)
            val runningTask = taskList.find { it.taskId == taskId }
            if (runningTask != null) { //
                if (deviceId != runningTask.deviceId) {
                    Log.w(TAG, "handleContinueDownload deviceId:$deviceId is not same as task:${runningTask.taskId}")
                    return
                }
                internalContinueDownload(runningTask)
            } else {
                Log.w(TAG, "handleContinueDownload taskId:$taskId not found running task!!")
                stopIfNoTasks()
            }
        }
    }

    private fun internalContinueDownload(task: DownloadTaskInfo) {
        asyncCall {
            Log.d(TAG, "internalContinueDownload -> task:$task")
            remoteDeviceApi?.continueDownloadFile(task.deviceId, task.taskId, object : OperateCallback {
                override fun onResult(operateCode: Int, resultCode: Int) {
                    Log.d(TAG, "continueDownload -> result:$resultCode")
                    downloadCallbacks.forEach {
                        it.onResult(operateCode, resultCode)
                    }
                }
            })
        }
    }

    /**
     * 异步调用
     */
    private fun asyncCall(run: Runnable) {
        if (isMainThread()) {
            scope.launch(Dispatchers.IO) {
                run.run()
            }
        } else {
            run.run()
        }
    }

    /**
     * 是否在主线程
     */
    private fun isMainThread(): Boolean {
        return Thread.currentThread() == Looper.getMainLooper().thread
    }

    /**
     * 触发媒体库扫描
     */
    fun triggerMediaFileScan(filePath: String) {
        mediaScannerBatchAction.add(filePath)
        mediaScannerBatchAction.flush()
    }

    private fun checkDownloadInfoValidity(downloadTask: DownloadTaskInfo): Boolean {
        return downloadTask.deviceId.isNotEmpty() && downloadTask.downloadFiles.isNotEmpty() && downloadTask.destPath.isNotEmpty()
    }

    @Suppress("TooGenericExceptionCaught")
    private fun stopService() {
        try {
            stopDownloadService(context)
        } catch (e: Exception) {
            Log.e(TAG, "stopIfNoTasks error", e)
        }
    }

    fun cancelMonitor() {
        Log.d(TAG, "cancelMonitor")
        downloadCallbacks.forEach {
            if (it is DownloadProgressMonitor) {
                it.cancelMonitor()
            }
        }
        unregisterDefaultCallbacks()
    }
}