/*
 * ********************************************************************
 *  * * Copyright (C), 2021 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  RecycleBinUtilsTest.java
 *  * * Description : RecycleBinUtilsTest.java
 *  * * Version     : 1.0
 *  * * Date        : 21-12-14 下午5:34
 *  * * Author      : W9010863
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.coloros.filemanager.common.fileutils

import android.content.ContentUris
import android.content.ContentValues
import android.content.Context
import android.net.Uri
import android.os.storage.StorageManager
import android.os.storage.StorageVolume
import android.provider.MediaStore
import android.text.TextUtils
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.MediaFileCompat
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.ImageFileWrapper
import io.mockk.InternalPlatformDsl
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import junit.framework.Assert
import org.junit.After
import org.junit.Before
import org.junit.Test
import java.io.File

class FileMediaHelperTest {
    @MockK
    lateinit var mContext: Context

    @MockK
    lateinit var mSpykMockk: FileMediaHelper

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mContext = mockk {
            every { contentResolver }.returns(mockk(relaxed = true))
            every { applicationContext }.returns(this)
            every { filesDir }.returns(File(""))
        }

        MyApplication.init(mContext)

        mSpykMockk = spyk(FileMediaHelper, recordPrivateCalls = true)
    }

    @Test
    fun `should verify when call getFileUri`() {
        mockkStatic(TextUtils::class)
        var baseFileBean = mockk<BaseFileBean>(relaxed = true)

        every { baseFileBean.mLocalFileUri }.returns(null)
        Assert.assertEquals(null, FileMediaHelper.getFileUri(mContext, baseFileBean))

        var uri = Uri.parse("test")
        every { baseFileBean.mLocalFileUri }.returns(uri)
        every { baseFileBean.mData }.returns(null)
        every { TextUtils.isEmpty(null) }.returns(true)
        Assert.assertEquals(null, FileMediaHelper.getFileUri(mContext, baseFileBean))

        every { baseFileBean.mData }.returns("test-mData")
        every { TextUtils.isEmpty(any()) }.returns(false)
        every { mContext.contentResolver.query(any(), any(), any(), any(), null) }.returns(null)
        Assert.assertEquals(null, FileMediaHelper.getFileUri(mContext, baseFileBean))

        every { mContext.contentResolver.query(any(), any(), any(), any(), null) }.returns(mockk(relaxed = true) {
            every { count }.returns(10)
            every { getLong(any()) }.returns(0L)
        })

        mockkStatic(ContentUris::class)
        every { ContentUris.withAppendedId(any(), any()) }.returns(uri)
        Assert.assertEquals(uri, FileMediaHelper.getFileUri(mContext, baseFileBean))

        try {
            every { mContext.contentResolver.query(any(), any(), any(), any(), null) }.throws(Exception("test-err"))
            Assert.assertEquals(null, FileMediaHelper.getFileUri(mContext, baseFileBean))
        } catch (e: Exception) {
            println("test!!!")
        }
    }

    @Test
    fun `should verify when call queryPathFromUri`() {
        var uri = mockk<Uri>(relaxed = true)

        every { mContext.contentResolver.query(any(), any(), null, null, null) }.returns(null)
        Assert.assertEquals(null, FileMediaHelper.queryPathFromUri(mContext, uri))

        every { mContext.contentResolver.query(any(), any(), null, null, null) }.returns(mockk(relaxed = true) {
            every { moveToFirst() }.returns(true)
            every { getString(0) }.returns("test")
        })
        Assert.assertEquals("test", FileMediaHelper.queryPathFromUri(mContext, uri))

        try {
            every { mContext.contentResolver.query(any(), any(), null, null, null) }.throws(Exception("test-error"))
            Assert.assertEquals(null, FileMediaHelper.queryPathFromUri(mContext, uri))
        } catch (e: Exception) {
            println("test!!!")
        }
    }

    @Test
    fun `should verify when call deleteMediaDBFile`() {
        mockkStatic(Utils::class)
        var baseFileBean = BaseFileBean()

        Assert.assertFalse(FileMediaHelper.deleteMediaDBFile(baseFileBean))

        baseFileBean.mData = "test-mData"
        every { Utils.isOperateDatabase(mContext, any()) }.returns(false)
        Assert.assertTrue(FileMediaHelper.deleteMediaDBFile(baseFileBean))

        every { Utils.isOperateDatabase(mContext, any()) }.returns(true)
        mockkStatic(MediaStore.Files::class)

        every { MediaStore.Files.getContentUri("external") }.returns(mockk(relaxed = true) {
            every { buildUpon() }.returns(mockk(relaxed = true) {
                every { build() }.returns(mockk(relaxed = true))
            })
        })
        baseFileBean.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
        every { mContext.contentResolver.delete(any(), any(), any()) }.returns(0)
        every { mContext.contentResolver.query(any(), arrayOf("_id"), any(), any(), null) }.returns(null)
        Assert.assertTrue(FileMediaHelper.deleteMediaDBFile(baseFileBean))


        baseFileBean.mLocalType = MimeTypeHelper.VIDEO_TYPE
        every { mContext.contentResolver.query(any(), arrayOf("_id"), any(), any(), null) }.returns(mockk(relaxed = true) {
            every { count }.returns(0)
        })
        Assert.assertTrue(FileMediaHelper.deleteMediaDBFile(baseFileBean))

        try {
            every { mContext.contentResolver.delete(any(), any(), any()) }.throws(Exception("test-error"))
            Assert.assertFalse(FileMediaHelper.deleteMediaDBFile(baseFileBean))
        } catch (e: Exception) {
            println("test!!!")
        }
    }

    @Test
    fun `should verify when call getFileTitleInMediaDB`() {

        var baseFileBean = BaseFileBean()

        baseFileBean.mLocalFileUri = mockk(relaxed = true)
        every { mContext.contentResolver.query(any(), any(), null, null, null) }.returns(null)

        Assert.assertNull(InternalPlatformDsl.dynamicCall(mSpykMockk, "getFileTitleInMediaDB", arrayOf(mContext, baseFileBean)) { mockk() })

        every { mContext.contentResolver.query(any(), any(), null, null, null) }.returns(mockk(relaxed = true) {
            every { count }.returns(1)
            every { getString(0) }.returns("test")
        })
        Assert.assertEquals("test", InternalPlatformDsl.dynamicCall(mSpykMockk, "getFileTitleInMediaDB", arrayOf(mContext, baseFileBean)) { mockk() })

        baseFileBean.mLocalFileUri = null
        every { mContext.contentResolver.query(any(), any(), any(), any(), null) }.returns(mockk(relaxed = true) {
            every { count }.returns(1)
            every { getString(0) }.returns("test")
        })
        Assert.assertEquals("test", InternalPlatformDsl.dynamicCall(mSpykMockk, "getFileTitleInMediaDB", arrayOf(mContext, baseFileBean)) { mockk() })

        try {
            every { mContext.contentResolver.query(any(), any(), any(), any(), null) }.throws(Exception("test-error"))
            Assert.assertNull(InternalPlatformDsl.dynamicCall(mSpykMockk, "getFileTitleInMediaDB", arrayOf(mContext, baseFileBean)) { mockk() })
        } catch (e: Exception) {
            println("test!!!")
        }
    }


    @Test
    fun `should verify when call getMediaType`() {
        mockkStatic(MediaFileCompat::class)
        mockkStatic(SdkUtils::class)
        mockkStatic(MimeTypeHelper::class)
        every { SdkUtils.isAtLeastR() }.returns(true)
        every { SdkUtils.isAtLeastS() }.returns(true)

        every { MediaFileCompat.getFileType(any()) }.returns(null)
        Assert.assertEquals(MediaStore.Files.FileColumns.MEDIA_TYPE_NONE, InternalPlatformDsl.dynamicCall(mSpykMockk,
                "getMediaType", arrayOf("test")) { mockk() })
    }

    @Test
    fun `should verify when call doAgainWithFileUri`() {
        every { mContext.contentResolver.update(FileMediaHelper.FILE_URI, any(), "test", any()) }.returns(1)
        Assert.assertTrue(InternalPlatformDsl
                .dynamicCall(mSpykMockk, "doAgainWithFileUri",
                        arrayOf(mContext, mockk<ContentValues>(), "test", arrayOf<String>())) { mockk() } as Boolean)

        try {
            every { mContext.contentResolver.update(FileMediaHelper.FILE_URI, any(), "test", any()) }
                    .throws(Exception("test-error"))
            Assert.assertFalse(InternalPlatformDsl.dynamicCall(mSpykMockk, "doAgainWithFileUri",
                    arrayOf(mContext, mockk<ContentValues>(), "test", arrayOf<String>())) { mockk() } as Boolean)
        } catch (e: Exception) {
            println("test!!!")
        }
    }

    @Test
    fun `should verify when call getVolumeUri`() {
        mockkStatic(TextUtils::class)
        mockkObject(MediaStore.Audio.Media::class)

        every { TextUtils.isEmpty("test") }.returns(true)
        Assert.assertNull(InternalPlatformDsl.dynamicCall(mSpykMockk, "getVolumeUri",
                arrayOf(MimeTypeHelper.AUDIO_TYPE, "test")) { mockk() })

        every { TextUtils.isEmpty("test") }.returns(false)
        InternalPlatformDsl.dynamicCall(mSpykMockk, "getVolumeUri",
                arrayOf(MimeTypeHelper.AUDIO_TYPE, "test")) { mockk() }

        InternalPlatformDsl.dynamicCall(mSpykMockk, "getVolumeUri",
                arrayOf(MimeTypeHelper.VIDEO_TYPE, "test")) { mockk() }

        InternalPlatformDsl.dynamicCall(mSpykMockk, "getVolumeUri",
                arrayOf(MimeTypeHelper.IMAGE_TYPE, "test")) { mockk() }

        InternalPlatformDsl.dynamicCall(mSpykMockk, "getVolumeUri",
                arrayOf(MimeTypeHelper.UNKNOWN_TYPE, "test")) { mockk() }
    }

    @Test
    fun `should verify when call getVolumeName`() {
        var file = mockk<File>(relaxed = true)

        every { file.absolutePath }.returns("test")
        Assert.assertEquals(MediaStore.VOLUME_INTERNAL, InternalPlatformDsl.dynamicCall(mSpykMockk,
                "getVolumeName", arrayOf(file)) { mockk() })

        every { file.absolutePath }.returns("/storage/test")
        var context = MyApplication.sAppContext

        every { context.getSystemService(Context.STORAGE_SERVICE) }.returns(null)
        Assert.assertEquals(MediaStore.VOLUME_INTERNAL, InternalPlatformDsl.dynamicCall(mSpykMockk,
                "getVolumeName", arrayOf(file)) { mockk() })

        var storageManagerMockk = mockk<StorageManager>()

        every { storageManagerMockk.getStorageVolume(file) }.returns(null)
        every { context.getSystemService(Context.STORAGE_SERVICE) }.returns(storageManagerMockk)
        Assert.assertEquals(MediaStore.VOLUME_INTERNAL, InternalPlatformDsl.dynamicCall(mSpykMockk,
                "getVolumeName", arrayOf(file)) { mockk() })

        var storageVolumeMockk = mockk<StorageVolume>()
        every { storageManagerMockk.getStorageVolume(file) }.returns(storageVolumeMockk)
        every { storageVolumeMockk.isPrimary }.returns(true)
        Assert.assertEquals(MediaStore.VOLUME_EXTERNAL_PRIMARY, InternalPlatformDsl.dynamicCall(mSpykMockk,
                "getVolumeName", arrayOf(file)) { mockk() })

        mockkStatic(TextUtils::class)

        every { TextUtils.isEmpty(any()) }.returns(true)
        every { storageVolumeMockk.isPrimary }.returns(false)
        every { storageVolumeMockk.uuid }.returns("test")
        InternalPlatformDsl.dynamicCall(mSpykMockk,
                "getVolumeName", arrayOf(file)) { mockk() }
    }

    @Test
    fun `should verify when call checkArgumentVolumeName`() {
        mockkStatic(TextUtils::class)

        every { TextUtils.isEmpty("") }.returns(true)
        Assert.assertEquals(MediaStore.VOLUME_INTERNAL, InternalPlatformDsl.dynamicCall(mSpykMockk,
                "checkArgumentVolumeName", arrayOf("")) { mockk() })

        every { TextUtils.isEmpty(any()) }.returns(false)
        Assert.assertEquals(MediaStore.VOLUME_INTERNAL, InternalPlatformDsl.dynamicCall(mSpykMockk,
                "checkArgumentVolumeName", arrayOf(MediaStore.VOLUME_INTERNAL)) { mockk() })
        Assert.assertEquals(MediaStore.VOLUME_EXTERNAL, InternalPlatformDsl.dynamicCall(mSpykMockk,
                "checkArgumentVolumeName", arrayOf(MediaStore.VOLUME_EXTERNAL)) { mockk() })
        Assert.assertEquals(MediaStore.VOLUME_EXTERNAL_PRIMARY, InternalPlatformDsl.dynamicCall(mSpykMockk,
                "checkArgumentVolumeName", arrayOf(MediaStore.VOLUME_EXTERNAL_PRIMARY)) { mockk() })

        Assert.assertEquals(MediaStore.VOLUME_INTERNAL, InternalPlatformDsl.dynamicCall(mSpykMockk,
                "checkArgumentVolumeName", arrayOf("aa-1-test")) { mockk() })

        Assert.assertEquals("aaa1", InternalPlatformDsl.dynamicCall(mSpykMockk,
                "checkArgumentVolumeName", arrayOf("aaa1")) { mockk() })
    }

    @Test
    fun `should verify when call insertFileMediaDB`() {
        mockkStatic(Utils::class)
        mockkObject(MediaScannerCompat)
        InternalPlatformDsl.dynamicCall(mSpykMockk, "insertFileMediaDB",
                arrayOf(mContext, "", "")) { mockk() }

        every { Utils.isOperateDatabase(mContext, "test") }.returns(false)
        InternalPlatformDsl.dynamicCall(mSpykMockk, "insertFileMediaDB",
                arrayOf(mContext, "test", "")) { mockk() }

        every { MediaScannerCompat.sendMediaScanner(any(), any()) }.returns(Unit)
        every { Utils.isOperateDatabase(mContext, "test") }.returns(true)
        InternalPlatformDsl.dynamicCall(mSpykMockk, "insertFileMediaDB",
                arrayOf(mContext, "test", "")) { mockk() }
    }

    @Test
    fun `should verify when call checkFileExist`() {
        mockkStatic(SdkUtils::class)

        every { SdkUtils.isAtLeastR() }.returns(true)
        Assert.assertTrue(InternalPlatformDsl.dynamicCall(mSpykMockk, "checkFileExist",
                arrayOf("test")) { mockk() } as Boolean)

        every { SdkUtils.isAtLeastR() }.returns(false)
        Assert.assertFalse(InternalPlatformDsl.dynamicCall(mSpykMockk, "checkFileExist",
                arrayOf("testsdfe3")) { mockk() } as Boolean)
    }

    @Test
    fun `should verify when call getImageFileList`() {
        mockkObject(FileMediaHelper)

        FileMediaHelper.getImageFileList(null, null)

        var bean = mockk<ImageFileWrapper>(relaxed = true)
        every { FileMediaHelper.isCShotCameraPath(mContext, bean) }.returns(true)

        every { bean.getCShot() }.returns(1)
        FileMediaHelper.getImageFileList(mContext, arrayListOf(bean))

        every { bean.getCShot() }.returns(-1)
        FileMediaHelper.getImageFileList(mContext, arrayListOf(bean))

        try {
            every { bean.getCShot() }.throws(Exception("test-error"))
            FileMediaHelper.getImageFileList(mContext, arrayListOf(bean))
        } catch (e: Exception) {
            println("test!!!")
        }
    }

    @Test
    fun `should verify when call getCshotFiles`() {

        InternalPlatformDsl.dynamicCall(mSpykMockk, "getCshotFiles",
                arrayOf(mContext, arrayListOf<BaseFileBean>())) { mockk() }

        every {
            mContext.contentResolver.query(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, any(),
                    any(), any(), null)
        }.returns(null)
        InternalPlatformDsl.dynamicCall(mSpykMockk, "getCshotFiles",
                arrayOf(mContext, arrayListOf<BaseFileBean>(mockk<ImageFileWrapper> {
            every { getCShot() }.returns(1)
        }, mockk()))) { mockk() }


        every {
            mContext.contentResolver.query(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, any(),
                    any(), any(), null)
        }.returns(mockk(relaxed = true) {
            every { moveToNext() }.returns(true).andThen(false)
        })
        InternalPlatformDsl.dynamicCall(mSpykMockk, "getCshotFiles",
                arrayOf(mContext, arrayListOf<BaseFileBean>(mockk<ImageFileWrapper> {
            every { getCShot() }.returns(1)
        }, mockk()))) { mockk() }


        try {
            every {
                mContext.contentResolver.query(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, any(),
                        any(), any(), null)
            }.throws(Exception("test-error"))

            InternalPlatformDsl.dynamicCall(mSpykMockk, "getCshotFiles",
                    arrayOf(mContext, arrayListOf<BaseFileBean>(mockk<ImageFileWrapper> {
                every { getCShot() }.returns(1)
            }, mockk()))) { mockk() }
        } catch (e: Exception) {
            println("test!!!")
        }
    }

    @Test
    fun `should verify when call isCShotCameraPath`() {
        mockkStatic(TextUtils::class)
        mockkStatic(VolumeEnvironment::class)

        Assert.assertFalse(FileMediaHelper.isCShotCameraPath(null, null))

        var bean = BaseFileBean()
        bean.mData = ""
        every { TextUtils.isEmpty("") }.returns(true)
        Assert.assertFalse(FileMediaHelper.isCShotCameraPath(mContext, bean))

        every { TextUtils.isEmpty(any()) }.returns(false)

        bean.mData = "test-InternalSd/DCIM/MyAlbums/111"
        every { VolumeEnvironment.getInternalSdPath(mContext) }.returns("test-InternalSd")
        Assert.assertTrue(FileMediaHelper.isCShotCameraPath(mContext, bean))

        bean.mData = "test-ExternalSd/DCIM/Camera/222"
        every { TextUtils.isEmpty("test") }.returns(false)
        every { VolumeEnvironment.getExternalSdPath(mContext) }.returns("test-ExternalSd")
        Assert.assertTrue(FileMediaHelper.isCShotCameraPath(mContext, bean))
    }


    @Test
    fun `should verify when call updateFileNameInMediaDB`() {
        mockkStatic(Utils::class)
        mockkStatic(MediaFileCompat::class)
        mockkStatic(SdkUtils::class)
        mockkStatic(TextUtils::class)
        mockkObject(MimeTypeHelper.Companion)
        every { SdkUtils.isAtLeastS() }.returns(true)

        var oldFile = BaseFileBean()
        var newFile = BaseFileBean()

        Assert.assertFalse(FileMediaHelper.updateFileNameInMediaDB(mContext, oldFile, newFile))

        oldFile.mData = "test-data"
        Assert.assertFalse(FileMediaHelper.updateFileNameInMediaDB(mContext, oldFile, newFile))

        newFile.mData = "test-data"
        every { Utils.isOperateDatabase(mContext, "test-data") }.returns(false)
        Assert.assertTrue(FileMediaHelper.updateFileNameInMediaDB(mContext, oldFile, newFile))

        every { Utils.isOperateDatabase(mContext, any()) }.returns(true)

        oldFile.mLocalType = MimeTypeHelper.VIDEO_TYPE
        newFile.mDisplayName = ".new name"
        every { mContext.getSystemService(Context.STORAGE_SERVICE) }.returns(null)
        every { mSpykMockk["getVolumeUri"](MimeTypeHelper.VIDEO_TYPE, "test-data") }.returns(null)
        Assert.assertFalse(mSpykMockk.updateFileNameInMediaDB(mContext, oldFile, newFile))

        every { mSpykMockk["getVolumeUri"](MimeTypeHelper.VIDEO_TYPE, "test-data") }.returns(mockk<Uri>(relaxed = true))
        every { MediaFileCompat.getMimeTypeForFile(any()) }.returns("test")
        every { TextUtils.equals("test", "test") }.returns(false)
        oldFile.mDisplayName = ".old name"
        every { mSpykMockk["getFileTitleInMediaDB"](mContext, oldFile) }.returns("test-Title")
        newFile.mData = "/test/data"
        every { mContext.contentResolver.update(any(), any(), any(), any()) }.returns(1)
        Assert.assertTrue(mSpykMockk.updateFileNameInMediaDB(mContext, oldFile, newFile))

        newFile.mDisplayName = "test.new name"
        oldFile.mDisplayName = "old name"
        every { MimeTypeHelper.getMediaTypeByType(any(), any()) }.returns(MediaStore.Files.FileColumns.MEDIA_TYPE_NONE)
        every { mSpykMockk["getMediaType"]("/test/data") }.returns(2)
        every { mContext.contentResolver.update(any(), any(), any(), any()) }.returns(-1)
        Assert.assertFalse(mSpykMockk.updateFileNameInMediaDB(mContext, oldFile, newFile))

        try {
            every { mContext.contentResolver.update(any(), any(), any(), any()) }.throws(Exception("test-error"))
            Assert.assertFalse(mSpykMockk.updateFileNameInMediaDB(mContext, oldFile, newFile))
        } catch (e: Exception) {
            println("test!!!!")
        }
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}