/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.sort
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/10/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.sort

import com.filemanager.common.helper.CategoryHelper
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNotEquals
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class SortRecordModeFactoryTest {

    @Test
    fun `should return category1 when getCategoryKey if category is 1`() {
        assertEquals("category_sort_1", SortRecordModeFactory.getCategoryKey(1))
    }

    @Test
    fun `should return category2 when getCategoryKey if category is 2`() {
        assertEquals("category_sort_2", SortRecordModeFactory.getCategoryKey(2))
    }

    @Test
    fun `should return super_sort_path when getSuperKey if path not null`() {
        val path = arrayOf("/path")
        assertEquals("super_sort_/path", SortRecordModeFactory.getSuperKey(path))
    }

    @Test
    fun `should return super_sort_ when getSuperKey if path is null`() {
        assertEquals("super_sort_", SortRecordModeFactory.getSuperKey(null))
    }

    @Test
    fun `should return super_sort_path_path when getSuperKey if path path`() {
        val path = arrayOf("/path", "/path2/2")
        assertEquals("super_sort_/path/path2/2", SortRecordModeFactory.getSuperKey(path))
    }

    @Ignore
    fun `should return browser_dir when getBrowserKey if dir`() {
        assertEquals("browser_dir", SortRecordModeFactory.getBrowserKey("dir"))
    }

    @Test
    fun `should return browser_ when getBrowserKey if dir is null`() {
        assertNotEquals("browser_null", SortRecordModeFactory.getBrowserKey(null))
    }

    @Ignore
    fun `should return browser_label when getLabelKey`() {
        assertEquals("browser_label_sort", SortRecordModeFactory.getLabelKey())
    }

    @Ignore
    fun `should return browser_storageemulated0 when getSelectPathKey`() {
        assertEquals("browser_/storage/emulated/0", SortRecordModeFactory.getSelectPathKey())
    }

    @Test
    fun `should return recycle_bin when getRecycleBinKey`() {
        assertEquals("recycle_bin", SortRecordModeFactory.getRecycleBinKey())
    }

    @Test
    fun `should return update time when getDefaultValue if recordMode is start with category`() {
        assertEquals(
            SortHelper.FILE_TIME_REVERSE_ORDER,
            SortRecordModeFactory.getDefaultValue("category_sort_1")
        )
    }

    @Test
    fun `should return open time when getDefaultValue if recordMode is start with category`() {
        assertEquals(
            SortHelper.FILE_LAST_OPEN_TIME_ORDER,
            SortRecordModeFactory.getDefaultValue("category_sort_3")
        )
    }

    @Test
    fun `should return RECYCLE_BIN_DEFAULT_ORDER when getDefaultValue if recordMode RECYCLE_SORT_RECORD`() {
        assertEquals(
            SortHelper.RECYCLE_BIN_DEFAULT_ORDER,
            SortRecordModeFactory.getDefaultValue(SortModeUtils.RECYCLE_SORT_RECORD)
        )
    }

    @Test
    fun `should return FILE_NAME_ORDER when getDefaultValue if recordMode start with browser`() {
        assertEquals(
            SortHelper.FILE_NAME_ORDER,
            SortRecordModeFactory.getDefaultValue("browser")
        )
    }

    @Test
    fun should_return_FILE_LAST_OPEN_TIME_ORDER_when_getDefaultValue_recordMode_tencentDoc() {
        assertEquals(
            SortHelper.FILE_LAST_OPEN_TIME_ORDER,
            SortRecordModeFactory.getDefaultValue(SortRecordModeFactory.getCloudFileKey(CategoryHelper.CATEGORY_TENCENT_DOCS))
        )
    }

    @Test
    fun should_return_FILE_TIME_REVERSE_ORDER_when_getDefaultValue_recordMode_kingDoc() {
        assertEquals(
            SortHelper.FILE_TIME_REVERSE_ORDER,
            SortRecordModeFactory.getDefaultValue(SortRecordModeFactory.getCloudFileKey(CategoryHelper.CATEGORY_K_DOCS))
        )
    }
}