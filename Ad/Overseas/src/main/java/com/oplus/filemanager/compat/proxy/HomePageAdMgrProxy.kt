/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : HomePageAdMgrProxy
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/3/16 16:35
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/3/16       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat.proxy

import android.app.Activity
import android.view.View
import android.widget.RelativeLayout
import androidx.annotation.VisibleForTesting
import com.oplus.filemanager.compat.interfaces.IHomePageAdMgr

class HomePageAdMgrProxy(
    @get:VisibleForTesting val homePageAdMgr: IHomePageAdMgr
) : IHomePageAdMgr {

    override fun setContainer(rootView: View, rootViewId: Int) {
        homePageAdMgr.setContainer(rootView, rootViewId)
    }

    override fun requestMainAd(activity: Activity) {
        homePageAdMgr.requestMainAd(activity)
    }

    override fun getEntryView(name: String): RelativeLayout? {
        return homePageAdMgr.getEntryView(name)
    }

    override fun eventOnDestroy() {
        homePageAdMgr.eventOnDestroy()
    }
}