/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WebCategoryCompressLoader
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/26 15:00
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/26       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.loader

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import android.text.TextUtils
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.constants.CommonConstants.COMPRESS_EXT_ARRAY
import com.filemanager.common.constants.CommonConstants.TYPE_ALL
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MediaHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.BlacklistParser
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.fileservice.utils.WebFileConstant
import java.io.File

class WebCategoryCompressLoader : WebBaseUriLoader<MediaFileWrapper> {
    companion object {
        private const val TAG = "WebCategoryCompressLoader"
        private const val INDEX_ID = 0
        private const val INDEX_DATA = 1
        private const val INDEX_DISPLAY_NAME = 2
        private const val INDEX_SIZE = 3
        private const val INDEX_DATE_MODIFIED = 4
        private const val INDEX_DATE_MIME_TYPE = 5
        private const val INDEX_DATE_MEDIA_FORMAT = 6

        @JvmStatic
        private val MEDIA_PROJECT = arrayOf(
            MediaStore.Files.FileColumns._ID,
            MediaStore.Files.FileColumns.DATA,
            MediaStore.Files.FileColumns.DISPLAY_NAME,
            MediaStore.Files.FileColumns.SIZE,
            MediaStore.Files.FileColumns.DATE_MODIFIED,
            MediaStore.Files.FileColumns.MIME_TYPE,
            MediaHelper.COLUMN_MEDIA_FORMAT
        )
    }

    private var mCompressType: String
    private var mBaseQuerySelection: String = ""
    private val mSizeFilterState: Boolean
    private val mFilterSize: Int
    var mSortOrderType: Int = SortHelper.FILE_NAME_ORDER

    constructor(context: Context, compressType: String) : super(context) {
        mCompressType = compressType
        initData()
        initSelection()
        mFilterSize = BlacklistParser.getFilterSizeByType(CategoryHelper.CATEGORY_COMPRESS)
        mSizeFilterState = mFilterSize > 0
    }

    private fun initSelection() {
        val sql = StringBuilder()
        when (mCompressType) {
            TYPE_ALL -> {
                sql.append(
                    MediaStoreCompat.getMediaStoreSqlQuery(
                        CategoryHelper.CATEGORY_COMPRESS,
                        COMPRESS_EXT_ARRAY
                    )
                )
            }
            else -> {
                val selectionArgs = ArrayList<String?>()
                selectionArgs.add(mCompressType)
                sql.append(
                    MediaStoreCompat.getMediaStoreSqlQuery(
                        CategoryHelper.CATEGORY_COMPRESS,
                        selectionArgs
                    )
                )
            }
        }
        mBaseQuerySelection = sql.toString()
    }

    override fun getUri(): Uri? = MediaHelper.FILE_URI

    override fun getSelection(): String? = mBaseQuerySelection

    override fun getSelectionArgs(): Array<String>? = null

    override fun getObserverUri(): Array<Uri>? = null

    override fun getProjection(): Array<String>? {
        return MEDIA_PROJECT
    }

    override fun createFromCursor(cursor: Cursor, uri: Uri?): MediaFileWrapper? {
        val path = cursor.getString(INDEX_DATA)
        val displayName = cursor.getString(INDEX_DISPLAY_NAME)
        if (TextUtils.isEmpty(path) || TextUtils.isEmpty(displayName)) {
            return null
        }
        val file = File(path)
        //file interface is slowly on Android R,So need not to call exists on Android R
        if (!SdkUtils.isAtLeastR() && (!file.exists() || file.isDirectory)) {
            return null
        } else {
            val isDir = if (cursor.isNull(INDEX_DATE_MEDIA_FORMAT)) {
                Log.d(TAG, "createFromCursor: format is null")
                file.isDirectory
            } else {
                val format = cursor.getInt(INDEX_DATE_MEDIA_FORMAT)
                (format == MediaHelper.MEDIA_FORMAT_DIR)
            }
            if (isDir) {
                return null
            }
        }
        val id = cursor.getInt(INDEX_ID)
        val size = cursor.getLong(INDEX_SIZE)
        var dateModified = cursor.getLong(INDEX_DATE_MODIFIED) * WebFileConstant.ONE_THOUSAND
        val mimeType = cursor.getString(INDEX_DATE_MIME_TYPE)
        if (dateModified == 0L) {
            Log.d(TAG, "dateModified is 0")
            dateModified = FileTimeUtil.getFileTime(path) ?: 0
        }
        return MediaFileWrapper(
            id,
            path,
            displayName,
            mimeType,
            size,
            dateModified,
            MediaHelper.FILE_URI
        )
    }

    override fun getSortOrder(): String {
        return getOrderWithType(mSortOrderType)
    }

    override fun preHandleResultBackground(list: MutableList<MediaFileWrapper>): MutableList<MediaFileWrapper> {
        if ((mSortOrderType != SortHelper.FILE_TIME_REVERSE_ORDER) && (mSortOrderType != SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER)) {
            SortHelper.sortCategoryFiles(list, mSortOrderType, CategoryHelper.CATEGORY_COMPRESS, true)
        }
        return list
    }
}