<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    android:orientation="vertical"
    android:splitMotionEvents="false">

    <include layout="@layout/appbar_with_sort_layout_secondary" />

    <FrameLayout
        android:id="@+id/recent_parent_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <com.oplus.filemanager.recent.view.refresh.BounceLayout
            android:id="@+id/recent_bl"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.filemanager.common.view.FileManagerRecyclerView
                android:id="@+id/recent_recycle_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="@dimen/dimen_12dp" />

        </com.oplus.filemanager.recent.view.refresh.BounceLayout>
    </FrameLayout>

    <ViewStub
        android:id="@+id/main_recent_empty_ll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/fragment_recent_empty_layout" />

    <ViewStub
        android:id="@+id/common_permission_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/permission_common_view_layout" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>