package com.oplus.filemanager.drivebrowser.download.notification

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import org.robolectric.RuntimeEnvironment
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.drivebrowser.R
import com.oplus.filemanager.drivebrowser.download.DocumentDownloadDispatcher
import com.oplus.filemanager.drivebrowser.download.DownloadTaskInfo
import io.mockk.*
import org.apache.commons.io.FilenameUtils
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlin.test.assertNotNull

/**
 * DownloadNotificationManager的单元测试类
 * 用于测试下载通知管理器的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class DownloadNotificationManagerTest {

    private lateinit var context: Context
    private lateinit var notificationManager: DownloadNotificationManager
    private lateinit var mockNotificationManager: NotificationManager
    private lateinit var mockNotificationManagerCompat: NotificationManagerCompat

    /**
     * 测试前的初始化方法
     * 1. 创建模拟的Context和NotificationManager
     * 2. 设置MyApplication的模拟上下文
     * 3. 初始化NotificationManagerCompat的模拟
     * 4. 设置权限检查的模拟返回值
     */
    @Before
    fun setUp() {
        // 创建模拟的Context
        context = spyk(RuntimeEnvironment.application)
        // 创建模拟的NotificationManager
        mockNotificationManager = mockk(relaxed = true)
        mockNotificationManagerCompat = mockk(relaxed = true)
        
        // 模拟MyApplication.sAppContext
        mockkObject(MyApplication)
        every { MyApplication.sAppContext } returns context
        
        // 模拟NotificationManagerCompat.from方法
        mockkStatic(NotificationManagerCompat::class)
        every { NotificationManagerCompat.from(any()) } returns mockNotificationManagerCompat
        
        // 模拟权限检查
        mockkObject(PermissionUtils)
        every { PermissionUtils.hasNotificationPermission(any()) } returns true
        // 模拟获取系统服务
        every { context.getSystemService(Context.NOTIFICATION_SERVICE) } returns mockNotificationManager
        // 初始化待测试的DownloadNotificationManager
        notificationManager = DownloadNotificationManager(context)
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试showDownloadSuccessNotification方法
     * 当文件名为空时应该直接返回，不发送通知
     */
    @Test
    fun `showDownloadSuccessNotification with empty filename should return early`() {
        notificationManager.showDownloadSuccessNotification("", "/path/to/file")
        // 验证没有调用通知发送方法
        verify(exactly = 0) { mockNotificationManagerCompat.notify(any(), any()) }
    }

    /**
     * 测试showDownloadSuccessNotification方法
     * 当文件路径为空时应该直接返回，不发送通知
     */
    @Test
    fun `showDownloadSuccessNotification with empty filepath should return early`() {
        notificationManager.showDownloadSuccessNotification("file.txt", "")
        // 验证没有调用通知发送方法
        verify(exactly = 0) { mockNotificationManagerCompat.notify(any(), any()) }
    }

    /**
     * 测试showDownloadFailureNotification方法
     * 当文件路径列表为空时应该直接返回，不发送通知
     */
    @Test
    fun `showDownloadFailureNotification with empty filePaths should return early`() {
        notificationManager.showDownloadFailureNotification(emptyList(), arrayListOf())
        // 验证没有调用通知发送方法
        verify(exactly = 0) { mockNotificationManagerCompat.notify(any(), any()) }
    }

    /**
     * 测试createProgressNotification方法
     * 验证是否能正确创建进度通知
     */
    @Test
    fun `createProgressNotification should build correct notification`() {
        val fileName = "test.txt"
        val progress = 50
        val index = 1
        val totalCount = 3

        // 模拟创建取消下载的Intent
        mockkObject(DocumentDownloadDispatcher.Companion)
        every { DocumentDownloadDispatcher.createCancelDownloadIntent(any()) } returns Intent()

        // 模拟PendingIntent
        val pendingIntentMock = mockk<PendingIntent>()
        mockkStatic(PendingIntent::class)
        every { PendingIntent.getService(any(), 0, any(), any<Int>()) } returns pendingIntentMock

        // 创建通知并验证
        val notification = notificationManager.createProgressNotification(fileName, progress, index, totalCount)

        // 验证通知不为空
        assertNotNull(notification)
    }

    /**
     * 测试createNotificationChannel方法
     * 当通知渠道不存在时应该创建新渠道
     */
    @Test
    fun `createNotificationChannel should create channel when it does not exist`() {
        val channelId = "test_channel"
        // 模拟没有找到通知渠道
        every { mockNotificationManager.notificationChannels } returns listOf()

        notificationManager.createNotificationChannel(channelId = channelId)

        // 验证创建了新的通知渠道
        verify { mockNotificationManager.createNotificationChannel(any<NotificationChannel>()) }
    }

    /**
     * 测试createNotificationChannel方法
     * 当通知渠道已存在时不应该重复创建
     */
    @Test
    fun `createNotificationChannel should not create channel when it exists`() {
        val channelId = "test_channel"
        // 模拟已存在的通知渠道
        val channelMock = mockk<NotificationChannel>()
        every { channelMock.id } returns channelId
        every { mockNotificationManager.notificationChannels } returns listOf(channelMock)

        notificationManager.createNotificationChannel(channelId = channelId)

        // 验证没有创建新的通知渠道
        verify(exactly = 0) { mockNotificationManager.createNotificationChannel(any<NotificationChannel>()) }
    }
}