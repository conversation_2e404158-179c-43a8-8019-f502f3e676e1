package com.filemanager.common.base

import com.filemanager.common.utils.Log
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runBlockingTest
import kotlinx.coroutines.test.setMain
import kotlinx.coroutines.test.advanceUntilIdle
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import kotlin.coroutines.CoroutineContext

/**
 * BaseViewModel的单元测试类
 * 使用Robolectric和MockK框架进行测试
 * 测试BaseViewModel中的协程相关功能
 */
@ExperimentalCoroutinesApi  // 标记使用了实验性的协程API
@RunWith(RobolectricTestRunner::class)  // 使用Robolectric运行测试
@org.robolectric.annotation.Config(maxSdk = 33)  // 配置Robolectric运行环境
class BaseViewModelTest {
    private lateinit var viewModel: BaseViewModel  // 待测试的ViewModel实例
    private val testDispatcher = TestCoroutineDispatcher()  // 测试用的协程调度器

    /**
     * 在每个测试方法执行前的准备工作
     * 1. 设置主调度器为测试调度器
     * 2. 初始化ViewModel实例
     */
    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)  // 将主调度器替换为测试调度器
        viewModel = BaseViewModel()  // 初始化待测试的ViewModel
    }

    /**
     * 在每个测试方法执行后的清理工作
     * 1. 重置主调度器
     * 2. 清理测试协程
     * 3. 解除所有Mock
     */
    @After
    fun tearDown() {
        Dispatchers.resetMain()  // 恢复默认的主调度器
        testDispatcher.cleanupTestCoroutines()  // 清理测试协程
        unmockkAll()  // 解除所有Mock对象
    }

    /**
     * 测试launchOnUI方法是否正常执行代码块
     */
    @Test
    fun `test launchOnUI executes block`() = testDispatcher.runBlockingTest {
        var executed = false  // 用于验证代码块是否执行的标志
        viewModel.launch { executed = true }  // 执行待测试方法
        advanceUntilIdle()  // 等待所有协程执行完成
        assertEquals(true, executed)  // 验证代码块是否被执行
    }

    /**
     * 测试launchOnUITryCatch方法(仅tryBlock参数)是否正常执行代码块
     */
    @Test
    fun `test launchOnUITryCatch with tryBlock only executes successfully`() = testDispatcher.runBlockingTest {
        var executed = false  // 用于验证代码块是否执行的标志
        viewModel.launchOnUITryCatch({ executed = true })  // 执行待测试方法
        advanceUntilIdle()  // 等待所有协程执行完成
        assertEquals(true, executed)  // 验证代码块是否被执行
    }

    /**
     * 测试tryCatch方法是否正确处理CancellationException(当手动处理时)
     */
    @Test
    fun `test tryCatch handles CancellationException when manually handled`() = testDispatcher.runBlockingTest {
        val exception = CancellationException("test")  // 测试用的取消异常
        var catchExecuted = false  // 用于验证catch块是否执行的标志

        viewModel.launchOnUITryCatch(
            tryBlock = { throw exception },  // 抛出测试异常
            catchBlock = { e -> 
                assertEquals(exception, e)  // 验证捕获的异常是否正确
                catchExecuted = true  // 标记catch块已执行
            },
            finallyBlock = {},
            handleCancellationExceptionManually = true  // 设置为手动处理取消异常
        )
        advanceUntilIdle()  // 等待所有协程执行完成

        assertEquals(true, catchExecuted)  // 验证catch块是否被执行
    }
}