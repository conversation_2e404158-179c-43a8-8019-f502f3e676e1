package com.oplus.filemanager.preview.selection.loader

import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.net.Uri
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Log
import com.filemanager.thumbnail.ThumbnailManager
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.interfaze.remotedevice.IRemotePicListener
import com.oplus.filemanager.interfaze.remotedevice.IRemoteThumbnail
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.unmockkAll
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import java.io.IOException
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import java.lang.reflect.Field
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper as TypeChecker

/**
 * HeapUpImageLoader的单元测试类
 * 用于测试HeapUpImageLoader中各种文件类型的加载逻辑
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
@OptIn(ExperimentalCoroutinesApi::class)
class HeapUpImageLoaderTest {

    private lateinit var context: Context
    private lateinit var loader: HeapUpImageLoader
    private val testDispatcher = UnconfinedTestDispatcher()

    /**
     * 测试前的初始化方法
     * 1. 设置主线程调度器
     * 2. 初始化上下文和测试对象
     * 3. 模拟静态依赖项
     */
    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        context = RuntimeEnvironment.getApplication()
        loader = HeapUpImageLoader(context)

        // 模拟静态依赖
        mockkStatic(Log::class)
        mockkObject(ThumbnailManager)
        mockkObject(Injector)
        mockkStatic(Glide::class)
    }

    /**
     * 测试后的清理方法
     * 1. 重置主线程调度器
     * 2. 解除所有模拟
     */
    @After
    fun tearDown() {
        Dispatchers.resetMain()
        unmockkAll()
    }

    /**
     * 测试加载远程文件(已有URI)的情况
     * 验证:
     * 1. 是否正确使用Glide加载远程URI
     * 2. 返回的目标对象类型是否正确
     */
    @Test
    fun `test loadHeapUpImage for remote file with existing uri`() {
        // 准备测试数据
        val remoteFile = RemoteFileBean().apply {
            mData = "/remote/path"
            remotePicUri = mockk()
        }
        val glideMock = mockk<RequestManager>()
        val requestBuilderMock = mockk<RequestBuilder<Bitmap>>(relaxed = true)
        every { Glide.with(context) } returns glideMock
        every { glideMock.asBitmap() } returns requestBuilderMock
        every { requestBuilderMock.load(any<Uri>()) } returns requestBuilderMock
        every { requestBuilderMock.apply(any<RequestOptions>()) } returns requestBuilderMock
        every { requestBuilderMock.addListener(any<RequestListener<Bitmap>>()) } returns requestBuilderMock
        every { requestBuilderMock.into(any()) } returns mockk()

        // 模拟typeChecker避免厂商API调用
        mockTypeChecker(loader, true)  // true表示远程文件

        // 执行测试
        val target = loader.loadHeapUpImage(remoteFile)

        // 验证结果
        verify { requestBuilderMock.load(remoteFile.remotePicUri) }
        assertTrue(target is CommonHeapUpTarget)
    }

    /**
     * 测试加载远程文件(无URI)的情况
     * 验证:
     * 1. 是否正确调用远程缩略图获取接口
     * 2. 返回的目标对象类型是否正确
     */
    @Test
    fun `test loadHeapUpImage for remote file without uri`() {
        // 准备测试数据
        val remoteFile = RemoteFileBean().apply {
            mData = "/remote/path"
            originalPath = "/original/path"
        }
        val remoteThumbnailMock = mockk<IRemoteThumbnail>()
        val remoteDeviceMock = mockk<IRemoteDevice>()
        every { Injector.injectFactory<IRemoteThumbnail>() } returns remoteThumbnailMock
        every { Injector.injectFactory<IRemoteDevice>() } returns remoteDeviceMock
        every { remoteDeviceMock.getCurrentLinkedRemoteDiceInfo() } returns mockk {
            every { deviceId } returns "device123"
        }
        every { remoteThumbnailMock.getPicThumbnails(any(), any(), any(), any(), any(), any(), any()) } answers {
            val callback = args[6] as IRemotePicListener
            // 使用真实Uri对象避免buildUpon调用失败
            val mockUri = Uri.parse("content://test")
            callback.onResult(1000, mockUri)
        }

        // 模拟typeChecker避免厂商API调用
        mockTypeChecker(loader, true)  // true表示远程文件

        // 执行测试
        val target = loader.loadHeapUpImage(remoteFile)

        // 验证结果
        verify { remoteThumbnailMock.getPicThumbnails(any(), any(), any(), any(), any(), any(), any()) }
        assertTrue(target is CommonHeapUpTarget)
    }

    /**
     * 测试加载图片文件(URI为空)的情况
     * 验证:
     * 1. 是否正确记录错误日志
     * 2. 返回的目标对象类型是否正确
     */
    @Test
    fun `test loadAsImageFile with null uri`() {
        // 准备测试数据
        val fileBean = BaseFileBean().apply { mData = null }
        loader = HeapUpImageLoader(context)

        // 模拟typeChecker并设置图片类型
        mockTypeChecker(loader, isImage = true)

        // 执行测试
        val target = loader.loadHeapUpImage(fileBean)

        // 验证结果
        verify { Log.e("HeapUpImageLoader", "loadAsImageFile: ERROR! can not get image uri") }
        assertTrue(target is CommonHeapUpTarget)
    }

    /**
     * 测试加载音频文件(路径为空)的情况
     * 验证:
     * 1. 是否正确记录错误日志
     * 2. 返回的目标对象类型是否正确
     */
    @Test
    fun `test loadAsAudioFile with empty path`() {
        // 准备测试数据
        val fileBean = BaseFileBean().apply { mData = "" }
        loader = HeapUpImageLoader(context)

        // 模拟typeChecker并设置音频类型
        mockTypeChecker(loader, isAudio = true)

        // 执行测试
        val target = loader.loadHeapUpImage(fileBean)

        // 验证结果
        verify { Log.e("HeapUpImageLoader", "loadAsAudioFile: ERROR! No audio file path") }
        assertTrue(target is AudioHeapUpTarget)
    }

    /**
     * 测试加载APK文件(路径为空)的情况
     * 验证:
     * 1. 是否正确记录错误日志
     * 2. 返回的目标对象类型是否正确
     */
    @Test
    fun `test loadAsApkFile with empty path`() {
        // 准备测试数据
        val fileBean = BaseFileBean().apply { mData = "" }
        loader = HeapUpImageLoader(context)

        // 模拟typeChecker并设置APK类型
        mockTypeChecker(loader, isApk = true)

        // 执行测试
        val target = loader.loadHeapUpImage(fileBean)

        // 验证结果
        verify { Log.e("HeapUpImageLoader", "loadAsApkFile: ERROR! No audio file path") }
        assertTrue(target is CommonHeapUpTarget)
    }

    /**
     * 辅助函数: 通过反射模拟typeChecker字段
     * @param loader 要注入模拟对象的HeapUpImageLoader实例
     * @param isRemote 是否模拟远程文件类型
     * @param isImage 是否模拟图片文件类型
     * @param isVideo 是否模拟视频文件类型
     * @param isAudio 是否模拟音频文件类型
     * @param isApk 是否模拟APK文件类型
     * @param isDoc 是否模拟文档文件类型
     */
    private fun mockTypeChecker(
        loader: HeapUpImageLoader,
        isRemote: Boolean = false,
        isImage: Boolean = false,
        isVideo: Boolean = false,
        isAudio: Boolean = false,
        isApk: Boolean = false,
        isDoc: Boolean = false
    ) {
        try {
            val mockTypeChecker = mockk<TypeChecker>()
            every { mockTypeChecker.getMediaType(any()) } returns MimeTypeHelper.UNKNOWN_TYPE
            every { mockTypeChecker.isRemoteType(any(), any()) } returns isRemote
            every { mockTypeChecker.isImageFileType(any(), any()) } returns isImage
            every { mockTypeChecker.isVideoFileType(any(), any()) } returns isVideo
            every { mockTypeChecker.isAudioFileType(any(), any()) } returns isAudio
            every { mockTypeChecker.isApkFileType(any(), any()) } returns isApk
            every { mockTypeChecker.isDocType(any(), any()) } returns isDoc
            
            val field: Field = loader::class.java.getDeclaredField("typeChecker")
            field.isAccessible = true
            field.set(loader, mockTypeChecker)
        } catch (e: Exception) {
            throw RuntimeException("Failed to mock typeChecker", e)
        }
    }
}