package com.oplus.filemanager.shortcutfolder

import android.app.Activity
import android.content.Intent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.fragment.app.Fragment
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewListFragmentCreator
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import io.mockk.*
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import com.oplus.filemanager.shortcutfolder.operate.ShortcutFolderOperator
import com.oplus.filemanager.shortcutfolder.ui.ShortcutFolderActivity
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue
import org.robolectric.Shadows
import io.mockk.MockKAnnotations

/**
 * ShortcutFolderApi的单元测试类
 * 使用Robolectric和MockK框架进行测试
 */
@ExperimentalCoroutinesApi
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class ShortcutFolderApiTest {

    // 使用MockK框架创建模拟对象
    @MockK
    private lateinit var mockPreviewListFragment: IPreviewListFragment

    @MockK
    private lateinit var mockActivity: Activity

    @MockK
    private lateinit var mockFragment: Fragment

    @MockK
    private lateinit var mockPreviewCombineFragment: PreviewCombineFragment

    @MockK
    private lateinit var mockMenuItem: MenuItem

    @MockK
    private lateinit var mockMenu: Menu

    @MockK
    private lateinit var mockMenuInflater: MenuInflater

    @MockK
    private lateinit var mockBaseFileBeanList: List<BaseFileBean>

    private lateinit var api: ShortcutFolderApi
    private val testDispatcher = StandardTestDispatcher()

    /**
     * 测试前的初始化方法
     * 1. 设置主调度器为测试调度器
     * 2. 初始化所有MockK注解的模拟对象
     * 3. 模拟ShortcutFolderOperator对象
     * 4. 创建待测试的ShortcutFolderApi实例
     */
    @Before
    fun setUp() {
        Dispatchers.setMain(testDispatcher)
        MockKAnnotations.init(this)  // 初始化所有@MockK属性
        mockkObject(ShortcutFolderOperator)
        api = ShortcutFolderApi()
    }

    /**
     * 测试后的清理方法
     * 1. 重置主调度器
     * 2. 取消所有模拟对象
     * 3. 清除所有模拟调用记录
     */
    @After
    fun tearDown() {
        Dispatchers.resetMain()
        unmockkAll()
        clearAllMocks()
    }

    /**
     * 测试loadFolders方法
     * 验证是否调用了ShortcutFolderOperator.queryFolders()
     */
    @Test
    fun `loadFolders should call queryFolders`() = runTest {
        val expected = mutableListOf<MainCategoryItemsBean>()
        coEvery { ShortcutFolderOperator.queryFolders() } returns expected

        val result = api.loadFolders()

        assertEquals(expected, result)
        coVerify { ShortcutFolderOperator.queryFolders() }
    }

    /**
     * 测试addShortcutFolders方法处理null路径的情况
     */
    @Test
    fun `addShortcutFolders should handle null paths`() = runTest {
        val expected = listOf(1L, 2L)
        coEvery { ShortcutFolderOperator.addShortcutFolders(null) } returns expected

        val result = api.addShortcutFolders(null)

        assertEquals(expected, result)
        coVerify { ShortcutFolderOperator.addShortcutFolders(null) }
    }

    /**
     * 测试addShortcutFolders方法处理空路径列表的情况
     */
    @Test
    fun `addShortcutFolders should handle empty paths`() = runTest {
        val expected = emptyList<Long>()
        coEvery { ShortcutFolderOperator.addShortcutFolders(emptyList()) } returns expected

        val result = api.addShortcutFolders(emptyList())

        assertEquals(expected, result)
        coVerify { ShortcutFolderOperator.addShortcutFolders(emptyList()) }
    }

    /**
     * 测试isAddShortcutFolder方法
     * 验证是否调用了ShortcutFolderOperator.isAddShortcutFolder()
     */
    @Test
    fun `isAddShortcutFolder should call operator`() {
        every { ShortcutFolderOperator.isAddShortcutFolder(any()) } just Runs

        api.isAddShortcutFolder(mockBaseFileBeanList)

        verify { ShortcutFolderOperator.isAddShortcutFolder(mockBaseFileBeanList) }
    }

    /**
     * 测试startShortcutFolderActivity方法
     * 验证是否启动了正确的Activity并传递了正确的参数
     */
    @Test
    fun `startShortcutFolderActivity should launch correct intent`() {
        val activity = Robolectric.buildActivity(Activity::class.java).get()
        val dbId = 123L
        val title = "Test"
        val path = "/test/path"
        val sideType = 1

        api.startShortcutFolderActivity(activity, dbId, title, path, sideType)

        val intent = Shadows.shadowOf(activity).nextStartedActivity
        assertEquals(ShortcutFolderActivity::class.java.name, intent.component?.className)
        assertEquals(title, intent.getStringExtra(KtConstants.P_TITLE))
        assertEquals(path, intent.getStringExtra(KtConstants.FILE_PATH))
        assertEquals(dbId, intent.getLongExtra(Constants.DB_ID, -1))
        assertEquals(sideType, intent.getIntExtra(Constants.SIDE_CATEGORY_TYPE, -1))
    }

    /**
     * 测试getCurrentPath方法(Activity参数)
     * 验证当传入正确的Activity类型时返回正确的路径
     */
    @Test
    fun `getCurrentPath with activity should return path when correct type`() {
        val activity = mockk<ShortcutFolderActivity>(relaxed = true)
        val expectedPath = "/activity/path"
        every { activity.getCurrentPath() } returns expectedPath

        val result = api.getCurrentPath(activity)

        assertEquals(expectedPath, result)
    }

    /**
     * 测试getCurrentPath方法
     * 验证当传入错误类型时返回null
     */
    @Test
    fun `getCurrentPath should return null for wrong types`() {
        assertNull(api.getCurrentPath(mockPreviewListFragment))
        assertNull(api.getCurrentPath(mockActivity))
    }

    /**
     * 测试deleteShortcutFolder方法
     * 验证是否调用了ShortcutFolderOperator.deleteShortcutFolder()并返回正确结果
     */
    @Test
    fun `deleteShortcutFolder should return operator result`() = runTest {
        coEvery { ShortcutFolderOperator.deleteShortcutFolder(any()) } returns true

        assertTrue(api.deleteShortcutFolder(1L))
        coVerify { ShortcutFolderOperator.deleteShortcutFolder(1L) }
    }

    /**
     * 测试getFragment方法
     * 验证返回的Fragment是否为PreviewCombineFragment类型
     */
    @Test
    fun `getFragment should return PreviewCombineFragment with creator`() {
        val fragment = api.getFragment(mockActivity) as PreviewCombineFragment

        assertTrue(fragment is PreviewCombineFragment)
    }

    /**
     * 测试onResumeLoadData方法
     * 验证当传入正确的Fragment类型时是否调用了onResumeLoadData()
     */
    @Test
    fun `onResumeLoadData should call when correct fragment type`() {
        every { mockPreviewCombineFragment.onResumeLoadData() } just Runs

        api.onResumeLoadData(mockPreviewCombineFragment)

        verify { mockPreviewCombineFragment.onResumeLoadData() }
    }

    /**
     * 测试onMenuItemSelected方法
     * 验证当传入正确的Fragment类型时是否调用了onMenuItemSelected()并返回正确结果
     */
    @Test
    fun `onMenuItemSelected should return operator result`() {
        every { mockPreviewCombineFragment.onMenuItemSelected(any()) } returns true

        val result = api.onMenuItemSelected(mockPreviewCombineFragment, mockMenuItem)

        assertTrue(result)
        verify { mockPreviewCombineFragment.onMenuItemSelected(mockMenuItem) }
    }

    /**
     * 测试onMenuItemSelected方法
     * 验证当传入错误的Fragment类型时返回false
     */
    @Test
    fun `onMenuItemSelected should return false for wrong fragment`() {
        val result = api.onMenuItemSelected(mockFragment, mockMenuItem)

        assertFalse(result)
    }

    /**
     * 测试setCurrentFilePath方法
     * 验证当路径不为null且Fragment类型正确时是否调用了setCurrentFromOtherSide()
     */
    @Test
    fun `setCurrentFilePath should call when path not null and correct fragment`() {
        val path = "/valid/path"
        every { mockPreviewCombineFragment.setCurrentFromOtherSide(any()) } just Runs

        api.setCurrentFilePath(mockPreviewCombineFragment, path)

        verify { mockPreviewCombineFragment.setCurrentFromOtherSide(path) }
    }

    /**
     * 测试setCurrentFilePath方法
     * 验证当路径为null时不调用setCurrentFromOtherSide()
     */
    @Test
    fun `setCurrentFilePath should not call when path is null`() {
        api.setCurrentFilePath(mockPreviewCombineFragment, null)

        verify(exactly = 0) { mockPreviewCombineFragment.setCurrentFromOtherSide(any()) }
    }

    /**
     * 测试onSideNavigationClicked方法
     * 验证当传入正确的Fragment类型时是否调用了onSideNavigationClicked()并返回正确结果
     */
    @Test
    fun `onSideNavigationClicked should return operator result`() {
        every { mockPreviewCombineFragment.onSideNavigationClicked(any()) } returns true

        val result = api.onSideNavigationClicked(mockPreviewCombineFragment, true)

        assertTrue(result)
        verify { mockPreviewCombineFragment.onSideNavigationClicked(true) }
    }

    /**
     * 测试getCurrentPath方法(Fragment参数)
     * 验证当传入PreviewCombineFragment类型时返回正确的路径
     */
    @Test
    fun `getCurrentPath with fragment should return path for PreviewCombineFragment`() {
        val expectedPath = "/combine/path"
        every { mockPreviewCombineFragment.getCurrentPath() } returns expectedPath

        val result = api.getCurrentPath(mockPreviewCombineFragment)

        assertEquals(expectedPath, result)
    }

    /**
     * 测试getCurrentPath方法(Fragment参数)
     * 验证当传入错误的Fragment类型时返回空字符串
     */
    @Test
    fun `getCurrentPath should return empty string for wrong fragment type`() {
        val result = api.getCurrentPath(mockFragment)

        assertEquals("", result)
    }

    /**
     * 测试updateFilePath方法
     * 验证是否调用了ShortcutFolderOperator.updateFilePath()
     */
    @Test
    fun `updateFilePath should call operator`() {
        every { ShortcutFolderOperator.updateFilePath(any(), any()) } just Runs

        api.updateFilePath("old", "new")

        verify { ShortcutFolderOperator.updateFilePath("old", "new") }
    }

    /**
     * 测试deleteFiles方法
     * 验证是否调用了ShortcutFolderOperator.deleteFiles()
     */
    @Test
    fun `deleteFiles should call operator`() {
        every { ShortcutFolderOperator.deleteFiles(any()) } just Runs

        api.deleteFiles(listOf("/path1", "/path2"))

        verify { ShortcutFolderOperator.deleteFiles(listOf("/path1", "/path2")) }
    }
}