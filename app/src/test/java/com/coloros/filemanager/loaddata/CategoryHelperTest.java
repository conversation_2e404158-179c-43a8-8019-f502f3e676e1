/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: CategoryHelperTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/9/18
 ** Author: <PERSON><PERSON><PERSON><PERSON>
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/9/18      1.0     create
 ****************************************************************/
package com.coloros.filemanager.loaddata;

import android.database.Cursor;
import android.os.Build;
import android.os.Handler;
import android.provider.MediaStore;
import android.util.SparseArray;

import com.coloros.filemanager.BaseTest;
import com.filemanager.common.constants.Constants;
import com.filemanager.common.helper.CategoryHelper;
import com.filemanager.common.helper.MediaHelper;
import com.filemanager.common.helper.VolumeEnvironment;
import com.filemanager.common.utils.PreferencesUtils;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.annotation.Config;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import shadow.ShadowSQLiteDatabase;
import shadow.ShadowVolumeEnvironment;
import utils.ReflectionUtils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@Ignore("stupid test")
@RunWith(RobolectricTestRunner.class)
@Config(sdk = Build.VERSION_CODES.S)
public class CategoryHelperTest extends BaseTest {
    private CategoryHelper mCategoryHelper;
    private String mCategoryItemText[];
    private String mItemString;
    private String mExternalPath;
    private String mInternalPath;

    @Before
    public void setUp() throws Exception {
        Handler handler = new Handler();
        mContext = RuntimeEnvironment.application;
        mCategoryHelper = new CategoryHelper(handler, mContext);

    }

    @After
    public void tearDown() throws Exception {
    }


    @Test
    public void should_verify_title_when_getTitle_with_index() {
        String expectItemText[] = mContext.getResources().getStringArray(com.filemanager.common.R.array.category_activity_title_new);
        assertEquals("", mCategoryHelper.getTitle(-1));
        assertEquals(expectItemText[0], mCategoryHelper.getTitle(0));
        assertEquals(expectItemText[1], mCategoryHelper.getTitle(1));
        assertEquals(expectItemText[2], mCategoryHelper.getTitle(2));
        assertEquals(expectItemText[3], mCategoryHelper.getTitle(3));
        assertEquals(expectItemText[4], mCategoryHelper.getTitle(4));
        assertEquals(expectItemText[5], mCategoryHelper.getTitle(5));
        assertEquals(expectItemText[6], mCategoryHelper.getTitle(6));
        assertEquals(expectItemText[7], mCategoryHelper.getTitle(7));
        assertEquals(expectItemText[8], mCategoryHelper.getTitle(8));
        assertEquals(expectItemText[9], mCategoryHelper.getTitle(9));
        assertEquals(expectItemText[10], mCategoryHelper.getTitle(10));
        assertEquals(expectItemText[11], mCategoryHelper.getTitle(11));
        assertEquals("", mCategoryHelper.getTitle(222));
        mCategoryHelper = new CategoryHelper(null, null);
    }

    @Test
    public void should_verify_when_getCountSqlWithRootPath_with_getCountSql_isEmpty_and_isExternalPath_isTrue()
            throws NoSuchFieldException, IllegalAccessException {
        StringBuilder sql = new StringBuilder("");
        mExternalPath = (String) ReflectionUtils.getPrivateField(mCategoryHelper, "mExternalPath");
        sql.append(" _data LIKE '");
        sql.append(mExternalPath);
        sql.append("%'");
        assertEquals(sql.toString(), mCategoryHelper.getCountSqlWithRootPath(CategoryHelper.CATEGORY_IMAGE, true));
    }

    @Test
    public void should_verify_when_getCountSqlWithRootPath_with_getCountSql_isEmpty_and_isExternalPath_isFalse()
            throws NoSuchFieldException, IllegalAccessException {
        StringBuilder sql = new StringBuilder("");
        mInternalPath = (String) ReflectionUtils.getPrivateField(mCategoryHelper, "mInternalPath");
        sql.append(" _data LIKE '");
        sql.append(mInternalPath);
        sql.append("%'");
        assertEquals(sql.toString(), mCategoryHelper.getCountSqlWithRootPath(CategoryHelper.CATEGORY_IMAGE, false));
    }

    @Test
    public void should_verify_when_getCountSqlWithRootPath_with_getCountSql_notEmpty_and_isExternalPath_isTrue()
            throws NoSuchFieldException, IllegalAccessException {
        mExternalPath = (String) ReflectionUtils.getPrivateField(mCategoryHelper, "mExternalPath");
        StringBuilder sql = new StringBuilder("");
        sql.append(mCategoryHelper.getCountSql(CategoryHelper.CATEGORY_APK));
        sql.append(" AND ");
        sql.append(" _data LIKE '");
        sql.append(mExternalPath);
        sql.append("%'");
        assertEquals(sql.toString(), mCategoryHelper.getCountSqlWithRootPath(CategoryHelper.CATEGORY_APK, true));
    }

    @Test
    public void should_verify_when_getCountSqlWithRootPath_with_getCountSql_notEmpty_and_isExternalPath_isFalse()
            throws NoSuchFieldException, IllegalAccessException {
        mInternalPath = (String) ReflectionUtils.getPrivateField(mCategoryHelper, "mInternalPath");
        StringBuilder sql = new StringBuilder("");
        sql.append(mCategoryHelper.getCountSql(CategoryHelper.CATEGORY_APK));
        sql.append(" AND ");
        sql.append(" _data LIKE '");
        sql.append(mInternalPath);
        sql.append("%'");
        assertEquals(sql.toString(), mCategoryHelper.getCountSqlWithRootPath(CategoryHelper.CATEGORY_APK, false));
    }

    @Ignore
    @Test
    @Config(shadows = {ShadowSQLiteDatabase.class, ShadowVolumeEnvironment.class})
    public void should_return_ignoredPaths_when_setIgnoredPath_with_context() throws IOException {
        Cursor cursor = mock(Cursor.class);
        when(cursor.moveToFirst()).thenReturn(true);
        ShadowSQLiteDatabase.setsCursor(cursor);
        ShadowVolumeEnvironment.setmDataDirPath("test.txt");
        String dbName = mContext.getResources().getString(com.filemanager.common.R.string.resourceFile);
        String dbPath = VolumeEnvironment.getDataDirPath(mContext, "databases", dbName);
        File file = new File(dbPath);
        if (!file.exists()) {
            file.createNewFile();
        }
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(10);
        fos.flush();
        fos.close();
        SparseArray<String> ignoredPaths = new SparseArray<String>();
        do {
            int i = 0;
            String ignoredPath = cursor.getString(0);
            ignoredPaths.put(i, ignoredPath);
            i++;
        } while (cursor.moveToNext());
        SparseArray<String> actulSparseArray = mCategoryHelper.setIgnoredPath(mContext, CategoryHelper.CATEGORY_DOC);
        verify(cursor).close();
        assertEquals(ignoredPaths.toString(), actulSparseArray.toString());

        if (file.exists()) {
            file.delete();
        }
    }

    @Ignore
    @Test
    @Config(shadows = {ShadowSQLiteDatabase.class, ShadowVolumeEnvironment.class})
    public void should_return_ignoredPaths_when_getIgnoredPath_with_context() throws IOException {
        Cursor cursor = mock(Cursor.class);
        when(cursor.moveToFirst()).thenReturn(true);
        ShadowSQLiteDatabase.setsCursor(cursor);
        ShadowVolumeEnvironment.setmDataDirPath("test.txt");
        String dbName = mContext.getResources().getString(com.filemanager.common.R.string.resourceFile);
        String dbPath = VolumeEnvironment.getDataDirPath(mContext, "databases", dbName);
        File file = new File(dbPath);
        if (!file.exists()) {
            file.createNewFile();
        }
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(10);
        fos.flush();
        fos.close();
        List<String> ignoredPaths = new ArrayList<>();
        do {
            String ignoredPath = cursor.getString(0);
            ignoredPaths.add(ignoredPath);
        } while (cursor.moveToNext());
        List<String> actulList = mCategoryHelper.getIgnoredPath(mContext);
        verify(cursor).close();
        assertEquals(ignoredPaths.toString(), actulList.toString());

        if (file.exists()) {
            file.delete();
        }
    }

    @Test
    public void should_verify_return_when_isIgnoredPathWithType_with_path_and_internalPath() {
        SparseArray<String> ignoredPaths = mock(SparseArray.class);
        when(ignoredPaths.size()).thenReturn(1);
        when(ignoredPaths.get(anyInt())).thenReturn("");
        assertTrue(mCategoryHelper.isIgnoredPathWithType("", "", ignoredPaths));
        assertFalse(mCategoryHelper.isIgnoredPathWithType("D:/", "C", ignoredPaths));
    }

    @Ignore
    @Test
    public void should_verify_selection_when_getAudioFilterSelection_with_Context_notNull() {
        String internalPath = "";
        String externalPath = "";
        int sizeFilter = mCategoryHelper.getVideoFilterSizeValue(mContext);
        int durationFilter = mCategoryHelper.getVideoFilterDurationValue(mContext);
        String recordSelection = mCategoryHelper.getAudioRecordSelection(internalPath, externalPath);
        String selection = MediaStore.Audio.Media.DURATION + ">=" + durationFilter + " OR "
                + MediaStore.Audio.Media.SIZE + ">=" + sizeFilter + " OR " + recordSelection;
        assertEquals(selection, mCategoryHelper.getAudioFilterSelection(mContext, internalPath, externalPath));
    }

    @Test
    public void should_verify_selection_when_getAudioRecordSelection_with_externalPath_notEmpty() {
        String internalPath = "";
        String externalPath = "test";
        String internalRecord = internalPath + File.separator + CategoryHelper.RECORDINGS + File.separator;
        String selection = MediaStore.Audio.Media.DATA + " LIKE '%" + internalRecord + "%'";
        String externalRecord = externalPath + File.separator + CategoryHelper.RECORDINGS + File.separator;
        assertTrue(mCategoryHelper.getAudioRecordSelection(internalPath, externalPath).contains(selection));
        assertTrue(mCategoryHelper.getAudioRecordSelection(internalPath, externalPath).contains(externalRecord));
    }

    @Test
    public void should_verity_saveCategoryCountData() {
        int[] categories = {CategoryHelper.CATEGORY_IMAGE, CategoryHelper.CATEGORY_AUDIO, CategoryHelper.CATEGORY_VIDEO,
                CategoryHelper.CATEGORY_DOC, CategoryHelper.CATEGORY_DOWNLOAD, CategoryHelper.CATEGORY_APK,
                CategoryHelper.CATEGORY_COMPRESS, CategoryHelper.CATEGORY_QQ, CategoryHelper.CATEGORY_MICROMSG,
                CategoryHelper.ITEM_APP_ITEM_7, CategoryHelper.ITEM_APP_ITEM_8, CategoryHelper.ITEM_APP_ITEM_9};
        for (int category : categories) {
            CategoryHelper.saveCategoryCountData(category, 0, 0);
            Assert.assertNotNull(PreferencesUtils.getLong(Constants.FILEMANAGER_RECORD, "", 0));
        }
    }

    @Test
    public void should_return_uri_when_get_uri_with_category() {
        Assert.assertEquals(MediaHelper.IMAGES_MEDIA_URI, mCategoryHelper.getUri(CategoryHelper.CATEGORY_IMAGE));
        Assert.assertEquals(MediaHelper.AUDIO_MEDIA_URI, mCategoryHelper.getUri(CategoryHelper.CATEGORY_AUDIO));
        Assert.assertEquals(MediaHelper.VIDEO_MEDIA_URI, mCategoryHelper.getUri(CategoryHelper.CATEGORY_VIDEO));
        Assert.assertEquals(MediaHelper.FILE_URI, mCategoryHelper.getUri(CategoryHelper.CATEGORY_DOC));
    }
}