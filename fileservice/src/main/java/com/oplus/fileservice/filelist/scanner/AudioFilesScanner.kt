/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AudioFilesScanner
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/23 16:01
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/23       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.scanner

import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.base.FileLoader
import com.filemanager.common.base.loader.UriLoadResult
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.AudioFileWrapper
import com.oplus.filemanager.category.audiovideo.ui.CategoryAudioLoader
import com.oplus.fileservice.bean.WebFileBean
import com.oplus.fileservice.bean.WebScannerResult
import com.oplus.fileservice.fileServiceScope
import com.oplus.fileservice.filelist.FileSelectPathService
import com.oplus.fileservice.filelist.utils.getDateAndTimeByDefaultFormat
import com.oplus.fileservice.utils.WebFileConstant
import com.oplus.fileservice.utils.WebMimeTypeHelper
import com.oplus.fileservice.utils.WebThumbCacheManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.apache.commons.io.FilenameUtils

class AudioFilesScanner(pageNo: Int, pageSize: Int, sortOrder: Int, categoryType: Int, private var isNeedThumbnail: Boolean = true) :
    BaseUriScanner() {
    companion object {
        const val TAG = "AudioFilesScanner"
    }

    private var mPageNo = pageNo
    private var mPageSize = pageSize
    private var mTotal = 1
    private var mSortOrder = sortOrder
    private var mCategoryType = categoryType
    private val mLoaderController = LoaderController()

    override fun scannerFiles(): WebScannerResult? {
        mLoaderController.initLoader(TAG.hashCode(), ServiceAudioCallBack())
        return null
    }

    inner class ServiceAudioCallBack :
        OnLoaderListener<UriLoadResult<Int, AudioFileWrapper>> {
        override fun onCreateLoader(): FileLoader<UriLoadResult<Int, AudioFileWrapper>>? {
            Log.d(TAG, "mCategoryType =$mCategoryType")
            val categoryAudioLoader = CategoryAudioLoader(
                MyApplication.sAppContext,
                UriHelper.geCategoryUri(mCategoryType),
                null,
                mCategoryType,
                true
            )
            categoryAudioLoader.setSort(mSortOrder)
            return categoryAudioLoader
        }

        override fun onLoadComplete(result: UriLoadResult<Int, AudioFileWrapper>?) {
            val pageAudioFiles = pageData(result?.mResultList)
            val webFileBeans = convertAudioFileToWebFileBean(pageAudioFiles)
            fileServiceScope.launch(Dispatchers.IO) {
                if (isNeedThumbnail && (mCategoryType == CategoryHelper.CATEGORY_VIDEO)) {
                    WebThumbCacheManager.setThumbnailData(webFileBeans)
                }
                Log.d(TAG, "mScannerCallback , mPageNo =$mPageNo, mTotal =$mTotal")
                mScannerCallback?.scannerSuc(WebScannerResult(mPageNo, mTotal, webFileBeans))
            }
        }
    }

    @VisibleForTesting
    fun pageData(result: List<AudioFileWrapper>?): List<AudioFileWrapper>? {
        mTotal = result?.size ?: 0
        if (mPageSize < 1) {
            mPageSize = WebFileConstant.DEFAULT_PAGE_SIZE
            Log.d(TAG, "Service modify mPageSize $mPageSize")
        }
        var pageTotal = mTotal / mPageSize
        if (mTotal % mPageSize > 0) {
            pageTotal++
        }

        if (mPageNo > pageTotal) {
            Log.d(FileSelectPathService.TAG, "mPageNo > pageTotal is true, mPageNo = $mPageNo")
            mPageNo = pageTotal
        }
        if (mPageNo < 1) {
            mPageNo = 1
        }
        val start = mPageSize * (mPageNo - 1)
        val end = if (mPageSize * mPageNo > mTotal) {
            mTotal
        } else {
            mPageSize * mPageNo
        }

        return result?.subList(start, end)
    }

    @VisibleForTesting
    fun convertAudioFileToWebFileBean(resultFileList: List<AudioFileWrapper>?): ArrayList<WebFileBean> {
        val resultWebFileBean = ArrayList<WebFileBean>()
        if (resultFileList != null) {
            for (mediaFileWrapper in resultFileList) {
                mediaFileWrapper.apply {
                    val webFileBean = WebFileBean()
                    webFileBean.mFileName = FilenameUtils.getBaseName(mDisplayName)
                    webFileBean.mFilePath = mData
                    webFileBean.mFileId = mId
                    webFileBean.mFileSize = mSize
                    webFileBean.mFileFormat = FilenameUtils.getExtension(mDisplayName)
                    webFileBean.mFileType =
                        WebMimeTypeHelper.getFileTypeFromExtension(webFileBean.mFileFormat)
                    webFileBean.mLastModifyDate = getDateAndTimeByDefaultFormat(mDateModified)
                    webFileBean.mDateModified = mDateModified
                    webFileBean.mLocalType = mLocalType
                    resultWebFileBean.add(webFileBean)
                }
            }
        }
        return resultWebFileBean
    }
}