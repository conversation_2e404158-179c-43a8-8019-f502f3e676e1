/*
 * ********************************************************************
 *  * * Copyright (C), 2021 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  RecycleBinUtilsTest.java
 *  * * Description : RecycleBinUtilsTest.java
 *  * * Version     : 1.0
 *  * * Date        : 21-12-14 下午5:34
 *  * * Author      : W9010863
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.coloros.filemanager.utils

import android.graphics.BitmapFactory
import com.filemanager.common.utils.DecodeUtils
import io.mockk.every
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Test

class DecodeUtilsTest {

    @Test
    fun should_verify_decode() {
        Assert.assertNull(DecodeUtils.decode(null, null))
        Assert.assertNull(DecodeUtils.decode("", null))
        Assert.assertNull(DecodeUtils.decode("", BitmapFactory.Options()))

        mockkStatic(BitmapFactory::class)
        try {
            every { BitmapFactory.decodeFile("test") }.throws(Exception("test-error"))
            Assert.assertNull(DecodeUtils.decode("test", null))
        } catch (e: Exception) {
            println("test!!!")
        }
    }
}