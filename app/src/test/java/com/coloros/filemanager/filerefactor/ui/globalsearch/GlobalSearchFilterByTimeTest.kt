/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.GlobalSearchFilterByTimeTest
 * * Version     : 1.0
 * * Date        : 2020/7/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.filerefactor.ui.globalsearch

import com.coloros.filemanager.BaseTest
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterCondition
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterDataHelperFactory
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import java.util.*

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class GlobalSearchFilterByTimeTest : BaseTest() {
    private val mDayMill = 24 * 60 * 60 * 1000L

    @Before
    override fun setUp() {
        mContext = RuntimeEnvironment.application
    }

    @After
    override fun tearDown() {
        super.tearDown()
    }

    @Test
    fun should_verify_filter_result_by_time_today() {
        val filterItem = FilterItem(FilterConstants.FILTER_TIME_TODAY,
                FilterCondition(FilterConstants.FILTER_TIME, ""), "")
        val bean1 = BaseFileBean()
        bean1.mDateModified = System.currentTimeMillis()
        val bean2 = BaseFileBean()
        bean2.mDateModified = System.currentTimeMillis() - mDayMill
        val bean3 = BaseFileBean()
        bean3.mDateModified = getTomorrowZero()
        val bean4 = BaseFileBean()
        bean4.mDateModified = getSpecialPreDayZeroTime(1)
        val bean5 = BaseFileBean()
        bean5.mDateModified = getSpecialPreDayLastTime(1)
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
            add(bean4)
            add(bean5)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(2, resultList!!.size)
        Assert.assertTrue(resultList!!.contains(bean1))
        Assert.assertTrue(resultList!!.contains(bean4))
    }

    @Test
    fun should_verify_filter_result_by_time_3_days() {
        val filterItem = FilterItem(FilterConstants.FILTER_TIME_3_DAY,
                FilterCondition(FilterConstants.FILTER_TIME, ""), "")
        val bean1 = BaseFileBean()
        bean1.mDateModified = System.currentTimeMillis()
        val bean2 = BaseFileBean()
        bean2.mDateModified = System.currentTimeMillis() - mDayMill
        val bean3 = BaseFileBean()
        bean3.mDateModified = getTomorrowZero()
        val bean4 = BaseFileBean()
        bean4.mDateModified = getSpecialPreDayZeroTime(3)
        val bean5 = BaseFileBean()
        bean5.mDateModified = getSpecialPreDayLastTime(3)
        val bean6 = BaseFileBean()
        bean6.mDateModified = getSpecialPreDayLastTime(4)
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
            add(bean4)
            add(bean5)
            add(bean6)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(3, resultList!!.size)
        Assert.assertTrue(resultList!!.contains(bean1))
        Assert.assertTrue(resultList!!.contains(bean2))
        Assert.assertTrue(resultList!!.contains(bean4))
    }

    @Test
    fun should_verify_filter_result_by_time_7_days() {
        val filterItem = FilterItem(FilterConstants.FILTER_TIME_7_DAY,
                FilterCondition(FilterConstants.FILTER_TIME, ""), "")
        val bean1 = BaseFileBean()
        bean1.mDateModified = System.currentTimeMillis()
        val bean2 = BaseFileBean()
        bean2.mDateModified = System.currentTimeMillis() - 3 * mDayMill
        val bean3 = BaseFileBean()
        bean3.mDateModified = getTomorrowZero()
        val bean4 = BaseFileBean()
        bean4.mDateModified = getSpecialPreDayZeroTime(6)
        val bean5 = BaseFileBean()
        bean5.mDateModified = getSpecialPreDayLastTime(7)
        val bean6 = BaseFileBean()
        bean6.mDateModified = getSpecialPreDayLastTime(8)
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
            add(bean4)
            add(bean5)
            add(bean6)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(3, resultList!!.size)
        Assert.assertTrue(resultList!!.contains(bean1))
        Assert.assertTrue(resultList!!.contains(bean2))
        Assert.assertTrue(resultList!!.contains(bean4))
    }

    @Test
    fun should_verify_filter_result_by_time_30_days() {
        val filterItem = FilterItem(FilterConstants.FILTER_TIME_30_DAY,
                FilterCondition(FilterConstants.FILTER_TIME, ""), "")
        val bean1 = BaseFileBean()
        bean1.mDateModified = System.currentTimeMillis()
        val bean2 = BaseFileBean()
        bean2.mDateModified = System.currentTimeMillis() - 26 * mDayMill
        val bean3 = BaseFileBean()
        bean3.mDateModified = getTomorrowZero()
        val bean4 = BaseFileBean()
        bean4.mDateModified = getSpecialPreDayZeroTime(29)
        val bean5 = BaseFileBean()
        bean5.mDateModified = getSpecialPreDayLastTime(30)
        val bean6 = BaseFileBean()
        bean6.mDateModified = getSpecialPreDayLastTime(31)
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
            add(bean4)
            add(bean5)
            add(bean6)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(3, resultList!!.size)
        Assert.assertTrue(resultList!!.contains(bean1))
        Assert.assertTrue(resultList!!.contains(bean2))
        Assert.assertTrue(resultList!!.contains(bean4))
    }

    private fun getTomorrowZero(): Long {
        val calendar = Calendar.getInstance()
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59)
        return calendar.timeInMillis + 1000
    }

    private fun getSpecialPreDayZeroTime(start: Int): Long {
        val calendar = Calendar.getInstance()
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59)
        return calendar.timeInMillis + 1000 - start * mDayMill
    }

    private fun getSpecialPreDayLastTime(start: Int): Long {
        val calendar = Calendar.getInstance()
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59)
        return calendar.timeInMillis - start * mDayMill
    }

}
