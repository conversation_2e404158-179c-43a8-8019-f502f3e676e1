/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : keweiwei
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/6/11 10:33
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/6/11       1.0      create
 ***********************************************************************/
package com.oplus.filemanager

import android.content.Context
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.startKoin
import org.koin.core.logger.Level
import org.koin.dsl.module

class FileManagerApplication : MainApplication() {

    override fun attachBaseContext(base: Context?) {
        inject()
        super.attachBaseContext(base)
    }

    private fun inject() {
        startKoin {
            androidLogger(Level.INFO)
            androidContext(this@FileManagerApplication)
            val context = module {
                single<Context> { this@FileManagerApplication }
            }
            modules(context)
            modules(KoinRegister.getKoinModules())
        }
    }
}