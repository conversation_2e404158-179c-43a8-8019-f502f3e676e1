<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundWithCard"
    android:clipChildren="false"
    android:splitMotionEvents="false">

    <com.filemanager.common.view.ViewPagerWrapperForPC
        android:id="@+id/view_pager_wrapper"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.filemanager.common.view.viewpager.RTLViewPager
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:orientation="horizontal" />
    </com.filemanager.common.view.ViewPagerWrapperForPC>

    <include layout="@layout/appbar_with_tab_layout_secondary" />

    <com.filemanager.common.view.FeedbackFloatingButton
        android:id="@+id/create_file_fab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginStart="@dimen/dimen_20dp"
        android:layout_marginTop="@dimen/dimen_24dp"
        android:layout_marginEnd="@dimen/dimen_40dp"
        android:layout_marginBottom="@dimen/dimen_24dp"
        android:clipChildren="false"
        android:elevation="0dp"
        android:forceDarkAllowed="false"
        android:transitionName="shared_element_fab"
        android:visibility="gone"
        app:mainFloatingButtonSrc="@drawable/ic_fab_add_label" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>