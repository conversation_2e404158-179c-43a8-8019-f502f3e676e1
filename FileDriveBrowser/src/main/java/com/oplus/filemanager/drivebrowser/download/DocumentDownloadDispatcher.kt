/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DocumentDownloadDispatcher
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/9 15:16
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/9       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.download

import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import androidx.annotation.VisibleForTesting
import com.filemanager.common.fileutils.fetchFileName
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.Utils
import com.filemanager.fileoperate.NotifyMediaScannerBatchAction
import com.oplus.filemanager.drivebrowser.data.repository.DocumentDownloadErrorCode
import com.oplus.filemanager.drivebrowser.data.repository.kdocs.KDocsFileDriveRepository
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.di.NetworkModule
import com.oplus.filemanager.drivebrowser.download.notification.DownloadNotificationManager
import com.oplus.filemanager.drivebrowser.download.notification.DownloadNotificationManager.Companion.NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.commons.io.FilenameUtils
import java.io.File
import java.lang.Exception
import kotlin.coroutines.CoroutineContext

/**
 * Document download when async requests are executed.
 */
class DocumentDownloadDispatcher(
    private val context: Context,
    private val scope: CoroutineScope
) {

    @get:Synchronized
    private var maxDownloadCount = 1

    @get:Synchronized
    private var currIndex = 0

    @get:Synchronized
    private var totalCount = 0

    private var callback: Callback? = null

    @Volatile
    private var isCancel: Boolean = false

    /**
     * Current document download task.
     */
    private var currDownloadTask: DownloadTaskInfo? = null

    /**
     * Ready async document download in the order
     */
    private var readyDownloadTasks = ArrayDeque<DownloadTaskInfo>()

    private var runningDownloadTasks = ArrayDeque<DownloadTaskInfo>()

    private var failureDownloadTasks = ArrayDeque<DownloadTaskInfo>()

    private var notificationManager: DownloadNotificationManager = DownloadNotificationManager(context)

    private var tencentDocumentDownload: DocumentDownload? = null

    private var kDocsDocumentDownload: DocumentDownload? = null

    private val mediaScannerBatchAction by lazy {
        NotifyMediaScannerBatchAction(Utils.MEDIA_SCAN_DOWNLOAD)
    }
    fun setCallback(callback: Callback) {
        if (this.callback != null) {
            return
        }
        this.callback = callback
    }

    fun onStartCommand(intent: Intent) {
        val action = intent.action
        Log.d(TAG, "onStartCommand -> action = $action")
        when (action) {
            ACTION_ENQUEUE_DOWNLOAD -> handleEnqueueDownload(intent)

            ACTION_CANCEL_DOWNLOAD -> handleCancelDownload()

            ACTION_RETRY_DOWNLOAD -> handleRetryDownload(intent)
        }
    }

    @VisibleForTesting
    fun handleEnqueueDownload(intent: Intent) {
        Log.d(TAG, "handleEnqueueDownload -> intent = $intent")
        val downloadTaskInfo = parseDownloadTaskInfo(intent.extras)
        if (checkDownloadInfoValidity(downloadTaskInfo)) {
            synchronized(this) {
                downloadTaskInfo?.let {
                    readyDownloadTasks.add(it)
                    totalCount++
                }
            }
            promoteAndExecute()
        } else {
            Log.d(TAG, "handleEnqueueDownload -> download info invalid")
            stopIfNoTasks()
        }
    }

    @VisibleForTesting
    fun handleCancelDownload() {
        currDownloadTask?.let { internalCancel(it) }
    }

    @VisibleForTesting
    fun handleRetryDownload(intent: Intent) {
        val retryTask = IntentUtils.getSerializableExtra(intent, KEY_RETRY_TASK)
        (retryTask as? ArrayList<DownloadTaskInfo>)?.let {
            internalRetry(it)
        }
    }

    private fun checkDownloadInfoValidity(downloadTaskInfo: DownloadTaskInfo?): Boolean {
        return downloadTaskInfo?.let {
            when (it.category) {
                CategoryHelper.CATEGORY_TENCENT_DOCS -> it.fileId.isNotEmpty()

                CategoryHelper.CATEGORY_K_DOCS -> it.fileId.isNotEmpty()

                else -> false
            }
        } ?: false
    }

    private fun promoteAndExecute(): Boolean {
        val executableTasks = mutableListOf<DownloadTaskInfo>()
        val isRunning: Boolean
        synchronized(this) {
            val taskInfoMutableIterator = readyDownloadTasks.iterator()
            while (taskInfoMutableIterator.hasNext()) {
                val downloadTask = taskInfoMutableIterator.next()
                if (runningTaskCounts() >= maxDownloadCount) break

                taskInfoMutableIterator.remove()
                executableTasks.add(downloadTask)
                runningDownloadTasks.add(downloadTask)
            }
            isRunning = runningTaskCounts() > 0
        }
        Log.d(TAG, "promoteAndExecute -> isRunning = $isRunning; count = ${readyTaskCounts()}-${runningTaskCounts()}")
        executableTasks.forEach { taskInfo ->
            currDownloadTask = taskInfo
            internalDownload(taskInfo)
        }
        return isRunning
    }

    @Suppress("TooGenericExceptionCaught")
    @VisibleForTesting
    fun internalDownload(taskInfo: DownloadTaskInfo, coroutineContext: CoroutineContext = Dispatchers.IO) {
        scope.launch(coroutineContext) {
            Log.d(TAG, "internalDownload -> taskInfo = $taskInfo")
            try {
                if (taskInfo.category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
                    tencentDocumentDownload = DocumentDownload(TencentFileDriveRepository(NetworkModule.providerTencentFileDriveService()))
                } else {
                    kDocsDocumentDownload = DocumentDownload(KDocsFileDriveRepository(NetworkModule.provideKDocsFileDriveService()))
                }
                currIndex++
                if (showForeground) {
                    notificationManager.createNotificationChannel(closeSound = true)
                    val notification =
                        notificationManager.createProgressNotification(FilenameUtils.getName(taskInfo.targetPath), 0, currIndex, totalCount)
                    (context as? DocumentDownloadService)?.startForeground(
                        NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID,
                        notification,
                        ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC
                    )
                    showForeground = false
                }
                verifyTaskInfo(taskInfo)
                val downloadResult = if (taskInfo.category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
                    tencentDocumentDownload?.download(taskInfo) {
                        notificationManager.showDownloadProgressNotification(FilenameUtils.getName(taskInfo.targetPath), it, currIndex, totalCount)
                    }
                } else {
                    kDocsDocumentDownload?.download(taskInfo) {
                        notificationManager.showDownloadProgressNotification(FilenameUtils.getName(taskInfo.targetPath), it, currIndex, totalCount)
                    }
                }
                Log.d(TAG, "internalDownload -> downloadResult = $downloadResult")
                if (downloadResult?.success == true) {
                    val file = File(taskInfo.targetPath)
                    mediaScannerBatchAction.add(taskInfo.targetPath)
                    mediaScannerBatchAction.flush()
                    notificationManager.showDownloadSuccessNotification(file.name, file.parent)
                } else {
                    withContext(Dispatchers.Main) {
                        disposeFailure(taskInfo, downloadResult)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "internalDownload -> error cause ${e.message}", e)
            } finally {
                finished(taskInfo)
            }
        }
    }

    /**
     * Verify taskInfo. If dest dir already exist target file, rename to avoid overwrite other file.
     */
    @VisibleForTesting
    fun verifyTaskInfo(taskInfo: DownloadTaskInfo) {
        Log.d(TAG, "verifyTaskInfo -> taskInfo = $taskInfo")
        val destFile = File(taskInfo.targetPath)
        var destPath = destFile.absolutePath
        if (destFile.exists()) {
            Log.d(TAG, "verifyTaskInfo -> destFile already exists.")
            val targetDir = destFile.parent
            targetDir?.let { dir ->
                fetchFileName(dir, destFile.nameWithoutExtension, ".${destFile.extension}")?.let { path ->
                    destPath = path
                }
            }
        }
        Log.d(TAG, "verifyTaskInfo -> destPath = $destPath")
        taskInfo.targetPath = destPath
    }

    @VisibleForTesting
    fun disposeFailure(taskInfo: DownloadTaskInfo, downloadResult: DownloadResult?) {
        Log.d(TAG, "disposeDownloadFailure -> isCancel = $isCancel ; result = $downloadResult")
        if (!isCancel()) {
            val errorCode = downloadResult?.errorCode ?: 0
            if (DocumentDownloadErrorCode.unSupportRetryError(errorCode)) {
                CustomToast.showShort(com.filemanager.common.R.string.download_file_error)
            } else {
                failureDownloadTasks.add(taskInfo)
                val failurePaths = mutableListOf<String>()
                val retryTasks = arrayListOf<DownloadTaskInfo>()
                failureDownloadTasks.forEach {
                    failurePaths.add(it.targetPath)
                    retryTasks.add(it)
                }
                notifyFailure(failurePaths, retryTasks)
            }
        } else {
            setCancel(false)
        }
    }

    @VisibleForTesting
    fun notifyFailure(
        failurePaths: MutableList<String>,
        retryTasks: ArrayList<DownloadTaskInfo>
    ) {
        notificationManager.showDownloadFailureNotification(failurePaths, retryTasks)
    }

    private fun internalCancel(taskInfo: DownloadTaskInfo) {
        setCancel(true)
        synchronized(this) {
            if (taskInfo.category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
                tencentDocumentDownload?.cancelDownload()
            } else if (taskInfo.category == CategoryHelper.CATEGORY_K_DOCS) {
                kDocsDocumentDownload?.cancelDownload()
            }
            runningDownloadTasks.remove(taskInfo)
            readyDownloadTasks.clear()
        }
        scope.launch(Dispatchers.IO) {
            clearCache(taskInfo)
            finished(taskInfo)
        }
    }

    private fun clearCache(downloadTaskInfo: DownloadTaskInfo) {
        downloadTaskInfo.targetPath.let {
            val file = File(it)
            if (file.exists()) file.delete()
        }
    }

    private fun internalRetry(downloadTaskInfos: ArrayList<DownloadTaskInfo>) {
        synchronized(this) {
            notificationManager.cancelDownloadFailedNotification()
            val iterator = failureDownloadTasks.iterator()
            while (iterator.hasNext()) {
                val downloadInfo = iterator.next()
                readyDownloadTasks.add(downloadInfo)
            }
            downloadTaskInfos.forEach {
                Log.d(TAG, "internalRetry -> downloadTaskInfo = $it")
                if (failureDownloadTasks.contains(it)) {
                    readyDownloadTasks.add(it)
                    failureDownloadTasks.remove(it)
                } else {
                    readyDownloadTasks.add(it)
                }
                totalCount++
            }
            failureDownloadTasks.clear()
            promoteAndExecute()
        }
    }

    private fun finished(taskInfo: DownloadTaskInfo) {
        Log.d(TAG, "finished -> taskInfo = $taskInfo")
        if (!runningDownloadTasks.remove(taskInfo)) {
            Log.d(TAG, "finished -> remove $taskInfo error")
        }
        val isRunning = promoteAndExecute()
        if (!isRunning) {
            Log.d(TAG, "finished -> no running task, stop service")
            totalCount = 0
            currIndex = 0
            showForeground = true
            callback?.stop()
            notificationManager.cancelDownloadProgressNotification(context)
        }
    }

    private fun stopIfNoTasks() {
        if (runningDownloadTasks.isEmpty() && readyDownloadTasks.isEmpty()) {
            Log.d(TAG, "No running or ready task, stop self")
            callback?.stop()
        }
    }

    @VisibleForTesting
    @Synchronized
    fun isCancel(): Boolean {
        return isCancel
    }

    @VisibleForTesting
    @Synchronized
    fun setCancel(cancel: Boolean) {
        isCancel = cancel
    }

    @Synchronized
    fun runningTaskCounts(): Int = runningDownloadTasks.size

    @Synchronized
    fun readyTaskCounts(): Int = readyDownloadTasks.size

    interface Callback {
        fun stop()
    }

    companion object {
        private const val TAG = "DocumentDownloadDispatcher"

        var showForeground = true

        @VisibleForTesting
        const val ACTION_ENQUEUE_DOWNLOAD = "ACTION_ENQUEUE_DOWNLOAD"
        @VisibleForTesting
        const val ACTION_CANCEL_DOWNLOAD = "ACTION_CANCEL_DOWNLOAD"
        @VisibleForTesting
        const val ACTION_RETRY_DOWNLOAD = "ACTION_RETRY_DOWNLOAD"
        private const val KEY_RETRY_TASK = "RETRY_TASK"

        fun createCancelDownloadIntent(context: Context): Intent {
            val intent = Intent(context, DocumentDownloadService::class.java)
            intent.action = ACTION_CANCEL_DOWNLOAD
            return intent
        }

        fun createRetryDownloadIntent(context: Context, failureDownloadTasks: ArrayList<DownloadTaskInfo>): Intent {
            val intent = Intent(context, DocumentDownloadService::class.java)
            intent.action = ACTION_RETRY_DOWNLOAD
            intent.putExtra(KEY_RETRY_TASK, failureDownloadTasks)
            return intent
        }

        fun createEnqueueDownloadIntent(
            context: Context,
            category: Int,
            fileId: String,
            exportType: String,
            targetPath: String
        ) {
            val intent = Intent(context, DocumentDownloadService::class.java)
            intent.action = ACTION_ENQUEUE_DOWNLOAD
            intent.putExtra(KEY_SOURCE_TYPE, category)
            intent.putExtra(KEY_FILE_ID, fileId)
            intent.putExtra(KEY_EXPORT_TYPE, exportType)
            intent.putExtra(KEY_TARGET_PATH, targetPath)
            if (showForeground && PermissionUtils.hasNotificationPermission(context)) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
    }
}