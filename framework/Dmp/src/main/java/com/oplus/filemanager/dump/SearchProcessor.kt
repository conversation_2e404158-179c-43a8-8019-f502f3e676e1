/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : SearchProcessor.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.dump

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.annotation.WorkerThread
import com.filemanager.common.MyApplication
import com.filemanager.common.bean.SearchDataBean
import com.filemanager.common.bean.SearchResultBean
import com.oplus.dmp.sdk.BusinessConstants
import com.oplus.dmp.sdk.InitConfig
import com.oplus.dmp.sdk.analyzer.IAnalyzer
import com.oplus.dmp.sdk.analyzer.bean.AnalyzedResult
import com.oplus.dmp.sdk.analyzer.bean.AnalyzedTerm
import com.oplus.dmp.sdk.analyzer.local.dict.entity.LocalAnalyzerConfigure
import com.oplus.dmp.sdk.analyzer.normalize.NormalizeHelper
import com.oplus.dmp.sdk.index.IndexProtocol
import com.oplus.dmp.sdk.querypreprocess.PreProcessType
import com.oplus.dmp.sdk.querypreprocess.QueryPreProcessContext
import com.oplus.dmp.sdk.querypreprocess.QueryPreProcessorHelper
import com.oplus.dmp.sdk.search.RawDocument
import com.oplus.dmp.sdk.search.bean.CommonBriefSorter
import com.oplus.dmp.sdk.search.bean.DefaultRecallStrategy
import com.oplus.dmp.sdk.search.bean.HitFieldStatistics
import com.oplus.dmp.sdk.search.bean.HitTermNumberFilter
import com.oplus.dmp.sdk.search.bean.RecallStrategy
import com.oplus.dmp.sdk.search.bean.RecallStrategyScoreSorter
import com.oplus.dmp.sdk.search.bean.RecallTypeGroupSorter
import com.oplus.dmp.sdk.search.bean.RecallTypeSorter
import com.oplus.dmp.sdk.search.bean.RelativityGroupSorter
import com.oplus.dmp.sdk.search.bean.RelativitySorter
import com.oplus.dmp.sdk.search.bean.SearchExpr
import com.oplus.dmp.sdk.search.bean.SearchTerm
import com.oplus.dmp.sdk.search.bean.SortOrder
import com.oplus.dmp.sdk.search.bean.Sorter
import com.oplus.dmp.sdk.search.bean.SubstringMatchRecallStrategy
import com.oplus.dmp.sdk.search.bean.SubstringMatchRecallStrategyParam
import com.oplus.dmp.sdk.search.bean.v2.CommonHighlighter
import com.oplus.dmp.sdk.search.bean.v2.CommonHighlighter.Companion.ALL_SUB_STRING
import com.oplus.dmp.sdk.search.bean.v2.Highlighter
import com.oplus.dmp.sdk.search.engine.CustomCursor
import com.oplus.dmp.sdk.search.engine.SearchProxyV3
import com.oplus.dmp.sdk.search.engine.StandardSearchRequest
import com.oplus.dmp.sdk.version.VersionProtocol
import com.oplus.filemanager.dump.IndexProcessor.INDEX_FIELD_DETECT_TIME
import com.oplus.filemanager.dump.IndexProcessor.INDEX_FIELD_FILE_SIZE
import com.oplus.filemanager.dump.IndexProcessor.INDEX_FIELD_FILE_TIME
import com.oplus.filemanager.dump.IndexProcessor.INDEX_FIELD_NAME
import com.oplus.filemanager.dump.IndexProcessor.INDEX_FIELD_NAME_PINGYIN
import com.oplus.filemanager.dump.IndexProcessor.INDEX_FIELD_PACKAGE
import com.oplus.filemanager.dump.IndexProcessor.INDEX_FIELD_SOURCE_NAME
import com.oplus.filemanager.dump.IndexProcessor.INDEX_FIELD_SOURCE_TYPE
import com.oplus.filemanager.dump.SearchManagerUtil.getClientWrapper
import com.oplus.filemanager.dump.SearchManagerUtil.initSyncWrapper
import com.oplus.filemanager.dump.bean.SwitchBean

object SearchProcessor {

    private const val TAG = "SearchProcessor"

    private const val ACCURATE_NAME_RECALL_STRATEGY = "accurateNameRecall"
    private const val ACCURATE_SOURCE_NAME_RECALL_STRATEGY = "accurateSourceNameRecall"
    private const val SPLIT_NAME_RECALL_STRATEGY = "splitNameRecall"

    private const val HIGHLIGHT_NAME = "highlight"

    private const val DEFAULT_PAGE_NUM = 1
    private const val DEFAULT_PAGE_SIZE = 20
    private const val DEFAULT_FILTER_PERCENT = 0.5
    private const val DEFAULT_HIGHLIGHT_PREFIX = 10
    private const val DEFAULT_HIGHLIGHT_SUFFIX = 10
    private const val MIN_RELATE_SORTER = 0.5f
    private const val MAX_RELATE_SORTER = 1.0f

    private const val SWITCH_INDEX_PROVIDER_URI = "content://com.oplus.dmp.index"
    private const val SWITCH_CURSOR_KEY_RESULT = "result"
    private const val SWITCH_CURSOR_KEY_ERRORMSG = "errMsg"
    private const val SWITCH_CURSOR_KEY_VALUE = "value"
    private const val SWITCH_BUNDLE_METHOD_KEY = "method"
    private const val SWITCH_BUNDLE_SET_METHOD = "setSearchOtherAppFile"
    private const val SWITCH_BUNDLE_GET_METHOD = "getSearchOtherAppFile"
    private const val SWITCH_BUNDLE_VALUE_KEY = "value"


    @Volatile
    private var searchProxy: SearchProxyV3? = null

    @Volatile
    private var analyzer: IAnalyzer? = null


    /**
     * sdk初始化，包含api版本号设置以及分词词典相关设置。
     */
    fun init(context: Context): Boolean {
        val supportVersions = intArrayOf(VersionProtocol.API_VERSION_2, VersionProtocol.API_VERSION_3)
        val localAnalyzerConfigure = LocalAnalyzerConfigure.Builder()
            .setCutAll(false)
            .setAsTermDictUseNerDict(true)
            .build()
        val initConfig = InitConfig(supportVersions, localAnalyzerConfigure)
        val initResult = initSyncWrapper(context, initConfig)
        Log.i(TAG, "init initConfig $initConfig initResult $initResult")
        //这里调用本地搜索的的初始化内部变量的逻辑
        SearchHelper.getStatusAfterInitDmp()
        return initResult
    }


    fun isServiceAvailable(): Boolean {
        val result = SearchManagerUtil.getInstanceWrapper()?.isServiceAvailable ?: false
        Log.i(TAG, "isServiceAvailable $result")
        return result
    }


    /**
     * 获取索引中的所有精简数据
     */
    @WorkerThread
    fun queryRaw(): List<SearchResultBean> {
        val searchProxy = getSearchProxy()
        val result = mutableListOf<SearchResultBean>()
        Log.i(TAG, "queryRaw ing searchProxy $searchProxy")
        if (searchProxy == null) {
            Log.e(TAG, "queryRaw searchProxy null, return")
            return result
        }
        runCatching {
            val rawDocuments = searchProxy.queryDocuments() ?: return result
            Log.i(TAG, "queryRaw ing raw data size ${rawDocuments.size}")
            val iterator = rawDocuments.iterator()
            while (iterator.hasNext()) {
                val resultBean = getSearchResultBeanFromRaw(iterator.next())
                result.add(resultBean)
            }
        }.onFailure {
            Log.e(TAG, "queryRaw error", it)
        }
        return result
    }

    /**
     * 获取搜索结果，输入字符串，输出结果列表
     */
    @WorkerThread
    fun startSearchThirdAppFile(inputQuery: String, page: Int): List<SearchResultBean> {
        //预处理
        Log.i(TAG, "startSearchThirdAppFile start inputQuery $inputQuery, page $page")
        val queryPreProcessContext = queryPreProcessContext(inputQuery)
        //分词处理
        val analyzedTermList = getAnalyzedSplitTerms(inputQuery)
        val result = mutableListOf<SearchResultBean>()
        if (analyzedTermList.isEmpty()) {
            Log.i(TAG, "startSearchThirdAppFile buildAnalyzedTermsState fail, return")
            return result
        }
        if (queryPreProcessContext == null) {
            Log.i(TAG, "startSearchThirdAppFile queryPreProcessContext null, return")
            return result
        }
        runCatching {
            //构建召回策略
            val strategyMap = getRecallStrategyForQuery(queryPreProcessContext, analyzedTermList)
            //获取排序器
            val totalStrategyList = strategyMap.values.toList()
            val sorters = getSortersForQuery(totalStrategyList, strategyMap)
            //构建searchRequest
            val request = getSearchRequest(page, inputQuery, totalStrategyList, sorters)
            // 获得searchProxy 对象
            val searchProxy = getSearchProxy()
            Log.i(TAG, "startSearchThirdAppFile ing searchProxy $searchProxy")
            searchProxy?.let {
                val cursor = it.search(request)
                checkNotNull(cursor)
                Log.i(TAG, "startSearchThirdAppFile ing cursor ${cursor.count}")
                while (cursor.moveToNext()) {
                    val resultBean = getSearchResultBeanFromCursor(cursor)
                    Log.i(TAG, "search ING, bean $resultBean")
                    result.add(resultBean)
                }
            }
        }.onFailure {
            Log.e(TAG, "startSearchThirdAppFile error,", it)
        }
        Log.i(TAG, "search input $inputQuery, output ${result.size}")
        return result
    }

    private fun getSearchRequest(
        page: Int,
        inputQuery: String,
        totalStrategyList: List<RecallStrategy>,
        sorters: List<Sorter>
    ): StandardSearchRequest? {
        val request = StandardSearchRequest()
            .setColumnNames(
                listOf(
                    INDEX_FIELD_NAME,
                    INDEX_FIELD_PACKAGE,
                    INDEX_FIELD_SOURCE_NAME,
                    INDEX_FIELD_FILE_TIME,
                    INDEX_FIELD_DETECT_TIME,
                    INDEX_FIELD_SOURCE_TYPE,
                    INDEX_FIELD_FILE_SIZE
                )
            )
            .setStatisticians(listOf(HitFieldStatistics()))
            //外部输入的page是从0开始，中子这边需要从1开始，这里产品要求不做分页，设置pageSize为最大值
            .setPageNum(page + 1)
            .setPageSize(BusinessConstants.MAX_RESULT_VALUE)
            .setOriginQuery(inputQuery)
            .setRecallStrategies(totalStrategyList)
            .setSorters(sorters)
            //这里需要调整，0.5
            .setFilters(listOf(HitTermNumberFilter(DEFAULT_FILTER_PERCENT)))
            //这里需要调整，设置高亮配置
            .setHighlighters(
                listOf<Highlighter>(
                    CommonHighlighter(
                        INDEX_FIELD_NAME, 0, 0, false, true, ALL_SUB_STRING
                    )
                )
            )
        return request
    }

    private fun getSearchResultBeanFromCursor(cursor: CustomCursor): SearchResultBean {
        val searchBean = SearchDataBean()
        var index = cursor.getColumnIndex(INDEX_FIELD_NAME)
        if (index > 0) {
            searchBean.fileName = cursor.getString(index)
        }
        index = cursor.getColumnIndex(INDEX_FIELD_SOURCE_NAME)
        if (index > 0) {
            searchBean.sourceName = cursor.getString(index)
        }
        index = cursor.getColumnIndex(INDEX_FIELD_PACKAGE)
        if (index > 0) {
            searchBean.sourcePackage = cursor.getString(index)
        }
        index = cursor.getColumnIndex(INDEX_FIELD_FILE_TIME)
        if (index > 0) {
            searchBean.fileTime = cursor.getLong(index)
        }
        index = cursor.getColumnIndex(INDEX_FIELD_DETECT_TIME)
        if (index > 0) {
            searchBean.detectTime = cursor.getLong(index)
        }
        index = cursor.getColumnIndex(INDEX_FIELD_FILE_SIZE)
        if (index > 0) {
            searchBean.fileSize = cursor.getLong(index)
        }
        index = cursor.getColumnIndex(INDEX_FIELD_SOURCE_TYPE)
        if (index > 0) {
            searchBean.sourceType = cursor.getInt(index)
        }
        index = cursor.getColumnIndex(HIGHLIGHT_NAME)
        val highLightString = if (index > 0) {
            cursor.getString(index)
        } else {
            ""
        }
        index = cursor.getColumnIndex(IndexProtocol.DOC_IDENTIFICATION)
        val identification = if (index > 0) {
            cursor.getString(index)
        } else {
            ""
        }
        index = cursor.getColumnIndex(IndexProtocol.DOC_CHECKSUM)
        val checkSum = if (index > 0) {
            cursor.getLong(index)
        } else {
            0L
        }
        return SearchResultBean(searchBean, highLightString, identification, checkSum)
    }


    private fun getSearchResultBeanFromRaw(rawDocument: RawDocument): SearchResultBean {
        return SearchResultBean(
            SearchDataBean(),
            "",
            rawDocument.identification as String,
            rawDocument.checksum
        )
    }

    @WorkerThread
    private fun getSearchProxy(): SearchProxyV3? {
        if (searchProxy == null) {
            // 指定资源名，每个资源都有独立的数据库
            searchProxy = getClientWrapper(IndexProcessor.CLIENT_NAME)
                ?.searchProxy as? SearchProxyV3
        }
        return searchProxy
    }


    fun resetProxy() {
        Log.i(TAG, "resetProxy")
        searchProxy = null
        //清空sdk内部缓存
        SearchManagerUtil.getInstanceWrapper()?.destroy()
    }

    /**
     * 这里判断是V3版本的
     */
    fun checkSupportV3(): Boolean {
        val proxyV3 = getSearchProxy()
        val result = proxyV3 != null
        Log.i(TAG, "checkSupportV3 result $result")
        return result
    }


    @WorkerThread
    private fun getAnalyzer(): IAnalyzer? {
        if (analyzer == null) {
            // 指定资源名，每个资源都有独立的数据库
            analyzer = SearchManagerUtil.getInstanceWrapper()?.analyzer
        }
        return analyzer
    }


    private fun queryPreProcessContext(inputQuery: String): QueryPreProcessContext? {
        val queryPreProcessorHelper =
            QueryPreProcessorHelper(
                PreProcessType.NORMALIZE,
                PreProcessType.ERROR_CORRECT
            )
        // 可选，自定义归一化类型
        queryPreProcessorHelper.setNormalizeTypes(
            NormalizeHelper.DEFAULT_NORMALIZE_TYPE
                    or NormalizeHelper.FILTER_PUNCTUATION_TYPE
        )
        val queryPreProcessContext = queryPreProcessorHelper.doPreProcess(inputQuery)
        Log.i(TAG, "queryPreProcessContext $queryPreProcessContext")
        return queryPreProcessContext
    }


    private fun getAnalyzedSplitTerms(query: String): List<AnalyzedTerm> {
        var result = listOf<AnalyzedTerm>()
        kotlin.runCatching {
            val analyzedResult: AnalyzedResult? = getAnalyzer()?.analyze(query)
            analyzedResult?.let {
                result = it.originalQueryAnalyzeResult.analyzedTerms
            }
        }.onFailure {
            Log.i(TAG, "getRecallStrategyForQuery fail: ${it.message}")
        }
        return result
    }


    private fun getRecallStrategyForQuery(
        queryContext: QueryPreProcessContext,
        analyzedTerms: List<AnalyzedTerm>
    ): Map<String, RecallStrategy> {
        Log.i(TAG, "getRecallStrategyForQuery $queryContext")
        val nameAccurateRecallStrategy = getAccuratNameRecallStrategy(queryContext)
        val sourceNameAccurateeRecallStrategy = getAccuratSourceNameRecallStrategy(queryContext)
        val splitNameRecallStrategy = getSplitTermNameRecallStrategy(queryContext, analyzedTerms)
        val result = mutableMapOf<String, RecallStrategy>()
        result[ACCURATE_NAME_RECALL_STRATEGY] = nameAccurateRecallStrategy
        result[ACCURATE_SOURCE_NAME_RECALL_STRATEGY] = sourceNameAccurateeRecallStrategy
        result[SPLIT_NAME_RECALL_STRATEGY] = splitNameRecallStrategy
        Log.i(
            TAG,
            "getRecallStrategyForQuery nameAccurateRecallStrategy $nameAccurateRecallStrategy, " +
                    "sourceNameAccurateeRecallStrategy $sourceNameAccurateeRecallStrategy, " +
                    "splitNameRecallStrategy $splitNameRecallStrategy"
        )
        return result
    }

    private fun getAccuratNameRecallStrategy(queryContext: QueryPreProcessContext): RecallStrategy {
        val substringMatchRecallStrategyParam = SubstringMatchRecallStrategyParam(
            queryContext.originalQuery,
            SearchTerm.MatchPattern.SUBSTRING,
            SubstringMatchRecallStrategyParam.FieldRelation.AND,
            false,
            INDEX_FIELD_NAME
        )
        return SubstringMatchRecallStrategy(substringMatchRecallStrategyParam)
    }

    private fun getAccuratSourceNameRecallStrategy(queryContext: QueryPreProcessContext): RecallStrategy {
        val substringMatchRecallStrategyParam = SubstringMatchRecallStrategyParam(
            queryContext.originalQuery,
            SearchTerm.MatchPattern.SUBSTRING,
            SubstringMatchRecallStrategyParam.FieldRelation.AND,
            false,
            INDEX_FIELD_SOURCE_NAME
        )
        return SubstringMatchRecallStrategy(substringMatchRecallStrategyParam)
    }

    private fun getSplitTermNameRecallStrategy(
        queryContext: QueryPreProcessContext,
        analyzedTerms: List<AnalyzedTerm>
    ): RecallStrategy {
        val exprBuilder = SearchExpr.Builder()
        //预处理(纠错，近义词，大写转小写等)的搜索
        val termSet = HashSet<String>()
        queryContext.results.forEach { processQuery ->
            val rewrittenQuery = processQuery.rewrittenQuery
            if (termSet.contains(rewrittenQuery)) {
                return@forEach
            }
            termSet.add(rewrittenQuery)
            exprBuilder.orHave(
                SearchTerm(rewrittenQuery, INDEX_FIELD_NAME).apply {
                    isRewriteQuery = true
                }
            )
        }
        //分词的搜索
        analyzedTerms.forEach { analyzedTerm ->
            val analyzeWord = analyzedTerm.word
            if (termSet.contains(analyzeWord)) {
                return@forEach
            }
            termSet.add(analyzeWord)
            exprBuilder.orHave(
                SearchTerm(analyzeWord, INDEX_FIELD_NAME).apply {
                    wordWeight = analyzedTerm.weight
                }
            ).orHave(SearchTerm(analyzeWord, INDEX_FIELD_NAME_PINGYIN).apply {
                wordWeight = analyzedTerm.weight
            })
        }
        return DefaultRecallStrategy(exprBuilder.build())
    }


    /**
     * 获取排序规则，
     * 详细规则PRD: https://odocs.myoas.com/docs/1syFozrfmrkJzvA4 5.4.3小节
     * 技术接入文档：https://odocs.myoas.com/docs/zgWjOhFW3BMsA4rN
     */
    private fun getSortersForQuery(
        strategyList: List<RecallStrategy>,
        strategyMap: Map<String, RecallStrategy>
    ): List<Sorter> {
        Log.i(TAG, "getSortersForQuery $strategyMap")
        val accurateNameStrategy = strategyMap[ACCURATE_NAME_RECALL_STRATEGY]
        val accurateSourceNameStrategy = strategyMap[ACCURATE_SOURCE_NAME_RECALL_STRATEGY]
        val splitNameStrategy = strategyMap[SPLIT_NAME_RECALL_STRATEGY] as? DefaultRecallStrategy
        checkNotNull(accurateNameStrategy)
        checkNotNull(accurateSourceNameStrategy)
        checkNotNull(splitNameStrategy)
        // 同时命中标题与来源 -> 1优先级 对应PRD5.4.3小节的优先级1处理
        val titleAndSourceStage = listOf(accurateNameStrategy, accurateSourceNameStrategy)
        // 只命中标题 -> 2 对应PRD5.4.3小节的优先级2处理
        val titleStage = listOf(accurateNameStrategy)
        // 按照时间排序
        val timeSorter = CommonBriefSorter(INDEX_FIELD_DETECT_TIME, SortOrder.DESC)
        // 命中标题与来源主排序器, （标题+来源）均满足精准搜索，即like语句命中
        val stage1RecallTypeSorter = RecallTypeSorter(
            strategyList,
            listOf(titleAndSourceStage, titleStage, listOf(splitNameStrategy))
        )
        // 命中标题与来源组合排序器, stage1RecallTypeSorter中两个数据优先级相同时，按照时间排序。对应PRD5.4.3小节的优先级1处理
        val stage1GroupSorter = RecallTypeGroupSorter(
            stage1RecallTypeSorter,
            strategyList,
            mapOf(Pair(titleAndSourceStage, timeSorter), Pair(titleStage, timeSorter))
        )
        // 相关性为[50%,100%]排序器
        val relativitySorter = RelativitySorter(
            listOf(IndexProcessor.INDEX_FIELD_NAME, INDEX_FIELD_NAME_PINGYIN),
            MIN_RELATE_SORTER,
            MAX_RELATE_SORTER
        )
        // 相关性为[50%,100%]组合排序器，对应对应PRD5.4.3小节的优先级3处理
        val relativityGroupSorter = RelativityGroupSorter(
            relativitySorter,
            mapOf(
                Pair(
                    RelativityGroupSorter.EQUAL_TO_RELATIVITY,
                    RecallStrategyScoreSorter(splitNameStrategy, strategyList)
                )
            )
        )
        //下面列表中最后一个timesoter 对应PRD5.4.3小节的优先级4处理
        return listOf(stage1GroupSorter, relativityGroupSorter, timeSorter)
    }


    /**
     * 接口文档：https://odocs.myoas.com/docs/N2A1MEMe9vTryBAD   第3小结
     * 更新开关状态到中子搜索中台DMP中
     */
    fun setSwitchStateToDmp(switchOn: Boolean): SwitchBean {
        val data = Bundle()
        data.putString(SWITCH_BUNDLE_METHOD_KEY, SWITCH_BUNDLE_SET_METHOD)
        data.putBoolean(SWITCH_BUNDLE_VALUE_KEY, switchOn)
        val uri = Uri.parse(SWITCH_INDEX_PROVIDER_URI)
        val context = MyApplication.appContext
        val resultBean = SwitchBean()
        runCatching {
            val cursor: Cursor? = context.contentResolver.query(uri, null, data, null)
            if (cursor == null) {
                //老版本dmp，不支持更新状态到dmp
                resultBean.switchValue = false
                resultBean.errorMsg = SwitchBean.LOWER_DMP_NOT_SUPPORT
            } else {
                cursor.use {
                    //设置是否成功
                    val result = cursor.extras.getBoolean(SWITCH_CURSOR_KEY_RESULT, false)
                    //设置失败，错误信息
                    val errMsg = cursor.extras.getString(SWITCH_CURSOR_KEY_ERRORMSG) ?: ""
                    resultBean.isSuc = result
                    resultBean.errorMsg = errMsg
                }
            }
        }.onFailure {
            Log.e(TAG, "setSwitchStateToDmp error", it)
        }
        Log.d(TAG, "setSwitchStateToDmp input $switchOn, result $resultBean")
        return resultBean
    }


    /**
     * 接口文档：https://odocs.myoas.com/docs/N2A1MEMe9vTryBAD  第3小结
     * 从中子搜索中台DMP获取开关状态
     */
    fun getSwitchStateFromDmp(): SwitchBean {
        val data = Bundle()
        data.putString(SWITCH_BUNDLE_METHOD_KEY, SWITCH_BUNDLE_GET_METHOD)
        val uri = Uri.parse(SWITCH_INDEX_PROVIDER_URI)
        val context = MyApplication.appContext
        val resultBean = SwitchBean()
        runCatching {
            val cursor: Cursor? = context.contentResolver.query(uri, null, data, null)
            if (cursor == null) {
                //老版本dmp，不支持更新状态到dmp
                resultBean.switchValue = false
                resultBean.errorMsg = SwitchBean.LOWER_DMP_NOT_SUPPORT
            } else {
                cursor.use {
                    //获取是否成功
                    val result = cursor.extras.getBoolean(SWITCH_CURSOR_KEY_RESULT, false)
                    //获取到的开关状态
                    val switchOn = cursor.extras.getBoolean(SWITCH_CURSOR_KEY_VALUE, false)
                    //设置失败，错误信息
                    val errMsg = cursor.extras.getString(SWITCH_CURSOR_KEY_ERRORMSG) ?: ""
                    resultBean.isSuc = result
                    resultBean.switchValue = switchOn
                    resultBean.errorMsg = errMsg
                }
            }
        }.onFailure {
            Log.e(TAG, "getSwitchStateFromDmp error", it)
        }
        Log.i(TAG, "getSwitchStateFromDmp result $resultBean")
        return resultBean
    }
}