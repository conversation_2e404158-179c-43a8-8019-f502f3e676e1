/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - RecyclerViewHolder.java
 ** Description: Recycler ViewHolder
 ** Version: 1.0
 ** Date : 2020/05/25
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  Jiafei.<PERSON>@Apps.FileManager      2020/05/25    1.0     create
 ****************************************************************/

package com.oplus.dropdrag.base

import android.view.MotionEvent
import android.view.View
import androidx.annotation.Keep
import androidx.recyclerview.widget.RecyclerView
import com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails

@Keep
abstract class SelectionViewHolder<K>(itemView: View) : RecyclerView.ViewHolder(itemView) {
    protected var mDetails: ItemDetails<K>? = null

    open fun getItemDetails(): ItemDetails<K>? {
        return mDetails
    }

    abstract fun updateKey(key: K?)

    abstract fun isInDragRegion(event: MotionEvent): Boolean

    abstract fun isInSelectRegion(event: MotionEvent): Boolean
}