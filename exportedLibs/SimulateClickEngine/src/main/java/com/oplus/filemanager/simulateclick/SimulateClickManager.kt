/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SimulateClickManager.kt
 * Description:
 *     The manager for supported scripts from SimulateClickEngine
 *
 * Version: 1.0
 * Date: 2024-05-28
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-05-28   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick

import android.content.Context
import androidx.annotation.Keep
import com.oplus.filemanager.simulateclick.engine.SCRIPT_ARGS_PREFIX
import com.oplus.filemanager.simulateclick.engine.utils.Log
import com.oplus.filemanager.simulateclick.file.RemoteScriptReceiveHandler
import com.oplus.filemanager.simulateclick.request.ScriptExecRequest
import com.oplus.filemanager.simulateclick.sdk.BuildConfig
import com.oplus.filemanager.simulateclick.sdk.OnScriptDetectedCallback
import com.oplus.filemanager.simulateclick.service.ScriptServiceManager
import java.io.File

@Keep
object SimulateClickManager {

    private const val TAG = "SimulateClickManager"
    private const val SCRIPT_ARG_0 = "${SCRIPT_ARGS_PREFIX}0"
    private const val SKILL_OPEN_FILE = "open_file"

    @JvmStatic
    var baseLogTag: String
        get() = Log.baseLogTag
        set(value) = Log::baseLogTag.set(value)

    @JvmStatic
    var logEnable: Boolean
        get() = Log.enableLog
        set(value) = Log::enableLog.set(value)

    @JvmStatic
    var enableDebugScripts: Boolean = BuildConfig.DEBUG

    @JvmStatic
    private val jumpper: OtherAppJumpper = OtherAppJumpper()

    /**
     * 初始化日志和debug模式的接口、
     * @param logEnable 内部日志开关
     * @param baseLogTag 内部日志的TAG
     * @param enableDebugScripts 是否使用内部debug路径下的脚本调试
     */
    @JvmStatic
    fun initLogAndDebug(logEnable: Boolean = false, baseLogTag: String? = null, enableDebugScripts: Boolean = false) {
        SimulateClickManager.logEnable = logEnable
        if (!baseLogTag.isNullOrEmpty()) {
            SimulateClickManager.baseLogTag = baseLogTag
        }
        SimulateClickManager.enableDebugScripts = enableDebugScripts
    }


    /**
     * 跳转3方应用的接口
     * @param context 输入的context
     * @param dataBean 需要跳转应用的基本信息，内部包含输入文本框的文件名称+跳转的应用包名
     */
    @JvmStatic
    fun jumpToOtherApp(context: Context, dataBean: InputDataBean) {
        jumpper.onClickThirdAppFile(context, dataBean)
    }

    @Keep
    class OpenFileExecutor internal constructor(
        private val packageName: String,
        private val fileName: String,
    ) {
        private var detectedCallback: OnScriptDetectedCallback? = null

        fun onDetectedCallback(callback: OnScriptDetectedCallback): OpenFileExecutor =
            apply {
                Log.i("SimulateClick", "onDetectedCallback $callback")
                detectedCallback = callback
            }

        fun execute(context: Context) {
            val request = ScriptExecRequest(
                packageName = packageName,
                skillName = SKILL_OPEN_FILE,
                replacedMap = mapOf(SCRIPT_ARG_0 to fileName),
                detectedCallback = detectedCallback
            )
            Log.i("SimulateClick", "execute request $request")
            ScriptServiceManager.executeScript(context, request)
        }
    }

    @Keep
    class SkillCaller internal constructor(
        private val packageName: String
    ) {
        /**
         * Execute below steps:
         * 1. Launch main page of target app
         * 2. Click search widget to enter search page
         * 3. Auto input [fileName] into search box to search file
         */
        fun openSearchFile(fileName: String): OpenFileExecutor =
            OpenFileExecutor(packageName, fileName)
    }

    @JvmStatic
    fun callOnApp(packageName: String): SkillCaller {
        logSdkVersionInfo()
        return SkillCaller(packageName)
    }

    @Keep
    class RemoteScriptsReceiver internal constructor(context: Context) {
        private val receiveHandler = RemoteScriptReceiveHandler(context).apply {
            triggerTimeoutFinish()
        }

        fun receiveRemoteScripts(remoteFile: File): RemoteScriptsReceiver = apply {
            receiveHandler.triggerReceiveRemoteFile(remoteFile)
        }

        fun checkReceiveDebugRemoteScripts(): RemoteScriptsReceiver = apply {
            receiveHandler.triggerReceiveDebugRemoteFile()
        }
    }

    @JvmStatic
    fun prepareRemoteReceiver(context: Context): RemoteScriptsReceiver {
        logSdkVersionInfo()
        return RemoteScriptsReceiver(context)
    }

    @JvmStatic
    private fun logSdkVersionInfo() {
        val versionInfo = StringBuilder()
            .append("versionName=${BuildConfig.VERSION_NAME}, ")
            .append("versionCode=${BuildConfig.VERSION_CODE}, ")
            .append("versionCommit=${BuildConfig.VERSION_COMMIT}, ")
            .append("versionDate=${BuildConfig.VERSION_DATE}, ")
            .append("buildType=${BuildConfig.BUILD_TYPE}, ")
            .toString()
        Log.d(TAG, "sdkVersionInfo: $versionInfo")
    }
}