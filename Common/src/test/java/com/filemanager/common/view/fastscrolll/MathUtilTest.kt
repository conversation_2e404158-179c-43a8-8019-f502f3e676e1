/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.coloros.filemanager.common.view
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/11/01
 * * Author      : whlei
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *     whlei              2022/11/01         1
 ***********************************************************************/
package com.filemanager.common.view.fastscrolll

import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class MathUtilTest {

    @Before
    fun setUp() {
        mockkStatic(MathUtil::class)
    }

    @After
    fun tearDown() {
        unmockkStatic(MathUtil::class)
    }
    @Test
    fun clampTest() {
        Assert.assertEquals(3, MathUtil.clamp(3, 2, 5))
        Assert.assertEquals(2, MathUtil.clamp(1, 2, 5))
        Assert.assertEquals(5, MathUtil.clamp(6, 2, 5))
    }
}