/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentFileCardItemDataPackerTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/19
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/19      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.packer

import com.oplus.filemanager.cardwidget.data.RecentFileCardData
import com.oplus.filemanager.cardwidget.data.RecentFileCardData.Companion.UI_STATE_LOADING_EMPTY
import com.oplus.filemanager.cardwidget.data.RecentFileCardData.Companion.UI_STATE_NO_PRIVACY
import com.oplus.filemanager.cardwidget.data.RecentFileCardData.Companion.UI_STATE_NO_STORAGE_PERMISSION
import com.oplus.filemanager.cardwidget.packer.RecentFileCardItemDataPacker.Companion.LOADING_DRAWABLE_2_TO_2
import com.oplus.filemanager.cardwidget.packer.RecentFileCardItemDataPacker.Companion.LOADING_DRAWABLE_2_TO_4
import com.oplus.filemanager.cardwidget.packer.RecentFileCardItemDataPacker.Companion.LOADING_DRAWABLE_4_TO_4
import com.oplus.filemanager.cardwidget.packer.RecentFileCardItemDataPacker.Companion.NO_DATA_DRAWABLE_2_TO_2
import com.oplus.filemanager.cardwidget.packer.RecentFileCardItemDataPacker.Companion.NO_DATA_DRAWABLE_2_TO_4
import com.oplus.filemanager.cardwidget.packer.RecentFileCardItemDataPacker.Companion.NO_DATA_DRAWABLE_4_TO_4
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_2_COLOROS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_2_ONEPLUS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_4_COLOROS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_4_ONEPLUS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_4_TO_4_COLOROS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_4_TO_4_ONEPLUS
import com.oplus.smartenginehelper.dsl.DSLCoder
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert.assertEquals
import org.junit.Test

class RecentFileCardItemDataPackerTest {

    @Test
    fun getNoDataDrawableTest() {
        val packer = mockk<RecentFileCardItemDataPacker>(relaxed = true, relaxUnitFun = true) {
            every { getNoDataDrawable(any()) } answers { callOriginal() }
        }
        assertEquals(NO_DATA_DRAWABLE_2_TO_2, packer.getNoDataDrawable(CARD_TYPE_2_TO_2_COLOROS))
        assertEquals(NO_DATA_DRAWABLE_2_TO_4, packer.getNoDataDrawable(CARD_TYPE_2_TO_4_COLOROS))
        assertEquals(NO_DATA_DRAWABLE_4_TO_4, packer.getNoDataDrawable(CARD_TYPE_4_TO_4_COLOROS))
        assertEquals(NO_DATA_DRAWABLE_2_TO_2, packer.getNoDataDrawable(CARD_TYPE_2_TO_2_ONEPLUS))
        assertEquals(NO_DATA_DRAWABLE_2_TO_4, packer.getNoDataDrawable(CARD_TYPE_2_TO_4_ONEPLUS))
        assertEquals(NO_DATA_DRAWABLE_4_TO_4, packer.getNoDataDrawable(CARD_TYPE_4_TO_4_ONEPLUS))
        assertEquals(NO_DATA_DRAWABLE_2_TO_2, packer.getNoDataDrawable(-1))
    }

    @Test
    fun getLoadingDrawableTest() {
        val packer = mockk<RecentFileCardItemDataPacker>(relaxed = true, relaxUnitFun = true) {
            every { getLoadingDrawable(any()) } answers { callOriginal() }
        }
        assertEquals(LOADING_DRAWABLE_2_TO_2, packer.getLoadingDrawable(CARD_TYPE_2_TO_2_COLOROS))
        assertEquals(LOADING_DRAWABLE_2_TO_4, packer.getLoadingDrawable(CARD_TYPE_2_TO_4_COLOROS))
        assertEquals(LOADING_DRAWABLE_4_TO_4, packer.getLoadingDrawable(CARD_TYPE_4_TO_4_COLOROS))
        assertEquals(LOADING_DRAWABLE_2_TO_2, packer.getLoadingDrawable(CARD_TYPE_2_TO_2_ONEPLUS))
        assertEquals(LOADING_DRAWABLE_2_TO_4, packer.getLoadingDrawable(CARD_TYPE_2_TO_4_ONEPLUS))
        assertEquals(LOADING_DRAWABLE_4_TO_4, packer.getLoadingDrawable(CARD_TYPE_4_TO_4_ONEPLUS))
        assertEquals(LOADING_DRAWABLE_2_TO_2, packer.getLoadingDrawable(-1))
    }

    @Test
    fun onPackTest() {
        val coder = mockk<DSLCoder>(relaxed = true, relaxUnitFun = true)
        val packer = mockk<RecentFileCardItemDataPacker>(relaxed = true, relaxUnitFun = true) {
            every { onPack(coder) } answers { callOriginal() }
        }
        every { packer.recentFile.uiState } returns UI_STATE_NO_STORAGE_PERMISSION
        assertEquals(true, packer.onPack(coder))
        every { packer.recentFile.uiState } returns UI_STATE_NO_PRIVACY
        assertEquals(true, packer.onPack(coder))
        every { packer.recentFile.uiState } returns UI_STATE_LOADING_EMPTY
        assertEquals(true, packer.onPack(coder))
        every { packer.recentFile.uiState } returns RecentFileCardData.UI_STATE_LOADING_SUCCESS
        assertEquals(true, packer.onPack(coder))
    }

    /*@Test
    fun showNoPermissionOrEmptyDataUiTest() {
        val coder = mockk<DSLCoder>(relaxed = true, relaxUnitFun = true)
        val packer = mockk<RecentFileCardItemDataPacker>(relaxed = true, relaxUnitFun = true) {
            every { onPack(coder) } answers { callOriginal()}
        }
        assertEquals(UI_STATE_NO_STORAGE_PERMISSION, packer.showNoPermissionOrEmptyDataUi(coder, UI_STATE_NO_STORAGE_PERMISSION))
        assertEquals(UI_STATE_NO_PRIVACY, packer.showNoPermissionOrEmptyDataUi(coder, UI_STATE_NO_PRIVACY))
        assertEquals(UI_STATE_LOADING_EMPTY, packer.showNoPermissionOrEmptyDataUi(coder, UI_STATE_LOADING_EMPTY))
    }*/

    @Test
    fun `should return string when getTextSizeLargestToG2String`() {
        val packer = mockk<RecentFileCardItemDataPacker>(relaxed = true, relaxUnitFun = true) {
            every { getTextSizeLargestToG2String(any(), any()) } answers { callOriginal() }
            every { getTextSizeLargest(any(), any(), any()) } answers { callOriginal() }
        }
        val result = packer.getTextSizeLargestToG2String(1, null)
        assertEquals(DEFAULT_SIZE_WHEN_CONTEXT_NULL, result)
    }

    companion object {
        const val DEFAULT_SIZE_WHEN_CONTEXT_NULL = "-1.0px"
    }
}