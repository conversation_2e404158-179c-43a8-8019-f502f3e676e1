package com.coloros.filemanager.utils

import android.content.Context
import com.filemanager.common.utils.IdentifierManager
import com.oplus.stdid.sdk.StdIDSDK
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import com.sdid.id.IdentifierManager as IdManager

class IdentifierManagerTest {
    @MockK
    lateinit var mContext: Context


    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mContext = mockk(relaxed = true)
        mockkStatic(StdIDSDK::class)
        mockkStatic(IdManager::class)
    }

    @After
    fun teardown() {
        unmockkStatic(StdIDSDK::class)
        unmockkStatic(IdManager::class)
    }


    @Test
    fun `should verify when init Sdk without`() {
        IdentifierManager.initSdk(mContext)
        verify { StdIDSDK.init(mContext) }
    }


    @Test
    fun `should verify when init UDID without`() {
        mockkStatic(StdIDSDK::class)
        every { StdIDSDK.isSupported() }.returns(true)
        every { StdIDSDK.getGUID(mContext) }.returns("test")
        assertEquals("test", IdentifierManager.getUDID(mContext))

        every { StdIDSDK.isSupported() }.returns(false)
        every { IdManager.getUDID(any()) }.returns("testUDID")
        assertEquals("testUDID", IdentifierManager.getUDID(mContext))
    }


    @Test
    fun `should verify when get OUID status without`() {
        mockkStatic(StdIDSDK::class)
        every { StdIDSDK.getOUIDStatus(mContext) }.returns(true)
        assertTrue(IdentifierManager.getOUIDStatus(mContext))
    }


    @Test
    fun `should verify when get OUID without`() {
        mockkStatic(StdIDSDK::class)
        every { StdIDSDK.isSupported() }.returns(true)
        every { StdIDSDK.getOUID(mContext) }.returns("test")
        assertEquals("test", IdentifierManager.getOUID(mContext))

        every { StdIDSDK.isSupported() }.returns(false)
        every { IdManager.getOAID(any()) }.returns("TestOAID")
        assertEquals("TestOAID", IdentifierManager.getOUID(mContext))
    }

    @Test
    fun `should verify when get VAID without`() {
        mockkStatic(StdIDSDK::class)
        every { StdIDSDK.isSupported() }.returns(true)
        every { StdIDSDK.getDUID(mContext) }.returns("test")
        assertEquals("test", IdentifierManager.getVAID(mContext))

        every { StdIDSDK.isSupported() }.returns(false)
        every { IdManager.getVAID(any()) }.returns("TestVAID")
        assertEquals("TestVAID", IdentifierManager.getVAID(mContext))
    }

    @Test
    fun `should verify when get AAID without`() {
        mockkStatic(StdIDSDK::class)
        every { StdIDSDK.isSupported() }.returns(true)
        every { StdIDSDK.getAUID(mContext) }.returns("test")
        assertEquals("test", IdentifierManager.getAAID(mContext))

        every { StdIDSDK.isSupported() }.returns(false)
        every { IdManager.getAAID(any()) }.returns("TestAAID")
        assertEquals("TestAAID", IdentifierManager.getAAID(mContext))
    }


    @Test
    fun `should verify when get APID without`() {
        mockkStatic(StdIDSDK::class)
        every { StdIDSDK.isSupported() }.returns(true)
        every { StdIDSDK.getAPID(mContext) }.returns("test")
        assertEquals("test", IdentifierManager.getAPID(mContext))

        every { StdIDSDK.isSupported() }.returns(false)
        assertEquals("NA", IdentifierManager.getAPID(mContext))
    }

    @Test
    fun `should verify when clear SDK without`() {
        mockkStatic(StdIDSDK::class)
        IdentifierManager.clearSdk(mContext)
        verify { StdIDSDK.clear(mContext) }
    }
}