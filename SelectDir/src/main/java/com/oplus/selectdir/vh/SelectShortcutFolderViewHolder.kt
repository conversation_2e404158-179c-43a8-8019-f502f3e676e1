/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: SelectShortcutFolderViewHolder
 * * Description: SelectShortcutFolderViewHolder
 * * Version: 1.0
 * * Date : 2024/10/24
 * * Author:chao.xue
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     chao.xue        2024/10/24      1.0            create
 ****************************************************************/
package com.oplus.selectdir.vh

import android.app.Activity
import android.view.MotionEvent
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.LayoutRes
import androidx.core.view.isVisible
import androidx.recyclerview.widget.COUIRecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionViewHolder
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.TextViewSnippet
import com.oplus.selectdir.R
import com.oplus.selectdir.SelectDirViewModel

class SelectShortcutFolderViewHolder(convertView: View) : BaseSelectionViewHolder(convertView),
    COUIRecyclerView.ICOUIDividerDecorationInterface {

    companion object {
        private const val TAG = "SelectShortcutFolderViewHolder"

        /**
         * this name is set for hidden file alpha
         */
        private const val TEST_HIDDEN_FILENAME = ".test"

        @LayoutRes
        fun layoutId(): Int {
            return R.layout.selectdir_path_recycler_item_shortcut_folder
        }
    }

    private val mImgRadius = MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.file_list_bg_radius)
    private val mNormalTitleMaxSize by lazy {
        appContext.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.file_list_item_info_selected_width_new)
    }
    private val mFolderTitleMaxSize by lazy {
        val context = convertView.context
        if (context is Activity) {
            KtViewUtils.getWindowSize(context).x - context.getResources()
                .getDimensionPixelOffset(com.filemanager.common.R.dimen.file_list_adapter_folder_max_size)
        } else {
            mNormalTitleMaxSize
        }
    }

    var mImg: FileThumbView? = null
    var mTitle: TextViewSnippet? = null
    var mDetail: TextView? = null
    var mAnotherName: TextViewSnippet? = null
    private var addFlagTv: TextView? = null
    private var checkBoxLayout: LinearLayout? = null


    init {
        mImg = convertView.findViewById(R.id.file_list_item_icon)
        mTitle = convertView.findViewById(R.id.file_list_item_title)
        mDetail = convertView.findViewById(R.id.mark_file_list_item_detail)
        mAnotherName = convertView.findViewById(R.id.another_name_view)
        addFlagTv = convertView.findViewById(R.id.add_flag_tv)
        mCheckBox = convertView.findViewById(R.id.listview_scrollchoice_checkbox)
        checkBoxLayout = convertView.findViewById(R.id.checkbox_layout)
    }

    fun bindData(
        file: BaseFileBean?,
        key: Int?,
        position: Int,
        totalCount: Int,
        isDarkModel: Boolean,
        mViewModel: SelectDirViewModel?
    ) {
        if (file?.mData == null) {
            Log.w(TAG, "bindData() file or path is null")
            return
        }
        updateKey(key)
        setAlpha(file, isDarkModel)
        showFileIcon(file)
        showTitle(file)
        showAnotherName(file)
        showAddedFlag(file)
        updateDividerVisible(totalCount - 1, position)
        checkBoxLayout?.setOnClickListener {  }
        mCheckBox?.let { checkbox ->
            checkbox.setOnClickListener {
                Log.d(TAG, "setOnClickListener checkBox isChecked ${checkbox.isChecked} key $key")
                key?.let {
                    if (checkbox.isChecked) {
                        mViewModel?.selectItems(listOf(key))
                    } else {
                        mViewModel?.deselectItems(listOf(key))
                    }
                }
            }
        }
    }


    private fun showFileIcon(file: BaseFileBean) {
        mImg?.let {
            if (file.mLocalType == MimeTypeHelper.IMAGE_TYPE || file.mLocalType == MimeTypeHelper.VIDEO_TYPE) {
                it.setStrokeStyle(FileThumbView.STROKE_4DP)
            } else {
                it.setStrokeStyle(FileThumbView.STROKE_NONE)
            }
            val borderSize = when (file.mLocalType) {
                MimeTypeHelper.IMAGE_TYPE, MimeTypeHelper.VIDEO_TYPE -> KtViewUtils.getBorderSize()
                else -> 0F
            }
            it.setBorderStyle(mImgRadius.toFloat(), borderSize)
            FileImageLoader.sInstance.clear(it.context, it)
            FileImageLoader.sInstance.displayDefault(file, it, 0, mImgRadius)
        }
    }

    private fun showTitle(file: BaseFileBean) {
        var titleMaxWidth = mNormalTitleMaxSize
        if ((file.mLocalType == MimeTypeHelper.DIRECTORY_TYPE) && (mFolderTitleMaxSize > 0)) {
            titleMaxWidth = mFolderTitleMaxSize
        }
        mTitle?.tag = file.mData
        mTitle?.text = file.mDisplayName
        mTitle?.maxWidth = titleMaxWidth
    }

    private fun showAnotherName(file: BaseFileBean) {
        mAnotherName?.visibility = View.GONE
    }

    private fun setAlpha(file: BaseFileBean, isDarkModel: Boolean) {
        val alpha = if (file.mLocalType == MimeTypeHelper.DIRECTORY_TYPE) {
            itemView.isEnabled = true
            mCheckBox?.isEnabled = true
            HiddenFileHelper.getAlphaWithHidden(file.mDisplayName, isDarkModel)
        } else {
            itemView.isEnabled = false
            mCheckBox?.isEnabled = false
            HiddenFileHelper.getAlphaWithHidden(TEST_HIDDEN_FILENAME, isDarkModel)
        }
        mTitle?.alpha = alpha
        mDetail?.alpha = alpha
        mImg?.alpha = alpha
        mAnotherName?.alpha = alpha
    }


    private fun showAddedFlag(file: BaseFileBean) {
        addFlagTv?.isVisible = file.mHasLabel
        mCheckBox?.isClickable = !file.mHasLabel
        mCheckBox?.isEnabled = !file.mHasLabel
    }

    override fun drawDivider(): Boolean {
        return true
    }

    override fun getDividerStartAlignView(): View {
        return mTitle as View
    }

    override fun getDividerEndInset(): Int {
        return MyApplication.appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp)
    }

    override fun isInClickArea(event: MotionEvent): Boolean {
        return isInSelectRegion(event) && mCheckBox?.isEnabled ?: false
    }
}