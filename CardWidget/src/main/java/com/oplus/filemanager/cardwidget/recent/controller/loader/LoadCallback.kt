/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LoadCallback
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/13 17:50
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/13       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.controller.loader

import com.filemanager.common.base.BaseFileBean

interface LoadCallback {
    /**
     * load success callback.
     */
    fun loadSuccess(result: List<BaseFileBean>)

    /**
     * load failure callback.
     */
    fun loadFailure()
}