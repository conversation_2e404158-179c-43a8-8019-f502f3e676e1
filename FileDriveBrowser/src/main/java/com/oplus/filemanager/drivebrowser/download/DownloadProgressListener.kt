/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DownloadProgressListener
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/9 19:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/9       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.download

import com.oplus.filemanager.drivebrowser.data.repository.DocumentDownloadErrorCode

interface DownloadProgressListener {
    fun onProgressChange(progress: Int)
    fun onStop()
    fun onError(error: DocumentDownloadErrorCode)
}