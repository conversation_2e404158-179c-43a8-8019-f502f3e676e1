/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SuperAppActivityViewModelTest
 * * Description : SuperAppActivityViewModel Unit Test
 * * Version     : 1.0
 * * Date        : 2022/12/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.superapp.ui.superapp

import android.content.Context
import androidx.arch.core.executor.ArchTaskExecutor
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.StatisticsUtils
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SuperAppActivityViewModelTest {

    private lateinit var viewModel: SuperAppParentFragmentViewModel

    @Before
    fun setup() {
        viewModel = mockk<SuperAppParentFragmentViewModel>().apply {
            every { mLoaderController = any() }.answers { callOriginal() }
            every { mLoaderController }.answers { callOriginal() }
            every { mSuperAppPackage }.returns("")
        }
        mockkObject(ConfigSharedPreferenceUtils)
        mockkStatic(StatisticsUtils::class)
        mockkStatic(ArchTaskExecutor::class)
        val executor = mockk<ArchTaskExecutor>()
        every { ArchTaskExecutor.getInstance() }.returns(executor)
        every { executor.isMainThread }.returns(true)
    }

    @After
    fun teardown() {
        unmockkObject(ConfigSharedPreferenceUtils)
        unmockkStatic(StatisticsUtils::class)
        unmockkStatic(ArchTaskExecutor::class)
    }

    @Test
    fun should_return_int_when_getScanMode() {
        every { viewModel.getScanMode() }.answers { callOriginal() }
        every { ConfigSharedPreferenceUtils.getInt(any(), any()) }.returns(0)
        var scanMode = viewModel.getScanMode()
        Assert.assertEquals(KtConstants.SCAN_MODE_GRID, scanMode)

        every { ConfigSharedPreferenceUtils.getInt(any(), any()) }.returns(10)
        scanMode = viewModel.getScanMode()
        Assert.assertEquals(10, scanMode)
    }

    @Test
    fun should_when_clickScanModeItem() {
        val context = mockk<Context>()
        every { viewModel.clickScanModeItem(any()) }.answers { callOriginal() }
        val modeState = MutableLiveData<Int>()
        every { viewModel.mBrowseModeState }.returns(modeState)
        justRun { StatisticsUtils.onCommon(any(), any(), any<Map<String, String>>()) }
        justRun { ConfigSharedPreferenceUtils.putInt(any(), any()) }

        modeState.value = KtConstants.SCAN_MODE_LIST
        viewModel.clickScanModeItem(context)
        verify {
            StatisticsUtils.onCommon(
                any(),
                StatisticsUtils.SCAN_MODE_OTHER_SWITCH,
                hashMapOf(StatisticsUtils.SCAN_MODE_OTHER_SWITCH to "0")
            )
        }
        verify { ConfigSharedPreferenceUtils.putInt(any(), KtConstants.SCAN_MODE_GRID) }

        viewModel.clickScanModeItem(context)
        verify {
            StatisticsUtils.onCommon(
                any(),
                StatisticsUtils.SCAN_MODE_OTHER_SWITCH,
                hashMapOf(StatisticsUtils.SCAN_MODE_OTHER_SWITCH to "1")
            )
        }
        verify { ConfigSharedPreferenceUtils.putInt(any(), KtConstants.SCAN_MODE_LIST) }
    }

    @Test
    fun should_when_onCleared() {
        every { viewModel.onCleared() }.answers { callOriginal() }
        val controller = mockk<LoaderController>()
        justRun { controller.onDestroy() }
        every { viewModel.mLoaderController }.returns(null)

        viewModel.onCleared()
        verify(inverse = true) { controller.onDestroy() }

        every { viewModel.mLoaderController }.returns(controller)
        viewModel.onCleared()
        verify { controller.onDestroy() }
    }

    @Test
    fun should_when_refreshBrowseModeState() {
        every { viewModel.refreshBrowseModeState() }.answers { callOriginal() }
        every { viewModel.getScanMode() }.returns(KtConstants.SCAN_MODE_LIST)
        val modeState = MutableLiveData<Int>()
        every { viewModel.mBrowseModeState }.returns(modeState)

        viewModel.refreshBrowseModeState()
        Assert.assertEquals(viewModel.mBrowseModeState.value, KtConstants.SCAN_MODE_LIST)
    }
}