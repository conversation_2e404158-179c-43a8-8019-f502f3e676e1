package com.oplus.filemanager.preview.core

import android.app.Activity
import android.content.ComponentName
import android.content.Intent
import android.view.View
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.detail.FileDetailDialogAdapter
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.main.IMain
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * PreviewOnClickFilePathImpl 的单元测试类
 * 用于测试文件预览页面点击文件路径跳转功能的实现逻辑
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class PreviewOnClickFilePathImplTest {

    // 模拟的Activity对象
    private val mockActivity: Activity = mockk(relaxed = true)
    // 模拟的View对象
    private val mockView: View = mockk(relaxed = true)
    // 模拟的主界面操作接口
    private val mockMainAction: IMain = mockk(relaxed = true)
    // 模拟的文件浏览器接口
    private val mockFileBrowser: IFileBrowser = mockk(relaxed = true)
    // 模拟的组件名称
    private val mockComponentName: ComponentName = mockk(relaxed = true)

    // 被测试的对象
    private lateinit var previewOnClick: PreviewOnClickFilePathImpl

    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 清除所有模拟对象
     * 3. 创建被测试对象实例
     * 4. 模拟静态方法和对象
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        clearAllMocks()
        previewOnClick = PreviewOnClickFilePathImpl(mockActivity)

        // 模拟Injector对象
        mockkObject(Injector)
        // 模拟Log类的静态方法
        mockkStatic(Log::class)
        // 模拟VolumeEnvironment类的静态方法
        mockkStatic(VolumeEnvironment::class)
        // 模拟Intent类的静态方法
        mockkStatic(Intent::class)
    }

    /**
     * 测试后的清理方法
     * 1. 解除所有模拟
     * 2. 清除所有模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
        clearAllMocks()
    }

    /**
     * 测试当传入null路径时的处理逻辑
     * 预期结果：不执行任何操作
     */
    @Test
    fun `test onClickFilePath with null path should do nothing`() {
        // 模拟Activity返回组件名称
        every { mockActivity.componentName } returns mockComponentName
        
        // 调用被测试方法，传入null路径
        previewOnClick.onClickFilePath(mockView, null)
        
        // 验证：确保没有调用startActivity和Log.d方法
        verify(exactly = 0) { 
            mockActivity.startActivity(any())
            Log.d(any(), any())
        }
        // 确认所有验证已完成
        confirmVerified(mockActivity)
    }
}