/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:/RecycleBinUtils.kt
 * * Description:
 * * Version:1.0
 * * Date :2020/3/4
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * * ZejiangDuan, 2020/3/4, v1.0,
 ****************************************************************/
package com.filemanager.recyclebin.utils

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import androidx.activity.ComponentActivity
import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread
import com.coui.appcompat.contextutil.COUIContextUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.fileutils.JavaFileHelper.fileTotalSize
import com.filemanager.common.helper.CategoryHelper.CATEGORY_APK
import com.filemanager.common.helper.CategoryHelper.CATEGORY_AUDIO
import com.filemanager.common.helper.CategoryHelper.CATEGORY_COMPRESS
import com.filemanager.common.helper.CategoryHelper.CATEGORY_DOC
import com.filemanager.common.helper.CategoryHelper.CATEGORY_IMAGE
import com.filemanager.common.helper.CategoryHelper.CATEGORY_RECENT
import com.filemanager.common.helper.CategoryHelper.CATEGORY_SEARCH
import com.filemanager.common.helper.CategoryHelper.CATEGORY_VIDEO
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.RecycleFileWrapper
import com.filemanager.recyclebin.operation.BaseOperation
import com.filemanager.recyclebin.operation.DeleteOperation
import com.filemanager.recyclebin.operation.EraseOperation
import com.filemanager.recyclebin.operation.RecycleBinOperationListener
import com.filemanager.recyclebin.operation.RecycleOperation
import com.filemanager.recyclebin.operation.listener.DeleteOperationListener
import com.filemanager.recyclebin.operation.listener.EraseOperationListener
import com.filemanager.recyclebin.operation.listener.RecycleOperationListener
import com.oplus.filemanager.dfm.DFMManager
import java.io.File
import kotlin.math.abs


object RecycleBinUtils {
    const val SHARED_PREFS_NAME = "com.oppo.filemanager_preferences"
    private const val TAG = "RecycleBinUtils"
    private const val RECYCLE_TOTAL_SIZE_RECORD = "recycle_bin_size"
    private const val RECYCLE_MAX = 30
    private const val TWO_SPACE = "  "
    private const val RTL_CHARACTER_PREVIOUS = "\u200F"
    private const val DIVIDER_WITH_SPACE_SIDE = " ｜ "
    private const val ONE_SPACE = " "

    /**
     * get parameter from sharePreferences
     */
    fun getSharedRecycleBinSize(): Long {
        return PreferencesUtils.getLong(SHARED_PREFS_NAME, RECYCLE_TOTAL_SIZE_RECORD, 0L)
    }

    fun saveSharedRecycleBinSize(size: Long) {
        PreferencesUtils.put(SHARED_PREFS_NAME, RECYCLE_TOTAL_SIZE_RECORD, size)
    }

    /**
     * @param time1 milliseconds
     * @param time2 milliseconds
     * @return
     */
    fun getRecycleTimeLeftString(time1: Long, time2: Long): String {
        val diffDays = calculateDiffDaysBetweenTwoDate(time1, time2)
        val timeLeft = if (diffDays > RECYCLE_MAX) 0 else RECYCLE_MAX - diffDays
        val res = MyApplication.sAppContext.resources
        return res.getQuantityString(com.filemanager.common.R.plurals.tip_recycle_bin_will_be_deleted_after_x_days, timeLeft, timeLeft)
    }

    private fun calculateDiffDaysBetweenTwoDate(time1: Long, time2: Long): Int {
        return abs((time1 - time2) / (1000 * 3600 * 24)).toInt()
    }

    @JvmStatic
    fun formatRecycleDetail(context: Context, detailString: String?, dateAndTime: String?, timeLeftString: String): CharSequence {
        if ((dateAndTime == null) || (detailString == null)) {
            return ""
        }
        val formatDetailString = Utils.formatMessage(detailString, Utils.RTL_POSITION_DOUBLE)
        val formatTimeLeftString = Utils.formatMessage(timeLeftString, Utils.RTL_POSITION_DOUBLE)
        var detail = "$formatTimeLeftString$DIVIDER_WITH_SPACE_SIDE$formatDetailString$DIVIDER_WITH_SPACE_SIDE$dateAndTime"
        if (Utils.isRtl()) {
            detail = "\u200F$formatTimeLeftString$DIVIDER_WITH_SPACE_SIDE\u200F$formatDetailString$DIVIDER_WITH_SPACE_SIDE\u200F$dateAndTime"
        }
        val spanString = constructorSpannableString(detail)
        //这里context需要是activity的context，如果用application的context获取出来的色值会为0
        val colorCharacter = COUIContextUtil.getAttrColor(
            context,
            com.support.appcompat.R.attr.couiColorLabelTertiary
        )
        val colorSpan = ForegroundColorSpan(colorCharacter)
        var index = formatTimeLeftString.length + ONE_SPACE.length
        if (Utils.isRtl()) {
            index += RTL_CHARACTER_PREVIOUS.length
        }
        val colorSpanSecond = ForegroundColorSpan(colorCharacter)
        var indexSecond = detail.length - dateAndTime.length - ONE_SPACE.length - 1
        if (Utils.isRtl()) {
            indexSecond -= RTL_CHARACTER_PREVIOUS.length
        }
        spanString.setSpan(colorSpan, index, index + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        spanString.setSpan(colorSpanSecond, indexSecond, indexSecond + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        return spanString
    }

    @JvmStatic
    fun formatRecycleDetail(context: Context, detail: List<String>): CharSequence {
        val isRtl = Utils.isRtl()
        val size = detail.size
        if (size <= 0) {
            return ""
        }
        if (size == 1) {
            return detail.get(0)
        }
        val builder = StringBuffer()
        val linePos = mutableListOf<Int>()
        val rtlLen = RTL_CHARACTER_PREVIOUS.length
        val oneSpaceLen = ONE_SPACE.length
        val dividerSpaceLen = DIVIDER_WITH_SPACE_SIDE.length
        var lastItemLen = 0
        detail.forEachIndexed { index, item ->
            var len = 0
            if (isRtl) {
                builder.append(RTL_CHARACTER_PREVIOUS)
                len += rtlLen
            }
            builder.append(item)
            len += item.length
            if (index < size - 1) {
                builder.append(DIVIDER_WITH_SPACE_SIDE)
                len += oneSpaceLen
                linePos.add(len + lastItemLen)
            }
            lastItemLen += len - oneSpaceLen + dividerSpaceLen
        }
        val detailText = builder.toString()
        val spanString = constructorSpannableString(detailText)
        //这里context需要是activity的context，如果用application的context获取出来的色值会为0
        val colorCharacter = COUIContextUtil.getAttrColor(
            context,
            com.support.appcompat.R.attr.couiColorLabelTertiary
        )
        linePos.forEach { index ->
            val colorSpan = ForegroundColorSpan(colorCharacter)
            spanString.setSpan(colorSpan, index, index + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        return spanString
    }

    @Suppress("UtilMustStaticRule")
    @VisibleForTesting
    fun constructorSpannableString(detail: String) = SpannableString(detail)

    @WorkerThread
    fun formatRecycleSize(file: RecycleFileWrapper?): String {
        var formatSize = ""
        if (file == null) {
            Log.w(TAG, "file is null")
        } else {
            formatSize = Utils.byteCountToDisplaySize(fileTotalSize(file))
        }
        return Utils.formatMessage(formatSize, Utils.RTL_POSITION_DOUBLE)
    }

    /**
     * Get folder and child files size. or get file self Size
     *
     * Please execute this method in a worker thread
     *
     * @param file File
     * @return long
     */
    @WorkerThread
    fun getFileOrFolderSize(file: File?): Long {
        var size: Long = 0
        if (file == null) {
            return size
        }
        try {
            if (file.isDirectory) {
                //count folder size
                size += file.length()
                val fileList = JavaFileHelper.listFiles(file)
                fileList?.let {
                    for (i in fileList.indices) {
                        if (Thread.currentThread().isInterrupted) {
                            return size
                        }
                        size += if (fileList[i].isDirectory) {
                            getFileOrFolderSize(fileList[i])
                        } else {
                            fileList[i].length()
                        }
                    }
                }
            } else {
                size += file.length()
            }
        } catch (e: Exception) {
            Log.e(TAG, "getFileOrFolderSize ${e.message}")
        }

        return size
    }

    @JvmStatic
    fun innerDelete(context: ComponentActivity, parameters: BaseOperation.OperationParameters?,
                    listener: RecycleBinOperationListener?, isMediaType: Boolean) {
        parameters?.let {
            val operation = DeleteOperation(context, DeleteOperationListener(context, listener), isMediaType)
            if (it.mUris != null) {
                if (operation.addUris(it.mUris)) {
                    operation.execute()
                }
            } else if (it.mPaths != null) {
                if (operation.addPaths(it.mPaths)) {
                    operation.execute()
                }
            } else if (it.mBaseFiles != null) {
                if (operation.addFileBeans(it.mBaseFiles)) {
                    operation.execute()
                }
            } else {
                Log.w(TAG, "innerDelete invalid parameters")
            }
        }
    }

    @JvmStatic
    fun innerRecycle(
        context: ComponentActivity,
        parameters: BaseOperation.OperationParameters?,
        listener: RecycleBinOperationListener?,
        isMediaType: Boolean,
        isSelectAll: Boolean = false,
        showProgress: Boolean = true
    ) {
        parameters?.let {
            val operation = RecycleOperation(context, RecycleOperationListener(context, listener, isSelectAll, showProgress), isMediaType)
            if (it.mUris != null) {
                if (operation.addUris(it.mUris)) {
                    operation.execute()
                }
            } else if (it.mPaths != null) {
                if (operation.addPaths(it.mPaths)) {
                    operation.execute()
                }
            } else if (it.mBaseFiles != null) {
                if (operation.addFileBeans(it.mBaseFiles)) {
                    operation.execute()
                }
            } else {
                Log.w(TAG, "innerRecycle invalid parameters")
            }
        }
    }

    fun innerEraseRecycle(context: ComponentActivity, parameters: BaseOperation.OperationParameters?,
                          listener: RecycleBinOperationListener?) {
        parameters?.let {
            val operation = EraseOperation(context, EraseOperationListener(context, listener))
            if (parameters.mUris != null) {
                if (operation.addUris(parameters.mUris)) {
                    operation.execute()
                }
            } else if (parameters.mBaseFiles != null) {
                if (operation.addFileBeans(parameters.mBaseFiles)) {
                    operation.execute()
                }
            } else {
                Log.w(TAG, "innerEraseRecycle invalid parameters")
            }
        }
    }

    fun makeFileCountStr(
        fileCount: Int,
        deleteForever: Boolean,
        hasExternal: Boolean,
        isSelectedAll: Boolean
    ): String {
        if (fileCount <= 0) {
            return ""
        }
        if (deleteForever || hasExternal) {
            //删除外部文件时是彻底删除
            return if (isSelectedAll) {
                MyApplication.sAppContext.getString(com.filemanager.common.R.string.delete_all_forever_tip)
            } else {
                if (fileCount == 1) {
                    MyApplication.sAppContext.getString(com.filemanager.common.R.string.delete_one_forever_tip)
                } else {
                    MyApplication.sAppContext.resources.getQuantityString(
                        com.filemanager.common.R.plurals.delete_forever_tip_new,
                        fileCount,
                        fileCount
                    )
                }
            }
        } else {
            return if (isSelectedAll) {
                MyApplication.sAppContext.getString(com.filemanager.common.R.string.move_all_to_recycle_tip)
            } else {
                if (fileCount == 1) {
                    MyApplication.sAppContext.getString(com.filemanager.common.R.string.move_one_to_recycle_tip)
                } else {
                    MyApplication.sAppContext.resources.getQuantityString(
                        com.filemanager.common.R.plurals.move_to_recycle_tip_new,
                        fileCount,
                        fileCount
                    )
                }
            }
        }
    }

    @SuppressLint("ResourceType")
    @VisibleForTesting
    @JvmStatic
    fun getForeverDeleteMessage(fileCount: Int, mIsSelectedAll: Boolean): String {
        return if (fileCount == 1) {
            MyApplication.appContext.getString(com.filemanager.common.R.string.memory_is_full_message_delete_one)
        } else if (mIsSelectedAll) {
            MyApplication.appContext.getString(com.filemanager.common.R.string.memory_is_full_message_delete_all)
        } else {
            MyApplication.appContext.resources.getQuantityString(
                com.filemanager.common.R.plurals.memory_is_full_message_delete_more_one,
                fileCount,
                fileCount
            )
        }
    }

    @JvmStatic
    fun makeDfmCountStr(fileCount: Int, isSelectedAll: Boolean): String {
        if (fileCount <= 0) {
            return ""
        }
        val deviceName = DFMManager.getDFSDeviceName() ?: KtConstants.DFM_MOUNT_PATH_SUFFIX
        return if (isSelectedAll) {
            MyApplication.sAppContext.getString(com.filemanager.common.R.string.delete_all_file_msg, deviceName)
        } else {
            if (fileCount == 1) {
                MyApplication.sAppContext.getString(com.filemanager.common.R.string.delete_one_file_msg, deviceName)
            } else {
                MyApplication.sAppContext.resources.getQuantityString(
                    com.filemanager.common.R.plurals.delete_some_file_msg,
                    fileCount,
                    fileCount,
                    deviceName
                )
            }
        }
    }

    @JvmStatic
    fun getDeleteDialogTitle(fileCount: Int, isSelectedAll: Boolean): String {
        return if (isSelectedAll) {
            return MyApplication.sAppContext.getString(com.filemanager.common.R.string.delete_all_title)
        } else if (fileCount == 1) {
            return MyApplication.sAppContext.getString(com.filemanager.common.R.string.delete_one_title)
        } else {
            MyApplication.sAppContext.resources.getQuantityString(
                com.filemanager.common.R.plurals.delete_item_title,
                fileCount,
                fileCount
            )
        }
    }

    fun getDelButtonStr(selectAll: Boolean): String {
        return if (selectAll) {
            MyApplication.sAppContext.getString(com.filemanager.common.R.string.delete_all)
        } else {
            MyApplication.sAppContext.getString(com.filemanager.common.R.string.menu_file_list_delete)
        }
    }

    /**
     * delete target items in MediaStore if return true, or send broadcast for MediaStore to scan target file
     * @param category category type
     * @return true if filebean query from MediaStore
     */
    fun isMediaTypeCategory(category: Int): Boolean {
        return when (category) {
            CATEGORY_IMAGE, CATEGORY_VIDEO, CATEGORY_AUDIO,
            CATEGORY_DOC, CATEGORY_APK, CATEGORY_COMPRESS,
            CATEGORY_SEARCH, CATEGORY_RECENT -> true
            else -> false
        }
    }

    /**
     * 从不同数组(paths: ArrayList<String>,uris: ArrayList<Uri>，files: ArrayList<BaseFileBean>)中提取文件路径到allFilePath集合中
     */
    @JvmStatic
    fun storeFilesForDeleteLabels(
        allFilePath: ArrayList<String>?,
        paths: ArrayList<String>?,
        uris: ArrayList<Uri>?,
        files: ArrayList<BaseFileBean>?
    ) {
        if (allFilePath == null) return
        allFilePath.clear()
        when {
            paths != null -> {
                paths.forEach {
                    allFilePath.add(it)
                }
            }
            uris != null -> {
                uris.forEach {
                    allFilePath.add(it.path!!)
                }
            }
            else -> {
                files?.forEach {
                    allFilePath.add(it.mData!!)
                }
            }
        }
    }
}