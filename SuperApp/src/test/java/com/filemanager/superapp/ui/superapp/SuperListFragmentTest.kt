/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SuperListFragmentTest
 * * Description : SuperListFragment Unit Test
 * * Version     : 1.0
 * * Date        : 2023/1/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.superapp.ui.superapp

import io.mockk.every
import io.mockk.mockk
import org.junit.Test

class SuperListFragmentTest {

    @Test
    fun should_call_getViewModelStore_when_getParentViewModel() {
        val superListFragment = mockk<SuperListFragment>(relaxed = true)
        val superAppParentFragmentViewModel = mockk<SuperAppParentFragmentViewModel>(relaxed = true)
        every { superListFragment.getParentViewModel() }.answers { superAppParentFragmentViewModel }
        val viewModel = superListFragment.getParentViewModel()
        assert(viewModel != null)
        println(viewModel)
    }
}