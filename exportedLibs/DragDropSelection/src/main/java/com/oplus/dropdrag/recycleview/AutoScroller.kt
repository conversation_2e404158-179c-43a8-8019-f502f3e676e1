/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - AutoScroller.java
 ** Description: Auto Scroller
 ** Version: 1.0
 ** Date : 2020/05/15
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/15    1.0     create
 ****************************************************************/

package com.oplus.dropdrag.recycleview

import android.graphics.Point
import android.view.View
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView
import com.oplus.dropdrag.SelectionTracker.Companion.DEBUG
import com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE
import com.oplus.dropdrag.utils.Log
import kotlin.math.abs
import kotlin.math.min
import kotlin.math.pow
import kotlin.math.sign

private const val TAG = "ViewAutoScroller"

internal abstract class AutoScroller(
    protected val mLayoutType: LAYOUT_TYPE = LAYOUT_TYPE.LIST,
    protected val mScrollThresholdRatio: Float = DEFAULT_SCROLL_THRESHOLD_RATIO
) {
    companion object {
        // ratio used to calculate the top/bottom hotspot region; used with view height
        const val DEFAULT_SCROLL_THRESHOLD_RATIO: Float = 0.125f
        internal fun createAutoScroll(
            host: ScrollHost,
            layoutType: LAYOUT_TYPE,
            scrollThresholdRatio: Float = DEFAULT_SCROLL_THRESHOLD_RATIO
        ): AutoScroller {
            return if (layoutType == LAYOUT_TYPE.GRID) {
                GridAutoScroller(host, layoutType, scrollThresholdRatio)
            } else {
                ListAutoScroller(host, layoutType, scrollThresholdRatio)
            }
        }
    }

    /**
     * Resets state of the scroller. Call this when the user activity that is driving
     * auto-scrolling is done.
     */
    abstract fun reset()

    /**
     * Processes a new input location.
     * @param location
     */
    abstract fun scroll(location: Point)

    /**
     * stop scroll.
     */
    abstract fun stopScroll()
}

/**
 * Used by to calculate the proper amount of pixels to scroll given time passed
 * since scroll started, and to properly scroll / proper listener clean up if necessary.
 *
 * Callback used by scroller to perform UI tasks, such as scrolling and rerunning at next UI
 * cycle.
 */
internal abstract class ScrollHost {
    companion object {
        fun createScrollHost(recyclerView: RecyclerView): ScrollHost {
            return RuntimeHost(recyclerView)
        }
    }

    /**
     * @return height of the view.
     */
    abstract val viewHeight: Int

    /**
     * @return top location of the view.
     */
    abstract val viewTop: Int

    /**
     * @return bottom location of the view.
     */
    abstract val viewBottom: Int

    /**
     * @return height of specify position item view.
     */
    abstract fun getItemHeight(point: Point?): Int

    /**
     * @param dy distance to scroll.
     */
    abstract fun scrollBy(dy: Int)

    /**
     * @param r schedule runnable to be run at next convenient time.
     */
    abstract fun runAtNextFrame(r: Runnable)

    /**
     * @param r remove runnable from being run.
     */
    abstract fun removeCallback(r: Runnable)
}

/**
 * Tracks location of last surface contact as reported by RecyclerView.
 */
internal class RuntimeHost internal constructor(private val mRecyclerView: RecyclerView) :
    ScrollHost() {
    private var mItemViewHeight = -1

    override fun runAtNextFrame(r: Runnable) {
        ViewCompat.postOnAnimation(mRecyclerView, r)
    }

    override fun removeCallback(r: Runnable) {
        mRecyclerView.removeCallbacks(r)
    }

    override fun scrollBy(dy: Int) {
        if (DEBUG) {
            Log.v(TAG, "Scrolling view by: $dy")
        }

        mRecyclerView.scrollBy(0, dy)
    }

    override val viewHeight: Int
        get() = mRecyclerView.height

    override val viewTop: Int
        get() = mRecyclerView.top

    override val viewBottom: Int
        get() = mRecyclerView.bottom

    override fun getItemHeight(point: Point?): Int {
        if (mItemViewHeight < 0) {
            if (mRecyclerView.childCount > 0) {
                var view: View? = mRecyclerView.getChildAt(0)
                if (view == null) {
                    view = mRecyclerView.getChildAt(mRecyclerView.childCount - 1)
                }

                if (view == null) {
                    point?.let {
                        view = mRecyclerView.findChildViewUnder(it.x.toFloat(), it.y.toFloat())
                    }
                }

                mItemViewHeight = view?.height ?: -1
            }
        }
        return mItemViewHeight
    }
}

internal class ListAutoScroller(
    private val mHost: ScrollHost,
    layoutType: LAYOUT_TYPE,
    scrollThresholdRatio: Float
) : AutoScroller(layoutType, scrollThresholdRatio) {

    private val mRunner: Runnable
    private var mOrigin: Point? = null
    private var mLastLocation: Point? = null

    private var mPassedInitialMotionThreshold = false

    init {
        mRunner = Runnable { runScroll() }
    }

    override fun reset() {
        mHost.removeCallback(mRunner)
        mOrigin = null
        mLastLocation = null
        mPassedInitialMotionThreshold = false
    }

    override fun scroll(location: Point) {
        mLastLocation = location

        // See #aboveMotionThreshold for details on how we track initial location.
        if (mOrigin == null) {
            mOrigin = location
            if (DEBUG) {
                Log.v(TAG, "Origin @ $mOrigin")
            }
        }

        if (DEBUG) {
            Log.v(TAG, "Current location @ $mLastLocation")
        }
        mHost.runAtNextFrame(mRunner)
    }

    override fun stopScroll() {
        mHost.removeCallback(mRunner)
    }

    private fun runScroll() {
        val lastLocation = mLastLocation ?: return
        if (DEBUG) {
            Log.v(TAG, "runScroll in background using event location @ $mLastLocation")
        }

        // Compute the number of pixels the pointer's y-coordinate is past the view.
        // Negative values mean the pointer is at or before the top of the view, and
        // positive values mean that the pointer is at or after the bottom of the view. Note
        // that top/bottom threshold is added here so that the view still scrolls when the
        // pointer are in these buffer pixels.
        val verticalThreshold = (mHost.viewHeight * mScrollThresholdRatio).toInt()

        val pixelsPastView = when {
            lastLocation.y <= verticalThreshold -> {
                lastLocation.y - verticalThreshold
            }
            lastLocation.y >= (mHost.viewHeight - verticalThreshold) -> {
                lastLocation.y - mHost.viewHeight + verticalThreshold
            }
            else -> {
                0
            }
        }

        if (DEBUG) {
            Log.v(TAG, "runScroll in pixelsPastView pixelsPastView=${pixelsPastView}, eventY=${lastLocation.y}")
        }

        if (pixelsPastView == 0) {
            // If the operation that started the scrolling is no longer inactive, or if it is active
            // but not at the edge of the recyclerview, no scrolling is necessary.
            return
        }

        // We're in one of the endzones. Now determine if there's enough of a difference
        // from the orgin to take any action. Basically if a user has somehow initiated
        // selection, but is hovering at or near their initial contact point, we don't
        // scroll. This avoids a situation where the user initiates selection in an "endzone"
        // only to have scrolling start automatically.
        if (!mPassedInitialMotionThreshold && !aboveMotionThreshold(lastLocation)) {
            if (DEBUG) {
                Log.v(TAG, "runScroll Ignoring event below motion threshold.")
            }
            return
        }
        mPassedInitialMotionThreshold = true

        val numPixels = computeScrollDistance(pixelsPastView, mLastLocation)

        if (DEBUG) {
            Log.v(TAG, "runScroll numPixels $numPixels")
        }

        mHost.scrollBy(numPixels)
    }

    private fun aboveMotionThreshold(location: Point): Boolean {
        // We reuse the scroll threshold to calculate a much smaller area
        // in which we ignore motion initially.
        val originY = mOrigin?.y ?: return false
        val motionThreshold = (mHost.viewHeight * mScrollThresholdRatio
                * (mScrollThresholdRatio * 2)).toInt()
        return abs(originY - location.y) >= motionThreshold
    }

    private fun computeScrollDistance(pixelsPastView: Int, point: Point?): Int {
        val direction = sign(pixelsPastView.toFloat()).toInt()
        return mHost.getItemHeight(point) * direction
    }
}

internal class GridAutoScroller constructor(
    private val mHost: ScrollHost,
    layoutType: LAYOUT_TYPE,
    scrollThresholdRatio: Float
) : AutoScroller(layoutType, scrollThresholdRatio) {

    companion object {
        private const val MAX_SCROLL_GRID_STEP = 6
    }

    private val mRunner: Runnable
    private var mOrigin: Point? = null
    private var mLastLocation: Point? = null
    private var mPassedInitialMotionThreshold = false

    init {
        mRunner = Runnable { runScroll() }
    }

    override fun reset() {
        mHost.removeCallback(mRunner)
        mOrigin = null
        mLastLocation = null
        mPassedInitialMotionThreshold = false
    }

    override fun scroll(location: Point) {
        mLastLocation = location

        // See #aboveMotionThreshold for details on how we track initial location.
        if (mOrigin == null) {
            mOrigin = location
            if (DEBUG) {
                Log.v(TAG, "Origin @ $mOrigin")
            }
        }

        if (DEBUG) {
            Log.v(TAG, "Current location @ $mLastLocation")
        }
        mHost.runAtNextFrame(mRunner)
    }

    override fun stopScroll() {
        mHost.removeCallback(mRunner)
    }

    /**
     * Attempts to smooth-scroll the view at the given UI frame. Application should be
     * responsible to do any clean up (such as unsubscribing scrollListeners) after the run has
     * finished, and re-run this method on the next UI frame if applicable.
     */
    private fun runScroll() {
        val lastLocation = mLastLocation ?: return
        if (DEBUG) {
            Log.v(TAG, "runScroll in background using event location @ $mLastLocation")
        }

        // Compute the number of pixels the pointer's y-coordinate is past the view.
        // Negative values mean the pointer is at or before the top of the view, and
        // positive values mean that the pointer is at or after the bottom of the view. Note
        // that top/bottom threshold is added here so that the view still scrolls when the
        // pointer are in these buffer pixels.
        val verticalThreshold = (mHost.viewHeight * mScrollThresholdRatio).toInt()
        var pixelsPastView = when {
            lastLocation.y <= (mHost.viewTop + verticalThreshold) -> {
                lastLocation.y - (verticalThreshold + mHost.viewTop)
            }
            lastLocation.y >= (mHost.viewBottom - verticalThreshold) -> {
                (lastLocation.y - mHost.viewBottom + verticalThreshold)
            }
            else -> {
                0
            }
        }

        if (DEBUG) {
            Log.v(TAG, "runScroll in pixelsPastView @ $pixelsPastView")
        }

        if (pixelsPastView == 0) {
            // If the operation that started the scrolling is no longer inactive, or if it is active
            // but not at the edge of the recyclerview, no scrolling is necessary.
            return
        }

        // We're in one of the endzones. Now determine if there's enough of a difference
        // from the orgin to take any action. Basically if a user has somehow initiated
        // selection, but is hovering at or near their initial contact point, we don't
        // scroll. This avoids a situation where the user initiates selection in an "endzone"
        // only to have scrolling start automatically.
        if (!mPassedInitialMotionThreshold && !aboveMotionThreshold(lastLocation)) {
            if (DEBUG) {
                Log.v(TAG, "runScroll Ignoring event below motion threshold.")
            }
            return
        }
        mPassedInitialMotionThreshold = true

        if (pixelsPastView > verticalThreshold) {
            pixelsPastView = verticalThreshold
        }

        // Compute the number of pixels to scroll, and scroll that many pixels.
        val numPixels = computeScrollDistance(pixelsPastView)

        if (DEBUG) {
            Log.v(TAG, "runScroll numPixels $numPixels")
        }

        mHost.scrollBy(numPixels)

        // Replace any existing scheduled jobs with the latest and greatest..
        mHost.removeCallback(mRunner)
        mHost.runAtNextFrame(mRunner)
    }

    private fun aboveMotionThreshold(location: Point): Boolean {
        // We reuse the scroll threshold to calculate a much smaller area
        // in which we ignore motion initially.
        val originY = mOrigin?.y ?: return false
        val motionThreshold = (mHost.viewHeight * mScrollThresholdRatio
                * (mScrollThresholdRatio * 2)).toInt()
        return abs(originY - location.y) >= motionThreshold
    }

    /**
     * Computes the number of pixels to scroll based on how far the pointer is past the end
     * of the region. Roughly based on ItemTouchHelper's algorithm for computing the number of
     * pixels to scroll when an item is dragged to the end of a view.
     * @return
     */
    private fun computeScrollDistance(pixelsPastView: Int): Int {
        val topBottomThreshold = (mHost.viewHeight * mScrollThresholdRatio).toInt()
        val direction = sign(pixelsPastView.toFloat()).toInt()
        if (topBottomThreshold == 0) {
            Log.w(TAG, "computeScrollDistance topBottomThreshold ZERO")
            return direction
        }

        val absPastView = abs(pixelsPastView)

        // Calculate the ratio of how far out of the view the pointer currently resides to
        // the top/bottom scrolling hotspot of the view.
        val outOfBoundsRatio = min(1.0f, absPastView.toFloat() / topBottomThreshold)
        // Interpolate this ratio and use it to compute the maximum scroll that should be
        // possible for this step.
        val cappedScrollStep =
            (direction * MAX_SCROLL_GRID_STEP * smoothOutOfBoundsRatio(outOfBoundsRatio)).toInt()

        // If the final number of pixels to scroll ends up being 0, the view should still
        // scroll at least one pixel.
        return if (cappedScrollStep != 0) {
            cappedScrollStep
        } else {
            direction
        }
    }

    /**
     * Interpolates the given out of bounds ratio on a curve which starts at (0,0) and ends
     * at (1,1) and quickly approaches 1 near the start of that interval. This ensures that
     * drags that are at the edge or barely past the edge of the threshold does little to no
     * scrolling, while drags that are near the edge of the view does a lot of
     * scrolling. The equation y=x^2 is used, but this could also be tweaked if
     * needed.
     * @param ratio A ratio which is in the range [0, 1].
     * @return A "smoothed" value, also in the range [0, 1].
     */
    private fun smoothOutOfBoundsRatio(ratio: Float): Float {
        return ratio.toDouble().pow(2.0).toFloat()
    }
}

