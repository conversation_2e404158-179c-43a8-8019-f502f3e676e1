/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ArchiveIconHelperTest.kt
 * Description:
 * The test cases for ArchiveIconHelper
 *
 * Version: 1.0
 * Date: 2024-10-17
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><EMAIL>    2024-10-17   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.utils

import com.filemanager.common.base.BaseFileBean
import org.junit.Assert
import org.junit.Test

class ArchiveIconHelperTest : Assert() {

    @Test
    fun `should get jar icon when getArchiveIconId with jar file`() {
        // Given
        val fileBean = BaseFileBean().apply { mDisplayName = "test.jar" }

        // When
        val result = ArchiveIconHelper.getArchiveIconId(fileBean)

        // Then
        assertEquals(com.filemanager.common.R.drawable.ic_file_compress_jar, result)
    }

    @Test
    fun `should get rar icon when getArchiveIconId with rar file`() {
        // Given
        val fileBean = BaseFileBean().apply { mDisplayName = "test.rar" }

        // When
        val result = ArchiveIconHelper.getArchiveIconId(fileBean)

        // Then
        assertEquals(com.filemanager.common.R.drawable.ic_file_compress_rar, result)
    }

    @Test
    fun `should get 7z icon when getArchiveIconId with 7z file`() {
        // Given
        val fileBean = BaseFileBean().apply { mDisplayName = "test.7z" }

        // When
        val result = ArchiveIconHelper.getArchiveIconId(fileBean)

        // Then
        assertEquals(com.filemanager.common.R.drawable.ic_file_compress_7z, result)
    }

    @Test
    fun `should get zip icon when getArchiveIconId with zip file`() {
        // Given
        val fileBean = BaseFileBean().apply { mDisplayName = "test.zip" }

        // When
        val result = ArchiveIconHelper.getArchiveIconId(fileBean)

        // Then
        assertEquals(com.filemanager.common.R.drawable.ic_file_compress_zip, result)
    }

    @Test
    fun `should get zip icon when getArchiveIconId with other file`() {
        // Given
        val fileBean = BaseFileBean().apply { mDisplayName = "test.tar.gz" }

        // When
        val result = ArchiveIconHelper.getArchiveIconId(fileBean)

        // Then
        assertEquals(com.filemanager.common.R.drawable.ic_file_compress_zip, result)
    }
}