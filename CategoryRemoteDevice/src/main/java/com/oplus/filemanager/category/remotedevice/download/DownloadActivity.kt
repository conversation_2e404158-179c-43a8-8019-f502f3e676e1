/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DownloadActivity
 * * Description : 远程设备下载文件界面
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download

import android.app.Activity
import android.app.PendingIntent
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.oplus.filemanager.category.remotedevice.R
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean
import com.oplus.filemanager.category.remotedevice.operate.RemoteFileDownloadAction
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.selectdir.SelectPathController

class DownloadActivity : BaseVMActivity(), TransformNextFragmentListener {

    companion object {
        private const val TAG = "DownloadActivity"
        private const val TAG_DOWNLOAD = "TAG_DOWNLOAD"

        /**
         * 启动远程设备文件下载页面
         * @param deviceId 设备id
         * @param downloadPaths 文件路径
         * @param code 是否是打开
         */
        fun start(
            activity: Activity,
            deviceId: String,
            downloadPaths: ArrayList<Pair<String, Long>>,
            code: Int = MessageConstant.MSG_DOWNLOAD_REMOTE_FILE,
            continueDownload: Boolean = false,
            pendingIntent: PendingIntent? = null
        ) {
            if (!checkDownloadFileSize(downloadPaths, MessageConstant.MSG_OPEN_REMOTE_FILE == code)) {
                Log.e(TAG, "download file size is over size")
                return
            }
            if (isFileChannelOccupied(deviceId, continueDownload)) {
                return
            }
            val intent = Intent(activity, DownloadActivity::class.java)
            intent.putExtra(KtConstants.P_DEVICE_ID, deviceId)
            val downloadFiles = downloadPaths.map { DownloadRemoteFileBean(it) }
            intent.putParcelableArrayListExtra(KtConstants.P_PATH_LIST, ArrayList(downloadFiles))
            intent.putExtra(KtConstants.P_CODE, code)
            intent.putExtra(KtConstants.P_CONTINUE_DOWNLOAD, continueDownload)
            intent.putExtra(KtConstants.P_PENDING_INTENT, pendingIntent)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            activity.startActivity(intent)
        }

        /**
         * 判断文件通道是否被占用
         */
        private fun isFileChannelOccupied(deviceId: String, continueDownload: Boolean): Boolean {
            if (continueDownload) {
                Log.w(TAG, "isFileChannelOccupied -> continue Download")
                return false
            }
            val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
            val isOccupied = remoteDeviceApi?.isFileChannelOccupied(deviceId) ?: false
            Log.w(TAG, "isFileChannelOccupied ->device $deviceId is occupied: $isOccupied")
            if (isOccupied) {
                CustomToast.showShort(com.filemanager.common.R.string.file_is_transferring_and_retry_after_completed)
            }
            return isOccupied
        }

        /**
         * 检测要下载的文件是否数量超过99个和大小超过1G
         * @param downloadFiles 要下载的文件
         * @param isOpen 是否是打开
         * @return 是否检测通过，ture:表示通过
         */
        private fun checkDownloadFileSize(downloadFiles: ArrayList<Pair<String, Long>>, isOpen: Boolean): Boolean {
            if (downloadFiles.isEmpty()) {
                Log.e(TAG, "checkDownloadFileSize -> download path must not be empty")
                return false
            }
            // 检测文件个数不能超过99个
            if (downloadFiles.size > RemoteFileDownloadAction.MAX_FILE_COUNT) {
                Log.e(TAG, "checkDownloadFileSize -> file count:${downloadFiles.size} > 99")
                CustomToast.showShort(com.filemanager.common.R.string.not_support_download_too_many_file)
                return false
            }
            var totalSize = 0L
            downloadFiles.forEach {
                totalSize += it.second
            }
            // 检测文件总大小不能超过1G
            if (totalSize > RemoteFileDownloadAction.MAX_FILE_SIZE) {
                Log.e(TAG, "checkDownloadFileSize -> file size：$totalSize > 1G")
                if (isOpen) {
                    CustomToast.showShort(com.filemanager.common.R.string.not_support_open_too_large_file)
                } else {
                    CustomToast.showShort(com.filemanager.common.R.string.not_support_download_too_large_file)
                }
                return false
            }
            return true
        }
    }

    private var deviceId = ""
    private var downloadPaths = ArrayList<DownloadRemoteFileBean>()
    private var code = MessageConstant.MSG_DOWNLOAD_REMOTE_FILE
    private var pendingIntent: PendingIntent? = null
    private var downloadFragment: DownloadFragment? = null
    private val selectPathController by lazy { SelectPathController(lifecycle) }
    private var continueDownload: Boolean = false

    override fun getLayoutResId(): Int {
        return R.layout.activity_download
    }

    override fun initView() {
        if (null == intent) {
            Log.e(TAG, "initView intent is null")
            finish()
            return
        }
        deviceId = IntentUtils.getString(intent, KtConstants.P_DEVICE_ID) ?: ""
        downloadPaths = IntentUtils.getParcelableArrayList(intent, KtConstants.P_PATH_LIST) as? ArrayList<DownloadRemoteFileBean> ?: arrayListOf()
        code = IntentUtils.getInt(intent, KtConstants.P_CODE, MessageConstant.MSG_DOWNLOAD_REMOTE_FILE)
        continueDownload = IntentUtils.getBoolean(intent, KtConstants.P_CONTINUE_DOWNLOAD, false)
        pendingIntent = IntentUtils.getParcelable(intent, KtConstants.P_PENDING_INTENT) as? PendingIntent
        Log.d(TAG, "initView download device:$deviceId path size: ${downloadPaths.size}, code = $code, continueDownload:$continueDownload")
        if (downloadPaths.isEmpty() || deviceId.isEmpty()) {
            finish()
            return
        }
        if ((MessageConstant.MSG_OPEN_REMOTE_FILE == code) && (downloadPaths.size > 1)) {
            Log.e(TAG, "initView only support open one file!!!!")
            finish()
            return
        }
        setFragment()
    }

    override fun startObserve() {
    }

    override fun initData() {
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
    }

    private fun setFragment() {
        var fragment = supportFragmentManager.findFragmentByTag(TAG_DOWNLOAD)
        if ((fragment == null) || (fragment !is DownloadFragment)) {
            fragment = DownloadFragment.create(deviceId, downloadPaths)
        }
        val bundle = Bundle()
        bundle.putString(KtConstants.P_DEVICE_ID, deviceId)
        bundle.putParcelableArrayList(KtConstants.P_PATH_LIST, downloadPaths)
        bundle.putInt(KtConstants.P_CODE, code)
        bundle.putBoolean(KtConstants.P_CONTINUE_DOWNLOAD, continueDownload)
        bundle.putParcelable(KtConstants.P_PENDING_INTENT, pendingIntent)
        fragment.arguments = bundle
        val ft = supportFragmentManager.beginTransaction()
        ft.replace(R.id.fragment_container, fragment, TAG_DOWNLOAD)
        ft.show(fragment)
        ft.commitAllowingStateLoss()
        downloadFragment = fragment
    }

    override fun onBackPressed() {
        if ((downloadFragment as? OnBackPressed)?.pressBack() == true) {
            return
        }
        super.onBackPressed()
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        downloadFragment?.onUIConfigChanged(configList)
        selectPathController.updateDialogHeightIfNeed(supportFragmentManager)
    }

    override fun transformToNextFragment(path: String?) {
        selectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        selectPathController.showSelectPathFragmentDialog(supportFragmentManager, code)
    }

    override fun showSelectPathFragmentDialog(code: Int, path: String?) {
        selectPathController.showSelectPathFragmentDialog(supportFragmentManager, code, path)
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        selectPathController.onDestroy()
        downloadFragment?.fromSelectPathResult(code, paths)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (downloadFragment != null) {
            return downloadFragment!!.onMenuItemSelected(item)
        }
        return super.onOptionsItemSelected(item)
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
    }

    override fun onUpdatedLabel() {
    }

    override fun hasShowPanel(): Boolean {
        return selectPathController.hasShowPanel()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.e(TAG, "onDestroy")
    }
}