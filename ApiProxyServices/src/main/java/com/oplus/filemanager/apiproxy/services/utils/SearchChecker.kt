/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchChecker
 ** Description : Search 前的检查
 ** Version     : 1.0
 ** Date        : 2024/06/24 11:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/06/24       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.utils

import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.SdkUtils
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi

object SearchChecker {

    private const val TAG = "SearchChecker"

    const val CODE = "code"

    /**
     * 检查没有问题
     */
    const val CODE_OK = 200

    /**
     * 手机版本
     */
    const val CODE_NO_MATCH_OS_VERSION = 101

    /**
     * 没有同意用户须知
     */
    const val CODE_DISAGREE_PRIVACY = 102

    /**
     * 没有权限
     */
    const val CODE_NO_PERMISSION = 103

    /**
     * 查询结果出错
     */
    const val CODE_QUERY_ERROR = 104

    /**
     * 查询参数出错
     */
    const val CODE_QUERY_ARGS_ERROR = 105


    fun check(): Int {
        var code = checkHasAgreePrivacy()
        if (code == CODE_OK) {
            code = checkPermission()
        }
        Log.d(TAG, "check result:$code")
        return code
    }

    /**
     * 检查os版本
     */
    fun checkOsVersion(): Int {
        if (SdkUtils.isAtLeastOS15()) {
            return CODE_OK
        }
        return CODE_NO_MATCH_OS_VERSION
    }

    /**
     * 是否同意用户须知
     */
    fun checkHasAgreePrivacy(): Int {
        if (PrivacyPolicyController.hasAgreePrivacy()) {
            return CODE_OK
        }
        return CODE_DISAGREE_PRIVACY
    }

    /**
     * 判断权限
     */
    fun checkPermission(): Int {
        if (PermissionUtils.hasStoragePermission()) {
            return CODE_OK
        }
        return CODE_NO_PERMISSION
    }

    fun isOK(code: Int): Boolean {
        return CODE_OK == code
    }

    /**
     * 检查传入的文件类型：（0~13）
     */
    fun checkFileType(fileType: Int): Int {
        if (fileType >= SearchFileType.TYPE_ALL_POSITION && fileType <= SearchFileType.TYPE_MD_POSITION) {
            return CODE_OK
        }
        return CODE_QUERY_ARGS_ERROR
    }

    /**
     * 判断是否支持DFM搜索
     */
    fun isSupportDmpSearch(fileType: Int): Boolean {
        val dmpSearchApi = Injector.injectFactory<IDmpSearchApi>()
        val isLoadDmp = dmpSearchApi?.isShouldLoadDMP() ?: false
        val isSupportType = SearchFileType.isSupportDmp(fileType)
        Log.d(TAG, "isSupportDmpSearch fileType:$fileType isLoadDmp:$isLoadDmp supportType:$isSupportType")
        return isLoadDmp && isSupportType
    }
}