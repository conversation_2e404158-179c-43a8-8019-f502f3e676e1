package com.oplus.filemanager.recent.adapter.viewholder

import android.content.Context
import android.view.MotionEvent
import android.view.View
import android.widget.TextView
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.oplus.filemanager.recent.entity.recent.RecentFileEntity
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.clearAllMocks
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.util.HashMap
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * BaseRecentFileVH 的单元测试类
 * 用于测试 BaseRecentFileVH 类的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class BaseRecentFileVHTest {

    // 定义测试所需的mock对象
    private lateinit var mockContext: Context
    private lateinit var mockView: View
    private lateinit var mockAdapter: BaseSelectionRecycleAdapter<*, *>
    private lateinit var mockThreadManager: ThreadManager
    private lateinit var mockClickListener: BaseRecentFileVH.OnRecentItemClickListener
    private lateinit var mockFile: RecentFileEntity
    private lateinit var mockBaseFileBean: BaseFileBean
    private lateinit var mockDetailTextView: TextView
    private lateinit var testVH: TestBaseRecentFileVH

    /**
     * 测试前的准备工作
     * 初始化所有mock对象和测试实例
     */
    @Before
    fun setUp() {
        clearAllMocks()
        mockContext = mockk(relaxed = true)
        mockView = mockk(relaxed = true)
        mockAdapter = mockk(relaxed = true)
        mockThreadManager = mockk(relaxed = true)
        mockClickListener = mockk(relaxed = true)
        mockFile = mockk(relaxed = true)
        mockBaseFileBean = mockk(relaxed = true)
        mockDetailTextView = mockk(relaxed = true)
        
        // 设置mock对象的默认行为
        every { mockAdapter.itemCount } returns 10
        every { mockFile.mDateModified } returns 1000L
        every { mockFile.mSize } returns 1024L
        every { mockThreadManager.execute(any<FileRunnable>()) } returns "success"
        
        // 创建测试用的ViewHolder实例
        testVH = TestBaseRecentFileVH(mockView)
        testVH.setClickListener(mockClickListener)
    }

    /**
     * 测试后的清理工作
     * 清除所有mock对象
     */
    @After
    fun tearDown() {
        clearAllMocks()
    }

    /**
     * 测试loadData方法 - 整个ViewHolder点击的情况
     * 验证是否正确设置了点击监听器
     */
    @Test
    fun testLoadData_wholeItemClick() {
        testVH.setWholeViewHolderItemClick(true)
        
        testVH.loadData(
            mockContext,
            1,
            mockFile,
            false,
            mutableListOf(),
            HashMap(),
            mockThreadManager,
            mockAdapter,
            0
        )
        
        // 验证是否设置了正确的监听器
        verify(exactly = 1) { mockView.setOnTouchListener(any()) }
        verify(exactly = 1) { mockView.setOnClickListener(any()) }
        verify(exactly = 1) { mockView.setOnLongClickListener(any()) }
    }

    /**
     * 测试showDetail方法 - 有缓存大小的网格视图情况
     * 验证是否直接从缓存中获取并显示文件大小
     */
    @Test
    fun testShowDetail_withCachedSize_gridView() {
        val sizeCache = HashMap<String, String>()
        sizeCache["path10241000false"] = "1 KB"
        
        every { mockDetailTextView.text } returns "1 KB"
        every { mockBaseFileBean.mDateModified } returns 1000L
        every { mockBaseFileBean.mSize } returns 1024L
        
        testVH.callShowDetail(
            mockContext,
            mockBaseFileBean,
            mockDetailTextView,
            "path",
            sizeCache,
            mockThreadManager,
            false,
            {}
        )
        
        // 验证是否直接从缓存设置文本，没有执行线程任务
        verify(exactly = 1) { mockDetailTextView.text = any<String>() }
        verify(exactly = 0) { mockThreadManager.execute(any<FileRunnable>()) }
    }

    /**
     * 测试showDetail方法 - 无缓存的情况
     * 验证是否执行了线程任务来获取文件大小
     */
    @Test
    fun testShowDetail_noCache() {
        val sizeCache = HashMap<String, String>()
        
        testVH.callShowDetail(
            mockContext,
            mockBaseFileBean,
            mockDetailTextView,
            "path",
            sizeCache,
            mockThreadManager,
            true,
            {}
        )
        
        // 验证是否先清空文本并执行了线程任务
        verify(exactly = 1) { mockDetailTextView.text = "" }
        verify(exactly = 1) { mockThreadManager.execute(any<FileRunnable>()) }
    }

    /**
     * 测试触摸事件监听器
     * 验证是否正确处理了触摸事件
     */
    @Test
    fun testOnTouchListener() {
        val motionEvent = MotionEvent.obtain(0, 0, MotionEvent.ACTION_DOWN, 0f, 0f, 0)
        testVH.callOnTouchListener(mockView, motionEvent)
        
        // 验证触摸事件是否被正确保存
        assertEquals(motionEvent, testVH.getOnTouchEvent())
        motionEvent.recycle()
    }

    /**
     * 测试isPerformWholeViewHolderItemClick方法
     * 验证默认返回值为true
     */
    @Test
    fun testIsPerformWholeViewHolderItemClick() {
        assertTrue(testVH.isPerformWholeViewHolderItemClick())
    }

    /**
     * 测试getPerformChildClickWidget方法
     * 验证默认返回null
     */
    @Test
    fun testGetPerformChildClickWidget() {
        assertNull(testVH.getPerformChildClickWidget())
    }

    /**
     * 用于测试的BaseRecentFileVH子类
     * 提供了一些辅助方法来测试父类的功能
     */
    class TestBaseRecentFileVH(view: View) : BaseRecentFileVH(view) {
        private var wholeViewHolderItemClick = true
        var childWidget: View? = null
        private var onTouchEvent: MotionEvent? = null
        
        /**
         * 设置点击监听器
         */
        fun setClickListener(listener: BaseRecentFileVH.OnRecentItemClickListener) {
            mClickListener = listener
        }
        
        /**
         * 设置是否整个ViewHolder可点击
         */
        fun setWholeViewHolderItemClick(value: Boolean) {
            wholeViewHolderItemClick = value
        }
        
        /**
         * 调用父类的showDetail方法
         */
        fun callShowDetail(
            context: Context,
            file: BaseFileBean,
            detail: TextView,
            path: String,
            sizeCache: HashMap<String, String>,
            threadManager: ThreadManager,
            isLinearViewHolder: Boolean,
            block: ((String) -> Unit)
        ) {
            showDetail(context, file, detail, path, sizeCache, threadManager, isLinearViewHolder, block)
        }
        
        /**
         * 模拟触摸事件
         */
        fun callOnTouchListener(view: View, event: MotionEvent) {
            view.performClick()
            onTouchEvent = event
        }
        
        /**
         * 获取保存的触摸事件
         */
        fun getOnTouchEvent(): MotionEvent? {
            return onTouchEvent
        }
        
        /**
         * 实现抽象方法 - 更新ViewHolder
         */
        override fun updateViewHolder(
            context: Context,
            key: Int?,
            file: RecentFileEntity,
            choiceMode: Boolean,
            selectionArray: MutableList<Int>,
            sizeCache: HashMap<String, String>,
            threadManager: ThreadManager,
            adapter: BaseSelectionRecycleAdapter<*, *>,
            roundConnerType: Int
        ) {
            // 测试不需要实现
        }

        /**
         * 实现抽象方法 - 是否整个ViewHolder可点击
         */
        override fun isPerformWholeViewHolderItemClick(): Boolean {
            return wholeViewHolderItemClick
        }

        /**
         * 实现抽象方法 - 获取可点击的子控件
         */
        override fun getPerformChildClickWidget(): View? {
            return childWidget
        }
    }
}