/***********************************************************
 ** Copyright (C), 2010-2023 Oplus. All rights reserved.
 ** File:  - NewFileDragDropShadow.kt
 ** Description: Drag Shadow Builder
 ** Version: 1.0
 ** Date : 2023/06/21
 ** Author: W9001702
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  W9001702     2023/06/21    1.0        create
 ****************************************************************/

package com.filemanager.common.dragselection

import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapShader
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.PaintFlagsDrawFilter
import android.graphics.Point
import android.graphics.Rect
import android.graphics.Shader
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.View.DragShadowBuilder
import android.view.View.GONE
import android.view.View.LAYER_TYPE_HARDWARE
import android.view.View.LAYOUT_DIRECTION_RTL
import android.view.View.MeasureSpec
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import androidx.core.graphics.withSave
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.reddot.COUIHintRedDot
import com.filemanager.common.R
import com.filemanager.common.dragselection.DragUtils.getItemSize
import com.filemanager.common.dragselection.DragUtils.getShadowBuild
import com.filemanager.common.dragselection.DragUtils.getShadowView
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_GRID
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_GRID_IMG
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_LIST
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_RECENT_IMG
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_RECENT_SINGLE_IMG
import com.filemanager.common.utils.Log
import com.oplus.dropdrag.SelectionTracker.Companion.DEBUG
import java.lang.ref.WeakReference
import java.util.Locale
import com.filemanager.common.dragselection.action.DropDispatchAction.NORMAL_STATE
import com.filemanager.common.utils.SdkUtils

@Suppress("DEPRECATION")
class NewFileDragDropShadow(
    val context: Context,
    code: Int,
    private var viewWidth: Int = 0,
    private var viewHeight: Int = 0,
) : DragShadowBuilder() {
    companion object {
        private const val TAG = "NewFileDragDropShadow"

        private const val SHADOW_COUNT_ONE = 1
        private const val SHADOW_COUNT_TWO = 2
        const val POSITION_LIST_SIZE = 2
        private const val SHADOW_DX = 0f
        private const val SHADOW_DY = 10f
        private const val SHADOW_MOST_LAYER_COUNT = 3
        private const val SHADOW_WHOLE_DX = 0f
        private const val SHADOW_WHOLE_DY = 10f
        private const val SHADOW_SINGLE_DX = 1f
        private const val SHADOW_SINGLE_DY = 1f
        private const val DRAG_ITEM_ALPHA = 0.85f
        private const val DRAG_ALPHA = 255f
        private const val DRAG_ITEM_OPACITY = (0.85 * 255).toInt()
        private const val SECOND_LAYER_ROTATE = 2f
        private const val TOP_LAYER_ROTATE = -2f
        private const val MAX_LAYER_COUNT: Int = 3
        private const val GRID_SHADOW_RADIUS: Float = 20f
        private const val SHADOW_ANIM_DURATION = 200L
        const val SINGLE_SHADOW_ALPHA = (255 * 0.16).toInt()
        private const val WHOLE_SHADOW_ALPHA = (255 * 0.2).toInt()
        private const val MARK_ALPHA_VALUE = (255 * 0.1)

        // 放大淡出动画相关常量
        private const val SCALE_FADE_DURATION = 300L
        private const val SCALE_START = 1.0f
        private const val SCALE_END = 1.2f
        private const val ALPHA_START = 1.0f
        private const val ALPHA_END = 0.0f
    }

    private lateinit var shadowViewWR: WeakReference<View>
    private var width: Int = 0
    private var height: Int = 0
    private var shadowBlur: Float = 0.0f
    private var layoutPadding: Float = 0.0f
    private var shadowRadius: Float = 0.0f
    private var shadowPaint: Paint
    private val shadowColor: Int
    private val middleMaskColor: Int
    private val bottomMaskColor: Int
    private val rectStrokeColor: Int
    private val rectStrokeWidth: Float
    private var shadowImgThreeLayerMarginBottom: Float = 0.0f
    private var rootView: View? = null
    var mShowShadow = true
    private var countView: COUIHintRedDot? = null
    private var bitmapList = HashMap<Int, Triple<Float, Float, View>>()
    val statusCode = code
    var viewMode = 0
    private var mIsRtl: Boolean = false
    private var cornerMarkPositionList: ArrayList<Float>? = null

    // 放大淡出动画相关属性
    private var currentScale = SCALE_START
    private var currentAlpha = ALPHA_START
    private var isScaleFadeAnimRunning = false

    private val secondLayerDx = context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_item_second_layer_dx)
    private val secondLayerDy = context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_item_second_layer_dy)
    private val topLayerDx = context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_item_top_layer_dx)
    private val cornerRadius = context.resources.getDimensionPixelOffset(R.dimen.dimen_4dp)
    private val topLayerDy = -topLayerDx
    private var bitmapOne: Bitmap? = null
    private var bitmapOneShader: BitmapShader? = null
    private var bitmapTwo: Bitmap? = null
    private var bitmapTwoShader: BitmapShader? = null
    private var bitmapThree: Bitmap? = null
    private var bitmapThreeShader: BitmapShader? = null
    var dropCount: Int = 0
    private var shadowLayerAlpha = DRAG_ALPHA
    private var bitmapMatrix = Matrix()

    init {
        cornerMarkPositionList = ArrayList()
        shadowBlur = context.resources.getDimensionPixelOffset(R.dimen.list_drag_shadow_blur_radius).toFloat()
        layoutPadding = context.resources.getDimensionPixelOffset(R.dimen.list_drag_shadow_item_layout_padding).toFloat()
        shadowImgThreeLayerMarginBottom =
            context.resources.getDimensionPixelOffset(R.dimen.img_drag_shadow_three_margin_bottom).toFloat()
        rectStrokeWidth = context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_layout_background_stride_width).toFloat()
        shadowColor = context.getColor(R.color.drag_shadow_item_whole_shadow_color)
        middleMaskColor = context.getColor(R.color.drag_shadow_item_middle_color)
        bottomMaskColor = context.getColor(R.color.drag_shadow_item_bottom_color)
        rectStrokeColor = context.getColor(R.color.drag_shadow_item_stride_color)

        mIsRtl = (TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == LAYOUT_DIRECTION_RTL)
        if (DEBUG) {
            Log.d(TAG, "mIsRtl : $mIsRtl")
        }
        shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        rootView?.setLayerType(LAYER_TYPE_HARDWARE, shadowPaint)
    }

    private fun initShadowLayout() {
        when (statusCode) {
            STATUS_RECENT_SINGLE_IMG, STATUS_GRID_IMG, STATUS_GRID, STATUS_RECENT_IMG -> {
                initGrid(context)
                viewMode = STATUS_GRID
            }

            STATUS_LIST -> {
                initList(context)
                viewMode = STATUS_LIST
            }
        }
        shadowViewWR = WeakReference(rootView)
    }

    fun addShadowView(list: ArrayList<View>) {
        val viewList = ArrayList<View>()
        viewList.addAll(list)
        val topView = getShadowView()?.get() ?: return
        val size = viewList.size
        viewList.remove(topView)
        viewList.add(topView)
        getShadowBuild()?.apply { ->
            viewList.forEachIndexed { index, _ ->
                val viewIndex = size - index - 1
                if (viewIndex < 0 || viewIndex >= viewList.size) return
                val view = viewList[viewIndex]
                MeasureSpec.makeMeasureSpec(view.width, MeasureSpec.EXACTLY)
                MeasureSpec.makeMeasureSpec(view.height, MeasureSpec.EXACTLY)
                val measuredWidth = view.measuredWidth
                val measuredHeight = view.measuredHeight
                val viewScaleArgs = getItemSize(viewMode, viewList.size, measuredWidth, measuredHeight, context)
                val scaleWidth = (viewScaleArgs.first.toDouble()).div(view.measuredWidth.toDouble()).toFloat()
                val scaleHeight = (viewScaleArgs.second.toDouble()).div(view.measuredHeight.toDouble()).toFloat()
                bitmapList[index] = Triple(scaleWidth, scaleHeight, view)
                if (index > POSITION_LIST_SIZE) return
            }
        }
    }

    private val layerAlphaAnim by lazy {
        ValueAnimator.ofFloat(DRAG_ALPHA, DRAG_ALPHA * DRAG_ITEM_ALPHA).apply {
            duration = SHADOW_ANIM_DURATION
            interpolator = LinearInterpolator()
            val weakView = WeakReference(this@NewFileDragDropShadow)
            addUpdateListener { animator ->
                weakView.get()?.run {
                    shadowLayerAlpha = animator.animatedValue as Float
                } ?: animator.cancel()
            }
        }
    }

    private val singleShadowAnimator by lazy {
        ValueAnimator.ofInt(0, SINGLE_SHADOW_ALPHA).apply {
            duration = SHADOW_ANIM_DURATION
            interpolator = LinearInterpolator()
            val weakView = WeakReference(this@NewFileDragDropShadow)
            addUpdateListener { animator ->
                weakView.get()?.run {
                    val shadowAlpha = animator.animatedValue as Int
                    singleShadowPaint.alpha = shadowAlpha
                    if (context is Activity) {
                        context.window.decorView.updateDragShadow(this)
                        if (shadowAlpha == SINGLE_SHADOW_ALPHA) {
                            DragUtils.lastCornerMarkStateCode = NORMAL_STATE
                            DragUtils.setCornerMarkStateCode(NORMAL_STATE, context.window.decorView)
                            DragUtils.isSingleShadowComplete = true
                        }
                    }
                } ?: animator.cancel()
            }
        }
    }

    private val singleShadowPaint by lazy {
        Paint().apply {
            isAntiAlias = true
            val shadowSingleBlur = context.resources.getDimensionPixelOffset(R.dimen.dimen_10dp).toFloat()
            color = Color.TRANSPARENT
            alpha = 0
            maskFilter = BlurMaskFilter(shadowSingleBlur, BlurMaskFilter.Blur.NORMAL)
        }
    }

    private val wholeShadowPaint by lazy {
        Paint().apply {
            isAntiAlias = true
            color = Color.TRANSPARENT
            alpha = 0
            val wholeShadowBlur = context.resources.getDimensionPixelOffset(R.dimen.dimen_50dp).toFloat()
            maskFilter = BlurMaskFilter(wholeShadowBlur, BlurMaskFilter.Blur.NORMAL)
        }
    }

    private val wholeShadowAnimator by lazy {
        ValueAnimator.ofInt(0, WHOLE_SHADOW_ALPHA).apply {
            duration = SHADOW_ANIM_DURATION
            interpolator = LinearInterpolator()
            val weakView = WeakReference(this@NewFileDragDropShadow)
            addUpdateListener { animator ->
                weakView.get()?.run {
                    val shadowAlpha = animator.animatedValue as Int
                    wholeShadowPaint.alpha = shadowAlpha
                    if (context is Activity) {
                        context.window.decorView.updateDragShadow(this)
                        if (shadowAlpha == SINGLE_SHADOW_ALPHA) {
                            DragUtils.lastCornerMarkStateCode = NORMAL_STATE
                            DragUtils.setCornerMarkStateCode(NORMAL_STATE, context.window.decorView)
                            DragUtils.isWholeShadowComplete = true
                        }
                    }
                } ?: animator.cancel()
            }
        }
    }

    // 放大淡出动画器
    private val scaleFadeAnimator by lazy {
        val scaleAnimator = ValueAnimator.ofFloat(SCALE_START, SCALE_END).apply {
            duration = SCALE_FADE_DURATION
            addUpdateListener { animator ->
                currentScale = animator.animatedValue as Float
                if (context is Activity) {
                    context.window.decorView.updateDragShadow(this@NewFileDragDropShadow)
                }
            }
        }

        val alphaAnimator = ValueAnimator.ofFloat(ALPHA_START, ALPHA_END).apply {
            duration = SCALE_FADE_DURATION
            addUpdateListener { animator ->
                currentAlpha = animator.animatedValue as Float
                if (context is Activity) {
                    context.window.decorView.updateDragShadow(this@NewFileDragDropShadow)
                }
            }
        }

        AnimatorSet().apply {
            playTogether(scaleAnimator, alphaAnimator)
            addListener(object : android.animation.Animator.AnimatorListener {
                override fun onAnimationStart(animation: android.animation.Animator) {
                    isScaleFadeAnimRunning = true
                }

                override fun onAnimationEnd(animation: android.animation.Animator) {
                    isScaleFadeAnimRunning = false
                    // 动画结束后可以执行清理操作
                    Log.d(TAG, "Scale fade animation completed")
                }

                override fun onAnimationCancel(animation: android.animation.Animator) {
                    isScaleFadeAnimRunning = false
                }

                override fun onAnimationRepeat(animation: android.animation.Animator) {}
            })
        }
    }

    fun startSingleShadowAnim(): ValueAnimator? {
        if (singleShadowAnimator?.isRunning == true) return null
        singleShadowAnimator?.start()
        return singleShadowAnimator
    }

    fun startShadowAnim(): ValueAnimator? {
        if (wholeShadowAnimator?.isRunning == true) return null
        layerAlphaAnim?.start()
        wholeShadowAnimator?.start()
        return wholeShadowAnimator
    }

    fun setShadowVisible(visible: Boolean) {
        mShowShadow = visible
    }

    /**
     * 启动放大淡出动画
     * 通常在拖拽结束时调用，创建视觉反馈效果
     */
    fun startScaleFadeAnimation() {
        if (!isScaleFadeAnimRunning) {
            Log.d(TAG, "Starting scale fade animation")
            scaleFadeAnimator.start()
        } else {
            Log.d(TAG, "Scale fade animation is already running")
        }
    }

    /**
     * 停止放大淡出动画
     */
    fun stopScaleFadeAnimation() {
        if (isScaleFadeAnimRunning) {
            scaleFadeAnimator.cancel()
            // 重置动画状态
            currentScale = SCALE_START
            currentAlpha = ALPHA_START
            Log.d(TAG, "Scale fade animation stopped and reset")
        }
    }

    /**
     * 检查放大淡出动画是否正在运行
     */
    fun isScaleFadeAnimationRunning(): Boolean {
        return isScaleFadeAnimRunning
    }

    /**
     * 在拖拽结束时启动放大淡出动画
     * 这个方法通常在以下场景调用：
     * 1. 拖拽操作成功完成时
     * 2. 拖拽被取消时
     * 3. 需要提供视觉反馈时
     *
     * 使用示例：
     * ```
     * // 在拖拽结束的回调中调用
     * shadowBuilder?.startScaleFadeOnDragEnd()
     *
     * // 或者在DragUtils.resetAllState()中自动调用
     * DragUtils.getShadowBuild()?.startScaleFadeOnDragEnd()
     * ```
     */
    fun startScaleFadeOnDragEnd() {
        Log.d(TAG, "Starting scale fade animation on drag end")
        startScaleFadeAnimation()
    }

    /**
     * 重置动画状态到初始值
     * 通常在创建新的拖拽操作前调用
     */
    fun resetAnimationState() {
        stopScaleFadeAnimation()
        currentScale = SCALE_START
        currentAlpha = ALPHA_START
        Log.d(TAG, "Animation state reset to initial values")
    }

    private val bitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        isAntiAlias = true
        style = Paint.Style.FILL
    }

    private val markPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        isAntiAlias = true
        style = Paint.Style.FILL
        color = COUIContextUtil.getColor(context, R.color.drag_shadow_item_animation_mark)
    }

    private val layerPaint by lazy {
        Paint().apply {
            isAntiAlias = true
            style = Paint.Style.FILL
            color = COUIContextUtil.getColor(context, com.support.appcompat.R.color.coui_color_background_with_card)
        }
    }

    private fun initList(context: Context) {
        val layerCount = DefaultDropListener.selectedItemViews?.value?.size ?: 0
        val (itemWidth, itemHeight) = getItemSize(STATUS_LIST, layerCount, viewWidth, viewHeight, context)
        shadowRadius = GRID_SHADOW_RADIUS
        width = (itemWidth + POSITION_LIST_SIZE * layoutPadding).toInt()
        height = (itemHeight + POSITION_LIST_SIZE * layoutPadding).toInt()
        rootView = LayoutInflater.from(context).inflate(R.layout.list_dragshadow_item_layout, null)
    }

    private fun initGrid(context: Context) {
        val layerCount = DefaultDropListener.selectedItemViews?.value?.size ?: 0
        val (itemWidth, itemHeight) = getItemSize(STATUS_GRID, layerCount, viewWidth, viewHeight, context)
        shadowRadius = GRID_SHADOW_RADIUS
        width = (itemWidth + POSITION_LIST_SIZE * layoutPadding).toInt()
        height = (itemHeight + POSITION_LIST_SIZE * layoutPadding).toInt()
        rootView = LayoutInflater.from(context).inflate(R.layout.grid_dragshadow_item_layout, null)
    }

    private fun drawListAndGridNew(canvas: Canvas, r: Rect) {
        if (dropCount <= 0) return
        Log.d(TAG, "drawListAndGridNew dropCount:$dropCount")
        shadowPaint.color = Color.TRANSPARENT
        shadowPaint.setShadowLayer(shadowBlur, SHADOW_WHOLE_DX, SHADOW_WHOLE_DY, shadowColor)
        shadowPaint.isAntiAlias = true
        val left = r.left + layoutPadding
        val top = r.top + layoutPadding
        val right = r.right - layoutPadding
        val bottom = r.bottom - layoutPadding
        val itemCount = bitmapList.size
        when (itemCount) {
            SHADOW_COUNT_ONE -> drawOneLayer(canvas, left, top, right, bottom)

            SHADOW_COUNT_TWO -> drawTwoLayer(canvas, left, top, right, bottom)

            else -> drawThreeLayer(canvas, left, top, right, bottom)
        }
    }

    private fun drawThreeLayer(canvas: Canvas, left: Float, top: Float, right: Float, bottom: Float) {
        val layerCountOne = canvas.saveLayerAlpha(0f, 0f,
            canvas.width.toFloat(), canvas.height.toFloat(), shadowLayerAlpha.toInt()
        )
        onViewDraw(canvas, left, top, right, bottom, POSITION_LIST_SIZE)
        canvas.restoreToCount(layerCountOne)
        val layerCountTwo = canvas.saveLayerAlpha(
            0f, 0f,
            canvas.width.toFloat(), canvas.height.toFloat(), DRAG_ITEM_OPACITY
        )
        // 2 层
        if (onViewDrawTwo(left, top, right, bottom, canvas, 1)) return
        canvas.restoreToCount(layerCountTwo)
        val layerCountThree = canvas.saveLayerAlpha(
            0f, 0f,
            canvas.width.toFloat(), canvas.height.toFloat(), DRAG_ITEM_OPACITY
        )
        // 3 层
        if (onViewDrawThree(left, top, right, bottom, canvas)) return
        canvas.withSave {
            canvasCountView(right, top, left, -topLayerDx, topLayerDy)
        }
        canvas.restoreToCount(layerCountThree)
    }

    private fun onViewDraw(
        canvas: Canvas,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float,
        index: Int
    ) {
        val args = bitmapList[index] ?: return
        val view = args.third
        if (view is ViewGroup && (bitmapOne == null || bitmapOneShader == null)) {
            bitmapOne = DragUtils.getViewBitmap(view, view, DefaultDropListener.viewMode)
            bitmapOneShader = bitmapOne?.let { BitmapShader(it, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP) }
        }
        bitmapOne?.let {
            val scaleWidth = args.first
            val scaleHeight = args.second
            val shadowTop = top - (it.height * scaleHeight - (bottom - top)) / POSITION_LIST_SIZE
            val shadowLeft = left - (it.width * scaleWidth - (right - left)) / POSITION_LIST_SIZE
            val rightLayerDx = shadowLeft + it.width * scaleWidth
            val bottomLayerDy = shadowTop + it.height * scaleHeight
            drawWholeShadow(canvas, shadowLeft, shadowTop, rightLayerDx, bottomLayerDy)
            canvas.drawRoundRect(shadowLeft, shadowTop, rightLayerDx, bottomLayerDy, shadowRadius, shadowRadius, layerPaint)
            bitmapMatrix.setScale(scaleWidth, scaleHeight)
            bitmapMatrix.postTranslate(shadowLeft, shadowTop)
            bitmapOneShader?.setLocalMatrix(bitmapMatrix)
            bitmapPaint.shader = bitmapOneShader
            canvas.drawRoundRect(shadowLeft, shadowTop, rightLayerDx, bottomLayerDy, shadowRadius, shadowRadius, bitmapPaint)
            if (bitmapThree == null && bitmapTwo == null) {
                canvas.drawRoundRect(shadowLeft, shadowTop, rightLayerDx, bottomLayerDy, shadowRadius, shadowRadius, markPaint)
            }
        }
    }

    private fun onViewDrawTwo(left: Float, top: Float, right: Float, bottom: Float, canvas: Canvas, index: Int): Boolean {
        val args = bitmapList[index] ?: return true
        val view = args.third
        if (view is ViewGroup && (bitmapTwo == null || bitmapTwoShader == null)) {
            bitmapTwo = DragUtils.getViewBitmap(view, view, DefaultDropListener.viewMode)
            bitmapTwoShader = bitmapTwo?.let { BitmapShader(it, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP) }
        }
        bitmapTwo?.let {
            val scaleWidth = args.first
            val scaleHeight = args.second
            val shadowLeft = left + secondLayerDx - (it.width * scaleWidth - (right - left)) / POSITION_LIST_SIZE
            val shadowTop = top + secondLayerDy - (it.height * scaleHeight - (bottom - top)) / POSITION_LIST_SIZE
            val rightLayerDx = shadowLeft + it.width * scaleWidth
            val bottomLayerDy = shadowTop + it.height * scaleHeight
            val centerX = (shadowLeft + rightLayerDx) / POSITION_LIST_SIZE
            val centerY = (shadowTop + bottomLayerDy) / POSITION_LIST_SIZE
            canvas.rotate(SECOND_LAYER_ROTATE, centerX, centerY)
            drawLayer(canvas, shadowLeft, shadowTop, rightLayerDx, bottomLayerDy)
            bitmapMatrix.setScale(scaleWidth, scaleHeight)
            bitmapMatrix.postTranslate(shadowLeft, shadowTop)
            bitmapTwoShader?.setLocalMatrix(bitmapMatrix)
            bitmapPaint.shader = bitmapTwoShader
            canvas.drawRoundRect(shadowLeft, shadowTop, rightLayerDx, bottomLayerDy, shadowRadius, shadowRadius, bitmapPaint)
            if (bitmapThree == null) {
                canvas.drawRoundRect(shadowLeft, shadowTop, rightLayerDx, bottomLayerDy, shadowRadius, shadowRadius, markPaint)
            }
        }
        return false
    }

    private fun onViewDrawThree(
        left: Float,
        top: Float,
        right: Float,
        bottom: Float,
        canvas: Canvas,
    ): Boolean {
        val args = bitmapList[0] ?: return true
        val scaleWidth = args.first
        val scaleHeight = args.second
        val view = args.third
        if (view is ViewGroup && (bitmapThree == null || bitmapThreeShader == null)) {
            bitmapThree = DragUtils.getViewBitmap(view, view, DefaultDropListener.viewMode)
            bitmapThreeShader = bitmapThree?.let { BitmapShader(it, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP) }
        }
        bitmapThree?.let {
            val topShadowLeft = left + topLayerDx - (it.width * scaleWidth - (right - left)) / POSITION_LIST_SIZE
            val topShadowTop = top + topLayerDy - (it.height * scaleHeight - (bottom - top)) / POSITION_LIST_SIZE
            val rightLayerDx = topShadowLeft + it.width * scaleWidth
            val bottomLayerDy = topShadowTop + it.height * scaleHeight

            val centerX2 = (topShadowLeft + rightLayerDx) / POSITION_LIST_SIZE
            val centerY2 = (topShadowTop + bottomLayerDy) / POSITION_LIST_SIZE

            canvas.rotate(TOP_LAYER_ROTATE, centerX2, centerY2)
            drawLayer(canvas, topShadowLeft, topShadowTop, rightLayerDx, bottomLayerDy)
            bitmapMatrix.setScale(scaleWidth, scaleHeight)
            bitmapMatrix.postTranslate(topShadowLeft, topShadowTop)
            bitmapThreeShader?.setLocalMatrix(bitmapMatrix)
            bitmapPaint.shader = bitmapThreeShader
            canvas.drawRoundRect(topShadowLeft, topShadowTop, rightLayerDx, bottomLayerDy, shadowRadius, shadowRadius, bitmapPaint)
            canvas.drawRoundRect(topShadowLeft, topShadowTop, rightLayerDx, bottomLayerDy, shadowRadius, shadowRadius, markPaint)
        }

        return false
    }

    private fun drawTwoLayer(
        canvas: Canvas,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float
    ) {
        val layerCountOne = canvas.saveLayerAlpha(
            0f,
            0f,
            canvas.width.toFloat(),
            canvas.height.toFloat(),
            shadowLayerAlpha.toInt()
        )
        onViewDraw(canvas, left, top, right, bottom, 1)
        canvas.restoreToCount(layerCountOne)
        val layerCountTwo = canvas.saveLayerAlpha(
            0f,
            0f,
            canvas.width.toFloat(),
            canvas.height.toFloat(),
            DRAG_ITEM_OPACITY
        )
        if (onViewDrawTwo(left, top, right, bottom, canvas, 0)) return

        // 判断系统版本，决定是否展示角标
        canvas.withSave {
            canvasCountView(right, top, left, secondLayerDx, secondLayerDy)
        }
        Log.d(TAG, " secondLayerDx:$secondLayerDx $secondLayerDy topLayerDx:$topLayerDx $topLayerDy")
        canvas.restoreToCount(layerCountTwo)
    }

    private fun drawOneLayer(
        canvas: Canvas,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float
    ) {
        val saveCount = canvas.saveLayerAlpha(
            0f,
            0f,
            canvas.width.toFloat(),
            canvas.height.toFloat(),
            shadowLayerAlpha.toInt()
        )
        onViewDraw(canvas, left, top, right, bottom, 0)

        canvas.withSave {
            canvasCountView(right, top, left, 0, 0)
        }

        canvas.restoreToCount(saveCount)
    }

    private fun Canvas.canvasCountView(
        right: Float,
        topShadowTop: Float,
        topShadowLeft: Float,
        offsetDistanceX: Int,
        offsetDistanceY: Int
    ) {
        if (SdkUtils.isAtLeastOS16()) return
        if (mIsRtl) {
            translate(topShadowLeft + offsetDistanceX, topShadowTop + offsetDistanceY)
        } else {
            translate(right + offsetDistanceX * POSITION_LIST_SIZE, topShadowTop + offsetDistanceY * POSITION_LIST_SIZE)
        }
        countView?.draw(this)
    }

    private fun drawLayer(
        canvas: Canvas,
        l: Float,
        t: Float,
        r: Float,
        b: Float
    ) {
        canvas.drawRoundRect(
            l,
            t,
            r,
            b,
            shadowRadius,
            shadowRadius,
            singleShadowPaint
        )
        //rect
        canvas.drawRoundRect(l, t, r, b, shadowRadius, shadowRadius, layerPaint)
    }

    private fun drawWholeShadow(canvas: Canvas, left: Float, top: Float, right: Float, bottom: Float) {
        canvas.drawRoundRect(
            left,
            top,
            right,
            bottom,
            shadowRadius,
            shadowRadius,
            wholeShadowPaint
        )
    }

    override fun onDrawShadow(canvas: Canvas) {
        Log.d(TAG, "onDrawShadow")
        val r = canvas.clipBounds
        val shadowView = shadowViewWR.get() ?: return
        canvas.drawFilter = PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG)
        if (!mShowShadow || bitmapList == null) {
            Log.d(TAG, "onDrawShadow return mShowShadow: $mShowShadow bitmapList: $bitmapList")
            return
        }

        // 应用放大淡出动画效果
        val saveCount = canvas.save()

        // 计算缩放中心点（画布中心）
        val centerX = r.exactCenterX()
        val centerY = r.exactCenterY()

        // 应用缩放变换
        canvas.scale(currentScale, currentScale, centerX, centerY)

        // 应用透明度（通过saveLayerAlpha）
        val layerAlpha = (currentAlpha * 255).toInt()
        val layerSaveCount = canvas.saveLayerAlpha(
            r.left.toFloat(), r.top.toFloat(),
            r.right.toFloat(), r.bottom.toFloat(),
            layerAlpha
        )

        shadowView.measure(
            MeasureSpec.makeMeasureSpec(r.right - r.left, MeasureSpec.EXACTLY),
            MeasureSpec.makeMeasureSpec(r.bottom - r.top, MeasureSpec.EXACTLY))
        shadowView.layout(r.left, r.top, r.right, r.bottom)
        initCountViewPosition(r)
        drawListAndGridNew(canvas, r)

        // 恢复画布状态
        canvas.restoreToCount(layerSaveCount)
        canvas.restoreToCount(saveCount)
    }

    private fun initCountViewPosition(r: Rect) {
        if (dropCount < 0 || !SdkUtils.isAtLeastOS16()) return
        val left = r.left + layoutPadding
        val top = r.top + layoutPadding
        val right = r.right - layoutPadding
        when (statusCode) {
            STATUS_GRID_IMG, STATUS_RECENT_SINGLE_IMG -> {
                if (mIsRtl) {
                    setCornerMarkPosition(left, top)
                } else {
                    setCornerMarkPosition(right, top)
                }
            }

            STATUS_LIST, STATUS_GRID, STATUS_RECENT_IMG -> {
                when (dropCount) {
                    SHADOW_COUNT_ONE -> {
                        if (mIsRtl) {
                            setCornerMarkPosition(left, top)
                        } else {
                            setCornerMarkPosition(right, top)
                        }
                    }

                    SHADOW_COUNT_TWO -> {
                        if (mIsRtl) {
                            setCornerMarkPosition(left, top)
                        } else {
                            setCornerMarkPosition(
                                right + secondLayerDx,
                                top + secondLayerDy
                            )
                        }
                    }

                    else -> {
                        if (mIsRtl) {
                            setCornerMarkPosition(left, top)
                        } else {
                            setCornerMarkPosition(
                                right - topLayerDx,
                                top - topLayerDy
                            )
                        }
                    }
                }
            }
        }
    }

    override fun onProvideShadowMetrics(outShadowSize: Point, outShadowTouchPoint: Point) {
        super.onProvideShadowMetrics(outShadowSize, outShadowTouchPoint)
        outShadowSize.set(width, height)
        outShadowTouchPoint.set(width / POSITION_LIST_SIZE, height / POSITION_LIST_SIZE)
    }

    fun updateShadowRedDot(dragDropCount: Int?) {
        dropCount = dragDropCount ?: return
        initShadowLayout()
        if (SdkUtils.isAtLeastOS16()) return
        val shadowView = shadowViewWR.get() ?: return
        countView = shadowView.findViewById(R.id.dragshadow_item_count) ?: return
        countView?.pointMode = COUIHintRedDot.POINT_NUM_MODE_STROKE
        if (dropCount > 1) {
            countView?.pointNumber = dropCount
            countView?.visibility = VISIBLE
        } else {
            countView?.visibility = GONE
        }
    }

    fun getCornerMarkPosition(): ArrayList<Float>? {
        return cornerMarkPositionList
    }

    private fun setCornerMarkPosition(x: Float, y: Float) {
        cornerMarkPositionList?.clear()
        cornerMarkPositionList?.add(x)
        cornerMarkPositionList?.add(y)
    }
}