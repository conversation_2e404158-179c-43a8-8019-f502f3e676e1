/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - CommonHeapUpTarget.kt
 * Description:
 *     The common file implementation about receive loaded preview image from loader
 *
 * Version: 1.0
 * Date: 2024-10-19
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-10-19   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection.loader

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.preview.selection.data.HeadUpImageData

internal class CommonHeapUpTarget(
    context: Context,
    fileBean: BaseFileBean,
    defaultPlaceholderRes: Int,
    isAlwaysIcon: Boolean = false,
    isVideoType: Boolean = false
) : AbsHeapUpImageTarget<Bitmap>(
    context,
    fileBean,
    defaultPlaceholderRes,
    isAlwaysIcon,
    isVideoType
) {

    private companion object {
        private const val TAG = "CommonHeapUpTarget"
    }

    override val logTag: String = TAG

    override fun obtainResourceDrawable(resource: Bitmap): Drawable =
        BitmapDrawable(context.resources, resource)

    override fun obtainResourceSizeInfo(resource: Bitmap): HeadUpImageData.ImageSizeInfo =
        HeadUpImageData.ImageSizeInfo(resource.width, resource.height)
}