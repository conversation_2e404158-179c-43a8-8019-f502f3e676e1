package com.oplus.filemanager.apiproxy.services.loader

import android.content.ContentResolver
import android.content.Context
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo
import com.filemanager.common.bean.remotedevice.RemoteFileData
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.apiproxy.services.data.SearchResultData
import com.oplus.filemanager.apiproxy.services.utils.SearchFileType
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import io.mockk.*
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.robolectric.annotation.Config

/**
 * RemoteFileSearchMethodImpl 的单元测试类
 * 用于测试远程文件搜索功能的实现逻辑
 */
@Config(sdk = [28])
class RemoteFileSearchMethodImplTest {

    // 使用MockK框架模拟的依赖对象
    @MockK
    private lateinit var mockContext: Context

    @MockK
    private lateinit var mockContentResolver: ContentResolver

    @MockK
    private lateinit var mockRemoteDevice: IRemoteDevice

    @MockK
    private lateinit var mockDocumentExtensionType: IDocumentExtensionType

    @MockK
    private lateinit var mockSearchResultData: SearchResultData

    // 被测对象
    private lateinit var remoteFileSearchMethod: RemoteFileSearchMethodImpl
    // 测试用的协程调度器
    private val testDispatcher = TestCoroutineDispatcher()

    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 设置主调度器为测试调度器
     * 3. 模拟静态对象和方法
     */
    @Before
    fun setUp() {
        // 初始化MockK注解
        MockKAnnotations.init(this)
        // 设置主调度器为测试调度器
        Dispatchers.setMain(testDispatcher)
        // 模拟Injector对象
        mockkObject(Injector)
        // 模拟SearchFileType对象
        mockkObject(SearchFileType)
        // 模拟Log类的静态方法
        mockkStatic(Log::class)

        // 设置模拟对象的预期行为
        every { Injector.injectFactory<IRemoteDevice>() } returns mockRemoteDevice
        every { Injector.injectFactory<IDocumentExtensionType>() } returns mockDocumentExtensionType
        every { Log.d(any<String>(), any<String>()) } just Runs
        every { Log.e(any<String>(), any<String>()) } just Runs

        // 创建被测对象实例
        remoteFileSearchMethod = RemoteFileSearchMethodImpl(mockContext)
    }

    /**
     * 测试后的清理方法
     * 1. 解除所有模拟
     * 2. 重置主调度器
     * 3. 清理测试协程
     */
    @After
    fun tearDown() {
        // 解除所有模拟
        unmockkAll()
        // 重置主调度器
        Dispatchers.resetMain()
        // 清理测试协程
        testDispatcher.cleanupTestCoroutines()
    }

    /**
     * 测试isSupport方法在设备未连接时返回false
     */
    @Test
    fun `isSupport should return false when device not connected`() {
        // 模拟设备未连接的情况
        every { mockRemoteDevice.getCurrentLinkedRemoteDiceInfo() } returns null

        // 调用被测方法
        val result = remoteFileSearchMethod.isSupport("test", 1)

        // 验证结果和日志输出
        assertFalse(result)
        verify { Log.e("RemoteFileSearchMethodImpl", "isSupport device hasn't connected") }
    }

    /**
     * 测试isSupport方法在设备已连接时返回true
     */
    @Test
    fun `isSupport should return true when device connected`() {
        // 模拟设备信息
        val mockDeviceInfo = mockk<RemoteDeviceInfo>()
        every { mockDeviceInfo.deviceId } returns "device123"
        // 模拟设备已连接的情况
        every { mockRemoteDevice.getCurrentLinkedRemoteDiceInfo() } returns mockDeviceInfo

        // 调用被测方法
        val result = remoteFileSearchMethod.isSupport("test", 1)

        // 验证结果
        assertTrue(result)
    }

    /**
     * 测试handleSearch方法在设备未连接时返回空列表
     */
    @Test
    fun `handleSearch should return empty list when device not connected`() {
        // 模拟设备未连接的情况
        every { mockRemoteDevice.getCurrentLinkedRemoteDiceInfo() } returns null

        // 调用被测方法
        val result = remoteFileSearchMethod.handleSearch(mockContentResolver, "test", 1)

        // 验证结果和日志输出
        assertTrue(result.isEmpty())
        verify { Log.e("RemoteFileSearchMethodImpl", "handleSearch device hasn't connected") }
    }

    /**
     * 测试classifyItem方法能正确排序和分类远程文件
     */
    @Test
    fun `classifyItem should sort and classify remote files`() {
        // 创建测试用的远程文件对象
        val file1 = RemoteFileBean().apply { mDateModified = 1000L }
        val file2 = RemoteFileBean().apply { mDateModified = 2000L }
        val files = listOf(file1, file2)

        // 设置模拟对象的预期行为
        every { mockDocumentExtensionType.sortFiles(files, SortHelper.FILE_TIME_REVERSE_ORDER, SortHelper.FILE_TIME_REVERSE_ORDER, true, true) } just Runs
        every { mockSearchResultData.classifyRemoteFileSearchList(files) } just Runs

        // 调用被测方法
        remoteFileSearchMethod.classifyItem(files, mockSearchResultData)

        // 验证排序和分类方法被正确调用
        verify { mockDocumentExtensionType.sortFiles(files, SortHelper.FILE_TIME_REVERSE_ORDER, SortHelper.FILE_TIME_REVERSE_ORDER, true, true) }
        verify { mockSearchResultData.classifyRemoteFileSearchList(files) }
    }
}