/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SubPageAdMgrCompat
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/3/14 16:58
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/3/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat

object SubPageAdMgrCompat {

    fun isSupportDocAd(): Boolean {
        return when (initAdBrand()) {
            AdBrand.OPPO -> false
            AdBrand.REALME -> true
            AdBrand.NULL -> false
        }
    }

    fun isSupportApkAd(): Boolean {
        return when (initAdBrand()) {
            AdBrand.OPPO -> true
            AdBrand.REALME -> true
            AdBrand.NULL -> false
        }
    }

    fun isHideApkInstallAd(): Boolean {
        return when (initAdBrand()) {
            AdBrand.OPPO -> true
            AdBrand.REALME -> false
            AdBrand.NULL -> false
        }
    }

    fun isSupportAudioAd(): Boolean {
        return when (initAdBrand()) {
            AdBrand.OPPO -> true
            AdBrand.REALME -> true
            AdBrand.NULL -> false
        }
    }

    fun isSupportAlbumSetAd(): Boolean {
        return when (initAdBrand()) {
            AdBrand.OPPO -> true
            AdBrand.REALME -> false
            AdBrand.NULL -> false
        }
    }

    fun isSupportPhotoStorageAd(): Boolean {
        return when (initAdBrand()) {
            AdBrand.OPPO -> true
            AdBrand.REALME -> false
            AdBrand.NULL -> false
        }
    }
}