package com.oplus.dropdrag

import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE

/**
 * SelectionDelegate的单元测试类
 * 用于测试SelectionDelegate接口的各种功能实现
 */
@RunWith(JUnit4::class)
class SelectionDelegateTest {

    // 被测试的SelectionDelegate实例
    private lateinit var delegate: SelectionDelegate<Int>
    // 使用MockK创建的模拟SelectionDelegate实例
    private val mockDelegate = mockk<SelectionDelegate<Int>>(relaxed = true)

    /**
     * 在每个测试方法执行前初始化测试环境
     * 创建一个SelectionDelegate的具体实现用于测试
     */
    @Before
    fun setUp() {
        delegate = object : SelectionDelegate<Int>() {
            // 存储被选中的项
            private val selectedItems = mutableListOf<Int>()
            // 标记是否处于选择模式
            private var inSelectMode = false

            // 进入选择模式
            override fun enterSelectionMode(key: Int): Boolean {
                inSelectMode = true
                return inSelectMode
            }

            // 检查是否处于选择模式
            override fun isInSelectMode(): Boolean = inSelectMode

            // 选择多个项
            override fun selectItems(key: ArrayList<Int>): Boolean {
                if (!inSelectMode) return false
                selectedItems.addAll(key)
                return true
            }

            // 取消选择多个项
            override fun deselectItems(key: ArrayList<Int>): Boolean {
                if (!inSelectMode) return false
                selectedItems.removeAll(key)
                return true
            }

            // 检查指定项是否被选中
            override fun isItemSelected(key: Int): Boolean = selectedItems.contains(key)

            // 获取所有被选中的项
            override fun getSelectionList(): List<Int> = selectedItems.toList()

            // 检查是否支持拖拽
            override fun canDragDrop(): Boolean = true
        }
    }

    /**
     * 在每个测试方法执行后清理测试环境
     * 清除所有Mock对象
     */
    @After
    fun tearDown() {
        clearAllMocks()
    }

    /**
     * 测试进入选择模式的功能
     * 验证enterSelectionMode和isInSelectMode方法
     */
    @Test
    fun `should enter selection mode and return true`() {
        assertTrue(delegate.enterSelectionMode(1))
        assertTrue(delegate.isInSelectMode())
    }

    /**
     * 测试在选择模式下选择多个项的功能
     * 验证selectItems和getSelectionList方法
     */
    @Test
    fun `should select items when in selection mode`() {
        delegate.enterSelectionMode(1)
        assertTrue(delegate.selectItems(arrayListOf(1, 2, 3)))
        assertEquals(listOf(1, 2, 3), delegate.getSelectionList())
    }

    /**
     * 测试不在选择模式下选择项的功能
     * 验证非选择模式下selectItems方法的行为
     */
    @Test
    fun `should not select items when not in selection mode`() {
        assertFalse(delegate.selectItems(arrayListOf(1, 2, 3)))
        assertTrue(delegate.getSelectionList().isEmpty())
    }

    /**
     * 测试在选择模式下取消选择多个项的功能
     * 验证deselectItems方法
     */
    @Test
    fun `should deselect items when in selection mode`() {
        delegate.enterSelectionMode(1)
        delegate.selectItems(arrayListOf(1, 2, 3))
        assertTrue(delegate.deselectItems(arrayListOf(1, 2)))
        assertEquals(listOf(3), delegate.getSelectionList())
    }

    /**
     * 测试不在选择模式下取消选择项的功能
     * 验证非选择模式下deselectItems方法的行为
     */
    @Test
    fun `should not deselect items when not in selection mode`() {
        assertFalse(delegate.deselectItems(arrayListOf(1, 2)))
    }

    /**
     * 测试选择单个项的功能
     * 验证selectItem和isItemSelected方法
     */
    @Test
    fun `should select single item`() {
        delegate.enterSelectionMode(1)
        assertTrue(delegate.selectItem(1))
        assertTrue(delegate.isItemSelected(1))
    }

    /**
     * 测试取消选择单个项的功能
     * 验证deselectItem和isItemSelected方法
     */
    @Test
    fun `should deselect single item`() {
        delegate.enterSelectionMode(1)
        delegate.selectItem(1)
        assertTrue(delegate.deselectItem(1))
        assertFalse(delegate.isItemSelected(1))
    }

    /**
     * 测试检查是否有项被选中的功能
     * 验证isAnyItemSelected方法在有选中项时返回true
     */
    @Test
    fun `should return true when items are selected`() {
        delegate.enterSelectionMode(1)
        delegate.selectItem(1)
        assertTrue(delegate.isAnyItemSelected())
    }

    /**
     * 测试检查是否有项被选中的功能
     * 验证isAnyItemSelected方法在没有选中项时返回false
     */
    @Test
    fun `should return false when no items are selected`() {
        delegate.enterSelectionMode(1)
        assertFalse(delegate.isAnyItemSelected())
    }

    /**
     * 测试检查是否有项被选中的功能
     * 验证isAnyItemSelected方法在非选择模式下返回false
     */
    @Test
    fun `should return false when not in selection mode`() {
        assertFalse(delegate.isAnyItemSelected())
    }

    /**
     * 测试检查是否支持拖拽的功能
     * 验证canDragDrop方法默认返回true
     */
    @Test
    fun `should return true by default`() {
        assertTrue(delegate.canDragDrop())
    }

    /**
     * 测试获取布局类型的功能
     * 验证getLayoutType方法默认返回LIST类型
     */
    @Test
    fun `should return LIST by default`() {
        assertEquals(LAYOUT_TYPE.LIST, delegate.getLayoutType())
    }

    /**
     * 使用MockK测试抽象方法
     * 验证所有抽象方法的模拟行为
     */
    @Test
    fun `mock test for abstract methods`() {
        // 设置模拟对象的行为
        every { mockDelegate.enterSelectionMode(any()) } returns true
        every { mockDelegate.isInSelectMode() } returns true
        every { mockDelegate.selectItems(any()) } returns true
        every { mockDelegate.deselectItems(any()) } returns true
        every { mockDelegate.isItemSelected(any()) } returns true
        every { mockDelegate.getSelectionList() } returns listOf(1, 2, 3)
        every { mockDelegate.canDragDrop() } returns false

        // 验证模拟对象的行为
        assertTrue(mockDelegate.enterSelectionMode(1))
        assertTrue(mockDelegate.isInSelectMode())
        assertTrue(mockDelegate.selectItems(arrayListOf(1, 2, 3)))
        assertTrue(mockDelegate.deselectItems(arrayListOf(1)))
        assertTrue(mockDelegate.isItemSelected(1))
        assertEquals(listOf(1, 2, 3), mockDelegate.getSelectionList())
        assertFalse(mockDelegate.canDragDrop())
    }
}