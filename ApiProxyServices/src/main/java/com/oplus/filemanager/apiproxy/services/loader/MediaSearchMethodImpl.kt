/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MediaSearchMethodImpl
 ** Description : media Search impl
 ** Version     : 1.0
 ** Date        : 2025/02/19 11:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2025/02/19       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.loader

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.SearchFileWrapper
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.DmpUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.closeQuietly
import com.oplus.filemanager.apiproxy.services.data.SearchResultData
import com.oplus.filemanager.apiproxy.services.loader.SearchDocLoader.Companion.LIMIT_COUNT
import com.oplus.filemanager.apiproxy.services.loader.SearchDocLoader.Companion.PROJECTION
import com.oplus.filemanager.apiproxy.services.utils.SearchChecker
import com.oplus.filemanager.apiproxy.services.utils.SearchFileType
import com.oplus.filemanager.apiproxy.services.utils.execInInnerProcess
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType

class MediaSearchMethodImpl(context: Context) : SearchMethod(context) {

    companion object {
        private const val TAG = "MediaSearchMethodImpl"
    }


    private fun getUri(): Uri {
        return FileMediaHelper.FILE_URI
    }


    private fun getProjection(): Array<String> {
        return PROJECTION
    }

    private fun getSelection(): String {
        val builder = StringBuilder()
        // 过滤后缀名
        filterSuffixSelection(builder, fileType)
        // 过滤文件名
        filterFileNameSelection(builder, keyword)
        // 过滤掉文件夹
        filterDirSelection(builder)
        return builder.toString().apply {
            Log.d(TAG, "getSelection keyword:$keyword fileType:$fileType -> $this")
        }
    }

    /**
     * 过滤文件名
     */
    private fun filterFileNameSelection(builder: StringBuilder, keyword: String?) {
        if (keyword.isNullOrEmpty()) {
            return
        }
        if (builder.isNotEmpty()) {
            builder.append(" AND ")
        }
        builder.append(MediaStore.Files.FileColumns.DISPLAY_NAME)
            .append(" LIKE '%")
            .append(keyword)
        builder.append("%'")
    }

    /**
     * 过滤后缀名
     */
    private fun filterSuffixSelection(builder: StringBuilder, fileType: Int) {
        val documentExtensionType = Injector.injectFactory<IDocumentExtensionType>() ?: return
        val sql = if (fileType == SearchFileType.TYPE_ALL_POSITION) {
            documentExtensionType.getDocumentCountSqlQuery(context)
        } else {
            val suffix = SearchFileType.getDocSuffix(fileType)
            val selectionArgs = documentExtensionType.getAllSelectionArgs(arrayListOf(suffix))
            documentExtensionType.getDocumentSqlQuery(selectionArgs)
        }
        if (builder.isNotEmpty()) {
            builder.append(" AND ")
        }
        builder.append(sql)
    }

    /**
     * 过滤文件夹
     */
    private fun filterDirSelection(builder: StringBuilder) {
        if (builder.isNotEmpty()) {
            builder.append(" AND ")
        }
        builder.append("(${MediaStore.Files.FileColumns.MIME_TYPE} != ${MimeTypeHelper.DIRECTORY_TYPE})")

        if (!HiddenFileHelper.isNeedShowHiddenFile()) {
            builder.append(" AND ")
            builder.append("(${MediaStore.Files.FileColumns.DATA} NOT LIKE '%/.%')")
        }
    }


    override fun isSupport(keyword: String, fileType: Int): Boolean {
        return true
    }

    @Suppress("TooGenericExceptionCaught")
    override fun handleSearch(contentResolver: ContentResolver, keyword: String, fileType: Int): List<BaseFileBean> {
        val uri = getUri()
        val projection = getProjection()
        val selection = getSelection()
        val bundle = Bundle()
        bundle.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, selection)
        // 限制查询条数为10
        bundle.putInt(ContentResolver.QUERY_ARG_LIMIT, LIMIT_COUNT)
        code = SearchChecker.CODE_OK
        val list = mutableListOf<BaseFileBean>()
        contentResolver.execInInnerProcess {
            var cursor: Cursor? = null
            try {
                cursor = query(uri, projection, bundle, null)
                Log.d(TAG, "handleSearch keyword:$keyword fileType:$fileType -> result:${cursor?.count}")
                cursor?.let {
                    while (it.moveToNext()) {
                        val item = createFromCursor(it, uri)
                        item?.let {
                            list.add(item)
                        }
                    }
                    code = SearchChecker.CODE_OK
                }
            } catch (e: Exception) {
                Log.e(TAG, "handleSearch keyword:$keyword fileType:$fileType error", e)
                code = SearchChecker.CODE_QUERY_ERROR
            } finally {
                cursor?.closeQuietly()
            }
        }
        return list
    }


    private fun createFromCursor(cursor: Cursor, uri: Uri?): BaseFileBean? {
        val absolutePath = cursor.getString(SearchFileWrapper.INDEX_DATA)
        return if (isIgnoredPath(absolutePath)) {
            null
        } else {
            SearchFileWrapper(cursor, uri)
        }
    }

    override fun classifyItem(list: List<BaseFileBean>, searchResult: SearchResultData) {
        val isSimpleWord = DmpUtils.getSimpleWordValue(keyword)
        list.forEach {
            searchResult.classMediaSearchItem(it, isSimpleWord)
        }
    }
}