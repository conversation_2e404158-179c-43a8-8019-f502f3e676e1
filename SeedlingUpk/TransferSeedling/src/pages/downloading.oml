<template>
  <card-template entry="notification" category="modular" onclick="cardClick">
    <compact>
      <leading>
        <image level="A*" src="{{fileIcon}}"></image>
        <text level="B*"></text>
      </leading>
      <trailing>
        <text level="B0" color="#FFFFFF">{{progress}}%</text>
        <widget level="A0" value="{{progress}}" color="#2E9BFF" bg-color="rgba(255,255,255,0.16)"></widget>
      </trailing>
    </compact>
    <expanded bg-color="#000000" border-color="#0000ff" level="F1*">
      <leading category="mirror">
        <text level="C10" color="#FFFFFF">{{appName}}</text>
        <image level="D9" src="{{appIcon}}"></image>
      </leading>
      <center category="text-highlight">
        <image level="D1" src="{{fileIcon}}" shape-style="round-rectangle"></image>
        <text level="B1*">{{title}}</text>
        <text level="C2">{{subTitle}}</text>
        <widget level="E1*" value="{{progress}}" color="#2E9BFF" bg-color="rgba(255,255,255,0.16)" show-progress="true">
          <text color="#FFFFFF">{{progress}}%</text>
        </widget>
      </center>
    </expanded>
    <voice>{{ $t('strings.voiceLabel') }}</voice>
    <!-- A1组件为服务图标，不能删除 -->
    <image level="A1*" src="assets/images/symmetry_logo.png"></image>
  </card-template>
</template>
<style>
</style>
<data>
    {
  "uiData": {
    "appName": "文件管理",
    "appIcon":"@drawable/ic_launcher_filemanager",
    "fileIcon":"@drawable/ic_launcher_filemanager",
    "taskId": 0,
    "progress": 0,
    "title":"下载中",
    "subTitle": ""
  },
  "uiEvent": {
    "cardClick": {
      "type": "deeplink",
      "uri": "nativeapp://android.intent.action.VIEW",
      //data为快应用链接
      "data": "filemanager://deeplink.remotedevice.download",
      "params": {
          "P_task_id": "{{taskId}}",
          "from":"seedling"
      }
   }
  }
}
</data>