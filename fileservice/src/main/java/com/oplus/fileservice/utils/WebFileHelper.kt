/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WebFileHelper.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/12 15:22
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/12       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.utils

import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Log
import java.io.File

object WebFileHelper {
    private const val TAG = "WebFileHelper"

    /**
     * Get the file count where under the @param file, include both file and dir
     *
     * @param file the parent dir
     * @param excludeHideFile ignore the hidden file or not
     *
     * @return the file count
     */
    @Suppress("TooGenericExceptionCaught")
    fun listFilesCount(filePath: String?, excludeHideFile: Boolean = true): Int {
        if (filePath.isNullOrEmpty()) {
            return 0
        }
        try {
            return JavaFileHelper.list(File(filePath))?.filter {
                !excludeHideFile || !HiddenFileHelper.isHiddenFile(it)
            }?.size ?: 0
        } catch (e: Exception) {
            Log.w(TAG, "listFilesCount failed: $filePath, ${e.message}")
        }
        return 0
    }

    fun sortOrderVerify(sortOrder: Int): Boolean {
        return when (sortOrder) {
            SortHelper.FILE_NAME_ORDER,
            SortHelper.FILE_TYPE_ORDER,
            SortHelper.FILE_TIME_REVERSE_ORDER,
            SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER ->
                true
            else -> false
        }
    }

    fun recycleSortOrderVerify(sortOrder: Int): Boolean {
        return when (sortOrder) {
            SortHelper.FILE_NAME_ORDER,
            SortHelper.FILE_TYPE_ORDER,
            SortHelper.FILE_TIME_DELETE_ORDER,
            SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER ->
                true
            else -> false
        }
    }

    /**
     * 在owork的web写死的路径前缀为/storage/emulated/0，所以这里做判断
     */
    fun isLegalPathString(path: String?): Boolean {
        return !(path.isNullOrEmpty() || !path.startsWith("/storage/emulated/0", ignoreCase = true) || path.contains("../") || path.contains("./"))
    }
}