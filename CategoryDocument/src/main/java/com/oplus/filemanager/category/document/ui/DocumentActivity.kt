/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:/DocumentActivity.kt
 * * Description:Document Category Page
 * * Version:
 * * Date :2020/6/5
 * * Author:********
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>          <data>        <version>     <desc>
 * *  ZeJiang.Duan      2020/6/5        1.0           Create
 ****************************************************************/
package com.oplus.filemanager.category.document.ui

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.DragEvent
import android.view.Menu
import android.view.MenuItem
import android.view.View
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.dragselection.DragDropInterface
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.*
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.encrypt.EncryptActivity
import com.oplus.filemanager.category.document.R
import com.oplus.filemanager.category.document.ui.DocumentFragment.Companion.KEY_LAST_OPEN_TIME
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.labelmanager.AddFileLabelController
import com.oplus.selectdir.SelectPathController
import kotlin.collections.ArrayList

class DocumentActivity : EncryptActivity(), NavigationBarView.OnItemSelectedListener,
    NavigationInterface, TransformNextFragmentListener, BaseVMActivity.PermissonCallBack, DragDropInterface, IDraggingActionOperate {

    companion object {
        private const val TAG = "DocumentActivity"
        private const val TAG_DOC_PARENT_FRAGMENT = "doc_parent_fragment"
        /**
         * 侧边栏打开文档页面会传入包名，判断是否显示打开时间排序
         */
        const val KEY_FROM_PACKAGE = "from_package"
        const val FROM_PACKAGE_SIDE_BAR = "com.coloros.smartsidebar"
    }

    var mDocParentFragment: DocParentFragment? = null

    private var mExternalUri: String? = null
    private var mUri: Uri? = null
    private var mDocArray: ArrayList<String>? = null
    private var mTitle: String? = null
    private var mSql: String? = null

    private var mActionActivityResultListener: ActionActivityResultListener? = null
    private val mNavigationController by lazy { NavigationController(lifecycle, id = R.id.navigation_tool) }
    private val mSelectPathController by lazy { SelectPathController(lifecycle) }
    private val mAddFileLabelController by lazy { AddFileLabelController(lifecycle) }

    override fun getLayoutResId(): Int {
        return R.layout.activity_document
    }

    override fun initView() {
        registerVmChangedReceiver(null)
        mDocParentFragment = (supportFragmentManager.findFragmentByTag(TAG_DOC_PARENT_FRAGMENT) as? DocParentFragment) ?: DocParentFragment()
        supportFragmentManager.beginTransaction().replace(R.id.fragment_container_view, mDocParentFragment!!, TAG_DOC_PARENT_FRAGMENT).commit()
        initIntentData(mDocParentFragment!!)
    }

    private fun initIntentData(fragment: DocParentFragment): Boolean {
        mSql = IntentUtils.getString(intent, Constants.SQL)
        val resId = IntentUtils.getInt(intent, Constants.TITLE_RES_ID, -1)
        mTitle = if (resId > 0) getString(resId) else ""
        if (mTitle.isNullOrEmpty()) {
            mTitle = IntentUtils.getString(intent, Constants.TITLE)
        }
        if (mTitle.isNullOrEmpty()) {
            mTitle = MyApplication.sAppContext.getString(com.filemanager.common.R.string.string_documents)
        }
        intent.putStringArrayListExtra(
            Constants.DOCUMENT_FORMAT_ARRAY,
            Injector.injectFactory<IDocumentExtensionType>()?.getDocumentFormat(this)
        )
        mDocArray = IntentUtils.getStringArrayList(intent, Constants.DOCUMENT_FORMAT_ARRAY)
        mUri = intent.data
        mExternalUri = IntentUtils.getString(intent, Constants.EXTERNALURI)
        val fromPackage = IntentUtils.getString(intent, KEY_FROM_PACKAGE)
        var displayOrderOpenTime = fromPackage == FROM_PACKAGE_SIDE_BAR
        Log.d(
            TAG, "initIntentData mSql = $mSql ,\n mTitle = $mTitle \n" +
                    " mDocArray = $mDocArray ,\n mUri = $mUri ,\n mExternalUri = $mExternalUri \n fromPackage:$fromPackage"
        )

        if (mSql == null) {
            mSql = Injector.injectFactory<IDocumentExtensionType>()?.getDocumentCountSqlQuery(this)
        }

        if (IntentUtils.getString(intent, Constants.KEY_SOURCE_APP_NAME).isNullOrEmpty().not()) {
            displayOrderOpenTime = true
            StatisticsUtils.onCommon(MyApplication.sAppContext, StatisticsUtils.RECENT_FILE_CARD_CLICK)
        }

        if (null == mUri && null != mExternalUri) {
            //open by outside, not need filter
            Utils.setFilterState(false)
            mUri = Uri.parse(mExternalUri)
        } else {
            Utils.setFilterState(true)
        }
        if (mUri == null) {
            mUri = UriHelper.geCategoryUri(CategoryHelper.CATEGORY_DOC)
        }
        if (null == mUri && TextUtils.isEmpty(mSql) && TextUtils.isEmpty(mTitle)) {
            Log.e(TAG, "initIntentData data(uri, sql, title) error")
            finish()
            return false
        }

        val bundle = Bundle().apply {
            putString(KtConstants.P_SQL, mSql)
            putString(Constants.TITLE, mTitle)
            putStringArrayList(Constants.DOCUMENT_FORMAT_ARRAY, mDocArray)
            putString(KtConstants.P_URI, mUri.toString())
            putString(Constants.EXTERNALURI, mExternalUri)
            putBoolean(KEY_LAST_OPEN_TIME, displayOrderOpenTime)
        }
        fragment.arguments = bundle
        return true
    }

    override fun initData() {
    }

    override fun startObserve() {
    }

    override fun onPermissionSuccess(isInstallPermission: Boolean?) {
        super.onPermissionSuccess(isInstallPermission)
        mDocParentFragment?.refreshCurrentFragment()
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        mDocParentFragment?.refreshCurrentFragment()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        mDocParentFragment?.onCreateOptionsMenu(menu, menuInflater)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return mDocParentFragment?.onOptionsItemSelected(item) ?: super.onOptionsItemSelected(item)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mActionActivityResultListener?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        return mDocParentFragment?.onNavigationItemSelected(item) ?: false
    }

    override fun onBackPressed() {
        if (mDocParentFragment?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mDocParentFragment?.onUIConfigChanged(configList)
        mSelectPathController.updateDialogHeightIfNeed(supportFragmentManager)
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
    }

    override fun showNavigation() {
        mNavigationController.showNavigation(this)
        updateNavigationToolPadding()
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean, hasAndroidData: Boolean) {
        mNavigationController.setNavigateItemAble(isEnable, mHasDrm)
    }

    override fun hideNavigation() {
        mNavigationController.hideNavigation(this)
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
        mActionActivityResultListener = actionActivityResultListener
    }

    override fun transformToNextFragment(path: String?) {
        mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        mDocParentFragment?.mDocParentViewModel?.let {
            mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, code)
        }
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        mSelectPathController.onDestroy()
        mDocParentFragment?.getCurrentFragment()?.fromSelectPathResult(code, paths)
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        mAddFileLabelController.showAddLabelFragment(supportFragmentManager, fileList)
    }

    override fun onUpdatedLabel() {
        mDocParentFragment?.updatedLabel()
    }

    override fun hasShowPanel(): Boolean {
        return mSelectPathController.hasShowPanel() || mAddFileLabelController.hasShowPanel(supportFragmentManager)
    }

    override fun backtoTop() {
        super.backtoTop()
        mDocParentFragment?.backToTop()
    }

    override fun onRefreshData() {
        mDocParentFragment?.onResumeLoadData()
    }

    override fun handleNoStoragePermission() {
        mDocParentFragment?.setPermissionEmptyVisible(View.VISIBLE)
    }

    override fun updateNavigationToolPadding() {
        mNavigationController.updateNavigationToolPadding(navPaddingBottom)
    }

    override fun handleDragEvent(event: DragEvent?): Boolean? {
        return event?.let { mDocParentFragment?.handleDragScroll(it) }
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        return mDocParentFragment?.getSelectedItemView()
    }

    override fun setNavigateItemAble() {
        mDocParentFragment?.setNavigateItemAble()
    }

    override fun getDragCurrentPath(): String? {
        return null
    }
}

