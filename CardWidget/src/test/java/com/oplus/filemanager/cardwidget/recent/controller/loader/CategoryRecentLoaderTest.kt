package com.oplus.filemanager.cardwidget.recent.controller.loader

import android.content.Context
import android.content.SharedPreferences
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.SdkUtils
import com.oplus.filemanager.cardwidget.recent.data.SUPER_APP_QQ_NAME_LOWERCASE
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.recent.entity.recent.MediaEntity
import com.oplus.filemanager.recent.entity.recent.RecentFileEntity
import com.oplus.filemanager.recent.task.BaseMediaDBTask
import com.oplus.filemanager.recent.task.LoadMediaDBTaskQ
import com.oplus.filemanager.recent.task.LoadMediaDBTaskR
import com.oplus.filemanager.recent.utils.RecentUtils
import io.mockk.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import java.io.File
import kotlin.test.assertEquals
import kotlin.test.assertNull

/**
 * CategoryRecentLoader的单元测试类
 * 用于测试CategoryRecentLoader类的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class CategoryRecentLoaderTest {

    private lateinit var loader: CategoryRecentLoader
    private val type = CategoryHelper.CATEGORY_IMAGE  // 测试用的文件类型，设置为图片类型
    private val size = 10  // 测试用的文件数量限制
    private val name = "test"  // 测试用的名称

    private val context = RuntimeEnvironment.application  // 使用Robolectric提供的测试上下文

    @OptIn(ExperimentalCoroutinesApi::class)
    private val testDispatcher = StandardTestDispatcher()  // 用于测试的协程调度器

    /**
     * 测试前的初始化方法
     * 1. 设置主调度器为测试调度器
     * 2. 模拟MyApplication
     * 3. 初始化测试对象
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    @Before
    fun setUp() {
        Dispatchers.setMain(testDispatcher)  // 设置主调度器为测试调度器
        
        // 模拟MyApplication
        mockkObject(MyApplication)
        every { MyApplication.appContext } returns context  // 返回测试上下文
        every { MyApplication.sAppContext } returns context  // 返回测试上下文
        
        loader = CategoryRecentLoader(type, size, name)  // 初始化测试对象
    }

    /**
     * 测试后的清理方法
     * 1. 重置主调度器
     * 2. 清除所有模拟对象
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    @After
    fun tearDown() {
        Dispatchers.resetMain()  // 重置主调度器
        clearAllMocks()  // 清除所有模拟对象
    }

    /**
     * 测试getSuperAppPaths方法对于标准分类类型应返回null
     * 验证标准分类类型(文档、压缩包、APK、音频、视频、图片、最近文件)不会返回超级应用路径
     */
    @Test
    fun `getSuperAppPaths should return null for standard category types`() {
        // Given
        val standardTypes = listOf(
            CategoryHelper.CATEGORY_DOC,
            CategoryHelper.CATEGORY_COMPRESS,
            CategoryHelper.CATEGORY_APK,
            CategoryHelper.CATEGORY_AUDIO,
            CategoryHelper.CATEGORY_VIDEO,
            CategoryHelper.CATEGORY_IMAGE,
            CategoryHelper.CATEGORY_RECENT
        )
        
        for (standardType in standardTypes) {
            val loader = CategoryRecentLoader(standardType, size, name)
            
            // When & Then
            assertNull(getSuperAppPaths(loader))  // 验证标准类型返回null
        }
    }

    /**
     * 测试当注入器返回null时，getSuperAppPaths应返回null
     * 模拟Injector返回null的情况
     */
    @Test
    fun `getSuperAppPaths should return null when injector returns null`() {
        // Given
        val customType = 999  // 自定义类型
        val loader = CategoryRecentLoader(customType, size, name)
        
        mockkObject(Injector)
        every { Injector.injectFactory<ISuperApp>() } returns null  // 模拟Injector返回null
        
        // When & Then
        assertNull(getSuperAppPaths(loader))  // 验证返回null
    }

    /**
     * 测试当超级应用项为空时，getSuperAppPaths应返回null
     * 模拟超级应用返回空列表的情况
     */
    @Test
    fun `getSuperAppPaths should return null when superApp items are null or empty`() {
        // Given
        val customType = 999  // 自定义类型
        val loader = CategoryRecentLoader(customType, size, name)
        
        val superApp = mockk<ISuperApp>()
        every { superApp.getCategoryItems(any()) } returns mutableListOf()  // 模拟返回空列表
        
        mockkObject(Injector)
        every { Injector.injectFactory<ISuperApp>() } returns superApp  // 模拟返回超级应用对象
        
        // When & Then
        assertNull(getSuperAppPaths(loader))  // 验证返回null
    }

    // 以下是通过反射访问私有方法的辅助方法

    /**
     * 通过反射调用getSuperAppPaths私有方法
     * @param loader CategoryRecentLoader实例
     * @return 超级应用路径数组或null
     */
    private fun getSuperAppPaths(loader: CategoryRecentLoader): Array<String>? {
        return try {
            val method = CategoryRecentLoader::class.java.getDeclaredMethod("getSuperAppPaths")
            method.isAccessible = true  // 设置方法可访问
            method.invoke(loader) as Array<String>?  // 调用方法并转换结果
        } catch (e: Exception) {
            null  // 异常时返回null
        }
    }

    /**
     * 通过反射调用getFullPath私有方法
     * @param paths 原始路径数组
     * @return 完整路径数组
     */
    private fun getFullPath(paths: Array<String>): Array<String> {
        return try {
            val method = CategoryRecentLoader::class.java.getDeclaredMethod("getFullPath", Array<String>::class.java)
            method.isAccessible = true  // 设置方法可访问
            method.invoke(loader, paths) as Array<String>  // 调用方法并转换结果
        } catch (e: Exception) {
            arrayOf()  // 异常时返回空数组
        }
    }
}