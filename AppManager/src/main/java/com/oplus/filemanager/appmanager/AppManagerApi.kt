/***********************************************************
 * * Copyright (C), 2020 - 2020, Oplus. All rights reserved.
 * * File: AppManagerApi
 * * Description: api for appmanager feature
 * * Version: 1.0
 * * Date : 2022/3/30
 * * Author:yanx<PERSON><PERSON><PERSON>@myoas.com
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>              <data>      <version >        <desc>
 * * <EMAIL>    2022/3/30       1.0         API for appmanager feature
 ****************************************************************/
package com.oplus.filemanager.appmanager

import android.app.Activity
import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.reddot.COUIHintRedDot
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.constants.Constants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.ModelUtils
import com.oplus.filemanager.interfaze.categoryappamanger.ICategoryAppManagerApi
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean

object AppManagerApi : ICategoryAppManagerApi {
    private const val TAG = "AppManagerHelp"
    private const val JUMP_URL: String = "oaps://mk/app/manager"
    private const val PKG_MK_HEYTAP_EXPORT = "com.heytap.market"
    private const val VERSION_MK_HEYTAP_EXPORT: Long = 91000
    private const val MARKET_URL = "content://mk_ex"
    private const val APP_MANAGER_LAST_RECORD_INDEX = 14
    private const val TRY_MAX_TIME = 8
    private const val SLEEP_TIME = 1000L

    private val hasComponent by lazy {
        !(ModelUtils.isTablet() || !Utils.isRealmePhone() || ModelUtils.isEURegion())
    }

    override fun isAppManagerEnable(): Boolean {
        if (!hasComponent) {
            return false
        }

        if (!Utils.checkAppStateEnable(appContext, PKG_MK_HEYTAP_EXPORT)) {
            Log.w(TAG, "startUnknownFiles: new market not exist !")
            return false
        }

        val version = getVersionCode(appContext, PKG_MK_HEYTAP_EXPORT)
        return checkVersion(version)
    }

    override fun getInstallAppNumber(): Long {
        if (!hasComponent) {
            return 0
        }
        val info = appContext.packageManager.getInstalledPackages(0)
        if (info.size <= 0) {
            return 0
        }
        var number: Long = 0
        for (packageInfo in info) {
            if (packageInfo.applicationInfo?.run { flags and ApplicationInfo.FLAG_SYSTEM } == 0) {
                number++
            }
        }
        return number
    }

    @Suppress("TooGenericExceptionCaught")
    override fun entryAppManager(activity: Activity) {
        if (!hasComponent) {
            return
        }

        Log.i(TAG, "entryAppmanager start")
        if (!checkAppStoreEnabled(activity)) {
            return
        }
        StatisticsUtils.onCommon(appContext, StatisticsUtils.EVENT_CLICK_APPMANAGER)
        StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_APP_MANAGER, Constants.PAGE_MAIN)
        runCatching {
            val url = "$JUMP_URL?goback=2"
            val intent = Intent()
            intent.apply {
                action = Intent.ACTION_VIEW
                addCategory(Intent.CATEGORY_DEFAULT)
                `package` = PKG_MK_HEYTAP_EXPORT
                data = Uri.parse(url)
                putExtra("app_uninstall_visible", false)
            }
            activity.startActivity(intent)
        }.onFailure {
            Log.d(TAG, it.message)
        }
    }

    override fun getUpdateAppNumber(): Int {
        if (!hasComponent) {
            return 0
        }

        var tryTime = 0
        var appNumber = -1
        while (tryTime < TRY_MAX_TIME) {
            appNumber = getNumberOnece()
            if (appNumber != -1) {
                return appNumber
            }
            tryTime++
            Thread.sleep(SLEEP_TIME)
        }
        return appNumber
    }

    override fun getAppManagerItem(context: Context): MainCategoryItemsBean? {
        if (!hasComponent) {
            return null
        }
        val lastDate = Injector.injectFactory<ISuperApp>()?.getCategoryItemLastData(
            APP_MANAGER_LAST_RECORD_INDEX,
            context
        ) ?: return null
        val resources = context.resources
        val appName = resources.getString(com.filemanager.common.R.string.string_appmanager)
        return MainCategoryItemsBean(
            CategoryHelper.CATEGORY_APPMANAGER,
            appName,
            R.drawable.main_category_appmanager,
            lastDate[0],
            lastDate[1],
            null,
            CategoryHelper.ITEM_APPMANAGER,
            R.drawable.main_category_appmanager_bg
        )
    }

    override fun getAppManagerView(parent: ViewGroup): View? {
        return if (!hasComponent) {
            null
        } else {
            LayoutInflater.from(parent.context)
                .inflate(R.layout.main_category_appmanager_item, parent, false)
        }
    }

    override fun getAppUpdateTextView(holdView: View): COUIHintRedDot? {
        return if (!hasComponent) {
            null
        } else {
            holdView.findViewById(R.id.list_item_appmanager_update)
        }
    }

    fun getVersionCode(context: Context, packageName: String): Long {
        var versionCode: Long = -1
        try {
            val info =
                context.packageManager.getPackageInfo(packageName, PackageManager.GET_META_DATA)
            if (info != null) {
                versionCode = info.longVersionCode
            }
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, e.message)
        }
        return versionCode
    }

    fun checkAppStoreEnabled(context: Context?): Boolean {
        return if (AppUtils.isAppInstalledByPkgName(context, PKG_MK_HEYTAP_EXPORT)) {
            KtAppUtils.checkAppEnabledWithDialog(
                context!!,
                PKG_MK_HEYTAP_EXPORT,
                com.filemanager.common.R.string.app_store_disable_message
            )
        } else {
            false
        }
    }

    private fun checkVersion(versionCode: Long): Boolean {
        return versionCode >= VERSION_MK_HEYTAP_EXPORT
    }

    @Suppress("TooGenericExceptionCaught")
    fun getNumberOnece(): Int {
        return runCatching {
            val resolver: ContentResolver = appContext.contentResolver
            val number = resolver.acquireUnstableContentProviderClient(Uri.parse(MARKET_URL)).use {
                val bundle: Bundle? = it?.call("queryUpdateApps", null, null)
                val num = bundle?.getInt("update_num") ?: 0
                num
            }
            Log.i(TAG, "getupdateAppNumber  num $number")
            number
        }.onFailure {
            Log.d(TAG, it.message)
        }.getOrDefault(-1)
    }
}