/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ShareFileCheckUtils
 * * Description : 碰一碰分享的文件判断是否支持分享的工具类
 * * Version     : 1.0
 * * Date        : 2025/05/19
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.framework.touchshare.utils

import androidx.annotation.StringRes
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.DFMMediaFile
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.base.ThirdAppFileWrapper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.thread.ThreadUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log

object ShareFileCheckUtils {
    private const val TAG = "ShareFileCheckUtils"
    private const val MAX_COUNT = 99

    private val searchCategorySet by lazy {
        val set = mutableSetOf<Int>()
        // search tab
        set.add(Integer.MIN_VALUE) // TAB_ALL
        set.add(CategoryHelper.CATEGORY_IMAGE)
        set.add(CategoryHelper.CATEGORY_VIDEO)
        set.add(CategoryHelper.CATEGORY_AUDIO)
        set.add(CategoryHelper.CATEGORY_DOC)
        set.add(CategoryHelper.CATEGORY_APK)
        set.add(CategoryHelper.CATEGORY_COMPRESS)
        // search more
        set.add(CategoryHelper.CATEGORY_SEARCH)
        set.add(CategoryHelper.CATEGORY_SEARCH_LOCAL_FILE)
        set.add(CategoryHelper.CATEGORY_SEARCH_DRIVE_FILE)
        set.add(CategoryHelper.CATEGORY_SEARCH_THIRD_APP)
        set.add(CategoryHelper.CATEGORY_POSITION_GROUP_SUB_LIST_REMOTE_MAC)
        set
    }

    /**
     * 判断是否支持分享
     * 1. 非本地文件不支持分享，例如：私密文件，云文档，dfm文件，远程mac文件，最近删除文件
     * 2. 文件个数不能超过99个
     */
    @JvmStatic
    fun isSupport(from: Int, files: List<BaseFileBean>): Boolean {
        val count = files.size
        if (count > MAX_COUNT) {
            Log.e(TAG, "isSupport file count:$count > 99 !!!")
            showToast(com.filemanager.common.R.string.share_over_count_toast)
            return false
        }
        if (CategoryHelper.CATEGORY_RECYCLE_BIN == from) { // 最近删除
            Log.e(TAG, "isSupport file has deleted")
            showToast(com.filemanager.common.R.string.share_not_support_deleted_file_toast)
            return false
        }

        if (CategoryHelper.CATEGORY_PRIVATE_SAVE == from) { // 私密文件
            Log.e(TAG, "isSupport file is private")
            showToast(com.filemanager.common.R.string.touch_share_not_support_private_file_toast)
            return false
        }

        // 非本地文件
        if (CategoryHelper.isDfmType(from) || CategoryHelper.isCloudDriveCategoryType(from) || CategoryHelper.isRemoteMacDeviceType(from)) {
            Log.e(TAG, "isSupport file is non local file")
            showToast(com.filemanager.common.R.string.share_non_local_file_need_download_toast)
            return false
        }

        if (searchCategorySet.contains(from)) { // 搜索界面，判断云文档，远程mac文件，dfm文件, 三方应用文件
            val hasNonLocalFile = files.filter {
                it is DriveFileWrapper || it is RemoteFileBean || it is DFMMediaFile || it is ThirdAppFileWrapper
            }.isNotEmpty()
            Log.w(TAG, "isSupport file is from search, has non local file:$hasNonLocalFile")
            if (hasNonLocalFile) {
                showToast(com.filemanager.common.R.string.share_non_local_file_need_download_toast)
                return false
            }
        }
        return true
    }

    @JvmStatic
    private fun showToast(@StringRes toastRes: Int) {
        ThreadUtils.runOnMainThread {
            CustomToast.showLong(toastRes)
        }
    }
}