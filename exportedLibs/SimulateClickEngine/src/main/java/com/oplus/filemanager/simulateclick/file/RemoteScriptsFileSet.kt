/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - RemoteScriptsFileSet.kt
 * Description:
 *     Define the data class for remote scripts collection file.
 *
 * Version: 1.0
 * Date: 2024-06-12
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><EMAIL>    2024-06-12   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.file

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.oplus.filemanager.simulateclick.engine.Script
import com.oplus.filemanager.simulateclick.engine.utils.Log
import com.oplus.filemanager.simulateclick.engine.utils.getAppPackageName
import java.io.File
import java.io.FileReader
import java.io.Reader

/**
 * Due to can only receive one file from one cloud config service api.
 * Use a collection file to contains all necessary remote scripts and push it to client.
 *
 * File format:
 * {
 *     "<app package name>": {
 *          "<script json file name>": {
 *              <script json file content, see [Script]>
 *          }
 *     }
 *     ... ...
 * }
 */
internal class RemoteScriptsFileSet @VisibleForTesting internal constructor(
    private val allFilesData: Map<String, List<Script>>
) {
    fun getTargetAppPackages(): Collection<String> = allFilesData.keys

    fun getFilesForTargetApp(packageName: String): List<Script>? = allFilesData[packageName]

    override fun toString(): String = StringBuilder().apply {
        append("{ ")
        allFilesData.forEach { (app, scripts) ->
            append("$app : scripts.size=${scripts.size}, ")
        }
        append("}")
    }.toString()

    companion object {
        private const val TAG = "RemoteScriptsFileSet"

        /**
         * [allFilesData] is not match the format design of scripts collection file.
         * So customize json decode logics.
         */
        @JvmStatic
        fun fromJson(context: Context, gson: Gson, reader: Reader): RemoteScriptsFileSet? {
            val jsonData = gson.fromJson(reader, JsonObject::class.java) ?: return null
            val allFilesData = mutableMapOf<String, MutableList<Script>>()
            jsonData.keySet().forEach { targetAppPkg ->
                val subData = jsonData.getAsJsonObject(targetAppPkg)
                val appFilesData = allFilesData[targetAppPkg] ?: mutableListOf()
                subData.keySet().forEach { fileName ->
                    val originJsonData = subData.get(fileName)
                    val fileContent = gson.fromJson(originJsonData, Script::class.java)
                    fileContent.applyExpectedFilePath(context, targetAppPkg, fileName)
                    fileContent.originFileJson = originJsonData.toString()
                    if (targetAppPkg == fileContent.getAppPackageName()) {
                        appFilesData.add(fileContent)
                    } else {
                        val errInfo =
                            "$targetAppPkg is not matched script app_pkg, script=$fileName"
                        Log.e(TAG, "fromJson: drop since $errInfo")
                    }
                }
                if (appFilesData.isNotEmpty()) {
                    allFilesData[targetAppPkg] = appFilesData
                }
            }
            if (allFilesData.isEmpty()) {
                return null
            }
            return RemoteScriptsFileSet(allFilesData)
        }

        @JvmStatic
        private fun Script.applyExpectedFilePath(
            context: Context,
            targetAppPkg: String,
            fileName: String
        ) {
            val appDirPath = ScriptFilePathUtils.getAppScriptDirPath(targetAppPkg)
            val appDir = ScriptFilePathUtils.getRemoteScriptFileOrDir(context, appDirPath)
            filePath = File(appDir, fileName).absolutePath
        }

        /**
         * To conveniently compare remote scripts collection and received script files.
         * Read all script files under [receivedDir] and assemble as [RemoteScriptsFileSet].
         */
        @JvmStatic
        fun fromReceivedDir(gson: Gson, receivedDir: File): RemoteScriptsFileSet? {
            if (!receivedDir.exists() || !receivedDir.isDirectory) {
                return null
            }
            val appDirs = receivedDir.listFiles { file: File -> file.isDirectory } ?: return null
            val allFilesData = mutableMapOf<String, MutableList<Script>>()
            appDirs.forEach forEachDir@{ dir ->
                val appFiles = dir.listFiles { file: File -> file.isFile } ?: return@forEachDir
                val targetAppPkg = ScriptFilePathUtils.getAppScriptPackageName(dir.name)
                val appFilesData = allFilesData[targetAppPkg] ?: mutableListOf()
                appFiles.forEach forEachFile@{ file ->
                    val fileContent = readScriptFile(gson, file) ?: return@forEachFile
                    appFilesData.add(fileContent)
                }
                if (appFilesData.isNotEmpty()) {
                    allFilesData[targetAppPkg] = appFilesData
                }
            }
            if (allFilesData.isEmpty()) {
                return null
            }
            return RemoteScriptsFileSet(allFilesData)
        }

        @JvmStatic
        private fun readScriptFile(gson: Gson, file: File): Script? =
            kotlin.runCatching {
                FileReader(file).use {
                    gson.fromJson(it, Script::class.java)
                }
            }.onFailure {
                Log.e(TAG, "fromReceivedDir: ERROR when read $file")
            }.getOrNull()?.also {
                it.filePath = file.absolutePath
            }
    }
}