{"layout_width": "match_parent", "layout_height": "match_parent", "package": "com.coloros.filemanager", "background": "@drawable/ic_card_background", "id": "container", "type": "constraint", "child": [{"padding": "0dp", "layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "@dimen/dimen_32dp", "textSize": "@dimen/font_size_12", "textColor": "?attr/couiColorLabelTheme", "textAppearance": "@style/couiTextButtonS", "fontFamily": "sans-serif-medium", "layout_marginEnd": "@dimen/dimen_16dp", "layout_marginStart": "@dimen/dimen_16dp", "layout_marginBottom": "@dimen/dimen_16dp", "type": "button", "layout_constraintStart_toStartOf": "parent", "layout_marginTop": "@dimen/dimen_30dp", "layout_constraintBottom_toBottomOf": "parent", "background": "@drawable/card_action_bg", "id": "action", "text": "@string/empty_file_label_card_action_text"}, {"layout_constraintStart_toEndOf": "label_card_icon", "layout_width": "wrap_content", "layout_height": "wrap_content", "textSize": "@dimen/font_size_14", "layout_marginStart": "@dimen/dimen_6dp", "fontFamily": "sans-serif-medium", "textAppearance": "@style/couiTextBodyS", "id": "label_title", "maxLines": "1", "textColor": "?attr/couiColorLabelPrimary", "layout_constraintTop_toTopOf": "parent", "type": "text", "layout_marginTop": "@dimen/dimen_16dp"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "wrap_content", "layout_height": "wrap_content", "paddingEnd": "@dimen/dimen_16dp", "paddingStart": "@dimen/dimen_16dp", "id": "tips", "text": "@string/empty_file_label_card_summary", "layout_constraintTop_toTopOf": "parent", "type": "text", "maxLines": "2", "ellipsize": "end", "fontFamily": "sans-serif-medium", "textColor": "?attr/couiColorLabelPrimary", "textAppearance": "@style/couiTextHeadlineXS", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintVertical_bias": "0.44"}, {"layout_width": "@dimen/dimen_14dp", "layout_height": "@dimen/dimen_14dp", "layout_constraintBottom_toBottomOf": "label_title", "src": "@drawable/ic_label_card", "layout_marginStart": "@dimen/dimen_16dp", "id": "label_card_icon", "layout_constraintTop_toTopOf": "label_title", "type": "image", "layout_constraintStart_toStartOf": "parent"}]}