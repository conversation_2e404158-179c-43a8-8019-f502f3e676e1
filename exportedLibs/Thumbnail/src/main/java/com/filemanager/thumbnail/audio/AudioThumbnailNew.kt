/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.audio
 ** Version: 1.0
 ** Date: 2021/5/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.audio

import androidx.annotation.Keep

/**
 * construct the input object for Extracting audio's thumbnail
 */
@Keep
class AudioThumbnailNew(
        val mFilePath: String,
        val mLastModified: Long,
        val mSize: Long
) {

    private var mHashCode = 0

    override fun hashCode(): Int {
        if(mHashCode == 0) {
            mHashCode = mFilePath.hashCode()
            mHashCode = 31 * mHashCode + mLastModified.hashCode()
            mHashCode = 31 * mHashCode + mSize.hashCode()
        }
        return mHashCode
    }

    override fun equals(other: Any?): Boolean {
        if(other is AudioThumbnailNew) {
            return (mFilePath == other.mFilePath) &&
                    (mLastModified == other.mLastModified) &&
                    (mSize == other.mSize)
        }
        return false
    }
}