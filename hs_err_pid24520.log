#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes for Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:110), pid=24520, tid=17744
#
# JRE version: Java(TM) SE Runtime Environment (17.0.8+9) (build 17.0.8+9-LTS-211)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\Ad\AdNull\build\20250901_3004967216914650746.compiler.options

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Sep  1 12:32:52 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 6.245640 seconds (0d 0h 0m 6s)

---------------  T H R E A D  ---------------

Current thread (0x000001bf2d282840):  JavaThread "main" [_thread_in_vm, id=17744, stack(0x0000009bcfd00000,0x0000009bcfe00000)]

Stack: [0x0000009bcfd00000,0x0000009bcfe00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x677d0a]
V  [jvm.dll+0x7d8c54]
V  [jvm.dll+0x7da3fe]
V  [jvm.dll+0x7daa63]
V  [jvm.dll+0x245c5f]
V  [jvm.dll+0x7d4d5b]
V  [jvm.dll+0x61dcf6]
V  [jvm.dll+0x1c0127]
V  [jvm.dll+0x620650]
V  [jvm.dll+0x61e6b6]
V  [jvm.dll+0x24362c]
V  [jvm.dll+0x6ce69d]
V  [jvm.dll+0x6cf09f]
V  [jvm.dll+0x36ea05]
V  [jvm.dll+0x36dab4]
V  [jvm.dll+0x36da58]
V  [jvm.dll+0x37d88a]
C  0x000001bf3d7ab82d

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  org.jetbrains.kotlin.descriptors.impl.AbstractTypeAliasDescriptor.<init>(Lorg/jetbrains/kotlin/storage/StorageManager;Lorg/jetbrains/kotlin/descriptors/DeclarationDescriptor;Lorg/jetbrains/kotlin/descriptors/annotations/Annotations;Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/descriptors/SourceElement;Lorg/jetbrains/kotlin/descriptors/DescriptorVisibility;)V+85
j  org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedTypeAliasDescriptor.<init>(Lorg/jetbrains/kotlin/storage/StorageManager;Lorg/jetbrains/kotlin/descriptors/DeclarationDescriptor;Lorg/jetbrains/kotlin/descriptors/annotations/Annotations;Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/descriptors/DescriptorVisibility;Lorg/jetbrains/kotlin/metadata/ProtoBuf$TypeAlias;Lorg/jetbrains/kotlin/metadata/deserialization/NameResolver;Lorg/jetbrains/kotlin/metadata/deserialization/TypeTable;Lorg/jetbrains/kotlin/metadata/deserialization/VersionRequirementTable;Lorg/jetbrains/kotlin/serialization/deserialization/descriptors/DeserializedContainerSource;)V+77
j  org.jetbrains.kotlin.serialization.deserialization.MemberDeserializer.loadTypeAlias(Lorg/jetbrains/kotlin/metadata/ProtoBuf$TypeAlias;)Lorg/jetbrains/kotlin/descriptors/TypeAliasDescriptor;+224
j  org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedMemberScope$OptimizedImplementation.createTypeAlias(Lorg/jetbrains/kotlin/name/Name;)Lorg/jetbrains/kotlin/descriptors/TypeAliasDescriptor;+71
j  org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedMemberScope$OptimizedImplementation.access$createTypeAlias(Lorg/jetbrains/kotlin/serialization/deserialization/descriptors/DeserializedMemberScope$OptimizedImplementation;Lorg/jetbrains/kotlin/name/Name;)Lorg/jetbrains/kotlin/descriptors/TypeAliasDescriptor;+2
j  org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedMemberScope$OptimizedImplementation$typeAliasByName$1.invoke(Lorg/jetbrains/kotlin/name/Name;)Lorg/jetbrains/kotlin/descriptors/TypeAliasDescriptor;+11
j  org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedMemberScope$OptimizedImplementation$typeAliasByName$1.invoke(Ljava/lang/Object;)Ljava/lang/Object;+5
J 2791 c1 org.jetbrains.kotlin.storage.LockBasedStorageManager$MapBasedMemoizedFunction.invoke(Ljava/lang/Object;)Ljava/lang/Object; (410 bytes) @ 0x000001bf3691a364 [0x000001bf36919a20+0x0000000000000944]
J 2737 c1 org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedMemberScope.getContributedClassifier(Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Lorg/jetbrains/kotlin/descriptors/ClassifierDescriptor; (63 bytes) @ 0x000001bf368f4124 [0x000001bf368f3460+0x0000000000000cc4]
J 2735 c1 org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedPackageMemberScope.getContributedClassifier(Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Lorg/jetbrains/kotlin/descriptors/ClassifierDescriptor; (26 bytes) @ 0x000001bf368ed9dc [0x000001bf368ece40+0x0000000000000b9c]
j  org.jetbrains.kotlin.load.java.lazy.descriptors.JvmPackageScope.getContributedClassifier(Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Lorg/jetbrains/kotlin/descriptors/ClassifierDescriptor;+83
j  org.jetbrains.kotlin.resolve.scopes.ChainedMemberScope.getContributedClassifier(Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Lorg/jetbrains/kotlin/descriptors/ClassifierDescriptor;+54
j  org.jetbrains.kotlin.resolve.scopes.AbstractScopeAdapter.getContributedClassifier(Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Lorg/jetbrains/kotlin/descriptors/ClassifierDescriptor;+18
j  org.jetbrains.kotlin.resolve.AllUnderImportScope.getContributedClassifier(Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Lorg/jetbrains/kotlin/descriptors/ClassifierDescriptor;+33
j  org.jetbrains.kotlin.resolve.lazy.LazyImportScope$getClassifier$1.invoke()Lorg/jetbrains/kotlin/descriptors/ClassifierDescriptor;+61
j  org.jetbrains.kotlin.resolve.lazy.LazyImportScope$getClassifier$1.invoke()Ljava/lang/Object;+1
j  org.jetbrains.kotlin.storage.LockBasedStorageManager.compute(Lkotlin/jvm/functions/Function0;)Ljava/lang/Object;+19
j  org.jetbrains.kotlin.resolve.lazy.LazyImportScope.getClassifier(Lorg/jetbrains/kotlin/resolve/lazy/LazyImportResolver;Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Lorg/jetbrains/kotlin/descriptors/ClassifierDescriptor;+21
j  org.jetbrains.kotlin.resolve.lazy.LazyImportScope.getContributedClassifier(Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Lorg/jetbrains/kotlin/descriptors/ClassifierDescriptor;+19
j  org.jetbrains.kotlin.resolve.scopes.ResolutionScope$DefaultImpls.getContributedClassifierIncludeDeprecated(Lorg/jetbrains/kotlin/resolve/scopes/ResolutionScope;Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Lorg/jetbrains/kotlin/descriptors/DescriptorWithDeprecation;+15
j  org.jetbrains.kotlin.resolve.scopes.HierarchicalScope$DefaultImpls.getContributedClassifierIncludeDeprecated(Lorg/jetbrains/kotlin/resolve/scopes/HierarchicalScope;Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Lorg/jetbrains/kotlin/descriptors/DescriptorWithDeprecation;+18
j  org.jetbrains.kotlin.resolve.scopes.ImportingScope$DefaultImpls.getContributedClassifierIncludeDeprecated(Lorg/jetbrains/kotlin/resolve/scopes/ImportingScope;Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Lorg/jetbrains/kotlin/descriptors/DescriptorWithDeprecation;+18
j  org.jetbrains.kotlin.resolve.lazy.LazyImportScope.getContributedClassifierIncludeDeprecated(Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Lorg/jetbrains/kotlin/descriptors/DescriptorWithDeprecation;+3
j  org.jetbrains.kotlin.resolve.scopes.utils.ScopeUtilsKt.findFirstClassifierWithDeprecationStatus(Lorg/jetbrains/kotlin/resolve/scopes/HierarchicalScope;Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Lorg/jetbrains/kotlin/descriptors/DescriptorWithDeprecation;+54
j  org.jetbrains.kotlin.resolve.QualifiedExpressionResolver.findClassifierAndReportDeprecationIfNeeded(Lorg/jetbrains/kotlin/resolve/scopes/LexicalScope;Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/KotlinLookupLocation;Lorg/jetbrains/kotlin/psi/KtExpression;Lorg/jetbrains/kotlin/resolve/BindingTrace;)Lorg/jetbrains/kotlin/descriptors/ClassifierDescriptor;+34
j  org.jetbrains.kotlin.resolve.QualifiedExpressionResolver.resolveDescriptorForType(Lorg/jetbrains/kotlin/psi/KtUserType;Lorg/jetbrains/kotlin/resolve/scopes/LexicalScope;Lorg/jetbrains/kotlin/resolve/BindingTrace;Z)Lorg/jetbrains/kotlin/resolve/QualifiedExpressionResolver$TypeQualifierResolutionResult;+84
j  org.jetbrains.kotlin.resolve.TypeResolver.resolveDescriptorForType(Lorg/jetbrains/kotlin/resolve/scopes/LexicalScope;Lorg/jetbrains/kotlin/psi/KtUserType;Lorg/jetbrains/kotlin/resolve/BindingTrace;Z)Lorg/jetbrains/kotlin/resolve/QualifiedExpressionResolver$TypeQualifierResolutionResult;+113
j  org.jetbrains.kotlin.resolve.TypeResolver$resolveTypeElement$1.visitUserType(Lorg/jetbrains/kotlin/psi/KtUserType;)V+44
j  org.jetbrains.kotlin.psi.KtVisitorVoid.visitUserType(Lorg/jetbrains/kotlin/psi/KtUserType;Ljava/lang/Void;)Ljava/lang/Void;+12
j  org.jetbrains.kotlin.psi.KtVisitorVoid.visitUserType(Lorg/jetbrains/kotlin/psi/KtUserType;Ljava/lang/Object;)Ljava/lang/Object;+6
j  org.jetbrains.kotlin.psi.KtUserType.accept(Lorg/jetbrains/kotlin/psi/KtVisitor;Ljava/lang/Object;)Ljava/lang/Object;+11
j  org.jetbrains.kotlin.psi.KtElementImplStub.accept(Lorg/jetbrains/kotlin/com/intellij/psi/PsiElementVisitor;)V+21
j  org.jetbrains.kotlin.resolve.TypeResolver.resolveTypeElement(Lorg/jetbrains/kotlin/resolve/TypeResolutionContext;Lorg/jetbrains/kotlin/descriptors/annotations/Annotations;Lorg/jetbrains/kotlin/psi/KtModifierList;Lorg/jetbrains/kotlin/psi/KtTypeElement;)Lorg/jetbrains/kotlin/resolve/PossiblyBareType;+154
j  org.jetbrains.kotlin.resolve.TypeResolver.resolvePossiblyBareType(Lorg/jetbrains/kotlin/resolve/TypeResolutionContext;Lorg/jetbrains/kotlin/psi/KtTypeReference;)Lorg/jetbrains/kotlin/resolve/PossiblyBareType;+117
j  org.jetbrains.kotlin.resolve.TypeResolver.resolveType(Lorg/jetbrains/kotlin/resolve/TypeResolutionContext;Lorg/jetbrains/kotlin/psi/KtTypeReference;)Lorg/jetbrains/kotlin/types/KotlinType;+44
j  org.jetbrains.kotlin.resolve.TypeResolver.resolveType(Lorg/jetbrains/kotlin/resolve/scopes/LexicalScope;Lorg/jetbrains/kotlin/psi/KtTypeReference;Lorg/jetbrains/kotlin/resolve/BindingTrace;Z)Lorg/jetbrains/kotlin/types/KotlinType;+43
j  org.jetbrains.kotlin.resolve.FunctionDescriptorResolver.resolveValueParameters(Lorg/jetbrains/kotlin/descriptors/FunctionDescriptor;Lorg/jetbrains/kotlin/resolve/scopes/LexicalWritableScope;Ljava/util/List;Lorg/jetbrains/kotlin/resolve/BindingTrace;Ljava/util/List;Lorg/jetbrains/kotlin/resolve/calls/components/InferenceSession;)Ljava/util/List;+142
j  org.jetbrains.kotlin.resolve.FunctionDescriptorResolver.createValueParameterDescriptors(Lorg/jetbrains/kotlin/psi/KtFunction;Lorg/jetbrains/kotlin/descriptors/impl/SimpleFunctionDescriptorImpl;Lorg/jetbrains/kotlin/resolve/scopes/LexicalWritableScope;Lorg/jetbrains/kotlin/resolve/BindingTrace;Lorg/jetbrains/kotlin/types/KotlinType;Lorg/jetbrains/kotlin/resolve/calls/components/InferenceSession;)Ljava/util/List;+371
j  org.jetbrains.kotlin.resolve.FunctionDescriptorResolver.initializeFunctionDescriptorAndExplicitReturnType(Lorg/jetbrains/kotlin/descriptors/DeclarationDescriptor;Lorg/jetbrains/kotlin/resolve/scopes/LexicalScope;Lorg/jetbrains/kotlin/psi/KtFunction;Lorg/jetbrains/kotlin/descriptors/impl/SimpleFunctionDescriptorImpl;Lorg/jetbrains/kotlin/resolve/BindingTrace;Lorg/jetbrains/kotlin/types/KotlinType;Lorg/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowInfo;Lorg/jetbrains/kotlin/resolve/calls/components/InferenceSession;)V+395
j  org.jetbrains.kotlin.resolve.FunctionDescriptorResolver.resolveFunctionDescriptor(Lkotlin/jvm/functions/Function5;Lorg/jetbrains/kotlin/descriptors/DeclarationDescriptor;Lorg/jetbrains/kotlin/resolve/scopes/LexicalScope;Lorg/jetbrains/kotlin/psi/KtNamedFunction;Lorg/jetbrains/kotlin/resolve/BindingTrace;Lorg/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowInfo;Lorg/jetbrains/kotlin/types/KotlinType;Lorg/jetbrains/kotlin/resolve/calls/components/InferenceSession;)Lorg/jetbrains/kotlin/descriptors/SimpleFunctionDescriptor;+68
j  org.jetbrains.kotlin.resolve.FunctionDescriptorResolver.resolveFunctionDescriptor(Lorg/jetbrains/kotlin/descriptors/DeclarationDescriptor;Lorg/jetbrains/kotlin/resolve/scopes/LexicalScope;Lorg/jetbrains/kotlin/psi/KtNamedFunction;Lorg/jetbrains/kotlin/resolve/BindingTrace;Lorg/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowInfo;Lorg/jetbrains/kotlin/resolve/calls/components/InferenceSession;)Lorg/jetbrains/kotlin/descriptors/SimpleFunctionDescriptor;+87
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope.getDeclaredFunctions(Lorg/jetbrains/kotlin/name/Name;)Ljava/util/Collection;+251
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope.access$getDeclaredFunctions(Lorg/jetbrains/kotlin/resolve/lazy/descriptors/AbstractLazyMemberScope;Lorg/jetbrains/kotlin/name/Name;)Ljava/util/Collection;+2
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope$declaredFunctionDescriptors$1.invoke(Lorg/jetbrains/kotlin/name/Name;)Ljava/util/Collection;+11
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope$declaredFunctionDescriptors$1.invoke(Ljava/lang/Object;)Ljava/lang/Object;+5
J 2791 c1 org.jetbrains.kotlin.storage.LockBasedStorageManager$MapBasedMemoizedFunction.invoke(Ljava/lang/Object;)Ljava/lang/Object; (410 bytes) @ 0x000001bf3691a364 [0x000001bf36919a20+0x0000000000000944]
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$MapBasedMemoizedFunctionToNotNull.invoke(Ljava/lang/Object;)Ljava/lang/Object;+2
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope.doGetFunctions(Lorg/jetbrains/kotlin/name/Name;)Ljava/util/Collection;+9
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope.access$doGetFunctions(Lorg/jetbrains/kotlin/resolve/lazy/descriptors/AbstractLazyMemberScope;Lorg/jetbrains/kotlin/name/Name;)Ljava/util/Collection;+2
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope$functionDescriptors$1.invoke(Lorg/jetbrains/kotlin/name/Name;)Ljava/util/Collection;+11
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope$functionDescriptors$1.invoke(Ljava/lang/Object;)Ljava/lang/Object;+5
J 2791 c1 org.jetbrains.kotlin.storage.LockBasedStorageManager$MapBasedMemoizedFunction.invoke(Ljava/lang/Object;)Ljava/lang/Object; (410 bytes) @ 0x000001bf3691a364 [0x000001bf36919a20+0x0000000000000944]
j  org.jetbrains.kotlin.storage.LockBasedStorageManager$MapBasedMemoizedFunctionToNotNull.invoke(Ljava/lang/Object;)Ljava/lang/Object;+2
j  org.jetbrains.kotlin.resolve.lazy.descriptors.AbstractLazyMemberScope.getContributedFunctions(Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Ljava/util/Collection;+25
j  org.jetbrains.kotlin.resolve.lazy.descriptors.LazyClassMemberScope.getContributedFunctions(Lorg/jetbrains/kotlin/name/Name;Lorg/jetbrains/kotlin/incremental/components/LookupLocation;)Ljava/util/Collection;+17
j  org.jetbrains.kotlin.resolve.lazy.LazyDeclarationResolver$resolveToDescriptor$1.visitNamedFunction(Lorg/jetbrains/kotlin/psi/KtNamedFunction;Ljava/lang/Void;)Lorg/jetbrains/kotlin/descriptors/DeclarationDescriptor;+47
j  org.jetbrains.kotlin.resolve.lazy.LazyDeclarationResolver$resolveToDescriptor$1.visitNamedFunction(Lorg/jetbrains/kotlin/psi/KtNamedFunction;Ljava/lang/Object;)Ljava/lang/Object;+6
j  org.jetbrains.kotlin.psi.KtNamedFunction.accept(Lorg/jetbrains/kotlin/psi/KtVisitor;Ljava/lang/Object;)Ljava/lang/Object;+11
j  org.jetbrains.kotlin.resolve.lazy.LazyDeclarationResolver.resolveToDescriptor(Lorg/jetbrains/kotlin/psi/KtDeclaration;Z)Lorg/jetbrains/kotlin/descriptors/DeclarationDescriptor;+14
j  org.jetbrains.kotlin.resolve.lazy.LazyDeclarationResolver.resolveToDescriptor(Lorg/jetbrains/kotlin/psi/KtDeclaration;)Lorg/jetbrains/kotlin/descriptors/DeclarationDescriptor;+9
j  org.jetbrains.kotlin.resolve.LazyTopDownAnalyzer.createFunctionDescriptors(Lorg/jetbrains/kotlin/resolve/TopDownAnalysisContext;Ljava/util/List;)V+36
j  org.jetbrains.kotlin.resolve.LazyTopDownAnalyzer.analyzeDeclarations(Lorg/jetbrains/kotlin/resolve/TopDownAnalysisMode;Ljava/util/Collection;Lorg/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowInfo;Lorg/jetbrains/kotlin/types/expressions/ExpressionTypingContext;)Lorg/jetbrains/kotlin/resolve/TopDownAnalysisContext;+178
j  org.jetbrains.kotlin.resolve.LazyTopDownAnalyzer.analyzeDeclarations$default(Lorg/jetbrains/kotlin/resolve/LazyTopDownAnalyzer;Lorg/jetbrains/kotlin/resolve/TopDownAnalysisMode;Ljava/util/Collection;Lorg/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowInfo;Lorg/jetbrains/kotlin/types/expressions/ExpressionTypingContext;ILjava/lang/Object;)Lorg/jetbrains/kotlin/resolve/TopDownAnalysisContext;+31
j  org.jetbrains.kotlin.cli.jvm.compiler.TopDownAnalyzerFacadeForJVM.analyzeFilesWithJavaIntegration(Lorg/jetbrains/kotlin/com/intellij/openapi/project/Project;Ljava/util/Collection;Lorg/jetbrains/kotlin/resolve/BindingTrace;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;Lorg/jetbrains/kotlin/com/intellij/psi/search/GlobalSearchScope;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lorg/jetbrains/kotlin/resolve/TargetEnvironment;)Lorg/jetbrains/kotlin/analyzer/AnalysisResult;+267
j  org.jetbrains.kotlin.cli.jvm.compiler.TopDownAnalyzerFacadeForJVM.analyzeFilesWithJavaIntegration$default(Lorg/jetbrains/kotlin/com/intellij/openapi/project/Project;Ljava/util/Collection;Lorg/jetbrains/kotlin/resolve/BindingTrace;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;Lorg/jetbrains/kotlin/com/intellij/psi/search/GlobalSearchScope;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lorg/jetbrains/kotlin/resolve/TargetEnvironment;ILjava/lang/Object;)Lorg/jetbrains/kotlin/analyzer/AnalysisResult;+111
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler$analyze$1.invoke()Lorg/jetbrains/kotlin/analyzer/AnalysisResult;+273
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler$analyze$1.invoke()Ljava/lang/Object;+1
j  org.jetbrains.kotlin.cli.common.messages.AnalyzerWithCompilerReport.analyzeAndReport(Ljava/util/Collection;Lkotlin/jvm/functions/Function0;)V+16
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler.analyze(Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment;)Lorg/jetbrains/kotlin/analyzer/AnalysisResult;+160
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler.compileModules$cli(Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment;Ljava/io/File;Ljava/util/List;Z)Z+406
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinToJVMBytecodeCompiler.compileModules$cli$default(Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinToJVMBytecodeCompiler;Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment;Ljava/io/File;Ljava/util/List;ZILjava/lang/Object;)Z+17
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(Lorg/jetbrains/kotlin/cli/common/arguments/K2JVMCompilerArguments;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/utils/KotlinPaths;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+1046
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(Lorg/jetbrains/kotlin/cli/common/arguments/CommonCompilerArguments;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/utils/KotlinPaths;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+9
j  org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonCompilerArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+220
j  org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonToolArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+7
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonToolArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+76
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Ljava/io/PrintStream;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;[Ljava/lang/String;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+239
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Ljava/io/PrintStream;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;[Ljava/lang/String;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+25
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMainNoExit(Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+39
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMainNoExit$default(Lorg/jetbrains/kotlin/cli/common/CLITool$Companion;Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;ILjava/lang/Object;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+16
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMain(Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;)V+54
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler$Companion.main([Ljava/lang/String;)V+20
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.main([Ljava/lang/String;)V+4
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001bfa3acd530, length=22, elements={
0x000001bf2d282840, 0x000001bf5ba5da70, 0x000001bf5ba5e7f0, 0x000001bf5ba730b0,
0x000001bf5ba73a70, 0x000001bf5ba74330, 0x000001bf5ba74bf0, 0x000001bf5ba75890,
0x000001bf5ba78250, 0x000001bf5babdf30, 0x000001bf5c29aea0, 0x000001bf5c2b3c50,
0x000001bf5c2b8570, 0x000001bf5c474e10, 0x000001bf5c6ab790, 0x000001bfa24bb8a0,
0x000001bfa24b9a40, 0x000001bfa24ba460, 0x000001bfa24b8b10, 0x000001bfa24ba970,
0x000001bfa24bae80, 0x000001bfa24b9530
}

Java Threads: ( => current thread )
=>0x000001bf2d282840 JavaThread "main" [_thread_in_vm, id=17744, stack(0x0000009bcfd00000,0x0000009bcfe00000)]
  0x000001bf5ba5da70 JavaThread "Reference Handler" daemon [_thread_blocked, id=9004, stack(0x0000009bd0400000,0x0000009bd0500000)]
  0x000001bf5ba5e7f0 JavaThread "Finalizer" daemon [_thread_blocked, id=24264, stack(0x0000009bd0500000,0x0000009bd0600000)]
  0x000001bf5ba730b0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=18356, stack(0x0000009bd0600000,0x0000009bd0700000)]
  0x000001bf5ba73a70 JavaThread "Attach Listener" daemon [_thread_blocked, id=26864, stack(0x0000009bd0700000,0x0000009bd0800000)]
  0x000001bf5ba74330 JavaThread "Service Thread" daemon [_thread_blocked, id=13704, stack(0x0000009bd0800000,0x0000009bd0900000)]
  0x000001bf5ba74bf0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=22076, stack(0x0000009bd0900000,0x0000009bd0a00000)]
  0x000001bf5ba75890 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=7716, stack(0x0000009bd0a00000,0x0000009bd0b00000)]
  0x000001bf5ba78250 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=15608, stack(0x0000009bd0b00000,0x0000009bd0c00000)]
  0x000001bf5babdf30 JavaThread "Sweeper thread" daemon [_thread_blocked, id=1012, stack(0x0000009bd0c00000,0x0000009bd0d00000)]
  0x000001bf5c29aea0 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=14800, stack(0x0000009bd0d00000,0x0000009bd0e00000)]
  0x000001bf5c2b3c50 JavaThread "Notification Thread" daemon [_thread_blocked, id=18644, stack(0x0000009bd0e00000,0x0000009bd0f00000)]
  0x000001bf5c2b8570 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=28296, stack(0x0000009bd1000000,0x0000009bd1100000)]
  0x000001bf5c474e10 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=25020, stack(0x0000009bd1200000,0x0000009bd1300000)]
  0x000001bf5c6ab790 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=13348, stack(0x0000009bd1300000,0x0000009bd1400000)]
  0x000001bfa24bb8a0 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=9484, stack(0x0000009bd1e00000,0x0000009bd1f00000)]
  0x000001bfa24b9a40 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=23560, stack(0x0000009bd2100000,0x0000009bd2200000)]
  0x000001bfa24ba460 JavaThread "C2 CompilerThread3" daemon [_thread_in_native, id=28400, stack(0x0000009bd2400000,0x0000009bd2500000)]
  0x000001bfa24b8b10 JavaThread "C2 CompilerThread4" daemon [_thread_blocked, id=18792, stack(0x0000009bd2500000,0x0000009bd2600000)]
  0x000001bfa24ba970 JavaThread "C2 CompilerThread5" daemon [_thread_blocked, id=17604, stack(0x0000009bd2600000,0x0000009bd2700000)]
  0x000001bfa24bae80 JavaThread "C2 CompilerThread6" daemon [_thread_blocked, id=21292, stack(0x0000009bd2700000,0x0000009bd2800000)]
  0x000001bfa24b9530 JavaThread "C2 CompilerThread7" daemon [_thread_blocked, id=23976, stack(0x0000009bd2800000,0x0000009bd2900000)]

Other Threads:
  0x000001bf5ba58f10 VMThread "VM Thread" [stack: 0x0000009bd0300000,0x0000009bd0400000] [id=23496]
  0x000001bf5c2b8130 WatcherThread [stack: 0x0000009bd0f00000,0x0000009bd1000000] [id=15992]
  0x000001bf2d331440 GCTaskThread "GC Thread#0" [stack: 0x0000009bcfe00000,0x0000009bcff00000] [id=16044]
  0x000001bf5c8e3030 GCTaskThread "GC Thread#1" [stack: 0x0000009bd1100000,0x0000009bd1200000] [id=21984]
  0x000001bf5c8e32e0 GCTaskThread "GC Thread#2" [stack: 0x0000009bd1400000,0x0000009bd1500000] [id=25244]
  0x000001bf5c8e3590 GCTaskThread "GC Thread#3" [stack: 0x0000009bd1500000,0x0000009bd1600000] [id=7160]
  0x000001bf5c8f79e0 GCTaskThread "GC Thread#4" [stack: 0x0000009bd1600000,0x0000009bd1700000] [id=3368]
  0x000001bf5c8f7c90 GCTaskThread "GC Thread#5" [stack: 0x0000009bd1700000,0x0000009bd1800000] [id=10984]
  0x000001bf5c8f7f40 GCTaskThread "GC Thread#6" [stack: 0x0000009bd1800000,0x0000009bd1900000] [id=8040]
  0x000001bf5c8f81f0 GCTaskThread "GC Thread#7" [stack: 0x0000009bd1900000,0x0000009bd1a00000] [id=26884]
  0x000001bf5c90b490 GCTaskThread "GC Thread#8" [stack: 0x0000009bd1a00000,0x0000009bd1b00000] [id=1608]
  0x000001bf5c90b740 GCTaskThread "GC Thread#9" [stack: 0x0000009bd1b00000,0x0000009bd1c00000] [id=17776]
  0x000001bf5c936610 GCTaskThread "GC Thread#10" [stack: 0x0000009bd1c00000,0x0000009bd1d00000] [id=8832]
  0x000001bf5c9368c0 GCTaskThread "GC Thread#11" [stack: 0x0000009bd1d00000,0x0000009bd1e00000] [id=27684]
  0x000001bfa274ca90 GCTaskThread "GC Thread#12" [stack: 0x0000009bd2200000,0x0000009bd2300000] [id=21852]
  0x000001bf2d343060 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000009bcff00000,0x0000009bd0000000] [id=3460]
  0x000001bf2d343980 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000009bd0000000,0x0000009bd0100000] [id=26308]
  0x000001bf5c9360b0 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000009bd1f00000,0x0000009bd2000000] [id=27440]
  0x000001bf5c935340 ConcurrentGCThread "G1 Conc#2" [stack: 0x0000009bd2000000,0x0000009bd2100000] [id=23604]
  0x000001bf5b98e7a0 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000009bd0100000,0x0000009bd0200000] [id=10232]
  0x000001bfa2979fd0 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000009bd2300000,0x0000009bd2400000] [id=5572]
  0x000001bf5b98f0d0 ConcurrentGCThread "G1 Service" [stack: 0x0000009bd0200000,0x0000009bd0300000] [id=16360]

Threads with active compile tasks:
C2 CompilerThread0     6292 2965       4       org.jetbrains.kotlin.metadata.ProtoBuf$Type::writeTo (354 bytes)
C2 CompilerThread2     6292 2991   !   4       org.jetbrains.kotlin.metadata.ProtoBuf$Type$Argument::<init> (401 bytes)
C2 CompilerThread3     6292 2917   !   4       org.jetbrains.kotlin.metadata.ProtoBuf$Function::<init> (1410 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001bf2d27c2d0] Metaspace_lock - owner thread: 0x000001bf2d282840

Heap address: 0x0000000604800000, size: 8120 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001bf5d000000-0x000001bf5dbd0000-0x000001bf5dbd0000), size 12386304, SharedBaseAddress: 0x000001bf5d000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001bf5e000000-0x000001bf9e000000, reserved size: 1073741824
Narrow klass base: 0x000001bf5d000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32479M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8120M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 69632K, used 24811K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 33783K, committed 33984K, reserved 1114112K
  class space    used 4189K, committed 4288K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%|HS|  |TAMS 0x0000000604c00000, 0x0000000604800000| Complete 
|   1|0x0000000604c00000, 0x0000000604e28400, 0x0000000605000000| 53%| O|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   2|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605400000, 0x0000000605000000| Untracked 
|   3|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%| O|  |TAMS 0x0000000605800000, 0x0000000605400000| Untracked 
|   4|0x0000000605800000, 0x0000000605c00000, 0x0000000605c00000|100%| O|  |TAMS 0x0000000605c00000, 0x0000000605800000| Untracked 
|   5|0x0000000605c00000, 0x0000000606000000, 0x0000000606000000|100%| O|  |TAMS 0x0000000605cb8200, 0x0000000605c00000| Untracked 
|   6|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|   7|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|   8|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|   9|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  10|0x0000000607000000, 0x00000006072129b0, 0x0000000607400000| 51%| S|CS|TAMS 0x0000000607000000, 0x0000000607000000| Complete 
|  11|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  12|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  13|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
| 114|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 115|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000, 0x0000000621400000| Untracked 
| 126|0x0000000624000000, 0x0000000624259fa0, 0x0000000624400000| 58%| E|  |TAMS 0x0000000624000000, 0x0000000624000000| Complete 

Card table byte_map: [0x000001bf472f0000,0x000001bf482d0000] _byte_map_base: 0x000001bf442cc000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001bf2d332970, (CMBitMap*) 0x000001bf2d3329b0
 Prev Bits: [0x000001bf492b0000, 0x000001bf51190000)
 Next Bits: [0x000001bf51190000, 0x000001bf59070000)

Polling page: 0x000001bf2d360000

Metaspace:

Usage:
  Non-class:     28.90 MB used.
      Class:      4.09 MB used.
       Both:     32.99 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      29.00 MB ( 45%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.19 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      33.19 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  1.36 MB
       Class:  11.81 MB
        Both:  13.17 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 54.50 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 142.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 529.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 1268.
num_chunk_merges: 6.
num_chunk_splits: 1058.
num_chunks_enlarged: 971.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=1358Kb max_used=1358Kb free=117809Kb
 bounds [0x000001bf3dee0000, 0x000001bf3e150000, 0x000001bf45340000]
CodeHeap 'profiled nmethods': size=119104Kb used=6552Kb max_used=6552Kb free=112551Kb
 bounds [0x000001bf36340000, 0x000001bf369b0000, 0x000001bf3d790000]
CodeHeap 'non-nmethods': size=7488Kb used=2941Kb max_used=2997Kb free=4546Kb
 bounds [0x000001bf3d790000, 0x000001bf3da90000, 0x000001bf3dee0000]
 total_blobs=3562 nmethods=2996 adapters=475
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 5.604 Thread 0x000001bf5c6ab790 2996       3       org.jetbrains.kotlin.load.kotlin.header.ReadKotlinClassHeaderAnnotationVisitor$KotlinMetadataArgumentVisitor::visit (195 bytes)
Event: 5.605 Thread 0x000001bfa24bae80 nmethod 2964 0x000001bf3e02d790 code [0x000001bf3e02d920, 0x000001bf3e02db88]
Event: 5.605 Thread 0x000001bf5ba78250 nmethod 2995 0x000001bf369a1f90 code [0x000001bf369a21e0, 0x000001bf369a2b78]
Event: 5.605 Thread 0x000001bfa24bae80 2990       4       org.jetbrains.kotlin.protobuf.SmallSortedMap::binarySearchInArray (133 bytes)
Event: 5.606 Thread 0x000001bf5c6ab790 nmethod 2996 0x000001bf369a2e90 code [0x000001bf369a3200, 0x000001bf369a4db8]
Event: 5.607 Thread 0x000001bfa24bb8a0 nmethod 2916 0x000001bf3e02de10 code [0x000001bf3e02dfa0, 0x000001bf3e02e3f8]
Event: 5.607 Thread 0x000001bfa24bb8a0 2992       4       org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$JvmMethodSignature::getDefaultInstance (4 bytes)
Event: 5.607 Thread 0x000001bfa24bb8a0 nmethod 2992 0x000001bf3e02e490 code [0x000001bf3e02e600, 0x000001bf3e02e658]
Event: 5.607 Thread 0x000001bfa24bb8a0 2989       4       org.jetbrains.kotlin.metadata.ProtoBuf$Type$Argument$Projection::valueOf (50 bytes)
Event: 5.608 Thread 0x000001bfa24bb8a0 nmethod 2989 0x000001bf3e02e790 code [0x000001bf3e02e900, 0x000001bf3e02e998]
Event: 5.610 Thread 0x000001bfa24b9530 nmethod 2994 0x000001bf3e02ea90 code [0x000001bf3e02ec60, 0x000001bf3e02f0c8]
Event: 5.610 Thread 0x000001bfa24ba970 nmethod 2954 0x000001bf3e02f490 code [0x000001bf3e02f680, 0x000001bf3e02fff8]
Event: 5.611 Thread 0x000001bf5c6ab790 2997       3       org.jetbrains.kotlin.serialization.deserialization.NameResolverUtilKt::getClassId (30 bytes)
Event: 5.611 Thread 0x000001bfa24bb8a0 2998       4       java.lang.AbstractStringBuilder::append (54 bytes)
Event: 5.611 Thread 0x000001bfa24bae80 nmethod 2990 0x000001bf3e030690 code [0x000001bf3e030840, 0x000001bf3e030af8]
Event: 5.611 Thread 0x000001bfa24b8b10 nmethod 2953 0x000001bf3e030e10 code [0x000001bf3e031000, 0x000001bf3e031a38]
Event: 5.611 Thread 0x000001bfa24bae80 2999       4       java.util.concurrent.locks.AbstractQueuedSynchronizer::compareAndSetState (13 bytes)
Event: 5.611 Thread 0x000001bf5c6ab790 nmethod 2997 0x000001bf369a5390 code [0x000001bf369a55e0, 0x000001bf369a5d98]
Event: 5.611 Thread 0x000001bfa24ba970 3000       4       java.lang.String::startsWith (138 bytes)
Event: 5.612 Thread 0x000001bfa24bae80 nmethod 2999 0x000001bf3e032010 code [0x000001bf3e032180, 0x000001bf3e032218]

GC Heap History (14 events):
Event: 0.678 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 520192K, used 24576K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 6775K, committed 6912K, reserved 1114112K
  class space    used 701K, committed 768K, reserved 1048576K
}
Event: 0.681 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 7052K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 6775K, committed 6912K, reserved 1114112K
  class space    used 701K, committed 768K, reserved 1048576K
}
Event: 1.390 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 35724K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 15281K, committed 15488K, reserved 1114112K
  class space    used 2018K, committed 2112K, reserved 1048576K
}
Event: 1.392 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 8750K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 15281K, committed 15488K, reserved 1114112K
  class space    used 2018K, committed 2112K, reserved 1048576K
}
Event: 2.436 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 45614K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 21346K, committed 21504K, reserved 1114112K
  class space    used 2889K, committed 2944K, reserved 1048576K
}
Event: 2.439 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 520192K, used 14079K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 21346K, committed 21504K, reserved 1114112K
  class space    used 2889K, committed 2944K, reserved 1048576K
}
Event: 3.582 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 69632K, used 46847K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 10 young (40960K), 2 survivors (8192K)
 Metaspace       used 27314K, committed 27520K, reserved 1114112K
  class space    used 3458K, committed 3520K, reserved 1048576K
}
Event: 3.587 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 69632K, used 17446K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 27314K, committed 27520K, reserved 1114112K
  class space    used 3458K, committed 3520K, reserved 1048576K
}
Event: 3.762 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 69632K, used 21542K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 28645K, committed 28864K, reserved 1114112K
  class space    used 3618K, committed 3712K, reserved 1048576K
}
Event: 3.768 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 69632K, used 17809K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 28645K, committed 28864K, reserved 1114112K
  class space    used 3618K, committed 3712K, reserved 1048576K
}
Event: 4.860 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 69632K, used 38289K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 33123K, committed 33344K, reserved 1114112K
  class space    used 4108K, committed 4224K, reserved 1048576K
}
Event: 4.866 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 69632K, used 23701K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 33123K, committed 33344K, reserved 1114112K
  class space    used 4108K, committed 4224K, reserved 1048576K
}
Event: 5.581 GC heap before
{Heap before GC invocations=8 (full 0):
 garbage-first heap   total 69632K, used 44181K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 33682K, committed 33920K, reserved 1114112K
  class space    used 4175K, committed 4288K, reserved 1048576K
}
Event: 5.589 GC heap after
{Heap after GC invocations=9 (full 0):
 garbage-first heap   total 69632K, used 24811K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 33682K, committed 33920K, reserved 1114112K
  class space    used 4175K, committed 4288K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 4.930 Thread 0x000001bf2d282840 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001bf3dfcf480 relative=0x00000000000001c0
Event: 4.930 Thread 0x000001bf2d282840 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001bf3dfcf480 method=java.lang.Character.codePointAt(Ljava/lang/CharSequence;I)I @ 2 c2
Event: 4.930 Thread 0x000001bf2d282840 DEOPT PACKING pc=0x000001bf3dfcf480 sp=0x0000009bcfdfcbb0
Event: 4.930 Thread 0x000001bf2d282840 DEOPT UNPACKING pc=0x000001bf3d7e23a3 sp=0x0000009bcfdfcb58 mode 2
Event: 4.930 Thread 0x000001bf2d282840 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001bf3dfcf480 relative=0x00000000000001c0
Event: 4.930 Thread 0x000001bf2d282840 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001bf3dfcf480 method=java.lang.Character.codePointAt(Ljava/lang/CharSequence;I)I @ 2 c2
Event: 4.930 Thread 0x000001bf2d282840 DEOPT PACKING pc=0x000001bf3dfcf480 sp=0x0000009bcfdfcbb0
Event: 4.930 Thread 0x000001bf2d282840 DEOPT UNPACKING pc=0x000001bf3d7e23a3 sp=0x0000009bcfdfcb58 mode 2
Event: 4.930 Thread 0x000001bf2d282840 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001bf3dfcf480 relative=0x00000000000001c0
Event: 4.930 Thread 0x000001bf2d282840 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001bf3dfcf480 method=java.lang.Character.codePointAt(Ljava/lang/CharSequence;I)I @ 2 c2
Event: 4.930 Thread 0x000001bf2d282840 DEOPT PACKING pc=0x000001bf3dfcf480 sp=0x0000009bcfdfcbb0
Event: 4.930 Thread 0x000001bf2d282840 DEOPT UNPACKING pc=0x000001bf3d7e23a3 sp=0x0000009bcfdfcb58 mode 2
Event: 4.930 Thread 0x000001bf2d282840 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001bf3dfcf480 relative=0x00000000000001c0
Event: 4.930 Thread 0x000001bf2d282840 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001bf3dfcf480 method=java.lang.Character.codePointAt(Ljava/lang/CharSequence;I)I @ 2 c2
Event: 4.930 Thread 0x000001bf2d282840 DEOPT PACKING pc=0x000001bf3dfcf480 sp=0x0000009bcfdfcbb0
Event: 4.930 Thread 0x000001bf2d282840 DEOPT UNPACKING pc=0x000001bf3d7e23a3 sp=0x0000009bcfdfcb58 mode 2
Event: 5.359 Thread 0x000001bf2d282840 DEOPT PACKING pc=0x000001bf3695e663 sp=0x0000009bcfdfc110
Event: 5.359 Thread 0x000001bf2d282840 DEOPT UNPACKING pc=0x000001bf3d7e2b43 sp=0x0000009bcfdfb618 mode 0
Event: 5.360 Thread 0x000001bf2d282840 DEOPT PACKING pc=0x000001bf3695e663 sp=0x0000009bcfdfc110
Event: 5.360 Thread 0x000001bf2d282840 DEOPT UNPACKING pc=0x000001bf3d7e2b43 sp=0x0000009bcfdfb618 mode 0

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.762 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d36938}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x0000000623d36938) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.764 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d45cf8}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object, java.lang.Object)'> (0x0000000623d45cf8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.766 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d54888}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int, int)'> (0x0000000623d54888) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.767 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d632e8}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, long, long)'> (0x0000000623d632e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.770 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d71900}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int)'> (0x0000000623d71900) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.783 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623e60da0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623e60da0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.787 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623e89ea0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623e89ea0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.814 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623f69630}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623f69630) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.901 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623a8d818}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623a8d818) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.992 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623699710}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623699710) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.119 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233558d0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006233558d0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.127 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233cfed8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006233cfed8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.146 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c8b158}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c8b158) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.960 Thread 0x000001bf2d282840 Implicit null exception at 0x000001bf3df06072 to 0x000001bf3df066cc
Event: 2.175 Thread 0x000001bf2d282840 Implicit null exception at 0x000001bf3df087f2 to 0x000001bf3df08e6c
Event: 2.263 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x00000006226170e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006226170e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.821 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x0000000624153478}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x0000000624153478) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.951 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x000000062434a440}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000062434a440) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.951 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x000000062434eac8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000062434eac8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.952 Thread 0x000001bf2d282840 Exception <a 'java/lang/NoSuchMethodError'{0x0000000624352938}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000624352938) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 3.947 Executing VM operation: HandshakeAllThreads
Event: 3.947 Executing VM operation: HandshakeAllThreads done
Event: 4.092 Executing VM operation: HandshakeAllThreads
Event: 4.092 Executing VM operation: HandshakeAllThreads done
Event: 4.390 Executing VM operation: HandshakeAllThreads
Event: 4.390 Executing VM operation: HandshakeAllThreads done
Event: 4.466 Executing VM operation: HandshakeAllThreads
Event: 4.466 Executing VM operation: HandshakeAllThreads done
Event: 4.583 Executing VM operation: HandshakeAllThreads
Event: 4.583 Executing VM operation: HandshakeAllThreads done
Event: 4.665 Executing VM operation: ICBufferFull
Event: 4.665 Executing VM operation: ICBufferFull done
Event: 4.860 Executing VM operation: G1CollectForAllocation
Event: 4.868 Executing VM operation: G1CollectForAllocation done
Event: 4.880 Executing VM operation: G1Concurrent
Event: 4.883 Executing VM operation: G1Concurrent done
Event: 4.887 Executing VM operation: G1Concurrent
Event: 4.888 Executing VM operation: G1Concurrent done
Event: 5.363 Executing VM operation: G1CollectForAllocation
Event: 5.590 Executing VM operation: G1CollectForAllocation done

Events (20 events):
Event: 4.217 loading class java/text/CharacterIterator done
Event: 4.217 loading class java/text/StringCharacterIterator done
Event: 4.328 loading class java/util/ArrayList$SubList$1
Event: 4.328 loading class java/util/ArrayList$SubList$1 done
Event: 4.352 loading class java/lang/annotation/ElementType
Event: 4.352 loading class java/lang/annotation/ElementType done
Event: 4.352 loading class java/lang/Override
Event: 4.352 loading class java/lang/Override done
Event: 4.632 Thread 0x000001bfa24ba970 Thread exited: 0x000001bfa24ba970
Event: 4.894 loading class java/util/stream/IntPipeline$1
Event: 4.894 loading class java/util/stream/IntPipeline$1 done
Event: 4.897 loading class java/util/stream/IntPipeline$1$1
Event: 4.897 loading class java/util/stream/IntPipeline$1$1 done
Event: 5.015 Thread 0x000001bfa24bb8a0 Thread added: 0x000001bfa24bb8a0
Event: 5.065 Thread 0x000001bfa24b9a40 Thread added: 0x000001bfa24b9a40
Event: 5.068 Thread 0x000001bfa24ba460 Thread added: 0x000001bfa24ba460
Event: 5.069 Thread 0x000001bfa24b8b10 Thread added: 0x000001bfa24b8b10
Event: 5.071 Thread 0x000001bfa24ba970 Thread added: 0x000001bfa24ba970
Event: 5.071 Thread 0x000001bfa24bae80 Thread added: 0x000001bfa24bae80
Event: 5.072 Thread 0x000001bfa24b9530 Thread added: 0x000001bfa24b9530


Dynamic libraries:
0x00007ff708300000 - 0x00007ff708310000 	E:\JAVA\bin\java.exe
0x00007ff827030000 - 0x00007ff827228000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8255d0000 - 0x00007ff825692000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff824d50000 - 0x00007ff825046000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8246c0000 - 0x00007ff8247c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff81fde0000 - 0x00007ff81fdf9000 	E:\JAVA\bin\jli.dll
0x00007ff81fdc0000 - 0x00007ff81fddb000 	E:\JAVA\bin\VCRUNTIME140.dll
0x00007ff826e80000 - 0x00007ff826f31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff826340000 - 0x00007ff8263de000 	C:\Windows\System32\msvcrt.dll
0x00007ff825e80000 - 0x00007ff825f1f000 	C:\Windows\System32\sechost.dll
0x00007ff825b10000 - 0x00007ff825c33000 	C:\Windows\System32\RPCRT4.dll
0x00007ff824a20000 - 0x00007ff824a47000 	C:\Windows\System32\bcrypt.dll
0x00007ff825050000 - 0x00007ff8251ed000 	C:\Windows\System32\USER32.dll
0x00007ff8248a0000 - 0x00007ff8248c2000 	C:\Windows\System32\win32u.dll
0x00007ff81dfe0000 - 0x00007ff81e27a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff825da0000 - 0x00007ff825dcb000 	C:\Windows\System32\GDI32.dll
0x00007ff824a50000 - 0x00007ff824b69000 	C:\Windows\System32\gdi32full.dll
0x00007ff824980000 - 0x00007ff824a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff823400000 - 0x00007ff82340a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8253f0000 - 0x00007ff82541f000 	C:\Windows\System32\IMM32.DLL
0x00007ff81fd50000 - 0x00007ff81fd5c000 	E:\JAVA\bin\vcruntime140_1.dll
0x00007ffffb0c0000 - 0x00007ffffb14e000 	E:\JAVA\bin\msvcp140.dll
0x00007fffe0350000 - 0x00007fffe0f2e000 	E:\JAVA\bin\server\jvm.dll
0x00007ff826f40000 - 0x00007ff826f48000 	C:\Windows\System32\PSAPI.DLL
0x00007fffeb340000 - 0x00007fffeb349000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff826e10000 - 0x00007ff826e7b000 	C:\Windows\System32\WS2_32.dll
0x00007ff81e3c0000 - 0x00007ff81e3e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff823360000 - 0x00007ff823372000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff81c4f0000 - 0x00007ff81c4fa000 	E:\JAVA\bin\jimage.dll
0x00007ff821e10000 - 0x00007ff822011000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff80fef0000 - 0x00007ff80ff24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff824810000 - 0x00007ff824892000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff818d20000 - 0x00007ff818d45000 	E:\JAVA\bin\java.dll
0x00007fffef390000 - 0x00007fffef467000 	E:\JAVA\bin\jsvml.dll
0x00007ff826570000 - 0x00007ff826cde000 	C:\Windows\System32\SHELL32.dll
0x00007ff8222d0000 - 0x00007ff822a74000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff825fd0000 - 0x00007ff826323000 	C:\Windows\System32\combase.dll
0x00007ff8240f0000 - 0x00007ff82411b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff826d40000 - 0x00007ff826e0d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff825f20000 - 0x00007ff825fcd000 	C:\Windows\System32\SHCORE.dll
0x00007ff825d40000 - 0x00007ff825d9b000 	C:\Windows\System32\shlwapi.dll
0x00007ff8245f0000 - 0x00007ff824614000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff811d70000 - 0x00007ff811d89000 	E:\JAVA\bin\net.dll
0x00007ff81ede0000 - 0x00007ff81eeea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff823e50000 - 0x00007ff823eba000 	C:\Windows\system32\mswsock.dll
0x00007ff810fb0000 - 0x00007ff810fc6000 	E:\JAVA\bin\nio.dll
0x00007ff81bc20000 - 0x00007ff81bc38000 	E:\JAVA\bin\zip.dll
0x00007ff81c4d0000 - 0x00007ff81c4e0000 	E:\JAVA\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\JAVA\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;E:\JAVA\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\Ad\AdNull\build\20250901_3004967216914650746.compiler.options
java_class_path (initial): E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.22\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\kotlin-compiler-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.22\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\kotlin-script-runtime-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.22\20e2c5df715f3240c765cfc222530e2796542021\kotlin-daemon-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8514437120                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8514437120                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\JAVA
CLASSPATH=.;E:\JAVA\lib\dt.jar;E:\JAVA\lib\tools.jar;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\dotnet\;E:\git\Git\cmd;D:\Users\W9096060\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\dotnet\;E:\1.8java\lib\dt.jar;E:\1.8java\lib\tools.jar;E:\1.8java\bin;E:\JAVA\bin;E:\JAVA\lib;D:\Users\W9096060\AppData\Local\Microsoft\WindowsApps;
USERNAME=W9096060
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 19:56 hours

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 32479M (2763M free)
TotalPageFile size 47840M (AvailPageFile size 438M)
current process WorkingSet (physical memory assigned to process): 189M, peak: 189M
current process commit charge ("private bytes"): 244M, peak: 678M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211) for windows-amd64 JRE (17.0.8+9-LTS-211), built on Jun 14 2023 10:34:31 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
