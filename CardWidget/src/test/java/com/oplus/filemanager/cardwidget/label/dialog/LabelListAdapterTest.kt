/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LabelListAdapterTest
 ** Description : 标签名称Adapter 的单元测试
 ** Version     : 1.0
 ** Date        : 2022/12/19
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue      2022/12/19      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.dialog

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.RadioButton
import android.widget.TextView
import com.coui.appcompat.cardlist.COUICardListHelper
import com.oplus.filemanager.cardwidget.R
import com.oplus.filemanager.cardwidget.label.data.LabelData
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class LabelListAdapterTest {

    lateinit var adapter: LabelListAdapter
    lateinit var inflater: LayoutInflater

    @Before
    fun setup() {
        val context = mockk<Context>()

        mockkStatic(LayoutInflater::class)
        inflater = mockk<LayoutInflater>()
        every { LayoutInflater.from(any()) }.returns(inflater)
        adapter = spyk(LabelListAdapter(context, 0, emptyList()))
        every { adapter.layoutInflater }.returns(inflater)
        every { adapter.list }.answers { callOriginal() }
        every { adapter.list = any() }.answers { callOriginal() }
        every { adapter.selectLabel }.answers { callOriginal() }
        every { adapter.selectLabel = any() }.answers { callOriginal() }
        every { adapter.selectIndex }.answers { callOriginal() }
        every { adapter.selectIndex = any() }.answers { callOriginal() }

        mockkStatic(COUICardListHelper::class)
    }

    @After
    fun teardown() {
        unmockkStatic(LayoutInflater::class)
        unmockkStatic(COUICardListHelper::class)
    }

    @Test
    fun should_when_setLabelList() {
        every { adapter.setLabelList(any()) }.answers { callOriginal() }
        justRun { adapter.notifyDataSetChanged() }
        justRun { adapter.findSelectIndex() }
        val list = mutableListOf<LabelData>()
        adapter.setLabelList(list)
        Assert.assertTrue(adapter.list.isEmpty())

        list.add(LabelData(0, "测试"))
        adapter.setLabelList(list)
        Assert.assertTrue(adapter.list.isNotEmpty())
    }

    @Test
    fun should_notNull_when_onCreateViewHolder() {
        every { adapter.onCreateViewHolder(any(), any()) }.answers { callOriginal() }
        val itemView = mockk<View>()
        every { itemView.findViewById<TextView>(R.id.label_name) }.returns(mockk())
        every { itemView.findViewById<RadioButton>(R.id.label_radio) }.returns(mockk())
        every { itemView.findViewById<RadioButton>(R.id.divider_line) }.returns(mockk())
        every { inflater.inflate(R.layout.card_item_select_label, any(), false) }.returns(itemView)
        var holder = adapter.onCreateViewHolder(mockk(), 0)
        Assert.assertNotNull(holder)
    }

    @Test
    fun should_call_bindData_when_onBindViewHolder() {
        every { adapter.onBindViewHolder(any(), any()) }.answers { callOriginal() }
        every { COUICardListHelper.getPositionInGroup(any(), any()) }.returns(0)
        justRun { COUICardListHelper.setItemCardBackground(any(), any()) }
        val holder = mockk<LabelListAdapter.LabelListVH>()
        justRun { holder.bindData(any()) }
        justRun { holder.setClickEvent(any(), any()) }
        justRun { holder.updateDividerVisible(any(), any()) }
        adapter.list = mutableListOf<LabelData>(LabelData(0, "测试"))
        adapter.onBindViewHolder(holder, 0)

        verify { holder.bindData(any()) }
    }

    @Test
    fun should_return_int_when_getItemCount() {
        val list = mutableListOf<LabelData>()
        every { adapter.itemCount }.answers { callOriginal() }
        adapter.list = list
        Assert.assertEquals(0, adapter.itemCount)

        list.add(mockk())
        Assert.assertEquals(1, adapter.itemCount)
    }


    @Test
    fun should_when_findSelectIndex() {
        every { adapter.findSelectIndex() }.answers { callOriginal() }
        adapter.selectLabel = 1
        val list = mutableListOf<LabelData>()
        list.add(LabelData(0, "标签"))
        list.add(LabelData(1, "学习"))
        adapter.list = list
        adapter.findSelectIndex()
        Assert.assertEquals(1, adapter.selectIndex)
    }

    @Test
    fun should_when_selectLabel() {
        every { adapter.selectLabel(any(), any()) }.answers { callOriginal() }
        justRun { adapter.notifyItemChanged(any()) }

        adapter.selectIndex = 0
        adapter.selectLabel(0, LabelData(0, "标签"))
        verify(inverse = true) { adapter.notifyItemChanged(any()) }

        adapter.selectLabel(1, LabelData(0, "标签"))
        verify { adapter.notifyItemChanged(0) }
        verify { adapter.notifyItemChanged(1) }
        Assert.assertEquals(1, adapter.selectIndex)
    }

    @Test
    fun should_when_bindData() {
        val holder = mockk<LabelListAdapter.LabelListVH>()
        every { holder.bindData(any()) }.answers { callOriginal() }
        every { holder.bindingAdapter }.returns(adapter)
        val textView = mockk<TextView>()
        justRun { textView.text = any() }
        every { holder.nameTv }.returns(textView)
        val radio = mockk<RadioButton>()
        justRun { radio.isChecked = any() }
        every { holder.radio }.returns(radio)
        val dividerLine = mockk<View>()
        justRun { dividerLine.visibility = any() }
        every { holder.dividerLine }.returns(dividerLine)

        holder.bindData(LabelData(1, "标签"))

        verify { textView.text = any() }
        verify { radio.isChecked = any() }
    }

    @Test
    fun should_when_setClickEvent() {
        val itemView = mockk<View>()
        val radio = mockk<RadioButton>()
        justRun { radio.setOnClickListener(any()) }
        every { itemView.findViewById<TextView>(R.id.label_name) }.returns(mockk())
        every { itemView.findViewById<RadioButton>(R.id.label_radio) }.returns(radio)
        every { itemView.findViewById<RadioButton>(R.id.divider_line) }.returns(mockk())
        justRun { itemView.setOnClickListener(any()) }
        val holder = spyk(LabelListAdapter.LabelListVH(itemView))
        every { holder.setClickEvent(any(), any()) }.answers { callOriginal() }
        every { holder.bindingAdapter }.returns(adapter)

        holder.setClickEvent(0, LabelData(1, "标签"))

        verify { itemView.setOnClickListener(any()) }
        verify { radio.setOnClickListener(any()) }
    }
}