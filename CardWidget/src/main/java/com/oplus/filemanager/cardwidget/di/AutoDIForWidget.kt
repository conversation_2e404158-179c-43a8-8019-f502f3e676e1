/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: AutoDIForCategoryAlbum.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2024/6/6
 ** Author: yangqichang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.cardwidget.di

import com.oplus.filemanager.cardwidget.api.CardWidgetApi
import com.oplus.filemanager.interfaze.cardwidget.ICardWidgetApi
import org.koin.dsl.module

object AutoDIForWidget {

    val widgetAlbumApi = module {
        single<ICardWidgetApi>(createdAtStart = true) {
            CardWidgetApi
        }
    }
}