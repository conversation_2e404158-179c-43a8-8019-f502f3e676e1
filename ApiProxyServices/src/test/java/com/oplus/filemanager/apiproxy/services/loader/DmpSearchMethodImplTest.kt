package com.oplus.filemanager.apiproxy.services.loader

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.net.Uri
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.SearchDMPFileWrapper
import com.filemanager.common.utils.DmpUtils
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.apiproxy.services.data.SearchResultData
import com.oplus.filemanager.apiproxy.services.utils.SearchChecker
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import io.mockk.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * DmpSearchMethodImpl的单元测试类
 * 用于测试DmpSearchMethodImpl类的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class DmpSearchMethodImplTest {

    // 测试所需的mock对象
    private lateinit var context: Context
    private lateinit var contentResolver: ContentResolver
    private lateinit var dmpSearchApi: IDmpSearchApi
    private lateinit var dmpSearchMethodImpl: DmpSearchMethodImpl
    private lateinit var searchResultData: SearchResultData

    /**
     * 测试前的初始化方法
     * 创建所有需要的mock对象并设置初始状态
     */
    @Before
    fun setUp() {
        // 初始化MockK注解
        MockKAnnotations.init(this)
        // 创建mock对象
        context = mockk(relaxed = true)
        contentResolver = mockk(relaxed = true)
        dmpSearchApi = mockk(relaxed = true)
        searchResultData = mockk(relaxed = true)
        
        // 模拟静态对象
        mockkObject(Injector)
        mockkObject(DmpUtils)
        mockkObject(SearchChecker)
        
        // 设置Injector返回模拟的dmpSearchApi
        every { Injector.injectFactory<IDmpSearchApi>() } returns dmpSearchApi
        
        // 创建被测对象的部分mock
        dmpSearchMethodImpl = spyk(DmpSearchMethodImpl(context))
    }

    /**
     * 测试后的清理方法
     * 释放所有mock资源
     */
    @After
    fun tearDown() {
        unmockkAll()
    }
    
    /**
     * 测试handleSearch方法在dmpSearchApi为null时的行为
     * 预期返回空列表且状态码为OK
     */
    @Test
    fun `handleSearch should return empty list when dmpSearchApi is null`() {
        // 设置Injector返回null
        every { Injector.injectFactory<IDmpSearchApi>() } returns null
        
        // 调用被测方法
        val result = dmpSearchMethodImpl.handleSearch(contentResolver, "keyword", 1)
        
        // 验证结果为空列表
        assertTrue(result.isEmpty())
        // 通过反射获取code字段的值，因为它是protected
        val codeField = SearchMethod::class.java.getDeclaredField("code")
        codeField.isAccessible = true
        // 验证状态码为OK
        assertEquals(SearchChecker.CODE_OK, codeField.get(dmpSearchMethodImpl))
    }
    
    /**
     * 测试handleSearch方法在cursor为null时的行为
     * 预期返回空列表且状态码为OK
     */
    @Test
    fun `handleSearch should return empty list when cursor is null`() {
        // 设置getCursor返回null
        every { dmpSearchApi.getCursor(any()) } returns null
        
        // 调用被测方法
        val result = dmpSearchMethodImpl.handleSearch(contentResolver, "keyword", 1)
        
        // 验证结果为空列表
        assertTrue(result.isEmpty())
        // 通过反射获取code字段的值，因为它是protected
        val codeField = SearchMethod::class.java.getDeclaredField("code")
        codeField.isAccessible = true
        // 验证状态码为OK
        assertEquals(SearchChecker.CODE_OK, codeField.get(dmpSearchMethodImpl))
    }
    
    /**
     * 测试handleSearch方法在cursor有数据时的行为
     * 预期返回包含BaseFileBean的列表且状态码为OK
     */
    @Test
    fun `handleSearch should return list of BaseFileBean when cursor has data`() {
        // 创建mock对象
        val cursor = mockk<Cursor>()
        val baseFileBean = mockk<BaseFileBean>()
        
        // 设置mock行为
        every { dmpSearchApi.getCursor(any()) } returns cursor
        every { cursor.count } returns 1
        every { cursor.moveToNext() } returns true andThen false
        every { dmpSearchMethodImpl.createFromCursor(cursor, null) } returns baseFileBean
        
        // 调用被测方法
        val result = dmpSearchMethodImpl.handleSearch(contentResolver, "keyword", 1)
        
        // 验证结果包含一个BaseFileBean
        assertEquals(1, result.size)
        assertEquals(baseFileBean, result[0])
        // 通过反射获取code字段的值，因为它是protected
        val codeField = SearchMethod::class.java.getDeclaredField("code")
        codeField.isAccessible = true
        // 验证状态码为OK
        assertEquals(SearchChecker.CODE_OK, codeField.get(dmpSearchMethodImpl))
        // 验证cursor被正确关闭
        verify(exactly = 1) { cursor.close() }
    }
    
    /**
     * 测试handleSearch方法在发生异常时的行为
     * 预期返回空列表且状态码为QUERY_ERROR
     */
    @Test
    fun `handleSearch should set error code when exception occurs`() {
        // 创建mock对象
        val cursor = mockk<Cursor>()
        
        // 设置mock行为，模拟抛出异常
        every { dmpSearchApi.getCursor(any()) } returns cursor
        every { cursor.count } returns 1
        every { cursor.moveToNext() } throws RuntimeException("Test exception")
        
        // 调用被测方法
        val result = dmpSearchMethodImpl.handleSearch(contentResolver, "keyword", 1)
        
        // 验证结果为空列表
        assertTrue(result.isEmpty())
        // 通过反射获取code字段的值，因为它是protected
        val codeField = SearchMethod::class.java.getDeclaredField("code")
        codeField.isAccessible = true
        // 验证状态码为QUERY_ERROR
        assertEquals(SearchChecker.CODE_QUERY_ERROR, codeField.get(dmpSearchMethodImpl))
        // 验证cursor被正确关闭
        verify(exactly = 1) { cursor.close() }
    }
    
    /**
     * 测试createFromCursor方法在路径被忽略时的行为
     * 预期返回null
     */
    @Test
    fun `createFromCursor should return null when path is ignored`() {
        // 创建mock对象
        val cursor = mockk<Cursor>()
        val uri = mockk<Uri>()
        val ignoredPath = "/ignored/path"
        
        // 设置mock行为
        every { cursor.getColumnIndex(SearchDMPFileWrapper.ABSOLUTE_PATH_INDEX) } returns 0
        every { cursor.getString(0) } returns ignoredPath
        every { dmpSearchMethodImpl.isIgnoredPath(ignoredPath) } returns true
        
        // 调用被测方法
        val result = dmpSearchMethodImpl.createFromCursor(cursor, uri)
        
        // 验证结果为null
        assertNull(result)
    }
    
    /**
     * 测试classifyItem方法的行为
     * 预期对每个item调用classifyDMPSearchItem方法
     */
    @Test
    fun `classifyItem should call classifyDMPSearchItem for each item`() {
        // 创建测试数据
        val items = listOf(mockk<BaseFileBean>(), mockk<BaseFileBean>())
        val keyword = "test"
        
        // 通过反射设置keyword字段的值
        val keywordField = SearchMethod::class.java.getDeclaredField("keyword")
        keywordField.isAccessible = true
        keywordField.set(dmpSearchMethodImpl, keyword)
        
        // 调用被测方法
        dmpSearchMethodImpl.classifyItem(items, searchResultData)
        
        // 验证对每个item都调用了classifyDMPSearchItem
        verify(exactly = items.size) { searchResultData.classifyDMPSearchItem(any(), keyword) }
        
        // 清理反射修改
        keywordField.set(dmpSearchMethodImpl, null)
    }
}