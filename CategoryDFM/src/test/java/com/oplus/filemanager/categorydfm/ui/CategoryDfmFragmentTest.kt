package com.oplus.filemanager.categorydfm.ui

import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.graphics.Point
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.MutableLiveData
import com.oplus.filemanager.categorydfm.R
import com.oplus.filemanager.categorydfm.view.TypeSortEntryView
import com.filemanager.common.base.DFMMediaFile
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.utils.KtViewUtils
import com.oplus.filemanager.dfm.DFMManager
import com.filemanager.fileoperate.NormalFileOperateController
import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])  // 将SDK版本从28改为29以解决错误
class CategoryDfmFragmentTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    @MockK
    lateinit var mockViewModel: DFMFragmentViewModel

    @RelaxedMockK
    lateinit var mockParentViewModel: DFMParentViewModel

    @RelaxedMockK
    lateinit var mockView: View

    @RelaxedMockK
    lateinit var mockSortEntryView: TypeSortEntryView

    @RelaxedMockK
    lateinit var mockLoadingController: LoadingController

    @RelaxedMockK
    lateinit var mockSortPopupController: SortPopupController

    @RelaxedMockK
    lateinit var mockFileOperateController: NormalFileOperateController

    private lateinit var fragment: CategoryDfmFragment

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkObject(DFMManager)
        mockkObject(KtViewUtils)

        every { DFMManager.getDFSConnectState() } returns true
        every { KtViewUtils.getWindowSize(any()) } returns Point(1080, 1920)

        fragment = spyk(CategoryDfmFragment())
        every { fragment.checkShowPermissionEmpty() } returns false
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun testNewInstance() {
        val fragment = CategoryDfmFragment.newInstance(0, "/path", true)
        assert(fragment.arguments?.getInt(KtConstants.P_TAB_POSITION) == 0)
        assert(fragment.arguments?.getString(KtConstants.CURRENT_PATH) == "/path")
        assert(fragment.arguments?.getBoolean(KtConstants.P_NEED_LOAD_DATA) == true)
    }

    @Test
    fun testOnAttach() {
        val bundle = Bundle().apply {
            putInt(KtConstants.P_TAB_POSITION, 1)
            putString(KtConstants.CURRENT_PATH, "/test")
            putBoolean(KtConstants.P_NEED_LOAD_DATA, true)
        }
        fragment.arguments = bundle

        fragment.onAttach(mockk())

        // 由于tabPosition、rootPath和needLoadData是private的，改为验证arguments
        assert(fragment.arguments?.getInt(KtConstants.P_TAB_POSITION) == 1)
        assert(fragment.arguments?.getString(KtConstants.CURRENT_PATH) == "/test")
        assert(fragment.arguments?.getBoolean(KtConstants.P_NEED_LOAD_DATA) == true)
    }
}