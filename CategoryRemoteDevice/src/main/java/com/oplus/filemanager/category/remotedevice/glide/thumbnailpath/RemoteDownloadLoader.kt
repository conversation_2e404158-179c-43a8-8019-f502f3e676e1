/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemotePathLoader.kt
 ** Description: RemotePathLoader
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.category.remotedevice.glide.thumbnailpath

import android.content.Context
import android.graphics.Bitmap
import com.bumptech.glide.load.Options
import com.bumptech.glide.load.model.ModelLoader
import com.bumptech.glide.signature.ObjectKey
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean

class RemoteDownloadLoader(
    private val context: Context
) : ModelLoader<DownloadRemoteFileBean, Bitmap> {
    override fun buildLoadData(
        model: DownloadRemoteFileBean,
        width: Int,
        height: Int,
        options: Options
    ): ModelLoader.LoadData<Bitmap> = ModelLoader.LoadData(
        ObjectKey(model),
        RemoteDownloadFetcher(context, model, width, height)
    )

    override fun handles(model: DownloadRemoteFileBean): Boolean = true
}
