package com.android.documentsui.base;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import android.content.Context;
import android.os.UserHandle;
import android.os.UserManager;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.net.ProtocolException;

/**
 * UserId类的单元测试类
 * 使用Robolectric测试框架进行Android环境下的单元测试
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 29)
public class UserIdTest {

    @Mock
    private Context mockContext;  // 模拟的Context对象
    @Mock
    private UserManager mockUserManager;  // 模拟的UserManager对象

    /**
     * 测试前的初始化方法
     * 使用Mockito框架初始化模拟对象
     */
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 设置当调用getSystemService(USER_SERVICE)时返回模拟的UserManager
        when(mockContext.getSystemService(Context.USER_SERVICE)).thenReturn(mockUserManager);
    }

    /**
     * 测试UserId.of()方法
     * 验证能正确创建UserId对象
     */
    @Test
    public void testOf() {
        UserHandle userHandle = UserHandle.getUserHandleForUid(0);
        UserId userId = UserId.of(userHandle);
        assertNotNull(userId);  // 验证返回的UserId不为null
        assertEquals(userId, UserId.of(userHandle));  // 验证相同UserHandle创建的UserId相等
    }

    /**
     * 测试UserId.of()方法传入null参数的情况
     * 预期抛出NullPointerException异常
     */
    @Test(expected = NullPointerException.class)
    public void testOf_withNullUserHandle() {
        UserId.of(null);  // 传入null参数，预期抛出异常
    }

    /**
     * 测试isQuietModeEnabled()方法
     * 验证能正确返回用户的静默模式状态
     */
    @Test
    public void testIsQuietModeEnabled() {
        UserHandle userHandle = UserHandle.getUserHandleForUid(0);
        UserId userId = UserId.of(userHandle);
        
        // 设置模拟UserManager返回true，验证isQuietModeEnabled返回true
        when(mockUserManager.isQuietModeEnabled(userHandle)).thenReturn(true);
        assertTrue(userId.isQuietModeEnabled(mockContext));
        
        // 设置模拟UserManager返回false，验证isQuietModeEnabled返回false
        when(mockUserManager.isQuietModeEnabled(userHandle)).thenReturn(false);
        assertFalse(userId.isQuietModeEnabled(mockContext));
    }

    /**
     * 测试hashCode()方法
     * 验证相同UserHandle创建的UserId具有相同的hashCode
     */
    @Test
    public void testHashCode() {
        UserId userId1 = UserId.of(UserHandle.getUserHandleForUid(0));
        UserId userId2 = UserId.of(UserHandle.getUserHandleForUid(0));
        
        assertEquals(userId1.hashCode(), userId2.hashCode());  // 验证hashCode相等
    }

    /**
     * 测试read()和write()方法
     * 验证能正确序列化和反序列化UserId对象
     */
    @Test
    public void testReadWrite() throws IOException {
        UserId original = UserId.of(UserHandle.getUserHandleForUid(0));
        
        // 将UserId对象写入字节流
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        DataOutputStream out = new DataOutputStream(byteOut);
        UserId.write(out, original);
        
        // 从字节流中读取UserId对象
        DataInputStream in = new DataInputStream(new ByteArrayInputStream(byteOut.toByteArray()));
        UserId read = UserId.read(in);
        
        assertEquals(original, read);  // 验证序列化前后的对象相等
    }

    /**
     * 测试read()方法处理无效版本号的情况
     * 预期抛出ProtocolException异常
     */
    @Test(expected = ProtocolException.class)
    public void testRead_invalidVersion() throws IOException {
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        DataOutputStream out = new DataOutputStream(byteOut);
        out.writeInt(999); // 写入无效的版本号
        out.writeInt(0);
        
        DataInputStream in = new DataInputStream(new ByteArrayInputStream(byteOut.toByteArray()));
        UserId.read(in);  // 预期抛出ProtocolException
    }
}