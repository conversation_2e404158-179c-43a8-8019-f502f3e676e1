<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource">
    <item
        android:id="@+id/action_close"
        android:contentDescription="@string/string_back"
        android:title="@string/string_back"
        app:showAsAction="always" />
    <item
        android:id="@+id/action_new_folder"
        android:contentDescription="@string/menu_new_folder"
        android:title="@string/menu_new_folder"
        app:showAsAction="always" />
</menu>