/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AccountLogoutReceiver
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/9 14:27
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/9       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.account

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.utils.Log
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils

class AccountLogoutReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent?) {
        if (!PrivacyPolicyController.hasAgreePrivacy(context)) {
            Log.d(TAG, "onReceive -> not agree privacy")
            return
        }
        when (intent?.action) {
            ACTION_ACCOUNT_LOGOUT_1, ACTION_ACCOUNT_LOGOUT_2, ACTION_ACCOUNT_LOGOUT_3 -> {
                Log.d(TAG, "onReceive -> account logout, clear authorization status")
                FileDriveStateUtils.clearAuthorizationStatus()
                FileDriveStateUtils.clearLastRefreshTime()
            }
        }
    }

    companion object {
        private const val TAG = "AccountLogoutReceiver"

        private const val ACTION_ACCOUNT_LOGOUT_1 = "oppo.intent.action.usercenter.ACCOUNT_LOGOUT"
        private const val ACTION_ACCOUNT_LOGOUT_2 = "com.heytap.usercenter.account_logout"
        private const val ACTION_ACCOUNT_LOGOUT_3 = "com.usercenter.action.receive.account_logout"
    }
}