/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: AdvertSwitchHelp.kt
 ** Description: ad tools
 ** Version: 1.0
 ** Date: 2023/8/16
 ** Author: w8028088
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.compat

import android.content.Context
import android.provider.Settings
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.OptimizeStatisticsUtil.AdSwitchStatus
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.Utils
import com.oplus.coreapp.appfeature.AppFeatureProviderUtils
import com.oplus.filemanager.utils.AdHelper
import com.oplus.filemanager.utils.cloudconfig.CloudConfigManager

enum class MachineLevel(var value: String) {
    HIGH("G"), MIDDLE("D"), LOW("C")
}

object AdvertSwitchHelp {
    const val AD_TAG = "file_ad"
    private val adDebug = android.util.Log.isLoggable("FileManagerAdDebug", android.util.Log.DEBUG)

    private const val SETTINGS_RECOMMEND = "recommend_ad"
    private const val RECOMMEND_ENABLED = 1
    private const val RECOMMEND_DEFAULT = RECOMMEND_ENABLED

    private const val BRAND = "realme"

    @VisibleForTesting
    const val DOMESTIC = "domestic"

    private const val CN = "CN"
    private const val MACHINE_AD_SUPPORT = "com.oplus.commercial_product_series"
    private const val FILEMANAGER_AD_SUPPORT = "com.oplus.filemanager.ad_support"
    private const val REGION_FEATURE = "com.oplus.filemanager.ad_region"

    @JvmStatic
    @VisibleForTesting
    fun isSupportMachine(context: Context): Boolean {
        val machineLevel = runCatching {
            AppFeatureProviderUtils.getString(
                context.contentResolver, MACHINE_AD_SUPPORT, MachineLevel.LOW.value
            )
        }.onFailure {
            Log.e(AD_TAG, "get machine fail ${it.message}")
        }.getOrDefault(MachineLevel.LOW.value)

        Log.d(AD_TAG, "the machine level is $machineLevel")
        return machineLevel.equals(MachineLevel.HIGH.value, ignoreCase = true).not()
    }

    @JvmStatic
    fun getAdSwitchStatus(): Boolean {
        var switchStatus = false
        if (PreferencesUtils.haveKey(key = CommonConstants.AD_SWITCH_STATUS)) {
            switchStatus = PreferencesUtils.getBoolean(key = CommonConstants.AD_SWITCH_STATUS, default = false)
        }
        return switchStatus
    }

    @JvmStatic
    fun setAdSwitchStatus(status: Boolean) {
        PreferencesUtils.put(key = CommonConstants.AD_SWITCH_STATUS, value = status)
    }

    @JvmStatic
    fun isShowAd(context: Context): Boolean {
        val isRealMe = Utils.isRealmePhone()
        val switchOpen = getAdSwitchStatus()
        Log.d(AD_TAG, "isRealMe:$isRealMe")

        return if (isRealMe) {
            val enabled = (Settings.System.getInt(context.contentResolver, SETTINGS_RECOMMEND, RECOMMEND_DEFAULT) == RECOMMEND_ENABLED)
            val supportMachine = isSupportMachine(context)
            Log.d(AD_TAG, "isSupportMachine=$supportMachine,enabled=$enabled")
            enabled && switchOpen && supportMachine
        } else {
            switchOpen
        }
    }

    @JvmStatic
    fun showAdSwitch(context: Context): Boolean {
        val isRealMe = Utils.isRealmePhone()
        val isOnePlus = Utils.isOnePlusPhone()
        val isSupportMachine = isSupportMachine(context)
        val isSupportAdRegion = isSupportAdRegion(context)
        val noDomestic = DOMESTIC.equals(MyApplication.flavorRegion, ignoreCase = true).not()

        Log.d(
            AD_TAG,
            "isRealMe:$isRealMe, isOnePlus:$isOnePlus, isOppo:${!isRealMe && !isOnePlus},"
                    + "isSupportMachine=$isSupportMachine," + "isSupportAdRegion=$isSupportAdRegion,noDomestic=$noDomestic"
        )

        return if (!noDomestic) {
            false
        } else if (isRealMe) {
            isSupportMachine && isSupportAdRegion
        } else {
            true
        }
    }

    @JvmStatic
    fun getAdCloudShowAdSwitch(): Boolean = AdHelper.showAdSwitch

    @JvmStatic
    fun updateAdCloud(): Unit {
        CloudConfigManager.initCloudConfig()
        CloudConfigManager.updateHomeAndSubAdType()
    }

    @JvmStatic
    fun isSupportAdRegion(context: Context): Boolean {
        if (AppFeatureProviderUtils.isFeatureSupport(
                context.contentResolver, FILEMANAGER_AD_SUPPORT
            )
        ) {
            return true
        }

        // 外销共包且都接入广告sdk, 所以直接返回true
        if (FeatureCompat.sIsExpRom) {
            return true
        }
        return false
    }

    @JvmStatic
    fun getRegion(context: Context): String {
        if (adDebug) {
            return "ID"
        }

        if (AppFeatureProviderUtils.isFeatureSupport(
                context.contentResolver, FILEMANAGER_AD_SUPPORT
            )
        ) {
            return PropertyCompat.sSystemRegion
        }

        return runCatching {
            val support = AppFeatureProviderUtils.isFeatureSupport(
                context.contentResolver, REGION_FEATURE
            )
            if (support) {
                AppFeatureProviderUtils.getString(context.contentResolver, REGION_FEATURE, CN)
            } else {
                PropertyCompat.sSystemRegion
            }
        }.onFailure {
            Log.d(AD_TAG, "get RG fail  = ${it.message}")
        }.getOrDefault(CN)
    }
}