<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <com.coui.appcompat.sidenavigation.COUISideNavigationBar
        android:id="@+id/side_navigation_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:elevation="0dp"
        app:elevation="0dp"
        tools:openDrawer="start">

        <!-- main content -->
        <FrameLayout
            android:id="@+id/fragment_container_view_detail_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.fragment.app.FragmentContainerView
                android:id="@+id/fragment_container_view_detail"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <com.filemanager.common.view.NavigationView
                android:id="@+id/navigation_tool_for_child"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:visibility="gone"
                app:navigationType="tool" />

            <ImageView
                android:id="@+id/container_mask_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                android:background="@color/coui_color_mask"
                tools:ignore="ContentDescription" />
        </FrameLayout>

        <!-- drawer -->
        <androidx.fragment.app.FragmentContainerView
            android:layout_gravity="start"
            android:id="@+id/fragment_container_view_master"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.coui.appcompat.sidenavigation.COUISideNavigationBar>

    <ImageView
        android:id="@+id/side_navigation_icon"
        android:src="@drawable/ic_panel_fold_normal"
        android:forceDarkAllowed="false"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cover_anima_view"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/anima_view_background">

        <ImageView
            android:id="@+id/cover_icon_left"
            android:layout_width="@dimen/dimen_56dp"
            android:layout_height="@dimen/dimen_56dp"
            android:src="@drawable/ic_launcher_filemanager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            />

        <View
            android:id="@+id/cover_separator_line"
            android:layout_width="@dimen/divider_background_height"
            android:layout_height="match_parent"
            android:background="?attr/couiColorDivider"
            android:forceDarkAllowed="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            />

        <ImageView
            android:id="@+id/cover_icon_right"
            android:layout_width="@dimen/dimen_56dp"
            android:layout_height="@dimen/dimen_56dp"
            android:src="@drawable/ic_launcher_filemanager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>