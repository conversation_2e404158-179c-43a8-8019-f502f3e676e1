package com.coloros.encryption;
import com.coloros.encryption.CallbackInterface;
import com.coloros.encryption.ImageInfo;
import com.coloros.encryption.IEncryptProgressListener;

interface EncyptionInterface{
    void registerCallback(in CallbackInterface callback);
    int encryptionTask(String source,int imageType,boolean scanState);
    int encryptionTasks(in List<String> sources, in int[] imageTypes, boolean scanState, IEncryptProgressListener listener);
    void setStopEncryption(boolean state);
    int dencryptionTask(String rootPath,String targetRootPath,String targetPath,String encrypName,String originalPath,int originalType,String password,boolean replaced);
    void deleteTask(String source);
    String viewTask(String source);
    Bitmap getBitmap(String source,String password);
    void savePasswordAndMode(int mode,String password);
    ImageInfo getSafeFile(String filePath,long fileSize,long thumbSize,String md5,String gid);
    boolean mergeBackupRestoreData();
    boolean deleteBackupRestoreData();
    Bitmap getBitmapForExternal(String source,int originalType,int width,int height);
}
