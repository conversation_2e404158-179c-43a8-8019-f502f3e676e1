/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.superapp
 * * Version     : 1.0
 * * Date        : 2022/11/2
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.superapp.ui.superapp

import android.content.Context
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.StatisticsUtils

private const val SUPER_SCAN_MODE_SP_KEY = "super_scan_mode"

class SuperAppParentFragmentViewModel : BaseViewModel() {
    @VisibleForTesting
    var mLoaderController: LoaderController? = null
    var mSuperAppPackage: String = ""
    val mBrowseModeState by lazy {
        MutableLiveData<Int>(getScanMode())
    }

    @VisibleForTesting
    fun getScanMode(): Int {
        val lastScanMode = ConfigSharedPreferenceUtils.getInt(SUPER_SCAN_MODE_SP_KEY + mSuperAppPackage, 0)
        return if (lastScanMode == 0) {
            KtConstants.SCAN_MODE_GRID
        } else {
            lastScanMode
        }
    }

    fun clickScanModeItem(context: Context? = null) {
        if (mBrowseModeState.value == KtConstants.SCAN_MODE_LIST) {
            mBrowseModeState.value = KtConstants.SCAN_MODE_GRID
            StatisticsUtils.onCommon(context, StatisticsUtils.SCAN_MODE_OTHER_SWITCH, hashMapOf(StatisticsUtils.SCAN_MODE_OTHER_SWITCH to "0"))
        } else {
            mBrowseModeState.value = KtConstants.SCAN_MODE_LIST
            StatisticsUtils.onCommon(context, StatisticsUtils.SCAN_MODE_OTHER_SWITCH, hashMapOf(StatisticsUtils.SCAN_MODE_OTHER_SWITCH to "1"))
        }
        mBrowseModeState.value?.apply {
            ConfigSharedPreferenceUtils.putInt(SUPER_SCAN_MODE_SP_KEY + mSuperAppPackage, this)
        }
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    public override fun onCleared() {
        super.onCleared()
        mLoaderController?.onDestroy()
    }

    //refresh current browse mode state for onNewIntent
    fun refreshBrowseModeState() {
        mBrowseModeState.value = getScanMode()
    }
}