/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SuperAppHelperTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/1/6
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/1/6      1.0        create
 ***********************************************************************/
package com.filemanager.superappsource.helper

import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.CommonConstants.OWORK
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.CategoryAppConfig
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.QQ
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.WECHAT
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import junit.framework.TestCase
import org.junit.Assert
import java.io.File

class SuperAppHelperTest : TestCase() {

    @MockK
    lateinit var mContext: Context

    public override fun setUp() {
        super.setUp()
        MockKAnnotations.init(this)
        mContext = mockk {
            every { contentResolver }.returns(mockk(relaxed = true))
            every { applicationContext }.returns(this)
        }
        mockkStatic(SdkUtils::class)
        mockkStatic(KtUtils::class)
        mockkStatic(KtConstants::class)
        MyApplication.init(mContext)
        mockkStatic(SuperAppHelper::class)
        mockkStatic(FeatureCompat::class)
        mockkStatic(PropertyCompat::class)
        mockkObject(PreferencesUtils)
        mockkStatic(Utils::class)
        mockkObject(PCConnectAction)
        mockkStatic(AppUtils::class)
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(PreferencesUtils::class)
        mockkObject(JavaFileHelper)
    }

    public override fun tearDown() {
        unmockkStatic(SuperAppHelper::class)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(PropertyCompat::class)
        unmockkObject(PreferencesUtils)
        unmockkStatic(Utils::class)
        unmockkObject(PCConnectAction)
        unmockkStatic(AppUtils::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(PreferencesUtils::class)
        unmockkObject(JavaFileHelper)
        unmockkStatic(SdkUtils::class)
        unmockkStatic(KtUtils::class)
        unmockkStatic(KtConstants::class)
    }

    fun testGetMainSuperInitListWhenOShareDisabledOrNot() {

        //precondition begin
        every { VolumeEnvironment.getInternalSdPath(any()) }.returns("sdcard")
        every { FeatureCompat.sIsExpRom } returns false
        every { PropertyCompat.sSystemRegion } returns "CN"
        every {
            PreferencesUtils.getString(
                "app_install_state_cache",
                "CN",
                null
            )
        } returns "6:QQ,7:微信,"
        every { mContext.getString(com.filemanager.common.R.string.remote_computer_file) } returns "远程电脑文件"
        every { mContext.getString(com.filemanager.common.R.string.download) } returns "下载"
        every { mContext.getString(com.filemanager.common.R.string.bluetooth) } returns "蓝牙"
        every { mContext.getString(com.filemanager.common.R.string.oneplus_share) } returns "一加互传"
        every { mContext.getString(com.filemanager.common.R.string.realme_share) } returns "realme 互传"
        every { mContext.getString(com.filemanager.common.R.string.oppo_share) } returns "OPPO 互传"
        every { mContext.getString(com.filemanager.common.R.string.owork_space) } returns "随身工作台"
        every { mContext.getString(com.filemanager.common.R.string.owork_space_new) } returns "网页端访问文件"
        every { mContext.getString(com.filemanager.common.R.string.owork_space_web_page_files) } returns "网页端文件"
        every { mContext.getString(com.filemanager.common.R.string.owork_space_web_page) } returns "网页端"
        every { AppUtils.isAppInstalledByPkgName(any(), any()) }.returns(true)
        mockkStatic("com.filemanager.superappsource.helper.SuperAppConditionsKt")
        every { checkOWorkSuperAppCondition(mContext) } returns true
        //precondition end

        every { SdkUtils.isAtLeastOS16() }.returns(true)
        every { KtUtils.getOWorkName(1) }.returns("网页端")
        every { KtUtils.getOWorkName(2) }.returns("网页端文件")
        every { KtUtils.getOWorkName(3) }.returns("网页端访问文件")

        // test case when sIsOShareDisabled is true
        every { SuperAppHelper.sIsOShareDisabled } returns true
        var listResult = SuperAppHelper.getMainSuperInitList()
        var isShowOShare = checkIfHasOShareItem(listResult)
        Assert.assertEquals(false, isShowOShare)

        // test case when sIsOShareDisabled is false
        every { SuperAppHelper.sIsOShareDisabled } returns false
        listResult = SuperAppHelper.getMainSuperInitList()
        isShowOShare = checkIfHasOShareItem(listResult)
        Assert.assertEquals(true, isShowOShare)
    }

    private fun checkIfHasOShareItem(
        listResult: ArrayList<MainCategoryItemsBean>?,
    ): Boolean {
        var isShowOShare = false
        listResult?.let { list ->
            list.forEach {
                if ("一加互传" == it.name || "realme 互传" == it.name || "OPPO 互传" == it.name) {
                    isShowOShare = true
                }
            }
        }
        return isShowOShare
    }

    fun testShouldReturnNullWhenListIsEmpty() {
        val mainCategoryItemList = createCategoryItemList()
        every { SuperAppHelper.getCategoryItems(mContext) } returns mainCategoryItemList
        val findResult = SuperAppHelper.findSourceBeanFromDocAction(mContext, "")
        Assert.assertEquals(null, findResult)
        val findNullResult = SuperAppHelper.findSourceBeanFromDocAction(mContext, null)
        Assert.assertEquals(null, findNullResult)
    }

    fun testShouldReturnShareWhenListContainShare() {
        val mainCategoryItemList = createCategoryItemList()
        every { SuperAppHelper.getCategoryItems(mContext) } returns mainCategoryItemList
        val findResult = SuperAppHelper.findSourceBeanFromDocAction(mContext, "Share")
        Assert.assertEquals("Share", findResult?.name)
    }

    private fun createCategoryItemList(): MutableList<MainCategoryItemsBean> {
        val mainCategoryItemList = mutableListOf<MainCategoryItemsBean>()
        val shareBean = MainCategoryItemsBean(
            CategoryHelper.CATEGORY_SHARE,
            "Share",
            com.filemanager.common.R.drawable.main_category_share,
            0,
            0,
            null,
            0
        )
        shareBean.fileList = arrayOf("Share")
        mainCategoryItemList.add(shareBean)
        return mainCategoryItemList
    }

    fun testgetCategoryItems() {
        every { FeatureCompat.sIsExpRom }.returns(false)
        every { PropertyCompat.sSystemRegion }.returns("CN")
        every { PreferencesUtils.getString(CategoryAppConfig.APP_CONFIG, "CN") }
            .returns("$WECHAT#$QQ")
        val context = mockk<Context>(relaxed = true)
        val packageManager = mockk<PackageManager>(relaxed = true)
        every { context.packageManager }.returns(packageManager)
        val info1 = PackageInfo()
        val info2 = PackageInfo()
        every { packageManager.getPackageInfo(WECHAT, 0) }.returns(info1)
        every { packageManager.getPackageInfo(QQ, 0) }.returns(info2)
        val appInfo = mockk<ApplicationInfo>(relaxed = true)
        every { appInfo.loadLabel(packageManager) }.returns("test")
        info1.applicationInfo = appInfo
        info1.packageName = WECHAT
        info2.applicationInfo = appInfo
        info2.packageName = QQ
        every { Utils.isNeededSdk24() }.returns(false)
        every { PreferencesUtils.getString(CategoryAppConfig.APP_CONFIG, WECHAT) }
            .returns("Tencent/MicroMsg/WeiXin/#Tencent/MicroMsg/Download/")
        every { PreferencesUtils.getString(CategoryAppConfig.APP_CONFIG, QQ) }
            .returns("Tencent/QQfile_recv/#Tencent/QQ_Images/")
        every { SuperAppHelper.sIsOShareDisabled }.returns(false)
        every { PCConnectAction.isMultiScreenConnectSupport() }.returns(false)
        mockkStatic("com.filemanager.superappsource.helper.SuperAppConditionsKt")
        every { AppUtils.isAppInstalledByPkgName(appContext, OWORK) }.returns(true)
        every { PreferencesUtils.getBoolean(key = CommonConstants.OWORK_FUNCTION_SHOW, default = true) }.returns(true)
        every { JavaFileHelper.checkAnyPathExist(any()) }.returns(false)
        every { checkOWorkSuperAppCondition(mContext) }.returns(false)
    }

    fun testAddShareItem() {
        val mainCategoryItemList = mutableListOf<MainCategoryItemsBean>()
        every { mContext.getString(any()) }.returns("")
        every { FeatureCompat.sIsExpRom } returns false
        every { SuperAppHelper.sIsOShareDisabled } returns true
        SuperAppHelper.addShareItem(mContext, mainCategoryItemList)
        Assert.assertEquals(mainCategoryItemList.size, 0)

        every { SuperAppHelper.sIsOShareDisabled } returns false
        SuperAppHelper.addShareItem(mContext, mainCategoryItemList)
        Assert.assertEquals(mainCategoryItemList.size, 1)

        mainCategoryItemList.clear()
        every { FeatureCompat.sIsExpRom } returns true
        MyApplication.flavorBrand = KtUtils.FLAVOR_ONEPLUS
        SuperAppHelper.addShareItem(mContext, mainCategoryItemList)
        Assert.assertEquals(mainCategoryItemList.size, 0)

        MyApplication.flavorBrand = KtUtils.FLAVOR_OPPO
        every { SuperAppHelper.hasFileInDir(any()) } returns false
        SuperAppHelper.addShareItem(mContext, mainCategoryItemList)
        Assert.assertEquals(mainCategoryItemList.size, 0)

        MyApplication.flavorBrand = KtUtils.FLAVOR_OPPO
        every { SuperAppHelper.hasFileInDir(any()) } returns true
        SuperAppHelper.addShareItem(mContext, mainCategoryItemList)
        Assert.assertEquals(mainCategoryItemList.size, 1)
    }

    fun testHasFileInDir() {
        mockkStatic(JavaFileHelper::class)
        val file = mockk<File>(relaxed = true)
        every { file.exists() }.returns(false)
        Assert.assertEquals(SuperAppHelper.hasFileInDir(file), false)
        every { file.exists() }.returns(true)
        every { file.isDirectory }.returns(false)
        Assert.assertEquals(SuperAppHelper.hasFileInDir(file), false)
        every { file.isDirectory }.returns(true)
        every { JavaFileHelper.listFiles(file) }.returns(null)
        Assert.assertEquals(SuperAppHelper.hasFileInDir(file), false)
        val child = mockk<File>(relaxed = true)
        every { child.exists() }.returns(true)
        every { child.isDirectory }.returns(false)
        val childs = arrayOf(child).toList()
        every { JavaFileHelper.listFiles(file) }.returns(childs)
        Assert.assertEquals(SuperAppHelper.hasFileInDir(file), true)
        every { child.isDirectory }.returns(true)
        every { JavaFileHelper.listFiles(child) }.returns(null)
        Assert.assertEquals(SuperAppHelper.hasFileInDir(file), false)

        val childfile = mockk<File>(relaxed = true)
        every { childfile.isDirectory }.returns(false)
        every { childfile.exists() }.returns(true)
        every { JavaFileHelper.listFiles(child) }.returns(arrayOf(childfile).toList())
        Assert.assertEquals(SuperAppHelper.hasFileInDir(file), true)
        unmockkStatic(JavaFileHelper::class)
    }
}