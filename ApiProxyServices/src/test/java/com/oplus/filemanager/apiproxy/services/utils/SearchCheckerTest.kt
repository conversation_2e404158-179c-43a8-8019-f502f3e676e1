package com.oplus.filemanager.apiproxy.services.utils

import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.SdkUtils
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * SearchChecker的单元测试类
 * 使用Robolectric测试框架进行Android环境下的单元测试
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SearchCheckerTest {

    // 模拟对象声明
    private lateinit var mockPrivacyPolicyController: PrivacyPolicyController
    private lateinit var mockPermissionUtils: PermissionUtils
    private lateinit var mockSdkUtils: SdkUtils
    private lateinit var mockDmpSearchApi: IDmpSearchApi

    /**
     * 测试前置方法
     * 初始化所有模拟对象和静态方法
     */
    @Before
    fun setUp() {
        // 模拟静态对象和方法
        mockkObject(Injector)
        mockkStatic(SdkUtils::class)
        mockkObject(PermissionUtils)
        mockkObject(PrivacyPolicyController)

        // 创建各个模拟对象
        mockPrivacyPolicyController = mockk()
        mockPermissionUtils = mockk()
        mockSdkUtils = mockk()
        mockDmpSearchApi = mockk()

        // 设置Injector的模拟行为
        every { Injector.injectFactory<IDmpSearchApi>() } returns mockDmpSearchApi
    }

    /**
     * 测试后置方法
     * 清理所有模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试check方法 - 所有条件都满足的情况
     */
    @Test
    fun testCheck_AllConditionsPass() {
        // 模拟隐私政策已同意
        every { PrivacyPolicyController.hasAgreePrivacy() } returns true
        // 模拟有存储权限
        every { PermissionUtils.hasStoragePermission() } returns true

        val result = SearchChecker.check()

        // 验证返回CODE_OK(200)
        assertEquals(SearchChecker.CODE_OK, result)
    }

    /**
     * 测试check方法 - 隐私政策未同意的情况
     */
    @Test
    fun testCheck_PrivacyNotAgreed() {
        // 模拟隐私政策未同意
        every { PrivacyPolicyController.hasAgreePrivacy() } returns false
        // 模拟有存储权限
        every { PermissionUtils.hasStoragePermission() } returns true

        val result = SearchChecker.check()

        // 验证返回CODE_DISAGREE_PRIVACY(102)
        assertEquals(SearchChecker.CODE_DISAGREE_PRIVACY, result)
    }

    /**
     * 测试check方法 - 没有存储权限的情况
     */
    @Test
    fun testCheck_NoPermission() {
        // 模拟隐私政策已同意
        every { PrivacyPolicyController.hasAgreePrivacy() } returns true
        // 模拟没有存储权限
        every { PermissionUtils.hasStoragePermission() } returns false

        val result = SearchChecker.check()

        // 验证返回CODE_NO_PERMISSION(103)
        assertEquals(SearchChecker.CODE_NO_PERMISSION, result)
    }

    /**
     * 测试checkOsVersion方法 - 系统版本满足要求(至少OS15)
     */
    @Test
    fun testCheckOsVersion_AtLeastOS15() {
        // 模拟系统版本至少是OS15
        every { SdkUtils.isAtLeastOS15() } returns true

        val result = SearchChecker.checkOsVersion()

        // 验证返回CODE_OK(200)
        assertEquals(SearchChecker.CODE_OK, result)
    }

    /**
     * 测试checkOsVersion方法 - 系统版本低于要求(低于OS15)
     */
    @Test
    fun testCheckOsVersion_BelowOS15() {
        // 模拟系统版本低于OS15
        every { SdkUtils.isAtLeastOS15() } returns false

        val result = SearchChecker.checkOsVersion()

        // 验证返回CODE_NO_MATCH_OS_VERSION(101)
        assertEquals(SearchChecker.CODE_NO_MATCH_OS_VERSION, result)
    }

    /**
     * 测试checkHasAgreePrivacy方法 - 已同意隐私政策
     */
    @Test
    fun testCheckHasAgreePrivacy_Agreed() {
        // 模拟隐私政策已同意
        every { PrivacyPolicyController.hasAgreePrivacy() } returns true

        val result = SearchChecker.checkHasAgreePrivacy()

        // 验证返回CODE_OK(200)
        assertEquals(SearchChecker.CODE_OK, result)
    }

    /**
     * 测试checkHasAgreePrivacy方法 - 未同意隐私政策
     */
    @Test
    fun testCheckHasAgreePrivacy_NotAgreed() {
        // 模拟隐私政策未同意
        every { PrivacyPolicyController.hasAgreePrivacy() } returns false

        val result = SearchChecker.checkHasAgreePrivacy()

        // 验证返回CODE_DISAGREE_PRIVACY(102)
        assertEquals(SearchChecker.CODE_DISAGREE_PRIVACY, result)
    }

    /**
     * 测试checkPermission方法 - 有存储权限
     */
    @Test
    fun testCheckPermission_HasPermission() {
        // 模拟有存储权限
        every { PermissionUtils.hasStoragePermission() } returns true

        val result = SearchChecker.checkPermission()

        // 验证返回CODE_OK(200)
        assertEquals(SearchChecker.CODE_OK, result)
    }

    /**
     * 测试checkPermission方法 - 没有存储权限
     */
    @Test
    fun testCheckPermission_NoPermission() {
        // 模拟没有存储权限
        every { PermissionUtils.hasStoragePermission() } returns false

        val result = SearchChecker.checkPermission()

        // 验证返回CODE_NO_PERMISSION(103)
        assertEquals(SearchChecker.CODE_NO_PERMISSION, result)
    }

    /**
     * 测试isOK方法 - 返回true的情况(输入CODE_OK)
     */
    @Test
    fun testIsOK_True() {
        // 验证CODE_OK返回true
        assertTrue(SearchChecker.isOK(SearchChecker.CODE_OK))
    }

    /**
     * 测试isOK方法 - 返回false的情况(输入非CODE_OK)
     */
    @Test
    fun testIsOK_False() {
        // 验证非CODE_OK返回false
        assertFalse(SearchChecker.isOK(SearchChecker.CODE_DISAGREE_PRIVACY))
    }

    /**
     * 测试checkFileType方法 - 有效的文件类型
     */
    @Test
    fun testCheckFileType_Valid() {
        // 使用有效的文件类型(TYPE_ALL_POSITION)测试
        val result = SearchChecker.checkFileType(SearchFileType.TYPE_ALL_POSITION)
        // 验证返回CODE_OK(200)
        assertEquals(SearchChecker.CODE_OK, result)
    }

    /**
     * 测试checkFileType方法 - 无效的文件类型
     */
    @Test
    fun testCheckFileType_Invalid() {
        // 使用无效的文件类型(-1)测试
        val result = SearchChecker.checkFileType(-1)
        // 验证返回CODE_QUERY_ARGS_ERROR(105)
        assertEquals(SearchChecker.CODE_QUERY_ARGS_ERROR, result)
    }
}