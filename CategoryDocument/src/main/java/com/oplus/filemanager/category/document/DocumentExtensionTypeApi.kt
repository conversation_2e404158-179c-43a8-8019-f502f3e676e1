/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  DocumentExtensionTypeApi.kt
 * * Version     : 1.0
 * * Date        : 2022/12/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *     hank.zhou                             1.0       create
 ***********************************************************************/
package com.oplus.filemanager.category.document

import android.content.Context
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import java.util.ArrayList

object DocumentExtensionTypeApi : IDocumentExtensionType {

    private const val TAG = "DocumentExtensionTypeApi"

    private val SUPPORT_OPEN_TYPE = intArrayOf(
        MimeTypeHelper.KEYNOTE_TYPE,
        MimeTypeHelper.PAGES_TYPE,
        MimeTypeHelper.NUMBERS_TYPE,
        MimeTypeHelper.MARKDOWN_TYPE,
        MimeTypeHelper.DWG_TYPE,
        MimeTypeHelper.DWT_TYPE,
        MimeTypeHelper.DXF_TYPE,
        MimeTypeHelper.XMIND_TYPE,
        MimeTypeHelper.PSD_TYPE,
        MimeTypeHelper.OFD_TYPE,
        MimeTypeHelper.AI_TYPE,
        MimeTypeHelper.VSDX_TYPE,
        MimeTypeHelper.VSDM_TYPE,
        MimeTypeHelper.VSTX_TYPE,
        MimeTypeHelper.VSTM_TYPE,
        MimeTypeHelper.VSSX_TYPE,
        MimeTypeHelper.VSSM_TYPE,
        MimeTypeHelper.VSD_TYPE,
        MimeTypeHelper.VSS_TYPE,
        MimeTypeHelper.VST_TYPE,
        MimeTypeHelper.VDW_TYPE,
    )

    override fun getDocumentFormat(context: Context): ArrayList<String> {
        Log.d(TAG, "getDocumentFormat")
        return DocFormatEnhancement.getDocumentFormat()
    }

    override fun getDocumentCountSqlQuery(context: Context): String {
        Log.d(TAG, "getDocumentCountSqlQuery")
        return if (FeatureCompat.sIsExpRom) {
            MediaStoreCompat.getMediaCountSqlQuery(CategoryHelper.CATEGORY_DOC)
        } else {
            DocFormatEnhancement.documentCountSqlQuery
        }
    }

    override fun getDocumentSqlQuery(selectionArg: ArrayList<String?>): String {
        Log.d(TAG, "getDocumentSqlQuery")
        return if (FeatureCompat.sIsExpRom) {
            MediaStoreCompat.getMediaStoreSqlQuery(CategoryHelper.CATEGORY_DOC, selectionArg)
        } else {
            DocFormatEnhancement.getDocumentSqlQuery(selectionArg)
        }
    }

    override fun getAllSelectionArgs(fileExts: ArrayList<String>?): ArrayList<String?> {
        Log.d(TAG, "getAllSelectionArgs")
        return if (FeatureCompat.sIsExpRom) {
            CategoryDocumentApi.getAllSelectionArgs(fileExts)
        } else {
            DocFormatEnhancement.getAllSelectionArgs(fileExts)
        }
    }

    override fun isCategoryDocSupportType(baseFileBean: BaseFileBean): Boolean {
        return DocFormatEnhancement.isDocEnhancement(baseFileBean)
    }

    override fun getSearchArrayForDoc(oldArray: IntArray): Pair<IntArray, Array<String>> {
        Log.d(TAG, "getSearchArrayForDoc")
        return if (FeatureCompat.sIsExpRom) {
            val strArray =
                appContext.resources.getStringArray(com.filemanager.common.R.array.search_filter_doc_ext)
            Pair(oldArray, strArray)
        } else {
            DocFormatEnhancement.getSearchArrayForDoc(oldArray)
        }
    }

    override fun getArrayExtByFilterItem(itemId: Int): Array<String> {
        Log.d(TAG, "getArrayExtByFilterItem")
        return DocFormatEnhancement.getArrayExtByFilterItem(itemId)
    }

    override fun classifyFileByMimeType(
        file: BaseFileBean,
        map: HashMap<String, ArrayList<BaseFileBean>>
    ) {
        Log.d(TAG, "classifyFileByMimeType")
        if (FeatureCompat.sIsExpRom) {
            return SortHelper.classifyFileByMimeType(
                file,
                map[IDocumentExtensionType.ITEM_FOLDER]!!,
                map[IDocumentExtensionType.ITEM_IMAGE]!!,
                map[IDocumentExtensionType.ITEM_VIDEO]!!,
                map[IDocumentExtensionType.ITEM_AUDIO]!!,
                map[IDocumentExtensionType.ITEM_DOC]!!,
                map[IDocumentExtensionType.ITEM_APP]!!,
                map[IDocumentExtensionType.ITEM_COMPRESS]!!,
                map[IDocumentExtensionType.ITEM_OTHER]!!
            )
        } else {
            return DocFormatEnhancement.classifyFileByMimeType(
                file,
                map[IDocumentExtensionType.ITEM_FOLDER]!!,
                map[IDocumentExtensionType.ITEM_IMAGE]!!,
                map[IDocumentExtensionType.ITEM_VIDEO]!!,
                map[IDocumentExtensionType.ITEM_AUDIO]!!,
                map[IDocumentExtensionType.ITEM_DOC]!!,
                map[IDocumentExtensionType.ITEM_APP]!!,
                map[IDocumentExtensionType.ITEM_COMPRESS]!!,
                map[IDocumentExtensionType.ITEM_OTHER]!!
            )
        }
    }

    override fun sortFiles(
        files: List<BaseFileBean>,
        order: Int,
        lastMode: Int,
        hideHeadLabel: Boolean,
        isDesc: Boolean
    ) {
        Log.d(TAG, "sortFiles order $order, lastMode $lastMode, isDesc $isDesc")
        if (FeatureCompat.sIsExpRom) {
            SortHelper.sortFiles(files, order, lastMode, hideHeadLabel, isDesc)
        } else {
            DocFormatEnhancement.sortFiles(files, order, lastMode, hideHeadLabel, isDesc)
        }
    }

    override fun sortFileIgnoreHeadLabel(
        files: List<BaseFileBean>,
        order: Int,
        lastMode: Int,
        isDesc: Boolean
    ) {
        Log.d(TAG, "sortFileIgnoreHeadLabel")
        if (FeatureCompat.sIsExpRom) {
            SortHelper.sortFileIgnoreHeadLabel(files, order, lastMode, isDesc)
        } else {
            DocFormatEnhancement.sortFileIgnoreHeadLabel(files, order, lastMode, isDesc)
        }
    }

    override fun canOpenByFileManager(path: String?): Boolean {
        return if (FeatureCompat.sIsExpRom) {
            false
        } else {
            val type = MimeTypeHelper.getTypeFromPath(path)
            val result = SUPPORT_OPEN_TYPE.contains(type)
            Log.d(TAG, "canOpenByFileManager result = $result")
            result
        }
    }

    override fun isNeedDocFilter(file: BaseFileBean): Boolean {
        Log.d(TAG, "isNeedDocFilter")
        return if (FeatureCompat.sIsExpRom) {
            true
        } else {
            !DocFormatEnhancement.isDocEnhancement(file)
        }
    }

    override fun classifyOtherFileByMimeType(
        docFiles: ArrayList<BaseFileBean>,
        otherFiles: ArrayList<BaseFileBean>
    ) {
        Log.d(TAG, "classifyOtherFileByMimeType")
        if (!FeatureCompat.sIsExpRom) {
            DocFormatEnhancement.classifyOtherFileByMimeType(docFiles, otherFiles)
        }
    }
}