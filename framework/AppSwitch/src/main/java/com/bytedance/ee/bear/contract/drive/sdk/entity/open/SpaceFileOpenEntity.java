/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : SpaceFileOpenEntity.java
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * * 飞书中intent中带入的数据结构，在查看云文档（我的空间，共享空间，收藏，离线等）中的云端上传的云文档，intent中的extras中会带入这个类的类
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.bytedance.ee.bear.contract.drive.sdk.entity.open;

import android.os.Bundle;
import android.os.Parcel;

import java.util.HashMap;
import java.util.Map;

public class SpaceFileOpenEntity extends BaseSpaceOpenEntity {

    public static final Creator<SpaceFileOpenEntity> CREATOR = new Creator<SpaceFileOpenEntity>() {
        @Override
        public SpaceFileOpenEntity createFromParcel(Parcel in) {
            return new SpaceFileOpenEntity(in);
        }

        @Override
        public SpaceFileOpenEntity[] newArray(int size) {
            return new SpaceFileOpenEntity[size];
        }
    };

    private Map<String, String> mExtra = new HashMap();
    private String mFeedId;
    private boolean mHasSpaceCache;
    private String mHostWikiUrl;
    private String mOriginUrl;
    private Bundle mReminderBinder;
    private String mUrl;


    protected SpaceFileOpenEntity(Parcel parcel) {
        super(parcel);
        this.mUrl = parcel.readString();
        this.mOriginUrl = parcel.readString();
        parcel.readByte(); //反编译会byte读取到没有，会转换为true或false赋值给hasSpaceCache
        this.mReminderBinder = parcel.readBundle();
        this.mExtra = parcel.readHashMap(getClass().getClassLoader());
        this.mFeedId = parcel.readString();
        this.mHostWikiUrl = parcel.readString();
    }


    public boolean isHasSpaceCache() {
        return mHasSpaceCache;
    }

    public String getHostWikiUrl() {
        return mHostWikiUrl;
    }

    public String getOriginUrl() {
        return mOriginUrl;
    }

    public Bundle getReminderBinder() {
        return mReminderBinder;
    }

    public String getUrl() {
        return mUrl;
    }

    public Map<String, String> getExtra() {
        return mExtra;
    }

    public String getFeedId() {
        return mFeedId;
    }
}
