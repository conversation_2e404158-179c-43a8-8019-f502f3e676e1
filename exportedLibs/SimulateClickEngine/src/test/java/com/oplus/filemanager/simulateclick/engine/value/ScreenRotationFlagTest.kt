/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScreenRotationFlagTest.kt
 * Description:
 * The test cases for ScreenRotationFlag
 *
 * Version: 1.0
 * Date: 2024-06-06
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-06-06   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.value

import org.junit.Assert
import org.junit.Test

class ScreenRotationFlagTest : Assert() {

    @Test
    fun `should to target flag when fromAlias with single alias`() {
        assertEquals(ScreenRotationFlag.ROTATION_0, ScreenRotationFlag("0").flag)
        assertEquals(ScreenRotationFlag.ROTATION_90, ScreenRotationFlag("90").flag)
        assertEquals(ScreenRotationFlag.ROTATION_180, ScreenRotationFlag("180").flag)
        assertEquals(ScreenRotationFlag.ROTATION_270, ScreenRotationFlag("270").flag)
        assertEquals(ScreenRotationFlag.ROTATION_ALL, ScreenRotationFlag("all").flag)
    }

    @Test
    fun `should to multi flags when fromAlias with multi alias`() {
        assertEquals(ScreenRotationFlag.ROTATION_ALL, ScreenRotationFlag("0|90|180|270").flag)
    }

    @Test
    fun `should to target alias when toAlias with single flag`() {
        assertEquals("0", ScreenRotationFlag(ScreenRotationFlag.ROTATION_0).toAlias())
        assertEquals("90", ScreenRotationFlag(ScreenRotationFlag.ROTATION_90).toAlias())
        assertEquals("180", ScreenRotationFlag(ScreenRotationFlag.ROTATION_180).toAlias())
        assertEquals("270", ScreenRotationFlag(ScreenRotationFlag.ROTATION_270).toAlias())
    }

    @Test
    fun `should to multi alias when toAlias with multi flags`() {
        assertEquals(
            "0|180",
            ScreenRotationFlag(ScreenRotationFlag.ROTATION_0 or ScreenRotationFlag.ROTATION_180).toAlias()
        )
    }
}