/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved.
 * * COLOROS_EDIT
 * * File: ListSecond
 * * Description: the model for second list to show in adapter
 * * Version: 1.0
 * * Date : 2020/7/17
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/7/17       1.0         the model for second list to show in adapter
 ****************************************************************/
package com.oplus.filemanager.keymove.ui.model

import androidx.annotation.WorkerThread
import com.filemanager.common.base.BaseFileBean

/**
 * This class should be constructed @WorkThread
 */
@WorkerThread
data class ListSecond(var mTitle: String, var mChild: ArrayList<ItemChild>) : Visitable {

    private var mSize = 0L
    private var mSelectedSize = 0L
    @Volatile
    private var mChosenState = Visitable.NOTHING_BE_CHOSEN

    init {
        updateState()
    }

    private fun updateState() {
        var scout = 0
        mSize = 0
        mSelectedSize = 0
        for (child in mChild) {
            scout += child.getChosenState()
            mSize += child.getSize()
            mSelectedSize += child.getSelectedSize()
        }
        mChosenState = when (scout) {
            Visitable.NOTHING_BE_CHOSEN -> Visitable.NOTHING_BE_CHOSEN
            mChild.size * Visitable.ALL_BE_CHOSEN -> Visitable.ALL_BE_CHOSEN
            else -> Visitable.SOME_BE_CHOSEN
        }
    }

    @WorkerThread
    override fun setChosenState(state: Int) {
        if (mChild.isEmpty()) {
            mChosenState = state
            return
        }
        for (child in mChild) {
            child.setChosenState(state)
        }
        updateState()
    }

    @WorkerThread
    override fun getSelectedFile(): ArrayList<BaseFileBean> {
        val list = ArrayList<BaseFileBean>()
        for (child in mChild) {
            list.addAll(child.getSelectedFile())
        }
        return list
    }

    override fun getChosenState(): Int {
        return mChosenState
    }

    override fun getSize(): Long {
        return mSize
    }

    override fun getSelectedSize(): Long {
        return mSelectedSize
    }
}