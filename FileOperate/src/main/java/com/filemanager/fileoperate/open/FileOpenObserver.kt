/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileOpenObserver.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.open

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.net.Uri
import android.view.ContextThemeWrapper
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.R
import com.filemanager.fileoperate.base.BaseFileActionObserver

const val CENTER_ALERT_DIALOG = 0
const val OPEN_ERROR = 1
const val UNKNOWN_FILE_DIALOG = 2
const val OPEN_ANIM = 3
const val ZOOM_WARNING = 4
const val DRM_OPEN_ERROR = 5
const val APP_RECOMMEND_DIALOG = 6
const val FORMAT_NOT_SUPPORT = 7
const val OPEN_ERROR_DFM_DISCONNECT = 8

open class FileOpenObserver(context: ContextThemeWrapper) : BaseFileActionObserver(context) {
    companion object {
        private const val TAG = "FileOpenObserver"
    }

    private var installDialog: AlertDialog? = null
    private var unknownFileDialog: AlertDialog? = null
    private var recommendAppDialog: AlertDialog? = null
    private var notSupportDialog: AlertDialog? = null

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        Log.i(TAG, "onChanged: ")
        val temp = result.second
        when (result.first) {
            CENTER_ALERT_DIALOG ->
                if (temp is Pair<*, *>) {
                    installDialog = showNormalCenterCOUIAlertDialog(context,
                            temp.first as DialogInterface.OnClickListener, temp.second as DialogInterface.OnClickListener,
                            context.getString(com.filemanager.common.R.string.not_install_wps),
                            context.getString(com.filemanager.common.R.string.go_to_download),
                            context.getString(com.filemanager.common.R.string.open_with_other_app),
                            context.getString(com.filemanager.common.R.string.button_cancel_text),
                            true)
                }
            OPEN_ERROR -> {
                CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
                onActionDone(false)
            }
            OPEN_ERROR_DFM_DISCONNECT -> {
                CustomToast.showShort(com.filemanager.common.R.string.open_failed_by_device_disconnect)
                onActionDone(false)
            }
            UNKNOWN_FILE_DIALOG -> {
                if (temp is UnKnownFileBean) {
                    CustomToast.showShort(com.filemanager.common.R.string.doc_viewer_not_support_format)
                    unknownFileDialog = UnknownFilesDialog().showUnknownFilesDialog(context, temp)
                }
            }
            OPEN_ANIM -> {
                if (context is Activity) {
                    context.overridePendingTransition(
                        com.support.appcompat.R.anim.coui_open_slide_enter,
                            com.support.appcompat.R.anim.coui_open_slide_exit)
                }
            }
            ZOOM_WARNING -> {
                if (UIConfigMonitor.isZoomWindowShow()) {
                    CustomToast.showLong(com.filemanager.common.R.string.toast_opened_without_window_mode)
                }
            }
            DRM_OPEN_ERROR -> {
                CustomToast.showShort(com.filemanager.common.R.string.string_drm_unable_to_open)
                onActionDone(false)
            }
            APP_RECOMMEND_DIALOG -> {
                if ((temp !is Pair<*, *>) || (context !is Activity)) {
                    Log.w(TAG, "onChanged: APP_RECOMMEND_DIALOG can not be inited")
                    return false
                }
                val title = context.getString(com.filemanager.common.R.string.unknown_file_message1) + temp.first
                recommendAppDialog = COUIAlertDialogBuilder(context).setTitle(title)
                        .setPositiveButton(com.filemanager.common.R.string.go_to_download) { _, _ ->
                            FileActionOpen.UnknownFileManager.downloadApps(context, temp.second as String)
                        }
                        .setNegativeButton(android.R.string.cancel, null).show()
            }
            FORMAT_NOT_SUPPORT -> {
                if (temp is UnKnownFileBean) {
                    notSupportDialog = showNoneDealWithDialog(context, temp)
                }
            }
            else -> return false
        }
        return true
    }

    private fun showNormalCenterCOUIAlertDialog(
        context: Context?,
        listener: DialogInterface.OnClickListener?,
        cancelListener: DialogInterface.OnClickListener?,
        title: String?, positiveString: String?,
        negativeString: String?, cancel: String?,
        cancelable: Boolean
    ): AlertDialog {
        val builder = COUIAlertDialogBuilder(context!!)
            .setTitle(title)
            .setPositiveButton(positiveString, listener)
            .setNeutralButton(negativeString, cancelListener)
            .setNegativeButton(cancel, null)
            .setCancelable(cancelable)
        return builder.show()
    }

    private fun showNoneDealWithDialog(context: Context, temp: UnKnownFileBean): AlertDialog {
        val builder = COUIAlertDialogBuilder(context)
            .setTitle(com.filemanager.common.R.string.doc_viewer_not_support_format)
            .setPositiveButton(com.filemanager.common.R.string.positive_ok) { dialog, _ ->
                dialog.dismiss()
            }
        builder.setOnDismissListener {
            if (temp.isFromRecentCardWidget) {
                (context as? Activity)?.finish()
            }
        }
        return builder.show()
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || installDialog?.isShowing ?: false || unknownFileDialog?.isShowing ?: false
                || recommendAppDialog?.isShowing ?: false || notSupportDialog?.isShowing ?: false
    }

    data class UnKnownFileBean(var path: String?, var uri: Uri, var openFlag: Boolean, var isFromRecentCardWidget: Boolean)
}