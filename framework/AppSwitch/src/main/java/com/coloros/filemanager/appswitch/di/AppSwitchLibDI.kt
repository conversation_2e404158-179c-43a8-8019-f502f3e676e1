/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: AppSwitchLibDI.kt
 ** Description: appSwitch的注入类
 ** Version: 1.0
 ** Date: 2024/6/12
 ** Author: huangyuanwang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.coloros.filemanager.appswitch.di

import androidx.annotation.Keep
import com.coloros.filemanager.appswitch.AppSwitchApi
import com.oplus.filemanager.interfaze.appswitch.IAppSwitchApi
import org.koin.dsl.module

@Keep
class AppSwitchLibDI {

    val appSwitchLibModule = module {
        single<IAppSwitchApi>(createdAtStart = true) {
            AppSwitchApi
        }
    }
}