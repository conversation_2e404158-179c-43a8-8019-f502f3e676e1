/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowPathHelper.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/9/03
 ** Author: JiaBo Lan
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/9/03      1.0     create
 ****************************************************************/
package shadow;

import android.content.Context;

import com.filemanager.common.helper.PathHelper;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(PathHelper.class)
public class ShadowPathHelper {
    private static String sRootPath;
    @Implementation
    public void __constructor__(Context context) {

    }
    @Implementation
    public String getRootPath() {
        return sRootPath;
    }

    public static void setsRootPath(String rootPath) {
        sRootPath = rootPath;
    }
}
