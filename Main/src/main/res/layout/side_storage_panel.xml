<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <include
        android:id="@+id/side_phone_storage"
        layout="@layout/item_side_storage_wrapper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <include
        android:id="@+id/side_otg_storage"
        layout="@layout/item_side_storage_wrapper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <include
        android:id="@+id/side_remote_device"
        layout="@layout/side_remote_device_list_wrapper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <include
        android:id="@+id/side_sd_card_storage"
        layout="@layout/item_side_storage_wrapper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <include
        android:id="@+id/side_dfm_storage"
        layout="@layout/item_side_storage_wrapper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <include
        android:id="@+id/side_cloud"
        layout="@layout/item_side_options"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone" />
</LinearLayout>