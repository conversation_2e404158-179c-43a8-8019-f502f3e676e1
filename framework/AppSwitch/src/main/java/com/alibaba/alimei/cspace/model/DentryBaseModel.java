/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DentryBaseModel.kt
 * * Description : 钉钉中的intent中传输文件的类
 * * Version     : 1.0
 * * Date        : 2024/5/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.alibaba.alimei.cspace.model;

import android.os.Parcel;

import com.alibaba.alimei.framework.model.AbsBaseModel;

public abstract class DentryBaseModel extends AbsBaseModel {

    public String contentType;
    public long createTime;
    public String creatorEmail;
    public String creatorNick;
    public int dirty;
    public long downloadedSize;
    public String extension;

    /* renamed from: id */
    public long id;
    public String localUrl;
    public long modifiedTime;
    public String modifierEmail;
    public String modifierNick;
    public String name;
    public String parentPath;
    public String path;
    public long size;
    public String spaceId;
    public String tempUrl;
    public String type;
    public String versionType;


    public DentryBaseModel(Parcel parcel) {
        this.id = parcel.readLong();
        this.spaceId = parcel.readString();
        this.path = parcel.readString();
        this.name = parcel.readString();
        this.type = parcel.readString();
        this.contentType = parcel.readString();
        this.extension = parcel.readString();
        this.size = parcel.readLong();
        this.createTime = parcel.readLong();
        this.modifiedTime = parcel.readLong();
        this.creatorEmail = parcel.readString();
        this.creatorNick = parcel.readString();
        this.modifierEmail = parcel.readString();
        this.modifierNick = parcel.readString();
        this.versionType = parcel.readString();
        this.localUrl = parcel.readString();
        this.tempUrl = parcel.readString();
        this.dirty = parcel.readInt();
        this.parentPath = parcel.readString();
    }
}
