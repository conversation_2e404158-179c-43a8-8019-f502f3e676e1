/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileProviderExt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/10
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/10      1.0        create
 ***********************************************************************/
@file:JvmName("FileProviderExtKt")
package com.oplus.filemanager.cardwidget.ext

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import com.oplus.filemanager.cardwidget.provider.RecentCardImageFileProvider
import java.io.File

fun writeFileProvider(context: Context, file: File, packageNames: Array<String>): Uri {
    val contentUri = FileProvider.getUriForFile(context, context.packageName, file)
    packageNames.forEach {
        context.grantUriPermission(it, contentUri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
    }
    return contentUri
}
