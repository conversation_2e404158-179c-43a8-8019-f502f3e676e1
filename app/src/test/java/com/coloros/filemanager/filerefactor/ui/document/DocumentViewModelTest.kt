/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.document
 * * Version     : 1.0
 * * Date        : 2020/7/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.filerefactor.ui.document

import com.coloros.filemanager.BaseTest
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.dropdrag.SelectionTracker
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.category.document.ui.DocumentViewModel
import io.mockk.every
import io.mockk.mockkObject
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.Ignore
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class DocumentViewModelTest : BaseTest() {
    private val mDocumentViewModel = DocumentViewModel()

    @Before
    override fun setUp() {
        mContext = RuntimeEnvironment.application
        val file1 = MediaFileWrapper()
        file1.id = 1
        val file2 = MediaFileWrapper()
        file2.id = 2
        val file3 = MediaFileWrapper()
        file3.id = 3
        val list = ArrayList<MediaFileWrapper>()
        list.add(file1)
        list.add(file2)
        list.add(file3)
        mDocumentViewModel.uiState.value = BaseUiModel(list, mDocumentViewModel.mModeState,
                ArrayList(), HashMap())
    }

    @Test
    fun testAllExtArray() {
        Assert.assertEquals(
            arrayListOf(".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".ofd", ".txt"),
            DocumentViewModel.DEF_ALL_EXT_ARRAY
        )
    }

    @Test
    @Ignore("Ignore temporarily, solve oppoPallDomesticAplLevelAllDebugUnitTest error")
    @Throws(Exception::class)
    fun should_clickToolbarSelectAll_when_view_model_fun_clickToolbarSelectAll() {
        mDocumentViewModel.uiState.value?.selectedList?.clear()
        mDocumentViewModel.clickToolbarSelectAll()
        Assert.assertEquals(mDocumentViewModel.uiState.value?.fileList?.size, mDocumentViewModel.uiState.value?.selectedList?.size)
        mDocumentViewModel.clickToolbarSelectAll()
        Assert.assertEquals(0, mDocumentViewModel.uiState.value?.selectedList?.size)
        mDocumentViewModel.uiState.value?.selectedList?.add(
            mDocumentViewModel.uiState.value?.fileList?.get(0)?.id
                ?: 0)
        mDocumentViewModel.clickToolbarSelectAll()
        Assert.assertEquals(mDocumentViewModel.uiState.value?.fileList?.size, mDocumentViewModel.uiState.value?.selectedList?.size)
    }

    @Test
    fun testGetRealFileSize() {
        mDocumentViewModel.uiState.value = null
        Assert.assertEquals(0, mDocumentViewModel.getRealFileSize())

        val file1 = MediaFileWrapper()
        file1.id = 1
        val file2 = MediaFileWrapper()
        file2.id = 2
        val file3 = MediaFileWrapper()
        file3.id = 3
        val list = ArrayList<MediaFileWrapper>()
        list.add(file1)
        list.add(file2)
        list.add(file3)
        val fakeUiModel = BaseUiModel(list, mDocumentViewModel.mModeState, ArrayList(), HashMap())
        mDocumentViewModel.uiState.value = fakeUiModel
        mockkObject(AdvertManager.Companion)
        every { AdvertManager.getAdViewCount(any()) } returns 1
        Assert.assertEquals(2, mDocumentViewModel.getRealFileSize())
    }

    @Test
    fun testGetRecyclerViewScanMode() {
        Assert.assertEquals(SelectionTracker.LAYOUT_TYPE.LIST, mDocumentViewModel.getRecyclerViewScanMode())
        mDocumentViewModel.mScanModeValue = KtConstants.SCAN_MODE_GRID
        Assert.assertEquals(SelectionTracker.LAYOUT_TYPE.GRID, mDocumentViewModel.getRecyclerViewScanMode())
    }

    @After
    override fun tearDown() {
        mContext = null
    }
}