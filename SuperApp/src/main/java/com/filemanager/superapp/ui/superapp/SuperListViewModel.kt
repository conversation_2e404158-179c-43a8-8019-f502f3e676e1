/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.superapp
 * * Version     : 1.0
 * * Date        : 2020/4/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.superapp.ui.superapp

import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.*
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.interfaces.LoadingLoaderListener
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.LIST_NORMAL_MODE
import com.filemanager.common.utils.Log
import com.filemanager.superapp.ui.superapp.SuperLoader.Companion.DEFAULT_DIR_DEPTH
import com.oplus.dropdrag.SelectionTracker
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.Locale

class SuperListViewModel : SelectionViewModel<BaseFileBean, BaseUiModel<BaseFileBean>>() {
    companion object {
        private const val TAG = "SuperListViewModel"
    }

    val mModeState = BaseStateModel(MutableLiveData<Int>(LIST_NORMAL_MODE))
    var mTabPosition = Constants.TAB_ALL
    var mPath: Array<String>? = null
    var mDirDepth: Int = DEFAULT_DIR_DEPTH
    val mPositionModel = MutableLiveData<Int>()
    var mBrowseModeState: Int = KtConstants.SCAN_MODE_GRID
    @VisibleForTesting
    var mNeedScroll = false
    @VisibleForTesting
    val mSuperLoaderCallBack = SuperLoaderCallBack(this)

    fun initLoader(mLoaderController: LoaderController?, tabPosition: Int, paths: Array<String>?, dirDepth: Int) {
        mTabPosition = tabPosition
        mPath = paths
        mDirDepth = dirDepth
        if (mSuperLoaderCallBack.getLoader() == null) {
            mLoaderController?.initLoader(tabPosition, mSuperLoaderCallBack)
        } else {
            loadData()
        }
    }

    override fun loadData() {
        mSuperLoaderCallBack.getLoader()?.forceLoad()
    }

    class SuperLoaderCallBack(viewModel: SuperListViewModel) :
            LoadingLoaderListener<SuperListViewModel, SuperLoader, PathLoadResult<Int, BaseFileBean>>(viewModel,
                viewModel.dataLoadState
            ) {

        override fun onCreateLoader(viewModel: SuperListViewModel?): SuperLoader? {
            return if (viewModel != null) {
                SuperLoader(appContext, viewModel.mTabPosition, viewModel.mPath
                        ?: Array(0) { "" }, viewModel.mDirDepth)
            } else null
        }

        override fun onLoadComplete(viewModel: SuperListViewModel?, result: PathLoadResult<Int, BaseFileBean>?) {
            Log.d(TAG, "SuperListViewModel onLoadFinished size" + result?.mResultList?.size)
            result?.let {
                if (viewModel != null) {
                    viewModel.mModeState.initState = true
                    viewModel.launch {
                        val selectedList = ArrayList<Int>()
                        if ((viewModel.uiState.value?.selectedList?.size ?: 0) > 0) {
                            withContext(Dispatchers.IO) {
                                for (selectedFile in viewModel.uiState.value!!.selectedList) {
                                    if (result.mResultMap.containsKey(selectedFile)) {
                                        selectedList.add(selectedFile)
                                    }
                                }
                            }
                        }
                        if (it.mResultList.isEmpty() && (viewModel.mModeState.listModel.value == KtConstants.LIST_SELECTED_MODE)) {
                            Log.d(TAG, "onLoadComplete mResultList is empty change to normal mode")
                            viewModel.mModeState.listModel.value = LIST_NORMAL_MODE
                        }
                        viewModel.uiState.postValue(BaseUiModel(it.mResultList, viewModel.mModeState, selectedList, it.mResultMap))
                        if (viewModel.mNeedScroll) {
                            viewModel.mPositionModel.value = 0
                            viewModel.mNeedScroll = false
                        }
                    }
                } else {
                    Log.d(TAG, "onLoadComplete viewModel is null")
                }
            }
        }
    }

    fun sortReload() {
        mSuperLoaderCallBack.getLoader()?.forceLoad()
    }

    fun resetState() {
        changeListMode(LIST_NORMAL_MODE)
    }

    fun clickToolbarSelectAll() {
        if (uiState.value?.fileList?.size == uiState.value?.selectedList?.size) {
            uiState.value?.selectedList?.clear()
            uiState.value = uiState.value
        } else {
            uiState.value?.selectedList?.clear()
            uiState.value?.fileList?.let {
                var path: String?
                for (baseFileBean in it) {
                    path = baseFileBean.mData ?: continue
                    if (path.isEmpty()) {
                        continue
                    }
                    uiState.value?.selectedList?.add(path.lowercase(Locale.getDefault()).hashCode())
                }
            }
            uiState.value = uiState.value
        }
    }

    fun selectItem(key: Int) {
        if (uiState.value?.stateModel?.listModel?.value != KtConstants.LIST_SELECTED_MODE) {
            return
        }

        uiState.value?.selectedList?.let {
            if (it.contains(key)) {
                it.remove(key)
            } else {
                it.add(key)
            }
        }
        uiState.value = uiState.value
    }

    fun pressBack(): Boolean {
        if (mModeState.listModel.value == KtConstants.LIST_SELECTED_MODE) {
            changeListMode(LIST_NORMAL_MODE)
            return true
        } else {
            return false
        }
    }

    override fun getRealFileSize(): Int {
        return uiState.value?.fileList?.size ?: 0
    }

    fun getCurrentSpanCount(): Int {
        if (mBrowseModeState == KtConstants.SCAN_MODE_LIST) {
            return 1
        } else {
            when (mTabPosition) {
                Constants.TAB_ALL -> {
                    return 3
                }
                Constants.TAB_IMAGE -> {
                    return 4
                }
                Constants.TAB_VIDEO -> {
                    return 4
                }
                Constants.TAB_DOCUMENT -> {
                    return 3
                }
                else -> {
                    return 3
                }
            }
        }
    }

    override fun getRecyclerViewScanMode(): SelectionTracker.LAYOUT_TYPE {
        return if (mBrowseModeState == KtConstants.SCAN_MODE_LIST) {
            SelectionTracker.LAYOUT_TYPE.LIST
        } else {
            SelectionTracker.LAYOUT_TYPE.GRID
        }
    }

    //update loader path when onNewIntent this page
    fun updateLoaderPath(mPath: Array<String>?) {
        val superLoader = mSuperLoaderCallBack.getLoader()
        superLoader?.let { loader ->
            mPath?.let {
                loader.updatePath(mPath)
            }
        }
    }

    fun setSort(type: Int, desc: Int) {
        mSuperLoaderCallBack.getLoader()?.setSort(type, desc)
    }
}