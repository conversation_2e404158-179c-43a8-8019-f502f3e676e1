/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SelectionItem.kt
 * Description:
 *     The item about selection file
 *
 * Version: 1.0
 * Date: 2024-10-21
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-21   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection.data

import com.filemanager.common.base.BaseFileBean

internal data class SelectionItem(
    val fileBean: BaseFileBean,
    @Transient val selectPriority: Int
) {
    override fun equals(other: Any?): Boolean {
        if (other is SelectionItem) {
            return fileBean == other.fileBean
        }
        return super.equals(other)
    }

    override fun hashCode(): Int {
        return fileBean.hashCode()
    }
}