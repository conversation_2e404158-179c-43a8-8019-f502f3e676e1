/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CategoryRecentLoader
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/14 10:23
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.controller.loader

import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.SdkUtils
import com.oplus.filemanager.cardwidget.recent.data.SUPER_APP_QQ_NAME_LOWERCASE
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.recent.task.BaseMediaDBTask
import com.oplus.filemanager.recent.task.LoadMediaDBTaskQ
import com.oplus.filemanager.recent.task.LoadMediaDBTaskR
import com.oplus.filemanager.recent.utils.RecentUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import java.io.File

class CategoryRecentLoader(
    private val type: Int,
    private val size: Int,
    private val name: String
) {

    private var callback: LoadCallback? = null
    private var loadMediaTask: BaseMediaDBTask? = null
    private val mainScope by lazy { MainScope() }

    fun loadDatas(callback: LoadCallback) {
        this.callback = callback
        loadMediaTask = if (SdkUtils.isAtLeastR()) {
            LoadMediaDBTaskR()
        } else {
            LoadMediaDBTaskQ()
        }
        loadMediaTask?.let {
            mainScope.launch(Dispatchers.IO) {
                val mediaEntity = RecentUtils.getCardWidgetRecentFilesInMediaDB(MyApplication.appContext,
                    KtConstants.LOCAL_VOLUME_MULTI_APP_PATH,
                    type,
                    size,
                    getSuperAppPaths(),
                    loadMediaTask?.getAudioFilterCondition(MyApplication.appContext))
                mediaEntity?.mEntities?.let {
                    callback.loadSuccess(it)
                } ?: callback.loadFailure()
            }
        }
    }

    private fun getSuperAppPaths(): Array<String>? {
        return when (type) {
            CategoryHelper.CATEGORY_DOC,
            CategoryHelper.CATEGORY_COMPRESS,
            CategoryHelper.CATEGORY_APK,
            CategoryHelper.CATEGORY_AUDIO,
            CategoryHelper.CATEGORY_VIDEO,
            CategoryHelper.CATEGORY_IMAGE,
            CategoryHelper.CATEGORY_RECENT -> null
            else -> {
                val superApp = Injector.injectFactory<ISuperApp>()
                val items = superApp?.getCategoryItems(MyApplication.appContext)
                if (items.isNullOrEmpty()) {
                    return null
                }
                val item = if (type == CategoryHelper.CATEGORY_QQ) {
                    if (name.equals(SUPER_APP_QQ_NAME_LOWERCASE, true)) {
                        items.find {
                            type == it.itemType && it.name.equals(SUPER_APP_QQ_NAME_LOWERCASE, true)
                        }
                    } else {
                        items.find {
                            type == it.itemType && it.name.equals(SUPER_APP_QQ_NAME_LOWERCASE, true).not()
                        }
                    }
                } else {
                    items.find { it.itemType == type }
                }
                return item?.fileList?.let { getFullPath(it) }
            }
        }
    }

    private fun getFullPath(paths: Array<String>): Array<String> {
        val fullPaths = mutableListOf<String>()
        val internalPath = VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)
        val externalPath = VolumeEnvironment.getExternalSdPath(MyApplication.sAppContext)
        val internalPath999 = if (FeatureCompat.sIsSupportMultiApp) KtConstants.LOCAL_VOLUME_MULTI_APP_PATH else null
        for (path in paths) {
            internalPath?.let {
                fullPaths.add(File(it, path).absolutePath)
            }
            externalPath?.let {
                fullPaths.add(File(it, path).absolutePath)
            }
            internalPath999?.let {
                fullPaths.add(File(it, path).absolutePath)
            }
        }
        return fullPaths.toTypedArray()
    }

    companion object {
        private const val TAG = "CategoryRecentLoader"
    }
}