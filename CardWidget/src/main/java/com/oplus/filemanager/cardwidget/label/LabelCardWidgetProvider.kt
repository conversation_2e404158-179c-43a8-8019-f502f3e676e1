/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LabelCardWidgetProvider
 ** Description : label card provider
 ** Version     : 1.0
 ** Date        : 2022/11/24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue      2022/11/24      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label

import android.content.Context
import com.filemanager.common.utils.Log
import com.oplus.cardwidget.serviceLayer.AppCardWidgetProvider

class LabelCardWidgetProvider : AppCardWidgetProvider() {

    companion object {
        private const val TAG = "LabelCardWidgetProvider"
    }

    private val labelCardController = LabelCardController()

    override fun subscribed(context: Context, widgetCode: String) {
        super.subscribed(context, widgetCode)
        Log.d(TAG, "subscribed widgetCode:$widgetCode")
        labelCardController.addCard(widgetCode)
    }

    override fun unSubscribed(context: Context, widgetCode: String) {
        super.unSubscribed(context, widgetCode)
        Log.d(TAG, "unSubscribed widgetCode:$widgetCode")
        labelCardController.removeCard(widgetCode)
    }

    override fun getCardLayoutName(widgetCode: String): String {
        Log.d(TAG, "getCardLayoutName widgetCode:$widgetCode ")
        return labelCardController.getCardLayoutName(widgetCode)
    }

    override fun onResume(context: Context, widgetCode: String) {
        Log.d(TAG, "onResume widgetCode:$widgetCode")
        labelCardController.refreshOnResume(context, widgetCode)
    }
}