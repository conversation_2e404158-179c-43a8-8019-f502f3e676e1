package com.oplus.filemanager.apiproxy.services

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.os.CancellationSignal
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.oplus.filemanager.apiproxy.services.cache.SearchFileSizeCache
import com.oplus.filemanager.apiproxy.services.cursor.SearchCursor
import com.oplus.filemanager.apiproxy.services.loader.SearchLoaderFactory
import com.oplus.filemanager.apiproxy.services.utils.SearchChecker
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.apiproxy.services.loader.ILoader
import io.mockk.*
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * SearchProvider的单元测试类
 * 用于测试SearchProvider的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
@OptIn(ExperimentalCoroutinesApi::class)
class SearchProviderTest {
    // 模拟的Context对象
    @MockK
    private lateinit var mockContext: Context
    
    // 模拟的ContentResolver对象
    @MockK
    private lateinit var mockContentResolver: ContentResolver
    
    // 模拟的DMP搜索API对象
    @MockK
    private lateinit var mockDmpSearchApi: IDmpSearchApi
    
    // 模拟的远程设备对象
    @MockK
    private lateinit var mockRemoteDevice: IRemoteDevice
    
    // 模拟的加载器对象
    @MockK
    private lateinit var mockLoader: ILoader
    
    // 模拟的Cursor对象
    @MockK
    private lateinit var mockCursor: Cursor
    
    // 被测试的SearchProvider实例
    private lateinit var searchProvider: SearchProvider
    
    // 测试用的协程调度器
    private val testDispatcher = StandardTestDispatcher()
    
    /**
     * 测试前的初始化方法
     * 用于设置测试环境和模拟对象
     */
    @Before
    fun setUp() {
        // 初始化MockK注解
        MockKAnnotations.init(this)
        // 设置主调度器为测试调度器
        Dispatchers.setMain(testDispatcher)
        
        // 配置模拟对象的行为
        every { mockContext.contentResolver } returns mockContentResolver
        every { mockContext.getSystemService(Context.APP_OPS_SERVICE) } returns mockk(relaxed = true)
        
        // 模拟静态对象
        mockkObject(Injector)
        mockkObject(SearchFileSizeCache)
        mockkObject(SearchLoaderFactory)
        mockkObject(SearchChecker)
        mockkStatic(Log::class)
        mockkStatic(SdkUtils::class)
        
        // 创建被测试对象的spy实例
        searchProvider = spyk(SearchProvider())
    }
    
    /**
     * 测试后的清理方法
     * 用于重置测试环境和清理模拟对象
     */
    @After
    fun tearDown() {
        // 重置主调度器
        Dispatchers.resetMain()
        // 清理所有模拟对象
        unmockkAll()
    }
    
    /**
     * 测试当context为null时的query方法
     * 预期返回null
     */
    @Test
    fun testQueryWhenContextIsNull() {
        // 准备mock
        every { SearchChecker.check() } returns 0
        every { SearchChecker.isOK(0) } returns true
        every { searchProvider.context } returns null
        
        // 设置调用包名
        mockkStatic("android.content.ContentProvider")
        every { searchProvider.callingPackage } returns "test.package"
        
        // 执行query方法
        val result = searchProvider.query(Uri.EMPTY, null, Bundle(), null)
        
        // 验证结果应为null
        assertNull(result)
    }
    
    /**
     * 测试当queryArgs为null时的query方法
     * 预期返回null
     */
    @Test
    fun testQueryWhenQueryArgsIsNull() {
        // 准备mock
        every { searchProvider.context } returns mockContext
        every { SearchChecker.check() } returns 0
        every { SearchChecker.isOK(0) } returns true
        
        // 设置调用包名
        mockkStatic("android.content.ContentProvider")
        every { searchProvider.callingPackage } returns "test.package"
        
        // 执行query方法
        val result = searchProvider.query(Uri.EMPTY, null, null, null)
        
        // 验证结果应为null
        assertNull(result)
    }
    
    /**
     * 测试已废弃的query方法
     * 预期返回null
     */
    @Test
    fun testDeprecatedQuery() {
        val result = searchProvider.query(Uri.EMPTY, null, null, null, null)
        assertNull(result)
    }
    
    /**
     * 测试delete方法
     * 预期返回0
     */
    @Test
    fun testDelete() {
        assertEquals(0, searchProvider.delete(Uri.EMPTY, null, null))
    }
    
    /**
     * 测试getType方法
     * 预期返回null
     */
    @Test
    fun testGetType() {
        assertNull(searchProvider.getType(Uri.EMPTY))
    }
    
    /**
     * 测试insert方法
     * 预期返回null
     */
    @Test
    fun testInsert() {
        assertNull(searchProvider.insert(Uri.EMPTY, null))
    }
    
    /**
     * 测试update方法
     * 预期返回0
     */
    @Test
    fun testUpdate() {
        assertEquals(0, searchProvider.update(Uri.EMPTY, null, null, null))
    }
}