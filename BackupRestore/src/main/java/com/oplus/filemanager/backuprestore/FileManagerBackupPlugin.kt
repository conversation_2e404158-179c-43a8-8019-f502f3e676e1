/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileManagerBackupPlugin
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/28
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/7/28      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.backuprestore

import android.content.Context
import android.os.Bundle
import androidx.annotation.VisibleForTesting
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.google.gson.Gson
import com.oplus.backup.sdk.common.host.BREngineConfig
import com.oplus.backup.sdk.component.BRPluginHandler
import com.oplus.backup.sdk.component.plugin.BackupPlugin
import com.oplus.backup.sdk.host.listener.ProgressHelper
import com.oplus.filemanager.backuprestore.BRUtils.saveToFile
import com.oplus.filemanager.cardwidget.label.LabelCardWidgetCodeUtils
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.provider.SearchHistoryDBHelper
import com.oplus.filemanager.provider.ThirdAppFileDBHelper
import com.oplus.filemanager.room.model.SearchHistoryEntity
import java.io.File

class FileManagerBackupPlugin : BackupPlugin() {

    companion object {
        const val TAG = "FileManagerBackupPlugin"
        private const val PREVIEW_SIZE = 1000L
        private const val ALBUM_SCAN_MODE_SP_KEY = "album_scan_mode"
        private const val AUDIO_SCAN_MODE_SP_KEY = "audio_scan_mode"
        private const val DOC_SCAN_MODE_SP_KEY = "doc_scan_mode"
        private const val APK_SCAN_MODE_SP_KEY = "apk_scan_mode"
        private const val COMPRESS_SCAN_MODE_SP_KEY = "compress_scan_mode"
        private const val SUPER_SCAN_MODE_SP_KEY = "super_scan_mode"
        private const val FILE_LABEL_SCAN_MODE_SP_KEY = "file_label_scan_mode"
        private const val FILE_BROWSER_SCAN_MODE_SP_KEY = "file_browser_scan_mode"
    }

    @VisibleForTesting
    lateinit var backupPath: String
    @VisibleForTesting
    var isCancel: Boolean = false

    override fun onCreate(context: Context?, brPluginHandler: BRPluginHandler?, config: BREngineConfig?) {
        super.onCreate(context, brPluginHandler, config)
        if (config != null) {
            backupPath = config.backupRootPath + File.separator + FILE_MANAGER_FOLDER
        }
    }

    override fun onPreview(p0: Bundle?): Bundle {
        val previewBundle = Bundle()
        ProgressHelper.putMaxCount(previewBundle, 1)
        ProgressHelper.putPreviewDataSize(previewBundle, PREVIEW_SIZE)
        return previewBundle
    }

    override fun onPrepare(p0: Bundle?): Bundle {
        val prepareBundle = Bundle()
        ProgressHelper.putMaxCount(prepareBundle, 1)
        return prepareBundle
    }

    override fun onBackup(p0: Bundle?) {
        Log.v(TAG, "onBackup")
        backupFileLabels()
        backupFileMappings()
        backupLabelCardMapping()
        backupSettingValues()
        backupScanModeValues()
        backupOldSortMode()
        backupAllSortMode()
        backupAllSearchHistory()
        backupThirdAppFileDatas()
    }

    /**
     * 新版本，搬到旧版本，旧版本中的排序需要恢复默认，为了兼容旧代码。
     * 因为新版本没有旧版本中的排序了，旧版本搬家排序没有值会导致搬家业务异常，所以必须加上默认值。
     */
    @VisibleForTesting
    fun backupOldSortMode() {
        val sortRecordValueList = mutableListOf<BackupBean>()
        sortRecordValueList.add(BackupBean(recordSortMode = SortHelper.RECYCLE_BIN_DEFAULT_ORDER,  isDesc = true))
        val backupContentRecycleBin = Gson().toJson(sortRecordValueList)
        val backupFileFullPathRecycleBin = backupPath + File.separator + RECYCLE_BIN_SORT_BACKUP_FILE
        saveToFile(this.getFileDescriptor(backupFileFullPathRecycleBin), backupContentRecycleBin)
        sortRecordValueList.clear()

        sortRecordValueList.add(BackupBean(recordSortMode = SortHelper.FILE_TIME_REVERSE_ORDER,  isDesc = true))
        val backupContentParticular = Gson().toJson(sortRecordValueList)
        val backupFileFullPathParticular = backupPath + File.separator + PARTICULAR_SORT_BACKUP_FILE
        saveToFile(this.getFileDescriptor(backupFileFullPathParticular), backupContentParticular)
        sortRecordValueList.clear()

        sortRecordValueList.add(BackupBean(recordSortMode = SortHelper.FILE_TIME_REVERSE_ORDER,  isDesc = true))
        sortRecordValueList.add(BackupBean(recordSortMode = SortHelper.FILE_NAME_ORDER,  isDesc = true))
        val backupContentCategory = Gson().toJson(sortRecordValueList)
        val backupFileFullPath = backupPath + File.separator + CATEGORY_AND_BROWSER_SORT_BACKUP_FILE
        saveToFile(this.getFileDescriptor(backupFileFullPath), backupContentCategory)
    }

    override fun onPause(p0: Bundle?) {
        // do nothing
    }

    @VisibleForTesting
    fun backupLabelCardMapping() {
        val list = LabelCardWidgetCodeUtils.instance.getCacheList()
        val backupFileFullPath = backupPath + File.separator + FILE_LABEL_CARD_MAPPING_BACKUP_FILE
        val backupContent = Gson().toJson(list)
        Log.d(TAG, "backupLabelCardMapping backupContent:$backupContent ")
        saveToFile(this.getFileDescriptor(backupFileFullPath), backupContent)
    }

    @VisibleForTesting
    fun backupFileLabels() {
        val labels = FileLabelDBHelper.getAllLabels()
        val backupContent = Gson().toJson(labels)
        Log.v(TAG, "backupFileLabels backupContent = $backupContent")
        val backupFileFullPath = backupPath + File.separator + FILE_LABEL_BACKUP_FILE
        saveToFile(this.getFileDescriptor(backupFileFullPath), backupContent)
    }

    /**
     * 备份3方应用的db中的数据
     */
    fun backupThirdAppFileDatas() {
        val thirdAppDataList = ThirdAppFileDBHelper.getAllThirdAppFileData()
        val backupContent = Gson().toJson(thirdAppDataList)
        Log.v(TAG, "backupThirdAppFileDatas backupContent = $backupContent")
        val backupFileFullPath = backupPath + File.separator + FILE_THIRD_APP_BACKUP_FILE
        saveToFile(this.getFileDescriptor(backupFileFullPath), backupContent)
    }


    @VisibleForTesting
    fun backupFileMappings() {
        val mappings = FileLabelMappingDBHelper.getAllMappings() ?: listOf()
        val backupContent = Gson().toJson(mappings)
        Log.v(TAG, "backupFileMappings backupContent = $backupContent")
        val backupFileFullPath = backupPath + File.separator + FILE_LABEL_MAPPING_BACKUP_FILE
        saveToFile(this.getFileDescriptor(backupFileFullPath), backupContent)
    }

    override fun onContinue(p0: Bundle?) {
        isCancel = false
    }

    override fun onCancel(p0: Bundle?) {
        isCancel = true
    }

    override fun onDestroy(p0: Bundle?): Bundle {
        val resultBundle = Bundle()
        ProgressHelper.putBRResult(resultBundle, if (isCancel) ProgressHelper.BR_RESULT_CANCEL else ProgressHelper.BR_RESULT_SUCCESS)
        ProgressHelper.putMaxCount(resultBundle, 1)
        ProgressHelper.putCompletedCount(resultBundle, 1)
        Log.d(TAG, "onDestroy resultBundle = $resultBundle. ${Thread.currentThread().name}")
        return resultBundle
    }

    /**备份设置项数据*/
    fun backupSettingValues() {
        val settingValueList = mutableListOf<BackupBean>()
        val settingKeyArray = if (!FeatureCompat.sIsExpRom) {
            arrayOf(
                CommonConstants.NEED_SHOW_HIDDEN_FILES,
                CommonConstants.NEED_SHOW_ENCRYPT_BOX,
                CommonConstants.CLOUD_FUNCTION_SHOW,
                CommonConstants.CLEANUP_FUNCTION_SHOW,
                CommonConstants.OWORK_FUNCTION_SHOW,
                CommonConstants.K_DOCS_FUNCTION_SHOW,
                CommonConstants.TENCENT_DOCS_FUNCTION_SHOW,
                CommonConstants.THIRD_APP_SEARCH_FUNCTION_SHOW,
                CommonConstants.NEED_SHOW_RECENT_CAMERA,
                AndroidDataHelper.PREF_ANDROID_DATA_ACCESS
            )
        } else {
            arrayOf(
                CommonConstants.NEED_SHOW_HIDDEN_FILES, CommonConstants.NEED_SHOW_ENCRYPT_BOX, CommonConstants.OWORK_FUNCTION_SHOW,
                CommonConstants.NEED_SHOW_RECENT_CAMERA, AndroidDataHelper.PREF_ANDROID_DATA_ACCESS
            )
        }
        settingKeyArray.forEach { key ->
            getSettingsValue(settingValueList, key)
        }
        val backupContent = Gson().toJson(settingValueList)
        Log.v(TAG, "backupSettingValues backupContent = $backupContent")
        val backupFileFullPath = backupPath + File.separator + SETTING_VALUES_BACKUP_FILE
        saveToFile(this.getFileDescriptor(backupFileFullPath), backupContent)
    }

    @VisibleForTesting
    fun getSettingsValue(settingValueList: MutableList<BackupBean>, key: String) {
        var settingValue = PreferencesUtils.getBoolean(key = key)
        when (key) {
            CommonConstants.CLOUD_FUNCTION_SHOW -> {
                if (PreferencesUtils.haveKey(key = CommonConstants.CLOUD_FUNCTION_SHOW).not()) {
                    settingValue = true
                }
            }
            CommonConstants.CLEANUP_FUNCTION_SHOW -> {
                if (PreferencesUtils.haveKey(key = CommonConstants.CLEANUP_FUNCTION_SHOW).not()) {
                    settingValue = true
                }
            }
            CommonConstants.OWORK_FUNCTION_SHOW -> {
                if (PreferencesUtils.haveKey(key = CommonConstants.OWORK_FUNCTION_SHOW).not()) {
                    settingValue = true
                }
            }
            CommonConstants.NEED_SHOW_ENCRYPT_BOX -> {
                if (PreferencesUtils.haveKey(key = CommonConstants.NEED_SHOW_ENCRYPT_BOX).not()) {
                    settingValue = true
                }
            }
            CommonConstants.K_DOCS_FUNCTION_SHOW -> {
                if (PreferencesUtils.haveKey(key = CommonConstants.K_DOCS_FUNCTION_SHOW).not()) {
                    settingValue = true
                }
            }
            CommonConstants.TENCENT_DOCS_FUNCTION_SHOW -> {
                if (PreferencesUtils.haveKey(key = CommonConstants.TENCENT_DOCS_FUNCTION_SHOW).not()) {
                    settingValue = true
                }
            }
            CommonConstants.THIRD_APP_SEARCH_FUNCTION_SHOW -> {
                if (PreferencesUtils.haveKey(key = CommonConstants.THIRD_APP_SEARCH_FUNCTION_SHOW).not()) {
                    settingValue = false
                }
            }
            CommonConstants.NEED_SHOW_RECENT_CAMERA -> {
                if (PreferencesUtils.haveKey(key = CommonConstants.NEED_SHOW_RECENT_CAMERA).not()) {
                    settingValue = false
                }
            }
            AndroidDataHelper.PREF_ANDROID_DATA_ACCESS -> {
                if (PreferencesUtils.haveKey(key = AndroidDataHelper.PREF_ANDROID_DATA_ACCESS).not()) {
                    settingValue = true
                }
            }
        }
        Log.v(TAG, "getSettingsValue key = $key  value = $settingValue")
        settingValueList.add(BackupBean(key = key, settingValue = settingValue))
    }

    /**<图片,视频,音频,文档,安装包,压缩包>,<来源>,<标签列表文件>数据*/
    fun backupScanModeValues() {
        val sacnModeValueList = mutableListOf<BackupBean>()
        /**<图片,视频,音频,文档,安装包,压缩包>,<来源>,<标签列表文件> scanmode key值*/
        val scanModeKeyArray = arrayOf(ALBUM_SCAN_MODE_SP_KEY, AUDIO_SCAN_MODE_SP_KEY + CategoryHelper.CATEGORY_VIDEO,
                AUDIO_SCAN_MODE_SP_KEY + CategoryHelper.CATEGORY_AUDIO, DOC_SCAN_MODE_SP_KEY, APK_SCAN_MODE_SP_KEY,
                COMPRESS_SCAN_MODE_SP_KEY, SUPER_SCAN_MODE_SP_KEY, FILE_LABEL_SCAN_MODE_SP_KEY, FILE_BROWSER_SCAN_MODE_SP_KEY)
        scanModeKeyArray.forEach { key ->
            getScanModeKeyAndValue(sacnModeValueList, key)
        }
        val backupContent = Gson().toJson(sacnModeValueList)
        Log.v(TAG, "backup sacnModeValueList backupContent = $backupContent")
        val backupFileFullPath = backupPath + File.separator + SCAN_MODE_VALUES_BACKUP_FILE
        saveToFile(this.getFileDescriptor(backupFileFullPath), backupContent)
    }

    fun getScanModeKeyAndValue(sacnModeValueList: MutableList<BackupBean>, key: String) {
        val scanModeValue = ConfigSharedPreferenceUtils.getInt(key, 0)
        sacnModeValueList.add(BackupBean(key = key, scanMode = scanModeValue))
    }

    /** 备份所有页面浏览排序值  */
    fun backupAllSortMode() {
        val sortRecordValueList = mutableListOf<BackupBean>()
        val map = PreferencesUtils.getPreferences(SortModeUtils.SHARED_PREFS_NAME)?.all
        map?.keys?.let { keys ->
            for (key in keys) {
                val value = map[key]
                if (value is Int) {
                    val desc = map["${key}${SortModeUtils.ORDER_SUFFIX}"] as? Boolean
                    sortRecordValueList.add(BackupBean(key = key, recordSortMode = value, isDesc = desc))
                    Log.v(TAG, "key:$key value is Int $value  desc:$desc")
                }
            }
        }
        val backupContent = Gson().toJson(sortRecordValueList)
        val backupFileFullPath = backupPath + File.separator + ALL_SORT_BACKUP_FILE
        saveToFile(this.getFileDescriptor(backupFileFullPath), backupContent)
    }

    /**备份搜索历史*/
    fun backupAllSearchHistory() {
        val searchHistoryList: List<SearchHistoryEntity> = SearchHistoryDBHelper.getAllSearchHistory()
                ?: return
        val backupContent = Gson().toJson(searchHistoryList)
        Log.v(TAG, "backupAllSearchHistory backupContent = $backupContent")
        val backupFileFullPath = backupPath + File.separator + ALL_SEARCH_HISTORY_BACKUP_FILE
        saveToFile(this.getFileDescriptor(backupFileFullPath), backupContent)
    }
}