/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: GetRusDataUtilsTest.kt
 ** Description: unit test
 ** Version: 1.0
 ** Date : 2022/7/26
 ** Author: YanShengNeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * YanShengNeng 2022/7/14      1.0    create
 ****************************************************************/
package com.oplus.filemanager.compat.oppo

import android.content.Context
import android.provider.Settings
import com.oplus.filemanager.utils.GetRusDataUtils
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class GetRusDataUtilsTest {
    @MockK
    lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `assert result when getRusEnabled`() {
        mockkStatic(Settings.System::class)

        every { Settings.System.getInt(any(), any(), any()) } answers { 0 }
        every { context.contentResolver } answers { mockk() }
        Assert.assertFalse(GetRusDataUtils.getRusEnabled(context))
        every { Settings.System.getInt(any(), any(), any()) } answers { GetRusDataUtils.FILE_AD_STATUS_OPEN }
        Assert.assertTrue(GetRusDataUtils.getRusEnabled(context))
        unmockkStatic(Settings.System::class)
    }
}