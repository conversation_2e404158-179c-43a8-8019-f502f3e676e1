plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/variant.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace "com.oplus.filemanager.backuprestore"
}

dependencies {
    implementation libs.androidx.appcompat
    implementation libs.google.gson
    implementation libs.google.material

    implementation libs.oplus.backup.sdk
    implementation libs.koin.android

    implementation project(':Provider')
    implementation project(':Common')
    implementation project(':CardWidget')
}