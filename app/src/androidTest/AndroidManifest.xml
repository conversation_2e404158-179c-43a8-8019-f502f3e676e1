<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:versionCode="1"
    android:versionName="1.0">

    <uses-sdk android:targetSdkVersion="31" />

    <instrumentation
        android:name="com.oplus.autotest.olt.testlib.common.OplusRunner"
        android:functionalTest="false"
        android:handleProfiling="false"
        android:label="Tests For FileManager"
        android:targetPackage="com.coloros.filemanager">
        <meta-data
            android:name="listener"
            android:value="com.coloros.filemanager.FileMTestRunListener" />
    </instrumentation>

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INJECT_EVENTS" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!--  访问文件打开时间必要权限  -->
    <uses-permission android:name="com.oplus.permission.safe.PROTECT" />

    <queries>
        <package android:name="com.coloros.filemanager" />
    </queries>

    <application
        android:label="@string/filemanager_test_app_name">
        <uses-library
            android:name="androidx.test.runner"
            android:required="false" />

        <activity
            android:name="com.coloros.autotest.layeredtesting.app.MainActivity"
            android:label="@string/filemanager_test_app_name"
            android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>