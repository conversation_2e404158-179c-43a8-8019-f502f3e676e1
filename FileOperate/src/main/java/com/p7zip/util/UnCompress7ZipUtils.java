package com.p7zip.util;

import android.util.Log;

import com.filemanager.fileoperate.decompress.P7ZipDecompressHelper;

public class UnCompress7ZipUtils {
    public static native int excuteCommand(String command);

    public static native Object[] getRarList(String command);

    private static P7ZipDecompressHelper mHelper;

    public static void jniCallBack(int progress) {
        mProgress = progress;
        Log.d("p7zip_jni", "UnCompress7ZipUtils callback progress = " + progress);
        if (mHelper != null) {
            mHelper.updateProgress(progress);
        }
    }

    public static void setHelper(P7ZipDecompressHelper helper) {
        mHelper = helper;
    }

    public static P7ZipDecompressHelper getHelper() {
        return mHelper;
    }

    public static int mProgress = 0;

    public static int executeCommand(String command) {
        return excuteCommand(command);
    }

    public static Object[] getRarInfoList(String command) {
        return getRarList(command);
    }
}
