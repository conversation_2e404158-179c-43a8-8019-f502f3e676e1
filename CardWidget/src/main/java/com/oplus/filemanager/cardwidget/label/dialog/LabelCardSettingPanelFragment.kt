/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LabelCardSettingPanelFragment
 ** Description : 标签卡片的设置弹窗
 ** Version     : 1.0
 ** Date        : 2022/11/25
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue      2022/11/25      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.dialog

import android.annotation.SuppressLint
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.filemanager.common.MyApplication
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.FileEmptyUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.oplus.anim.EffectiveAnimationView
import com.oplus.filemanager.cardwidget.R
import com.oplus.filemanager.cardwidget.label.data.LabelData
import com.oplus.filemanager.cardwidget.util.GridSpaceItemDecoration
import com.oplus.filemanager.cardwidget.util.TextUtil

class LabelCardSettingPanelFragment : COUIPanelFragment() {

    companion object {
        private const val TAG = "LabelCardSettingPanelFragment"
        private const val DOUBLE_ACTION_INTERVAL = 2000L
        const val MAX_COUNT = 4
        private const val SPAN_COUNT = 2
    }

    private var selectId: Long = 0L
    private var mLastClickTime = 0L
    private var settingVM: LabelCardSettingVM? = null
    private var labelNameTv: TextView? = null
    private var labelIconImg: ImageView? = null
    private var moreArrowImg: ImageView? = null
    private var labelMoreTv: TextView? = null
    private var fileRecyclerView: COUIRecyclerView? = null
    private var emptyViewStub: ViewStub? = null
    private var emptyLayout: View? = null
    private var noLabelViewStub: ViewStub? = null
    private var noLabelLayout: View? = null
    private var filesRootLayout: View? = null
    private var adapter: LabelFileAdapter? = null
    private var labelSettingLayout: LinearLayout? = null
    private var labelRecyclerView: COUIRecyclerView? = null
    private var labelAdapter: LabelListAdapter? = null
    private var emptyLabelViewStub: ViewStub? = null
    private var emptyViewLayout: View? = null
    private var emptyImageView: EffectiveAnimationView? = null
    private var isShowEmptyView = false
    private var isStop = false

    override fun initView(panelView: View?) {
        super.initView(panelView)
        val rootView = LayoutInflater.from(activity).inflate(R.layout.card_dialog_label_card_setting, null, false)
        (contentView as? ViewGroup)?.addView(rootView)
        initData()
        initContentView(rootView)
        startObserver()
    }

    private fun initContentView(view: View) {
        initPanelViews(view)
        initToolbar()
        initOutSideViewClickListener()
        initOnBackKeyListener()
        initDismissListener()
        initRecyclerView()
        initLabelRecyclerView()
    }

    private fun initToolbar() {
        dragView.visibility = View.INVISIBLE
        toolbar = toolbar.apply {
            visibility = View.VISIBLE
            title = context.getString(R.string.card_label_title)
            isTitleCenterStyle = true
            inflateMenu(R.menu.menu_lable_setting)
            menu.findItem(R.id.cancel).apply {
                setOnMenuItemClickListener {
                    dismissPanel()
                    true
                }
            }
            menu.findItem(R.id.save).apply {
                setOnMenuItemClickListener {
                    settingVM?.saveLabelCardMapping()
                    dismissPanel()
                    true
                }
            }
        }
    }

    private fun initPanelViews(view: View) {
        labelSettingLayout = view.findViewById(R.id.label_setting_container)
        labelNameTv = view.findViewById(R.id.label_name)
        labelIconImg = view.findViewById(R.id.label_name_icon)
        moreArrowImg = view.findViewById(R.id.more_arrow)
        labelMoreTv = view.findViewById(R.id.label_files_more)
        fileRecyclerView = view.findViewById(R.id.files_recycler)
        labelRecyclerView = view.findViewById(R.id.label_recycler)
        emptyViewStub = view.findViewById(R.id.empty_layout)
        noLabelViewStub = view.findViewById(R.id.no_label_layout)
        emptyLabelViewStub = view.findViewById(R.id.label_setting_empty_layout)
        filesRootLayout = view.findViewById(R.id.files_root_layout)
    }

    private fun initRecyclerView() {
        val activity = activity ?: return
        adapter = LabelFileAdapter(activity, mutableListOf())
        fileRecyclerView?.layoutManager = GridLayoutManager(activity, SPAN_COUNT)
        fileRecyclerView?.addItemDecoration(GridSpaceItemDecoration(SPAN_COUNT))
        fileRecyclerView?.setOverScrollEnable(false)
        fileRecyclerView?.adapter = adapter
    }

    private fun initLabelRecyclerView() {
        val activity = activity ?: return
        val list = mutableListOf<LabelData>()
        labelAdapter = LabelListAdapter(activity, selectId, list)
        labelAdapter?.onItemClickListener = object : OnItemClickListener<LabelData> {
            override fun onItemClick(view: View, position: Int, data: LabelData) {
                Log.d(TAG, "initLabelRecyclerView -> itemClick -> position = $position ; data = $data")
                settingVM?.selectLabel(data.id)
            }
        }
        labelRecyclerView?.layoutManager = LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
        labelRecyclerView?.adapter = labelAdapter
    }

    private fun initEmptyView() {
        if (emptyLayout != null) {
            return
        }
        val view = emptyViewStub?.inflate()
        emptyLayout = view?.findViewById(R.id.empty_data_container)
    }

    private fun initNoLabelView() {
        if (noLabelLayout != null) {
            return
        }
        val view = noLabelViewStub?.inflate()
        labelNameTv?.visibility = View.GONE
        labelIconImg?.visibility = View.GONE
        noLabelLayout = view?.findViewById(R.id.previewSingleTipsLayout)
        val addLabelTv = noLabelLayout?.findViewById<TextView>(R.id.tips)
        addLabelTv?.let {
            it.text = MyApplication.sAppContext.getString(com.filemanager.common.R.string.unselected_label_card_summary)
        }
    }

    private fun initEmptyLabelView() {
        if (emptyViewLayout != null) {
            return
        }
        val view = emptyLabelViewStub?.inflate()
        emptyViewLayout = view?.findViewById(R.id.empty_view_layout)
        emptyImageView = view?.findViewById(R.id.empty_iv)
    }

    private fun setEmptyVisible(visible: Int) {
        if (visible == View.VISIBLE) {
            isShowEmptyView = true
            initEmptyLabelView()
            labelSettingLayout?.isVisible = false
        } else {
            isShowEmptyView = false
            labelSettingLayout?.isVisible = true
        }
        emptyViewLayout?.let { layout ->
            layout.visibility = visible
            if (View.GONE == visible) {
                return
            }
            emptyImageView?.apply {
                if (Utils.isNightMode(MyApplication.sAppContext)) {
                    setAnimation(FileEmptyUtils.LABEL_EMPTY_ANIMATION_JSON_NIGHT)
                } else {
                    setAnimation(FileEmptyUtils.LABEL_EMPTY_ANIMATION_JSON)
                }
                if (isAnimating.not()) {
                    playAnimation()
                }
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initOutSideViewClickListener() {
        setOutSideViewOnTouchListener { _, event ->
            if (event.actionMasked == MotionEvent.ACTION_UP) {
                if (System.currentTimeMillis() - mLastClickTime > DOUBLE_ACTION_INTERVAL) {
                    Toast.makeText(context, getString(com.filemanager.common.R.string.panel_click_outside_view_toast), Toast.LENGTH_SHORT)
                        .show()
                    (parentFragment as? COUIBottomSheetDialogFragment)?.doFeedbackAnimation()
                    mLastClickTime = System.currentTimeMillis()
                } else {
                    dismissPanel()
                }
            }
            true
        }
    }

    private fun initOnBackKeyListener() {
        setDialogOnKeyListener { _, keyCode, event ->
            Log.d(TAG, "onKey keyCode:$keyCode event:$event")
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                if (System.currentTimeMillis() - mLastClickTime > DOUBLE_ACTION_INTERVAL) {
                    Toast.makeText(context, getString(com.filemanager.common.R.string.panel_back_toast), Toast.LENGTH_SHORT).show()
                    (parentFragment as? COUIBottomSheetDialogFragment)?.doFeedbackAnimation()
                    mLastClickTime = System.currentTimeMillis()
                } else {
                    dismissPanel()
                }
                return@setDialogOnKeyListener true
            }
            false
        }
        PredictiveBackUtils.registerOnBackInvokedCallback(this)
    }

    private fun initDismissListener() {
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)?.setOnDismissListener {
            Log.e(TAG, "Setting dialog dismiss,so activity finish")
            activity?.finishAndRemoveTask()
        }
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)?.setPanelBackgroundTintColor(
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackgroundElevatedWithCard)
        )
    }


    private fun initData() {
        val activity = activity ?: return
        settingVM = ViewModelProvider(activity)[LabelCardSettingVM::class.java]
        selectId = settingVM?.labelId?.value ?: LabelCardSettingVM.NO_LABEL
        settingVM?.loadAllLabels()
    }

    private fun startObserver() {
        val activity = activity ?: return
        settingVM?.labelName?.observe(activity) {
            setLabelName(it)
        }

        settingVM?.labelList?.observe(activity) { list ->
            val isEmpty = list.isEmpty()
            setEmptyVisible(if (isEmpty) View.VISIBLE else View.GONE)
            if (isEmpty.not()) {
                labelAdapter?.setLabelList(list)
            }
        }

        settingVM?.fileList?.observe(activity) { pair ->
            if (pair.first == 0) {
                if (isNoLabel()) { //无标签
                    showNoLabelView()
                } else { // 无文件
                    showEmptyView()
                }
            } else { // 有文件
                showContentView(pair.first, pair.second)
            }
        }
    }

    private fun removeObservers() {
        val activity = activity ?: return
        settingVM?.labelName?.removeObservers(activity)
        settingVM?.fileList?.removeObservers(activity)
        settingVM?.labelList?.removeObservers(activity)
    }

    /**
     * 判断是否无标签
     */
    private fun isNoLabel(): Boolean {
        return LabelCardSettingVM.isNoLabel(settingVM?.labelId?.value)
    }

    private fun setLabelName(name: String) {
        val isNoLabel = isNoLabel()
        Log.e(TAG, "setLabelName $name 无标签：$isNoLabel")
        if (isNoLabel) {
            labelNameTv?.text = context?.getString(R.string.card_label_title)
        } else {
            labelNameTv?.text = TextUtil.getLabelNameForLabelCard(name)
        }
    }

    private fun dismissPanel() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    private fun showNoLabelView() {
        Log.e(TAG, "showNoLabelView")
        initNoLabelView()
        emptyLayout?.isVisible = false
        fileRecyclerView?.isVisible = false
        noLabelLayout?.isVisible = true
        labelIconImg?.isVisible = false
        moreArrowImg?.isVisible = false
        labelMoreTv?.isVisible = false
    }

    private fun showEmptyView() {
        Log.e(TAG, "showEmptyView")
        initEmptyView()
        labelNameTv?.isVisible = true
        emptyLayout?.isVisible = true
        fileRecyclerView?.isVisible = false
        noLabelLayout?.isVisible = false
        labelIconImg?.isVisible = true
        moreArrowImg?.isVisible = false
        labelMoreTv?.visibility = View.GONE
    }

    private fun showContentView(count: Int, list: List<BaseFileBean>) {
        Log.e(TAG, "showContentView count:$count")
        emptyLayout?.isVisible = false
        fileRecyclerView?.isVisible = true
        noLabelLayout?.isVisible = false
        labelNameTv?.isVisible = true
        labelIconImg?.isVisible = true
        if (count > MAX_COUNT) {
            labelMoreTv?.visibility = View.VISIBLE
            moreArrowImg?.isVisible = true
        } else {
            labelMoreTv?.visibility = View.GONE
            moreArrowImg?.isVisible = false
        }
        adapter?.setFileList(list)
    }

    override fun onStart() {
        super.onStart()
        if (isStop) {
            settingVM?.loadAllLabels()
        }
        isStop = false
    }

    override fun onStop() {
        super.onStop()
        isStop = true
    }

    override fun onDestroyView() {
        super.onDestroyView()
        removeObservers()
    }
}