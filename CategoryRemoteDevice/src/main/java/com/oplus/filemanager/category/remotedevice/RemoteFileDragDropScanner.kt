/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - DragDropScanner.kt
 ** Description: scanner for dragdrop share
 ** Version: 1.0
 ** Date : 2020/05/27
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/27    1.0     create
 ****************************************************************/

package com.oplus.filemanager.category.remotedevice

import android.content.ClipData
import android.content.ClipDescription
import android.content.Context
import android.net.Uri
import android.os.PersistableBundle
import androidx.annotation.VisibleForTesting
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.constants.CommonConstants.DRAG_FROM_MAC_FILES
import com.filemanager.common.constants.CommonConstants.KEY_NONE_MEDIA_PATH_LIST
import com.filemanager.common.constants.KtConstants.FILE_MANAGER_URI_PREFIX
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.FileDragDropScanner
import com.filemanager.common.dragselection.ScanData
import com.filemanager.common.dragselection.util.DropUtil
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.GsonUtil
import com.filemanager.common.utils.PCConnectAction
import com.oplus.dropdrag.SelectionTracker
import com.oplus.dropdrag.dragdrop.DragScanResult


open class RemoteFileDragDropScanner(
    context: Context,
    scannerListener: RemoteFileDragListener?,
    private val layoutType: SelectionTracker.LAYOUT_TYPE?,
    isGridImg: Boolean
) : FileDragDropScanner(context, scannerListener, layoutType, false) {

    private val isGridImg = isGridImg

    @Suppress("LongMethod")
    @VisibleForTesting
    override fun innerScanFiles(files: ArrayList<BaseFileBean>): DragScanResult {
        val clipItems = ArrayList<ClipData.Item>()
        val clipTypes = ArrayList<String>()
        val scanData = ScanData(clipItems, clipTypes, 0)
        val grantUriPermissionList = arrayListOf<Uri?>()
        val noneMediaPathList = ArrayList<String>()
        for (file in files) {
            if (file.mLocalType == MimeTypeHelper.DRM_TYPE) {
                scanData.drmCount++
                continue
            }

            when (file.mLocalType) {
                MimeTypeHelper.UNKNOWN_TYPE -> DragUtils.getMimeTypeByPath(file, scanData.clipTypes)

                else -> scanData.clipTypes.add(DragUtils.getMimeTypeByType(file.mLocalType))
            }

            val uri = generateUriByFile(file, scanData, grantUriPermissionList)
            addNoneMediaPath(uri, file, noneMediaPathList)
            if (checkMaxCount(scanData.items.size)) {
                return DragScanResult(ERROR_REACH_MAX_COUNT, null, null, null, null, null)
            }
        }

        val isList: Boolean
        if (layoutType == null) {
            return DragScanResult(ERROR_CONTEXT_INVALID, null,
                null, null,
                null, null)
        } else {
            isList = (layoutType == SelectionTracker.LAYOUT_TYPE.LIST)
        }
        // build the detail and cover icon
        val description = ClipDescription("", clipTypes.toTypedArray())
        PCConnectAction.grantUriPermissionsForPad(grantUriPermissionList)
        val bundle = PersistableBundle()
        val noneMediaPathArray = noneMediaPathList.toTypedArray()
        bundle.putStringArray(KEY_NONE_MEDIA_PATH_LIST, noneMediaPathArray)
        // 将 files 列表放入 Bundle
        bundle.putString(DRAG_FROM_MAC_FILES, GsonUtil.toJson(files))
        description.extras = bundle

        return if (isGridImg) {
            DragScanResult(STATUS_GRID_IMG,
                DragUtils.createClipData(description, clipItems), null, null, files.size, null
            )
        } else if (isList) {
            DragScanResult(STATUS_LIST,
                DragUtils.createClipData(description, clipItems), null, null, files.size, null)
        } else {
            DragScanResult(STATUS_GRID,
                DragUtils.createClipData(description, clipItems), null, null, files.size, null)
        }
    }

    private fun addNoneMediaPath(uri: Uri?, file: BaseFileBean, noneMediaPathList: ArrayList<String>) {
        if (uri != null && DropUtil.isFileManagerUri(uri.toString())) {
            (file as RemoteFileBean).originalPath.let { noneMediaPathList.add(it) }
        }
    }
}