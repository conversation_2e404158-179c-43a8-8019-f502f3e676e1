/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - RemoteScriptStore.kt
 * Description:
 *     To store remote scripts into local and then can use them.
 *
 * Version: 1.0
 * Date: 2024-06-12
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-06-12   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.file

import android.content.Context
import androidx.annotation.IntDef
import androidx.annotation.VisibleForTesting
import com.oplus.filemanager.simulateclick.engine.Script
import com.oplus.filemanager.simulateclick.engine.ScriptGsonBuilder
import com.oplus.filemanager.simulateclick.engine.utils.Log
import com.oplus.filemanager.simulateclick.engine.utils.contains
import com.oplus.filemanager.simulateclick.engine.utils.getAppPackageName
import com.oplus.filemanager.simulateclick.engine.utils.isEquals
import com.oplus.filemanager.simulateclick.engine.value.IScriptFlagValue
import java.io.File
import java.io.FileReader
import java.io.FileWriter
import java.nio.file.Files
import kotlin.random.Random

internal class RemoteScriptStore(private val context: Context) {

    private val gson by lazy { ScriptGsonBuilder().create() }

    fun receiveRemoteScripts(remoteFile: File) {
        val remoteFileSet = readFileSetFromRemoteFile(remoteFile)
        if (remoteFileSet == null) {
            Log.e(TAG, "receiveRemoteScripts: No contents in remote file $remoteFile")
            return
        }
        val receivedFileSet = readFileSetFromReceived()
        Log.d(TAG, "receiveRemoteScripts: remote=$remoteFileSet, received=$receivedFileSet")
        val toWrite = mutableSetOf<Script>()
        val toObsolete = mutableSetOf<Script>()
        remoteFileSet.getTargetAppPackages().forEach forEachApp@{ appPkg ->
            val receivedScripts = receivedFileSet?.getFilesForTargetApp(appPkg)
            remoteFileSet.getFilesForTargetApp(appPkg)?.forEach forEachRemote@{ remoteScript ->
                if (receivedScripts == null) {
                    // No received scripts in local, store all remote scripts.
                    toWrite.add(remoteScript)
                    return@forEachRemote
                }
                val comparedReceived = mutableMapOf<Int, MutableSet<Script>>()
                receivedScripts.forEach { receivedScript ->
                    val compare = compareScriptConditions(remoteScript, receivedScript)
                    val comparedScripts = comparedReceived[compare] ?: mutableSetOf()
                    comparedScripts.add(receivedScript)
                    comparedReceived[compare] = comparedScripts
                }
                if (comparedReceived.contains(COMPARE_OVERWRITTEN)) {
                    // Have at least one received script can overwrite remote script, drop it.
                    return@forEachRemote
                }
                if (comparedReceived.contains(COMPARE_EQUIVALENCE)) {
                    // Have at least one received script is equivalence with remote script, drop it.
                    return@forEachRemote
                }
                if (comparedReceived.contains(COMPARE_OVERWRITE)) {
                    /*
                     * Remote script can overwrite some received scripts.
                     * Store remote script and delete received scripts which be overwritten.
                     */
                    toWrite.add(remoteScript)
                    comparedReceived[COMPARE_OVERWRITE]?.let(toObsolete::addAll)
                } else if (comparedReceived.contains(COMPARE_INDEPENDENT)) {
                    // Remote script is independent with any received scripts, store it
                    toWrite.add(remoteScript)
                }
            }
        }
        Log.d(TAG, "receiveRemoteScripts: toWrite=${toWrite.size}, toObsolete=${toObsolete.size}")
        storeScriptsToLocal(toWrite, toObsolete)
    }

    @VisibleForTesting
    internal fun readFileSetFromRemoteFile(remoteFile: File): RemoteScriptsFileSet? {
        if (!remoteFile.exists() || !remoteFile.isFile) {
            return null
        }
        return kotlin.runCatching {
            FileReader(remoteFile).use { reader ->
                RemoteScriptsFileSet.fromJson(context, gson, reader)
            }
        }.onFailure {
            Log.e(TAG, "readFileSetFromRemoteFile: ERROR! $it")
        }.getOrNull()
    }

    @VisibleForTesting
    internal fun readFileSetFromReceived(): RemoteScriptsFileSet? =
        kotlin.runCatching {
            val receivedDir = ScriptFilePathUtils.getRemoteScriptRootDir(context)
            RemoteScriptsFileSet.fromReceivedDir(gson, receivedDir)
        }.onFailure {
            Log.e(TAG, "readFileSetFromReceived: ERROR! $it")
        }.getOrNull()

    /**
     * Compare results:
     *   [COMPARE_OVERWRITE]  : [remoteScript] can overwrite [receivedScript]
     *   [COMPARE_INDEPENDENT]: [remoteScript] and [receivedScript] are mutually independent
     *   [COMPARE_EQUIVALENCE]: [remoteScript] and [receivedScript] have equivalence conditions
     *   [COMPARE_OVERWRITTEN]: [receivedScript] can overwrite [remoteScript]
     *
     * @param remoteScript the new script from remote side
     * @param receivedScript the already received script in local
     * @return compare result
     */
    @ConditionCompareResult
    @VisibleForTesting
    internal fun compareScriptConditions(remoteScript: Script, receivedScript: Script): Int {
        if (remoteScript.getAppPackageName() != receivedScript.getAppPackageName()) {
            return COMPARE_INDEPENDENT
        }
        val verCompare = compareScriptAppVerRange(remoteScript, receivedScript)
        if (verCompare == COMPARE_INDEPENDENT) {
            return COMPARE_INDEPENDENT
        }
        val displayCompare =
            compareScriptConditionFlag(remoteScript.screenDisplay, receivedScript.screenDisplay)
        if (displayCompare == COMPARE_INDEPENDENT || verCompare != displayCompare) {
            return COMPARE_INDEPENDENT
        }
        val orientationCompare = compareScriptConditionFlag(
            remoteScript.screenOrientation,
            receivedScript.screenOrientation
        )
        if (orientationCompare == COMPARE_INDEPENDENT || displayCompare != orientationCompare) {
            return COMPARE_INDEPENDENT
        }
        val rotationCompare =
            compareScriptConditionFlag(remoteScript.screenRotation, receivedScript.screenRotation)
        if (rotationCompare == COMPARE_INDEPENDENT || orientationCompare != rotationCompare) {
            return COMPARE_INDEPENDENT
        }
        val skillCompare = compareScriptSkill(remoteScript, receivedScript)
        if (skillCompare == COMPARE_INDEPENDENT || skillCompare != rotationCompare) {
            return COMPARE_INDEPENDENT
        }
        return compareScriptVersion(remoteScript, receivedScript, skillCompare)
    }

    @ConditionCompareResult
    @VisibleForTesting
    internal fun compareScriptAppVerRange(remoteScript: Script, receivedScript: Script): Int {
        val minVerCompare = if (remoteScript.appMinVersion < receivedScript.appMinVersion) {
            COMPARE_OVERWRITE
        } else if (remoteScript.appMinVersion == receivedScript.appMinVersion) {
            COMPARE_EQUIVALENCE
        } else {
            COMPARE_OVERWRITTEN
        }
        val maxVerCompare = if (remoteScript.appMaxVersion > receivedScript.appMaxVersion) {
            COMPARE_OVERWRITE
        } else if (remoteScript.appMaxVersion == receivedScript.appMaxVersion) {
            COMPARE_EQUIVALENCE
        } else {
            COMPARE_OVERWRITTEN
        }
        if (minVerCompare == maxVerCompare || maxVerCompare == COMPARE_EQUIVALENCE) {
            return minVerCompare
        }
        if (minVerCompare == COMPARE_EQUIVALENCE) {
            return maxVerCompare
        }
        return COMPARE_INDEPENDENT
    }

    @ConditionCompareResult
    @VisibleForTesting
    internal fun compareScriptConditionFlag(
        remoteFlag: IScriptFlagValue,
        receivedFlag: IScriptFlagValue
    ): Int {
        if (remoteFlag.isEquals(receivedFlag)) {
            return COMPARE_EQUIVALENCE
        }
        if (remoteFlag.contains(receivedFlag)) {
            return COMPARE_OVERWRITE
        }
        if (receivedFlag.contains(remoteFlag)) {
            return COMPARE_OVERWRITTEN
        }
        return COMPARE_INDEPENDENT
    }

    @ConditionCompareResult
    @VisibleForTesting
    internal fun compareScriptSkill(remoteScript: Script, receivedScript: Script): Int {
        if (remoteScript.skills == null || receivedScript.skills == null) {
            return if (remoteScript.skills == receivedScript.skills) {
                COMPARE_EQUIVALENCE
            } else {
                COMPARE_INDEPENDENT
            }
        }
        val remoteSkillNames = remoteScript.skills.map { it.skillName }.sortedBy { it }
        val receivedSkillNames = receivedScript.skills.map { it.skillName }.sortedBy { it }
        if (remoteSkillNames == receivedSkillNames) {
            return COMPARE_EQUIVALENCE
        }
        if (remoteScript.skills.containsAll(receivedScript.skills)) {
            return COMPARE_OVERWRITE
        }
        if (receivedScript.skills.containsAll(remoteScript.skills)) {
            return COMPARE_OVERWRITTEN
        }
        return COMPARE_INDEPENDENT
    }

    @ConditionCompareResult
    @VisibleForTesting
    internal fun compareScriptVersion(
        remoteScript: Script,
        receivedScript: Script,
        othersCompare: Int
    ): Int {
        if (othersCompare == COMPARE_OVERWRITE) {
            return if (remoteScript.scriptVersion >= receivedScript.scriptVersion) {
                COMPARE_OVERWRITE
            } else {
                COMPARE_INDEPENDENT
            }
        }
        if (othersCompare == COMPARE_EQUIVALENCE) {
            return if (remoteScript.scriptVersion > receivedScript.scriptVersion) {
                COMPARE_OVERWRITE
            } else if (remoteScript.scriptVersion == receivedScript.scriptVersion) {
                COMPARE_EQUIVALENCE
            } else {
                COMPARE_OVERWRITTEN
            }
        }
        if (othersCompare == COMPARE_OVERWRITTEN) {
            return if (remoteScript.scriptVersion <= receivedScript.scriptVersion) {
                COMPARE_OVERWRITTEN
            } else {
                COMPARE_INDEPENDENT
            }
        }
        return COMPARE_INDEPENDENT
    }

    @VisibleForTesting
    internal fun storeScriptsToLocal(toWrite: Collection<Script>, toObsolete: Collection<Script>) {
        toObsolete.forEach { script ->
            val filePath = script.filePath
            if (filePath.isNullOrEmpty()) {
                Log.e(TAG, "storeScriptsToLocal: ERROR! No file path of obsolete script, $script")
                return@forEach
            }
            val scriptFile = File(filePath)
            runCatching {
                Files.delete(scriptFile.toPath())
            }.onFailure {
                Log.e(TAG, "storeScriptsToLocal: ERROR when delete obsolete file $scriptFile, $it")
            }
        }
        toWrite.forEach { script ->
            val filePath = script.filePath
            if (filePath.isNullOrEmpty()) {
                Log.e(TAG, "storeScriptsToLocal: ERROR! No file path to write script, $script")
                return@forEach
            }
            val writeFile = getNoDupNameFileToWrite(filePath)
            runCatching {
                writeFile.parentFile?.takeUnless { it.exists() }?.run {
                    val mkResult = mkdirs()
                    Log.d(TAG, "storeScriptsToLocal: mkdirs=$mkResult, dir=$absolutePath")
                }
                FileWriter(writeFile).use { writer ->
                    val originJson = script.originFileJson
                    if (originJson.isNullOrEmpty()) {
                        gson.toJson(script, Script::class.java, writer)
                    } else {
                        writer.write(originJson)
                    }
                }
            }.onFailure {
                Log.e(TAG, "storeScriptsToLocal: ERROR when write script file $writeFile")
            }
        }
    }

    private fun getNoDupNameFileToWrite(filePath: String): File {
        val targetFile = File(filePath)
        return getNoDupNameFileToWrite(targetFile)
    }

    @VisibleForTesting
    internal fun getNoDupNameFileToWrite(targetFile: File): File {
        if (!targetFile.exists()) {
            return targetFile
        }
        var renameFile: File
        do {
            val random = Random.nextInt(0, MAX_RANDOM_INT)
            val time = System.currentTimeMillis()
            val newFileName = "${time}_${random}_${targetFile.name}"
            renameFile = obtainNewNameFile(targetFile, newFileName)
        } while (renameFile.exists())
        return renameFile
    }

    @VisibleForTesting
    internal fun obtainNewNameFile(targetFile: File, newFileName: String) =
        File(targetFile.parentFile, newFileName)

    @VisibleForTesting
    internal companion object {
        private const val TAG = "RemoteScriptStore"

        @VisibleForTesting
        internal const val COMPARE_OVERWRITE = 2

        @VisibleForTesting
        internal const val COMPARE_INDEPENDENT = 1

        @VisibleForTesting
        internal const val COMPARE_EQUIVALENCE = 0

        @VisibleForTesting
        internal const val COMPARE_OVERWRITTEN = -1

        private const val MAX_RANDOM_INT = 999999
    }

    @IntDef(
        COMPARE_OVERWRITE,
        COMPARE_INDEPENDENT,
        COMPARE_EQUIVALENCE,
        COMPARE_OVERWRITTEN
    )
    @Retention(AnnotationRetention.SOURCE)
    private annotation class ConditionCompareResult
}