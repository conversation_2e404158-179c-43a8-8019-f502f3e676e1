package com.oplus.dropdrag.recycleview

import android.graphics.Point
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.oplus.dropdrag.SelectionTracker
import com.oplus.dropdrag.utils.Log
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.math.abs
import kotlin.math.sign
import org.junit.Assert.assertEquals

/**
 * AutoScroller的单元测试类，用于测试自动滚动功能的各种场景
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class AutoScrollerTest {

    // 模拟的RecyclerView对象
    private lateinit var mockRecyclerView: RecyclerView
    // 模拟的ScrollHost对象
    private lateinit var mockScrollHost: ScrollHost
    // 列表布局类型
    private val layoutTypeList = SelectionTracker.LAYOUT_TYPE.LIST
    // 网格布局类型
    private val layoutTypeGrid = SelectionTracker.LAYOUT_TYPE.GRID
    // 默认的滚动阈值比例
    private val defaultThresholdRatio = AutoScroller.DEFAULT_SCROLL_THRESHOLD_RATIO

    /**
     * 测试前的初始化方法
     */
    @Before
    fun setUp() {
        // 创建模拟的RecyclerView和ScrollHost
        mockRecyclerView = mockk(relaxed = true)
        mockScrollHost = mockk(relaxed = true)
        // 模拟Log类
        mockkStatic(Log::class)
        every { Log.v(any(), any()) } just runs
        every { Log.w(any(), any()) } just runs
    }

    /**
     * 测试后的清理方法
     */
    @After
    fun tearDown() {
        // 解除所有模拟
        unmockkAll()
    }

    /**
     * 测试创建ListAutoScroller的情况
     */
    @Test
    fun `createAutoScroll returns ListAutoScroller for LIST layout`() {
        val autoScroller = AutoScroller.createAutoScroll(mockScrollHost, layoutTypeList)
        assert(autoScroller is ListAutoScroller)
    }

    /**
     * 测试创建GridAutoScroller的情况
     */
    @Test
    fun `createAutoScroll returns GridAutoScroller for GRID layout`() {
        val autoScroller = AutoScroller.createAutoScroll(mockScrollHost, layoutTypeGrid)
        assert(autoScroller is GridAutoScroller)
    }

    /**
     * 测试ListAutoScroller的reset方法
     */
    @Test
    fun `ListAutoScroller reset clears state`() {
        val listAutoScroller = ListAutoScroller(mockScrollHost, layoutTypeList, defaultThresholdRatio)
        every { mockScrollHost.removeCallback(any()) } just runs

        listAutoScroller.reset()

        verify { mockScrollHost.removeCallback(any()) }
        assertEquals(null, listAutoScroller.getLastLocationForTest())
    }

    /**
     * 测试ListAutoScroller的scroll方法
     */
    @Test
    fun `ListAutoScroller scroll sets last location and schedules runner`() {
        val listAutoScroller = ListAutoScroller(mockScrollHost, layoutTypeList, defaultThresholdRatio)
        val location = Point(100, 200)
        every { mockScrollHost.runAtNextFrame(any()) } just runs

        listAutoScroller.scroll(location)

        assertEquals(location, listAutoScroller.getLastLocationForTest())
        verify { mockScrollHost.runAtNextFrame(any()) }
    }

    /**
     * 测试ListAutoScroller的stopScroll方法
     */
    @Test
    fun `ListAutoScroller stopScroll removes callback`() {
        val listAutoScroller = ListAutoScroller(mockScrollHost, layoutTypeList, defaultThresholdRatio)
        every { mockScrollHost.removeCallback(any()) } just runs

        listAutoScroller.stopScroll()

        verify { mockScrollHost.removeCallback(any()) }
    }

    /**
     * 测试ListAutoScroller在pixelsPastView为0时不滚动的情况
     */
    @Test
    fun `ListAutoScroller runScroll does nothing when pixelsPastView is zero`() {
        val listAutoScroller = ListAutoScroller(mockScrollHost, layoutTypeList, defaultThresholdRatio)
        val location = Point(100, 500)
        every { mockScrollHost.viewHeight } returns 1000
        every { mockScrollHost.viewTop } returns 0
        every { mockScrollHost.viewBottom } returns 1000
        every { mockScrollHost.getItemHeight(any()) } returns 100
        every { mockScrollHost.runAtNextFrame(any()) } just runs

        listAutoScroller.scroll(location)
        listAutoScroller.invokeRunScrollForTest()

        verify(exactly = 0) { mockScrollHost.scrollBy(any()) }
    }

    /**
     * 测试ListAutoScroller在顶部阈值区域向上滚动的情况
     */
    @Test
    fun `ListAutoScroller runScroll scrolls up when in top threshold`() {
        val listAutoScroller = ListAutoScroller(mockScrollHost, layoutTypeList, defaultThresholdRatio)
        val threshold = (1000 * defaultThresholdRatio).toInt()
        val location = Point(100, threshold - 10)
        val itemHeight = 100
        val expectedScroll = -itemHeight

        every { mockScrollHost.viewHeight } returns 1000
        every { mockScrollHost.viewTop } returns 0
        every { mockScrollHost.viewBottom } returns 1000
        every { mockScrollHost.getItemHeight(any()) } returns itemHeight
        every { mockScrollHost.scrollBy(expectedScroll) } just runs
        every { mockScrollHost.runAtNextFrame(any()) } just runs

        listAutoScrollerWithMotionThresholdPassed(listAutoScroller, Point(100, 300))
        listAutoScroller.scroll(location)
        listAutoScroller.invokeRunScrollForTest()

        verify { mockScrollHost.scrollBy(expectedScroll) }
    }

    /**
     * 测试ListAutoScroller在底部阈值区域向下滚动的情况
     */
    @Test
    fun `ListAutoScroller runScroll scrolls down when in bottom threshold`() {
        val listAutoScroller = ListAutoScroller(mockScrollHost, layoutTypeList, defaultThresholdRatio)
        val viewHeight = 1000
        val threshold = (viewHeight * defaultThresholdRatio).toInt()
        val location = Point(100, viewHeight - threshold + 10)
        val itemHeight = 100
        val expectedScroll = itemHeight

        every { mockScrollHost.viewHeight } returns viewHeight
        every { mockScrollHost.viewTop } returns 0
        every { mockScrollHost.viewBottom } returns viewHeight
        every { mockScrollHost.getItemHeight(any()) } returns itemHeight
        every { mockScrollHost.scrollBy(expectedScroll) } just runs
        every { mockScrollHost.runAtNextFrame(any()) } just runs

        listAutoScrollerWithMotionThresholdPassed(listAutoScroller, Point(100, 300))
        listAutoScroller.scroll(location)
        listAutoScroller.invokeRunScrollForTest()

        verify { mockScrollHost.scrollBy(expectedScroll) }
    }

    /**
     * 测试ListAutoScroller对移动阈值的处理
     */
    @Test
    fun `ListAutoScroller runScroll respects motion threshold`() {
        val listAutoScroller = ListAutoScroller(mockScrollHost, layoutTypeList, defaultThresholdRatio)
        val viewHeight = 1000
        val threshold = (viewHeight * defaultThresholdRatio).toInt()
        val motionThreshold = (viewHeight * defaultThresholdRatio * (defaultThresholdRatio * 2)).toInt()
        val origin = Point(100, 500)
        val location = Point(100, 500 + motionThreshold - 5)

        every { mockScrollHost.viewHeight } returns viewHeight
        every { mockScrollHost.viewTop } returns 0
        every { mockScrollHost.viewBottom } returns viewHeight
        every { mockScrollHost.runAtNextFrame(any()) } just runs

        listAutoScroller.scroll(origin)
        listAutoScroller.scroll(location)
        listAutoScroller.invokeRunScrollForTest()

        verify(exactly = 0) { mockScrollHost.scrollBy(any()) }
    }

    /**
     * 测试GridAutoScroller的reset方法
     */
    @Test
    fun `GridAutoScroller reset clears state`() {
        val gridAutoScroller = GridAutoScroller(mockScrollHost, layoutTypeGrid, defaultThresholdRatio)
        every { mockScrollHost.removeCallback(any()) } just runs

        gridAutoScroller.reset()

        verify { mockScrollHost.removeCallback(any()) }
        assertEquals(null, gridAutoScroller.getLastLocationForTest())
    }

    /**
     * 测试GridAutoScroller的scroll方法
     */
    @Test
    fun `GridAutoScroller scroll sets last location and schedules runner`() {
        val gridAutoScroller = GridAutoScroller(mockScrollHost, layoutTypeGrid, defaultThresholdRatio)
        val location = Point(100, 200)
        every { mockScrollHost.runAtNextFrame(any()) } just runs

        gridAutoScroller.scroll(location)

        assertEquals(location, gridAutoScroller.getLastLocationForTest())
        verify { mockScrollHost.runAtNextFrame(any()) }
    }

    /**
     * 测试GridAutoScroller的stopScroll方法
     */
    @Test
    fun `GridAutoScroller stopScroll removes callback`() {
        val gridAutoScroller = GridAutoScroller(mockScrollHost, layoutTypeGrid, defaultThresholdRatio)
        every { mockScrollHost.removeCallback(any()) } just runs

        gridAutoScroller.stopScroll()

        verify { mockScrollHost.removeCallback(any()) }
    }

    /**
     * 测试GridAutoScroller在pixelsPastView为0时不滚动的情况
     */
    @Test
    fun `GridAutoScroller runScroll does nothing when pixelsPastView is zero`() {
        val gridAutoScroller = GridAutoScroller(mockScrollHost, layoutTypeGrid, defaultThresholdRatio)
        val location = Point(100, 500)
        every { mockScrollHost.viewHeight } returns 1000
        every { mockScrollHost.viewTop } returns 0
        every { mockScrollHost.viewBottom } returns 1000
        every { mockScrollHost.runAtNextFrame(any()) } just runs

        gridAutoScroller.scroll(location)
        gridAutoScroller.invokeRunScrollForTest()

        verify(exactly = 0) { mockScrollHost.scrollBy(any()) }
    }

    /**
     * 测试GridAutoScroller在顶部阈值区域向上滚动的情况
     */
    @Test
    fun `GridAutoScroller runScroll scrolls up when in top threshold`() {
        val gridAutoScroller = GridAutoScroller(mockScrollHost, layoutTypeGrid, defaultThresholdRatio)
        val viewHeight = 1000
        val viewTop = 50
        val threshold = (viewHeight * defaultThresholdRatio).toInt()
        val location = Point(100, viewTop + threshold - 10)
        val expectedScroll = -1

        every { mockScrollHost.viewHeight } returns viewHeight
        every { mockScrollHost.viewTop } returns viewTop
        every { mockScrollHost.viewBottom } returns viewTop + viewHeight
        every { mockScrollHost.scrollBy(expectedScroll) } just runs
        every { mockScrollHost.runAtNextFrame(any()) } just runs
        every { mockScrollHost.removeCallback(any()) } just runs

        gridAutoScannerWithMotionThresholdPassed(gridAutoScroller, Point(100, 300))
        gridAutoScroller.scroll(location)
        gridAutoScroller.invokeRunScrollForTest()

        verify { mockScrollHost.scrollBy(expectedScroll) }
    }

    /**
     * 测试GridAutoScroller在底部阈值区域向下滚动的情况
     */
    @Test
    fun `GridAutoScroller runScroll scrolls down when in bottom threshold`() {
        val gridAutoScroller = GridAutoScroller(mockScrollHost, layoutTypeGrid, defaultThresholdRatio)
        val viewHeight = 1000
        val viewTop = 50
        val viewBottom = viewTop + viewHeight
        val threshold = (viewHeight * defaultThresholdRatio).toInt()
        val location = Point(100, viewBottom - threshold + 10)
        val expectedScroll = 1

        every { mockScrollHost.viewHeight } returns viewHeight
        every { mockScrollHost.viewTop } returns viewTop
        every { mockScrollHost.viewBottom } returns viewBottom
        every { mockScrollHost.scrollBy(expectedScroll) } just runs
        every { mockScrollHost.runAtNextFrame(any()) } just runs
        every { mockScrollHost.removeCallback(any()) } just runs

        gridAutoScannerWithMotionThresholdPassed(gridAutoScroller, Point(100, 300))
        gridAutoScroller.scroll(location)
        gridAutoScroller.invokeRunScrollForTest()

        verify { mockScrollHost.scrollBy(expectedScroll) }
    }

    /**
     * 测试GridAutoScroller对pixelsPastView的限制
     */
    @Test
    fun `GridAutoScroller runScroll caps pixelsPastView`() {
        val gridAutoScroller = GridAutoScroller(mockScrollHost, layoutTypeGrid, defaultThresholdRatio)
        val viewHeight = 1000
        val viewTop = 50
        val viewBottom = viewTop + viewHeight
        val threshold = (viewHeight * defaultThresholdRatio).toInt()
        val location = Point(100, viewBottom + threshold * 2)
        val expectedScroll = 6 // 使用实际值而不是访问私有常量

        every { mockScrollHost.viewHeight } returns viewHeight
        every { mockScrollHost.viewTop } returns viewTop
        every { mockScrollHost.viewBottom } returns viewBottom
        every { mockScrollHost.scrollBy(expectedScroll) } just runs
        every { mockScrollHost.runAtNextFrame(any()) } just runs
        every { mockScrollHost.removeCallback(any()) } just runs

        gridAutoScannerWithMotionThresholdPassed(gridAutoScroller, Point(100, 300))
        gridAutoScroller.scroll(location)
        gridAutoScroller.invokeRunScrollForTest()

        verify { mockScrollHost.scrollBy(expectedScroll) }
    }

    /**
     * 测试GridAutoScroller对移动阈值的处理
     */
    @Test
    fun `GridAutoScroller runScroll respects motion threshold`() {
        val gridAutoScroller = GridAutoScroller(mockScrollHost, layoutTypeGrid, defaultThresholdRatio)
        val viewHeight = 1000
        val viewTop = 50
        val threshold = (viewHeight * defaultThresholdRatio).toInt()
        val motionThreshold = (viewHeight * defaultThresholdRatio * (defaultThresholdRatio * 2)).toInt()
        val origin = Point(100, 500)
        val location = Point(100, 500 + motionThreshold - 5)

        every { mockScrollHost.viewHeight } returns viewHeight
        every { mockScrollHost.viewTop } returns viewTop
        every { mockScrollHost.viewBottom } returns viewTop + viewHeight
        every { mockScrollHost.runAtNextFrame(any()) } just runs

        gridAutoScroller.scroll(origin)
        gridAutoScroller.scroll(location)
        gridAutoScroller.invokeRunScrollForTest()

        verify(exactly = 0) { mockScrollHost.scrollBy(any()) }
    }

    /**
     * 辅助方法：设置ListAutoScroller通过移动阈值
     */
    private fun listAutoScrollerWithMotionThresholdPassed(scroller: ListAutoScroller, origin: Point) {
        val viewHeight = 1000
        val motionThreshold = (viewHeight * defaultThresholdRatio * (defaultThresholdRatio * 2)).toInt()
        val distantPoint = Point(origin.x, origin.y + motionThreshold + 5)

        every { mockScrollHost.viewHeight } returns viewHeight
        every { mockScrollHost.viewTop } returns 0
        every { mockScrollHost.viewBottom } returns viewHeight
        every { mockScrollHost.getItemHeight(any()) } returns 100
        every { mockScrollHost.runAtNextFrame(any()) } just runs

        scroller.scroll(origin)
        scroller.scroll(distantPoint)
        scroller.invokeRunScrollForTest()
    }

    /**
     * 辅助方法：设置GridAutoScroller通过移动阈值
     */
    private fun gridAutoScannerWithMotionThresholdPassed(scroller: GridAutoScroller, origin: Point) {
        val viewHeight = 1000
        val viewTop = 50
        val motionThreshold = (viewHeight * defaultThresholdRatio * (defaultThresholdRatio * 2)).toInt()
        val distantPoint = Point(origin.x, origin.y + motionThreshold + 5)

        every { mockScrollHost.viewHeight } returns viewHeight
        every { mockScrollHost.viewTop } returns viewTop
        every { mockScrollHost.viewBottom } returns viewTop + viewHeight
        every { mockScrollHost.runAtNextFrame(any()) } just runs
        every { mockScrollHost.removeCallback(any()) } just runs

        scroller.scroll(origin)
        scroller.scroll(distantPoint)
        scroller.invokeRunScrollForTest()
    }

    /**
     * 反射获取ListAutoScroller的mLastLocation字段值
     */
    private fun ListAutoScroller.getLastLocationForTest(): Point? {
        return javaClass.getDeclaredField("mLastLocation").apply { isAccessible = true }.get(this) as? Point
    }

    /**
     * 反射调用ListAutoScroller的runScroll方法
     */
    private fun ListAutoScroller.invokeRunScrollForTest() {
        val method = javaClass.getDeclaredMethod("runScroll").apply { isAccessible = true }
        method.invoke(this)
    }

    /**
     * 反射获取GridAutoScroller的mLastLocation字段值
     */
    private fun GridAutoScroller.getLastLocationForTest(): Point? {
        return javaClass.getDeclaredField("mLastLocation").apply { isAccessible = true }.get(this) as? Point
    }

    /**
     * 反射调用GridAutoScroller的runScroll方法
     */
    private fun GridAutoScroller.invokeRunScrollForTest() {
        val method = javaClass.getDeclaredMethod("runScroll").apply { isAccessible = true }
        method.invoke(this)
    }
}