/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - FilePreviewTypeCheckHelper.kt
 * Description:
 *     Help to check file type when preview.
 *
 * Version: 1.0
 * Date: 2024-10-17
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><EMAIL>    2024-10-17   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.utils

import android.content.Context
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import java.util.WeakHashMap

internal class FilePreviewTypeCheckHelper(initContext: Context) {

    private val context: Context = initContext.applicationContext
    private val mediaTypeCache: MutableMap<BaseFileBean, Int> = WeakHashMap()

    private fun BaseFileBean.getMediaTypeWithCache(): Int {
        mediaTypeCache[this]?.let { return it }
        val type = getMediaType(context)
        mediaTypeCache[this] = type
        return type
    }

    fun getMediaType(fileBean: BaseFileBean): Int = fileBean.getMediaTypeWithCache()

    fun isApkFileType(fileBean: BaseFileBean, logTag: String? = null): Boolean {
        val type = fileBean.mLocalType
        return isApkFileType(type, logTag)
    }

    fun isArchiveFileType(fileBean: BaseFileBean, logTag: String? = null): Boolean {
        val type = fileBean.mLocalType
        return isArchiveFileType(type, logTag)
    }

    fun isAudioFileType(fileBean: BaseFileBean, logTag: String? = null): Boolean {
        val type = fileBean.getMediaTypeWithCache()
        return isAudioFileType(type, logTag)
    }

    fun isImageFileType(fileBean: BaseFileBean, logTag: String? = null): Boolean {
        val type = fileBean.getMediaTypeWithCache()
        return isImageFileType(type, logTag)
    }

    fun isVideoFileType(fileBean: BaseFileBean, logTag: String? = null): Boolean {
        val type = fileBean.getMediaTypeWithCache()
        return isVideoFileType(type, logTag)
    }

    fun isDocType(fileBean: BaseFileBean, logTag: String? = null): Boolean {
        val type = fileBean.mLocalType
        return isDocType(type, logTag)
    }

    fun isRemoteType(fileBean: BaseFileBean, logTag: String? = null): Boolean {
        if (logTag != null) {
            Log.e(logTag, "isRemoteFileType: type ${fileBean.mLocalType}")
        }
        return fileBean is RemoteFileBean
    }

    companion object {
        @JvmStatic
        fun isApkFileType(type: Int, logTag: String? = null): Boolean {
            if (MimeTypeHelper.APPLICATION_TYPE == type) {
                return true
            }
            if (logTag != null) {
                Log.e(logTag, "isApkFileType: not application type $type")
            }
            return false
        }

        @JvmStatic
        fun isArchiveFileType(type: Int, logTag: String? = null): Boolean {
            if (type == MimeTypeHelper.COMPRESSED_TYPE) {
                return true
            }
            if (logTag != null) {
                Log.e(logTag, "isArchiveFileType: not archive type $type")
            }
            return false
        }

        @JvmStatic
        fun isAudioFileType(type: Int, logTag: String? = null): Boolean {
            if (MimeTypeHelper.isAudioType(type)) {
                return true
            }
            if (logTag != null) {
                Log.e(logTag, "isAudioFileType: not audio type $type")
            }
            return false
        }

        @JvmStatic
        fun isImageFileType(type: Int, logTag: String? = null): Boolean {
            if (MimeTypeHelper.IMAGE_TYPE == type) {
                return true
            }
            if (logTag != null) {
                Log.e(logTag, "isImageFileType: not image type $type")
            }
            return false
        }

        @JvmStatic
        fun isVideoFileType(type: Int, logTag: String? = null): Boolean {
            if (MimeTypeHelper.VIDEO_TYPE == type) {
                return true
            }
            if (logTag != null) {
                Log.e(logTag, "isVideoFileType: not video type $type")
            }
            return false
        }

        @JvmStatic
        fun isDocType(type: Int, logTag: String? = null): Boolean {
            if (MimeTypeHelper.isDocType(type)) {
                return true
            }
            if (logTag != null) {
                Log.e(logTag, "isDocType: not doc type $type")
            }
            return false
        }
    }
}