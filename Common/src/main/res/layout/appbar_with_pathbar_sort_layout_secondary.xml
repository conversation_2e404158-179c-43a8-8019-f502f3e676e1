<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.COUIDividerAppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/appbar_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?attr/couiColorBackgroundWithCard"
    android:clickable="true"
    android:importantForAccessibility="no"
    app:elevation="0dp">

    <com.coui.appcompat.toolbar.COUIToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        style="@style/COUIToolBarInAppBarLayoutStyle"
        app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

    <com.filemanager.common.view.BrowserPathBar
        android:id="@+id/path_bar"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dimen_8dp"
        android:minHeight="@dimen/pathbar_height"
        android:layout_height="wrap_content"
        android:focusable="false" />

    <com.filemanager.common.sort.SortEntryView
        android:id="@+id/sort_entry_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_36dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        />

</com.google.android.material.appbar.COUIDividerAppBarLayout>