/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemotePathHelper
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.path

import com.filemanager.common.path.FilePathHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.bean.remotedevice.Constants
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import java.io.File

class RemotePathHelper(var currentPath: String) : FilePathHelper(currentPath) {

    companion object {
        const val TAG = "RemotePathHelper"
    }

    override fun isLocalFile(): Boolean = false

    override fun getExternalPath(): String? {
        return null
    }

    override fun getInternalPath(): String? {
        return null
    }

    override fun getOtgPath(): List<String>? {
        return null
    }

    override fun getDfmRootPath(): String? {
        return null
    }

    override fun getDfmDeviceName(): String? {
        return null
    }

    override fun isRootExternalPath(path: String): Boolean {
        return false
    }

    override fun isRootInternalPath(path: String): Boolean {
        return false
    }

    override fun isRootOtgPath(path: String): Boolean {
        return false
    }

    override fun isRootDfmPath(path: String): Boolean {
        return false
    }

    override fun getParentPath(path: String): String {
        var result = path
        if (isRootPath(path)) {
            Log.d(TAG, "getParentPath path $path, is RootPath return")
            return result
        }
        val index = path.lastIndexOf(File.separator)
        if (index != -1) {
            result = path.substring(0, index)
        } else {
            result = path
        }
        Log.d(TAG, "getParentPath path $path, currentPath $currentPath, result $result")
        return result
    }

    override fun getRemoteMacRootPath(): String? {
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>() ?: return null
        val currentLinkInfo = remoteDeviceApi.getCurrentLinkedRemoteDiceInfo()
        //这里linkInfo不等于空，表明存在已连接的数据
        return if (currentLinkInfo != null) {
            Constants.REMOTE_PATH_PREFIX
        } else {
            null
        }
    }

    override fun shouldDisableRootButton(): Boolean {
        return true
    }

    override fun getRemoteMacDeviceName(): String? {
        //这里后续要从RemoteDevicesManager中获取
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>() ?: return null
        return remoteDeviceApi.getCurrentLinkedRemoteDiceInfo()?.deviceName
    }
}