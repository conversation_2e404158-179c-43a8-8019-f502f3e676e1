/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.coloros.filemanager.BaseFileNameDialogTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/5/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.fileoperate.base

import android.content.Context
import android.os.IBinder
import android.text.InputFilter
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.EditText
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.edittext.COUIEditText
import com.coui.appcompat.edittext.COUIInputView
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.R
import com.filemanager.fileoperate.base.BaseFileNameDialog
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import java.util.Timer
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class BaseFileNameDialogTest {

    @Before
    fun setUp() {
        mockkStatic(ViewHelper::class)
    }

    @After
    fun tearDown() {
        unmockkStatic(ViewHelper::class)
    }

    @Test
    fun `should execute init when init views`() {
        val clickPositive = mockk<() -> Unit>()
        val btn = mockk<Button> {
            justRun { setOnClickListener(any()) }
        }
        val contextMock = mockk<Context>()
        val editText = mockk<COUIEditText> {
            justRun { addTextChangedListener(any()) }
            justRun { addOnErrorStateChangedListener(any()) }
            every { context } returns contextMock
        }
        val inputView = mockk<COUIInputView> {
            every { getEditText() } returns editText
        }
        val filter = mockk<InputFilter>()
        justRun { ViewHelper.setClassificationTextSize(contextMock, editText) }
        val dialogMock = mockk<AlertDialog> {
            every { getButton(AlertDialog.BUTTON_POSITIVE) } returns btn
            every { findViewById<COUIInputView>(com.filemanager.common.R.id.inputViewDialogFileName) } returns inputView
        }
        val baseDialog = mockk<BaseFileNameDialog> {
            every { mOppoEditText } returns editText
            justRun { addInputFilter(editText, filter) }
            justRun { showSoftInput(editText) }
            justRun { postExecuteSetWeight() }
            every { mIllegalFilter } returns filter
            every { initViews(clickPositive) } answers { callOriginal() }
            every { setMaxCount() } answers { callOriginal() }
            every { getNameLengthLimit() } answers { CommonConstants.CUSTOM_NAME_LEN }
            every { mDialog } returns dialogMock
        }
        baseDialog.initViews(clickPositive)
        verify { baseDialog.showSoftInput(editText) }
        verify { baseDialog.addInputFilter(editText, filter) }
        verify { ViewHelper.setClassificationTextSize(contextMock, editText) }
        verify { editText.addTextChangedListener(any()) }
        verify { dialogMock.getButton(AlertDialog.BUTTON_POSITIVE) }
        verify { btn.setOnClickListener(any()) }
        verify { baseDialog.postExecuteSetWeight() }
    }

    @Test
    fun `should return false when set positive button enable`() {
        val btn = mockk<Button> {
            every { isEnabled = any() } answers { callOriginal() }
            every { isEnabled } answers { callOriginal() }
        }
        val dialogMock = mockk<AlertDialog> {
            every { getButton(AlertDialog.BUTTON_POSITIVE) } returns btn
        }
        val baseDialog = mockk<BaseFileNameDialog> {
            every { setPositiveButtonEnabled(any()) } answers { callOriginal() }
            every { mDialog } returns dialogMock
        }
        baseDialog.setPositiveButtonEnabled(false)
        assertEquals(false, btn.isEnabled)
    }

    @Test
    fun `should return true when set positive button enable`() {
        val btn = mockk<Button> {
            every { isEnabled = any() } answers { callOriginal() }
            every { isEnabled } returns true
        }
        val dialogMock = mockk<AlertDialog> {
            every { getButton(AlertDialog.BUTTON_POSITIVE) } returns btn
        }
        val baseDialog = mockk<BaseFileNameDialog> {
            every { setPositiveButtonEnabled(any()) } answers { callOriginal() }
            every { mDialog } returns dialogMock
        }
        baseDialog.setPositiveButtonEnabled(true)
        assertEquals(true, btn.isEnabled)
    }

    @Test
    fun `should return true when check file name if name is point`() {
        val baseDialog = mockk<BaseFileNameDialog> {
            every { checkFileNameIllegal(any()) } answers { callOriginal() }
        }
        assertEquals(true, baseDialog.checkFileNameIllegal("."))
        assertEquals(true, baseDialog.checkFileNameIllegal(".."))
    }

    @Test
    fun `should return false when check input text if input text is null`() {
        val baseDialog = mockk<BaseFileNameDialog> {
            every { checkInputText() } answers { callOriginal() }
        }
        every { baseDialog.mReShowDialogInputText } returns null
        assertEquals(false, baseDialog.checkInputText())
    }

    @Test
    fun `should return true when check input text if input text is not null`() {
        val text = "some text"
        val editText = mockk<EditText> {
            justRun { setText(text) }
        }
        val baseDialog = mockk<BaseFileNameDialog> {
            every { mOppoEditText } returns editText
            every { checkInputText() } answers { callOriginal() }
        }
        every { baseDialog.mReShowDialogInputText = null } answers { callOriginal() }
        every { baseDialog.mReShowDialogInputText } returns text
        assertEquals(true, baseDialog.checkInputText())
        verify { editText.setText(text) }
    }

    @Test
    fun `should return true when is showing if is showing`() {
        val dialogMock = mockk<AlertDialog> {
            every { isShowing } returns true
        }
        val baseDialog = mockk<BaseFileNameDialog> {
            every { mDialog } returns dialogMock
            every { isShowing() } answers { callOriginal() }
        }
        assertEquals(true, baseDialog.isShowing())
    }

    @Test
    fun `should return false when is showing if is not showing`() {
        val dialogMock = mockk<AlertDialog> {
            every { isShowing } returns false
        }
        val baseDialog = mockk<BaseFileNameDialog> {
            every { mDialog } returns dialogMock
            every { isShowing() } answers { callOriginal() }
        }
        assertEquals(false, baseDialog.isShowing())
    }

    @Test
    fun `should execute dismiss when dismiss`() {
        val dialogMock = mockk<AlertDialog> {
            justRun { dismiss() }
        }
        val baseDialog = mockk<BaseFileNameDialog> {
            every { mDialog } returns dialogMock
            every { dismiss() } answers { callOriginal() }
        }
        baseDialog.dismiss()
        verify { dialogMock.dismiss() }
    }

    @Test
    fun `should execute remove when dismiss content is not null`() {
        mockkStatic(Log::class)
        val dialogMock = mockk<AlertDialog> {
            every { dismiss() } throws Exception()
        }
        val baseDialog = mockk<BaseFileNameDialog> {
            every { mDialog } returns dialogMock
            every { dismiss() } answers { callOriginal() }
        }
        baseDialog.dismiss()
        verify { dialogMock.dismiss() }
        verify { Log.w(any(), any()) }
        unmockkStatic(Log::class)
    }

    @Test
    fun `test add input filter`() {
        val inputFilter = mockk<InputFilter>()
        val inputFilter1 = mockk<InputFilter>()
        val inputFilter2 = mockk<InputFilter>()
        val filtersMockk = arrayOf(inputFilter, inputFilter1, inputFilter2)
        val editText = mockk<EditText> {
            every { filters } returns filtersMockk
            every { filters = any() } answers { callOriginal() }
        }
        val baseDialog = mockk<BaseFileNameDialog> {
            every { mOppoEditText } returns editText
            every { addInputFilter(editText, inputFilter) } answers { callOriginal() }
        }
        baseDialog.addInputFilter(editText, inputFilter)
        assertEquals(inputFilter, editText.filters[0])
    }

    @Test
    fun `test hide soft input`() {
        val winToken = mockk<IBinder>()
        val imm = mockk<InputMethodManager> {
            every { hideSoftInputFromWindow(winToken, 0) } returns true
        }
        val editText = mockk<EditText> {
            every { windowToken } returns winToken
            every { context } returns mockk {
                every { getSystemService(Context.INPUT_METHOD_SERVICE) } returns imm
            }
        }
        val baseDialog = mockk<BaseFileNameDialog> {
            every { hideSoftInput(editText) } answers { callOriginal() }
        }
        baseDialog.hideSoftInput(editText)
        verify { imm.hideSoftInputFromWindow(winToken, 0) }
    }

    @Test
    fun `test show soft input`() {
        val imm = mockk<InputMethodManager> {
            every { showSoftInput(any(), 0) } returns true
        }
        val editText = mockk<EditText> {
            justRun { isFocusable = true }
            justRun { isFocusableInTouchMode = true }
            every { requestFocus() } returns true
            every { context } returns mockk {
                every { getSystemService(Context.INPUT_METHOD_SERVICE) } returns imm
            }
        }

        val dialog = mockk<AlertDialog> {
            every { window } answers { callOriginal() }
        }

        val baseDialog = mockk<BaseFileNameDialog> {
            every { mOppoEditText } returns editText
            every { showSoftInput(editText) } answers { callOriginal() }
            every { mDialog } returns dialog
        }
        baseDialog.showSoftInput(editText)
        verify {
            editText.isFocusable = true
            editText.isFocusableInTouchMode = true
            editText.requestFocus()
        }
    }
}