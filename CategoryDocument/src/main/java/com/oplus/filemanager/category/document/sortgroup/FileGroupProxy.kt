/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/14
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.document.sortgroup

import com.filemanager.common.sort.SortHelper

class FileGroupProxy(sort: Int) : IFileGroup by when (sort) {

    SortHelper.FILE_NAME_ORDER -> FilePinyinLetterGroup()

    SortHelper.FILE_TYPE_ORDER -> FileTypeGroup()

    SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER -> FileSizeGroup()

    SortHelper.FILE_TIME_REVERSE_ORDER -> FileModifyTimeGroup()

    SortHelper.FILE_LAST_OPEN_TIME_ORDER -> FileOpenTimeGroup()

    else -> FilePinyinLetterGroup()
}