/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForFileService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.filemanager.fileoperate.di

import com.filemanager.fileoperate.FileOperateApi
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi
import org.koin.dsl.module

object AutoDIForFileOperate {

    val fileOperateModule = module {
        single<IFileOperateApi>(createdAtStart = true) {
            FileOperateApi
        }
    }
}