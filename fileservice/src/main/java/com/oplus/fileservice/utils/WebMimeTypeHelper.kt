/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WebMimeTypeHelper
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/6 12:01
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/6       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.utils

import android.webkit.MimeTypeMap
import androidx.annotation.VisibleForTesting
import com.filemanager.common.compat.FeatureCompat
import java.util.Locale

object WebMimeTypeHelper {
    @VisibleForTesting
    const val DOC_TYPE = "doc"
    @VisibleForTesting
    const val EXCL_TYPE = "excl"
    @VisibleForTesting
    const val PDF_TYPE = "pdf"
    @VisibleForTesting
    const val LRC_TYPE = "lrc"
    @VisibleForTesting
    const val PPT_TYPE = "ppt"
    @VisibleForTesting
    const val TXT_TYPE = "txt"
    @VisibleForTesting
    const val IMAGE_TYPE = "image"
    @VisibleForTesting
    const val MUSIC_TYPE = "music"
    @VisibleForTesting
    const val VIDEO_TYPE = "video"
    @VisibleForTesting
    const val APK_TYPE = "apk"
    @VisibleForTesting
    const val RAR_TYP = "rar"
    @VisibleForTesting
    const val Z7_TYPE = "7z"
    @VisibleForTesting
    const val JAR_TYPE = "jar"
    @VisibleForTesting
    const val ZIP_TYPE = "zip"
    @VisibleForTesting
    const val HTML_TYPE = "html"
    const val OTHER_TYPE = "other"
    const val DIRECTORY_TYPE = "files"
    const val ALBUM_SET = "album"

    private var mAdditionalMimeTypeMap = mutableMapOf<String, String>()
    private var mExtFileTypeMap = mutableMapOf<String, String>()

    init {
        mAdditionalMimeTypeMap["7z"] = "application/x-7z-compressed"
        mAdditionalMimeTypeMap["ogg"] = "audio/ogg"
        mAdditionalMimeTypeMap["jar"] = "application/java-archive"
        mAdditionalMimeTypeMap["ape"] = "audio/ape"
        mAdditionalMimeTypeMap["lrc"] = "application/lrc"
        mAdditionalMimeTypeMap["ebk2"] = "text/plain"
        mAdditionalMimeTypeMap["ebk3"] = "text/plain"
        mAdditionalMimeTypeMap["dm"] = "application/x-android-drm-fl"
        mAdditionalMimeTypeMap["dcf"] = "application/x-android-drm-fl"
        mAdditionalMimeTypeMap["rm"] = "*/*"
        mExtFileTypeMap["doc"] = DOC_TYPE
        mExtFileTypeMap["html"] = HTML_TYPE
        mExtFileTypeMap["htm"] = HTML_TYPE
        mExtFileTypeMap["epub"] = OTHER_TYPE
        mExtFileTypeMap["lrc"] = LRC_TYPE
        mExtFileTypeMap["dat"] = OTHER_TYPE
        mExtFileTypeMap["csv"] = OTHER_TYPE
        mExtFileTypeMap["vcf"] = OTHER_TYPE
        mExtFileTypeMap["ics"] = OTHER_TYPE
        mExtFileTypeMap["vcs"] = OTHER_TYPE
        mExtFileTypeMap["apk"] = APK_TYPE
        mExtFileTypeMap["theme"] = OTHER_TYPE
        mExtFileTypeMap["db"] = OTHER_TYPE
        mExtFileTypeMap["docx"] = DOC_TYPE
        mExtFileTypeMap["xls"] = EXCL_TYPE
        mExtFileTypeMap["xlsx"] = EXCL_TYPE
        mExtFileTypeMap["ppt"] = PPT_TYPE
        mExtFileTypeMap["pptx"] = PPT_TYPE
        mExtFileTypeMap["pdf"] = PDF_TYPE
        mExtFileTypeMap["txt"] = TXT_TYPE
        mExtFileTypeMap["wav"] = MUSIC_TYPE
        mExtFileTypeMap["amr"] = MUSIC_TYPE
        mExtFileTypeMap["vmsg"] = OTHER_TYPE
        mExtFileTypeMap["torrent"] = OTHER_TYPE
        mExtFileTypeMap["chm"] = OTHER_TYPE
        mExtFileTypeMap["p12"] = OTHER_TYPE
        mExtFileTypeMap["cer"] = OTHER_TYPE
        mExtFileTypeMap["ebk2"] = OTHER_TYPE
        mExtFileTypeMap["ebk3"] = OTHER_TYPE
        mExtFileTypeMap["ico"] = IMAGE_TYPE
        if (FeatureCompat.sIsSupportDrm) {
            mExtFileTypeMap["fl"] = OTHER_TYPE
            mExtFileTypeMap["dm"] = OTHER_TYPE
            mExtFileTypeMap["dcf"] = OTHER_TYPE
        }
        mExtFileTypeMap["rar"] = RAR_TYP
        mExtFileTypeMap["zip"] = ZIP_TYPE
        mExtFileTypeMap["ozip"] = ZIP_TYPE
        mExtFileTypeMap["jar"] = JAR_TYPE
        mExtFileTypeMap["7z"] = Z7_TYPE
    }

    @JvmStatic
    fun getFileTypeFromExtension(extension: String?): String {
        if (extension.isNullOrEmpty()) {
            return OTHER_TYPE
        }
        val ext = extension.lowercase(Locale.US)
        if (mExtFileTypeMap.containsKey(ext)) {
            return mExtFileTypeMap[ext]!!
        } else {
            val mimeType = getMimeTypeFromExtension(ext)
            if (mimeType != null) {
                return getFileTypeByMimeType(mimeType)
            }
        }
        return OTHER_TYPE
    }

    @JvmStatic
    @VisibleForTesting
    fun getMimeTypeFromExtension(extension: String?): String? {
        if (extension.isNullOrEmpty()) {
            return "*/*"
        }
        if (mAdditionalMimeTypeMap.containsKey(extension)) {
            return mAdditionalMimeTypeMap[extension]
        }
        val mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(
            extension.lowercase(Locale.US)
        )
        return if (!mimeType.isNullOrEmpty()) {
            mimeType
        } else {
            "*/*"
        }
    }

    @JvmStatic
    @VisibleForTesting
    fun getFileTypeByMimeType(mimeType: String): String {
        return when {
            mimeType.startsWith("image/") -> IMAGE_TYPE
            mimeType.startsWith("video/") -> VIDEO_TYPE
            mimeType.startsWith("audio/") -> MUSIC_TYPE
            else -> OTHER_TYPE
        }
    }
}