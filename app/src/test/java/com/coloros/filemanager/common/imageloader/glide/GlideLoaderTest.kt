/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GlideLoaderTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/4/28
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/4/28      1.0        create
 ***********************************************************************/
package com.coloros.filemanager.common.imageloader.glide

import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.filemanager.common.imageloader.glide.GlideLoader
import com.filemanager.common.imageloader.glide.RoundedCornersBitmapTransformation
import com.filemanager.common.utils.Utils
import org.junit.Assert
import org.junit.Test

class GlideLoaderTest {

    @Test
    fun request_options_for_round_conner_left_top_only_test() {
        var options1 = RequestOptions()
        var options2 = RequestOptions()
        val cornerRadius = 10
        options1 = GlideLoader.requestOptionsForRoundConner(
            options1,
            GlideLoader.ROUND_CONNER_LEFT_TOP_ONLY,
            cornerRadius
        )
        options2 = options2.transform(
            RoundedCornersBitmapTransformation(
                cornerRadius.toFloat(),
                Utils.isRtl(),
                true,
                false,
                false,
                false
            )
        )
        Assert.assertEquals(options1, options2)
    }

    @Test
    fun request_options_for_round_conner_right_top_only_test() {
        var options1 = RequestOptions()
        var options2 = RequestOptions()
        val cornerRadius = 10
        options1 = GlideLoader.requestOptionsForRoundConner(
            options1,
            GlideLoader.ROUND_CONNER_RIGHT_TOP_ONLY,
            cornerRadius
        )
        options2 = options2.transform(
            RoundedCornersBitmapTransformation(
                cornerRadius.toFloat(),
                Utils.isRtl(),
                false,
                true,
                false,
                false
            )
        )
        Assert.assertEquals(options1, options2)
    }

    @Test
    fun request_options_for_round_conner_left_bottom_only_test() {
        var options1 = RequestOptions()
        var options2 = RequestOptions()
        val cornerRadius = 10
        options1 = GlideLoader.requestOptionsForRoundConner(
            options1,
            GlideLoader.ROUND_CONNER_LEFT_BOTTOM_ONLY,
            cornerRadius
        )
        options2 = options2.transform(
            RoundedCornersBitmapTransformation(
                cornerRadius.toFloat(),
                Utils.isRtl(),
                false,
                false,
                true,
                false
            )
        )
        Assert.assertEquals(options1, options2)
    }

    @Test
    fun request_options_for_round_conner_right_bottom_only_test() {
        var options1 = RequestOptions()
        var options2 = RequestOptions()
        val cornerRadius = 10
        options1 = GlideLoader.requestOptionsForRoundConner(
            options1,
            GlideLoader.ROUND_CONNER_RIGHT_BOTTOM_ONLY,
            cornerRadius
        )
        options2 = options2.transform(
            RoundedCornersBitmapTransformation(
                cornerRadius.toFloat(),
                Utils.isRtl(),
                false,
                false,
                false,
                true
            )
        )
        Assert.assertEquals(options1, options2)
    }

    @Test
    fun request_options_for_round_conner_left_top_bottom_only_test() {
        var options1 = RequestOptions()
        var options2 = RequestOptions()
        val cornerRadius = 10
        options1 = GlideLoader.requestOptionsForRoundConner(
            options1,
            GlideLoader.ROUND_CONNER_LEFT_TOP_BOTTOM_ONLY,
            cornerRadius
        )
        options2 = options2.transform(
            RoundedCornersBitmapTransformation(
                cornerRadius.toFloat(),
                Utils.isRtl(),
                true,
                false,
                true,
                false
            )
        )
        Assert.assertEquals(options1, options2)
    }

    @Test
    fun request_options_for_round_conner_right_top_bottom_only_test() {
        var options1 = RequestOptions()
        var options2 = RequestOptions()
        val cornerRadius = 10
        options1 = GlideLoader.requestOptionsForRoundConner(
            options1,
            GlideLoader.ROUND_CONNER_RIGHT_TOP_BOTTOM_ONLY,
            cornerRadius
        )
        options2 = options2.transform(
            RoundedCornersBitmapTransformation(
                cornerRadius.toFloat(),
                Utils.isRtl(),
                false,
                true,
                false,
                true
            )
        )
        Assert.assertEquals(options1, options2)
    }

    @Test
    fun request_options_for_round_conner_none_test() {
        var options1 = RequestOptions()
        var options2 = RequestOptions()
        val cornerRadius = 10
        options1 = GlideLoader.requestOptionsForRoundConner(
            options1,
            GlideLoader.ROUND_CONNER_NONE,
            cornerRadius
        )
        options2 = options2.transform(CenterCrop())
        Assert.assertEquals(options1, options2)
    }

    @Test
    fun request_options_for_round_conner_all_test() {
        var options1 = RequestOptions()
        var options2 = RequestOptions()
        val cornerRadius = 10
        options1 = GlideLoader.requestOptionsForRoundConner(
            options1,
            GlideLoader.ROUND_CONNER_ALL,
            cornerRadius
        )
        options2 = options2.transform(
            RoundedCornersBitmapTransformation(
                cornerRadius.toFloat(),
                Utils.isRtl(),
                true,
                true,
                true,
                true
            )
        )
        Assert.assertEquals(options1, options2)
    }
}