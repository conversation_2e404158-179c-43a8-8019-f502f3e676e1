/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : InitHelper
 ** Description : 初始化类，从MainApplication中的初始化逻辑抽取而来
 ** Version     : 1.0
 ** Date        : 2024/06/22 15:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/21       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.appswitch.util

import android.content.ContentResolver
import android.provider.Settings
import androidx.annotation.Keep
import com.coloros.filemanager.appswitch.AppSwitchManager
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.google.gson.GsonBuilder
import java.text.SimpleDateFormat
import java.util.Date
import kotlin.math.abs

object MaxCntChecker {

    const val TAG = "MaxCntChecker"

    const val DAY_TIME = 24 * 60 * 60 * 1000L
    const val IGNORE_SETTINGS_NAME = "IgnoreMaxCnt"
    const val DEBUG_SETTINGS_NAME = "debug"
    const val DEBUG_SETTINGS_VALUE_TRUE = 1
    const val DEBUG_SETTINGS_VALUE_FALSE = 0

    //每次拉起插入/更新dmp的最大次数，这里默认设置20次
    const val DEFAULT_MAX_DAY_CNT = 20

    const val SP_NAME = "max_count_entity"


    /**
     * 检查是否超过当天的采集数量
     */
    fun checkBelowMaxCnt(): Boolean {
        val timeStamp = System.currentTimeMillis()
        val settingIngore = isSettingIgnore()
        if (settingIngore) {
            Log.i(TAG, "checkBeloweMaxCnt settingIngore return true")
            return true
        }
        val maxCntLimit = getMaxCntLimit()
        val maxCntEntityFromSp = getMaxCntEntityFromSp()
        if (maxCntEntityFromSp == null) {
            Log.i(TAG, "checkBeloweMaxCnt sp empty return true")
            return true
        }
        val deltaTime = timeStamp - maxCntEntityFromSp.timeStamp
        val currenTimeStampString = getTimeStampString(timeStamp)
        val preTimeStampString = getTimeStampString(maxCntEntityFromSp.timeStamp)
        if (deltaTime > DAY_TIME) {
            Log.i(TAG, "checkBeloweMaxCnt time exeed 1 day deltaTime $deltaTime, currentTime $timeStamp, $currenTimeStampString" +
                        ", preTime ${maxCntEntityFromSp.timeStamp}, $preTimeStampString return true")
            //超过一天则更新时间戳，并将当前采集次数清零
            saveMaxCntEntityToSp(MaxCntEntity(timeStamp, 0))
            return true
        } else {
            val overLimit = maxCntEntityFromSp.currentCnt >= maxCntLimit
            Log.i(TAG, "checkBeloweMaxCnt limit $maxCntLimit, currentCnt ${maxCntEntityFromSp.currentCnt}")
            return !overLimit
        }
    }

    private fun getMaxCntLimit(): Int {
        val maxCntLimitFromConfig =
            AppSwitchManager.currentThirdAppListenConfigParam?.maxDayExactCnt
        val result = if (maxCntLimitFromConfig == null) {
            //云端没有配置，则使用默认的，每天20次
            DEFAULT_MAX_DAY_CNT
        } else {
            if (maxCntLimitFromConfig == -1) {
                //云端配置了-1, 则设置为最大，无限制
                Integer.MAX_VALUE
            } else {
                //云端配置了除了-1的其他苏，设置为
                abs(maxCntLimitFromConfig)
            }
        }
        Log.i(TAG, "getMaxCntLimit maxCntLimitFromConfig $maxCntLimitFromConfig  result $result")
        return result
    }


    /**
     * 当前采集数据+1
     */
    fun increaseAndRefershMaxCnt() {
        val debugMode = isSettingIgnore()
        if (debugMode) {
            Log.i(TAG, "increateAndRefershMaxCnt settingIngore return")
            return
        }
        val maxCntEntityFromSp = getMaxCntEntityFromSp()
        if (maxCntEntityFromSp == null) {
            Log.i(TAG, "increateAndRefershMaxCnt sp empty return")
            //当前sp不存在时，使用当前时间搓+
            saveMaxCntEntityToSp(MaxCntEntity(System.currentTimeMillis(), 0))
            return
        }
        val increasedCnt = maxCntEntityFromSp.currentCnt + 1
        val newMaxCntEntity = MaxCntEntity(maxCntEntityFromSp.timeStamp, increasedCnt)
        Log.i(TAG, "increateAndRefershMaxCnt newMaxCntEntity $newMaxCntEntity")
        saveMaxCntEntityToSp(newMaxCntEntity)
    }


    private fun getMaxCntEntityFromSp(): MaxCntEntity? {
        val spString = PreferencesUtils.getString(key = SP_NAME)
        if (spString.isNullOrEmpty()) {
            return null
        } else {
            val result = MaxCntEntity.parseFromJsonString(spString)
            Log.i(TAG, "getMaxCntEntityFromSp $result")
            return result
        }
    }

    private fun saveMaxCntEntityToSp(maxCntEntity: MaxCntEntity?) {
        if (maxCntEntity == null) {
            Log.e(TAG, "saveMaxCntEntityToSp input null, not save to sp")
            return
        } else {
            val spString = maxCntEntity.toJsonString()
            if (spString.isNullOrEmpty()) {
                Log.e(TAG, "saveMaxCntEntityToSp toJsonString empty, not save to sp")
                return
            } else {
                PreferencesUtils.put(key = SP_NAME, value = spString)
                Log.e(TAG, "saveMaxCntEntityToSp maxCntEntity $maxCntEntity, spString $spString")
            }
        }
    }

    private fun getTimeStampString(timeStamp: Long): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        val timeString = dateFormat.format(Date(timeStamp))
        return timeString
    }


    /**
     * 判断是否是调试模式，如果是调试模式，则不受每日最大采集次数影响，每次进入预览页面都可以采集并写入dmp
     * 设置setting值的操作步骤：
     * 1.手机进入USB调试模式，打开禁止权限监控的开关
     * 2. 输入命令（进入shell命令）：
     *      adb shell
     * 3. 输入命令（设置setting值）：
     *      settings put secure IgnoreMaxCnt 1
     * 4. 输入命令（查看上一步setting值设置是否成功）
     *      settings get secure IgnoreMaxCnt
     */
    fun isSettingIgnore(): Boolean {
        val context = MyApplication.appContext
        val resolver = context.contentResolver
        val settingValue = getIntFromSetting(resolver, IGNORE_SETTINGS_NAME)
        return settingValue == DEBUG_SETTINGS_VALUE_TRUE
    }


    fun isSettingDebug(): Boolean {
        val context = MyApplication.appContext
        val resolver = context.contentResolver
        val settingValue = getIntFromSetting(resolver, DEBUG_SETTINGS_NAME)
        return settingValue == DEBUG_SETTINGS_VALUE_TRUE
    }


    private fun getIntFromSetting(resolver: ContentResolver, name: String): Int? {
        val settingIntValue = runCatching {
            Settings.Secure.getInt(resolver, name)
        }.getOrNull()
        Log.i(TAG, "getIntFromSetting name $name value $settingIntValue")
        return settingIntValue
    }
}

@Keep
data class MaxCntEntity(val timeStamp: Long, val currentCnt: Int) {
    companion object {
        const val TAG = "MaxCntEntity"
        fun parseFromJsonString(jsonString: String?): MaxCntEntity? {
            val result = runCatching {
                val gb = GsonBuilder().setPrettyPrinting().serializeNulls().create()
                gb.fromJson(jsonString, MaxCntEntity::class.java)
            }.onFailure {
                Log.e(TAG, "parseFromJsonString error, $jsonString", it)
            }.getOrNull()
            return result
        }
    }

    fun toJsonString(): String? {
        val result = kotlin.runCatching {
            val gb = GsonBuilder().setPrettyPrinting()
                .serializeNulls().create()
            gb.toJson(this)
        }.onFailure {
            Log.e(TAG, "toJsonString error, $this", it)
        }.getOrNull()
        return result
    }

    override fun toString(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        val timeString = dateFormat.format(Date(timeStamp))
        return "MaxCntEntity(timeStamp=$timeStamp, currentCnt=$currentCnt, timeFormateString=$timeString)"
    }
}