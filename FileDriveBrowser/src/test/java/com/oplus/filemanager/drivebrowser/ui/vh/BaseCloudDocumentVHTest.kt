package com.oplus.filemanager.drivebrowser.ui.vh

import android.content.Context
import android.view.View
import android.widget.TextView
import androidx.core.view.setPadding
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.imageloader.ImageLoaderFactory
import com.filemanager.common.imageloader.ImageLoaderInterface
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.FileThumbView
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * BaseCloudDocumentVH的单元测试类
 * 用于测试BaseCloudDocumentVH基类的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class BaseCloudDocumentVHTest {

    // 测试所需的成员变量
    private lateinit var context: Context
    private lateinit var mockView: View
    private lateinit var testViewHolder: TestBaseCloudDocumentVH
    private lateinit var mockFile: BaseFileBean
    private lateinit var mockAdapter: BaseSelectionRecycleAdapter<*, *>
    private lateinit var mockThumbView: FileThumbView
    private lateinit var mockTextView: TextView
    private lateinit var mockJumpImg: View

    /**
     * 测试前的初始化方法
     * 创建所有mock对象和测试环境
     */
    @Before
    fun setUp() {
        // 获取Robolectric提供的测试上下文
        context = RuntimeEnvironment.getApplication()
        // 先初始化MyApplication mock
        mockkObject(MyApplication)
        every { MyApplication.sAppContext } returns context
        
        // 创建其他mock对象
        mockView = mockk(relaxed = true)
        testViewHolder = TestBaseCloudDocumentVH(mockView)
        mockFile = mockk(relaxed = true)
        mockAdapter = mockk(relaxed = true)
        mockThumbView = mockk(relaxed = true)
        mockTextView = mockk(relaxed = true)
        mockJumpImg = mockk(relaxed = true)
        
        // 模拟静态方法和单例对象
        mockkStatic(Utils::class)
        mockkObject(FileImageLoader)
        
        // 模拟图片加载相关调用
        every { FileImageLoader.sInstance.clear(any(), any()) } returns Unit
        every { FileImageLoader.sInstance.displayDefault(any(), any(), any(), any(), any(), any(), any(), any(), any(), any()) } returns Unit
        
        // 模拟ImageLoaderFactory防止真实Glide调用
        mockkObject(ImageLoaderFactory)
        val mockImageLoader = mockk<ImageLoaderInterface>(relaxed = true)
        every { ImageLoaderFactory.buildImageLoader() } returns mockImageLoader
    }

    /**
     * 测试后的清理方法
     * 释放所有mock对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试getCloudFileIconRes方法
     * 验证已知MIME类型能返回正确的图标资源
     */
    @Test
    fun `getCloudFileIconRes should return correct resource for known mime types`() {
        // 测试已知MIME类型
        assertEquals(
            com.oplus.filemanager.drivebrowser.R.drawable.ic_kdocs_otl,
            BaseCloudDocumentVH.getCloudFileIconRes(MimeTypeHelper.KDOCS_OTL_TYPE)
        )
        assertEquals(
            com.oplus.filemanager.drivebrowser.R.drawable.ic_tencent_mind,
            BaseCloudDocumentVH.getCloudFileIconRes(MimeTypeHelper.TENCENT_MIND_TYPE)
        )
    }

    /**
     * 测试getCloudFileIconRes方法
     * 验证未知MIME类型返回0
     */
    @Test
    fun `getCloudFileIconRes should return 0 for unknown mime type`() {
        // 测试未知MIME类型
        assertEquals(0, BaseCloudDocumentVH.getCloudFileIconRes(999))
    }

    /**
     * 测试showFileIcon方法
     * 验证图片和视频类型会设置padding
     */
    @Test
    fun `showFileIcon should set padding for image and video types`() {
        // 测试图片类型
        testViewHolder.callShowFileIcon(
            context, 
            mockThumbView, 
            MimeTypeHelper.IMAGE_TYPE, 
            mockFile, 
            10
        )
        verify { mockThumbView.setPadding(any(), any(), any(), any()) }

        // 测试视频类型
        testViewHolder.callShowFileIcon(
            context, 
            mockThumbView, 
            MimeTypeHelper.VIDEO_TYPE, 
            mockFile, 
            10
        )
        verify(exactly = 2) { mockThumbView.setPadding(any(), any(), any(), any()) }
    }

    /**
     * 测试showFileIcon方法
     * 验证其他类型不会设置padding
     */
    @Test
    fun `showFileIcon should not set padding for other types`() {
        // 测试其他类型
        testViewHolder.callShowFileIcon(
            context, 
            mockThumbView, 
            MimeTypeHelper.KDOCS_OTL_TYPE, 
            mockFile, 
            10
        )
        verify { mockThumbView.setPadding(0, 0, 0, 0) }
    }

    /**
     * 测试showDetail方法
     * 验证日期格式化显示功能
     */
    @Test
    fun `showDetail should set formatted date on TextView`() {
        // 测试日期格式化显示
        val testDate = 1672531200000L // 2023-01-01
        every { Utils.getDateFormat(any(), any()) } returns "2023-01-01"
        testViewHolder.callShowDetail(context, mockTextView, testDate)
        verify { mockTextView.text = "2023-01-01" }
    }

    /**
     * 测试showJumpImg方法
     * 验证根据参数控制视图可见性的逻辑
     */
    @Test
    fun `showJumpImg should handle visibility based on parameters`() {
        // 测试文件夹+非选择模式
        testViewHolder.callShowJumpImg(mockJumpImg, true, false, false)
        verify { mockJumpImg.visibility = View.VISIBLE }

        // 测试非文件夹+非选择模式+动画未运行
        testViewHolder.callShowJumpImg(mockJumpImg, false, false, false)
        verify { mockJumpImg.visibility = View.INVISIBLE }

        // 测试选择模式+动画未运行
        testViewHolder.callShowJumpImg(mockJumpImg, true, true, false)
        verify { mockJumpImg.visibility = View.INVISIBLE }

        // 测试选择模式+动画运行
        testViewHolder.callShowJumpImg(mockJumpImg, true, true, true)
        verify(atLeast = 1) { mockJumpImg.visibility = View.VISIBLE }
    }

    /**
     * 测试loadData方法
     * 验证数据加载和视图更新功能
     */
    @Test
    fun `loadData should update key and call updateViewHolder`() {
        // 测试数据加载方法
        val testKey = 123
        val selectionArray = mutableListOf<Int>()
        
        testViewHolder.loadData(
            context, 
            testKey, 
            mockFile, 
            false, 
            selectionArray, 
            mockAdapter
        )
        
        assertEquals(testKey, testViewHolder.getKey())
        assertTrue(testViewHolder.updateViewHolderCalled)
    }

    /**
     * 用于测试的ViewHolder实现类
     * 继承BaseCloudDocumentVH并暴露需要测试的protected方法
     */
    private inner class TestBaseCloudDocumentVH(convertView: View) : 
        BaseCloudDocumentVH(convertView) {
        
        // 标记updateViewHolder是否被调用
        var updateViewHolderCalled = false
        private var currentKey: Int? = null
        
        /**
         * 重写updateViewHolder方法用于测试
         */
        override fun updateViewHolder(
            context: Context,
            key: Int?,
            data: BaseFileBean,
            choiceMode: Boolean,
            selectionArray: MutableList<Int>,
            adapter: BaseSelectionRecycleAdapter<*, *>
        ) {
            updateViewHolderCalled = true
        }
        
        /**
         * 获取当前key值
         */
        fun getKey(): Int? = currentKey
        
        /**
         * 重写updateKey方法用于测试
         */
        override fun updateKey(key: Int?) {
            currentKey = key
        }
        
        /**
         * 暴露showFileIcon方法用于测试
         */
        fun callShowFileIcon(
            context: Context, 
            imageView: FileThumbView, 
            type: Int, 
            data: BaseFileBean, 
            radius: Int
        ) {
            super.showFileIcon(context, imageView, type, data, radius)
        }
        
        /**
         * 暴露showDetail方法用于测试
         */
        fun callShowDetail(
            context: Context, 
            detailTv: TextView, 
            date: Long
        ) {
            super.showDetail(context, detailTv, date)
        }
        
        /**
         * 暴露showJumpImg方法用于测试
         */
        fun callShowJumpImg(
            jumpImg: View, 
            isDir: Boolean, 
            choiceMode: Boolean, 
            isAnimRunning: Boolean
        ) {
            super.showJumpImg(jumpImg, isDir, choiceMode, isAnimRunning)
        }
    }
}