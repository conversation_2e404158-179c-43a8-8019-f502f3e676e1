/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - HeapUpImageLoader.kt
 * Description:
 *     The preview image loader for heap up files
 *
 * Version: 1.0
 * Date: 2024-10-19
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-10-19   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection.loader

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.net.Uri
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileImageLoader.Companion.VIDEO_FRAME_VALUE
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Log
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.audio.AudioThumbnailNew
import com.filemanager.thumbnail.audio.AudioThumbnailResult
import com.filemanager.thumbnail.audio.AudioThumbnailTransformation
import com.filemanager.thumbnail.doc.DocThumbnail
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.interfaze.remotedevice.IRemotePicListener
import com.oplus.filemanager.interfaze.remotedevice.IRemoteThumbnail
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.utils.ArchiveIconHelper
import com.oplus.filemanager.preview.utils.BorderedRoundedCornersTrans
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import com.oplus.filemanager.preview.utils.getFileUri
import com.oplus.filemanager.preview.utils.glideSignatureKey
import com.oplus.filemanager.preview.utils.isBoardDoc
import com.oplus.filemanager.preview.utils.logInfo
import com.oplus.filemanager.remotedevice.provider.ThumbnailProvider
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException
import java.util.UUID

internal class HeapUpImageLoader(private val context: Context) {

    private companion object {
        private const val TAG = "HeapUpImageLoader"
        private const val SUCCESS = 1000
    }

    private val typeChecker = FilePreviewTypeCheckHelper(context)
    private val docPaperWidth: Int =
        context.resources.getDimensionPixelSize(R.dimen.selection_doc_paper_max_width)
    private val docPaperHeight =
        context.resources.getDimensionPixelSize(R.dimen.selection_doc_paper_max_height)
    private val docBoardWidth: Int =
        context.resources.getDimensionPixelSize(R.dimen.selection_doc_board_max_width)
    private val docBoardHeight =
        context.resources.getDimensionPixelSize(R.dimen.selection_doc_board_max_height)
    private val remoteWidth: Int =
        context.resources.getDimensionPixelSize(R.dimen.preview_remote_max_width)
    private val remoteHeight =
        context.resources.getDimensionPixelSize(R.dimen.preview_remote_max_height)

    fun loadHeapUpImage(fileBean: BaseFileBean): AbsHeapUpImageTarget<*> = typeChecker.run {
        Log.d(TAG, "loadHeapUpImage: ${fileBean.logInfo()}")
        return when {
            isRemoteType(fileBean) -> loadAsRemoteFile(fileBean)
            isImageFileType(fileBean) -> loadAsImageFile(fileBean, false)
            isVideoFileType(fileBean) -> loadAsImageFile(fileBean, true)
            isAudioFileType(fileBean) -> loadAsAudioFile(fileBean)
            isSupportedDocType(fileBean) -> loadAsDocFile(fileBean)
            isApkFileType(fileBean) -> loadAsApkFile(fileBean)
            else -> loadAsNormalFile(fileBean)
        }
    }

    private fun BaseFileBean.getPlaceholderRes(isUnknown: Boolean = false): Int {
        if (isUnknown) {
            return KtThumbnailHelper.getClassifyResId(MimeTypeHelper.UNKNOWN_TYPE)
        }
        val type = typeChecker.getMediaType(this@getPlaceholderRes)
        if (type == MimeTypeHelper.COMPRESSED_TYPE) {
            return ArchiveIconHelper.getArchiveIconId(this@getPlaceholderRes)
        }
        return KtThumbnailHelper.getClassifyResId(type)
    }

    private fun FilePreviewTypeCheckHelper.isSupportedDocType(fileBean: BaseFileBean): Boolean {
        if (!ThumbnailManager.isDocThumbnailSupported(context)) {
            return false
        }
        val type = getMediaType(fileBean)
        return MimeTypeHelper.isDocType(type)
    }

    private fun prepareOptions(fileBean: BaseFileBean): RequestOptions {
        val radius = context.resources.getDimensionPixelOffset(R.dimen.preview_image_round_radius)
        val roundCorners = RoundedCorners(radius)
        var options = RequestOptions()
        options = options.diskCacheStrategy(DiskCacheStrategy.NONE)
            .skipMemoryCache(false)
            .signature(fileBean.glideSignatureKey())
        options = options.transform(roundCorners)
        options.frame(VIDEO_FRAME_VALUE)
        return options
    }

    private fun loadAsNormalFile(fileBean: BaseFileBean): AbsHeapUpImageTarget<*> {
        val target = CommonHeapUpTarget(
            context = context,
            fileBean = fileBean,
            defaultPlaceholderRes = fileBean.getPlaceholderRes(),
            isAlwaysIcon = true
        )
        target.loadWithPlaceholder()
        return target
    }

    private fun loadAsImageFile(
        fileBean: BaseFileBean,
        isVideoType: Boolean,
        options: RequestOptions = prepareOptions(fileBean)
    ): AbsHeapUpImageTarget<*> {
        val target = CommonHeapUpTarget(
            context = context,
            fileBean = fileBean,
            defaultPlaceholderRes = fileBean.getPlaceholderRes(),
            isVideoType = isVideoType
        )
        val imageUri = fileBean.getFileUri() ?: run {
            Log.e(TAG, "loadAsImageFile: ERROR! can not get image uri")
            target.loadWithPlaceholder()
            return target
        }
        Glide.with(context)
            .asBitmap()
            .load(imageUri)
            .apply(options)
            .addListener(LoadResultLogListener("loadAsImageFile", fileBean))
            .into(target)
        return target
    }

    private fun prepareAudioOptions(fileBean: BaseFileBean): RequestOptions {
        val roundCorners = BorderedRoundedCornersTrans(context)
        var options = RequestOptions()
        options = options.diskCacheStrategy(DiskCacheStrategy.NONE)
            .skipMemoryCache(false)
            .signature(fileBean.glideSignatureKey())
        options = options.transform(roundCorners).transform(
            AudioThumbnailResult::class.java,
            AudioThumbnailTransformation(roundCorners)
        )
        return options
    }

    private fun loadAsAudioFile(
        fileBean: BaseFileBean,
        options: RequestOptions = prepareAudioOptions(fileBean)
    ): AbsHeapUpImageTarget<*> {
        val target = AudioHeapUpTarget(context, fileBean, fileBean.getPlaceholderRes())
        val filePath = fileBean.mData.takeUnless { it.isNullOrEmpty() } ?: run {
            Log.e(TAG, "loadAsAudioFile: ERROR! No audio file path")
            target.loadWithPlaceholder()
            return target
        }
        val thumbnailData = AudioThumbnailNew(filePath, fileBean.mDateModified, fileBean.mSize)
        Glide.with(context)
            .`as`(AudioThumbnailResult::class.java)
            .load(thumbnailData)
            .apply(options)
            .override(-1, -1)
            .addListener(LoadResultLogListener("loadAsAudioFile", fileBean))
            .into(target)
        return target
    }

    private fun loadAsDocFile(
        fileBean: BaseFileBean,
        options: RequestOptions = prepareOptions(fileBean)
    ): AbsHeapUpImageTarget<*> {
        val target = CommonHeapUpTarget(
            context = context,
            fileBean = fileBean,
            defaultPlaceholderRes = fileBean.getPlaceholderRes()
        )
        val filePath = fileBean.mData.takeUnless { it.isNullOrEmpty() } ?: run {
            Log.e(TAG, "loadAsDocFile: ERROR! No audio file path")
            target.loadWithPlaceholder()
            return target
        }
        val useBoard = fileBean.isBoardDoc()
        val width = if (useBoard) docBoardWidth else docPaperWidth
        val height = if (useBoard) docBoardHeight else docPaperHeight
        val thumbnailData = DocThumbnail(filePath, fileBean.mDateModified, fileBean.mSize)
        thumbnailData.extraSerializeKey = <EMAIL>()
        Log.d(TAG, "loadAsDocFile: file=${fileBean.logInfo()}, uri=${thumbnailData.uri.logInfo()}")
        Glide.with(context)
            .asBitmap()
            .load(thumbnailData)
            .apply(options)
            .override(width, height)
            .addListener(LoadResultLogListener("loadAsDocFile", fileBean))
            .into(target)
        return target
    }

    private fun loadAsApkFile(fileBean: BaseFileBean): AbsHeapUpImageTarget<*> {
        val target = CommonHeapUpTarget(
            context = context,
            fileBean = fileBean,
            defaultPlaceholderRes = fileBean.getPlaceholderRes(),
            isAlwaysIcon = true
        )
        val filePath = fileBean.mData.takeUnless { it.isNullOrEmpty() } ?: run {
            Log.e(TAG, "loadAsApkFile: ERROR! No audio file path")
            target.loadWithPlaceholder()
            return target
        }
        val loader = ApkIconLoader(context, LoadResultLogListener("loadAsApkFile", fileBean))
        loader.load(filePath, target)
        return target
    }

    private fun loadAsRemoteFile(
        fileBean: BaseFileBean,
        options: RequestOptions = prepareOptions(fileBean)
    ): AbsHeapUpImageTarget<*> {
        val target = CommonHeapUpTarget(
            context = context,
            fileBean = fileBean,
            defaultPlaceholderRes = fileBean.getPlaceholderRes(),
        )
        if (fileBean is RemoteFileBean) {
            val picUri = fileBean.remotePicUri
            //后续可能有时效性问题
            if (picUri != null) {
                Glide.with(context).asBitmap().load(picUri).apply(options)
                    .addListener(LoadResultLogListener("loadAsRemoteFile", fileBean))
                    .into(target)
                return target
            }
            val devicesId = getDeviceId()
            val picFile = createFile(context)
            val newPicUri = createUri(context, picFile)
            val packageName = context.packageName
            context.grantUriPermission(
                "com.oplus.remotecontrol",
                newPicUri,
                Intent.FLAG_GRANT_WRITE_URI_PERMISSION
            )
            Injector.injectFactory<IRemoteThumbnail>()?.getPicThumbnails(devicesId,
                fileBean.originalPath,
                newPicUri,
                packageName,
                remoteWidth,
                remoteHeight,
                object : IRemotePicListener {
                    override fun onResult(code: Int, picUri: Uri) {
                        context.revokeUriPermission(
                            "com.oplus.remotecontrol",
                            picUri,
                            Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                        )
                        if (code == SUCCESS) {
                            fileBean.remotePicUri = picUri
                        }
                        Log.i(TAG, "loadAsRemoteFile picUri: $picUri")
                        Glide.with(context).asBitmap().load(picUri).apply(options)
                            .addListener(LoadResultLogListener("loadAsRemoteFile", fileBean))
                            .into(target)
                    }
                })
        }
        return target
    }

    private class LoadResultLogListener<T : Any>(
        private val methodTag: String,
        private val fileBean: BaseFileBean
    ) : RequestListener<T> {
        override fun onLoadFailed(
            e: GlideException?,
            model: Any?,
            target: Target<T>,
            isFirstResource: Boolean
        ): Boolean {
            logLoadFailed()
            return false
        }

        fun logLoadFailed() {
            Log.e(TAG, "$methodTag: onLoadFailed: ${fileBean.logInfo()}")
        }

        override fun onResourceReady(
            resource: T,
            model: Any,
            target: Target<T>?,
            dataSource: DataSource,
            isFirstResource: Boolean
        ): Boolean {
            logResourceReady()
            return false
        }

        fun logResourceReady() {
            Log.d(TAG, "$methodTag: onResourceReady: ${fileBean.logInfo()}")
        }
    }

    private class ApkIconLoader(
        private val context: Context,
        private val resultListener: LoadResultLogListener<Bitmap>
    ) {

        private val runningScope = CoroutineScope(Dispatchers.IO)

        fun load(apkPath: String, target: CommonHeapUpTarget) {
            runningScope.launch {
                val icon = getApkIcon(apkPath)
                if (icon != null) {
                    resultListener.logResourceReady()
                    withContext(Dispatchers.Main) { target.onResourceReady(icon) }
                } else {
                    resultListener.logLoadFailed()
                    withContext(Dispatchers.Main) { target.loadWithPlaceholder() }
                }
            }
        }

        private fun getApkIcon(apkPath: String): Drawable? {
            val pm = context.packageManager
            val pkgInfo =
                pm.getPackageArchiveInfo(apkPath, PackageManager.GET_ACTIVITIES) ?: return null
            val appInfo = pkgInfo.applicationInfo ?: return null
            appInfo.sourceDir = apkPath
            appInfo.publicSourceDir = apkPath
            val icon = pm.getApplicationIcon(appInfo)
            return icon
        }
    }

    private fun createDir(picFile: File) {
        if (picFile.parentFile?.exists()?.not() == true) {
            picFile.parentFile?.mkdirs()
        }
    }

    private fun createFile(context: Context): File {
        val pictureName = UUID.randomUUID().toString() //需要增加其他标志位
        val picFile = File(File(context.cacheDir, "remotePic"), "$pictureName.png")
        createDir(picFile)
        try {
            picFile.createNewFile()
        } catch (e: IOException) {
            Log.e(TAG, "createUri error " + e.message)
        }
        return picFile
    }

    private fun createUri(context: Context, picFile: File): Uri {
        return ThumbnailProvider.getUriForFile(context, "filemanager.thumbnail.provider", picFile)
    }

    private fun getDeviceId(): String {
        var devicesId = ""
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        val currentLinkInfo = remoteDeviceApi?.getCurrentLinkedRemoteDiceInfo()
        if (currentLinkInfo != null) {
            devicesId = currentLinkInfo.deviceId.toString()
        }
        return devicesId
    }
}