/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LabelLoadingDataPackerTest.kt
 ** Description : LabelLoadingDataPacker Unit Test
 ** Version     : 1.0
 ** Date        : 2023/11/24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue       2023/11/24      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.packer

import android.content.Context
import android.content.res.Resources
import com.filemanager.common.MyApplication
import com.oplus.filemanager.cardwidget.label.data.LabelCardData
import com.oplus.filemanager.cardwidget.label.packer.EmptyFilesPacker
import com.oplus.filemanager.cardwidget.util.TextUtil
import com.oplus.smartenginehelper.dsl.DSLCoder
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import java.io.File

class EmptyFilesPackerTest {

    private lateinit var context: Context
    private lateinit var resources: Resources
    private lateinit var coder: DSLCoder
    private lateinit var labelCard: LabelCardData
    private lateinit var packer: EmptyFilesPacker

    @Before
    fun setup() {
        context = mockk()
        resources = mockk()
        mockkObject(MyApplication)
        every { MyApplication.sAppContext }.returns(context)
        every { context.packageName }.returns("FileManager")
        every { context.filesDir }.returns(File("/data/user/0/com.xxx.filemanager/files/"))
        every { context.resources }.returns(resources)
        coder = mockk()
        labelCard = LabelCardData(1, "最近文件", 1, mutableListOf())
        packer = EmptyFilesPacker(context, labelCard)
    }

    @After
    fun teardown() {
        unmockkObject(MyApplication)
    }

    @Test
    fun should_call_setTextViewText_when_onPack() {
        mockkStatic(TextUtil::class)
        justRun { TextUtil.setTextSize(any(), any(), any(), any()) }
        justRun { coder.setTextViewText(any(), any()) }
        justRun { coder.setOnClick(any(), any()) }

        packer.onPack(coder)
        verify { coder.setTextViewText("label_name", "最近文件") }
        verify { coder.setOnClick("container", any()) }

        unmockkStatic(TextUtil::class)
    }
}