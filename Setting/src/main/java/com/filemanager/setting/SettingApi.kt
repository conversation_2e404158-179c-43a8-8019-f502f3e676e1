/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: SettingApi.kt
 ** Description:  SettingApi
 ** Version: 1.0
 ** Date: 2021/5/7
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.setting

import android.app.Activity
import android.content.res.Configuration
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.utils.Log
import com.filemanager.setting.ui.SettingActivity
import com.filemanager.setting.ui.function.SettingFunctionActivity
import com.filemanager.setting.utils.FlexibleWindowUtil
import com.oplus.filemanager.interfaze.setting.ISetting

object SettingApi : ISetting {

    private const val TAG = "SettingApi"

    override fun startSettingActivity(activity: Activity?) {
        Log.d(TAG, "startSettingActivity")
        SettingActivity.start(activity)
    }

    override fun setBarBackgroundColor(
        toolbar: COUIToolbar,
        appBarLayout: View,
        decorView: View,
        configuration: Configuration,
        activity: AppCompatActivity
    ) {
        FlexibleWindowUtil.setBarBackgroundColor(
            toolbar,
            appBarLayout,
            decorView,
            configuration,
            activity
        )
    }

    override fun setActionCloseFlexibleActivity(decorView: View, activity: Activity) {
        FlexibleWindowUtil.setActionCloseFlexibleActivity(decorView, activity)
    }

    override fun jumpToSettingFunctionActivity(activity: Activity) {
        SettingFunctionActivity.start(activity, true)
    }
}