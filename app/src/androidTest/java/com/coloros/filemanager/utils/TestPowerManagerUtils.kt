/*********************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : TestPowerManagerUtils
 * Description :
 * Version     : 1.0
 * Date        : 2023/08/01
 * Author      : W9002848
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * W9002848        2023/08/08       1.0      create
 **********************************************************************/
package com.coloros.filemanager.utils

import android.content.Context
import android.os.PowerManager
import com.heytap.addon.app.OplusWhiteListManager
import com.oplus.autotest.olt.testlib.common.BasicData

object TestPowerManagerUtils {
    private val TAG = TestPowerManagerUtils::class.java.simpleName

    private val powerManager by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        BasicData.getTargetContext().getSystemService(Context.POWER_SERVICE) as PowerManager
    }


    val isInteractive: Boolean
        get() {
            return powerManager.isInteractive
        }

    fun removeStageProtect(context: Context) {
        TestLog.e(TAG, "[removeStageProtect] app = " + context.packageName)
        val owm = OplusWhiteListManager(context)
        owm.removeStageProtectInfo(context.packageName)
    }

    val isPowerSaveMode: Boolean
        get() {
            return powerManager.isPowerSaveMode
        }
}