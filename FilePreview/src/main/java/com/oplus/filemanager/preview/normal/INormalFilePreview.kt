/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - INormalFilePreview.kt
 * Description:
 *     The interface to show file icon to preview it.
 *
 * Version: 1.0
 * Date: 2024-10-17
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-10-17   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.normal

import android.view.View
import androidx.lifecycle.LifecycleOwner
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite

internal interface INormalFilePreview {
    /**
     * Attach to container views and show file icon.
     */
    fun attachToContainer(container: PreviewFileInfoSuite)

    /**
     * Release when not use it anymore.
     */
    fun release()

    /**
     * fill file info
     */
    fun setFileInfoStruct(view: View)

    companion object {
        @JvmStatic
        fun obtain(
            lifecycleOwner: LifecycleOwner,
            previewModel: FilePreviewViewModel
        ): INormalFilePreview = NormalFilePreviewImpl(lifecycleOwner, previewModel)
    }
}