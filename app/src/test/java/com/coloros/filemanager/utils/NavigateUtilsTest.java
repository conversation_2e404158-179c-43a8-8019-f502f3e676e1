/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: NavigateUtilsTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/9/03
 ** Author: <PERSON><PERSON><PERSON><PERSON>
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/9/03      1.0     create
 ****************************************************************/
package com.coloros.filemanager.utils;

import static com.filemanager.common.utils.NavigateUtils.NAVIGATE_UP_TITLE_ID;
import static com.filemanager.common.utils.NavigateUtils.NAVIGATE_UP_TITLE_TEXT;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.media.RingtoneManager;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;

import com.coloros.filemanager.BaseTest;
import com.filemanager.common.utils.IntentUtils;
import com.filemanager.common.utils.NavigateUtils;

import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.robolectric.RobolectricTestRunner;

@Ignore("stupid test")
@RunWith(RobolectricTestRunner.class)
public class NavigateUtilsTest extends BaseTest {
    private NavigateUtils mNavigateUtils;
    private Intent mIntent;
    private ActionBar mActionBar;
    private Resources mResources;

    @Before
    public void setUp() {
        mNavigateUtils = new NavigateUtils();
        mContext = mock(Context.class);
        mIntent = mock(Intent.class);
        mActionBar = mock(ActionBar.class);
        mResources = mock(Resources.class);
    }

    @After
    public void tearDown() {
        mNavigateUtils = null;
        mIntent = null;
        mActionBar = null;
        mResources = null;
    }

    @Test
    public void should_verify_when_setNavigateTitle() {
        AppCompatActivity activity = mock(AppCompatActivity.class);
        when(activity.getSupportActionBar()).thenReturn(mActionBar);
        mNavigateUtils.setNavigateTitle(activity, mIntent);
        verify(mActionBar).setDisplayHomeAsUpEnabled(true);
    }


    @Test
    public void should_verify_when_setNavigateTitle_without() {

        mNavigateUtils.setNavigateTitle(null, mActionBar, mIntent);
        mNavigateUtils.setNavigateTitle(mContext, null, mIntent);

        MockedStatic intentUtilsMockedStatic = mockStatic(IntentUtils.class);
        intentUtilsMockedStatic.when(() -> IntentUtils.getString(mIntent, NAVIGATE_UP_TITLE_TEXT)).thenReturn("test");
        mNavigateUtils.setNavigateTitle(mContext, mActionBar, mIntent);

        intentUtilsMockedStatic.when(() -> IntentUtils.getString(mIntent, NAVIGATE_UP_TITLE_TEXT)).thenReturn("");
        intentUtilsMockedStatic.when(() -> IntentUtils.getInt(mIntent, NAVIGATE_UP_TITLE_ID, 0)).thenReturn(0);
        mNavigateUtils.setNavigateTitle(mContext, mActionBar, mIntent);
        verify(mActionBar, times(2)).setDisplayHomeAsUpEnabled(false);

        intentUtilsMockedStatic.close();
    }

    @Test
    public void should_verify_when_setTitle_without() {
        mNavigateUtils.setTitle(null, mActionBar, mIntent);
        mNavigateUtils.setTitle(mContext, null, mIntent);

        String title = "test";
        when(mContext.getResources()).thenReturn(mResources);
        when(mIntent.getIntExtra(RingtoneManager.EXTRA_RINGTONE_TITLE, 0)).thenReturn(1);
        when(mResources.getString(1)).thenReturn(title);
        mNavigateUtils.setTitle(mContext, mActionBar, mIntent);
        verify(mActionBar).setTitle(title);
    }
}