/*********************************************************************************
 * Copyright (C), 2008-2024, Oplus, All rights reserved.
 *
 * File: - PreviewLoadingScheduler.kt
 * Description:
 *     Schedule show or dismiss preview loading page.
 *
 * Version: 1.0
 * Date: 2024-12-10
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-12-10   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.core

import android.os.Handler
import android.os.Looper
import android.os.Message
import android.os.SystemClock
import com.filemanager.common.utils.Log

internal abstract class AbsPreviewLoadingScheduler : Handler.Callback {

    private companion object {
        private const val TAG = "PreviewLoadingScheduler"
        private const val MSG_DELAY_TO_SHOW_LOADING = 1001
        private const val MSG_DELAY_TO_HIDE_LOADING = 1002

        /**
         * 需要加载时，加载时间在该限制值内则不显示loading状态
         */
        private const val MAX_CURB_SHOW_LOADING_TIME = 1000L

        /**
         * 加载状态实际显示出来后的最少显示时间
         */
        private const val MIN_SHOWING_LOADING_TIME = 500L
    }

    protected abstract val subTag: String

    protected val logTag by lazy { "$TAG($subTag)" }

    private val loadingHandler by lazy { Handler(Looper.getMainLooper(), this) }

    private var isRequiredShowLoading = false
    private var isRequiredDismissLoading = false

    fun resetLoadingScheduler() {
        loadingHandler.removeCallbacksAndMessages(null)
        isRequiredShowLoading = false
        isRequiredDismissLoading = false
    }

    fun requireToShowLoading() {
        if (onGetStartLoadingTime() != null) {
            return
        }
        if (isRequiredShowLoading) {
            return
        }
        isRequiredShowLoading = true
        Log.d(logTag, "requireToShowLoading: delay to show")
        loadingHandler
            .sendEmptyMessageDelayed(MSG_DELAY_TO_SHOW_LOADING, MAX_CURB_SHOW_LOADING_TIME)
    }

    private fun cancelToShowLoading() {
        Log.d(logTag, "cancelToShowLoading")
        isRequiredShowLoading = false
        loadingHandler.removeMessages(MSG_DELAY_TO_SHOW_LOADING)
    }

    fun dismissLoading(extraObj: Any?) {
        if (isRequiredDismissLoading) {
            return
        }
        cancelToShowLoading()
        isRequiredShowLoading = false
        loadingHandler.removeMessages(MSG_DELAY_TO_HIDE_LOADING)
        val startLoadingTime = onGetStartLoadingTime()
        if (startLoadingTime == null) {
            onDismissLoading(extraObj)
            return
        }
        val showingTime = SystemClock.uptimeMillis() - startLoadingTime
        if (showingTime >= MIN_SHOWING_LOADING_TIME) {
            onDismissLoading(extraObj)
            return
        }
        isRequiredDismissLoading = true
        val delayTime = MIN_SHOWING_LOADING_TIME - showingTime
        val logMsg = "delayTime=$delayTime, extraObj=${onLogPrintExtraObj(extraObj)}"
        Log.d(logTag, "dismissLoading: delay to hide, $logMsg")
        val msg = Message.obtain(loadingHandler, MSG_DELAY_TO_HIDE_LOADING, extraObj)
        loadingHandler.sendMessageDelayed(msg, MIN_SHOWING_LOADING_TIME - showingTime)
    }

    /**
     * 将预览界面切换为loading状态
     */
    protected abstract fun onShowLoading()

    /**
     * 获取界面实际进入loading状态的[SystemClock.uptimeMillis]时间戳
     */
    protected abstract fun onGetStartLoadingTime(): Long?

    /**
     * 退出预览界面的loading状态
     */
    protected abstract fun onDismissLoading(extraObj: Any?)

    /**
     * 日志中如何输出[extraObj]
     */
    protected open fun onLogPrintExtraObj(extraObj: Any?): String = "$extraObj"

    final override fun handleMessage(msg: Message): Boolean {
        when (msg.what) {
            MSG_DELAY_TO_SHOW_LOADING -> {
                Log.d(logTag, "requireToShowLoading: execute show loading")
                isRequiredShowLoading = false
                onShowLoading()
            }

            MSG_DELAY_TO_HIDE_LOADING -> {
                Log.d(logTag, "dismissLoading: execute hide loading")
                isRequiredDismissLoading = false
                onDismissLoading(msg.obj)
            }

            else -> return false
        }
        return true
    }
}