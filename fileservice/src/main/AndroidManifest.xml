<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-sdk tools:overrideLibrary="com.oplus.oworksdk,com.oplus.sdk.addon.sdk" />

    <uses-permission android:name="com.oplus.permission.safe.POWER" />

    <application>

        <meta-data
            android:name="service.xml.res"
            android:resource="@raw/file_service" />

        <service
            android:name=".operate.FileOperateService"
            android:enabled="true"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE" />
        <service
            android:name=".operate.FileCancelOperateService"
            android:enabled="true"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE" />

        <service
            android:name=".filelist.RootFilesService"
            android:enabled="true"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE" />
        <service
            android:name=".filelist.FilePathListsService"
            android:enabled="true"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE" />
        <service
            android:name=".filelist.FileSelectPathService"
            android:enabled="true"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE" />
        <service
            android:name=".filelist.CategoryFilesService"
            android:enabled="true"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE" />
        <service
            android:name=".filelist.AlbumFilesService"
            android:enabled="true"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE" />
        <service
            android:name=".filelist.AlbumSetFilesService"
            android:enabled="true"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE" />
    </application>
</manifest>