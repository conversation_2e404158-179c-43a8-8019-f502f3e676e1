package com.filemanager.common.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.text.Layout
import android.text.Spanned
import android.text.TextUtils
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import com.filemanager.common.utils.AppUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * IconWrapSpan的单元测试类
 * 用于测试IconWrapSpan类的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class IconWrapSpanTest {

    // 定义测试所需的mock对象
    private lateinit var context: Context
    private lateinit var mockDrawable: Drawable
    private lateinit var mockBitmap: Bitmap
    private lateinit var mockCanvas: Canvas
    private lateinit var mockPaint: Paint
    private lateinit var mockLayout: Layout
    private lateinit var mockSpanned: Spanned

    /**
     * 测试前的初始化方法
     * 创建所有需要的mock对象并设置静态方法的mock行为
     */
    @Before
    fun setUp() {
        context = mockk(relaxed = true)
        mockDrawable = mockk(relaxed = true)
        mockBitmap = mockk(relaxed = true)
        mockCanvas = mockk(relaxed = true)
        mockPaint = mockk(relaxed = true)
        mockLayout = mockk(relaxed = true)
        mockSpanned = mockk(relaxed = true)

        // 设置静态方法的mock行为
        mockkStatic(ContextCompat::class)
        mockkStatic(AppUtils::class)
        mockkStatic(android.util.Log::class)
    }

    /**
     * 测试后的清理方法
     * 解除所有mock对象的绑定
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试getLeadingMargin方法在非首行时的行为
     * 预期返回0
     */
    @Test
    fun testGetLeadingMargin_whenNotFirst_returnsZero() {
        val span = IconWrapSpan(mockBitmap)
        
        val result = span.getLeadingMargin(false)
        
        assertEquals(0, result)
    }

    /**
     * 测试drawLeadingMargin方法在非首行时的行为
     * 预期不会调用绘制位图的方法
     */
    @Test
    fun testDrawLeadingMargin_whenNotFirst_doesNothing() {
        val span = IconWrapSpan(mockBitmap)
        
        span.drawLeadingMargin(
            mockCanvas, mockPaint, 0, 1, 0, 0, 0,
            mockSpanned, 0, 0, false, mockLayout
        )
        
        verify(exactly = 0) { mockCanvas.drawBitmap(any<Bitmap>(), any<Float>(), any<Float>(), any<Paint>()) }
    }

    /**
     * 测试getBitmapFromVectorDrawable方法在drawable为null时的行为
     * 预期返回null
     */
    @Test
    fun testGetBitmapFromVectorDrawable_whenDrawableNull_returnsNull() {
        every { ContextCompat.getDrawable(any(), any()) } returns null
        
        val result = IconWrapSpan.getBitmapFromVectorDrawable(context, 123)
        
        assertEquals(null, result)
    }

    /**
     * 测试getBitmapFromVectorDrawable方法在drawable有效时的行为
     * 预期返回对应的位图对象
     */
    @Test
    fun testGetBitmapFromVectorDrawable_whenDrawableValid_returnsBitmap() {
        every { ContextCompat.getDrawable(any(), any()) } returns mockDrawable
        every { AppUtils.getBitmapFromDrawable(mockDrawable) } returns mockBitmap
        
        val result = IconWrapSpan.getBitmapFromVectorDrawable(context, 123)
        
        assertEquals(mockBitmap, result)
    }
}