package com.filemanager.feedback

import android.app.Activity
import android.content.pm.ActivityInfo
import com.customer.feedback.sdk.FeedbackHelper
import com.customer.feedback.sdk.model.RequestData
import com.filemanager.common.utils.IdentifierManager
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import io.mockk.*
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * FeedbackApi的单元测试类
 * 使用Robolectric和MockK框架进行测试
 * 主要测试FeedbackApi的反馈功能相关逻辑
 */
@ExperimentalCoroutinesApi
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class FeedbackApiTest {

    // 模拟Activity对象
    @MockK
    private lateinit var mockActivity: Activity

    // 模拟FeedbackHelper对象
    @MockK
    private lateinit var mockFeedbackHelper: FeedbackHelper

    // 测试用的协程调度器
    private val testDispatcher = StandardTestDispatcher()
    // 测试用的协程作用域
    private val testScope = TestScope(testDispatcher)

    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 设置主调度器为测试调度器
     * 3. 模拟各种静态方法和对象
     */
    @Before
    fun setUp() {
        // 初始化MockK注解
        MockKAnnotations.init(this)
        // 设置主调度器为测试调度器
        Dispatchers.setMain(testDispatcher)
        // 模拟FeedbackHelper单例对象
        mockkObject(FeedbackHelper)
        // 模拟CollectPrivacyUtils对象
        mockkObject(CollectPrivacyUtils)
        // 模拟ModelUtils的静态方法
        mockkStatic(ModelUtils::class)  // 修复：使用静态mock替代对象mock
        // 模拟Log工具类
        mockkStatic(Log::class)
        // 模拟统计工具类
        mockkStatic(StatisticsUtils::class)
        // 模拟标识管理器
        mockkStatic(IdentifierManager::class)

        // 设置FeedbackHelper.getInstance()总是返回模拟对象
        every { FeedbackHelper.getInstance(any()) } returns mockFeedbackHelper
        // 设置模拟Activity的applicationContext
        every { mockActivity.applicationContext } returns mockk(relaxed = true)
        // 设置获取VAID的协程调用返回测试值
        coEvery { IdentifierManager.getVAID(any()) } returns "test_vaid"
        // 设置初始化SDK的协程调用不做任何操作
        coEvery { IdentifierManager.initSdk(any()) } just Runs
        // 设置清理SDK的协程调用不做任何操作
        coEvery { IdentifierManager.clearSdk(any()) } just Runs
        
        // 修复：添加ModelUtils.isTablet()的默认mock，默认返回false
        every { ModelUtils.isTablet() } returns false
        
        // 修复：添加StatisticsUtils.onCommon的mock，不做任何操作
        every { StatisticsUtils.onCommon(any(), any()) } just Runs
    }

    /**
     * 测试后的清理方法
     * 1. 重置主调度器
     * 2. 解除所有模拟
     */
    @After
    fun tearDown() {
        // 重置主调度器
        Dispatchers.resetMain()
        // 解除所有模拟
        unmockkAll()
    }

    /**
     * 测试平板设备下的方向设置
     * 验证当设备是平板时，会正确设置屏幕方向
     */
    @Test
    fun `launchFeedBack should set orientation for tablets`() = testScope.runTest {
        // 设置设备为平板
        every { ModelUtils.isTablet() } returns true

        // 调用被测方法
        FeedbackApi.launchFeedBack(mockActivity)

        // 验证是否调用了设置屏幕方向的方法
        verify { mockFeedbackHelper.setCommonOrientationType(ActivityInfo.SCREEN_ORIENTATION_USER) }
    }
}