/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Durable.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/9
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  hank.zhou      2022/9/9      1.0        create
 ***********************************************************************/
package com.android.documentsui.base;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;

public interface Durable {
     void reset();
     void read(DataInputStream in) throws IOException;
     void write(DataOutputStream out) throws IOException;
}

