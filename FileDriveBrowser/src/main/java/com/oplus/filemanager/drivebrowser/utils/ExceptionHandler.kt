/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ExceptionHandler
 ** Description : Exception handler
 ** Version     : 1.0
 ** Date        : 2024/03/05 11:05
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/03/05       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import retrofit2.HttpException
import java.io.IOException
import java.net.UnknownHostException
import java.net.UnknownServiceException

object ExceptionHandler {

    /**
     * 判断是否是服务异常
     */
    fun isSeverError(t: Throwable): Boolean {
        return t is HttpException || t is UnknownHostException || t is UnknownServiceException || t is IOException
    }
}