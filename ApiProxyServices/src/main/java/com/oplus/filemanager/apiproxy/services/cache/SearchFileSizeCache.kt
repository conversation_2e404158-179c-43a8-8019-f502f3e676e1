/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchFileSizeCache
 ** Description : 由于打开远程电脑文件需要传递filePath和size 两个值，而deeplink传递过来的值只有filePath, 因此缓存搜索结果的filePath和size
 ** Version     : 1.0
 ** Date        : 2025/02/20 16:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2025/02/20       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.cache

import com.filemanager.common.base.BaseFileBean

object SearchFileSizeCache {

    /**
     * 缓存 文件path 和size
     */
    private val cache = HashMap<String, Long>()

    @JvmStatic
    fun put(filePath: String, fileSize: Long) {
        cache.put(filePath, fileSize)
    }

    @JvmStatic
    fun getFileSize(filePath: String): Long {
        return cache.get(filePath) ?: -1L
    }

    @JvmStatic
    fun clear() {
        cache.clear()
    }

    @JvmStatic
    fun cache(list: List<BaseFileBean>) {
        list.forEach {
            put(it.mData ?: "", it.mSize)
        }
    }
}