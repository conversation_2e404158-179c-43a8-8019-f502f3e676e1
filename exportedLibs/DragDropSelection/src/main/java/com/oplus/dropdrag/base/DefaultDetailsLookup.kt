/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - DefaultDetailsLookup.java
 ** Description: Details Lookup for file path
 ** Version: 1.0
 ** Date : 2020/05/25
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  Jiafei.<PERSON>@Apps.FileManager      2020/05/25    1.0     create
 ****************************************************************/

package com.oplus.dropdrag.base

import android.view.MotionEvent
import android.view.View
import androidx.annotation.Keep
import androidx.recyclerview.widget.RecyclerView
import com.oplus.dropdrag.recycleview.ItemDetailsLookup
import com.oplus.dropdrag.utils.Log

@Keep
class DefaultDetailsLookup(view: RecyclerView?) : ItemDetailsLookup<Int>() {
    private companion object {
        private const val TAG = "DefaultDetailsLookup"
    }
    private var mRecView: RecyclerView? = view

    override fun getItemDetails(e: MotionEvent): ItemDetails<Int>? {
        mRecView?.let {
            val view = it.findChildViewUnder(e.x, e.y) ?: return null
            val holder = it.getChildViewHolder(view)
            if (holder is SelectionViewHolder<*>) {
                return holder.getItemDetails() as? ItemDetails<Int>?
            }
        }

        return null
    }

    override fun getItemView(e: MotionEvent): View? {
        kotlin.runCatching {
            mRecView?.let {
                return it.findChildViewUnder(e.x, e.y)
            }
        }.onFailure {
            Log.e(TAG, "getItemView onFailure: ${it.message}")
        }
        return null
    }
}