/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowMediaHelper.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/8/28
 ** Author: Ji<PERSON>B<PERSON> Lan
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/8/28      1.0     create
 ****************************************************************/
package shadow;

import android.content.Context;
import android.net.Uri;

import com.filemanager.common.helper.FileWrapper;
import com.filemanager.common.helper.MediaHelper;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(MediaHelper.class)
public class ShadowMediaHelper {
    @Implementation
    public static Uri getUri(Context context, FileWrapper file) {
        return null;
    }
}
