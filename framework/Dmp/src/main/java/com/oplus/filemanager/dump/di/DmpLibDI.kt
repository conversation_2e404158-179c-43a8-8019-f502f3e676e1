/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: DmpLibDI.kt
 ** Description: dmpLib的注入类
 ** Version: 1.0
 ** Date: 2024/6/12
 ** Author: huangyuanwang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.dump.di

import androidx.annotation.Keep
import com.oplus.filemanager.dump.DumpSearchApi
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import org.koin.dsl.module

@Keep
class DmpLibDI {

    val dmpLibModule = module {
        single<IDmpSearchApi>(createdAtStart = true) {
            DumpSearchApi
        }
    }
}