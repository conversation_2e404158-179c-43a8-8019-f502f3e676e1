/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.select
 * * Version     : 1.0
 * * Date        : 2020/7/6
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir

import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.View.GONE
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.view.WindowMetrics
import androidx.annotation.RequiresApi
import androidx.annotation.VisibleForTesting
import androidx.appcompat.widget.Toolbar
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.COUIRecyclerView.VISIBLE
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.button.SingleButtonWrap
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseFolderAnimAdapter.Companion.FILE_BROWSER_FOLDER_ANIM_DELAY
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.BaseVMPanelFragment
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.OnGetUIInfoListener
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.COUISnackBarUtils
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.BrowserPathBar
import com.filemanager.common.view.BrowserPathBar.Companion.FILE_BROWSER_FOLDER_ANIM_TIME
import com.filemanager.common.view.FileManagerRecyclerView
import com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller
import com.oplus.dropdrag.recycleview.ItemDetailsLookup
import com.oplus.selectdir.filebrowser.SelectFileBrowserViewModel

open class SelectDirPathPanelFragment : BaseVMPanelFragment<SelectFileBrowserViewModel>(), OnBackPressed, Toolbar.OnMenuItemClickListener,
    OnRecyclerItemClickListener, OnGetUIInfoListener {
    companion object {
        private const val TAG = "SelectDirPathPanelFragment"
        const val KEY_ACTION_CODE = "KEY_ACTION_CODE"
        const val DELAYED_TIME = 10L
        const val PANEL_MAX_HEIGHT = "panel_max_height"
        const val FULL_SCREEN_SELECT_PANEL_MIN_PERCENT = 0.9
        const val KEEP_TWO_DECIMAL_PLACE = 100.00
        const val DIALOG_DISMISS_DURATION = 350L
        const val SELECT_DIALOG_FRAGMENT_TAG = "SELECT_DIALOG_FRAGMENT_TAG"
        private const val LOADING_DELAY_TIME = 500L
    }

    private var mInitCurrentPath: String? = null
    private var mCurrentPath: String? = null
    private var mIsSetInitLoad = true
    protected var mActionCode: Int = -1
    private var mTitle: String? = null
    private var titleId: Int = -1
    protected var mToolbar: COUIToolbar? = null
    protected var mPathBar: BrowserPathBar? = null
    protected var mSelectButton: COUIButton? = null
    protected var mRecyclerView: FileManagerRecyclerView? = null
    protected var mRecyclerViewFastScroller: RecyclerViewFastScroller? = null
    private var selectButtonWrapper: View? = null
    private var mAdapter: SelectDirPathAdapter? = null
    private var mLayoutManager: GridLayoutManager? = null
    var mViewModel: SelectDirViewModel? = null
    private var mLoadingController: LoadingController? = null
    protected var mSelectPathDialogListener: TransformNextFragmentListener? = null
    protected var mSelectPathDialogInterface: SelectPathDialogInterface? = null
    private val mFolderTransformAnimator by lazy { FolderTransformAnimator() }
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private var mButtonDivider: View? = null
    private val mHandler = Handler(Looper.getMainLooper())
    private var verticalButtonWrap: SingleButtonWrap? = null
    private var isDialogShowing = false
    @VisibleForTesting
    var canClose = true
    protected var hasStoragePermission: Boolean = true

    fun setSelectPathDialogInterface(selectPathDialogInterface: SelectPathDialogInterface) {
        mSelectPathDialogInterface = selectPathDialogInterface
    }

    override fun getLayoutResId(): Int {
        return R.layout.selectdir_path_fragment
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        initViewModel()
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    /**
     * 该类没在用所以直接使用@RequiresApi注解,过编译lint检查
     * 以后如果用到该方法则需向Q版本适配
     */
    @RequiresApi(Build.VERSION_CODES.R)
    private fun isZoomWindow(): Boolean {
        /* 判断悬浮窗的时候，如果是悬浮窗，再判断一下activity的弹出框高度跟屏幕的高度比 */
        /* 如果大于FULL_SCREEN_SELECT_PANEL_MIN_PERCENT，就不是悬浮窗 */
        var isZoomWindow = false
        mActivity?.let { activity ->
            val windowManager = activity.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            // 屏幕物理高度
            val maxWindowMetrics: WindowMetrics = windowManager.maximumWindowMetrics
            val physicalHeight = maxWindowMetrics.bounds.height()
            // app界面高度
            val currentWindowMetrics: WindowMetrics = windowManager.currentWindowMetrics
            val appHeight = currentWindowMetrics.bounds.height()
            Log.d(TAG, "physicalHeight: $physicalHeight, appHeight: $appHeight")
            // app的弹出panel高度
            rootView?.layoutParams?.height?.let { height ->
                var finalHeight = 0
                val tempHeight = appHeight - activity.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_40dp)
                finalHeight = if (height < 0) {
                    tempHeight
                } else {
                    handleZoomWindowHeight(activity, height)
                }
                if (finalHeight > 0 && physicalHeight > 0) {
                    val heightPercent = (finalHeight * KEEP_TWO_DECIMAL_PLACE) / (physicalHeight * KEEP_TWO_DECIMAL_PLACE)
                    Log.d(TAG, "tempHeight: $tempHeight, finalHeight: $finalHeight, heightPercent: $heightPercent")
                    isZoomWindow = heightPercent < FULL_SCREEN_SELECT_PANEL_MIN_PERCENT
                }
            }
            Log.d(TAG, "isZoomWindow $isZoomWindow")
        }
        return isZoomWindow
    }

    /**
     * 处理浮窗模式下的高度
     */
    private fun handleZoomWindowHeight(activity: Activity, height: Int): Int {
        var result = height
        val statusBarH = COUIPanelMultiWindowUtils.getStatusBarHeight(activity)
        val configuration = activity.resources.configuration
        val paddingBottom = COUIPanelMultiWindowUtils.getPanelMarginBottom(context, configuration)
        val rect = COUIPanelMultiWindowUtils.getCurrentWindowVisibleRect(activity)
        if (rect != null) {
            if (rect.top == 0) {
                Log.d(TAG, "handleZoomWindowHeight: rect $rect top is invalid, reset")
                rect.top = activity.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.panel_min_top_margin)
            }
            if (rect.bottom == 0) {
                val windowH = KtViewUtils.getWindowSize(activity).y
                Log.d(TAG, "handleZoomWindowHeight: rect $rect bottom is invalid, reset $windowH")
                rect.bottom = windowH
            }
            result = (rect.bottom - rect.top) - paddingBottom - statusBarH
            Log.d(TAG, "handleZoomWindowHeight: height $result = ${rect.bottom}-${rect.top}-$paddingBottom-$statusBarH")
        }
        return result
    }

    /**
     * 处理全面屏手势导航时的高度
     */
    private fun handleGestureNavModeHeight(activity: Activity, height: Int): Int {
        if (COUIPanelMultiWindowUtils.isDisplayInUpperWindow(activity)) { // 上分屏，不用处理
            return height
        }
        // 处于下分屏，并且显示了手势指示条
        if (StatusBarUtil.checkShowGestureNavBar(activity)) {
            val result = height - activity.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_16dp)
            Log.d(TAG, "handleGestureNavMode lower split screen with show gesture nvaBar height:$result")
            return result
        }
        return height
    }

    /**
     * 处理底部导航栏导航时的高度
     */
    private fun handleNavModeHeight(it: Activity, height: Int): Int {
        val navBarHeight = KtViewUtils.getSoftNavigationBarSize(it)
        var result = height
        when {
            ModelUtils.isTablet() -> { //平板竖屏分屏时
                if (UIConfigMonitor.instance.isDevicePortrait(it)) {
                    result = height - navBarHeight
                    Log.d(TAG, "handleNavMode pad portrait height:$result")
                }
            }

            UIConfigMonitor.instance.isFoldable(it) -> { // 折叠屏
                result = height - navBarHeight
                Log.d(TAG, "handleNavMode fold height:$result")
            }

            else -> { //手机且分屏处于下屏
                if (!COUIPanelMultiWindowUtils.isDisplayInUpperWindow(it)) {
                    result = height - navBarHeight
                    Log.d(TAG, "handleNavMode phone lower split screen height:$result")
                }
            }
        }
        return result
    }

    private fun initOnBackKeyListener() {
        setDialogOnKeyListener { _, keyCode, event ->
            Log.d(TAG, "onKey keyCode:$keyCode event:$event")
            if ((keyCode == KeyEvent.KEYCODE_BACK) && (event.action == KeyEvent.ACTION_UP)) {
                if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK).not()) {
                    if ((activity as? COUISnackBarUtils.ShowPanel)?.isAlreadySave() == true || pressBack().not()) {
                        mSelectPathDialogInterface?.performCancel(SELECT_DIALOG_FRAGMENT_TAG)
                    }
                }
                true
            } else {
                false
            }
        }
        PredictiveBackUtils.registerOnBackInvokedCallback(this)
    }

    private fun initViewModel() {
        (activity as? TransformNextFragmentListener)?.run {
            mSelectPathDialogListener = this
        }
        mViewModel = ViewModelProvider(this).get(SelectDirViewModel::class.java)
        fragmentViewModel = mViewModel
    }

    private fun initArgument() {
        activity?.let {
            mActivity = it as? BaseVMActivity
            val bundle = arguments ?: return
            val currentPath = initSelectDirPanelPath(bundle)
            if (currentPath != mCurrentPath) {
                mIsSetInitLoad = true
            }
            mCurrentPath = currentPath
            mActionCode = bundle.getInt(KEY_ACTION_CODE)
            val title = bundle.getString(KtConstants.P_TITLE)
            titleId = bundle.getInt(KtConstants.P_TITLE_ID, -1)
            mTitle = if (titleId != -1) {
                getString(titleId)
            } else {
                title
            }
            if (mAdapter == null) {
                mAdapter = SelectDirPathAdapter(
                    it, <EMAIL>, mActionCode,
                    mViewModel
                ).apply {
                    setHasStableIds(true)
                    setSelectEnabled(mActionCode == MessageConstant.MSG_ADD_SHORTCUT_FOLDER)
                }
            }
            hasStoragePermission = bundle.getBoolean(KtConstants.HAS_STORAGE_PERMISSION, true)
        }
    }

    private fun initSelectDirPanelPath(bundle: Bundle?): String? {
        mInitCurrentPath = bundle?.getString(KtConstants.P_CURRENT_PATH)
        val currentPath =
                if (mViewModel?.mPositionModel?.value?.mCurrentPath != null
                        && !mViewModel?.mPositionModel?.value?.mCurrentPath.equals("")) {
                    mViewModel?.mPositionModel?.value?.mCurrentPath
                } else {
                    mInitCurrentPath
                }
        return currentPath
    }

    override fun initContentView(view: View) {
        initArgument()
        hideDragView()
        rootView = view.findViewById(R.id.coordinator_layout)
        appBarLayout = view.findViewById(R.id.appbar)
        mRecyclerViewFastScroller = view.findViewById(R.id.fastScroller)
        mRecyclerView = view.findViewById(R.id.recycler_view)
        fragmentRecyclerView = mRecyclerView
        mPathBar = view.findViewById<BrowserPathBar>(R.id.path_bar)?.apply {
            setInPopWindow(true)
        }
        initToolBar(view)
        initSelectButton(view)
        setNavigationVisible(mActionCode != MessageConstant.MSG_SAVE)
        updateButtonView()
        initOnBackKeyListener()
        mButtonDivider = view.findViewById<View>(R.id.button_divider)
        setRecyclerViewMarginTop()
    }

    private fun setRecyclerViewMarginTop() {
        appBarLayout?.post {
            mRecyclerViewFastScroller?.apply {
                val marginLayoutParams = this.layoutParams as ViewGroup.MarginLayoutParams
                marginLayoutParams.topMargin = appBarLayout?.measuredHeight ?: 0
                this.layoutParams = marginLayoutParams
            }
        }
    }

    private fun initToolBar(view: View) {
        mToolbar = view.findViewById<COUIToolbar>(R.id.toolbar)?.apply {
            title = mTitle
            isTitleCenterStyle = true
            inflateMenu(R.menu.selectdir_close_option)
            if (hasStoragePermission.not()) {
                menu.findItem(R.id.action_new_folder).isEnabled = false
            }
            setOnMenuItemClickListener(this@SelectDirPathPanelFragment)
        }
    }

    private fun initSelectButton(view: View) {
        selectButtonWrapper = view.findViewById(R.id.select_button_parent)
        mSelectButton = view.findViewById<COUIButton>(R.id.select_button)?.apply {
            verticalButtonWrap = SingleButtonWrap(this, SingleButtonWrap.Type.Large)
            setOnClickListener {
                if (Utils.isQuickClick() || isDialogShowing) {
                    Log.w(TAG, "click too fast, try later")
                    return@setOnClickListener
                }
                isDialogShowing = true
                var selectPaths = mViewModel?.getSelectPaths()
                Log.d(TAG, "selectButton click -> select path:${selectPaths?.size}")
                if (selectPaths?.isEmpty() == true) {
                    mViewModel?.mPositionModel?.value?.mCurrentPath?.let { currentPath ->
                        selectPaths = listOf(currentPath)
                    }
                }
                mSelectButton?.postDelayed({
                    mSelectPathDialogListener?.onSelect(mActionCode, selectPaths)
                }, DIALOG_DISMISS_DURATION)
                if (activity !is COUISnackBarUtils.ShowPanel) {
                    mSelectPathDialogInterface?.performDismiss()
                }
            }
        }
    }

    private fun updateButtonView() {
        when (mActionCode) {
            MessageConstant.MSG_EDITOR_COPY -> updateButtonText(com.filemanager.common.R.string.copy_selected_target)
            MessageConstant.MSG_EDITOR_CUT -> updateButtonText(com.filemanager.common.R.string.move_selected_target)
            MessageConstant.MSG_EDITOR_DECOMPRESS -> updateButtonText(com.filemanager.common.R.string.decompress_selected_target)
            MessageConstant.MSG_EDITOR_COMPRESS -> updateButtonText(com.filemanager.common.R.string.compress_selected_target)
            MessageConstant.MSG_SAVE_TO_HERE -> updateButtonText(com.filemanager.common.R.string.save_to_here)
            MessageConstant.MSG_SAVE -> updateButtonText(com.filemanager.common.R.string.save_file)
            MessageConstant.MSG_CLOUD_DRIVE_DOWNLOAD -> updateButtonText(com.filemanager.common.R.string.save_file)
            MessageConstant.MSG_FOLDER_PICKER -> updateButtonText(com.filemanager.common.R.string.safe_decryption)
            MessageConstant.MSG_DOWNLOAD_REMOTE_FILE -> updateButtonText(com.filemanager.common.R.string.remote_file_save_to_here)
            else -> updateButtonText(com.filemanager.common.R.string.dialog_ok)
        }
    }

    override fun onDestroyView() {
        Log.i(TAG, "onDestroyView")
        isDialogShowing = false
        //这里调用反注册关系，将loader和FolderTransformAnimator两者解除关系
        mFolderTransformAnimator.unRegisterNeddSkipAnimator()
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        verticalButtonWrap?.release()
        mPathBar?.setOnPathClickListener(null)
    }

    private fun updateButtonText(strId: Int) {
        mSelectButton?.let {
            it.setText(strId)
            it.contentDescription = resources.getString(strId)
        }
    }

    private fun setNavigationVisible(isVisible: Boolean) {
        mToolbar?.apply {
            if (isVisible) {
                canClose = true
                setNavigationOnClickListener {
                    if (canClose) { // 点击x 直接退出
                        mSelectPathDialogInterface?.performCancel(SELECT_DIALOG_FRAGMENT_TAG)
                        return@setNavigationOnClickListener
                    }
                    if(pressBack().not()) {
                        mSelectPathDialogInterface?.performCancel(SELECT_DIALOG_FRAGMENT_TAG)
                    }
                }
            } else {
                navigationIcon = null
                setNavigationOnClickListener(null)
            }
        }
    }

    override fun initData() {
        initPathBar(mCurrentPath)
        mRecyclerView?.let {
            mLayoutManager = GridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1)
            it.layoutManager = mLayoutManager!!
            it.isNestedScrollingEnabled = true
            it.clipToPadding = false
            it.itemAnimator = mFolderTransformAnimator
            it.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }
            mAdapter?.let { adapter ->
                it.adapter = adapter
                adapter.setOnRecyclerItemClickListener(this@SelectDirPathPanelFragment)
            }
            mToolbar?.post {
                if (isAdded) {
                    setRecyclerViewPadding()
                }
            }
            it.isForceDarkAllowed = false
        }
        if (mIsSetInitLoad) {
            mIsSetInitLoad = false
            onResumeLoadData()
        }
        mToolbar?.let {
            setToolbarNavDrawable(it)
        }
        setSelectButtonVisibility(0)
        if (hasStoragePermission.not()) {
            setPermissionEmptyVisible(View.VISIBLE)
        }
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    fun getSelectDirCurrentPath(): String? {
        return mViewModel?.mPositionModel?.value?.mCurrentPath
    }

    fun setSelectDirCurrentPath(path: String) {
        mViewModel?.mPositionModel?.value?.mCurrentPath = path
    }

    fun isNotEmpty(path: String?): Boolean {
        return path != null && path != ""
    }

    open fun initPathBar(currentPath: String?) {
        mPathBar?.let {
            if (!TextUtils.isEmpty(currentPath)) {
                mViewModel?.initPathHelper(currentPath!!)
                it.setPathHelper(mViewModel?.mPathHelp)
                it.setOnPathClickListener(object : BrowserPathBar.OnPathClickListener {
                    override fun onPathClick(index: Int, path: String?) {
                        mViewModel?.clickPathBar(index)
                    }
                }).setTextFocusChangeListener(object : BrowserPathBar.OnTextFocusColorChangeListener {
                    override fun onFocusChange(currentFocusText: String) {
                        if (mActionCode != MessageConstant.MSG_SAVE && mActionCode != MessageConstant.MSG_CLOUD_DRIVE_DOWNLOAD) {
                            mTitle = currentFocusText
                            if (mViewModel?.mNeedScroll == true) {
                                KtAnimationUtil.showUpdateToolbarTitleWithAnimate(mToolbar, mTitle)
                            } else {
                                mToolbar?.title = mTitle
                            }
                            setToolbarNavDrawable(mToolbar)
                        }
                    }
                }).show()
                it.setCurrentPath(currentPath!!)
            }
            updateHorizontalMargin(it)
        }
    }

    /**
     * 更新左右间距，让其和底部的文件列表对齐
     */
    private fun updateHorizontalMargin(pathBar: BrowserPathBar) {
        val marginRes = if (mActionCode == MessageConstant.MSG_ADD_SHORTCUT_FOLDER) {
            com.filemanager.common.R.dimen.dimen_16dp
        } else {
            com.filemanager.common.R.dimen.default_margin
        }
        val margin = resources.getDimensionPixelOffset(marginRes)
        pathBar.updateHorizontalMargin(margin)
    }

    /**
     * 设置toolbar 导航图标
     * 在根目录第一层级，返回按钮应当改为 “关闭”按钮。在其他层级，返回按钮为箭头
     * 如果传入的path不是根目录，则直接显示"x"，不显示箭头
     */
    @VisibleForTesting
    fun setToolbarNavDrawable(toolbar: Toolbar?) {
        if (mActionCode == MessageConstant.MSG_SAVE) {
            return
        }
        if (mViewModel?.isRootPath(mInitCurrentPath ?: "") != true) {
            toolbar?.menu?.clear()
            if (mActionCode == MessageConstant.MSG_CLOUD_DRIVE_DOWNLOAD) {
                toolbar?.inflateMenu(R.menu.selectdir_download_option)
            } else {
                toolbar?.inflateMenu(R.menu.selectdir_close_option)
            }
            canClose = true
            return
        }
        canClose = if (mViewModel?.isChildPath() == true || mSelectPathDialogInterface?.showMoreStorageFragment() == true) {
            toolbar?.menu?.clear()
            if (mViewModel?.isCurrentDfmRootPath() == true) {
                toolbar?.inflateMenu(R.menu.selectdir_dfm_root_option)
            } else {
                toolbar?.inflateMenu(R.menu.selectdir_back_option)
            }
            false
        } else {
            toolbar?.menu?.clear()
            toolbar?.inflateMenu(R.menu.selectdir_close_option)
            true
        }
    }

    private fun setSelectButtonVisibility(selectCount: Int) {
        if (mViewModel?.isCurrentDfmRootPath() == true) {
            selectButtonWrapper?.visibility = GONE
        } else {
            selectButtonWrapper?.visibility = VISIBLE
        }
        if (mActionCode == MessageConstant.MSG_ADD_SHORTCUT_FOLDER) {
            mSelectButton?.isEnabled = selectCount >= 1
        }
    }

    private fun setRecyclerViewPadding() {
        mRecyclerView?.let {
            val bottomPadding = getRecyclerViewBottomPadding()
            if (it.paddingBottom != bottomPadding) {
                it.setPadding(it.paddingLeft, 0, it.paddingRight, bottomPadding)
                mRecyclerViewFastScroller?.apply {
                    trackMarginBottom = bottomPadding
                }
            }
        }
    }

    protected open fun getRecyclerViewBottomPadding(): Int {
        val extraMargin =
            MyApplication.sAppContext.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.select_path_recycler_view_margin_bottom)
        return if (mViewModel?.isCurrentDfmRootPath() == true) {
            extraMargin
        } else {
            val minHeight =
                MyApplication.sAppContext.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.operation_btn_background_height)
            val measureWidth = selectButtonWrapper?.measuredHeight ?: 0
            (if (measureWidth > 0) measureWidth else minHeight) + extraMargin
        }
    }

    override fun startObserve() {
        mRecyclerView?.post {
            if (isAdded && (mViewModel != null)) {
                mViewModel?.mUiState?.observe(this) { fileUiModel ->
                    Log.d(TAG, "mUiState =" + fileUiModel.mFileList.size + "," + fileUiModel.mSelectedList.size)
                    if (fileUiModel.mFileList.isEmpty() && (mActivity != null) && (rootView != null)) {
                        mFileEmptyController.showFileEmptyView(mActivity!!, rootView!!)
                    } else {
                        mFileEmptyController.hideFileEmptyView()
                    }
                    mAdapter?.let {
                        if (fileUiModel.mFileList is ArrayList<BaseFileBean>) {
                            mFolderTransformAnimator.mIsFolderInAnimation = mViewModel?.mIsFolderIn ?: true
                            it.setData(
                                fileUiModel.mFileList as ArrayList<BaseFileBean>, fileUiModel.mSelectedList, doAnimation = mViewModel?.mNeedScroll
                            )
                        }
                    }
                    if (fileUiModel.mFileList.isEmpty()) {
                        setRecyclerViewPadding()
                        setSelectButtonVisibility(fileUiModel.selectedList.size)
                        mButtonDivider?.alpha = 0f
                    } else {
                        mRecyclerView?.let {
                            it.viewTreeObserver.addOnGlobalLayoutListener(object :
                                ViewTreeObserver.OnGlobalLayoutListener {
                                override fun onGlobalLayout() {
                                    it.viewTreeObserver.removeOnGlobalLayoutListener(this)
                                    mHandler.postDelayed({
                                        setRecyclerViewPadding()
                                        setSelectButtonVisibility(fileUiModel.selectedList.size)
                                        val lastitem = mLayoutManager!!.findLastCompletelyVisibleItemPosition()
                                        val firstItem = mLayoutManager!!.findFirstCompletelyVisibleItemPosition()
                                        var count = -1
                                        if (mAdapter != null) {
                                            count = mAdapter!!.itemCount
                                        }
                                        if (lastitem < count - 1) {
                                            mButtonDivider?.alpha = 1f
                                        } else {
                                            if (firstItem > 0) {
                                                mButtonDivider?.alpha = 1f
                                            } else {
                                                mButtonDivider?.alpha = 0f
                                            }
                                        }
                                    }, DELAYED_TIME)
                                }
                            })
                        }
                    }
                }
                mViewModel?.mPositionModel?.observe(this) { positionModel ->
                    positionModel?.let {
                        mPathBar?.run {
                            if (getCurrentPath() != positionModel.mCurrentPath) {
                                setCurrentPath(positionModel.mCurrentPath)
                            }
                        }
                        mRecyclerView?.run {
                            if (scrollState != RecyclerView.SCROLL_STATE_IDLE) {
                                stopScroll()
                            }
                        }
                        appBarLayout?.run {
                            setExpanded(false)
                            postDelayed({
                                mLayoutManager?.scrollToPositionWithOffset(it.mPosition, it.mOffset)
                                mViewModel?.mPositionModel?.value?.mPosition = 0
                                mViewModel?.mPositionModel?.value?.mOffset = 0
                                mViewModel?.mNeedScroll = false
                            }, FILE_BROWSER_FOLDER_ANIM_DELAY)
                        }
                    }
                }
            }
            activity?.let {
                val bgColor = context?.resources?.getColor(com.support.appcompat.R.color.coui_color_background_top_light)
                mLoadingController = LoadingController(it, this).apply {
                    observe(mViewModel?.mDataLoadState, rootView) {
                        mViewModel?.mSelectDirLoaderCallBack?.isLoadNewPath()?.not() ?: false
                    }
                    bgColor?.let {
                        setBackgroundColor(it)
                    }
                    setDeleyShowTime(LOADING_DELAY_TIME)
                    setShowAinimate(true)
                    setDissapearAnimate(true)
                    setShowLoadingTips(false)
                    //这里蒋LoadingController和FolderTransformAnimator通过接口方式关联起来
                    mFolderTransformAnimator.registerNeedSkipAnimator(this)
                }
            }
        }
    }

    override fun onResumeLoadData() {
        if (!isAdded) {
            return
        }
        mCurrentPath?.let {
            mViewModel?.initLoader(LoaderViewModel.getLoaderController(this), it, mActionCode)
        }
    }

    override fun handleToolbarEnable(permissionEmptyVisible: Int) {
        mToolbar?.menu?.findItem(R.id.action_new_folder)?.isEnabled = permissionEmptyVisible != VISIBLE
    }

    override fun pressBack(): Boolean {
        // 回退到传入的目录时，直接退出
        if (TextUtils.equals(mInitCurrentPath, mViewModel?.mPositionModel?.value?.mCurrentPath)) {
            Log.e(TAG, "pressBack already back to currentPath $mCurrentPath")
            return false
        }
        return mViewModel?.pressBack() ?: false
    }

    override fun onMenuItemClick(item: MenuItem?): Boolean {
        return onMenuItemSelected(item)
    }

    fun onMenuItemSelected(item: MenuItem?): Boolean {
        if ((null == item) || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        when (item.itemId) {
            R.id.action_new_folder -> createFolder()
            R.id.action_close -> {
                if (canClose) { // 点击x 直接退出
                    mSelectPathDialogInterface?.performCancel(SELECT_DIALOG_FRAGMENT_TAG)
                } else if (pressBack().not()) {
                    mSelectPathDialogInterface?.performCancel(SELECT_DIALOG_FRAGMENT_TAG)
                }
            }
            else -> {
                Log.w(TAG, "call onMenuItemSelected with unknown item")
            }
        }
        return true
    }

    protected fun createFolder() {
        mActivity?.let {
            val viewPosition = mLayoutManager?.findFirstVisibleItemPosition() ?: 0
            val offset = mLayoutManager?.findViewByPosition(viewPosition)?.top ?: mRecyclerView?.paddingTop ?: 0
            mViewModel?.createNewFolder(it, mSelectPathDialogInterface, viewPosition, offset)
            OptimizeStatisticsUtil.createFolder(actionCode = mActionCode)
        }
    }

    override fun getViewModel() = mViewModel

    override fun getRecyclerView() = mRecyclerView

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if(mSelectPathDialogInterface == null) {
            mSelectPathDialogInterface = activity?.supportFragmentManager?.findFragmentByTag(
                SelectPathController.SELECT_DIALOG_FRAGMENT_TAG) as? SelectPathDialogInterface
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d(TAG, "onConfigurationChanged newConfig = $newConfig")
        rootView?.postDelayed({
            Log.d(TAG, "onConfigurationChanged setPanelMaxHeight")
            updateHeightAndRequestLayout()
        }, 50)
        mActivity?.let {
            mFileEmptyController.changeEmptyFileIcon()
        }
    }

    override fun onItemClick(view: View, position: Int) {
        mRecyclerView?.let {
            val viewPosition = mLayoutManager?.findFirstVisibleItemPosition() ?: 0
            val offset = mLayoutManager?.findViewByPosition(viewPosition)?.top ?: it.paddingTop
            mViewModel?.onItemClick(mActivity, position, viewPosition, offset - it.paddingTop)
        }
    }

    override fun onItemLongClick(view: View, position: Int) {
    }

    /**
     * SelectShortcutFolderViewHolder item点击事件
     */
    override fun onItemClick(item: ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        mRecyclerView?.let {
            val viewPosition = mLayoutManager?.findFirstVisibleItemPosition() ?: 0
            val offset = mLayoutManager?.findViewByPosition(viewPosition)?.top ?: it.paddingTop
            mViewModel?.onItemClick(mActivity, item.position, viewPosition, offset - it.paddingTop)
        }
        return super.onItemClick(item, e)
    }

    fun updateHeightAndRequestLayout() {
        rootView?.requestLayout()
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
            ?.setPanelBackgroundTintColor(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackgroundElevated))
    }

    open fun onUIConfigChanged(dialog: COUIBottomSheetDialog?) {
    }
}