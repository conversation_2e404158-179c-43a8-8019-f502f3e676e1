package com.oplus.filemanager.simulateclick.engine

import android.accessibilityservice.AccessibilityService
import android.graphics.Path
import android.graphics.Point
import android.graphics.Rect
import android.os.Bundle
import android.view.accessibility.AccessibilityNodeInfo
import com.oplus.filemanager.simulateclick.accessibility.GestureResultAdapter
import com.oplus.filemanager.simulateclick.engine.utils.FindNodeUtil
import com.oplus.filemanager.simulateclick.service.AccessibilityServiceBridge
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * UiObject类的单元测试类
 * 用于测试UiObject中各种UI操作的功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class UiObjectTest {

    // 测试用成员变量
    private lateinit var uiObject: UiObject
    private lateinit var mockEngine: SimulateClickEngine
    private lateinit var mockServiceBridge: AccessibilityServiceBridge
    private lateinit var mockNode: AccessibilityNodeInfo
    private lateinit var mockAction: Action
    private lateinit var mockService: AccessibilityService

    /**
     * 测试前的初始化方法
     * 创建所有mock对象并设置默认行为
     */
    @Before
    fun setUp() {
        mockEngine = mockk(relaxed = true)
        mockServiceBridge = mockk()
        mockNode = mockk()
        mockAction = mockk()
        mockService = mockk()
        
        // 设置mock对象的默认行为
        every { mockEngine.serviceBridge } returns mockServiceBridge
        every { mockNode.viewIdResourceName } returns "test_id"
        every { mockNode.text } returns "test_text"
        every { mockNode.getBoundsInScreen(any()) } answers { 
            val rect = firstArg<Rect>()
            rect.set(0, 0, 100, 100)
            true
        }
        every { mockNode.recycle() } returns Unit
        uiObject = UiObject(mockEngine, mockServiceBridge)
    }

    /**
     * 测试后的清理方法
     * 释放所有mock对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试dealWith方法在node为null时的行为
     * 预期返回false
     */
    @Test
    fun `dealWith should return false when node is null`() = runBlocking {
        val result = uiObject.dealWith(null, mockAction)
        assertFalse(result)
    }

    /**
     * 测试dealWith方法在action为null时的行为
     * 预期返回false
     */
    @Test
    fun `dealWith should return false when action is null`() = runBlocking {
        val result = uiObject.dealWith(mockNode, null)
        assertFalse(result)
    }

    /**
     * 测试click方法在节点可点击时的行为
     * 预期返回true并验证recycle方法被调用
     */
    @Test
    fun `click should return true when node is clickable`() = runBlocking {
        every { mockNode.isClickable } returns true
        every { mockNode.performAction(AccessibilityNodeInfo.ACTION_CLICK) } returns true
        every { mockNode.recycle() } returns Unit
        val result = uiObject.dealWith(mockNode, mockk { every { actionType } returns "click" })
        assertTrue(result)
        verify { mockNode.recycle() }
    }

    /**
     * 测试detectSelectIndex方法在selectIndex无效时的行为
     * 预期返回0并验证相关逻辑
     */
    @Test
    fun `detectSelectIndex should return 0 when selectIndex is invalid`() = runBlocking {
        mockkStatic(FindNodeUtil::class)
        every { FindNodeUtil.findNodesById(any(), any()) } returns emptyList()
        every { mockAction.selectIndex } returns "invalid"
        every { mockAction.childWidgetID } returns "test_id"
        val result = uiObject.dealWith(mockNode, mockk { 
            every { actionType } returns "select"
            every { selectIndex } returns "invalid"
            every { childWidgetID } returns "test_id"
        })
        assertEquals(false, result)
    }

    /**
     * 测试scrollForward方法的行为
     * 预期返回performAction的结果并验证recycle方法被调用
     */
    @Test
    fun `scrollForward should return performAction result`() = runBlocking {
        every { mockNode.performAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD) } returns true
        every { mockNode.recycle() } returns Unit
        val result = uiObject.dealWith(mockNode, mockk { every { actionType } returns "scroll_forward" })
        assertTrue(result)
        verify { mockNode.recycle() }
    }

    /**
     * 测试getText方法在node为null时的行为
     * 预期返回false
     */
    @Test
    fun `getText should return false when node is null`() = runBlocking {
        val result = uiObject.dealWith(null, mockk { every { actionType } returns "get_text" })
        assertFalse(result)
    }

    /**
     * 测试clickGesture方法在service为null时的行为
     * 预期返回false
     */
    @Test
    fun `clickGesture should return false when service is null`() = runBlocking {
        every { mockServiceBridge.pickService() } returns null
        val result = uiObject.dealWith(mockNode, mockk { 
            every { actionType } returns "click_gesture"
            every { clickGestureDelay() } returns 0L
            every { handleTime() } returns 1000L
            every { clickGestureWait() } returns 0L
        })
        assertFalse(result)
    }
}