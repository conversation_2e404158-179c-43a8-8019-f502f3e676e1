<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="?attr/couiColorBackgroundElevated"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/body"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:baselineAligned="false"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/anim_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginTop="@dimen/coui_progress_dialog_padding_top"
            android:orientation="vertical">

            <com.oplus.anim.EffectiveAnimationView
                android:id="@+id/progress_anim"
                android:layout_width="@dimen/dimen_80dp"
                android:layout_height="@dimen/dimen_80dp" />

            <TextView
                android:id="@+id/dialog_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_12dp"
                android:text="@string/security_chip_is_encrypting_file"
                android:textAppearance="@style/couiTextHeadlineXS"
                android:textColor="@color/dialog_black_85_percent"/>

            <TextView
                android:id="@+id/dialog_sub_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:forceDarkAllowed="false"
                android:paddingBottom="@dimen/coui_progress_cancelable_dialog_padding_bottom"
                android:textAppearance="@style/couiTextBodyM"
                android:textColor="@color/dialog_black_85_percent"/>
        </LinearLayout>

        <TextView
            android:id="@+id/dialog_cancel"
            style="?attr/couiTextButtonM"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:gravity="center"
            android:text="@string/alert_dialog_no"
            android:textColor="?attr/couiColorPrimaryTextOnPopup" />
    </LinearLayout>

</FrameLayout>