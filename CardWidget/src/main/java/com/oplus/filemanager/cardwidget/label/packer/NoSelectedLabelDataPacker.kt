/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : NoSelectedLabelDataPacker
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/7 11:41
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.packer

import android.content.Context
import com.filemanager.common.utils.Log
import com.oplus.cardwidget.domain.pack.BaseDataPack
import com.oplus.filemanager.cardwidget.util.StartActivityUtil
import com.oplus.filemanager.cardwidget.util.TextUtil
import com.oplus.smartenginehelper.dsl.DSLCoder

class NoSelectedLabelDataPacker(
    private val context: Context,
    private val widgetCode: String
) : BaseDataPack() {

    override fun onPack(coder: DSLCoder): Boolean {
        Log.d(DATA_PACK_TAG, "onPack -> coder = $coder")
        TextUtil.setTextSize(coder, context, ID_CARD_TIPS, TextUtil.TEXT_SIZE_16)
        TextUtil.setTextSize(coder, context, ID_CARD_LINK, TextUtil.TEXT_SIZE_14)
        coder.setTextViewText(ID_CARD_TIPS, NO_SELECTED_LABEL_TIPS_STRING)
        coder.setTextViewText(ID_CARD_LINK, NO_SELECTED_LABEL_LINKS_STRING)
        val start = StartActivityUtil().startLabelListDialog(widgetCode)
        coder.setOnClick(ID_CARD_CONTAINER, start)
        coder.setOnClick(ID_CARD_LINK, start)
        return true
    }

    companion object {
        private const val DATA_PACK_TAG = "NoSelectedLabelDataPacker"
        private const val ID_CARD_CONTAINER = "container"
        private const val ID_CARD_TIPS = "tips"
        private const val ID_CARD_LINK = "links"
        private const val NO_SELECTED_LABEL_TIPS_STRING = "@string/unselected_label"
        private const val NO_SELECTED_LABEL_LINKS_STRING = "@string/unselected_label_card_summary"
    }
}