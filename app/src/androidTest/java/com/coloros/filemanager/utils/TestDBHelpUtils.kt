/*********************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : TestDBHelpUtils
 * Description :
 * Version     : 1.0
 * Date        : 2023/08/01
 * Author      : W9002848
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * W9002848        2023/08/04       1.0      create
 **********************************************************************/
package com.coloros.filemanager.utils

import android.content.ContentValues
import android.content.Context
import android.net.Uri
import com.coloros.filemanager.utils.TestConstants.FILE_OPEN_TIME_KEY
import com.coloros.filemanager.utils.TestConstants.FILE_OPEN_TIME_METHOD
import com.coloros.filemanager.utils.TestConstants.FILE_OPEN_TIME_URI
import org.junit.Assert

object TestDBHelpUtils {
    private val TAG = TestDBHelpUtils::class.java.simpleName

    /**
     * 文件管理提供打开文件时间接口
     * 需求：文件管理对所有的文件增加了一个最近打开时间，提供给侧边栏排序
     *   * 1.文件的默认最近打开时间为文件的创建时间
     *   * 2.文件在文管里面被打开过，刷新最近打开时间为当前时间
     *   * 3.文件重命名，移动时，不更新最近打开时间，文件复制，更新当前时间为最近打开时间
     * 文件删除恢复后，更新最近打开时间为当前恢复的时间
     * 文件管理本地数据库
     * 文件管理在数据里新建了一张表，表名：file_open_time，用户保存最近打开时间的数据
     *   * 数据库字段：ID：_id、文件路径：file_path
     *   * 文件打开时间：file_open_time
     *   * 文件创建时间：file_create_time
     *   @param context 被测试应用上下文
     */
    @JvmStatic
     fun getRecentlyOpenListData(context: Context): ArrayList<ContentValues>? {
        val uri = Uri.parse(FILE_OPEN_TIME_URI)
        //return bundle
        val bundle = context.contentResolver.call(uri, FILE_OPEN_TIME_METHOD, null, null)
        Assert.assertNotNull("The bundle can't be empty! If is empty, the \"implementation project(':FileOpenTime')\" code is annotated!", bundle)
        //get bundle list data
        return (bundle?.getParcelableArrayList<ContentValues>(FILE_OPEN_TIME_KEY, ContentValues::class.java))
    }
}