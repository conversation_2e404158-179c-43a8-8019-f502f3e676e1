/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : COUIToolTipsViewTest
 ** Description : COUIToolTipsView Unit Test
 ** Version     : 1.0
 ** Date        : 2024/04/29 14:05
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/04/29       1.0      create
 ***********************************************************************/
package com.filemanager.common.view

import android.app.Activity
import androidx.fragment.app.FragmentActivity
import io.mockk.mockk
import org.junit.After
import org.junit.Assert
import org.junit.Test

class COUIToolTipsViewTest {

    @After
    fun teardown() {
        COUIToolTipsView.activityList.clear()
    }

    @Test
    fun should_add_when_pushActivity() {
        Assert.assertEquals(0, COUIToolTipsView.activityList.size)

        val activity = mockk<Activity>()
        COUIToolTipsView.pushActivity(activity)
        Assert.assertEquals(1, COUIToolTipsView.activityList.size)
    }

    @Test
    fun should_remove_when_pollActivity() {
        Assert.assertEquals(0, COUIToolTipsView.activityList.size)

        val activity = mockk<Activity>()
        COUIToolTipsView.pushActivity(activity)
        Assert.assertEquals(1, COUIToolTipsView.activityList.size)

        COUIToolTipsView.pollActivity(activity)
        Assert.assertEquals(0, COUIToolTipsView.activityList.size)
    }

    @Test
    fun should_return_boolean_when_isShowTips() {
        val activity1 = mockk<Activity>()
        Assert.assertFalse(COUIToolTipsView.isShowTips(activity1))

        val activity2 = mockk<FragmentActivity>()
        COUIToolTipsView.pushActivity(activity1)
        COUIToolTipsView.pushActivity(activity2)

        Assert.assertTrue(COUIToolTipsView.isShowTips(activity1))
    }
}