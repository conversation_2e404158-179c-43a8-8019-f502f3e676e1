package com.android.documentsui.base;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import android.os.Parcel;
import android.text.TextUtils;
import java.net.ProtocolException;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;

@RunWith(RobolectricTestRunner.class)
@Config(sdk = 29)
public class RootInfoTest {

    private RootInfo rootInfo;

    @Before
    public void setUp() {
        rootInfo = new RootInfo();
    }

    @Test
    public void testReset() {
        rootInfo.reset();
        assertEquals(UserId.UNSPECIFIED_USER, rootInfo.userId);
        assertNull(rootInfo.authority);
        assertNull(rootInfo.rootId);
        assertEquals(0, rootInfo.flags);
        assertEquals(0, rootInfo.icon);
        assertNull(rootInfo.title);
        assertNull(rootInfo.summary);
        assertNull(rootInfo.documentId);
        assertEquals(-1, rootInfo.availableBytes);
        assertNull(rootInfo.mimeTypes);
        assertFalse(rootInfo.ejecting);
        assertNull(rootInfo.queryArgs);
        assertNull(rootInfo.derivedMimeTypes);
        assertEquals(0, rootInfo.derivedIcon);
        assertEquals(RootInfo.TYPE_RECENTS, rootInfo.derivedType);
    }

    @Test
    public void testReadWrite_VersionUserId() throws IOException {
        // Prepare test data
        rootInfo.userId = UserId.CURRENT_USER;
        rootInfo.queryArgs = "query";
        rootInfo.authority = "authority";
        rootInfo.rootId = "rootId";
        rootInfo.flags = 1;
        rootInfo.icon = 2;
        rootInfo.title = "title";
        rootInfo.summary = "summary";
        rootInfo.documentId = "docId";
        rootInfo.availableBytes = 1000L;
        rootInfo.mimeTypes = "mimeTypes";

        // Write to output stream
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        DataOutputStream out = new DataOutputStream(byteOut);
        rootInfo.write(out);

        // Read from input stream
        ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
        DataInputStream in = new DataInputStream(byteIn);
        RootInfo newRoot = new RootInfo();
        newRoot.read(in);

        // Verify
        assertEquals(rootInfo.userId, newRoot.userId);
        assertEquals(rootInfo.queryArgs, newRoot.queryArgs);
        assertEquals(rootInfo.authority, newRoot.authority);
        assertEquals(rootInfo.rootId, newRoot.rootId);
        assertEquals(rootInfo.flags, newRoot.flags);
        assertEquals(rootInfo.icon, newRoot.icon);
        assertEquals(rootInfo.title, newRoot.title);
        assertEquals(rootInfo.summary, newRoot.summary);
        assertEquals(rootInfo.documentId, newRoot.documentId);
        assertEquals(rootInfo.availableBytes, newRoot.availableBytes);
        assertEquals(rootInfo.mimeTypes, newRoot.mimeTypes);
    }

    @Test(expected = ProtocolException.class)
    public void testRead_InvalidVersion() throws IOException {
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        DataOutputStream out = new DataOutputStream(byteOut);
        out.writeInt(999); // Invalid version
        ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
        DataInputStream in = new DataInputStream(byteIn);
        rootInfo.read(in);
    }

    @Test
    public void testParcelable() {
        // Prepare test data
        rootInfo.userId = UserId.CURRENT_USER;
        rootInfo.authority = "authority";
        rootInfo.rootId = "rootId";

        // Write to parcel
        Parcel parcel = Parcel.obtain();
        rootInfo.writeToParcel(parcel, 0);

        // Read from parcel
        parcel.setDataPosition(0);
        RootInfo newRoot = RootInfo.CREATOR.createFromParcel(parcel);

        // Verify
        assertEquals(rootInfo.userId, newRoot.userId);
        assertEquals(rootInfo.authority, newRoot.authority);
        assertEquals(rootInfo.rootId, newRoot.rootId);
    }

    @Test
    public void testCopyRootInfo() {
        // Prepare test data
        rootInfo.userId = UserId.CURRENT_USER;
        rootInfo.authority = "authority";
        rootInfo.rootId = "rootId";
        rootInfo.flags = 1;
        rootInfo.icon = 2;
        rootInfo.title = "title";
        rootInfo.summary = "summary";
        rootInfo.documentId = "docId";
        rootInfo.availableBytes = 1000L;
        rootInfo.mimeTypes = "mimeTypes";
        rootInfo.queryArgs = "query";
        rootInfo.derivedType = RootInfo.TYPE_IMAGES;
        rootInfo.derivedIcon = 3;
        rootInfo.derivedMimeTypes = new String[]{"image/*"};

        RootInfo copy = RootInfo.copyRootInfo(rootInfo);

        assertEquals(rootInfo.userId, copy.userId);
        assertEquals(rootInfo.authority, copy.authority);
        assertEquals(rootInfo.rootId, copy.rootId);
        assertEquals(rootInfo.flags, copy.flags);
        assertEquals(rootInfo.icon, copy.icon);
        assertEquals(rootInfo.title, copy.title);
        assertEquals(rootInfo.summary, copy.summary);
        assertEquals(rootInfo.documentId, copy.documentId);
        assertEquals(rootInfo.availableBytes, copy.availableBytes);
        assertEquals(rootInfo.mimeTypes, copy.mimeTypes);
        assertEquals(rootInfo.queryArgs, copy.queryArgs);
        assertEquals(rootInfo.derivedType, copy.derivedType);
        assertEquals(rootInfo.derivedIcon, copy.derivedIcon);
        assertArrayEquals(rootInfo.derivedMimeTypes, copy.derivedMimeTypes);
    }

    @Test
    public void testEquals() {
        RootInfo root1 = new RootInfo();
        root1.userId = UserId.CURRENT_USER;
        root1.authority = "authority";
        root1.rootId = "rootId";

        RootInfo root2 = new RootInfo();
        root2.userId = UserId.CURRENT_USER;
        root2.authority = "authority";
        root2.rootId = "rootId";

        RootInfo root3 = new RootInfo();
        root3.userId = UserId.UNSPECIFIED_USER;
        root3.authority = "other";
        root3.rootId = "other";

        assertTrue(root1.equals(root2));
        assertFalse(root1.equals(root3));
        assertFalse(root1.equals(null));
        assertTrue(root1.equals(root1));
    }

    @Test
    public void testHashCode() {
        RootInfo root1 = new RootInfo();
        root1.userId = UserId.CURRENT_USER;
        root1.authority = "authority";
        root1.rootId = "rootId";

        RootInfo root2 = new RootInfo();
        root2.userId = UserId.CURRENT_USER;
        root2.authority = "authority";
        root2.rootId = "rootId";

        assertEquals(root1.hashCode(), root2.hashCode());
    }

    @Test
    public void testCompareTo() {
        RootInfo root1 = new RootInfo();
        root1.derivedType = RootInfo.TYPE_IMAGES;
        root1.title = "A";
        root1.summary = "A";

        RootInfo root2 = new RootInfo();
        root2.derivedType = RootInfo.TYPE_VIDEO;
        root2.title = "B";
        root2.summary = "B";

        RootInfo root3 = new RootInfo();
        root3.derivedType = RootInfo.TYPE_IMAGES;
        root3.title = "B";
        root3.summary = "B";

        assertTrue(root1.compareTo(root2) < 0); // TYPE_IMAGES < TYPE_VIDEO
        assertTrue(root1.compareTo(root3) < 0); // "A" < "B"
    }

    @Test
    public void testCompareToIgnoreCaseNullable() {
        assertEquals(0, RootInfo.compareToIgnoreCaseNullable(null, null));
        assertTrue(RootInfo.compareToIgnoreCaseNullable(null, "a") < 0);
        assertTrue(RootInfo.compareToIgnoreCaseNullable("a", null) > 0);
        assertTrue(RootInfo.compareToIgnoreCaseNullable("a", "b") < 0);
    }

    @Test
    public void testGetDirectoryString() {
        rootInfo.title = "title";
        rootInfo.summary = "summary";
        assertEquals("summary", rootInfo.getDirectoryString());

        rootInfo.summary = null;
        assertEquals("title", rootInfo.getDirectoryString());
    }

    @Test
    public void testToDebugString() {
        rootInfo.title = "title";
        rootInfo.summary = "summary";
        assertEquals("\"title (summary)\" @ ", rootInfo.toDebugString());

        rootInfo.summary = null;
        assertEquals("\"title\" @ ", rootInfo.toDebugString());
    }
}