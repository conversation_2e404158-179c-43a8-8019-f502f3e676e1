<?xml version="1.0" encoding="utf-8"?>
<com.filemanager.common.view.HoverAwareConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/normal_list_item_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:forceDarkAllowed="false"
    android:background="@drawable/select_list_item_background_selector"
    android:minHeight="?android:attr/listPreferredItemHeightSmall">

    <RelativeLayout
        android:id="@+id/file_list_item_icon_container"
        android:layout_width="@dimen/file_list_frame_size"
        android:layout_height="@dimen/file_list_frame_size"
        android:layout_marginStart="@dimen/file_list_margin"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <com.filemanager.common.view.FileThumbView
            android:id="@+id/file_list_item_icon"
            android:layout_width="@dimen/file_list_video_image_size"
            android:layout_height="@dimen/file_list_video_image_size"
            android:layout_centerInParent="true"
            android:forceDarkAllowed="false"/>
    </RelativeLayout>

    <TextView
        android:id="@+id/file_duration_tv"
        android:layout_width="@dimen/dimen_36dp"
        android:layout_height="wrap_content"
        android:background="@drawable/item_video_duration_view_bg"
        android:gravity="center"
        android:textColor="@color/coui_color_label_on_color"
        android:fontFamily="sans-serif-medium"
        android:textSize="@dimen/duration_text_size"
        android:visibility="gone"
        android:layout_marginBottom="@dimen/dimen_4dp"
        app:layout_constraintBottom_toBottomOf="@+id/file_list_item_icon_container"
        app:layout_constraintEnd_toEndOf="@+id/file_list_item_icon_container"
        app:layout_constraintStart_toStartOf="@+id/file_list_item_icon_container" />

    <com.filemanager.common.view.TextViewSnippet
        android:id="@+id/file_list_item_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/file_list_item_info_margin"
        android:layout_marginTop="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginEnd="@dimen/file_list_margin"
        android:adjustViewBounds="true"
        android:maxLines="2"
        android:textAppearance="@style/couiTextHeadlineXS"
        android:textColor="?attr/couiColorLabelPrimary"
        android:includeFontPadding="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/file_list_item_icon_container"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/file_list_item_detail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/file_list_item_info_margin"
        android:layout_marginEnd="@dimen/file_list_margin"
        android:layout_marginTop="@dimen/file_list_item_detail_margin_top"
        android:layout_marginBottom="@dimen/file_list_right_layout_margin_vertical"
        android:adjustViewBounds="true"
        android:ellipsize="marquee"
        android:singleLine="true"
        android:textAppearance="@style/couiTextBodyXS"
        android:textColor="?attr/couiColorLabelSecondary"
        android:includeFontPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/file_list_item_icon_container"
        app:layout_constraintTop_toBottomOf="@+id/file_list_item_title" />

    <com.coui.appcompat.checkbox.COUICheckBox
        android:id="@+id/listview_scrollchoice_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="false"
        android:focusable="false"
        android:background="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/divider_line"
        android:layout_width="0dp"
        android:layout_height="0.33dp"
        android:background="?attr/couiColorDivider"
        app:layout_constraintStart_toStartOf="@id/file_list_item_title"
        app:layout_constraintEnd_toEndOf="@+id/file_list_item_title"
        app:layout_constraintBottom_toBottomOf="parent" />
</com.filemanager.common.view.HoverAwareConstraintLayout>