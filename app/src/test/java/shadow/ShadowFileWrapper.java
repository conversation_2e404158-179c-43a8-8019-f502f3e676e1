/***************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * Description:Unit test shadow class
 * Version: 1.0
 * Date : 2019/7/22
 * <EMAIL>
 *
 * -------------------Revision History: ----------------------
 *     <author>    <data>     <version >     <desc>
 *   lanjiabo    2019/7/22   1.0            build this moudle
 ****************************************************************/
package shadow;

import com.filemanager.common.helper.FileWrapper;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.util.ArrayList;

@Implements(FileWrapper.class)
public class ShadowFileWrapper {
    private static String sPath;

    private static boolean sIsDirectory;

    private static boolean sIsExists;

    private static ArrayList<FileWrapper> sArrayLists;

    public static void setPath(String path) {
        sPath = path;
    }

    public static void setIsDirectory(boolean isDirectory) {
        sIsDirectory = isDirectory;
    }

    public static void setIsExists(boolean isExists) {
        sIsExists = isExists;
    }

    public static void setsArrayLists(ArrayList<FileWrapper> arrayLists) {
        sArrayLists = arrayLists;
    }

    @Implementation
    public boolean exists() {
        return sIsExists;
    }

    @Implementation
    public boolean isDirectory() {
        return sIsDirectory;
    }

    @Implementation
    public long length() {
        return sPath.length();
    }

    @Implementation
    public ArrayList<FileWrapper> listArrayFiles(boolean flag) {
        return sArrayLists;
    }
}
