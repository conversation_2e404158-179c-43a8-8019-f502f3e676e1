/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - YoZoDocThumbnailLoader.kt
 * Description:
 *     Load doc thumbnail with YoZo thumbnail service
 *
 * Version: 1.0
 * Date: 2024-07-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Bix<PERSON><PERSON>.<EMAIL>    2024-07-29   1.0    Create this module
 *********************************************************************************/
package com.filemanager.thumbnail.doc.yozo

import android.content.Context
import android.net.Uri
import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import androidx.annotation.VisibleForTesting
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.doc.DocThumbnail
import com.filemanager.thumbnail.doc.IDocThumbnailLoader
import com.filemanager.thumbnail.doc.IDocThumbnailCallback
import com.filemanager.thumbnail.doc.IYoZoDocThumbnailCallback
import com.filemanager.thumbnail.utils.Log
import com.filemanager.thumbnail.utils.logInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.util.Queue
import java.util.concurrent.CancellationException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentLinkedQueue

internal class YoZoDocThumbnailLoader(
    private val context: Context,
    private val minIdleServicesNum: Int = ThumbnailManager.YoZoDocConfigs.minIdleThumbnailService,
    occupyThumbServices: Set<Int> = ThumbnailManager.YoZoDocConfigs.occupyThumbServices
) : IDocThumbnailLoader, Handler.Callback {

    override val shallSampleBitmap: Boolean = true

    /**
     * 当前独占的永中缩略图服务总表
     */
    private val allOccupyServices =
        YoZoDocThumbService.getOccupyThumbServices(context, occupyThumbServices)

    /**
     * 当前空闲的永中缩略图服务队列，从该队列调度服务并执行缩略图加载请求。
     * 因为[noEnoughIdleService]和poll操作需要配合执行，该队列使用时需加锁。
     * 同时因为已经加锁，该队列本身不再采用线程安全的类型。
     */
    @VisibleForTesting
    internal val idleServiceQueue: Queue<YoZoDocThumbService> =
        java.util.ArrayDeque(allOccupyServices)

    /**
     * 当前等待调度执行的缩略图请求队列，所有缩略图请求统一入队后等待排队调度执行。
     */
    @VisibleForTesting
    internal val loadRequestQueue = ConcurrentLinkedQueue<LoadRequest>()

    /**
     * 通过[Uri]查找[LoadRequest]，用于取消时快速定位对应的请求
     */
    @VisibleForTesting
    internal val requestIndexMap = ConcurrentHashMap<DocThumbnail, LoadRequest>()

    private val digestCache = YoZoDocThumbnailDigestCache()
    private val loaderScope = CoroutineScope(Dispatchers.IO)
    private val scheduleThread by lazy { HandlerThread(TAG).apply { start() } }
    private val scheduleHandler by lazy { Handler(scheduleThread.looper, this) }
    private val isServiceSupport by lazy {
        val resolveInfo = context.packageManager
            .queryIntentServices(allOccupyServices.first().getServiceIntent(context.packageName), 0)
        resolveInfo.isNotEmpty()
    }

    @Volatile
    private var isReleased = false

    override fun isSupported(): Boolean = isServiceSupport

    override fun loadThumbnail(
        docThumbnail: DocThumbnail?,
        width: Int,
        height: Int,
        snapshotCallback: IDocThumbnailCallback
    ) {
        if (isReleased) {
            return
        }
        if (docThumbnail?.uri == null) {
            snapshotCallback.onLoadFailed(IllegalArgumentException("uri cannot be null"))
            return
        }
        val loadRequest = LoadRequest(
            docThumbnail = docThumbnail,
            width = width,
            height = height,
            snapshotCallback = snapshotCallback
        )
        Log.d(TAG, "loadThumbnail: schedule, doc=${loadRequest.docUri.logInfo()}, size=${width}x$height")
        offerRequest(loadRequest)
        scheduleExecuteRequest()
    }

    @VisibleForTesting
    internal fun offerRequest(loadRequest: LoadRequest) {
        requestIndexMap[loadRequest.docThumbnail] = loadRequest
        loadRequestQueue.offer(loadRequest)
    }

    /**
     * 永中缩略图服务的调度执行和释放均通过handler转派到专用的请求分派线程上执行以减少时序冲突。
     */
    @VisibleForTesting
    internal fun scheduleExecuteRequest() {
        if (isReleased) {
            return
        }
        scheduleHandler.sendEmptyMessage(MSG_SCHEDULE_EXECUTE)
    }

    /**
     * @see scheduleExecuteRequest
     */
    private fun releaseThumbnailService(thumbService: YoZoDocThumbService) {
        if (isReleased) {
            return
        }
        Message.obtain(scheduleHandler, MSG_RELEASE_SERVICE, thumbService).sendToTarget()
    }

    override fun handleMessage(msg: Message): Boolean {
        if (isReleased) {
            return false
        }
        Log.d(TAG, "handleMessage: what=${msg.what}")
        when (msg.what) {
            MSG_SCHEDULE_EXECUTE -> onPollAndExecuteRequest()
            MSG_RELEASE_SERVICE -> onReleaseThumbnailService(msg.obj)
        }
        return true
    }

    @VisibleForTesting
    internal fun onPollAndExecuteRequest() {
        val (loadRequest, thumbService) = synchronized(idleServiceQueue) {
            if (noEnoughIdleService()) {
                Log.d(TAG, "onPollAndExecuteRequest: pending idle")
                return@onPollAndExecuteRequest
            }
            val loadRequest = loadRequestQueue.pollNotCancelled() ?: return@onPollAndExecuteRequest
            val thumbService = idleServiceQueue.poll() ?: run {
                Log.e(TAG, "onPollAndExecuteRequest: ERROR! no idle service")
                loadRequest.invalidRetry()
                offerRequest(loadRequest)
                return@onPollAndExecuteRequest
            }
            Log.d(TAG, "onPollAndExecuteRequest: busy: $thumbService")
            return@synchronized loadRequest to thumbService
        }
        executeWithService(loadRequest, thumbService)
    }

    /**
     * 根据设定的最小空闲缩略图服务数量，计算是否有足够的空闲服务用于调度执行。
     * 若设定的最小数量大于等于独占服务的总数量，则设定的最小数量无效。
     * 该方法会配合poll操作一起使用，必须在[idleServiceQueue]已加锁的情况下和poll操作在同一个锁的范围内调用。
     */
    private fun noEnoughIdleService(): Boolean {
        val minNum = if (allOccupyServices.size > minIdleServicesNum) minIdleServicesNum else 0
        return idleServiceQueue.size <= minNum
    }

    @VisibleForTesting
    internal fun executeWithService(loadRequest: LoadRequest, thumbService: YoZoDocThumbService) {
        loaderScope.launch {
            if (isReleased) {
                return@launch
            }
            loadRequest.executeLoadRequest(thumbService)
        }
    }

    @VisibleForTesting
    internal fun LoadRequest.executeLoadRequest(
        thumbService: YoZoDocThumbService
    ) = kotlin.runCatching {
        if (isCancelled || isFinished) {
            callbackCancelled()
            releaseThumbnailService(thumbService)
            return@runCatching
        }
        Log.d(TAG, "executeLoadRequest: loadThumb, doc=${docUri.logInfo()}")
        val cachedThumbUri = digestCache.pickThumbnailCache(
            context, docThumbnail, LoadDocThumbServiceConnection
                .isExportOfficeThumbnailFirstPage(width, context)
        )
        if (cachedThumbUri != null) {
            Log.d(TAG, "executeLoadRequest: useDigestCache, thumb=${cachedThumbUri.logInfo()}")
            callbackSuccess(cachedThumbUri)
            releaseThumbnailService(thumbService)
            return@runCatching
        }
        thumbService.loadThumb(
            context,
            scheduleThread,
            docThumbnail,
            LoadRequestCallback(thumbService, this@executeLoadRequest),
            width
        )
    }.onFailure {
        Log.e(TAG, "executeLoadRequest: ERROR! doc=${docUri.logInfo()}, err=$it")
        callbackFailed(it)
        releaseThumbnailService(thumbService)
    }

    @VisibleForTesting
    internal fun onReleaseThumbnailService(obj: Any?) {
        val thumbService = obj as? YoZoDocThumbService
            ?: throw IllegalArgumentException("$TAG: no YoZoDocThumbService, obj=$obj")
        synchronized(idleServiceQueue) {
            if (!idleServiceQueue.contains(thumbService)) {
                Log.d(TAG, "onReleaseThumbnailService: idle: $thumbService")
                idleServiceQueue.offer(thumbService)
            }
        }
        scheduleExecuteRequest()
    }

    /**
     * 因LinkedQueue的遍历查找开销较大，而Glide滚动列表加载过程中可能会有大量的取消行为。
     * 故通过HashSet记录被取消的加载请求，在其从请求队列出队时再进行匹配过滤。
     */
    private fun Queue<LoadRequest>.pollNotCancelled(): LoadRequest? {
        var nextRequest: LoadRequest? = null
        do {
            nextRequest = poll()
        } while (nextRequest != null && (nextRequest.isCancelled || !nextRequest.checkRetry()))
        return nextRequest
    }

    private fun LoadRequest.checkRetry(): Boolean {
        val canRetry = checkCanRetry()
        if (!canRetry) {
            Log.d(TAG, "checkRetry: abandon since retry limit, doc=${docUri.logInfo()}")
            callbackFailed(IllegalStateException("$TAG: too many retry times for doc ${docUri.logInfo()}"))
        }
        return canRetry
    }

    /**
     * @see pollNotCancelled
     */
    override fun cancel(thumbModel: DocThumbnail) {
        if (isReleased) {
            return
        }
        val docUri = thumbModel.uri
        val request = requestIndexMap[thumbModel] ?: return
        Log.d(TAG, "cancel: ${docUri.logInfo()}")
        request.isCancelled = true
        allOccupyServices.forEach {
            if (it.cancel(docUri)) {
                return@cancel
            }
        }
        request.callbackCancelled()
    }

    override fun release() {
        if (isReleased) {
            return
        }
        Log.d(TAG, "release")
        isReleased = true
        scheduleHandler.removeCallbacksAndMessages(null)
        scheduleThread.quitSafely()
        loaderScope.cancel("$TAG: release")
        allOccupyServices.forEach { it.cancel() }
        synchronized(idleServiceQueue) {
            idleServiceQueue.clear()
        }
        loadRequestQueue.clear()
        requestIndexMap.clear()
    }

    /**
     * [IDocThumbnailCallback]的回调在另外的协程域上执行以避免阻塞缩略图服务的回调binder线程或请求分派线程。
     */
    private fun LoadRequest.callbackSuccess(uri: Uri) {
        if (isReleased || isFinished) {
            return
        }
        isFinished = true
        loaderScope.launch {
            if (isReleased) {
                return@launch
            }
            snapshotCallback.onDataReady(uri)
            requestIndexMap.remove(docThumbnail)
        }
    }

    /**
     * @see [callbackSuccess]
     */
    private fun LoadRequest.callbackFailed(e: Throwable) {
        if (isReleased || isFinished) {
            return
        }
        isFinished = true
        loaderScope.launch {
            if (isReleased) {
                return@launch
            }
            snapshotCallback.onLoadFailed(e)
            requestIndexMap.remove(docThumbnail)
        }
    }

    @VisibleForTesting
    internal fun LoadRequest.callbackCancelled() =
        callbackFailed(CancellationException("$TAG: cancelled"))

    private companion object {
        private const val TAG = "YoZoDocThumbnailLoader"
        private const val MSG_SCHEDULE_EXECUTE = 101
        private const val MSG_RELEASE_SERVICE = 102
    }

    @VisibleForTesting
    internal data class LoadRequest(
        val docThumbnail: DocThumbnail,
        val width: Int,
        val height: Int,
        val snapshotCallback: IDocThumbnailCallback
    ) {
        val docUri: Uri
            get() = docThumbnail.uri

        @Volatile
        var isCancelled: Boolean = false

        @Volatile
        var isFinished: Boolean = false

        @VisibleForTesting
        internal var retryCount: Int = -1

        fun invalidRetry() {
            retryCount--
            if (retryCount < -1) {
                retryCount = -1
            }
        }

        fun checkCanRetry(): Boolean {
            retryCount++
            return (retryCount <= ThumbnailManager.YoZoDocConfigs.maxRetryCount)
        }
    }

    private inner class LoadRequestCallback(
        private val thumbService: YoZoDocThumbService,
        private val loadRequest: LoadRequest
    ) : IYoZoDocThumbnailCallback {
        override fun onDataReady(uri: Uri) = loadRequest.run {
            if (isReleased || isFinished) {
                return@run
            }
            if (isCancelled) {
                callbackCancelled()
                return@run
            }
            Log.d(TAG, "executeLoadRequest: onDataReady, doc=${docUri.logInfo()}, thumb=${uri.logInfo()}")
            callbackSuccess(uri)
            loaderScope.launch {
                digestCache.putThumbnailCache(
                    context, docThumbnail, uri, LoadDocThumbServiceConnection
                        .isExportOfficeThumbnailFirstPage(width, context)
                )
            }
        }

        override fun onLoadFailed(e: Throwable) = loadRequest.run {
            if (isReleased || isFinished) {
                return
            }
            if (isCancelled) {
                callbackCancelled()
                return
            }
            Log.e(TAG, "executeLoadRequest: onLoadFailed, doc=${docUri.logInfo()}, err=$e")
            callbackFailed(e)
        }

        override fun onCancel() = loadRequest.run {
            if (isReleased || isFinished) {
                return
            }
            Log.e(TAG, "executeLoadRequest: onCancel, doc=${docUri.logInfo()}")
            callbackCancelled()
        }

        /**
         * 永中缩略图服务处理超时被强制取消，则将未完成的缩略图请求重新入列并排队调度。
         */
        override fun onConnectionTimeout() = loadRequest.run {
            if (isReleased || isFinished) {
                return
            }
            if (isCancelled) {
                callbackCancelled()
                return
            }
            Log.e(TAG, "executeLoadRequest: onConnectionTimeout, doc=${docUri.logInfo()}")
            offerRequest(loadRequest)
            scheduleExecuteRequest()
        }

        /**
         * 永中缩略图服务finish时将所用服务释放回服务队列供新的请求调度使用。
         * 加载完成、被取消、超时等情况最终均会调用finish。
         */
        override fun onFinish() {
            releaseThumbnailService(thumbService)
        }
    }
}