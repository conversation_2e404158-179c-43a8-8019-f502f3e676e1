/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForFileService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.di

import androidx.annotation.Keep
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.fileservice.FileServiceApi
import org.koin.dsl.module

@Keep
class AutoDIForFileService {

    val fileServiceModule = module {
        single<IFileService>(createdAtStart = true) {
            FileServiceApi
        }
    }
}