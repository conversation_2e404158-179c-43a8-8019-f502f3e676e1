plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace "com.oplus.fileopentime"
}

dependencies {
    compileOnly(libs.oplus.addon.sdk) { artifact { type = "aar" } }

    implementation libs.androidx.appcompat
    implementation libs.androidx.fragment.ktx
    implementation libs.androidx.room.runtime
    implementation libs.google.material
    implementation libs.google.gson
    implementation libs.koin.android

    implementation project(':Common')
    implementation project(':Provider')
}