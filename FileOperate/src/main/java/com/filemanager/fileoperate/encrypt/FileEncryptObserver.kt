/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FilePrivacyObserver.kt
 ** Description: File upload to cloud
 ** Version: 1.0
 ** Date: 2020/3/5
 ** Author: LiHao(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.encrypt

import android.content.Context
import android.view.ContextThemeWrapper
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.common.utils.CustomToast

const val ERROR_ENCRYPT_PERMISSION = -2

open class FileEncryptObserver(context: ContextThemeWrapper) : BaseFileActionObserver(context) {

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        when (result.first) {
            ERROR_ENCRYPT_PERMISSION -> {
                dismissProgressDialog()
                CustomToast.showLong(com.filemanager.common.R.string.unable_to_encrypted)
                onActionDone(false)
                return true
            }
            ACTION_DONE -> CustomToast.showLong(com.filemanager.common.R.string.string_privacy_success_toast1)
            ACTION_FAILED -> {
                dismissProgressDialog()
                onActionDone(false)
                val failedCount = if (result.second is Int) result.second as Int else -1
                if (failedCount > 0) {
                    CustomToast.showLong(context.resources.getQuantityString(
                        com.filemanager.common.R.plurals.encrypt_failed_hint_mix, failedCount, failedCount))
                }
            }
        }
        return false
    }
}