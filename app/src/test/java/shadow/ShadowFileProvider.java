/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowFileProvider.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/8/28
 ** Author: JiaBo Lan
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/8/28      1.0     create
 ****************************************************************/
package shadow;

import android.content.Context;
import android.net.Uri;

import androidx.core.content.FileProvider;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.io.File;

@Implements(FileProvider.class)
public class ShadowFileProvider {
    @Implementation
    public static Uri getUriForFile(Context context, String authority, File file) {
        return null;
    }
}
