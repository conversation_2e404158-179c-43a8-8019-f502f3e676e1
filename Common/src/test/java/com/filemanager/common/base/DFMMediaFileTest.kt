package com.filemanager.common.base

import android.content.Context
import android.net.Uri
import com.filemanager.common.MyApplication
import com.oplus.filemanager.dfm.DFMManager
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import org.apache.commons.io.FilenameUtils
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File

/**
 * DFMMediaFile 的单元测试类
 * 使用 Robolectric 测试框架进行 Android 环境下的单元测试
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class DFMMediaFileTest {

    // 待测试的 DFMMediaFile 对象
    private lateinit var dfmMediaFile: DFMMediaFile
    // 模拟的 Context 对象
    private val mockContext = mockk<Context>(relaxed = true)
    // 模拟的 Uri 对象
    private val mockUri = mockk<Uri>(relaxed = true)

    /**
     * 测试前的初始化方法
     * 1. 模拟 MyApplication 单例
     * 2. 模拟 DFMManager 对象
     */
    @Before
    fun setUp() {
        // 模拟 MyApplication 单例对象
        mockkObject(MyApplication)
        // 设置模拟的应用程序上下文
        every { MyApplication.sAppContext } returns mockContext
        
        // 模拟 DFMManager 对象
        mockkObject(DFMManager)
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试 DFMMediaFile 的构造函数
     * 验证构造函数的参数是否正确设置到对象属性中
     */
    @Test
    fun testConstructor() {
        val id = 1
        val cursorPath = "test/path"
        val displayName = "test.jpg"
        val mimeType = "image/jpeg"
        val size = 1024L
        val dateModified = 1234567890L
        
        // 使用带参数的构造函数创建对象
        dfmMediaFile = DFMMediaFile(id, cursorPath, displayName, mimeType, size, dateModified)
        
        // 验证各个属性是否与构造参数一致
        assertEquals(id, dfmMediaFile.id)
        assertEquals(cursorPath, dfmMediaFile.cursorPath)
        assertEquals(displayName, dfmMediaFile.mDisplayName)
        assertEquals(mimeType, dfmMediaFile.mMimeType)
        assertEquals(size, dfmMediaFile.mSize)
        assertEquals(dateModified, dfmMediaFile.mDateModified)
        // 验证文件扩展名是否正确
        assertEquals(FilenameUtils.getExtension(displayName), "jpg")
    }

    /**
     * 测试 equals 方法 - 相同实例的情况
     * 验证对象与自身比较应该返回 true
     */
    @Test
    fun testEquals_SameInstance() {
        dfmMediaFile = DFMMediaFile()
        assertTrue(dfmMediaFile.equals(dfmMediaFile))
    }

    /**
     * 测试 equals 方法 - 不同类型的情况
     * 验证对象与其他类型比较应该返回 false
     */
    @Test
    fun testEquals_DifferentClass() {
        dfmMediaFile = DFMMediaFile()
        assertFalse(dfmMediaFile.equals(Any()))
    }

    /**
     * 测试 equals 方法 - 不同ID的情况
     * 验证两个不同ID的DFMMediaFile对象比较应该返回 false
     */
    @Test
    fun testEquals_DifferentId() {
        val file1 = DFMMediaFile(1, "path1", "name1", "type1", 1L, 1L)
        val file2 = DFMMediaFile(2, "path1", "name1", "type1", 1L, 1L)
        
        assertFalse(file1.equals(file2))
    }
}