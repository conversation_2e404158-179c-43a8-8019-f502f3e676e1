/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WebRequestBean
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/8 14:45
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/8       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.bean

data class WebRequestBean(val categoryId: Int, val pageNo: Int, val pageSize: Int) {
    override fun toString(): String {
        return "WebRequestBean(categoryId=$categoryId, pageNo = $pageNo, pageSize=$pageSize)"
    }
}
