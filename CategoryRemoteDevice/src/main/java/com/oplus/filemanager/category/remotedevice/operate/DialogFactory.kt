/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DialogFactory
 * * Description : DialogFactory
 * * Version     : 1.0
 * * Date        : 2024/12/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.operate

import android.app.Activity
import android.content.DialogInterface
import android.util.Log
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.COUISecurityAlertDialogBuilder
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.PreferencesUtils
import java.util.function.Consumer

object DialogFactory {

    private const val TAG = "DialogFactory"

    /**
     * 创建流量消耗提醒弹窗
     * @param activity
     * @param traffic 流量
     */
    @JvmStatic
    fun createMobileTrafficDialog(activity: Activity, traffic: String, callback: Consumer<Boolean>?): AlertDialog {
        val dialog = COUISecurityAlertDialogBuilder(activity).apply {
            val msg = activity.getString(com.filemanager.common.R.string.use_mobile_traffic_msg, traffic)
            setTitle(com.filemanager.common.R.string.use_mobile_traffic_title)
            setMessage(msg)
            setChecked(false)
            setHasCheckBox(true)
            setCheckBoxString(com.filemanager.common.R.string.no_more_download_remind_msg)
            setShowStatementText(false)
            setNegativeString(com.filemanager.common.R.string.dialog_cancel)
            setPositiveString(com.filemanager.common.R.string.download)
            setOnSelectedListener(COUISecurityAlertDialogBuilder.OnSelectedListener { whichButton, isCheck ->
                Log.d(TAG, "createMobileTrafficDialog: select : which = $whichButton, isCheck = $isCheck")
                if (whichButton == 0) { // 点击了checkbox
                    return@OnSelectedListener
                }
                // 下载按钮
                val isPositiveBtn = whichButton == DialogInterface.BUTTON_POSITIVE
                if (isCheck && isPositiveBtn) {
                    PreferencesUtils.put(key = CommonConstants.DOWNLOAD_TRAFFIC_REMIND_SHOW, value = true)
                }
                callback?.accept(isPositiveBtn)
            })
            setHasMessageMerge(true)
            setBlurBackgroundDrawable(true)
        }.show()
        return dialog
    }

    /**
     * 存储空间不足的弹窗
     */
    @JvmStatic
    fun createSpaceNotEnoughDialog(context: Activity): AlertDialog {
        val dialog = COUIAlertDialogBuilder(context)
            .setTitle(com.filemanager.common.R.string.storage_space_not_enough)
            .setMessage(com.filemanager.common.R.string.clear_space_and_retry)
            .setPositiveButton(com.filemanager.common.R.string.garbage_cleanup) { _, _ ->
                KtAppUtils.startPhoneManager(context)
            }
            .setNegativeButton(com.filemanager.common.R.string.alert_dialog_cancel, null)
            .setBlurBackgroundDrawable(true)
            .show()
        dialog.setCanceledOnTouchOutside(false)
        return dialog
    }

    /**
     * 创建自定义的通知权限弹窗
     * @param activity
     * @param callback 回调
     */
    @JvmStatic
    fun createCustomNotifyPermissionDialog(activity: Activity, callback: Consumer<Boolean>?): AlertDialog {
        var dialog: AlertDialog? = null
        dialog = COUISecurityAlertDialogBuilder(activity).apply {
            setTitle(com.filemanager.common.R.string.custom_notify_dialog_title)
            setShowStatementText(true)
            setStatementLinkString(com.filemanager.common.R.string.custom_notify_dialog_msg, com.filemanager.common.R.string.notify_manager)
            setOnLinkTextClickListener {
                dialog?.dismiss()
                PermissionUtils.openNotifySettings(activity, callback)
            }
            setChecked(false)
            setHasCheckBox(true)
            setCheckBoxString(com.filemanager.common.R.string.file_encrypt_checkbox_describe)
            setNegativeString(com.filemanager.common.R.string.dialog_cancel)
            setPositiveString(com.filemanager.common.R.string.go_settings)
            setOnSelectedListener(COUISecurityAlertDialogBuilder.OnSelectedListener { whichButton, isCheck ->
                Log.d(TAG, "createMobileTrafficDialog: select : which = $whichButton, isCheck = $isCheck")
                if (whichButton == 0) { // 点击了checkbox
                    return@OnSelectedListener
                }
                // 去设置
                val isPositiveBtn = whichButton == DialogInterface.BUTTON_POSITIVE
                if (isCheck && isPositiveBtn) {
                    PreferencesUtils.put(key = CommonConstants.DOWNLOAD_NOTIFY_REMIND_SHOW, value = true)
                }
                if (isPositiveBtn) {
                    PermissionUtils.openNotifySettings(activity, callback)
                } else {
                    callback?.accept(false)
                }
            })
            setHasMessageMerge(true)
            setBlurBackgroundDrawable(true)
        }.show()
        return dialog
    }
}