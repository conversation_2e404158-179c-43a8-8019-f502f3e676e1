/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.filemanager.cloudconfig.bean
 ** Version: 1.0
 ** Date: 2021/5/11
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudconfig.bean

import com.filemanager.common.utils.Log
import org.json.JSONObject
import java.io.Serializable
import java.lang.Exception

/**
 * get the param config of app
 * this is used for many configurations but only one value, like on-off switcher
 */
class AppConfigParam(
    var isWpsThumbnailClose: Int? = 0,
    var isViewAndroidDataClose: Int? = 0
) : Serializable {
    companion object {
        private const val TAG = "AppConfigParam"
        fun parse(content: String?): AppConfigParam {
            return if (content.isNullOrEmpty()) {
                AppConfigParam()
            } else {
                AppConfigParam().apply {
                    try {
                        val json = JSONObject(content)
                        isWpsThumbnailClose = json.optInt("isWpsThumbnailClose")
                        isViewAndroidDataClose = json.optInt("isViewAndroidDataClose")
                    } catch (e: Exception) {
                        Log.d(TAG, "parse error: $e")
                    }
                }
            }
        }
    }
}