/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - RecycleFileManagerTest.java
 * Description: Recycle FileManager Test
 * Version: 1.0
 * Date : 2020/04/20
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/04/20    1.0     create
 ****************************************************************/
package com.coloros.filemanager.recyclebin

import android.content.Context
import android.content.res.Resources
import android.net.Uri
import com.filemanager.common.base.BaseFileBean
import com.filemanager.recyclebin.RecycleFileManager
import io.mockk.every
import io.mockk.mockk
import junit.framework.Assert.fail
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class RecycleFileManagerTest {

    lateinit var context: Context
    lateinit var resources: Resources

    @Before
    fun setUp() {
        context = mockk<Context>(relaxed = true)
        resources = mockk<Resources>(relaxed = true)
        every { context.resources }.returns(resources)
    }

    @Test
    fun should_vertify_recycleFiles_when_parameters_isNull() {
        val recycleFileManager = RecycleFileManager.INSTANCE
        val recycleFileParameters = RecycleFileManager.RecycleFileParameters()
        try {
            recycleFileManager.recycleFiles(null, null, null, recycleFileParameters)
        } catch (ex: Exception) {
            fail("recycleFiles1 parameter is null error")
        }

        try {
            recycleFileManager.recycleFiles(null, null, null, 0, false)
        } catch (ex: Exception) {
            fail("recycleFiles2 parameter is null error")
        }
    }

    @Test
    fun should_vertify_recyclePaths_when_parameters_isNull() {
        val recycleFileManager = RecycleFileManager.INSTANCE
        try {
            recycleFileManager.recyclePaths(null, null, null, 0, true)
        } catch (ex: Exception) {
            fail("recyclePaths1 parameter is null error")
        }

        try {
            recycleFileManager.recyclePaths(null, null, null)
        } catch (ex: Exception) {
            fail("recyclePaths2 parameter is null error")
        }
    }

    @Test
    fun should_vertify_restoreBaseFiles_when_parameters_isNull() {
        val recycleFileManager = RecycleFileManager.INSTANCE
        try {
            val files = ArrayList<BaseFileBean>();
            recycleFileManager.restoreBaseFiles(null, files, null,0)
        } catch (ex: Exception) {
            fail("restoreFiles parameter is null error")
        }
    }

    @Test
    fun should_vertify_restoreUris_when_parameters_isNull() {
        val recycleFileManager = RecycleFileManager.INSTANCE
        try {
            val uris = ArrayList<Uri>();
            recycleFileManager.restoreUris(null, uris, null)
        } catch (ex: Exception) {
            fail("restoreUris parameter is null error")
        }
    }

    @Test
    fun should_vertify_deleteUris_when_parameters_isNull() {
        val recycleFileManager = RecycleFileManager.INSTANCE
        try {
            val uris = ArrayList<Uri>();
            recycleFileManager.deleteUris(null, uris, null)
        } catch (ex: Exception) {
            fail("deleteUris parameter is null error")
        }
    }

    @Test
    fun should_vertify_deleteFiles_when_parameters_isNull() {
        val recycleFileManager = RecycleFileManager.INSTANCE
        try {
            val files = ArrayList<BaseFileBean>();
            recycleFileManager.deleteBaseFileBeans(null, files, null)
        } catch (ex: Exception) {
            fail("deleteFiles parameter is null error")
        }
    }

    @Test
    fun `should return different title when pass in different params`() {
        //given
        val recycleFileManager = RecycleFileManager.INSTANCE
        every { resources.getString(any()) }.returns("要恢复此项目吗?")
        //when
        val title = recycleFileManager.getRestoreTitle(context, 1, 10)
        //then
        Assert.assertEquals("要恢复此项目吗?", title)

        every { resources.getQuantityString(any(), any(), any()) }.returns("恢复 2 项")
        val title2 = recycleFileManager.getRestoreTitle(context, 2, 10)
        Assert.assertEquals("恢复 2 项", title2)

        every { context.getString(any()) }.returns("恢复全部项")
        val title3 = recycleFileManager.getRestoreTitle(context, 10, 10)
        Assert.assertEquals("恢复全部项", title3)
    }
}