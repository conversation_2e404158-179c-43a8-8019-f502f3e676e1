package com.filemanager.thumbnail.audio

import android.content.Context
import android.graphics.Bitmap
import androidx.test.platform.app.InstrumentationRegistry
import com.bumptech.glide.Glide
import com.bumptech.glide.load.Transformation
import com.bumptech.glide.load.engine.Resource
import com.bumptech.glide.load.resource.bitmap.BitmapResource
import com.filemanager.thumbnail.utils.Log
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.security.MessageDigest

/**
 * AudioThumbnailTransformation 的单元测试类
 * 用于测试音频缩略图转换器的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(maxSdk = 33)
class AudioThumbnailTransformationTest {

    // 测试所需的成员变量
    private lateinit var context: Context
    private lateinit var wrappedTransformation: Transformation<Bitmap>
    private lateinit var transformation: AudioThumbnailTransformation
    private lateinit var mockBitmap: Bitmap
    private lateinit var mockMessageDigest: MessageDigest

    /**
     * 测试前的初始化方法
     * 1. 获取测试上下文
     * 2. 创建模拟对象
     * 3. 初始化待测试对象
     * 4. 设置静态方法的模拟行为
     */
    @Before
    fun setup() {
        // 获取测试上下文环境
        context = InstrumentationRegistry.getInstrumentation().targetContext
        // 创建被包装的转换器模拟对象
        wrappedTransformation = mockk(relaxed = true)
        // 初始化待测试的音频缩略图转换器
        transformation = AudioThumbnailTransformation(wrappedTransformation)
        // 创建模拟的Bitmap对象
        mockBitmap = mockk(relaxed = true)
        // 创建模拟的MessageDigest对象
        mockMessageDigest = mockk(relaxed = true)
        
        // 模拟静态类和方法
        mockkStatic(Glide::class)
        mockkStatic(Log::class)
        
        // 设置Glide.get().bitmapPool的模拟返回值
        every { Glide.get(any()).bitmapPool } returns mockk(relaxed = true)
        // 设置Log.d()的模拟行为
        every { Log.d(any(), any()) } returns Unit
    }

    /**
     * 测试后的清理方法
     * 清除所有模拟对象和静态模拟
     */
    @After
    fun tearDown() {
        clearAllMocks()
        unmockkAll()
    }

    /**
     * 测试transform方法 - 当bitmap存在时的转换逻辑
     * 验证:
     * 1. 返回的Resource不为null
     * 2. 转换后的bitmap与预期一致
     * 3. 正确调用了包装转换器的transform方法
     * 4. 正确记录了日志
     */
    @Test
    fun `transform should return transformed resource when bitmap exists`() {
        // Given - 准备测试数据
        val audioThumbnailResult = AudioThumbnailResult(mockBitmap)
        val resource = mockk<Resource<AudioThumbnailResult>>()
        val transformedResource = mockk<Resource<Bitmap>>()
        val transformedBitmap = mockk<Bitmap>()
        
        // 设置模拟对象的行为
        every { resource.get() } returns audioThumbnailResult
        every { wrappedTransformation.transform(any(), any(), any(), any()) } returns transformedResource
        every { transformedResource.get() } returns transformedBitmap

        // When - 执行被测试方法
        val result = transformation.transform(context, resource, 100, 100)

        // Then - 验证结果
        assertNotNull(result)
        assertEquals(transformedBitmap, (result.get() as AudioThumbnailResult).mBitmap)
        verify(exactly = 1) { wrappedTransformation.transform(any(), any(), 100, 100) }
        verify(exactly = 1) { Log.d("AudioThumbnailTransformation", any()) }
    }

    /**
     * 测试transform方法 - 当bitmap为null时的处理逻辑
     * 验证:
     * 1. 返回原始resource
     * 2. 没有调用包装转换器的transform方法
     */
    @Test
    fun `transform should return original resource when bitmap is null`() {
        // Given
        val audioThumbnailResult = AudioThumbnailResult(null)
        val resource = mockk<Resource<AudioThumbnailResult>>()
        every { resource.get() } returns audioThumbnailResult

        // When
        val result = transformation.transform(context, resource, 100, 100)

        // Then
        assertEquals(resource, result)
        verify(exactly = 0) { wrappedTransformation.transform(any(), any(), any(), any()) }
    }

    /**
     * 测试equals方法 - 当包装转换器相等时返回true
     */
    @Test
    fun `equals should return true when wrapped transformations are equal`() {
        // Given
        val other = AudioThumbnailTransformation(wrappedTransformation)
        every { wrappedTransformation.equals(any()) } returns true

        // When
        val result = transformation.equals(other)

        // Then
        assertTrue(result)
        verify(exactly = 1) { wrappedTransformation.equals(any()) }
    }

    /**
     * 测试equals方法 - 当包装转换器不相等时返回false
     */
    @Test
    fun `equals should return false when wrapped transformations are not equal`() {
        // Given
        val otherWrapped = mockk<Transformation<Bitmap>>()
        val other = AudioThumbnailTransformation(otherWrapped)
        every { wrappedTransformation.equals(any()) } returns false

        // When
        val result = transformation.equals(other)

        // Then
        assertFalse(result)
        verify(exactly = 1) { wrappedTransformation.equals(any()) }
    }

    /**
     * 测试equals方法 - 当比较不同类型对象时返回false
     */
    @Test
    fun `equals should return false when comparing with different type`() {
        // When
        val result = transformation.equals(Any())

        // Then
        assertFalse(result)
        verify(exactly = 0) { wrappedTransformation.equals(any()) }
    }

    /**
     * 测试hashCode方法 - 返回包装转换器的hashCode
     */
    @Test
    fun `hashCode should return wrapped transformation's hashCode`() {
        // Given
        val expectedHash = 123
        every { wrappedTransformation.hashCode() } returns expectedHash

        // When
        val result = transformation.hashCode()

        // Then
        assertEquals(expectedHash, result)
        verify(exactly = 1) { wrappedTransformation.hashCode() }
    }

    /**
     * 测试updateDiskCacheKey方法 - 委托给包装转换器执行
     */
    @Test
    fun `updateDiskCacheKey should delegate to wrapped transformation`() {
        // When
        transformation.updateDiskCacheKey(mockMessageDigest)

        // Then
        verify(exactly = 1) { wrappedTransformation.updateDiskCacheKey(mockMessageDigest) }
    }
}