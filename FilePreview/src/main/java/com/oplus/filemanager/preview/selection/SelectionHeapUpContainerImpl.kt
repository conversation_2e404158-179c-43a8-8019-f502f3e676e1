/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SelectionHeapUpContainerImpl.kt
 * Description:
 *     The container views for heap up page.
 *
 * Version: 1.0
 * Date: 2024-10-22
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-10-22   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection

import android.os.SystemClock
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.widget.SelectionHeapUpView

internal class SelectionHeapUpContainerImpl(
    override val containerRoot: ViewGroup
) : ISelectionHeapUpContainer {

    private companion object {
        private const val TAG = "SelectionHeapUpContainerImpl"
    }

    override val heapUpView: SelectionHeapUpView =
        containerRoot.findViewById(R.id.selection_files_heap_up)
    override val filesCountView: AppCompatTextView =
        containerRoot.findViewById(R.id.selection_files_count)
    override val filesStatView: AppCompatTextView =
        containerRoot.findViewById(R.id.selection_files_stat_info)
    private val loadingView: View = containerRoot.findViewById(R.id.loading_layout)

    override var startLoadingTime: Long? = null
        private set

    override fun showAsLoading(isLoading: Boolean) {
        if (isLoading && (loadingView.visibility != View.VISIBLE)) {
            Log.d(TAG, "showAsLoading: true")
            loadingView.visibility = View.VISIBLE
            heapUpView.visibility = View.INVISIBLE
            filesCountView.visibility = View.INVISIBLE
            filesStatView.visibility = View.INVISIBLE
            startLoadingTime = SystemClock.uptimeMillis()
        } else if (!isLoading && (loadingView.visibility == View.VISIBLE)) {
            Log.d(TAG, "showAsLoading: false")
            loadingView.visibility = View.GONE
            heapUpView.visibility = View.VISIBLE
            filesCountView.visibility = View.VISIBLE
            filesStatView.visibility = View.VISIBLE
            startLoadingTime = null
        }
    }
}