/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.audio
 ** Version: 1.0
 ** Date: 2021/5/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.audio

import android.media.MediaMetadataRetriever
import com.bumptech.glide.Priority
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.data.DataFetcher
import com.filemanager.thumbnail.utils.Log

internal class AudioThumbnailFetcher(
    private val model: AudioThumbnailNew,
    private val width: Int,
    private val height: Int
) : DataFetcher<AudioThumbnailResultByteArray> {

    private companion object {
        private const val TAG = "AudioThumbnailFetcher"
    }

    @Volatile
    private var mIsCancelled = false

    override fun getDataClass(): Class<AudioThumbnailResultByteArray> {
        return AudioThumbnailResultByteArray::class.java
    }

    override fun cleanup() {
        //do nothing
    }

    override fun getDataSource(): DataSource {
        return DataSource.REMOTE
    }

    override fun cancel() {
        mIsCancelled = true
    }

    override fun loadData(
        priority: Priority,
        callback: DataFetcher.DataCallback<in AudioThumbnailResultByteArray>
    ) {
        if (mIsCancelled) {
            callback.onDataReady(null)
            return
        }

        val bitmap = createAudioThumbnail()
        when {
            mIsCancelled -> {
                callback.onDataReady(null)
            }
            bitmap != null -> {
                callback.onDataReady(bitmap)
            }
            else -> {
                callback.onLoadFailed(Exception("load audio cover failed"))
            }
        }
    }

    private fun createAudioThumbnail(): AudioThumbnailResultByteArray? {
        try {
            MediaMetadataRetriever().use { retriever ->
                retriever.setDataSource(model.mFilePath)
                val raw = retriever.embeddedPicture
                return if (raw != null) {
                    AudioThumbnailResultByteArray(raw)
                } else {
                    AudioThumbnailResultByteArray(null)
                }
            }
        } catch (e: RuntimeException) {
            Log.d(TAG, "Failed to create thumbnail: $e")
        }

        return null
    }
}