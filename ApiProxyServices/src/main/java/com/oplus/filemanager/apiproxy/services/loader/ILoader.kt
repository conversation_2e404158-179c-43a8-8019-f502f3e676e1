/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ILoader
 ** Description : Loader interface
 ** Version     : 1.0
 ** Date        : 2024/06/20 10:38
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/06/20       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.loader

import android.content.ContentResolver
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import com.filemanager.common.base.BaseFileBean

interface ILoader {

    /**
     * 查询结果
     */
    fun query(contentResolver: ContentResolver, queryArgs: Bundle?): Cursor?
}