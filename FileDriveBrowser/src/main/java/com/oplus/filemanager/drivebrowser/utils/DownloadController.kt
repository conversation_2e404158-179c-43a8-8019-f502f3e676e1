/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DownloadController
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/8/16 15:44
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/8/16       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import androidx.annotation.VisibleForTesting

class DownloadController {
    @VisibleForTesting
    var workState = WorkState.RUNNING

    @Synchronized
    fun pause() {
        workState = WorkState.STOP
    }

    @Synchronized
    fun start() {
        workState = WorkState.RUNNING
    }

    @Synchronized
    fun reset() {
        workState = WorkState.RUNNING
    }

    fun isPause(): Boolean = workState == WorkState.STOP
}

enum class WorkState {
    RUNNING, STOP
}