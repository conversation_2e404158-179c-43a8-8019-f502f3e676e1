/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : SearchManagerUtil.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : wanghonglei
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>         <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.dump

import android.content.Context
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.oplus.dmp.sdk.InitConfig
import com.oplus.dmp.sdk.SearchManager
import com.oplus.dmp.sdk.client.SearchClient

object SearchManagerUtil {
    private const val TAG: String = "SearchManagerUtil"
    private val isAtLeastU = SdkUtils.isAtLeastU()

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getClientWrapper(clientName: String): SearchClient? {
        if (isAtLeastU.not()) {
            return null
        }
        return try {
            SearchManager.getInstance().getClient(clientName)
        } catch (e: Exception) {
            Log.d(TAG, "getClientWrapper error ${e.message}")
            null
        }
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getClientWrapper(name1: String, name2: String): SearchClient? {
        if (isAtLeastU.not()) {
            return null
        }
        return try {
            SearchManager.getInstance().getClient(name1, name2)
        } catch (e: Exception) {
            Log.d(TAG, "getClientWrapper error ${e.message}")
            null
        }
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun initSyncWrapper(context: Context, initConfig: InitConfig): Boolean {
        if (isAtLeastU.not()) {
            return false
        }
        return try {
            SearchManager.getInstance().initSync(context, initConfig)
        } catch (e: Exception) {
            Log.d(TAG, "initSyncWrapper error ${e.message}")
            false
        }
    }

    @JvmStatic
    fun initWrapper(context: Context?, config: InitConfig) {
        if (isAtLeastU.not()) {
            return
        }
        kotlin.runCatching {
            SearchManager.getInstance().init(context, config)
        }.onFailure {
            Log.d(TAG, "initWrapper error ${it.message}")
        }
    }

    @JvmStatic
    fun getInstanceWrapper(): SearchManager? {
        return if (isAtLeastU.not()) {
            null
        } else {
            SearchManager.getInstance()
        }
    }
}