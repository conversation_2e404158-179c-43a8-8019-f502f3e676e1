/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FakeTencentFileDriveService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/12 14:58
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/12       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.repository

import com.oplus.filemanager.drivebrowser.data.api.TencentFileDriveService
import com.oplus.filemanager.drivebrowser.data.model.AuthorizationPath
import com.oplus.filemanager.drivebrowser.data.model.AuthorizationState
import com.oplus.filemanager.drivebrowser.data.model.tencent.BatchDeleteRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.BatchDeleteResponse
import com.oplus.filemanager.drivebrowser.data.model.tencent.BatchProcessResponse
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListsRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListsResponse
import com.oplus.filemanager.drivebrowser.data.model.BaseResponse
import com.oplus.filemanager.drivebrowser.data.model.tencent.AsyncExportFileRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.AsyncExportFileResponse
import com.oplus.filemanager.drivebrowser.data.model.tencent.BaseFileItem
import com.oplus.filemanager.drivebrowser.data.model.tencent.DeleteFileRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.DeleteFolderRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentFilterRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.ExportFileProcessRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.ExportFileProcessResponse
import com.oplus.filemanager.drivebrowser.data.model.tencent.RenameFileRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.RenameFolderRequest
import com.oplus.filemanager.drivebrowser.data.model.tencent.SearchListResp
import com.oplus.filemanager.drivebrowser.data.model.tencent.SearchRequest

class FakeTencentFileDriveService(
    var authState: Boolean? = null,
    var authorizationPath: AuthorizationPath? = null,
    var documentListsResponse: DocumentListsResponse? = null,
    var code: Int = -1,
    var deleteResponse: BatchDeleteResponse? = null,
    var deleteProcessResponse: BatchProcessResponse? = null
) : TencentFileDriveService {
    override suspend fun checkAuthorizationState(): BaseResponse<AuthorizationState> {
        return BaseResponse(data = AuthorizationState(authState))
    }

    override suspend fun buildAuthorizationPath(): BaseResponse<AuthorizationPath> {
        return BaseResponse(data = authorizationPath)
    }

    override suspend fun callbackAuth(code: String, state: String): BaseResponse<Any> {
        return BaseResponse(code = this.code)
    }

    override suspend fun cancelAuth(): BaseResponse<Any> {
        return BaseResponse()
    }

    override suspend fun buildWebViewAuthorizationPath(redirectUrl: String): BaseResponse<AuthorizationPath> {
        return BaseResponse(data = authorizationPath)
    }

    override suspend fun getDocumentLists(request: DocumentListsRequest): BaseResponse<DocumentListsResponse> {
        return BaseResponse(data = documentListsResponse)
    }

    override suspend fun filterDocumentLists(request: DocumentFilterRequest): BaseResponse<DocumentListsResponse> {
        return BaseResponse(data = documentListsResponse)
    }

    override suspend fun renameDocument(request: RenameFileRequest): BaseResponse<Any> {
        return BaseResponse(code = code)
    }

    override suspend fun renameFolder(request: RenameFolderRequest): BaseResponse<Any> {
        return BaseResponse(code = code)
    }

    override suspend fun deleteFile(request: DeleteFileRequest): BaseResponse<Any> {
        return BaseResponse(code = code)
    }

    override suspend fun deleteFolder(request: DeleteFolderRequest): BaseResponse<Any> {
        return BaseResponse(code = code)
    }

    override suspend fun deleteSelectFiles(request: BatchDeleteRequest): BaseResponse<BatchDeleteResponse> {
        return BaseResponse(data = deleteResponse)
    }

    override suspend fun getBatchProcess(processId: String): BaseResponse<BatchProcessResponse> {
        return BaseResponse(data = deleteProcessResponse)
    }

    override suspend fun asyncExportFile(request: AsyncExportFileRequest): BaseResponse<AsyncExportFileResponse> {
        return BaseResponse()
    }

    override suspend fun exportFileProcess(request: ExportFileProcessRequest): BaseResponse<ExportFileProcessResponse> {
        return BaseResponse()
    }


    override suspend fun searchFile(request: SearchRequest): BaseResponse<SearchListResp> {
        return BaseResponse()
    }

    override suspend fun metaFile(request: BaseFileItem): BaseResponse<Any> {
        return BaseResponse()
    }
}