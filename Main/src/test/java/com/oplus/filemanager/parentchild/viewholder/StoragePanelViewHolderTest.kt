package com.oplus.filemanager.parentchild.viewholder

import android.content.Context
import android.content.res.Resources
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.ModelUtils
import com.oplus.filemanager.MainApi
import com.oplus.filemanager.main.R
import com.oplus.filemanager.main.ui.uistate.Storage
import com.oplus.filemanager.main.view.HorizontalProgressBar
import com.oplus.filemanager.parentchild.adapter.SideRemoteDeviceListAdapter
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.util.ExternalStorageHelper
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper
import com.oplus.filemanager.parentchild.view.SideNavigationItemContainer
import com.oplus.filemanager.parentchild.view.SideNavigationStorageContainer
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue
import java.util.Locale
import com.filemanager.common.utils.Utils

/**
 * StoragePanelViewHolder的单元测试类
 * 用于测试StoragePanelViewHolder的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class StoragePanelViewHolderTest {

    // 测试类中使用的mock对象
    private lateinit var viewHolder: StoragePanelViewHolder
    private lateinit var mockItemView: View
    private lateinit var mockContext: Context
    private lateinit var mockActivity: ComponentActivity
    private lateinit var mockCallback: (View?, CategoryListBean, Storage?) -> Unit
    private lateinit var mockAnimationHelper: ItemAnimationHelper
    private lateinit var mockExternalStorageHelper: ExternalStorageHelper

    /**
     * 在每个测试方法执行前的初始化方法
     * 1. 设置默认Locale防止NPE
     * 2. 初始化所有mock对象
     * 3. 配置mock对象的行为
     * 4. 创建要测试的ViewHolder实例
     */
    @Before
    fun setUp() {
        // 设置默认Locale防止NPE - 提前到最前面确保在所有操作前设置
        Locale.setDefault(Locale.US)
        
        // 初始化所有mock对象
        mockItemView = mockk(relaxed = true)
        mockContext = mockk(relaxed = true)
        mockActivity = mockk(relaxed = true)
        mockCallback = mockk(relaxed = true)
        mockAnimationHelper = mockk(relaxed = true)
        mockExternalStorageHelper = mockk(relaxed = true)

        // 配置MyApplication的mock行为
        mockkObject(MyApplication)
        every { MyApplication.appContext } returns mockContext
        
        // 配置ModelUtils的mock行为
        mockkObject(ModelUtils)
        every { mockContext.getString(any()) } returns "Test String"
        
        // 修复资源访问问题
        val mockResources = mockk<Resources>(relaxed = true)
        every { mockContext.resources } returns mockResources
        every { mockResources.getString(any()) } returns "Test String"
        
        // 修复VectorDrawableCompat配置错误
        mockkStatic(AppCompatResources::class)
        every { AppCompatResources.getDrawable(any(), any()) } returns mockk(relaxed = true)

        // 创建要测试的ViewHolder实例
        viewHolder = StoragePanelViewHolder(
            itemView = mockItemView,
            context = mockContext,
            activity = mockActivity,
            onItemViewCallback = mockCallback
        )
    }

    /**
     * 在每个测试方法执行后的清理方法
     * 释放所有mock对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试setStorageData方法处理空远程设备列表的情况
     * 验证当传入空设备列表时，视图会被隐藏
     */
    @Test
    fun `setStorageData should handle RemoteDeviceStorage with empty list`() {
        // 准备测试数据 - 空设备列表
        val remoteStorage = Storage.RemoteDeviceStorage(deviceList = emptyList())
        val mockStorageWrapper: View = mockk(relaxed = true)

        // 配置mock行为
        every { mockItemView.findViewById<View>(R.id.side_remote_device) } returns mockStorageWrapper

        // 执行测试方法
        viewHolder.setStorageData(mockItemView, listOf(remoteStorage))

        // 验证视图被隐藏
        verify { mockStorageWrapper.visibility = View.GONE }
    }

    /**
     * 测试setStorageData方法处理已登录的云盘存储的情况
     * 验证当云盘已登录时，视图会显示
     */
    @Test
    fun `setStorageData should handle CloudDiskStorage when signed in`() {
        // 准备测试数据 - 已登录的云盘
        val cloudStorage = Storage.CloudDiskStorage(isSingedIn = true)
        val mockStorageWrapper: SideNavigationItemContainer = mockk(relaxed = true)
        val mockTitleView = mockk<TextView>(relaxed = true)
        val mockIconView = mockk<ImageView>(relaxed = true)

        // 配置mock行为
        every { mockItemView.findViewById<SideNavigationItemContainer>(R.id.side_cloud) } returns mockStorageWrapper
        every { mockStorageWrapper.findViewById<TextView>(R.id.option_title) } returns mockTitleView
        every { mockStorageWrapper.findViewById<ImageView>(R.id.option_icon) } returns mockIconView

        // 执行测试方法
        viewHolder.setStorageData(mockItemView, listOf(cloudStorage))

        // 验证视图显示
        verify { mockStorageWrapper.visibility = View.VISIBLE }
    }

    /**
     * 测试onAnimationUpdate方法在拖动时设置正确的透明度
     * 验证当拖动时，视图的透明度会正确变化
     */
    @Test
    fun `onAnimationUpdate should set correct alpha when dragging`() {
        // 配置MainApi的mock行为
        mockkObject(MainApi)
        // 修改为false以使用fraction计算alpha
        every { MainApi.isDragging(mockActivity) } returns false
        val mockCloudView: SideNavigationItemContainer = mockk(relaxed = true)
        val mockTitleView = mockk<TextView>(relaxed = true)
        val mockIconView = mockk<ImageView>(relaxed = true)
        every { mockItemView.findViewById<SideNavigationItemContainer>(R.id.side_cloud) } returns mockCloudView
        every { mockCloudView.findViewById<TextView>(R.id.option_title) } returns mockTitleView
        every { mockCloudView.findViewById<ImageView>(R.id.option_icon) } returns mockIconView

        // 添加云盘存储数据以确保视图被初始化
        val cloudStorage = Storage.CloudDiskStorage(isSingedIn = true)
        viewHolder.bindData(listOf(cloudStorage), false, mockAnimationHelper)

        // 执行测试方法
        viewHolder.onAnimationUpdate(0.5f, true)

        // 验证透明度设置正确
        verify { mockCloudView.alpha = 0.63f }
    }

    /**
     * 测试setRemoveDeviceList方法初始化适配器的情况
     * 验证当传入设备列表时，会正确初始化RecyclerView的适配器
     */
    @Test
    fun `setRemoveDeviceList should initialize adapter with data`() {
        // 模拟Utils.getNavigationBarHeight防止ClassCastException
        mockkStatic(Utils::class)
        every { Utils.getNavigationBarHeight(any()) } returns 0

        // 准备测试数据 - 设备列表
        val deviceList = listOf(
            RemoteDeviceInfo(deviceName = "Device1", deviceId = "id1"),
            RemoteDeviceInfo(deviceName = "Device2", deviceId = "id2")
        )
        val remoteStorage = Storage.RemoteDeviceStorage(deviceList = deviceList)
        val mockView = mockk<View>(relaxed = true)
        // 修复ClassCastException: 使用正确的COUIRecyclerView类型
        val mockRecyclerView: COUIRecyclerView = mockk(relaxed = true)

        // 配置mock行为
        every { mockItemView.findViewById<View>(R.id.side_remote_device) } returns mockView
        every { mockView.findViewById<COUIRecyclerView>(R.id.recycler_view) } returns mockRecyclerView
        every { mockRecyclerView.layoutManager } returns LinearLayoutManager(mockContext)
        every { mockRecyclerView.adapter } returns null

        // 执行测试方法
        viewHolder.setStorageData(mockItemView, listOf(remoteStorage))

        // 验证适配器被设置
        verify { mockRecyclerView.adapter = any<SideRemoteDeviceListAdapter>() }
    }

    /**
     * 测试onMacDragStateChange方法更新透明度的情况
     * 验证当Mac拖动状态改变时，视图的透明度会正确更新
     */
    @Test
    fun `onMacDragStateChange should update alpha correctly`() {
        val mockCloudView: SideNavigationItemContainer = mockk(relaxed = true)
        val mockTitleView = mockk<TextView>(relaxed = true)
        val mockIconView = mockk<ImageView>(relaxed = true)
        every { mockItemView.findViewById<SideNavigationItemContainer>(R.id.side_cloud) } returns mockCloudView
        every { mockCloudView.findViewById<TextView>(R.id.option_title) } returns mockTitleView
        every { mockCloudView.findViewById<ImageView>(R.id.option_icon) } returns mockIconView

        // 添加云盘存储数据并设置isEdit=true确保条件满足
        val cloudStorage = Storage.CloudDiskStorage(isSingedIn = true)
        viewHolder.bindData(listOf(cloudStorage), true, mockAnimationHelper)

        // 执行测试方法
        viewHolder.onMacDragStateChange(true, 0.5f)

        // 验证透明度设置正确
        verify { mockCloudView.alpha = 0.63f }
    }

    /**
     * 测试setStorageData方法处理未登录的云盘存储的情况
     * 验证当云盘未登录时，视图会被隐藏
     */
    @Test
    fun `setStorageData should hide CloudDiskStorage when not signed in`() {
        // 准备测试数据 - 未登录的云盘
        val cloudStorage = Storage.CloudDiskStorage(isSingedIn = false)
        val mockStorageWrapper: SideNavigationItemContainer = mockk(relaxed = true)

        // 配置mock行为
        every { mockItemView.findViewById<SideNavigationItemContainer>(R.id.side_cloud) } returns mockStorageWrapper

        // 执行测试方法
        viewHolder.setStorageData(mockItemView, listOf(cloudStorage))

        // 验证视图被隐藏
        verify { mockStorageWrapper.visibility = View.GONE }
    }
}