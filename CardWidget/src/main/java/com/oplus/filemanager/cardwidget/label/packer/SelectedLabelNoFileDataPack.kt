/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SelectedLabelNoFileDataPack
 * * Description : 无文件标签卡片显示，显示标签名称，点击跳转到文管所属标签页面
 * * Version     : 1.0
 * * Date        : 2022/12/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.packer

import android.content.Context
import com.filemanager.common.utils.Log
import com.oplus.cardwidget.domain.pack.BaseDataPack
import com.oplus.filemanager.cardwidget.label.data.LabelCardData
import com.oplus.filemanager.cardwidget.util.StartActivityUtil
import com.oplus.filemanager.cardwidget.util.TextUtil
import com.oplus.smartenginehelper.dsl.DSLCoder

class SelectedLabelNoFileDataPack(
    private val context: Context,
    private val labelCard: LabelCardData
) : BaseDataPack() {

    override fun onPack(coder: DSLCoder): Boolean {
        Log.d(DATA_PACK_TAG, "onPack -> coder = $coder")
        val labelName = labelCard.labelName
        val labelId = labelCard.labelId
        Log.d(DATA_PACK_TAG, "onPack -> labelName = $labelName; labelId = $labelId")
        val showDialogStart = StartActivityUtil().startLabelListShowAddFileDialog(labelName, labelId)
        coder.setOnClick(ID_LABEL_TITLE, showDialogStart)
        coder.setOnClick(ID_LABEL_CARD_ICON, showDialogStart)
        coder.setOnClick(ID_LABEL_ACTION, showDialogStart)
        coder.setOnClick(ID_TIPS, showDialogStart)
        coder.setOnClick(ID_LABEL_CONTAINER, showDialogStart)
        coder.setTextViewText(ID_LABEL_TITLE, TextUtil.getLabelNameForLabelCard(labelName))
        TextUtil.setTextSize(coder, context, ID_LABEL_TITLE, TextUtil.TEXT_SIZE_14)
        TextUtil.setTextSize(coder, context, ID_LABEL_ACTION, TextUtil.TEXT_SIZE_12)
        TextUtil.setTextSize(coder, context, ID_TIPS, TextUtil.TEXT_SIZE_16)
        return true
    }

    companion object {
        private const val DATA_PACK_TAG = "SelectedLabelNoFileDataPack"
        private const val ID_LABEL_CARD_ICON = "label_card_icon"
        private const val ID_TIPS = "tips"
        private const val ID_LABEL_TITLE = "label_title"
        private const val ID_LABEL_CONTAINER = "container"
        private const val ID_LABEL_ACTION = "action"
    }
}