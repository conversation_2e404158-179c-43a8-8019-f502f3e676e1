/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DocumentInfo.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/9
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  hank.zhou      2022/9/9      1.0        create
 ***********************************************************************/
package com.android.documentsui.base;

import android.net.Uri;
import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.Nullable;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.net.ProtocolException;
import java.util.Objects;

public class DocumentInfo implements Durable, Parcelable {

    public final static Parcelable.Creator<DocumentInfo> CREATOR = new Creator<DocumentInfo>() {
        @Override
        public DocumentInfo createFromParcel(Parcel in) {
            final DocumentInfo doc = new DocumentInfo();
            DurableUtils.readFromParcel(in, doc);
            return doc;
        }

        @Override
        public DocumentInfo[] newArray(int size) {
            return new DocumentInfo[size];
        }
    };
    private static final String TAG = "DocumentInfo";
    private static final String DEFAULT_STRING_NULL_URI = "<DocumentInfo null derivedUri>";
    private static final String DEFAULT_STRING_NULL = "<null DocumentInfo>";
    private static final int VERSION_INIT = 1;
    private static final int VERSION_SPLIT_URI = 2;
    private static final int VERSION_USER_ID = 3;

    public UserId userId;
    public String authority;
    public String documentId;
    public String mimeType;
    public String displayName;
    public long lastModified;
    public int flags;
    public String summary;
    public long size;
    public int icon;

    /** Derived fields that aren't persisted */
    public Uri derivedUri;

    public DocumentInfo() {
        reset();
    }

    @Override
    public void reset() {
        userId = UserId.UNSPECIFIED_USER;
        authority = null;
        documentId = null;
        mimeType = null;
        displayName = null;
        lastModified = -1;
        flags = 0;
        summary = null;
        size = -1;
        icon = 0;
        derivedUri = Uri.EMPTY;
    }

    @Override
    public void read(DataInputStream in) throws IOException {
        final int version = in.readInt();
        switch (version) {
            case VERSION_USER_ID:
                userId = UserId.read(in);
                authority = DurableUtils.readNullableString(in);
                documentId = DurableUtils.readNullableString(in);
                mimeType = DurableUtils.readNullableString(in);
                displayName = DurableUtils.readNullableString(in);
                lastModified = in.readLong();
                flags = in.readInt();
                summary = DurableUtils.readNullableString(in);
                size = in.readLong();
                icon = in.readInt();
                break;
            case VERSION_SPLIT_URI:
                userId = UserId.CURRENT_USER;
                authority = DurableUtils.readNullableString(in);
                documentId = DurableUtils.readNullableString(in);
                mimeType = DurableUtils.readNullableString(in);
                displayName = DurableUtils.readNullableString(in);
                lastModified = in.readLong();
                flags = in.readInt();
                summary = DurableUtils.readNullableString(in);
                size = in.readLong();
                icon = in.readInt();
                break;
            case VERSION_INIT:
                throw new ProtocolException("Ignored upgrade");
            default:
                throw new ProtocolException("Unknown version " + version);
        }
    }

    @Override
    public void write(DataOutputStream out) throws IOException {
        out.writeInt(VERSION_USER_ID);
        UserId.write(out, userId);
        DurableUtils.writeNullableString(out, authority);
        DurableUtils.writeNullableString(out, documentId);
        DurableUtils.writeNullableString(out, mimeType);
        DurableUtils.writeNullableString(out, displayName);
        out.writeLong(lastModified);
        out.writeInt(flags);
        DurableUtils.writeNullableString(out, summary);
        out.writeLong(size);
        out.writeInt(icon);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        DurableUtils.writeToParcel(dest, this);
    }

    @Override
    public String toString() {
        return "DocumentInfo{"
                + "docId=" + documentId
                + ", userId=" + userId
                + ", name=" + displayName
                + ", mimeType=" + mimeType
                + "} @ "
                + derivedUri;
    }

    @Override
    public int hashCode() {
        return userId.hashCode() + derivedUri.hashCode() + mimeType.hashCode();
    }

    @Override
    public boolean equals(Object o) {
        if (o == null) {
            return false;
        }

        if (this == o) {
            return true;
        }

        if (o instanceof DocumentInfo) {
            DocumentInfo other = (DocumentInfo) o;
            // Uri + mime type should be totally unique.
            return Objects.equals(userId, other.userId)
                    && Objects.equals(derivedUri, other.derivedUri)
                    && Objects.equals(mimeType, other.mimeType);
        }
        return false;
    }

    public static String debugString(@Nullable DocumentInfo doc) {
        if (doc == null) {
            return DEFAULT_STRING_NULL;
        }

        if (doc.derivedUri == null) {
            return DEFAULT_STRING_NULL_URI;
        }
        return doc.derivedUri.toString();
    }
}
