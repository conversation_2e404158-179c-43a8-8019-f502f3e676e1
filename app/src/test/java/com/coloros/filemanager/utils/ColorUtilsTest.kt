/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.coloros.filemanager.ColorUtilsTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/5/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.utils

import android.content.Context
import android.graphics.Color
import com.coloros.filemanager.R
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.filemanager.common.utils.ColorUtil
import com.filemanager.common.utils.ColorUtil.DARK_MODE_LEVEL_ENHANCE
import com.filemanager.common.utils.ColorUtil.DARK_MODE_LEVEL_ENHANCE_COLOR
import com.filemanager.common.utils.ColorUtil.DARK_MODE_LEVEL_MODERATE
import com.filemanager.common.utils.ColorUtil.DARK_MODE_LEVEL_MODERATE_COLOR
import com.filemanager.common.utils.ColorUtil.DARK_MODE_LEVEL_SOFT
import com.filemanager.common.utils.ColorUtil.DARK_MODE_LEVEL_SOFT_COLOR
import com.filemanager.common.utils.ColorUtil.LABEL_CARD_CHECKED_DARK_MODE_LEVEL_ENHANCE_COLOR
import com.filemanager.common.utils.ColorUtil.LABEL_CARD_CHECKED_DARK_MODE_LEVEL_MODERATE_COLOR
import com.filemanager.common.utils.ColorUtil.LABEL_CARD_CHECKED_DARK_MODE_LEVEL_SOFT_COLOR
import com.filemanager.common.utils.ColorUtil.LABEL_CARD_CHECKED_LIGHT_MODE
import com.filemanager.common.utils.ColorUtil.getFileLabelCardBackground
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class ColorUtilsTest {

    @MockK
    lateinit var mContext: Context

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        mockkStatic(COUIContextUtil::class)
        mockkStatic(COUIDarkModeUtil::class)
        mockkStatic(ColorUtil::class)
    }

    @After
    fun tearDown() {
        unmockkStatic(COUIContextUtil::class)
        unmockkStatic(COUIDarkModeUtil::class)
        unmockkStatic(ColorUtil::class)
    }

    @Test
    fun `should return color when get coui color card bg if is not night mode`() {
        every { COUIContextUtil.getAttrColor(mContext, com.support.appcompat.R.attr.couiColorCard) } returns Color.RED
        every { COUIDarkModeUtil.isNightMode(mContext) } returns false
        every { ColorUtil.getCouiColorCardBackground(mContext) } answers { callOriginal() }
        assertEquals(Color.RED, ColorUtil.getCouiColorCardBackground(mContext))
    }

    @Test
    fun `should return 0xff1a1a1a when get coui color card bg if is night mode and dark mode level is 0`() {
        every { COUIContextUtil.getAttrColor(mContext, com.support.appcompat.R.attr.couiColorCard) } returns Color.RED
        every { COUIDarkModeUtil.isNightMode(mContext) } returns true
        every { ColorUtil.getDarkModeLevel() } returns 0
        every { ColorUtil.getCouiColorCardBackground(mContext) } answers { callOriginal() }
        assertEquals(0xff1a1a1a.toInt(), ColorUtil.getCouiColorCardBackground(mContext))
    }

    @Test
    fun `should return 0xff2f2f2f when get coui color card bg if is night mode and dark mode level is 1`() {
        every { COUIContextUtil.getAttrColor(mContext, com.support.appcompat.R.attr.couiColorCard) } returns Color.RED
        every { COUIDarkModeUtil.isNightMode(mContext) } returns true
        every { ColorUtil.getDarkModeLevel() } returns 1
        every { ColorUtil.getCouiColorCardBackground(mContext) } answers { callOriginal() }
        assertEquals(0xff2f2f2f.toInt(), ColorUtil.getCouiColorCardBackground(mContext))
    }

    @Test
    fun `should return 0xff454545 when get coui color card bg if is night mode and dark mode level is 2`() {
        every { COUIContextUtil.getAttrColor(mContext, com.support.appcompat.R.attr.couiColorCard) } returns Color.RED
        every { COUIDarkModeUtil.isNightMode(mContext) } returns true
        every { ColorUtil.getDarkModeLevel() } returns 2
        every { ColorUtil.getCouiColorCardBackground(mContext) } answers { callOriginal() }
        assertEquals(0xff454545.toInt(), ColorUtil.getCouiColorCardBackground(mContext))
    }

    @Test
    fun `should return 0xff454545 when get coui color card bg if is night mode and dark mode level is other`() {
        every { COUIContextUtil.getAttrColor(mContext, com.support.appcompat.R.attr.couiColorCard) } returns Color.RED
        every { COUIDarkModeUtil.isNightMode(mContext) } returns true
        every { ColorUtil.getDarkModeLevel() } returns 4
        every { ColorUtil.getCouiColorCardBackground(mContext) } answers { callOriginal() }
        assertEquals(Color.RED, ColorUtil.getCouiColorCardBackground(mContext))
    }

    @Test
    fun getFileLabelCardBackgroundTest() {
        var isChecked = true

        every { COUIDarkModeUtil.isNightMode(mContext) } returns true

        every { ColorUtil.getDarkModeLevel() } returns DARK_MODE_LEVEL_ENHANCE
        assertEquals(LABEL_CARD_CHECKED_DARK_MODE_LEVEL_ENHANCE_COLOR, getFileLabelCardBackground(mContext, isChecked))

        every { ColorUtil.getDarkModeLevel() } returns DARK_MODE_LEVEL_MODERATE
        assertEquals(LABEL_CARD_CHECKED_DARK_MODE_LEVEL_MODERATE_COLOR, getFileLabelCardBackground(mContext, isChecked))

        every { ColorUtil.getDarkModeLevel() } returns DARK_MODE_LEVEL_SOFT
        assertEquals(LABEL_CARD_CHECKED_DARK_MODE_LEVEL_SOFT_COLOR, getFileLabelCardBackground(mContext, isChecked))

        every { ColorUtil.getDarkModeLevel() } returns -1
        assertEquals(LABEL_CARD_CHECKED_DARK_MODE_LEVEL_ENHANCE_COLOR, getFileLabelCardBackground(mContext, isChecked))

        every { COUIDarkModeUtil.isNightMode(mContext) } returns false
        assertEquals(LABEL_CARD_CHECKED_LIGHT_MODE, getFileLabelCardBackground(mContext, isChecked))

        isChecked = false

        every { COUIDarkModeUtil.isNightMode(mContext) } returns true

        every { ColorUtil.getDarkModeLevel() } returns DARK_MODE_LEVEL_ENHANCE
        assertEquals(DARK_MODE_LEVEL_ENHANCE_COLOR, getFileLabelCardBackground(mContext, isChecked))

        every { ColorUtil.getDarkModeLevel() } returns DARK_MODE_LEVEL_MODERATE
        assertEquals(DARK_MODE_LEVEL_MODERATE_COLOR, getFileLabelCardBackground(mContext, isChecked))

        every { ColorUtil.getDarkModeLevel() } returns DARK_MODE_LEVEL_SOFT
        assertEquals(DARK_MODE_LEVEL_SOFT_COLOR, getFileLabelCardBackground(mContext, isChecked))

        every { ColorUtil.getDarkModeLevel() } returns -1
        assertEquals(DARK_MODE_LEVEL_ENHANCE_COLOR, getFileLabelCardBackground(mContext, isChecked))

        every { COUIDarkModeUtil.isNightMode(mContext) } returns false

        every { mContext.getColor(any()) } returns Color.RED
        assertEquals(Color.RED, getFileLabelCardBackground(mContext, isChecked))
    }
}