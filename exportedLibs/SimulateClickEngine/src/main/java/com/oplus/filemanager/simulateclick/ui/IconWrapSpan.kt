/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : IconWrapSpan
 ** Description : Icon Wrap Text Span
 ** Version     : 1.0
 ** Date        : 2023/12/26 15:51
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue      2023/12/26       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.simulateclick.ui

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.text.Layout
import android.text.Spanned
import android.text.style.IconMarginSpan
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import com.oplus.filemanager.simulateclick.ui.util.CommonUtil
import com.oplus.filemanager.simulateclick.engine.utils.Log

class IconWrapSpan(private val bitmap: Bitmap, padding: Int = 0) : IconMarginSpan(bitmap, padding) {

    companion object {
        private const val TAG = "IconWrapSpan"
        private const val HALF = 2.0f

        /**
         * Get a bitmap from a Vector drawable
         * AppUtils.getBitmapFromDrawable()
         * @param context context
         * @param drawableId The resource id of vector drawable
         * @return The bitmap which a drawable above it.
         */
        fun getBitmapFromVectorDrawable(context: Context, @DrawableRes drawableId: Int): Bitmap? {
            val vectorDrawable: Drawable = ContextCompat.getDrawable(context, drawableId) ?: return null
            return CommonUtil.getBitmapFromDrawable(vectorDrawable)
        }
    }

    override fun getLeadingMargin(first: Boolean): Int {
        if (first) {
            return super.getLeadingMargin(true)
        }
        return 0
    }

    override fun drawLeadingMargin(
        c: Canvas?,
        p: Paint?,
        x: Int,
        dir: Int,
        top: Int,
        baseline: Int,
        bottom: Int,
        text: CharSequence?,
        start: Int,
        end: Int,
        first: Boolean,
        layout: Layout
    ) {
        if (!first) {
            return
        }
        val spanStart = (text as Spanned).getSpanStart(this)
        val spanLine = layout.getLineForOffset(spanStart)
        val lineTop = layout.getLineTop(spanLine)
        val lineBottom = layout.getLineBottom(spanLine)
        // 让图片上下居中
        val imgTop = (lineBottom - lineTop - bitmap.height) / HALF
        // 设置居中时，让图片和文字一起居中
        val adjustMargin = adjustMargin(p, layout, text.subSequence(start, end))
        val imgLeft = if (dir > 0) { // 从左到右布局
            x + adjustMargin
        } else { // 从右到左布局
            x - bitmap.width - adjustMargin
        }
        Log.d(TAG, "drawLeadingMargin dir:$dir x:$x adjust:$adjustMargin left:$imgLeft")
        c?.drawBitmap(bitmap, imgLeft, imgTop, p)
    }

    /**
     * 为了实现在textAlignment="center",icon也需要和文字居中显示，调整文字和图片之间的间距
     */
    private fun adjustMargin(paint: Paint?, layout: Layout, text: CharSequence?): Float {
        val alignment = layout.alignment
        if (Layout.Alignment.ALIGN_CENTER != layout.alignment) {
            Log.d(TAG, "adjustMargin alignment:$alignment")
            return 0F
        }
        val width = layout.width.toFloat()
        val leadingMargin = getLeadingMargin(true)
        val measureWidth = paint?.measureText(text.toString()) ?: 0F
        val result = (width - measureWidth - leadingMargin) / HALF
        Log.d(TAG, "adjustMargin text:$text width:$width measureWidth:$measureWidth result:$result")
        return result
    }
}