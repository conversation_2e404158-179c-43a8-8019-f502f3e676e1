/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - PreviewAudioPlayer.kt
 * Description:
 *     The implementations of audio player with TBL.
 *
 * Version: 1.0
 * Date: 2024-09-13
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-09-13   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.audio

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.net.Uri
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.os.Message
import androidx.annotation.CallSuper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayErrorListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayPreparedListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayProgressUpdateListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayStateChangedListener
import com.oplus.filemanager.preview.utils.MediaPlayerHelper
import com.oplus.tblplayer.IMediaPlayer
import com.oplus.tblplayer.TBLPlayerManager
import com.oplus.tblplayer.monitor.ErrorCode
import kotlin.math.max
import kotlin.math.min

internal open class TBLPreviewAudioPlayer(context: Context) : IPreviewAudioPlayer {

    protected companion object {
        private const val TAG = "TBLPreviewAudioPlayer"

        protected const val DEFAULT_PROGRESS_UPDATE_INTERVAL = 100L

        protected const val MSG_PREPARE_AND_PLAY = 101
        protected const val MSG_RESUME_PLAY = 102
        protected const val MSG_PAUSE_PLAY = 103
        protected const val MSG_STOP_PLAY = 104
        protected const val MSG_SEEK_TO = 105
        protected const val MSG_RELEASE = 106
        protected const val MSG_UPDATE_PROGRESS = 107
        protected const val MSG_SET_VOLUME = 108
        protected const val MSG_PREPARE_TIMEOUT = 109

        protected const val ERROR_CODE_REASON_TIMEOUT = -1000
    }

    protected open val impl = AudioPlayerImpl(context.applicationContext)

    private var preparedUri: Uri? = null

    override fun setOnPlayPreparedListener(
        listener: OnPlayPreparedListener
    ): IPreviewAudioPlayer = apply {
        impl.preparedListener = listener
    }

    override fun setOnPlayErrorListener(listener: OnPlayErrorListener): IPreviewAudioPlayer =
        apply {
            impl.errorListener = listener
        }

    override fun setOnPlayStateChangedListener(
        listener: OnPlayStateChangedListener
    ): IPreviewAudioPlayer = apply {
        impl.playStateListener = listener
    }

    override fun setOnPlayProgressUpdateListener(
        listener: OnPlayProgressUpdateListener
    ): IPreviewAudioPlayer = apply {
        impl.progressListener = listener
    }

    override fun setPlayProgressUpdateInterval(interval: Long): IPreviewAudioPlayer = apply {
        impl.progressInterval = interval
    }

    override fun setPrepareTimeout(timeout: Long): IPreviewAudioPlayer = apply {
        impl.prepareTimeout = timeout
    }

    override fun prepareAndPlay(uri: Uri) {
        if (preparedUri == uri) {
            return
        }
        preparedUri = uri
        impl.playerHandler.removeMessages(MSG_PREPARE_AND_PLAY)
        Message.obtain(impl.playerHandler, MSG_PREPARE_AND_PLAY, uri).sendToTarget()
    }

    override fun resume() {
        impl.playerHandler.apply {
            removeMessages(MSG_RESUME_PLAY)
            sendEmptyMessage(MSG_RESUME_PLAY)
        }
    }

    override fun pause() {
        impl.playerHandler.apply {
            removeMessages(MSG_PAUSE_PLAY)
            sendEmptyMessage(MSG_PAUSE_PLAY)
        }
    }

    override fun stop() {
        impl.playerHandler.apply {
            removeMessages(MSG_STOP_PLAY)
            sendEmptyMessage(MSG_STOP_PLAY)
        }
    }

    override fun release() {
        impl.playerHandler.apply {
            removeMessages(MSG_RELEASE)
            sendEmptyMessage(MSG_RELEASE)
        }
    }

    override fun seekTo(position: Long, previewMode: Boolean) {
        impl.playerHandler.removeMessages(MSG_SEEK_TO)
        val params = SeekParams(position, previewMode)
        Message.obtain(impl.playerHandler, MSG_SEEK_TO, params).sendToTarget()
    }

    override fun setVolume(volume: Float) {
        val configVolume = max(0f, min(1f, volume))
        impl.playerHandler.removeMessages(MSG_SET_VOLUME)
        Message.obtain(impl.playerHandler, MSG_SET_VOLUME, configVolume).sendToTarget()
    }

    override fun getVolume(): Float = impl.tblPlayer?.volume ?: 0f

    override fun isPlaying(): Boolean = impl.tblPlayer?.isPlaying ?: false

    protected data class SeekParams(
        val position: Long,
        val previewMode: Boolean
    )

    protected open class AudioPlayerImpl(
        protected val appContext: Context,
        protected val logTag: String = TAG,
        private val requireAudioFocus: Boolean = MediaPlayerHelper.ENABLE_REQUIRE_FOCUS_WHEN_PLAY_AUDIO
    ) : Handler.Callback, IMediaPlayer.OnPreparedListener, IMediaPlayer.OnErrorListener,
        IMediaPlayer.OnCompletionListener, AudioManager.OnAudioFocusChangeListener {
        val playerHandler: Handler by lazy { Handler(playerThread.looper, this) }
        val mainHandler by lazy { Handler(Looper.getMainLooper(), this) }

        protected open val defaultVolume: Float = 1f
        protected open val audioAttrs: AudioAttributes by lazy {
            AudioAttributes.Builder()
                .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .build()
        }

        private val playerThread by lazy { HandlerThread(logTag).apply { start() } }
        private val audioManager by lazy {
            appContext.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        }
        private val audioFocus by lazy {
            AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
                .setOnAudioFocusChangeListener(this)
                .setAudioAttributes(audioAttrs)
                .setWillPauseWhenDucked(true)
                .build()
        }

        @Volatile
        var tblPlayer: IMediaPlayer? = null
        var preparedListener: OnPlayPreparedListener? = null
        var errorListener: OnPlayErrorListener? = null
        var playStateListener: OnPlayStateChangedListener? = null
        var progressListener: OnPlayProgressUpdateListener? = null
        var progressInterval: Long = DEFAULT_PROGRESS_UPDATE_INTERVAL
        var prepareTimeout: Long = MediaPlayerHelper.getPrepareTimeout(null)

        protected var isPlayStarted = false

        private var preparingPlayer: IMediaPlayer? = null
        private var currentDuration: Long? = null
        private var isPlayCompleted = false

        @CallSuper
        override fun handleMessage(msg: Message): Boolean {
            when (msg.what) {
                MSG_PREPARE_AND_PLAY -> onPrepareAndPlay(msg.obj as Uri)
                MSG_RESUME_PLAY -> onRequestResumePlay()
                MSG_PAUSE_PLAY -> onRequestPausePlay()
                MSG_STOP_PLAY -> onRequestStopPlay()
                MSG_UPDATE_PROGRESS -> onCyclicUpdateProgress()
                MSG_SEEK_TO -> onSeekTo(msg.obj as SeekParams)
                MSG_RELEASE -> onRelease()
                MSG_SET_VOLUME -> onSetVolume(msg.obj as Float)
                MSG_PREPARE_TIMEOUT -> onPrepareTimeout()
                else -> return false
            }
            return true
        }

        private fun onPrepareAndPlay(uri: Uri) {
            val player = tblPlayer?.also {
                if (it.isPlaying) {
                    it.stop()
                }
                it.reset()
                Log.d(logTag, "onPrepareAndPlay: reset, prepare for ${uri.hashCode()}")
            } ?: TBLPlayerManager.createPlayer(appContext).also {
                Log.d(logTag, "onPrepareAndPlay: create, prepare for ${uri.hashCode()}")
                it.volume = defaultVolume
            }
            isPlayStarted = false
            player.apply {
                currentDuration = null
                setDataSource(uri)
                isLooping = false
                setOnErrorListener(this@AudioPlayerImpl)
                setOnCompletionListener(this@AudioPlayerImpl)
                setOnPreparedListener(this@AudioPlayerImpl)
            }
            onConfigureBeforePrepare(player)
            preparingPlayer = player
            player.prepareAsync()
            if (prepareTimeout > 0) {
                playerHandler.sendEmptyMessageDelayed(MSG_PREPARE_TIMEOUT, prepareTimeout)
            }
        }

        override fun onPrepared(player: IMediaPlayer) {
            Log.d(logTag, "onPrepared: $player")
            playerHandler.removeMessages(MSG_PREPARE_TIMEOUT)
            preparingPlayer = null
            tblPlayer = player
            currentDuration = player.duration
            isPlayStarted = true
            onBeforeRequestPlayWhenPrepared(player)
            onUpdateProgress(player)
        }

        @Synchronized
        protected open fun onConfigureBeforePrepare(player: IMediaPlayer) {
            // Default do nothing, configure more for video in sub class
        }

        protected open fun onBeforeRequestPlayWhenPrepared(player: IMediaPlayer) {
            callbackPrepared(isPlayStarted)
        }

        protected fun callbackPrepared(isPlayStarted: Boolean) {
            Log.d(logTag, "callbackPrepared: $isPlayStarted")
            mainHandler.post {
                preparedListener?.onPrepared(isPlayStarted)
            }
        }

        private fun onPrepareTimeout() {
            Log.d(logTag, "onPrepareTimeout")
            onError(preparingPlayer, ErrorCode.TYPE_RENDERER, ERROR_CODE_REASON_TIMEOUT, null)
            onRelease()
        }

        private fun onRequestResumePlay() {
            val player = tblPlayer ?: run {
                Log.e(logTag, "onResumePlay: ERROR!! player is not prepared.")
                return
            }
            onRequireFocusToPlay(player)
        }

        private fun onRequireFocusToPlay(player: IMediaPlayer) {
            if (!requireAudioFocus) {
                onStartPlay(player)
                return
            }
            val result = audioManager.requestAudioFocus(audioFocus)
            Log.d(logTag, "onRequireFocusToPlay: $result")
            if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                onStartPlay(player)
            }
        }

        private fun onStartPlay(player: IMediaPlayer) {
            if (player.isPlaying) {
                return
            }
            Log.d(logTag, "onStartPlay")
            player.start()
            if (isPlayCompleted) {
                player.seekTo(0L)
            }
            isPlayCompleted = false
            isPlayStarted = true
            playerHandler.sendEmptyMessageDelayed(MSG_UPDATE_PROGRESS, progressInterval)
            runOnMainThread {
                playStateListener?.onPlayStateChanged(true)
            }
        }

        private fun onRequestPausePlay() {
            val player = tblPlayer ?: run {
                Log.e(logTag, "onPausePlay: ERROR!! player is not prepared.")
                return
            }
            onReleaseFocusAndPause(player)
        }

        private fun onReleaseFocusAndPause(player: IMediaPlayer) {
            onPausePlay(player)
            if (!requireAudioFocus) {
                return
            }
            val result = audioManager.abandonAudioFocusRequest(audioFocus)
            Log.d(logTag, "onReleaseFocusAndPause: $result")
        }

        private fun onPausePlay(player: IMediaPlayer) {
            if (player.isPause) {
                return
            }
            Log.d(logTag, "onPausePlay")
            isPlayStarted = false
            playerHandler.removeMessages(MSG_UPDATE_PROGRESS)
            player.pause()
            runOnMainThread {
                playStateListener?.onPlayStateChanged(false)
            }
        }

        private fun onRequestStopPlay() {
            val player = tblPlayer ?: run {
                Log.e(logTag, "onStopPlay: ERROR!! player is not prepared.")
                return
            }
            onReleaseFocusAndStop(player)
        }

        private fun onReleaseFocusAndStop(player: IMediaPlayer) {
            onStopPlay(player)
            if (!requireAudioFocus) {
                return
            }
            val result = audioManager.abandonAudioFocusRequest(audioFocus)
            Log.d(logTag, "onReleaseFocusAndStop: $result")
        }

        private fun onStopPlay(player: IMediaPlayer) {
            if (player.isStop) {
                return
            }
            Log.d(logTag, "onStopPlay")
            isPlayStarted = false
            playerHandler.removeMessages(MSG_UPDATE_PROGRESS)
            player.stop()
            runOnMainThread {
                playStateListener?.onPlayStateChanged(false)
            }
        }

        private fun onCyclicUpdateProgress() {
            val player = tblPlayer ?: return
            if (!isPlayStarted) {
                return
            }
            onUpdateProgress(player)
            playerHandler.sendEmptyMessageDelayed(MSG_UPDATE_PROGRESS, progressInterval)
        }

        private fun onUpdateProgress(player: IMediaPlayer) {
            val listener = progressListener ?: return
            val position = player.currentPosition
            val duration = obtainDuration(player)
            runOnMainThread {
                listener.onProgressUpdate(position, duration)
            }
        }

        private fun obtainDuration(player: IMediaPlayer): Long =
            currentDuration ?: player.duration

        private fun onSeekTo(seekParams: SeekParams) {
            val position = seekParams.position
            val previewMode = seekParams.previewMode
            val player = tblPlayer ?: return
            playerHandler.removeMessages(MSG_UPDATE_PROGRESS)
            player.fastSeekTo(max(0, min(position, obtainDuration(player))), previewMode)
            if (isPlayStarted) {
                playerHandler.sendEmptyMessageDelayed(MSG_UPDATE_PROGRESS, progressInterval)
            }
        }

        private fun onSetVolume(volume: Float) {
            val player = tblPlayer ?: run {
                Log.e(logTag, "onSetVolume: ERROR!! player is not prepared.")
                return
            }
            Log.d(logTag, "onSetVolume: volume=$volume")
            player.volume = volume
        }

        override fun onCompletion(player: IMediaPlayer?) {
            if ((tblPlayer == player) && (player != null)) {
                Log.d(logTag, "onCompletion")
                isPlayCompleted = true
                onReleaseFocusAndPause(player)
            }
        }

        override fun onError(
            player: IMediaPlayer?,
            errorType: Int,
            errorCode: Int,
            extra: String?
        ): Boolean {
            val currPlayer = tblPlayer
            if ((currPlayer != null) && ((player == null) || (currPlayer != player))) {
                return false
            }
            playerHandler.removeMessages(MSG_PREPARE_TIMEOUT)
            Log.e(logTag, "onError: errorType=$errorType, errorCode=$errorCode, extra=$extra")
            val listener = errorListener ?: return false
            mainHandler.post {
                listener.onError(errorType, errorCode, extra)
            }
            return false
        }

        @Synchronized
        protected fun onRelease() {
            val player = tblPlayer ?: preparingPlayer ?: return
            Log.d(logTag, "onRelease")
            tblPlayer = null
            preparingPlayer = null
            onReleaseFocusAndStop(player)
            onRelease(player)
        }

        @CallSuper
        protected open fun onRelease(player: IMediaPlayer) {
            player.release()
            playerHandler.removeCallbacksAndMessages(null)
            mainHandler.removeCallbacksAndMessages(null)
            preparedListener = null
            errorListener = null
            playStateListener = null
            progressListener = null
            currentDuration = null
        }

        override fun onAudioFocusChange(focusChange: Int) {
            val player = tblPlayer ?: return
            Log.d(logTag, "onAudioFocusChange: $focusChange")
            when (focusChange) {
                AudioManager.AUDIOFOCUS_GAIN -> onStartPlay(player)
                AudioManager.AUDIOFOCUS_GAIN_TRANSIENT -> onStartPlay(player)
                AudioManager.AUDIOFOCUS_LOSS -> onPausePlay(player)
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> onPausePlay(player)
            }
        }

        private inline fun runOnMainThread(crossinline block: () -> Unit) {
            if (mainHandler.looper.isCurrentThread) {
                block()
            } else {
                mainHandler.post { block() }
            }
        }
    }
}