/*********************************************************************
 * * Copyright (C), 2010-2023 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AddFilePanelFragmentTest.kt
 * * Description : AddFilePanelFragmentTest unit test
 * * Version     : 1.0
 * * Date        : 2024/2/20
 * * Author      : W9059186
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    W9059186                 2024/2/20     1.0     AddFilePanelFragmentTest unit test
 *  ***********************************************************************/
package com.oplus.filemanager.filelabel.controller

import android.app.Activity
import android.content.res.Resources
import android.widget.RelativeLayout
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.utils.StatusBarUtil
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AddFilePanelFragmentTest {
    private lateinit var addFilePanelFragment: AddFilePanelFragment
    private lateinit var loadingController: LoadingController
    private lateinit var contentLayout: RelativeLayout
    private lateinit var activity: Activity
    private lateinit var resources: Resources
    @Before
    fun setup() {
        addFilePanelFragment = mockk()
        loadingController = mockk()
        contentLayout = mockk()
        activity = mockk()
        every { addFilePanelFragment.loadingController }.returns(loadingController)
        every { addFilePanelFragment.contentLayout }.returns(contentLayout)
        every { activity.resources }.returns(mockk())
    }

    @Test
    fun should_when_showLoadingView() {
        every { addFilePanelFragment.showLoadingView(any()) }.answers { callOriginal() }
        justRun { addFilePanelFragment.showLoadingView(true) }
        addFilePanelFragment.showLoadingView(true)

        justRun { addFilePanelFragment.showLoadingView(false) }
        addFilePanelFragment.showLoadingView(false)
    }

    @Test
    fun should_when_handleGestureNavModeHeight() {
        every { addFilePanelFragment.handleGestureNavModeHeight(any(), any()) }.answers { callOriginal() }
        mockkStatic(COUIPanelMultiWindowUtils::class)
        every { COUIPanelMultiWindowUtils.isDisplayInUpperWindow(any()) }.answers { true }
        Assert.assertEquals(addFilePanelFragment.handleGestureNavModeHeight(activity, 100), 100)

        every { COUIPanelMultiWindowUtils.isDisplayInUpperWindow(any()) }.answers { false }
        mockkStatic(StatusBarUtil::class)
        every { StatusBarUtil.checkShowGestureNavBar(any()) }.answers { true }
        every { activity.resources.getDimensionPixelOffset(any()) }.answers { 10 }
        Assert.assertNotEquals(addFilePanelFragment.handleGestureNavModeHeight(activity, 100), 100)

        every { COUIPanelMultiWindowUtils.isDisplayInUpperWindow(any()) }.answers { false }
        every { StatusBarUtil.checkShowGestureNavBar(any()) }.answers { false }
        Assert.assertEquals(addFilePanelFragment.handleGestureNavModeHeight(activity, 100), 100)
    }
}