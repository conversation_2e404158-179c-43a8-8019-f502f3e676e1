/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - LegacyDataRetriever.java
 * Description: LegacyDataRetriever.java
 * Version: 1.0
 * Date : 2020/02/24
 * Author: <PERSON><PERSON><PERSON>.<PERSON>
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/02/24    1.0     create
 ****************************************************************/
package com.filemanager.recyclebin.retriever

import android.content.ContentValues
import android.net.Uri
import android.provider.MediaStore
import com.filemanager.common.MyApplication
import com.filemanager.common.RecycleStore
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.constants.KtConstants.LOCAL_VOLUME_MULTI_APP_PATH
import com.filemanager.common.utils.Log
import com.filemanager.recyclebin.db.RecycleBinRecordHelper
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_ERROR
import java.io.File


open class LegacyDataRetriever internal constructor() : DataRetriever() {

    private companion object {
        private const val TAG = "LegacyDataRetriever"
        private const val SCHEME_FILE = "file"

        @JvmStatic
        private fun extractDataModify(file: File?): Long {
            return file?.lastModified() ?: 0
        }
    }

    override fun deleteQuietly(originPath: String): Boolean {
        val result = delete(originPath)
        Log.d(TAG, "deleteQuietly RESULT: $result")
        return result
    }

    override fun deleteQuietly(originUri: Uri): Boolean {
        if (SCHEME_FILE != originUri.authority) {
            return false
        }
        val originPath = originUri.path ?: run {
            Log.e(TAG, "deleteQuietly ERROR: path is null in originUri $originUri")
            return false
        }
        return deleteQuietly(originPath)
    }

    override fun isInternalPath(path: String?): Boolean {
        path?.let {
            return (it.startsWith(VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext))
                    || it.startsWith(LOCAL_VOLUME_MULTI_APP_PATH, true))
        }
        return false
    }

    override fun isInternalUri(uri: Uri?): Boolean {
        uri?.let {
            return isInternalPath(uri.path)
        }
        return false
    }

    override fun extractContentValues(uri: Uri): ContentValues? {
        val scheme = uri.authority
        if (SCHEME_FILE == scheme) {
            val uriPath = uri.path
            return extractContentValues(uriPath!!)
        } else if (MediaStore.AUTHORITY == scheme) {
        } else {
            Log.w(TAG, "extractContentValues authority not support")
        }
        return null
    }

    override fun extractContentValues(path: String): ContentValues? {
        val file = File(path)
        if (!file.exists()) {
            Log.w(TAG, "extractContentValues file not exist")
            return null
        }
        return RecycleBinRecordHelper.extractContentValues(file.absolutePath, extractDataModify(file))
    }

    override fun moveToRecycleBin(originPath: String, recyclePath: String): Int {
        val originFile = File(originPath)
        val recycleFile = File(recyclePath)
        Log.d(TAG, "moveToRecycleBin originFile: " + originFile.absolutePath + " recycleFile: " + recycleFile)
        val statusCode = innerRecyclerRenameTo(originFile, recycleFile, false)
        Log.d(TAG, "moveToRecycleBin statusCode: $statusCode")
        return statusCode
    }

    override fun moveToRecycleBin(originUri: Uri, recyclePath: String): Int {
        throw UnsupportedOperationException()
    }

    override fun restoreToOriginPath(values: ContentValues): Int {
        // if file or folder not exist in recycle bin
        Log.w(TAG, "restoreToOriginPath")
        val originPath = values.getAsString(RecycleStore.Files.FileColumns.ORIGIN_PATH)
        val recyclePath = values.getAsString(RecycleStore.Files.FileColumns.RECYCLE_PATH)
        Log.w(TAG, "restoreToOriginPath originPath $originPath recyclePath $recyclePath")
        var finalOriginPath: String = originPath
        val recycleFile = File(recyclePath)
        if (!recycleFile.exists()) {
            Log.w(TAG, "restoreToOriginPath recycle file not exist")
            return STATUS_ERROR
        }

        var originFile = File(originPath)
        if (originFile.exists()) {
            Log.w(TAG, "restoreToOriginPath originFile exist")
            val parentPath = originFile.parent ?: run {
                Log.e(TAG, "restoreToOriginPath: ERROR parent is null, originFile=$originFile")
                return STATUS_ERROR
            }
            val index = originPath.lastIndexOf(File.separator)
            if (index < 0) {
                Log.w(TAG, "restoreToOriginPath extract displayname failed")
                return STATUS_ERROR
            }
            val displayName = originPath.substring(index)
            val newPath = RecycleStore.ensureRestoreOriginPath(parentPath, displayName, originFile.isDirectory)
            originFile = File(newPath)
            finalOriginPath = originFile.absolutePath
            values.put(RecycleStore.Files.FileColumns.ORIGIN_PATH, finalOriginPath)
        } else {
            // make dirs first
            Log.w(TAG, "restoreToOriginPath originFile not exit")
            val parentFile = originFile.parentFile
            Log.w(TAG, "restoreToOriginPath parentFile $parentFile")
            if (parentFile != null) {
                if (!parentFile.exists()) {
                    Log.w(TAG, "restoreToOriginPath parentFile parentFile exit not")
                    if (!parentFile.mkdirs()) {
                        Log.w(TAG, "restoreToOriginPath mkdirs failed")
                        return STATUS_ERROR
                    }
                }
            } else {
                return STATUS_ERROR
            }
        }
        return innerRecyclerRenameTo(recycleFile, originFile, true)
    }
}
