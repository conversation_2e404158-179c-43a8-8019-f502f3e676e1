package com.oplus.filemanager.filechoose.ui.singlepicker

import android.app.Activity
import android.content.ClipData
import android.content.Intent
import android.net.Uri
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.path.FileBrowPathHelper
import com.filemanager.common.path.FilePathHelper
import com.filemanager.common.utils.Utils
import com.oplus.selectdir.filebrowser.SelectFileBrowserLoader
import com.oplus.selectdir.filebrowser.SelectFileBrowserViewModel
import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File
import java.util.*

/**
 * SinglePickerFragmentViewModel的单元测试类
 * 用于测试SinglePickerFragmentViewModel的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SinglePickerFragmentViewModelTest {

    // 使用InstantTaskExecutorRule确保LiveData的同步执行
    @get:Rule
    val instantExecutorRule = InstantTaskExecutorRule()

    // 使用RelaxedMockK创建LoaderController的模拟对象
    @RelaxedMockK
    lateinit var mockLoaderController: LoaderController

    // 创建BaseVMActivity的模拟对象
    @MockK
    lateinit var mockActivity: BaseVMActivity

    // 创建SinglePickerActivity的模拟对象
    @MockK
    lateinit var mockSinglePickerActivity: SinglePickerActivity

    // 待测试的ViewModel实例
    private lateinit var viewModel: SinglePickerFragmentViewModel

    // 使用StandardTestDispatcher作为协程调度器
    @OptIn(ExperimentalCoroutinesApi::class)
    private val testDispatcher = StandardTestDispatcher()

    /**
     * 测试前的初始化方法
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    @Before
    fun setUp() {
        // 初始化MockK注解
        MockKAnnotations.init(this)
        // 创建被测试的ViewModel实例
        viewModel = spyk(SinglePickerFragmentViewModel())
        // 模拟UriHelper对象
        mockkObject(UriHelper)
        // 模拟MyApplication对象
        mockkObject(MyApplication)
        // 设置MyApplication的sAppContext返回模拟对象
        every { MyApplication.sAppContext } returns mockk(relaxed = true)
        // 模拟Utils类的静态方法
        mockkStatic(Utils::class)
        // 设置Utils.isQuickClick总是返回false
        every { Utils.isQuickClick(any()) } returns false
        
        // 设置主调度器为测试调度器
        Dispatchers.setMain(testDispatcher)
    }

    /**
     * 测试后的清理方法
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    @After
    fun tearDown() {
        // 恢复主调度器
        Dispatchers.resetMain()
        // 解除所有模拟
        unmockkAll()
    }

    /**
     * 测试getRealFileSize方法
     * 当uiState不为null时，应返回keyMap的大小
     */
    @Test
    fun `getRealFileSize should return size of keyMap when uiState is not null`() {
        // Given - 准备测试数据
        val keyMap = hashMapOf<Int, BaseFileBean>()
        keyMap[1] = mockk()
        keyMap[2] = mockk()
        val uiModel = mockk<SinglePickerFragmentViewModel.SinglePickerUiModel>()
        every { uiModel.keyMap } returns keyMap
        viewModel.uiState.value = uiModel

        // When - 执行测试方法
        val result = viewModel.getRealFileSize()

        // Then - 验证结果
        assertEquals(2, result)
    }

    /**
     * 测试getRealFileSize方法
     * 当uiState为null时，应返回0
     */
    @Test
    fun `getRealFileSize should return 0 when uiState is null`() {
        // Given - 设置uiState为null
        viewModel.uiState.value = null

        // When - 执行测试方法
        val result = viewModel.getRealFileSize()

        // Then - 验证结果
        assertEquals(0, result)
    }

    /**
     * 测试loadData方法
     * 该方法应该不执行任何操作
     */
    @Test
    fun `loadData should do nothing`() {
        // When & Then - 执行方法并验证无异常
        viewModel.loadData()
        // 不应抛出任何异常
    }

    /**
     * 测试initLoader方法
     * 当isNeedInit为false且loader存在时，应调用loadData方法
     */
    @Test
    fun `initLoader should load data when isNeedInit is false and loader exists`() {
        // Given - 准备模拟对象
        val path = "/test/path"
        val mockLoaderCallback = mockk<SinglePickerFragmentViewModel.FileBrowserCallBack>(relaxed = true)
        every { mockLoaderCallback.getLoader() } returns mockk()
        // 使用反射设置私有字段
        val loaderCallbackField = SinglePickerFragmentViewModel::class.java.getDeclaredField("mLoaderCallback")
        loaderCallbackField.isAccessible = true
        loaderCallbackField.set(viewModel, mockLoaderCallback)

        // When - 执行测试方法
        viewModel.initLoader(mockLoaderController, path, false)

        // Then - 验证是否调用了loadData方法
        verify { mockLoaderCallback.loadData() }
    }

    /**
     * 测试handleLoadComplete方法
     * 应更新mUiDataState和mPositionModel
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    @Test
    fun `handleLoadComplete should update mUiDataState and mPositionModel`() = runTest {
        // Given - 准备测试数据
        val fileList = listOf<BaseFileBean>(mockk(), mockk())
        val positionModel = mockk<SelectFileBrowserViewModel.PositionModel>()
        // 使用反射设置私有字段
        val pushPathInfoField = SinglePickerFragmentViewModel::class.java.getDeclaredField("mPushPathInfo")
        pushPathInfoField.isAccessible = true
        pushPathInfoField.set(viewModel, positionModel)
        every { positionModel.mCurrentPath } returns "/current/path"

        // When - 使用反射调用私有方法
        val method = SinglePickerFragmentViewModel::class.java.getDeclaredMethod("handleLoadComplete", List::class.java, SinglePickerFragmentViewModel::class.java)
        method.isAccessible = true
        method.invoke(viewModel, fileList, viewModel)

        // Then - 验证结果
        assertEquals("/current/path", viewModel.mCurrentPath)
        assertNotNull(viewModel.mUiDataState.value)
        assertEquals(fileList.size, viewModel.mUiDataState.value?.size)
    }

    /**
     * 测试initPathHelper方法
     * 当mInitPath已设置时，不应重新初始化
     */
    @Test
    fun `initPathHelper should not reinitialize when mInitPath is already set`() {
        // Given - 准备测试数据
        val currentPath = "/test/current/path"
        val initPath = "/init/path"
        // 使用反射设置私有字段
        val initPathField = SinglePickerFragmentViewModel::class.java.getDeclaredField("mInitPath")
        initPathField.isAccessible = true
        initPathField.set(viewModel, initPath)
        val pathHelpField = SinglePickerFragmentViewModel::class.java.getDeclaredField("mPathHelp")
        pathHelpField.isAccessible = true
        pathHelpField.set(viewModel, mockk<FileBrowPathHelper>())

        // When - 执行测试方法
        viewModel.initPathHelper(currentPath)

        // Then - 验证结果
        assertEquals(currentPath, viewModel.mCurrentPath)
        assertEquals(initPath, viewModel.mInitPath)
    }

    /**
     * 测试onItemClick方法
     * 当position超出范围时应提前返回
     */
    @Test
    fun `onItemClick should return early when position is out of bounds`() {
        // Given - 准备测试数据
        viewModel.mUiDataState.value = listOf(mockk(), mockk())
        val position = 5

        // When - 执行测试方法
        viewModel.onItemClick(mockActivity, position, 0, 0, false)

        // Then - 验证没有抛出异常
    }

    /**
     * 测试FileBrowserCallBack的loadData方法
     * 当提供path时应调用forceLoad
     */
    @Test
    fun `FileBrowserCallBack loadData should call forceLoad when path is provided`() {
        // Given - 准备测试数据
        val callBack = SinglePickerFragmentViewModel.FileBrowserCallBack(viewModel)
        val loader = mockk<SelectFileBrowserLoader>(relaxed = true)
        // 使用反射设置私有字段
        val loaderField = SinglePickerFragmentViewModel.FileBrowserCallBack::class.java.superclass.getDeclaredField("mLoader")
        loaderField.isAccessible = true
        loaderField.set(callBack, loader)

        // When - 执行测试方法
        callBack.loadData("/test/path", true)

        // Then - 验证结果
        assertTrue(callBack.isLoadNewPath())
        verify { loader.setPath("/test/path") }
        verify { loader.forceLoad() }
    }

    /**
     * 测试FileBrowserCallBack的onCreateLoader方法
     * 应创建SelectFileBrowserLoader实例
     */
    @Test
    fun `FileBrowserCallBack onCreateLoader should create SelectFileBrowserLoader`() {
        // Given - 准备测试数据
        val callBack = SinglePickerFragmentViewModel.FileBrowserCallBack(viewModel)
        viewModel.mPathHelp = mockk()
        val pathInfo = FilePathHelper.PathInfo("/test/path")
        every { viewModel.mPathHelp?.getPath(0) } returns pathInfo
        every { viewModel.mPathHelp?.getCount() } returns 1

        // When - 执行测试方法
        val loader = callBack.onCreateLoader(viewModel)

        // Then - 验证结果
        assertNotNull(loader)
        assertTrue(loader is SelectFileBrowserLoader)
    }
}