/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.coloros.filemanager.common.view
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/7/6
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *     zhengshuai           2022/7/6         1
 ***********************************************************************/
package com.filemanager.common.view

import android.content.Context
import androidx.core.content.ContextCompat
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class FileThumbViewTest {

    @MockK(relaxed = true)
    private lateinit var mFileThumbView: FileThumbView

    private lateinit var context: Context

    @Before
    fun setUp() {
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
        MockKAnnotations.init(this)
    }

    @Test
    fun `should return equals when set border style`() {
        every { mFileThumbView.setBorderStyle(any(), any()) } answers { callOriginal() }
        every { mFileThumbView.mBorderRadius } answers { callOriginal() }
        every { mFileThumbView.mBorderSize } answers { callOriginal() }
        every { mFileThumbView.mBorderColor } answers { callOriginal() }
        mFileThumbView.setBorderStyle(10f, 20f)
        Assert.assertEquals(10f, mFileThumbView.mBorderRadius)
        Assert.assertEquals(20f, mFileThumbView.mBorderSize)
        Assert.assertEquals(ContextCompat.getColor(context, R.color.color_text_ripple_bg_color), mFileThumbView.mBorderColor)
    }

    @Test
    fun `should return equals when set stroke style`() {
        every { mFileThumbView.mStrokeStyle } answers { callOriginal() }
        every { mFileThumbView.setStrokeStyle(any()) } answers { callOriginal() }
        mFileThumbView.setStrokeStyle(10)
        Assert.assertEquals(10, mFileThumbView.mStrokeStyle)
    }
}