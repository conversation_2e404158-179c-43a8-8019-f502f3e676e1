/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.cardwidget.label
 * * Description : 桌面卡片缓存在硬盘的数据，保存卡片与标签的的映射
 * * Version     : 1.0
 * * Date        : 2022/12/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.data

data class LabelCardCacheData(
    var widgetCode: String,
    var labelId: Long?,
    var cardStatus: Int = 0
) {
    companion object {
        const val STATUS_INIT = 0
        const val STATUS_NORMAL = 1
    }
}