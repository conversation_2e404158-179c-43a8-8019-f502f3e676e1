package com.oplus.filemanager.compat.brand.realme

import android.app.Activity
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.recyclerview.widget.RecyclerView
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAdView
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.utils.AdHelper
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.lang.reflect.Field

/**
 * RealmeSubMaxAdMgr的单元测试类
 * 用于测试Realme品牌下二级页面广告管理器的功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RealmeSubMaxAdMgrTest {

    // 测试类中使用的mock对象
    private lateinit var realmeSubMaxAdMgr: RealmeSubMaxAdMgr
    private lateinit var mockActivity: Activity
    private lateinit var mockAdapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>
    private lateinit var mockFileList: ArrayList<MediaFileWrapper>
    private lateinit var mockAdView: MaxAdView
    private lateinit var mockAdLayout: RelativeLayout
    private lateinit var mockMaxAd: MaxAd
    private lateinit var mockMaxError: MaxError
    
    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 创建各种mock对象
     * 3. 设置静态方法的mock行为
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        // 模拟AdHelper单例对象
        mockkObject(AdHelper)
        // 模拟MyApplication单例对象
        mockkObject(MyApplication)
        // 模拟Log类的静态方法
        mockkStatic(Log::class)
        // 模拟StatisticsUtils类的静态方法
        mockkStatic(StatisticsUtils::class)
        
        // 设置Log.d和Log.e的默认行为
        every { Log.d(any<String>(), any<String>()) } just Runs
        every { Log.e(any<String>(), any<String>()) } just Runs
        // 设置MyApplication.appContext返回mock对象
        every { MyApplication.appContext } returns mockk(relaxed = true)
        // 设置StatisticsUtils相关方法的默认行为
        every { StatisticsUtils.onCommon(any(), any<String>(), any<String>(), any()) } just Runs
        every { StatisticsUtils.statisticsAdRequestFlow(any(), any<String>(), any<String>(), any()) } just Runs
        every { StatisticsUtils.statisticsAdReturnFlow(any(), any<Boolean>(), any<String>(), any<String>(), any(), any()) } just Runs
        every { StatisticsUtils.statisticsAdShowFlow(any(), any<String>(), any<String>(), any(), any(), any()) } just Runs
        every { StatisticsUtils.statisticsAdClick(any(), any<String>(), any<String>(), any(), any(), any()) } just Runs
        
        // 直接模拟ModelUtils.isTablet()方法而不使用mockkObject
        mockkStatic(ModelUtils::class)
        every { ModelUtils.isTablet() } returns false
        
        // 创建被测对象的spy实例
        realmeSubMaxAdMgr = spyk(RealmeSubMaxAdMgr())
        // 创建各种mock对象
        mockActivity = mockk(relaxed = true)
        mockAdapter = mockk(relaxed = true)
        mockFileList = arrayListOf(mockk())
        mockAdView = mockk(relaxed = true)
        mockAdLayout = mockk(relaxed = true)
        mockMaxAd = mockk(relaxed = true)
        mockMaxError = mockk(relaxed = true)
        
        // 模拟资源相关方法
        val resources = mockk<android.content.res.Resources>()
        every { mockActivity.resources } returns resources
        // 模拟尺寸资源
        every { resources.getDimensionPixelSize(R.dimen.dimen_50dp) } returns 50
        every { resources.getDimensionPixelSize(R.dimen.dimen_90dp) } returns 90
        // 添加对getDisplayMetrics的mock以解决测试问题
        val displayMetrics = android.util.DisplayMetrics()
        displayMetrics.density = 1.0f
        every { resources.displayMetrics } returns displayMetrics
        // 修复测试错误，mock getBoolean方法
        every { resources.getBoolean(any()) } returns true
    }
    
    /**
     * 测试后的清理方法
     * 1. 解除所有mock
     * 2. 清除所有mock状态
     */
    @After
    fun tearDown() {
        unmockkAll()
        clearAllMocks()
    }
    
    /**
     * 辅助方法：设置对象的私有字段值
     * @param obj 目标对象
     * @param fieldName 字段名
     * @param value 要设置的值
     */
    private fun setPrivateField(obj: Any, fieldName: String, value: Any?) {
        val field: Field = obj.javaClass.getDeclaredField(fieldName)
        field.isAccessible = true
        field.set(obj, value)
    }
    
    /**
     * 辅助方法：获取对象的私有字段值
     * @param obj 目标对象
     * @param fieldName 字段名
     * @return 字段值
     */
    private fun getPrivateField(obj: Any, fieldName: String): Any? {
        val field: Field = obj.javaClass.getDeclaredField(fieldName)
        field.isAccessible = true
        return field.get(obj)
    }
    
    /**
     * 辅助方法：设置基类中的name字段
     * @param obj 目标对象
     * @param name 要设置的名称
     */
    private fun setNameField(obj: Any, name: String) {
        val baseClass = obj.javaClass.superclass
        val nameField = baseClass.getDeclaredField("name")
        nameField.isAccessible = true
        nameField.set(obj, name)
    }
    
    /**
     * 测试makeName方法
     * 验证是否能正确地在名称后追加递增的数字
     */
    @Test
    fun `makeName should append incrementing count to name`() {
        val name1 = realmeSubMaxAdMgr.makeName("test")
        val name2 = realmeSubMaxAdMgr.makeName("test")
        
        assert(name1 == "test1")
        assert(name2 == "test2")
    }
    
    /**
     * 测试requestSubAd方法
     * 当subAdSwitch为false时应提前返回
     */
    @Test
    fun `requestSubAd should return early when subAdSwitch is false`() {
        every { AdHelper.subAdSwitch } returns false
        
        realmeSubMaxAdMgr.requestSubAd(mockActivity, "test", mockAdapter, mockFileList, false)
        
        verify { Log.e(AdvertEntry.AD_TAG, "mdp sub ad switch is closed") }
        verify(exactly = 0) { mockAdLayout.addView(any(), any<FrameLayout.LayoutParams>()) }
    }
    
    /**
     * 测试scanModeChange方法
     * 当adLayout可见且有子视图时应调用onLoadSuccess
     */
    @Test
    fun `scanModeChange should call onLoadSuccess when adLayout is visible and has children`() {
        // 设置adLayout状态
        realmeSubMaxAdMgr = spyk(RealmeSubMaxAdMgr())
        val mockMultiFmAdEntityItem = mockk<MultiFmAdEntityItem>(relaxed = true)
        setPrivateField(realmeSubMaxAdMgr, "adLayout", mockAdLayout)
        setPrivateField(realmeSubMaxAdMgr, "multiFmAdEntityItem", mockMultiFmAdEntityItem)
        
        every { mockAdLayout.visibility } returns View.VISIBLE
        every { mockAdLayout.childCount } returns 1
        
        realmeSubMaxAdMgr.scanModeChange("test", true)
        
        verify { mockMultiFmAdEntityItem.setNeedHideAD(true) }
        verify { mockMultiFmAdEntityItem.onLoadSuccess() }
    }
    
    /**
     * 测试scanModeChange方法
     * 当adLayout不可见时不应调用onLoadSuccess
     */
    @Test
    fun `scanModeChange should not call onLoadSuccess when adLayout is not visible`() {
        val mockMultiFmAdEntityItem = mockk<MultiFmAdEntityItem>(relaxed = true)
        setPrivateField(realmeSubMaxAdMgr, "adLayout", mockAdLayout)
        setPrivateField(realmeSubMaxAdMgr, "multiFmAdEntityItem", mockMultiFmAdEntityItem)
        
        every { mockAdLayout.visibility } returns View.GONE
        every { mockAdLayout.childCount } returns 1
        
        realmeSubMaxAdMgr.scanModeChange("test", true)
        
        verify(exactly = 0) { mockMultiFmAdEntityItem.onLoadSuccess() }
    }
    
    /**
     * 测试getEntryView方法
     * 应返回adLayout对象
     */
    @Test
    fun `getEntryView should return adLayout`() {
        setPrivateField(realmeSubMaxAdMgr, "adLayout", mockAdLayout)
        
        val result = realmeSubMaxAdMgr.getEntryView("test")
        
        assert(result === mockAdLayout)
    }
    
    /**
     * 测试eventDestroy方法
     * 应正确清理广告组件
     */
    @Test
    fun `eventDestroy should clean up ad components`() {
        setPrivateField(realmeSubMaxAdMgr, "adLayout", mockAdLayout)
        setPrivateField(realmeSubMaxAdMgr, "adView", mockAdView)
        
        realmeSubMaxAdMgr.eventDestroy()
        
        verify { mockAdLayout.removeAllViews() }
        verify { mockAdView.destroy() }
        verify { mockAdLayout.visibility = View.GONE }
    }
    
    /**
     * 测试onAdLoaded方法
     * 应调用onLoadSuccess并上报统计信息
     */
    @Test
    fun `onAdLoaded should call onLoadSuccess and report statistics`() {
        val mockMultiFmAdEntityItem = mockk<MultiFmAdEntityItem>(relaxed = true)
        setPrivateField(realmeSubMaxAdMgr, "multiFmAdEntityItem", mockMultiFmAdEntityItem)
        
        // 设置name字段以匹配验证中的值
        setNameField(realmeSubMaxAdMgr, "oplus")
        every { AdHelper.adSource } returns "oplus"
        
        realmeSubMaxAdMgr.onAdLoaded(mockMaxAd)
        
        verify { Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdLoaded") }
        verify { mockMultiFmAdEntityItem.onLoadSuccess() }
        verify { StatisticsUtils.onCommon(any(), StatisticsUtils.EVENT_AD_LOAD_TAG, StatisticsUtils.REALME_FILE_EVENT, any()) }
        verify { StatisticsUtils.statisticsAdReturnFlow(any(), true, "oplus", "oplus", null, null) }
    }
    
    /**
     * 测试onAdDisplayed方法
     * 应上报统计信息
     */
    @Test
    fun `onAdDisplayed should report statistics`() {
        // 设置name字段以匹配验证中的值
        setNameField(realmeSubMaxAdMgr, "oplus")
        every { AdHelper.adSource } returns "oplus"
        
        realmeSubMaxAdMgr.onAdDisplayed(mockMaxAd)
        
        verify { Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdDisplayed") }
        verify { StatisticsUtils.onCommon(any(), StatisticsUtils.EVENT_AD_LOAD_TAG, StatisticsUtils.REALME_FILE_EVENT, any()) }
        verify { StatisticsUtils.statisticsAdShowFlow(any(), "oplus", "oplus", null, null, null) }
    }
    
    /**
     * 测试onAdHidden方法
     * 应记录日志信息
     */
    @Test
    fun `onAdHidden should log message`() {
        realmeSubMaxAdMgr.onAdHidden(mockMaxAd)
        
        verify { Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdHidden") }
    }
    
    /**
     * 测试onAdClicked方法
     * 应上报统计信息
     */
    @Test
    fun `onAdClicked should report statistics`() {
        // 设置name字段以匹配验证中的值
        setNameField(realmeSubMaxAdMgr, "oplus")
        every { AdHelper.adSource } returns "oplus"
        
        realmeSubMaxAdMgr.onAdClicked(mockMaxAd)
        
        verify { StatisticsUtils.onCommon(any(), StatisticsUtils.EVENT_AD_LOAD_TAG, StatisticsUtils.REALME_FILE_EVENT, any()) }
        verify { StatisticsUtils.statisticsAdClick(any(), "oplus", "oplus", null, null, null) }
        verify { Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdClicked") }
    }
    
    /**
     * 测试onAdLoadFailed方法
     * 应清理广告组件并上报统计信息
     */
    @Test
    fun `onAdLoadFailed should clean up ad components and report statistics`() {
        setPrivateField(realmeSubMaxAdMgr, "adLayout", mockAdLayout)
        
        // 设置name字段以匹配验证中的值
        setNameField(realmeSubMaxAdMgr, "oplus")
        every { AdHelper.adSource } returns "oplus"
        
        realmeSubMaxAdMgr.onAdLoadFailed("test", mockMaxError)
        
        verify { mockAdLayout.removeAllViews() }
        verify { mockAdLayout.visibility = View.GONE }
        verify { Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdLoadFailed:${mockMaxError.code}") }
        verify { Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdLoadFailed:${mockMaxError.message}") }
        verify { StatisticsUtils.onCommon(any(), StatisticsUtils.EVENT_AD_LOAD_TAG, StatisticsUtils.REALME_FILE_EVENT, any()) }
        verify { StatisticsUtils.statisticsAdReturnFlow(any(), false, "oplus", "oplus", null, null) }
    }
    
    /**
     * 测试onAdDisplayFailed方法
     * 应记录日志信息
     */
    @Test
    fun `onAdDisplayFailed should log message`() {
        every { mockMaxError.message } returns "Display failed"
        
        realmeSubMaxAdMgr.onAdDisplayFailed(mockMaxAd, mockMaxError)
        
        verify { Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdDisplayFailed:Display failed") }
    }
    
    /**
     * 测试onAdExpanded方法
     * 应记录日志信息
     */
    @Test
    fun `onAdExpanded should log message`() {
        realmeSubMaxAdMgr.onAdExpanded(mockMaxAd)
        
        verify { Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdExpanded") }
    }
    
    /**
     * 测试onAdCollapsed方法
     * 应记录日志信息
     */
    @Test
    fun `onAdCollapsed should log message`() {
        realmeSubMaxAdMgr.onAdCollapsed(mockMaxAd)
        
        verify { Log.d(AdvertEntry.AD_TAG, "max sub binner ad onAdCollapsed") }
    }
}