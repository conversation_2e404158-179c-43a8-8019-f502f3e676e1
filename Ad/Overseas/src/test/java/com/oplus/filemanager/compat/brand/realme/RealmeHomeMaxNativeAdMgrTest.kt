package com.oplus.filemanager.compat.brand.realme

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxError
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdListener
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.utils.AdHelper
import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * RealmeHomeMaxNativeAdMgr 的单元测试类
 * 用于测试 RealmeHomeMaxNativeAdMgr 类的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RealmeHomeMaxNativeAdMgrTest {

    @MockK
    private lateinit var mockActivity: Activity  // 模拟的Activity对象

    @RelaxedMockK
    private lateinit var mockNativeAdLoader: MaxNativeAdLoader  // 模拟的广告加载器

    @RelaxedMockK
    private lateinit var mockNativeAd: MaxAd  // 模拟的广告对象

    @RelaxedMockK
    private lateinit var mockNativeAdView: MaxNativeAdView  // 模拟的广告视图

    @RelaxedMockK
    private lateinit var mockMainFragmentContainer: FrameLayout  // 模拟的主Fragment容器

    @RelaxedMockK
    private lateinit var mockAdRootView: View  // 模拟的广告根视图

    private lateinit var adMgr: RealmeHomeMaxNativeAdMgr  // 被测试的广告管理器实例

    /**
     * 测试前的准备工作
     * 1. 初始化MockK注解
     * 2. 设置模拟Activity的基本行为
     * 3. 模拟静态类和对象
     * 4. 创建被测试对象并设置其属性
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        every { mockActivity.packageManager } returns mockk(relaxed = true)
        every { mockActivity.packageName } returns "com.oplus.filemanager"
        mockkStatic(Log::class)
        mockkStatic(StatisticsUtils::class)
        mockkObject(AdHelper)
        adMgr = spyk(RealmeHomeMaxNativeAdMgr())
        adMgr.mMainFragmentContainer = mockMainFragmentContainer
        adMgr.adRootView = mockAdRootView
    }

    /**
     * 测试后的清理工作
     * 解除所有模拟
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试当homeAdSwitch为false时不应请求广告
     * 验证:
     * 1. 应记录错误日志
     * 2. nativeAdLoader应为null
     * 3. isRequesting应为false
     */
    @Test
    fun `test requestMainAd when homeAdSwitch is false should not request ad`() {
        // Given - 设置测试条件: 广告开关关闭
        every { AdHelper.homeAdSwitch } returns false

        // When - 执行测试方法
        adMgr.requestMainAd(mockActivity)

        // Then - 验证结果
        verify { Log.e(any(), "mdp home ad switch is closed") }
        assertNull(adMgr.getNativeAdLoaderForTest())
        assertFalse(adMgr.getIsRequestingForTest())
    }

    /**
     * 测试当已经在请求广告时不应重复请求
     * 验证:
     * 1. 应记录调试日志
     * 2. nativeAdLoader应为null
     */
    @Test
    fun `test requestMainAd when already requesting should not request again`() {
        // Given - 设置测试条件: 广告开关打开且正在请求中
        every { AdHelper.homeAdSwitch } returns true
        adMgr.setIsRequestingForTest(true)

        // When - 执行测试方法
        adMgr.requestMainAd(mockActivity)

        // Then - 验证结果
        verify { Log.d(any(), "native ad is requesting, ignore") }
        assertNull(adMgr.getNativeAdLoaderForTest())
    }

    /**
     * 测试销毁事件应清理资源
     * 验证:
     * 1. 应销毁广告
     * 2. 应清除所有视图
     * 3. 应销毁广告加载器
     */
    @Test
    fun `test eventOnDestroy should clean up resources`() {
        // Given - 设置测试条件: 有广告和广告加载器
        adMgr.setNativeAdLoaderForTest(mockNativeAdLoader)
        adMgr.setNativeAdForTest(mockNativeAd)

        // When - 执行测试方法
        adMgr.eventOnDestroy()

        // Then - 验证结果
        verify { mockNativeAdLoader.destroy(mockNativeAd) }
        verify { mockMainFragmentContainer.removeAllViews() }
        verify { mockNativeAdLoader.destroy() }
    }

    /**
     * 辅助方法: 捕获广告监听器
     * 用于测试广告加载器的回调
     * @return 捕获到的MaxNativeAdListener实例
     */
    private fun captureListener(): MaxNativeAdListener {
        mockkConstructor(MaxNativeAdLoader::class)
        every { anyConstructed<MaxNativeAdLoader>().setNativeAdListener(any()) } answers {
            firstArg<MaxNativeAdListener>()
        }
        every { anyConstructed<MaxNativeAdLoader>().loadAd(any()) } returns Unit
        every { AdHelper.homeAdSwitch } returns true

        adMgr.requestMainAd(mockActivity)
        return slot<MaxNativeAdListener>().let {
            verify { anyConstructed<MaxNativeAdLoader>().setNativeAdListener(capture(it)) }
            it.captured
        }
    }
}

/**
 * 测试辅助扩展函数 - 获取nativeAdLoader私有属性
 */
private fun RealmeHomeMaxNativeAdMgr.getNativeAdLoaderForTest() = this::class.java.getDeclaredField("nativeAdLoader").let {
    it.isAccessible = true
    it.get(this) as? MaxNativeAdLoader
}

/**
 * 测试辅助扩展函数 - 设置nativeAdLoader私有属性
 */
private fun RealmeHomeMaxNativeAdMgr.setNativeAdLoaderForTest(loader: MaxNativeAdLoader) {
    this::class.java.getDeclaredField("nativeAdLoader").let {
        it.isAccessible = true
        it.set(this, loader)
    }
}

/**
 * 测试辅助扩展函数 - 获取nativeAd私有属性
 */
private fun RealmeHomeMaxNativeAdMgr.getNativeAdForTest() = this::class.java.getDeclaredField("nativeAd").let {
    it.isAccessible = true
    it.get(this) as? MaxAd
}

/**
 * 测试辅助扩展函数 - 设置nativeAd私有属性
 */
private fun RealmeHomeMaxNativeAdMgr.setNativeAdForTest(ad: MaxAd) {
    this::class.java.getDeclaredField("nativeAd").let {
        it.isAccessible = true
        it.set(this, ad)
    }
}

/**
 * 测试辅助扩展函数 - 获取isRequeting私有属性
 */
private fun RealmeHomeMaxNativeAdMgr.getIsRequestingForTest() = this::class.java.getDeclaredField("isRequeting").let {
    it.isAccessible = true
    it.get(this) as Boolean
}

/**
 * 测试辅助扩展函数 - 设置isRequeting私有属性
 */
private fun RealmeHomeMaxNativeAdMgr.setIsRequestingForTest(requesting: Boolean) {
    this::class.java.getDeclaredField("isRequeting").let {
        it.isAccessible = true
        it.set(this, requesting)
    }
}