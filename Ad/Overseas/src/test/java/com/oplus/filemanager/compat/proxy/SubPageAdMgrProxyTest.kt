/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : SubPageAdMgrProxyTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/3/22 19:36
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2023/3/22       1.0      create
 **********************************************************************/
package com.oplus.filemanager.compat.proxy

import android.app.Activity
import android.widget.RelativeLayout
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.compat.interfaces.ISubPageAdMgr
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import org.junit.Test

class SubPageAdMgrProxyTest {

    @Test
    fun testSubPageAdMgr() {
        val fakeSubAdMgr = spyk<FakeSubAdMgr>()
        val subPageAdMgrProxy = SubPageAdMgrProxy(fakeSubAdMgr)
        subPageAdMgrProxy.makeName("")
        verify { fakeSubAdMgr.makeName("") }

        val activity = mockk<Activity>()
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>>()
        subPageAdMgrProxy.requestSubAd(activity, "", adapter, arrayListOf(), false)
        verify { fakeSubAdMgr.requestSubAd(any(), any(), any(), any(), any()) }

        subPageAdMgrProxy.scanModeChange("", false)
        verify { fakeSubAdMgr.scanModeChange("", false) }

        subPageAdMgrProxy.getEntryView("")
        verify { fakeSubAdMgr.getEntryView("") }

        subPageAdMgrProxy.requestCommonAd(activity, adapter, arrayListOf())
        verify { fakeSubAdMgr.requestCommonAd(any(), any(), any()) }

        val albumAdapter = mockk<BaseSelectionRecycleAdapter<*, AlbumItem>>()
        subPageAdMgrProxy.requestSubAlbumSetAd(activity, albumAdapter, arrayListOf())
        verify { fakeSubAdMgr.requestSubAlbumSetAd(any(), any(), any()) }

        val baseBeanAdapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>>()
        subPageAdMgrProxy.requestPhoneStorageAd(activity, baseBeanAdapter, arrayListOf())
        verify { fakeSubAdMgr.requestPhoneStorageAd(any(), any(), any()) }

        subPageAdMgrProxy.refreshIfListChanged()
        verify { fakeSubAdMgr.refreshIfListChanged() }

        subPageAdMgrProxy.refreshByScanModeChanged()
        verify { fakeSubAdMgr.refreshByScanModeChanged() }

        subPageAdMgrProxy.eventDestroy()
        verify { fakeSubAdMgr.eventDestroy() }
    }
}

class FakeSubAdMgr : ISubPageAdMgr {
    override fun makeName(name: String): String {
        return ""
    }

    override fun requestSubAd(
        activity: Activity,
        name: String,
        adapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>,
        fileList: ArrayList<MediaFileWrapper>,
        needHideAd: Boolean
    ) {
        // do nothing
    }

    override fun scanModeChange(name: String, needHideAd: Boolean) {
        // do nothing
    }

    override fun requestCommonAd(
        activity: Activity,
        adapter: BaseSelectionRecycleAdapter<*, MediaFileWrapper>,
        fileList: ArrayList<MediaFileWrapper>
    ) {
        // do nothing
    }

    override fun requestSubAlbumSetAd(
        activity: Activity,
        adapter: BaseSelectionRecycleAdapter<*, AlbumItem>,
        fileList: ArrayList<AlbumItem>
    ) {
        // do nothing
    }

    override fun requestPhoneStorageAd(
        activity: Activity,
        adapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>,
        fileList: ArrayList<BaseFileBean>
    ) {
        // do nothing
    }

    override fun getEntryView(name: String): RelativeLayout? {
        return null
    }

    override fun refreshIfListChanged() {
        // do nothing
    }

    override fun refreshByScanModeChanged() {
        // do nothing
    }

    override fun eventDestroy() {
        // do nothing
    }
}