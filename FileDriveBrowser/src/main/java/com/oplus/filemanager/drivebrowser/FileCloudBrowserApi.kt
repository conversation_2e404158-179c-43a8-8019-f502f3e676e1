/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileCloudDriveApi
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/11 14:34
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/11       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.activity.ComponentActivity
import androidx.annotation.DrawableRes
import androidx.fragment.app.Fragment
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.Constants
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.utils.Injector
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.drivebrowser.data.utils.ServerConfig
import com.oplus.filemanager.drivebrowser.ui.FileDriveActivity
import com.oplus.filemanager.drivebrowser.operate.DriveFileOperator
import com.oplus.filemanager.drivebrowser.operate.search.SearchDriveHelper
import com.oplus.filemanager.drivebrowser.ui.FileDriveFragment
import com.oplus.filemanager.drivebrowser.ui.vh.BaseCloudDocumentVH
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils
import com.oplus.filemanager.drivebrowser.utils.UniqueIdUtils
import com.oplus.filemanager.interfaze.filecloudbrowser.IFileCloudBrowser
import com.oplus.filemanager.interfaze.main.IMain
import java.util.function.Consumer

object FileCloudBrowserApi : IFileCloudBrowser {
    private const val TAG = "FileCloudDriveApi"

    override fun initStd(context: Context) {
        UniqueIdUtils.init(context)
    }

    override fun initDocsEnvironment() {
        ServerConfig.getInstance().initEnvironment()
    }

    override fun getFragment(activity: Activity): Fragment {
        return FileDriveFragment()
    }

    override fun getPickerFragment(activity: Activity?): Fragment? {
        return FileDriveFragment()
    }

    override fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater) {
        if (fragment is FileDriveFragment) {
            fragment.onCreateOptionsMenu(menu, inflater)
        }
    }

    override fun onMenuItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        if (fragment is FileDriveFragment) {
            return fragment.onOptionsItemSelected(item)
        }
        return false
    }

    override fun saveAuthorizationResult(code: String, state: String) {
        FileDriveStateUtils.saveTencentAuthorizationResult(code, state)
    }

    override fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        if (fragment is FileDriveFragment) {
            fragment.setIsHalfScreen(isHalfScreen)
        }
    }

    override fun pressBack(fragment: Fragment): Boolean {
        Log.d(TAG, "pressBack")
        if (fragment is FileDriveFragment) {
            return fragment.pressBack()
        }
        return false
    }

    override fun onNavigationItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onNavigationItemSelected")
        if (fragment is FileDriveFragment) {
            return fragment.onNavigationItemSelected(item)
        }
        return false
    }

    override fun fromSelectPathResult(fragment: Fragment, requestCode: Int, paths: List<String>?) {
        Log.d(TAG, "fromSelectPathResult")
        if (fragment is FileDriveFragment) {
            fragment.onSelect(requestCode, paths?.getOrNull(0))
        }
    }

    override fun startCloudDriveFragment(
        activity: Activity,
        itemType: Int,
        clearTask: Boolean,
        auth: Boolean
    ) {
        if (FeatureCompat.isSmallScreenPhone || FeatureCompat.isFlipDevice) {
            val intent = Intent().apply {
                putExtra(Constants.KEY_FILE_DRIVE_TYPE, itemType)
                putExtra(CommonConstants.KEY_IS_AUTHORIZING, auth)
                if (clearTask) {
                    addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                }
                setClass(activity, FileDriveActivity::class.java)
            }
            runCatching {
                activity.startActivity(intent)
            }.onFailure {
                Log.e(TAG, "startCloudDriveFragment ${it.message}")
            }
        } else {
            val bundle = Bundle()
            bundle.putInt(Constants.KEY_FILE_DRIVE_TYPE, itemType)
            bundle.putBoolean(CommonConstants.KEY_IS_AUTHORIZING, auth)
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.startFragment(activity, itemType, bundle)
        }
    }

    override fun startCloudDrive(activity: Activity, itemType: Int, auth: Boolean) {
        if (FeatureCompat.isSmallScreenPhone || FeatureCompat.isFlipDevice) {
            val intent = Intent().apply {
                putExtra(Constants.KEY_FILE_DRIVE_TYPE, itemType)
                putExtra(CommonConstants.KEY_IS_AUTHORIZING, auth)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                setClass(activity, FileDriveActivity::class.java)
            }
            runCatching {
                activity.startActivity(intent)
            }.onFailure {
                Log.e(TAG, "startCloudDriveFragment ${it.message}")
            }
        } else {
            val intent = Intent().apply {
                putExtra(Constants.KEY_FILE_DRIVE_TYPE, itemType)
                putExtra(CommonConstants.KEY_START_FILE_CLOUD_DRIVE, true)
                putExtra(CommonConstants.KEY_IS_AUTHORIZING, auth)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            }
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.startMainActivity(activity, intent)
        }
    }

    override fun onResumeLoadData(fragment: Fragment) {
        //do nothing
    }

    override fun permissionSuccess(fragment: Fragment) {
        //do nothing
    }

    override fun backToTop(fragment: Fragment) {
        //do nothing
    }

    override fun getCurrentPath(fragment: Fragment): String {
        return ""
    }

    override fun exitSelectionMode(fragment: Fragment) {}

    override fun onSideNavigationClicked(fragment: Fragment, isOpen: Boolean): Boolean {
        if (fragment is FileDriveFragment) {
            return fragment.onSideNavigationClicked(isOpen)
        }
        return false
    }

    override fun setToolbarAndTabListener(
        fragment: Fragment,
        toolbar: COUIToolbar?,
        title: String,
        tabListener: TabActivityListener<MediaFileWrapper>
    ) {
        //do nothing
    }

    override fun updateLabels(fragment: Fragment) {
        //do nothing
    }

    override fun setCurrentFilePath(fragment: Fragment, path: String?) {
        //do nothing
    }


    override fun searchDriveFile(searchKey: String, page: Int): List<BaseFileBean> {
        return SearchDriveHelper.search(searchKey, page)
    }

    /**
     * 获取云文档特殊的ICON资源
     */

    @DrawableRes
    override fun getCloudFileIconRes(mimeType: Int): Int {
        return BaseCloudDocumentVH.getCloudFileIconRes(mimeType)
    }

    /**
     * 打开金山文档
     */
    override fun openKDocsFile(context: Context, fileId: String, fileName: String, fileType: String) {
        DriveFileOperator.openKDocsFile(context, fileId, fileName, fileType)
    }

    /**
     * 打开腾讯文档
     */
    override fun openTencentDocs(context: Context, fileUrl: String, fileType: String) {
        DriveFileOperator.openTencentDocs(context, fileUrl, fileType)
    }

    /**
     * 下载云文档
     */
    override fun downloadCloudFile(activity: ComponentActivity, file: DriveFileWrapper, targetPath: String, consumer: Consumer<Int>) {
        DriveFileOperator.downloadCloudFile(activity, file, targetPath, consumer)
    }

    /**
     * 重命名云文档
     */
    override fun renameCloudFile(activity: ComponentActivity, file: DriveFileWrapper, consumer: Consumer<Pair<Int, String>>) {
        DriveFileOperator.renameCloudFile(activity, file, consumer)
    }

    /**
     * 删除云文档
     */
    override fun deleteCloudFiles(files: List<DriveFileWrapper>): Int {
        return DriveFileOperator.deleteCloudFiles(files)
    }

    /**
     * 删除相同类型的云文档
     */
    override fun deleteSameCategoryCloudFile(activity: ComponentActivity, files: List<DriveFileWrapper>, consumer: Consumer<Int>) {
        DriveFileOperator.deleteSameCategoryCloudFile(activity, files, consumer)
    }
}