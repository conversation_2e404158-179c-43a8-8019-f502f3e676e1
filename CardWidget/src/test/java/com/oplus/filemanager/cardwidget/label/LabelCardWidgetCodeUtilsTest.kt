/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.cardwidget.label
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label

import android.content.Context
import com.filemanager.common.MyApplication
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class LabelCardWidgetCodeUtilsTest {

    private lateinit var mContext: Context

    companion object {
        private const val WIDGET_CODE = "20221216"
    }

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        mContext = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(mContext)
    }

    @After
    fun tearDown() {
    }

    @Test
    fun `should return 0 when getCacheList`() {
        val utils = LabelCardWidgetCodeUtils()
        assertEquals(0, utils.getCacheList().size)
    }
}