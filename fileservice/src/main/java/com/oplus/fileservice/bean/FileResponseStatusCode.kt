/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileResponseStatusCode
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/8 14:45
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/8       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.bean

import com.google.gson.annotations.SerializedName

data class FileResponseStatusCode(
    @SerializedName("code") val code: Int,
    @SerializedName("description") val description: String
) {
    override fun toString(): String = "$code $description"

    override fun equals(other: Any?): Boolean =
        other is FileResponseStatusCode && other.code == code

    override fun hashCode(): Int = code.hashCode()

    fun description(value: String): FileResponseStatusCode = copy(description = value)

    companion object {
        val DoNothing: FileResponseStatusCode = FileResponseStatusCode(100, "Do Nothing")

        val InvalidSourceFileInfo: FileResponseStatusCode =
            FileResponseStatusCode(101, "Invalid Source File Info")

        val InvalidTargetFileInfo: FileResponseStatusCode =
            FileResponseStatusCode(102, "Invalid Target File Info")

        val SameOperationDirectory: FileResponseStatusCode =
            FileResponseStatusCode(103, "Same Operation Directory")

        val InvalidOperation: FileResponseStatusCode =
            FileResponseStatusCode(104, "Invalid Operation")

        val FileNotExist: FileResponseStatusCode = FileResponseStatusCode(100, "File Not Exist")

        val InvalidInputParams: FileResponseStatusCode =
            FileResponseStatusCode(102, "Invalid Input Params")

        private val FileCategoryInputError: FileResponseStatusCode =
            FileResponseStatusCode(108, "File Category Input Error")

        private val FileSortInputError: FileResponseStatusCode =
            FileResponseStatusCode(109, "File Sort Input Error")

        val CategoryIdInputError: FileResponseStatusCode =
            FileResponseStatusCode(110, "Category Id Input Error")


        val OK: FileResponseStatusCode = FileResponseStatusCode(200, "OK")

        val FilePathNull: FileResponseStatusCode =
            FileResponseStatusCode(207, "request file path is null")

        val OperateFailure: FileResponseStatusCode = FileResponseStatusCode(500, "Operate Failure")
        val OperateInterrupted: FileResponseStatusCode =
            FileResponseStatusCode(501, "Operate Interrupted")
        val OperateCancelled: FileResponseStatusCode =
            FileResponseStatusCode(502, "Operate Cancelled")

        val LoaderTimeOut: FileResponseStatusCode =
            FileResponseStatusCode(530, "File Loader time out")

        private val ResultValueOverSized: FileResponseStatusCode =
            FileResponseStatusCode(531, "The result is larger than 512kb")

        val CreateDirFailed: FileResponseStatusCode =
            FileResponseStatusCode(300, "Create Dir Failed")
        val CreateDirExists: FileResponseStatusCode =
            FileResponseStatusCode(301, "Create Dir Exists")

        val RenameSourceFileError: FileResponseStatusCode =
            FileResponseStatusCode(400, "Rename Source File Error")
        val RenameInvalidFileInfo: FileResponseStatusCode =
            FileResponseStatusCode(401, "Rename Invalid Target File Information")

        fun getTimeOutResponseData(): ResponseData<Unit> {
            return ResponseData(
                LoaderTimeOut.code,
                Unit,
                LoaderTimeOut.description
            )
        }

        fun getParamErrorResponseData(): ResponseData<Unit> {
            return ResponseData(
                FileCategoryInputError.code,
                Unit,
                FileCategoryInputError.description
            )
        }

        fun getResultOverSizedResponseData(): ResponseData<Unit> {
            return ResponseData(
                ResultValueOverSized.code,
                Unit,
                ResultValueOverSized.description
            )
        }

        fun getSortInputErrorResponseData(): ResponseData<Unit> {
            return ResponseData(
                FileResponseStatusCode.FileSortInputError.code,
                Unit,
                FileResponseStatusCode.FileSortInputError.description
            )
        }
    }
}
