/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * File        :  -
 * * Description : com.coloros.filemanager
 * * Version     : 1.0
 * * Date        : 2020/11/13
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
package com.coloros.filemanager.utils

import android.app.Activity
import android.app.Application.ActivityLifecycleCallbacks
import android.os.Bundle
import com.coloros.filemanager.utils.TestActivityLifecycleCallbacksImpl
import com.coloros.filemanager.utils.TestLog.d

open class TestActivityLifecycleCallbacksImpl : ActivityLifecycleCallbacks {
    override fun onActivityCreated(activity: Activity, bundle: Bundle?) {}
    override fun onActivityStarted(activity: Activity) {}
    override fun onActivityResumed(activity: Activity) {
        d(TAG, "onActivityResumed")
    }

    override fun onActivityPaused(activity: Activity) {}
    override fun onActivityStopped(activity: Activity) {}
    override fun onActivitySaveInstanceState(activity: Activity, bundle: Bundle) {}
    override fun onActivityDestroyed(activity: Activity) {}

    companion object {
        private val TAG = TestActivityLifecycleCallbacksImpl::class.java.simpleName
    }
}