/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: AutoDIForCategoryAlbum.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2024/6/6
 ** Author: yangqichang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.category.album.di

import com.oplus.filemanager.category.album.CategoryAlbumApi
import com.oplus.filemanager.interfaze.categoryalbum.ICategoryAlbumApi
import org.koin.dsl.module

object AutoDIForCategoryAlbum {

    val categoryAlbumApi = module {
        single<ICategoryAlbumApi>(createdAtStart = true) {
            CategoryAlbumApi
        }
    }
}