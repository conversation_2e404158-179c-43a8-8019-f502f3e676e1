/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : SwitchBean.kt
 * * Description : 从中子读取开关状态的数据结构封装类/或写入中子的开关状态封装类
 * * @param isSuc 这一次写入/读取操作是否成功的标志
 * * @param switchValue 只有读取接口才有该值，该值代表中子中存储的开关值是打开或关闭
 * * @param errorMsg 这一次写入/读取失败时，对应的错误提示
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.dump.bean

data class SwitchBean(
    var isSuc: Boolean = false,
    var switchValue: Boolean? = null,
    var errorMsg: String = ""
) {

    companion object {
        const val LOWER_DMP_NOT_SUPPORT = "lower dmp not support switch state"
    }

    fun isDmpNotSupport(): Boolean {
        return !isSuc && errorMsg == LOWER_DMP_NOT_SUPPORT
    }

    fun isBeanInvalid(): Boolean {
        return !isSuc
    }
}
