/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.cardwidget.label
 * * Description : 桌面卡片显示的标签对象，包含名称，文件数量，文件列表
 * * Version     : 1.0
 * * Date        : 2022/12/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.data

import com.filemanager.common.base.BaseFileBean

data class LabelCardData(
    val labelId: Long,
    val labelName: String,
    var fileCount: Int,
    var fileList: List<BaseFileBean>?
)