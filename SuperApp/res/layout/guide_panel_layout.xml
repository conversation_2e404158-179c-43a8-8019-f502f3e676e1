<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/guide_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.coui.appcompat.scrollview.COUIScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="always">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_guide"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/dimen_8dp"
                android:contentDescription="@null"
                android:src="@drawable/ic_guide_wechat" />

            <TextView
                android:id="@+id/tv_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_24dp"
                android:layout_marginTop="@dimen/dimen_12dp"
                android:layout_marginEnd="@dimen/dimen_24dp"
                android:gravity="start|top"
                android:textColor="?attr/couiColorLabelPrimary"
                android:textSize="@dimen/font_size_14" />
        </LinearLayout>
    </com.coui.appcompat.scrollview.COUIScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>