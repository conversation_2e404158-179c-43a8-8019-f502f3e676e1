/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - PreviewAudioCard.kt
 * Description:
 *     The widget for preview audio file.
 *
 * Version: 1.0
 * Date: 2024-10-09
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-10-09   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.widget

import android.content.Context
import android.graphics.Bitmap
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.coui.appcompat.imageview.FileMgrCOUIRoundImageView
import com.coui.appcompat.seekbar.COUISeekBar
import com.filemanager.common.utils.Log
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.utils.formatToMediaPlayTime
import com.oplus.filemanager.preview.utils.setContentDescription
import com.oplus.filemanager.preview.utils.toMediaPlayPosition
import com.oplus.filemanager.preview.utils.toMediaPlayProgress

internal class PreviewAudioCard @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : RelativeLayout(context, attrs), COUISeekBar.OnSeekBarChangeListener {

    private companion object {
        private const val TAG = "PreviewAudioCard"
    }

    private val audioCoverImage: FileMgrCOUIRoundImageView
    private val audioCoverFrame: FrameLayout
    private val audioCoverDefIc: AppCompatImageView
    private val audioCoverDefBg: AppCompatImageView
    private val fileNameText: TextViewSnippet
    private val fileArtistText: AppCompatTextView
    private val positionText: AppCompatTextView
    private val durationText: AppCompatTextView
    private val progressSeekBar: COUISeekBar
    private val playButton: AppCompatImageView

    private var seekProgressListener: OnSeekPlayProgressListener? = null
    private var clickPlayListener: OnClickPlayButtonListener? = null
    private var isTouchingSeekBar = false
    private var isAudioPlaying = false

    init {
        LayoutInflater.from(context).inflate(R.layout.widget_preview_audio_card, this)
        audioCoverImage = findViewById(R.id.preview_audio_cover)
        val borderWidth =
            context.resources.getDimensionPixelOffset(R.dimen.preview_image_stroke_width)
        audioCoverImage.setBorderWidth(borderWidth)
        audioCoverFrame = findViewById(R.id.preview_audio_cover_frame)
        audioCoverDefIc = findViewById(R.id.preview_audio_cover_def_ic)
        audioCoverDefBg = findViewById(R.id.preview_audio_cover_def_bg)
        val coverElevation = context.resources.getDimension(R.dimen.preview_audio_cover_elevation)
        audioCoverDefBg.z = coverElevation
        audioCoverDefIc.z = 1 + coverElevation
        setAudioCover(null)
        fileNameText = findViewById(R.id.preview_audio_file_name)
        fileArtistText = findViewById(R.id.preview_audio_artist)
        setAudioArtist(null)
        positionText = findViewById(R.id.preview_audio_play_position)
        durationText = findViewById(R.id.preview_audio_play_duration)
        progressSeekBar = findViewById(R.id.preview_audio_play_seek)
        progressSeekBar.min = 0
        progressSeekBar.setOnSeekBarChangeListener(this)
        playButton = findViewById(R.id.preview_audio_play_button)
        playButton.setOnClickListener(::onClickPlayButton)
        gravity = Gravity.CENTER
        setBackgroundResource(R.drawable.preview_widget_card_background)
    }

    fun setAudioFileName(fileName: String?) {
        fileNameText.text = fileName
        fileNameText.setTextViewStyle()
    }

    fun setAudioArtist(artist: String?) {
        if (artist.isNullOrEmpty()) {
            fileArtistText.visibility = INVISIBLE
            fileArtistText.text = " "
        } else {
            fileArtistText.text = artist
            fileArtistText.visibility = VISIBLE
        }
    }

    fun setAudioCover(bitmap: Bitmap?) {
        if (bitmap == null) {
            audioCoverImage.setImageResource(com.filemanager.common.R.drawable.ic_file_audio)
            audioCoverImage.visibility = GONE
            audioCoverDefBg.visibility = VISIBLE
            audioCoverDefIc.visibility = VISIBLE
        } else {
            audioCoverImage.setImageBitmap(bitmap)
            audioCoverImage.visibility = VISIBLE
            audioCoverDefBg.visibility = GONE
            audioCoverDefIc.visibility = GONE
        }
    }

    fun setDuration(duration: Long) {
        val progressMax = duration.toMediaPlayProgress()
        if (progressSeekBar.max == progressMax) {
            return
        }
        Log.d(TAG, "setDuration: duration=$duration")
        progressSeekBar.max = progressMax
        durationText.text = duration.formatToMediaPlayTime()
    }

    fun setProgress(position: Long) {
        if (isTouchingSeekBar) {
            return
        }
        val progressPosition = position.toMediaPlayProgress()
        if (progressSeekBar.progress == progressPosition) {
            return
        }
        progressSeekBar.progress = progressPosition
    }

    fun setOnSeekPlayProgressListener(listener: OnSeekPlayProgressListener) {
        seekProgressListener = listener
    }

    override fun onProgressChanged(seekBar: COUISeekBar?, progress: Int, fromUser: Boolean) {
        val audioPosition = progress.toMediaPlayPosition()
        positionText.text = audioPosition.formatToMediaPlayTime()
        if (fromUser) {
            seekProgressListener?.onSeekPlayProgress(audioPosition, isTouchingSeekBar)
        }
    }

    override fun onStartTrackingTouch(seekBar: COUISeekBar?) {
        isTouchingSeekBar = true
    }

    override fun onStopTrackingTouch(seekBar: COUISeekBar?) {
        isTouchingSeekBar = false
        val audioPosition = seekBar?.progress?.toMediaPlayPosition() ?: return
        seekProgressListener?.onSeekPlayProgress(audioPosition, false)
    }

    fun setAudioPlayState(isPlaying: Boolean) {
        isAudioPlaying = isPlaying
        if (isPlaying) {
            playButton.setImageResource(R.drawable.preview_icon_media_pause)
            playButton.setContentDescription(com.filemanager.common.R.string.pause)
        } else {
            playButton.setImageResource(R.drawable.preview_icon_media_play)
            playButton.setContentDescription(com.filemanager.common.R.string.media_play)
        }
    }

    private fun onClickPlayButton(view: View) {
        clickPlayListener?.onClickPlayButton(isAudioPlaying)
        setAudioPlayState(!isAudioPlaying)
    }

    fun setOnClickPlayButtonListener(listener: OnClickPlayButtonListener) {
        clickPlayListener = listener
    }

    fun release() {
        seekProgressListener = null
        clickPlayListener = null
    }

    fun interface OnSeekPlayProgressListener {
        fun onSeekPlayProgress(position: Long, isTouching: Boolean)
    }

    fun interface OnClickPlayButtonListener {
        fun onClickPlayButton(requireToPause: Boolean)
    }
}