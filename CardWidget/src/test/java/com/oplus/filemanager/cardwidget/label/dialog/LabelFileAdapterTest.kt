/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LabelFileAdapterTest
 ** Description : 标签文件Adapter 的单元测试
 ** Version     : 1.0
 ** Date        : 2022/12/16
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue      2022/12/16      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.dialog

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.FileProvider
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.MiddleMultilineTextView
import com.oplus.filemanager.cardwidget.R
import com.oplus.filemanager.cardwidget.util.CardWidgetThumbnailGenerator
import com.oplus.filemanager.cardwidget.util.FOLDER
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.io.File

class LabelFileAdapterTest {

    private lateinit var context: Context
    private lateinit var adapter: LabelFileAdapter
    private lateinit var inflater: LayoutInflater

    @Before
    fun setup() {
        val resourcesMock = mockk<Resources>()
        every { resourcesMock.getDimensionPixelSize(any()) }.returns(0)
        every { resourcesMock.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_64dp) }.returns(64)
        every { resourcesMock.getDimensionPixelSize(com.filemanager.common.R.dimen.file_browser_icon_size) }.returns(50)
        every { resourcesMock.getDimension(any()) }.returns(2.0f)
        context = mockk()
        every { context.resources }.returns(resourcesMock)
        every { context.applicationContext }.returns(context)
        val file = File("/data/user/0/com.coloros.filemanager/")
        every { context.filesDir }.returns(file)
        MyApplication.init(context)
        FOLDER = file.absolutePath + File.separator + "recent_card_widget"
        MyApplication.init(context)

        mockkStatic(LayoutInflater::class)
        mockkStatic(Utils::class)
        mockkStatic(ModelUtils::class)
        inflater = mockk<LayoutInflater>()
        every { LayoutInflater.from(any()) }.returns(inflater)
        adapter = spyk(LabelFileAdapter(context, emptyList()))
        every { adapter.iconWidth }.returns(50)
        every { adapter.imgWidth }.returns(64)
        every { adapter.layoutInflater }.returns(inflater)
        every { ModelUtils.isTablet() } returns false

        mockkStatic(CardWidgetThumbnailGenerator::class)
        mockkStatic(FileProvider::class)
        mockkStatic(ViewHelper::class)
        justRun { ViewHelper.setClassificationTextSizeG2(any(), any()) }
    }

    @After
    fun teardown() {
        unmockkStatic(LayoutInflater::class)
        unmockkStatic(CardWidgetThumbnailGenerator::class)
        unmockkStatic(FileProvider::class)
        unmockkStatic(ViewHelper::class)
        unmockkObject(Utils::class)
        unmockkStatic(ModelUtils::class)
    }

    @Test
    fun should_when_setFileList() {
        justRun { adapter.notifyDataSetChanged() }
        every { adapter.setFileList(any()) }.answers { callOriginal() }
        val list = mutableListOf<BaseFileBean>()
        adapter.setFileList(list)
        Assert.assertTrue(adapter.list.isEmpty())

        list.add(mockk())
        adapter.setFileList(list)
        Assert.assertTrue(adapter.list.isNotEmpty())
    }

    @Test
    fun should_return_VH_when_onCreateViewHolder() {
        val rootView = mockk<ViewGroup>()
        val img = mockk<FileThumbView>()
        val filenameTv = mockk<MiddleMultilineTextView>()
        every { filenameTv.context }.returns(context)
        every { rootView.findViewById<TextView>(R.id.file_duration_tv) }.returns(mockk())
        every { rootView.findViewById<MiddleMultilineTextView>(R.id.file_name) }.returns(filenameTv)
        every { rootView.findViewById<FileThumbView>(R.id.file_img) }.returns(img)
        every { inflater.inflate(any<Int>(), any(), false) }.returns(rootView)
        every { adapter.onCreateViewHolder(any(), any()) }.answers { callOriginal() }
        adapter.onCreateViewHolder(rootView, 0)
        Assert.assertNotNull(adapter.onCreateViewHolder(mockk(), 0))
    }

    @Test
    fun should_call_bindData_when_onBindViewHolder() {
        every { adapter.onBindViewHolder(any(), any()) }.answers { callOriginal() }
        val holder = mockk<LabelFileAdapter.LabelFileVH>()
        justRun { holder.bindData(any()) }
        adapter.list = mutableListOf(mockk())
        adapter.onBindViewHolder(holder, 0)
        verify { holder.bindData(any()) }
    }

    @Test
    fun should_return_int_when_getItemCount() {
        val list = mutableListOf<BaseFileBean>()
        every { adapter.itemCount }.answers { callOriginal() }
        adapter.list = list
        Assert.assertEquals(0, adapter.itemCount)

        list.add(mockk())
        Assert.assertEquals(1, adapter.itemCount)
    }

    @Test
    fun should_when_bindData() {
        val itemView = mockk<View>()
        justRun { itemView.addOnLayoutChangeListener(any()) }
        val img = mockk<FileThumbView>()
        val fileNameTv = mockk<MiddleMultilineTextView>()
        every { fileNameTv.context }.returns(context)
        every { itemView.findViewById<FileThumbView>(R.id.file_img) }.returns(img)
        every { itemView.findViewById<TextView>(R.id.file_duration_tv) }.returns(mockk())
        every { itemView.findViewById<MiddleMultilineTextView>(R.id.file_name) }.returns(fileNameTv)
        val holder = spyk(LabelFileAdapter.LabelFileVH(itemView, context))
        every { holder.bindData(any()) }.answers { callOriginal() }
        every { holder.bindingAdapter }.returns(adapter)
        justRun { adapter.showIcon(any(), any()) }
        justRun { adapter.showDurationIfNeed(any(), any()) }

        holder.bindData(BaseFileBean())
        verify { adapter.showIcon(any(), any()) }
    }

    @Test
    fun should_when_showIcon() {
        justRun { adapter.setViewSize(any(), any<Int>()) }
        val img = mockk<FileThumbView>()
        justRun { img.setImageResource(any()) }
        justRun { img.setImageURI(any()) }
        every { MyApplication.appContext.getColor(any()) }.returns(Color.RED)

        val file = BaseFileBean().apply {
            mDateModified = 1
            mSize = 1
            mData = "/sdcard/DCIM"
            mIsDirectory = true
            mLocalType = MimeTypeHelper.DIRECTORY_TYPE
        }
        every { adapter.showIcon(img, file) }.answers { callOriginal() }
        adapter.showIcon(img, file)
        verify { adapter.setViewSize(any(), any()) }
        mockkObject(MimeTypeHelper)
        every { MimeTypeHelper.getTypeFromPath(any()) }.returns(MimeTypeHelper.IMAGE_TYPE)
        file.mIsDirectory = false
        file.mData = "/sdcard/DCIM/1.png"
        file.mLocalType = MimeTypeHelper.IMAGE_TYPE
        adapter.showIcon(img, file)
        verify { adapter.setViewSize(any(), any()) }

        every { MimeTypeHelper.getTypeFromPath(any()) }.returns(MimeTypeHelper.COMPRESSED_TYPE)
        every { MimeTypeHelper.getCompressedTypeByPath(any()) }.returns(MimeTypeHelper.ZIP_TYPE)
        file.mIsDirectory = false
        file.mData = "/sdcard/Download/1.zip"
        file.mLocalType = MimeTypeHelper.ZIP_TYPE
        adapter.showIcon(img, file)
        verify { adapter.setViewSize(any(), any()) }

        every { MimeTypeHelper.getTypeFromPath(any()) }.returns(MimeTypeHelper.TXT_TYPE)
        file.mIsDirectory = false
        file.mData = "/sdcard/Download/1.txt"
        file.mLocalType = MimeTypeHelper.TXT_TYPE
        adapter.showIcon(img, file)
        verify { adapter.setViewSize(any(), any()) }

        unmockkObject(MimeTypeHelper)
    }

    @Test
    fun should_when_showDurationIfNeed() {
        every { adapter.showDurationIfNeed(any(), any()) }.answers { callOriginal() }
        every { Utils.getDateFormat(any(), any()) }.returns("")
        val textView = mockk<TextView>()
        justRun { textView.text = any() }
        justRun { textView.visibility = any() }
        var fileBean = BaseFileBean().apply {
            mMediaDuration = 0L
            mLocalType = MimeTypeHelper.TXT_TYPE
        }
        adapter.showDurationIfNeed(textView, fileBean)
        verify { textView.visibility = View.VISIBLE }

        fileBean = BaseFileBean().apply {
            mMediaDuration = 10L
            mLocalType = MimeTypeHelper.VIDEO_TYPE
        }
        adapter.showDurationIfNeed(textView, fileBean)
        verify { textView.visibility = View.VISIBLE }
    }

    @Test
    fun should_call_updateLayoutParam_when_setViewSize() {
        val view = mockk<View>()
        val layoutParam = ViewGroup.LayoutParams(0, 0)
        every { view.layoutParams }.answers { layoutParam }
        justRun { view.layoutParams = any() }
        every { adapter.setViewSize(any(), any()) }.answers { callOriginal() }

        adapter.setViewSize(view, 50)
        Assert.assertEquals(layoutParam.width, 50)
        Assert.assertEquals(layoutParam.height, 50)
    }
}