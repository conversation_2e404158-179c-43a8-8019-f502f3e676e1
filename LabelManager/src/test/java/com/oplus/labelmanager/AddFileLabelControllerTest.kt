/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.labelmanager.AddFileLabelController
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.labelmanager

import android.app.Dialog
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.Lifecycle
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.filemanager.common.base.BaseFileBean
import io.mockk.every
import io.mockk.just
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.runs
import io.mockk.spyk
import io.mockk.verify
import org.junit.Assert
import org.junit.Assert.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AddFileLabelControllerTest {

    @Test
    fun `should execute showPanelFragment when showAddLabelFragment`() {
        val lifecycle = mockk<Lifecycle>()
        justRun { lifecycle.addObserver(any()) }
        val mController = spyk(AddFileLabelController(lifecycle))
        val manager = mockk<FragmentManager>()
        every { manager.findFragmentByTag(any()) } returns null
        val fileList = arrayListOf<BaseFileBean>()
        mController.showAddLabelFragment(manager, fileList)
    }

    @Test
    fun `should execute showPanelFragment when showAddLabelFragment if findFragment is not null`() {
        val tag = "ADD_LABEL_DIALOG_FRAGMENT_TAG"
        val lifecycle = mockk<Lifecycle>()
        justRun { lifecycle.addObserver(any()) }
        val mController = spyk(AddFileLabelController(lifecycle))
        val manager = mockk<FragmentManager>()
        val addLabelDialogFragment = mockk<AddLabelDialogFragment>(relaxed = true) {
            justRun { showPanelFragment(manager, tag) }
        }
        val fragmentTransaction = mockk<FragmentTransaction> {
            every { remove(addLabelDialogFragment) } returns this
            every { commitAllowingStateLoss() } returns 1
        }
        every { manager.beginTransaction() } returns fragmentTransaction
        every { manager.findFragmentByTag(tag) } returns addLabelDialogFragment
        val fileList = arrayListOf<BaseFileBean>()
        mController.showAddLabelFragment(manager, fileList)
        assertEquals(fileList, addLabelDialogFragment.mFileList)
        verify { fragmentTransaction.commitAllowingStateLoss() }
        verify { addLabelDialogFragment.showPanelFragment(any(), tag) }
    }

    @Test
    fun `should call AddLabelDialogFragment setOnDismissListener when call setOnDismissListener`() {
        //given
        val lifecycle = mockk<Lifecycle>()
        justRun { lifecycle.addObserver(any()) }
        val manager = mockk<FragmentManager>()
        val onDismissListener = mockk<COUIBottomSheetDialogFragment.OnDismissListener>()
        val addLabelDialogFragment = mockk<AddLabelDialogFragment>()
        every { manager.findFragmentByTag(any()) } returns addLabelDialogFragment
        val controller = spyk(AddFileLabelController(lifecycle), recordPrivateCalls = true)
        every { addLabelDialogFragment.setOnDismissListener(any()) } just runs
        //when
        controller.setOnDismissListener(manager, onDismissListener)
        //then
        verify { addLabelDialogFragment.setOnDismissListener(onDismissListener) }
    }

    @Test
    fun `should return true when call hasShowPanel if AddLabelDialogFragment is showing`() {
        //given
        val lifecycle = mockk<Lifecycle>()
        justRun { lifecycle.addObserver(any()) }
        val manager = mockk<FragmentManager>()
        val addLabelDialogFragment = mockk<AddLabelDialogFragment>()
        every { manager.findFragmentByTag(any()) } returns addLabelDialogFragment
        val dialog = mockk<Dialog>()
        every { addLabelDialogFragment.dialog } returns dialog
        every { dialog.isShowing } returns true
        val controller = spyk(AddFileLabelController(lifecycle))
        //when
        val result = controller.hasShowPanel(manager)
        //then
        Assert.assertTrue(result)
    }

    @Test
    fun `should return true when call hasShowPanel if AddLabelDialogFragment is not showing`() {
        //given
        val lifecycle = mockk<Lifecycle>()
        justRun { lifecycle.addObserver(any()) }
        val manager = mockk<FragmentManager>()
        val addLabelDialogFragment = mockk<AddLabelDialogFragment>()
        every { manager.findFragmentByTag(any()) } returns addLabelDialogFragment
        val dialog = mockk<Dialog>()
        every { addLabelDialogFragment.dialog } returns dialog
        every { dialog.isShowing } returns false
        val controller = spyk(AddFileLabelController(lifecycle))
        //when
        val result = controller.hasShowPanel(manager)
        //then
        Assert.assertTrue(result.not())
    }
}