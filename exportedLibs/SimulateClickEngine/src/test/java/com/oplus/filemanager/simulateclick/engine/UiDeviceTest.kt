/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - UiDeviceTest.kt
 * Description:
 * The test cases for UiDevice
 *
 * Version: 1.0
 * Date: 2024-06-03
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-06-03   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine

import android.content.Context
import android.content.Intent
import android.view.Surface
import com.oplus.filemanager.simulateclick.engine.utils.ScreenStatusUtils
import com.oplus.filemanager.simulateclick.engine.value.ScreenDisplayFlag
import com.oplus.filemanager.simulateclick.engine.value.ScreenOrientationFlag
import com.oplus.filemanager.simulateclick.engine.value.ScreenRotationFlag
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import io.mockk.verifyOrder
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class UiDeviceTest : Assert() {

    @Before
    fun setUp() {
        mockkStatic(UiDevice::class)
    }

    @After
    fun tearDown() {
        unmockkStatic(UiDevice::class)
    }

    @Test
    fun `should open twice when openTargetApp without deeplink if has openAgainDelay`() {
        // Given
        val testPkg = "com.test.pkg"
        val intent = mockk<Intent>(relaxed = true)
        val context = mockk<Context>(relaxed = true) {
            every { packageManager } returns mockk {
                every { getLaunchIntentForPackage(testPkg) } returns intent
            }
        }
        every { UiDevice.isPackageExisted(context, testPkg) } returns true
        val againIntent = mockk<Intent>(relaxed = true)
        every { UiDevice.constructIntent(intent) } returns againIntent

        // When
        UiDevice.openTargetApp(context, testPkg, "", 1)

        // Then
        verify {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
        }
        verify(inverse = true) {
            againIntent.addFlags(any())
        }
        verifyOrder {
            context.startActivity(intent)
            context.startActivity(againIntent)
        }
    }

    @Test
    fun `should open once when openTargetApp without deeplink if no openAgainDelay`() {
        // Given
        val testPkg = "com.test.pkg"
        val intent = mockk<Intent>(relaxed = true)
        val context = mockk<Context>(relaxed = true) {
            every { packageManager } returns mockk {
                every { getLaunchIntentForPackage(testPkg) } returns intent
            }
        }
        every { UiDevice.isPackageExisted(context, testPkg) } returns true
        val againIntent = mockk<Intent>(relaxed = true)
        every { UiDevice.constructIntent(intent) } returns againIntent

        // When
        UiDevice.openTargetApp(context, testPkg, "", null)

        // Then
        verify {
            context.startActivity(intent)
        }
        verify(inverse = true) {
            context.startActivity(againIntent)
        }
    }

    @Test
    fun `should use deepLink when openTargetApp with deeplink`() {
        // Given
        val testPkg = "com.test.pkg"
        val context = mockk<Context>(relaxed = true) {
            every { packageManager } returns mockk {
                every { getLaunchIntentForPackage(testPkg) } returns mockk(relaxed = true)
            }
        }
        every { UiDevice.isPackageExisted(context, testPkg) } returns true
        every { UiDevice.constructIntent(any()) } returns mockk(relaxed = true)
        val deepLink = "testLink"
        every { UiDevice.openByDeepLink(context, deepLink) } returns true

        // When
        UiDevice.openTargetApp(context, testPkg, deepLink, null)

        // Then
        verify {
            UiDevice.openByDeepLink(context, deepLink)
        }
        verify(inverse = true) {
            context.startActivity(any())
        }
    }

    @Test
    fun `should flip flag when getCurrentScreenDisplay if isFocusSecondaryDisplay`() {
        // SetUp
        mockkStatic(ScreenStatusUtils::class)

        // Given
        every { ScreenStatusUtils.isFocusSecondaryDisplay(any()) } returns true
        every { ScreenStatusUtils.isTablet(any()) } returns false
        every { ScreenStatusUtils.isUnfold(any()) } returns false

        // When
        val result = UiDevice.getCurrentScreenDisplay(mockk())

        // Then
        assertEquals(ScreenDisplayFlag.FLIP, result.flag)

        // TearDown
        unmockkStatic(ScreenStatusUtils::class)
    }

    @Test
    fun `should pad flag when getCurrentScreenDisplay if isTablet`() {
        // SetUp
        mockkStatic(ScreenStatusUtils::class)

        // Given
        every { ScreenStatusUtils.isFocusSecondaryDisplay(any()) } returns false
        every { ScreenStatusUtils.isTablet(any()) } returns true
        every { ScreenStatusUtils.isUnfold(any()) } returns false

        // When
        val result = UiDevice.getCurrentScreenDisplay(mockk())

        // Then
        assertEquals(ScreenDisplayFlag.PAD, result.flag)

        // TearDown
        unmockkStatic(ScreenStatusUtils::class)
    }

    @Test
    fun `should unfold flag when getCurrentScreenDisplay if isUnfold`() {
        // SetUp
        mockkStatic(ScreenStatusUtils::class)

        // Given
        every { ScreenStatusUtils.isFocusSecondaryDisplay(any()) } returns false
        every { ScreenStatusUtils.isTablet(any()) } returns false
        every { ScreenStatusUtils.isUnfold(any()) } returns true

        // When
        val result = UiDevice.getCurrentScreenDisplay(mockk())

        // Then
        assertEquals(ScreenDisplayFlag.UNFOLD, result.flag)

        // TearDown
        unmockkStatic(ScreenStatusUtils::class)
    }

    @Test
    fun `should normal flag when getCurrentScreenDisplay if normal device`() {
        // SetUp
        mockkStatic(ScreenStatusUtils::class)

        // Given
        every { ScreenStatusUtils.isFocusSecondaryDisplay(any()) } returns false
        every { ScreenStatusUtils.isTablet(any()) } returns false
        every { ScreenStatusUtils.isUnfold(any()) } returns false

        // When
        val result = UiDevice.getCurrentScreenDisplay(mockk())

        // Then
        assertEquals(ScreenDisplayFlag.NORMAL, result.flag)

        // TearDown
        unmockkStatic(ScreenStatusUtils::class)
    }

    @Test
    fun `should landscape flag when getCurrentScreenOrientation if isLandscape`() {
        // SetUp
        mockkStatic(ScreenStatusUtils::class)

        // Given
        every { ScreenStatusUtils.isLandscape(any()) } returns true

        // When
        val result = UiDevice.getCurrentScreenOrientation(mockk())

        // Then
        assertEquals(ScreenOrientationFlag.LANDSCAPE, result.flag)

        // TearDown
        unmockkStatic(ScreenStatusUtils::class)
    }

    @Test
    fun `should portrait flag when getCurrentScreenOrientation if isLandscape`() {
        // SetUp
        mockkStatic(ScreenStatusUtils::class)

        // Given
        every { ScreenStatusUtils.isLandscape(any()) } returns false

        // When
        val result = UiDevice.getCurrentScreenOrientation(mockk())

        // Then
        assertEquals(ScreenOrientationFlag.PORTRAIT, result.flag)

        // TearDown
        unmockkStatic(ScreenStatusUtils::class)
    }

    @Test
    fun `should rotation 0 flag when getCurrentScreenRotation if screen rotation is 0`() {
        // SetUp
        mockkStatic(ScreenStatusUtils::class)

        // Given
        every { ScreenStatusUtils.getScreenRotation(any()) } returns Surface.ROTATION_0

        // When
        val result = UiDevice.getCurrentScreenRotation(mockk())

        // Then
        assertEquals(ScreenRotationFlag.ROTATION_0, result.flag)

        // TearDown
        unmockkStatic(ScreenStatusUtils::class)
    }

    @Test
    fun `should rotation 90 flag when getCurrentScreenRotation if screen rotation is 90`() {
        // SetUp
        mockkStatic(ScreenStatusUtils::class)

        // Given
        every { ScreenStatusUtils.getScreenRotation(any()) } returns Surface.ROTATION_90

        // When
        val result = UiDevice.getCurrentScreenRotation(mockk())

        // Then
        assertEquals(ScreenRotationFlag.ROTATION_90, result.flag)

        // TearDown
        unmockkStatic(ScreenStatusUtils::class)
    }

    @Test
    fun `should rotation 180 flag when getCurrentScreenRotation if screen rotation is 180`() {
        // SetUp
        mockkStatic(ScreenStatusUtils::class)

        // Given
        every { ScreenStatusUtils.getScreenRotation(any()) } returns Surface.ROTATION_180

        // When
        val result = UiDevice.getCurrentScreenRotation(mockk())

        // Then
        assertEquals(ScreenRotationFlag.ROTATION_180, result.flag)

        // TearDown
        unmockkStatic(ScreenStatusUtils::class)
    }

    @Test
    fun `should rotation 270 flag when getCurrentScreenRotation if screen rotation is 270`() {
        // SetUp
        mockkStatic(ScreenStatusUtils::class)

        // Given
        every { ScreenStatusUtils.getScreenRotation(any()) } returns Surface.ROTATION_270

        // When
        val result = UiDevice.getCurrentScreenRotation(mockk())

        // Then
        assertEquals(ScreenRotationFlag.ROTATION_270, result.flag)

        // TearDown
        unmockkStatic(ScreenStatusUtils::class)
    }
}