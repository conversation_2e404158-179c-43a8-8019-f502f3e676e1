#Configure global properties
#Define APK name
prop_archivesBaseName=FileManager
#Define target SDK api level
prop_compileSdkVersion=android-36
#Define build tools version
prop_buildToolsVersion=36.0.0
# Define target SDK api level
prop_targetSdkVersion=36
prop_minSdkVersion=29
#Define app prebuild SDK api level,for example android-21,android-22,android-23
prop_appPrebuildSdkVersion=android-34,android-35
#copy apk to vm android-29
prop_appTargetPlatformVersion=android-29,android-30,android-31,android-32,android-33,android-U,android-34,android-35
#prop_couiVersionName=15.0.3-beta9bca6f9-SNAPSHOT
#Native SDK flag
prop_decoupleSdk=true
prop_internalSdk=false
#heytap.support:api-adapter SDK Version
#prop_supportSdkVersion=13.0.0-SNAPSHOT
android.injected.testOnly=false
#Define resource subpacage params,default is empty
#CI Server will introduce the correct params when build
#Language resource settings, the configuration is valid locally, the server will modify this configuration to package all resources
prop_exp_resConfig=en_US,es_MX,es_ES,pt_BR,da_DK,de_DE,el_GR,fr_FR,in_ID,it_IT,ja_JP,nl_NL,nb_NO,pt_PT,ru_RU,sv_SE,th_TH,tr_TR,vi_VN,zh_CN,zh_HK,zh_TW,my_MM,my_ZG,hi_IN,ms_MY,tl_PH,cs_CZ,en_GB,hu_HU,pl_PL,ro_RO,uk_UA,fa_IR,ur_PK,ar_EG,bn_BD,ko_KR,mr_IN,ta_IN,gu_IN,lo_LA,km_KH,sw_KE,si_LK,pa_IN,te_IN,kn_IN,ml_IN,or_IN,as_IN,kk_KZ,iw_IL,ne_NP,bo_CN,en_AU,en_NZ,ca_ES,eu_ES,bg_BG,fi_FI,sk_SK,hr_HR,lt_LT,sl_SI,lv_LV,et_EE,gl_ES,fr_CH,it_CH,de_CH,uz_UA,uz_UZ,sr_Latn_RS,kea_CV,ar_AR,fil_PH,id_ID,sr_RS,sq_AL,mk_MK,az_AZ,ka_GE,hy_AM,bs
#app can verify locally use params as below
prop_resConfigs=zh_CN,en_US,zh_TW,hi_IN,in_ID,ms_MY,my_MM,tl_PH,ru_RU,th_TH,vi_VN,ja_JP,fr_FR,bn_BD,ko_KR,lo_LA,ne_NP,bo_CN,ug_CN,km_KH,ar_EG,zh_HK,kk_CN,kea_CV,ar_AR,fil_PH,id_ID,it_IT,nl_NL,de_DE,es_MX,pt_BR
#Define app disable resource subpackage,default is comment off!!!!
prop_disableSubPackage=false
#Define oppo maven repositories url,stable branch should use Maven Stable Url!!!
#Maven Stable Url
prop_oppoMavenUrl=http://maven.scm.adc.com:8081/nexus/content/groups/stable-public/
prop_oplusMavenUrl=http://maven.scm.adc.com:8081/nexus/content/groups/public/
prop_oppoTestMavenUrl=http://maven.scm.adc.com:8081/nexus/repository/nearme_os_public/
prop_oppoMavenUrlSnapshot=http://maven.scm.adc.com:8081/nexus/content/groups/snapshots/
prop_oapm_oppoMavenUrl=http://nexus.os.adc.com/nexus/content/groups/public/
prop_synergySdkMavenUrl=http://maven.scm.adc.com:8081/nexus/content/groups/public/
prop_sdkMavenUrlRelease=http://mirror-maven.myoas.com/repository/ars-sdk-release/
prop_nexusReleaseUrl=http://nexus.os.adc.com/nexus/content/repositories/releases/
prop_oppoBrowserMavenUrl=http://maven.scm.adc.com:8081/nexus/content/groups/oppo-browser-public/
prop_maven2=https://repo1.maven.org/maven2
#Maven Snapshots Url
#prop_oppoMavenUrl=http://maven.scm.adc.com:8081/nexus/content/groups/public/
#Define versionCommit and versionDate,used in AndroidManifest.xml
prop_versionCommit=12345678
prop_versionDate=160701
prop_compileSdkApi=29
#Define target build region,same as TARGET_BUILD_VERSION
prop_targetBuildRegion=CN
#Define the APK Signature Scheme: v1 Scheme, enabled by default
prop_apkSignatureSchemeV1=true
#Define the APK Signature Scheme: v2 Scheme, enabled by default
prop_apkSignatureSchemeV2=true
##############################
#######Don't modify below#####
#hdpi resource subpacakge params
#prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,hdpi
#xhdpi resource subpacakge params
#prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,xhdpi
#xxhdpi resource subpacakge params
#prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,xxhdpi
#2.5K resource subpacakge params
#prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,xxxhdpi
#1080p with 2.5K resource subpacakge params
#prop_resConfigs=zh_CN,en_US,zh_TW,nodpi,xxhdpi,xxxhdpi
#######################
prop_keyPassword=android
prop_storePassword=android
sonatypeUsername=swdp
sonatypePassword=swdp
########################
#####Don't modify above#####
############################
android.useAndroidX=true
android.enableJetifier=true
enable_androidJunit4Test_outputDir=false
oppo_unit_test=unitTestTask
############################
codeScanCommand=codeScanTask
#gradleéç½®ä¼å ---------åè start-------------------------
#å¼å¯gradleç¼å­ï¼é»è®¤ä¸ä¼å¼å¯ï¼demoç¬¬äºæ¬¡cleanç¼è¯æµè¯æ¶é´åå°50%
org.gradle.caching=true
#éè¿éç¨ä»¥åæå»ºä¸­çè®¡ç®æ¥æé«æå»ºéåº¦ãç¶èï¼å®çå¥½å¤æ¯å·¨å¤§çï¼æä»¬éå¸¸ä¼å¨éåçæå»ºä¸­åå°15-75%çæå»ºæ¶é´ã
#Gradleå®æ¤è¿ç¨å¨é»è®¤æåµä¸æ¯å¯ç¨ç
org.gradle.daemon=true
#å¹¶åçº¿ç¨æ°éï¼é»è®¤ä¸ºcpuæ ¸å¿æ°
org.gradle.workers.max=16
# éè¿éç½® Gradle æç¨çæä½³ JVM åå¾åæ¶å¨ï¼å¯ä»¥æåæå»ºæ§è½ã
# When configured, Gradle will run in incubating parallel mode.ï¼éç½®åï¼Gradleå°ä»¥å¹¶è¡æ¨¡å¼è¿è¡ãï¼
# This option should only be used with decoupled projects. More details, visitï¼è¯¥éç½®ä»ä»ç¨ä¸ºå®ç°ç¼è¯éç¦»çå¤é¡¹ç®ï¼åªéå¯¹ä½¿ç¨ç»ä»¶åæææï¼å®ç°ç¼è¯éç¦»çåºç¨çæææå¥½ï¼
org.gradle.parallel=true
org.gradle.parallel.threads=16
#gradleéç½®ä¼å ---------end-------------------------
prop_targetCompatibility=17
#Cocoverageæä»¶ ä¸éè¦ååæµè¯çmoduleéç½®
prop_unitTestBlackProjects=app,Oaps,:Ad:AdNull,:DMP:SearchNull,FileOperate
# å¿é¡»ï¼Define build-plugin version
useOBuildPlugin=true
oCoverageLog=true
includePageCoverage=true
mainVersionCode=16002010
mainVersionName=16.2.10
org.gradle.jvmargs=-Xmx10g -XX:+UseG1GC -XX:+HeapDumpOnOutOfMemoryError

#Jacoco mavem branch_io Url
prop_oppoMavenUrlBranchIO=http://nexus.os.adc.com/nexus/content/repositories/branch_io/
#Jacoco mavem snapshots Url
prop_oppoMavenUrlSnapshots=http://maven.scm.adc.com:8081/nexus/content/repositories/snapshots/
android.nonTransitiveRClass=true
android.enableR8.fullMode=true

# Use test env for cloud config
prop_cloudConfig_testEnv=false

# Use prebuilt libs in maven to replace local module when compile APK
prop_use_prebuilt_thumbnail_lib=true
prop_use_prebuilt_thumbnail_wps_lib=true
prop_use_prebuilt_simulate_click_lib=true
prop_use_prebuilt_drap_drop_lib=true

#AAR suffix when build with pipeline
versionSuffix=-alpha01
prop_archivesGroupName=com.oplus.filemanager
prop_artifactId_thumbnail=thumbnail
prop_artifactId_thumbnailWpsCompat=thumbnail_wps_compat
prop_artifactId_simulateClickEngine=simulate_click_engine
prop_artifactId_dragDrpo=dragdrop