/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : DefaultRegexProcessor.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.reg

import android.util.Log
import com.coloros.filemanager.appswitch.process.IViewExactProcess
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam

open class DefaultRegexProcessor : BaseRegexProcessor() {

    companion object {
        const val TAG = "DefaultRegexProcessor"
    }

    override fun processTopLevelFileTime(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem
    ): IViewExactProcess.TimeValues? {
        return null
    }

    override fun processTopLevelBaseTime(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ): IViewExactProcess.TimeValues? {
        return null
    }

    override fun processTopLevelDeltaTime(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ): IViewExactProcess.TimeValues? {
        return null
    }

    override fun processTopLevelFileSize(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem
    ): IViewExactProcess.SizeValues? {
        return null
    }

    override fun processTopLevelFileName(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem
    ): String {
        return ""
    }

    override fun processTopLevelSourceName(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem
    ): String {
        val topLevelProcessedRegString = topLevelReg.processedTopLevelReg
        Log.i(TAG,
            "processTopLevelSourceName topLevelInputString $topLevelInputString, topLevelProcessedRegString $topLevelProcessedRegString"
        )
        return if (topLevelInputString.isEmpty() || topLevelProcessedRegString.isEmpty()) {
            Log.e(TAG,
                "processTopLevelSourceName topLevelInputString $topLevelInputString or topLevelProcessedRegString $topLevelProcessedRegString empty"
            )
            ""
        } else {
            topLevelInputString
        }
    }

    override fun processTopLevelSourceType(
        topLevelInputString: String,
        topLevelReg: ThirdAppListenConfigParam.ParseRegItem,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ): Int {
        var result =  topLevelReg.regMatchSourceType ?: -1
        if (result == -1) {
            result = checkActivityItem.sourceType ?: -1
        }
        if (result == -1) {
            result = checkActivityItem.defaultSourceType ?: -1
        }
        Log.i(TAG, "processTopLevelSourceType topLevelInputString $topLevelInputString, topLevelReg $topLevelReg, result $result")
        return result
    }

    override fun processSecondLevelFileName(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>
    ): String {
        return ""
    }

    override fun processSecondLevelFileTime(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>
    ): IViewExactProcess.TimeValues? {
        return extractTimeValuesFromRegex(regexList, secondLevelInputString)
    }

    private fun extractTimeValuesFromRegex(
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>,
        secondLevelInputString: String
    ): IViewExactProcess.TimeValues? {
        if (regexList.isEmpty() || secondLevelInputString.isEmpty()) {
            Log.e(TAG, "processSecondLevelFileTime secondLevelInputString $secondLevelInputString or regexList empty, return default")
            return null
        } else {
            var timeValues: IViewExactProcess.TimeValues? = null
            for (secondItem in regexList) {
                val regex = kotlin.runCatching {
                    Regex(secondItem.regex)
                }.onFailure {
                    Log.e(TAG, "processField regex constuctor error, IllegalArgumentException ${secondItem.regex} ", it)
                }.getOrNull() ?: continue
                val result = regex.find(secondLevelInputString)
                if (result != null) {
                    timeValues = getTimeValues(result, secondItem)
                    break
                }
                Log.i(TAG, "processSecondLevelFileTime secondLevelInputString $secondLevelInputString, regex ${secondItem.regex} not matched, next")
            }
            return timeValues
        }
    }


    override fun processSecondLevelBaseTime(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>
    ): IViewExactProcess.TimeValues? {
        return extractTimeValuesFromRegex(regexList, secondLevelInputString)
    }


    override fun processSecondLevelDeltaTime(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>
    ): IViewExactProcess.TimeValues? {
        return extractTimeValuesFromRegex(regexList, secondLevelInputString)
    }

    override fun processSecondLevelFileSize(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>
    ): IViewExactProcess.SizeValues? {
        if (regexList.isEmpty() || secondLevelInputString.isEmpty()) {
            Log.e(TAG, "processSecondLevelFileSize secondLevelInputString $secondLevelInputString or regexList empty, return default")
            return null
        } else {
            var outputResult: IViewExactProcess.SizeValues? = null
            for (secondItem in regexList) {
                val regex = kotlin.runCatching {
                    Regex(secondItem.regex)
                }.onFailure {
                    Log.e(TAG, "processField regex constuctor error, IllegalArgumentException ${secondItem.regex} ", it)
                }.getOrNull() ?: continue
                val regResult = regex.find(secondLevelInputString)
                if (regResult != null) {
                    outputResult = getSizeValues(regResult, secondItem)
                    break
                }
            }
            return outputResult
        }
    }

    override fun processSecondLevelSourceName(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>
    ): String {
        return processSecondLevelRegexString(regexList, secondLevelInputString)
    }

    private fun processSecondLevelRegexString(
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>,
        secondLevelInputString: String
    ): String {
        if (regexList.isEmpty() || secondLevelInputString.isEmpty()) {
            Log.e(TAG, "processSecondLevelRegexString secondLevelInputString $secondLevelInputString or regexList empty, return default")
            return ""
        } else {
            var targetStringResult = ""
            for (secondItem in regexList) {
                val regex = kotlin.runCatching {
                    Regex(secondItem.regex)
                }.onFailure {
                    Log.e(TAG, "processSecondLevelRegexString regex constuctor error, IllegalArgumentException ${secondItem.regex} ", it)
                }.getOrNull() ?: continue
                val result = regex.find(secondLevelInputString)
                val isGroupIndex = secondItem.indexType == ThirdAppListenConfigParam.ParseRegItem.INDEX_TYPE_GROUP_INDEX
                Log.i(TAG, "processSecondLevelRegexString preRegex ${secondItem.regex}, regex result $result, isGroup $isGroupIndex")
                if (result != null) {
                    targetStringResult = if (isGroupIndex) {
                        getTargetResultStringForGroup(result, secondItem)
                    } else {
                        getTargetResultStringForIndex(result, secondItem)
                    }
                    if (targetStringResult.isNotEmpty()) {
                        break
                    }
                } else {
                    Log.e(TAG, "processSecondLevelRegexString secondLevelInputString $secondLevelInputString, " +
                                "regex ${secondItem.regex} not matched, next")
                }
            }
            return targetStringResult
        }
    }

    private fun getTargetResultStringForGroup(
        result: MatchResult,
        secondLevelRegItem: ThirdAppListenConfigParam.SecondLevelRegItem,
    ): String {
        var secondLevelOutput = ""
        val groups = result.groups
        val groupSize = groups.size
        val currentLevelIndex = secondLevelRegItem.index
        if (currentLevelIndex == null) {
            Log.i(TAG, "getTargetResultStringForGroup no index for this reg, return ")
            return secondLevelOutput
        }
        var targetSecondLeveGroup: MatchGroup? = null
        runCatching {
            //这个地方可能外部配置的currentLevelIndex超过了groupsize，导致出错
            targetSecondLeveGroup = groups[currentLevelIndex + 1]
            if (targetSecondLeveGroup != null) {
                secondLevelOutput = targetSecondLeveGroup?.value ?: ""
            }
        }.onFailure {
            Log.e(TAG, "getTargetInputStringForGroup error", it)
        }
        Log.i(TAG, "getTargetResultStringForGroup groupSize $groupSize, secondGroup $targetSecondLeveGroup, index $currentLevelIndex, " +
                "output $secondLevelOutput")
        return secondLevelOutput
    }

    private fun getTargetResultStringForIndex(
        result: MatchResult,
        secondLevelRegItem: ThirdAppListenConfigParam.SecondLevelRegItem
    ): String {
        val value = result.value
        val currentLevelIndex = secondLevelRegItem.index
        if (currentLevelIndex == null) {
            Log.i(TAG, "getTargetResultStringForGroup no index for this reg, return ")
            return ""
        }
        val targetStringResult = value.substring(currentLevelIndex).trim()
        Log.i(TAG,
            "getTargetInputStringForIndex value $value, currentLevelIndex $currentLevelIndex, targetOutput $targetStringResult"
        )
        return targetStringResult
    }


    override fun processSecondLevelSourceType(
        secondLevelInputString: String,
        regexList: List<ThirdAppListenConfigParam.SecondLevelRegItem>,
        checkActivityItem: ThirdAppListenConfigParam.CheckActivityItem
    ): Int {
        return -1
    }

    private fun getSizeValues(matchResult: MatchResult, regItem: ThirdAppListenConfigParam.SecondLevelRegItem): IViewExactProcess.SizeValues {
        val unitIntIndex = regItem.unitIntIndex
        val unitIntValue = getValues(matchResult, unitIntIndex)
        val unitDecimalIndex = regItem.unitDecimalIndex
        val unitDecimalValue = getValues(matchResult, unitDecimalIndex)
        val unitDecimalStringLength = if (unitDecimalIndex != null) {
            matchResult.groups[unitDecimalIndex + 1]?.value?.length ?: 0
        } else {
            0
        }
        val unitValue = regItem.unit ?: 1
        return IViewExactProcess.SizeValues(
            unitValue,
            unitIntValue,
            unitDecimalValue,
            unitDecimalStringLength
        )
    }

    private fun getTimeValues(matchResult: MatchResult, regItem: ThirdAppListenConfigParam.SecondLevelRegItem): IViewExactProcess.TimeValues {
        val yearIndex = regItem.yearIndex
        val yearValue = getValues(matchResult, yearIndex)
        val monthIndex = regItem.monthIndex
        val monthValue = getValues(matchResult, monthIndex)
        val dayIndex = regItem.dayIndex
        val dayValue = getValues(matchResult, dayIndex)
        var dayAjustValue = regItem.dayAdjust
        if (dayAjustValue == null) {
            val dayAdjustIndex = regItem.dayAdjustIndex
            dayAjustValue = getValues(matchResult, dayAdjustIndex, 0)
        }
        val hourIndex = regItem.hourIndex
        val hourValue = getValues(matchResult, hourIndex)
        val minuteIndex = regItem.minuteIndex
        val minuteValue = getValues(matchResult, minuteIndex)
        val secondIndex = regItem.secondIndex
        val secondValue = getValues(matchResult, secondIndex)
        return IViewExactProcess.TimeValues(
            yearValue,
            monthValue,
            dayValue,
            dayAjustValue,
            hourValue,
            minuteValue,
            secondValue
        )
    }

    private fun getValues(
        matchResult: MatchResult,
        valueIndex: Int?,
        defaultValue: Int = -1
    ): Int {
        return runCatching {
            if (valueIndex != null) {
                matchResult.groups[valueIndex + 1]?.value?.toInt() ?: defaultValue
            } else {
                defaultValue
            }
        }.onFailure {
            Log.w(TAG, "getValues error", it)
        }.getOrNull() ?: defaultValue
    }
}