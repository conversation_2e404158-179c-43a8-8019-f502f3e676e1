/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : CategoryRemoteFileActivity
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.DragEvent
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.RemoteDeviceConstants
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.controller.navigation.NavigationType
import com.filemanager.common.dragselection.DragDropInterface
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.InstalledPermissionCallback
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.interfaces.PerformClickDir
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ResourceUtils
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.encrypt.EncryptActivity
import java.io.File

class CategoryRemoteFileActivity : EncryptActivity(), NavigationInterface,
    NavigationBarView.OnItemSelectedListener,
    TransformNextFragmentListener, InstalledPermissionCallback, BaseVMActivity.PermissonCallBack,
    DragDropInterface, IDraggingActionOperate, PerformClickDir {


    companion object {
        private const val TAG = "CategoryRemoteFileActivity"
        private const val TAG_REMOTE_FRAGMENT = "remote_fragment_tag"
    }

    private var mCurrentPath: String? = null
    private var mTitle: String? = ""
    private var mRootView: ViewGroup? = null
    private var mRemoteFileFragment: CategoryRemoteFileFragment? = null
    private var mActionActivityResultListener: ActionActivityResultListener? = null
    private var mIsSDPage = false
    private val mNavigationController by lazy {
        NavigationController(
            lifecycle,
            NavigationType.REMOTE_MAC,
            id = R.id.navigation_tool
        )
    }
    private var mIsFromDetail = false
    private var mDeviceId: String? = ""
    private var mRemoteDeviceName: String? = ""
    private var mDeviceStatus: Int = RemoteDeviceConstants.UNDISCOVERED
    private var mDeviceSameAccount: Int = RemoteDeviceConstants.UN_SAME_ACCOUNT

    override fun getLayoutResId(): Int {
        return R.layout.remote_file_list_activity
    }

    override fun initView() {
        registerVmChangedReceiver(null)
        val intent = intent
        if (null == intent) {
            Log.v(TAG, "intent null")
            finish()
            return
        }
        mCurrentPath = getCurrentPath(intent)
        mTitle = getTitle(intent)
        mIsSDPage = KtUtils.checkIsSDPath(this, mCurrentPath + File.separator)
        mIsFromDetail = IntentUtils.getBoolean(intent, KtConstants.FROM_DETAIL, false)
        mDeviceId = IntentUtils.getString(intent, KtConstants.P_REMOTE_DEVICE_ID)
        mRemoteDeviceName = IntentUtils.getString(intent, KtConstants.P_REMOTE_DEVICE_NAME)
        mDeviceStatus = IntentUtils.getInt(intent, KtConstants.P_REMOTE_DEVICE_STATUS, RemoteDeviceConstants.UNDISCOVERED)
        mDeviceSameAccount = IntentUtils.getInt(intent, KtConstants.P_REMOTE_DEVICE_SAME_ACCOUNT, RemoteDeviceConstants.UN_SAME_ACCOUNT)

        mRootView = findViewById(R.id.coordinator_layout)
        setFragment()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        return if (mRemoteFileFragment != null) {
            mRemoteFileFragment?.onCreateOptionsMenu(menu, menuInflater)
            true
        } else {
            super.onCreateOptionsMenu(menu)
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return if (mRemoteFileFragment != null) {
            mRemoteFileFragment!!.onMenuItemSelected(item)
        } else {
            super.onOptionsItemSelected(item)
        }
    }

    override fun initData() {
        //doNothing
    }

    override fun startObserve() {
    }

    override fun onPermissionSuccess(isInstallPermission: Boolean?) {
        Log.d(TAG, "onPermissionSuccess")
        super.onPermissionSuccess(isInstallPermission)
        /*mRootView?.post {
            mRemoteFileFragment?.onResumeLoadData()
        }*/
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
        Log.d(TAG, "refreshCurrentPage  $action-->$data ;  $mCurrentPath")
        // File system not need to care the media scan result
        if (Intent.ACTION_MEDIA_SCANNER_FINISHED == action) {
            return
        }
        fun refreshCurrentPage() {
            mRemoteFileFragment?.onResumeLoadData()
        }
        val actionMatch = (Intent.ACTION_MEDIA_BAD_REMOVAL == action) || (Intent.ACTION_MEDIA_REMOVED == action)
                || (Intent.ACTION_MEDIA_UNMOUNTED == action) || (Intent.ACTION_MEDIA_EJECT == action)
        if ((mCurrentPath != null) && mIsSDPage && actionMatch) {
            var uriPath: String? = null
            kotlin.runCatching {
                data?.let {
                    uriPath = Uri.parse(it).path
                }
            }.onFailure {
                Log.w(TAG, "refreshCurrentPage data err: ${it.message}")
            }
            if (uriPath.isNullOrEmpty() || (mCurrentPath?.startsWith(uriPath!!) == true)) {
                finish()
                return
            }
        }
        refreshCurrentPage()
    }

    private fun setFragment() {
        var fragment = supportFragmentManager.findFragmentByTag(TAG_REMOTE_FRAGMENT)
        if ((fragment == null) || (fragment !is CategoryRemoteFileFragment)) {
            fragment = CategoryRemoteFileFragment()
        }
        val bundle = Bundle()
        bundle.putString(KtConstants.P_TITLE, mTitle)
        bundle.putBoolean(KtConstants.FROM_DETAIL, mIsFromDetail)
        bundle.putString(KtConstants.P_REMOTE_DEVICE_ID, mDeviceId)
        bundle.putString(KtConstants.P_REMOTE_DEVICE_NAME, mRemoteDeviceName)
        bundle.putInt(KtConstants.P_REMOTE_DEVICE_STATUS, mDeviceStatus)
        bundle.putInt(KtConstants.P_REMOTE_DEVICE_SAME_ACCOUNT, mDeviceSameAccount)
        fragment.arguments = bundle
        val ft = supportFragmentManager.beginTransaction()
        Log.d(TAG, "setFragment $mDeviceId, mRemoteDeviceName $mRemoteDeviceName")
        ft.replace(R.id.content, fragment, TAG_REMOTE_FRAGMENT)
        ft.show(fragment)
        ft.commitAllowingStateLoss()
        mRemoteFileFragment = fragment
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mRemoteFileFragment?.onUIConfigChanged(configList)
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        mActionActivityResultListener?.onActivityResult(requestCode, resultCode, data)
    }

    override fun onBackPressed() {
        if ((mRemoteFileFragment as? OnBackPressed)?.pressBack() == true) {
            return
        } else {
            super.onBackPressed()
        }
    }

    override fun backtoTop() {
        super.backtoTop()
        mRemoteFileFragment?.let {
            it.getRecyclerView()?.fastSmoothScrollToTop()
        }
    }

    override fun onNavigationItemSelected(menuItem: MenuItem): Boolean {
        return mRemoteFileFragment?.onNavigationItemSelected(menuItem) ?: false
    }

    override fun showNavigation() {
        mNavigationController.showNavigation(this)
        updateNavigationToolPadding()
    }

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean, hasAndroidData: Boolean) {
        mNavigationController.setNavigateItemAble(isEnable, mHasDrm)
    }

    override fun hideNavigation() {
        mNavigationController.hideNavigation(this)
    }

    override fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener) {
        mActionActivityResultListener = actionActivityResultListener
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        val path = getCurrentPath(intent)
        Log.d(TAG, "onNewIntent = $path")
        mCurrentPath = path
        mRemoteFileFragment?.setCurrentFromOtherSide(path)
    }

    override fun transformToNextFragment(path: String?) {
        //mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, path)
    }

    override fun <T : BaseFileBean> showEditLabelFragmentDialog(fileList: ArrayList<T>) {
        //mAddFileLabelController.showAddLabelFragment(supportFragmentManager, fileList)
    }

    override fun onUpdatedLabel() {
        //mRemoteFileFragment?.onResumeLoadData()
    }

    override fun hasShowPanel(): Boolean {
        return false
    }

    override fun showSelectPathFragmentDialog(code: Int) {
        //mSelectPathController.showSelectPathFragmentDialog(supportFragmentManager, code)
    }

    override fun onSelect(code: Int, paths: List<String>?) {
        //mSelectPathController.onDestroy()
        mRemoteFileFragment?.fromSelectPathResult(code, paths)
    }

    override fun getActivityType(): Int {
        return InstalledPermissionCallback.FILE_BROWSER_ACTIVITY
    }

    fun getCurrentPath(): String {
        return mRemoteFileFragment?.getCurrentPath() ?: ""
    }

    override fun onRefreshData() {
        Log.d(TAG, "onRefreshData")
        mRemoteFileFragment?.onRefreshData()
    }

    override fun handleNoStoragePermission() {
        mRemoteFileFragment?.setPermissionEmptyVisible(View.VISIBLE)
    }

    override fun updateNavigationToolPadding() {
        mNavigationController.updateNavigationToolPadding(navPaddingBottom)
    }


    fun getCurrentPath(intent: Intent): String {
        // 从FileBrowserActivityCompanion类的逻辑移植过来，后续针对远端文件界面需要修改。
        val currentPath = IntentUtils.getString(intent, KtConstants.CURRENT_DIR)
        Log.d(TAG, "currentPath = $currentPath")
        return currentPath ?: ""
    }


    fun getTitle(intent: Intent): String {
        val resId = IntentUtils.getInt(intent, Constants.TITLE_RES_ID, -1)
        val title = if (resId > 0) {
            ResourceUtils.getString(this, resId)
        } else {
            IntentUtils.getString(intent, Constants.TITLE)
        }
        return title ?: ""
    }

    override fun handleDragEvent(event: DragEvent?): Boolean? {
        return event?.let { mRemoteFileFragment?.handleDragScroll(it) }
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        return mRemoteFileFragment?.getSelectedItemView()
    }

    override fun setNavigateItemAble() {
        mRemoteFileFragment?.setNavigateItemAble()
    }

    override fun getDragCurrentPath(): String? {
        return getCurrentPath()
    }

    override fun onClickDir(path: String) {
        mRemoteFileFragment?.onClickDir(path)
    }
}