package com.oplus.filemanager.preview

import android.app.Application
import android.content.Context
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.interfaze.filepreview.*
import com.oplus.filemanager.preview.apk.ApkPreviewFragment
import com.oplus.filemanager.preview.archive.ArchivePreviewFragment
import com.oplus.filemanager.preview.audio.AudioPreviewFragment
import com.oplus.filemanager.preview.doc.DocPreviewFragment
import com.oplus.filemanager.preview.image.ImagePreviewFragment
import com.oplus.filemanager.preview.normal.NormalPreviewFragment
import com.oplus.filemanager.preview.remote.RemotePreviewFragment
import com.oplus.filemanager.preview.selection.SelectionHeapUpFragment
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import com.oplus.filemanager.preview.utils.MediaPlayerHelper
import com.oplus.filemanager.preview.video.VideoPreviewFragment
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.verify
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.robolectric.annotation.Config

/**
 * FilePreviewApi的单元测试类
 * 用于测试文件预览API的各种功能
 */
@Config(sdk = [28])
class FilePreviewApiTest {

    // 模拟的Context对象
    private lateinit var mockContext: Context
    // 模拟的Application对象
    private lateinit var mockApplication: Application
    // 模拟的文件对象
    private lateinit var mockFileBean: BaseFileBean

    /**
     * 测试前的初始化方法
     * 创建并配置所有模拟对象
     */
    @Before
    fun setUp() {
        // 创建并配置模拟的Context对象
        mockContext = mockk(relaxed = true) {
            every { applicationContext } returns mockk(relaxed = true) {
                every { getApplicationContext() } returns this
            }
        }
        // 创建模拟的Application对象
        mockApplication = mockk(relaxed = true)
        // 创建模拟的文件对象
        mockFileBean = mockk(relaxed = true)
    }

    /**
     * 测试obtainFilePreviewFragment方法
     * 验证对于不同的预览类型，是否能返回正确的Fragment实例
     */
    @Test
    fun `test obtainFilePreviewFragment should return correct fragment for each type`() {
        // 验证图片类型返回ImagePreviewFragment
        assertTrue(FilePreviewApi.obtainFilePreviewFragment(FILE_PREVIEW_TYPE_IMAGE) is ImagePreviewFragment)
        // 验证音频类型返回AudioPreviewFragment
        assertTrue(FilePreviewApi.obtainFilePreviewFragment(FILE_PREVIEW_TYPE_AUDIO) is AudioPreviewFragment)
        // 验证视频类型返回VideoPreviewFragment
        assertTrue(FilePreviewApi.obtainFilePreviewFragment(FILE_PREVIEW_TYPE_VIDEO) is VideoPreviewFragment)
        // 验证应用类型返回ApkPreviewFragment
        assertTrue(FilePreviewApi.obtainFilePreviewFragment(FILE_PREVIEW_TYPE_APPLICATION) is ApkPreviewFragment)
        // 验证压缩包类型返回ArchivePreviewFragment
        assertTrue(FilePreviewApi.obtainFilePreviewFragment(FILE_PREVIEW_TYPE_ARCHIVE) is ArchivePreviewFragment)
        // 验证文档类型返回DocPreviewFragment
        assertTrue(FilePreviewApi.obtainFilePreviewFragment(FILE_PREVIEW_TYPE_DOC) is DocPreviewFragment)
        // 验证远程文件类型返回RemotePreviewFragment
        assertTrue(FilePreviewApi.obtainFilePreviewFragment(FILE_PREVIEW_TYPE_REMOTE) is RemotePreviewFragment)
        // 验证未知类型返回NormalPreviewFragment
        assertTrue(FilePreviewApi.obtainFilePreviewFragment(999) is NormalPreviewFragment)
    }

    /**
     * 测试obtainFileHeapUpFragment方法
     * 验证是否能正确返回SelectionHeapUpFragment实例
     */
    @Test
    fun `test obtainFileHeapUpFragment should return SelectionHeapUpFragment`() {
        assertTrue(FilePreviewApi.obtainFileHeapUpFragment() is SelectionHeapUpFragment)
    }
}