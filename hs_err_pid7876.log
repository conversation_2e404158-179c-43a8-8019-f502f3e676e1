#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 378576 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:189), pid=7876, tid=5060
#
# JRE version: Java(TM) SE Runtime Environment (17.0.8+9) (build 17.0.8+9-LTS-211)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\Encrypt\build\20250901_13474342416618067799.compiler.options

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Sep  1 11:29:08 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 1.572109 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x00000212bcf81710):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=5060, stack(0x0000003739e00000,0x0000003739f00000)]


Current CompileTask:
C2:   1572 1211   !   4       java.util.zip.InflaterInputStream::read (138 bytes)

Stack: [0x0000003739e00000,0x0000003739f00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x677d0a]
V  [jvm.dll+0x7d8c54]
V  [jvm.dll+0x7da3fe]
V  [jvm.dll+0x7daa63]
V  [jvm.dll+0x245c5f]
V  [jvm.dll+0xaaa6b]
V  [jvm.dll+0xab00c]
V  [jvm.dll+0x361e3f]
V  [jvm.dll+0x32c551]
V  [jvm.dll+0x32b9ea]
V  [jvm.dll+0x217fff]
V  [jvm.dll+0x217431]
V  [jvm.dll+0x1a3af0]
V  [jvm.dll+0x2270be]
V  [jvm.dll+0x22535b]
V  [jvm.dll+0x78e7bc]
V  [jvm.dll+0x788bba]
V  [jvm.dll+0x676b35]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000212ff70e270, length=15, elements={
0x00000212907c3000, 0x00000212bcf5c9b0, 0x00000212bcf5ded0, 0x00000212bcf792f0,
0x00000212bcf79db0, 0x00000212bcf7ae10, 0x00000212bcf7ce80, 0x00000212bcf81710,
0x00000212bcf84010, 0x00000212bcf87100, 0x00000212bd799250, 0x00000212bd7b6c40,
0x00000212bd7c8570, 0x00000212bd91e1f0, 0x00000212bd920050
}

Java Threads: ( => current thread )
  0x00000212907c3000 JavaThread "main" [_thread_in_native_trans, id=16716, stack(0x0000003739100000,0x0000003739200000)]
  0x00000212bcf5c9b0 JavaThread "Reference Handler" daemon [_thread_blocked, id=24068, stack(0x0000003739800000,0x0000003739900000)]
  0x00000212bcf5ded0 JavaThread "Finalizer" daemon [_thread_blocked, id=22900, stack(0x0000003739900000,0x0000003739a00000)]
  0x00000212bcf792f0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=15484, stack(0x0000003739a00000,0x0000003739b00000)]
  0x00000212bcf79db0 JavaThread "Attach Listener" daemon [_thread_blocked, id=19932, stack(0x0000003739b00000,0x0000003739c00000)]
  0x00000212bcf7ae10 JavaThread "Service Thread" daemon [_thread_blocked, id=10392, stack(0x0000003739c00000,0x0000003739d00000)]
  0x00000212bcf7ce80 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=6744, stack(0x0000003739d00000,0x0000003739e00000)]
=>0x00000212bcf81710 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=5060, stack(0x0000003739e00000,0x0000003739f00000)]
  0x00000212bcf84010 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=18080, stack(0x0000003739f00000,0x000000373a000000)]
  0x00000212bcf87100 JavaThread "Sweeper thread" daemon [_thread_blocked, id=26160, stack(0x000000373a000000,0x000000373a100000)]
  0x00000212bd799250 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=22536, stack(0x000000373a100000,0x000000373a200000)]
  0x00000212bd7b6c40 JavaThread "Notification Thread" daemon [_thread_blocked, id=1732, stack(0x000000373a200000,0x000000373a300000)]
  0x00000212bd7c8570 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=16360, stack(0x000000373a400000,0x000000373a500000)]
  0x00000212bd91e1f0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=20584, stack(0x000000373a600000,0x000000373a700000)]
  0x00000212bd920050 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=26228, stack(0x000000373a700000,0x000000373a800000)]

Other Threads:
  0x00000212bcf58710 VMThread "VM Thread" [stack: 0x0000003739700000,0x0000003739800000] [id=10576]
  0x00000212bd7c7490 WatcherThread [stack: 0x000000373a300000,0x000000373a400000] [id=25020]
  0x0000021290871b30 GCTaskThread "GC Thread#0" [stack: 0x0000003739200000,0x0000003739300000] [id=22996]
  0x00000212ff2a2040 GCTaskThread "GC Thread#1" [stack: 0x000000373a800000,0x000000373a900000] [id=23660]
  0x00000212ff2a22f0 GCTaskThread "GC Thread#2" [stack: 0x000000373a900000,0x000000373aa00000] [id=4156]
  0x00000212ff2a25a0 GCTaskThread "GC Thread#3" [stack: 0x000000373aa00000,0x000000373ab00000] [id=21200]
  0x00000212ff2a2850 GCTaskThread "GC Thread#4" [stack: 0x000000373ab00000,0x000000373ac00000] [id=25668]
  0x00000212ff1e3cf0 GCTaskThread "GC Thread#5" [stack: 0x000000373ac00000,0x000000373ad00000] [id=26168]
  0x00000212ff1e3fa0 GCTaskThread "GC Thread#6" [stack: 0x000000373ad00000,0x000000373ae00000] [id=27372]
  0x00000212ff1e4250 GCTaskThread "GC Thread#7" [stack: 0x000000373ae00000,0x000000373af00000] [id=20728]
  0x00000212ff1e4500 GCTaskThread "GC Thread#8" [stack: 0x000000373af00000,0x000000373b000000] [id=17752]
  0x00000212ff1e47b0 GCTaskThread "GC Thread#9" [stack: 0x000000373b000000,0x000000373b100000] [id=12348]
  0x00000212ff2a9c40 GCTaskThread "GC Thread#10" [stack: 0x000000373b100000,0x000000373b200000] [id=19752]
  0x00000212ff2aa9b0 GCTaskThread "GC Thread#11" [stack: 0x000000373b200000,0x000000373b300000] [id=15240]
  0x0000021290882740 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000003739300000,0x0000003739400000] [id=17792]
  0x0000021290883060 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000003739400000,0x0000003739500000] [id=16056]
  0x00000212bce929e0 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000003739500000,0x0000003739600000] [id=24484]
  0x00000212bce93310 ConcurrentGCThread "G1 Service" [stack: 0x0000003739600000,0x0000003739700000] [id=25016]

Threads with active compile tasks:
C2 CompilerThread0     1605 1211   !   4       java.util.zip.InflaterInputStream::read (138 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000604800000, size: 8120 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x00000212be000000-0x00000212bebd0000-0x00000212bebd0000), size 12386304, SharedBaseAddress: 0x00000212be000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000212bf000000-0x00000212ff000000, reserved size: 1073741824
Narrow klass base: 0x00000212be000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32479M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8120M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 520192K, used 29540K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 17030K, committed 17216K, reserved 1114112K
  class space    used 2417K, committed 2496K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%|HS|  |TAMS 0x0000000604800000, 0x0000000604800000| Complete 
|   1|0x0000000604c00000, 0x0000000604eb7000, 0x0000000605000000| 67%| O|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   2|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|   3|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|   4|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|   5|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|   6|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|   7|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|   8|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|   9|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  10|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  11|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  12|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  13|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
|  14|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  15|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  16|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  17|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  18|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  19|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  20|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  21|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Untracked 
|  22|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|  23|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000, 0x000000060a400000| Untracked 
|  24|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000, 0x000000060a800000| Untracked 
|  25|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000, 0x000000060ac00000| Untracked 
|  26|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000, 0x000000060b000000| Untracked 
|  27|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000, 0x000000060b400000| Untracked 
|  28|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000, 0x000000060b800000| Untracked 
|  29|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000, 0x000000060bc00000| Untracked 
|  30|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|  31|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000, 0x000000060c400000| Untracked 
|  32|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000, 0x000000060c800000| Untracked 
|  33|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000, 0x000000060cc00000| Untracked 
|  34|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000, 0x000000060d000000| Untracked 
|  35|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000, 0x000000060d400000| Untracked 
|  36|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000, 0x000000060d800000| Untracked 
|  37|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000, 0x000000060dc00000| Untracked 
|  38|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|  39|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000, 0x000000060e400000| Untracked 
|  40|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000, 0x000000060e800000| Untracked 
|  41|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000, 0x000000060ec00000| Untracked 
|  42|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000, 0x000000060f000000| Untracked 
|  43|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000, 0x000000060f400000| Untracked 
|  44|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000, 0x000000060f800000| Untracked 
|  45|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000, 0x000000060fc00000| Untracked 
|  46|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|  47|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000, 0x0000000610400000| Untracked 
|  48|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000, 0x0000000610800000| Untracked 
|  49|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000, 0x0000000610c00000| Untracked 
|  50|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000, 0x0000000611000000| Untracked 
|  51|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000, 0x0000000611400000| Untracked 
|  52|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000, 0x0000000611800000| Untracked 
|  53|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000, 0x0000000611c00000| Untracked 
|  54|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
|  55|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000, 0x0000000612400000| Untracked 
|  56|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000, 0x0000000612800000| Untracked 
|  57|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000, 0x0000000612c00000| Untracked 
|  58|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000, 0x0000000613000000| Untracked 
|  59|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000, 0x0000000613400000| Untracked 
|  60|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000, 0x0000000613800000| Untracked 
|  61|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000, 0x0000000613c00000| Untracked 
|  62|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
|  63|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000, 0x0000000614400000| Untracked 
|  64|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000, 0x0000000614800000| Untracked 
|  65|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000, 0x0000000614c00000| Untracked 
|  66|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000, 0x0000000615000000| Untracked 
|  67|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000, 0x0000000615400000| Untracked 
|  68|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000, 0x0000000615800000| Untracked 
|  69|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000, 0x0000000615c00000| Untracked 
|  70|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
|  71|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000, 0x0000000616400000| Untracked 
|  72|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000, 0x0000000616800000| Untracked 
|  73|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000, 0x0000000616c00000| Untracked 
|  74|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000, 0x0000000617000000| Untracked 
|  75|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000, 0x0000000617400000| Untracked 
|  76|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000, 0x0000000617800000| Untracked 
|  77|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000, 0x0000000617c00000| Untracked 
|  78|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
|  79|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000, 0x0000000618400000| Untracked 
|  80|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000, 0x0000000618800000| Untracked 
|  81|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000, 0x0000000618c00000| Untracked 
|  82|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000, 0x0000000619000000| Untracked 
|  83|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000, 0x0000000619400000| Untracked 
|  84|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000, 0x0000000619800000| Untracked 
|  85|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000, 0x0000000619c00000| Untracked 
|  86|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000, 0x000000061a000000| Untracked 
|  87|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000, 0x000000061a400000| Untracked 
|  88|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000, 0x000000061a800000| Untracked 
|  89|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000, 0x000000061ac00000| Untracked 
|  90|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000, 0x000000061b000000| Untracked 
|  91|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000, 0x000000061b400000| Untracked 
|  92|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000, 0x000000061b800000| Untracked 
|  93|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000, 0x000000061bc00000| Untracked 
|  94|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000, 0x000000061c000000| Untracked 
|  95|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000, 0x000000061c400000| Untracked 
|  96|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000, 0x000000061c800000| Untracked 
|  97|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000, 0x000000061cc00000| Untracked 
|  98|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000, 0x000000061d000000| Untracked 
|  99|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000, 0x000000061d400000| Untracked 
| 100|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000, 0x000000061d800000| Untracked 
| 101|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000, 0x000000061dc00000| Untracked 
| 102|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000, 0x000000061e000000| Untracked 
| 103|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000, 0x000000061e400000| Untracked 
| 104|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000, 0x000000061e800000| Untracked 
| 105|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000, 0x000000061ec00000| Untracked 
| 106|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000, 0x000000061f000000| Untracked 
| 107|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000, 0x000000061f400000| Untracked 
| 108|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000, 0x000000061f800000| Untracked 
| 109|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000, 0x000000061fc00000| Untracked 
| 110|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000, 0x0000000620000000| Untracked 
| 111|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000, 0x0000000620400000| Untracked 
| 112|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000, 0x0000000620800000| Untracked 
| 113|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000, 0x0000000620c00000| Untracked 
| 114|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 115|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000, 0x0000000621400000| Untracked 
| 116|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000, 0x0000000621800000| Untracked 
| 117|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000, 0x0000000621c00000| Untracked 
| 118|0x0000000622000000, 0x0000000622222350, 0x0000000622400000| 53%| S|CS|TAMS 0x0000000622000000, 0x0000000622000000| Complete 
| 119|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000, 0x0000000622400000| Untracked 
| 120|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000, 0x0000000622800000| Untracked 
| 121|0x0000000622c00000, 0x0000000623000000, 0x0000000623000000|100%| E|  |TAMS 0x0000000622c00000, 0x0000000622c00000| Complete 
| 122|0x0000000623000000, 0x0000000623400000, 0x0000000623400000|100%| E|CS|TAMS 0x0000000623000000, 0x0000000623000000| Complete 
| 123|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000, 0x0000000623400000| Complete 
| 124|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000, 0x0000000623800000| Complete 
| 125|0x0000000623c00000, 0x0000000624000000, 0x0000000624000000|100%| E|CS|TAMS 0x0000000623c00000, 0x0000000623c00000| Complete 
| 126|0x0000000624000000, 0x0000000624400000, 0x0000000624400000|100%| E|CS|TAMS 0x0000000624000000, 0x0000000624000000| Complete 

Card table byte_map: [0x00000212a88d0000,0x00000212a98b0000] _byte_map_base: 0x00000212a58ac000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000021290872050, (CMBitMap*) 0x0000021290872090
 Prev Bits: [0x00000212aa890000, 0x00000212b2770000)
 Next Bits: [0x00000212b2770000, 0x00000212ba650000)

Polling page: 0x000002128e750000

Metaspace:

Usage:
  Non-class:     14.34 MB used.
      Class:      2.37 MB used.
       Both:     16.71 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      14.44 MB ( 23%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       2.44 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      16.88 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  15.38 MB
       Class:  13.50 MB
        Both:  28.88 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 110.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 269.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 679.
num_chunk_merges: 0.
num_chunk_splits: 535.
num_chunks_enlarged: 486.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=558Kb max_used=558Kb free=118609Kb
 bounds [0x000002129fc10000, 0x000002129fe80000, 0x00000212a7070000]
CodeHeap 'profiled nmethods': size=119104Kb used=2000Kb max_used=2000Kb free=117103Kb
 bounds [0x0000021298070000, 0x00000212982e0000, 0x000002129f4c0000]
CodeHeap 'non-nmethods': size=7488Kb used=2880Kb max_used=2918Kb free=4607Kb
 bounds [0x000002129f4c0000, 0x000002129f7b0000, 0x000002129fc10000]
 total_blobs=1715 nmethods=1221 adapters=404
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.380 Thread 0x00000212bd91e1f0 nmethod 1208 0x000002129825b690 code [0x000002129825b840, 0x000002129825bb28]
Event: 1.518 Thread 0x00000212bd799250 1209       3       org.jetbrains.kotlin.com.intellij.util.containers.FastUtilCaseInsensitiveStringHashingStrategy::equals (13 bytes)
Event: 1.518 Thread 0x00000212bd799250 nmethod 1209 0x000002129825bc10 code [0x000002129825bde0, 0x000002129825c248]
Event: 1.555 Thread 0x00000212bd920050 1210       1       org.jetbrains.kotlin.com.intellij.psi.tree.IElementType::getIndex (5 bytes)
Event: 1.555 Thread 0x00000212bd920050 nmethod 1210 0x000002129fc9b610 code [0x000002129fc9b7a0, 0x000002129fc9b878]
Event: 1.560 Thread 0x00000212bcf81710 1211   !   4       java.util.zip.InflaterInputStream::read (138 bytes)
Event: 1.565 Thread 0x00000212bd91e1f0 1212   !   3       org.jetbrains.kotlin.com.intellij.psi.tree.IElementType::<init> (284 bytes)
Event: 1.566 Thread 0x00000212bcf84010 1213       3       org.jetbrains.kotlin.com.intellij.psi.tree.IElementType::<init> (29 bytes)
Event: 1.566 Thread 0x00000212bcf84010 nmethod 1213 0x000002129825c390 code [0x000002129825c560, 0x000002129825c8a8]
Event: 1.566 Thread 0x00000212bd91e1f0 nmethod 1212 0x000002129825c990 code [0x000002129825ce60, 0x000002129825f358]
Event: 1.567 Thread 0x00000212bd91e1f0 1214       3       java.lang.Class::privateGetDeclaredConstructors (79 bytes)
Event: 1.567 Thread 0x00000212bd91e1f0 nmethod 1214 0x0000021298260410 code [0x0000021298260600, 0x0000021298260b68]
Event: 1.567 Thread 0x00000212bd91e1f0 1215       3       jdk.internal.reflect.ReflectionFactory::copyConstructor (11 bytes)
Event: 1.567 Thread 0x00000212bd799250 1216       3       java.lang.reflect.ReflectAccess::copyConstructor (5 bytes)
Event: 1.567 Thread 0x00000212bcf84010 1217       3       java.lang.reflect.Constructor::copy (72 bytes)
Event: 1.567 Thread 0x00000212bd920050 1218       3       java.lang.reflect.Constructor::<init> (50 bytes)
Event: 1.567 Thread 0x00000212bd91e1f0 nmethod 1215 0x0000021298260d10 code [0x0000021298260ec0, 0x00000212982610c8]
Event: 1.567 Thread 0x00000212bd799250 nmethod 1216 0x0000021298261210 code [0x00000212982613c0, 0x0000021298261508]
Event: 1.567 Thread 0x00000212bcf84010 nmethod 1217 0x0000021298261610 code [0x00000212982617e0, 0x0000021298261bd8]
Event: 1.567 Thread 0x00000212bd920050 nmethod 1218 0x0000021298261d10 code [0x0000021298261ee0, 0x0000021298262398]

GC Heap History (4 events):
Event: 0.590 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 520192K, used 24576K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 6775K, committed 6912K, reserved 1114112K
  class space    used 700K, committed 768K, reserved 1048576K
}
Event: 0.597 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 6926K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 6775K, committed 6912K, reserved 1114112K
  class space    used 700K, committed 768K, reserved 1048576K
}
Event: 1.083 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 31502K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 15290K, committed 15488K, reserved 1114112K
  class space    used 2018K, committed 2112K, reserved 1048576K
}
Event: 1.085 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 9060K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 15290K, committed 15488K, reserved 1114112K
  class space    used 2018K, committed 2112K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 1.080 Thread 0x00000212907c3000 DEOPT PACKING pc=0x000002129821c65a sp=0x00000037391fe210
Event: 1.080 Thread 0x00000212907c3000 DEOPT UNPACKING pc=0x000002129f512b43 sp=0x00000037391fd710 mode 0
Event: 1.081 Thread 0x00000212907c3000 DEOPT PACKING pc=0x000002129821c65a sp=0x00000037391fe210
Event: 1.081 Thread 0x00000212907c3000 DEOPT UNPACKING pc=0x000002129f512b43 sp=0x00000037391fd710 mode 0
Event: 1.105 Thread 0x00000212907c3000 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002129fc6aca8 relative=0x00000000000008a8
Event: 1.105 Thread 0x00000212907c3000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002129fc6aca8 method=org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ZipEntryMap.get(Ljava/lang/Object;)Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo; @ 61 c2
Event: 1.105 Thread 0x00000212907c3000 DEOPT PACKING pc=0x000002129fc6aca8 sp=0x00000037391fe200
Event: 1.105 Thread 0x00000212907c3000 DEOPT UNPACKING pc=0x000002129f5123a3 sp=0x00000037391fe1c8 mode 2
Event: 1.106 Thread 0x00000212907c3000 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002129fc67178 relative=0x0000000000000518
Event: 1.106 Thread 0x00000212907c3000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002129fc67178 method=org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.ZipEntryMap.isTheOne(Lorg/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo;Ljava/lang/CharSequence;
Event: 1.106 Thread 0x00000212907c3000 DEOPT PACKING pc=0x000002129fc67178 sp=0x00000037391fe170
Event: 1.106 Thread 0x00000212907c3000 DEOPT UNPACKING pc=0x000002129f5123a3 sp=0x00000037391fe130 mode 2
Event: 1.195 Thread 0x00000212907c3000 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002129fc818e0 relative=0x00000000000011e0
Event: 1.195 Thread 0x00000212907c3000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002129fc818e0 method=java.util.zip.ZipFile$Source.checkAndAddEntry(II)I @ 128 c2
Event: 1.195 Thread 0x00000212907c3000 DEOPT PACKING pc=0x000002129fc818e0 sp=0x00000037391fdd10
Event: 1.195 Thread 0x00000212907c3000 DEOPT UNPACKING pc=0x000002129f5123a3 sp=0x00000037391fdce0 mode 2
Event: 1.196 Thread 0x00000212907c3000 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002129fc85f64 relative=0x0000000000000c84
Event: 1.196 Thread 0x00000212907c3000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002129fc85f64 method=java.util.zip.ZipFile.getZipEntry(Ljava/lang/String;I)Ljava/util/zip/ZipEntry; @ 243 c2
Event: 1.196 Thread 0x00000212907c3000 DEOPT PACKING pc=0x000002129fc85f64 sp=0x00000037391fe330
Event: 1.196 Thread 0x00000212907c3000 DEOPT UNPACKING pc=0x000002129f5123a3 sp=0x00000037391fe1c8 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.617 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x00000006241072c8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x00000006241072c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.648 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x00000006242c4c40}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006242c4c40) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.668 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623cd0c40}: 'long java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623cd0c40) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.670 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623cdc2d0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623cdc2d0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.671 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623cdffc0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623cdffc0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.671 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623ce3628}: 'int java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623ce3628) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.673 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623cf2bb0}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x0000000623cf2bb0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.674 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623cfa7e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x0000000623cfa7e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.675 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d09ba8}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object, java.lang.Object)'> (0x0000000623d09ba8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.677 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d18738}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int, int)'> (0x0000000623d18738) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.678 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d27198}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, long, long)'> (0x0000000623d27198) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.680 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d7d4e8}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int)'> (0x0000000623d7d4e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.693 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623e6c988}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623e6c988) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.697 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623e957d0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623e957d0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.714 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623f74fa8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623f74fa8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.773 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623a98618}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623a98618) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.828 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x00000006236a4170}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000006236a4170) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.897 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233624d0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006233624d0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.906 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233dc600}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006233dc600) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.924 Thread 0x00000212907c3000 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c973a0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c973a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (10 events):
Event: 0.093 Executing VM operation: HandshakeAllThreads
Event: 0.093 Executing VM operation: HandshakeAllThreads done
Event: 0.364 Executing VM operation: HandshakeAllThreads
Event: 0.364 Executing VM operation: HandshakeAllThreads done
Event: 0.436 Executing VM operation: HandshakeAllThreads
Event: 0.436 Executing VM operation: HandshakeAllThreads done
Event: 0.590 Executing VM operation: G1CollectForAllocation
Event: 0.597 Executing VM operation: G1CollectForAllocation done
Event: 1.083 Executing VM operation: G1CollectForAllocation
Event: 1.085 Executing VM operation: G1CollectForAllocation done

Events (20 events):
Event: 1.343 loading class sun/nio/cs/ThreadLocalCoders$Cache
Event: 1.343 loading class sun/nio/cs/ThreadLocalCoders$Cache done
Event: 1.343 loading class sun/nio/cs/ThreadLocalCoders$1 done
Event: 1.343 loading class sun/nio/cs/ThreadLocalCoders$2
Event: 1.343 loading class sun/nio/cs/ThreadLocalCoders$2 done
Event: 1.347 loading class java/beans/PropertyChangeSupport
Event: 1.347 loading class java/beans/PropertyChangeSupport done
Event: 1.347 loading class java/beans/PropertyChangeSupport$PropertyChangeListenerMap
Event: 1.347 loading class java/beans/ChangeListenerMap
Event: 1.347 loading class java/beans/ChangeListenerMap done
Event: 1.347 loading class java/beans/PropertyChangeSupport$PropertyChangeListenerMap done
Event: 1.347 loading class java/beans/PropertyChangeListener
Event: 1.347 loading class java/beans/PropertyChangeListener done
Event: 1.380 Thread 0x00000212bd91f120 Thread exited: 0x00000212bd91f120
Event: 1.518 Thread 0x00000212bd91ec10 Thread exited: 0x00000212bd91ec10
Event: 1.555 Thread 0x00000212bd91cdb0 Thread exited: 0x00000212bd91cdb0
Event: 1.555 Thread 0x00000212bd91e700 Thread exited: 0x00000212bd91e700
Event: 1.560 Thread 0x00000212bd91fb40 Thread exited: 0x00000212bd91fb40
Event: 1.565 Thread 0x00000212bd91d2c0 Thread exited: 0x00000212bd91d2c0
Event: 1.565 Thread 0x00000212bd920560 Thread exited: 0x00000212bd920560


Dynamic libraries:
0x00007ff708300000 - 0x00007ff708310000 	E:\JAVA\bin\java.exe
0x00007ff827030000 - 0x00007ff827228000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8255d0000 - 0x00007ff825692000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff824d50000 - 0x00007ff825046000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8246c0000 - 0x00007ff8247c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff81fdc0000 - 0x00007ff81fddb000 	E:\JAVA\bin\VCRUNTIME140.dll
0x00007ff81fde0000 - 0x00007ff81fdf9000 	E:\JAVA\bin\jli.dll
0x00007ff826e80000 - 0x00007ff826f31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff826340000 - 0x00007ff8263de000 	C:\Windows\System32\msvcrt.dll
0x00007ff825e80000 - 0x00007ff825f1f000 	C:\Windows\System32\sechost.dll
0x00007ff825b10000 - 0x00007ff825c33000 	C:\Windows\System32\RPCRT4.dll
0x00007ff824a20000 - 0x00007ff824a47000 	C:\Windows\System32\bcrypt.dll
0x00007ff825050000 - 0x00007ff8251ed000 	C:\Windows\System32\USER32.dll
0x00007ff81dfe0000 - 0x00007ff81e27a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8248a0000 - 0x00007ff8248c2000 	C:\Windows\System32\win32u.dll
0x00007ff825da0000 - 0x00007ff825dcb000 	C:\Windows\System32\GDI32.dll
0x00007ff824a50000 - 0x00007ff824b69000 	C:\Windows\System32\gdi32full.dll
0x00007ff824980000 - 0x00007ff824a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff823400000 - 0x00007ff82340a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8253f0000 - 0x00007ff82541f000 	C:\Windows\System32\IMM32.DLL
0x00007ff81fd50000 - 0x00007ff81fd5c000 	E:\JAVA\bin\vcruntime140_1.dll
0x00007ffffb0c0000 - 0x00007ffffb14e000 	E:\JAVA\bin\msvcp140.dll
0x00007fffe0350000 - 0x00007fffe0f2e000 	E:\JAVA\bin\server\jvm.dll
0x00007fffeb340000 - 0x00007fffeb349000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff81e3c0000 - 0x00007ff81e3e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff826f40000 - 0x00007ff826f48000 	C:\Windows\System32\PSAPI.DLL
0x00007ff826e10000 - 0x00007ff826e7b000 	C:\Windows\System32\WS2_32.dll
0x00007ff823360000 - 0x00007ff823372000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff81c4f0000 - 0x00007ff81c4fa000 	E:\JAVA\bin\jimage.dll
0x00007ff821e10000 - 0x00007ff822011000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff80fef0000 - 0x00007ff80ff24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff824810000 - 0x00007ff824892000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff818d20000 - 0x00007ff818d45000 	E:\JAVA\bin\java.dll
0x00007fffef390000 - 0x00007fffef467000 	E:\JAVA\bin\jsvml.dll
0x00007ff826570000 - 0x00007ff826cde000 	C:\Windows\System32\SHELL32.dll
0x00007ff8222d0000 - 0x00007ff822a74000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff825fd0000 - 0x00007ff826323000 	C:\Windows\System32\combase.dll
0x00007ff8240f0000 - 0x00007ff82411b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff826d40000 - 0x00007ff826e0d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff825f20000 - 0x00007ff825fcd000 	C:\Windows\System32\SHCORE.dll
0x00007ff825d40000 - 0x00007ff825d9b000 	C:\Windows\System32\shlwapi.dll
0x00007ff8245f0000 - 0x00007ff824614000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff811d70000 - 0x00007ff811d89000 	E:\JAVA\bin\net.dll
0x00007ff81ede0000 - 0x00007ff81eeea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff823e50000 - 0x00007ff823eba000 	C:\Windows\system32\mswsock.dll
0x00007ff810fb0000 - 0x00007ff810fc6000 	E:\JAVA\bin\nio.dll
0x00007ff81bc20000 - 0x00007ff81bc38000 	E:\JAVA\bin\zip.dll
0x00007ff81c4d0000 - 0x00007ff81c4e0000 	E:\JAVA\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\JAVA\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;E:\JAVA\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\Encrypt\build\20250901_13474342416618067799.compiler.options
java_class_path (initial): E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.22\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\kotlin-compiler-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.22\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\kotlin-script-runtime-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.22\20e2c5df715f3240c765cfc222530e2796542021\kotlin-daemon-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8514437120                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8514437120                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\JAVA
CLASSPATH=.;E:\JAVA\lib\dt.jar;E:\JAVA\lib\tools.jar;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\dotnet\;E:\git\Git\cmd;D:\Users\W9096060\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\dotnet\;E:\1.8java\lib\dt.jar;E:\1.8java\lib\tools.jar;E:\1.8java\bin;E:\JAVA\bin;E:\JAVA\lib;D:\Users\W9096060\AppData\Local\Microsoft\WindowsApps;
USERNAME=W9096060
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 18:52 hours

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 32479M (2951M free)
TotalPageFile size 47840M (AvailPageFile size 4M)
current process WorkingSet (physical memory assigned to process): 120M, peak: 122M
current process commit charge ("private bytes"): 659M, peak: 662M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211) for windows-amd64 JRE (17.0.8+9-LTS-211), built on Jun 14 2023 10:34:31 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
