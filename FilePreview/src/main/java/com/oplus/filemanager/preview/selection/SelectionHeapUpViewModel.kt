/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SelectionHeapUpViewModel.kt
 * Description:
 *     The view model for selections heap up page.
 *
 * Version: 1.0
 * Date: 2024-10-21
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-10-21   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat
import com.oplus.filemanager.preview.selection.data.SelectionItem
import com.oplus.filemanager.preview.selection.utils.SelectionConstants.MAX_HEAP_UP_COUNT
import com.oplus.filemanager.preview.selection.utils.SelectionConstants.NOTIFY_IN_SCANNING_1ST_COUNT
import com.oplus.filemanager.preview.selection.utils.SelectionConstants.NOTIFY_IN_SCANNING_2ND_COUNT
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper.Companion.isApkFileType
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper.Companion.isArchiveFileType
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper.Companion.isAudioFileType
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper.Companion.isDocType
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper.Companion.isImageFileType
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper.Companion.isVideoFileType
import com.oplus.filemanager.preview.utils.getMediaType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

internal class SelectionHeapUpViewModel : ViewModel() {

    private companion object {
        private const val TAG = "SelectionHeapUpViewModel"
    }

    private val selectionItems = MutableLiveData<List<SelectionItem>>()
    private val selectionStat = MutableLiveData<SelectionFilesStat?>()
    private val currentSelection = mutableListOf<BaseFileBean>()

    private var selectionScanner: SelectionFilesScanner? = null

    fun launchSelectionItems(
        lifecycleOwner: LifecycleOwner,
        observer: Observer<List<SelectionItem>>
    ) {
        selectionItems.value?.let { observer.onChanged(it) }
        selectionItems.observe(lifecycleOwner, observer)
    }

    fun releaseSelectionItemsLauncher(observer: Observer<List<SelectionItem>>) {
        selectionItems.removeObserver(observer)
    }

    fun launchSelectionStat(
        lifecycleOwner: LifecycleOwner,
        observer: Observer<SelectionFilesStat?>
    ) {
        selectionStat.value.let { observer.onChanged(it) }
        selectionStat.observe(lifecycleOwner, observer)
    }

    fun releaseSelectionStatLauncher(observer: Observer<SelectionFilesStat?>) {
        selectionStat.removeObserver(observer)
    }

    fun stopScanner() {
        selectionScanner?.stop()
    }

    fun updateSelectedFiles(context: Context, selection: List<BaseFileBean>) {
        Log.d(TAG, "updateSelectedFiles: size=${selection.size}")
        if (checkSameAsCurrent(selection)) {
            Log.e(TAG, "updateSelectedFiles: ignore since no selection changes")
            return
        }
        currentSelection.clear()
        currentSelection.addAll(selection)
        if (selection.isEmpty()) {
            selectionItems.value = emptyList()
            selectionStat.value = null
            return
        }
        val newSelectionItems = mutableListOf<SelectionItem>()
        for (fileBean in selection) {
            if (newSelectionItems.size >= MAX_HEAP_UP_COUNT) {
                break
            }
            if (fileBean.mData.isNullOrEmpty()) {
                continue
            }
            val item = SelectionItem(fileBean, MAX_HEAP_UP_COUNT - newSelectionItems.size)
            newSelectionItems.add(item)
        }
        selectionItems.value = newSelectionItems
        selectionStat.value = null
        selectionScanner?.stop()
        selectionScanner = SelectionFilesScanner(context, selection).apply { start() }
    }

    private fun checkSameAsCurrent(selection: List<BaseFileBean>): Boolean {
        if (selection.size != currentSelection.size) {
            return false
        }
        currentSelection.forEachIndexed { index, fileBean ->
            val selectedItem = selection.getOrNull(index) ?: return@forEachIndexed
            if (fileBean.mData != selectedItem.mData) {
                return false
            }
        }
        return true
    }

    private inner class SelectionFilesScanner(
        initContext: Context,
        selection: Collection<BaseFileBean>
    ) {
        private val context = initContext.applicationContext
        private val currentSelection = selection.toList()
        private val typesCountMap = mutableMapOf<Int, Int>()
        private val selectionCount = currentSelection.size

        @Volatile
        private var isStopped = false
        private var sumSize = 0L
        private var pendingNotifyCount = 0
        private var isFirstNotify = true

        private lateinit var scanJob: Job

        fun start() {
            if (selectionCount < 1) {
                return
            }
            if (::scanJob.isInitialized) {
                return
            }
            Log.d(TAG, "SelectionFilesScanner.start: ${hashCode()}")
            scanJob = viewModelScope.launch(Dispatchers.IO) {
                execute(this@launch)
            }
        }

        fun stop() {
            if (!::scanJob.isInitialized) {
                return
            }
            if (isStopped) {
                return
            }
            Log.d(TAG, "SelectionFilesScanner.stop: ${hashCode()}")
            isStopped = true
            scanJob.cancel()
        }

        private fun CoroutineScope.isStopped(): Boolean {
            return isStopped || !isActive
        }

        private suspend fun execute(scope: CoroutineScope) {
            for (fileBean in currentSelection) {
                if (fileBean is RemoteFileBean) {
                    sumSize += fileBean.mSize
                }
            }
            val filePaths = currentSelection.mapNotNull { fileBean ->
                fileBean.mData.takeUnless { it.isNullOrEmpty() }
            }
            if (filePaths.isEmpty()) {
                return
            }
            filePaths.map { File(it) }.forEach {
                if (scope.isStopped()) {
                    return
                }
                scanFileInfo(scope, it)
            }
            if (pendingNotifyCount > 0) {
                // Final notify
                notifyUpdateStat()
            }
        }

        private suspend fun scanFileInfo(scope: CoroutineScope, file: File) {
            val isDir = statFileType(file)
            notifyProgress()
            if (!isDir) {
                return
            }
            val subFiles = file.listFiles().takeUnless { it.isNullOrEmpty() } ?: return
            subFiles.forEach {
                if (scope.isStopped()) {
                    return
                }
                scanFileInfo(scope, it)
            }
        }

        private fun statFileType(file: File): Boolean {
            if (file.isDirectory) {
                countFileType(SelectionFilesStat.TYPE_DIR)
                return true
            }
            val type = file.getMediaType(context)
            when {
                isApkFileType(type) -> countFileType(SelectionFilesStat.TYPE_APK)
                isArchiveFileType(type) -> countFileType(SelectionFilesStat.TYPE_ARCHIVE)
                isAudioFileType(type) -> countFileType(SelectionFilesStat.TYPE_AUDIO)
                isImageFileType(type) -> countFileType(SelectionFilesStat.TYPE_IMAGE)
                isVideoFileType(type) -> countFileType(SelectionFilesStat.TYPE_VIDEO)
                isDocType(type) -> countFileType(SelectionFilesStat.TYPE_DOC)
                else -> countFileType(SelectionFilesStat.TYPE_OTHER)
            }
            sumSize += file.length()
            return false
        }

        private fun countFileType(@SelectionFilesStat.StateFileTypes type: Int) {
            val current = typesCountMap[type] ?: 0
            typesCountMap[type] = (current + 1)
        }

        private suspend fun notifyProgress() {
            val notifyThreshold = if (isFirstNotify) {
                NOTIFY_IN_SCANNING_1ST_COUNT
            } else {
                NOTIFY_IN_SCANNING_2ND_COUNT
            }
            if (pendingNotifyCount < notifyThreshold) {
                pendingNotifyCount++
                return
            }
            pendingNotifyCount = 0
            notifyUpdateStat()
        }

        private suspend fun notifyUpdateStat() {
            isFirstNotify = false
            val statData = SelectionFilesStat(sumSize, selectionCount, typesCountMap.toMap())
            withContext(Dispatchers.Main) {
                selectionStat.value = statData
            }
        }
    }
}