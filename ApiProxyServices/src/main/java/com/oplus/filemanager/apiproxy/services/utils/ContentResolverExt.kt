/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ContentResolverExt
 ** Description : ContentResolver 扩展方法
 ** Version     : 1.0
 ** Date        : 2024/06/17 11:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/06/17       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.utils

import android.content.ContentResolver
import android.os.Binder

/**
 * The default token is caller's UID and PID which has no permission to access inner Provider.
 * So switch to FileManger's UID and PID to invoke when need to redirect exterior accessing.
 */
inline fun <R> ContentResolver.execInInnerProcess(action: ContentResolver.() -> R): R {
    val callingToken = Binder.clearCallingIdentity()
    try {
        return action()
    } finally {
        Binder.restoreCallingIdentity(callingToken)
    }
}