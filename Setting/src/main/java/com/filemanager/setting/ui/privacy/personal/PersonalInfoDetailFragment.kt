/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PersonalInfoDetailFragment
 * * Description: 个人信息明示清单的详情fragment
 * * Version: 1.0
 * * Date : 2024/08/22
 * * Author:********
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     ********         2024/08/22      1.0            create
 ****************************************************************/
package com.filemanager.setting.ui.privacy.personal

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.preference.COUIPreference
import com.coui.appcompat.preference.COUIPreferenceCategory
import com.coui.appcompat.preference.COUIPreferenceWithAppbarFragment
import com.coui.appcompat.preference.COUISpannablePreference
import com.filemanager.common.utils.Log
import com.filemanager.setting.R
import com.filemanager.setting.utils.CollectCountUtils
import com.filemanager.setting.utils.PrivacyCollectIntroduction
import com.filemanager.setting.utils.PrivacyIntroductionFactory
import com.oplus.filemanager.room.utils.CollectTimeUtils

class PersonalInfoDetailFragment : COUIPreferenceWithAppbarFragment() {

    companion object {
        private const val TAG = "PersonalInfoDetailFragment"
        private const val PREF_TIME_FILTER = "personal_detail_pref_time_filter"
        private const val PREF_USE_PURPOSE = "personal_detail_pref_use_purpose"
        private const val PREF_USE_SCENE = "personal_detail_pref_use_scene"
        private const val PREF_COLLECT_CONDITION = "personal_detail_pref_collect_condition"
        private const val PREF_INFO_CONTENT = "personal_detail_pref_info_content"
    }

    private var pref: String = ""
    private lateinit var introduction: PrivacyCollectIntroduction
    private var timeFilterPreference: COUIPreferenceCategory? = null
    private var collectConditionPref: COUIPreference? = null
    private var infoContentPref: COUISpannablePreference? = null
    private var timeFilterPopupWindow: COUIPopupListWindow? = null
    private var selectIndex = 0
    private var timeFilters = arrayOf<String>()

    private var viewModel: PersonalInfoDetailViewModel? = null

    private fun getLayoutResource(): Int {
        return R.xml.oplus_doc_personal_info_detail
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        pref = arguments?.getString(PersonalInfoDetailActivity.PREF, "") ?: ""
        introduction = PrivacyIntroductionFactory.create(pref)
        timeFilters = context.resources.getStringArray(com.filemanager.common.R.array.collect_info_time_filter)
        Log.d(TAG, "attach $pref")
        viewModel = ViewModelProvider(this)[PersonalInfoDetailViewModel::class.java]
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        (view as ViewGroup).removeView(view.findViewById(R.id.appbar_layout))
        addPreferencesFromResource(getLayoutResource())
        initView()
        startObserve()
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        loadData(CollectTimeUtils.LAST_7_DAYS)
    }

    private fun initView() {
        initTimeFilterPreference()
        initUserPurposePreference()
        initUserScenePreference()
        initCollectConditionPreference()
        initInfoContentPreference()
    }

    private fun startObserve() {
        val activity = activity ?: return
        viewModel?.let {
            // 收集个数
            it.collectCount.observe(viewLifecycleOwner) { count ->
                collectConditionPref?.setSummary(CollectCountUtils.getCountStr(activity, pref, count))
            }
            // 收集内容
            it.collectContent.observe(viewLifecycleOwner) { content ->
                infoContentPref?.summary = content
            }
        }
    }

    private fun loadData(filter: Int) {
        if (filter < CollectTimeUtils.LAST_7_DAYS || filter > CollectTimeUtils.LAST_1_YEAR) {
            Log.e(TAG, "loadData filter:$filter out range!!!")
            return
        }
        viewModel?.query(pref, filter)
    }

    /**
     * 过滤时间
     */
    private fun initTimeFilterPreference() {
        timeFilterPreference = findPreference(PREF_TIME_FILTER)
        timeFilterPreference?.setWidgetLayoutClickListener {
            Log.d(TAG, "click time filter")
            showTimeFilterPopupWindow(it)
        }
    }

    /**
     * 使用目的
     */
    private fun initUserPurposePreference() {
        val pref = findPreference<COUIPreference>(PREF_USE_PURPOSE)
        pref?.setSummary(introduction.usePurpose)
    }

    /**
     * 使用场景
     */
    private fun initUserScenePreference() {
        val pref = findPreference<COUIPreference>(PREF_USE_SCENE)
        pref?.setSummary(introduction.useScene)
    }

    /**
     * 收集情况
     */
    private fun initCollectConditionPreference() {
        val activity = activity ?: return
        collectConditionPref = findPreference(PREF_COLLECT_CONDITION)
        val count = introduction.collectCount
        collectConditionPref?.setSummary(CollectCountUtils.getCountStr(activity, pref, count))
    }

    /**
     * 信息内容
     */
    private fun initInfoContentPreference() {
        val activity = activity ?: return
        infoContentPref = findPreference(PREF_INFO_CONTENT)
        val content = introduction.getInfoContent(activity, null)
        infoContentPref?.summary = content
    }

    /**
     * 显示时间过滤的PopupWindow
     */
    private fun showTimeFilterPopupWindow(widgetView: View?) {
        Log.d(TAG, "showTimeFilterPopupWindow selectIndex:$selectIndex")
        val anchorView = widgetView?.findViewById<View>(com.support.preference.R.id.text_in_composition)
        val iconInCombination = widgetView?.findViewById<ImageView>(com.support.preference.R.id.icon_in_composition)
        val activity = activity ?: return
        if (timeFilterPopupWindow == null) {
            timeFilterPopupWindow = COUIPopupListWindow(activity).apply {
                setDismissTouchOutside(true)
                setUseBackgroundBlur(true)
                setOnItemClickListener { _, _, position, _ ->
                    dismiss()
                    selectIndex = position
                    timeFilterPreference?.setTextInRight(timeFilters.get(position))
                    // 重新查询数据
                    loadData(position)
                }
                setOnDismissListener {
                    iconInCombination?.setImageResource(com.filemanager.common.R.drawable.arrow_down)
                }
            }
        }

        val list = ArrayList<PopupListItem>()
        val builder = PopupListItem.Builder()
        timeFilters.forEachIndexed { index, item ->
            builder.reset()
                .setTitle(item)
                .setIsChecked(selectIndex == index)
            list.add(builder.build())
        }
        timeFilterPopupWindow?.apply {
            dismiss()
            itemList = list
            iconInCombination?.setImageResource(com.filemanager.common.R.drawable.arrow_up)
            show(anchorView)
        }
    }

    override fun getTitle(): String {
        activity?.let {
            return it.title.toString()
        }
        return ""
    }
}