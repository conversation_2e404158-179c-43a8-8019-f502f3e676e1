/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.cardwidget.label
 * * Description : 标签与卡片ID的映射关系保存
 * * Version     : 1.0
 * * Date        : 2022/12/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.google.gson.Gson
import com.oplus.cardwidget.util.getCardId
import com.oplus.cardwidget.util.getHostId
import com.oplus.filemanager.cardwidget.label.data.LabelCardCacheData


class LabelCardWidgetCodeUtils {

    companion object {
        private const val TAG = "LabelCardWidgetCodeUtils"
        private const val SP_NAME = "filemanager_label_card"
        val instance: LabelCardWidgetCodeUtils by lazy { LabelCardWidgetCodeUtils() }
    }

    private val cacheList = arrayListOf<LabelCardCacheData>()

    fun saveCard(widgetCode: String, data: LabelCardCacheData) {
        Log.d(TAG, "saveCard widgetCode：$widgetCode  data:$data")
        StatisticsUtils.onCommon(MyApplication.sAppContext, StatisticsUtils.ADD_LABELCRAD_TO_LAUNCHER_SUCCESS)
        PreferencesUtils.put(SP_NAME, widgetCode, Gson().toJson(data))
        refreshCacheList()
    }

    fun deleteCard(widgetCode: String) {
        Log.d(TAG, "deleteCard widgetCode：$widgetCode ")
        val sp = PreferencesUtils.getPreferences(SP_NAME)
        val editor = sp?.edit()
        editor?.remove(widgetCode)
        editor?.apply()
        refreshCacheList()
    }

    fun deleteCardLabelMapping(widgetCode: String, labelId: Long) {
        Log.d(TAG, "deleteCardLabel widgetCode:$widgetCode  labelId:$labelId")
        PreferencesUtils.put(SP_NAME, widgetCode, null)
        refreshCacheList()
    }

    fun getCacheByWidgetCode(context: Context, widgetCode: String): LabelCardCacheData? {
        val json = PreferencesUtils.getString(context, SP_NAME, widgetCode)
        Log.d(TAG, "getCacheByWidgetCode json：$json")
        if (json.isNullOrEmpty()) {
            return null
        }
        return Gson().fromJson(json, LabelCardCacheData::class.java).apply {
            this.widgetCode = widgetCode
        }
    }

    fun getCacheByWidgetCode(widgetCode: String): LabelCardCacheData? {
        val json = PreferencesUtils.getString(SP_NAME, widgetCode)
        Log.d(TAG, "getCacheByWidgetCode json：$json")
        if (json.isNullOrEmpty()) {
            return null
        }
        return Gson().fromJson(json, LabelCardCacheData::class.java).apply {
            this.widgetCode = widgetCode
        }
    }


    fun getCacheList(): List<LabelCardCacheData> {
        if (cacheList.isNotEmpty()) {
            return cacheList
        }
        refreshCacheList()
        return cacheList
    }

    private fun refreshCacheList() {
        cacheList.clear()
        val sp = PreferencesUtils.getPreferences(SP_NAME)
        val all = sp?.all ?: return
        all.forEach { entry ->
            val dataJson = sp.getString(entry.key, null)
            Log.d(TAG, "refreshCacheList all:${all.size} ${entry.key} => $dataJson")
            if (!dataJson.isNullOrEmpty()) {
                val data = Gson().fromJson(dataJson, LabelCardCacheData::class.java)
                data?.widgetCode = entry.key
                cacheList.add(data)
            }
        }
    }

    fun switchLabel(widgetCode: String, labelId: Long) {
        Log.d(TAG, "switchLabel widgetCode:$widgetCode  labelId:$labelId ")
        PreferencesUtils.put(SP_NAME, widgetCode, Gson().toJson(LabelCardCacheData(widgetCode, labelId, LabelCardCacheData.STATUS_NORMAL)))
        refreshCacheList()
    }

    fun clearAll() {
        cacheList.clear()
        val sp = PreferencesUtils.getPreferences(SP_NAME)
        sp?.edit()?.clear()?.apply()
        Log.d(TAG, "clearAll ")
    }

    fun getWidgetCardId(widgetCode: String): Int {
        return widgetCode.getCardId()
    }

    fun getWidgetHostId(widgetCode: String): Int {
        return widgetCode.getHostId()
    }
}