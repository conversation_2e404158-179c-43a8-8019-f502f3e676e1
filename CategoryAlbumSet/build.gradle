plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace "com.oplus.filemanager.category.albumset"
}

dependencies {
    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.recyclerview
    implementation libs.oplus.appcompat.poplist
    implementation libs.oplus.appcompat.toolbar
    implementation libs.oplus.appcompat.sidenavigationbar
    implementation libs.oplus.appcompat.panel
    implementation libs.koin.android

    compileOnly project(':Ad:Overseas')

    implementation project(':Common')
    implementation project(':SelectDir')
    implementation project(':FileOperate')

    if (prop_use_prebuilt_drap_drop_lib.toBoolean()) {
        implementation libs.oplus.filemanager.dragDrop
    } else {
        implementation project(':exportedLibs:DragDropSelection')
    }
}