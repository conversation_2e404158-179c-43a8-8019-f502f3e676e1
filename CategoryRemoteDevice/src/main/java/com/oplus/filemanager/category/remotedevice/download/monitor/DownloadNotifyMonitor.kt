/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DownloadNotifyMonitor
 * * Description : 下载进度的监视器，以通知作为UI展示
 * * Version     : 1.0
 * * Date        : 2025/01/07
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download.monitor

import android.app.Service
import android.content.pm.ServiceInfo
import com.filemanager.common.utils.Log
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean
import com.oplus.filemanager.category.remotedevice.download.notification.DownloadNotifyManager
import com.oplus.filemanager.category.remotedevice.download.service.DownloadTaskInfo

class DownloadNotifyMonitor(context: Service) : DownloadProgressMonitor(context) {

    companion object {
        private const val TAG = "DownloadNotifyMonitor"
        var showForeground = true
    }


    private val notificationManager: DownloadNotifyManager by lazy {
        DownloadNotifyManager(context)
    }

    override fun startMonitor(taskInfo: DownloadTaskInfo) {
        super.startMonitor(taskInfo)
        Log.d(TAG, "startMonitor showProgress $showForeground")
        if (!showForeground) {
            return
        }
        showForeground = false
        notificationManager.createNotifyChannel(closeSound = true)
        val notification = notificationManager.createDownloadingNotification(
            taskInfo.deviceId,
            taskInfo.taskId,
            taskInfo.downloadFiles.get(0).first,
            1.0f,
            taskInfo.downloadFiles.size
        )
        context.startForeground(
            DownloadNotifyManager.NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID,
            notification,
            ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC
        )
    }

    override fun cancelMonitor() {
        Log.d(TAG, "cancelMonitor isSuccess:$isSuccess")
        if (!isSuccess) {
            notificationManager.cancelNotification(DownloadNotifyManager.NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID)
        }
        context.stopForeground(Service.STOP_FOREGROUND_DETACH)
    }

    override fun onDownloadSuccess(taskInfo: DownloadTaskInfo?) {
        val downloadTask = taskInfo ?: return
        notificationManager.showDownloadSuccessNotification(downloadTask.localPaths().get(0), downloadTask)
    }

    override fun onDownloadFail(taskInfo: DownloadTaskInfo?, failMsg: String) {
        val downloadTask = taskInfo ?: return
        notificationManager.showDownloadFailureNotification(downloadTask.deviceId, downloadTask.taskId, failMsg)
    }

    override fun onDownloadPause(taskInfo: DownloadTaskInfo?) {
        super.onDownloadPause(taskInfo)
        val downloadTask = taskInfo ?: return
        notificationManager.showDownloadPauseNotification(downloadTask.deviceId, downloadTask.taskId)
    }

    override fun onDownloadContinue(taskInfo: DownloadTaskInfo?) {
        super.onDownloadContinue(taskInfo)
    }

    override fun onUpdateDownloadProgress(progress: Float, taskInfo: DownloadTaskInfo?) {
        val downloadTask = taskInfo ?: return
        notificationManager.showDownloadProgressNotification(
            downloadTask.deviceId, downloadTask.taskId, downloadTask.localPaths().get(0), progress, downloadTask.downloadFiles.size
        )
        onDownloadContinue(downloadTask)
    }

    override fun onSingleFileProgress(path: String?, progress: Float, taskInfo: DownloadTaskInfo?) {
        super.onSingleFileProgress(path, progress, taskInfo)
        val downloadTask = taskInfo ?: return
        if (progress == DownloadRemoteFileBean.PROGRESS_100) { // 下载完成一个文件
            notificationManager.showDownloadSuccessNotification(path ?: "", downloadTask)
        }
    }
}