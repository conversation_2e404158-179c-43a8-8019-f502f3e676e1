/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.utils
 ** Version: 1.0
 ** Date: 2021/6/11
 ** Author: <EMAIL>
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
</desc></version></data></author> */
package com.filemanager.thumbnail.utils

import android.util.Log
import com.filemanager.thumbnail.ThumbnailConstant

internal object Log {
    private const val THUMB_TAG: String = "Thumbnail"

    @JvmStatic
    var baseLogTag: String = ThumbnailConstant.DEFAULT_BASE_LOG_TAG

    @JvmStatic
    var enableLog: Boolean = true
        set(value) {
            field = value
            logLevel = if (value) Log.VERBOSE else Log.WARN
        }

    private var logLevel = Log.VERBOSE

    @JvmStatic
    fun v(tag: String, msg: String) {
        if (logLevel <= Log.VERBOSE) {
            Log.v(logTag(tag), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun v(msg: String) {
        if (logLevel <= Log.VERBOSE) {
            Log.v(logTag(), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun d(tag: String, msg: String) {
        if (logLevel <= Log.DEBUG) {
            Log.d(logTag(tag), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun d(msg: String) {
        if (logLevel <= Log.DEBUG) {
            Log.d(logTag(), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun i(tag: String, msg: String) {
        if (logLevel <= Log.INFO) {
            Log.i(logTag(tag), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun i(msg: String) {
        if (logLevel <= Log.INFO) {
            Log.i(logTag(), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun w(tag: String, msg: String) {
        if (logLevel <= Log.WARN) {
            Log.w(logTag(tag), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun w(msg: String) {
        if (logLevel <= Log.WARN) {
            Log.w(logTag(), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun w(tag: String, msg: String, ex: Throwable?) {
        if (logLevel <= Log.WARN) {
            Log.w(logTag(tag), handleEmptyMsg(msg), ex)
        }
    }

    @JvmStatic
    fun e(tag: String, msg: String) {
        if (logLevel <= Log.ERROR) {
            Log.e(logTag(tag), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun e(msg: String) {
        if (logLevel <= Log.ERROR) {
            Log.e(logTag(), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun e(msg: String, ex: Throwable?) {
        if (logLevel <= Log.ERROR) {
            Log.e(logTag(), handleEmptyMsg(msg), ex)
        }
    }

    @JvmStatic
    fun e(tag: String, msg: String, ex: Throwable?) {
        if (logLevel <= Log.ERROR) {
            Log.e(logTag(tag), handleEmptyMsg(msg), ex)
        }
    }

    @JvmStatic
    private fun logTag(tag: String = ""): String = if (tag.isEmpty()) {
        "$baseLogTag::$THUMB_TAG"
    } else {
        "$baseLogTag::$THUMB_TAG::$tag"
    }

    @JvmStatic
    private fun handleEmptyMsg(msg: String): String {
        var newMsg = msg
        if (msg.isEmpty()) {
            newMsg = "null or empty"
        }
        return newMsg
    }
}