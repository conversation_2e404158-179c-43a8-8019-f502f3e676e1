/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AccessibilityServiceBridgeTest.kt
 * Description:
 *     The test cases for AccessibilityServiceBridge
 *
 * Version: 1.0
 * Date: 2024-05-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-05-29   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.service

import android.accessibilityservice.AccessibilityService
import io.mockk.MockKAnnotations
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.mockk
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AccessibilityServiceBridgeTest : Assert() {

    @RelaxedMockK
    private lateinit var service: AccessibilityService

    private lateinit var bridge: AccessibilityServiceBridge

    @Before
    fun setUp() {
        // Common Given
        MockKAnnotations.init(this, relaxUnitFun = true)
        bridge = AccessibilityServiceBridge()
    }

    @Test
    fun `should pick service when onServiceConnected`() {
        // When
        bridge.onServiceConnected(service)

        // Then
        assertEquals(bridge.pickService(), service)
    }

    @Test
    fun `should pick null when onServiceDisconnected`() {
        // Given
        bridge.onServiceConnected(service)

        // When
        bridge.onServiceDisconnected(service)

        // Then
        assertEquals(bridge.pickService(), null)
    }

    @Test
    fun `should pick latest service when onServiceConnected if already connected before`() {
        // Given
        val service2 = mockk<AccessibilityService>()
        bridge.onServiceConnected(service)

        // When
        bridge.onServiceConnected(service2)

        // Then
        assertEquals(bridge.pickService(), service2)
    }

    @Test
    fun `should pick connected service when onServiceDisconnected if service is different`() {
        // Given
        val service2 = mockk<AccessibilityService>()
        bridge.onServiceConnected(service)

        // When
        bridge.onServiceDisconnected(service2)

        // Then
        assertEquals(bridge.pickService(), service)
    }
}
