/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowFileHelper.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/10/23
 ** Author: JiaBo Lan
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/10/23      1.0     create
 ****************************************************************/
package shadow;

import android.content.Context;

import com.filemanager.recyclebin.utils.RecycleBinFileHelper;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.io.File;
@Implements(RecycleBinFileHelper.class)
public class ShadowFileHelper {
    private static boolean sIsDelete;

    @Implementation
    public static boolean delete(File file, final Context context) {
        return sIsDelete;
    }

    public static void setsIsDelete(boolean isDelete) {
        sIsDelete = isDelete;
    }
}
