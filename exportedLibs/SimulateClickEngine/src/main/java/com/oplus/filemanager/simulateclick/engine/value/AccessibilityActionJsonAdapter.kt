/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AccessibilityActionJsonAdapter.kt
 * Description:
 *     The adapter to convert AccessibilityAction from json.
 *
 * Version: 1.0
 * Date: 2024-06-05
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-06-05   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.value

import android.os.Build
import android.view.accessibility.AccessibilityNodeInfo.AccessibilityAction
import androidx.annotation.RequiresApi
import androidx.annotation.VisibleForTesting
import com.google.gson.GsonBuilder
import com.oplus.filemanager.simulateclick.engine.utils.SdkUtils

internal class AccessibilityActionJsonAdapter : ScriptJsonAdapter<AccessibilityAction>() {
    override fun onWrite(value: AccessibilityAction): String {
        val result = actionToString[value] ?: ""
        return result
    }

    override fun onRead(jsonStr: String): AccessibilityAction? = stringToAction[jsonStr]

    companion object {
        @VisibleForTesting
        @JvmStatic
        internal val stringToAction: Map<String, AccessibilityAction>
            get() = _stringToAction
        private val _stringToAction: Map<String, AccessibilityAction> by lazy {
            initStringToAction()
        }

        @VisibleForTesting
        @JvmStatic
        internal val actionToString: Map<AccessibilityAction, String>
            get() = _actionToString
        private val _actionToString: Map<AccessibilityAction, String> by lazy {
            initActionToString()
        }

        @Suppress("NewApi")
        @VisibleForTesting
        @JvmStatic
        internal fun initStringToAction(): Map<String, AccessibilityAction> = mutableMapOf(
            "ACTION_FOCUS" to AccessibilityAction.ACTION_FOCUS,
            "ACTION_CLEAR_FOCUS" to AccessibilityAction.ACTION_CLEAR_FOCUS,
            "ACTION_SELECT" to AccessibilityAction.ACTION_SELECT,
            "ACTION_CLEAR_SELECTION" to AccessibilityAction.ACTION_CLEAR_SELECTION,
            "ACTION_CLICK" to AccessibilityAction.ACTION_CLICK,
            "ACTION_LONG_CLICK" to AccessibilityAction.ACTION_LONG_CLICK,
            "ACTION_ACCESSIBILITY_FOCUS" to AccessibilityAction.ACTION_ACCESSIBILITY_FOCUS,
            "ACTION_CLEAR_ACCESSIBILITY_FOCUS" to AccessibilityAction.ACTION_CLEAR_ACCESSIBILITY_FOCUS,
            "ACTION_NEXT_AT_MOVEMENT_GRANULARITY" to AccessibilityAction.ACTION_NEXT_AT_MOVEMENT_GRANULARITY,
            "ACTION_PREVIOUS_AT_MOVEMENT_GRANULARITY" to AccessibilityAction.ACTION_PREVIOUS_AT_MOVEMENT_GRANULARITY,
            "ACTION_NEXT_HTML_ELEMENT" to AccessibilityAction.ACTION_NEXT_HTML_ELEMENT,
            "ACTION_PREVIOUS_HTML_ELEMENT" to AccessibilityAction.ACTION_PREVIOUS_HTML_ELEMENT,
            "ACTION_SCROLL_FORWARD" to AccessibilityAction.ACTION_SCROLL_FORWARD,
            "ACTION_SCROLL_BACKWARD" to AccessibilityAction.ACTION_SCROLL_BACKWARD,
            "ACTION_COPY" to AccessibilityAction.ACTION_COPY,
            "ACTION_PASTE" to AccessibilityAction.ACTION_PASTE,
            "ACTION_CUT" to AccessibilityAction.ACTION_CUT,
            "ACTION_SET_SELECTION" to AccessibilityAction.ACTION_SET_SELECTION,
            "ACTION_EXPAND" to AccessibilityAction.ACTION_EXPAND,
            "ACTION_COLLAPSE" to AccessibilityAction.ACTION_COLLAPSE,
            "ACTION_DISMISS" to AccessibilityAction.ACTION_DISMISS,
            "ACTION_SET_TEXT" to AccessibilityAction.ACTION_SET_TEXT,
            "ACTION_SHOW_ON_SCREEN" to AccessibilityAction.ACTION_SHOW_ON_SCREEN,
            "ACTION_SCROLL_TO_POSITION" to AccessibilityAction.ACTION_SCROLL_TO_POSITION,
            "ACTION_SCROLL_UP" to AccessibilityAction.ACTION_SCROLL_UP,
            "ACTION_SCROLL_LEFT" to AccessibilityAction.ACTION_SCROLL_LEFT,
            "ACTION_SCROLL_DOWN" to AccessibilityAction.ACTION_SCROLL_DOWN,
            "ACTION_SCROLL_RIGHT" to AccessibilityAction.ACTION_SCROLL_RIGHT,
            "ACTION_PAGE_UP" to AccessibilityAction.ACTION_PAGE_UP,
            "ACTION_PAGE_DOWN" to AccessibilityAction.ACTION_PAGE_DOWN,
            "ACTION_PAGE_LEFT" to AccessibilityAction.ACTION_PAGE_LEFT,
            "ACTION_PAGE_RIGHT" to AccessibilityAction.ACTION_PAGE_RIGHT,
            "ACTION_CONTEXT_CLICK" to AccessibilityAction.ACTION_CONTEXT_CLICK,
            "ACTION_SET_PROGRESS" to AccessibilityAction.ACTION_SET_PROGRESS,
            "ACTION_MOVE_WINDOW" to AccessibilityAction.ACTION_MOVE_WINDOW,
            "ACTION_SHOW_TOOLTIP" to AccessibilityAction.ACTION_SHOW_TOOLTIP,
            "ACTION_HIDE_TOOLTIP" to AccessibilityAction.ACTION_HIDE_TOOLTIP,
            "ACTION_PRESS_AND_HOLD" to AccessibilityAction.ACTION_PRESS_AND_HOLD,
            "ACTION_IME_ENTER" to getImeEnterAction(),
        ).also {
            if (SdkUtils.isAtLeastT()) {
                it["ACTION_DRAG_START"] = AccessibilityAction.ACTION_DRAG_START
                it["ACTION_DRAG_DROP"] = AccessibilityAction.ACTION_DRAG_DROP
                it["ACTION_DRAG_CANCEL"] = AccessibilityAction.ACTION_DRAG_CANCEL
                it["ACTION_SHOW_TEXT_SUGGESTIONS"] =
                    AccessibilityAction.ACTION_SHOW_TEXT_SUGGESTIONS
            }
            if (SdkUtils.isAtLeastU()) {
                it["ACTION_SCROLL_IN_DIRECTION"] = AccessibilityAction.ACTION_SCROLL_IN_DIRECTION
            }
        }

        @VisibleForTesting
        @JvmStatic
        internal fun initActionToString(): Map<AccessibilityAction, String> =
            mutableMapOf<AccessibilityAction, String>().also {
                stringToAction.forEach { (str, action) ->
                    it[action] = str
                }
            }

        @JvmStatic
        fun registerJsonAdapter(gsonBuilder: GsonBuilder) {
            gsonBuilder.registerTypeAdapter(
                AccessibilityAction::class.java,
                AccessibilityActionJsonAdapter()
            )
        }
    }
}

/**
 * Just for mock in test since [AccessibilityAction.ACTION_IME_ENTER] is null under Junit.
 */
@RequiresApi(Build.VERSION_CODES.R)
@VisibleForTesting
internal fun getImeEnterAction(): AccessibilityAction =
    AccessibilityAction.ACTION_IME_ENTER