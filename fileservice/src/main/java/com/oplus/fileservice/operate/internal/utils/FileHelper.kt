/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileHelper
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/7 15:34
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/7       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.utils

import com.filemanager.common.utils.Log
import java.io.File

private const val TAG = "FileHelper"

fun exists(data: String): <PERSON><PERSON>an {
    try {
        val file = File(data)
        return exists(file)
    } catch (e: SecurityException) {
        Log.w(TAG, "$data file exists error: ${e.message}")
    }
    return false
}

fun exists(file: File): Boolean {
    try {
        return file.exists()
    } catch (e: SecurityException) {
        Log.w(TAG, "file exists error: ${e.message}")
    }
    return false
}