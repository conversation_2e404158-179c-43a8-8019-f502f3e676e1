/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SuperLoaderTest
 * * Description : SuperLoader Unit Test
 * * Version     : 1.0
 * * Date        : 2022/12/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.superapp.ui.superapp

import android.content.Context
import android.text.TextUtils
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.Constants
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.BlacklistParser
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.interfaze.main.IMain
import io.mockk.every
import io.mockk.just
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import java.io.File

class SuperLoaderTest {

    private lateinit var context: Context
    private lateinit var loader: SuperLoader

    private val mainAction = mockk<IMain>()
    private val documentExtension = mockk<IDocumentExtensionType>()

    private val koinApp = koinApplication {
        modules(module {
            single { mainAction }
            single { documentExtension }
        })
    }

    @Before
    fun setup() {
        context = mockk<Context>().apply {
            every { applicationContext }.returns(this)
            every { filesDir }.returns(File(""))
        }
        MyApplication.init(context)
        val tempFileList = arrayListOf<BaseFileBean>()
        loader = mockk<SuperLoader>().apply {
            every { mTempFileList }.returns(tempFileList)
        }
        mockkStatic(SortModeUtils::class)
        every { SortModeUtils.getSharedSortOrder(any()) }.returns(true)
        mockkObject(SortHelper)
        justRun { SortHelper.sortCategoryFiles(any(), any(), any(), any()) }
        mockkStatic(JavaFileHelper::class)
        mockkObject(HiddenFileHelper)
        mockkStatic(TextUtils::class)
        mockkStatic(BlacklistParser::class)
        every { TextUtils.isEmpty(any()) }.answers {
            val str = firstArg<CharSequence>()
            str.isEmpty()
        }
        justRun { mainAction.findFileLabelIfHad(any()) }
        startKoin(koinApp)
        val context = mockk<Context>(relaxed = true)
        mockkObject(MyApplication)
        every { appContext }.returns(context)
        mockkStatic(AndroidDataHelper::class)
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getInternalSdPath(context) }.returns("test")
    }

    @After
    fun teardown() {
        unmockkStatic(SortModeUtils::class)
        unmockkObject(SortHelper)
        unmockkStatic(JavaFileHelper::class)
        unmockkObject(HiddenFileHelper)
        unmockkStatic(BlacklistParser::class)
        unmockkStatic(TextUtils::class)
        stopKoin()
    }

    private fun createFiles(): File {
        val sdcardFile = File("sdcard")
        if (!sdcardFile.exists()) {
            sdcardFile.mkdir()
        }
        val dcimFile = File(sdcardFile, "DCIM")
        if (!dcimFile.exists()) {
            dcimFile.mkdirs()
        }
        val textFile = File(sdcardFile, "测试.txt")
        if (!textFile.exists()) {
            textFile.createNewFile()
        }
        val imgFile = File(dcimFile, "1.png")
        if (!imgFile.exists()) {
            imgFile.createNewFile()
        }
        return sdcardFile
    }

    private fun deleteFiles() {
        val sdcardFile = File("sdcard")
        if (sdcardFile.exists()) {
            sdcardFile.delete()
        }
        val dcimFile = File(sdcardFile, "DCIM")
        if (dcimFile.exists()) {
            dcimFile.delete()
        }
        val textFile = File(sdcardFile, "测试.txt")
        if (textFile.exists()) {
            textFile.delete()
        }
        val imgFile = File(dcimFile, "1.png")
        if (imgFile.exists()) {
            imgFile.delete()
        }
    }

    @Test
    fun should_when_createFromPath() {
        every { loader.createFromPath(any(), any(), any()) }.answers { callOriginal() }
        every { loader.traverseAllFiles(any(), any()) }.answers { callOriginal() }
        every { loader.filterFile(any()) }.answers { callOriginal() }
        every { loader.recursiveFileDirectory(any()) }.answers { callOriginal() }
        var list = loader.createFromPath("", "sdcard", "\\DCIM")
        println("list 11111 $list")
        Assert.assertEquals(1, list.size)

        val sdcardFile = createFiles()
        list = loader.createFromPath("", sdcardFile.absolutePath, "")
        println("list 2222 $list")
        Assert.assertEquals(2, list.size)

        deleteFiles()
    }

    @Test
    fun should_when_preHandleResultBackground() {
        every { loader.preHandleResultBackground(any()) }.answers { callOriginal() }
        every { documentExtension.sortFileIgnoreHeadLabel(any(), any(), any(), any()) } just runs
        loader.preHandleResultBackground(emptyList())
        verify { documentExtension.sortFileIgnoreHeadLabel(any(), any(), any(), any()) }
        verify { mainAction.findFileLabelIfHad(any()) }
    }

    @Test
    fun should_when_recursiveFileDirectory() {
        every { loader.recursiveFileDirectory(any()) }.answers { callOriginal() }
        every { HiddenFileHelper.isNeedShowHiddenFile() }.returns(false)
        every { loader.filterFile(any()) }.answers { callOriginal() }
        val list = mutableListOf<BaseFileBean>()
        every { JavaFileHelper.listFileBeans(any(), any()) }.returns(list)
        loader.recursiveFileDirectory(BaseFileBean())
        Assert.assertTrue(loader.mTempFileList.isEmpty())

        list.add(BaseFileBean().apply {
            this.mData = "sdcard"
            this.mDisplayName = "sdcard"
            this.mIsDirectory = true
        })
        list.add(BaseFileBean().apply {
            this.mData = "sdcard/DCIM"
            this.mDisplayName = "DCIM"
            this.mIsDirectory = true
        })
        list.add(BaseFileBean().apply {
            this.mData = "sdcard/DCIM/1.png"
            this.mDisplayName = "1.png"
            this.mIsDirectory = false
        })

        loader.recursiveFileDirectory(BaseFileBean())
        Assert.assertEquals(loader.mTempFileList.size, 3)
    }

    @Test
    fun should_return_boolean_when_filterFile() {
        every { loader.filterFile(any()) }.answers { callOriginal() }
        val file = BaseFileBean()
        file.mDisplayName = ""
        Assert.assertFalse(loader.filterFile(file))

        BlacklistParser.sFilterNoSuffixFile = BlacklistParser.FILTER_NO_SUFFIX_STATE
        every { BlacklistParser.isFilterNoSuffixFile(any()) }.returns(true)
        file.mDisplayName = "1.png"
        Assert.assertFalse(loader.filterFile(file))

        every { BlacklistParser.isFilterNoSuffixFile(any()) }.returns(false)
        every { loader.mTabPosition }.returns(Constants.TAB_VIDEO)
        file.mLocalType = MimeTypeHelper.IMAGE_TYPE
        Assert.assertFalse(loader.filterFile(file))

        every { loader.mTabPosition }.returns(Constants.TAB_IMAGE)
        file.mLocalType = MimeTypeHelper.IMAGE_TYPE
        Assert.assertTrue(loader.filterFile(file))
    }

    @Test
    fun should_return_list_when_getVolume() {
        every { loader.getVolume() }.answers { callOriginal() }
        every { loader.mInternalPath }.returns("/sdcard")
        every { loader.mExternalPath }.returns(null)
        every { loader.mInternalPath999 }.returns(null)
        var list = loader.getVolume()
        Assert.assertEquals(1, list.size)

        every { loader.mExternalPath }.returns("/data")
        list = loader.getVolume()
        Assert.assertEquals(2, list.size)

        every { loader.mInternalPath999 }.returns("/99")
        list = loader.getVolume()
        Assert.assertEquals(3, list.size)
    }

    @Test
    fun should_return_list_when_getPath() {
        every { loader.getPath() }.answers { callOriginal() }
        every { loader.mSuperPath }.returns(arrayOf())
        var pathList = loader.getPath()
        Assert.assertTrue(pathList.isEmpty())

        every { loader.mSuperPath }.returns(arrayOf("/", "/internal"))
        pathList = loader.getPath()
        Assert.assertEquals(pathList.size, 2)
    }

    @Test
    fun should_return_null_when_getFilterList() {
        every { loader.getFilterList() }.answers { callOriginal() }
        Assert.assertNull(loader.getFilterList())
    }

    @Test
    fun should_return_int_when_getItemKey() {
        every { loader.getItemKey(any()) }.answers { callOriginal() }
        val file = BaseFileBean()
        file.mData = ""
        Assert.assertNull(loader.getItemKey(file))

        file.mData = "/sdcard/img/1.png"
        Assert.assertNotNull(loader.getItemKey(file))
    }

    @Test
    fun should_when_traverseAllFiles() {
        every { loader.traverseAllFiles(any(), any(), any(), any()) }.answers { callOriginal() }
        every { HiddenFileHelper.isHiddenFile(any()) }.returns(false)
        every { HiddenFileHelper.isNeedShowHiddenFile() }.returns(false)
        val sdcardFile = createFiles()
        val path = sdcardFile.absolutePath

        val list = loader.traverseAllFiles(path, maxDepth = Int.MAX_VALUE, includeDir = true)
        println("list11111111 ${list.size}")

        loader.traverseAllFiles(path, maxDepth = Int.MAX_VALUE, includeDir = false)
        println("list222222222 ${list.size}")

        loader.traverseAllFiles(path, maxDepth = Int.MAX_VALUE, includeDir = false) {
            it.mLocalType == MimeTypeHelper.TXT_TYPE
        }
        println("list2222222222 ${list.size}")

        deleteFiles()
    }

    @Test
    fun should_when_updatePath() {
        every { loader.updatePath(any()) }.answers { callOriginal() }
        every { loader.mSuperPath = any() }.answers { callOriginal() }
        every { loader.mSuperPath }.answers { callOriginal() }

        loader.updatePath(arrayOf())
        Assert.assertTrue(loader.mSuperPath.isEmpty())

        loader.updatePath(arrayOf("/", "/internal"))
        Assert.assertEquals(loader.mSuperPath.size, 2)
    }
}