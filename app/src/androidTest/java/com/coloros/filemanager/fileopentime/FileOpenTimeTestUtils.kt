package com.coloros.filemanager.fileopentime

import android.content.ContentValues
import android.database.Cursor
import com.coloros.filemanager.utils.TestConstants
import com.coloros.filemanager.utils.TestConstants.TIME_WAIT_ONE_SEC
import com.coloros.filemanager.utils.TestLog
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.utils.Utils
import com.oplus.autotest.olt.testlib.utils.FileUtils
import com.oplus.fileopentime.provider.FileTimeStore
import com.oplus.fileopentime.utils.FileTimeUtil
import java.io.File

object FileOpenTimeTestUtils {

    private val TAG = FileOpenTimeTestUtils::class.java.simpleName

    /**
     * 删除测试数据
     * @param txtFileOpenTimePath 需要删除的文件路径
     * @param isDeleteRooPath Ture删除测试文件夹, False不删除
     */
    fun cleanTestResource(txtFileOpenTimePath: String, isDeleteRooPath: Boolean) {
        //删除测试数据，不删除测试文件夹
        FileUtils.deleteAllFilesAndDir(txtFileOpenTimePath, isDeleteRooPath)
        FileTimeUtil.deleteFileByFilePath(txtFileOpenTimePath)
        //系统媒体库同步
        MediaScannerCompat.sendMediaScanner(txtFileOpenTimePath, Utils.MEDIA_SCAN_SAVE)
        Thread.sleep(TIME_WAIT_ONE_SEC)
    }

    /**
     * 创建txt文件
     * @param txtFileOpenTimePath 文件路径
     */
    fun createTestFileTxt(txtFileOpenTimePath: String, isQueryTest: Boolean = true) {
        val testRootPath = File(TestConstants.PATH_FILE_MANAGER_TEST)
        //文件夹不存在，先创建文件夹
        if (!testRootPath.exists()) {
            assert(testRootPath.mkdirs()) { "The file create should be success!" }
        }
        val testFile = File(txtFileOpenTimePath)
        //文件不纯在，即创建
        if (!testFile.exists()) {
            assert(testFile.createNewFile()) { "The file create should be success!" }
        }
        //同步文件，触发媒体库数据同步
        MediaScannerCompat.sendMediaScanner(txtFileOpenTimePath, Utils.MEDIA_SCAN_SAVE)
        //由于耗时操作在子线程执行，有回调，这里不做回调处理，睡眠1秒等待。
        Thread.sleep(TIME_WAIT_ONE_SEC)
        //接口调用，更新file_open_time数据库
        if (isQueryTest) {
            FileTimeUtil.addOrUpdateFileTime(txtFileOpenTimePath, testFile.lastModified())
            Thread.sleep(TIME_WAIT_ONE_SEC)
        }
    }

    /**
     * 校验接口查询返回的数据集
     * @param cursor 数据库查询游标
     * @param txtFileOpenTimePath 需要校验、存在的txt文件路径
     * @param isLikeQuery Ture 为模糊查询， False 为精确查询， 默认False
     */
    fun checkCursorData(cursor: Cursor, txtFileOpenTimePath: String, isLikeQuery: Boolean = false) {
        TestLog.d(TAG, "checkCursorData isLikeQuery:$isLikeQuery, cursor.count:${cursor.count}")
        //like查询方法，会返回多个数据，但数量必须 >= 1
        if (isLikeQuery) assert(cursor.count >= 1) {
            "The cursor count should be >= 1."
        } else assert(cursor.count == 1) {
            "The cursor count should be 1."
        }
        val pathList: ArrayList<String> = arrayListOf()
        while (cursor.moveToNext()) {
            //解析cursor数据
            val id = cursor.getLong(cursor.getColumnIndex(FileTimeStore.Columns.ID))
            val filePath = cursor.getString(cursor.getColumnIndex(FileTimeStore.Columns.FILE_PATH))
            val fileCreateTime = cursor.getLong(cursor.getColumnIndex(FileTimeStore.Columns.FILE_CREATE_TIME))
            val fileOpenTime = cursor.getLong(cursor.getColumnIndex(FileTimeStore.Columns.FILE_OPEN_TIME))
            //避免无testFilePath数据返回，全部continue了，因此这里先记录查询返回的filePath, 循环外在做列表包含testFilePath校验。
            pathList.add(filePath)
            //属于like查询方式，且filePath不一致，跳过当次循环的判断
            if (isLikeQuery && filePath != txtFileOpenTimePath) {
                continue
            }
            assert(id > 0) {
                "The id should be > 0."
            }
            assert(filePath == txtFileOpenTimePath) {
                "The filePath:$filePath should bu equals testFilePath:$txtFileOpenTimePath"
            }
            assert(fileCreateTime < fileOpenTime) {
                "The fileCreateTime:$fileCreateTime < fileOpenTime:$fileOpenTime!"
            }
        }
        //这里确保查询的结果中必须存在testFilePath数据。
        assert(pathList.contains(txtFileOpenTimePath)) {
            "The pathList should be contains testFilePath:$txtFileOpenTimePath"
        }
    }

    /**
     * 获取指定文件的创建时间 & 打开时间
     * @param listData 被测试接口返回列表数据
     * @param txtFileOpenTimePath 是否是第一次创建文件
     * @param txtFileOpenTimePath 测试文件路径
     * @return pair<create_time, open_time> 多个参数返回《创建时间、打开时间》
     */
    fun checkFileOpenTime(listData: ArrayList<ContentValues>?, txtFileOpenTimePath: String): Pair<Long, Long> {
        listData?.forEach {
            val filePath = it.get(FileTimeStore.Columns.FILE_PATH) as String
            if (txtFileOpenTimePath == filePath) {
                val fileId = it.get(FileTimeStore.Columns.ID) as Long
                val fileOpenTime = it.get(FileTimeStore.Columns.FILE_OPEN_TIME) as Long
                val fileCreateTime = it.get(FileTimeStore.Columns.FILE_CREATE_TIME) as Long

                TestLog.d(
                    TAG,
                    "fileId:$fileId, filePath:$filePath, fileOpenTime:$fileOpenTime, fileCreateTime:$fileCreateTime"
                )

                //校验：1、文件新创建时，文件的最近打开时间为文件的创建时间！
                assert(fileId > 0L) { "The fileId is mediaStoreId should be > 0" }
                return Pair(fileCreateTime, fileOpenTime)
            }
        }
        return Pair(-1L, -1L)
    }
}