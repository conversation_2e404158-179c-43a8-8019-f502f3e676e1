/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : StorageLayoutCompatHelperTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/3/8 14:38
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/3/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.main.ui.category

import android.content.res.Resources
import android.text.TextUtils
import android.util.DisplayMetrics
import android.view.View
import android.view.ViewGroup
import android.widget.HorizontalScrollView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.BOTTOM
import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.END
import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.START
import androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.TOP
import androidx.constraintlayout.widget.ConstraintSet
import androidx.transition.TransitionManager
import androidx.transition.TransitionSet
import com.coloros.filemanager.R
import com.filemanager.common.constants.KtConstants.STATE_MOUNTED
import com.filemanager.common.constants.KtConstants.STATE_UNMOUNTED
import com.oplus.filemanager.main.ui.transition.ExplodeTransition
import com.oplus.filemanager.main.ui.transition.SlideTransition
import com.oplus.filemanager.main.ui.uistate.Storage
import com.oplus.filemanager.main.view.ScaleSizeTextView
import io.mockk.MockKAnnotations
import io.mockk.mockk
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.verify
import io.mockk.just
import io.mockk.runs
import io.mockk.InternalPlatformDsl
import io.mockk.justRun
import io.mockk.mockkConstructor
import io.mockk.unmockkConstructor
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.lang.ref.WeakReference
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class StorageLayoutCompatHelperTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkConstructor(ConstraintSet::class)
        mockkStatic(TransitionManager::class)
        mockkStatic(Resources::class)
    }

    @Test
    fun `test init scrollView`() {
        val scrollView = mockk<HorizontalScrollView>()
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        storageLayoutCompatHelper.initScrollView(scrollView)
        val scrollViewLayout =
            InternalPlatformDsl.dynamicGet(
                storageLayoutCompatHelper,
                "mScrollView"
            ) as WeakReference<*>
        Assert.assertEquals(scrollView, scrollViewLayout.get())
    }

    @Test
    fun `test init Storage layout`() {
        val storageLayout = mockk<ConstraintLayout>()
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        storageLayoutCompatHelper.initStorageLayout(storageLayout)
        val scrollViewLayout =
            InternalPlatformDsl.dynamicGet(
                storageLayoutCompatHelper,
                "mStorageLayout"
            ) as WeakReference<*>
        Assert.assertEquals(storageLayout, scrollViewLayout.get())
    }

    @Test
    fun `test init PhoneStorage layout`() {
        val storageLayout = mockk<ConstraintLayout>()
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        storageLayoutCompatHelper.initPhoneStorageLayout(storageLayout)
        val layout =
            InternalPlatformDsl.dynamicGet(
                storageLayoutCompatHelper,
                "mPhoneStorageLayout"
            ) as WeakReference<*>
        Assert.assertEquals(storageLayout, layout.get())
    }

    @Test
    fun `test init CloudDisk layout`() {
        val storageLayout = mockk<ConstraintLayout>()
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        storageLayoutCompatHelper.initCloudDiskLayout(storageLayout)
        val layout =
            InternalPlatformDsl.dynamicGet(
                storageLayoutCompatHelper,
                "mCloudDiskStorageLayout"
            ) as WeakReference<*>
        Assert.assertEquals(storageLayout, layout.get())
    }

    @Test
    fun `test init Otg layout`() {
        val storageLayout = mockk<ConstraintLayout>()
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        storageLayoutCompatHelper.initOtgLayout(storageLayout)
        val layout =
            InternalPlatformDsl.dynamicGet(
                storageLayoutCompatHelper,
                "mOTGStorageLayout"
            ) as WeakReference<*>
        Assert.assertEquals(storageLayout, layout.get())
    }

    @Test
    fun `test init SdCard layout`() {
        val storageLayout = mockk<ConstraintLayout>()
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        storageLayoutCompatHelper.initSdCardLayout(storageLayout)
        val layout =
            InternalPlatformDsl.dynamicGet(
                storageLayoutCompatHelper,
                "mSdCardStorageLayout"
            ) as WeakReference<*>
        Assert.assertEquals(storageLayout, layout.get())
    }

    @Test
    fun `test re-layout storage layout when only phone`() {
        val storageItems = mutableListOf<Storage>().apply {
            add(Storage.PhoneStorage(TOTAL_SIZE, AVAILABLE_SIZE))
            add(Storage.OTGStorage(listOf(), Pair(TOTAL_SIZE, AVAILABLE_SIZE), STATE_UNMOUNTED))
            add(Storage.SDCardStorage(TOTAL_SIZE, AVAILABLE_SIZE, STATE_UNMOUNTED))
            add(Storage.CloudDiskStorage(isSingedIn = false))
        }
        val displayMetrics = mockk<DisplayMetrics>()
        displayMetrics.density = DEFAULT_DENSITY
        val resources = mockk<Resources>()
        every { resources.displayMetrics } returns displayMetrics
        mockkStatic(Resources::class)
        every { Resources.getSystem() } returns resources
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        every { storageLayoutCompatHelper["modifyPhoneStorageConstraintSet"](allAny<Boolean>()) } returns Unit
        storageLayoutCompatHelper.reLayoutStorageLayout(storageItems)
        verify(exactly = 1) { storageLayoutCompatHelper["modifyPhoneStorageConstraintSet"](true) }
    }

    @Test
    fun `test re-layout storage layout when mount sd card`() {
        val storageItems = mutableListOf<Storage>().apply {
            add(Storage.PhoneStorage(TOTAL_SIZE, AVAILABLE_SIZE))
            add(Storage.SDCardStorage(TOTAL_SIZE, AVAILABLE_SIZE, STATE_MOUNTED))
        }
        val displayMetrics = mockk<DisplayMetrics>()
        displayMetrics.density = DEFAULT_DENSITY
        val resources = mockk<Resources>()
        every { resources.displayMetrics } returns displayMetrics
        mockkStatic(Resources::class)
        every { Resources.getSystem() } returns resources
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        every { storageLayoutCompatHelper["modifyPhoneStorageConstraintSet"](allAny<Boolean>()) } returns Unit
        storageLayoutCompatHelper.reLayoutStorageLayout(storageItems)
        verify(exactly = 1) { storageLayoutCompatHelper["modifyPhoneStorageConstraintSet"](false) }
    }

    @Test
    fun `test re-layout storage layout when mount otg card`() {
        val storageItems = mutableListOf<Storage>().apply {
            add(Storage.PhoneStorage(TOTAL_SIZE, AVAILABLE_SIZE))
            add(Storage.OTGStorage(listOf(), Pair(TOTAL_SIZE, AVAILABLE_SIZE), STATE_MOUNTED))
        }
        val displayMetrics = mockk<DisplayMetrics>()
        displayMetrics.density = DEFAULT_DENSITY
        val resources = mockk<Resources>()
        every { resources.displayMetrics } returns displayMetrics
        mockkStatic(Resources::class)
        every { Resources.getSystem() } returns resources
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        every { storageLayoutCompatHelper["modifyPhoneStorageConstraintSet"](allAny<Boolean>()) } returns Unit
        storageLayoutCompatHelper.reLayoutStorageLayout(storageItems)
        verify(exactly = 1) { storageLayoutCompatHelper["modifyPhoneStorageConstraintSet"](false) }
    }

    @Test
    fun `test re-layout storage layout when mount cloud disk card`() {
        val storageItems = mutableListOf<Storage>().apply {
            add(Storage.PhoneStorage(TOTAL_SIZE, AVAILABLE_SIZE))
            add(Storage.CloudDiskStorage(true))
        }
        val displayMetrics = mockk<DisplayMetrics>()
        displayMetrics.density = DEFAULT_DENSITY
        val resources = mockk<Resources>()
        every { resources.displayMetrics } returns displayMetrics
        mockkStatic(Resources::class)
        every { Resources.getSystem() } returns resources
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        every { storageLayoutCompatHelper["modifyPhoneStorageConstraintSet"](allAny<Boolean>()) } returns Unit
        storageLayoutCompatHelper.reLayoutStorageLayout(storageItems)
        verify(exactly = 1) { storageLayoutCompatHelper["modifyPhoneStorageConstraintSet"](false) }
    }

    @Test
    fun `test calculate max height item`() {
        val phoneStorage = 0
        val otgStorage = 1
        val sdCardStorage = 2
        val cloudDiskStorage = 1
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        val phoneStorageLayout = mockk<ConstraintLayout>()
        val phoneStorageLayoutParams = mockk<ViewGroup.LayoutParams>()
        every { phoneStorageLayout.layoutParams } returns phoneStorageLayoutParams
        every { phoneStorageLayout.layoutParams = any() } just runs
        storageLayoutCompatHelper.initPhoneStorageLayout(phoneStorageLayout)
        val sdCardStorageLayout = mockk<ConstraintLayout>()
        val cloudDiskStorageLayoutParams = mockk<ViewGroup.LayoutParams>()
        every { sdCardStorageLayout.layoutParams } returns cloudDiskStorageLayoutParams
        every { sdCardStorageLayout.layoutParams = any() } just runs
        storageLayoutCompatHelper.initSdCardLayout(sdCardStorageLayout)
        storageLayoutCompatHelper.calculatorMaxHeightItem(1, 1, 1, 1)
        verify { storageLayoutCompatHelper["updateStorageLayoutHeight"](1) }
        storageLayoutCompatHelper.calculatorMaxHeightItem(
            phoneStorage,
            otgStorage,
            sdCardStorage,
            cloudDiskStorage
        )
        verify { storageLayoutCompatHelper["updateStorageLayoutHeight"](3) }
        verify { storageLayoutCompatHelper["obtainLayoutIDFromItem"](1) }
        verify { storageLayoutCompatHelper["obtainLayoutIDFromItem"](3) }
        Assert.assertEquals(0, phoneStorageLayout.layoutParams.height)
        Assert.assertEquals(0, sdCardStorageLayout.layoutParams.height)
    }

    @Test
    fun `test obtain layout from item`() {
        val storageLayoutCompatHelper = mockk<StorageLayoutCompatHelper>()
        every { storageLayoutCompatHelper.obtainLayoutIDFromItem(any()) }.answers { callOriginal() }
        val phoneStorage = storageLayoutCompatHelper.obtainLayoutIDFromItem(1)
        val otgStorage = storageLayoutCompatHelper.obtainLayoutIDFromItem(2)
        val sdCardStorage = storageLayoutCompatHelper.obtainLayoutIDFromItem(3)
        val cloudDiskStorage = storageLayoutCompatHelper.obtainLayoutIDFromItem(4)
        val dfmStorage = storageLayoutCompatHelper.obtainLayoutIDFromItem(5)
        val elseStorage = storageLayoutCompatHelper.obtainLayoutIDFromItem(6)
        Assert.assertEquals(phoneStorage, com.oplus.filemanager.main.R.id.phone_storage)
        Assert.assertEquals(otgStorage, com.oplus.filemanager.main.R.id.otg_storage)
        Assert.assertEquals(sdCardStorage, com.oplus.filemanager.main.R.id.sd_card_storage)
        Assert.assertEquals(dfmStorage, com.oplus.filemanager.main.R.id.dfm_storage)
        Assert.assertEquals(cloudDiskStorage, com.oplus.filemanager.main.R.id.cloud_disk_storage)
        Assert.assertEquals(elseStorage, com.oplus.filemanager.main.R.id.phone_storage)
    }

    @Test
    fun testObtainTransition() {
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        var isLast = true
        var isScroll = false
        val slideTransition = InternalPlatformDsl.dynamicCall(
            storageLayoutCompatHelper,
            "obtainTransition",
            arrayOf(isLast, isScroll, 0, true),
            mockk()
        )
        verify(exactly = 0) { storageLayoutCompatHelper["addListener"](slideTransition, 0) }
        Assert.assertTrue(slideTransition is SlideTransition)
        isLast = false
        val explodeTransition = InternalPlatformDsl.dynamicCall(
            storageLayoutCompatHelper,
            "obtainTransition",
            arrayOf(isLast, isScroll, 0, true),
            mockk()
        )
        Assert.assertTrue(explodeTransition is ExplodeTransition)
        isScroll = true
        val scrollExplodeTransition = InternalPlatformDsl.dynamicCall(
            storageLayoutCompatHelper,
            "obtainTransition",
            arrayOf(isLast, isScroll, 0, true),
            mockk()
        )
        verify(exactly = 1) { storageLayoutCompatHelper["addListener"](scrollExplodeTransition, 0) }
        Assert.assertTrue(scrollExplodeTransition is ExplodeTransition)
    }

    @Test
    fun `should not execute isSmallCard when refreshTextViewDisplayLine if storageLayout is null`() {
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        every { storageLayoutCompatHelper["isSmallCard"]() } returns Unit
        storageLayoutCompatHelper.refreshTextViewDisplayLine(mockk(), mockk())
        verify(inverse = true) { storageLayoutCompatHelper["isSmallCard"]() }
    }

    @Test
    fun `should execute isSmallCard when refreshTextViewDisplayLine if storageLayout is not small card`() {
        val titleTv = mockk<TextView> {
            every { lineCount } returns 1
            justRun { maxLines = 2 }
            justRun { ellipsize = TextUtils.TruncateAt.END }
        }
        val descTv = mockk<TextView> {
            justRun { visibility = View.VISIBLE }
            justRun { maxLines = 2 }
            justRun { ellipsize = TextUtils.TruncateAt.END }
        }
        val storageLayoutCompatHelper = createStorageLayoutHelperForRefreshTextView()
        storageLayoutCompatHelper.refreshTextViewDisplayLine(titleTv, descTv)
        verify { titleTv.maxLines = 2 }
        verify { titleTv.ellipsize = TextUtils.TruncateAt.END }
        verify { descTv.maxLines = 2 }
        verify { descTv.ellipsize = TextUtils.TruncateAt.END }
    }

    @Test
    fun `should line 1 isSmallCard when refreshTextViewDisplayLine if storageLayout is small`() {
        val titleTv = mockk<TextView> {
            justRun { maxLines = 1 }
            justRun { ellipsize = TextUtils.TruncateAt.END }
        }
        val descTv = mockk<TextView> {
            justRun { maxLines = 1 }
            justRun { ellipsize = TextUtils.TruncateAt.END }
        }
        val storageLayoutCompatHelper = createStorageLayoutHelperForRefreshTextView()
        storageLayoutCompatHelper.displayCloud = true
        storageLayoutCompatHelper.displayPrivate = true
        every { storageLayoutCompatHelper["otgAndSdCardNotShow"]() } returns true
        storageLayoutCompatHelper.refreshTextViewDisplayLine(titleTv, descTv)
        verify { titleTv.maxLines = 1 }
        verify { titleTv.ellipsize = TextUtils.TruncateAt.END }
        verify { descTv.maxLines = 1 }
        verify { descTv.ellipsize = TextUtils.TruncateAt.END }
    }

    @Test
    fun `should return false when refreshTextViewDisplayLine if otgAndSdCardNotShow`() {
        val titleTv = mockk<TextView> {
            justRun { maxLines = 2 }
            justRun { ellipsize = TextUtils.TruncateAt.END }
        }
        val descTv = mockk<TextView> {
            justRun { maxLines = 2 }
            justRun { ellipsize = TextUtils.TruncateAt.END }
        }
        val storageLayoutCompatHelper = createStorageLayoutHelperForRefreshTextView()
        storageLayoutCompatHelper.displayCloud = true
        storageLayoutCompatHelper.displayPrivate = true
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.otg_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.sd_card_storage) } returns View.GONE

        storageLayoutCompatHelper.refreshTextViewDisplayLine(titleTv, descTv)
        verify { titleTv.maxLines = 2 }
        verify { descTv.maxLines = 2 }
    }

    @Test
    fun `should return false when refreshTextViewDisplayLine if otgAndSdCardNotShow and sd card is visible`() {
        val titleTv = mockk<TextView> {
            justRun { maxLines = 2 }
            justRun { ellipsize = TextUtils.TruncateAt.END }
        }
        val descTv = mockk<TextView> {
            justRun { maxLines = 2 }
            justRun { ellipsize = TextUtils.TruncateAt.END }
        }
        val storageLayoutCompatHelper = createStorageLayoutHelperForRefreshTextView()
        storageLayoutCompatHelper.displayCloud = true
        storageLayoutCompatHelper.displayPrivate = true
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.otg_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.sd_card_storage) } returns View.VISIBLE

        storageLayoutCompatHelper.refreshTextViewDisplayLine(titleTv, descTv)
        verify { titleTv.maxLines = 2 }
        verify { descTv.maxLines = 2 }
    }

    private fun createStorageLayoutHelperForRefreshTextView(): StorageLayoutCompatHelper {
        val storageLayout = mockk<ConstraintLayout>()
        justRun { anyConstructed<ConstraintSet>().clone(storageLayout) }
        justRun { anyConstructed<ConstraintSet>().applyTo(storageLayout) }
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        storageLayoutCompatHelper.initStorageLayout(storageLayout)
        return storageLayoutCompatHelper
    }

    @Test
    fun `should not execute resetPrivateSafe when reLayoutPrivateSafeLayout if storageLayout is null`() {
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        val constraintSet = mockk<ConstraintSet>()
        every { storageLayoutCompatHelper["resetPrivateSafe"](true, constraintSet) } returns Unit
        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(true, true)
        verify(inverse = true) {
            storageLayoutCompatHelper["resetPrivateSafe"](
                true,
                constraintSet
            )
        }
    }

    @Test
    fun `should execute resetPrivateSafe when refreshTextViewDisplayLine`() {
        val (storageLayoutCompatHelper, storageLayout) = createStorageLayoutHelperForReLayoutPrivateSafe()
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.cloud_disk_storage) } returns View.VISIBLE
        justRun {
            anyConstructed<ConstraintSet>().setVisibility(
                com.oplus.filemanager.main.R.id.private_safe,
                View.VISIBLE
            )
        }
        val transition = mockk<TransitionSet>()
        every {
            storageLayoutCompatHelper["obtainTransition"](
                true,
                true,
                0,
                true
            )
        } returns transition
        justRun { TransitionManager.beginDelayedTransition(storageLayout, transition) }
        every {
            storageLayoutCompatHelper["resetPrivateSafe"](
                true,
                any<ConstraintSet>()
            )
        } returns Unit

        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(true, true)

        verify { storageLayoutCompatHelper["resetPrivateSafe"](true, any<ConstraintSet>()) }
        verify {
            anyConstructed<ConstraintSet>().setVisibility(
                com.oplus.filemanager.main.R.id.private_safe,
                View.VISIBLE
            )
        }
        verify { anyConstructed<ConstraintSet>().clone(storageLayout) }
        verify { anyConstructed<ConstraintSet>().applyTo(storageLayout) }
        verify { TransitionManager.beginDelayedTransition(storageLayout, transition) }
    }

    @Test
    fun `should setVisibility Gone resetPrivateSafe when refreshTextViewDisplayLine if is not show`() {
        //given
        val (storageLayoutCompatHelper, storageLayout) = createStorageLayoutHelperForReLayoutPrivateSafe()
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.cloud_disk_storage) } returns View.VISIBLE
        justRun {
            anyConstructed<ConstraintSet>().setVisibility(
                com.oplus.filemanager.main.R.id.private_safe,
                View.GONE
            )
        }
        val transition = mockk<TransitionSet>()
        every {
            storageLayoutCompatHelper["obtainTransition"](
                true,
                true,
                0,
                false
            )
        } returns transition
        justRun { TransitionManager.beginDelayedTransition(storageLayout, transition) }
        every {
            storageLayoutCompatHelper["resetPrivateSafe"](
                true,
                any<ConstraintSet>()
            )
        } returns Unit
        //when
        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(false, true)
        //then
        verify {
            anyConstructed<ConstraintSet>().setVisibility(
                com.oplus.filemanager.main.R.id.private_safe,
                View.GONE
            )
        }
    }

    @Test
    fun `should execute false resetPrivateSafe when refreshTextViewDisplayLine if getVisibility is GONE`() {
        //given
        val (storageLayoutCompatHelper, storageLayout) = createStorageLayoutHelperForReLayoutPrivateSafe()
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.cloud_disk_storage) } returns View.GONE
        justRun {
            anyConstructed<ConstraintSet>().setVisibility(
                com.oplus.filemanager.main.R.id.private_safe,
                View.GONE
            )
        }
        val transition = mockk<TransitionSet>()
        every {
            storageLayoutCompatHelper["obtainTransition"](
                true,
                true,
                0,
                false
            )
        } returns transition
        justRun { TransitionManager.beginDelayedTransition(storageLayout, transition) }
        every {
            storageLayoutCompatHelper["resetPrivateSafe"](
                false,
                any<ConstraintSet>()
            )
        } returns Unit
        //when
        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(false, true)
        //then
        verify { storageLayoutCompatHelper["resetPrivateSafe"](false, any<ConstraintSet>()) }
    }

    private fun createStorageLayoutHelperForReLayoutPrivateSafe(): Pair<StorageLayoutCompatHelper, ConstraintLayout> {
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        val storageLayout = mockk<ConstraintLayout>()
        val privacyLayout = mockk<ConstraintLayout> {
            every { right } returns 0
        }
        storageLayoutCompatHelper.initStorageLayout(storageLayout)
        storageLayoutCompatHelper.initPrivateSafeLayout(privacyLayout)
        justRun { anyConstructed<ConstraintSet>().clone(storageLayout) }
        justRun { anyConstructed<ConstraintSet>().applyTo(storageLayout) }
        return Pair(storageLayoutCompatHelper, storageLayout)
    }

    @Test
    fun `should execute 1 when resetPrivateSafe if isCloudDiskShow and is otgStorageShow`() {
        //given
        val storageLayoutCompatHelper = createStorageLayoutHelperForResetPrivateSafe()
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.cloud_disk_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.otg_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.sd_card_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.private_safe) } returns View.VISIBLE
        //when
        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(false, true)
        //then
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                BOTTOM,
                ConstraintSet.PARENT_ID,
                BOTTOM,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                TOP,
                ConstraintSet.PARENT_ID,
                TOP,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                END,
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                END,
                any()
            )
        }
    }

    @Test
    fun `should execute 1 when resetPrivateSafe if isCloudDiskShow and is sdCardStorageShow`() {
        //given
        val storageLayoutCompatHelper = createStorageLayoutHelperForResetPrivateSafe()
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.cloud_disk_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.otg_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.sd_card_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.private_safe) } returns View.VISIBLE
        //when
        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(false, true)
        //then
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                BOTTOM,
                ConstraintSet.PARENT_ID,
                BOTTOM,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                TOP,
                ConstraintSet.PARENT_ID,
                TOP,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                END,
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                END,
                any()
            )
        }
    }

    @Test
    fun `should execute 1 when resetPrivateSafe if isCloudDiskShow and is otgStorageShow and sdCardStorageShow`() {
        //given
        val storageLayoutCompatHelper = createStorageLayoutHelperForResetPrivateSafe()
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.cloud_disk_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.otg_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.sd_card_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.private_safe) } returns View.VISIBLE
        //when
        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(false, true)
        //then
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                BOTTOM,
                ConstraintSet.PARENT_ID,
                BOTTOM,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                TOP,
                ConstraintSet.PARENT_ID,
                TOP,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                END,
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                END,
                any()
            )
        }
    }

    @Test
    fun `should execute 2 when resetPrivateSafe if isCloudDiskShow and is otgStorageShow and sdCardStorageShow and privateSafeShow is not`() {
        //given
        val storageLayoutCompatHelper = createStorageLayoutHelperForResetPrivateSafe()
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.cloud_disk_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.otg_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.sd_card_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.dfm_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.private_safe) } returns View.GONE
        //when
        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(false, true)
        //then
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                BOTTOM,
                ConstraintSet.PARENT_ID,
                BOTTOM,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                TOP,
                ConstraintSet.PARENT_ID,
                TOP,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                END,
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                END,
                ConstraintSet.PARENT_ID,
                END,
                any()
            )
        }
    }

    @Test
    fun `should execute 3 when resetPrivateSafe if isCloudDiskShow and is not otgStorageShow and not sdCardStorageShow`() {
        //given
        val storageLayoutCompatHelper = createStorageLayoutHelperForResetPrivateSafe()
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.cloud_disk_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.otg_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.sd_card_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.dfm_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.private_safe) } returns View.VISIBLE
        //when
        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(false, true)
        //then
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                BOTTOM,
                com.oplus.filemanager.main.R.id.private_safe,
                TOP,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                TOP,
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                BOTTOM,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                END,
                ConstraintSet.PARENT_ID,
                END,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                START,
                any()
            )
        }
    }

    @Test
    fun `should execute 3 when resetPrivateSafe if isCloudDiskShow and is not otgStorageShow and not sdCardStorageShow and not privateSafeShow`() {
        //given
        val storageLayoutCompatHelper = createStorageLayoutHelperForResetPrivateSafe()
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.cloud_disk_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.otg_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.sd_card_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.private_safe) } returns View.GONE
        //when
        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(false, true)
        //then
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                BOTTOM,
                ConstraintSet.PARENT_ID,
                BOTTOM,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.cloud_disk_storage,
                END,
                ConstraintSet.PARENT_ID,
                END,
                any()
            )
        }
    }

    @Test
    fun `should execute 4 when resetPrivateSafe if not isCloudDiskShow and is otgStorageShow and not sdCardStorageShow`() {
        //given
        val storageLayoutCompatHelper = createStorageLayoutHelperForResetPrivateSafe()
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.cloud_disk_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.otg_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.sd_card_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.dfm_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.private_safe) } returns View.GONE
        //when
        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(false, true)
        //then
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                com.oplus.filemanager.main.R.id.otg_storage,
                END,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.otg_storage,
                END,
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                TOP,
                ConstraintSet.PARENT_ID,
                TOP,
                any()
            )
        }
    }

    @Test
    fun `should execute 4 when resetPrivateSafe if not isCloudDiskShow and not otgStorageShow`() {
        //given
        val storageLayoutCompatHelper = createStorageLayoutHelperForResetPrivateSafe()
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.cloud_disk_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.otg_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.sd_card_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.private_safe) } returns View.GONE
        //when
        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(false, true)
        //then
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.otg_storage,
                END,
                com.oplus.filemanager.main.R.id.sd_card_storage,
                START,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.sd_card_storage,
                END,
                com.oplus.filemanager.main.R.id.dfm_storage,
                START,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                com.oplus.filemanager.main.R.id.dfm_storage,
                END,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                TOP,
                ConstraintSet.PARENT_ID,
                TOP,
                any()
            )
        }
    }

    @Test
    fun `should execute 4 when resetPrivateSafe if not isCloudDiskShow and sdCardStorageShow`() {
        //given
        val storageLayoutCompatHelper = createStorageLayoutHelperForResetPrivateSafe()
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.cloud_disk_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.otg_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.sd_card_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.dfm_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.private_safe) } returns View.GONE
        //when
        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(false, true)
        //then
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.otg_storage,
                END,
                com.oplus.filemanager.main.R.id.sd_card_storage,
                START,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.sd_card_storage,
                END,
                com.oplus.filemanager.main.R.id.dfm_storage,
                START,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.dfm_storage,
                END,
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                com.oplus.filemanager.main.R.id.dfm_storage,
                END,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                TOP,
                ConstraintSet.PARENT_ID,
                TOP,
                any()
            )
        }
    }

    @Test
    fun `should execute 4 when resetPrivateSafe if not isCloudDiskShow and not otgStorageShow and sdCardStorageShow`() {
        //given
        val storageLayoutCompatHelper = createStorageLayoutHelperForResetPrivateSafe()
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.cloud_disk_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.otg_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.dfm_storage) } returns View.GONE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.sd_card_storage) } returns View.VISIBLE
        every { anyConstructed<ConstraintSet>().getVisibility(com.oplus.filemanager.main.R.id.private_safe) } returns View.GONE
        //when
        storageLayoutCompatHelper.reLayoutPrivateSafeLayout(false, true)
        //then
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.otg_storage,
                END,
                com.oplus.filemanager.main.R.id.sd_card_storage,
                START,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.sd_card_storage,
                END,
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                START,
                com.oplus.filemanager.main.R.id.sd_card_storage,
                END,
                any()
            )
        }
        verify {
            anyConstructed<ConstraintSet>().connect(
                com.oplus.filemanager.main.R.id.private_safe,
                TOP,
                ConstraintSet.PARENT_ID,
                TOP,
                any()
            )
        }
    }

    private fun createStorageLayoutHelperForResetPrivateSafe(): StorageLayoutCompatHelper {
        every { Resources.getSystem() } returns mockk {
            every { displayMetrics } returns mockk()
        }
        val storageLayoutCompatHelper = spyk<StorageLayoutCompatHelper>(recordPrivateCalls = true)
        val storageLayout = mockk<ConstraintLayout>()
        val privacyLayout = mockk<ConstraintLayout> {
            every { right } returns 0
        }
        storageLayoutCompatHelper.initStorageLayout(storageLayout)
        storageLayoutCompatHelper.initPrivateSafeLayout(privacyLayout)
        justRun { anyConstructed<ConstraintSet>().clone(storageLayout) }
        justRun { anyConstructed<ConstraintSet>().applyTo(storageLayout) }
        justRun {
            anyConstructed<ConstraintSet>().setVisibility(
                com.oplus.filemanager.main.R.id.private_safe,
                View.GONE
            )
        }
        justRun { anyConstructed<ConstraintSet>().connect(any(), any(), any(), any(), any()) }
        val transition = mockk<TransitionSet>()
        every {
            storageLayoutCompatHelper["obtainTransition"](
                true,
                true,
                0,
                false
            )
        } returns transition
        justRun { TransitionManager.beginDelayedTransition(storageLayout, transition) }
        return storageLayoutCompatHelper
    }


    @After
    fun tearDDown() {
        unmockkConstructor(ConstraintSet::class)
        unmockkStatic(TransitionManager::class)
        unmockkStatic(Resources::class)
    }

    companion object {
        private const val AVAILABLE_SIZE = 1000L
        private const val TOTAL_SIZE = 1024L
        private const val DEFAULT_DENSITY = 2F
    }
}