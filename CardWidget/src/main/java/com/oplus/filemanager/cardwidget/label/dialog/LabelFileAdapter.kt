/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LabelFileAdapter
 ** Description : 标签中的文件Adapter
 ** Version     : 1.0
 ** Date        : 2022/11/25
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue      2022/11/25      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.dialog

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.MiddleMultilineTextView
import com.oplus.filemanager.cardwidget.R
import com.oplus.filemanager.cardwidget.ext.getIconResFromType
import com.oplus.filemanager.cardwidget.ext.writeFileProvider
import com.oplus.filemanager.cardwidget.util.ANDROID_LAUNCHER_PKG
import com.oplus.filemanager.cardwidget.util.ASSISTANT_SCREEN_PKG
import com.oplus.filemanager.cardwidget.util.CardWidgetThumbnailGenerator
import com.oplus.filemanager.cardwidget.util.OPPO_LAUNCHER_PKG
import com.oplus.filemanager.cardwidget.util.isSupportThumbnailForCardWidget
import com.oplus.filemanager.cardwidget.util.ruleForThumbnailFileName
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class LabelFileAdapter(
    var context: Context,
    var list: List<BaseFileBean>
) : RecyclerView.Adapter<LabelFileAdapter.LabelFileVH>() {

    companion object {
        private const val TAG = "LabelFileAdapter"
        private const val UPDATE_MULTI_TEXT_DELAY = 10L
    }

    @VisibleForTesting
    var imgWidth: Int = context.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_33dp)

    @VisibleForTesting
    var iconWidth: Int = context.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_33dp)

    @VisibleForTesting
    val layoutInflater: LayoutInflater by lazy {
        LayoutInflater.from(context)
    }

    fun setFileList(list: List<BaseFileBean>) {
        this.list = list
        notifyDataSetChanged()
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LabelFileVH {
        val view = layoutInflater.inflate(R.layout.card_preview_item_layout, parent, false)
        return LabelFileVH(view, context)
    }

    override fun onBindViewHolder(holder: LabelFileVH, position: Int) {
        val file = list.get(position)
        holder.bindData(file)
        holder.itemView?.let {
            if (DragUtils.getSelectedFiles()?.contains(file) == true && DragUtils.isDragging) {
                it.alpha = DragUtils.SELECTED_ITEMVIEW_ALPHA
            } else {
                it.alpha = 1f
            }
        }
    }

    override fun getItemCount(): Int {
        return list.size
    }


    class LabelFileVH(itemView: View, var context: Context) : RecyclerView.ViewHolder(itemView) {

        @VisibleForTesting
        val img: FileThumbView = itemView.findViewById(R.id.file_img)

        @VisibleForTesting
        val durationTv: TextView = itemView.findViewById(R.id.file_duration_tv)

        @VisibleForTesting
        val nameTv: MiddleMultilineTextView = itemView.findViewById(R.id.file_name)

        init {
            ViewHelper.setClassificationTextSizeG2(nameTv.context, nameTv)
        }

        fun bindData(file: BaseFileBean) {
            val adapter: LabelFileAdapter = bindingAdapter as? LabelFileAdapter ?: return
            // 缩略图
            adapter.showIcon(img, file)
            // 文件名
            (context as? FragmentActivity)?.lifecycleScope?.launch(Dispatchers.IO) {
                delay(UPDATE_MULTI_TEXT_DELAY)
                withContext(Dispatchers.Main) {
                    file.mDisplayName?.apply {
                        nameTv.setMultiText(this)
                    }
                }
            }
            // 视频时长
            adapter.showDurationIfNeed(durationTv, file)
        }
    }

    @VisibleForTesting
    fun showIcon(img: FileThumbView, fileBean: BaseFileBean) {
        val modified = fileBean.mDateModified
        val size = fileBean.mSize
        val path = fileBean.mData ?: ""
        val type = if (fileBean.mIsDirectory) {
            MimeTypeHelper.DIRECTORY_TYPE
        } else {
            val pathType = MimeTypeHelper.getTypeFromPath(path)
            if (pathType == MimeTypeHelper.COMPRESSED_TYPE) {
                //压缩类型，再根据后缀名区分具体类型
                MimeTypeHelper.getCompressedTypeByPath(path)
            } else {
                pathType
            }
        }
        val iconRes = getIconResFromType(type)
        if (isSupportThumbnailForCardWidget(fileBean.mLocalType)) {
            val file = File(ruleForThumbnailFileName(path, modified, size))
            if (file.exists().not()) {
                CardWidgetThumbnailGenerator.generate(type, path, modified, size)
            }
            //支持加载缩略图的，生成缩略图
            if (file.exists() && file.length() > 0) {
                Log.d(TAG, "generate thumbnail success $path")
                val uri = writeFileProvider(
                    context,
                    file,
                    arrayOf(ASSISTANT_SCREEN_PKG, ANDROID_LAUNCHER_PKG, OPPO_LAUNCHER_PKG)
                )
                if (MimeTypeHelper.isDocType(fileBean.mLocalType)) {
                    //文档，并且支持缩略图 64X64
                    setViewSize(img, imgWidth)
                } else {
                    //其他加载的图片50X50
                    setViewSize(img, iconWidth)
                }
                img.setImageURI(uri)
            } else {
                Log.e(TAG, "generate thumbnail failed $path")
                setViewSize(img, iconWidth)
                img.setImageResource(iconRes)
            }
        } else {
            setViewSize(img, iconWidth)
            img.setImageResource(iconRes)
        }
    }

    @VisibleForTesting
    fun showDurationIfNeed(duration: TextView, file: BaseFileBean) {
        duration.visibility = View.VISIBLE
        duration.text = Utils.getDateFormat(MyApplication.sAppContext, file.mDateModified)
    }

    @VisibleForTesting
    fun setViewSize(img: View, size: Int) {
        img.updateLayoutParams<ViewGroup.LayoutParams> {
            this.width = size
            this.height = size
        }
    }
}