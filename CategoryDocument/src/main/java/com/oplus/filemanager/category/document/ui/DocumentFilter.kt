/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:/
 * * Description:
 * * Version:1.0
 * * Date :2020/6/11
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/6/11,        v1.0,           Create
 ****************************************************************/
package com.oplus.filemanager.category.document.ui

import android.text.TextUtils
import android.util.SparseArray
import androidx.annotation.WorkerThread
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.BlacklistParser
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NewFunctionSwitch
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import java.io.File
import java.util.*
import kotlin.collections.ArrayList

class DocumentFilter {
    companion object {
        private const val TAG = "DocumentFilter"
    }

    private var mSort: Int = SortHelper.FILE_NAME_ORDER

    /**
     * value in :[CategoryHelper.CATEGORY_DOC]
     */
    private var mCategoryType: Int = CategoryHelper.CATEGORY_DOC

    private var mInternalPath: String? = VolumeEnvironment.getInternalSdPath(appContext)
    private var mIsSizeFilter: Boolean = false
    private var mFilterSize = 0
    private var mIgnoredPathByUriString: SparseArray<String>? = null
    private var mSupperAppPaths: ArrayList<String?> = ArrayList()
    private var mTypeFilterState: Boolean = false
    private var mFilterCount = 0


    @WorkerThread
    fun updateFilterOptions(sortMode: Int): DocumentFilter {
        //This method should be called in the work thread
        mIgnoredPathByUriString = CategoryHelper.setIgnoredPath(appContext, mCategoryType)
        mFilterSize = getSizeFilterState(mCategoryType)
        mTypeFilterState = BlacklistParser.isFilterTypes(mCategoryType, null)

        val superApp = Injector.injectFactory<ISuperApp>()
        mSupperAppPaths = superApp?.updateSupperAppPaths(appContext) ?: ArrayList()
        mSort = sortMode
        mFilterCount = 0
        return this
    }

    fun checkIsFilterItem(file: BaseFileBean?): Boolean {
        file?.let {
            return checkIsFilterItem(it.mData, it.mSize)
        }
        return false
    }

    private fun getSizeFilterState(category: Int): Int {
        val filterSize = BlacklistParser.getFilterSizeByType(category)
        mIsSizeFilter = filterSize > 0
        return filterSize
    }

    private fun checkIsFilterItem(absolutePath: String?, size: Long): Boolean {
        //跟据黑名单配置的后缀名进行拦截
        //Intercept according to the suffix name configured in the blacklist
        // <item>default_doc_blacklist:0:none</item>
        // <item>default_doc_blacklist:【Filter file size】:【Filter type by file extension, eg. .html;.txt】</item>
        if (mTypeFilterState && BlacklistParser.isFilterTypes(mCategoryType, absolutePath)) {
            mFilterCount++
            return true
        }
        //跟据黑名单配置的文件大小拦截进行拦截
        //Block according to the file size configured according to the blacklist
        // <item>default_doc_blacklist:0:none</item>
        // <item>default_doc_blacklist:【Filter file size】:【Filter type by file extension, eg. .html;.txt】</item>
        if (mIsSizeFilter && (size < mFilterSize)) {
            mFilterCount++
            return true
        }
        //排除超级应用目录配置的目录下的文件
        //CategoryHelper.isIgnoredFiles(MyApplication.sAppContext, mCategoryType, absolutePath, mInternalPath, mIgnoredPathByUriString)
        if (isSupperAppDirFile(absolutePath)) {
            return false
        } else {
            //此外，再跟据app_garbase_database.db的ignored_category_path 的相对路径进行拦截
            val isIgnored = isIgnoredPath(absolutePath, mInternalPath, mIgnoredPathByUriString)
            if (isIgnored) {
                if (NewFunctionSwitch.FILTER_SYSTEM_APP_LOG) {
                    val type = MimeTypeHelper.getTypeFromPath(absolutePath)
                    val isTxt = type == MimeTypeHelper.TXT_TYPE
                    Log.d(TAG, "isIgnoredPath type:$type")
                    if (isTxt) {
                        mFilterCount++
                        return true
                    }
                } else {
                    mFilterCount++
                    return true
                }
            }
        }
        return false
    }

    private fun isSupperAppDirFile(filePath: String?): Boolean {
        var path: String = filePath ?: return true
        mInternalPath?.let {
            path = path.lowercase(Locale.getDefault())
            var pathString: String?
            for (appRelativePath in mSupperAppPaths) {
                pathString = (mInternalPath + File.separator + appRelativePath)
                pathString = pathString.lowercase(Locale.getDefault())
                if (appRelativePath != null && path.startsWith(pathString)) {
                    Log.v(TAG, "isSupperAppFile path = $path")
                    return true
                }
            }
        }
        return false
    }

    private fun isIgnoredPath(filePath: String?, internalPath: String?, ignoredPaths: SparseArray<String>?): Boolean {
        if (TextUtils.isEmpty(filePath)) {
            Log.w(TAG, "isIgnoredPath path is empty")
            return true
        }
        // check if the path be under the ignored path
        ignoredPaths?.let {
            for (i in 0 until it.size()) {
                val string = internalPath + it[i]
                if (filePath?.lowercase(Locale.getDefault())?.startsWith(string.lowercase(Locale.getDefault())) == true) {
                    Log.v(TAG, "isIgnoredPath path = $filePath")
                    return true
                }
            }
        }
        return false
    }
}