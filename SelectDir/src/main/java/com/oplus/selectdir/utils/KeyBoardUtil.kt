/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.selectdir.utils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/6/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir.utils

import android.graphics.Rect
import androidx.fragment.app.FragmentActivity
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import kotlin.math.abs
import kotlin.math.max

/**
 * 键盘弹起工具类
 * 在FragmentDialog中，键盘在底部，需要改变RootView的高度，让键盘不遮挡输入框
 */
object KeyBoardUtil {

    private const val INDEX_VISIBLE_BOTTOM = 0
    private const val INDEX_VIEW_HEIGHT = 1
    private const val TAG = "KeyBoardUtil"

    private var maxRootHeight = 0

    /**
     * 判断输入法是否弹出隐藏的距离
     */
    private const val CHECK_DISTANCE_INPUTMETHOD = 200

    @JvmStatic
    fun initOldHeightAndBottomValues(): IntArray {
        return IntArray(2).apply {
            this[INDEX_VISIBLE_BOTTOM] = -1
            this[INDEX_VIEW_HEIGHT] = -1
        }
    }

    @JvmStatic
    fun checkIfUpdateViewHeight(activity: FragmentActivity, rect: Rect, rootHeight: Int, oldValues: IntArray): Int? {
        val isTablet = ModelUtils.isTablet()
        if (!activity.isInMultiWindowMode || isTablet) {
            if (oldValues[INDEX_VISIBLE_BOTTOM] == -1) {
                oldValues[INDEX_VISIBLE_BOTTOM] = rect.bottom
            }
            if (oldValues[INDEX_VIEW_HEIGHT] == -1) {
                oldValues[INDEX_VIEW_HEIGHT] = rootHeight
            }
            maxRootHeight = max(rootHeight, maxRootHeight)
            /**
             * 可见的底部与是的底部高度间距超过200，即判断键盘的弹出或隐藏
             */
            if (abs(oldValues[INDEX_VISIBLE_BOTTOM] - rect.bottom) > CHECK_DISTANCE_INPUTMETHOD) {
                val targetHeight = oldValues[INDEX_VIEW_HEIGHT] + rect.bottom - oldValues[INDEX_VISIBLE_BOTTOM]
                oldValues[INDEX_VISIBLE_BOTTOM] = rect.bottom
                oldValues[INDEX_VIEW_HEIGHT] = targetHeight
                /**
                 * 当弹起键盘后，切换暗色模式，会自动隐藏键盘，此时获取的targetHeight,会大于窗口的高度
                 * 判断，在此情况下,需要重置旧的底部高度
                 */
                Log.d(TAG, "targetHeight:$targetHeight maxRootHeight:$maxRootHeight")
                if (targetHeight <= maxRootHeight) {
                    return targetHeight
                } else {
                    oldValues[INDEX_VIEW_HEIGHT] = -1
                }
            }
        }
        return null
    }
}