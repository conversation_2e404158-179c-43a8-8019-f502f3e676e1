/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScriptExecRequest.kt
 * Description:
 *     The request info to execute script.
 *
 * Version: 1.0
 * Date: 2024-05-28
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-05-28   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.request

import com.oplus.filemanager.simulateclick.sdk.OnScriptDetectedCallback

internal data class ScriptExecRequest(
    /**
     * The target package name to execute script
     */
    val packageName: String,

    /**
     * The skill name which need to be executed in script
     */
    val skillName: String,

    /**
     * The replace map of args for executing script.
     */
    val replacedMap: Map<String, String>,

    /**
     * The callback of getting result about whether detected script to execute
     */
    val detectedCallback: OnScriptDetectedCallback?
)