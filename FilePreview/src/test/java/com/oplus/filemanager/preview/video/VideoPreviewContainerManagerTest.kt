package com.oplus.filemanager.preview.video

import android.graphics.drawable.ColorDrawable
import android.view.View
import androidx.appcompat.widget.AppCompatTextView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.utils.PreviewScrollAreaHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite
import com.oplus.filemanager.preview.widget.PreviewOperationsBar
import com.oplus.filemanager.preview.widget.PreviewScrollView
import com.oplus.filemanager.preview.widget.PreviewVideoPlaySuite
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlin.test.assertTrue

/**
 * VideoPreviewContainerManager的单元测试类
 * 用于测试视频预览容器管理器的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class VideoPreviewContainerManagerTest {

    // 定义测试所需的成员变量
    private lateinit var rootLayout: View
    private lateinit var defaultContainer: PreviewFileInfoSuite
    private lateinit var nameContainer: TextViewSnippet
    private lateinit var videoPlayerSuit: PreviewVideoPlaySuite
    private lateinit var videoContainer: View
    private lateinit var loadingLayout: View
    private lateinit var scrollAreaHelper: PreviewScrollAreaHelper
    private lateinit var manager: VideoPreviewContainerManager
    private lateinit var mDateTv: AppCompatTextView
    private lateinit var mSizeTv: AppCompatTextView
    private lateinit var scrollView: PreviewScrollView
    private lateinit var operationsBar: PreviewOperationsBar

    /**
     * 测试前的初始化方法
     * 创建所有需要的mock对象并设置其行为
     */
    @Before
    fun setUp() {
        // 初始化模拟对象
        rootLayout = mockk(relaxed = true)
        defaultContainer = mockk(relaxed = true)
        nameContainer = mockk(relaxed = true)
        videoPlayerSuit = mockk(relaxed = true)
        videoContainer = mockk(relaxed = true)
        loadingLayout = mockk(relaxed = true)
        scrollAreaHelper = mockk(relaxed = true)
        mDateTv = mockk(relaxed = true)
        mSizeTv = mockk(relaxed = true)
        scrollView = mockk(relaxed = true)
        operationsBar = mockk(relaxed = true)

        // 设置findViewById行为 - 模拟布局查找
        every { rootLayout.findViewById<PreviewFileInfoSuite>(R.id.preview_video_def_info) } returns defaultContainer
        every { rootLayout.findViewById<TextViewSnippet>(R.id.preview_remote_title) } returns nameContainer
        every { rootLayout.findViewById<PreviewVideoPlaySuite>(R.id.preview_video_play_suite) } returns videoPlayerSuit
        every { rootLayout.findViewById<View>(R.id.preview_video_container) } returns videoContainer
        every { rootLayout.findViewById<View>(R.id.loading_layout) } returns loadingLayout
        every { rootLayout.findViewById<AppCompatTextView>(R.id.preview_remote_time_info) } returns mDateTv
        every { rootLayout.findViewById<AppCompatTextView>(R.id.preview_remote_size_info) } returns mSizeTv
        every { rootLayout.findViewById<PreviewScrollView>(R.id.preview_audio_scroll_area) } returns scrollView
        every { rootLayout.findViewById<PreviewOperationsBar>(R.id.preview_operations_bar) } returns operationsBar

        // 创建测试对象 - 移除反射设置final字段
        manager = VideoPreviewContainerManager(rootLayout)

        // 模拟日志静态方法
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } returns Unit
    }

    /**
     * 测试后的清理方法
     * 解除所有mock对象的绑定
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试showAsFileContainer方法在default为true时的行为
     * 验证UI更新是否正确
     */
    @Test
    fun `showAsFileContainer with default true should update UI correctly`() {
        // When - 调用测试方法
        manager.showAsFileContainer(true)

        // Then - 验证各UI组件的可见性和状态
        verify(exactly = 1) { loadingLayout.visibility = View.GONE }
        verify(exactly = 1) { defaultContainer.visibility = View.VISIBLE }
        verify(exactly = 1) { defaultContainer.setFilePathVisible(false) }
        verify(exactly = 1) { nameContainer.visibility = View.INVISIBLE }
        verify(exactly = 1) { videoContainer.visibility = View.INVISIBLE }
        verify(exactly = 1) { videoContainer.foreground = null }
    }

    /**
     * 测试showAsFileContainer方法在default为false时的行为
     * 验证UI更新是否正确
     */
    @Test
    fun `showAsFileContainer with default false should update UI correctly`() {
        // Given - 设置模拟资源返回值
        every { defaultContainer.resources.getDimensionPixelOffset(any()) } returns 100

        // When - 调用测试方法
        manager.showAsFileContainer(false)

        // Then - 验证各UI组件的可见性和状态
        verify(exactly = 1) { loadingLayout.visibility = View.GONE }
        verify(exactly = 1) { defaultContainer.visibility = View.GONE }
        verify(exactly = 1) { nameContainer.visibility = View.VISIBLE }
        verify(exactly = 1) { videoContainer.visibility = View.VISIBLE }
        verify(exactly = 1) { videoContainer.foreground = null }
    }

    /**
     * 测试onUpdateUIWhenConfigChange方法
     * 验证配置变更时是否正确触发相关操作
     */
    @Test
    fun `onUpdateUIWhenConfigChange should trigger checkShowDivider`() {
        // When - 调用测试方法
        manager.onUpdateUIWhenConfigChange(mockk())

        // Then - 验证逻辑已移除
    }

    /**
     * 测试getVideoCoverForeground私有方法
     * 验证是否能正确获取视频封面前景色
     */
    @Test
    fun `getVideoCoverForeground should return valid color`() {
        // Given - 设置模拟上下文和颜色返回值
        every { videoContainer.context } returns RuntimeEnvironment.application
        // 修复：模拟COUIContextUtil静态方法
        mockkStatic(COUIContextUtil::class)
        every { COUIContextUtil.getAttrColor(any(), any()) } returns 0xFF0000FF.toInt()  // 返回非0颜色值

        // 通过反射调用私有方法
        val method = VideoPreviewContainerManager::class.java.getDeclaredMethod(
            "getVideoCoverForeground", 
            View::class.java
        ).apply { isAccessible = true }
        
        // When - 调用私有方法
        val color = method.invoke(manager, videoContainer) as Int
        
        // Then - 验证返回的颜色值是否有效
        assertTrue(color != 0, "Should return valid color resource")
    }
}