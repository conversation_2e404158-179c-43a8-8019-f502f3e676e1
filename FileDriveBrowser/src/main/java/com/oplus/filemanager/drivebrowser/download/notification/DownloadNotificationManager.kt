/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DownloadNotificationManager
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/9 15:15
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/9       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.download.notification

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.stringResource
import com.oplus.filemanager.drivebrowser.R
import com.oplus.filemanager.drivebrowser.download.DocumentDownloadDispatcher
import com.oplus.filemanager.drivebrowser.download.DownloadTaskInfo
import org.apache.commons.io.FilenameUtils

class DownloadNotificationManager(
    private val context: Context
) {

    fun showDownloadProgressNotification(fileName: String, progress: Int, index: Int, totalCount: Int) {
        val notification = createProgressNotification(fileName, progress, index, totalCount)
        notifyNotification(NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID, notification)
    }

    fun cancelDownloadProgressNotification(context: Context) {
        cancelNotification(NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID)
    }

    fun showDownloadSuccessNotification(filename: String, filePath: String) {
        if (filename.isEmpty() || filePath.isEmpty()) {
            return
        }
        Log.d(TAG, "showDownloadSuccessNotification -> filename = $filename")
        val downloadSuccessNotificationInfo = if (downloadSuccessNotificationMap.containsKey(filePath)) {
            downloadSuccessNotificationMap[filePath]?.let {
                Log.d(TAG, "showDownloadSuccessNotification -> curr info = $it")
                updateDownloadSuccessNotification(it, filename)
            } ?: createDownloadNotificationInfo(filePath, filename)
        } else {
            createDownloadNotificationInfo(filePath, filename)
        }
        Log.d(TAG, "showDownloadSuccessNotification -> info = $downloadSuccessNotificationInfo")
        downloadSuccessNotificationMap[filePath] = downloadSuccessNotificationInfo
        createNotificationChannel(NOTIFICATION_DOWNLOAD_SUCCESS_CHANNEL_ID)
        val displayPath = Utils.formatPathWithRTL(Utils.getVirtualPathString(context, filePath))
        val size = downloadSuccessNotificationInfo.successCount
        val successTitle = if (size == 1) {
            context.getString(com.filemanager.common.R.string.download_success, filename)
        } else {
            Utils.formatMessage(
                context.resources.getQuantityString(com.filemanager.common.R.plurals.download_part_success, size, filename, size),
                Utils.RTL_POSITION_DOUBLE
            )
        }
        val notification = createDownloadSuccessNotification(filePath, successTitle, displayPath)
        notifyNotification(downloadSuccessNotificationInfo.notificationId, notification)
    }

    private fun updateDownloadSuccessNotification(
        notificationInfo: DownloadSuccessNotificationInfo,
        filename: String
    ): DownloadSuccessNotificationInfo {
        Log.d(TAG, "updateDownloadSuccessNotification -> info = $notificationInfo")
        val containsNotificationId = notificationListContains(notificationInfo.notificationId)
        Log.d(TAG, "updateDownloadSuccessNotification -> contains = $containsNotificationId")
        if (containsNotificationId) {
            notificationInfo.currName = filename
            notificationInfo.successCount++
        } else {
            notificationInfo.currName = filename
            notificationInfo.successCount = 1
        }
        return notificationInfo
    }

    private fun createDownloadNotificationInfo(filePath: String, fileName: String): DownloadSuccessNotificationInfo {
        val notificationId = generateNotificationId()
        Log.d(TAG, "createDownloadNotificationInfo -> notificationId = $notificationId")
        return DownloadSuccessNotificationInfo(notificationId, filePath, fileName, 1)
    }

    private fun generateNotificationId(): Int {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val notifications = notificationManager.activeNotifications
        var generateNotificationId = NOTIFICATION_DOWNLOAD_SUCCESS_ID + notifications.size + 1
        notifications.forEach {
            if (it.id == generateNotificationId) {
                generateNotificationId += AUTO_INCREMENT_MAGIC_NUMBER
            }
        }
        return generateNotificationId
    }

    private fun notificationListContains(notificationId: Int): Boolean {
        val notificationManager =
            context.applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val notifications = notificationManager.activeNotifications
        notifications.forEach {
            Log.d(TAG, "notificationListContains -> ${it.id}")
            if (it.id == notificationId) {
                return true
            }
        }
        return false
    }

    fun showDownloadFailureNotification(filePaths: List<String>, failureDownloadTasks: ArrayList<DownloadTaskInfo>) {
        if (filePaths.isEmpty()) {
            return
        }
        val firstPath = filePaths[0]
        val name = FilenameUtils.getName(firstPath)
        val size = filePaths.size
        val failureTitle = if (size == 1) {
            context.getString(com.filemanager.common.R.string.download_failure, name)
        } else {
            Utils.formatMessage(
                context.resources.getQuantityString(com.filemanager.common.R.plurals.download_part_failure, size, name, size),
                Utils.RTL_POSITION_DOUBLE
            )
        }
        val notification = createDownloadFailureNotification(failureTitle, failureDownloadTasks)
        notifyNotification(NOTIFICATION_DOWNLOAD_FAILURE_ID, notification)
    }

    fun cancelDownloadFailedNotification() {
        cancelNotification(NOTIFICATION_DOWNLOAD_FAILURE_ID)
    }

    fun createProgressNotification(
        fileName: String,
        progress: Int,
        index: Int,
        totalCount: Int
    ): Notification {
        val intent = DocumentDownloadDispatcher.createCancelDownloadIntent(context)
        val cancelPendingIntent = createPendingIntent(context, intent)
        return NotificationCompat.Builder(context, NOTIFICATION_PROGRESS_CHANNEL_ID)
            .setSmallIcon(com.filemanager.common.R.drawable.ic_launcher_filemanager).addAction(
                com.filemanager.common.R.drawable.ic_launcher_filemanager,
                context.getString(com.filemanager.common.R.string.button_cancel_text),
                cancelPendingIntent
            )
            .setContentTitle(
                context.getString(
                    com.filemanager.common.R.string.downloading_with_progress,
                    index,
                    totalCount,
                    fileName
                )
            )
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(false)
            .setOnlyAlertOnce(true)
            .setProgress(NOTIFICATION_PROGRESS_MAX, progress, false)
            .build()
    }

    private fun createDownloadSuccessNotification(filePath: String, title: String, info: String): Notification {
        val openSpecifiedPathIntent = openSpecifiedPathIntent(filePath)
        val intent = createPendingIntentActivity(context, openSpecifiedPathIntent)
        return NotificationCompat.Builder(context, NOTIFICATION_PROGRESS_CHANNEL_ID)
            .setSmallIcon(com.filemanager.common.R.drawable.ic_launcher_filemanager).setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentTitle(title).setContentText(info).setAutoCancel(true).setContentIntent(intent).build()
    }

    private fun createDownloadFailureNotification(
        title: String,
        failureDownloadTasks: ArrayList<DownloadTaskInfo>
    ): Notification {
        val intent = DocumentDownloadDispatcher.createRetryDownloadIntent(context, failureDownloadTasks)
        val contentIntent = createOpenCloudDocsIntent(failureDownloadTasks)
        val retryPendingIntent = createPendingIntent(context, intent)
        val builder = NotificationCompat.Builder(context, NOTIFICATION_PROGRESS_CHANNEL_ID)
            .setSmallIcon(com.filemanager.common.R.drawable.ic_launcher_filemanager)
            .setContentTitle(title)
            .addAction(
                com.filemanager.common.R.drawable.ic_launcher_filemanager, context.getString(
                    com.filemanager.common.R.string.retry_string), retryPendingIntent)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
        contentIntent?.let {
            val contentPendingIntent = createPendingIntentActivityImmutable(context, it)
            builder.setContentIntent(contentPendingIntent)
        }
        return builder.build()
    }

    fun createNotificationChannel(
        channelId: String = NOTIFICATION_PROGRESS_CHANNEL_ID,
        channelName: String = stringResource(com.filemanager.common.R.string.cloud_docs),
        importance: Int = NotificationManager.IMPORTANCE_HIGH,
        closeSound: Boolean = false
    ) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        if (!checkNotificationChannelExist(notificationManager, channelId)) {
            val channel = NotificationChannel(channelId, channelName, importance)
            if (closeSound) {
                channel.setSound(null, null)
            }
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createOpenCloudDocsIntent(failureDownloadTasks: ArrayList<DownloadTaskInfo>): Intent? {
        val size = failureDownloadTasks.size
        if (size < FAILURE_MIN_COUNT) {
            Log.d(TAG, "createOpenCloudDocsIntent -> failure task less than one")
            return null
        }
        val firstTask = failureDownloadTasks[0]
        // 小屏手机跳转到FileDriveActivity，中大屏手机跳转到MainActivity
        val action = if (FeatureCompat.isSmallScreenPhone || FeatureCompat.isFlipDevice) {
            ACTION_OPEN_CLOUD_DOCS
        } else {
            ACTION_OPEN_MAIN
        }
        Log.d(TAG, "createOpenCloudDocsIntent -> action: $action")
        val intent = Intent(action).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            putExtra(Constants.KEY_FILE_DRIVE_TYPE, firstTask.category)
            putExtra(CommonConstants.KEY_START_FILE_CLOUD_DRIVE, true)
            putExtra(CommonConstants.KEY_IS_AUTHORIZING, true)
            setPackage(MyApplication.sAppContext.packageName)
        }
        return intent
    }

    private fun checkNotificationChannelExist(notificationManager: NotificationManager, channelId: String): Boolean {
        notificationManager.notificationChannels?.map { channel ->
            if (channel.id == channelId) {
                return true
            }
        }
        return false
    }

    private fun openSpecifiedPathIntent(path: String): Intent {
        val intent = Intent(ACTION_OPEN_SPECIFIED_PATH).apply {
            component = ComponentName(FILE_MANAGER_PKG, BROWSER_FILE_ACTIVITY)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            putExtra(KEY_CURRENT_DIR, path)
        }
        return intent
    }

    private fun createPendingIntentActivity(context: Context, intent: Intent): PendingIntent {
        return PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
        )
    }

    private fun createPendingIntentActivityImmutable(context: Context, intent: Intent): PendingIntent {
        return PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    private fun createPendingIntent(context: Context, intent: Intent): PendingIntent {
        return PendingIntent.getService(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
        )
    }

    @SuppressLint("MissingPermission")
    private fun notifyNotification(notificationId: Int, notification: Notification) {
        if (PermissionUtils.hasNotificationPermission(context)) {
            runCatching {
                NotificationManagerCompat.from(context).notify(notificationId, notification)
            }.onFailure {
                Log.e(TAG, "notifyNotification -> error cause ${it.message}")
            }
        }
    }

    private fun cancelNotification(notificationId: Int) {
        runCatching {
            Log.d(TAG, "cancelNotification $notificationId")
            NotificationManagerCompat.from(context).cancel(notificationId)
        }.onFailure {
            Log.e(TAG, "cancelNotification:$notificationId -> error cause ${it.message}")
        }
    }

    companion object {
        private const val TAG = "NotificationManager"

        private const val FAILURE_MIN_COUNT = 1

        private const val NOTIFICATION_PROGRESS_MAX = 100

        private const val NOTIFICATION_PROGRESS_CHANNEL_ID = "document-download-channel-id"

        private const val NOTIFICATION_DOWNLOAD_SUCCESS_CHANNEL_ID = "document-download-success-channel-id"

        private const val ACTION_OPEN_CLOUD_DOCS = "oplus.intent.action.filemanager.thirddrive"
        private const val ACTION_OPEN_MAIN = "oplus.intent.action.DRIVE_DOWNLOAD"
        private const val ACTION_OPEN_SPECIFIED_PATH = "oplus.intent.action.filemanager.BROWSER_FILE"
        private const val KEY_CURRENT_DIR = "CurrentDir"
        private const val FILE_MANAGER_PKG = "com.coloros.filemanager"
        private const val BROWSER_FILE_ACTIVITY = "com.oplus.filebrowser.FileBrowserActivity"

        private val downloadSuccessNotificationMap = mutableMapOf<String, DownloadSuccessNotificationInfo>()

        private const val AUTO_INCREMENT_MAGIC_NUMBER = 13

        const val NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID = 1000
        const val NOTIFICATION_DOWNLOAD_FAILURE_ID = 1001
        const val NOTIFICATION_DOWNLOAD_SUCCESS_ID = 1002
    }
}