package com.oplus.dropdrag.recycleview

import androidx.recyclerview.widget.RecyclerView
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * Range类的单元测试类
 * 测试ListRange和GridRange的各种边界条件和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RangeTest {

    // 测试使用的回调接口mock对象
    private lateinit var callbacks: Callbacks

    /**
     * 在每个测试方法执行前初始化
     * 创建Callbacks的spy对象用于验证方法调用
     */
    @Before
    fun setUp() {
        callbacks = spyk(CallbacksImpl())
    }

    /**
     * 在每个测试方法执行后清理
     * 清除所有mock对象的状态
     */
    @After
    fun tearDown() {
        clearAllMocks()
    }

    // ========== ListRange测试用例 ==========

    /**
     * 测试ListRange在传入NO_POSITION时不调用updateForRange方法
     */
    @Test
    fun `ListRange extendRange with NO_POSITION should not call updateForRange`() {
        val listRange = ListRange(5, callbacks, true)
        listRange.extendRange(RecyclerView.NO_POSITION)
        verify(exactly = 0) { callbacks.updateForRange(any(), any(), any(), any()) }
    }

    /**
     * 测试ListRange初始建立升序范围的情况
     */
    @Test
    fun `ListRange extendRange initial establishment ascending`() {
        val listRange = ListRange(5, callbacks, true)
        listRange.extendRange(8)
        verify(exactly = 1) { callbacks.updateForRange(6, 8, 5, true) }
    }

    /**
     * 测试ListRange初始建立降序范围的情况
     */
    @Test
    fun `ListRange extendRange initial establishment descending`() {
        val listRange = ListRange(10, callbacks, false)
        listRange.extendRange(7)
        verify(exactly = 1) { callbacks.updateForRange(7, 9, 10, false) }
    }

    /**
     * 测试ListRange初始建立相同位置范围的情况
     */
    @Test
    fun `ListRange extendRange initial establishment same position`() {
        val listRange = ListRange(5, callbacks, true)
        listRange.extendRange(5)
        verify(exactly = 1) { callbacks.updateForRange(5, 5, 5, true) }
    }

    /**
     * 测试ListRange在升序情况下扩展范围
     */
    @Test
    fun `ListRange extendRange revise ascending extend forward`() {
        val listRange = ListRange(5, callbacks, true).apply {
            extendRange(8) // 先建立范围
        }
        clearMocks(callbacks) // 清除之前的调用记录
        listRange.extendRange(10) // 向前扩展
        verify(exactly = 1) { callbacks.updateForRange(9, 10, 5, true) }
    }

    /**
     * 测试ListRange在升序情况下收缩范围
     */
    @Test
    fun `ListRange extendRange revise ascending contract`() {
        val listRange = ListRange(5, callbacks, true).apply {
            extendRange(10) // 先建立范围
        }
        clearMocks(callbacks) // 清除之前的调用记录
        listRange.extendRange(7) // 收缩范围
        verify(exactly = 1) { callbacks.updateForRange(8, 10, 5, true) }
    }

    /**
     * 测试ListRange从升序范围反转为降序范围
     */
    @Test
    fun `ListRange extendRange revise ascending reverse direction`() {
        val listRange = ListRange(10, callbacks, false).apply {
            extendRange(5) // 先建立降序范围
        }
        clearMocks(callbacks) // 清除之前的调用记录
        listRange.extendRange(12) // 反转为升序
        verifyOrder {
            callbacks.updateForRange(5, 9, 10, false)
            callbacks.updateForRange(11, 12, 10, false)
        }
    }

    /**
     * 测试ListRange在降序情况下扩展范围
     */
    @Test
    fun `ListRange extendRange revise descending extend backward`() {
        val listRange = ListRange(10, callbacks, true).apply {
            extendRange(5) // 先建立范围
        }
        clearMocks(callbacks) // 清除之前的调用记录
        listRange.extendRange(3) // 向后扩展
        verify(exactly = 1) { callbacks.updateForRange(3, 4, 10, true) }
    }

    /**
     * 测试ListRange在降序情况下收缩范围
     */
    @Test
    fun `ListRange extendRange revise descending contract`() {
        val listRange = ListRange(10, callbacks, true).apply {
            extendRange(5) // 先建立范围
        }
        clearMocks(callbacks) // 清除之前的调用记录
        listRange.extendRange(8) // 收缩范围
        verify(exactly = 1) { callbacks.updateForRange(5, 7, 10, true) }
    }

    /**
     * 测试ListRange从降序范围反转为升序范围
     */
    @Test
    fun `ListRange extendRange revise descending reverse direction`() {
        val listRange = ListRange(5, callbacks, false).apply {
            extendRange(10) // 先建立升序范围
        }
        clearMocks(callbacks) // 清除之前的调用记录
        listRange.extendRange(3) // 反转为降序
        verifyOrder {
            callbacks.updateForRange(6, 10, 5, false)
            callbacks.updateForRange(3, 4, 5, false)
        }
    }

    /**
     * 测试ListRange的toString方法输出格式
     */
    @Test
    fun `ListRange toString should return correct format`() {
        val listRange = ListRange(5, callbacks, true)
        assert(listRange.toString() == "Range{begin=5, end=-1}") // 初始为-1 (NO_POSITION)
        listRange.extendRange(8)
        assert(listRange.toString() == "Range{begin=5, end=8}")
    }

    // ========== GridRange测试用例 ==========

    /**
     * 测试GridRange在传入NO_POSITION时不调用updateForRange方法
     */
    @Test
    fun `GridRange extendRange with NO_POSITION should not call updateForRange`() {
        val gridRange = GridRange(5, callbacks, true)
        gridRange.extendRange(RecyclerView.NO_POSITION)
        verify(exactly = 0) { callbacks.updateForRange(any(), any(), any(), any()) }
    }

    /**
     * 测试GridRange在positive模式下初始建立升序范围
     */
    @Test
    fun `GridRange extendRange positive initial establishment ascending`() {
        val gridRange = GridRange(5, callbacks, true)
        gridRange.extendRange(8)
        verify(exactly = 1) { callbacks.updateForRange(6, 8, 5, true) }
    }

    /**
     * 测试GridRange在positive模式下初始建立降序范围
     */
    @Test
    fun `GridRange extendRange positive initial establishment descending`() {
        val gridRange = GridRange(10, callbacks, true)
        gridRange.extendRange(7)
        verify(exactly = 1) { callbacks.updateForRange(7, 9, 10, true) }
    }

    /**
     * 测试GridRange在positive模式下升序扩展范围
     */
    @Test
    fun `GridRange extendRange positive revise ascending extend`() {
        val gridRange = GridRange(5, callbacks, true).apply {
            extendRange(8) // 先建立范围
        }
        clearMocks(callbacks) // 清除之前的调用记录
        gridRange.extendRange(10) // 向前扩展
        verify(exactly = 1) { callbacks.updateForRange(8, 10, 5, true) }
    }

    /**
     * 测试GridRange在positive模式下升序收缩范围
     */
    @Test
    fun `GridRange extendRange positive revise ascending contract`() {
        val gridRange = GridRange(5, callbacks, true).apply {
            extendRange(10) // 先建立范围
        }
        clearMocks(callbacks) // 清除之前的调用记录
        gridRange.extendRange(7) // 收缩范围
        verify(exactly = 1) { callbacks.updateForRange(7, 10, 5, false) }
    }

    /**
     * 测试GridRange在positive模式下降序扩展范围
     */
    @Test
    fun `GridRange extendRange positive revise descending extend`() {
        val gridRange = GridRange(10, callbacks, true).apply {
            extendRange(7) // 先建立范围(方向设置为NEGATIVE)
        }
        clearMocks(callbacks) // 清除之前的调用记录
        gridRange.extendRange(5) // 向后扩展
        verify(exactly = 1) { callbacks.updateForRange(5, 7, 10, true) }
    }

    /**
     * 测试GridRange在positive模式下降序收缩范围
     */
    @Test
    fun `GridRange extendRange positive revise descending contract`() {
        val gridRange = GridRange(10, callbacks, true).apply {
            extendRange(5) // 先建立范围(方向设置为NEGATIVE)
        }
        clearMocks(callbacks) // 清除之前的调用记录
        gridRange.extendRange(8) // 收缩范围
        verify(exactly = 1) { callbacks.updateForRange(5, 8, 10, false) }
    }

    /**
     * 测试GridRange在negative模式下初始建立升序范围
     */
    @Test
    fun `GridRange extendRange negative initial establishment ascending`() {
        val gridRange = GridRange(5, callbacks, false)
        gridRange.extendRange(8)
        verify(exactly = 1) { callbacks.updateForRange(6, 8, 5, false) }
    }

    /**
     * 测试GridRange在negative模式下初始建立降序范围
     */
    @Test
    fun `GridRange extendRange negative initial establishment descending`() {
        val gridRange = GridRange(10, callbacks, false)
        gridRange.extendRange(7)
        verify(exactly = 1) { callbacks.updateForRange(7, 9, 10, false) }
    }

    /**
     * 测试GridRange在negative模式下的各种范围修改情况
     */
    @Test
    fun `GridRange extendRange negative revise cases`() {
        // 情况1: position > mEnd 且 mEnd > mBegin
        val gridRange1 = GridRange(5, callbacks, false).apply {
            extendRange(8) // mBegin=5, mEnd=8
        }
        clearMocks(callbacks)
        gridRange1.extendRange(10) // position=10
        verify(exactly = 1) { callbacks.updateForRange(8, 10, 5, false) }

        // 情况2: position < mEnd 且 mEnd > mBegin 且 position < mBegin
        val gridRange2 = GridRange(10, callbacks, false).apply {
            extendRange(15) // mBegin=10, mEnd=15
        }
        clearMocks(callbacks)
        gridRange2.extendRange(5) // position=5
        verify(exactly = 1) { callbacks.updateForRange(5, 15, 10, false) }

        // 可以添加更多测试情况...
    }

    /**
     * 用于测试的Callbacks实现类
     */
    private open class CallbacksImpl : Callbacks() {
        override fun updateForRange(begin: Int, end: Int, anchor: Int, selected: Boolean) {
            // 默认实现不做任何操作
        }
    }
}