/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/19
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
package com.filemanager.common.sort

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.CategoryHelper
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class TypeComparatorGeneratorTest {

    @MockK
    lateinit var context: Context

    @Before
    fun setUp() {
        context = mockk(relaxed = true) {
            every { contentResolver }.returns(mockk(relaxed = true))
            every { applicationContext }.returns(this)
        }
        mockkStatic(MyApplication::class).apply {
            every { appContext } returns context
        }
    }

    @Test
    fun should_return_true_when_validate_type_asc_sorting() {
        val fileName1 = "a8149f3142a747c7.txt"
        val fileName2 = "a8149f3142a747c8.doc"
        val fileName3 = "a8149f3142a747cc.ai"

        val tabTitleSuffixList = mutableMapOf(
            "DOC" to arrayListOf<String?>(".doc"),
            "XLS" to arrayListOf<String?>(".xls"),
            "PPT" to arrayListOf<String?>(".ppt"),
            "PDF" to arrayListOf<String?>(".pdf"),
            "TXT" to arrayListOf<String?>(".txt"),
            "AI" to arrayListOf<String?>(".ai")
        )

        val baseFileBeanMutableList = mutableListOf<BaseFileBean>().apply {
            add(BaseFileBean().apply { mDisplayName = fileName1 })
            add(BaseFileBean().apply { mDisplayName = fileName2 })
            add(BaseFileBean().apply { mDisplayName = fileName3 })
        }
        val typeComparatorGenerator = TypeComparatorGenerator(tabTitleSuffixList)
        val comparator = typeComparatorGenerator.genComparator()
        mockkObject(TypeComparatorGenerator.Companion).apply {
            TypeComparatorGenerator.sCategoryType = CategoryHelper.CATEGORY_DOC
            every { TypeComparatorGenerator.getComparator(any(), any()) } returns comparator
        }
        baseFileBeanMutableList.sortWith(TypeComparatorGenerator.getComparator(tabTitleSuffixList, false))
        Assert.assertTrue(baseFileBeanMutableList[0].mDisplayName == fileName2)
        Assert.assertTrue(baseFileBeanMutableList[1].mDisplayName == fileName1)
        Assert.assertTrue(baseFileBeanMutableList[2].mDisplayName == fileName3)
        unmockkObject(TypeComparatorGenerator.Companion)
    }

    @Test
    fun should_return_false_when_validate_type_desc_sorting() {
        val fileName1 = "a8149f3142a747c7.txt"
        val fileName2 = "a8149f3142a747c8.doc"
        val fileName3 = "a8149f3142a747cc.ai"

        val tabTitleSuffixList = mutableMapOf(
            "DOC" to arrayListOf<String?>(".doc"),
            "XLS" to arrayListOf<String?>(".xls"),
            "PPT" to arrayListOf<String?>(".ppt"),
            "PDF" to arrayListOf<String?>(".pdf"),
            "OFD" to arrayListOf<String?>(".ofd"),
            "TXT" to arrayListOf<String?>(".txt"),
            "AI" to arrayListOf<String?>(".ai")
        )
        val baseFileBeanMutableList = mutableListOf<BaseFileBean>().apply {
            add(BaseFileBean().apply { mDisplayName = fileName1 })
            add(BaseFileBean().apply { mDisplayName = fileName2 })
            add(BaseFileBean().apply { mDisplayName = fileName3 })
        }
        val typeComparatorGenerator = TypeComparatorGenerator(tabTitleSuffixList)
        val comparator = typeComparatorGenerator.genReverseComparator()
        mockkObject(TypeComparatorGenerator.Companion).apply {
            TypeComparatorGenerator.sCategoryType = CategoryHelper.CATEGORY_DOC
            every { TypeComparatorGenerator.getComparator(any(), any()) } returns comparator
        }
        baseFileBeanMutableList.sortWith(TypeComparatorGenerator.getComparator(tabTitleSuffixList, true))
        Assert.assertTrue(baseFileBeanMutableList[2].mDisplayName == fileName2)
        Assert.assertTrue(baseFileBeanMutableList[1].mDisplayName == fileName1)
        Assert.assertTrue(baseFileBeanMutableList[0].mDisplayName == fileName3)
        unmockkObject(TypeComparatorGenerator.Companion)
    }
}