/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File:FileRenameObserver.kt
 * * Description:
 * * Version:1.0
 * * Date :2020.1.9
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.rename

import android.content.Context
import android.view.ContextThemeWrapper
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.CustomToast
import com.filemanager.fileoperate.base.ACTION_CANCELLED
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.base.BaseFileNameDialog

const val SHOW_RENAME_DIALOG = 0
const val NOTICE_FAIL_RENAME_DRM_FILE = 1
const val NOTICE_FILE_EXTENSION_CHANGED = 2
const val NOTICE_FILENAME_EMPTY = 3
const val NOTICE_FOLDERNAME_EMPTY = 4
const val NOTICE_FILENAME_EXISTS = 5
const val NOTICE_FOLDER_EXISTS = 6
const val NOTICE_FOLDER_CREATE_ERROR = 7
const val NOTICE_FILENAME_DEEP = 8
const val NOTICE_FILE_NOT_EXIST = 9
const val NOTICE_FILE_WILL_BE_HIDE = 10
const val NOTICE_FILE_NOT_EXIST_DFS_DISCONNECT = 11

open class FileRenameObserver(context: ContextThemeWrapper) : BaseFileActionObserver(context) {
    private var mRenameDialog: FileRenameDialog? = null

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        when (result.first) {
            SHOW_RENAME_DIALOG -> {
                if (result.second is FileRenameBean) {
                    createAndShowRenameDialog(context, result.second as FileRenameBean)
                }
            }
            NOTICE_FAIL_RENAME_DRM_FILE, NOTICE_FILE_EXTENSION_CHANGED,
            NOTICE_FILENAME_EMPTY, NOTICE_FOLDER_CREATE_ERROR,
            NOTICE_FILENAME_EXISTS, NOTICE_FOLDER_EXISTS -> {
                if (mRenameDialog?.isShowing() == true) {
                    mRenameDialog?.showWarningNotice(result.first as Int)
                }
            }
            NOTICE_FILE_NOT_EXIST -> CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
            NOTICE_FILE_NOT_EXIST_DFS_DISCONNECT -> {
                CustomToast.showShort(
                    com.filemanager.common.R.string.rename_failed_by_device_disconnect
                )
            }
            ACTION_FAILED -> {
                dismissRenameDialog()
                val isDirectory = (result.second is Boolean) && (result.second as Boolean)
                val msg = if (isDirectory) {
                    com.filemanager.common.R.string.toast_rename_folder_error
                } else {
                    com.filemanager.common.R.string.toast_rename_file_error
                }
                CustomToast.showShort(msg)
                return false
            }
            ACTION_DONE, ACTION_CANCELLED -> {
                dismissRenameDialog()
                return false
            }
            else -> return false
        }
        return true
    }

    override fun onActionReShowDialog() {
        mRenameDialog?.reShowDialog()
    }

    private fun createAndShowRenameDialog(context: Context, renameBean: FileRenameBean) {
        if (mRenameDialog == null) {
            mRenameDialog = FileRenameDialog(context, renameBean)
        }
        mRenameDialog?.show()
    }

    protected fun dismissRenameDialog() {
        try {
            mRenameDialog?.dismiss()
        } catch (e: Exception) {
        }
        mRenameDialog = null
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || mRenameDialog?.isShowing() ?: false
    }

    override fun recycle() {
        dismissRenameDialog()
        super.recycle()
    }

    data class FileRenameBean(var type: Int = TYPE_RENAME_FILE, var srcOrParentFile: BaseFileBean) {
        var resultListener: BaseFileNameDialog.OnButtonClickListener? = null

        fun recycle() {
            resultListener = null
        }

        companion object {
            const val TYPE_CREATE_FOLDER = 1
            const val TYPE_RENAME_FILE = 2
            const val TYPE_RENAME_CLOUD_FILE = 3

            /**
             * 创建快捷文件夹
             */
            const val TYPE_CREATE_SHORTCUT_FOLDER = 4
        }
    }
}