/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved.
 * * File: KeyMoveGuideFragment
 * * Description: fragment for KeyMoveGuide
 * * Version: 1.0
 * * Date : 2020/7/23
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/7/23       1.0         fragment for KeyMoveGuide
 ****************************************************************/
package com.oplus.filemanager.keymove.ui.keymoveguide

import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.oplus.filemanager.keymove.R
import com.oplus.filemanager.keymove.ui.AKeyToMoveActivity

class KeyMoveGuideFragment : BaseVMFragment<BaseViewModel>() {

    companion object {
        private const val TAG = "KeyMoveGuideFragment"
        private const val HEIGHT_MARGIN_SCALE = 0.3f
    }

    var mGuideImageView: ImageView? = null
    var mGuideCenterRl: RelativeLayout? = null
    private var mRootView: ViewGroup? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            mActivity = activity as? AKeyToMoveActivity
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.key_move_guide
    }

    override fun initView(view: View) {
        if (isSdcardNormal()) {
            ViewHelper.setClassificationTextSize(MyApplication.sAppContext, view.findViewById<View>(R.id.move_title_tv) as? TextView)
            ViewHelper.setClassificationTextSize(MyApplication.sAppContext, view.findViewById<View>(R.id.move_tip_tv) as? TextView)
            if (!KtAppUtils.mIsOnePlusOverSea) {
                mGuideImageView = view.findViewById<View>(R.id.move_guide_img) as? ImageView
                mGuideCenterRl = view.findViewById<View>(R.id.guide_center_rl) as? RelativeLayout
                mGuideImageView?.apply {
                    val isTablet = ModelUtils.isTablet()
                    when {
                        isTablet -> {
                            setImageResource(R.drawable.akey_move_tablet_guide)
                        }
                        UIConfigMonitor.instance.isScreenFold() -> {
                            setImageResource(R.drawable.akey_move_guide)
                        }
                        else -> {
                            setImageResource(R.drawable.akey_move_screenfold_guide)
                        }
                    }
                    refreshGuideImageMargin()
                }
            }
        } else {
            mActivity?.finish()
            return
        }
        initToolBar(view)
        view.findViewById<COUIButton>(R.id.start_btn)?.apply {
            setOnClickListener {
                if (mActivity is AKeyToMoveActivity) {
                    (mActivity as AKeyToMoveActivity).switchToChoiceFragment(true)
                }
            }
        }
    }

    private fun refreshGuideImageMargin() {
        mGuideCenterRl?.viewTreeObserver?.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                mGuideCenterRl?.viewTreeObserver?.removeOnGlobalLayoutListener(this)
                mGuideImageView?.apply {
                    this.measure(RelativeLayout.LayoutParams.WRAP_CONTENT, RelativeLayout.LayoutParams.WRAP_CONTENT)
                    Log.d(TAG, "measuredHeight,  mGuideCenterRl = ${mGuideCenterRl!!.measuredHeight} ,mGuideImageView = $measuredHeight")
                    //根据Ui要求设置顶部边距为以下计算方式
                    val marginTop = (mGuideCenterRl!!.measuredHeight - measuredHeight) * HEIGHT_MARGIN_SCALE
                    val layoutParams = this.layoutParams as RelativeLayout.LayoutParams
                    layoutParams.topMargin = if (marginTop.toInt() < 0) 0 else marginTop.toInt()
                    setLayoutParams(layoutParams)
                    if (this.visibility == View.GONE) {
                        this.visibility = View.VISIBLE
                    }
                }
            }
        })
    }

    private fun initToolBar(view: View) {
        mRootView = view.findViewById(R.id.relative_layout)
        mRootView?.apply {
            setPadding(paddingLeft,
                COUIPanelMultiWindowUtils.getStatusBarHeight(mActivity), paddingRight, paddingBottom)
        }
        val toolbar: COUIToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        mActivity?.apply {
            setSupportActionBar(toolbar)
            supportActionBar?.let {
                it.setDisplayHomeAsUpEnabled(true)
                it.setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
    }

    private fun isSdcardNormal(): Boolean {
        val mExternalPath = VolumeEnvironment.getExternalSdPath(MyApplication.sAppContext)
        Log.d(TAG, "sdcardExist,  mExternalPath = $mExternalPath")
        return !TextUtils.isEmpty(mExternalPath) &&
                VolumeEnvironment.isExternalSdMounted(MyApplication.sAppContext)
    }

    override fun initData(savedInstanceState: Bundle?) {}

    override fun startObserve() {}

    override fun onResumeLoadData() {}

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        refreshGuideImageMargin()
        mGuideCenterRl?.requestLayout()
    }
}