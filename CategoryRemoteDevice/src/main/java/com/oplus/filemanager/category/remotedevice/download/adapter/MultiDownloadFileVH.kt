/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : MultiDownloadFileVH
 * * Description : 多个下载文件的VH
 * * Version     : 1.0
 * * Date        : 2024/12/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download.adapter

import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.LayoutRes
import androidx.core.view.isInvisible
import androidx.core.view.setPadding
import com.bumptech.glide.Glide
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionViewHolder
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.category.remotedevice.R
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean

class MultiDownloadFileVH(convertView: View) : BaseSelectionViewHolder(convertView) {

    companion object {
        private const val TAG = "MultiDownloadFileVH"

        @LayoutRes
        fun layoutId(): Int {
            return R.layout.layout_download_file_item
        }
    }

    private var img: FileThumbView = convertView.findViewById(R.id.item_file_icon)
    private var videoTypeTv: TextView = convertView.findViewById(R.id.video_type_tv)
    private var titleTv: TextViewSnippet = convertView.findViewById(R.id.item_file_title_tv)
    private var downloadStateImg: ImageView = convertView.findViewById(R.id.item_download_state_img)
    private var sizeTv: TextView = convertView.findViewById(R.id.item_file_size_tv)
    private val imgRadius = appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.file_list_bg_radius)
    private val mNormalTitleMaxSize by lazy {
        appContext.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.file_list_item_info_selected_width_new)
    }
    private val imagePadding = MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_4dp)
    private val imageSize by lazy {
        convertView.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_24dp)
    }

    fun loadData(
        context: Context,
        key: Int,
        position: Int,
        data: DownloadRemoteFileBean,
        count: Int
    ) {
        updateKey(key)
        showFileIcon(context, img, data.mLocalType, data, imgRadius)
        showVideoType(videoTypeTv, data)
        showTitle(titleTv, data.mDisplayName)
        downloadStateImg.isInvisible = !data.isDownloadComplete()
        sizeTv.text = KtUtils.formatSize(data)
        updateDividerVisible(count - 1, position)
    }


    private fun showFileIcon(context: Context, imageView: FileThumbView, type: Int, data: BaseFileBean, radius: Int) {
        val padding = when (type) {
            MimeTypeHelper.IMAGE_TYPE, MimeTypeHelper.VIDEO_TYPE -> {
                MyApplication.sAppContext.resources.getDimension(com.filemanager.common.R.dimen.file_list_image_padding).toInt()
            }

            else -> 0
        }
        imageView.setPadding(padding)

        if (MimeTypeHelper.IMAGE_TYPE == type || MimeTypeHelper.VIDEO_TYPE == type) {
            imageView.setStrokeStyle(FileThumbView.STROKE_2DP)
            val placeholderResId = KtThumbnailHelper.getClassifyResId(type)
            Glide.with(context).asBitmap().load(data).override(imageSize, imageSize)
                .error(placeholderResId)
//                .placeholder(placeholderResId)
                .into(imageView)
        } else {
            imageView.setStrokeStyle(FileThumbView.STROKE_NONE)
            FileImageLoader.sInstance.displayDefault(
                data,
                imageView,
                0,
                radius,
                FileImageLoader.THUMBNAIL_TYPE_LIST,
                loadDocThumbnail = false,
                isCoverError = true,
                isSmallDoc = true
            )
        }
    }

    private fun showVideoType(videoTypeTv: TextView, data: DownloadRemoteFileBean) {
        if (MimeTypeHelper.VIDEO_TYPE == data.mLocalType) {
            videoTypeTv.visibility = View.VISIBLE
            val ext = FileTypeUtils.getExtension(data.mDisplayName)
            videoTypeTv.text = ext
        } else {
            videoTypeTv.visibility = View.GONE
        }
    }

    private fun showTitle(titleTv: TextViewSnippet, displayName: String?) {
        titleTv.text = displayName
        titleTv.maxWidth = mNormalTitleMaxSize
        Log.d(TAG, "showTitle $displayName")
        titleTv.setTextViewStyle()
    }

    override fun drawDivider(): Boolean {
        return true
    }

    override fun getDividerStartAlignView(): View {
        return titleTv
    }

    override fun getDividerEndInset(): Int {
        return MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp)
    }
}