<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/header_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignWithParentIfMissing="true"
    android:layout_alignParentStart="true"
    android:paddingTop="@dimen/dimen_4dp"
    android:paddingBottom="@dimen/dimen_4dp">

    <TextView
        android:id="@+id/label_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_24dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="@color/black_30_percent"
        android:textSize="@dimen/file_list_item_detail_text_size" />
</RelativeLayout>