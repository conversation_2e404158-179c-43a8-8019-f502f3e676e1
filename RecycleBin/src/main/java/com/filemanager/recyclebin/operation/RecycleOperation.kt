/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - RecycleOperation.java
 * Description: operation for move file or folder to recyclebin
 * Version: 1.0
 * Date : 2020/02/26
 * Author: <PERSON><PERSON><PERSON>.<PERSON>
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/02/26    1.0     create
 ****************************************************************/

package com.filemanager.recyclebin.operation

import android.app.Activity
import android.content.ContentResolver
import android.content.ContentValues
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import android.net.Uri
import android.provider.MediaStore.*
import androidx.activity.ComponentActivity
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.RecycleStore
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.batch.SingleBatchAction
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.compat.OplusUsbEnvironmentCompat
import com.filemanager.common.fileutils.MIN_RECYCLER_SIZE
import com.filemanager.common.fileutils.getAllFilePathFromDirectory
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.recyclebin.retriever.DataRetriever
import com.filemanager.recyclebin.utils.OperationDBUtils
import com.filemanager.recyclebin.utils.RecycleBinUtils
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.tool.trackinglib.OpTracker
import com.oplus.tool.trackinglib.OpTrackerMaker
import com.oplus.tool.trackinglib.OpType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

class RecycleOperation(activity: ComponentActivity, listener: ProgressListener, private val mIsMediaType: Boolean = false)
    : BaseOperation<Activity>(activity, listener) {

    companion object {
        private const val TAG = "RecycleOperation"
        private const val BULK_SCAN_SIZE = 50
        private const val BULK_DELETE_SIZE = 50
        private const val MIN_RECYCLER_SIZE = 200 * 1024 * 1024

        private fun internalStoreInsert(values: ContentValues, db: SQLiteDatabase?, sqliteOpenHelper: SQLiteOpenHelper?): Uri? {
            var uri: Uri? = null
            try {
                val store = DataRetriever.INSTANCE
                val originPath = values.getAsString(RecycleStore.Files.FileColumns.ORIGIN_PATH)
                val recyclePath = values.getAsString(RecycleStore.Files.FileColumns.RECYCLE_PATH)
                val statusCode = store.moveToRecycleBin(originPath, recyclePath)
                if (statusCode == STATUS_SUCCESS && db != null) {
                    uri = OperationDBUtils.insert(
                        RecycleStore.Files.INTERNAL_CONTENT_URI,
                        values,
                        db,
                        sqliteOpenHelper
                    )
                }
            } catch (ex: Exception) {
                uri = null
                Log.w(TAG, "internalStoreInsert error: $ex")
            }
            return uri
        }
    }

    init {
        activity.runOnUiThread {
            activity.lifecycle.addObserver(this)
        }
    }

    private var mPaths: ArrayList<String>? = null
    private var mUris: ArrayList<Uri>? = null
    private var mFiles: ArrayList<BaseFileBean>? = null
    private var mPathsError: ArrayList<String>? = null
    private var mUrisError: ArrayList<Uri>? = null
    private var mFilesError: ArrayList<BaseFileBean>? = null

    private val allFilePath = ArrayList<String>()
    private val allFilePathForLabels = ArrayList<String>()
    private val fileContentValuesList = ArrayList<ContentValues>()
    private var errorState: Int = STATUS_ERROR
    private var dbPath: String = ""

    @VisibleForTesting
    val mRecycleBulk: SingleBatchAction<String> by lazy {
        if (mIsMediaType) {
            RecycleMediaAction()
        } else {
            RecycleFileAction()
        }
    }

    override fun addPaths(paths: ArrayList<String>?): Boolean {
        if (mPaths != null) {
            Log.w(TAG, "addPaths already add a task")
            return false
        }

        paths?.let {
            mPaths = ArrayList()
            mPathsError = ArrayList()
            mPaths?.addAll(it)
            return true
        }
        return false
    }

    override fun addUris(uris: ArrayList<Uri>?): Boolean {
        if (mUris != null) {
            Log.w(TAG, "addUris already add a task")
            return false
        }

        uris?.let {
            mUris = ArrayList()
            mUrisError = ArrayList()
            mUris?.addAll(it)
            return true
        }
        return false
    }

    override fun addFileBeans(files: ArrayList<BaseFileBean>?): Boolean {

        if (mFiles != null) {
            Log.w(TAG, "addFiles already add a task")
            return false
        }

        files?.let {
            mFiles = ArrayList()
            mFilesError = ArrayList()
            mFiles?.addAll(it)
            return true
        }
        return false
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    public override fun onTotalCountCallback(): Int {
        return when {
            mUris != null -> {
                mUris?.size ?: 0
            }
            mPaths != null -> {
                mPaths?.size ?: 0
            }
            mFiles != null -> {
                mFiles?.size ?: 0
            }
            else -> 0
        }
    }

    override fun onParametersCallback(): OperationParameters {
        return RecycleParameters(mUrisError, mPathsError, mFilesError, mIsMediaType)
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    public override fun doInBackground(vararg params: Void): OperationResult {
        val store = DataRetriever.INSTANCE
        // check internal Available space
        val spaceAvailable = Utils.getStorageAvailableSize(OplusUsbEnvironmentCompat.getInternalPath(
            appContext
        ))
        val totalCount = onTotalCountCallback()
        if (spaceAvailable < MIN_RECYCLER_SIZE) {
            Log.w(TAG, "doInBackground LOW SPACE: $spaceAvailable")
            cannotDeletedAllFileToErrorList()
            return OperationResult(totalCount, totalCount, STATUS_NOT_ENOUGH_SPACE)
        }

        if (!store.ensureRecycleDirectory()) {
            cannotDeletedAllFileToErrorList()
            Log.w(TAG, "doInBackground  ensureRecycleDirectory failed")
            return OperationResult(totalCount, totalCount, STATUS_NOT_ENOUGH_SPACE)
        }
        storeFilesForBuriedPoint()
        RecycleBinUtils.storeFilesForDeleteLabels(allFilePathForLabels, mPaths, mUris, mFiles)
        val sqliteOpenHelper =
            OperationDBUtils.createSQLiteOpenHelper(RecycleStore.Files.INTERNAL_CONTENT_URI)
        val writableDatabase = kotlin.runCatching {
            sqliteOpenHelper?.writableDatabase
        }.getOrElse {
            null
        }

        // 确保数据库目录存在
        if (writableDatabase == null) {
            Log.e(TAG, "Database directory does not exist and cannot be created")
            cannotDeletedAllFileToErrorList()
            return OperationResult(totalCount, totalCount, STATUS_NOT_ENOUGH_SPACE)
        }
        val db = OperationDBUtils.beginTransaction(writableDatabase)
        try {
            if (mPaths != null) {
                return recyclePaths(store, db, sqliteOpenHelper)
            } else if (mUris != null) {
                return recycleUris(store, db, sqliteOpenHelper)
            } else if (mFiles != null) {
                return recycleFiles(store, db, sqliteOpenHelper)
            } else {
                OperationDBUtils.endTransaction(db)
            }
        } catch (ex: Exception) {
            Log.w(TAG, "doInBackground failed $ex")
        } finally {
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.onRecycleFilePaths(allFilePathForLabels)
            buriedPointForMedia()
        }
        return OperationResult(totalCount, totalCount, STATUS_ERROR)
    }

    private fun cannotDeletedAllFileToErrorList() {
        mPaths?.let {
            mPathsError?.clear()
            mPathsError?.addAll(it)
        }
        mUris?.let {
            mUrisError?.clear()
            mUrisError?.addAll(it)
        }
        mFiles?.let {
            mFilesError?.clear()
            mFilesError?.addAll(it)
        }
    }

    override fun onPostExecute(result: OperationResult) {
        super.onPostExecute(result)
        Log.d(TAG, "onPostExecute ${mFiles?.size}")
        onDestroy()
    }

    override fun onDestroy() {
        mTarget.get()?.let {
            it.runOnUiThread {
                (it as? ComponentActivity)?.lifecycle?.removeObserver(this)
            }
        }
        super.onDestroy()
    }

    private fun recyclePaths(store: DataRetriever, db: SQLiteDatabase?, sqliteOpenHelper: SQLiteOpenHelper?): OperationResult {
        var failedCount = STATUS_SUCCESS
        var index = 0
        var totalSize = 0
        kotlin.runCatching {
            mPaths?.let {
                totalSize = it.size
                for (path in it) {
                    if (store.isInternalPath(path)) {
                        if (!internalRecycle(store, path, db, sqliteOpenHelper)) {
                            mPathsError?.add(path)
                            failedCount++
                            Log.w(TAG, "recyclePaths internalRecycle index: $index")
                        }
                    } else {
                        if (!externalRecycle(store, path)) {
                            mPathsError?.add(path)
                            failedCount++
                            Log.w(TAG, "recyclePaths  externalRecycle index: $index")
                        }
                    }
                    index++
                    publishProgress((index / totalSize.toFloat() * 100).toInt())
                }
            }
        }.onFailure {
            mPaths?.get(index)?.let { mPathsError?.add(it) }
            Log.d(TAG, "recyclePaths execution failed")
        }
        errorState = OperationDBUtils.commit(db, RecycleStore.Files.INTERNAL_CONTENT_URI)
        if (errorState == STATUS_NOT_ENOUGH_SPACE || errorState == STATUS_ERROR) {
            failedCount = mPaths?.size ?: 0
            cannotDeletedAllFileToErrorList()
            dbCommitFailRollback(fileContentValuesList, store)
        } else {
            mRecycleBulk.flush()
        }
        return OperationResult(totalSize, failedCount,
            if (failedCount > 0) {
                errorState
            } else {
                STATUS_SUCCESS
            }
        )
    }

    private fun recycleUris(store: DataRetriever, db: SQLiteDatabase?, sqliteOpenHelper: SQLiteOpenHelper?): OperationResult {
        var failedCount = STATUS_SUCCESS
        var index = 0
        var totalSize = 0
        kotlin.runCatching {
            mUris?.let {
                totalSize = it.size
                for (uri in it) {
                    if (!store.isInternalUri(uri)) {
                        if (!internalRecycle(store, uri, db, sqliteOpenHelper)) {
                            failedCount++
                            mUrisError?.add(uri)
                            Log.w(TAG, "recycleUris internal recycle index: $index")
                        }
                    } else {
                        if (!externalRecycle(store, uri)) {
                            failedCount++
                            mUrisError?.add(uri)
                            Log.w(TAG, "recycleUris  external recycle index: $index")
                        }
                    }
                    index++
                    publishProgress((index / totalSize.toFloat() * 100).toInt())
                }
            }
        }.onFailure {
            mUris?.get(index)?.let {
                mUrisError?.add(it)
            }
            Log.d(TAG, "recycleUris execution failed")
        }
        errorState = OperationDBUtils.commit(db, RecycleStore.Files.INTERNAL_CONTENT_URI)
        if (errorState == STATUS_NOT_ENOUGH_SPACE || errorState == STATUS_ERROR) {
            failedCount = mUris?.size ?: 0
            cannotDeletedAllFileToErrorList()
            dbCommitFailRollback(fileContentValuesList, store)
        } else {
            mRecycleBulk.flush()
        }
        return OperationResult(totalSize, failedCount,
                if (failedCount > 0) {
                    errorState
                } else {
                    STATUS_SUCCESS
                }
        )
    }

    /**
     * @return if success return failedCount,else return
     */
    private fun recycleFiles(store: DataRetriever, db: SQLiteDatabase?, sqliteOpenHelper: SQLiteOpenHelper?): OperationResult {
        var failedCount = STATUS_SUCCESS
        var index = 0
        var totalSize = 0
        var path: String?
        var internalRecycle = false
        var externalRecycle = false
        kotlin.runCatching {
            mFiles?.let {
                totalSize = it.size
                for (file in it) {
                    path = file.mData
                    if (path.isNullOrEmpty()) {
                        failedCount++
                        index++
                        continue
                    }
                    if (store.isInternalPath(path)) {
                        path?.let { internalRecycle = internalRecycle(store, it, db, sqliteOpenHelper) }
                        if (!internalRecycle) {
                            mFilesError?.add(file)
                            failedCount++
                            Log.w(TAG, "recycleFiles internalRecycle index: $index failedCount: $failedCount")
                        }
                    } else {
                        path?.let { externalRecycle = externalRecycle(store, it) }
                        if (!externalRecycle) {
                            mFilesError?.add(file)
                            failedCount++
                            index++
                            Log.w(TAG, "recycleFiles  externalRecycle index: $index failedCount: $failedCount")
                            continue
                        }
                    }
                    index++
                    publishProgress((index / totalSize.toFloat() * 100).toInt())
                }
            }
            failedCount += (totalSize - index)
        }.onFailure {
            mFiles?.get(index)?.let { mFilesError?.add(it) }
            failedCount += (totalSize - index)
            Log.d(TAG, "recycleFiles execution failed")
        }
        errorState = OperationDBUtils.commit(db, RecycleStore.Files.INTERNAL_CONTENT_URI)
        if (errorState == STATUS_NOT_ENOUGH_SPACE || errorState == STATUS_ERROR) {
            Log.d(TAG, "recycleFiles execution failed errorState is $errorState")
            failedCount = mFiles?.size ?: 0
            cannotDeletedAllFileToErrorList()
            dbCommitFailRollback(fileContentValuesList, store)
        } else {
            mRecycleBulk.flush()
        }
        return OperationResult(totalSize, failedCount, if (failedCount > 0) { errorState } else { STATUS_SUCCESS })
    }

    private fun dbCommitFailRollback(contentValuesList: ArrayList<ContentValues>, store: DataRetriever) {
        Log.d(TAG, "dbCommitFailRollback exec")
        contentValuesList.forEach { contentValue ->
            rollbackPath(contentValue, store)
        }
    }

    private fun rollbackPath(values: ContentValues, store: DataRetriever) {
        val originPath = values.getAsString(RecycleStore.Files.FileColumns.ORIGIN_PATH)
        val recyclePath = values.getAsString(RecycleStore.Files.FileColumns.RECYCLE_PATH)
        store.moveToRecycleBin(recyclePath, originPath)
    }

    private fun internalRecycle(store: DataRetriever, path: String, db: SQLiteDatabase?, sqliteOpenHelper: SQLiteOpenHelper?): Boolean {
        try {
            // NOTE: return success if file not exist
            if (!File(path).exists()) {
                Log.d(TAG, "internalRecycle FILE NOT EXIST")
                // clean item in MediaStore if file not exist
                mRecycleBulk.add(path)
                return true
            }

            val values = store.extractRequiredColumns(path) ?: return false
            fileContentValuesList.add(values)
            internalStoreInsert(values, db, sqliteOpenHelper)?.let {
                mRecycleBulk.add(path)
                return true
            }
            val originPath = values.getAsString(RecycleStore.Files.FileColumns.ORIGIN_PATH)
            val recyclePath = values.getAsString(RecycleStore.Files.FileColumns.RECYCLE_PATH)
            store.moveToRecycleBin(recyclePath, originPath)
        } catch (ex: Exception) {
            Log.e(TAG, "internalRecycle $path failed: $ex")
        }
        return false
    }

    private fun internalRecycle(store: DataRetriever, uri: Uri, db: SQLiteDatabase?, sqliteOpenHelper: SQLiteOpenHelper?): Boolean {
        try {
            val path = uri.path ?: return false
            // NOTE: return success if file not exist
            if (!File(path).exists()) {
                Log.d(TAG, "internalRecycle FILE NOT EXIST")
                // clean item in MediaStore if file not exist
                mRecycleBulk.add(path)
                return true
            }

            val values = store.extractRequiredColumns(uri) ?: return false
            fileContentValuesList.add(values)
            internalStoreInsert(values, db, sqliteOpenHelper)?.let {
                mRecycleBulk.add(path)
                return true
            }
            val originPath = values.getAsString(RecycleStore.Files.FileColumns.ORIGIN_PATH)
            val recyclePath = values.getAsString(RecycleStore.Files.FileColumns.RECYCLE_PATH)
            store.moveToRecycleBin(recyclePath, originPath)
        } catch (ex: Exception) {
            Log.e(TAG, "internalRecycle $uri failed: $ex")
        }
        return false
    }

    private fun externalRecycle(store: DataRetriever, path: String): Boolean {
        try {
            if (store.deleteQuietly(path)) {
                mRecycleBulk.add(path)
                return true
            }
        } catch (ex: Exception) {
            Log.e(TAG, "internalRecycle $path failed: $ex")
        }
        return false
    }

    private fun externalRecycle(store: DataRetriever, uri: Uri): Boolean {
        try {
            val path = uri.path ?: return false
            if (store.deleteQuietly(path)) {
                mRecycleBulk.add(path)
                return true
            }
        } catch (ex: Exception) {
            Log.e(TAG, "internalRecycle $uri failed: $ex")
        }
        return false
    }

    @VisibleForTesting
    fun storeFilesForBuriedPoint() {
        allFilePath.clear()
        if (mPaths != null) {
            mPaths!!.forEach {
                val file = File(it)
                if (file.isDirectory) {
                    allFilePath.addAll(getAllFilePathFromDirectory(file))
                } else {
                    if (file.name.startsWith(".").not()) {
                        allFilePath.add(it)
                    }
                }
            }
        } else if (mUris != null) {
            mUris!!.forEach {
                it.path?.let { path ->
                    val file = File(path)
                    if (file.isDirectory) {
                        allFilePath.addAll(getAllFilePathFromDirectory(file))
                    } else {
                        if (file.name.startsWith(".").not()) {
                            allFilePath.add(path)
                        }
                    }
                }
            }
        } else if (mFiles != null) {
            mFiles!!.forEach {
                it.mData?.let { data ->
                    val file = File(data)
                    if (file.isDirectory) {
                        allFilePath.addAll(getAllFilePathFromDirectory(file))
                    } else {
                        if (it.mDisplayName!!.startsWith(".").not()) {
                            allFilePath.add(data)
                        }
                    }
                }
            }
        }
    }

    @VisibleForTesting
    fun buriedPointForMedia() {
        val opTracker = OpTracker(appContext)
        var path = "default"
        mTarget.get()?.let {
            path = (it as? ComponentActivity)?.javaClass!!.name
        }
        Log.d(TAG, "buriedPointForMedia path = $path")
        val pointList: List<Map<String, String>> = opTracker.convert(
            OpTrackerMaker.Builder().apply {
                setOp(OpType.DELETE)
                setOpTime(System.currentTimeMillis().toString())
                setOpPath(path)
                setFilePaths(allFilePath)
            }.build()
        )
        pointList.forEach {
            StatisticsUtils.onCommon(appContext, StatisticsUtils.FILE_OPERATION, it)
        }
    }

    private class RecycleFileAction(bulkCount: Int = BULK_SCAN_SIZE)
        : SingleBatchAction<String>(bulkCount) {

        override fun flush() {
            val size = mPending.size
            if (size > 0) {
                MediaScannerCompat.sendMultiDirMediaScanner(mPending, Utils.MEDIA_SCAN_RECYCLE_RESTORE)
                mPending.clear()
            }
        }
    }

    private class RecycleMediaAction(bulkCount: Int = BULK_DELETE_SIZE)
        : SingleBatchAction<String>(bulkCount) {

        private val mWhereClause: StringBuilder = StringBuilder()
        private val mResolver: ContentResolver = appContext.contentResolver
        private val mUri = Files.getContentUri("external").buildUpon()
            .appendQueryParameter(PARAM_DELETE_DATA, "false").build()

        override fun add(pending: String) {
            if (pending.isNotEmpty()) {
                if (mWhereClause.isNotEmpty()) {
                    mWhereClause.append(",")
                }
                mWhereClause.append("?")
            }

            super.add(pending)
        }

        override fun flush() {
            val size = mPending.size
            if (size <= 0) {
                return
            }

            try {
                val foo = mPending.toTypedArray<String>()
                val numRows = mResolver.delete(mUri, "${MediaColumns.DATA} IN ($mWhereClause)", foo)
                MediaScannerCompat.sendMultiDirMediaScanner(mPending, Utils.MEDIA_SCAN_RECYCLE_RESTORE)
                Log.d(TAG, "flush size: $size numrows: $numRows")
            } catch (ex: Exception) {
                Log.w(TAG, "flush Failed $ex")
            } finally {
                //make sure reset args only when sucess
                mWhereClause.setLength(0)
                mWhereClause.clear()
                mPending.clear()
            }
        }
    }
}

internal class RecycleParameters(uris: ArrayList<Uri>? = null,
                                 paths: ArrayList<String>? = null,
                                 baseFiles: ArrayList<BaseFileBean>? = null,
                                 val mIsMediaType: Boolean = false)
    : BaseOperation.OperationParameters(uris, paths, baseFiles)
