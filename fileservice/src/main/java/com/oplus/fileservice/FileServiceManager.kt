/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileServiceManager
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/29 10:53
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/6/29       1.0      create
 ***********************************************************************/
package com.oplus.fileservice

import android.content.Context
import com.oplus.fileservice.impl.FileServiceManagerImpl
import com.oplus.fileservice.impl.FileServiceRunnable
import com.oplus.fileservice.operate.convert.Request
import com.oplus.fileservice.operate.internal.FileInfo

/**
 * [FileServiceManager] is used to enqueue request that is guaranteed to execute.
 */
abstract class FileServiceManager {

    /**
     * execute [Runnable] on background.
     */
    abstract fun executeOnBackground(serviceRunnable: Runnable?)

    /**
     * execute auto reply request.
     *
     */
    abstract fun enqueueAutoReplyRequest(context: Context, request: Request, stop: () -> Unit)

    /**
     * run async [Runnable].
     *
     * @param runnable
     */
    abstract fun runAsync(runnable: FileServiceRunnable)

    /**
     * Cancel unique request by requestId.
     */
    abstract fun cancelUniqueRequest(context: Context, requestId: String, stop: () -> Unit)

    /**
     * Rename folder.
     */
    abstract fun renameTo(context: Context, sourceFileInfo: FileInfo, targetFileInfo: FileInfo): Int

    /**
     * Cut file
     */
    abstract fun cutTo(context: Context, sourceFileInfos: List<FileInfo>, targetFileInfo: FileInfo): Int

    companion object {
        fun getInstance(): FileServiceManager = FileServiceManagerImpl.getInstance()
    }
}