/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - BorderedRoundedCornersTrans.kt
 * Description:
 *     Draw image as rounded corners with border when glide load and show it.
 *
 * Version: 1.0
 * Date: 2024-09-11
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-09-11   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Shader
import androidx.annotation.ColorRes
import androidx.annotation.DimenRes
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import com.bumptech.glide.load.resource.bitmap.TransformationUtils
import com.filemanager.common.imageloader.glide.RoundRectUtil
import com.oplus.filemanager.preview.R
import java.security.MessageDigest

internal class BorderedRoundedCornersTrans(
    private val radius: Float,
    private val borderPaint: Paint
) : BitmapTransformation() {

    private val id = javaClass.name
    private val idBytes = id.toByteArray(Charsets.UTF_8)

    constructor(
        context: Context,
        @DimenRes radiusId: Int = R.dimen.preview_image_round_radius,
        @DimenRes strokeWidthId: Int = R.dimen.preview_image_stroke_width,
        @ColorRes strokeColorId: Int = R.color.preview_image_border_color
    ) : this(
        radius = context.resources.getDimension(radiusId),
        borderPaint = Paint().apply {
            style = Paint.Style.STROKE
            strokeWidth = context.resources.getDimension(strokeWidthId)
            color = context.getColor(strokeColorId)
            isAntiAlias = true
        }
    )

    override fun equals(other: Any?): Boolean {
        if (other !is BorderedRoundedCornersTrans) {
            return false
        }
        if (radius != other.radius) {
            return false
        }
        if (borderPaint.strokeWidth != other.borderPaint.strokeWidth) {
            return false
        }
        if (borderPaint.color != other.borderPaint.color) {
            return false
        }
        return true
    }

    override fun hashCode(): Int {
        var result = radius.hashCode()
        result = 31 * result + borderPaint.strokeWidth.hashCode()
        result = 31 * result + borderPaint.color.hashCode()
        return result
    }

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {
        messageDigest.update(idBytes)
    }

    override fun transform(
        pool: BitmapPool,
        toTransform: Bitmap,
        outWidth: Int,
        outHeight: Int
    ): Bitmap? {
        val source =
            TransformationUtils.fitCenter(pool, toTransform, outWidth, outHeight) ?: return null
        val result = pool[source.width, source.height, Bitmap.Config.ARGB_8888]
        val canvas = Canvas(result)
        val paint = Paint()
        paint.shader = BitmapShader(source, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
        paint.isAntiAlias = true
        drawBorderedRoundedCorners(canvas, source.width.toFloat(), source.height.toFloat(), paint)
        return result
    }

    private fun drawBorderedRoundedCorners(
        canvas: Canvas,
        rectWidth: Float,
        rectHeight: Float,
        bitmapPaint: Paint
    ) {
        val strokeWidth = borderPaint.strokeWidth
        val centerX = rectWidth / 2
        val centerY = rectHeight / 2
        val path = getRoundRectPath(0f, 0f, rectWidth, rectHeight)
        // Draw corner
        val saveCorner = canvas.save()
        val cornerScaleX = (rectWidth - (strokeWidth * 2)) / rectWidth
        val cornerScaleY = (rectHeight - (strokeWidth * 2)) / rectHeight
        canvas.scale(cornerScaleX, cornerScaleY, centerX, centerY)
        canvas.drawPath(path, bitmapPaint)
        canvas.restoreToCount(saveCorner)
        // Draw border
        val saveBorder = canvas.save()
        val borderScaleX = (rectWidth - (strokeWidth / 2)) / rectWidth
        val borderScaleY = (rectHeight - (strokeWidth / 2)) / rectHeight
        canvas.scale(borderScaleX, borderScaleY, centerX, centerY)
        canvas.drawPath(path, borderPaint)
        canvas.restoreToCount(saveBorder)
    }

    private fun getRoundRectPath(left: Float, top: Float, right: Float, bottom: Float): Path =
        RoundRectUtil.getPath(left, top, right, bottom, radius, true, true, true, true)
}