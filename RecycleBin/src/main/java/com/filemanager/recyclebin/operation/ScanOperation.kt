/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - ScanOperation.java
 * Description: Scan and count size for all files selected to check if or not total size reach to limit
 * Version: 1.0
 * Date : 2020/02/27
 * Author: Jiafei.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/02/27    1.0     create
 ****************************************************************/
package com.filemanager.recyclebin.operation

import android.app.Activity
import android.net.Uri
import androidx.activity.ComponentActivity
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.MediaStoreCompat.getCShotFiles
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.recyclebin.operation.listener.ScanOperationListener
import com.filemanager.recyclebin.retriever.DataRetriever

class ScanOperation(activity: ComponentActivity, listener: ScanOperationListener, private val mParamState: Int = 0)
    : BaseOperation<Activity>(activity, listener) {
    companion object {
        private const val TAG: String = "ScanOperation"
        const val SCAN_LIMIT_SIZE: Long = 500 * 1024 * 1024
        const val NEED_CSHOT_STATE = 1 shl 0
    }

    init {
        activity.runOnUiThread {
            activity.lifecycle.addObserver(this)
        }
    }

    private var mFiles: ArrayList<BaseFileBean>? = null

    private var mPaths: ArrayList<String>? = null

    private var mTotalSize = 0L

    private var mIsFromRecycleBinDelete: Boolean = (listener.recycleFileParameters.mCategory == CategoryHelper.CATEGORY_RECYCLE_BIN)

    private var mHasExternalFile: Boolean = false

    override fun addPaths(paths: ArrayList<String>?): Boolean {
        if (mPaths != null) {
            Log.w(TAG, "addPaths already add a task")
            return false
        }

        paths?.let {
            mPaths = ArrayList<String>()
            mPaths?.addAll(it)
            return true
        }
        return false
    }

    override fun addUris(uris: ArrayList<Uri>?): Boolean {
        //do nothing
        Log.w(TAG, "addUris: not support")
        return false
    }

    override fun addFileBeans(files: ArrayList<BaseFileBean>?): Boolean {
        if (mFiles != null) {
            Log.w(TAG, "addFiles already add a task")
            return false
        }

        files?.let {
            mFiles = ArrayList<BaseFileBean>()
            mFiles?.addAll(it)
            return true
        }
        return false
    }

    override fun onTotalCountCallback(): Int {
        if (mPaths != null) {
            return mPaths?.size ?: 0
        } else if (mFiles != null) {
            return mFiles?.size ?: 0
        }
        return 0
    }

    override fun onParametersCallback(): OperationParameters? {
        return ScanParameters(null, null, mFiles, mTotalSize, mHasExternalFile)
    }

    override fun doInBackground(vararg voids: Void): OperationResult {
        mTotalSize = 0
        try {
            if (mFiles != null) {
                when (mParamState) {
                    NEED_CSHOT_STATE -> getCShotFiles(mFiles!!)
                }
                checkIncludeExternalFileByFile(mFiles)
                val statusCode = if (checkRecycleLimitSizeByFile(mFiles)) {
                    STATUS_REACH_LIMIT
                } else {
                    STATUS_SUCCESS
                }
                return OperationResult(onTotalCountCallback(), 0, statusCode)
            } else if (mPaths != null) {
                checkIncludeExternalFileByPath(mPaths)
                val statusCode = if (checkRecycleLimitSizeByPath(mPaths)) {
                    STATUS_REACH_LIMIT
                } else {
                    STATUS_SUCCESS
                }
                return OperationResult(onTotalCountCallback(), 0, statusCode)
            }
        } catch (ex: Exception) {
            Log.w(TAG, "doInBackground failed $ex")
        }
        return OperationResult(onTotalCountCallback(), 0, STATUS_SUCCESS)
    }

    override fun onPostExecute(result: OperationResult) {
        super.onPostExecute(result)
        onDestroy()
    }

    override fun onDestroy() {
        mTarget.get()?.let {
            it.runOnUiThread {
                (it as? ComponentActivity)?.lifecycle?.removeObserver(this)
            }
        }
        super.onDestroy()
    }

    private fun checkRecycleLimitSizeByPath(paths: ArrayList<String>?): Boolean {
        if (paths.isNullOrEmpty()) {
            Log.e(TAG, "checkRecycleLimitSizeByPath files empty")
            return false
        }
        if (FeatureCompat.sIsLightVersion) {
            Log.w(TAG, "lightVersion not need check size, file Delete Directly")
            return false
        }

        var file: BaseFileBean? = null
        for (path in paths) {
            file = PathFileWrapper(path)
            if (file.mIsDirectory) {
                checkRecycleLimitSize(file)
            } else {
                mTotalSize += file.mSize
            }
            if (mTotalSize >= SCAN_LIMIT_SIZE) {
                Log.w(TAG, "checkRecycleLimitSize reach limit mTotalSize: $mTotalSize")
                return true
            }
        }
        return false
    }

    private fun checkRecycleLimitSizeByFile(files: ArrayList<BaseFileBean>?): Boolean {
        if (files.isNullOrEmpty()) {
            Log.e(TAG, "checkRecycleLimitSizeByFile files empty")
            return false
        }
        if (FeatureCompat.sIsLightVersion) {
            Log.w(TAG, "lightVersion not need check size, file Delete Directly")
            return false
        }
        for (file: BaseFileBean in files) {
            if (file.mIsDirectory) {
                checkRecycleLimitSize(file)
            } else {
                mTotalSize += file.mSize
            }
            if ((mTotalSize >= SCAN_LIMIT_SIZE)) {
                Log.w(TAG, "checkRecycleLimitSize reach limit mTotalSize: $mTotalSize")
                return true
            }
        }
        return false
    }

    private fun checkRecycleLimitSize(directory: BaseFileBean): Boolean {
        if (mTotalSize >= SCAN_LIMIT_SIZE) {
            Log.e(TAG, "checkRecycleLimitSize reach limit")
            return true
        }
        //count folder size
        mTotalSize += directory.mSize
        val list = JavaFileHelper.listFileBeans(directory, excludeHideFile = !(mIsFromRecycleBinDelete || HiddenFileHelper.isNeedShowHiddenFile()))
                ?: return false

        for (file in list) {
            if (!file.mIsDirectory) {
                mTotalSize += file.mSize
            } else {
                checkRecycleLimitSize(file)
            }

            if ((mTotalSize >= SCAN_LIMIT_SIZE)) {
                Log.w(TAG, "checkRecycleLimitSize reach limit mTotalSize: $mTotalSize")
                return true
            }
        }
        return false
    }

    private fun checkIncludeExternalFileByPath(filePaths: ArrayList<String>?) {
        if (filePaths.isNullOrEmpty()) {
            Log.e(TAG, "checkIncludeExternalFileByPath files empty")
            return
        }
        var file: BaseFileBean?
        for (path in filePaths) {
            file = PathFileWrapper(path)
            if (mHasExternalFile.not()) {
                mHasExternalFile = DataRetriever.INSTANCE.isInternalPath(file.mData).not()
            } else {
                break
            }
        }
    }

    private fun checkIncludeExternalFileByFile(files: ArrayList<BaseFileBean>?) {
        if (files.isNullOrEmpty()) {
            Log.e(TAG, "checkIncludeExternalFileByFile files empty")
            return
        }
        for (file in files) {
            if (mHasExternalFile.not()) {
                mHasExternalFile = DataRetriever.INSTANCE.isInternalPath(file.mData).not()
            } else {
                break
            }
        }

    }
}

/**
 * ScanOperationParameters of recycler operation
 * @param mTotalSize The total size of the all selected file
 * @param mHasExternalFile true if contain external file in selected files
 */
internal class ScanParameters(uris: ArrayList<Uri>?, paths: ArrayList<String>?, fileBeans: ArrayList<BaseFileBean>?,
                              val mTotalSize: Long, val mHasExternalFile: Boolean = false)
    : BaseOperation.OperationParameters(uris, paths, fileBeans)
