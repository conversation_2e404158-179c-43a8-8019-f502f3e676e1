package com.oplus.filemanager.exportdmp

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.dmpsearch.IExportDmpSearchApi
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runBlockingTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.util.function.Consumer
import kotlin.test.assertFalse

/**
 * ExportAppSwitchApi的单元测试类
 * 使用Robolectric和MockK框架进行测试
 * 包含对ExportAppSwitchApi各个方法的测试用例
 */
@ExperimentalCoroutinesApi
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class ExportAppSwitchApiTest {

    // 测试用的协程调度器
    private val testDispatcher = TestCoroutineDispatcher()

    /**
     * 测试前置方法
     * 设置主调度器为测试调度器
     */
    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
    }

    /**
     * 测试后置方法
     * 重置主调度器并清理测试资源
     */
    @After
    fun tearDown() {
        Dispatchers.resetMain()
        testDispatcher.cleanupTestCoroutines()
        unmockkAll()  // 解除所有mock对象
        // 清理可能存在的静态状态
        ExportAppSwitchApi.setAppListConfigReadyCallback(null)
    }

    /**
     * 测试isCurrentAppSwitchConfigFeatureOn方法
     * 验证该方法始终返回false
     */
    @Test
    fun testIsCurrentAppSwitchConfigFeatureOn() {
        val context = mockk<Context>()  // 创建Context的mock对象
        assertFalse(ExportAppSwitchApi.isCurrentAppSwitchConfigFeatureOn(context))
    }

    /**
     * 测试isCurrentThirdAppListenConfigFeatureOn方法
     * 验证该方法始终返回false
     */
    @Test
    fun testIsCurrentThirdAppListenConfigFeatureOn() {
        assertFalse(ExportAppSwitchApi.isCurrentThirdAppListenConfigFeatureOn())
    }

    /**
     * 测试updateThirdAppListenConfig方法
     * 主要验证方法调用不会抛出异常
     */
    @Test
    fun testUpdateThirdAppListenConfig() {
        // 无返回值方法，主要验证不抛异常
        ExportAppSwitchApi.updateThirdAppListenConfig("test_config")
    }

    /**
     * 测试initDmpAndAppSwitch方法
     * 验证初始化DMP和应用开关的逻辑
     */
    @Test
    fun testInitDmpAndAppSwitch() = runBlockingTest {
        // 模拟静态对象
        mockkObject(MyApplication)
        mockkObject(Injector)

        // 创建IExportDmpSearchApi的mock对象
        val mockDmpSearchApi = mockk<IExportDmpSearchApi>(relaxed = true)
        // 设置Injector.injectFactory的模拟行为
        every { Injector.injectFactory<IExportDmpSearchApi>() } returns mockDmpSearchApi
        // 设置MyApplication.appContext的模拟行为
        every { MyApplication.appContext } returns mockk(relaxed = true)

        // 调用测试方法
        ExportAppSwitchApi.initDmpAndAppSwitch(true)

        // 验证是否调用了initDmpSdk方法
        verify { mockDmpSearchApi.initDmpSdk(any()) }
    }

    /**
     * 测试trigDbSync方法
     * 主要验证方法调用不会抛出异常
     */
    @Test
    fun testTrigDbSync() {
        // 无返回值方法，主要验证不抛异常
        ExportAppSwitchApi.trigDbSync(true)
    }

    /**
     * 测试getAppListConfigReady方法
     * 验证该方法始终返回false
     */
    @Test
    fun testGetAppListConfigReady() {
        assertFalse(ExportAppSwitchApi.getAppListConfigReady())
    }

    /**
     * 测试setAppListConfigReadyCallback方法
     * 验证回调设置功能
     */
    @Test
    fun testSetAppListConfigReadyCallback() {
        // 使用mock对象并确保测试后清理
        val mockCallback = mockk<Consumer<Boolean>>(relaxed = true)
        ExportAppSwitchApi.setAppListConfigReadyCallback(mockCallback)
    }

    /**
     * 测试initDmp方法
     * 验证DMP初始化的核心逻辑
     */
    @Test
    fun testInitDmp() = runBlockingTest {
        // 模拟静态对象
        mockkObject(MyApplication)
        mockkObject(Injector)

        // 创建IExportDmpSearchApi的mock对象
        val mockDmpSearchApi = mockk<IExportDmpSearchApi>(relaxed = true)
        // 设置Injector.injectFactory的模拟行为
        every { Injector.injectFactory<IExportDmpSearchApi>() } returns mockDmpSearchApi
        // 设置MyApplication.appContext的模拟行为
        every { MyApplication.appContext } returns mockk(relaxed = true)

        // 调用测试方法
        ExportAppSwitchApi.initDmp()

        // 验证是否调用了initDmpSdk方法
        verify { mockDmpSearchApi.initDmpSdk(any()) }
    }
}