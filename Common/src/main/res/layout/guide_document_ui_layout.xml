<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guide_center"
        app:layout_constraintBottom_toTopOf="@+id/guide_center"
        android:orientation="vertical">
        <com.oplus.anim.EffectiveAnimationView
            android:id="@+id/guide_icon"
            android:layout_width="@dimen/empty_content_img_width"
            android:layout_height="@dimen/empty_content_img_height"
            android:forceDarkAllowed="false"
            />
        <TextView
            android:id="@+id/guide_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/not_accessible"
            android:maxWidth="@dimen/empty_content_img_width"
            android:textAppearance="?attr/couiTextHeadlineXS"
            android:textColor="?attr/couiColorLabelPrimary"  />
        <TextView
            android:id="@+id/guide_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_2dp"
            android:gravity="center"
            android:text="@string/not_accessible_info"
            android:maxWidth="@dimen/empty_content_img_width"
            android:textAppearance="?attr/couiTextBodyXS"
            android:textColor="?attr/couiColorLabelSecondary" />
        <TextView
            android:id="@+id/guide_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:text="@string/btn_view"
            android:fontFamily="sans-serif-medium"
            android:textAppearance="?attr/couiTextButtonM"
            android:textColor="?attr/couiColorPrimaryText"/>
    </LinearLayout>

    <TextView
        android:id="@+id/go_documents_ui_tips_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:gravity="center"
        android:maxWidth="@dimen/empty_content_img_width"
        android:textAppearance="?attr/couiTextBodyXS"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/dimen_32dp"
        android:textColor="?attr/couiColorLabelSecondary"
        android:visibility="gone"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_center"
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_percent="0.45"
        />
</androidx.constraintlayout.widget.ConstraintLayout>