/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : IViewExactProcess.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.process

import android.util.Log
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date

interface IViewExactProcess {

    fun processDataBeanByViewExtraString(
        viewExactInfo: ViewExactInfo,
        inputDataBean: KeyDataBean
    ): List<KeyDataBean>

    data class ViewExactInfo(
        val viewExtraString: String,
        val viewExactActivity: String,
        val previewActivity: String,
        val packageName: String,
        val packageVersion: Int
    )

    data class KeyDataBean(
        var uid: String = "",
        var fileName: String = "",
        var fileSize: SizeValues? = null,
        var sourceType: Int = -1,
        var sourceName: String = "",
        var fileTime: TimeValues? = null,
        var baseFileTime: TimeValues? = null,
        var deltaTime: TimeValues? = null,
        var dbId: Int? = null
    ) {
        companion object {
            const val TAG = "KeyDataBean"
        }

        fun isFromDb(): Boolean {
            return dbId != null
        }

        fun checkDataValidate(): Boolean {
            return fileName.isNotEmpty() && sourceType != -1
        }

        fun getFileSizeLong(): Long {
            return fileSize?.getSizeForSizeValues() ?: -1
        }

        fun getTimeLong(): Long {
            var resultLong = -1L
            val innerFileTime = fileTime
            val innerBaseTime = baseFileTime
            val innerDeltaTime = deltaTime
            if (innerFileTime != null) {
                resultLong = innerFileTime.getTimeStamp()
            } else if (innerBaseTime != null && innerDeltaTime != null) {
                resultLong = innerBaseTime.plusDeltaTimeValues(innerDeltaTime)
            }
            Log.i(TAG, "getTimeLong fileTime $fileTime, baseFileTime $baseFileTime, deltaTime $deltaTime, result $resultLong")
            return resultLong
        }
    }

    data class TimeValues(
        val yearValue: Int,
        val monthValue: Int,
        val dayValue: Int,
        val dayAjustValue: Int,
        val hourValue: Int,
        val minuteValue: Int,
        val secondValue: Int
    ) {
        companion object {
            private const val TAG = "TimeValues"
        }
        fun getTimeStamp(): Long {
            val calendar = getCalendarForTimeValues()
            val timeStamp = calendar.timeInMillis
            val dateFormatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            val date = Date(timeStamp)
            val timeStampString = dateFormatter.format(date)
            Log.i(TAG, "getTimeStamp timeValues: $this, timeStamp $timeStamp, timeString: $timeStampString")
            return timeStamp
        }

        private fun getCalendarForTimeValues(): Calendar {
            val calendar = Calendar.getInstance()
            if (yearValue != -1) {
                calendar.set(Calendar.YEAR, yearValue)
            }
            if (monthValue != -1) {
                calendar.set(Calendar.MONTH, monthValue - 1)
            }
            if (dayValue != -1) {
                calendar.set(Calendar.DAY_OF_MONTH, dayValue)
            }
            if (dayAjustValue != 0) {
                calendar.add(Calendar.DAY_OF_MONTH, dayAjustValue)
            }
            if (hourValue != -1) {
                calendar.set(Calendar.HOUR_OF_DAY, hourValue)
            } else {
                calendar.set(Calendar.HOUR_OF_DAY, 0)
            }
            if (minuteValue != -1) {
                calendar.set(Calendar.MINUTE, minuteValue)
            } else {
                calendar.set(Calendar.MINUTE, 0)
            }
            if (secondValue != -1) {
                calendar.set(Calendar.SECOND, secondValue)
            } else {
                calendar.set(Calendar.SECOND, 0)
            }
            return calendar
        }

        fun plusDeltaTimeValues(deltaTime: TimeValues): Long {
            val calendar = getCalendarForTimeValues()
            deltaCalendarByTimeValues(calendar, deltaTime)
            val timeStamp = calendar.timeInMillis
            val dateFormatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            val date = Date(timeStamp)
            val timeStampString = dateFormatter.format(date)
            Log.i(TAG, "plusDeltaTimeValues timeValues: $this, deltaTime $deltaTime, timeStamp $timeStamp, timeString: $timeStampString")
            return timeStamp
        }

        private fun deltaCalendarByTimeValues(calendar: Calendar, deltaTime: TimeValues) {
            if (deltaTime.yearValue != -1) {
                calendar.add(Calendar.YEAR, deltaTime.yearValue)
            }
            if (deltaTime.monthValue != -1) {
                calendar.add(Calendar.MONTH, deltaTime.monthValue - 1)
            }
            if (deltaTime.dayValue != -1) {
                calendar.add(Calendar.DAY_OF_MONTH, deltaTime.dayValue)
            }
            if (deltaTime.hourValue != -1) {
                calendar.add(Calendar.HOUR_OF_DAY, deltaTime.hourValue)
            }
            if (deltaTime.minuteValue != -1) {
                calendar.add(Calendar.MINUTE, deltaTime.minuteValue)
            }
            if (deltaTime.secondValue != -1) {
                calendar.add(Calendar.SECOND, deltaTime.secondValue)
            }
        }
    }

    data class SizeValues(
        val unitValue: Int,
        val unitIntValue: Int,
        val unitDecimalValue: Int,
        val decimalStringLength: Int
    ) {
        companion object {
            private const val TAG = "SizeValues"
            private const val DECIMAL_UNIT = 10F
        }

        fun getSizeForSizeValues(): Long {
            var outputResult = -1L
            if (unitIntValue != -1) {
                outputResult = (unitIntValue * unitValue).toLong()
            }
            if (unitDecimalValue != -1 && decimalStringLength != 0) {
                outputResult += (unitDecimalValue / (DECIMAL_UNIT * decimalStringLength)).toLong()
            }
            Log.i(TAG, "getSizeLongForSizeValues inputValues $this, outputResult $outputResult")
            return outputResult
        }
    }
}