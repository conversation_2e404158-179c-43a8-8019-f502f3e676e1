/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudFileActionRename
 ** Description : 云端文件重命名的Action
 ** Version     : 1.0
 ** Date        : 2024/01/04 10:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/01/04       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.operate.rename

import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.DriveCache
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.rename.FileActionRename
import com.filemanager.fileoperate.rename.FileRenameObserver
import com.oplus.filemanager.drivebrowser.data.model.BaseResponse
import com.oplus.filemanager.drivebrowser.data.repository.kdocs.KDocsFileDriveRepository
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.drivebrowser.ui.CloudDocsItem
import com.oplus.filemanager.provider.DriveFileDBHelper
import com.oplus.filemanager.room.model.DriveFileEntity
import kotlinx.coroutines.runBlocking

class CloudFileActionRename(
    lifecycle: LifecycleOwner,
    val cloudFile: CloudDocsItem,
    private val networkService: FileDriveRepository,
    private val dbResultIgnore: Boolean = false
) : FileActionRename(lifecycle, cloudFile) {

    companion object {
        private const val TAG = "CloudFileActionRename"
    }

    private var response: BaseResponse<String>? = null

    override fun isFileExist(file: BaseFileBean): Boolean {
        return true
    }

    override fun initFileRenameBean() {
        super.initFileRenameBean()
        mFileRenameBean?.type = FileRenameObserver.FileRenameBean.TYPE_RENAME_CLOUD_FILE
    }

    override fun reallyExecuteAction(file: BaseFileBean, newFileName: String): Boolean {
        Log.d(TAG, "reallyExecuteAction newName:$newFileName dir:${file.mIsDirectory} file:$file, dbResultIgnore $dbResultIgnore")
        if (file.mDisplayName == newFileName) { //没有重命名
            return true
        }
        val formatNewFileName = getNewFileName(file, newFileName)
        val result = runBlocking {
            // 更新网络数据
            val networkResult = if (cloudFile.mIsDirectory) {
                networkService.renameFolder(cloudFile.id, formatNewFileName)
            } else {
                networkService.renameDocument(cloudFile.id, formatNewFileName)
            }
            var dbResult = false
            val success = networkResult.code == 0
            if (success) {
                // 更新数据库
                dbResult = DriveFileDBHelper.updateDocumentName(cloudFile.id, formatNewFileName)
            } else {
                val msg = networkResult.msg
                if (msg != null) {
                    statisticsRenameFail(msg)
                }
            }
            response = networkResult
            Log.d(TAG, "reallyExecuteAction result -> network:$networkResult db:$dbResult")
            if (dbResultIgnore) {
                success
            } else {
                success && dbResult
            }
        }
        return result
    }

    @VisibleForTesting
    fun getNewFileName(file: BaseFileBean, newFileName: String): String {
        if (cloudFile.mIsDirectory) {
            return newFileName
        }
        return if (networkService is KDocsFileDriveRepository) {
            CloudDocsItem.getFullTitle(newFileName, file.mMimeType)
        } else {
            newFileName
        }
    }

    /**
     * 统计重命名文件失败
     */
    @VisibleForTesting
    fun statisticsRenameFail(msg: String) {
        val fileSource = if (networkService is TencentFileDriveRepository) {
            DriveFileEntity.SOURCE_TYPE_TENCENT
        } else {
            DriveFileEntity.SOURCE_TYPE_KINGSSOFT
        }
        val map = hashMapOf<String, String>()
        map[StatisticsUtils.FILE_SOURCE] = fileSource
        map[StatisticsUtils.ERROR] = msg
        StatisticsUtils.onCommon(appContext, StatisticsUtils.RENAME_DRIVE_FILE_FAIL, map)
    }

    override fun afterRun(result: Boolean) {
        Log.d(TAG, "afterRun result:$result, response = $response")
        if (result) {
            //搜索+主页中云文档 重命名都走这里
            DriveCache.putRenameCache(cloudFile.id, cloudFile.title, mInputFileName ?: "")
            notifyObserver(ACTION_DONE, mInputFileName)
        } else {
            val obj = Pair(cloudFile.mIsDirectory, response)
            notifyObserver(ACTION_FAILED, obj)
        }
    }
}