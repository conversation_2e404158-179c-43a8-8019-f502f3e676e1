<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <group>
    <clip-path
        android:pathData="M0,0h32v32h-32z"/>
    <group>
      <clip-path
          android:pathData="M16,16m-16,0a16,16 0,1 1,32 0a16,16 0,1 1,-32 0"/>
      <path
          android:pathData="M16,16m-16,0a16,16 0,1 1,32 0a16,16 0,1 1,-32 0"
          android:fillColor="#ffffff"/>
      <path
          android:pathData="M28,4H4V28H28V4Z"
          android:fillColor="#ffffff"/>
      <path
          android:pathData="M12.161,18.319L16.095,20.814V25.844L7.848,21.107C7.758,21.055 7.727,20.941 7.779,20.851C7.795,20.822 7.819,20.798 7.848,20.781L12.161,18.319Z"
          android:fillColor="#0062FF"/>
      <group>
        <clip-path
            android:pathData="M12.16,18.319L16.094,20.814V25.844L7.847,21.107C7.757,21.055 7.726,20.941 7.778,20.851C7.794,20.822 7.819,20.798 7.847,20.781L12.16,18.319Z"/>
        <path
            android:pathData="M16.095,25.883V20.875L11.87,18.476L7.591,20.957L16.095,25.883Z"
            android:strokeAlpha="0.6"
            android:fillType="evenOdd"
            android:fillAlpha="0.6">
          <aapt:attr name="android:fillColor">
            <gradient 
                android:startX="856.828"
                android:startY="295.825"
                android:endX="653.928"
                android:endY="588.156"
                android:type="linear">
              <item android:offset="0" android:color="#FF0044B1"/>
              <item android:offset="1" android:color="#000062FF"/>
            </gradient>
          </aapt:attr>
        </path>
      </group>
      <path
          android:pathData="M24.719,15.953V20.969L16.095,25.844V20.931L24.719,15.953Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient 
              android:startX="119.511"
              android:startY="749.055"
              android:endX="592.807"
              android:endY="452.46"
              android:type="linear">
            <item android:offset="0" android:color="#FF1A4ACC"/>
            <item android:offset="1" android:color="#FF0854E4"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M24.72,20.969V11.031L20.407,12.783V18.455L24.72,20.969Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient 
              android:startX="236.032"
              android:startY="867.307"
              android:endX="236.032"
              android:endY="134.667"
              android:type="linear">
            <item android:offset="0" android:color="#FF0A5DF6"/>
            <item android:offset="1" android:color="#FF124CDC"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M11.964,13.497L16.097,15.62V20.965L7.759,16.165C7.669,16.113 7.638,15.999 7.69,15.909C7.707,15.88 7.732,15.855 7.761,15.839L11.964,13.497Z"
          android:fillColor="#0082FF"/>
      <group>
        <clip-path
            android:pathData="M11.965,13.497L16.098,15.62V20.965L7.76,16.165C7.67,16.113 7.639,15.999 7.691,15.909C7.708,15.88 7.733,15.855 7.762,15.839L11.965,13.497Z"/>
        <path
            android:pathData="M16.098,20.875V15.906L11.873,13.497L8.014,15.734C7.88,15.812 7.834,15.984 7.912,16.118C7.937,16.161 7.972,16.196 8.015,16.221L16.098,20.875Z"
            android:strokeAlpha="0.600679"
            android:fillType="evenOdd"
            android:fillAlpha="0.600679">
          <aapt:attr name="android:fillColor">
            <gradient 
                android:startX="830.231"
                android:startY="289.187"
                android:endX="624.918"
                android:endY="574.113"
                android:type="linear">
              <item android:offset="0" android:color="#FF0044B1"/>
              <item android:offset="1" android:color="#000062FF"/>
            </gradient>
          </aapt:attr>
        </path>
      </group>
      <path
          android:pathData="M15.922,6.172L7.859,10.852C7.769,10.904 7.739,11.019 7.791,11.108C7.808,11.137 7.831,11.16 7.86,11.177L16.095,15.907L24.72,11.032L16.297,6.171C16.181,6.104 16.038,6.104 15.922,6.172Z"
          android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
          <gradient 
              android:startX="855.484"
              android:startY="6.121"
              android:endX="855.484"
              android:endY="984.668"
              android:type="linear">
            <item android:offset="0" android:color="#FF4AC3FF"/>
            <item android:offset="1" android:color="#FF0E93F8"/>
          </gradient>
        </aapt:attr>
      </path>
      <path
          android:pathData="M16,16m-15.835,0a15.835,15.835 0,1 1,31.67 0a15.835,15.835 0,1 1,-31.67 0"
          android:strokeAlpha="0.12"
          android:strokeWidth="0.33"
          android:fillColor="#00000000"
          android:strokeColor="#000000"/>
    </group>
  </group>
</vector>
