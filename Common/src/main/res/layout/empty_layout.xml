<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/empty_view_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:viewBindingIgnore="true"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@+id/guide_center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guide_center">

        <LinearLayout
            android:id="@+id/empty_content_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.oplus.anim.EffectiveAnimationView
                android:id="@+id/empty_eav"
                android:layout_width="@dimen/empty_content_img_width"
                android:layout_height="@dimen/empty_content_img_height"
                android:layout_gravity="center"
                android:forceDarkAllowed="false" />

            <TextView
                android:id="@+id/emptybottle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingHorizontal="@dimen/default_margin"
                android:textAppearance="?attr/couiTextHeadlineXS"
                android:textColor="?attr/couiColorLabelPrimary" />
            <TextView
                android:id="@+id/empty_des_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_2dp"
                android:gravity="center"
                android:paddingHorizontal="@dimen/default_margin"
                android:textAppearance="?attr/couiTextBodyXS"
                android:textColor="?attr/couiColorLabelSecondary"
                android:visibility="gone" />

            <TextView
                android:id="@+id/guide_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dimen_8dp"
                android:fontFamily="sans-serif-medium"
                android:clickable="true"
                android:gravity="center"
                android:textAppearance="?attr/couiTextButtonM"
                android:textColor="?attr/couiColorContainerTheme"
                android:visibility="gone" />
        </LinearLayout>
    </ScrollView>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.45" />

</androidx.constraintlayout.widget.ConstraintLayout>