/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowRecentDataHelper.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/9/17
 ** Author: JiaBo Lan
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/9/17      1.0     create
 ****************************************************************/
package shadow;

import com.oplus.filemanager.recent.entity.recent.FileFilterEntity;
import com.oplus.filemanager.recent.utils.RecentDataHelper;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.util.List;

@Implements(RecentDataHelper.class)
public class ShadowRecentDataHelper {
    @Implementation
    public void addFileFilterEntities(List<FileFilterEntity> fileFilterEntities) {

    }
}
