/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CardWidgetThumbnailGenerator.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/13
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/13      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.util

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Shader
import android.media.ThumbnailUtils
import android.net.Uri
import android.os.CancellationSignal
import android.util.Size
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.imageloader.glide.RoundRectUtil
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.AppInfo
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.doc.DocThumbnail
import com.filemanager.thumbnail.doc.DocThumbnailLoaderFactory
import com.filemanager.thumbnail.doc.IDocThumbnailCallback
import com.oplus.filemanager.cardwidget.R
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import kotlin.math.min

@Suppress("TooGenericExceptionCaught")
object CardWidgetThumbnailGenerator {

    private const val TAG = "CardWidgetThumbnailGenerator"
    private const val FOLDER_NAME = "recent_card_widget"
    private const val SIZE = 200
    private const val TABLE_SIZE = 200
    private const val QUALITY_100 = 100
    private const val VIDEO_THUMBNAIL_TIMEOUT = 600L
    private var radius =
        appContext.resources.getDimensionPixelSize(R.dimen.card_widget_thumbnail_corner_radius)

    /**
     * /data/user/0/com.xxx.filemanager/files/recent_card_widget
     * **/
    private var FOLDER = appContext.filesDir.absolutePath + File.separator + FOLDER_NAME

    /**
     * 文档固定高宽
     */
    private val docWidth = appContext.resources.getDimensionPixelSize(R.dimen.label_card_icon_size)
    private val docHeight = appContext.resources.getDimensionPixelSize(R.dimen.label_card_icon_size)

    private val imgBorderStrokeWith = appContext.resources.getDimensionPixelSize(R.dimen.card_img_border_stroke_with)
    private val imgBorderColor = appContext.getColor(R.color.card_img_border_color)
    private val thumbnailSize = if (ModelUtils.isTablet()) TABLE_SIZE else SIZE
    private val cardImgWith = appContext.resources.getDimensionPixelSize(R.dimen.card_img_width)
    private val cardImgHeight = appContext.resources.getDimensionPixelSize(R.dimen.card_img_height)

    @JvmStatic
    fun generate(type: Int, path: String, modifiedTime: Long, size: Long) {
        try {
            if (!isSupportThumbnailForCardWidget(type)) {
                return
            }
            val thumbnailPath = ruleForThumbnailFileName(path, modifiedTime, size)
            if (File(thumbnailPath).exists()) {
                return
            }
            if (ThumbnailManager.isDocThumbnailSupported(appContext) && MimeTypeHelper.isDocType(type)) {
                createDocTypeThumbnail(path, modifiedTime, size, thumbnailPath)
            } else {
                var bitmap = createImageThumbnail(type, path)
                bitmap = bitmap?.let { centerCorp(it) }
                val borderStrokeWith = if (type == MimeTypeHelper.APPLICATION_TYPE) 0f else imgBorderStrokeWith.toFloat()
                bitmap = bitmap?.let { addSmoothRoundCorner(it, borderStrokeWith) }
                bitmap?.let { saveBitmapToFile(thumbnailPath, it) }
            }
        } catch (e: Exception) {
            Log.e(TAG, "generate Exception : ${e.message}")
        }
    }

    private fun centerCorp(srcBmp: Bitmap): Bitmap {
        val dstBmp: Bitmap?
        if (srcBmp.width >= srcBmp.height) {
            dstBmp = Bitmap.createBitmap(
                srcBmp,
                srcBmp.width / 2 - srcBmp.height / 2,
                0,
                srcBmp.height,
                srcBmp.height
            )
        } else {
            dstBmp = Bitmap.createBitmap(
                srcBmp,
                0,
                srcBmp.height / 2 - srcBmp.width / 2,
                srcBmp.width,
                srcBmp.width
            )
        }
        return dstBmp
    }

    //https://developer.android.com/reference/android/media/ThumbnailUtils
    private fun createImageThumbnail(type: Int, path: String): Bitmap? {
        return when (type) {
            MimeTypeHelper.IMAGE_TYPE -> ThumbnailUtils.createImageThumbnail(File(path), Size(thumbnailSize, thumbnailSize), CancellationSignal())

            MimeTypeHelper.VIDEO_TYPE -> createVideoThumbnailWithTimeout(path)

            MimeTypeHelper.AUDIO_TYPE -> ThumbnailUtils.createAudioThumbnail(File(path), Size(thumbnailSize, thumbnailSize), CancellationSignal())

            MimeTypeHelper.APPLICATION_TYPE -> getIconFromApk(File(path))

            else -> null
        }
    }

    private fun createVideoThumbnailWithTimeout(path: String): Bitmap? {
        var thumbnail: Bitmap? = null
        val countDownLatch = CountDownLatch(1)
        ThreadManager.sThreadManager.execute(FileRunnable({
            Log.d(TAG, "createVideoThumbnailWithTimeout run")
            kotlin.runCatching {
                thumbnail = ThumbnailUtils.createVideoThumbnail(File(path), Size(thumbnailSize, thumbnailSize), CancellationSignal())
            }.onFailure {
                Log.d(TAG, "createVideoThumbnailWithTimeout failed = ${it.message}")
            }
            countDownLatch.countDown()
        }, TAG))
        try {
            countDownLatch.await(VIDEO_THUMBNAIL_TIMEOUT, TimeUnit.MILLISECONDS)
        } catch (e: InterruptedException) {
            Log.d(TAG, "createVideoThumbnailWithTimeout -> await error = ${e.message}")
        }
        Log.d(TAG, "createVideoThumbnailWithTimeout thumbnail = $thumbnail")
        return thumbnail
    }

    private fun getIconFromApk(file: File): Bitmap? {
        var appInfo: AppInfo? = null
        try {
            appInfo = AppUtils.getAppInfoByPath(appContext, file.absolutePath)
        } catch (e: Exception) {
            Log.e(TAG, "getIconFromApk error " + e.message)
        }
        return appInfo?.icon
    }

    private fun saveBitmapToFile(thumbnailPath: String, bitmap: Bitmap) {
        val folder = File(FOLDER)
        if (!folder.exists()) {
            folder.mkdir()
        }
        val file = File(thumbnailPath)
        if (!file.exists()) {
            file.createNewFile()
        }
        try {
            FileOutputStream(thumbnailPath).use { out ->
                bitmap.compress(Bitmap.CompressFormat.PNG, QUALITY_100, out)
            }
        } catch (e: IOException) {
            Log.e(TAG, "saveBitmapToFile error " + e.message)
        }
    }

    private fun addSmoothRoundCorner(thumbnail: Bitmap, borderStrokeWith: Float = 0f): Bitmap {
        val source: Bitmap = createTargetWidthHeightBitmap(thumbnail, cardImgWith, cardImgHeight)
        val result: Bitmap = Bitmap.createBitmap(source.width, source.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(result)
        val paint = Paint()
        paint.shader = BitmapShader(source, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
        paint.isAntiAlias = true
        drawSmoothRoundCorners(canvas, source, paint)
        if (borderStrokeWith > 0f) {
            val borderPaint = Paint()
            borderPaint.isAntiAlias = true
            borderPaint.color = imgBorderColor
            borderPaint.strokeWidth = borderStrokeWith
            borderPaint.style = Paint.Style.STROKE
            drawRoundCornersBorder(canvas, source, borderStrokeWith / 2, borderPaint)
        }
        return result
    }

    private fun createTargetWidthHeightBitmap(
        originalBitmap: Bitmap,
        targetWidth: Int,
        targetHeight: Int
    ): Bitmap {
        val originalWidth = originalBitmap.width
        val originalHeight = originalBitmap.height
        val scaleWidth = targetWidth.toFloat() / originalWidth
        val scaleHeight = targetHeight.toFloat() / originalHeight

        // 选择最小的缩放比例以保持等比缩放
        val scaleFactor = min(scaleWidth.toDouble(), scaleHeight.toDouble()).toFloat()
        // 计算新的宽度和高度
        val newWidth = (originalWidth * scaleFactor).toInt()
        val newHeight = (originalHeight * scaleFactor).toInt()
        // 创建新的缩放后的位图
        return Bitmap.createScaledBitmap(originalBitmap, newWidth, newHeight, true)
    }

    private fun drawSmoothRoundCorners(
        canvas: Canvas,
        source: Bitmap,
        paint: Paint
    ) {
        val save = canvas.save()
        val path = RoundRectUtil.getPath(
            0f,
            0f,
            source.width.toFloat(),
            source.height.toFloat(),
            radius.toFloat(),
            tl = true,
            tr = true,
            bl = true,
            br = true
        )
        canvas.drawPath(path, paint)
        canvas.restoreToCount(save)
    }

    private fun drawRoundCornersBorder(
        canvas: Canvas,
        source: Bitmap,
        strokeWith: Float,
        paint: Paint
    ) {
        val save = canvas.save()
        val path = RoundRectUtil.getPath(
            strokeWith,
            strokeWith,
            source.width.toFloat() - strokeWith,
            source.height.toFloat() - strokeWith,
            radius.toFloat(),
            tl = true,
            tr = true,
            bl = true,
            br = true
        )
        canvas.drawPath(path, paint)
        canvas.restoreToCount(save)
    }

    private fun createDocTypeThumbnail(
        path: String,
        modifiedTime: Long,
        size: Long,
        thumbnailPath: String
    ) {
        val docThumbnail = DocThumbnail(path, modifiedTime, size)
        DocThumbnailLoaderFactory.getInstance(appContext)?.loadThumbnail(
            docThumbnail,
            docWidth,
            docHeight,
            object : IDocThumbnailCallback {
                override fun onDataReady(uri: Uri) {
                    kotlin.runCatching {
                        appContext.contentResolver.openInputStream(uri)?.use { inputStream ->
                            BitmapFactory.decodeStream(inputStream)?.let {
                                saveBitmapToFile(thumbnailPath, addSmoothRoundCorner(it, imgBorderStrokeWith.toFloat()))
                            }
                        }
                    }.onFailure {
                        Log.d(TAG, "onDataReady: $it")
                    }
                }

                override fun onLoadFailed(e: Throwable) {
                    Log.e(TAG, "onLoadFailed: $e")
                }
            })
    }
}