/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AccessibilityActionJsonAdapterTest.kt
 * Description:
 * The test cases for AccessibilityActionJsonAdapter
 *
 * Version: 1.0
 * Date: 2024-06-06
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-06-06   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.value

import android.view.accessibility.AccessibilityNodeInfo
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonToken
import com.google.gson.stream.JsonWriter
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AccessibilityActionJsonAdapterTest : Assert() {

    /**
     * Mock [AccessibilityNodeInfo.AccessibilityAction.ACTION_IME_ENTER]
     */
    private lateinit var actionImeEnter: AccessibilityNodeInfo.AccessibilityAction

    @Before
    fun setUp() {
        mockkStatic(AccessibilityActionJsonAdapter::class)
        mockkObject(AccessibilityActionJsonAdapter)
        mockkStatic(::getImeEnterAction)
        actionImeEnter = mockk()
        every { getImeEnterAction() } returns actionImeEnter
        every { AccessibilityActionJsonAdapter.stringToAction } answers {
            AccessibilityActionJsonAdapter.initStringToAction()
        }
        every { AccessibilityActionJsonAdapter.actionToString } answers {
            AccessibilityActionJsonAdapter.initActionToString()
        }
    }

    @After
    fun tearDown() {
        unmockkStatic(AccessibilityActionJsonAdapter::class)
        unmockkObject(AccessibilityActionJsonAdapter)
        unmockkStatic(::getImeEnterAction)
    }

    @Test
    fun `should get ACTION_IME_ENTER when read if jsonStr is correct`() {
        // Given
        val adapter = AccessibilityActionJsonAdapter()
        val reader = mockk<JsonReader> {
            every { peek() } returns JsonToken.STRING
            every { nextString() } returns "ACTION_IME_ENTER"
        }

        // When
        val result = adapter.read(reader)

        // Then
        assertEquals(actionImeEnter, result)
    }

    @Test
    fun `should write ACTION_IME_ENTER when write if action is correct`() {
        // Given
        val adapter = AccessibilityActionJsonAdapter()
        val writer = mockk<JsonWriter>(relaxed = true)

        // When
        adapter.write(writer, actionImeEnter)

        // Then
        verify { writer.value("ACTION_IME_ENTER") }
    }
}