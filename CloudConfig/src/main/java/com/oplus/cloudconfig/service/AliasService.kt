/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AliasService.kt
 * * Description : CloudConfig AliasService
 * * Version     : 1.0
 * * Date        : 2020/9/7
 * * Author      : W9001165
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.cloudconfig.service

import com.oplus.nearx.cloudconfig.anotation.Config
import com.oplus.nearx.cloudconfig.anotation.Default
import com.oplus.nearx.cloudconfig.anotation.Key
import com.oplus.nearx.cloudconfig.observable.Observable
import com.oplus.nearx.cloudconfig.stat.Const
import com.oplus.cloudconfig.bean.AliasBean

@Config(configCode = "folder_alias", type = Const.CONFIG_TYPE_DATABASE)
interface AliasService {

    @Key(configId = "folder_alias", nonull = true)
    fun getId(@Default defaultId: String): String

    fun getAllAliases(): Observable<List<AliasBean>>
}