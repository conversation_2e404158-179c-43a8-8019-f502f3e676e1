<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/fop_dimen_12dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/name_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/fop_default_margin"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textAppearance="?attr/couiTextBodyXS"
        android:gravity="start" />

    <TextView
        android:id="@+id/value_tv"
        android:textAppearance="?attr/couiTextBodyM"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/fop_default_margin"
        android:layout_marginTop="@dimen/fop_detail_item_info_margin_top"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textDirection="locale"
        android:gravity="start" />

    <com.google.android.material.chip.ChipGroup
        android:id="@+id/detail_label_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/fop_default_margin"
        android:layout_marginTop="@dimen/dimen_10dp"
        android:visibility="gone"
        app:chipSpacingHorizontal="@dimen/dimen_8dp"
        app:chipSpacingVertical="@dimen/dimen_16dp" />
</LinearLayout>