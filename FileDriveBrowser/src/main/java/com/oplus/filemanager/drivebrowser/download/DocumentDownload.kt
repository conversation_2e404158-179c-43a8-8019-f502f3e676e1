/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DocumentDownload
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/9 15:15
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/9       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.download

import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.drivebrowser.data.repository.DocumentDownloadErrorCode
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.room.model.DriveFileEntity

class DocumentDownload(
    private val repository: FileDriveRepository
) {
    suspend fun download(downloadTaskInfo: DownloadTaskInfo, progressChange: (Int) -> Unit): DownloadResult {
        var success = true
        var errorCode = 0
        if (downloadTaskInfo.fileId.isEmpty()) {
            return DownloadResult(false)
        }
        var curProgress = 0
        progressChange.invoke(0)
        repository.downloadFile(
            downloadTaskInfo.fileId,
            downloadTaskInfo.targetPath,
            downloadTaskInfo.exportType,
            object : DownloadProgressListener {
                override fun onProgressChange(progress: Int) {
                    Log.d(TAG, "onProgressChange -> progress = $progress")
                    if (curProgress != progress && progress <= MAX_PROGRESS) {
                        curProgress = progress
                        progressChange.invoke(progress)
                    }
                }

                override fun onStop() {
                    Log.d(TAG, "onStop")
                    success = false
                }

                override fun onError(error: DocumentDownloadErrorCode) {
                    Log.d(TAG, "onError -> error = $error")
                    success = false
                    errorCode = error.value
                    statisticsDownloadFail(error.description)
                }
            })
        return DownloadResult(success, errorCode)
    }

    /**
     * 统计下载失败的原因
     */
    private fun statisticsDownloadFail(msg: String) {
        val fileSource = if (repository is TencentFileDriveRepository) {
            DriveFileEntity.SOURCE_TYPE_TENCENT
        } else {
            DriveFileEntity.SOURCE_TYPE_KINGSSOFT
        }
        val map = hashMapOf<String, String>()
        map[StatisticsUtils.FILE_SOURCE] = fileSource
        map[StatisticsUtils.ERROR] = msg
        map[StatisticsUtils.COUNT] = "1"
        StatisticsUtils.onCommon(MyApplication.sAppContext, StatisticsUtils.DOWNLOAD_DRIVE_FILE_FAIL, map)
    }

    fun cancelDownload() {
        repository.cancelDownload()
    }

    companion object {
        private const val TAG = "DocumentDownload"
        private const val MAX_PROGRESS = 100L
    }
}