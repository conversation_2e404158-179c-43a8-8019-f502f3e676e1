/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.compresspreview
 * * Version     : 1.0
 * * Date        : 2020/5/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.compresspreview.ui

import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.path.FilePathHelper
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PREVIEW_ROOT_PATH
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.decompress.BaseDecompressFile
import com.filemanager.fileoperate.decompress.FileActionDecompress
import com.filemanager.fileoperate.decompress.FileDecompressObserver
import com.filemanager.fileoperate.decompress.P7ZipDecompressHelper
import com.filemanager.fileoperate.open.FileActionOpen
import com.filemanager.fileoperate.open.FileOpenObserver
import com.filemanager.fileoperate.previewcompress.CompressPreviewCacheHelper
import com.filemanager.fileoperate.previewcompress.FileActionOpenCompressFile
import com.filemanager.fileoperate.previewcompress.FileOpenCompressObserver
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import java.io.File

class CompressPreviewViewModel : SelectionViewModel<BaseDecompressFile, CompressPreviewViewModel.CompressPreviewUiModel>() {
    companion object {
        private const val TAG = "CompressPreviewViewModel"
    }

    val mModeState = BaseStateModel(MutableLiveData<Int>(KtConstants.LIST_NORMAL_MODE))
    var mPathHelp: FilePathHelper? = null

    //In order to distinguish between entering and exiting folders because of the folder animation
    var mIsFolderIn = true
    var mNeedScroll = false
    val mPositionModel = MutableLiveData<PositionModel>()
    var mPreviewMap: Map<String, MutableList<out BaseDecompressFile>?>? = null
    var mCurrentPath: String? = null
    private var mAction: BaseFileAction<*>? = null

    override fun loadData() {
    }

    fun previewData(activity: BaseVMActivity?, currentPath: String, isNeedCheckFinish: Boolean) {
        mCurrentPath = currentPath
        if (mPreviewMap == null) {
            Log.d(TAG, "previewData mPreviewMap init")
            mPreviewMap = CompressPreviewCacheHelper.fetchTransferPreviewFile()
            //REQUEST_INSTALL_PACKAGES changed will kill FileManager process and mPreviewMap will not store(return null),so need to finish activity
            if (isNeedCheckFinish && (mPreviewMap == null)) {
                Log.d(TAG, "previewData mPreviewMap null finish")
                activity?.finish()
                return
            }
        }
        Log.d(TAG, "previewData mPreviewMap size ${mPreviewMap?.size}")
        val previewPath = mPathHelp?.getTopPathInfo()?.path ?: PREVIEW_ROOT_PATH
        listCompressFile(previewPath)
    }

    private fun sortList(list: MutableList<BaseDecompressFile>): ArrayList<BaseDecompressFile> {
        val normalList = ArrayList<BaseDecompressFile>()
        val foldList = ArrayList<BaseDecompressFile>()
        val afterList = ArrayList<BaseDecompressFile>()
        list.forEach {
            if (it.mIsDirectory) {
                foldList.add(it)
            } else {
                normalList.add(it)
            }
        }
        afterList.addAll(foldList)
        afterList.addAll(normalList)
        return afterList
    }

    private fun listCompressFile(path: String): Boolean {
        mPreviewMap?.let {
            var listPath = path
            if ((listPath != PREVIEW_ROOT_PATH) && listPath.startsWith(File.separator)) {
                listPath = listPath.substring(1)
            }
            val previewList = it[listPath] as MutableList<BaseDecompressFile>?
            if (mPositionModel.value == null) {
                mModeState.mInitState = true
                mPositionModel.value = PositionModel(path, 0, 0)
            } else {
                mPositionModel.value!!.mCurrentPath = path
            }
            val keyMap = hashMapOf<Int, BaseDecompressFile>()
            val dataList = mutableListOf<BaseDecompressFile>()
            previewList?.forEach { file ->
                if (!file.mData.isNullOrEmpty()) {
                    getItemKey(file)?.let { key ->
                        keyMap[key] = file
                        dataList.add(file)
                    }
                }
            }
            mUiState.postValue(CompressPreviewUiModel(
                    if (dataList.isEmpty()) dataList else sortList(dataList),
                    mModeState, ArrayList(), keyMap, mPositionModel))
            if (mNeedScroll) {
                mPositionModel.value = mPositionModel.value
                mNeedScroll = false
            }
            return true
        }
        return false
    }

    fun resetState() {
        changeListMode(KtConstants.LIST_NORMAL_MODE)
    }

    fun clickToolbarSelectAll() {
        if (mUiState.value?.mFileList?.size == mUiState.value?.mSelectedList?.size) {
            mUiState.value?.mSelectedList?.clear()
            mUiState.value = mUiState.value
        } else {
            mUiState.value?.mSelectedList?.clear()
            mUiState.value?.mFileList?.let {
                for (baseFileBean in it) {
                    getItemKey(baseFileBean)?.let { key -> mUiState.value?.mSelectedList?.add(key) }
                }
            }
            mUiState.value = mUiState.value
        }
    }

    private fun getItemKey(file: BaseDecompressFile?): Int? {
        if (file?.mData?.isNotEmpty() == true) {
            return file.mData.hashCode()
        }
        return null
    }

    fun clickPathBar(index: Int) {
        if (mModeState.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
            changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
        val pathLeft = mPathHelp?.getPathLeft() ?: index + 1
        val index = pathLeft - index - 1
        val pathInfo = mPathHelp?.setTopPath(index)
        Log.d(TAG, "clickPathBar pathLeft=$pathLeft,pathLeft=$index,pathInfo $pathInfo")
        pathInfo?.let {
            mIsFolderIn = false
            mNeedScroll = true
            listCompressFile(it.path)
        }
    }

    fun onItemClick(activity: BaseVMActivity?, key: Int, firstVisibleItemPosition: Int, offset: Int) {
        if (mUiState.value?.mStateModel?.mListModel?.value == KtConstants.LIST_SELECTED_MODE) {
            toggleSelectItem(key)
        } else {
            if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return
            }
            val baseFile = mUiState.value?.mKeyMap?.get(key)
            if ((activity != null) && (baseFile != null)) {
                if (baseFile.mIsDirectory) {
                    baseFile.mDisplayName?.let {
                        val pathString = (mPositionModel.value?.mCurrentPath
                                ?: "") + it + File.separator
                        mIsFolderIn = true
                        mNeedScroll = true
                        if (listCompressFile(pathString)) {
                            mPathHelp?.push(FilePathHelper.PathInfo(pathString, firstVisibleItemPosition, offset))
                        }
                    }
                } else {
                    if (mCurrentPath == null) {
                        Log.d(TAG, "onItemClick mCurrentPath null")
                        return
                    }
                    FileActionOpenCompressFile(activity, PathFileWrapper(mCurrentPath!!), true, baseFile).execute(object : FileOpenCompressObserver(activity) {
                        override fun onActionDone(result: Boolean, data: Any?) {
                            Log.d(TAG, "onActionDone result=$result")
                            if (result && (data is BaseFileBean)) {
                                FileActionOpen.Companion.Builder(activity, data)
                                        .build()
                                        .execute(FileOpenObserver(activity))
                            }
                        }
                    })
                }
            }
        }
    }

    private fun toggleSelectItem(key: Int) {
        if (mUiState.value?.mStateModel?.mListModel?.value == KtConstants.LIST_SELECTED_MODE) {
            if (mUiState.value?.mSelectedList != null) {
                if (mUiState.value?.mSelectedList!!.contains(key)) {
                    mUiState.value?.mSelectedList!!.remove(key)
                } else {
                    mUiState.value?.mSelectedList!!.add(key)
                }
            }
            mUiState.value = mUiState.value
        }
    }

    fun doDeCompress(mActivity: BaseVMActivity, sourcePath: String, path: String, fileName: String) {
        val isPrivatePath = KtUtils.isPrivateDataPath(mActivity, sourcePath)
        val decompressObserver = object : FileDecompressObserver(mActivity) {
            override fun onActionDone(result: Boolean, data: Any?) {
                if (isPrivatePath && mActivity is CompressPreviewActivity && data is String) {
                    P7ZipDecompressHelper.clearPassword()
                    val fileBrowser = Injector.injectFactory<IFileBrowser>()
                    fileBrowser?.toFileBrowserActivity(mActivity, data)
                    CustomToast.showShort(com.filemanager.common.R.string.decompress_success)
                    mActivity.transitionFinish(true)
                }
                changeListMode(KtConstants.LIST_NORMAL_MODE)
            }
        }
        if (isPrivatePath) {
            decompressObserver.mIsNeedSkipShowClickToast = true
        }
        mAction = FileActionDecompress(
            lifecycle = mActivity,
            sourceFile = PathFileWrapper(sourcePath),
            destParentFile = PathFileWrapper(path),
            needShowCheckButton = true,
            selectFiles = getSelectItems(),
            decompressFileName = fileName
        ).execute(decompressObserver)
    }

    fun pressBack(): Boolean {
        mModeState.let {
            if (it.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
                changeListMode(KtConstants.LIST_NORMAL_MODE)
                return true
            } else {
                if (mPathHelp?.pop() != null) {
                    val info = mPathHelp?.getTopPathInfo()
                    info?.path?.let { path ->
                        mIsFolderIn = false
                        mNeedScroll = true
                        mPositionModel.value?.mCurrentPath = path
                        mPositionModel.value?.mPosition = info.position
                        mPositionModel.value?.mOffset = info.y
                        if (listCompressFile(path)) {
                            return true
                        }
                    }
                } else {
                    Log.d(TAG, "pressBack pop null")
                }
                return false
            }
        }
        return false
    }

    fun onConfigurationChanged() {
        mAction?.reShowDialog()
    }

    class CompressPreviewUiModel : BaseUiModel<BaseDecompressFile> {
        private val mPositionModel: MutableLiveData<PositionModel>

        constructor(
                fileList: List<BaseDecompressFile>,
                stateModel: BaseStateModel,
                selectedList: ArrayList<Int>,
                keyMap: HashMap<Int, BaseDecompressFile>,
                positionModel: MutableLiveData<PositionModel>
        ) : super(fileList, stateModel, selectedList, keyMap) {
            mPositionModel = positionModel
        }
    }


    data class PositionModel(
            var mCurrentPath: String,
            var mPosition: Int,
            var mOffset: Int
    )

    override fun getRealFileSize(): Int {
        return mUiState.value?.mFileList?.size ?: 0
    }

    override fun getRecyclerViewScanMode(): com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE {
        return com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.LIST
    }
}