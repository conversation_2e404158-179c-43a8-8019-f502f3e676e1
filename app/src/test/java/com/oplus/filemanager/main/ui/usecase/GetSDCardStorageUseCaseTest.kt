/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GetSDCardStorageUseCaseTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/3/7 16:43
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/3/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.main.ui.usecase

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.main.ui.uistate.Storage
import io.mockk.MockKAnnotations
import io.mockk.mockk
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class GetSDCardStorageUseCaseTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
    }

    @Test
    fun `get sd card storage info when is single sd card`() = runBlocking {
        val getSDCardStorageUseCase = GetSDCardStorageUseCase()
        val storageItems = mutableListOf<Storage>()
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.isSingleSdcard(context) }.returns(true)
        getSDCardStorageUseCase.invoke(storageItems)
        Assert.assertEquals(1, storageItems.size)
        val storage = storageItems[0]
        Assert.assertTrue(storage is Storage.SDCardStorage)
        storage as Storage.SDCardStorage
        Assert.assertEquals(0, storage.sdCardTotalSize)
        Assert.assertEquals(0, storage.sdCardAvailableSize)
        Assert.assertEquals(KtConstants.STATE_UNMOUNTED, storage.state)
    }

    @Test
    fun `get sd card storage info when don't have external sd`() = runBlocking {
        val getSDCardStorageUseCase = GetSDCardStorageUseCase()
        val storageItems = mutableListOf<Storage>()
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.isSingleSdcard(context) }.returns(false)
        every { VolumeEnvironment.isExternalSdMounted(context) }.returns(false)
        getSDCardStorageUseCase.invoke(storageItems)
        Assert.assertEquals(1, storageItems.size)
        val storage = storageItems[0]
        Assert.assertTrue(storage is Storage.SDCardStorage)
        storage as Storage.SDCardStorage
        Assert.assertEquals(0, storage.sdCardTotalSize)
        Assert.assertEquals(0, storage.sdCardAvailableSize)
        Assert.assertEquals(KtConstants.STATE_UNMOUNTED, storage.state)
    }

    @Test
    fun `get sd card storage info when have external sd`() = runBlocking {
        val getSDCardStorageUseCase = GetSDCardStorageUseCase()
        val storageItems = mutableListOf<Storage>()
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.isSingleSdcard(context) }.returns(false)
        every { VolumeEnvironment.isExternalSdMounted(context) }.returns(true)
        every { VolumeEnvironment.getExternalSdPath(context) }.returns(EXTERNAL_PATH)
        mockkStatic(Utils::class)
        every { Utils.getStorageTotalSize(any()) }.returns(TOTAL_SIZE)
        every { Utils.getStorageAvailableSize(any()) }.returns(AVAILABLE_SIZE)
        getSDCardStorageUseCase.invoke(storageItems)
        Assert.assertEquals(1, storageItems.size)
        val storage = storageItems[0]
        Assert.assertTrue(storage is Storage.SDCardStorage)
        storage as Storage.SDCardStorage
        Assert.assertEquals(TOTAL_SIZE, storage.sdCardTotalSize)
        Assert.assertEquals(AVAILABLE_SIZE, storage.sdCardAvailableSize)
        Assert.assertEquals(KtConstants.STATE_MOUNTED, storage.state)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    companion object {
        private const val EXTERNAL_PATH = "/test/InternalSdPath"
        private const val TOTAL_SIZE = 1000L
        private const val AVAILABLE_SIZE = 400L
    }
}