<?xml version="1.0" encoding="utf-8"?>
<com.oplus.filemanager.recent.view.MainRecentEmptyLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/empty_view_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guide_center"
        app:layout_constraintBottom_toTopOf="@+id/guide_center"
        android:gravity="center"
        android:orientation="vertical"
        >
        <com.oplus.anim.EffectiveAnimationView
            android:id="@+id/empty_iv"
            android:layout_width="@dimen/empty_content_img_width"
            android:layout_height="@dimen/empty_content_img_height"
            android:forceDarkAllowed="false"
            app:anim_autoPlay="true" />

        <TextView
            android:id="@+id/emptybottle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_40dp"
            android:text="@string/empty_file"
            android:maxWidth="@dimen/empty_content_img_width"
            android:textAppearance="?attr/couiTextHeadlineXS"
            android:textColor="?attr/couiColorLabelPrimary" />
        <TextView
            android:id="@+id/empty_bottom_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="@dimen/empty_content_img_width"
            android:layout_marginHorizontal="@dimen/dimen_40dp"
            android:layout_marginTop="@dimen/dimen_2dp"
            android:lineSpacingMultiplier="1.2"
            android:text="@string/display_file_in_recent_thirty_days"
            android:textAppearance="?attr/couiTextBodyXS"
            android:textColor="?attr/couiColorLabelSecondary" />
    </LinearLayout>
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guide_center"
        android:orientation="horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_percent="0.38"
        />

</com.oplus.filemanager.recent.view.MainRecentEmptyLayout>