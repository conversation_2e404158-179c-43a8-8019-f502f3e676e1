/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentCardImageFileProviderTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/6/29      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.provider

import android.net.Uri
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Test

class RecentCardImageFileProviderTest {

    @Test
    fun onCreateTest() {
        val provider = mockk<RecentCardImageFileProvider>()
        every { provider.onCreate() } answers { callOriginal() }
        Assert.assertEquals(false, provider.onCreate())
    }

    @Test
    fun openFileTest() {
        val provider = mockk<RecentCardImageFileProvider>()
        every { provider.openFile(any(), any()) } answers { callOriginal() }
        val uri = mockk<Uri>(relaxed = true)
        every { uri.path } returns "/data/user/0/com.coloros.filemanager/file/images/1.png"
        val parcel = provider.openFile(uri, "MODE_READ_ONLY")
        Assert.assertEquals(true, parcel == null)
    }
}