/*********************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : TestPermissionUtils
 * Description :
 * Version     : 1.0
 * Date        : 2023/08/01
 * Author      : W9002848
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * W9002848        2023/08/07       1.0      create
 **********************************************************************/
package com.coloros.filemanager.utils

import com.coloros.filemanager.utils.TestConstants.RES_CONTENT_DISMISS_IV
import com.coloros.filemanager.utils.TestConstants.RES_ID_COUI_TOOLBAR_BACK
import com.coloros.filemanager.utils.TestConstants.TEXT_AGREE_AND_USE_CN
import com.coloros.filemanager.utils.TestConstants.TEXT_AGREE_AND_USE_US
import com.coloros.filemanager.utils.TestConstants.TEXT_ALL_FILES_ACCESS_CN
import com.coloros.filemanager.utils.TestConstants.TEXT_ALL_FILES_ACCESS_TILE_CN
import com.coloros.filemanager.utils.TestConstants.TEXT_ALL_FILES_ACCESS_TILE_US
import com.coloros.filemanager.utils.TestConstants.TEXT_ALL_FILES_ACCESS_US
import com.coloros.filemanager.utils.TestConstants.TEXT_APP_NAME_CN
import com.coloros.filemanager.utils.TestConstants.TEXT_APP_NAME_US
import com.coloros.filemanager.utils.TestConstants.TEXT_CONTENT_CN
import com.coloros.filemanager.utils.TestConstants.TEXT_CONTENT_US
import com.coloros.filemanager.utils.TestConstants.TEXT_NOTICE_TO_USERS_CN
import com.coloros.filemanager.utils.TestConstants.TEXT_NOTICE_TO_USERS_US
import com.coloros.filemanager.utils.TestConstants.TEXT_OPEN
import com.coloros.filemanager.utils.TestConstants.TEXT_OPEN_ACCESS_TITLE_CN
import com.coloros.filemanager.utils.TestConstants.TEXT_OPEN_ACCESS_TITLE_US
import com.coloros.filemanager.utils.TestConstants.TEXT_OPEN_US
import com.coloros.filemanager.utils.TestConstants.TEXT_PERMIT_READ_CN
import com.coloros.filemanager.utils.TestConstants.TEXT_PERMIT_READ_TITLE_CN
import com.coloros.filemanager.utils.TestConstants.TEXT_PERMIT_READ_TITLE_US
import com.coloros.filemanager.utils.TestConstants.TEXT_PERMIT_READ_US
import com.coloros.filemanager.utils.TestConstants.TIME_WAIT_TWO_SEC
import com.coloros.filemanager.utils.TestUiViewUtils.findContainsTextExist
import com.coloros.filemanager.utils.TestUiViewUtils.findResourceIdOnClick
import com.coloros.filemanager.utils.TestUiViewUtils.findTextExist
import com.coloros.filemanager.utils.TestUiViewUtils.findTextOnClick
import com.filemanager.common.utils.KtUtils

object TestPermissionUtils {

    private val TAG = TestPermissionUtils::class.java.simpleName

    /**
     * 首次打开“文件管理”，需要校验权限
     */
    fun applyPermissions() {
        //根据语言不同，权限弹窗也会不同。目前只做中文 & 英文的适配。
        if (KtUtils.isChineseLanguage()) {
            //1、用户授权：应用权限弹窗
            if (findTextExist(TEXT_NOTICE_TO_USERS_CN) && findContainsTextExist(TEXT_APP_NAME_CN)) {
                findTextOnClick(TEXT_AGREE_AND_USE_CN)
                //2、开启"开启“访问所有文件”权限"弹窗
                if (findTextExist(TEXT_OPEN_ACCESS_TITLE_CN) && findContainsTextExist(TEXT_APP_NAME_CN)) {
                    findTextOnClick(TEXT_OPEN)
                    //3、用户授权：所有文件访问权限
                    if (findTextExist(TEXT_ALL_FILES_ACCESS_TILE_CN) && findContainsTextExist(TEXT_APP_NAME_CN)) {
                        findTextOnClick(TEXT_ALL_FILES_ACCESS_CN)
                        findResourceIdOnClick(RES_ID_COUI_TOOLBAR_BACK)
                    }
                }
                Thread.sleep(TIME_WAIT_TWO_SEC)
                //4、点击卡片关闭按钮
                if (findTextExist(TEXT_CONTENT_CN)) {
                    findResourceIdOnClick(RES_CONTENT_DISMISS_IV)
                }
                Thread.sleep(TIME_WAIT_TWO_SEC)
            }
            applyAppListPermission()
        } else {
            //1、用户授权：应用权限弹窗
            if (findTextExist(TEXT_NOTICE_TO_USERS_US) && findContainsTextExist(TEXT_APP_NAME_US)) {
                findTextOnClick(TEXT_AGREE_AND_USE_US)
                //2、开启"开启“访问所有文件”权限"弹窗
                if (findTextExist(TEXT_OPEN_ACCESS_TITLE_US) && findContainsTextExist(TEXT_APP_NAME_US)) {
                    findTextOnClick(TEXT_OPEN_US)
                    //3、用户授权：所有文件访问权限
                    if (findTextExist(TEXT_ALL_FILES_ACCESS_TILE_US) && findContainsTextExist(TEXT_APP_NAME_US)) {
                        findTextOnClick(TEXT_ALL_FILES_ACCESS_US)
                        findResourceIdOnClick(RES_ID_COUI_TOOLBAR_BACK)
                    }
                }
                Thread.sleep(TIME_WAIT_TWO_SEC)
                //4、点击卡片关闭按钮
                if (findTextExist(TEXT_CONTENT_US)) {
                    findResourceIdOnClick(RES_CONTENT_DISMISS_IV)
                }
                Thread.sleep(TIME_WAIT_TWO_SEC)
            }
            applyAppListPermission()
        }
    }

    fun applyAppListPermission() {
        if (KtUtils.isChineseLanguage()) {
            //5、用户授权：获取系统权限弹窗 CN
            if (findTextExist(TEXT_PERMIT_READ_TITLE_CN)) {
                findTextOnClick(TEXT_PERMIT_READ_CN)
            }
        } else {
            //5、用户授权：获取系统权限弹窗 US
            if (findTextExist(TEXT_PERMIT_READ_TITLE_US)) {
                findTextOnClick(TEXT_PERMIT_READ_US)
            }
        }
    }
}