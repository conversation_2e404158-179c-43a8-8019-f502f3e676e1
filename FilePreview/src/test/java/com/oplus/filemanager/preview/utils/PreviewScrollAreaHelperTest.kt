package com.oplus.filemanager.preview.utils

import android.view.WindowInsets
import com.oplus.filemanager.preview.widget.PreviewOperationsBar
import com.oplus.filemanager.preview.widget.PreviewScrollView
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import org.robolectric.annotation.LooperMode
import org.robolectric.shadows.ShadowLooper
import kotlin.test.assertEquals
import java.lang.reflect.Field

/**
 * PreviewScrollAreaHelper 的单元测试类
 * 用于测试 PreviewScrollAreaHelper 的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
@LooperMode(LooperMode.Mode.PAUSED)
internal class PreviewScrollAreaHelperTest {

    // 使用 MockK 框架模拟 PreviewScrollView 对象
    @MockK
    private lateinit var mockScrollView: PreviewScrollView

    // 使用 MockK 框架模拟 PreviewOperationsBar 对象
    @MockK
    private lateinit var mockOperationsBar: PreviewOperationsBar

    // 待测试的 PreviewScrollAreaHelper 实例
    private lateinit var helper: PreviewScrollAreaHelper

    /**
     * 在每个测试方法执行前的初始化操作
     */
    @Before
    fun setUp() {
        // 初始化 MockK 注解
        MockKAnnotations.init(this)
        // 设置 mockOperationsBar.setOnWindowInsetsChangedListener 的模拟行为
        every { mockOperationsBar.setOnWindowInsetsChangedListener(any()) } returns mockOperationsBar
        // 设置 mockOperationsBar.setDividerVisible 的模拟行为
        every { mockOperationsBar.setDividerVisible(any(), any()) } just Runs
        // 设置 mockScrollView.setFillViewportMinHeight 的模拟行为
        every { mockScrollView.setFillViewportMinHeight(any()) } just Runs
        // 设置 mockScrollView.requestLayout 的模拟行为
        every { mockScrollView.requestLayout() } just Runs
        // 创建待测试的 PreviewScrollAreaHelper 实例
        helper = PreviewScrollAreaHelper(mockScrollView, mockOperationsBar)
    }

    /**
     * 测试初始化时是否正确设置了窗口插入监听器
     */
    @Test
    fun `init should set window insets listener`() {
        // 验证是否调用了 setOnWindowInsetsChangedListener 方法
        verify { mockOperationsBar.setOnWindowInsetsChangedListener(helper) }
    }

    /**
     * 测试 checkShowDivider 方法在内容可滚动时应显示分割线
     */
    @Test
    fun `checkShowDivider should set divider visible when content is scrollable`() {
        // 设置模拟行为：内容可滚动
        every { mockScrollView.isContentScrollable() } returns true

        // 调用待测试方法
        helper.checkShowDivider()

        // 验证是否调用了显示分割线的方法
        verify { mockOperationsBar.setDividerVisible(true, true) }
        // 验证 requireHideDivider 标志位是否正确设置
        assertEquals(false, getRequireHideDivider(helper))
    }

    /**
     * 测试 checkShowDivider 方法在内容不可滚动时应隐藏分割线
     */
    @Test
    fun `checkShowDivider should set divider invisible when content not scrollable`() {
        // 设置模拟行为：内容不可滚动
        every { mockScrollView.isContentScrollable() } returns false

        // 调用待测试方法
        helper.checkShowDivider()

        // 验证是否调用了隐藏分割线的方法
        verify { mockOperationsBar.setDividerVisible(false, true) }
        // 验证 requireHideDivider 标志位是否正确设置
        assertEquals(false, getRequireHideDivider(helper))
    }

    /**
     * 测试 hideDivider 方法应隐藏分割线并更新标志位
     */
    @Test
    fun `hideDivider should set divider invisible and update flag`() {
        // 调用待测试方法
        helper.hideDivider()

        // 验证是否调用了隐藏分割线的方法
        verify { mockOperationsBar.setDividerVisible(false, true) }
        // 验证 requireHideDivider 标志位是否正确设置为 true
        assertEquals(true, getRequireHideDivider(helper))
    }

    /**
     * 测试 enableScrollViewportMinHeight 方法应从资源中获取最小高度并设置
     */
    @Test
    fun `enableScrollViewportMinHeight should set min height from resources`() {
        // 设置预期的高度值
        val expectedHeight = 100
        // 设置模拟行为：从资源中获取高度
        every { mockScrollView.context.resources.getDimensionPixelOffset(any()) } returns expectedHeight

        // 调用待测试方法
        helper.enableScrollViewportMinHeight()

        // 验证是否设置了正确的最小高度
        verify { 
            mockScrollView.setFillViewportMinHeight(expectedHeight)
            mockScrollView.requestLayout()
        }
    }

    /**
     * 测试带参数的 enableScrollViewportMinHeight 方法应设置指定的最小高度
     */
    @Test
    fun `enableScrollViewportMinHeight with param should set specified min height`() {
        // 设置测试高度值
        val testHeight = 200

        // 调用待测试方法
        helper.enableScrollViewportMinHeight(testHeight)

        // 验证是否设置了正确的最小高度
        verify { 
            mockScrollView.setFillViewportMinHeight(testHeight)
            mockScrollView.requestLayout()
        }
    }

    /**
     * 测试 disableScrollViewportMinHeight 方法应将最小高度设置为0
     */
    @Test
    fun `disableScrollViewportMinHeight should set min height to zero`() {
        // 调用待测试方法
        helper.disableScrollViewportMinHeight()

        // 验证是否将最小高度设置为0
        verify { 
            mockScrollView.setFillViewportMinHeight(0)
            mockScrollView.requestLayout()
        }
    }

    /**
     * 测试 onWindowInsetsChanged 方法在 requireHideDivider 为 true 时应隐藏分割线
     */
    @Test
    fun `onWindowInsetsChanged should hide divider when requireHideDivider is true`() {
        // 设置 requireHideDivider 为 true
        helper.hideDivider()
        // 捕获 Runnable 对象
        val runnableSlot = slot<Runnable>()
        // 设置模拟行为：捕获 postDelayed 的 Runnable
        every { mockOperationsBar.postDelayed(capture(runnableSlot), any()) } returns true

        // 调用待测试方法
        helper.onWindowInsetsChanged(mockk())

        // 执行捕获的 Runnable
        runnableSlot.captured.run()
        // 验证是否调用了隐藏分割线的方法
        verify { mockOperationsBar.setDividerVisible(false, false) }
    }

    /**
     * 测试 onWindowInsetsChanged 方法在 requireHideDivider 为 false 且内容可滚动时应显示分割线
     */
    @Test
    fun `onWindowInsetsChanged should show divider when requireHideDivider false and scrollable`() {
        // 设置模拟行为：内容可滚动
        every { mockScrollView.isContentScrollable() } returns true
        // 设置 requireHideDivider 为 false
        helper.checkShowDivider()
        // 捕获 Runnable 对象
        val runnableSlot = slot<Runnable>()
        // 设置模拟行为：捕获 postDelayed 的 Runnable
        every { mockOperationsBar.postDelayed(capture(runnableSlot), any()) } returns true

        // 调用待测试方法
        helper.onWindowInsetsChanged(mockk())

        // 执行捕获的 Runnable
        runnableSlot.captured.run()
        // 验证是否调用了显示分割线的方法
        verify { mockOperationsBar.setDividerVisible(true, false) }
    }

    /**
     * 测试 onWindowInsetsChanged 方法在 requireHideDivider 为 false 但内容不可滚动时应隐藏分割线
     */
    @Test
    fun `onWindowInsetsChanged should hide divider when requireHideDivider false but not scrollable`() {
        // 设置模拟行为：内容不可滚动
        every { mockScrollView.isContentScrollable() } returns false
        // 设置 requireHideDivider 为 false
        helper.checkShowDivider()
        // 捕获 Runnable 对象
        val runnableSlot = slot<Runnable>()
        // 设置模拟行为：捕获 postDelayed 的 Runnable
        every { mockOperationsBar.postDelayed(capture(runnableSlot), any()) } returns true

        // 调用待测试方法
        helper.onWindowInsetsChanged(mockk())

        // 执行捕获的 Runnable
        runnableSlot.captured.run()
        // 验证是否调用了隐藏分割线的方法
        verify { mockOperationsBar.setDividerVisible(false, false) }
    }

    /**
     * 测试 onWindowInsetsChanged 方法应能处理 null 的 insets 参数
     */
    @Test
    fun `onWindowInsetsChanged should handle null insets`() {
        // 捕获 Runnable 对象
        val runnableSlot = slot<Runnable>()
        // 设置模拟行为：捕获 postDelayed 的 Runnable
        every { mockOperationsBar.postDelayed(capture(runnableSlot), any()) } returns true

        // 调用待测试方法，传入 null 参数
        helper.onWindowInsetsChanged(null)

        // 执行捕获的 Runnable
        runnableSlot.captured.run()
        // 验证是否调用了 setDividerVisible 方法
        verify(exactly = 1) { mockOperationsBar.setDividerVisible(any(), false) }
    }
    
    /**
     * 辅助方法：通过反射获取 PreviewScrollAreaHelper 的 requireHideDivider 字段值
     */
    private fun getRequireHideDivider(helper: PreviewScrollAreaHelper): Boolean {
        // 获取 requireHideDivider 字段
        val field: Field = PreviewScrollAreaHelper::class.java.getDeclaredField("requireHideDivider")
        // 设置字段可访问
        field.isAccessible = true
        // 返回字段值
        return field.get(helper) as Boolean
    }
}