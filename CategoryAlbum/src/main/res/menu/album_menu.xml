<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="AlwaysShowAction">

    <item
        android:id="@+id/actionbar_preview"
        android:title=""
        android:visible="false"
        app:showAsAction="always|collapseActionView" />
    <item
        android:id="@+id/actionbar_search"
        android:icon="@drawable/color_tool_menu_ic_search"
        android:title="@string/search_item"
        android:visible="true"
        app:showAsAction="always|collapseActionView" />
    <item
        android:id="@+id/actionbar_scan_mode"
        android:title=""
        android:visible="true"
        app:showAsAction="always|collapseActionView" />
    <item
        android:id="@+id/actionbar_edit"
        android:title="@string/menu_recent_file_edit"
        android:visible="true"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_setting"
        android:title="@string/set_button_text"
        android:enabled="true"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_sort"
        android:title="@string/menu_file_list_sort"
        android:enabled="true"
        app:showAsAction="never">
        <menu>
            <item
                android:id="@+id/taken_time"
                android:title="@string/taken_time"
                app:showAsAction="never" />
            <item
                android:id="@+id/modify_time"
                android:title="@string/modify_time"
                app:showAsAction="never" />
            <item
                android:id="@+id/sort_by_name"
                android:title="@string/sort_by_name"
                app:showAsAction="never" />
        </menu>
    </item>
</menu>