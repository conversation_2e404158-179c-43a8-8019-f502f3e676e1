package com.oplus.filemanager.drivebrowser.di

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.CategoryHelper
import com.oplus.filemanager.drivebrowser.data.api.FileDriveService
import com.oplus.filemanager.drivebrowser.data.api.KDocsFileDriveService
import com.oplus.filemanager.drivebrowser.data.api.TencentFileDriveService
import com.oplus.filemanager.drivebrowser.data.utils.ServerConfig
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import okhttp3.OkHttpClient
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import retrofit2.Retrofit

/**
 * NetworkModule的单元测试类
 * 用于测试NetworkModule中网络相关功能的正确性
 */
@RunWith(RobolectricTestRunner::class)  // 使用Robolectric测试框架
@Config(sdk = [29])  // 配置测试使用的Android SDK版本
class NetworkModuleTest {

    // 模拟对象声明
    private lateinit var mockContext: Context
    private lateinit var mockServerConfig: ServerConfig
    private lateinit var mockRetrofit: Retrofit
    private lateinit var mockOkHttpClient: OkHttpClient

    /**
     * 测试前的初始化方法
     * 创建所有需要的模拟对象并设置默认行为
     */
    @Before
    fun setUp() {
        // 创建各种mock对象
        mockContext = mockk(relaxed = true)  // 创建宽松的Context mock
        mockServerConfig = mockk()  // 创建ServerConfig mock
        mockRetrofit = mockk()  // 创建Retrofit mock
        mockOkHttpClient = mockk()  // 创建OkHttpClient mock

        // 模拟MyApplication单例
        mockkObject(MyApplication)
        every { MyApplication.sAppContext } returns mockContext

        // 模拟ServerConfig单例
        mockkObject(ServerConfig)
        every { ServerConfig.getInstance() } returns mockServerConfig
        every { mockServerConfig.apiUrl } returns "https://test.api.url/"  // 设置模拟API地址
        every { mockServerConfig.kdocsOpenUrl } returns "https://test.kdocs.url/"  // 设置模拟金山文档打开地址
    }

    /**
     * 测试后的清理方法
     * 解除所有mock对象的模拟
     */
    @After
    fun tearDown() {
        unmockkAll()  // 解除所有mock
    }

    /**
     * 测试providerFileDriveService方法对腾讯文档类别的处理
     * 应返回TencentFileDriveService实例
     */
    @Test
    fun `providerFileDriveService should return correct service for Tencent Docs`() {
        val service = NetworkModule.providerFileDriveService(CategoryHelper.CATEGORY_TENCENT_DOCS)
        assertTrue(service is TencentFileDriveService)  // 验证返回的是腾讯文档服务
    }

    /**
     * 测试providerFileDriveService方法对金山文档类别的处理
     * 应返回KDocsFileDriveService实例
     */
    @Test
    fun `providerFileDriveService should return correct service for K Docs`() {
        val service = NetworkModule.providerFileDriveService(CategoryHelper.CATEGORY_K_DOCS)
        assertTrue(service is KDocsFileDriveService)  // 验证返回的是金山文档服务
    }

    /**
     * 测试providerFileDriveService方法对未知类别的处理
     * 应返回null
     */
    @Test
    fun `providerFileDriveService should return null for unknown category`() {
        val service = NetworkModule.providerFileDriveService(999)  // 传入未知类别ID
        assertNull(service)  // 验证返回null
    }

    /**
     * 测试providerTencentFileDriveService方法
     * 应返回TencentFileDriveService实例
     */
    @Test
    fun `providerTencentFileDriveService should return TencentFileDriveService`() {
        mockkObject(NetworkModule)  // 模拟NetworkModule
        every { mockRetrofit.create(TencentFileDriveService::class.java) } returns mockk()  // 设置Retrofit创建行为

        val service = NetworkModule.providerTencentFileDriveService()
        assertTrue(service is TencentFileDriveService)  // 验证返回的是腾讯文档服务
        
        unmockkAll()  // 解除模拟
    }

    /**
     * 测试provideKDocsFileDriveService方法
     * 应返回KDocsFileDriveService实例
     */
    @Test
    fun `provideKDocsFileDriveService should return KDocsFileDriveService`() {
        mockkObject(NetworkModule)  // 模拟NetworkModule
        every { mockRetrofit.create(KDocsFileDriveService::class.java) } returns mockk()  // 设置Retrofit创建行为

        val service = NetworkModule.provideKDocsFileDriveService()
        assertTrue(service is KDocsFileDriveService)  // 验证返回的是金山文档服务
        
        unmockkAll()  // 解除模拟
    }

    /**
     * 测试buildKingDocsOpenUrl方法
     * 应正确构建金山文档的打开URL
     */
    @Test
    fun `buildKingDocsOpenUrl should return correct url`() {
        val token = "test_token"  // 测试token
        val fileUrl = "test_file_url"  // 测试文件URL
        val expectedUrl = "https://test.kdocs.url/?token=test_token&url=test_file_url"  // 预期结果

        val result = NetworkModule.buildKingDocsOpenUrl(token, fileUrl)
        assertEquals(expectedUrl, result)  // 验证生成的URL符合预期
    }
}