/*********************************************************************
 * Copyright (C), 2010-2030 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : FileDriveAdapterTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/6/12  10:45
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * keweiwei        2024/6/12       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.ui

import androidx.paging.CombinedLoadStates
import androidx.paging.ItemSnapshotList
import androidx.paging.LoadState
import com.oplus.filemanager.drivebrowser.source.CloudDocsRemoteMediator
import com.oplus.filemanager.drivebrowser.ui.adapter.FileDriveAdapter
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import org.junit.Assert
import kotlin.test.Test

class FileDriveAdapterTest {

    @Test
    fun `should return right when call getCloudDocsFiles`() {
        //given
        val adapter = mockk<FileDriveAdapter>()
        val item1 = mockk<CloudDocsItem>()
        val item2 = mockk<CloudDocsItem>()
        val item3 = mockk<CloudDocsItem>()
        val file = mutableListOf<CloudDocsItem>()
        every { adapter.mFiles } returns file
        val currentItems = mutableListOf<CloudDocsItem>()
        file.add(item1)
        currentItems.add(item1)
        currentItems.add(item2)
        currentItems.add(item3)
        val mock = mockk<ItemSnapshotList<CloudDocsItem>>()
        every { mock.items } returns currentItems
        every { adapter.snapshot() } returns mock
        every { adapter.getCloudDocsFiles() } answers { callOriginal() }
        //when
        val result = adapter.getCloudDocsFiles()
        //then
        Assert.assertEquals(currentItems, result)
    }

    @Test
    fun `should return false when call isAuthError if loadState is null`() {
        //given
        val adapter = mockk<FileDriveAdapter>()
        every { adapter.isAuthError() } answers { callOriginal() }
        every { adapter.loadState } returns null
        //then
        val result = adapter.isAuthError()
        //then
        Assert.assertFalse(result)
    }

    @Test
    fun `should return true when call isAuthError if loadState append is auth error`() {
        //setUp
        mockkObject(CloudDocsRemoteMediator)
        //given
        val adapter = mockk<FileDriveAdapter>()
        every { adapter.isAuthError() } answers { callOriginal() }
        val loadState = mockk<CombinedLoadStates>()
        val append = mockk<LoadState>()
        val refresh = mockk<LoadState>()
        every { loadState.append } returns append
        every { loadState.refresh } returns refresh
        every { adapter.loadState } returns loadState
        every { CloudDocsRemoteMediator.isAuthError(append) } returns true
        every { CloudDocsRemoteMediator.isAuthError(refresh) } returns false
        //then
        val result = adapter.isAuthError()
        //then
        Assert.assertTrue(result)
        //teardown
        mockkObject(CloudDocsRemoteMediator)
    }

    @Test
    fun `should return true when call isAuthError if loadState refresh is auth error`() {
        //setUp
        mockkObject(CloudDocsRemoteMediator)
        //given
        val adapter = mockk<FileDriveAdapter>()
        every { adapter.isAuthError() } answers { callOriginal() }
        val loadState = mockk<CombinedLoadStates>()
        val append = mockk<LoadState>()
        val refresh = mockk<LoadState>()
        every { loadState.append } returns append
        every { loadState.refresh } returns refresh
        every { adapter.loadState } returns loadState
        every { CloudDocsRemoteMediator.isAuthError(append) } returns false
        every { CloudDocsRemoteMediator.isAuthError(refresh) } returns true
        //then
        val result = adapter.isAuthError()
        //then
        Assert.assertTrue(result)
        //teardown
        mockkObject(CloudDocsRemoteMediator)
    }

    @Test
    fun `should return false when call isAuthError`() {
        //setUp
        mockkObject(CloudDocsRemoteMediator)
        //given
        val adapter = mockk<FileDriveAdapter>()
        every { adapter.isAuthError() } answers { callOriginal() }
        val loadState = mockk<CombinedLoadStates>()
        val append = mockk<LoadState>()
        val refresh = mockk<LoadState>()
        every { loadState.append } returns append
        every { loadState.refresh } returns refresh
        every { adapter.loadState } returns loadState
        every { CloudDocsRemoteMediator.isAuthError(append) } returns false
        every { CloudDocsRemoteMediator.isAuthError(refresh) } returns false
        //then
        val result = adapter.isAuthError()
        //then
        Assert.assertFalse(result)
        //teardown
        mockkObject(CloudDocsRemoteMediator)
    }
}