/*********************************************************************************
 * Copyright (C), 2008-2024, Oplus, All rights reserved.
 *
 * File: - AbsPreviewContainerManager.kt
 * Description:
 *     The base implementation to manage preview container layout
 *
 * Version: 1.0
 * Date: 2024-12-10
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-12-10   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.core

import android.os.SystemClock
import androidx.annotation.CallSuper

internal abstract class AbsPreviewContainerManager : IPreviewContainerManager {

    override var startLoadingTime: Long? = null
        protected set

    @CallSuper
    override fun showAsLoading() {
        startLoadingTime = SystemClock.uptimeMillis()
    }

    @CallSuper
    override fun showAsFileContainer(isDefault: Boolean) {
        startLoadingTime = null
    }
}