<?xml version="1.0" encoding="utf-8"?>
<com.filemanager.common.view.HoverAwareRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/file_grid_item_layout"
    android:background="?attr/couiColorCard"
    app:hover_radius="@dimen/scan_grid_bg_radius"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/file_grid_item_icon_container"
        android:layout_width="@dimen/file_grid_frame_size"
        android:layout_height="@dimen/file_grid_frame_size"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:layout_centerHorizontal="true">

        <com.filemanager.common.view.FileThumbView
            android:id="@+id/file_grid_item_icon"
            android:layout_width="@dimen/file_grid_video_image_size"
            android:layout_height="@dimen/file_grid_video_image_size"
            android:layout_marginBottom="@dimen/file_grid_icon_margin_bottom"
            android:forceDarkAllowed="false"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:scaleType="fitEnd" />
        <ImageView
            android:layout_width="@dimen/dimen_24dp"
            android:layout_height="@dimen/dimen_24dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/dimen_18dp"
            android:layout_marginStart="@dimen/dimen_20dp"
            android:id="@+id/apk_icon"
            android:visibility="gone"
            />
        <TextView
            android:id="@+id/file_duration_tv"
            android:layout_width="@dimen/dimen_36dp"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/file_grid_item_icon"
            android:layout_centerHorizontal="true"
            android:background="@drawable/item_video_duration_view_bg"
            android:gravity="center"
            android:textColor="@color/coui_color_label_on_color"
            android:fontFamily="sans-serif-medium"
            android:textSize="@dimen/duration_text_size"
            android:visibility="gone" />
    </RelativeLayout>

    <com.filemanager.common.view.MiddleMultilineTextView
        android:id="@+id/title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/file_grid_item_icon_container"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:layout_marginStart="@dimen/dimen_4dp"
        android:layout_marginEnd="@dimen/dimen_4dp"
        android:ellipsize="middle"
        android:gravity="center_horizontal"
        android:maxLines="2"
        android:singleLine="false"
        android:textAppearance="?android:attr/textAppearanceLarge"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textSize="@dimen/grid_recent_item_text_size" />

    <TextView
        android:id="@+id/detail_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/title_tv"
        android:layout_alignStart="@+id/title_tv"
        android:layout_alignEnd="@+id/title_tv"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:layout_marginBottom="@dimen/dimen_6dp"
        android:adjustViewBounds="true"
        android:ellipsize="middle"
        android:gravity="center_horizontal"
        android:paddingStart="@dimen/dimen_18dp"
        android:paddingEnd="@dimen/dimen_18dp"
        android:singleLine="true"
        android:textAppearance="?android:attr/textAppearanceSmall" 
        android:textColor="?attr/couiColorLabelSecondary"
        android:textSize="@dimen/grid_recent_item_sub_text_size" />

    <com.coui.appcompat.checkbox.COUICheckBox
        android:id="@+id/gridview_scrollchoice_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:paddingStart="0dp"
        android:clickable="false"
        android:focusable="false" />
</com.filemanager.common.view.HoverAwareRelativeLayout>