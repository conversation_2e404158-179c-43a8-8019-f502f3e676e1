<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.cardlist.COUICardListSelectedItemLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/action_encrypt"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical"
    android:minHeight="@dimen/dimen_50dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:paddingStart="@dimen/dimen_32dp"
        android:paddingEnd="@dimen/dimen_32dp"
        android:gravity="center_vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/icon_encrypt"
                android:layout_width="@dimen/dimen_24dp"
                android:layout_height="@dimen/dimen_24dp"
                android:contentDescription="@null"
                android:src="@drawable/ic_encrypt_category"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/encrypt_storage_title"
                style="?android:attr/textAppearanceLarge"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_16dp"
                android:layout_marginEnd="@dimen/dimen_18dp"
                android:text="@string/string_encrypt_menu"
                android:textAlignment="viewStart"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/arrow_encrypt_storage"
                app:layout_constraintStart_toEndOf="@id/icon_encrypt"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed" />

            <ImageView
                android:id="@+id/arrow_encrypt_storage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@null"
                android:scaleType="fitCenter"
                android:src="@drawable/coui_btn_next"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

    <View
        android:id="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="0.33dp"
        android:layout_marginStart="@dimen/dimen_72dp"
        android:layout_marginEnd="@dimen/dimen_32dp"
        android:background="?attr/couiColorDivider" />
</com.coui.appcompat.cardlist.COUICardListSelectedItemLayout>
