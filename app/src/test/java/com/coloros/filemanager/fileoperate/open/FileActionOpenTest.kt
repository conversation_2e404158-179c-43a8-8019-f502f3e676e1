/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileActionOpenTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/16
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/16       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.fileoperate.open

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import androidx.activity.ComponentActivity
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.OpenAnyExtUtil
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.fileutils.createFile
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OpenAnyManager
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.open.FileActionOpen
import com.filemanager.fileoperate.open.OPEN_ANIM
import com.filemanager.fileoperate.open.OpenFileFactory
import com.filemanager.fileoperate.open.UNKNOWN_FILE_DIALOG
import com.filemanager.fileoperate.open.ZOOM_WARNING
import io.mockk.*
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.io.File
import org.apache.commons.io.FilenameUtils
import org.junit.Ignore

class FileActionOpenTest {

    private lateinit var context: Context
    private lateinit var open: FileActionOpen
    private lateinit var activity: Activity
    private lateinit var fileBean: BaseFileBean
    private lateinit var fileActionOpen: FileActionOpen

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
        open = mockk()
        activity = mockk()
        every { open.mActivity }.returns(activity)
        mockkStatic(TextUtils::class)
        mockkStatic(AppUtils::class)
        mockkStatic(StatisticsUtils::class)
        mockkStatic(FeatureCompat::class)
        mockkStatic(UriHelper::class)
        mockkStatic(FileTypeUtils::class)
        mockkStatic(FilenameUtils::class)
        mockkStatic(Log::class)
        fileActionOpen = spyk(
            FileActionOpen(mockk(relaxed = true)),
            recordPrivateCalls = true
        )
    }

    @After
    fun teardown() {
        unmockkStatic(TextUtils::class)
        unmockkStatic(AppUtils::class)
        unmockkStatic(StatisticsUtils::class)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(UriHelper::class)
        unmockkStatic(FileTypeUtils::class)
        unmockkStatic(FilenameUtils::class)
        unmockkStatic(Log::class)
    }

    @Test
    fun testisForNameFindApp() {
        val packageManager = mockk<PackageManager>()
        every { appContext.packageManager } returns packageManager
        val emptyLibelArray = arrayOf<String>()
        Assert.assertFalse(FileActionOpen.UnknownFileManager.isForNameFindApp(context, emptyLibelArray))
        val resolveInfo = ResolveInfo()
        val resolveInfos = mutableListOf(resolveInfo)
        every { packageManager.queryIntentActivities(any(), 0) } returns resolveInfos

        // libelArray is null
        val nullLibelArray: Array<String>? = null
        Assert.assertFalse(FileActionOpen.UnknownFileManager.isForNameFindApp(context, nullLibelArray))

        // libelArray is empty
        Assert.assertFalse(FileActionOpen.UnknownFileManager.isForNameFindApp(context, emptyLibelArray))

        // libelArray is not empty but resolveInfos is empty
        val emptyResolveInfos = emptyList<ResolveInfo>()
        every { packageManager.queryIntentActivities(any(), 0) } returns emptyResolveInfos

        val libelArray = arrayOf("com.oplus.filemanager")
        Assert.assertFalse(FileActionOpen.UnknownFileManager.isForNameFindApp(context, libelArray))

        // libelArray is not empty and resolveInfos is not empty
        val resolveInfosWithActivityInfo = mutableListOf<ResolveInfo>()

        val resolveInfo1 = ResolveInfo()
        val activityInfo1 = ActivityInfo()
        activityInfo1.packageName = null
        resolveInfo1.activityInfo = activityInfo1
        resolveInfosWithActivityInfo.add(resolveInfo1)
        every { packageManager.queryIntentActivities(any(), 0) } returns resolveInfosWithActivityInfo
        Assert.assertFalse(FileActionOpen.UnknownFileManager.isForNameFindApp(context, libelArray))

        val resolveInfo2 = ResolveInfo()
        val activityInfo2 = ActivityInfo()
        activityInfo2.packageName = "com.plus.filemanager"
        resolveInfo2.activityInfo = activityInfo2
        resolveInfosWithActivityInfo.add(resolveInfo2)
        every { packageManager.queryIntentActivities(any(), 0) } returns resolveInfosWithActivityInfo
        Assert.assertFalse(FileActionOpen.UnknownFileManager.isForNameFindApp(context, libelArray))

        val resolveInfo3 = ResolveInfo()
        val activityInfo3 = ActivityInfo()
        activityInfo3.packageName = "com.oplus.filemanager"
        resolveInfo3.activityInfo = activityInfo3
        resolveInfosWithActivityInfo.add(resolveInfo3)
        every { packageManager.queryIntentActivities(any(), 0) } returns resolveInfosWithActivityInfo
        Assert.assertTrue(FileActionOpen.UnknownFileManager.isForNameFindApp(context, libelArray))
    }

    @Test
    fun should_openByEpub_when_openFile() {
        fileBean = BaseFileBean().apply {
            mData = File("test.epub").absolutePath
        }
        every { open.openFile() }.answers { callOriginal() }
        every { open.mOperateFile }.returns(fileBean)
        val path = fileBean.mData
        every { open.setIntentExtra() }.returns(Intent())
        every { open.openByEpubType(any(), any(), any(), any()) }.returns(Intent())
        every { open.doStartActivity(any(), any(), any(), any(), any()) }.returns(0)
        every { open.setType(path) }.returns(MimeTypeHelper.EPUB_TYPE)
        every { UriHelper.getFileUri(fileBean, any(), any()) }.returns(mockk())
        every { open.checkIsFileManagerOpen(any()) }.returns(false)
        open.openFile()
        verify { open.openByEpubType(any(), any(), any(), any()) }
        verify { open.doStartActivity(any(), any(), any(), any(), any()) }
        every { UriHelper.getFileUri(fileBean, any(), any()) }.returns(null)
        Assert.assertEquals(open.openFile(), -1001)
    }

    @Test
    fun should_openByTxt_when_openFile1() {
        fileBean = BaseFileBean().apply {
            mData = File("test.txt").absolutePath
        }
        every { open.isSetFileDefaultOpenWay(any(), any()) }.answers { Pair(true, "11") }
        every { open.openByReaderOrBook(any(), any()) }.answers { true }
        every { open.openFile() }.answers { callOriginal() }
        every { open.mOperateFile }.returns(fileBean)
        val path = fileBean.mData
        every { open.setType(path) }.returns(MimeTypeHelper.TXT_TYPE)
        every { UriHelper.getFileUri(fileBean, any(), any()) }.returns(mockk())
        every { open.setIntentExtra() }.returns(Intent())
        mockkObject(OpenFileFactory)
        every { OpenFileFactory.intentToOpenOther(any(), any(), any(), any(), any()) }.returns(Intent())
        justRun { open.notifyObserver(any(), any(), any()) }
        justRun { open.commonOpenDoc() }
        every { open.wpsOpenFile(any()) }.returns(false)
        every { open.doStartActivity(any(), any(), any(), any(), any()) }.returns(0)

        every { open.checkIsFileManagerOpen(any()) }.returns(false)
        every { open.wpsIntentNotHasPackage(any()) }.returns(false)
        every { open.foundActivitiesByIntent(any(), any(), any(), any()) }.returns(true)
        open.openFile()
        verify { open.notifyObserver(ZOOM_WARNING) }
        verify { OpenFileFactory.intentToOpenOther(any(), any(), any(), any(), any()) }
        verify { open.doStartActivity(any(), any(), any(), any(), any()) }
        unmockkObject(OpenFileFactory)
    }

    @Test
    fun should_openByTxt_when_openFile2() {
        fileBean = BaseFileBean().apply {
            mData = File("test.txt").absolutePath
        }
        every { open.openFile() }.answers { callOriginal() }
        every { open.mOperateFile }.returns(fileBean)
        val path = fileBean.mData
        every { open.setType(path) }.returns(MimeTypeHelper.TXT_TYPE)
        every { UriHelper.getFileUri(fileBean, any(), any()) }.returns(mockk())
        every { open.setIntentExtra() }.returns(Intent())
        mockkObject(OpenFileFactory)
        every { OpenFileFactory.intentToOpenOther(any(), any(), any(), any(), any()) }.returns(Intent())
        justRun { open.notifyObserver(any(), any(), any()) }
        justRun { open.commonOpenDoc() }
        every { open.wpsOpenFile(any()) }.returns(false)
        every { open.doStartActivity(any(), any(), any(), any(), any()) }.returns(0)
        every { open.mIsOpenByOtherWay }.returns(false)
        every { FeatureCompat.sIsExpRom }.returns(false)
        every { open.isSetFileDefaultOpenWay(any()) }.returns(Pair(false, ""))
        every { open.openByReaderOrBook(any(), any()) }.returns(true)
        every { open.checkIsFileManagerOpen(any()) }.returns(false)
        every { open.wpsIntentNotHasPackage(any()) }.returns(false)
        every { open.foundActivitiesByIntent(any(), any(), any(), any()) }.returns(true)
        open.openFile()
        verify(inverse = true) { open.commonOpenDoc() }
        verify(inverse = true) { open.wpsOpenFile(any()) }
        unmockkObject(OpenFileFactory)
    }

    @Test
    fun should_openByTxt_when_openFile3() {
        fileBean = BaseFileBean().apply {
            mData = File("test.txt").absolutePath
        }
        every { open.isSetFileDefaultOpenWay(any(), any()) }.answers { Pair(true, "11") }
        every { open.openByReaderOrBook(any(), any()) }.answers { true }
        every { open.wpsIntentNotHasPackage(any()) }.returns(false)
        every { open.foundActivitiesByIntent(any(), any(), any(), any()) }.returns(true)
        every { open.openFile() }.answers { callOriginal() }
        every { open.mOperateFile }.returns(fileBean)
        val path = fileBean.mData
        every { open.setType(path) }.returns(MimeTypeHelper.TXT_TYPE)
        every { UriHelper.getFileUri(fileBean, any(), any()) }.returns(mockk())
        every { open.setIntentExtra() }.returns(Intent())
        mockkObject(OpenFileFactory)
        every { OpenFileFactory.intentToOpenOther(any(), any(), any(), any(), any()) }.returns(Intent())
        justRun { open.notifyObserver(any(), any(), any()) }
        justRun { open.commonOpenDoc() }
        every { open.wpsOpenFile(any()) }.returns(false)
        every { open.doStartActivity(any(), any(), any(), any(), any()) }.returns(0)
        every { open.mIsOpenByOtherWay }.returns(true)
        every { open.checkIsFileManagerOpen(any()) }.returns(false)
        open.openFile()
        verify { open.commonOpenDoc() }
        verify { open.wpsOpenFile(any()) }
        every { open.mIsOpenByOtherWay }.returns(false)
        every { FeatureCompat.sIsExpRom }.returns(true)
        every { open.checkIsFileManagerOpen(any()) }.returns(false)
        open.openFile()
        verify { open.commonOpenDoc() }
        verify { open.wpsOpenFile(any()) }

        every { FeatureCompat.sIsExpRom }.returns(false)
        every { open.isSetFileDefaultOpenWay(any()) }.returns(Pair(true, ""))
        every { open.checkIsFileManagerOpen(any()) }.returns(false)
        open.openFile()
        verify { open.commonOpenDoc() }
        verify { open.wpsOpenFile(any()) }

        every { open.isSetFileDefaultOpenWay(any()) }.returns(Pair(false, ""))
        every { open.openByReaderOrBook(any(), any()) }.returns(false)
        every { open.checkIsFileManagerOpen(any()) }.returns(false)
        open.openFile()
        verify { open.commonOpenDoc() }
        verify { open.wpsOpenFile(any()) }
        mockkObject(OpenFileFactory)
    }

    @Test
    fun should_openByImage_when_openFile() {
        fileBean = BaseFileBean().apply {
            mData = File("test.png").absolutePath
        }
        every { open.openFile() }.answers { callOriginal() }
        every { open.mOperateFile }.returns(fileBean)
        val path = fileBean.mData
        every { open.setIntentExtra() }.returns(Intent())
        every { open.openByImageType(any(), any(), any()) }.returns(Intent())
        every { open.doStartActivity(any(), any(), any(), any(), any()) }.returns(0)
        every { open.setType(path) }.returns(MimeTypeHelper.IMAGE_TYPE)
        every { UriHelper.getFileUri(fileBean, any(), any()) }.returns(mockk())
        every { open.checkIsFileManagerOpen(any()) }.returns(false)
        open.openFile()
        verify { open.openByImageType(any(), any(), any()) }
        verify { open.doStartActivity(any(), any(), any(), any(), any()) }
    }

    @Test
    fun should_openByVideo_when_openFile() {
        fileBean = BaseFileBean().apply {
            mData = File("test.mp4").absolutePath
        }
        every { open.openFile() }.answers { callOriginal() }
        every { open.mOperateFile }.returns(fileBean)
        val path = fileBean.mData
        every { open.setIntentExtra() }.returns(Intent())
        every { open.openByVideoType(any(), any()) }.returns(Intent())
        every { open.doStartActivity(any(), any(), any(), any(), any()) }.returns(0)
        every { open.setType(path) }.returns(MimeTypeHelper.VIDEO_TYPE)
        every { UriHelper.getFileUri(fileBean, any(), any()) }.returns(mockk())
        every { open.checkIsFileManagerOpen(any()) }.returns(false)
        open.openFile()
        verify { open.openByVideoType(any(), any()) }
        verify { open.doStartActivity(any(), any(), any(), any(), any()) }
    }

    @Test
    fun should_openByAudio_when_openFile() {
        fileBean = BaseFileBean().apply {
            mData = File("test.mp3").absolutePath
        }
        every { open.openFile() }.answers { callOriginal() }
        every { open.mOperateFile }.returns(fileBean)
        val path = fileBean.mData
        every { open.setIntentExtra() }.returns(Intent())
        mockkObject(OpenFileFactory)
        every { OpenFileFactory.intentToOpenAudio(any(), any()) }.returns(Intent())
        justRun { open.notifyObserver(any(), any(), any()) }
        every { open.doStartActivity(any(), any(), any(), any(), any()) }.returns(0)
        every { open.setType(path) }.returns(MimeTypeHelper.AUDIO_TYPE)
        every { UriHelper.getFileUri(fileBean, any(), any()) }.returns(mockk())
        every { open.isSetFileDefaultOpenWay(any()) }.returns(Pair(false, ""))
        mockkStatic(OptimizeStatisticsUtil::class)
        justRun { OptimizeStatisticsUtil.openAudioFile(any(), any()) }
        every { open.checkIsFileManagerOpen(any()) }.returns(false)
        open.openFile()
        verify { open.notifyObserver(ZOOM_WARNING) }
        verify { OpenFileFactory.intentToOpenAudio(any(), any()) }
        verify { open.doStartActivity(any(), any(), any(), any(), any()) }
        unmockkObject(OpenFileFactory)
        unmockkStatic(OptimizeStatisticsUtil::class)
    }

    @Test
    fun should_openByDrm_when_openFile() {
        fileBean = BaseFileBean().apply {
            mData = File("test").absolutePath
        }
        every { open.openFile() }.answers { callOriginal() }
        every { open.mOperateFile }.returns(fileBean)
        val path = fileBean.mData
        every { open.setIntentExtra() }.returns(Intent())
        every { open.doStartActivity(any(), any(), any(), any(), any()) }.returns(0)
        every { open.setType(path) }.returns(MimeTypeHelper.DRM_TYPE)
        every { UriHelper.getFileUri(fileBean, any(), any()) }.returns(mockk())
        every { open.checkIsFileManagerOpen(any()) }.returns(false)
        Assert.assertEquals(open.openFile(), 5)
    }

    @Test
    fun should_when_openByReaderOrBook() {
        val intent = mockk<Intent>(relaxed = true)
        val path = "test.txt"
        every { open.openByReaderOrBook(intent, any()) }.answers { callOriginal() }
        justRun { StatisticsUtils.onCommon(any(), any(), any<Map<String, String>>()) }
        every { intent.setPackage(any()) }.returns(mockk())
        every { FileTypeUtils.getExtension(path) }.returns("txt")

        every { AppUtils.checkApkInstalledByPackageName(activity, any()) }.returns(false)
        Assert.assertFalse(open.openByReaderOrBook(intent, path))
        verify(inverse = true) { intent.setPackage(any()) }
        verify(inverse = true) { StatisticsUtils.onCommon(any(), any(), any<Map<String, String>>()) }
        verify(inverse = true) { FileTypeUtils.getExtension(path) }

        every { AppUtils.checkApkInstalledByPackageName(activity, Constants.HEYTAP_READER_PACKAGE_NAME) }.returns(true)
        Assert.assertTrue(open.openByReaderOrBook(intent, path))
        verify { intent.setPackage(Constants.HEYTAP_READER_PACKAGE_NAME) }
        verify { StatisticsUtils.onCommon(any(), StatisticsUtils.EVENT_READER_TO_OPEN, any<Map<String, String>>()) }
        verify { FileTypeUtils.getExtension(path) }

        every { AppUtils.checkApkInstalledByPackageName(activity, Constants.HEYTAP_READER_PACKAGE_NAME) }.returns(false)
        every { AppUtils.checkApkInstalledByPackageName(activity, Constants.OPPO_READER_PACKAGE_NAME) }.returns(true)
        Assert.assertTrue(open.openByReaderOrBook(intent, path))
        verify { intent.setPackage(Constants.OPPO_READER_PACKAGE_NAME) }
        verify { StatisticsUtils.onCommon(any(), StatisticsUtils.EVENT_READER_TO_OPEN, any<Map<String, String>>()) }
        verify { FileTypeUtils.getExtension(path) }

        every { AppUtils.checkApkInstalledByPackageName(activity, Constants.HEYTAP_READER_PACKAGE_NAME) }.returns(false)
        every { AppUtils.checkApkInstalledByPackageName(activity, Constants.OPPO_READER_PACKAGE_NAME) }.returns(false)
        every { AppUtils.checkApkInstalledByPackageName(activity, Constants.BOOK_PACKAGE_NAME) }.returns(true)
        Assert.assertTrue(open.openByReaderOrBook(intent, path))
        verify { intent.setPackage(Constants.BOOK_PACKAGE_NAME) }
        verify { StatisticsUtils.onCommon(any(), StatisticsUtils.EVENT_BOOK_TO_OPEN, any<Map<String, String>>()) }
        verify { FileTypeUtils.getExtension(path) }
    }

    @Test
    fun should_when_commonOpenDoc() {
        fileBean = BaseFileBean().apply {
            mData = File("test.txt").absolutePath
            mIsDirectory = false
        }
        every { open.mOperateFile }.returns(fileBean)
        val path = fileBean.mData
        every { FileTypeUtils.getExtension(path) }.returns("txt")
        justRun { StatisticsUtils.onCommon(any(), any(), any<Map<String, String>>()) }
        every { open.commonOpenDoc() }.answers { callOriginal() }
        open.commonOpenDoc()
        verify { StatisticsUtils.onCommon(any(), StatisticsUtils.OPEN_DOC, any<Map<String, String>>()) }
    }

    @Test
    fun should_when_isSetFileDefaultOpenWay() {
        every { open.isSetFileDefaultOpenWay(any()) }.answers { callOriginal() }
        val packageManager = mockk<PackageManager>()
        every { activity.packageManager } returns packageManager
        every { packageManager.resolveActivity(any(), PackageManager.MATCH_DEFAULT_ONLY) }.returns(null)
        Assert.assertFalse(open.isSetFileDefaultOpenWay(mockk()).first)

        every { packageManager.resolveActivity(any(), PackageManager.MATCH_DEFAULT_ONLY) }.returns(
            mockk()
        )
        every { TextUtils.isEmpty(any()) }.returns(true)
        every { activity.packageName }.returns("com.coloros.filemanager")
        Assert.assertFalse(open.isSetFileDefaultOpenWay(mockk()).first)

        val ri1 = mockk<ResolveInfo>()
        val activityInfo1 = ActivityInfo()
        val applicationInfo1 = ApplicationInfo()
        ri1.activityInfo = activityInfo1
        activityInfo1.applicationInfo = applicationInfo1
        applicationInfo1.packageName = KtConstants.DEFAULT_OPEN_PACKAGE_NAME
        every { packageManager.resolveActivity(any(), PackageManager.MATCH_DEFAULT_ONLY) }.returns(ri1)
        every { TextUtils.isEmpty(any()) }.returns(false)
        Assert.assertFalse(open.isSetFileDefaultOpenWay(mockk()).first)

        val ri2 = mockk<ResolveInfo>()
        val activityInfo2 = ActivityInfo()
        val applicationInfo2 = ApplicationInfo()
        ri2.activityInfo = activityInfo2
        activityInfo2.applicationInfo = applicationInfo2
        applicationInfo2.packageName = "com.UCMobile"
        every { packageManager.resolveActivity(any(), PackageManager.MATCH_DEFAULT_ONLY) }.returns(ri2)
        every { TextUtils.isEmpty(any()) }.returns(false)
        Assert.assertTrue(open.isSetFileDefaultOpenWay(mockk()).first)
    }

    @Test
    fun should_when_openByEpubType() {
        every { open.openByEpubType(any(), any(), any(), any()) }.answers { callOriginal() }
        every { open.openByReaderOrBook(any(), any()) }.returns(false)
        justRun { open.notifyObserver(any(), any(), any()) }
        mockkObject(OpenFileFactory)
        every { OpenFileFactory.intentToOpenOther(any(), any(), any(), any(), any()) }.returns(Intent())


        open.openByEpubType(MimeTypeHelper.EPUB_TYPE, "test.txt", mockk(), mockk())
        verify { open.notifyObserver(ZOOM_WARNING) }
        verify { OpenFileFactory.intentToOpenOther(any(), any(), any(), any(), any()) }

        every { FeatureCompat.sIsExpRom }.returns(true)
        every { open.mIsOpenByOtherWay }.returns(true)
        open.openByEpubType(MimeTypeHelper.EPUB_TYPE, "test.txt", mockk(), mockk())
        verify(inverse = true) { open.openByReaderOrBook(any(), any()) }

        every { FeatureCompat.sIsExpRom }.returns(false)
        every { open.mIsOpenByOtherWay }.returns(true)
        open.openByEpubType(MimeTypeHelper.EPUB_TYPE, "test.txt", mockk(), mockk())
        verify(inverse = true) { open.openByReaderOrBook(any(), any()) }
        every { FeatureCompat.sIsExpRom }.returns(true)
        every { open.mIsOpenByOtherWay }.returns(false)
        open.openByEpubType(MimeTypeHelper.EPUB_TYPE, "test.txt", mockk(), mockk())
        verify(inverse = true) { open.openByReaderOrBook(any(), any()) }
        every { FeatureCompat.sIsExpRom }.returns(false)
        every { open.mIsOpenByOtherWay }.returns(false)
        open.openByEpubType(MimeTypeHelper.EPUB_TYPE, "test.txt", mockk(), mockk())
        verify { open.openByReaderOrBook(any(), any()) }
        unmockkObject(OpenFileFactory)
    }

    @Ignore("unkown error")
    @Test
    fun should_when_doStartActivity() {
        every { open.doStartActivity(any(), any(), any(), any(), any()) }.answers { callOriginal() }
        val intent = mockk<Intent>()
        every { intent.addFlags(any()) }.returns(intent)
        justRun { activity.startActivity(any()) }
        every { open.openUnknownFile(any(), any(), any(), any(), any()) }.returns(0)
        justRun { open.notifyObserver(any(), any(), any()) }

        open.doStartActivity(intent, MimeTypeHelper.EPUB_TYPE, "test.epub", mockk(), false)
        verify { activity.startActivity(intent) }
        verify(inverse = true) { open.notifyObserver(OPEN_ANIM) }

        open.doStartActivity(intent, MimeTypeHelper.IMAGE_TYPE, "test.epub", mockk(), false)
        verify { open.notifyObserver(OPEN_ANIM) }

        open.doStartActivity(intent, MimeTypeHelper.EPUB_TYPE, "test.epub", mockk(), true)
        verify { open.notifyObserver(OPEN_ANIM) }

        open.doStartActivity(intent, MimeTypeHelper.IMAGE_TYPE, "test.epub", mockk(), true)
        verify { open.notifyObserver(OPEN_ANIM) }

        mockkStatic(Intent::class)
        every { Intent.createChooser(intent, null) }.returns(intent)
        open.doStartActivity(intent, MimeTypeHelper.CSV_TYPE, "test.epub", mockk(), true)
        verify { Intent.createChooser(intent, null) }
        verify { activity.startActivity(intent) }
        every { activity.startActivity(any()) }.throws(Exception())
        open.doStartActivity(intent, MimeTypeHelper.EPUB_TYPE, "test.epub", null, false)
        verify { open.notifyObserver(UNKNOWN_FILE_DIALOG, any()) }
    }

    @Test
    fun should_when_openByVideoType() {
        fileBean = BaseFileBean().apply {
            mData = File("test.txt").absolutePath
            mIsDirectory = false
        }
        every { open.openByVideoType(any(), any()) }.answers { callOriginal() }
        justRun { StatisticsUtils.onCommon(any(), any(), any<Map<String, String>>()) }
        justRun { open.notifyObserver(any(), any(), any()) }
        mockkObject(OpenFileFactory)
        every { OpenFileFactory.intentToOpenVideo(any(), any()) }.returns(Intent())
        every { open.mOperateFile }.returns(fileBean)
        val path = fileBean.mData
        every { FileTypeUtils.getExtension(path) }.returns("txt")
        mockkObject(KtUtils)
        every { KtUtils.formatVideoTime(any()) }.returns("0")

        open.openByVideoType(mockk(), mockk())
        verify { open.notifyObserver(ZOOM_WARNING) }
        verify { OpenFileFactory.intentToOpenVideo(any(), any()) }
        unmockkObject(OpenFileFactory)
        unmockkObject(KtUtils)
    }

    @Test
    fun should_when_openByImageType() {
        every { open.openByImageType(any(), any(), any()) }.answers { callOriginal() }
        val intent = mockk<Intent>()
        every { intent.putExtra("file-type", 2) }.returns(intent)
        mockkObject(OpenFileFactory)
        every { OpenFileFactory.intentToOpenImage(any(), any(), any()) }.returns(Intent())

        open.openByImageType(intent, "test.img", mockk())
        verify { OpenFileFactory.intentToOpenImage(any(), any(), any()) }
        unmockkObject(OpenFileFactory)
    }

    @Test
    fun should_when_setType() {
        every { open.setType(any()) }.answers { callOriginal() }
        mockkObject(MimeTypeHelper)
        every { MimeTypeHelper.getTypeFromPath(any()) }.returns(0)
        every { MimeTypeHelper.getMediaType(any()) }.returns(0)
        every { MimeTypeHelper.getTypeFromDrm(any(), any(), any()) }.returns(0)

        open.setType("test")
        verify { MimeTypeHelper.getTypeFromPath(any()) }
        verify { MimeTypeHelper.getTypeFromDrm(any(), any(), any()) }

        every { MimeTypeHelper.getTypeFromPath(any()) }.returns(MimeTypeHelper.AUDIO_TYPE)
        open.setType("test")
        verify(inverse = true) { MimeTypeHelper.getMediaType(any()) }

        every { MimeTypeHelper.getTypeFromPath(any()) }.returns(MimeTypeHelper.UNKNOWN_TYPE)
        open.setType("test")
        verify { MimeTypeHelper.getMediaType(any()) }
        unmockkObject(MimeTypeHelper)
    }

    @Test
    fun `should call doStartActivity when call startOpenAnyApp if canOpenByQuickPreview return true`() {
        //given
        val intent = mockk<Intent>(relaxed = true)
        val type = 0
        val path = ""
        val uri = mockk<Uri>(relaxed = true)
        every { fileActionOpen["canOpenByQuickPreview"](path) } returns true
        every { AppUtils.isAppInstalledByPkgName(any(), any()) } returns true
        every { fileActionOpen["setOpenAnyAppIntent"](intent, path, uri, type) } answers { callOriginal() }
        justRun { intent.`package` }
        every { fileActionOpen.doStartActivity(any(), any(), any(), any(), any()) } answers { ACTION_DONE }
        every { fileActionOpen["startOpenAnyApp"](intent, type, path, uri) } answers { callOriginal() }

        //when
        InternalPlatformDsl.dynamicCall(fileActionOpen, "startOpenAnyApp", arrayOf(intent, type, path, uri), mockk())

        //then
        verify { Log.d(any(), any()) }
        verify { fileActionOpen.doStartActivity(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `should call checkRemovableApp when call startOpenAnyApp if canOpenByQuickPreview return true`() {
        //given
        val intent = mockk<Intent>(relaxed = true)
        val type = 0
        val path = ""
        val uri = mockk<Uri>(relaxed = true)
        val activity = mockk<ComponentActivity>(relaxed = true)
        mockkObject(OpenAnyManager)
        every { open.mActivity } returns activity
        every { fileActionOpen["canOpenByQuickPreview"](path) } returns true
        every { AppUtils.isAppInstalledByPkgName(any(), any()) } returns false
        justRun { fileActionOpen["checkRemovableApp"](allAny<ComponentActivity>(), allAny<String>(), allAny<Uri>(), allAny<Intent>(), allAny<Int>()) }
        every { fileActionOpen["startOpenAnyApp"](intent, type, path, uri) } answers { callOriginal() }

        //when
        val result = InternalPlatformDsl.dynamicCall(fileActionOpen, "startOpenAnyApp", arrayOf(intent, type, path, uri), mockk())

        //then
        verify { Log.d(any(), any()) }
        verify { fileActionOpen["checkRemovableApp"](allAny<ComponentActivity>(), allAny<String>(), allAny<Uri>(), allAny<Intent>(), allAny<Int>()) }
        Assert.assertEquals(ACTION_DONE, result)
    }

    @Test
    fun `should call doStartActivity when call startOpenAnyApp if canOpenByQuickPreview return false`() {
        //given
        mockkObject(MimeTypeHelper)
        mockkObject(OpenAnyManager)
        val intent = mockk<Intent>(relaxed = true)
        val type = 0
        val path = ""
        val uri = mockk<Uri>(relaxed = true)
        val mimeType = ""
        every { fileActionOpen["canOpenByQuickPreview"](path) } returns false
        every { OpenAnyManager.hasIntegrateQuickPreview() }.returns(true)
        every { MimeTypeHelper.getMimeTypeByFileType(any()) } returns mimeType
        every { intent.setDataAndType(any(), any()) } returns intent
        every { intent.setAction(Intent.ACTION_VIEW) } returns intent
        every { fileActionOpen.doStartActivity(any(), any(), any(), any(), any()) } answers { ACTION_DONE }
        every { fileActionOpen["startOpenAnyApp"](intent, type, path, uri) } answers { callOriginal() }

        //when
        InternalPlatformDsl.dynamicCall(fileActionOpen, "startOpenAnyApp", arrayOf(intent, type, path, uri), mockk())

        //then
        verify { Log.d(any(), any()) }
        verify { fileActionOpen.doStartActivity(any(), any(), any(), any(), any()) }
        unmockkObject(MimeTypeHelper)
        unmockkObject(OpenAnyManager)
    }

    @Test
    fun `should return true when call canOpenByQuickPreview`() {
        //given
        mockkObject(OpenAnyExtUtil)
        mockkObject(OpenAnyManager)
        val ext = "key"
        every { FilenameUtils.getExtension(any()).lowercase() } returns ext
        every { fileActionOpen["canOpenByQuickPreview"]("") } answers { callOriginal() }
        every { OpenAnyManager.hasIntegrateQuickPreview() }.returns(true)

        //when
        val canOpen = InternalPlatformDsl.dynamicCall(fileActionOpen, "canOpenByQuickPreview", arrayOf(""), mockk())

        //then
        verify { Log.d(any(), any()) }
        Assert.assertTrue(canOpen as Boolean)
        unmockkObject(OpenAnyExtUtil)
        unmockkObject(OpenAnyManager)
    }

    @Test
    fun `should return false when call canOpenByQuickPreview`() {
        //given
        mockkObject(OpenAnyExtUtil)
        mockkObject(OpenAnyManager)
        val ext = "keysss"
        every { FilenameUtils.getExtension(any()).lowercase() } returns ext
        every { fileActionOpen["canOpenByQuickPreview"]("") } answers { callOriginal() }
        every { OpenAnyManager.hasIntegrateQuickPreview() }.returns(false)

        //when
        val canOpen = InternalPlatformDsl.dynamicCall(fileActionOpen, "canOpenByQuickPreview", arrayOf(""), mockk())

        // then
        verify { Log.d(any(), any()) }
        Assert.assertFalse(canOpen as Boolean)
        unmockkObject(OpenAnyManager)
        unmockkObject(OpenAnyExtUtil)
    }

    @Test
    fun `should put extra when call setOpenAnyAppIntent`() {
        //given
        mockkObject(MimeTypeHelper)
        mockkStatic(::createFile)
        val intent = mockk<Intent>(relaxed = true)
        val uri = mockk<Uri>(relaxed = true)
        val path = ""
        val mimeType = ""
        val fileName = "aaa.txt"
        val fileSize = 100L
        val lastModify = 1000L
        every { MimeTypeHelper.getMimeTypeByFileType(any()) } answers { mimeType }
        every { FilenameUtils.getName(any()) } answers { fileName }
        every { intent.setDataAndType(any(), any()) } answers { intent }
        every { intent.setAction(Intent.ACTION_VIEW) } answers { intent }
        every { Log.d(any(), any()) } answers { callOriginal() }
        every { createFile(any())?.length() } answers { fileSize }
        every { createFile(any())?.lastModified() } answers { lastModify }
        every { fileActionOpen["setOpenAnyAppIntent"](intent, path, uri, 0) } answers { callOriginal() }

        //when
        InternalPlatformDsl.dynamicCall(fileActionOpen, "setOpenAnyAppIntent", arrayOf(intent, path, uri, 0), mockk())

        //then
        verify { intent.setDataAndType(any(), any()) }
        verify { intent.action = any() }
        verify { Log.d(any(), any()) }
        verify { intent.putExtra(KtConstants.FILE_PREVIEW_NAME, any<String>()) }
        verify { intent.putExtra(KtConstants.FILE_PREVIEW_SIZE, any<Long>()) }
        verify { intent.putExtra(KtConstants.FILE_MODIFY_TIME, any<Long>()) }
        unmockkObject(MimeTypeHelper)
        unmockkStatic(::createFile)
    }

    @Test
    fun `should return true or false when call checkIsFileManagerOpen`() {
        val packageManager = mockk<PackageManager>()
        every { appContext.packageManager } returns packageManager
        val intent = Intent()
        val lists = ArrayList<ResolveInfo>()
        every { packageManager.queryIntentActivities(intent, 0) }.returns(lists)
        every { appContext.packageName }.returns("com.coloros.filemanager")
        Assert.assertEquals(fileActionOpen.checkIsFileManagerOpen(intent), false)
        val info = ResolveInfo()
        val activityInfo = ActivityInfo()
        info.activityInfo = activityInfo
        activityInfo.packageName = "com.coloros.filemanager"
        lists.add(info)
        every { packageManager.queryIntentActivities(intent, 0) }.returns(lists)
        Assert.assertEquals(fileActionOpen.checkIsFileManagerOpen(intent), true)
        val info2 = ResolveInfo()
        val activityInfo2 = ActivityInfo()
        info2.activityInfo = activityInfo2
        activityInfo2.packageName = "com.test.test"
        lists.add(info2)
        every { packageManager.queryIntentActivities(intent, 0) }.returns(lists)
        Assert.assertEquals(fileActionOpen.checkIsFileManagerOpen(intent), false)
        val e = Exception()
        every { packageManager.queryIntentActivities(intent, 0) }.throws(e)
        Assert.assertEquals(fileActionOpen.checkIsFileManagerOpen(intent), false)
        lists.clear()
        val info3 = ResolveInfo()
        val activityInfo3 = ActivityInfo()
        info3.activityInfo = activityInfo3
        activityInfo3.packageName = "com.aaa.bbb"
        lists.add(info3)
        every { packageManager.queryIntentActivities(intent, 0) }.returns(lists)
        Assert.assertEquals(fileActionOpen.checkIsFileManagerOpen(intent), false)
    }

    @Test
    fun `should return false when call openDocument if openDocumentByOplusDocumentsReader is true`() {
        //given
        val intent = mockk<Intent>()
        every { fileActionOpen.openDocumentByOplusDocumentsReader(any()) } returns true
        //when
        val result = fileActionOpen.openDocument(intent)
        //then
        verify(inverse = true) { fileActionOpen.wpsOpenFile(any()) }
        Assert.assertEquals(result, false)
    }

    @Test
    fun `should call wpsOpenFile when call openDocument if openDocumentByOplusDocumentsReader is false`() {
        //given
        val intent = mockk<Intent>()
        every { fileActionOpen.openDocumentByOplusDocumentsReader(any()) } returns false
        every { fileActionOpen.wpsOpenFile(intent) } returns true
        //when
        val result = fileActionOpen.openDocument(intent)
        //then
        verify { fileActionOpen.wpsOpenFile(any()) }
        Assert.assertEquals(result, true)
    }

    @Test
    fun `should return false when call openDocumentByOplusDocumentsReader if mIsOpenByOtherWay is true`() {
        //given
        val intent = mockk<Intent>()
        //when
        val result = fileActionOpen.openDocumentByOplusDocumentsReader(intent)
        //then
        Assert.assertEquals(result, false)
    }

    @Test
    fun `should return true when call openDocumentByOplusDocumentsReader if packageName is null`() {
        //given
        val intent = Intent()
        every { fileActionOpen.mIsOpenByOtherWay } returns false
        val packageManager = mockk<PackageManager>()
        every { appContext.packageManager } returns packageManager
        val resolveInfo = mockk<ResolveInfo>()
        val activityInfo = mockk<ActivityInfo>()
        val applicationInfo = mockk<ApplicationInfo>()
        every { packageManager.resolveActivity(any(), any<Int>()) } returns resolveInfo
        resolveInfo.activityInfo = activityInfo
        activityInfo.applicationInfo = applicationInfo
        applicationInfo.packageName = null
        every { fileActionOpen.checkAppEnabled(any()) } returns true
        every { fileActionOpen.isOplusDocumentsReaderSupport() } returns true
        //when
        val result = fileActionOpen.openDocumentByOplusDocumentsReader(intent)
        //then
        Assert.assertEquals(result, true)
    }


    @Test
    fun `should return true when call openDocumentByOplusDocumentsReader if packageName is empty`() {
        //given
        val intent = Intent()
        every { fileActionOpen.mIsOpenByOtherWay } returns false
        val packageManager = mockk<PackageManager>()
        every { appContext.packageManager } returns packageManager
        val resolveInfo = mockk<ResolveInfo>()
        val activityInfo = mockk<ActivityInfo>()
        val applicationInfo = mockk<ApplicationInfo>()
        every { packageManager.resolveActivity(any(), any<Int>()) } returns resolveInfo
        resolveInfo.activityInfo = activityInfo
        activityInfo.applicationInfo = applicationInfo
        applicationInfo.packageName = ""
        every { fileActionOpen.checkAppEnabled(any()) } returns true
        every { fileActionOpen.isOplusDocumentsReaderSupport() } returns true
        //when
        val result = fileActionOpen.openDocumentByOplusDocumentsReader(intent)
        //then
        Assert.assertEquals(result, true)
    }

    @Test
    fun `should return true when call openDocumentByOplusDocumentsReader if packageName is android`() {
        //given
        val intent = Intent()
        every { fileActionOpen.mIsOpenByOtherWay } returns false
        val packageManager = mockk<PackageManager>()
        every { appContext.packageManager } returns packageManager
        val resolveInfo = mockk<ResolveInfo>()
        val activityInfo = mockk<ActivityInfo>()
        val applicationInfo = mockk<ApplicationInfo>()
        every { packageManager.resolveActivity(any(), any<Int>()) } returns resolveInfo
        resolveInfo.activityInfo = activityInfo
        activityInfo.applicationInfo = applicationInfo
        applicationInfo.packageName = "android"
        every { fileActionOpen.checkAppEnabled(any()) } returns true
        every { fileActionOpen.isOplusDocumentsReaderSupport() } returns true
        //when
        val result = fileActionOpen.openDocumentByOplusDocumentsReader(intent)
        //then
        Assert.assertEquals(result, true)
    }

    @Test
    fun `should return true when call openDocumentByOplusDocumentsReader if packageName is documentsreader`() {
        //given
        val intent = Intent()
        every { fileActionOpen.mIsOpenByOtherWay } returns false
        val packageManager = mockk<PackageManager>()
        every { appContext.packageManager } returns packageManager
        val resolveInfo = mockk<ResolveInfo>()
        val activityInfo = mockk<ActivityInfo>()
        val applicationInfo = mockk<ApplicationInfo>()
        every { packageManager.resolveActivity(any(), any<Int>()) } returns resolveInfo
        resolveInfo.activityInfo = activityInfo
        activityInfo.applicationInfo = applicationInfo
        applicationInfo.packageName = "andes.oplus.documentsreader"
        every { fileActionOpen.checkAppEnabled(any()) } returns true
        every { fileActionOpen.isOplusDocumentsReaderSupport() } returns true
        //when
        val result = fileActionOpen.openDocumentByOplusDocumentsReader(intent)
        //then
        Assert.assertEquals(result, true)
    }

    @Test
    fun `should return true when call openDocumentByOplusDocumentsReader if packageName is invalide`() {
        //given
        val intent = Intent()
        every { fileActionOpen.mIsOpenByOtherWay } returns false
        val packageManager = mockk<PackageManager>()
        every { appContext.packageManager } returns packageManager
        val resolveInfo = mockk<ResolveInfo>()
        val activityInfo = mockk<ActivityInfo>()
        val applicationInfo = mockk<ApplicationInfo>()
        every { packageManager.resolveActivity(any(), any<Int>()) } returns resolveInfo
        resolveInfo.activityInfo = activityInfo
        activityInfo.applicationInfo = applicationInfo
        applicationInfo.packageName = "com.coloros.filemanager"
        every { fileActionOpen.checkAppEnabled(any()) } returns true
        every { fileActionOpen.isOplusDocumentsReaderSupport() } returns true
        //when
        val result = fileActionOpen.openDocumentByOplusDocumentsReader(intent)
        //then
        Assert.assertEquals(result, false)
    }

    @Test
    fun `should return true when call openDocumentByOplusDocumentsReader if checkAppEnabled is false`() {
        //given
        val intent = Intent()
        every { fileActionOpen.mIsOpenByOtherWay } returns false
        val packageManager = mockk<PackageManager>()
        every { appContext.packageManager } returns packageManager
        val resolveInfo = mockk<ResolveInfo>()
        val activityInfo = mockk<ActivityInfo>()
        val applicationInfo = mockk<ApplicationInfo>()
        every { packageManager.resolveActivity(any(), any<Int>()) } returns resolveInfo
        resolveInfo.activityInfo = activityInfo
        activityInfo.applicationInfo = applicationInfo
        applicationInfo.packageName = ""
        every { fileActionOpen.checkAppEnabled(any()) } returns false
        every { fileActionOpen.isOplusDocumentsReaderSupport() } returns true
        //when
        val result = fileActionOpen.openDocumentByOplusDocumentsReader(intent)
        //then
        Assert.assertEquals(result, false)
    }

    @Test
    fun `should return true when call openDocumentByOplusDocumentsReader if isOplusDocumentsReaderSupport is false`() {
        //given
        val intent = Intent()
        every { fileActionOpen.mIsOpenByOtherWay } returns false
        val packageManager = mockk<PackageManager>()
        every { appContext.packageManager } returns packageManager
        val resolveInfo = mockk<ResolveInfo>()
        val activityInfo = mockk<ActivityInfo>()
        val applicationInfo = mockk<ApplicationInfo>()
        every { packageManager.resolveActivity(any(), any<Int>()) } returns resolveInfo
        resolveInfo.activityInfo = activityInfo
        activityInfo.applicationInfo = applicationInfo
        applicationInfo.packageName = ""
        every { fileActionOpen.checkAppEnabled(any()) } returns true
        every { fileActionOpen.isOplusDocumentsReaderSupport() } returns false
        //when
        val result = fileActionOpen.openDocumentByOplusDocumentsReader(intent)
        //then
        Assert.assertEquals(result, false)
    }

    @Test
    fun `should return false when call isOplusDocumentsReaderSupport if none support_preview`() {
        //given
        val packageManager = mockk<PackageManager>()
        mockkStatic(AppUtils::class)
        every { appContext.packageManager } returns packageManager
        val applicationInfo = mockk<ApplicationInfo>()
        every { packageManager.getApplicationInfo(any(), any<Int>()) } returns applicationInfo
        val metadata = mockk<Bundle>()
        applicationInfo.metaData = metadata
        every { metadata.getBoolean("support_preview") } returns false
        every { AppUtils.getAppVersionCode(any()) } returns 0
        //when
        val result = fileActionOpen.isOplusDocumentsReaderSupport()
        //then
        Assert.assertEquals(result, false)
        unmockkStatic(AppUtils::class)
    }

    @Test
    fun `should return true when call isOplusDocumentsReaderSupport if has support_preview`() {
        //given
        val packageManager = mockk<PackageManager>()
        mockkStatic(AppUtils::class)
        every { appContext.packageManager } returns packageManager
        val applicationInfo = mockk<ApplicationInfo>()
        every { packageManager.getApplicationInfo(any(), any<Int>()) } returns applicationInfo
        val metadata = mockk<Bundle>()
        applicationInfo.metaData = metadata
        every { metadata.getBoolean("support_preview") } returns true
        every { AppUtils.getAppVersionCode(any()) } returns 0
        //when
        val result = fileActionOpen.isOplusDocumentsReaderSupport()
        //then
        Assert.assertEquals(result, true)
        unmockkStatic(AppUtils::class)
    }

    @Test
    fun `should return true when call isOplusDocumentsReaderSupport if version fit`() {
        //given
        val packageManager = mockk<PackageManager>()
        mockkStatic(AppUtils::class)
        every { appContext.packageManager } returns packageManager
        val applicationInfo = mockk<ApplicationInfo>()
        every { packageManager.getApplicationInfo(any(), any<Int>()) } returns applicationInfo
        val metadata = mockk<Bundle>()
        applicationInfo.metaData = metadata
        every { metadata.getBoolean("support_preview") } returns false
        every { AppUtils.getAppVersionCode(any()) } returns 14141000
        //when
        val result = fileActionOpen.isOplusDocumentsReaderSupport()
        //then
        Assert.assertEquals(result, true)
        unmockkStatic(AppUtils::class)
    }
}