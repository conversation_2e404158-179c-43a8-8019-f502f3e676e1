/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:FileRenameObserver.kt
 * * Description:
 * * Version:1.0
 * * Date :2020.1.9
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.createdir

import android.content.Context
import android.view.ContextThemeWrapper
import com.filemanager.common.utils.CustomToast
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.rename.FileRenameObserver
import com.filemanager.fileoperate.R
import com.filemanager.fileoperate.base.ACTION_CANCELLED

open class FileCreateDirObserver(context: ContextThemeWrapper) : FileRenameObserver(context) {

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        return when (result.first) {
            ACTION_FAILED -> {
                dismissRenameDialog()
                CustomToast.showShort(com.filemanager.common.R.string.toast_create_folder_error)
                onActionDone(false)
                true
            }
            ACTION_CANCELLED -> {
                dismissRenameDialog()
                onActionDone(false)
                true
            }
            else -> super.onChanged(context, result)
        }
    }
}