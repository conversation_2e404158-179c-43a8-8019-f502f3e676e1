/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - SelectionDelegate.java
 ** Description: Selection Delegate
 ** Version: 1.0
 ** Date : 2020/05/17
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/17    1.0     create
 ****************************************************************/

package com.oplus.dropdrag

import androidx.annotation.Keep
import com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE

@Keep
abstract class SelectionDelegate<K> {
    /**
     * @param key for item
     * @return true if in selection mode.
     */
    abstract fun enterSelectionMode(key: K): Bo<PERSON>an

    /**
     * @return true if in selection mode.
     */
    abstract fun isInSelectMode(): Boolean

    /**
     * @param key list for items
     * @return true if select items success
     */
    abstract fun selectItems(key: ArrayList<K>): Boolean

    /**
     * @return true if deselect item success
     */
    abstract fun deselectItems(key: ArrayList<K>): Boolean

    /**
     * @param key for item
     * @return true if item is selected.
     */
    abstract fun isItemSelected(key: K): Boolean

    /**
     * @return key list of selected item
     */
    abstract fun getSelectionList(): List<K>

    /**
     * @return true if can drag and drop.or false
     */
    abstract fun canDragDrop(): Boolean

    /**
     * @param key for item
     * @return true if select item success
     */
    fun selectItem(key: K): Boolean {
        val keyList = arrayListOf(key)
        return selectItems(keyList)
    }

    /**
     * @param key for item
     * @return true if deselect item success
     */
    fun deselectItem(key: K): Boolean {
        val keyList = arrayListOf(key)
        return deselectItems(keyList)
    }

    /**
     * @return true if no item selected
     */
    fun isAnyItemSelected(): Boolean {
        val selectList = getSelectionList()
        return isInSelectMode() && !selectList.isNullOrEmpty()
    }

    /**
     * @return layoutType for slide selection
     */
    open fun getLayoutType(): LAYOUT_TYPE {
        return LAYOUT_TYPE.LIST
    }
}