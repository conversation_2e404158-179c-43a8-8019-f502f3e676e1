/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.audio
 ** Version: 1.0
 ** Date: 2021/7/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.audio

import androidx.annotation.Keep
import com.bumptech.glide.load.Options
import com.bumptech.glide.load.engine.Resource
import com.bumptech.glide.load.resource.transcode.ResourceTranscoder

/**
 * convert from AudioThumbnailResultByteArray to AudioThumbnailResultByteArray
 */
@Keep
class AudioThumbnailResultTranscoder :
    ResourceTranscoder<AudioThumbnailResultByteArray, AudioThumbnailResult> {

    override fun transcode(
        toTranscode: Resource<AudioThumbnailResultByteArray>,
        options: Options
    ): Resource<AudioThumbnailResult> {
        return AudioThumbnailResultResource(
            AudioThumbnailResult(
                AudioThumbnailResult.convertByteArrayToBitmap(toTranscode.get().mBitmapByteArray)
            )
        )
    }
}