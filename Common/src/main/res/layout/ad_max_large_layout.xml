<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_max_container"
    android:forceDarkAllowed="true"
    android:padding="@dimen/dimen_15dp">

    <ImageView
        android:id="@+id/ad_max_icon"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.coui.appcompat.textview.COUITextView
        android:id="@+id/ad_max_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:textSize="@dimen/font_size_14"
        android:textDirection="locale"
        android:fontFamily="sans-serif-medium"
        android:textColor="@color/coui_color_primary_neutral"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="title" />

    <LinearLayout
        android:id="@+id/ad_max_desc_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/dimen_9dp"
        app:layout_constraintTop_toBottomOf="@id/ad_max_title"
        app:layout_constraintStart_toStartOf="parent">

        <com.coui.appcompat.textview.COUITextView
            android:id="@+id/ad_max_flag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:textDirection="locale"
            android:background="@drawable/bg_ad_flag"
            android:textColor="@color/coui_color_secondary_neutral"
            android:textSize="@dimen/font_size_10"
            android:text="AD" />

        <com.coui.appcompat.textview.COUITextView
            android:id="@+id/ad_max_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textDirection="locale"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="2"
            android:textSize="@dimen/font_size_10"
            android:textColor="@color/coui_color_secondary_neutral"
            android:layout_gravity="top"
            android:layout_marginStart="@dimen/dimen_3dp"
            android:lineSpacingMultiplier="1.2"
            tools:text="body" />
    </LinearLayout>

    <com.filemanager.common.view.MediaFrameLayout
        android:id="@+id/ad_max_media_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_12dp"
        app:isLargeLayout="true"
        app:layout_radius="@dimen/dimen_8dp"
        app:layout_constraintTop_toBottomOf="@id/ad_max_desc_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <com.coui.appcompat.button.COUIButton
        android:id="@+id/ad_max_button"
        android:layout_height="@dimen/dimen_30dp"
        android:layout_width="0dp"
        android:textSize="@dimen/font_size_12"
        android:padding="0dp"
        android:fontFamily="sans-serif-medium"
        app:animEnable="true"
        android:ellipsize="end"
        app:drawableColor="@color/coui_color_primary_green"
        app:drawableRadius="-1dp"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:gravity="center"
        android:textColor="@android:color/white"
        android:lineHeight="@dimen/dimen_30dp"
        android:layout_marginTop="@dimen/dimen_12dp"
        app:layout_constraintTop_toBottomOf="@id/ad_max_media_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="install" />

    <include layout="@layout/ad_max_placeholder" android:visibility="gone"/>

</androidx.constraintlayout.widget.ConstraintLayout>