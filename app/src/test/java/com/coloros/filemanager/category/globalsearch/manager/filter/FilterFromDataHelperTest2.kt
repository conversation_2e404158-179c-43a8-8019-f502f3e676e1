/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FilterFromDataHelperTest2
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/07/28
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  yanshengneng    2022/09/09       1.0      filterData#FILTER_FROM_TRANSFER
 ***********************************************************************/
package com.coloros.filemanager.category.globalsearch.manager.filter

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.FilterConstants
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.category.globalsearch.bean.SearchLabelWrapper
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterCondition
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterFromDataHelper
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.room.model.FileLabelEntity
import com.oplus.filemanager.room.model.FileLabelMappingEntity
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import java.io.File
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module

@RunWith(JUnit4::class)
class FilterFromDataHelperTest2 {

    @MockK
    private lateinit var context: Context

    private val superApp = mockk<ISuperApp>()

    private val koinApp = koinApplication {
        modules(module {
            single { superApp }
        })
    }

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkStatic(FilterFromDataHelper::class)
        mockkStatic(VolumeEnvironment::class)
        every { context.applicationContext } returns context
        MyApplication.init(context)
        every { VolumeEnvironment.getExternalSdPath(context) } returns "${File.separator}test${File.separator}ExternalSdPath"
        every { VolumeEnvironment.getInternalSdPath(context) } returns "${File.separator}test${File.separator}InternalSdPath"
        mockkStatic(FeatureCompat::class)
        mockkStatic(Utils::class)
        startKoin(koinApp)
    }

    @After
    fun tearDown() {
        unmockkStatic(FilterFromDataHelper::class)
        unmockkStatic(VolumeEnvironment::class)
        mockkStatic(FeatureCompat::class)
        unmockkStatic(Utils::class)
        stopKoin()
    }

    @Test
    fun testFilterOWorkData() {
        every { VolumeEnvironment.isNeedLoadPath(context, any()) } returns false
        val dataHelper = spyk(FilterFromDataHelper())
        val condition = FilterCondition(FilterConstants.FILTER_FROM_OWORK, "")
        val filterItem = FilterItem(FilterConstants.FILTER_FROM_OWORK, condition, "", null)
        every { superApp.checkOWorkSuperAppCondition(any()) } returns true
        val dataList = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val label = FileLabelEntity(1, "name", 1, 1, 1, 0)
            val fileBean = SearchLabelWrapper(label)
            dataList.add(fileBean)
        }

        val resultList = dataHelper.filterData(filterItem, dataList)
        assertEquals(0, resultList.size)
        every { superApp.checkOWorkSuperAppCondition(any()) } returns false
        assertEquals(10, dataHelper.filterData(filterItem, dataList).size)
    }

    @Test
    fun `should return 0 when filter data if label filterList is empty and is realMe`() {
        every { Utils.isRealmePhone() } returns true
        every { VolumeEnvironment.isNeedLoadPath(context, any()) } returns false
        val dataHelper = spyk(FilterFromDataHelper())
        val filterItem = FilterItem(FilterConstants.FILTER_FROM_TRANSFER, mockk(), "", "")
        val dataList = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val label = FileLabelEntity(1, "name", 1, 1, 1, 0)
            val fileBean = SearchLabelWrapper(label)
            dataList.add(fileBean)
        }
        val resultList = dataHelper.filterData(filterItem, dataList)
        assertEquals(0, resultList.size)
    }

    @Test
    fun `should return 0 when filter data if label filterList is empty and is not realMe`() {
        every { Utils.isRealmePhone() } returns false
        every { VolumeEnvironment.isNeedLoadPath(context, any()) } returns false
        val dataHelper = spyk(FilterFromDataHelper())
        val filterItem = FilterItem(FilterConstants.FILTER_FROM_TRANSFER, mockk(), "", "")
        val dataList = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val label = FileLabelEntity(1, "name", 1, 1, 1, 0)
            val fileBean = SearchLabelWrapper(label)
            dataList.add(fileBean)
        }
        val resultList = dataHelper.filterData(filterItem, dataList)
        assertEquals(0, resultList.size)
    }

    @Test
    fun `should return 10 when filter data if label filterList`() {
        every { Utils.isRealmePhone() } returns false
        every { VolumeEnvironment.isNeedLoadPath(context, any()) } returns false
        val dataHelper = spyk(FilterFromDataHelper())
        val filterItem = FilterItem(FilterConstants.FILTER_FROM_TRANSFER, mockk(), "", "")
        val dataList = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val label = FileLabelEntity(1, "name", 1, 1, 1, 0)
            val fileBean = SearchLabelWrapper(label)
            for (indexPath in 1..10) {
                val labelFilter = FileLabelMappingEntity(
                    1,
                    1,
                    "${File.separator}TEST${File.separator}EXTERNALSDPATH" +
                            "${File.separator}DOCUMENTS${File.separator}OPPO SHARE" +
                            "${File.separator}file$indexPath",
                    1,
                    "",
                    0,
                    0
                )
                fileBean.fileListFilter.add(labelFilter)
            }
            dataList.add(fileBean)
        }
        val resultList = dataHelper.filterData(filterItem, dataList)
        assertEquals(10, resultList.size)
    }

    @Test
    fun `should return 10 when filter data if label filterList and not start with`() {
        every { Utils.isRealmePhone() } returns false
        every { VolumeEnvironment.isNeedLoadPath(context, any()) } returns false
        val dataHelper = spyk(FilterFromDataHelper())
        val filterItem = FilterItem(FilterConstants.FILTER_FROM_TRANSFER, mockk(), "", "")
        val dataList = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val label = FileLabelEntity(1, "name", 1, 1, 1, 0)
            val fileBean = SearchLabelWrapper(label)
            for (indexPath in 1..10) {
                val labelFilter = FileLabelMappingEntity(
                    1,
                    1,
                    "${File.separator}TEST${File.separator}EXTERNALSDPATH",
                    1,
                    "",
                    0,
                    0
                )
                fileBean.fileListFilter.add(labelFilter)
            }
            dataList.add(fileBean)
        }
        val resultList = dataHelper.filterData(filterItem, dataList)
        assertEquals(0, resultList.size)
    }

    @Test
    fun `should return 10 when filter data if label filterList and is realMe`() {
        every { Utils.isRealmePhone() } returns true
        every { VolumeEnvironment.isNeedLoadPath(context, any()) } returns false
        val dataHelper = spyk(FilterFromDataHelper())
        val filterItem = FilterItem(FilterConstants.FILTER_FROM_TRANSFER, mockk(), "", "")
        val dataList = arrayListOf<BaseFileBean>()
        val fileHead = "${File.separator}TEST${File.separator}EXTERNALSDPATH" +
                "${File.separator}DOCUMENTS${File.separator}REALME SHARE${File.separator}file"
        for (index in 1..10) {
            val label = FileLabelEntity(1, "name", 1, 1, 1, 0)
            val fileBean = SearchLabelWrapper(label)
            for (indexPath in 1..10) {
                val labelFilter = FileLabelMappingEntity(
                    1,
                    1,
                    "$fileHead$indexPath",
                    1,
                    "",
                    0,
                    0
                )
                fileBean.fileListFilter.add(labelFilter)
            }
            dataList.add(fileBean)
        }
        val resultList = dataHelper.filterData(filterItem, dataList)
        assertEquals(10, resultList.size)
    }

    @Test
    fun `should return 10 when filter data if label filterList and not start with and is realMe`() {
        every { Utils.isRealmePhone() } returns true
        every { VolumeEnvironment.isNeedLoadPath(context, any()) } returns false
        val dataHelper = spyk(FilterFromDataHelper())
        val filterItem = FilterItem(FilterConstants.FILTER_FROM_TRANSFER, mockk(), "", "")
        val dataList = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val label = FileLabelEntity(1, "name", 1, 1, 1, 0)
            val fileBean = SearchLabelWrapper(label)
            for (indexPath in 1..10) {
                val labelFilter = FileLabelMappingEntity(1, 1, "${File.separator}TEST${File.separator}EXTERNALSDPATH", 1, "", 0, 0)
                fileBean.fileListFilter.add(labelFilter)
            }
            dataList.add(fileBean)
        }
        val resultList = dataHelper.filterData(filterItem, dataList)
        assertEquals(0, resultList.size)
    }


}