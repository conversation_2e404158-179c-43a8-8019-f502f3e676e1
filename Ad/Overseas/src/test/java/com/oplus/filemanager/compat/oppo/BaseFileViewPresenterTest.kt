/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: BaseFileViewPresenterTest.kt
 ** Description: unit test
 ** Version: 1.0
 ** Date : 2022/7/26
 ** Author: YanShengNeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * YanShengNeng 2022/7/14      1.0    create
 ****************************************************************/
package com.oplus.filemanager.compat.oppo

import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.helper.VolumeEnvironment
import com.oplus.filemanager.compat.brand.oppo.BaseFileViewPresenter
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import java.io.File
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import org.mockito.Answers
import org.mockito.Mockito

@RunWith(JUnit4::class)
class BaseFileViewPresenterTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `assert result when createItem`() {
        val baseFileViewPresenter = Mockito.mock(BaseFileViewPresenter::class.java, Answers.CALLS_REAL_METHODS)
        val createItem = baseFileViewPresenter.createItem()
        Assert.assertNotNull(createItem)
        Assert.assertEquals(BaseFileBean.TYPE_FILE_AD, createItem.mFileWrapperViewType)
    }

    @Test
    fun `should return false when shouldDisplayByPath if not isFirstPage`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list = arrayListOf<BaseFileBean>()
        val layoutManager = mockk<GridLayoutManager>()
        val albumPresenter = spyk(BaseFileViewPresenter(activity, adapter, list, layoutManager))
        every { albumPresenter.shouldDisplayByPath() } answers { callOriginal() }
        Assert.assertEquals(false, albumPresenter.shouldDisplayByPath())
    }

    @Test
    fun `should return true when shouldDisplayByPath if not isFirstPage and first file is data is null`() {
        val adapter = mockk<BaseSelectionRecycleAdapter<*, BaseFileBean>> {
            every { getRecyclerView() } returns null
        }
        val activity = mockk<FragmentActivity>()
        val list = arrayListOf<BaseFileBean>()
        list.add(BaseFileBean())
        val layoutManager = mockk<GridLayoutManager>()
        val albumPresenter = spyk(BaseFileViewPresenter(activity, adapter, list, layoutManager))
        every { albumPresenter.shouldDisplayByPath() } answers { callOriginal() }
        Assert.assertEquals(true, albumPresenter.shouldDisplayByPath())
    }

    @Test
    fun `should return false when shouldDisplayByPath if not isFirstPage and first file is internal sd path`() {
        mockkStatic(VolumeEnvironment::class)
        val adapter = mockk<BaseSelectionRecycleAdapter<*, BaseFileBean>> {
            every { getRecyclerView() } returns null
        }
        val activity = mockk<FragmentActivity>()
        val filePath = "${File.separator}0${File.separator}storage${File.separator}my${File.separator}files"
        every { VolumeEnvironment.getInternalSdPath(activity) } returns filePath
        val list = arrayListOf<BaseFileBean>()
        val firstFile = BaseFileBean()
        firstFile.mData = filePath
        list.add(firstFile)
        val layoutManager = mockk<GridLayoutManager>()
        val albumPresenter = spyk(BaseFileViewPresenter(activity, adapter, list, layoutManager))
        every { albumPresenter.shouldDisplayByPath() } answers { callOriginal() }
        Assert.assertEquals(false, albumPresenter.shouldDisplayByPath())
        unmockkStatic(VolumeEnvironment::class)
    }

    @Test
    fun `should return false when shouldDisplayByPath if not isFirstPage and first file is not internal sd path`() {
        mockkStatic(VolumeEnvironment::class)
        val adapter = mockk<BaseSelectionRecycleAdapter<*, BaseFileBean>> {
            every { getRecyclerView() } returns null
        }
        val activity = mockk<FragmentActivity>()
        every { VolumeEnvironment.getInternalSdPath(activity) } returns "${File.separator}0${File.separator}storage${File.separator}android"
        val list = arrayListOf<BaseFileBean>()
        val firstFile = BaseFileBean()
        firstFile.mData = "${File.separator}0${File.separator}storage${File.separator}my${File.separator}files"
        list.add(firstFile)
        val layoutManager = mockk<GridLayoutManager>()
        val albumPresenter = spyk(BaseFileViewPresenter(activity, adapter, list, layoutManager))
        every { albumPresenter.shouldDisplayByPath() } answers { callOriginal() }
        Assert.assertEquals(false, albumPresenter.shouldDisplayByPath())
        unmockkStatic(VolumeEnvironment::class)
    }

    private fun mockAdapter(): BaseSelectionRecycleAdapter<*, BaseFileBean> {
        val recyclerView = mockRecyclerView()
        return mockk {
            every { getRecyclerView() } returns recyclerView
        }
    }

    private fun mockRecyclerView(): RecyclerView {
        return mockk {
            justRun { tag = any() }
            justRun { addOnScrollListener(any()) }
        }
    }
}