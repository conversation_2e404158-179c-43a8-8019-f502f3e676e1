/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudDriveKitApi
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/3/11
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798        2024/1/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.clouddrivekit

import android.app.Application
import com.oplus.filemanager.interfaze.clouddrivekit.ICloudDriveKit

class CloudDriveKitApi : ICloudDriveKit {

    override fun initCloudkitSDK(application: Application) {
        CloudDriveKitManager.initCloudkitSDK(application)
    }

    override fun isCloudSpaceFull(application: Application, callback: (Boolean?) -> Unit) {
        CloudDriveKitManager.isCloudSpaceFull(application, callback)
    }
}