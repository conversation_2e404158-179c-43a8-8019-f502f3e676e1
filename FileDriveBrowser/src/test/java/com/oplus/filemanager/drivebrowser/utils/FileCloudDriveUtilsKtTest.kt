/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : FileCloudDriveUtilsKtTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/29 19:52
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/29       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.stringResource
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.Assert
import org.junit.Test

class FileCloudDriveUtilsKtTest {
    @Test
    fun testGetFileCloudDriveTitle() {
        mockkStatic("com.filemanager.common.utils.StringResourcesKt")
        every { stringResource(any()) }.returns(TITLE)
        val tencentDocsTitle = getFileCloudDriveTitle(CategoryHelper.CATEGORY_TENCENT_DOCS)
        Assert.assertEquals(TITLE, tencentDocsTitle)
        val kdocsTitle = getFileCloudDriveTitle(CategoryHelper.CATEGORY_K_DOCS)
        Assert.assertEquals(TITLE, kdocsTitle)
        unmockkStatic("com.filemanager.common.utils.StringResourcesKt")
    }

    companion object {
        private const val TITLE = "CLOUD_DOCS"
    }
}