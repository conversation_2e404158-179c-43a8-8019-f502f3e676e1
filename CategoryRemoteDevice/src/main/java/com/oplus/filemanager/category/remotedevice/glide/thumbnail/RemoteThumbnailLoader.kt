/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemoteThumbnailLoader.kt
 ** Description: RemoteThumbnailLoader
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.category.remotedevice.glide.thumbnail

import android.content.Context
import android.graphics.Bitmap
import com.bumptech.glide.load.Options
import com.bumptech.glide.load.model.ModelLoader
import com.bumptech.glide.signature.ObjectKey
import com.filemanager.common.base.RemoteFileBean

class RemoteThumbnailLoader(
    private val context: Context
) : ModelLoader<RemoteFileBean, Bitmap> {
    override fun buildLoadData(
        model: RemoteFileBean,
        width: Int,
        height: Int,
        options: Options
    ): ModelLoader.LoadData<Bitmap> = ModelLoader.LoadData(
        ObjectKey(model),
        RemoteThumbnailFetcher(context, model, width, height)
    )

    override fun handles(model: RemoteFileBean): Boolean = true
}
