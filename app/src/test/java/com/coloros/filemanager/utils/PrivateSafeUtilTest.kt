/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PrivateSafeUtilTest
 * * Description: unit test for PrivateSafeUtil
 * * Version: 1.0
 * * Date : 2021/12/30
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>              <data>      <version >        <desc>
 * * <EMAIL>    2021/12/30       1.0         unit test for PrivateSafeUtil
 ****************************************************************/
package com.coloros.filemanager.utils

import com.filemanager.common.utils.PrivateSafeUtil
import io.mockk.mockkStatic
import org.junit.Assert
import org.junit.Test

class PrivateSafeUtilTest  {

    @Test
    fun `should return false when catch exception`() {
        mockkStatic(PrivateSafeUtil::class) {
            val result = try {
                PrivateSafeUtil.checkIsDisabledPrivateGarden()
            } catch (e: Throwable) {
                false
            }
            Assert.assertFalse(result)
        }
    }
}