/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileLabelRestorePlugin
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/28
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/7/28      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.backuprestore

import android.content.Context
import android.database.sqlite.SQLiteException
import android.os.Bundle
import androidx.annotation.VisibleForTesting
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.EncryptViewUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils.put
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.oplus.backup.sdk.common.host.BREngineConfig
import com.oplus.backup.sdk.component.BRPluginHandler
import com.oplus.backup.sdk.component.plugin.RestorePlugin
import com.oplus.backup.sdk.host.listener.ProgressHelper
import com.oplus.filemanager.backuprestore.BRUtils.getContentFromFile
import com.oplus.filemanager.cardwidget.label.LabelCardWidgetCodeUtils
import com.oplus.filemanager.cardwidget.label.data.LabelCardCacheData
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.provider.SearchHistoryDBHelper
import com.oplus.filemanager.provider.ThirdAppFileDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import com.oplus.filemanager.room.model.FileLabelMappingEntity
import com.oplus.filemanager.room.model.SearchHistoryEntity
import com.oplus.filemanager.room.model.ThirdAppFileDataEntity
import java.io.File
import java.lang.reflect.Type

class FileManagerRestorePlugin : RestorePlugin() {

    companion object {
        const val TAG = "FileManagerRestorePlugin"
        const val SHARED_PREFS_NAME = "com.filemanager_preferences"
    }

    lateinit var backupPath: String
    @VisibleForTesting
    var isCancel: Boolean = false
    @VisibleForTesting
    var localLabelNameIdMaps = mutableMapOf<String, Long>()
    @VisibleForTesting
    var backupLabelIdNameMaps = mutableMapOf<Long, String>()
    /** 备分的标签卡片，标签的搬迁前的ID与搬迁后的ID映射 */
    @VisibleForTesting
    var backupLabelCardIdMaps = mutableMapOf<Long, Long>()

    override fun onCreate(context: Context?, brPluginHandler: BRPluginHandler?, config: BREngineConfig?) {
        super.onCreate(context, brPluginHandler, config)
        if (config != null) {
            backupPath = config.restoreRootPath + File.separator + FILE_MANAGER_FOLDER
        }
        localLabelNameIdMaps.clear()
        backupLabelIdNameMaps.clear()
        backupLabelCardIdMaps.clear()
    }

    override fun onPreview(p0: Bundle?): Bundle = Bundle()

    override fun onPrepare(p0: Bundle?): Bundle = Bundle().apply { putInt(ProgressHelper.MAX_COUNT, 1) }

    override fun onRestore(p0: Bundle?) {
        Log.v(TAG, "onRestore")
        restoreLabels()
        restoreMappings()
        restoreLabelCardsMapping()
        restoreSettingValues()
        restoreScanModeValues()
        restoreAllSortMode()
        restoreAllSearchHistory()
        restoreAllThirdAppData()
    }

    override fun onPause(p0: Bundle?) {
        // do nothing
    }

    @Suppress("TooGenericExceptionCaught")
    @VisibleForTesting
    fun restoreLabelCardsMapping() {
        try {
            val filePath: String = backupPath + File.separator + FILE_LABEL_CARD_MAPPING_BACKUP_FILE
            val backupContent = getContentFromFile(this.getFileDescriptor(filePath))
            Log.v(TAG, "restoreLabelCardsMapping backupContent = $backupContent")
            val listType: Type = object : TypeToken<List<LabelCardCacheData>>() {}.type
            val moveFromList: List<LabelCardCacheData> = Gson().fromJson(backupContent, listType)
            //没有搬过来的数据，直接返回
            if (moveFromList.isEmpty()) return
            val codeUtils = LabelCardWidgetCodeUtils.instance
            val localList = arrayListOf<LabelCardCacheData>().apply { addAll(codeUtils.getCacheList()) }
            codeUtils.clearAll()
            moveFromList.forEach { remote ->
                Log.d(TAG, "moveFromList.forEach $remote = ${backupLabelCardIdMaps[remote.labelId]}")
                remote.labelId = backupLabelCardIdMaps[remote.labelId]
                codeUtils.saveCard(remote.widgetCode, remote)
            }
            if (moveFromList.size < localList.size) {
                //搬过来的比本地的少，替换前面几项
                val result = sortedLocalList(localList, codeUtils)
                val takeSize = localList.size - moveFromList.size
                if (takeSize > 0) {
                    result.takeLast(takeSize).forEach { local ->
                        codeUtils.saveCard(local.widgetCode, local)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "restoreLabelCardsMapping e:$e")
        }
    }

    private fun sortedLocalList(
        localList: ArrayList<LabelCardCacheData>,
        codeUtils: LabelCardWidgetCodeUtils
    ): List<LabelCardCacheData> {
        val result = localList.sortedWith { cache1, cache2 ->
            if (codeUtils.getWidgetCardId(cache1.widgetCode) > codeUtils.getWidgetCardId(cache2.widgetCode)) {
                1
            } else if ((codeUtils.getWidgetCardId(cache1.widgetCode) < codeUtils.getWidgetCardId(cache2.widgetCode))) {
                -1
            } else {
                if (codeUtils.getWidgetHostId(cache1.widgetCode) > codeUtils.getWidgetHostId(cache2.widgetCode)) {
                    1
                } else if ((codeUtils.getWidgetHostId(cache1.widgetCode) < codeUtils.getWidgetHostId(cache2.widgetCode))) {
                    -1
                } else {
                    0
                }
            }
        }
        return result
    }

    @Suppress("TooGenericExceptionCaught")
    @VisibleForTesting
    fun restoreLabels() {
        try {
            val filePath: String = backupPath + File.separator + FILE_LABEL_BACKUP_FILE
            Log.v(TAG, "restoreLabels filePath = $filePath")
            val backupContent = getContentFromFile(this.getFileDescriptor(filePath))
            Log.v(TAG, "restoreLabels backupContent = $backupContent")
            val listType: Type = object : TypeToken<List<FileLabelEntity>>() {}.type
            val backupLabelList: List<FileLabelEntity> = Gson().fromJson(backupContent, listType)
            backupLabelList.forEach { backupFileLabelEntity ->
                val label = FileLabelDBHelper.getFileLabelByName(backupFileLabelEntity.name)
                val backupLabelId = backupFileLabelEntity.id
                val newLabelId: Long
                if (label != null) {
                    mergeLabel(backupFileLabelEntity, label)
                    newLabelId = label.id
                } else {
                    val insertId = insertLabel(backupFileLabelEntity)
                    newLabelId = insertId ?: backupFileLabelEntity.id
                }
                backupLabelIdNameMaps[backupFileLabelEntity.id] = backupFileLabelEntity.name
                backupLabelCardIdMaps[backupLabelId] = newLabelId
                Log.v(TAG, "restoreLabels label = $backupFileLabelEntity")
            }
        } catch (e: Exception) {
            Log.e(TAG, "restoreLabels e = $e")
        }
    }

    @VisibleForTesting
    fun mergeLabel(backupLabel: FileLabelEntity, localLabel: FileLabelEntity) {
        Log.v(TAG, "mergeLabel backupLabel = $backupLabel, localLabel = $localLabel")
        var localLabelChangedAfterMerge = false
        if (backupLabel.viewCount > localLabel.viewCount) {
            localLabel.viewCount = backupLabel.viewCount
            localLabelChangedAfterMerge = true
        }
        if (backupLabel.useCount > localLabel.useCount) {
            localLabel.useCount = backupLabel.useCount
            localLabelChangedAfterMerge = true
        }
        if (backupLabel.pinTimeStamp > localLabel.pinTimeStamp) {
            localLabel.pinTimeStamp = backupLabel.pinTimeStamp
            localLabelChangedAfterMerge = true
        }
        if (backupLabel.lastUsedTime > localLabel.lastUsedTime) {
            localLabel.lastUsedTime = backupLabel.lastUsedTime
            localLabelChangedAfterMerge = true
        }
        if (localLabelChangedAfterMerge) {
            try {
                FileLabelDBHelper.updateFileLabel(localLabel)
            } catch (e: SQLiteException) {
                Log.e(TAG, "mergeLabel:$e")
            }
        }
        localLabelNameIdMaps[localLabel.name] = localLabel.id
    }

    @VisibleForTesting
    fun insertLabel(fileLabelEntity: FileLabelEntity): Long? {
        Log.v(TAG, "insertLabel fileLabelEntity = $fileLabelEntity")
        val insertId = FileLabelDBHelper.insertFileLabel(
            FileLabelEntity(
                id = 0,
                name = fileLabelEntity.name,
                viewCount = fileLabelEntity.viewCount,
                useCount = fileLabelEntity.useCount,
                pinTimeStamp = fileLabelEntity.pinTimeStamp,
                lastUsedTime = fileLabelEntity.lastUsedTime
            )
        )
        insertId?.let { localLabelNameIdMaps[fileLabelEntity.name] = it }
        return insertId
    }

    @Suppress("TooGenericExceptionCaught")
    @VisibleForTesting
    fun restoreMappings() {
        try {
            val filePath: String = backupPath + File.separator + FILE_LABEL_MAPPING_BACKUP_FILE
            Log.v(TAG, "restoreMappings filePath = $filePath")
            val backupContent = getContentFromFile(this.getFileDescriptor(filePath))
            Log.v(TAG, "restoreMappings backupContent = $backupContent")
            val listType: Type = object : TypeToken<List<FileLabelMappingEntity>>() {}.type
            val backupMappingList: List<FileLabelMappingEntity> = Gson().fromJson(backupContent, listType)
            backupMappingList.forEach { backupFileLabelMappingEntity ->
                val backupLabelId = backupFileLabelMappingEntity.labelId
                val labelName = backupLabelIdNameMaps[backupLabelId]
                val localLabelId = localLabelNameIdMaps[labelName]
                localLabelId?.let {
                    val localMappingEntity = FileLabelMappingDBHelper.getFileByLabelIdAndPath(
                        it, backupFileLabelMappingEntity.filePath
                    )
                    if (localMappingEntity != null) {
                        mergeFileMapping(localMappingEntity, backupFileLabelMappingEntity)
                    } else {
                        insertMapping(localLabelId, backupFileLabelMappingEntity)
                    }
                }
                Log.v(TAG, "restoreMappings mapping = $backupFileLabelMappingEntity")
            }
        } catch (e: Exception) {
            Log.e(TAG, "restoreMappings e:$e")
        }
    }

    @VisibleForTesting
    fun mergeFileMapping(localMappingEntity: FileLabelMappingEntity, backupFileLabelMappingEntity: FileLabelMappingEntity) {
        var localMappingChangedAfterMerge = false
        if (backupFileLabelMappingEntity.timestamp > localMappingEntity.timestamp) {
            localMappingEntity.timestamp = backupFileLabelMappingEntity.timestamp
            localMappingChangedAfterMerge = true
        }
        if (localMappingChangedAfterMerge) {
            FileLabelMappingDBHelper.updateFileLabelMappingEntity(
                localMappingEntity
            )
        }
    }

    @VisibleForTesting
    fun insertMapping(localLabelId: Long, fileLabelMappingEntity: FileLabelMappingEntity) {
        FileLabelMappingDBHelper.insertFileLabelMappingEntity(
            FileLabelMappingEntity(
                id = 0,
                labelId = localLabelId,
                filePath = fileLabelMappingEntity.filePath,
                localType = fileLabelMappingEntity.localType,
                mimeType = fileLabelMappingEntity.mimeType,
                timestamp = fileLabelMappingEntity.timestamp,
                duration = fileLabelMappingEntity.duration
            )
        )
    }

    override fun onContinue(p0: Bundle?) {
        isCancel = false
    }

    override fun onCancel(p0: Bundle?) {
        isCancel = true
    }

    override fun onDestroy(p0: Bundle?): Bundle {
        val resultBundle = Bundle()
        ProgressHelper.putBRResult(resultBundle, if (isCancel) ProgressHelper.BR_RESULT_CANCEL else ProgressHelper.BR_RESULT_SUCCESS)
        ProgressHelper.putCompletedCount(resultBundle, 1)
        ProgressHelper.putMaxCount(resultBundle, 1)
        Log.d(TAG, "onDestroy resultBundle = $resultBundle, ${Thread.currentThread().name}")
        return resultBundle
    }

    /**恢复设置项数据*/
    @Suppress("TooGenericExceptionCaught")
    @VisibleForTesting
    fun restoreSettingValues() {
        try {
            Log.d(TAG, "restoreSettingValues")
            val filePath: String = backupPath + File.separator + SETTING_VALUES_BACKUP_FILE
            val backupContent = getContentFromFile(this.getFileDescriptor(filePath)) ?: return
            Log.d(TAG, "restoreSettingValues backupContent = $backupContent")
            val listType: Type = object : TypeToken<List<BackupBean>>() {}.type
            val backupLabelList: List<BackupBean> = Gson().fromJson(backupContent, listType)
            backupLabelList.forEach { backupBean ->
                backupBean.key?.let {
                    if (it == CommonConstants.AD_SWITCH_STATUS) {
                        Log.d(TAG, "restoreSettingValues ignore AD_SWITCH_STATUS")
                    } else {
                        put(key = it, value = backupBean.settingValue)
                    }
                    when (it) {
                        CommonConstants.NEED_SHOW_HIDDEN_FILES ->
                            HiddenFileHelper.mIsNeedShowHiddenFile = backupBean.settingValue

                        CommonConstants.NEED_SHOW_ENCRYPT_BOX ->
                            EncryptViewUtils.mIsNeedShowEncryptBox = backupBean.settingValue

                        CommonConstants.THIRD_APP_SEARCH_FUNCTION_SHOW -> processRestoreThirdAppSwitchStatus(backupBean)
                        CommonConstants.NEED_SHOW_RECENT_CAMERA ->
                            HiddenFileHelper.mIsHideCameraScreenshotInRecentOpen = backupBean.settingValue

                        AndroidDataHelper.PREF_ANDROID_DATA_ACCESS ->
                            AndroidDataHelper.openAndroidData = backupBean.settingValue
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "restoreSettingValues e:$e")
        }
    }

    @Suppress("TooGenericExceptionCaught")
    @VisibleForTesting
    fun restoreScanModeValues() {
        try {
            Log.d(TAG, "restoreScanModeValues")
            val filePath: String = backupPath + File.separator + SCAN_MODE_VALUES_BACKUP_FILE
            val backupContent = getContentFromFile(this.getFileDescriptor(filePath)) ?: return
            Log.d(TAG, "restoreScanModeValues backupContent = $backupContent")
            val listType: Type = object : TypeToken<List<BackupBean>>() {}.type
            val backupLabelList: List<BackupBean> = Gson().fromJson(backupContent, listType)
            backupLabelList.forEach { backupBean ->
                backupBean.key?.let { ConfigSharedPreferenceUtils.putInt(key = it, value = backupBean.scanMode) }
            }
        } catch (e: Exception) {
            Log.e(TAG, "restoreScanModeValues e:$e")
        }
    }

    @Suppress("TooGenericExceptionCaught")
    fun restoreAllSortMode() {
        try {
            Log.d(TAG, "restoreAllSortMode")
            val filePath: String = backupPath + File.separator + ALL_SORT_BACKUP_FILE
            val backupContent = getContentFromFile(this.getFileDescriptor(filePath)) ?: return
            Log.d(TAG, "restoreAllSortMode backupContent = $backupContent")
            val listType: Type = object : TypeToken<List<BackupBean>>() {}.type
            val backupLabelList: List<BackupBean> = Gson().fromJson(backupContent, listType)
            if (backupLabelList.isNullOrEmpty()) {
                Log.d(TAG, "restoreAllSortMode backupLabelList is null or empty")
                return
            }
            backupLabelList.forEach { bean ->
                val recordKey = bean.key ?: ""
                put(SHARED_PREFS_NAME, recordKey, bean.recordSortMode)
                bean.isDesc?.let { SortModeUtils.putSharedSortOrder(recordKey, it) }
            }
        } catch (e: Exception) {
            Log.e(TAG, "restoreAllSortMode e:$e")
        }
    }

    @Suppress("TooGenericExceptionCaught")
    fun restoreAllSearchHistory() {
        try {
            val filePath: String = backupPath + File.separator + ALL_SEARCH_HISTORY_BACKUP_FILE
            val backupContent = getContentFromFile(this.getFileDescriptor(filePath)) ?: return
            Log.d(TAG, "restoreAllSearchHistory backupContent = $backupContent")
            val listType: Type = object : TypeToken<List<SearchHistoryEntity>>() {}.type
            val backupSearchHistoryList: List<SearchHistoryEntity> = Gson().fromJson(backupContent, listType) ?: return
            val localSearchHistoryList: List<SearchHistoryEntity>? = SearchHistoryDBHelper.getAllSearchHistory()

            Log.d(TAG, "backupSearchHistoryList = $backupSearchHistoryList")
            Log.d(TAG, "localSearchHistoryList =  $localSearchHistoryList")
            if (!localSearchHistoryList.isNullOrEmpty()) {
                val newSearchHistoryList = mergeSearchHistoryList(backupSearchHistoryList, localSearchHistoryList)
                Log.d(TAG, "newSearchHistoryList = $newSearchHistoryList")
                for (index in newSearchHistoryList.size - 1 downTo 0) {
                    newSearchHistoryList[index].mSearchContent?.let {
                        SearchHistoryDBHelper.addSearchHistory(it)
                    }
                }
            } else {
                for (index in backupSearchHistoryList.size - 1 downTo 0) {
                    backupSearchHistoryList[index].mSearchContent?.let {
                        SearchHistoryDBHelper.addSearchHistory(it)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "restoreAllSearchHistory e:$e")
        }
    }

    @Suppress("ParameterStyleBracesRule")
    private fun mergeSearchHistoryList(
            backupSearchHistoryList: List<SearchHistoryEntity>,
            localSearchHistoryList: List<SearchHistoryEntity>
    ): MutableList<SearchHistoryEntity> {
        Log.d(TAG, "mergeSearchHistoryList")
        val newSearchHistoryList = mutableListOf<SearchHistoryEntity>()
        backupSearchHistoryList.forEach { backupSearchHistoryEntity ->
            var isContain = false
            kotlin.run {
                localSearchHistoryList.forEach{ localSearchHistoryEntity ->
                    if (backupSearchHistoryEntity.mSearchContent.equals(localSearchHistoryEntity.mSearchContent)) {
                        isContain = true
                        return@run
                    }
                }
            }
            if (!isContain) {
                newSearchHistoryList.add(backupSearchHistoryEntity)
            }
        }
        return newSearchHistoryList
    }

    fun processRestoreThirdAppSwitchStatus(backupBean: BackupBean) {
        val thirdAppSwitch = backupBean.settingValue
        if (thirdAppSwitch != null) {
            val globalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
            globalSearchApi?.notifyThirdAppSearchSwitchStatus(thirdAppSwitch)
            Log.i(TAG, "processRestoreThirdAppSwitchStatus switch $thirdAppSwitch")
            val dmpApi = Injector.injectFactory<IDmpSearchApi>()
            dmpApi?.updateSwitchSpToDmp()
        }
    }

    fun restoreAllThirdAppData() {
        runCatching {
            val filePath: String = backupPath + File.separator + FILE_THIRD_APP_BACKUP_FILE
            val backupContent = getContentFromFile(this.getFileDescriptor(filePath)) ?: return
            val listType: Type = object : TypeToken<List<ThirdAppFileDataEntity>>() {}.type
            val backupThirdAppDataList: List<ThirdAppFileDataEntity> =
                Gson().fromJson(backupContent, listType) ?: return
            val localThirdAppDataList: List<ThirdAppFileDataEntity>? =
                ThirdAppFileDBHelper.getAllThirdAppFileData()
            Log.d(TAG, "backupThirdAppDataList = ${backupThirdAppDataList.map {
                        it.id.toString().plus(":").plus(it.mFileName).plus(":").plus(it.mSourceName)
                            .plus(":").plus(it.mDectPackage)}}")
            Log.d(TAG, "localThirdAppDataList =  ${localThirdAppDataList?.map {
                        it.id.toString().plus(":").plus(it.mFileName).plus(":").plus(it.mSourceName)
                            .plus(":").plus(it.mDectPackage)}}")
            ThirdAppFileDataRestoreHelper.compareAndRestoreThirdFileDataList(
                backupThirdAppDataList,
                localThirdAppDataList
            )
        }.onFailure {
            Log.e(TAG, "restoreAllThirdAppData error", it)
        }
    }
}