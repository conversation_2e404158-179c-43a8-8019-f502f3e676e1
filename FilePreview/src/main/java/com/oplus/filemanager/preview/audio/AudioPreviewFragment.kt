/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AudioPreviewFragment.kt
 * Description:
 *     The page to preview audio file.
 *
 * Version: 1.0
 * Date: 2024-10-09
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-10-09   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.audio

import android.content.Context
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewFragment
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import com.oplus.filemanager.preview.widget.PreviewAudioCard
import com.oplus.filemanager.preview.widget.PreviewFilePathItem

internal class AudioPreviewFragment :
    AbsPreviewFragment<AudioPreviewViewModel>(R.layout.fragment_preview_audio) {

    private companion object {
        private const val TAG = "AudioPreviewFragment"
    }

    override val logTag: String = TAG
    override val fragmentInstance: Fragment = this
    override val viewModelClass: Class<AudioPreviewViewModel> = AudioPreviewViewModel::class.java
    override val operationsBarId: Int = R.id.preview_operations_bar

    private var filePreview: IAudioFilePreview? = null

    override fun isPreviewFileApproved(context: Context, fileBean: BaseFileBean): Boolean =
        FilePreviewTypeCheckHelper(context).isAudioFileType(fileBean, TAG)

    override fun onViewModelCreated(view: View, viewModel: AudioPreviewViewModel) {
        filePreview = prepareFilePreview(view, viewModel)
    }

    private fun prepareFilePreview(
        view: View,
        viewModel: AudioPreviewViewModel
    ): IAudioFilePreview {
        val audioPlayCard = view.findViewById<PreviewAudioCard>(R.id.preview_audio_play_card)
        val defaultContainer = AudioPreviewContainerManager(view)
        val previewImpl = IAudioFilePreview.obtain(view.context, viewLifecycleOwner, viewModel)
        previewImpl.attachToContainer(audioPlayCard, defaultContainer)
        addUIConfigChangeObserver(previewImpl)
        return previewImpl
    }

    override fun onStart() {
        super.onStart()
        filePreview?.let(::addUIConfigChangeObserver)
    }

    override fun onStop() {
        super.onStop()
        filePreview?.let(::removeUIConfigChangeObserver)
    }

    override fun onCreateFilePathItem(view: View): PreviewFilePathItem {
        val item: PreviewFilePathItem = view.findViewById(R.id.preview_remote_location_info)
        item.setLabelGone()
        return item
    }


    override fun onDestroyView() {
        super.onDestroyView()
        filePreview?.release()
        filePreview = null
    }

    override fun releasePreview() {
        previewModel?.releaseAudioPlayer()
        previewModel?.releaseAudioMetadata()
    }

    override fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        val audioPlayCard =
            view?.findViewById<PreviewAudioCard>(R.id.preview_audio_play_card) ?: return
        val lp = audioPlayCard.layoutParams as? ConstraintLayout.LayoutParams ?: return
        val res = requireContext().resources
        lp.matchConstraintMaxWidth = res.getDimensionPixelOffset(R.dimen.preview_audio_max_width)
        audioPlayCard.layoutParams = lp
    }
}