/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: OPPOHomePageAdMgrTest.kt
 ** Description: unit test
 ** Version: 1.0
 ** Date : 2022/7/26
 ** Author: YanShengNeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * YanShengNeng 2022/7/14      1.0    create
 ****************************************************************/
package com.oplus.filemanager.compat.oppo

import android.app.Activity
import android.content.Context
import android.view.View
import com.oplus.filemanager.compat.brand.oppo.OPPOHomePageAdMgr
import io.mockk.MockKAnnotations
import io.mockk.impl.annotations.MockK
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import org.mockito.Mockito

@RunWith(JUnit4::class)
class OPPOHomePageAdMgrTest {
    @MockK
    lateinit var context: Context

    @MockK
    lateinit var activity: Activity

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun assert_false_when_HomePageAdMgr_init() {
        val homePageAdMgr = Mockito.mock(OPPOHomePageAdMgr::class.java)

        homePageAdMgr.setContainer(View(context), 1)

        homePageAdMgr.requestMainAd(activity)

        homePageAdMgr.getEntryView("name")

        homePageAdMgr.eventOnDestroy()
    }
}