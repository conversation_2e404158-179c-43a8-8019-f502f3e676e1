/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PrivacyEncryptFuncFactory
 * * Description: Privacy Encrypt Func Factory
 * * Version: 1.0
 * * Date : 2024/08/27
 * * Author:W9060445
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     W9060445         2024/08/27      1.0            create
 ****************************************************************/
package com.filemanager.setting.utils

import androidx.annotation.VisibleForTesting
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.privacy.ICollectPrivacyApi
import java.security.MessageDigest
import java.util.function.Function

object PrivacyEncryptFuncFactory {

    /**
     * 加密的星号
     */
    private const val TAG = "PrivacyEncryptFuncFactory"
    private const val ENCRYPT_ASTERISK = "*"
    private const val HASH_TYPE = "SHA-256"
    private const val HASH_HEX = 0xFF

    private const val ENCRYPT_ONE = 1
    private const val ENCRYPT_THREE = 3
    private const val ENCRYPT_FOUR = 4
    private const val ENCRYPT_SIX = 6

    /**
     * 没有加密
     */
    @VisibleForTesting
    val NO_ENCRYPT = Function<String, String> { content -> content }

    /**
     * 后六位打* 加密DUID
     */
    @VisibleForTesting
    val ENCRYPT_LAST_SIX = Function<String, String> { content -> encryptLast(content, ENCRYPT_SIX) }

    /**
     * 后四位打* 加密
     */
    val ENCRYPT_LAST_FOUR = Function<String, String> { content -> encryptLast(content, ENCRYPT_FOUR) }

    /**
     * hash 加密
     */
    val ENCRYPT_HASH = Function<String, String> { content -> encryptHash(content) }

    /**
     * 显示前三个数字和最后1位数字，其他中间部分全部隐藏  手机号 QQ号
     */
    @VisibleForTesting
    val ENCRYPT_NO_FIST_THREE_AND_LAST_ONE = Function<String, String> { content -> encryptCenter(content, ENCRYPT_THREE, ENCRYPT_ONE) }


    /**
     * 全部隐藏 邮箱
     */
    @VisibleForTesting
    val ENCRYPT_ALL = Function<String, String> { content -> encryptAll(content) }

    /**
     * 加密  未定义QQ号码 设备型号 设备品牌 软件OS版本 配置信息  SIM卡信息 IMSI信息  IDFA 应用使用记录  本应用软件签名 软件包名 电信运营商
     */
    fun get(category: String): Function<String, String> {
        val isEncrypt = PersonalPrivacyCategory.isEncryptContent(category)
        if (!isEncrypt) {
            return NO_ENCRYPT
        }
        return when (category) {
            ICollectPrivacyApi.PHONE_NUMBER -> ENCRYPT_NO_FIST_THREE_AND_LAST_ONE
            ICollectPrivacyApi.EMAIL_ADDRESS -> ENCRYPT_ALL
            ICollectPrivacyApi.DUID, ICollectPrivacyApi.DEVICE_MODEL, ICollectPrivacyApi.DEVICE_BRAND,
            ICollectPrivacyApi.OS_VERSION, ICollectPrivacyApi.CONFIG_INFO,
            ICollectPrivacyApi.SIM_CARD_INFO, ICollectPrivacyApi.IMSI_INFO, ICollectPrivacyApi.IMEI,
            ICollectPrivacyApi.ANDROID_ID, ICollectPrivacyApi.IDFA, ICollectPrivacyApi.APP_USAGE_RECORDS,
            ICollectPrivacyApi.APP_SIGN, ICollectPrivacyApi.SOFT_PKG,
            ICollectPrivacyApi.TELECOM_OPERATOR, ICollectPrivacyApi.NETWORK_ENVIRONMENT -> ENCRYPT_LAST_SIX
            ICollectPrivacyApi.IP_ADDRESS, ICollectPrivacyApi.SN_CODE -> ENCRYPT_LAST_FOUR
            ICollectPrivacyApi.OPEN_ID -> ENCRYPT_HASH
            else -> NO_ENCRYPT
        }
    }

    fun encryptLast(content: String, last: Int): String {
        val length = content.length
        val replaceLength = Math.min(length, last)
        val startIndex = length - replaceLength
        return replaceContent(content, startIndex, length)
    }

    fun encryptCenter(content: String, first: Int, last: Int): String {
        val length = content.length
        return replaceContent(content, first, length - last)
    }

    fun encryptAll(content: String): String {
        val length = content.length
        return replaceContent(content, 0, length)
    }

    /**
     * hash加密
     */
    @JvmStatic
    private fun encryptHash(content: String): String {
        runCatching {
            val bytesOrigin = content.toByteArray()
            val md = MessageDigest.getInstance(HASH_TYPE)
            md.update(bytesOrigin)
            val bytes = md.digest()
            val des = StringBuilder()
            var tmp: String? = null
            for (i in bytes.indices) {
                tmp = Integer.toHexString(bytes[i].toInt() and HASH_HEX)
                if (tmp.length == 1) {
                    des.append("0")
                }
                des.append(tmp)
            }
            return des.toString()
        }.onFailure {
            Log.e(TAG, "encryptHash ${it.message}")
        }
        return ENCRYPT_ASTERISK
    }

    /**
     * 采用部分屏蔽加密包名
     * 屏蔽应用包名中的一部分敏感信息。如“com.oppo.camera”可以使用
     * “com.oppo.c****a”代替；
     *  com.tencent.mm 替换为：com.tencent.mm
     */
    fun encryptPackage(content: String): String {
        val lastPointIndex = content.lastIndexOf(".")
        if (lastPointIndex == -1) { //
            return replaceCenterContent(content, 1)
        }
        val firstHalfContent = content.substring(0, lastPointIndex + 1)
        val endHalfContent = content.substring(lastPointIndex + 1, content.length)
        val keep = if (endHalfContent.length <= 2) {
            0
        } else {
            1
        }
        val endHalfEncryptContent = replaceCenterContent(endHalfContent, keep)
        return "$firstHalfContent$endHalfEncryptContent"
    }

    /**
     * 替换尾部内容
     * @param content 原内容
     * @param replaceLength 替换的长度
     */
    fun replaceTailContent(content: String, replaceLength: Int): String {
        val length = content.length
        // 0 <= replaceLength <= length
        val startIndex = length - replaceLength
        return replaceContent(content, startIndex, length)
    }

    /**
     * 替换内容，中间用*替换，前后保留原来的内容
     * @param content 原内容
     * @param keep 前后保留的长度
     */
    fun replaceCenterContent(content: String, keep: Int): String {
        val length = content.length
        // 0 <= keep <= length / 2
        val endIndex = length - keep
        return replaceContent(content, keep, endIndex)
    }

    /**
     * 替换内容，中间用*替换，前后保留原来的内容
     * @param content 原内容
     * @param startIndex 开始位置
     * @param endIndex 结束位置
     */
    fun replaceContent(content: String, startIndex: Int, endIndex: Int): String {
        val length = content.length
        runCatching {
            // 0 <= startIndex < endIndex <= length
            val builder = StringBuilder()
            builder.append(content.substring(0, startIndex))
            for (i in startIndex until endIndex) {
                builder.append(ENCRYPT_ASTERISK)
            }
            builder.append(content.substring(endIndex, length))
            return builder.toString()
        }.onFailure {
            Log.e(TAG, "length:$length,startIndex:$startIndex,endIndex:$endIndex it.message")
        }
        return ENCRYPT_ASTERISK
    }
}