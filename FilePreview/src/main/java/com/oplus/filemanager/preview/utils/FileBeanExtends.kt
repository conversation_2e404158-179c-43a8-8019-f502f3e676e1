/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - FileBeanExtends.kt
 * Description:
 *     The extensions for file bean.
 *
 * Version: 1.0
 * Date: 2024-09-05
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-09-05   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.utils

import android.content.Context
import android.net.Uri
import com.bumptech.glide.load.Key
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.thumbnail.FileThumbnailSignature
import org.apache.commons.io.FilenameUtils
import java.io.File

internal fun BaseFileBean.getMediaType(context: Context): Int {
    var type = MimeTypeHelper.getTypeFromPath(mData)
    if (type == MimeTypeHelper.UNKNOWN_TYPE) {
        type = MimeTypeHelper.getMediaType(mData)
    }
    type = MimeTypeHelper.getTypeFromDrm(context, type, mData)
    if (type == MimeTypeHelper.UNKNOWN_TYPE) {
        type = mLocalType
    }
    return type
}

internal fun BaseFileBean.getFileUri(): Uri? =
    mLocalFileUri ?: mData?.let { Uri.fromFile(File(it)) }

internal fun BaseFileBean.logInfo(): String? =
    mData?.let { "${it.hashCode()}_${FilenameUtils.getExtension(mDisplayName)}" }

internal fun Uri.logInfo(): String = "${hashCode()}_${FilenameUtils.getExtension(this.toString())}"

internal fun BaseFileBean.glideSignatureKey(salt: String = ""): Key =
    FileThumbnailSignature(mData ?: "", mDateModified, mSize, salt)

internal fun File.getMediaType(context: Context): Int {
    val filePath = absolutePath
    var type = MimeTypeHelper.getTypeFromPath(filePath)
    if (type == MimeTypeHelper.UNKNOWN_TYPE) {
        type = MimeTypeHelper.getMediaType(filePath)
    }
    type = MimeTypeHelper.getTypeFromDrm(context, type, filePath)
    return type
}

internal fun BaseFileBean.isBoardDoc(): Boolean =
    mLocalType == MimeTypeHelper.PPT_TYPE || mLocalType == MimeTypeHelper.PPTX_TYPE
            || mLocalType == MimeTypeHelper.XLS_TYPE || mLocalType == MimeTypeHelper.XLSX_TYPE