/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DateModifyUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/2/27 14:56
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/2/27       1.0      create
 ***********************************************************************/
@file:JvmName("DateModifyUtils")
package com.oplus.fileservice.filelist.utils

import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * Default time format, 1677482521404 convert to 2023/02/27 15:22
 */
private const val DEFAULT_FORMAT = "yyyy/MM/dd HH:mm"

fun getDateAndTimeByDefaultFormat(time: Long, format: String = DEFAULT_FORMAT): String {
    return SimpleDateFormat(format, Locale.CHINA).format(Date(time))
}