<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="HardcodedDebugMode">

    <permission android:name="com.oplus.filemanager.permission.DFM_PROVIDER"
        android:protectionLevel="signatureOrSystem"/>

    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        tools:node="remove" />
    <uses-permission android:name="com.oplus.permission.safe.PROTECT" />
    <!--add for shortcut-->
    <uses-permission android:name="com.oplus.permission.safe.SAFE_MANAGER" />
    <application
        android:name="com.oplus.filemanager.FileManagerApplication"
        android:allowBackup="false"
        android:enableOnBackInvokedCallback="true"
        android:icon="@drawable/ic_launcher_filemanager"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestRawExternalStorageAccess="true"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <meta-data
            android:name="versionCommit"
            android:value="${versionCommit}" />
        <meta-data
            android:name="versionDate"
            android:value="${versionDate}" />

        <!-- 禁用 WorkManager 自动初始化，使用自定义初始化 -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:ignore="MissingClass"
            tools:node="merge">
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
        </provider>
        <activity
            android:name="com.oplus.filemanager.category.album.ui.AlbumActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name="com.oplus.filemanager.category.albumset.ui.AlbumSetActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oppo.intent.action.FILE_GALLERT_VIEW" />
                <category android:name="android.intent.category.DEFAULT" />

            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.FILE_GALLERT_VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.filemanager.category.apk.ui.ApkActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oppo.intent.action.FILE_APK_VIEW" />
                <category android:name="android.intent.category.DEFAULT" />

            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.FILE_APK_VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>
        <activity
            android:name="com.oplus.filemanager.category.audiovideo.ui.CategoryAudioActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oppo.intent.action.FILE_MEDIA_VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.FILE_MEDIA_VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>
        <activity
            android:name="com.filemanager.categorycompress.ui.CategoryCompressActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oppo.intent.action.FILE_COMPRESS_VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.filemanager.category.document.ui.DocumentActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:label="@string/string_documents"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oppo.intent.action.FILE_DOCUMENT_VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.FILE_DOCUMENT_VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>
        <activity
            android:name="com.oplus.filemanager.category.globalsearch.ui.GlobalSearchActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="filemanager.intent.action.GLOBAL_SEARCH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- filemanager://deeplink.search?CATEGORY_TYPE=3&CATEGORY_KEY_WORD=xx-->
                <data
                    android:scheme="filemanager"
                    android:host="deeplink.search"
                    />
            </intent-filter>
        </activity>
        <activity
            android:name="com.filemanager.compresspreview.ui.CompressPreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oppo.intent.action.ACTION_COMPRESS_PREVIEW" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="content" />
                <data android:mimeType="application/x-7z-compressed" />
                <data android:mimeType="application/x-rar-compressed" />
                <data android:mimeType="application/rar" />
                <data android:mimeType="application/vnd.rar" />
                <data android:mimeType="application/zip" />
                <data android:mimeType="application/java-archive" />
                <data android:mimeType="text/uri-list" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.filebrowser.FileBrowserActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:launchMode="singleTop"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oppo.filemanager.intent.action.BROWSER_FILE" />
                <action android:name="oppo.intent.action.ACTION_FILEMANAGER_SAVE_PHONE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.BROWSER_FILE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>

        <activity
            android:name="com.oplus.filemanager.category.remotedevice.CategoryRemoteFileActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:launchMode="singleTop"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
        </activity>

        <activity
            android:name="com.oplus.filebrowser.otg.OtgFileBrowserActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:launchMode="singleTop"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">

        </activity>
        <activity
            android:name="com.oplus.filemanager.filechoose.ui.folderpicker.FolderPickerActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oppo.intent.action.ACTION_FILEMANAGER_SAVE" />
                <action android:name="oplus.intent.action.ACTION_FILEMANAGER_SAVE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.filemanager.filechoose.ui.addFile.AddFileActivity"
            android:configChanges="orientation|keyboardHidden|mcc|mnc|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:uiOptions="splitActionBarWhenNarrow">
            <intent-filter>
                <action android:name="oplus.intent.action.ACTION_FILEMANAGER_PICKFILES" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.OPENABLE" />
                <data android:mimeType="*/*"></data>
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.filemanager.filechoose.ui.singlepicker.SinglePickerActivity"
            android:configChanges="orientation|keyboardHidden|mcc|mnc|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:uiOptions="splitActionBarWhenNarrow">
            <intent-filter>
                <action android:name="oppo.intent.action.ACTION_FILEMANAGER_PICK" />
                <action android:name="com.oppo.select.vcf" />
                <action android:name="com.oppo.select.cvs" />
                <action android:name="oppo.intent.action.ACTION_SELECT_MEMO" />
                <action android:name="oppo.intent.action.ACTION_FILEMANAGER_BROWSE" />
                <action android:name="oppo.intent.action.ACTION_FILEMANAGER_SELECT" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.GET_CONTENT" />

                <category android:name="android.intent.category.OPENABLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="oppo.file.browser/browser.all" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.PICK" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="oppo.file.browser/browser.all" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.ACTION_FILEMANAGER_SELECT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>
        <activity
            android:name="com.oplus.filemanager.filechoose.ui.filepicker.FilePickerActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:label="@string/activity_mark"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:taskAffinity="com.oppo.filemanager.encrytion"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow">
            <intent-filter>
                <action android:name="oppo.intent.action.ACTION_FILE_SELECTION" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.filemanager.keymove.ui.AKeyToMoveActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:label="@string/title_onetap_to_move"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow">
            <intent-filter>
                <action android:name="com.oppo.filemanager.akeytomove.AKeyToMoveActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.AKEY_TO_MOVE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.filemanager.recyclebin.ui.RecycleBinActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:label="@string/string_documents"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">

        </activity>
        <activity
            android:name="com.filemanager.setting.ui.SettingActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">

        </activity>

        <activity
            android:name="com.filemanager.setting.ui.privacy.personal.PersonalListActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan" >

        </activity>

        <activity
            android:name="com.filemanager.setting.ui.privacy.personal.PersonalInfoDetailActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan" >

        </activity>

        <activity
            android:name="com.filemanager.setting.ui.about.SettingAboutActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">

        </activity>
        <activity
            android:name="com.filemanager.setting.ui.function.SettingFunctionActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.FUNC_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data
                android:name="support_jump"
                android:value="true" />
        </activity>
        <activity
            android:name="com.filemanager.setting.ui.opensourcelicense.OpenSourceActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">

        </activity>
        <activity
            android:name="com.filemanager.superapp.ui.superapp.SuperAppActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            android:permission="com.oplus.permission.safe.SECURITY"
            android:launchMode="singleTop"
            >
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.SOURCES_DOC" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/*"/>
            </intent-filter>
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.SUPER_APP" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.filemanager.main.ui.QuickDocumentActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize|keyboard|navigation"
            android:exported="true"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:icon="@drawable/shortcut_document_blue"
            android:label="@string/string_documents"
            android:launchMode="standard"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:windowSoftInputMode="adjustPan"
            android:autoRemoveFromRecents="true"
            android:excludeFromRecents="true"
            >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- filemanager://deeplink.doc-->
                <data
                    android:scheme="filemanager"
                    android:host="deeplink.doc"
                    />
            </intent-filter>
        </activity>

        <activity
            android:name="com.oplus.filemanager.main.ui.ShortCutActivity"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            android:autoRemoveFromRecents="true"
            android:excludeFromRecents="true"
            >
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.SHORTCUT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity-alias
            android:name="com.oplus.filemanager.main.ui.QuickDocumentActivity_user_skill"
            android:targetActivity="com.oplus.filemanager.main.ui.QuickDocumentActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize|keyboard|navigation"
            android:exported="true"
            android:permission="com.oplus.permission.safe.SECURITY"
            android:icon="@drawable/shortcut_document_blue"
            android:label="@string/string_documents"
            android:launchMode="standard"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:windowSoftInputMode="adjustPan"
            android:autoRemoveFromRecents="true"
            android:excludeFromRecents="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <data
                    android:scheme="document"
                    android:host="documentsreader.oplus.andes"
                    android:pathPrefix="/main"
                    />
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity-alias>
        <activity
            android:name="com.oplus.filemanager.main.ui.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize|keyboard|navigation"
            android:exported="true"
            android:screenOrientation="behind"
            android:launchMode="singleTop"
            android:theme="@style/AppNoTitleTheme.Splash"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
                        <intent-filter>
                            <action android:name="android.intent.action.MAIN" />
                            <category android:name="android.intent.category.LAUNCHER" />
                        </intent-filter>
            <intent-filter>
                <action android:name="ACTION_FILEMANAGER_BROWSE_CHECK_PASSWORD" />
                <action android:name="oppo.intent.action.OPEN_FILEMANAGER" />
                <action android:name="oppo.intent.action.ACTION_FILEMANAGER_SAVE_MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.OPEN_FILEMANAGER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.DRIVE_DOWNLOAD" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.QUICK_DOCUMENT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.superApp" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <meta-data
                android:name="oldComponentName"
                android:resource="@array/downLoadListOldComponentName" />
            <meta-data
                android:name="oldComponentNameVersion"
                android:value="1" />
            <!--add for shortcuts in desktop-->
            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/desktop_shortcuts" />
        </activity>

        <!-- Add for file share in Android N, 2017-04-04 -->
        <provider
            android:name="com.oplus.filemanager.provider.FilePathProvider"
            android:authorities="com.coloros.filemanager"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path" />
        </provider>

        <activity
            android:name="com.oplus.filemanager.main.ui.UsbAttachedActivity"
            android:exported="false"
            android:screenOrientation="behind"
            android:theme="@android:style/Theme.NoDisplay"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">

            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
            </intent-filter>

            <meta-data
                android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
                android:resource="@xml/device_filter" />
        </activity>

        <activity
            android:name="com.oplus.filemanager.filelabel.list.LabelFileListActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="behind"
            android:permission="com.oplus.permission.safe.ASSISTANT"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            android:exported="true"
            >
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.LABEL_FILE_LIST" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.filemanager.setting.ui.privacy.SettingPrivacyActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
        </activity>

        <activity
            android:name="com.oplus.filemanager.domestic.UserInformationListActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oplus.filemanager.action.personal.user.information"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name="com.oplus.filemanager.main.ui.TransitionActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:theme="@style/Theme.Transparent">

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.EXTERNAL_APP" />
                <action android:name="oplus.intent.action.filemanager.view_recycle_bin" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <provider
            android:name="com.filemanager.recyclebin.db.RecycleProvider"
            android:authorities="com.coloros.filemanager.recycleprovider"
            android:exported="false"
            android:grantUriPermissions="true" />
        <provider
            android:name="com.oplus.filemanager.provider.FileManagerProvider"
            android:authorities="com.coloros.filemanager.filemanagerprovider"
            android:exported="false"
            android:grantUriPermissions="true" />
        <provider
            android:name="com.oplus.filemanager.category.globalsearch.provider.DFMProvider"
            android:authorities="com.coloros.filemanager.dmpprovider"
            android:exported="true"
            android:multiprocess="false"
            android:permission="com.oplus.filemanager.permission.DFM_PROVIDER"
            />
        <provider
            android:name="com.oplus.filemanager.provider.FileDataProvider"
            android:authorities="com.coloros.filemanager.filedata"
            android:exported="true"
            android:multiprocess="false"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE"></provider>
        <provider
            android:name="com.oplus.filemanager.provider.MyFileProvider"
            android:authorities="com.coloros.filemanager.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path" />
        </provider>
        <provider
            android:authorities="com.coloros.filemanager.provider.shortcutprovider"
            android:exported="true"
            android:permission="com.oplus.permission.safe.SECURITY"
            android:name="com.oplus.filemanager.provider.ShortCutProvider"/>
        <meta-data
            android:name="AppCode"
            android:value="20016" />
        <!--apk权限：https://doc.myoas.com/pages/viewpage.action?pageId=151645839
                包括AIUnit，私密保险箱，外部调用-->
        <meta-data
            android:name="OppoPermissionKey"
            android:value="Oh+XWp+x/mCezMDMpIKtH6N1juXsvjeT3Mcxxi6kEIjxK4ev2c6hz7oAEqiLR0fBiWDQP6169qu5pMRmfXrsK+hmfCN5pu9r9DOTierbgj5DLLKvXJQ1f+CL4mKDOE4QraFKJB4J9ARhfEpzWIv9jwz8I9fZ7VSIM1zr3wV3lomw2RZf3/H7DFD4XoawxEhZSr6jSPkqJanHVbTCHbplnr3M6J8KUjgogjt6rnpCHtk7eFxqwgE5xqXTKWp0zUTFmNkTNIUNDoRyIgSV8yus/KhSUsnsdmgGagZcUopzZQz4wvvCyvWsckAf8fwWWkgKFYZuGhn3UXgKVOWw9Vd3WA== 1 com.coloros.filemanager 64AAFAF1D5BC9155A9E417A849E4F8EDA1D0D1341667C28ED7C443C76F820B9A 917" />
        <!--add for feature 3787712, true is support this function and false is not start-->
        <meta-data
            android:name="FolderPickSaveFile"
            android:value="true"/>
        <!--add for feature 3787712 end-->
        <!--AIUnit的鉴权标识符-->
        <meta-data
            android:name="com.oplus.aiunit.auth_style"
            android:value="0" />
        <!--AIUnit的鉴权标识符  end-->

        <meta-data
            android:name="com.oplus.pickle.to_tripartite"
            android:value="true" />
    </application>
</manifest>