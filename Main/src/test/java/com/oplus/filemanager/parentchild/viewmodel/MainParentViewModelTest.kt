package com.oplus.filemanager.parentchild.viewmodel

import android.graphics.drawable.Drawable
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.Observer
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.GsonUtil
import com.filemanager.common.utils.PreferencesUtils
import com.oplus.filemanager.bean.SuperAppBean
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.main.ui.uistate.Storage
import com.oplus.filemanager.main.utils.StorageInfoUtils
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.util.GroupCollapseUtil
import io.mockk.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * MainParentViewModel的单元测试类
 * 用于测试MainParentViewModel中的各种功能逻辑
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
@ExperimentalCoroutinesApi
class MainParentViewModelTest {

    /**
     * 用于确保LiveData的更新是同步执行的规则
     */
    @get:Rule
    val instantExecutorRule = InstantTaskExecutorRule()

    private lateinit var viewModel: MainParentViewModel
    private val testDispatcher = StandardTestDispatcher()
    private val testScope = TestScope(testDispatcher)
    
    /**
     * 测试前的初始化方法
     * 1. 设置主调度器为测试调度器
     * 2. 模拟各种依赖对象
     * 3. 创建被测ViewModel实例
     */
    @Before
    fun setUp() {
        // 设置主调度器为测试调度器
        Dispatchers.setMain(testDispatcher)
        // 模拟StorageInfoUtils工具类
        mockkObject(StorageInfoUtils)
        // 模拟GroupCollapseUtil工具类
        mockkObject(GroupCollapseUtil)
        // 模拟GsonUtil工具类
        mockkObject(GsonUtil)
        // 模拟PreferencesUtils工具类
        mockkObject(PreferencesUtils)
        
        // 模拟stringResource方法调用，避免资源加载错误
        mockkStatic("com.filemanager.common.utils.StringResourcesKt")
        every { com.filemanager.common.utils.stringResource(any()) } returns "test"
        
        // 模拟MyApplication，避免初始化错误
        mockkStatic("com.filemanager.common.MyApplication")
        val mockApplication = mockk<android.app.Application>()
        every { mockApplication.getSystemService(any<String>()) } returns mockk()
        
        // 创建被测ViewModel实例
        viewModel = MainParentViewModel()
    }
    
    /**
     * 测试后的清理方法
     * 1. 重置主调度器
     * 2. 解除所有模拟
     */
    @After
    fun tearDown() {
        Dispatchers.resetMain()
        unmockkAll()
    }
    
    /**
     * 测试ViewModel初始化时是否正确加载存储信息
     * 验证点：
     * 1. 存储列表包含2个存储项
     * 2. 第一个是PhoneStorage类型且显示垃圾清理
     * 3. 第二个是CloudDiskStorage类型
     */
    @Test
    fun `init should load storage info`() = testScope.runTest {
        // Given - 准备测试数据
        val showGarbage = true
        every { StorageInfoUtils.getCleanupGarbageVisibility() } returns showGarbage
        coEvery { StorageInfoUtils.getCloudDiskStorage() } returns Storage.CloudDiskStorage(true, 1000L, 500L)
        
        // When - 执行测试操作
        advanceUntilIdle()
        
        // Then - 验证结果
        val storageState = viewModel.storageUiState.value
        assert(storageState.storageItems.size == 2)
        assert(storageState.storageItems[0] is Storage.PhoneStorage)
        assert((storageState.storageItems[0] as Storage.PhoneStorage).showCleanGarbage == showGarbage)
        assert(storageState.storageItems[1] is Storage.CloudDiskStorage)
    }
    
    /**
     * 测试getSuperAppCategory方法将超级应用列表转换为分类列表
     * 验证点：
     * 1. 返回列表大小正确
     * 2. 分类类型按预期递增
     * 3. 包名正确保留
     */
    @Test
    fun `getSuperAppCategory should convert super app list to category list`() {
        // Given - 准备测试数据
        val superAppList = mutableListOf<MainCategoryItemsBean>().apply {
            add(createMainCategoryItemsBean("com.tencent.mobileqq", CategoryHelper.CATEGORY_QQ))
            add(createMainCategoryItemsBean("com.tencent.mm", CategoryHelper.CATEGORY_MICROMSG))
        }
        
        // When - 执行测试方法
        val result = viewModel.getSuperAppCategory(superAppList)
        
        // Then - 验证结果
        assert(result.size == 2)
        assert(result[0].categoryType == CategoryHelper.CATEGORY_SOURCE_GROUP + 1)
        assert(result[1].categoryType == CategoryHelper.CATEGORY_SOURCE_GROUP + 2)
        assert(result[0].packageName == "com.tencent.mobileqq")
        assert(result[1].packageName == "com.tencent.mm")
    }
    
    /**
     * 测试getSuperAppCategory方法处理特殊分类类型
     * 验证点：
     * 1. 特殊分类类型(K_DOCS和TENCENT_DOCS)被正确保留
     */
    @Test
    fun `getSuperAppCategory should handle special category types`() {
        // Given - 准备测试数据
        val superAppList = mutableListOf<MainCategoryItemsBean>().apply {
            add(createMainCategoryItemsBean("com.kingsoft.wpsoffice", CategoryHelper.CATEGORY_K_DOCS))
            add(createMainCategoryItemsBean("com.tencent.docs", CategoryHelper.CATEGORY_TENCENT_DOCS))
        }
        
        // When - 执行测试方法
        val result = viewModel.getSuperAppCategory(superAppList)
        
        // Then - 验证结果
        assert(result.size == 2)
        assert(result[0].categoryType == CategoryHelper.CATEGORY_K_DOCS)
        assert(result[1].categoryType == CategoryHelper.CATEGORY_TENCENT_DOCS)
    }
    
    /**
     * 测试updateCategory方法是否调用了updateCategoryList
     * 验证点：
     * 1. updateCategoryList方法被正确调用一次
     */
    @Test
    fun `updateCategory should call updateCategoryList`() = testScope.runTest {
        // Given - 准备测试数据(创建spy对象)
        val spyViewModel = spyk(viewModel)
        every { spyViewModel.updateCategoryList() } just runs
        
        // When - 执行测试方法
        spyViewModel.updateCategory()
        advanceUntilIdle()
        
        // Then - 验证结果
        verify(exactly = 1) { spyViewModel.updateCategoryList() }
    }
    
    /**
     * 测试loadClassificationList方法是否正确添加分类组
     * 验证点：
     * 1. 分类组被正确添加
     * 2. 分类面板在组展开时被添加
     */
    @Test
    fun `loadClassificationList should add classification group`() = testScope.runTest {
        // Given - 准备测试数据
        val categoryList = arrayListOf<CategoryListBean>()
        every { GroupCollapseUtil.isGroupOpen(CategoryHelper.CATEGORY_CLASSIFICATION_GROUP) } returns true
        
        // When - 使用反射调用私有方法
        val method = MainParentViewModel::class.java.getDeclaredMethod("loadClassificationList", ArrayList::class.java)
        method.isAccessible = true
        method.invoke(viewModel, categoryList)
        advanceUntilIdle()
        
        // Then - 验证结果
        assert(categoryList.size == 2)
        assert(categoryList[0].categoryType == CategoryHelper.CATEGORY_CLASSIFICATION_GROUP)
        assert(categoryList[0].layoutType == CategoryListBean.TYPE_EXPANDABLE)
        assert(categoryList[1].layoutType == CategoryListBean.TYPE_CLASS_PANEL)
    }
    
    /**
     * 测试loadPositionList方法是否正确添加位置组
     * 验证点：
     * 1. 问卷调查项被添加
     * 2. 最近页面项被添加
     * 3. 位置组被添加
     * 4. 位置面板在组展开时被添加
     */
    @Test
    fun `loadPositionList should add position groups`() = testScope.runTest {
        // Given - 准备测试数据
        val categoryList = arrayListOf<CategoryListBean>()
        every { GroupCollapseUtil.isGroupOpen(CategoryHelper.CATEGORY_POSITION_GROUP) } returns true
        
        // When - 使用反射调用私有方法
        val method = MainParentViewModel::class.java.getDeclaredMethod("loadPositionList", ArrayList::class.java)
        method.isAccessible = true
        method.invoke(viewModel, categoryList)
        advanceUntilIdle()
        
        // Then - 验证结果
        // 根据源码，loadPositionList应该添加4个元素：
        // 1. 问卷调查
        // 2. 最近页面
        // 3. 位置组
        // 4. 位置组内的面板（因为组是展开的）
        assert(categoryList.size == 4)
        assert(categoryList[0].categoryType == CategoryHelper.CATEGORY_QUESTIONNAIRE)
        assert(categoryList[1].categoryType == CategoryHelper.CATEGORY_RECENT)
        assert(categoryList[2].categoryType == CategoryHelper.CATEGORY_POSITION_GROUP)
        assert(categoryList[3].layoutType == CategoryListBean.TYPE_STORAGE_PANEL)
    }
    
    /**
     * 创建MainCategoryItemsBean测试对象的辅助方法
     * @param packageName 包名
     * @param itemType 项目类型
     * @return 构造好的MainCategoryItemsBean对象
     */
    private fun createMainCategoryItemsBean(packageName: String, itemType: Int): MainCategoryItemsBean {
        return MainCategoryItemsBean().apply {
            this.packageName = packageName
            this.itemType = itemType
            this.name = "Test App"
            this.iconId = 123
            this.drawable = mockk<Drawable>()
            this.superAppSwitch = true
        }
    }
    
    /**
     * 创建CategoryListBean测试对象的辅助方法
     * @param packageName 包名
     * @param switchStatus 开关状态
     * @return 构造好的CategoryListBean对象
     */
    private fun createCategoryListBean(packageName: String, switchStatus: Boolean): CategoryListBean {
        return CategoryListBean(1, CategoryListBean.TYPE_EDIT_OPTIONS).apply {
            this.packageName = packageName
            this.superAppSwitch = switchStatus
        }
    }
}