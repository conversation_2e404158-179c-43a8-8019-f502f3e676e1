/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScriptServiceManagerTest.kt
 * Description:
 * The test cases for ScriptServiceManager
 *
 * Version: 1.0
 * Date: 2024-05-30
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-05-30   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.service

import com.oplus.filemanager.simulateclick.accessibility.AccessibilitySettings
import com.oplus.filemanager.simulateclick.request.ScriptExecRequest
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class ScriptServiceManagerTest : Assert() {

    @Before
    fun setUp() {
        mockkStatic(AccessibilitySettings::class)
        ScriptServiceManager.runningService = null
        ScriptServiceManager.pendingRequest = null
    }

    @After
    fun tearDown() {
        unmockkStatic(AccessibilitySettings::class)
        ScriptServiceManager.runningService = null
        ScriptServiceManager.pendingRequest = null
    }

    @Test
    fun `should set true when startScriptService`() {
        // Given
        every { AccessibilitySettings.getEnabledServicesFromSettings(any()) } returns mutableSetOf()
        justRun { AccessibilitySettings.setAccessibilityServiceState(any(), any(), any(), any()) }

        // When
        ScriptServiceManager.startScriptService(mockk())

        // Then
        verify { AccessibilitySettings.setAccessibilityServiceState(any(), any(), true, any()) }
    }

    @Test
    fun `should set false when stopScriptService`() {
        // Given
        every { AccessibilitySettings.getEnabledServicesFromSettings(any()) } returns mutableSetOf()
        justRun { AccessibilitySettings.setAccessibilityServiceState(any(), any(), any(), any()) }

        // When
        ScriptServiceManager.stopScriptService(mockk())

        // Then
        verify { AccessibilitySettings.setAccessibilityServiceState(any(), any(), false, any()) }
    }

    @Test
    fun `should exec pending request when onServiceConnected`() {
        // Given
        val request = mockk<ScriptExecRequest>()
        ScriptServiceManager.pendingRequest = request
        val service = mockk<SimulateClickService>(relaxed = true)

        // When
        ScriptServiceManager.onServiceConnected(service)

        // Then
        assertEquals(service, ScriptServiceManager.runningService)
        assertNull(ScriptServiceManager.pendingRequest)
        verify { service.executeScript(request) }
    }

    @Test
    fun `should clear runningService when onServiceDisconnected from running service`() {
        // Given
        val service = mockk<SimulateClickService>()
        ScriptServiceManager.runningService = service

        // When
        ScriptServiceManager.onServiceDisconnected(service)

        // Then
        assertNull(ScriptServiceManager.runningService)
    }

    @Test
    fun `should keep runningService when onServiceDisconnected from other service`() {
        // Given
        val service = mockk<SimulateClickService>()
        ScriptServiceManager.runningService = service

        // When
        ScriptServiceManager.onServiceDisconnected(mockk())

        // Then
        assertEquals(service, ScriptServiceManager.runningService)
    }

    @Test
    fun `should direct exec when executeScript if service is running`() {
        // SetUp
        mockkStatic(ScriptServiceManager::startScriptService)

        // Given
        justRun { ScriptServiceManager.startScriptService(any()) }
        val request = mockk<ScriptExecRequest>()
        val service = mockk<SimulateClickService>(relaxed = true)
        ScriptServiceManager.runningService = service

        // When
        ScriptServiceManager.executeScript(mockk(), request)

        // Then
        service.executeScript(request)
        verify(inverse = true) { ScriptServiceManager.startScriptService(any()) }

        // Then
        unmockkStatic(ScriptServiceManager::startScriptService)
    }

    @Test
    fun `should start service when executeScript if service is not running`() {
        // SetUp
        mockkStatic(ScriptServiceManager::startScriptService)

        // Given
        justRun { ScriptServiceManager.startScriptService(any()) }
        val request = mockk<ScriptExecRequest>()
        ScriptServiceManager.runningService = null

        // When
        ScriptServiceManager.executeScript(mockk(), request)

        // Then
        assertEquals(request, ScriptServiceManager.pendingRequest)
        verify { ScriptServiceManager.startScriptService(any()) }

        // Then
        unmockkStatic(ScriptServiceManager::startScriptService)
    }
}