/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - ItemDetailsLookup.kt
 ** Description: Item Details Lookup
 ** Version: 1.0
 ** Date : 2020/05/15
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/15    1.0     create
 ****************************************************************/
package com.oplus.dropdrag.recycleview

import android.view.MotionEvent
import android.view.View
import androidx.annotation.Keep
import androidx.recyclerview.widget.RecyclerView

@Keep
abstract class ItemDetailsLookup<K> {
    private companion object {
        @JvmStatic
        private fun hasSelectionKey(item: ItemDetails<*>?): Boolean {
            return item?.selectionKey != null
        }
    }

    /**
     * @return true if there is an item at the event coordinates.
     */
    fun overItem(e: MotionEvent): Boolean {
        return getItemPosition(e) != RecyclerView.NO_POSITION
    }

    /**
     * @return true if there is an item w/ a stable ID at the event coordinates.
     */
    fun overItemWithSelectionKey(e: MotionEvent): Boolean {
        return overItem(e) && hasSelectionKey(getItemDetails(e))
    }

    /**
     * @return true if the event coordinates are in an area of the item
     * that can result in dragging the item. List items frequently have a white
     * area that is not draggable allowing band selection to be initiated
     * in that area.
     */
    fun inItemDragRegion(e: MotionEvent): Boolean {
        return overItem(e) && (getItemDetails(e)?.inDragRegion(e) ?: false)
    }

    /**
     * @return true if the event coordinates are in a "selection hot spot"
     * region of an item. Contact in these regions result in immediate
     * selection, even when there is no existing selection.
     */
    fun inItemSelectRegion(e: MotionEvent): Boolean {
        return overItem(e) && getItemDetails(e)!!.inSelectionHotspot(e)
    }

    /**
     * @return the adapter position of the item at the event coordinates.
     */
    fun getItemPosition(e: MotionEvent): Int {
        val item: ItemDetails<K>? = getItemDetails(e)
        return item?.position ?: RecyclerView.NO_POSITION
    }

    /**
     * @return the ItemDetails for the item under the event, or null.
     */
    abstract fun getItemDetails(e: MotionEvent): ItemDetails<K>?

    /**
     * @return the view for the item under the event, or null.
     */
    abstract fun getItemView(e: MotionEvent): View?

    @Keep
    abstract class ItemDetails<K> {
        /**
         * Returns the adapter position of the item. See
         * [ViewHolder.getAdapterPosition][RecyclerView.ViewHolder.getAdapterPosition]
         */
        abstract val position: Int

        /**
         * @return true if the item has a selection key.
         */
        open fun hasSelectionKey(): Boolean {
            return selectionKey != null
        }

        /**
         * @return the selection key of an item.
         */
        abstract val selectionKey: K?

        /**
         * @return true if the event is in an area of the item that should be
         * directly interpreted as a user wishing to select the item. This
         * is useful for checkboxes and other UI affordances focused on enabling
         * selection.
         */
        abstract fun inSelectionHotspot(e: MotionEvent): Boolean

        /**
         * @return true if the event is in an area of the item can click
         */
        open fun isClickArea(e: MotionEvent): Boolean {
            return true
        }

        abstract fun inDragRegion(e: MotionEvent): Boolean
        /*
         *  @return false if the ViewHolder can not be selected like LabelViewHolder in FileBrowserAdapter.
         *  default return true
         */
        abstract fun canLongPressOrClick(): Boolean

        override fun equals(obj: Any?): Boolean {
            return (obj is ItemDetails<*>
                    && isEqualTo(obj))
        }

        private fun isEqualTo(other: ItemDetails<*>): Boolean {
            val key = selectionKey
            var sameKeys = false
            sameKeys = if (key == null) {
                other.selectionKey == null
            } else {
                key == other.selectionKey
            }
            return sameKeys && position == other.position
        }

        override fun hashCode(): Int {
            return position ushr 8
        }
    }
}