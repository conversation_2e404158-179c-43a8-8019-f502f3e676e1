#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes for Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:110), pid=26672, tid=1396
#
# JRE version: Java(TM) SE Runtime Environment (17.0.8+9) (build 17.0.8+9-LTS-211)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\framework\HeytapAccount\build\20250901_17026502023556230817.compiler.options

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Sep  1 12:32:52 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 3.180390 seconds (0d 0h 0m 3s)

---------------  T H R E A D  ---------------

Current thread (0x000001bde44a38d0):  JavaThread "main" [_thread_in_vm, id=1396, stack(0x0000004a80e00000,0x0000004a80f00000)]

Stack: [0x0000004a80e00000,0x0000004a80f00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x677d0a]
V  [jvm.dll+0x7d8c54]
V  [jvm.dll+0x7da3fe]
V  [jvm.dll+0x7daa63]
V  [jvm.dll+0x245c5f]
V  [jvm.dll+0x7d4d5b]
V  [jvm.dll+0x61dcf6]
V  [jvm.dll+0x1c0127]
V  [jvm.dll+0x620650]
V  [jvm.dll+0x61e6b6]
V  [jvm.dll+0x23b6b1]
V  [jvm.dll+0x125202]
V  [jvm.dll+0x24e1f9]
V  [jvm.dll+0x24f55f]
V  [jvm.dll+0x1e254f]
V  [jvm.dll+0x1e1add]
V  [jvm.dll+0x53dde5]
V  [jvm.dll+0x753768]
V  [jvm.dll+0x753854]
V  [jvm.dll+0x40ba4f]
V  [jvm.dll+0x411a69]
C  [java.dll+0x17ec]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
J 517  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (0 bytes) @ 0x000001bdf50f40e3 [0x000001bdf50f4020+0x00000000000000c3]
J 539 c1 java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class; java.base@17.0.8 (43 bytes) @ 0x000001bded612cdc [0x000001bded612980+0x000000000000035c]
J 663 c1 java.security.SecureClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class; java.base@17.0.8 (16 bytes) @ 0x000001bded65300c [0x000001bded652f40+0x00000000000000cc]
J 596 c1 jdk.internal.loader.BuiltinClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class; java.base@17.0.8 (121 bytes) @ 0x000001bded62fa74 [0x000001bded62e840+0x0000000000001234]
J 478 c1 jdk.internal.loader.BuiltinClassLoader.findClassOnClassPathOrNull(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (64 bytes) @ 0x000001bded5ea8e4 [0x000001bded5e9860+0x0000000000001084]
J 228 c1 jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class; java.base@17.0.8 (143 bytes) @ 0x000001bded57ac54 [0x000001bded579d40+0x0000000000000f14]
J 341 c1 jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class; java.base@17.0.8 (40 bytes) @ 0x000001bded5ab48c [0x000001bded5aae60+0x000000000000062c]
J 340 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (7 bytes) @ 0x000001bded5a9f4c [0x000001bded5a9e40+0x000000000000010c]
v  ~StubRoutines::call_stub
J 517  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (0 bytes) @ 0x000001bdf50f40e3 [0x000001bdf50f4020+0x00000000000000c3]
J 539 c1 java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class; java.base@17.0.8 (43 bytes) @ 0x000001bded612cdc [0x000001bded612980+0x000000000000035c]
J 663 c1 java.security.SecureClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class; java.base@17.0.8 (16 bytes) @ 0x000001bded65300c [0x000001bded652f40+0x00000000000000cc]
J 596 c1 jdk.internal.loader.BuiltinClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class; java.base@17.0.8 (121 bytes) @ 0x000001bded62fa74 [0x000001bded62e840+0x0000000000001234]
J 478 c1 jdk.internal.loader.BuiltinClassLoader.findClassOnClassPathOrNull(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (64 bytes) @ 0x000001bded5ea8e4 [0x000001bded5e9860+0x0000000000001084]
J 228 c1 jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class; java.base@17.0.8 (143 bytes) @ 0x000001bded57ac54 [0x000001bded579d40+0x0000000000000f14]
J 341 c1 jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class; java.base@17.0.8 (40 bytes) @ 0x000001bded5ab48c [0x000001bded5aae60+0x000000000000062c]
J 340 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.8 (7 bytes) @ 0x000001bded5a9f4c [0x000001bded5a9e40+0x000000000000010c]
v  ~StubRoutines::call_stub
j  org.jetbrains.kotlin.com.intellij.core.JavaCoreApplicationEnvironment.<init>(Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Z)V+119
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinCoreApplicationEnvironment.<init>(Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Z)V+3
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinCoreApplicationEnvironment.<init>(Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;ZLkotlin/jvm/internal/DefaultConstructorMarker;)V+3
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinCoreApplicationEnvironment$Companion.create(Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Z)Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreApplicationEnvironment;+13
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinCoreEnvironment$Companion.createApplicationEnvironment(Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Z)Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreApplicationEnvironment;+5
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinCoreEnvironment$Companion.getOrCreateApplicationEnvironment(Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Z)Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreApplicationEnvironment;+45
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinCoreEnvironment$Companion.getOrCreateApplicationEnvironmentForProduction(Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/config/CompilerConfiguration;)Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreApplicationEnvironment;+16
j  org.jetbrains.kotlin.cli.jvm.compiler.KotlinCoreEnvironment$Companion.createForProduction(Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/cli/jvm/compiler/EnvironmentConfigFiles;)Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment;+25
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.createCoreEnvironment(Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Ljava/lang/String;)Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment;+19
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(Lorg/jetbrains/kotlin/cli/common/arguments/K2JVMCompilerArguments;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/utils/KotlinPaths;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+943
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(Lorg/jetbrains/kotlin/cli/common/arguments/CommonCompilerArguments;Lorg/jetbrains/kotlin/config/CompilerConfiguration;Lorg/jetbrains/kotlin/com/intellij/openapi/Disposable;Lorg/jetbrains/kotlin/utils/KotlinPaths;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+9
j  org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonCompilerArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+220
j  org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonToolArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+7
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Lorg/jetbrains/kotlin/cli/common/messages/MessageCollector;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/arguments/CommonToolArguments;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+76
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Ljava/io/PrintStream;Lorg/jetbrains/kotlin/config/Services;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;[Ljava/lang/String;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+239
j  org.jetbrains.kotlin.cli.common.CLITool.exec(Ljava/io/PrintStream;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;[Ljava/lang/String;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+25
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMainNoExit(Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+39
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMainNoExit$default(Lorg/jetbrains/kotlin/cli/common/CLITool$Companion;Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;Lorg/jetbrains/kotlin/cli/common/messages/MessageRenderer;ILjava/lang/Object;)Lorg/jetbrains/kotlin/cli/common/ExitCode;+16
j  org.jetbrains.kotlin.cli.common.CLITool$Companion.doMain(Lorg/jetbrains/kotlin/cli/common/CLITool;[Ljava/lang/String;)V+54
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler$Companion.main([Ljava/lang/String;)V+20
j  org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.main([Ljava/lang/String;)V+4
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001bd93b45300, length=14, elements={
0x000001bde44a38d0, 0x000001bdffe7bef0, 0x000001bdffe7cd60, 0x000001bdffe95450,
0x000001bdffe95e00, 0x000001bdffe967b0, 0x000001bdffe97160, 0x000001bdffe97e40,
0x000001bdffe98830, 0x000001bdffedcfc0, 0x000001bd933febc0, 0x000001bd934065b0,
0x000001bd93439070, 0x000001bd93441be0
}

Java Threads: ( => current thread )
=>0x000001bde44a38d0 JavaThread "main" [_thread_in_vm, id=1396, stack(0x0000004a80e00000,0x0000004a80f00000)]
  0x000001bdffe7bef0 JavaThread "Reference Handler" daemon [_thread_blocked, id=27388, stack(0x0000004a81500000,0x0000004a81600000)]
  0x000001bdffe7cd60 JavaThread "Finalizer" daemon [_thread_blocked, id=22108, stack(0x0000004a81600000,0x0000004a81700000)]
  0x000001bdffe95450 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=4212, stack(0x0000004a81700000,0x0000004a81800000)]
  0x000001bdffe95e00 JavaThread "Attach Listener" daemon [_thread_blocked, id=27384, stack(0x0000004a81800000,0x0000004a81900000)]
  0x000001bdffe967b0 JavaThread "Service Thread" daemon [_thread_blocked, id=20628, stack(0x0000004a81900000,0x0000004a81a00000)]
  0x000001bdffe97160 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=19412, stack(0x0000004a81a00000,0x0000004a81b00000)]
  0x000001bdffe97e40 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=25000, stack(0x0000004a81b00000,0x0000004a81c00000)]
  0x000001bdffe98830 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=17824, stack(0x0000004a81c00000,0x0000004a81d00000)]
  0x000001bdffedcfc0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=16952, stack(0x0000004a81d00000,0x0000004a81e00000)]
  0x000001bd933febc0 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=24160, stack(0x0000004a81e00000,0x0000004a81f00000)]
  0x000001bd934065b0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=24008, stack(0x0000004a81f00000,0x0000004a82000000)]
  0x000001bd93439070 JavaThread "Notification Thread" daemon [_thread_blocked, id=23964, stack(0x0000004a82100000,0x0000004a82200000)]
  0x000001bd93441be0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=3536, stack(0x0000004a82300000,0x0000004a82400000)]

Other Threads:
  0x000001bdffe73f50 VMThread "VM Thread" [stack: 0x0000004a81400000,0x0000004a81500000] [id=16356] _threads_hazard_ptr=0x000001bd93b45300
  0x000001bd93439780 WatcherThread [stack: 0x0000004a82200000,0x0000004a82300000] [id=24712]
  0x000001bde4551240 GCTaskThread "GC Thread#0" [stack: 0x0000004a80f00000,0x0000004a81000000] [id=21420]
  0x000001bd93b33930 GCTaskThread "GC Thread#1" [stack: 0x0000004a82400000,0x0000004a82500000] [id=17172]
  0x000001bd93b33be0 GCTaskThread "GC Thread#2" [stack: 0x0000004a82500000,0x0000004a82600000] [id=27372]
  0x000001bd93b33e90 GCTaskThread "GC Thread#3" [stack: 0x0000004a82600000,0x0000004a82700000] [id=11240]
  0x000001bd937ac3b0 GCTaskThread "GC Thread#4" [stack: 0x0000004a82700000,0x0000004a82800000] [id=14712]
  0x000001bd937ac660 GCTaskThread "GC Thread#5" [stack: 0x0000004a82800000,0x0000004a82900000] [id=25404]
  0x000001bd937ac910 GCTaskThread "GC Thread#6" [stack: 0x0000004a82900000,0x0000004a82a00000] [id=28428]
  0x000001bd937acbc0 GCTaskThread "GC Thread#7" [stack: 0x0000004a82a00000,0x0000004a82b00000] [id=20080]
  0x000001bd937ace70 GCTaskThread "GC Thread#8" [stack: 0x0000004a82b00000,0x0000004a82c00000] [id=11796]
  0x000001bd93b0d8f0 GCTaskThread "GC Thread#9" [stack: 0x0000004a82c00000,0x0000004a82d00000] [id=25176]
  0x000001bd93ba85d0 GCTaskThread "GC Thread#10" [stack: 0x0000004a82d00000,0x0000004a82e00000] [id=9544]
  0x000001bd93ba8320 GCTaskThread "GC Thread#11" [stack: 0x0000004a82e00000,0x0000004a82f00000] [id=4416]
  0x000001bde4561f40 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000004a81000000,0x0000004a81100000] [id=2836]
  0x000001bde4562950 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000004a81100000,0x0000004a81200000] [id=18464]
  0x000001bde457e960 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000004a81200000,0x0000004a81300000] [id=20744]
  0x000001bdffdae360 ConcurrentGCThread "G1 Service" [stack: 0x0000004a81300000,0x0000004a81400000] [id=25756]

Threads with active compile tasks:

VM state: synchronizing (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001bde449dc30] Metaspace_lock - owner thread: 0x000001bde44a38d0
[0x000001bde449e190] Threads_lock - owner thread: 0x000001bdffe73f50

Heap address: 0x0000000604800000, size: 8120 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001bd94000000-0x000001bd94bd0000-0x000001bd94bd0000), size 12386304, SharedBaseAddress: 0x000001bd94000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001bd95000000-0x000001bdd5000000, reserved size: 1073741824
Narrow klass base: 0x000001bd94000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32479M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8120M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 520192K, used 11040K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 1 survivors (4096K)
 Metaspace       used 8682K, committed 8768K, reserved 1114112K
  class space    used 919K, committed 960K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%|HS|  |TAMS 0x0000000604800000, 0x0000000604800000| Complete 
|   1|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   2|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|   3|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|   4|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|   5|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|   6|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|   7|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|   8|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|   9|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  10|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  11|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  12|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  13|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
|  14|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  15|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  16|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  17|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  18|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  19|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  20|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  21|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Untracked 
|  22|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|  23|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000, 0x000000060a400000| Untracked 
|  24|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000, 0x000000060a800000| Untracked 
|  25|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000, 0x000000060ac00000| Untracked 
|  26|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000, 0x000000060b000000| Untracked 
|  27|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000, 0x000000060b400000| Untracked 
|  28|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000, 0x000000060b800000| Untracked 
|  29|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000, 0x000000060bc00000| Untracked 
|  30|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|  31|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000, 0x000000060c400000| Untracked 
|  32|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000, 0x000000060c800000| Untracked 
|  33|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000, 0x000000060cc00000| Untracked 
|  34|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000, 0x000000060d000000| Untracked 
|  35|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000, 0x000000060d400000| Untracked 
|  36|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000, 0x000000060d800000| Untracked 
|  37|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000, 0x000000060dc00000| Untracked 
|  38|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|  39|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000, 0x000000060e400000| Untracked 
|  40|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000, 0x000000060e800000| Untracked 
|  41|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000, 0x000000060ec00000| Untracked 
|  42|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000, 0x000000060f000000| Untracked 
|  43|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000, 0x000000060f400000| Untracked 
|  44|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000, 0x000000060f800000| Untracked 
|  45|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000, 0x000000060fc00000| Untracked 
|  46|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|  47|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000, 0x0000000610400000| Untracked 
|  48|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000, 0x0000000610800000| Untracked 
|  49|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000, 0x0000000610c00000| Untracked 
|  50|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000, 0x0000000611000000| Untracked 
|  51|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000, 0x0000000611400000| Untracked 
|  52|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000, 0x0000000611800000| Untracked 
|  53|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000, 0x0000000611c00000| Untracked 
|  54|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
|  55|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000, 0x0000000612400000| Untracked 
|  56|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000, 0x0000000612800000| Untracked 
|  57|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000, 0x0000000612c00000| Untracked 
|  58|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000, 0x0000000613000000| Untracked 
|  59|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000, 0x0000000613400000| Untracked 
|  60|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000, 0x0000000613800000| Untracked 
|  61|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000, 0x0000000613c00000| Untracked 
|  62|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
|  63|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000, 0x0000000614400000| Untracked 
|  64|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000, 0x0000000614800000| Untracked 
|  65|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000, 0x0000000614c00000| Untracked 
|  66|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000, 0x0000000615000000| Untracked 
|  67|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000, 0x0000000615400000| Untracked 
|  68|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000, 0x0000000615800000| Untracked 
|  69|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000, 0x0000000615c00000| Untracked 
|  70|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
|  71|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000, 0x0000000616400000| Untracked 
|  72|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000, 0x0000000616800000| Untracked 
|  73|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000, 0x0000000616c00000| Untracked 
|  74|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000, 0x0000000617000000| Untracked 
|  75|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000, 0x0000000617400000| Untracked 
|  76|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000, 0x0000000617800000| Untracked 
|  77|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000, 0x0000000617c00000| Untracked 
|  78|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
|  79|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000, 0x0000000618400000| Untracked 
|  80|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000, 0x0000000618800000| Untracked 
|  81|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000, 0x0000000618c00000| Untracked 
|  82|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000, 0x0000000619000000| Untracked 
|  83|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000, 0x0000000619400000| Untracked 
|  84|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000, 0x0000000619800000| Untracked 
|  85|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000, 0x0000000619c00000| Untracked 
|  86|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000, 0x000000061a000000| Untracked 
|  87|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000, 0x000000061a400000| Untracked 
|  88|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000, 0x000000061a800000| Untracked 
|  89|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000, 0x000000061ac00000| Untracked 
|  90|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000, 0x000000061b000000| Untracked 
|  91|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000, 0x000000061b400000| Untracked 
|  92|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000, 0x000000061b800000| Untracked 
|  93|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000, 0x000000061bc00000| Untracked 
|  94|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000, 0x000000061c000000| Untracked 
|  95|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000, 0x000000061c400000| Untracked 
|  96|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000, 0x000000061c800000| Untracked 
|  97|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000, 0x000000061cc00000| Untracked 
|  98|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000, 0x000000061d000000| Untracked 
|  99|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000, 0x000000061d400000| Untracked 
| 100|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000, 0x000000061d800000| Untracked 
| 101|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000, 0x000000061dc00000| Untracked 
| 102|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000, 0x000000061e000000| Untracked 
| 103|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000, 0x000000061e400000| Untracked 
| 104|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000, 0x000000061e800000| Untracked 
| 105|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000, 0x000000061ec00000| Untracked 
| 106|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000, 0x000000061f000000| Untracked 
| 107|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000, 0x000000061f400000| Untracked 
| 108|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000, 0x000000061f800000| Untracked 
| 109|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000, 0x000000061fc00000| Untracked 
| 110|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000, 0x0000000620000000| Untracked 
| 111|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000, 0x0000000620400000| Untracked 
| 112|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000, 0x0000000620800000| Untracked 
| 113|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000, 0x0000000620c00000| Untracked 
| 114|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 115|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000, 0x0000000621400000| Untracked 
| 116|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000, 0x0000000621800000| Untracked 
| 117|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000, 0x0000000621c00000| Untracked 
| 118|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000, 0x0000000622000000| Untracked 
| 119|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000, 0x0000000622400000| Untracked 
| 120|0x0000000622800000, 0x0000000622ac8188, 0x0000000622c00000| 69%| S|CS|TAMS 0x0000000622800000, 0x0000000622800000| Complete 
| 121|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000, 0x0000000622c00000| Untracked 
| 122|0x0000000623000000, 0x0000000623000000, 0x0000000623400000|  0%| F|  |TAMS 0x0000000623000000, 0x0000000623000000| Untracked 
| 123|0x0000000623400000, 0x0000000623400000, 0x0000000623800000|  0%| F|  |TAMS 0x0000000623400000, 0x0000000623400000| Untracked 
| 124|0x0000000623800000, 0x0000000623800000, 0x0000000623c00000|  0%| F|  |TAMS 0x0000000623800000, 0x0000000623800000| Untracked 
| 125|0x0000000623c00000, 0x0000000623f29a98, 0x0000000624000000| 79%| E|  |TAMS 0x0000000623c00000, 0x0000000623c00000| Complete 
| 126|0x0000000624000000, 0x0000000624400000, 0x0000000624400000|100%| E|CS|TAMS 0x0000000624000000, 0x0000000624000000| Complete 

Card table byte_map: [0x000001bdfe4c0000,0x000001bdff4a0000] _byte_map_base: 0x000001bdfb49c000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001bde4551850, (CMBitMap*) 0x000001bde4551890
 Prev Bits: [0x000001bd80fe0000, 0x000001bd88ec0000)
 Next Bits: [0x000001bd88ec0000, 0x000001bd90da0000)

Polling page: 0x000001bde4300000

Metaspace:

Usage:
  Non-class:      7.58 MB used.
      Class:    919.19 KB used.
       Both:      8.48 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       7.62 MB ( 12%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     960.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       8.56 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  5.86 MB
       Class:  14.94 MB
        Both:  20.80 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 98.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 137.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 375.
num_chunk_merges: 0.
num_chunk_splits: 283.
num_chunks_enlarged: 244.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=263Kb max_used=263Kb free=118904Kb
 bounds [0x000001bdf50d0000, 0x000001bdf5340000, 0x000001bdfc530000]
CodeHeap 'profiled nmethods': size=119104Kb used=1478Kb max_used=1478Kb free=117625Kb
 bounds [0x000001bded530000, 0x000001bded7a0000, 0x000001bdf4980000]
CodeHeap 'non-nmethods': size=7488Kb used=2291Kb max_used=2857Kb free=5196Kb
 bounds [0x000001bdf4980000, 0x000001bdf4c50000, 0x000001bdf50d0000]
 total_blobs=1345 nmethods=886 adapters=370
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 2.479 Thread 0x000001bd933febc0  874       3       jdk.internal.org.objectweb.asm.MethodVisitor::<init> (88 bytes)
Event: 2.479 Thread 0x000001bdffe98830 nmethod 872 0x000001bded69d590 code [0x000001bded69d780, 0x000001bded69de28]
Event: 2.479 Thread 0x000001bd934065b0 nmethod 873 0x000001bdf5110890 code [0x000001bdf5110a20, 0x000001bdf5110af8]
Event: 2.479 Thread 0x000001bdffe98830  875       3       jdk.internal.org.objectweb.asm.Frame::getInputStackSize (6 bytes)
Event: 2.479 Thread 0x000001bdffe98830 nmethod 875 0x000001bded69e010 code [0x000001bded69e1a0, 0x000001bded69e2b8]
Event: 2.479 Thread 0x000001bd934065b0  876       3       jdk.internal.org.objectweb.asm.Type::<init> (26 bytes)
Event: 2.479 Thread 0x000001bd933febc0 nmethod 874 0x000001bded69e390 code [0x000001bded69e5e0, 0x000001bded69eec8]
Event: 2.480 Thread 0x000001bd934065b0 nmethod 876 0x000001bded69f190 code [0x000001bded69f320, 0x000001bded69f4f8]
Event: 2.507 Thread 0x000001bd934065b0  877       3       java.util.Arrays::copyOf (10 bytes)
Event: 2.507 Thread 0x000001bd934065b0 nmethod 877 0x000001bded69f590 code [0x000001bded69f740, 0x000001bded69f888]
Event: 2.512 Thread 0x000001bd934065b0  879       3       jdk.internal.org.objectweb.asm.SymbolTable::addTypeInternal (85 bytes)
Event: 2.513 Thread 0x000001bd934065b0 nmethod 879 0x000001bded69f990 code [0x000001bded69fb60, 0x000001bded6a01d8]
Event: 2.514 Thread 0x000001bd934065b0  880       3       java.lang.invoke.MemberName::<init> (50 bytes)
Event: 2.514 Thread 0x000001bd934065b0 nmethod 880 0x000001bded6a0390 code [0x000001bded6a0560, 0x000001bded6a0878]
Event: 2.516 Thread 0x000001bd934065b0  884       1       java.lang.ClassLoader::getUnnamedModule (5 bytes)
Event: 2.516 Thread 0x000001bd934065b0 nmethod 884 0x000001bdf5111790 code [0x000001bdf5111920, 0x000001bdf51119f8]
Event: 2.520 Thread 0x000001bdffe97e40  885       4       jdk.internal.misc.Unsafe::allocateUninitializedArray (55 bytes)
Event: 2.521 Thread 0x000001bd934065b0  886       3       jdk.internal.org.objectweb.asm.Frame::accept (264 bytes)
Event: 2.521 Thread 0x000001bdffe97e40 nmethod 885 0x000001bdf5111a90 code [0x000001bdf5111c20, 0x000001bdf5111df8]
Event: 2.521 Thread 0x000001bd934065b0 nmethod 886 0x000001bded6a0a10 code [0x000001bded6a0c40, 0x000001bded6a1578]

GC Heap History (2 events):
Event: 1.522 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 520192K, used 24576K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 6820K, committed 6912K, reserved 1114112K
  class space    used 706K, committed 768K, reserved 1048576K
}
Event: 2.104 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 6944K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 6820K, committed 6912K, reserved 1114112K
  class space    used 706K, committed 768K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 1.332 Thread 0x000001bde44a38d0 DEOPT PACKING pc=0x000001bded5706e5 sp=0x0000004a80efeaf0
Event: 1.332 Thread 0x000001bde44a38d0 DEOPT UNPACKING pc=0x000001bdf49d2b43 sp=0x0000004a80efdf40 mode 0
Event: 1.422 Thread 0x000001bde44a38d0 DEOPT PACKING pc=0x000001bded57f6c8 sp=0x0000004a80efb4a0
Event: 1.422 Thread 0x000001bde44a38d0 DEOPT UNPACKING pc=0x000001bdf49d2b43 sp=0x0000004a80efa9b8 mode 0
Event: 2.521 Thread 0x000001bde44a38d0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001bdf5111d48 relative=0x0000000000000128
Event: 2.521 Thread 0x000001bde44a38d0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001bdf5111d48 method=jdk.internal.misc.Unsafe.allocateUninitializedArray(Ljava/lang/Class;I)Ljava/lang/Object; @ 51 c2
Event: 2.521 Thread 0x000001bde44a38d0 DEOPT PACKING pc=0x000001bdf5111d48 sp=0x0000004a80efc830
Event: 2.521 Thread 0x000001bde44a38d0 DEOPT UNPACKING pc=0x000001bdf49d23a3 sp=0x0000004a80efc7b8 mode 2
Event: 2.521 Thread 0x000001bde44a38d0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001bdf5111d48 relative=0x0000000000000128
Event: 2.521 Thread 0x000001bde44a38d0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001bdf5111d48 method=jdk.internal.misc.Unsafe.allocateUninitializedArray(Ljava/lang/Class;I)Ljava/lang/Object; @ 51 c2
Event: 2.521 Thread 0x000001bde44a38d0 DEOPT PACKING pc=0x000001bdf5111d48 sp=0x0000004a80efc620
Event: 2.521 Thread 0x000001bde44a38d0 DEOPT UNPACKING pc=0x000001bdf49d23a3 sp=0x0000004a80efc5a8 mode 2
Event: 2.522 Thread 0x000001bde44a38d0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001bdf5111d48 relative=0x0000000000000128
Event: 2.522 Thread 0x000001bde44a38d0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001bdf5111d48 method=jdk.internal.misc.Unsafe.allocateUninitializedArray(Ljava/lang/Class;I)Ljava/lang/Object; @ 51 c2
Event: 2.522 Thread 0x000001bde44a38d0 DEOPT PACKING pc=0x000001bdf5111d48 sp=0x0000004a80efc720
Event: 2.522 Thread 0x000001bde44a38d0 DEOPT UNPACKING pc=0x000001bdf49d23a3 sp=0x0000004a80efc6a8 mode 2
Event: 2.522 Thread 0x000001bde44a38d0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001bdf5111d48 relative=0x0000000000000128
Event: 2.522 Thread 0x000001bde44a38d0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001bdf5111d48 method=jdk.internal.misc.Unsafe.allocateUninitializedArray(Ljava/lang/Class;I)Ljava/lang/Object; @ 51 c2
Event: 2.522 Thread 0x000001bde44a38d0 DEOPT PACKING pc=0x000001bdf5111d48 sp=0x0000004a80efc7c0
Event: 2.522 Thread 0x000001bde44a38d0 DEOPT UNPACKING pc=0x000001bdf49d23a3 sp=0x0000004a80efc748 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 1.482 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622fcba90}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x0000000622fcba90) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.511 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622fd5818}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622fd5818) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.520 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622ff5a88}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int)'> (0x0000000622ff5a88) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.109 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000624002d10}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x0000000624002d10) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.125 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062408a728}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062408a728) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.160 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000624183048}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object)'> (0x0000000624183048) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.160 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006241867a0}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x00000006241867a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.380 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006243898a0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006243898a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.428 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d4d058}: 'long java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623d4d058) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.429 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d586e8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623d586e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.430 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d5c3d8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623d5c3d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.430 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d5fa40}: 'int java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623d5fa40) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.433 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d6efc8}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x0000000623d6efc8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.436 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d76c00}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x0000000623d76c00) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.439 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d85fc0}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object, java.lang.Object)'> (0x0000000623d85fc0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.442 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d94b50}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int, int)'> (0x0000000623d94b50) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.445 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623da35b0}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, long, long)'> (0x0000000623da35b0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.453 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623db1bc8}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int)'> (0x0000000623db1bc8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.512 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623ea1698}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623ea1698) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.520 Thread 0x000001bde44a38d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623eca4e0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623eca4e0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (8 events):
Event: 0.522 Executing VM operation: HandshakeAllThreads
Event: 0.522 Executing VM operation: HandshakeAllThreads done
Event: 1.018 Executing VM operation: HandshakeAllThreads
Event: 1.018 Executing VM operation: HandshakeAllThreads done
Event: 1.134 Executing VM operation: HandshakeAllThreads
Event: 1.134 Executing VM operation: HandshakeAllThreads done
Event: 1.522 Executing VM operation: G1CollectForAllocation
Event: 2.104 Executing VM operation: G1CollectForAllocation done

Events (20 events):
Event: 2.379 loading class java/lang/SecurityException
Event: 2.379 loading class java/lang/SecurityException done
Event: 2.379 loading class sun/misc/Unsafe
Event: 2.379 loading class sun/misc/Unsafe done
Event: 2.382 loading class jdk/internal/reflect/UnsafeFieldAccessorFactory
Event: 2.382 loading class jdk/internal/reflect/UnsafeFieldAccessorFactory done
Event: 2.382 loading class jdk/internal/reflect/UnsafeQualifiedStaticObjectFieldAccessorImpl
Event: 2.382 loading class jdk/internal/reflect/UnsafeQualifiedStaticFieldAccessorImpl
Event: 2.382 loading class jdk/internal/reflect/UnsafeQualifiedStaticFieldAccessorImpl done
Event: 2.382 loading class jdk/internal/reflect/UnsafeQualifiedStaticObjectFieldAccessorImpl done
Event: 2.413 loading class java/util/function/DoubleFunction
Event: 2.413 loading class java/util/function/DoubleFunction done
Event: 2.421 loading class java/io/StringWriter
Event: 2.421 loading class java/io/StringWriter done
Event: 2.473 loading class java/util/EmptyStackException
Event: 2.473 loading class java/util/EmptyStackException done
Event: 2.483 loading class java/lang/reflect/InvocationTargetException
Event: 2.483 loading class java/lang/reflect/InvocationTargetException done
Event: 2.507 loading class java/util/concurrent/BlockingQueue
Event: 2.507 loading class java/util/concurrent/BlockingQueue done


Dynamic libraries:
0x00007ff708300000 - 0x00007ff708310000 	E:\JAVA\bin\java.exe
0x00007ff827030000 - 0x00007ff827228000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8255d0000 - 0x00007ff825692000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff824d50000 - 0x00007ff825046000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8246c0000 - 0x00007ff8247c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff81fde0000 - 0x00007ff81fdf9000 	E:\JAVA\bin\jli.dll
0x00007ff826e80000 - 0x00007ff826f31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff826340000 - 0x00007ff8263de000 	C:\Windows\System32\msvcrt.dll
0x00007ff825e80000 - 0x00007ff825f1f000 	C:\Windows\System32\sechost.dll
0x00007ff825b10000 - 0x00007ff825c33000 	C:\Windows\System32\RPCRT4.dll
0x00007ff824a20000 - 0x00007ff824a47000 	C:\Windows\System32\bcrypt.dll
0x00007ff825050000 - 0x00007ff8251ed000 	C:\Windows\System32\USER32.dll
0x00007ff8248a0000 - 0x00007ff8248c2000 	C:\Windows\System32\win32u.dll
0x00007ff825da0000 - 0x00007ff825dcb000 	C:\Windows\System32\GDI32.dll
0x00007ff824a50000 - 0x00007ff824b69000 	C:\Windows\System32\gdi32full.dll
0x00007ff824980000 - 0x00007ff824a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff81fdc0000 - 0x00007ff81fddb000 	E:\JAVA\bin\VCRUNTIME140.dll
0x00007ff823400000 - 0x00007ff82340a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff81dfe0000 - 0x00007ff81e27a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8253f0000 - 0x00007ff82541f000 	C:\Windows\System32\IMM32.DLL
0x00007ff81fd50000 - 0x00007ff81fd5c000 	E:\JAVA\bin\vcruntime140_1.dll
0x00007ffffb0c0000 - 0x00007ffffb14e000 	E:\JAVA\bin\msvcp140.dll
0x00007fffe0350000 - 0x00007fffe0f2e000 	E:\JAVA\bin\server\jvm.dll
0x00007ff826f40000 - 0x00007ff826f48000 	C:\Windows\System32\PSAPI.DLL
0x00007fffeb340000 - 0x00007fffeb349000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff826e10000 - 0x00007ff826e7b000 	C:\Windows\System32\WS2_32.dll
0x00007ff81e3c0000 - 0x00007ff81e3e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff823360000 - 0x00007ff823372000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff81c4f0000 - 0x00007ff81c4fa000 	E:\JAVA\bin\jimage.dll
0x00007ff821e10000 - 0x00007ff822011000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff80fef0000 - 0x00007ff80ff24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff824810000 - 0x00007ff824892000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff818d20000 - 0x00007ff818d45000 	E:\JAVA\bin\java.dll
0x00007fffef390000 - 0x00007fffef467000 	E:\JAVA\bin\jsvml.dll
0x00007ff826570000 - 0x00007ff826cde000 	C:\Windows\System32\SHELL32.dll
0x00007ff8222d0000 - 0x00007ff822a74000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff825fd0000 - 0x00007ff826323000 	C:\Windows\System32\combase.dll
0x00007ff8240f0000 - 0x00007ff82411b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff826d40000 - 0x00007ff826e0d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff825f20000 - 0x00007ff825fcd000 	C:\Windows\System32\SHCORE.dll
0x00007ff825d40000 - 0x00007ff825d9b000 	C:\Windows\System32\shlwapi.dll
0x00007ff8245f0000 - 0x00007ff824614000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff811d70000 - 0x00007ff811d89000 	E:\JAVA\bin\net.dll
0x00007ff81ede0000 - 0x00007ff81eeea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff823e50000 - 0x00007ff823eba000 	C:\Windows\system32\mswsock.dll
0x00007ff810fb0000 - 0x00007ff810fc6000 	E:\JAVA\bin\nio.dll
0x00007ff81bc20000 - 0x00007ff81bc38000 	E:\JAVA\bin\zip.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\JAVA\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;E:\JAVA\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\framework\HeytapAccount\build\20250901_17026502023556230817.compiler.options
java_class_path (initial): E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.22\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\kotlin-compiler-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.22\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\kotlin-script-runtime-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.22\20e2c5df715f3240c765cfc222530e2796542021\kotlin-daemon-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8514437120                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8514437120                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\JAVA
CLASSPATH=.;E:\JAVA\lib\dt.jar;E:\JAVA\lib\tools.jar;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\dotnet\;E:\git\Git\cmd;D:\Users\W9096060\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\dotnet\;E:\1.8java\lib\dt.jar;E:\1.8java\lib\tools.jar;E:\1.8java\bin;E:\JAVA\bin;E:\JAVA\lib;D:\Users\W9096060\AppData\Local\Microsoft\WindowsApps;
USERNAME=W9096060
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 19:56 hours

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 32479M (2707M free)
TotalPageFile size 47840M (AvailPageFile size 368M)
current process WorkingSet (physical memory assigned to process): 83M, peak: 83M
current process commit charge ("private bytes"): 632M, peak: 632M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211) for windows-amd64 JRE (17.0.8+9-LTS-211), built on Jun 14 2023 10:34:31 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
