/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WebBaseUriLoader
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/26 11:18
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/26       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.loader

import android.content.Context
import android.database.Cursor
import android.net.Uri
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.FileTaskLoader
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.closeQuietly

abstract class WebBaseUriLoader<T : BaseFileBean> : FileTaskLoader<MutableList<T>> {
    companion object {
        private const val TAG = "WebBaseUriLoader"
    }

    var mUri: Uri? = null
    var mProjection: Array<String>? = null
    var mSelectionArgs: Array<String>? = null
    var mSelection: String? = null
    var mSortOrder: String? = null
    var mLoadResult: MutableList<T>? = null
    var mObserver: ForceLoadContentObserver? = null
    var mObserverUris: Array<Uri>? = null
    var mInterrupt = false
    var mTotal = 0

    constructor(context: Context) : super(context)

    @Suppress("TooGenericExceptionCaught")
    fun initData() {
        mUri = getUri()
        mObserverUris = getObserverUri()
        mObserverUris?.let {
            if (it.isNotEmpty()) {
                mObserver = ForceLoadContentObserver()
                for (observerUri in it) {
                    try {
                        context.contentResolver?.registerContentObserver(
                            observerUri,
                            true,
                            mObserver!!
                        )
                    } catch (e: Exception) {
                        Log.i(TAG, "registerContentObserver failed")
                    }
                }
            }
        }
    }

    private fun isCancelled() = Thread.currentThread().isInterrupted.also {
        if (it) {
            Log.d(TAG, "loadInBackground interrupted")
        }
    }

    @Suppress("NestedBlockDepth", "TooGenericExceptionCaught")
    override fun loadInBackground(): MutableList<T>? {
        var cursor: Cursor? = null
        val items = ArrayList<T>()
        Log.d(TAG, "loadInBackground()")
        try {
            if (mUri != null) {
                mProjection = getProjection()
                mSelectionArgs = getSelectionArgs()
                mSelection = getSelection()
                mSortOrder = getSortOrder()
                preHandleBeforeBackground()
                Log.d(
                    TAG,
                    "loadInBackground() mUri=$mUri,mProjection=$mProjection," +
                            "mSelection=$mSelection,mSelectionArgs=$mSelectionArgs,mSortOrder=$mSortOrder"
                )
                cursor = context.contentResolver.query(
                    mUri!!, mProjection, mSelection,
                    mSelectionArgs, mSortOrder
                )
                if ((cursor != null) && !isCancelled()) {
                    mTotal = cursor.count
                    Log.d(TAG, "cursor not null , mTotal =$mTotal")
                    while (cursor.moveToNext() && !isCancelled()) {
                        try {
                            val item = createFromCursor(cursor, mUri)
                            Log.d(TAG, "item=$item")
                            if (item != null) {
                                items.add(item)
                            }
                        } catch (ex: Exception) {
                            Log.i(TAG, "createFromCursor exception")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        } finally {
            cursor.closeQuietly()
        }
        val sortItems = preHandleResultBackground(items)
        Log.d(TAG, "loadInBackground() sortItems.size= ${sortItems.size}")
        return sortItems
    }

    /**
     *get URI, using the content:// scheme, for the content to
     *retrieve.
     * @return uri  can be null
     */
    abstract fun getUri(): Uri?

    /**
     * A filter declaring which rows to return, formatted as an
     *SQL WHERE clause (excluding the WHERE itself). Passing null will
     *return all rows for the given URI.
     */
    abstract fun getSelection(): String?

    /**
     * You may include ?s in selection, which will be
     * replaced by the values from selectionArgs, in the order that they
     *appear in the selection. The values will be bound as Strings.
     * @return can be null
     */
    abstract fun getSelectionArgs(): Array<String>?

    /**
     * get URI array, using the content:// scheme, for the content to
     *retrieve.
     * @return can be null
     */
    abstract fun getObserverUri(): Array<Uri>?

    /**
     * A list of which columns to return. Passing null will
     * return all columns, which is inefficient.
     * @return can be null
     */
    abstract fun getProjection(): Array<String>?

    /**
     *Create data from Cursor
     * @return The data of the cursor's current position, which can be null
     */
    abstract fun createFromCursor(cursor: Cursor, uri: Uri?): T?

    /**
     * Processing the query result list in background,Subclasses can be replicated
     * @return Processed results
     */
    open fun preHandleResultBackground(list: MutableList<T>): MutableList<T> {
        return list
    }

    /**
     * Preprocessing before querying data in background,Subclasses can be replicated
     */
    open fun preHandleBeforeBackground() {
    }

    /**
     * Collation of the query,Subclasses can be replicated
     */
    open fun getSortOrder(): String? {
        return null
    }

    fun getOrderWithType(sort: Int): String {
        val orderBy: String
        if (sort == SortHelper.FILE_TIME_REVERSE_ORDER) {
            orderBy = "date_modified DESC"
        } else if (sort == SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER) {
            orderBy = "_size DESC"
        } else {
            orderBy = "date_modified DESC"
        }
        return orderBy
    }

    override fun onStartLoading() {
        if (mInterrupt) {
            return
        }

        val resultSize = mLoadResult?.size ?: 0
        if (resultSize > 0) {
            deliverResult(mLoadResult)
        }

        if (takeContentChanged() || (mLoadResult == null) || (resultSize == 0)) {
            forceLoad()
        }
    }

    override fun onStopLoading() {
        cancelLoad()
    }

    @Suppress("TooGenericExceptionCaught")
    override fun forceLoad() {
        try {
            super.forceLoad()
        } catch (e: Exception) {
            Log.w(TAG, "forceLoad " + e.message)
        }
    }

    override fun deliverResult(data: MutableList<T>?) {
        if (mReset) {
            data?.clear()
            return
        }
        mLoadResult = data
        if (mStarted) {
            super.deliverResult(data)
        }
    }

    @Suppress("TooGenericExceptionCaught")
    override fun onReset() {
        super.onReset()
        onStopLoading()
        mObserver?.let {
            try {
                context.contentResolver?.unregisterContentObserver(it)
            } catch (e: Exception) {
                Log.i(TAG, "unregisterContentObserver exception")
            }
        }
    }
}