/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AccountManager
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/8 16:33
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.account

import android.content.Context
import com.filemanager.common.bean.TransitionCloudAccount
import com.filemanager.common.utils.Log
import com.heytap.usercenter.accountsdk.AcExtension
import com.heytap.usercenter.accountsdk.AccountAgent
import com.heytap.usercenter.accountsdk.AccountAgentClient
import com.heytap.usercenter.accountsdk.AccountSDKConfig
import com.heytap.usercenter.accountsdk.http.AccountNameTask
import com.heytap.usercenter.accountsdk.model.AccountEntity
import com.heytap.usercenter.accountsdk.model.SignInAccount

object AccountManager {
    private const val TAG = "AccountManager"
    private const val APP_CODE = ""

    var showAcPage: Boolean = false

    @JvmStatic
    fun initSdk(context: Context) {
        Log.d(TAG, "initSdk")
        val builder: AccountSDKConfig.Builder = AccountSDKConfig.Builder()
            .context(context)
            .env(AccountSDKConfig.ENV.ENV_RELEASE)
            .extension(object : AcExtension {
                override fun isForeground(): Boolean {
                    return true
                }

                override fun isShowAcPage(): Boolean {
                    return showAcPage
                }
            })
        AccountAgentClient.get().init(builder.create())
    }

    @JvmStatic
    fun isLogin(context: Context): Boolean {
        val entity = getAccountEntity(context)
        return entity?.authToken?.isNotEmpty() ?: false
    }

    @JvmStatic
    fun getUserCountry(context: Context): String? {
        return AccountAgent.reqAccountCountry(context)
    }

    @JvmStatic
    fun login(context: Context, isShowAcPage: Boolean, callback: (Boolean) -> Unit) {
        showAcPage = isShowAcPage
        AccountAgent.reqSignInAccount(context, APP_CODE, object : AccountNameTask.onReqAccountCallback<SignInAccount> {
            override fun onReqStart() {
                Log.d(TAG, "login -> onReqStart")
            }

            override fun onReqLoading() {
                Log.d(TAG, "login -> onReqLoading")
            }

            override fun onReqFinish(account: SignInAccount?) {
                Log.d(TAG, "onReqFinish -> account = $account")
                val isLogin = account?.isLogin ?: false
                Log.d(TAG, "login -> onReqFinish account login = $isLogin")
                callback.invoke(isLogin)
            }
        })
    }

    @JvmStatic
    fun reqSignInAccount(context: Context, callback: (TransitionCloudAccount) -> Unit) {
        AccountAgent.reqSignInAccount(context, APP_CODE, object : AccountNameTask.onReqAccountCallback<SignInAccount> {
            override fun onReqStart() {
                Log.d(TAG, "reqSignInAccount -> onReqStart")
            }

            override fun onReqLoading() {
                Log.d(TAG, "reqSignInAccount -> onReqLoading")
            }

            override fun onReqFinish(account: SignInAccount) {
                Log.d(TAG, "reqSignInAccount -> account = $account")
                val transitionCloudAccount = TransitionCloudAccount(
                    userId = account.userInfo.ssoid,
                    username = account.userInfo.accountName,
                    avatar = account.userInfo.avatarUrl,
                    token = account.token,
                    isLogin = account.isLogin,
                    status = account.userInfo.status,
                    resultCode = account.resultCode,
                    resultMsg = account.resultMsg
                )
                callback.invoke(transitionCloudAccount)
            }
        })
    }

    @JvmStatic
    fun getUserToken(context: Context): String {
        val entity = getAccountEntity(context)
        return entity?.authToken ?: ""
    }

    @JvmStatic
    fun getUserId(context: Context): String? = getAccountEntity(context)?.ssoid

    @JvmStatic
    fun getUserName(context: Context): String? = getAccountEntity(context)?.accountName

    @JvmStatic
    private fun getAccountEntity(context: Context): AccountEntity? {
        return AccountAgent.getAccountEntity(context, APP_CODE)
    }
}