/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CardDataExt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/15 21:03
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/15       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.utils

import com.oplus.pantanal.seedling.bean.SeedlingCard

fun SeedlingCard.generateCardId(): String {
    val cardId = this.cardId
    val cardIndex = this.cardIndex
    return "$cardId&$cardIndex"
}