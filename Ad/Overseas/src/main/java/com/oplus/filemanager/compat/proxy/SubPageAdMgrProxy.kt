/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SubPageAdMgrProxy
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/3/16 16:28
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/3/16       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat.proxy

import android.app.Activity
import android.widget.RelativeLayout
import androidx.annotation.VisibleForTesting
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.compat.interfaces.ISubPageAdMgr

class SubPageAdMgrProxy(
    @get:VisibleForTesting val subPageAdMgr: ISubPageAdMgr
) : ISubPageAdMgr {

    override fun makeName(name: String): String {
        return subPageAdMgr.makeName(name)
    }

    override fun requestSubAd(
        activity: Activity,
        name: String,
        adapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>,
        fileList: ArrayList<MediaFileWrapper>,
        needHideAd: Boolean
    ) {
        subPageAdMgr.requestSubAd(activity, name, adapter, fileList, needHideAd)
    }

    override fun scanModeChange(name: String, needHideAd: Boolean) {
        subPageAdMgr.scanModeChange(name, needHideAd)
    }

    override fun getEntryView(name: String): RelativeLayout? {
        return subPageAdMgr.getEntryView(name)
    }

    override fun requestCommonAd(
        activity: Activity,
        adapter: BaseSelectionRecycleAdapter<*, MediaFileWrapper>,
        fileList: ArrayList<MediaFileWrapper>
    ) {
        subPageAdMgr.requestCommonAd(activity, adapter, fileList)
    }

    override fun requestSubAlbumSetAd(
        activity: Activity,
        adapter: BaseSelectionRecycleAdapter<*, AlbumItem>,
        fileList: ArrayList<AlbumItem>
    ) {
        subPageAdMgr.requestSubAlbumSetAd(activity, adapter, fileList)
    }

    override fun requestPhoneStorageAd(
        activity: Activity,
        adapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>,
        fileList: ArrayList<BaseFileBean>
    ) {
        subPageAdMgr.requestPhoneStorageAd(activity, adapter, fileList)
    }

    override fun refreshIfListChanged() {
        subPageAdMgr.refreshIfListChanged()
    }

    override fun refreshByScanModeChanged() {
        subPageAdMgr.refreshByScanModeChanged()
    }

    override fun eventDestroy() {
        subPageAdMgr.eventDestroy()
    }
}