/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.audio
 ** Version: 1.0
 ** Date: 2021/7/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.audio

import androidx.annotation.Keep
import com.bumptech.glide.load.model.FileLoader
import com.filemanager.thumbnail.utils.Log
import java.io.*

/**
 * load origin data in the file, and then convert to the AudioThumbnailResultByteArray
 */
@Keep
class AudioThumbnailResultByteArrayFactory : FileLoader.Factory<AudioThumbnailResultByteArray>(object :
    FileLoader.FileOpener<AudioThumbnailResultByteArray> {

    override fun getDataClass(): Class<AudioThumbnailResultByteArray> {
        return AudioThumbnailResultByteArray::class.java
    }

    override fun open(file: File?): AudioThumbnailResultByteArray? {
        val os: InputStream?
        return try {
            os = ObjectInputStream(FileInputStream(file))
            os.use {
                os.readObject() as AudioThumbnailResultByteArray
            }
        } catch (e: Exception) {
            Log.d(TAG, "Failed to decode AudioThumbnailResult: $e")
            null
        }
    }

    override fun close(data: AudioThumbnailResultByteArray?) {

    }

}) {

    companion object {
        private const val TAG = "AudioThumbnailResultByteArrayFactory"
    }
}