<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include layout="@layout/appbar_with_divider_layout_secondary" />

    <TextView
        android:id="@+id/move_title_id"
        style="@style/akey_choice_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/appbar_layout"
        android:gravity="center"
        android:text="@string/string_select_data_transferred" />

    <TextView
        android:id="@+id/selected_size_id"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/move_title_id"
        android:layout_centerHorizontal="true"
        android:layout_marginHorizontal="@dimen/default_margin"
        android:layout_marginTop="@dimen/dimen_8dp"
        android:gravity="center"
        android:text="@string/string_already_chosen"
        android:textColor="@color/black_55_percent"
        android:textSize="@dimen/TD07" />

    <TextView
        android:id="@+id/sdcard_space_id"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/selected_size_id"
        android:layout_centerHorizontal="true"
        android:layout_marginHorizontal="@dimen/default_margin"
        android:gravity="center_horizontal"
        android:text="@string/string_sdcard_available_space"
        android:textColor="@color/black_55_percent"
        android:textSize="@dimen/TD07" />

    <com.filemanager.common.base.FileManagerExpandableRecyclerView
        android:id="@+id/recyclerview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/sdcard_space_id"
        android:layout_marginTop="@dimen/akeytomove_listview_margin_top"
        android:childDivider="@null"
        android:divider="@null"
        android:scrollbars="none" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/operation_btn_background_height"
        android:layout_alignParentBottom="true"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:background="?attr/couiColorBackgroundWithCard">
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_background_height"
            android:layout_gravity="top"
            android:alpha="1"
            android:background="?attr/couiColorDivider"
            android:forceDarkAllowed="false" />
        <com.coui.appcompat.button.COUIButton
            style="@style/Widget.COUI.Button.Large"
            android:id="@+id/move_btn"
            android:layout_width="@dimen/operation_btn_width"
            android:layout_height="@dimen/operation_btn_height"
            android:paddingHorizontal="@dimen/dimen_7dp"
            android:paddingVertical="@dimen/dimen_2dp"
            android:layout_gravity="bottom|center_horizontal"
            android:layout_marginBottom="@dimen/operation_btn_margin_bottom"
            android:forceDarkAllowed="false"
            android:text="@string/string_a_key_moving_menue"
            android:textSize="@dimen/akeytomove_btn_text_size" />
    </FrameLayout>
</RelativeLayout>