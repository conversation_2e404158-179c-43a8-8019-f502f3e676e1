/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DeeplinkUtils
 ** Description : Deeplink 相关的工具类
 ** Version     : 1.0
 ** Date        : 2024/06/21 15:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/06/21       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.utils

import android.content.Intent
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Log

object DeeplinkUtils {

    private const val TAG = "DeeplinkUtils"

    /**
     * 返回结果中跳转搜索页面的key
     */
    const val SEARCH_DEEP_LINK = "search_deeplink"

    /**
     * 返回结果中跳转文档页面的key
     */
    const val DOC_DEEP_LINK = "doc_deeplink"

    /**
     * 搜索界面的deeplink
     */
    const val DEEP_LINK_SEARCH = "filemanager://deeplink.search?${KtConstants.P_CATEGORY_TYPE}=${CategoryHelper.CATEGORY_DOC}" +
            "&${KtConstants.P_KEY_WORD}"

    /**
     * 文档界面的deeplink
     */
    const val DEEP_LINK_DOC = "filemanager://deeplink.doc"

    /**
     * 返回结果中跳转文管打开文件的key
     */
    const val OPEN_DEEP_LINK = "open_deeplink"

    /**
     * 打开文件的deeplink
     */
    const val DEEP_LINK_OPEN = "filemanager://deeplink.open?file_path="


    /**
     * 从deeplink中获取传递的结果值
     * @param intent
     * @param key 查询的key
     * @return 传递的结果值
     */
    @JvmStatic
    fun getValueFromDeepLink(intent: Intent, key: String): String? {
        var result: String? = null
        if (Intent.ACTION_VIEW == intent.action) {
            val uri = intent.data
            uri?.apply {
                result = this.getQueryParameter(key)
            }
        }
        return result.apply {
            Log.d(TAG, "getValueFromDeepLink $key -> $this")
        }
    }
}