/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PersonalInfoDetailActivity
 * * Description: 收集个人信息明示清单详情界面
 * * Version: 1.0
 * * Date : 2024/08/22
 * * Author:********
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     ********        2024/08/22      1.0            create
 ****************************************************************/
package com.filemanager.setting.ui.privacy.personal

import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityOptions
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.annotation.StringRes
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.WindowUtils
import com.filemanager.setting.R
import com.filemanager.setting.utils.FlexibleWindowUtil
import com.google.android.material.appbar.AppBarLayout
import com.oplus.flexiblewindow.FlexibleWindowManager

class PersonalInfoDetailActivity : BaseVMActivity() {

    private var pref: String = ""
    private var titleRes: Int = 0
    private lateinit var appBarLayout: AppBarLayout
    private lateinit var toolbar: COUIToolbar
    private lateinit var mContainerView: View

    override fun getLayoutResId(): Int {
        return R.layout.activity_personal_info_detail
    }


    override fun initView() {
        pref = IntentUtils.getString(intent, PREF) ?: ""
        titleRes = IntentUtils.getInt(intent, TITLE_RES, com.filemanager.common.R.string.personal_information_collection_list)
        Log.d(TAG, "initView $pref")
        registerVmChangedReceiver(null)
        appBarLayout = findViewById(R.id.appbar_layout)
        toolbar = findViewById(R.id.toolbar)
        mContainerView = findViewById(R.id.fragment_container)

        initToolbar()
        setBarBg()
    }

    override fun startObserve() {
    }

    override fun initData() {
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
    }

    private fun getViewTopPadding(): Int {
        return StatusBarUtil.getStatusBarHeight() + toolbar.height
    }

    @SuppressLint("PrivateResource")
    private fun initToolbar() {
        toolbar.title = resources.getString(titleRes)
        toolbar.isTitleCenterStyle = false
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        val res = application.resources//使用getter方法获取
        var appBarTopPadding = StatusBarUtil.getStatusBarHeight()
        var navigationIcon = R.drawable.coui_back_arrow
        if (FeatureCompat.isApplicableForFlexibleWindow &&
            WindowUtils.isMiddleAndLargeScreen(this) && !isInMultiWindowMode
        ) {
            navigationIcon = com.support.snackbar.R.drawable.coui_menu_ic_cancel
            appBarTopPadding = res.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_16dp)
            toolbar.post {
                mContainerView.setPadding(
                    mContainerView.paddingLeft,
                    toolbar.height + res.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_12dp),
                    mContainerView.paddingRight,
                    0
                )
                initFragment()
            }
        } else {
            toolbar.post {
                mContainerView.setPadding(
                    mContainerView.paddingLeft,
                    getViewTopPadding(),
                    mContainerView.paddingRight,
                    0
                )
                initFragment()
            }
        }
        toolbar.setNavigationIcon(navigationIcon)
        appBarLayout.setPadding(0, appBarTopPadding, 0, 0)
    }

    private fun setBarBg() {
        FlexibleWindowUtil.setBarBackgroundColor(toolbar, appBarLayout, window.decorView, resources.configuration, this)
    }

    private fun initFragment() {
        var fragment = supportFragmentManager.findFragmentByTag(TAG)
        if ((fragment == null) || (fragment !is PersonalInfoDetailFragment)) {
            fragment = PersonalInfoDetailFragment()
        }
        if (fragment.isStateSaved.not()) {
            fragment.arguments = Bundle().apply {
                putString(PREF, pref)
            }
            val ft = supportFragmentManager.beginTransaction()
            ft.replace(R.id.fragment_container, fragment, TAG)
            ft.show(fragment)
            ft.commitAllowingStateLoss()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        Log.d(TAG, "onUIConfigChanged")
        initToolbar()
        setBarBg()
    }

    companion object {
        private const val TAG = "PersonalInfoDetailActivity"
        const val PREF = "pref"
        const val TITLE_RES = "title_res"

        /**
         * 跳转到个人信息明示清单详情界面
         * @param activity
         * @param perf 表示跳转的哪一个详情
         * @param titleRes 标题
         */
        fun start(activity: Activity, perf: String, @StringRes titleRes: Int) {
            Log.d(TAG, "start PersonalInfoDetailActivity $perf")
            runCatching {
                val intent = Intent(activity, PersonalInfoDetailActivity::class.java)
                intent.setPackage(activity.packageName)
                intent.putExtra(PREF, perf)
                intent.putExtra(TITLE_RES, titleRes)
                if (FeatureCompat.isApplicableForFlexibleWindow) {
                    val exBundle = Bundle()
                    exBundle.putInt(
                        FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_POSITION,
                        FlexibleWindowUtil.checkOrientation(activity.window.decorView as ViewGroup)
                    )
                    exBundle.putBoolean(FlexibleWindowManager.KEY_FLEXIBLE_START_ACTIVITY, true)
                    exBundle.putBoolean(
                        FlexibleWindowManager.KEY_FLEXIBLE_ACTIVITY_DESCENDANT,
                        true
                    )

                    val options = ActivityOptions.makeBasic()
                    activity.startActivity(
                        intent,
                        FlexibleWindowManager.getInstance().setExtraBundle(options, exBundle)
                    )
                } else {
                    activity.startActivity(intent)
                }
            }.onFailure {
                Log.e(TAG, "start personal info detail activity error, ${it.message}")
            }
        }
    }
}