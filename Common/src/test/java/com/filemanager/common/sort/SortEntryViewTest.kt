/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SortEntryViewTest
 ** Description : Sort EntryV iew Unit test
 ** Version     : 1.0
 ** Date        : 2022/10/8
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2022/10/8      1.0        create
 ***********************************************************************/
package com.filemanager.common.sort

import android.content.Context
import android.content.res.Resources
import android.widget.ImageView
import android.widget.TextView
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SortEntryViewTest {

    lateinit var sortEntryView: SortEntryView
    lateinit var context: Context
    lateinit var resources: Resources

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk<Context>(relaxed = true)
        resources = mockk<Resources>(relaxed = true)
        mockkObject(MyApplication)
        every { MyApplication.sAppContext }.returns(context)
        sortEntryView = mockk<SortEntryView>(relaxed = true)
        every { sortEntryView.context }.returns(context)
        every { context.resources }.returns(resources)
        mockkStatic(COUIChangeTextUtil::class)
        justRun { COUIChangeTextUtil.adaptFontSize(any(), any()) }
    }

    @After
    fun tearDown() {
        unmockkObject(MyApplication)
    }

    @Test
    fun should_not_null_when_initView() {
        val sortImg = mockk<ImageView>(relaxed = true)
        every { sortEntryView.findViewById<TextView>(R.id.sort_entry_file_count_text) }.returns(mockk<TextView>())
        every { sortEntryView.findViewById<ImageView>(R.id.sort_entry_order_img) }.returns(sortImg)
        every { sortEntryView.findViewById<TextView>(R.id.sort_entry_order_text) }.returns(mockk<TextView>())
        every { sortEntryView.initView() }.answers { callOriginal() }

        sortEntryView.initView()
        Assert.assertNotNull(sortEntryView.sortOrderImg)
    }

    @Test
    fun should_when_initEvent() {
        val rotateView = mockk<ImageView>(relaxed = true)
        every { sortEntryView.sortOrderImg }.returns(rotateView)
        every { sortEntryView.initEvent() }.answers { callOriginal() }
        every { sortEntryView.sortOrderImg?.setOnClickListener(any()) }.answers { callOriginal() }
        every { sortEntryView.sortOrderImg?.performClick() }.answers { callOriginal() }
        every { sortEntryView.sortOrderImg?.hasOnClickListeners() }.returns(true)
        sortEntryView.initEvent()

        Assert.assertTrue(sortEntryView.sortOrderImg?.hasOnClickListeners() == true)
    }

    @Test
    fun should_when_setFileCount() {
        val textView = mockk<TextView>(relaxed = true)
        every { sortEntryView.countTv }.returns(textView)
        every { sortEntryView.setFileCount(any(), any()) }.answers { callOriginal() }
        every { textView.setText(any<CharSequence>()) }.answers { callOriginal() }
        every { textView.getText() }.answers { callOriginal() }
        every { resources.getQuantityString(R.plurals.search_result_count, any<Int>()) }.answers {
            val count = secondArg<Int>()
            String.format("共 %d 项", count)
        }
        sortEntryView.setFileCount(10)
        verify { textView.setText(any<CharSequence>()) }
    }

    @Test
    fun should_when_setDefaultOrder() {
        mockkStatic(SortModeUtils::class)
        every { sortEntryView.setDefaultOrder(any()) }.answers { callOriginal() }
        every { SortModeUtils.getSharedSortMode(any(), any()) }.returns(SortHelper.FILE_NAME_ORDER)
        sortEntryView.setDefaultOrder(SortModeUtils.RECORD_SORTMODE)
        verify { sortEntryView.setSortOrder(SortHelper.FILE_NAME_ORDER, any()) }
        unmockkStatic(SortModeUtils::class)
    }

    @Test
    fun should_when_setSortOrder() {
        val textView = mockk<TextView>()
        every { textView.setText(any<CharSequence>()) }.answers { callOriginal() }
        every { sortEntryView.sortOrderTv }.returns(textView)
        val imageView = mockk<ImageView>()
        every { imageView.setImageResource(any()) }.answers { callOriginal() }
        every { sortEntryView.sortOrderImg }.returns(imageView)
        every { sortEntryView.setSortOrder(any(), any()) }.answers { callOriginal() }
        every { sortEntryView.post(any()) }.answers {
            val run = firstArg<Runnable>()
            run.run()
            true
        }
        sortEntryView.setSortOrder(SortHelper.RECYCLE_BIN_DEFAULT_ORDER, true)

        verify { sortEntryView.getSortType(any()) }
        verify { sortEntryView.getOrderImg(any()) }
    }

    @Test
    fun should_return_String_when_getSortType() {
        every { context.getString(R.string.sort_by_name) }.returns("名称")
        every { context.getString(R.string.sort_by_type) }.returns("类型")
        every { context.getString(R.string.sort_by_size) }.returns("大小")
        every { context.getString(R.string.modify_time) }.returns("修改时间")
        every { context.getString(R.string.sort_by_remain_time) }.returns("剩余时间")
        every { sortEntryView.getSortType(any()) }.answers { callOriginal() }

        Assert.assertEquals("名称", sortEntryView.getSortType(0))
        Assert.assertEquals("名称", sortEntryView.getSortType(SortHelper.FILE_NAME_ORDER))
        Assert.assertEquals("类型", sortEntryView.getSortType(SortHelper.FILE_TYPE_ORDER))
        Assert.assertEquals("大小", sortEntryView.getSortType(SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER))
        Assert.assertEquals("修改时间", sortEntryView.getSortType(SortHelper.FILE_TIME_REVERSE_ORDER))
        Assert.assertEquals("剩余时间", sortEntryView.getSortType(SortHelper.FILE_TIME_DELETE_ORDER))
    }

    @Test
    fun should_return_Float_when_getOrderImgRotation() {
        every { sortEntryView.getOrderImg(any()) }.answers { callOriginal() }
        Assert.assertEquals(sortEntryView.getOrderImg(true), R.drawable.icon_sort_desc)
        Assert.assertEquals(sortEntryView.getOrderImg(false), R.drawable.icon_sort_asc)
    }

    @Test
    fun should_notNull_when_setClickSortListener() {
        every { sortEntryView.setClickSortListener(any()) }.answers { callOriginal() }
        every { sortEntryView.clickListener }.answers { callOriginal() }

        sortEntryView.setClickSortListener { }
        Assert.assertNotNull(sortEntryView.clickListener)
    }
}