/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : WeworkIntentProcess.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.process

import android.content.ComponentName
import android.content.Intent
import android.os.BadParcelableException
import android.os.Bundle
import android.util.Log
import com.filemanager.common.constants.ThirdAppConstants.WEWORK_PACKAGE
import java.lang.ClassCastException

class WeworkIntentProcess : DefaultIntentProcess() {

    companion object {
        const val TAG = "WeworkIntentProcess"
    }



    override fun getKeyDataBeanFromIntent(inputIntent: Intent): IViewExactProcess.KeyDataBean {
        val keyInfo = getKeyInfoFromIntent(inputIntent)
        val uid = getUidFromIntent(keyInfo)
        val fileName = getFileNameFromIntent(keyInfo)
        val result = IViewExactProcess.KeyDataBean()
        result.uid = uid
        result.fileName = fileName
        Log.i(TAG, "getKeyDataBeanFromIntent intent $inputIntent, result $result")
        return IViewExactProcess.KeyDataBean()
    }


    private fun getKeyInfoFromIntent(inputIntent: Intent): KeyInfoFromIntent {
        val intentData = inputIntent.data
        val extras = inputIntent.extras
        val sb = StringBuilder()
        val result = KeyInfoFromIntent()
        result.intentComponent = inputIntent.component ?: ComponentName(WEWORK_PACKAGE, "")
        if (extras != null) {
            val extrasKeys = extras.keySet()
            Log.i(TAG, "getUidFromIntent start extra keys ${extrasKeys.toList()}")
            if (extrasKeys != null) {
                for (key in extrasKeys) {
                    try {
                        extractedOtherField(key, extras, sb, result)
                    } catch (e: BadParcelableException) {
                        Log.e(TAG, "getEntityFromIntent error", e)
                    } catch (e: ClassCastException) {
                        Log.e(TAG, "getEntityFromIntent error", e)
                    }
                }
            }
        }
        Log.i(TAG, "getUidFromIntent end itent: $inputIntent intentData $intentData, extras: $extras, sb $sb, result: $result")
        return result
    }

    private fun extractedOtherField(
        key: String?,
        extras: Bundle,
        sb: StringBuilder,
        result: KeyInfoFromIntent
    ) {
        val fileName: String?
        val filePath: String?
        val fileExt: String?
        if (WechatIntentProcess.EXTRA_FILE_NAME.contentEquals(key, true)) {
            fileName = extras.getString(key)
            sb.append("key $key, value $fileName\n")
            result.fileName = fileName ?: ""
        } else if (WechatIntentProcess.EXTRA_FILE_PATH.contentEquals(key, true)) {
            filePath = extras.getString(key)
            sb.append("key $key, value $filePath\n")
            result.filePath = filePath ?: ""
        } else if (WechatIntentProcess.EXTRA_FILE_EXT.contentEquals(key, true)) {
            fileExt = extras.getString(key)
            sb.append("key $key, value ${fileExt}\n")
            result.fileExt = fileExt ?: ""
        }
    }

    private fun getUidFromIntent(keyInfo: KeyInfoFromIntent): String {
        val result = keyInfo.intentComponent.toShortString() + "_filePath:" + keyInfo.filePath
        Log.i(TAG, "getUidFromIntent no real file, no cloud File, generate uuid $result as uid")
        return result
    }


    private fun getFileNameFromIntent(keyInfo: KeyInfoFromIntent): String {
        var result = keyInfo.fileName
        if (result.isEmpty()) {
            val filePath = keyInfo.filePath
            val fileExt = keyInfo.fileExt
            if (filePath.isNotEmpty() && fileExt.isNotEmpty()) {
                val index = filePath.lastIndexOf("/")
                if (index != -1 && index < filePath.length - 1) {
                    val tmp = filePath.substring(index + 1)
                    if (tmp.contains(fileExt, ignoreCase = true)) {
                        result = getFileNameExceptPresuffix(tmp)
                        Log.i(TAG, "getFileNameFromIntent substring index $index, tmp $tmp")
                    }
                }
            }
        }
        Log.i(TAG, "getFileNameFromIntent $result")
        return result
    }


    private fun getFileNameExceptPresuffix(input: String): String {
        var result: String = input
        runCatching {
            val regex = Regex("(\\d*_*)((\\S|\\s)*\\.\\w*)")
            val matchResult = regex.find(input)
            if (matchResult != null) {
                val suffixString = matchResult.groups[2]?.value
                if (suffixString != null) {
                    result = suffixString
                }
            }
        }.onFailure {
            Log.e(TAG, "getFileNameExceptPresuffix ", it)
        }
        Log.i(TAG, "getFileNameExceptPresuffix input $input, output $result")
        return result
    }


    data class KeyInfoFromIntent(
        var intentComponent: ComponentName = ComponentName("", ""),
        var fileName: String = "",
        var fileExt: String = "",
        var filePath: String = ""
    )
}