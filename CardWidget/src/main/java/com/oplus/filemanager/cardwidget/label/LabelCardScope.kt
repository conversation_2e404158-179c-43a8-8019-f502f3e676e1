/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : labelCardScope
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/55
 ** Author      : wanghonglei
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  wanghonglei     2023/11/55       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import java.io.Closeable
import kotlin.coroutines.CoroutineContext

val labelCardScope: CoroutineScope = CloseableCoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)

internal class CloseableCoroutineScope(context: CoroutineContext) : Closeable, CoroutineScope {
    override val coroutineContext: CoroutineContext = context

    override fun close() {
        coroutineContext.cancel()
    }
}