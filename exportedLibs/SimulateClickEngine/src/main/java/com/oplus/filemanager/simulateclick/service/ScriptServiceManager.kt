/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScriptServiceManager.kt
 * Description:
 *     The manager to Schedule SimulateClickService and execute script.
 *
 * Version: 1.0
 * Date: 2024-05-27
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-05-27   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.service

import android.content.ComponentName
import android.content.Context
import androidx.annotation.VisibleForTesting
import com.oplus.filemanager.simulateclick.accessibility.AccessibilitySettings
import com.oplus.filemanager.simulateclick.engine.utils.Log
import com.oplus.filemanager.simulateclick.request.ScriptExecRequest

internal object ScriptServiceManager {

    private const val TAG = "ScriptServiceManager"

    @VisibleForTesting
    @Volatile
    internal var runningService: SimulateClickService? = null

    @VisibleForTesting
    @Volatile
    internal var pendingRequest: ScriptExecRequest? = null

    @JvmStatic
    fun startScriptService(context: Context) {
        Log.d(TAG, "startScriptService")
        switchScriptService(context, true)
    }

    @JvmStatic
    fun stopScriptService(context: Context) {
        Log.d(TAG, "stopScriptService")
        switchScriptService(context, false)
    }

    @JvmStatic
    private fun switchScriptService(context: Context, isOpen: Boolean) {
        val scriptComponent = ComponentName(context, SimulateClickService::class.java)
        AccessibilitySettings.setAccessibilityServiceState(context, scriptComponent, isOpen)
    }

    @JvmStatic
    @Synchronized
    fun onServiceConnected(service: SimulateClickService) {
        runningService = service
        pendingRequest?.let {
            pendingRequest = null
            service.executeScript(it)
        }
    }

    @JvmStatic
    @Synchronized
    fun onServiceDisconnected(service: SimulateClickService) {
        if (runningService == service) {
            runningService = null
        }
    }

    @JvmStatic
    @Synchronized
    fun executeScript(context: Context, request: ScriptExecRequest) {
        val scriptService = runningService
        if (scriptService == null) {
            Log.d(TAG, "executeScript: service is not running, pend request=$request")
            pendingRequest = request
            startScriptService(context)
        } else {
            Log.d(TAG, "executeScript: service is running, execute request=$request")
            scriptService.executeScript(request)
        }
    }
}