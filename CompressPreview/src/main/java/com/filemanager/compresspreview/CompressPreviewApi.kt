/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: CompressPreviewApi.kt
 ** Description:  CompressPreviewApi
 ** Version: 1.0
 ** Date: 2021/5/11
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.compresspreview

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.fragment.app.Fragment
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.filemanager.compresspreview.ui.CompressPreviewActivity
import com.filemanager.compresspreview.ui.CompressPreviewFragment
import com.oplus.filemanager.interfaze.compresspreview.ICompressPreview

object CompressPreviewApi : ICompressPreview {

    private const val TAG = "CompressPreviewApi"

    override fun startCompressPreviewActivity(activity: Activity, path: String?) {
        val intent = Intent(activity, CompressPreviewActivity::class.java)
        intent.putExtra(KtConstants.P_CURRENT_PATH, path)
        activity.startActivity(intent)
    }

    override fun startCompressPreviewActivity(activity: Activity, file: BaseFileBean, flag: Int) {
        val intent = Intent("oppo.intent.action.ACTION_COMPRESS_PREVIEW")
        intent.addCategory(Intent.CATEGORY_DEFAULT)
        val uri: Uri? = UriHelper.getFileUri(file, intent, fileType = file.mLocalType)
        intent.setDataAndType(uri, "application/zip")
        intent.addFlags(flag)
        try {
            activity.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            Log.e(TAG, "startCompressPreviewActivity error", e)
        }
    }

    override fun getFragment(activity: Activity): Fragment {
        Log.d(TAG, "getFragment")
        return CompressPreviewFragment()
    }

    override fun onResumeLoadData(fragment: Fragment) {
        Log.d(TAG, "onResumeLoadData")
        if (fragment is CompressPreviewFragment) {
            fragment.onResumeLoadData()
        }
    }

    override fun onMenuItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onMenuItemSelected")
        if (fragment is CompressPreviewFragment) {
            return fragment.onMenuItemSelected(item)
        }
        return false
    }

    override fun pressBack(fragment: Fragment): Boolean {
        Log.d(TAG, "pressBack")
        if (fragment is CompressPreviewFragment) {
            return fragment.pressBack()
        }
        return false
    }

    override fun onNavigationItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onNavigationItemSelected")
        if (fragment is CompressPreviewFragment) {
            return fragment.onNavigationItemSelected(item)
        }
        return false
    }

    override fun fromSelectPathResult(fragment: Fragment, requestCode: Int, paths: List<String>?) {
        Log.d(TAG, "fromSelectPathResult")
        if (fragment is CompressPreviewFragment) {
            fragment.fromSelectPathResult(requestCode, paths)
        }
    }

    override fun backToTop(fragment: Fragment) {
        Log.d(TAG, "backToTop")
        if (fragment is CompressPreviewFragment) {
            fragment.backToTop()
        }
    }

    override fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater) {
        //do nothing
    }

    override fun setToolbarAndTabListener(
        fragment: Fragment,
        toolbar: COUIToolbar?,
        title: String,
        tabListener: TabActivityListener<MediaFileWrapper>
    ) {
        //do nothing
    }

    override fun updateLabels(fragment: Fragment) {
        //do nothing
    }

    override fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        //do nothing
    }

    override fun permissionSuccess(fragment: Fragment) {
        //do nothing
    }

    override fun setCurrentFilePath(fragment: Fragment, path: String?) {
        //do nothing
    }

    override fun getCurrentPath(fragment: Fragment): String {
        return ""
    }

    override fun exitSelectionMode(fragment: Fragment) {
        if (fragment is CompressPreviewFragment) {
            fragment.exitSelectionMode()
        }
    }

    override fun onSideNavigationClicked(fragment: Fragment, isOpen: Boolean): Boolean {
        if (fragment is PreviewCombineFragment) {
            return fragment.onSideNavigationClicked(isOpen)
        }
        return false
    }
}