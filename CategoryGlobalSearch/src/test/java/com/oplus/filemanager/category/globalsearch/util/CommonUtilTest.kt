/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CommonUtilTest
 ** Description : CommonUtilTest Unit Test
 ** Version     : 1.0
 ** Date        : 2023/2/21
 ** Author      : wanghonglei
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  wanghonglei     2023/2/21      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.util

import android.content.Context
import android.content.res.Resources
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import androidx.core.content.res.ResourcesCompat
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.KtUtils
import com.oplus.filemanager.category.globalsearch.R
import com.oplus.filemanager.category.globalsearch.ui.CommonUtil
import com.oplus.filemanager.dfm.DFMManager
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class CommonUtilTest {

    @Before
    fun setup() {
        mockkObject(MyApplication)
        val context: Context = mockk(relaxed = true)
        every { MyApplication.appContext }.returns(context)
        val resources = mockk<Resources>(relaxed = true)
        every { context.resources }.returns(resources)
        every { resources.getString(any()) } answers {
            val firstArg = arg<Int>(0)
            val stringReusut = if (firstArg == com.filemanager.common.R.string.storage_external) {
                "sd 卡"
            } else if (firstArg == com.filemanager.common.R.string.storage_otg) {
                "otg"
            } else {
                ""
            }
            stringReusut
        }
        val resourcesCompat = mockkStatic(ResourcesCompat::class)
        every { context.resources }.returns(resources)
        every { ResourcesCompat.getDrawable(any(), any(), null) } answers {
            val secondIntArg = arg<Int>(1)
            val drawableResult =
                if (secondIntArg == R.drawable.sdcard_icon || secondIntArg == R.drawable.otg_icon ||
                    secondIntArg == R.drawable.phone_icon || secondIntArg == R.drawable.pad_icon
                ) {
                    ColorDrawable()
                } else {
                    null
                }
            drawableResult
        }

        mockkStatic(FeatureCompat::class)
        mockkStatic(DFMManager::class)
        every { DFMManager.getCurrentStatus() } returns DFMManager.STATUS_META_DATA_OK
        every { DFMManager.openP2pConnectAndWaitDFSReady() } returns true
        every { DFMManager.checkStateSearchAvalable() } returns true
        every { DFMManager.getDFSDeviceName() } returns "OPPOPAD"
        every { DFMManager.getDFSDevice() } answers {
            val result = Bundle()
            result.putInt(KtConstants.DFM_DEVICE_TYPE, KtConstants.DFM_PHONE_TYPE)
            result
        }
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getInternalSdPath(context) } returns "/storage/emulated/0"
        every { VolumeEnvironment.getExternalSdPath(context) } returns "/storage/emulated/1"
        every { VolumeEnvironment.isSingleSdcard(context) } returns true
        mockkStatic(KtUtils::class)
    }

    @After
    fun after() {
        unmockkObject(MyApplication)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(DFMManager::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(KtUtils::class)
    }

    @Test
    fun `should internal case`() {
        // Given
        val baseFileBean = BaseFileBean()
        baseFileBean.mData = "/storage/emulated/0/Download/1.txt"
        val isDfm = CommonUtil.checkFileBeanIsDFM(MyApplication.appContext, baseFileBean)
        Assert.assertEquals(false, isDfm)
        val beanType = CommonUtil.checkAndGetTypeForFileBean(MyApplication.appContext, baseFileBean)
        Assert.assertEquals(KtUtils.STORAGE_INTERNAL, beanType)
        val sourceName =
            CommonUtil.checkAndGetTypeStringForFileBean(MyApplication.appContext, baseFileBean)
        Assert.assertEquals(true, sourceName.isEmpty())
        val sourceIcon = CommonUtil.getSourceIconForFileBean(MyApplication.appContext, baseFileBean)
        Assert.assertNull(sourceIcon)
        val sourceString =
            CommonUtil.getSourceIconSpanString(MyApplication.appContext, baseFileBean)
        Assert.assertEquals(true, sourceString.isEmpty())
        val sourceString2 =
            CommonUtil.getSourceIconSpanString(MyApplication.appContext, baseFileBean, "")
        Assert.assertEquals(true, sourceString2.isEmpty())
    }

    @Test
    fun `should sdcard case`() {
        // Given
        val baseFileBean = BaseFileBean()
        baseFileBean.mData = "/storage/emulated/1/Download/1.txt"
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getInternalSdPath(any()) } returns "/storage/emulated/0"
        every { VolumeEnvironment.getExternalSdPath(any()) } returns "/storage/emulated/1"
        every { VolumeEnvironment.isSingleSdcard(any()) } returns false
        mockkStatic(DFMManager::class)
        every { DFMManager.getDFSDevice() } answers {
            val result = Bundle()
            result.putInt(KtConstants.DFM_DEVICE_TYPE, KtConstants.DFM_PHONE_TYPE)
            result
        }

        val isDfm = CommonUtil.checkFileBeanIsDFM(MyApplication.appContext, baseFileBean)
        Assert.assertEquals(false, isDfm)
        val beanType = CommonUtil.checkAndGetTypeForFileBean(MyApplication.appContext, baseFileBean)
        Assert.assertEquals(KtUtils.STORAGE_EXTERNAL, beanType)
        val sourceName =
            CommonUtil.checkAndGetTypeStringForFileBean(MyApplication.appContext, baseFileBean)
        Assert.assertEquals(false, sourceName.isEmpty())
        val sourceIcon = CommonUtil.getSourceIconForFileBean(MyApplication.appContext, baseFileBean)
        Assert.assertNotNull(sourceIcon)
        val sourceString =
            CommonUtil.getSourceIconSpanString(MyApplication.appContext, baseFileBean)
        Assert.assertEquals(true, sourceString.isEmpty())
        val sourceString2 =
            CommonUtil.getSourceIconSpanString(MyApplication.appContext, baseFileBean, "")
        Assert.assertEquals(true, sourceString2.isEmpty())
    }


    @Test
    fun `should dfs case`() {
        // Given
        val baseFileBean = BaseFileBean()
        baseFileBean.mData = "/mnt/dfs/1/Download/1.txt"

        val isDfm = CommonUtil.checkFileBeanIsDFM(MyApplication.appContext, baseFileBean)
        Assert.assertEquals(true, isDfm)
        val beanType = CommonUtil.checkAndGetTypeForFileBean(MyApplication.appContext, baseFileBean)
        Assert.assertEquals(KtUtils.STORAGE_DMF, beanType)
        val sourceName =
            CommonUtil.checkAndGetTypeStringForFileBean(MyApplication.appContext, baseFileBean)
        Assert.assertEquals(false, sourceName.isEmpty())
        val sourceIcon = CommonUtil.getSourceIconForFileBean(MyApplication.appContext, baseFileBean)
        Assert.assertNull(sourceIcon)
        val sourceString =
            CommonUtil.getSourceIconSpanString(MyApplication.appContext, baseFileBean)
        Assert.assertEquals(true, sourceString.isEmpty())
        val sourceString2 =
            CommonUtil.getSourceIconSpanString(MyApplication.appContext, baseFileBean, "")
        Assert.assertEquals(true, sourceString2.isEmpty())
    }
}