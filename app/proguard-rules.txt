# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html

# Optimizations: If you don't want to optimize, use the
# proguard-android.txt configuration file instead of this one, which
# turns off the optimization flags.  Adding optimization introduces
# certain risks, since for example not all optimizations performed by
# ProGuard works on all versions of Dalvik.  The following flags turn
# off various optimizations known to have issues, but the list may not
# be complete or up to date. (The "arithmetic" optimization can be
# used if you are only targeting Android 2.0 or later.)  Make sure you
# test thoroughly if you go this route.
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify
-ignorewarnings

# The remainder of this file is identical to the non-optimized version
# of the Proguard configuration file (except that the other file has
# flags to turn off optimization).

-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose
-renamesourcefileattribute SourceFile 
-keepattributes SourceFile,LineNumberTable

-keepattributes *Annotation*
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.webkit.WebView
-keep public class * extends android.app.Service
-keep public class * extends android.app.Fragment
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference

# For native methods, see http://proguard.sourceforge.net/manual/examples.html#native
-keepclasseswithmembernames class * {
    native <methods>;
}

# keep setters in Views so that animations can still work.
# see http://proguard.sourceforge.net/manual/examples.html#beans
-keepclassmembers public class * extends android.view.View {
   void set*(***);
   *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

# For enumeration classes, see http://proguard.sourceforge.net/manual/examples.html#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator CREATOR;
}

-keep public class * implements java.io.Serializable {*;}
-keepclassmembers class * implements java.io.Serializable {
  private static final long serialVersionUID;
  private static final java.io.ObjectStreamField[] serialPersistentFields;
  private void writeObject(java.io.ObjectOutputStream);
  private void readObject(java.io.ObjectInputStream);
  java.lang.Object writeReplace();
  java.lang.Object readResolve();
}

-keepclassmembers class **.R$* {
    public static <fields>;
}

# The support library contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version.  We know about them, and they are safe.

-dontwarn android.support.**
-keep public class android.support.** { *; }

-dontwarn org.apache.**
-keep public class org.apache.** { *; }

-dontwarn com.color.**
-keep public class com.color.** { *; }

-dontwarn com.coloros.commons.**
-keep public class com.coloros.commons.** { *; }

-dontwarn com.coloros.compatibility.**
-keep public class com.coloros.compatibility.** { *; }

-dontwarn com.coloros.fingerprint.service.**
-keep public class com.coloros.fingerprint.service.** { *; }

-dontwarn gnu.crypto.**
-keep public class gnu.crypto.** { *; }

-dontwarn com.google.**
-keep public class com.google.common.** { *; }

-dontwarn com.nearme.**
-keep public class com.nearme.** { *; }

-dontwarn com.oppo.**
-keep public class com.oppo.** { *; }

-dontwarn com.baidu.**
-keep public class com.baidu.** { *; }

-dontwarn de.innosystec.unrar.**
-keep public class de.innosystec.unrar.** { *; }

-dontwarn com.github.junrar.**
-keep public class com.github.junrar.** { *; }

-dontwarn net.lingala.zip4j.**
-keep public class net.lingala.zip4j.** { *; }

-dontwarn com.nostra13.universalimageloader.**
-keep public class com.nostra13.universalimageloader.** { *; }

-dontwarn de.javagl.jgltf.**
-keep public class de.javagl.jgltf.** { *; }
-keep class com.fasterxml.jackson.**{*;}
-dontwarn com.fasterxml.jackson.**

-dontwarn com.oppo.os.**
-keep class com.oppo.os.LinearmotorVibrator{*;}
-keep class com.oppo.os.WaveformEffect{*;}
-keep class com.oppo.os.WaveformEffect$Builder{*;}

-keep class com.color.compat.**{*;}

#for openId Sdk,don't delete
-dontwarn android.content.pm.**
-keep class android.support.annotation.Keep
-keep @android.support.annotation.Keep class * {*;}
-keep class android.content.pm.** { *; }
#openId Sdk end

#for Monkey
-keep class com.squareup.haha.** { *; }
-keep class com.oppo.perftest.** { *; }
-keep class com.squareup.leakcanary.** { *; }
#for Monkey end

-keepclassmembers public class com.coloros.filemanager.view.main.view.*{
    void set*(***);
    *** get*();
}

#Glide
-dontwarn com.bumptech.glide.**
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public class * implements com.bumptech.glide.module.AppGlideModule
-keep public class * implements com.bumptech.glide.module.LibraryGlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}

#heytap-api
-dontwarn com.android.mkstubs.**
-dontwarn com.color.inner.**
-dontwarn android.content.pm.**
-dontwarn android.hardware.fingerprint.**
-keep class mirror.android.** {*;}
-keep class com.heytap.reflect.** {*;}
-keep class com.heytap.epona.ExceptionInfo {*;}
-keepclassmembers class com.heytap.compat.**.*Native {
    static com.heytap.reflect.Ref* *;
}
-keepclassmembers class com.heytap.compat.**.*Native$* {
    static com.heytap.reflect.Ref* *;
}

-keep class com.coui.appcompat.searchview.COUISearchBar{ *; }
-keep class com.coui.appcompat.searchview.COUISearchView{ *; }
-keep class com.coui.appcompat.toolbar.COUIActionMenuItemView{ *; }
-keep class android.os.DynamicEffect { *; }
-keep class android.os.HapticPlayer { *; }

#feedback-api
-keep public class com.customer.feedback.sdk.**{*;}

#CloudConfig
-keep @androidx.anntotation.Keep class **
-keep @com.heytap.baselib.database.annotation.DbEntity class * {*;}
-keepclassmembers class * {
    @com.heytap.nearx.cloudconfig.anotation.FieldIndex  *;
}

#api-adapter
-dontwarn com.color.inner.**
-dontwarn com.oplus.inner.**
-dontwarn android.content.pm.**
-dontwarn android.hardware.fingerprint.**
-dontwarn com.oplus.compat.**
-dontwarn com.oplus.epona.internal.LoggerSnapshot
-dontwarn com.oplus.epona.internal.LoggerSnapShotOplusCompat
-dontwarn android.telephony.**

-keep class mirror.android.** {*;}
-keep class com.oplus.utils.reflect.** {*;}
-keep class com.oplus.epona.Request {*;}
-keep class com.oplus.epona.ExceptionInfo {*;}
-keep class com.oplus.epona.provider.** {*;}
-keep class com.oplus.epona.Call$Callback {*;}
-keep class com.oplus.epona.ParcelableException {*;}

-keep class com.heytap.epona.ExceptionInfo {*;}
-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}
-keep class * implements androidx.lifecycle.LifecycleObserver {
}
-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}
-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

-keepclassmembers class com.oplus.compat.**.*Native {
 static com.oplus.utils.reflect.Ref* *;
}
-keepclassmembers class com.oplus.compat.**.*Native$* {
 static com.oplus.utils.reflect.Ref* *;
}

-keepclassmembers class com.oplus.compat.**.*Compat {
 static com.oplus.utils.reflect.Ref* *;
}
-keepclassmembers class com.oplus.compat.**.*Compat$* {
 static com.oplus.utils.reflect.Ref* *;
}
-keep class com.oplus.filemanager.category.apk.provider.** { *; }
-keep class com.coloros.filemanager.BuildConfig { *; }
-keep class com.oneplus.filemanager.BuildConfig { *; }

#StdId SDK
-dontwarn android.content.pm.**
-keep class android.support.annotation.Keep
-keep @android.support.annotation.Keep class * {*;}
-keep class android.content.pm.** { *; }
-keep class com.oplus.zoomwindow.**{*;}
-dontwarn com.oplus.zoomwindow.**
-keep class com.filemanager.common.helper.uiconfig.UIConfigMonitor$ZoomWindowObserver{*;}
-keep class * implements com.oplus.filemanager.provider.content.BaseProviderContent {
    <init>(...);
}

-keep class com.p7zip.util.UnCompress7ZipUtils { *; }


#feedback begin
-keep public class com.customer.feedback.sdk.**{*;}
-keep @com.oplus.baselib.database.annotation.DbEntity class * {*;}
-keepclassmembers class * {
    @com.oplus.nearx.cloudconfig.anotation.FieldIndex *;
}
-keep @androidx.anntotation.Keep class **
-keep public class com.customer.feedback.sdk.model.RequestData { *; }
-keep public class com.customer.feedback.sdk.model.RequestData$* { *;}
#feedback end


-dontwarn com.google.**
-keep class com.google.** {
    *;
}

-keep class com.oplus.questionnaire.data.** {
    *;
}

# Retrofit2
-dontwarn okio.**
-dontwarn retrofit2.Platform$Java8

#--------------------------------融合桌面SDK start----------------------------------------
-keep class com.oplus.cardwidget.proto.** {*;}
-keep class com.google.protobuf.** {*;}
#--------------------------------融合桌面SDK end----------------------------------------

# Serializer
-if @kotlinx.serialization.Serializable class **
-keepclassmembers class <1> {
    static <1>$Companion Companion;
}

# Keep `serializer()` on companion objects (both default and named) of serializable classes.
-if @kotlinx.serialization.Serializable class ** {
    static **$* *;
}
-keepclassmembers class <2>$<3> {
    kotlinx.serialization.KSerializer serializer(...);
}

# Keep `INSTANCE.serializer()` of serializable objects.
-if @kotlinx.serialization.Serializable class ** {
    public static ** INSTANCE;
}
-keepclassmembers class <1> {
    public static <1> INSTANCE;
    kotlinx.serialization.KSerializer serializer(...);
}

# @Serializable and @Polymorphic are used at runtime for polymorphic serialization.
-keepattributes RuntimeVisibleAnnotations,AnnotationDefault
#Json bean
-keep class com.oplus.fileservice.bean.WebFileBean {*;}

#冻结保活接口
-keep class com.oplus.app.** { *; }
-keep class com.oplus.fileservice.utils.HansFreezeManager$ProtectConnect{*;}
#冻结保活接口
-keep class com.oplus.app.** { *; }
-keep class com.oplus.openanyfile.util.HansFreezeManager$ProtectConnect{*;}

-keep class com.android.documentsui.DocumentUtil{ *; }
-keep class com.android.documentsui.base.DocumentInfo{ *; }
-keep class com.android.documentsui.base.DocumentStack{ *; }
-keep class com.android.documentsui.base.Durable{ *; }
-keep class com.android.documentsui.base.DurableUtils{ *; }
-keep class com.android.documentsui.base.RootInfo{ *; }
-keep class com.android.documentsui.base.UserId{ *; }

#--------------------------------backuprestore SDK start----------------------------------------
-keep class com.heytap.backup.sdk.** { *; }
-keep class com.oplus.backup.sdk.** { *; }
-keep class com.oplus.filemanager.backuprestore.** { *; }
#--------------------------------backuprestore SDK end----------------------------------------

# format convert sdk start
-keep class andes.oplus.documentsreaderkit.util.http.response.** { *; }
-keep class andes.oplus.documentsreaderkit.pojo.** { *; }

#Gson
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Account SDK
-keep class com.nearme.aidl.** { *; }
-keep class * implements com.platform.usercenter.annotation.NoProguard {
  public *;
}
-keep class com.heytap.usercenter.accountsdk.BuildConfig { *; }
-keep class com.platform.usercenter.annotation.Keep
-keep @com.platform.usercenter.annotation.Keep class * {*;}
-keep class com.heytap.uccompiler.annotation.interceptor.** { *; }
-keep class com.accountbase.** { *; }
-keep class com.platform.usercenter.basic.annotation.Keep
-keep @com.platform.usercenter.basic.annotation.Keep class * {*;}

#stdId
-dontwarn android.content.pm.**
-keep class android.support.annotation.Keep
-keep @android.support.annotation.Keep class * {*;}
-keep class android.content.pm.**{*;}
-keep class com.oplus.onet.link.**{*;}

#dmp sdk
# 保留加了Keep注解的class
-keep @androidx.annotation.Keep class ** { *; }
-keep class com.oplus.dmp.sdk.analyzer.** { *; }
-keep class com.oplus.dmp.sdk.index.** { *; }
-keep class com.oplus.dmp.sdk.search.** { *; }
-keep class com.oplus.dmp.sdk.common.dict.** { *; }
-keep class com.oplus.dmp.sdk.common.exception.** { *; }
-keep class com.oplus.dmp.sdk.common.log.** { *; }
-keep class com.oplus.dmp.sdk.common.thread.** { *; }
-keep class com.oplus.dmp.sdk.querypreprocess.** { *; }
-keep class com.oplus.dmp.sdk.version.VersionProtocol { *; }
-keep class com.oplus.dmp.sdk.GlobalContext { *; }

-keep class com.filemanager.common.base.HighLightEntity { *; }
-keep class com.filemanager.common.base.ContentEntity { *; }
-keep class com.oplus.filemanager.category.globalsearch.ui.loader.GlobalSearchDMPLoader { *; }

# kolin
-keep class org.koin.** {*;}

# 配置项序列化dataBean，在api接口中的入参和出参，需要keep
-keep class com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam { *; }
-keep class com.coloros.filemanager.appswitch.bean.AppSwitchConfig { *; }
-keep class com.filemanager.common.bean.SearchIndexBean { *; }
-keep class com.filemanager.common.bean.SearchResultBean { *; }
-keep class com.filemanager.common.bean.SearchDataBean { *; }
-keep class com.filemanager.common.bean.HighLightItem { *; }
-keep class com.filemanager.common.bean.HighLightItems { *; }
# 图文提取框架keep以及内部调用类keep
-keep class com.oplus.viewextract.** {*;}
-keep class com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper { *; }
# 反编译钉钉intent中带入的类
-keep class com.alibaba.alimei.cspace.model.** { *; }
-keep class com.alibaba.alimei.framework.model.** { *; }
# 反编译飞书intent中带入的类
-keep class com.bytedance.ee.bear.contract.drive.sdk.entity.menu.** { *; }
-keep class com.bytedance.ee.bear.contract.drive.sdk.entity.open.** { *; }
-keep class com.bytedance.ee.bear.domain.** { *; }
# 反编译QQ中intent中带入的类
-keep class com.tencent.mobileqq.filemanager.data.** { *; }
# 这里加这个是为了解决之前release包中在ExternalLiveData中报错callMethodPutIfAbsent中报错找不到putIfAbsent方法导致LiteEventBus失效的问题
-keep class androidx.lifecycle.ExternalLiveData { *;}
-keep class androidx.arch.core.internal.SafeIterableMap { *; }

# TBL Player
-keep class com.oplus.tblplayer.android.TBLAndroidPlayer { *;}
#-keep class com.oplus.tblplayer.** { *;}

# -------- Start of extra rules for R8 full mode --------
# gson sdk
-if class *
-keepclasseswithmembers,allowobfuscation class <1> {
  @com.google.gson.annotations.SerializedName <fields>;
}

-if class * {
  @com.google.gson.annotations.SerializedName <fields>;
}
-keepclassmembers,allowobfuscation,allowoptimization class <1> {
  <init>();
}

-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.Expose <fields>;
  @com.google.gson.annotations.JsonAdapter <fields>;
  @com.google.gson.annotations.Since <fields>;
  @com.google.gson.annotations.Until <fields>;
}

# retrofit sdk
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface * extends <1>

-if interface * { @retrofit2.http.* public *** *(...); }
-keep,allowoptimization,allowshrinking,allowobfuscation class <3>

-keep,allowobfuscation,allowshrinking class retrofit2.Response

# kotlin coroutines
-keepattributes Signature
-keep class kotlin.coroutines.Continuation

# androidx.room sdk
-keep,allowoptimization,allowobfuscation @androidx.room.** class ** { *; }
-keepclassmembers,allowobfuscation class * {
    @androidx.room.** <fields>;
    @androidx.room.** <methods>;
}

# glide sdk
-keep,allowoptimization,allowobfuscation @com.bumptech.glide.annotation.** class ** { *; }
-keepclassmembers,allowobfuscation class * {
    @com.bumptech.glide.annotation.** <fields>;
    @com.bumptech.glide.annotation.** <methods>;
}
-keep,allowoptimization,allowobfuscation @com.bumptech.glide.annotation.compiler.** class ** { *; }
-keepclassmembers,allowobfuscation class * {
    @com.bumptech.glide.annotation.compiler.** <fields>;
    @com.bumptech.glide.annotation.compiler.** <methods>;
}

# ostitch sdk
-keep,allowoptimization,allowobfuscation @com.inno.ostitch.annotation.Component class *
-keepclassmembers,allowoptimization,allowobfuscation class * {
    @com.inno.ostitch.annotation.Action <fields>;
    @com.inno.ostitch.annotation.Action <methods>;
}

# cloudconfig sdk
-keep,allowobfuscation,allowoptimization class com.heytap.nearx.cloudconfig.** {*;}
-keep,allowobfuscation,allowoptimization class com.oplus.nearx.cloudconfig.** {*;}
-keep @com.oplus.nearx.cloudconfig.anotation.Config interface ** { *; }
-keep class com.oplus.nearx.cloudconfig.observable.Observable { *; }

# account sdk
-keep class com.heytap.usercenter.**{*;}
-keep class com.platform.usercenter.**{*;}

# ptc sdk
-keep class com.oplus.pantaconnect.sdk.**{*;}
-keep class com.oplus.pantaconnect.**{*;}

# 最近文件泛在卡 - 中卡 Provider
-keep class com.oplus.pantanal.seedling.**{*;}
-keep class com.oplus.filemanager.cardwidget.recent.RecentMiddleCardWidgetProvider{*;}

#广告
-keep class com.heytap.ipswitcher.**{*;}
-keep class com.heytap.okhttp.** { *; }
-keepclassmembers class com.heytap.okhttp.extension.HeyCenterHelper$build.** { *; }
-keep class com.opos.overseas.ad.biz.mix
-keep public class com.google.android.gms.** { public protected *; }
# -------- End of extra rules for R8 full mode --------

# coverage
-keep class com.autotest.opasm.**{*;}

#share sdk
-keep class com.oplus.interconnect.share.sdk.data.** {*;}
-keep class com.oplus.interconnect.share.sdk.* {*;}
-keep class com.oplus.interconnect.share.* {*;}
-keep class com.oplus.interconnect.service.** {*;}
-keep class org.apache.poi.** {*;}
-keep class org.apache.logging.log4j.** {*;}
-keep class org.openxmlformats.schemas.** { *; }
-keep class com.microsoft.schemas.** { *; }
-keep class org.apache.commons.compress.archivers.** {*;}