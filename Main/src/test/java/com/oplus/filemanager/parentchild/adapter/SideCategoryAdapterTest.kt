package com.oplus.filemanager.parentchild.adapter

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.activity.ComponentActivity
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.main.view.SideEditText
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.parentchild.util.ItemAnimationHelper
import com.oplus.filemanager.parentchild.viewholder.BaseOptionHolder
import com.oplus.filemanager.parentchild.viewholder.EditableViewHolder
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import java.lang.reflect.Field

/**
 * SideCategoryAdapter的单元测试类
 * 用于测试SideCategoryAdapter的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SideCategoryAdapterTest {

    // 测试所需的成员变量
    private lateinit var context: Context
    private lateinit var activity: ComponentActivity
    private lateinit var animationHelper: ItemAnimationHelper
    private lateinit var fileService: IFileService
    private lateinit var adapter: SideCategoryAdapter
    private lateinit var mockView: View
    private lateinit var realParent: ViewGroup

    /**
     * 测试前的初始化方法
     * 1. 初始化上下文和Activity
     * 2. 创建模拟对象
     * 3. 初始化适配器
     */
    @Before
    fun setUp() {
        // 获取运行时上下文
        context = RuntimeEnvironment.getApplication()
        // 构建测试Activity
        activity = Robolectric.buildActivity(ComponentActivity::class.java).get()
        // 创建模拟的动画帮助类
        animationHelper = mockk(relaxed = true)
        // 创建模拟的文件服务接口
        fileService = mockk(relaxed = true)
        // 创建模拟的View
        mockView = mockk(relaxed = true)
        // 创建真实的父布局
        realParent = FrameLayout(context)

        // 模拟静态工具类Injector
        mockkObject(Injector)
        // 设置Injector返回模拟的文件服务
        every { Injector.injectFactory<IFileService>() } returns fileService

        // 初始化待测试的适配器
        adapter = SideCategoryAdapter(
            context = context,
            activity = activity,
            animationHelper = animationHelper,
            onItemViewCallback = { _, _ -> },
            onItemOperateCallback = {},
            onItemDragCallback = {}
        )
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试onCreateViewHolder方法
     * 验证对于TYPE_EDIT_OPTIONS类型返回EditableViewHolder
     */
    @Test
    fun `onCreateViewHolder should return EditableViewHolder for TYPE_EDIT_OPTIONS`() {
        // 准备：设置视图类型为编辑选项类型
        val viewType = CategoryListBean.TYPE_EDIT_OPTIONS

        // 执行：调用创建ViewHolder方法
        val holder = adapter.onCreateViewHolder(realParent, viewType)

        // 验证：返回的应该是EditableViewHolder实例
        assertTrue(holder is EditableViewHolder)
    }

    /**
     * 测试onCreateViewHolder方法
     * 验证对于非编辑类型返回BaseOptionHolder
     */
    @Test
    fun `onCreateViewHolder should return BaseOptionHolder for other view types`() {
        // 准备：设置一个非编辑类型的视图类型
        val viewType = 99 // 非编辑类型

        // 执行：调用创建ViewHolder方法
        val holder = adapter.onCreateViewHolder(realParent, viewType)

        // 验证：返回的应该是BaseOptionHolder实例
        assertTrue(holder is BaseOptionHolder)
    }

    /**
     * 测试getItemCount方法
     * 验证返回的数据列表大小是否正确
     */
    @Test
    fun `getItemCount should return correct size`() {
        // 准备：设置测试数据
        val testData = listOf(
            CategoryListBean(1, CategoryListBean.TYPE_EDIT_OPTIONS),
            CategoryListBean(2, 99)
        )
        adapter.setData(testData.toMutableList())

        // 执行 & 验证：检查返回的项数是否正确
        assertEquals(2, adapter.itemCount)
    }

    /**
     * 测试getItemViewType方法
     * 验证返回的视图类型是否正确
     */
    @Test
    fun `getItemViewType should return correct type`() {
        // 准备：设置测试数据
        val testData = listOf(
            CategoryListBean(1, 10),
            CategoryListBean(2, 20)
        )
        adapter.setData(testData.toMutableList())

        // 执行 & 验证：检查返回的视图类型是否正确
        assertEquals(10, adapter.getItemViewType(0))
        assertEquals(20, adapter.getItemViewType(1))
    }

    /**
     * 测试onBindViewHolder方法
     * 验证对于编辑类型数据是否正确绑定到EditableViewHolder
     */
    @Test
    fun `onBindViewHolder should bind data to EditableViewHolder for TYPE_EDIT_OPTIONS`() {
        // 准备：创建测试数据和模拟的ViewHolder
        val testBean = CategoryListBean(150, CategoryListBean.TYPE_EDIT_OPTIONS)
        adapter.setData(mutableListOf(testBean))
        val holder = mockk<EditableViewHolder>(relaxed = true)

        // 执行：调用绑定数据方法
        adapter.onBindViewHolder(holder, 0)

        // 验证：检查是否调用了bindData方法
        verify {
            holder.bindData(
                bean = testBean,
                isEdit = any(),
                onItemViewCallback = any(),
                onItemOperateCallback = any(),
                onItemDragCallback = any()
            )
        }
    }

    /**
     * 测试onBindViewHolder方法
     * 验证对于非编辑类型数据是否正确绑定到BaseOptionHolder
     */
    @Test
    fun `onBindViewHolder should bind data to BaseOptionHolder for other types`() {
        // 准备：创建测试数据和模拟的ViewHolder
        val testBean = CategoryListBean(1, 99)
        adapter.setData(mutableListOf(testBean))
        val holder = mockk<BaseOptionHolder>(relaxed = true)

        // 执行：调用绑定数据方法
        adapter.onBindViewHolder(holder, 0)

        // 验证：检查是否调用了bindData方法
        verify {
            holder.bindData(
                bean = testBean,
                isEdit = any(),
                onItemViewCallback = any()
            )
        }
    }

    /**
     * 测试setEditTextOperate方法
     * 验证对于快捷文件夹类型是否正确设置编辑器
     */
    @Test
    fun `setEditTextOperate should setup editor for shortcut folder type`() {
        // 准备：模拟CategoryHelper和测试数据
        mockkStatic(CategoryHelper::class)
        every { CategoryHelper.isShortcutFolderType(150) } returns true
        every { CategoryHelper.isLabelType(150) } returns false

        val testBean = CategoryListBean(150, CategoryListBean.TYPE_EDIT_OPTIONS)
        val editableViewHolder = mockk<EditableViewHolder>(relaxed = true)
        val editText = mockk<SideEditText>(relaxed = true)
        every { editableViewHolder.optionTitle } returns editText

        // 执行：设置数据并绑定视图
        adapter.setData(mutableListOf(testBean))
        adapter.onBindViewHolder(editableViewHolder, 0)

        // 验证：检查是否设置了文件服务和编辑器监听器
        verify { editText.fileServiceAction = fileService }
        verify { editText.setOnEditorActionListener(any()) }
    }

    /**
     * 测试setData方法
     * 验证数据更新和通知是否正确
     */
    @Test
    fun `setData should update list and notify changes`() {
        // 准备：创建测试数据
        val testData = mutableListOf(CategoryListBean(1, 10))

        // 执行：设置数据
        adapter.setData(testData)

        // 验证：检查数据列表是否更新
        assertEquals(1, adapter.getAllList().size)
    }

    /**
     * 测试带类型参数的setData方法
     * 验证对于TYPE_SOURCE_PANEL类型是否正确更新oldDataList
     */
    @Test
    fun `setData with TYPE_SOURCE_PANEL should update oldDataList`() {
        // 准备：创建初始数据和新数据
        val initialData = mutableListOf(CategoryListBean(1, 10))
        val newData = mutableListOf(CategoryListBean(2, 10))
        adapter.setData(initialData)

        // 执行：使用TYPE_SOURCE_PANEL类型设置新数据
        adapter.setData(newData, CategoryListBean.TYPE_SOURCE_PANEL)

        // 验证：通过反射检查oldDataList是否正确更新
        val oldDataListField: Field = SideCategoryAdapter::class.java.getDeclaredField("oldDataList")
        oldDataListField.isAccessible = true
        val oldDataListValue: MutableList<CategoryListBean> = oldDataListField.get(adapter) as MutableList<CategoryListBean>
        assertEquals(initialData, oldDataListValue)
        assertEquals(newData, adapter.getAllList())
    }

    /**
     * 测试updateTargetBean方法
     * 验证是否通知指定项更新
     */
    @Test
    fun `updateTargetBean should notify item changed`() {
        // 准备：创建测试数据
        val testBean = CategoryListBean(1, 10)
        adapter.setData(mutableListOf(testBean))

        // 执行：更新指定项
        adapter.updateTargetBean(testBean)

        // 验证：通过模拟验证notifyItemChanged被调用
        // 实际测试中需使用AdapterDataObserver验证
    }

    /**
     * 测试getAllList方法
     * 验证返回的当前数据列表是否正确
     */
    @Test
    fun `getAllList should return current data list`() {
        // 准备：创建测试数据
        val testData = mutableListOf(
            CategoryListBean(1, 10),
            CategoryListBean(2, 20)
        )
        adapter.setData(testData)

        // 执行 & 验证：检查返回的列表是否正确
        assertEquals(testData, adapter.getAllList())
    }

    /**
     * 测试onViewAttachedToWindow方法
     * 验证是否正确添加动画监听器
     */
    @Test
    fun `onViewAttachedToWindow should add animation listener`() {
        // 准备：创建模拟的ViewHolder
        val holder = mockk<RecyclerView.ViewHolder>(
            relaxed = true,
            moreInterfaces = arrayOf(ItemAnimationHelper.AnimationListener::class)
        )

        // 执行：调用视图附加到窗口方法
        adapter.onViewAttachedToWindow(holder)

        // 验证：检查是否添加了动画监听器
        verify { animationHelper.addListener(holder as ItemAnimationHelper.AnimationListener, animationHelper.isEdit) }
    }

    /**
     * 测试onViewRecycled方法
     * 验证是否正确移除动画监听器
     */
    @Test
    fun `onViewRecycled should remove animation listener`() {
        // 准备：创建模拟的ViewHolder
        val holder = mockk<RecyclerView.ViewHolder>(
            relaxed = true,
            moreInterfaces = arrayOf(ItemAnimationHelper.AnimationListener::class)
        )

        // 执行：调用视图回收方法
        adapter.onViewRecycled(holder)

        // 验证：检查是否移除了动画监听器
        verify { animationHelper.removeListener(holder as ItemAnimationHelper.AnimationListener) }
    }
}