/*********************************************************************************
 * Copyright (C), 2008-2024, Oplus, All rights reserved.
 *
 * File: - GlideExtend.kt
 * Description:
 *     Extend some glide apis.
 *
 * Version: 1.0
 * Date: 2024-11-26
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Bixia<PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-11-26   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.utils

import android.content.Context
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager

internal object GlideExtend {

    private const val GLIDE_ASSERT_ERR_MSG_PREFIX = "You cannot start a load"

    @JvmStatic
    fun with(context: Context): RequestManager? = kotlin.runCatching {
        Glide.with(context)
    }.onFailure {
        val errMsg = it.message ?: throw it
        if (!errMsg.startsWith(GLIDE_ASSERT_ERR_MSG_PREFIX)) {
            throw it
        }
    }.getOrNull()
}