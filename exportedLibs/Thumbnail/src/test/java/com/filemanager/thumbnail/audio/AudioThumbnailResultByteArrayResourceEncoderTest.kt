package com.filemanager.thumbnail.audio

import com.bumptech.glide.load.EncodeStrategy
import com.bumptech.glide.load.Options
import com.bumptech.glide.load.engine.Resource
import com.filemanager.thumbnail.audio.AudioThumbnailResultByteArrayResource
import com.filemanager.thumbnail.audio.AudioThumbnailResultByteArrayResourceEncoder
import com.filemanager.thumbnail.utils.Log
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File
import java.io.FileOutputStream
import java.io.ObjectOutputStream
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@RunWith(RobolectricTestRunner::class)
@Config(maxSdk = 33)
class AudioThumbnailResultByteArrayResourceEncoderTest {

    private lateinit var encoder: AudioThumbnailResultByteArrayResourceEncoder
    private lateinit var mockResource: Resource<AudioThumbnailResultByteArrayResource>
    private lateinit var mockAudioResource: AudioThumbnailResultByteArrayResource
    private lateinit var mockOptions: Options
    private lateinit var tempFile: File

    @Before
    fun setUp() {
        encoder = AudioThumbnailResultByteArrayResourceEncoder()
        mockResource = mockk()
        mockAudioResource = mockk()
        mockOptions = mockk()
        tempFile = File.createTempFile("test", ".tmp")
        
        every { mockResource.get() } returns mockAudioResource
    }

    @After
    fun tearDown() {
        if (tempFile.exists()) {
            tempFile.delete()
        }
    }

    @Test
    fun `getEncodeStrategy should return TRANSFORMED`() {
        assertEquals(EncodeStrategy.TRANSFORMED, encoder.getEncodeStrategy(mockOptions))
    }
}