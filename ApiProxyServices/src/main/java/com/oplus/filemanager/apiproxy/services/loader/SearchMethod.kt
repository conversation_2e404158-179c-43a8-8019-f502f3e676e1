/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchLoader
 ** Description : abstract Search Loader
 ** Version     : 1.0
 ** Date        : 2025/02/19 10:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2025/02/19       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.loader

import android.content.ContentResolver
import android.content.Context
import android.text.TextUtils
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Log
import com.oplus.filemanager.apiproxy.services.data.SearchResultData
import com.oplus.filemanager.apiproxy.services.utils.SearchChecker
import java.util.Locale

abstract class SearchMethod(val context: Context) {
    companion object {
        private const val TAG = "SearchLoader"
    }

    private val internalPath: String by lazy {
        VolumeEnvironment.getInternalSdPath(context)
    }

    private val ignoredPaths by lazy {
        CategoryHelper.setIgnoredPath(context, CategoryHelper.CATEGORY_DOC)
    }

    /**
     * 搜索关键词
     */
    protected var keyword: String? = null

    /**
     * 搜索的类型(0~13)
     */
    protected var fileType: Int = 0

    protected var code = SearchChecker.CODE_OK

    /**
     * 是否支持搜索
     */
    abstract fun isSupport(keyword: String, fileType: Int): Boolean


    /**
     * 开始搜索
     */
    abstract fun handleSearch(contentResolver: ContentResolver, keyword: String, fileType: Int): List<BaseFileBean>


    /**
     * 将数据分类
     */
    abstract fun classifyItem(list: List<BaseFileBean>, searchResult: SearchResultData)


    fun search(contentResolver: ContentResolver, keyword: String?, fileType: Int, searchResult: SearchResultData) {
        if (keyword.isNullOrEmpty()) {
            return
        }
        // 判断是否支持
        if (!isSupport(keyword, fileType)) {
            Log.e(TAG, "handleDmpSearch not support search!!")
            return
        }
        this.keyword = keyword
        this.fileType = fileType
        // 开始搜索
        val list = handleSearch(contentResolver, keyword, fileType)
        // 对结果进行分类
        classifyItem(list, searchResult)
        searchResult.setResultCode(code)
    }


    fun isIgnoredPath(filePath: String?): Boolean {
        val type = MimeTypeHelper.getTypeFromPath(filePath)
        val isTxt = type == MimeTypeHelper.TXT_TYPE
        if (!isTxt) {
            Log.w(TAG, "isIgnoredPath file isn't txt")
            return false
        }
        if (TextUtils.isEmpty(filePath)) {
            Log.w(TAG, "isIgnoredPath path is empty")
            return true
        }
        // check if the path be under the ignored path
        ignoredPaths?.let {
            val locale = Locale.getDefault()
            for (i in 0 until it.size()) {
                val string = internalPath + it[i]
                if (filePath?.lowercase(locale)?.startsWith(string.lowercase(locale)) == true) {
                    Log.v(TAG, "isIgnoredPath path = $filePath")
                    return true
                }
            }
        }
        return false
    }
}