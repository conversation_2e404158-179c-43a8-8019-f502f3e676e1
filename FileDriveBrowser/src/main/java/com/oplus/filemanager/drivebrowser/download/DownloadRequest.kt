/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DownloadRequest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/9 15:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/9       1.0      create
 ***********************************************************************/
@file:JvmName("DownloadRequestKt")
package com.oplus.filemanager.drivebrowser.download

import android.os.Bundle
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem.Companion.obtainExportType
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem.Companion.obtainExtensionByExportType
import org.apache.commons.io.FilenameUtils

private const val TAG = "DownloadRequest"

const val KEY_SOURCE_TYPE = "source_type"

const val KEY_FILE_ID = "fileId"
const val KEY_EXPORT_TYPE = "EXPORT_TYPE"
const val KEY_TARGET_PATH = "targetPath"

fun parseDownloadTaskInfo(requestBundle: Bundle?): DownloadTaskInfo? {
    if (requestBundle == null) {
        Log.d(TAG, "parseDownloadTaskInfo -> requestBundle is null")
        return null
    }
    return when (requestBundle.getInt(KEY_SOURCE_TYPE)) {
        CategoryHelper.CATEGORY_TENCENT_DOCS -> parseTencentDocumentDownloadTaskInfo(requestBundle)

        CategoryHelper.CATEGORY_K_DOCS -> parseKDocsDocumentDownloadTaskInfo(requestBundle)

        else -> null
    }
}

private fun parseTencentDocumentDownloadTaskInfo(requestBundle: Bundle): DownloadTaskInfo {
    val fileId = requestBundle.getString(KEY_FILE_ID) ?: ""
    val targetPath = requestBundle.getString(KEY_TARGET_PATH) ?: ""
    val exportType = requestBundle.getString(KEY_EXPORT_TYPE) ?: ""
    val fileExtensionFromExportType = obtainExtensionByExportType(exportType)
    val fileName = FilenameUtils.getName(targetPath)
    val extensionFromFileName = FilenameUtils.getExtension(fileName)
    val path = if (!extensionFromFileName.isNullOrEmpty()) {
        //文件名称本来自带后缀名时,并且自己的后缀名和extension推测出来的后缀名相同时，使用源文件名称
        if (extensionFromFileName.contentEquals(fileExtensionFromExportType)) {
            targetPath
        } else {
            //文件名称中的后缀名和格式的后缀名不同时，走默认逻辑，添加格式后缀名
            fileExtensionFromExportType?.let { "$targetPath.$fileExtensionFromExportType" } ?: targetPath
        }
    } else {
        //文件名称没有自带后缀名时，走之前的逻辑，人为添加后缀名
        fileExtensionFromExportType?.let { "$targetPath.$fileExtensionFromExportType" } ?: targetPath
    }
    Log.i(TAG, "parseTencentDocumentDownloadTaskInfo targetPathFromIntent $targetPath, " +
            "fileExtensionFromExport $fileExtensionFromExportType , fileExtension $extensionFromFileName, path $path")
    val targetExportType = obtainExportType(exportType)
    Log.d(TAG, "parseTaskInfo -> fileId = $fileId ; exportType = $targetExportType")
    val taskInfo = DownloadTaskInfo()
    taskInfo.category = CategoryHelper.CATEGORY_TENCENT_DOCS
    taskInfo.targetPath = path
    taskInfo.fileId = fileId
    taskInfo.exportType = targetExportType
    return taskInfo
}

private fun parseKDocsDocumentDownloadTaskInfo(requestBundle: Bundle): DownloadTaskInfo {
    val fileId = requestBundle.getString(KEY_FILE_ID) ?: ""
    val targetPath = requestBundle.getString(KEY_TARGET_PATH) ?: ""
    val taskInfo = DownloadTaskInfo()
    taskInfo.category = CategoryHelper.CATEGORY_K_DOCS
    taskInfo.targetPath = targetPath
    taskInfo.fileId = fileId
    return taskInfo
}