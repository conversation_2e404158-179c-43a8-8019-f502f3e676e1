/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/19
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
package com.filemanager.common.sort

import com.filemanager.common.base.BaseFileBean
import org.junit.Assert
import org.junit.Test

class NameToPinyinComparatorGeneratorTest : Assert() {

    @Test
    fun should_return_true_when_validate_pinyin_asc_sorting() {
        // Given
        val fileName1 = "a8149f3142a747c7.txt"
        val fileName2 = "b8149f3142a747c8.doc"
        val fileName3 = "c8149f3142a747cc.ai"
        val fileName4 = "149f3142a747cc.ai"
        val baseFileBeanMutableList = mutableListOf<BaseFileBean>().apply {
            add(BaseFileBean().apply {
                mDisplayName = fileName3
                letter = "c"
            })
            add(BaseFileBean().apply {
                mDisplayName = fileName1
                letter = "a"
            })
            add(BaseFileBean().apply {
                mDisplayName = fileName4
                letter = "1"
            })
            add(BaseFileBean().apply {
                mDisplayName = fileName2
                letter = "b"
            })
        }
        val comparator = NameToPinyinComparatorGenerator.getCategoryComparator(true)

        // When
        baseFileBeanMutableList.sortWith(comparator)

        // Then
        assertEquals(fileName1, baseFileBeanMutableList[0].mDisplayName)
        assertEquals(fileName2, baseFileBeanMutableList[1].mDisplayName)
        assertEquals(fileName3, baseFileBeanMutableList[2].mDisplayName)
        assertEquals(fileName4, baseFileBeanMutableList[3].mDisplayName)
    }
}