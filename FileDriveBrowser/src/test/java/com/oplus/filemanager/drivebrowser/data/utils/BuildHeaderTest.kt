/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : BuildHeaderTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/29 19:56
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/29       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.data.utils

import android.content.Context
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.utils.AppUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

internal class BuildHeaderTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        context = mockk<Context>(relaxed = true)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun testGenerateHeader() {
        mockkStatic(TimeUtils::class)
        every { TimeUtils.obtainTimestamp() }.returns(TIME_STAMP)
        mockkStatic(PropertyCompat::class)
        every { PropertyCompat.sColorOSVersion }.returns(COLOR_OS_VERSION)
        every { PropertyCompat.sAndroidVersion }.returns(ANDROID_VERSION)
        every { PropertyCompat.sModel }.returns(MODEL)
        every { PropertyCompat.sOTAInfo }.returns(OTA_VERSION)
        mockkStatic(AppUtils::class)
        every { AppUtils.getVersionCode() }.returns(VERSION_CODE)
        every { context.packageName }.returns(PACKAGE_NAME)

        val headerMap = BuildHeader.generateHeader(context, APP_ID)
        Assert.assertEquals(TIME_STAMP.toString(), headerMap[BuildHeader.TIME_STAMP])
        Assert.assertEquals(COLOR_OS_VERSION, headerMap[BuildHeader.OPLUS_OS_VERSION])
        Assert.assertEquals(ANDROID_VERSION, headerMap[BuildHeader.ANDROID_VERSION])
        Assert.assertEquals(MODEL, headerMap[BuildHeader.MODEL])
        Assert.assertEquals(VERSION_CODE.toString(), headerMap[BuildHeader.CLIENT_VERSION_CODE])
        Assert.assertEquals(PACKAGE_NAME, headerMap[BuildHeader.CLIENT_PACKAGE])
        Assert.assertEquals(APP_ID, headerMap[BuildHeader.APP_ID])
        Assert.assertEquals(APP_ID, headerMap[BuildHeader.OPENID])
        Assert.assertEquals("", headerMap[BuildHeader.SIGN])
        Assert.assertEquals("", headerMap[BuildHeader.BRAND])
        Assert.assertEquals(OTA_VERSION, headerMap[BuildHeader.OTA_VERSION])
        Assert.assertEquals(VERSION_CODE.toString(), headerMap[BuildHeader.APP_VERSION])

        unmockkStatic(AppUtils::class)
        unmockkStatic(PropertyCompat::class)
        unmockkStatic(TimeUtils::class)
    }

    companion object {
        private const val TIME_STAMP = 1684321673807
        private const val COLOR_OS_VERSION = "V13.1.0"
        private const val ANDROID_VERSION = "13"
        private const val MODEL = "PGEM10"
        private const val OTA_VERSION = "PGEM10_11.A.35_0351_202304252303"
        private const val VERSION_CODE = 13001004
        private const val PACKAGE_NAME = "com.oplus.owork"
        private const val APP_ID = "12312"
    }
}