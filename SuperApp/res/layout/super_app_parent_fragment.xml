<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundWithCard"
    android:splitMotionEvents="false">

    <com.filemanager.common.view.ViewPagerWrapperForPC
        android:id="@+id/view_pager_wrapper"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.filemanager.common.view.viewpager.RTLViewPager
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/appbar"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:orientation="horizontal" />
    </com.filemanager.common.view.ViewPagerWrapperForPC>

    <include layout="@layout/appbar_with_tab_layout_secondary" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>