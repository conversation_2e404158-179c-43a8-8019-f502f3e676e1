package com.oplus.filemanager.cardwidget.download

import android.content.Context
import android.os.Bundle
import androidx.test.platform.app.InstrumentationRegistry
import com.filemanager.common.MyApplication
import com.oplus.filemanager.interfaze.remotedevice.DownloadTransferCard
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * 下载状态卡片的单元测试类
 * 用于测试DownloadCompleteCard、DownloadFailCard、DownloadingCard等下载状态卡片的功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class DownloadStatusCardTest {

    private lateinit var context: Context

    /**
     * 测试前的初始化方法
     * 1. 获取测试上下文
     * 2. 模拟MyApplication对象
     */
    @Before
    fun setUp() {
        // 获取测试上下文环境
        context = InstrumentationRegistry.getInstrumentation().targetContext
        // 模拟MyApplication单例对象
        mockkObject(MyApplication)
        // 设置模拟对象返回测试上下文
        every { MyApplication.appContext } returns context
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试下载完成卡片的功能
     * 验证DownloadCompleteCard的初始化状态和属性值
     */
    @Test
    fun testDownloadCompleteCard() {
        val files = listOf("file1.txt", "file2.jpg")
        val count = 2
        val destPath = "/storage/emulated/0/Download"
        
        // 创建下载完成卡片实例
        val card = DownloadCompleteCard(files, count, destPath)
        
        // 验证卡片状态和属性
        assertEquals(DownloadTransferCard.STATUS_DOWNLOADING, card.status)
        assertEquals(DownloadTransferCard.PROGRESS_MAX, card.progress)
        assertEquals(files, card.files)
        assertEquals("pages/download_completed", card.pageId())
        assertNotNull(card.fileIcon)
        assertTrue(card.title.isNotEmpty())
        assertTrue(card.subTitle.isNotEmpty())
    }

    /**
     * 测试下载失败卡片的功能
     * 验证DownloadFailCard的初始化状态和属性值
     */
    @Test
    fun testDownloadFailCard() {
        val files = listOf("file1.txt")
        val failMsg = "Network error"
        
        // 创建下载失败卡片实例
        val card = DownloadFailCard(files, failMsg)
        
        // 验证卡片状态和属性
        assertEquals(DownloadTransferCard.STATUS_FAIL, card.status)
        assertEquals(0, card.progress)
        assertEquals(files, card.files)
        assertEquals("pages/download_fail", card.pageId())
        assertNotNull(card.fileIcon)
        assertTrue(card.title.isNotEmpty())
        assertEquals(failMsg, card.subTitle)
    }

    /**
     * 测试下载中卡片的功能
     * 验证DownloadingCard的初始化状态和属性值
     */
    @Test
    fun testDownloadingCard() {
        val taskId = 123
        val progress = 50
        val files = listOf("file1.txt", "file2.jpg", "file3.mp4")
        val count = 3
        
        // 创建下载中卡片实例
        val card = DownloadingCard(taskId, progress, files, count)
        
        // 验证卡片状态和属性
        assertEquals(DownloadTransferCard.STATUS_DOWNLOADING, card.status)
        assertEquals(progress, card.progress)
        assertEquals(files, card.files)
        assertEquals("pages/downloading", card.pageId())
        assertNotNull(card.fileIcon)
        assertTrue(card.title.isNotEmpty())
        assertTrue(card.subTitle.isNotEmpty())
    }

    /**
     * 测试使用无效数据创建下载卡片
     * 预期抛出IllegalArgumentException异常
     */
    @Test(expected = IllegalArgumentException::class)
    fun testCreateDownloadCardWithInvalidData() {
        val bundle = Bundle()
        createDownloadCard(bundle)
    }

    /**
     * 测试创建下载完成卡片
     * 验证从Bundle数据正确创建DownloadCompleteCard实例
     */
    @Test
    fun testCreateDownloadCompleteCard() {
        val bundle = Bundle().apply {
            putInt("status", DownloadTransferCard.STATUS_DOWNLOADING)
            putInt("progress", DownloadTransferCard.PROGRESS_MAX)
            putString("files", "[\"file1.txt\",\"file2.jpg\"]")
            putInt("count", 2)
            putString("destPath", "/storage/emulated/0/Download")
        }
        
        // 创建卡片并验证类型
        val card = createDownloadCard(bundle)
        assertTrue(card is DownloadCompleteCard)
    }

    /**
     * 测试创建下载中卡片
     * 验证从Bundle数据正确创建DownloadingCard实例
     */
    @Test
    fun testCreateDownloadingCard() {
        val bundle = Bundle().apply {
            putInt("status", DownloadTransferCard.STATUS_DOWNLOADING)
            putInt("progress", 30)
            putString("files", "[\"file1.txt\"]")
            putInt("count", 1)
            putInt("taskId", 123)
        }
        
        // 创建卡片并验证类型
        val card = createDownloadCard(bundle)
        assertTrue(card is DownloadingCard)
    }

    /**
     * 测试创建下载失败卡片
     * 验证从Bundle数据正确创建DownloadFailCard实例
     */
    @Test
    fun testCreateDownloadFailCard() {
        val bundle = Bundle().apply {
            putInt("status", DownloadTransferCard.STATUS_FAIL)
            putInt("progress", 0)
            putString("files", "[\"file1.txt\"]")
            putString("failMsg", "Network error")
        }
        
        // 创建卡片并验证类型
        val card = createDownloadCard(bundle)
        assertTrue(card is DownloadFailCard)
    }

    /**
     * 测试使用默认值创建下载卡片
     * 当状态无效时，默认创建DownloadingCard实例
     */
    @Test
    fun testCreateDownloadCardWithDefaultValues() {
        val bundle = Bundle().apply {
            putInt("status", 999) // 无效状态
            putInt("progress", 50)
            putString("files", "[\"file1.txt\"]")
        }
        
        // 创建卡片并验证类型
        val card = createDownloadCard(bundle)
        assertTrue(card is DownloadingCard)
        assertEquals(1, card.files.size)
    }
}