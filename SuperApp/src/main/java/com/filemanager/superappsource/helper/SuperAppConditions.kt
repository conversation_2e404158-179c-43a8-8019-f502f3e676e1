/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SuperAppConditions
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/3/2 19:37
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/3/2       1.0      create
 ***********************************************************************/
@file:JvmName("SuperAppConditionsKt")
package com.filemanager.superappsource.helper

import android.content.Context
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.UserManagerUtils

private const val TAG = "SupperAppConditions"

/**
 * Check the constraints displayed by [CommonConstants.OWORK] at the super app are satisfied.
 */
fun checkOWorkSuperAppCondition(context: Context): Boolean {
    val appInstalled = AppUtils.isAppInstalledByPkgName(context, CommonConstants.OWORK)
    val dirExists: Boolean = JavaFileHelper.checkAnyPathExist(CommonConstants.OWORK_DEFAULT_PATHS)
    val funcShow = PreferencesUtils.getBoolean(key = CommonConstants.OWORK_FUNCTION_SHOW, default = true)
    val isSystemUser = UserManagerUtils.checkIsSystemUser(context)
    Log.d(TAG, "check owork condition: installed = $appInstalled - dirExists = $dirExists - funcShow = $funcShow")
    return appInstalled && dirExists && funcShow && isSystemUser
}