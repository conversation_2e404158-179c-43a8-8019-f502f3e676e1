/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SimulateClickReceiver.kt
 * Description:
 *     The system events receiver for SimulateClickService
 *
 * Version: 1.0
 * Date: 2024-05-28
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-05-28   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.service

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.OplusPowerManager
import com.oplus.filemanager.simulateclick.engine.utils.Log
import com.oplus.filemanager.simulateclick.engine.utils.SdkUtils
import com.oplus.os.OplusScreenStatusListener

internal class SimulateClickReceiver(
    private val serviceContext: Context
) : BroadcastReceiver() {

    private val appContext: Context
        get() = serviceContext.applicationContext

    @Suppress("DEPRECATION")
    private val filter = IntentFilter().apply {
        addAction(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)
        if (!SdkUtils.isAtLeastOS11Point3()) {
            addAction(Intent.ACTION_SCREEN_OFF)
        }
    }

    private var screenListener: ScreenStatusListener? = null
    private var isRegistered: Boolean = false

    fun register() {
        Log.d(TAG, "register")
        if (SdkUtils.isAtLeastOS11Point3() && screenListener == null) {
            val listener = ScreenStatusListener()
            OplusPowerManager().registerScreenStatusListener(listener)
            screenListener = listener
        }
        if (isRegistered.not()) {
            appContext.registerExportedReceiver(this, filter, true)
            isRegistered = true
        }
    }

    fun unregister() {
        Log.d(TAG, "unregister")
        if (isRegistered) {
            appContext.unregisterReceiver(this)
            isRegistered = false
        }
        if (SdkUtils.isAtLeastOS11Point3()) {
            val listener = screenListener ?: return
            OplusPowerManager().unregisterScreenStatusListener(listener)
            screenListener = null
        }
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        val action = intent?.action ?: return
        if (!filter.matchAction(action)) {
            return
        }
        val extras = intent.extras
        onHandleReceived(context ?: appContext, action, extras)
    }

    private fun onHandleReceived(context: Context, action: String, extras: Bundle?) {
        Log.d(TAG, "onReceive: action=$action")
        when {
            isScreenOff(action) -> ScriptServiceManager.stopScriptService(context)
            isHomeKey(action, extras) -> ScriptServiceManager.stopScriptService(context)
        }
    }

    private fun isScreenOff(action: String): Boolean =
        action == Intent.ACTION_SCREEN_OFF

    @Suppress("DEPRECATION")
    private fun isHomeKey(action: String, extras: Bundle?): Boolean {
        if (action != Intent.ACTION_CLOSE_SYSTEM_DIALOGS) {
            return false
        }
        val reason = extras?.getString(CLOSE_REASON)
        Log.d(TAG, "isHomeKey: reason=$reason")
        return (reason == REASON_HOME_KEY) || (reason == REASON_RECENT)
    }

    private companion object {
        private const val TAG = "SimulateClickReceiver"
        private const val CLOSE_REASON = "reason"
        private const val REASON_HOME_KEY = "homekey"
        private const val REASON_RECENT = "recentapps"

        @SuppressLint("UnspecifiedRegisterReceiverFlag")
        @JvmStatic
        fun Context.registerExportedReceiver(
            receiver: BroadcastReceiver?,
            filter: IntentFilter,
            exported: Boolean = true
        ): Intent? {
            if (SdkUtils.isAtLeastT()) {
                val receiverFlag = if (exported) {
                    Context.RECEIVER_EXPORTED
                } else {
                    Context.RECEIVER_NOT_EXPORTED
                }
                return this.registerReceiver(receiver, filter, receiverFlag)
            } else {
                return this.registerReceiver(receiver, filter)
            }
        }
    }

    private inner class ScreenStatusListener : OplusScreenStatusListener() {

        override fun onScreenOff() {
            onHandleReceived(appContext, Intent.ACTION_SCREEN_OFF, null)
        }

        override fun onScreenOn() {
            // Unnecessary at current
        }
    }
}