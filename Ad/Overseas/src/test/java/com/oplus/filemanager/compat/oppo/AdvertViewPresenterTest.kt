/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.compat.oppo.AdvertViewPresenterTest
 * * Description :  Unit test
 * * Version     : 1.0
 * * Date        : 2022/7/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.compat.oppo

import android.content.Context
import android.graphics.Point
import android.view.View
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.StatusBarUtil
import com.oplus.filemanager.compat.brand.oppo.BaseFileViewPresenter
import com.opos.overseas.ad.api.template.ITemplateAd
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AdvertViewPresenterTest {

    @MockK
    lateinit var activity: FragmentActivity

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should return size when calculateIndex if span size is less or eq size`() {
        val viewPresenter = mockk<BaseFileViewPresenter>()
        every { viewPresenter.calculateIndex(any(), any()) } answers { callOriginal() }
        assertEquals(1, viewPresenter.calculateIndex(1, 1))

        assertEquals(1, viewPresenter.calculateIndex(3, 1))
        assertEquals(2, viewPresenter.calculateIndex(3, 2))
        assertEquals(3, viewPresenter.calculateIndex(3, 3))
    }

    @Test
    fun `should return 11 when calculateIndex if size is 11`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list = arrayListOf<BaseFileBean>()
        val nextView = mockk<View> {
            every { height } returns 30
        }
        val layoutManager = mockLayoutManager(nextView)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list, layoutManager))
        every { viewPresenter.getRecyclerViewHeight() } returns 160
        every { viewPresenter.getVerticalMargin() } returns 0
        every { viewPresenter.calculateIndex(any(), any()) } answers { callOriginal() }
        assertEquals(11, viewPresenter.calculateIndex(3, 11))
    }

    @Test
    fun `should return 15 when calculateIndex if size is 60`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list = arrayListOf<BaseFileBean>()
        val nextView = mockk<View> {
            every { height } returns 30
        }
        val layoutManager = mockLayoutManager(nextView)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list, layoutManager))
        every { viewPresenter.getRecyclerViewHeight() } returns 160
        every { viewPresenter.getVerticalMargin() } returns 0
        every { viewPresenter.calculateIndex(any(), any()) } answers { callOriginal() }
        assertEquals(15, viewPresenter.calculateIndex(3, 60))
    }

    @Test
    fun `should return 5 when calculateIndex if size is 60 and span count is 1`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list = arrayListOf<BaseFileBean>()
        val nextView = mockk<View> {
            every { height } returns 30
        }
        val layoutManager = mockLayoutManager(nextView, 1)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list, layoutManager))
        every { viewPresenter.getRecyclerViewHeight() } returns 160
        every { viewPresenter.getVerticalMargin() } returns 0
        every { viewPresenter.calculateIndex(any(), any()) } answers { callOriginal() }
        assertEquals(5, viewPresenter.calculateIndex(3, 60))
    }

    private fun mockLayoutManager(nextView: View?, count: Int = 3): GridLayoutManager {
        return mockk {
            every { findViewByPosition(any()) } returns nextView
            every { spanCount } returns count
            every { spanSizeLookup } returns mockk {
                every { getSpanSize(any()) } returns 1
            }
        }
    }

    @Test
    fun `should return 0 when calculateIndex if next view is null`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list = arrayListOf<BaseFileBean>()
        val layoutManager = mockLayoutManager(null)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list, layoutManager))
        every { viewPresenter.getRecyclerViewHeight() } returns 160
        every { viewPresenter.getVerticalMargin() } returns 0
        every { viewPresenter.calculateIndex(any(), any()) } answers { callOriginal() }
        assertEquals(0, viewPresenter.calculateIndex(3, 60))
    }

    @Test
    fun `should not execute method when displayAdvert if templateAd is null`() {
        val viewPresenter = mockk<BaseFileViewPresenter>()
        justRun { viewPresenter.shouldDisplayByPath() }
        every { viewPresenter.displayAdvert(null) } answers { callOriginal() }
        viewPresenter.displayAdvert(null)
        verify(inverse = true) { viewPresenter.shouldDisplayByPath() }
    }

    @Test
    fun `should execute shouldDisplayByPath when displayAdvert if templateAd is not null`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val context = mockk<Context>()
        every { activity.applicationContext } returns context
        val list = arrayListOf<BaseFileBean>()
        val layoutManager = mockLayoutManager(null)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list, layoutManager))
        val templateView = mockk<View>()
        val templateAd = mockk<ITemplateAd> {
            every { buildTemplateView(context) } returns templateView
        }
        every { viewPresenter.shouldDisplayByPath() } returns false
        every { viewPresenter.displayAdvert(templateAd) } answers { callOriginal() }
        viewPresenter.displayAdvert(templateAd)
        verify { viewPresenter.shouldDisplayByPath() }
    }

    @Test
    fun `should execute checkFileList when displayAdvert if templateAd is not null and shouldDisplayAdvert is true`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val context = mockk<Context>()
        every { activity.applicationContext } returns context
        val list = arrayListOf<BaseFileBean>()
        val layoutManager = mockLayoutManager(null)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list, layoutManager))
        val templateView = mockk<View>()
        val templateAd = mockk<ITemplateAd> {
            every { buildTemplateView(context) } returns templateView
        }
        every { viewPresenter.shouldDisplayByPath() } returns true
        justRun { viewPresenter.checkFileList() }
        justRun { viewPresenter.refreshAfterPreDraw() }
        every { viewPresenter.displayAdvert(templateAd) } answers { callOriginal() }

        viewPresenter.displayAdvert(templateAd)
        verify { viewPresenter.checkFileList() }
        verify { viewPresenter.refreshAfterPreDraw() }
    }

    @Test
    fun `should return same file list when checkFileList if difference`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list1 = arrayListOf<BaseFileBean>()
        val list2 = arrayListOf<BaseFileBean>()
        every { adapter.mFiles } returns list2
        val layoutManager = mockLayoutManager(null)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list1, layoutManager))
        every { viewPresenter.checkFileList() } answers { callOriginal() }

        viewPresenter.checkFileList()
        assertEquals(adapter.mFiles, list1)
    }

    @Test
    fun `should return same file list when checkFileList if same`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list1 = arrayListOf<BaseFileBean>()
        every { adapter.mFiles } returns list1
        val layoutManager = mockLayoutManager(null)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list1, layoutManager))
        every { viewPresenter.checkFileList() } answers { callOriginal() }

        viewPresenter.checkFileList()
        assertEquals(adapter.mFiles, list1)
    }

    @Test
    fun `should return 0 when getRecyclerViewHeight if getRecyclerView is null`() {
        val adapter = mockk<BaseSelectionRecycleAdapter<*, BaseFileBean>> {
            every { getRecyclerView() } returns null
        }
        val activity = mockk<FragmentActivity>()
        val list1 = arrayListOf<BaseFileBean>()
        val layoutManager = mockLayoutManager(null)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list1, layoutManager))
        every { viewPresenter.getRecyclerViewHeight() } answers { callOriginal() }

        assertEquals(0, viewPresenter.getRecyclerViewHeight())
    }

    @Test
    fun `should return 160 when getRecyclerViewHeight if checkIsGestureNavMode is true`() {
        mockkStatic(StatusBarUtil::class)

        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list1 = arrayListOf<BaseFileBean>()
        val layoutManager = mockLayoutManager(null)
        every { StatusBarUtil.checkIsGestureNavMode(activity) } returns true
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list1, layoutManager))
        every { viewPresenter.getRecyclerViewHeight() } answers { callOriginal() }

        assertEquals(160, viewPresenter.getRecyclerViewHeight())
        unmockkStatic(StatusBarUtil::class)
    }

    @Test
    fun `should return 104 when getRecyclerViewHeight if checkIsGestureNavMode is false`() {
        mockkObject(KtViewUtils)
        mockkStatic(ViewHelper::class, StatusBarUtil::class)

        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list1 = arrayListOf<BaseFileBean>()
        val layoutManager = mockLayoutManager(null)
        val point = spyk(Point(0, 500))
        every { KtViewUtils.getWindowSize(activity) } returns point
        every { ViewHelper.dip2px(activity, 44) } returns 44
        every { StatusBarUtil.checkIsGestureNavMode(activity) } returns false
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list1, layoutManager))
        every { viewPresenter.getRecyclerViewHeight() } answers { callOriginal() }

        assertEquals(-104, viewPresenter.getRecyclerViewHeight())

        unmockkObject(KtViewUtils)
        unmockkStatic(ViewHelper::class, StatusBarUtil::class)
    }

    @Test
    fun `should not execute notifyItemInsertedWrapper when refreshIfListChanged if templateAd is null`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list1 = arrayListOf<BaseFileBean>()
        val list2 = arrayListOf<BaseFileBean>()
        every { adapter.mFiles } returns list1
        val layoutManager = mockLayoutManager(null)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list2, layoutManager))
        justRun { adapter.notifyItemInsertedWrapper(any()) }
        justRun { viewPresenter.refreshData() }
        every { viewPresenter.refreshIfListChanged() } answers { callOriginal() }

        viewPresenter.refreshIfListChanged()

        verify(inverse = true) { adapter.notifyItemInsertedWrapper(any()) }
        verify(inverse = true) { viewPresenter.refreshData() }
    }

    @Test
    fun `should not execute notifyItemInsertedWrapper when refreshIfListChanged if templateAd is null and params`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list1 = arrayListOf<BaseFileBean>()
        val list2 = arrayListOf<BaseFileBean>()
        val list3 = arrayListOf<BaseFileBean>()
        every { adapter.mFiles } returns list1
        val layoutManager = mockLayoutManager(null)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list2, layoutManager))
        justRun { adapter.notifyItemInsertedWrapper(any()) }
        justRun { viewPresenter.refreshData() }
        every { viewPresenter.refreshIfListChanged(list3) } answers { callOriginal() }

        viewPresenter.refreshIfListChanged(list3)

        verify(inverse = true) { adapter.notifyItemInsertedWrapper(any()) }
        verify(inverse = true) { viewPresenter.refreshData() }
    }

    @Test
    fun `should not execute notifyItemInsertedWrapper when refreshIfListChanged if templateAd is null and same list`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list1 = arrayListOf<BaseFileBean>()
        val list2 = arrayListOf<BaseFileBean>()
        every { adapter.mFiles } returns list1
        val layoutManager = mockLayoutManager(null)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list2, layoutManager))
        justRun { adapter.notifyItemInsertedWrapper(any()) }
        justRun { viewPresenter.refreshData() }
        every { viewPresenter.refreshIfListChanged() } answers { callOriginal() }

        viewPresenter.refreshIfListChanged(list1)

        verify(inverse = true) { adapter.notifyItemInsertedWrapper(any()) }
        verify(inverse = true) { viewPresenter.refreshData() }
    }

    @Test
    fun `should execute refreshData when refreshIfListChanged if cacheIndex is null`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list1 = arrayListOf<BaseFileBean>()
        val list2 = arrayListOf<BaseFileBean>()
        every { adapter.mFiles } returns list1
        val layoutManager = mockLayoutManager(null)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list2, layoutManager))
        justRun { viewPresenter.refreshData() }
        every { viewPresenter.mTemplateAd } returns mockk()
        every { viewPresenter.refreshIfListChanged() } answers { callOriginal() }

        viewPresenter.refreshIfListChanged()

        verify { viewPresenter.refreshData() }
    }

    @Test
    fun `should execute notifyItemInsertedWrapper when refreshIfListChanged if cacheIndex is not null`() {
        val adapter = mockAdapter()
        val activity = mockk<FragmentActivity>()
        val list1 = arrayListOf<BaseFileBean>()
        val list2 = arrayListOf<BaseFileBean>()
        every { adapter.mFiles } returns list1
        val layoutManager = mockLayoutManager(null)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list2, layoutManager))
        justRun { adapter.notifyItemInsertedWrapper(0) }
        every { viewPresenter.getIndexByCacheKey() } returns 0
        every { viewPresenter.mTemplateAd } returns mockk()
        every { viewPresenter.refreshIfListChanged() } answers { callOriginal() }

        viewPresenter.refreshIfListChanged()

        verify { adapter.notifyItemInsertedWrapper(0) }
    }

    @Test
    fun `should execute refreshDelay when refreshIfListChanged if templateAd is not null and params`() {
        val adapter = mockAdapter()
        val list1 = arrayListOf<BaseFileBean>()
        val list2 = arrayListOf<BaseFileBean>()
        val list3 = arrayListOf<BaseFileBean>()
        every { adapter.mFiles } returns list1
        val layoutManager = mockLayoutManager(null)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, list2, layoutManager))
        justRun { adapter.notifyItemInsertedWrapper(any()) }
        every { viewPresenter.mTemplateAd } returns mockk()
        justRun { viewPresenter.refreshData() }
        justRun { viewPresenter.refreshDelay() }
        every { viewPresenter.refreshIfListChanged(list3) } answers { callOriginal() }

        viewPresenter.refreshIfListChanged(list3)

        verify(inverse = true) { adapter.notifyItemInsertedWrapper(any()) }
        verify(inverse = true) { viewPresenter.refreshData() }
        verify { viewPresenter.refreshDelay() }
    }

    private fun mockAdapter(): BaseSelectionRecycleAdapter<*, BaseFileBean> {
        val recyclerView = mockRecyclerView()
        return mockk {
            every { getRecyclerView() } returns recyclerView
        }
    }

    private fun mockRecyclerView(): RecyclerView {
        return mockk {
            every { height } returns 200
            every { paddingTop } returns 20
            every { paddingBottom } returns 20
            every { top } returns 20
            justRun { addOnScrollListener(any()) }
        }
    }

    @Test
    fun `should execute remove old item when closeAdvert`() {
        val viewPresenter = mockk<BaseFileViewPresenter>()
        every { viewPresenter.mTemplateAd } returns null
        justRun { viewPresenter.removeOldItem() }
        every { viewPresenter.closeAdvert() } answers { callOriginal() }
        viewPresenter.closeAdvert()
        assertEquals(null, viewPresenter.mTemplateAd)
        verify { viewPresenter.removeOldItem() }
    }

    @Test
    fun `should 0 when getLastVisibleChildIndex when recyclerView is null`() {
        val adapter = mockAdapter()
        every { adapter.getRecyclerView() } returns null
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, mockk(), mockk()))
        every { viewPresenter.getLastVisibleChildIndex() } answers { callOriginal() }
        assertEquals(0, viewPresenter.getLastVisibleChildIndex())
    }

    @Test
    fun `should 0 when getLastVisibleChildIndex when child is null`() {
        val adapter = mockAdapter()
        val layoutManager = mockLayoutManager(null)
        every { layoutManager.getChildAt(0) } returns null
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, ArrayList(), layoutManager))
        every { viewPresenter.getLastVisibleChildIndex() } answers { callOriginal() }
        assertEquals(0, viewPresenter.getLastVisibleChildIndex())
    }

    @Test
    fun `should 0 when getLastVisibleChildIndex when child position is more than 0`() {
        val adapter = mockAdapter()
        val layoutManager = mockLayoutManager(null)
        val child = mockk<View>()
        every { layoutManager.getChildAt(0) } returns child
        every { layoutManager.getPosition(child) } returns 1
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, ArrayList(), layoutManager))
        every { viewPresenter.getLastVisibleChildIndex() } answers { callOriginal() }
        assertEquals(0, viewPresenter.getLastVisibleChildIndex())
    }

    @Test
    fun `should 0 when getVerticalMargin when span count is 1`() {
        val adapter = mockAdapter()
        val firstView = mockk<View>()
        val layoutManager = mockLayoutManager(firstView, 1)
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, ArrayList(), layoutManager))
        assertEquals(0, viewPresenter.getVerticalMargin())
    }

    @Test
    fun `should 0 when getVerticalMargin when first view is null`() {
        val adapter = mockAdapter()
        val layoutManager = mockLayoutManager(null)
        every { layoutManager.childCount } returns 0
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, ArrayList(), layoutManager))
        assertEquals(0, viewPresenter.getVerticalMargin())
    }

    @Test
    fun `should 101 when getVerticalMargin when top max`() {
        val adapter = mockAdapter()
        val firstView = mockk<View>()
        val layoutManager = mockLayoutManager(firstView)
        every { layoutManager.childCount } returns 0
        every { layoutManager.getTopDecorationHeight(firstView) } returns 101
        every { layoutManager.getBottomDecorationHeight(firstView) } returns 100
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, ArrayList(), layoutManager))
        assertEquals(101, viewPresenter.getVerticalMargin())
    }

    @Test
    fun `should 101 when getVerticalMargin when bottom max`() {
        val adapter = mockAdapter()
        val firstView = mockk<View>()
        val layoutManager = mockLayoutManager(firstView)
        every { layoutManager.childCount } returns 0
        every { layoutManager.getTopDecorationHeight(firstView) } returns 100
        every { layoutManager.getBottomDecorationHeight(firstView) } returns 101
        val viewPresenter = spyk(BaseFileViewPresenter(activity, adapter, ArrayList(), layoutManager))
        assertEquals(101, viewPresenter.getVerticalMargin())
    }
}