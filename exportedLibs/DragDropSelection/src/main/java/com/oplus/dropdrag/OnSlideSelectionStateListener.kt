/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - OnSlideSelectionStateListener.kt
 * Description: abstract base for Selection Tracker
 * Version: 1.0
 * Date : 2021/02/18
 * Author: W9000846
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2021/02/18    1.0     create
 ***********************************************************************/
package com.oplus.dropdrag

import androidx.annotation.Keep

@Keep
interface OnSlideSelectionStateListener {
    /**
     * callback for slide selection start
     */
    fun onSlideSelectionStart()

    /**
     * callback for slide selection end
     */
    fun onSlideSelectionEnd()
}