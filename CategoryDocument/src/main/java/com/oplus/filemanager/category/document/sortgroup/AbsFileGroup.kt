/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/14
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.document.sortgroup

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper

abstract class AbsFileGroup : IFileGroup {

    companion object {
        const val INIT_TITLE_ID = -10000
    }

    override fun convertGroupFileList(fileList: MutableList<MediaFileWrapper>?): MutableList<MediaFileWrapper> {
        val convertList = mutableListOf<MediaFileWrapper>()
        if (fileList.isNullOrEmpty()) {
            return convertList
        }
        var firstTitle = ""
        fileList.forEachIndexed { index, fileWrapper ->
            val nowTitle = createGroupTitle(fileWrapper)
            if (firstTitle != nowTitle) {
                firstTitle = nowTitle
                val titleMediaFileWrapper = MediaFileWrapper().apply {
                    mId = INIT_TITLE_ID - index
                    mDisplayName = nowTitle
                    mFileWrapperViewType = BaseFileBean.TYPE_DOC_GROUP_TITLE
                }
                convertList.add(titleMediaFileWrapper)
            }
            convertList.add(fileWrapper)
        }
        Log.d("DocumentFragment", "convertGroupFileList end: convertList.size: ${convertList.size}")
        return convertList
    }

    /**
     * 分组副标题
     */
    abstract fun createGroupTitle(fileWrapper: MediaFileWrapper): String
}