/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AuthorizationWebViewClient
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/13 17:51
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/13       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import android.net.Uri
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import com.filemanager.common.utils.Log
import com.oplus.filemanager.drivebrowser.data.utils.ServerConfig
import org.jetbrains.annotations.VisibleForTesting

class AuthorizationWebViewClient(
    private val callback: (String, String) -> Unit
) : WebViewClient() {

    override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
        Log.d(TAG, "shouldOverrideUrlLoading -> request = ${request?.url?.toString()}")
        request?.url?.toString()?.let {
            if (redirect(it)) {
                val authorizationResult = parseAuthorizationResult(it)
                callback.invoke(authorizationResult.first, authorizationResult.second)
                return true
            }
        }
        return super.shouldOverrideUrlLoading(view, request)
    }

    @VisibleForTesting
    fun redirect(url: String): Boolean {
        return url.startsWith(ServerConfig.getInstance().kdocsRedirectUrl)
    }

    /**
     * Parse authorization redirect url.
     */
    @VisibleForTesting
    fun parseAuthorizationResult(url: String): Pair<String, String> {
        val uri = Uri.parse(url)
        val code = uri.getQueryParameter(KEY_CODE) ?: ""
        val state = uri.getQueryParameter(KEY_STATE) ?: ""
        Log.d(TAG, "parseAuthorizationResult -> code = $code ; state = $state")
        return Pair(code, state)
    }

    companion object {
        private const val TAG = "AuthorizationWebViewClient"
        @VisibleForTesting
        const val KEY_CODE = "code"
        @VisibleForTesting
        const val KEY_STATE = "state"
    }
}