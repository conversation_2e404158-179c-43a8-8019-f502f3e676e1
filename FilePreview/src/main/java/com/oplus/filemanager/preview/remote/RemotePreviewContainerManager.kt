/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemotePreviewContainerManager.kt
 ** Description: RemotePreviewContainerManager
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.preview.remote

import android.annotation.SuppressLint
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.coui.appcompat.button.COUIButton
import com.filemanager.common.utils.Log
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewContainerManager
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite
import com.oplus.filemanager.preview.widget.PreviewScrollView

internal class RemotePreviewContainerManager(rootLayout: View) : AbsPreviewContainerManager(), IRemotePreviewContainerManager {
    private companion object {
        private const val TAG = "RemotePreviewContainerManager"
    }

    override val infoContainer: ConstraintLayout = rootLayout.findViewById(R.id.preview_remote_info)
    override val imageView: AppCompatImageView = rootLayout.findViewById(R.id.preview_remote_view)
    override val download: COUIButton = rootLayout.findViewById(R.id.preview_download_button)
    override val defaultContainer: PreviewFileInfoSuite =
        rootLayout.findViewById(R.id.preview_remote_def_info)
    override val remoteContainer: ConstraintLayout = rootLayout.findViewById(R.id.preview_remote_container)
    private val loadingLayout = rootLayout.findViewById<View>(R.id.loading_layout)
    override val timeInfo: AppCompatTextView =
        rootLayout.findViewById(R.id.preview_remote_time_info)
    override val sizeInfo: AppCompatTextView =
        rootLayout.findViewById(R.id.preview_remote_size_info)
    override val locationInfo: AppCompatTextView =
        rootLayout.findViewById(R.id.preview_remote_location_info)
    override val remoteTitle: TextViewSnippet =
        rootLayout.findViewById(R.id.preview_remote_title)
    override val downloadDivider: View =
        rootLayout.findViewById(R.id.divider_download)
    override val scroll: PreviewScrollView =
        rootLayout.findViewById(R.id.preview_audio_scroll_area)
    override val rootView: ConstraintLayout =
        rootLayout.findViewById(R.id.rootView)
    override val mDateTv: AppCompatTextView = rootLayout.findViewById(R.id.preview_remote_time_info)
    override val mSizeTv: AppCompatTextView = rootLayout.findViewById(R.id.preview_remote_size_info)
    override val nameContainer: TextViewSnippet = rootLayout.findViewById(R.id.preview_remote_title)

    @SuppressLint("MissingSuperCall")
    override fun showAsLoading() {
        super.showAsLoading()
        Log.d(TAG, "showAsLoading")
        remoteContainer.visibility = View.INVISIBLE
        defaultContainer.visibility = View.GONE
        infoContainer.visibility = View.VISIBLE
        loadingLayout.visibility = View.VISIBLE
        download.visibility = View.VISIBLE
    }

    @SuppressLint("MissingSuperCall")
    override fun showAsFileContainer(isDefault: Boolean) {
        super.showAsFileContainer(isDefault)
        Log.d(TAG, "impl showAsFileContainer: $isDefault")
        loadingLayout.visibility = View.GONE
        download.visibility = View.VISIBLE
        infoContainer.visibility = View.VISIBLE
        if (isDefault) {
            remoteContainer.visibility = View.INVISIBLE
            defaultContainer.visibility = View.VISIBLE
            defaultContainer.setFileNameVisible(false)
            defaultContainer.filePathView.visibility = View.GONE
        } else {
            remoteContainer.visibility = View.VISIBLE
            defaultContainer.visibility = View.GONE
        }
    }
}