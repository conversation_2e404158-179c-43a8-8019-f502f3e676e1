/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.filebrowser.morestorage
 * * Version     : 1.0
 * * Date        : 2020/5/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser.morestorage

import android.content.Context
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.interfaces.OnGetUIInfoListener
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.utils.*
import com.filemanager.common.view.FileManagerRecyclerView
import com.google.android.material.appbar.AppBarLayout
import com.oplus.filebrowser.R
import com.oplus.filebrowser.otg.OtgFileBrowserActivity
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.filechoose.IFileChoose
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.selectdir.filebrowser.SelectFileBrowserViewModel

class MoreStorageFragment : BaseVMFragment<SelectFileBrowserViewModel>(), OnRecyclerItemClickListener,
    OnGetUIInfoListener {
    companion object {
        private const val TAG = "MoreStorageFragment"
    }

    private var mRecyclerView: FileManagerRecyclerView? = null
    private var mAppBarLayout: AppBarLayout? = null
    private var mToolbar: COUIToolbar? = null
    private var mRootView: ViewGroup? = null
    private var mAdapter: MoreStorageAdapter? = null
    private var mMoreStorageViewModel: MoreStorageViewModel? = null
    private var mLayoutManager: GridLayoutManager? = null
    private var mPaths: ArrayList<String>? = null
    private var mTitle: String = ""
    private var isChildDisplay = false

    private val fileChooseAction: IFileChoose? by lazy {
        Injector.injectFactory<IFileChoose>()
    }

    fun onMenuItemSelected(item: MenuItem?): Boolean {
        if (null == item || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            android.R.id.home -> {
                if (baseVMActivity is OtgFileBrowserActivity) {
                    (baseVMActivity as OtgFileBrowserActivity).onBackPressed()
                } else if (fileChooseAction?.isSinglePickerActivity(baseVMActivity) == true) {
                    fileChooseAction?.singlePickerOnBackPressed(baseVMActivity)
                } else {
                    baseVMActivity?.onBackPressed()
                }
                true
            }
            else -> {
                false
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            val bundle = arguments ?: return
            mPaths = bundle.getStringArrayList(KtConstants.P_PATH_LIST)
            mTitle = bundle.getString(KtConstants.P_TITLE) ?: ""
            isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
            mAdapter = MoreStorageAdapter(it, <EMAIL>)
            mAdapter!!.setHasStableIds(true)
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.more_storage_fragment
    }

    override fun initView(view: View) {
        mRootView = view.findViewById(R.id.coordinator_layout)
        mAppBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        mRecyclerView = view.findViewById(R.id.recycler_view)
        mToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        initToolbar()
    }

    private fun initToolbar() {
        //此处修改是遗留的toolbar适配问题
        mRootView?.apply {
            setPadding(paddingLeft,
                    COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom)
        }
        mToolbar?.apply {
            title = mTitle
            titleMarginStart = 0
            isTitleCenterStyle = false
            setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
            inflateMenu(R.menu.more_storage_menu)
        }
        baseVMActivity?.apply {
            setSupportActionBar(mToolbar)
            supportActionBar?.let {
                it.setDisplayHomeAsUpEnabled(!isChildDisplay)
                it.setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        if (mMoreStorageViewModel == null) {
            mMoreStorageViewModel = ViewModelProvider(this)[MoreStorageViewModel::class.java]
        }
        mRecyclerView?.let {
            mLayoutManager = GridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1)
            it.isNestedScrollingEnabled = true
            it.setClipToPadding(false)
            it.layoutManager = mLayoutManager!!
            it.itemAnimator?.changeDuration = 0
            it.itemAnimator?.addDuration = 0
            it.itemAnimator?.removeDuration = 0
            it.itemAnimator?.moveDuration = 0
            mAdapter?.let { adapter ->
                it.adapter = adapter
                adapter.setOnRecyclerItemClickListener(this@MoreStorageFragment)
            }
            mToolbar?.post {
                val paddingBottom = if (it.paddingBottom == 0) {
                    appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                } else {
                    it.paddingBottom
                }
                it.setPadding(it.paddingLeft, KtViewUtils.getRecyclerViewTopPadding(mAppBarLayout), it.paddingRight, paddingBottom)
                mMoreStorageViewModel?.initLoader(mPaths)
            }
        }
    }

    override fun startObserve() {
        mRecyclerView?.post {
            if (mMoreStorageViewModel != null) {
                mMoreStorageViewModel!!.mUiState.observe(this) { list -> mAdapter?.setData(list) }
            }
        }
    }

    override fun onResumeLoadData() {
        Log.d(TAG, "onResumeLoadData size ${mPaths?.size ?: "null"}")
        mMoreStorageViewModel?.initLoader(mPaths)
        val bundle = arguments ?: return
        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
            baseVMActivity?.apply {
                setSupportActionBar(mToolbar)
                supportActionBar?.apply {
                    setDisplayHomeAsUpEnabled(!isChildDisplay)
                    setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
                }
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
    }

    override fun onItemClick(view: View, position: Int) {
        mPaths?.let {
            val currentPath = it[position]
            when {
                baseVMActivity is OtgFileBrowserActivity -> (baseVMActivity as OtgFileBrowserActivity).toOtgFragment(currentPath)

                (fileChooseAction?.isSinglePickerActivity(baseVMActivity) == true) -> {
                    fileChooseAction?.singlePickerTransformToPickerFragment(baseVMActivity, currentPath)
                }
                else -> {
                    baseVMActivity?.let { activity ->
                        val mainAction = Injector.injectFactory<IMain>()
                        if (mainAction?.isMainActivity(activity) == true) {
                            val fileBrowser = Injector.injectFactory<IFileBrowser>()
                            fileBrowser?.startOtgBrowserFragment(activity, mutableListOf(currentPath), fromOTGList = true)
                            return
                        }
                    }
                    Log.e(TAG, "onItemClick do nothing")
                }
            }
        }
    }

    override fun onItemLongClick(view: View, position: Int) {}

    override fun getViewModel() = mMoreStorageViewModel

    override fun getRecyclerView() = mRecyclerView

    fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.P_CHILD_DISPLAY, isChildDisplay)
        baseVMActivity?.supportActionBar?.setDisplayHomeAsUpEnabled(!isChildDisplay)
    }
}