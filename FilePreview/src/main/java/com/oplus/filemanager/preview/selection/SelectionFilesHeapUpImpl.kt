/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SelectionFilesHeapUpImpl.kt
 * Description:
 *     The implementation to heap up selections with their preview image.
 *
 * Version: 1.0
 * Date: 2024-10-22
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-22   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection

import android.content.Context
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.core.AbsPreviewLoadingScheduler
import com.oplus.filemanager.preview.selection.data.HeadUpImageData
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat
import com.oplus.filemanager.preview.selection.data.SelectionItem
import com.oplus.filemanager.preview.selection.loader.AbsHeapUpImageTarget
import com.oplus.filemanager.preview.selection.loader.HeapUpImageLoader
import com.oplus.filemanager.preview.selection.utils.SelectionConstants.MAX_KEEP_IN_MEMORY_COUNT
import com.oplus.filemanager.preview.selection.utils.SelectionStatStringHelper
import com.oplus.filemanager.preview.utils.logInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentLinkedDeque

internal class SelectionFilesHeapUpImpl(
    private val fragment: Fragment,
    private val selectionViewModel: SelectionHeapUpViewModel
) : ISelectionFilesHeapUp {

    private companion object {
        private const val TAG = "SelectionFilesHeapUpImpl"
    }

    private val heapUpLoadManager = HeapUpItemsLoadManager(fragment.requireContext())
    private val statStringHelper = SelectionStatStringHelper(fragment.requireContext())
    private val selectionStatLoader = Observer<SelectionFilesStat?> { onSelectionStatUpdate(it) }
    private val selectionItems = Observer<List<SelectionItem>> { onSelectionItemsUpdate(it) }

    @Volatile
    private var autoFreeJob: Job? = null
    private var heapUpContainer: ISelectionHeapUpContainer? = null

    override fun attachToContainer(container: ISelectionHeapUpContainer) {
        heapUpContainer = container
        selectionViewModel.launchSelectionStat(fragment.viewLifecycleOwner, selectionStatLoader)
        selectionViewModel.launchSelectionItems(fragment.viewLifecycleOwner, selectionItems)
    }

    override fun release() {
        heapUpContainer = null
        selectionViewModel.releaseSelectionStatLauncher(selectionStatLoader)
        selectionViewModel.releaseSelectionItemsLauncher(selectionItems)
        heapUpLoadManager.release()
    }

    override fun updatePaddingBottom(systemBarInsetsBottom: Int) {
        heapUpContainer?.containerRoot?.run {
            setPadding(paddingLeft, paddingTop, paddingRight, systemBarInsetsBottom)
        }
    }

    private fun onSelectionStatUpdate(statData: SelectionFilesStat?) {
        statData ?: return
        val container = heapUpContainer ?: return
        container.filesCountView.text = statStringHelper.formatFilesCount(statData)
        container.filesStatView.text = statStringHelper.formatStatInfo(statData)
    }

    private fun onSelectionItemsUpdate(selectionItems: List<SelectionItem>) {
        val itemsInfo = selectionItems.map { it.selectPriority }
        Log.d(TAG, "onSelectionItemsUpdate: items=$itemsInfo")
        heapUpLoadManager.updateSelectionItems(selectionItems)
    }

    override fun onDeselectionAnimationEnd(onEnd: () -> Unit) {
        heapUpContainer?.heapUpView?.onDeselectionAnimationEnd(onEnd)
    }

    private inner class HeapUpItemsLoadManager(context: Context) : AbsPreviewLoadingScheduler() {
        override val subTag: String = TAG
        private val imageLoader = HeapUpImageLoader(context)
        private val loadedTargets = ConcurrentHashMap<String, AbsHeapUpImageTarget<*>>()
        private val selectedTargets = ConcurrentHashMap<SelectionItem, AbsHeapUpImageTarget<*>>()
        private val keepInMemTargets = ConcurrentLinkedDeque<AbsHeapUpImageTarget<*>>()
        private val autoFreeScope by lazy { CoroutineScope(Dispatchers.IO) }

        fun updateSelectionItems(selectionItems: List<SelectionItem>) {
            selectedTargets.values.forEach { it.clearObservers() }
            if (selectionItems.isEmpty()) {
                keepInMemTargets.addAll(selectedTargets.values)
                selectedTargets.clear()
                updateHeapUpView()
                return
            }
            selectedTargets.forEach { (item, target) ->
                if (!selectionItems.contains(item)) {
                    keepInMemTargets.addFirst(target)
                    selectedTargets.remove(item)
                }
            }
            selectionItems.forEach {
                val imageTarget = obtainImageTarget(it) ?: return@forEach
                keepInMemTargets.remove(imageTarget)
                selectedTargets.remove(it)
                selectedTargets[it] = imageTarget
            }
            if (updateHeapUpView()) {
                return
            }
            selectedTargets.forEach { (item, target) ->
                target.observe(fragment.viewLifecycleOwner) {
                    if (!selectedTargets.values.contains(target)) {
                        return@observe
                    }
                    val updateInfo = "target=${target.hashCode()}, item=${item.selectPriority}"
                    Log.d(TAG, "onSelectionItemsUpdate: update image, $updateInfo")
                    updateHeapUpView()
                }
            }
        }

        private fun obtainImageTarget(item: SelectionItem): AbsHeapUpImageTarget<*>? {
            val key = obtainLoadedKey(item.fileBean) ?: run {
                Log.e(TAG, "obtainImageTarget: ERROR! path is empty, item=${item.selectPriority}")
                return null
            }
            val target = loadedTargets[key] ?: imageLoader.loadHeapUpImage(item.fileBean).also {
                loadedTargets[key] = it
            }
            return target
        }

        private fun obtainLoadedKey(fileBean: BaseFileBean): String? =
            fileBean.mData.takeUnless { it.isNullOrEmpty() }

        private fun updateHeapUpView(): Boolean {
            try {
                return updateHeapUpViewImpl()
            } finally {
                triggerAutoFreeMemory()
            }
        }

        private fun updateHeapUpViewImpl(): Boolean {
            heapUpContainer ?: return false
            Log.d(TAG, "updateHeapUpView: targets=${selectedTargets.map { it.value.hashCode() }}")
            val heapUpMap = toHeapUpMap(selectedTargets)
            val heapUpMapInfo = heapUpMap.map { (item, data) ->
                data.run {
                    "(file=${fileBean.logInfo()}, isLoading=$isLoading, item=${item.selectPriority})"
                }
            }
            Log.d(TAG, "updateHeapUpView: heapUpMap=$heapUpMapInfo")
            val isLoading =
                heapUpMap.isNotEmpty() && (heapUpMap.values.find { it.isLoading } != null)
            if (!isLoading) {
                dismissLoading(heapUpMap)
                return true
            }
            requireToShowLoading()
            return false
        }

        private fun updateLoadedHeapUp(
            container: ISelectionHeapUpContainer,
            heapUpMap: Map<SelectionItem, HeadUpImageData>
        ) {
            Log.d(TAG, "updateLoadedHeapUp: heapUpMap=${heapUpMap.size}")
            container.showAsLoading(false)
            container.heapUpView.updateHeapUp(heapUpMap)
        }

        override fun onShowLoading() {
            heapUpContainer?.showAsLoading(true)
        }

        override fun onGetStartLoadingTime(): Long? =
            heapUpContainer?.startLoadingTime

        @Suppress("UNCHECKED_CAST")
        override fun onDismissLoading(extraObj: Any?) {
            val container = heapUpContainer ?: return
            (extraObj as? Map<SelectionItem, HeadUpImageData>)?.let { heapUpMap ->
                updateLoadedHeapUp(container, heapUpMap)
            }
        }

        @Suppress("UNCHECKED_CAST")
        override fun onLogPrintExtraObj(extraObj: Any?): String =
            "${(extraObj as? Map<SelectionItem, HeadUpImageData>)?.run { "heapUpMap(size=$size)" }}"

        private fun toHeapUpMap(
            targets: Map<SelectionItem, AbsHeapUpImageTarget<*>>
        ): Map<SelectionItem, HeadUpImageData> {
            val heapUpMap = mutableMapOf<SelectionItem, HeadUpImageData>()
            targets.forEach { (item, target) ->
                val imageData = target.imageData.value
                if (imageData != null) {
                    heapUpMap[item] = imageData
                }
            }
            return heapUpMap
        }

        fun release() {
            loadedTargets.values.forEach { it.release() }
            loadedTargets.clear()
            resetLoadingScheduler()
        }

        private fun triggerAutoFreeMemory() {
            if (keepInMemTargets.size <= MAX_KEEP_IN_MEMORY_COUNT) {
                return
            }
            if (autoFreeJob != null) {
                return
            }
            autoFreeJob = autoFreeScope.launch {
                autoFreeMemory()
                autoFreeJob = null
            }
        }

        private fun autoFreeMemory() {
            while (keepInMemTargets.size > MAX_KEEP_IN_MEMORY_COUNT) {
                val toFree = keepInMemTargets.removeLast()
                if (selectedTargets.values.contains(toFree)) {
                    continue
                }
                obtainLoadedKey(toFree.fileBean)?.let(loadedTargets::remove)
                toFree.release()
                Log.d(TAG, "autoFreeMemory: target=${toFree.hashCode()}")
            }
        }
    }
}