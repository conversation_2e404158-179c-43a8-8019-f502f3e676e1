/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:/ViewDataSizeRunnable.kt
 * * Description:
 * * Version:1.0
 * * Date :2020/3/20
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/3/20,        v1.0,           Create
 ****************************************************************/
package com.filemanager.recyclebin.viewmodel

import android.content.Context
import android.database.Cursor
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.filemanager.common.RecycleStore
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.interfaze.recyclebin.RecycleBinTotalSizeCallback
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.recyclebin.utils.RecycleBinUtils
import java.io.File
import java.lang.ref.WeakReference
import java.util.*

const val TIME_30_DAYS = 30 * 24 * 60 * 60 * 1000L

class ViewDataSizeRunnable(context: Context) : Runnable {

    var mSizeListener: RecycleBinTotalSizeCallback? = null
    private val mTarget: WeakReference<Context>? = WeakReference(context)

    companion object {
        const val CODE_SUCC = 0
        const val CODE_ERROR = 1
        const val CODE_STOP = 2
        private const val TAG = "RecycleBinViewDataSizeRunnable"
        private val PROJECTION = arrayOf(
                RecycleStore.Files.FileColumns.RECYCLE_PATH,
                RecycleStore.Files.FileColumns.DISPLAY_NAME,
                RecycleStore.Files.FileColumns.RECYCLE_DATE,
                RecycleStore.Files.FileColumns.SIZE
        )
        private const val INDEX_COLUMN_RECYCLE_PATH = 0
        private const val INDEX_COLUMN_DISPLAY_NAME = 1
        private const val INDEX_COLUMN_RECYCLE_DATE = 2
        private const val INDEX_COLUMN_SIZE = 3
    }

    fun setQuerySizeListener(listener: RecycleBinTotalSizeCallback?) {
        mSizeListener = listener
    }

    override fun run() {
        var totalFileSize = 0L
        var folderList: ArrayList<BaseFileBean>? = null
        var statusCode = CODE_SUCC
        if (Thread.currentThread().isInterrupted) {
            statusCode = CODE_STOP
            calculateSizeAndCallback(totalFileSize, folderList, statusCode, 0L)
            return
        }
        var cursor: Cursor? = null
        var fileCount = 0L
        try {
            cursor = mTarget?.get()?.contentResolver?.query(
                    RecycleStore.Files.INTERNAL_CONTENT_URI,
                    getSizeProjection(),
                    getSQL(), null, getOrderBy())
            Log.d(TAG, "queryFilesSize From Uri Completed, next to build Wrapper -> ${cursor?.count}")
            if ((null != cursor) && !Thread.currentThread().isInterrupted && cursor.moveToFirst()) {
                folderList = ArrayList(cursor.count)
                cursor.moveToFirst()
                do {
                    if (Thread.currentThread().isInterrupted) {
                        break
                    }
                    val displayName = cursor.getString(INDEX_COLUMN_DISPLAY_NAME)
                    val path = cursor.getString(INDEX_COLUMN_RECYCLE_PATH)
                    val size = cursor.getLong(INDEX_COLUMN_SIZE)
                    if (TextUtils.isEmpty(path) || TextUtils.isEmpty(displayName)) {
                        continue
                    }
                    val fileTemp = File(path)
                    if (!fileTemp.exists()) {
                        Log.d(TAG, "run file not exists")
                        continue
                    }
                    val file = PathFileWrapper(path)
                    totalFileSize += size
                    if (size <= 0L) {
                        folderList.add(file)
                    }
                    fileCount++
                } while (cursor.moveToNext())
                val fileSize = folderList.size
                Log.d(TAG, "query fileSize = $totalFileSize  folderSize = $fileSize")
            }
        } catch (e: Exception) {
            statusCode = CODE_ERROR
            Log.e(TAG, "queryFilesSize error -> ${e.message}")
        } finally {
            cursor?.close()
        }
        calculateSizeAndCallback(totalFileSize, folderList, statusCode, fileCount)
    }

    @VisibleForTesting
    fun calculateSizeAndCallback(totalFileSize: Long, folderList: ArrayList<BaseFileBean>?, resultCode: Int, fileCount: Long): Long {
        var totalSize = totalFileSize
        folderList?.let {
            for (file in it) {
                if (Thread.currentThread().isInterrupted) {
                    Log.d(TAG, "calculateSizeAndCallback interrupted")
                    break
                }
                val path = file.mData ?: continue
                totalSize += RecycleBinUtils.getFileOrFolderSize(File(path))
            }
        }
        if (!Thread.currentThread().isInterrupted) {
            Log.d(TAG, "calculateSizeAndCallback: resultCode:$resultCode totalFileSize:$totalFileSize totalSize:$totalSize file:$fileCount")
            mSizeListener?.onTotalSizeReturn(Pair(totalSize, fileCount))
        }
        return totalSize
    }

    private fun getSizeProjection(): Array<String>? {
        return PROJECTION
    }

    private fun getSQL(): String? {
        val endTime = System.currentTimeMillis()
        val startTime = endTime - TIME_30_DAYS
        val sql = StringBuilder()
        sql.append(" ${RecycleStore.Files.FileColumns.RECYCLE_DATE} between $startTime and $endTime AND ")
        sql.append(" ( ${RecycleStore.Files.FileColumns.RECYCLE_DATE} IS NOT NULL ) ")
        Log.d(TAG, "getSQL: $sql")
        return sql.toString()
    }

    private fun getOrderBy(): String? {
        return null
    }
}