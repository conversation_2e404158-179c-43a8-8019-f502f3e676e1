/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemotePreviewFragment.kt
 ** Description: RemotePreviewFragment
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.preview.remote

import android.content.Context
import android.view.View
import androidx.fragment.app.Fragment
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewFragment
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite
import com.oplus.filemanager.preview.widget.PreviewFilePathItem

internal class RemotePreviewFragment :
    AbsPreviewFragment<RemotePreviewViewModel>(R.layout.fragment_preview_remote) {

    private companion object {
        private const val TAG = "RemotePreviewFragment"
    }

    override val logTag: String = TAG
    override val fragmentInstance: Fragment = this
    override val viewModelClass: Class<RemotePreviewViewModel> = RemotePreviewViewModel::class.java
    override val operationsBarId: Int = -1

    private var filePreview: IRemoteFilePreview? = null

    override fun isPreviewFileApproved(context: Context, fileBean: BaseFileBean): Boolean =
        FilePreviewTypeCheckHelper(context).isRemoteType(fileBean, TAG)

    override fun onViewModelCreated(view: View, viewModel: RemotePreviewViewModel) {
        filePreview = prepareFilePreview(view, viewModel)
    }

    override fun onCreateFilePathItem(view: View): PreviewFilePathItem =
        view.findViewById<PreviewFileInfoSuite>(R.id.preview_remote_def_info).filePathView

    private fun prepareFilePreview(
        view: View,
        viewModel: RemotePreviewViewModel
    ): IRemoteFilePreview {
        val previewImpl = IRemoteFilePreview.obtain(this, viewLifecycleOwner, viewModel).apply {
            attachToContainer(RemotePreviewContainerManager(view))
        }
        return previewImpl
    }

    override fun onDestroyView() {
        super.onDestroyView()
        filePreview?.release()
        filePreview = null
    }
}