/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File: FileActionDelete.kt
 ** Description: Delete File/Directory function
 ** Version: 1.0
 ** Date: 2020/2/24
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.recyclebin.operation.action

import android.app.Activity
import android.app.KeyguardManager
import android.content.Context
import android.content.Intent
import android.view.Gravity
import androidx.activity.ComponentActivity
import androidx.annotation.MainThread
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.interfaces.fileoprate.IFileActionObserver
import com.filemanager.common.interfaces.fileoprate.IFileOperateAction
import com.filemanager.common.utils.DeleteSoundUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.VibratorUtil
import com.filemanager.common.wrapper.RecycleBinLiveData
import com.filemanager.recyclebin.RecycleFileManager
import com.filemanager.recyclebin.operation.BaseOperation
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_ERROR_CANCEL
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_SUCCESS
import com.filemanager.recyclebin.operation.RecycleBinCancelListener
import com.filemanager.recyclebin.operation.RecycleBinOperationListener
import com.filemanager.recyclebin.operation.listener.ScanOperationListener
import com.filemanager.recyclebin.utils.RecycleBinUtils
import com.oplus.filemanager.interfaze.fileopentime.IFileOpenTime
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.fileservice.IFileService.Companion.OPERATE_TYPE_DELETE
import com.oplus.filemanager.interfaze.fileservice.IFileService.Companion.OPERATE_TYPE_DELETE_IN_RECYCLE_BIN
import com.oplus.filemanager.interfaze.main.IMain
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import java.io.File

/**
 * @param[mParamState] : the state which may be used in action from outside
 * now it (1 shl 0) shows whether it need to get cShot files when execute
 * @param bgDelete 是否在后台删除：后台删除表示不显示删除确认弹窗，不显示删除进度条
 */
class FileActionDelete(
    private val context: Context,
    private val mSelectFiles: List<BaseFileBean>,
    private val mIsSelectedAll: Boolean = false,
    private val mExternalCategory: Int = 0,
    private val mParamState: Int = 0,
    private var bgDelete: Boolean = false
) : IFileOperateAction<IFileActionObserver>, LifecycleObserver {
    private var mUiObserver: IFileActionObserver? = null
    private var mOperation: ScanOperationListener? = null
    private var confirmDeleteDialog: AlertDialog? = null

    companion object {
        private const val TAG = "FileActionDelete"
        private const val REQUEST_CODE_PASSWORD_KEYGUARD = 9070
    }

    @MainThread
    override fun execute(uiObserver: IFileActionObserver): FileActionDelete {
        if (mSelectFiles.isEmpty()) {
            Log.d(TAG, "Failed to execute: select file is empty")
            return this
        }
        if (context is ComponentActivity) {
            mUiObserver = uiObserver
            context.lifecycle.addObserver(this)
            val isDfmFile = mExternalCategory == CategoryHelper.CATEGORY_DFM
            if (FeatureCompat.sIsLightVersion && !isDfmFile) {
                Log.d(TAG, "execute： isLightVersion, ask user confirm")
                askUserConfirmDelete()
            } else {
                realDeleteFile(!bgDelete, !bgDelete)
            }
        } else {
            Log.w(TAG, "Failed to execute: context is null or not an ComponentActivity")
        }
        return this
    }

    private fun realDeleteFile(showConfirmDialog: Boolean = true, showProgressDialog: Boolean = true) {
        Log.i(TAG, "realDeleteFile showConfirmDialog $showConfirmDialog, showProgressDialog $showProgressDialog")
        val deleteCompleteListener = object : RecycleBinOperationListener {
            override fun onOperationCompleted(opType: Int, result: BaseOperation.OperationResult) {
                Log.d(
                    TAG, "RecycleBinOperationListener total: ${result.mTotalCount}, " +
                        "failed: ${result.mFailedCount}, statusCode: ${result.mStatusCode}")
                if (result.mTotalCount > result.mFailedCount) {
                    syncFileServiceDelete(result)
                    refreshRecentPageData()
                }
                notifyUI(result, opType)
                playDeleteEffect(result)
            }
        }

        val deleteCancelListener = object : RecycleBinCancelListener {
            override fun onCancel() {
                onDestroy()
            }
        }
        Log.d(TAG, "Use recycle bin delete function to execute")
        val files = ArrayList<BaseFileBean>(mSelectFiles.size)
        files.addAll(mSelectFiles)
        val parameters = RecycleFileManager.RecycleFileParameters(
            mParamState = mParamState,
            mCategory = mExternalCategory,
            mIsSelectedAll = mIsSelectedAll,
            mIsShowConfigDialog = showConfirmDialog,
            showProgress = showProgressDialog
        )
        if (context is ComponentActivity) {
            mOperation = RecycleFileManager.INSTANCE.recycleFiles(context, files, deleteCompleteListener, parameters)
            mOperation?.mRecycleBinCancelListener = deleteCancelListener
        }
    }

    /**
     * 同步通知FileService操作
     */
    private fun syncFileServiceDelete(result: BaseOperation.OperationResult) {
        if (result.mStatusCode != STATUS_ERROR_CANCEL) {
            val filePaths = hashSetOf<String?>()
            mSelectFiles.forEach { fileBean ->
                fileBean.mData?.let { file ->
                    val deleteFile = File(file)
                    filePaths.add(deleteFile.parent)
                }
            }
            val fileServiceAction = Injector.injectFactory<IFileService>()
            if (mExternalCategory == CategoryHelper.CATEGORY_RECYCLE_BIN) {
                fileServiceAction?.syncOperate(OPERATE_TYPE_DELETE_IN_RECYCLE_BIN, hashSetOf())
            } else {
                fileServiceAction?.syncOperate(OPERATE_TYPE_DELETE, filePaths)
            }
        }
    }

    /**
     * 删除后同步更新最近页面刷新数据
     */
    private fun refreshRecentPageData() {
        val mainScope = MainScope()
        mainScope.launch(Dispatchers.IO) {
            for (file in mSelectFiles) {
                //For update recent files when entry to recent view from other pages in File Manager after delete file in FileManager
                /**文件删除时，删除数据库里面该文件的数据*/
                val mainAction = Injector.injectFactory<IMain>()
                mainAction?.refreshRecentFileEntityCausedByDelete(file.mData)
                file.mData?.let {
                    val fileOpenTimeAction = Injector.injectFactory<IFileOpenTime>()
                    fileOpenTimeAction?.deleteFileByFilePath(it)
                }
            }
        }
    }


    /**
     * 刷新UI
     */
    private fun notifyUI(result: BaseOperation.OperationResult, opType: Int) {
        val isKeepSelectionMode = if ((result.mStatusCode != STATUS_SUCCESS) && (result.mStatusCode != STATUS_ERROR_CANCEL)) {
            // just reload data if recycle failed and not cancel alert dialog
            opType == RecycleBinLiveData.RECYCLE_RECYCLE
        } else {
            false
        }

        when (mExternalCategory) {
            CategoryHelper.CATEGORY_RECYCLE_BIN -> {
                mUiObserver?.onActionDone(
                    result.mTotalCount > result.mFailedCount,
                    Pair(opType, (result.mTotalCount - result.mFailedCount).toLong())
                )
            }

            else -> {
                if (isKeepSelectionMode) {
                    mUiObserver?.onActionReloadData()
                } else {
                    mUiObserver?.onActionDone(result.mTotalCount > result.mFailedCount)
                }
            }
        }

        // Don't do destroy if keep select mode to do next operation
        if (!isKeepSelectionMode) {
            onDestroy()
        }
    }

    /**
     * 播放删除音效和震动
     * 将播放声音的时间放后面，这样当文件多时，播放声音的时间和删除完之后的时间更加接近
     * play delete sound if at least one item delete success and No cancel operation callback
     */
    private fun playDeleteEffect(result: BaseOperation.OperationResult) {
        if (result.mStatusCode != STATUS_ERROR_CANCEL) {
            Log.d(TAG, "playDeleteSound")
            DeleteSoundUtil.playDeleteSound()
            VibratorUtil.vibrate()
        }
    }

    @VisibleForTesting
    fun askUserConfirmDelete() {
        val builder = COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setWindowGravity(Gravity.BOTTOM)
            .setTitle(getDeleteTitle())
            .setMessage(getDeleteMessage())
            .setNeutralButton(RecycleBinUtils.getDelButtonStr(mIsSelectedAll)) { _, _ ->
                Log.d(TAG, "askUserConfirmDelete: positive button clicked")
                if (isNeededShowPasswordKeyguard()) {
                    Log.d(TAG, "isNeededShowPasswordKeyguard true")
                    showPasswordKeyguard(context)
                } else {
                    realDeleteFile(false)
                }
            }
            .setNegativeButton(com.filemanager.common.R.string.alert_dialog_no, null)
        confirmDeleteDialog = builder.show()
    }

    @VisibleForTesting
    fun getDeleteTitle(): String {
        return if (mSelectFiles.size == 1) {
            appContext.getString(com.filemanager.common.R.string.delete_one_title)
        } else if (mIsSelectedAll) {
            appContext.getString(com.filemanager.common.R.string.delete_all_title)
        } else {
            appContext.resources.getQuantityString(
                com.filemanager.common.R.plurals.delete_item_title,
                mSelectFiles.size,
                mSelectFiles.size
            )
        }
    }

    @VisibleForTesting
    fun getDeleteMessage(): String {
        return if (mSelectFiles.size == 1) {
            appContext.getString(com.filemanager.common.R.string.delete_one_forever_tip)
        } else if (mIsSelectedAll) {
            appContext.getString(com.filemanager.common.R.string.delete_all_forever_tip)
        } else {
            appContext.resources.getQuantityString(
                com.filemanager.common.R.plurals.delete_forever_tip_new,
                mSelectFiles.size,
                mSelectFiles.size
            )
        }
    }

    private fun isNeededShowPasswordKeyguard(): Boolean {
        val shouldShow = mIsSelectedAll && (mSelectFiles.size > 1)
        if (shouldShow) {
            val km = appContext.getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
            if (km.isDeviceSecure.not()) {
                return false
            }
        }
        return shouldShow
    }

    private fun showPasswordKeyguard(context: Context) {
        if ((context is NavigationInterface) && ((context is Fragment) || (context is Activity))) {
            context.registerActionResultListener(object : ActionActivityResultListener {
                override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
                    if ((requestCode == REQUEST_CODE_PASSWORD_KEYGUARD) && (resultCode == Activity.RESULT_OK)) {
                        realDeleteFile(false)
                    }
                }
            })
            val intent = Intent("android.app.action.CONFIRM_DEVICE_CREDENTIAL")
            intent.`package` = PermissionUtils.SETTING_PACKAGE_NAME
            intent.putExtra("start_type", "customize_head")
            intent.putExtra("customize_head_str_pattern", context
                    .getString(com.filemanager.common.R.string.string_delete_password_pattern))
            intent.putExtra("customize_head_str_password", context
                    .getString(com.filemanager.common.R.string.string_delete_password_number))
            try {
                if (context is Fragment) {
                    context.startActivityForResult(intent, REQUEST_CODE_PASSWORD_KEYGUARD)
                } else if (context is Activity) {
                    context.startActivityForResult(intent, REQUEST_CODE_PASSWORD_KEYGUARD)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start psd keyguard: ${e.message}")
            }
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        Log.d(TAG, "onDestroy()")
        (context as? ComponentActivity)?.lifecycle?.removeObserver(this)
        mUiObserver = null
        mOperation?.recycle()
        mOperation = null
    }

    override fun hideDialog() {
        mOperation?.hideDialog()
    }

    override fun isShowDialog(): Boolean {
        return mUiObserver?.isShowDialog() ?: false || confirmDeleteDialog?.isShowing ?: false || mOperation?.isShowDialog() ?: false
    }
}