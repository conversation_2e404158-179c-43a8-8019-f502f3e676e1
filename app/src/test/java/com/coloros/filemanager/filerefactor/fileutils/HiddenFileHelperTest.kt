/***********************************************************
 * * Copyright (C), 2007-2020, Oplus. All rights reserved
 * *
 * * File:/
 * * Description:
 * * Version:1.0
 * * Date :2020/10/12
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/10/12,        v1.0,           Create
</desc></version></data></author> */
package com.coloros.filemanager.filerefactor.fileutils

import com.coloros.filemanager.BaseTest
import com.filemanager.common.constants.CommonConstants.NEED_SHOW_HIDDEN_FILES
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.utils.PreferencesUtils
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class HiddenFileHelperTest : BaseTest() {

    @Test
    fun testIsNeedShowHiddenFile() {
        HiddenFileHelper.mIsNeedShowHiddenFile = true
        Assert.assertTrue(HiddenFileHelper.isNeedShowHiddenFile())
        HiddenFileHelper.mIsNeedShowHiddenFile = false
        Assert.assertFalse(HiddenFileHelper.isNeedShowHiddenFile())
        HiddenFileHelper.mIsNeedShowHiddenFile = null
        PreferencesUtils.put(key = NEED_SHOW_HIDDEN_FILES, value = true)
        Assert.assertTrue(HiddenFileHelper.isNeedShowHiddenFile())
    }

    @Test
    fun testIsHiddenFile() {
        val fileName1 = "test.file"
        val fileName2 = ".test.file"
        Assert.assertFalse(HiddenFileHelper.isHiddenFile(null))
        Assert.assertFalse(HiddenFileHelper.isHiddenFile(fileName1))
        Assert.assertTrue(HiddenFileHelper.isHiddenFile(fileName2))
    }

    @Test
    fun testIsHiddenFileByPath() {
        val fileName1 = "test.file"
        val fileName2 = ".test.file"
        val filePath3 = "/A/B/C/D/.test.file"
        val filePath4 = "/A/B/C/D/test.file"
        val filePath5 = "/A/.B/C/D/test.file"
        val filePath6 = "/A/.B/C/D/.test.file"
        val filePath7 = "/A/.B/C/D.test.file"
        val filePath8 = "/A/B/C/D.test.file"
        Assert.assertFalse(HiddenFileHelper.isHiddenFileByPath(null))
        Assert.assertFalse(HiddenFileHelper.isHiddenFileByPath(fileName1))
        Assert.assertTrue(HiddenFileHelper.isHiddenFileByPath(fileName2))
        Assert.assertTrue(HiddenFileHelper.isHiddenFileByPath(filePath3))
        Assert.assertFalse(HiddenFileHelper.isHiddenFileByPath(filePath4))
        Assert.assertTrue(HiddenFileHelper.isHiddenFileByPath(filePath5))
        Assert.assertTrue(HiddenFileHelper.isHiddenFileByPath(filePath6))
        Assert.assertTrue(HiddenFileHelper.isHiddenFileByPath(filePath7))
        Assert.assertFalse(HiddenFileHelper.isHiddenFileByPath(filePath8))
    }

    @Test
    fun testGetAlphaWithHidden() {
        val fileName1 = "test.file"
        val fileName2 = ".test.file"
        Assert.assertEquals(HiddenFileHelper.getAlphaWithHidden(fileName2, true), 0.5f)
        Assert.assertEquals(HiddenFileHelper.getAlphaWithHidden(fileName2, false), 0.3f)
        Assert.assertEquals(HiddenFileHelper.getAlphaWithHidden(fileName1, true), 1.0f)
        Assert.assertEquals(HiddenFileHelper.getAlphaWithHidden(fileName1, false), 1.0f)
    }

    @Test
    fun testFilterOutHiddenFile() {
        HiddenFileHelper.mIsNeedShowHiddenFile = true
        Assert.assertTrue(HiddenFileHelper.isDisplayFile("test.file"))
        Assert.assertTrue(HiddenFileHelper.isDisplayFile(".test.file"))
        Assert.assertTrue(HiddenFileHelper.isDisplayFile("/android/doc/test.file"))
        Assert.assertTrue(HiddenFileHelper.isDisplayFile("/android/doc/.test.file"))
        Assert.assertTrue(HiddenFileHelper.isDisplayFile("/android/.doc/test.file"))
        Assert.assertTrue(HiddenFileHelper.isDisplayFile("/android/.doc/.test.file"))

        HiddenFileHelper.mIsNeedShowHiddenFile = false
        Assert.assertTrue(HiddenFileHelper.isDisplayFile("test.file"))
        Assert.assertFalse(HiddenFileHelper.isDisplayFile(".test.file"))
        Assert.assertTrue(HiddenFileHelper.isDisplayFile("/android/doc/test.file"))
        Assert.assertFalse(HiddenFileHelper.isDisplayFile("/android/doc/.test.file"))
        Assert.assertFalse(HiddenFileHelper.isDisplayFile("/android/.doc/test.file"))
        Assert.assertFalse(HiddenFileHelper.isDisplayFile("/android/.doc/.test.file"))
    }
}