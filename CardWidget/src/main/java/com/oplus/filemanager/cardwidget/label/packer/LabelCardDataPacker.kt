/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.cardwidget.label.packer.LabelCardDataPacker
 * * Description : 该类主要是负责数据刷新使用的，在这里可以设置卡片的数据对象，并在onPack方法中进行对应控件的赋值
 * * Version     : 1.0
 * * Date        : 2022/12/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.packer

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.Utils
import com.oplus.cardwidget.domain.pack.BaseDataPack
import com.oplus.filemanager.cardwidget.activity.CardWidgetOpenFileActivity
import com.oplus.filemanager.cardwidget.ext.getIconFromType
import com.oplus.filemanager.cardwidget.ext.writeFileProvider
import com.oplus.filemanager.cardwidget.label.data.LabelCardData
import com.oplus.filemanager.cardwidget.util.ANDROID_LAUNCHER_PKG
import com.oplus.filemanager.cardwidget.util.ASSISTANT_SCREEN_PKG
import com.oplus.filemanager.cardwidget.util.CardWidgetThumbnailGenerator
import com.oplus.filemanager.cardwidget.util.OPEN_FILE_ACTION
import com.oplus.filemanager.cardwidget.util.OPPO_LAUNCHER_PKG
import com.oplus.filemanager.cardwidget.util.StartActivityUtil
import com.oplus.filemanager.cardwidget.util.TextUtil
import com.oplus.filemanager.cardwidget.util.isSupportThumbnailForCardWidget
import com.oplus.filemanager.cardwidget.util.ruleForThumbnailFileName
import com.oplus.smartenginehelper.dsl.DSLCoder
import com.oplus.smartenginehelper.entity.StartActivityClickEntity
import org.json.JSONObject
import java.io.File

class LabelCardDataPacker(
    private val context: Context,
    private val labelCard: LabelCardData
) : BaseDataPack() {

    companion object {
        private const val TAG_CARD = "LabelCardDataPacker"

        private const val CONTAINER = "container"
        private const val LABEL_NAME = "label_name"
        private const val LABEL_NAME_ICON = "label_name_icon"
        private const val TOP_SPACE = "top_space"
        private const val LABEL_FILE_MORE = "label_files_more"
        private const val LABEL_FILE_MORE_ICON = "label_files_more_icon"

        @VisibleForTesting
        const val ITEM_NAME_1 = "item_file_name_1"
        @VisibleForTesting
        const val ITEM_NAME_2 = "item_file_name_2"
        @VisibleForTesting
        const val ITEM_NAME_3 = "item_file_name_3"
        @VisibleForTesting
        const val ITEM_NAME_4 = "item_file_name_4"
        private const val ITEM_DURATION_1 = "item_file_duration_1"
        private const val ITEM_DURATION_2 = "item_file_duration_2"
        private const val ITEM_DURATION_3 = "item_file_duration_3"
        private const val ITEM_DURATION_4 = "item_file_duration_4"
        @VisibleForTesting
        const val ITEM_ICON_1 = "item_file_icon_1"
        @VisibleForTesting
        const val ITEM_ICON_2 = "item_file_icon_2"
        @VisibleForTesting
        const val ITEM_ICON_3 = "item_file_icon_3"
        @VisibleForTesting
        const val ITEM_ICON_4 = "item_file_icon_4"

        //非文档缩略图大小及缩放类型
        private const val IMAGE_SIZE_ICON = 36
        private const val SCALE_TYPE_ICON = "fitXY"
        //文档缩略图大小及缩放类型
        private const val IMAGE_SIZE_DOC = 40
        private const val SCALE_TYPE_DOC = "fitCenter"

        private const val DEFAULT_MAX_SIZE = 4
        private const val IMAGE_PADDING = 3
        private const val TABLET_IMAGE_PADDING = 4
    }

    override fun onPack(coder: DSLCoder): Boolean {
        Log.d(TAG_CARD, "onPack -> coder = $coder")
        coder.setOnClick(
            CONTAINER,
            StartActivityUtil().startFileManagerLabelList(labelCard.labelName, labelCard.labelId)
        )
        coder.setOnClick(
            TOP_SPACE,
            StartActivityUtil().startFileManagerLabelList(labelCard.labelName, labelCard.labelId)
        )
        coder.setOnClick(
            LABEL_FILE_MORE,
            StartActivityUtil().startFileManagerLabelList(labelCard.labelName, labelCard.labelId)
        )
        coder.setOnClick(
            LABEL_FILE_MORE_ICON,
            StartActivityUtil().startFileManagerLabelList(labelCard.labelName, labelCard.labelId)
        )
        coder.setOnClick(
            LABEL_NAME,
            StartActivityUtil().startFileManagerLabelList(labelCard.labelName, labelCard.labelId)
        )
        coder.setOnClick(
            LABEL_NAME_ICON, StartActivityUtil().startFileManagerLabelList(labelCard.labelName, labelCard.labelId)
        )
        displayVisibility(coder)
        coder.setTextViewText(LABEL_NAME, TextUtil.getLabelNameForLabelCard(labelCard.labelName))
        TextUtil.setTextSize(coder, context, LABEL_NAME, TextUtil.TEXT_SIZE_14)
        TextUtil.setTextSize(coder, context, LABEL_FILE_MORE, TextUtil.TEXT_SIZE_12)
        val moreVisibility = if (labelCard.fileCount > DEFAULT_MAX_SIZE) {
            View.VISIBLE
        } else {
            View.GONE
        }
        coder.setVisibility(LABEL_FILE_MORE, moreVisibility)
        coder.setVisibility(LABEL_FILE_MORE_ICON, moreVisibility)
        labelCard.fileList?.forEachIndexed { index, baseFile ->
            when (index) {
                0 -> {
                    showFileName(coder, ITEM_NAME_1, baseFile)
                    showIcon(coder, ITEM_ICON_1, baseFile)
                    showDuration(coder, ITEM_DURATION_1, baseFile)
                }
                1 -> {
                    showFileName(coder, ITEM_NAME_2, baseFile)
                    showIcon(coder, ITEM_ICON_2, baseFile)
                    showDuration(coder, ITEM_DURATION_2, baseFile)
                }
                2 -> {
                    showFileName(coder, ITEM_NAME_3, baseFile)
                    showIcon(coder, ITEM_ICON_3, baseFile)
                    showDuration(coder, ITEM_DURATION_3, baseFile)
                }
                3 -> {
                    showFileName(coder, ITEM_NAME_4, baseFile)
                    showIcon(coder, ITEM_ICON_4, baseFile)
                    showDuration(coder, ITEM_DURATION_4, baseFile)
                }
            }
        }
        return true
    }

    @VisibleForTesting
    fun showFileName(coder: DSLCoder, itemId: String, baseFile: BaseFileBean) {
        val displayName = baseFile.mDisplayName ?:""
        val fontSize = if (ModelUtils.isTablet()) TextUtil.TEXT_SIZE_16 else TextUtil.TEXT_SIZE_12
        TextUtil.setTextSize(coder, context, itemId, fontSize)
        coder.setTextViewText(itemId, displayName)
        coder.setTextViewLineSpacingExtra(itemId, -4.0f)
        setClickEventForLabelItem(coder, itemId, baseFile)
    }

    @VisibleForTesting
    fun showDuration(coder: DSLCoder, viewId: String, baseFile: BaseFileBean) {
        val fontSize = if (ModelUtils.isTablet()) TextUtil.TEXT_SIZE_12 else TextUtil.TEXT_SIZE_10
        TextUtil.setTextSize(coder, context, viewId, fontSize)
        coder.setVisibility(viewId, View.VISIBLE)
        val date = Utils.getDateFormat(context, baseFile.mDateModified)
        coder.setTextViewText(viewId, date)
        setClickEventForLabelItem(coder, viewId, baseFile)
    }

    @VisibleForTesting
    fun showIcon(coder: DSLCoder, itemId: String, fileBean: BaseFileBean) {
        val modified = fileBean.mDateModified
        val size = fileBean.mSize
        val path = fileBean.mData ?: ""
        val type = if (fileBean.mIsDirectory) {
            MimeTypeHelper.DIRECTORY_TYPE
        } else {
            val pathType = MimeTypeHelper.getTypeFromPath(path)
            if (pathType == MimeTypeHelper.COMPRESSED_TYPE) {
                //压缩类型，再根据后缀名区分具体类型
                MimeTypeHelper.getCompressedTypeByPath(path)
            } else {
                pathType
            }
        }
        val iconRes = getIconFromType(type)
        if (isSupportThumbnailForCardWidget(fileBean.mLocalType)) {
            //支持加载缩略图的，生成缩略图
            CardWidgetThumbnailGenerator.generate(type, path, modified, size)
            val file = File(ruleForThumbnailFileName(path, modified, size))
            if (file.exists() && file.length() > 0) {
                val uri = writeFileProvider(
                    context,
                    file,
                    arrayOf(ASSISTANT_SCREEN_PKG, ANDROID_LAUNCHER_PKG, OPPO_LAUNCHER_PKG)
                )
                if (MimeTypeHelper.isDocType(fileBean.mLocalType)) {
                    //文档，并且支持缩略图 64X64
                    setImageSizeAndScaleType(coder, itemId, SCALE_TYPE_DOC)
                } else {
                    //其他加载的图片50X50
                    setImageSizeAndScaleType(coder, itemId, SCALE_TYPE_ICON)
                }
                coder.setImageViewResource(itemId, uri.toString())
                if (type != MimeTypeHelper.APPLICATION_TYPE) {
                    val padding = if (ModelUtils.isTablet()) TABLET_IMAGE_PADDING else IMAGE_PADDING
                    coder.setPaddingTop(itemId, padding)
                    coder.setPaddingStart(itemId, padding)
                    coder.setPaddingEnd(itemId, padding)
                    coder.setPaddingBottom(itemId, padding)
                }
            } else {
                setImageSizeAndScaleType(coder, itemId, SCALE_TYPE_ICON)
                coder.setImageViewResource(itemId, iconRes)
            }
        } else {
            setImageSizeAndScaleType(coder, itemId, SCALE_TYPE_ICON)
            coder.setImageViewResource(itemId, iconRes)
        }
        setClickEventForLabelItem(coder, itemId, fileBean)
    }

    @VisibleForTesting
    fun setClickEventForLabelItem(coder: DSLCoder, itemId: String, fileBean: BaseFileBean) {
        val start = StartActivityClickEntity()
        start.setPackageName(appContext.packageName)
        start.setAction(OPEN_FILE_ACTION)
        start.setCategory(Intent.CATEGORY_DEFAULT)
        val paramsJson = JSONObject()
        paramsJson.put(CardWidgetOpenFileActivity.KEY_FILE_PATH, fileBean.mData)
        paramsJson.put(CardWidgetOpenFileActivity.KEY_DISPLAY_NAME, fileBean.mDisplayName)
        paramsJson.put(CardWidgetOpenFileActivity.KEY_LOCAL_TYPE, fileBean.mLocalType)
        paramsJson.put(CardWidgetOpenFileActivity.KEY_SIZE, fileBean.mSize)
        paramsJson.put(CardWidgetOpenFileActivity.KEY_FILE_MODIFIED, fileBean.mDateModified)
        start.setParams(CardWidgetOpenFileActivity.KEY_FILE_BEAN, paramsJson.toString())
        start.addFlag(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        coder.setOnClick(itemId, start)
    }

    @VisibleForTesting
    fun setImageSizeAndScaleType(
        coder: DSLCoder,
        itemId: String,
        scaleType: String
    ) {
        coder.setImageScaleType(itemId, scaleType)
    }

    @VisibleForTesting
    fun displayVisibility(coder: DSLCoder) {
        val itemCount = Math.min(labelCard.fileCount, DEFAULT_MAX_SIZE)
        Log.d(TAG, "displayVisibility fileCount:${labelCard.fileCount} itemCount:$itemCount")
        if (itemCount <= 0) {
            return
        }
        val nameList = mutableListOf(ITEM_NAME_1, ITEM_NAME_2, ITEM_NAME_3, ITEM_NAME_4)
        val iconList = mutableListOf(ITEM_ICON_1, ITEM_ICON_2, ITEM_ICON_3, ITEM_ICON_4)
        val durationList = mutableListOf(ITEM_DURATION_1, ITEM_DURATION_2, ITEM_DURATION_3, ITEM_DURATION_4)

        for (i in 0 until DEFAULT_MAX_SIZE) {
            if (i < itemCount) {
                coder.setVisibility(nameList[i], View.VISIBLE)
                coder.setVisibility(iconList[i], View.VISIBLE)
                coder.setVisibility(durationList[i], View.VISIBLE)
            } else {
                coder.setVisibility(nameList[i], View.INVISIBLE)
                coder.setVisibility(iconList[i], View.INVISIBLE)
                coder.setVisibility(durationList[i], View.INVISIBLE)
            }
        }
    }
}