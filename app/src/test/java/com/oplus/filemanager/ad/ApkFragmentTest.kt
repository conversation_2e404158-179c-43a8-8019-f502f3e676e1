/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ApkFragmentTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/1/7
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  yanxiaowei        2022/1/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.ad

import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.category.apk.ui.ApkFragment
import io.mockk.InternalPlatformDsl
import io.mockk.mockk
import org.junit.Test
import io.mockk.spyk
import org.junit.Assert

class ApkFragmentTest {
    @Test
    fun `should return true when viewtype is TYPE_FILE_AD`() {
        val viewType = BaseFileBean.TYPE_FILE_AD
        Assert.assertTrue(executePrivateMethodIsTheGridViewType(viewType))
    }

    @Test
    fun `should return true when viewtype is TYPE_LABEL_FILE`() {
        val viewType = BaseFileBean.TYPE_LABEL_FILE
        Assert.assertTrue(executePrivateMethodIsTheGridViewType(viewType))
    }

    @Test
    fun `should return true when viewtype is TYPE_FILE_LIST_HEADER`() {
        val viewType = BaseFileBean.TYPE_FILE_LIST_HEADER
        Assert.assertTrue(executePrivateMethodIsTheGridViewType(viewType))
    }

    @Test
    fun `should return false when viewtype is TYPE_RELATED_FILE`() {
        val viewType = BaseFileBean.TYPE_RELATED_FILE
        Assert.assertFalse(executePrivateMethodIsTheGridViewType(viewType))
    }

    @Test
    fun `should return true when viewtype is TYPE_FILE_LIST_FOOTER`() {
        val viewType = BaseFileBean.TYPE_FILE_LIST_FOOTER
        Assert.assertTrue(executePrivateMethodIsTheGridViewType(viewType))
    }

    private fun executePrivateMethodIsTheGridViewType(
        viewType: Int
    ): Boolean {
        val apkFragment = spyk(ApkFragment(), recordPrivateCalls = true)
        return InternalPlatformDsl.dynamicCall(
            apkFragment,
            "isTheGridViewType",
            arrayOf(viewType)
        ) { mockk() } as Boolean
    }
}