/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : NoAgreePrivacyDataPackerTest.kt
 ** Description : NoAgreePrivacyDataPacker Unit Test
 ** Version     : 1.0
 ** Date        : 2023/11/24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue       2023/11/24      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.packer

import android.content.Context
import com.filemanager.common.MyApplication
import com.oplus.filemanager.cardwidget.label.packer.NoAgreePrivacyDataPacker
import com.oplus.filemanager.cardwidget.util.TextUtil
import com.oplus.smartenginehelper.dsl.DSLCoder
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test

class NoAgreePrivacyDataPackerTest {

    private lateinit var context: Context
    private lateinit var coder: DSLCoder
    private lateinit var packer: NoAgreePrivacyDataPacker

    @Before
    fun setup() {
        context = mockk()
        mockkObject(MyApplication)
        every { MyApplication.sAppContext }.returns(context)
        every { context.packageName }.returns("FileManager")
        coder = mockk()
        packer = NoAgreePrivacyDataPacker(context)
    }

    @After
    fun teardown() {
        unmockkObject(MyApplication)
    }

    @Test
    fun should_call_setTextViewText_when_onPack() {
        mockkStatic(TextUtil::class)
        justRun { TextUtil.setTextSize(any(), any(), any(), any()) }
        justRun { coder.setTextViewText(any(), any()) }
        justRun { coder.setOnClick(any(), any()) }

        packer.onPack(coder)
        verify { coder.setTextViewText("tips", "@string/card_agree_privacy") }
        verify { coder.setOnClick("container", any()) }

        unmockkStatic(TextUtil::class)
    }
}