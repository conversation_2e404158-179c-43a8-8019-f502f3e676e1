/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : AuthorizationWebViewClientTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/30 10:47
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/30       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import android.net.Uri
import android.webkit.WebResourceRequest
import android.webkit.WebView
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.data.utils.ServerConfig
import com.oplus.filemanager.drivebrowser.data.utils.ServerConfig.Companion.HOST_KDOCS_AUTHORIZATION_REDIRECT_URL_TEST
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Test

class AuthorizationWebViewClientTest {

    @Test
    fun testShouldOverrideUrlLoading() {
        mockkObject(ServerConfig.Companion)
        val serverConfig = mockk<ServerConfig>()
        every { serverConfig.kdocsRedirectUrl }.returns(HOST_KDOCS_AUTHORIZATION_REDIRECT_URL_TEST)
        every { ServerConfig.getInstance() }.returns(serverConfig)
        val callback = mockk<(String, String) -> Unit>()
        justRun { callback.invoke(any(), any()) }
        val client = AuthorizationWebViewClient(callback)
        val webView = mockk<WebView>(relaxed = true)
        val request = mockk<WebResourceRequest>()
        every { request.url }.returns(null)
        val requestNullResult = client.shouldOverrideUrlLoading(webView, request = null)
        Assert.assertFalse(requestNullResult)
        val requestUrlNullResult = client.shouldOverrideUrlLoading(webView, request)
        Assert.assertFalse(requestUrlNullResult)
        every { request.url.toString() }.returns(AUTHORIZATION_URL)
        mockkStatic(Uri::class)
        val mockUri = mockk<Uri>()
        every { Uri.parse(any()) }.returns(mockUri)
        every { mockUri.getQueryParameter(AuthorizationWebViewClient.KEY_CODE) }.returns(null)
        every { mockUri.getQueryParameter(AuthorizationWebViewClient.KEY_STATE) }.returns(null)
        val redirectResult = client.shouldOverrideUrlLoading(webView, request)
        verify { callback.invoke("", "") }
        Assert.assertTrue(redirectResult)
        unmockkStatic(Uri::class)
        unmockkObject(ServerConfig.Companion)
    }

    @Test
    fun testRedirect() {
        val url = ""
        val callback = mockk<(String, String) -> Unit>()
        val client = AuthorizationWebViewClient(callback)
        val isRedirectWithEmptyUrl = client.redirect(url)
        Assert.assertFalse(isRedirectWithEmptyUrl)

        mockkObject(ServerConfig.Companion)
        val serverConfig = mockk<ServerConfig>()
        every { serverConfig.kdocsRedirectUrl }.returns(HOST_KDOCS_AUTHORIZATION_REDIRECT_URL_TEST)
        every { ServerConfig.getInstance() }.returns(serverConfig)

        val redirectUrl = "${TencentFileDriveRepository.REDIRECT_URL_PATH}$SUFFIX_REDIRECT_URL"
        val isRedirect = client.redirect(redirectUrl)
        Assert.assertTrue(isRedirect)
        unmockkObject(ServerConfig.Companion)
    }

    @Test
    fun testParseAuthorizationResult() {
        mockkStatic(Uri::class)
        val mockUri = mockk<Uri>()
        every { Uri.parse(any()) }.returns(mockUri)
        every { mockUri.getQueryParameter(AuthorizationWebViewClient.KEY_CODE) }.returns(null)
        every { mockUri.getQueryParameter(AuthorizationWebViewClient.KEY_STATE) }.returns(null)
        val callback = mockk<(String, String) -> Unit>()
        val client = AuthorizationWebViewClient(callback)
        val parseResult = client.parseAuthorizationResult(AUTHORIZATION_URL)
        Assert.assertEquals("", parseResult.first)
        Assert.assertEquals("", parseResult.second)

        every { mockUri.getQueryParameter(AuthorizationWebViewClient.KEY_CODE) }.returns(AUTHORIZATION_CODE)
        every { mockUri.getQueryParameter(AuthorizationWebViewClient.KEY_STATE) }.returns(AUTHORIZATION_STATE)
        val parseValidResult = client.parseAuthorizationResult(AUTHORIZATION_URL)
        Assert.assertEquals(AUTHORIZATION_CODE, parseValidResult.first)
        Assert.assertEquals(AUTHORIZATION_STATE, parseValidResult.second)
        unmockkStatic(Uri::class)
    }

    companion object {
        private const val AUTHORIZATION_URL =
            "https://owork-test.wanyol.com/mobile-third-doc/login&state=ggxUDizrENKNC6HAoXm0rE0EPyROkcdm"
        private const val SUFFIX_REDIRECT_URL = "/test"
        private const val AUTHORIZATION_CODE = "04MRUFDSOAKRSPHJ4J-QLW"
        private const val AUTHORIZATION_STATE = "ggxUDizrENKNC6HAoXm0rE0EPyROkcdm"
    }
}