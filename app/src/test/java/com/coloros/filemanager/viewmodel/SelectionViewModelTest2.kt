/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SelectionViewModelTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/10/14
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/10/14        1.0      create
 ***********************************************************************/
package com.coloros.filemanager.viewmodel

import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.utils.SdkUtils
import com.oplus.dropdrag.SelectionTracker
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.mockkStatic
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test


class SelectionViewModelTest2 {

    private lateinit var selectionViewModel: SelectionViewModel<*, *>

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        selectionViewModel = object : SelectionViewModel<FakeBaseFileBean, FakeUiModel>() {
            override fun getRealFileSize(): Int {
                return 0
            }

            override fun loadData() {
                // fake view model do nothing
            }

            override fun getRecyclerViewScanMode(): SelectionTracker.LAYOUT_TYPE {
                return SelectionTracker.LAYOUT_TYPE.GRID
            }

        }
    }

    @Test
    fun `test whether can drag drop`() {
        mockkStatic(SdkUtils::class)
        mockkObject(PCConnectAction)
        mockkObject(UIConfigMonitor)
        every { SdkUtils.isAtLeastR() }.returns(false)
        assertEquals(false, selectionViewModel.canDragDrop())

        every { SdkUtils.isAtLeastR() }.returns(true)
        every { PCConnectAction.isScreenCast() }.returns(true)

        assertEquals(false, selectionViewModel.canDragDrop())


        every { PCConnectAction.isScreenCast() }.returns(false)
        every { PCConnectAction.isPadScreenCast() }.returns(false)
        every { UIConfigMonitor.isAnyAppZoomWindowShow() }.returns(false)
        every { UIConfigMonitor.isMultiWindow() }.returns(false)
        assertEquals(true, selectionViewModel.canDragDrop())

        every { UIConfigMonitor.isAnyAppZoomWindowShow() }.returns(true)
        every { UIConfigMonitor.isMultiWindow() }.returns(false)
        assertEquals(true, selectionViewModel.canDragDrop())

        every { UIConfigMonitor.isAnyAppZoomWindowShow() }.returns(false)
        every { UIConfigMonitor.isMultiWindow() }.returns(true)
        assertEquals(true, selectionViewModel.canDragDrop())

        every { UIConfigMonitor.isAnyAppZoomWindowShow() }.returns(true)
        every { UIConfigMonitor.isMultiWindow() }.returns(true)
        assertEquals(true, selectionViewModel.canDragDrop())

        every { PCConnectAction.isScreenCast() }.returns(false)
        every { PCConnectAction.isPadScreenCast() }.returns(true)
        every { UIConfigMonitor.isAnyAppZoomWindowShow() }.returns(false)
        every { UIConfigMonitor.isMultiWindow() }.returns(false)
        assertEquals(true, selectionViewModel.canDragDrop())
    }

}