/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:/RecycleBinOperationListener.kt
 * * Description:
 * * Version:1.0
 * * Date :2020/3/10
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * * ZejiangDuan, 2020/3/10, v1.0,
 ****************************************************************/
package com.filemanager.recyclebin.operation

import com.filemanager.common.wrapper.RecycleBinLiveData

interface RecycleBinOperationListener {

    /**
     * @param opType Operation Type, default vale [RecycleBinLiveData.RECYCLE_NONE];
     * @param result Operation result
     */
    fun onOperationCompleted(opType: Int, result: BaseOperation.OperationResult)
}