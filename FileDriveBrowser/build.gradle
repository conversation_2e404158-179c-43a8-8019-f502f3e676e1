plugins {
    id 'com.android.library'
    id "kotlin-parcelize"
    id 'kotlinx-serialization'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace "com.oplus.filemanager.drivebrowser"

    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    implementation libs.kotlinx.serialization.json
    implementation libs.androidx.appcompat
    // Lifecycle
    implementation libs.androidx.lifecycle.service
    implementation libs.androidx.lifecycle.runtime.ktx
    // paging
    implementation libs.androidx.paging.ktx
    implementation libs.google.material
    implementation libs.apache.commons.io
    implementation libs.allawn.crypto.android
    implementation libs.bundles.squareup.okhttp3
    implementation libs.bundles.squareup.retrofit2

    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.dialog
    implementation libs.oplus.appcompat.recyclerview
    implementation libs.oplus.appcompat.poplist
    implementation libs.oplus.appcompat.button
    implementation libs.oplus.appcompat.scrollview
    implementation libs.oplus.appcompat.statement
    implementation libs.oplus.appcompat.toolbar
    implementation libs.oplus.appcompat.sidenavigationbar
    implementation libs.oplus.appcompat.bottomnavigation
    implementation libs.oplus.appcompat.panel
    implementation libs.oplus.stdid.sdk
    implementation libs.koin.android
    implementation libs.taphttp
    implementation libs.taphttp.domestic

    implementation project(':Common')
    implementation project(':Provider')
    implementation project(':FileOperate')
    implementation project(':SelectDir')

    if (prop_use_prebuilt_drap_drop_lib.toBoolean()) {
        implementation libs.oplus.filemanager.dragDrop
    } else {
        implementation project(':exportedLibs:DragDropSelection')
    }
}