/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : DocumentDownloadDispatcherTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/30 18:59
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/30       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.download

import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.os.Bundle
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.CustomToast
import com.oplus.filemanager.drivebrowser.data.repository.DocumentDownloadErrorCode
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.unmockkStatic
import io.mockk.verify
import kotlinx.coroutines.test.TestScope
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class DocumentDownloadDispatcherTest {

    private lateinit var context: Context
    private lateinit var scope: TestScope

    @Before
    fun setup() {
        context = mockk()
        val resources = mockk<Resources>(relaxed = true)
        every { context.resources }.returns(resources)
        every { context.getString(any()) }.returns("")
        every { context.getString(any(), any()) }.returns("")
        every { resources.getQuantityString(any(), any(), any(), any()) }.returns("")
        scope = TestScope()
    }

    @After
    fun teardown() {
        unmockkAll()
    }

    @Test
    fun testStartCommandEnqueueDownload() {
        val dispatcher = spyk(DocumentDownloadDispatcher(context, scope))
        justRun { dispatcher.handleEnqueueDownload(any()) }
        val intent = mockk<Intent>()
        every { intent.action }.returns(DocumentDownloadDispatcher.ACTION_ENQUEUE_DOWNLOAD)
        dispatcher.onStartCommand(intent)
        verify { dispatcher.handleEnqueueDownload(any()) }
    }

    @Test
    fun testStartCommandCancelDownload() {
        val dispatcher = spyk(DocumentDownloadDispatcher(context, scope))
        justRun { dispatcher.handleCancelDownload() }
        val intent = mockk<Intent>()
        every { intent.action }.returns(DocumentDownloadDispatcher.ACTION_CANCEL_DOWNLOAD)
        dispatcher.onStartCommand(intent)
        verify { dispatcher.handleCancelDownload() }
    }

    @Test
    fun testStartCommandRetryDownload() {
        val dispatcher = spyk(DocumentDownloadDispatcher(context, scope))
        justRun { dispatcher.handleRetryDownload(any()) }
        val intent = mockk<Intent>()
        every { intent.action }.returns(DocumentDownloadDispatcher.ACTION_RETRY_DOWNLOAD)
        dispatcher.onStartCommand(intent)
        verify { dispatcher.handleRetryDownload(any()) }
    }

    @Test
    fun testHandleEnqueueDownloadWhenRequestInvalid() {
        mockkStatic("com.oplus.filemanager.drivebrowser.download.DownloadRequestKt")
        every { parseDownloadTaskInfo(any()) }.returns(null)
        val dispatcher = spyk(DocumentDownloadDispatcher(context, scope))
        val callback = mockk<DocumentDownloadDispatcher.Callback>()
        justRun { callback.stop() }
        dispatcher.setCallback(callback)
        val intent = mockk<Intent>()
        val bundle = mockk<Bundle>()
        every { intent.extras }.returns(bundle)
        dispatcher.handleEnqueueDownload(intent)
        verify { callback.stop() }
        unmockkStatic("com.oplus.filemanager.drivebrowser.download.DownloadRequestKt")
    }

    @Test
    fun testHandleEnqueueDownloadWhenOtherRequestInvalid() {
        mockkStatic("com.oplus.filemanager.drivebrowser.download.DownloadRequestKt")
        every { parseDownloadTaskInfo(any()) }.returns(DownloadTaskInfo(CategoryHelper.CATEGORY_DOWNLOAD))
        val dispatcher = spyk(DocumentDownloadDispatcher(context, scope))
        val callback = mockk<DocumentDownloadDispatcher.Callback>()
        justRun { callback.stop() }
        dispatcher.setCallback(callback)
        val intent = mockk<Intent>()
        val bundle = mockk<Bundle>()
        every { intent.extras }.returns(bundle)
        dispatcher.handleEnqueueDownload(intent)
        verify { callback.stop() }
        unmockkStatic("com.oplus.filemanager.drivebrowser.download.DownloadRequestKt")
    }

    @Test
    fun testHandleEnqueueDownloadWhenTencentRequestInvalid() {
        mockkStatic("com.oplus.filemanager.drivebrowser.download.DownloadRequestKt")
        every { parseDownloadTaskInfo(any()) }.returns(DownloadTaskInfo(CategoryHelper.CATEGORY_TENCENT_DOCS))
        val dispatcher = spyk(DocumentDownloadDispatcher(context, scope))
        val callback = mockk<DocumentDownloadDispatcher.Callback>()
        justRun { callback.stop() }
        dispatcher.setCallback(callback)
        val intent = mockk<Intent>()
        val bundle = mockk<Bundle>()
        every { intent.extras }.returns(bundle)
        dispatcher.handleEnqueueDownload(intent)
        verify { callback.stop() }
        unmockkStatic("com.oplus.filemanager.drivebrowser.download.DownloadRequestKt")
    }

    @Test
    fun testHandleEnqueueDownloadWhenKDocsRequestInvalid() {
        mockkStatic("com.oplus.filemanager.drivebrowser.download.DownloadRequestKt")
        every { parseDownloadTaskInfo(any()) }.returns(DownloadTaskInfo(CategoryHelper.CATEGORY_K_DOCS))
        val dispatcher = spyk(DocumentDownloadDispatcher(context, scope))
        val callback = mockk<DocumentDownloadDispatcher.Callback>()
        justRun { callback.stop() }
        dispatcher.setCallback(callback)
        val intent = mockk<Intent>()
        val bundle = mockk<Bundle>()
        every { intent.extras }.returns(bundle)
        dispatcher.handleEnqueueDownload(intent)
        verify { callback.stop() }
        unmockkStatic("com.oplus.filemanager.drivebrowser.download.DownloadRequestKt")
    }

    @Test
    fun testPromoteAndExecute() {
        mockkStatic("com.oplus.filemanager.drivebrowser.download.DownloadRequestKt")
        every { parseDownloadTaskInfo(any()) }.returns(
            DownloadTaskInfo(
                CategoryHelper.CATEGORY_TENCENT_DOCS,
                fileId = FILE_ID
            )
        )
        val dispatcher = spyk(DocumentDownloadDispatcher(context, scope))
        justRun { dispatcher.internalDownload(any()) }
        val callback = mockk<DocumentDownloadDispatcher.Callback>()
        justRun { callback.stop() }
        dispatcher.setCallback(callback)
        val intent = mockk<Intent>()
        val bundle = mockk<Bundle>()
        every { intent.extras }.returns(bundle)
        dispatcher.handleEnqueueDownload(intent)
        verify { dispatcher.internalDownload(any()) }
        Assert.assertEquals(1, dispatcher.runningTaskCounts())
        unmockkStatic("com.oplus.filemanager.drivebrowser.download.DownloadRequestKt")
    }

    @Test
    fun testDisposeFailureWhenIsCancel() {
        val taskInfo = DownloadTaskInfo()
        val downloadResult = DownloadResult(false)
        val dispatcher = spyk(DocumentDownloadDispatcher(context, scope))
        dispatcher.setCancel(true)
        dispatcher.disposeFailure(taskInfo, downloadResult)
        verify { dispatcher.setCancel(false) }
    }

    @Test
    fun testDisposeFailureWhenExportFailure() {
        mockkStatic(CustomToast::class)
        justRun { CustomToast.showShort(any() as Int) }
        val taskInfo = DownloadTaskInfo()
        val downloadResult = DownloadResult(false, DocumentDownloadErrorCode.EXPORT_FILE_ERROR.value)
        val dispatcher = spyk(DocumentDownloadDispatcher(context, scope))
        dispatcher.setCancel(false)
        dispatcher.disposeFailure(taskInfo, downloadResult)
        verify { CustomToast.showShort(any() as Int) }
        unmockkStatic(CustomToast::class)
    }

    @Test
    fun testDisposeFailureWhenDownloadFailure() {
        mockkStatic(CustomToast::class)
        justRun { CustomToast.showShort(any() as Int) }
        val taskInfo = DownloadTaskInfo()
        val downloadResult = DownloadResult(false, DocumentDownloadErrorCode.DOWNLOAD_ERROR.value)
        val dispatcher = spyk(DocumentDownloadDispatcher(context, scope))
        justRun { dispatcher.notifyFailure(any(), any()) }
        dispatcher.setCancel(false)
        dispatcher.disposeFailure(taskInfo, downloadResult)
        verify(inverse = true) { CustomToast.showShort(any() as Int) }
        verify { dispatcher.notifyFailure(any(), any()) }
        unmockkStatic(CustomToast::class)
    }

    @Test
    fun testIsCancel() {
        val dispatcher = spyk(DocumentDownloadDispatcher(context, scope))
        Assert.assertFalse(dispatcher.isCancel())
        dispatcher.setCancel(true)
        Assert.assertTrue(dispatcher.isCancel())
    }

    @Test
    fun testSetCancel() {
        val dispatcher = spyk(DocumentDownloadDispatcher(context, scope))
        Assert.assertFalse(dispatcher.isCancel())
        dispatcher.setCancel(true)
        Assert.assertTrue(dispatcher.isCancel())
        dispatcher.setCancel(false)
        Assert.assertFalse(dispatcher.isCancel())
    }

    companion object {
        private const val FILE_ID = "300000000GKvJqeggQNng"
    }
}