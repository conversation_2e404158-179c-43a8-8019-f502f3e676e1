/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScriptExecHelperTest.kt
 * Description:
 * The test cases for ScriptExecHelper
 *
 * Version: 1.0
 * Date: 2024-05-30
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-05-30   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.request

import android.content.Context
import com.oplus.filemanager.simulateclick.engine.Script
import com.oplus.filemanager.simulateclick.engine.SimulateClickEngine
import com.oplus.filemanager.simulateclick.engine.Skill
import com.oplus.filemanager.simulateclick.engine.utils.MultiAppUtils
import com.oplus.filemanager.simulateclick.file.BuiltinScriptDetector
import com.oplus.filemanager.simulateclick.file.DebugScriptDetector
import com.oplus.filemanager.simulateclick.file.RemoteScriptDetector
import com.oplus.filemanager.simulateclick.file.ScriptFilePathUtils
import com.oplus.filemanager.simulateclick.sdk.OnScriptDetectedCallback
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkConstructor
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert
import org.junit.Test

class ScriptExecHelperTest : Assert() {

    @Test
    fun `should dealWithStep when executeScript if script is enabled`() {
        // SetUp
        mockkConstructor(SimulateClickEngine::class)
        mockkStatic(MultiAppUtils::class)

        // Given
        justRun { anyConstructed<SimulateClickEngine>().dealWithStep(any(), any(), any(), any()) }
        val callback = mockk<OnScriptDetectedCallback>(relaxed = true)
        val request = ScriptExecRequest(
            packageName = "test.pkg",
            skillName = "testSkill",
            replacedMap = mapOf("testArg" to "testValue"),
            detectedCallback = callback
        )
        every { MultiAppUtils.isChooseCloneApp("test.pkg") } returns false
        val script = mockk<Script>()
        val context = mockk<Context> {
            every { mainExecutor } returns mockk {
                every { execute(any()) } answers { firstArg<Runnable>().run() }
            }
        }
        val helper = spyk(ScriptExecHelper(context, mockk())) {
            every { readScript(request) } returns script
            every { script.checkScriptEnableStatus(request) } returns true
        }

        // When
        helper.executeScript(mockk(), request)
        helper.executeScript(mockk(), request)
        helper.executeScript(mockk(), request)

        // Then
        assertEquals(2, helper.executingCount)
        verify(exactly = 3) {
            anyConstructed<SimulateClickEngine>().dealWithStep(
                packageName = request.packageName,
                skillName = request.skillName,
                script = script,
                replacedMap = request.replacedMap
            )
            callback.onScriptDetected(true)
        }

        // TearDown
        unmockkConstructor(SimulateClickEngine::class)
        unmockkStatic(MultiAppUtils::class)
    }

    @Test
    fun `should not execute when executeScript if script is disabled`() {
        // SetUp
        mockkConstructor(SimulateClickEngine::class)
        mockkStatic(MultiAppUtils::class)

        // Given
        justRun { anyConstructed<SimulateClickEngine>().dealWithStep(any(), any(), any(), any()) }
        val callback = mockk<OnScriptDetectedCallback>(relaxed = true)
        val request = ScriptExecRequest(
            packageName = "test.pkg",
            skillName = "testSkill",
            replacedMap = mapOf("testArg" to "testValue"),
            detectedCallback = callback
        )
        every { MultiAppUtils.isChooseCloneApp("test.pkg") } returns false
        val script = mockk<Script>()
        val context = mockk<Context> {
            every { mainExecutor } returns mockk {
                every { execute(any()) } answers { firstArg<Runnable>().run() }
            }
        }
        val helper = spyk(ScriptExecHelper(context, mockk())) {
            every { readScript(request) } returns script
            every { script.checkScriptEnableStatus(request) } returns false
        }

        // When
        helper.executeScript(mockk(), request)

        // Then
        verify {
            callback.onScriptDetected(false)
        }
        verify(inverse = true) {
            anyConstructed<SimulateClickEngine>().dealWithStep(
                packageName = any(),
                skillName = any(),
                script = any(),
                replacedMap = any()
            )
        }

        // TearDown
        unmockkConstructor(SimulateClickEngine::class)
        unmockkStatic(MultiAppUtils::class)
    }

    @Test
    fun `should not execute when executeScript if is choose clone`() {
        // SetUp
        mockkConstructor(SimulateClickEngine::class)
        mockkStatic(MultiAppUtils::class)

        // Given
        justRun { anyConstructed<SimulateClickEngine>().dealWithStep(any(), any(), any(), any()) }
        val callback = mockk<OnScriptDetectedCallback>(relaxed = true)
        val request = ScriptExecRequest(
            packageName = "test.pkg",
            skillName = "testSkill",
            replacedMap = mapOf("testArg" to "testValue"),
            detectedCallback = callback
        )
        every { MultiAppUtils.isChooseCloneApp("test.pkg") } returns true
        val script = mockk<Script>()
        val context = mockk<Context> {
            every { mainExecutor } returns mockk {
                every { execute(any()) } answers { firstArg<Runnable>().run() }
            }
        }
        val helper = spyk(ScriptExecHelper(context, mockk())) {
            every { readScript(request) } returns script
            every { script.checkScriptEnableStatus(request) } returns true
        }

        // When
        helper.executeScript(mockk(), request)

        // Then
        verify {
            callback.onScriptDetected(false)
        }
        verify(inverse = true) {
            anyConstructed<SimulateClickEngine>().dealWithStep(
                packageName = any(),
                skillName = any(),
                script = any(),
                replacedMap = any()
            )
        }

        // TearDown
        unmockkConstructor(SimulateClickEngine::class)
        unmockkStatic(MultiAppUtils::class)
    }

    @Test
    fun `should count down when checkHasExecuting`() {
        // Given
        val helper = ScriptExecHelper(mockk(), mockk()).apply {
            executingCount = 2
        }

        // When
        val result1 = helper.run { checkHasExecuting() to executingCount }
        val result2 = helper.run { checkHasExecuting() to executingCount }

        // Then
        assertEquals(true to 1, result1)
        assertEquals(false to 0, result2)
    }

    @Test
    fun `should true when checkScriptEnableStatus if script and skill both enabled`() {
        // Given
        val testSkillName = "test_skill"
        val script = mockk<Script> {
            every { scriptEnable } returns true
            val skill = mockk<Skill> {
                every { skillName } returns testSkillName
                every { skillEnable } returns true
            }
            every { skills } returns listOf(skill)
        }
        val request = mockk<ScriptExecRequest> {
            every { skillName } returns testSkillName
        }

        // When
        val result = ScriptExecHelper(mockk(), mockk()).run {
            script.checkScriptEnableStatus(request)
        }

        // Then
        assertTrue(result)
    }

    @Test
    fun `should false when checkScriptEnableStatus if script disabled`() {
        // Given
        val testSkillName = "test_skill"
        val script = mockk<Script> {
            every { scriptEnable } returns false
            val skill = mockk<Skill> {
                every { skillName } returns testSkillName
                every { skillEnable } returns true
            }
            every { skills } returns listOf(skill)
        }
        val request = mockk<ScriptExecRequest> {
            every { skillName } returns testSkillName
        }

        // When
        val result = ScriptExecHelper(mockk(), mockk()).run {
            script.checkScriptEnableStatus(request)
        }

        // Then
        assertFalse(result)
    }

    @Test
    fun `should false when checkScriptEnableStatus if skill disabled`() {
        // Given
        val testSkillName = "test_skill"
        val script = mockk<Script> {
            every { scriptEnable } returns true
            val skill = mockk<Skill> {
                every { skillName } returns testSkillName
                every { skillEnable } returns false
            }
            every { skills } returns listOf(skill)
        }
        val request = mockk<ScriptExecRequest> {
            every { skillName } returns testSkillName
        }

        // When
        val result = ScriptExecHelper(mockk(), mockk()).run {
            script.checkScriptEnableStatus(request)
        }

        // Then
        assertFalse(result)
    }

    @Test
    fun `should return max ver script when readScript if detected by multi detectors include debug`() {
        // SetUp
        mockkStatic(ScriptFilePathUtils::isDebugPathEnabled)
        mockkConstructor(
            BuiltinScriptDetector::class,
            RemoteScriptDetector::class,
            DebugScriptDetector::class
        )

        // Given
        every { ScriptFilePathUtils.isDebugPathEnabled } returns true
        val script1 = Script(scriptVersion = 100)
        every {
            anyConstructed<BuiltinScriptDetector>().detectScript(any(), any(), any())
        } returns script1
        val script2 = Script(scriptVersion = 110)
        every {
            anyConstructed<RemoteScriptDetector>().detectScript(any(), any(), any())
        } returns script2
        val script3 = Script(scriptVersion = 120)
        every {
            anyConstructed<DebugScriptDetector>().detectScript(any(), any(), any())
        } returns script3
        val helper = ScriptExecHelper(mockk(), mockk())

        // When
        val result = helper.readScript(mockk(relaxed = true))

        // Then
        assertEquals(script3, result)

        // TearDown
        unmockkStatic(ScriptFilePathUtils::isDebugPathEnabled)
        unmockkConstructor(
            BuiltinScriptDetector::class,
            RemoteScriptDetector::class,
            DebugScriptDetector::class
        )
    }

    @Test
    fun `should return max ver script when readScript if detected by multi detectors without debug`() {
        // SetUp
        mockkStatic(ScriptFilePathUtils::isDebugPathEnabled)
        mockkConstructor(
            BuiltinScriptDetector::class,
            RemoteScriptDetector::class,
            DebugScriptDetector::class
        )

        // Given
        every { ScriptFilePathUtils.isDebugPathEnabled } returns false
        val script1 = Script(scriptVersion = 100)
        every {
            anyConstructed<BuiltinScriptDetector>().detectScript(any(), any(), any())
        } returns script1
        val script2 = Script(scriptVersion = 110)
        every {
            anyConstructed<RemoteScriptDetector>().detectScript(any(), any(), any())
        } returns script2
        val script3 = Script(scriptVersion = 120)
        every {
            anyConstructed<DebugScriptDetector>().detectScript(any(), any(), any())
        } returns script3
        val helper = ScriptExecHelper(mockk(), mockk())

        // When
        val result = helper.readScript(mockk(relaxed = true))

        // Then
        assertEquals(script2, result)

        // TearDown
        unmockkStatic(ScriptFilePathUtils::isDebugPathEnabled)
        unmockkConstructor(
            BuiltinScriptDetector::class,
            RemoteScriptDetector::class,
            DebugScriptDetector::class
        )
    }
}