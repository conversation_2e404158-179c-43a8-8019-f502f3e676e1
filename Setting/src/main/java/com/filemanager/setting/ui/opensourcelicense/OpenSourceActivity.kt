/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: OpenSourceActivity
 * * Description: the fragment for setting_pref_about
 * * Version: 1.0
 * * Date : 2021/2/8
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2021/2/8       1.0         the activity for open source statement
 ****************************************************************/
package com.filemanager.setting.ui.opensourcelicense

import android.app.Activity
import android.content.Intent
import android.view.MenuItem
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.WindowUtils
import com.filemanager.setting.R
import com.filemanager.setting.utils.FlexibleWindowUtil
import com.google.android.material.appbar.COUIDividerAppBarLayout
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.InputStreamReader

class OpenSourceActivity : BaseVMActivity() {

    companion object {
        private const val TAG = "OpenSourceActivity"

        fun start(activity: Activity?) {
            try {
                activity?.apply {
                    val intent = Intent(activity, OpenSourceActivity::class.java)
                    FlexibleWindowUtil.startActivityForFlexibleWindow(activity, intent)
                }
            } catch (e: Exception) {
                Log.w(TAG, "start: ${e.message}")
            }
        }
    }

    private var mStatement: TextView? = null
    private lateinit var mAppBarLayout: COUIDividerAppBarLayout
    private lateinit var mToolbar: COUIToolbar

    override fun getLayoutResId(): Int {
        return R.layout.open_source_statement
    }

    override fun initView() {
        registerVmChangedReceiver(null)
        mAppBarLayout = findViewById(R.id.appbar)
        mToolbar = findViewById(R.id.toolbar)
        mToolbar.title = resources.getString(com.filemanager.common.R.string.open_source_statement)
        mToolbar.isTitleCenterStyle = false
        setSupportActionBar(mToolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        initToolbar()
        FlexibleWindowUtil.setActionCloseFlexibleActivity(window.decorView, this)
        val titleView = findViewById<TextView>(R.id.title)
        titleView.text = "OPEN SOURCE SOFTWARE NOTICE"
        mStatement = findViewById(R.id.statement)
        FlexibleWindowUtil.setBarBackgroundColor(mToolbar, mAppBarLayout, window.decorView, resources.configuration, this)
    }

    private fun initToolbar() {
        var appBarTopPadding = StatusBarUtil.getStatusBarHeight()
        if (FeatureCompat.isApplicableForFlexibleWindow && WindowUtils.isMiddleAndLargeScreen(this)) {
            appBarTopPadding = MyApplication.sAppContext.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_16dp)
        }
        mAppBarLayout.setPadding(0, appBarTopPadding, 0, 0)
    }

    override fun startObserve() {}

    private fun getStatement() : String {
        val sb = StringBuffer("")
        resources.openRawResource(R.raw.open_source_statement).use { inputStream ->
            InputStreamReader(inputStream, "UTF-8").use {
                val reader = BufferedReader(it)
                var line: String?
                while (reader.readLine().also { line = it } != null) {
                    sb.append(line)
                    sb.append("\n")
                }
            }
        }
        return sb.toString()
    }

    override fun initData() {
        val vm = ViewModelProvider(this).get(BaseViewModel::class.java)
        vm.launch {
            val statement = withContext(Dispatchers.IO) {
                getStatement()
            }
            mStatement?.text = statement
        }
    }

    override fun refreshCurrentPage(action: String?, data: String?) {}

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        initToolbar()
        FlexibleWindowUtil.setBarBackgroundColor(mToolbar, mAppBarLayout, window.decorView, resources.configuration, this)
    }
}