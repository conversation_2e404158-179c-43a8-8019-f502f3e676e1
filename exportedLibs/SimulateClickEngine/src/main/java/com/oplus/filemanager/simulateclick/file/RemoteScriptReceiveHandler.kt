/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - RemoteScriptReceiveHandler.kt
 * Description:
 *     <PERSON><PERSON> receive remote scripts file and update local stored scripts
 *
 * Version: 1.0
 * Date: 2024-06-13
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-06-13   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.file

import android.content.Context
import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import com.oplus.filemanager.simulateclick.engine.utils.Log
import java.io.File

internal class RemoteScriptReceiveHandler(private val context: Context) : Handler.Callback {

    /**
     * To prevent modify stored script files at the same time in different threads.
     * Use same specified thread to handle update with cloud config file data and debug file data.
     */
    private val handlerThread = HandlerThread(THREAD_NAME).apply { start() }
    private val handler = Handler(handlerThread.looper, this@RemoteScriptReceiveHandler)
    private val store = RemoteScriptStore(context)
    private val completedReceiveMsg = mutableSetOf<Int>()

    fun triggerReceiveRemoteFile(remoteFile: File) {
        handler.removeMessages(MSG_FINISH_HANDLER)
        Log.d(TAG, "triggerReceiveRemoteFile: $remoteFile")
        val msg = Message.obtain(handler, MSG_EXEC_RECEIVE_REMOTE, remoteFile)
        msg.sendToTarget()
    }

    fun triggerReceiveDebugRemoteFile() {
        if (!ScriptFilePathUtils.isDebugPathEnabled) {
            return
        }
        handler.removeMessages(MSG_FINISH_HANDLER)
        val debugFile = ScriptFilePathUtils.getDebugRemoteFile(context)
        Log.d(TAG, "triggerReceiveDebugRemoteFile: $debugFile")
        val msg = Message.obtain(handler, MSG_EXEC_RECEIVE_DEBUG, debugFile)
        msg.sendToTarget()
    }

    fun triggerTimeoutFinish() {
        Log.d(TAG, "triggerTimeoutFinish")
        triggerFinish(RECEIVE_TIMEOUT)
    }

    private fun triggerFinish(delay: Long) {
        handler.removeMessages(MSG_FINISH_HANDLER)
        handler.sendEmptyMessageDelayed(MSG_FINISH_HANDLER, delay)
    }

    override fun handleMessage(msg: Message): Boolean {
        Log.d(TAG, "handleMessage: what=${msg.what}, obj=${msg.obj}")
        runCatching<Unit> {
            when (msg.what) {
                MSG_EXEC_RECEIVE_REMOTE, MSG_EXEC_RECEIVE_DEBUG -> {
                    (msg.obj as? File)?.takeIf {
                        it.exists() && it.isFile
                    }?.let { remoteFile ->
                        store.receiveRemoteScripts(remoteFile)
                    }
                    completedReceiveMsg.add(msg.what)
                    val completeCount = if (ScriptFilePathUtils.isDebugPathEnabled) 2 else 1
                    if (completedReceiveMsg.size >= completeCount) {
                        triggerFinish(0)
                    }
                }
                MSG_FINISH_HANDLER -> {
                    handler.removeCallbacksAndMessages(null)
                    handlerThread.quitSafely()
                }
            }
        }.onFailure {
            Log.e(TAG, "handleMessage: ERROR! what=${msg.what}, $it")
        }
        return true
    }

    private companion object {
        private const val TAG = "RemoteScriptReceiveHandler"
        private const val THREAD_NAME = "remote_simulate_click_update"
        private const val MSG_EXEC_RECEIVE_REMOTE = 2001
        private const val MSG_EXEC_RECEIVE_DEBUG = 2002
        private const val MSG_FINISH_HANDLER = 2003
        private const val RECEIVE_TIMEOUT = 60 * 1000L
    }
}