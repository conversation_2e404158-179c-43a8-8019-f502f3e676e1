/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentPageConfig
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/13 15:17
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/13       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.data

const val PAGE_NOT_AGREE_PRIVACY = "pages/not_agree_privacy"
const val PAGE_NO_PERMISSION = "pages/no_permission"
const val PAGE_NO_FILES = "pages/no_files"
const val PAGE_LOADING = "pages/loading"
const val PAGE_DATA = "pages/data"