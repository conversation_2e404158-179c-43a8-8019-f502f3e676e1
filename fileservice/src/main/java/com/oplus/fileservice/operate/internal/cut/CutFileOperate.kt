/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CutFileOperate
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/8 11:39
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/8       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.cut

import android.content.Context
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.checkDestStorageSpace
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.fileservice.operate.internal.ConflictPolicy
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.internal.NotifyMediaScannerBatchAction
import com.oplus.fileservice.operate.internal.copy.BaseCopyCutOperate
import com.oplus.fileservice.operate.internal.copy.FileCopyHelper
import com.oplus.fileservice.operate.internal.cut.FileCutHelper.isDirectoriesSameDisk
import com.oplus.fileservice.operate.internal.utils.OnFileOperateListener
import com.oplus.fileservice.operate.internal.utils.converterFileBean
import java.io.File

open class CutFileOperate(
    private val context: Context
) : BaseCopyCutOperate() {

    // In same volume just need to renameTo, else should copy+delete
    private var mIsSameVolume = false
    private var mSuccessDirListInSameVolume: MutableList<String>? = null

    private var mIsOperateDatabase: Boolean = true

    private var mTotalFileLength: Long = 0

    // Result Count
    private var mHasSkipFile = false
    private var mHasSuccessFile = false

    private val mMediaScannerBatchAction by lazy {
        NotifyMediaScannerBatchAction(Utils.MEDIA_SCAN_MOVE)
    }

    private val mediaScannerBatchActionDFS by lazy {
        NotifyMediaScannerBatchAction(Utils.MEDIA_SCAN_MOVE)
    }

    private var mFileOperateListener = object : OnFileOperateListener() {
        override var isCancel: () -> Boolean = {
            isCancelled()
        }

        override fun onSuccess(sourceFile: File, destFile: File) {
            onDealFileSuccess(sourceFile, destFile)
        }

        override fun onFailure(sourceFile: File, destFile: File): Boolean {
            return if (isCancelled()) {
                false
            } else {
                onDealFileError(sourceFile, destFile)
            }
        }
    }

    override fun exceptionDetection(sourceInfos: List<FileInfo>, targetInfo: FileInfo): Any? {
        mIsSameVolume = targetInfo.mData?.let { isDirectoriesSameDisk(sourceInfos, it) } ?: true
        var result = super.exceptionDetection(sourceInfos, targetInfo)
        if ((result == null) && !mIsSameVolume) {
            sourceInfos.forEach { fileInfo ->
                fileInfo.data?.let {
                    FileCutHelper.checkSizeAndInternalStorageImage(File(it)).let { length ->
                        mTotalFileLength += length.first
                    }
                }
            }
            Log.d(TAG, "exceptionDetection -> totalLength = $mTotalFileLength")

            if (mTotalFileLength < 0) {
                return ERROR_PATH_NULL
            }

            // Check the empty size of the dest storage
            val storageState = checkDestStorageSpace(targetInfo.converterFileBean(), mTotalFileLength)
            if (storageState.first) {
                Log.d(TAG, "exceptionDetection: storage is not enough")
                result = Pair(ERROR_STORAGE_NOT_ENOUGH, storageState.second)
            }
        }
        return result
    }

    override fun workRun(
        sourceInfos: List<FileInfo>,
        targetInfo: FileInfo,
        denyPolicy: ConflictPolicy
    ): OperationResult {
        mIsOperateDatabase = Utils.isOperateDatabase(context, targetInfo.mData)
        Log.d(TAG, "workRun -> mIsOperateDatabase = $mIsOperateDatabase ; isSameVolume = $mIsSameVolume")
        if (mIsSameVolume) {
            mSuccessDirListInSameVolume = arrayListOf()
        }
        return super.workRun(sourceInfos, targetInfo, denyPolicy)
    }

    override fun onSkipDealFile(sourceFile: File) {
        mHasSkipFile = true
    }

    override fun onDealFile(sourceFile: File, destFile: File): Boolean {
        Log.d(TAG, "onDealFile -> sourceFile = $sourceFile ; destFile =$destFile ; mIsSameVolume = $mIsSameVolume")
        var shouldScan = mIsOperateDatabase
        val result: Boolean
        if (mIsSameVolume) {
            result = FileCutHelper.cutFile(sourceFile, destFile) { path: String ->
                checkFileParentIsSuccess(path)
            }
            Log.d(TAG, "onDealFile -> result = $result")
            if (result) {
                onDealFileSuccess(sourceFile, destFile)
            } else {
                onDealFileError(sourceFile, destFile)
            }
            Log.d(TAG, "onDealFile -> shouldScan = $shouldScan")
            if (shouldScan) {
                shouldScan = destFile.exists()
            }
        } else {
            result = FileCopyHelper.copyFile(sourceFile, destFile, listener = mFileOperateListener)
            Log.d(TAG, "onDealFile -> notSameVolume result = $result")
        }
        Log.d(TAG, "onDealFile -> shouldScan = $shouldScan")
        if (shouldScan) {
            addScannerPath(destFile.absolutePath)
            addScannerPath(sourceFile.absolutePath)
        }
        return result
    }

    private fun addScannerPath(absolutePath: String) {
        if (absolutePath.startsWith(KtConstants.DFM_MOUNT_PATH_SUFFIX)) {
            Log.d(TAG, "addScannerPath dfs")
            mediaScannerBatchActionDFS.add(absolutePath)
        } else {
            mMediaScannerBatchAction.add(absolutePath)
        }
    }

    override fun onDealAllFilesEnd() {
        if (mIsOperateDatabase) {
            mMediaScannerBatchAction.flush()
            mediaScannerBatchActionDFS.flush()
        }
        mSuccessDirListInSameVolume?.clear()
    }

    private fun onDealFileSuccess(sourceFile: File, destFile: File) {
        mHasSuccessFile = true
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.onUpdateFilePath(sourceFile.absolutePath, destFile.absolutePath)
        if (mIsSameVolume) {
            if (destFile.isDirectory) {
                mSuccessDirListInSameVolume?.add(sourceFile.absolutePath.let {
                    if (it.endsWith(File.separator)) it else it.plus(File.separator)
                })
            }
        } else {
            val result = sourceFile.delete()
            Log.d(TAG, "onDealFileSuccess -> delete source file $result : ${sourceFile.absolutePath}")
        }
    }

    private fun checkFileParentIsSuccess(path: String): Boolean {
        mSuccessDirListInSameVolume?.forEach {
            if (path.startsWith(it)) {
                return true
            }
        }
        return false
    }

    companion object {
        private const val TAG = "CutFileOperate"
    }
}