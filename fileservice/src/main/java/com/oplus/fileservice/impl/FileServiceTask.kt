/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileServiceTask
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/13 9:27
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/13       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.impl

import com.oplus.fileservice.operate.convert.Response
import java.util.function.Supplier

abstract class FileServiceTask(
    val name: String,
    val cancelable: Boolean = false
) : Supplier<Response> {

    override fun get(): Response {
        return runInternal()
    }

    /**
     * Runnable internal execution method.
     */
    abstract fun runInternal(): Response

    fun cancel(immediately: Boolean) {
        if (cancelable) {
            cancelInternal(immediately)
        }
    }

    /**
     * Cancel the runnable.
     *
     * @param immediately Indicate whether to execute immediately
     */
    abstract fun cancelInternal(immediately: Boolean)
}