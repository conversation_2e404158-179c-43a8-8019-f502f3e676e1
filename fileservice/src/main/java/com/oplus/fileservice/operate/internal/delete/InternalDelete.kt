/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Delete
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 21:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/21       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.delete

import android.content.Context
import android.os.Environment
import android.provider.MediaStore
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.FileDeleteHelper
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTraceUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.recyclebin.retriever.DataRetriever
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.InvalidSourceFileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.OperateFailure
import com.oplus.fileservice.operate.convert.OperationResult

class InternalDelete(
    context: Context,
    isMediaType: Boolean = false
) : IDelete {

    private val mBatchAction: InnerOperation = if (isMediaType) {
        DeleteAction(
            context.applicationContext.contentResolver,
            MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL)
                .buildUpon()
                .appendQueryParameter(PARAM_DELETE_DATA, "false")
                .build(),
            MediaStore.Images.ImageColumns.DATA
        )
    } else {
        ScanAction()
    }
    private var mDeleteHelper: FileDeleteHelper? = null
    private var mColorOsSrcPath: String? = null
    private var mColorOsDestPath: String? = null

    @Suppress("TooGenericExceptionCaught")
    override fun delete(fileInfos: List<FileInfo>?): OperationResult {
        Log.d(TAG, "delete -> fileInfos = $fileInfos")
        val pathList = arrayListOf<String>()
        fileInfos?.forEach { fileInfo ->
            fileInfo.data?.let { pathList.add(it) }
        } ?: return OperationResult(false, InvalidSourceFileInfo)
        mDeleteHelper = FileDeleteHelper()
        if (mColorOsSrcPath == null) {
            mColorOsSrcPath = (Environment.getExternalStorageDirectory().absolutePath + "/ColorOS")
        }

        if (mColorOsDestPath == null) {
            mColorOsDestPath = (Environment.getExternalStorageDirectory().absolutePath + "/.ColorOS")
        }

        try {
            pathList.let {
                return deletePaths(it)
            }
        } catch (e: Exception) {
            Log.w(TAG, "delete -> failed = ${e.message}")
        }
        return OperationResult(false, OperateFailure)
    }

    private fun deletePaths(paths: ArrayList<String>): OperationResult {
        val files = ArrayList<BaseFileBean>()
        var file: BaseFileBean
        paths.forEach { path ->
            file = PathFileWrapper(path)
            files.add(file)
        }
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.onDeleteFilePaths(paths)
        return deleteFiles(files)
    }

    @Suppress("LongMethod")
    private fun deleteFiles(fileBeans: ArrayList<BaseFileBean>): OperationResult {
        var failedCount = 0
        var index = 0
        var traceState = false
        val totalSize = fileBeans.size
        var realDelete = true
        var statusCode = STATUS_ERROR

        for (fileBean in fileBeans) {
            val path = fileBean.mData

            val invalidFileWrapperViewType = fileBean.mFileWrapperViewType == BaseFileBean.TYPE_FILE_LIST_HEADER ||
                    fileBean.mFileWrapperViewType == BaseFileBean.TYPE_LABEL_FILE

            if (path.isNullOrEmpty() || invalidFileWrapperViewType) {
                index++
                failedCount++
                continue
            }

            if (!mBatchAction.mHasDir && fileBean.mIsDirectory) {
                mBatchAction.mHasDir = true
            }
            val type = fileBean.mLocalType
            if ((path == mColorOsSrcPath) && (mColorOsDestPath != null)) {
                val destFile = PathFileWrapper(mColorOsDestPath!!)
                val state = JavaFileHelper.renameTo(fileBean, mColorOsDestPath!!)
                if (state) {
                    realDelete = mDeleteHelper?.delete(destFile) ?: true
                    FileTraceUtil.getInstance().traceAction(FileTraceUtil.TraceAction.MARK_DELETE, type, path)
                }
            } else {
                realDelete = mDeleteHelper?.delete(fileBean) ?: true
                if (!realDelete) {
                    // check if or not is Restricted Directory
                    if (DataRetriever.isRestrictedDirectoryR(path) || DataRetriever.isAndroidDirectoryR(path)) {
                        statusCode = STATUS_PROTECTED_DIR
                    }
                } else {
                    if (type == MimeTypeHelper.IMAGE_TYPE) {
                        traceState = true
                    }
                    FileTraceUtil.getInstance().traceAction(
                        FileTraceUtil.TraceAction.MARK_DELETE,
                        type, path
                    )
                }
            }
            if (!realDelete) {
                index++
                failedCount++
                continue
            }
            mBatchAction.add(path)
            index++
        }

        mBatchAction.flush()
        if (traceState) {
            FileTraceUtil.getInstance().traceAtThisMoment()
        }

        failedCount += (totalSize - index)
        val status = when {
            statusCode == STATUS_PROTECTED_DIR -> statusCode
            failedCount > 0 -> STATUS_ERROR
            else -> STATUS_SUCCESS
        }
        return if (status == STATUS_SUCCESS) {
            OperationResult(true, OperateStatusCode.OK)
        } else {
            OperationResult(false, OperateFailure)
        }
    }

    companion object {
        private const val TAG = "Delete"
        private const val PARAM_DELETE_DATA = "deletedata"

        /**
         * status code for all operation in recycleBin,if statusCode is Positive, represent failed count
         */
        // status code for success
        private const val STATUS_SUCCESS = 0

        // status code for common exception
        private const val STATUS_ERROR = -1

        // status code for protected directory exception
        private const val STATUS_PROTECTED_DIR = -3
    }
}