/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecycleBinScanner
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/23 10:15
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/23       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.scanner

import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.base.FileLoader
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.RecycleFileWrapper
import com.oplus.fileservice.bean.WebFileBean
import com.oplus.fileservice.bean.WebScannerResult
import com.oplus.fileservice.fileServiceScope
import com.oplus.fileservice.filelist.FileSelectPathService
import com.oplus.fileservice.filelist.loader.WebRecycleBinLoader
import com.oplus.fileservice.filelist.utils.getDateAndTimeByDefaultFormat
import com.oplus.fileservice.utils.WebFileConstant
import com.oplus.fileservice.utils.WebFileHelper
import com.oplus.fileservice.utils.WebMimeTypeHelper
import com.oplus.fileservice.utils.WebThumbCacheManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeoutOrNull
import org.apache.commons.io.FilenameUtils

class RecycleBinScanner(pageNo: Int, pageSize: Int, sortOrderType: Int, private var isNeedThumbnail: Boolean = true) : BaseUriScanner() {
    companion object {
        const val TAG = "RecycleBinScanner"
    }

    private val mLoaderController = LoaderController()
    private var mPageNo = pageNo
    private var mPageSize = pageSize
    private var mTotal = 0
    private var mSortOrderType = sortOrderType
    override fun scannerFiles(): WebScannerResult? {
        mLoaderController.initLoader(TAG.hashCode(), ServiceRecycleBinCallBack())
        return null
    }


    inner class ServiceRecycleBinCallBack :
        OnLoaderListener<MutableList<RecycleFileWrapper>> {
        override fun onCreateLoader(): FileLoader<MutableList<RecycleFileWrapper>>? {
            val recycleBinLoader = WebRecycleBinLoader(MyApplication.sAppContext)
            recycleBinLoader.setSort(mSortOrderType)
            return recycleBinLoader
        }

        override fun onLoadComplete(result: MutableList<RecycleFileWrapper>?) {
            var webPathFileBeans = ArrayList<WebFileBean>()
            if (result == null) {
                mTotal = 0
                mPageNo = 1
                mScannerCallback?.scannerSuc(WebScannerResult(mPageNo, mTotal, webPathFileBeans))
            } else {
                val recycleFileWrappers = pageData(result)
                webPathFileBeans = transformRecycleFileWrapperToWebFileBeans(recycleFileWrappers)
                fileServiceScope.launch(Dispatchers.IO) {
                    decorateResultWebFiles(webPathFileBeans)
                    mScannerCallback?.scannerSuc(WebScannerResult(mPageNo, mTotal, webPathFileBeans))
                }
            }
        }
    }

    @VisibleForTesting
    fun pageData(result: List<RecycleFileWrapper>): List<RecycleFileWrapper> {
        mTotal = result.size
        if (mPageSize < 1) {
            mPageSize = WebFileConstant.DEFAULT_PAGE_SIZE
            Log.d(TAG, "Service modify mPageSize $mPageSize")
        }
        var pageTotal = mTotal / mPageSize
        if (mTotal % mPageSize > 0) {
            pageTotal++
        }

        if (mPageNo > pageTotal) {
            Log.d(FileSelectPathService.TAG, "mPageNo > pageTotal is true, mPageNo = $mPageNo")
            mPageNo = pageTotal
        }
        if (mPageNo < 1) {
            mPageNo = 1
        }
        val start = mPageSize * (mPageNo - 1)
        val end = if (mPageSize * mPageNo > mTotal) {
            mTotal
        } else {
            mPageSize * mPageNo
        }

        return result.subList(start, end)
    }

    private fun transformRecycleFileWrapperToWebFileBeans(recycleFileWrappers: List<RecycleFileWrapper>): ArrayList<WebFileBean> {
        val resultWebFileBean = ArrayList<WebFileBean>()
        for (recycleFile in recycleFileWrappers) {
            recycleFile.apply {
                val webFileBean = WebFileBean()
                webFileBean.mFilePath = mRecyclePath
                webFileBean.mFileId = mRecycleId?.toInt()
                webFileBean.mFileSize = mSize
                webFileBean.mFileFormat = FileTypeUtils.getExtension(mDisplayName)
                webFileBean.mFileType = if (mLocalType == MimeTypeHelper.DIRECTORY_TYPE) {
                    webFileBean.mFileFormat = ""
                    webFileBean.mFileName = mDisplayName
                    WebMimeTypeHelper.DIRECTORY_TYPE
                } else {
                    webFileBean.mFileName = FilenameUtils.getBaseName(mDisplayName)
                    WebMimeTypeHelper.getFileTypeFromExtension(
                        webFileBean.mFileFormat
                    )
                }
                webFileBean.mLocalType = mLocalType
                webFileBean.mLastModifyDate = getDateAndTimeByDefaultFormat(mRecycelDate)
                webFileBean.mDateModified = mRecycelDate
                resultWebFileBean.add(webFileBean)
            }
        }
        Log.d(TAG, "transformRecycleFileWrapperToWebFileBeans finish()")
        return resultWebFileBean
    }

    private suspend fun decorateResultWebFiles(resultWebFileBean: ArrayList<WebFileBean>) = runBlocking {
        val thumbnailsWebBeans = resultWebFileBean.filter {
            isNeedThumbnail && ((it.mLocalType == MimeTypeHelper.APPLICATION_TYPE) ||
                    (it.mLocalType == MimeTypeHelper.IMAGE_TYPE) ||
                    (it.mLocalType == MimeTypeHelper.VIDEO_TYPE))
        }

        resultWebFileBean.filter {
            it.mFileType == WebMimeTypeHelper.DIRECTORY_TYPE
        }.map {
            fileServiceScope.async(Dispatchers.IO) {
                val waitFolderIem = withTimeoutOrNull(WebFileConstant.WAIT_TIME_OUT) {
                    it.mFolderItem = WebFileHelper.listFilesCount(it.mFilePath, false)
                    true
                }
                Log.d(TAG, "${it.mFileFormat} get Files mFolderItem $waitFolderIem")
            }
        }.plus(
            async(Dispatchers.IO) {
                WebThumbCacheManager.setThumbnailData(thumbnailsWebBeans)
            }
        ).awaitAll()
    }
}