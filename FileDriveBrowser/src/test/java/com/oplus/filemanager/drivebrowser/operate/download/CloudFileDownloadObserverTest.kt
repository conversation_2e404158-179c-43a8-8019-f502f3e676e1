/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : CloudFileDownloadObserverTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/2/27 10:56
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/2/27       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.operate.download

import android.app.Activity
import android.content.Context
import com.filemanager.common.utils.COUISnackBarUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.fileoperate.base.ACTION_CANCELLED
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class CloudFileDownloadObserverTest {

    private lateinit var context: Context

    @Before
    fun setup() {
        context = mockk()
    }

    @After
    fun teardown() {
        unmockkAll()
    }

    @Test
    fun testOnChanged() {
        mockkStatic(CustomToast::class)
        justRun { CustomToast.showShort(any() as Int) }

        mockkObject(COUISnackBarUtils)
        justRun { COUISnackBarUtils.show(any(), any() as Int, any()) }
        val activity = mockk<Activity>()
        val observer = CloudFileDownloadObserver(activity)
        val failureResult = Pair(ACTION_FAILED, DOWNLOAD_ERROR)
        observer.onChanged(context, failureResult)
        verify { CustomToast.showShort(any() as Int) }

        val fileErrorResult = Pair(ACTION_FAILED, DOWNLOAD_FILE_ERROR)
        observer.onChanged(context, fileErrorResult)
        verify { CustomToast.showShort(any() as Int) }

        val doneResult = Pair(ACTION_DONE, "")
        observer.onChanged(activity, doneResult)
        verify { COUISnackBarUtils.show(any(), any() as Int, any()) }

        val doneContextResult = Pair(ACTION_DONE, 1)
        observer.onChanged(activity, doneContextResult)
        verify { CustomToast.showShort(any() as Int) }

        val otherResult = Pair(ACTION_CANCELLED, "")
        val result = observer.onChanged(context, otherResult)
        Assert.assertFalse(result)

        unmockkObject(COUISnackBarUtils)
        unmockkStatic(CustomToast::class)
    }
}