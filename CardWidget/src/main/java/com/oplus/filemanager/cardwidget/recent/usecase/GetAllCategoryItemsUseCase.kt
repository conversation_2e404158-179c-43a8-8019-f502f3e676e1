/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GetAllCategoryItemsUseCase
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/18 19:09
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/18       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.usecase

import com.filemanager.common.helper.CategoryHelper
import com.oplus.filemanager.cardwidget.R
import com.oplus.filemanager.cardwidget.recent.data.RecentPreviewItem
import com.filemanager.common.utils.stringResource

class GetAllCategoryItemsUseCase {
    operator fun invoke(): ArrayList<RecentPreviewItem> {
        val allItems = arrayListOf<RecentPreviewItem>()
        allItems.add(
            RecentPreviewItem(
                type = CategoryHelper.CATEGORY_RECENT,
                name = stringResource(com.filemanager.common.R.string.recent_file_card_select_range_all_item)
            )
        )
        val items = arrayListOf<RecentPreviewItem>()
        items.addAll(allItems)
        items.addAll(GetCategoryItemUseCase().invoke())
        items.addAll(GetSuperAppItemUseCase().invoke())
        return items
    }
}