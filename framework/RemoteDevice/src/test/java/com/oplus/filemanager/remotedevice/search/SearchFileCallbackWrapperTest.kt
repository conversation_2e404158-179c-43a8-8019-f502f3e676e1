package com.oplus.filemanager.remotedevice.search

import com.filemanager.common.bean.remotedevice.RemoteFileData
import com.filemanager.common.utils.Log
import com.oplus.filemanager.remotedevice.ResultConstant
import com.oplus.filemanager.remotedevice.util.mapRemoteFileInfo
import com.oplus.remotecontrol.remotecontrolsdk.RemoteControlServiceManager
import com.oplus.remotecontrol.remotecontrolsdk.bean.RemoteFileInfoInternal
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.util.concurrent.TimeUnit
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * SearchFileCallbackWrapper的单元测试类
 * 用于测试远程文件搜索回调包装器的功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(maxSdk = 33)  // 添加此注解解决targetSdkVersion问题
class SearchFileCallbackWrapperTest {

    // 测试对象
    private lateinit var callbackWrapper: SearchFileCallbackWrapper
    // 模拟的远程控制服务管理器
    private lateinit var mockRemoteMgr: RemoteControlServiceManager
    // 测试用设备ID
    private val testDeviceId = "testDevice123"
    // 测试用关键词
    private val testKeyword = "test"
    // 测试用页码
    private val testPageNum = 1
    // 测试用每页大小
    private val testPageSize = 10

    /**
     * 测试前的初始化方法
     * 1. 模拟Log类
     * 2. 创建模拟的RemoteControlServiceManager
     * 3. 初始化测试对象
     */
    @Before
    fun setUp() {
        // 模拟Log类的静态方法
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } just Runs
        every { Log.e(any(), any(), any()) } just Runs

        // 创建放松的模拟对象
        mockRemoteMgr = mockk(relaxed = true)
        // 初始化测试对象
        callbackWrapper = SearchFileCallbackWrapper()
    }

    /**
     * 测试后的清理方法
     * 1. 解除所有模拟
     * 2. 清除所有mock
     */
    @After
    fun tearDown() {
        unmockkAll()
        clearAllMocks()
    }

    /**
     * 测试当远程管理器为null时的搜索行为
     * 预期结果: 返回空列表
     */
    @Test
    fun `test search with null remote manager should return empty list`() {
        val result = callbackWrapper.search(null, testDeviceId, testKeyword, testPageNum, testPageSize)
        assertTrue(result.isEmpty())
    }

    /**
     * 测试使用有效参数调用搜索方法
     * 验证是否正确地调用了远程管理器的搜索方法
     */
    @Test
    fun `test search with valid parameters should call remote manager`() {
        // 设置模拟行为
        every { mockRemoteMgr.searchKeyWordFileList(any(), any(), any(), any(), any()) } just Runs

        // 执行测试方法
        callbackWrapper.search(mockRemoteMgr, testDeviceId, testKeyword, testPageNum, testPageSize)

        // 验证是否调用了远程管理器的搜索方法
        verify {
            mockRemoteMgr.searchKeyWordFileList(
                testDeviceId,
                testKeyword,
                testPageSize,
                testPageNum,
                callbackWrapper
            )
        }
    }

    /**
     * 测试当返回失败码时的onResult回调
     * 预期结果: 结果列表应为空
     */
    @Test
    fun `test onResult with failure code should return empty list`() {
        // 创建模拟的文件信息对象
        val mockFileInfo = mockk<RemoteFileInfoInternal>()

        // 调用回调方法，传入失败码
        callbackWrapper.onResult(ResultConstant.RESULT_CODE_SUCCESS + 1, listOf(mockFileInfo))

        // 验证结果列表是否为空
        assertTrue(callbackWrapper.getResultListForTest().isEmpty())
    }

    /**
     * 测试当结果没有立即返回时的等待行为
     * 验证方法是否会等待超时时间
     */
    @Test
    fun `test search should wait for result when not returned immediately`() {
        // 设置模拟行为
        every { mockRemoteMgr.searchKeyWordFileList(any(), any(), any(), any(), any()) } just Runs

        // 记录开始时间
        val startTime = System.currentTimeMillis()
        // 执行测试方法
        val result = callbackWrapper.search(mockRemoteMgr, testDeviceId, testKeyword, testPageNum, testPageSize)
        // 记录结束时间
        val endTime = System.currentTimeMillis()

        // 验证结果为空
        assertTrue(result.isEmpty())
        // 验证等待时间是否接近超时时间(允许100ms误差)
        assertTrue(endTime - startTime >= SearchFileCallbackWrapper.WAIT_TIME_OUT - 100)
    }

    /**
     * 辅助方法：用于测试访问私有字段resultList
     * @return 返回测试对象内部的resultList
     */
    private fun SearchFileCallbackWrapper.getResultListForTest(): List<RemoteFileData> {
        // 通过反射获取私有字段
        val field = this::class.java.getDeclaredField("resultList")
        field.isAccessible = true
        return field.get(this) as List<RemoteFileData>
    }
}