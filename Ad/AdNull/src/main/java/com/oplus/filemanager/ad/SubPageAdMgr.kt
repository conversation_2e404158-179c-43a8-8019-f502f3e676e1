/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: SubPageAdMgr.kt
 ** Description: subFragment ad request manager
 ** Version: 5.0
 ** Date : 2021/1/25
 ** Author: HuiHua Teng
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * HuiHua.Teng 2021/01/25     5.0    modify
 ****************************************************************/
package com.oplus.filemanager.ad

import android.app.Activity
import android.content.Context
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper

@Suppress("FunctionOnlyReturningConstant", "UnusedPrivateMember")
class SubPageAdMgr(lifecycle: Lifecycle) : AdvertManager() {

    companion object {
        fun isSupportDocAd(context: Context): Boolean = false
        fun isSupportApkAd(context: Context): Boolean = false
        fun isHideApkUninstallAd(context: Context): Boolean = false
        fun isSupportAudioAd(context: Context): Boolean = false
        fun isSupportAlbumSetAd(context: Context): Boolean = false
        fun isSupportPhotoStorageAd(context: Context): Boolean = false
    }
    fun makeName(name: String): String {
        return name
    }

    fun requestSubAd(
        activity: Activity,
        name: String,
        adapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>,
        fileList: java.util.ArrayList<MediaFileWrapper>,
        needHideAD: Boolean
    ) {
        Log.d("requestSubAd activity is $activity name is  $name   adapter is $adapter  fileList is  $fileList and hideAD is $needHideAD")
    }

    fun scanModeChange(name: String, needHideAD: Boolean) {
        Log.d("scanModeChange name is $name hideAD is $needHideAD")
    }

    fun requestCommonAd(
        activity: FragmentActivity,
        adapter: BaseSelectionRecycleAdapter<*, MediaFileWrapper>,
        fileList: java.util.ArrayList<MediaFileWrapper>
    ) {
        Log.d("requestCommonAd activity is $activity adapter is $adapter fileList is $fileList")
    }

    fun requestSubAlbumSetAd(activity: Activity, adapter: BaseSelectionRecycleAdapter<*, AlbumItem>, fileList: java.util.ArrayList<AlbumItem>) {
        Log.d("requestCommonAd activity is $activity adapter is $adapter fileList is $fileList")
    }

    fun requestPhoneStorageAd(
        activity: Activity,
        adapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>,
        fileList: java.util.ArrayList<BaseFileBean>
    ) {
        Log.d("requestCommonAd activity is $activity adapter is $adapter fileList is $fileList")
    }

    fun refreshIfListChanged() {
        Log.d("refreshIfListChanged")
    }

    fun refreshByScanModeChanged() {
        Log.d("refreshByScanModeChanged")
    }
}