package com.filemanager.common.view

import android.content.Context
import android.graphics.Color
import android.text.SpannableString
import android.text.Spanned
import android.view.ViewGroup
import androidx.test.platform.app.InstrumentationRegistry
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * TextViewSnippet的单元测试类
 * 用于测试TextViewSnippet类的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])  // 将SDK版本从28改为29以解决错误
class TextViewSnippetTest {

    private lateinit var context: Context
    private lateinit var textViewSnippet: TextViewSnippet

    /**
     * 测试前的初始化方法
     * 1. 获取测试上下文
     * 2. 创建TextViewSnippet实例
     * 3. 模拟MyApplication单例
     */
    @Before
    fun setUp() {
        context = InstrumentationRegistry.getInstrumentation().targetContext
        textViewSnippet = TextViewSnippet(context)
        mockkObject(MyApplication)
        every { MyApplication.appContext } returns context
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试设置高亮颜色的功能
     * 验证设置的高亮颜色是否正确应用到TextViewSnippet实例
     */
    @Test
    fun testSetHighLightColor() {
        val color = Color.RED
        textViewSnippet.setHighLightColor(color)
        val field = TextViewSnippet::class.java.getDeclaredField("mHighlightColor")
        field.isAccessible = true
        assertEquals(color, field.get(textViewSnippet) as Int)
    }

    /**
     * 测试设置文本时搜索文本为空的情况
     * 验证当搜索文本为空时，TextViewSnippet会直接显示完整文本
     */
    @Test
    fun testSetTextWithEmptySearchText() {
        textViewSnippet.setText("test", "")
        assertEquals("test", textViewSnippet.text.toString())
    }

    /**
     * 测试设置文本时完整文本为空的情况
     * 验证当完整文本为空时，TextViewSnippet的文本内容也为空
     */
    @Test
    fun testSetTextWithEmptyFullText() {
        textViewSnippet.setText("", "search")
        assertTrue(textViewSnippet.text.isNullOrEmpty())
    }

    /**
     * 测试更新文本时输入为空的情况
     * 验证当mFullText和mSearchText都为空时，TextViewSnippet的文本内容保持为空
     */
    @Test
    fun testUpdateTextWithEmptyInputs() {
        val fullTextField = TextViewSnippet::class.java.getDeclaredField("mFullText")
        fullTextField.isAccessible = true
        fullTextField.set(textViewSnippet, null)
        
        val searchTextField = TextViewSnippet::class.java.getDeclaredField("mSearchText")
        searchTextField.isAccessible = true
        searchTextField.set(textViewSnippet, null)
        
        val updateTextMethod = TextViewSnippet::class.java.getDeclaredMethod("updateText", Boolean::class.java)
        updateTextMethod.isAccessible = true
        updateTextMethod.invoke(textViewSnippet, true)
        
        assertTrue(textViewSnippet.text.isNullOrEmpty())
    }
}