/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileServiceImpl
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/29 10:59
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/6/29       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.impl

import android.content.Context
import com.filemanager.common.utils.Log
import com.oplus.fileservice.FileServiceManager
import com.oplus.fileservice.operate.OperateUpdateTask
import com.oplus.fileservice.operate.convert.OperateRequest
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.convert.Request
import com.oplus.fileservice.operate.convert.Response
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.internal.cut.CutFileOperate
import com.oplus.fileservice.operate.internal.rename.RenameFileOperate
import com.oplus.fileservice.operate.reply.OperateReplyTask
import com.oplus.fileservice.utils.taskexecutor.FileServiceTaskExecutor
import java.util.concurrent.CompletableFuture
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.ThreadFactory
import java.util.concurrent.atomic.AtomicInteger
import java.util.function.Consumer

class FileServiceManagerImpl : FileServiceManager() {

    private var mFileServiceTaskExecutor: FileServiceTaskExecutor? = null

    private var mExecutorServiceOrNull: ExecutorService? = null

    private val mFutures: ConcurrentHashMap<String, CompletableFuture<*>> = ConcurrentHashMap()
    private val mTasks: ConcurrentHashMap<String, FileServiceTask> = ConcurrentHashMap()

    @get:Synchronized
    val executorService: ExecutorService
        get() {
            if (mExecutorServiceOrNull == null) {
                mExecutorServiceOrNull = createDefaultExecutor()
            }
            return mExecutorServiceOrNull!!
        }

    init {
        initTaskExecutor()
    }

    override fun executeOnBackground(serviceRunnable: Runnable?) {
        if (serviceRunnable != null) {
            mFileServiceTaskExecutor?.executeOnBackgroundThread(serviceRunnable)
        }
    }

    /**
     * Enqueue auto-reply request.
     *
     * [OperateUpdateTask] provide operation result as supplier,
     * and [OperateReplyTask] as the consumer consumes the operation result and sends it to the caller.
     */
    override fun enqueueAutoReplyRequest(context: Context, request: Request, stop: () -> Unit) {
        Log.d(TAG, "enqueueCancellableRequest -> request = $request")
        val requestId = request.requestId
        if (requestId.isNullOrEmpty()) {
            Log.d(TAG, "enqueueAutoReplyRequest -> request is null or empty.")
            return
        }
        if (request is OperateRequest) {
            val operateTask = OperateUpdateTask(context, request)
            val operateReplyTask = OperateReplyTask(context, requestId, request)
            supplyTaskWithConsumer(requestId, operateTask, operateReplyTask, stop)
        }
    }

    /**
     * Execute operation task asynchronously by [CompletableFuture.supplyAsync],
     * and link the operation task and reply result task through by [CompletableFuture.thenAcceptAsync].
     *
     * When the execution is completed, dequeue the task and determine whether all requests have been completed
     */
    private fun supplyTaskWithConsumer(
        requestId: String,
        task: FileServiceTask,
        replyTask: Consumer<Response>,
        stop: () -> Unit
    ) {
        val future = CompletableFuture.supplyAsync(task, executorService)
            .exceptionally {
                Log.e(TAG, "supplyTaskWithConsumer -> error = $it")
                OperationResult(false)
            }
            .thenAcceptAsync(replyTask, executorService)
            .whenComplete { _, _ ->
                dequeueAndCheckComplete(requestId, stop)
            }
        queueRequest(requestId, task, future)
        Log.d(TAG, "supplyTaskWithCustom -> Future Size = ${mFutures.size} ; Task Size = ${mTasks.size}")
    }

    override fun runAsync(runnable: FileServiceRunnable) {
        CompletableFuture.runAsync(runnable, executorService).whenComplete { _, t ->
            Log.d(TAG, "Async run ${runnable.name} complete.")
            if (t != null) {
                Log.w(TAG, "Async run has error = ${t.message}")
            }
        }
    }

    override fun cancelUniqueRequest(context: Context, requestId: String, stop: () -> Unit) {
        Log.d(TAG, "cancelUniqueRequest -> requestId = $requestId ; Futures Size = ${mFutures.size}")
        if (!mFutures.containsKey(requestId)) {
            stop.invoke()
            return
        }
        val future = mFutures[requestId]
        Log.d(TAG, "cancelUniqueRequest -> future isCancelled = ${future?.isCancelled} ; isDone = ${future?.isDone}")
        if (future?.isCancelled == true || future?.isDone == true) {
            stop.invoke()
            return
        }
        synchronized(mTasks) {
            val task = mTasks[requestId]
            task?.let {
                if (it.cancelable) {
                    it.cancel(true)
                }
            }
            stop.invoke()
        }
    }

    override fun renameTo(context: Context, sourceFileInfo: FileInfo, targetFileInfo: FileInfo): Int {
        val renameFileOperate = RenameFileOperate(context)
        val operateRequest = OperateRequest()
        operateRequest.targetFileInfo = targetFileInfo
        operateRequest.sourceFileInfo = listOf(sourceFileInfo)
        return renameFileOperate.run(operateRequest).statusCode.value
    }

    override fun cutTo(context: Context, sourceFileInfos: List<FileInfo>, targetFileInfo: FileInfo): Int {
        val cutFileOperate = CutFileOperate(context)
        val operateRequest = OperateRequest()
        operateRequest.targetFileInfo = targetFileInfo
        operateRequest.sourceFileInfo = sourceFileInfos
        return cutFileOperate.run(operateRequest).statusCode.value
    }

    @Synchronized
    private fun queueRequest(requestId: String, task: FileServiceTask, future: CompletableFuture<*>) {
        Log.d(TAG, "queueRequest -> requestId = $requestId")
        mFutures[requestId] = future
        mTasks[requestId] = task
    }

    @Suppress("TooGenericExceptionCaught")
    @Synchronized
    private fun dequeueAndCheckComplete(requestId: String, stop: () -> Unit) {
        Log.d(TAG, "dequeueAndCheckComplete -> Future Size = ${mFutures.size}")
        mFutures.remove(requestId)
        mTasks.remove(requestId)
        if (mFutures.isEmpty()) {
            Log.d(TAG, "No operations in progress, stop internal perform service.")
            try {
                stop.invoke()
            } catch (e: Exception) {
                Log.e(TAG, "dequeueAndCheckComplete -> error = $e")
            }
        }
    }

    private fun initTaskExecutor() {
        if (mFileServiceTaskExecutor == null) {
            mFileServiceTaskExecutor = FileServiceTaskExecutor(executorService)
        }
    }

    private fun createDefaultExecutor(): ExecutorService {
        return Executors.newFixedThreadPool(
            2.coerceAtLeast((Runtime.getRuntime().availableProcessors() - 1).coerceAtMost(MAX_POOL)),
            createDefaultThreadFactory()
        )
    }

    private fun createDefaultThreadFactory(): ThreadFactory {
        return object : ThreadFactory {
            private val mThreadCount = AtomicInteger(0)
            override fun newThread(runnable: Runnable): Thread {
                return Thread(runnable, PREFIX + mThreadCount.incrementAndGet())
            }
        }
    }

    private object Holder {
        val instance = FileServiceManagerImpl()
    }

    companion object {
        private const val TAG = "FileServiceImpl"
        private const val PREFIX = "file-service-"
        private const val MAX_POOL = 4

        fun getInstance(): FileServiceManagerImpl = Holder.instance
    }
}