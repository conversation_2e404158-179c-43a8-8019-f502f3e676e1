/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: SecondaryTitleBehavior
 * * Description: SecondaryTitleBehavior
 * * Version: 1.0
 * * Date : 2024/03/01
 * * Author:ztf
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *        ztf           2024/03/01      1.0            create
 ****************************************************************/
package com.filemanager.setting.ui.behavior

import android.content.Context
import android.os.Build
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.AbsListView
import androidx.annotation.Keep
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.setting.R
import com.google.android.material.appbar.AppBarLayout

@Keep
class SecondaryTitleBehavior(context: Context, attrs: AttributeSet? = null) :
    CoordinatorLayout.Behavior<AppBarLayout>(context, attrs), AbsListView.OnScrollListener {

    private var mContext: Context
    private var mChild: View? = null
    private var mToolbar: COUIToolbar? = null
    private var mScrollView: ViewGroup? = null
    private var mFirstView: View? = null

    // Divider line
    private var mDividerLine: View? = null
    private var mDividerAlphaChangeOffset = 0
    private var mDividerWidthChangeOffset = 0
    private var mDividerHeight = 0
    private var mDividerLineMargin = 0
    private var mTargetViewInitY = 0
    private var mDividerSlideBoundary = 0
    private val mListViewLocation = IntArray(2)
    private var mDividerParam: AppBarLayout.LayoutParams? = null

    init {
        val resources = context.resources
        mContext = context
        mDividerLineMargin = context.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.common_margin)
        mDividerWidthChangeOffset =
            resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.line_width_range_count_height)
        mDividerAlphaChangeOffset =
            resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.line_alpha_range_change_offset)
        mDividerHeight = resources.getDimensionPixelSize(com.support.toolbar.R.dimen.toolbar_divider_height)
        mDividerSlideBoundary = resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_8dp)
    }

    override fun onStartNestedScroll(
        parent: CoordinatorLayout,
        child: AppBarLayout,
        directTargetChild: View,
        target: View,
        nestedScrollAxes: Int,
        type: Int
    ): Boolean {
        initBehavior(child, target)
        return false
    }

    private fun initBehavior(appbarLayout: AppBarLayout, target: View) {
        mScrollView = target as? ViewGroup
        if (mToolbar == null) {
            mToolbar = appbarLayout.findViewById(R.id.toolbar)
            mDividerLine = appbarLayout.findViewById(R.id.divider_line)
            mDividerParam = mDividerLine?.layoutParams as? AppBarLayout.LayoutParams
            mTargetViewInitY = (appbarLayout.measuredHeight - mDividerHeight
                    + mContext.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_12dp))

            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    target.setOnScrollChangeListener { _, _, _, _, _ ->
                        onListScroll()
                    }
                }

                target is AbsListView -> target.setOnScrollListener(this)

                target is COUIRecyclerView -> {
                    target.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                            onListScroll()
                        }
                    })
                }
            }
        }
    }

    override fun onScrollStateChanged(absListView: AbsListView, i: Int) {}

    override fun onScroll(absListView: AbsListView, i: Int, i1: Int, i2: Int) {
        onListScroll()
    }

    private fun onListScroll() {
        mChild = null
        if (mFirstView == null && (mScrollView?.childCount ?: 0) > 0) {
            for (i in 0 until (mScrollView?.childCount ?: 0)) {
                if (mScrollView?.getChildAt(i)?.visibility == View.VISIBLE) {
                    mChild = mScrollView?.getChildAt(i)
                    break
                }
            }
        }

        if (mChild == null) {
            mChild = mScrollView
        }

        if (mFirstView == null) {
            mFirstView = mChild
        }

        if (mFirstView?.visibility == View.VISIBLE) {
            mFirstView?.getLocationInWindow(mListViewLocation)

            val diff = mTargetViewInitY - mListViewLocation[1]
            if (diff <= mDividerSlideBoundary) {
                updateView(0F)
            } else {
                updateView(diff.toFloat())
            }
        }
    }

    private fun updateView(offset: Float) {
        val dividerAlphaRatio = getDividerAlphaRatio(offset)
        val dividerWidthRatio = getDividerWidthRatio(offset)

        mDividerLine?.let { dividerLine ->
            mDividerParam?.let {
                it.setMargins(
                    (mDividerLineMargin * (1 - dividerWidthRatio)).toInt(), it.topMargin,
                    (mDividerLineMargin * (1 - dividerWidthRatio)).toInt(), it.bottomMargin
                )
            }
            dividerLine.apply {
                layoutParams = mDividerParam
                alpha = dividerAlphaRatio
            }
        }
    }

    private fun getDividerAlphaRatio(currentOffset: Float): Float {
        // range from 0 to 10
        val ratio = currentOffset / mDividerAlphaChangeOffset
        return when {
            ratio > 1f -> 1f
            ratio < 0f -> 0f
            else -> ratio
        }
    }

    private fun getDividerWidthRatio(currentOffset: Float): Float {
        // range from 10 to 35
        val ratio = (currentOffset - mDividerAlphaChangeOffset) / mDividerWidthChangeOffset
        return when {
            ratio > 1f -> 1f
            ratio < 0f -> 0f
            else -> ratio
        }
    }
}