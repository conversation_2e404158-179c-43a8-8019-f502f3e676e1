/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileActionCreateDir.kt
 ** Description: Create directory function
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.createdir

import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.rename.FileActionRename
import com.filemanager.fileoperate.rename.FileRenameObserver.FileRenameBean.Companion.TYPE_CREATE_FOLDER
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.fileservice.IFileService.Companion.OPERATE_TYPE_CREATE_DIR
import java.io.File

class FileActionCreateDir(lifecycle: LifecycleOwner, file: BaseFileBean) :
    FileActionRename(lifecycle, file) {

    companion object {
        private const val TAG = "FileActionCreateDir"
    }

    override fun initFileRenameBean() {
        super.initFileRenameBean()
        mFileRenameBean?.type = TYPE_CREATE_FOLDER
    }

    override fun reallyExecuteAction(file: BaseFileBean, newFileName: String): Boolean {
        Log.d(TAG, "reallyExecuteAction: file=$newFileName, path=${file.mData}")
        if (file.mData.isNullOrEmpty().not()) {
            file.mData = file.mData.plus(File.separator + newFileName)
        }
        Log.d(TAG, "reallyExecuteAction: mkdir start")
        val result = JavaFileHelper.mkdir(file)
        Log.d(TAG, "reallyExecuteAction: mkdir end, result: $result")
        return result
    }

    override fun afterRun(result: Boolean) {
        Log.d(TAG, "Create dir: $result")
        if (result) {
            if (mOperateFile.mData.isNullOrEmpty().not()) {
                FileMediaHelper.insertFileMediaDB(mContext, mOperateFile.mData!!, Utils.MEDIA_SCAN_CREATE_FOLDER)
                val fileServiceAction = Injector.injectFactory<IFileService>()
                val operateFile = File(mOperateFile.mData!!)
                fileServiceAction?.syncOperate(OPERATE_TYPE_CREATE_DIR, hashSetOf(operateFile.parent))
            }
            notifyObserver(ACTION_DONE, mOperateFile.mData)
        } else {
            notifyObserver(ACTION_FAILED)
        }
    }
}