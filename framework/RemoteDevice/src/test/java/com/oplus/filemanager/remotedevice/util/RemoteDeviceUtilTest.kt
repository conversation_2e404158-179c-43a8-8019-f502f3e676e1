package com.oplus.filemanager.remotedevice.util

import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo
import com.filemanager.common.bean.remotedevice.RemoteFileData
import com.filemanager.common.constants.RemoteDeviceConstants
import com.oplus.filemanager.interfaze.remotedevice.IRemoteFileLoadCallback
import com.oplus.filemanager.remotedevice.RemoteDeviceManager
import com.oplus.remotecontrol.remotecontrolsdk.bean.RemoteFileInfoInternal
import com.oplus.remotecontrol.remotecontrolsdk.constants.ResultConstant
import com.oplus.remotecontrol.remotecontrolsdk.db.DeviceInfo
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * RemoteDeviceUtil工具类的单元测试类
 * 用于测试RemoteDeviceUtil中的各种映射方法
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RemoteDeviceUtilTest {

    /**
     * 在每个测试方法执行前初始化
     * 1. 对RemoteDeviceManager进行mock
     * 2. 清空连接状态缓存
     */
    @Before
    fun setup() {
        mockkObject(RemoteDeviceManager)
        RemoteDeviceManager.connectingStatus.clear()
    }

    /**
     * 在每个测试方法执行后清理
     * 解除所有mock对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试mapRemoteDeviceInfoList方法在设备处于连接状态时的行为
     * 验证当设备状态为DISCOVERED且连接状态为true时，映射后的设备状态应为CONNECTING
     */
    @Test
    fun `test mapRemoteDeviceInfoList with connecting status`() {
        // Given 准备测试数据
        val deviceId = "testDeviceId"
        val deviceInfo = DeviceInfo(
            deviceId = deviceId,
            name = "testDevice",
            icon = null,
            deviceType = 1,
            deviceCode = "testCode",
            remoteDeviceId = null,
            sameAccount = 1
        ).apply {
            deviceFileState = RemoteDeviceConstants.DISCOVERED
        }
        // 模拟连接状态为true
        every { RemoteDeviceManager.connectingStatus[deviceId] } returns true

        // When 执行映射方法
        val result = mapRemoteDeviceInfoList(listOf(deviceInfo))

        // Then 验证结果
        assertEquals(1, result.size)
        assertEquals(deviceId, result[0].deviceId)
        assertEquals("testDevice", result[0].deviceName)
        assertEquals(RemoteDeviceConstants.CONNECTING, result[0].deviceStatus)
        verify { RemoteDeviceManager.connectingStatus[deviceId] }
    }

    /**
     * 测试mapRemoteDeviceInfoList方法在设备不处于连接状态时的行为
     * 验证当设备状态为DISCOVERED且连接状态为false时，映射后的设备状态应为DISCOVERED
     */
    @Test
    fun `test mapRemoteDeviceInfoList with not connecting status`() {
        // Given
        val deviceId = "testDeviceId"
        val deviceInfo = DeviceInfo(
            deviceId = deviceId,
            name = "testDevice",
            icon = null,
            deviceType = 1,
            deviceCode = "testCode",
            remoteDeviceId = null,
            sameAccount = 1
        ).apply {
            deviceFileState = RemoteDeviceConstants.DISCOVERED
        }
        // 模拟连接状态为false
        every { RemoteDeviceManager.connectingStatus[deviceId] } returns false
        every { RemoteDeviceManager.connectingStatus.put(deviceId, false) } returns false

        // When
        val result = mapRemoteDeviceInfoList(listOf(deviceInfo))

        // Then
        assertEquals(1, result.size)
        assertEquals(RemoteDeviceConstants.DISCOVERED, result[0].deviceStatus)
        verify { RemoteDeviceManager.connectingStatus[deviceId] }
    }

    /**
     * 测试mapRemoteDeviceInfoList方法在设备ID为null时的行为
     * 验证当设备ID为null时，映射后的设备状态应为DISCOVERED
     */
    @Test
    fun `test mapRemoteDeviceInfoList with null deviceId`() {
        // Given
        val deviceInfo = DeviceInfo(
            deviceId = null,
            name = "testDevice",
            icon = null,
            deviceType = 1,
            deviceCode = "testCode",
            remoteDeviceId = null,
            sameAccount = 1
        ).apply {
            deviceFileState = RemoteDeviceConstants.DISCOVERED
        }

        // When
        val result = mapRemoteDeviceInfoList(listOf(deviceInfo))

        // Then
        assertEquals(1, result.size)
        assertEquals(RemoteDeviceConstants.DISCOVERED, result[0].deviceStatus)
    }

    /**
     * 测试mapRemoteFileInfo方法在有效数据情况下的映射行为
     * 验证所有文件属性是否正确映射
     */
    @Test
    fun `test mapRemoteFileInfo with valid data`() {
        // Given 准备测试文件数据
        val remoteFileInfo = RemoteFileInfoInternal().apply {
            id = "file1"
            name = "test.txt"
            path = "/path/to/file"
            alias = "alias"
            size = 1024L
            creationDate = 123456789L
            modificationDate = 123456790L
            lastOpenedDate = 123456791L
            isDirectory = false
            fileNum = 0
            width = 100
            height = 200
            videoDuration = 300f
            audioChannels = 2
        }

        // When 执行映射方法
        val result = mapRemoteFileInfo(listOf(remoteFileInfo))

        // Then 验证所有属性映射正确
        assertEquals(1, result.size)
        assertEquals("file1", result[0].remoteId)
        assertEquals("test.txt", result[0].fileName)
        assertEquals("/path/to/file", result[0].path)
        assertEquals("alias", result[0].alternativeName)
        assertEquals(1024L, result[0].size)
        assertEquals(123456789L, result[0].createDate)
        assertEquals(123456790L, result[0].modifyDate)
        assertEquals(123456791L, result[0].lastOpenDate)
        assertEquals(false, result[0].isDir)
        assertEquals(100, result[0].remoteImageWidth)
        assertEquals(200, result[0].remoteImageHeight)
        assertEquals(300L, result[0].remoteVideoDuration)
        assertEquals(2, result[0].remoteAudioChannel)
    }

    /**
     * 测试mapRemoteFileInfo方法在目录情况下的映射行为
     * 验证目录相关属性是否正确映射
     */
    @Test
    fun `test mapRemoteFileInfo with directory`() {
        // Given
        val remoteFileInfo = RemoteFileInfoInternal().apply {
            isDirectory = true
            fileNum = 5
            id = "dir1"
            name = "testDir"
        }

        // When
        val result = mapRemoteFileInfo(listOf(remoteFileInfo))

        // Then
        assertEquals(1, result.size)
        assertEquals(true, result[0].isDir)
        assertEquals(5, result[0].fileNum)
        assertEquals("dir1", result[0].remoteId)
        assertEquals("testDir", result[0].fileName)
    }

    /**
     * 测试mapRemoteFileInfoErrorCode方法在所有错误码情况下的映射行为
     * 验证SDK错误码到应用错误码的转换是否正确
     */
    @Test
    fun `test mapRemoteFileInfoErrorCode with all cases`() {
        // 成功情况
        assertEquals(IRemoteFileLoadCallback.ERROR_CODE_SUC, mapRemoteFileInfoErrorCode(ResultConstant.RESULT_CODE_SUCCESS))
        
        // 各种失败情况
        assertEquals(IRemoteFileLoadCallback.ERROR_CODE_FAILED_NORMAL, mapRemoteFileInfoErrorCode(ResultConstant.RESULT_CODE_FAIL_NORMAL))
        assertEquals(IRemoteFileLoadCallback.ERROR_CODE_FAILED_A, mapRemoteFileInfoErrorCode(ResultConstant.RESULT_CODE_FAIL_A))
        assertEquals(IRemoteFileLoadCallback.ERROR_CODE_CANCEL, mapRemoteFileInfoErrorCode(ResultConstant.RESULT_CODE_CANCEL))
        assertEquals(IRemoteFileLoadCallback.ERROR_CODE_PASSWORD_ERROR, mapRemoteFileInfoErrorCode(ResultConstant.SERVER_REMOTE_PASSWORD_ERROR))
        assertEquals(IRemoteFileLoadCallback.ERROR_CODE_CONNECTED_OTHER_PC, mapRemoteFileInfoErrorCode(ResultConstant.PC_CLIENT_OCCUPIED))
        assertEquals(IRemoteFileLoadCallback.ERROR_LOCAL_CONNECTING, mapRemoteFileInfoErrorCode(ResultConstant.LOCAL_CONNECTING))
        assertEquals(IRemoteFileLoadCallback.NOT_SUPPORT_FILE, mapRemoteFileInfoErrorCode(ResultConstant.NOT_SUPPORT_FILE))
        
        // 默认情况
        assertEquals(IRemoteFileLoadCallback.ERROR_CODE_FAILED_NORMAL, mapRemoteFileInfoErrorCode(999))
    }
}