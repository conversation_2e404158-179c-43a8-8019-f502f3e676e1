<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <permission
        android:name="com.coloros.filemanager.permission.ACS_SERVICE"
        android:protectionLevel="signatureOrSystem" />

    <uses-permission android:name="com.coloros.filemanager.permission.ACS_SERVICE" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:node="replace" />

    <application android:networkSecurityConfig="@xml/network_security_config">
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3740773242095046~5504993456" />
        <receiver
            android:name="CheckRusChangedReceiver"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE">
            <intent-filter>
                <action android:name="oppo.intent.action.ROM_UPDATE_CONFIG_SUCCESS" />
            </intent-filter>
        </receiver>
    </application>
</manifest>