/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - DragDropScanner.kt
 ** Description: scanner for dragdrop share
 ** Version: 1.0
 ** Date : 2020/05/27
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/27    1.0     create
 ****************************************************************/

package com.oplus.dropdrag.dragdrop

import android.content.ClipData
import android.content.Context
import android.graphics.drawable.Drawable
import android.os.AsyncTask
import androidx.annotation.Keep
import com.oplus.dropdrag.DragDropSdkManager
import com.oplus.dropdrag.utils.Log

/**
 * data class for drag scan result
 * @param statusCode scan status code
 * @param ClipData all item in clip data
 * @param coverIcon cover icon to draw on drag layout
 * @param folderName file name to draw on drag layout
 * @param itemCount total items cout to send
 * @param details file details to draw on drag layout
 */
@Keep
data class DragScanResult(
    val statusCode: Int,
    val clipData: ClipData?,
    val coverIcon: Drawable?,
    val folderName: String?,
    val itemCount: Int?,
    val details: String?
)

/**
 * Interface definition for a callback to be invoked when drag scan finished
 */
@Keep
interface DragScannerListener {
    /**
     * Callback method to be invoked when drag scan finished
     * @param DragScanResult result of drag scan
     */
    fun onDragScanFinished(result: DragScanResult?)
}

@Keep
abstract class DragDropScanner<T> : AsyncTask<Void, Integer, DragScanResult> {
    private companion object {
        private const val TAG = "DragDropScanner"
    }

    protected var mScannerListener: DragScannerListener? = null
    protected var mDataList: ArrayList<T>? = null

    constructor(context: Context, scannerListener: DragScannerListener?) : super() {
        DragDropSdkManager.init(context)
        mScannerListener = scannerListener
    }

    fun addData(dataList: List<T>?): Boolean {
        if (mDataList != null) {
            Log.w(TAG, "addPaths already add a task")
            return false
        }

        if (!dataList.isNullOrEmpty()) {
            mDataList = ArrayList<T>()
            mDataList?.addAll(dataList)
            return true
        }

        Log.d(TAG, "addData INVALID PARAMETER")
        return false
    }

    override fun doInBackground(vararg params: Void?): DragScanResult? {
        return mDataList?.let { scanData() }
    }

    abstract fun scanData(): DragScanResult?

    override fun onCancelled() {
        mScannerListener = null
    }

    override fun onPostExecute(result: DragScanResult?) {
        mScannerListener?.onDragScanFinished(result)
        mScannerListener = null
    }
}