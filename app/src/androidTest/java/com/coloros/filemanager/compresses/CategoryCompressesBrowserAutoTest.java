/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * File        :  -
 * * Description : com.coloros.filemanager
 * * Version     : 1.0
 * * Date        : 2020/11/13
 * * Author      : W9006071
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.compresses;

import android.os.SystemClock;

import androidx.activity.ComponentActivity;
import androidx.test.filters.SmallTest;
import androidx.test.rule.ActivityTestRule;

import com.coloros.filemanager.utils.TestUtils;
import com.filemanager.categorycompress.ui.CategoryCompressActivity;
import com.filemanager.common.base.BaseFileBean;
import com.filemanager.common.wrapper.PathFileWrapper;
import com.filemanager.recyclebin.RecycleFileManager;
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;

public class CategoryCompressesBrowserAutoTest {
    
    private final static String FOLDER_NAME = "/sdcard/SDET_TEST";
    private RecycleFileManager.RecycleFileParameters recycleFileParameters = null;
    
    @Rule
    public ActivityTestRule mCategoryCompressesBrowser =
            new ActivityTestRule<>(CategoryCompressActivity.class, false, false);
    
    @Before
    public void setUp() {
    }
    
    @After
    public void tearDown() {
        recycleFileParameters = null;
    }
    
    /**
     * story id 195333
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_zip_success_when_delete_with_Activity() {
        mCategoryCompressesBrowser.launchActivity(null);
        ComponentActivity mActivity = (ComponentActivity) mCategoryCompressesBrowser.getActivity();
        File createFolder = new File(FOLDER_NAME);
        TestUtils.createFolder(FOLDER_NAME);
        if (!createFolder.exists()) {
            Assert.fail("create Folder Failed");
        }
        String fileZip = FOLDER_NAME + "/FileManager_0079_0434.jar";
        File zipFile = new File(fileZip);
        TestUtils.createZip(zipFile);
        if (!zipFile.exists()) {
            Assert.fail("create jar Failed");
        }
        ArrayList<BaseFileBean> folder = new ArrayList<>();
        PathFileWrapper pathFileWrapper = new PathFileWrapper(fileZip);
        folder.add(pathFileWrapper);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);
        mActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(mActivity, folder, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(500);
        if (zipFile.exists()) {
            Assert.fail("FileManager_0079_0434.jar Delete Failed");
        }
    }

    /**
     * story id 118334
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_jar_success_when_delete_with_Activity() {
        mCategoryCompressesBrowser.launchActivity(null);
        ComponentActivity mActivity = (ComponentActivity) mCategoryCompressesBrowser.getActivity();
        File createFolder = new File(FOLDER_NAME);
        TestUtils.createFolder(FOLDER_NAME);
        if (!createFolder.exists()) {
            Assert.fail("create Folder Failed");
        }
        String fileZip = FOLDER_NAME + "/FileManager_0079_0434.jar";
        File zipFile = new File(fileZip);
        TestUtils.createZip(zipFile);
        if (!zipFile.exists()) {
            Assert.fail("create jar Failed");
        }
        ArrayList<BaseFileBean> folder = new ArrayList<>();
        PathFileWrapper pathFileWrapper = new PathFileWrapper(fileZip);
        folder.add(pathFileWrapper);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);

        mActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(mActivity, folder, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(500);
        if (zipFile.exists()) {
            Assert.fail("FileManager_0079_0434.jar Delete Failed");
        }
    }

    /**
     * story id 192218
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_rar_success_when_delete_with_Activity() {
        mCategoryCompressesBrowser.launchActivity(null);
        ComponentActivity mActivity = (ComponentActivity) mCategoryCompressesBrowser.getActivity();
        File createFolder = new File(FOLDER_NAME);
        TestUtils.createFolder(FOLDER_NAME);
        if (!createFolder.exists()) {
            Assert.fail("create Folder Failed");
        }
        String fileZip = FOLDER_NAME + "/FileManager_0079_0433.rar";
        File zipFile = new File(fileZip);
        TestUtils.createZip(zipFile);
        SystemClock.sleep(500);
        if (!zipFile.exists()) {
            Assert.fail("create Zip Failed");
        }
        ArrayList<BaseFileBean> folder = new ArrayList<>();
        PathFileWrapper pathFileWrapper = new PathFileWrapper(fileZip);
        folder.add(pathFileWrapper);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);

        mActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(mActivity, folder, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(500);
        if (zipFile.exists()) {
            Assert.fail("FileManager_0079_0433.rar Delete Failed");
        }
    }

    /**
     * story id 123049
     */
    @SmallTest
    @Test
    @CasePrioritization("B")
    public void should_delete_zip1_success_when_delete_with_Activity() {
        mCategoryCompressesBrowser.launchActivity(null);
        ComponentActivity mActivity = (ComponentActivity) mCategoryCompressesBrowser.getActivity();
        File createFolder = new File(FOLDER_NAME);
        TestUtils.createFolder(FOLDER_NAME);
        if (!createFolder.exists()) {
            Assert.fail("create Folder Failed");
        }
        String fileZip = FOLDER_NAME + "/FileManager_0079_0432.zip";
        File zipFile = new File(fileZip);
        TestUtils.createZip(zipFile);
        if (!zipFile.exists()) {
            Assert.fail("create Zip Failed");
        }
        ArrayList<BaseFileBean> folder = new ArrayList<>();
        PathFileWrapper pathFileWrapper = new PathFileWrapper(fileZip);
        folder.add(pathFileWrapper);
        recycleFileParameters = new RecycleFileManager.RecycleFileParameters(false, 0, 0, false, true);

        mActivity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RecycleFileManager.Companion.getINSTANCE().recycleFiles(mActivity, folder, null, recycleFileParameters);
            }
        });
        SystemClock.sleep(500);
        if (zipFile.exists()) {
            Assert.fail("FileManager_0079_0432.zip Delete Failed");
        }
    }
}
