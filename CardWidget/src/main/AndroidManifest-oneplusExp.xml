<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools"
    package="com.oplus.filemanager.cardwidget">

    <application>
        <!-- appcard -->
        <meta-data android:name="com.oplus.ocs.card.AUTH_CODE"
            android:value="ADBGAiEAr54Xt9mbIP066z/cvLKbaOwioY06VN0VGRZx4Ql3Mr4CIQDRPRj3wODJXFw6vPltj+SEeyjn1xzvQAO5Wn3HtGFbU2vdbnk="
            tools:replace="android:value" />

        <activity android:name=".activity.OpenDeeplinkActivity"
            android:autoRemoveFromRecents="true"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize|keyboard|navigation"
            android:excludeFromRecents="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- filemanager://deeplink.remotedevice.download
                跳转到DownloadActivity
                参数另外传递-->
                <data
                    android:scheme="filemanager"
                    android:host="deeplink.remotedevice.download"
                />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- filemanager://deeplink.filebrowser.open
                    跳转到FileBrowserActivity
                    参数另外传递,-->
                <data
                    android:scheme="filemanager"
                    android:host="deeplink.filebrowser" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- filemanager://deeplink.super.remotepc
                    跳转到远程电脑文件 SuperActivity
                    参数另外传递,-->
                <data
                    android:scheme="filemanager"
                    android:host="deeplink.super.remotepc"
                />
            </intent-filter>

        </activity>

        <provider
            android:name=".download.DownloadSeedlingCardProvider"
            android:authorities="com.oplus.filemanager.transfer.download"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="com.oplus.seedling.action.SEEDLING_CARD" />
            </intent-filter>
            <!--             注意：如果upk存在App的assets目录中，需要按照以下格式，配置meta-data标签, assets目录下UPK文件路径，数组，多个用;分割 -->
            <meta-data
                android:name="oplus.seedling.provider"
                android:value="TransferSeedling-test-1.0.1-20250108.upk" />
        </provider>

    </application>

</manifest>