/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - DefaultDragListener.kt
 ** Description: Default Drag Listener
 ** Version: 1.0
 ** Date : 2020/05/28
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/28    1.0     create
 ****************************************************************/

package com.filemanager.common.dragselection

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.drawable.Drawable
import android.view.DragEvent
import android.view.MotionEvent
import android.view.View
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.CommonConstants.DRAG_FROM_FILE_MANAGER
import com.filemanager.common.constants.CommonConstants.MAX_SEND_COUNT
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.ERROR_CONTEXT_INVALID
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.ERROR_DRM_FILE
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.ERROR_REACH_MAX_COUNT
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatisticsUtils.DRAG_TO_OTHER_APP
import com.filemanager.common.utils.StatisticsUtils.DRAG_TO_OTHER_APP_COUNT
import com.filemanager.common.view.AlertDialogFactory
import com.filemanager.common.view.SelectDragItemViewParent
import com.filemanager.common.view.SelectDragItemViewParent.Companion.DRAG_ITEM_OPACITY
import com.oplus.dropdrag.dragdrop.DragScanResult
import com.oplus.dropdrag.dragdrop.DragScannerListener
import java.lang.ref.WeakReference

open class DefaultDragListener(
    private val activity: Activity,
    view: View,
    private val dragHoldDownFile: BaseFileBean?,
    private val dragHoldDrawable: Drawable?,
    private val fragmentCategoryType: Int,
    private val event: MotionEvent? = null,
    private val viewMode: com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE?
) : DragScannerListener {
    companion object {
        private const val TAG = "DefaultDragListener"
    }

    @VisibleForTesting
    val mWeakActivity: WeakReference<Activity> = WeakReference(activity)
    @VisibleForTesting
    val mWeakView: WeakReference<View> = WeakReference(view)
    var dragOutSide = true
    var selectedViewList: MutableLiveData<ArrayList<View>> = MutableLiveData()
    var shaderBuilder: NewFileDragDropShadow? = null
    var rootView: SelectDragItemViewParent? = null
    var isDragging = false

    open fun addSelectedView(list: ArrayList<View>?): DefaultDragListener {
        list?.let { list ->
            val visibleList = list.filter {
                val visible = KtViewUtils.isViewVisible(it)
                if (!visible) {
                    it.alpha = DRAG_ITEM_OPACITY
                }
                visible
            }.toCollection(ArrayList())
            Log.d(TAG, "buildAnimationLayout visibleList:${visibleList?.size}")
            if (visibleList.size > 0) {
                selectedViewList.value = visibleList
            } else {
                selectedViewList.value = list
            }
            selectedViewList.value?.let { list ->
                DefaultDropListener.setSelectedItemViewList(list) }
            viewMode?.let { viewMode -> DefaultDropListener.viewMode = viewMode }
        }
        return this@DefaultDragListener
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onDragScanFinished(result: DragScanResult?) {
        Log.d(TAG, "onDragScanFinished statusCode ${result?.statusCode}")
        result?.let {
            val activity = mWeakActivity.get() ?: return
            if (activity.isDestroyed || activity.isFinishing) {
                DragUtils.destorySelectedFiles()
                Log.d(TAG, "onDragScanFinished activity INVALID")
                return
            }
            Log.d(TAG, "onDragScanFinished  state is ${it.statusCode}")
            when (it.statusCode) {
                ERROR_DRM_FILE -> {
                    CustomToast.showShort(R.string.string_drm_unable_to_send)
                    DragUtils.destorySelectedFiles()
                    return
                }
                ERROR_REACH_MAX_COUNT -> {
                    val maxCountTips = activity.resources.getQuantityString(R.plurals.drag_send_beyond_count, MAX_SEND_COUNT, MAX_SEND_COUNT)
                    CustomToast.showShort(maxCountTips)
                    DragUtils.destorySelectedFiles()
                    return
                }
                ERROR_CONTEXT_INVALID -> {
                    AlertDialogFactory.showSingleTitleDialog(activity, R.string.toast_send_file_error)
                    DragUtils.destorySelectedFiles()
                    return
                }
                else -> {
                }
            }

            val bundle = result.clipData?.description?.extras
            val folderCount = bundle?.getInt(CommonConstants.KEY_FOLDER_PATH_SIZE) ?: 0
            Log.d(TAG, "folderCount $folderCount result.itemCount ${result.itemCount}")
            val allCount = (result.itemCount ?: 0) + folderCount
            if (allCount <= 0) {
                Log.d(TAG, "onDragScanFinished RESULT INVALID")
                AlertDialogFactory.showSingleTitleDialog(activity, R.string.toast_send_file_error)
                return
            }

            val view = mWeakView.get() ?: return

            shaderBuilder = selectedViewList.value?.let { viewList -> DragUtils.createShadowBuild(activity, it.statusCode, view, viewList) }
            DragUtils.createShadowView(view)

            var flag = View.DRAG_FLAG_GLOBAL or View.DRAG_FLAG_OPAQUE
            flag = flag or (View.DRAG_FLAG_GLOBAL_URI_READ
                    or View.DRAG_FLAG_GLOBAL_URI_WRITE)
            shaderBuilder?.let { shaderBuilder ->
                updateShaderUI(shaderBuilder, it, activity)
            }
            val dragTag = if (fragmentCategoryType != -1) {
                CommonConstants.DRAG_FROM_FRAGMENT + fragmentCategoryType
            } else {
                DRAG_FROM_FILE_MANAGER
            }
            DragUtils.dragTag = dragTag
            shaderBuilder?.setShadowVisible(false)
            selectedViewList.value?.let { list -> shaderBuilder?.addShadowView(list) }
            activity.window.decorView.startDragAndDrop(it.clipData, shaderBuilder, dragTag, flag)
            StatisticsUtils.onCommon(
                activity,
                DRAG_TO_OTHER_APP,
                mapOf(DRAG_TO_OTHER_APP_COUNT to allCount.toString())
            )
        }
    }

    open fun updateShaderUI(shaderBuilder: NewFileDragDropShadow, result: DragScanResult, context: Context) {
        val bundle = result.clipData?.description?.extras
        val folderCount = bundle?.getInt(CommonConstants.KEY_FOLDER_PATH_SIZE) ?: 0
        val allCount = (result.itemCount ?: 0) + folderCount
        shaderBuilder.updateShadowRedDot(allCount)
    }

    @VisibleForTesting
    fun actionDragEnded(e: DragEvent): Boolean {
        Log.d(TAG, "actionDragEnded dragOutSide:$dragOutSide")
        if (dragOutSide) {
            dragOutSide = false
            val r = e.result
            Log.d(TAG, "actionDragEnded r:$r")
            if (!r) {
                CustomToast.showShort(R.string.drag_unable_share_to_app)
            }
        }
        return true
    }

    @VisibleForTesting
    fun actionDragEntered(): Boolean {
        dragOutSide = false
        return true
    }

    @VisibleForTesting
    fun actionDragExited(): Boolean {
        dragOutSide = true
        return true
    }
}