/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentRecyclerExpandBaseAdapterTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/17
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/17       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.recent.ui

import android.app.Activity
import android.content.Context
import android.content.res.Resources
import com.filemanager.common.MyApplication
import com.oplus.filemanager.recent.ui.RecentRecyclerExpandBaseAdapter
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class RecentRecyclerExpandBaseAdapterTest {

    private lateinit var context: Context

    @MockK
    lateinit var res: Resources
    @MockK
    lateinit var activity: Activity

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        activity = mockk(relaxed = true)
        MyApplication.init(context)
    }

    @Test
    fun testListExpandItemCount() {
        val resources = mockk<Resources>()
        every { resources.getBoolean(any()) } returns false
        every { context.resources } returns resources
        RecentRecyclerExpandBaseAdapter.getListExpandItemCount(activity)
        Assert.assertEquals(8, RecentRecyclerExpandBaseAdapter.LIST_EXPAND_ITEM_COUNT)

       /* // is tablet is false, return default value
        val resources = mockk<Resources>()
        every { resources.getBoolean(any()) } returns false
        every { context.resources } returns resources
        RecentRecyclerExpandBaseAdapter.getListExpandItemCount(context)
        Assert.assertEquals(8, RecentRecyclerExpandBaseAdapter.LIST_EXPAND_ITEM_COUNT)

        // is table is true, and device is portrait

        mockkObject(UIConfigMonitor.instance)
        every { UIConfigMonitor.instance.isDevicePortrait(context) } returns true
        every { resources.getBoolean(any()) } returns true
        RecentRecyclerExpandBaseAdapter.getListExpandItemCount(context)
        Assert.assertEquals(6, RecentRecyclerExpandBaseAdapter.LIST_EXPAND_ITEM_COUNT)

        val activity = mockk<Activity>()
        val activityResources = mockk<Resources>()
        every { activityResources.getBoolean(any()) } returns true
        every { activity.resources } returns activityResources
        every { UIConfigMonitor.instance.isDevicePortrait(activity) } returns false
        every { UIConfigMonitor.instance.getLandscapeUiMode(activity) }
            .returns(UIConfigMonitor.LandscapeUiMode.MultiWindow46)
        RecentRecyclerExpandBaseAdapter.getListExpandItemCount(activity)
        Assert.assertEquals(4, RecentRecyclerExpandBaseAdapter.LIST_EXPAND_ITEM_COUNT)

        every { UIConfigMonitor.instance.getLandscapeUiMode(activity) }
            .returns(UIConfigMonitor.LandscapeUiMode.MultiWindow55)
        RecentRecyclerExpandBaseAdapter.getListExpandItemCount(activity)
        Assert.assertEquals(5, RecentRecyclerExpandBaseAdapter.LIST_EXPAND_ITEM_COUNT)

        every { UIConfigMonitor.instance.getLandscapeUiMode(activity) }
            .returns(UIConfigMonitor.LandscapeUiMode.MultiWindow64)
        RecentRecyclerExpandBaseAdapter.getListExpandItemCount(activity)
        Assert.assertEquals(6, RecentRecyclerExpandBaseAdapter.LIST_EXPAND_ITEM_COUNT)

        every { UIConfigMonitor.instance.getLandscapeUiMode(activity) }
            .returns(UIConfigMonitor.LandscapeUiMode.NoneMultiWindow)
        RecentRecyclerExpandBaseAdapter.getListExpandItemCount(activity)
        Assert.assertEquals(9, RecentRecyclerExpandBaseAdapter.LIST_EXPAND_ITEM_COUNT)*/
    }

    @After
    fun tearDown() {
        unmockkAll()
    }
}