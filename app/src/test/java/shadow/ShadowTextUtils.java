/***************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * Description:Unit test shadow class
 * Version: 1.0
 * Date : 2019/7/22
 * <EMAIL>
 *
 * -------------------Revision History: ----------------------
 *     <author>    <data>     <version >     <desc>
 *   lanjiabo    2019/7/22   1.0            build this moudle
 ****************************************************************/
package shadow;

import android.text.TextUtils;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.util.Locale;

@Implements(TextUtils.class)
public class ShadowTextUtils {
    private static int sLayoutDirectionFromLocale;

    @Implementation
    public static int getLayoutDirectionFromLocale(Locale var0) {
        return sLayoutDirectionFromLocale;
    }

    public static void setLayoutDirectionFromLocale(int layoutDirectionFromLocale) {
        sLayoutDirectionFromLocale = layoutDirectionFromLocale;
    }
}
