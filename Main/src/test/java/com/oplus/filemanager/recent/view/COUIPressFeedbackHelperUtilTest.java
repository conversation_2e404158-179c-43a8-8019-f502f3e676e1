package com.oplus.filemanager.recent.view;

import android.view.View;
import android.animation.ValueAnimator;
import android.content.res.Resources;
import android.util.TypedValue;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * COUIPressFeedbackHelperUtil的单元测试类
 * 用于测试COUIPressFeedbackHelperUtil的各种功能
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 29)
public class COUIPressFeedbackHelperUtilTest {

    @Mock
    private View mockView;  // 模拟的View对象
    @Mock
    private Resources mockResources;  // 模拟的资源对象
    @Mock
    private ValueAnimator mockAnimator;  // 模拟的动画对象

    private COUIPressFeedbackHelperUtil helper;  // 被测试的辅助类实例
    private AutoCloseable mockitoCloseable;  // Mockito的自动关闭资源

    /**
     * 测试前的准备工作
     * 初始化Mock对象和测试环境
     */
    @Before
    public void setUp() {
        mockitoCloseable = MockitoAnnotations.openMocks(this);
        // 设置模拟View的上下文和资源
        when(mockView.getContext()).thenReturn(mock(android.content.Context.class));
        when(mockView.getContext().getResources()).thenReturn(mockResources);
        // 创建测试对象实例，使用卡片按压反馈类型
        helper = new COUIPressFeedbackHelperUtil(mockView, COUIPressFeedbackHelperUtil.CARD_PRESS_FEEDBACK);
    }

    /**
     * 测试后的清理工作
     * 关闭资源，重置模拟对象
     */
    @After
    public void tearDown() throws Exception {
        try {
            // 通过反射获取并取消可能正在运行的动画
            java.lang.reflect.Field scaleAnimatorField = COUIPressFeedbackHelperUtil.class.getDeclaredField("mScaleAnimator");
            scaleAnimatorField.setAccessible(true);
            ValueAnimator animator = (ValueAnimator) scaleAnimatorField.get(helper);
            if (animator != null) {
                animator.cancel();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        helper = null;
        mockitoCloseable.close();
        Mockito.reset(mockView, mockResources, mockAnimator);
    }

    /**
     * 测试卡片按压反馈动画的执行
     */
    @Test
    public void testExecuteFeedbackAnimator_CardPress() {
        helper = new COUIPressFeedbackHelperUtil(mockView, COUIPressFeedbackHelperUtil.CARD_PRESS_FEEDBACK);
        // 设置模拟View的宽高
        when(mockView.getWidth()).thenReturn(200);
        when(mockView.getHeight()).thenReturn(200);
        
        // 执行按压动画
        helper.executeFeedbackAnimator(true);
        // 验证是否调用了获取宽高的方法
        verify(mockView, atLeastOnce()).getWidth();
        verify(mockView, atLeastOnce()).getHeight();
    }

    /**
     * 测试无边框按钮按压反馈动画的执行
     */
    @Test
    public void testExecuteFeedbackAnimator_BorderlessButton() {
        helper = new COUIPressFeedbackHelperUtil(mockView, COUIPressFeedbackHelperUtil.BORDERLESS_BUTTON_PRESS_FEEDBACK);
        helper.executeFeedbackAnimator(true);
        // 验证是否调用了获取上下文的方法
        verify(mockView, atLeastOnce()).getContext();
    }

    /**
     * 测试填充按钮按压反馈动画的执行
     */
    @Test
    public void testExecuteFeedbackAnimator_FillButton() {
        helper = new COUIPressFeedbackHelperUtil(mockView, COUIPressFeedbackHelperUtil.FILL_BUTTON_PRESS_FEEDBACK);
        helper.executeFeedbackAnimator(true);
        // 验证是否调用了获取上下文的方法
        verify(mockView, atLeastOnce()).getContext();
    }

    /**
     * 测试不可跳转卡片按压反馈动画的执行
     */
    @Test
    public void testExecuteFeedbackAnimator_UnjumpableCard() {
        helper = new COUIPressFeedbackHelperUtil(mockView, COUIPressFeedbackHelperUtil.UNJUMPABLE_CARD_PRESS_FEEDBACK);
        // 设置模拟View的宽高
        when(mockView.getWidth()).thenReturn(200);
        when(mockView.getHeight()).thenReturn(200);
        
        helper.executeFeedbackAnimator(true);
        // 验证是否调用了获取宽高的方法
        verify(mockView, atLeastOnce()).getWidth();
        verify(mockView, atLeastOnce()).getHeight();
    }

    /**
     * 测试取消动画的方法
     */
    @Test
    public void testCancelAnimator() throws Exception {
        // 通过反射设置模拟动画对象
        java.lang.reflect.Field scaleAnimatorField = COUIPressFeedbackHelperUtil.class.getDeclaredField("mScaleAnimator");
        scaleAnimatorField.setAccessible(true);
        scaleAnimatorField.set(helper, mockAnimator);
        
        // 设置动画正在运行
        when(mockAnimator.isRunning()).thenReturn(true);
        
        // 通过反射调用cancelAnimator方法
        java.lang.reflect.Method method = COUIPressFeedbackHelperUtil.class.getDeclaredMethod("cancelAnimator");
        method.setAccessible(true);
        method.invoke(helper);
        
        // 验证是否调用了cancel方法
        verify(mockAnimator).cancel();
    }
}