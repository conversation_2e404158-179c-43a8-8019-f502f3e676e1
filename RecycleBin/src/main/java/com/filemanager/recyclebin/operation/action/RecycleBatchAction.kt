/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** File:  - RecycleBatchAction.kt
 ** Description: Recycle Batch Action
 ** Version: 1.0
 ** Date : 2020/11/17
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/11/17    1.0     create
 ****************************************************************/
package com.filemanager.recyclebin.operation.action

import android.content.ContentResolver
import android.content.ContentValues
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import android.net.Uri
import com.filemanager.common.MyApplication
import com.filemanager.common.RecycleStore
import com.filemanager.common.batch.DoubleBatchAction
import com.filemanager.common.batch.StringBatchForResult
import com.filemanager.common.utils.Log
import com.filemanager.recyclebin.utils.OperationDBUtils

class QueryBatchAction(private val mUri: Uri = RecycleStore.Files.INTERNAL_CONTENT_URI,
                       column: String, bulkCount: Int = QUERY_BATCH_PATH_COUNT)
    : StringBatchForResult<HashMap<String, ContentValues>>(bulkCount) {

    companion object {
        val TAG = "QueryBatchAction"
        val QUERY_PROJECTION = arrayOf(RecycleStore.Files.FileColumns.RECYCLE_ID,
                RecycleStore.Files.FileColumns.ORIGIN_PATH,
                RecycleStore.Files.FileColumns.RECYCLE_PATH,
                RecycleStore.Files.FileColumns.RECYCLE_DATE)

        val QUERY_BATCH_ID_COUNT = 3000
        val QUERY_BATCH_PATH_COUNT = 50
        val RECYCLE_ID_INDEX = 0
        val ORIGIN_PATH_INDEX = 1
        val RECYCLE_PATH_INDEX = 2
        val RECYCLE_DATE_INDEX = 3
    }

    private val mValuesList: HashMap<String, ContentValues>
    private val mKey: Int
    private val mColumnName: String

    private val mResolver: ContentResolver by lazy {
        MyApplication.sAppContext.contentResolver
    }

    init {
        mValuesList = HashMap()
        when (column) {
            RecycleStore.Files.FileColumns.RECYCLE_ID -> mKey = RECYCLE_ID_INDEX
            RecycleStore.Files.FileColumns.RECYCLE_PATH -> mKey = RECYCLE_PATH_INDEX
            else -> mKey = RECYCLE_PATH_INDEX
        }
        mLen = 0
        mColumnName = column
    }

    override fun flush() {
        try {
            mResolver.query(mUri, QUERY_PROJECTION,
                    mColumnName + " IN ("
                            + mWhereClause.toString() + ")",
                    null,
                    null, null)?.use { cursor ->
                var id: String
                var originPath: String
                var recyclePath: String
                var recycleDate: Long
                var values: ContentValues
                var key: String
                while (cursor.moveToNext()) {
                    originPath = cursor.getString(ORIGIN_PATH_INDEX)
                    if (originPath.isNullOrEmpty()) {
                        continue
                    }
                    id = cursor.getString(RECYCLE_ID_INDEX)
                    recyclePath = cursor.getString(RECYCLE_PATH_INDEX)
                    recycleDate = cursor.getLong(RECYCLE_DATE_INDEX)
                    values = ContentValues()
                    values.put(RecycleStore.Files.FileColumns.RECYCLE_ID, id)
                    values.put(RecycleStore.Files.FileColumns.ORIGIN_PATH, originPath)
                    values.put(RecycleStore.Files.FileColumns.RECYCLE_PATH, recyclePath)
                    values.put(RecycleStore.Files.FileColumns.RECYCLE_DATE, recycleDate)
                    when (mKey) {
                        RECYCLE_ID_INDEX -> key = id
                        RECYCLE_PATH_INDEX -> key = recyclePath
                        else -> key = recyclePath
                    }
                    mValuesList.put(key, values)
                }
            }
        } catch (ex: Exception) {
            Log.w(TAG, "flush error: $ex")
        } finally {
            mLen = 0
            mWhereClause.clear()
        }
    }

    override fun getBatchResult(): HashMap<String, ContentValues> {
        return mValuesList
    }
}

class DoubleDeleteAction(private val mUri: Uri = RecycleStore.Files.INTERNAL_CONTENT_URI,
                         private val mColumnName1: String,
                         private val mColumnName2: String)
    : DoubleBatchAction<String, String>(BULK_FILE_SIZE, BULK_FOLER_SIZE) {

    companion object {
        private val TAG = "DoubleParamsDeleteAction"
        private const val BULK_FILE_SIZE = 100
        private const val BULK_FOLER_SIZE = 1
    }

    private val mWhereClause1: StringBuilder
    private val mWhereClause2: StringBuilder
    var sqliteOpenHelper: SQLiteOpenHelper? = null
    var db: SQLiteDatabase? = null
    private val mResolver: ContentResolver by lazy {
        MyApplication.sAppContext.contentResolver
    }

    init {
        mWhereClause1 = StringBuilder()
        mWhereClause2 = StringBuilder()
    }

    override fun add(pending1: String?, pending2: String?) {
        if (!pending1.isNullOrEmpty()) {
            if (mWhereClause1.isNotEmpty()) {
                mWhereClause1.append(",")
            }
            mWhereClause1.append("?")
        }

        if (!pending2.isNullOrEmpty()) {
            if (mWhereClause2.isNotEmpty()) {
                mWhereClause2.append(" OR ")
            }
            mWhereClause2.append(mColumnName2)
            mWhereClause2.append(" LIKE ")
            mWhereClause2.append(" ? ")
        }
        super.add(pending1, pending2)
    }

    override fun flushParams2() {
        val size = mPending2.size
        if (size <= 0) {
            return
        }

        try {
            if (db == null) {
                sqliteOpenHelper = OperationDBUtils.createSQLiteOpenHelper(mUri)
                db = OperationDBUtils.beginTransaction(sqliteOpenHelper?.writableDatabase)
            }
            val foo = mPending2.toTypedArray<String>()
            val numrows = OperationDBUtils.delete(mUri,
                " ($mWhereClause2)", foo, db, sqliteOpenHelper)
            Log.d(TAG, "flush size: $size numrows: $numrows")
        } catch (ex: Exception) {
            Log.w(TAG, "flush Failed $ex")
        } finally {
            //make sure reset args only when sucess
            mWhereClause2.setLength(0)
            mPending2.clear()
            OperationDBUtils.commit(db, RecycleStore.Files.INTERNAL_CONTENT_URI)
            sqliteOpenHelper = null
            db = null
        }
    }

    override fun flushParams1() {
        val size = mPending1.size
        if (size <= 0) {
            return
        }

        try {
            if (db == null) {
                sqliteOpenHelper = OperationDBUtils.createSQLiteOpenHelper(mUri)
                db = OperationDBUtils.beginTransaction(sqliteOpenHelper?.writableDatabase)
            }
            val foo = mPending1.toTypedArray<String>()
            val numrows = OperationDBUtils.delete(mUri,
                    mColumnName1 + " IN ("
                            + mWhereClause1.toString() + ")", foo, db, sqliteOpenHelper)
            Log.d(TAG, "flush size: $size numrows: $numrows")
        } catch (ex: Exception) {
            Log.w(TAG, "flush Failed $ex")
        } finally {
            //make sure reset args only when sucess
            mWhereClause1.setLength(0)
            mPending1.clear()
            OperationDBUtils.commit(db, RecycleStore.Files.INTERNAL_CONTENT_URI)
            sqliteOpenHelper = null
            db = null
        }
    }
}
