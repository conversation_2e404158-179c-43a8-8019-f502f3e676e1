<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:key="personal_detail_pref_time_filter"
        android:title="@string/collect_info_instruction"
        android:widgetLayout="@layout/coui_preference_category_widget_layout_textwithicon"
        app:icon_in_right="@drawable/arrow_down"
        app:text_in_right="@string/last_7_days"
        app:top_margin_type="small">

    <com.coui.appcompat.preference.COUIPreference
            android:key="personal_detail_pref_use_purpose"
            android:summary="@string/use_purpose_feedback_bug"
            android:title="@string/use_purpose" />

        <com.coui.appcompat.preference.COUIPreference
            android:key="personal_detail_pref_use_scene"
            android:summary="@string/use_scene_feedback"
            android:title="@string/use_scene" />

        <com.coui.appcompat.preference.COUIPreference
            android:key="personal_detail_pref_collect_condition"
            android:summary="0次"
            android:title="@string/collect_condition" />

        <com.coui.appcompat.preference.COUISpannablePreference
            android:key="personal_detail_pref_info_content"
            android:layout="@layout/coui_detail_preference"
            android:title="@string/info_content" />

    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.filemanager.setting.ui.privacy.personal.PlaceHolderPreference
        android:layout="@layout/preference_info_detail_empty"
        android:selectable="false" />

    <com.coui.appcompat.preference.COUIPagerFooterPreference
        android:selectable="false"
        android:summary="@string/collect_info_exclude_today_data"
        app:top_margin_type="small"/>
</PreferenceScreen>