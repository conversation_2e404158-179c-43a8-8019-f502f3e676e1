/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.select
 * * Version     : 1.0
 * * Date        : 2020/7/6
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir

import android.os.Bundle
import android.view.View
import androidx.annotation.VisibleForTesting
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import com.filemanager.common.base.BaseVMPanelFragment
import com.filemanager.common.controller.BaseLifeController
import com.filemanager.common.utils.Log

//activity must extends TransformNextFragmentListener
class SelectPathController(lifecycle: Lifecycle) : BaseLifeController {
    companion object {
        private const val TAG = "SelectPathController"
        const val SELECT_DIALOG_FRAGMENT_TAG = "SELECT_DIALOG_FRAGMENT_TAG"
        const val SELECT_FILE_TYPE_FILL_NAME_LIST = "fileTypeFillNameList"
    }

    @VisibleForTesting
    var mSelectionPathFragment: SelectPathDialogFragment? = null

    init {
        lifecycle.addObserver(this)
    }

    fun showSelectPathFragmentDialog(manager: FragmentManager, path: String?) {
        mSelectionPathFragment?.showPanelFragment(
            manager,
            SELECT_DIALOG_FRAGMENT_TAG,
            path = path,
            bundle = null,
            true
        )
    }

    fun showSelectPathFragmentDialog(
        manager: FragmentManager,
        code: Int,
        path: String? = null,
        bundle: Bundle? = null,
        hasStoragePermission: Boolean = true
    ) {
        mSelectionPathFragment = (manager.findFragmentByTag(SELECT_DIALOG_FRAGMENT_TAG)
                as? SelectPathDialogFragment)?.apply {
            Log.d(TAG, "showSelectPathFragmentDialog remove fragment")
            try {
                manager.beginTransaction().remove(this).commitAllowingStateLoss()
            } catch (e: Exception) {
                Log.e(TAG, e.message)
            }
        }
        mSelectionPathFragment = SelectPathDialogFragment() // fragment复用时通过isAdded判断时异常
        try {
            mSelectionPathFragment?.setIsShowInMaxHeight(true)
            mSelectionPathFragment?.showPanelFragment(manager, SELECT_DIALOG_FRAGMENT_TAG, code, path, bundle, hasStoragePermission)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to show dialog:error=" + e.message)
        }
    }

    fun hideSelectPathFragmentDialog(manager: FragmentManager) {
        (manager.findFragmentByTag(SELECT_DIALOG_FRAGMENT_TAG) as? SelectPathDialogFragment)?.apply {
            Log.d(TAG, "hideSelectPathFragmentDialog")
            try {
                this.dismissAllowingStateLoss()
            } catch (e: Exception) {
                Log.e(TAG, "hideSelectPathFragmentDialog: ${e.message}")
            }
        }
        mSelectionPathFragment = null
    }

    fun updateDialogHeightIfNeed(manager: FragmentManager? = null) {
        if (mSelectionPathFragment == null) {
            (manager?.findFragmentByTag(SELECT_DIALOG_FRAGMENT_TAG) as? SelectPathDialogFragment)?.apply {
                mSelectionPathFragment = this
            }
        }
        Log.v(TAG, "updateDialogHeightIfNeed  $mSelectionPathFragment ")
        mSelectionPathFragment?.updatePanelFragmentHeight()
    }

    fun onUIConfigChanged() {
        mSelectionPathFragment?.onUIConfigChanged()
    }

    override fun onDestroy() {
        mSelectionPathFragment = null
    }

    fun onStoragePermissionChange(
        hasStoragePermission: Boolean,
        supportFragmentManager: FragmentManager? = null
    ) {
        Log.d(TAG, "onStoragePermissionChange hasStoragePermission $hasStoragePermission")
        if (mSelectionPathFragment == null) {
            //切换暗色模糊后，界面重建mSelectionPathFragment被重置，需要重新赋值
            val fragments = supportFragmentManager?.fragments
            if (fragments?.isNotEmpty() == true) {
                (fragments[0] as? SelectPathDialogFragment)?.let { fragment ->
                    mSelectionPathFragment = fragment
                    Log.d(TAG, "onStoragePermissionChange mSelectionPathFragment $mSelectionPathFragment")
                }
            }
        }
        mSelectionPathFragment?.let {
            if (it.isAdded.not()) {
                Log.e(TAG, "mSelectionPathFragment is not added, return")
                return
            }
            val fragments = it.childFragmentManager.fragments
            if (fragments.isEmpty()) {
                Log.e(TAG, "fragments is empty, return")
                return
            }
            Log.d(TAG, "it.childFragmentManager.fragments[0] ${fragments[0]}")
            (fragments[0] as? BaseVMPanelFragment<*>)?.let { child ->
                if (child.isAdded.not()) {
                    Log.e(TAG, "child fragment is not added, return")
                    return
                }
                if (hasStoragePermission) {
                    Log.d(TAG, "onStoragePermissionChange isShowPermissionEmptyView ${child.isShowPermissionEmptyView}")
                    if (child.isShowPermissionEmptyView) {
                        child.setPermissionEmptyVisible(View.GONE)
                        child.onResumeLoadData()
                    }
                } else if (child.isShowPermissionEmptyView.not()) {
                    child.setPermissionEmptyVisible(View.VISIBLE)
                }
            }
        }
    }

    fun hasShowPanel(): Boolean {
        return mSelectionPathFragment?.dialog?.isShowing ?: false
    }
}