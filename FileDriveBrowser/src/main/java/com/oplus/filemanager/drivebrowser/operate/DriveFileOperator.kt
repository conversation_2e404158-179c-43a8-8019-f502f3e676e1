/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DriveFileOperator
 ** Description : Drive File Operator：open,download,rename,delete
 ** Version     : 1.0
 ** Date        : 2024/05/08 15:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/08       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.operate

import android.content.Context
import android.content.Intent
import androidx.activity.ComponentActivity
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.DriveCache
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.rename.FileRenameObserver
import com.oplus.filemanager.drivebrowser.data.model.tencent.DeleteFileRequest
import com.oplus.filemanager.drivebrowser.data.repository.kdocs.KDocsFileDriveRepository
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.di.NetworkModule
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.drivebrowser.operate.delete.CloudFileActionDelete
import com.oplus.filemanager.drivebrowser.operate.delete.CloudFileDeleteObserver
import com.oplus.filemanager.drivebrowser.operate.download.ACTION_DOWNLOAD
import com.oplus.filemanager.drivebrowser.operate.download.CloudFileDownloadAction
import com.oplus.filemanager.drivebrowser.operate.download.CloudFileDownloadObserver
import com.oplus.filemanager.drivebrowser.operate.rename.CloudFileActionRename
import com.oplus.filemanager.drivebrowser.ui.CloudDocsItem
import com.oplus.filemanager.drivebrowser.ui.DocumentPreviewActivity
import com.oplus.filemanager.interfaze.wechat.IWechat
import com.oplus.filemanager.provider.DriveFileDBHelper
import com.oplus.filemanager.room.model.DriveFileEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import java.util.function.Consumer

object DriveFileOperator {

    private const val TAG = "DriveFileOperator"

    private val tencentRepository: FileDriveRepository by lazy {
        val service = NetworkModule.providerTencentFileDriveService()
        TencentFileDriveRepository(service)
    }

    private val kDocsRepository: FileDriveRepository by lazy {
        val service = NetworkModule.provideKDocsFileDriveService()
        KDocsFileDriveRepository(service)
    }

    /**
     * 打开金山文档
     */
    @JvmStatic
    fun openKDocsFile(context: Context, fileId: String, fileName: String, fileType: String) {
        val url = runBlocking(Dispatchers.IO) {
            kDocsRepository.getEditUrl(fileId)
        }
        val intent = Intent(context, DocumentPreviewActivity::class.java)
        intent.putExtra(DocumentPreviewActivity.KEY_DOCUMENT_URL, url)
        intent.putExtra(DocumentPreviewActivity.KEY_DOCUMENT_TITLE, fileName)
        context.startActivity(intent)
        statisticsFileOperate(StatisticsUtils.OPEN_DRIVE_FILE, DriveFileEntity.SOURCE_TYPE_KINGSSOFT, fileType)
    }

    /**
     * 打开腾讯文档
     */
    @JvmStatic
    fun openTencentDocs(context: Context, fileUrl: String, fileType: String) {
        val wechatSdkApi = Injector.injectFactory<IWechat>()
        wechatSdkApi?.openFile(fileUrl)
        statisticsFileOperate(StatisticsUtils.OPEN_DRIVE_FILE_BY_MINIAPP, DriveFileEntity.SOURCE_TYPE_TENCENT, fileType)
    }

    /**
     * 下载云文档
     */
    fun downloadCloudFile(activity: ComponentActivity, file: DriveFileWrapper, targetPath: String, consumer: Consumer<Int>) {
        val category = if (file.source == DriveFileWrapper.SOURCE_TYPE_TENCENT) {
            CategoryHelper.CATEGORY_TENCENT_DOCS
        } else {
            CategoryHelper.CATEGORY_K_DOCS
        }

        val networkService = if (file.isTencentDocs()) {
            tencentRepository
        } else {
            kDocsRepository
        }

        Log.d(TAG, "downloadCloudFile $file targetPath:$targetPath")
        statisticsFileOperate(StatisticsUtils.DOWNLOAD_DRIVE_FILE, category, 1)
        CloudFileDownloadAction(networkService, activity, category, arrayListOf(CloudDocsItem.map(file)), targetPath).execute(object :
            CloudFileDownloadObserver(activity) {
            override fun onActionDone(result: Boolean, data: Any?) {
                super.onActionDone(result, data)
                Log.d(TAG, "onActionDone -> result = $result ; data = $data")
                consumer.accept(ACTION_DONE)
            }

            override fun onActionDownload(isAuth: Boolean) {
                super.onActionDownload(isAuth)
                Log.d(TAG, "onActionDownload")
                consumer.accept(ACTION_DOWNLOAD)
            }
        })
    }

    /**
     * 重命名文档
     * @param activity activity
     * @param file 重命名的文件
     * @param consumer 命名成功后的操作 Pair中first: 重命名结果，second: 重命名后的名称
     */
    fun renameCloudFile(activity: ComponentActivity, file: DriveFileWrapper, consumer: Consumer<Pair<Int, String>>) {
        val networkService = if (file.isTencentDocs()) {
            tencentRepository
        } else {
            kDocsRepository
        }
        Log.d(TAG, "renameCloudFile $file")
        statisticsFileOperate(StatisticsUtils.RENAME_DRIVE_FILE, file.source, file.type)
        CloudFileActionRename(activity, CloudDocsItem.map(file), networkService, true).execute(object : FileRenameObserver(activity) {

            override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
                Log.d(TAG, "renameCloudFile onChanged $result")
                when (result.first) {
                    ACTION_FAILED -> {
                        dismissRenameDialog()
                        return false
                    }
                }
                return super.onChanged(context, result)
            }

            override fun onActionDone(result: Boolean, data: Any?) {
                super.onActionDone(result, data)
                Log.d(TAG, "renameCloudFile onActionDone result:$result data:$data")
                if (result) {
                    consumer.accept(Pair(ACTION_DONE, data as String))
                } else {
                    CustomToast.showShort(com.filemanager.common.R.string.rename_docs_failure)
                }
            }
        })
    }

    /**
     * 删除相同类型的云文档，只包含腾讯文档或者金山文档
     */
    fun deleteSameCategoryCloudFile(activity: ComponentActivity, files: List<DriveFileWrapper>, consumer: Consumer<Int>) {
        val file = files[0]
        val category: Int
        val networkService = if (file.isTencentDocs()) {
            category = CategoryHelper.CATEGORY_TENCENT_DOCS
            tencentRepository
        } else {
            category = CategoryHelper.CATEGORY_K_DOCS
            kDocsRepository
        }
        statisticsFileOperate(StatisticsUtils.DELETE_DRIVE_FILE, category, files.size)
        CloudFileActionDelete(activity, file.source, files, networkService).execute(object : CloudFileDeleteObserver(activity) {
            override fun onActionDone(result: Boolean, data: Any?) {
                super.onActionDone(result, data)
                Log.d(TAG, "deleteCloudFiles onActionDone result:$result data:$data")
                if (result) {
                    consumer.accept(ACTION_DONE)
                }
            }
        })
    }

    /**
     * 删除云文档
     */
    fun deleteCloudFiles(files: List<DriveFileWrapper>): Int {
        Log.d(TAG, "deleteCloudFiles ${files.size}")
        val result = runBlocking(Dispatchers.IO) {
            // 拆分为腾讯文档和金山文档
            val tencentFiles = mutableListOf<DriveFileWrapper>()
            val kDocsFiles = mutableListOf<DriveFileWrapper>()
            files.forEach {
                if (it.isTencentDocs()) {
                    tencentFiles.add(it)
                } else if (it.isKDocs()) {
                    kDocsFiles.add(it)
                }
            }
            // 分别调用删除
            val tencentDelJob = async { realDeleteCloudFiles(DriveFileWrapper.SOURCE_TYPE_TENCENT, tencentFiles) }
            val kDocsDelJob = async { realDeleteCloudFiles(DriveFileWrapper.SOURCE_TYPE_KINGSSOFT, kDocsFiles) }
            tencentDelJob.await() + kDocsDelJob.await()
        }
        Log.d(TAG, "deleteCloudFiles result -> $result")
        return result
    }

    /**
     * 真正的删除文件
     */
    private suspend fun realDeleteCloudFiles(fileSource: String, files: List<DriveFileWrapper>): Int {
        if (files.isEmpty()) {
            return 0
        }
        val networkService: FileDriveRepository
        val category: Int
        if (fileSource == DriveFileWrapper.SOURCE_TYPE_TENCENT) {
            networkService = tencentRepository
            category = CategoryHelper.CATEGORY_TENCENT_DOCS
        } else {
            networkService = kDocsRepository
            category = CategoryHelper.CATEGORY_K_DOCS
        }
        statisticsFileOperate(StatisticsUtils.DELETE_DRIVE_FILE, category, files.size)

        var successCount = 0
        // 请求网络删除
        files.forEach {
            val networkResult = if (it.mIsDirectory) {
                networkService.deleteFolder(it.id)
            } else {
                networkService.deleteFile(it.id, DeleteFileRequest.TYPE_ORIGIN)
            }
            if (networkResult.first) {
                if (it.mIsDirectory) {
                    DriveFileDBHelper.deleteFolder(fileSource, it.id)
                } else {
                    DriveFileDBHelper.deleteDocument(fileSource, it.id)
                }
                //搜索页面云文档混合选择金山+腾讯文档会走这里
                DriveCache.putDeleteCache(it.id)
                successCount++
            } else {
                statisticsDeleteFail(fileSource, networkResult.second)
            }
        }
        Log.w(TAG, "realDeleteCloudFiles source:$fileSource all:${files.size} -> success:$successCount")
        return successCount
    }


    /**
     * 统计文件操作事件
     * @param eventId 事件
     * @param fileType 文件类型
     */
    private fun statisticsFileOperate(eventId: String, fileSource: String, fileType: String) {
        val map = hashMapOf<String, String>()
        map[StatisticsUtils.FILE_SOURCE] = fileSource
        map[StatisticsUtils.FILE_TYPE] = fileType
        StatisticsUtils.onCommon(appContext, eventId, map)
    }

    /**
     * 统计文件操作事件
     * @param eventId 事件
     * @param category 分类，用于区分腾讯文档还是云文档
     * @param count 文件个数
     */
    private fun statisticsFileOperate(eventId: String, category: Int, count: Int = -1) {
        val fileSource = if (category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
            DriveFileWrapper.SOURCE_TYPE_TENCENT
        } else {
            DriveFileWrapper.SOURCE_TYPE_KINGSSOFT
        }
        val map = hashMapOf<String, String>()
        map[StatisticsUtils.FILE_SOURCE] = fileSource
        map[StatisticsUtils.COUNT] = "$count"
        StatisticsUtils.onCommon(appContext, eventId, map)
    }

    /**
     * 统计删除文件失败
     */
    private fun statisticsDeleteFail(fileSource: String, msg: String) {
        val map = hashMapOf<String, String>()
        map[StatisticsUtils.FILE_SOURCE] = fileSource
        map[StatisticsUtils.ERROR] = msg
        map[StatisticsUtils.COUNT] = "1"
        StatisticsUtils.onCommon(appContext, StatisticsUtils.DELETE_DRIVE_FILE_FAIL, map)
    }
}