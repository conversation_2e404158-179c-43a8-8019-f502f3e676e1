/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ArchiveOperationsController.kt
 * Description:
 *     The customized operations for archive preview page.
 *
 * Version: 1.0
 * Date: 2024-10-17
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-10-17   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.archive

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.preview.core.PreviewOperationsController
import com.oplus.ota.upgrade.OtaUtils

internal class ArchiveOperationsController(
    fragment: Fragment,
    previewModel: FilePreviewViewModel,
    selectPathsCache: MutableMap<Int, List<String>?>
) : PreviewOperationsController(fragment, previewModel, selectPathsCache) {

    private companion object {
        private const val TAG = "ArchiveOperationsController"
    }

    override fun onActionOpen(activity: FragmentActivity, fileBean: BaseFileBean) {
        previewModel.launch { deCompressArchiveFile(activity, fileBean) }
    }

    private suspend fun deCompressArchiveFile(activity: FragmentActivity, fileBean: BaseFileBean) {
        if (OtaUtils.checkOzipFile(activity, fileBean)) {
            Log.d(TAG, "onActionOpen: open as OTA package.")
            return
        }
        normalController.onSelectDecompressDest(activity)
    }
}