package com.oplus.dropdrag

import android.view.KeyEvent
import android.view.MotionEvent
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.mockito.MockitoAnnotations
import org.mockito.MockitoSession
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * MotionEvents工具类的单元测试类
 * 使用Robolectric测试框架和Mockito进行测试
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class MotionEventsTest {
    private lateinit var mockitoSession: MockitoSession

    /**
     * 在每个测试方法执行前初始化
     * 初始化Mockito框架
     */
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        mockitoSession = Mockito.mockitoSession()
            .initMocks(this)
            .startMocking()
    }

    /**
     * 在每个测试方法执行后清理
     * 结束Mockito会话
     */
    @After
    fun tearDown() {
        mockitoSession.finishMocking()
    }

    /**
     * 测试isMouseEvent方法
     * 验证是否能正确识别鼠标事件
     */
    @Test
    fun testIsMouseEvent() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟鼠标事件
        Mockito.`when`(event.getToolType(0)).thenReturn(MotionEvent.TOOL_TYPE_MOUSE)
        assertTrue(MotionEvents.isMouseEvent(event))

        // 模拟非鼠标事件
        Mockito.`when`(event.getToolType(0)).thenReturn(MotionEvent.TOOL_TYPE_FINGER)
        assertFalse(MotionEvents.isMouseEvent(event))
    }

    /**
     * 测试isTouchEvent方法
     * 验证是否能正确识别触摸事件
     */
    @Test
    fun testIsTouchEvent() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟触摸事件
        Mockito.`when`(event.getToolType(0)).thenReturn(MotionEvent.TOOL_TYPE_FINGER)
        assertTrue(MotionEvents.isTouchEvent(event))

        // 模拟非触摸事件
        Mockito.`when`(event.getToolType(0)).thenReturn(MotionEvent.TOOL_TYPE_MOUSE)
        assertFalse(MotionEvents.isTouchEvent(event))
    }

    /**
     * 测试isActionMove方法
     * 验证是否能正确识别移动动作
     */
    @Test
    fun testIsActionMove() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟移动动作
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_MOVE)
        assertTrue(MotionEvents.isActionMove(event))

        // 模拟非移动动作
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_DOWN)
        assertFalse(MotionEvents.isActionMove(event))
    }

    /**
     * 测试isActionDown方法
     * 验证是否能正确识别按下动作
     */
    @Test
    fun testIsActionDown() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟按下动作
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_DOWN)
        assertTrue(MotionEvents.isActionDown(event))

        // 模拟非按下动作
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_UP)
        assertFalse(MotionEvents.isActionDown(event))
    }

    /**
     * 测试isActionUp方法
     * 验证是否能正确识别抬起动作
     */
    @Test
    fun testIsActionUp() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟抬起动作
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_UP)
        assertTrue(MotionEvents.isActionUp(event))

        // 模拟非抬起动作
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_DOWN)
        assertFalse(MotionEvents.isActionUp(event))
    }

    /**
     * 测试isActionPointerUp方法
     * 验证是否能正确识别多点触控的抬起动作
     */
    @Test
    fun testIsActionPointerUp() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟多点触控抬起动作
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_POINTER_UP)
        assertTrue(MotionEvents.isActionPointerUp(event))

        // 模拟非多点触控抬起动作
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_DOWN)
        assertFalse(MotionEvents.isActionPointerUp(event))
    }

    /**
     * 测试isActionPointerDown方法
     * 验证是否能正确识别多点触控的按下动作
     */
    @Test
    fun testIsActionPointerDown() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟多点触控按下动作
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_POINTER_DOWN)
        assertTrue(MotionEvents.isActionPointerDown(event))

        // 模拟非多点触控按下动作
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_UP)
        assertFalse(MotionEvents.isActionPointerDown(event))
    }

    /**
     * 测试isActionCancel方法
     * 验证是否能正确识别取消动作
     */
    @Test
    fun testIsActionCancel() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟取消动作
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_CANCEL)
        assertTrue(MotionEvents.isActionCancel(event))

        // 模拟非取消动作
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_DOWN)
        assertFalse(MotionEvents.isActionCancel(event))
    }

    /**
     * 测试getOrigin方法
     * 验证是否能正确获取事件坐标点
     */
    @Test
    fun testGetOrigin() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 设置模拟坐标
        Mockito.`when`(event.x).thenReturn(100f)
        Mockito.`when`(event.y).thenReturn(200f)
        val point = MotionEvents.getOrigin(event)
        // 验证x坐标
        assertEquals(100, point.x)
        // 验证y坐标
        assertEquals(200, point.y)
    }

    /**
     * 测试isPrimaryMouseButtonPressed方法
     * 验证是否能正确识别主鼠标按钮按下状态
     */
    @Test
    fun testIsPrimaryMouseButtonPressed() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟主鼠标按钮按下
        Mockito.`when`(event.buttonState).thenReturn(MotionEvent.BUTTON_PRIMARY)
        assertTrue(MotionEvents.isPrimaryMouseButtonPressed(event))

        // 模拟非主鼠标按钮按下
        Mockito.`when`(event.buttonState).thenReturn(MotionEvent.BUTTON_SECONDARY)
        assertFalse(MotionEvents.isPrimaryMouseButtonPressed(event))
    }

    /**
     * 测试isSecondaryMouseButtonPressed方法
     * 验证是否能正确识别次鼠标按钮按下状态
     */
    @Test
    fun testIsSecondaryMouseButtonPressed() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟次鼠标按钮按下
        Mockito.`when`(event.buttonState).thenReturn(MotionEvent.BUTTON_SECONDARY)
        assertTrue(MotionEvents.isSecondaryMouseButtonPressed(event))

        // 模拟非次鼠标按钮按下
        Mockito.`when`(event.buttonState).thenReturn(MotionEvent.BUTTON_PRIMARY)
        assertFalse(MotionEvents.isSecondaryMouseButtonPressed(event))
    }

    /**
     * 测试isTertiaryMouseButtonPressed方法
     * 验证是否能正确识别第三鼠标按钮按下状态
     */
    @Test
    fun testIsTertiaryMouseButtonPressed() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟第三鼠标按钮按下
        Mockito.`when`(event.buttonState).thenReturn(MotionEvent.BUTTON_TERTIARY)
        assertTrue(MotionEvents.isTertiaryMouseButtonPressed(event))

        // 模拟非第三鼠标按钮按下
        Mockito.`when`(event.buttonState).thenReturn(MotionEvent.BUTTON_PRIMARY)
        assertFalse(MotionEvents.isTertiaryMouseButtonPressed(event))
    }

    /**
     * 测试isShiftKeyPressed方法
     * 验证是否能正确识别Shift键按下状态
     */
    @Test
    fun testIsShiftKeyPressed() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟Shift键按下
        Mockito.`when`(event.metaState).thenReturn(KeyEvent.META_SHIFT_ON)
        assertTrue(MotionEvents.isShiftKeyPressed(event))

        // 模拟非Shift键按下
        Mockito.`when`(event.metaState).thenReturn(KeyEvent.META_CTRL_ON)
        assertFalse(MotionEvents.isShiftKeyPressed(event))
    }

    /**
     * 测试isCtrlKeyPressed方法
     * 验证是否能正确识别Ctrl键按下状态
     */
    @Test
    fun testIsCtrlKeyPressed() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟Ctrl键按下
        Mockito.`when`(event.metaState).thenReturn(KeyEvent.META_CTRL_ON)
        assertTrue(MotionEvents.isCtrlKeyPressed(event))

        // 模拟非Ctrl键按下
        Mockito.`when`(event.metaState).thenReturn(KeyEvent.META_SHIFT_ON)
        assertFalse(MotionEvents.isCtrlKeyPressed(event))
    }

    /**
     * 测试isAltKeyPressed方法
     * 验证是否能正确识别Alt键按下状态
     */
    @Test
    fun testIsAltKeyPressed() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟Alt键按下
        Mockito.`when`(event.metaState).thenReturn(KeyEvent.META_ALT_ON)
        assertTrue(MotionEvents.isAltKeyPressed(event))

        // 模拟非Alt键按下
        Mockito.`when`(event.metaState).thenReturn(KeyEvent.META_SHIFT_ON)
        assertFalse(MotionEvents.isAltKeyPressed(event))
    }

    /**
     * 测试isTouchpadScroll方法
     * 验证是否能正确识别触摸板滚动事件
     */
    @Test
    fun testIsTouchpadScroll() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟触摸板滚动事件
        Mockito.`when`(event.getToolType(0)).thenReturn(MotionEvent.TOOL_TYPE_MOUSE)
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_MOVE)
        Mockito.`when`(event.buttonState).thenReturn(0)
        assertTrue(MotionEvents.isTouchpadScroll(event))

        // 模拟非触摸板滚动事件
        Mockito.`when`(event.getToolType(0)).thenReturn(MotionEvent.TOOL_TYPE_FINGER)
        assertFalse(MotionEvents.isTouchpadScroll(event))
    }

    /**
     * 测试isPointerDragEvent方法
     * 验证是否能正确识别指针拖拽事件
     */
    @Test
    fun testIsPointerDragEvent() {
        val event = Mockito.mock(MotionEvent::class.java)
        // 模拟指针拖拽事件
        Mockito.`when`(event.buttonState).thenReturn(MotionEvent.BUTTON_PRIMARY)
        Mockito.`when`(event.actionMasked).thenReturn(MotionEvent.ACTION_MOVE)
        assertTrue(MotionEvents.isPointerDragEvent(event))

        // 模拟非指针拖拽事件
        Mockito.`when`(event.buttonState).thenReturn(MotionEvent.BUTTON_SECONDARY)
        assertFalse(MotionEvents.isPointerDragEvent(event))
    }
}