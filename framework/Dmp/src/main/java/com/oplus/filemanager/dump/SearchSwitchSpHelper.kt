/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : SearchSwitchSpHelper.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.dump

import android.util.Log
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.utils.PreferencesUtils

object SearchSwitchSpHelper {

    const val TAG = "SearchSwitchSpHelper"

    /**
     * 更新本地的sp的开关状态到dmp中
     */
    @JvmStatic
    fun updateThirdAppSwitchToDmp() {
        val thirdAppSwitchSp = PreferencesUtils.getBoolean(
            key = CommonConstants.THIRD_APP_SEARCH_FUNCTION_SHOW,
            default = false
        )
        val setResult = SearchProcessor.setSwitchStateToDmp(thirdAppSwitchSp)
        Log.i(TAG, "updateThirdAppSwitchToDmp sp $thirdAppSwitchSp, setResult $setResult")
    }

    /**
     * 比较本地的sp的开关到dmp中的开关是否一致
     */
    @JvmStatic
    fun checkNeedUpdateSpToDmp(): Boolean {
        val thirdAppSwitchSp = PreferencesUtils.getBoolean(
            key = CommonConstants.THIRD_APP_SEARCH_FUNCTION_SHOW,
            default = false
        )
        val switchBean = SearchProcessor.getSwitchStateFromDmp()
        //这里如果对端的dmp不支持，或从dmp读取开关状态出错时，不用做更新
        if (switchBean.isDmpNotSupport() || switchBean.isBeanInvalid()) {
            Log.w(TAG, "checkNeedUpdateSpToDmp get switchBean error, $switchBean, return")
            return false
        }
        val spEqualDmpSwitch = switchBean.switchValue == thirdAppSwitchSp
        Log.d(
            TAG,
            "checkNeedUpdateSpToDmp localSp $thirdAppSwitchSp, dmpSwitch ${switchBean.switchValue}, equal $spEqualDmpSwitch"
        )
        return !spEqualDmpSwitch
    }

    /**
     * 检查是否需要将本地的sp更新到dmp，如果需要则更新一次
     */
    @JvmStatic
    fun checkAndUpdateSpToDmp() {
        if (checkNeedUpdateSpToDmp()) {
            updateThirdAppSwitchToDmp()
        }
    }
}