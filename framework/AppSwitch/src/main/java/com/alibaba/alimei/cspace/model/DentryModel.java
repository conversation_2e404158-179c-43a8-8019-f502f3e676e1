/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DentryModel.kt
 * * Description : 钉钉中的intent中传输文件的类
 * * Version     : 1.0
 * * Date        : 2024/5/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.alibaba.alimei.cspace.model;

import android.os.Parcel;
import android.util.Log;

import androidx.annotation.NonNull;

public class DentryModel extends DentryBaseModel {

    public static final Creator<DentryModel> CREATOR = new Creator<DentryModel>() {
        @Override
        public DentryModel createFromParcel(Parcel in) {
            return new DentryModel(in);
        }

        @Override
        public DentryModel[] newArray(int size) {
            return new DentryModel[size];
        }
    };

    public static final String TAG = "DentryModel";


    public DentryModel(Parcel parcel) {
        super(parcel);
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        Log.i(TAG, "writeToParcel");
    }
}
