plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")
apply from: rootProject.file("scripts/variant.gradle")

android {
    namespace "com.oplus.filemanager.category.globalsearch"
}

dependencies {
    implementation libs.apache.commons.io
    implementation libs.google.gson

    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.recyclerview
    implementation libs.oplus.appcompat.dialog
    implementation libs.oplus.appcompat.poplist
    implementation libs.oplus.appcompat.clickablespan
    implementation libs.oplus.appcompat.statement
    implementation libs.oplus.appcompat.grid
    implementation libs.oplus.appcompat.scroll
    implementation libs.oplus.appcompat.scrollview
    implementation libs.oplus.appcompat.viewpager
    implementation libs.oplus.appcompat.bottomnavigation
    implementation libs.oplus.appcompat.card
    implementation libs.oplus.appcompat.scrollbar
    implementation libs.oplus.appcompat.progressbar
    implementation libs.oplus.appcompat.toolbar
    implementation libs.oplus.appcompat.tablayout
    implementation libs.oplus.appcompat.chip
    implementation libs.oplus.appcompat.preference
    implementation libs.oplus.appcompat.panel
    implementation libs.androidx.lifecycle.viewmodel.ktx
    implementation libs.koin.android

    implementation project(':Common')
    implementation project(':Encrypt')
    implementation project(':FileOperate')
    implementation project(':Provider')
    implementation project(':SelectDir')
    implementation project(':LabelManager')
    implementation project(':framework:DFM')
    implementation project(':CategoryRemoteDevice')

    if (prop_use_prebuilt_drap_drop_lib.toBoolean()) {
        implementation libs.oplus.filemanager.dragDrop
    } else {
        implementation project(':exportedLibs:DragDropSelection')
    }
}