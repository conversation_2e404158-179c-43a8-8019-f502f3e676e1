/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AsyncOperationInfo
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/8 17:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/9/8       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.reply

import androidx.annotation.VisibleForTesting
import com.oplus.fileservice.operate.convert.OperateRequest
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.reply.AsyncOperationInfo.Companion.STATE_FAILURE
import com.oplus.fileservice.operate.reply.AsyncOperationInfo.Companion.STATE_SUCCESS
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

@OptIn(ExperimentalSerializationApi::class)
private val jsonEncodeDefaults = Json {
    encodeDefaults = true
    explicitNulls = false
    coerceInputValues = true
}

private const val APP_ID = "file"
private const val STORAGE_PATH = "/storage/emulated/0/"

@Serializable
data class AsyncOperationInfo(
    val id: String,
    val appId: String,
    val category: Int = 3,
    val operation: String,
    var source: String? = null,
    var target: String? = null,
    val state: Int
) {
    companion object {
        const val STATE_SUCCESS = 0
        const val STATE_FAILURE = 1
    }
}

fun generateReplyInfoByOperateResult(
    id: String,
    request: OperateRequest,
    operationResult: OperationResult
): String {
    val targetFile = mutableSetOf<String>()
    if ((request.targetFileInfo == null) || (request.targetFileInfo?.data?.isEmpty() == true)) {
        request.sourceFileInfo?.let { fileInfoList ->
            fileInfoList.forEach { fileInfo ->
                fileInfo.data?.let(targetFile::add)
            }
        }
    } else {
        request.targetFileInfo?.data?.let(targetFile::add)
    }
    if (targetFile.isEmpty()) {
        targetFile.add(STORAGE_PATH)
    }
    val operateType = request.operateType.toString()
    val operation = AsyncOperationInfo(
        id = id,
        appId = APP_ID,
        operation = operateType,
        target = targetFile.joinToString(),
        state = if (operationResult.success) STATE_SUCCESS else STATE_FAILURE
    )
    return jsonEncodeDefaults.encodeToString(operation)
}

@VisibleForTesting
fun generateReplyInfoById(
    id: String,
    operateType: Int,
    paths: HashSet<String?>
): String {
    val operation = AsyncOperationInfo(
        id = id,
        appId = APP_ID,
        operation = operateType.toString(),
        target = paths.joinToString(),
        state = STATE_SUCCESS
    )
    return jsonEncodeDefaults.encodeToString(operation)
}
