/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - EraseOperationListener.java
 ** Description: Erase Operation Listener
 ** Version: 1.0
 ** Date : 2020/03/10
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/03/10    1.0     create
 ****************************************************************/
package com.filemanager.recyclebin.operation.listener

import android.app.Activity
import android.os.Message
import androidx.activity.ComponentActivity
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.wrapper.RecycleBinLiveData
import com.coui.appcompat.progressbar.COUIHorizontalProgressBar
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import com.filemanager.recyclebin.operation.BaseOperation
import com.filemanager.recyclebin.operation.BaseOperation.OperationParameters
import com.filemanager.recyclebin.operation.BaseOperation.OperationResult
import com.filemanager.recyclebin.operation.RecycleBinOperationListener
import com.oplus.recyclebin.R

class EraseOperationListener(activity: ComponentActivity, var mListener: RecycleBinOperationListener? = null)
    : OperationListener(activity) {
    companion object {
        private const val TAG = "EraseOperationListener"

        private fun createProgressDialog(activity: Activity): AlertDialog? {
            if (activity.isFinishing || activity.isDestroyed) {
                Log.w(TAG, "createProgressDialog ACTIVITY INVALID")
                return null
            }
            val dialog = COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Progress)
                .setTitle(com.filemanager.common.R.string.dialog_deleting)
                .setCancelable(false).create()
            dialog.setCanceledOnTouchOutside(false)
            return dialog
        }
    }

    private var mProgressDialog: AlertDialog? = null

    override fun innerHandleMessage(message: Message, activity: ComponentActivity) {
        Log.d(TAG, "handleMessage what : ${message.what}")
        mWeakActivity.get() ?: return
        when (message.what) {
            MSG_SHOW_PROGRESS_DIALOG -> {
                mProgressDialog?.show()
            }
            MSG_UPDATE_PROGRESS_DIALOG -> {
                if (message.obj is Int) {
                    mProgressDialog?.window?.findViewById<COUIHorizontalProgressBar>(com.support.dialog.R.id.progress)?.progress =
                        (message.obj as Int)
                }
            }
        }
    }

    override fun onProgressStart() {
        Log.d(TAG, "onProgressStart")
        mWeakActivity.get()?.let {
            mProgressDialog?.let {
                if (it.isShowing) {
                    it.dismiss()
                }
                mProgressDialog = null
            }
            mProgressDialog = createProgressDialog(it)
            mMainHandler.sendEmptyMessageDelayed(MSG_SHOW_PROGRESS_DIALOG, MESSAGE_DELAY_TIME)
        }
    }

    override fun onProgressUpdate(progress: Int?) {
        Log.d(TAG, "onProgressUpdate")
        progress?.let {
            mProgressDialog?.let {
                mMainHandler.removeMessages(MSG_UPDATE_PROGRESS_DIALOG)
                if (it.isShowing) {
                    it.window?.findViewById<COUIHorizontalProgressBar>(com.support.dialog.R.id.progress)?.progress = progress
                } else {
                    mMainHandler.sendMessageDelayed(mMainHandler.obtainMessage(MSG_UPDATE_PROGRESS_DIALOG, progress),
                            MESSAGE_DELAY_TIME)
                }
            }
        }
    }

    override fun onProgressComplete(parameters: OperationParameters?, result: OperationResult) {
        Log.d(TAG, "onProgressComplete")
        mMainHandler.removeCallbacksAndMessages(null)
        try {
            mProgressDialog?.let { dialog ->
                dialog.dismiss()
            }
        } catch (e: Exception) {
        }
        mProgressDialog = null

        mListener?.let {
            it.onOperationCompleted(RecycleBinLiveData.RECYCLE_DELETE, result)
        }

        if (result.mStatusCode != BaseOperation.STATUS_SUCCESS) {
            CustomToast.showLong(com.filemanager.common.R.string.toast_delete_file_exception)
        }
    }

    override fun recycle() {
        mListener = null
        try {
            mProgressDialog?.dismiss()
        } catch (e: Exception) {
        }
        mProgressDialog = null
        super.recycle()
    }
}