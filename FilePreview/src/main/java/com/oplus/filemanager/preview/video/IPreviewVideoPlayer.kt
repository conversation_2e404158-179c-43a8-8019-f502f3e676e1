/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IPreviewVideoPlayer.kt
 * Description:
 *     The interface for video player
 *
 * Version: 1.0
 * Date: 2024-09-14
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-09-14   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.video

import android.content.Context
import android.view.SurfaceView
import android.view.TextureView
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayErrorListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayPreparedListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayProgressUpdateListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayStateChangedListener

internal interface IPreviewVideoPlayer : IPreviewAudioPlayer {

    override fun setOnPlayPreparedListener(listener: OnPlayPreparedListener): IPreviewVideoPlayer

    override fun setOnPlayErrorListener(listener: OnPlayErrorListener): IPreviewVideoPlayer

    override fun setOnPlayStateChangedListener(listener: OnPlayStateChangedListener): IPreviewVideoPlayer

    override fun setOnPlayProgressUpdateListener(
        listener: OnPlayProgressUpdateListener
    ): IPreviewVideoPlayer

    override fun setPlayProgressUpdateInterval(interval: Long): IPreviewVideoPlayer

    fun setOnVideoSizeChangedListener(listener: OnVideoSizeChangedListener): IPreviewVideoPlayer

    fun setVideoSurfaceView(surfaceView: SurfaceView?): IPreviewVideoPlayer

    fun setVideoTextureView(textureView: TextureView?): IPreviewVideoPlayer

    override fun setPrepareTimeout(timeout: Long): IPreviewVideoPlayer

    fun setVideoSizeDecodeTimeout(timeout: Long): IPreviewVideoPlayer

    fun interface OnVideoSizeChangedListener {
        fun onVideoSizeChanged(width: Int, height: Int)
    }

    companion object {
        @JvmStatic
        fun obtain(context: Context): IPreviewVideoPlayer = TBLPreviewVideoPlayer(context)
    }
}