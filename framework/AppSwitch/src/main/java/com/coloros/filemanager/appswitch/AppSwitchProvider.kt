/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : AppSwitchProvider.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/27
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch

import android.content.ContentProvider
import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import com.coloros.filemanager.appswitch.util.DataSyncUtil
import com.filemanager.common.utils.HansFreezeManager
import com.oplus.app.OplusAppEnterInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class AppSwitchProvider : ContentProvider() {

    companion object {

        const val TAG = "AppSwitchProvider"

        // 方法：请求应用切换配置
        const val REQUEST_APP_SWITCH_CONFIG = "request_app_switch_config"
        const val KEY_PACKAGE_LIST = "packageList"
        const val KEY_ACTIVITY_LIST = "activityList"

        // 方法：场景智能监听到应用切换，通知到文管的方法
        const val NOTIFY_APP_SWITCH_UPDATE = "notify_app_switch_update"
        const val KEY_PROVIDER_NAME = "providerName"
        const val KEY_EVENT_NAME = "eventName"
        const val KEY_TARGET_NAME = "targetName"
        const val KEY_SOURCE_INFO = "sourceInfo"

        const val EVENT_APP_ENTER = "appEnter"
        const val EVENT_APP_EXIT = "appExit"
        const val EVENT_ACTIVITY_ENTER = "activityEnter"
        const val EVENT_ACTIVITY_EXIT = "activityExit"

        //方法：充电，空闲时，收索中台通知文管进行索引同步更新处理
        const val NOTIFY_APP_INDEX_SYNC_WORK = "syncIndexData"
        const val SYNC_TYPE_1 = 1
        const val SYNC_TYPE_2 = 2
    }


    override fun onCreate(): Boolean {
        Log.d(TAG, "AppSwitchProvider onCreate")
        return true
    }


    override fun call(method: String, arg: String?, extras: Bundle?): Bundle? {
        Log.i(TAG, "call method $method, arg $arg, extra $extras")
        checkAndDelayFroozen(context)
        if (REQUEST_APP_SWITCH_CONFIG == method) {
            //场景智能被清楚数据之后，重新启动之后会调用这个方法，到文管获取相应监听界面的package和activity的配置项。
            return onRequestAppSwitchConfig(method, arg, extras)
        } else if (NOTIFY_APP_SWITCH_UPDATE == method) {
            //场景智能监听到App切换场景，调用这个方法通知业务方
            onAppSwitchCalled(extras)
        } else if (NOTIFY_APP_INDEX_SYNC_WORK == method) {
            //dmp拉起触发，24小时空闲时触发
            context?.let { onSyncDataMethodCalled(it, method, arg, extras) }
        }
        return super.call(method, arg, extras)
    }

    private fun onAppSwitchCalled(extras: Bundle?) {
        val providerName = extras?.getString(KEY_PROVIDER_NAME, null)
        val eventName = extras?.getString(KEY_EVENT_NAME, null)
        val targetName = extras?.getString(KEY_TARGET_NAME, null)
        Log.i(TAG, "onAppSwitchCalled $providerName, eventName $eventName, targetName $targetName")
        if (eventName == EVENT_ACTIVITY_ENTER) {
            val appEnterInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                extras.getParcelable(
                    KEY_SOURCE_INFO,
                    OplusAppEnterInfo::class.java
                )
            } else {
                extras.getParcelable(KEY_SOURCE_INFO)
            }
            if (appEnterInfo != null) {
                AppSwitchManager.processAppEnterInfo(appEnterInfo)
                Log.i(TAG, "onAppSwitchCalled activityEnter $appEnterInfo")
            }
        }
    }


    private fun onRequestAppSwitchConfig(method: String, arg: String?, extras: Bundle?): Bundle {
        if (AppSwitchManager.getCurrentConfig() == null) {
            AppSwitchManager.initAppSwitchManagerAsync(true)
        }
        Log.i(TAG, "onRequestAppSwitchConfig method $method, packages ${listOf(AppSwitchManager.getCurrentConfig()?.packages)}, activity" +
                " ${listOf(AppSwitchManager.getCurrentConfig()?.activities)}")
        // 省略安全校验等测试代码,这里需要从
        return Bundle().apply {
            // 才需要添加KEY_PACKAGE_LIST，不支持监听所有App
            putStringArrayList(
                KEY_PACKAGE_LIST,
                ArrayList(
                    AppSwitchManager.getCurrentConfig()?.packages ?: mutableListOf<String>()
                )
            )
            // 才需要添加KEY_ACTIVITY_LIST
            putStringArrayList(
                KEY_ACTIVITY_LIST,
                ArrayList(AppSwitchManager.getCurrentConfig()?.activities ?: mutableListOf())
            )
        }
    }


    private fun onSyncDataMethodCalled(context: Context, method: String, arg: String?, extras: Bundle?) {
        Log.i(TAG, "onSyncDataMethodCalled method $method, arg $arg, extras $extras")
        GlobalScope.launch(Dispatchers.IO) {
            DataSyncUtil.doSyncDataBetweenDmpAndFileManager(context)
        }
    }

    /**
     * 加上防冻结的处理，AppProvider被拉起来之后，加入防冻结
     */
    private fun checkAndDelayFroozen(context: Context?) {
        context?.let {
            if (HansFreezeManager.instance.getFrozenDelayTime(it) == 0L) {
                HansFreezeManager.instance.keepBackgroundRunning(it)
                Log.i(TAG, "checkAndDelayFroozen request delay freeze")
            }
        }
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        return null
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        return 0
    }

    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<out String>?
    ): Int {
        return 0
    }
}