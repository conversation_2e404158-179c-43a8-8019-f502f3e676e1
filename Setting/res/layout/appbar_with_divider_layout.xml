<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.COUIDividerAppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/appbar_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/couiColorBackgroundWithCard"
    app:elevation="0dp">

    <com.coui.appcompat.toolbar.COUIToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@null"
        android:minHeight="@dimen/toolbar_min_height"
        app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

    <View
        android:id="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/divider_height"
        android:layout_gravity="center_horizontal"
        android:layout_marginLeft="@dimen/common_margin"
        android:layout_marginRight="@dimen/common_margin"
        android:alpha="0"
        android:background="?attr/couiColorDivider"
        android:forceDarkAllowed="false" />
</com.google.android.material.appbar.COUIDividerAppBarLayout>