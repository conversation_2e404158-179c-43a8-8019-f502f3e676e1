{"layout_width": "match_parent", "layout_height": "match_parent", "package": "com.coloros.filemanager", "background": "@drawable/ic_card_background", "id": "container", "type": "constraint", "child": [{"padding": "0dp", "layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "@dimen/dimen_32dp", "textSize": "@dimen/font_size_12", "textColor": "?attr/couiColorLabelOnColor", "layout_marginEnd": "@dimen/dimen_78dp", "layout_marginStart": "@dimen/dimen_78dp", "layout_marginBottom": "@dimen/dimen_16dp", "type": "button", "layout_constraintStart_toStartOf": "parent", "layout_marginTop": "@dimen/dimen_30dp", "layout_constraintBottom_toBottomOf": "parent", "background": "@drawable/card_view_bg", "id": "view", "text": "@string/card_view"}, {"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "paddingEnd": "@dimen/dimen_16dp", "paddingStart": "@dimen/dimen_16dp", "id": "tips", "gravity": "center", "text": "@string/card_agree_privacy", "layout_constraintTop_toTopOf": "parent", "type": "text", "maxLines": "2", "ellipsize": "end", "fontFamily": "sans-serif-medium", "textColor": "?attr/couiColorLabelPrimary", "textAppearance": "@style/couiTextHeadlineXS", "layout_constraintStart_toStartOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "layout_constraintVertical_bias": "0.38"}]}