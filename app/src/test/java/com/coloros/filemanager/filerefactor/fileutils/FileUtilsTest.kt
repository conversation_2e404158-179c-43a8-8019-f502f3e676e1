package com.coloros.filemanager.filerefactor.fileutils

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.CommonConstants.P7ZIP
import com.filemanager.common.constants.CommonConstants.UNKNOWN_TYPE
import com.filemanager.common.fileutils.contains
import com.filemanager.common.fileutils.fetchFileName
import com.filemanager.common.fileutils.getAllFilePathFromDirectory
import com.filemanager.common.fileutils.getChildFromFile
import com.filemanager.common.fileutils.getCompressFileType
import com.filemanager.common.fileutils.getDisplayNameByString
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.MimeTypeHelper
import org.junit.Assert
import org.junit.Test
import java.io.File

class FileUtilsTest {

    companion object {
        private const val SDCARD_PATH = "/sdcard"
    }

    @Test
    fun should_verity_fetchFileName() {
        val fileBean = BaseFileBean()
        Assert.assertNull(fetchFileName(fileBean, "", ""))
        fileBean.mData = "there is parent dir"
        Assert.assertNotNull(fetchFileName(fileBean, "", ""))
        Assert.assertNotNull(fetchFileName(fileBean, "file name", ""))
        Assert.assertNotNull(fetchFileName(fileBean, "file name", ".ext"))
    }

    @Test
    fun should_verity_hasDrmFile() {
        val files = mutableListOf<BaseFileBean>()
        Assert.assertFalse(hasDrmFile(files))
        files.add(BaseFileBean())
        Assert.assertFalse(hasDrmFile(files))
        val file = BaseFileBean()
        file.mLocalType = MimeTypeHelper.DRM_TYPE
        files.add(file)
        Assert.assertTrue(hasDrmFile(files))
    }

    @Test
    fun should_verity_getDisplayNameByString() {
        Assert.assertEquals("", getDisplayNameByString(""))
        Assert.assertEquals("there is display name", getDisplayNameByString("/there is display name"))
    }

    @Test
    fun should_verity_contains() {
        Assert.assertFalse(contains(null, null))
        val path = "path"
        Assert.assertTrue(contains(path, path))
        Assert.assertTrue(contains("path", "path/file"))
        Assert.assertFalse(contains("path", null))
        Assert.assertFalse(contains("path", "file"))
    }

    @Test
    fun should_verity_getCompressType() {
        val fileBean = BaseFileBean()
        fileBean.mData = "aaa.7z"
        Assert.assertEquals(P7ZIP, getCompressFileType(fileBean))
        fileBean.mData = ""
        fileBean.mLocalFileUri = null
        Assert.assertEquals(UNKNOWN_TYPE, getCompressFileType(fileBean))
    }

    @Test
    fun should_verify_getChildFromFile() {
        val filePath = "$SDCARD_PATH/Download/bilibili.apk"
        val file = File(filePath)
        val result = getChildFromFile(file)
        Assert.assertEquals(1, result.size)
    }

    @Test
    fun should_verify_getChildFromFile_directory() {
        val filePath = "$SDCARD_PATH/Download"
        val file = File(filePath)
        if (file.mkdirs()) {
            println("file  mkdir")
        }
        val result = getChildFromFile(file)
        Assert.assertEquals(0, result.size)
    }

    @Test
    fun should_verify_getAllFilePathFromDirectory() {
        val filePath = "$SDCARD_PATH/Download/bilibili.apk"
        val file = File(filePath)
        val result = getAllFilePathFromDirectory(file)
        Assert.assertEquals(1, result.size)
    }
}