/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SuperAppScanner
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/21       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.scanner

import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.BlacklistParser
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import java.io.File

class SuperAppScanner(
    paths: Array<String>,
    dirDepth: Int = DEFAULT_DIR_DEPTH,
    sortOrder: Int,
    pageNo: Int,
    pageSize: Int,
    isNeedThumbnail: Boolean = true
) : BasePathScanner(pageNo, pageSize, isNeedThumbnail) {
    companion object {
        const val TAG = "SuperAppScanner"
        const val DEFAULT_DIR_DEPTH = 0
    }

    private var mSuperPath: Array<String> = paths
    private val mInternalPath: String? =
        VolumeEnvironment.getInternalSdPath(appContext)
    private val mExternalPath: String? =
        VolumeEnvironment.getExternalSdPath(appContext)
    private var mInternalPath999: String? = null
    private val mTempFileList = ArrayList<BaseFileBean>()
    private val mDirDepth = dirDepth
    private var mSortOrder = sortOrder
    private var mLastSortOrder: Int = SortHelper.FILE_NAME_ORDER

    init {
        if (FeatureCompat.sIsSupportMultiApp) {
            mInternalPath999 = KtConstants.LOCAL_VOLUME_MULTI_APP_PATH
            Log.d(TAG, "MultiAppPath: ${this.mInternalPath999}")
        }
    }

    override fun createFromPath(
        volume: String,
        parentPath: String,
        path: String
    ): List<BaseFileBean> {
        val parentPathWithSeparator = if (parentPath.endsWith(File.separator)) {
            parentPath
        } else {
            parentPath.plus(File.separator)
        }
        val file = PathFileWrapper(volume.plus(File.separator).plus(parentPathWithSeparator).plus(path))
        mTempFileList.clear()
        if (file.mIsDirectory) {
            try {
                if (mDirDepth > DEFAULT_DIR_DEPTH) {
                    mTempFileList.addAll(traverseAllFiles(file.mData, mDirDepth) {
                        filterFile(it)
                    })
                } else {
                    recursiveFileDirectory(file)
                }
            } catch (e: StackOverflowError) {
                Log.e(TAG, e.message)
            }
        } else {
            if (filterFile(file)) {
                mTempFileList.add(file)
            }
        }
        return mTempFileList
    }

    override fun getPath(): Array<String> {
        return mSuperPath
    }

    override fun getVolume(): List<String> {
        val volume = ArrayList<String>()
        mInternalPath?.let {
            volume.add(it)
        }
        mExternalPath?.let {
            volume.add(it)
        }
        mInternalPath999?.let {
            volume.add(it)
        }
        return volume
    }

    override fun getFilterList(): List<Int>? {
        return null
    }

    /**
     *
     * @param path root path
     * @param maxDepth the maximum of a directory tree to traverse. By default there is no limit
     */
    private fun traverseAllFiles(
        path: String?,
        maxDepth: Int = Int.MAX_VALUE,
        includeDir: Boolean = false,
        filter: ((PathFileWrapper) -> Boolean)? = null
    ): List<PathFileWrapper> {

        val fileList = arrayListOf<PathFileWrapper>()

        path?.let { p ->
            File(p).walk().maxDepth(maxDepth)
                .filter { includeDir || it.isFile }
                .filter {
                    HiddenFileHelper.isNeedShowHiddenFile() || HiddenFileHelper.isHiddenFile(
                        it.name
                    ).not()
                }
                .forEach {
                    val bean = PathFileWrapper(it.absolutePath)
                    if (filter?.invoke(bean) != false) {
                        fileList.add(bean)
                    }
                }
        }

        return fileList
    }


    private fun recursiveFileDirectory(baseFileBean: BaseFileBean) {
        val fileList =
            JavaFileHelper.listFileBeans(baseFileBean, !HiddenFileHelper.isNeedShowHiddenFile())
        if (fileList != null) {
            for (file in fileList) {
                if (file.mIsDirectory) {
                    recursiveFileDirectory(file)
                } else {
                    if (filterFile(file)) {
                        mTempFileList.add(file)
                    }
                }
            }
        }
    }

    @VisibleForTesting
    fun filterFile(file: BaseFileBean): Boolean {
        val subFileName = file.mDisplayName
        if (subFileName.isNullOrEmpty()) {
            return false
        }

        if ((BlacklistParser.sFilterNoSuffixFile == BlacklistParser.FILTER_NO_SUFFIX_STATE)
            && BlacklistParser.isFilterNoSuffixFile(subFileName)
        ) {
            return false
        }
        return true
    }

    override fun preHandleResultBackground(list: List<BaseFileBean>): List<BaseFileBean> {
        Injector.injectFactory<IDocumentExtensionType>()?.sortFileIgnoreHeadLabel(list, mSortOrder, mLastSortOrder, true)
        mLastSortOrder = mSortOrder
        return list
    }
}