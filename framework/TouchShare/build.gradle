plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace 'com.oplus.filemanager.framework.touchshare'
}

dependencies {
    implementation libs.androidx.appcompat
    implementation libs.google.material

    implementation libs.koin.android
    implementation (libs.heytap.accessory.share) {
        exclude group: 'com.google.protobuf', module: 'protobuf-javalite'
    }
    implementation libs.google.protobuf.java

    implementation project(":Common")
}