/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor
 * * Version     : 1.0
 * * Date        : 2020/2/12
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir.filebrowser

import android.content.Context
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BasePathLoader
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.interfaze.main.IMain
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.io.File
import java.util.*

class SelectFileBrowserLoader(context: Context, path: String) :
    BasePathLoader<Int, BaseFileBean>(context) {

    private var mLoadPath: String? = path
    private var mHideFileLabel = false

    init {
        initData()
    }

    fun setPath(path: String) {
        mLoadPath = path
    }

    fun setHideFileLabel(hide: Boolean) {
        mHideFileLabel = hide
    }

    @Suppress("TooGenericExceptionCaught")
    override fun createFromPath(volume: String, parentPath: String, path: String): List<BaseFileBean> {
        val fullPath = parentPath.plus(File.separator).plus(path)
        val file = PathFileWrapper(fullPath)
        if (parentPath == AndroidDataHelper.ANDROID_DATA_PATH || parentPath == AndroidDataHelper.ANDROID_DATA_NORMAL_PATH) {
            file.originPackage = path
        }
        return arrayListOf(file)
    }

    override fun preHandleResultBackground(list: List<BaseFileBean>): List<BaseFileBean> {
        mLoadPath?.apply {
            if (AndroidDataHelper.isAndroidDataPath(this)) {
                if (AndroidDataHelper.openAndroidData == true) {
                    GlobalScope.launch(Dispatchers.IO) {
                        Log.d(TAG, "preHandleResultBackground data size ${AndroidDataHelper.installedNameMap?.size}")
                    }
                } else {
                    return mutableListOf()
                }
            }
        }
        val currentSort = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getBrowserKey(mLoadPath))
        val isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getBrowserKey(mLoadPath))
        val mLastSort = SortModeUtils.getSharedSortMode(appContext, SortModeUtils.BROWSER_LAST_SORT_RECORD)
        Injector.injectFactory<IDocumentExtensionType>()?.sortFiles(list, currentSort, mLastSort, mHideFileLabel, isDesc)
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.findFileLabelIfHad(list)
        return list
    }

    override fun getVolume(): List<String>? {
        return null
    }

    override fun getPath(): Array<String> {
        return Array(1) { mLoadPath!! }
    }

    override fun getFilterList(): List<Int>? {
        return null
    }

    override fun getItemKey(item: BaseFileBean): Int? {
        item.let {
            val path = item.mData
            if (path.isNullOrEmpty()) {
                return null
            }
            return path.lowercase(Locale.getDefault()).hashCode()
        }
    }
}