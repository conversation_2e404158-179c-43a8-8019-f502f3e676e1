/*********************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : TestConstants
 * Description :
 * Version     : 1.0
 * Date        : 2023/08/01
 * Author      : W9002848
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * W9002848        2023/08/03       1.0      create
 **********************************************************************/
package com.coloros.filemanager.utils

object TestConstants {
    private const val PATH_TEST_ROOT = "/storage/emulated/0/"
    ///storage/emulated/0/Documents/
    const val FILE_TXT_TYPE = ".txt"
    const val FILE_TXT_NAME = "FileOpenTimeTest"
    const val PATH_FILE_MANAGER_TEST = "${PATH_TEST_ROOT}Documents/FileManagerTest/"

    const val FILE_OPEN_TIME_KEY = "file_time"
    const val FILE_OPEN_TIME_METHOD = "getAllFile"
    const val FILE_OPEN_TIME_URI = "content://com.oplus.filemanager.FileManagerOpenProvider"

    const val FILE_TYPE_BOTH = "file_type_both"
    const val FILE_TYPE_IMAGE = "file_type_image"
    const val FILE_TYPE_VIDEO = "file_type_video"
    const val TIME_WAIT_ONE_SEC = 1000L
    const val TIME_WAIT_TWO_SEC = 2000L
    const val TIME_WAIT_THREE_SEC = 3000L

    //1、用户授权：应用权限弹窗
    const val TEXT_APP_NAME_CN = "文件管理"
    const val TEXT_AGREE_AND_USE_CN = "同意并使用"
    const val TEXT_NOTICE_TO_USERS_CN = "用户须知"

    const val TEXT_APP_NAME_US = "My Files"
    const val TEXT_AGREE_AND_USE_US = "Agree and continue"
    const val TEXT_NOTICE_TO_USERS_US = "Statement of Use"

    //2、开启"开启“访问所有文件”权限"弹窗
    const val TEXT_OPEN_ACCESS_TITLE_CN = "开启“访问所有文件”权限"
    const val TEXT_OPEN = "去开启"

    const val TEXT_OPEN_ACCESS_TITLE_US = "Turn on \"All files access\""
    const val TEXT_OPEN_US = "Settings"

    //3、用户授权：所有文件访问权限
    const val TEXT_ALL_FILES_ACCESS_TILE_CN = "所有文件访问权限"
    const val TEXT_ALL_FILES_ACCESS_CN = "授予管理所有文件的权限"
    const val TEXT_CONTENT_CN = "点击卡片查看设备中存储的全部文件"
    const val TEXT_ALL_FILES_ACCESS_TILE_US = "All files access"
    const val TEXT_ALL_FILES_ACCESS_US = "Allow access to manage all files"
    const val RES_ID_COUI_TOOLBAR_BACK = "com.android.settings:id/coui_toolbar_back_view"
    const val RES_CONTENT_DISMISS_IV = "com.coloros.filemanager:id/dismissIv"
    const val RES_CONTENT_TV = "com.coloros.filemanager:id/contentTv"
    const val TEXT_CONTENT_US = "Tap the card to view all files on your device."

    //4、用户授权：获取系统权限弹窗
    const val TEXT_PERMIT_READ_TITLE_CN = "允许“文件管理”读取应用列表?"
    const val TEXT_PERMIT_READ_CN = "允许"
    const val TEXT_PERMIT_READ_TITLE_US = "Allow \"My Files\" to read your app list?"
    const val TEXT_PERMIT_READ_US = "Allow"

}