/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - VideoPreviewFragment.kt
 * Description:
 *     The fragment for preview video file.
 *
 * Version: 1.0
 * Date: 2024-09-19
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-09-19   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.video

import android.content.Context
import android.view.View
import androidx.fragment.app.Fragment
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewFragment
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite
import com.oplus.filemanager.preview.widget.PreviewFilePathItem

internal class VideoPreviewFragment :
    AbsPreviewFragment<VideoPreviewViewModel>(R.layout.fragmen_preview_video) {

    private companion object {
        private const val TAG = "VideoPreviewFragment"
    }

    override val logTag: String = TAG
    override val fragmentInstance: Fragment = this
    override val viewModelClass: Class<VideoPreviewViewModel> = VideoPreviewViewModel::class.java
    override val operationsBarId: Int = R.id.preview_operations_bar

    private var filePreview: IVideoFilePreview? = null

    override fun isPreviewFileApproved(context: Context, fileBean: BaseFileBean): Boolean =
        FilePreviewTypeCheckHelper(context).isVideoFileType(fileBean, TAG)

    override fun onViewModelCreated(view: View, viewModel: VideoPreviewViewModel) {
        filePreview = prepareFilePreview(view, viewModel)
    }

    override fun onCreateFilePathItem(view: View): PreviewFilePathItem {
        val item: PreviewFilePathItem = view.findViewById(R.id.preview_remote_location_info)
        item.setLabelGone()
        return item
    }
    override fun onDestroyView() {
        super.onDestroyView()
        filePreview?.release()
        filePreview = null
    }

    private fun prepareFilePreview(view: View, viewModel: VideoPreviewViewModel): IVideoFilePreview {
        val previewImpl = IVideoFilePreview.obtain(view.context, viewLifecycleOwner, viewModel)
        previewImpl.attachToContainer(VideoPreviewContainerManager(view))
        return previewImpl
    }

    override fun releasePreview() {
        previewModel?.releaseVideoPlayer()
    }
}