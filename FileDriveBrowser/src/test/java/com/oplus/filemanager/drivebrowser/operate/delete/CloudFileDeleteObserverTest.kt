/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudFileDeleteObserverTest
 ** Description : CloudFileDeleteObserver Unit Test
 ** Version     : 1.0
 ** Date        : 2024/01/11 14:15
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/01/11       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.operate.delete

import android.app.Activity
import android.app.Dialog
import androidx.fragment.app.FragmentActivity
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.utils.CustomToast
import com.filemanager.fileoperate.base.ACTION_CANCELLED
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import io.mockk.every
import io.mockk.just
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test

class CloudFileDeleteObserverTest {

    private lateinit var activity: FragmentActivity
    private lateinit var observer: CloudFileDeleteObserver

    @Before
    fun setup() {
        activity = mockk()
        every { activity.applicationContext }.returns(activity)
        mockkObject(MyApplication)
        every { appContext }.returns(activity)
        observer = mockk(relaxed = true)
        mockkStatic(CustomToast::class)
    }

    @After
    fun teardown() {
        unmockkObject(MyApplication)
        unmockkStatic(CustomToast::class)
    }

    @Test
    fun should_return_true_when_onChanged_SHOW_DELETE_CONFIRM_DIALOG() {
        every { observer.onChanged(any(), any()) }.answers { callOriginal() }
        justRun { observer.showDeleteConfirmDialog(any(), any()) }
        justRun { observer.dismissDialog() }

        var result = observer.onChanged(activity, Pair(SHOW_DELETE_CONFIRM_DIALOG, Any()))
        Assert.assertTrue(result)
        verify(inverse = true) { observer.showDeleteConfirmDialog(activity, any()) }

        val deleteBean = CloudFileDeleteObserver.FileDeleteBean(1, "tencent", null)
        result = observer.onChanged(activity, Pair(SHOW_DELETE_CONFIRM_DIALOG, deleteBean))
        Assert.assertTrue(result)
        verify { observer.showDeleteConfirmDialog(activity, deleteBean) }
    }

    @Test
    fun should_return_false_when_onChanged() {
        every { observer.onChanged(any(), any()) }.answers { callOriginal() }
        justRun { observer.showDeleteConfirmDialog(any(), any()) }
        justRun { observer.actionFailed(any()) }
        justRun { CustomToast.showShort(any<Int>()) }

        var result = observer.onChanged(activity, Pair(ACTION_FAILED, Any()))
        Assert.assertFalse(result)
        verify { observer.actionFailed(any()) }

        result = observer.onChanged(activity, Pair(ACTION_DONE, Any()))
        Assert.assertFalse(result)
        verify(atLeast = 1) { observer.dismissDialog() }

        result = observer.onChanged(activity, Pair(ACTION_CANCELLED, Any()))
        Assert.assertFalse(result)
        verify(atLeast = 2) { observer.dismissDialog() }

        result = observer.onChanged(activity, Pair(-2000, Any()))
        Assert.assertFalse(result)
    }

    @Ignore("Wait for correct")
    @Test
    fun should_call_dismissDialog_when_recycle() {
        every { observer.recycle() }.answers { callOriginal() }
        justRun { observer.dismissProgressDialog() }
        justRun { observer.dismissDialog() }

        observer.recycle()
        verify { observer.dismissDialog() }
    }

    @Test
    fun should_call_dismiss_when_dismissDialog() {
        val dialog = mockk<Dialog>()
        justRun { dialog.dismiss() }
        every { observer.dismissDialog() }.answers { callOriginal() }
        every { observer.deleteConfirmDialog }.returns(null)
        observer.dismissDialog()
        verify(inverse = true) { dialog.dismiss() }

        every { observer.deleteConfirmDialog }.returns(dialog)
        every { dialog.isShowing }.returns(false)
        observer.dismissDialog()
        verify(inverse = true) { dialog.dismiss() }

        every { dialog.isShowing }.returns(true)
        observer.dismissDialog()
        verify { dialog.dismiss() }
    }

    @Test
    fun should_return_boolean_when_isTencentDoc() {
        var data = CloudFileDeleteObserver.FileDeleteBean(0, "kingsoft", null)
        Assert.assertFalse(data.isTencentDoc())

        data = CloudFileDeleteObserver.FileDeleteBean(0, "tencent", null)
        Assert.assertTrue(data.isTencentDoc())
    }

    @Test
    fun `should not call toast when call actionFailed if is not auth`() {
        //setup
        mockkStatic(CustomToast::class)
        //given
        val result = Pair(false, false)
        every { CustomToast.showShort(any<Int>()) } just runs
        val activity = mockk<Activity>()
        val observer = CloudFileDeleteObserver(activity)
        mockkObject(observer)
        every { observer.dismissDialog() } just runs
        //when
        observer.actionFailed(result)
        //then
        verify(inverse = true) { CustomToast.showShort(any<Int>()) }
        //teardown
        unmockkStatic(CustomToast::class)
    }

    @Test
    fun `should call toast when call actionFailed if is auth`() {
        //setup
        mockkStatic(CustomToast::class)
        //given
        val result = Pair(false, true)
        every { CustomToast.showShort(any<Int>()) } just runs
        val activity = mockk<Activity>()
        val observer = CloudFileDeleteObserver(activity)
        mockkObject(observer)
        every { observer.dismissDialog() } just runs
        //when
        observer.actionFailed(result)
        //then
        verify { CustomToast.showShort(any<Int>()) }
        //teardown
        unmockkStatic(CustomToast::class)
    }
}