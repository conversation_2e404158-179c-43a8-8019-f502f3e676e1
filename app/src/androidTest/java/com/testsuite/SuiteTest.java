/*********************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : SuiteTest
 * Description : 没有传入caseID的时候筛选所有用例：
 *               在测试桌面或者AQMD上下发传人了caseID即可筛选有对应caseID注解的用例执行
 * Version     : 1.0
 * Date        : 2023/08/01
 * Author      : W9002848
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * W9002848        2023/08/07       1.0      create
 **********************************************************************/
package com.testsuite;

import com.oplus.autotest.olt.testlib.common.OplusCategory;

import org.junit.runner.RunWith;

@RunWith(OplusCategory.class)
public class SuiteTest {
}
