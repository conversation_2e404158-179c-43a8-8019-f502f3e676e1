/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowSafeUtils.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/10/23
 ** Author: <PERSON><PERSON><PERSON><PERSON>n
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/10/23      1.0     create
 ****************************************************************/
package shadow;

import com.filemanager.common.utils.SafeUtils;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(SafeUtils.class)
public class ShadowSafeUtils {
    private static boolean sIsStorageEnable;
    @Implementation
    public static boolean isStorageEnable(int type, long length, String rootPath) {
        return sIsStorageEnable;
    }

    public static void setsIsStorageEnable(boolean isStorageEnable) {
        sIsStorageEnable = isStorageEnable;
    }
}
