/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SignInfo
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/8/3 14:19
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/8/3       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.utils

data class SignInfo(
    val appKey: String? = null,
    val secret: String? = null,
    val timestamp: String? = null,
    val invokeMethodName: String? = null,
    val jsonStr: String? = null,
    val stringA: String? = null
)
