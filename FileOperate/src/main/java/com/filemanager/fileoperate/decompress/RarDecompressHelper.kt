/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: RarDecompressHelper.kt
 ** Description: Decompress rar file
 ** Version: 1.0
 ** Date: 2020/3/10
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.decompress

import android.text.TextUtils
import android.util.ArrayMap
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.thread.ThreadPriority
import com.filemanager.common.thread.ThreadType
import com.filemanager.common.utils.Log
import de.innosystec.unrar.Archive
import de.innosystec.unrar.UnrarCallback
import de.innosystec.unrar.exception.RarException
import de.innosystec.unrar.rarfile.FileHeader
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStream
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import kotlin.collections.ArrayList

class RarDecompressHelper : DecompressHelper<RarDecompressFile>() {
    private var mMultiThreadKeyList: Vector<String?>? = null

    // For multi-thread
    private var mCompletedCountInMultiThread: AtomicInteger? = null

    // Has been switch to single thread to decompress work, means don't switch it again
    private var mUsedSingleThreadWhenRarErr: AtomicBoolean? = null
    private var mErrInMultiThread: AtomicBoolean? = null

    // End for multi-thread
    private var mListener: OnDecompressListener? = null

    companion object {
        private const val TAG = "RarDecompressHelper"
        private const val MAX_COUNT_LIMIT = 200
        private const val SLEEP_TIMES: Long = 10
    }

    override fun createDecompressDirFile(path: String): RarDecompressFile {
        return RarDecompressFile(null).easyCreateDirFile(path) as RarDecompressFile
    }

    override fun internalPreview(sourceFile: BaseFileBean): Pair<Int, MutableMap<String, MutableList<RarDecompressFile>?>?> {
        var archive: Archive? = null
        try {
            archive = Archive(File(sourceFile.mData), null, false)
            var fileHeaders = archive.fileHeaders
            Log.d(TAG, "internalPreview fileHeaders.size = ${fileHeaders.size}")
            if (fileHeaders.size == 0) {
                quietClose(archive)
                archive = Archive(File(sourceFile.mData), null, false)
                fileHeaders = archive.fileHeaders
            }
            if (fileHeaders.size > MAX_COMPRESS_FILE_NUM) {
                return Pair(PREVIEW_OVERCOUNT, null)
            }

            var compressFiles = ArrayMap<String, MutableList<RarDecompressFile>?>()
            if (fileHeaders.size == 0) {
                return Pair(PREVIEW_FAILED, compressFiles)
            }
            fileHeaders.forEach {
                if (isValidPreviewFileName(RarDecompressFile.parsePath(it))) {
                    val file = RarDecompressFile(it)
                    createPreviewFileTree(file, compressFiles)
                }
            }
            return Pair(PREVIEW_SUCCESS, compressFiles)
        } catch (e: Exception) {
            Log.e(TAG, "rar file internalPreview failed: ${e.message}")
            return Pair(PREVIEW_FAILED, null)
        } finally {
            quietClose(archive)
        }
    }

    override fun internalDecompress(sourceFile: BaseFileBean, destParentFile: BaseFileBean, password: String?,
                                    selectFiles: MutableList<RarDecompressFile>?, listener: OnDecompressListener?): Boolean? {
        Log.d(TAG, "decompress file path = ${sourceFile.mData} outPutDir = ${destParentFile.mData}")
        mListener = listener
        val sourceJavaFile = File(sourceFile.mData)
        if (sourceJavaFile.exists() && isCancelled().not()) {
            val parentFile = File(destParentFile.mData.plus(
                    if (destParentFile.mData!!.endsWith(File.separator)) "" else File.separator))
            val archive: Archive
            try {
                archive = createArchive(sourceJavaFile, password)
                val hasSelectFile = selectFiles?.isNotEmpty() ?: false
                val decompressList = ArrayList<FileHeader>()
                var totalSize = 0L
                archive.fileHeaders.forEach {
                    if (!hasSelectFile || checkCanDecompress(RarDecompressFile.parsePath(it),
                                    it.isDirectory, selectFiles!!)) {
                        decompressList.add(it)
                        totalSize += it.fullUnpackSize
                    }
                    if (isCancelled()) {
                        Log.d(TAG, "internalDecompress failed: cancelled when calculate")
                        return false
                    }
                }
                saveDecompressTotalSize(totalSize)

                val fileCount = decompressList.size

                // Fix unzip the .rar compressed package to an empty folder for issue 2680793
                if (fileCount == 0) {
                    return false
                }

                Log.d(TAG, "Last decompress archive file count: $fileCount")
                val singleThread = (fileCount >= MAX_COUNT_LIMIT) || (fileCount <= 1) || !TextUtils.isEmpty(password)
                return if (singleThread) {
                    singleThreadDecompress(archive, decompressList, parentFile)
                } else {
                    multiThreadDecompress(decompressList, sourceJavaFile, password, parentFile, archive)
                    null
                }
            } catch (e: Exception) {
                Log.d(TAG, "decompress file failed, ${e.message}")
            }
        }
        Log.d(TAG, "internalDecompress return false")
        return false
    }

    @Throws(RarException::class)
    private fun decompressFileOrDir(archive: Archive, fh: FileHeader, destFile: File): Boolean {
        val name = RarDecompressFile.parsePath(fh)
        if (name.isNullOrEmpty()) {
            Log.e(TAG, "decompressFileOrDir: file name is empty")
        } else {
            val file = File(destFile, name)
            if (fh.isDirectory) {
                return forceMkdirs(file)
            } else {
                if (!file.exists()) {
                    forceMkdirs(file.parentFile)
                    if (!file.createNewFile()) {
                        Log.e(TAG, "decompressFileOrDir: createNewFile error")
                        return false
                    }
                }
                var stream: OutputStream? = null
                try {
                    stream = FileOutputStream(file)
                    archive.extractFile(fh, stream)
                } catch (rarExp: RarException) {
                    Log.e(TAG, "decompressFileOrDir failed by RarException: ${rarExp.message}")
                    throw rarExp
                } catch (e: Exception) {
                    Log.e(TAG, "decompressFileOrDir failed: ${e.message}")
                    return false
                } finally {
                    quietClose(stream)
                }
                return true
            }
        }
        return false
    }

    private fun createArchive(sourceJavaFile: File, password: String?): Archive {
        return Archive(sourceJavaFile, object : UnrarCallback {
            private var mLastPackSize: Long = 0

            override fun isNextVolumeReady(nextVolume: File?): Boolean = false

            override fun volumeProgressChanged(current: Long, total: Long) {
                val delta = current - mLastPackSize
                mLastPackSize = current
                updateDecompressProgress(delta)
            }
        }, password, false)
    }

    private fun singleThreadDecompress(archive: Archive, selectedFile: List<FileHeader>, destFile: File): Boolean {
        Log.d(TAG, "start singleThreadDecompress")
        var result = true
        run out@{
            try {
                selectedFile.forEach {
                    if (!decompressFileOrDir(archive, it, destFile) || isCancelled()) {
                        result = false
                        return@out
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "singleThreadDecompress failed: ${e.message}")
                result = false
            } finally {
                quietClose(archive)
            }
        }
        return result
    }

    private fun multiThreadDecompress(selectedFile: List<FileHeader>, sourceJavaFile: File, password: String?,
                                      destFile: File, sourceArchive: Archive) {
        fun findFileHeader(archive: Archive, sourceHeader: FileHeader): FileHeader? {
            val sourceName = RarDecompressFile.parsePath(sourceHeader)
            archive.fileHeaders.forEach {
                if (RarDecompressFile.parsePath(it) == sourceName) {
                    return it
                }
            }
            Log.d(TAG, "findFileHeader failed: ${sourceHeader.fileNameW}")
            return null
        }

        // ======================================
        Log.d(TAG, "start multiThreadDecompress")
        // Optimization: sort the file size
        if (selectedFile.size > 1) {
            selectedFile.sortedWith(Comparator { o1, o2 ->
                (o2.fullPackSize - o1.fullPackSize).toInt()
            })
        }
        mMultiThreadKeyList?.clear()
        mMultiThreadKeyList = Vector(selectedFile.size)
        mCompletedCountInMultiThread = AtomicInteger(0)
        mErrInMultiThread = AtomicBoolean(false)
        mUsedSingleThreadWhenRarErr = AtomicBoolean(false)


        run outside@{
            selectedFile.forEach {
                if (isCancelled() || mErrInMultiThread!!.get()) {
                    Log.d(TAG, "interrupt add new task")
                    return@outside
                }
                var threadKey: String? = null
                threadKey = ThreadManager.sThreadManager.execute(FileRunnable(object : Runnable {
                    override fun run() {
                        var archive: Archive? = null
                        var fileHeader: FileHeader? = null
                        try {
                            if (isCancelled() || mErrInMultiThread!!.get()) {
                                return
                            }
                            archive = createArchive(sourceJavaFile, password)
                            fileHeader = findFileHeader(archive, it)
                            if ((fileHeader == null) || !decompressFileOrDir(archive, fileHeader, destFile) || isCancelled()) {
                                mErrInMultiThread!!.set(true)
                            } else {
                                mCompletedCountInMultiThread!!.addAndGet(1)
                            }
                        } catch (e1: RarException) {
                            if (mErrInMultiThread!!.getAndSet(true)) {
                                return
                            }
                            if (e1.type == RarException.RarExceptionType.unkownError) {
                                // Some rar file not support decompress by multi-thread, use single thread again in this case
                                Log.d(TAG, "RarException.RarExceptionType.unkownError in multi-thread")
                                // May not one RarException occurred, so check has been switched to single thread first
                                if (mUsedSingleThreadWhenRarErr!!.getAndSet(true).not()) {
                                    if (isCancelled()) {
                                        return
                                    }
                                    // Some rar file not support decompress by multi-thread, use single thread again in this case
                                    Log.d(TAG, "re-decompress by single thread because of RarException in multi-thread")
                                    val result = singleThreadDecompress(sourceArchive, selectedFile, destFile)
                                    if (result == false) {
                                        Thread.interrupted()
                                        mDecompressListener?.onTryAgain()
                                        return
                                    }
                                    notifyResult(result)
                                }
                            }
                            Log.e(TAG, "RarException in multi-thread: ${fileHeader?.fileNameString}, ${e1.message}")
                        } catch (e: Exception) {
                            mErrInMultiThread!!.set(true)
                            Log.e(TAG, "other exception in multi-thread: ${fileHeader?.fileNameString}, ${e.message}")
                        } finally {
                            quietClose(archive)
                            if (mErrInMultiThread!!.get()) {
                                synchronized(RarDecompressHelper::class.java) {
                                    if (mMultiThreadKeyList.isNullOrEmpty().not()) {
                                        Log.e(TAG, "Recycle multi-thread resource because of error")
                                        if (!mUsedSingleThreadWhenRarErr!!.get()) {
                                            Log.e(TAG, "Notify decompress failed in multi-thread because of error")
                                            quietClose(sourceArchive)
                                            notifyResult(false)
                                        }
                                        // Cancel others thread because we used single thread to decompress work
                                        mMultiThreadKeyList?.remove(threadKey) // The current thread not need to cancel
                                        mMultiThreadKeyList?.forEach { key ->
                                            ThreadManager.sThreadManager.cancelThread(key)
                                        }
                                        mMultiThreadKeyList?.clear()
                                        mMultiThreadKeyList = null
                                    }
                                }
                            } else if (mCompletedCountInMultiThread!!.get() == selectedFile.size) {
                                Log.d(TAG, "Notify decompress success in multi-thread")
                                quietClose(sourceArchive)
                                notifyResult(true)
                            }
                        }
                    }

                    private fun notifyResult(result: Boolean) {
                        if (!isCancelled()) {
                            mListener?.onFinished(result)
                        }
                        recycle()
                    }
                }, TAG), ThreadType.NORMAL_THREAD, ThreadPriority.HIGH)
                mMultiThreadKeyList!!.add(threadKey)
                Thread.sleep(SLEEP_TIMES)
            }
        }
    }

    override fun internalIsEncrypted(sourceFile: BaseFileBean): Boolean {
        if (sourceFile.mData.isNullOrEmpty()) {
            return false
        }
        var arch: Archive? = null
        try {
            arch = Archive(File(sourceFile.mData), null, false)
        } catch (e: Exception) {
            Log.d(TAG, "isEncrypted create archive error: ${e.message}")
            quietClose(arch)
            return false
        }
        var result = false
        try {
            result = arch.isEncrypted
        } catch (e: Exception) {
            Log.d(TAG, "isEncrypted check by archive error: ${e.message}")
            // Jar packets may be reported to the null pointer, must capture
            // the null pointer,for example:
            // de.innosystec.unrar.Archive.isEncrypted(Archive.java:207), mainheader is null
        }
        if (result.not()) {
            Log.d(TAG, "isEncrypted check again use fileHeader")
            try {
                arch.fileHeaders.let {
                    if (it.isNotEmpty()) {
                        result = it[0].isEncrypted
                    }
                }
            } catch (e: Exception) {
                Log.d(TAG, "isEncrypted check by fileHeader error: ${e.message}")
            }
        }
        quietClose(arch)
        return result
    }

    override fun internalVerifyPassword(sourceFile: BaseFileBean, password: String?): Boolean {
        var arch: Archive? = null
        try {
            arch = Archive(File(sourceFile.mData), password, false)
            val fh: FileHeader = arch.nextFileHeader() ?: return false
            arch.extractFile(fh, object : OutputStream() {
                override fun write(i: Int) {}
            })
            arch.nextFileHeader()
            return true
        } catch (e: Exception) {
            Log.e(TAG, "verifyPwd error: ${e.message}")
            return false
        } finally {
            quietClose(arch)
        }
    }

    override fun recycle() {
        mListener = null
        super.recycle()
    }

    override  fun checkIsSupport(sourceFile: BaseFileBean): Boolean {
        var result = true
        var arch: Archive? = null
        try {
            arch = Archive(File(sourceFile.mData), null, false)
        } catch (e: Exception) {
            Log.d(TAG, "checkIsNotSupport create archive error: ${e.message}")
            quietClose(arch)
            return false
        }
        if (arch.fileHeaders.size == 0) {
            result = false
        }
        quietClose(arch)
        return result
    }
}