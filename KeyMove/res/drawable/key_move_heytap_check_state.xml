<?xml version="1.0" encoding="utf-8"?>
<animated-selector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:coui="http://schemas.android.com/apk/res-auto">
    <item
        android:id="@+id/part_normal"
        android:drawable="@drawable/coui_btn_part_check_on_normal"
        android:state_enabled="true"
        coui:coui_state_allSelect="false"
        coui:coui_state_partSelect="true" />
    <item
        android:drawable="@drawable/coui_btn_part_check_on_disabled"
        android:state_enabled="false"
        coui:coui_state_allSelect="false"
        coui:coui_state_partSelect="true" />
    <item
        android:id="@+id/selected_normal"
        android:drawable="@drawable/coui_btn_check_on_normal"
        android:state_enabled="true"
        coui:coui_state_allSelect="true"
        coui:coui_state_partSelect="false" />

    <item
        android:drawable="@drawable/coui_btn_check_on_disabled"
        android:state_enabled="false"
        coui:coui_state_allSelect="true"
        coui:coui_state_partSelect="false" />
    <item
        android:id="@+id/unselected_normal"
        android:drawable="@drawable/coui_btn_check_off_normal"
        android:state_enabled="true"
        coui:coui_state_allSelect="false"
        coui:coui_state_partSelect="false" />
    <item
        android:drawable="@drawable/coui_btn_check_off_disabled"
        android:state_enabled="false"
        coui:coui_state_allSelect="false"
        coui:coui_state_partSelect="false" />

    <transition
        android:drawable="@drawable/coui_checkbox_selected_to_unselected"
        android:fromId="@+id/selected_normal"
        android:toId="@+id/unselected_normal" />
    <transition
        android:drawable="@drawable/coui_checkbox_unselected_to_selected"
        android:fromId="@+id/unselected_normal"
        android:toId="@+id/selected_normal" />
    <transition
        android:drawable="@drawable/coui_checkbox_part_to_unselected"
        android:fromId="@id/part_normal"
        android:toId="@id/unselected_normal" />
    <transition
        android:drawable="@drawable/coui_checkbox_unselected_to_part"
        android:fromId="@id/unselected_normal"
        android:toId="@id/part_normal" />
    <transition
        android:drawable="@drawable/coui_checkbox_part_to_selected"
        android:fromId="@id/part_normal"
        android:toId="@id/selected_normal" />
    <transition
        android:drawable="@drawable/coui_checkbox_selected_to_part"
        android:fromId="@id/selected_normal"
        android:toId="@id/part_normal" />
</animated-selector>

