/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudDocumentService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/8 9:38
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/8       1.0      create
 ***********************************************************************/
package com.oplus.cloudconfig.service

import com.oplus.cloudconfig.bean.CloudDocumentConfigParam
import com.oplus.nearx.cloudconfig.anotation.Config
import com.oplus.nearx.cloudconfig.observable.Observable
import com.oplus.nearx.cloudconfig.stat.Const

@Config(configCode = "cloud_document_config_param_new", type = Const.CONFIG_TYPE_DATABASE)
interface CloudDocumentService {
    fun getData(): Observable<CloudDocumentConfigParam>
}