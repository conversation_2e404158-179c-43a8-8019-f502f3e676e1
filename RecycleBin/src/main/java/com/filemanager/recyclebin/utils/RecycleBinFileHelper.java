/***********************************************************
 * * Copyright (C), 2008-2017 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:
 * * Description:
 * * Version:
 * * Date :
 * * Author:
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.recyclebin.utils;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import com.filemanager.common.fileutils.JavaFileHelper;
import com.filemanager.common.helper.FileWrapper;
import com.filemanager.common.helper.MimeTypeHelper;
import com.filemanager.common.utils.CustomToast;
import com.filemanager.common.utils.Log;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class RecycleBinFileHelper {
    private static final String TAG = "RecycleBinFileHelper";
    public static final boolean HIDE_SYSTEM_FILE = true;
    private static final char WINDOWS_SEPARATOR = '\\';
    private static final char SYSTEM_SEPARATOR = File.separatorChar;

    private static Handler sUiHandler;
    private static long sFilesSize;

    public RecycleBinFileHelper(Context context) {
        sUiHandler = new Handler(Looper.getMainLooper());
    }

    public static String getAbsolutePath(String parent, String name) {
        if ((null == parent) || (null == name)) {
            return null;
        }
        return parent + File.separator + name;
    }

    private static boolean isSystemWindows() {
        return SYSTEM_SEPARATOR == WINDOWS_SEPARATOR;
    }

    private static boolean isSymlink(File file) throws IOException {
        if (file == null) {
            throw new NullPointerException("File must not be null");
        }
        if (isSystemWindows()) {
            return false;
        }
        File fileInCanonicalDir = null;
        if (file.getParentFile() == null) {
            fileInCanonicalDir = file;
        } else {
            File canonicalDir = file.getParentFile().getCanonicalFile();
            fileInCanonicalDir = new File(canonicalDir, file.getName());
        }

        if (fileInCanonicalDir.getCanonicalFile().equals(fileInCanonicalDir.getAbsoluteFile())) {
            return false;
        } else {
            return true;
        }
    }

    private static void deleteDirectory(File directory) throws IOException {
        if (!directory.exists()) {
            return;
        }

        if (!isSymlink(directory)) {
            cleanDirectory(directory);
        }

        sFilesSize += directory.length();
        setDeleteFilesSize(sFilesSize);

        if (!directory.delete()) {
            String message = "Unable to delete directory " + directory + ".";
            throw new IOException(message);
        }
    }

    public static boolean delete(File file) {
        // a file has b and c, a deleted first
        if (!file.exists()) {
            Log.w(TAG,"delete file not exists, delete true");
            return true;
        }
        if (file.isDirectory()) {
            try {
                deleteDirectory(file);
            } catch (IOException e) {
                Log.w(TAG, "Delete file error.");
                Log.e(TAG, e.getMessage());
                return false;
            } catch (StackOverflowError e) {
                Log.d(TAG, "delete StackOverflowError");
                sUiHandler.post(() -> CustomToast.INSTANCE.showShort(com.filemanager.common.R.string.toast_file_name_deep_path));
            }
        } else {
            sFilesSize += file.length();
            setDeleteFilesSize(sFilesSize);
            return file.delete();
        }
        return true;
    }

    private static void forceDelete(File file) throws IOException {
        if (file.isDirectory()) {
            deleteDirectory(file);
        } else {
            boolean filePresent = file.exists();
            sFilesSize += file.length();
            setDeleteFilesSize(sFilesSize);
            if (!file.delete()) {
                if (!filePresent) {
                    throw new FileNotFoundException("File does not exist: " + file);
                }
                String message = "Unable to delete file: " + file;
                throw new IOException(message);
            }
        }
    }

    public static long getDeleteFilesSize() {
        return sFilesSize;
    }

    public static void setDeleteFilesSize(long size) {
        sFilesSize = size;
    }

    private static void cleanDirectory(File directory) throws IOException {
        if (!directory.exists()) {
            String message = directory + " does not exist";
            Log.e(TAG, "message = " + message);
            return;
        }

        if (!directory.isDirectory()) {
            String message = directory + " is not a directory";
            Log.e(TAG, "message = " + message);
            return;
        }

        List<File> files = JavaFileHelper.listFiles(directory);
        if (files == null) {
            Log.e(TAG, "files == null");
            return;
        }

        IOException exception = null;
        for (File file : files) {
            try {
                forceDelete(file);
            } catch (IOException ioe) {
                exception = ioe;
            }
        }

        if (null != exception) {
            throw exception;
        }
    }

    public static boolean rename(File src, File dest) {
        return (src != null) && (src.exists()) && (dest != null) && src.renameTo(dest);
    }

    public static long getSize(File file) {
        if ((file != null) && file.exists()) {
            try {
                return FileUtils.sizeOf(file);
            } catch (IllegalArgumentException e) {
                // IllegalArgumentException throw by system,When read the file
                // size but the file loss, for example, disk pull out, file will
                // not be found. ignore file does not exist
            }
        }
        return 0;
    }

    public static boolean isDirectoryFiles(final ArrayList<FileWrapper> files) {
        boolean state = false;
        if ((files == null) || (files.size() <= 0)) {
            return state;
        }
        int len = files.size();
        if (len == 1) {
            int type = MimeTypeHelper.UNKNOWN_TYPE;
            FileWrapper file = files.get(0);
            if (file == null) {
                return state;
            }
            type = file.getType();
            if (type == MimeTypeHelper.DIRECTORY_TYPE) {
                String paths[] = file.list();
                if ((paths != null) && (paths.length > 0)) {
                    state = true;
                }
            } else {
                state = false;
            }
        } else {
            int type = MimeTypeHelper.UNKNOWN_TYPE;
            for (FileWrapper file : files) {
                if (file == null) {
                    continue;
                }
                type = file.getType();
                if (type == MimeTypeHelper.DIRECTORY_TYPE) {
                    String paths[] = file.list();
                    if ((paths != null) && (paths.length > 0)) {
                        state = true;
                        break;
                    }
                }
            }
        }
        return state;
    }

    public static boolean isNotExitFile(ArrayList<FileWrapper> files) {
        if (files == null) {
            return false;
        }
        if (files.size() == 1) {
            File file = files.get(0);
            if (!file.exists()) {
                return true;
            }
        }
        return false;
    }

    public static boolean isHandleDecompress(ArrayList<FileWrapper> files) {
        if (null == files) {
            return false;
        }
        if (files.size() == 0) {
            return false;
        }
        if ((files.size() == 1) && (files.get(0).getType() == MimeTypeHelper.COMPRESSED_TYPE)) {
            return true;
        }
        return false;
    }

    public static ArrayList<String> getSelectedFilePath(ArrayList<FileWrapper> source) {
        if (null == source) {
            return null;
        }
        if (source.size() == 0) {
            return null;
        }
        ArrayList<String> sourcePath = new ArrayList<String>();
        for (FileWrapper file : source) {
            if (null != file) {
                sourcePath.add(file.getPath());
            }
        }
        return sourcePath;
    }

    public static boolean haseDrmFile(ArrayList<FileWrapper> files) {
        if ((null == files) || (files.size() == 0)) {
            return false;
        }

        for (FileWrapper file : files) {
            if ((null != file) && (file.getType() == MimeTypeHelper.DRM_TYPE)) {
                return true;
            }
        }
        return false;
    }
}
