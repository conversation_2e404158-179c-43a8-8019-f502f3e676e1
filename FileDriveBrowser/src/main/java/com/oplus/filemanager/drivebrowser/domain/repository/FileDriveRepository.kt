/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileDriveRepository
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/7 17:28
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.domain.repository

import com.filemanager.common.base.DriveFileWrapper
import com.oplus.filemanager.drivebrowser.data.model.BaseResponse
import com.oplus.filemanager.drivebrowser.domain.model.AuthorizationData
import com.oplus.filemanager.drivebrowser.domain.model.DocumentPageData
import com.oplus.filemanager.drivebrowser.download.DownloadProgressListener

interface FileDriveRepository {
    suspend fun checkAuthorizationState(): Boolean

    suspend fun getAuthUrl(): AuthorizationData?

    suspend fun saveAuthorizationResult(code: String, state: String): Pair<Boolean, String>

    suspend fun cancelAuth(): Pair<Boolean, String>

    suspend fun getWebViewAuthUrl(): AuthorizationData?

    suspend fun getDocumentLists(
        sortType: String,
        asc: Int,
        start: Int,
        limit: Int,
        folderId: String
    ): BaseResponse<DocumentPageData>


    suspend fun renameDocument(fileId: String, title: String): BaseResponse<String>

    suspend fun renameFolder(folderId: String, title: String): BaseResponse<String>

    suspend fun deleteFile(fileId: String, listType: String): Pair<Boolean, String>

    suspend fun deleteFiles(files: List<DriveFileWrapper>): Pair<Boolean, String>

    suspend fun deleteFolder(
        folderId: String
    ): Pair<Boolean, String>

    suspend fun downloadFile(
        fileId: String,
        targetPath: String,
        exportType: String?,
        listener: DownloadProgressListener
    )

    fun cancelDownload()

    suspend fun getEditUrl(fileId: String): String

    suspend fun searchFile(searchKey: String, page: Int): DocumentPageData

    suspend fun isFileAuth(fileId: String): Boolean
}