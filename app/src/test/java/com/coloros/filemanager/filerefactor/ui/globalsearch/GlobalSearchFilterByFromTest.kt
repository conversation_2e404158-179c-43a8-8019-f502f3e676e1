/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.GlobalSearchFilterByFromTest
 * * Version     : 1.0
 * * Date        : 2020/7/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.filerefactor.ui.globalsearch

import com.coloros.filemanager.BaseTest
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.FilterConstants.FILTER_FROM_BLUETOOTH
import com.filemanager.common.constants.FilterConstants.FILTER_FROM_DOWNLOAD
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterCondition
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterDataHelperFactory
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import shadow.ShadowVolumeEnvironment
import java.io.File

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
@Config(shadows = [ShadowVolumeEnvironment::class])
class GlobalSearchFilterByFromTest : BaseTest() {
    private var mStorageRootPath = "/sdcard/"
    
    @Before
    override fun setUp() {
        mContext = RuntimeEnvironment.application
        if (!mStorageRootPath.endsWith(File.separator)) {
            mStorageRootPath = mStorageRootPath.plus(File.separator)
        }
    }

    @After
    override fun tearDown() {
        super.tearDown()
    }

    @Ignore
    fun should_verify_filter_result_by_from_download() {
        ShadowVolumeEnvironment.setmIsExternalSdMounted(true)
        val filterItem = FilterItem(FILTER_FROM_DOWNLOAD,
                FilterCondition(FilterConstants.FILTER_FROM, ""), "")
        val bean1 = BaseFileBean()
        bean1.mData = mStorageRootPath.plus("download/abc.png")
        val bean2 = BaseFileBean()
        bean2.mData = mStorageRootPath.plus("Download/abc.png")
        val bean3 = BaseFileBean()
        bean3.mData = mStorageRootPath.plus("Bluetooth/abc.png")
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(2, resultList!!.size)
        Assert.assertFalse(resultList.contains(bean3))
    }

    @Ignore
    fun should_verify_filter_result_by_from_bluetooth() {
        val filterItem = FilterItem(FILTER_FROM_BLUETOOTH,
                FilterCondition(FilterConstants.FILTER_FROM, ""), "")
        val bean1 = BaseFileBean()
        bean1.mData = mStorageRootPath.plus("bluetooth/abc.png")
        val bean2 = BaseFileBean()
        bean2.mData = mStorageRootPath.plus("Download/abc.png")
        val bean3 = BaseFileBean()
        bean3.mData = mStorageRootPath.plus("Bluetooth/abc.png")
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(2, resultList!!.size)
        Assert.assertFalse(resultList.contains(bean2))
    }
}
