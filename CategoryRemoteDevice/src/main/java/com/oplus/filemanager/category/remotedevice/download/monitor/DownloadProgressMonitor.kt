/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DownloadMonitor
 * * Description : 下载进度的监视器
 * * Version     : 1.0
 * * Date        : 2025/01/07
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download.monitor

import android.app.Service
import com.filemanager.common.fileutils.getDisplayNameByString
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean
import com.oplus.filemanager.category.remotedevice.download.service.DownloadTaskInfo
import com.oplus.filemanager.interfaze.remotedevice.DownloadRemoteFileCallback
import com.oplus.filemanager.interfaze.remotedevice.OperateCallback
import com.oplus.filemanager.remotedevice.ResultConstant
import java.io.File

abstract class DownloadProgressMonitor(val context: Service) : DownloadRemoteFileCallback {

    private var downloadTask: DownloadTaskInfo? = null
    protected var isSuccess: Boolean = false

    fun setDownloadingTask(task: DownloadTaskInfo) {
        downloadTask = task
    }

    open fun startMonitor(taskInfo: DownloadTaskInfo) {
        downloadTask = taskInfo
    }


    override fun onProgressEachFile(path: String?, status: Int, progress: Float) {
//        onSingleFileProgress(path, progress, downloadTask)
    }

    override fun onProgress(ratio: Int) {
        onUpdateDownloadProgress(ratio * 1.0f, downloadTask)
    }

    override fun onResult(operateCode: Int, resultCode: Int) {
        if (operateCode == OperateCallback.OPERATE_BEGIN_DOWNLOAD) {
            if (ResultConstant.isSuccess(resultCode)) {
                isSuccess = true
                onDownloadSuccess(downloadTask)
            } else if (ResultConstant.isFail(resultCode)) {
                onDownloadFail(downloadTask, "网络错误")
            } else if (ResultConstant.RESULT_CODE_CANCEL == resultCode) { // 取消
//                cancelMonitor()
            } else if (ResultConstant.isPause(resultCode)) { // 暂停下载
                onDownloadPause(downloadTask)
            } else if (ResultConstant.isContinue(resultCode)) { //继续下载
                onDownloadContinue(downloadTask)
            }
        }
    }

    /**
     * 取消监听
     */
    abstract fun cancelMonitor()

    /**
     * 下载成功
     */
    abstract fun onDownloadSuccess(taskInfo: DownloadTaskInfo?)

    /**
     * 下载失败
     */
    abstract fun onDownloadFail(taskInfo: DownloadTaskInfo?, failMsg: String)

    /**
     * 当暂停下载
     */
    open fun onDownloadPause(taskInfo: DownloadTaskInfo?) {
    }

    /**
     * 当继续下载
     */
    open fun onDownloadContinue(taskInfo: DownloadTaskInfo?) {
    }

    /**
     * 下载总进度
     */
    abstract fun onUpdateDownloadProgress(progress: Float, taskInfo: DownloadTaskInfo?)

    /**
     * 单个文件的进度
     */
    open fun onSingleFileProgress(path: String?, progress: Float, taskInfo: DownloadTaskInfo?) {
    }

    /**
     * 获取下载到本地的路径
     * @param destDir 保存到本地的目录
     * @param remotePath 远程文件的地址
     * @return 远程文件保存到本地后的文件路径
     */
    fun getLocalPath(destDir: String, remotePath: String): String {
        val displayName = getDisplayNameByString(remotePath)
        val localFile = File(destDir, displayName)
        return localFile.absolutePath
    }
}