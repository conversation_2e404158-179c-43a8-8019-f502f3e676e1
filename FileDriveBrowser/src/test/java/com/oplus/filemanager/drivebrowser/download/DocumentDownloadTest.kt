package com.oplus.filemanager.drivebrowser.download

import android.app.Application
import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.drivebrowser.data.repository.DocumentDownloadErrorCode
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.room.model.DriveFileEntity
import io.mockk.*
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * DocumentDownload类的单元测试类
 * 用于测试文件下载功能的各种场景
 */
@ExperimentalCoroutinesApi
class DocumentDownloadTest {

    // 模拟的FileDriveRepository对象
    @MockK
    private lateinit var mockRepository: FileDriveRepository
    
    // 被测试的DocumentDownload对象
    private lateinit var documentDownload: DocumentDownload

    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 模拟MyApplication的上下文
     * 3. 创建被测试对象
     * 4. 模拟StatisticsUtils类
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        // 修复：使用Application类型模拟上下文
        mockkObject(MyApplication)
        val mockContext = mockk<Application>(relaxed = true)
        every { MyApplication.sAppContext } returns mockContext
        
        documentDownload = DocumentDownload(mockRepository)
        mockkStatic(StatisticsUtils::class)
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试当fileId为空时下载应该返回失败
     */
    @Test
    fun `download should return failure when fileId is empty`() = runTest {
        // 准备测试数据
        val taskInfo = DownloadTaskInfo(fileId = "", targetPath = "/path", exportType = null)

        // 执行下载操作
        val result = documentDownload.download(taskInfo) { }

        // 验证结果
        assertFalse(result.success)
    }

    /**
     * 测试下载过程中进度回调被正确调用
     */
    @Test
    fun `download should invoke progress callback correctly`() = runTest {
        // 准备测试数据
        val taskInfo = DownloadTaskInfo(fileId = "file123", targetPath = "/path", exportType = "pdf")
        val progressValues = mutableListOf<Int>()
        val progressListener = slot<DownloadProgressListener>()

        // 设置模拟行为：捕获进度监听器并模拟进度变化
        coEvery { mockRepository.downloadFile(any(), any(), any(), capture(progressListener)) } coAnswers {
            progressListener.captured.onProgressChange(0)
            progressListener.captured.onProgressChange(50)
            progressListener.captured.onProgressChange(100)
        }

        // 执行下载操作并收集进度值
        documentDownload.download(taskInfo) { progress ->
            progressValues.add(progress)
        }

        // 验证进度回调被正确调用
        assertEquals(listOf(0, 50, 100), progressValues)
    }

    /**
     * 测试进度为0时的回调情况
     */
    @Test
    fun `progress callback should be invoked when progress changes within limit for 0`() = runTest {
        testProgressCallback(0)
    }

    /**
     * 测试进度为50时的回调情况
     */
    @Test
    fun `progress callback should be invoked when progress changes within limit for 50`() = runTest {
        testProgressCallback(50)
    }

    /**
     * 测试进度为100时的回调情况
     */
    @Test
    fun `progress callback should be invoked when progress changes within limit for 100`() = runTest {
        testProgressCallback(100)
    }

    /**
     * 测试进度回调的通用方法
     * @param progressValue 要测试的进度值
     */
    private fun testProgressCallback(progressValue: Int) = runTest {
        // 准备测试数据
        val taskInfo = DownloadTaskInfo(fileId = "file123", targetPath = "/path", exportType = null)
        val progressValues = mutableListOf<Int>()
        val progressListener = slot<DownloadProgressListener>()

        // 设置模拟行为：捕获进度监听器并模拟指定进度变化
        coEvery { mockRepository.downloadFile(any(), any(), any(), capture(progressListener)) } coAnswers {
            progressListener.captured.onProgressChange(progressValue)
        }

        // 执行下载操作并收集进度值
        documentDownload.download(taskInfo) { progress ->
            progressValues.add(progress)
        }

        // 验证进度回调情况
        if (progressValue == 0) {
            assertEquals(listOf(0), progressValues)
        } else {
            assertEquals(listOf(0, progressValue), progressValues)
        }
    }

    /**
     * 测试当进度超过最大值时不应回调
     */
    @Test
    fun `progress callback should not be invoked when progress exceeds max`() = runTest {
        // 准备测试数据
        val taskInfo = DownloadTaskInfo(fileId = "file123", targetPath = "/path", exportType = null)
        val progressValues = mutableListOf<Int>()
        val progressListener = slot<DownloadProgressListener>()

        // 设置模拟行为：模拟进度超过最大值的情况
        coEvery { mockRepository.downloadFile(any(), any(), any(), capture(progressListener)) } coAnswers {
            progressListener.captured.onProgressChange(101)
        }

        // 执行下载操作并收集进度值
        documentDownload.download(taskInfo) { progress ->
            progressValues.add(progress)
        }

        // 验证只有初始进度0被回调
        assertEquals(listOf(0), progressValues)
    }

    /**
     * 测试onStop调用会将success设置为false
     */
    @Test
    fun `onStop should set success to false`() = runTest {
        // 准备测试数据
        val taskInfo = DownloadTaskInfo(fileId = "file123", targetPath = "/path", exportType = null)
        val progressListener = slot<DownloadProgressListener>()

        // 设置模拟行为：模拟调用onStop
        coEvery { mockRepository.downloadFile(any(), any(), any(), capture(progressListener)) } coAnswers {
            progressListener.captured.onStop()
        }

        // 执行下载操作
        val result = documentDownload.download(taskInfo) { }

        // 验证结果为失败
        assertFalse(result.success)
    }

    /**
     * 测试取消下载操作会调用repository的cancelDownload方法
     */
    @Test
    fun `cancelDownload should invoke repository cancel`() {
        // 设置mock行为避免未定义调用
        justRun { mockRepository.cancelDownload() }
        
        // 执行取消下载操作
        documentDownload.cancelDownload()

        // 验证repository的cancelDownload方法被调用
        verify { mockRepository.cancelDownload() }
    }
}