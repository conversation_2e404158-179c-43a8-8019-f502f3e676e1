/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentFilesCardWidgetProviderTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/19
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/19      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.provider

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.utils.PermissionUtils
import com.oplus.filemanager.cardwidget.data.RecentFileCardData
import com.oplus.filemanager.cardwidget.provider.RecentFilesCardWidgetProvider.Companion.EMPTY_2_TO_2
import com.oplus.filemanager.cardwidget.provider.RecentFilesCardWidgetProvider.Companion.EMPTY_2_TO_4
import com.oplus.filemanager.cardwidget.provider.RecentFilesCardWidgetProvider.Companion.EMPTY_4_TO_4
import com.oplus.filemanager.cardwidget.provider.RecentFilesCardWidgetProvider.Companion.LAYOUT_NAME_2_TO_2
import com.oplus.filemanager.cardwidget.provider.RecentFilesCardWidgetProvider.Companion.LAYOUT_NAME_2_TO_4
import com.oplus.filemanager.cardwidget.provider.RecentFilesCardWidgetProvider.Companion.LAYOUT_NAME_4_TO_4
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_2_COLOROS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_2_ONEPLUS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_4_COLOROS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_4_ONEPLUS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_4_TO_4_COLOROS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_4_TO_4_ONEPLUS
import com.oplus.filemanager.cardwidget.util.LAYOUT_2_TO_2_MAX_ITEM_COUNT
import com.oplus.filemanager.cardwidget.util.LAYOUT_2_TO_4_MAX_ITEM_COUNT
import com.oplus.filemanager.cardwidget.util.LAYOUT_4_TO_4_MAX_ITEM_COUNT
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.Assert
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class RecentFilesCardWidgetProviderTest {

    private lateinit var mInstance: RecentFilesCardWidgetProvider
    private lateinit var mContext: Context

    companion object {
        const val WIDGET_CODE_22 = "222220024&128&0"
        const val WIDGET_CODE_24 = "222220025&21&0"
        const val WIDGET_CODE_44 = "222220026&69&0"
    }

    @Before
    fun setUp() {
        mInstance = RecentFilesCardWidgetProvider.instance
        MockKAnnotations.init(this)
        mContext = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(mContext)
    }

    @Test
    fun getCardLayoutNameByCardTypeTest() {
        assertEquals(
            LAYOUT_NAME_2_TO_2,
            mInstance.getCardLayoutNameByCardType(CARD_TYPE_2_TO_2_COLOROS)
        )
        assertEquals(
            LAYOUT_NAME_2_TO_4,
            mInstance.getCardLayoutNameByCardType(CARD_TYPE_2_TO_4_COLOROS)
        )
        assertEquals(
            LAYOUT_NAME_4_TO_4,
            mInstance.getCardLayoutNameByCardType(CARD_TYPE_4_TO_4_COLOROS)
        )

        assertEquals(
            LAYOUT_NAME_2_TO_2, mInstance.getCardLayoutNameByCardType(
                CARD_TYPE_2_TO_2_ONEPLUS
            )
        )
        assertEquals(
            LAYOUT_NAME_2_TO_4, mInstance.getCardLayoutNameByCardType(
                CARD_TYPE_2_TO_4_ONEPLUS
            )
        )
        assertEquals(
            LAYOUT_NAME_4_TO_4, mInstance.getCardLayoutNameByCardType(
                CARD_TYPE_4_TO_4_ONEPLUS
            )
        )
        assertEquals(
            LAYOUT_NAME_2_TO_2, mInstance.getCardLayoutNameByCardType(
                -1
            )
        )
    }

    @Test
    fun subscribedTest() {
        RecentFilesCardWidgetProvider.widgetCodeList.clear()
        Assert.assertFalse(
            RecentFilesCardWidgetProvider.widgetCodeList.contains(
                CARD_TYPE_2_TO_2_COLOROS.toString()
            )
        )
        mInstance.subscribed(mContext, CARD_TYPE_2_TO_2_COLOROS.toString())
        assertTrue(
            RecentFilesCardWidgetProvider.widgetCodeList.contains(
                CARD_TYPE_2_TO_2_COLOROS.toString()
            )
        )
    }

    @Test
    fun unSubscribedTest() {
        RecentFilesCardWidgetProvider.widgetCodeList.clear()
        RecentFilesCardWidgetProvider.widgetCodeList.add(CARD_TYPE_2_TO_2_COLOROS.toString())
        mInstance.unSubscribed(mContext, CARD_TYPE_2_TO_2_COLOROS.toString())
        Assert.assertFalse(
            RecentFilesCardWidgetProvider.widgetCodeList.contains(
                CARD_TYPE_2_TO_2_COLOROS.toString()
            )
        )
        assertTrue(RecentFilesCardWidgetProvider.mCardWidgetLayoutMaps.isEmpty())
    }

    @Test
    fun initWidgetCodeTest() {
        val spykInstance = spyk(mInstance)
        every { spykInstance.getShowedCardList() } returns mutableListOf()
        RecentFilesCardWidgetProvider.widgetCodeList.clear()
        spykInstance.initWidgetCode()
        assertTrue(RecentFilesCardWidgetProvider.widgetCodeList.isEmpty())

        every { spykInstance.getShowedCardList() } returns mutableListOf(CARD_TYPE_2_TO_2_COLOROS.toString())
        RecentFilesCardWidgetProvider.widgetCodeList.clear()
        spykInstance.initWidgetCode()
        assertTrue(
            RecentFilesCardWidgetProvider.widgetCodeList.contains(
                CARD_TYPE_2_TO_2_COLOROS.toString()
            )
        )
    }

    @Test
    fun getMaxCountOfCardItemsTest() {
        RecentFilesCardWidgetProvider.widgetCodeList.clear()
        assertEquals(LAYOUT_2_TO_2_MAX_ITEM_COUNT, mInstance.getMaxCountOfCardItems())

        RecentFilesCardWidgetProvider.widgetCodeList.clear()
        RecentFilesCardWidgetProvider.widgetCodeList.add(WIDGET_CODE_22)
        assertEquals(LAYOUT_2_TO_2_MAX_ITEM_COUNT, mInstance.getMaxCountOfCardItems())

        RecentFilesCardWidgetProvider.widgetCodeList.clear()
        RecentFilesCardWidgetProvider.widgetCodeList.add(WIDGET_CODE_24)
        assertEquals(LAYOUT_2_TO_4_MAX_ITEM_COUNT, mInstance.getMaxCountOfCardItems())

        RecentFilesCardWidgetProvider.widgetCodeList.clear()
        RecentFilesCardWidgetProvider.widgetCodeList.add(WIDGET_CODE_44)
        assertEquals(LAYOUT_4_TO_4_MAX_ITEM_COUNT, mInstance.getMaxCountOfCardItems())

        RecentFilesCardWidgetProvider.widgetCodeList.clear()
        RecentFilesCardWidgetProvider.widgetCodeList.add(WIDGET_CODE_22)
        RecentFilesCardWidgetProvider.widgetCodeList.add(WIDGET_CODE_24)
        assertEquals(LAYOUT_2_TO_4_MAX_ITEM_COUNT, mInstance.getMaxCountOfCardItems())

        RecentFilesCardWidgetProvider.widgetCodeList.clear()
        RecentFilesCardWidgetProvider.widgetCodeList.add(WIDGET_CODE_22)
        RecentFilesCardWidgetProvider.widgetCodeList.add(WIDGET_CODE_44)
        assertEquals(LAYOUT_4_TO_4_MAX_ITEM_COUNT, mInstance.getMaxCountOfCardItems())

        RecentFilesCardWidgetProvider.widgetCodeList.clear()
        RecentFilesCardWidgetProvider.widgetCodeList.add(WIDGET_CODE_22)
        RecentFilesCardWidgetProvider.widgetCodeList.add(WIDGET_CODE_24)
        RecentFilesCardWidgetProvider.widgetCodeList.add(WIDGET_CODE_44)
        assertEquals(LAYOUT_4_TO_4_MAX_ITEM_COUNT, mInstance.getMaxCountOfCardItems())

        RecentFilesCardWidgetProvider.widgetCodeList.clear()
        RecentFilesCardWidgetProvider.widgetCodeList.add(WIDGET_CODE_24)
        RecentFilesCardWidgetProvider.widgetCodeList.add(WIDGET_CODE_44)
        assertEquals(LAYOUT_4_TO_4_MAX_ITEM_COUNT, mInstance.getMaxCountOfCardItems())
    }

    @Test
    fun getRecentFilesFromSourceDataTest() {
        assertTrue(mInstance.getRecentFilesFromSourceData(null).size == 0)
        assertTrue(mInstance.getRecentFilesFromSourceData(mutableListOf()).size == 0)
    }

    @Test
    fun `should refresh If need when on resume`() {
        val provider = spyk(mInstance)
        justRun { provider.refreshIfNeed() }
        provider.onResume(mContext, "")
        verify { provider.refreshIfNeed() }
    }

    @Test
    fun `should init widget code when refresh If need if last time less than MIN_TIME`() {
        mockkObject(PrivacyPolicyController.Companion)
        mockkObject(PermissionUtils)
        val provider = spyk(mInstance)
        every { provider.refreshIfNeed() } answers { callOriginal() }
        every { PermissionUtils.hasStoragePermission() } returns true
        every { PrivacyPolicyController.hasAgreePrivacy() } returns true
        justRun { provider.initWidgetCode() }
        justRun { provider.refreshData(true, true) }

        provider.refreshIfNeed()

        verify { provider.initWidgetCode() }
        verify { provider.refreshData(true, true) }
        unmockkObject(PermissionUtils)
        unmockkObject(PrivacyPolicyController.Companion)
    }

    @Test
    fun `should not init widget code when refresh If need if last time less than MIN_TIME`() {
        mockkObject(PrivacyPolicyController.Companion)
        mockkObject(PermissionUtils)
        val provider = spyk(mInstance)
        every { provider.mLastRefreshTime } returns System.currentTimeMillis() - 1
        every { provider.refreshIfNeed() } answers { callOriginal() }
        every { PermissionUtils.hasStoragePermission() } returns true
        every { PrivacyPolicyController.hasAgreePrivacy() } returns true
        justRun { provider.initWidgetCode() }
        justRun { provider.refreshData(true, true) }

        provider.refreshIfNeed()

        verify(inverse = true) { provider.initWidgetCode() }
        verify(inverse = true) { provider.refreshData(true, true) }
        unmockkObject(PermissionUtils)
        unmockkObject(PrivacyPolicyController.Companion)
    }

    @Test
    fun `should refresh data normally when refresh data`() {
        val provider = spyk(mInstance)
        val isPrivacyGranted = true
        val isStorageGranted = true
        val data = mockk<RecentFileCardData>(relaxed = true)
        every { provider.refreshData(isPrivacyGranted, isStorageGranted) } answers { callOriginal() }
        justRun { provider.refreshDataNormally(data) }
        every { provider.createData() } returns data
        provider.refreshData(isPrivacyGranted, isStorageGranted)
        verify { provider.refreshDataNormally(data) }
    }

    @Test
    fun `should refresh data privacy not granted when refresh data`() {
        val provider = spyk(mInstance)
        val isPrivacyGranted = false
        val isStorageGranted = true
        val data = mockk<RecentFileCardData>(relaxed = true)
        every { provider.refreshData(isPrivacyGranted, isStorageGranted) } answers { callOriginal() }
        justRun { provider.refreshDataPrivacyNotGranted(data) }
        every { provider.createData() } returns data

        provider.refreshData(isPrivacyGranted, isStorageGranted)

        verify { provider.refreshDataPrivacyNotGranted(data) }
    }

    @Test
    fun `should refresh data storage permission not granted when refresh data`() {
        val provider = spyk(mInstance)
        val isPrivacyGranted = true
        val isStorageGranted = false
        val data = mockk<RecentFileCardData>(relaxed = true)
        every { provider.createData() } returns data
        every { provider.refreshData(isPrivacyGranted, isStorageGranted) } answers { callOriginal() }
        justRun { provider.refreshDataStoragePermissionNotGranted(data) }


        provider.refreshData(isPrivacyGranted, isStorageGranted)

        verify { provider.refreshDataStoragePermissionNotGranted(data) }
    }

    @Test
    fun `should reset last refresh time when config changed`() {
        val provider = spyk(mInstance)
        provider.onConfigurationChanged(mockk(relaxed = true))
        assertEquals(0L, provider.mLastRefreshTime)
    }

    @Test
    fun `should reset card widget layout maps when config changed`() {
        val provider = spyk(mInstance)
        provider.onConfigurationChanged(mockk(relaxed = true))
        assertEquals(0, RecentFilesCardWidgetProvider.mCardWidgetLayoutMaps.size)
    }

    @Test
    fun getEmptyLayoutTest() {
        assertEquals(EMPTY_2_TO_2, mInstance.getEmptyLayout(CARD_TYPE_2_TO_2_COLOROS))
        assertEquals(EMPTY_2_TO_2, mInstance.getEmptyLayout(CARD_TYPE_2_TO_2_ONEPLUS))
        assertEquals(EMPTY_2_TO_4, mInstance.getEmptyLayout(CARD_TYPE_2_TO_4_COLOROS))
        assertEquals(EMPTY_2_TO_4, mInstance.getEmptyLayout(CARD_TYPE_2_TO_4_ONEPLUS))
        assertEquals(EMPTY_4_TO_4, mInstance.getEmptyLayout(CARD_TYPE_4_TO_4_COLOROS))
        assertEquals(EMPTY_4_TO_4, mInstance.getEmptyLayout(CARD_TYPE_4_TO_4_ONEPLUS))
        assertEquals(EMPTY_2_TO_2, mInstance.getEmptyLayout(-1))
    }
}