/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.filemanager.categorycompress.ui.compress
 * * Version     : 1.0
 * * Date        : 2022/2/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager

import com.filemanager.categorycompress.ui.CategoryCompressViewModel
import com.filemanager.categorycompress.ui.CompressParentFragment
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class CategoryCompressActivityTest : BaseTest() {
    private val mFragment = CompressParentFragment()

    @Test
    fun test_tab_titles_if_normal() {
        val fragment = CompressParentFragment::class.java
        val tabField = fragment.getDeclaredField("mTabTitles")
        tabField.isAccessible = true
        val result = tabField.get(mFragment) as Array<String>
        Assert.assertEquals(5, result.size)
        Assert.assertTrue(result.contains(CategoryCompressViewModel.TYPE_ALL))
        Assert.assertTrue(result.contains(CategoryCompressViewModel.TYPE_RAR))
        Assert.assertTrue(result.contains(CategoryCompressViewModel.TYPE_ZIP))
        Assert.assertTrue(result.contains(CategoryCompressViewModel.TYPE_JAR))
        Assert.assertTrue(result.contains(CategoryCompressViewModel.TYPE_7ZIP))
    }
}