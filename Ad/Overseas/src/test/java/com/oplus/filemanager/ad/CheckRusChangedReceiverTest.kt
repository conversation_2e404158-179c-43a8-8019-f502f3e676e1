package com.oplus.filemanager.ad

import android.content.Context
import android.content.Intent
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.utils.AdConstants
import com.oplus.filemanager.utils.GetRusDataUtils
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import org.robolectric.RuntimeEnvironment
import org.robolectric.shadows.ShadowLog
import java.util.*
import org.junit.Assert.*
import org.robolectric.annotation.LooperMode
import org.robolectric.shadows.ShadowApplication

/**
 * CheckRusChangedReceiver的单元测试类
 * 用于测试RUS配置变更广播接收器的各种场景
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
@LooperMode(LooperMode.Mode.PAUSED)
class CheckRusChangedReceiverTest {

    companion object {
        // RUS配置变更的广播Action
        private const val RUS_CHANGED_ACTION = "oppo.intent.action.ROM_UPDATE_CONFIG_SUCCESS"
        // 配置变更列表的Key
        private const val KEY_UPDATE_CONFIG_LIST = "ROM_UPDATE_CONFIG_LIST"
    }

    // 模拟的Context对象
    @MockK
    private lateinit var mockContext: Context

    // 被测的广播接收器实例
    private lateinit var receiver: CheckRusChangedReceiver
    // Robolectric提供的应用上下文
    private val appContext: Context = RuntimeEnvironment.getApplication()

    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 设置日志输出
     * 3. 创建被测接收器实例
     * 4. 模拟静态类
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        ShadowLog.stream = System.out
        receiver = CheckRusChangedReceiver()
        mockkStatic(Log::class)
        mockkStatic(Utils::class)
        mockkStatic(ModelUtils::class)  // 修复：使用mockkStatic替代mockkObject
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试接收错误Action时的处理
     * 预期：记录错误日志且不执行任何操作
     */
    @Test
    fun `onReceive with incorrect action should log error and return`() {
        // Arrange - 准备测试数据：错误的Action
        val intent = Intent("WRONG_ACTION")
        mockkConstructor(ThreadManager::class)

        // Act - 执行被测方法
        receiver.onReceive(appContext, intent)

        // Assert - 验证：1. 记录了错误日志 2. 没有执行线程任务
        verify { Log.e(AdConstants.AD_TAG, "onReceive action is WRONG_ACTION") }
        verify(exactly = 0) { anyConstructed<ThreadManager>().execute(any<FileRunnable>()) }
    }

    /**
     * 测试非realme手机且非平板设备时的处理
     * 预期：记录日志且不执行任何操作
     */
    @Test
    fun `onReceive with non-realme and non-tablet device should log and return`() {
        // Arrange - 准备测试数据：模拟非realme手机和非平板设备
        val intent = Intent(RUS_CHANGED_ACTION)
        every { Utils.isRealmePhone() } returns false
        every { ModelUtils.isTablet() } returns false  // 修复后能正确模拟静态方法
        mockkConstructor(ThreadManager::class)

        // Act - 执行被测方法
        receiver.onReceive(appContext, intent)

        // Assert - 验证：1. 记录了调试日志 2. 没有执行线程任务
        verify { Log.d(AdConstants.AD_TAG, "No need to check Rus Changed.") }
        verify(exactly = 0) { anyConstructed<ThreadManager>().execute(any<FileRunnable>()) }
    }

    /**
     * 测试空变更列表时的处理
     * 预期：不执行任何操作
     */
    @Test
    fun `onReceive with null changeTableNameList should do nothing`() {
        // Arrange - 准备测试数据：模拟realme手机但无变更列表
        val intent = Intent(RUS_CHANGED_ACTION)
        every { Utils.isRealmePhone() } returns true
        mockkConstructor(ThreadManager::class)

        // Act - 执行被测方法
        receiver.onReceive(appContext, intent)

        // Assert - 验证：没有执行线程任务
        verify(exactly = 0) { anyConstructed<ThreadManager>().execute(any<FileRunnable>()) }
    }

    /**
     * 测试空变更列表时的处理
     * 预期：不执行任何操作
     */
    @Test
    fun `onReceive with empty changeTableNameList should do nothing`() {
        // Arrange - 准备测试数据：模拟realme手机但变更列表为空
        val intent = Intent(RUS_CHANGED_ACTION).apply {
            putStringArrayListExtra(KEY_UPDATE_CONFIG_LIST, ArrayList())
        }
        every { Utils.isRealmePhone() } returns true
        mockkConstructor(ThreadManager::class)

        // Act - 执行被测方法
        receiver.onReceive(appContext, intent)

        // Assert - 验证：没有执行线程任务
        verify(exactly = 0) { anyConstructed<ThreadManager>().execute(any<FileRunnable>()) }
    }

    /**
     * 测试不匹配的配置变更时的处理
     * 预期：不执行任何操作
     */
    @Test
    fun `onReceive with non-matching config should do nothing`() {
        // Arrange - 准备测试数据：模拟realme手机但配置不匹配
        val intent = Intent(RUS_CHANGED_ACTION).apply {
            putStringArrayListExtra(KEY_UPDATE_CONFIG_LIST, arrayListOf("OTHER_CONFIG"))
        }
        every { Utils.isRealmePhone() } returns true
        mockkConstructor(ThreadManager::class)

        // Act - 执行被测方法
        receiver.onReceive(appContext, intent)

        // Assert - 验证：没有执行线程任务
        verify(exactly = 0) { anyConstructed<ThreadManager>().execute(any<FileRunnable>()) }
    }

    /**
     * 测试匹配的配置变更时的处理
     * 预期：执行线程任务
     */
    @Test
    fun `onReceive with matching config should execute runnable`() {
        // Arrange - 准备测试数据：模拟realme手机且配置匹配
        val intent = Intent(RUS_CHANGED_ACTION).apply {
            putStringArrayListExtra(KEY_UPDATE_CONFIG_LIST, 
                arrayListOf(GetRusDataUtils.APPS_SECURITY_CONF))
        }
        every { Utils.isRealmePhone() } returns true
        mockkConstructor(ThreadManager::class)
        val slot = slot<FileRunnable>()  // 用于捕获Runnable对象
        every { anyConstructed<ThreadManager>().execute(capture(slot)) } returns "task_id"

        // Act - 执行被测方法
        receiver.onReceive(appContext, intent)

        // Assert - 验证：1. 执行了线程任务 2. 捕获的Runnable不为空
        verify(exactly = 1) { anyConstructed<ThreadManager>().execute(any<FileRunnable>()) }
        assertNotNull(slot.captured)
    }

    /**
     * 测试平板设备匹配配置时的处理
     * 预期：执行线程任务
     */
    @Test
    fun `onReceive with tablet device should process when config matches`() {
        // Arrange - 准备测试数据：模拟平板设备且配置匹配
        val intent = Intent(RUS_CHANGED_ACTION).apply {
            putStringArrayListExtra(KEY_UPDATE_CONFIG_LIST, 
                arrayListOf(GetRusDataUtils.APPS_SECURITY_CONF))
        }
        every { Utils.isRealmePhone() } returns false
        every { ModelUtils.isTablet() } returns true  // 修复后能正确模拟静态方法
        mockkConstructor(ThreadManager::class)
        val slot = slot<FileRunnable>()  // 用于捕获Runnable对象
        every { anyConstructed<ThreadManager>().execute(capture(slot)) } returns "task_id"

        // Act - 执行被测方法
        receiver.onReceive(appContext, intent)

        // Assert - 验证：执行了线程任务
        verify(exactly = 1) { anyConstructed<ThreadManager>().execute(any<FileRunnable>()) }
    }
}