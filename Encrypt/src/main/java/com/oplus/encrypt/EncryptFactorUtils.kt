/*********************************************************************
 * * Copyright (C), 2020, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/6/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.encrypt

import android.app.Activity
import android.content.Intent
import com.filemanager.common.R
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.EncryptConstants
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.Log

object EncryptFactorUtils {
    private const val TAG = "EncryptFactorUtils"
    private const val PACKAGE_NAME_KEY = "package_name_key"
    private const val FROM_EXT_KEY = "from_external_app"


    @JvmStatic
    fun jumpToEncryptR(activity: Activity): Boolean {
        return runCatching {
            val intent = Intent()
            intent.action = Constants.INTENT_FILE_SAFE_ACTION
            intent.putExtra(PACKAGE_NAME_KEY, AppUtils.getPackageName())
            intent.putExtra(FROM_EXT_KEY, true)
            intent.`package` = EncryptConstants.FILE_ENCRYPTION_PACKGE
            activity.startActivity(intent)
            activity.overridePendingTransition(R.anim.app_push_up_enter, R.anim.app_zoom_fade_exit)
            Log.i(TAG, "jumpToEncryptS R")
            true
        }.onFailure {
            android.util.Log.w(TAG, "jumpToEncryptR S  = ${it.message}")
        }.getOrDefault(false)
    }

    @JvmStatic
    fun jumpToEncryptS(activity: Activity) {
        runCatching {
            val intent = Intent()
            intent.action = Constants.INTENT_FILE_SAFE_ACTION_OPLUS
            intent.putExtra(PACKAGE_NAME_KEY, AppUtils.getPackageName())
            intent.putExtra(FROM_EXT_KEY, true)
            intent.`package` = EncryptConstants.FILE_ENCRYPTION_PACKGE_OPLUS
            activity.startActivity(intent)
            activity.overridePendingTransition(com.filemanager.common.R.anim.app_push_up_enter, com.filemanager.common.R.anim.app_zoom_fade_exit)
            android.util.Log.i(TAG, "jumpToEncryptS S")
        }.onFailure {
            android.util.Log.w(TAG, "jumpToEncryptS S  = ${it.message}")
        }
    }
}

