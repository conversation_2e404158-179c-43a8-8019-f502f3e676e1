plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")


android {
    namespace 'com.coloros.filemanager.appswitch'
}


dependencies {
    implementation project(':Common')
    implementation project(':Provider')
    implementation libs.koin.android
    implementation libs.google.gson
    implementation libs.apache.commons.io
    implementation libs.androidx.lifecycle.process
    compileOnly(libs.oplus.addon.sdk) { artifact { type = "aar" } }
}