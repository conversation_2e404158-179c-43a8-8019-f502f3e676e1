/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AudioPreviewViewModel.kt
 * Description:
 *     The view model for preview audio file.
 *
 * Version: 1.0
 * Date: 2024-10-09
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-10-09   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.audio

import android.content.Context
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.preview.utils.logInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

internal class AudioPreviewViewModel : FilePreviewViewModel() {

    private companion object {
        private const val TAG = "AudioPreviewViewModel"
    }

    private val audioMetadata by lazy { MutableLiveData<AudioFileMetadata>() }

    @Volatile
    var audioPlayer: IPreviewAudioPlayer? = null
        private set

    @Synchronized
    fun obtainAudioPlayer(context: Context): IPreviewAudioPlayer {
        audioPlayer?.let { return it }
        val newPlayer = IPreviewAudioPlayer.obtain(context)
        audioPlayer = newPlayer
        return newPlayer
    }

    @Synchronized
    fun releaseAudioPlayer() {
        val player = audioPlayer ?: return
        audioPlayer = null
        player.release()
    }

    fun releaseAudioMetadata() {
        audioMetadata.value?.coverImage?.let {
            if (!it.isRecycled) {
                it.recycle()
            }
        }
        audioMetadata.value = null
    }

    override fun onCleared() {
        super.onCleared()
        releaseAudioPlayer()
        releaseAudioMetadata()
    }

    override fun loadData() {
        val audioFile = pickPreviewFile()
        if (audioFile == null) {
            Log.e(TAG, "loadData: audioFile=null")
            audioMetadata.value = null
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            val metadata = detectAudioMetadata(audioFile)
            withContext(Dispatchers.Main) {
                audioMetadata.value = metadata
            }
        }
    }

    fun loadAudioMetadata(
        lifecycleOwner: LifecycleOwner,
        onLoaded: Observer<AudioFileMetadata?>
    ) {
        lifecycleOwner.lifecycleScope.launch {
            loadAndObserverAudioMetadata(lifecycleOwner, onLoaded)
        }
    }

    private fun loadAndObserverAudioMetadata(
        lifecycleOwner: LifecycleOwner,
        onLoaded: Observer<AudioFileMetadata?>
    ) {
        val currentValue = audioMetadata.value
        onLoaded.onChanged(currentValue)
        audioMetadata.observe(lifecycleOwner, onLoaded)
    }

    fun releaseAudioMetadataLoader(onLoaded: Observer<AudioFileMetadata?>) {
        audioMetadata.removeObserver(onLoaded)
    }

    private fun detectAudioMetadata(audioFile: BaseFileBean): AudioFileMetadata? = runCatching {
        MediaMetadataRetriever().use {
            it.setDataSource(audioFile.mData)
            val coverImage = it.embeddedPicture?.let { coverData ->
                BitmapFactory.decodeByteArray(coverData, 0, coverData.size)
            }
            AudioFileMetadata(
                fileName = audioFile.mDisplayName,
                artist = it.extractMetadata(MediaMetadataRetriever.METADATA_KEY_ARTIST),
                coverImage = coverImage
            )
        }
    }.onFailure {
        Log.e(TAG, "detectAudioMetadata: ERROR when load for ${audioFile.logInfo()}, err=$it")
    }.getOrNull()
}