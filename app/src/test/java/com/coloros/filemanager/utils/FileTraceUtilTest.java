/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: FileTraceUtilTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/8/30
 ** Author: <PERSON><PERSON><PERSON><PERSON>
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/8/30      1.0     create
 ****************************************************************/
package com.coloros.filemanager.utils;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mockStatic;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

import com.coloros.filemanager.BaseTest;
import com.filemanager.common.compat.FeatureCompat;
import com.filemanager.common.utils.FileTraceUtil;

import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.shadows.ShadowLog;

@Ignore("stupid test")
@RunWith(RobolectricTestRunner.class)
public class FileTraceUtilTest extends BaseTest {
    private FileTraceUtil mFileTraceUtil;
    private Context mContext;
    private PackageManager mPackageManager;
    private PackageInfo mPackageInfo;
    private StringBuilder mVersion;

    @Before
    public void setUp() throws Exception {
        ShadowLog.stream = System.out;
        mContext = RuntimeEnvironment.application;
        mFileTraceUtil = FileTraceUtil.getInstance();
        mFileTraceUtil = FileTraceUtil.getInstance();
        mPackageManager = mContext.getPackageManager();
        mPackageInfo = mPackageManager.getPackageInfo(mContext.getPackageName(),
                PackageManager.GET_META_DATA);
        mVersion = new StringBuilder(mPackageInfo.versionName);
    }

    @After
    public void tearDown() {
        mFileTraceUtil = null;
        mContext = null;
        mPackageManager = null;
        mPackageInfo = null;
        mVersion = null;
    }

    @Test
    public void should_return_when_getVersion_with_needDateIsTrue_needCommitIsFalse() {
        Object versionDateObject = mPackageInfo.applicationInfo.metaData.get("versionDate");
        String versionDate = versionDateObject.toString();
        mVersion.append("_").append(versionDate);
        String expected = mVersion.toString();
        if (FeatureCompat.getSIsExpRom()) {
            assertEquals("", mFileTraceUtil.getVersion(mContext, true, false));
        } else {
            assertEquals(expected, mFileTraceUtil.getVersion(mContext, true, false));
        }
    }


    @Test
    public void should_return_when_getVersion_with_getSIsExpRomIsFalse() {
        MockedStatic featureCompatMockedStatic = mockStatic(FeatureCompat.class);
        featureCompatMockedStatic.when(() -> FeatureCompat.getSIsExpRom()).thenReturn(true);
        assertEquals("", mFileTraceUtil.getVersion(null, true, false));
        featureCompatMockedStatic.close();
    }

    //deleteFileManagerTrace
    @Test
    public void should_return_when_deleteTraceFileIfExpRom() {
        MockedStatic featureCompatMockedStatic = mockStatic(FeatureCompat.class);
        featureCompatMockedStatic.when(() -> FeatureCompat.getSIsExpRom()).thenReturn(false);
        mFileTraceUtil.deleteTraceFileIfExpRom();
        featureCompatMockedStatic.when(() -> FeatureCompat.getSIsExpRom()).thenReturn(true);
        mFileTraceUtil.deleteTraceFileIfExpRom();
        featureCompatMockedStatic.close();
    }


    //traceAtThisMoment
    @Test
    public void should_return_when_traceAtThisMoment() {
        MockedStatic featureCompatMockedStatic = mockStatic(FeatureCompat.class);
        featureCompatMockedStatic.when(() -> FeatureCompat.getSIsExpRom()).thenReturn(true);
        mFileTraceUtil.traceAtThisMoment();
        featureCompatMockedStatic.when(() -> FeatureCompat.getSIsExpRom()).thenReturn(false);
        mFileTraceUtil.traceAtThisMoment();
        featureCompatMockedStatic.close();
    }

    //traceAction
    @Test
    public void should_return_when_traceAction() {
        MockedStatic featureCompatMockedStatic = mockStatic(FeatureCompat.class);
        featureCompatMockedStatic.when(() -> FeatureCompat.getSIsExpRom()).thenReturn(true);
        mFileTraceUtil.traceAction(null, 1, null);
        featureCompatMockedStatic.when(() -> FeatureCompat.getSIsExpRom()).thenReturn(false);
        mFileTraceUtil.traceAction("test", 1, "pass");
        featureCompatMockedStatic.close();
    }

}