/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LocalMixLoaderTest
 ** Description : LocalMixLoaderTest Unit Test
 ** Version     : 1.0
 ** Date        : 2023/2/21
 ** Author      : wanghonglei
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  wanghonglei     2023/2/21      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui.loader

import android.content.Context
import android.util.SparseArray
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.oplus.filemanager.category.globalsearch.bean.CategoryFileWrapper
import com.oplus.filemanager.category.globalsearch.bean.MoreFileWrapper
import com.oplus.filemanager.category.globalsearch.bean.SearchLabelWrapper
import com.oplus.filemanager.dfm.DFMManager
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class LocalMixLoaderTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
        mockkStatic(FeatureCompat::class)
        mockkStatic(DFMManager::class)
        every { DFMManager.getCurrentStatus() } returns DFMManager.STATUS_META_DATA_OK
        every { DFMManager.openP2pConnectAndWaitDFSReady() } returns true
        every { DFMManager.checkStateSearchAvalable() } returns true
        every { DFMManager.getDFSDeviceName() } returns "OPPOPAD"
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getInternalSdPath(context) } returns "/storage/emulated/0"
        every { VolumeEnvironment.getExternalSdPath(context) } returns "/storage/emulated/0"
        mockkStatic(CategoryHelper::class)
        every {
            CategoryHelper.setIgnoredPath(context, CategoryHelper.CATEGORY_DOC)
        } returns SparseArray(8)
        mockkObject(FileLabelDBHelper)
    }

    @After
    fun tearDown() {
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(DFMManager::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(CategoryHelper::class)
        unmockkObject(FileLabelDBHelper)
    }

    @Test
    fun `should loader not null when call if method is initLoaders`() {
        // Given
        val searchApi = mockk<IDmpSearchApi>()
        every { searchApi.isShouldLoadDMP() } returns true

        // when
        val localMixLoader = LocalMixLoader()
        //every { localMixLoader.dmpSearchApi } returns searchApi

        // Then
        Assert.assertEquals(true, localMixLoader.dfmLoader != null)
        Assert.assertEquals(true, localMixLoader.localLoader is GlobalSearchNormalLoader)
        Assert.assertEquals(true, localMixLoader.dfmLoader != null)
        Assert.assertEquals(true, localMixLoader.dfmLoader is GlobalSearchDFMLoader)
    }

    @Test
    fun `should return boolean when call if method is checkAndInitDfm`() {
        // Given
        val localMixLoader = spyk(LocalMixLoader())
        localMixLoader.setSearchKey("1")
        val searchApi = mockk<IDmpSearchApi>()
        every { localMixLoader.dmpSearchApi } returns searchApi
        every { searchApi.isDMPCursor(any()) } returns false
        //When
        localMixLoader.checkAndInitDfm()
        // Then
        Assert.assertEquals(true, localMixLoader.dfmAvailable)
        Assert.assertEquals(false, localMixLoader.supportDfm)
    }

    @Test
    fun `should return cut to 5 items when call if method is genAllTabData`() {
        // Given
        val localMixLoader = spyk(LocalMixLoader())
        localMixLoader.setSearchKey("1")
        val searchApi = mockk<IDmpSearchApi>()
        every { localMixLoader.dmpSearchApi } returns searchApi
        every { searchApi.isDMPCursor(any()) } returns false
        every { searchApi.initDmpSdk(any()) } returns false
        val tabFiles: ArrayList<BaseFileBean> = ArrayList<BaseFileBean>()
        val allFiles = mutableListOf<BaseFileBean>()
        val directory1 = BaseFileBean().apply {
            this.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
            this.mData = "directory1"
        }
        val directory2 = BaseFileBean().apply {
            this.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
            this.mData = "directory2"
        }
        val directory3 = BaseFileBean().apply {
            this.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
            this.mData = "directory3"
        }
        allFiles.add(directory1)
        allFiles.add(directory2)
        allFiles.add(directory3)

        val labelFiles: MutableList<BaseFileBean> = mutableListOf()
        val label1 = SearchLabelWrapper(FileLabelEntity(0, "生活", 0, 0, 0, 0, null, null))
        val label2 = SearchLabelWrapper(FileLabelEntity(0, "工作", 0, 0, 0, 0, null, null))
        val label3 = SearchLabelWrapper(FileLabelEntity(0, "重要", 0, 0, 0, 0, null, null))
        labelFiles.add(label1)
        labelFiles.add(label2)
        labelFiles.add(label3)
        // When
        searchApi.initDmpSdk(context)
        localMixLoader.genAllTabData(tabFiles, allFiles, labelFiles, true)
        // Then
        Assert.assertEquals(true, tabFiles.size == 6)
        Assert.assertEquals(true, tabFiles[0] is CategoryFileWrapper)
        Assert.assertEquals(true, tabFiles[1] is SearchLabelWrapper)
        Assert.assertEquals(true, tabFiles[2] is SearchLabelWrapper)
        Assert.assertEquals(true, tabFiles[3].isDir())
        Assert.assertEquals(true, tabFiles[4].isDir())
        Assert.assertEquals(true, tabFiles[5] is MoreFileWrapper)
    }

    @Test
    fun `should addItem when call if method is loadInBackground`() {
        // Given
        val localMixLoader = spyk(LocalMixLoader())
        localMixLoader.setSearchKey("1")
        val searchApi = mockk<IDmpSearchApi>()
        every { localMixLoader.dmpSearchApi } returns searchApi
        every { searchApi.initDmpSdk(any()) }
        every { FileLabelDBHelper.getFileLabelByNameFuzzy(any()) } returns null
        // When
        val result = localMixLoader.loadInBackground()
        // Then
        Assert.assertEquals(true, result?.searchKey == "1")
    }

    @Test
    fun `should return null when call if method is onDestory`() {
        // Given
        val localMixLoader = spyk(LocalMixLoader())
        localMixLoader.setSearchKey("1")
        val searchApi = mockk<IDmpSearchApi>()
        every { localMixLoader.dmpSearchApi } returns searchApi
        every { searchApi.initDmpSdk(any()) }
        every { FileLabelDBHelper.getFileLabelByNameFuzzy(any()) } returns null
        // When
        localMixLoader.onDestroy()
        // Then
        Assert.assertEquals(true, localMixLoader.localDmpLoader == null)
        Assert.assertEquals(true, localMixLoader.localMediaLoader == null)
        Assert.assertEquals(true, localMixLoader.localLoader == null)
        Assert.assertEquals(true, localMixLoader.dfmLoader == null)
    }
}