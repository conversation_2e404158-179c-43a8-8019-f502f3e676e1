/*
 * ********************************************************************
 *  * * Copyright (C), 2021 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  FileDeleteHelperTest.java
 *  * * Description : FileDeleteHelperTest.java
 *  * * Version     : 1.0
 *  * * Date        : 21-12-14 下午5:34
 *  * * Author      : W9010863
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.coloros.filemanager.utils

import android.content.pm.ApplicationInfo
import android.graphics.Bitmap
import com.filemanager.common.utils.AppInfo
import io.mockk.MockKAnnotations
import io.mockk.mockk
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AppInfoTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should verify when simple method`() {
        var appInfo = AppInfo()

        appInfo.packageName = "test"
        Assert.assertEquals("test", appInfo.packageName)

        appInfo.appName = "test"
        Assert.assertEquals("test", appInfo.appName)

        var bitmap = mockk<Bitmap>()
        appInfo.icon = bitmap
        Assert.assertEquals(bitmap, appInfo.icon)

        appInfo.versionName = "test"
        Assert.assertEquals("test", appInfo.versionName)

        var applicationInfo = mockk<ApplicationInfo>()
        appInfo.applicationInfo = applicationInfo
        Assert.assertEquals(applicationInfo, appInfo.applicationInfo)

        appInfo.versionCode = 100
        Assert.assertEquals(100, appInfo.versionCode)
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}