package com.filemanager.common.back

import android.app.Dialog
import android.view.KeyEvent
import android.view.MotionEvent
import android.window.OnBackInvokedCallback
import android.window.OnBackInvokedDispatcher
import androidx.appcompat.app.AppCompatActivity
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.StatusBarUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * PredictiveBackUtils工具类的单元测试类
 * 用于测试预测性返回功能的各种场景
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [33])  // 修改SDK版本为33以匹配T版本要求
class PredictiveBackUtilsTest {

    // 模拟对象声明
    private lateinit var mockDispatcher: OnBackInvokedDispatcher
    private lateinit var mockCallback: OnBackInvokedCallback
    private lateinit var mockActivity: AppCompatActivity
    private lateinit var mockFragment: COUIPanelFragment
    private lateinit var mockParentFragment: COUIBottomSheetDialogFragment
    private lateinit var mockDialog: Dialog

    /**
     * 测试前的初始化方法
     * 1. 模拟静态类
     * 2. 创建各模拟对象
     * 3. 设置模拟对象的默认行为
     */
    @Before
    fun setUp() {
        // 模拟静态工具类
        mockkStatic(SdkUtils::class)
        mockkStatic(StatusBarUtil::class)
        mockkStatic(Log::class)
        
        // 创建各模拟对象
        mockDispatcher = mockk(relaxed = true)
        mockCallback = mockk(relaxed = true)
        mockActivity = mockk(relaxed = true)
        mockFragment = mockk(relaxed = true)
        mockParentFragment = mockk(relaxed = true)
        mockDialog = mockk(relaxed = true)
        
        // 设置Log类的模拟行为
        every { Log.d(any(), any()) } returns Unit
        every { Log.w(any(), any()) } returns Unit
        every { Log.i(any(), any()) } returns Unit
        // 设置状态栏工具类的模拟行为
        every { StatusBarUtil.checkIsGestureNavMode(any()) } returns false
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试isSupport方法在SDK版本>=T时的返回结果
     */
    @Test
    fun `isSupport should return true when SDK is at least T`() {
        // 模拟SDK版本>=T
        every { SdkUtils.isAtLeastT() } returns true
        assert(PredictiveBackUtils.isSupport())
    }

    /**
     * 测试isSupport方法在SDK版本<T时的返回结果
     */
    @Test
    fun `isSupport should return false when SDK is below T`() {
        // 模拟SDK版本<T
        every { SdkUtils.isAtLeastT() } returns false
        assert(!PredictiveBackUtils.isSupport())
    }

    /**
     * 测试registerBackCallback方法在支持预测性返回时的注册行为
     */
    @Test
    fun `registerBackCallback should register callback when supported`() {
        // 模拟支持预测性返回
        every { SdkUtils.isAtLeastT() } returns true
        PredictiveBackUtils.registerBackCallback(mockDispatcher, mockCallback)
        // 验证回调注册方法被调用
        verify { mockDispatcher.registerOnBackInvokedCallback(OnBackInvokedDispatcher.PRIORITY_DEFAULT, mockCallback) }
    }

    /**
     * 测试registerBackCallback方法在不支持预测性返回时的行为
     */
    @Test
    fun `registerBackCallback should not register callback when not supported`() {
        // 模拟不支持预测性返回
        every { SdkUtils.isAtLeastT() } returns false
        PredictiveBackUtils.registerBackCallback(mockDispatcher, mockCallback)
        // 验证回调注册方法未被调用
        verify(exactly = 0) { mockDispatcher.registerOnBackInvokedCallback(any(), any()) }
    }

    /**
     * 测试unregisterBackCallback方法在支持预测性返回时的注销行为
     */
    @Test
    fun `unregisterBackCallback should unregister callback when supported`() {
        // 模拟支持预测性返回
        every { SdkUtils.isAtLeastT() } returns true
        PredictiveBackUtils.unregisterBackCallback(mockDispatcher, mockCallback)
        // 验证回调注销方法被调用
        verify { mockDispatcher.unregisterOnBackInvokedCallback(mockCallback) }
    }

    /**
     * 测试unregisterBackCallback方法在不支持预测性返回时的行为
     */
    @Test
    fun `unregisterBackCallback should not unregister callback when not supported`() {
        // 模拟不支持预测性返回
        every { SdkUtils.isAtLeastT() } returns false
        PredictiveBackUtils.unregisterBackCallback(mockDispatcher, mockCallback)
        // 验证回调注销方法未被调用
        verify(exactly = 0) { mockDispatcher.unregisterOnBackInvokedCallback(any()) }
    }

    /**
     * 测试registerOnBackInvokedCallback方法对Activity的处理
     */
    @Test
    fun `registerOnBackInvokedCallback for activity should attach monitor when supported`() {
        // 模拟支持预测性返回
        every { SdkUtils.isAtLeastT() } returns true
        PredictiveBackUtils.registerOnBackInvokedCallback(mockActivity, mockCallback)
    }

    /**
     * 测试registerOnBackInvokedCallback方法对Fragment的处理
     */
    @Test
    fun `registerOnBackInvokedCallback for fragment should set listener when supported`() {
        // 模拟支持预测性返回
        every { SdkUtils.isAtLeastT() } returns true
        // 设置Fragment的父Fragment和Dialog
        every { mockFragment.parentFragment } returns mockParentFragment
        every { mockParentFragment.dialog } returns mockDialog
        
        PredictiveBackUtils.registerOnBackInvokedCallback(mockFragment, mockCallback)
        // 验证设置本地监听器方法被调用
        verify { mockFragment.setOnBackInvokedLocalListener(any()) }
    }

    /**
     * 测试registerOnBackInvokedCallback方法对Dialog的处理
     */
    @Test
    fun `registerOnBackInvokedCallback for dialog should register callback when supported`() {
        // 模拟支持预测性返回
        every { SdkUtils.isAtLeastT() } returns true
        // 设置Dialog的返回调度器
        every { mockDialog.onBackInvokedDispatcher } returns mockDispatcher
        
        PredictiveBackUtils.registerOnBackInvokedCallback(mockDialog, mockCallback)
        // 验证回调注册方法被调用
        verify { mockDispatcher.registerOnBackInvokedCallback(OnBackInvokedDispatcher.PRIORITY_DEFAULT, any()) }
    }
}