<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/footer_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dimen_16dp"
    android:paddingBottom="@dimen/dimen_32dp"
    android:layout_marginLeft="@dimen/dimen_16dp"
    android:layout_marginRight="@dimen/dimen_16dp"
    android:background="?attr/couiColorBackgroundWithCard"
    tools:ignore="MissingDefaultResource">

    <TextView
        android:id="@+id/footer_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top|center_horizontal"
        android:gravity="center"
        android:paddingLeft="@dimen/dimen_16dp"
        android:paddingRight="@dimen/dimen_16dp"
        android:textColor="@color/black_55_percent"
        android:textSize="@dimen/file_list_item_detail_text_size" />
</FrameLayout>