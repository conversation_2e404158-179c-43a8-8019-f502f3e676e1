<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundWithCard"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:splitMotionEvents="false">

    <com.filemanager.common.view.FileManagerRecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <include layout="@layout/appbar_with_divider_layout_secondary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>