/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.audio
 ** Version: 1.0
 ** Date: 2021/7/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.audio

import androidx.annotation.Keep
import com.bumptech.glide.load.EncodeStrategy
import com.bumptech.glide.load.Options
import com.bumptech.glide.load.ResourceEncoder
import com.bumptech.glide.load.engine.Resource
import com.filemanager.thumbnail.utils.Log
import java.io.*

/**
 * convert AudioThumbnailResultByteArray to ObjectOutputStream, and then cache the object to File
 * this is for the transformed resource
 */
@Keep
class AudioThumbnailResultByteArrayResourceEncoder : ResourceEncoder<AudioThumbnailResultByteArrayResource> {

    companion object {
        private const val TAG = "AudioThumbnailResultByteArrayResourceEncoder"
    }

    override fun encode(
        data: Resource<AudioThumbnailResultByteArrayResource>,
        file: File,
        options: Options
    ): Boolean {
        val resource = data.get()
        val os: OutputStream?
        return try {
            os = ObjectOutputStream(FileOutputStream(file))
            os.use {
                it.writeObject(resource)
            }
            true
        } catch (e: Exception) {
            Log.d(TAG, "Failed to encode Bitmap: $e")
            false
        }
    }

    override fun getEncodeStrategy(options: Options): EncodeStrategy {
        return EncodeStrategy.TRANSFORMED
    }
}