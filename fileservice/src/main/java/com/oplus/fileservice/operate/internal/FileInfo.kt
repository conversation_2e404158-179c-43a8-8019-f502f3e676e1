/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileInfo
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/7 21:10
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/7       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal

import com.filemanager.common.base.BaseFileBean

data class FileInfo(
    var displayName: String? = null,
    var data: String? = null,
    var recycleId: String? = null
) : BaseFileBean() {
    init {
        mData = data
    }
}
