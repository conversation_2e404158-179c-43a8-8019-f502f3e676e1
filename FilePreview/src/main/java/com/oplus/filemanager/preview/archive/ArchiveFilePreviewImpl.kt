/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ArchiveFilePreviewImpl.kt
 * Description:
 *     The implementation of previewing archive file
 *
 * Version: 1.0
 * Date: 2024-10-16
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-10-16   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.archive

import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.utils.Log
import com.filemanager.compresspreview.ui.CompressPreviewViewModel
import com.oplus.filemanager.preview.core.AbsPreviewLoadingScheduler
import com.oplus.filemanager.preview.widget.PreviewOperationsBar

internal class ArchiveFilePreviewImpl(
    private val lifecycleOwner: LifecycleOwner,
    private val previewModel: ArchivePreviewViewModel
) : IArchiveFilePreview {

    private companion object {
        private const val TAG = "ArchiveFilePreviewImpl"
    }

    private val loadingScheduler = PreviewLoadingScheduler()
    private val onPreviewDataLaunched by lazy { ::onLaunchedPreviewData }

    private var containerManager: IArchivePreviewContainerManager? = null
    private var operationsBar: PreviewOperationsBar? = null

    override fun attachToContainer(containerManager: IArchivePreviewContainerManager) {
        this.containerManager = containerManager
        previewModel.pickPreviewFile()?.let {
            containerManager.defaultInfoUi.apply {
                setPreviewFile(it)
                setNotifyMessage(null)
            }
        }
        loadingScheduler.requireToShowLoading()
        previewModel.launchArchivePreviewData(lifecycleOwner, onPreviewDataLaunched)
    }

    override fun bindOperationsBar(bar: PreviewOperationsBar?) {
        operationsBar = bar
    }

    override fun release() {
        previewModel.releaseArchivePreviewDataLauncher(onPreviewDataLaunched)
        loadingScheduler.resetLoadingScheduler()
        containerManager = null
    }

    private fun handlePreviewData(
        loadResult: ArchivePreviewLoadResult,
        compressModel: CompressPreviewViewModel
    ): Unit = loadResult.run {
        Log.d(TAG, "handlePreviewData: resultCode=$resultCode")
        if (resultCode == ArchivePreviewLoadResult.PREVIEW_LOAD_RESULT_DONE) {
            containerManager?.previewCardUi?.apply {
                setPreviewFile(previewFile)
                setPreviewContents(compressModel)
            }
            containerManager?.showAsFileContainer(false)
        } else {
            containerManager?.defaultInfoUi?.apply {
                setPreviewFile(previewFile)
                setNotifyMessage(errorMessage)
            }
            containerManager?.showAsFileContainer(true)
        }
        if (resultCode == ArchivePreviewLoadResult.PREVIEW_LOAD_RESULT_IS_OTA) {
            operationsBar?.setOpenButtonText(com.filemanager.common.R.string.install_string)
        } else {
            operationsBar?.setOpenButtonText(com.filemanager.common.R.string.unzip)
        }
        containerManager?.fillFileDetailInfo(previewFile)
    }

    private fun onLaunchedPreviewData(
        loadResult: ArchivePreviewLoadResult,
        compressModel: CompressPreviewViewModel
    ) {
        val data = PreviewData(loadResult, compressModel)
        loadingScheduler.dismissLoading(data)
    }

    private inner class PreviewLoadingScheduler : AbsPreviewLoadingScheduler() {
        override val subTag: String = TAG

        override fun onShowLoading() {
            containerManager?.showAsLoading()
        }

        override fun onGetStartLoadingTime(): Long? = containerManager?.startLoadingTime

        override fun onDismissLoading(extraObj: Any?) {
            val data = extraObj as? PreviewData ?: return
            handlePreviewData(data.loadResult, data.compressModel)
        }
    }

    private data class PreviewData(
        val loadResult: ArchivePreviewLoadResult,
        val compressModel: CompressPreviewViewModel
    )
}