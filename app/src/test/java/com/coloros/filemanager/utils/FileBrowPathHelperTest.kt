package com.coloros.filemanager.utils

import android.content.Context
import android.text.TextUtils
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.FileBrowPathHelper
import com.filemanager.common.utils.KtUtils
import io.mockk.*
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class FileBrowPathHelperTest {

    private lateinit var mFileBrowPathHelper: FileBrowPathHelper

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        var context = mockk<Context>(relaxed = true) {
            every { applicationContext }.returns(this)
            every { resources }.returns(mockk(relaxed = true) {
                every { getString(any()) }.returns("test")
            })
        }
        MyApplication.init(context)
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getInternalSdPath(any()) }.returns(null)
        every { VolumeEnvironment.getExternalSdPath(any()) }.returns(null)
        every { VolumeEnvironment.getOTGPath(any()) }.returns(null)
        mFileBrowPathHelper = FileBrowPathHelper("test pass")
    }

    @Test
    fun `should verify when constructor without`() {
        var fileBrowPathHelper = FileBrowPathHelper("InternalSd")
        verify { fileBrowPathHelper.updateRootPath("InternalSd-test") }
    }

    @Test
    fun `should verify when update root path without`() {
        mockkStatic(VolumeEnvironment::class)

        every { VolumeEnvironment.getInternalSdPath(any()) }.returns("InternalSd")
        every { VolumeEnvironment.getExternalSdPath(any()) }.returns(null)
        every { VolumeEnvironment.getOTGPath(any()) }.returns(null)
        mFileBrowPathHelper.updateRootPath("InternalSd-test")
        Assert.assertEquals("InternalSd", mFileBrowPathHelper.getRootPath())
        Assert.assertEquals("test", mFileBrowPathHelper.getCurrentDirectoryName("InternalSd"))
        Assert.assertFalse(mFileBrowPathHelper.isAtInternalSD("test pass"))

        every { VolumeEnvironment.getInternalSdPath(any()) }.returns(null)
        every { VolumeEnvironment.getExternalSdPath(any()) }.returns("ExternalSd")
        mFileBrowPathHelper.updateRootPath("ExternalSd-test")
        Assert.assertEquals("ExternalSd", mFileBrowPathHelper.getRootPath())
        Assert.assertFalse(mFileBrowPathHelper.isAtExternalSD("test pass"))

        every { VolumeEnvironment.getExternalSdPath(any()) }.returns(null)
        mockkObject(KtUtils)
        every { KtUtils.checkIsMultiAppPath(any()) }.returns(false)

        every { VolumeEnvironment.getOTGPath(any()) }.returns(null)
        mFileBrowPathHelper.updateRootPath("test pass")
        Assert.assertEquals("ExternalSd", mFileBrowPathHelper.getRootPath())



        every { VolumeEnvironment.getOTGPath(any()) }.returns(List(1) { "OTG" })
        mFileBrowPathHelper.updateRootPath("OTG-test")
        Assert.assertEquals("OTG", mFileBrowPathHelper.getRootPath())
        Assert.assertTrue(mFileBrowPathHelper.isRootOtgPath("OTG"))
    }

    @Test
    fun `should verify when get current directory name without`() {
        mockkStatic(TextUtils::class)
        every { TextUtils.isEmpty("null") }.returns(true)
        Assert.assertEquals(null, mFileBrowPathHelper.getCurrentDirectoryName("null"))
    }


    @Test
    fun `should verify when pop without`() {

        Assert.assertEquals(null, mFileBrowPathHelper.pop())

        mFileBrowPathHelper.push(FileBrowPathHelper.PathInfo("test", 200, 200))
        mFileBrowPathHelper.push(FileBrowPathHelper.PathInfo("test", 100, 100))

        Assert.assertNotNull(mFileBrowPathHelper.pop())
    }


    @Test
    fun `should verify when push without`() {

        Assert.assertEquals(1, mFileBrowPathHelper.push(null))

        var pathInfo = FileBrowPathHelper.PathInfo("test", 200, 200)
        mFileBrowPathHelper.push(pathInfo)

        Assert.assertEquals(2, mFileBrowPathHelper.push(pathInfo))
        Assert.assertEquals(2, mFileBrowPathHelper.push(pathInfo))
    }


    @Test
    fun `should verify when simple method without`() {

        Assert.assertNull(mFileBrowPathHelper.getExternalPath())
        Assert.assertNull(mFileBrowPathHelper.getInternalPath())
        Assert.assertEquals("test pass", mFileBrowPathHelper.getRootPath())
        Assert.assertNull(mFileBrowPathHelper.getOtgPath())
        Assert.assertEquals(1, mFileBrowPathHelper.getCount())
        Assert.assertEquals(1, mFileBrowPathHelper.getPathLeft())
        Assert.assertNotNull(mFileBrowPathHelper.getRootPathInfo())
        Assert.assertFalse(mFileBrowPathHelper.isRootPath("ttttt"))
        Assert.assertTrue(mFileBrowPathHelper.isRootPath("test pass"))

        Assert.assertFalse(mFileBrowPathHelper.isRootExternalPath("test pass"))
        Assert.assertFalse(mFileBrowPathHelper.isRootInternalPath("test pass"))
        Assert.assertFalse(mFileBrowPathHelper.isAtInternalSD("test pass"))
        Assert.assertFalse(mFileBrowPathHelper.isAtExternalSD("test pass"))

        Assert.assertNotNull(mFileBrowPathHelper.getTopPathInfo())

    }


    @Test
    fun `should verify when set top path without`() {
        Assert.assertNull(mFileBrowPathHelper.setTopPath(-1))

        mFileBrowPathHelper.push(FileBrowPathHelper.PathInfo("test", 100, 100))
        mFileBrowPathHelper.push(FileBrowPathHelper.PathInfo("test2", 200, 200))
        mFileBrowPathHelper.push(FileBrowPathHelper.PathInfo("test3", 300, 300))
        Assert.assertNotNull(mFileBrowPathHelper.setTopPath(2))
    }

    @Test
    fun `should verify when set current path index without`() {
        mFileBrowPathHelper.setCurrentPathIndex(1)
        mFileBrowPathHelper.push(FileBrowPathHelper.PathInfo("test", 100, 100))
        mFileBrowPathHelper.push(FileBrowPathHelper.PathInfo("test2", 200, 200))
        mFileBrowPathHelper.push(FileBrowPathHelper.PathInfo("test3", 300, 300))
        mFileBrowPathHelper.setCurrentPathIndex(1)

    }

    @Test
    fun `should verify when get path index without`() {
        Assert.assertNull(mFileBrowPathHelper.getPath(-1))
        mFileBrowPathHelper.push(FileBrowPathHelper.PathInfo("test", 100, 100))
        mFileBrowPathHelper.push(FileBrowPathHelper.PathInfo("test2", 200, 200))
        mFileBrowPathHelper.push(FileBrowPathHelper.PathInfo("test3", 300, 300))
        Assert.assertNotNull(mFileBrowPathHelper.getPath(1))
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}