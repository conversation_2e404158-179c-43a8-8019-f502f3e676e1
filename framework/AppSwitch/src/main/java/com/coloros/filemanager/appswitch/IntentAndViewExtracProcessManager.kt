/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : IntentAndViewExtracProcessManager.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch

import android.content.Intent
import android.util.Log
import android.util.LruCache
import com.coloros.filemanager.appswitch.IntentAndViewExtracProcessManager.DbOperationData.Companion.DB_OPERATION_DONOTHING
import com.coloros.filemanager.appswitch.IntentAndViewExtracProcessManager.DbOperationData.Companion.DB_OPERATION_INSERT
import com.coloros.filemanager.appswitch.IntentAndViewExtracProcessManager.DbOperationData.Companion.DB_OPERATION_UPDATE
import com.coloros.filemanager.appswitch.IntentAndViewExtracProcessManager.DbOperationData.Companion.INDEX_OPERATION_DONOTHING
import com.coloros.filemanager.appswitch.IntentAndViewExtracProcessManager.DbOperationData.Companion.INDEX_OPERATION_INSERT
import com.coloros.filemanager.appswitch.IntentAndViewExtracProcessManager.DbOperationData.Companion.INDEX_OPERATION_UPDATE
import com.coloros.filemanager.appswitch.index.IndexHelper
import com.coloros.filemanager.appswitch.process.IIntentProcessFactory
import com.coloros.filemanager.appswitch.process.IViewExactProcess
import com.coloros.filemanager.appswitch.process.IntenProcessFactory
import com.coloros.filemanager.appswitch.process.IntentProcess
import com.coloros.filemanager.appswitch.util.InitHelper
import com.coloros.filemanager.appswitch.util.MaxCntChecker
import com.coloros.filemanager.appswitch.util.StaticUtil
import com.coloros.filemanager.appswitch.util.StaticUtil.DETECT_DB_INSERT_UPDATE_STEP1_ERROR
import com.coloros.filemanager.appswitch.util.StaticUtil.DETECT_MAX_CNT_REACHED_ERROR
import com.coloros.filemanager.appswitch.util.StaticUtil.DETECT_REG_MATCH_ERROR
import com.coloros.filemanager.appswitch.util.StaticUtil.DETECT_VIEW_EXACTED_ERROR_CALLBACK
import com.coloros.filemanager.appswitch.util.StaticUtil.DETECT_VIEW_EXACTED_ERROR_EMPTY_RESULT
import com.coloros.filemanager.appswitch.util.StaticUtil.DETECT_VIEW_EXACTED_ERROR_NO_WINDOW_FOUND
import com.coloros.filemanager.appswitch.util.StaticUtil.DETECT_VIEW_EXACTED_ERROR_UNKOWN
import com.coloros.filemanager.appswitch.util.Util
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper.ViewExactResultCallBack.Companion.RESULT_CODE_FAILED_NOTARGET_FOUND
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper.ViewExactResultCallBack.Companion.RESULT_CODE_FAILED_UNKOWN
import com.filemanager.common.bean.SearchIndexBean
import com.filemanager.common.constants.ThirdAppConstants.DINGTALK_PACKAGE
import com.filemanager.common.constants.ThirdAppConstants.FEISHU_PACKAGE
import com.filemanager.common.constants.ThirdAppConstants.QQ_PACKAGE
import com.filemanager.common.constants.ThirdAppConstants.WECHAT_PACKAGE
import com.filemanager.common.constants.ThirdAppConstants.WEWORK_PACKAGE
import com.filemanager.common.utils.AppUtils
import com.oplus.app.OplusAppEnterInfo
import com.oplus.filemanager.provider.ThirdAppFileDBHelper
import com.oplus.filemanager.room.model.ThirdAppFileDataEntity
import java.util.LinkedList
import java.util.Queue

class IntentAndViewExtracProcessManager {

    companion object {

        const val TAG = "IntentAndViewExtracProcessManager"

        const val INTENT_CACHE_MAX_SIZE = 100
    }


    private var factory: IIntentProcessFactory = IntenProcessFactory.get()

    private var viewExtractHelper: ViewExtractHelper = ViewExtractHelper()

    @Volatile
    private var dingTalkProcess: IntentProcess? = null

    @Volatile
    private var weChatProcess: IntentProcess? = null

    @Volatile
    private var weWorkProcess: IntentProcess? = null

    @Volatile
    private var feishuProcess: IntentProcess? = null

    @Volatile
    private var qqProcess: IntentProcess? = null

    @Volatile
    private var defaultProcess: IntentProcess? = null

    private var intentCache: LruCache<String, List<ThirdAppFileDataEntity>> = LruCache(INTENT_CACHE_MAX_SIZE)

    var indexHelper: IndexHelper = IndexHelper()

    //在dmp未初始化成功前，这里的采集的数据
    private var pendingIndexQueue: Queue<DbOperationData> = LinkedList<DbOperationData>()

    fun processAppEnterInfo(info: OplusAppEnterInfo) {
        val inputIntent: Intent = info.intent
        val packageName = getPackageNameFromIntent(inputIntent)
        val previewActivity = info.targetName
        val packageVersion = AppUtils.getAppVersionCode(packageName)
        if (packageName == null) {
            Log.e(TAG, "processAppEnterInfo packageName from intent is null, return")
            return
        } else if (previewActivity == null) {
            Log.e(TAG, "processAppEnterInfo targetActivity from OplusAppEnterInfo is null, return")
            return
        } else {
            val process: IntentProcess
            try {
                process = getIntentProcessByPackageName(packageName)
            } catch (e: IllegalStateException) {
                Log.e(TAG, "processAppEnterInfo packageName $packageName not supproted", e)
                return
            }
            val keyDataBean = process.getKeyDataBeanFromIntent(inputIntent)
            if (!checkFileNameMeet(keyDataBean)) {
                Log.i(TAG, "processAppEnterInfo fileName suffix not meet, no more process, just return")
                return
            }
            val preKeyDataBeanHashCode = keyDataBean.hashCode().toString()
            Log.i(TAG, "processAppEnterInfo process $process, keyDataBean $keyDataBean, intentHashCode $preKeyDataBeanHashCode")
            if (intentCache.get(preKeyDataBeanHashCode) != null) {
                Log.i(TAG, "processAppEnterInfo has already processed data, no more process, just return")
                return
            } else {
                startViewExactProcess(
                    keyDataBean,
                    packageName,
                    packageVersion,
                    previewActivity,
                    process,
                    preKeyDataBeanHashCode
                )
            }
        }
    }

    private fun startViewExactProcess(
        keyDataBean: IViewExactProcess.KeyDataBean,
        packageName: String,
        packageVersion: Int,
        previewActivity: String,
        process: IntentProcess,
        preKeyDataBeanHashCode: String
    ) {
        val requestId = keyDataBean.toString() + "hashCode:" + keyDataBean.hashCode().toString()
        Log.i(TAG, "processAppEnterInfo keyDataBean $keyDataBean")
        val request = ViewExtractHelper.ViewExtracRequest(
            requestId = requestId,
            packageName = packageName,
            packageVersion = packageVersion,
            preViewActivity = previewActivity,
            callBack = object : ViewExtractHelper.ViewExactResultCallBack {
                override fun onSingleViewExactResult(requestId: String, result: ViewExtractHelper.ViewExtractResult, resultCode: Int) {
                    Log.i(TAG, "processAppEnterInfo onViewExactResult requestId $requestId, result $result, resultCode $resultCode")
                    if (result.isDataValid() && resultCode == ViewExtractHelper.ViewExactResultCallBack.RESULT_CODE_SUC) {
                        val resultEntity = processViewExactResult(process, result, previewActivity, keyDataBean, packageName, false)
                        val inputFileNameEmpty = keyDataBean.fileName.isEmpty()
                        Log.i(TAG, "processAppEnterInfo onViewExactResult fileNameEmpty $inputFileNameEmpty, resultEntity $resultEntity")
                        if (resultEntity.isNotEmpty() && !inputFileNameEmpty) {
                            //这里针对输入fileName为空时(微信才会存在这种情况)，会走将目标页面中其他文件提取的逻辑，其他文件提取结果不放在缓存中
                            intentCache.put(preKeyDataBeanHashCode, resultEntity)
                        }
                    } else {
                        if (result.isDefault) {
                            StaticUtil.uploadViewExactErrorStatic(result, DETECT_VIEW_EXACTED_ERROR_EMPTY_RESULT)
                        }
                        Log.e(TAG, "processAppEnterInfo viewExact error result $result, resultCode $resultCode")
                    }
                }

                override fun onViewExactStarted(requestId: String, innerRequestCnt: Int) {
                    Log.i(TAG, "onViewExactStarted requestId $requestId, innerRequestCnt $innerRequestCnt")
                }

                override fun onViewExactCompleted(
                    requestId: String,
                    failResults: List<ViewExtractHelper.ViewExtractResult>,
                    isAllSuc: Boolean,
                    hasFailedRequest: Boolean,
                    resultCode: Int
                ) {
                    Log.i(TAG, "onViewExactCompleted $requestId, failResults ${failResults.size}, isAllSuc $isAllSuc, hasFaile $hasFailedRequest")
                    if (hasFailedRequest) {
                        val errorCode = when (resultCode) {
                            RESULT_CODE_FAILED_UNKOWN -> DETECT_VIEW_EXACTED_ERROR_UNKOWN
                            RESULT_CODE_FAILED_NOTARGET_FOUND -> DETECT_VIEW_EXACTED_ERROR_NO_WINDOW_FOUND
                            else -> DETECT_VIEW_EXACTED_ERROR_CALLBACK
                        }
                        failResults.forEach {
                            StaticUtil.uploadViewExactErrorStatic(it, errorCode)
                        }
                    }
                }
            })
        viewExtractHelper.tryExtractViewFromWindow(request)
    }

    private fun checkFileNameMeet(keyDataBean: IViewExactProcess.KeyDataBean): Boolean {
        val fileName = keyDataBean.fileName
        val suffixList = AppSwitchManager.currentThirdAppListenConfigParam?.detectSuffix
        val fileNameMeetSuffix = Util.checkFileNameSuffixMeet(fileName, suffixList, true)
        Log.i(TAG, "checkFileNameMeet keyDataBean $keyDataBean, result $fileNameMeetSuffix")
        return fileNameMeetSuffix
    }


    fun processViewExactResult(
        process: IntentProcess,
        result: ViewExtractHelper.ViewExtractResult,
        previewActivity: String,
        keyDataBean: IViewExactProcess.KeyDataBean,
        packageName: String,
        ignoreMaxCntLimit: Boolean = true
    ): List<ThirdAppFileDataEntity> {
        val viewExactInfo = IViewExactProcess.ViewExactInfo(
            result.viewExactString,
            result.viewExactActivity,
            result.preViewActivity,
            packageName,
            result.packageVersion
        )
        val postDataBeanList = process.processDataBeanByViewExtraString(
            viewExactInfo,
            keyDataBean
        )
        val processedSucEntityList = mutableListOf<ThirdAppFileDataEntity>()
        val currentBlowMaxCnt = if (ignoreMaxCntLimit) true else MaxCntChecker.checkBelowMaxCnt()
        Log.i(TAG, "processViewExactResult belowMaxCnt $currentBlowMaxCnt")
        for (postDataBean in postDataBeanList) {
            if (!postDataBean.checkDataValidate()) {
                StaticUtil.uploadRegMatchErrorStatic(result, postDataBean, DETECT_REG_MATCH_ERROR)
                Log.e(TAG, "processViewExactResult postDataBean notValid $postDataBean")
                continue
            }
            val dbOperationData = getDbOperationData(result.viewExactActivity, packageName, previewActivity, postDataBean, result.viewExactString)
            var outputResult: ThirdAppFileDataEntity? = null
            if (dbOperationData.dbOperation == DB_OPERATION_INSERT || dbOperationData.dbOperation == DB_OPERATION_UPDATE) {
                val dbResult = ThirdAppFileDBHelper.insertOrUpdateThirdAppFileData(dbOperationData.data)
                if (dbResult > 0) {
                    if (currentBlowMaxCnt) {
                        dbOperationData.data.id = dbResult
                        outputResult = processInsertOrUpdateIndex(dbOperationData)
                    } else {
                        StaticUtil.uploadRegMatchErrorStatic(result, postDataBean, DETECT_MAX_CNT_REACHED_ERROR)
                        Log.w(TAG, "processViewExactResult db and index process not complete because currentBlowMaxCnt $currentBlowMaxCnt")
                    }
                } else {
                    StaticUtil.uploadDbOrDmpErrorStatic(dbOperationData.data, DETECT_DB_INSERT_UPDATE_STEP1_ERROR)
                    Log.w(TAG, "processViewExactResult db and index process not complete because " +
                            "dbResult $dbResult currentBlowMaxCnt $currentBlowMaxCnt")
                }
            } else {
                //新采集的数据和数据库中记录的数据相同，需要看一下是否需要触发一次Index操作(老的index未创建，indentification未空时，需要index进行insert，但是不返回值给缓存)
                Log.i(TAG, "processViewExactResult only index insert or update, indexOperation ${dbOperationData.indexOperation}")
                if (currentBlowMaxCnt) {
                    processInsertOrUpdateIndex(dbOperationData)
                } else {
                    StaticUtil.uploadRegMatchErrorStatic(result, postDataBean, DETECT_MAX_CNT_REACHED_ERROR)
                    Log.w(TAG, "processViewExactResult not complete because currentBlowMaxCnt $currentBlowMaxCnt ")
                }
            }
            if (outputResult != null) {
                processedSucEntityList.add(outputResult)
            }
        }
        if (!ignoreMaxCntLimit && processedSucEntityList.isNotEmpty()) {
            MaxCntChecker.increaseAndRefershMaxCnt()
        }
        return processedSucEntityList
    }

    private fun processInsertOrUpdateIndex(dbOperationData: DbOperationData): ThirdAppFileDataEntity? {
        var outputResult: ThirdAppFileDataEntity? = null
        if (dbOperationData.indexOperation == INDEX_OPERATION_INSERT) {
            val putPending = checkAndPutPending(dbOperationData)
            if (putPending) {
                Log.w(TAG, "processInsertOrUpdateIndex indexOperation ${dbOperationData.indexOperation}, put pending queue")
                return null
            }
            val indexSuc = indexHelper.trigIndexInsert(dbOperationData.data)
            //index添加失败，不赋值，说明本地操作失败，不放在缓存中
            if (indexSuc) {
                outputResult = dbOperationData.data
                StaticUtil.uploadDetectSucStatic(dbOperationData.data)
            }
        } else if (dbOperationData.indexOperation == INDEX_OPERATION_UPDATE) {
            val putPending = checkAndPutPending(dbOperationData)
            if (putPending) {
                Log.w(TAG, "processInsertOrUpdateIndex indexOperation ${dbOperationData.indexOperation}, put pending queue")
                return null
            }
            val indexSuc = indexHelper.trigIndexUpdate(dbOperationData.data)
            //index更新失败，不赋值，说明本地操作失败，不放在缓存中
            if (indexSuc) {
                outputResult = dbOperationData.data
                StaticUtil.uploadDetectSucStatic(dbOperationData.data)
            }
        } else {
            //index不需要更新或新增，说明数据库中的数据和采集的数据相同，同时本地的identification不为空时，已经存在索引，只是把数据库中读取的内容放在缓存中，下次进来不需要走一次数据库逻辑
            Log.i(TAG, "processInsertOrUpdateIndex indexOperation ${dbOperationData.indexOperation}, no index operation")
            outputResult = dbOperationData.data
        }
        return outputResult
    }


    private fun checkAndPutPending(dbOperationData: DbOperationData): Boolean {
        var needPutPending = false
        if (InitHelper.isInInitProgress()) {
            pendingIndexQueue.add(dbOperationData)
            needPutPending = true
        }
        return needPutPending
    }

    fun flushPending() {
        Log.i(TAG, "flushPending START")
        if (InitHelper.isInitSuc()) {
            processPendingDmpSuc()
        } else if (InitHelper.isInitFailed()) {
            processPendingDmpFailed()
        }
        Log.i(TAG, "flushPending END")
    }

    private fun processPendingDmpSuc() {
        Log.i(TAG, "processPendingDmpSuc START pendingIndexQueue.size ${pendingIndexQueue.size}")
        pendingIndexQueue.forEach {
            //将dmp初始化成功之前采集到的数据,从
            processInsertOrUpdateIndex(it)
        }
        Log.i(TAG, "processPendingDmpSuc END")
        pendingIndexQueue.clear()
    }

    private fun processPendingDmpFailed() {
        Log.i(TAG, "processPendingIndexInsertOrUpdate START pendingIndexQueue.size ${pendingIndexQueue.size}")
        pendingIndexQueue.forEach {
            //将dmp初始化成功之前的采集的db数据删除
            val dbId = it.data.id
            if (dbId != null) {
                ThirdAppFileDBHelper.deleteThirdAppFileDataById(dbId)
            }
        }
        Log.i(TAG, "processPendingIndexInsertOrUpdate END")
        pendingIndexQueue.clear()
    }


    private fun getDbOperationData(
        viewExtractActivity: String,
        packageName: String,
        previewActivity: String,
        keyDataBean: IViewExactProcess.KeyDataBean,
        metaData: String
    ): DbOperationData {
        //这里主要是为了解决一个极端场景（IndexSyncHelper中修复之前失效数据时，数据库中同时存在两条记录，一条待修复记录，一条待修复记录之后的正确记录，需要找到待修复记录通过主键id查找数据）
        val dbId = keyDataBean.dbId
        val dbThirdAppFileDataEntity = if (dbId != null) {
            ThirdAppFileDBHelper.getThirdAppFileDataById(dbId)
        } else {
            ThirdAppFileDBHelper.getThirdAppFileData(keyDataBean.fileName, packageName, keyDataBean.sourceName)
        }
        val result = if (dbThirdAppFileDataEntity != null) {
            //需要更新
            processDbOperationDataUpdate(
                dbThirdAppFileDataEntity,
                viewExtractActivity,
                packageName,
                previewActivity,
                keyDataBean,
                metaData,
            )
        } else {
            //需要新增
            processDbOperationDataInsert(
                viewExtractActivity,
                packageName,
                previewActivity,
                keyDataBean,
                metaData,
            )
        }
        return result
    }

    private fun processDbOperationDataInsert(
        viewExtractActivity: String,
        packageName: String,
        previewActivity: String,
        keyDataBean: IViewExactProcess.KeyDataBean,
        metaData: String
    ): DbOperationData {
        val sourcePackageVersion = AppUtils.getAppVersionCode(packageName)
        val sourceActivity = viewExtractActivity
        val parseVersion = AppSwitchManager.currentThirdAppListenConfigParam?.version ?: 0
        val insertEntitiy = ThirdAppFileDataEntity(
            mFileName = keyDataBean.fileName,
            mFileSize = keyDataBean.getFileSizeLong(),
            mUid = keyDataBean.uid,
            mDectPackage = packageName,
            mDectPackageVersion = sourcePackageVersion.toString(),
            mSourceType = keyDataBean.sourceType,
            mSourceName = keyDataBean.sourceName,
            mSourceActivity = sourceActivity,
            mPreviewActivity = previewActivity,
            mFileSendTime = keyDataBean.getTimeLong(),
            mDetectTime = System.currentTimeMillis(),
            mMetaData = metaData,
            mIndexCheckSum = SearchIndexBean.DEFAULT_CHECK_SUM,
            mIndentification = "",
            mParseVersion = parseVersion
        )
        Log.i(TAG, "processDbOperationDataInsert packageName $packageName, keyDataBean $keyDataBean,sourcePackageVersion $sourcePackageVersion, " +
                "sourceActivity: $sourceActivity, parseVersion $parseVersion, insertEntitiy $insertEntitiy")
        return DbOperationData(DB_OPERATION_INSERT, INDEX_OPERATION_INSERT, insertEntitiy)
    }

    private fun processDbOperationDataUpdate(
        dbThirdAppFileDataEntity: ThirdAppFileDataEntity,
        viewExtractActivity: String,
        packageName: String,
        previewActivity: String,
        keyDataBean: IViewExactProcess.KeyDataBean,
        metaData: String
    ): DbOperationData {
        val sourcePackageVersion = AppUtils.getAppVersionCode(packageName)
        val parseVersion = AppSwitchManager.currentThirdAppListenConfigParam?.version ?: 0
        val dbAndIndexOperation = getDbAndIndexOperation(keyDataBean, dbThirdAppFileDataEntity, metaData, parseVersion)
        val dbOperation = dbAndIndexOperation.first
        val indexOperation = dbAndIndexOperation.second
        val updateEntity = ThirdAppFileDataEntity(
            id = dbThirdAppFileDataEntity.id,
            mFileName = keyDataBean.fileName,
            mFileSize = keyDataBean.getFileSizeLong(),
            mUid = keyDataBean.uid,
            mDectPackage = packageName,
            mDectPackageVersion = sourcePackageVersion.toString(),
            mSourceType = keyDataBean.sourceType,
            mSourceName = keyDataBean.sourceName,
            mSourceActivity = viewExtractActivity,
            mPreviewActivity = previewActivity,
            mFileSendTime = keyDataBean.getTimeLong(),
            mDetectTime = if (dbOperation == DB_OPERATION_UPDATE) System.currentTimeMillis() else dbThirdAppFileDataEntity.mDetectTime,
            mMetaData = metaData,
            mIndexCheckSum = when (indexOperation) {
                INDEX_OPERATION_UPDATE -> dbThirdAppFileDataEntity.mIndexCheckSum + 1
                INDEX_OPERATION_INSERT -> SearchIndexBean.DEFAULT_CHECK_SUM
                else -> dbThirdAppFileDataEntity.mIndexCheckSum
            },
            mIndentification = dbThirdAppFileDataEntity.mIndentification,
            mParseVersion = parseVersion
        )
        Log.i(TAG, "processDbOperationDataUpdate packageName $packageName, keyDataBean $keyDataBean, dbOperation $dbOperation, " +
                "indexOperation $indexOperation, parseVersion $parseVersion, updateEntity $updateEntity, dbEntity $dbThirdAppFileDataEntity")
        return DbOperationData(dbOperation, indexOperation, updateEntity)
    }

    private fun getDbAndIndexOperation(
        keyDataBean: IViewExactProcess.KeyDataBean,
        dbThirdAppFileDataEntity: ThirdAppFileDataEntity,
        metaData: String,
        parseVersion: Int
    ): Pair<Int, Int> {
        val baseDataEqual = checkBasicDataEqual(keyDataBean, dbThirdAppFileDataEntity)
        val metaDataEqual = checkMetaDataEqual(metaData, dbThirdAppFileDataEntity)
        val uidEqual = checkUidEqual(keyDataBean, dbThirdAppFileDataEntity)
        val parseVersionEqual = checkParseVersionEqual(parseVersion, dbThirdAppFileDataEntity)
        var dbOperation = DB_OPERATION_DONOTHING
        if (!baseDataEqual
            || !metaDataEqual
            || !uidEqual
            || !parseVersionEqual
        ) {
            dbOperation = DB_OPERATION_UPDATE
        }
        var indexOperation = INDEX_OPERATION_DONOTHING
        if (dbThirdAppFileDataEntity.mIndentification.isNullOrEmpty()) {
            indexOperation = INDEX_OPERATION_INSERT
        } else if (dbOperation == DB_OPERATION_UPDATE) {
            if (!baseDataEqual) {
                indexOperation = INDEX_OPERATION_UPDATE
            }
        }
        Log.i(TAG, "getDbAndIndexOperation dbOperation $dbOperation, indexOperation $indexOperation")
        return Pair<Int, Int>(dbOperation, indexOperation)
    }


    private fun checkBasicDataEqual(
        keyDataBean: IViewExactProcess.KeyDataBean,
        dataEntity: ThirdAppFileDataEntity
    ): Boolean {
        val fileNameEqual = keyDataBean.fileName == dataEntity.mFileName
        val fileSizeEqual = keyDataBean.getFileSizeLong() == dataEntity.mFileSize
        val fileTimeEqual = keyDataBean.getTimeLong() == dataEntity.mFileSendTime
        val sourceTypeEqual = keyDataBean.sourceType == dataEntity.mSourceType
        val sourceNameEqual = keyDataBean.sourceName == dataEntity.mSourceName
        Log.i(TAG, "checkBasicDataEqual fileNameEqual $fileNameEqual, fileSizeEqual $fileSizeEqual" +
                "fileTimeEqual $fileTimeEqual, sourceTypeEqual $sourceTypeEqual, sourceNameEqual $sourceNameEqual")
        return fileNameEqual && fileSizeEqual && fileTimeEqual && sourceTypeEqual && sourceNameEqual
    }

    private fun checkMetaDataEqual(metaData: String, dataEntity: ThirdAppFileDataEntity): Boolean {
        val metaDataEqual = metaData == dataEntity.mMetaData
        Log.i(TAG, "checkMetaDataEqual equal: $metaDataEqual")
        return metaDataEqual
    }

    private fun checkUidEqual(keyDataBean: IViewExactProcess.KeyDataBean, dataEntity: ThirdAppFileDataEntity): Boolean {
        val uidEqual = keyDataBean.uid == dataEntity.mUid
        Log.i(TAG, "checkUidEqual equal: $uidEqual")
        return uidEqual
    }

    private fun checkParseVersionEqual(currentParseVersion: Int, dataDbEntity: ThirdAppFileDataEntity): Boolean {
        val parseVersionEqual = currentParseVersion == dataDbEntity.mParseVersion
        Log.i(TAG, "checkParseVersionEqual equal $parseVersionEqual")
        return parseVersionEqual
    }


    private fun getPackageNameFromIntent(inputIntent: Intent): String? {
        val result = inputIntent.component?.packageName ?: inputIntent.`package`
        Log.i(TAG, "getPackageNameFromIntent $result")
        return result
    }


    fun getIntentProcessByPackageName(packageString: String): IntentProcess {
        return when (packageString) {
            FEISHU_PACKAGE -> getOrCreateFeishuProcess()
            WECHAT_PACKAGE -> getOrCreateWechatProcess()
            DINGTALK_PACKAGE -> getOrCreateDingTalkProcess()
            WEWORK_PACKAGE -> getOrCreateWeworkProcess()
            QQ_PACKAGE -> getOrCreateQQProcess()
            else -> getOrCreateDefaultProcess(packageString)
        }
    }

    private fun getOrCreateFeishuProcess(): IntentProcess {
        val result: IntentProcess
        if (feishuProcess == null) {
            result = factory.createIntentProcess(FEISHU_PACKAGE)
            feishuProcess = result
        } else {
            result = feishuProcess as IntentProcess
        }
        return result
    }

    private fun getOrCreateWechatProcess(): IntentProcess {
        val result: IntentProcess
        if (weChatProcess == null) {
            result = factory.createIntentProcess(WECHAT_PACKAGE)
            weChatProcess = result
        } else {
            result = weChatProcess as IntentProcess
        }
        return result
    }

    private fun getOrCreateWeworkProcess(): IntentProcess {
        val result: IntentProcess
        if (weWorkProcess == null) {
            result = factory.createIntentProcess(WEWORK_PACKAGE)
            weWorkProcess = result
        } else {
            result = weWorkProcess as IntentProcess
        }
        return result
    }

    private fun getOrCreateDingTalkProcess(): IntentProcess {
        val result: IntentProcess
        if (dingTalkProcess == null) {
            result = factory.createIntentProcess(DINGTALK_PACKAGE)
            dingTalkProcess = result
        } else {
            result = dingTalkProcess as IntentProcess
        }
        return result
    }

    private fun getOrCreateQQProcess(): IntentProcess {
        val result: IntentProcess
        if (qqProcess == null) {
            result = factory.createIntentProcess(QQ_PACKAGE)
            qqProcess = result
        } else {
            result = qqProcess as IntentProcess
        }
        return result
    }


    private fun getOrCreateDefaultProcess(packageString: String): IntentProcess {
        val result: IntentProcess
        if (defaultProcess == null) {
            result = factory.createIntentProcess(packageString)
            defaultProcess = result
        } else {
            result = defaultProcess as IntentProcess
        }
        return result
    }


    data class DbOperationData(
        var dbOperation: Int = DB_OPERATION_DONOTHING,
        var indexOperation: Int = INDEX_OPERATION_DONOTHING,
        var data: ThirdAppFileDataEntity
    ) {

        companion object {
            const val TAG = "DbOperationData"
            //代表数据库不做任何操作
            const val DB_OPERATION_DONOTHING = 0
            const val DB_OPERATION_INSERT = 1
            const val DB_OPERATION_UPDATE = 2

            const val INDEX_OPERATION_DONOTHING = 0
            const val INDEX_OPERATION_INSERT = 1
            const val INDEX_OPERATION_UPDATE = 2
        }
    }
}