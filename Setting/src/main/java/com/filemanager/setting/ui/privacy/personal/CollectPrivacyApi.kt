/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: CollectPrivacyApiImpl
 * * Description: Collect Privacy Api impl
 * * Version: 1.0
 * * Date : 2024/08/28
 * * Author:W9060445
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     W9060445         2024/08/27      1.0            create
 ****************************************************************/
package com.filemanager.setting.ui.privacy.personal

import android.os.Looper
import com.oplus.filemanager.interfaze.privacy.ICollectPrivacyApi
import com.oplus.filemanager.provider.CollectPrivacyDBHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.MainScope

class CollectPrivacyApi : ICollectPrivacyApi {

    override fun collect(@ICollectPrivacyApi.PrivacyCategory category: String, content: String?) {
        if (isMainThread()) {
            MainScope().launch(Dispatchers.IO) {
                CollectPrivacyDBHelper.insert(category, content)
            }
        } else {
            CollectPrivacyDBHelper.insert(category, content)
        }
    }

    /**
     * 是否在主线程A
     */
    private fun isMainThread(): Boolean {
        return Thread.currentThread() == Looper.getMainLooper().thread
    }
}