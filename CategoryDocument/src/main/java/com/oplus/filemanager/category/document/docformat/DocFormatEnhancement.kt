package com.oplus.filemanager.category.document.docformat

import android.provider.MediaStore
import android.text.TextUtils
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.WhiteListParser
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout
import java.util.*
import kotlin.collections.ArrayList
import kotlin.coroutines.cancellation.CancellationException

object DocFormatEnhancement {
    const val DOC_TYPE_DOC = ".doc"
    const val DOC_TYPE_DOCX = ".docx"
    const val DOC_TYPE_XLS = ".xls"
    const val DOC_TYPE_XLSX = ".xlsx"
    const val DOC_TYPE_PPT = ".ppt"
    const val DOC_TYPE_PPTX = ".pptx"

    const val DOC_TYPE_IWORK = ".iwork"
    const val DOC_TYPE_CAD = ".cad"
    const val DOC_TYPE_VISIO = ".visio"
    const val DOC_TYPE_KEYNOTE = ".key"
    const val DOC_TYPE_NUMBERS = ".numbers"
    const val DOC_TYPE_PAGES = ".pages"
    const val DOC_TYPE_XMIND = ".xmind"
    const val DOC_TYPE_DWG = ".dwg"
    const val DOC_TYPE_DWT = ".dwt"
    const val DOC_TYPE_DXF = ".dxf"
    const val DOC_TYPE_MD = ".md"
    const val DOC_TYPE_PDF = ".pdf"
    const val DOC_TYPE_OFD = ".ofd"
    const val DOC_TYPE_TXT = ".txt"
    const val DOC_TYPE_PSD = ".psd"
    const val DOC_TYPE_AI = ".ai"
    const val DOC_TYPE_VSDX = ".vsdx"
    const val DOC_TYPE_VSDM = ".vsdm"
    const val DOC_TYPE_VSTX = ".vstx"
    const val DOC_TYPE_VSTM = ".vstm"
    const val DOC_TYPE_VSSX = ".vssx"
    const val DOC_TYPE_VSSM = ".vssm"
    const val DOC_TYPE_VSD = ".vsd"
    const val DOC_TYPE_VSS = ".vss"
    const val DOC_TYPE_VST = ".vst"
    const val DOC_TYPE_VDW = ".vdw"

    const val FILTER_DOC_PDF = 1 shl 0
    const val FILTER_DOC_XLS = 1 shl 1
    const val FILTER_DOC_DOC = 1 shl 2
    const val FILTER_DOC_PPT = 1 shl 3
    const val FILTER_DOC_TXT = 1 shl 4
    const val FILTER_DOC_OFD = 1 shl 5
    const val FILTER_DOC_IWORK = 1 shl 6
    const val FILTER_DOC_CAD = 1 shl 7
    const val FILTER_DOC_XMIND = 1 shl 8
    const val FILTER_DOC_MD = 1 shl 9
    const val FILTER_DOC_PSD = 1 shl 10
    const val FILTER_DOC_AI = 1 shl 11
    const val FILTER_DOC_VISIO = 1 shl 12

    private const val TAG = "DocFormatEnhancement"
    private const val LOAD_TIME_OUT = 15 * 1000L
    val CATEGORY_DOC = arrayOf(
        DOC_TYPE_TXT,
        DOC_TYPE_DOC,
        DOC_TYPE_DOCX,
        DOC_TYPE_XLS,
        DOC_TYPE_XLSX,
        DOC_TYPE_PPT,
        DOC_TYPE_PPTX,
        DOC_TYPE_PDF,
        DOC_TYPE_OFD,
        DOC_TYPE_KEYNOTE,
        DOC_TYPE_NUMBERS,
        DOC_TYPE_PAGES,
        DOC_TYPE_DWG,
        DOC_TYPE_DWT,
        DOC_TYPE_DXF,
        DOC_TYPE_MD,
        DOC_TYPE_XMIND,
        DOC_TYPE_PSD,
        DOC_TYPE_AI,
        DOC_TYPE_VSDX,
        DOC_TYPE_VSDM,
        DOC_TYPE_VSTX,
        DOC_TYPE_VSTM,
        DOC_TYPE_VSSX,
        DOC_TYPE_VSSM,
        DOC_TYPE_VSD,
        DOC_TYPE_VSS,
        DOC_TYPE_VST,
        DOC_TYPE_VDW
    )

    /**
     * 得到属于文档的所有文件格式（在默认的基础上添加上随心开支持的类型）
     */
    fun getDocumentFormat(): ArrayList<String> {
        val lists = ArrayList<String>()
        lists.addAll(WhiteListParser.getDocumentFormat())
        if (FeatureCompat.sIsExpRom) {
            lists.apply {
                if (this.contains(DOC_TYPE_TXT)) {
                    remove(DOC_TYPE_TXT)
                }
                if (this.contains(DOC_TYPE_OFD).not()) {
                    add(DOC_TYPE_OFD)
                }
                add(DOC_TYPE_TXT)
            }
        } else {
            lists.apply {
                if (this.contains(DOC_TYPE_TXT)) {
                    remove(DOC_TYPE_TXT)
                }
                if (this.contains(DOC_TYPE_OFD).not()) {
                    add(DOC_TYPE_OFD)
                }
                add(DOC_TYPE_IWORK)
                add(DOC_TYPE_XMIND)
                add(DOC_TYPE_VISIO)
                add(DOC_TYPE_TXT)
                add(DOC_TYPE_CAD)
                add(DOC_TYPE_PSD)
                add(DOC_TYPE_AI)
                add(DOC_TYPE_MD)
            }
        }
        Log.d(TAG, "getDocumentFormat lists = $lists")
        return lists
    }

    /**
     * 返回一个查询文档类型文件数量的sql语句
     */
    val documentCountSqlQuery: String by lazy {
        val builder = StringBuilder(MediaStoreCompat.DEFAULT_CAPACITY)
        builder.append("(")
        var extension: String?
        val selectLimit = CATEGORY_DOC.size - 1
        for (index in CATEGORY_DOC.indices) {
            extension = CATEGORY_DOC[index]
            builder.append(MediaStore.Files.FileColumns.DISPLAY_NAME)
                .append(" LIKE '%")
                .append(extension)
            if (index < selectLimit) {
                builder.append("' OR ")
            } else {
                builder.append("'")
            }
        }
        builder.append(")")
        if (SdkUtils.isAtLeastR()) {
            MediaStoreCompat.addFilterMultiAppClause(builder)
            MediaStoreCompat.addFilterSize(builder)
        }
        builder.toString()
    }

    /**
     * @param selectionArg selection argument for query
     * @return sql string for document query
     */
    fun getDocumentSqlQuery(selectionArg: ArrayList<String?>): String {
        val builder = StringBuilder(MediaStoreCompat.DEFAULT_CAPACITY)
        if (SdkUtils.isAtLeastR()) {
            MediaStoreCompat.addFilterMultiAppClause(builder)
        }
        // no selection arg,just return now
        if (selectionArg.isEmpty()) {
            Log.d(TAG, "getDocumentSqlQuery selectionArg is null")
            return builder.toString()
        }
        if (builder.isNotEmpty()) {
            builder.append(" AND ")
        }
        builder.append("(")
        var index = 0
        for (selection in selectionArg) {
            if (TextUtils.isEmpty(selection)) {
                continue
            }
            if (index > 0) {
                builder.append(" OR ")
            }
            builder.append(MediaStore.Files.FileColumns.DISPLAY_NAME)
                .append(" LIKE '%")
                .append(selection)
                .append("'")
            index++
        }
        builder.append(")")
        Log.d(TAG, "getDocumentSqlQuery selection = $builder")
        return builder.toString()
    }

    /**
     * 得到所有的搜索tag
     */
    fun getAllSelectionArgs(fileExts: ArrayList<String>?): ArrayList<String?> {
        val selectionArgs = ArrayList<String?>()
        //如果没有选择搜索tag，则把所有符合要求的搜索的文档类型的文件都列出
        if (fileExts.isNullOrEmpty()) {
            for (item in CATEGORY_DOC) {
                selectionArgs.add(item)
            }
        } else {
            for (fileExt in fileExts) {
                if (DOC_TYPE_IWORK.equals(fileExt, true)) {
                    selectionArgs.add(DOC_TYPE_KEYNOTE)
                    selectionArgs.add(DOC_TYPE_NUMBERS)
                    selectionArgs.add(DOC_TYPE_PAGES)
                } else if (DOC_TYPE_CAD.equals(fileExt, true)) {
                    selectionArgs.add(DOC_TYPE_DWG)
                    selectionArgs.add(DOC_TYPE_DWT)
                    selectionArgs.add(DOC_TYPE_DXF)
                } else if (DOC_TYPE_MD.equals(fileExt, true)) {
                    selectionArgs.add(DOC_TYPE_MD)
                } else if (DOC_TYPE_XMIND.equals(fileExt, true)) {
                    selectionArgs.add(DOC_TYPE_XMIND)
                } else if (DOC_TYPE_PSD.equals(fileExt, true)) {
                    selectionArgs.add(DOC_TYPE_PSD)
                } else if (DOC_TYPE_AI.equals(fileExt, true)) {
                    selectionArgs.add(DOC_TYPE_AI)
                } else if (DOC_TYPE_VISIO.equals(fileExt, true)) {
                    selectionArgs.apply {
                        add(DOC_TYPE_VSDX)
                        add(DOC_TYPE_VSDM)
                        add(DOC_TYPE_VSTX)
                        add(DOC_TYPE_VSTM)
                        add(DOC_TYPE_VSSX)
                        add(DOC_TYPE_VSSM)
                        add(DOC_TYPE_VSD)
                        add(DOC_TYPE_VSS)
                        add(DOC_TYPE_VST)
                        add(DOC_TYPE_VDW)
                    }
                } else {
                    selectionArgs.add(fileExt)
                    if (!DOC_TYPE_PDF.equals(fileExt, true)
                        && !DOC_TYPE_OFD.equals(fileExt, true)
                        && !DOC_TYPE_TXT.equals(fileExt, true)) {
                        selectionArgs.add("${fileExt}x")
                    }
                }
            }
        }
        return selectionArgs
    }

    /**
     * 文档类型的所有格式
     */
    fun getSearchArrayForDoc(oldArray: IntArray): Pair<IntArray, Array<String>> {
        val lists = ArrayList<Int>(oldArray.toList())
        if (lists.contains(FILTER_DOC_TXT)) {
            lists.remove(FILTER_DOC_TXT)
        }
        if (lists.contains(FILTER_DOC_OFD)) {
            lists.remove(FILTER_DOC_OFD)
        }
        lists.apply {
            add(FILTER_DOC_OFD)
            add(FILTER_DOC_IWORK)
            add(FILTER_DOC_XMIND)
            add(FILTER_DOC_VISIO)
            add(FILTER_DOC_TXT)
            add(FILTER_DOC_CAD)
            add(FILTER_DOC_PSD)
            add(FILTER_DOC_AI)
            add(FILTER_DOC_MD)
        }
        val array: IntArray = lists.toIntArray()
        val strArray = appContext.resources.getStringArray(com.filemanager.common.R.array.search_filter_doc_ext_enhancement)
        return Pair(array, strArray)
    }

    fun getArrayExtByFilterItem(itemId: Int): Array<String> {
        Log.d(TAG, "getArrayExtByFilterItem itemId = $itemId")
        return when (itemId) {
            FILTER_DOC_IWORK -> arrayOf(DOC_TYPE_KEYNOTE.uppercase(), DOC_TYPE_NUMBERS.uppercase(), DOC_TYPE_PAGES.uppercase())
            FILTER_DOC_CAD -> arrayOf(DOC_TYPE_DWG.uppercase(), DOC_TYPE_DWT.uppercase(), DOC_TYPE_DXF.uppercase())
            FILTER_DOC_MD -> arrayOf(DOC_TYPE_MD.uppercase())
            FILTER_DOC_XMIND -> arrayOf(DOC_TYPE_XMIND.uppercase())
            FILTER_DOC_PSD -> arrayOf(DOC_TYPE_PSD.uppercase())
            FILTER_DOC_AI -> arrayOf(DOC_TYPE_AI.uppercase())
            FILTER_DOC_VISIO -> {
                arrayOf(
                    DOC_TYPE_VSDX.uppercase(),
                    DOC_TYPE_VSDM.uppercase(),
                    DOC_TYPE_VSTX.uppercase(),
                    DOC_TYPE_VSTM.uppercase(),
                    DOC_TYPE_VSSX.uppercase(),
                    DOC_TYPE_VSSM.uppercase(),
                    DOC_TYPE_VSD.uppercase(),
                    DOC_TYPE_VSS.uppercase(),
                    DOC_TYPE_VST.uppercase(),
                    DOC_TYPE_VDW.uppercase()
                )
            }
            else -> {
                arrayOf("")
            }
        }
    }

    /**
     * 根据文件的mimetype来对文件进行分类
     */
    fun <T : BaseFileBean> classifyFileByMimeType(file: T, folderFiles: ArrayList<T>,
                                                  imageFiles: ArrayList<T>,
                                                  videoFiles: ArrayList<T>,
                                                  audioFiles: ArrayList<T>,
                                                  docFiles: ArrayList<T>,
                                                  appFiles: ArrayList<T>,
                                                  compressFiles: ArrayList<T>,
                                                  otherFiles: ArrayList<T>) {
        when {
            file.mLocalType == MimeTypeHelper.DIRECTORY_TYPE -> {
                folderFiles.add(file)
            }
            file.mLocalType == MimeTypeHelper.IMAGE_TYPE -> {
                imageFiles.add(file)
            }
            file.mLocalType == MimeTypeHelper.VIDEO_TYPE -> {
                videoFiles.add(file)
            }
            MimeTypeHelper.isAudioType(file.mLocalType) -> {
                audioFiles.add(file)
            }
            MimeTypeHelper.isDocType(file.mLocalType) || MimeTypeHelper.isOtherDocType(file.mLocalType) -> docFiles.add(file)
            file.mLocalType == MimeTypeHelper.APPLICATION_TYPE -> {
                appFiles.add(file)
            }
            file.mLocalType == MimeTypeHelper.COMPRESSED_TYPE -> {
                compressFiles.add(file)
            }
            else -> {
                otherFiles.add(file)
            }
        }
    }

    fun isDocEnhancement(file: BaseFileBean): Boolean {
        file.mData?.let { path ->
            if (path.lastIndexOf(".") < 0) {
                return false
            }
            val ext = path.subSequence(path.lastIndexOf("."), path.length)
            for (item in CATEGORY_DOC) {
                if (ext == item) {
                    return true
                }
            }
        }
        return false
    }

    fun sortFileIgnoreHeadLabel(files: List<BaseFileBean>, order: Int, lastMode: Int, isDesc: Boolean) {
        try {
            Log.d(TAG, "sortFileIgnoreHeadLabel -> order = $order ; ${files is ArrayList}")
            if ((order == SortHelper.FILE_TYPE_ORDER) && (files is ArrayList)) {
                Collections.sort(files,
                    SortHelper.getComparator(SortHelper.FILE_NAME_ORDER, lastMode, isDesc)
                )
                val folderFiles = ArrayList<BaseFileBean>()
                val imageFiles = ArrayList<BaseFileBean>()
                val videoFiles = ArrayList<BaseFileBean>()
                val audioFiles = ArrayList<BaseFileBean>()
                val docFiles = ArrayList<BaseFileBean>()
                val appFiles = ArrayList<BaseFileBean>()
                val compressFiles = ArrayList<BaseFileBean>()
                val otherFiles = ArrayList<BaseFileBean>()
                files.forEach {
                    classifyFileByMimeType(
                        it, folderFiles, imageFiles, videoFiles, audioFiles, docFiles,
                        appFiles, compressFiles, otherFiles
                    )
                }
                files.clear()
                val dataList = arrayOf(
                    folderFiles, imageFiles, videoFiles,
                    audioFiles, docFiles, appFiles,
                    compressFiles, otherFiles)
                val size = dataList.size
                for (index in dataList.indices) {
                    val i = SortHelper.getDescIndex(index, size, isDesc)
                    if (dataList[i].size > 0) {
                        files.addAll(dataList[i])
                        dataList[i].clear()
                    }
                }
            } else {
                Collections.sort(files, SortHelper.getComparator(order, lastMode, isDesc))
            }
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, e.message)
        }
    }

    fun sortFiles(files: List<BaseFileBean>, order: Int, lastMode: Int, hideHeadLabel: Boolean = false, isDesc: Boolean) {
        try {
            if ((order == SortHelper.FILE_TYPE_ORDER) && (files is ArrayList)) {
                Collections.sort(files,
                    SortHelper.getComparator(SortHelper.FILE_NAME_ORDER, lastMode, isDesc)
                )
                if (!hideHeadLabel) {
                    val folderFiles = ArrayList<BaseFileBean>()
                    val imageFiles = ArrayList<BaseFileBean>()
                    val videoFiles = ArrayList<BaseFileBean>()
                    val audioFiles = ArrayList<BaseFileBean>()
                    val docFiles = ArrayList<BaseFileBean>()
                    val appFiles = ArrayList<BaseFileBean>()
                    val compressFiles = ArrayList<BaseFileBean>()
                    val otherFiles = ArrayList<BaseFileBean>()
                    files.forEach {
                        classifyFileByMimeType(
                            it, folderFiles, imageFiles, videoFiles, audioFiles, docFiles,
                            appFiles, compressFiles, otherFiles
                        )
                    }
                    files.clear()

                    var fileRelatedHeader: BaseFileBean?
                    val dataList = arrayOf(
                        folderFiles, imageFiles, videoFiles,
                        audioFiles, docFiles, appFiles,
                        compressFiles, otherFiles)
                    val size = dataList.size
                    for (index in dataList.indices) {
                        val i = SortHelper.getDescIndex(index, size, isDesc)
                        if (dataList[i].size > 0) {
                            fileRelatedHeader = BaseFileBean()
                            fileRelatedHeader.fileWrapperLabel = SortHelper.ITEM_IDS[i]
                            fileRelatedHeader.mFileWrapperViewType = BaseFileBean.TYPE_LABEL_FILE
                            fileRelatedHeader.mFileWrapperTypeNum = dataList[i].size
                            files.add(fileRelatedHeader)
                            files.addAll(dataList[i])
                            dataList[i].clear()
                        }
                    }
                }
            } else {
                runBlocking {
                    try {
                        timeoutCoroutine(files, SortHelper.getComparator(order, lastMode, isDesc))
                    } catch (e: CancellationException) {
                        Log.d(TAG, "sort CancellationException ${e.message}")
                    }
                }
            }
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, e.message)
        }
    }

    private suspend fun timeoutCoroutine(files: List<BaseFileBean>, comparator: Comparator<BaseFileBean>?) {
        Log.d(TAG, "timeoutCoroutine sort start")
        withTimeout(LOAD_TIME_OUT) {
            Collections.sort(files, comparator)
            Log.d(TAG, "timeoutCoroutine sort end")
        }
    }

    fun <T : BaseFileBean> classifyOtherFileByMimeType(
        docFiles: ArrayList<T>,
        otherFiles: ArrayList<T>
    ) {
        val tempOthers = ArrayList<T>()
        for (file in otherFiles) {
            if (MimeTypeHelper.isDocType(file.mLocalType) || MimeTypeHelper.isOtherDocType(file.mLocalType)) {
                docFiles.add(file)
            } else {
                tempOthers.add(file)
            }
        }
        otherFiles.clear()
        otherFiles.addAll(tempOthers)
    }
}