/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.superapp
 * * Version     : 1.0
 * * Date        : 2020/4/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.categorycompress.ui

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.categorycompress.R
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.LIST_SELECTED_MODE
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.PCConsumeOnGenericMotionListener
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.BrowserPathBar.Companion.FILE_BROWSER_FOLDER_ANIM_TIME
import com.filemanager.common.wrapper.MediaFileWrapper
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier

class CategoryCompressFragment : RecyclerSelectionVMFragment<CategoryCompressViewModel>(), OnBackPressed,
        NavigationBarView.OnItemSelectedListener {

    companion object {
        private const val TAG = "CategoryCompressFragment"
    }

    private var sortEntryView: SortEntryView? = null
    private var mCompressType = CategoryCompressViewModel.TYPE_ALL
    private var mAdapter: CategoryCompressAdapter? = null
    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    private var mCompressParentViewModel: CompressParentViewModel? = null
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val mSortPopupController by lazy { SortPopupController(lifecycle) }
    private val mSpacesItemDecoration by lazy {
        ItemDecorationFactory.getGridItemDecoration(ItemDecorationFactory.GRID_ITEM_DECORATION_COMPRESS)
    }
    private val mFolderTransformAnimator by lazy { FolderTransformAnimator() }

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }
    internal var mFileOperateController: NormalFileOperateController? = null
        private set
    private var mLoadingController: LoadingController? = null
    private var mTabActivityListener: TabActivityListener<MediaFileWrapper>? = null
    private var mNeedLoadData = false
    private var hasShowEmpty: Boolean = false
    private var tempSortDesc = -1
    private var tempSortType = -1

    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? = null

    override fun getLayoutResId(): Int {
        return R.layout.category_compress_fragment
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            val bundle = arguments ?: return
            mCompressType = bundle.getString(KtConstants.P_TAB_POSITION, CategoryCompressViewModel.TYPE_ALL)
            mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
            tempSortDesc = bundle.getInt(Constants.TEMP_SORT_DESC, -1)
            tempSortType = bundle.getInt(Constants.TEMP_SORT_TYPE, -1)
            mAdapter = CategoryCompressAdapter(it, <EMAIL>).apply {
                setHasStableIds(true)
            }
        }
    }

    override fun initView(view: View) {
        rootView = view.findViewById(R.id.root_view)
        fragmentFastScroller = view.findViewById(R.id.fastScroller)
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        fragmentRecyclerView?.setOnGenericMotionListener(PCConsumeOnGenericMotionListener())
        mGridSpanAnimationHelper = GridSpanAnimationHelper(fragmentRecyclerView!!)
        sortEntryView = (parentFragment as? CompressParentFragment)?.sortEntryView
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_COMPRESS))
        if (tempSortType != -1) {
            sortEntryView?.setSortOrder(tempSortType, tempSortDesc == 0)
        } else {
            sortEntryView?.setDefaultOrder(SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_COMPRESS))
        }
    }

    fun setToolbarNew(toolbarParam: COUIToolbar?) {
        toolbar = toolbarParam
    }

    private fun isCurrentFragment(): Boolean {
        (parentFragment as? CompressParentFragment)?.let {
            if (it.getCurrentTabCompressType() == mCompressType) {
                return true
            }
        }
        return false
    }

    override fun createViewModel(): CategoryCompressViewModel {
        val vm = ViewModelProvider(this)[mCompressType, CategoryCompressViewModel::class.java]
        mFileOperateController = NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_COMPRESS, vm).also {
            it.setResultListener(FileOperatorListenerImpl(vm))
        }
        return vm
    }

    override fun initData(savedInstanceState: Bundle?) {
        fragmentRecyclerView?.let {
            it.addItemDecoration(mSpacesItemDecoration)
            it.isNestedScrollingEnabled = true
            it.clipToPadding = false
            it.layoutManager = FileGridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1)
            it.itemAnimator = mFolderTransformAnimator
            it.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }
            mAdapter?.let { ad ->
                fragmentRecyclerView!!.adapter = ad
            }
            (toolbar as? View)?.post {
                if (isAdded) {
                    val paddingBottom = if (it.paddingBottom == 0) {
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    } else {
                        it.paddingBottom
                    }
                    val appbar = (parentFragment as? CompressParentFragment)?.mAppBarLayout
                    it.setPadding(it.paddingLeft, KtViewUtils.getRecyclerViewTopPadding(appbar, 0), it.paddingRight, paddingBottom)
                }
            }
        }
        if (mNeedLoadData) {
            onResumeLoadData()
        }
        if (actionCheckPermission().not()) {
            sortEntryView?.setFileCount(0)
        }
        TouchShareSupplier.attach(this, mFileOperateController)
    }

    override fun startObserve() {
        mCompressParentViewModel = (parentFragment as? CompressParentFragment)?.mViewModel
        fragmentRecyclerView?.post {
            if (isAdded) {
                startListSelectModeObserver()
                startUIDataStateObserver()
                startScrollToPositionObserver()
                startScanModeObserver()
                startObserveLoadState()
            }
        }
    }

    private fun startScrollToPositionObserver() {
        fragmentViewModel?.mPositionModel?.observe(this) { positionModel ->
            positionModel?.let {
                (fragmentRecyclerView?.layoutManager as? GridLayoutManager)?.scrollToPosition(it)
            }
        }
    }

    private fun startListSelectModeObserver() {
        val viewModule = fragmentViewModel ?: return
        viewModule.mModeState.listModel.observe(this, object : Observer<Int> {
            override fun onChanged(value: Int) {
                if (!viewModule.mModeState.initState || !isCurrentFragment()) {
                    toolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                    return
                }
                Log.d(TAG, "startListSelectModeObserver: mListModel=$value")
                val selectModel = (value == LIST_SELECTED_MODE)
                mAdapter?.setSelectEnabled(selectModel)
                if (selectModel) {
                    (parentFragment as? CompressParentFragment).apply {
                        this?.disableViewPager()
                    }
                    (parentFragment as? CompressParentFragment)?.previewEditedFiles(
                        fragmentViewModel?.getSelectItems()
                    )
                } else {
                    (parentFragment as? CompressParentFragment)?.previewClickedFile(
                        fragmentViewModel?.previewClickedFileLiveData?.value,
                        fragmentViewModel?.previewClickedFileLiveData
                    )
                }
                fragmentRecyclerView?.let {
                    var navigationView: View? = null
                    baseVMActivity?.apply {
                        navigationView = if (baseVMActivity is CategoryCompressActivity) {
                            this.findViewById(R.id.navigation_tool)
                        } else {
                            val mainAction = Injector.injectFactory<IMain>()
                            mainAction?.getNavigationView(this)
                        }
                    }
                    val bottom = if (selectModel) {
                        KtViewUtils.getSelectModelPaddingBottom(it, navigationView)
                    } else {
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    }
                    it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, bottom)
                    fragmentFastScroller?.apply { trackMarginBottom = bottom }

                }
                (baseVMActivity as? NavigationInterface)?.let {
                    if (selectModel) {
                        it.showNavigation()
                        viewModule.setNavigateItemAble(baseVMActivity as NavigationInterface)
                    } else {
                        it.hideNavigation()
                    }
                }
                toolbar?.let {
                    changeActionModeAnim(it, {
                        if (selectModel) {
                            mTabActivityListener?.initToolbarSelectedMode(
                                true,
                                viewModule.getRealFileSize(),
                                viewModule.uiState.value?.selectedList?.size ?: 0,
                                viewModule.getSelectItems())
                        } else {
                            mTabActivityListener?.apply {
                                val empty = (viewModule.uiState.value?.fileList?.isNotEmpty() != true)
                                initToolbarNormalMode(true, empty)
                                refreshScanModeItemIcon()
                            }
                        }
                    } , (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true))
                    it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
                }
            }
        })
    }

    private fun startUIDataStateObserver() {
        fragmentViewModel?.uiState!!.observe(this) { uiModel ->
            Log.d(
                TAG, "startUIDataStateObserver: total=${uiModel.fileList.size},"
                        + "select=${uiModel.selectedList.size}, keyword=${uiModel.keyWord}"
            )
            val fileSize = fragmentViewModel?.getRealFileSize() ?: 0
            sortEntryView?.setFileCount(fileSize)
            val empty = uiModel.fileList.isEmpty()
            if (empty) {
                showEmptyView()
                sortEntryView?.visibility = View.GONE
                fragmentFastScroller?.visibility = View.INVISIBLE
            } else {
                hasShowEmpty = false
                mFileEmptyController.hideFileEmptyView()
                sortEntryView?.visibility = View.VISIBLE
                fragmentFastScroller?.visibility = View.VISIBLE
            }
            val selectModel = (uiModel.stateModel.listModel.value == LIST_SELECTED_MODE)
            if (selectModel) {
                mTabActivityListener?.initToolbarSelectedMode(
                    false, fileSize,
                    uiModel.selectedList.size,
                    fragmentViewModel!!.getSelectItems()
                )
                if (uiModel.fileList is ArrayList<MediaFileWrapper>) {
                    mAdapter?.setData(uiModel.fileList as ArrayList<MediaFileWrapper>,
                        uiModel.selectedList
                    )
                    (parentFragment as? CompressParentFragment)?.previewEditedFiles(
                        fragmentViewModel?.getSelectItems()
                    )
                }
            } else {
                (parentFragment as? CompressParentFragment)?.previewClickedFile(
                    fragmentViewModel?.previewClickedFileLiveData?.value,
                    fragmentViewModel?.previewClickedFileLiveData
                )
                mTabActivityListener?.apply {
                    initToolbarNormalMode(false, empty)
                    refreshScanModeItemIcon()
                }
                if (uiModel.fileList is ArrayList<MediaFileWrapper>) {
                    mAdapter?.setData(uiModel.fileList as ArrayList<MediaFileWrapper>,
                        uiModel.selectedList
                    )
                }
            }
        }
        fragmentViewModel?.previewClickedFileLiveData?.observe(this) {
            mAdapter?.setPreviewClickedFile(it)
        }
    }

    private fun startScanModeObserver() {
        mCompressParentViewModel?.mBrowseModeState?.observe(this) { scanMode ->
            updateScanModeView(scanMode)
        }
    }

    private fun updateScanModeView(scanMode: Int) {
        fragmentViewModel?.mScanModeValue = scanMode
        toolbar?.let {
            val needSkipAnimation = mNeedSkipAnimation
            if (needSkipAnimation) {
                refreshScanModeAdapter(scanMode)
            } else {
                fragmentRecyclerView?.let { recyclerView ->
                    recyclerView.mTouchable = false
                    recyclerView.stopScroll()
                }
                mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                    override fun onSpanChangeCallback() {
                        (fragmentRecyclerView?.layoutManager as? GridLayoutManager)?.scrollToPosition(0)
                        refreshScanModeAdapter(scanMode)
                    }
                }, object : OnAnimatorEndListener {
                    override fun onAnimatorEnd() {
                        fragmentRecyclerView?.mTouchable = true
                    }
                })

            }
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            mLoadingController = LoadingController(it, this).apply {
                observe(fragmentViewModel?.dataLoadState, rootView) {
                    val isNotEmpty = (fragmentViewModel?.getRealFileSize() ?: 0) > 0
                    Log.d(TAG, "isNotEmpty $isNotEmpty")
                    if (isNotEmpty) {
                        hasShowEmpty = false
                        mFileEmptyController.hideFileEmptyView()
                    }
                    isNotEmpty
                }
                //这里将LoadingController和FolderTransformAnimator通过接口方式关联起来
                mFolderTransformAnimator.registerNeedSkipAnimator(this)
            }
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            val scanMode = mCompressParentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            if (scanMode == KtConstants.SCAN_MODE_GRID) {
                refreshScanModeAdapter(scanMode)
            }
            baseVMActivity?.let {
                mFileEmptyController.changeEmptyFileIcon()
            }
            mSortPopupController.hideSortPopUp()
            mFileOperateController?.onConfigurationChanged(context?.resources?.configuration)
            if (fragmentViewModel?.mModeState?.listModel?.value == LIST_SELECTED_MODE) {
                fragmentViewModel?.changeListMode(LIST_SELECTED_MODE)
            }
            updateLeftRightMargin()
        }
    }

    fun updateLeftRightMargin() {
        val scanMode = mCompressParentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            mAdapter?.notifyDataSetChanged()
        }
        sortEntryView?.updateLeftRightMargin()
    }

    fun isEmptyList(): Boolean {
        return hasShowEmpty
    }

    fun getFileList(): List<MediaFileWrapper>? {
        return fragmentViewModel?.uiState?.value?.fileList
    }

    fun getSelectedFileList(): List<MediaFileWrapper>? {
        return fragmentViewModel?.getSelectItems()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getGridItemCount(activity, scanMode,
                                                               ItemDecorationFactory.GRID_ITEM_DECORATION_COMPRESS
        )
        (fragmentRecyclerView?.layoutManager as? GridLayoutManager)?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mAdapter?.apply {
            mScanViewModel = scanMode
            mFolderTransformAnimator.mSkipAddRemoveAnimation = true
            notifyDataSetChanged()
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        if (fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true) {
            showEmptyView()
        }
        if (mCompressParentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_GRID) {
            refreshScanModeAdapter(KtConstants.SCAN_MODE_GRID)
        }
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    private fun showEmptyView() {
        if (hasShowEmpty) return
        if ((baseVMActivity != null) && (rootView != null)) {
            mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!)
            hasShowEmpty = true
            (parentFragment as? CompressParentFragment)?.listEmptyFile()
        }
        Log.d(TAG, "showEmptyView")
    }

    override fun onResumeLoadData() {
        if (!isAdded) {
            return
        }
        if (checkShowPermissionEmpty()) {
            sortEntryView?.setFileCount(0)
            return
        }
        fragmentViewModel?.initLoader(LoaderViewModel.getLoaderController(baseVMActivity ?: this), mCompressType)
        mViewModel?.setSort(tempSortType, tempSortDesc)
    }

    override fun pressBack(): Boolean {
        return fragmentViewModel?.pressBack() ?: false
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return activity?.let {
            mFileOperateController?.onNavigationItemSelected(it, item)
        } ?: false
    }

    fun onMenuItemSelected(item: MenuItem?): Boolean {
        if ((null == item) || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            android.R.id.home -> {
                baseVMActivity?.onBackPressed()
                true
            }
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(activity, CategoryHelper.CATEGORY_COMPRESS)
                OptimizeStatisticsUtil.pageSearch(OptimizeStatisticsUtil.COMPRESS)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, Constants.PAGE_COMPRESS)
                true
            }
            R.id.actionbar_edit -> {
                fragmentViewModel?.changeListMode(LIST_SELECTED_MODE)
                OptimizeStatisticsUtil.pageEdit(OptimizeStatisticsUtil.COMPRESS)
                true
            }
            R.id.navigation_sort -> {
                baseVMActivity?.let {
                    StatisticsUtils.nearMeStatistics(it, StatisticsUtils.SEQUENCE_ACTION)
                    OptimizeStatisticsUtil.pageSort(OptimizeStatisticsUtil.COMPRESS)
                    val bundle = Bundle()
                    bundle.putInt(Constants.TEMP_SORT_TYPE, tempSortType)
                    bundle.putInt(Constants.TEMP_SORT_DESC, tempSortDesc)
                    bundle.putString(SortModeUtils.RECORD_CATEGORY_MODE, SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_COMPRESS))
                    val anchorView: View? = parentFragment?.view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
                    mSortPopupController.showSortPopUp(
                        it,
                        anchorView,
                        bundle,
                        object : SelectItemListener {

                            override fun onDismiss() {
                                sortEntryView?.rotateArrow()
                            }

                            override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                                if (flag) {
                                    sortEntryView?.setSortOrder(sortMode, isDesc)
                                    mViewModel?.setSort(-1, -1)
                                    fragmentViewModel?.loadData()
                                }
                            }
                        })
                }
                true
            }
            R.id.actionbar_scan_mode -> {
                mTabActivityListener?.updateNeedSkipAnimation(true)
                mCompressParentViewModel?.clickScanModeItem(baseVMActivity)
                true
            }
            R.id.action_setting -> {
                Injector.injectFactory<ISetting>()?.startSettingActivity(activity)
                OptimizeStatisticsUtil.pageSetting(OptimizeStatisticsUtil.COMPRESS)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, Constants.PAGE_COMPRESS)
                true
            }
            R.id.action_select_all -> {
                fragmentViewModel?.clickToolbarSelectAll()
                true
            }
            com.filemanager.common.R.id.action_select_cancel -> {
                if (fragmentViewModel?.mModeState?.listModel?.value == LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                }
                true
            }
            else -> {
                false
            }
        }
    }

    fun setTabActivityListener(tabListener: TabActivityListener<MediaFileWrapper>) {
        mTabActivityListener = tabListener
    }

    fun fromSelectPathResult(requestCode: Int, path: List<String>?) {
        activity?.let { mFileOperateController?.onSelectPathReturn(it, requestCode, path) }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onItemClick(item: com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if (uiModel.stateModel.listModel.value != KtConstants.LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.keyMap[item.selectionKey].apply {
                Log.d(TAG, "onItemClick baseFile=$this")
            } ?: return true
            activity?.let {
                val previewResult = (parentFragment as? CompressParentFragment)?.previewClickedFile(
                    baseFile, fragmentViewModel?.previewClickedFileLiveData
                )
                if (previewResult != true) {
                    mFileOperateController?.onFileClick(it, baseFile, e)
                }
            }
        }
        return true
    }

    /**
     * 判断当前是否处于选中编辑模式
     */
    fun isInSelectMode(): Boolean {
        return fragmentViewModel?.isInSelectMode() == true
    }

    fun exitSelectionMode() {
        if (isInSelectMode()) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        if (mCompressParentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val slideWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            slideWidth,
            ItemDecorationFactory.GRID_ITEM_DECORATION_COMPRESS
        )
        return true
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            fragmentRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController = SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }

    override fun bringToFront(visible: Int) {
        //显示的时候在appbarlayout初始化后在执行 因为高度依赖appbarlayout
        if (View.GONE == visible) {
            super.bringToFront(visible)
        }
    }

    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        super.updatePermissionEmptyViewPaddingForChild()
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_COMPRESS
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy")
        super.onDestroy()
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
    }

    override fun onDestroyView() {
        Log.i(TAG, "onDestroyView")
        //这里调用反注册关系，将loader和FolderTransformAnimator两者解除关系
        mFolderTransformAnimator.unRegisterNeddSkipAnimator()
        super.onDestroyView()
    }
}