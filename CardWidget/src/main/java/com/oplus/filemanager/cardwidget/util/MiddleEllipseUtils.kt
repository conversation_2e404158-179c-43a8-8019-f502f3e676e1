/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.cardwidget.util
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/1/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.util

import android.graphics.text.LineBreaker
import android.text.Layout
import android.text.StaticLayout
import android.text.TextDirectionHeuristics
import android.text.TextPaint
import kotlin.math.min

class MiddleEllipseUtils {
    companion object {
        private const val TEXT_SIZE = 10f
        private const val WIDTH = 56
        private const val MAX_LINE = 2
        private const val SYMBOL = "\u2026" // HORIZONTAL ELLIPSIS (…)
        private const val SPACING_ADD = -2.0f
    }

    fun getEllipseText(text: String): String {
        val staticLayout = getStaticLayout(text)
        if (staticLayout.lineCount <= MAX_LINE) {
            return text
        }
        val firstEnd = staticLayout.getLineEnd(0)
        val substringStart = text.substring(0, firstEnd) // The text before the ellipsis
        val endIndex = staticLayout.lineCount - 1

        var substringEndStart = staticLayout.getLineStart(endIndex)
        val extPointStart = text.indexOfLast { it == '.' }
        if (extPointStart > firstEnd) {
            substringEndStart = min(substringEndStart, extPointStart)
        }
        var substringEnd = text.substring(substringEndStart)
        // Crop the text after the ellipsis until the whole can be displayed within the range of the number of lines
        while (getStaticLayout(createEllipsizedText(substringStart, substringEnd)).lineCount > MAX_LINE) {
            val firstSpace = substringEnd.indexOf(' ')
            substringEnd = if ((firstSpace == -1) || (firstSpace >= substringEnd.length - 1)) {
                substringEnd.substring(1)
            } else {
                substringEnd.substring(firstSpace + 1)
            }
        }
        return createEllipsizedText(substringStart, substringEnd)
    }

    /**
     * 创建包含省略号的文本
     * 带上换行，第一行使用第一次获取到的行文本
     * 因为若不带上换行，会出现以下情况：
     * abc acdaaaaaaaaaaaa.mp3 此文件名，系统默认会分成3行，即：
     * abc （当第一段单词较短）
     * acdaaaaaaaaaaaa
     * .mp3
     * 取第一、三两行，中间加三点，得到字符串abc....mp3 最终变成一行显示
     */
    private fun createEllipsizedText(substringStart: String, substringEnd: String) = "$substringStart\n${SYMBOL}$substringEnd"

    private fun getStaticLayout(text: CharSequence): StaticLayout {
        val paint  = TextPaint()
        paint.textSize = TEXT_SIZE
        return StaticLayout.Builder.obtain(text, 0, text.length, paint, WIDTH)
            .setAlignment(Layout.Alignment.ALIGN_NORMAL)
            .setTextDirection(TextDirectionHeuristics.FIRSTSTRONG_LTR)
            .setLineSpacing(SPACING_ADD, 0f)
            .setIncludePad(true)
            .setBreakStrategy(LineBreaker.BREAK_STRATEGY_BALANCED)
            .setHyphenationFrequency(Layout.HYPHENATION_FREQUENCY_NORMAL)
            .setMaxLines(Integer.MAX_VALUE)
            .setJustificationMode(LineBreaker.JUSTIFICATION_MODE_NONE).build()
    }
}