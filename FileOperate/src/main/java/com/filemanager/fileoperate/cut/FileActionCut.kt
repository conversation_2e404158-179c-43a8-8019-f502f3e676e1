/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileActionCut.kt
 ** Description: cut files function
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.cut

import android.app.Activity
import android.content.DialogInterface
import android.media.MediaScannerConnection
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.fileutils.checkDestStorageSpace
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NewFunctionSwitch
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.fileoperate.NotifyMediaScannerBatchAction
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_DONE_FOR_EXTERNAL_PATH
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.base.BaseFileActionObserver.Companion.EXTERNAL_STORAGE_COPY_PROGRESS
import com.filemanager.fileoperate.base.BaseFileActionObserver.Companion.MAX_PROGRESS
import com.filemanager.fileoperate.base.SHOW_PROGRESS
import com.filemanager.fileoperate.base.SHOW_TOAST
import com.filemanager.fileoperate.base.UPDATE_PROGRESS
import com.filemanager.fileoperate.copy.ERROR_DFS_STORAGE_CUT_NOT_ENOUGH
import com.filemanager.fileoperate.copy.ERROR_PATH_NULL
import com.filemanager.fileoperate.copy.ERROR_STORAGE_NOT_ENOUGH
import com.filemanager.fileoperate.copy.FileActionBaseCopyCut
import com.filemanager.fileoperate.copy.FileCopyHelper
import com.filemanager.fileoperate.cut.FileCutHelper.isDirectoriesSameDisk
import com.filemanager.fileoperate.cut.FileCutObserver.CutConfirmBean.Companion.TYPE_CUT_BETWEEN_DFM_AND_DEVICE
import com.filemanager.fileoperate.cut.FileCutObserver.CutConfirmBean.Companion.TYPE_CUT_IMAGE_IN_STORAGE
import com.oplus.filemanager.dfm.DFMManager
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.fileservice.IFileService.Companion.OPERATE_TYPE_CUT
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.tool.trackinglib.MediaType
import com.oplus.tool.trackinglib.OpTracker
import com.oplus.tool.trackinglib.OpTrackerMaker
import com.oplus.tool.trackinglib.OpType
import java.io.File
import java.lang.ref.WeakReference

class FileActionCut(
    lifecycle: LifecycleOwner,
    sourceFiles: List<BaseFileBean>,
    destFile: BaseFileBean,
    private val categoryType: Int
) : FileActionBaseCopyCut(lifecycle, sourceFiles, destFile) {
    override var TAG = "FileActionCut"

    companion object {
        const val WAIT_MEDIA_SCAN_MAX_PROGRESS = 90
    }

    //用于判断是否是拖拽造成的剪切
    var dragToCut = false
    var isMacDragToCut: Boolean = false
    // File total info
    private var mHasImageInInternalStorage = false
    private var mTotalFileLength: Long = 0

    // In same volume just need to renameTo, else should copy+delete
    private var mIsSameVolume = false
    private var mSuccessDirListInSameVolume: MutableList<String>? = null
    private var mCurrentProgress = 0
    private var mIsOperateDatabase: Boolean = true

    private val mSyncFilePaths = hashSetOf<String?>()

    // Result Count
    private var mHasSkipFile = false
    private var mHasSuccessFile = false

    private var mLifecycleOwner: WeakReference<LifecycleOwner>? = WeakReference(lifecycle)
    private val allFilePath = ArrayList<String>()
    private val allDirectoryPath = ArrayList<String>()

    private val mMediaScannerBatchAction by lazy {
        NotifyMediaScannerBatchAction(Utils.MEDIA_SCAN_MOVE)
    }
    private var scanPathCount = 0
    private var dealPathCount = 0
    private var isDealDone = false
    private var waitMediaScanEnd = false

    private val mediaScannerBatchActionDFS by lazy {
        NotifyMediaScannerBatchAction(Utils.MEDIA_SCAN_MOVE)
    }

    private val mDealFileListener: FileCopyHelper.OnDealFileListener by lazy {
        object : FileCopyHelper.OnDealFileListener() {
            override var isCancel: () -> Boolean = { isCancelled() }

            override fun onProgressChanged(progress: Long) {
                onDealFileProgressChanged(progress)
            }

            override fun onSuccess(sourceFile: File, destFile: File, isDirectory: Boolean) {
                onDealFileSuccess(sourceFile, destFile, isDirectory)
            }

            // return true means skip this failed file, false means cancel copy file
            override fun onError(sourceFile: File, destFile: File): Boolean {
                Log.d(TAG, "onError cancel ${isCancelled()}")
                return if (isCancelled()) false else onDealFileError(sourceFile, destFile)
            }
        }
    }

    init {
        setScanPathCallback()
    }

    private fun setScanPathCallback() {
        Log.d(TAG, "setScanPathCallback operateFiles ${mOperateFiles.size} categoryType $categoryType")
        if (checkWaitMediaScanEnd(categoryType) && SdkUtils.isAtLeastR()) {
            waitMediaScanEnd = true
            mMediaScannerBatchAction.completeCallback = MediaScannerConnection.OnScanCompletedListener { path, uri ->
                scanPathCount++
                Log.d(TAG, "setScanPathCallback onScanCompleted isDealDone:$isDealDone count:$scanPathCount")
                if (isDealDone) {
                    val leftProgress = MAX_PROGRESS - WAIT_MEDIA_SCAN_MAX_PROGRESS
                    val progress = leftProgress * (scanPathCount / (dealPathCount).toFloat()) + WAIT_MEDIA_SCAN_MAX_PROGRESS
                    notifyObserver(UPDATE_PROGRESS, progress.toInt())
                    if (scanPathCount == dealPathCount) {
                        Log.d(TAG, "setScanPathCallback -> scan complete")
                        releaseLock()
                    }
                }
            }
        }
    }

    private fun checkNeedConfirmImageInStorage(): Boolean {
        val internalPath = VolumeEnvironment.getInternalSdPath(mContext)
        if (!FeatureCompat.sIsExpRom && (mDestFile.mData?.startsWith(internalPath) != true)) {
            return mHasImageInInternalStorage
        }
        return false
    }

    private fun checkNeedConfirmTransBetweenDevices(): Boolean {
        return NewFunctionSwitch.isSupportDfmSearch && FileCutHelper.checkTransBetweenDevices(mOperateFiles, mDestFile.mData ?: "") && !isMacDragToCut
    }

    override fun exceptionDetection(): Any? {
        mIsSameVolume = mDestFile.mData?.let { isDirectoriesSameDisk(mOperateFiles, it) } ?: true
        var result = super.exceptionDetection()
        if (!((result == null) && !mIsSameVolume)) {
            return result
        }
        // calculate file total size
        for (file in mOperateFiles) {
            val fileData = file.mData ?: continue
            FileCutHelper.checkSizeAndInternalStorageImage(File(fileData)).let {
                mTotalFileLength += it.first
                mHasImageInInternalStorage = mHasImageInInternalStorage or it.second
            }
        }
        Log.d(TAG, "exceptionDetection: totalLength:$mTotalFileLength, hasImage: $mHasImageInInternalStorage")
        if (mTotalFileLength < 0) {
            return ERROR_PATH_NULL
        }
        // Check the empty size of the dest storage
        val storageState = checkDestStorageSpace(mDestFile, mTotalFileLength)
        if (storageState.first) {
            Log.d(TAG, "exceptionDetection: storage is not enough")
            result = if (!KtUtils.checkIsDfmPath(mDestFile.mData)) {
                Pair(ERROR_STORAGE_NOT_ENOUGH, storageState.second)
            } else {
                val deviceName = DFMManager.getDFSDeviceName() ?: KtConstants.DFM_MOUNT_PATH_SUFFIX
                Pair(
                    ERROR_DFS_STORAGE_CUT_NOT_ENOUGH,
                    mContext.getString(
                        com.filemanager.common.R.string.move_failed_by_device_not_enough,
                        deviceName
                    )
                )
            }
        }
        return result
    }

    override fun workRun(): Boolean {
        storeFilesForBuriedPoint()
        mIsOperateDatabase = Utils.isOperateDatabase(mContext, mDestFile.mData)

        var result = true
        if (checkNeedConfirmImageInStorage()) {
            result = false
            val confirmBean = FileCutObserver.CutConfirmBean(
                TYPE_CUT_IMAGE_IN_STORAGE,
                mOperateFiles.size,
                mDestFile.mData
            )
            confirmBean.negativeListener = DialogInterface.OnClickListener { _, _ ->
                Log.d(TAG, "workRun: negative click")
                releaseLock()
            }
            confirmBean.positiveListener = DialogInterface.OnClickListener { _, _ ->
                Log.d(TAG, "workRun: positive click")
                result = true
                releaseLock()
            }
            notifyObserver(SHOW_MOVE_CONFIRM_DIALOG, confirmBean)
            waitLock()
        }
        if (checkNeedConfirmTransBetweenDevices()) {
            result = false
            val confirmBean = FileCutObserver.CutConfirmBean(
                TYPE_CUT_BETWEEN_DFM_AND_DEVICE,
                mOperateFiles.size,
                mDestFile.mData
            )
            confirmBean.negativeListener = DialogInterface.OnClickListener { _, _ ->
                Log.d(TAG, "workRun: negative click")
                releaseLock()
            }
            confirmBean.positiveListener = DialogInterface.OnClickListener { _, _ ->
                Log.d(TAG, "workRun: positive click")
                result = true
                releaseLock()
            }
            notifyObserver(SHOW_MOVE_CONFIRM_DIALOG, confirmBean)
            waitLock()
        }
        return if (result) {
            if (mIsSameVolume) {
                mSuccessDirListInSameVolume = arrayListOf()
            }
            super.workRun()
        } else false
    }

    override fun onDealFileResume() {
        notifyObserver(
            SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                mContext.getString(com.filemanager.common.R.string.cut_file_dialog_title), false, mCurrentProgress
        ))
    }

    private fun onDealFileSuccess(sourceFile: File, destFile: File, isDirectory: Boolean) {
        mHasSuccessFile = true
        val targetExtension = FileTypeUtils.getExtension(destFile.absolutePath)
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.RELOCATE_FILE,
            mapOf(
                StatisticsUtils.SOURCE_FILE_PATH to sourceFile.absolutePath,
                StatisticsUtils.TARGET_FILE_PATH to destFile.absolutePath,
                StatisticsUtils.TARGET_FILE_TYPE to
                        if (isDirectory) StatisticsUtils.DIRECTORY_FILE_TYPE else targetExtension
            )
        )
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.onUpdateFilePath(sourceFile.absolutePath, destFile.absolutePath)
        if (mIsSameVolume) {
            if (destFile.isDirectory) { // Don't use sourceFile, because sourceFile not exist now
                mSuccessDirListInSameVolume?.add(sourceFile.absolutePath.let {
                    if (it.endsWith(File.separator)) it else it.plus(File.separator)
                })
            }
        } else {
            // Don't use cycle delete, because some files maybe skipped
            val result = sourceFile.delete()
            Log.d(TAG, "onDealFileSuccess: delete source file[$result]: ${sourceFile.absolutePath}")
        }
    }

    private fun onDealFileProgressChanged(progress: Long) {
        val maxProgress = if (waitMediaScanEnd) WAIT_MEDIA_SCAN_MAX_PROGRESS else MAX_PROGRESS
        if (mIsSameVolume) {
            mCurrentProgress += progress.toInt()
            notifyObserver(UPDATE_PROGRESS, (mCurrentProgress * maxProgress / mOperateFiles.size))
        } else {
            mCurrentProgress = if (mTotalFileLength <= 0) {
                maxProgress
            } else {
                (progress * maxProgress / mTotalFileLength).toInt()
            }
            if (isOtgOrSDCard) {
                mCurrentProgress =
                    (mCurrentProgress.toFloat() * EXTERNAL_STORAGE_COPY_PROGRESS / MAX_PROGRESS).toInt()
            }
            notifyObserver(UPDATE_PROGRESS, mCurrentProgress)
        }
    }

    override fun onSkipDealFile(sourceFile: File) {
        mHasSkipFile = true
        if (mIsSameVolume) {
            onDealFileProgressChanged(1)
        } else {
            JavaFileHelper.fileTotalSize(sourceFile).let {
                mDealFileListener.addProgress(it)
            }
        }
    }

    override fun onDealFile(sourceFile: File, destFile: File, isDirectory: Boolean) {
        if (isOtgOrSDCard && hasShowKeepConnectToast.not()) {
            hasShowKeepConnectToast = true
            notifyObserver(
                SHOW_TOAST,
                appContext.resources.getString(com.filemanager.common.R.string.toast_cutting_message)
            )
        }
        var shouldScan = mIsOperateDatabase
        if (mIsSameVolume) {
            val result = FileCutHelper.cutFile(sourceFile, destFile) { path: String ->
                checkFileParentIsSuccess(path)
            }
            if (result) {
                onDealFileSuccess(sourceFile, destFile, isDirectory)
                mSyncFilePaths.add(sourceFile.parent)
                onDealFileProgressChanged(1)
            } else {
                onDealFileError(sourceFile, destFile)
            }
            if (shouldScan) {
                shouldScan = destFile.exists()
            }
        } else {
            FileCopyHelper.copyFile(sourceFile, destFile, isDirectory, mDealFileListener)
        }
        if (shouldScan) {
            addScannerPath(destFile.absolutePath)
            addScannerPath(sourceFile.absolutePath)
        }
    }

    private fun addScannerPath(absolutePath: String) {
        if (absolutePath.startsWith(KtConstants.DFM_MOUNT_PATH_SUFFIX)) {
            Log.d(TAG, "addScannerPath dfs")
            mediaScannerBatchActionDFS.add(absolutePath)
        } else {
            mMediaScannerBatchAction.add(absolutePath)
        }
    }

    override fun onDealAllFilesEnd() {
        Log.d(TAG, "onDealAllFilesEnd mHasSuccessFile $mHasSuccessFile waitMediaScanEnd $waitMediaScanEnd mIsOperateDatabase" +
                " $mIsOperateDatabase isOtgOrSDCard $isOtgOrSDCard")
        if (mIsOperateDatabase) {
            dealPathCount = mMediaScannerBatchAction.getAllScanPathSize()
            mMediaScannerBatchAction.flush()
            mediaScannerBatchActionDFS.flush()
        }
        if (mHasSuccessFile) {
            buriedPointForMedia()
            val fileServiceAction = Injector.injectFactory<IFileService>()
            fileServiceAction?.let {
                mSyncFilePaths.add(mDestFile.mData)
                it.syncOperate(OPERATE_TYPE_CUT, mSyncFilePaths)
            }
            if (isOtgOrSDCard) {
                waitMediaScanEnd = false
                notifyObserver(ACTION_DONE_FOR_EXTERNAL_PATH, Pair(mDestFile.mData, dragToCut))
            } else if (!mIsOperateDatabase) {
                notifyObserver(ACTION_DONE, Pair(mDestFile.mData, dragToCut))
            } else if (!waitMediaScanEnd) {
                notifyObserver(ACTION_DONE, Pair(mDestFile.mData, dragToCut))
            }
        } else {
            notifyObserver(ACTION_FAILED, ERROR_CUT)
        }
        mSuccessDirListInSameVolume?.clear()
        StatisticsUtils.onCommon(mContext, StatisticsUtils.CUT_MENU_PRESSED)

        waitMediaScanComplete()
    }

    private fun waitMediaScanComplete() {
        Log.d(TAG, "waitMediaScanComplete waitMediaScanEnd $waitMediaScanEnd mHasSuccessFile $mHasSuccessFile mIsOperateDatabase $mIsOperateDatabase")
        if (waitMediaScanEnd && mHasSuccessFile && mIsOperateDatabase) {
            isDealDone = true
            Log.d(TAG, "waitMediaScanComplete pathCount: deal:$dealPathCount scanned:$scanPathCount")
            if (dealPathCount != scanPathCount) {
                waitLock()
            }
            notifyObserver(ACTION_DONE, Pair(mDestFile.mData, dragToCut))
        }
    }

    private fun storeFilesForBuriedPoint() {
        allFilePath.clear()
        allDirectoryPath.clear()
        mOperateFiles.forEach {
            if (it.mDisplayName!!.startsWith(".").not()) {
                it.mData?.let { data ->
                    val file = File(data)
                    if (file.isDirectory) {
                        allDirectoryPath.add(data)
                    } else {
                        allFilePath.add(data)
                    }
                }
            }
        }
    }

    private fun checkWaitMediaScanEnd(categoryType: Int): Boolean {
        val set = HashSet<Int>()
        set.add(CategoryHelper.CATEGORY_RECENT)
        set.add(CategoryHelper.CATEGORY_VIDEO)
        set.add(CategoryHelper.CATEGORY_AUDIO)
        set.add(CategoryHelper.CATEGORY_DOC)
        set.add(CategoryHelper.CATEGORY_APK)
        set.add(CategoryHelper.CATEGORY_COMPRESS)
        set.add(CategoryHelper.CATEGORY_IMAGE)
        return set.contains(categoryType)
    }

    private fun buriedPointForMedia() {
        val opTracker = OpTracker(appContext)
        val life: LifecycleOwner? = mLifecycleOwner?.get()
        var path = "default"
        if ((life != null) && (life is Activity)) {
            path = life.javaClass.name
        }
        Log.d(TAG, "buriedPointForMedia path = $path")
        val pointList: List<Map<String, String>> = opTracker.convert(
            OpTrackerMaker.Builder().apply {
                setOp(OpType.MOVE_TO)
                setOpTime(System.currentTimeMillis().toString())
                setOpPath(path)
                setTgtPath(mDestFile.mData!!)
                setFilePaths(allFilePath)
            }.build()
        )
        pointList.forEach {
            StatisticsUtils.onCommon(appContext, StatisticsUtils.FILE_OPERATION, it)
        }

        allDirectoryPath.forEach {
            val list: List<Map<String, String>> = opTracker.convert(
                OpTrackerMaker.Builder().apply {
                    setOp(OpType.MOVE_TO)
                    setOpTime(System.currentTimeMillis().toString())
                    setOpPath(path)
                    setTgtPath(mDestFile.mData!!)
                    setDirPath(it)
                    setMediaType(MediaType.MEDIA_TYPE_IMAGE)
                }.build()
            )
            list.forEach {
                StatisticsUtils.onCommon(appContext, StatisticsUtils.FILE_OPERATION, it)
            }
        }
    }

    /**
     * In search mode, a file and its parent may both be searched. And if its parent cut first,
     * the file also cut at the same time, in this case, when we cut the file all alone, it should be
     * see as cut successfully.
     * This method is used to check is the parent of the file cut successfully or not.
     */
    private fun checkFileParentIsSuccess(path: String): Boolean {
        mSuccessDirListInSameVolume?.forEach {
            if (path.startsWith(it)) {
                return true
            }
        }
        return false
    }
}