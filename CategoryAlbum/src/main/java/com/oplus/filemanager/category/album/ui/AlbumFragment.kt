/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.album
 * * Version     : 1.0
 * * Date        : 2020/6/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.album.ui

import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Context
import android.content.res.Configuration
import android.os.Bundle
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ImageView
import androidx.annotation.DrawableRes
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.KEY_IMAGE_COVER_PATH
import com.filemanager.common.constants.KtConstants.KEY_IMAGE_RELATIVE_PATH
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.AlbumSortPopupController
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.controller.navigation.NavigationInterfaceForMain
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_DECORATION_ALBUM
import com.filemanager.common.dragselection.DefaultDragListener
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.FileDragDropScanner
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewOperate
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnRefreshDataCallback
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.SortAnimationHelper
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.ImageFileWrapper
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.dropdrag.OnItemClickListener
import com.oplus.dropdrag.SelectionTracker
import com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails
import com.oplus.filemanager.category.album.R
import com.oplus.filemanager.category.album.adapter.AlbumAdapter
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

open class AlbumFragment : RecyclerSelectionVMFragment<AlbumFragmentViewModel>(), OnBackPressed,
    NavigationBarView.OnItemSelectedListener, OnItemClickListener<Int>, IPreviewListFragment {

    companion object {
        const val TAG = "AlbumFragment"
    }
    internal var mToolbar: COUIToolbar? = null
    internal var mTitle: String? = null
    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    internal var sortAnimationHelper: SortAnimationHelper? = null
    internal var mAdapter: AlbumAdapter? = null
    internal var mLayoutManager: FileGridLayoutManager? = null
    private var mContentValues: ContentValues = ContentValues(2)
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val mSortPopupController by lazy { AlbumSortPopupController(lifecycle) }
    internal val mSpacesItemDecoration by lazy {
        ItemDecorationFactory.getGridItemDecoration(GRID_ITEM_DECORATION_ALBUM)
    }
    internal var mNeedLoadData = false
    private var isChildDisplay = false
    private var hasShowEmpty: Boolean = false

    private var scrollHelper: DragScrollHelper? = null

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }
    internal var mFileOperateController: NormalFileOperateController? = null
    private var mLoadingController: LoadingController? = null
    internal var previewOperate: IPreviewOperate? = null

    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? = null

    internal var toolbarOverflowPopupWindow: COUIPopupListWindow? = null

    fun setTitle(title: String?) {
        mTitle = title
        arguments?.putString(Constants.TITLE, mTitle)
    }

    override fun getLayoutResId(): Int {
        return R.layout.album_fragment
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            initArgument()
            mAdapter = getAdapter(it)
            mAdapter!!.setHasStableIds(true)
        }
    }

    open fun getAdapter(activity: FragmentActivity): AlbumAdapter {
        return AlbumAdapter(activity, <EMAIL>)
    }

    private fun initArgument() {
        val bundle = arguments ?: return
        val path: String? = bundle.getString(KEY_IMAGE_RELATIVE_PATH)
        val coverPath: String? = bundle.getString(KEY_IMAGE_COVER_PATH)
        mContentValues.put(KEY_IMAGE_RELATIVE_PATH, path)
        mContentValues.put(KEY_IMAGE_COVER_PATH, coverPath)
        mTitle = bundle.getString(Constants.TITLE)
        mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
        isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
    }

    override fun initView(view: View) {
        rootView = view.findViewById(R.id.coordinator_layout)
        fragmentFastScroller = view.findViewById(R.id.fastScroller)
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        appBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        if (previewOperate?.isSupportPreview() != true) {
            mToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        }
        toolbar = mToolbar
        mGridSpanAnimationHelper = GridSpanAnimationHelper(fragmentRecyclerView!!)
        sortAnimationHelper = SortAnimationHelper(getSortKey())
        scrollHelper = DragScrollHelper(fragmentRecyclerView)
        initToolbar()
    }

    override fun initData(savedInstanceState: Bundle?) {
        fragmentRecyclerView?.let { recyclerView ->
            recyclerView.addItemDecoration(mSpacesItemDecoration)
            mLayoutManager = FileGridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        val viewType = mAdapter?.getItemViewType(position)
                        return if (viewType == BaseFileBean.TYPE_FILE_LIST_FOOTER) spanCount else 1
                    }
                }
            }
            recyclerView.isNestedScrollingEnabled = true
            recyclerView.clipToPadding = false
            recyclerView.layoutManager = mLayoutManager!!
            recyclerView.itemAnimator = sortAnimationHelper?.sortAnimator
            mAdapter?.let {
                recyclerView.adapter = it
            }
            mToolbar?.post {
                if (isAdded) {
                    val paddingBottom = if (recyclerView.paddingBottom == 0) {
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    } else {
                        recyclerView.paddingBottom
                    }
                    recyclerView.setPadding(
                        recyclerView.paddingLeft,
                        KtViewUtils.getRecyclerViewTopPadding(appBarLayout, 0),
                        recyclerView.paddingRight,
                        paddingBottom
                    )
                    fragmentViewModel?.mBrowseModeState?.value = fragmentViewModel?.mBrowseModeState?.value
                }
            }
        }
        if (mNeedLoadData) {
            onResumeLoadData()
        }
        TouchShareSupplier.attach(this, mFileOperateController)
    }

    private fun initToolbar() {
        mToolbar?.apply {
            title = mTitle
            titleMarginStart = 0
            isTitleCenterStyle = false
            inflateMenu(getMenuLayoutResId())
            updateToolbarHeight(this)
        }
        if (previewOperate?.isSupportPreview() != true) {
            rootView?.apply {
                setPadding(paddingLeft,
                    COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom)
            }
        }
        baseVMActivity?.apply {
            setSupportActionBar(mToolbar)
            supportActionBar?.let {
                it.setDisplayHomeAsUpEnabled(true)
                it.setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
    }

    override fun startObserve() {
        if (baseVMActivity == null) return
        fragmentRecyclerView?.post {
            val viewModule = fragmentViewModel ?: return@post
            if (!isAdded) {
                return@post
            }
            viewModule.mModeState.listModel.observe(this) { listModel ->
                onListModelChanged(viewModule, listModel)
            }
            viewModule.uiState.observe(this) { imagesUiModel ->
                onCategoryAudioModel(imagesUiModel, viewModule)
            }
            startScanModeObserver()
            startObserveLoadState()
            startSideNavigationStatusObserver()
            fragmentViewModel?.previewClickedFileLiveData?.observe(this) {
                mAdapter?.setPreviewClickedFile(it)
            }
        }
    }

    open fun onListModelChanged(
        viewModule: AlbumFragmentViewModel,
        value: Int
    ) {
        if (!viewModule.mModeState.initState) {
            mToolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            return
        }
        Log.d(TAG, "startObserve: mListModel=$value")
        if (value == KtConstants.LIST_SELECTED_MODE) {
            if (baseVMActivity is NavigationInterface) {
                (baseVMActivity as NavigationInterface).showNavigation()
                fragmentViewModel?.setNavigateItemAble(baseVMActivity as NavigationInterface)
            }
            mAdapter?.setSelectEnabled(true)
            previewEditedFiles(fragmentViewModel?.getSelectItems())
            fragmentRecyclerView?.let {
                it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, appContext.resources.getDimensionPixelSize(
                    com.filemanager.common.R.dimen.empty_content_img_width))
                val bottomView = baseVMActivity?.findViewById<View>(R.id.navigation_tool)
                val paddingBottom = KtViewUtils.getSelectModelPaddingBottom(it, bottomView)
                it.setPadding(it.paddingLeft,
                    it.paddingTop,
                    it.paddingRight,
                    paddingBottom)
                fragmentFastScroller?.apply { trackMarginBottom = paddingBottom }
            }
            mToolbar?.let {
                changeActionModeAnim(it, {
                    initToolbarWithEditMode(it)
                    refreshSelectToolbar(it)
                })
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
        } else {
            previewClickedFile(
                fragmentViewModel?.previewClickedFileLiveData?.value,
                fragmentViewModel?.previewClickedFileLiveData
            )
            mToolbar?.let {
                changeActionModeAnim(it, {
                    initToolbarNormalMode(it)
                    refreshScanModeItemIcon(it)
                }, (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true))
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
            if (baseVMActivity is NavigationInterfaceForMain) {
                (baseVMActivity as NavigationInterfaceForMain).hideNavigation {
                    setRecyclerViewNormalState()
                }
            } else if (baseVMActivity is NavigationInterface) {
                setRecyclerViewNormalState()
                (baseVMActivity as NavigationInterface).hideNavigation()
            }
        }
    }

    open fun onCategoryAudioModel(
        categoryAudioUiModel: BaseUiModel<ImageFileWrapper>,
        viewModule: AlbumFragmentViewModel
    ) {
        Log.d(
            TAG,
            "AlbumFragment mUiState =" + categoryAudioUiModel.fileList.size + ","
                    + categoryAudioUiModel.selectedList.size + "," + categoryAudioUiModel.keyWord
                    + "," + categoryAudioUiModel.stateModel.toString()
        )
        if (categoryAudioUiModel.stateModel.listModel.value == KtConstants.LIST_SELECTED_MODE) {
            mToolbar?.let {
                refreshSelectToolbar(it)
            }
            if (categoryAudioUiModel.fileList is ArrayList<ImageFileWrapper>) {
                mAdapter?.setData(
                    categoryAudioUiModel.fileList as ArrayList<ImageFileWrapper>,
                    categoryAudioUiModel.selectedList
                )
                previewEditedFiles(fragmentViewModel?.getSelectItems())
            }
        } else {
            previewClickedFile(
                fragmentViewModel?.previewClickedFileLiveData?.value,
                fragmentViewModel?.previewClickedFileLiveData
            )
            if (categoryAudioUiModel.fileList.isEmpty()) {
                showEmptyView()
            } else {
                hasShowEmpty = false
                mFileEmptyController.hideFileEmptyView()
            }
            mToolbar?.let {
                refreshScanModeItemIcon(it)
                refreshToolbarNormalMode(it)
            }
            if (categoryAudioUiModel.fileList is ArrayList<ImageFileWrapper>) {
                if (viewModule.mBrowseModeState.value == KtConstants.SCAN_MODE_LIST) {
                    listDataRefresh(categoryAudioUiModel)
                } else {
                    mAdapter?.setData(
                        categoryAudioUiModel.fileList as ArrayList<ImageFileWrapper>,
                        categoryAudioUiModel.selectedList
                    )
                }
            }
        }
    }
    private fun listDataRefresh(imagesUiModel: BaseUiModel<ImageFileWrapper>) {
        val imageFileWrappers = imagesUiModel.fileList as ArrayList<ImageFileWrapper>
        val needDoListAnimate = sortAnimationHelper?.needDoListAnimate() ?: false
        Log.d(TAG, "list setData animate $needDoListAnimate")
        if (mGridSpanAnimationHelper != null && needDoListAnimate) {
            fragmentRecyclerView?.let { recyclerView ->
                recyclerView.mTouchable = false
                recyclerView.stopScroll()
            }
            mGridSpanAnimationHelper?.startListSortAnimation(object : OnRefreshDataCallback {
                override fun onRefreshDataCallback() {
                    mAdapter?.setData(imageFileWrappers, imagesUiModel.selectedList)
                }
            }, object : OnAnimatorEndListener {
                override fun onAnimatorEnd() {
                    fragmentRecyclerView?.mTouchable = true
                }
            })
        } else {
            mAdapter?.setData(imageFileWrappers, imagesUiModel.selectedList)
        }
    }

    private fun setRecyclerViewNormalState() {
        mAdapter?.setSelectEnabled(false)
        fragmentRecyclerView?.let {
            val paddingBottom = appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
            it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, paddingBottom)
            fragmentFastScroller?.apply {
                trackMarginBottom = paddingBottom
            }
        }
    }

    private fun startScanModeObserver() {
        fragmentViewModel?.mBrowseModeState?.observe(this) { scanMode ->
            sortAnimationHelper?.setAnimatorDurationWithScanModeChange(fragmentRecyclerView, scanMode)
            mToolbar?.let {
                val needSkipAnimation = mNeedSkipAnimation
                Log.d(TAG, "needSkipAnimation=$needSkipAnimation")
                if (needSkipAnimation) {
                    refreshScanModeAdapter(scanMode)
                } else {
                    fragmentRecyclerView?.let { recyclerView ->
                        recyclerView.mTouchable = false
                        recyclerView.stopScroll()
                    }
                    mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                        override fun onSpanChangeCallback() {
                            mLayoutManager?.scrollToPosition(0)
                            refreshScanModeAdapter(scanMode)
                        }
                    }, object : OnAnimatorEndListener {
                        override fun onAnimatorEnd() {
                            fragmentRecyclerView?.mTouchable = true
                        }
                    })
                }
                delay { refreshScanModeItemIcon(it) }
            }
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            if (scanMode == KtConstants.SCAN_MODE_GRID) {
                refreshScanModeAdapter(scanMode)
            }
            baseVMActivity?.let {
                mFileEmptyController.changeEmptyFileIcon()
            }
            if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
            }
            updateLeftRightMargin()
        }
    }

    override fun updateLeftRightMargin() {
        val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            mAdapter?.notifyDataSetChanged()
        }
    }

    override fun isEmptyList(): Boolean {
        return hasShowEmpty
    }

    override fun handleDragScroll(event: DragEvent): Boolean {
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    override fun resetScrollStatus() {
        scrollHelper?.resetDragStatus()
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        val selectedFiles = DragUtils.getSelectedFiles()
        val itemViewList = ArrayList<View>()
        val size = fragmentViewModel?.uiState?.value?.fileList?.size ?: return null
        selectedFiles?.forEach { fileBean ->
            val indexOf = fragmentViewModel?.uiState?.value?.fileList?.indexOf(fileBean) ?: return null
            if (indexOf >= 0 && indexOf < size) {
                val itemView =
                    mRecyclerView?.findViewHolderForAdapterPosition(indexOf)?.itemView
                itemView?.let { itemViewList.add(it) }
            }
        }
        return itemViewList
    }

    override fun setNavigateItemAble() {
        activity?.lifecycle?.coroutineScope?.launch(Dispatchers.Main) {
            val selectItems = fragmentViewModel?.getSelectItems()
            val selectItemSize = selectItems?.size ?: 0
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble((selectItemSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems))
        }
    }

    override fun getSelectItems(): ArrayList<out BaseFileBean>? {
        return fragmentViewModel?.getSelectItems()
    }

    @SuppressLint("NotifyDataSetChanged")
    internal open fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getGridItemCount(activity, scanMode, GRID_ITEM_DECORATION_ALBUM)
        mLayoutManager?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mAdapter?.apply {
            mScanViewModel = scanMode
            notifyDataSetChanged()
        }
    }

    private fun refreshScanModeItemIcon(toolbar: COUIToolbar, needSkipAnimation: Boolean = true) {
        toolbar.menu.findItem(R.id.actionbar_scan_mode)?.let {
            val desc: String
            val resId: Int = if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                desc = appContext.getString(com.filemanager.common.R.string.palace_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
            } else {
                desc = appContext.getString(com.filemanager.common.R.string.list_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
            }
            it.contentDescription = desc
            setScanModeStatue(it, desc, needSkipAnimation, resId)
        }
    }

    private fun setScanModeStatue(
        toolbar: MenuItem,
        desc: String,
        needSkipAnimation: Boolean,
        resId: Int
    ) {
        if (baseVMActivity?.sideNavigationStatus?.value == KtConstants.LIST_SELECTED_MODE
            && fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true && isChildDisplay
        ) {
            toolbar.setIcon(null)
            toolbar.setTitle(desc)
            toolbar.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
        } else {
            toolbar.setTitle(null)
            if (needSkipAnimation) {
                toolbar.setIcon(resId)
            } else {
                KtAnimationUtil.updateMenuItemWithFadeAnimate(toolbar, resId, baseVMActivity)
            }
            toolbar.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            mLoadingController = LoadingController(it, this).apply {
                observe(fragmentViewModel?.dataLoadState) {
                    (fragmentViewModel?.getRealFileSize() ?: 0) > 0
                }
            }
        }
    }

    private fun startSideNavigationStatusObserver() {
        baseVMActivity?.sideNavigationStatus?.observe(this) { status ->
            Log.d(TAG, "sideNavigationStatus observe: $status")
            toolbar?.let {
                refreshScanModeItemIcon(it)
                setToolbarEditIcon(it, isChildDisplay)
            }
        }
    }

    private fun initToolbarNormalMode(toolbar: COUIToolbar) {
        baseVMActivity?.supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
        }
        toolbar.menu.clear()
        toolbar.isTitleCenterStyle = false
        toolbar.title = mTitle
        toolbar.inflateMenu(getMenuLayoutResId())

        refreshToolbarNormalMode(toolbar)
        setToolbarMenuVisible(toolbar, !isChildDisplay)
        setToolbarEditIcon(toolbar, isChildDisplay)
        previewOperate?.onToolbarMenuUpdated(toolbar.menu)
        toolbar.post {
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
    }

    private fun setToolbarMenuVisible(toolbar: COUIToolbar, visible: Boolean) {
        toolbar.menu.findItem(R.id.action_setting)?.isVisible = visible
    }

    private fun setToolbarEditIcon(toolbar: COUIToolbar, isChildDisplay: Boolean) {
        toolbar.menu.findItem(R.id.actionbar_edit)?.let {
            if (baseVMActivity?.sideNavigationStatus?.value == KtConstants.LIST_SELECTED_MODE
                && fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST
                && fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true && isChildDisplay) {
                it.setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_edit)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            } else {
                it.setIcon(null)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            }
        }
    }

    private fun refreshToolbarNormalMode(toolbar: COUIToolbar) {
        val visible = (fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true)
        toolbar.menu.findItem(R.id.actionbar_edit)?.isVisible = visible
    }

    private fun initToolbarWithEditMode(toolbar: COUIToolbar) {
        baseVMActivity?.supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(false)
        }
        toolbar.apply {
            menu.clear()
            isTitleCenterStyle = true
            inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
    }

    open fun refreshSelectToolbar(toolbar: COUIToolbar) {
        val checkedCount = fragmentViewModel?.uiState?.value?.selectedList?.size ?: 0
        val isSelectAll = (fragmentViewModel?.getRealFileSize() == fragmentViewModel?.uiState?.value?.selectedList?.size)
        ToolbarUtil.updateToolbarTitle(toolbar, checkedCount, isSelectAll)
        (baseVMActivity as? NavigationInterface)?.setNavigateItemAble(
            (fragmentViewModel?.uiState?.value?.selectedList?.isNotEmpty() == true && !DragUtils.isDragging)
                ?: false, hasDrmFile(fragmentViewModel?.getSelectItems()))
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        if (fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true) {
            showEmptyView()
        }
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    private fun showEmptyView() {
        if (hasShowEmpty) return
        if ((baseVMActivity != null) && (rootView != null)) {
            mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!)
            hasShowEmpty = true
            listEmptyFile()
        }
        Log.d(TAG, "showEmptyView")
    }

    override fun getFragmentInstance(): Fragment {
        return this
    }

    override fun setFragmentArguments(arguments: Bundle?) {
        this.arguments = arguments
    }

    override fun setPreviewToolbar(toolbar: COUIToolbar?) {
        mToolbar = toolbar
    }

    override fun onResumeLoadData() {
        if (!isAdded) {
            return
        }
        checkShowPermissionEmpty()
        fragmentViewModel?.initLoader(LoaderViewModel.getLoaderController(this), mContentValues)
        val bundle = arguments ?: return
        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
            baseVMActivity?.apply {
                setSupportActionBar(mToolbar)
                baseVMActivity?.supportActionBar?.apply {
                    setDisplayHomeAsUpEnabled(true)
                    setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
                }
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return activity?.let {
            mFileOperateController?.onNavigationItemSelected(it, item)
        } ?: false
    }

    override fun pressBack(): Boolean {
        val result = fragmentViewModel?.pressBack() ?: false
        if (!result) {
            return if (baseVMActivity is AlbumActivity) {
                false
            } else {
                baseVMActivity?.let {
                    val mainAction = Injector.injectFactory<IMain>()
                    mainAction?.backPreviousFragment(CategoryHelper.CATEGORY_IMAGE, it)
                }
                true
            }
        }
        return true
    }

    @SuppressLint("RestrictedApi")
    override fun onMenuItemSelected(item: MenuItem): Boolean {
        if (null == item || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            android.R.id.home -> {
                if (baseVMActivity is AlbumActivity) {
                    baseVMActivity?.finish()
                } else {
                    baseVMActivity?.let {
                        val mainAction = Injector.injectFactory<IMain>()
                        mainAction?.backPreviousFragment(CategoryHelper.CATEGORY_IMAGE, it)
                    }
                }
                true
            }
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(activity, CategoryHelper.CATEGORY_IMAGE)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, Constants.PAGE_IMAGE)
                OptimizeStatisticsUtil.pageSearch(OptimizeStatisticsUtil.IMAGE)
                true
            }
            R.id.actionbar_edit -> {
                fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                OptimizeStatisticsUtil.pageEdit(OptimizeStatisticsUtil.IMAGE)
                true
            }
            R.id.actionbar_scan_mode -> {
                fragmentViewModel?.clickScanModeItem(baseVMActivity)
                true
            }
            R.id.action_setting -> {
                Injector.injectFactory<ISetting>()?.startSettingActivity(activity)
                OptimizeStatisticsUtil.pageSetting(OptimizeStatisticsUtil.IMAGE)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, Constants.PAGE_IMAGE)
                true
            }
            R.id.action_sort -> {
                Log.d(TAG, "action_sort")
                if (fragmentViewModel?.dataLoadState?.value == OnLoaderListener.STATE_START) {
                    Log.d(TAG, "onMenuItemSelected navigation_sort mFileLoadState = STATE_START")
                    return true
                }
                baseVMActivity?.let {
                    mSortPopupController.showSortPopUp(it, getSortKey(), object : SelectItemListener {
                        override fun onDismiss() { }

                        override fun onPopUpItemClick(
                            flag: Boolean,
                            sortMode: Int,
                            isDesc: Boolean
                        ) {
                            if (flag) {
                                fragmentViewModel?.sortReload()
                                OptimizeStatisticsUtil.albumSort(sortMode)
                            }
                        }
                    })
                }
                toolbarOverflowPopupWindow?.apply {
                    setSubMenuClickListener(AdapterView.OnItemClickListener { _, _, position, _ ->
                        val mainList = itemList
                        val sortIndex =
                            itemList.indexOfFirst { it.title == appContext.getString(com.filemanager.common.R.string.menu_file_list_sort) }
                        if (sortIndex != -1) {
                            mainList[sortIndex].subMenuItemList[position].stateIconId = getSelectImageResource(position)
                            mainList[sortIndex].subMenuItemList[position].isChecked = true
                            mSortPopupController.clickItemHandle(position)
                            dismiss()
                        }
                    })
                }
                true
            }
            com.filemanager.common.R.id.action_select_all -> {
                fragmentViewModel?.clickToolbarSelectAll()
                true
            }
            com.filemanager.common.R.id.action_select_cancel -> {
                if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                }
                true
            }
            else -> {
                false
            }
        }
    }

    private fun initSortMenu() {
        Log.d(TAG, "initSortMenu")
        mToolbar?.menuView?.apply {
            setUseBackgroundBlur(true)
            setOverflowMenuListener { popup ->
                Log.d(TAG, "setOverflowMenuListener")
                toolbarOverflowPopupWindow = popup
                val mainList = popup.itemList
                val sortIndex = popup.itemList.indexOfFirst {
                    it.title == appContext.getString(com.filemanager.common.R.string.menu_file_list_sort)
                }
                for ((index, subItem) in mainList[sortIndex].subMenuItemList.withIndex()) {
                    val imdRes = getSelectImageResource(index)
                    subItem.isChecked = (index == mSortPopupController.getDefaultItem())
                    subItem.stateIconId = imdRes
                }
            }
        }
    }

    @DrawableRes
    internal fun getSelectImageResource(position: Int): Int {
        var res: Int = com.filemanager.common.R.drawable.ic_order_default
        if (position == mSortPopupController.getDefaultItem()) {
            res = if (mSortPopupController.ismIsDesc()) {
                com.filemanager.common.R.drawable.ic_order_desc
            } else {
                com.filemanager.common.R.drawable.ic_order_asc
            }
        }
        return res
    }

    override fun fromSelectPathResult(requestCode: Int, paths: List<String>?) {
        activity?.let { mFileOperateController?.onSelectPathReturn(it, requestCode, paths) }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(getMenuLayoutResId(), menu)
        initSortMenu()
        mToolbar?.apply {
            mNeedSkipAnimation = true
            refreshScanModeItemIcon(this)
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this, isChildDisplay)
        }
    }

    override fun onItemClick(item: ItemDetails<Int>, e: MotionEvent): Boolean {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if (uiModel.stateModel.listModel.value != KtConstants.LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.keyMap[item.selectionKey].apply {
                Log.d(TAG, "onItemClick baseFile=$this")
            } ?: return true
            activity?.let {
                val previewResult = previewClickedFile(baseFile, fragmentViewModel?.previewClickedFileLiveData)
                if (!previewResult) {
                    val mediaImgIds: ArrayList<String> = ArrayList()
                    // 判断当前点击是否是媒体库中的图片
                    lifecycleScope.launch(context = Dispatchers.IO) {
                        if (FileMediaHelper.isImgAndInMedia(baseFile)) {
                            // 获取当前视图中的图片文件，按照排序顺序生成一个media id列表，传给相册
                            fragmentViewModel?.uiState?.value?.fileList?.forEach { file ->
                                if (file.mLocalType == MimeTypeHelper.IMAGE_TYPE) {
                                    mediaImgIds.add(file.mId.toString())
                                }
                            }
                            // 限制列表大小
                            FileMediaHelper.limitNumberOfFileList(baseFile, mediaImgIds)
                        }
                        withContext(Dispatchers.Main) {
                            mFileOperateController?.onFileClick(it, baseFile, e, mediaImgIds)
                        }
                    }
                }
            }
        }
        return false
    }

    override fun createViewModel(): AlbumFragmentViewModel {
        val vm = ViewModelProvider(this)[AlbumFragmentViewModel::class.java]
        mFileOperateController = NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_IMAGE,
                vm, SortHelper.FILE_TIME_REVERSE_ORDER).also {
            it.setResultListener(FileOperatorListenerImpl(vm))
            it.setInterceptor(vm)
        }
        return vm
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        fragmentRecyclerView?.adapter?.notifyDataSetChanged()
        mFileOperateController?.onConfigurationChanged(newConfig)
        fragmentViewModel?.onConfigurationChanged()
    }

    override fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.P_CHILD_DISPLAY, isChildDisplay)
        mToolbar?.let {
            setToolbarMenuVisible(it, !isChildDisplay)
            setToolbarEditIcon(it, isChildDisplay)
            refreshScanModeItemIcon(it)
        }
    }

    override fun backToTop() {
        fragmentRecyclerView?.fastSmoothScrollToTop()
    }

    override fun updatedLabel() {}

    override fun permissionSuccess() {}

    override fun setCurrentFromOtherSide(currentPath: String) {}

    override fun getCurrentPath(): String {
        return ""
    }

    override fun getScanMode(): Int {
        return fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
    }

    override fun setScanMode(mode: Int) {
        fragmentViewModel?.mBrowseModeState?.value = mode
    }

    override fun isSelectionMode(): Boolean {
        return fragmentViewModel?.isInSelectMode() ?: false
    }

    override fun setPreviewOperate(operate: IPreviewOperate) {
        previewOperate = operate
    }

    override fun onDragStart(e: MotionEvent): Boolean {
        Log.d(TAG, "onDragStart dragging:${DragUtils.isDragging}")
        if (DragUtils.isDragging) return false
        fragmentDragScanner?.cancel(true)
        val view = fragmentRecyclerView?.findChildViewUnder(e.x, e.y) ?: return false
        var result = true

        fragmentRecyclerView?.post {
            val position = fragmentRecyclerView?.getChildAdapterPosition(view) ?: 0
            if (position == -1) {
                Log.e(TAG, "onDragStart position is -1")
                result = false
                return@post
            }
            val dragHoldDownFile = fragmentViewModel?.uiState?.value?.fileList?.get(position)
            val activityContext = this.activity ?: run { result = false; return@post }
            val selectList = fragmentViewModel?.getSelectItems() ?: run { result = false; return@post }
            val viewMode = fragmentViewModel?.getRecyclerViewScanMode()
            val dragHoldDrawable = if (viewMode == SelectionTracker.LAYOUT_TYPE.LIST) {
                view.findViewById<ImageView>(com.filemanager.common.R.id.file_list_item_icon).drawable
            } else {
                view.findViewById<ImageView>(com.filemanager.common.R.id.file_grid_item_icon).drawable
            }

            val itemViewList = ArrayList<View>()
            selectList.forEach { baseFileBean ->
                val fileList = fragmentViewModel?.uiState?.value?.fileList
                val indexOf = fileList?.indexOf(baseFileBean)
                if (indexOf != null && indexOf >= 0 && indexOf < fileList.size) {
                    val viewHolder = fragmentRecyclerView?.findViewHolderForAdapterPosition(indexOf)
                    if (viewHolder != null) {
                        itemViewList.add(viewHolder.itemView)
                    }
                }
            }
            (baseVMActivity as? NavigationInterface)?.let { fragmentViewModel?.setNavigateItemAble(it) }
            DragUtils.createSelectedFileList(selectList)
            fragmentDragScanner = FileDragDropScanner(
                activityContext,
                DefaultDragListener(
                    activityContext,
                    view,
                    dragHoldDownFile,
                    dragHoldDrawable,
                    getFragmentCategoryType(),
                    e,
                    viewMode
                ).addSelectedView(itemViewList),
                viewMode,
                viewMode == SelectionTracker.LAYOUT_TYPE.GRID
            )
            fragmentDragScanner?.let {
                if (it.addData(selectList)) {
                    it.execute()
                }
            }
            Log.d(TAG, "onDragStart end")
        }
        return result
    }

    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        super.updatePermissionEmptyMarginTop()
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    internal fun previewClickedFile(
        file: BaseFileBean?,
        clickFileLiveData: MutableLiveData<BaseFileBean?>?
    ): Boolean {
        return previewOperate?.previewClickedFile(file, clickFileLiveData) ?: false
    }

    private fun previewEditedFiles(files: List<BaseFileBean>?): Boolean {
        mFileOperateController?.setPreviewOpen(isPreviewOpen())
        return previewOperate?.previewEditedFiles(files) ?: false
    }

    private fun isPreviewOpen(): Boolean {
        return previewOperate?.isPreviewOpen() ?: false
    }

    private fun listEmptyFile() {
        previewOperate?.listEmptyFile()
    }

    override fun getOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.OperateResultListener? =
        mFileOperateController?.pickResultListener()

    override fun exitSelectionMode() {
        if (isSelectionMode()) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val sideNavigationWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        val gridWidth = if (isOpen) {
            windowWidth - sideNavigationWidth
        } else {
            windowWidth
        }
        val gridWidthDp = ViewHelper.px2dip(appContext, gridWidth)
        val spanCount = ItemDecorationFactory.getAlumOrVideoColumnByScreenWidth(gridWidthDp)
        val itemSpace = appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.weixin_grid_vertical_spacing)
        val itemWidth = KtViewUtils.getGridItemWidth(activity, itemSpace, spanCount, 0, gridWidth)
        mAdapter?.setItemWith(itemWidth)

        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            sideNavigationWidth,
            GRID_ITEM_DECORATION_ALBUM
        )
        return true
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_IMAGE
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            fragmentRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController = SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }

    open fun getMenuLayoutResId(): Int {
        return R.menu.album_menu
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
    }

    open fun getSortKey(): String {
        return SortRecordModeFactory.getAlbumKey()
    }
}