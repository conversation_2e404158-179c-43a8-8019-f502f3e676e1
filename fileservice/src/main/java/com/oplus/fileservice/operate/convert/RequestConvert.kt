/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RequestConvert
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/19 20:31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/19       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.convert

import androidx.annotation.VisibleForTesting
import com.filemanager.common.utils.Log
import com.oplus.fileservice.operate.internal.ConflictPolicy
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.internal.OPERATE_COPY
import com.oplus.fileservice.operate.internal.OPERATE_CUT
import com.oplus.fileservice.operate.internal.OPERATE_DELETE
import kotlin.collections.ArrayList

private const val TAG = "OperateRequest"
private const val KEY_OPERATE_TYPE = "operateType"
private const val KEY_TARGET_FILE_INFO = "targetFileInfo"
private const val KEY_SOURCE_FILE_INFO = "sourceFileInfo"
private const val KEY_OPTION = "option"
private const val KEY_CONFLICT_POLICY = "conflictPolicy"
private const val KEY_DATA = "data"
private const val KEY_DISPLAY_NAME = "displayName"
private const val KEY_RECYCLE_ID = "recycleId"

private const val CONFLICT_POLICY_KEEP_BOTH = 1
private const val CONFLICT_POLICY_REPLACE = 2
private const val CONFLICT_POLICY_SKIP = 3

fun convertOperateInfoByRequest(request: Any?): OperateRequest {
    val operateRequest = OperateRequest()
    request?.let { operateInfo ->
        if (operateInfo !is Map<*, *>) {
            return operateRequest
        }

        operateInfo.forEach { (key, value) ->
            Log.d(TAG, "Convert request to operate info: $key - $value")
            when (key) {
                KEY_OPERATE_TYPE -> operateRequest.operateType = value as? Int ?: -1
                KEY_TARGET_FILE_INFO -> {
                    Log.d(TAG, "Convert target file info.")
                    val targetFileInfo = parseFileInfo(value)
                    operateRequest.targetFileInfo = targetFileInfo
                }
                KEY_SOURCE_FILE_INFO -> {
                    Log.d(TAG, "Convert source file info.")
                    val sourceFileInfo = parseFileInfoLists(value)
                    operateRequest.sourceFileInfo = sourceFileInfo
                }
            }
        }

        if (operateInfo.containsKey(KEY_CONFLICT_POLICY)) {
            Log.d(TAG, "convertOperateInfoByRequest -> conflictPolicy = ${operateInfo[KEY_CONFLICT_POLICY]}")
            parseConflictPolicy(operateRequest, operateInfo[KEY_CONFLICT_POLICY])
        }

        if (operateInfo.containsKey(KEY_OPTION)) {
            Log.d(TAG, "convertOperateInfoByRequest -> option = ${operateInfo[KEY_OPTION]}")
            parseOptions(operateRequest, operateInfo[KEY_OPTION])
        }
    }
    Log.d(TAG, "convertOperateInfoByRequest -> operateRequest = $operateRequest")
    return operateRequest
}

@VisibleForTesting
fun parseFileInfoLists(fileInfoLists: Any?): List<FileInfo> {
    val fileInfos = mutableListOf<FileInfo>()
    if (fileInfoLists !is ArrayList<*>) {
        return fileInfos
    }
    fileInfoLists.forEach { fileInfo ->
        if (fileInfo is Map<*, *>) {
            val file = parseFileInfo(fileInfo)
            fileInfos.add(file)
        }
    }
    return fileInfos
}

@VisibleForTesting
fun parseFileInfo(fileInfoMap: Any?): FileInfo {
    if (fileInfoMap !is Map<*, *>) {
        return FileInfo()
    }
    var fileData: String? = null
    var fileDisplayName: String? = null
    var recycleId: String? = null
    fileInfoMap.forEach { (key, value) ->
        when (key) {
            KEY_DATA -> fileData = value as String
            KEY_DISPLAY_NAME -> fileDisplayName = value as String
            KEY_RECYCLE_ID -> recycleId = value as String
        }
    }
    return FileInfo(fileDisplayName, fileData, recycleId)
}

@VisibleForTesting
fun parseConflictPolicy(operateRequest: OperateRequest, conflictPolicy: Any?) {
    if (operateRequest.operateType == OPERATE_COPY || operateRequest.operateType == OPERATE_CUT && conflictPolicy is Int) {
        operateRequest.conflictPolicy = when (conflictPolicy) {
            CONFLICT_POLICY_KEEP_BOTH -> ConflictPolicy.KEEP_BOTH
            CONFLICT_POLICY_REPLACE -> ConflictPolicy.REPLACE
            CONFLICT_POLICY_SKIP -> ConflictPolicy.SKIP
            else -> ConflictPolicy.KEEP_BOTH
        }
    }
}

@VisibleForTesting
fun parseOptions(operateRequest: OperateRequest, options: Any?) {
    if (operateRequest.operateType == OPERATE_DELETE && options is Int) {
        operateRequest.option = options
    }
}