/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: UtilsTest.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/9/03
 ** Author: <PERSON><PERSON><PERSON><PERSON>
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/7/22      1.0     create
 ****************************************************************/
package com.coloros.filemanager.utils;

import static com.filemanager.common.utils.Utils.ACTIVITY_QUICK_CLICK;
import static com.filemanager.common.utils.Utils.FRAGMENT_QUICK_CLICK;
import static com.filemanager.common.utils.Utils.RTL_POSITION_DOUBLE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.database.Cursor;
import android.icu.text.DateFormat;
import android.net.Uri;
import android.os.Build;

import com.filemanager.common.compat.FeatureCompat;
import com.filemanager.common.compat.MediaScannerCompat;
import com.filemanager.common.helper.FileWrapper;
import com.filemanager.common.helper.PathHelper;
import com.filemanager.common.utils.Utils;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.robolectric.Robolectric;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.Shadows;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;
import org.robolectric.shadows.ShadowLog;
import org.robolectric.shadows.ShadowPackageManager;

import java.util.ArrayList;
import java.util.Date;

import shadow.ShadowColorDateUtils;
import shadow.ShadowContentResolver;
import shadow.ShadowPathHelper;
import shadow.ShadowVolumeEnvironment;
import shadow.mapping.MappingUserHandleNative;
import shadow.mapping.utils.MappingUtil;

@Ignore("stupid test")
@RunWith(RobolectricTestRunner.class)
@Config(sdk = Build.VERSION_CODES.S)
public class UtilsTest {
    private Context mContext;
    private Activity mActivity;
    private Cursor mCursor;
    private String mPath = "D:/";
    private FileWrapper mFileWrapper;
    private ShadowApplication mShadowApplication;
    private static final String IS_OPPO_MULTIAPP_SUPPORT = "oppo.multiapp.support";

    @BeforeClass
    public static void classSetUp() {
        MappingUtil.init(MappingUserHandleNative.class, MappingUtil.FLAG_CLASS_INITI);
    }

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = RuntimeEnvironment.application;
        mActivity = Robolectric.setupActivity(Activity.class);

        mFileWrapper = mock(FileWrapper.class);
        when(mFileWrapper.exists()).thenReturn(true);
        mShadowApplication = ShadowApplication.getInstance();
    }

    @After
    public void tearDown() {
        mContext = null;
        mFileWrapper = null;
        mShadowApplication = null;
    }

    @Test
    @Config(shadows = {ShadowContentResolver.class})
    public void should_return_false_when_isMediaScannerScanning_with_cursor_count_is_zero() {
        mCursor = mock(Cursor.class);
        ShadowContentResolver.setsCursor(mCursor);
        assertFalse(MediaScannerCompat.isMediaScannerScanning());
    }

    @Test
    @Config(shadows = {ShadowContentResolver.class})
    public void should_return_true_when_isMediaScannerScanning_with_cursor_count_is_one() {
        mCursor = mock(Cursor.class);
        when(mCursor.getCount()).thenReturn(1);
        ShadowContentResolver.setsCursor(mCursor);
        assertFalse(MediaScannerCompat.isMediaScannerScanning());
    }

    @Test
    public void should_return_file_size_when_formatSize_with_file() {
        when(mFileWrapper.exists()).thenReturn(false);
        assertEquals("", Utils.formatSize(mFileWrapper));
        when(mFileWrapper.exists()).thenReturn(true);
        when(mFileWrapper.isDirectory()).thenReturn(true);
        assertEquals("", Utils.formatSize(mFileWrapper));
        when(mFileWrapper.isDirectory()).thenReturn(false);
        assertEquals(Utils.byteCountToDisplaySize(mFileWrapper.length()), Utils.formatSize(mFileWrapper));

    }


    @Test
    public void should_return_format_size() {
        assertEquals("", Utils.formatSize(null));
        when(mFileWrapper.isDirectory()).thenReturn(true);
        assertEquals("", Utils.formatSize(mFileWrapper));
        when(mFileWrapper.isDirectory()).thenReturn(false);
        assertEquals(Utils.formatMessage(Utils.byteCountToDisplaySize(mFileWrapper.length()), RTL_POSITION_DOUBLE), Utils.formatSize(mFileWrapper));
    }


    @Test
    public void should_return_storage_available_size() {
        assertEquals(0, Utils.getStorageAvailableSize("path"));
    }

    @Test
    public void should_return_storage_total_size() {
        assertEquals(0, Utils.getStorageTotalSize("path"));
    }


    @Test
    @Config(shadows = {ShadowVolumeEnvironment.class, ShadowPathHelper.class})
    public void should_return_result_when_getVirtualPathString_with_flagI_isFalse_flagE_isFalse() {
        assertEquals("", Utils.getVirtualPathString(null, ""));

        ShadowVolumeEnvironment.setmInternalSdPath("C");
        ShadowVolumeEnvironment.setmExternalSdPath("C");

        assertTrue(Utils.getVirtualPathString(mContext, "C-test").contains("test"));

        ShadowPathHelper.setsRootPath("1");
        String result = mPath.substring(new PathHelper(mContext).getRootPath().length() + 1);
        assertEquals(result, Utils.getVirtualPathString(mContext, mPath));

        ShadowVolumeEnvironment.setmExternalSdPath("B");
        assertTrue(Utils.getVirtualPathString(mContext, "C-test").contains("test"));

        ShadowVolumeEnvironment.setmInternalSdPath("C");
        ShadowVolumeEnvironment.setmInternalSdPath("B");
        assertTrue(Utils.getVirtualPathString(mContext, "C-test").contains("test"));

        ShadowVolumeEnvironment.setmInternalSdPath("C2");
        assertTrue(Utils.getVirtualPathString(mContext, "C-test").contains("test"));

        MockedStatic<FeatureCompat> featureCompatMock = mockStatic(FeatureCompat.class);

        featureCompatMock.when(() -> FeatureCompat.getSIsSupportMultiApp()).thenReturn(true);
        assertEquals(result, Utils.getVirtualPathString(mContext, mPath));

        featureCompatMock.close();

    }


    @Test
    public void should_return_file_size_when_formatSize_with_file_for_unit() {
        when(mFileWrapper.isDirectory()).thenReturn(false);

        MockedStatic<FeatureCompat> featureCompatMock = mockStatic(FeatureCompat.class);


        featureCompatMock.when(() -> FeatureCompat.getSIsStorageUnitNormal()).thenReturn(true);
        assertEquals(Utils.byteCountToDisplaySizeForUnit(mFileWrapper.length()), Utils.formatSize(mFileWrapper));

        featureCompatMock.when(() -> FeatureCompat.getSIsStorageUnitNormal()).thenReturn(false);
        assertEquals(Utils.byteCountToDisplaySizeForUnit(mFileWrapper.length()), Utils.formatSize(mFileWrapper));
        featureCompatMock.close();
    }


    @Test
    public void should_return_category_item_last_record() {
        assertEquals(0, Utils.getCategoryItemLastRecord(mContext, "test@"));
    }

    @Test
    @Config(shadows = {ShadowVolumeEnvironment.class})
    public void should_return_storage_mounted() {
        ShadowVolumeEnvironment.setmInternalSdMounted(true);
        assertTrue(Utils.isStorageMounted(mContext));
        ShadowVolumeEnvironment.setmInternalSdMounted(false);
        assertFalse(Utils.isStorageMounted(mContext));
    }

    @Test
    public void should_return_app_state_enable() {
        assertFalse(Utils.checkAppStateEnable(mContext, "test"));
    }

    @Test
    public void should_operating_flag() {
        Utils.setOperatingFlag(false);
        assertFalse(Utils.isOperating());
    }


    @Test
    @Config(shadows = {ShadowVolumeEnvironment.class})
    public void should_return_boolean_when_isOperateDatabase_with_contextAndPath() {
        assertFalse(Utils.isOperateDatabase(mContext, null));
        assertFalse(Utils.isOperateDatabase(null, mPath));
        ShadowPackageManager shadowPackageManager = Shadows.shadowOf(mContext.getPackageManager());
        shadowPackageManager.setSystemFeature(IS_OPPO_MULTIAPP_SUPPORT, true);
        assertTrue(Utils.isOperateDatabase(mContext, mPath));
        ShadowVolumeEnvironment.setmInternalSdPath("");
        assertTrue(Utils.isOperateDatabase(mContext, mPath));
    }


    @Test
    public void should_return_sSourcePath_when_SetSourcePath_with_ArrayList() {
        ArrayList<String> paths = new ArrayList();
        String test = "a";
        paths.add(test);
        Utils.setSourcePath(paths);
        assertEquals(paths, Utils.getSourcePath());

        Utils.setSourcePath(null);
        assertEquals(new ArrayList(), Utils.getSourcePath());
    }

    @Test
    public void should_return_sSourcePath_Null_when_releaseSourcePath_without() {
        ArrayList<String> paths = new ArrayList();
        String test = "a";
        paths.add(test);
        Utils.setSourcePath(paths);
        Utils.releaseSourcePath();
        assertNull(Utils.getSourcePath());

        Utils.setSourcePath(null);
        assertEquals(new ArrayList(), Utils.getSourcePath());
    }

    @Test
    public void should_return_sSourceUri_when_setSourceUri_with_ArrayList() {
        Utils.setSourceUri(null);
        assertEquals(new ArrayList(), Utils.getSourceUri());

        ArrayList<Uri> uris = new ArrayList<Uri>();
        Uri uri = mock(Uri.class);
        uris.add(uri);
        Utils.setSourceUri(uris);
        assertEquals(uris, Utils.getSourceUri());
    }

    @Test
    public void should_return_sSourceUri_Null_when_releaseSourceUri_without() {
        Utils.releaseSourceUri();
        assertNull(Utils.getSourceUri());
        ArrayList<Uri> uris = new ArrayList<Uri>();
        Uri uri = mock(Uri.class);
        uris.add(uri);
        Utils.setSourceUri(uris);
        Utils.releaseSourceUri();
        assertNull(Utils.getSourceUri());
    }


    @Test
    public void should_return_storage_total_size_after_format() {
        when(mFileWrapper.isDirectory()).thenReturn(false);

        MockedStatic<FeatureCompat> featureCompatMock = mockStatic(FeatureCompat.class);


        featureCompatMock.when(() -> FeatureCompat.getSIsStorageUnitNormal()).thenReturn(true);
        assertEquals(0, Utils.getStorageTotalSizeAfterFormat("path"));

        featureCompatMock.when(() -> FeatureCompat.getSIsStorageUnitNormal()).thenReturn(false);
        assertEquals(0, Utils.getStorageTotalSizeAfterFormat("path"));

        featureCompatMock.close();
    }

//    @Test
//    public void should_return_RTLPath_when_normalizeRTLPath_with_path() {
//        String path = "D:/";
//        String expected = "\u200E" + path.replaceAll("/", "/\u200E");
//        Assert.assertEquals(null, Utils.normalizeRTLPath(null));
//        Assert.assertEquals(expected, Utils.normalizeRTLPath(path));
//    }
//
//    @Test
//    @Config(shadows = {ShadowColorDateUtils.class})
//    public void should_return_date_when_getDateFormat_with_timeMillis() {
//        long timeMillis = System.currentTimeMillis();
//        Date date = new Date(timeMillis);
//        String formatDate = Utils.getYMDDate(date);
//        String dateAndTime = formatDate;
//        Assert.assertEquals(dateAndTime, Utils.getDateFormat(mContext, timeMillis));
//    }
//
//    @Test
//    public void should_return_dateAndTime_when_getDateAndTimeFormat_with_timeMillis() {
//        long timeMillis = System.currentTimeMillis();
//        Date date = new Date(timeMillis);
//        String formatDate = Utils.getYMDDate(date);
//        String formatTime = Utils.getTime(mContext, date);
//        String dateAndTime = formatDate + " " + formatTime;
//        Assert.assertEquals(dateAndTime, Utils.getDateAndTimeFormat(mContext, timeMillis));
//    }

    @Test
    @Config(shadows = {ShadowColorDateUtils.class})
    public void should_return_MDDate_when_testGetDateMDFormat_with_timeMillis() {
        long timeMillis = System.currentTimeMillis();
        Date date = new Date(timeMillis);
        String formatDate = Utils.getMDDate(mContext, date);
        String dateAndTime = formatDate;
        Assert.assertEquals(dateAndTime, Utils.getDateMDFormat(mContext, timeMillis));
    }

    @Test
    public void should_return_true_when_isNightMode_with_context() {
        Context context = mock(Context.class);
        Resources resources = mock(Resources.class);
        Configuration configuration = mock(Configuration.class);
        when(context.getResources()).thenReturn(resources);
        when(resources.getConfiguration()).thenReturn(configuration);
        configuration.uiMode = 32;
        assertTrue(Utils.isNightMode(context));

        assertFalse(Utils.isNightMode(null));
    }


    @Test
    public void should_return_true_when_isNeededFilter() {
        Utils.setFilterState(true);
        assertTrue(Utils.getFilterState());

        Utils.setFilterState(false);
        assertFalse(Utils.getFilterState());
    }

    @Test
    public void should_return_batter_left() {
        assertEquals(Utils.getBatteryLeft(), 0);
    }


    @Test
    public void should_return_data_and_time_format() {
        assertTrue(!Utils.getDateAndTimeFormat(mContext, 123123123).isEmpty());
    }

    @Test
    public void should_return_data_format() {
        assertTrue(!Utils.getDateFormat(mContext, 123123123).isEmpty());
    }

    @Ignore
    @Test
    public void should_return_target_language() {
        assertFalse(Utils.isNeededTargetLanguage("test"));
    }

    @Test
    public void should_needed_sdk() {
        assertTrue(Utils.isNeededSdk24());
        assertTrue(Utils.isNeededSdk27());
        assertTrue(Utils.isNeededSdk28());
    }

    @Test
    public void should_container_user() {
        assertFalse(Utils.isContainerUser(mContext));
    }

    @Test
    public void should_is_rtl() {
        assertFalse(Utils.isRtl());
    }

    @Test
    public void should_navigation_bar_height() {
        assertEquals(0, Utils.getNavigationBarHeight(null));
        assertEquals(0, Utils.getNavigationBarHeight(mActivity));
    }
    //getYMDDate

    @Test
    public void should_return_ymd_data() {
        Date date = new Date(123123);
        assertEquals(DateFormat.getDateInstance(DateFormat.LONG).format(date), Utils.getYMDDate(date));
    }

    @Test
    public void should_return_dash_data() {
        Date date = new Date(123123);
        assertEquals(DateFormat.getDateInstance(java.text.DateFormat.SHORT).format(date), Utils.getDashDate(date));
    }

    @Test
    public void should_return_time_to_seconds() {
        Date date = new Date(123123);
        assertEquals(java.text.DateFormat.getTimeInstance(java.text.DateFormat.MEDIUM).format(date), Utils.getTimeToSeconds(date));
    }

    @Test
    public void should_return_time() {
        Date date = new Date(123123);
        assertNull(Utils.getTime(null, date));
        assertEquals(android.text.format.DateFormat.getTimeFormat(mContext).format(date),
                Utils.getTime(mContext, date));
    }

    @Test
    public void should_return_md_data() {
        Date date = new Date(123123);
        assertNull(Utils.getMDDate(null, date));
    }

    @Test
    public void should_return_application_detail_format() {
        assertEquals("", Utils.getApplicationDetailFormat(null, "test1", "test2", mFileWrapper));
        assertTrue(Utils.getApplicationDetailFormat(mContext,
                "test1", "test2",
                mFileWrapper).toString().contains("test1"));
        assertTrue(Utils.getApplicationDetailFormat(mContext,
                "test1", "test2",
                mFileWrapper).toString().contains("test2"));
        assertTrue(Utils.getApplicationDetailFormat(mContext,
                null, "test2",
                mFileWrapper).toString().contains("test2"));
    }

    @Test
    public void should_quick_click() {

        assertFalse(Utils.isQuickClick());

        assertTrue(Utils.isQuickClick());

        assertFalse(Utils.isQuickClick(ACTIVITY_QUICK_CLICK));

        assertFalse(Utils.isQuickClick(FRAGMENT_QUICK_CLICK));
    }

    @Test
    public void should_quick_change_model() {

        assertFalse(Utils.isQuickChangeModel());

        assertTrue(Utils.isQuickChangeModel());

    }

    @Test
    public void should_return_detail_when_format_detail_with_detail_string() {
        assertEquals("", Utils.formatDetail(null, null, null));
        assertEquals("", Utils.formatDetail(null, null, ""));
        assertEquals("", Utils.formatDetail(null, "", null));

        assertNotNull(Utils.formatDetail(null, "0kb", "2021/12/1"));
    }

    @Test
    public void should_return_detail_when_format_detail_drag_with_detail() {
        assertEquals("", Utils.formatDetailDrag(null, null));
        assertEquals("", Utils.formatDetailDrag(null, ""));
        assertEquals("", Utils.formatDetailDrag("", null));

        assertNotNull(Utils.formatDetailDrag("0kb", "2021/12/1"));
    }

    @Test
    public void should_return_message_when_format_message_with_message() {
        assertEquals("", Utils.formatMessage(null, 0));

        assertNotNull(Utils.formatMessage("", 0));
        assertNotNull(Utils.formatMessage("message", 0));
    }

    @Test
    public void should_return_path_when_formatPathWithRTL_with_path() {
        assertNull(Utils.formatPathWithRTL(null));

        assertNotNull(Utils.formatPathWithRTL("path"));
    }

    @Test
    public void should_return_name_when_formatFileNameWithRTL_with_name() {
        assertNull(Utils.formatFileNameWithRTL(null));

        assertNotNull(Utils.formatFileNameWithRTL("path"));
    }


    @Test
    public void should_when_calcSpaceSizeRatio() {
        long availableSize = 0L;
        long totalSize = 0L;
        int result = Utils.calcSpaceSizeRatio(availableSize, totalSize);
        Assert.assertEquals(0, result);

        availableSize = 1000L;
        totalSize = 0L;
        result = Utils.calcSpaceSizeRatio(availableSize, totalSize);
        Assert.assertEquals(0, result);

        availableSize = 0L;
        totalSize = 1000L;
        result = Utils.calcSpaceSizeRatio(availableSize, totalSize);
        Assert.assertEquals(1000, result);

        availableSize = 10L;
        totalSize = 1000L;
        result = Utils.calcSpaceSizeRatio(availableSize, totalSize);
        Assert.assertEquals(990, result);

        availableSize = 1000L;
        totalSize = 1000L;
        result = Utils.calcSpaceSizeRatio(availableSize, totalSize);
        Assert.assertEquals(0, result);

        availableSize = 300L;
        totalSize = 1024L;
        result = Utils.calcSpaceSizeRatio(availableSize, totalSize);
        Assert.assertEquals(707, result);
    }

    @Test
    public void byteCountToDisplayUnitInThousandTest() {
        Assert.assertEquals(
                "0\u00A0B",
                Utils.byteCountToDisplayUnitInThousand(mContext, 0L)
        );

        Assert.assertEquals(
                "100\u00A0B",
                Utils.byteCountToDisplayUnitInThousand(mContext, 100L)
        );

        Assert.assertEquals(
                "1\u00A0KB",
                Utils.byteCountToDisplayUnitInThousand(mContext, 1000L)
        );

        Assert.assertEquals(
                "3\u00A0KB",
                Utils.byteCountToDisplayUnitInThousand(mContext, 2500L)
        );

        Assert.assertEquals(
                "5.0\u00A0MB",
                Utils.byteCountToDisplayUnitInThousand(mContext, 5000000L)
        );

        Assert.assertEquals(
                "5.00\u00A0GB",
                Utils.byteCountToDisplayUnitInThousand(mContext, 5000000000L)
        );
    }

    @Test
    public void formatInThousandTest() {
        Assert.assertEquals(
                0,
                Utils.formatInThousand(0)
        );

        Assert.assertEquals(
                0,
                Utils.formatInThousand(0.01)
        );

        Assert.assertEquals(
                2,
                Utils.formatInThousand(1.0)
        );

        Assert.assertEquals(
                2,
                Utils.formatInThousand(1.001)
        );

        Assert.assertEquals(
                128,
                Utils.formatInThousand(100.001)
        );
    }
}
