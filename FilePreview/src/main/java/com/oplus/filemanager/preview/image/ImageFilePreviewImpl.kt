/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ImageFilePreviewImpl.kt
 * Description:
 *     The implementation to show image to preview it.
 *
 * Version: 1.0
 * Date: 2024-09-05
 * Author: Bixia<PERSON>.<EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-09-05   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.image

import android.graphics.Bitmap
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.core.AbsPreviewLoadingScheduler
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.preview.utils.BorderedRoundedCornersTrans
import com.oplus.filemanager.preview.utils.GlideExtend
import com.oplus.filemanager.preview.utils.getFileUri
import com.oplus.filemanager.preview.utils.glideSignatureKey
import com.oplus.filemanager.preview.utils.logInfo

internal class ImageFilePreviewImpl(
    private val lifecycleOwner: LifecycleOwner,
    private val previewModel: FilePreviewViewModel
) : IImageFilePreview {

    private companion object {
        private const val TAG = "ImageFilePreviewImpl"
    }

    private val loadingScheduler = PreviewLoadingScheduler()
    private val defImagePlaceholder = KtThumbnailHelper.getClassifyResId(MimeTypeHelper.IMAGE_TYPE)
    private val onImageLoaded = Observer<BaseFileBean?> { displayOnContainer(it) }

    private var containerManager: IImagePreviewContainerManager? = null

    override fun release() {
        previewModel.releaseLoader(onImageLoaded)
        loadingScheduler.resetLoadingScheduler()
        containerManager?.imageView?.let {
            GlideExtend.with(it.context)?.clear(it)
        }
        containerManager = null
    }

    override fun attachToContainer(containerManager: IImagePreviewContainerManager) {
        this.containerManager = containerManager
        containerManager.imageView.setImageResource(defImagePlaceholder)
        containerManager.defaultContainer.setFileIcon(defImagePlaceholder)
        loadingScheduler.requireToShowLoading()
        previewModel.loadPreviewFile(lifecycleOwner, onImageLoaded)
    }

    private fun displayOnContainer(imageBean: BaseFileBean?) {
        val fileName = imageBean?.mDisplayName
        containerManager?.apply {
            fillFileDetailInfo(imageBean)
            defaultContainer.setFileName(fileName)
        }
        val imageView = containerManager?.imageView ?: return
        val imageUri = imageBean?.getFileUri()
        if (imageUri == null) {
            imageView.setImageResource(defImagePlaceholder)
            return
        }
        Log.d(TAG, "displayOnContainer: start load: ${imageBean.logInfo()}")
        val roundCorners = BorderedRoundedCornersTrans(imageView.context)
        var options = RequestOptions()
        options = options.diskCacheStrategy(DiskCacheStrategy.NONE)
            .skipMemoryCache(false)
            .signature(imageBean.glideSignatureKey())
        options = options.transform(roundCorners)
        Glide.with(imageView.context)
            .asBitmap()
            .load(imageUri)
            .apply(options)
            .placeholder(defImagePlaceholder)
            .addListener(object : RequestListener<Bitmap> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Bitmap>,
                    isFirstResource: Boolean
                ): Boolean {
                    Log.e(TAG, "displayOnContainer: onLoadFailed: ${imageBean.logInfo()}")
                    loadingScheduler.dismissLoading(true)
                    return false
                }

                override fun onResourceReady(
                    resource: Bitmap,
                    model: Any,
                    target: Target<Bitmap>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    Log.d(TAG, "displayOnContainer: onResourceReady: ${imageBean.logInfo()}")
                    loadingScheduler.dismissLoading(false)
                    return false
                }
            })
            .into(imageView)
    }

    private inner class PreviewLoadingScheduler : AbsPreviewLoadingScheduler() {
        override val subTag: String = TAG

        override fun onShowLoading() {
            containerManager?.showAsLoading()
        }

        override fun onGetStartLoadingTime(): Long? = containerManager?.startLoadingTime

        override fun onDismissLoading(extraObj: Any?) {
            val isDefault = extraObj as? Boolean ?: return
            containerManager?.showAsFileContainer(isDefault)
        }
    }
}