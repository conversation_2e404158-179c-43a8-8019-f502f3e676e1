package com.coloros.filemanager.utils

import android.widget.Toast
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.CustomToast
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test

class CustomToastTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        MyApplication.init(mockk(relaxed = true) {
            every { applicationContext }.returns(this)
            every { getString(any()) }.returns("test")
        })

    }

    @Test
    fun `should verify when simple method without`() {
        mockkStatic(Toast::class)
        every { Toast.makeText(MyApplication.sAppContext, "test", any()) }.returns(mockk(relaxed = true))
        CustomToast.showLong(111)
        CustomToast.showLong("test")
        CustomToast.showShort(111)
        CustomToast.showShort("test")

        every { MyApplication.sAppContext.getString(any()) }.returns("")
        CustomToast.showShort("test")
    }


    @After
    fun afterTests() {
        unmockkAll()
    }
}