/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowJsonReader.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/8/30
 ** Author: JiaBo Lan
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/8/30      1.0     create
 ****************************************************************/
package shadow;

import android.util.JsonReader;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

import java.io.IOException;

@Implements(JsonReader.class)
public class ShadowJsonReader {
    private static boolean sHasNext;
    @Implementation
    public void beginArray() throws IOException {
    }

    @Implementation
    public boolean hasNext() throws IOException {
        return sHasNext;
    }

    @Implementation
    public void endArray() throws IOException {
    }

    public static void setsHasNext(boolean hasNext) {
        sHasNext = hasNext;
    }
}
