/*
 * ********************************************************************
 *  * * Copyright (C), 2021 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  AppMarketUtilsTest.java
 *  * * Description : AppMarketUtilsTest.java
 *  * * Version     : 1.0
 *  * * Date        : 21-12-14 下午5:34
 *  * * Author      : W9010863
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.oplus.filemanager.ad

import android.content.Context
import android.text.TextUtils
import com.cdo.oaps.api.Oaps
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AppMarketUtilsTest {
    lateinit var mContext: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        mContext = mockk(relaxed = true)
    }

    @Test
    fun `should verify when called jumpDetail`() {
        Assert.assertEquals(1, 1)
    }

    @Test
    fun `should verify when called dp2px`() {
        Assert.assertEquals(1, 1)
    }

    @Test
    fun `should verify when called jumpGooglePlayDownload`() {
        Assert.assertFalse(false)
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}