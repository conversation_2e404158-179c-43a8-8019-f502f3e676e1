/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.doc
 ** Version: 1.0
 ** Date: 2021/5/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.doc

import android.net.Uri
import androidx.annotation.Keep

@Keep
interface IDocThumbnailLoader {

    /**
     * 是否对返回的缩略图进行压缩采样及按需重绘，仅针对永中方案启用
     */
    val shallSampleBitmap: Boolean

    /**
     * called for extracting thumbnail form doc
     */
    fun loadThumbnail(
        docThumbnail: DocThumbnail?,
        width: Int,
        height: Int,
        snapshotCallback: IDocThumbnailCallback
    )

    fun cancel(thumbModel: DocThumbnail)

    /**
     * Whether support the solution of current loader.
     */
    fun isSupported(): Boolean

    /**
     * Release related resources when do not use anymore.
     */
    fun release()
}

@Keep
interface IDocThumbnailCallback {
    /**
     * Called with the loaded data if the load succeeded, or with `null` if the load failed.
     */
    fun onDataReady(uri: Uri)

    /**
     * Called when the load fails.
     *
     * @param e a non-null [Exception] indicating why the load failed.
     */
    fun onLoadFailed(e: Throwable)
}

/**
 * The doc thumbnail loading call back only for YoZo solution.
 */
internal interface IYoZoDocThumbnailCallback : IDocThumbnailCallback {

    /**
     * Callback when cancel thumbnail service
     */
    fun onCancel()

    /**
     * Callback when thumbnail service executing timeout
     */
    fun onConnectionTimeout()

    /**
     * Callback when thumbnail service executing end.
     * This callback means thumbnail service is ready to be used for loading new thumbnail.
     */
    fun onFinish()
}