/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DocParentFragment
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2022/10/27       1      create
 ***********************************************************************/
package com.oplus.filemanager.category.document.ui

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.tablayout.COUITab
import com.coui.appcompat.tablayout.COUITabLayout
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseFragmentAdapter
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewOperate
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.helper.uiconfig.type.ScreenFoldConfig
import com.filemanager.common.helper.uiconfig.type.ScreenOrientationConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.OnGetUIInfoListener
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.OpenAnyManager
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.utils.isNetworkAvailable
import com.filemanager.common.view.FeedbackFloatingButton
import com.filemanager.common.view.ViewPagerWrapperForPC
import com.filemanager.common.view.viewpager.RTLViewPager
import com.filemanager.common.wrapper.MediaFileWrapper
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.ad.SubPageAdMgr
import com.oplus.filemanager.category.document.R
import com.oplus.filemanager.category.document.ui.DocumentFragment.Companion.KEY_LAST_OPEN_TIME
import com.oplus.filemanager.interfaze.main.IMain
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.apache.commons.io.FilenameUtils
import java.util.Locale

class DocParentFragment : BaseVMFragment<DocParentViewModel>(), OnBackPressed, NavigationBarView.OnItemSelectedListener,
    COUITabLayout.OnTabSelectedListener, TabActivityListener<MediaFileWrapper>,
    IPreviewListFragment, OnGetUIInfoListener {

    companion object {
        private const val TAG = "DocParentFragment"
        private const val ALL_POSITION = 0
        private const val DOC_POSITION = 1
        private const val XLS_POSITION = 2
        private const val PPT_POSITION = 3
        private const val PDF_POSITION = 4
        private const val OFD_POSITION = 5
        private const val IWORK_POSITION = 6
        private const val XMIND_POSITION = 7
        private const val VISIO_POSITION = 8
        private const val TXT_POSITION = 9
        private const val CAD_POSITION = 10
        private const val PSD_POSITION = 11
        private const val AI_POSITION = 12
        private const val MD_POSITION = 13

        private const val SHOW_FAB_DELAY = 300L
    }

    var mAdManager: SubPageAdMgr? = null
    var mDocParentViewModel: DocParentViewModel? = null

    private var mExternalUri: String? = null
    private var mUri: Uri? = null
    private var mDocArray: ArrayList<String>? = null
    private var mTitle: String? = null
    private var mTempSortType: Int = -1
    private var mSql: String? = null
    private var isChildDisplay = false
    private var mNeedLoadData = false
    private var mFileCount = 0L

    private var mPosition: Int = 0
    private var mRootLayout: ViewGroup? = null
    private var mViewPager: RTLViewPager? = null
    var mAppBarLayout: COUIDividerAppBarLayout? = null
    private var mViewPagerWrapper: ViewPagerWrapperForPC? = null
    private var mToolbar: COUIToolbar? = null
    private var mTabView: COUITabLayout? = null
    var sortEntryView: SortEntryView? = null

    private var mTabTitles: ArrayList<String> = ArrayList()
    private var mPages: ArrayList<DocumentFragment> = ArrayList()
    private var isLastOpenTimeOrder = false
    // 悬浮按钮
    private var createFileFab: FeedbackFloatingButton? = null
    private var canShowFileFab = false
    private var previewOperate: IPreviewOperate? = null

    private var isEmpty: Boolean = false
    private var scrollHelper: DragScrollHelper? = null

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = false
        get() {
            return field.also {
                field = false
            }
        }

    override fun getLayoutResId(): Int {
        return R.layout.document_parent_fragment
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        initArguments()
    }

    private fun initArguments() {
        val bundle = arguments ?: return
        mSql = bundle.getString(KtConstants.P_SQL, null)
        mTitle = bundle.getString(Constants.TITLE, context?.getString(com.filemanager.common.R.string.string_documents))
        mDocArray = bundle.getStringArrayList(Constants.DOCUMENT_FORMAT_ARRAY)
        val uriString = bundle.getString(KtConstants.P_URI, null)
        mUri = if (uriString == null) null else Uri.parse(uriString)
        mExternalUri = bundle.getString(Constants.EXTERNALURI, null)
        isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
        mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
        mFileCount = bundle.getLong(KtConstants.P_CATEGORY_COUNT, 0L)
        isLastOpenTimeOrder = bundle.getBoolean(KEY_LAST_OPEN_TIME, false)
        Log.d(
            TAG, "initArguments mSql = $mSql \n mTitle = $mTitle \n" + " mDocArray = $mDocArray \n mUri = $mUri " +
                    "\n mExternalUri = $mExternalUri \nisChildDisplay:$isChildDisplay mNeedLoadData:$mNeedLoadData" +
                    "\n isLastOpenTimeOrder:$isLastOpenTimeOrder"
        )
    }

    @SuppressLint("RestrictedApi")
    override fun initView(view: View) {
        mRootLayout = view.findViewById(R.id.coordinator_layout)
        mAppBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        if (previewOperate?.isSupportPreview() != true) {
            mToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        }
        toolbar = mToolbar
        mTabView = view.findViewById(com.filemanager.common.R.id.tab_layout)
        mViewPager = view.findViewById(R.id.viewPager)
        mViewPagerWrapper = view.findViewById(R.id.view_pager_wrapper)
        sortEntryView = view.findViewById(com.filemanager.common.R.id.sort_entry_view)
        sortEntryView?.setClickSortListener {
            val menu = ActionMenuItem(view.context, 0, R.id.navigation_sort, 0, 0, "")
            onOptionsItemSelected(menu)
        }
        mViewPagerWrapper?.notifyMainViewPager = object : ((Boolean) -> Unit) {
            override fun invoke(enable: Boolean) {
                baseVMActivity?.let {
                    val mainAction = Injector.injectFactory<IMain>()
                    mainAction?.setViewPagerScrollEnabled(it, enable)
                }
            }
        }
        initFab(view)
    }

    private fun initFab(view: View) {
        createFileFab = view.findViewById(R.id.create_file_fab)
        createFileFab?.setFloatingButtonClickListener {
            Log.d(TAG, "go to create file")
            val newIntent = Intent()
            newIntent.`package` = Constants.OPEN_ANY_PKG_NAME
            newIntent.action = Constants.START_FILE_CREATE_BY_YOZO_SOFT
            kotlin.runCatching {
                this.context?.startActivity(newIntent)
            }.onFailure {
                Log.e(TAG, "initFab e = ${it.message}")
            }
        }
        tryShowFab()
    }

    private fun tryShowFab() {
        /**
         * 1.是一加&内销
         * 2.是平板
         * 3.是否支持CreateFile
         */
        val canShowFab = KtAppUtils.mIsOnePlusDomestic
                && ModelUtils.isTablet()
                && OpenAnyManager.hasCreateFileFunctionInOpenAnyApp()
        Log.d(TAG, "tryShowFab = mIsOnePlusDomestic: ${KtAppUtils.mIsOnePlusDomestic}, isTablet: ${ModelUtils.isTablet()}")
        if (canShowFab) {
            createFileFab?.visibility = View.VISIBLE
            createFileFab?.show()
            Log.d(TAG, "show createFileFab")
        } else {
            Log.d(TAG, "hide createFileFab")
            createFileFab?.visibility = View.GONE
        }
        canShowFileFab = canShowFab
    }

    private fun delayShowFabWhenExitEditMode() {
        if (canShowFileFab) {
            mRootLayout?.postDelayed({
                createFileFab?.visibility = View.VISIBLE
                createFileFab?.show()
            }, SHOW_FAB_DELAY)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        initTabTitles()
        val fragments = childFragmentManager.fragments
        for (i in mTabTitles.indices) {
            initFragment(i, fragments)
        }
        initViewPager()
        initToolbar()
        mDocParentViewModel = ViewModelProvider(this)[DocParentViewModel::class.java]
        startScanModeObserver()
        startSideNavigationStatusObserver()
        if (AdvertManager.isAdEnabled() && SubPageAdMgr.isSupportDocAd()
            && isNetworkAvailable(appContext)//无网络、网络返回超时，不展示广告位
        ) {
            mAdManager = SubPageAdMgr(<EMAIL>)
        }
    }

    private fun initTabTitles() {
        mTabTitles.add(appContext.getString(com.filemanager.common.R.string.total))
        if ((mDocArray != null) && (mDocArray!!.size > 0)) {
            for (name in mDocArray!!) {
                var ext = FilenameUtils.getExtension(name)
                ext = if (TextUtils.isEmpty(ext)) {
                    name.uppercase(Locale.getDefault())
                } else {
                    ext.uppercase(Locale.getDefault())
                }
                mTabTitles.add(ext)
            }
        }
    }

    private fun initFragment(position: Int, fragments: List<Fragment>): Fragment {
        var fragment = if (fragments.isEmpty()) null else fragments[position]
        if (fragment == null) {
            fragment = DocumentFragment()
            val bundle = Bundle().apply {
                putInt(KtConstants.P_TAB_POSITION, position)
                putString(KtConstants.P_EXTENSION, mTabTitles[position])
                putString(KtConstants.P_URI, mUri.toString())
                putString(KtConstants.P_SQL, mSql)
                putInt(Constants.TEMP_SORT_TYPE, mTempSortType)
                putStringArrayList(Constants.DOCUMENT_FORMAT_ARRAY, mDocArray)
                putBoolean(KEY_LAST_OPEN_TIME, isLastOpenTimeOrder)
                if (position == 0) {
                    putBoolean(KtConstants.P_NEED_LOAD_DATA, mNeedLoadData)
                }
            }
            fragment.arguments = bundle
        }

        if (fragment is DocumentFragment) {
            fragment.setToolbarNew(mToolbar, mTitle ?: "")
            fragment.setTabActivityListener(this@DocParentFragment)
            mPages.add(fragment)
        }
        return fragment
    }

    private fun initViewPager() {
        mViewPager?.let {
            it.offscreenPageLimit = mTabTitles.size
            it.adapter = ViewPagerFragmentStateAdapter()
            it.overScrollMode = View.OVER_SCROLL_NEVER
            it.currentItem = 0
        }
    }

    private fun initToolbar() {
        mToolbar?.apply {
            menu.clear()
            isTitleCenterStyle = false
            title = mTitle
            inflateMenu(R.menu.category_doc_menu)
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this, isChildDisplay)
            val edit = mToolbar?.menu?.findItem(R.id.actionbar_edit)
            edit?.isVisible = mFileCount > 0
        }

        baseVMActivity?.apply {
            setSupportActionBar(mToolbar)
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(!isChildDisplay)
                setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }

        if (previewOperate?.isSupportPreview() != true) {
            mRootLayout?.apply {
                setPadding(
                    paddingLeft,
                    COUIPanelMultiWindowUtils.getStatusBarHeight(appContext), paddingRight, paddingBottom
                )
            }
        }
        if ((mTabView != null) && (mViewPager != null)) {
            mTabView?.let {
                it.setupWithViewPager(mViewPager, false)
                it.addOnTabSelectedListener(this)
                it.isUpdateindicatorposition = true
            }
        }
    }

    private fun startScanModeObserver() {
        mDocParentViewModel?.mBrowseModeState?.observe(this) {
            delay { refreshScanModeItemIcon(mNeedSkipAnimation.not()) }
        }
    }

    private fun startSideNavigationStatusObserver() {
        baseVMActivity?.sideNavigationStatus?.observe(this) { status ->
            Log.d(TAG, "sideNavigationStatus observe: $status")
            toolbar?.let {
                refreshScanModeItemIcon()
                setToolbarEditIcon(it, isChildDisplay)
            }
        }
    }

    override fun startObserve() {
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        if (getCurrentFragment()?.isInSelectMode() == true) {
            inflater.inflate(com.filemanager.common.R.menu.menu_edit_mode, menu)
        } else {
            inflater.inflate(R.menu.category_doc_menu, menu)
        }
        mToolbar?.apply {
            refreshScanModeItemIcon()
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this, isChildDisplay)
            val edit = menu.findItem(R.id.actionbar_edit)
            edit?.isVisible = mFileCount > 0
        }
        displayMenuItems(menu)
    }

    override fun onMenuItemSelected(item: MenuItem): Boolean {
        return onOptionsItemSelected(item)
    }

    /**
     * 分屏与否，显示不同的标题栏选项，子屏时，不显示搜索及设置
     */
    private fun displayMenuItems(menu: Menu) {
        menu.findItem(R.id.action_setting)?.isVisible = !isChildDisplay
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return getCurrentFragment()?.onMenuItemSelected(item) ?: super.onOptionsItemSelected(item)
    }

    override fun getFragmentInstance(): Fragment {
        return this
    }

    override fun setFragmentArguments(arguments: Bundle?) {
        this.arguments = arguments
    }

    override fun setPreviewToolbar(toolbar: COUIToolbar?) {
        mToolbar = toolbar
    }

    override fun onResumeLoadData() {
        getCurrentFragment()?.onResumeLoadData()
        val bundle = arguments ?: return
        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
            baseVMActivity?.apply {
                setSupportActionBar(mToolbar)
                baseVMActivity?.supportActionBar?.apply {
                    setDisplayHomeAsUpEnabled(!isChildDisplay)
                    setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
                }
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
    }

    override fun handleDragScroll(event: DragEvent): Boolean {
        if (scrollHelper == null) {
            scrollHelper = DragScrollHelper(getRecyclerView())
        }
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    override fun resetScrollStatus() {
        scrollHelper?.resetDragStatus()
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        val selectedFiles = DragUtils.getSelectedFiles()
        val itemViewList = ArrayList<View>()
        val fileList = getCurrentFragment()?.getFileList()
        val size = fileList?.size ?: return null
        selectedFiles?.forEach { fileBean ->
            val indexOf = fileList.indexOf(fileBean) ?: return null
            if (indexOf >= 0 && indexOf < size) {
                val itemView =
                    getRecyclerView()?.findViewHolderForAdapterPosition(indexOf)?.itemView
                itemView?.let { itemViewList.add(it) }
            }
        }
        return itemViewList
    }

    override fun setNavigateItemAble() {
        activity?.lifecycle?.coroutineScope?.launch(Dispatchers.Main) {
            val selectItems = getCurrentFragment()?.getSelectItems()
            val selectItemSize = selectItems?.size ?: 0
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble((selectItemSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mTabTitles.clear()
        mPages.clear()
    }

    override fun pressBack(): Boolean {
        return (getCurrentFragment() as? OnBackPressed)?.pressBack() == true
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        return getCurrentFragment()?.onNavigationItemSelected(item) ?: false
    }

    override fun onTabSelected(tab: COUITab?) {
        tab?.let {
            mPosition = it.position
            refreshCurrentFragment()
            val opTab = when (mPosition) {
                ALL_POSITION -> "all"
                DOC_POSITION -> "doc"
                XLS_POSITION -> "xls"
                PPT_POSITION -> "ppt"
                PDF_POSITION -> "pdf"
                OFD_POSITION -> "ofd"
                IWORK_POSITION -> "iwork"
                XMIND_POSITION -> "xmind"
                VISIO_POSITION -> "visio"
                TXT_POSITION -> "txt"
                CAD_POSITION -> "cad"
                PSD_POSITION -> "psd"
                AI_POSITION -> "ai"
                MD_POSITION -> "md"
                else -> ""
            }
            OptimizeStatisticsUtil.pageTab(OptimizeStatisticsUtil.DOCUMENT, opTab)
        }
    }

    fun refreshCurrentFragment() {
        getCurrentFragment()?.onResumeLoadData()
    }

    fun getCurrentFragment(): DocumentFragment? {
        return getPositionFragment(mPosition)
    }

    private fun getPositionFragment(position: Int): DocumentFragment? {
        return if (position < mPages.size) {
            mPages[mPosition]
        } else {
            null
        }
    }

    override fun onTabUnselected(tab: COUITab?) {
        Log.i(TAG, "onTabUnselected ")
    }

    override fun onTabReselected(tab: COUITab?) {
        Log.i(TAG, "onTabReselected ")
    }

    override fun initToolbarSelectedMode(
        needInit: Boolean,
        realFileSize: Int,
        selectedFileSize: Int,
        selectItems: ArrayList<MediaFileWrapper>
    ) {
        mToolbar?.let {
            if (needInit && (mTabView?.isInEditMode != true)) {
                it.menu.clear()
                it.isTitleCenterStyle = true
                it.inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
            }
            showEditMode()
            val isSelectAll = (realFileSize == selectedFileSize)
            ToolbarUtil.updateToolbarTitle(it, selectedFileSize, isSelectAll)
            if (baseVMActivity is NavigationInterface) {
                (baseVMActivity as NavigationInterface).setNavigateItemAble((selectedFileSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems))
            }
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
        baseVMActivity?.supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(false)
        }
    }

    private fun showEditMode() {
        mViewPager?.isUserInputEnabled = false
        mTabView?.isEnabled = false
        mViewPagerWrapper?.setEditMode(true)
        createFileFab?.visibility = View.GONE
    }

    override fun initToolbarNormalMode(needInit: Boolean, empty: Boolean) {
        exitEditMode()
        mToolbar?.apply {
            if (needInit) {
                menu.clear()
                isTitleCenterStyle = false
                title = mTitle
                inflateMenu(R.menu.category_doc_menu)
            }
            isEmpty = empty
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this, isChildDisplay)
            menu.findItem(R.id.navigation_sort)?.isEnabled = empty.not()
            menu.findItem(R.id.actionbar_edit)?.isVisible = empty.not()
            displayMenuItems(menu)
            previewOperate?.onToolbarMenuUpdated(menu)
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }

        baseVMActivity?.apply {
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(!isChildDisplay)
                setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
    }

    private fun exitEditMode() {
        mViewPager?.isUserInputEnabled = true
        mTabView?.isEnabled = true
        mViewPagerWrapper?.setEditMode(false)
        delayShowFabWhenExitEditMode()
    }

    override fun refreshScanModeItemIcon(withAnimation: Boolean) {
        mToolbar?.menu?.findItem(R.id.actionbar_scan_mode)?.let {
            val resources = appContext.resources
            val desc: String
            val resId: Int = if (mDocParentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                desc = resources.getString(com.filemanager.common.R.string.palace_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
            } else {
                desc = resources.getString(com.filemanager.common.R.string.list_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
            }
            it.contentDescription = desc
            setScanModeStatue(it, desc, withAnimation, resId)
        }
    }

    override fun updateNeedSkipAnimation(withAnimation: Boolean) {
        mNeedSkipAnimation = withAnimation
    }

    private fun setScanModeStatue(
        toolbar: MenuItem,
        desc: String,
        needSkipAnimation: Boolean,
        resId: Int
    ) {
        if (baseVMActivity?.sideNavigationStatus?.value == KtConstants.LIST_SELECTED_MODE
            && !isEmpty && isChildDisplay
        ) {
            toolbar.setIcon(null)
            toolbar.setTitle(desc)
            toolbar.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
        } else {
            toolbar.setTitle(null)
            if (needSkipAnimation) {
                toolbar.setIcon(resId)
            } else {
                KtAnimationUtil.updateMenuItemWithFadeAnimate(toolbar, resId, baseVMActivity)
            }
            toolbar.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mPages.forEach {
            it.onUIConfigChanged(configList)
        }
        configList.forEach {
            if ((it is ScreenFoldConfig) || (it is ScreenOrientationConfig)) {
                mViewPager?.setCurrentItem(mPosition, false)
                return
            }
        }
    }

    fun disableViewPager() {
        mViewPager?.isUserInputEnabled = false
    }

    fun getCurrentTabPosition(): Int {
        return mPosition
    }

    inner class ViewPagerFragmentStateAdapter : BaseFragmentAdapter(this) {
        override fun createFragment(position: Int): Fragment {
            return mPages[position]
        }

        override fun getItemCount(): Int {
            return mTabTitles.size
        }

        override fun getPageTitle(position: Int): CharSequence {
            return mTabTitles[position]
        }

        override fun getItemPosition(`object`: Any): Int {
            if (`object` is DocumentFragment) {
                return mPages.indexOf(`object`)
            }
            return super.getItemPosition(`object`)
        }
    }

    override fun fromSelectPathResult(requestCode: Int, paths: List<String>?) {
        getCurrentFragment()?.fromSelectPathResult(requestCode, paths)
    }

    override fun updatedLabel() {
        getCurrentFragment()?.onResumeLoadData()
    }

    override fun permissionSuccess() {}

    override fun setCurrentFromOtherSide(currentPath: String) {}

    override fun getCurrentPath(): String {
        return ""
    }

    override fun getScanMode(): Int {
        return mDocParentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
    }

    override fun setScanMode(mode: Int) {
        mDocParentViewModel?.mBrowseModeState?.value = mode
    }

    override fun isSelectionMode(): Boolean {
        return getCurrentFragment()?.isInSelectMode() ?: false
    }

    override fun setPreviewOperate(operate: IPreviewOperate) {
        previewOperate = operate
    }

    override fun backToTop() {
        getCurrentFragment()?.let {
            it.getRecyclerView()?.fastSmoothScrollToTop()
        }
    }

    override fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.P_CHILD_DISPLAY, isChildDisplay)
        mToolbar?.let {
            setToolbarMenuVisible(it, !isChildDisplay)
            setToolbarEditIcon(it, isChildDisplay)
            refreshScanModeItemIcon(true)
        }
        baseVMActivity?.supportActionBar?.apply {
            if (getCurrentFragment()?.isInSelectMode() == true) {
                setDisplayHomeAsUpEnabled(true)
                setHomeAsUpIndicator(com.support.snackbar.R.drawable.coui_menu_ic_cancel)
            } else {
                setDisplayHomeAsUpEnabled(!isChildDisplay)
            }
        }
    }

    private fun setToolbarMenuVisible(toolbar: COUIToolbar, visible: Boolean) {
        toolbar.menu.findItem(R.id.action_setting)?.isVisible = visible
    }

    private fun setToolbarEditIcon(toolbar: COUIToolbar, isChildDisplay: Boolean) {
        toolbar.menu.findItem(R.id.actionbar_edit)?.let {
            if (baseVMActivity?.sideNavigationStatus?.value == KtConstants.LIST_NORMAL_MODE
                && !isEmpty && isChildDisplay
            ) {
                it.setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_edit)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            } else {
                it.setIcon(null)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            }
        }
    }

    override fun checkPermission() {
        baseVMActivity?.let {
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.checkPermission(it)
        }
    }

    override fun setPermissionEmptyVisible(visible: Int) {
        mPages.forEach {
            it.setPermissionEmptyVisible(visible)
        }
    }

    fun previewClickedFile(file: BaseFileBean?, clickFileLiveData: MutableLiveData<BaseFileBean?>?): Boolean {
        return previewOperate?.previewClickedFile(file, clickFileLiveData) ?: false
    }

    fun previewEditedFiles(files: List<BaseFileBean>?): Boolean {
        getCurrentFragment()?.mFileOperateController?.setPreviewOpen(isPreviewOpen())
        return previewOperate?.previewEditedFiles(files) ?: false
    }

    fun listEmptyFile() {
        previewOperate?.listEmptyFile()
    }

    fun isPreviewOpen(): Boolean {
        return previewOperate?.isPreviewOpen() ?: false
    }

    override fun getOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.OperateResultListener? =
        getCurrentFragment()?.mFileOperateController?.pickResultListener()

    override fun exitSelectionMode() {
        getCurrentFragment()?.exitSelectionMode()
    }

    override fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        return getCurrentFragment()?.onSideNavigationClicked(isOpen) ?: false
    }

    override fun updateLeftRightMargin() {
        getCurrentFragment()?.updateLeftRightMargin()
    }

    override fun isEmptyList(): Boolean {
        return getCurrentFragment()?.isEmptyList() ?: false
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_DOC
    }

    override fun getViewModel(): ViewModel? {
        return null
    }

    override fun getRecyclerView(): RecyclerView? {
        return getCurrentFragment()?.getRecyclerView()
    }
}