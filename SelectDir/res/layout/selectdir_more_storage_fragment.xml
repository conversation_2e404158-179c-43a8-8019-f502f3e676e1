<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:splitMotionEvents="false">

    <com.filemanager.common.view.FileManagerRecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
    <ViewStub
        android:id="@+id/common_permission_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/permission_common_view_layout" />

    <com.google.android.material.appbar.COUIDividerAppBarLayout
        android:id="@+id/appbar"
        style="@style/CommonAppBarStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_transparent"
        android:clickable="true"
        app:elevation="@dimen/toolbar_elevation">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:background="@null"
            style="@style/COUIToolBarInAppBarLayoutStyle"
            app:supportTitleTextAppearance="@style/textAppearanceSecondTitle"
            />

    </com.google.android.material.appbar.COUIDividerAppBarLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>