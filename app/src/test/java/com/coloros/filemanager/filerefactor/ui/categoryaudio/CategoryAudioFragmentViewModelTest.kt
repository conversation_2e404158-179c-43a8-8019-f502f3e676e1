/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.categoryaudio
 * * Version     : 1.0
 * * Date        : 2020/3/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.filerefactor.ui.categoryaudio

import android.content.Context
import com.coloros.filemanager.BaseTest
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.wrapper.AudioFileWrapper
import com.oplus.dropdrag.SelectionTracker
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.category.audiovideo.ui.CategoryAudioFragmentViewModel
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class CategoryAudioFragmentViewModelTest : BaseTest() {
    private val mCategoryAudioFragmentViewModel =
        CategoryAudioFragmentViewModel()

    @Before
    override fun setUp() {
        mContext = RuntimeEnvironment.application
        val file1 = AudioFileWrapper()
        file1.mId = 1
        val file2 = AudioFileWrapper()
        file2.mId = 2
        val file3 = AudioFileWrapper()
        file3.mId = 3
        val list = ArrayList<AudioFileWrapper>()
        list.add(file1)
        list.add(file2)
        list.add(file3)
        mCategoryAudioFragmentViewModel.mUiState.value = BaseUiModel(list, mCategoryAudioFragmentViewModel.mModeState,
                ArrayList(), HashMap())
    }


    @Test
    @Ignore("Ignore temporarily, solve oppoPallDomesticAplLevelAllDebugUnitTest error")
    @Throws(Exception::class)
    fun should_clickToolbarSelectAll_when_view_model_fun_clickToolbarSelectAll() {
        mCategoryAudioFragmentViewModel.mUiState.value?.mSelectedList?.clear()
        mCategoryAudioFragmentViewModel.clickToolbarSelectAll()
        assertEquals(mCategoryAudioFragmentViewModel.mUiState.value?.mFileList?.size, mCategoryAudioFragmentViewModel.mUiState.value?.mSelectedList?.size)
        mCategoryAudioFragmentViewModel.clickToolbarSelectAll()
        assertEquals(0, mCategoryAudioFragmentViewModel.mUiState.value?.mSelectedList?.size)
        mCategoryAudioFragmentViewModel.mUiState.value?.mSelectedList?.add(mCategoryAudioFragmentViewModel.mUiState.value?.mFileList?.get(0)?.mId
                ?: 0)
        mCategoryAudioFragmentViewModel.clickToolbarSelectAll()
        assertEquals(mCategoryAudioFragmentViewModel.mUiState.value?.mFileList?.size, mCategoryAudioFragmentViewModel.mUiState.value?.mSelectedList?.size)
    }

    @Test
    fun testGetRealFileSize() {
        mCategoryAudioFragmentViewModel.mUiState.value = null
        assertEquals(0, mCategoryAudioFragmentViewModel.getRealFileSize())

        val file1 = AudioFileWrapper()
        file1.mId = 1
        val file2 = AudioFileWrapper()
        file2.mId = 2
        val file3 = AudioFileWrapper()
        file3.mId = 3
        val list = ArrayList<AudioFileWrapper>()
        list.add(file1)
        list.add(file2)
        list.add(file3)
        val fakeUiModel = BaseUiModel(list, mCategoryAudioFragmentViewModel.mModeState, ArrayList(), HashMap())
        mCategoryAudioFragmentViewModel.mUiState.value = fakeUiModel
        mockkObject(AdvertManager.Companion)
        every { AdvertManager.getAdViewCount(any()) } returns 1
        assertEquals(2, mCategoryAudioFragmentViewModel.getRealFileSize())
    }

    @Test
    fun testClickScanModeItem() {
        val context = mockk<Context>()
        mockkObject(ConfigSharedPreferenceUtils.Companion)
        every { ConfigSharedPreferenceUtils.putInt(any(), any()) } returns Unit
        mockkStatic(StatisticsUtils::class)
        every { StatisticsUtils.onCommon(any(), any(), any() as HashMap<String, String>) } returns Unit
        assertEquals(KtConstants.SCAN_MODE_LIST, mCategoryAudioFragmentViewModel.mBrowseModeState.value)
        mCategoryAudioFragmentViewModel.clickScanModeItem(context)
        assertEquals(KtConstants.SCAN_MODE_GRID, mCategoryAudioFragmentViewModel.mBrowseModeState.value)
        mCategoryAudioFragmentViewModel.clickScanModeItem(context)
        assertEquals(KtConstants.SCAN_MODE_LIST, mCategoryAudioFragmentViewModel.mBrowseModeState.value)
    }

    @Test
    fun testGetRecyclerViewScanMode() {
        assertEquals(SelectionTracker.LAYOUT_TYPE.LIST, mCategoryAudioFragmentViewModel.getRecyclerViewScanMode())
        mCategoryAudioFragmentViewModel.mBrowseModeState.value = KtConstants.SCAN_MODE_GRID
        assertEquals(SelectionTracker.LAYOUT_TYPE.GRID, mCategoryAudioFragmentViewModel.getRecyclerViewScanMode())
    }

    @After
    override fun tearDown() {
        mContext = null
    }
}