/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : SignUtilKtTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/29 19:28
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/29       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.data.utils

import com.allawn.cryptography.algorithm.HkdfUtil
import com.allawn.cryptography.algorithm.HmacUtil
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.Assert
import org.junit.Test

class SignUtilKtTest {

    @Test
    fun testSignInfo() {
        val signInfo = SignInfo()
        println(hexSignInfo(signInfo))
    }

    @Test
    fun testHex() {
        val hexString = BYTE_ARRAY.toHex()
        Assert.assertEquals(SIGN_WITHOUT_UPPER, hexString)
    }

    @Test
    fun testGenerateKeyWhenThrowError() {
        mockkStatic(HkdfUtil::class)
        every { HkdfUtil.hkdfWithSha256(any(), any(), any(), any()) }.throws(Exception())
        val errorKey = generateKey(DUID, SALT.toByteArray(), PACKAGE_NAME)
        Assert.assertTrue(errorKey.isEmpty())
        unmockkStatic(HkdfUtil::class)
    }

    @Test
    fun testGenerateKeyWhenReturnNull() {
        mockkStatic(HkdfUtil::class)
        every { HkdfUtil.hkdfWithSha256(any(), any(), any(), any()) }.returns(null)
        val errorKey = generateKey(DUID, SALT.toByteArray(), PACKAGE_NAME)
        Assert.assertNull(errorKey)
        unmockkStatic(HkdfUtil::class)
    }

    @Test
    fun testGenerateKeyWhenReturnKey() {
        mockkStatic(HkdfUtil::class)
        every { HkdfUtil.hkdfWithSha256(any(), any(), any(), any()) }.returns(ByteArray(32))
        val errorKey = generateKey(DUID, SALT.toByteArray(), PACKAGE_NAME)
        Assert.assertFalse(errorKey.isEmpty())
        unmockkStatic(HkdfUtil::class)
    }

    @Test
    fun testHmacSignInfoWithGenerateKeyWhenThrowError() {
        mockkStatic(HmacUtil::class)
        val signInfoWithNullString = SignInfo(APP_KEY, TIME_STAMP, INVOKE_METHOD_NAME, JSON_STR, STRING_A_NULL, SECRET)
        every { HmacUtil.hmacSha256(any(), any()) }.throws(Exception())
        val secret = hmacSignInfoWithGenerateKey(signInfoWithNullString, ByteArray(32))
        Assert.assertEquals("", secret)
        unmockkStatic(HmacUtil::class)
    }

    @Test
    fun testHmacSignInfoWithGenerateKeyWhenReturnNull() {
        mockkStatic(HmacUtil::class)
        val signInfoWithNullString = SignInfo(APP_KEY, TIME_STAMP, INVOKE_METHOD_NAME, JSON_STR, STRING_A_NULL, SECRET)
        every { HmacUtil.hmacSha256(any(), any()) }.returns(null)
        val secret = hmacSignInfoWithGenerateKey(signInfoWithNullString, ByteArray(32))
        Assert.assertEquals("", secret)
        unmockkStatic(HmacUtil::class)
    }

    @Test
    fun testHmacSignInfoWithGenerateKey() {
        mockkStatic(HmacUtil::class)
        val signInfoWithNullString = SignInfo(APP_KEY, TIME_STAMP, INVOKE_METHOD_NAME, JSON_STR, STRING_A_NULL, SECRET)
        every { HmacUtil.hmacSha256(any(), any()) }.returns(BYTE_ARRAY)
        val secret = hmacSignInfoWithGenerateKey(signInfoWithNullString, ByteArray(32))
        Assert.assertEquals(SIGN, secret)
        unmockkStatic(HmacUtil::class)
    }
    @Test
    fun testSplicingSignInfoByJoinToString() {
        val signInfoWithNullStringA = SignInfo(APP_KEY, TIME_STAMP, INVOKE_METHOD_NAME, JSON_STR, STRING_A_NULL, SECRET)
        val joinStringWithNullStringA = splicingSignInfoByJoinToString(signInfoWithNullStringA)
        Assert.assertEquals(SPLICING_STRING_WITH_NULL_STRING_A, joinStringWithNullStringA)
        val signInfo = SignInfo(APP_KEY, TIME_STAMP, INVOKE_METHOD_NAME, JSON_STR, STRING_A, SECRET)
        val joinString = splicingSignInfoByJoinToString(signInfo)
        Assert.assertEquals(SPLICING_STRING, joinString)
    }

    @Test
    fun testHexSignInfoWithSha256() {
        val signInfoWithNullStringA = SignInfo(APP_KEY, TIME_STAMP, INVOKE_METHOD_NAME, JSON_STR, STRING_A_NULL, SECRET)
        val result = hexSignInfo(signInfoWithNullStringA)
        Assert.assertEquals(HEX_SIGN_WITH_NULL_STRING_A, result)
    }

    @Test
    fun testFormatUrlMapToLower() {
        val paramMap = hashMapOf<String, String>()
        paramMap[KEY_FILE_ID] = VALUE_FILE_ID
        paramMap[KEY_RELATION_ID] = VALUE_RELATION_ID
        paramMap[KEY_DISPLAY_DETAIL] = VALUE_DISPLAY_DETAIL
        val resultToLower = formatUrlMap(paramMap, false, keyToLower = true)
        Assert.assertEquals(FORMAT_URL_MAP_RESULT_LOWERCASE, resultToLower)
    }

    @Test
    fun testFormatUrlMap() {
        val paramMap = hashMapOf<String, String>()
        paramMap[KEY_FILE_ID] = VALUE_FILE_ID
        paramMap[KEY_RELATION_ID] = VALUE_RELATION_ID
        paramMap[KEY_DISPLAY_DETAIL] = VALUE_DISPLAY_DETAIL
        val result = formatUrlMap(paramMap, false, keyToLower = false)
        Assert.assertEquals(FORMAT_URL_MAP_RESULT, result)
    }

    companion object {
        private const val DUID = "D96ADF9E6977411BA6B5FBA0A42242B2EC7D424979CC9C51B5FD65094B891062"
        private const val SALT = "0ab7b49091ef892b7cde3e5cbebff3ca7ba908861ee94b2b37e95a0aed2118f6"
        private const val PACKAGE_NAME = "com.coloros.filemanager"
        private const val SIGN = "1C33BDAD40EE03C33F58A2291FB99B09B4FA53E3E127CB2B7B9547D0A1114F8A"
        private const val SIGN_WITHOUT_UPPER = "1c33bdad40ee03c33f58a2291fb99b09b4fa53e3e127cb2b7b9547d0a1114f8a"
        private val BYTE_ARRAY = byteArrayOf(
            28, 51, -67, -83, 64, -18, 3, -61, 63, 88, -94, 41, 31, -71, -101, 9, -76,
            -6, 83, -29, -31, 39, -53, 43, 123, -107, 71, -48, -95, 17, 79, -118
        )
        private const val APP_KEY = "app_key"
        private const val TIME_STAMP = "1659580580310"
        private const val INVOKE_METHOD_NAME = "owork-service"
        private const val JSON_STR = ""
        private val STRING_A_NULL: String? = null
        private const val STRING_A = "stringA"
        private const val SECRET = "secret"
        private const val SPLICING_STRING_WITH_NULL_STRING_A = "app_key&owork-service&&null&secret&1659580580310"
        private const val SPLICING_STRING = "app_key&owork-service&&stringA&secret&1659580580310"
        private const val HEX_SIGN_WITH_NULL_STRING_A =
            "c0efa73355ea469a8f2cdf34876ecb98e3312f208d282c2c6b4c0323f2ee7671"
        private const val KEY_FILE_ID = "fileId"
        private const val KEY_RELATION_ID = "kingDocRelationId"
        private const val KEY_DISPLAY_DETAIL = "displayDetail"
        private const val VALUE_FILE_ID = "x/tGh4wdQAySmXbitFKjSw=="
        private const val VALUE_RELATION_ID = "43426b0d49ba08ecddcb18293e00279e"
        private const val VALUE_DISPLAY_DETAIL = "true"
        private val FORMAT_URL_MAP_RESULT_LOWERCASE = "${KEY_DISPLAY_DETAIL.lowercase()}=$VALUE_DISPLAY_DETAIL&" +
                "${KEY_FILE_ID.lowercase()}=$VALUE_FILE_ID&" +
                "${KEY_RELATION_ID.lowercase()}=$VALUE_RELATION_ID"

        private const val FORMAT_URL_MAP_RESULT =
            "$KEY_DISPLAY_DETAIL=$VALUE_DISPLAY_DETAIL&$KEY_FILE_ID=$VALUE_FILE_ID&$KEY_RELATION_ID=$VALUE_RELATION_ID"
    }
}