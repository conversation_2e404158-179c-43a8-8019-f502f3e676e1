/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: SettingActivity
 * * Description: the activity for settings
 * * Version: 1.0
 * * Date : 2020/8/31
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/8/31       1.0         the activity for settings
 ****************************************************************/
package com.filemanager.setting.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.WindowUtils
import com.filemanager.setting.R
import com.filemanager.setting.utils.FlexibleWindowUtil
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.oplus.flexiblewindow.FlexibleWindowManager

class SettingActivity : BaseVMActivity() {
    companion object {
        private const val TAG = "SettingActivity"

        fun start(activity: Activity?) {
            try {
                activity?.apply {
                    val intent = Intent(activity, SettingActivity::class.java)
                    if (FeatureCompat.isApplicableForFlexibleWindow) {
                        val flexibleWindowPosition = FlexibleWindowUtil.checkOrientation(activity.window.decorView as ViewGroup)
                        Log.d(TAG, "flexibleWindowPosition: $flexibleWindowPosition")
                        PreferencesUtils.put(key = FlexibleWindowUtil.FLEXIALE_WINDOW_POSITION, value = flexibleWindowPosition)
                    }
                    FlexibleWindowUtil.startActivityForFlexibleWindow(activity, intent)
                }
            } catch (e: Exception) {
                Log.w(TAG, "start: ${e.message}")
            }
        }
    }
    private lateinit var mAppBarLayout: COUIDividerAppBarLayout
    private lateinit var mToolbar: COUIToolbar
    private lateinit var mContainerView: View

    override fun getLayoutResId(): Int {
        return R.layout.activity_setting
    }

    override fun initView() {
        registerVmChangedReceiver(null)
        mAppBarLayout = findViewById(R.id.appbar)
        mToolbar = findViewById(R.id.toolbar)
        mContainerView = findViewById(R.id.fragment_container)
        mToolbar.title = resources.getString(com.filemanager.common.R.string.set_button_text)
        mToolbar.isTitleCenterStyle = false
        setSupportActionBar(mToolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        initToolbar()
        FlexibleWindowUtil.setBarBackgroundColor(mToolbar, mAppBarLayout, window.decorView, resources.configuration, this)
        FlexibleWindowUtil.setActionCloseFlexibleActivity(window.decorView, this)
    }

    private fun getViewTopPadding(): Int {
        return StatusBarUtil.getStatusBarHeight() + mToolbar.height
    }

    @SuppressLint("PrivateResource")
    private fun initToolbar() {
        val res = MyApplication.appContext.resources
        var appBarTopPadding = StatusBarUtil.getStatusBarHeight()
        var navigationIcon = R.drawable.coui_back_arrow
        if (FeatureCompat.isApplicableForFlexibleWindow &&
            WindowUtils.isMiddleAndLargeScreen(this) && !isInMultiWindowMode
            && FlexibleWindowManager.isFlexibleActivitySuitable(resources.configuration)) {
            navigationIcon = com.support.snackbar.R.drawable.coui_menu_ic_cancel
            appBarTopPadding =
                res.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_16dp)
            mToolbar.post {
                mContainerView.setPadding(
                    mContainerView.paddingLeft,
                    mToolbar.height + res.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_12dp),
                    mContainerView.paddingRight,
                    0
                )
                initFragment()
            }
        } else {
            mToolbar.post {
                mContainerView.setPadding(
                    mContainerView.paddingLeft,
                    getViewTopPadding(),
                    mContainerView.paddingRight,
                    0
                )
                initFragment()
            }
        }
        mToolbar.setNavigationIcon(navigationIcon)
        mAppBarLayout.setPadding(0, appBarTopPadding, 0, 0)
    }

    private fun initFragment() {
        var fragment = supportFragmentManager.findFragmentByTag(TAG)
        if ((fragment == null) || (fragment !is SettingFragment)) {
            fragment = SettingFragment()
        }
        val ft = supportFragmentManager.beginTransaction()
        ft.replace(R.id.fragment_container, fragment, TAG)
        ft.show(fragment)
        ft.commitAllowingStateLoss()
    }

    override fun startObserve() {}

    override fun initData() {}

    override fun refreshCurrentPage(action: String?, data: String?) {}

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
       super.onUIConfigChanged(configList)
        Log.d(TAG, "onUIConfigChanged")
        initToolbar()
        FlexibleWindowUtil.setBarBackgroundColor(mToolbar, mAppBarLayout, window.decorView, resources.configuration, this)
    }
}