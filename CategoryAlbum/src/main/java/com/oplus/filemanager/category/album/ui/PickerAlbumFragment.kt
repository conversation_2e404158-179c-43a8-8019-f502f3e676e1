/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.album
 * * Version     : 1.0
 * * Date        : 2020/6/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.album.ui

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.LIST_SELECTED_MODE
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_DECORATION_ALBUM
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.IPickerManager
import com.filemanager.common.interfaces.OnPickerRecyclerItemClickListener
import com.filemanager.common.picker.IPickerFragment
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.viewholder.listener.PickerNormalListTouchClickListener.Companion.CLICK_TO_SELECT
import com.filemanager.common.viewholder.listener.PickerNormalListTouchClickListener.Companion.CLICK_TO_SHOW
import com.filemanager.common.wrapper.ImageFileWrapper
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.PikerFileOperateController
import com.oplus.filemanager.category.album.R
import com.oplus.filemanager.category.album.adapter.AlbumAdapter
import com.oplus.filemanager.category.album.adapter.PickerAlbumAdapter
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.router.RouterUtil.CATEGORY_IMAGE_DISPLAY

class PickerAlbumFragment : AlbumFragment(), IPickerFragment, OnPickerRecyclerItemClickListener {
    private val mPickerSortPopupController by lazy { SortPopupController(lifecycle) }
    private var sortEntryView: SortEntryView? = null
    private var pickerManager: IPickerManager? = null
    private var mTempSortType = -1
    private var tempSortDesc = -1
    override fun createViewModel(): AlbumFragmentViewModel {
        val vm = ViewModelProvider(this)[AlbumFragmentViewModel::class.java].apply {
            isPickerPage = true
            mPickerMultipleType = pickerManager?.getFinalPickerMimeType()
            mModeState.listModel.value = LIST_SELECTED_MODE
        }
        mFileOperateController = PikerFileOperateController(lifecycle, CategoryHelper.CATEGORY_IMAGE,
            vm, SortHelper.FILE_TIME_REVERSE_ORDER).also {
            it.setResultListener(FileOperatorListenerImpl(vm))
            it.setInterceptor(vm)
        }
        return vm
    }

    override fun getLayoutResId(): Int {
        return R.layout.picker_album_fragment
    }
    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            pickerManager = activity as? IPickerManager
        }
        mAdapter?.let {
            it.mChoiceMode = pickerManager?.supportMultipleSelection() == true
        }
        val bundle = arguments ?: return
        mTempSortType = bundle.getInt(Constants.TEMP_SORT_TYPE, -1)
    }

    override fun getAdapter(activity: FragmentActivity): AlbumAdapter {
        return PickerAlbumAdapter(activity, <EMAIL>)
    }

    override fun initSelectionTracker() {
    }

    override fun initData(savedInstanceState: Bundle?) {
        fragmentViewModel?.pickerSelectedKeyList = pickerManager?.getSelectedKeys()
        super.initData(savedInstanceState)
        mAdapter?.let {
            it.setOnPickerRecyclerItemClickListener(this@PickerAlbumFragment)
        }
        if (actionCheckPermission().not()) {
            sortEntryView?.setFileCount(0)
        }
        startObservePickerSelected()
    }
    @SuppressLint("NotifyDataSetChanged")
    override fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getPickerGridItemCount(activity, scanMode, GRID_ITEM_DECORATION_ALBUM, isPickerPage = true)
        mLayoutManager?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mAdapter?.apply {
            mScanViewModel = scanMode
            notifyDataSetChanged()
        }
    }
    private fun startObservePickerSelected() {
        pickerManager?.getSelectedLiveData()?.observe(this) {
            fragmentViewModel?.apply {
                pickerSelectedKeyList = pickerManager?.getSelectedKeys()
                val selectKeys = pickerSelectedKeyList?.filter {
                    uiState?.value?.keyMap?.get(it) != null
                }
                uiState?.value?.selectedList?.let {
                    it.clear()
                    it.addAll(ArrayList(selectKeys))
                }
                Log.d(TAG, "startObservePickerSelected selectKeys.size = ${selectKeys?.size}")
                uiState.value?.apply {
                    uiState.value = this
                }
            }
        }
    }

    @SuppressLint("RestrictedApi")
    override fun initView(view: View) {
        super.initView(view)
        if (previewOperate?.isSupportPreview() != true) {
            rootView?.apply {
                setPadding(paddingLeft, 0, paddingRight, paddingBottom)
            }
        }
        sortEntryView = view.findViewById(com.filemanager.common.R.id.sort_entry_view)
        if (mTempSortType != -1) {
            sortEntryView?.setSortOrder(mTempSortType, tempSortDesc == 0)
        } else {
            sortEntryView?.setDefaultOrder(getSortKey())
        }
        sortEntryView?.setClickSortListener {
            val menu = ActionMenuItem(view.context, 0, R.id.navigation_sort, 0, 0, "")
            onMenuItemSelected(menu)
        }
    }
    override fun onCategoryAudioModel(
        categoryAudioUiModel: BaseUiModel<ImageFileWrapper>,
        viewModule: AlbumFragmentViewModel
    ) {
        sortEntryView?.setFileCount(viewModule.getRealFileSize())
        if (categoryAudioUiModel.fileList.isEmpty()) {
            sortEntryView?.visibility = View.GONE
        } else {
            sortEntryView?.visibility = View.VISIBLE
        }
        super.onCategoryAudioModel(categoryAudioUiModel, viewModule)
    }
    override fun onListModelChanged(
        viewModule: AlbumFragmentViewModel,
        value: Int
    ) {
        //picker 无需状态变化监听
    }

    @SuppressLint("RestrictedApi")
    override fun onMenuItemSelected(item: MenuItem): Boolean {
        if (null == item || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            android.R.id.home -> {
                baseVMActivity?.onBackPressed()
                true
            }
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startPickerGlobalSearch(activity, CategoryHelper.CATEGORY_IMAGE)
                true
            }
            R.id.actionbar_scan_mode -> {
                fragmentViewModel?.clickScanModeItem(baseVMActivity)
                true
            }
            R.id.navigation_sort -> {
                baseVMActivity?.let {
                    val bundle = Bundle()
                    bundle.putInt(Constants.TEMP_SORT_TYPE, mTempSortType)
                    bundle.putInt(Constants.TEMP_SORT_DESC, tempSortDesc)
                    bundle.putBoolean(SortModeUtils.FROM_PICKER_PAGE, true)
                    bundle.putString(SortModeUtils.RECORD_CATEGORY_MODE, getSortKey())
                    val anchorView: View? = view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
                    mPickerSortPopupController.showSortPopUp(
                        it,
                        anchorView,
                        bundle,
                        object : SelectItemListener {

                            override fun onDismiss() {
                                sortEntryView?.rotateArrow()
                            }

                            override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                                if (flag) {
                                    sortEntryView?.setSortOrder(sortMode, isDesc)
                                    fragmentViewModel?.sortReload()
                                }
                            }
                        })
                }
                true
            }
            else -> false
        }
    }

    override fun refreshSelectToolbar(toolbar: COUIToolbar) {
    }

    override fun pressBack(): Boolean {
        return false
    }

    override fun getMenuLayoutResId(): Int {
        return R.menu.picker_album_menu
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            if (scanMode == KtConstants.SCAN_MODE_GRID) {
                refreshScanModeAdapter(scanMode)
            }
            mPickerSortPopupController.hideSortPopUp()
            if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
            }
            updateLeftRightMargin()
        }
    }

    override fun onItemClick(view: View, position: Int, type: Int) {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if ((position < 0) or (position >= (uiModel.mFileList?.size ?: 0))) {
                Log.d(TAG, "onItemClick: position IndexOutOfBounds")
                return
            }
            val baseFile: BaseFileBean = uiModel.mFileList?.get(position) ?: return
            if (pickerManager?.getPickerAction() == Intent.ACTION_OPEN_DOCUMENT_TREE) {
                //do nothing
            } else {
                when (type) {
                    CLICK_TO_SELECT -> clickToSelect(baseFile)
                    CLICK_TO_SHOW -> clickToShow(baseFile)
                    else -> { }
                }
            }
        }
    }
    private fun clickToShow(baseFile: BaseFileBean) {
        activity?.let {
            val mediaImgIds: ArrayList<String> = ArrayList()
            // 判断当前点击是否是媒体库中的图片
            if (FileMediaHelper.isImgAndInMedia(baseFile)) {
                // 获取当前视图中的图片文件，按照排序顺序生成一个media id列表，传给相册
                fragmentViewModel?.uiState?.value?.fileList?.forEach { file ->
                    if (file.mLocalType == MimeTypeHelper.IMAGE_TYPE) {
                        mediaImgIds.add(file.mId.toString())
                    }
                }
                // 限制列表大小
                FileMediaHelper.limitNumberOfFileList(baseFile, mediaImgIds)
            }
            mFileOperateController?.onFileClick(it, baseFile, null, mediaImgIds)
        }
    }
    private fun clickToSelect(baseFile: BaseFileBean) {
        if (pickerManager?.supportMultipleSelection() == true) {
            fragmentViewModel?.uiState?.value?.apply {
                val key = baseFile.mData?.toLowerCase()?.hashCode() ?: 0
                if (selectedList.contains(key).not()) {
                    if (pickerManager?.checkSelectedCounts() != true) {
                        return
                    }
                    pickerManager?.addFile(key, baseFile)
                } else {
                    pickerManager?.removeFile(key, baseFile)
                }
                mAdapter?.let {
                    it.setData(
                        fileList as ArrayList<ImageFileWrapper>,
                        selectedList
                    )
                }
            }
            pickerManager?.onEditCountChange()
        } else {
            pickerManager?.pickerSingleFile(baseFile)
        }
    }
    override fun onItemLongClick(view: View, position: Int) {
    }

    override fun getFragmentCategoryType(): Int {
        return CATEGORY_IMAGE_DISPLAY
    }

    override fun getSortKey(): String {
        return SortRecordModeFactory.getPickerAlbumKey()
    }

    override fun attachPCConnect(): Boolean {
        return false
    }
}