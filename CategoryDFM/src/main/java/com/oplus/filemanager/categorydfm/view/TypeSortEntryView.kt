/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DocSortEntryView
 ** Description : 文件类型排序的View：显示两个排序：一个为文件类型，一个为时间，大小，名称，类型等排序
 ** Version     : 1.0
 ** Date        : 2024/02/29 10:48
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/02/29       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.categorydfm.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.annotation.PluralsRes
import androidx.annotation.VisibleForTesting
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.Log
import com.oplus.filemanager.categorydfm.R

class TypeSortEntryView : ConstraintLayout {

    companion object {
        private const val TAG = "TypeSortEntryView"
        private const val DESC_ROTATE_ANGLE = 0.0f
        private const val ASC_ROTATE_ANGLE = 180.0f
    }

    var anchorView: View? = null

    @VisibleForTesting
    var countTv: TextView? = null

    @VisibleForTesting
    var typeOrderTv: TextView? = null

    @VisibleForTesting
    var typeOrderImg: ImageView? = null

    @VisibleForTesting
    var sortOrderTv: TextView? = null

    @VisibleForTesting
    var sortOrderImg: ImageView? = null

    @VisibleForTesting
    var sortClickListener: View.OnClickListener? = null

    @VisibleForTesting
    var typeClickListener: View.OnClickListener? = null

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle) {
        initView()
        initEvent()
        showTypeSort(false)
    }

    @VisibleForTesting
    fun initView() {
        inflate(context, R.layout.layout_type_sort_entry, this)

        countTv = findViewById(R.id.sort_entry_file_count_text)
        anchorView = findViewById(R.id.anchor_view)
        typeOrderImg = findViewById(R.id.type_order_img)
        typeOrderTv = findViewById(R.id.type_order_text)
        sortOrderImg = findViewById(R.id.sort_entry_order_img)
        sortOrderTv = findViewById(R.id.sort_entry_order_text)

        countTv?.let {
            COUIChangeTextUtil.adaptFontSize(it, COUIChangeTextUtil.G2)
        }
        typeOrderTv?.let {
            COUIChangeTextUtil.adaptFontSize(it, COUIChangeTextUtil.G2)
        }
        sortOrderTv?.let {
            COUIChangeTextUtil.adaptFontSize(it, COUIChangeTextUtil.G2)
        }
        updateLeftRightMargin()
    }

    @VisibleForTesting
    fun initEvent() {
        typeOrderImg?.setOnClickListener {
            onClickTypeEvent(it)
        }
        typeOrderTv?.setOnClickListener {
            onClickTypeEvent(it)
        }
        sortOrderImg?.setOnClickListener {
            onClickSortEvent(it)
        }
        sortOrderTv?.setOnClickListener {
            onClickSortEvent(it)
        }
    }

    @VisibleForTesting
    fun onClickTypeEvent(view: View) {
        this.typeClickListener?.onClick(view)
    }

    @VisibleForTesting
    fun onClickSortEvent(view: View) {
        this.sortClickListener?.onClick(view)
    }

    fun setFileCount(count: Int, @PluralsRes pluralsRes: Int = com.filemanager.common.R.plurals.search_result_count) {
        countTv?.text = context.resources.getQuantityString(pluralsRes, count, count)
    }

    fun showTypeSort(show: Boolean = true) {
        Log.d(TAG, "showTypeSort $show")
        typeOrderImg?.isVisible = show
        typeOrderTv?.isVisible = show
    }

    fun setDefaultOrder(recordMode: String, type: String = "") {
        val sort = SortModeUtils.getSharedSortMode(MyApplication.sAppContext, recordMode)
        val isDesc = SortModeUtils.getSharedSortOrder(recordMode)
        setSortOrder(sort, isDesc)
        setTypeOrder(type)
    }

    fun setTypeOrder(type: String) {
        Log.d(TAG, "setTypeOrder type:$type")
        post {
            typeOrderTv?.text = type
        }
    }

    fun rotateTypeArrow() {
    }

    fun setSortOrder(sortType: Int, isDesc: Boolean) {
        Log.d(TAG, "setSortOrder sortType:$sortType isDesc:$isDesc")
        post {
            sortOrderTv?.text = getSortType(sortType)
            val imgRes = getOrderImg(isDesc)
            sortOrderImg?.setImageResource(imgRes)
        }
    }

    fun rotateArrow() {
    }

    @VisibleForTesting
    fun getSortType(sortType: Int): String {
        val id = when (sortType) {
            SortHelper.FILE_NAME_ORDER -> com.filemanager.common.R.string.sort_by_name
            SortHelper.FILE_TYPE_ORDER -> com.filemanager.common.R.string.sort_by_type
            SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER -> com.filemanager.common.R.string.sort_by_size
            SortHelper.FILE_TIME_REVERSE_ORDER -> com.filemanager.common.R.string.modify_time
            SortHelper.FILE_TIME_DELETE_ORDER -> com.filemanager.common.R.string.sort_by_remain_time
            SortHelper.FILE_LAST_OPEN_TIME_ORDER -> com.filemanager.common.R.string.sort_by_last_open_time
            else -> com.filemanager.common.R.string.sort_by_name
        }
        return context.getString(id)
    }


    @DrawableRes
    fun getOrderImg(isDesc: Boolean): Int {
        return if (isDesc) {
            com.filemanager.common.R.drawable.icon_sort_desc
        } else {
            com.filemanager.common.R.drawable.icon_sort_asc
        }
    }

    fun setClickSortListener(sortListener: OnClickListener) {
        this.sortClickListener = sortListener
    }

    fun setClickTypeListener(typeListener: OnClickListener?) {
        this.typeClickListener = typeListener
    }

    fun updateLeftRightMargin() {
        countTv?.updateLayoutParams<LayoutParams> {
            this.marginStart = FileImageVHUtils.getListLeftMargin()
        }
        sortOrderImg?.updateLayoutParams<LayoutParams> {
            this.marginEnd = FileImageVHUtils.getListRightMargin()
        }
    }
}