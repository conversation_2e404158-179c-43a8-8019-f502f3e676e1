/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : TypeSortEntryViewTest
 ** Description : TypeSortEntryView Unit Test
 ** Version     : 1.0
 ** Date        : 2024/04/09 16:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/04/09       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.categorydfm.view

import android.content.Context
import android.content.res.Resources
import android.view.View
import android.view.View.OnClickListener
import android.widget.ImageView
import android.widget.TextView
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.oplus.filemanager.categorydfm.R
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class TypeSortEntryViewTest {

    private lateinit var context: Context
    private lateinit var entryView: TypeSortEntryView
    private lateinit var countTv: TextView
    private lateinit var typeOrderTv: TextView
    private lateinit var typeOrderImg: ImageView
    private lateinit var sortOrderTv: TextView
    private lateinit var sortOrderImg: ImageView

    @Before
    fun setup() {
        context = mockk()
        entryView = mockk(relaxed = true)
        countTv = mockk()
        typeOrderTv = mockk()
        typeOrderImg = mockk()
        sortOrderTv = mockk()
        sortOrderImg = mockk()
        every { entryView.context }.returns(context)
        mockkStatic(View::class)
        every { View.inflate(any(), any(), any()) }.returns(mockk())
        mockkObject(MyApplication)
        every { MyApplication.sAppContext }.returns(context)
        justRun { typeOrderImg.setImageResource(any()) }
        justRun { sortOrderImg.setImageResource(any()) }
    }

    @After
    fun teardown() {
        unmockkStatic(View::class)
        unmockkObject(MyApplication)
    }

    @Test
    fun should_notNull_when_initView() {
        every { entryView.findViewById<TextView>(R.id.sort_entry_file_count_text) }.returns(countTv)
        every { entryView.findViewById<View>(R.id.anchor_view) }.returns(mockk())
        every { entryView.findViewById<ImageView>(R.id.type_order_img) }.returns(typeOrderImg)
        every { entryView.findViewById<TextView>(R.id.type_order_text) }.returns(typeOrderTv)
        every { entryView.findViewById<ImageView>(R.id.sort_entry_order_img) }.returns(sortOrderImg)
        every { entryView.findViewById<TextView>(R.id.sort_entry_order_text) }.returns(sortOrderTv)
        every { entryView.initView() }.answers { callOriginal() }
        mockkStatic(COUIChangeTextUtil::class)
        justRun { COUIChangeTextUtil.adaptFontSize(any(), any()) }

        entryView.initView()
        Assert.assertNotNull(entryView.countTv)
        Assert.assertNotNull(entryView.typeOrderImg)
        Assert.assertNotNull(entryView.typeOrderTv)
        Assert.assertNotNull(entryView.sortOrderImg)
        Assert.assertNotNull(entryView.sortOrderTv)

        unmockkStatic(COUIChangeTextUtil::class)
    }

    @Test
    fun should_call_setOnClickListener_when_initEvent() {
        every { entryView.typeOrderImg }.returns(typeOrderImg)
        every { entryView.typeOrderTv }.returns(typeOrderTv)
        every { entryView.sortOrderImg }.returns(sortOrderImg)
        every { entryView.sortOrderTv }.returns(sortOrderTv)
        every { entryView.initEvent() }.answers { callOriginal() }

        justRun { typeOrderImg.setOnClickListener(any()) }
        justRun { typeOrderTv.setOnClickListener(any()) }
        justRun { sortOrderImg.setOnClickListener(any()) }
        justRun { sortOrderTv.setOnClickListener(any()) }

        entryView.initEvent()
        verify { typeOrderImg.setOnClickListener(any()) }
        verify { typeOrderTv.setOnClickListener(any()) }
        verify { sortOrderImg.setOnClickListener(any()) }
        verify { sortOrderTv.setOnClickListener(any()) }
    }

    @Test
    fun should_call_onClick_when_onClickTypeEvent() {
        every { entryView.onClickTypeEvent(any()) }.answers { callOriginal() }
        val listener = mockk<View.OnClickListener>()
        justRun { listener.onClick(any()) }
        every { entryView.typeClickListener }.returns(listener)

        entryView.onClickTypeEvent(mockk())
        verify { listener.onClick(any()) }
    }

    @Test
    fun should_call_onClick_when_onClickSortEvent() {
        every { entryView.onClickSortEvent(any()) }.answers { callOriginal() }
        val listener = mockk<View.OnClickListener>()
        justRun { listener.onClick(any()) }
        every { entryView.sortClickListener }.returns(listener)

        entryView.onClickSortEvent(mockk())
        verify { listener.onClick(any()) }
    }

    @Test
    fun should_call_setText_when_setFileCount() {
        every { entryView.countTv }.returns(countTv)
        justRun { countTv.text = any<String>() }
        every { entryView.setFileCount(any(), any()) }.answers { callOriginal() }
        val resource = mockk<Resources>()
        every { context.resources }.returns(resource)
        every { resource.getQuantityString(any(), any(), any()) }.answers {
            val count = secondArg<Int>()
            "共 $count 项"
        }
        entryView.setFileCount(10)
        verify { countTv.text = "共 10 项" }
    }

    @Test
    fun should_call_isVisible_when_showTypeSort() {
        every { entryView.showTypeSort(any()) }.answers { callOriginal() }
        every { entryView.typeOrderImg }.returns(typeOrderImg)
        every { entryView.typeOrderTv }.returns(typeOrderTv)
        justRun { typeOrderImg.visibility = any() }
        justRun { typeOrderTv.visibility = any() }

        entryView.showTypeSort(true)
        verify { typeOrderImg.visibility = View.VISIBLE }
        verify { typeOrderTv.visibility = View.VISIBLE }

        entryView.showTypeSort(false)
        verify { typeOrderImg.visibility = View.GONE }
        verify { typeOrderTv.visibility = View.GONE }
    }

    @Test
    fun should_call_setTypeOrder_when_setDefaultOrder() {
        mockkStatic(SortModeUtils::class)
        every { SortModeUtils.getSharedSortMode(any(), any()) }.returns(SortHelper.FILE_LAST_OPEN_TIME_ORDER)
        every { SortModeUtils.getSharedSortOrder(any()) }.returns(true)
        every { entryView.setDefaultOrder(any(), any()) }.answers { callOriginal() }
        justRun { entryView.setSortOrder(any(), any()) }
        justRun { entryView.setTypeOrder(any()) }

        entryView.setDefaultOrder("dfm", "time")
        verify { entryView.setSortOrder(any(), true) }
        verify { entryView.setTypeOrder("time") }

        unmockkStatic(SortModeUtils::class)
    }

    @Test
    fun should_setText_when_setTypeOrder() {
        every { entryView.setTypeOrder(any()) }.answers { callOriginal() }
        every { entryView.post(any()) }.answers {
            val run = firstArg<Runnable>()
            run.run()
            true
        }
        every { entryView.typeOrderTv }.returns(typeOrderTv)
        justRun { typeOrderTv.text = any() }

        entryView.setTypeOrder("全部")
        verify { typeOrderTv.text = "全部" }
    }

    @Test
    fun should_setText_when_setSortOrder() {
        every { entryView.setSortOrder(any(), any()) }.answers { callOriginal() }
        every { entryView.post(any()) }.answers {
            val run = firstArg<Runnable>()
            run.run()
            true
        }
        every { entryView.sortOrderTv }.returns(sortOrderTv)
        every { entryView.sortOrderImg }.returns(sortOrderImg)
        every { entryView.getSortType(any()) }.returns("名称")
        every { entryView.getOrderImg(any()) }.answers { callOriginal() }
        justRun { sortOrderTv.text = any() }
        justRun { sortOrderImg.rotation = any() }

        entryView.setSortOrder(1, true)
        verify { sortOrderTv.text = "名称" }
        verify { sortOrderImg.setImageResource(com.filemanager.common.R.drawable.icon_sort_desc) }
    }

    @Test
    fun should_return_string_when_getSortType() {
        every { context.getString(com.filemanager.common.R.string.sort_by_name) }.returns("名称")
        every { context.getString(com.filemanager.common.R.string.sort_by_type) }.returns("类型")
        every { context.getString(com.filemanager.common.R.string.sort_by_size) }.returns("大小")
        every { context.getString(com.filemanager.common.R.string.modify_time) }.returns("修改时间")
        every { context.getString(com.filemanager.common.R.string.sort_by_remain_time) }.returns("删除时间")
        every { context.getString(com.filemanager.common.R.string.sort_by_last_open_time) }.returns("打开时间")
        every { entryView.getSortType(any()) }.answers { callOriginal() }
        var type = entryView.getSortType(SortHelper.FILE_NAME_ORDER)
        Assert.assertEquals(type, "名称")

        type = entryView.getSortType(SortHelper.FILE_TYPE_ORDER)
        Assert.assertEquals(type, "类型")

        type = entryView.getSortType(SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER)
        Assert.assertEquals(type, "大小")

        type = entryView.getSortType(SortHelper.FILE_TIME_REVERSE_ORDER)
        Assert.assertEquals(type, "修改时间")

        type = entryView.getSortType(SortHelper.FILE_TIME_DELETE_ORDER)
        Assert.assertEquals(type, "删除时间")

        type = entryView.getSortType(SortHelper.FILE_LAST_OPEN_TIME_ORDER)
        Assert.assertEquals(type, "打开时间")

        type = entryView.getSortType(-1)
        Assert.assertEquals(type, "名称")
    }

    @Test
    fun should_return_int_when_getOrderImg() {
        every { entryView.getOrderImg(any()) }.answers { callOriginal() }
        var imgRes = entryView.getOrderImg(true)
        Assert.assertEquals(com.filemanager.common.R.drawable.icon_sort_desc, imgRes)

        imgRes = entryView.getOrderImg(false)
        Assert.assertEquals(com.filemanager.common.R.drawable.icon_sort_asc, imgRes)
    }

    @Test
    fun should_notNull_when_setClickSortListener() {
        every { entryView.setClickSortListener(any()) }.answers { callOriginal() }
        every { entryView.sortClickListener = any() }.answers { callOriginal() }
        every { entryView.sortClickListener }.answers { callOriginal() }
        entryView.setClickSortListener(OnClickListener {})
        Assert.assertNotNull(entryView.sortClickListener)
    }

    @Test
    fun should_notNull_when_setClickTypeListener() {
        every { entryView.setClickTypeListener(any()) }.answers { callOriginal() }
        every { entryView.typeClickListener = any() }.answers { callOriginal() }
        every { entryView.typeClickListener }.answers { callOriginal() }
        entryView.setClickTypeListener(OnClickListener {})
        Assert.assertNotNull(entryView.typeClickListener)
    }
}