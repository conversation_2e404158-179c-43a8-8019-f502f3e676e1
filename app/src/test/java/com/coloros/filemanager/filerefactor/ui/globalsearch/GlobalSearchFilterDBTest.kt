/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.globalsearch
 * * Version     : 1.0
 * * Date        : 2020/07/30
 * * Author      : w9007122
 * *
 * * ---------------------Revision filter: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.filerefactor.ui.globalsearch

import com.coloros.filemanager.BaseTest
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterCondition
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConditionManager
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants.FILTER_FROM
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants.FILTER_TIME
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Ignore
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class GlobalSearchFilterDBTest : BaseTest() {
    @Before
    override fun setUp() {
        mContext = RuntimeEnvironment.application
    }

    @After
    override fun tearDown() {
        super.tearDown()
    }

    @Ignore
    fun should_verify_clear_filter() {
        val clearCount = FilterConditionManager.clearFilter()
        assertTrue("clear clearFilter fail", -1 != clearCount)
    }

    @Ignore
    fun should_verify_load_filter() {
        FilterConditionManager.clearFilter()

        val fc1 = FilterCondition(FILTER_TIME, "").parseFilterItems(0b111)
        val fc2 = FilterCondition(FILTER_FROM, "").parseFilterItems(0b111)

        FilterConditionManager.insertFilter(fc1)
        FilterConditionManager.insertFilter(fc2)

        val ls = FilterConditionManager.loadFilter()

        assertEquals("load filter fail", 2, ls.size)
    }

    @Ignore
    fun should_verify_update_filter() {
        FilterConditionManager.clearFilter()

        val fc = FilterCondition(FILTER_TIME, "").parseFilterItems(0b111)
        FilterConditionManager.insertFilter(fc)

        fc.parseFilterItems(0b11111)
        FilterConditionManager.updateFilter(fc)

        val ls = FilterConditionManager.loadFilter()
        assertEquals("update  filter fail", 1, ls.size)
    }

    @Ignore
    fun should_verify_delete_filter() {
        FilterConditionManager.clearFilter()

        val fc = FilterCondition(FILTER_TIME, "").parseFilterItems(0b111)
        FilterConditionManager.insertFilter(fc)

        val count = FilterConditionManager.removeFilter(listOf(fc))
        assertEquals("delete history fail", count, 1)
    }
}
