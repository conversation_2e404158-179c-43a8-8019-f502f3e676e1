/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - ScanOperationTest.java
 ** Description: Test case for Scan Operation
 ** Version: 1.0
 ** Date : 2020/04/20
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/04/20    1.0     create
 ****************************************************************/

package com.coloros.filemanager.recyclebin.operation

import androidx.activity.ComponentActivity
import com.coloros.filemanager.BaseTest
import com.filemanager.recyclebin.RecycleFileManager
import com.filemanager.common.base.BaseFileBean
import com.filemanager.recyclebin.operation.ScanOperation
import com.filemanager.recyclebin.operation.listener.ScanOperationListener
import junit.framework.Assert.assertFalse
import junit.framework.Assert.assertTrue
import org.junit.After
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import shadow.ShadowViewHelper
import shadow.ShadowVolumeEnvironment

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
@Config(shadows = [ShadowViewHelper::class, ShadowVolumeEnvironment::class])
class ScanOperationTest : BaseTest() {
    private lateinit var mActivity: ComponentActivity

    @Before
    override fun setUp() {
        super.setUp()
        mActivity = Robolectric.buildActivity(ComponentActivity::class.java).get()
    }

    @After
    override fun tearDown() {
        super.tearDown()
    }

    @Test
    fun should_vertify_when_addPaths_isNull() {
        val progressListener = ScanOperationListener(mActivity, null, RecycleFileManager.RecycleFileParameters())
        val scanOperation = ScanOperation(mActivity, progressListener)
        assertFalse(scanOperation.addPaths(null))
        assertTrue(scanOperation.addPaths(ArrayList<String>()))
        assertFalse(scanOperation.addPaths(ArrayList<String>()))
    }

    @Test
    fun should_vertify_when_addFileBeans_isNull() {
        val progressListener = ScanOperationListener(mActivity, null, RecycleFileManager.RecycleFileParameters())
        val scanOperation = ScanOperation(mActivity, progressListener)
        assertFalse(scanOperation.addFileBeans(null))
        assertTrue(scanOperation.addFileBeans(ArrayList<BaseFileBean>()))
        assertFalse(scanOperation.addFileBeans(ArrayList<BaseFileBean>()))
    }
}