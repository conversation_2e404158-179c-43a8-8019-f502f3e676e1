<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/pinned_header_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/parent_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/recent_title_item_height">

        <com.coui.appcompat.rotateview.COUIRotateView
            android:id="@+id/parent_expend"
            android:layout_width="@dimen/recent_icon_size"
            android:layout_height="@dimen/recent_icon_size"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:clickable="false"
            android:focusable="false"
            app:supportRotateType="couiRotateAloneX"
            android:src="@drawable/line_arrow" />

        <TextView
            android:id="@+id/parent_left_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_toStartOf="@id/parent_expend"
            android:textAppearance="@style/couiTextButtonM"
            android:textColor="?attr/couiColorLabelPrimary" />
    </RelativeLayout>
</LinearLayout>