/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : WeChatUtilsTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/14 14:25
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/14       1.0      create
 **********************************************************************/
package com.oplus.filemanager.wechatsdk

import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.SdkUtils
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class WeChatUtilsTest {

    private lateinit var context: Context
    private lateinit var api: IWXAPI

    @Before
    fun setup() {
        MockKAnnotations.init()
        context = mockk(relaxed = true)
        every { context.applicationContext }.returns(context)
        MyApplication.init(context)
        every { context.registerReceiver(any(), any()) }.returns(Intent())
        every { context.registerReceiver(any(), any(), any<Int>()) }.returns(Intent())
        justRun { context.unregisterReceiver(any()) }

        api = mockk()
        every { api.registerApp(any()) }.returns(true)
        justRun { api.unregisterApp() }
        mockkStatic(WXAPIFactory::class)
        every { WXAPIFactory.createWXAPI(any(), any()) }.returns(api)
        WeChatUtils.api = api
    }

    @After
    fun teardown() {
        unmockkAll()
    }

    @Test
    fun should_call_once_registerApp_when_register() {
        mockkStatic(SdkUtils::class)
        every { SdkUtils.isAtLeastT() }.returns(true)

        WeChatUtils.register(MyApplication.sAppContext)
        verify { api.registerApp(any()) }

        WeChatUtils.register(MyApplication.sAppContext)
        verify(atMost = 1) { api.registerApp(any()) }

        WeChatUtils.unregister(MyApplication.sAppContext)
        unmockkStatic(SdkUtils::class)
    }

    @Test
    fun should_call_registerReceiver_when_register() {
        mockkStatic(SdkUtils::class)
        every { SdkUtils.isAtLeastT() }.returns(false)
        WeChatUtils.register(MyApplication.sAppContext)
        verify { context.registerReceiver(any(), any()) }

        every { SdkUtils.isAtLeastT() }.returns(true)
        WeChatUtils.unregister(MyApplication.sAppContext)
        WeChatUtils.register(MyApplication.sAppContext)
        verify { context.registerReceiver(any(), any(), any()) }

        WeChatUtils.unregister(MyApplication.sAppContext)
        unmockkStatic(SdkUtils::class)
    }

    @Test
    fun should_call_once_unregisterApp_when_unregister() {
        WeChatUtils.unregister(context)
        verify(inverse = true) { api.unregisterApp() }

        WeChatUtils.register(context)
        WeChatUtils.unregister(context)
        verify { api.unregisterApp() }

        WeChatUtils.unregister(context)
        verify(atMost = 1) { api.unregisterApp() }
    }

    @Test
    fun should_call_once_unregisterReceiver_when_unregister() {
        WeChatUtils.unregister(context)
        verify(inverse = true) { context.unregisterReceiver(any()) }

        WeChatUtils.register(context)
        WeChatUtils.unregister(context)
        verify { context.unregisterReceiver(any()) }

        WeChatUtils.unregister(context)
        verify(atMost = 1) { context.unregisterReceiver(any()) }
    }

    @Test
    fun testIsWXInstalledWhenGetPackageInfoThrowError() {
        every { context.packageManager }.returns(mockk(relaxed = true) {
            every { getApplicationInfo(Constants.WECHAT_PACKAGE_NAME, 0) }.throws(Exception())
            every { getPackageInfo(Constants.WECHAT_PACKAGE_NAME, 0) }.throws(Exception())
        })
        val isWxInstalled = WeChatUtils.isWxInstalled(context)
        Assert.assertFalse(isWxInstalled)
    }

    @Test
    fun testIsWXInstalledWhenGetApplicationInfoThrowError() {
        val packageInfo = mockk<PackageInfo>()
        every { context.packageManager }.returns(mockk(relaxed = true) {
            every { getApplicationInfo(Constants.WECHAT_PACKAGE_NAME, 0) }.throws(Exception())
            every { getPackageInfo(Constants.WECHAT_PACKAGE_NAME, 0) }.returns(packageInfo)
        })
        val isWxInstalled = WeChatUtils.isWxInstalled(context)
        Assert.assertFalse(isWxInstalled)
    }

    @Test
    fun testIsWXInstalledWhenGetApplicationInfoDisabled() {
        val packageInfo = mockk<PackageInfo>()
        val applicationInfo = mockk<ApplicationInfo>(relaxed = true)
        applicationInfo.enabled = false
        every { context.packageManager }.returns(mockk(relaxed = true) {
            every { getApplicationInfo(Constants.WECHAT_PACKAGE_NAME, 0) }.returns(applicationInfo)
            every { getPackageInfo(Constants.WECHAT_PACKAGE_NAME, 0) }.returns(packageInfo)
        })
        val isWxInstalled = WeChatUtils.isWxInstalled(context)
        Assert.assertFalse(isWxInstalled)
    }

    @Test
    fun testIsEXInstalledWhenGetApplicationInfoEnable() {
        val packageInfo = mockk<PackageInfo>()
        val applicationInfo = mockk<ApplicationInfo>(relaxed = true)
        applicationInfo.enabled = true
        every { context.packageManager }.returns(mockk(relaxed = true) {
            every { getApplicationInfo(Constants.WECHAT_PACKAGE_NAME, 0) }.returns(applicationInfo)
            every { getPackageInfo(Constants.WECHAT_PACKAGE_NAME, 0) }.returns(packageInfo)
        })
        val isWxInstalled = WeChatUtils.isWxInstalled(context)
        Assert.assertTrue(isWxInstalled)
    }
}