<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackgroundWithCard"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:splitMotionEvents="false">

    <com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller
        android:id="@+id/fastScroller"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:common_track_marginBottom="@dimen/ftp_text_margin_bottom"
        app:common_track_marginEnd="@dimen/base_album_fastscroller_margin_end"
        app:common_track_marginTop="@dimen/base_album_recyclerview_padding_top">

        <com.filemanager.common.view.FileManagerRecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller>

    <ViewStub
        android:id="@+id/common_permission_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/permission_common_view_layout" />

    <include layout="@layout/appbar_with_divider_layout_secondary" />
    <View
        android:id="@+id/viewAnchor"
        android:layout_width="@dimen/dimen_1dp"
        android:layout_height="@dimen/dimen_1dp"
        android:layout_gravity="end"
        />

</androidx.coordinatorlayout.widget.CoordinatorLayout>