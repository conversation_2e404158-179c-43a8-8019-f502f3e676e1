<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/content_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="@dimen/coui_popup_list_window_min_width"
        android:minHeight="@dimen/coui_popup_list_window_item_min_height"
        android:orientation="horizontal"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:paddingTop="@dimen/coui_popup_list_window_item_padding_top_and_bottom"
        android:paddingBottom="@dimen/coui_popup_list_window_item_padding_top_and_bottom">

        <TextView
            android:id="@+id/sort_item_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:fontFamily="sans-serif-regular"
            android:lineSpacingMultiplier="1.2"
            android:maxWidth="@dimen/coui_popup_list_window_item_max_width"
            android:textAlignment="viewStart"
            android:textColor="?attr/couiColorLabelPrimary"
            android:textFontWeight="400"
            android:layout_marginEnd="@dimen/dimen_4dp"
            android:singleLine="true"
            android:textSize="16sp"
            tools:ignore="RtlCompat" />

        <ImageView
            android:id="@+id/sort_item_img"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/coui_popup_list_window_item_icon_margin_left"
            android:src="@drawable/ic_order_desc" />
    </LinearLayout>

    <View
        android:id="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/coui_popup_list_divider_height"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginLeft="@dimen/coui_popup_list_window_item_title_margin_with_no_icon"
        android:layout_marginRight="@dimen/coui_popup_list_window_item_title_margin_with_no_icon"
        android:background="?attr/couiColorDivider"
        android:forceDarkAllowed="false" />

</LinearLayout>