/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - VideoFilePreviewImpl.kt
 * Description:
 *     The implementation to play video to preview it.
 *
 * Version: 1.0
 * Date: 2024-09-19
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-09-19   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.video

import android.content.Context
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.audio.AudioFilePreviewImpl.PreparedResult
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayErrorListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayPreparedListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayProgressUpdateListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayStateChangedListener
import com.oplus.filemanager.preview.core.AbsPreviewLoadingScheduler
import com.oplus.filemanager.preview.utils.MediaPlayerHelper
import com.oplus.filemanager.preview.utils.getFileUri
import com.oplus.filemanager.preview.video.IPreviewVideoPlayer.OnVideoSizeChangedListener
import com.oplus.filemanager.preview.widget.PreviewVideoPlayBar.OnClickPlayButtonListener
import com.oplus.filemanager.preview.widget.PreviewVideoPlayBar.OnClickVolumeButtonListener
import com.oplus.filemanager.preview.widget.PreviewVideoPlayBar.OnSeekPlayProgressListener

internal class VideoFilePreviewImpl(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner,
    private val previewModel: VideoPreviewViewModel
) : IVideoFilePreview, OnPlayPreparedListener, OnPlayProgressUpdateListener,
    OnClickPlayButtonListener, OnClickVolumeButtonListener, OnSeekPlayProgressListener,
    OnVideoSizeChangedListener, OnPlayErrorListener, OnPlayStateChangedListener,
    DefaultLifecycleObserver {

    private companion object {
        private const val TAG = "VideoFilePreviewImpl"
        private const val IS_MUTE_VOLUME = 0.0000001f
    }

    private val loadingScheduler = PreviewLoadingScheduler()
    private val onVideoLoaded = Observer<BaseFileBean?> { playVideoByPlayer(it) }

    private var containerManager: IVideoPreviewContainerManager? = null
    private var isError = false
    private var isPlayingWhenUiOnPause = false

    override fun release() {
        if (!isError) {
            previewModel.obtainVideoPlayer(context).setVideoTextureView(null)
        }
        previewModel.releaseLoader(onVideoLoaded)
        containerManager?.videoPlayerSuit?.videoPlayBar?.release()
        containerManager = null
        lifecycleOwner.lifecycle.removeObserver(this)
    }

    override fun attachToContainer(containerManager: IVideoPreviewContainerManager) {
        lifecycleOwner.lifecycle.addObserver(this)
        isError = false
        this.containerManager = containerManager
        containerManager.videoPlayerSuit.videoPlayBar.apply {
            setOnClickPlayButtonListener(this@VideoFilePreviewImpl)
            setOnClickVolumeButtonListener(this@VideoFilePreviewImpl)
            setOnSeekPlayProgressListener(this@VideoFilePreviewImpl)
        }
        loadingScheduler.requireToShowLoading()
        containerManager.defaultContainer.setFileIcon(
            KtThumbnailHelper.getClassifyResId(MimeTypeHelper.VIDEO_TYPE)
        )
        val player = previewModel.obtainVideoPlayer(context)
            .setOnPlayPreparedListener(this)
            .setOnPlayStateChangedListener(this)
            .setOnPlayProgressUpdateListener(this)
            .setOnVideoSizeChangedListener(this)
            .setOnPlayErrorListener(this)
            .setVideoTextureView(containerManager.videoPlayerSuit.videoSurface)
        previewModel.loadPreviewFile(lifecycleOwner, onVideoLoaded)
        if (player.isPlaying()) {
            preparePlayBar(player, true)
        }
    }

    private fun playVideoByPlayer(videoFile: BaseFileBean?) {
        val fileName = videoFile?.mDisplayName
        containerManager?.apply {
            nameContainer.text = fileName
            defaultContainer.setFileName(fileName)
        }
        containerManager?.fillFileDetailInfo(videoFile)
        val uri = videoFile?.getFileUri() ?: kotlin.run {
            Log.e(TAG, "playVideoByPlayer: ERROR! No video file to play")
            return
        }
        val player = previewModel.obtainVideoPlayer(context)
        player.setPrepareTimeout(MediaPlayerHelper.getPrepareTimeout(videoFile))
            .setVideoSizeDecodeTimeout(MediaPlayerHelper.getVideoSizeDecodeTimeout(videoFile))
            .prepareAndPlay(uri)
    }

    override fun onPrepared(isPlayStarted: Boolean) {
        val player = previewModel.obtainVideoPlayer(context)
        loadingScheduler.dismissLoading(PreparedResult(false, isPlayStarted))
        preparePlayBar(player, isPlayStarted)
        isPlayingWhenUiOnPause = isPlayStarted
    }

    override fun onPlayStateChanged(isPlaying: Boolean) {
        val playBar = containerManager?.videoPlayerSuit?.videoPlayBar ?: return
        Log.d(TAG, "onPlayStatChanged: isPlaying=$isPlaying")
        playBar.setVideoPlayState(isPlaying)
    }

    private fun preparePlayBar(player: IPreviewVideoPlayer, isPlayStarted: Boolean) {
        val playBar = containerManager?.videoPlayerSuit?.videoPlayBar ?: return
        playBar.setVideoPlayState(isPlayStarted)
        val volume = player.getVolume()
        Log.d(TAG, "preparePlayBar: isPlayStarted=$isPlayStarted, volume=$volume")
        playBar.setVideoVolumeState(volume <= IS_MUTE_VOLUME)
    }

    override fun onProgressUpdate(position: Long, duration: Long) {
        val playBar = containerManager?.videoPlayerSuit?.videoPlayBar ?: return
        playBar.setDuration(duration)
        playBar.setProgress(position)
    }

    override fun onVideoSizeChanged(width: Int, height: Int) {
        containerManager?.videoPlayerSuit?.setVideoSize(width, height)
    }

    override fun onSeekPlayProgress(position: Long, isTouching: Boolean) {
        previewModel.obtainVideoPlayer(context).seekTo(position, isTouching)
    }

    override fun onClickPlayButton(requireToPause: Boolean) {
        val player = previewModel.obtainVideoPlayer(context)
        if (requireToPause) {
            player.pause()
        } else {
            player.resume()
        }
    }

    override fun onClickVolumeButton(requireToMute: Boolean) {
        val player = previewModel.obtainVideoPlayer(context)
        if (requireToMute) {
            player.setVolume(0f)
        } else {
            player.setVolume(1f)
        }
    }

    override fun onError(errorType: Int, errorCode: Int, extra: String?) {
        Log.e(TAG, "onError: ERROR! type=$errorType, code=$errorCode, extra=$extra")
        isError = true
        loadingScheduler.dismissLoading(PreparedResult(true))
        previewModel.releaseVideoPlayer()
    }

    override fun onResume(owner: LifecycleOwner) {
        val player = previewModel.obtainVideoPlayer(context)
        Log.d(TAG, "onResume: isPlaying=$isPlayingWhenUiOnPause")
        if (isPlayingWhenUiOnPause) {
            player.resume()
        }
    }

    override fun onPause(owner: LifecycleOwner) {
        val player = previewModel.obtainVideoPlayer(context)
        isPlayingWhenUiOnPause = player.isPlaying()
        Log.d(TAG, "onPause: isPlaying=$isPlayingWhenUiOnPause")
        if (isPlayingWhenUiOnPause) {
            player.pause()
        }
    }

    private inner class PreviewLoadingScheduler : AbsPreviewLoadingScheduler() {
        override val subTag: String = TAG

        override fun onShowLoading() {
            containerManager?.showAsLoading()
        }

        override fun onGetStartLoadingTime(): Long? =
            containerManager?.startLoadingTime

        override fun onDismissLoading(extraObj: Any?) {
            val preparedResult = extraObj as? PreparedResult ?: return
            containerManager?.showAsFileContainer(preparedResult.isDefault)
            if (!preparedResult.isDefault && preparedResult.isPlayStarted) {
                val player = previewModel.obtainVideoPlayer(context)
                player.resume()
            }
        }
    }
}