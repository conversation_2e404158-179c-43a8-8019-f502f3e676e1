/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SelectionStatStringHelper.kt
 * Description:
 *     The helper to format selections stat string.
 *
 * Version: 1.0
 * Date: 2024-10-22
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-10-22   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection.utils

import android.content.Context
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat.Companion.TYPE_APK
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat.Companion.TYPE_ARCHIVE
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat.Companion.TYPE_AUDIO
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat.Companion.TYPE_DIR
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat.Companion.TYPE_DOC
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat.Companion.TYPE_IMAGE
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat.Companion.TYPE_OTHER
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat.Companion.TYPE_VIDEO

internal class SelectionStatStringHelper(private val context: Context) {

    private companion object {
        private const val TAG = "SelectionStatStringHelper"

        private const val SELECTION_2_ITEMS = 2
        private const val SELECTION_3_ITEMS = 3
        private const val SELECTION_4_ITEMS = 4
        private const val SELECTION_5_ITEMS = 5
        private const val SELECTION_6_ITEMS = 6
        private const val SELECTION_7_ITEMS = 7
        private const val SELECTION_8_ITEMS = 8

        private val statTypeOrder = intArrayOf(
            TYPE_DIR,
            TYPE_IMAGE,
            TYPE_VIDEO,
            TYPE_AUDIO,
            TYPE_DOC,
            TYPE_APK,
            TYPE_ARCHIVE,
            TYPE_OTHER
        )
    }

    fun formatFilesCount(statData: SelectionFilesStat): CharSequence =
        context.resources.getQuantityString(
            com.filemanager.common.R.plurals.preview_selection_all_count,
            statData.selectionCount,
            statData.selectionCount
        )

    fun formatStatInfo(statData: SelectionFilesStat): CharSequence {
        val sumSizeStr = Utils.byteCountToDisplaySize(statData.sumSize)
        val typesStatStr = formatTypesStat(statData)
        return Utils.formatDetail(context, sumSizeStr, typesStatStr)
    }

    private fun formatTypesStat(statData: SelectionFilesStat): String {
        if (statData.typesCountMap.isEmpty()) {
            return ""
        }
        val typeCountStrMap = mutableMapOf<Int, String>()
        statTypeOrder.forEach { type ->
            val count = statData.getCountForType(type)
            if (count > 0) {
                typeCountStrMap[type] = formatTypeCount(type, count)
            }
        }
        return joinTypeCountStrings(typeCountStrMap)
    }

    private fun formatTypeCount(
        @SelectionFilesStat.StateFileTypes type: Int,
        count: Int
    ): String {
        val strId = when (type) {
            TYPE_DIR -> com.filemanager.common.R.plurals.preview_selection_folder_count
            TYPE_IMAGE -> com.filemanager.common.R.plurals.preview_selection_image_count
            TYPE_VIDEO -> com.filemanager.common.R.plurals.preview_selection_video_count
            TYPE_AUDIO -> com.filemanager.common.R.plurals.preview_selection_audio_count
            TYPE_DOC -> com.filemanager.common.R.plurals.preview_selection_doc_count
            TYPE_APK -> com.filemanager.common.R.plurals.preview_selection_apk_count
            TYPE_ARCHIVE -> com.filemanager.common.R.plurals.preview_selection_archive_count
            TYPE_OTHER -> com.filemanager.common.R.plurals.preview_selection_other_count
            else -> throw IllegalArgumentException("$TAG: incorrect file stat type $type")
        }
        return context.resources.getQuantityString(strId, count, count)
    }

    @Suppress("SpreadOperator")
    private fun joinTypeCountStrings(typeCountStrMap: Map<Int, String>): String {
        if (typeCountStrMap.isEmpty()) {
            return ""
        }
        val joinedList = mutableListOf<String>()
        statTypeOrder.forEach { type ->
            typeCountStrMap[type]?.let { joinedList.add(it) }
        }
        if (joinedList.isEmpty()) {
            return ""
        }
        if (joinedList.size == 1) {
            return joinedList.first()
        }
        val joinFormat = when (joinedList.size) {
            SELECTION_2_ITEMS -> com.filemanager.common.R.string.preview_selection_2_type
            SELECTION_3_ITEMS -> com.filemanager.common.R.string.preview_selection_3_type
            SELECTION_4_ITEMS -> com.filemanager.common.R.string.preview_selection_4_type
            SELECTION_5_ITEMS -> com.filemanager.common.R.string.preview_selection_5_type
            SELECTION_6_ITEMS -> com.filemanager.common.R.string.preview_selection_6_type
            SELECTION_7_ITEMS -> com.filemanager.common.R.string.preview_selection_7_type
            SELECTION_8_ITEMS -> com.filemanager.common.R.string.preview_selection_8_type
            else -> throw IllegalArgumentException("$TAG: incorrect join count ${joinedList.size}")
        }
        return context.getString(joinFormat, *(joinedList.toTypedArray()))
    }
}