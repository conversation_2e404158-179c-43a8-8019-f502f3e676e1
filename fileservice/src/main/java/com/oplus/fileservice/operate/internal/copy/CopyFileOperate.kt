/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CopyFileOperate
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/7 20:35
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/7       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.copy

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.fileutils.checkDestStorageSpace
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.fileservice.operate.internal.ConflictPolicy
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.internal.NotifyMediaScannerBatchAction
import com.oplus.fileservice.operate.internal.utils.OnFileOperateListener
import com.oplus.fileservice.operate.internal.utils.converterFileBean
import java.io.File

open class CopyFileOperate(
    private val context: Context
) : BaseCopyCutOperate() {

    @VisibleForTesting
    var mHasSkipFile = false
    private var mHasSuccessFile = false
    private var mIsOperateDatabase: Boolean = true

    private var mTotalFileLength: Long = 0

    private val mMediaScannerBatchAction by lazy {
        NotifyMediaScannerBatchAction(Utils.MEDIA_SCAN_COPY)
    }

    private var mFileOperateListener = object : OnFileOperateListener() {
        override var isCancel: () -> Boolean = {
            isCancelled()
        }

        override fun onSuccess(sourceFile: File, destFile: File) {
            onDealFileSuccess(sourceFile, destFile)
        }

        override fun onFailure(sourceFile: File, destFile: File): Boolean {
            return if (isCancelled()) {
                false
            } else {
                onDealFileError(sourceFile, destFile)
            }
        }
    }

    override fun exceptionDetection(sourceInfos: List<FileInfo>, targetInfo: FileInfo): Any? {
        return super.exceptionDetection(sourceInfos, targetInfo) ?: kotlin.run {
            sourceInfos.forEach { file ->
                file.data?.let {
                    FileCopyHelper.checkTotalSize(File(it)).let { length ->
                        mTotalFileLength += length
                    }
                }
            }
            Log.d(TAG, "exceptionDetection: totalLength = $mTotalFileLength")
            if (mTotalFileLength < 0) {
                return ERROR_PATH_NULL
            }

            // Check the empty size of the dest storage
            val storageState = checkDestStorageSpace(targetInfo.converterFileBean(), mTotalFileLength)
            return if (storageState.first) {
                Pair(ERROR_STORAGE_NOT_ENOUGH, storageState.second)
            } else null
        }
    }

    override fun workRun(
        sourceInfos: List<FileInfo>,
        targetInfo: FileInfo,
        denyPolicy: ConflictPolicy
    ): OperationResult {
        mIsOperateDatabase = Utils.isOperateDatabase(context, targetInfo.data)
        Log.d(TAG, "workRun -> mIsOperateDatabase = $mIsOperateDatabase")
        return super.workRun(sourceInfos, targetInfo, denyPolicy)
    }

    override fun onSkipDealFile(sourceFile: File) {
        mHasSkipFile = true
        JavaFileHelper.fileTotalSize(sourceFile)
    }

    override fun onDealFile(sourceFile: File, destFile: File): Boolean {
        val result = FileCopyHelper.copyFile(sourceFile, destFile, listener = mFileOperateListener)
        Log.d(TAG, "onDealFile -> copy result = $result ;  sourceFile = $sourceFile ; destFile =$destFile")
        if (mIsOperateDatabase) {
            mMediaScannerBatchAction.add(destFile.absolutePath)
        }
        return result
    }

    override fun onDealAllFilesEnd() {
        if (mIsOperateDatabase) {
            mMediaScannerBatchAction.flush()
        }
    }

    private fun onDealFileSuccess(sourceFile: File, destFile: File) {
        mHasSuccessFile = true
        val sourceFileBean = PathFileWrapper(sourceFile.absolutePath)
        val localType = sourceFileBean.mLocalType
        val mimeType = sourceFileBean.mMimeType ?: ""
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.onCopyFile(sourceFile.absolutePath, destFile.absolutePath, localType, mimeType)
    }

    companion object {
        private const val TAG = "CopyFileOperate"
    }
}