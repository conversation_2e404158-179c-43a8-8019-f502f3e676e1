/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DocumentStack.java
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/9
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  hank.zhou      2022/9/9      1.0        create
 ***********************************************************************/
package com.android.documentsui.base;

import android.os.Parcel;
import android.os.Parcelable;
import android.util.Log;

import androidx.annotation.Nullable;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.ProtocolException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedList;
import java.util.Objects;

public class DocumentStack implements Durable, Parcelable {

    public final static Parcelable.Creator<DocumentStack> CREATOR = new Creator<DocumentStack>() {
        @Override
        public DocumentStack createFromParcel(Parcel in) {
            final DocumentStack stack = new DocumentStack();
            DurableUtils.readFromParcel(in, stack);
            return stack;
        }

        @Override
        public DocumentStack[] newArray(int size) {
            return new DocumentStack[size];
        }
    };
    private final static int VERSION_INIT = 1;
    private final static int VERSION_ADD_ROOT = 2;
    private static final String TAG = "DocumentStack";
    private static boolean DEBUG = true;

    private LinkedList<DocumentInfo> mList;
    private @Nullable
    RootInfo mRoot;

    private boolean mStackTouched;

    public DocumentStack() {
        mList = new LinkedList<>();
    }

    /**
     * Creates an instance, and pushes all docs to it in the same order as they're passed as
     * parameters, i.e. the last document will be at the top of the stack.
     */
    public DocumentStack(RootInfo root, DocumentInfo... docs) {
        mList = new LinkedList<>();
        for (int i = 0; i < docs.length; ++i) {
            mList.add(docs[i]);
        }
        mRoot = root;
    }

    public DocumentStack(RootInfo root, ArrayList<DocumentInfo> docs) {
        mList = new LinkedList<>();
        for (int i = 0; i < docs.size(); ++i) {
            mList.add(docs.get(i));
        }
        mRoot = root;
    }

    public boolean isInitialized() {
        return mRoot != null;
    }

    public @Nullable RootInfo getRoot() {
        return mRoot;
    }

    public boolean isEmpty() {
        return mList.isEmpty();
    }

    public int size() {
        return mList.size();
    }

    public DocumentInfo peek() {
        return mList.peekLast();
    }

    /**
     * Returns {@link DocumentInfo} at index counted from the bottom of this stack.
     */
    public DocumentInfo get(int index) {
        return mList.get(index);
    }

    public DocumentInfo pop() {
        if (DEBUG) {
            Log.d(TAG, "Popping doc off stack.");
        }
        final DocumentInfo result = mList.removeLast();
        mStackTouched = true;

        return result;
    }

    public String getTitle() {
        if (mList.size() == 1 && mRoot != null) {
            return mRoot.title;
        } else if (mList.size() > 1) {
            DocumentInfo info = peek();
            if (info == null) {
                return null;
            }
            return info.displayName;
        } else {
            return null;
        }
    }

    @Override
    public String toString() {
        return "DocumentStack{"
                + "root=" + mRoot
                + ", docStack=" + mList
                + ", stackTouched=" + mStackTouched
                + "}";
    }

    @Override
    public void reset() {
        mList.clear();
        mRoot = null;
    }

    private void updateRoot(Collection<RootInfo> matchingRoots) throws FileNotFoundException {
        for (RootInfo root : matchingRoots) {
            if (root.equals(this.mRoot)) {
                this.mRoot = root;
                return;
            }
        }
        throw new FileNotFoundException("Failed to find matching mRoot for " + mRoot);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (!(o instanceof DocumentStack)) {
            return false;
        }

        DocumentStack other = (DocumentStack) o;
        return Objects.equals(mRoot, other.mRoot)
                && mList.equals(other.mList);
    }

    @Override
    public int hashCode() {
        return Objects.hash(mRoot, mList);
    }

    @Override
    public void read(DataInputStream in) throws IOException {
        final int version = in.readInt();
        switch (version) {
            case VERSION_INIT:
                throw new ProtocolException("Ignored upgrade");
            case VERSION_ADD_ROOT:
                if (in.readBoolean()) {
                    mRoot = new RootInfo();
                    mRoot.read(in);
                }
                final int size = in.readInt();
                for (int i = 0; i < size; i++) {
                    final DocumentInfo doc = new DocumentInfo();
                    doc.read(in);
                    mList.add(doc);
                }
                mStackTouched = in.readInt() != 0;
                break;
            default:
                throw new ProtocolException("Unknown version " + version);
        }
    }

    @Override
    public void write(DataOutputStream out) throws IOException {
        out.writeInt(VERSION_ADD_ROOT);
        if (mRoot != null) {
            out.writeBoolean(true);
            mRoot.write(out);
        } else {
            out.writeBoolean(false);
        }
        final int size = mList.size();
        out.writeInt(size);
        for (int i = 0; i < size; i++) {
            final DocumentInfo doc = mList.get(i);
            doc.write(out);
        }
        out.writeInt(mStackTouched ? 1 : 0);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        DurableUtils.writeToParcel(dest, this);
    }

}
