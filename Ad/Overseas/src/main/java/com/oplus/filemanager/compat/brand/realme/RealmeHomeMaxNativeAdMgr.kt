/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RealmeHomeMaxNativeAdMgr
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/8/8 17:08
 ** Author      : ********
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********        2024/8/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat.brand.realme

import android.app.Activity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxError
import com.applovin.mediation.nativeAds.MaxNativeAdListener
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_CLICK
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_FILL
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_REQUEST
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_SHOW
import com.filemanager.common.utils.StatisticsUtils.AD_REQUEST_FAIL
import com.filemanager.common.utils.StatisticsUtils.EVENT_AD_LOAD_TAG
import com.filemanager.common.utils.StatisticsUtils.HOMEPAGE_NATIVE_ADS_CLICK
import com.filemanager.common.utils.StatisticsUtils.HOMEPAGE_NATIVE_ADS_DISPLAY
import com.filemanager.common.utils.StatisticsUtils.HOMEPAGE_NATIVE_ADS_FILL
import com.filemanager.common.utils.StatisticsUtils.NATIVE_ADS_REQ
import com.filemanager.common.utils.StatisticsUtils.NATIVE_ADS_REQ_FAIL
import com.filemanager.common.utils.StatisticsUtils.REALME_FILE_EVENT
import com.oplus.filemanager.utils.AdHelper

class RealmeHomeMaxNativeAdMgr : BaseHomePageAdMgr() {
    private var nativeAdLoader: MaxNativeAdLoader? = null
    private var nativeAd: MaxAd? = null
    private var isRequeting = false
    companion object {
        private const val AD_POS_ID = "36b3b31b9a18edcc"
    }

    override fun requestMainAd(activity: Activity) {
        if (!AdHelper.homeAdSwitch) {
            // 云控关闭了首页广告
            Log.e(AdvertEntry.AD_TAG, "mdp home ad switch is closed")
            return
        }

        if (isRequeting) {
            Log.d(AdvertEntry.AD_TAG, "native ad is requesting, ignore")
            return
        }
        Log.d(AdvertEntry.AD_TAG, "start request home native Ad")
        nativeAdLoader = MaxNativeAdLoader(AD_POS_ID, activity)
        nativeAdLoader?.setNativeAdListener(object : MaxNativeAdListener() {
            override fun onNativeAdLoaded(nativeAdView: MaxNativeAdView?, ad: MaxAd) {
                Log.d(AdvertEntry.AD_TAG, "home onNativeAdLoaded")
                isRequeting = false
                // Clean up any pre-existing native ad to prevent memory leaks.
                if (nativeAd != null) {
                    nativeAdLoader?.destroy(nativeAd)
                }

                // Save ad for cleanup.
                nativeAd = ad

                // Add ad view to view.
                mMainFragmentContainer?.removeAllViews()
                mMainFragmentContainer?.addView(nativeAdView, FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT))
                adRootView?.visibility = View.VISIBLE

                val fillMap = HashMap<String, String>()
                fillMap[HOMEPAGE_NATIVE_ADS_FILL] = AD_EVENT_FILL
                StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, fillMap)
                StatisticsUtils.statisticsAdReturnFlow(appContext, true, Constants.PAGE_MAIN, AdHelper.adSource, null, null)

                val map = HashMap<String, String>()
                map[HOMEPAGE_NATIVE_ADS_DISPLAY] = AD_EVENT_SHOW
                StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
                StatisticsUtils.statisticsAdShowFlow(appContext, Constants.PAGE_MAIN, AdHelper.adSource, null, null, null)
            }

            override fun onNativeAdLoadFailed(adUnitId: String, error: MaxError) {
                Log.e(AdvertEntry.AD_TAG, "home onNativeAdLoadFailed error code:${error.code} message:${error.message}")
                isRequeting = false
                adRootView?.visibility = View.GONE

                val map = HashMap<String, String>()
                map[NATIVE_ADS_REQ_FAIL] = AD_REQUEST_FAIL
                StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
                StatisticsUtils.statisticsAdReturnFlow(appContext, false, Constants.PAGE_MAIN, AdHelper.adSource, null, null)
            }

            override fun onNativeAdClicked(ad: MaxAd) {
                val map = HashMap<String, String>()
                map[HOMEPAGE_NATIVE_ADS_CLICK] = AD_EVENT_CLICK
                StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
                StatisticsUtils.statisticsAdClick(appContext, Constants.PAGE_MAIN, AdHelper.adSource, null, null, null)
                Log.d(AdvertEntry.AD_TAG, "home onNativeAdClicked")
            }

            override fun onNativeAdExpired(p0: MaxAd) {
                super.onNativeAdExpired(p0)
                isRequeting = false
            }
        })
        nativeAdLoader?.loadAd(AdHelper.createBinder(activity))
        isRequeting = true
        val map = HashMap<String, String>()
        map[NATIVE_ADS_REQ] = AD_EVENT_REQUEST
        StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        StatisticsUtils.statisticsAdRequestFlow(appContext, Constants.PAGE_MAIN, AdHelper.adSource, null)
    }

    override fun eventOnDestroy() {
        if (nativeAd != null) {
            nativeAdLoader?.destroy(nativeAd)
        }
        mMainFragmentContainer?.removeAllViews()
        nativeAdLoader?.destroy()
    }
}