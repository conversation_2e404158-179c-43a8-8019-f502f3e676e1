/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WebRecycleBinLoader
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/26 16:19
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/26       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.loader

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.text.TextUtils
import com.filemanager.common.MyApplication
import com.filemanager.common.RecycleStore
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.wrapper.RecycleFileWrapper
import java.io.File
import java.util.*
import kotlin.collections.ArrayList

class WebRecycleBinLoader(context: Context) : WebBaseUriLoader<RecycleFileWrapper>(context) {
    private var mLowerCaseSearchKey: String? = null
    private var mSortType: Int = SortHelper.FILE_NAME_ORDER

    companion object {
        private const val TAG = "WebRecycleBinLoader"
        const val TIME_30_DAYS = 30 * 24 * 60 * 60 * 1000L
    }

    init {
        initData()
    }

    override fun getUri(): Uri {
        return RecycleStore.Files.INTERNAL_CONTENT_URI
    }

    override fun getSelection(): String {
        val endTime = System.currentTimeMillis()
        val startTime = endTime - TIME_30_DAYS
        val sql1 = StringBuilder()
        sql1.append(" ${RecycleStore.Files.FileColumns.RECYCLE_DATE} between $startTime and $endTime AND ")
        sql1.append(" ( ${RecycleStore.Files.FileColumns.RECYCLE_DATE} IS NOT NULL ) ")
        val selection = sql1.toString()
        Log.d(TAG, "getSelection: $selection")
        return selection
    }

    override fun getSelectionArgs(): Array<String>? {
        return null
    }

    override fun getObserverUri(): Array<Uri>? {
        return null
    }

    override fun getProjection(): Array<String> {
        return RecycleFileWrapper.RECYCLEBIN_PROJECTION
    }

    override fun getSortOrder(): String {
        val orderBy: String = when (mSortType) {
            SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER -> RecycleStore.Files.FileColumns.SIZE + " DESC"
            SortHelper.FILE_TIME_DELETE_ORDER -> RecycleStore.Files.FileColumns.RECYCLE_DATE + " DESC"
            SortHelper.FILE_NAME_ORDER -> RecycleStore.Files.FileColumns.DISPLAY_NAME
            else -> RecycleStore.Files.FileColumns.DISPLAY_NAME
        }
        Log.d(TAG, "getSortOrder  $orderBy")
        return orderBy
    }

    override fun createFromCursor(cursor: Cursor, uri: Uri?): RecycleFileWrapper? {
        val path = cursor.getString(RecycleFileWrapper.INDEX_COLUMN_RECYCLE_PATH)
        val displayName = cursor.getString(RecycleFileWrapper.INDEX_COLUMN_DISPLAY_NAME)
        if (TextUtils.isEmpty(path) || TextUtils.isEmpty(displayName)) {
            Log.d(TAG, "createFromCursor file is empty")
            return null
        }
        if (SdkUtils.isAtLeastR().not()) {
            val file = File(path)
            if (!file.exists()) {
                Log.d(TAG, "createFromCursor file not exists")
                return null
            }
        }
        if (mLowerCaseSearchKey.isNullOrEmpty() || displayName.toLowerCase(Locale.getDefault())
                .contains(mLowerCaseSearchKey!!)
        ) {
            return RecycleFileWrapper(cursor, uri)
        }
        return null
    }

    override fun preHandleResultBackground(list: MutableList<RecycleFileWrapper>): MutableList<RecycleFileWrapper> {
        //sort data
        val fileSize = list.size
        val arrayList = ArrayList<RecycleFileWrapper>()
        val sortList = ArrayList(list)
        Log.d(TAG, "preHandleResultBackground data size $fileSize")
        when (mSortType) {
            SortHelper.FILE_TYPE_ORDER, SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER, SortHelper.FILE_NAME_ORDER -> {
                SortHelper.sortRecycleFiles(sortList, mSortType, true, isShowHeader = false)
            }
        }
        Log.d(TAG, "preHandleResultBackground after sort add label size ${sortList.size}")
        arrayList.addAll(sortList)
        Log.d(TAG, "preHandleResultBackground result size = ${arrayList.size}, fileCount =$fileSize")
        return arrayList
    }

    /**
     * @param [sort] value in : -1,[SortHelper.FILE_NAME_ORDER], [SortHelper.FILE_TYPE_ORDER],
     * [SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER], [SortHelper.FILE_TIME_DELETE_ORDER]
     */
    fun setSort(sort: Int) {
        mSortType = if (sort == -1) {
            SortModeUtils.getSharedSortMode(MyApplication.sAppContext, SortRecordModeFactory.getRecycleBinKey())
        } else {
            sort
        }
        mSortOrder = getSortOrder()
    }
}