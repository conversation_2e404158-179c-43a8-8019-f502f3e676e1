/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - WpsDocThumbnailDelegate.kt
 * Description:
 *     The delegate to create the loader of WPS thumbnail
 *
 * Version: 1.0
 * Date: 2024-08-27
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-08-27   1.0    Create this module
 *********************************************************************************/
package com.filemanager.thumbnail.doc.wps

import android.content.Context
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.doc.DocThumbnail
import com.filemanager.thumbnail.doc.IDocThumbnailCallback
import com.filemanager.thumbnail.doc.IDocThumbnailLoader
import com.filemanager.thumbnail.utils.Log

internal object WpsDocThumbnailDelegate {

    private const val TAG = "WpsDocThumbnailDelegate"
    private const val WPS_THUMB_LOADER_FACTORY_CLASS =
        "com.filemanager.thumbnail.doc.wps.WpsDocThumbnailFactory"

    private val factory: IWpsDocThumbnailFactory? by lazy { loadFactory() }

    @JvmStatic
    fun createDocThumbnailLoader(context: Context): IDocThumbnailLoader =
        factory?.createWpsThumbnailLoader(context, LoggerImpl) ?: DummyWpsLoader()

    @JvmStatic
    private fun loadFactory(): IWpsDocThumbnailFactory? = kotlin.runCatching {
        Class.forName(WPS_THUMB_LOADER_FACTORY_CLASS)
            .getDeclaredConstructor()
            .newInstance() as? IWpsDocThumbnailFactory
    }.onFailure {
        if (ThumbnailManager.WpsDocConfigs.enableThumbnail) {
            Log.e(TAG, "loadFactory: can not load WpsDocThumbnailFactory! err=$it")
        }
    }.getOrNull()

    private class DummyWpsLoader : IDocThumbnailLoader {
        override val shallSampleBitmap: Boolean = false

        override fun loadThumbnail(
            docThumbnail: DocThumbnail?,
            width: Int,
            height: Int,
            snapshotCallback: IDocThumbnailCallback
        ) {
            throw UnsupportedOperationException("WPS snapshot is not integrated!")
        }

        override fun cancel(thumbModel: DocThumbnail) {
            throw UnsupportedOperationException("WPS snapshot is not integrated!")
        }

        override fun isSupported(): Boolean = false

        override fun release() {
            throw UnsupportedOperationException("WPS snapshot is not integrated!")
        }
    }

    private object LoggerImpl : IWpsThumbnailLogger {

        override val isEnabled: Boolean
            get() = Log.enableLog

        override fun v(tag: String, msg: String) {
            Log.v(tag, msg)
        }

        override fun d(tag: String, msg: String) {
            Log.d(tag, msg)
        }

        override fun i(tag: String, msg: String) {
            Log.i(tag, msg)
        }

        override fun w(tag: String, msg: String, tr: Throwable?) {
            if (tr == null) {
                Log.w(tag, msg)
            } else {
                Log.w(tag, msg, tr)
            }
        }

        override fun e(tag: String, msg: String, tr: Throwable?) {
            if (tr == null) {
                Log.e(tag, msg)
            } else {
                Log.e(tag, msg, tr)
            }
        }
    }
}