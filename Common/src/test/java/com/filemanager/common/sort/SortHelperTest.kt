/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.sort.SortHelperTest
 * * Description : SortHelperTest
 * * Version     : 1.0
 * * Date        : 2022/11/30
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.sort

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.sort.SortHelper.FILE_NAME_ORDER
import com.filemanager.common.sort.SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER
import com.filemanager.common.sort.SortHelper.FILE_TIME_DELETE_ORDER
import com.filemanager.common.sort.SortHelper.FILE_TYPE_ORDER
import com.filemanager.common.wrapper.RecycleFileWrapper
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test

class SortHelperTest {

    @Test
    fun `should return 10 when sortFileIgnoreHeadLabel if is files size is 10`() {
        val files = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val file = BaseFileBean()
            files.add(file)
        }
        SortHelper.sortFileIgnoreHeadLabel(files, 0, 0, false)
        assertEquals(10, files.size)
    }

    @Test
    fun `should sort by display name when sortFileIgnoreHeadLabel if order is 0 lastMode is 0 isDesc is true`() {
        val files = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val file = BaseFileBean()
            file.mDisplayName = "name${index}"
            files.add(file)
        }
        SortHelper.sortFileIgnoreHeadLabel(files, 0, 0, true)
        assertEquals("name1", files.get(0).mDisplayName)
        assertEquals("name10", files.get(9).mDisplayName)
    }

    @Test
    fun `should sort by display name when sortFileIgnoreHeadLabel if order is 0 lastMode is 0 isDesc is false`() {
        //given
        val files = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val file = BaseFileBean()
            file.mDisplayName = "name${index}"
            files.add(file)
        }
        //when
        SortHelper.sortFileIgnoreHeadLabel(files, 0, 0, false)
        //then
        assertEquals("name1", files.get(9).mDisplayName)
        assertEquals("name10", files.get(0).mDisplayName)
    }

    @Test
    fun `should sort by display name and order when sortFileIgnoreHeadLabel if order is 0 lastMode is 0 isDesc is false`() {
        //given
        val files = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val file = BaseFileBean()
            file.mDisplayName = "name${index}"
            file.mLocalType = when(index) {
                in 1..3 -> MimeTypeHelper.DOCX_TYPE
                in 5..7 -> MimeTypeHelper.VIDEO_TYPE
                in 8..9 -> MimeTypeHelper.COMPRESSED_TYPE
                else -> MimeTypeHelper.APPLICATION_TYPE
            }
            files.add(file)
        }
        //when
        SortHelper.sortFileIgnoreHeadLabel(files, FILE_TYPE_ORDER, 0, false)
        //then
        assertEquals("name9", files.get(0).mDisplayName)
        assertEquals("name8", files.get(1).mDisplayName)
        assertEquals("name10", files.get(2).mDisplayName)
        assertEquals("name4", files.get(3).mDisplayName)
        assertEquals("name3", files.get(4).mDisplayName)
        assertEquals("name2", files.get(5).mDisplayName)
        assertEquals("name1", files.get(6).mDisplayName)
        assertEquals("name7", files.get(7).mDisplayName)
        assertEquals("name6", files.get(8).mDisplayName)
        assertEquals("name5", files.get(9).mDisplayName)
    }

    @Test
    fun `should sort by display name and order when sortFileIgnoreHeadLabel if order is 0 lastMode is 0 isDesc is true`() {
        //given
        val files = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val file = BaseFileBean()
            file.mDisplayName = "name${index}"
            file.mLocalType = when(index) {
                in 1..3 -> MimeTypeHelper.DOCX_TYPE
                in 5..7 -> MimeTypeHelper.VIDEO_TYPE
                in 8..9 -> MimeTypeHelper.COMPRESSED_TYPE
                else -> MimeTypeHelper.APPLICATION_TYPE
            }
            files.add(file)
        }
        //when
        SortHelper.sortFileIgnoreHeadLabel(files, FILE_TYPE_ORDER, 0, true)
        //then
        assertEquals("name9", files.get(9).mDisplayName)
        assertEquals("name8", files.get(8).mDisplayName)
        assertEquals("name10", files.get(7).mDisplayName)
        assertEquals("name4", files.get(6).mDisplayName)
        assertEquals("name3", files.get(5).mDisplayName)
        assertEquals("name2", files.get(4).mDisplayName)
        assertEquals("name1", files.get(3).mDisplayName)
        assertEquals("name7", files.get(2).mDisplayName)
        assertEquals("name6", files.get(1).mDisplayName)
        assertEquals("name5", files.get(0).mDisplayName)
    }

    @Test
    fun `should sort by date modified when sortFileIgnoreHeadLabel if is files size is 10`() {
        val files = arrayListOf<BaseFileBean>()
        for (index in 1..10) {
            val file = BaseFileBean()
            file.mDateModified = (1..100).random().toLong()
            file.mDisplayName = "name${index}"
            files.add(file)
        }
        SortHelper.sortFileIgnoreHeadLabel(files, 0, 0, true)
        assertEquals(10, files.size)
    }

    @Test
    fun `should return wrapper comparator when getRecycleBinComparator if order is name and isDesc is true`() {
        val comparator = SortHelper.getRecycleBinComparator( FILE_NAME_ORDER , true)
        assertTrue(comparator is Comparator<RecycleFileWrapper>)
    }

    @Test
    fun `should return wrapper comparator when getRecycleBinComparator if order is type and isDesc is true`() {
        val comparator = SortHelper.getRecycleBinComparator( FILE_TYPE_ORDER  , true)
        assertTrue(comparator is Comparator<RecycleFileWrapper>)
    }

    @Test
    fun `should return wrapper comparator when getRecycleBinComparator if order is size and isDesc is true`() {
        val comparator = SortHelper.getRecycleBinComparator( FILE_SIZE_SUMDIR_REVERSE_ORDER  , true)
        assertTrue(comparator is Comparator<RecycleFileWrapper>)
    }

    @Test
    fun `should return wrapper comparator when getRecycleBinComparator if order is delete and isDesc is true`() {
        val comparator = SortHelper.getRecycleBinComparator( FILE_TIME_DELETE_ORDER  , true)
        assertTrue(comparator is Comparator<RecycleFileWrapper>)
    }

    @Test
    fun `should return comparator when getComparatorCategory if order is file name`() {
        val comparator1 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        val comparator2 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        mockkObject(NameComparatorGenerator)
        every { NameComparatorGenerator.getCategoryComparator(true) }.returns(comparator1)
        every { NameComparatorGenerator.getCategoryComparator(false) }.returns(comparator1)
        every { NameComparatorGenerator.getNameComparator(true) }.returns(comparator2)
        every { NameComparatorGenerator.getNameComparator(false) }.returns(comparator2)
        assertEquals(SortHelper.getComparatorCategory(FILE_NAME_ORDER, 1, true), comparator1)
        assertEquals(SortHelper.getComparatorCategory(FILE_NAME_ORDER, 1, false), comparator1)
        assertEquals(SortHelper.getComparatorCategory(FILE_NAME_ORDER, 0, true), comparator1)
        assertEquals(SortHelper.getComparatorCategory(FILE_NAME_ORDER, 0, false), comparator1)
        assertEquals(SortHelper.getComparatorCategory(FILE_NAME_ORDER, -1, true), comparator2)
        assertEquals(SortHelper.getComparatorCategory(FILE_NAME_ORDER, -1, false), comparator2)
        unmockkObject(NameComparatorGenerator)
    }

    @Test
    fun `should return comparator when getComparatorCategory if order is file type`() {
        val comparator1 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        mockkObject(ExtensionComparatorGenerator)
        every { ExtensionComparatorGenerator.getComparator(true) }.returns(comparator1)
        every { ExtensionComparatorGenerator.getComparator(false) }.returns(comparator1)
        assertEquals(SortHelper.getComparatorCategory(FILE_TYPE_ORDER, 1, true), comparator1)
        verify { ExtensionComparatorGenerator.setCategoryType(1) }
        assertEquals(SortHelper.getComparatorCategory(FILE_TYPE_ORDER, 1, false), comparator1)
        verify { ExtensionComparatorGenerator.setCategoryType(1) }
        assertEquals(SortHelper.getComparatorCategory(FILE_TYPE_ORDER, 0, true), comparator1)
        assertEquals(SortHelper.getComparatorCategory(FILE_TYPE_ORDER, 0, false), comparator1)
        unmockkObject(ExtensionComparatorGenerator)
    }

    @Test
    fun `should return comparator when getComparatorCategory if order is file size`() {
        val comparator1 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        mockkObject(SizeComparatorGenerator)
        every { SizeComparatorGenerator.getComparator(true) }.returns(comparator1)
        every { SizeComparatorGenerator.getComparator(false) }.returns(comparator1)
        assertEquals(SortHelper.getComparatorCategory(FILE_SIZE_SUMDIR_REVERSE_ORDER, 1, true), comparator1)
        assertEquals(SortHelper.getComparatorCategory(FILE_SIZE_SUMDIR_REVERSE_ORDER, 1, false), comparator1)
        assertEquals(SortHelper.getComparatorCategory(FILE_SIZE_SUMDIR_REVERSE_ORDER, 0, true), comparator1)
        assertEquals(SortHelper.getComparatorCategory(FILE_SIZE_SUMDIR_REVERSE_ORDER, 0, false), comparator1)
        unmockkObject(SizeComparatorGenerator)
    }

    @Test
    fun `should return comparator when getComparatorCategory if order is file time`() {
        val comparator1 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        val comparator2 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        mockkObject(LastModifiedComparatorGenerator)
        every { LastModifiedComparatorGenerator.getComparator(true) }.returns(comparator1)
        every { LastModifiedComparatorGenerator.getComparator(false) }.returns(comparator2)
        assertEquals(SortHelper.getComparatorCategory(SortHelper.FILE_TIME_REVERSE_ORDER, 1, true), comparator1)
        assertEquals(SortHelper.getComparatorCategory(SortHelper.FILE_TIME_REVERSE_ORDER, 1, false), comparator2)
        assertEquals(SortHelper.getComparatorCategory(SortHelper.FILE_TIME_REVERSE_ORDER, 0, true), comparator1)
        assertEquals(SortHelper.getComparatorCategory(SortHelper.FILE_TIME_REVERSE_ORDER, 0, false), comparator2)
        unmockkObject(LastModifiedComparatorGenerator)
    }

    @Test
    fun `should return comparator when getComparatorCategory if order is file open time`() {
        val comparator1 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        val comparator2 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        mockkObject(LastOpenTimeComparatorGenerator)
        every { LastOpenTimeComparatorGenerator.getComparator(true) }.returns(comparator1)
        every { LastOpenTimeComparatorGenerator.getComparator(false) }.returns(comparator2)
        assertEquals(SortHelper.getComparatorCategory(SortHelper.FILE_LAST_OPEN_TIME_ORDER, 1, true), comparator1)
        assertEquals(SortHelper.getComparatorCategory(SortHelper.FILE_LAST_OPEN_TIME_ORDER, 1, false), comparator2)
        assertEquals(SortHelper.getComparatorCategory(SortHelper.FILE_LAST_OPEN_TIME_ORDER, 0, true), comparator1)
        assertEquals(SortHelper.getComparatorCategory(SortHelper.FILE_LAST_OPEN_TIME_ORDER, 0, false), comparator2)
        unmockkObject(LastOpenTimeComparatorGenerator)
    }

    @Test
    fun `should return correct index when getDescIndex`() {
        assertEquals(SortHelper.getDescIndex(0, 5, true), 0)
        assertEquals(SortHelper.getDescIndex(0, 8, true), 0)
        assertEquals(SortHelper.getDescIndex(0, -1, true), 0)
        assertEquals(SortHelper.getDescIndex(1, -1, true), 1)
        assertEquals(SortHelper.getDescIndex(1, -1, false), -3)
        assertEquals(SortHelper.getDescIndex(2, 6, false), 3)
        assertEquals(SortHelper.getDescIndex(0, 5, false), 4)
    }

    @Test
    fun `should return comparator when getComparator if order is file name`() {
        val comparator1 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        val comparator2 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        mockkObject(NameComparatorGenerator)
        every { NameComparatorGenerator.getNameComparator(true) }.returns(comparator1)
        every { NameComparatorGenerator.getNameComparator(false) }.returns(comparator2)
        assertEquals(SortHelper.getComparator(FILE_NAME_ORDER, 0, true), comparator1)
        assertEquals(SortHelper.getComparator(FILE_NAME_ORDER, 0, false), comparator2)
        assertEquals(SortHelper.getComparator(FILE_NAME_ORDER, 1, true), comparator1)
        assertEquals(SortHelper.getComparator(FILE_NAME_ORDER, 1, false), comparator2)
        unmockkObject(NameComparatorGenerator)
    }

    @Test
    fun `should return comparator when getComparator if order is file type`() {
        val comparator1 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        val comparator2 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        mockkObject(ExtensionComparatorGenerator)
        every { ExtensionComparatorGenerator.getComparator(true) }.returns(comparator1)
        every { ExtensionComparatorGenerator.getComparator(false) }.returns(comparator2)
        assertEquals(SortHelper.getComparator(FILE_TYPE_ORDER, 0, true), comparator1)
        verify { ExtensionComparatorGenerator.setCategoryType(-1) }
        verify { ExtensionComparatorGenerator.setLastSortMode(0) }
        assertEquals(SortHelper.getComparator(FILE_TYPE_ORDER, 0, false), comparator2)
        assertEquals(SortHelper.getComparator(FILE_TYPE_ORDER, 1, true), comparator1)
        verify { ExtensionComparatorGenerator.setCategoryType(-1) }
        verify { ExtensionComparatorGenerator.setLastSortMode(0) }
        assertEquals(SortHelper.getComparator(FILE_TYPE_ORDER, 1, false), comparator2)
        unmockkObject(ExtensionComparatorGenerator)
    }

    @Test
    fun `should return comparator when getComparator if order is file size`() {
        val comparator1 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        val comparator2 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        mockkObject(SizeComparatorGenerator)
        every { SizeComparatorGenerator.getComparator(true) }.returns(comparator1)
        every { SizeComparatorGenerator.getComparator(false) }.returns(comparator2)
        assertEquals(SortHelper.getComparator(FILE_SIZE_SUMDIR_REVERSE_ORDER, 0, true), comparator1)
        verify { SizeComparatorGenerator.setLastSortMode(0) }
        assertEquals(SortHelper.getComparator(FILE_SIZE_SUMDIR_REVERSE_ORDER, 0, false), comparator2)
        assertEquals(SortHelper.getComparator(FILE_SIZE_SUMDIR_REVERSE_ORDER, 1, true), comparator1)
        verify { SizeComparatorGenerator.setLastSortMode(0) }
        assertEquals(SortHelper.getComparator(FILE_SIZE_SUMDIR_REVERSE_ORDER, 1, false), comparator2)
        unmockkObject(SizeComparatorGenerator)
    }

    @Test
    fun `should return comparator when getComparator if order is file time`() {
        val comparator1 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        val comparator2 = mockk<java.util.Comparator<BaseFileBean>>(relaxed = true)
        mockkObject(LastModifiedComparatorGenerator)
        every { LastModifiedComparatorGenerator.getComparator(true) }.returns(comparator1)
        every { LastModifiedComparatorGenerator.getComparator(false) }.returns(comparator2)
        assertEquals(SortHelper.getComparator(SortHelper.FILE_TIME_REVERSE_ORDER, 0, true), comparator1)
        assertEquals(SortHelper.getComparator(SortHelper.FILE_TIME_REVERSE_ORDER, 0, false), comparator2)
        assertEquals(SortHelper.getComparator(SortHelper.FILE_TIME_REVERSE_ORDER, 1, true), comparator1)
        assertEquals(SortHelper.getComparator(SortHelper.FILE_TIME_REVERSE_ORDER, 1, false), comparator2)
        unmockkObject(LastModifiedComparatorGenerator)
    }

    @Test
    fun `should add correct list when classifyFileByMimeType if file is folder`() {
        val file = BaseFileBean()
        val folderList = ArrayList<BaseFileBean>()
        val imageFiles = ArrayList<BaseFileBean>()
        val videoFiles = ArrayList<BaseFileBean>()
        val audioFiles = ArrayList<BaseFileBean>()
        val docFiles = ArrayList<BaseFileBean>()
        val appFiles = ArrayList<BaseFileBean>()
        val compressFiles = ArrayList<BaseFileBean>()
        val otherFiles = ArrayList<BaseFileBean>()
        file.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
        SortHelper.classifyFileByMimeType(file, folderList, imageFiles, videoFiles, audioFiles, docFiles, appFiles, compressFiles, otherFiles)
        assertEquals(folderList.size, 1)
        assertEquals(imageFiles.size, 0)
        assertEquals(videoFiles.size, 0)
        assertEquals(audioFiles.size, 0)
        assertEquals(docFiles.size, 0)
        assertEquals(appFiles.size, 0)
        assertEquals(compressFiles.size, 0)
        assertEquals(otherFiles.size, 0)
    }

    @Test
    fun `should add correct list when classifyFileByMimeType if file is image`() {
        val file = BaseFileBean()
        val folderList = ArrayList<BaseFileBean>()
        val imageFiles = ArrayList<BaseFileBean>()
        val videoFiles = ArrayList<BaseFileBean>()
        val audioFiles = ArrayList<BaseFileBean>()
        val docFiles = ArrayList<BaseFileBean>()
        val appFiles = ArrayList<BaseFileBean>()
        val compressFiles = ArrayList<BaseFileBean>()
        val otherFiles = ArrayList<BaseFileBean>()
        file.mLocalType = MimeTypeHelper.IMAGE_TYPE
        SortHelper.classifyFileByMimeType(file, folderList, imageFiles, videoFiles, audioFiles, docFiles, appFiles, compressFiles, otherFiles)
        assertEquals(folderList.size, 0)
        assertEquals(imageFiles.size, 1)
        assertEquals(videoFiles.size, 0)
        assertEquals(audioFiles.size, 0)
        assertEquals(docFiles.size, 0)
        assertEquals(appFiles.size, 0)
        assertEquals(compressFiles.size, 0)
        assertEquals(otherFiles.size, 0)
    }

    @Test
    fun `should add correct list when classifyFileByMimeType if file is video`() {
        val file = BaseFileBean()
        val folderList = ArrayList<BaseFileBean>()
        val imageFiles = ArrayList<BaseFileBean>()
        val videoFiles = ArrayList<BaseFileBean>()
        val audioFiles = ArrayList<BaseFileBean>()
        val docFiles = ArrayList<BaseFileBean>()
        val appFiles = ArrayList<BaseFileBean>()
        val compressFiles = ArrayList<BaseFileBean>()
        val otherFiles = ArrayList<BaseFileBean>()
        file.mLocalType = MimeTypeHelper.VIDEO_TYPE
        SortHelper.classifyFileByMimeType(file, folderList, imageFiles, videoFiles, audioFiles, docFiles, appFiles, compressFiles, otherFiles)
        assertEquals(folderList.size, 0)
        assertEquals(imageFiles.size, 0)
        assertEquals(videoFiles.size, 1)
        assertEquals(audioFiles.size, 0)
        assertEquals(docFiles.size, 0)
        assertEquals(appFiles.size, 0)
        assertEquals(compressFiles.size, 0)
        assertEquals(otherFiles.size, 0)
    }

    @Test
    fun `should add correct list when classifyFileByMimeType if file is audio`() {
        val file = BaseFileBean()
        val folderList = ArrayList<BaseFileBean>()
        val imageFiles = ArrayList<BaseFileBean>()
        val videoFiles = ArrayList<BaseFileBean>()
        val audioFiles = ArrayList<BaseFileBean>()
        val docFiles = ArrayList<BaseFileBean>()
        val appFiles = ArrayList<BaseFileBean>()
        val compressFiles = ArrayList<BaseFileBean>()
        val otherFiles = ArrayList<BaseFileBean>()
        file.mLocalType = MimeTypeHelper.AUDIO_TYPE
        SortHelper.classifyFileByMimeType(file, folderList, imageFiles, videoFiles, audioFiles, docFiles, appFiles, compressFiles, otherFiles)
        assertEquals(folderList.size, 0)
        assertEquals(imageFiles.size, 0)
        assertEquals(videoFiles.size, 0)
        assertEquals(audioFiles.size, 1)
        assertEquals(docFiles.size, 0)
        assertEquals(appFiles.size, 0)
        assertEquals(compressFiles.size, 0)
        assertEquals(otherFiles.size, 0)
    }

    @Test
    fun `should add correct list when classifyFileByMimeType if file is doc`() {
        val file = BaseFileBean()
        val folderList = ArrayList<BaseFileBean>()
        val imageFiles = ArrayList<BaseFileBean>()
        val videoFiles = ArrayList<BaseFileBean>()
        val audioFiles = ArrayList<BaseFileBean>()
        val docFiles = ArrayList<BaseFileBean>()
        val appFiles = ArrayList<BaseFileBean>()
        val compressFiles = ArrayList<BaseFileBean>()
        val otherFiles = ArrayList<BaseFileBean>()
        file.mLocalType = MimeTypeHelper.DOC_TYPE
        SortHelper.classifyFileByMimeType(file, folderList, imageFiles, videoFiles, audioFiles, docFiles, appFiles, compressFiles, otherFiles)
        assertEquals(folderList.size, 0)
        assertEquals(imageFiles.size, 0)
        assertEquals(videoFiles.size, 0)
        assertEquals(audioFiles.size, 0)
        assertEquals(docFiles.size, 1)
        assertEquals(appFiles.size, 0)
        assertEquals(compressFiles.size, 0)
        assertEquals(otherFiles.size, 0)
    }

    @Test
    fun `should add correct list when classifyFileByMimeType if file is app`() {
        val file = BaseFileBean()
        val folderList = ArrayList<BaseFileBean>()
        val imageFiles = ArrayList<BaseFileBean>()
        val videoFiles = ArrayList<BaseFileBean>()
        val audioFiles = ArrayList<BaseFileBean>()
        val docFiles = ArrayList<BaseFileBean>()
        val appFiles = ArrayList<BaseFileBean>()
        val compressFiles = ArrayList<BaseFileBean>()
        val otherFiles = ArrayList<BaseFileBean>()
        file.mLocalType = MimeTypeHelper.APPLICATION_TYPE
        SortHelper.classifyFileByMimeType(file, folderList, imageFiles, videoFiles, audioFiles, docFiles, appFiles, compressFiles, otherFiles)
        assertEquals(folderList.size, 0)
        assertEquals(imageFiles.size, 0)
        assertEquals(videoFiles.size, 0)
        assertEquals(audioFiles.size, 0)
        assertEquals(docFiles.size, 0)
        assertEquals(appFiles.size, 1)
        assertEquals(compressFiles.size, 0)
        assertEquals(otherFiles.size, 0)
    }

    @Test
    fun `should add correct list when classifyFileByMimeType if file is compress`() {
        val file = BaseFileBean()
        val folderList = ArrayList<BaseFileBean>()
        val imageFiles = ArrayList<BaseFileBean>()
        val videoFiles = ArrayList<BaseFileBean>()
        val audioFiles = ArrayList<BaseFileBean>()
        val docFiles = ArrayList<BaseFileBean>()
        val appFiles = ArrayList<BaseFileBean>()
        val compressFiles = ArrayList<BaseFileBean>()
        val otherFiles = ArrayList<BaseFileBean>()
        file.mLocalType = MimeTypeHelper.COMPRESSED_TYPE
        SortHelper.classifyFileByMimeType(file, folderList, imageFiles, videoFiles, audioFiles, docFiles, appFiles, compressFiles, otherFiles)
        assertEquals(folderList.size, 0)
        assertEquals(imageFiles.size, 0)
        assertEquals(videoFiles.size, 0)
        assertEquals(audioFiles.size, 0)
        assertEquals(docFiles.size, 0)
        assertEquals(appFiles.size, 0)
        assertEquals(compressFiles.size, 1)
        assertEquals(otherFiles.size, 0)
    }

    @Test
    fun `should add correct list when classifyFileByMimeType if file is other`() {
        val file = BaseFileBean()
        val folderList = ArrayList<BaseFileBean>()
        val imageFiles = ArrayList<BaseFileBean>()
        val videoFiles = ArrayList<BaseFileBean>()
        val audioFiles = ArrayList<BaseFileBean>()
        val docFiles = ArrayList<BaseFileBean>()
        val appFiles = ArrayList<BaseFileBean>()
        val compressFiles = ArrayList<BaseFileBean>()
        val otherFiles = ArrayList<BaseFileBean>()
        file.mLocalType = MimeTypeHelper.UNKNOWN_TYPE
        SortHelper.classifyFileByMimeType(file, folderList, imageFiles, videoFiles, audioFiles, docFiles, appFiles, compressFiles, otherFiles)
        assertEquals(folderList.size, 0)
        assertEquals(imageFiles.size, 0)
        assertEquals(videoFiles.size, 0)
        assertEquals(audioFiles.size, 0)
        assertEquals(docFiles.size, 0)
        assertEquals(appFiles.size, 0)
        assertEquals(compressFiles.size, 0)
        assertEquals(otherFiles.size, 1)
    }
}