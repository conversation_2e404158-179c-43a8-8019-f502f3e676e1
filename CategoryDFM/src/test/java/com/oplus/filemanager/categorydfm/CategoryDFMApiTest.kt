/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CategoryDFMApiTest
 ** Description : CategoryDFMApi Unit Test
 ** Version     : 1.0
 ** Date        : 2024/04/09 18:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/04/09       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.categorydfm

import android.app.Activity
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.KtConstants
import com.oplus.filemanager.categorydfm.ui.CategoryDfmParentFragment
import com.oplus.filemanager.dfm.DFMManager
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import java.util.function.Consumer

@Ignore("stupid test")
class CategoryDFMApiTest {

    private lateinit var fragment: CategoryDfmParentFragment

    @Before
    fun setup() {
        fragment = mockk()
        mockkStatic(CategoryDFMApi::class)
        mockkStatic(DFMManager::class)
    }

    @After
    fun teardown() {
        unmockkStatic(CategoryDFMApi::class)
        unmockkStatic(DFMManager::class)
    }

    @Test
    fun should_notNull_when_getFragment() {
        val activity = mockk<Activity>()
        every { CategoryDFMApi.getFragment(any()) }.answers { callOriginal() }
        val fragment = CategoryDFMApi.getFragment(activity)
        Assert.assertNotNull(fragment)
    }

    @Test
    fun test_onResumeLoadData() {
        justRun { fragment.jumpAllTab() }
        justRun { fragment.onResumeLoadData() }
        val bundle = mockk<Bundle>()
        every { fragment.arguments }.returns(null)
        every { bundle.getBoolean(KtConstants.DFM_JUMP_ALL_TAB) }.returns(false)
        justRun { bundle.remove(any()) }
        every { CategoryDFMApi.onResumeLoadData(any()) }.answers { callOriginal() }

        CategoryDFMApi.onResumeLoadData(Fragment())
        verify(inverse = true) { fragment.onResumeLoadData() }

        CategoryDFMApi.onResumeLoadData(fragment)
        verify { fragment.onResumeLoadData() }
        verify(inverse = true) { fragment.jumpAllTab() }

        every { fragment.arguments }.returns(bundle)
        every { bundle.getBoolean(KtConstants.DFM_JUMP_ALL_TAB) }.returns(false)
        CategoryDFMApi.onResumeLoadData(fragment)
        verify(atLeast = 2) { fragment.onResumeLoadData() }
        verify(inverse = true) { fragment.jumpAllTab() }

        every { fragment.arguments }.returns(bundle)
        every { bundle.getBoolean(KtConstants.DFM_JUMP_ALL_TAB) }.returns(true)
        CategoryDFMApi.onResumeLoadData(fragment)
        verify(atLeast = 3) { fragment.onResumeLoadData() }
        verify { fragment.jumpAllTab() }
    }

    @Test
    fun test_onCreateOptionsMenu() {
        every { CategoryDFMApi.onCreateOptionsMenu(any(), any(), any()) }.answers { callOriginal() }
        justRun { fragment.onCreateOptionsMenu(any(), any()) }

        CategoryDFMApi.onCreateOptionsMenu(Fragment(), mockk(), mockk())
        verify(inverse = true) { fragment.onCreateOptionsMenu(any(), any()) }

        CategoryDFMApi.onCreateOptionsMenu(fragment, mockk(), mockk())
        verify { fragment.onCreateOptionsMenu(any(), any()) }
    }

    @Test
    fun test_onMenuItemSelected() {
        every { CategoryDFMApi.onMenuItemSelected(any(), any()) }.answers { callOriginal() }
        every { fragment.onOptionsItemSelected(any()) }.returns(true)

        var result = CategoryDFMApi.onMenuItemSelected(mockk(), mockk())
        Assert.assertFalse(result)
        verify(inverse = true) { fragment.onOptionsItemSelected(any()) }

        result = CategoryDFMApi.onMenuItemSelected(fragment, mockk())
        Assert.assertTrue(result)
        verify { fragment.onOptionsItemSelected(any()) }
    }

    @Test
    fun test_pressBack() {
        every { CategoryDFMApi.pressBack(any()) }.answers { callOriginal() }
        every { fragment.pressBack() }.returns(true)

        var result = CategoryDFMApi.pressBack(mockk())
        Assert.assertFalse(result)
        verify(inverse = true) { fragment.pressBack() }

        result = CategoryDFMApi.pressBack(fragment)
        Assert.assertTrue(result)
        verify { fragment.pressBack() }
    }

    @Test
    fun test_onNavigationItemSelected() {
        every { CategoryDFMApi.onNavigationItemSelected(any(), any()) }.answers { callOriginal() }
        every { fragment.onNavigationItemSelected(any()) }.returns(true)

        var result = CategoryDFMApi.onNavigationItemSelected(mockk(), mockk())
        Assert.assertFalse(result)
        verify(inverse = true) { fragment.onNavigationItemSelected(any()) }

        result = CategoryDFMApi.onNavigationItemSelected(fragment, mockk())
        Assert.assertTrue(result)
        verify { fragment.onNavigationItemSelected(any()) }
    }

    @Test
    fun test_fromSelectPathResult() {
        every { CategoryDFMApi.fromSelectPathResult(any(), any(), any()) }.answers { callOriginal() }
        justRun { fragment.fromSelectPathResult(any(), any()) }

        CategoryDFMApi.fromSelectPathResult(Fragment(), 100, listOf("/sdcard/download"))
        verify(inverse = true) { fragment.fromSelectPathResult(100, listOf("/sdcard/download")) }

        CategoryDFMApi.fromSelectPathResult(fragment, 100, listOf("/sdcard/download"))
        verify { fragment.fromSelectPathResult(100, listOf("/sdcard/download")) }
    }

    @Test
    fun test_setIsHalfScreen() {
        every { CategoryDFMApi.setIsHalfScreen(any(), any(), any()) }.answers { callOriginal() }
        justRun { fragment.setIsHalfScreen(any()) }

        CategoryDFMApi.setIsHalfScreen(Fragment(), 100, false)
        verify(inverse = true) { fragment.setIsHalfScreen(false) }

        CategoryDFMApi.setIsHalfScreen(fragment, 100, false)
        verify { fragment.setIsHalfScreen(false) }
    }

    @Test
    fun test_backToTop() {
        every { CategoryDFMApi.backToTop(any()) }.answers { callOriginal() }
        justRun { fragment.backToTop() }

        CategoryDFMApi.backToTop(Fragment())
        verify(inverse = true) { fragment.backToTop() }

        CategoryDFMApi.backToTop(fragment)
        verify { fragment.backToTop() }
    }

    @Test
    fun test_init() {
        mockkStatic(FeatureCompat::class)
        every { CategoryDFMApi.init(any(), any()) }.answers { callOriginal() }
        justRun { DFMManager.init(any(), any()) }

        every { FeatureCompat.isSupportPCConnect }.returns(false)
        CategoryDFMApi.init(mockk(), true)
        verify(inverse = true) { DFMManager.init(any(), true) }

        every { FeatureCompat.isSupportPCConnect }.returns(true)
        CategoryDFMApi.init(mockk(), true)
        verify { DFMManager.init(any(), true) }

        unmockkStatic(FeatureCompat::class)
    }

    @Test
    fun test_openDfsP2pConnect() {
        every { CategoryDFMApi.openDfsP2PConnect() }.answers { callOriginal() }
        justRun { DFMManager.openDfsP2pConnect() }

        CategoryDFMApi.openDfsP2PConnect()
        verify { DFMManager.openDfsP2pConnect() }
    }

    @Test
    fun test_exit() {
        every { CategoryDFMApi.exit() }.answers { callOriginal() }
        justRun { DFMManager.exit() }

        CategoryDFMApi.exit()
        verify { DFMManager.exit() }
    }

    @Test
    fun test_getDFSDevice() {
        every { CategoryDFMApi.getDFSDevice(any()) }.answers { callOriginal() }
        every { DFMManager.getDFSDevice(any()) }.returns(Bundle())

        val bundle = CategoryDFMApi.getDFSDevice(Consumer { })
        verify { DFMManager.getDFSDevice(any()) }
        Assert.assertNotNull(bundle)
    }

    @Test
    fun test_handleP2PConflict() {
        every { CategoryDFMApi.handleP2PConflict(any()) }.answers { callOriginal() }
        val activity = mockk<AppCompatActivity>()
        val lifecycle = mockk<Lifecycle>()
        justRun { lifecycle.addObserver(any()) }
        every { activity.lifecycle }.returns(lifecycle)

        CategoryDFMApi.handleP2PConflict(activity)
    }
}