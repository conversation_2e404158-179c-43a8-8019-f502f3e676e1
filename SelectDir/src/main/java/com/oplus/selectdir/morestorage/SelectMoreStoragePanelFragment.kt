/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.select
 * * Version     : 1.0
 * * Date        : 2021/3/25
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir.morestorage

import android.content.Context
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.appcompat.widget.Toolbar
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.BaseVMPanelFragment
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.OnGetUIInfoListener
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.FileManagerRecyclerView
import com.oplus.selectdir.R
import com.oplus.selectdir.SelectPathController.Companion.SELECT_DIALOG_FRAGMENT_TAG
import com.oplus.selectdir.SelectPathDialogInterface
import com.oplus.selectdir.filebrowser.SelectFileBrowserViewModel

class SelectMoreStoragePanelFragment : BaseVMPanelFragment<SelectFileBrowserViewModel>(), OnBackPressed,
    OnRecyclerItemClickListener, OnGetUIInfoListener, Toolbar.OnMenuItemClickListener {
    companion object {
        private const val TAG = "MoreStoragePanelFragment"
        const val MORE_DIALOG_FRAGMENT_TAG = "MORE_DIALOG_FRAGMENT_TAG"
    }

    private var mRecyclerView: FileManagerRecyclerView? = null
    private var mToolbar: COUIToolbar? = null
    private var mAdapter: SelectMoreStorageAdapter? = null
    private var mMoreStorageViewModel: SelectMoreStorageViewModel? = null
    private var mLayoutManager: GridLayoutManager? = null
    private var mPaths: ArrayList<String>? = null
    private var mTitle: String = ""
    private var mSelectPathDialogInterface: SelectPathDialogInterface? = null
    private var hasStoragePermission: Boolean = true

    fun setSelectPathDialogInterface(selectPathDialogInterface: SelectPathDialogInterface) {
        mSelectPathDialogInterface = selectPathDialogInterface
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            mActivity = activity as BaseVMActivity
            arguments?.apply {
                mPaths = getStringArrayList(KtConstants.P_PATH_LIST)
                mTitle = MyApplication.sAppContext.getString(com.filemanager.common.R.string.select_device)
                mAdapter = SelectMoreStorageAdapter(it, <EMAIL>).apply {
                    setHasStableIds(true)
                }
                hasStoragePermission = getBoolean(KtConstants.HAS_STORAGE_PERMISSION, true)
            }
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.selectdir_more_storage_fragment
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    private fun setPanelMaxHeight() {
        if (rootView != null && mActivity is Context) {
            val context = mActivity as Context
            rootView!!.layoutParams = rootView!!.layoutParams.apply {
                height = COUIPanelMultiWindowUtils.getPanelMaxHeight(context, context.resources.configuration) -
                        COUIPanelMultiWindowUtils.getStatusBarHeight(context)
            }
        }
    }

    override fun initContentView(view: View) {
        hideDragView()
        rootView = view.findViewById(R.id.coordinator_layout)
        appBarLayout = view.findViewById(R.id.appbar)
        mRecyclerView = view.findViewById(R.id.recycler_view)
        mToolbar = view.findViewById<COUIToolbar>(R.id.toolbar)?.apply {
            title = mTitle
            isTitleCenterStyle = true
            navigationIcon = null
            inflateMenu(R.menu.selectdir_more_storage_menu)
            setOnMenuItemClickListener(this@SelectMoreStoragePanelFragment)
        }
        setPanelMaxHeight()
        setDialogOnKeyListener { _, keyCode, event ->
            Log.d(TAG, "onKey keyCode:$keyCode event:$event")
            mSelectPathDialogInterface?.performCancel(MORE_DIALOG_FRAGMENT_TAG)
            true
        }
        PredictiveBackUtils.registerOnBackInvokedCallback(this)
    }

    override fun initData() {
        if (mMoreStorageViewModel == null) {
            mMoreStorageViewModel = ViewModelProvider(this).get(SelectMoreStorageViewModel::class.java)
        }
        mRecyclerView?.let {
            mLayoutManager = GridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1)
            it.isNestedScrollingEnabled = true
            it.clipToPadding = false
            it.layoutManager = mLayoutManager!!
            it.itemAnimator?.apply {
                changeDuration = 0
                addDuration = 0
                removeDuration = 0
                moveDuration = 0
            }
            mAdapter?.let { adapter ->
                it.adapter = adapter
                adapter.setOnRecyclerItemClickListener(this@SelectMoreStoragePanelFragment)
            }
            mToolbar?.post {
                val paddingBottom = if (it.paddingBottom == 0) {
                    MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                } else {
                    it.paddingBottom
                }
                it.setPadding(it.paddingLeft, KtViewUtils.getRecyclerViewTopPadding(appBarLayout), it.paddingRight, paddingBottom)
                mMoreStorageViewModel?.initLoader(mPaths)
            }
        }
        if (hasStoragePermission.not()) {
            setPermissionEmptyVisible(View.VISIBLE)
        }
    }

    override fun startObserve() {
        mRecyclerView?.post {
            mMoreStorageViewModel?.let {
                it.mUiState.observe(this, { list -> mAdapter?.setData(list) })
            }
        }
    }

    override fun onResumeLoadData() {
        Log.d(TAG, "onResumeLoadData size ${mPaths?.size ?: "null"}")
        mMoreStorageViewModel?.initLoader(mPaths)
    }

    override fun pressBack(): Boolean {
        return false
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (mSelectPathDialogInterface == null) {
            mSelectPathDialogInterface = activity?.supportFragmentManager?.findFragmentByTag(
                SELECT_DIALOG_FRAGMENT_TAG) as? SelectPathDialogInterface
        }
        onResumeLoadData()
    }

    override fun onItemClick(view: View, position: Int) {
        mPaths?.let {
            val currentPath = it[position]
            if ((mSelectPathDialogInterface != null) && (mActivity != null)) {
                mSelectPathDialogInterface!!.showPanelFragment(
                    mActivity!!.supportFragmentManager, MORE_DIALOG_FRAGMENT_TAG, currentPath, arguments, true)
            } else {
                (mActivity as? TransformNextFragmentListener)?.transformToNextFragment(currentPath)
            }
        }
    }

    override fun onItemLongClick(view: View, position: Int) {}

    override fun getViewModel() = mMoreStorageViewModel

    override fun getRecyclerView() = mRecyclerView

    override fun getEmptyMarginTop(): Int {
        return MyApplication.sAppContext.resources.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_50dp)
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        initData()
        super.onShow(isShowOnFirstPanel)
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
            ?.setPanelBackgroundTintColor(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackgroundElevatedWithCard))
    }

    override fun onMenuItemClick(item: MenuItem?): Boolean {
        if ((null == item) || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        when (item.itemId) {
            R.id.action_close -> mSelectPathDialogInterface?.performCancel(MORE_DIALOG_FRAGMENT_TAG)
            else -> Log.w(TAG, "call onMenuItemSelected with unknown item")
        }
        return true
    }
}
