/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - TBLPreviewVideoPlayer.kt
 * Description:
 *     The implementations of video player with TBL.
 *
 * Version: 1.0
 * Date: 2024-09-14
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-09-14   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.video

import android.content.Context
import android.media.AudioAttributes
import android.os.Message
import android.view.SurfaceView
import android.view.TextureView
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayErrorListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayPreparedListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayProgressUpdateListener
import com.oplus.filemanager.preview.audio.IPreviewAudioPlayer.OnPlayStateChangedListener
import com.oplus.filemanager.preview.audio.TBLPreviewAudioPlayer
import com.oplus.filemanager.preview.utils.MediaPlayerHelper
import com.oplus.tblplayer.IMediaPlayer
import com.oplus.tblplayer.IMediaPlayer.OnPlayerEventListener
import com.oplus.tblplayer.IMediaPlayer.OnVideoSizeChangedListener
import com.oplus.tblplayer.monitor.ErrorCode

internal class TBLPreviewVideoPlayer(context: Context) : TBLPreviewAudioPlayer(context),
    IPreviewVideoPlayer {

    private companion object {
        private const val TAG = "TBLPreviewVideoPlayer"
        private const val MSG_UPDATE_SURFACE_VIEW = 201
        private const val MSG_UPDATE_TEXTURE_VIEW = 202
        private const val MSG_DECODE_VIDEO_SIZE_ERROR = 203

        protected const val ERROR_DECODE_REASON_TIMEOUT = -2000
    }

    override val impl: AudioPlayerImpl = VideoPlayerImpl(context.applicationContext)

    override fun setOnPlayPreparedListener(
        listener: OnPlayPreparedListener
    ): IPreviewVideoPlayer = apply {
        super.setOnPlayPreparedListener(listener)
    }

    override fun setOnPlayErrorListener(
        listener: OnPlayErrorListener
    ): IPreviewVideoPlayer = apply {
        super.setOnPlayErrorListener(listener)
    }

    override fun setOnPlayStateChangedListener(
        listener: OnPlayStateChangedListener
    ): IPreviewVideoPlayer = apply {
        super.setOnPlayStateChangedListener(listener)
    }

    override fun setOnPlayProgressUpdateListener(
        listener: OnPlayProgressUpdateListener
    ): IPreviewVideoPlayer = apply {
        super.setOnPlayProgressUpdateListener(listener)
    }

    override fun setPlayProgressUpdateInterval(interval: Long): IPreviewVideoPlayer = apply {
        super.setPlayProgressUpdateInterval(interval)
    }

    override fun setOnVideoSizeChangedListener(
        listener: IPreviewVideoPlayer.OnVideoSizeChangedListener
    ): IPreviewVideoPlayer = apply {
        (impl as VideoPlayerImpl).setOnVideoSizeChangedListener(listener)
    }

    override fun setVideoSurfaceView(surfaceView: SurfaceView?): IPreviewVideoPlayer = apply {
        (impl as VideoPlayerImpl).setVideoSurfaceView(surfaceView)
    }

    override fun setVideoTextureView(textureView: TextureView?): IPreviewVideoPlayer = apply {
        (impl as VideoPlayerImpl).setVideoTextureView(textureView)
    }

    override fun setPrepareTimeout(timeout: Long): IPreviewVideoPlayer = apply {
        super.setPrepareTimeout(timeout)
    }

    override fun setVideoSizeDecodeTimeout(timeout: Long): IPreviewVideoPlayer = apply {
        (impl as VideoPlayerImpl).videoSizeDecodeTimeout = timeout
    }

    private class VideoPlayerImpl(
        appContext: Context,
        requireAudioFocus: Boolean = MediaPlayerHelper.ENABLE_REQUIRE_FOCUS_WHEN_PLAY_VIDEO
    ) : AudioPlayerImpl(appContext, TAG, requireAudioFocus), OnVideoSizeChangedListener,
        OnPlayerEventListener {

        override val defaultVolume: Float = 0f
        override val audioAttrs: AudioAttributes by lazy {
            AudioAttributes.Builder()
                .setContentType(AudioAttributes.CONTENT_TYPE_MOVIE)
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .build()
        }

        var videoSizeDecodeTimeout: Long = MediaPlayerHelper.getVideoSizeDecodeTimeout(null)

        private var videoSizeListener: IPreviewVideoPlayer.OnVideoSizeChangedListener? = null

        @Volatile
        private var surfaceView: SurfaceView? = null

        @Volatile
        private var textureView: TextureView? = null

        @Volatile
        private var lastVideoSize: Pair<Int, Int>? = null
        private var isVideoSizeDecodeTimeout = false

        override fun handleMessage(msg: Message): Boolean {
            when (msg.what) {
                MSG_UPDATE_SURFACE_VIEW -> onUpdateSurfaceView(msg.obj as? SurfaceView)
                MSG_UPDATE_TEXTURE_VIEW -> onUpdateTextureView(msg.obj as? TextureView)
                MSG_DECODE_VIDEO_SIZE_ERROR -> onVideoSizeDecodeTimeout()
                else -> return super.handleMessage(msg)
            }
            return true
        }

        override fun onConfigureBeforePrepare(player: IMediaPlayer) {
            super.onConfigureBeforePrepare(player)
            applyCurrentVideoSurface(player)
            player.setOnVideoSizeChangedListener(this)
            player.setOnPlayerEventListener(this)
            isVideoSizeDecodeTimeout = false
        }

        override fun onBeforeRequestPlayWhenPrepared(player: IMediaPlayer) {
            isVideoSizeDecodeTimeout = false
            playerHandler
                .sendEmptyMessageDelayed(MSG_DECODE_VIDEO_SIZE_ERROR, videoSizeDecodeTimeout)
        }

        override fun onRelease(player: IMediaPlayer) {
            clearLastVideoSurface(player)
            super.onRelease(player)
        }

        @Synchronized
        fun setVideoSurfaceView(surfaceView: SurfaceView?) {
            if (this.surfaceView === surfaceView) {
                return
            }
            if (tblPlayer == null) {
                this.surfaceView = surfaceView
                this.textureView = null
            } else {
                playerHandler.removeMessages(MSG_UPDATE_SURFACE_VIEW)
                playerHandler.removeMessages(MSG_UPDATE_TEXTURE_VIEW)
                Message.obtain(playerHandler, MSG_UPDATE_SURFACE_VIEW, surfaceView).sendToTarget()
            }
        }

        @Synchronized
        fun setVideoTextureView(textureView: TextureView?) {
            if (this.textureView === textureView) {
                return
            }
            if (tblPlayer == null) {
                this.surfaceView = null
                this.textureView = textureView
            } else {
                playerHandler.removeMessages(MSG_UPDATE_SURFACE_VIEW)
                playerHandler.removeMessages(MSG_UPDATE_TEXTURE_VIEW)
                Message.obtain(playerHandler, MSG_UPDATE_TEXTURE_VIEW, textureView).sendToTarget()
            }
        }

        @Synchronized
        private fun applyCurrentVideoSurface(player: IMediaPlayer) {
            surfaceView?.let {
                player.setVideoSurfaceView(it)
            }
            textureView?.let {
                player.setVideoTextureView(it)
            }
        }

        @Synchronized
        private fun clearLastVideoSurface(player: IMediaPlayer) {
            surfaceView?.let {
                player.clearVideoSurfaceView(it)
                surfaceView = null
            }
            textureView?.let {
                player.clearVideoTextureView(it)
                textureView = null
            }
        }

        private fun onUpdateSurfaceView(surfaceView: SurfaceView?) {
            val player = tblPlayer ?: run {
                Log.e(logTag, "onUpdateSurfaceView: ERROR!! player is not prepared.")
                return
            }
            onUpdateSurfaceView(player, surfaceView)
        }

        private fun onUpdateSurfaceView(player: IMediaPlayer, surfaceView: SurfaceView?) {
            clearLastVideoSurface(player)
            this.surfaceView = surfaceView
            applyCurrentVideoSurface(player)
        }

        private fun onUpdateTextureView(textureView: TextureView?) {
            val player = tblPlayer ?: run {
                Log.e(logTag, "onUpdateTextureView: ERROR!! player is not prepared.")
                return
            }
            onUpdateTextureView(player, textureView)
        }

        private fun onUpdateTextureView(player: IMediaPlayer, textureView: TextureView?) {
            clearLastVideoSurface(player)
            this.textureView = textureView
            applyCurrentVideoSurface(player)
        }

        fun setOnVideoSizeChangedListener(
            listener: IPreviewVideoPlayer.OnVideoSizeChangedListener
        ) {
            lastVideoSize?.let { (width, height) ->
                if (mainHandler.looper.isCurrentThread) {
                    listener.onVideoSizeChanged(width, height)
                } else {
                    mainHandler.post { listener.onVideoSizeChanged(width, height) }
                }
            }
            videoSizeListener = listener
        }

        override fun onVideoSizeChanged(
            player: IMediaPlayer?,
            width: Int,
            height: Int,
            unappliedRotationDegrees: Int,
            pixelWidthHeightRatio: Float
        ) {
            if ((player == null) || (tblPlayer != player) || isVideoSizeDecodeTimeout) {
                return
            }
            Log.d(
                logTag,
                "onVideoSizeChanged: width=$width, height=$height, " +
                        "rotation=$unappliedRotationDegrees, ratio=$pixelWidthHeightRatio"
            )
            notifyVideoSize(width, height)
        }

        private fun notifyVideoSize(width: Int, height: Int) {
            if (lastVideoSize == null) {
                playerHandler.removeMessages(MSG_DECODE_VIDEO_SIZE_ERROR)
                callbackPrepared(isPlayStarted)
            }
            lastVideoSize = width to height
            val listener = videoSizeListener ?: return
            mainHandler.post { listener.onVideoSizeChanged(width, height) }
        }

        private fun onVideoSizeDecodeTimeout() {
            Log.e(logTag, "onVideoSizeDecodeTimeout")
            isVideoSizeDecodeTimeout = true
            onError(tblPlayer, ErrorCode.TYPE_RENDERER, ERROR_DECODE_REASON_TIMEOUT, null)
            onRelease()
        }

        override fun onPlayerStateChanged(player: IMediaPlayer?, playbackState: Int) {
            // Do nothing
        }

        override fun onIsPlayingChanged(player: IMediaPlayer?, isPlaying: Boolean) {
            if ((player == null) || (tblPlayer != player) || !isPlaying || lastVideoSize != null) {
                return
            }
            playerHandler.removeMessages(MSG_DECODE_VIDEO_SIZE_ERROR)
            val width = player.videoWidth
            val height = player.videoHeight
            Log.d(logTag, "onIsPlayingChanged: Check size, width=$width, height=$height")
            if (width > 0 && height > 0) {
                notifyVideoSize(width, height)
            } else {
                Log.e(logTag, "onIsPlayingChanged: ERROR! Still no video size")
                onVideoSizeDecodeTimeout()
            }
        }

        override fun onDownstreamSizeChanged(
            player: IMediaPlayer?,
            width: Int,
            height: Int,
            rotationDegrees: Int,
            pixelWidthHeightRatio: Float
        ) {
            // Do nothing
        }
    }
}