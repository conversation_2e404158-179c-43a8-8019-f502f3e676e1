/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileActionOpen.kt
 ** Description:  open file function
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(Liu<PERSON><EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.open

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.net.ConnectivityManager
import android.net.NetworkInfo
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import androidx.activity.ComponentActivity
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.OpenAnyExtUtil
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.fileutils.UriHelper.getFileUri
import com.filemanager.common.fileutils.createFile
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.removableapp.InstallResultCallback
import com.filemanager.common.removableapp.RemovableAppManager
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.*
import com.filemanager.common.utils.StatisticsUtils.FILE_TYPE
import com.filemanager.common.wrapper.ImageFileWrapper
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.MimeTypeUtil
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.oplus.filemanager.interfaze.fileopentime.IFileOpenTime
import com.oplus.filemanager.interfaze.oaps.IOapsLib
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import java.util.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.apache.commons.io.FilenameUtils
import kotlin.collections.ArrayList


class FileActionOpen(val builder: Builder) : BaseFileAction<BaseFileActionObserver>(builder.activity) {

    companion object {
        const val OPEN_FLAG = "oppo_filemanager_openflag"
        const val NEW_OPEN_FLAG = "oplus_filemanager_openflag"
        private const val TAG = "FileActionOpen"
        const val MEDIA_FROM = "media-from"
        const val MEDIA_FROM_FILEMANAGER = "from_file_manager"
        const val INTENT_FLAG_OPEN_FROM_RECENT = "file-manager-recent"
        private const val IMAGE_FILE_TYPE = "file-type"
        private const val IMAGE_FILE_ORDER = "order-type"
        private const val ORDER_MEDIA_ID_LIST = "order-media-id-list"
        private const val ENABLE_THUMB_LINE_PREVIEW = "enable_thumbLine_preview"    /*是否显示缩图轴*/
        private const val IMAGE_FILE_TYPE_FILE_MANAGER = 2
        private const val SECOND = 1000L
        private const val CODE_ID = "code_id"
        private const val SCREEN = "SCREEN,SCREEN_AD"
        private const val APP_START_THE_SOURCE = "app_start_the_source"
        private const val READ_TYPE = "readType"
        private const val BROWSE = "browse"
        private const val MIN_VERSION = 14141000
        private const val YOZO_EXTRA_FILE_PATH = "File_Path"
        private const val YOZO_EXTRA_AUTO_SAVE = "need_auto_save"

        class Builder(val activity: ComponentActivity, val file: BaseFileBean) {
            var mIsOpenByOtherWay = false
            var mIsFromRecent = false
            var mIsFromRecentCardWidget = false
            var mOrderMediaIdList: ArrayList<String>? = null

            /**
             * value in {[SortHelper.FILE_NAME_ORDER],[SortHelper.FILE_TYPE_ORDER],
             * [SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER],[SortHelper.FILE_TIME_REVERSE_ORDER]}
             */
            var mSortMode: Int? = null

            /**
             * @param [isOpenByOtherWay] whether not need to open file by other way
             */
            fun setIsOpenByOtherWay(isOpenByOtherWay: Boolean): Builder {
                mIsOpenByOtherWay = isOpenByOtherWay
                return this
            }

            /**
             * @param [isFromRecent] whether the page is not MainRecentFragment. If true,
             * the picture in APP OplusGallery will be not available to slide switch
             */
            fun setIsFromRecent(isFromRecent: Boolean): Builder {
                mIsFromRecent = isFromRecent
                return this
            }

            fun setSortMode(sortMode: Int?): Builder {
                mSortMode = sortMode
                return this
            }

            fun setOrderMediaIdList(orderMediaIdList: ArrayList<String>?): Builder {
                mOrderMediaIdList = orderMediaIdList
                return this
            }

            fun build(): FileActionOpen {
                return FileActionOpen(this)
            }

            fun setIsFromRecentCardWidget(isFromRecentCardWidget: Boolean): Builder {
                mIsFromRecentCardWidget = isFromRecentCardWidget
                return this
            }
        }
    }

    @VisibleForTesting
    val mOperateFile: BaseFileBean = builder.file
    @VisibleForTesting
    val mActivity: Context = builder.activity
    @VisibleForTesting
    val mIsOpenByOtherWay = builder.mIsOpenByOtherWay
    private val mIsFromRecent = builder.mIsFromRecent
    private val mIsFromRecentCardWidget = builder.mIsFromRecentCardWidget
    private val mSortMode = builder.mSortMode
    private val mUnknownFileManager: UnknownFileManager by lazy {
        UnknownFileManager()
    }
    private var removableAppManager: RemovableAppManager? = null
    private var installConfirmDialog: Dialog? = null
    private var installFailureDialog: Dialog? = null

    override fun run(): Boolean {
        return if (mOperateFile.mIsDirectory || !JavaFileHelper.exists(mOperateFile)) {
            Log.e(TAG, "file == null or file is a directory")
            val path = mOperateFile.mData
            if (KtUtils.checkDfmFileAndDfmDisconnected(path)) {
                notifyObserver(OPEN_ERROR_DFM_DISCONNECT)
            } else {
                notifyObserver(OPEN_ERROR)
            }
            false
        } else {
            when (openFile()) {
                ACTION_FAILED -> notifyObserver(ACTION_FAILED)
                ACTION_DONE -> {
                    if (mIsOpenByOtherWay) {
                        StatisticsUtils.onCommon(
                            appContext,
                            StatisticsUtils.EVENT_OTHER_WAYS_TO_OPEN,
                            mapOf(FILE_TYPE to mOperateFile.mData?.let { FileTypeUtils.getExtension(it) })
                        )
                    }
                    notifyObserver(ACTION_DONE)
                }
                DRM_OPEN_ERROR -> notifyObserver(DRM_OPEN_ERROR)
            }
            true
        }
    }

    @VisibleForTesting
    fun setType(path: String?): Int {
        var type = MimeTypeHelper.getTypeFromPath(path)
        if (type == MimeTypeHelper.UNKNOWN_TYPE) {
            type = MimeTypeHelper.getMediaType(path)
        }
        type = MimeTypeHelper.getTypeFromDrm(mActivity, type, path)
        return type
    }

    @VisibleForTesting
    fun setIntentExtra(): Intent {
        val intent = Intent()
        intent.putExtra(MEDIA_FROM, MEDIA_FROM_FILEMANAGER)
        intent.putExtra(OPEN_FLAG, mIsOpenByOtherWay)
        intent.putExtra(NEW_OPEN_FLAG, mIsOpenByOtherWay)
        intent.putExtra(INTENT_FLAG_OPEN_FROM_RECENT, mIsFromRecent)
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        intent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
        if (((mOperateFile as? ImageFileWrapper)?.getCShot() ?: 0) > ImageFileWrapper.EMPTY_CSHOT) {
            val bundle = Bundle()
            bundle.putBoolean("not_display_cshot_btn", false)
            intent.putExtras(bundle)
        }
        intent.putExtra(YOZO_EXTRA_FILE_PATH, mOperateFile.mData)
        intent.putExtra(YOZO_EXTRA_AUTO_SAVE, false)
        return intent
    }

    @VisibleForTesting
    fun openByImageType(intent: Intent, path: String?, uri: Uri): Intent {
            intent.putExtra(IMAGE_FILE_TYPE, IMAGE_FILE_TYPE_FILE_MANAGER)
            mSortMode?.let {
                if (mSortMode == SortHelper.FILE_TYPE_ORDER) {
                    intent.putExtra(IMAGE_FILE_ORDER, SortHelper.FILE_NAME_ORDER)
                } else {
                    intent.putExtra(IMAGE_FILE_ORDER, mSortMode)
                }
            }
            return OpenFileFactory.intentToOpenImage(path, uri, intent)
    }

    @VisibleForTesting
    fun openByVideoType(uri: Uri, intent: Intent): Intent {
        val fileExtension = FileTypeUtils.getExtension(mOperateFile.mData)
        val type = if (fileExtension != null && !mOperateFile.mIsDirectory) {
            fileExtension
        } else {
            StatisticsUtils.DIRECTORY_FILE_TYPE
        }

        MainScope().launch(Dispatchers.IO) {
            val videoTime = KtUtils.formatVideoTime(GetMediaDurationUtil.getDuration(mOperateFile) / SECOND)
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.OPEN_VIDEO,
                mapOf(
                    FILE_TYPE to type,
                    StatisticsUtils.OPEN_VIDEO_TIME to videoTime
                )
            )
        }
        notifyObserver(ZOOM_WARNING)
        return  OpenFileFactory.intentToOpenVideo(uri, intent)
    }

    @VisibleForTesting
    fun openByEpubType(type: Int, path: String?, uri: Uri, intent: Intent): Intent {
        if (!mIsOpenByOtherWay && !FeatureCompat.sIsExpRom) {
            openByReaderOrBook(intent, path)
        }
        notifyObserver(ZOOM_WARNING)
        return  OpenFileFactory.intentToOpenOther(mActivity, type, path!!, uri, intent)
    }

    @VisibleForTesting
    fun openFile(): Int {
        val path = mOperateFile.mData ?: return ACTION_FAILED
        var wpsActivityOverridePending = false
        val type = setType(path)
        val uri = getFileUri(mOperateFile, fileType = type) ?: return ACTION_FAILED
        var intent = setIntentExtra()
        val fileOpenTimeAction = Injector.injectFactory<IFileOpenTime>()
        fileOpenTimeAction?.addOrUpdateFileTime(path, mOperateFile.mDateModified)
        MyApplication.openFileCauseBg = true
        when (type) {
            MimeTypeHelper.IMAGE_TYPE -> {
                intent = openByImageType(intent, path, uri)
                if (!builder.mOrderMediaIdList.isNullOrEmpty()) {
                    intent.putExtra(ORDER_MEDIA_ID_LIST, builder.mOrderMediaIdList)
                }
                intent.putExtra(ENABLE_THUMB_LINE_PREVIEW, true)
                Log.d(TAG, "orderMediaIdList size=${builder.mOrderMediaIdList?.size}")
            }
            MimeTypeHelper.VIDEO_TYPE -> intent = openByVideoType(uri, intent)
            MimeTypeHelper.AUDIO_TYPE -> {
                intent = OpenFileFactory.intentToOpenAudio(uri, intent)
                val result = isSetFileDefaultOpenWay(intent)
                OptimizeStatisticsUtil.openAudioFile(path, result.second)
                notifyObserver(ZOOM_WARNING)
            }
            MimeTypeHelper.HTML_TYPE, MimeTypeHelper.LRC_TYPE, MimeTypeHelper.UMD_TYPE,
            MimeTypeHelper.EBK_TYPE, MimeTypeHelper.CHM_TYPE, MimeTypeHelper.APPLICATION_TYPE,
            MimeTypeHelper.CSV_TYPE, MimeTypeHelper.CER_TYPE, MimeTypeHelper.VCF_TYPE, MimeTypeHelper.VCS_TYPE,
            MimeTypeHelper.ICS_TYPE, MimeTypeHelper.P12_TYPE, MimeTypeHelper.TORRENT_TYPE, MimeTypeHelper.THEME_TYPE -> {
                intent = OpenFileFactory.intentToOpenOther(mActivity, type, path, uri, intent)
                notifyObserver(ZOOM_WARNING)
                if (!foundActivitiesByIntent(intent, type, path, uri)) {
                    return ACTION_DONE
                }
            }
            MimeTypeHelper.DOC_TYPE, MimeTypeHelper.DOCX_TYPE, MimeTypeHelper.XLS_TYPE, MimeTypeHelper.XLSX_TYPE,
            MimeTypeHelper.PPT_TYPE, MimeTypeHelper.PPTX_TYPE, MimeTypeHelper.PDF_TYPE, MimeTypeHelper.OFD_TYPE -> {
                commonOpenDoc()
                intent = OpenFileFactory.intentToOpenOther(mActivity, type, path, uri, intent)
                wpsActivityOverridePending = openDocument(intent)
                notifyObserver(ZOOM_WARNING)
                if (!FeatureCompat.sIsExpRom && wpsIntentNotHasPackage(intent) && !mIsOpenByOtherWay
                    && !isSetFileDefaultOpenWay(intent).first
                ) {
                    showRecommendIfNeed(intent, type, path, uri)
                    return ACTION_DONE
                }
            }
            MimeTypeHelper.DRM_TYPE ->  return DRM_OPEN_ERROR
            MimeTypeHelper.EPUB_TYPE -> intent = openByEpubType(type, path, uri, intent)
            MimeTypeHelper.TXT_TYPE -> {
                intent = OpenFileFactory.intentToOpenOther(mActivity, type, path, uri, intent)
                if (mIsOpenByOtherWay || FeatureCompat.sIsExpRom || isSetFileDefaultOpenWay(intent).first || !openByReaderOrBook(intent, path)) {
                    commonOpenDoc()
                    wpsActivityOverridePending = wpsOpenFile(intent)
                }
                notifyObserver(ZOOM_WARNING)
                val noOpenByDefault = !isSetFileDefaultOpenWay(intent).first && !openByReaderOrBook(path = path)
                if (!FeatureCompat.sIsExpRom && wpsIntentNotHasPackage(intent) && !mIsOpenByOtherWay && noOpenByDefault) {
                    showRecommendIfNeed(intent, type, path, uri)
                    return ACTION_DONE
                }
            }
            else -> {
                val defaultAppIntent = setIntentExtra()
                OpenFileFactory.obtainIntentForDefaultApp(type, uri, defaultAppIntent)
                //是否设置了默认应用打开
                if (isSetFileDefaultOpenWay(defaultAppIntent, Constants.OPEN_ANY_PKG_NAME).first) {
                    notifyObserver(ZOOM_WARNING)
                    Log.d(TAG, "open by default app")
                    return doStartActivity(defaultAppIntent, type, path, uri, false)
                } else if (mIsOpenByOtherWay) {
                    //若为其他打开方式，不管是否支持随心开，都调起应用选择器
                    setOpenAnyAppIntent(intent, path, uri, type)
                } else if (DocFormatUtil.needWpsOpenFile(path)) {
                    wpsActivityOverridePending = wpsOpenFile(intent)
                    intent.action = Intent.ACTION_VIEW
                    intent.setDataAndType(uri, "text/plain")
                } else if (canOpenByQuickPreview(path)) {
                    return startOpenAnyApp(intent, type, path, uri)
                } else {
                    val mimeType = MimeTypeHelper.getMimeTypeByFileType(type)
                    if (mimeType == MimeTypeHelper.MimeType.MIMETYPE_UNKNOWN || mimeType == MimeTypeHelper.MimeType.MIMETYPE_STREAM) {
                        return openUnknownFile(path, uri, intent, mIsFromRecentCardWidget, type)
                    } else {
                        setOpenAnyAppIntent(intent, path, uri, type)
                    }
                }
            }
        }
        Log.d(TAG, "wpsActivityOverridePending $wpsActivityOverridePending")
        if (checkIsFileManagerOpen(intent)) {
            notifyObserver(FORMAT_NOT_SUPPORT, FileOpenObserver.UnKnownFileBean(path, uri, mIsOpenByOtherWay, mIsFromRecentCardWidget))
            return ACTION_DONE
        }
        return doStartActivity(intent, type, path, uri, wpsActivityOverridePending)
    }

    @VisibleForTesting
    fun doStartActivity(
        intent: Intent,
        type: Int,
        path: String?,
        uri: Uri?,
        wpsActivityOverridePending: Boolean
    ): Int {
        Log.d(TAG, "doStartActivity")
        if (mIsFromRecentCardWidget) {
            (mActivity as? Activity)?.apply {
                runOnUiThread {
                    realStartActivity(intent, type, path, uri, wpsActivityOverridePending)
                }
            }
            return ACTION_DONE
        } else {
            return realStartActivity(intent, type, path, uri, wpsActivityOverridePending)
        }
    }

    private fun realStartActivity(
        intent: Intent,
        type: Int,
        path: String?,
        uri: Uri?,
        wpsActivityOverridePending: Boolean
    ): Int {
        try {
            // fix 2582139 multiple selection box appears in file manager
            intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
            when {
                (type == MimeTypeHelper.CSV_TYPE) -> mActivity.startActivity(Intent.createChooser(intent, null))

                (type == MimeTypeHelper.IMAGE_TYPE) && mIsOpenByOtherWay -> {
                    mActivity.startActivity(
                        OpenFileFactory.getChooseIntentExcludeQuickPreview(
                            mActivity,
                            intent
                        )
                    )
                }
                else -> mActivity.startActivity(intent)
            }
            if (!mIsFromRecentCardWidget && ((type == MimeTypeHelper.IMAGE_TYPE) || wpsActivityOverridePending)) {
                notifyObserver(OPEN_ANIM)
            } else if (mIsFromRecentCardWidget) {
                (mActivity as? Activity)?.apply {
                    overridePendingTransition(
                        com.support.dialog.R.anim.coui_bottom_dialog_enter, com.support.dialog.R.anim.coui_bottom_dialog_exit
                    )
                    finish()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "doStartActivity e:${e.message}", e)
            if (uri == null) {
                return ACTION_FAILED
            }
            notifyObserver(UNKNOWN_FILE_DIALOG, FileOpenObserver.UnKnownFileBean(path, uri, mIsOpenByOtherWay, mIsFromRecentCardWidget))
        }
        return ACTION_DONE
    }

    /**
     * 启动随心开apk
     *
     * 检测随心开apk是否支持打开
     *      -是，检测随心开是否安装
     *          -是，使用随心开打开
     *          -否，检测随心开是否可卸载找回
     *              -是，引导安装
     *      -否，随心开不支持该文件格式
     */
    @SuppressLint("RestrictedApi")
    private fun startOpenAnyApp(intent: Intent, type: Int, path: String, uri: Uri): Int {
        Log.d(TAG, "startOpenAnyApp")
        val newIntent: Intent = intent
        if (AppUtils.isAppInstalledByPkgName(appContext, Constants.OPEN_ANY_PKG_NAME)) {
            //使用随心开打开
            setOpenAnyAppIntent(newIntent, path, uri, type)
            newIntent.`package` = Constants.OPEN_ANY_PKG_NAME
            CollectPrivacyUtils.collectInstalledAppList(Constants.OPEN_ANY_PKG_NAME)
            return doStartActivity(intent, type, path, uri, false)
        } else {
            //随心开是否可卸载找回
            (mActivity as? ComponentActivity)?.let {
                checkRemovableApp(it, path, uri, newIntent, type)
            }
        }
        return ACTION_DONE
    }

    /**
     * 独立apk是否支持打开该文件
     */
    private fun canOpenByQuickPreview(filePath: String): Boolean {
        val ext = FilenameUtils.getExtension(filePath).lowercase()
        val canOpen = OpenAnyExtUtil.openAnyExt.contains(ext)
        Log.d(TAG, "canOpenByQuickPreview canOpen:$canOpen ext:$ext")
        val hasIntegrated = OpenAnyManager.hasIntegrateQuickPreview()
        Log.d(TAG, "canOpenByQuickPreview -> hasIntegrated = $hasIntegrated")
        return canOpen && hasIntegrated
    }

    private fun setOpenAnyAppIntent(intent: Intent, path: String, uri: Uri?, type: Int): Intent {
        val mimeType = MimeTypeHelper.getMimeTypeByFileType(type)
        intent.apply {
            setDataAndType(uri, mimeType)
            action = Intent.ACTION_VIEW
        }
        val file = createFile(path)
        val fileName = FilenameUtils.getName(path)
        val fileSize = file?.length() ?: 0
        val dateModified = file?.lastModified() ?: 0
        Log.d(TAG, "setOpenAnyAppIntent fileName:$fileName fileSize:$fileSize dateModified:$dateModified")
        intent.apply {
            putExtra(KtConstants.FILE_PREVIEW_NAME, fileName)
            putExtra(KtConstants.FILE_PREVIEW_SIZE, fileSize)
            putExtra(KtConstants.FILE_MODIFY_TIME, dateModified)
            putExtra(KtConstants.FILE_ABSOLUTE_PATH, path)
            putExtra(KtConstants.FILE_HAS_LABEL, mOperateFile.mHasLabel)
        }
        return intent
    }

    private fun checkRemovableApp(activity: ComponentActivity, path: String, uri: Uri?, intent: Intent, type: Int) {
        if (removableAppManager == null) {
            removableAppManager = RemovableAppManager(activity)
        }
        val removableAppInfo = removableAppManager?.obtainRemovableInfo()
        if (removableAppInfo == null) {
            Log.d(TAG, "checkRemovableApp removableAppInfo null")
            uri?.let {
                openUnknownFile(path, uri, intent, mIsFromRecentCardWidget, type)
            }
            return
        }
        removableAppInfo.let { info ->
            activity.lifecycleScope.launch(Dispatchers.Main) {
                installConfirmDialog = removableAppManager?.createRemovableConfirmDialog(activity, info) {
                    if (!it) {
                        if (mIsFromRecentCardWidget) {
                            (mActivity as Activity).finish()
                        }
                        return@createRemovableConfirmDialog
                    }
                    if (installConfirmDialog?.isShowing == true) {
                        installConfirmDialog?.dismiss()
                    }
                    removableAppManager?.reInstallApp(activity, info, object : InstallResultCallback {
                        override fun onInstalledResult(success: Boolean) {
                            activity.runOnUiThread {
                                Log.d(TAG, "onInstalledResult -> success = $success")
                                if (success) {
                                    setOpenAnyAppIntent(intent, path, uri, type)
                                    intent.`package` = Constants.OPEN_ANY_PKG_NAME
                                    CollectPrivacyUtils.collectInstalledAppList(Constants.OPEN_ANY_PKG_NAME)
                                    doStartActivity(intent, type, path, uri, false)
                                } else {
                                    installFailureDialog = removableAppManager?.createInstallFailureDialog(activity, this)
                                    if (installFailureDialog?.isShowing == false) {
                                        installFailureDialog?.show()
                                    }
                                }
                            }
                        }
                    })
                }
                StatisticsUtils.onCommonByOpenAnyFile(
                    appContext,
                    StatisticsUtils.FILE_PREVIEW_INSTALL_DIALOG,
                    mapOf()
                )
                if (installConfirmDialog?.isShowing == false) {
                    installConfirmDialog?.show()
                }
            }
        }
    }

    @VisibleForTesting
    fun openByReaderOrBook(intent: Intent? = null, path: String?): Boolean {
        if (AppUtils.checkApkInstalledByPackageName(mActivity, Constants.HEYTAP_READER_PACKAGE_NAME)) {
            intent?.let {
                it.setPackage(Constants.HEYTAP_READER_PACKAGE_NAME)
                CollectPrivacyUtils.collectInstalledAppList(Constants.HEYTAP_READER_PACKAGE_NAME)
                it.putExtra(CODE_ID, SCREEN)
                it.putExtra(APP_START_THE_SOURCE, 1)
                StatisticsUtils.onCommon(
                    mActivity,
                    StatisticsUtils.EVENT_READER_TO_OPEN,
                    mapOf(FILE_TYPE to path?.let { FileTypeUtils.getExtension(it) })
                )
            }
            return true
        }

        if (AppUtils.checkApkInstalledByPackageName(mActivity, Constants.OPPO_READER_PACKAGE_NAME)) {
            intent?.let {
                it.setPackage(Constants.OPPO_READER_PACKAGE_NAME)
                CollectPrivacyUtils.collectInstalledAppList(Constants.OPPO_READER_PACKAGE_NAME)
                it.putExtra(CODE_ID, SCREEN)
                it.putExtra(APP_START_THE_SOURCE, 1)
                StatisticsUtils.onCommon(
                    mActivity,
                    StatisticsUtils.EVENT_READER_TO_OPEN,
                    mapOf(FILE_TYPE to path?.let { FileTypeUtils.getExtension(it) })
                )
            }
            return true
        }

        if (AppUtils.checkApkInstalledByPackageName(mActivity, Constants.BOOK_PACKAGE_NAME)) {
            intent?.let {
                it.setPackage(Constants.BOOK_PACKAGE_NAME)
                CollectPrivacyUtils.collectInstalledAppList(Constants.BOOK_PACKAGE_NAME)
                it.putExtra(READ_TYPE, BROWSE)
                StatisticsUtils.onCommon(
                    mActivity,
                    StatisticsUtils.EVENT_BOOK_TO_OPEN,
                    mapOf(FILE_TYPE to path?.let { FileTypeUtils.getExtension(it) })
                )
            }
            return true
        }
        return false
    }

    @VisibleForTesting
    fun commonOpenDoc() {
        val fileExtension = FileTypeUtils.getExtension(mOperateFile.mData)
        val type = if (fileExtension != null && !mOperateFile.mIsDirectory) {
            fileExtension
        } else {
            StatisticsUtils.DIRECTORY_FILE_TYPE
        }
        StatisticsUtils.onCommon(
            mActivity,
            StatisticsUtils.OPEN_DOC,
            mapOf(FILE_TYPE to type)
        )
    }

    @VisibleForTesting
    fun isSetFileDefaultOpenWay(intent: Intent, excludePkgName: String? = null): Pair<Boolean, String> {
        val pm: PackageManager = mActivity.packageManager
        val ri = pm.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)
        var resolvePackageName = ""
        if (ri != null) {
            resolvePackageName = ri.activityInfo?.applicationInfo?.packageName ?: ""
            Log.d(TAG, "isSetFileDefaultOpenWay resolvePackageName:$resolvePackageName")
            if (
                !TextUtils.isEmpty(resolvePackageName) &&
                !KtConstants.DEFAULT_OPEN_PACKAGE_NAME.equals(resolvePackageName, ignoreCase = true) &&
                excludePkgName != resolvePackageName &&
                resolvePackageName != mActivity.packageName
            ) {
                return Pair(true, resolvePackageName)
            }
        }
        return Pair(false, resolvePackageName)
    }

    @VisibleForTesting
    fun checkAppEnabled(packageName: String): Boolean {
        return KtAppUtils.checkAppForceEnabled(packageName) == KtAppUtils.ConnectionResult.SUCCESS
    }

    @VisibleForTesting
    fun openUnknownFile(path: String?, uri: Uri, intent: Intent, isFromRecentCardWidget: Boolean, type: Int): Int {
        Log.d(TAG, "openUnknownFile")
        OptimizeStatisticsUtil.openUnknownFile(path ?: "")
        return if (FeatureCompat.sIsNotSupportUnknownFile) {
            notifyObserver(UNKNOWN_FILE_DIALOG, FileOpenObserver.UnKnownFileBean(path, uri, mIsOpenByOtherWay, isFromRecentCardWidget))
            ACTION_DONE
        } else {
            if (mOperateFile.mData.isNullOrEmpty()) {
                return ACTION_FAILED
            }
            val unknownFileResult = mUnknownFileManager.startUnknownFiles(mActivity,
                PathFileWrapper(mOperateFile.mData!!).apply {
                    mLocalFileUri = uri
                })
            if ((unknownFileResult == false) || ((unknownFileResult == null) && !initUnknownFileDialog())) {
                val file = createFile(mOperateFile.mData!!)
                var mimeType: String? = null
                if (file != null) {
                    mimeType = MimeTypeUtil.getMimeType(file)
                }
                Log.d(TAG, "openUnknownFile mimeType:$mimeType file:$file")
                if (mimeType.isNullOrEmpty() || mimeType == MimeTypeHelper.MimeType.MIMETYPE_STREAM) {
                    notifyObserver(UNKNOWN_FILE_DIALOG, FileOpenObserver.UnKnownFileBean(path, uri, mIsOpenByOtherWay, isFromRecentCardWidget))
                } else {
                    intent.action = Intent.ACTION_VIEW
                    intent.setDataAndType(uri, mimeType)
                    if (checkIsFileManagerOpen(intent)) {
                        notifyObserver(UNKNOWN_FILE_DIALOG, FileOpenObserver.UnKnownFileBean(path, uri, mIsOpenByOtherWay, isFromRecentCardWidget))
                        return ACTION_DONE
                    }
                    notifyObserver(ZOOM_WARNING)
                    return doStartActivity(intent, type, path, uri, isFromRecentCardWidget)
                }
            }
            ACTION_DONE
        }
    }

    @SuppressLint("MissingPermission")
    private fun initUnknownFileDialog(): Boolean {
        fun isNetworkConnect(): Boolean {
            try {
                val connectivity = appContext
                    .getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
                val info = connectivity?.activeNetworkInfo
                if ((info != null) && info.isConnected) {
                    if (info.state == NetworkInfo.State.CONNECTED) {
                        return true
                    }
                }
            } catch (e: Exception) {
                Log.e("isNetworkConnect: error", e.toString())
            }
            return false
        }

        if (!isNetworkConnect()) {
            Log.d(TAG, "initUnknownFileDialog: no network connected")
            return false
        }
        if (mUnknownFileManager.mUnknownFileNameArray.isEmpty() || mUnknownFileManager.mUnknownPckNameArray.isEmpty()) {
            Log.d(TAG, "mUnknownFileNameArray or mUnknownPckNameArray is empty")
            return false
        }
        val fileName = mUnknownFileManager.mUnknownFileNameArray[0]
        val pkgName = mUnknownFileManager.mUnknownPckNameArray[0]
        notifyObserver(ZOOM_WARNING)
        notifyObserver(APP_RECOMMEND_DIALOG, Pair(fileName, pkgName))
        return true
    }

    @VisibleForTesting
    fun openDocument(intent: Intent): Boolean {
        if (openDocumentByOplusDocumentsReader(intent)) {
            return false
        }
        return wpsOpenFile(intent)
    }

    @VisibleForTesting
    fun openDocumentByOplusDocumentsReader(intent: Intent): Boolean {
        Log.d(TAG, "openDocumentByOplusDocumentsReader mIsOpenByOtherWay = $mIsOpenByOtherWay")
        if (mIsOpenByOtherWay) {
            return false
        }
        kotlin.runCatching {
            val resolveInfo = appContext.packageManager.resolveActivity(
                intent,
                PackageManager.MATCH_DEFAULT_ONLY
            )
            val resolvePackageName = resolveInfo?.activityInfo?.applicationInfo?.packageName
            Log.d(TAG, "openDocumentByOplusDocumentsReader resolvePackageName = $resolvePackageName")
            //先判断是否设置了默认
            val canSetOplusDocumentsReader =
                resolvePackageName.isNullOrEmpty() || KtConstants.DEFAULT_OPEN_PACKAGE_NAME.equals(
                    resolvePackageName,
                    ignoreCase = true
                ) || KtConstants.QUICK_PREVIEW_PACKAGE.equals(
                    resolvePackageName,
                    ignoreCase = true
                )
            if (canSetOplusDocumentsReader
                && checkAppEnabled(KtConstants.QUICK_PREVIEW_PACKAGE)
                && isOplusDocumentsReaderSupport()
            ) {
                Log.d(TAG, "openDocumentByOplusDocumentsReader success")
                intent.setPackage(KtConstants.QUICK_PREVIEW_PACKAGE)
                CollectPrivacyUtils.collectInstalledAppList(KtConstants.QUICK_PREVIEW_PACKAGE)
                return true
            }
        }
        return false
    }

    @VisibleForTesting
    fun isOplusDocumentsReaderSupport(): Boolean {
        kotlin.runCatching {
            val applicationInfo = appContext.packageManager.getApplicationInfo(
                KtConstants.QUICK_PREVIEW_PACKAGE,
                PackageManager.GET_META_DATA
            )
            val supportPreview = applicationInfo.metaData.getBoolean("support_preview")
            Log.d(TAG, "isOplusDocumentsReaderSupport supportPreview = $supportPreview")
            //由于目前support_preview 还没加入，为了测试先加入版本号的判断，等后面加入了就去掉版本好的判断
            return supportPreview || AppUtils.getAppVersionCode(
                KtConstants.QUICK_PREVIEW_PACKAGE
            ) >= MIN_VERSION
        }.onFailure {
            Log.e(TAG, "it = ${it.message}")
        }
        return false
    }
    @VisibleForTesting
    fun wpsOpenFile(intent: Intent): Boolean {
        var wpsActivityOverridePending = false
        if (!mIsOpenByOtherWay && WpsManager.isSupportWpsPreview) {
            fun appendWpsPackageInfo(intent: Intent): Boolean {
                return when {
                    WpsManager.needOpenByWpsPersonal() &&
                            checkAppEnabled(Constants.WPS_PERSONAL_PACKAGE_NAME) -> {
                        intent.setPackage(Constants.WPS_PERSONAL_PACKAGE_NAME)
                        CollectPrivacyUtils.collectInstalledAppList(Constants.WPS_PERSONAL_PACKAGE_NAME)
                        intent.putExtra("oplusPreview", 1)
                        true
                    }
                    AppUtils.checkApkInstalledByPackageName(mActivity, Constants.WPS_PACKAGE_NAME) &&
                            checkAppEnabled(Constants.WPS_PACKAGE_NAME) -> {
                        intent.setPackage(Constants.WPS_PACKAGE_NAME)
                        CollectPrivacyUtils.collectInstalledAppList(Constants.WPS_PACKAGE_NAME)
                        true
                    }
                    else -> {
                        false
                    }
                }
            }

            try {
                val pm: PackageManager = mActivity.packageManager
                val ri = pm.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)
                if (ri != null) {
                    val resolvePackageName = ri.activityInfo.applicationInfo.packageName
                    Log.d(TAG, "openFile resolve=$resolvePackageName")
                    if (TextUtils.isEmpty(resolvePackageName) || KtConstants.DEFAULT_OPEN_PACKAGE_NAME.equals(resolvePackageName, ignoreCase = true)) {
                        wpsActivityOverridePending = appendWpsPackageInfo(intent)
                    }
                } else {
                    Log.d(TAG, "openFile resolveInfo null")
                    wpsActivityOverridePending = appendWpsPackageInfo(intent)
                }
            } catch (e: Exception) {
                Log.e(TAG, "openFile ${e.message}")
            }
        }
        return wpsActivityOverridePending
    }

    class UnknownFileManager {
        companion object {
            private const val TAG = "UnknownFileManager"
            private const val PKG_NAME_OPPO_SHOP = "com.oppo.market"
            private const val NEW_PKG_NAME_OPPO_SHOP = "com.heytap.market"
            private const val DOWNLOAD_EXTRA_FROM = 1000
            private const val TYPE_AUDIO = 0
            private const val TYPE_VIDEO = 1
            private const val TYPE_DOC = 2
            private const val TYPE_TORRENT = 3
            private const val TYPE_EXCEL = 4
            private const val TYPE_PPT = 5
            private const val TYPE_PDF = 6
            private const val TYPE_OFD = 7
            private const val TYPE_ARRAY_LENGTH = 3
            private const val FILE_TYPE_ARRAY_INDEX = 3

            fun downloadApps(context: Context, pkgName: String) {
                StatisticsUtils.nearMeStatistics(context, StatisticsUtils.OPEN_UNKNOWN_DOWNLOAD_APP)
                val checkAppStoreEnabledWithDialog = (Injector.injectFactory<IOapsLib>()
                    ?.checkAppStoreEnabledWithDialog(context) == true).not()
                if (checkAppStoreEnabledWithDialog) {
                    return
                }
                Log.d(TAG, "downloadApps pkg= $pkgName")
                val uri = Uri.parse("oppomarket://details?packagename=$pkgName")
                val intent = Intent(Intent.ACTION_VIEW, uri)
                intent.putExtra("extra.key.intent.from", DOWNLOAD_EXTRA_FROM)
                intent.putExtra("extra.key.productdetail_start_with_download", true)
                intent.putExtra("go_back_to_launcher_app", true)
                try {
                    context.startActivity(intent)
                } catch (e: Exception) {
                    Log.e(TAG, "downloadApps : ${e.message}")
                }
            }

            fun isForNameFindApp(context: Context, labelArray: Array<String>?): Boolean {
                val pm = context.packageManager
                val intent = Intent(Intent.ACTION_MAIN)
                intent.addCategory(Intent.CATEGORY_LAUNCHER)
                val resolveInfos: List<ResolveInfo> = try {
                    pm.queryIntentActivities(intent, 0)
                } catch (e: Exception) {
                    Log.e("isForNameFindApp: queryIntentActivities failed")
                    return false
                }
                if (labelArray.isNullOrEmpty()) {
                    return false
                }
                for (j in labelArray.indices) {
                    for (i in resolveInfos.indices) {
                        var string = resolveInfos[i].activityInfo.packageName
                        if (null != string) {
                            val regExpression = "^[ \r\n\t\u00A0]+|[ \r\n\t\u00A0]+$"
                            string = string.replace(regExpression.toRegex(), "")
                            Log.d(TAG, "isForNameFindApp: loadAllApplicationIons string = $string")
                            if (string == labelArray[j]) {
                                return true
                            }
                        }
                    }
                }
                return false
            }
        }

        private var mUnknownFileType = -1

        var mUnknownFileNameArray: Array<String> = arrayOf()

        var mUnknownPckNameArray: Array<String> = arrayOf()

        /**
         * @return null: no recommended app installed.
         */
        fun startUnknownFiles(context: Context, file: BaseFileBean): Boolean? {
            if (!Utils.checkAppStateEnable(context, NEW_PKG_NAME_OPPO_SHOP)) {
                Log.w(TAG, "startUnknownFiles: new market not exist !")
                if (!Utils.checkAppStateEnable(context, PKG_NAME_OPPO_SHOP)) {
                    Log.w(TAG, "startUnknownFiles: market not exist !")
                    return false
                }
            }
            var name: Array<String>? = null
            var unknownFileType = -1
            if (isUnknownFile(context, file)) {
                name = mUnknownPckNameArray
                unknownFileType = mUnknownFileType
            }

            when (unknownFileType) {
                TYPE_AUDIO -> return OpenFileFactory.openAudioFiles(context, file, name)

                TYPE_VIDEO -> return OpenFileFactory.openVideoFiles(context, file, name)

                TYPE_DOC, TYPE_PPT, TYPE_PDF, TYPE_OFD, TYPE_EXCEL -> return OpenFileFactory.openOfficeFiles(context, file, name)

                TYPE_TORRENT -> return OpenFileFactory.openTorrentFiles(context, file, name)
            }
            return false
        }

        private fun isUnknownFile(context: Context, file: BaseFileBean): Boolean {
            val ext = FileTypeUtils.getExtension(file.mData)
            if (ext.isNullOrEmpty()) {
                return false
            }
            var fileType = ".$ext"
            fileType = fileType.lowercase(Locale.getDefault())
            Log.d(TAG, "isUnknownFile type = $fileType")
            val unknownFileList = BlacklistParser.getUnknownFiles(context)
            var typeLength: Int
            if (unknownFileList != null) {
                val len = unknownFileList.size
                for (i in 0 until len) {
                    val string = unknownFileList[i]
                    if (string.contains(fileType)) {
                        val stringArray = string.split(":".toRegex()).toTypedArray()
                        typeLength = stringArray.size
                        if (typeLength == TYPE_ARRAY_LENGTH) {
                            val pkgName = stringArray[1]
                            mUnknownFileNameArray = arrayOf("")
                            mUnknownPckNameArray = pkgName.split(";".toRegex()).toTypedArray()
                            mUnknownFileType = stringArray[2].toInt()
                            return true
                        } else if (typeLength > TYPE_ARRAY_LENGTH) {
                            val unknownFileName = stringArray[1]
                            val pkgName = stringArray[2]
                            mUnknownFileNameArray =
                                unknownFileName.split(";".toRegex()).toTypedArray()
                            mUnknownPckNameArray = pkgName.split(";".toRegex()).toTypedArray()
                            mUnknownFileType = stringArray[FILE_TYPE_ARRAY_INDEX].toInt()
                            return true
                        }
                    }
                }
            }
            return false
        }
    }

    @VisibleForTesting
    fun checkIsFileManagerOpen(intent: Intent): Boolean {
        val pm = appContext.packageManager
        val resolveInfos: List<ResolveInfo> = try {
            pm.queryIntentActivities(intent, 0)
        } catch (e: Exception) {
            Log.e("isForNameFindApp: queryIntentActivities failed")
            return false
        }
        val ownerPackageName = appContext.packageName
        return resolveInfos.size == 1 && resolveInfos[0].activityInfo.packageName == ownerPackageName
    }

    @VisibleForTesting
    @SuppressLint("QueryPermissionsNeeded")
    fun foundActivitiesByIntent(
        intent: Intent,
        type: Int,
        path: String?,
        uri: Uri
    ): Boolean {
        val hasActivity: Boolean
        val pm = appContext.packageManager
        hasActivity = try {
            val resultList = pm.queryIntentActivities(intent, PackageManager.GET_ACTIVITIES)
            resultList.isNotEmpty()
        } catch (e: ActivityNotFoundException) {
            Log.e(TAG, e.message)
            false
        }
        Log.d(TAG, "foundActivitiesByIntent hasActivity $hasActivity")
        // 若无应用打开，则执行找回随心开 or openUnknownFile逻辑
        if (hasActivity) {
            return true
        }
        showRecommendIfNeed(intent, type, path, uri)
        return false
    }

    private fun showRecommendIfNeed(intent: Intent, type: Int, path: String?, uri: Uri) {
        if (SdkUtils.isAtLeastOS14Point1() && !FeatureCompat.sIsLightVersion) {
            Log.d(TAG, "doStartActivity exception -> startOpenAnyApp")
            // 14.1 及之后，引导找回文件随心开的可卸载找回
            path?.let { startOpenAnyApp(intent, type, it, uri) }
        } else {
            Log.d(TAG, "doStartActivity exception -> openUnknownFile")
            // 14.1 之前，打开推荐应用
            openUnknownFile(path, uri, intent, mIsFromRecentCardWidget, type)
        }
    }
    @VisibleForTesting
    fun wpsIntentNotHasPackage(intent: Intent): Boolean {
        return intent.`package`.isNullOrEmpty()
    }
}