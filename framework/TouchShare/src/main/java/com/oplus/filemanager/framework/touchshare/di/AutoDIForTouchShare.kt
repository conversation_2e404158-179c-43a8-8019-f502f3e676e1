/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AutoDIForTouchShare
 * * Description : 碰一碰分享的自动注入
 * * Version     : 1.0
 * * Date        : 2025/05/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.framework.touchshare.di

import androidx.annotation.Keep
import com.oplus.filemanager.framework.touchshare.TouchShareApi
import com.oplus.filemanager.interfaze.touchshare.ITouchShareApi
import org.koin.dsl.module

@Keep
object AutoDIForTouchShare {

    val touchShareModule = module {
        single<ITouchShareApi>(createdAtStart = true) {
            TouchShareApi()
        }
    }
}