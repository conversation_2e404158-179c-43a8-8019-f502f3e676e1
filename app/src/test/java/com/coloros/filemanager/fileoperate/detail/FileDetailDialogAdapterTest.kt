/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileDetailDialogAdapterTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/07/04
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  v-wanghonglei  2021/12/15       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.fileoperate.detail

import android.app.Dialog
import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.fileoperate.detail.FileDetailDialogAdapter
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class FileDetailDialogAdapterTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
    }

    @Test
    fun testIsNotFromFileBrowserDir() {
        val dialog = mockk<Dialog>()
        val fileDetailDialogAdapter = FileDetailDialogAdapter(context, dialog)
        assertTrue(fileDetailDialogAdapter.isNotFromFileBrowserDir("com.oplus.filemanager.category.album.ui.AlbumActivity"))
        assertTrue(fileDetailDialogAdapter.isNotFromFileBrowserDir("com.oplus.filemanager.category.audiovideo.ui.CategoryAudioActivity"))
        assertTrue(fileDetailDialogAdapter.isNotFromFileBrowserDir("com.oplus.filemanager.category.document.ui.DocumentActivity"))
        assertTrue(fileDetailDialogAdapter.isNotFromFileBrowserDir("com.oplus.filemanager.category.apk.ui.ApkActivity"))
        assertTrue(fileDetailDialogAdapter.isNotFromFileBrowserDir("com.filemanager.categorycompress.ui.CategoryCompressActivity"))
        assertTrue(fileDetailDialogAdapter.isNotFromFileBrowserDir("com.filemanager.superapp.ui.superapp.SuperAppActivity"))
        assertTrue(fileDetailDialogAdapter.isNotFromFileBrowserDir("com.oplus.filemanager.favorite.ui.CategoryFavoriteActivity"))
        assertFalse(fileDetailDialogAdapter.isNotFromFileBrowserDir("com.oplus.filemanager.category.album.ui.Album"))
    }
}