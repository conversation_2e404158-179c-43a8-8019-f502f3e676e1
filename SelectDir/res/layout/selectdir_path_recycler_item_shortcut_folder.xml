<?xml version="1.0" encoding="utf-8"?>
<com.filemanager.common.view.HoverAwareRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignWithParentIfMissing="true"
    android:layout_alignParentTop="true"
    android:background="@drawable/coui_preference_bg_selector"
    android:minHeight="?android:attr/listPreferredItemHeightSmall">

    <FrameLayout
        android:id="@+id/icon_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_centerVertical="true"
        android:layout_marginTop="@dimen/file_listview_icon_margin_top">

        <com.filemanager.common.view.FileThumbView
            android:id="@+id/file_list_item_icon"
            android:layout_width="@dimen/main_image_width"
            android:layout_height="@dimen/main_image_height"
            android:forceDarkAllowed="false" />
    </FrameLayout>

    <RelativeLayout
        android:id="@+id/title_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/file_list_item_info_margin"
        android:layout_marginTop="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginEnd="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginBottom="@dimen/file_list_right_layout_margin_vertical"
        android:layout_toStartOf="@+id/add_flag_tv"
        android:layout_toEndOf="@id/icon_layout">

        <com.filemanager.common.view.TextViewSnippet
            android:id="@+id/file_list_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:ellipsize="middle"
            android:maxWidth="@dimen/file_list_item_info_selected_width_new_new"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textSize="@dimen/file_list_item_title_text_size" />

        <TextView
            android:id="@+id/mark_file_list_item_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/file_list_item_title"
            android:layout_marginTop="@dimen/file_list_item_detail_margin_top"
            android:adjustViewBounds="true"
            android:ellipsize="marquee"
            android:maxWidth="@dimen/file_list_item_info_selected_width_new_new"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textColor="@color/black_55_percent"
            android:visibility="gone"
            android:textSize="@dimen/file_list_item_detail_text_size" />

        <com.filemanager.common.view.TextViewSnippet
            android:id="@+id/another_name_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/file_list_item_title"
            android:ellipsize="end"
            android:paddingTop="@dimen/file_list_item_another_name_padding_top"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:visibility="gone"
            android:textSize="@dimen/file_list_item_detail_text_size" />

    </RelativeLayout>

    <TextView
        android:id="@+id/add_flag_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:ellipsize="marquee"
        android:maxWidth="@dimen/file_list_item_info_selected_width_new_new"
        android:singleLine="true"
        android:layout_toStartOf="@id/checkbox_layout"
        android:textAppearance="@style/couiTextBodyM"
        android:textColor="?attr/couiColorLabelSecondary"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:visibility="gone"
        android:text="@string/added"
        />

    <LinearLayout
        android:id="@+id/checkbox_layout"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:orientation="horizontal"
        >

        <View
            android:id="@+id/vertical_line"
            android:layout_width="@dimen/dimen_1dp"
            android:layout_height="@dimen/dimen_24dp"
            android:background="?attr/couiColorDivider"
            android:src="@drawable/coui_btn_next" />

        <com.coui.appcompat.checkbox.COUICheckBox
            android:id="@+id/listview_scrollchoice_checkbox"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:clickable="true"
            android:focusable="true"
            android:background="@null"
            />
    </LinearLayout>

    <View
        android:id="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="0.33dp"
        android:layout_marginStart="@dimen/dimen_76dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:background="?attr/couiColorDivider"
        android:layout_alignParentBottom="true" />

</com.filemanager.common.view.HoverAwareRelativeLayout>