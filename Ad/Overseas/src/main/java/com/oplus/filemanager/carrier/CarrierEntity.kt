/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: CarrierEntity
 * * Description: 运营商的实体类
 * * Version: 1.0
 * * Date : 2024/10/31
 * * Author:chao.xue
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     chao.xue        2024/10/31      1.0            create
 ****************************************************************/
package com.oplus.filemanager.carrier

import androidx.annotation.Keep
import com.oplus.filemanager.utils.TextUtils

@Keep
data class CarrierEntity(
    var line: Int = 0, // 第几行
    @Transient
    var tam: String? = null, // tam
    @Transient
    var warZone: String? = null, // 战区
    var region: String? = null, // 区域/集团
    var regionCode: List<String>? = emptyList(), //区域二位码
    var carrier: List<String>? = emptyList(), // 运营商
    var channelName: List<String>? = emptyList(), //渠道名称
    @Transient
    var deliver: String? = null, //交付方式
    var pipelineKey: List<String>? = emptyList(), // 流水线的key
    var isSupport: Boolean = false //是否可上
) {

    companion object {
        private const val TAM_INDEX = 0
        private const val WAR_ZONE_INDEX = 1
        private const val REGION_INDEX = 2
        private const val REGION_CODE_INDEX = 3
        private const val CARRIER_INDEX = 4
        private const val CHANNEL_NAME_INDEX = 5
        private const val DELIVER_INDEX = 6
        private const val PIPELINE_KEY_INDEX = 7
        private const val IS_SUPPORT_INDEX = 8
    }

    fun setValue(columnIndex: Int, value: String) {
        when (columnIndex) {
            TAM_INDEX -> tam = value
            WAR_ZONE_INDEX -> warZone = value
            REGION_INDEX -> region = value
            REGION_CODE_INDEX -> regionCode = TextUtils.handleValue(value)
            CARRIER_INDEX -> carrier = TextUtils.handleValue(value)
            CHANNEL_NAME_INDEX -> channelName = TextUtils.handleValue(value)
            DELIVER_INDEX -> deliver = value
            PIPELINE_KEY_INDEX -> pipelineKey = TextUtils.handleValue(value)
            IS_SUPPORT_INDEX -> isSupport = TextUtils.handleSupportValue(value)
        }
    }
}