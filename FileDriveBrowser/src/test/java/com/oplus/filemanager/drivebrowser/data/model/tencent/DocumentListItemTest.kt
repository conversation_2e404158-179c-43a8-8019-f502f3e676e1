/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : DocumentListItemTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/30 19:11
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/30       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.data.model.tencent

import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem.Companion.DOCX_EXTENSION
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem.Companion.TENCENT_FILE_TYPE_DOC
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem.Companion.TENCENT_FILE_TYPE_PDF
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem.Companion.TENCENT_FILE_TYPE_SHEET
import com.oplus.filemanager.drivebrowser.data.model.tencent.DocumentListItem.Companion.TENCENT_FILE_TYPE_SLIDE
import org.junit.Assert
import org.junit.Test

class DocumentListItemTest {

    @Test
    fun testObtainExportType() {
        val docsType = "Doc"
        val docExportType = DocumentListItem.obtainExportType(docsType)
        Assert.assertEquals(TENCENT_FILE_TYPE_DOC, docExportType)

        val sheetType = "Sheet"
        val sheetExportType = DocumentListItem.obtainExportType(sheetType)
        Assert.assertEquals(TENCENT_FILE_TYPE_SHEET, sheetExportType)

        val slideType = "Slide"
        val slideExportType = DocumentListItem.obtainExportType(slideType)
        Assert.assertEquals(TENCENT_FILE_TYPE_SLIDE, slideExportType)

        val pdfType = "PDF"
        val pdfExportType = DocumentListItem.obtainExportType(pdfType)
        Assert.assertEquals(TENCENT_FILE_TYPE_PDF, pdfExportType)

        val otherType = "xx"
        val otherExportType = DocumentListItem.obtainExportType(otherType)
        Assert.assertNull(otherExportType)
    }

    @Test
    fun testObtainExtensionByExportType() {
        val docsType = "Doc"
        val docExportType = DocumentListItem.obtainExtensionByExportType(docsType)
        Assert.assertEquals(DOCX_EXTENSION, docExportType)

        val sheetType = "Sheet"
        val sheetExportType = DocumentListItem.obtainExtensionByExportType(sheetType)
        Assert.assertEquals(DocumentListItem.SHEET_EXTENSION, sheetExportType)

        val slideType = "Slide"
        val slideExportType = DocumentListItem.obtainExtensionByExportType(slideType)
        Assert.assertEquals(DocumentListItem.SLIDE_EXTENSION, slideExportType)

        val pdfType = "PDF"
        val pdfExportType = DocumentListItem.obtainExtensionByExportType(pdfType)
        Assert.assertEquals(DocumentListItem.PDF_EXTENSION, pdfExportType)

        val otherType = "xx"
        val otherExportType = DocumentListItem.obtainExtensionByExportType(otherType)
        Assert.assertNull(otherExportType)
    }
}