<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:orientation="horizontal"
    android:layout_height="@dimen/dimen_108dp"
    android:background="@drawable/bg_max_container"
    android:forceDarkAllowed="true">

    <FrameLayout
        android:id="@+id/iv_container"
        android:layout_width="@dimen/dimen_120dp"
        android:layout_height="@dimen/dimen_80dp"
        android:layout_marginStart="@dimen/dimen_14dp"
        android:layout_gravity="center_vertical"
        >
        <ImageView
            android:id="@+id/ad_max_icon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:visibility="gone"
             />

        <com.filemanager.common.view.MediaFrameLayout
            android:id="@+id/ad_max_media_container"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_80dp"
            app:layout_radius="@dimen/dimen_10dp" />
    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginEnd="@dimen/dimen_14dp"
        android:layout_marginTop="@dimen/dimen_13dp"
        android:layout_marginStart="@dimen/dimen_14dp"
        android:layout_marginBottom="@dimen/dimen_14dp">

        <include layout="@layout/ad_max_placeholder" android:visibility="gone"/>

        <com.coui.appcompat.textview.COUITextView
            android:id="@+id/ad_max_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textSize="@dimen/font_size_14"
            android:textDirection="locale"
            android:fontFamily="sans-serif-medium"
            android:textColor="@color/coui_color_primary_neutral"
            tools:text="title" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <com.coui.appcompat.textview.COUITextView
                android:id="@+id/ad_max_flag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:background="@drawable/bg_ad_flag"
                android:forceDarkAllowed="false"
                android:text="AD"
                android:textColor="@color/coui_color_secondary_neutral"
                android:textDirection="locale"
                android:textSize="@dimen/font_size_10" />

            <com.coui.appcompat.textview.COUITextView
                android:id="@+id/ad_max_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:layout_marginStart="@dimen/dimen_3dp"
                android:ellipsize="end"
                android:gravity="start"
                android:maxLines="2"
                android:textColor="@color/coui_color_secondary_neutral"
                android:textDirection="locale"
                android:textSize="@dimen/font_size_10"
                tools:text="body" />
        </LinearLayout>

        <com.coui.appcompat.button.COUIButton
            android:id="@+id/ad_max_button"
            android:layout_height="@dimen/dimen_30dp"
            android:layout_width="match_parent"
            android:textSize="@dimen/font_size_12"
            android:padding="0dp"
            android:fontFamily="sans-serif-medium"
            android:layout_gravity="bottom"
            android:gravity="center"
            android:ellipsize="end"
            app:drawableColor="@color/coui_color_primary_green"
            app:animEnable="true"
            android:text="install"
            android:lineHeight="@dimen/dimen_30dp"
            android:textColor="@android:color/white"
            app:drawableRadius="-1dp" />
    </LinearLayout>

</LinearLayout>