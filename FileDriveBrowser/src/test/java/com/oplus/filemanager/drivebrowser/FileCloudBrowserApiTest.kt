/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileCloudDriveApi
 ** Description :
 ** Version     : 1.0
 ** Date        :  2024/06/17  14:34
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/17       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser

import android.app.Activity
import com.filemanager.common.compat.FeatureCompat
import com.oplus.filemanager.interfaze.main.IMain
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module

class FileCloudBrowserApiTest {

    private val main = mockk<IMain>()

    private val koinApp = koinApplication {
        modules(module {
            single { main }
        })
    }

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        startKoin(koinApp)
    }

    @After
    fun tearDown() {
        stopKoin()
    }

    @Test
    fun `should call startActivity when call startCloudDriveFragment if isSmallScreenPhone`() {
        //setup
        mockkStatic(FeatureCompat::class)
        //given
        every { FeatureCompat.isSmallScreenPhone } returns true
        val activity = mockk<Activity>()
        every { activity.startActivity(any()) } just runs
        val itemType = 0
        val clearTask = true
        val auth = false
        //when
        FileCloudBrowserApi.startCloudDriveFragment(activity, itemType, clearTask, auth)
        //then
        verify { activity.startActivity(any()) }
        //teardown
        unmockkStatic(FeatureCompat::class)
    }

    @Test
    fun `should call startActivity when call startCloudDriveFragment if isFlipDevice`() {
        //setup
        mockkStatic(FeatureCompat::class)
        //given
        every { FeatureCompat.isSmallScreenPhone } returns false
        every { FeatureCompat.isFlipDevice } returns true
        val activity = mockk<Activity>()
        every { activity.startActivity(any()) } just runs
        val itemType = 0
        val clearTask = true
        val auth = false
        //when
        FileCloudBrowserApi.startCloudDriveFragment(activity, itemType, clearTask, auth)
        //then
        verify { activity.startActivity(any()) }
        //teardown
        unmockkStatic(FeatureCompat::class)
    }

    @Test
    fun `should call startFragment when call startCloudDriveFragment`() {
        //setup
        mockkStatic(FeatureCompat::class)
        //given
        every { FeatureCompat.isSmallScreenPhone } returns false
        every { FeatureCompat.isFlipDevice } returns false
        val activity = mockk<Activity>()
        val itemType = 0
        val clearTask = true
        val auth = false
        every { main.startFragment(activity, itemType, any()) } returns true
        //when
        FileCloudBrowserApi.startCloudDriveFragment(activity, itemType, clearTask, auth)
        //then
        verify { main.startFragment(activity, itemType, any()) }
        //teardown
        unmockkStatic(FeatureCompat::class)
    }

    @Test
    fun `should call startActivity when call startCloudDrive if isSmallScreenPhone`() {
        //setup
        mockkStatic(FeatureCompat::class)
        //given
        every { FeatureCompat.isSmallScreenPhone } returns true
        val activity = mockk<Activity>()
        every { activity.startActivity(any()) } just runs
        val itemType = 0
        val clearTask = true
        //when
        FileCloudBrowserApi.startCloudDrive(activity, itemType, clearTask)
        //then
        verify { activity.startActivity(any()) }
        //teardown
        unmockkStatic(FeatureCompat::class)
    }

    @Test
    fun `should call startActivity when call startCloudDrive if isFlipDevice`() {
        //setup
        mockkStatic(FeatureCompat::class)
        //given
        every { FeatureCompat.isSmallScreenPhone } returns false
        every { FeatureCompat.isFlipDevice } returns true
        val activity = mockk<Activity>()
        every { activity.startActivity(any()) } just runs
        val itemType = 0
        val clearTask = true
        //when
        FileCloudBrowserApi.startCloudDrive(activity, itemType, clearTask)
        //then
        verify { activity.startActivity(any()) }
        //teardown
        unmockkStatic(FeatureCompat::class)
    }

    @Test
    fun `should call startMainActivity when call startCloudDrive`() {
        //setup
        mockkStatic(FeatureCompat::class)
        //given
        every { FeatureCompat.isSmallScreenPhone } returns false
        every { FeatureCompat.isFlipDevice } returns false
        val activity = mockk<Activity>()
        every { activity.startActivity(any()) } just runs
        val itemType = 0
        val clearTask = true
        every { main.startMainActivity(any(), any()) } just runs
        //when
        FileCloudBrowserApi.startCloudDrive(activity, itemType, clearTask)
        //then
        verify { main.startMainActivity(any(), any()) }
        //teardown
        unmockkStatic(FeatureCompat::class)
    }
}