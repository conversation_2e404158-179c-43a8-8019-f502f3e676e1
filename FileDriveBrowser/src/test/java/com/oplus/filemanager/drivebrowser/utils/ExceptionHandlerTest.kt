/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ExceptionHandlerTest
 ** Description : Exception handler Unit Test
 ** Version     : 1.0
 ** Date        : 2024/03/05 11:06
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/03/05       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import okhttp3.ResponseBody.Companion.toResponseBody
import org.junit.Assert
import org.junit.Test
import retrofit2.HttpException
import retrofit2.Response
import java.io.IOException
import java.net.UnknownHostException
import java.net.UnknownServiceException

class ExceptionHandlerTest {

    @Test
    fun testIsSeverError() {
        Assert.assertFalse(ExceptionHandler.isSeverError(NullPointerException()))
        Assert.assertTrue(ExceptionHandler.isSeverError(HttpException(Response.error<String>(504, "fail".toResponseBody()))))
        Assert.assertTrue(ExceptionHandler.isSeverError(UnknownHostException()))
        Assert.assertTrue(ExceptionHandler.isSeverError(UnknownServiceException()))
        Assert.assertTrue(ExceptionHandler.isSeverError(IOException()))
    }
}