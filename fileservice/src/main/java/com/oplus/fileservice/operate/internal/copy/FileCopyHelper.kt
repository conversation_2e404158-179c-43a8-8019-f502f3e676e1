/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileCopyHelper
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/24 10:44
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/6/24       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.copy

import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.utils.Log
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.internal.utils.OnFileOperateListener
import java.io.Closeable
import java.io.File
import java.io.IOException
import java.io.RandomAccessFile
import java.nio.ByteBuffer
import java.nio.channels.FileChannel

object FileCopyHelper {

    private const val TAG = "FileCopyHelper"

    @Volatile
    private var mForceInterruptState: Byte? = null

    fun init() {
        mForceInterruptState = null
    }

    fun checkInSameFolder(sourceLists: List<FileInfo>, destDir: FileInfo): Pair<Boolean, Int> {
        if (destDir.data.isNullOrEmpty()) {
            return Pair(false, DEST_FOLDER_NOT_EXIST)
        }
        var sourceFile: File
        var errorCode: Int
        sourceLists.forEach {
            if (it.data.isNullOrEmpty().not() && it.displayName.isNullOrEmpty().not()) {
                sourceFile = File(destDir.data, it.displayName!!)
                errorCode = when {
                    sourceFile.absolutePath == it.data -> SAME_PATH
                    it.data == destDir.data -> SAME_FOLDER
                    destDir.data?.startsWith(it.displayName.plus(File.separator)) == true -> DEST_IS_SUB_FOLDER
                    else -> NOT_SAME_FOLDER
                }
                if (errorCode != -1) {
                    return Pair(true, errorCode)
                }
            }
        }
        return Pair(false, NOT_SAME_FOLDER)
    }

    fun checkTotalSize(file: File): Long {
        return if (file.isDirectory) {
            var total: Long = file.length() // include the self of this dir
            JavaFileHelper.listFiles(file)?.forEach {
                val result = checkTotalSize(it)
                total += result
            }
            total
        } else {
            file.length()
        }
    }

    fun copyFile(
        sourceFile: File,
        destFile: File,
        isExternalStorage: Boolean = false,
        listener: OnFileOperateListener
    ): Boolean {
        return if (sourceFile.isDirectory) {
            if (mForceInterruptState == null && (destFile.exists() || destFile.mkdir())) {
                listener.addProgress(sourceFile.length())
                val fileList = JavaFileHelper.listFiles(sourceFile)
                fileList?.forEach {
                    Log.i(TAG, "copyFile -> isCancel = ${listener.isCancel.invoke()}")
                    if (listener.isCancel.invoke() ||
                        !copyFile(it, File(destFile, it.name), isExternalStorage, listener)
                    ) {
                        return true
                    }
                }
                listener.onSuccess(sourceFile, destFile)
                true
            } else {
                listener.onFailure(sourceFile, destFile)
            }
        } else {
            copyFileData(sourceFile, destFile, isExternalStorage, listener)
        }
    }

    @Suppress("LongMethod")
    private fun copyFileData(
        sourceFile: File,
        destFile: File,
        isExternalStorage: Boolean = false,
        listener: OnFileOperateListener
    ): Boolean {
        Log.d(TAG, "copyFileData -> sourceFile = ${sourceFile.absolutePath}, destFile = ${destFile.absolutePath}")
        var destRAF: RandomAccessFile? = null
        var srcRAF: RandomAccessFile? = null
        var srcChannel: FileChannel? = null
        var destChannel: FileChannel? = null
        var currentLen: Long = 0
        var copyInterrupt = true
        var hasException = false
        try {
            destRAF = RandomAccessFile(destFile, "rw")
            srcRAF = RandomAccessFile(sourceFile, "r")
            srcChannel = srcRAF.channel
            destChannel = destRAF.channel
            val size: Long = srcChannel.size()
            var length = K_128
            val buff = ByteBuffer.allocate(K_64)
            if (size < Int.MAX_VALUE && !isExternalStorage) {
                while (!listener.isCancel.invoke()) {
                    if (size == currentLen) {
                        copyInterrupt = false
                        break
                    }
                    if (mForceInterruptState != null) {
                        throw InterruptedException("Force interrupted by external")
                    }
                    if (size - currentLen < length) {
                        length = size - currentLen
                    }
                    srcChannel.transferTo(currentLen, length, destChannel)
                    listener.addProgress(length)
                    currentLen += length
                }
            } else {
                while (!listener.isCancel.invoke()) {
                    val readLen = srcChannel.read(buff)
                    if (-1 == readLen) {
                        copyInterrupt = false
                        break
                    }
                    if (mForceInterruptState != null) {
                        throw InterruptedException("Force interrupted by external")
                    }
                    buff.flip()
                    destChannel.write(buff)
                    listener.addProgress(readLen.toLong())
                    buff.clear()
                    currentLen += readLen
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "copyFileData failed: ${e.message}")
            // If return true, skip this file, else cancel copy
            listener.addProgress(sourceFile.length() - currentLen)
            hasException = true
        } finally {
            if (copyInterrupt && destFile.exists()) { // Delete the dest file when copy progress interrupt
                destFile.delete()
            }
            quietClose(srcRAF)
            quietClose(srcChannel)
            quietClose(destChannel)
            quietClose(destRAF)
        }
        return when {
            listener.isCancel.invoke() -> {
                Log.d(TAG, "copyFileData failed: listener.isCancel = true")
                false
            }
            hasException -> listener.onFailure(sourceFile, destFile)
            else -> {
                listener.onSuccess(sourceFile, destFile)
                true
            }
        }
    }

    private fun quietClose(stream: Closeable?) {
        try {
            stream?.close()
        } catch (e: IOException) {
            Log.e(TAG, "quietClose has error: ${e.message}")
        }
    }

    private const val NOT_SAME_FOLDER = -1
    private const val DEST_FOLDER_NOT_EXIST = 1 shl 0
    private const val SAME_PATH = 1 shl 1
    private const val SAME_FOLDER = 1 shl 2
    private const val DEST_IS_SUB_FOLDER = 1 shl 3
    private const val K_128 = 1024 * 128L
    private const val K_64 = 1024 * 64
}