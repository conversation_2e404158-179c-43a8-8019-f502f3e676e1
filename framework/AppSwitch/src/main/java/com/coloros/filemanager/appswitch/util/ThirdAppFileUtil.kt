/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : ThirdAppFileUtil.kt
 * * Description : 获取asset目录和cache目录配置文件的工具类
 * * Version     : 1.0
 * * Date        : 2024/3/27
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.util

import android.content.Context
import android.content.res.AssetManager
import android.util.Log
import androidx.annotation.VisibleForTesting
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam
import com.filemanager.common.MyApplication.appContext
import org.apache.commons.io.IOUtils
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.InputStream
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.nio.charset.Charset

object ThirdAppFileUtil {

    const val TAG = "ThirdAppFileUtil"

    private const val ASSET_FILE_NAME = "app_switch_detect.json"
    private const val CACHE_FILE_NAME = "app_switch_detect.json"

    @VisibleForTesting
    @JvmStatic
    fun cacheFile(context: Context): File {
        return File(context.filesDir, CACHE_FILE_NAME)
    }

    @JvmStatic
    fun getConfigBetweenAssetAndCache(): ThirdAppListenConfigParam? {
        val configFromAsset = getConfigFromAsset()
        val configFromCache = getConfigFromCacheFile()
        val versionAsset = configFromAsset?.version ?: 0
        val versionCache = configFromCache?.version ?: 0
        val finalConfig = if (configFromCache == null) {
            configFromAsset
        } else {
            if (versionCache > versionAsset) {
                configFromCache
            } else {
                configFromAsset
            }
        }
        Log.d(TAG, "getConfigBetweenAssetAndCache configFromAsset $configFromAsset, configFromCache $configFromCache, " +
                "versionAsset $versionAsset, versionCache $versionCache, result $finalConfig")
        return finalConfig
    }

    @JvmStatic
    fun getConfigFromAsset(): ThirdAppListenConfigParam? {
        Log.d(TAG, "getConfigFromAsset begin")
        val assetManager: AssetManager = appContext.assets
        var inputStream: InputStream?
        val config = runCatching {
            inputStream = assetManager.open(ASSET_FILE_NAME)
            inputStream.use {
                val jsonString = IOUtils.toString(inputStream, Charset.defaultCharset())
                ThirdAppListenConfigParam.parseFromJsonString(jsonString)
            }
        }.onFailure {
            Log.e(TAG, "getConfigFromAsset error", it)
        }.getOrNull()
        Log.d(TAG, "getConfigFromAsset end config $config, config.version ${config?.version}")
        return config
    }

    @JvmStatic
    fun getConfigFromCacheFile(): ThirdAppListenConfigParam? {
        Log.d(TAG, "getConfigFromCacheFile begin")
        val context = appContext
        val scriptFile = cacheFile(context)
        if (scriptFile.exists().not()) {
            Log.w(TAG, "getConfigFromCacheFile scriptFile not exist, return null")
            return null
        }
        val result = runCatching {
            val fileInputStream = FileInputStream(scriptFile)
            val scriptReader = InputStreamReader(fileInputStream)
            scriptReader.use {
                ThirdAppListenConfigParam.parseFromReader(scriptReader)
            }
        }.onFailure {
            Log.e(TAG, "getConfigFromCacheFile: failed to from JSON, path=$scriptFile")
        }.getOrNull()
        Log.d(TAG, "getConfigFromCacheFile end, config $result, config.version ${result?.version}")
        return result
    }


    @JvmStatic
    fun checkAndSaveConfigToCacheFile(configParam: ThirdAppListenConfigParam): Boolean {
        return if (checkCanUpdateCacheConfig(configParam)) {
            saveConfigToCacheFile(configParam)
        } else {
            false
        }
    }

    @JvmStatic
    fun checkCanUpdateCacheConfig(newConfig: ThirdAppListenConfigParam): Boolean {
        val cacheConfig = getConfigFromCacheFile()
        val cacheConfigVersion = cacheConfig?.version ?: 0
        val newConfigVersion = newConfig.version
        val canUpdate = cacheConfig == null || (newConfigVersion > cacheConfigVersion)
        Log.d(TAG, "checkNeedUpdateCacheConfig newConfigVersion $newConfigVersion, cacheConfigVersion $cacheConfigVersion, " +
                    "cacheConfig $cacheConfig, result $canUpdate")
        return canUpdate
    }

    @JvmStatic
    fun saveConfigToCacheFile(configParam: ThirdAppListenConfigParam): Boolean {
        Log.d(TAG, "saveConfigToCacheFile begin")
        val context = appContext
        val scriptFile = cacheFile(context)
        val writeSuc = runCatching {
            val fileOutputStream = FileOutputStream(scriptFile)
            val scriptWritter = OutputStreamWriter(fileOutputStream)
            scriptWritter.use {
                it.write(configParam.toJsonString())
            }
            true
        }.onFailure {
            Log.e(TAG, "saveConfigToCacheFile: failed to write JSON, path=$scriptFile")
        }.getOrDefault(false)
        Log.d(TAG, "saveConfigToCacheFile end, config $configParam, scriptFile $scriptFile, result $writeSuc")
        return writeSuc
    }
}