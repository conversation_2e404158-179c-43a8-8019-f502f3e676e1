/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileDecompressObserver.kt
 ** Description: Monitor file decompress
 ** Version: 1.0
 ** Date: 2020/3/9
 ** Author: Li<PERSON>ao(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.previewcompress

import android.app.Dialog
import android.content.Context
import android.view.ContextThemeWrapper
import com.filemanager.common.view.AlertDialogFactory
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.decompress.FileDecompressObserver
import com.filemanager.fileoperate.R

const val NOT_SUPPORT_PREVIEW_COMPRESS = 100
open class FileOpenCompressObserver(context: ContextThemeWrapper) : FileDecompressObserver(context) {

    private var notSupportDialog: Dialog? = null
    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        return when (result.first) {
            NOT_SUPPORT_PREVIEW_COMPRESS -> {
                notSupportDialog =
                    AlertDialogFactory.showSingleTitleDialog(context, com.filemanager.common.R.string.toast_decompress_preview_not_support)
                onActionDone(false)
                return true
            }
            ACTION_DONE -> {
                false
            }
            else -> super.onChanged(context, result)
        }
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || notSupportDialog?.isShowing ?: false
    }
}
