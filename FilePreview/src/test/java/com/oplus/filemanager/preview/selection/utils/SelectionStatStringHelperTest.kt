package com.oplus.filemanager.preview.selection.utils

import android.content.Context
import androidx.test.platform.app.InstrumentationRegistry
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.preview.selection.data.SelectionFilesStat
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * SelectionStatStringHelper 的单元测试类
 * 用于测试 SelectionStatStringHelper 类的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SelectionStatStringHelperTest {

    // 测试上下文
    private lateinit var context: Context
    // 被测试的 SelectionStatStringHelper 实例
    private lateinit var helper: SelectionStatStringHelper
    // 模拟的 Utils 工具类
    private lateinit var mockUtils: Utils

    /**
     * 测试前的初始化方法
     * 1. 获取测试上下文
     * 2. 创建 SelectionStatStringHelper 实例
     * 3. 模拟 Utils 类的静态方法
     */
    @Before
    fun setUp() {
        // 获取测试上下文
        context = InstrumentationRegistry.getInstrumentation().targetContext
        // 创建被测试的 helper 实例
        helper = SelectionStatStringHelper(context)
        // 模拟 Utils 类的静态方法
        mockkStatic(Utils::class)
        mockUtils = mockk()
    }

    /**
     * 测试 formatFilesCount 方法
     * 验证文件数量统计字符串的格式化功能
     */
    @Test
    fun testFormatFilesCount() {
        // 测试单个项目的情况
        val stat1 = SelectionFilesStat(100, 1, emptyMap())
        val result1 = helper.formatFilesCount(stat1)
        assertEquals("1 item", result1.toString())

        // 测试多个项目的情况
        val stat2 = SelectionFilesStat(100, 5, emptyMap())
        val result2 = helper.formatFilesCount(stat2)
        assertEquals("5 items", result2.toString())
    }

    /**
     * 测试 formatStatInfo 方法
     * 验证统计信息的格式化功能
     */
    @Test
    fun testFormatStatInfo() {
        // 模拟 Utils 类的方法
        every { Utils.byteCountToDisplaySize(any()) } returns "100 KB"
        every { Utils.formatDetail(any(), any(), any()) } returns "100 KB • 2 images"

        // 创建测试数据 - 包含2个图片的统计信息
        val stat = SelectionFilesStat(102400, 1, mapOf(SelectionFilesStat.TYPE_IMAGE to 2))
        val result = helper.formatStatInfo(stat)
        assertEquals("100 KB • 2 images", result.toString())
    }

    /**
     * 测试 formatTypesStat 方法 - 空映射表的情况
     * 验证当类型统计为空时的处理
     */
    @Test
    fun testFormatTypesStat_emptyMap() {
        // 模拟 Utils 类的方法
        every { Utils.byteCountToDisplaySize(any()) } returns "100 KB"
        every { Utils.formatDetail(any(), any(), any()) } returns "100 KB • "
        
        // 创建空统计数据的测试用例
        val stat = SelectionFilesStat(0, 0, emptyMap())
        val result = helper.formatStatInfo(stat)
        assertEquals("100 KB • ", result.toString())
    }

    /**
     * 测试 formatTypesStat 方法 - 单一类型的情况
     * 验证当只有一种文件类型时的格式化结果
     */
    @Test
    fun testFormatTypesStat_singleType() {
        // 模拟 Utils 类的方法
        every { Utils.byteCountToDisplaySize(any()) } returns "100 KB"
        every { Utils.formatDetail(any(), any(), any()) } returns "100 KB • 1 image"
        
        // 创建只包含1个图片的测试用例
        val stat = SelectionFilesStat(0, 1, mapOf(SelectionFilesStat.TYPE_IMAGE to 1))
        val result = helper.formatStatInfo(stat)
        assertEquals("100 KB • 1 image", result.toString())
    }

    /**
     * 测试 formatTypesStat 方法 - 多种类型的情况
     * 验证当有多种文件类型时的格式化结果
     */
    @Test
    fun testFormatTypesStat_multipleTypes() {
        // 模拟 Utils 类的方法
        every { Utils.byteCountToDisplaySize(any()) } returns "100 KB"
        every { Utils.formatDetail(any(), any(), any()) } returns "100 KB • 1 image • 2 videos"
        
        // 创建包含1个图片和2个视频的测试用例
        val stat = SelectionFilesStat(0, 3, mapOf(
            SelectionFilesStat.TYPE_IMAGE to 1,
            SelectionFilesStat.TYPE_VIDEO to 2
        ))
        val result = helper.formatStatInfo(stat)
        assertEquals("100 KB • 1 image • 2 videos", result.toString())
    }
}