/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : AppSwitchConfig.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/27
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.bean

import android.os.Parcel
import android.os.Parcelable

data class AppSwitchConfig(
    var switchOn: Boolean = false,
    var packages: MutableList<String> = mutableListOf(),
    var activities: MutableList<String> = mutableListOf()
) : Parcelable {
    constructor(parcel: Parcel) : this() {
        parcel.readByte() != 0.toByte()
        parcel.readStringList(packages)
        parcel.readStringList(activities)
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeByte(if (switchOn) 1 else 0)
        parcel.writeStringList(packages)
        parcel.writeStringList(activities)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object {
        @JvmField
        val CREATOR = object : Parcelable.Creator<AppSwitchConfig> {
            override fun createFromParcel(parcel: Parcel): AppSwitchConfig {
                return AppSwitchConfig(parcel)
            }

            override fun newArray(size: Int): Array<AppSwitchConfig?> {
                return arrayOfNulls(size)
            }
        }
    }
}