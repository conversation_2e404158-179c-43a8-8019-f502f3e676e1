/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PersonalListFragment
 * * Description: 个人信息明示清单的fragment
 * * Version: 1.0
 * * Date : 2024/08/22
 * * Author:********
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     ********         2024/08/22      1.0            create
 ****************************************************************/
package com.filemanager.setting.ui.privacy.personal

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.WindowInsetsCompat
import com.coui.appcompat.preference.COUIPreference
import com.coui.appcompat.preference.COUIPreferenceWithAppbarFragment
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NavigationBarHelper
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.setting.R
import com.oplus.filemanager.interfaze.privacy.ICollectPrivacyApi

class PersonalListFragment : COUIPreferenceWithAppbarFragment() {

    companion object {
        private const val TAG = "PersonalListFragment"
        private const val CATEGORY_PREFERENCE_EMPTY = "category_preference_empty"
    }

    private val prefMap = mutableMapOf<String, Int>()
    private var preferencePlaceHolder: PlaceHolderPreference? = null
    private val navigationBarHelper: NavigationBarHelper by lazy { NavigationBarHelper() }
    private var showTaskBar: Boolean = false

    private fun getLayoutResource(): Int {
        return R.xml.oplus_doc_personal_list
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        (view as ViewGroup).removeView(view.findViewById(R.id.appbar_layout))
        addPreferencesFromResource(getLayoutResource())
        initPreferenceRes()
        initView()
        return view
    }

    private fun initPreferenceRes() {
        prefMap[ICollectPrivacyApi.PHONE_NUMBER] = com.filemanager.common.R.string.phone_number
        prefMap[ICollectPrivacyApi.EMAIL_ADDRESS] = com.filemanager.common.R.string.email_address

        prefMap[ICollectPrivacyApi.IMAGE_DATA] = com.filemanager.common.R.string.image_data
        prefMap[ICollectPrivacyApi.AUDIO_DATA] = com.filemanager.common.R.string.audio_data
        prefMap[ICollectPrivacyApi.VIDEO_DATA] = com.filemanager.common.R.string.video_data
        prefMap[ICollectPrivacyApi.DOCUMENT_DATA] = com.filemanager.common.R.string.document_data
        prefMap[ICollectPrivacyApi.COMPRESS_DATA] = com.filemanager.common.R.string.compress_data
        prefMap[ICollectPrivacyApi.APK_DATA] = com.filemanager.common.R.string.apk_data

        prefMap[ICollectPrivacyApi.DUID] = com.filemanager.common.R.string.duid
        prefMap[ICollectPrivacyApi.DEVICE_MODEL] = com.filemanager.common.R.string.device_model
        prefMap[ICollectPrivacyApi.DEVICE_BRAND] = com.filemanager.common.R.string.device_brand
        prefMap[ICollectPrivacyApi.OS_VERSION] = com.filemanager.common.R.string.os_version
        prefMap[ICollectPrivacyApi.CONFIG_INFO] = com.filemanager.common.R.string.config_info
        prefMap[ICollectPrivacyApi.SIM_CARD_INFO] = com.filemanager.common.R.string.sim_card_info
        prefMap[ICollectPrivacyApi.IMSI_INFO] = com.filemanager.common.R.string.imsi_info
        prefMap[ICollectPrivacyApi.IMEI] = com.filemanager.common.R.string.imei
        prefMap[ICollectPrivacyApi.SN_CODE] = com.filemanager.common.R.string.sn_code
        prefMap[ICollectPrivacyApi.OPEN_ID] = com.filemanager.common.R.string.open_id
        prefMap[ICollectPrivacyApi.ANDROID_ID] = com.filemanager.common.R.string.android_id
        prefMap[ICollectPrivacyApi.IDFA] = com.filemanager.common.R.string.idfa
        prefMap[ICollectPrivacyApi.APP_USAGE_RECORDS] = com.filemanager.common.R.string.app_usage_records
        prefMap[ICollectPrivacyApi.IP_ADDRESS] = com.filemanager.common.R.string.ip_address
        prefMap[ICollectPrivacyApi.INSTALLED_APP_LIST] = com.filemanager.common.R.string.installed_app_list
        prefMap[ICollectPrivacyApi.APP_SIGN] = com.filemanager.common.R.string.app_sign
        prefMap[ICollectPrivacyApi.SOFT_PKG] = com.filemanager.common.R.string.soft_pkg
        prefMap[ICollectPrivacyApi.ERROR_LOG_REPORT] = com.filemanager.common.R.string.error_log_report
        prefMap[ICollectPrivacyApi.BURIED_POINT_INFO] = com.filemanager.common.R.string.buried_point_info
        prefMap[ICollectPrivacyApi.FEEDBACK_CONTENT_ATTACHMENT] = com.filemanager.common.R.string.feedback_content_attachment

        prefMap[ICollectPrivacyApi.TELECOM_OPERATOR] = com.filemanager.common.R.string.telecom_operator
        prefMap[ICollectPrivacyApi.NETWORK_ENVIRONMENT] = com.filemanager.common.R.string.network_environment
    }

    private fun initView() {
        preferencePlaceHolder = findPreference(CATEGORY_PREFERENCE_EMPTY)
        prefMap.forEach { (pref, titleRes) ->
            val preference = findPreference<COUIPreference>(pref)
            preference?.setOnPreferenceClickListener {
                activity?.let { PersonalInfoDetailActivity.start(it, pref, titleRes) }
                true
            }
        }
        (activity as? PersonalListActivity)?.run {
            findViewById<View>(android.R.id.content).let {
                navigationBarHelper.addInsetsCallback(it, window) { _, insets, showNavigationBar ->
                    val systemBarInsetsBottom =
                        if (StatusBarUtil.checkIsGestureNavMode()) {
                            insets.getInsets(WindowInsetsCompat.Type.systemBars()).bottom
                        } else {
                            0
                        }
                    showTaskBar = showNavigationBar
                    Log.d(TAG, "updateWindowInsets systemBarInsetsBottom $systemBarInsetsBottom showTaskBar:$showTaskBar")
                    preferencePlaceHolder?.systemBarInsetsBottom = systemBarInsetsBottom
                }
            }
        }
    }

    override fun getTitle(): String {
        activity?.let {
            return it.title.toString()
        }
        return ""
    }
}