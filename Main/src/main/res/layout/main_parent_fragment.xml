<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout_main_parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    android:orientation="vertical"
    android:splitMotionEvents="false">

    <androidx.recyclerview.widget.COUIRecyclerView
        android:id="@+id/main_recycle_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingTop="@dimen/recyclerview_default_padding_top"
        android:paddingBottom="@dimen/recent_file_list_bottom_margin"
        android:scrollbars="none" />

    <include layout="@layout/scroll_appbar_layout" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>