<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:descendantFocusability="blocksDescendants"
    android:minHeight="@dimen/akeytomove_choice_item_min_height">

    <com.coui.appcompat.checkbox.COUICheckBox
        android:id="@+id/child_file_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/child_icon_margin_start"
        android:layout_marginEnd="@dimen/akeytomove_choice_textsize_margin_start"
        android:focusable="false"
        app:couiButton="@drawable/key_move_heytap_check_state" />

    <com.filemanager.common.view.FileThumbView
        android:id="@+id/child_file_icon"
        android:layout_width="@dimen/main_image_width"
        android:layout_height="@dimen/main_image_width"
        android:layout_centerVertical="true"
        android:layout_toEndOf="@+id/child_file_checkbox"
        android:focusable="false"
        android:forceDarkAllowed="false" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginVertical="@dimen/file_list_photo_right_layout_margin_vertical"
        android:layout_marginEnd="@dimen/file_list_photo_right_layout_margin_end"
        android:layout_toStartOf="@+id/child_photo_detail"
        android:layout_toEndOf="@+id/child_file_checkbox"
        android:orientation="vertical">

        <TextView
            android:id="@+id/child_photo_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:ellipsize="end"
            android:focusable="false"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceLarge" />

        <TextView
            android:id="@+id/child_file_size"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/file_list_item_detail_margin_top"
            android:ellipsize="end"
            android:focusable="false"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textColor="@color/black_30_percent" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/main_tip_margin_right"
        android:layout_marginEnd="@dimen/file_list_photo_right_layout_margin_end"
        android:layout_toEndOf="@+id/child_file_icon"
        android:gravity="center_vertical"
        android:minHeight="@dimen/akeytomove_choice_item_min_height"
        android:orientation="vertical">

        <TextView
            android:id="@+id/child_file_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:focusable="false"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="@color/black_85_percent"
            android:textSize="@dimen/font_size_16" />

        <TextView
            android:id="@+id/child_file_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:focusable="false"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textColor="@color/black_55_percent"
            android:textSize="@dimen/font_size_12" />
    </LinearLayout>

    <TextView
        android:id="@+id/child_photo_detail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:focusable="false"
        android:gravity="end"
        android:layout_marginEnd="@dimen/akeytomove_choice_file_size_end"
        android:layout_toStartOf="@+id/jump_photo_mark"
        android:layout_centerVertical="true"
        android:singleLine="true"
        android:textAppearance="?android:attr/textAppearanceSmall"
        android:textColor="@color/black_55_percent"
        android:textSize="@dimen/font_size_12"
        android:visibility="invisible"/>

    <ImageView
        android:id="@+id/jump_photo_mark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/akeytomove_choice_jump_margin_end"
        android:contentDescription="@null"
        android:src="@drawable/coui_btn_next"
        android:visibility="invisible" />

</RelativeLayout>