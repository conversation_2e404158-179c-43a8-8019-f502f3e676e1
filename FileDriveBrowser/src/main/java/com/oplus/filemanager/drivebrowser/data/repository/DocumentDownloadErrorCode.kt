/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DocumentDownloadErrorCode
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/16 15:46
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/16       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.repository

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class DocumentDownloadErrorCode(
    @SerializedName("value") val value: Int,
    @SerializedName("description") val description: String
) {
    override fun toString(): String {
        return "$value - $description"
    }

    override fun equals(other: Any?): Boolean {
        return other is DocumentDownloadErrorCode && other.value == value
    }

    fun description(value: String?): DocumentDownloadErrorCode {
        return if (value.isNullOrEmpty()) {
            this
        } else {
            copy(description = value)
        }
    }

    companion object {
        val DOWNLOAD_ERROR: DocumentDownloadErrorCode = DocumentDownloadErrorCode(200, "DownloadError")
        val SAVE_ERROR: DocumentDownloadErrorCode = DocumentDownloadErrorCode(201, "Save Error")
        val UN_SUPPORT_TYPE: DocumentDownloadErrorCode = DocumentDownloadErrorCode(202, "UN_SUPPORT_TYPE")
        val EXPORT_FILE_ERROR: DocumentDownloadErrorCode = DocumentDownloadErrorCode(203, "EXPORT FILE ERROR")
        val NO_PERMISSION_ERROR: DocumentDownloadErrorCode = DocumentDownloadErrorCode(204, "NO PERMISSION ERROR")
        val UNKNOWN_ERROR: DocumentDownloadErrorCode = DocumentDownloadErrorCode(205, "UNKNOWN_ERROR")

        fun unSupportRetryError(errorCode: Int): Boolean {
            return errorCode == EXPORT_FILE_ERROR.value || errorCode == NO_PERMISSION_ERROR.value || errorCode == UN_SUPPORT_TYPE.value
        }
    }
}