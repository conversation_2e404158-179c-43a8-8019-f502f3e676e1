/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : VerifyPasswordQueue
 ** Description : VerifyPassword Queue
 ** Version     : 1.0
 ** Date        : 2022/10/14
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2022/10/14      1.0        create
 ***********************************************************************/
package com.filemanager.fileoperate.decompress

import androidx.annotation.VisibleForTesting
import java.util.Objects
import java.util.concurrent.locks.Lock
import java.util.concurrent.locks.ReentrantLock

/**
 * 验证密码的队列
 * 验证密码的队列最多存放两个密码 #first 和 #last
 * 添加密码时，按照顺序 first --> last 添加满了后，继续添加的数据会设置到last中
 * 使用密码时，按照顺序 first --> last 依次出队
 *
 */
class VerifyPasswordQueue {

    // 记录当前验证的密码
    private var verifyPwd: String? = null

    @VisibleForTesting
    var first: String? = null
    @VisibleForTesting
    var last: String? = null

    private val lock: Lock = ReentrantLock()

    /**
     * 添加密码
     */
    fun addPassword(password: String?) {
        lock.lock()
        try {
            // 初次设置密码
            if (Objects.isNull(first)) {
                first = password
                verifyPwd = password
                return
            }
            // 第二次及之后设置密码
            last = password

            // 当设置的密码和当前验证的密码一致时
            if (Objects.equals(first, last)) {
                last = null
            }
        } finally {
            lock.unlock()
        }
    }

    /**
     * 判断是否还有需要验证的密码
     */
    fun hasNext(): Boolean {
        return Objects.nonNull(first)
    }

    /**
     * 验证下一个
     */
    fun next(): String? {
        val first = removeFirst()
        if (Objects.nonNull(first)) {
            verifyPwd = first
        }
        return verifyPwd
    }

    /**
     * 获取当前正在验证的密码
     */
    fun getVerifyPassword(): String? {
        return verifyPwd
    }

    /**
     * 获取当前最新的密码
     */
    fun getLastedPassword(): String? {
        if (Objects.nonNull(last)) {
            return last
        }
        if (Objects.nonNull(first)) {
            return first
        }
        return verifyPwd
    }

    /**
     *
     */
    fun size(): Int {
        if (Objects.nonNull(last)) {
            return 2
        }
        if (Objects.nonNull(first)) {
            return 1
        }
        return 0
    }

    /**
     * 取消验证
     */
    fun cancel() {
        removeLast()
    }

    /**
     * 取消所有
     */
    fun cancelAll() {
        lock.lock()
        try {
            last = null
            first = null
        } finally {
            lock.unlock()
        }
    }

    @VisibleForTesting
    fun removeFirst(): String? {
        lock.lock()
        try {
            if (Objects.nonNull(last)) {
                val result = first
                first = last
                last = null
                return result
            }
            if (Objects.nonNull(first)) {
                val result = first
                first = null
                return result
            }
            return null
        } finally {
            lock.unlock()
        }
    }

    @VisibleForTesting
    fun removeLast(): String? {
        lock.lock()
        try {
            if (Objects.nonNull(last)) {
                val result = last
                last = null
                return result
            }
            if (Objects.nonNull(first)) {
                val result = first
                first = null
                return result
            }
            return null
        } finally {
            lock.unlock()
        }
    }
}