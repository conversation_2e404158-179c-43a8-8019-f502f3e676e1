package com.oplus.ota.upgrade

import android.app.Activity
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PropertyCompat
import io.mockk.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * OtaUtils工具类的单元测试类
 * 用于测试OtaUtils中与OTA升级相关的功能
 */
@OptIn(ExperimentalCoroutinesApi::class)
class OtaUtilsTest {

    // 使用标准测试调度器来控制协程的执行
    private val testDispatcher = StandardTestDispatcher()
    
    /**
     * 测试前的准备工作
     * 1. 设置主调度器为测试调度器
     * 2. 模拟PropertyCompat和FeatureCompat对象
     */
    @Before
    fun setUp() {
        Dispatchers.setMain(testDispatcher)  // 设置主调度器为测试调度器
        mockkObject(PropertyCompat)  // 模拟PropertyCompat对象
        mockkObject(FeatureCompat)  // 模拟FeatureCompat对象
    }
    
    /**
     * 测试后的清理工作
     * 1. 重置主调度器
     * 2. 取消所有模拟对象
     */
    @After
    fun tearDown() {
        Dispatchers.resetMain()  // 重置主调度器
        unmockkAll()  // 取消所有模拟对象
    }
    
    /**
     * 测试isOzipFile方法
     * 场景：当传入有效的ozip文件且扫描成功时返回true
     */
    @Test
    fun `isOzipFile returns true when valid ozip file and scanning succeeds`() = runTest {
        // Given - 准备测试数据
        every { PropertyCompat.sIsNeedUpdateByOta } returns true  // 模拟需要OTA更新
        every { FeatureCompat.sIsSupportEncryptOTAOnly } returns false  // 模拟不支持仅加密OTA
        val file = BaseFileBean().apply { mData = "test.ozip" }  // 创建测试文件对象
        
        // 模拟UpgradeVersionCheck类的构造函数和scanningPackage方法
        mockkConstructor(UpgradeVersionCheck::class)
        every { anyConstructed<UpgradeVersionCheck>().scanningPackage(any()) } returns PackageInfo()
        
        // When - 执行测试方法
        val result = OtaUtils.isOzipFile(file)
        
        // Then - 验证结果
        assertFalse(result) // 根据源码逻辑，当sIsSupportEncryptOTAOnly为false时，会检查文件扩展名是否为ozip，如果不是则返回false
    }
}