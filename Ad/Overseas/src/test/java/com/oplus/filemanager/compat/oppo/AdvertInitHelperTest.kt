/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: AdvertInitHelperTest.kt
 ** Description: unit test
 ** Version: 1.0
 ** Date : 2022/7/26
 ** Author: YanShengNeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * YanShengNeng 2022/7/14      1.0    create
 ****************************************************************/
package com.oplus.filemanager.compat.oppo

import android.content.Context
import android.provider.Settings
import com.oplus.filemanager.compat.brand.oppo.AdvertInitHelper
import com.oplus.filemanager.utils.GetRusDataUtils
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AdvertInitHelperTest {
    @MockK
    lateinit var context: Context

    @MockK
    lateinit var advertInitHelper: AdvertInitHelper

    companion object {
        private const val FILEMANAGER_AD_SUPPORT = "com.oplus.filemanager.ad_support"
        private const val REGION_FEATURE = "com.oplus.filemanager.ad_region"
        private const val PACKAGE_NAME = "com.coloros.filemanager"
    }

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        every { context.contentResolver } answers { mockk() }
        mockkStatic(Settings.System::class)
    }

    @After
    fun after() {
        unmockkStatic(Settings.System::class)
    }

    @Test
    fun `should false when call isAdEnabled if enable is 1`() {
        every { Settings.System.getInt(any(), any(), any()) } answers { 1 }   //AdvertInitHelper.RECOMMEND_ENABLED
        every { advertInitHelper.isHighRamDevice() } returns false
        every { advertInitHelper.isAdEnabled(context) } answers { callOriginal() }
        Assert.assertFalse(advertInitHelper.isAdEnabled(context))
    }

    @Test
    fun `should false when call isAdEnabled if enable is 2`() {
        every { Settings.System.getInt(any(), any(), any()) } answers { 2 }   //AdvertInitHelper.RECOMMEND_ENABLED
        Assert.assertFalse(AdvertInitHelper().isAdEnabled(context))
    }

    @Test
    fun `should false when call isAdEnabled if enable is 1 and not high ram`() {
        every { Settings.System.getInt(any(), any(), any()) } answers { 1 }   //AdvertInitHelper.RECOMMEND_ENABLED
        every { advertInitHelper.isAdEnabled(context) } answers { callOriginal() }
        every { advertInitHelper.isHighRamDevice() } returns false
        Assert.assertFalse(advertInitHelper.isAdEnabled(context))
    }

    @Test
    fun `should false when call isAdEnabled if enable is 1 and high ram and not provideEnable`() {
        every { Settings.System.getInt(any(), any(), any()) } answers { 1 }   //AdvertInitHelper.RECOMMEND_ENABLED
        every { advertInitHelper.isAdEnabled(context) } answers { callOriginal() }
        every { advertInitHelper.isHighRamDevice() } returns true
        every { advertInitHelper.adProvidesEnabled(context) } returns false
        Assert.assertFalse(advertInitHelper.isAdEnabled(context))
    }

    @Test
    fun `should false when call isAdEnabled if enable is 1 and high ram and provideEnable and not supportAdRegion`() {
        every { Settings.System.getInt(any(), any(), any()) } answers { 1 }   //AdvertInitHelper.RECOMMEND_ENABLED
        every { advertInitHelper.isAdEnabled(context) } answers { callOriginal() }
        every { advertInitHelper.isHighRamDevice() } returns true
        every { advertInitHelper.adProvidesEnabled(context) } returns true
        every { advertInitHelper.isSupportAdRegion(context) } returns false
        Assert.assertFalse(advertInitHelper.isAdEnabled(context))
    }

    @Test
    fun `should false when call isAdEnabled if enable is 1 and high ram and provideEnable and supportAdRegion and not getRusEnabled`() {
        every { Settings.System.getInt(any(), any(), any()) } answers { 1 }   //AdvertInitHelper.RECOMMEND_ENABLED
        every { advertInitHelper.isAdEnabled(context) } answers { callOriginal() }
        every { advertInitHelper.isHighRamDevice() } returns true
        every { advertInitHelper.adProvidesEnabled(context) } returns true
        every { advertInitHelper.isSupportAdRegion(context) } returns true
        every { Settings.System.getInt(any(), any(), any()) } answers { 0 }
        Assert.assertFalse(advertInitHelper.isAdEnabled(context))
    }

    @Test
    fun `should true when call isAdEnabled if enable is 1 and high ram and provideEnable and supportAdRegion and getRusEnabled`() {
        every { Settings.System.getInt(any(), any(), any()) } answers { 1 }   //AdvertInitHelper.RECOMMEND_ENABLED
        every { advertInitHelper.isAdEnabled(context) } answers { callOriginal() }
        every { advertInitHelper.isHighRamDevice() } returns true
        every { advertInitHelper.adProvidesEnabled(context) } returns true
        every { advertInitHelper.isSupportAdRegion(context) } returns true
        every { Settings.System.getInt(any(), any(), any()) } answers { GetRusDataUtils.FILE_AD_STATUS_OPEN }
        Assert.assertTrue(advertInitHelper.isAdEnabled(context))
    }

    @Test
    fun `should return CN when getRegion if support`() {
        every { advertInitHelper.isFileManagerFeatureSupport(context, FILEMANAGER_AD_SUPPORT) } returns false
        every { advertInitHelper.getSystemRegion() } answers { "CN" }
        every { advertInitHelper.getRegion(context) } answers { callOriginal() }
        Assert.assertEquals("CN", advertInitHelper.getRegion(context))
    }

    @Test
    fun `should return CN when getRegion if not support`() {
        every { advertInitHelper.isFileManagerFeatureSupport(context, FILEMANAGER_AD_SUPPORT) } returns false
        every { advertInitHelper.isFileManagerFeatureSupport(context, REGION_FEATURE) } returns false
        every { advertInitHelper.getSystemRegion() } answers { "CN" }
        every { advertInitHelper.getRegion(context) } answers { callOriginal() }
        Assert.assertEquals("CN", advertInitHelper.getRegion(context))
    }

    @Test
    fun `should return CN when getRegion if support and feature`() {
        every { advertInitHelper.isFileManagerFeatureSupport(context, FILEMANAGER_AD_SUPPORT) } returns false
        every { advertInitHelper.isFileManagerFeatureSupport(context, REGION_FEATURE) } returns true
        every { advertInitHelper.getFeatureString(context) } answers { "CN" }
        every { advertInitHelper.getRegion(context) } answers { callOriginal() }
        Assert.assertEquals("CN", advertInitHelper.getRegion(context))
    }

    @Test
    fun `should return CN when getRegion if support and throw exception`() {
        every { advertInitHelper.isFileManagerFeatureSupport(context, FILEMANAGER_AD_SUPPORT) } returns false
        every { advertInitHelper.isFileManagerFeatureSupport(context, REGION_FEATURE) } returns true
        every { advertInitHelper.getFeatureString(context) } throws Exception()
        every { advertInitHelper.getRegion(context) } answers { callOriginal() }
        Assert.assertEquals("CN", advertInitHelper.getRegion(context))
    }

    @Test
    fun `should true when isSupportAdRegion if support`() {
        every { advertInitHelper.isFileManagerFeatureSupport(context, FILEMANAGER_AD_SUPPORT) } returns true
        every { advertInitHelper.isSupportAdRegion(context) } answers { callOriginal() }
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
    }

    @Test
    fun `should true when isSupportAdRegion if not support and in region`() {
        every { advertInitHelper.isFileManagerFeatureSupport(context, FILEMANAGER_AD_SUPPORT) } returns false
        every { advertInitHelper.isSupportAdRegion(context) } answers { callOriginal() }
        every { advertInitHelper.getRegion(context) } returns "MM"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "VN"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "TH"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "KH"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "PH"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "ID"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "IN"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "SG"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "MY"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "mm"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "vn"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "th"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "kh"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "ph"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "id"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "in"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "sg"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
        every { advertInitHelper.getRegion(context) } returns "my"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
    }

    @Test
    fun `should true when isSupportAdRegion if not support and not in region`() {
        every { advertInitHelper.isFileManagerFeatureSupport(context, FILEMANAGER_AD_SUPPORT) } returns false
        every { advertInitHelper.isSupportAdRegion(context) } answers { callOriginal() }
        every { advertInitHelper.getRegion(context) } returns "CN"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
    }

    @Test
    fun `should true when isSupportAdRegion if not support and not in region and in setting region`() {
        every { advertInitHelper.isFileManagerFeatureSupport(context, FILEMANAGER_AD_SUPPORT) } returns false
        every { advertInitHelper.isSupportAdRegion(context) } answers { callOriginal() }
        every { Settings.System.getString(context.contentResolver, "enabled_regions_setting") } returns "CN,MY"
        every { advertInitHelper.getRegion(context) } returns "CN"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
    }

    @Test
    fun `should true when isSupportAdRegion if not support and not in region and not in setting region`() {
        every { advertInitHelper.isFileManagerFeatureSupport(context, FILEMANAGER_AD_SUPPORT) } returns false
        every { advertInitHelper.isSupportAdRegion(context) } answers { callOriginal() }
        every { Settings.System.getString(context.contentResolver, "enabled_regions_setting") } returns "IN,MY"
        every { advertInitHelper.getRegion(context) } returns "CN"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
    }

    @Test
    fun `should true when isSupportAdRegion if not support and not in region and not throw exception`() {
        every { advertInitHelper.isFileManagerFeatureSupport(context, FILEMANAGER_AD_SUPPORT) } returns false
        every { advertInitHelper.isSupportAdRegion(context) } answers { callOriginal() }
        every { Settings.System.getString(context.contentResolver, "enabled_regions_setting") } throws Exception("")
        every { advertInitHelper.getRegion(context) } returns "CN"
        Assert.assertTrue(advertInitHelper.isSupportAdRegion(context))
    }

    @Test
    fun `should true when adProvidesEnabled and provides enable and support ad region`() {
        every { context.packageName } returns PACKAGE_NAME
        every { advertInitHelper.isAdProvidesEnabled(context, PACKAGE_NAME) } returns true
        every { advertInitHelper.isSupportAdRegion(context) } returns true
        every { advertInitHelper.adProvidesEnabled(context) } answers { callOriginal() }
        justRun { advertInitHelper.setAdProvidesEnabled(context, PACKAGE_NAME, true) }
        Assert.assertTrue(advertInitHelper.adProvidesEnabled(context))
        verify(inverse = true) { advertInitHelper.setAdProvidesEnabled(context, PACKAGE_NAME, true) }
    }

    @Test
    fun `should true when adProvidesEnabled and provides enable and not support ad region`() {
        every { context.packageName } returns PACKAGE_NAME
        every { advertInitHelper.isAdProvidesEnabled(context, PACKAGE_NAME) } returns true
        every { advertInitHelper.isSupportAdRegion(context) } returns false
        every { advertInitHelper.adProvidesEnabled(context) } answers { callOriginal() }
        justRun { advertInitHelper.setAdProvidesEnabled(context, PACKAGE_NAME, false) }
        Assert.assertFalse(advertInitHelper.adProvidesEnabled(context))
        verify { advertInitHelper.setAdProvidesEnabled(context, PACKAGE_NAME, false) }
    }

    @Test
    fun `should true when adProvidesEnabled and not provides enable and support ad region`() {
        every { context.packageName } returns PACKAGE_NAME
        every { advertInitHelper.isAdProvidesEnabled(context, PACKAGE_NAME) } returns false
        every { advertInitHelper.isSupportAdRegion(context) } returns false
        every { advertInitHelper.adProvidesEnabled(context) } answers { callOriginal() }
        justRun { advertInitHelper.setAdProvidesEnabled(context, PACKAGE_NAME, false) }
        Assert.assertFalse(advertInitHelper.adProvidesEnabled(context))
        verify(inverse = true) { advertInitHelper.setAdProvidesEnabled(context, PACKAGE_NAME, false) }
    }

    @Test
    fun `should true when adProvidesEnabled and not provides enable and not support ad region`() {
        every { context.packageName } returns PACKAGE_NAME
        every { advertInitHelper.isAdProvidesEnabled(context, PACKAGE_NAME) } returns false
        every { advertInitHelper.isSupportAdRegion(context) } returns true
        every { advertInitHelper.adProvidesEnabled(context) } answers { callOriginal() }
        justRun { advertInitHelper.setAdProvidesEnabled(context, PACKAGE_NAME, true) }
        Assert.assertFalse(advertInitHelper.adProvidesEnabled(context))
        verify { advertInitHelper.setAdProvidesEnabled(context, PACKAGE_NAME, true) }
    }
}