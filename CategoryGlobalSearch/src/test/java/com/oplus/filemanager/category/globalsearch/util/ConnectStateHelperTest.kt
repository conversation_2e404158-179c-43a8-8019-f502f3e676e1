/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ConnectStateHelperTest
 ** Description : ConnectStateHelperTest Unit Test
 ** Version     : 1.0
 ** Date        : 2023/2/21
 ** Author      : wanghonglei
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  wanghonglei     2023/2/21      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.util

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.NewFunctionSwitch
import com.oplus.filemanager.category.globalsearch.manager.filter.ConnectState
import com.oplus.filemanager.category.globalsearch.manager.filter.ConnectStateHelper
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConditionManager
import com.oplus.filemanager.dfm.DFMManager
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals

class ConnectStateHelperTest {

    private val callback: ConnectStateHelper.ConnectStateChangeCallback =
        object : ConnectStateHelper.ConnectStateChangeCallback {
            override fun onConnectStateChanaged(connectState: ConnectState) {
                println("do nothing")
            }
        }

    @Before
    fun setup() {
        mockkObject(MyApplication)
        val context: Context = mockk(relaxed = true)
        every { MyApplication.appContext }.returns(context)

        mockkStatic(NewFunctionSwitch::class)
        every { NewFunctionSwitch.isSupportDfmSearch } returns true

        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.isExternalSdMounted(context) } returns true
        every { VolumeEnvironment.getExternalOTGState(context, false) } returns true
        every { VolumeEnvironment.isSingleSdcard(context) } returns false

        mockkStatic(DFMManager::class)
        every { DFMManager.checkStateFileAvalable() } returns true
        every { DFMManager.registerDfmStateChangeListener(any()) } returns Unit
        every { DFMManager.unRegisterDfmStateChangeListener(any()) } returns Unit

        mockkObject(FilterConditionManager)
        every { FilterConditionManager.updateConnectState(any()) } returns Unit
    }

    @After
    fun after() {
        unmockkObject(MyApplication)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(DFMManager::class)
        unmockkStatic(FilterConditionManager::class)
    }


    @Test
    fun `should initAndRegister case`() {
        //given
        ConnectStateHelper.initAndRegister()
        //then
        assertEquals(true, ConnectStateHelper.connectState != null)
        assertEquals(true, ConnectStateHelper.dfmStateChangeListener != null)
        assertEquals(true, ConnectStateHelper.mountReceiver != null)

        verify { ConnectStateHelper.registerChangeCallback() }
        verify { FilterConditionManager.updateConnectState(any()) }
    }


    @Test
    fun `should release case`() {
        //given
        ConnectStateHelper.release()
        //then
        assertEquals(true, ConnectStateHelper.connectState == null)
        assertEquals(true, ConnectStateHelper.dfmStateChangeListener == null)
        assertEquals(true, ConnectStateHelper.mountReceiver == null)

        verify { ConnectStateHelper.unregisterChangeCallback() }
    }

    @Test
    fun `should registerConnectStateChangeCallback case`() {
        //given
        ConnectStateHelper.registerConnectStateChangeCallback(callback)
        //then
        assertEquals(true, ConnectStateHelper.callBacks.size > 0)
    }

    @Test
    fun `should unRegisterConnectStateChangeCallback case`() {
        //given
        ConnectStateHelper.unRegisterConnectStateChangeCallback(callback)
        //then
        assertEquals(true, ConnectStateHelper.callBacks.size == 0)
    }


    @Test
    fun `should clearCallbacks case`() {
        //given
        ConnectStateHelper.clearCallbacks()
        //then
        assertEquals(true, ConnectStateHelper.callBacks.size == 0)
    }
}