<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/color_transparent"
    android:layoutDirection="locale"
    android:padding="@dimen/drag_shadow_item_layout_padding">

    <LinearLayout
        android:layout_width="@dimen/drag_shadow_item_layout_width"
        android:layout_height="@dimen/drag_shadow_item_layout_height"
        android:layout_marginTop="@dimen/drag_shadow_item_layout_margin_top"
        android:layout_marginEnd="@dimen/drag_shadow_item_layout_margin_end"
        android:background="@drawable/dragshadow_layout_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="0dp">

        <com.filemanager.common.view.FileThumbView
            android:id="@+id/dragshadow_item_cover"
            android:layout_width="@dimen/drag_shadow_item_cover_width"
            android:layout_height="@dimen/drag_shadow_item_cover_width"
            android:layout_marginStart="@dimen/drag_shadow_item_cover_margin_start"
            android:layout_marginTop="@dimen/drag_shadow_item_cover_margin_top"
            android:layout_marginBottom="@dimen/drag_shadow_item_cover_margin_bottom"
            android:forceDarkAllowed="false" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/drag_shadow_item_details_margin_start"
            android:layout_marginTop="@dimen/drag_shadow_item_details_margin_top"
            android:layout_marginEnd="@dimen/file_list_right_layout_margin_vertical"
            android:layout_marginBottom="@dimen/drag_shadow_item_details_margin_bottom"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/dragshadow_item_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="title"
                android:textAlignment="viewStart"
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:textColor="@color/black_has_night"
                android:textSize="@dimen/drag_shadow_item_title_size" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/dragshadow_item_details"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/file_list_item_detail_margin_top"
                android:adjustViewBounds="true"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="date"
                android:textAlignment="viewStart"
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:textColor="@color/black_55_percent"
                android:textSize="@dimen/drag_shadow_item_date_size" />
        </LinearLayout>
    </LinearLayout>

    <com.coui.appcompat.reddot.COUIHintRedDot
        android:id="@+id/dragshadow_item_count"
        style="?attr/couiHintRedDotStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:couiHeight="@dimen/red_dot_height"
        app:couiCornerRadius="@dimen/red_dot_radius"
        app:couiSmallWidth="@dimen/red_dot_small_width"
        app:couiMediumWidth="@dimen/red_dot_medium_width"
        app:couiLargeWidth="@dimen/red_dot_large_width"
        android:layout_gravity="end|top"
        app:couiHintRedDotColor="@color/coui_color_label_theme_blue" />

</FrameLayout>