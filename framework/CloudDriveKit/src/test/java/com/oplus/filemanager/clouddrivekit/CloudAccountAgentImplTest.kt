package com.oplus.filemanager.clouddrivekit

import android.content.Context
import com.filemanager.common.utils.Injector
import com.heytap.cloudkit.libcommon.account.CloudAccount
import com.heytap.cloudkit.libcommon.account.ICloudAccountCallback
import com.oplus.filemanager.interfaze.heytapaccount.IHeytapAccount
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.module
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * CloudAccountAgentImpl的单元测试类
 * 使用Robolectric测试框架进行Android环境下的单元测试
 * 使用MockK框架进行mock对象模拟
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class CloudAccountAgentImplTest {

    // 被测试对象
    private lateinit var cloudAccountAgentImpl: CloudAccountAgentImpl
    // 模拟的Context对象
    private val mockContext = mockk<Context>(relaxed = true)
    // 模拟的HeytapAccount接口对象
    private val mockHeytapAccount = mockk<IHeytapAccount>()
    // 模拟的回调接口对象
    private val mockCallback = mockk<ICloudAccountCallback>(relaxed = true)

    /**
     * 测试前的初始化方法
     * 1. 启动Koin依赖注入框架
     * 2. 配置测试模块
     * 3. 创建被测试对象实例
     */
    @Before
    fun setUp() {
        startKoin {
            modules(module {
                single { mockHeytapAccount }
            })
        }
        cloudAccountAgentImpl = CloudAccountAgentImpl(mockContext)
    }

    /**
     * 测试后的清理方法
     * 1. 停止Koin框架
     * 2. 清除所有mock对象
     */
    @After
    fun tearDown() {
        stopKoin()
        clearAllMocks()
        unmockkAll()
    }

    /**
     * 测试getSignInAccount方法
     * 场景：当Heytap账号可用时，应返回包含正确信息的账号
     */
    @Test
    fun `getSignInAccount should return account with correct info when heytap account is available`() {
        // Given - 准备测试数据
        val expectedToken = "test_token"
        val expectedUserId = "test_user_id"
        val expectedUsername = "test_username"
        // 设置mock对象的行为
        every { mockHeytapAccount.getUserToken(mockContext) } returns expectedToken
        every { mockHeytapAccount.getUserId(mockContext) } returns expectedUserId
        every { mockHeytapAccount.getUserName(mockContext) } returns expectedUsername
        every { mockHeytapAccount.isLogin(mockContext) } returns true

        // When - 执行被测试方法
        cloudAccountAgentImpl.getSignInAccount(mockCallback)

        // Then - 验证结果
        verify(exactly = 1) {
            mockCallback.onComplete(withArg { account ->
                // 验证返回的账号信息是否正确
                assert(account.token == expectedToken)
                assert(account.userId == expectedUserId)
                assert(account.username == expectedUsername)
                assert(account.isLogin)
            })
        }
    }

    /**
     * 测试getSignInAccount方法
     * 场景：当Heytap账号为null时，应返回空账号
     */
    @Test
    fun `getSignInAccount should return empty account when heytap account is null`() {
        // Given - 模拟Injector返回null
        mockkObject(Injector)
        every { Injector.injectFactory<IHeytapAccount>() } returns null

        // When - 执行被测试方法
        cloudAccountAgentImpl.getSignInAccount(mockCallback)

        // Then - 验证返回的账号信息是否为空
        verify(exactly = 1) {
            mockCallback.onComplete(withArg { account ->
                assert(account.token == null)
                assert(account.userId == null)
                assert(account.username == null)
                assert(!account.isLogin)
            })
        }
    }

    /**
     * 测试reqSignInAccount方法
     * 场景：当Heytap账号为null时，应不执行任何操作
     */
    @Test
    fun `reqSignInAccount should do nothing when heytap account is null`() {
        // Given - 模拟Injector返回null
        mockkObject(Injector)
        every { Injector.injectFactory<IHeytapAccount>() } returns null

        // When - 执行被测试方法
        cloudAccountAgentImpl.reqSignInAccount(mockCallback)

        // Then - 验证回调方法没有被调用
        verify(exactly = 0) { mockCallback.onComplete(any()) }
    }
}

/**
 * 辅助类，用于匹配回调参数类型
 * 模拟CloudAccount类的结构
 */
class TransitionCloudAccount {
    var token: String? = null
    var isLogin: Boolean = false
    var resultCode: Int = -1
    var resultMsg: String? = null
    var userId: String? = null
    var username: String? = null
    var avatar: String? = null
    var status: Int = -1
}