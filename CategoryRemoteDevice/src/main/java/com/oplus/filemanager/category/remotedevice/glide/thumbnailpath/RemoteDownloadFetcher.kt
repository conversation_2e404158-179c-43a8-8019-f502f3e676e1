/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemotePathFetcher.kt
 ** Description: RemotePathFetcher
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.category.remotedevice.glide.thumbnailpath

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.bumptech.glide.Priority
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.data.DataFetcher
import com.filemanager.common.bean.remotedevice.ThumbnailInfo
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.interfaze.remotedevice.IRemoteThumbnail
import com.oplus.filemanager.interfaze.remotedevice.IRemoteThumbnailListener
import java.io.File
import java.util.Base64

class RemoteDownloadFetcher(
    private val context: Context,
    private val downloadRemoteFileBean: DownloadRemoteFileBean,
    private val width: Int,
    private val height: Int
) : DataFetcher<Bitmap> {

    private companion object {
        private const val TAG = "RemoteThumbnailPathFetcher"
    }

    @Volatile
    private var isCancelled = false
    private var bitmap: Bitmap? = null
    override fun loadData(priority: Priority, callback: DataFetcher.DataCallback<in Bitmap>) {
        if (isCancelled) {
            //return null when request has been cancelled
            callback.onDataReady(null)
            return
        }
        val totalPath = downloadRemoteFileBean.originalPath
        Log.i(TAG, "remoteThumbnailGlideBean path: $totalPath")
        Injector.injectFactory<IRemoteThumbnail>()
            ?.getFileThumbnails(getDeviceId(), totalPath, width, height,
                object : IRemoteThumbnailListener {
                    override fun onResult(thumbnailInfo: ThumbnailInfo) {
                        val remoteBase64 = thumbnailInfo.thumbnail
                        Log.i(TAG, "remoteBase64 : $remoteBase64")
                        if (remoteBase64?.isNotEmpty() == true) {
                            val decodedBytes = Base64.getDecoder().decode(remoteBase64)
                            bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)
                            callback.onDataReady(bitmap)
                        } else {
                            callback.onDataReady(null)
                        }
                    }
                }
            )
    }

    private fun createDir(picFile: File) {
        if (picFile.parentFile?.exists()?.not() == true) {
            picFile.parentFile?.mkdirs()
        }
    }

    private fun getDeviceId(): String {
        var devicesId = ""
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        val currentLinkInfo = remoteDeviceApi?.getCurrentLinkedRemoteDiceInfo()
        if (currentLinkInfo != null) {
            devicesId = currentLinkInfo.deviceId.toString()
        }
        return devicesId
    }

    override fun cleanup() {
        bitmap?.recycle()
    }

    override fun cancel() {
        isCancelled = true
        bitmap = null
    }

    override fun getDataClass(): Class<Bitmap> {
        return Bitmap::class.java
    }

    override fun getDataSource(): DataSource {
        return DataSource.REMOTE
    }
}