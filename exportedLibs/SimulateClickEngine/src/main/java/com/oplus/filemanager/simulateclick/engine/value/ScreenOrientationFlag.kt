/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScreenOrientationFlag.kt
 * Description:
 *     The flag for configuring orientation type in Script.
 *
 * Version: 1.0
 * Date: 2024-06-05
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><EMAIL>    2024-06-05   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.value

import com.google.gson.GsonBuilder
import com.oplus.filemanager.simulateclick.engine.utils.convertAliasToFlagValue
import com.oplus.filemanager.simulateclick.engine.utils.convertFlagValueToAlias

internal data class ScreenOrientationFlag(override val flag: Int = DEFAULT) : IScriptFlagValue {

    constructor(aliasValue: String?) : this(fromAlias(aliasValue))

    override fun toAlias(): String = toAlias(flag)

    override fun toString(): String = "ScreenOrientationFlag(flag=${toAlias(flag)})"

    companion object {
        const val PORTRAIT = 0x0001
        const val LANDSCAPE = 0x0002
        private const val DEFAULT = PORTRAIT or LANDSCAPE

        private const val ALIAS_PORTRAIT = "portrait"
        private const val ALIAS_LANDSCAPE = "landscape"

        @JvmStatic
        private fun fromAlias(aliasValue: String?): Int = convertAliasToFlagValue(
            aliasValue,
            DEFAULT,
            ALIAS_PORTRAIT to PORTRAIT,
            ALIAS_LANDSCAPE to LANDSCAPE
        )

        @JvmStatic
        private fun toAlias(flag: Int): String = convertFlagValueToAlias(
            flag,
            ALIAS_PORTRAIT to PORTRAIT,
            ALIAS_LANDSCAPE to LANDSCAPE
        )

        @JvmStatic
        fun registerJsonAdapter(gsonBuilder: GsonBuilder) {
            gsonBuilder.registerTypeAdapter(ScreenOrientationFlag::class.java, JsonAdapter())
        }
    }

    class JsonAdapter : ScriptJsonAdapter<ScreenOrientationFlag>() {
        override fun onWrite(value: ScreenOrientationFlag): String = value.toAlias()
        override fun onRead(jsonStr: String): ScreenOrientationFlag = ScreenOrientationFlag(jsonStr)
    }
}