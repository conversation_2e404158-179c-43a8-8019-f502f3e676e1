/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.superapp.ui.superapp.SuperParentFragment
 * * Description : 父子级开发，原先在Activity中的Tab，放到Fragment中，再重复调用 此Fragment于SuperActivity及MainActivity
 * * Version     : 1.0
 * * Date        : 2022/11/2
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.superapp.ui.superapp

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.ActionBar
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.tablayout.COUITab
import com.coui.appcompat.tablayout.COUITabLayout
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseFragmentAdapter
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewOperate
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.helper.uiconfig.type.ScreenFoldConfig
import com.filemanager.common.helper.uiconfig.type.ScreenOrientationConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.OnGetUIInfoListener
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.view.ViewPagerWrapperForPC
import com.filemanager.common.view.viewpager.RTLViewPager
import com.filemanager.superapp.R
import com.filemanager.superapp.ui.superapp.SuperAppActivity.Companion.ACTION_EXTERNAL_APP_KEY_PACKAGE_NAME
import com.filemanager.superapp.ui.utils.obtainCategoryItem
import com.filemanager.superappsource.helper.SuperAppHelper
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

private const val TAG = "SuperAppParentFragment"

class SuperAppParentFragment : BaseVMFragment<SuperAppParentFragmentViewModel>(), TabActivityListener<BaseFileBean>,
    COUITabLayout.OnTabSelectedListener, IPreviewListFragment, OnGetUIInfoListener {

    companion object {
        private const val ALL_POSITION = 0
        private const val IMAGE_POSITION = 1
        private const val VIDEO_POSITION = 2
        private const val AUDIO_POSITION = 3
        private const val DOC_POSITION = 4
        private const val OTHER_POSITION = 5
    }

    private var mToolbar: COUIToolbar? = null
    private var mRootLayout: ViewGroup? = null
    var mAppBarLayout: COUIDividerAppBarLayout? = null
    private var mTitleResId: Int = -1
    private var mTabView: COUITabLayout? = null
    private var mViewPager: RTLViewPager? = null
    private var mViewPagerWrapper: ViewPagerWrapperForPC? = null
    var sortEntryView: SortEntryView? = null
    private var mTitle: String? = null
    private var mViewModel: SuperAppParentFragmentViewModel? = null
    private var isEmpty: Boolean = false
    private var sideCategoryType = -1
    var isFirstEnter = true
    private var scrollHelper: DragScrollHelper? = null

    /**
     *  if change language ,app title will be change ,
     *  so we should use packageName to get app name once more
     */
    private var mSuperAppPackage: String? = null
    private var mPosition: Int = 0
    private val mTabTitles = intArrayOf(Constants.TAB_ALL, Constants.TAB_IMAGE, Constants.TAB_VIDEO,
        Constants.TAB_AUDIO, Constants.TAB_DOCUMENT, Constants.TAB_OTHER)
    private var mTabTitle = arrayListOf(
        appContext.resources.getString(com.filemanager.common.R.string.total),
        appContext.resources.getString(com.filemanager.common.R.string.string_photos),
        appContext.resources.getString(com.filemanager.common.R.string.string_videos),
        appContext.resources.getString(com.filemanager.common.R.string.string_audio),
        appContext.resources.getString(com.filemanager.common.R.string.string_documents),
        appContext.resources.getString(com.filemanager.common.R.string.string_other))
    private var mPages: ArrayList<SuperListFragment> = ArrayList()
    private var mPathArray: Array<String>? = null
    private var mDirDepth: Int = SuperLoader.DEFAULT_DIR_DEPTH
    private var externalPath: String? = ""
    private var mNeedLoadData = false
    private var isChildDisplay = false
    private var mNeedSkipAnimation: Boolean = false
    private var tempSortType = -1
    private var tempSortDesc = -1
    private var isFromOnPause: Boolean = false
        get() {
            return field.also {
                field = false
            }
        }

    private var previewOperate: IPreviewOperate? = null

    override fun getLayoutResId(): Int {
        return R.layout.super_app_parent_fragment
    }

    override fun pressBack(): Boolean {
        return (getCurrentFragment() as? OnBackPressed)?.pressBack() == true
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        return getCurrentFragment()?.onNavigationItemSelected(item) ?: false
    }

    override fun getScanMode(): Int {
        return mViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
    }

    override fun setScanMode(mode: Int) {
        mViewModel?.mBrowseModeState?.value = mode
    }

    override fun isSelectionMode(): Boolean {
        return getCurrentFragment()?.isInSelectMode() ?: false
    }

    override fun setPreviewOperate(operate: IPreviewOperate) {
        previewOperate = operate
    }

    override fun onMenuItemSelected(item: MenuItem): Boolean {
        return getCurrentFragment()?.onMenuItemSelected(item) ?: false
    }

    @SuppressLint("RestrictedApi")
    override fun initView(view: View) {
        if (previewOperate?.isSupportPreview() != true) {
            mToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        }
        rootView = view.findViewById(R.id.coordinator_layout)
        toolbar = mToolbar
        mAppBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        mTabView = view.findViewById(com.filemanager.common.R.id.tab_layout)
        mViewPager = view.findViewById(R.id.viewPager)
        mViewPagerWrapper = view.findViewById(R.id.view_pager_wrapper)
        mRootLayout = view.findViewById(R.id.coordinator_layout)
        sortEntryView = view.findViewById(com.filemanager.common.R.id.sort_entry_view)
        sortEntryView?.setClickSortListener {
            val menu = ActionMenuItem(view.context, 0, R.id.navigation_sort, 0, 0, "")
            onOptionsItemSelected(menu)
        }
        mViewPagerWrapper?.notifyMainViewPager = object : ((Boolean) -> Unit) {
            override fun invoke(enable: Boolean) {
                baseVMActivity?.let {
                    val mainAction = Injector.injectFactory<IMain>()
                    mainAction?.setViewPagerScrollEnabled(it, enable)
                }
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return getCurrentFragment()?.onMenuItemSelected(item) ?: super.onOptionsItemSelected(item)
    }

    override fun initData(savedInstanceState: Bundle?) {
        val fragments = childFragmentManager.fragments
        for (i in mTabTitles.indices) {
            initFragment(i, fragments)
        }
        initViewPager()
        initToolbar()
        if (mViewModel == null) {
            mViewModel = ViewModelProvider(baseVMActivity ?: this)[SuperAppParentFragmentViewModel::class.java]
        }
        startScanModeObserver()
        startSideNavigationStatusObserver()
        if (mNeedLoadData) {
            mViewModel?.let { onResumeLoadData() }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        initArguments()
    }

    private fun initArguments() {
        val bundle = arguments ?: return
        mTitleResId = bundle.getInt(Constants.TITLE_RES_ID)
        mTitle = if (mTitleResId > 0) {
            context?.getString(mTitleResId)
        } else {
            bundle.getString(Constants.TITLE, context?.getString(com.filemanager.common.R.string.string_documents))
        }
        externalPath = bundle.getString(KtConstants.FILE_PATH)
        mPathArray = bundle.getStringArray(KtConstants.P_SUPER_PATH_LIST)
        mSuperAppPackage = bundle.getString(KtConstants.P_PACKAGE)
        mDirDepth = bundle.getInt(KtConstants.SUPER_DIR_DEPTH)
        mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
        isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
        tempSortType = bundle.getInt(Constants.TEMP_SORT_TYPE, -1)
        tempSortDesc = bundle.getInt(Constants.TEMP_SORT_DESC, -1)
        //中大屏幕切换来源需要更新sideCategoryType
        val bundleCategoryType = bundle.getInt(Constants.SIDE_CATEGORY_TYPE, -1)
        if (sideCategoryType != -1 && bundleCategoryType != sideCategoryType) {
            rootView?.apply {
                super.setFragmentViewDragTag(this)
                mPages?.forEach { fragment -> fragment.updateSideCategoryType(bundleCategoryType) }
            }
        }
        sideCategoryType = bundleCategoryType
    }

    override fun startObserve() {
    }

    override fun getFragmentInstance(): Fragment {
        return this
    }

    override fun setFragmentArguments(arguments: Bundle?) {
        this.arguments = arguments
    }

    override fun setPreviewToolbar(toolbar: COUIToolbar?) {
        mToolbar = toolbar
    }

    /**
     * 首页，中屏，分屏时，重复打开不同的文件来源路径，调用此方法重新加载数据
     */
    override fun onResumeLoadData() {
        initArguments()
        refreshByNewIntent(switchToMain = false)
        val bundle = arguments ?: return
        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
            baseVMActivity?.apply {
                setSupportActionBar(mToolbar)
                baseVMActivity?.supportActionBar?.apply {
                    setDisplayHomeAsUpEnabled(!isChildDisplay)
                    setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
                }
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
    }

    private fun initToolbar() {
        mToolbar?.apply {
            menu.clear()
            isTitleCenterStyle = false
            title = mTitle
            inflateMenu(R.menu.super_app_menu)
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this, isChildDisplay)
        }
        baseVMActivity?.apply {
            setSupportActionBar(mToolbar)
            displayActionIcon(supportActionBar)
        }
        if (previewOperate?.isSupportPreview() != true) {
            mRootLayout?.apply {
                setPadding(paddingLeft,
                    COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom)
            }
        }
        if ((mTabView != null) && (mViewPager != null)) {
            mTabView?.let {
                it.setupWithViewPager(mViewPager, false)
                it.addOnTabSelectedListener(this)
                it.isUpdateindicatorposition = true
            }
        }
    }

    /**
     * 显示标题栏返回按钮，分屏时无返回按钮
     */
    private fun displayActionIcon(bar: ActionBar?) {
        bar?.let {
            it.setDisplayHomeAsUpEnabled(!isChildDisplay)
            it.setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
        }
    }

    override fun refreshScanModeItemIcon(withAnimation: Boolean) {
        Log.d(TAG, "refreshScanModeItemIcon withAnimation=$withAnimation")
        mToolbar?.menu?.findItem(R.id.actionbar_scan_mode)?.let {
            val resources = appContext.resources
            val desc: String
            val resId: Int = if (mViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                desc = resources.getString(com.filemanager.common.R.string.palace_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
            } else {
                desc = resources.getString(com.filemanager.common.R.string.list_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
            }
            it.contentDescription = desc
            setScanModeStatue(it, desc, withAnimation, resId)
        }
    }

    override fun updateNeedSkipAnimation(withAnimation: Boolean) {
        mNeedSkipAnimation  = withAnimation
    }

    private fun setScanModeStatue(
        toolbar: MenuItem,
        desc: String,
        needSkipAnimation: Boolean,
        resId: Int
    ) {
        if (baseVMActivity?.sideNavigationStatus?.value == KtConstants.LIST_SELECTED_MODE
            && !isEmpty && isChildDisplay
        ) {
            toolbar.setIcon(null)
            toolbar.setTitle(desc)
            toolbar.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
        } else {
            toolbar.setTitle(null)
            if (needSkipAnimation) {
                toolbar.setIcon(resId)
            } else {
                KtAnimationUtil.updateMenuItemWithFadeAnimate(toolbar, resId, baseVMActivity)
            }
            toolbar.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
        }
    }

    private fun initFragment(position: Int, fragments: List<Fragment>): Fragment {
        var fragment = if (fragments.isEmpty()) null else fragments[position]
        if (fragment == null) {
            fragment = SuperListFragment()
            val bundle = Bundle()
            bundle.putInt(KtConstants.P_TAB_POSITION, mTabTitles[position])
            bundle.putString(KtConstants.P_PACKAGE, mSuperAppPackage)
            bundle.putStringArray(SuperListFragment.SUPER_LIST_PATH, mPathArray)
            bundle.putInt(Constants.TITLE_RES_ID, mTitleResId)
            bundle.putInt(KtConstants.SUPER_DIR_DEPTH, mDirDepth)
            bundle.putInt(Constants.TEMP_SORT_TYPE, tempSortType)
            bundle.putInt(Constants.TEMP_SORT_DESC, tempSortDesc)
            bundle.putString(KtConstants.FILE_PATH, externalPath)
            bundle.putInt(Constants.SIDE_CATEGORY_TYPE, sideCategoryType)
            if (mNeedLoadData) {
                mNeedLoadData = position == Constants.TAB_ALL
            }
            bundle.putBoolean(KtConstants.P_NEED_LOAD_DATA, mNeedLoadData)
            fragment.arguments = bundle
        }
        if (fragment is SuperListFragment) {
            fragment.setToolbarNew(mToolbar, mTitle ?: "")
            fragment.setTabActivityListener(this@SuperAppParentFragment)
            mPages.add(fragment)
        }
        return fragment
    }

    private fun initViewPager() {
        mViewPager?.let {
            it.offscreenPageLimit = mTabTitles.size
            it.adapter = ViewPagerFragmentStateAdapter(this)
            it.currentItem = 0
            it.overScrollMode = View.OVER_SCROLL_NEVER
        }
    }

    override fun onTabReselected(p0: COUITab?) {}

    override fun onTabUnselected(tab: COUITab?) {}

    override fun onTabSelected(tab: COUITab?) {
        tab?.let {
            mPosition = it.position
            refreshCurrentFragment()
            val page = OptimizeStatisticsUtil.signalToPage(mTitle ?: "")
            val opTab = when (mPosition) {
                ALL_POSITION -> "all"
                IMAGE_POSITION -> "image"
                AUDIO_POSITION -> "audio"
                VIDEO_POSITION -> "video"
                DOC_POSITION -> "doc"
                OTHER_POSITION -> "other"
                else -> ""
            }
            OptimizeStatisticsUtil.pageTab(page, opTab)
        }
    }

    inner class ViewPagerFragmentStateAdapter(fragment: Fragment) : BaseFragmentAdapter(fragment) {
        override fun createFragment(position: Int): Fragment {
            return mPages[position]
        }

        override fun getItemCount(): Int {
            return mTabTitle.size
        }

        override fun getPageTitle(position: Int): CharSequence {
            return mTabTitle[position]
        }
    }

    private fun startScanModeObserver() {
        mViewModel?.mBrowseModeState?.observe(this) {
            delay { refreshScanModeItemIcon(mNeedSkipAnimation.not()) }
        }
    }

    private fun startSideNavigationStatusObserver() {
        baseVMActivity?.sideNavigationStatus?.observe(this) { status ->
            Log.d(TAG, "sideNavigationStatus observe: $status")
            toolbar?.let {
                refreshScanModeItemIcon()
                setToolbarEditIcon(it, isChildDisplay)
            }
        }
    }

    fun refreshCurrentFragment() {
        Log.d(TAG, "refreshCurrentFragment:$mViewModel  $this")
        mViewModel?.let {
            getCurrentFragment()?.onResumeLoadData()
        }
    }

    private fun getCurrentFragment(): SuperListFragment? {
        return getPositionFragment(mPosition)
    }

    private fun getPositionFragment(position: Int): SuperListFragment? {
        return if (position < mPages.size) {
            mPages[mPosition]
        } else {
            null
        }
    }

    fun getCurrentTabPosition(): Int {
        return if (mPosition < mTabTitles.size) {
            mTabTitles[mPosition]
        } else {
            -1
        }
    }

    override fun onCreateOptionsMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.super_app_menu, menu)
        mToolbar?.apply {
            refreshScanModeItemIcon()
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this, isChildDisplay)
        }
    }

    /**
     * 分屏与否，显示不同的标题栏选项，子屏时，不显示搜索及设置
     */
    private fun setToolbarMenuVisible(toolbar: COUIToolbar, visible: Boolean) {
        toolbar.menu.findItem(R.id.action_setting)?.isVisible = visible
    }

    private fun setToolbarEditIcon(toolbar: COUIToolbar, isChildDisplay: Boolean) {
        toolbar.menu.findItem(R.id.actionbar_edit)?.let {
            if (baseVMActivity?.sideNavigationStatus?.value == KtConstants.LIST_NORMAL_MODE
                && !isEmpty && isChildDisplay
            ) {
                it.setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_edit)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            } else {
                it.setIcon(null)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            }
        }
    }

    private fun showEditMode() {
        mViewPager?.isUserInputEnabled = false
        mTabView?.isEnabled = false
        mViewPagerWrapper?.setEditMode(true)
    }

    fun disableViewPager() {
        mViewPager?.isUserInputEnabled = false
    }

    private fun exitEditMode() {
        mViewPager?.isUserInputEnabled = true
        mTabView?.isEnabled = true
        mViewPagerWrapper?.setEditMode(false)
    }

    override fun initToolbarSelectedMode(
        needInit: Boolean,
        realFileSize:
        Int,
        selectedFileSize: Int,
        selectItems: ArrayList<BaseFileBean>
    ) {
        mToolbar?.let {
            if (needInit && (mTabView?.isInEditMode != true)) {
                it.menu.clear()
                it.isTitleCenterStyle = true
                it.inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
            }
            showEditMode()
            val isSelectAll = (realFileSize == selectedFileSize)
            ToolbarUtil.updateToolbarTitle(it, selectedFileSize, isSelectAll)
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble(
                (selectedFileSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems),
                hasAndroidData = AndroidDataHelper.hasAndroidDataFile(selectItems)
            )
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
        baseVMActivity?.supportActionBar?.setDisplayHomeAsUpEnabled(false)
    }

    override fun initToolbarNormalMode(needInit: Boolean, empty: Boolean) {
        exitEditMode()
        isEmpty = empty
        mToolbar?.apply {
            if (needInit) {
                menu.clear()
                isTitleCenterStyle = false
                title = mTitle
                inflateMenu(R.menu.super_app_menu)
            }

            menu.findItem(R.id.actionbar_edit)?.isVisible = empty.not()
            menu.findItem(R.id.navigation_sort)?.isEnabled = empty.not()
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this, isChildDisplay)
            previewOperate?.onToolbarMenuUpdated(menu)
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }

        baseVMActivity?.let {
            displayActionIcon(it.supportActionBar)
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mPages.forEach {
            it.onUIConfigChanged(configList)
        }
        configList.forEach {
            if ((it is ScreenFoldConfig) || (it is ScreenOrientationConfig)) {
                mViewPager?.setCurrentItem(mPosition, false)
                return
            }
        }
    }
    override fun onPause() {
        super.onPause()
        isFromOnPause = true
    }

    override fun onDestroy() {
        super.onDestroy()
        mPages.clear()
    }

    fun refreshByNewIntent(switchToMain: Boolean = true) {
        mSuperAppPackage?.let {
            mViewModel?.mSuperAppPackage = it
        }
        mToolbar?.apply {
            title = mTitle
        }
        //update data of every page
        for (fragment in mPages) {
            fragment.updatePathAndPackage(mPathArray, mSuperAppPackage)
        }
        mNeedSkipAnimation = true
        if (isFromOnPause) {
            mViewModel?.let {
                if (mPages.size > 0) {
                    mViewPager?.currentItem?.let {
                        if (it < mPages.size) {
                            mPages[it].onResumeLoadData()
                        }
                    }
                }
            }
            isFromOnPause = false
            return
        }
        mViewModel?.let {
            if (mPages.size > 0) {
                mViewPager?.currentItem?.let {
                    if (it < mPages.size && mPages[it].getViewModel()?.isInSelectMode() == true && !isPreviewOpen()) {
                        //之前调用 resetState() 修复的问题：在微信子级页面进入编辑模式，点击父级的“下载”，没有退出编辑模式。
                        mPages[it].getViewModel()?.resetState()
                    }
                }
                if (switchToMain) {
                    mViewPager?.currentItem = 0
                    mPages[0].onResumeLoadData()
                } else {
                    refreshCurrentFragment()
                }
            }
        }
    }

    fun findAndCheckSourceBeanByIntent(intent: Intent): MainCategoryItemsBean? {
        val sourceBean = SuperAppHelper.findSourceBeanFromDocAction(
            appContext,
            IntentUtils.getString(intent, SuperAppActivity.ACTION_SOURCES_DOC_KEY_SOURCE_PATH)
        )
        sourceBean?.let {
            mTitle = it.name
            mPathArray = it.fileList
            mSuperAppPackage = it.packageName
        } ?: run {
            findAndCheckRecentCardSourceBeanByIntent(intent)
        }
        return sourceBean
    }

    /**
     * 通过包名找应用
     * */
    fun findSourceBean(intent: Intent): MainCategoryItemsBean? {
        val sourceBean = SuperAppHelper.findSourceBeanByThirdPkgName(
            appContext, intent.getStringExtra(
                ACTION_EXTERNAL_APP_KEY_PACKAGE_NAME
            )
        )
        sourceBean?.let {
            mTitle = it.name
            mPathArray = it.fileList
            mSuperAppPackage = it.packageName
        } ?: run {
            findAndCheckRecentCardSourceBeanByIntent(intent)
        }
        return sourceBean
    }

    private fun findAndCheckRecentCardSourceBeanByIntent(intent: Intent): MainCategoryItemsBean? {
        val categoryItems = SuperAppHelper.getCategoryItems(MyApplication.sAppContext)
        val cardType = IntentUtils.getString(intent, Constants.KEY_CARD_TYPE)
        Log.d(TAG, "findAndCheckRecentCardSourceBeanByIntent -> cardType = $cardType")
        if (cardType.isNullOrEmpty().not()) {
            val categoryId = cardType?.toInt() ?: -1
            Log.d(TAG, "findAndCheckRecentCardSourceBeanByIntent -> categoryId = $categoryId")
            val sourceAppName = IntentUtils.getString(intent, Constants.KEY_SOURCE_APP_NAME)
            Log.d(TAG, "findAndCheckRecentCardSourceBeanByIntent -> sourceAppName = $sourceAppName")
            if (sourceAppName.isNullOrEmpty().not()) {
                StatisticsUtils.onCommon(appContext, StatisticsUtils.RECENT_FILE_CARD_CLICK)
            }
            obtainCategoryItem(categoryItems, categoryId, sourceAppName ?: "")?.let {
                mTitle = it.name
                mPathArray = it.fileList
                mSuperAppPackage = it.packageName
            } ?: run {
                CustomToast.showShort(com.filemanager.common.R.string.could_find_any_files)
            }
            intent.putExtra(Constants.TEMP_SORT_TYPE, SortHelper.FILE_TIME_REVERSE_ORDER)
            intent.putExtra(Constants.TEMP_SORT_DESC, 0)
            tempSortType = SortHelper.FILE_TIME_REVERSE_ORDER
            tempSortDesc = 0
        }
        return null
    }

    fun updateIntentData(title: String?, pathArray: Array<String>?, dirDepth: Int, superAppPackage: String?, externalPath: String?) {
        mTitle = title
        mPathArray = pathArray
        mDirDepth = dirDepth
        mSuperAppPackage = superAppPackage
        this.externalPath = externalPath
    }

    fun fastSmoothScrollToTop() {
        getCurrentFragment()?.getRecyclerView()?.fastSmoothScrollToTop()
    }

    override fun fromSelectPathResult(code: Int, paths: List<String>?) {
        getCurrentFragment()?.fromSelectPathResult(code, paths)
    }

    override fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.P_CHILD_DISPLAY, isChildDisplay)
        mToolbar?.let {
            setToolbarMenuVisible(it, !isChildDisplay)
            setToolbarEditIcon(it, isChildDisplay)
            refreshScanModeItemIcon()
        }
        baseVMActivity?.supportActionBar?.apply {
            if (getCurrentFragment()?.isInSelectMode() == true) {
                setDisplayHomeAsUpEnabled(true)
                setHomeAsUpIndicator(com.support.snackbar.R.drawable.coui_menu_ic_cancel)
            } else {
                setDisplayHomeAsUpEnabled(!isChildDisplay)
            }
        }
    }

    override fun checkPermission() {
        baseVMActivity?.let {
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.checkPermission(it)
        }
    }

    override fun backToTop() {
        getCurrentFragment()?.let {
            it.getRecyclerView()?.fastSmoothScrollToTop()
        }
    }

    override fun updatedLabel() {}

    override fun permissionSuccess() {}

    override fun setCurrentFromOtherSide(currentPath: String) {}

    override fun getCurrentPath(): String {
        return ""
    }

    override fun setPermissionEmptyVisible(visible: Int) {
        mPages.forEach {
            it.setPermissionEmptyVisible(visible)
        }
    }

    fun previewClickedFile(file: BaseFileBean?, clickFileLiveData: MutableLiveData<BaseFileBean?>?): Boolean {
        return previewOperate?.previewClickedFile(file, clickFileLiveData) ?: false
    }

    fun previewEditedFiles(files: List<BaseFileBean>?): Boolean {
        getCurrentFragment()?.mFileOperateController?.setPreviewOpen(isPreviewOpen())
        return previewOperate?.previewEditedFiles(files) ?: false
    }

    fun listEmptyFile() {
        previewOperate?.listEmptyFile()
    }

    fun isPreviewOpen(): Boolean {
        return previewOperate?.isPreviewOpen() ?: false
    }

    override fun getFragmentCategoryType(): Int {
        if (sideCategoryType == -1) {
            return CategoryHelper.CATEGORY_SOURCE_GROUP
        } else {
            return sideCategoryType
        }
    }

    fun getFilePaths(): Array<String>? {
        return mPathArray
    }

    override fun getOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.OperateResultListener? =
        getCurrentFragment()?.mFileOperateController?.pickResultListener()

    override fun exitSelectionMode() {
        getCurrentFragment()?.exitSelectionMode()
    }

    override fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        return getCurrentFragment()?.onSideNavigationClicked(isOpen) ?: false
    }

    override fun updateLeftRightMargin() {
        getCurrentFragment()?.updateLeftRightMargin()
    }

    override fun isEmptyList(): Boolean {
        return getCurrentFragment()?.isEmptyList() ?: false
    }

    override fun handleDragScroll(event: DragEvent): Boolean {
        if (scrollHelper == null) {
            scrollHelper = DragScrollHelper(getRecyclerView())
        }
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    override fun resetScrollStatus() {
        scrollHelper?.resetDragStatus()
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        val selectedFiles = DragUtils.getSelectedFiles()
        val itemViewList = ArrayList<View>()
        val fileList = getCurrentFragment()?.getFileList()
        val size = fileList?.size ?: return null
        selectedFiles?.forEach { fileBean ->
            val indexOf = fileList.indexOf(fileBean) ?: return null
            if (indexOf >= 0 && indexOf < size) {
                val itemView =
                    getRecyclerView()?.findViewHolderForAdapterPosition(indexOf)?.itemView
                itemView?.let { itemViewList.add(it) }
            }
        }
        return itemViewList
    }

    override fun setNavigateItemAble() {
        activity?.lifecycle?.coroutineScope?.launch(Dispatchers.Main) {
            val selectItems = getCurrentFragment()?.getSelectItems()
            val selectItemSize = selectItems?.size ?: 0
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble((selectItemSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems))
        }
    }

    override fun getViewModel(): ViewModel? {
        return null
    }

    override fun getRecyclerView(): RecyclerView? {
        return getCurrentFragment()?.getRecyclerView()
    }
}