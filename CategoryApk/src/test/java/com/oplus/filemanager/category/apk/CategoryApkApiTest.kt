package com.oplus.filemanager.category.apk

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.fragment.app.Fragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.filepreview.IPreviewListFragmentCreator
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.category.apk.ui.ApkActivity
import com.oplus.filemanager.interfaze.main.IMain
import io.mockk.*
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.Shadows
import org.robolectric.annotation.Config
import org.junit.Assert.*
import com.oplus.filemanager.category.apk.provider.requestUnInstallApkCount
import org.robolectric.RobolectricTestRunner
import io.mockk.MockKAnnotations

/**
 * CategoryApkApi的单元测试类
 * 用于测试CategoryApkApi对象的各种功能方法
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class CategoryApkApiTest {

    // 使用MockK框架模拟的Activity对象
    @MockK
    private lateinit var mockActivity: Activity

    // 使用MockK框架模拟的Fragment对象
    @MockK
    private lateinit var mockFragment: Fragment

    // 使用MockK框架模拟的PreviewCombineFragment对象
    @MockK
    private lateinit var mockPreviewFragment: PreviewCombineFragment

    // 使用MockK框架模拟的Menu对象
    @MockK
    private lateinit var mockMenu: Menu

    // 使用MockK框架模拟的MenuItem对象
    @MockK
    private lateinit var mockMenuItem: MenuItem

    // 使用MockK框架模拟的MenuInflater对象
    @MockK
    private lateinit var mockMenuInflater: MenuInflater

    // 使用MockK框架模拟的IMain接口对象
    @MockK
    private lateinit var mockMain: IMain

    // 测试用的协程调度器
    private val testDispatcher = TestCoroutineDispatcher()

    /**
     * 测试前的初始化方法
     * 1. 初始化所有MockK注解的字段
     * 2. 设置主调度器为测试调度器
     * 3. 模拟Injector对象
     * 4. 模拟Log类
     * 5. 模拟UriHelper对象
     * 6. 模拟ApkFileProviderKt中的顶层函数
     */
    @Before
    fun setup() {
        MockKAnnotations.init(this)  // 初始化所有@MockK注解的字段
        Dispatchers.setMain(testDispatcher)
        mockkObject(Injector)
        mockkStatic(Log::class)
        mockkObject(UriHelper)
        mockkStatic("com.oplus.filemanager.category.apk.provider.ApkFileProviderKt")  // 模拟顶层函数
    }

    /**
     * 测试后的清理方法
     * 1. 重置主调度器
     * 2. 清理测试协程
     * 3. 解除所有模拟
     */
    @After
    fun tearDown() {
        Dispatchers.resetMain()
        testDispatcher.cleanupTestCoroutines()
        unmockkAll()
    }

    /**
     * 测试getUnInstallApkCount方法
     * 验证是否能正确返回requestUnInstallApkCount的结果
     */
    @Test
    fun `getUnInstallApkCount should return bundle from requestUnInstallApkCount`() {
        // Given - 准备测试数据：预期返回的Bundle对象
        val expectedBundle = Bundle().apply { putInt("count", 5) }
        coEvery { requestUnInstallApkCount() } returns expectedBundle

        // When - 执行测试方法
        val result = CategoryApkApi.getUnInstallApkCount("arg", Bundle())

        // Then - 验证结果
        assertEquals(expectedBundle, result)
        coVerify { requestUnInstallApkCount() }
    }

    /**
     * 测试getFragment方法
     * 验证是否能正确返回包含ApkParentFragment创建器的PreviewCombineFragment
     */
    @Test
    fun `getFragment should return PreviewCombineFragment with ApkParentFragment creator`() {
        // When - 执行测试方法
        val fragment = CategoryApkApi.getFragment(mockActivity)

        // Then - 验证返回的Fragment类型和日志记录
        assertTrue(fragment is PreviewCombineFragment)
        verify { Log.d("CategoryApkApi", "getFragment") }
    }

    /**
     * 测试onResumeLoadData方法
     * 验证是否能正确委托给PreviewCombineFragment的onResumeLoadData方法
     */
    @Test
    fun `onResumeLoadData should delegate to PreviewCombineFragment`() {
        // Given - 设置模拟对象的行为
        every { mockPreviewFragment.onResumeLoadData() } just Runs

        // When - 执行测试方法
        CategoryApkApi.onResumeLoadData(mockPreviewFragment)

        // Then - 验证方法调用和日志记录
        verify { mockPreviewFragment.onResumeLoadData() }
        verify { Log.d("CategoryApkApi", "onResumeLoadData") }
    }

    /**
     * 测试onCreateOptionsMenu方法
     * 验证是否能正确委托给PreviewCombineFragment的onCreateOptionsMenu方法
     */
    @Test
    fun `onCreateOptionsMenu should delegate to PreviewCombineFragment`() {
        // Given - 设置模拟对象的行为
        every { mockPreviewFragment.onCreateOptionsMenu(any(), any()) } just Runs

        // When - 执行测试方法
        CategoryApkApi.onCreateOptionsMenu(mockPreviewFragment, mockMenu, mockMenuInflater)

        // Then - 验证方法调用
        verify { mockPreviewFragment.onCreateOptionsMenu(mockMenu, mockMenuInflater) }
    }

    /**
     * 测试onMenuItemSelected方法
     * 验证是否能正确委托给PreviewCombineFragment的onMenuItemSelected方法
     */
    @Test
    fun `onMenuItemSelected should delegate to PreviewCombineFragment`() {
        // Given - 设置模拟对象的行为
        every { mockPreviewFragment.onMenuItemSelected(any()) } returns true

        // When - 执行测试方法
        val result = CategoryApkApi.onMenuItemSelected(mockPreviewFragment, mockMenuItem)

        // Then - 验证返回值和日志记录
        assertTrue(result)
        verify { mockPreviewFragment.onMenuItemSelected(mockMenuItem) }
        verify { Log.d("CategoryApkApi", "onMenuItemSelected") }
    }

    /**
     * 测试pressBack方法
     * 验证是否能正确委托给PreviewCombineFragment的pressBack方法
     */
    @Test
    fun `pressBack should delegate to PreviewCombineFragment`() {
        // Given - 设置模拟对象的行为
        every { mockPreviewFragment.pressBack() } returns true

        // When - 执行测试方法
        val result = CategoryApkApi.pressBack(mockPreviewFragment)

        // Then - 验证返回值和日志记录
        assertTrue(result)
        verify { mockPreviewFragment.pressBack() }
        verify { Log.d("CategoryApkApi", "pressBack") }
    }

    /**
     * 测试onNavigationItemSelected方法
     * 验证是否能正确委托给PreviewCombineFragment的onNavigationItemSelected方法
     */
    @Test
    fun `onNavigationItemSelected should delegate to PreviewCombineFragment`() {
        // Given - 设置模拟对象的行为
        every { mockPreviewFragment.onNavigationItemSelected(any()) } returns true

        // When - 执行测试方法
        val result = CategoryApkApi.onNavigationItemSelected(mockPreviewFragment, mockMenuItem)

        // Then - 验证返回值和日志记录
        assertTrue(result)
        verify { mockPreviewFragment.onNavigationItemSelected(mockMenuItem) }
        verify { Log.d("CategoryApkApi", "onNavigationItemSelected") }
    }

    /**
     * 测试setIsHalfScreen方法
     * 验证是否能正确委托给PreviewCombineFragment的setIsHalfScreen方法
     */
    @Test
    fun `setIsHalfScreen should delegate to PreviewCombineFragment`() {
        // Given - 设置模拟对象的行为
        every { mockPreviewFragment.setIsHalfScreen(any()) } just Runs

        // When - 执行测试方法
        CategoryApkApi.setIsHalfScreen(mockPreviewFragment, CategoryHelper.CATEGORY_APK, true)

        // Then - 验证方法调用
        verify { mockPreviewFragment.setIsHalfScreen(true) }
    }

    /**
     * 测试fromSelectPathResult方法
     * 验证是否能正确委托给PreviewCombineFragment的fromSelectPathResult方法
     */
    @Test
    fun `fromSelectPathResult should delegate to PreviewCombineFragment`() {
        // Given - 准备测试数据和设置模拟对象的行为
        val testPaths = listOf("/path1", "/path2")
        every { mockPreviewFragment.fromSelectPathResult(any(), any()) } just Runs

        // When - 执行测试方法
        CategoryApkApi.fromSelectPathResult(mockPreviewFragment, 100, testPaths)

        // Then - 验证方法调用和日志记录
        verify { mockPreviewFragment.fromSelectPathResult(100, testPaths) }
        verify { Log.d("CategoryApkApi", "fromSelectPathResult") }
    }

    /**
     * 测试updateLabels方法
     * 验证是否能正确委托给PreviewCombineFragment的updatedLabel方法
     */
    @Test
    fun `updateLabels should delegate to PreviewCombineFragment`() {
        // Given - 设置模拟对象的行为
        every { mockPreviewFragment.updatedLabel() } just Runs

        // When - 执行测试方法
        CategoryApkApi.updateLabels(mockPreviewFragment)

        // Then - 验证方法调用和日志记录
        verify { mockPreviewFragment.updatedLabel() }
        verify { Log.d("CategoryApkApi", "updateLabels") }
    }

    /**
     * 测试backToTop方法
     * 验证是否能正确委托给PreviewCombineFragment的backToTop方法
     */
    @Test
    fun `backToTop should delegate to PreviewCombineFragment`() {
        // Given - 设置模拟对象的行为
        every { mockPreviewFragment.backToTop() } just Runs

        // When - 执行测试方法
        CategoryApkApi.backToTop(mockPreviewFragment)

        // Then - 验证方法调用和日志记录
        verify { mockPreviewFragment.backToTop() }
        verify { Log.d("CategoryApkApi", "backToTop") }
    }

    /**
     * 测试permissionSuccess方法
     * 验证是否能正确委托给PreviewCombineFragment的permissionSuccess方法
     */
    @Test
    fun `permissionSuccess should delegate to PreviewCombineFragment`() {
        // Given - 设置模拟对象的行为
        every { mockPreviewFragment.permissionSuccess() } just Runs

        // When - 执行测试方法
        CategoryApkApi.permissionSuccess(mockPreviewFragment)

        // Then - 验证方法调用和日志记录
        verify { mockPreviewFragment.permissionSuccess() }
        verify { Log.d("CategoryApkApi", "permissionSuccess fragment:$mockPreviewFragment") }
    }

    /**
     * 测试exitSelectionMode方法
     * 验证是否能正确委托给PreviewCombineFragment的exitSelectionMode方法
     */
    @Test
    fun `exitSelectionMode should delegate to PreviewCombineFragment`() {
        // Given - 设置模拟对象的行为
        every { mockPreviewFragment.exitSelectionMode() } just Runs

        // When - 执行测试方法
        CategoryApkApi.exitSelectionMode(mockPreviewFragment)

        // Then - 验证方法调用
        verify { mockPreviewFragment.exitSelectionMode() }
    }

    /**
     * 测试onSideNavigationClicked方法
     * 验证是否能正确委托给PreviewCombineFragment的onSideNavigationClicked方法
     */
    @Test
    fun `onSideNavigationClicked should delegate to PreviewCombineFragment`() {
        // Given - 设置模拟对象的行为
        every { mockPreviewFragment.onSideNavigationClicked(any()) } returns true

        // When - 执行测试方法
        val result = CategoryApkApi.onSideNavigationClicked(mockPreviewFragment, true)

        // Then - 验证返回值
        assertTrue(result)
        verify { mockPreviewFragment.onSideNavigationClicked(true) }
    }

    /**
     * 测试当传入的Fragment不是PreviewCombineFragment时的默认行为
     * 验证所有方法都能安全处理非PreviewCombineFragment的情况
     */
    @Test
    fun `methods should do nothing when fragment is not PreviewCombineFragment`() {
        // When/Then - 执行所有方法并验证默认行为
        CategoryApkApi.onResumeLoadData(mockFragment)
        CategoryApkApi.onCreateOptionsMenu(mockFragment, mockMenu, mockMenuInflater)
        val menuResult = CategoryApkApi.onMenuItemSelected(mockFragment, mockMenuItem)
        val backResult = CategoryApkApi.pressBack(mockFragment)
        val navResult = CategoryApkApi.onNavigationItemSelected(mockFragment, mockMenuItem)
        CategoryApkApi.setIsHalfScreen(mockFragment, CategoryHelper.CATEGORY_APK, true)
        CategoryApkApi.fromSelectPathResult(mockFragment, 100, listOf())
        CategoryApkApi.updateLabels(mockFragment)
        CategoryApkApi.backToTop(mockFragment)
        CategoryApkApi.permissionSuccess(mockFragment)
        CategoryApkApi.exitSelectionMode(mockFragment)
        val sideNavResult = CategoryApkApi.onSideNavigationClicked(mockFragment, true)

        // 验证默认返回值
        assertFalse(menuResult)
        assertFalse(backResult)
        assertFalse(navResult)
        assertFalse(sideNavResult)
    }
}