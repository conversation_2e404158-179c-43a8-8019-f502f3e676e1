/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/6/30
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.cpu.orms

import org.junit.Test

class OrmsManagerTest {

    @Test
    fun getDefaultScene() {
        OrmsManager.INSTANCE.getDefaultScene()
    }

    @Test
    fun getCopyScene() {
        OrmsManager.INSTANCE.getCopyScene()
    }

    @Test
    fun getUnzipScene() {
        OrmsManager.INSTANCE.getUnzipScene()
    }
}