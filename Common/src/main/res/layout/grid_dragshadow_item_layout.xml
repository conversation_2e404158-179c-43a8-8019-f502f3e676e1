<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/dragshadow_item_parent"
    android:background="@color/color_transparent"
    android:layoutDirection="locale"
    android:padding="@dimen/list_drag_shadow_item_layout_padding">

    <LinearLayout
        android:id="@+id/grid_dragshadow_item_cons"
        android:layout_width="@dimen/grid_drag_shadow_item_layout_width"
        android:layout_height="@dimen/grid_drag_shadow_item_layout_height"
        android:layout_marginTop="@dimen/drag_shadow_margin_top"
        android:layout_marginEnd="@dimen/drag_shadow_margin_end"
        android:background="@drawable/list_dragshadow_layout_background"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <com.filemanager.common.view.FileThumbView
            android:id="@+id/dragshadow_item_cover"
            android:layout_width="@dimen/grid_drag_shadow_cover_width"
            android:layout_height="@dimen/grid_drag_shadow_cover_width"
            android:layout_marginTop="@dimen/dimen_18dp"
            android:layout_gravity="center_horizontal"
            android:background="@color/color_transparent"
            android:forceDarkAllowed="false" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_8dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="@dimen/dimen_8dp"
            android:layout_marginEnd="@dimen/dimen_8dp"
            android:layout_marginBottom="@dimen/dimen_6dp"
            android:orientation="vertical">

            <com.filemanager.common.view.MiddleMultilineTextView
                android:id="@+id/dragshadow_item_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="middle"
                android:gravity="center_horizontal"
                android:maxLines="2"
                android:singleLine="false"
                android:textAppearance="?android:attr/textAppearanceLarge"
                android:textColor="?attr/couiColorLabelPrimary"
                android:textSize="@dimen/dimen_12dp"/>

            <TextView
                android:id="@+id/dragshadow_item_details"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/grid_drag_shadow_title_margin_detail"
                android:textAppearance="?android:attr/textAppearanceSmall"
                android:textColor="?attr/couiColorLabelSecondary"
                android:layout_gravity="center_horizontal"
                android:textSize="@dimen/dimen_10dp"/>
        </LinearLayout>
    </LinearLayout>

    <include layout="@layout/dragshadow_reddot_layout" />
</FrameLayout>