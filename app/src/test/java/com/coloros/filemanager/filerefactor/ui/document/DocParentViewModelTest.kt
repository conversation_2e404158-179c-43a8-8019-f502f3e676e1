/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.document
 * * Version     : 1.0
 * * Date        : 2020/9/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.filerefactor.ui.document

import com.coloros.filemanager.BaseTest
import com.filemanager.common.constants.KtConstants
import com.oplus.filemanager.category.document.ui.DocParentViewModel
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class DocParentViewModelTest : BaseTest() {
    private val mDocParentViewModel = DocParentViewModel()

    @Test
    @Throws(Exception::class)
    fun should_change_scan_view_model_when_click_scanModeItem() {
        var initScanMode = mDocParentViewModel.mBrowseModeState.value
        Assert.assertEquals(KtConstants.SCAN_MODE_LIST, initScanMode)
        mDocParentViewModel.clickScanModeItem()
        initScanMode = mDocParentViewModel.mBrowseModeState.value
        Assert.assertEquals(KtConstants.SCAN_MODE_GRID, initScanMode)
        mDocParentViewModel.clickScanModeItem()
        initScanMode = mDocParentViewModel.mBrowseModeState.value
        Assert.assertEquals(KtConstants.SCAN_MODE_LIST, initScanMode)
    }
}
