/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:/FileActionRestore.kt
 * * Description:
 * * Version:
 * * Date :2020/6/18
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>          <data>        <version>     <desc>
 * *  ZeJiang.Duan      2020/6/18        1.0           Create
 ****************************************************************/
package com.filemanager.recyclebin.operation.action

import androidx.activity.ComponentActivity
import androidx.lifecycle.LifecycleOwner
import com.filemanager.recyclebin.RecycleFileManager
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.DeleteSoundUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.VibratorUtil
import com.filemanager.recyclebin.operation.listener.FileRestoreObserver
import com.filemanager.recyclebin.operation.BaseOperation
import com.filemanager.recyclebin.operation.RecycleBinOperationListener

class FileActionRestore {
    private var mLifecycleOwner: LifecycleOwner?
    private var mSelectFiles: List<out BaseFileBean>?
    private var mAllFilesSize: Int = -1

    companion object {
        private const val TAG = "FileActionDelete"
    }

    constructor(
        lifecycle: LifecycleOwner,
        selectFiles: List<BaseFileBean>,
        allFilesSize: Int
    ) {
        mLifecycleOwner = lifecycle
        mSelectFiles = selectFiles
        mAllFilesSize = allFilesSize
    }

    /**
     * Note:
     * This method is only for RecycleBin page. others page please use method [execute]
     */
    fun executeRecycleBinRestore(uiObserver: FileRestoreObserver) {
        if (mSelectFiles.isNullOrEmpty()) {
            Log.w(TAG, "executeRecycleBinRestore, Failed to execute: select file is empty")
            return
        }
        if (uiObserver.getContext() is ComponentActivity) {
            val deleteCompleteListener = object : RecycleBinOperationListener {
                override fun onOperationCompleted(opType: Int, result: BaseOperation.OperationResult) {
                    Log.d(TAG, "RecycleBinOperationListener total = ${result.mTotalCount} failed = ${result.mFailedCount}")
                    if (result.mTotalCount > result.mFailedCount) {
                        DeleteSoundUtil.playDeleteSound()
                        VibratorUtil.vibrate()
                    }
                    uiObserver.onActionDone(result.mTotalCount > result.mFailedCount)
                }
            }
            val files = ArrayList<BaseFileBean>(mSelectFiles!!.size)
            mSelectFiles?.let {
                files.addAll(it)
            }
            Log.d(TAG, "executeRecycleBinRestore,  Use recycle bin delete function to execute")
            RecycleFileManager.INSTANCE.restoreBaseFiles(
                uiObserver.getContext() as ComponentActivity,
                files,
                deleteCompleteListener,
                mAllFilesSize
            )
        } else {
            Log.w(TAG, "executeRecycleBinRestore,  Failed to execute: context is null or not an activity")
        }

        mLifecycleOwner = null
        mSelectFiles = null
        mAllFilesSize = -1
    }
}