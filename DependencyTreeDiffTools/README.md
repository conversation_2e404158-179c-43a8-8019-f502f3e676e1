背景：
文件管理广告sdk因为内部依赖变化导致闪退问题，账号SDK和广告SDK依赖的OkHttp版本号不同，经过APK编译后应用中的OkHttp版本号升级为高版本V3.14.4。在启动文件管理加载广告时，广告sdk内部调用OkHttp发生逻辑异常（使用反射构造OpenSSLSocketImpl和相关方法，这些方法在api30被定义为restricted methods无法反射调用，内部抛出异常），导致加载vungle策略广告时出现异常，文件管理器出现闪退。

短期方案：
文管提测前对比广告sdk相关依赖库的版本变化，识别是否影响广告SDK的依赖关系，同步广告sdk方进行识别和验收，拿到验收结果后再发版。

识别原理：
首先保持一份目前文管线上最新的广告sdk依赖树的数据(old.txt),通过程序自动执行当前文件管理依赖树并与old.txt进行比较，识别到有差异则将对应的差异情况输出，如果有依赖库的版本变化则同步广告方进行识别和验收，验收通过后将old.txt替换为最新的依赖情况。

方法：
进入到FileManager/DependencyTreeDiffTools目录下命令行直接运行java -jar dependency-tree-diff-fat.jar即可完成依赖树的识别，相关实现细节可参考源码（https://odocs.myoas.com/file/zdkyBjPld1TOLpA6/ 「dependency-tree-diff-trunk.zip」）。

注:基于https://github.com/JakeWharton/dependency-tree-diff/tree/trunk改造

结果样例：
1.依赖变更
\--- project :Ad:Overseas
\--- com.opos.ad:overseas-ad-global-pub:2.14.1
\--- com.opos.ad:overseas-ad-native-entry-global-pub:2.14.1
+--- com.opos.ad:overseas-ad-third-native-global:2.14.1
|    \--- com.vungle:publisher-sdk-android:6.12.1
update com.squareup.okhttp3:okhttp:3.14.9 -> 4.11.0 (*)   >>>>>>  3.14.9 (*)  okhttp从3.14.9 -> 4.11.0 (*) 变更为 3.14.9 (*)
\--- com.opos.ad:stobus-oaPub:1.0.1
\--- com.opos.ad:st-lib_obus-oa:1.0.1
update com.squareup.okhttp3:okhttp:3.14.9 -> 4.11.0 (*)   >>>>>>  3.14.9 (*)  okhttp从3.14.9 -> 4.11.0 (*) 变更为 3.14.9 (*)
2.新增依赖
+\--- project :Ad:Overseas
+     \--- com.opos.ad:overseas-ad-global-pub:2.14.1
+          \--- com.opos.ad:overseas-ad-native-entry-global-pub:2.14.1
+               \--- com.opos.ad:stobus-oaPub:1.0.1
+                    \--- com.opos.ad:st-lib_obus-oa:1.0.1
+                         +--- com.heytap.nearx:cloudconfig-env-oversea:2.4.2.1
+                         \--- com.heytap.nearx:taphttp-env-oversea:3.12.12.236
+                              \--- com.heytap.nearx:cloudconfig-env-oversea:2.3.9 -> 2.4.2.1
3.减少依赖
-      \--- com.opos.ad:overseas-ad-mix-ad-global-pub:2.14.1
-           +--- com.opos.ad:overseas-ad-cmn-base:2.14.1 (*)
-           +--- org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0 -> 1.8.0 (*)
-           +--- com.google.ads.interactivemedia.v3:interactivemedia:3.29.0
-           |    +--- com.google.android.gms:play-services-ads-identifier:18.0.1 (*)
-           |    +--- com.google.android.gms:play-services-appset:16.0.2 (*)
-           |    +--- com.google.android.gms:play-services-base:18.1.0 (*)
-           |    +--- com.google.android.gms:play-services-basement:18.1.0 -> 18.2.0 (*)
-           |    +--- com.google.android.gms:play-services-tasks:18.0.2 (*)
-           |    +--- androidx.annotation:annotation:1.5.0 (*)
-           |    +--- androidx.browser:browser:1.4.0 (*)
-           |    +--- androidx.preference:preference:1.2.0 (*)
-           |    \--- androidx.webkit:webkit:1.5.0
-           |         +--- androidx.annotation:annotation:1.2.0 -> 1.5.0 (*)
-           |         \--- androidx.core:core:1.1.0 -> 1.8.0 (*)


