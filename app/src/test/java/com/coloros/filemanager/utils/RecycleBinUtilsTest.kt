/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecycleBinUtilsTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/16
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/16       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.utils

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.RecycleStore
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.RecycleBinUtils
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.mockkObject
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.io.File

class RecycleBinUtilsTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
    }

    @Test
    fun testIsRecycleDirPath() {
        mockkObject(RecycleBinUtils)
        mockkStatic(VolumeEnvironment::class)
        mockkObject(KtAppUtils)
        every { VolumeEnvironment.getInternalSdPath(context) } returns INTERNAL_SD_PATH

        // path is null
        var path: String? = null
        Assert.assertFalse(RecycleBinUtils.isRecycleDirPath(path))

        // path is empty
        path = ""
        Assert.assertFalse(RecycleBinUtils.isRecycleDirPath(path))

        // path is start with RECYCLER_DIR_PATH
        path =
            "$INTERNAL_SD_PATH${File.separator}${RecycleStore.RECYCLE_DIR_NAME}${File.separator}test"
        Assert.assertTrue(RecycleBinUtils.isRecycleDirPath(path))

        // path is start with LEGACY_RECYCLER_DIR_PATH
        path =
            "$INTERNAL_SD_PATH/Android/data/${APPLICATION_ID}${File.separator}" +
                    "${RecycleStore.LEGACY_RECYCLE_DIR_NAME}${File.separator}test"
        Assert.assertTrue(RecycleBinUtils.isRecycleDirPath(path))

        // path is RECYCLER_DIR_PATh
        path = "$INTERNAL_SD_PATH${File.separator}${RecycleStore.RECYCLE_DIR_NAME}${File.separator}"
        Assert.assertTrue(RecycleBinUtils.isRecycleDirPath(path))

        // path is LEGACY_RECYCLER_DIR_PATH
        path =
            "$INTERNAL_SD_PATH/Android/data/${APPLICATION_ID}${File.separator}" +
                    RecycleStore.LEGACY_RECYCLE_DIR_NAME
        Assert.assertTrue(RecycleBinUtils.isRecycleDirPath(path))
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    companion object {
        private const val INTERNAL_SD_PATH = "/test/InternalSdPath"
        private const val APPLICATION_ID = "com.coloros.filemanager"
    }
}