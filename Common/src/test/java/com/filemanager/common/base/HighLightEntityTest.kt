/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : HighLightEntityTest
 ** Description : HighLightEntityTest
 ** Version     : 1.0
 ** Date        : 2024/05/28 15:05
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/05/28       1.0      create
 ***********************************************************************/
package com.filemanager.common.base

import io.mockk.spyk
import org.junit.Assert
import org.junit.Test

class HighLightEntityTest {
    private val highLightEntity = spyk(HighLightEntity())


    @Test
    fun testTitleKeyList() {
        //given
        val title = ContentEntity()
        title.content = "qwertyuiop"
        title.position = "0 1 2 3 4 5"
        highLightEntity.title = title

        val except = mutableListOf<String>()
        except.add("q")
        except.add("e")
        except.add("t")

        //when
        val list = highLightEntity.titleKeyList

        //then
        Assert.assertTrue(list == except)
    }

    @Test
    fun testContentHighLightStart() {
        //given
        val content = ContentEntity()
        content.content = "qwertyuiop"
        content.position = "0 1"
        highLightEntity.content = content

        //when
        val result = highLightEntity.contentHighLightStart

        //then
        Assert.assertTrue(result == 0)
    }

    @Test
    fun testContentHighLightEnd() {
        //given
        val content = ContentEntity()
        content.content = "qwertyuiop"
        content.position = "0 1"
        highLightEntity.content = content

        //when
        val result = highLightEntity.contentHighLightEnd

        //then
        Assert.assertTrue(result == 1)
    }
}
