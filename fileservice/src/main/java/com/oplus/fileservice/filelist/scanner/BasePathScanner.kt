/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BaseScaner
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 11:09
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/21       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.scanner

import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.RecycleBinUtils
import com.oplus.fileservice.bean.WebFileBean
import com.oplus.fileservice.bean.WebPathFileBean
import com.oplus.fileservice.bean.WebScannerResult
import com.oplus.fileservice.fileServiceScope
import com.oplus.fileservice.filelist.FileSelectPathService
import com.oplus.fileservice.utils.WebFileConstant
import com.oplus.fileservice.utils.WebFileConstant.DIR_ALL_FILES_PAGE_SIZE
import com.oplus.fileservice.utils.WebFileHelper
import com.oplus.fileservice.utils.WebMimeTypeHelper
import com.oplus.fileservice.utils.WebThumbCacheManager
import java.io.File
import kotlin.collections.ArrayList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeoutOrNull

abstract class BasePathScanner(pageNo: Int, pageSize: Int, private var isNeedThumbnail: Boolean = true) : BaseScanner() {
    companion object {
        private const val TAG = "BasePathScanner"
    }

    private var mFilterList: List<Int>? = null
    private var mPath: Array<String>? = null
    private var mVolume: List<String>? = null
    private var mPageNo = pageNo
    private var mPageSize = pageSize
    private var mTotal = 1

    @Suppress("LongMethod", "NestedBlockDepth", "TooGenericExceptionCaught")
    override fun scannerFiles(): WebScannerResult? {
        var allFileList = ArrayList<BaseFileBean>()
        var itemList: List<BaseFileBean>? = null
        try {
            mVolume = getVolume()
            mPath = getPath()
            Log.d(TAG, "scannerFiles() mVolume.size = ${mVolume?.size},mPath.size=${mPath?.size}")
            if (mVolume == null) {
                for (i in 0 until mPath!!.size) {
                    val path = mPath!![i]
                    val file = File(path)
                    Log.d(TAG, "scannerFiles() path=${file.absolutePath}")
                    if (file.isDirectory) {
                        var ss: Array<String>?
                        try {
                            Log.d(TAG, "scannerFiles() begin list")
                            ss = JavaFileHelper.list(file)
                            Log.d(TAG, "scannerFiles() end list")
                        } catch (e: Exception) {
                            Log.e(TAG, e.message)
                            continue
                        }
                        if (ss == null) {
                            Log.v(TAG, "file system can not fetch data")
                            return continue
                        }
                        val tempSize = ss.size
                        Log.d(TAG, "scannerFiles() tempSize=$tempSize")
                        for (j in 0 until tempSize) {
                            if (!HiddenFileHelper.isNeedShowHiddenFile() && HiddenFileHelper.isHiddenFile(
                                    ss[j]
                                )
                            ) {
                                continue
                            }
                            itemList = createFromPath("", path, ss[j])
                            if (itemList.isNullOrEmpty()) {
                                continue
                            }

                            for (item: BaseFileBean in itemList) {
                                if (RecycleBinUtils.isRecycleDirPath(item.mData)) {
                                    continue
                                }
                                allFileList.add(item)
                            }
                        }
                        Log.d(TAG, "scannerFiles() allFileList.size=${allFileList.size}")
                    }
                }
            } else {
                for (volume in mVolume!!) {
                    if (VolumeEnvironment.isNeedLoadPath(MyApplication.sAppContext, volume)) {
                        continue
                    }
                    Log.d(TAG, "scannerFiles() mVolume=$volume")
                    for (i in 0 until mPath!!.size) {
                        val path = mPath!![i]
                        val file = File(volume, path)
                        Log.d(TAG, "scannerFiles() path=${file.absolutePath}")
                        if (file.isDirectory) {
                            var ss: Array<String>?
                            try {
                                Log.d(TAG, "scannerFiles() begin list")
                                ss = JavaFileHelper.list(file)
                                Log.d(TAG, "scannerFiles() end list")
                            } catch (e: Exception) {
                                Log.e(TAG, e.message)
                                continue
                            }
                            if (ss == null) {
                                Log.v(TAG, "file system can not fetch data")
                                return continue
                            }
                            val tempSize = ss.size
                            Log.d(TAG, "scannerFiles() tempSize=$tempSize")
                            for (j in 0 until tempSize) {
                                if (!HiddenFileHelper.isNeedShowHiddenFile() && HiddenFileHelper.isHiddenFile(
                                        ss[j]
                                    )
                                ) {
                                    continue
                                }

                                itemList = createFromPath(volume, path, ss[j])
                                if (itemList.isNullOrEmpty()) {
                                    continue
                                }

                                for (item: BaseFileBean in itemList) {
                                    if (RecycleBinUtils.isRecycleDirPath(item.mData)) {
                                        continue
                                    }
                                    allFileList.add(item)
                                }
                            }
                            Log.d(
                                TAG,
                                "scannerFiles() itemList.size=${itemList?.size},allFileList.size=${allFileList.size}"
                            )
                        }
                    }
                }
            }
            mFilterList = getFilterList()
            mFilterList?.let {
                if (!it.contains(MimeTypeHelper.UNKNOWN_TYPE)) {
                    val filterLists = ArrayList<BaseFileBean>()
                    for (tempFile in allFileList) {
                        if (it.contains(tempFile.mLocalType)) {
                            filterLists.add(tempFile)
                        }
                    }
                    allFileList = filterLists
                    Log.d(TAG, "scannerFiles() allFileList.size=${allFileList?.size}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        }
        val sortItems = preHandleResultBackground(allFileList)
        Log.d(TAG, "scannerFiles() sortItems.size=${sortItems.size}")
        val webFileBean = transformBaseFileBeanToWebFileBean(pageData(sortItems))
        return WebScannerResult(mPageNo, mTotal, webFileBean)
    }

    /**
     *create BaseFileBean under the path
     * @param volume files and directories
     * @param parentPath file parent directory
     * @param path file path
     * @return may be null
     */
    abstract fun createFromPath(
        volume: String,
        parentPath: String,
        path: String
    ): List<BaseFileBean>?

    /**
     *get an Array<String> of paths to scan
     * @return 不能为null
     */
    abstract fun getPath(): Array<String>

    /**
     *get an Array<String> of paths to scan
     * @return may be null
     */
    abstract fun getVolume(): List<String>?

    /**
     *get the data that needs to be filtered
     * @return may be null
     */
    abstract fun getFilterList(): List<Int>?

    open fun preHandleResultBackground(list: List<BaseFileBean>): List<BaseFileBean> {
        return list
    }

    private fun transformBaseFileBeanToWebFileBean(baseFileBeans: List<BaseFileBean>): List<WebFileBean> {
        val resultWebBeans = ArrayList<WebFileBean>(baseFileBeans.size)
        var webPathFileBean: WebFileBean?
        for (baseFileBean in baseFileBeans) {
            baseFileBean.mData?.let {
                webPathFileBean = WebPathFileBean(it)
                webPathFileBean!!.mLocalType = baseFileBean.mLocalType
                resultWebBeans.add(webPathFileBean!!)
            }
        }
        runBlocking {
            val thumbnailsWebBeans = resultWebBeans.filter {
                isNeedThumbnail && ((it.mLocalType == MimeTypeHelper.APPLICATION_TYPE) ||
                        (it.mLocalType == MimeTypeHelper.IMAGE_TYPE) ||
                        (it.mLocalType == MimeTypeHelper.VIDEO_TYPE))
            }.take(
                if (mPageSize == DIR_ALL_FILES_PAGE_SIZE) {
                    0
                } else if (resultWebBeans.size > WebFileConstant.DEFAULT_PAGE_SIZE) {
                    WebFileConstant.DEFAULT_PAGE_SIZE
                } else {
                    resultWebBeans.size
                }
            )
            resultWebBeans.filter {
                it.mFileType == WebMimeTypeHelper.DIRECTORY_TYPE
            }.map {
                fileServiceScope.async(Dispatchers.IO) {
                    val timeOut = getFolderNum(it)
                    Log.d(TAG, "${it.mFileName} get folder number $timeOut")
                }
            }.plus(
                async(Dispatchers.IO) {
                    WebThumbCacheManager.setThumbnailData(thumbnailsWebBeans)
                }
            ).awaitAll()
        }
        Log.d(TAG, "transformBaseFileBeanToWebFileBean finish(), mTotal = $mTotal")
        return resultWebBeans
    }

    @VisibleForTesting
    fun pageData(result: List<BaseFileBean>): ArrayList<BaseFileBean> {
        mTotal = result.size
        if (mPageSize < 1) {
            mPageSize = WebFileConstant.DEFAULT_PAGE_SIZE
            Log.d(TAG, "Service modify mPageSize $mPageSize")
        }
        var pageTotal = mTotal / mPageSize
        if (mTotal % mPageSize > 0) {
            pageTotal++
        }

        if (mPageNo > pageTotal) {
            Log.d(FileSelectPathService.TAG, "mPageNo > pageTotal is true, mPageNo = $mPageNo")
            mPageNo = pageTotal
        }
        if (mPageNo < 1) {
            mPageNo = 1
        }
        val start = mPageSize * (mPageNo - 1)

        /**
         * When the number of requested pages exceeds the total, or the requested PageSize is [DIR_ALL_FILES_PAGE_SIZE],
         * the end index is [result]'s size.
         */
        val end = if (mPageSize * mPageNo > mTotal || mPageSize == DIR_ALL_FILES_PAGE_SIZE) {
            mTotal
        } else {
            mPageSize * mPageNo
        }

        val webFileBeans = ArrayList<BaseFileBean>()
        webFileBeans.addAll(result.subList(start, end))
        return webFileBeans
    }

    private suspend fun getFolderNum(webFileBean: WebFileBean) =
        withTimeoutOrNull(WebFileConstant.WAIT_TIME_OUT) {
            webFileBean.mFolderItem = WebFileHelper.listFilesCount(
                webFileBean.mFilePath,
                excludeHideFile = HiddenFileHelper.isNeedShowHiddenFile().not()
            )
            true
        }

    override fun destroy() {
    }
}