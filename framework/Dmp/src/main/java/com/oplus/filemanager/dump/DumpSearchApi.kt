/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : DumpSearchApi.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.dump

import android.app.Activity
import android.content.Context
import android.database.Cursor
import android.util.Log
import com.filemanager.common.bean.IndexOperationResult
import com.filemanager.common.bean.SearchIndexBean
import com.filemanager.common.bean.SearchResultBean
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi

object DumpSearchApi : IDmpSearchApi {

    const val TAG = "DumpSearchApi"

    @Volatile
    private var dmpSdkInitSuc = false

    override fun initIndex(): Boolean {
        Log.i(TAG, "initIndex")
        return IndexProcessor.initIndexProxy()
    }


    override fun clearIndex(): Boolean {
        Log.i(TAG, "clearIndex")
        return IndexProcessor.clearIndex()
    }


    override fun hasIndexConfig(): Boolean {
        Log.i(TAG, "hasIndexConfig")
        return IndexProcessor.getCurrentIndexProxy()?.config != null
    }


    override fun checkAndPreprocessIndex(): Boolean {
        Log.i(TAG, "checkAndPreprocessIndex")
        return IndexProcessor.checkAndProcessIndexState()
    }


    override fun addDocument(indexBean: SearchIndexBean): IndexOperationResult {
        Log.i(TAG, "addDocument indexBean $indexBean")
        return IndexProcessor.addDocument(indexBean)
    }


    override fun addDocuments(indexBeans: List<SearchIndexBean>): List<IndexOperationResult> {
        Log.i(TAG, "addDocument indexBeans size ${indexBeans.size}")
        return IndexProcessor.addDocuments(indexBeans)
    }


    override fun updateDocument(indexBean: SearchIndexBean): IndexOperationResult {
        Log.i(TAG, "updateDocument indexBean $indexBean")
        return IndexProcessor.updateDocument(indexBean)
    }


    override fun updateDocuments(indexBeans: List<SearchIndexBean>): List<IndexOperationResult> {
        Log.i(TAG, "updateDocuments indexBeans size ${indexBeans.size}")
        return IndexProcessor.updateDocuments(indexBeans)
    }

    override fun deleteDocument(indexBean: SearchIndexBean): IndexOperationResult {
        Log.i(TAG, "deleteDocument indexBean $indexBean")
        return IndexProcessor.deleteDocument(indexBean)
    }


    override fun deleteDocuments(indexBeans: List<SearchIndexBean>): List<IndexOperationResult> {
        Log.i(TAG, "deleteDocuments indexBeans size ${indexBeans.size}")
        return IndexProcessor.deleteDocuments(indexBeans)
    }


    @Synchronized
    override fun initDmpSdk(context: Context): Boolean {
        Log.i(TAG, "initDmpSdk before dmpSdkInitSuc $dmpSdkInitSuc")
        if (!dmpSdkInitSuc) {
            dmpSdkInitSuc = SearchProcessor.init(context)
        }
        Log.i(TAG, "initDmpSdk after dmpSdkInitSuc $dmpSdkInitSuc")
        return dmpSdkInitSuc
    }


    override fun isSearchAvalable(): Boolean {
        Log.i(TAG, "isSearchAvalable")
        return SearchProcessor.isServiceAvailable()
    }


    override fun search(query: String, page: Int): List<SearchResultBean> {
        Log.i(TAG, "search query $query, page $page")
        return SearchProcessor.startSearchThirdAppFile(query, page)
    }


    override fun queryRaw(): List<SearchResultBean> {
        Log.i(TAG, "queryRaw")
        return SearchProcessor.queryRaw()
    }


    override fun checkDmpV3(): Boolean {
        Log.i(TAG, "checkDmpV3")
        return SearchProcessor.checkSupportV3()
    }

    override fun updateSwitchSpToDmp() {
        Log.i(TAG, "updateSwitchSpToDmp")
        return SearchSwitchSpHelper.updateThirdAppSwitchToDmp()
    }

    override fun checkAndUpdateSwitchSpToDmp() {
        Log.i(TAG, "checkAndUpdateSwitchSpToDmp")
        return SearchSwitchSpHelper.checkAndUpdateSpToDmp()
    }

    override fun initClient() {
        Log.i(TAG, "initClient")
        SearchHelper.initClient()
    }

    override fun getCursor(searchKey: String?): Cursor? {
        Log.i(TAG, "getCursor searchKey $searchKey")
        return SearchHelper.getCursor(searchKey)
    }

    override fun checkState(activity: Activity) {
        Log.i(TAG, "checkState $activity")
        SearchHelper.checkState(activity)
    }

    override fun isDMPCursor(cursor: Cursor): Boolean {
        return SearchHelper.isDMPCursor(cursor)
    }

    override fun isShouldLoadDMP(): Boolean {
        Log.i(TAG, "isShouldLoadDMP")
        return SearchHelper.isShouldLoadDMP()
    }

    override fun buildRewriteQueries(mSearchKey: String?): Collection<String> {
        Log.i(TAG, "buildRewriteQueries mSearchKey $mSearchKey")
        return SearchHelper.buildRewriteQueries(mSearchKey)
    }


    override fun resetDmpInitStatus() {
        Log.i(TAG, "resetDmpInitStatus")
        dmpSdkInitSuc = false
        IndexProcessor.resetProxy()
        SearchProcessor.resetProxy()
    }
}