/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentMiddleCardWidgetProvider
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/13 14:12
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/13       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent

import android.content.Context
import android.os.Bundle
import com.filemanager.common.utils.Log
import com.oplus.pantanal.seedling.SeedlingCardWidgetProvider
import com.oplus.pantanal.seedling.bean.SeedlingCard

class RecentMiddleCardWidgetProvider : SeedlingCardWidgetProvider() {

    private val recentCardControl: IRecentCardControl = RecentCardControl()

    override fun onCardCreate(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onCardCreate -> card = $card")
        recentCardControl.cardCreate(context, card)
    }

    override fun onCardObserve(context: Context, cards: List<SeedlingCard>) {
        Log.d(TAG, "onCardObserve -> card = $cards")
        recentCardControl.cardObserve(context, cards)
    }

    override fun onDestroy(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onDestroy -> card = $card")
        recentCardControl.destroy(context, card)
    }

    override fun onHide(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onHide -> card = $card")
        recentCardControl.hide(context, card)
    }

    override fun onShow(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onShow -> card = $card")
        recentCardControl.show(context, card)
    }

    override fun onSubscribed(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onSubscribed -> card = $card")
        recentCardControl.subscribed(context, card)
    }

    override fun onUnSubscribed(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onUnSubscribed -> card = $card")
        recentCardControl.unSubscribed(context, card)
    }

    override fun onUpdateData(context: Context, card: SeedlingCard, data: Bundle) {
        Log.d(TAG, "onUpdateData -> card = $card ; data = $data")
        recentCardControl.updateData(context, card, data)
    }

    companion object {
        private const val TAG = "RecentMiddleCardWidgetProvider"
    }
}