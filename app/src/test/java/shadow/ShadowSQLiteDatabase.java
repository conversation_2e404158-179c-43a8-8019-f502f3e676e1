/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowSQLiteDatabase.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/9/18
 ** Author: JiaBo Lan
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/9/18      1.0     create
 ****************************************************************/
package shadow;

import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(SQLiteDatabase.class)
public class ShadowSQLiteDatabase {
    private static Cursor sCursor;
    @Implementation
    public Cursor query(String table, String[] columns, String selection, String[] selectionArgs, String groupBy, String having, String orderBy) {
        return sCursor;
    }

    @Implementation
    public Cursor query(String table, String[] columns, String selection, String[] selectionArgs, String groupBy, String having, String orderBy, String limit) {
        return sCursor;
    }

    public static void setsCursor(Cursor cursor) {
        sCursor = cursor;
    }
}
