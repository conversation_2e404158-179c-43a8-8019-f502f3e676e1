/*********************************************************************
 * Copyright (C), 2010-2030 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : FileDriveAdapterTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/6/12  10:45
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * keweiwei        2024/6/12       1.0      create
 **********************************************************************/
package com.filemanager.superappsource.helper

import android.content.Context
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.Utils
import com.filemanager.superapp.R
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.Assert
import kotlin.test.Test

class MainCategoryItemsBeanFactoryTest {
    private val context = mockk<Context>()

    @Test
    fun `should return right when call getCategoryItemLastData`() {
        //setup
        mockkStatic(Utils::class)
        //given
        every {
            Utils.getCategoryItemLastRecord(
                context,
                Constants.RECORD_LAST_IMAGE_COUNT
            )
        } returns 10L
        every {
            Utils.getCategoryItemLastRecord(
                context,
                Constants.RECORD_LAST_IMAGE_SIZE
            )
        } returns 20L
        val expect = longArrayOf(10L, 20L)
        //when
        val result = MainCategoryItemsBeanFactory.getCategoryItemLastData(0, context)
        //then
        Assert.assertEquals(expect[0], result[0])
        Assert.assertEquals(expect[1], result[1])
        //teardown
        unmockkStatic(Utils::class)
    }

    @Test
    fun `should return error when call getCategoryItemLastData`() {
        //setup
        mockkStatic(Utils::class)
        //given
        every {
            Utils.getCategoryItemLastRecord(
                context,
                Constants.RECORD_LAST_IMAGE_COUNT
            )
        } returns 10L
        every {
            Utils.getCategoryItemLastRecord(
                context,
                Constants.RECORD_LAST_IMAGE_SIZE
            )
        } returns 20L
        val expect = longArrayOf(0, 0)
        //when
        val result = MainCategoryItemsBeanFactory.getCategoryItemLastData(-1, context)
        //then
        Assert.assertEquals(expect[0], result[0])
        Assert.assertEquals(expect[1], result[1])
        //teardown
        unmockkStatic(Utils::class)
    }

    @Test
    fun `should return right when call getCategoryIcon`() {
        //given
        val category = mutableListOf(
            MainCategoryItemsBean.IC_CATEGORY_PIC,
            MainCategoryItemsBean.IC_CATEGORY_VIDEO,
            MainCategoryItemsBean.IC_CATEGORY_AUDIO,
            MainCategoryItemsBean.IC_CATEGORY_DOC,
            MainCategoryItemsBean.IC_CATEGORY_APK,
            MainCategoryItemsBean.IC_CATEGORY_ARCHIVE,
            MainCategoryItemsBean.MAIN_CATEGORY_PICTURE_BG,
            MainCategoryItemsBean.MAIN_CATEGORY_VIDEO_BG,
            MainCategoryItemsBean.MAIN_CATEGORY_AUDIO_BG,
            MainCategoryItemsBean.MAIN_CATEGORY_DOCUMENT_BG,
            MainCategoryItemsBean.MAIN_CATEGORY_COMPRESS_BG,
            MainCategoryItemsBean.MAIN_CATEGORY_APK_BG,
            MainCategoryItemsBean.IC_PARENT_CHILD_CATEGORY_PIC,
            MainCategoryItemsBean.IC_PARENT_CHILD_CATEGORY_VIDEO,
            MainCategoryItemsBean.IC_PARENT_CHILD_CATEGORY_AUDIO,
            MainCategoryItemsBean.IC_PARENT_CHILD_CATEGORY_DOC,
            MainCategoryItemsBean.IC_PARENT_CHILD_CATEGORY_APK,
            MainCategoryItemsBean.IC_PARENT_CHILD_CATEGORY_ARCHIVE,
            MainCategoryItemsBean.IC_PARENT_CHILD_CLOUD_ICON,
            MainCategoryItemsBean.IC_PARENT_CHILD_OTG_ICON,
            MainCategoryItemsBean.IC_PARENT_CHILD_SDCARD_ICON,
            MainCategoryItemsBean.IC_CLOUD_DISK,
            MainCategoryItemsBean.IC_PRIVATE_SAFE,
            100
        )

        val expect = mutableListOf(
            R.drawable.ic_category_pic,
            R.drawable.ic_category_video,
            R.drawable.ic_category_audio,
            R.drawable.ic_category_doc,
            R.drawable.ic_category_apk,
            R.drawable.ic_category_archive,
            com.filemanager.common.R.drawable.main_category_picture_bg,
            com.filemanager.common.R.drawable.main_category_vedio_bg,
            com.filemanager.common.R.drawable.main_category_audio_bg,
            com.filemanager.common.R.drawable.main_category_document_bg,
            com.filemanager.common.R.drawable.main_category_compress_bg,
            com.filemanager.common.R.drawable.main_category_apk_bg,
            com.filemanager.common.R.drawable.ic_parent_child_category_pic,
            com.filemanager.common.R.drawable.ic_parent_child_category_video,
            com.filemanager.common.R.drawable.ic_parent_child_category_audio,
            com.filemanager.common.R.drawable.ic_parent_child_category_doc,
            com.filemanager.common.R.drawable.ic_parent_child_category_apk,
            com.filemanager.common.R.drawable.ic_parent_child_category_archive,
            R.drawable.ic_parent_child_cloud_icon,
            R.drawable.ic_parent_child_otg_icon,
            R.drawable.ic_parent_child_sdcard_icon,
            R.drawable.ic_cloud_disk,
            R.drawable.ic_private_safe,
            -1
        )
        //when
        category.forEachIndexed { index, i ->
            val result = MainCategoryItemsBeanFactory.getCategoryIcon(i)
            Assert.assertEquals(expect[index], result)
        }
    }
}