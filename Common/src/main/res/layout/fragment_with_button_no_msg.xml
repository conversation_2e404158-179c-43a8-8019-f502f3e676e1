<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.scrollview.COUIScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rootScrollView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:overScrollMode="never"
    android:scrollbars="none">

    <RelativeLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_40dp">

        <TextView
            android:id="@+id/textView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dimen_18dp"
            android:text="@string/manage_files_permission_title"
            android:gravity="center"
            android:textColor="?attr/couiColorLabelPrimary"
            android:textAppearance="@style/couiTextHeadlineXS"
            android:textSize="18sp" />

        <TextView
            android:id="@+id/textDsc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/textView"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dimen_4dp"
            android:gravity="center"
            android:maxWidth="@dimen/dimen_280dp"
            android:text="@string/manage_files_permission_desc_normal"
            android:textColor="?attr/couiColorLabelSecondary"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/textDsc"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:clickable="true"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:text="@string/set_button_text"
            android:textColor="?attr/couiColorLabelTheme"
            android:textSize="@dimen/font_size_16" />
    </RelativeLayout>
</com.coui.appcompat.scrollview.COUIScrollView>