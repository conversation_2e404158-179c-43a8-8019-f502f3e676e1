/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentPreviewItemExt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/29 17:52
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/29       1.0      create
 ***********************************************************************/
@file:JvmName("RecentPreviewItemExtKt")
package com.oplus.filemanager.cardwidget.recent.utils

import com.filemanager.common.helper.CategoryHelper
import com.oplus.filemanager.cardwidget.recent.data.RecentPreviewItem
import com.oplus.filemanager.cardwidget.recent.data.SUPER_APP_QQ_NAME_LOWERCASE
import com.filemanager.common.utils.stringResource

fun obtainTargetItem(type: Int, name: String, items: List<RecentPreviewItem>): RecentPreviewItem {
    return if (type == CategoryHelper.CATEGORY_QQ) {
        if (name.equals(SUPER_APP_QQ_NAME_LOWERCASE, true)) {
            items.find {
                type == it.type && it.name.equals(SUPER_APP_QQ_NAME_LOWERCASE, true)
            }
        } else {
            items.find {
                type == it.type && it.name.equals(SUPER_APP_QQ_NAME_LOWERCASE, true).not()
            }
        }
    } else {
        items.find {
            type == it.type
        }
    } ?: RecentPreviewItem(
        CategoryHelper.CATEGORY_RECENT,
        stringResource(com.filemanager.common.R.string.label_add_recent_file_title)
    )
}