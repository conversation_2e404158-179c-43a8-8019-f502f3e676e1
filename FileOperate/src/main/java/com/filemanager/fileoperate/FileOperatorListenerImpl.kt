/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.fileoperate.FileOperatorListenerImpl
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/1/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.fileoperate

import com.filemanager.common.MyApplication
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils

open class FileOperatorListenerImpl(viewModel: SelectionViewModel<*, *>,
                               private val mFromMedia: Boolean = true) : IFileOperate.OperateResultListener {
    private var mViewModel: SelectionViewModel<*, *>? = viewModel
    private var isPreviewOpen: Boolean = false

    companion object {
        const val TAG = "FileOperatorListenerImpl"
    }

    open fun refreshData() {
        Log.d(TAG, "refreshData mFromMedia mFromMedia $mFromMedia")
        mViewModel?.apply {
            if (mFromMedia) {
                delayLoadData()
            } else {
                loadData()
            }
        }
    }

    override fun onActionDone(opType: Int, result: Boolean, data: Any?) {
        Log.d(TAG, "onActionDone opType $opType result $result")
        // NOTE!!!: COPY/CUT/DEL DON'T DELAY LOAD DATA in file browser ui,
        // it will cause the performance test to fail.
        mViewModel?.apply {
            when (opType) {
                IFileOperate.OP_ENCRYPT -> {
                    changeListMode(KtConstants.LIST_NORMAL_MODE)
                    refreshData()
                }
                IFileOperate.OP_RENAME,
                IFileOperate.OP_DECOMPRESS,
                IFileOperate.OP_COMPRESS,
                IFileOperate.OP_DELETE_TO_RECYCLE,
                IFileOperate.OP_RESTORE,
                IFileOperate.OP_FAVORITE,
                IFileOperate.OP_REMOVE_FAVORITE,
                IFileOperate.OP_DELETE_FOREVER -> {
                    if ((opType == IFileOperate.OP_DELETE_FOREVER) && (data is Pair<*, *>)) {
                        StatisticsUtils.nearMeStatisticsRecycleBinDeleteCount(MyApplication.sAppContext,
                                data.first as? Int ?: 0, data.second as? Long ?: 0)
                    }
                    changeListMode(KtConstants.LIST_NORMAL_MODE)
                    refreshData()
                }
                IFileOperate.OP_CREATE_FOLDER -> {
                    onCreateFolderPath(data.toString())
                    refreshData()
                }
                IFileOperate.OP_COPY,
                IFileOperate.OP_CREATE_SHORTCUT_FOLDER,
                IFileOperate.OP_CUT -> {
                    refreshData()
                }
                IFileOperate.OP_OPEN_BY_OTHER,
                IFileOperate.OP_UPLOAD_CLOUD_DISK -> {}
            }
        }
    }

    override fun onActionCancelled(opType: Int) {
        mViewModel?.apply {
            when (opType) {
                IFileOperate.OP_COPY,
                IFileOperate.OP_CUT,
                IFileOperate.OP_DECOMPRESS,
                IFileOperate.OP_COMPRESS -> {
                    refreshData()
                }
                IFileOperate.OP_CREATE_SHORTCUT -> changeListMode(KtConstants.LIST_NORMAL_MODE)
                IFileOperate.OP_RENAME -> {}
                IFileOperate.OP_ENCRYPT -> {
                    refreshData()
                }
            }
        }
    }

    override fun onActionReloadData(opType: Int) {
        when (opType) {
            IFileOperate.OP_DELETE_TO_RECYCLE -> refreshData()
        }
    }

    fun setPreviewOpen(isOpen: Boolean) {
        isPreviewOpen = isOpen
    }
}