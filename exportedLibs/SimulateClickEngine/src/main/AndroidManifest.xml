<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-sdk tools:overrideLibrary="com.oplus.sdk.addon.sdk" />

    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />

    <!-- Original required permission: android.permission.WRITE_SECURE_SETTINGS -->
    <uses-permission android:name="com.oplus.permission.safe.SETTINGS" />

    <!-- For OplusScreenStatusListener -->
    <uses-permission android:name="com.oplus.permission.safe.SAFE_MANAGER"/>

    <application>
        <service
            android:name="com.oplus.filemanager.simulateclick.service.SimulateClickService"
            android:exported="false"
            android:enabled="@bool/service_enable"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">

            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/simulate_click_service_config" />
        </service>
    </application>
</manifest>