package com.oplus.filemanager.cardwidget.recent.setting

import com.filemanager.common.helper.CategoryHelper
import com.oplus.filemanager.cardwidget.recent.data.RecentCard
import com.oplus.filemanager.cardwidget.recent.data.RecentPreviewItem
import com.oplus.filemanager.cardwidget.recent.utils.RecentCardUtils
import com.oplus.filemanager.cardwidget.recent.usecase.GetAllCategoryItemsUseCase
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.unmockkAll
import io.mockk.verify
import io.mockk.mockkObject
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * RecentCardSettingViewModel的单元测试类
 * 用于测试RecentCardSettingViewModel的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RecentCardSettingViewModelTest {

    // 待测试的ViewModel实例
    private lateinit var viewModel: RecentCardSettingViewModel
    // RecentCardUtils的mock对象
    private lateinit var mockRecentCardUtils: RecentCardUtils
    // GetAllCategoryItemsUseCase的mock对象
    private lateinit var mockGetAllCategoryItemsUseCase: GetAllCategoryItemsUseCase

    /**
     * 测试前的初始化方法
     * 1. 创建mock对象
     * 2. 设置mock行为
     * 3. 创建ViewModel实例
     */
    @Before
    fun setUp() {
        // 创建RecentCardUtils的mock对象
        mockRecentCardUtils = mockk(relaxed = true)
        // 创建GetAllCategoryItemsUseCase的mock对象
        mockGetAllCategoryItemsUseCase = mockk(relaxed = true)
        // 设置GetAllCategoryItemsUseCase的invoke方法返回空列表
        every { mockGetAllCategoryItemsUseCase.invoke() } returns arrayListOf()
        // 将RecentCardUtils类mock为对象
        mockkObject(RecentCardUtils)
        // 设置RecentCardUtils.instance返回mock对象
        every { RecentCardUtils.instance } returns mockRecentCardUtils
        // 创建待测试的ViewModel实例
        viewModel = RecentCardSettingViewModel()
    }

    /**
     * 测试后的清理方法
     * 1. 清除所有mock
     * 2. 解除所有mock
     */
    @After
    fun tearDown() {
        // 清除所有mock
        clearAllMocks()
        // 解除所有mock
        unmockkAll()
    }

    /**
     * 测试saveRecentCardMapping方法
     * 当类型与原始类型相同时不应保存
     */
    @Test
    fun `saveRecentCardMapping should not save when type is same as origin`() {
        // Given 准备测试数据
        // 设置卡片ID
        viewModel.setCardId("testCardId")
        // 设置mock行为：getCacheCardDataByCardId返回测试用的RecentCard对象
        every { mockRecentCardUtils.getCacheCardDataByCardId(any()) } returns 
            RecentCard("testCardId", 4, "testName", CategoryHelper.CATEGORY_QQ)
        // 选择与原始类型相同的项
        viewModel.selectRecentItem(RecentPreviewItem(CategoryHelper.CATEGORY_QQ, "testName"))

        // When 执行测试方法
        viewModel.saveRecentCardMapping()

        // Then 验证结果
        // 验证switchCategory方法没有被调用
        verify(exactly = 0) { mockRecentCardUtils.switchCategory(any(), any(), any()) }
    }
}