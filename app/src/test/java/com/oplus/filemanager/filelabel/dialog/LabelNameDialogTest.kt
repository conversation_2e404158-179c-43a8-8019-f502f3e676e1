/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LabelNameDialogTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/14
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/9/14      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.filelabel.dialog

import com.oplus.filemanager.filelabel.dialog.LabelDialogBean.Companion.TYPE_CREATE_LABEL
import com.oplus.filemanager.filelabel.dialog.LabelDialogBean.Companion.TYPE_RENAME_LABEL
import com.oplus.filemanager.main.R
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Test

class LabelNameDialogTest {
    @Test
    fun getTitleNameTest() {
        val dlg = mockk<LabelNameDialog>()
        every { dlg.getTitleName() } answers { callOriginal() }
        every { dlg.labelBean.type } returns TYPE_CREATE_LABEL
        Assert.assertEquals(com.filemanager.common.R.string.create_label, dlg.getTitleName())
        every { dlg.labelBean.type } returns TYPE_RENAME_LABEL
        Assert.assertEquals(com.filemanager.common.R.string.dialog_rename_label, dlg.getTitleName())
        every { dlg.labelBean.type } returns 0
        Assert.assertEquals(com.filemanager.common.R.string.create_label, dlg.getTitleName())
    }
}