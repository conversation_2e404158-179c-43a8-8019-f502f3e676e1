/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RarDecompressHelperTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/17
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/17       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.fileoperate.decompress

import com.filemanager.common.base.BaseFileBean
import com.filemanager.fileoperate.decompress.RarDecompressHelper
import de.innosystec.unrar.Archive
import de.innosystec.unrar.rarfile.FileHeader
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class RarDecompressHelperTest {

    private lateinit var rarDecompressHelper: RarDecompressHelper

    @Before
    fun setUp() {
        rarDecompressHelper = RarDecompressHelper()
    }

    @Test
    fun testInternalIsEncrypted() {
        // sourceFile mData is null
        Assert.assertFalse(rarDecompressHelper.internalIsEncrypted(fakeSourceFile))

        // sourceFile mData is empty
        fakeSourceFile.mData = ""
        Assert.assertFalse(rarDecompressHelper.internalIsEncrypted(fakeSourceFile))

        // archive exception
        fakeSourceFile.mData = FILE_PATH
        Assert.assertFalse(rarDecompressHelper.internalIsEncrypted(fakeSourceFile))

        mockkConstructor(Archive::class) {
            every { anyConstructed<Archive>().isEncrypted } returns true
        }
        Assert.assertFalse(rarDecompressHelper.internalIsEncrypted(fakeSourceFile))
    }

    @Test
    fun testInternalVerifyPassword() {
        fakeSourceFile.mData = FILE_PATH
        Assert.assertFalse(rarDecompressHelper.internalVerifyPassword(fakeSourceFile, password))
        val fileHead = mockk<FileHeader>()
        mockkConstructor(Archive::class) {
            every {
                anyConstructed<Archive>().extractFile(any(), any())
            } returns Unit
            every {
                anyConstructed<Archive>().nextFileHeader()
            } returns fileHead
        }
        Assert.assertFalse(rarDecompressHelper.internalVerifyPassword(fakeSourceFile, password))
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    companion object {
        private val fakeSourceFile = BaseFileBean()
        private var password: String? = null
        private const val FILE_PATH = "/sdcard/0/file/"
    }
}