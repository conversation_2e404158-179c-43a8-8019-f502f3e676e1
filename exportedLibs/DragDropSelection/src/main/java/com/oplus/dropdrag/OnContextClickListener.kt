/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - OnContextClickListener.kt
 * Description: On Context ClickListener
 * Version: 1.0
 * Date : 2020/05/15
 * Author: <PERSON><PERSON><PERSON>.<PERSON>
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/05/15    1.0     create
 ***********************************************************************/

package com.oplus.dropdrag

import android.view.MotionEvent
import androidx.annotation.Keep

@Keep
interface OnContextClickListener {
    /**
     * Called when user performs a context click, usually via mouse pointer
     * right-click.
     * @param e the event associated with the click.
     * @return true if the event was handled.
     */
    fun onContextClick(e: MotionEvent): <PERSON><PERSON>an
}