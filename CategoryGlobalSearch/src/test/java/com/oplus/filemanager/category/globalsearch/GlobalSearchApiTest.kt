package com.oplus.filemanager.category.globalsearch

import android.app.Activity
import androidx.lifecycle.ViewModel
import com.filemanager.common.bean.SwitchChangeEvent
import com.filemanager.common.databus.EventConstants
import com.filemanager.common.databus.LiteEventBus
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchActivity
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchFragment
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchRecyclebinViewModel
import com.oplus.filemanager.category.globalsearch.ui.more.GlobalSearchMoreActivity
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * GlobalSearchApi的单元测试类
 * 用于测试GlobalSearchApi对象的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class GlobalSearchApiTest {

    // 定义测试所需的mock对象
    private lateinit var mockActivity: Activity
    private lateinit var mockFragment: GlobalSearchFragment
    private lateinit var mockViewModel: GlobalSearchRecyclebinViewModel
    private lateinit var mockRecyclebinViewModel: GlobalSearchRecyclebinViewModel
    private lateinit var mockLiteEventBus: LiteEventBus

    /**
     * 测试前的初始化方法
     * 创建所有需要的mock对象
     */
    @Before
    fun setUp() {
        mockActivity = mockk(relaxed = true)  // 创建Activity的mock对象
        mockFragment = mockk()  // 创建GlobalSearchFragment的mock对象
        mockViewModel = mockk()  // 创建ViewModel的mock对象
        mockRecyclebinViewModel = mockk()  // 创建回收站ViewModel的mock对象
        mockLiteEventBus = mockk(relaxed = true)  // 创建LiteEventBus的mock对象
        mockkObject(LiteEventBus.Companion)  // mock LiteEventBus的伴生对象
    }

    /**
     * 测试isGlobalSearchFragment方法
     * 当传入的fragment是GlobalSearchFragment时应该返回true
     */
    @Test
    fun `isGlobalSearchFragment should return true when fragment is GlobalSearchFragment`() {
        assertTrue(GlobalSearchApi.isGlobalSearchFragment(mockFragment))
    }

    /**
     * 测试isGlobalSearchFragment方法
     * 当传入的fragment不是GlobalSearchFragment时应该返回false
     */
    @Test
    fun `isGlobalSearchFragment should return false when fragment is not GlobalSearchFragment`() {
        assertFalse(GlobalSearchApi.isGlobalSearchFragment(Any()))
    }

    /**
     * 测试getGlobalSearchFragmentViewModel方法
     * 当传入的fragment是GlobalSearchFragment时应该返回对应的ViewModel
     */
    @Test
    fun `getGlobalSearchFragmentViewModel should return viewModel when fragment is GlobalSearchFragment`() {
        every { mockFragment.getViewModel() } returns mockViewModel  // 设置mock行为

        val result = GlobalSearchApi.getGlobalSearchFragmentViewModel(mockFragment, false)

        assertEquals(mockViewModel, result)  // 验证返回的ViewModel是否正确
    }

    /**
     * 测试getGlobalSearchFragmentViewModel方法
     * 当传入的fragment不是GlobalSearchFragment时应该返回null
     */
    @Test
    fun `getGlobalSearchFragmentViewModel should return null when fragment is not GlobalSearchFragment`() {
        val result = GlobalSearchApi.getGlobalSearchFragmentViewModel(Any(), false)

        assertNull(result)  // 验证返回结果应为null
    }

    /**
     * 测试getGlobalSearchFragmentViewModel方法
     * 当excludeRecycleBin为true且ViewModel是GlobalSearchRecyclebinViewModel时应该返回null
     */
    @Test
    fun `getGlobalSearchFragmentViewModel should return null when excludeRecycleBin is true and viewModel is GlobalSearchRecyclebinViewModel`() {
        every { mockFragment.getViewModel() } returns mockRecyclebinViewModel  // 设置mock行为

        val result = GlobalSearchApi.getGlobalSearchFragmentViewModel(mockFragment, true)

        assertNull(result)  // 验证返回结果应为null
    }

    /**
     * 测试notifyThirdAppSearchSwitchStatus方法
     * 当switchOn为true时应该发送EVENT_TYPE_ON事件
     */
    @Test
    fun `notifyThirdAppSearchSwitchStatus should send EVENT_TYPE_ON when switchOn is true`() {
        every { LiteEventBus.instance } returns mockLiteEventBus  // 设置mock行为

        GlobalSearchApi.notifyThirdAppSearchSwitchStatus(true)

        // 验证是否正确发送了EVENT_TYPE_ON事件
        verify { mockLiteEventBus.send(EventConstants.EVENT_THIRD_APP_SEARCH_SWITCH_CHANGE, 
            SwitchChangeEvent(SwitchChangeEvent.EVENT_TYPE_ON)) }
    }

    /**
     * 测试notifyThirdAppSearchSwitchStatus方法
     * 当switchOn为false时应该发送EVENT_TYPE_OFF事件
     */
    @Test
    fun `notifyThirdAppSearchSwitchStatus should send EVENT_TYPE_OFF when switchOn is false`() {
        every { LiteEventBus.instance } returns mockLiteEventBus  // 设置mock行为

        GlobalSearchApi.notifyThirdAppSearchSwitchStatus(false)

        // 验证是否正确发送了EVENT_TYPE_OFF事件
        verify { mockLiteEventBus.send(EventConstants.EVENT_THIRD_APP_SEARCH_SWITCH_CHANGE, 
            SwitchChangeEvent(SwitchChangeEvent.EVENT_TYPE_OFF)) }
    }

    /**
     * 测试isGlobalSearchActivity方法
     * 当activity是GlobalSearchActivity时应该返回true
     */
    @Test
    fun `isGlobalSearchActivity should return true when activity is GlobalSearchActivity`() {
        val activity = mockk<GlobalSearchActivity>()  // 创建GlobalSearchActivity的mock对象
        assertTrue(GlobalSearchApi.isGlobalSearchActivity(activity))
    }

    /**
     * 测试isGlobalSearchActivity方法
     * 当activity不是GlobalSearchActivity时应该返回false
     */
    @Test
    fun `isGlobalSearchActivity should return false when activity is not GlobalSearchActivity`() {
        assertFalse(GlobalSearchApi.isGlobalSearchActivity(mockActivity))
    }

    /**
     * 测试isGlobalSearchMoreActivity方法
     * 当activity是GlobalSearchMoreActivity时应该返回true
     */
    @Test
    fun `isGlobalSearchMoreActivity should return true when activity is GlobalSearchMoreActivity`() {
        val activity = mockk<GlobalSearchMoreActivity>()  // 创建GlobalSearchMoreActivity的mock对象
        assertTrue(GlobalSearchApi.isGlobalSearchMoreActivity(activity))
    }

    /**
     * 测试isGlobalSearchMoreActivity方法
     * 当activity不是GlobalSearchMoreActivity时应该返回false
     */
    @Test
    fun `isGlobalSearchMoreActivity should return false when activity is not GlobalSearchMoreActivity`() {
        assertFalse(GlobalSearchApi.isGlobalSearchMoreActivity(mockActivity))
    }
}