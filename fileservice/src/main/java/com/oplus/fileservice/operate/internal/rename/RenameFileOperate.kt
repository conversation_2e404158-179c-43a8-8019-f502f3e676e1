/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RenameFileOperate
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/7 15:30
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/7       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.rename

import android.content.Context
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.fileservice.operate.convert.OperateRequest
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.FileNotExist
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.InvalidSourceFileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.InvalidTargetFileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.OK
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.OperateCancelled
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.OperateFailure
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.internal.BaseFileOperate
import com.oplus.fileservice.operate.internal.utils.converterFileBean
import com.oplus.fileservice.operate.internal.utils.exists
import java.io.File

open class RenameFileOperate(
    private val context: Context
) : BaseFileOperate() {

    private val mLockObj = Object()

    override fun run(operateRequest: OperateRequest): OperationResult {
        Log.d(TAG, "run -> operateRequest = $operateRequest ")
        val fileInfo = operateRequest.sourceFileInfo?.getOrNull(0)
        Log.d(TAG, "run -> fileInfo = $fileInfo")
        fileInfo?.let { info ->
            Log.d(TAG, "run -> info = $info")
            info.data?.let { data ->
                Log.d(TAG, "run -> data = $data")
                if (!exists(data)) {
                    Log.d(TAG, "run -> file not exists")
                    return OperationResult(false, FileNotExist)
                }
                Log.d(TAG, "run -> isCancelled = ${isCancelled()}")
                if (isCancelled()) {
                    return OperationResult(false, OperateCancelled)
                }
                val targetFileInfo =
                    operateRequest.targetFileInfo ?: return OperationResult(false, InvalidTargetFileInfo)
                return executeAction(info, targetFileInfo)
            } ?: return OperationResult(false, InvalidSourceFileInfo)
        } ?: return OperationResult(false, InvalidSourceFileInfo)
    }

    override fun recycle() {
        // Do Nothing
    }

    override fun afterRun(result: OperationResult) {
        Log.d(TAG, "afterRun -> result = $result")
    }

    private fun notifyLockReleased() {
        synchronized(mLockObj) {
            mLockObj.notify()
        }
    }

    open fun executeAction(sourceFileInfo: FileInfo, targetFileInfo: FileInfo): OperationResult {
        Log.i(TAG, "executeAction -> source = $sourceFileInfo ; target = $targetFileInfo")
        val newFileName = targetFileInfo.displayName
        Log.i(TAG, "executeAction -> newFileName = $newFileName")
        if (newFileName.isNullOrEmpty()) {
            return OperationResult(false, InvalidTargetFileInfo)
        }
        return if (renameTo(sourceFileInfo, newFileName)) {
            Log.i(TAG, "executeAction -> rename success")
            val fileBean = sourceFileInfo.converterFileBean()
            if (File(sourceFileInfo.data).isDirectory) {
                fileBean.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
            }
            sendMediaScanner(fileBean, newFileName)
            OperationResult(true, OK)
        } else {
            Log.i(TAG, "executeAction -> rename failure")
            OperationResult(false, OperateFailure)
        }
    }

    private fun sendMediaScanner(operateFile: BaseFileBean, newFileName: String) {
        val pathList = ArrayList<String>(2)
        if (operateFile.mData.isNullOrEmpty().not()) {
            pathList.add(File(File(operateFile.mData!!).parent, newFileName).absolutePath)
            pathList.add(operateFile.mData!!)
        }
        when {
            SdkUtils.isAtLeastR() -> {
                // Dir rename maybe cause a long time scan, so not support here
                if (pathList.isNotEmpty() && operateFile.mIsDirectory.not()) {
                    scanFile(pathList)
                } else {
                    MediaScannerCompat.sendMultiDirMediaScanner(pathList, Utils.MEDIA_SCAN_RENAME)
                }
            }
            else -> {
                if (operateFile.mIsDirectory.not()) {
                    val oldFile = File(operateFile.mData!!)
                    val newFile = File(oldFile.parent, newFileName)
                    val newFileBean = PathFileWrapper(newFile.absolutePath)
                    FileMediaHelper.updateFileNameInMediaDB(context, operateFile, newFileBean)
                }
                MediaScannerCompat.sendMultiDirMediaScanner(pathList, Utils.MEDIA_SCAN_RENAME)
            }
        }
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.onUpdateFilePath(pathList[1], pathList[0])
    }

    @Suppress("TooGenericExceptionCaught")
    private fun scanFile(pathList: ArrayList<String>) {
        Log.d(TAG, "sendMediaScanner: sendMultiDirMediaScanner")
        var totalCount = pathList.size
        MediaScannerCompat.sendMultiDirMediaScanner(pathList, Utils.MEDIA_SCAN_RENAME) { _, _ ->
            totalCount--
            if (totalCount == 0) {
                try {
                    notifyLockReleased()
                } catch (e: Exception) {
                    Log.w(TAG, "onScanCompleted failed, ${e.message}")
                }
            }
        }
        try {
            synchronized(mLockObj) {
                mLockObj.wait(WAIT_TIME_OUT)
            }
        } catch (e: Exception) {
            Log.d(TAG, "sendMediaScanner interrupted")
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun renameTo(fileInfo: FileInfo, name: String?): Boolean {
        if (name.isNullOrEmpty() || name.isBlank()) {
            return false
        }
        return try {
            val file = fileInfo.data?.let { File(it) }
            file?.renameTo(File(file.parent, name)) == true
        } catch (e: Exception) {
            false
        }
    }

    companion object {
        private const val TAG = "RenameFileOperate"
        private const val WAIT_TIME_OUT = 2000L
    }
}