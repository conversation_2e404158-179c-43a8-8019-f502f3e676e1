/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IScriptFlagValue.kt
 * Description:
 *     Define the flag value type in Script.
 *
 * Version: 1.0
 * Date: 2024-06-04
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Bix<PERSON><EMAIL>    2024-06-04   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.value

/**
 * Define common interface for flag value in script.
 * The we can add extend method for all flag value type in script.
 */
internal interface IScriptFlagValue {
    /**
     * The int value of flag value.
     * Recommend to override it as member value of data class
     */
    val flag: Int

    /**
     * Convert flag value to alias.
     */
    fun toAlias(): String
}