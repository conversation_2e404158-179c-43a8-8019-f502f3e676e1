/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: AutoDIForPush.kt
 ** Description: Push的自动依赖
 ** Version: 1.0
 ** Date : 2025/04/09
 ** Author: chao.xue
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 *  chao.xue 2025/04/09   1.0    create
 ****************************************************************/
package com.oplus.filemanager.framework.push.di

import androidx.annotation.Keep
import com.oplus.filemanager.interfaze.push.IPushApi
import org.koin.dsl.module

@Keep
class AutoDIForPush {

    val pushApi = module {
        single<IPushApi>(createdAtStart = true) {
            PushApi()
        }
    }
}