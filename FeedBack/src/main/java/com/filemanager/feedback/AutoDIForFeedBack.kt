/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForArchive
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/6/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.filemanager.feedback

import androidx.annotation.Keep
import com.oplus.filemanager.interfaze.feedback.IFeedback
import org.koin.dsl.module

@Keep
class AutoDIForFeedBack {

    val feedBackModule = module {
        single<IFeedback>(createdAtStart = true) {
            FeedbackApi
        }
    }
}