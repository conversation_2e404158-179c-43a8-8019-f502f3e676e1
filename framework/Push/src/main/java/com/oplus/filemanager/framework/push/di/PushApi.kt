/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: IPushApi.kt
 ** Description: push 接口
 ** Version: 1.0
 ** Date : 2025/04/09
 ** Author: chao.xue
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 *  chao.xue 2025/04/09   1.0    create
 ****************************************************************/
package com.oplus.filemanager.framework.push.di

import android.content.Context
import androidx.annotation.MainThread
import com.filemanager.common.BuildConfig
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.utils.Log
import com.heytap.msp.push.HeytapPushManager
import com.oplus.filemanager.framework.push.register.PushCallbackImpl
import com.oplus.filemanager.interfaze.push.IPushApi

class PushApi : IPushApi {

    companion object {
        private const val TAG = "PushApi"
        private const val IS_DEBUG = false

        /**
         * 正式环境
         */
        private const val PUSH_CODE = "c842cbfc88b44ab78d9b43bc6f60af92"
        private const val PUSH_SCODE = "92e320ef81264b5d81c736f57914422c"

        /**
         * 测试环境
         */
        private const val PUSH_CODE_TEST = "d0aba585f7d741fd8138c304946ed32b"
        private const val PUSH_SCODE_TEST = "ec509a0dc034463cb937d5e55c600d82"

        /**
         * 判断是否注册
         */
        var registed: Boolean = false
    }

    private val callbackImpl: PushCallbackImpl by lazy {
        PushCallbackImpl()
    }


    @MainThread
    override fun init(context: Context) {
        if (!isSupport(context)) {
            Log.e(TAG, "init -> not support push !!!")
            return
        }
        val needLog = BuildConfig.DEBUG || PropertyCompat.sLogEnable
        Log.w(TAG, "init -> needLog:$needLog")
        HeytapPushManager.init(context, needLog)
    }

    override fun isSupport(context: Context): Boolean {
        val isSupport = HeytapPushManager.isSupportPush(context)
        Log.w(TAG, "isSupport $isSupport")
        return isSupport
    }

    override fun register(context: Context) {
        if (!isSupport(context)) {
            Log.e(TAG, "register -> not support push !!!")
            return
        }
        if (registed) {
            Log.d(TAG, "register -> has already register !!!")
            return
        }
        Log.d(TAG, "register start..., isTestEnv: $IS_DEBUG")
        var code = PUSH_CODE
        var scode = PUSH_SCODE
        if (IS_DEBUG) {
            code = PUSH_CODE_TEST
            scode = PUSH_SCODE_TEST
        }
        HeytapPushManager.register(context, code, scode, callbackImpl)
    }
}