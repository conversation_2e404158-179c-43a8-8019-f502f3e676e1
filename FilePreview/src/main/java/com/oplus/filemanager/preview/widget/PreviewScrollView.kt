/*********************************************************************************
 * Copyright (C), 2008-2024, Oplus, All rights reserved.
 *
 * File: - PreviewScrollView.kt
 * Description:
 *     The customized scroll view to dynamic layout target scroll item.
 *
 * Version: 1.0
 * Date: 2024-11-13
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-11-13   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.widget

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup
import android.widget.ScrollView
import androidx.annotation.IdRes
import androidx.core.view.children
import com.oplus.filemanager.preview.R
import kotlin.math.max

internal class PreviewScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : ScrollView(context, attrs) {

    private var scrollRootId = 0
    private var scrollRoot: ViewGroup? = null
    private var fillViewportLayoutId = 0
    private var fillViewportLayout: ViewGroup? = null
    private var fillViewportMinHeight = 0
    private var forceUpdateViewport = false

    init {
        val typedArr = context.obtainStyledAttributes(attrs, R.styleable.PreviewScrollView)
        setScrollRoot(
            typedArr.getResourceId(R.styleable.PreviewScrollView_previewScrollRoot, 0)
        )
        setFillViewportLayout(
            typedArr.getResourceId(R.styleable.PreviewScrollView_previewFillViewportLayout, 0)
        )
        setFillViewportMinHeight(
            typedArr.getDimensionPixelOffset(
                R.styleable.PreviewScrollView_previewFillViewportMinHeight,
                0
            )
        )
        typedArr.recycle()
    }

    fun setScrollRoot(@IdRes rootId: Int) {
        scrollRootId = rootId
        scrollRoot = null
    }

    private fun getScrollRoot(): ViewGroup? {
        scrollRoot?.let { return it }
        if (scrollRootId == 0) {
            return null
        }
        return findViewById<ViewGroup?>(scrollRootId).also {
            scrollRoot = it
        }
    }

    fun setFillViewportLayout(@IdRes layoutId: Int) {
        fillViewportLayoutId = layoutId
        fillViewportLayout = null
    }

    private fun getFillViewportLayout(): ViewGroup? {
        fillViewportLayout?.let { return it }
        if (fillViewportLayoutId == 0) {
            return null
        }
        return findViewById<ViewGroup?>(fillViewportLayoutId).also {
            fillViewportLayout = it
        }
    }

    fun setFillViewportMinHeight(minHeight: Int) {
        fillViewportMinHeight = max(minHeight, 0)
        forceUpdateViewport = true
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        if (!changed && !forceUpdateViewport) {
            return
        }
        val scrollRoot = getScrollRoot() ?: return
        val fillLayout = getFillViewportLayout() ?: return
        var otherChildrenHeight = 0
        scrollRoot.children.forEach {
            if (it != fillLayout) {
                otherChildrenHeight += it.height
            }
        }
        val fillHeight = max(height - otherChildrenHeight, fillViewportMinHeight)
        val fillLp = fillLayout.layoutParams
        if (fillLp.height != fillHeight) {
            fillLp.height = fillHeight
            fillLayout.layoutParams = fillLp
        }
        forceUpdateViewport = false
    }

    fun isContentScrollable(): Boolean {
        val scrollRoot = getScrollRoot() ?: getChildAt(0) ?: return false
        return scrollRoot.height > height
    }
}