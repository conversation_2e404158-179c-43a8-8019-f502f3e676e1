JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 0
JvmtiExport can_post_on_exceptions 0
# 286 ciObject found
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
instanceKlass org/jetbrains/kotlin/resolve/DescriptorFactory
instanceKlass java/util/RegularEnumSet$EnumSetIterator
instanceKlass org/jetbrains/kotlin/resolve/scopes/ScopeUtils
instanceKlass org/jetbrains/kotlin/descriptors/annotations/AnnotationSplitter$LazySplitAnnotations
instanceKlass org/jetbrains/kotlin/descriptors/annotations/AnnotationSplitter$Companion
instanceKlass org/jetbrains/kotlin/descriptors/annotations/AnnotationSplitter
instanceKlass org/jetbrains/kotlin/resolve/VariableAsPropertyInfo$Companion
instanceKlass org/jetbrains/kotlin/resolve/VariableAsPropertyInfo
instanceKlass org/jetbrains/kotlin/resolve/lazy/descriptors/ClassResolutionScopesSupportKt
instanceKlass org/jetbrains/kotlin/util/Box
instanceKlass org/jetbrains/kotlin/resolve/constants/CompileTimeConstant$DefaultImpls
instanceKlass org/jetbrains/kotlin/resolve/constants/IntegerValueTypeConstant
instanceKlass org/jetbrains/kotlin/resolve/constants/TypedCompileTimeConstant
instanceKlass org/jetbrains/kotlin/resolve/constants/CompileTimeConstant$Parameters
instanceKlass org/jetbrains/kotlin/resolve/constants/KClassValue$Value
instanceKlass org/jetbrains/kotlin/resolve/constants/ClassLiteralValue
instanceKlass org/jetbrains/kotlin/resolve/constants/KClassValue$Companion
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinJvmBinaryPackageSourceElement
instanceKlass org/jetbrains/kotlin/resolve/source/PsiSourceElementKt
instanceKlass org/jetbrains/kotlin/descriptors/DeclarationDescriptorWithNavigationSubstitute
instanceKlass org/jetbrains/kotlin/resolve/DescriptorToSourceUtils
instanceKlass org/jetbrains/kotlin/coroutines/CoroutineUtilKt
instanceKlass org/jetbrains/kotlin/resolve/jvm/RuntimeAssertionsDataFlowExtras
instanceKlass org/jetbrains/kotlin/resolve/jvm/RuntimeAssertionInfo$DataFlowExtras
instanceKlass org/jetbrains/kotlin/resolve/jvm/RuntimeAssertionInfo$Companion
instanceKlass org/jetbrains/kotlin/resolve/jvm/RuntimeAssertionInfo
instanceKlass org/jetbrains/kotlin/resolve/ImplicitIntegerCoercion
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/NewAbstractResolvedCall$getDataFlowInfoForArguments$1
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ArgumentMatchImpl
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ArgumentMatch
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ArgumentMapping
instanceKlass org/jetbrains/kotlin/resolve/calls/model/VarargValueArgument
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/EmptyLabeledReturn
instanceKlass org/jetbrains/kotlin/resolve/calls/model/QualifierReceiverKotlinCallArgument
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ReceiverExpressionKotlinCallArgument
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/BuilderInferenceUtilKt
instanceKlass org/jetbrains/kotlin/types/checker/ClassicTypeSystemContextKt
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/model/InitialConstraint
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/ConstraintSystemBuilderKt$WhenMappings
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/ConstraintSystemTransaction
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/ConstraintSystemBuilderKt
instanceKlass org/jetbrains/kotlin/resolve/calls/components/SimpleArgumentsChecksKt
instanceKlass org/jetbrains/kotlin/resolve/calls/components/PostponeArgumentsChecksKt
instanceKlass org/jetbrains/kotlin/resolve/calls/components/UnitTypeConversions
instanceKlass org/jetbrains/kotlin/resolve/calls/components/SuspendTypeConversions
instanceKlass org/jetbrains/kotlin/resolve/calls/components/TypeConversions$ConversionData
instanceKlass org/jetbrains/kotlin/resolve/calls/components/SamTypeConversions
instanceKlass org/jetbrains/kotlin/resolve/calls/components/ParameterTypeConversion
instanceKlass org/jetbrains/kotlin/resolve/calls/components/TypeConversions
instanceKlass org/jetbrains/kotlin/resolve/calls/components/ResolutionPartsKt
instanceKlass org/jetbrains/kotlin/resolve/calls/components/ReceiverInfo$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/components/ReceiverInfo
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ResolvedCallArgument
instanceKlass org/jetbrains/kotlin/resolve/calls/model/SubKotlinCallArgument
instanceKlass org/jetbrains/kotlin/resolve/calls/model/LambdaKotlinCallArgument
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ExpressionKotlinCallArgument
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/QualifierReceiverKt
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/IdentifierInfo$Expression
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/ExpressionReceiver$Companion
instanceKlass org/jetbrains/kotlin/util/IncrementalTrackerUtilKt
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/IdentifierInfo$NO
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/IdentifierInfo$PackageOrClass
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/IdentifierInfoKt
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/StubTypesBasedInferenceSession
instanceKlass org/jetbrains/kotlin/types/expressions/DoubleColonExpressionResolverKt
instanceKlass org/jetbrains/kotlin/types/expressions/DoubleColonExpressionResolver$ReservedDoubleColonLHSResolutionResult
instanceKlass org/jetbrains/kotlin/types/expressions/DoubleColonExpressionResolver$LHSResolutionResult
instanceKlass org/jetbrains/kotlin/resolve/calls/util/PsiUtilsKt
instanceKlass org/jetbrains/kotlin/types/expressions/typeInfoFactory/TypeInfoFactoryKt
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticWithParameters1Marker
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/TypeParameterQualifier
instanceKlass org/jetbrains/kotlin/resolve/QualifiedExpressionResolveUtilKt
instanceKlass org/jetbrains/kotlin/resolve/calls/results/OverloadResolutionResultsUtil
instanceKlass org/jetbrains/kotlin/resolve/calls/components/ArgumentsUtilsKt
instanceKlass org/jetbrains/kotlin/resolve/calls/components/ArgumentsToParametersMapper$CallArgumentProcessor
instanceKlass org/jetbrains/kotlin/resolve/calls/model/SimpleCandidateFactory$WhenMappings
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/CandidateWithBoundDispatchReceiver
instanceKlass org/jetbrains/kotlin/resolve/calls/util/IsFromStdlibJre7Or8Kt
instanceKlass org/jetbrains/kotlin/resolve/descriptorUtil/AnnotationsForResolveUtilsKt
instanceKlass org/jetbrains/kotlin/resolve/sam/SamConversionResolverImplKt
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/PrioritizedCompositeScopeTowerProcessor
instanceKlass org/jetbrains/kotlin/resolve/calls/CallTransformer
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/AbstractInvokeTowerProcessor
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/PSICallResolver$FactoryProviderForInvoke
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/CandidateFactoryProviderForInvoke
instanceKlass org/jetbrains/kotlin/resolve/TraceEntryFilter
instanceKlass org/jetbrains/kotlin/diagnostics/AbstractDiagnostic
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticWithParameters3Marker
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/ErrorCandidate
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/ErrorCandidateFactoryKt$WhenMappings
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/ErrorCandidateContext
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/ErrorCandidateFactoryKt
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/PSICallResolver$WhenMappings
instanceKlass org/jetbrains/kotlin/resolve/calls/model/KotlinCallDiagnostic
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/TowerLevelsKt
instanceKlass org/jetbrains/kotlin/resolve/scopes/MemberScopeKt
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/ClassValueReceiver
instanceKlass org/jetbrains/kotlin/synthetic/JavaSyntheticPropertiesScope$SyntheticPropertyHolder$Companion
instanceKlass org/jetbrains/kotlin/synthetic/JavaSyntheticPropertiesScope$SyntheticPropertyHolder
instanceKlass javaslang/Tuple2
instanceKlass javaslang/Tuple
instanceKlass javaslang/collection/IteratorModule$EmptyIterator
instanceKlass javaslang/collection/AbstractIterator
instanceKlass javaslang/collection/List$Nil
instanceKlass javaslang/collection/List
instanceKlass javaslang/collection/Stack
instanceKlass javaslang/collection/Queue
instanceKlass javaslang/collection/LinearSeq
instanceKlass javaslang/collection/LinkedHashMap
instanceKlass javaslang/collection/LinkedHashSet
instanceKlass javaslang/control/Option$None
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowValueKt
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/IdentifierInfo$Receiver
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/IdentifierInfo$ERROR
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/IdentifierInfo
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowValue$Companion
instanceKlass org/jetbrains/kotlin/resolve/scopes/DeprecatedLexicalScope
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/AbstractScopeTowerLevel
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/ScopeTowerLevel
instanceKlass org/jetbrains/kotlin/resolve/descriptorUtil/AnnotationsForResolveKt
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/TowerResolver$Task
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/AbstractSimpleScopeTowerProcessor
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/SimpleScopeTowerProcessor
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/VariableAndObjectScopeTowerProcessor
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/ScopeTowerProcessorsKt
instanceKlass org/jetbrains/kotlin/resolve/calls/KotlinCallResolver$WhenMappings
instanceKlass org/jetbrains/kotlin/resolve/calls/util/CallMaker$CallImpl
instanceKlass org/jetbrains/kotlin/resolve/calls/context/TemporaryResolutionResultsCache
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/NewCallArgumentsKt
instanceKlass org/jetbrains/kotlin/psi/KtPsiUtil$KtExpressionWrapper
instanceKlass org/jetbrains/kotlin/descriptors/impl/TypeAliasConstructorDescriptorImpl$Companion
instanceKlass org/jetbrains/kotlin/types/TypeAliasExpansionReportStrategy$DO_NOTHING
instanceKlass org/jetbrains/kotlin/types/TypeAliasExpander$Companion
instanceKlass org/jetbrains/kotlin/types/TypeAliasExpander
instanceKlass org/jetbrains/kotlin/types/TypeAliasExpansion$Companion
instanceKlass org/jetbrains/kotlin/types/TypeAliasExpansion
instanceKlass org/jetbrains/kotlin/resolve/TypeResolver$TracingTypeAliasExpansionReportStrategy
instanceKlass org/jetbrains/kotlin/types/TypeAliasExpansionReportStrategy
instanceKlass org/jetbrains/kotlin/resolve/annotations/ThrowUtilKt
instanceKlass org/jetbrains/kotlin/resolve/SinceKotlinValue
instanceKlass org/jetbrains/kotlin/descriptors/impl/AbstractTypeAliasDescriptor$typeConstructor$1
instanceKlass org/jetbrains/kotlin/descriptors/annotations/AnnotationsImpl
instanceKlass org/jetbrains/kotlin/serialization/deserialization/AnnotationDeserializer$WhenMappings
instanceKlass org/jetbrains/kotlin/types/AbstractTypeChecker$WhenMappings
instanceKlass org/jetbrains/kotlin/types/model/TypeSystemContextKt$WhenMappings
instanceKlass org/jetbrains/kotlin/types/model/TypeSystemContextKt
instanceKlass org/jetbrains/kotlin/types/checker/NewCapturedTypeKt
instanceKlass org/jetbrains/kotlin/descriptors/ModalityUtilsKt
instanceKlass org/jetbrains/kotlin/resolve/constants/IntegerLiteralTypeConstructor
instanceKlass org/jetbrains/kotlin/types/AbstractNullabilityChecker
instanceKlass kotlin/jvm/internal/TypeIntrinsics
instanceKlass org/jetbrains/kotlin/resolve/jvm/jvmSignature/JvmMethodSignature
instanceKlass org/jetbrains/org/objectweb/asm/commons/Method
instanceKlass org/jetbrains/kotlin/codegen/state/InlineClassManglingUtilsKt
instanceKlass org/jetbrains/kotlin/descriptors/ScriptDescriptor
instanceKlass org/jetbrains/kotlin/load/java/SpecialBuiltinMembers
instanceKlass org/jetbrains/kotlin/resolve/jvm/jvmSignature/JvmMethodParameterSignature
instanceKlass org/jetbrains/kotlin/codegen/AsmUtil
instanceKlass org/jetbrains/kotlin/load/kotlin/TypeMappingMode$WhenMappings
instanceKlass org/jetbrains/kotlin/codegen/AccessorForCallableDescriptor
instanceKlass org/jetbrains/kotlin/codegen/state/KotlinTypeMapper$WhenMappings
instanceKlass org/jetbrains/kotlin/codegen/signature/AsmTypeFactory
instanceKlass org/jetbrains/kotlin/load/kotlin/JvmDescriptorTypeWriter
instanceKlass org/jetbrains/kotlin/codegen/JvmCodegenUtil
instanceKlass org/jetbrains/kotlin/codegen/coroutines/CoroutineCodegenUtilKt$INITIAL_SUSPEND_DESCRIPTOR_FOR_INVOKE_SUSPEND$1
instanceKlass org/jetbrains/kotlin/codegen/coroutines/CoroutineCodegenUtilKt$INITIAL_DESCRIPTOR_FOR_SUSPEND_FUNCTION$1
instanceKlass org/jetbrains/kotlin/codegen/CodegenUtilKt
instanceKlass org/jetbrains/kotlin/codegen/coroutines/CoroutineCodegenUtilKt
instanceKlass org/jetbrains/kotlin/resolve/ImportedFromObjectCallableDescriptor
instanceKlass org/jetbrains/kotlin/codegen/OwnerKind$Companion
instanceKlass org/jetbrains/kotlin/descriptors/annotations/CompositeAnnotations
instanceKlass org/jetbrains/kotlin/builtins/UnsignedTypes
instanceKlass org/jetbrains/kotlin/builtins/KotlinBuiltIns$Primitives
instanceKlass org/jetbrains/kotlin/resolve/constants/IntegerValueTypeConstructor
instanceKlass org/jetbrains/kotlin/types/NotNullTypeParameter
instanceKlass org/jetbrains/kotlin/types/TypeWithEnhancementKt
instanceKlass org/jetbrains/kotlin/load/kotlin/JvmType$Companion
instanceKlass org/jetbrains/kotlin/load/kotlin/JvmType
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/EnhancedTypeAnnotations
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/TypeEnhancementKt
instanceKlass org/jetbrains/kotlin/load/kotlin/TypeSignatureMappingKt
instanceKlass org/jetbrains/kotlin/builtins/FunctionTypesKt
instanceKlass org/jetbrains/kotlin/utils/FunctionsKt
instanceKlass org/jetbrains/kotlin/load/kotlin/TypeMappingMode$Companion
instanceKlass org/jetbrains/kotlin/load/kotlin/TypeMappingMode
instanceKlass org/jetbrains/kotlin/load/kotlin/JvmTypeFactoryImpl
instanceKlass org/jetbrains/kotlin/load/kotlin/JvmTypeFactory
instanceKlass org/jetbrains/kotlin/types/FlexibleTypeImpl$Companion
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/AbstractReceiverValue
instanceKlass org/jetbrains/kotlin/types/AbstractStrictEqualityTypeChecker
instanceKlass org/jetbrains/kotlin/types/checker/StrictEqualityTypeChecker
instanceKlass org/jetbrains/kotlin/types/typesApproximation/CapturedTypeApproximationKt
instanceKlass org/jetbrains/kotlin/descriptors/annotations/FilteredAnnotations
instanceKlass org/jetbrains/kotlin/types/TypeSubstitutionKt
instanceKlass org/jetbrains/kotlin/types/TypeCapabilitiesKt
instanceKlass org/jetbrains/kotlin/types/TypeSubstitutor$2
instanceKlass org/jetbrains/kotlin/types/TypeWithEnhancement
instanceKlass org/jetbrains/kotlin/types/DisjointKeysUnionTypeSubstitution$Companion
instanceKlass org/jetbrains/kotlin/types/DescriptorSubstitutor
instanceKlass org/jetbrains/kotlin/descriptors/impl/FunctionDescriptorImpl$CopyConfiguration
instanceKlass org/jetbrains/kotlin/load/java/lazy/descriptors/LazyJavaAnnotationDescriptor
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/PredefinedFunctionEnhancementInfo
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/TypeEnhancementInfo
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/SignatureEnhancementBuilder$ClassEnhancementBuilder$FunctionEnhancementBuilder
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/SignatureEnhancementBuilder$ClassEnhancementBuilder
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/SignatureEnhancementBuilder
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/PredefinedEnhancementInfoKt
instanceKlass org/jetbrains/kotlin/load/kotlin/TypeMappingConfigurationImpl
instanceKlass org/jetbrains/kotlin/load/kotlin/MethodSignatureBuildingUtilsKt
instanceKlass org/jetbrains/kotlin/types/checker/NewTypeVariableConstructor
instanceKlass org/jetbrains/kotlin/load/kotlin/DescriptorBasedTypeSignatureMappingKt
instanceKlass org/jetbrains/kotlin/load/kotlin/MethodSignatureMappingKt
instanceKlass org/jetbrains/kotlin/load/java/BuiltinSpecialPropertiesKt
instanceKlass org/jetbrains/kotlin/load/java/BuiltinSpecialProperties
instanceKlass org/jetbrains/kotlin/load/java/PropertiesConventionUtilKt
instanceKlass org/jetbrains/kotlin/load/java/lazy/descriptors/LazyJavaScope$MethodSignatureData
instanceKlass org/jetbrains/kotlin/resolve/jvm/kotlinSignature/SignaturesPropagationData$ValueParameters
instanceKlass org/jetbrains/kotlin/resolve/jvm/kotlinSignature/SignaturePropagationUtilKt
instanceKlass org/jetbrains/kotlin/util/CheckResult
instanceKlass org/jetbrains/kotlin/util/ReturnsCheck
instanceKlass org/jetbrains/kotlin/util/IsKPropertyCheck
instanceKlass org/jetbrains/kotlin/util/NoDefaultAndVarargsCheck
instanceKlass org/jetbrains/kotlin/util/ValueParameterCountCheck
instanceKlass org/jetbrains/kotlin/util/MemberKindCheck
instanceKlass org/jetbrains/kotlin/util/Check
instanceKlass org/jetbrains/kotlin/util/Checks
instanceKlass org/jetbrains/kotlin/util/AbstractModifierChecks
instanceKlass org/jetbrains/kotlin/codegen/state/KotlinTypeMapper$typeMappingConfiguration$1
instanceKlass org/jetbrains/kotlin/load/kotlin/TypeMappingConfiguration
instanceKlass org/jetbrains/kotlin/codegen/ClassBuilderMode
instanceKlass org/jetbrains/kotlin/resolve/InlineClassDescriptorResolver
instanceKlass org/jetbrains/kotlin/codegen/state/KotlinTypeMapper$Companion
instanceKlass org/jetbrains/kotlin/codegen/state/KotlinTypeMapperBase
instanceKlass org/jetbrains/kotlin/codegen/signature/KotlinToJvmSignatureMapperImpl
instanceKlass org/jetbrains/kotlin/resolve/jvm/jvmSignature/KotlinToJvmSignatureMapper
instanceKlass org/jetbrains/kotlin/resolve/jvm/kotlinSignature/SignaturesPropagationData
instanceKlass org/jetbrains/kotlin/load/java/lazy/descriptors/LazyJavaScope$ResolvedValueParameters
instanceKlass org/jetbrains/kotlin/load/java/descriptors/JavaMethodDescriptor$2
instanceKlass org/jetbrains/kotlin/load/java/descriptors/JavaMethodDescriptor$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/tree/RecursiveTreeElementWalkingVisitor$ASTTreeGuide
instanceKlass org/jetbrains/kotlin/com/intellij/util/WalkingState
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/tree/AstBufferUtil
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaLoadingKt
instanceKlass org/jetbrains/kotlin/load/java/lazy/descriptors/ClassDeclaredMemberIndex
instanceKlass org/jetbrains/kotlin/load/java/lazy/descriptors/DeclaredMemberIndex
instanceKlass org/jetbrains/kotlin/descriptors/impl/AbstractTypeParameterDescriptor$2$1
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/JavaTypeEnhancement$Result
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/JavaTypeEnhancement$SimpleResult
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/TypeComponentPositionKt
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/TypeEnhancementUtilsKt
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/JavaTypeQualifiers$Companion
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/JavaTypeQualifiers
instanceKlass org/jetbrains/kotlin/types/RawType
instanceKlass org/jetbrains/kotlin/load/java/JavaDefaultQualifiers
instanceKlass org/jetbrains/kotlin/load/java/JvmAnnotationNamesKt
instanceKlass org/jetbrains/kotlin/load/java/AnnotationQualifiersFqNamesKt
instanceKlass org/jetbrains/kotlin/load/java/components/JavaAnnotationDescriptor
instanceKlass org/jetbrains/kotlin/load/java/descriptors/PossiblyExternalAnnotationDescriptor
instanceKlass org/jetbrains/kotlin/com/intellij/util/IdempotenceChecker
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/JavaElementCollectionFromPsiArrayUtil
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmTypeParameter
instanceKlass org/jetbrains/kotlin/name/FqNamesUtilKt$WhenMappings
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/roots/ProjectRootModificationTracker
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/BaseActionRunnable
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/ReadActionProcessor
instanceKlass org/jetbrains/kotlin/com/intellij/util/CollectionQuery
instanceKlass org/jetbrains/kotlin/com/intellij/util/CommonProcessors$CollectProcessor
instanceKlass org/jetbrains/kotlin/com/intellij/util/Processors
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/CachedValueProfiler$Overhead
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/CachedValueProfiler$ThreadContext
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/CachedValueProfiler$GlobalContext
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/CachedValueProfiler$EventPlace
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/CachedValueProfiler
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionManager$StackFrame
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionManager$MyKey
instanceKlass org/jetbrains/kotlin/com/intellij/util/CachedValueBase$Data
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/file/PsiPackageImpl$PackageAnnotationValueProvider
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/AbstractSignatureParts$TypeAndDefaultQualifiers
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/AbstractSignatureParts
instanceKlass org/jetbrains/kotlin/types/TypeAttributeTranslator$DefaultImpls
instanceKlass org/jetbrains/kotlin/types/TypeAttributesKt
instanceKlass org/jetbrains/kotlin/types/ErasureTypeAttributes
instanceKlass org/jetbrains/kotlin/load/java/lazy/types/JavaTypeAttributesKt
instanceKlass org/jetbrains/kotlin/load/java/FakePureImplementationsProvider
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaArrayAnnotationArgument
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaEnumValueAnnotationArgument
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/ClassifierResolutionContext$Result
instanceKlass org/jetbrains/kotlin/utils/SmartList$SingletonIteratorBase
instanceKlass org/jetbrains/kotlin/types/SpecialTypesKt
instanceKlass org/jetbrains/kotlin/types/DynamicTypesKt
instanceKlass org/jetbrains/kotlin/load/java/components/JavaAnnotationMapper
instanceKlass org/jetbrains/kotlin/resolve/TypeResolver$Companion$WhenMappings
instanceKlass org/jetbrains/kotlin/resolve/AnnotationTargetList
instanceKlass org/jetbrains/kotlin/resolve/AnnotationTargetLists$TargetListBuilder
instanceKlass org/jetbrains/kotlin/descriptors/annotations/KotlinTarget$Companion
instanceKlass org/jetbrains/kotlin/resolve/AnnotationTargetLists
instanceKlass org/jetbrains/kotlin/resolve/ModifierCheckerCore
instanceKlass gnu/trove/TIterator
instanceKlass gnu/trove/THashMap$MapBackedView
instanceKlass org/jetbrains/kotlin/resolve/descriptorUtil/DescriptorUtilsKt$WhenMappings
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/ClassQualifier
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/ClassifierQualifier
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/PlainJavaAnnotationArgument
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaLiteralAnnotationArgument
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaAnnotationArgument
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaWildcardType
instanceKlass org/jetbrains/kotlin/descriptors/annotations/AnnotationsKt
instanceKlass org/jetbrains/kotlin/descriptors/Visibilities
instanceKlass org/jetbrains/kotlin/descriptors/DeserializedDeclarationsFromSupertypeConflictDataKey
instanceKlass org/jetbrains/kotlin/utils/SmartSet$SingletonIterator
instanceKlass org/jetbrains/kotlin/load/java/SpecialGenericSignatures$Companion$NameAndSignature
instanceKlass org/jetbrains/kotlin/load/java/SpecialGenericSignatures$Companion
instanceKlass org/jetbrains/kotlin/load/java/SpecialGenericSignatures
instanceKlass org/jetbrains/kotlin/resolve/OverridingUtil$8
instanceKlass org/jetbrains/kotlin/resolve/OverridingUtil$OverrideCompatibilityInfo
instanceKlass org/jetbrains/kotlin/types/checker/ClassicTypeCheckerStateKt
instanceKlass org/jetbrains/kotlin/resolve/OverridingUtilTypeSystemContext
instanceKlass org/jetbrains/kotlin/name/CallableId$Companion
instanceKlass org/jetbrains/kotlin/name/CallableId
instanceKlass org/jetbrains/kotlin/serialization/deserialization/SuspendFunctionTypeUtilKt
instanceKlass org/jetbrains/kotlin/load/java/JavaDescriptorVisibilities
instanceKlass org/jetbrains/kotlin/load/java/UtilsKt
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/JavaModifierListOwnerImpl
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/JavaAnnotationOwnerImpl
instanceKlass org/jetbrains/kotlin/load/java/lazy/LazyJavaTypeParameterResolver
instanceKlass org/jetbrains/kotlin/load/java/components/JavaSourceElementImpl
instanceKlass org/jetbrains/kotlin/load/java/lazy/descriptors/LazyJavaClassDescriptor$Companion
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryJavaTypeParameter
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaTypeParameter
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryJavaAnnotation$Companion
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryJavaAnnotation
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaAnnotation
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryJavaValueParameter
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaValueParameter
instanceKlass org/jetbrains/org/objectweb/asm/MethodVisitor
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaConstructor
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryJavaMethodBase$Companion$MethodInfo
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryJavaMethodBase$Companion
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryJavaMethodBase
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaArrayType
instanceKlass org/jetbrains/org/objectweb/asm/FieldVisitor
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryJavaField
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryJavaClass$WhenMappings
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaPrimitiveType
instanceKlass java/text/StringCharacterIterator
instanceKlass java/text/CharacterIterator
instanceKlass javaslang/control/Option$Some
instanceKlass javaslang/control/Option
instanceKlass org/jetbrains/kotlin/util/javaslang/JavaslangAdaptersKt
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/ClassifierResolutionContext$InnerClassInfo
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/CommonMixinsKt
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/JavaPlainType
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaClassifierType
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaType
instanceKlass org/jetbrains/kotlin/load/java/structure/ListBasedJavaAnnotationOwner
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryJavaModifierListOwner
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/VirtualFileBoundJavaClass
instanceKlass org/jetbrains/kotlin/load/java/structure/MutableJavaAnnotationOwner
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/ClassifierResolutionContext
instanceKlass org/jetbrains/kotlin/resolve/calls/results/SingleOverloadResolutionResult$WhenMappings
instanceKlass org/jetbrains/kotlin/resolve/calls/results/AbstractOverloadResolutionResults
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/CallInfo
instanceKlass org/jetbrains/kotlin/resolve/calls/components/CompletedCallInfo
instanceKlass org/jetbrains/kotlin/resolve/calls/DiagnosticReporterByTrackingStrategy
instanceKlass org/jetbrains/kotlin/resolve/calls/model/DiagnosticReporter
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ResolvedCallAtomsKt
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/TrackingBindingTrace
instanceKlass org/jetbrains/kotlin/resolve/inline/InlineUtil
instanceKlass org/jetbrains/kotlin/resolve/sam/SamConstructorDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/synthetic/FunctionInterfaceConstructorDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/synthetic/SyntheticMemberDescriptor
instanceKlass org/jetbrains/kotlin/synthetic/SyntheticJavaPropertyDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/SyntheticPropertyDescriptor
instanceKlass org/jetbrains/kotlin/load/java/descriptors/JavaCallableMemberDescriptor
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ArgumentMappingKt
instanceKlass org/jetbrains/kotlin/resolve/checkers/MissingDependencySupertypeChecker
instanceKlass org/jetbrains/kotlin/contracts/parsing/ContractsDslNames
instanceKlass org/jetbrains/kotlin/contracts/parsing/PsiContractsUtilsKt
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ExpressionValueArgument
instanceKlass org/jetbrains/kotlin/resolve/calls/util/ResolvedCallUtilKt$WhenMappings
instanceKlass org/jetbrains/kotlin/resolve/calls/util/ResolvedCallUtilKt
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/ConstructorHeaderCallCheckerKt
instanceKlass org/jetbrains/kotlin/resolve/calls/tasks/DynamicCallsKt
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/CallCheckerKt
instanceKlass org/jetbrains/kotlin/descriptors/DescriptorUtilKt
instanceKlass org/jetbrains/kotlin/resolve/calls/model/VariableAsFunctionResolvedCall
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/PSIKotlinCallsKt
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/ResolvedAtomCompleter
instanceKlass org/jetbrains/kotlin/resolve/calls/model/KotlinCallDiagnosticsKt
instanceKlass org/jetbrains/kotlin/resolve/calls/model/PostponedCallableReferenceMarker
instanceKlass org/jetbrains/kotlin/resolve/calls/model/LambdaWithTypeVariableAsExpectedTypeMarker
instanceKlass org/jetbrains/kotlin/types/checker/ClassicTypeSystemContext$DefaultImpls
instanceKlass kotlin/collections/builders/SetBuilder$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ResolutionAtomsKt
instanceKlass org/jetbrains/kotlin/resolve/calls/components/KotlinCallCompleter$WhenMappings
instanceKlass org/jetbrains/kotlin/resolve/calls/components/KotlinCallCompleterKt
instanceKlass org/jetbrains/kotlin/resolve/calls/components/CompletionModeCalculator$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/components/CompletionModeCalculator
instanceKlass org/jetbrains/kotlin/resolve/KtDescriptorUtilKt
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/NewTypeSubstitutorByConstructorMap
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/InferenceUtilsKt
instanceKlass org/jetbrains/kotlin/types/checker/NewCapturedTypeConstructor
instanceKlass org/jetbrains/kotlin/resolve/RecursiveContractHelperKt
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/CandidateApplicabilityKt
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/KnownResultProcessor
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/ImplicitScopeTowerKt
instanceKlass org/jetbrains/kotlin/resolve/calls/components/CheckExplicitReceiverKindConsistency$WhenMappings
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/EmptySubstitutor
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/FreshVariableNewTypeSubstitutor$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/FreshVariableNewTypeSubstitutor
instanceKlass org/jetbrains/kotlin/utils/addToStdlib/AddToStdlibKt
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/TowerUtilsKt
instanceKlass org/jetbrains/kotlin/resolve/deprecation/DescriptorBasedDeprecationInfoKt$DEPRECATED_FUNCTION_KEY$1
instanceKlass org/jetbrains/kotlin/descriptors/CallableDescriptor$UserDataKey
instanceKlass org/jetbrains/kotlin/resolve/deprecation/DescriptorBasedDeprecationInfoKt
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/OperatorCallCheckerKt
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/NewResolutionOldInferenceKt
instanceKlass org/jetbrains/kotlin/types/AbstractTypeChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/model/ConstraintStorage$Empty
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/DescriptorRelatedInferenceUtilsKt
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/model/MutableConstraintStorage
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/NewConstraintSystem
instanceKlass org/jetbrains/kotlin/resolve/calls/components/ClassicTypeSystemContextForCSKt
instanceKlass org/jetbrains/kotlin/resolve/calls/model/SimpleCandidateFactory
instanceKlass org/jetbrains/kotlin/resolve/calls/model/KotlinCallKt$WhenMappings
instanceKlass org/jetbrains/kotlin/resolve/calls/model/KotlinCallKt
instanceKlass org/jetbrains/kotlin/resolve/calls/model/GivenCandidate
instanceKlass org/jetbrains/kotlin/psi/KtPsiFactoryKt
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/PSICallResolver$ASTScopeTower
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/PSIKotlinCall
instanceKlass org/jetbrains/kotlin/resolve/calls/util/DelegatingCall
instanceKlass org/jetbrains/kotlin/resolve/calls/util/CallUtilKt
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ResolutionPart
instanceKlass org/jetbrains/kotlin/resolve/calls/GenericCandidateResolverKt
instanceKlass org/jetbrains/kotlin/resolve/calls/CallResolver$ResolutionTask
instanceKlass org/jetbrains/kotlin/resolve/calls/tasks/TracingStrategy$1
instanceKlass org/jetbrains/kotlin/resolve/calls/tasks/AbstractTracingStrategy
instanceKlass org/jetbrains/kotlin/resolve/scopes/SyntheticScopesKt
instanceKlass org/jetbrains/kotlin/resolve/calls/util/CallResolverUtilKt
instanceKlass org/jetbrains/kotlin/resolve/calls/context/ResolutionResultsCacheImpl
instanceKlass org/jetbrains/kotlin/resolve/calls/context/ResolutionResultsCache
instanceKlass org/jetbrains/kotlin/resolve/calls/components/InferenceSession$Companion$default$1
instanceKlass org/jetbrains/kotlin/resolve/calls/components/InferenceSession$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/util/CallMaker$3
instanceKlass org/jetbrains/kotlin/resolve/calls/util/CallMaker
instanceKlass org/jetbrains/kotlin/types/FlexibleTypesKt
instanceKlass org/jetbrains/kotlin/resolve/deprecation/DeprecationResolver$DeprecationInfo$Companion
instanceKlass org/jetbrains/kotlin/metadata/deserialization/VersionRequirement$Companion
instanceKlass org/jetbrains/kotlin/metadata/deserialization/VersionRequirement
instanceKlass org/jetbrains/kotlin/resolve/SinceKotlinAccessibility
instanceKlass org/jetbrains/kotlin/resolve/calls/util/FakeCallableDescriptorForObject
instanceKlass org/jetbrains/kotlin/descriptors/annotations/AnnotationDescriptor$DefaultImpls
instanceKlass org/jetbrains/kotlin/descriptors/annotations/AnnotationDescriptorImpl
instanceKlass org/jetbrains/kotlin/resolve/constants/ConstantValueFactory
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/CapturedTypeConstructorImpl
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/CapturedTypeConstructor
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/CapturedTypeConstructorKt
instanceKlass org/jetbrains/kotlin/resolve/scopes/SubstitutingScope
instanceKlass org/jetbrains/kotlin/descriptors/impl/ModuleAwareClassDescriptorKt
instanceKlass org/jetbrains/kotlin/types/TypeProjectionBase
instanceKlass org/jetbrains/kotlin/serialization/deserialization/descriptors/DeserializedCallableMemberDescriptor
instanceKlass org/jetbrains/kotlin/serialization/deserialization/descriptors/DeserializedMemberDescriptor
instanceKlass org/jetbrains/kotlin/serialization/deserialization/descriptors/DescriptorWithContainerSource
instanceKlass org/jetbrains/kotlin/load/java/components/DescriptorResolverUtils
instanceKlass org/jetbrains/kotlin/load/kotlin/BinaryClassAnnotationAndConstantLoaderImpl$AbstractAnnotationArgumentVisitor$visitArray$1
instanceKlass org/jetbrains/kotlin/load/kotlin/BinaryClassAnnotationAndConstantLoaderImpl$AbstractAnnotationArgumentVisitor
instanceKlass org/jetbrains/kotlin/resolve/ResolutionAnchorProviderKt
instanceKlass org/jetbrains/kotlin/descriptors/FindClassInModuleKt
instanceKlass org/jetbrains/kotlin/SpecialJvmAnnotations
instanceKlass org/jetbrains/kotlin/load/kotlin/AbstractBinaryClassAnnotationLoader$loadClassAnnotations$1
instanceKlass org/jetbrains/kotlin/resolve/SinceKotlinUtilKt
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$1
instanceKlass org/jetbrains/kotlin/resolve/AllUnderImportScope$Companion
instanceKlass kotlin/collections/ArraysKt___ArraysKt$asIterable$$inlined$Iterable$1
instanceKlass org/jetbrains/kotlin/resolve/OverridingStrategy
instanceKlass org/jetbrains/kotlin/resolve/InlineClassesUtilsKt
instanceKlass org/jetbrains/kotlin/resolve/DelegationResolver$Companion
instanceKlass org/jetbrains/kotlin/resolve/DelegationResolver
instanceKlass org/jetbrains/kotlin/resolve/lazy/descriptors/LazyClassMemberScope$generateDelegatingDescriptors$lazyMemberExtractor$1
instanceKlass org/jetbrains/kotlin/resolve/DelegationResolver$MemberExtractor
instanceKlass org/jetbrains/kotlin/resolve/lazy/descriptors/LazyClassMemberScope$generateDelegatingDescriptors$lazyTypeResolver$1
instanceKlass org/jetbrains/kotlin/resolve/DelegationResolver$TypeResolver
instanceKlass org/jetbrains/kotlin/resolve/BindingContextUtils
instanceKlass org/jetbrains/kotlin/resolve/lazy/ForceResolveUtil
instanceKlass kotlin/reflect/jvm/internal/impl/name/SpecialNames
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/CompanionObjectMapping
instanceKlass kotlin/reflect/jvm/internal/impl/name/FqNamesUtilKt
instanceKlass kotlin/reflect/jvm/internal/impl/utils/CollectionsKt
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/PrimitiveType$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/StandardNames$FqNames
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JavaToKotlinClassMap$PlatformMutabilityMapping
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/functions/FunctionClassKind$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/StandardNames
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JavaToKotlinClassMap
instanceKlass kotlin/jvm/functions/Function22
instanceKlass kotlin/jvm/functions/Function21
instanceKlass kotlin/jvm/functions/Function20
instanceKlass kotlin/jvm/functions/Function19
instanceKlass kotlin/jvm/functions/Function18
instanceKlass kotlin/jvm/functions/Function17
instanceKlass kotlin/jvm/functions/Function16
instanceKlass kotlin/jvm/functions/Function15
instanceKlass kotlin/jvm/functions/Function14
instanceKlass kotlin/jvm/functions/Function13
instanceKlass kotlin/jvm/functions/Function12
instanceKlass kotlin/jvm/functions/Function11
instanceKlass kotlin/jvm/functions/Function10
instanceKlass kotlin/jvm/functions/Function9
instanceKlass kotlin/jvm/functions/Function8
instanceKlass kotlin/jvm/functions/Function7
instanceKlass kotlin/jvm/functions/Function6
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/structure/ReflectClassUtilKt
instanceKlass kotlin/reflect/jvm/internal/impl/name/ClassId
instanceKlass kotlin/reflect/jvm/internal/impl/name/FqNameUnsafe$1
instanceKlass kotlin/reflect/jvm/internal/impl/name/Name
instanceKlass kotlin/reflect/jvm/internal/impl/name/FqNameUnsafe
instanceKlass kotlin/reflect/jvm/internal/impl/name/FqName
instanceKlass kotlin/reflect/jvm/internal/RuntimeTypeMapper
instanceKlass kotlin/reflect/jvm/internal/KDeclarationContainerImpl$Data
instanceKlass org/jetbrains/kotlin/util/AbstractArrayMapOwner$AbstractArrayMapAccessor
instanceKlass org/jetbrains/kotlin/types/TypeAttribute
instanceKlass org/jetbrains/kotlin/types/AnnotationsTypeAttributeKt
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/ProjectCoreUtil
instanceKlass java/util/ArrayList$SubList$1
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinJavaPsiFacade$3
instanceKlass kotlin/sequences/DropWhileSequence$iterator$1
instanceKlass kotlin/sequences/DropWhileSequence
instanceKlass org/jetbrains/kotlin/psi/typeRefHelpers/TypeRefHelpersKt
instanceKlass org/jetbrains/kotlin/resolve/scopes/LexicalScopeStorage$IntList
instanceKlass org/jetbrains/kotlin/resolve/scopes/utils/LexicalScopeWrapper
instanceKlass org/jetbrains/kotlin/types/expressions/ExpressionTypingUtils
instanceKlass org/jetbrains/kotlin/descriptors/impl/ValueParameterDescriptorImpl$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/util/UnderscoreUtilKt
instanceKlass org/jetbrains/kotlin/types/TypeConstructorSubstitution$Companion
instanceKlass org/jetbrains/kotlin/types/TypeSubstitution$Companion
instanceKlass org/jetbrains/kotlin/types/TypeSubstitution
instanceKlass org/jetbrains/kotlin/types/IntersectionTypeConstructor
instanceKlass org/jetbrains/kotlin/types/model/IntersectionTypeConstructorMarker
instanceKlass org/jetbrains/kotlin/types/CustomTypeParameter
instanceKlass org/jetbrains/kotlin/utils/SmartSet$Companion
instanceKlass org/jetbrains/kotlin/types/typeUtil/TypeUtilsKt
instanceKlass org/jetbrains/kotlin/util/EmptyArrayMap$iterator$1
instanceKlass org/jetbrains/kotlin/resolve/checkers/TrailingCommaChecker
instanceKlass org/jetbrains/kotlin/resolve/PlatformClassesMappedToKotlinChecker
instanceKlass org/jetbrains/kotlin/descriptors/impl/TypeAliasConstructorDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/impl/DescriptorDerivedFromTypeAlias
instanceKlass org/jetbrains/kotlin/descriptors/DescriptorVisibilityUtils
instanceKlass org/jetbrains/kotlin/resolve/QualifiedExpressionResolverKt
instanceKlass org/jetbrains/kotlin/descriptors/DescriptorWithDeprecation$Companion
instanceKlass org/jetbrains/kotlin/descriptors/DescriptorWithDeprecation
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinJvmBinarySourceElement
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinCliJavaFileManagerImpl$WhenMappings
instanceKlass org/jetbrains/kotlin/load/java/lazy/descriptors/LazyJavaPackageScope$KotlinClassLookupResult
instanceKlass org/jetbrains/kotlin/load/java/lazy/descriptors/LazyJavaPackageScope$FindClassRequest
instanceKlass org/jetbrains/kotlin/utils/CallOnceFunction
instanceKlass java/util/AbstractList$SubList$1
instanceKlass org/jetbrains/kotlin/resolve/PackageFragmentWithCustomSource
instanceKlass org/jetbrains/kotlin/com/google/common/collect/PeekingIterator
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Iterators
instanceKlass org/jetbrains/kotlin/com/google/common/collect/ObjectArrays
instanceKlass org/jetbrains/kotlin/com/google/common/collect/ImmutableMultimap$Builder
instanceKlass org/jetbrains/kotlin/resolve/scopes/ImportingScope$DefaultImpls
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopeFactory$currentPackageScope$1
instanceKlass org/jetbrains/kotlin/resolve/lazy/LazyImportScope
instanceKlass org/jetbrains/kotlin/resolve/DelegatingBindingTrace$MyBindingContext
instanceKlass org/jetbrains/kotlin/resolve/AnalyzingUtils
instanceKlass org/jetbrains/kotlin/resolve/extensions/ExtraImportsProviderExtension$Companion$CompoundExtraImportsProviderExtension
instanceKlass org/jetbrains/kotlin/resolve/scopes/MemberScope$DefaultImpls
instanceKlass org/jetbrains/kotlin/resolve/scopes/ResolutionScope$DefaultImpls
instanceKlass org/jetbrains/kotlin/resolve/scopes/HierarchicalScope$DefaultImpls
instanceKlass org/jetbrains/kotlin/resolve/scopes/LexicalScope$DefaultImpls
instanceKlass org/jetbrains/kotlin/psi/KtCodeFragmentBase
instanceKlass org/jetbrains/kotlin/com/intellij/psi/JavaCodeFragment
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiCodeFragment
instanceKlass org/jetbrains/kotlin/psi/codeFragmentUtil/CodeFragmentUtilKt
instanceKlass org/jetbrains/kotlin/resolve/scopes/LexicalWritableScope$Snapshot
instanceKlass org/jetbrains/kotlin/resolve/scopes/AbstractLocalRedeclarationChecker
instanceKlass org/jetbrains/kotlin/descriptors/annotations/TargetedAnnotations
instanceKlass org/jetbrains/kotlin/resolve/lazy/descriptors/LazyAnnotationDescriptor
instanceKlass kotlin/jvm/functions/Function5
instanceKlass java/util/stream/Sink$ChainedInt
instanceKlass java/util/stream/Sink$OfInt
instanceKlass java/util/function/IntConsumer
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/ReduceOps
instanceKlass java/util/stream/IntStream
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/ImplicitClassReceiver
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/ThisClassReceiver
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/ImplicitReceiver
instanceKlass org/jetbrains/kotlin/resolve/scopes/LexicalChainedScope$Companion
instanceKlass org/jetbrains/kotlin/resolve/scopes/LexicalChainedScope
instanceKlass kotlin/collections/ReversedList$listIterator$1
instanceKlass kotlin/jvm/internal/markers/KMutableListIterator
instanceKlass kotlin/jvm/internal/markers/KMutableList
instanceKlass org/jetbrains/kotlin/builtins/CompanionObjectMapping
instanceKlass org/jetbrains/kotlin/builtins/jvm/JavaToKotlinClassMap$PlatformMutabilityMapping
instanceKlass org/jetbrains/kotlin/name/StandardClassIdsKt
instanceKlass org/jetbrains/kotlin/name/StandardClassIds
instanceKlass org/jetbrains/kotlin/builtins/jvm/JavaToKotlinClassMap
instanceKlass org/jetbrains/kotlin/load/kotlin/SignatureBuildingComponents
instanceKlass org/jetbrains/kotlin/builtins/jvm/JvmBuiltInsSignatures
instanceKlass org/jetbrains/kotlin/metadata/deserialization/ProtoTypeTableUtilKt
instanceKlass org/jetbrains/kotlin/utils/DFS
instanceKlass org/jetbrains/kotlin/utils/DFS$AbstractNodeHandler
instanceKlass org/jetbrains/kotlin/utils/DFS$NodeHandler
instanceKlass org/jetbrains/kotlin/utils/DFS$VisitedWithSet
instanceKlass org/jetbrains/kotlin/utils/DFS$Visited
instanceKlass org/jetbrains/kotlin/resolve/FindLoopsInSupertypes
instanceKlass org/jetbrains/kotlin/utils/DFS$Neighbors
instanceKlass org/jetbrains/kotlin/storage/SingleThreadValue
instanceKlass org/jetbrains/kotlin/types/KotlinTypeKt
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$TypeAliasOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Effect$InvocationKind$1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Expression$ConstantValue$1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$ExpressionOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Effect$EffectType$1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$EffectOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$VersionRequirement$VersionKind$1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$VersionRequirement$Level$1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$VersionRequirementOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/jvm/deserialization/JvmNameResolverBase$WhenMappings
instanceKlass org/jetbrains/kotlin/metadata/deserialization/ProtoBufUtilKt
instanceKlass org/jetbrains/kotlin/load/kotlin/JvmPackagePartSource
instanceKlass org/jetbrains/kotlin/serialization/deserialization/descriptors/DeserializedContainerSource
instanceKlass org/jetbrains/kotlin/load/kotlin/FacadeClassSource
instanceKlass org/jetbrains/kotlin/metadata/jvm/deserialization/JvmNameResolverKt
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmProtoBuf$StringTableTypes$Record$Operation$1
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmProtoBuf$StringTableTypes$RecordOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmProtoBuf$StringTableTypesOrBuilder
instanceKlass kotlin/collections/IndexedValue
instanceKlass kotlin/collections/IndexingIterator
instanceKlass kotlin/collections/IndexingIterable
instanceKlass org/jetbrains/kotlin/metadata/jvm/deserialization/JvmNameResolverBase$Companion
instanceKlass org/jetbrains/kotlin/metadata/jvm/deserialization/JvmNameResolverBase
instanceKlass org/jetbrains/kotlin/metadata/jvm/deserialization/UtfEncodingKt
instanceKlass org/jetbrains/kotlin/metadata/jvm/deserialization/BitEncoding
instanceKlass org/jetbrains/kotlin/load/kotlin/FileBasedKotlinClass$OuterAndInnerName
instanceKlass org/jetbrains/kotlin/load/kotlin/header/KotlinClassHeader
instanceKlass org/jetbrains/kotlin/load/kotlin/header/ReadKotlinClassHeaderAnnotationVisitor$CollectStringArrayAnnotationVisitor
instanceKlass org/jetbrains/org/objectweb/asm/Type
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinJvmBinaryClass$AnnotationArrayArgumentVisitor
instanceKlass org/jetbrains/kotlin/load/kotlin/header/ReadKotlinClassHeaderAnnotationVisitor$KotlinMetadataArgumentVisitor
instanceKlass java/lang/Override
instanceKlass org/jetbrains/kotlin/load/java/JvmAnnotationNames
instanceKlass org/jetbrains/org/objectweb/asm/Context
instanceKlass org/jetbrains/org/objectweb/asm/Attribute
instanceKlass org/jetbrains/org/objectweb/asm/Label
instanceKlass org/jetbrains/org/objectweb/asm/ClassReader
instanceKlass org/jetbrains/kotlin/load/kotlin/FileBasedKotlinClass$InnerClassesInfo
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinJvmBinaryClass$AnnotationArgumentVisitor
instanceKlass org/jetbrains/kotlin/load/kotlin/header/ReadKotlinClassHeaderAnnotationVisitor
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinJvmBinaryClass$AnnotationVisitor
instanceKlass kotlin/jvm/functions/Function4
instanceKlass org/jetbrains/kotlin/load/kotlin/VirtualFileKotlinClass$Factory
instanceKlass org/jetbrains/org/objectweb/asm/AnnotationVisitor
instanceKlass org/jetbrains/org/objectweb/asm/ClassVisitor
instanceKlass org/jetbrains/kotlin/load/kotlin/FileBasedKotlinClass
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Computable
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinBinaryClassCache$RequestCache
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinClassFinderKt
instanceKlass org/jetbrains/kotlin/resolve/jvm/JvmClassName
instanceKlass gnu/trove/TObjectProcedure
instanceKlass org/jetbrains/kotlin/resolve/jvm/multiplatform/OptionalAnnotationClassDataFinder
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JvmDependenciesIndexImpl$SearchResult
instanceKlass org/jetbrains/kotlin/load/java/lazy/LazyJavaAnnotations
instanceKlass org/jetbrains/kotlin/load/java/lazy/LazyJavaAnnotationsKt
instanceKlass org/jetbrains/kotlin/load/java/lazy/descriptors/JvmPackageScope
instanceKlass kotlin/UnsafeLazyImpl
instanceKlass org/jetbrains/kotlin/load/java/lazy/ContextKt
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaElementsKt
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/source/JavaElementPsiSource
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/JavaElementImpl
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/source/JavaSourceFactoryOwner
instanceKlass org/jetbrains/kotlin/load/java/structure/MapBasedJavaAnnotationOwner
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/source/JavaElementSourceFactory$Companion
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JvmDependenciesIndexImpl$FindClassRequest
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JvmDependenciesIndexImpl$WhenMappings
instanceKlass gnu/trove/PrimeFinder
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/IntArrayList
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JvmDependenciesIndexImpl$Cache
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JvmDependenciesIndexImpl$TraverseRequest
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JvmDependenciesIndexImpl$SearchRequest
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinCliJavaFileManagerImplKt
instanceKlass kotlin/jvm/internal/Ref$BooleanRef
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinSafeClassFinder
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ImmutableList$Itr
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinJavaPsiFacade$CliFinder
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/CompactVirtualFileSet$2
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/SequenceIterator
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinJavaPsiFacade$PackageCache
instanceKlass org/jetbrains/kotlin/descriptors/annotations/Annotations$DefaultImpls
instanceKlass org/jetbrains/kotlin/resolve/jvm/annotations/JvmAnnotationUtilKt
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Lists
instanceKlass org/jetbrains/kotlin/resolve/scopes/LexicalScopeImpl$InitializeHandler
instanceKlass org/jetbrains/kotlin/resolve/scopes/LocalRedeclarationChecker$DO_NOTHING
instanceKlass org/jetbrains/kotlin/resolve/scopes/utils/ScopeUtilsKt
instanceKlass org/jetbrains/kotlin/resolve/bindingContextUtil/BindingContextUtilsKt
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopeFactory$FilesScopesBuilder$importResolver$1
instanceKlass org/jetbrains/kotlin/resolve/scopes/BaseHierarchicalScope
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopeFactory$FilesScopesBuilder$lazyImportingScope$1
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopeFactoryKt
instanceKlass org/jetbrains/kotlin/resolve/lazy/LazyImportResolver
instanceKlass org/jetbrains/kotlin/resolve/lazy/IndexedImports
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopeFactory$FilesScopesBuilder
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopeProviderKt
instanceKlass org/jetbrains/kotlin/types/AbstractTypeConstructor$Supertypes
instanceKlass org/jetbrains/kotlin/resolve/lazy/descriptors/LazyClassMemberScope$Companion$EXTRACT_PROPERTIES$1
instanceKlass org/jetbrains/kotlin/resolve/lazy/descriptors/LazyClassMemberScope$Companion$EXTRACT_FUNCTIONS$1
instanceKlass org/jetbrains/kotlin/resolve/lazy/descriptors/LazyClassMemberScope$MemberExtractor
instanceKlass org/jetbrains/kotlin/resolve/lazy/descriptors/LazyClassMemberScope$Companion
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/QualifierKt
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/PackageQualifier
instanceKlass org/jetbrains/kotlin/psi/KtNamedDeclarationUtil
instanceKlass org/jetbrains/kotlin/resolve/lazy/descriptors/ClassResolutionScopesSupport$Companion
instanceKlass org/jetbrains/kotlin/resolve/lazy/descriptors/ClassResolutionScopesSupport
instanceKlass org/jetbrains/kotlin/resolve/source/KotlinSourceElement
instanceKlass org/jetbrains/kotlin/resolve/source/PsiSourceElement
instanceKlass org/jetbrains/kotlin/resolve/source/KotlinSourceElementKt
instanceKlass org/jetbrains/kotlin/descriptors/ValueClassRepresentation
instanceKlass org/jetbrains/kotlin/resolve/lazy/data/KtClassOrObjectInfo
instanceKlass org/jetbrains/kotlin/resolve/lazy/data/KtClassInfoUtil
instanceKlass org/jetbrains/kotlin/com/google/common/collect/LinkedHashMultimap$ValueSet$1
instanceKlass org/jetbrains/kotlin/com/google/common/collect/AbstractMapBasedMultimap$WrappedCollection$WrappedIterator
instanceKlass org/jetbrains/kotlin/com/google/common/collect/ListMultimap
instanceKlass org/jetbrains/kotlin/resolve/lazy/declarations/AbstractPsiBasedDeclarationProvider$Index
instanceKlass org/jetbrains/kotlin/storage/LockBasedStorageManager$KeyWithComputation
instanceKlass org/jetbrains/kotlin/resolve/lazy/declarations/AbstractPsiBasedDeclarationProvider
instanceKlass org/jetbrains/kotlin/parsing/KotlinParsing$PropertyComponentKind$Collector
instanceKlass org/jetbrains/kotlin/psi/psiUtil/KtPsiUtilKt
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/PsiElementProcessor$FindElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/PsiElementProcessor$CollectElements
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/PsiElementProcessor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/PsiTreeUtil
instanceKlass org/jetbrains/kotlin/util/slicedMap/OpenAddressLinearProbingHashTableKt
instanceKlass org/jetbrains/kotlin/util/slicedMap/OpenAddressLinearProbingHashTable$Companion
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Sets
instanceKlass org/jetbrains/kotlin/resolve/FilePreprocessorKt
instanceKlass org/jetbrains/kotlin/psi/KtPsiUtil
instanceKlass org/jetbrains/kotlin/psi/KtSimpleNameExpressionImpl$Companion
instanceKlass org/jetbrains/kotlin/psi/KtExpressionImpl$Companion
instanceKlass org/jetbrains/kotlin/psi/KtNameReferenceExpression$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/SourceTreeToPsiMap
instanceKlass org/jetbrains/kotlin/psi/psiUtil/PsiUtilsKt$siblings$1$iterator$1
instanceKlass org/jetbrains/kotlin/psi/psiUtil/PsiUtilsKt$siblings$1
instanceKlass org/jetbrains/kotlin/psi/psiUtil/PsiUtilsKt
instanceKlass org/jetbrains/kotlin/psi/KtDotQualifiedExpression$Companion
instanceKlass org/jetbrains/kotlin/psi/KtStubbedPsiUtil
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/AccessToken
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/Cancellation
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/WrappedProgressIndicator
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/impl/NonCancelableIndicator
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/StandardProgressIndicator
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/ThrowableComputable
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/ILeafElementType
instanceKlass org/jetbrains/kotlin/kdoc/psi/api/KDoc
instanceKlass org/jetbrains/kotlin/kdoc/psi/api/KDocElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiDocCommentBase
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/ICustomParsingType
instanceKlass org/jetbrains/kotlin/com/intellij/lang/WhitespacesAndCommentsBinder$RecursiveBinder
instanceKlass org/jetbrains/kotlin/com/intellij/lang/impl/PsiBuilderImpl$RelativeTokenTextView
instanceKlass org/jetbrains/kotlin/parsing/PrecedingCommentsBinder
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntArrays$ArrayHashStrategy
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntArrays$Segment
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntArrays
instanceKlass org/jetbrains/kotlin/parsing/PrecedingDocCommentsBinder
instanceKlass org/jetbrains/kotlin/parsing/AbstractKotlinParsing$OptionalMarker
instanceKlass org/jetbrains/kotlin/parsing/KotlinParsing$ModifierDetector
instanceKlass java/util/function/IntUnaryOperator
instanceKlass org/jetbrains/kotlin/com/intellij/lang/WhitespacesBinders$2
instanceKlass org/jetbrains/kotlin/com/intellij/lang/WhitespacesBinders$1
instanceKlass org/jetbrains/kotlin/com/intellij/lang/WhitespacesBinders
instanceKlass org/jetbrains/kotlin/parsing/TrailingCommentsBinder
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/CharSequenceSubSequence
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/Arrays
instanceKlass org/jetbrains/kotlin/com/google/common/collect/ImmutableCollection$Builder
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass org/jetbrains/kotlin/com/google/common/collect/ImmutableMap$Builder
instanceKlass org/jetbrains/kotlin/parsing/AbstractTokenStreamPredicate
instanceKlass org/jetbrains/kotlin/parsing/AbstractTokenStreamPattern
instanceKlass org/jetbrains/kotlin/parsing/TokenStreamPattern
instanceKlass org/jetbrains/kotlin/parsing/Consumer
instanceKlass org/jetbrains/kotlin/parsing/TokenStreamPredicate
instanceKlass org/jetbrains/kotlin/com/intellij/lang/WhitespacesAndCommentsBinder
instanceKlass org/jetbrains/kotlin/parsing/AbstractKotlinParsing
instanceKlass org/jetbrains/kotlin/com/intellij/lang/impl/PsiBuilderAdapter
instanceKlass org/jetbrains/kotlin/parsing/SemanticWhitespaceAwarePsiBuilder
instanceKlass org/jetbrains/kotlin/parsing/KotlinParser
instanceKlass org/jetbrains/kotlin/lexer/_JetLexer$State
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/ImmutableText$InnerLeaf
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/ImmutableText$CompositeNode
instanceKlass org/jetbrains/kotlin/com/intellij/lang/impl/TokenSequence$Builder
instanceKlass org/jetbrains/kotlin/com/intellij/lang/impl/TokenSequence
instanceKlass org/jetbrains/kotlin/com/intellij/lexer/TokenList
instanceKlass org/jetbrains/kotlin/com/intellij/lang/impl/PsiBuilderImpl$Token
instanceKlass org/jetbrains/kotlin/com/intellij/lang/LighterLazyParseableNode
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntListIterator
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntBidirectionalIterator
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntStack
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntList
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntSpliterator
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntIterator
instanceKlass java/util/PrimitiveIterator$OfInt
instanceKlass java/util/PrimitiveIterator
instanceKlass java/util/BitSet
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/IntStack
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectArrays$ArrayHashStrategy
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectArrays
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectListIterator
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectBidirectionalIterator
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/BidirectionalIterator
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectList
instanceKlass org/jetbrains/kotlin/com/intellij/psi/text/BlockSupport
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/tree/SharedImplUtil
instanceKlass org/jetbrains/kotlin/lexer/_JetLexer
instanceKlass org/jetbrains/kotlin/com/intellij/lexer/FlexLexer
instanceKlass org/jetbrains/kotlin/com/intellij/lexer/LexerPosition
instanceKlass org/jetbrains/kotlin/com/intellij/lang/WhitespacesAndCommentsBinder$TokenTextGetter
instanceKlass org/jetbrains/kotlin/com/intellij/lang/PsiBuilder$Marker
instanceKlass org/jetbrains/kotlin/com/intellij/lang/SyntaxTreeBuilder$Marker
instanceKlass org/jetbrains/kotlin/com/intellij/lang/impl/PsiBuilderImpl$ProductionMarker
instanceKlass org/jetbrains/kotlin/com/intellij/lang/impl/PsiBuilderImpl$Node
instanceKlass org/jetbrains/kotlin/com/intellij/lang/LighterASTNode
instanceKlass org/jetbrains/kotlin/com/intellij/util/diff/ShallowNodeComparator
instanceKlass org/jetbrains/kotlin/com/intellij/util/diff/DiffTreeChangeBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/UnprotectedUserDataHolder
instanceKlass org/jetbrains/kotlin/com/intellij/util/ThrowableRunnable
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/tree/TreeUtil
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/DebugUtil
instanceKlass org/jetbrains/kotlin/com/intellij/lang/LighterAST
instanceKlass org/jetbrains/kotlin/com/intellij/util/AstLoadingFilter
instanceKlass org/jetbrains/kotlin/util/slicedMap/Slices$SliceBuilder
instanceKlass org/jetbrains/kotlin/util/slicedMap/SetSlice$Companion
instanceKlass org/jetbrains/kotlin/util/slicedMap/RewritePolicy$1
instanceKlass org/jetbrains/kotlin/resolve/BindingContext$1
instanceKlass kotlin/jvm/internal/Ref$ObjectRef
instanceKlass javaslang/collection/HashArrayMappedTrieModule$AbstractNode
instanceKlass javaslang/collection/HashArrayMappedTrie
instanceKlass javaslang/collection/Set
instanceKlass javaslang/collection/Seq
instanceKlass javaslang/collection/Iterator
instanceKlass javaslang/collection/HashMap
instanceKlass javaslang/collection/Map
instanceKlass javaslang/collection/Traversable
instanceKlass javaslang/collection/Foldable
instanceKlass javaslang/Value
instanceKlass javaslang/Function1
instanceKlass javaslang/\u03bb
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowInfoImpl$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowInfoImpl
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowInfoFactory
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowInfo$Companion
instanceKlass org/jetbrains/kotlin/library/metadata/impl/KlibResolvedModuleDescriptorsFactoryImpl$Companion
instanceKlass org/jetbrains/kotlin/library/metadata/impl/KlibResolvedModuleDescriptorsFactoryImpl
instanceKlass org/jetbrains/kotlin/library/metadata/KlibResolvedModuleDescriptorsFactory
instanceKlass org/jetbrains/kotlin/library/metadata/impl/KlibMetadataModuleDescriptorFactoryImpl
instanceKlass org/jetbrains/kotlin/library/metadata/KlibMetadataModuleDescriptorFactory
instanceKlass org/jetbrains/kotlin/library/metadata/impl/KlibMetadataDeserializedPackageFragmentsFactoryImpl
instanceKlass org/jetbrains/kotlin/library/metadata/KlibMetadataDeserializedPackageFragmentsFactory
instanceKlass org/jetbrains/kotlin/library/metadata/impl/KlibModuleDescriptorFactoryImpl
instanceKlass org/jetbrains/kotlin/library/metadata/KlibModuleDescriptorFactory
instanceKlass org/jetbrains/kotlin/library/metadata/NullFlexibleTypeDeserializer
instanceKlass org/jetbrains/kotlin/library/metadata/KlibMetadataFactories
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/TopDownAnalyzerFacadeForJVMKt
instanceKlass org/jetbrains/kotlin/load/kotlin/incremental/IncrementalPackagePartProvider
instanceKlass kotlin/reflect/jvm/internal/pcollections/ConsPStack$Itr
instanceKlass org/jetbrains/kotlin/cli/jvm/config/ClassicFrontendSpecificJvmConfigurationKeys
instanceKlass org/jetbrains/kotlin/resolve/CodeAnalyzerInitializer$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/ConstraintSystemBuilderImpl$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/ConstraintSystemBuilderImpl
instanceKlass org/jetbrains/kotlin/resolve/calls/results/FlatSignatureForResolvedCallKt
instanceKlass org/jetbrains/kotlin/resolve/calls/CallExpressionResolver$Companion
instanceKlass org/jetbrains/kotlin/builtins/jvm/JvmBuiltInsPackageFragmentProvider$Companion
instanceKlass org/jetbrains/kotlin/resolve/jvm/TopPackageNamesProvider
instanceKlass org/jetbrains/kotlin/serialization/deserialization/JvmEnumEntriesDeserializationSupport
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmProtoBuf$JvmFieldSignatureOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmProtoBuf$JvmPropertySignatureOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmProtoBuf$JvmMethodSignatureOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmProtoBuf
instanceKlass org/jetbrains/kotlin/metadata/jvm/deserialization/JvmProtoBufUtil
instanceKlass org/jetbrains/kotlin/load/kotlin/JavaFlexibleTypeDeserializer
instanceKlass org/jetbrains/kotlin/load/kotlin/DeserializationComponentsForJava$Companion
instanceKlass org/jetbrains/kotlin/load/kotlin/AbstractBinaryClassAnnotationLoader$Companion
instanceKlass org/jetbrains/kotlin/types/TypeParameterErasureOptions
instanceKlass org/jetbrains/kotlin/types/TypeParameterUpperBoundEraser$Companion
instanceKlass org/jetbrains/kotlin/types/TypeParameterUpperBoundEraser
instanceKlass org/jetbrains/kotlin/types/ErasureProjectionComputer
instanceKlass org/jetbrains/kotlin/load/java/lazy/types/JavaTypeResolver
instanceKlass kotlin/InitializedLazyImpl
instanceKlass org/jetbrains/kotlin/load/java/lazy/TypeParameterResolver$EMPTY
instanceKlass org/jetbrains/kotlin/load/java/lazy/TypeParameterResolver
instanceKlass org/jetbrains/kotlin/load/java/components/TraceBasedErrorReporter$Companion
instanceKlass org/jetbrains/kotlin/load/kotlin/header/KotlinClassHeader$Kind$Companion
instanceKlass org/jetbrains/kotlin/load/kotlin/DeserializedDescriptorResolver$Companion
instanceKlass org/jetbrains/kotlin/util/slicedMap/Slices$2
instanceKlass org/jetbrains/kotlin/util/slicedMap/Slices$1
instanceKlass org/jetbrains/kotlin/util/slicedMap/RewritePolicy
instanceKlass org/jetbrains/kotlin/util/slicedMap/Slices
instanceKlass org/jetbrains/kotlin/load/java/components/FilesByFacadeFqNameIndexer$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/util/SmartFMap
instanceKlass org/jetbrains/kotlin/resolve/ObservableBindingTrace
instanceKlass org/jetbrains/kotlin/resolve/ShadowedExtensionChecker
instanceKlass org/jetbrains/kotlin/descriptors/EffectiveVisibility
instanceKlass org/jetbrains/kotlin/resolve/ExposedVisibilityChecker
instanceKlass org/jetbrains/kotlin/resolve/DeclarationsChecker$Companion
instanceKlass org/jetbrains/kotlin/resolve/VarianceCheckerCore$Companion
instanceKlass org/jetbrains/kotlin/resolve/VarianceCheckerCore
instanceKlass org/jetbrains/kotlin/idea/MainFunctionDetector$Companion
instanceKlass org/jetbrains/kotlin/resolve/OverrideResolver$Companion
instanceKlass org/jetbrains/kotlin/load/java/AbstractAnnotationTypeQualifierResolver$Companion
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopeFactory$DefaultImportImpl
instanceKlass org/jetbrains/kotlin/util/collectionUtils/ScopeUtilsKt
instanceKlass org/jetbrains/kotlin/builtins/jvm/CloneableClassScope$Companion
instanceKlass org/jetbrains/kotlin/descriptors/TypeParameterUtilsKt
instanceKlass org/jetbrains/kotlin/incremental/UtilsKt
instanceKlass org/jetbrains/kotlin/serialization/deserialization/descriptors/DeserializedClassDescriptor$EnumEntryClassDescriptors
instanceKlass org/jetbrains/kotlin/builtins/functions/FunctionTypeKindExtractor$Companion
instanceKlass org/jetbrains/kotlin/builtins/functions/FunctionTypeKindExtractor
instanceKlass org/jetbrains/kotlin/serialization/deserialization/descriptors/DeserializedAnnotations
instanceKlass org/jetbrains/kotlin/descriptors/impl/AbstractTypeParameterDescriptor$2
instanceKlass org/jetbrains/kotlin/descriptors/impl/AbstractTypeParameterDescriptor$1
instanceKlass org/jetbrains/kotlin/descriptors/SupertypeLoopChecker$EMPTY
instanceKlass org/jetbrains/kotlin/descriptors/ScopesHolderForClass$Companion
instanceKlass org/jetbrains/kotlin/descriptors/ScopesHolderForClass
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ProtoEnumFlagsUtilsKt$WhenMappings
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ProtoEnumFlagsUtilsKt
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ProtoEnumFlags$WhenMappings
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$MemberKind$1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Class$Kind$1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Modality$1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Visibility$1
instanceKlass org/jetbrains/kotlin/metadata/deserialization/Flags$FlagField
instanceKlass org/jetbrains/kotlin/metadata/deserialization/Flags
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ProtoEnumFlags
instanceKlass org/jetbrains/kotlin/descriptors/DeserializedDescriptor
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ClassDeserializer$ClassKey
instanceKlass org/jetbrains/kotlin/resolve/scopes/ChainedMemberScope$Companion
instanceKlass org/jetbrains/kotlin/resolve/scopes/ChainedMemberScope
instanceKlass org/jetbrains/kotlin/descriptors/PackageFragmentProviderKt
instanceKlass org/jetbrains/kotlin/descriptors/impl/CompositePackageFragmentProvider
instanceKlass org/jetbrains/kotlin/resolve/scopes/MemberScope$Companion
instanceKlass org/jetbrains/kotlin/resolve/scopes/DescriptorKindFilter$Companion$MaskToName
instanceKlass org/jetbrains/kotlin/resolve/scopes/DescriptorKindFilter$Companion
instanceKlass org/jetbrains/kotlin/resolve/scopes/DescriptorKindFilter
instanceKlass org/jetbrains/kotlin/resolve/scopes/AbstractScopeAdapter
instanceKlass org/jetbrains/kotlin/resolve/PlatformDependentAnalyzerServices$DefaultImportsKey
instanceKlass org/jetbrains/kotlin/resolve/VariableTypeAndInitializerResolver$Companion
instanceKlass org/jetbrains/kotlin/resolve/checkers/ResolutionWithStubTypesChecker
instanceKlass org/jetbrains/kotlin/types/checker/SimpleClassicTypeSystemContext
instanceKlass org/jetbrains/kotlin/resolve/checkers/PassingProgressionAsCollectionCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/CallCheckerWithAdditionalResolve
instanceKlass org/jetbrains/kotlin/resolve/calls/components/NewOverloadingConflictResolver$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/KotlinConstraintSystemCompleter$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/PostponedArgumentInputTypesResolver$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/KotlinToResolvedCallTransformer$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/results/OverloadingConflictResolver$SpecificityComparisonWithNumerics$1
instanceKlass org/jetbrains/kotlin/resolve/calls/results/SpecificityComparisonCallbacks
instanceKlass org/jetbrains/kotlin/resolve/calls/results/OverloadingConflictResolver$resolvedCallHashingStrategy$1
instanceKlass org/jetbrains/kotlin/resolve/calls/components/CallableReferenceOverloadConflictResolver$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/TrivialConstraintTypeInferenceOracle$Companion
instanceKlass org/jetbrains/kotlin/types/AbstractTypeApproximator$Companion
instanceKlass org/jetbrains/kotlin/types/expressions/DoubleColonExpressionResolver$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/SmartCastManager$Companion
instanceKlass org/jetbrains/kotlin/resolve/StatementFilter$Companion
instanceKlass org/jetbrains/kotlin/resolve/AnnotationChecker$Companion
instanceKlass org/jetbrains/kotlin/resolve/TypeResolver$Companion
instanceKlass org/jetbrains/kotlin/resolve/QualifiedExpressionResolver$Companion
instanceKlass org/jetbrains/kotlin/resolve/constants/evaluate/ConstantExpressionEvaluator$Companion$ExperimentalityDiagnostic1
instanceKlass org/jetbrains/kotlin/resolve/constants/evaluate/ConstantExpressionEvaluator$Companion
instanceKlass org/jetbrains/kotlin/util/CounterWithExclude$Companion
instanceKlass org/jetbrains/kotlin/util/ReenterableCounter$Companion
instanceKlass org/jetbrains/kotlin/container/MethodBinding
instanceKlass org/jetbrains/kotlin/resolve/extensions/SyntheticResolveExtension$Companion$getInstance$1
instanceKlass org/jetbrains/kotlin/resolve/lazy/descriptors/LazyAnnotations
instanceKlass org/jetbrains/kotlin/resolve/lazy/ResolveSession$1
instanceKlass org/jetbrains/kotlin/storage/LockBasedLazyResolveStorageManager$LockProtectedContext
instanceKlass org/jetbrains/kotlin/storage/LockBasedLazyResolveStorageManager$LockProtectedTrace
instanceKlass org/jetbrains/kotlin/storage/LockBasedLazyResolveStorageManager
instanceKlass org/jetbrains/kotlin/resolve/checkers/OptInNames
instanceKlass org/jetbrains/kotlin/resolve/checkers/OptInUsageChecker$OptInFactoryBasedReporter
instanceKlass org/jetbrains/kotlin/resolve/checkers/OptInUsageChecker$OptInDiagnosticReporter
instanceKlass org/jetbrains/kotlin/resolve/checkers/OptInUsageChecker$Companion
instanceKlass org/jetbrains/kotlin/resolve/checkers/ExpectedActualDeclarationChecker$Companion
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/InlinePlatformCompatibilityChecker$Companion
instanceKlass org/jetbrains/kotlin/resolve/LanguageVersionSettingsProvider$Companion
instanceKlass org/jetbrains/kotlin/resolve/LanguageVersionSettingsProvider
instanceKlass org/jetbrains/kotlin/synthetic/SyntheticScopeProviderExtension
instanceKlass org/jetbrains/kotlin/resolve/scopes/SyntheticScope$Default
instanceKlass org/jetbrains/kotlin/resolve/scopes/SyntheticScope
instanceKlass org/jetbrains/kotlin/resolve/deprecation/DeprecationResolver$Companion
instanceKlass org/jetbrains/kotlin/container/IterableDescriptor
instanceKlass org/jetbrains/kotlin/builtins/ReflectionTypes$ClassLookup
instanceKlass org/jetbrains/kotlin/builtins/ReflectionTypes$Companion
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JavaNullabilityChecker$Companion
instanceKlass org/jetbrains/kotlin/container/ConstructorBinding
instanceKlass org/jetbrains/kotlin/container/ResolveKt
instanceKlass org/jetbrains/kotlin/container/SingletonDescriptor$WhenMappings
instanceKlass org/jetbrains/kotlin/load/kotlin/MemberSignature
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ProtoContainer
instanceKlass org/jetbrains/kotlin/load/kotlin/AbstractBinaryClassAnnotationLoader$AnnotationsContainer
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/JavaTypeEnhancement
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/NullabilityQualifierWithMigrationStatus
instanceKlass org/jetbrains/kotlin/load/java/JavaTypeQualifiersByElementType
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ClassData
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinJvmBinaryClass
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/SignatureEnhancement
instanceKlass org/jetbrains/kotlin/load/kotlin/DeserializedDescriptorResolver
instanceKlass org/jetbrains/kotlin/load/java/lazy/JavaResolverComponents
instanceKlass org/jetbrains/kotlin/resolve/inline/ReasonableInlineRule
instanceKlass org/jetbrains/kotlin/resolve/AnalyzerExtensions
instanceKlass org/jetbrains/kotlin/resolve/ControlFlowAnalyzer
instanceKlass org/jetbrains/kotlin/resolve/scopes/optimization/OptimizingOptions$Default
instanceKlass org/jetbrains/kotlin/resolve/scopes/optimization/OptimizingOptions
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopeFactory$DummyContainerDescriptor
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopeFactory$DefaultImportResolvers
instanceKlass org/jetbrains/kotlin/resolve/lazy/ImportResolutionComponents
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopeFactory
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopeProviderImpl
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopes
instanceKlass org/jetbrains/kotlin/resolve/lazy/ImportForceResolver
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiErrorElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiPlainTextFile
instanceKlass org/jetbrains/kotlin/com/intellij/psi/templateLanguages/OuterLanguageElement
instanceKlass org/jetbrains/kotlin/resolve/calls/context/CallPosition
instanceKlass org/jetbrains/kotlin/resolve/calls/context/TemporaryTraceAndCache
instanceKlass org/jetbrains/kotlin/builtins/PlatformSpecificCastChecker$Default
instanceKlass org/jetbrains/kotlin/resolve/checkers/PlatformDiagnosticSuppressor$Default
instanceKlass org/jetbrains/kotlin/resolve/DeclarationsChecker
instanceKlass org/jetbrains/kotlin/types/expressions/FakeCallResolver$RealExpression
instanceKlass org/jetbrains/kotlin/resolve/ModifiersChecker$ModifiersCheckingProcedure
instanceKlass org/jetbrains/kotlin/types/expressions/ClassLiteralChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/TypeVariableDependencyInformationProvider
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/VariableFixationFinder$VariableForFixation
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/model/VariableWithConstraints
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/ConstraintSystemCompletionContext
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/VariableFixationFinder$Context
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/ResultTypeResolver$Context
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/PostponedArgumentInputTypesResolver
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/VariableFixationFinder
instanceKlass org/jetbrains/kotlin/resolve/calls/components/PostponedArgumentsAnalyzer$SubstitutorAndStubsForLambdaAnalysis
instanceKlass org/jetbrains/kotlin/resolve/calls/components/ReturnArgumentsAnalysisResult
instanceKlass org/jetbrains/kotlin/resolve/calls/components/PostponedArgumentsAnalyzerContext
instanceKlass org/jetbrains/kotlin/resolve/calls/model/KotlinDiagnosticsHolder$SimpleHolder
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/CandidateFactory
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/model/ConstraintStorage
instanceKlass org/jetbrains/kotlin/resolve/calls/components/KotlinCallCompleter
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/NewAbstractResolvedCall
instanceKlass org/jetbrains/kotlin/resolve/calls/components/AdditionalDiagnosticReporter
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/PSIKotlinCallArgument
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/KotlinResolutionCallbacksImpl
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/ResultTypeResolver
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/KotlinConstraintSystemCompleter
instanceKlass org/jetbrains/kotlin/resolve/calls/components/PostponedArgumentsAnalyzer
instanceKlass org/jetbrains/kotlin/resolve/calls/KotlinCallResolver
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/KotlinToResolvedCallTransformer
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/NewTypeSubstitutor
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/ReceiverValueWithSmartCastInfo
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/ImplicitsExtensionsResolutionFilter$Default
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/TowerData
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/TowerResolver$ResultCollector
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/ScopeTowerProcessor
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/ImplicitsExtensionsResolutionFilter
instanceKlass org/jetbrains/kotlin/resolve/calls/results/ResolutionResultsHandler
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/TowerResolver
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ResolvedAtom
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ResolvedCallableReferenceAtom
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/ConstraintSystemBuilder
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/ConstraintSystemOperation
instanceKlass org/jetbrains/kotlin/resolve/calls/results/OverloadingConflictResolver
instanceKlass org/jetbrains/kotlin/types/TypeApproximatorConfiguration
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/model/Constraint
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/ConstraintIncorporator$Context
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/TrivialConstraintTypeInferenceOracle
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/ConstraintInjector$Context
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/ConstraintIncorporator
instanceKlass org/jetbrains/kotlin/resolve/calls/components/TypeArgumentsToParametersMapper$TypeArgumentsMapping
instanceKlass org/jetbrains/kotlin/resolve/calls/components/ArgumentsToParametersMapper$ArgumentMapping
instanceKlass org/jetbrains/kotlin/resolve/calls/components/CallableReferenceArgumentResolver
instanceKlass org/jetbrains/kotlin/resolve/calls/components/TypeArgumentsToParametersMapper
instanceKlass org/jetbrains/kotlin/resolve/calls/components/ArgumentsToParametersMapper
instanceKlass org/jetbrains/kotlin/resolve/calls/CandidateResolver$ValueArgumentsCheckingResult
instanceKlass org/jetbrains/kotlin/resolve/calls/model/MutableResolvedCall
instanceKlass org/jetbrains/kotlin/resolve/calls/CandidateResolver
instanceKlass org/jetbrains/kotlin/resolve/calls/results/OverloadResolutionResultsImpl
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/ConstraintSystem$Builder
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/BuilderInferenceSupport
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/PSICallResolver
instanceKlass org/jetbrains/kotlin/resolve/calls/CallCompleter
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/NewResolutionOldInference
instanceKlass org/jetbrains/kotlin/resolve/calls/model/MutableDataFlowInfoForArguments
instanceKlass org/jetbrains/kotlin/resolve/calls/model/DataFlowInfoForArguments
instanceKlass org/jetbrains/kotlin/resolve/calls/tasks/OldResolutionCandidate
instanceKlass org/jetbrains/kotlin/resolve/calls/GenericCandidateResolver
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/ExpressionReceiver
instanceKlass org/jetbrains/kotlin/resolve/DelegatingBindingTrace
instanceKlass org/jetbrains/kotlin/resolve/calls/tasks/TracingStrategy
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/NewResolutionOldInference$ResolutionKind
instanceKlass org/jetbrains/kotlin/types/expressions/DoubleColonExpressionResolver$ResolutionResultsAndTraceCommitCallback
instanceKlass org/jetbrains/kotlin/types/expressions/DoubleColonLHS
instanceKlass org/jetbrains/kotlin/types/FunctionPlaceholders
instanceKlass org/jetbrains/kotlin/resolve/calls/ArgumentTypeResolver
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Ref
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/SmartCastResult
instanceKlass org/jetbrains/kotlin/resolve/constants/CompileTimeConstant
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/SmartCastManager
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/ConditionalDataFlowInfo
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/RttiExpressionChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/model/KotlinCallComponents
instanceKlass org/jetbrains/kotlin/types/expressions/ForLoopConventionsChecker
instanceKlass org/jetbrains/kotlin/types/expressions/FakeCallResolver
instanceKlass org/jetbrains/kotlin/resolve/calls/CallExpressionResolver
instanceKlass org/jetbrains/kotlin/types/expressions/ControlStructureTypingUtils
instanceKlass org/jetbrains/kotlin/types/expressions/DoubleColonExpressionResolver
instanceKlass org/jetbrains/kotlin/builtins/PlatformSpecificCastChecker
instanceKlass org/jetbrains/kotlin/resolve/DeclarationsCheckerBuilder
instanceKlass org/jetbrains/kotlin/types/expressions/ValueParameterResolver
instanceKlass org/jetbrains/kotlin/contracts/parsing/ContractParsingServices
instanceKlass org/jetbrains/kotlin/resolve/MissingSupertypesResolver
instanceKlass org/jetbrains/kotlin/types/expressions/LocalClassifierAnalyzer
instanceKlass org/jetbrains/kotlin/resolve/CollectionLiteralResolver
instanceKlass org/jetbrains/kotlin/resolve/LocalVariableResolver
instanceKlass org/jetbrains/kotlin/types/expressions/DataFlowAnalyzer
instanceKlass org/jetbrains/kotlin/contracts/EffectSystem
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/KotlinResolutionCallbacksImpl$LambdaInfo
instanceKlass org/jetbrains/kotlin/types/expressions/KotlinTypeInfo
instanceKlass org/jetbrains/kotlin/resolve/StatementFilter
instanceKlass org/jetbrains/kotlin/resolve/AnnotationChecker
instanceKlass org/jetbrains/kotlin/types/expressions/ExpressionTypingComponents
instanceKlass org/jetbrains/kotlin/types/expressions/ExpressionTypingInternals
instanceKlass org/jetbrains/kotlin/types/expressions/ExpressionTypingFacade
instanceKlass org/jetbrains/kotlin/resolve/DelegatedPropertyResolver
instanceKlass org/jetbrains/kotlin/resolve/scopes/LexicalScopeStorage
instanceKlass org/jetbrains/kotlin/resolve/DeclarationSignatureAnonymousTypeTransformer
instanceKlass org/jetbrains/kotlin/types/AbstractTypeApproximator
instanceKlass org/jetbrains/kotlin/resolve/ModifiersChecker
instanceKlass org/jetbrains/kotlin/types/expressions/DestructuringDeclarationResolver
instanceKlass org/jetbrains/kotlin/resolve/OverloadChecker
instanceKlass org/jetbrains/kotlin/types/expressions/ExpressionTypingServices
instanceKlass org/jetbrains/kotlin/resolve/VariableTypeAndInitializerResolver
instanceKlass org/jetbrains/kotlin/resolve/scopes/LocalRedeclarationChecker
instanceKlass org/jetbrains/kotlin/types/SubtypingRepresentatives
instanceKlass org/jetbrains/kotlin/resolve/scopes/ImportingScope
instanceKlass org/jetbrains/kotlin/incremental/KotlinLookupLocation
instanceKlass org/jetbrains/kotlin/resolve/QualifiedExpressionResolver$QualifiedExpressionResolveResult
instanceKlass org/jetbrains/kotlin/resolve/QualifiedExpressionResolver$QualifierPart
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/Qualifier
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/QualifierReceiver
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/DetailedReceiver
instanceKlass org/jetbrains/kotlin/resolve/PossiblyBareType
instanceKlass org/jetbrains/kotlin/resolve/QualifiedExpressionResolver$TypeQualifierResolutionResult
instanceKlass org/jetbrains/kotlin/descriptors/TypeAliasDescriptor
instanceKlass org/jetbrains/kotlin/mpp/TypeAliasSymbolMarker
instanceKlass org/jetbrains/kotlin/resolve/TypeResolutionContext
instanceKlass org/jetbrains/kotlin/resolve/calls/tasks/DynamicCallableDescriptors
instanceKlass org/jetbrains/kotlin/resolve/ModuleStructureOracle$SingleModule
instanceKlass org/jetbrains/kotlin/resolve/sam/SamWithReceiverResolver
instanceKlass org/jetbrains/kotlin/psi/Call
instanceKlass org/jetbrains/kotlin/resolve/deprecation/DeprecationResolver$DeprecationInfo
instanceKlass org/jetbrains/kotlin/cfg/ControlFlowInformationProvider
instanceKlass org/jetbrains/kotlin/idea/MainFunctionDetector
instanceKlass org/jetbrains/kotlin/load/java/AbstractAnnotationTypeQualifierResolver
instanceKlass org/jetbrains/kotlin/resolve/jvm/modules/JavaModuleResolver$AccessError
instanceKlass org/jetbrains/kotlin/resolve/jvm/modules/JavaModule$Explicit
instanceKlass org/jetbrains/kotlin/resolve/jvm/modules/JavaModule
instanceKlass org/jetbrains/kotlin/load/java/descriptors/JavaClassDescriptor
instanceKlass org/jetbrains/kotlin/mpp/MppJavaImplicitActualizatorMarker
instanceKlass org/jetbrains/kotlin/builtins/jvm/JvmBuiltIns$Settings
instanceKlass org/jetbrains/kotlin/load/java/sources/JavaSourceElement
instanceKlass org/jetbrains/kotlin/descriptors/SimpleFunctionDescriptor
instanceKlass org/jetbrains/kotlin/mpp/SimpleFunctionSymbolMarker
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaPackage
instanceKlass org/jetbrains/kotlin/load/java/JavaClassFinder$Request
instanceKlass org/jetbrains/kotlin/load/java/lazy/LazyJavaResolverContext
instanceKlass org/jetbrains/kotlin/load/java/components/SignaturePropagator$PropagatedSignature
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaMethod
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaField
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaMember
instanceKlass org/jetbrains/kotlin/load/kotlin/AbstractBinaryClassAnnotationLoader
instanceKlass org/jetbrains/kotlin/load/kotlin/JavaClassDataFinder
instanceKlass org/jetbrains/kotlin/load/java/lazy/LazyJavaPackageFragmentProvider
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinClassFinder$Result
instanceKlass org/jetbrains/kotlin/resolve/lazy/data/KtClassLikeInfo
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/module/Module
instanceKlass org/jetbrains/kotlin/resolve/calls/results/OverloadResolutionResults
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ResolvedValueArgument
instanceKlass org/jetbrains/kotlin/resolve/scopes/LexicalScope
instanceKlass org/jetbrains/kotlin/resolve/scopes/HierarchicalScope
instanceKlass org/jetbrains/kotlin/resolve/constants/evaluate/ConstantExpressionEvaluator
instanceKlass org/jetbrains/kotlin/resolve/calls/CallResolver
instanceKlass org/jetbrains/kotlin/resolve/TopDownAnalysisContext
instanceKlass org/jetbrains/kotlin/resolve/BodiesResolveContext
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowInfo
instanceKlass org/jetbrains/kotlin/resolve/FilePreprocessor
instanceKlass org/jetbrains/kotlin/resolve/QualifiedExpressionResolver
instanceKlass org/jetbrains/kotlin/resolve/BodyResolver
instanceKlass org/jetbrains/kotlin/resolve/VarianceChecker
instanceKlass org/jetbrains/kotlin/resolve/OverloadResolver
instanceKlass org/jetbrains/kotlin/resolve/OverrideResolver
instanceKlass org/jetbrains/kotlin/resolve/DeclarationResolver
instanceKlass org/jetbrains/kotlin/container/SetterInfo
instanceKlass org/jetbrains/kotlin/javax/inject/Inject
instanceKlass org/jetbrains/kotlin/resolve/calls/components/InferenceSession
instanceKlass org/jetbrains/kotlin/descriptors/ClassDescriptorWithResolutionScopes
instanceKlass org/jetbrains/kotlin/resolve/lazy/DeclarationScopeProviderImpl
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopeProvider
instanceKlass org/jetbrains/kotlin/resolve/lazy/LazyDeclarationResolver
instanceKlass org/jetbrains/kotlin/resolve/FunctionDescriptorResolver
instanceKlass org/jetbrains/kotlin/resolve/DescriptorResolver
instanceKlass org/jetbrains/kotlin/resolve/TypeResolver
instanceKlass org/jetbrains/kotlin/resolve/lazy/LazyEntity
instanceKlass org/jetbrains/kotlin/resolve/lazy/DeclarationScopeProvider
instanceKlass org/jetbrains/kotlin/resolve/lazy/descriptors/LazyAnnotationsContext
instanceKlass org/jetbrains/kotlin/storage/LazyResolveStorageManager
instanceKlass org/jetbrains/kotlin/incremental/components/Position
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/model/OnlyInputTypeConstraintPosition
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/model/ConstraintPosition
instanceKlass org/jetbrains/kotlin/resolve/calls/model/PostponedAtomWithRevisableExpectedType
instanceKlass org/jetbrains/kotlin/resolve/calls/model/PostponedResolvedAtomMarker
instanceKlass org/jetbrains/kotlin/types/model/ObsoleteTypeKind
instanceKlass org/jetbrains/kotlin/resolve/checkers/EmptyIntersectionTypeInfo
instanceKlass org/jetbrains/kotlin/types/TypeCheckerState$SupertypesPolicy
instanceKlass org/jetbrains/kotlin/types/model/DynamicTypeMarker
instanceKlass org/jetbrains/kotlin/types/model/StubTypeMarker
instanceKlass org/jetbrains/kotlin/types/model/TypeVariableMarker
instanceKlass org/jetbrains/kotlin/builtins/functions/FunctionTypeKind
instanceKlass org/jetbrains/kotlin/types/model/TypeVariableTypeConstructorMarker
instanceKlass org/jetbrains/kotlin/types/model/CapturedTypeConstructorMarker
instanceKlass org/jetbrains/kotlin/types/model/CapturedTypeMarker
instanceKlass org/jetbrains/kotlin/types/model/FlexibleTypeMarker
instanceKlass org/jetbrains/kotlin/types/model/DefinitelyNotNullTypeMarker
instanceKlass org/jetbrains/kotlin/resolve/checkers/OptInUsageChecker$OptInReporterMultiplexer
instanceKlass org/jetbrains/kotlin/resolve/calls/components/KotlinResolutionCallbacks
instanceKlass org/jetbrains/kotlin/resolve/calls/components/candidate/ResolutionCandidate
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/Candidate
instanceKlass org/jetbrains/kotlin/resolve/calls/model/KotlinDiagnosticsHolder
instanceKlass org/jetbrains/kotlin/resolve/calls/model/SimpleKotlinCallArgument
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ReceiverKotlinCallArgument
instanceKlass org/jetbrains/kotlin/resolve/calls/model/KotlinCall
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/ImplicitScopeTower
instanceKlass org/jetbrains/kotlin/resolve/calls/model/CallableReferenceKotlinCallArgument
instanceKlass org/jetbrains/kotlin/resolve/calls/model/PostponableKotlinCallArgument
instanceKlass org/jetbrains/kotlin/resolve/calls/model/KotlinCallArgument
instanceKlass org/jetbrains/kotlin/resolve/calls/model/CallableReferenceResolutionAtom
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ResolutionAtom
instanceKlass org/jetbrains/kotlin/resolve/calls/results/SimpleConstraintSystem
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/ConstraintInjector
instanceKlass org/jetbrains/kotlin/types/TypeCheckerState
instanceKlass org/jetbrains/kotlin/resolve/ModuleStructureOracle
instanceKlass org/jetbrains/kotlin/descriptors/ValueParameterDescriptor
instanceKlass org/jetbrains/kotlin/mpp/ValueParameterSymbolMarker
instanceKlass org/jetbrains/kotlin/resolve/deprecation/DeprecationResolver
instanceKlass org/jetbrains/kotlin/builtins/ReflectionTypes
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowValue
instanceKlass org/jetbrains/kotlin/resolve/UpperBoundViolatedReporter
instanceKlass org/jetbrains/kotlin/types/TypeSubstitutor
instanceKlass org/jetbrains/kotlin/types/model/TypeSubstitutorMarker
instanceKlass org/jetbrains/kotlin/descriptors/TypeParameterDescriptor
instanceKlass org/jetbrains/kotlin/types/model/TypeParameterMarker
instanceKlass org/jetbrains/kotlin/mpp/TypeParameterSymbolMarker
instanceKlass java/lang/reflect/WildcardType
instanceKlass sun/reflect/generics/tree/MethodTypeSignature
instanceKlass sun/reflect/generics/tree/VoidDescriptor
instanceKlass sun/reflect/generics/tree/Wildcard
instanceKlass sun/reflect/generics/tree/BottomSignature
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticSink$DiagnosticsCallback
instanceKlass org/jetbrains/kotlin/com/intellij/util/xmlb/annotations/Transient
instanceKlass java/lang/Deprecated
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/ListenerDescriptor
instanceKlass kotlin/ReplaceWith
instanceKlass kotlin/Deprecated
instanceKlass org/jetbrains/kotlin/cfg/ControlFlowInformationProviderImpl$Factory
instanceKlass org/jetbrains/kotlin/cfg/ControlFlowInformationProvider$Factory
instanceKlass org/jetbrains/kotlin/idea/MainFunctionDetector$Factory$Ordinary
instanceKlass org/jetbrains/kotlin/idea/MainFunctionDetector$Factory
instanceKlass org/jetbrains/kotlin/resolve/lazy/BasicAbsentDescriptorHandler
instanceKlass org/jetbrains/kotlin/resolve/lazy/CompilerLocalDescriptorResolver
instanceKlass org/jetbrains/kotlin/resolve/lazy/LocalDescriptorResolver
instanceKlass org/jetbrains/kotlin/resolve/BodyResolveCache$ThrowException
instanceKlass org/jetbrains/kotlin/resolve/BodyResolveCache
instanceKlass org/jetbrains/kotlin/resolve/jvm/JvmDiagnosticComponents
instanceKlass org/jetbrains/kotlin/platform/PlatformSpecificDiagnosticComponents
instanceKlass org/jetbrains/kotlin/load/java/components/FilesByFacadeFqNameIndexer
instanceKlass org/jetbrains/kotlin/resolve/FilePreprocessorExtension
instanceKlass org/jetbrains/kotlin/resolve/jvm/modules/JavaModuleResolver$SERVICE
instanceKlass org/jetbrains/kotlin/load/java/lazy/JavaResolverSettings$Companion$create$1
instanceKlass org/jetbrains/kotlin/load/java/lazy/JavaResolverSettings$Companion
instanceKlass org/jetbrains/kotlin/load/java/lazy/JavaResolverSettings
instanceKlass org/jetbrains/kotlin/load/java/JavaClassesTracker$Default
instanceKlass org/jetbrains/kotlin/load/java/JavaClassesTracker
instanceKlass org/jetbrains/kotlin/resolve/jvm/multiplatform/OptionalAnnotationPackageFragmentProvider
instanceKlass org/jetbrains/kotlin/serialization/deserialization/AbstractDeserializedPackageFragmentProvider
instanceKlass org/jetbrains/kotlin/load/java/components/JavaSourceElementFactoryImpl
instanceKlass org/jetbrains/kotlin/load/java/sources/JavaSourceElementFactory
instanceKlass org/jetbrains/kotlin/load/java/components/AbstractJavaResolverCache
instanceKlass org/jetbrains/kotlin/load/java/components/JavaResolverCache
instanceKlass org/jetbrains/kotlin/load/java/AbstractJavaClassFinder
instanceKlass org/jetbrains/kotlin/load/java/JavaClassFinder
instanceKlass org/jetbrains/kotlin/resolve/jvm/CompositeSyntheticJavaPartsProvider
instanceKlass org/jetbrains/kotlin/resolve/jvm/SyntheticJavaPartsProvider$Companion
instanceKlass org/jetbrains/kotlin/resolve/jvm/SyntheticJavaPartsProvider
instanceKlass org/jetbrains/kotlin/load/java/components/JavaDeprecationSettings
instanceKlass org/jetbrains/kotlin/resolve/deprecation/DeprecationSettings
instanceKlass org/jetbrains/kotlin/resolve/TypeResolver$TypeTransformerForTests
instanceKlass org/jetbrains/kotlin/load/java/components/TraceBasedErrorReporter
instanceKlass org/jetbrains/kotlin/load/java/components/SignaturePropagatorImpl
instanceKlass org/jetbrains/kotlin/load/java/components/SignaturePropagator
instanceKlass org/jetbrains/kotlin/load/java/components/JavaPropertyInitializerEvaluatorImpl
instanceKlass org/jetbrains/kotlin/load/java/components/JavaPropertyInitializerEvaluator
instanceKlass org/jetbrains/kotlin/load/kotlin/DeserializationComponentsForJava
instanceKlass org/jetbrains/kotlin/resolve/jvm/JavaDescriptorResolver
instanceKlass org/jetbrains/kotlin/load/kotlin/VirtualFileFinder$SERVICE
instanceKlass org/jetbrains/kotlin/load/kotlin/VirtualFileFinder
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinClassFinder
instanceKlass org/jetbrains/kotlin/serialization/deserialization/KotlinMetadataFinder
instanceKlass org/jetbrains/kotlin/load/kotlin/VirtualFileFinderFactory$SERVICE
instanceKlass org/jetbrains/kotlin/resolve/AnnotationResolver
instanceKlass org/jetbrains/kotlin/resolve/LazyTopDownAnalyzer
instanceKlass org/jetbrains/kotlin/resolve/lazy/ResolveSession
instanceKlass org/jetbrains/kotlin/resolve/lazy/LazyClassContext
instanceKlass org/jetbrains/kotlin/resolve/lazy/KotlinCodeAnalyzer
instanceKlass org/jetbrains/kotlin/resolve/lazy/TopLevelDescriptorProvider
instanceKlass org/jetbrains/kotlin/util/ProgressManagerBasedCancellationChecker
instanceKlass org/jetbrains/kotlin/util/CancellationChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/ClassicConstraintSystemUtilContext
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/ConstraintSystemUtilContext
instanceKlass org/jetbrains/kotlin/resolve/calls/components/ClassicTypeSystemContextForCS
instanceKlass org/jetbrains/kotlin/types/model/TypeSystemInferenceExtensionContextDelegate
instanceKlass org/jetbrains/kotlin/types/checker/ClassicTypeSystemContext
instanceKlass org/jetbrains/kotlin/types/model/TypeSystemInferenceExtensionContext
instanceKlass org/jetbrains/kotlin/types/model/TypeSystemCommonSuperTypesContext
instanceKlass org/jetbrains/kotlin/types/model/TypeSystemTypeFactoryContext
instanceKlass org/jetbrains/kotlin/types/model/TypeCheckerProviderContext
instanceKlass org/jetbrains/kotlin/types/model/TypeSystemBuiltInsContext
instanceKlass org/jetbrains/kotlin/types/TypeSystemCommonBackendContext
instanceKlass org/jetbrains/kotlin/types/model/TypeSystemContext
instanceKlass org/jetbrains/kotlin/types/model/TypeSystemOptimizationContext
instanceKlass org/jetbrains/kotlin/resolve/calls/components/BuiltInsProvider
instanceKlass org/jetbrains/kotlin/contracts/ContractDeserializerImpl
instanceKlass org/jetbrains/kotlin/resolve/checkers/OptInUsageChecker$ClassifierUsage
instanceKlass org/jetbrains/kotlin/resolve/checkers/OptInUsageChecker$Overrides
instanceKlass org/jetbrains/kotlin/resolve/checkers/OptInUsageChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowValueFactoryImpl
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowValueFactory
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/KotlinResolutionStatelessCallbacksImpl
instanceKlass org/jetbrains/kotlin/resolve/calls/components/KotlinResolutionStatelessCallbacks
instanceKlass org/jetbrains/kotlin/resolve/SupertypeLoopCheckerImpl
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/RepeatableAnnotationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ExpectedActualDeclarationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/OptInMarkerDeclarationAnnotationChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmSerializableLambdaAnnotationChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/multiplatform/JavaActualAnnotationArgumentExtractor
instanceKlass org/jetbrains/kotlin/resolve/checkers/ExpectedActualDeclarationChecker$ActualAnnotationArgumentExtractor
instanceKlass org/jetbrains/kotlin/types/expressions/GenericArrayClassLiteralSupport$Enabled
instanceKlass org/jetbrains/kotlin/types/expressions/GenericArrayClassLiteralSupport
instanceKlass org/jetbrains/kotlin/types/expressions/FunctionWithBigAritySupport$LanguageVersionDependent
instanceKlass org/jetbrains/kotlin/types/expressions/FunctionWithBigAritySupport
instanceKlass org/jetbrains/kotlin/resolve/jvm/JvmPlatformAnnotationFeaturesSupport
instanceKlass org/jetbrains/kotlin/resolve/PlatformAnnotationFeaturesSupport
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmRecordApplicabilityChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/JvmAdditionalClassPartsProvider
instanceKlass org/jetbrains/kotlin/resolve/AdditionalClassPartsProvider
instanceKlass org/jetbrains/kotlin/load/java/sam/JvmSamConversionOracle
instanceKlass org/jetbrains/kotlin/resolve/sam/SamConversionOracle
instanceKlass org/jetbrains/kotlin/resolve/jvm/JvmPlatformOverloadsSpecificityComparator
instanceKlass org/jetbrains/kotlin/resolve/calls/results/PlatformOverloadsSpecificityComparator
instanceKlass org/jetbrains/kotlin/resolve/jvm/JvmTypeSpecificityComparator
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmModuleAccessibilityChecker$ClassifierUsage
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmModuleAccessibilityChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/InlinePlatformCompatibilityChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmDefaultChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/InterfaceDefaultMethodCallChecker
instanceKlass org/jetbrains/kotlin/synthetic/JavaSyntheticScopes
instanceKlass org/jetbrains/kotlin/resolve/scopes/SyntheticScopes
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/AbstractReflectionApiCallChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmStaticChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JavaNullabilityChecker
instanceKlass org/jetbrains/kotlin/container/SingletonDescriptor
instanceKlass org/jetbrains/kotlin/resolve/UpperBoundChecker
instanceKlass org/jetbrains/kotlin/frontend/di/InjectionKt
instanceKlass org/jetbrains/kotlin/types/WrappedTypeFactory
instanceKlass sun/reflect/generics/reflectiveObjects/LazyReflectiveObjectGenerator
instanceKlass java/lang/reflect/TypeVariable
instanceKlass sun/reflect/generics/tree/TypeVariableSignature
instanceKlass sun/reflect/generics/reflectiveObjects/ParameterizedTypeImpl
instanceKlass java/lang/reflect/ParameterizedType
instanceKlass org/jetbrains/kotlin/resolve/checkers/ClassifierUsageCheckerContext
instanceKlass org/jetbrains/kotlin/resolve/calls/context/ResolutionContext
instanceKlass java/lang/annotation/Documented
instanceKlass kotlin/annotation/MustBeDocumented
instanceKlass kotlin/jvm/JvmStatic
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/CallCheckerContext
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ResolvedCall
instanceKlass org/jetbrains/kotlin/descriptors/annotations/AnnotationDescriptor
instanceKlass org/jetbrains/kotlin/types/model/AnnotationMarker
instanceKlass org/jetbrains/kotlin/container/ConstructorInfo
instanceKlass kotlin/annotation/Retention
instanceKlass org/jetbrains/kotlin/container/DefaultImplementation
instanceKlass sun/reflect/generics/tree/ClassSignature
instanceKlass sun/reflect/generics/tree/Signature
instanceKlass sun/reflect/generics/tree/FormalTypeParameter
instanceKlass sun/reflect/generics/repository/AbstractRepository
instanceKlass jdk/internal/vm/annotation/IntrinsicCandidate
instanceKlass org/jetbrains/kotlin/resolve/checkers/DeclarationCheckerContext
instanceKlass org/jetbrains/kotlin/resolve/checkers/CheckerContext
instanceKlass org/jetbrains/kotlin/container/ClassInfo
instanceKlass org/jetbrains/kotlin/container/ClassTraversalCache
instanceKlass org/jetbrains/kotlin/container/CacheKt
instanceKlass org/jetbrains/kotlin/container/DynamicComponentDescriptor
instanceKlass org/jetbrains/kotlin/container/ComponentResolveContext
instanceKlass org/jetbrains/kotlin/container/ValueResolveContext
instanceKlass org/jetbrains/kotlin/container/InstanceComponentDescriptor
instanceKlass org/jetbrains/kotlin/container/ComponentDescriptor
instanceKlass org/jetbrains/kotlin/container/ValueDescriptor
instanceKlass org/jetbrains/kotlin/container/ContainerKt
instanceKlass org/jetbrains/kotlin/resolve/checkers/PlatformDiagnosticSuppressor
instanceKlass org/jetbrains/kotlin/resolve/lazy/AbsentDescriptorHandler
instanceKlass org/jetbrains/kotlin/types/DynamicTypesSettings
instanceKlass org/jetbrains/kotlin/resolve/calls/results/TypeSpecificityComparator$NONE
instanceKlass org/jetbrains/kotlin/resolve/calls/results/TypeSpecificityComparator
instanceKlass org/jetbrains/kotlin/resolve/checkers/OptionalExpectationUsageChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/MissingDependencyClassChecker$ClassifierUsage
instanceKlass org/jetbrains/kotlin/resolve/checkers/ApiVersionClassifierUsageChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/DeprecatedClassifierUsageChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/EnumEntriesUnsupportedChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/CustomEnumEntriesMigrationCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/CompanionIncorrectlyUnboundedWhenUsedAsLHSCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/IncorrectCapturedApproximationCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/BuilderInferenceAssignmentChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/UnsupportedUntilOperatorChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/EqualityCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/ResolutionToPrivateConstructorOfSealedClassChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/CompanionInParenthesesLHSCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/EnumEntryVsCompanionPriorityCallChecker
instanceKlass org/jetbrains/kotlin/utils/StringsKt
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/NewSchemeOfIntegerOperatorResolutionChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/SelfCallInNestedObjectConstructorChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/VarargWrongExecutionOrderChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/ReferencingToUnderscoreNamedParameterOfCatchBlockChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/NullableExtensionOperatorWithSafeCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/FunInterfaceConstructorReferenceChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/UnitConversionCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/SuspendConversionCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/AbstractClassInstantiationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/MissingDependencySupertypeChecker$ForCalls
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/ReifiedTypeParameterSubstitutionChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/ContractNotAllowedCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/NamedFunAsExpressionChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/NullableVarargArgumentCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/ResultTypeWithNullableOperatorsChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/UselessElvisCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/LambdaWithSuspendModifierCallChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/PrimitiveNumericComparisonCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/ImplicitNothingAsTypeParameterCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/AssigningNamedArgumentToVarargChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/UnderscoreUsageChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/CallableReferenceCompatibilityChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/MissingDependencyClassChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/DslScopeViolationCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/BuilderFunctionsCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/CoroutineSuspendCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/ApiVersionCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/ProtectedConstructorCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/ConstructorHeaderCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/OperatorCallChecker$Companion
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/OperatorCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/InfixCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/CallReturnsArrayOfNothingChecker
instanceKlass kotlin/jvm/internal/SpreadBuilder
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/DeprecatedCallChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/TrailingCommaCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/SafeCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/SynchronizedByValueChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/InlineCheckerWrapper
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/CapturingInClosureChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ActualTypealiasToSpecialAnnotationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/VolatileAnnotationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/EnumEntriesRedeclarationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/DataObjectContentChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/UnsupportedUntilRangeDeclarationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/CyclicAnnotationsChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ValueParameterUsageInDefaultArgumentChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/SubtypingBetweenContextReceiversChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ContextualDeclarationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/EnumCompanionInEnumConstructorCallChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/SuspendFunctionAsSupertypeChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/SealedInterfaceAllowedChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/SealedInheritorInSameModuleChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/SealedInheritorInSamePackageChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/PrivateInlineFunctionsReturningAnonymousObjectsChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ContractDescriptionBlockChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/DeprecatedSinceKotlinAnnotationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/DeprecationInheritanceChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/FunInterfaceDeclarationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/MissingDependencySupertypeChecker$ForDeclarations
instanceKlass org/jetbrains/kotlin/resolve/checkers/TrailingCommaDeclarationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/TailrecFunctionChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ExplicitApiDeclarationChecker$Companion
instanceKlass org/jetbrains/kotlin/resolve/checkers/ExplicitApiDeclarationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/LocalVariableTypeParametersChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ResultClassInReturnTypeChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ReservedMembersAndConstructsForValueClass$Companion
instanceKlass org/jetbrains/kotlin/resolve/checkers/ReservedMembersAndConstructsForValueClass
instanceKlass org/jetbrains/kotlin/resolve/checkers/AnnotationClassTargetAndRetentionChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/InnerClassInsideValueClass
instanceKlass org/jetbrains/kotlin/resolve/checkers/PropertiesWithBackingFieldsInsideValueClass
instanceKlass org/jetbrains/kotlin/resolve/checkers/MultiFieldValueClassAnnotationsChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ValueClassDeclarationChecker
instanceKlass org/jetbrains/kotlin/util/OperatorNameConventions
instanceKlass org/jetbrains/kotlin/resolve/checkers/SuspendLimitationsChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/KClassWithIncorrectTypeArgumentChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/DelegationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/DynamicReceiverChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ReifiedTypeParameterAnnotationChecker
instanceKlass org/jetbrains/kotlin/resolve/RequireKotlinConstants
instanceKlass org/jetbrains/kotlin/resolve/AnnotationsKt
instanceKlass org/jetbrains/kotlin/resolve/checkers/KotlinVersionStringAnnotationValueChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/InfixModifierChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/InlineParameterChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/UnderscoreChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ConstModifierChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/DataClassDeclarationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ExpectActualClassifiersAreInBetaChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ActualClassifierMustHasTheSameMembersAsNonFinalExpectClassifierChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ExpectActualInTheSameModuleChecker
instanceKlass org/jetbrains/kotlin/resolve/PlatformConfiguratorBaseKt
instanceKlass org/jetbrains/kotlin/container/ComponentRegistry
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/MultiMap
instanceKlass org/jetbrains/kotlin/container/ComponentStorage
instanceKlass org/jetbrains/kotlin/container/ValueResolver
instanceKlass org/jetbrains/kotlin/container/StorageComponentContainer
instanceKlass org/jetbrains/kotlin/container/ComponentProvider
instanceKlass org/jetbrains/kotlin/container/ComponentContainer
instanceKlass org/jetbrains/kotlin/container/DslKt
instanceKlass org/jetbrains/kotlin/resolve/jvm/JvmDeclarationReturnTypeSanitizer
instanceKlass org/jetbrains/kotlin/resolve/DeclarationReturnTypeSanitizer
instanceKlass org/jetbrains/kotlin/resolve/jvm/JvmOverridesBackwardCompatibilityHelper
instanceKlass org/jetbrains/kotlin/resolve/OverridesBackwardCompatibilityHelper
instanceKlass org/jetbrains/kotlin/resolve/jvm/JvmDelegationFilter
instanceKlass org/jetbrains/kotlin/resolve/lazy/DelegationFilter
instanceKlass org/jetbrains/kotlin/resolve/jvm/JvmOverloadFilter
instanceKlass org/jetbrains/kotlin/resolve/OverloadFilter
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmSimpleNameBacktickChecker
instanceKlass org/jetbrains/kotlin/resolve/IdentifierChecker
instanceKlass org/jetbrains/kotlin/resolve/sam/SamConversionResolver$Empty
instanceKlass org/jetbrains/kotlin/container/PlatformExtensionsClashResolver
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/SynchronizedAnnotationOnLambdaChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/ExplicitMetadataChecker
instanceKlass org/jetbrains/kotlin/name/JvmNames
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/FileClassAnnotationsChecker
instanceKlass org/jetbrains/kotlin/resolve/AdditionalAnnotationChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/MissingBuiltInDeclarationChecker$ClassifierUsage
instanceKlass org/jetbrains/kotlin/resolve/checkers/BigFunctionTypeAvailabilityChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/ClassifierUsageChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmArrayVariableInLoopAssignmentChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JavaTypeAccessibilityChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/platform/JavaGenericVarianceViolationTypeChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/RuntimeAssertionsTypeChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/AdditionalTypeChecker
instanceKlass org/jetbrains/kotlin/diagnostics/rendering/DiagnosticFactoryToRendererMap
instanceKlass org/jetbrains/kotlin/diagnostics/rendering/DefaultErrorMessages$Extension
instanceKlass org/jetbrains/kotlin/diagnostics/Errors$Initializer
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Hashing
instanceKlass org/jetbrains/kotlin/com/google/common/math/IntMath$1
instanceKlass org/jetbrains/kotlin/com/google/common/math/MathPreconditions
instanceKlass org/jetbrains/kotlin/com/google/common/math/IntMath
instanceKlass org/jetbrains/kotlin/com/google/common/collect/ImmutableSet$SetBuilderImpl
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticFactoryForDeprecation4$Companion
instanceKlass org/jetbrains/kotlin/diagnostics/ClassicPositioningStrategies
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticFactoryForDeprecation2$Companion
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticFactoryForDeprecation0$Companion
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticFactoryForDeprecation1$Companion
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticFactoryForDeprecation3$Companion
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticFactoryForDeprecation
instanceKlass org/jetbrains/kotlin/diagnostics/ParametrizedDiagnostic
instanceKlass org/jetbrains/kotlin/diagnostics/Diagnostic
instanceKlass org/jetbrains/kotlin/diagnostics/Errors
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmSyntheticAssignmentChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/AssignmentChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmPropertyVsFieldAmbiguityCallChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/LateinitIntrinsicApplicabilityChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/UpperBoundViolatedInTypealiasConstructorChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/EnumDeclaringClassDeprecationChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/SamInterfaceConstructorReferenceCallChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/PolymorphicSignatureCallChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/InconsistentOperatorFromJavaCallChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/ApiVersionIsAtLeastArgumentsChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/RuntimeAssertionsOnExtensionReceiverCallChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/ProtectedSyntheticExtensionCallChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/SuperCallWithDefaultArgumentsChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/UnsupportedSyntheticCallableReferenceChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/ProtectedInSuperClassCompanionCallChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JavaClassOnCompanionChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/SuspensionPointInsideMutexLockChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JavaAnnotationCallChecker$Companion
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JavaAnnotationCallChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/MissingBuiltInDeclarationChecker
instanceKlass org/jetbrains/kotlin/resolve/calls/checkers/CallChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JavaOverrideWithWrongNullabilityOverrideChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/ClassInheritsJavaSealedClassChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/FunctionDelegateMemberNameClashChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/DefaultCheckerInTailrec
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmMultifileClassStateChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/BadInheritedJavaSignaturesChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/SuspendInFunInterfaceChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmAnnotationsTargetNonExistentAccessorChecker$Companion
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmAnnotationsTargetNonExistentAccessorChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/StrictfpApplicabilityChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmInlineApplicabilityChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmSyntheticApplicabilityChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/TypeParameterBoundIsNotArrayChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmFieldApplicabilityChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/OverloadsAnnotationChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/ExternalFunChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/LocalFunInlineChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/SynchronizedAnnotationChecker
instanceKlass org/jetbrains/kotlin/resolve/jvm/checkers/JvmNameAnnotationChecker
instanceKlass org/jetbrains/kotlin/resolve/checkers/DeclarationChecker
instanceKlass org/jetbrains/kotlin/resolve/PlatformConfiguratorBase
instanceKlass org/jetbrains/kotlin/resolve/PlatformConfigurator
instanceKlass org/jetbrains/kotlin/resolve/ImportPath$Companion
instanceKlass org/jetbrains/kotlin/resolve/ImportPath
instanceKlass org/jetbrains/kotlin/resolve/PlatformDependentAnalyzerServices
instanceKlass org/jetbrains/kotlin/resolve/SealedClassInheritorsProvider
instanceKlass org/jetbrains/kotlin/resolve/ModuleAnnotationsResolver$Companion
instanceKlass org/jetbrains/kotlin/load/kotlin/JvmPackagePartProviderBase$ModuleMappingInfo
instanceKlass org/jetbrains/kotlin/metadata/jvm/deserialization/ModuleMappingKt
instanceKlass org/jetbrains/kotlin/protobuf/UnmodifiableLazyStringList$2
instanceKlass org/jetbrains/kotlin/metadata/jvm/deserialization/PackageParts
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmModuleProtoBuf$PackagePartsOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmModuleProtoBuf$ModuleOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/deserialization/VersionSpecificBehaviorKt
instanceKlass kotlin/collections/MapWithDefault
instanceKlass org/jetbrains/kotlin/utils/MetadataHelpersKt
instanceKlass org/jetbrains/kotlin/utils/DeserializationHelpersKt
instanceKlass org/jetbrains/kotlin/load/kotlin/ModuleMappingUtilKt
instanceKlass org/jetbrains/kotlin/metadata/jvm/deserialization/BinaryModuleData
instanceKlass org/jetbrains/kotlin/metadata/jvm/deserialization/JvmMetadataVersion$Companion
instanceKlass org/jetbrains/kotlin/metadata/jvm/deserialization/ModuleMapping$Companion
instanceKlass org/jetbrains/kotlin/metadata/jvm/deserialization/ModuleMapping
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/JvmPackagePartProviderKt
instanceKlass org/jetbrains/kotlin/resolve/CompilerDeserializationConfiguration
instanceKlass org/jetbrains/kotlin/load/kotlin/JvmPackagePartProviderBase$Companion
instanceKlass org/jetbrains/kotlin/load/kotlin/JvmPackagePartProviderBase
instanceKlass org/jetbrains/kotlin/load/kotlin/PackageAndMetadataPartProvider
instanceKlass org/jetbrains/kotlin/serialization/deserialization/MetadataPartProvider
instanceKlass org/jetbrains/kotlin/load/kotlin/PackagePartProvider
instanceKlass org/jetbrains/kotlin/resolve/lazy/declarations/FileBasedDeclarationProviderFactory$Index
instanceKlass org/jetbrains/kotlin/resolve/lazy/declarations/ClassMemberDeclarationProvider
instanceKlass org/jetbrains/kotlin/resolve/lazy/declarations/PackageMemberDeclarationProvider
instanceKlass org/jetbrains/kotlin/resolve/lazy/declarations/DeclarationProvider
instanceKlass org/jetbrains/kotlin/resolve/lazy/declarations/AbstractDeclarationProviderFactory
instanceKlass org/jetbrains/kotlin/resolve/lazy/declarations/DeclarationProviderFactory
instanceKlass org/jetbrains/kotlin/config/LanguageFeature$Companion
instanceKlass org/jetbrains/kotlin/frontend/java/di/InjectionKt
instanceKlass org/jetbrains/kotlin/descriptors/impl/ModuleDependenciesImpl
instanceKlass org/jetbrains/kotlin/descriptors/impl/ModuleDependencies
instanceKlass org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator
instanceKlass org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter
instanceKlass org/jetbrains/kotlin/serialization/deserialization/descriptors/DeserializedMemberScope$OptimizedImplementation
instanceKlass org/jetbrains/kotlin/serialization/deserialization/descriptors/DeserializedMemberScope$Implementation
instanceKlass org/jetbrains/kotlin/serialization/deserialization/MemberDeserializer
instanceKlass org/jetbrains/kotlin/serialization/deserialization/TypeDeserializer
instanceKlass org/jetbrains/kotlin/serialization/deserialization/DeserializationContext
instanceKlass org/jetbrains/kotlin/metadata/deserialization/VersionRequirementTable$Companion
instanceKlass org/jetbrains/kotlin/metadata/deserialization/VersionRequirementTable
instanceKlass org/jetbrains/kotlin/metadata/deserialization/TypeTable
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ClassDeserializer$Companion
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ClassDeserializer
instanceKlass org/jetbrains/kotlin/serialization/deserialization/EnumEntriesDeserializationSupport$Default
instanceKlass org/jetbrains/kotlin/serialization/deserialization/EnumEntriesDeserializationSupport
instanceKlass org/jetbrains/kotlin/resolve/OverridingUtil$1
instanceKlass org/jetbrains/kotlin/load/java/JavaIncompatibilityRulesOverridabilityCondition$Companion
instanceKlass org/jetbrains/kotlin/load/java/JavaIncompatibilityRulesOverridabilityCondition
instanceKlass org/jetbrains/kotlin/load/java/ErasedOverridabilityCondition
instanceKlass org/jetbrains/kotlin/load/java/FieldOverridabilityCondition
instanceKlass org/jetbrains/kotlin/resolve/ExternalOverridabilityCondition
instanceKlass org/jetbrains/kotlin/types/checker/KotlinTypeChecker$TypeConstructorEquality
instanceKlass org/jetbrains/kotlin/resolve/OverridingUtil
instanceKlass org/jetbrains/kotlin/types/AbstractTypePreparator
instanceKlass org/jetbrains/kotlin/types/checker/NewKotlinTypeCheckerImpl
instanceKlass org/jetbrains/kotlin/types/checker/NewKotlinTypeChecker$Companion
instanceKlass org/jetbrains/kotlin/types/checker/NewKotlinTypeChecker
instanceKlass org/jetbrains/kotlin/types/checker/KotlinTypeChecker
instanceKlass org/jetbrains/kotlin/resolve/sam/SamConversionResolverImpl
instanceKlass org/jetbrains/kotlin/resolve/sam/SamConversionResolver
instanceKlass org/jetbrains/kotlin/container/PlatformSpecificExtension
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ContractDeserializer$Companion$DEFAULT$1
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ContractDeserializer$Companion
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ContractDeserializer
instanceKlass org/jetbrains/kotlin/serialization/deserialization/FlexibleTypeDeserializer$ThrowException
instanceKlass org/jetbrains/kotlin/serialization/deserialization/FlexibleTypeDeserializer
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ErrorReporter$1
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ErrorReporter
instanceKlass org/jetbrains/kotlin/serialization/deserialization/LocalClassifierTypeSettings$Default
instanceKlass org/jetbrains/kotlin/serialization/deserialization/LocalClassifierTypeSettings
instanceKlass org/jetbrains/kotlin/resolve/constants/ConstantValue
instanceKlass org/jetbrains/kotlin/serialization/deserialization/AnnotationDeserializer
instanceKlass org/jetbrains/kotlin/serialization/deserialization/AnnotationAndConstantLoaderImpl
instanceKlass org/jetbrains/kotlin/serialization/deserialization/AnnotationAndConstantLoader
instanceKlass org/jetbrains/kotlin/serialization/deserialization/AnnotationLoader
instanceKlass org/jetbrains/kotlin/serialization/deserialization/DeserializedClassDataFinder
instanceKlass org/jetbrains/kotlin/serialization/deserialization/DeserializationConfiguration$Default
instanceKlass org/jetbrains/kotlin/serialization/deserialization/DeserializationConfiguration
instanceKlass org/jetbrains/kotlin/serialization/deserialization/DeserializationComponents
instanceKlass org/jetbrains/kotlin/descriptors/NotFoundClasses
instanceKlass org/jetbrains/kotlin/descriptors/PackageFragmentProviderImpl
instanceKlass org/jetbrains/kotlin/descriptors/PackageFragmentProviderOptimized
instanceKlass org/jetbrains/kotlin/descriptors/PackageFragmentProvider
instanceKlass jdk/internal/misc/ScopedMemoryAccess$Scope
instanceKlass java/util/LinkedList$ListItr
instanceKlass kotlin/Triple
instanceKlass java/util/LinkedList$Node
instanceKlass org/jetbrains/kotlin/metadata/deserialization/NameResolverImpl$WhenMappings
instanceKlass org/jetbrains/kotlin/protobuf/Utf8
instanceKlass org/jetbrains/kotlin/serialization/deserialization/NameResolverUtilKt
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ProtoBasedClassDataFinder
instanceKlass org/jetbrains/kotlin/serialization/deserialization/ClassDataFinder
instanceKlass org/jetbrains/kotlin/metadata/deserialization/NameResolverImpl
instanceKlass org/jetbrains/kotlin/metadata/deserialization/NameResolver
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection$1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Type$ArgumentOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Annotation$ArgumentOrBuilder
instanceKlass org/jetbrains/kotlin/protobuf/SmallSortedMap$Entry
instanceKlass org/jetbrains/kotlin/protobuf/FieldSet$1
instanceKlass org/jetbrains/kotlin/protobuf/AbstractMessageLite$Builder
instanceKlass org/jetbrains/kotlin/protobuf/GeneratedMessageLite$1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$QualifiedNameTable$QualifiedName$Kind$1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$QualifiedNameTable$QualifiedNameOrBuilder
instanceKlass org/jetbrains/kotlin/protobuf/WireFormat
instanceKlass org/jetbrains/kotlin/protobuf/CodedOutputStream
instanceKlass org/jetbrains/kotlin/protobuf/CodedInputStream
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$QualifiedNameTableOrBuilder
instanceKlass org/jetbrains/kotlin/protobuf/LazyStringList
instanceKlass org/jetbrains/kotlin/protobuf/ProtocolStringList
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$StringTableOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$PackageFragmentOrBuilder
instanceKlass kotlin/collections/IntIterator
instanceKlass org/jetbrains/kotlin/metadata/builtins/BuiltInsBinaryVersion$Companion
instanceKlass org/jetbrains/kotlin/metadata/deserialization/BinaryVersion$Companion
instanceKlass org/jetbrains/kotlin/metadata/deserialization/BinaryVersion
instanceKlass org/jetbrains/kotlin/metadata/builtins/ReadPackageFragmentKt
instanceKlass org/jetbrains/kotlin/serialization/deserialization/builtins/BuiltInsPackageFragmentImpl$Companion
instanceKlass org/jetbrains/kotlin/builtins/BuiltInsPackageFragment
instanceKlass org/jetbrains/kotlin/protobuf/ExtensionRegistryLite$ObjectIntPair
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$TypeParameter$Variance$1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$TypeParameterOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$EnumEntryOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Annotation$Argument$Value$Type$1
instanceKlass org/jetbrains/kotlin/protobuf/Internal$EnumLiteMap
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Annotation$Argument$ValueOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$ValueParameterOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$PropertyOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$ContractOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$FunctionOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$ConstructorOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$AnnotationOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$TypeOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$ClassOrBuilder
instanceKlass org/jetbrains/kotlin/protobuf/Internal$EnumLite
instanceKlass org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtensionDescriptor
instanceKlass org/jetbrains/kotlin/protobuf/GeneratedMessageLite$GeneratedExtension
instanceKlass java/lang/Long$LongCache
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$VersionRequirementTableOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$TypeTableOrBuilder
instanceKlass org/jetbrains/kotlin/protobuf/ByteString$ByteIterator
instanceKlass org/jetbrains/kotlin/protobuf/ByteString
instanceKlass org/jetbrains/kotlin/protobuf/SmallSortedMap$EmptySet$2
instanceKlass org/jetbrains/kotlin/protobuf/SmallSortedMap$EmptySet$1
instanceKlass org/jetbrains/kotlin/protobuf/SmallSortedMap$EmptySet
instanceKlass org/jetbrains/kotlin/protobuf/LazyFieldLite
instanceKlass org/jetbrains/kotlin/protobuf/FieldSet
instanceKlass org/jetbrains/kotlin/protobuf/AbstractParser
instanceKlass org/jetbrains/kotlin/protobuf/MessageLite$Builder
instanceKlass org/jetbrains/kotlin/protobuf/Parser
instanceKlass org/jetbrains/kotlin/protobuf/FieldSet$FieldDescriptorLite
instanceKlass org/jetbrains/kotlin/protobuf/AbstractMessageLite
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$PackageOrBuilder
instanceKlass org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessageOrBuilder
instanceKlass org/jetbrains/kotlin/protobuf/MessageLite
instanceKlass org/jetbrains/kotlin/protobuf/MessageLiteOrBuilder
instanceKlass org/jetbrains/kotlin/metadata/builtins/BuiltInsProtoBuf
instanceKlass org/jetbrains/kotlin/protobuf/ExtensionRegistryLite
instanceKlass org/jetbrains/kotlin/serialization/SerializerExtensionProtocol
instanceKlass org/jetbrains/kotlin/storage/LockBasedStorageManager$CacheWithNullableValuesBasedOnMemoizedFunction$1
instanceKlass org/jetbrains/kotlin/types/KotlinTypeFactory
instanceKlass org/jetbrains/kotlin/util/ArrayMap
instanceKlass org/jetbrains/kotlin/util/TypeRegistry
instanceKlass org/jetbrains/kotlin/util/AbstractArrayMapOwner
instanceKlass org/jetbrains/kotlin/descriptors/FieldDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/PropertySetterDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/PropertyGetterDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/PropertyAccessorDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/VariableAccessorDescriptor
instanceKlass org/jetbrains/kotlin/types/error/ErrorPropertyDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/PropertyDescriptor
instanceKlass org/jetbrains/kotlin/mpp/PropertySymbolMarker
instanceKlass org/jetbrains/kotlin/descriptors/VariableDescriptorWithAccessors
instanceKlass org/jetbrains/kotlin/descriptors/VariableDescriptor
instanceKlass kotlin/jvm/internal/StringCompanionObject
instanceKlass org/jetbrains/kotlin/types/error/ErrorTypeConstructor
instanceKlass org/jetbrains/kotlin/types/error/ErrorScope
instanceKlass org/jetbrains/kotlin/cli/common/ModuleVisibilityHelperImpl
instanceKlass org/jetbrains/kotlin/descriptors/DescriptorVisibilities$12
instanceKlass org/jetbrains/kotlin/descriptors/DescriptorVisibilities$11
instanceKlass org/jetbrains/kotlin/descriptors/DescriptorVisibilities$10
instanceKlass org/jetbrains/kotlin/util/ModuleVisibilityHelper
instanceKlass org/jetbrains/kotlin/descriptors/DescriptorVisibility
instanceKlass org/jetbrains/kotlin/descriptors/Visibility
instanceKlass org/jetbrains/kotlin/descriptors/DescriptorVisibilities
instanceKlass org/jetbrains/kotlin/name/SpecialNames
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/ReceiverValue
instanceKlass org/jetbrains/kotlin/resolve/scopes/receivers/Receiver
instanceKlass org/jetbrains/kotlin/descriptors/FunctionDescriptor$CopyBuilder
instanceKlass org/jetbrains/kotlin/descriptors/CallableMemberDescriptor$CopyBuilder
instanceKlass org/jetbrains/kotlin/descriptors/ClassConstructorDescriptor
instanceKlass org/jetbrains/kotlin/mpp/ConstructorSymbolMarker
instanceKlass org/jetbrains/kotlin/descriptors/ConstructorDescriptor
instanceKlass java/util/Formattable
instanceKlass java/util/Formatter$Flags
instanceKlass java/util/Formatter$FormatSpecifier
instanceKlass java/util/Formatter$Conversion
instanceKlass java/util/Formatter$FixedString
instanceKlass java/util/Formatter$FormatString
instanceKlass java/util/Formatter
instanceKlass org/jetbrains/kotlin/types/error/ErrorModuleDescriptor
instanceKlass org/jetbrains/kotlin/types/error/ErrorUtils
instanceKlass org/jetbrains/kotlin/types/TypeProjection
instanceKlass org/jetbrains/kotlin/types/model/TypeArgumentMarker
instanceKlass org/jetbrains/kotlin/types/TypeUtils
instanceKlass org/jetbrains/kotlin/descriptors/impl/AbstractClassDescriptor$1$1
instanceKlass org/jetbrains/kotlin/types/AbstractTypeRefiner
instanceKlass org/jetbrains/kotlin/types/checker/KotlinTypeRefinerKt
instanceKlass org/jetbrains/kotlin/resolve/descriptorUtil/DescriptorUtilsKt
instanceKlass org/jetbrains/kotlin/descriptors/FunctionDescriptor
instanceKlass org/jetbrains/kotlin/mpp/FunctionSymbolMarker
instanceKlass org/jetbrains/kotlin/descriptors/CallableMemberDescriptor
instanceKlass org/jetbrains/kotlin/resolve/DescriptorUtils
instanceKlass org/jetbrains/kotlin/resolve/scopes/MemberScopeImpl
instanceKlass org/jetbrains/kotlin/descriptors/SupertypeLoopChecker
instanceKlass org/jetbrains/kotlin/types/ClassifierBasedTypeConstructor
instanceKlass org/jetbrains/kotlin/descriptors/ReceiverParameterDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/ParameterDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/ValueDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/CallableDescriptor
instanceKlass org/jetbrains/kotlin/mpp/CallableSymbolMarker
instanceKlass org/jetbrains/kotlin/descriptors/impl/AbstractClassDescriptor$3
instanceKlass org/jetbrains/kotlin/descriptors/impl/AbstractClassDescriptor$2
instanceKlass org/jetbrains/kotlin/descriptors/impl/AbstractClassDescriptor$1
instanceKlass org/jetbrains/kotlin/descriptors/Modality$Companion
instanceKlass org/jetbrains/kotlin/descriptors/impl/ModuleAwareClassDescriptor$Companion
instanceKlass org/jetbrains/kotlin/types/TypeConstructor
instanceKlass org/jetbrains/kotlin/types/model/TypeConstructorMarker
instanceKlass org/jetbrains/kotlin/resolve/scopes/MemberScope
instanceKlass org/jetbrains/kotlin/resolve/scopes/ResolutionScope
instanceKlass org/jetbrains/kotlin/descriptors/impl/ModuleAwareClassDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/ClassDescriptor
instanceKlass org/jetbrains/kotlin/mpp/RegularClassSymbolMarker
instanceKlass org/jetbrains/kotlin/descriptors/ClassifierDescriptorWithTypeParameters
instanceKlass org/jetbrains/kotlin/mpp/ClassLikeSymbolMarker
instanceKlass org/jetbrains/kotlin/descriptors/Substitutable
instanceKlass org/jetbrains/kotlin/descriptors/MemberDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/DeclarationDescriptorWithVisibility
instanceKlass org/jetbrains/kotlin/descriptors/PackageViewDescriptor
instanceKlass org/jetbrains/kotlin/renderer/KeywordStringsGenerated
instanceKlass org/jetbrains/kotlin/renderer/RenderingUtilsKt
instanceKlass org/jetbrains/kotlin/descriptors/ModuleDescriptor$DefaultImpls
instanceKlass org/jetbrains/kotlin/renderer/DescriptorRendererImpl$RenderDeclarationDescriptorVisitor
instanceKlass org/jetbrains/kotlin/descriptors/DeclarationDescriptorVisitor
instanceKlass org/jetbrains/kotlin/renderer/ClassifierNamePolicy$FULLY_QUALIFIED
instanceKlass org/jetbrains/kotlin/renderer/ClassifierNamePolicy$SHORT
instanceKlass org/jetbrains/kotlin/renderer/ExcludedTypeAnnotations
instanceKlass org/jetbrains/kotlin/renderer/DescriptorRenderer$ValueParametersHandler$DEFAULT
instanceKlass org/jetbrains/kotlin/renderer/DescriptorRenderer$ValueParametersHandler
instanceKlass org/jetbrains/kotlin/renderer/DescriptorRendererModifier$Companion
instanceKlass kotlin/properties/ObservableProperty
instanceKlass kotlin/properties/ReadWriteProperty
instanceKlass kotlin/properties/Delegates
instanceKlass org/jetbrains/kotlin/renderer/ClassifierNamePolicy$SOURCE_CODE_QUALIFIED
instanceKlass org/jetbrains/kotlin/renderer/ClassifierNamePolicy
instanceKlass org/jetbrains/kotlin/renderer/DescriptorRendererOptionsImpl
instanceKlass org/jetbrains/kotlin/renderer/DescriptorRendererOptions
instanceKlass org/jetbrains/kotlin/renderer/DescriptorRenderer$Companion
instanceKlass org/jetbrains/kotlin/renderer/DescriptorRenderer
instanceKlass org/jetbrains/kotlin/descriptors/SourceElement$1
instanceKlass org/jetbrains/kotlin/descriptors/SourceElement
instanceKlass org/jetbrains/kotlin/descriptors/PackageFragmentDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/ClassOrPackageFragmentDescriptor
instanceKlass org/jetbrains/kotlin/builtins/jvm/JavaToKotlinClassMapper
instanceKlass org/jetbrains/kotlin/builtins/PlatformToKotlinClassMapper
instanceKlass org/jetbrains/kotlin/builtins/jvm/JvmBuiltInsCustomizer
instanceKlass org/jetbrains/kotlin/storage/StorageKt
instanceKlass org/jetbrains/kotlin/utils/CollectionsKt
instanceKlass org/jetbrains/kotlin/builtins/StandardNames$FqNames
instanceKlass org/jetbrains/kotlin/builtins/StandardNames
instanceKlass org/jetbrains/kotlin/builtins/jvm/JvmBuiltInClassDescriptorFactory$Companion
instanceKlass org/jetbrains/kotlin/builtins/jvm/JvmBuiltInClassDescriptorFactory
instanceKlass org/jetbrains/kotlin/builtins/functions/BuiltInFictitiousFunctionClassFactory
instanceKlass org/jetbrains/kotlin/descriptors/deserialization/ClassDescriptorFactory
instanceKlass org/jetbrains/kotlin/serialization/deserialization/builtins/BuiltInsResourceLoader
instanceKlass org/jetbrains/kotlin/serialization/deserialization/builtins/BuiltInsLoaderImpl
instanceKlass sun/net/www/MessageHeader
instanceKlass sun/net/www/protocol/jar/JarFileFactory
instanceKlass sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
instanceKlass org/jetbrains/kotlin/builtins/BuiltInsLoader$Companion
instanceKlass org/jetbrains/kotlin/builtins/BuiltInsLoader
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/TopDownAnalyzerFacadeForJVM$SourceOrBinaryModuleClassResolver
instanceKlass org/jetbrains/kotlin/load/java/lazy/ModuleClassResolver
instanceKlass org/jetbrains/kotlin/modules/TargetId
instanceKlass org/jetbrains/kotlin/modules/TargetIdKt
instanceKlass org/jetbrains/kotlin/incremental/components/EnumWhenTracker$DoNothing
instanceKlass org/jetbrains/kotlin/incremental/components/EnumWhenTracker
instanceKlass org/jetbrains/kotlin/incremental/components/InlineConstTracker$DoNothing
instanceKlass org/jetbrains/kotlin/incremental/components/InlineConstTracker
instanceKlass org/jetbrains/kotlin/incremental/components/ExpectActualTracker$DoNothing
instanceKlass org/jetbrains/kotlin/incremental/components/ExpectActualTracker
instanceKlass org/jetbrains/kotlin/incremental/components/LookupTracker$DO_NOTHING
instanceKlass org/jetbrains/kotlin/incremental/components/LookupTracker
instanceKlass org/jetbrains/kotlin/builtins/KotlinBuiltIns$4
instanceKlass org/jetbrains/kotlin/context/MutableModuleContextImpl
instanceKlass org/jetbrains/kotlin/context/MutableModuleContext
instanceKlass org/jetbrains/kotlin/context/ModuleContext
instanceKlass org/jetbrains/kotlin/descriptors/impl/PackageViewDescriptorFactory$Default
instanceKlass org/jetbrains/kotlin/descriptors/ModuleCapability
instanceKlass org/jetbrains/kotlin/descriptors/impl/PackageViewDescriptorFactory$Companion
instanceKlass org/jetbrains/kotlin/descriptors/impl/PackageViewDescriptorFactory
instanceKlass org/jetbrains/kotlin/descriptors/annotations/Annotations$Companion$EMPTY$1
instanceKlass org/jetbrains/kotlin/descriptors/annotations/Annotations$Companion
instanceKlass org/jetbrains/kotlin/descriptors/annotations/Annotations
instanceKlass org/jetbrains/kotlin/descriptors/annotations/AnnotatedImpl
instanceKlass org/jetbrains/kotlin/builtins/jvm/JvmBuiltIns$WhenMappings
instanceKlass org/jetbrains/kotlin/builtins/KotlinBuiltIns$3
instanceKlass org/jetbrains/kotlin/builtins/KotlinBuiltIns$2
instanceKlass org/jetbrains/kotlin/storage/LockBasedStorageManager$LockBasedLazyValue
instanceKlass org/jetbrains/kotlin/builtins/KotlinBuiltIns$1
instanceKlass org/jetbrains/kotlin/descriptors/ModuleDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/deserialization/AdditionalClassPartsProvider
instanceKlass org/jetbrains/kotlin/types/model/TypeArgumentListMarker
instanceKlass org/jetbrains/kotlin/types/model/SimpleTypeMarker
instanceKlass org/jetbrains/kotlin/types/KotlinType
instanceKlass org/jetbrains/kotlin/incremental/components/LookupLocation
instanceKlass org/jetbrains/kotlin/descriptors/deserialization/PlatformDependentDeclarationFilter
instanceKlass org/jetbrains/kotlin/descriptors/ClassifierDescriptor
instanceKlass org/jetbrains/kotlin/mpp/ClassifierSymbolMarker
instanceKlass org/jetbrains/kotlin/descriptors/DeclarationDescriptorNonRoot
instanceKlass org/jetbrains/kotlin/descriptors/DeclarationDescriptorWithSource
instanceKlass org/jetbrains/kotlin/descriptors/DeclarationDescriptor
instanceKlass org/jetbrains/kotlin/mpp/DeclarationSymbolMarker
instanceKlass org/jetbrains/kotlin/descriptors/annotations/Annotated
instanceKlass org/jetbrains/kotlin/descriptors/ValidateableDescriptor
instanceKlass org/jetbrains/kotlin/descriptors/Named
instanceKlass org/jetbrains/kotlin/builtins/KotlinBuiltIns
instanceKlass org/jetbrains/kotlin/context/SimpleGlobalContext
instanceKlass org/jetbrains/kotlin/storage/ExceptionTracker
instanceKlass org/jetbrains/kotlin/context/ProjectContextImpl
instanceKlass org/jetbrains/kotlin/context/ProjectContext
instanceKlass org/jetbrains/kotlin/context/GlobalContext
instanceKlass org/jetbrains/kotlin/context/ContextKt
instanceKlass org/jetbrains/kotlin/platform/TargetPlatform
instanceKlass org/jetbrains/kotlin/platform/TargetPlatformKt
instanceKlass org/jetbrains/kotlin/platform/TargetPlatformVersion$NoVersion
instanceKlass org/jetbrains/kotlin/platform/SimplePlatform
instanceKlass org/jetbrains/kotlin/platform/jvm/JvmPlatforms
instanceKlass org/jetbrains/kotlin/resolve/TargetEnvironment$Companion
instanceKlass org/jetbrains/kotlin/resolve/TargetEnvironment
instanceKlass java/lang/invoke/MethodHandle$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/CachedValueProvider$Result
instanceKlass org/jetbrains/kotlin/com/intellij/util/CachedValueBase
instanceKlass org/jetbrains/kotlin/resolve/diagnostics/DiagnosticSuppressor$Companion
instanceKlass org/jetbrains/kotlin/resolve/diagnostics/DiagnosticSuppressor
instanceKlass org/jetbrains/kotlin/util/ExtensionProvider$Companion
instanceKlass org/jetbrains/kotlin/util/MappedExtensionProvider
instanceKlass org/jetbrains/kotlin/resolve/diagnostics/KotlinSuppressCache$Companion
instanceKlass org/jetbrains/kotlin/diagnostics/AbstractKotlinSuppressCache$Companion
instanceKlass org/jetbrains/kotlin/resolve/diagnostics/Diagnostics$Companion$EMPTY$1
instanceKlass org/jetbrains/kotlin/resolve/diagnostics/Diagnostics$Companion
instanceKlass org/jetbrains/kotlin/resolve/diagnostics/MutableDiagnosticsWithSuppression
instanceKlass org/jetbrains/kotlin/resolve/diagnostics/Diagnostics
instanceKlass org/jetbrains/kotlin/diagnostics/GenericDiagnostics
instanceKlass org/jetbrains/kotlin/util/slicedMap/WritableSlice
instanceKlass org/jetbrains/kotlin/resolve/BindingTraceContext$1
instanceKlass org/jetbrains/kotlin/resolve/CleanableBindingContext
instanceKlass org/jetbrains/kotlin/util/slicedMap/SlicedMapImpl
instanceKlass org/jetbrains/kotlin/resolve/BindingTraceFilter$Companion
instanceKlass org/jetbrains/kotlin/resolve/BindingTraceFilter
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticSink$2
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticSink$1
instanceKlass org/jetbrains/kotlin/util/slicedMap/ReadOnlySlice
instanceKlass org/jetbrains/kotlin/util/slicedMap/MutableSlicedMap
instanceKlass org/jetbrains/kotlin/util/slicedMap/SlicedMap
instanceKlass org/jetbrains/kotlin/diagnostics/AbstractKotlinSuppressCache
instanceKlass org/jetbrains/kotlin/resolve/BindingContext
instanceKlass org/jetbrains/kotlin/resolve/BindingTraceContext
instanceKlass org/jetbrains/kotlin/resolve/BindingTrace
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticSink
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileWithId
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileSet
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileVisitor
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VfsUtilCore
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/TopDownAnalyzerFacadeForJVM
instanceKlass org/jetbrains/kotlin/psi/KtOperationExpression
instanceKlass org/jetbrains/kotlin/BlockExpressionElementType$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/IReparseableElementTypeBase
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/ICompositeElementType
instanceKlass org/jetbrains/kotlin/KtNodeTypes
instanceKlass org/jetbrains/kotlin/com/intellij/lang/PsiParser
instanceKlass org/jetbrains/kotlin/kdoc/lexer/KDocTokens
instanceKlass org/jetbrains/kotlin/lexer/KtTokens
instanceKlass org/jetbrains/kotlin/diagnostics/PositioningStrategy
instanceKlass org/jetbrains/kotlin/diagnostics/PositioningStrategies
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticFactory$Companion
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticMarker
instanceKlass org/jetbrains/kotlin/diagnostics/UnboundDiagnostic
instanceKlass org/jetbrains/kotlin/diagnostics/DiagnosticFactory
instanceKlass org/jetbrains/kotlin/cli/common/messages/AnalyzerWithCompilerReport$Companion
instanceKlass org/jetbrains/kotlin/cli/common/messages/AnalyzerWithCompilerReport
instanceKlass org/jetbrains/kotlin/analyzer/AbstractAnalyzerWithCompilerReport
instanceKlass org/jetbrains/kotlin/load/kotlin/ModuleVisibilityManager$SERVICE
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinToJVMBytecodeCompiler
instanceKlass org/jetbrains/kotlin/com/intellij/injected/editor/DocumentWindow
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/impl/FrozenDocument
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/ByteArrayCharSequence
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/impl/DocumentImpl$1
instanceKlass java/beans/PropertyChangeListener
instanceKlass java/beans/ChangeListenerMap
instanceKlass java/beans/PropertyChangeSupport
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/impl/IntervalTreeImpl$IntervalTreeGuide
instanceKlass org/jetbrains/kotlin/com/intellij/util/WalkingState$TreeGuide
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/longs/LongSet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/longs/LongCollection
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/longs/LongIterable
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/impl/RedBlackTree$Node
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/ex/PrioritizedDocumentListener$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/ex/PrioritizedDocumentListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/ex/DocumentEx$1
instanceKlass org/jetbrains/kotlin/com/intellij/util/keyFMap/ArrayBackedFMap
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileEditor/impl/LoadTextUtil$ConvertResult
instanceKlass sun/nio/cs/ThreadLocalCoders$Cache
instanceKlass sun/nio/cs/ThreadLocalCoders
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileEditor/impl/LoadTextUtil$DetectResult
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/CharsetToolkit
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileEditor/impl/LoadTextUtil
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/JavaLanguageLevelKt
instanceKlass org/jetbrains/kotlin/config/AppendJavaSourceRootsHandlerKeyKt
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/CliVirtualFileFinderFactory
instanceKlass org/jetbrains/kotlin/load/kotlin/VirtualFileFinderFactory
instanceKlass org/jetbrains/kotlin/load/kotlin/MetadataFinderFactory
instanceKlass org/jetbrains/kotlin/cli/jvm/modules/CliJavaModuleResolver$Companion
instanceKlass org/jetbrains/kotlin/cli/jvm/modules/CliJavaModuleResolver
instanceKlass org/jetbrains/kotlin/resolve/jvm/modules/JavaModuleResolver
instanceKlass org/jetbrains/kotlin/load/java/JavaModuleAnnotationsProvider
instanceKlass org/jetbrains/kotlin/cli/jvm/index/SingleJavaFileRootsIndex
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JvmDependenciesIndexImpl
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock
instanceKlass java/util/concurrent/locks/ReadWriteLock
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JvmDependenciesDynamicCompoundIndex
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JvmDependenciesIndex
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/ClasspathRootsResolver$RootsAndModules
instanceKlass kotlin/sequences/TransformingSequence$iterator$1
instanceKlass kotlin/sequences/FilteringSequence$iterator$1
instanceKlass kotlin/jvm/internal/ArrayIterator
instanceKlass kotlin/jvm/internal/ArrayIteratorKt
instanceKlass kotlin/sequences/FlatteningSequence$iterator$1
instanceKlass kotlin/sequences/FlatteningSequence
instanceKlass kotlin/collections/ArraysKt___ArraysKt$asSequence$$inlined$Sequence$1
instanceKlass kotlin/collections/CollectionsKt___CollectionsKt$asSequence$$inlined$Sequence$1
instanceKlass kotlin/sequences/FilteringSequence
instanceKlass kotlin/sequences/TransformingSequence
instanceKlass kotlin/sequences/EmptySequence
instanceKlass kotlin/sequences/DropTakeSequence
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JavaRoot$RootTypes
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JavaRoot
instanceKlass java/io/RandomAccessFile$1
instanceKlass org/jetbrains/kotlin/cli/jvm/config/JavaSourceRoot
instanceKlass java/util/AbstractMap$SimpleEntry
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/FactoryMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/CharArrayExternalizable
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/ImmutableCharSequence
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/CharArrayUtil
instanceKlass java/util/zip/ZipFile$ZipEntryIterator
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler$EntryInfo
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ShareableKey
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/hash/LinkedHashMap$Entry
instanceKlass java/util/DualPivotQuicksort
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/hash/HashUtil
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/SLRUMap
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileAttributes
instanceKlass org/jetbrains/kotlin/com/intellij/util/io/ResourceHandle
instanceKlass org/jetbrains/kotlin/com/intellij/util/io/FileAccessorCache
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/hash/EqualityPolicy
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/AddonlyKeylessHash$KeyValueMapper
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Pair
instanceKlass org/jetbrains/kotlin/cli/jvm/modules/JavaModuleGraph
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/ClasspathRootsResolver$Companion
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/ClasspathRootsResolver
instanceKlass org/jetbrains/kotlin/cli/jvm/modules/CliJavaModuleFinder
instanceKlass org/jetbrains/kotlin/resolve/jvm/modules/JavaModuleFinder
instanceKlass kotlin/comparisons/ComparisonsKt__ComparisonsKt
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileUtil$2
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileUtil$1
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/CaseInsensitiveStringHashingStrategy
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileUtilRt$SymlinkResolver
instanceKlass org/jetbrains/kotlin/com/intellij/util/indexing/IndexingDataKeys
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment$special$$inlined$sortBy$1
instanceKlass org/jetbrains/kotlin/psi/UserDataProperty
instanceKlass kotlin/reflect/jvm/internal/KPropertyImpl$Companion
instanceKlass kotlin/reflect/jvm/internal/KCallableImpl
instanceKlass kotlin/reflect/KMutableProperty$Setter
instanceKlass org/jetbrains/kotlin/resolve/multiplatform/IsCommonSourceKt
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/PsiUtilCore$NullPsiElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/PsiUtilCore
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/FileTrees
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinScriptStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinPlaceHolderWithTextStub
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/IReparseableLeafElementType
instanceKlass org/jetbrains/kotlin/com/intellij/psi/TokenType
instanceKlass org/jetbrains/kotlin/com/intellij/psi/LiteralTextEscaper
instanceKlass org/jetbrains/kotlin/com/intellij/psi/ContributedReferenceHost
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinCollectionLiteralExpressionStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinClassLiteralExpressionStub
instanceKlass org/jetbrains/kotlin/psi/KtDoubleColonExpression
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinConstantExpressionStub
instanceKlass org/jetbrains/kotlin/psi/stubs/elements/KtConstantExpressionElementType$Companion
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinContextReceiverStub
instanceKlass org/jetbrains/kotlin/psi/LambdaArgument
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinContractEffectStub
instanceKlass org/jetbrains/kotlin/psi/ValueArgumentName
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinValueArgumentStub
instanceKlass org/jetbrains/kotlin/psi/ValueArgument
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinEnumEntrySuperclassReferenceExpressionStub
instanceKlass org/jetbrains/kotlin/psi/KtQualifiedExpression
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinNameReferenceExpressionStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinTypeProjectionStub
instanceKlass org/jetbrains/kotlin/psi/KtSimpleNameExpression
instanceKlass org/jetbrains/kotlin/psi/KtReferenceExpression
instanceKlass org/jetbrains/kotlin/psi/stubs/impl/KotlinTypeBean
instanceKlass org/jetbrains/kotlin/types/model/KotlinTypeMarker
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinUserTypeStub
instanceKlass org/jetbrains/kotlin/psi/KtTypeElement
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinModifierListStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinImportAliasStub
instanceKlass org/jetbrains/kotlin/psi/KtImportInfo$ImportContent
instanceKlass org/jetbrains/kotlin/psi/KtImportInfo
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinImportDirectiveStub
instanceKlass org/jetbrains/kotlin/psi/KtAnnotationsContainer
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinAnnotationUseSiteTargetStub
instanceKlass org/jetbrains/kotlin/psi/KtCallElement
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinAnnotationEntryStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinTypeParameterStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinParameterStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinConstructorStub
instanceKlass org/jetbrains/kotlin/psi/KtAnonymousInitializer
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinPlaceHolderStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinObjectStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinTypeAliasStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinBackingFieldStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinPropertyAccessorStub
instanceKlass org/jetbrains/kotlin/psi/KtVariableDeclaration
instanceKlass org/jetbrains/kotlin/psi/KtValVarKeywordOwner
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinPropertyStub
instanceKlass org/jetbrains/kotlin/psi/KtFunction
instanceKlass org/jetbrains/kotlin/psi/KtDeclarationWithBody
instanceKlass org/jetbrains/kotlin/psi/KtCallableDeclaration
instanceKlass org/jetbrains/kotlin/psi/KtDeclarationWithInitializer
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinFunctionStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinCallableStubBase
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/SubstrateRef
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinClassStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinClassOrObjectStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinStubWithFqName
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/NamedStub
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinClassifierStub
instanceKlass org/jetbrains/kotlin/com/intellij/psi/StubBasedPsiElement
instanceKlass org/jetbrains/kotlin/psi/KtTypeParameterListOwner
instanceKlass org/jetbrains/kotlin/psi/KtPureClassOrObject
instanceKlass org/jetbrains/kotlin/psi/KtClassLikeDeclaration
instanceKlass org/jetbrains/kotlin/psi/KtNamedDeclaration
instanceKlass org/jetbrains/kotlin/psi/KtStatementExpression
instanceKlass org/jetbrains/kotlin/psi/KtNamed
instanceKlass org/jetbrains/kotlin/psi/KtDeclaration
instanceKlass org/jetbrains/kotlin/psi/KtModifierListOwner
instanceKlass org/jetbrains/kotlin/psi/KtExpression
instanceKlass org/jetbrains/kotlin/psi/stubs/elements/KtStubElementTypes
instanceKlass org/jetbrains/kotlin/psi/stubs/elements/KtTokenSets
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/TokenSet
instanceKlass org/jetbrains/kotlin/psi/KtFile$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/StubbedSpine
instanceKlass org/jetbrains/kotlin/psi/KtDeclarationContainer
instanceKlass org/jetbrains/kotlin/psi/KtAnnotated
instanceKlass org/jetbrains/kotlin/psi/KtElement
instanceKlass org/jetbrains/kotlin/psi/KtPureElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiModifiableCodeBlock
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiClassOwner
instanceKlass org/jetbrains/kotlin/com/intellij/util/keyFMap/PairElementsFMap
instanceKlass java/util/function/UnaryOperator
instanceKlass org/jetbrains/kotlin/com/intellij/psi/AbstractFileViewProvider$VirtualFileContent
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiLock
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiLargeBinaryFile
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiLargeFile
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiBinaryFile
instanceKlass org/jetbrains/kotlin/com/intellij/psi/AbstractFileViewProvider$Content
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ContainerUtilRt
instanceKlass org/jetbrains/kotlin/com/intellij/model/ModelBranch
instanceKlass org/jetbrains/kotlin/com/intellij/injected/editor/VirtualFileWindow
instanceKlass kotlin/collections/AbstractIterator$WhenMappings
instanceKlass kotlin/io/FileTreeWalk$WalkState
instanceKlass kotlin/collections/AbstractIterator
instanceKlass kotlin/io/FileTreeWalk
instanceKlass kotlin/io/FilePathComponents
instanceKlass kotlin/io/FilesKt__FilePathComponentsKt
instanceKlass org/jetbrains/kotlin/extensions/PreprocessedFileCreator
instanceKlass java/util/AbstractList$Itr
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/KeyedExtensionCollector$MyExtensionPointListener
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/CoreEnvironmentUtilsKt
instanceKlass org/jetbrains/kotlin/resolve/lazy/declarations/DeclarationProviderFactoryService$Companion
instanceKlass org/jetbrains/kotlin/resolve/lazy/declarations/DeclarationProviderFactoryService
instanceKlass org/jetbrains/kotlin/com/intellij/diagnostic/Activity
instanceKlass org/jetbrains/kotlin/com/intellij/diagnostic/StartUpMeasurer
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/CliModuleAnnotationsResolver
instanceKlass org/jetbrains/kotlin/resolve/ModuleAnnotationsResolver
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinJavaPsiFacade$2
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinJavaPsiFacade$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiModifierList
instanceKlass org/jetbrains/kotlin/resolve/jvm/NotFoundPackagesCachingStrategy$Default
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/ElementBase$ElementIconRequest
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaClass
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaTypeParameterListOwner
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaModifierListOwner
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaClassifier
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaNamedElement
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaAnnotationOwner
instanceKlass org/jetbrains/kotlin/load/java/structure/JavaElement
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinJavaPsiFacade$KotlinPsiElementFinderWrapperImpl
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinJavaPsiFacade$KotlinPsiElementFinderWrapper
instanceKlass org/jetbrains/kotlin/resolve/jvm/NotFoundPackagesCachingStrategy
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinJavaPsiFacade
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/source/JavaElementSourceFactory
instanceKlass org/jetbrains/kotlin/cli/common/CliModuleVisibilityManagerImpl
instanceKlass org/jetbrains/kotlin/load/kotlin/ModuleVisibilityManager
instanceKlass org/jetbrains/kotlin/compiler/plugin/CompilerPluginRegistrarKt
instanceKlass org/jetbrains/kotlin/compiler/plugin/CompilerPluginRegistrar$ExtensionStorage
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass org/jetbrains/kotlin/fir/extensions/FirAnalysisHandlerExtension
instanceKlass org/jetbrains/kotlin/resolve/extensions/AssignResolutionAltererExtension
instanceKlass org/jetbrains/kotlin/extensions/AnnotationBasedExtension
instanceKlass org/jetbrains/kotlin/types/DefaultTypeAttributeTranslator
instanceKlass org/jetbrains/kotlin/types/extensions/TypeAttributeTranslators
instanceKlass org/jetbrains/kotlin/extensions/TypeAttributeTranslatorExtension
instanceKlass org/jetbrains/kotlin/types/TypeAttributeTranslator
instanceKlass org/jetbrains/kotlin/fir/extensions/FirExtensionRegistrarAdapter
instanceKlass org/jetbrains/kotlin/serialization/DescriptorSerializerPlugin
instanceKlass org/jetbrains/kotlin/extensions/internal/CallResolutionInterceptorExtension
instanceKlass org/jetbrains/kotlin/extensions/internal/CandidateInterceptor
instanceKlass org/jetbrains/kotlin/extensions/internal/TypeResolutionInterceptorExtension
instanceKlass org/jetbrains/kotlin/extensions/internal/TypeResolutionInterceptor
instanceKlass org/jetbrains/kotlin/cli/common/extensions/ShellExtension
instanceKlass org/jetbrains/kotlin/cli/common/extensions/ScriptEvaluationExtension
instanceKlass org/jetbrains/kotlin/backend/common/extensions/IrGenerationExtension
instanceKlass org/jetbrains/kotlin/ir/linkage/IrDeserializer$IrLinkerExtension
instanceKlass org/jetbrains/kotlin/resolve/extensions/ExtraImportsProviderExtension
instanceKlass org/jetbrains/kotlin/extensions/ProcessSourcesBeforeCompilingExtension
instanceKlass org/jetbrains/kotlin/extensions/CollectAdditionalSourcesExtension
instanceKlass org/jetbrains/kotlin/extensions/CompilerConfigurationExtension
instanceKlass org/jetbrains/kotlin/js/translate/extensions/JsSyntheticTranslateExtension
instanceKlass org/jetbrains/kotlin/extensions/PreprocessedVirtualFileFactoryExtension
instanceKlass org/jetbrains/kotlin/extensions/DeclarationAttributeAltererExtension
instanceKlass org/jetbrains/kotlin/extensions/StorageComponentContainerContributor
instanceKlass org/jetbrains/kotlin/resolve/jvm/extensions/PackageFragmentProviderExtension
instanceKlass org/jetbrains/kotlin/resolve/jvm/extensions/AnalysisHandlerExtension
instanceKlass org/jetbrains/kotlin/resolve/extensions/AnalysisHandlerExtension
instanceKlass org/jetbrains/kotlin/codegen/extensions/ClassFileFactoryFinalizerExtension
instanceKlass org/jetbrains/kotlin/backend/jvm/extensions/ClassGeneratorExtension
instanceKlass org/jetbrains/kotlin/codegen/extensions/ClassBuilderInterceptorExtension
instanceKlass org/jetbrains/kotlin/resolve/jvm/extensions/SyntheticJavaResolveExtension
instanceKlass org/jetbrains/kotlin/resolve/extensions/SyntheticResolveExtension
instanceKlass org/jetbrains/kotlin/extensions/ProjectExtensionDescriptor
instanceKlass org/jetbrains/kotlin/codegen/extensions/ExpressionCodegenExtension
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileUtilRt$2
instanceKlass java/util/Random
instanceKlass java/util/random/RandomGenerator
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileUtilRt$RepeatableIOOperation
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass java/io/FileFilter
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileUtilRt
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/PersistentFSConstants
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/DummyHolderFactory$DefaultFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/DummyHolderFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiImportHolder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/PsiFileWithStubSupport
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiFileEx
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/ui/Queryable
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/JavaDummyHolderFactory
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/facade/JvmFacadeImpl
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/facade/JvmFacade
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/HolderFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiConstantEvaluationHelper
instanceKlass org/jetbrains/kotlin/com/intellij/psi/JavaPsiFacade
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/MockInferredAnnotationsManager$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/InferredAnnotationsManager
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/ExternalAnnotationsListener
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/ExternalAnnotationsManager
instanceKlass org/jetbrains/kotlin/com/intellij/util/Processor
instanceKlass org/jetbrains/kotlin/asJava/finder/JavaElementFinder$Companion
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinFinderMarker
instanceKlass org/jetbrains/kotlin/asJava/KotlinAsJavaSupport$Companion
instanceKlass org/jetbrains/kotlin/asJava/KotlinAsJavaSupport
instanceKlass org/jetbrains/kotlin/asJava/classes/ImplUtilsKt
instanceKlass org/jetbrains/kotlin/asJava/LightClassGenerationSupport$Companion
instanceKlass org/jetbrains/kotlin/asJava/LightClassGenerationSupport
instanceKlass org/jetbrains/kotlin/resolve/jvm/JvmCodeAnalyzerInitializer
instanceKlass org/jetbrains/kotlin/resolve/CodeAnalyzerInitializer
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/JvmPsiConversionHelperImpl
instanceKlass org/jetbrains/kotlin/com/intellij/psi/JvmPsiConversionHelper
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/Interner
instanceKlass org/jetbrains/kotlin/utils/PlatformUtilsKt
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryClassSignatureParser
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionManager$2
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/SoftKeySoftValueHashMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/NotNullFunction
instanceKlass org/jetbrains/kotlin/com/intellij/util/NullableFunction
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/ProjectScope
instanceKlass gnu/trove/TObjectHash$NULL
instanceKlass gnu/trove/TObjectObjectProcedure
instanceKlass gnu/trove/THash
instanceKlass gnu/trove/TObjectHashingStrategy
instanceKlass gnu/trove/Equality
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinCliJavaFileManagerImpl$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/core/CoreJavaFileManager
instanceKlass org/jetbrains/kotlin/resolve/jvm/KotlinCliJavaFileManager
instanceKlass org/jetbrains/kotlin/com/intellij/psi/controlFlow/ControlFlowFactory$1
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentWeakKeySoftValueHashMap$HardKey
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentWeakKeySoftValueHashMap$KeyReference
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentWeakKeySoftValueHashMap
instanceKlass org/jetbrains/kotlin/com/intellij/psi/controlFlow/ControlFlow
instanceKlass org/jetbrains/kotlin/com/intellij/psi/controlFlow/ControlFlowFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/codeStyle/SuggestedNameInfo
instanceKlass org/jetbrains/kotlin/com/intellij/psi/codeStyle/JavaCodeStyleManager
instanceKlass org/jetbrains/kotlin/com/intellij/psi/codeStyle/JavaCodeStyleSettingsFacade
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/JavaResolveCache$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/JavaResolveCache
instanceKlass org/jetbrains/kotlin/com/intellij/util/lang/JavaVersion
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentRefValueHashMap$ValueReference
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentRefValueHashMap
instanceKlass sun/util/locale/LanguageTag
instanceKlass java/util/ResourceBundle$Control
instanceKlass org/jetbrains/kotlin/com/intellij/AbstractBundle
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/roots/LanguageLevelProjectExtension$LanguageLevelChangeListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/roots/LanguageLevelProjectExtension
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionManager$CalculationStack
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionGuard
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionGuard$StackStamp
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionManager
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiInferenceHelper
instanceKlass org/jetbrains/kotlin/com/intellij/psi/scope/processor/FilterScopeProcessor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/scope/PsiScopeProcessor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/scope/NameHint
instanceKlass org/jetbrains/kotlin/com/intellij/psi/scope/ElementClassHint
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/PsiResolveHelperImpl
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiResolveHelper
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/JavaPsiImplementationHelper
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiElementFactoryImpl$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/TypeAnnotationProvider$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/TypeAnnotationProvider$Static
instanceKlass org/jetbrains/kotlin/com/intellij/psi/TypeAnnotationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiAnnotation
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/types/JvmPrimitiveTypeKind
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmAnnotation
instanceKlass org/jetbrains/kotlin/com/intellij/lang/java/parser/JavaParserUtil$ParserWrapper
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiImportStatementBase
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiTypeElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReferenceExpression
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiExpression
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiCaseLabelElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiAnnotationMemberValue
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJavaCodeReferenceElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiQualifiedReferenceElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiQualifiedReference
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJavaReference
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiPolyVariantReference
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/types/JvmReferenceType
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/types/JvmPrimitiveType
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiType
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiAnnotationOwner
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/types/JvmType
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiJavaParserFacadeImpl
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiElementFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJavaParserFacade
instanceKlass org/jetbrains/kotlin/com/intellij/psi/JVMElementFactory
instanceKlass org/jetbrains/kotlin/com/intellij/lang/injection/InjectedLanguageManager
instanceKlass org/jetbrains/kotlin/com/intellij/mock/MockDumbUtil
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/DumbUtil
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/impl/VirtualFileEnumerationAware
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/impl/VirtualFileEnumeration
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/SearchScope
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/ProjectAwareFileFilter
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiDirectory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/file/PsiDirectoryFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/CachedValue
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/ParameterizedCachedValue
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiCachedValuesFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/CachedValueProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/CachedValuesManager
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/tree/TreeElementVisitor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiFileFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiToDocumentSynchronizer
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiRecursiveVisitor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiElementVisitor
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/event/DocumentListener
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiDocumentManager
instanceKlass org/jetbrains/kotlin/com/intellij/core/MockDocumentCommitProcessor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/DocumentCommitProcessor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/SmartPointerManager
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/file/impl/FileManagerImpl$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Comparing
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileTypes/CharsetUtil
instanceKlass org/jetbrains/kotlin/com/intellij/util/LocalTimeCounter
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointPriorityListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointUtil$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointUtil
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/NonPhysicalFileSystem
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/file/impl/FileManagerImpl
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/PsiModificationTracker$SERVICE
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/NotNullFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Factory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileFilter$2
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileFilter$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileFilter
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/LowMemoryWatcher
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/ResolveCache$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/AnyPsiChangeListener
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/file/impl/FileManager
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/wm/ex/ProgressIndicatorEx
instanceKlass java/util/concurrent/atomic/AtomicReferenceArray
instanceKlass org/jetbrains/kotlin/com/intellij/psi/ResolveResult
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/ResolveCache$StrongValueReference
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentWeakKeySoftValueHashMap$ValueReference
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReference
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/ResolveCache$AbstractResolver
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/ResolveCache
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiModificationTrackerImpl$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/DumbService$DumbModeListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/DumbService
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/PsiModificationTracker$Listener
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiModificationTrackerImpl
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/PsiModificationTracker
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/facade/JvmElementProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiElementFinder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiTreeChangePreprocessor
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/roots/PackageIndex
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/file/impl/JavaFileManager
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/ProjectScopeBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/roots/FileIndexFacade
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/ResolveScopeManager
instanceKlass org/jetbrains/kotlin/com/intellij/util/CachedValuesFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/Project
instanceKlass org/jetbrains/kotlin/com/intellij/core/CoreProjectEnvironment
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment$Companion$getOrCreateApplicationEnvironment$1$2
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/TransactionGuard
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/JavaClassSupers
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinBinaryClassCache$Companion
instanceKlass org/jetbrains/kotlin/load/kotlin/KotlinBinaryClassCache
instanceKlass org/jetbrains/kotlin/psi/stubs/KotlinFileStub
instanceKlass org/jetbrains/kotlin/parsing/KotlinParserDefinition$Companion
instanceKlass org/jetbrains/kotlin/parsing/KotlinParserDefinition
instanceKlass sun/invoke/util/VerifyAccess$1
instanceKlass javax/swing/Icon
instanceKlass org/jetbrains/kotlin/com/intellij/util/keyFMap/OneElementFMap
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/impl/ExtensionProcessingHelper
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/BuildNumber
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginContentDescriptor$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginContentDescriptor
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/IdeaPluginDescriptorImpl$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/IdeaPluginDescriptorImpl
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/IdeaPluginDescriptor
instanceKlass org/jetbrains/kotlin/org/codehaus/stax2/ri/typed/ValueDecoderFactory
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/ByteBasedPNameTable$Bucket
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointDescriptor
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/util/DataUtil
instanceKlass kotlin/_Assertions
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/ElementScope
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/ByteBasedPNameFactory
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/ModuleDependenciesDescriptor$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/ModuleDependenciesDescriptor
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/ContainerDescriptor
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/RawPluginDescriptor
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/util/BufferRecycler
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/util/NameTable
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/util/XmlCharTypes
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/AttributeCollector
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/util/TextBuilder
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/FixedNsContext
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/NsBinding
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/PName
instanceKlass org/jetbrains/kotlin/org/codehaus/stax2/XMLStreamLocation2
instanceKlass org/jetbrains/kotlin/org/codehaus/stax2/typed/TypedValueDecoder
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/stax/StreamReaderImpl
instanceKlass org/jetbrains/kotlin/org/codehaus/stax2/XMLStreamReader2
instanceKlass org/jetbrains/kotlin/org/codehaus/stax2/typed/TypedXMLStreamReader
instanceKlass javax/xml/stream/XMLStreamReader
instanceKlass org/jetbrains/kotlin/org/codehaus/stax2/LocationInfo
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/XmlScanner
instanceKlass javax/xml/stream/XMLStreamConstants
instanceKlass javax/xml/namespace/NamespaceContext
instanceKlass javax/xml/stream/Location
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/InputBootstrapper
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/util/UriCanonicalizer
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/ReaderConfig$EncodingContext
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/impl/CommonConfig
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/StaxFactory
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/XmlReader
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/JavaZipFileDataLoader
instanceKlass org/jetbrains/kotlin/com/intellij/platform/util/plugins/DataLoader
instanceKlass org/jetbrains/kotlin/com/intellij/util/lang/ZipFilePool
instanceKlass sun/nio/fs/ExtendedFileSystemProvider
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/DescriptorListLoadingContext$threadLocalXmlFactory$1
instanceKlass org/jetbrains/kotlin/com/intellij/util/PlatformUtils
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginDescriptorLoader$createPluginLoadingResult$1
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginManagerCore
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginLoadingResult
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/DescriptorListLoadingContext
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/ReadModuleContext
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginXmlPathResolver$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginXmlPathResolver
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PathResolver
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/PluginDescriptorLoader
instanceKlass org/jetbrains/kotlin/com/intellij/psi/JavaModuleSystem
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/compiled/ClsCustomNavigationPolicy
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/IdeaExtensionPoints
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/smartPointers/SmartPointerAnchorProvider
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/runner/JavaMainMethodProvider
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/NotNullLazyValue
instanceKlass org/jetbrains/kotlin/com/intellij/psi/augment/PsiAugmentProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/meta/MetaDataContributor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/FileContextProvider
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/DummyJavaFileCodeStyleFacadeFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/codeStyle/JavaFileCodeStyleFacadeFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/FileViewProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/compiled/ClsStubBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/compiled/ClassFileDecompilers$Full
instanceKlass java/util/TimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass java/lang/Class$AnnotationData
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/components/Service
instanceKlass org/jetbrains/kotlin/com/intellij/psi/compiled/ClassFileDecompilers
instanceKlass org/jetbrains/kotlin/com/intellij/psi/compiled/ClassFileDecompilers$Decompiler
instanceKlass org/jetbrains/kotlin/com/intellij/psi/util/MethodSignature
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/QueryExecutorBase
instanceKlass org/jetbrains/kotlin/com/intellij/util/QueryExecutor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/search/searches/SuperMethodsSearch$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/SmartExtensionPoint
instanceKlass org/jetbrains/kotlin/com/intellij/util/Query
instanceKlass org/jetbrains/kotlin/com/intellij/util/QueryFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/registry/RegistryValue
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/registry/Registry
instanceKlass org/jetbrains/kotlin/com/intellij/lang/folding/FoldingBuilderEx
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/PossiblyDumbAware
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/DumbAware
instanceKlass org/jetbrains/kotlin/com/intellij/lang/folding/FoldingBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/folding/JavaCodeFoldingSettings
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiParameter
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmParameter
instanceKlass org/jetbrains/kotlin/com/intellij/psi/presentation/java/VariablePresentationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiLocalVariable
instanceKlass org/jetbrains/kotlin/com/intellij/psi/presentation/java/FieldPresentationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiField
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiVariable
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmField
instanceKlass org/jetbrains/kotlin/com/intellij/psi/presentation/java/MethodPresentationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiMethod
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiParameterListOwner
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmMethod
instanceKlass org/jetbrains/kotlin/com/intellij/psi/presentation/java/ClassPresentationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiClass
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiTypeParameterListOwner
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJvmMember
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiTarget
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiNameIdentifierOwner
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiDocCommentOwner
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiMember
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJavaDocumentedElement
instanceKlass org/jetbrains/kotlin/com/intellij/pom/PomRenameableTarget
instanceKlass org/jetbrains/kotlin/com/intellij/pom/PomNamedTarget
instanceKlass org/jetbrains/kotlin/com/intellij/pom/PomTarget
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmClass
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmTypeParametersOwner
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmMember
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmTypeDeclaration
instanceKlass org/jetbrains/kotlin/com/intellij/navigation/ItemPresentation
instanceKlass org/jetbrains/kotlin/com/intellij/psi/presentation/java/PackagePresentationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/navigation/ItemPresentationProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiPackage
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiQualifiedNamedElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJvmModifiersOwner
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiModifierListOwner
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmModifiersOwner
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiDirectoryContainer
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmPackage
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmNamedElement
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmAnnotatedElement
instanceKlass org/jetbrains/kotlin/com/intellij/lang/jvm/JvmElement
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/projectRoots/JavaVersionService
instanceKlass org/jetbrains/kotlin/com/intellij/psi/JavaDirectoryService
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiSubstitutor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiSubstitutorFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/ModificationTracker$1
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/file/PsiPackageImplementationHelper
instanceKlass org/jetbrains/kotlin/com/intellij/util/graph/InboundSemiGraph
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/LoadingOrder
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/JavaContainerProvider
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/ContainerProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/PsiExpressionEvaluator
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/ConstantExpressionEvaluator
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/IndexSink
instanceKlass org/jetbrains/kotlin/com/intellij/util/diff/FlyweightCapableTreeStructure
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/LightStubBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/java/stubs/PsiJavaFileStub
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/PsiClassHolderFileStub
instanceKlass org/jetbrains/kotlin/com/intellij/psi/StubBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/PsiFileStub
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/StubElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/Stub
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/ClearableLazyValue
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/StubSerializer
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/ObjectStubSerializer
instanceKlass org/jetbrains/kotlin/com/intellij/lang/java/JavaParserDefinition
instanceKlass org/jetbrains/kotlin/com/intellij/psi/javadoc/PsiDocToken
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiKeyword
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiIdentifier
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiJavaToken
instanceKlass org/jetbrains/kotlin/com/intellij/psi/javadoc/PsiDocTagValue
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/compiled/ClassFileStubBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/BinaryFileStubBuilder$CompositeBinaryFileStubBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/BinaryFileStubBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/psi/ClassFileViewProviderFactory
instanceKlass org/jetbrains/kotlin/com/intellij/psi/FileViewProviderFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Condition$3
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Condition$2
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Condition$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Condition
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Conditions
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/IElementType$Predicate
instanceKlass org/jetbrains/kotlin/com/intellij/lexer/Lexer
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileTypes/PlainTextParserDefinition
instanceKlass org/jetbrains/kotlin/com/intellij/lang/LanguageUtil
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiPlainText
instanceKlass org/jetbrains/kotlin/com/intellij/lang/ASTFactory$DefaultFactoryHolder
instanceKlass org/jetbrains/kotlin/com/intellij/ide/highlighter/ArchiveFileType
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass java/util/concurrent/ExecutorService
instanceKlass org/jetbrains/kotlin/com/intellij/util/ConcurrencyUtil
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileTypes/LanguageFileType
instanceKlass org/jetbrains/kotlin/com/intellij/ide/highlighter/JavaClassFileType
instanceKlass org/jetbrains/kotlin/com/intellij/DynamicBundle$LanguageBundleEP
instanceKlass org/jetbrains/kotlin/com/intellij/util/pico/CachingConstructorInjectionComponentAdapter
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/ApplicationInfo
instanceKlass org/jetbrains/kotlin/com/intellij/util/graph/GraphAlgorithms
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/command/impl/CoreCommandProcessor$1
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/command/CommandListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/command/CommandToken
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/command/CommandProcessor
instanceKlass org/jetbrains/kotlin/com/intellij/codeInsight/folding/CodeFoldingSettings
instanceKlass org/jetbrains/kotlin/com/intellij/util/ObjectUtils$Sentinel
instanceKlass org/jetbrains/kotlin/com/intellij/util/ObjectUtilsRt
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentIntObjectHashMap$CounterCell
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentLongObjectHashMap$Node
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentLongObjectMap$LongEntry
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentLongObjectHashMap
instanceKlass java/util/concurrent/Future
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/PerformInBackgroundOption
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/ProgressIndicator
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/Task
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/TaskInfo
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/Progressive
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReferenceService
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/ObjectStubTree
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiFile
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiFileSystemItem
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiCheckedRenameElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiNamedElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/NavigatablePsiElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/stubs/StubTreeLoader
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReferenceProviderBean
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReferenceContributor
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReferenceRegistrar
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/doubles/Double2ObjectMap
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/doubles/Double2ObjectFunction
instanceKlass java/util/function/DoubleFunction
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiReferenceProvider
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/resolve/reference/ReferenceProvidersRegistry
instanceKlass org/jetbrains/kotlin/com/intellij/lang/PsiBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/UserDataHolderUnprotected
instanceKlass org/jetbrains/kotlin/com/intellij/lang/SyntaxTreeBuilder
instanceKlass org/jetbrains/kotlin/com/intellij/lang/PsiBuilderFactory
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorFactory
instanceKlass sun/misc/Unsafe
instanceKlass org/jetbrains/kotlin/com/intellij/util/ReflectionUtil
instanceKlass org/jetbrains/kotlin/com/intellij/psi/CommonClassNames
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/CharArrayCharSequence
instanceKlass org/jetbrains/kotlin/com/intellij/util/text/CharSequenceBackedByArray
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/text/CharSequenceWithStringHash
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/text/Strings
instanceKlass java/util/regex/ASCII
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/text/StringUtilRt
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/source/CharTableImpl
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiLanguageInjectionHost
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiComment
instanceKlass org/jetbrains/kotlin/com/intellij/model/psi/UrlReferenceHost
instanceKlass org/jetbrains/kotlin/com/intellij/model/psi/PsiExternalReferenceHost
instanceKlass org/jetbrains/kotlin/com/intellij/navigation/NavigationItem
instanceKlass org/jetbrains/kotlin/com/intellij/pom/Navigatable
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiWhiteSpace
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiElement
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/ILazyParseableElementTypeBase
instanceKlass org/jetbrains/kotlin/com/intellij/psi/tree/IElementType
instanceKlass org/jetbrains/kotlin/com/intellij/lang/FileASTNode
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Iconable
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/ReparseableASTNode
instanceKlass org/jetbrains/kotlin/com/intellij/lang/ASTNode
instanceKlass org/jetbrains/kotlin/com/intellij/util/CharTable
instanceKlass org/jetbrains/kotlin/com/intellij/lang/ASTFactory
instanceKlass org/jetbrains/kotlin/com/intellij/lang/DefaultASTFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/encoding/EncodingRegistry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass org/jetbrains/kotlin/com/intellij/serviceContainer/LazyExtensionInstance
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/PluginAware
instanceKlass org/jetbrains/kotlin/com/intellij/util/KeyedLazyInstance
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/BulkVirtualFileListenerAdapter
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/impl/BaseBusConnection
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/impl/MessageBusImpl$MessageHandlerHolder
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/newvfs/CachingVirtualFileSystem
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass java/util/concurrent/atomic/AtomicLongFieldUpdater$CASUpdater$1
instanceKlass java/util/concurrent/atomic/AtomicLongFieldUpdater
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/newvfs/BulkFileListener
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/Topic
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileManager
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/EmptyRunnable
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointAdapter
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointAndAreaListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPointListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/impl/ExtensionComponentAdapter
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/LoadingOrder$Orderable
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/PluginId
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/DefaultPluginDescriptor
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileManagerListener
instanceKlass java/util/EventObject
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/ex/DocumentEx
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/Document
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileEditor/FileDocumentManager
instanceKlass org/jetbrains/kotlin/cli/jvm/modules/CoreJrtFileSystem$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentFactoryMap
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ArchiveHandler
instanceKlass org/jetbrains/kotlin/com/intellij/util/Function
instanceKlass org/jetbrains/kotlin/com/intellij/util/EventDispatcher
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/BaseExtensionPointName
instanceKlass java/util/EventListener
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/CachedSingletonsRegistry
instanceKlass org/jetbrains/kotlin/com/intellij/util/ArrayUtil
instanceKlass org/jetbrains/kotlin/com/intellij/util/ArrayFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/Extensions
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/ObjectNode
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Disposer$CheckedDisposableImpl
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/CheckedDisposable
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/impl/ImplementationClassResolver
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/impl/ExtensionPointImpl
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPoint
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/impl/ExtensionsAreaImpl
instanceKlass org/jetbrains/kotlin/com/intellij/util/pico/DefaultPicoContainer$InstanceComponentAdapter
instanceKlass org/jetbrains/kotlin/com/intellij/util/pico/DefaultPicoContainer$LinkedHashSetWrapper
instanceKlass org/jetbrains/kotlin/org/picocontainer/ComponentAdapter
instanceKlass org/jetbrains/kotlin/com/intellij/util/ArrayUtilRt
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/impl/MessageQueue
instanceKlass java/util/concurrent/Executor
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/impl/MessageBusImpl$MessagePublisher
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/MessageBusConnection
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/SimpleMessageBusConnection
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/impl/MessageBusImpl
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/impl/MessageBusEx
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/MessageBusFactory
instanceKlass org/jetbrains/kotlin/com/intellij/util/keyFMap/DebugFMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/keyFMap/EmptyFMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/keyFMap/KeyFMap
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/ModalityState
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionsArea
instanceKlass org/jetbrains/kotlin/com/intellij/util/pico/DefaultPicoContainer
instanceKlass org/jetbrains/kotlin/org/picocontainer/MutablePicoContainer
instanceKlass org/jetbrains/kotlin/org/picocontainer/PicoContainer
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/MessageBus
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileTypes/UnknownFileType
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/FastUtilCaseInsensitiveStringHashingStrategy
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/FastUtilHashingStrategies$1
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/FastUtilHashingStrategies$SerializableHashStrategy
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/FastUtilCharSequenceHashingStrategy
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/FastUtilHashingStrategies
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/SystemInfoRt
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Getter
instanceKlass org/jetbrains/kotlin/com/intellij/ide/plugins/DisabledPluginsState
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreApplicationEnvironment$Companion
instanceKlass org/jetbrains/kotlin/com/intellij/lang/ParserDefinition
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileTypes/FileType
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/PluginDescriptor
instanceKlass org/jetbrains/kotlin/com/intellij/concurrency/JobLauncher
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/UserDataHolderEx
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/MessageBusOwner
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/ex/ApplicationEx
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/ProgressIndicatorProvider
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/SimpleModificationTracker
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VirtualFileSystem
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/KeyedExtensionCollector
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/ModificationTracker
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/fileTypes/FileTypeRegistry
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/Application
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/components/ComponentManager
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/UserDataHolder
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/AreaInstance
instanceKlass org/jetbrains/kotlin/com/intellij/core/CoreApplicationEnvironment
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Disposer$2
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment$Companion
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment
instanceKlass org/jetbrains/kotlin/cli/jvm/config/JvmContentRootsKt
instanceKlass org/jetbrains/kotlin/cli/jvm/config/JvmClasspathRoot
instanceKlass org/jetbrains/kotlin/cli/jvm/config/JvmContentRoot
instanceKlass org/jetbrains/kotlin/cli/jvm/config/JvmClasspathRootBase
instanceKlass org/jetbrains/kotlin/cli/jvm/config/JvmContentRootBase
instanceKlass org/jetbrains/kotlin/cli/common/config/KotlinSourceRoot
instanceKlass org/jetbrains/kotlin/cli/common/config/ContentRoot
instanceKlass org/jetbrains/kotlin/cli/common/config/ContentRootsKt
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/KotlinToJVMBytecodeCompilerKt
instanceKlass org/jetbrains/kotlin/cli/common/modules/ModuleChunk
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/CliCompilerUtilsKt
instanceKlass org/jetbrains/kotlin/cli/common/modules/ModuleBuilder
instanceKlass org/jetbrains/kotlin/modules/Module
instanceKlass org/jetbrains/kotlin/cli/jvm/K2JVMCompilerKt
instanceKlass org/jetbrains/kotlin/config/JvmSerializeIrMode$Companion
instanceKlass org/jetbrains/kotlin/config/JvmAbiStability$Companion
instanceKlass org/jetbrains/kotlin/compiler/plugin/CommandLineProcessor
instanceKlass org/jetbrains/kotlin/compiler/plugin/CompilerPluginRegistrar$Companion
instanceKlass org/jetbrains/kotlin/compiler/plugin/CompilerPluginRegistrar
instanceKlass org/jetbrains/kotlin/compiler/plugin/ComponentRegistrar$Companion
instanceKlass org/jetbrains/kotlin/compiler/plugin/ComponentRegistrar
instanceKlass org/jetbrains/kotlin/util/ServiceLoaderLite
instanceKlass org/jetbrains/kotlin/cli/common/CLICompilerKt
instanceKlass org/jetbrains/kotlin/cli/jvm/plugins/PluginCliParser
instanceKlass org/jetbrains/kotlin/cli/plugins/PluginsOptionsParserKt
instanceKlass kotlin/collections/ArrayAsCollection
instanceKlass org/jetbrains/kotlin/backend/common/phaser/PhaseConfig
instanceKlass org/jetbrains/kotlin/backend/common/phaser/PhaseConfigurationService
instanceKlass org/jetbrains/kotlin/backend/common/phaser/PhaseConfigKt
instanceKlass org/jetbrains/kotlin/cli/common/CreatePhaseConfigKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ReflectiveAccessKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FragmentLocalFunctionPatchLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ResolveInlineCallsKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/GenerateMultifileFacadesKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FileClassLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ScriptLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/SerializeIrPhaseKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ProcessOptionalAnnotationsKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FragmentSharedVariablesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ExternalPackageParentPatcherLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FakeLocalVariablesForIrInlinerLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FakeLocalVariablesForBytecodeInlinerLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/RenameFieldsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ReplaceNumberToCharCallSitesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/AddSuperQualifierToJavaFieldAccessLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/MakePropertyDelegateMethodsStaticLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ReplaceKFunctionInvokeWithFunctionInvokeKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/TypeOperatorLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/RecordEnclosingMethodsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/RepeatedAnnotationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/AdditionalClassAnnotationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmOptimizationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmSafeCallChainFoldingLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ToArrayLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmArgumentNullabilityAssertionsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/BridgeLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FunctionNVarargBridgeLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/UniqueLoopLabelsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/EnumExternalEntriesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/EnumClassLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/AddContinuationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/TailCallOptimizationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/InheritedDefaultMethodsOnClassesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmStringConcatenationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/common/lower/FlattenStringConcatenationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmDefaultConstructorLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/StaticCallableReferenceLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/SingletonReferencesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/MappedEnumWhenLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmInlineClassLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmMultiFieldValueClassLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmSingleAbstractMethodLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/CollectionStubMethodLoweringKt
instanceKlass org/jetbrains/kotlin/backend/common/lower/loops/ForLoopsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/common/lower/RangeContainsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmBuiltInsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/AnonymousObjectSuperConstructorLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/MoveCompanionObjectFieldsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/PropertyReferenceLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/SingletonOrConstantDelegationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/PropertyReferenceDelegationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/SuspendLambdaLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/RemoveDuplicatedInlinedLocalClassesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmInventNamesForLocalClassesKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmLateinitLoweringKt
instanceKlass org/jetbrains/kotlin/builtins/PrimitiveType$Companion
instanceKlass sun/reflect/misc/ReflectUtil
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl$1
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater
instanceKlass kotlin/SafePublicationLazyImpl$Companion
instanceKlass kotlin/SafePublicationLazyImpl
instanceKlass kotlin/LazyKt__LazyJVMKt$WhenMappings
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/VarargLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/PolymorphicSignatureLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmAnnotationImplementationTransformerKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/AnnotationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/MainMethodGenerationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmOverloadsAnnotationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/TypeAliasAnnotationMethodsLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/CreateSeparateCallForInlinedLambdasLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/MarkNecessaryInlinedClassesAsRegeneratedLoweringKt
instanceKlass org/jetbrains/kotlin/ir/declarations/IrTypeParametersContainer
instanceKlass org/jetbrains/kotlin/ir/declarations/IrReturnTarget
instanceKlass org/jetbrains/kotlin/ir/declarations/IrDeclarationParent
instanceKlass org/jetbrains/kotlin/ir/declarations/IrPossiblyExternalDeclaration
instanceKlass org/jetbrains/kotlin/ir/declarations/IrOverridableDeclaration
instanceKlass org/jetbrains/kotlin/ir/declarations/IrOverridableMember
instanceKlass org/jetbrains/kotlin/ir/declarations/IrDeclarationWithVisibility
instanceKlass org/jetbrains/kotlin/ir/declarations/IrMetadataSourceOwner
instanceKlass org/jetbrains/kotlin/ir/declarations/IrMemberWithContainerSource
instanceKlass org/jetbrains/kotlin/ir/declarations/IrDeclarationWithName
instanceKlass org/jetbrains/kotlin/ir/declarations/IrAttributeContainer
instanceKlass org/jetbrains/kotlin/ir/IrElementBase
instanceKlass org/jetbrains/kotlin/ir/declarations/IrDeclaration
instanceKlass org/jetbrains/kotlin/ir/declarations/IrSymbolOwner
instanceKlass org/jetbrains/kotlin/ir/declarations/IrMutableAnnotationContainer
instanceKlass org/jetbrains/kotlin/ir/declarations/IrAnnotationContainer
instanceKlass org/jetbrains/kotlin/ir/IrStatement
instanceKlass org/jetbrains/kotlin/ir/IrElement
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/JvmStaticAnnotationLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/StaticDefaultFunctionLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/ObjectClassLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/DirectInvokeLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/AssertionLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/InlineCallableReferenceToLambdaKt
instanceKlass org/jetbrains/kotlin/backend/common/lower/SharedVariablesLoweringKt
instanceKlass org/jetbrains/kotlin/backend/jvm/lower/FunctionReferenceLoweringKt
instanceKlass org/jetbrains/kotlin/backend/common/lower/PropertiesLowering$Companion
instanceKlass org/jetbrains/kotlin/backend/common/lower/PropertiesLowering
instanceKlass org/jetbrains/kotlin/backend/common/DeclarationTransformer
instanceKlass org/jetbrains/kotlin/backend/common/FileLoweringPass
instanceKlass org/jetbrains/kotlin/backend/common/phaser/ModuleLoweringPhaseAdapter
instanceKlass org/jetbrains/kotlin/backend/common/phaser/CustomPhaseAdapter
instanceKlass org/jetbrains/kotlin/backend/jvm/JvmLowerKt
instanceKlass org/jetbrains/kotlin/backend/common/phaser/CompositePhase
instanceKlass org/jetbrains/kotlin/backend/common/phaser/PerformByIrFilePhase
instanceKlass org/jetbrains/kotlin/backend/common/phaser/PerformByIrFileKt
instanceKlass org/jetbrains/kotlin/backend/common/phaser/FileLoweringPhaseAdapter
instanceKlass org/jetbrains/kotlin/backend/common/phaser/CompilerPhaseKt
instanceKlass org/jetbrains/kotlin/backend/common/phaser/DumperVerifierKt
instanceKlass kotlin/jvm/functions/Function3
instanceKlass org/jetbrains/kotlin/backend/common/phaser/PhaseBuildersKt
instanceKlass kotlin/collections/SetsKt__SetsJVMKt
instanceKlass org/jetbrains/kotlin/backend/jvm/JvmPhasesKt$generateAdditionalClassesPhase$1
instanceKlass org/jetbrains/kotlin/backend/common/phaser/SameTypeCompilerPhase
instanceKlass org/jetbrains/kotlin/backend/common/phaser/AbstractNamedCompilerPhase
instanceKlass org/jetbrains/kotlin/backend/common/phaser/CompilerPhase
instanceKlass org/jetbrains/kotlin/backend/jvm/JvmPhasesKt
instanceKlass org/jetbrains/kotlin/cli/common/environment/UtilKt
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Disposer$1
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/CanonicalHashingStrategy
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentRefHashMap$2
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentRefHashMap$HardKey
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentRefHashMap$KeyReference
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/Object2ObjectMap$FastEntrySet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/AbstractObject2ObjectFunction
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/Object2ObjectMap
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/Object2ObjectFunction
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/RefHashMap$HardKey
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/RefHashMap$Key
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/IdentityHashingStrategy
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/HashingStrategy
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/Hash$Strategy
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/CollectionFactory
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/Reference2ObjectMap$FastEntrySet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/AbstractReference2ObjectFunction
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/Reference2ObjectMap
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/Reference2ObjectFunction
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass java/util/stream/Collectors
instanceKlass java/util/stream/Collector
instanceKlass java/util/function/BinaryOperator
instanceKlass java/util/function/BiConsumer
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectSpliterator
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectIterator
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ReferenceSet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ReferenceCollection
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/ObjectTree
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Disposer
instanceKlass org/jetbrains/kotlin/progress/ProgressIndicatorAndCompilationCanceledStatus
instanceKlass org/jetbrains/kotlin/progress/CompilationCanceledStatus
instanceKlass org/jetbrains/kotlin/com/google/common/collect/LinkedHashMultimap$1
instanceKlass java/util/stream/MatchOps$BooleanTerminalSink
instanceKlass java/util/stream/MatchOps$MatchOp
instanceKlass java/util/stream/MatchOps
instanceKlass java/util/Spliterators$IteratorSpliterator
instanceKlass org/jetbrains/kotlin/utils/KotlinPathsFromBaseDirectory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/PathManager
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass org/jetbrains/kotlin/utils/PathUtil
instanceKlass kotlin/collections/AbstractList$IteratorImpl
instanceKlass org/jetbrains/kotlin/config/JvmTarget$Companion
instanceKlass org/jetbrains/kotlin/platform/TargetPlatformVersion
instanceKlass org/jetbrains/kotlin/config/JVMConfigurationKeys
instanceKlass org/jetbrains/kotlin/cli/jvm/JvmArgumentsKt
instanceKlass org/jetbrains/kotlin/config/CommonConfigurationKeysKt
instanceKlass kotlin/Unit
instanceKlass org/jetbrains/kotlin/utils/WrappedValues$ThrowableWrapper
instanceKlass org/jetbrains/kotlin/config/LanguageVersionSettingsKt
instanceKlass org/jetbrains/kotlin/cli/common/messages/MessageCollector$Companion$NONE$1
instanceKlass org/jetbrains/kotlin/cli/common/messages/MessageCollector$Companion
instanceKlass org/jetbrains/kotlin/cli/common/arguments/JavaTypeEnhancementStateParser$Companion
instanceKlass org/jetbrains/kotlin/cli/common/arguments/JavaTypeEnhancementStateParser
instanceKlass org/jetbrains/kotlin/config/JvmAnalysisFlags$Delegates$JvmDefaultModeDisabledByDefault
instanceKlass org/jetbrains/kotlin/utils/WrappedValues$1
instanceKlass org/jetbrains/kotlin/utils/WrappedValues
instanceKlass kotlin/collections/EmptyIterator
instanceKlass org/jetbrains/kotlin/name/FqNamesUtilKt
instanceKlass org/jetbrains/kotlin/load/java/NullabilityAnnotationStates$Companion
instanceKlass org/jetbrains/kotlin/load/java/Jsr305Settings
instanceKlass org/jetbrains/kotlin/storage/LockBasedStorageManager$MapBasedMemoizedFunction
instanceKlass org/jetbrains/kotlin/storage/DefaultSimpleLock
instanceKlass org/jetbrains/kotlin/storage/SimpleLock$Companion
instanceKlass org/jetbrains/kotlin/storage/EmptySimpleLock
instanceKlass org/jetbrains/kotlin/storage/LockBasedStorageManager$ExceptionHandlingStrategy$1
instanceKlass org/jetbrains/kotlin/storage/LockBasedStorageManager$ExceptionHandlingStrategy
instanceKlass org/jetbrains/kotlin/storage/CacheWithNotNullValues
instanceKlass org/jetbrains/kotlin/storage/MemoizedFunctionToNotNull
instanceKlass org/jetbrains/kotlin/storage/NotNullLazyValue
instanceKlass org/jetbrains/kotlin/storage/CacheWithNullableValues
instanceKlass org/jetbrains/kotlin/storage/MemoizedFunctionToNullable
instanceKlass org/jetbrains/kotlin/storage/NullableLazyValue
instanceKlass org/jetbrains/kotlin/storage/SimpleLock
instanceKlass org/jetbrains/kotlin/storage/LockBasedStorageManager
instanceKlass org/jetbrains/kotlin/storage/StorageManager
instanceKlass kotlin/TuplesKt
instanceKlass kotlin/internal/ProgressionUtilKt
instanceKlass kotlin/ranges/IntRange$Companion
instanceKlass kotlin/ranges/IntProgression$Companion
instanceKlass kotlin/ranges/IntProgression
instanceKlass kotlin/ranges/OpenEndRange
instanceKlass kotlin/ranges/ClosedRange
instanceKlass kotlin/KotlinVersionCurrentValue
instanceKlass kotlin/KotlinVersion$Companion
instanceKlass kotlin/KotlinVersion
instanceKlass org/jetbrains/kotlin/load/java/ReportLevel$Companion
instanceKlass org/jetbrains/kotlin/load/java/JavaNullabilityAnnotationsStatus$Companion
instanceKlass org/jetbrains/kotlin/load/java/JavaNullabilityAnnotationsStatus
instanceKlass kotlin/Pair
instanceKlass org/jetbrains/kotlin/load/java/NullabilityAnnotationStatesImpl
instanceKlass org/jetbrains/kotlin/load/java/NullabilityAnnotationStates
instanceKlass org/jetbrains/kotlin/load/java/JavaNullabilityAnnotationSettingsKt
instanceKlass org/jetbrains/kotlin/load/java/JavaTypeEnhancementState$Companion
instanceKlass org/jetbrains/kotlin/load/java/JavaTypeEnhancementState
instanceKlass org/jetbrains/kotlin/config/JvmAnalysisFlags$Delegates$JavaTypeEnhancementStateWarnByDefault
instanceKlass org/jetbrains/kotlin/config/JvmAnalysisFlags
instanceKlass org/jetbrains/kotlin/config/AnalysisFlag$Delegates$ApiModeDisabledByDefault
instanceKlass org/jetbrains/kotlin/config/AnalysisFlag$Delegates$ListOfStrings
instanceKlass org/jetbrains/kotlin/config/AnalysisFlag
instanceKlass org/jetbrains/kotlin/config/AnalysisFlag$Delegate
instanceKlass kotlin/properties/ReadOnlyProperty
instanceKlass org/jetbrains/kotlin/config/AnalysisFlag$Delegates$Boolean
instanceKlass kotlin/reflect/KProperty$Getter
instanceKlass kotlin/reflect/KProperty$Accessor
instanceKlass org/jetbrains/kotlin/config/AnalysisFlags
instanceKlass org/jetbrains/kotlin/config/LanguageVersionSettingsImpl$Companion
instanceKlass org/jetbrains/kotlin/config/LanguageVersionSettings$Companion
instanceKlass org/jetbrains/kotlin/config/LanguageVersionSettingsImpl
instanceKlass java/math/MutableBigInteger
instanceKlass org/jetbrains/kotlin/config/MavenComparableVersion$IntegerItem
instanceKlass org/jetbrains/kotlin/config/MavenComparableVersion$Item
instanceKlass org/jetbrains/kotlin/config/MavenComparableVersion
instanceKlass org/jetbrains/kotlin/config/ApiVersion$Companion
instanceKlass org/jetbrains/kotlin/config/ApiVersion
instanceKlass org/jetbrains/kotlin/config/LanguageVersion$Companion
instanceKlass org/jetbrains/kotlin/config/IncrementalCompilation
instanceKlass org/jetbrains/kotlin/cli/common/UtilsKt
instanceKlass org/jetbrains/kotlin/config/CommonConfigurationKeys
instanceKlass org/jetbrains/kotlin/cli/common/messages/IrMessageCollector$Companion
instanceKlass org/jetbrains/kotlin/cli/common/messages/IrMessageCollector
instanceKlass org/jetbrains/kotlin/ir/util/IrMessageLogger$Companion
instanceKlass org/jetbrains/kotlin/ir/util/IrMessageLogger
instanceKlass org/jetbrains/kotlin/com/google/common/collect/AbstractMapEntry
instanceKlass org/jetbrains/kotlin/com/google/common/collect/LinkedHashMultimap$ValueSetLink
instanceKlass org/jetbrains/kotlin/com/google/common/collect/CollectPreconditions
instanceKlass org/jetbrains/kotlin/com/google/common/base/Preconditions
instanceKlass org/jetbrains/kotlin/com/google/common/collect/ImmutableMap
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Maps$EntryTransformer
instanceKlass org/jetbrains/kotlin/com/google/common/collect/BiMap
instanceKlass org/jetbrains/kotlin/com/google/common/base/Converter
instanceKlass org/jetbrains/kotlin/com/google/common/base/Function
instanceKlass org/jetbrains/kotlin/com/google/common/collect/UnmodifiableIterator
instanceKlass org/jetbrains/kotlin/com/google/common/collect/SortedMapDifference
instanceKlass org/jetbrains/kotlin/com/google/common/collect/MapDifference
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Maps
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper
instanceKlass java/util/logging/LogManager$4
instanceKlass jdk/internal/logger/BootstrapLogger$BootstrapExecutors
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass java/util/ServiceLoader$1
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass java/util/Collections$EmptyIterator
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass java/lang/System$Logger
instanceKlass java/util/stream/Streams
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/stream/Streams$AbstractStreamBuilderImpl
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/stream/FindOps
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass java/util/Spliterator
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/Properties$EntrySet
instanceKlass java/util/Collections$3
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$VisitedLoggers
instanceKlass java/util/function/Predicate
instanceKlass java/util/logging/LogManager$2
instanceKlass java/lang/System$LoggerFinder
instanceKlass java/security/Security$2
instanceKlass jdk/internal/access/JavaSecurityPropertiesAccess
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/util/Properties$LineReader
instanceKlass java/security/Security$1
instanceKlass java/security/Security
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/util/logging/LogManager$LoggingProviderAccess
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/LogManager
instanceKlass java/util/logging/Logger$ConfigurationData
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Platform
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Multiset
instanceKlass org/jetbrains/kotlin/com/google/common/collect/AbstractMultimap
instanceKlass org/jetbrains/kotlin/com/google/common/collect/SetMultimap
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Multimap
instanceKlass org/jetbrains/kotlin/cli/common/messages/GroupingMessageCollector
instanceKlass java/lang/StrictMath
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/HashCommon
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/Int2ObjectMap$FastEntrySet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectSet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntSet
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntCollection
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/IntIterable
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectCollection
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/ObjectIterable
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/AbstractInt2ObjectFunction
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/Hash
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/Int2ObjectMap
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/Int2ObjectFunction
instanceKlass java/util/function/IntFunction
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/Function
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/IntKeyWeakValueHashMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentList
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentLongObjectMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentIntObjectMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/IntObjectMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ContainerUtil
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/Key
instanceKlass org/jetbrains/kotlin/config/CompilerConfigurationKey
instanceKlass org/jetbrains/kotlin/cli/common/CLIConfigurationKeys
instanceKlass org/jetbrains/kotlin/utils/SmartList$EmptyIterator
instanceKlass org/jetbrains/kotlin/cli/common/ArgumentsKt
instanceKlass java/net/URLConnection
instanceKlass org/jetbrains/kotlin/cli/common/messages/PrintingMessageCollector
instanceKlass kotlin/jvm/internal/CollectionToArray
instanceKlass kotlin/reflect/jvm/internal/pcollections/MapEntry
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties$Val$1
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties$Val
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties
instanceKlass java/util/regex/CharPredicates
instanceKlass kotlin/text/Regex$Companion
instanceKlass kotlin/text/Regex
instanceKlass kotlin/jvm/internal/DefaultConstructorMarker
instanceKlass kotlin/reflect/jvm/internal/KDeclarationContainerImpl$Companion
instanceKlass kotlin/reflect/jvm/internal/KTypeParameterOwnerImpl
instanceKlass kotlin/reflect/jvm/internal/KClassifierImpl
instanceKlass kotlin/reflect/jvm/internal/pcollections/ConsPStack
instanceKlass kotlin/reflect/jvm/internal/pcollections/IntTree
instanceKlass kotlin/reflect/jvm/internal/pcollections/IntTreePMap
instanceKlass kotlin/reflect/jvm/internal/pcollections/HashPMap
instanceKlass kotlin/reflect/jvm/internal/KClassCacheKt
instanceKlass kotlin/reflect/jvm/internal/KDeclarationContainerImpl
instanceKlass kotlin/jvm/internal/ClassBasedDeclarationContainer
instanceKlass kotlin/reflect/KMutableProperty0
instanceKlass kotlin/reflect/KMutableProperty1
instanceKlass kotlin/reflect/KTypeParameter
instanceKlass kotlin/reflect/KMutableProperty2
instanceKlass kotlin/reflect/KMutableProperty
instanceKlass kotlin/reflect/KProperty2
instanceKlass kotlin/reflect/KProperty1
instanceKlass kotlin/reflect/KType
instanceKlass kotlin/reflect/KClass
instanceKlass kotlin/reflect/KProperty0
instanceKlass kotlin/reflect/KProperty
instanceKlass kotlin/jvm/internal/ReflectionFactory
instanceKlass kotlin/reflect/KClassifier
instanceKlass kotlin/jvm/internal/Reflection
instanceKlass kotlin/jvm/JvmClassMappingKt
instanceKlass jdk/internal/reflect/ClassDefiner$1
instanceKlass jdk/internal/reflect/ClassDefiner
instanceKlass jdk/internal/reflect/MethodAccessorGenerator$1
instanceKlass jdk/internal/reflect/Label$PatchInfo
instanceKlass jdk/internal/reflect/Label
instanceKlass jdk/internal/reflect/UTF8
instanceKlass jdk/internal/reflect/ClassFileAssembler
instanceKlass jdk/internal/reflect/ByteVectorImpl
instanceKlass jdk/internal/reflect/ByteVector
instanceKlass jdk/internal/reflect/ByteVectorFactory
instanceKlass jdk/internal/reflect/AccessorGenerator
instanceKlass jdk/internal/reflect/ClassFileConstants
instanceKlass org/jetbrains/kotlin/config/LanguageVersionSettings
instanceKlass org/jetbrains/kotlin/config/LanguageOrApiVersion
instanceKlass org/jetbrains/kotlin/utils/DescriptionAware
instanceKlass org/jetbrains/kotlin/cli/common/messages/MessageCollector
instanceKlass kotlin/collections/builders/MapBuilder$EntryRef
instanceKlass kotlin/jvm/internal/markers/KMutableMap$Entry
instanceKlass kotlin/collections/builders/MapBuilder$Itr
instanceKlass kotlin/jvm/internal/markers/KMutableIterator
instanceKlass org/jetbrains/kotlin/cli/common/arguments/ArgumentField
instanceKlass org/jetbrains/kotlin/util/capitalizeDecapitalize/CapitalizeDecapitalizeKt
instanceKlass org/jetbrains/kotlin/name/ClassId
instanceKlass org/jetbrains/kotlin/name/FqNameUnsafe$1
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass org/jetbrains/kotlin/name/Name
instanceKlass org/jetbrains/kotlin/name/FqNameUnsafe
instanceKlass org/jetbrains/kotlin/name/FqName
instanceKlass org/jetbrains/kotlin/load/java/JvmAbi
instanceKlass kotlin/Metadata
instanceKlass java/lang/annotation/Target
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass jdk/internal/org/objectweb/asm/Edge
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass java/util/StringJoiner
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass java/util/function/Consumer
instanceKlass jdk/internal/module/Checks
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass java/lang/PublicMethods
instanceKlass java/util/Collections$1
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass java/lang/reflect/Proxy
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass kotlin/annotation/Target
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass kotlin/jvm/internal/markers/KMutableSet
instanceKlass kotlin/jvm/internal/markers/KMutableCollection
instanceKlass kotlin/jvm/internal/markers/KMutableIterable
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass org/jetbrains/kotlin/cli/common/arguments/Argument
instanceKlass kotlin/collections/EmptySet
instanceKlass kotlin/collections/EmptyMap
instanceKlass kotlin/ranges/RangesKt__RangesKt
instanceKlass kotlin/collections/builders/ListBuilderKt
instanceKlass kotlin/collections/builders/MapBuilder$Companion
instanceKlass kotlin/collections/builders/MapBuilder
instanceKlass kotlin/jvm/internal/markers/KMutableMap
instanceKlass kotlin/collections/MapsKt__MapWithDefaultKt
instanceKlass java/io/FileInputStream$1
instanceKlass kotlin/io/CloseableKt
instanceKlass kotlin/text/CharsKt__CharJVMKt
instanceKlass java/lang/Character$CharacterCache
instanceKlass kotlin/sequences/GeneratorSequence$iterator$1
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/util/function/BiFunction
instanceKlass java/lang/invoke/VarHandle$1
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/VarHandles
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass kotlin/sequences/ConstrainedOnceSequence
instanceKlass kotlin/sequences/GeneratorSequence
instanceKlass kotlin/sequences/Sequence
instanceKlass kotlin/sequences/SequencesKt__SequenceBuilderKt
instanceKlass java/io/Reader
instanceKlass kotlin/text/Charsets
instanceKlass org/jetbrains/kotlin/cli/common/arguments/ArgumentParseErrors
instanceKlass org/jetbrains/kotlin/cli/common/arguments/PreprocessCommandLineArgumentsKt
instanceKlass kotlin/UNINITIALIZED_VALUE
instanceKlass kotlin/SynchronizedLazyImpl
instanceKlass kotlin/Lazy
instanceKlass kotlin/LazyKt__LazyJVMKt
instanceKlass kotlin/jvm/internal/Lambda
instanceKlass kotlin/jvm/functions/Function0
instanceKlass org/jetbrains/kotlin/cli/common/arguments/ParseCommandLineArgumentsKt
instanceKlass kotlin/collections/ArraysUtilJVM
instanceKlass kotlin/collections/ArraysKt__ArraysJVMKt
instanceKlass java/lang/ProcessEnvironment$CheckedEntry
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet$1
instanceKlass java/lang/ProcessEnvironment$EntryComparator
instanceKlass java/lang/ProcessEnvironment$NameComparator
instanceKlass org/jetbrains/kotlin/config/JvmDefaultMode$Companion
instanceKlass org/jetbrains/kotlin/config/JVMAssertionsMode$Companion
instanceKlass org/jetbrains/kotlin/config/ExplicitApiMode$Companion
instanceKlass kotlin/collections/EmptyList
instanceKlass kotlin/collections/CollectionsKt__CollectionsJVMKt
instanceKlass org/jetbrains/kotlin/cli/common/arguments/K2JVMCompilerArguments$Companion
instanceKlass org/jetbrains/kotlin/cli/common/arguments/CommonCompilerArguments$Companion
instanceKlass org/jetbrains/kotlin/cli/common/arguments/CommonToolArguments$Companion
instanceKlass org/jetbrains/kotlin/config/Services$Builder
instanceKlass org/jetbrains/kotlin/config/Services$Companion
instanceKlass org/jetbrains/kotlin/cli/common/messages/XcodeStyleMessageRenderer
instanceKlass org/jetbrains/kotlin/cli/common/messages/GradleStyleMessageRenderer
instanceKlass java/lang/Class$3
instanceKlass kotlin/text/StringsKt__AppendableKt
instanceKlass org/jetbrains/kotlin/cli/common/PropertiesKt
instanceKlass org/jetbrains/kotlin/cli/common/messages/PlainTextMessageRenderer
instanceKlass org/jetbrains/kotlin/cli/common/messages/XmlMessageRenderer
instanceKlass org/jetbrains/kotlin/cli/common/messages/MessageRenderer
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/application/ApplicationManager
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/util/ArrayList$Itr
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass sun/invoke/util/Wrapper$1
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass java/lang/invoke/InnerClassLambdaMetafactory$1
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass jdk/internal/org/objectweb/asm/ClassReader
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Attachment
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$2
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/invoke/Invokers
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet
instanceKlass java/lang/invoke/LambdaMetafactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger$DefaultFactory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger$Factory
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/Logger
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/IdeaStandaloneExecutionSetup
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/CompatKt
instanceKlass kotlin/jvm/functions/Function2
instanceKlass kotlin/jvm/internal/CallableReference$NoReceiver
instanceKlass kotlin/reflect/KDeclarationContainer
instanceKlass kotlin/jvm/internal/CallableReference
instanceKlass kotlin/reflect/KFunction
instanceKlass kotlin/reflect/KCallable
instanceKlass kotlin/reflect/KAnnotatedElement
instanceKlass kotlin/jvm/internal/FunctionBase
instanceKlass kotlin/jvm/functions/Function1
instanceKlass kotlin/Function
instanceKlass org/jetbrains/kotlin/cli/common/CompilerSystemProperties$Companion
instanceKlass kotlin/collections/AbstractList$Companion
instanceKlass kotlin/collections/AbstractCollection
instanceKlass kotlin/enums/EnumEntries
instanceKlass kotlin/jvm/internal/markers/KMappedMarker
instanceKlass kotlin/enums/EnumEntriesKt
instanceKlass org/jetbrains/kotlin/util/PerformanceCounter$Companion
instanceKlass org/jetbrains/kotlin/util/PerformanceCounter
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass java/nio/file/Paths
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass kotlin/jvm/internal/Intrinsics
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass java/util/TreeMap$Entry
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass org/jetbrains/kotlin/cli/jvm/K2JVMCompiler$Companion
instanceKlass org/jetbrains/kotlin/cli/common/CLICompiler$Companion
instanceKlass org/jetbrains/kotlin/cli/common/CLITool$Companion
instanceKlass java/lang/Void
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass org/jetbrains/kotlin/utils/KotlinPaths
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/Disposable
instanceKlass org/jetbrains/kotlin/config/CompilerConfiguration
instanceKlass org/jetbrains/kotlin/cli/common/CommonCompilerPerformanceManager
instanceKlass org/jetbrains/kotlin/config/Services
instanceKlass org/jetbrains/kotlin/cli/common/arguments/Freezable
instanceKlass org/jetbrains/kotlin/utils/exceptions/KotlinExceptionWithAttachments
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/ExceptionWithAttachments
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/ControlFlowException
instanceKlass org/jetbrains/kotlin/cli/common/CLITool
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass jdk/internal/loader/Resource
instanceKlass sun/security/action/GetIntegerAction
instanceKlass sun/security/util/Debug
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass jdk/internal/util/jar/JarIndex
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryImpl
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/net/URI$Parser
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/Arrays$ArrayItr
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass sun/security/action/GetBooleanAction
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/net/util/URLUtil
instanceKlass sun/launcher/LauncherHelper
instanceKlass jdk/internal/vm/PostVMInitHook$1
instanceKlass jdk/internal/util/EnvUtils
instanceKlass jdk/internal/vm/PostVMInitHook$2
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass jdk/internal/vm/PostVMInitHook
instanceKlass java/lang/reflect/Array
instanceKlass java/lang/invoke/StringConcatFactory$3
instanceKlass java/lang/invoke/StringConcatFactory$2
instanceKlass java/lang/invoke/StringConcatFactory$1
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/ModuleLayer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass java/util/function/Function
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/util/AbstractMap$1$1
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/lang/module/Configuration
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass jdk/internal/util/Preconditions
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass java/util/HexFormat
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/lang/Enum
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$default
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass java/util/Collections
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/charset/CoderResult
instanceKlass java/lang/Readable
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass sun/nio/cs/GBK$EncodeHolder
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/io/Writer
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/lang/Integer$IntegerCache
instanceKlass java/lang/CharacterData
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass java/lang/StringCoding
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass java/lang/StringUTF16
instanceKlass sun/nio/cs/DoubleByte
instanceKlass sun/nio/cs/GBK$DecodeHolder
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass sun/nio/cs/DelegatableDecoder
instanceKlass java/lang/reflect/Modifier
instanceKlass java/lang/Class$1
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass java/util/Arrays
instanceKlass java/lang/ThreadLocal
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass jdk/internal/misc/VM
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass java/lang/ref/ReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/Runtime
instanceKlass java/util/HashMap$Node
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/Map$Entry
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/Math
instanceKlass jdk/internal/reflect/Reflection
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/StringLatin1
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/invoke/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Map
instanceKlass java/util/Dictionary
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 92 7 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 3 8 1 100 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Class 1 1 1611 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 7 1 8 1 10 12 1 11 100 12 1 1 1 10 12 1 1 11 8 1 18 8 1 10 12 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 7 1 100 1 10 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 100 1 100 1 10 10 12 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 1 10 10 10 12 1 1 10 12 1 1 10 12 10 12 1 1 100 1 8 1 10 10 12 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 100 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 11 100 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 10 12 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 10 12 100 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 8 1 10 12 1 11 11 12 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 7 1 10 12 1 1 7 11 7 1 9 12 1 1 9 12 1 100 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 10 10 12 1 1 10 12 10 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 10 7 8 1 18 8 1 8 1 10 12 1 9 12 1 9 12 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 7 1 10 10 12 1 10 7 1 9 12 1 8 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 100 1 8 1 10 7 1 4 10 10 12 11 7 12 1 1 1 10 12 1 100 1 10 12 1 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 11 12 7 1 11 7 12 1 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 9 12 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 7 1 11 12 1 10 7 12 1 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 1 10 12 1 10 11 12 1 11 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 100 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 18 12 1 1 11 12 1 1 18 11 12 1 18 12 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 8 1 10 12 1 7 1 9 12 1 1 100 1 100 1 100 1 100 1 100 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 11 12 16 1 16 15 16 15 10 12 16 16 15 10 12 16 15 16 1 15 10 12 16 1 1 1 1 1 1 1 1 100 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/io/Serializable 1 0 7 100 1 100 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 487 100 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 1 100 1 10 12 1 1 11 100 12 1 1 11 100 12 1 1 100 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 100 1 10 12 1 1 11 100 12 1 1 100 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 1 100 1 9 12 1 1 10 100 12 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 0 0 196 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 10 100 12 1 1 100 1 9 12 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/util/Iterator 1 1 53 100 1 8 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/resolve/calls/model/ArgumentMatchStatus
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/model/ConstraintKind
instanceKlass org/jetbrains/kotlin/resolve/calls/components/ArgumentsToParametersMapper$CallArgumentProcessor$State
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/WrongResolutionToClassifier
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/DataFlowValue$Kind
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/Nullability
instanceKlass org/jetbrains/kotlin/types/TypeCheckerState$LowerCapturedTypePolicy
instanceKlass org/jetbrains/kotlin/resolve/jvm/jvmSignature/JvmMethodParameterKind
instanceKlass org/jetbrains/kotlin/codegen/OwnerKind
instanceKlass org/jetbrains/kotlin/builtins/UnsignedArrayType
instanceKlass org/jetbrains/kotlin/types/TypeSubstitutor$VarianceConflictType
instanceKlass org/jetbrains/kotlin/load/java/descriptors/JavaMethodDescriptor$ParameterNamesStatus
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/TypeComponentPosition
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/NullabilityQualifier
instanceKlass org/jetbrains/kotlin/name/State
instanceKlass org/jetbrains/kotlin/load/java/lazy/types/JavaTypeFlexibility
instanceKlass org/jetbrains/kotlin/types/TypeUsage
instanceKlass org/jetbrains/kotlin/descriptors/annotations/KotlinTarget
instanceKlass org/jetbrains/kotlin/load/java/SpecialGenericSignatures$TypeSafeBarrierDescription
instanceKlass org/jetbrains/kotlin/resolve/ExternalOverridabilityCondition$Result
instanceKlass org/jetbrains/kotlin/resolve/ExternalOverridabilityCondition$Contract
instanceKlass org/jetbrains/kotlin/resolve/OverridingUtil$OverrideCompatibilityInfo$Result
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryClassSignatureParser$JavaSignatureVariance
instanceKlass javaslang/collection/HashArrayMappedTrieModule$Action
instanceKlass org/jetbrains/kotlin/resolve/calls/results/OverloadResolutionResults$Code
instanceKlass org/jetbrains/kotlin/resolve/calls/tower/CandidateApplicability
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/model/NewConstraintSystemImpl$State
instanceKlass org/jetbrains/kotlin/resolve/calls/model/KotlinCallKind
instanceKlass org/jetbrains/kotlin/resolve/calls/tasks/ExplicitReceiverKind
instanceKlass org/jetbrains/kotlin/psi/Call$CallType
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/jvm/JvmPrimitiveType
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/PrimitiveType
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/functions/FunctionClassKind
instanceKlass org/jetbrains/kotlin/load/java/structure/LightClassOriginKind
instanceKlass org/jetbrains/kotlin/resolve/lazy/LazyImportScope$FilteringKind
instanceKlass org/jetbrains/kotlin/resolve/jvm/JvmPrimitiveType
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Effect$InvocationKind
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Expression$ConstantValue
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Effect$EffectType
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$VersionRequirement$VersionKind
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$VersionRequirement$Level
instanceKlass org/jetbrains/kotlin/serialization/deserialization/descriptors/DeserializedContainerAbiStability
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmProtoBuf$StringTableTypes$Record$Operation
instanceKlass java/lang/annotation/ElementType
instanceKlass org/jetbrains/kotlin/resolve/jvm/NotFoundPackagesCachingStrategy$CacheType
instanceKlass org/jetbrains/kotlin/resolve/scopes/LexicalScopeKind
instanceKlass org/jetbrains/kotlin/parsing/KotlinParsing$DeclarationParsingMode
instanceKlass org/jetbrains/kotlin/parsing/KotlinParsing$NameParsingMode
instanceKlass org/jetbrains/kotlin/parsing/KotlinParsing$AnnotationParsingMode
instanceKlass org/jetbrains/kotlin/parsing/KotlinExpressionParsing$Precedence
instanceKlass org/jetbrains/kotlin/load/kotlin/header/KotlinClassHeader$Kind
instanceKlass org/jetbrains/kotlin/load/java/AnnotationQualifierApplicabilityType
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$MemberKind
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Class$Kind
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Modality
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Visibility
instanceKlass org/jetbrains/kotlin/incremental/components/NoLookupLocation
instanceKlass org/jetbrains/kotlin/serialization/deserialization/AnnotatedCallableKind
instanceKlass org/jetbrains/kotlin/load/java/typeEnhancement/MutabilityQualifier
instanceKlass org/jetbrains/kotlin/resolve/lazy/FileScopeFactory$FilteringKind
instanceKlass org/jetbrains/kotlin/types/expressions/ControlStructureTypingUtils$ResolveConstruct
instanceKlass org/jetbrains/kotlin/types/expressions/FakeCallKind
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/TypeVariableDirectionCalculator$ResolveDirection
instanceKlass org/jetbrains/kotlin/resolve/calls/inference/components/ConstraintSystemCompletionMode
instanceKlass org/jetbrains/kotlin/resolve/calls/context/CheckArgumentTypesMode
instanceKlass org/jetbrains/kotlin/resolve/calls/results/ResolutionStatus
instanceKlass org/jetbrains/kotlin/resolve/calls/util/ResolveArgumentsMode
instanceKlass org/jetbrains/kotlin/resolve/calls/smartcasts/SmartCastManager$ReceiverSmartCastResult
instanceKlass org/jetbrains/kotlin/types/expressions/CoercionStrategy
instanceKlass org/jetbrains/kotlin/resolve/calls/context/ContextDependency
instanceKlass org/jetbrains/kotlin/resolve/QualifierPosition
instanceKlass org/jetbrains/kotlin/psi/KtProjectionKind
instanceKlass org/jetbrains/kotlin/resolve/TopDownAnalysisMode
instanceKlass org/jetbrains/kotlin/incremental/components/ScopeKind
instanceKlass org/jetbrains/kotlin/types/model/CaptureStatus
instanceKlass org/jetbrains/kotlin/types/model/TypeVariance
instanceKlass org/jetbrains/kotlin/analyzer/ModuleInfo$DependencyOnBuiltIns
instanceKlass org/jetbrains/kotlin/types/Variance
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionDescriptor$Os
instanceKlass org/jetbrains/kotlin/com/intellij/diagnostic/ActivityCategory
instanceKlass kotlin/DeprecationLevel
instanceKlass org/jetbrains/kotlin/container/ComponentState
instanceKlass org/jetbrains/kotlin/container/ComponentStorageState
instanceKlass java/math/RoundingMode
instanceKlass org/jetbrains/kotlin/descriptors/annotations/AnnotationUseSiteTarget
instanceKlass org/jetbrains/kotlin/config/LanguageFeature$State
instanceKlass org/jetbrains/kotlin/config/LanguageFeature$Kind
instanceKlass org/jetbrains/kotlin/config/LanguageFeature
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$QualifiedNameTable$QualifiedName$Kind
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$TypeParameter$Variance
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Annotation$Argument$Value$Type
instanceKlass org/jetbrains/kotlin/protobuf/WireFormat$JavaType
instanceKlass org/jetbrains/kotlin/protobuf/WireFormat$FieldType
instanceKlass org/jetbrains/kotlin/types/error/ErrorScopeKind
instanceKlass org/jetbrains/kotlin/descriptors/CallableMemberDescriptor$Kind
instanceKlass java/util/Locale$Category
instanceKlass org/jetbrains/kotlin/types/error/ErrorEntity
instanceKlass org/jetbrains/kotlin/types/error/ErrorTypeKind
instanceKlass org/jetbrains/kotlin/descriptors/ClassKind
instanceKlass org/jetbrains/kotlin/descriptors/Modality
instanceKlass org/jetbrains/kotlin/renderer/AnnotationArgumentsRenderingPolicy
instanceKlass org/jetbrains/kotlin/renderer/PropertyAccessorRenderingPolicy
instanceKlass org/jetbrains/kotlin/renderer/ParameterNameRenderingPolicy
instanceKlass org/jetbrains/kotlin/renderer/RenderingFormat
instanceKlass org/jetbrains/kotlin/renderer/OverrideRenderingPolicy
instanceKlass org/jetbrains/kotlin/renderer/DescriptorRendererModifier
instanceKlass org/jetbrains/kotlin/builtins/jvm/JvmBuiltIns$Kind
instanceKlass org/jetbrains/kotlin/diagnostics/Severity
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/CharsetToolkit$GuessedEncoding
instanceKlass org/jetbrains/kotlin/cli/jvm/index/JavaRoot$RootType
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/io/FileAttributes$CaseSensitivity
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/VFileProperty
instanceKlass kotlin/collections/State
instanceKlass kotlin/io/FileWalkDirection
instanceKlass org/jetbrains/kotlin/com/intellij/pom/java/LanguageLevel
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/LowMemoryWatcher$LowMemoryWatcherType
instanceKlass org/jetbrains/kotlin/com/intellij/diagnostic/LoadingState
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/impl/CoreProgressManager$CheckCanceledBehavior
instanceKlass org/jetbrains/kotlin/com/intellij/util/messages/Topic$BroadcastDirection
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionPoint$Kind
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/EnvironmentConfigFiles
instanceKlass org/jetbrains/kotlin/config/JvmSerializeIrMode
instanceKlass org/jetbrains/kotlin/config/JvmAbiStability
instanceKlass org/jetbrains/kotlin/builtins/UnsignedType
instanceKlass kotlin/LazyThreadSafetyMode
instanceKlass org/jetbrains/kotlin/builtins/PrimitiveType
instanceKlass java/util/stream/Collector$Characteristics
instanceKlass java/util/stream/MatchOps$MatchKind
instanceKlass org/jetbrains/kotlin/config/JvmTarget
instanceKlass org/jetbrains/kotlin/storage/LockBasedStorageManager$NotValue
instanceKlass org/jetbrains/kotlin/load/java/ReportLevel
instanceKlass jdk/internal/logger/BootstrapLogger$LoggingBackend
instanceKlass java/util/stream/StreamShape
instanceKlass java/util/stream/StreamOpFlag$Type
instanceKlass java/util/stream/StreamOpFlag
instanceKlass java/util/regex/Pattern$Qtype
instanceKlass org/jetbrains/kotlin/config/LanguageVersion
instanceKlass java/lang/annotation/RetentionPolicy
instanceKlass org/jetbrains/kotlin/config/JvmDefaultMode
instanceKlass org/jetbrains/kotlin/config/JVMAssertionsMode
instanceKlass org/jetbrains/kotlin/config/ExplicitApiMode
instanceKlass org/jetbrains/kotlin/cli/common/messages/CompilerMessageSeverity
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassOption
instanceKlass java/lang/invoke/VarHandle$AccessType
instanceKlass java/lang/invoke/VarHandle$AccessMode
instanceKlass java/lang/invoke/MethodHandleImpl$Intrinsic
instanceKlass java/lang/invoke/LambdaForm$BasicType
instanceKlass java/lang/invoke/LambdaForm$Kind
instanceKlass sun/invoke/util/Wrapper
instanceKlass org/jetbrains/kotlin/cli/common/CompilerSystemProperties
instanceKlass org/jetbrains/kotlin/cli/common/ExitCode
instanceKlass java/util/concurrent/TimeUnit
instanceKlass java/nio/file/LinkOption
instanceKlass sun/nio/fs/WindowsPathType
instanceKlass java/nio/file/StandardOpenOption
instanceKlass java/io/File$PathStatus
instanceKlass java/lang/module/ModuleDescriptor$Requires$Modifier
instanceKlass java/lang/module/ModuleDescriptor$Modifier
ciInstanceKlass java/lang/Enum 1 1 188 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 100 1 10 10 7 12 1 1 10 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 1 100 1 8 1 10 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 1 1 100 1 100 1 1
ciInstanceKlass java/lang/System 1 1 803 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 100 1 10 12 1 8 1 10 12 1 10 12 1 1 100 1 10 12 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 8 1 10 10 12 1 100 1 8 1 10 8 1 10 7 12 1 1 8 1 10 12 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 18 12 1 100 1 9 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 100 1 8 1 10 9 12 1 9 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 8 1 11 12 1 10 12 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 8 1 7 1 9 7 12 1 1 1 10 12 1 7 1 9 12 10 9 12 7 1 10 12 8 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 10 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 8 1 10 8 1 8 1 8 1 8 1 10 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 1 8 1 10 10 10 12 1 1 10 12 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 7 1 10 10 12 1 10 12 1 9 12 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 1 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/BufferedInputStream
staticfield java/lang/System out Ljava/io/PrintStream; java/io/PrintStream
staticfield java/lang/System err Ljava/io/PrintStream; java/io/PrintStream
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1098 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 100 12 1 10 7 1 10 7 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 100 1 10 12 1 10 100 12 1 1 1 10 10 12 1 1 10 12 1 1 100 1 8 1 10 8 1 10 12 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 1 8 1 8 1 10 7 12 1 1 100 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 12 1 10 7 1 10 12 1 100 1 18 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 8 1 100 1 10 10 12 1 9 12 1 10 7 12 1 1 10 12 1 100 1 8 1 10 12 1 10 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 100 1 100 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 7 1 18 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 18 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 12 1 10 11 12 1 1 10 18 10 12 1 1 11 100 12 1 18 12 1 11 12 1 1 10 12 10 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 8 1 8 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 100 1 10 11 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 9 12 1 1 9 12 9 12 1 9 12 1 9 12 1 8 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 11 12 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 1 15 10 12 16 1 16 15 10 12 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 1 1 100 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 1 1 18 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1
instanceKlass java/net/URLClassLoader
instanceKlass jdk/internal/loader/BuiltinClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 102 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 7 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 7 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 737 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 100 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 7 1 10 12 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 9 12 1 1 10 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 100 12 1 1 100 1 10 7 12 1 1 1 10 12 1 100 1 8 1 10 12 1 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 11 12 1 7 1 10 11 12 1 1 11 10 12 1 1 7 1 10 12 1 10 7 12 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 11 12 1 100 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 18 12 1 1 10 12 1 10 12 1 1 18 100 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 10 12 1 11 12 1 100 1 10 12 1 7 1 100 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 8 1 10 10 12 1 8 1 8 1 10 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 1 10 12 1 7 1 10 11 12 1 1 10 12 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 10 12 16 15 10 12 16 1 1 1 100 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/security/AccessController 1 1 295 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 10 11 7 12 1 1 1 10 7 12 1 1 11 7 1 100 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 100 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 100 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor16
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor15
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor14
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor13
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor12
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor11
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor10
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor9
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor8
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor7
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor6
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor5
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor4
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor3
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor2
instanceKlass jdk/internal/reflect/BootstrapConstructorAccessorImpl
instanceKlass jdk/internal/reflect/GeneratedConstructorAccessor1
instanceKlass jdk/internal/reflect/DelegatingConstructorAccessorImpl
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DelegatingMethodAccessorImpl
instanceKlass jdk/internal/reflect/NativeMethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 25 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1
ciInstanceKlass java/lang/Module 1 1 959 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 9 12 1 1 11 12 1 9 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 1 10 12 1 1 11 12 1 9 12 1 11 12 10 100 12 1 1 100 1 8 1 10 7 1 11 12 1 1 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 11 12 1 1 9 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 10 12 10 7 12 1 1 10 7 12 1 1 10 7 1 18 12 1 1 11 100 12 1 1 1 18 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 4 7 1 11 12 1 7 1 7 1 10 10 7 12 1 1 1 10 11 7 12 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 11 12 1 1 10 12 10 12 1 1 9 12 1 100 1 10 10 12 1 1 11 100 1 10 12 1 1 11 12 1 10 10 12 1 11 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 18 12 1 11 12 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 10 12 1 10 12 1 9 12 1 7 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 18 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 100 1 8 1 100 1 10 100 1 100 1 3 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 1 10 100 12 1 1 8 1 10 12 1 8 1 10 12 1 10 12 10 12 1 8 1 10 10 100 12 1 1 7 1 10 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 11 12 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 7 12 1 1 1 16 15 10 12 16 16 15 10 12 16 16 15 10 16 1 15 10 12 16 1 15 10 12 16 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/NotNullList
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/Stack
instanceKlass org/jetbrains/kotlin/config/MavenComparableVersion$ListItem
ciInstanceKlass java/util/ArrayList 1 1 492 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 11 7 12 1 1 1 9 12 1 1 10 12 1 1 7 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 11 100 12 1 1 1 11 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 11 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 100 1 8 1 10 7 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1210 7 1 7 1 3 10 12 1 1 3 100 1 10 7 12 1 1 1 100 1 10 100 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 4 10 12 1 9 12 1 10 12 1 1 100 1 10 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 7 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 9 10 12 1 1 9 12 1 10 12 1 1 5 0 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 7 1 10 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 11 100 1 10 12 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 9 10 12 1 9 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 100 1 10 12 11 100 12 1 1 10 11 7 12 1 10 12 1 100 1 10 12 1 100 1 10 10 9 100 12 1 1 1 10 12 3 10 100 12 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 9 12 1 9 7 12 1 1 10 12 1 1 10 12 1 3 9 12 1 9 12 1 10 12 1 1 7 1 9 3 9 12 1 100 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 100 12 1 1 1 100 10 12 1 100 1 5 0 10 100 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 100 1 100 1 10 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 8 10 12 1 1 8 8 8 8 7 10 12 1 1 10 12 1 100 1 8 1 10 7 1 100 1 100 1 1 1 5 0 1 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 16
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
ciInstanceKlass java/lang/String 1 1 1396 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 9 7 12 1 1 3 10 7 12 1 1 1 7 1 11 12 1 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 10 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 100 1 10 12 1 1 10 12 1 1 10 12 1 100 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 11 10 12 1 10 12 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 3 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 10 100 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 11 7 1 11 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 1 10 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 1 10 10 12 1 1 10 12 10 10 12 1 10 12 10 10 12 10 10 12 1 10 12 1 10 12 10 10 12 10 12 1 10 12 10 12 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 100 12 1 1 1 11 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 100 1 100 1 8 1 10 10 10 12 1 8 1 10 12 1 3 3 7 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 11 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 10 12 10 12 1 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 1 10 10 12 1 8 1 10 12 1 1 18 12 1 1 11 100 12 1 1 1 7 1 3 18 12 1 18 12 1 8 1 10 100 12 1 1 1 11 12 1 1 10 12 10 10 12 1 10 11 12 1 1 10 12 1 1 11 12 1 18 3 11 10 12 1 11 11 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 100 12 1 100 1 100 1 10 12 1 100 1 10 10 100 12 1 1 1 100 1 10 7 1 10 10 12 1 10 10 12 1 8 1 10 10 12 1 8 1 8 1 10 12 1 10 12 1 10 10 12 10 100 12 1 1 10 100 12 1 1 10 100 12 1 1 8 1 10 12 1 10 12 1 1 10 10 12 8 1 8 1 10 8 1 8 1 8 1 8 1 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 10 10 12 10 12 7 1 9 12 1 1 7 1 10 100 1 100 1 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 10 12 15 10 12 15 10 12 1 1 1 1 100 1 100 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciInstanceKlass java/security/ProtectionDomain 1 1 324 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 7 1 9 12 1 1 9 12 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 9 100 12 1 1 10 12 1 1 10 100 1 10 12 1 1 8 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 8 1 11 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 100 12 1 1 1 10 100 1 10 12 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 11 100 12 1 1 1 10 12 1 10 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 100 12 1 1 11 12 1 10 12 8 1 8 1 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/CodeSource 1 1 395 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 8 1 8 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 10 12 1 1 10 100 12 1 1 10 12 1 100 1 10 12 10 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 100 1 8 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 10 12 10 12 1 1 11 100 12 1 1 10 10 12 1 11 10 12 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 11 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StringBuilder 1 1 409 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 100 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 183 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 7 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1285 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 5 0 5 0 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 100 1 8 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 100 1 10 10 12 1 1 8 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 1 9 100 1 9 7 1 9 100 1 9 9 100 1 9 100 1 9 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 5 0 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 3 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 5 0 10 12 1 1 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 5 0 5 0 5 0 10 12 1 1 10 12 1 10 12 1 10 12 10 100 12 1 1 8 1 100 1 11 12 1 1 8 1 11 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 12 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
ciInstanceKlass java/util/Map 1 1 259 11 7 12 1 1 1 11 12 1 1 10 100 12 1 1 11 12 1 1 11 7 12 1 1 1 11 100 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 100 1 100 1 10 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 11 12 1 10 12 1 1 11 12 1 11 100 12 1 9 7 12 1 1 1 100 1 10 12 7 1 7 1 10 12 1 7 1 10 7 1 11 12 1 1 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadGroup 1 1 293 10 7 12 1 1 1 9 7 12 1 1 1 8 1 9 12 1 1 7 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 9 12 1 9 12 1 1 10 7 12 1 1 1 100 10 12 1 1 10 7 12 1 1 1 10 100 12 1 9 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 100 1 10 10 12 1 10 12 1 10 12 1 7 10 12 1 9 12 1 1 10 12 1 1 8 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 1 100 1 9 12 1 100 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 100 1 8 1 10 8 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Properties 1 1 651 10 7 12 1 1 1 100 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 7 1 10 12 10 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 3 10 10 100 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 9 100 12 1 1 1 10 12 1 10 12 1 1 100 1 10 10 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 10 12 1 1 8 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 100 12 1 1 9 100 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 1 100 1 10 10 12 1 11 7 12 1 1 10 7 12 1 1 1 8 1 10 100 12 1 1 11 8 1 10 100 1 11 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 100 1 10 11 100 12 1 1 4 11 10 12 1 1 10 100 12 1 1 11 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 100 1 6 0 10 12 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 512 100 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 7 1 9 12 1 1 4 10 7 12 1 1 1 9 12 1 4 10 12 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 100 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 3 9 12 1 9 12 1 3 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 9 12 1 1 10 100 1 100 1 10 12 1 10 8 1 10 10 12 1 8 1 10 8 1 10 100 12 1 1 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 11 12 1 1 11 12 1 100 1 10 10 10 100 12 1 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 8 1 10 4 10 12 4 10 12 1 8 1 10 12 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Set 1 1 140 100 1 10 100 12 1 1 1 9 7 12 1 1 1 7 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 7 1 7 1 7 1 10 12 1 10 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Map$Entry 1 0 178 18 12 1 1 100 1 100 1 18 10 100 12 1 1 1 18 12 1 18 100 1 11 7 12 1 1 1 11 12 1 11 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 10 12 1 8 10 100 1 10 12 1 8 10 12 1 8 1 10 12 1 8 10 12 1 8 1 10 12 1 1 8 1 100 1 8 1 10 12 1 1 11 12 100 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 11 12 16 3 3 15 11 12 15 11 12 15 11 12 1 1 100 1 100 1 1
instanceKlass org/jetbrains/kotlin/protobuf/RopeByteString$RopeInputStream
instanceKlass org/jetbrains/kotlin/com/intellij/util/io/UnsyncByteArrayInputStream
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/in/MergedStream
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 184 100 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 100 1 3 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 3 100 1 8 1 10 10 100 12 1 1 1 100 1 10 11 100 12 1 1 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 100 1 10 100 12 1 1 1 5 0 10 12 1 10 12 1 1 100 1 10 8 1 10 8 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 96 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
ciInstanceKlass java/lang/Thread 1 1 610 9 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 1 3 8 1 100 1 5 0 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 100 1 8 1 10 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 10 7 12 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 9 12 1 10 12 1 1 9 12 1 100 1 10 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 100 1 11 7 12 1 1 9 100 12 1 1 1 10 12 1 10 12 1 10 12 9 12 1 1 10 9 12 1 10 12 1 100 1 10 10 12 1 1 9 12 1 10 12 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 1 10 12 1 100 1 8 1 10 10 12 1 10 12 8 1 10 12 1 8 1 10 8 1 8 1 10 100 12 1 1 10 100 12 1 1 1 100 1 8 1 10 9 12 1 9 12 1 1 10 12 1 1 10 10 12 1 1 9 12 1 10 12 1 1 100 1 10 12 11 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 100 1 10 12 1 10 12 1 1 11 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 9 12 1 10 12 1 1 11 100 12 1 1 1 10 100 12 1 1 1 11 12 1 10 12 1 7 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
instanceKlass org/jetbrains/kotlin/load/java/structure/impl/classFiles/BinaryClassSignatureParser$ClsFormatException
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 393 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 10 10 12 1 100 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 8 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 10 12 1 100 1 10 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 8 1 8 1 9 12 1 1 10 100 12 1 1 100 1 10 11 12 1 8 1 8 1 10 7 12 1 1 8 1 10 12 1 8 1 100 1 10 12 1 9 12 1 1 10 12 1 10 100 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 8 1 10 12 1 1 8 1 10 10 9 100 12 1 1 1 8 1 10 12 1 1 10 100 1 8 1 10 11 12 1 1 8 1 9 12 1 10 100 12 1 1 11 9 12 1 1 11 12 1 1 100 10 12 1 10 12 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
instanceKlass org/jetbrains/kotlin/types/TypeSubstitutor$SubstitutionException
instanceKlass org/jetbrains/kotlin/com/intellij/util/AstLoadingException
instanceKlass org/jetbrains/kotlin/container/UnresolvedDependenciesException
instanceKlass org/jetbrains/kotlin/container/InvalidCardinalityException
instanceKlass org/jetbrains/kotlin/container/ContainerConsistencyException
instanceKlass kotlin/reflect/full/IllegalPropertyDelegateAccessException
instanceKlass kotlin/reflect/full/IllegalCallableAccessException
instanceKlass org/jetbrains/kotlin/com/intellij/psi/scope/MethodProcessorSetupFailedException
instanceKlass javax/xml/stream/XMLStreamException
instanceKlass java/text/ParseException
instanceKlass org/jetbrains/kotlin/com/intellij/util/cls/ClsFormatException
instanceKlass java/net/URISyntaxException
instanceKlass java/lang/CloneNotSupportedException
instanceKlass java/lang/InterruptedException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/InstantiationException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/lexer/KotlinLexerException
instanceKlass org/jetbrains/kotlin/com/intellij/psi/text/BlockSupport$ReparsedSuccessfullyException
instanceKlass org/jetbrains/kotlin/com/intellij/psi/impl/DebugUtil$IncorrectTreeStructureException
instanceKlass org/jetbrains/kotlin/types/error/LazyWrappedTypeComputationException
instanceKlass org/jetbrains/kotlin/protobuf/UninitializedMessageException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/RecursionManager$CachingPreventedException
instanceKlass org/jetbrains/kotlin/com/intellij/core/CoreJavaCodeStyleManager$1CancelException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/util/StackOverflowPreventedException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/InvalidVirtualFileAccessException
instanceKlass org/jetbrains/kotlin/com/intellij/psi/PsiInvalidElementAccessException
instanceKlass org/jetbrains/kotlin/com/fasterxml/aalto/UncheckedStreamException
instanceKlass org/jetbrains/kotlin/com/intellij/diagnostic/PluginException
instanceKlass java/io/UncheckedIOException
instanceKlass java/util/MissingResourceException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/SortingException
instanceKlass org/jetbrains/kotlin/com/intellij/diagnostic/ImplementationConflictException
instanceKlass java/util/EmptyStackException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/diagnostic/RuntimeExceptionWithAttachments
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/project/IndexNotReadyException
instanceKlass java/lang/SecurityException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/ReadOnlyModificationException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/editor/ReadOnlyFragmentModificationException
instanceKlass org/jetbrains/kotlin/com/intellij/util/lang/CompoundRuntimeException
instanceKlass org/jetbrains/kotlin/org/picocontainer/PicoException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/extensions/ExtensionNotApplicableException
instanceKlass java/nio/file/FileSystemNotFoundException
instanceKlass org/jetbrains/kotlin/util/ServiceLoaderLite$ServiceLoadingException
instanceKlass org/jetbrains/kotlin/compiler/plugin/PluginProcessingException
instanceKlass org/jetbrains/kotlin/compiler/plugin/CliOptionProcessingException
instanceKlass org/jetbrains/kotlin/backend/common/CompilationException
instanceKlass org/jetbrains/kotlin/com/intellij/util/IncorrectOperationException
instanceKlass org/jetbrains/kotlin/utils/WrappedValues$WrappedProcessCanceledException
instanceKlass java/util/ConcurrentModificationException
instanceKlass java/lang/reflect/UndeclaredThrowableException
instanceKlass java/util/NoSuchElementException
instanceKlass kotlin/NoWhenBranchMatchedException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass kotlin/UninitializedPropertyAccessException
instanceKlass java/lang/IllegalStateException
instanceKlass org/jetbrains/kotlin/cli/jvm/compiler/CompileEnvironmentException
instanceKlass org/jetbrains/kotlin/utils/KotlinExceptionWithAttachments
instanceKlass org/jetbrains/kotlin/analyzer/CompilationErrorException
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/progress/ProcessCanceledException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/io/IOError
instanceKlass kotlin/reflect/jvm/internal/KotlinReflectionInternalError
instanceKlass kotlin/NotImplementedError
instanceKlass kotlin/jvm/KotlinReflectionNotSupportedError
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
instanceKlass java/lang/ThreadDeath
ciInstanceKlass java/lang/Error 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/UnsatisfiedLinkError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 7 12 1 1 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadDeath 0 0 21 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1
ciInstanceKlass java/util/Collections 1 1 851 10 7 12 1 1 1 11 7 12 1 1 1 7 1 11 12 1 1 7 1 10 12 1 1 10 12 1 11 12 1 1 100 1 11 12 1 1 11 12 1 1 10 12 1 11 100 12 1 1 11 12 1 1 11 12 1 10 12 1 10 12 1 10 12 11 100 12 1 1 1 10 12 1 1 11 12 1 11 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 100 1 8 1 10 12 1 11 7 12 1 1 1 11 100 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 7 1 10 12 1 100 1 10 12 1 100 1 10 12 1 7 1 7 1 10 12 10 7 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 10 12 1 7 1 10 10 12 1 100 1 10 100 1 10 7 1 10 100 1 10 10 12 1 10 7 1 10 100 1 10 100 1 10 100 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 9 7 12 1 1 1 9 100 12 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 7 1 10 100 1 10 7 1 10 7 1 10 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 100 1 10 12 1 9 100 12 1 1 1 9 100 12 1 1 1 100 1 9 12 1 1 10 12 7 1 10 100 1 10 11 100 12 1 1 11 12 1 10 12 1 100 1 11 11 12 1 11 7 1 10 100 1 10 100 12 1 1 1 100 1 10 12 1 7 1 10 7 1 10 7 1 10 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/Collections EMPTY_SET Ljava/util/Set; java/util/Collections$EmptySet
staticfield java/util/Collections EMPTY_LIST Ljava/util/List; java/util/Collections$EmptyList
staticfield java/util/Collections EMPTY_MAP Ljava/util/Map; java/util/Collections$EmptyMap
ciInstanceKlass java/lang/StackTraceElement 0 0 224 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 100 12 1 1 1 100 1 9 12 1 8 1 9 12 1 9 12 1 9 12 1 1 8 1 10 12 1 1 10 12 1 100 1 10 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 100 12 1 1 10 10 12 1 1 10 12 1 10 12 1 1 100 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/ints/AbstractIntCollection
instanceKlass org/jetbrains/kotlin/com/google/common/collect/ImmutableCollection
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/AbstractObjectCollection
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/UnsafeWeakList
instanceKlass java/util/AbstractQueue
instanceKlass org/jetbrains/kotlin/it/unimi/dsi/fastutil/objects/AbstractReferenceCollection
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Multimaps$Entries
instanceKlass java/util/IdentityHashMap$Values
instanceKlass org/jetbrains/kotlin/com/google/common/collect/AbstractMapBasedMultimap$WrappedCollection
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ImmutableList
instanceKlass java/util/HashMap$Values
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 160 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 11 12 1 11 7 1 10 12 1 10 12 1 10 7 12 1 1 1 11 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 151 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 9 12 1 1 9 12 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 9 100 12 1 1 9 12 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 32 10 7 12 1 1 1 9 7 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 0 0 132 10 100 12 1 1 1 9 100 12 1 1 1 9 100 1 9 12 1 1 11 100 12 1 1 1 9 12 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 11 12 1 11 12 1 1 11 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 100 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 10 100 1 10 12 1 100 1 10 12 1 100 1 100 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/Character 1 1 576 7 1 100 1 100 1 9 12 1 1 8 1 9 12 1 1 100 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 3 3 3 3 3 10 12 1 1 10 12 1 3 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 3 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 5 0 10 12 1 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 10 12 1 9 12 1 1 100 1 10 10 12 1 10 12 1 1 3 10 100 12 1 1 1 10 12 1 10 100 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 10 12 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
ciInstanceKlass java/lang/Float 1 1 223 7 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 4 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 4 4 4 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Double 1 1 285 7 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 100 12 1 1 1 6 0 8 1 10 12 1 1 8 1 10 12 1 1 8 1 6 0 10 12 1 1 100 1 5 0 5 0 8 1 8 1 10 100 12 1 1 1 10 100 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 6 0 6 0 6 0 10 7 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 215 7 1 100 1 10 100 12 1 1 1 9 12 1 1 8 1 9 12 1 1 100 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 100 1 100 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 224 7 1 100 1 100 1 10 100 12 1 1 1 10 12 1 1 100 1 100 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 8 1 9 12 1 1 100 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 100 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 3 3 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Integer 1 1 445 7 1 100 1 7 1 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 3 10 12 1 1 3 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 1 100 1 10 11 10 12 1 1 8 1 10 12 1 1 8 1 100 1 10 12 1 1 10 12 1 1 5 0 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 9 12 1 1 9 12 1 1 10 12 1 10 7 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 5 0 3 3 3 3 10 12 1 3 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 3 3 3 3 3 3 9 12 1 1 100 1 100 1 100 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
staticfield java/lang/Integer sizeTable [I 10
ciInstanceKlass java/lang/Long 1 1 506 7 1 100 1 7 1 100 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 10 12 1 10 12 1 10 12 1 5 0 5 0 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 5 0 5 0 9 12 1 1 9 12 1 5 0 100 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 5 0 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 1 100 1 10 11 10 12 1 1 8 1 10 12 1 1 8 1 100 1 10 12 1 1 10 12 1 8 1 8 1 11 12 1 1 10 12 1 10 12 1 10 12 1 5 0 5 0 9 7 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 5 0 10 12 1 10 12 1 5 0 5 0 5 0 10 12 1 1 5 0 5 0 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 195 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 100 1 100 1 10 12 1 9 12 1 9 12 1 100 1 10 10 12 1 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentSoftValueHashMap$MySoftReference
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentWeakKeySoftValueHashMap$SoftValue
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/SoftKeySoftValueHashMap$ValueReference
instanceKlass org/jetbrains/kotlin/com/intellij/reference/SoftReference
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentWeakKeySoftValueHashMap$WeakKey
instanceKlass org/jetbrains/kotlin/com/intellij/util/PatchedWeakReference
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentWeakValueHashMap$MyWeakReference
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/UnsafeWeakList$MyReference
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/IntKeyWeakValueHashMap$MyReference
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 7 12 1 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 47 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 152 9 7 12 1 1 1 10 100 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 12 1 100 1 11 100 12 1 1 100 1 10 12 1 100 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 10 12 1 10 7 12 1 1 1 7 1 10 7 1 10 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 39 10 100 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 398 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 7 12 1 1 1 11 12 1 100 1 10 12 1 7 1 100 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 7 1 10 10 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 100 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 11 100 1 100 1 8 1 10 10 12 1 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 10 100 1 8 1 10 11 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 7 1 10 12 1 10 12 1 1 10 100 1 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 8 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 9 12 1 100 1 10 7 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 7 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 548 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 100 1 8 1 8 1 10 12 1 100 1 8 1 10 12 1 8 1 11 100 12 1 1 1 100 1 10 12 1 1 11 12 1 8 1 18 8 1 10 12 1 10 12 1 1 18 8 1 10 12 1 100 1 10 12 1 10 12 1 11 100 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 100 1 10 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 3 100 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 8 1 8 1 8 1 9 12 1 10 12 1 100 1 8 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 100 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 10 10 10 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 9 12 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 15 10 100 12 1 1 1 16 15 16 1 16 1 15 10 12 16 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 429 10 7 12 1 1 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 100 1 8 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 100 12 1 1 10 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Method 1 1 446 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 8 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 11 100 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 7 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Field 1 1 437 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 1 10 7 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 10 12 1 8 1 8 1 10 11 100 1 9 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 11 7 1 10 12 1 7 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Parameter 0 0 226 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 10 100 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 100 1 10 11 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass java/lang/StringBuffer 0 0 470 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 1 10 10 100 12 1 1 1 10 10 12 1 10 8 10 100 12 1 1 1 8 10 12 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 9 100 12 1 1 1 9 100 1 9 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 547 7 1 7 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 3 3 10 12 1 10 12 1 1 11 7 1 100 1 100 1 10 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 1 100 1 10 12 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 12 1 1 10 12 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 10 12 1 10 8 1 8 1 8 1 10 10 100 1 10 12 1 100 1 10 100 1 10 100 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 15 10 100 12 1 1 1 16 1 15 10 12 16 15 10 12 1 1 1 1 100 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciInstanceKlass java/lang/SecurityManager 0 0 576 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 1 10 100 1 10 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 100 1 8 1 10 9 12 1 1 9 12 1 8 1 9 12 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 1 10 12 1 1 8 1 100 1 8 1 10 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 8 1 100 1 8 1 8 1 10 8 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 11 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 18 11 12 1 18 12 1 11 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 18 12 1 18 10 100 12 1 1 1 18 12 1 10 12 1 18 18 8 1 10 12 1 9 12 1 1 11 100 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 8 1 100 1 10 9 12 1 8 1 10 12 1 8 1 100 1 10 10 100 12 1 1 10 100 1 9 100 12 1 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 100 12 1 1 1 16 1 16 15 10 12 16 1 15 10 12 16 15 11 100 1 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 1 16 1 15 11 12 1 15 10 12 16 15 10 16 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/security/AccessControlContext 1 1 373 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 12 1 11 12 1 11 12 1 1 100 1 11 12 1 1 10 12 1 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 7 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 10 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1
ciInstanceKlass java/net/URL 1 1 743 10 7 12 1 1 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 100 1 10 10 12 1 1 8 1 10 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 1 8 1 9 12 1 10 12 1 1 8 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 8 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 10 7 12 1 1 1 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 10 12 1 100 1 10 12 1 10 12 1 1 8 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 10 12 1 9 12 1 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 10 7 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 10 12 1 100 1 10 9 12 1 1 10 7 12 1 1 8 1 10 12 1 1 100 1 10 10 100 12 1 1 1 8 9 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 10 8 8 10 12 1 8 8 8 100 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 100 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 10 10 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 100 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/util/jar/Manifest 1 1 336 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 100 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 12 1 1 100 1 10 12 1 8 1 11 12 1 100 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 100 12 1 1 1 8 1 10 12 1 1 10 9 100 12 1 1 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 9 100 12 1 1 1 8 1 10 12 1 8 1 8 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 8 1 10 10 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 11 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 10 12 1 11 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Objects 1 1 151 10 7 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 1 100 1 10 10 12 1 8 1 10 100 12 1 1 1 8 1 100 1 11 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Collections$SingletonMap
instanceKlass kotlin/collections/AbstractMutableMap
instanceKlass org/jetbrains/kotlin/protobuf/SmallSortedMap
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/impl/ZipEntryMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/hash/LinkedHashMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ConcurrentRefHashMap
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/RefHashMap
instanceKlass java/util/EnumMap
instanceKlass java/util/TreeMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/HashMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 192 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 10 12 1 1 11 12 1 100 1 10 11 12 1 11 7 1 10 12 1 1 11 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 100 1 10 11 11 12 1 1 11 12 1 100 1 100 1 11 12 1 8 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1
ciInstanceKlass java/util/Collection 1 1 115 11 100 12 1 1 1 100 1 11 7 12 1 1 1 10 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 10 100 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/List 1 1 217 10 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 100 1 10 100 12 1 1 1 9 7 12 1 1 1 7 1 10 12 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
instanceKlass kotlin/collections/AbstractMutableList
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Lists$AbstractListWrapper
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Lists$Partition
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Lists$TransformingRandomAccessList
instanceKlass org/jetbrains/kotlin/com/intellij/lang/impl/PsiBuilderImpl$RelativeTokenTypesView
instanceKlass java/util/AbstractSequentialList
instanceKlass org/jetbrains/kotlin/protobuf/UnmodifiableLazyStringList
instanceKlass org/jetbrains/kotlin/protobuf/LazyStringArrayList
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/ContainerUtilRt$EmptyList
instanceKlass java/util/ArrayList$SubList
instanceKlass org/jetbrains/kotlin/com/intellij/util/containers/DisposableWrapperList
instanceKlass org/jetbrains/kotlin/com/intellij/util/SmartList
instanceKlass java/util/AbstractList$SubList
instanceKlass java/util/Vector
instanceKlass java/util/Collections$SingletonList
instanceKlass org/jetbrains/kotlin/utils/SmartList
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 11 12 1 1 11 12 1 10 100 12 1 1 1 10 12 1 11 12 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 100 1 11 7 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 7 1 10 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 8 1 8 1 8 1 10 100 1 11 10 10 12 1 11 12 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/util/Preconditions 1 1 170 10 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 100 1 100 1 10 100 12 1 1 1 10 12 1 8 1 100 1 10 100 12 1 1 1 10 12 1 1 8 1 8 1 10 100 12 1 1 7 1 10 12 1 8 1 10 100 12 1 1 1 8 1 10 12 1 1 10 12 1 1 11 12 1 8 1 8 1 11 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/Objects checkIndex (II)I 510 0 31145 0 -1
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/invoke/NativeEntryPoint 0 0 92 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 11 100 12 1 1 1 10 12 1 1 10 12 1 11 100 12 1 1 11 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 302 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 10 12 1 1 100 1 100 1 10 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 10 12 1 1 9 12 1 9 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 10 12 1 1 9 12 1 8 1 100 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 10 12 1 1 9 12 1 1 100 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 100 1 8 1 10 10 12 10 12 1 1 100 1 100 1 100 1 8 1 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 37 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 771 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 100 12 1 1 1 9 7 1 9 7 1 10 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 11 12 1 1 7 7 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 100 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 11 12 1 1 11 12 1 10 100 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 9 12 1 1 7 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 12 1 11 12 1 100 1 11 100 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 10 100 12 1 1 10 12 1 100 10 12 1 1 10 12 1 10 7 1 7 1 9 12 1 1 100 1 100 1 100 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 16 15 10 12 16 1 1 1 1 100 1 1 100 1 1 100 1 100 1 1
staticfield java/lang/invoke/MethodType internTable Ljava/lang/invoke/MethodType$ConcurrentWeakInternSet; java/lang/invoke/MethodType$ConcurrentWeakInternSet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/Record 0 0 22 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 96 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 119 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 42 8 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
instanceKlass kotlin/KotlinNullPointerException
ciInstanceKlass java/lang/NullPointerException 1 1 52 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/InternalError 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 224 100 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 1 100 1 8 1 10 12 1 8 1 8 1 9 12 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 100 1 10 100 1 10 100 1 10 10 100 12 1 1 1 10 11 100 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 644 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 100 12 1 1 1 9 12 1 1 100 1 10 9 100 12 1 1 1 9 100 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 10 100 12 1 1 1 100 1 11 12 1 10 100 1 11 12 1 100 1 10 12 1 11 12 1 9 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 10 12 1 1 9 12 1 11 12 1 9 12 1 9 12 1 9 12 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 10 7 12 1 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 100 12 1 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 7 12 1 1 9 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 8 10 12 1 1 8 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 100 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 0 0 17 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 1 1 126 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 1 8 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1
staticfield jdk/internal/reflect/NativeConstructorAccessorImpl U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/reflect/NativeConstructorAccessorImpl GENERATED_OFFSET J 16
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 940 7 1 7 1 100 1 7 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 7 12 1 1 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 1 9 12 9 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 7 1 9 12 1 1 10 7 12 1 10 12 1 1 10 12 1 100 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 10 12 1 8 1 9 12 1 9 12 1 10 12 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 7 1 10 12 1 9 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 8 1 8 1 8 1 8 1 10 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 8 9 12 1 1 10 12 1 1 8 1 8 8 9 12 1 8 1 8 8 8 8 8 1 8 10 12 1 10 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1052 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 100 1 10 9 12 1 10 12 1 1 9 12 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 7 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 9 12 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 12 10 10 12 1 1 9 12 1 8 10 12 1 1 100 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 1 8 1 10 100 12 1 1 10 12 1 1 100 1 100 1 10 10 12 1 1 10 12 1 1 8 1 8 1 100 1 8 1 10 12 10 12 1 10 12 1 10 12 1 1 8 1 8 1 9 100 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 8 1 100 1 8 1 100 1 8 1 100 1 8 1 10 12 1 8 1 9 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 8 1 8 1 100 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 8 1 10 12 1 9 12 1 1 7 1 10 7 12 1 1 1 8 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 10 12 1 10 10 12 1 9 9 12 1 7 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 7 1 9 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 684 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 1 9 100 12 1 1 1 8 1 10 100 12 1 1 1 100 1 10 12 100 1 100 1 8 1 7 1 10 10 12 1 7 1 9 7 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 8 1 10 100 12 1 1 1 7 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 100 1 100 1 10 12 1 10 12 1 8 1 8 1 10 10 12 1 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 100 1 100 1 10 10 100 1 100 1 10 100 1 10 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 11 7 12 1 1 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/MethodHandleNatives JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 142 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeStaticIntegerFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeQualifiedStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 59 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 1 1 254 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 1 8 1 8 1 8 1 10 12 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/reflect/UnsafeFieldAccessorImpl unsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 63 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/invoke/VarHandleReferences$Array
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
ciInstanceKlass java/lang/invoke/VarHandle 1 1 390 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 9 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 100 12 1 1 100 1 10 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 10 7 12 1 1 1 9 12 1 1 8 10 12 1 1 7 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/VarHandle AIOOBE_SUPPLIER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 757 7 1 7 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 100 12 1 1 10 12 1 100 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 1 7 1 10 10 12 1 1 100 1 100 1 10 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 1 10 12 1 9 12 1 1 3 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 8 10 12 1 1 10 12 1 1 8 1 9 100 1 8 9 100 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 8 1 8 1 100 1 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 3 10 12 1 3 10 12 1 3 3 3 3 3 3 3 100 1 10 12 1 10 7 12 1 1 1 10 12 1 3 9 12 1 10 12 1 1 3 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 100 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 10 10 12 100 1 10 10 10 12 1 1 10 12 1 1 10 10 12 1 8 10 100 1 10 12 1 10 100 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 7 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 8 1 10 10 12 1 10 12 1 8 1 8 1 10 10 12 1 8 1 10 100 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 100 1 10 8 1 8 1 8 1 8 1 10 12 1 100 1 100 1 100 1 10 100 1 10 100 1 10 100 12 1 1 1 9 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackWalker 0 0 235 9 100 12 1 1 1 10 100 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 8 1 10 10 100 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 9 100 12 1 1 11 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 0 306 100 1 100 1 3 10 100 12 1 1 1 10 100 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 9 100 12 1 1 1 10 100 12 1 1 9 12 1 8 1 5 0 8 1 8 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/module/Modules 1 1 504 10 100 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 100 12 1 1 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 11 12 1 9 12 1 1 11 100 12 1 1 1 10 12 1 1 10 10 12 1 10 9 12 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 12 1 1 18 12 1 1 11 100 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 100 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 1 11 12 1 1 10 12 1 18 18 10 12 1 1 9 12 1 1 11 100 12 1 1 1 100 1 10 11 12 1 11 12 1 1 11 12 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 10 12 1 1 100 1 10 18 12 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 18 12 1 11 11 12 10 12 1 10 10 100 1 18 12 1 10 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 1 16 16 15 10 12 1 16 1 16 1 15 10 12 1 16 1 16 1 15 10 12 16 1 15 10 16 1 15 10 12 16 1 15 10 12 16 15 10 12 16 15 10 12 1 1 1 100 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
instanceKlass org/jetbrains/kotlin/utils/SmartSet
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Sets$ImprovedAbstractSet
instanceKlass org/jetbrains/kotlin/com/google/common/collect/Sets$SetView
instanceKlass org/jetbrains/kotlin/protobuf/SmallSortedMap$EntrySet
instanceKlass org/jetbrains/kotlin/com/intellij/openapi/vfs/CompactVirtualFileSet
instanceKlass java/util/LinkedHashMap$LinkedKeySet
instanceKlass java/util/Collections$SingletonSet
instanceKlass java/util/LinkedHashMap$LinkedEntrySet
instanceKlass kotlin/collections/AbstractMutableSet
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet
instanceKlass java/util/EnumSet
instanceKlass java/util/TreeMap$KeySet
instanceKlass java/util/TreeSet
instanceKlass java/util/IdentityHashMap$KeySet
instanceKlass java/util/HashMap$KeySet
instanceKlass java/util/AbstractMap$1
instanceKlass java/util/WeakHashMap$KeySet
instanceKlass java/util/Collections$SetFromMap
instanceKlass java/util/HashSet
instanceKlass java/util/Collections$EmptySet
instanceKlass java/util/HashMap$EntrySet
instanceKlass java/util/ImmutableCollections$MapN$1
ciInstanceKlass java/util/AbstractSet 1 1 96 10 7 12 1 1 1 100 1 7 1 11 12 1 1 10 7 1 10 12 1 1 100 1 100 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 7 12 1 1 10 7 12 1 1 1 11 10 12 1 1 11 12 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Collections$EmptySet 1 1 100 10 7 12 1 1 1 10 7 12 1 1 1 11 100 12 1 1 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Collections$EmptyMap 1 1 125 10 7 12 1 1 1 10 7 12 1 1 1 100 1 11 12 1 1 10 100 12 1 1 1 100 1 10 9 12 1 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/util/Collections$EmptyIterator 1 1 57 10 7 12 1 1 1 100 1 10 100 1 10 10 100 12 1 1 1 7 1 10 9 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
staticfield java/util/Collections$EmptyIterator EMPTY_ITERATOR Ljava/util/Collections$EmptyIterator; java/util/Collections$EmptyIterator
ciMethod java/util/Collections$EmptyIterator hasNext ()Z 256 0 128 0 0
ciInstanceKlass java/util/function/BiFunction 1 1 65 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 11 100 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 11 12 1 100 1 100 1 1
ciInstanceKlass java/util/Collections$EmptyList 1 1 150 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 100 12 1 1 1 7 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 11 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/Collections$EmptyList size ()I 274 0 137 0 0
instanceKlass java/util/Collections$UnmodifiableList
instanceKlass java/util/Collections$UnmodifiableSet
ciInstanceKlass java/util/Collections$UnmodifiableCollection 1 1 136 10 7 12 1 1 1 100 1 10 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 10 12 1 1 7 1 10 12 1 100 1 10 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/util/Collections$UnmodifiableRandomAccessList
ciInstanceKlass java/util/Collections$UnmodifiableList 1 1 127 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 100 1 10 12 1 11 12 1 1 11 12 1 10 12 1 1 100 1 10 12 1 11 12 1 1 10 12 1 100 1 100 1 10 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1
ciInstanceKlass java/util/Collections$UnmodifiableRandomAccessList 1 1 52 10 7 12 1 1 1 100 1 9 12 1 1 11 100 12 1 1 1 10 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciMethod java/util/Collections$UnmodifiableCollection size ()I 560 0 7896 0 128
ciMethod java/util/Collections$UnmodifiableList get (I)Ljava/lang/Object; 866 0 7544 0 256
ciMethod java/util/Collections$EmptyMap entrySet ()Ljava/util/Set; 512 0 19926 0 0
ciMethod java/util/Collections$EmptySet iterator ()Ljava/util/Iterator; 512 0 19930 0 0
ciMethod java/util/AbstractSet <init> ()V 484 0 21296 0 0
ciMethod jdk/internal/util/Preconditions checkIndex (IILjava/util/function/BiFunction;)I 514 0 31145 0 -1
ciMethod java/util/List get (I)Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/util/List size ()I 0 0 1 0 -1
ciMethod java/util/Collection size ()I 0 0 1 0 -1
ciMethod java/util/AbstractCollection <init> ()V 520 0 58110 0 0
ciMethod java/util/Collections emptySet ()Ljava/util/Set; 512 0 24130 0 0
ciMethod java/util/Collections emptyIterator ()Ljava/util/Iterator; 514 0 20232 0 0
ciMethod java/util/Set iterator ()Ljava/util/Iterator; 0 0 1 0 -1
ciMethod java/util/Map entrySet ()Ljava/util/Set; 0 0 1 0 -1
ciMethod java/util/ArrayList size ()I 256 0 128 0 0
ciMethod java/util/ArrayList get (I)Ljava/lang/Object; 512 0 29634 0 192
ciMethod java/util/ArrayList elementData (I)Ljava/lang/Object; 514 0 30680 0 0
ciMethod java/util/Iterator hasNext ()Z 0 0 1 0 -1
ciMethod java/util/Iterator next ()Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/lang/Object <init> ()V 1024 0 493669 0 64
ciInstanceKlass org/jetbrains/kotlin/config/MavenComparableVersion$ListItem 1 1 118 1 7 1 1 7 1 7 1 1 100 1 1 1 100 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 12 10 12 11 1 12 10 1 1 1 1 1 1 12 11 12 11 1 1 12 10 1 7 1 12 11 1 1 12 11 1 100 1 7 10 1 8 1 1 12 10 1 100 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 1 1 1 10 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/jetbrains/kotlin/protobuf/MessageLite 1 0 33 1 100 1 100 1 100 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/protobuf/GeneratedMessageLite
ciInstanceKlass org/jetbrains/kotlin/protobuf/AbstractMessageLite 1 1 128 1 7 1 7 1 100 1 1 100 1 1 100 1 100 1 1 1 1 1 12 10 12 9 1 1 1 1 1 100 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 100 1 8 1 12 10 1 1 1 1 1 1 1 7 1 1 12 10 1 12 10 1 8 1 1 1 1 1 1 1 12 10 1 12 10 1 12 10 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 100 1 12 10 1 1 1 100 1 1 12 10 1 8 1 12 10 1 1 1 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Expression
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Effect
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$VersionRequirement
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmProtoBuf$StringTableTypes$Record
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmProtoBuf$StringTableTypes
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmProtoBuf$JvmFieldSignature
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmProtoBuf$JvmPropertySignature
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmProtoBuf$JvmMethodSignature
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmModuleProtoBuf$PackageParts
instanceKlass org/jetbrains/kotlin/metadata/jvm/JvmModuleProtoBuf$Module
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Annotation$Argument
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$QualifiedNameTable$QualifiedName
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$QualifiedNameTable
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$StringTable
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Annotation$Argument$Value
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Contract
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Annotation
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$VersionRequirementTable
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$TypeTable
instanceKlass org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage
ciInstanceKlass org/jetbrains/kotlin/protobuf/GeneratedMessageLite 1 1 390 1 7 1 7 1 100 1 1 7 1 100 1 1 7 1 1 7 1 1 100 1 1 100 1 1 100 1 1 100 1 1 7 1 7 1 100 1 100 1 1 7 1 7 1 1 100 1 1 100 1 7 1 1 7 1 1 1 5 0 1 1 12 10 1 1 1 1 1 1 1 1 1 100 1 8 1 12 10 1 1 1 100 1 7 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 7 1 1 12 10 1 1 12 9 1 1 12 10 1 1 12 10 1 1 12 9 1 1 12 9 1 1 12 10 1 1 12 10 1 12 10 1 12 9 1 12 10 1 12 10 1 1 12 10 1 1 12 11 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 9 1 1 12 10 1 12 10 12 10 1 1 12 10 1 1 12 11 1 1 12 10 1 12 11 1 12 9 1 12 10 1 1 12 10 1 1 12 10 1 12 11 1 100 1 12 10 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 12 10 1 1 1 1 100 1 100 1 1 12 10 1 100 1 1 12 10 1 100 1 1 12 10 1 100 12 10 12 10 1 8 1 1 12 10 1 8 1 8 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 12 10 1 8 1 1 12 10 1 100 1 8 1 1 1 1 1 1 1 1 1 1 1 100 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$TypeAlias
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$PackageFragment
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$TypeParameter
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$EnumEntry
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$ValueParameter
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Property
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Function
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Constructor
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Type
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Class
instanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Package
ciInstanceKlass org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage 1 1 175 1 7 1 1 7 1 100 1 1 1 7 1 1 100 1 1 100 1 1 7 1 1 1 100 1 7 1 1 100 1 1 1 1 1 12 10 1 1 12 10 12 9 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 12 10 1 12 10 1 100 1 8 1 12 10 1 1 1 1 1 1 12 10 1 1 12 9 1 1 12 10 1 1 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 1 12 9 1 1 12 10 1 1 1 1 1 12 10 1 12 10 1 1 1 1 1 12 10 1 1 1 100 1 1 12 10 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/jetbrains/kotlin/protobuf/FieldSet 1 1 700 1 7 1 1 7 1 1 7 1 7 1 1 7 1 100 1 1 7 1 100 1 1 100 1 7 1 1 100 1 100 1 1 7 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 1 7 1 1 12 10 12 9 1 1 1 1 12 10 1 1 1 1 10 1 12 9 12 9 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 11 1 12 11 1 1 12 10 1 1 12 10 1 7 1 1 12 11 1 7 1 12 11 1 12 11 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 12 10 12 10 1 100 1 1 12 10 1 1 1 12 10 1 1 12 11 1 1 1 1 1 1 1 1 1 12 10 1 7 11 1 12 10 1 1 1 1 12 11 1 100 1 8 1 12 10 1 1 12 10 1 1 1 1 1 1 7 1 8 1 7 10 1 1 12 11 11 1 1 12 11 1 1 12 10 1 12 10 1 1 1 1 1 1 1 12 10 1 12 10 1 1 1 1 8 12 10 1 12 11 1 1 1 1 100 10 1 12 11 1 1 1 1 1 1 12 11 1 1 1 8 1 1 12 11 1 1 1 100 10 1 1 12 9 1 1 12 10 1 12 10 1 7 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 12 10 1 1 12 11 1 1 12 9 12 11 1 1 1 1 12 10 1 1 1 1 1 1 12 10 1 1 1 100 1 1 12 10 1 1 1 12 10 1 1 12 11 1 1 12 11 1 12 11 1 1 1 1 100 1 12 9 10 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 8 1 8 1 8 1 100 1 8 10 1 1 1 1 1 1 1 12 10 1 1 1 1 12 10 1 12 11 1 12 11 1 7 1 1 12 10 1 1 1 12 9 1 12 10 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 11 1 12 10 1 1 1 12 10 1 12 10 12 10 1 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 12 10 12 10 1 100 12 10 1 12 10 1 1 1 1 1 1 1 1
staticfield org/jetbrains/kotlin/protobuf/FieldSet DEFAULT_INSTANCE Lorg/jetbrains/kotlin/protobuf/FieldSet; org/jetbrains/kotlin/protobuf/FieldSet
instanceKlass org/jetbrains/kotlin/protobuf/SmallSortedMap$1
ciInstanceKlass org/jetbrains/kotlin/protobuf/SmallSortedMap 1 1 255 1 7 1 1 7 1 1 7 1 1 100 1 1 7 1 1 7 1 1 7 1 100 1 100 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 12 10 12 9 1 7 1 1 12 10 12 9 1 1 12 10 12 9 1 1 1 1 12 9 1 1 12 11 1 1 12 10 1 1 1 7 1 12 11 1 1 1 1 1 12 11 1 1 11 1 1 1 1 12 10 1 1 12 11 1 1 1 7 1 1 12 10 12 11 1 1 1 1 1 1 1 1 1 12 10 12 11 1 1 1 1 12 10 1 12 10 1 12 10 1 1 12 10 1 100 1 12 11 1 12 11 1 1 12 10 1 12 10 1 1 12 11 1 1 1 1 1 1 1 11 12 11 11 1 12 10 12 11 1 11 1 100 1 1 12 11 1 100 1 12 11 1 12 10 12 11 12 11 1 1 1 1 1 1 12 11 1 1 1 1 1 12 9 1 12 10 1 100 10 1 1 100 10 1 7 10 12 10 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/jetbrains/kotlin/protobuf/SmallSortedMap$1 1 1 108 1 7 1 1 7 1 1 1 12 1 7 1 100 1 1 7 1 100 1 1 1 1 12 10 1 1 1 1 1 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 11 1 12 11 1 12 11 1 7 1 7 1 1 12 10 1 1 12 11 1 1 12 10 1 7 1 1 12 11 1 7 1 12 11 1 12 11 12 10 1 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1
instanceKlass org/jetbrains/kotlin/protobuf/RopeByteString
instanceKlass org/jetbrains/kotlin/protobuf/LiteralByteString
ciInstanceKlass org/jetbrains/kotlin/protobuf/ByteString 1 1 356 1 7 1 1 7 1 100 1 1 100 1 100 1 1 7 1 1 100 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 12 10 1 1 1 12 10 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 7 1 1 12 10 1 7 1 12 10 1 1 1 1 1 12 10 1 1 100 1 1 12 10 1 1 1 12 10 12 10 1 1 100 1 100 1 1 12 10 1 1 1 1 1 1 8 1 100 1 8 1 12 10 1 1 1 1 1 100 1 12 10 1 1 1 1 1 100 10 1 12 10 1 7 1 12 11 1 100 1 1 12 10 1 12 10 1 1 1 1 1 1 1 100 1 1 12 10 1 1 1 1 1 1 5 0 1 100 1 100 1 12 10 1 8 1 1 12 10 1 12 10 1 8 1 1 12 10 1 12 10 1 7 1 1 12 10 1 1 1 1 1 12 11 1 100 1 12 11 1 1 12 11 12 11 12 9 11 11 1 1 12 10 1 1 1 1 1 1 1 1 1 12 9 1 100 10 12 10 1 1 1 1 1 1 1 1 12 10 1 1 100 1 8 10 1 8 1 8 1 8 1 8 1 12 10 1 1 1 1 1 1 1 100 1 12 9 1 1 1 1 8 1 12 10 1 1 1 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 10 1 1 1 1 1 12 10 1 1 1 1 1 8 1 1 12 10 1 100 1 1 12 10 1 1 12 10 1 1 12 10 12 10 1 1 7 1 12 10 1 1 1 1 1 1 1 1
staticfield org/jetbrains/kotlin/protobuf/ByteString EMPTY Lorg/jetbrains/kotlin/protobuf/ByteString; org/jetbrains/kotlin/protobuf/LiteralByteString
staticfield org/jetbrains/kotlin/protobuf/ByteString $assertionsDisabled Z 1
instanceKlass org/jetbrains/kotlin/protobuf/BoundedByteString
ciInstanceKlass org/jetbrains/kotlin/protobuf/LiteralByteString 1 1 257 1 7 1 7 1 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 12 10 12 9 12 9 1 1 1 1 1 1 1 1 1 1 100 1 100 1 12 10 1 8 1 1 12 10 1 12 10 1 8 1 1 12 10 1 12 10 12 10 1 8 1 8 1 8 1 8 1 1 12 9 1 100 1 12 10 1 12 10 1 1 1 1 1 1 1 100 1 1 12 10 1 1 1 1 1 1 1 100 1 1 12 10 1 1 1 1 12 10 1 12 10 1 1 1 1 1 100 10 12 10 1 100 1 1 12 11 1 1 1 1 1 100 1 1 12 10 1 100 1 12 10 1 1 1 1 12 10 1 1 1 100 1 7 1 12 10 1 1 1 1 1 7 1 12 10 1 1 1 1 12 10 1 1 1 10 1 1 12 10 1 100 1 100 12 10 1 100 1 1 12 10 1 1 12 10 12 10 1 8 10 1 1 1 8 1 8 1 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 1 100 10 1 1 1 100 1 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 1 1 1 1 1 1
ciInstanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Type 1 1 511 1 7 1 1 7 1 100 1 1 100 1 1 100 1 1 7 1 1 100 1 1 7 1 100 1 100 1 1 7 1 7 1 1 1 7 1 1 100 1 7 1 100 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 3 1 1 1 3 1 1 3 1 1 3 1 1 3 1 1 3 1 1 3 1 1 3 1 1 3 1 1 3 1 1 3 1 1 3 1 1 3 1 1 1 1 1 1 5 0 1 1 1 12 10 12 9 12 9 1 1 12 10 12 9 1 1 1 1 1 1 12 10 1 12 9 1 1 1 12 9 1 1 1 100 1 100 1 12 10 1 1 12 10 1 7 1 1 12 10 1 7 1 100 1 1 12 10 1 1 12 10 12 9 1 12 10 12 9 1 7 10 12 9 12 9 1 1 12 10 1 7 1 1 12 11 1 1 12 10 12 9 12 9 12 9 1 1 12 10 9 1 1 12 10 1 12 10 12 9 12 9 12 9 12 9 12 9 12 9 12 9 12 9 12 9 1 7 1 1 12 10 1 12 10 1 12 10 1 100 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 11 1 1 1 1 12 11 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 12 10 1 12 10 12 10 12 10 12 10 12 10 10 12 10 12 10 12 10 12 10 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 10 1 1 1 100 12 10 1 1 1 100 1 12 11 1 1 1 12 11 1 1 12 11 1 1 1 12 11 1 1 12 11 1 1 1 12 11 1 12 11 12 11 1 1 12 11 1 1 12 11 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 1 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 12 10 1 1 1 1 1 1 1 1 1 1
staticfield org/jetbrains/kotlin/metadata/ProtoBuf$Type defaultInstance Lorg/jetbrains/kotlin/metadata/ProtoBuf$Type; org/jetbrains/kotlin/metadata/ProtoBuf$Type
ciInstanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$ValueParameter 1 1 397 1 7 1 1 7 1 100 1 1 100 1 1 100 1 1 7 1 7 1 1 100 1 100 1 1 100 1 7 1 7 1 1 1 7 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 3 1 1 3 1 1 3 1 1 1 3 1 1 3 1 1 3 1 1 1 1 1 1 5 0 1 1 1 12 10 12 9 12 9 1 1 12 10 12 9 1 1 1 1 1 1 12 10 1 12 9 1 1 1 1 12 9 1 1 1 100 1 100 1 12 10 1 1 12 10 1 7 1 1 12 10 1 7 1 100 1 1 12 10 1 1 12 10 12 9 1 12 10 12 9 12 9 12 9 1 1 12 10 12 9 1 1 12 10 1 1 12 10 1 1 12 10 12 9 12 9 12 9 1 12 10 1 12 10 1 100 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1 12 10 12 10 12 10 12 10 12 10 12 10 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 1 100 12 10 1 1 1 100 1 12 11 1 1 1 12 11 1 1 12 11 1 1 1 12 11 1 1 12 11 1 1 1 12 11 1 12 11 12 11 1 1 12 11 1 1 12 11 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 12 10 1 12 10 1 1 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 10 12 10 1 1 1 1 1 1 1 1 1 1
staticfield org/jetbrains/kotlin/metadata/ProtoBuf$ValueParameter defaultInstance Lorg/jetbrains/kotlin/metadata/ProtoBuf$ValueParameter; org/jetbrains/kotlin/metadata/ProtoBuf$ValueParameter
ciInstanceKlass org/jetbrains/kotlin/protobuf/CodedOutputStream 1 1 530 1 7 1 7 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 12 9 1 1 1 1 1 1 1 1 12 10 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 1 1 100 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 1 1 1 12 10 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 12 10 1 12 10 1 100 1 1 12 10 1 12 10 1 100 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 8 1 100 1 1 12 10 1 12 10 1 1 7 1 1 12 11 1 1 1 12 11 1 7 1 12 10 12 10 12 10 1 100 1 12 10 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 12 10 1 1 1 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 12 10 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 12 10 1 12 10 1 12 10 1 12 10 1 12 10 1 100 1 100 1 8 1 12 10 1 1 1 1 100 10 1 10 1 7 1 12 10 1 12 10 1 1 100 1 8 12 10 1 12 10 1 100 1 8 10 1 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 1 100 1 1 12 10 1 1 12 10 1 12 10 1 7 1 12 10 1 3 3 5 0 5 0 5 0 5 0 5 0 5 0 5 0 5 0 5 0 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/jetbrains/kotlin/protobuf/WireFormat 1 1 62 1 7 1 100 1 1 100 1 100 1 1 100 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 12 10 12 9 12 9 12 9 12 9 1 1 1 1 1 1
staticfield org/jetbrains/kotlin/protobuf/WireFormat MESSAGE_SET_ITEM_TAG I 11
staticfield org/jetbrains/kotlin/protobuf/WireFormat MESSAGE_SET_ITEM_END_TAG I 12
staticfield org/jetbrains/kotlin/protobuf/WireFormat MESSAGE_SET_TYPE_ID_TAG I 16
staticfield org/jetbrains/kotlin/protobuf/WireFormat MESSAGE_SET_MESSAGE_TAG I 26
ciInstanceKlass org/jetbrains/kotlin/protobuf/GeneratedMessageLite$1 1 1 41 1 7 1 100 1 1 100 1 7 1 100 1 1 1 1 1 1 100 1 1 12 10 12 9 1 1 12 9 1 1 12 10 1 12 9 1 1 1 1 1
staticfield org/jetbrains/kotlin/protobuf/GeneratedMessageLite$1 $SwitchMap$com$google$protobuf$WireFormat$JavaType [I 9
ciInstanceKlass org/jetbrains/kotlin/protobuf/SmallSortedMap$Entry 1 1 123 1 7 1 1 7 1 100 1 100 1 1 100 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 12 11 1 12 11 1 12 10 1 1 1 1 1 1 1 12 9 1 12 10 12 9 12 9 1 1 1 1 1 1 12 10 1 12 11 1 1 1 1 1 1 12 10 1 1 1 1 1 12 10 1 1 1 1 12 10 1 1 1 100 1 1 12 10 1 100 1 12 10 1 12 10 1 1 12 10 1 8 12 10 12 10 1 1 12 10 1 1 1 1 1 1 1 1
ciInstanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument 1 1 370 1 7 1 7 1 100 1 1 7 1 100 1 1 1 100 1 1 7 1 1 7 1 100 1 100 1 7 1 7 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 3 1 1 1 3 1 1 1 1 1 1 5 0 1 1 12 10 12 9 12 9 1 1 12 10 12 9 1 1 1 1 1 12 10 1 12 9 1 1 1 1 12 9 1 1 1 100 1 100 1 12 10 1 1 12 10 1 7 1 1 12 10 1 7 1 100 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 12 9 12 9 12 9 1 1 12 10 12 9 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 12 9 1 12 10 1 12 10 1 100 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 1 1 1 1 1 1 1 1 1 12 9 12 10 1 12 10 12 10 12 10 1 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 1 12 10 1 1 12 10 1 12 10 1 12 10 1 1 1 100 12 10 1 1 1 100 1 12 11 1 1 1 12 11 1 1 12 11 1 1 1 12 11 1 1 12 11 1 1 1 12 11 1 12 11 12 11 1 1 12 11 1 1 12 11 1 1 1 12 10 1 12 10 1 12 10 1 12 10 1 12 10 12 10 1 12 10 1 12 10 1 1 1 1 1 10 1 1 1 1 1 1 1 1 1 1 10 12 10 1 1 1 1 1 1 1 1 1
staticfield org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument defaultInstance Lorg/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument; org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument
ciInstanceKlass org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection 1 1 106 1 7 1 1 7 1 100 1 1 100 1 100 1 1 100 1 1 1 7 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 12 9 7 1 1 12 10 1 1 1 12 10 1 1 1 1 12 9 1 1 12 9 12 9 12 9 12 9 1 1 1 12 9 1 1 1 1 12 10 1 1 1 8 12 10 8 8 8 12 10 1 1 1 1 1 1 1 1
staticfield org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection IN Lorg/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection; org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection
staticfield org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection OUT Lorg/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection; org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection
staticfield org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection INV Lorg/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection; org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection
staticfield org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection STAR Lorg/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection; org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection
staticfield org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection $VALUES [Lorg/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection; 4 [Lorg/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection;
ciInstanceKlass org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter 1 1 133 1 7 1 7 1 1 7 1 100 1 1 7 1 100 1 1 7 1 1 1 100 1 100 1 100 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 12 9 1 12 10 1 1 12 10 1 1 12 10 12 9 1 7 1 1 12 11 1 12 11 12 9 12 9 1 1 1 1 1 1 100 1 12 11 1 1 12 10 1 1 12 10 1 1 12 9 1 12 10 1 12 11 1 100 1 100 1 1 12 10 1 1 12 10 1 1 1 1 1 1 1 12 10 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/jetbrains/kotlin/protobuf/SmallSortedMap$EntrySet 1 1 99 1 7 1 1 7 1 1 100 1 1 100 1 100 1 1 100 1 7 1 1 1 1 1 12 9 1 12 10 1 1 1 1 1 1 1 12 10 1 1 12 10 1 1 1 1 12 11 1 1 12 10 1 12 11 1 100 1 12 10 1 1 1 1 1 1 1 1 1 1 1 12 10 1 100 1 1 12 10 1 12 10 1 12 10 12 10 1 12 10 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator 1 1 118 1 7 1 1 7 1 7 1 1 7 1 7 1 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 12 9 1 12 10 12 9 1 1 1 1 1 1 1 12 10 1 7 1 1 12 11 1 1 12 10 12 11 1 1 1 12 9 1 1 12 11 1 12 11 1 1 100 1 8 1 12 10 1 12 10 1 1 12 10 12 11 1 12 9 1 1 12 10 1 1 12 11 1 7 1 12 11 12 10 1 12 10 1 1 1 1 1 1 1 1 1 1
ciMethodData java/lang/Object <init> ()V 2 493779 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 139 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/AbstractCollection <init> ()V 2 58123 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0xe207 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList get (I)Ljava/lang/Object; 2 29634 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 19 0x50002 0x72c2 0xb0005 0x533b 0x0 0x18bd8671a80 0x1f73 0x18c1f3186c0 0x14 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 5 java/util/ArrayList 7 org/jetbrains/kotlin/config/MavenComparableVersion$ListItem methods 0
ciMethodData java/util/Objects checkIndex (II)I 2 31145 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 139 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x30002 0x78aa 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/ArrayList elementData (I)Ljava/lang/Object; 2 30680 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/AbstractSet <init> ()V 2 21337 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x5266 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/Collections$UnmodifiableCollection size ()I 2 7896 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40005 0x0 0x0 0x18bd8671a80 0x1dc0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/util/ArrayList methods 0
ciMethodData java/util/Collections$UnmodifiableList get (I)Ljava/lang/Object; 2 7544 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x50005 0x0 0x0 0x18bd8671a80 0x1bc7 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 3 java/util/ArrayList methods 0
ciMethodData java/util/Collections emptySet ()Ljava/util/Set; 2 24130 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/util/Collections emptyIterator ()Ljava/util/Iterator; 2 20232 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/util/Collections$EmptyMap entrySet ()Ljava/util/Set; 2 19926 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x2 0x4cd6 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/Collections$EmptySet iterator ()Ljava/util/Iterator; 2 19930 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x2 0x4cda 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethod org/jetbrains/kotlin/protobuf/MessageLite writeTo (Lorg/jetbrains/kotlin/protobuf/CodedOutputStream;)V 0 0 1 0 -1
ciMethod org/jetbrains/kotlin/protobuf/MessageLite getSerializedSize ()I 0 0 1 0 -1
ciMethod org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage newExtensionWriter ()Lorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter; 512 0 18475 0 0
ciMethod org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage extensionsSerializedSize ()I 512 0 8136 0 -1
ciMethod org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage access$200 (Lorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage;)Lorg/jetbrains/kotlin/protobuf/FieldSet; 256 0 128 0 0
ciMethod org/jetbrains/kotlin/protobuf/FieldSet iterator ()Ljava/util/Iterator; 512 0 18876 0 0
ciMethod org/jetbrains/kotlin/protobuf/SmallSortedMap entrySet ()Ljava/util/Set; 512 0 18898 0 0
ciMethod org/jetbrains/kotlin/protobuf/SmallSortedMap access$400 (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;)Ljava/util/List; 256 0 128 0 0
ciMethod org/jetbrains/kotlin/protobuf/SmallSortedMap access$600 (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;)Ljava/util/Map; 262 0 131 0 0
ciMethod org/jetbrains/kotlin/protobuf/ByteString size ()I 0 0 1 0 -1
ciMethod org/jetbrains/kotlin/protobuf/LiteralByteString size ()I 846 0 3017 0 96
ciMethod org/jetbrains/kotlin/metadata/ProtoBuf$Type writeTo (Lorg/jetbrains/kotlin/protobuf/CodedOutputStream;)V 512 22 12309 0 -1
ciMethod org/jetbrains/kotlin/metadata/ProtoBuf$Type getSerializedSize ()I 522 22 5820 0 -1
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream writeInt32 (II)V 520 0 26005 0 0
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream writeBool (IZ)V 34 0 540 0 -1
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream writeMessage (ILorg/jetbrains/kotlin/protobuf/MessageLite;)V 432 0 21954 0 0
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream writeEnum (II)V 298 0 1319 0 -1
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream writeInt32NoTag (I)V 516 0 29013 0 0
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream writeMessageNoTag (Lorg/jetbrains/kotlin/protobuf/MessageLite;)V 482 0 25559 0 0
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream computeInt32Size (II)I 520 0 14582 0 -1
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream computeBoolSize (IZ)I 34 0 546 0 -1
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream computeMessageSize (ILorg/jetbrains/kotlin/protobuf/MessageLite;)I 400 0 13014 0 0
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream computeEnumSize (II)I 118 0 1312 0 0
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream computeInt32SizeNoTag (I)I 524 0 17973 0 0
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream computeMessageSizeNoTag (Lorg/jetbrains/kotlin/protobuf/MessageLite;)I 438 0 15592 0 0
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream computeEnumSizeNoTag (I)I 118 0 1312 0 0
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream refreshBuffer ()V 510 0 6898 0 -1
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (B)V 0 0 5769 0 192
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (I)V 0 0 10878 0 0
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawBytes (Lorg/jetbrains/kotlin/protobuf/ByteString;)V 530 0 26070 0 -1
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream writeTag (II)V 518 0 54826 0 0
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream computeTagSize (I)I 512 0 32529 0 0
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawVarint32 (I)V 0 0 9717 0 448
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream computeRawVarint32Size (I)I 516 0 5400 0 128
ciMethod org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawVarint64 (J)V 0 0 1 0 -1
ciMethod org/jetbrains/kotlin/protobuf/WireFormat makeTag (II)I 534 0 87036 0 0
ciMethod org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument writeTo (Lorg/jetbrains/kotlin/protobuf/CodedOutputStream;)V 514 0 5390 0 0
ciMethod org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument getSerializedSize ()I 524 0 11049 0 0
ciMethod org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection getNumber ()I 166 0 83 0 0
ciMethodData org/jetbrains/kotlin/protobuf/LiteralByteString size ()I 2 3017 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawVarint32 (I)V 2 9717 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 139 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 31 0x40007 0x24e 0x58 0x25f5 0x90005 0x25f5 0x0 0x0 0x0 0x0 0x0 0x160005 0x24e 0x0 0x0 0x0 0x0 0x0 0x1e0003 0x24e 0xffffffffffffff70 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (I)V 2 10878 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 139 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x30005 0x2a7e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (B)V 2 5769 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 139 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x80007 0x1689 0x30 0x0 0xc0002 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethod org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter <init> (Lorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage;Z)V 512 0 18838 0 0
ciMethod org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter writeUntil (ILorg/jetbrains/kotlin/protobuf/CodedOutputStream;)V 512 38 18409 0 -1
ciMethod org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter <init> (Lorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage;ZLorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$1;)V 512 0 18768 0 0
ciMethod org/jetbrains/kotlin/protobuf/SmallSortedMap$EntrySet <init> (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;)V 510 0 18940 0 0
ciMethod org/jetbrains/kotlin/protobuf/SmallSortedMap$EntrySet iterator ()Ljava/util/Iterator; 512 0 19135 0 0
ciMethod org/jetbrains/kotlin/protobuf/SmallSortedMap$EntrySet <init> (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;Lorg/jetbrains/kotlin/protobuf/SmallSortedMap$1;)V 512 0 18924 0 0
ciMethod org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator <init> (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;)V 510 0 19216 0 0
ciMethod org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator hasNext ()Z 512 0 21085 0 0
ciMethod org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator next ()Ljava/util/Map$Entry; 510 0 1750 0 0
ciMethod org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator getOverflowIterator ()Ljava/util/Iterator; 472 0 19368 0 0
ciMethod org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator next ()Ljava/lang/Object; 512 0 1750 0 0
ciMethod org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator <init> (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;Lorg/jetbrains/kotlin/protobuf/SmallSortedMap$1;)V 510 0 19148 0 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream computeRawVarint32Size (I)I 2 5400 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x40007 0x12a 0x20 0x12ec 0xe0007 0x0 0x20 0x12a 0x180007 0x0 0x20 0x0 0x220007 0x0 0x20 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/WireFormat makeTag (II)I 2 87038 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 5 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream computeTagSize (I)I 2 32529 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x20002 0x7e11 0x50002 0x7e11 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream writeTag (II)V 2 54840 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 20 0x30002 0xd535 0x60005 0xd535 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream computeInt32SizeNoTag (I)I 2 17973 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x10007 0x0 0x30 0x452f 0x50002 0x452f 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream writeInt32NoTag (I)V 2 29041 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 31 0x10007 0x0 0x70 0x706f 0x60005 0x706f 0x0 0x0 0x0 0x0 0x0 0x90003 0x706f 0x50 0xf0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream writeInt32 (II)V 2 27722 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x30005 0x6b46 0x0 0x0 0x0 0x0 0x0 0x80005 0x6b46 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator hasNext ()Z 2 21171 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 38 0xa0002 0x51ae 0xd0005 0x0 0x0 0x18c20ca68d0 0x449f 0x18bd8671a80 0xd0f 0x120007 0x6bb 0x88 0x4af3 0x160002 0x4af3 0x190005 0x0 0x0 0x18c210b7920 0x4af3 0x0 0x0 0x1e0007 0x4af3 0x38 0x0 0x220003 0x6bb 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 3 5 java/util/Collections$EmptyList 7 java/util/ArrayList 18 java/util/Collections$EmptyIterator methods 0
ciMethodData org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator getOverflowIterator ()Ljava/util/Iterator; 2 19574 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 140 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 29 0x40007 0x0 0xa0 0x4b8a 0xc0002 0x4b8a 0xf0005 0x0 0x0 0x18c210bb160 0x4b8a 0x0 0x0 0x140005 0x0 0x0 0x18c210bb210 0x4b8a 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 2 9 java/util/Collections$EmptyMap 16 java/util/Collections$EmptySet methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream computeMessageSize (ILorg/jetbrains/kotlin/protobuf/MessageLite;)I 2 13804 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 140 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x10002 0x3524 0x50002 0x3524 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream computeMessageSizeNoTag (Lorg/jetbrains/kotlin/protobuf/MessageLite;)I 2 15594 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 140 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x10005 0x10f0 0x0 0x18c20ca5610 0x1ff7 0x18c1ab80f30 0x981 0x80002 0x3c0d 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 2 3 org/jetbrains/kotlin/metadata/ProtoBuf$Type 5 org/jetbrains/kotlin/metadata/ProtoBuf$ValueParameter methods 0
ciMethodData org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage newExtensionWriter ()Lorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter; 2 18786 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x70002 0x4862 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter <init> (Lorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage;ZLorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$1;)V 2 18844 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 140 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x30002 0x489c 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter <init> (Lorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage;Z)V 2 18895 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 139 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 47 0x60002 0x48c9 0xe0002 0x48c9 0x110005 0x48c9 0x0 0x0 0x0 0x0 0x0 0x1b0005 0x0 0x0 0x18c1f19cea0 0x48c9 0x0 0x0 0x200007 0x424b 0x90 0x67e 0x280005 0x0 0x0 0x18c1f19cea0 0x67e 0x0 0x0 0x2d0004 0x0 0x0 0x18c1f19cf50 0x67e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xe 0x0 0x0 oops 3 14 org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator 25 org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator 32 org/jetbrains/kotlin/protobuf/SmallSortedMap$Entry methods 0
ciMethodData org/jetbrains/kotlin/protobuf/FieldSet iterator ()Ljava/util/Iterator; 2 18898 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 43 0x40007 0x48d2 0xa0 0x0 0xf0005 0x0 0x0 0x0 0x0 0x0 0x0 0x120005 0x0 0x0 0x0 0x0 0x0 0x0 0x170002 0x0 0x1f0005 0x0 0x0 0x18c1f1a1760 0x48d2 0x0 0x0 0x220005 0x0 0x0 0x18c1f1a1810 0x48d1 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 2 23 org/jetbrains/kotlin/protobuf/SmallSortedMap$1 30 org/jetbrains/kotlin/protobuf/SmallSortedMap$EntrySet methods 0
ciMethodData org/jetbrains/kotlin/protobuf/SmallSortedMap entrySet ()Ljava/util/Set; 2 18938 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 255 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x40007 0x0 0x30 0x48fa 0xe0002 0x48fa 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/SmallSortedMap$EntrySet <init> (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;Lorg/jetbrains/kotlin/protobuf/SmallSortedMap$1;)V 2 18941 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 140 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x20002 0x48fd 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/SmallSortedMap$EntrySet <init> (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;)V 2 18962 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 140 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x60002 0x4912 0x0 0x0 0x0 0x0 0x9 0x2 0x6 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/SmallSortedMap$EntrySet iterator ()Ljava/util/Iterator; 2 19175 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x90002 0x49e7 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator <init> (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;Lorg/jetbrains/kotlin/protobuf/SmallSortedMap$1;)V 2 19228 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 140 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x20002 0x4a1c 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator <init> (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;)V 2 19249 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 140 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x60002 0x4a32 0x0 0x0 0x0 0x0 0x9 0x2 0xe 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream writeMessage (ILorg/jetbrains/kotlin/protobuf/MessageLite;)V 2 23771 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 140 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x30005 0x5c03 0x0 0x0 0x0 0x0 0x0 0x80005 0x5c03 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream writeMessageNoTag (Lorg/jetbrains/kotlin/protobuf/MessageLite;)V 2 25559 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 140 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 31 0x20005 0x24f8 0x0 0x18c1ab80f30 0x97c 0x18c20ca5610 0x3390 0x70005 0x62e6 0x0 0x0 0x0 0x0 0x0 0xc0005 0x24f8 0x0 0x18c1ab80f30 0x97c 0x18c20ca5610 0x3390 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 4 3 org/jetbrains/kotlin/metadata/ProtoBuf$ValueParameter 5 org/jetbrains/kotlin/metadata/ProtoBuf$Type 17 org/jetbrains/kotlin/metadata/ProtoBuf$ValueParameter 19 org/jetbrains/kotlin/metadata/ProtoBuf$Type methods 0
ciMethodData org/jetbrains/kotlin/metadata/ProtoBuf$Type writeTo (Lorg/jetbrains/kotlin/protobuf/CodedOutputStream;)V 2 12317 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 228 0x10005 0x2f1d 0x0 0x0 0x0 0x0 0x0 0x60005 0x0 0x0 0x18c20ca5610 0x2f1d 0x0 0x0 0x150007 0x2f19 0x58 0x4 0x1e0005 0x4 0x0 0x0 0x0 0x0 0x0 0x280005 0x0 0x0 0x18c20ca68d0 0x22ef 0x18c20ca6980 0x1efa 0x2d0007 0x2f1d 0xe0 0x12cc 0x370005 0x0 0x0 0x18c20ca6980 0x12cc 0x0 0x0 0x3c0004 0x0 0x0 0x18c20ca6a30 0x12cc 0x0 0x0 0x3f0005 0x12cc 0x0 0x0 0x0 0x0 0x0 0x450003 0x12cc 0xffffffffffffff00 0x4f0007 0x2d1e 0x58 0x1ff 0x580005 0x1ff 0x0 0x0 0x0 0x0 0x0 0x620007 0x2f1d 0x58 0x0 0x6b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x750007 0x2f1d 0x58 0x0 0x7e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x8a0007 0xbcf 0x58 0x234e 0x940005 0x234e 0x0 0x0 0x0 0x0 0x0 0xa00007 0x2f1d 0x58 0x0 0xaa0005 0x0 0x0 0x0 0x0 0x0 0x0 0xb60007 0x2f1d 0x58 0x0 0xc00005 0x0 0x0 0x0 0x0 0x0 0x0 0xcc0007 0x23d2 0x58 0xb4b 0xd60005 0xb4b 0x0 0x0 0x0 0x0 0x0 0xe40007 0x2f1d 0x58 0x0 0xee0005 0x0 0x0 0x0 0x0 0x0 0x0 0xfc0007 0x2f1d 0x58 0x0 0x1060005 0x0 0x0 0x0 0x0 0x0 0x0 0x1140007 0x2e99 0x58 0x84 0x11e0005 0x84 0x0 0x0 0x0 0x0 0x0 0x12c0007 0x2e99 0x58 0x84 0x1360005 0x84 0x0 0x0 0x0 0x0 0x0 0x1440007 0x2f1d 0x58 0x0 0x14e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1560005 0x0 0x0 0x18c20ca6ae0 0x2f1d 0x0 0x0 0x15e0005 0x2f1d 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 6 10 org/jetbrains/kotlin/metadata/ProtoBuf$Type 28 java/util/Collections$EmptyList 30 java/util/Collections$UnmodifiableRandomAccessList 39 java/util/Collections$UnmodifiableRandomAccessList 46 org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument 195 org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter methods 0
ciMethodData org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument getSerializedSize ()I 2 11049 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 45 0x70007 0x133 0x20 0x28f0 0x150007 0xd0 0x68 0x63 0x1e0005 0x63 0x0 0x0 0x0 0x0 0x0 0x210002 0x63 0x2d0007 0x6 0x30 0x12d 0x360002 0x12d 0x420007 0x133 0x30 0x0 0x4b0002 0x0 0x550005 0x0 0x0 0x18c1ab839f0 0x133 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 32 org/jetbrains/kotlin/protobuf/LiteralByteString methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream computeEnumSize (II)I 2 1312 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 139 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x10002 0x4e5 0x50002 0x4e5 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/CodedOutputStream computeEnumSizeNoTag (I)I 2 1312 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 139 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x10002 0x4e5 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument writeTo (Lorg/jetbrains/kotlin/protobuf/CodedOutputStream;)V 2 5390 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 64 0x10005 0x140d 0x0 0x0 0x0 0x0 0x0 0xc0007 0x1140 0x90 0x2cd 0x150005 0x2cd 0x0 0x0 0x0 0x0 0x0 0x180005 0x2cd 0x0 0x0 0x0 0x0 0x0 0x220007 0x10 0x58 0x13fd 0x2b0005 0x13fd 0x0 0x0 0x0 0x0 0x0 0x350007 0x140d 0x58 0x0 0x3e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x460005 0x140d 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator next ()Ljava/lang/Object; 2 1750 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 248 127 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10005 0x0 0x0 0x18c1f19cea0 0x5d6 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator methods 0
ciMethodData org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator next ()Ljava/util/Map$Entry; 2 1750 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 140 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 54 0x140002 0x5d7 0x170005 0x0 0x0 0x18bd8671a80 0x5d7 0x0 0x0 0x1c0007 0x0 0xa0 0x5d7 0x230002 0x5d7 0x2a0005 0x0 0x0 0x18bd8671a80 0x5d7 0x0 0x0 0x2f0004 0x0 0x0 0x18c1f19cf50 0x5d7 0x0 0x0 0x340002 0x0 0x370005 0x0 0x0 0x0 0x0 0x0 0x0 0x3c0004 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 3 5 java/util/ArrayList 18 java/util/ArrayList 25 org/jetbrains/kotlin/protobuf/SmallSortedMap$Entry methods 0
compile org/jetbrains/kotlin/metadata/ProtoBuf$Type writeTo (Lorg/jetbrains/kotlin/protobuf/CodedOutputStream;)V -1 4 inline 93 0 -1 org/jetbrains/kotlin/metadata/ProtoBuf$Type writeTo (Lorg/jetbrains/kotlin/protobuf/CodedOutputStream;)V 1 6 org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage newExtensionWriter ()Lorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter; 2 7 org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter <init> (Lorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage;ZLorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$1;)V 3 3 org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage$ExtensionWriter <init> (Lorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage;Z)V 4 6 java/lang/Object <init> ()V 4 14 org/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage access$200 (Lorg/jetbrains/kotlin/protobuf/GeneratedMessageLite$ExtendableMessage;)Lorg/jetbrains/kotlin/protobuf/FieldSet; 4 17 org/jetbrains/kotlin/protobuf/FieldSet iterator ()Ljava/util/Iterator; 5 31 org/jetbrains/kotlin/protobuf/SmallSortedMap entrySet ()Ljava/util/Set; 6 14 org/jetbrains/kotlin/protobuf/SmallSortedMap$EntrySet <init> (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;Lorg/jetbrains/kotlin/protobuf/SmallSortedMap$1;)V 7 2 org/jetbrains/kotlin/protobuf/SmallSortedMap$EntrySet <init> (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;)V 8 6 java/util/AbstractSet <init> ()V 9 1 java/util/AbstractCollection <init> ()V 10 1 java/lang/Object <init> ()V 5 34 org/jetbrains/kotlin/protobuf/SmallSortedMap$EntrySet iterator ()Ljava/util/Iterator; 6 9 org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator <init> (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;Lorg/jetbrains/kotlin/protobuf/SmallSortedMap$1;)V 7 2 org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator <init> (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;)V 8 6 java/lang/Object <init> ()V 4 27 org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator hasNext ()Z 5 10 org/jetbrains/kotlin/protobuf/SmallSortedMap access$400 (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;)Ljava/util/List; 5 13 java/util/Collections$EmptyList size ()I 5 13 java/util/ArrayList size ()I 5 22 org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator getOverflowIterator ()Ljava/util/Iterator; 6 12 org/jetbrains/kotlin/protobuf/SmallSortedMap access$600 (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;)Ljava/util/Map; 6 15 java/util/Collections$EmptyMap entrySet ()Ljava/util/Set; 7 0 java/util/Collections emptySet ()Ljava/util/Set; 6 20 java/util/Collections$EmptySet iterator ()Ljava/util/Iterator; 7 0 java/util/Collections emptyIterator ()Ljava/util/Iterator; 5 25 java/util/Collections$EmptyIterator hasNext ()Z 4 40 org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator next ()Ljava/lang/Object; 5 1 org/jetbrains/kotlin/protobuf/SmallSortedMap$EntryIterator next ()Ljava/util/Map$Entry; 6 20 org/jetbrains/kotlin/protobuf/SmallSortedMap access$400 (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;)Ljava/util/List; 6 23 java/util/ArrayList size ()I 6 35 org/jetbrains/kotlin/protobuf/SmallSortedMap access$400 (Lorg/jetbrains/kotlin/protobuf/SmallSortedMap;)Ljava/util/List; 6 42 java/util/ArrayList get (I)Ljava/lang/Object; 7 5 java/util/Objects checkIndex (II)I 7 11 java/util/ArrayList elementData (I)Ljava/lang/Object; 1 30 org/jetbrains/kotlin/protobuf/CodedOutputStream writeInt32 (II)V 2 3 org/jetbrains/kotlin/protobuf/CodedOutputStream writeTag (II)V 3 3 org/jetbrains/kotlin/protobuf/WireFormat makeTag (II)I 3 6 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawVarint32 (I)V 4 9 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (I)V 5 3 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (B)V 2 8 org/jetbrains/kotlin/protobuf/CodedOutputStream writeInt32NoTag (I)V 3 6 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawVarint32 (I)V 4 22 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (I)V 5 3 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (B)V 4 9 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (I)V 5 3 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (B)V 1 40 java/util/Collections$EmptyList size ()I 1 40 java/util/Collections$UnmodifiableCollection size ()I 2 4 java/util/ArrayList size ()I 1 55 java/util/Collections$UnmodifiableList get (I)Ljava/lang/Object; 2 5 java/util/ArrayList get (I)Ljava/lang/Object; 3 5 java/util/Objects checkIndex (II)I 3 11 java/util/ArrayList elementData (I)Ljava/lang/Object; 1 63 org/jetbrains/kotlin/protobuf/CodedOutputStream writeMessage (ILorg/jetbrains/kotlin/protobuf/MessageLite;)V 2 3 org/jetbrains/kotlin/protobuf/CodedOutputStream writeTag (II)V 3 3 org/jetbrains/kotlin/protobuf/WireFormat makeTag (II)I 3 6 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawVarint32 (I)V 4 9 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (I)V 5 3 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (B)V 2 8 org/jetbrains/kotlin/protobuf/CodedOutputStream writeMessageNoTag (Lorg/jetbrains/kotlin/protobuf/MessageLite;)V 3 2 org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument getSerializedSize ()I 4 30 org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection getNumber ()I 4 33 org/jetbrains/kotlin/protobuf/CodedOutputStream computeEnumSize (II)I 5 1 org/jetbrains/kotlin/protobuf/CodedOutputStream computeTagSize (I)I 6 2 org/jetbrains/kotlin/protobuf/WireFormat makeTag (II)I 6 5 org/jetbrains/kotlin/protobuf/CodedOutputStream computeRawVarint32Size (I)I 5 5 org/jetbrains/kotlin/protobuf/CodedOutputStream computeEnumSizeNoTag (I)I 6 1 org/jetbrains/kotlin/protobuf/CodedOutputStream computeInt32SizeNoTag (I)I 7 5 org/jetbrains/kotlin/protobuf/CodedOutputStream computeRawVarint32Size (I)I 4 54 org/jetbrains/kotlin/protobuf/CodedOutputStream computeMessageSize (ILorg/jetbrains/kotlin/protobuf/MessageLite;)I 5 1 org/jetbrains/kotlin/protobuf/CodedOutputStream computeTagSize (I)I 6 2 org/jetbrains/kotlin/protobuf/WireFormat makeTag (II)I 6 5 org/jetbrains/kotlin/protobuf/CodedOutputStream computeRawVarint32Size (I)I 5 5 org/jetbrains/kotlin/protobuf/CodedOutputStream computeMessageSizeNoTag (Lorg/jetbrains/kotlin/protobuf/MessageLite;)I 6 8 org/jetbrains/kotlin/protobuf/CodedOutputStream computeRawVarint32Size (I)I 4 85 org/jetbrains/kotlin/protobuf/LiteralByteString size ()I 3 7 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawVarint32 (I)V 4 22 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (I)V 5 3 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (B)V 4 9 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (I)V 5 3 org/jetbrains/kotlin/protobuf/CodedOutputStream writeRawByte (B)V 3 12 org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument writeTo (Lorg/jetbrains/kotlin/protobuf/CodedOutputStream;)V 4 1 org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument getSerializedSize ()I 5 30 org/jetbrains/kotlin/metadata/ProtoBuf$Type$Argument$Projection getNumber ()I 5 33 org/jetbrains/kotlin/protobuf/CodedOutputStream computeEnumSize (II)I 6 1 org/jetbrains/kotlin/protobuf/CodedOutputStream computeTagSize (I)I 7 2 org/jetbrains/kotlin/protobuf/WireFormat makeTag (II)I 7 5 org/jetbrains/kotlin/protobuf/CodedOutputStream computeRawVarint32Size (I)I 6 5 org/jetbrains/kotlin/protobuf/CodedOutputStream computeEnumSizeNoTag (I)I 7 1 org/jetbrains/kotlin/protobuf/CodedOutputStream computeInt32SizeNoTag (I)I 8 5 org/jetbrains/kotlin/protobuf/CodedOutputStream computeRawVarint32Size (I)I
