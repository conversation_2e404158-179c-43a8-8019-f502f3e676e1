/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : DataSyncUtil.kt
 * * Description : 处理同步逻辑类，其中包含跟处理文件管理被清除数据和dmp被清除数据的初始化逻辑
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.util

import android.content.Context
import android.util.Log
import com.coloros.filemanager.appswitch.AppSwitchManager
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi

object DataSyncUtil {

    const val TAG = "DataSyncUtil"

    @JvmStatic
    fun doSyncDataBetweenDmpAndFileManager(context: Context) {
        val dmpSearchApi = Injector.injectFactory<IDmpSearchApi>()
        if (dmpSearchApi == null) {
            Log.w(TAG, "onSyncDataMethodCalled $dmpSearchApi is null, return")
            return
        }
        val needInit = InitHelper.checkNeedInit(context)
        if (!needInit) {
            Log.w(TAG, "onSyncDataMethodCalled needInit false, return")
            return
        }
        val serviceManagerAvailable = dmpSearchApi.isSearchAvalable()
        Log.i(TAG, "onSyncDataMethodCalled serviceManagerAvailable $serviceManagerAvailable")
        if (serviceManagerAvailable) {
            val initSuc = MyApplication.dmpAndIndexInitSuc
            val hasIndexConfig = dmpSearchApi.hasIndexConfig()
            Log.i(TAG, "onSyncDataMethodCalled initSuc $initSuc, hasIndexConfig $hasIndexConfig")
            if (initSuc && dmpSearchApi.checkAndPreprocessIndex()) {
                //这里有一种特殊情况，文管进程存在，dmp被清除数据后，需要判定index内部状态IndexState是否ok，ok才能直接操作，不行则走下面的重新初始化
                Log.i(TAG, "onSyncDataMethodCalled doIndexSyncWork directly")
                AppSwitchManager.doIndexSyncWork()
            } else {
                //文管被清除数据之后，会走这个分支
                val dmpInitSuc = InitHelper.initDmpSdkAndIndexAndCheckV3(context)
                if (dmpInitSuc) {
                    Log.w(TAG, "onSyncDataMethodCalled do syncWork")
                    AppSwitchManager.doIndexSyncWork()
                }
            }
        } else {
            //非Avalable的情况包含dmp版本和dmpsdk不兼容，dmpapk未安装等，这些错误都是不可逆，不需要再重新初始化，整体功能不可用
            Log.e(
                TAG,
                "onSyncDataMethodCalled serviceManager not Available, no need to do syncWork"
            )
        }
    }
}