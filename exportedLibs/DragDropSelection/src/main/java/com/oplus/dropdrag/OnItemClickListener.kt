/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - SelectionTracker.kt
 * Description: abstract base for Selection Tracker
 * Version: 1.0
 * Date : 2020/05/15
 * Author: Ji<PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/05/15    1.0     create
 ***********************************************************************/
package com.oplus.dropdrag

import android.view.MotionEvent
import androidx.annotation.Keep
import com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails

@Keep
interface OnItemClickListener<K> {
    /**
     * Called when an item is "activated". An item is activated, for example, when no selection
     * exists and the user taps an item with her finger, or double clicks an item with a
     * pointing device like a Mouse.
     *
     * @param item details of the item.
     * @param e the event associated with item.
     *
     * @return true if the event was handled.
     */
    fun onItemClick(item: ItemDetails<K>, e: MotionEvent): Boolean
}