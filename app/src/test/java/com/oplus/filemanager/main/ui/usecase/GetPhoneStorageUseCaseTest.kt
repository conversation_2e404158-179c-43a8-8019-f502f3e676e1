/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GetPhoneStorageUseCaseTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/3/7 16:42
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/3/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.main.ui.usecase

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.main.ui.uistate.Storage
import io.mockk.MockKAnnotations
import io.mockk.mockk
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class GetPhoneStorageUseCaseTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
    }

    @Test
    fun `get phone storage when internal sd not mounted`() = runBlocking {
        val getPhoneStorageUseCase = GetPhoneStorageUseCase()
        val storageItems = mutableListOf<Storage>()
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.isInternalSdMounted(context) }.returns(false)
        getPhoneStorageUseCase.invoke(storageItems)
        Assert.assertEquals(0, storageItems.size)
    }

    @Test
    fun `get phone storage when internal sd have been mounted`() = runBlocking {
        val getPhoneStorageUseCase = GetPhoneStorageUseCase()
        val storageItems = mutableListOf<Storage>()
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.isInternalSdMounted(context) }.returns(true)
        every { VolumeEnvironment.getInternalSdPath(context) }.returns(INTERNAL_PATH)
        mockkStatic(Utils::class)
        every { Utils.getStorageTotalSizeAfterFormat(any()) }.returns(TOTAL_SIZE)
        every { Utils.getStorageAvailableSize(any()) }.returns(AVAILABLE_SIZE)
        getPhoneStorageUseCase.invoke(storageItems)
        Assert.assertEquals(1, storageItems.size)
        val storage = storageItems[0]
        Assert.assertTrue(storage is Storage.PhoneStorage)
        storage as Storage.PhoneStorage
        Assert.assertEquals(TOTAL_SIZE, storage.phoneTotalSize)
        Assert.assertEquals(AVAILABLE_SIZE, storage.phoneAvailableSize)
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    companion object {
        private const val INTERNAL_PATH = "/test/InternalSdPath"
        private const val TOTAL_SIZE = 1000L
        private const val AVAILABLE_SIZE = 400L
    }
}