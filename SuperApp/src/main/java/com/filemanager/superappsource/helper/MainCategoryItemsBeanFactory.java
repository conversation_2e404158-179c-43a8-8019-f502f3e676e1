/***********************************************************
 * * Copyright (C), 2008-2017 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File:MainCategoryItemsBeanFactory.java
 * * Description:
 * * Version:1.0
 * * Date :2019/6/13
 * * Author:********
 * * OPLUS Java File Skip Rule:MethodLength,DeclarationOrder
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *
 ****************************************************************/
package com.filemanager.superappsource.helper;

import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CATEGORY_APK;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CATEGORY_ARCHIVE;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CATEGORY_AUDIO;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CATEGORY_DOC;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CATEGORY_PIC;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CATEGORY_VIDEO;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_CLOUD_DISK;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_PARENT_CHILD_CATEGORY_APK;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_PARENT_CHILD_CATEGORY_ARCHIVE;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_PARENT_CHILD_CATEGORY_AUDIO;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_PARENT_CHILD_CATEGORY_DOC;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_PARENT_CHILD_CATEGORY_PIC;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_PARENT_CHILD_CATEGORY_VIDEO;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_PARENT_CHILD_CLOUD_ICON;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_PARENT_CHILD_OTG_ICON;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_PARENT_CHILD_SDCARD_ICON;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.IC_PRIVATE_SAFE;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.MAIN_CATEGORY_APK_BG;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.MAIN_CATEGORY_AUDIO_BG;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.MAIN_CATEGORY_COMPRESS_BG;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.MAIN_CATEGORY_DOCUMENT_BG;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.MAIN_CATEGORY_PICTURE_BG;
import static com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.MAIN_CATEGORY_VIDEO_BG;

import android.content.Context;

import com.filemanager.common.helper.CategoryHelper;
import com.filemanager.common.constants.Constants;
import com.filemanager.common.utils.Utils;
import com.filemanager.superapp.R;

public class MainCategoryItemsBeanFactory {
    //分类顺序不能修改，只能在后边添加
    public static final int[] CATEGORY = new int[]{
            CategoryHelper.CATEGORY_IMAGE, CategoryHelper.CATEGORY_VIDEO,
            CategoryHelper.CATEGORY_AUDIO, CategoryHelper.CATEGORY_DOC,
            CategoryHelper.CATEGORY_APK, CategoryHelper.CATEGORY_COMPRESS};
    public static final int[] CATEGORY_ITEM_ICON = new int[]{
            R.drawable.ic_category_pic, R.drawable.ic_category_video,
            R.drawable.ic_category_audio, R.drawable.ic_category_doc,
            R.drawable.ic_category_apk, R.drawable.ic_category_archive};
    public static final int[] CATEGORY_BG_ITEM_ICON = new int[]{
            com.filemanager.common.R.drawable.main_category_picture_bg,
            com.filemanager.common.R.drawable.main_category_vedio_bg,
            com.filemanager.common.R.drawable.main_category_audio_bg,
            com.filemanager.common.R.drawable.main_category_document_bg,
            com.filemanager.common.R.drawable.main_category_apk_bg,
            com.filemanager.common.R.drawable.main_category_compress_bg
    };
    public static final String[] LAST_RECORD_COUNT = {
            Constants.RECORD_LAST_IMAGE_COUNT, Constants.RECORD_LAST_VIDEO_COUNT,
            Constants.RECORD_LAST_AUDIO_COUNT, Constants.RECORD_LAST_DOC_COUNT,
            Constants.RECORD_LAST_APK_COUNT, Constants.RECORD_LAST_COMPRESS_COUNT,
            Constants.RECORD_LAST_DOWNLOAD_COUNT, Constants.RECORD_LAST_CLOUD_COUNT,
            Constants.RECORD_LAST_QQ_COUNT, Constants.RECORD_LAST_MICROMSG_COUNT,
            Constants.RECORD_LAST_APK_NINE_COUNT, Constants.RECORD_LAST_APK_TEN_COUNT,
            Constants.RECORD_LAST_APK_ELEVEN_COUNT, Constants.RECORD_LAST_OAPS_COUNT,
            Constants.RECORD_LAST_APP_INSTALL_COUNT
    };
    public static final String[] LAST_RECORD_SIZE = {
            Constants.RECORD_LAST_IMAGE_SIZE, Constants.RECORD_LAST_VIDEO_SIZE,
            Constants.RECORD_LAST_AUDIO_SIZE, Constants.RECORD_LAST_DOC_SIZE,
            Constants.RECORD_LAST_APK_SIZE, Constants.RECORD_LAST_COMPRESS_SIZE,
            Constants.RECORD_LAST_DOWNLOAD_SIZE, Constants.RECORD_LAST_CLOUD_SIZE,
            Constants.RECORD_LAST_QQ_SIZE, Constants.RECORD_LAST_MICROMSG_SIZE,
            Constants.RECORD_LAST_APK_NINE_SIZE, Constants.RECORD_LAST_APK_TEN_SIZE,
            Constants.RECORD_LAST_APK_ELEVEN_SIZE, Constants.RECORD_LAST_OAPS_SIZE,
            Constants.RECORD_LAST_APP_INSTALL_SIZE
    };

    public static long[] getCategoryItemLastData(int position, Context context) {
        long[] data = {0, 0};
        if ((position >= 0)) {
            data[0] = Utils.getCategoryItemLastRecord(context, LAST_RECORD_COUNT[position]);
            data[1] = Utils.getCategoryItemLastRecord(context, LAST_RECORD_SIZE[position]);
        }
        return data;
    }

    public static final int[] PARENT_CATEGORY_ITEM_ICON = new int[]{
            com.filemanager.common.R.drawable.ic_parent_child_category_pic, com.filemanager.common.R.drawable.ic_parent_child_category_video,
            com.filemanager.common.R.drawable.ic_parent_child_category_audio, com.filemanager.common.R.drawable.ic_parent_child_category_doc,
            com.filemanager.common.R.drawable.ic_parent_child_category_apk, com.filemanager.common.R.drawable.ic_parent_child_category_archive};

    public static int getCategoryIcon(int category) {
        switch (category) {
            case IC_CATEGORY_PIC: {
                return R.drawable.ic_category_pic;
            }
            case IC_CATEGORY_VIDEO: {
                return R.drawable.ic_category_video;
            }
            case IC_CATEGORY_AUDIO: {
                return R.drawable.ic_category_audio;
            }
            case IC_CATEGORY_DOC: {
                return R.drawable.ic_category_doc;
            }
            case IC_CATEGORY_APK: {
                return R.drawable.ic_category_apk;
            }
            case IC_CATEGORY_ARCHIVE: {
                return R.drawable.ic_category_archive;
            }
            case MAIN_CATEGORY_PICTURE_BG: {
                return com.filemanager.common.R.drawable.main_category_picture_bg;
            }
            case MAIN_CATEGORY_VIDEO_BG: {
                return com.filemanager.common.R.drawable.main_category_vedio_bg;
            }
            case MAIN_CATEGORY_AUDIO_BG: {
                return com.filemanager.common.R.drawable.main_category_audio_bg;
            }
            case MAIN_CATEGORY_DOCUMENT_BG: {
                return com.filemanager.common.R.drawable.main_category_document_bg;
            }
            case MAIN_CATEGORY_COMPRESS_BG: {
                return com.filemanager.common.R.drawable.main_category_compress_bg;
            }
            case MAIN_CATEGORY_APK_BG: {
                return com.filemanager.common.R.drawable.main_category_apk_bg;
            }
            case IC_PARENT_CHILD_CATEGORY_PIC: {
                return com.filemanager.common.R.drawable.ic_parent_child_category_pic;
            }
            case IC_PARENT_CHILD_CATEGORY_VIDEO: {
                return com.filemanager.common.R.drawable.ic_parent_child_category_video;
            }
            case IC_PARENT_CHILD_CATEGORY_AUDIO: {
                return com.filemanager.common.R.drawable.ic_parent_child_category_audio;
            }
            case IC_PARENT_CHILD_CATEGORY_DOC: {
                return com.filemanager.common.R.drawable.ic_parent_child_category_doc;
            }
            case IC_PARENT_CHILD_CATEGORY_APK: {
                return com.filemanager.common.R.drawable.ic_parent_child_category_apk;
            }
            case IC_PARENT_CHILD_CATEGORY_ARCHIVE: {
                return com.filemanager.common.R.drawable.ic_parent_child_category_archive;
            }
            case IC_PARENT_CHILD_CLOUD_ICON: {
                return R.drawable.ic_parent_child_cloud_icon;
            }
            case IC_PARENT_CHILD_OTG_ICON: {
                return R.drawable.ic_parent_child_otg_icon;
            }
            case IC_PARENT_CHILD_SDCARD_ICON: {
                return R.drawable.ic_parent_child_sdcard_icon;
            }
            case IC_CLOUD_DISK: {
                return R.drawable.ic_cloud_disk;
            }
            case IC_PRIVATE_SAFE: {
                return R.drawable.ic_private_safe;
            }
            default: {
                return -1;
            }
        }
    }
}
