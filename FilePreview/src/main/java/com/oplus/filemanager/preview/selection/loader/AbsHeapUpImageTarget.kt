/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AbsHeapUpImageTarget.kt
 * Description:
 *     The base implementation about receive loaded preview image from loader
 *
 * Version: 1.0
 * Date: 2024-10-19
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Bix<PERSON><PERSON>.<EMAIL>    2024-10-19   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection.loader

import android.content.Context
import android.graphics.drawable.Drawable
import androidx.annotation.DrawableRes
import androidx.appcompat.content.res.AppCompatResources
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.selection.data.HeadUpImageData
import com.oplus.filemanager.preview.utils.GlideExtend
import com.oplus.filemanager.preview.utils.logInfo

internal abstract class AbsHeapUpImageTarget<T : Any>(
    protected val context: Context,
    val fileBean: BaseFileBean,
    @DrawableRes private val defaultPlaceholderRes: Int,
    private val isAlwaysIcon: Boolean = false,
    private val isVideoType: Boolean = false
) : CustomTarget<T>(
    context.resources.getDimensionPixelOffset(R.dimen.preview_image_max_width),
    context.resources.getDimensionPixelOffset(R.dimen.preview_image_max_height)
) {

    val imageData: LiveData<HeadUpImageData>
        get() = _imageData
    private val _imageData = MutableLiveData<HeadUpImageData>()

    /**
     * The log tag
     */
    protected abstract val logTag: String

    private val trackedLifecycle = mutableSetOf<LifecycleOwner>()

    private val defaultIconImageData by lazy {
        val drawable = AppCompatResources.getDrawable(context, defaultPlaceholderRes)
        HeadUpImageData(
            fileBean = fileBean,
            drawable = drawable,
            isIcon = true,
            isVideoType = isVideoType,
            isLoading = true
        )
    }

    @Volatile
    private var isReleased = false

    init {
        _imageData.value = defaultIconImageData
    }

    private fun applyImageData(newData: HeadUpImageData) {
        if (context.mainLooper.isCurrentThread) {
            _imageData.value = newData
            return
        }
        context.mainExecutor.execute { _imageData.value = newData }
    }

    fun observe(lifecycleOwner: LifecycleOwner, observer: Observer<HeadUpImageData>) {
        if (isReleased) {
            return
        }
        trackedLifecycle.add(lifecycleOwner)
        imageData.observe(lifecycleOwner, observer)
    }

    fun releaseObserver(observer: Observer<HeadUpImageData>) {
        if (isReleased) {
            return
        }
        imageData.removeObserver(observer)
    }

    fun clearObservers() {
        trackedLifecycle.forEach {
            imageData.removeObservers(it)
        }
        trackedLifecycle.clear()
    }

    fun release() {
        if (isReleased) {
            return
        }
        Log.d(logTag, "release: ${fileBean.logInfo()}")
        isReleased = true
        clearObservers()
        applyImageData(defaultIconImageData)
        GlideExtend.with(context)?.clear(this)
    }

    fun loadWithPlaceholder(placeholder: Drawable? = null) {
        if (isReleased) {
            return
        }
        Log.d(logTag, "loadWithPlaceholder: ${fileBean.logInfo()}")
        val drawable = placeholder ?: AppCompatResources.getDrawable(context, defaultPlaceholderRes)
        val newData = HeadUpImageData(
            fileBean = fileBean,
            drawable = drawable,
            isIcon = true,
            isVideoType = isVideoType,
            isLoading = false
        )
        applyImageData(newData)
    }

    override fun onResourceReady(resource: T, transition: Transition<in T>?) {
        if (isReleased) {
            return
        }
        val drawable = obtainResourceDrawable(resource)
        if (drawable == null) {
            Log.e(logTag, "onResourceReady: Failed to obtain drawable")
            onLoadFailed(null)
            return
        }
        onResourceReady(drawable, obtainResourceSizeInfo(resource))
    }

    /**
     * Implement to create drawable with [T]
     */
    protected abstract fun obtainResourceDrawable(resource: T): Drawable?

    /**
     * Implement to get size of [T]
     */
    protected abstract fun obtainResourceSizeInfo(resource: T): HeadUpImageData.ImageSizeInfo?

    fun onResourceReady(drawable: Drawable, sizeInfo: HeadUpImageData.ImageSizeInfo? = null) {
        if (isReleased) {
            return
        }
        Log.d(logTag, "onResourceReady: ${fileBean.logInfo()}")
        val newData = HeadUpImageData(
            fileBean = fileBean,
            drawable = drawable,
            isIcon = isAlwaysIcon,
            isLoading = false,
            isVideoType = isVideoType,
            imageSize = sizeInfo
        )
        applyImageData(newData)
    }

    override fun onLoadFailed(errorDrawable: Drawable?) {
        Log.d(logTag, "onLoadFailed: ${fileBean.logInfo()}")
        loadWithPlaceholder(errorDrawable)
    }

    override fun onLoadCleared(placeholder: Drawable?) {
        Log.d(logTag, "onLoadCleared: ${fileBean.logInfo()}")
        loadWithPlaceholder(placeholder)
    }
}