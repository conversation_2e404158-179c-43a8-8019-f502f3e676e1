/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileOperationService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/29 10:53
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/6/29       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.os.Messenger
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.HansFreezeManager

/**
 * [FileOperateService] provides an entry for websites to operate files.
 *
 * It must register in file_service.xml, and registration by Service Platform.
 */
class FileOperateService : Service() {

    private var mOperateHandler: FileOperateServiceHandler? = null
    private var mAllowRebind: Boolean = false

    override fun onCreate() {
        Log.d(TAG, "onCreate")
        super.onCreate()
    }

    override fun onBind(intent: Intent?): IBinder {
        Log.d(TAG, "onBind")
        HansFreezeManager.instance.keepBackgroundRunning()
        mOperateHandler = FileOperateServiceHandler(this.applicationContext)
        return Messenger(mOperateHandler).binder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Log.d(TAG, "onUnbind")
        HansFreezeManager.instance.cancelFrozenDelay()
        mOperateHandler = null
        return mAllowRebind
    }

    override fun onRebind(intent: Intent?) {
        Log.d(TAG, "onRebind -> ${intent?.extras}")
        super.onRebind(intent)
    }

    override fun onDestroy() {
        Log.d(TAG, "onDestroy")
        super.onDestroy()
    }

    companion object {
        private const val TAG = "FileOperateService"
    }
}