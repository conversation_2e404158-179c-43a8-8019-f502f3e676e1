/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - ModernFileStore.java
 * Description: ModernFileStore.java
 * Version: 1.0
 * Date : 2020/02/24
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/02/24    1.0     create
 ****************************************************************/

package com.filemanager.recyclebin.retriever

import android.content.ContentValues
import android.net.Uri

class ModernDataRetriever internal constructor() : DataRetriever() {
    override fun deleteQuietly(originPath: String): Bo<PERSON>an {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun deleteQuietly(originUri: Uri): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun isInternalUri(uri: Uri?): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun isInternalPath(path: String?): Boolean {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }

    override fun extractContentValues(uri: Uri): ContentValues? {
        return null
    }

    override fun extractContentValues(path: String): ContentValues? {
        return null
    }

    override fun moveToRecycleBin(originPath: String, recyclePath: String): Int {
        TODO("not implemented")
    }

    override fun moveToRecycleBin(originUri: Uri, recyclePath: String): Int {
        TODO("not implemented")
    }

    override fun restoreToOriginPath(values: ContentValues): Int {
        TODO("not implemented") //To change body of created functions use File | Settings | File Templates.
    }
}
