/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LabelCardSettingVMTest
 ** Description : 标签设置的ViewModel 的单元测试
 ** Version     : 1.0
 ** Date        : 2022/12/12
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue      2022/12/12      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.dialog

import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.oplus.filemanager.cardwidget.label.LabelCardWidgetCodeUtils
import com.oplus.filemanager.cardwidget.label.data.LabelCardCacheData
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import com.oplus.filemanager.room.model.FileLabelMappingEntity
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test

class LabelCardSettingVMTest {

    companion object {
        const val CARD_TYPE_CODE = 222220101
        const val CARD_CODE = "222220101&1&0"
    }

    private lateinit var settingVM: LabelCardSettingVM

    @Before
    fun setup() {
        val context = mockk<Context>(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
        settingVM = mockk<LabelCardSettingVM>().apply {
            every { cardWidgetCode }.answers { callOriginal() }
            every { cardWidgetCode = any() }.answers { callOriginal() }
            every { originLabel }.answers { callOriginal() }
            every { originLabel = any() }.answers { callOriginal() }
        }
        mockkObject(FileLabelDBHelper)
        mockkObject(FileLabelMappingDBHelper)
        mockkStatic(SortModeUtils::class)
        mockkObject(SortHelper)
    }

    @Test
    fun teardown() {
        unmockkObject(FileLabelDBHelper)
        unmockkObject(FileLabelMappingDBHelper)
        unmockkObject(SortModeUtils::class)
        unmockkObject(SortHelper)
    }

    @Test
    fun should_return_boolean_when_isNoLabel() {
        Assert.assertTrue(LabelCardSettingVM.isNoLabel(null))
        Assert.assertTrue(LabelCardSettingVM.isNoLabel(0L))
        Assert.assertFalse(LabelCardSettingVM.isNoLabel(1L))
    }

    @Test
    fun should_call_loadLabelCardData_when_setCardWidgetCode() {
        val labelId = MutableLiveData<Long>()
        every { settingVM.setCardWidgetCode(any(), any(), any()) }.answers { callOriginal() }
        every { settingVM.getLabelId(any()) }.returns(1L)
        every { settingVM.labelId }.returns(labelId)
        justRun { settingVM.labelId.value = any() }
        justRun { settingVM.loadLabelCardData(any()) }
        settingVM.setCardWidgetCode(CARD_TYPE_CODE, 1, 0)
        Assert.assertEquals(settingVM.cardWidgetCode, "$CARD_TYPE_CODE&1&0")
        Assert.assertEquals(settingVM.originLabel, 1L)

        every { settingVM.getLabelId(any()) }.returns(0L)
        settingVM.setCardWidgetCode(CARD_TYPE_CODE, 1, 0)
        Assert.assertEquals(settingVM.originLabel, 0L)
        verify { settingVM.loadLabelCardData(any()) }
    }


    @Test
    fun should_return_long_when_getLabelId() {
        val codeUtils = mockk<LabelCardWidgetCodeUtils>()
        every { settingVM.widgetCodeUtils }.returns(codeUtils)
        every { settingVM.getLabelId(any()) }.answers { callOriginal() }
        every { codeUtils.getCacheByWidgetCode(any()) }.returns(null)
        Assert.assertEquals(0, settingVM.getLabelId(CARD_CODE))

        every { codeUtils.getCacheByWidgetCode(any()) }.returns(LabelCardCacheData(CARD_CODE, null, LabelCardCacheData.STATUS_INIT))
        Assert.assertEquals(0, settingVM.getLabelId(CARD_CODE))

        every { codeUtils.getCacheByWidgetCode(any()) }.returns(LabelCardCacheData(CARD_CODE, 1, LabelCardCacheData.STATUS_INIT))
        Assert.assertEquals(1, settingVM.getLabelId(CARD_CODE))
    }

    @Ignore
    fun should_return__when_queryLabelCardData() {
        every { FileLabelDBHelper.getFileLabelById(any()) }.returns(null)
        every { settingVM.queryLabelCardData(any()) }.answers { callOriginal() }
        every { settingVM.queryDefaultLabel() }.returns(null)
        var cardData = settingVM.queryLabelCardData(1)
        Assert.assertEquals(0, cardData.labelId)
        Assert.assertEquals(0, cardData.fileCount)

        every { settingVM.queryDefaultLabel() }.returns(FileLabelEntity(1, "标签", 1, 1, 2L, 2L))
        every { FileLabelMappingDBHelper.getFileListByLabelId(any()) }.returns(null)
        cardData = settingVM.queryLabelCardData(1)
        Assert.assertEquals(0, cardData.labelId)
        Assert.assertEquals("", cardData.labelName)
        Assert.assertEquals(0, cardData.fileCount)

        every { FileLabelDBHelper.getFileLabelById(any()) }.returns(FileLabelEntity(1, "标签", 1, 1, 2L, 2L))
        every { FileLabelMappingDBHelper.getFileListByLabelId(any()) }.returns(null)
        cardData = settingVM.queryLabelCardData(1)
        Assert.assertEquals(1, cardData.labelId)
        Assert.assertEquals("标签", cardData.labelName)
        Assert.assertEquals(0, cardData.fileCount)

        val list = mutableListOf<FileLabelMappingEntity>()
        list.add(FileLabelMappingEntity(1, 1, "/sdcard/DCIM/1.png", MimeTypeHelper.IMAGE_TYPE, "png", 0, 0))
        list.add(FileLabelMappingEntity(2, 1, "/sdcard/Document/2.txt", MimeTypeHelper.TXT_TYPE, "txt", 0, 0))
        every { FileLabelMappingDBHelper.getFileListByLabelId(any()) }.returns(list)
        every { SortModeUtils.getSharedSortMode(any(), any()) }.returns(SortHelper.FILE_NAME_ORDER)
        every { SortModeUtils.getSharedSortOrder(any()) }.returns(true)
        justRun { SortHelper.sortFiles(any(), any(), any(), any(), any()) }
        cardData = settingVM.queryLabelCardData(1)
        Assert.assertEquals(1, cardData.labelId)
        Assert.assertEquals("标签", cardData.labelName)
        Assert.assertEquals(2, cardData.fileCount)
    }

    @Test
    fun should_when_queryDefaultLabel() {
        every { settingVM.queryDefaultLabel() }.answers { callOriginal() }
        val list = mutableListOf<FileLabelEntity>()
        every { FileLabelDBHelper.getAllLabels() }.returns(list)
        var label = settingVM.queryDefaultLabel()
        Assert.assertNull(label)

        list.add(FileLabelEntity(1, "标签", 0, 0, 0, 0))
        list.add(FileLabelEntity(2, "测试", 0, 0, 0, 0))
        label = settingVM.queryDefaultLabel()
        Assert.assertNotNull(label)
        Assert.assertEquals(label?.id, 1L)
    }

    @Test
    fun should_call_switchLabel_when_saveLabelCardMapping() {
        every { settingVM.saveLabelCardMapping() }.answers { callOriginal() }
        val codeUtils = mockk<LabelCardWidgetCodeUtils>()
        every { settingVM.widgetCodeUtils }.returns(codeUtils)
        justRun { codeUtils.switchLabel(any(), any()) }
        justRun { codeUtils.deleteCardLabelMapping(any(), any()) }
        every { settingVM.labelId }.returns(MutableLiveData<Long>(1))
        settingVM.originLabel = 1
        settingVM.saveLabelCardMapping()
        verify(inverse = true) { codeUtils.switchLabel(any(), any()) }

        every { settingVM.labelId }.returns(MutableLiveData<Long>(0))
        settingVM.originLabel = 1
        settingVM.saveLabelCardMapping()
        verify { codeUtils.deleteCardLabelMapping(any(), any()) }

        every { settingVM.labelId }.returns(MutableLiveData<Long>(2))
        settingVM.originLabel = 1
        settingVM.saveLabelCardMapping()
        verify { codeUtils.switchLabel(any(), any()) }
    }

    @Test
    fun should_when_queryAllLabels() {
        every { settingVM.queryAllLabels() }.answers { callOriginal() }
        val labels = mutableListOf<FileLabelEntity>()
        every { FileLabelDBHelper.getAllLabels() }.returns(labels)
        var list = settingVM.queryAllLabels()
        Assert.assertTrue(list.isEmpty())

        labels.add(FileLabelEntity(1, "标签", 1, 1, 2, 3))
        list = settingVM.queryAllLabels()
        Assert.assertTrue(list.isNotEmpty())
    }

    @Test
    fun should_when_selectLabel() {
        every { settingVM.selectLabel(any()) }.answers { callOriginal() }
        justRun { settingVM.loadLabelCardData(any()) }
        val labelId = mockk<MutableLiveData<Long>>()
        every { labelId.value }.returns(1)
        justRun { labelId.value = any() }
        every { settingVM.labelId }.returns(labelId)
        settingVM.selectLabel(1)
        verify(inverse = true) { settingVM.loadLabelCardData(any()) }

        settingVM.selectLabel(2)
        verify { settingVM.loadLabelCardData(2) }
    }
}