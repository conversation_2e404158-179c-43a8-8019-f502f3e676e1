/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DownloadTaskInfo
 * * Description : Download TaskInfo
 * * Version     : 1.0
 * * Date        : 2024/12/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download.service

import android.app.PendingIntent
import android.content.Context
import androidx.annotation.Keep
import com.filemanager.common.constants.MessageConstant
import com.oplus.filemanager.category.remotedevice.download.notification.DownloadNotifyManager
import java.io.File

@Keep
data class DownloadTaskInfo(
    var deviceId: String,
    val downloadFiles: ArrayList<Pair<String, Long>>,
    val destPath: String,
    val code: Int = MessageConstant.MSG_DOWNLOAD_REMOTE_FILE,
    var taskId: Int = -1,
    var pendingIntent: PendingIntent? = null,
) {

    companion object {

        fun remotePCActivityIntent(context: Context, path: String): PendingIntent {
            val intent = DownloadNotifyManager.jumpRemotePCActivityIntent(path)
            return PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
        }
    }
    fun paths(): List<String> {
        return downloadFiles.map { it.first }
    }

    /**
     * 获取下载后文件路径
     */
    fun localPaths(): List<String> {
        return downloadFiles.map {
            val srcPath = it.first
            val srcFile = File(srcPath)
            val destFile = File(destPath, srcFile.name)
            destFile.absolutePath
        }
    }
}