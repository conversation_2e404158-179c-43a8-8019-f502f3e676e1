/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ApkIconLoader
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/5/26 11:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/5/26       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.utils

import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.LightingColorFilter
import android.graphics.drawable.Drawable
import com.filemanager.common.compat.CompatUtils
import com.filemanager.common.utils.AppInfo
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.Log

private const val TAG = "ApkIconLoader"
private const val RGB_BPP = 255
private const val BIT_16 = 16
private const val DEFAULT_RELATIVE_BRIGHTNESS_R_VERSION = 0.84f

@Suppress("TooGenericExceptionCaught")
fun getAppInfoByPathIgnoreDarkTheme(context: Context, path: String): AppInfo {
    val appInfo = AppInfo()
    val pm = context.packageManager
    val packageInfo =
        pm.getPackageArchiveInfo(path, PackageManager.GET_ACTIVITIES) ?: return appInfo
    val pkgApplicationInfo = packageInfo.applicationInfo ?: return appInfo
    pkgApplicationInfo.sourceDir = path
    pkgApplicationInfo.publicSourceDir = path
    appInfo.appName = pm.getApplicationLabel(pkgApplicationInfo).toString()
    var icon: Drawable? = pm.getApplicationIcon(pkgApplicationInfo)
    var bitmap: Bitmap? = null
    if (icon == null) {
        Log.d(TAG, "getAppInfoByPathIgnoreDarkTheme -> package manager application icon is null.")
        icon = pkgApplicationInfo.loadIcon(pm)
    }
    try {
        disableDarkFilterToDrawable(icon)
    } catch (e: Exception) {
        Log.e(TAG, "getAppInfoByPathIgnoreDarkTheme -> setDarkFilterToDrawable error: ${e.message}")
    }
    icon?.let {
        bitmap = AppUtils.getBitmapFromDrawable(it)
    }
    appInfo.icon = bitmap
    appInfo.versionName = packageInfo.versionName
    appInfo.packageName = pkgApplicationInfo.packageName
    appInfo.applicationInfo = pkgApplicationInfo
    return appInfo
}

/**
 * Disable dark color filter
 */
private fun disableDarkFilterToDrawable(drawable: Drawable?) {
    if (drawable == null) {
        Log.d(TAG, "setDarkFilterToDrawable -> drawable is null.")
        return
    }

    val color = getDarkModeColorInCurrentContrast()
    val darkModeColorFilter = LightingColorFilter(color, 0)
    val colorFilter = drawable.colorFilter
    /**
     * Similar to the color Filter obtained by Adaptive Icon Drawable is always null, but the corresponding
     * child is not null
     * Therefore, it is necessary to clear the Color Filter as a whole. If other ordinary drawables are null,
     * it does not matter if they are set again.
     */
    if (colorFilter == null) {
        drawable.colorFilter = null
    } else {
        if (colorFilter is LightingColorFilter && (colorFilter.colorMultiply == darkModeColorFilter.colorMultiply)) {
            drawable.colorFilter = null
        }
    }
}

private fun getDarkModeColorInCurrentContrast(): Int {
    val defaultBrightness = CompatUtils.compactSApi({
        android.app.OplusUxIconConstants.DEFAULT_RELATIVE_BRIGHTNESS
    }, {
        DEFAULT_RELATIVE_BRIGHTNESS_R_VERSION
    })
    val currentColorR: Int = (RGB_BPP * defaultBrightness).toInt()
    val colorR = Integer.toHexString(currentColorR)
    return (colorR + colorR + colorR).toInt(BIT_16)
}