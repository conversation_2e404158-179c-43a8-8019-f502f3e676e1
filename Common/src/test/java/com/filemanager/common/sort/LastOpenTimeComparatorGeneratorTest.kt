/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LastOpenTimeComparatorGeneratorTest
 ** Description : LastOpenTimeComparatorGenerator Unit Test
 ** Version     : 1.0
 ** Date        : 2023/11/20
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  hank.zhou      2022/11/20      1.0        create
 ***********************************************************************/
package com.filemanager.common.sort

import com.filemanager.common.base.BaseFileBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.Assert
import org.junit.Test

class LastOpenTimeComparatorGeneratorTest {

    @Test
    fun should_return_correct_when_compare1() {
        val bean1 = BaseFileBean()
        val bean2 = BaseFileBean()
        val compare = LastOpenTimeComparatorGenerator()
        bean1.lastOpenTime = 11
        bean2.lastOpenTime = 14
        Assert.assertEquals(compare.compare1(bean1, bean2, true), -1)
        bean1.lastOpenTime = 11
        bean2.lastOpenTime = 1411
        Assert.assertEquals(compare.compare1(bean1, bean2, true), -1)
        bean1.lastOpenTime = 14
        bean2.lastOpenTime = 11
        Assert.assertEquals(compare.compare1(bean1, bean2, true), 1)
        bean1.lastOpenTime = 104
        bean2.lastOpenTime = 11
        Assert.assertEquals(compare.compare1(bean1, bean2, true), 1)
        bean1.lastOpenTime = 12
        bean2.lastOpenTime = 12
        Assert.assertEquals(compare.compare1(bean1, bean2, true), 0)
    }

    @Test
    fun should_return_correct_when_compare() {
        val bean1 = BaseFileBean()
        val bean2 = BaseFileBean()
        val compare = mockk<LastOpenTimeComparatorGenerator>()
        every { compare.compare(bean1, bean2) }.answers { callOriginal() }
        every { compare.compare1(bean1, bean2, true) }.returns(1)
        Assert.assertEquals(compare.compare(bean1, bean2), 1)
        every { compare.compare1(bean1, bean2, true) }.returns(0)
        Assert.assertEquals(compare.compare(bean1, bean2), 0)
        every { compare.compare1(bean1, bean2, true) }.returns(-1)
        Assert.assertEquals(compare.compare(bean1, bean2), -1)
        every { compare.compare1(bean1, bean2, true) }.returns(2)
        Assert.assertEquals(compare.compare(bean1, bean2), 2)
    }

    @Test
    fun should_return_Comparator_when_getComparator() {
        val tor = mockk<Comparator<BaseFileBean>>()
        val tor2 = mockk<Comparator<BaseFileBean>>()
        val compare = mockk<LastOpenTimeComparatorGenerator>()
        mockkObject(LastOpenTimeComparatorGenerator)
        every { LastOpenTimeComparatorGenerator.comparator }.returns(compare)
        every { compare.genComparator() }.returns(tor)
        every { compare.genReverseComparator() }.returns(tor2)
        Assert.assertEquals(LastOpenTimeComparatorGenerator.getComparator(true), tor2)
        Assert.assertEquals(LastOpenTimeComparatorGenerator.getComparator(false), tor)
        unmockkObject(LastOpenTimeComparatorGenerator)
    }
}