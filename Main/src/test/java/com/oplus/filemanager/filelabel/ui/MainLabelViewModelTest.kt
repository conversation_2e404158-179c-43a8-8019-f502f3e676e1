package com.oplus.filemanager.filelabel.ui

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import com.oplus.filemanager.room.model.FileLabelMappingEntity
import io.mockk.*
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runBlockingTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * MainLabelViewModel的单元测试类
 * 使用Robolectric和MockK框架进行测试
 */
@ExperimentalCoroutinesApi
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])  // 将SDK版本从28改为29以解决错误
class MainLabelViewModelTest {

    /**
     * 用于处理LiveData的即时执行规则
     */
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    /**
     * 测试用的协程调度器
     */
    private val testDispatcher = TestCoroutineDispatcher()

    /**
     * 模拟的FileLabelDBHelper对象
     */
    @MockK
    private lateinit var mockFileLabelDBHelper: FileLabelDBHelper

    /**
     * 模拟的FileLabelMappingDBHelper对象
     */
    @MockK
    private lateinit var mockFileLabelMappingDBHelper: FileLabelMappingDBHelper

    /**
     * 模拟的HiddenFileHelper对象
     */
    @MockK
    private lateinit var mockHiddenFileHelper: HiddenFileHelper

    /**
     * 被测的ViewModel对象
     */
    private lateinit var viewModel: MainLabelViewModel

    /**
     * 测试前的初始化方法
     */
    @Before
    fun setUp() {
        // 初始化MockK注解
        MockKAnnotations.init(this)
        // 设置主调度器为测试调度器
        Dispatchers.setMain(testDispatcher)
        // 模拟静态类
        mockkStatic(Log::class)
        mockkStatic(FileLabelDBHelper::class)
        mockkStatic(FileLabelMappingDBHelper::class)
        mockkStatic(HiddenFileHelper::class)

        // 设置Log类的模拟行为
        every { Log.d(any(), any()) } just Runs
        every { Log.e(any(), any<String>()) } just Runs
        every { Log.e(any(), any(), any<Throwable>()) } just Runs
        
        // 创建被测ViewModel实例
        viewModel = MainLabelViewModel()
    }

    /**
     * 测试后的清理方法
     */
    @After
    fun tearDown() {
        // 重置主调度器
        Dispatchers.resetMain()
        // 清理测试协程
        testDispatcher.cleanupTestCoroutines()
        // 取消所有模拟
        unmockkAll()
        clearAllMocks()
    }

    /**
     * 测试deleteLabel方法在label为null时不会崩溃
     */
    @Test
    fun `deleteLabel should not crash when label is null`() {
        // When 调用deleteLabel方法并传入null
        viewModel.deleteLabel(null)

        // Then 验证是否打印了正确的日志
        verify { Log.d("MainLabelViewModel", "deleteLabel label entry is null, return!") }
    }

    /**
     * 测试getItemKey方法返回正确的名称哈希值
     */
    @Test
    fun `getItemKey should return name hashcode`() {
        // Given 准备测试数据
        val testLabel = FileLabelEntity(1, "test", 0, 0, 0, 0)
        val wrapper = FileLabelEntityWrapper(testLabel)

        // When 调用getItemKey方法
        val result = viewModel.getItemKey(wrapper)

        // Then 验证返回的哈希值是否正确
        assertEquals("test".hashCode(), result)
    }

    /**
     * 测试getRecyclerViewScanMode方法返回GRID类型
     */
    @Test
    fun `getRecyclerViewScanMode should return GRID`() {
        // When 调用getRecyclerViewScanMode方法
        val result = viewModel.getRecyclerViewScanMode()

        // Then 验证返回的布局类型是否为GRID
        assertEquals(com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.GRID, result)
    }
}