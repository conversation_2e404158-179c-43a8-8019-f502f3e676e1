<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.cardlist.COUICardListSelectedItemLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/action_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:minHeight="@dimen/dimen_48dp"
    android:orientation="vertical">

    <View
        android:id="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="0.33dp"
        android:layout_marginStart="@dimen/dimen_66dp"
        android:layout_marginEnd="@dimen/dimen_32dp"
        android:background="?attr/couiColorDivider" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/dimen_28dp"
            android:paddingEnd="@dimen/dimen_32dp">

            <ImageView
                android:id="@+id/icon_item"
                android:layout_width="@dimen/dimen_24dp"
                android:layout_height="@dimen/dimen_24dp"
                android:contentDescription="@null"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/title_item"
                style="?android:attr/textAppearanceLarge"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dimen_16dp"
                android:textAlignment="viewStart"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/count_item"
                app:layout_constraintStart_toEndOf="@id/icon_item"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/count_item"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                android:paddingEnd="@dimen/dimen_6dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>


</com.coui.appcompat.cardlist.COUICardListSelectedItemLayout>