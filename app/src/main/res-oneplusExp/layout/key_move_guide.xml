<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/relative_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:clipChildren="false"
    android:clipToPadding="false">

    <include layout="@layout/appbar_with_divider_layout_secondary" />

    <TextView
        android:id="@+id/move_title_tv"
        style="@style/akey_move_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text="@string/text_onetap_to_move_sdcard" />

    <TextView
        android:id="@+id/move_tip_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginHorizontal="@dimen/default_margin"
        android:layout_marginTop="@dimen/akeytomove_tip_margin_top"
        android:gravity="center_horizontal"
        android:text="@string/string_a_key_move_detailed_description"
        android:textColor="@color/black_55_percent"
        android:textSize="@dimen/akeytomove_tip_text_size" />

    <RelativeLayout android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <ImageView
            android:layout_width="312dp"
            android:layout_height="260dp"
            android:contentDescription="@null"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:scaleType="centerCrop"
            android:src="@drawable/akey_move_guide_oneplus" />
    </RelativeLayout>


    <com.coui.appcompat.button.COUIButton
        style="@style/Widget.COUI.Button.Large"
        android:id="@+id/start_btn"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center_horizontal"
        android:layout_marginHorizontal="@dimen/common_margin"
        android:layout_marginBottom="@dimen/akeytomove_btn_margin_bottom"
        android:minWidth="@dimen/ftp_btn_width"
        android:minHeight="@dimen/ftp_btn_height"
        android:paddingHorizontal="@dimen/dimen_7dp"
        android:paddingVertical="@dimen/dimen_2dp"
        android:lineSpacingExtra="@dimen/akeytomove_line_spacing_extra"
        android:text="@string/string_a_key_moving_menue"
        android:textSize="@dimen/akeytomove_btn_text_size"/>

</LinearLayout>
