/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: AudioViewPresenterTest.kt
 ** Description: unit test
 ** Version: 1.0
 ** Date : 2022/7/26
 ** Author: Yan<PERSON>hengNeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * YanShengNeng 2022/7/14      1.0    create
 ****************************************************************/
package com.oplus.filemanager.compat.oppo

import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.compat.brand.oppo.AudioViewPresenter
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import org.mockito.Answers
import org.mockito.Mockito

@RunWith(JUnit4::class)
class AudioViewPresenterTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `assert result when createItem`() {
        val presenter = Mockito.mock(AudioViewPresenter::class.java, Answers.CALLS_REAL_METHODS)
        val createItem = presenter.createItem()
        Assert.assertNotNull(createItem)
        Assert.assertEquals(BaseFileBean.TYPE_FILE_AD, createItem.mFileWrapperViewType)
    }

    @Test
    fun `should return true when shouldDisplayByPath`() {
        val albumPresenter = mockk<AudioViewPresenter>()
        every { albumPresenter.shouldDisplayByPath() } answers { callOriginal() }
        Assert.assertEquals(true, albumPresenter.shouldDisplayByPath())
    }
}