/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchResultData
 ** Description : Search 结果
 ** Version     : 1.0
 ** Date        : 2024/06/27 11:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/06/27       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.data

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.base.UriFileWrapper
import com.filemanager.common.constants.DmpConst.RECALL_TYPE_CONTENT
import com.filemanager.common.constants.DmpConst.RECALL_TYPE_FUZZY
import com.filemanager.common.constants.DmpConst.RECALL_TYPE_TITLE
import com.filemanager.common.constants.DmpConst.RECALL_TYPE_TITLE_CONTENT
import com.filemanager.common.utils.Log
import com.oplus.filemanager.apiproxy.services.utils.SearchChecker
import java.io.File

class SearchResultData {

    companion object {
        private const val TAG = "SearchResultData"
    }

    private var code: Int = SearchChecker.CODE_OK

    // 文件夹
    var listDirectory: MutableList<BaseFileBean>? = null

    // 文件名+文件内容命中
    var listTitleAndContent: MutableList<BaseFileBean>? = null

    // 文件名+文件内容命中 id
    var listNameAndTitleIds: MutableSet<Int>? = null

    // 只是文件名命中
    var listJustTitle: MutableList<BaseFileBean>? = null

    // 内容命中
    var listContent: MutableList<BaseFileBean>? = null

    // 文件名模糊命中
    var listTitleFuzzy: MutableList<BaseFileBean>? = null

    // 文件名模糊命中id
    var listTitleFuzzyIds: MutableList<BaseFileBean>? = null

    // 媒体库搜索
    var listMediaQuery: MutableList<BaseFileBean>? = null

    init {
        listDirectory = mutableListOf()
        listTitleAndContent = mutableListOf()
        listNameAndTitleIds = hashSetOf()
        listJustTitle = mutableListOf()
        listContent = mutableListOf()
        listTitleFuzzy = mutableListOf()
        listTitleFuzzyIds = mutableListOf()
        listMediaQuery = mutableListOf()
    }

    /**
     * 对dmp搜索结果进行分类
     */
    fun classifyDMPSearchItem(item: BaseFileBean?, keyword: String?) {
        val recallType = item?.recallType
        (item as? UriFileWrapper)?.let {
            if (!isFileExit(item)) {
                Log.i(TAG, "classifyDMPSearchItem item $item not exist, return")
                return
            }
            if (item.mIsDirectory.not()) {
                when (recallType) {
                    RECALL_TYPE_TITLE_CONTENT -> {
                        listTitleAndContent?.add(item)
                        item.id?.let { listNameAndTitleIds?.add(it) }
                    }

                    RECALL_TYPE_TITLE -> listJustTitle?.add(item)

                    RECALL_TYPE_CONTENT -> {
                        listContent?.add(item)
                        listTitleFuzzyIds?.add(item)
                    }

                    RECALL_TYPE_FUZZY -> {
                        keyword?.let { searchKey ->
                            if (item.mDisplayName?.contains(searchKey) == true) {
                                return
                            }
                        }
                        listTitleFuzzy?.add(item)
                        listTitleFuzzyIds?.add(item)
                    }

                    else -> Log.d(TAG, "recallType not need handle")
                }
            } else {
                listDirectory?.add(item)
            }
        }
    }

    /**
     * 判断文件是否存在
     */
    fun isFileExit(file: BaseFileBean): Boolean {
        return file.mData?.let { File(it).exists() } ?: false
    }

    /**
     * 对媒体搜索结果进行分类
     */
    fun classMediaSearchItem(item: BaseFileBean?, isSimpleWord: Boolean) {
        if ((item != null) && isFileExit(item)) {
            if (item.mIsDirectory) {
                if (isSimpleWord) {
                    listMediaQuery?.add(item)
                }
            } else {
                if (item is UriFileWrapper && (listNameAndTitleIds?.contains(item.id) == false)) {
                    listMediaQuery?.add(item)
                }
            }
        }
    }

    /**
     * 对远程mac搜索内容进行分类
     */
    fun classifyRemoteFileSearchList(list: List<RemoteFileBean>) {
        listTitleFuzzyIds?.addAll(list)
    }

    fun resetResultCode() {
        code = SearchChecker.CODE_OK
    }

    fun getResultCode(): Int {
        return code
    }

    fun setResultCode(code: Int) {
        if (!SearchChecker.isOK(this.code)) {
            return
        }
        this.code = code
    }

    /**
     * 获取最终的搜索结果
     */
    fun getSearchList(): List<BaseFileBean> {
        val items = mutableListOf<BaseFileBean>()
        //文件夾
        listDirectory?.let {
            items.addAll(it)
        }
        //文件名+内容
        listTitleAndContent?.let {
            items.addAll(it)
        }
        //文件名 来自文管内部搜索
        listMediaQuery?.let {
            items.addAll(it)
        }
        //内容+文件名模糊
        listTitleFuzzyIds?.let {
            items.addAll(it)
        }
        Log.d(
            TAG, "getSearchList dir:${listDirectory?.size} titleAndContent:${listTitleAndContent?.size} " +
                    "media:${listMediaQuery?.size} TitleFuzzy:${listTitleFuzzyIds?.size}, itemSize ${items.size}"
        )
        return items
    }
}