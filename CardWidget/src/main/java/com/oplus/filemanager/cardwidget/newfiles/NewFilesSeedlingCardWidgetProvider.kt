/*********************************************************************
 * * Copyright (C), 2024, OPlus. All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/8/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.newfiles

import android.content.Context
import android.os.Bundle
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.Constants
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.PreferencesUtils
import com.oplus.filemanager.cardwidget.label.labelCardScope
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.main.RecentFilesLoadListener
import com.oplus.pantanal.seedling.SeedlingCardWidgetProvider
import com.oplus.pantanal.seedling.bean.SeedlingCard
import com.oplus.pantanal.seedling.util.SeedlingTool
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.json.JSONObject

class NewFilesSeedlingCardWidgetProvider : SeedlingCardWidgetProvider() {
    companion object {
        private const val TAG = "NewFilesSeedlingCardWidgetProvider"
        private const val MAX_DISPLAY_SIZE = 99
        private const val NUMBER_VALUE_KEY = "number_value"
        private const val SHOW_UNIT_KEY = "showUnit"
        private const val SHOW_EMPTY_KEY = "showEmpty"
    }

    override fun onCardCreate(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onCardCreate -> card = $card")
    }

    override fun onCardObserve(context: Context, cards: List<SeedlingCard>) {
        Log.d(TAG, "onCardObserve -> card = $cards")
    }

    override fun onDestroy(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onDestroy -> card = $card")
    }

    override fun onHide(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onHide -> card = $card")
    }

    override fun onShow(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onShow -> card = $card")
    }

    private fun postBasicUI(card: SeedlingCard) {
        val businessData = JSONObject().apply {
            put(SHOW_EMPTY_KEY, true)
        }
        SeedlingTool.updateData(card, businessData)
    }

    override fun onSubscribed(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onSubscribed -> card = $card id=${card.getSeedlingCardId()}")
    }

    override fun onUnSubscribed(context: Context, card: SeedlingCard) {
        Log.d(TAG, "onUnSubscribed -> card = $card")
    }

    override fun onUpdateData(context: Context, card: SeedlingCard, data: Bundle) {
        Log.d(TAG, "onUpdateData -> card = $card ; data = $data")
        val hasAgreePrivacy = PrivacyPolicyController.hasAgreePrivacy()
        Log.d(TAG, "refreshCard -> hasAgreePrivacy = $hasAgreePrivacy")
        if (!hasAgreePrivacy) {
            postBasicUI(card)
            return
        }

        val isPermissionGranted = PermissionUtils.hasStoragePermission(context)
        Log.d(TAG, "refreshCard -> permissionGranted = $isPermissionGranted")
        if (!isPermissionGranted) {
            postBasicUI(card)
            return
        }
        labelCardScope.launch(Dispatchers.IO) {
            val mainAction = Injector.injectFactory<IMain>()
            Log.d(TAG, "getIncrementRecentFiles ${mainAction != null}")
            if (mainAction == null) {
                postBasicUI(card)
            } else {
                mainAction?.getIncrementRecentFiles(object : RecentFilesLoadListener {
                    override fun onSuccess(result: List<BaseFileBean>) {
                        Log.d(TAG, "getIncrementRecentFiles ${result.size}")
                        if (result.isNotEmpty()) {
                            val businessData = JSONObject().apply {
                                put(SHOW_EMPTY_KEY, false)
                                if (result.size > MAX_DISPLAY_SIZE) {
                                    put(NUMBER_VALUE_KEY, "99+")
                                    put(SHOW_UNIT_KEY, false)
                                } else {
                                    put(NUMBER_VALUE_KEY, result.size)
                                    put(SHOW_UNIT_KEY, true)
                                }
                            }
                            SeedlingTool.updateAllCardData(card, businessData)
                        } else {
                            Log.d(TAG, "no file,do not update")
                        }
                    }
                })
            }
        }
    }
}