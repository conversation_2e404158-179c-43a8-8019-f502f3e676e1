/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : InternalErase
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/25 15:32
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/25       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.delete

import android.content.ContentValues
import android.content.Context
import com.filemanager.common.RecycleStore
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.recyclebin.operation.action.DoubleDeleteAction
import com.filemanager.recyclebin.operation.action.QueryBatchAction
import com.filemanager.recyclebin.retriever.DataRetriever
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.InvalidSourceFileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.OK
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.OperateFailure
import com.oplus.fileservice.operate.convert.OperationResult
import java.io.File

class InternalErase(
    private val context: Context
) : IDelete {

    private val mEraseBulk: DoubleDeleteAction = DoubleDeleteAction(
        mColumnName1 = RecycleStore.Files.FileColumns.RECYCLE_ID,
        mColumnName2 = RecycleStore.Files.FileColumns.RECYCLE_PATH
    )
    private val allFilePathForLabels = ArrayList<String>()

    @Suppress("LongMethod")
    override fun delete(fileInfos: List<FileInfo>?): OperationResult {
        Log.d(TAG, "delete -> fileInfos = $fileInfos")
        if (fileInfos.isNullOrEmpty()) {
            return OperationResult(false, InvalidSourceFileInfo)
        }
        val internalRoot = VolumeEnvironment.getInternalSdPath(context.applicationContext)
        if (internalRoot.isEmpty()) {
            return OperationResult(false)
        }
        val queryAction = QueryBatchAction(
            RecycleStore.Files.INTERNAL_CONTENT_URI, RecycleStore.Files.FileColumns.RECYCLE_ID,
            QueryBatchAction.QUERY_BATCH_ID_COUNT
        )
        var failedCount = 0
        val ids = ArrayList<String>()
        var id: String?
        var path: String?
        for (file in fileInfos) {
            Log.d(TAG, "delete -> recycleId = ${file.recycleId}")
            if (file.recycleId.isNullOrEmpty()) {
                failedCount++
                continue
            } else {
                path = file.data
                id = file.recycleId
            }
            val invalidPath = path.isNullOrEmpty() || !path.startsWith(internalRoot)
            if (invalidPath || id.isNullOrEmpty()) {
                failedCount++
                continue
            }
            queryAction.add(id)
            ids.add(id)
        }
        queryAction.flush()
        val valuesList = queryAction.getBatchResult()
        var index = 0
        var values: ContentValues?
        val store = DataRetriever.INSTANCE
        Log.d(TAG, "delete -> ids = $ids")
        ids.forEach { i ->
            values = valuesList[i]
            values?.let {
                if (!internalInnerErase(store, it)) {
                    failedCount++
                    index++
                    return@forEach
                }
                index++
            }
        }
        mEraseBulk.flush()
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.onDeleteFilePaths(allFilePathForLabels)
        failedCount += (ids.size - index)
        return if (failedCount > 0) {
            OperationResult(false, OperateFailure)
        } else {
            OperationResult(true, OK)
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun internalInnerErase(retriever: DataRetriever, values: ContentValues): Boolean {
        try {
            val recyclePath = values.getAsString(RecycleStore.Files.FileColumns.RECYCLE_PATH)
            val originPath = values.getAsString(RecycleStore.Files.FileColumns.ORIGIN_PATH)
            if (retriever.deleteRecycle(recyclePath)) {
                val recycleId = values.getAsString(RecycleStore.Files.FileColumns.RECYCLE_ID)
                val file = File(recyclePath)
                if (file.isDirectory) {
                    val path = recyclePath + File.separator + "/%"
                    mEraseBulk.add(recycleId, path)
                } else {
                    mEraseBulk.add(recycleId, null)
                }
                allFilePathForLabels.add(originPath)
                return true
            }
        } catch (e: Exception) {
            Log.e(TAG, "internalInnerErase -> error = ${e.message}")
        }
        return false
    }

    companion object {
        private const val TAG = "InternalErase"
    }
}