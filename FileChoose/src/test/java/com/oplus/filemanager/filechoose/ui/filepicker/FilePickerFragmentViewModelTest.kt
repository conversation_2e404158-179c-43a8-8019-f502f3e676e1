package com.oplus.filemanager.filechoose.ui.filepicker

import android.app.Activity.RESULT_OK
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.encrypt.EncryptActivity
import com.oplus.filemanager.filechoose.factor.LoaderFactor
import io.mockk.*
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * FilePickerFragmentViewModel的单元测试类
 * 使用Robolectric和MockK框架进行测试
 */
@ExperimentalCoroutinesApi
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])  // 将SDK版本从28改为29以解决错误
class FilePickerFragmentViewModelTest {

    // 模拟LoaderController对象
    @MockK
    private lateinit var mockLoaderController: LoaderController

    // 模拟EncryptActivity对象
    @MockK
    private lateinit var mockEncryptActivity: EncryptActivity

    // 模拟FilePickerLoader对象
    @MockK
    private lateinit var mockFilePickerLoader: FilePickerLoader

    // 模拟BaseVMActivity对象
    @MockK
    private lateinit var mockBaseActivity: BaseVMActivity

    // 测试用的协程调度器
    private val testDispatcher = TestCoroutineDispatcher()
    // 被测ViewModel实例
    private lateinit var viewModel: FilePickerFragmentViewModel

    /**
     * 测试前的初始化方法
     */
    @Before
    fun setup() {
        // 初始化MockK注解
        MockKAnnotations.init(this)
        // 设置主调度器为测试调度器
        Dispatchers.setMain(testDispatcher)
        // 创建被测ViewModel实例
        viewModel = FilePickerFragmentViewModel()
        // 模拟Log类的静态方法
        mockkStatic(Log::class)
        // 设置Log.d()方法总是执行
        every { Log.d(any(), any()) } just Runs
        // 初始化ViewModel的默认状态
        viewModel.mUiState.value = BaseUiModel(emptyList(), BaseStateModel(MutableLiveData()), ArrayList(), HashMap())
    }

    /**
     * 测试后的清理方法
     */
    @After
    fun tearDown() {
        // 恢复主调度器
        Dispatchers.resetMain()
        // 清理测试协程
        testDispatcher.cleanupTestCoroutines()
        // 取消所有模拟
        unmockkAll()
    }

    /**
     * 测试selectItem方法 - 当项目已被选中时
     */
    @Test
    fun `test selectItem when item is selected`() {
        // 初始化选中列表
        val initialList = ArrayList<Int>().apply { add(1); add(2); add(3) }
        viewModel.mUiState.value = BaseUiModel(emptyList(), BaseStateModel(MutableLiveData()), initialList, HashMap())
        
        // 执行测试方法
        val result = viewModel.selectItem(2)
        
        // 验证结果
        assertTrue(result)
        assertEquals(2, viewModel.mUiState.value?.mSelectedList?.size)
        assertFalse(viewModel.mUiState.value?.mSelectedList?.contains(2) ?: false)
    }

    /**
     * 测试selectItem方法 - 当项目未被选中时
     */
    @Test
    fun `test selectItem when item is not selected`() {
        // 初始化选中列表
        val initialList = ArrayList<Int>().apply { add(1); add(3) }
        viewModel.mUiState.value = BaseUiModel(emptyList(), BaseStateModel(MutableLiveData()), initialList, HashMap())
        
        // 执行测试方法
        val result = viewModel.selectItem(2)
        
        // 验证结果
        assertTrue(result)
        assertEquals(3, viewModel.mUiState.value?.mSelectedList?.size)
        assertTrue(viewModel.mUiState.value?.mSelectedList?.contains(2) ?: false)
    }

    /**
     * 测试clickToolbarSelectAll方法 - 当所有项目都已选中时
     */
    @Test
    fun `test clickToolbarSelectAll when all selected`() {
        // 初始化文件列表和选中列表
        val fileList = listOf(MediaFileWrapper().apply { mId = 1 }, MediaFileWrapper().apply { mId = 2 })
        val selectedList = ArrayList<Int>().apply { add(1); add(2) }
        viewModel.mUiState.value = BaseUiModel(fileList, BaseStateModel(MutableLiveData()), selectedList, HashMap())
        
        // 执行测试方法
        viewModel.clickToolbarSelectAll()
        
        // 验证结果
        assertEquals(0, viewModel.mUiState.value?.mSelectedList?.size)
    }

    /**
     * 测试clickToolbarSelectAll方法 - 当不是所有项目都选中时
     */
    @Test
    fun `test clickToolbarSelectAll when not all selected`() {
        // 初始化文件列表和选中列表
        val fileList = listOf(MediaFileWrapper().apply { mId = 1 }, MediaFileWrapper().apply { mId = 2 })
        val selectedList = ArrayList<Int>().apply { add(1) }
        viewModel.mUiState.value = BaseUiModel(fileList, BaseStateModel(MutableLiveData()), selectedList, HashMap())
        
        // 执行测试方法
        viewModel.clickToolbarSelectAll()
        
        // 验证结果
        assertEquals(2, viewModel.mUiState.value?.mSelectedList?.size)
        assertTrue(viewModel.mUiState.value?.mSelectedList?.contains(1) ?: false)
        assertTrue(viewModel.mUiState.value?.mSelectedList?.contains(2) ?: false)
    }

    /**
     * 测试onClickEncryption方法 - 当Activity不是EncryptActivity时
     */
    @Test
    fun `test onClickEncryption with non-EncryptActivity`() {
        // 执行测试方法
        viewModel.onClickEncryption(mockBaseActivity)
        
        // 验证日志输出
        verify { Log.d("FilePickerFragmentViewModel", "onClickEncryption not EncryptActivity") }
        // 验证Activity方法未被调用
        verify(exactly = 0) { mockBaseActivity.setResult(any()) }
        verify(exactly = 0) { mockBaseActivity.finish() }
    }

    /**
     * 测试getRealFileSize方法
     */
    @Test
    fun `test getRealFileSize`() {
        // 初始化文件列表
        val fileList = listOf(MediaFileWrapper(), MediaFileWrapper())
        viewModel.mUiState.value = BaseUiModel(fileList, BaseStateModel(MutableLiveData()), ArrayList(), HashMap())
        
        // 执行测试方法并验证结果
        assertEquals(2, viewModel.getRealFileSize())
    }

    /**
     * 测试getRecyclerViewScanMode方法
     */
    @Test
    fun `test getRecyclerViewScanMode`() {
        // 执行测试方法并验证结果
        assertEquals(com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.LIST, viewModel.getRecyclerViewScanMode())
    }
}