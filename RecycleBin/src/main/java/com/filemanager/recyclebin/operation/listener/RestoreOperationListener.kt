/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - RestoreOperationListener.java
 ** Description: Restore Operation Listener
 ** Version: 1.0
 ** Date : 2020/03/10
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/03/10    1.0     create
 ****************************************************************/

package com.filemanager.recyclebin.operation.listener

import android.app.Activity
import android.os.Message
import androidx.activity.ComponentActivity
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.progressbar.COUIHorizontalProgressBar
import com.filemanager.common.wrapper.RecycleBinLiveData
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ShortCutUtils
import com.filemanager.common.wrapper.RecycleFileWrapper
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_NOT_ENOUGH_SPACE
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_SUCCESS
import com.filemanager.recyclebin.operation.BaseOperation.OperationParameters
import com.filemanager.recyclebin.operation.BaseOperation.OperationResult
import com.filemanager.recyclebin.operation.RecycleBinOperationListener

class RestoreOperationListener(activity: ComponentActivity, var listener: RecycleBinOperationListener?) : OperationListener(activity) {
    companion object {
        private const val TAG = "RestoreOperationListener"

        private fun createProgressDialog(activity: Activity): AlertDialog? {
            if (activity.isFinishing || activity.isDestroyed) {
                Log.w(TAG, "createProgressDialog ACTIVITY INVALID")
                return null
            }
            val dialog = COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Progress)
                .setTitle(com.filemanager.common.R.string.send_file_message)
                .setCancelable(false).create()
            dialog.setCanceledOnTouchOutside(false)
            return dialog
        }
    }

    @VisibleForTesting
    var mProgressDialog: AlertDialog? = null

    override fun innerHandleMessage(message: Message, activity: ComponentActivity) {
        Log.d(TAG, "handleMessage what : ${message.what}")
        mWeakActivity.get() ?: return
        when (message.what) {
            MSG_SHOW_PROGRESS_DIALOG -> {
                mProgressDialog?.show()
            }

            MSG_UPDATE_PROGRESS_DIALOG -> {
                if (message.obj is Int) {
                    mProgressDialog?.window?.findViewById<COUIHorizontalProgressBar>(com.support.dialog.R.id.progress)?.progress =
                        (message.obj as Int)
                }
            }
        }
    }

    override fun onProgressStart() {
        Log.d(TAG, "onProgressStart")
        mWeakActivity.get()?.let {
            mProgressDialog?.let { dialog ->
                if (dialog.isShowing) {
                    dialog.dismiss()
                }
                mProgressDialog = null
            }

            mProgressDialog = createProgressDialog(it)
            mMainHandler.sendEmptyMessageDelayed(MSG_SHOW_PROGRESS_DIALOG, MESSAGE_DELAY_TIME)
        }
    }

    override fun onProgressUpdate(progress: Int?) {
        progress?.let {
            mProgressDialog?.let {
                mMainHandler.removeMessages(MSG_UPDATE_PROGRESS_DIALOG)
                if (it.isShowing) {
                    it.window?.findViewById<COUIHorizontalProgressBar>(com.support.dialog.R.id.progress)?.progress = progress
                } else {
                    mMainHandler.sendMessageDelayed(
                        mMainHandler.obtainMessage(MSG_UPDATE_PROGRESS_DIALOG, progress),
                        MESSAGE_DELAY_TIME
                    )
                }
            }
        }
    }

    override fun onProgressComplete(parameters: OperationParameters?, result: OperationResult) {
        Log.d(TAG, "onProgressComplete result: $result")
        // remove all message when task complete
        mMainHandler.removeCallbacksAndMessages(null)
        mProgressDialog?.let { dialog ->
            if (dialog.isShowing) {
                dialog.dismiss()
            }
        }

        listener?.onOperationCompleted(RecycleBinLiveData.RECYCLE_RESTORE, result)

        val statusCode = result.mStatusCode
        when {
            (statusCode == STATUS_NOT_ENOUGH_SPACE) -> {
                CustomToast.showLong(
                    appContext.resources.getString(
                        com.filemanager.common.R.string.disk_space_not_enough,
                        appContext.getString(com.filemanager.common.R.string.device_storage),
                        appContext.getString(com.filemanager.common.R.string.toast_restore_file_error)
                    )
                )
                return
            }

            (statusCode != STATUS_SUCCESS) -> {
                CustomToast.showLong(com.filemanager.common.R.string.toast_restore_file_exception)
                return
            }

            (result.mTotalCount > 0) -> {
                Log.d(TAG, "onProgressComplete success ${result.mTotalCount}")
            }

            else -> {
                Log.w(TAG, "onProgressComplete error totalCount: ${result.mTotalCount}")
            }
        }
        (mWeakActivity.get() as? BaseVMActivity)?.let {
            ShortCutUtils.checkShortCuts(it, parameters?.mBaseFiles?.map {
                if (it is RecycleFileWrapper) {
                    it.mOriginPath ?: ""
                } else {
                    ""
                }
            })
        }
    }

    override fun recycle() {
        listener = null
        runCatching {
            mProgressDialog?.dismiss()
        }.onFailure {
            Log.e(TAG, "${it.message}")
        }
        mProgressDialog = null
        super.recycle()
    }
}