<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:layout_marginHorizontal="@dimen/download_file_item_horizontal_margin"
    >

    <com.filemanager.common.view.FileThumbView
        android:id="@+id/item_file_icon"
        android:layout_width="@dimen/dimen_32dp"
        android:layout_height="@dimen/dimen_32dp"
        android:layout_marginVertical="@dimen/dimen_10dp"
        android:forceDarkAllowed="false"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/video_type_tv"
        android:layout_width="@dimen/dimen_32dp"
        android:layout_height="wrap_content"
        android:background="@drawable/item_video_duration_view_bg"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:textColor="?attr/couiColorLabelOnColor"
        android:textSize="@dimen/duration_text_size"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/item_file_icon"
        app:layout_constraintEnd_toEndOf="@id/item_file_icon"
        app:layout_constraintStart_toStartOf="@id/item_file_icon" />

    <com.filemanager.common.view.TextViewSnippet
        android:id="@+id/item_file_title_tv"
        style="?attr/couiTextHeadlineXS"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginVertical="@dimen/dimen_14dp"
        android:ellipsize="middle"
        android:gravity="start"
        android:maxLines="2"
        android:singleLine="false"
        android:textColor="?attr/couiColorLabelPrimary"
        app:layout_constraintBottom_toBottomOf="@id/item_file_icon"
        app:layout_constraintEnd_toStartOf="@id/item_download_state_img"
        app:layout_constraintStart_toEndOf="@id/item_file_icon"
        app:layout_constraintTop_toTopOf="@id/item_file_icon" />

    <ImageView
        android:id="@+id/item_download_state_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:src="@drawable/ic_status_success"
        app:layout_constraintBottom_toBottomOf="@id/item_file_icon"
        app:layout_constraintEnd_toStartOf="@id/item_file_size_tv"
        app:layout_constraintStart_toEndOf="@id/item_file_title_tv"
        app:layout_constraintTop_toTopOf="@id/item_file_icon" />

    <TextView
        android:id="@+id/item_file_size_tv"
        style="?attr/couiTextBodyM"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="@dimen/dimen_72dp"
        android:gravity="center_horizontal|end"
        android:singleLine="true"
        android:textColor="?attr/couiColorLabelSecondary"
        app:layout_constraintBottom_toBottomOf="@id/item_file_icon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/item_file_icon" />

    <View
        android:id="@+id/divider_line"
        android:layout_width="0dp"
        android:layout_height="0.33dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/item_file_title_tv"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="?attr/couiColorDivider"
        />

</androidx.constraintlayout.widget.ConstraintLayout>