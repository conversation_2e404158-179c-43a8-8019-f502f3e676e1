/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : IndexSyncHelper.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.index

import android.util.Log
import com.coloros.filemanager.appswitch.AppSwitchManager
import com.coloros.filemanager.appswitch.IntentAndViewExtracProcessManager
import com.coloros.filemanager.appswitch.process.IViewExactProcess
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import com.oplus.filemanager.provider.ThirdAppFileDBHelper
import com.oplus.filemanager.room.model.ThirdAppFileDataEntity
import kotlin.math.abs

class IndexSyncHelper(private val intentAndViewProcessManager: IntentAndViewExtracProcessManager) {

    companion object {
        const val TAG = "IndexSyncHelper"

        const val YEAR_TIME: Long = 365 * 24 * 60 * 60 * 1000

        const val TEST_EXPIRE_TIME = 30 * 60 * 1000
    }

    private var indexHelper = intentAndViewProcessManager.indexHelper


    fun doSyncWorks() {
        processNeedIndexDataList()
        syncIndexBetweenDmpAndFileManager()
        repairDbAndIndexWithNewRules()
        clearExpireIndex()
    }


    private fun syncIndexBetweenDmpAndFileManager() {
        Log.i(TAG, "syncIndexBetweenDmpAndFileManager START")
        val dmpSearchApi = Injector.injectFactory<IDmpSearchApi>()
        if (dmpSearchApi == null) {
            Log.i(TAG, "syncIndexBetweenDmpAndFileManager null, return false")
            return
        }
        val allRawDataFromDmp = dmpSearchApi.queryRaw()
        val mapForDmp = allRawDataFromDmp.associateBy({ it.identification },
            { CompareData(it.identification, it.checkSum) })
        val allEntityFromFileManager = ThirdAppFileDBHelper.getNoNeedIndexData()
        val mapForFileManager = allEntityFromFileManager?.associateBy { it.mIndentification ?: "" }
        //文件管理独有的记录
        val fileManagerDiffList =
            mapForFileManager?.filterNot { mapForDmp.containsKey(it.key) }?.values?.toList()
        //文件管理和dmp共有的记录
        var fileManagerCommonMap: Map<String, Pair<ThirdAppFileDataEntity, CompareData>> =
            mutableMapOf()
        if (!mapForFileManager.isNullOrEmpty()) {
            val filterMap = mapForFileManager.filter { mapForDmp.containsKey(it.key) }
            fileManagerCommonMap = filterMap.mapValues { entry ->
                val identification = entry.key
                val entity = entry.value
                val compareData = mapForDmp[identification] ?: CompareData(identification, 0L)
                Pair(entity, compareData)
            }
        }
        //中子dmp apk中独有的记录
        val dmpDiffList =
            mapForDmp.filterNot { mapForFileManager?.containsKey(it.key) ?: false }.values.toList()
        if (!fileManagerDiffList.isNullOrEmpty()) {
            processFileManagerDiffList(fileManagerDiffList)
        }
        if (fileManagerCommonMap.isNotEmpty()) {
            processFileManagerCommonList(fileManagerCommonMap)
        }
        if (dmpDiffList.isNotEmpty()) {
            processDmpDiffList(dmpDiffList)
        }
        Log.i(TAG, "syncIndexBetweenDmpAndFileManager END")
    }

    private fun processFileManagerDiffList(diffList: List<ThirdAppFileDataEntity>) {
        Log.i(TAG, "processFileManagerDiffList diffList size ${diffList.size} START")
        for (entity in diffList) {
            val insertSuc = indexHelper.trigIndexInsert(entity)
            Log.i(TAG, "processFileManagerDiffList insert entity $entity, result $insertSuc")
        }
        Log.i(TAG, "processFileManagerDiffList diffList size ${diffList.size} end")
    }

    private fun processFileManagerCommonList(commonMap: Map<String, Pair<ThirdAppFileDataEntity, CompareData>>) {
        Log.i(TAG, "processFileManagerCommonList diffList size ${commonMap.size} START")
        for (item in commonMap) {
            val entity = item.value.first
            val compareData = item.value.second
            //这里只有文件管理中的版本号高于dmp中的版本号时，才更新索引
            if (entity.mIndexCheckSum > compareData.checkSum) {
                val updateSuc = indexHelper.trigIndexUpdate(entity)
                Log.i(TAG, "processFileManagerCommonList update entity $entity, result $updateSuc")
            } else {
                Log.i(
                    TAG,
                    "processFileManagerCommonList dmp checksum ${compareData.checkSum}, entityCheckSum ${entity.mIndexCheckSum}, no update"
                )
            }
        }
        Log.i(TAG, "processFileManagerCommonList diffList size ${commonMap.size} end")
    }


    private fun processDmpDiffList(diffList: List<CompareData>) {
        Log.i(TAG, "processDmpDiffList diffList size ${diffList.size} START")
        for (item in diffList) {
            //这里后续讨论，怎么处理（dmp中多余了一些index索引记录，这些索引记录是需要怎么处理？ 比如文管管理被清楚了数据，dmp没有被清楚数据，dmp多出来的这部分是需要被删除）
            val deleteSuc = indexHelper.trigIndexDelete(item.identification)
            Log.i(TAG, "processDmpDiffList ing item $item, result $deleteSuc")
        }
        Log.i(TAG, "processDmpDiffList diffList size ${diffList.size} end")
    }


    private fun processNeedIndexDataList() {
        Log.i(TAG, "processNeedIndexDataList START")
        val needIndexData = ThirdAppFileDBHelper.getNeedIndexData()
        if (needIndexData.isNullOrEmpty()) {
            Log.i(TAG, "processNeedIndexDataList list empty returned")
            return
        }
        for (entity in needIndexData) {
            val insertSuc = indexHelper.trigIndexInsert(entity)
            Log.i(TAG, "processFileManagerDiffList insert entity $entity, result $insertSuc")
        }
        Log.i(TAG, "processNeedIndexDataList size ${needIndexData.size} END")
    }


    private fun clearExpireIndex() {
        Log.i(TAG, "clearExpireIndex START")
        val dbData = ThirdAppFileDBHelper.getAllThirdAppFileData()
        if (dbData.isNullOrEmpty()) {
            Log.i(TAG, "clearExpireIndex no data in db, no need to clear expire date")
            return
        }
        val currentTime = System.currentTimeMillis()
        val filterList = dbData.filter {
            val timeExpired = abs(currentTime - it.mDetectTime) > YEAR_TIME
            //val timeExpired = abs(currentTime - it.mDetectTime) > TEST_EXPIRE_TIME
            val packageUninstalled =
                !AppUtils.isAppInstalledByPkgName(MyApplication.sAppContext, it.mDectPackage)
            timeExpired || packageUninstalled
        }
        Log.i(TAG, "clearExpireIndex expire list ${filterList.size}")
        filterList.forEach { item ->
            var dbDeleteResult = 0
            val identification = item.mIndentification
            if (identification.isNullOrEmpty()) {
                dbDeleteResult = ThirdAppFileDBHelper.deleteThirdAppFileData(
                    item.mFileName,
                    item.mDectPackage,
                    item.mSourceName
                )
                Log.i(
                    TAG,
                    "clearExpireIndex no identification direct delete db, item $item, result $dbDeleteResult"
                )
            } else {
                val indexDeleteResult = indexHelper.trigIndexDelete(identification)
                if (indexDeleteResult) {
                    dbDeleteResult = ThirdAppFileDBHelper.deleteThirdAppFileData(
                        item.mFileName,
                        item.mDectPackage,
                        item.mSourceName
                    )
                    Log.i(
                        TAG,
                        "clearExpireIndex success identification $identification, item $item, dbResult $dbDeleteResult"
                    )
                } else {
                    Log.e(
                        TAG,
                        "clearExpireIndex failed, identification $identification, item $item"
                    )
                }
            }
        }
        Log.i(TAG, "clearExpireIndex END")
    }


    private fun repairDbAndIndexWithNewRules() {
        Log.i(TAG, "repairDbAndIndexWithNewRules START")
        val currentThirdAppConfig = AppSwitchManager.currentThirdAppListenConfigParam
        if (currentThirdAppConfig == null) {
            Log.i(TAG, "repairDbAndIndexWithNewRules currentThirdAppConfig null return")
            return
        }
        val currentParseVersion = currentThirdAppConfig.version
        val forceUpdateCheckActivityList = currentThirdAppConfig.getAllForceUpdateCheckActivitys()
        Log.i(TAG, "repairDbAndIndexWithNewRules forceUpdate activitys ${forceUpdateCheckActivityList.map { it.activity }}")
        forceUpdateCheckActivityList.forEach { checkActivityItem ->
            val activity = checkActivityItem.activity
            val thirdAppFileDataEntitys =
                ThirdAppFileDBHelper.getThirdAppFilesBySourceActivityAndBlowVersion(
                    activity,
                    currentParseVersion
                )
            Log.i(
                TAG,
                "repairDbAndIndexWithNewRules ing need repaird entity ${thirdAppFileDataEntitys?.map { it.mFileName + it.mSourceName }}"
            )
            if (thirdAppFileDataEntitys.isNullOrEmpty()) return@forEach
            for (entity in thirdAppFileDataEntitys) {
                repaireSingleEntity(entity)
            }
        }
        Log.i(TAG, "repairDbAndIndexWithNewRules END")
    }


    private fun repaireSingleEntity(entity: ThirdAppFileDataEntity) {
        val packageName = entity.mDectPackage
        val activity = entity.mSourceActivity
        val veiwExactString = entity.mMetaData
        val preViewActivity = entity.mPreviewActivity
        val packageVersion = entity.mDectPackageVersion ?: ""
        val packageVersionInt = runCatching {
            packageVersion.toInt()
        }.getOrDefault(-1)
        val viewResult = ViewExtractHelper.ViewExtractResult(
            veiwExactString,
            activity,
            preViewActivity,
            packageName,
            packageVersionInt
        )
        //这里新解析除了fileName之外的其他的所有字段
        val keyDataBean = IViewExactProcess.KeyDataBean()
        keyDataBean.dbId = entity.id
        keyDataBean.fileName = entity.mFileName
        keyDataBean.uid = entity.mUid ?: ""
        runCatching {
            val intentProcess =
                intentAndViewProcessManager.getIntentProcessByPackageName(packageName)
            intentAndViewProcessManager.processViewExactResult(
                process = intentProcess,
                viewResult,
                preViewActivity,
                keyDataBean,
                packageName
            )
        }.onFailure {
            Log.e(TAG, "repaireSingleEntity error entity $entity", it)
        }
    }


    data class CompareData(var identification: String, var checkSum: Long)
}