/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ThirdAppSearchSwitchPreference.kt
 ** Description : 三方应用搜索开关的Preference
 ** Version     : 1.0
 ** Date        : 2024/7/18
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue      2024/7/18     1.0        create
 ***********************************************************************/
package com.filemanager.setting.ui.function

import android.content.Context
import android.util.AttributeSet
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.coui.appcompat.preference.COUISwitchPreference
import com.filemanager.common.utils.Log

class ThirdAppSearchSwitchPreference : COUISwitchPreference {

    constructor(context: Context?) : this(context, null)

    constructor(context: Context?, attrs: AttributeSet?) : this(
        context,
        attrs,
        androidx.preference.R.attr.switchPreferenceStyle
    )

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : this(
        context,
        attrs,
        defStyleAttr,
        0
    )

    constructor(
        context: Context?,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int
    ) : super(
        context,
        attrs,
        defStyleAttr,
        defStyleRes
    )

    companion object {
        private const val TAG = "ThirdAppSearchSwitchPref"
        const val ANIM_TIME_400 = 400L
        const val ANIM_TIME_500 = 500L
    }

    var needAnim = false

    private var rootView: COUICardListSelectedItemLayout? = null


    override fun onBindViewHolder(holder: PreferenceViewHolder?) {
        super.onBindViewHolder(holder)
        rootView = holder?.itemView as? COUICardListSelectedItemLayout
        Log.e(TAG, "onBindVH item:${holder?.itemView}")

        startAnim()
    }


    fun startAnim() {
        if (!isVisible || isChecked) {
            Log.w(TAG, "startAnim preference is gone or is Checked:$isChecked")
            return
        }
        Log.w(TAG, "startAnim anim: $needAnim rootView:$rootView")
        if (!needAnim) {
            return
        }
        needAnim = false
        val view = rootView ?: return
        startAppearAnimation(view) {
            startDisAppearAnimation(view)
        }
    }

    private fun startAppearAnimation(
        view: COUICardListSelectedItemLayout,
        animEndCallback: () -> Unit
    ) {
        Log.d(TAG, "startAppearAnimation start ....")
        view.postDelayed({
            Log.d(TAG, "startAppearAnimation running ....")
            view.startAppearAnimation()
            animEndCallback.invoke()
        }, ANIM_TIME_400)
    }

    private fun startDisAppearAnimation(view: COUICardListSelectedItemLayout) {
        Log.d(TAG, "startDisAppearAnimation start...")
        view.postDelayed({
            Log.d(TAG, "startDisAppearAnimation running ....")
            view.startDisAppearAnimationOrNot()
        }, ANIM_TIME_500)
    }

    override fun onDetached() {
        super.onDetached()
        rootView?.removeCallbacks(null)
    }
}