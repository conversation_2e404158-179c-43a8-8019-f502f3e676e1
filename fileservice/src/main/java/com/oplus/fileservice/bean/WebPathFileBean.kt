/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WebPathFileBean
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/6 10:15
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/6       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.bean

import android.os.Build
import androidx.annotation.RequiresApi
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import com.oplus.fileservice.filelist.utils.getDateAndTimeByDefaultFormat
import com.oplus.fileservice.utils.WebMimeTypeHelper
import org.apache.commons.io.FilenameUtils
import java.io.File
import java.nio.file.Files
import java.nio.file.attribute.BasicFileAttributes
import java.util.*

class WebPathFileBean : WebFileBean {
    private val mFileNameFull: String?

    constructor(absolutePath: String) {
        mFilePath = absolutePath
        mFileNameFull = if (!mFilePath.isNullOrEmpty()) {
            File(mFilePath).name
        } else {
            null
        }
        initFileAttributes()
    }


    @RequiresApi(Build.VERSION_CODES.O)
    @Suppress("TooGenericExceptionCaught")
    private fun initFileAttributes() {
        if (!mFilePath.isNullOrEmpty()) {
            var properties: BasicFileAttributes? = null
            try {
                properties =
                    Files.readAttributes(File(mFilePath).toPath(), BasicFileAttributes::class.java)
                properties?.apply {
                    mFileSize = size()
                    mLastModifyDate = getDateAndTimeByDefaultFormat(lastModifiedTime().toMillis())
                    mDateModified = lastModifiedTime().toMillis()
                    mFileFormat = FilenameUtils.getExtension(mFileNameFull)
                    mFileType = if (isDirectory) {
                        mFileName = mFileNameFull
                        mFileFormat = ""
                        WebMimeTypeHelper.DIRECTORY_TYPE
                    } else {
                        mFileName = FilenameUtils.getBaseName(mFileNameFull)
                        WebMimeTypeHelper.getFileTypeFromExtension(mFileFormat)
                    }
                    mLocalType = if (isDirectory) {
                        MimeTypeHelper.DIRECTORY_TYPE
                    } else {
                        MimeTypeHelper.getTypeFromExtension(mFileFormat)
                            ?: MimeTypeHelper.UNKNOWN_TYPE
                    }
                }
            } catch (e: Exception) {
                Log.w("PathFileWrapper", "Failed to read attributes, ${e.message}")
            }
        }
    }
}