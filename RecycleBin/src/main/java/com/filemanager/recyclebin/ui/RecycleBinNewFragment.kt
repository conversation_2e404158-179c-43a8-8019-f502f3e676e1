/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:RecycleBinNewFragment.kt
 * * Description:
 * * Version:1.0
 * * Date :2020/6/16
 * * Author:********
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/6/16,        v1.0,           Create
 ****************************************************************/
package com.filemanager.recyclebin.ui

import android.annotation.SuppressLint
import android.content.Context
import android.content.DialogInterface
import android.content.res.Configuration
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils.getStatusBarHeight
import com.coui.appcompat.tips.def.COUIDefaultTopTips
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.RecycleStore
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.FileManagerDefaultItemAnimator
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.base.observer.SimpleUriChangeObserver.Companion.observerUirChanged
import com.filemanager.common.compat.PrivacyPasswordSettingCompat
import com.filemanager.common.compat.PrivacyPasswordSettingCompat.isRecentlyDeletedSwitchOpen
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.controller.navigation.NavigationInterfaceForMain
import com.filemanager.common.controller.navigation.NavigationType
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.helper.uiconfig.type.ScreenSizeConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.RecycleFileWrapper
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.recyclebin.controller.RecycleFileOperatorController
import com.filemanager.recyclebin.ui.adapter.RecycleBinAdapter
import com.filemanager.recyclebin.ui.decoration.RecycleBinItemDecoration
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.dropdrag.recycleview.ItemDetailsLookup
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.touchshare.TouchShareNotSupportSupplier
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier
import com.oplus.recyclebin.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class RecycleBinNewFragment : RecyclerSelectionVMFragment<RecycleBinViewModel>(), OnBackPressed,
    NavigationBarView.OnItemSelectedListener {
    private companion object {
        private const val TAG = "RecycleBinNewFragment"
        private const val RECYCLE_BIN_REFRESH_DELAY = 500L
        private const val IS_SHOW_NAVI_NORMAL = "isShowNaviNormal"
    }

    private val sortPopupController by lazy { SortPopupController(lifecycle) }
    private val fileEmptyController by lazy { FileEmptyController(lifecycle) }

    /**
     * 回收站数据库操作接口暂未完全适配批量操作，从侧边栏删除大量文件时
     * 每个文件都会触发刷新，短时间多个界面刷新效果叠加导致显示异常。
     * 故先添加间隔实现限制界面的刷新频率，后续回收站支持批量操作后再对此处做进一步优化。
     */
    private val recycleBinRefreshAction = object : Runnable {
        @Volatile
        private var isPosting = false

        fun refresh(view: View) {
            if (isPosting) {
                return
            }
            isPosting = true
            Log.d(TAG, "recycleBinRefreshAction: post reload")
            view.postDelayed(this, RECYCLE_BIN_REFRESH_DELAY)
        }

        override fun run() {
            Log.d(TAG, "recycleBinRefreshAction: execute reload")
            getViewModel()?.loadData()
            isPosting = false
        }
    }

    private var uiToolbar: COUIToolbar? = null
    private var sortEntryView: SortEntryView? = null
    private var headerLayout: COUIDefaultTopTips? = null
    private var headerTipsCountView: TextView? = null
    private var bottomAlertDialogBuilder: COUIAlertDialogBuilder? = null
    private var bottomAlertDialog: AlertDialog? = null
    private var privacySettingPassword = false
    private var pageAdapter: RecycleBinAdapter? = null
    private var layoutManager: LinearLayoutManager? = null
    private var fileOperateController: RecycleFileOperatorController? = null
    private var loadingController: LoadingController? = null
    private var tempSortType = -1
    private var pageTitle: String? = null
    private var isChildDisplay = false
    private var needLoadData = false
    private var hasShowEmpty: Boolean = false
    /**
     * 导航栏同一时间只能显示一个，如果显示了Normal就无法显示EDIT，
     * 因此使用一个Flag去规避折叠屏/平板上重复调用显示导航栏方法的导致的导航栏闪烁的问题。
     */
    private var isNormalShow: Boolean = false

    override fun onAttach(context: Context) {
        super.onAttach(context)
        withBaseVMActivity {
            pageAdapter = RecycleBinAdapter(it, <EMAIL>).apply {
                setHasStableIds(true)
            }
        }
        val bundle = arguments ?: return
        isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
        needLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
        pageTitle = IntentUtils.getTitleFromBundle(context, bundle)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        savedInstanceState?.getBoolean(IS_SHOW_NAVI_NORMAL)?.let {
            isNormalShow = it
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putBoolean(IS_SHOW_NAVI_NORMAL, isNormalShow)
        super.onSaveInstanceState(outState)
    }

    override fun getLayoutResId(): Int {
        return R.layout.recycle_bin_new_fragment
    }

    @SuppressLint("RestrictedApi")
    override fun initView(view: View) {
        rootView = view.findViewById(R.id.coordinator_layout)
        appBarLayout = view.findViewById(R.id.appBarLayout)
        uiToolbar = view.findViewById(R.id.toolbar)
        toolbar = uiToolbar
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        fragmentFastScroller = view.findViewById(R.id.fastScroller)
        headerLayout = view.findViewById(R.id.header_layout)
        headerTipsCountView = view.findViewById(R.id.header_tips_count_view)
        initToolbar()
        sortEntryView = view.findViewById(R.id.sort_entry_view)
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getRecycleBinKey())
        sortEntryView?.setClickSortListener {
            val menu = ActionMenuItem(view.context, 0, R.id.actionbar_sort, 0, 0, "")
            onMenuItemSelected(menu)
        }
        updateLeftRightMargin()
    }

    private fun updatePrivacy() {
        if (!privacySettingPassword) {
            headerLayout?.isVisible = getViewModel()?.isPrivacyPasswordTip == false
            headerLayout?.updateLayoutParams<LinearLayout.LayoutParams> {
                this.width = LinearLayout.LayoutParams.MATCH_PARENT
                this.height = LinearLayout.LayoutParams.WRAP_CONTENT
            }
            headerLayout?.run {
                setTipsText(resources.getString(com.filemanager.common.R.string.privacy_password_tip))
                setStartIcon(
                    ContextCompat.getDrawable(
                        context,
                        com.filemanager.common.R.drawable.privacy_psd_header_lock
                    )
                )
                setPositiveButton(resources.getString(com.filemanager.common.R.string.set_button_text))
                setNegativeButton(resources.getString(com.filemanager.common.R.string.questionnaire_ignore_btn))
                setPositiveButtonListener {
                    createChoicePrivacyOptionsDialog()
                }
                setNegativeButtonListener {
                    getViewModel()?.updatePrivacyPasswordTipShow()
                    headerLayout?.isVisible = false
                }
            }
        } else {
            headerLayout?.isVisible = false
        }
    }

    private fun createChoicePrivacyOptionsDialog() {
        if (Utils.isQuickClick()) {
            Log.d(TAG, "click too fast")
            return
        }
        withBaseVMActivity { act ->
            if (privacySettingPassword) { //设置了密码
                PrivacyPasswordSettingCompat.jumpPrivacySettingPrivacy(act)
            } else {
                val clickListener =
                    DialogInterface.OnClickListener { dialog, position ->
                        Log.d(TAG, "createChoicePrivacyOptionsDialog: chick which = $position")
                        when (position) {
                            0 -> {
                                PrivacyPasswordSettingCompat.jumpPrivacySettingPasswordChoose(act, PrivacyPasswordSettingCompat.SETTINGS_CHOICE_NUM)
                            }

                            1 -> {
                                PrivacyPasswordSettingCompat.jumpPrivacySettingPasswordChoose(act, PrivacyPasswordSettingCompat.SETTINGS_CHOICE_MIX)
                            }

                            2 -> {
                                PrivacyPasswordSettingCompat.jumpPrivacySettingPasswordChoose(act, PrivacyPasswordSettingCompat.SETTINGS_CHOICE_PIC)
                            }
                        }
                        dialog.dismiss()
                    }

                bottomAlertDialogBuilder = COUIAlertDialogBuilder(
                    act, com.support.dialog.R.style.COUIAlertDialog_List_Bottom
                ).apply {
                    bottomAlertDialogBuilder = this
                    setTitle(com.filemanager.common.R.string.privacy_password_choice_title)
                    setItems(
                        com.filemanager.common.R.array.privacy_password_dialog_multi_options,
                        clickListener
                    )
                    setNegativeButton(com.filemanager.common.R.string.dialog_cancel) { _, _ -> }
                    setBlurBackgroundDrawable(false)
                }
                bottomAlertDialog = bottomAlertDialogBuilder?.show()
            }
        }
    }

    private fun initToolbar() {
        uiToolbar?.apply {
            title = pageTitle
            titleMarginStart = 0
            isTitleCenterStyle = false
            inflateMenu(R.menu.recycle_bin_file_list_option)
            setToolbarMenuVisible(this, !isChildDisplay)
            updateToolbarHeight(this)
        }
        runOnBaseVMActivity {
            setSupportActionBar(uiToolbar)
            supportActionBar?.let {
                it.setDisplayHomeAsUpEnabled(!isChildDisplay)
                it.setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
            StatusBarUtil.setStatusBarTransparentAndBlackFont(this)
        }
        rootView?.apply {
            setPadding(paddingLeft, getStatusBarHeight(context), paddingRight, paddingBottom)
        }
    }

    private fun setToolbarMenuVisible(toolbar: COUIToolbar, visible: Boolean) {
        toolbar.menu.findItem(R.id.action_setting)?.isVisible = visible
        updatePrivacyPasswordMenu(toolbar)
    }

    private fun updatePrivacyPasswordMenu(toolbar: COUIToolbar?) {
        if (!PrivacyPasswordSettingCompat.isSupportPrivacyPWD) {
            toolbar?.menu?.findItem(R.id.action_privacy)?.isVisible = false
            headerLayout?.isVisible = false
            return
        }
        lifecycleScope.launch(Dispatchers.IO) {
            privacySettingPassword =
                PrivacyPasswordSettingCompat.checkPrivacySettingPassword(activity)
            Log.d(TAG, "updatePrivacyPasswordMenu spSettingPrivacy:${getViewModel()?.isPrivacyPasswordSetting}" +
                    "spPrivacyPasswordTip:${getViewModel()?.isPrivacyPasswordTip} isSettingPrivacy:$privacySettingPassword")
            withContext(Dispatchers.Main) {
                if (privacySettingPassword && isRecentlyDeletedSwitchOpen(appContext)) {
                    toolbar?.menu?.findItem(R.id.action_privacy)?.title =
                        context?.resources?.getString(com.filemanager.common.R.string.privacy_button_text)
                } else {
                    toolbar?.menu?.findItem(R.id.action_privacy)?.title =
                        context?.resources?.getString(com.filemanager.common.R.string.set_privacy_button_text)
                }
                updatePermissionEmptyMarginTop()
                if (getViewModel()?.isPrivacyPasswordSetting == true) {
                    toolbar?.setRedDot(R.id.action_privacy, -1)
                } else {
                    toolbar?.setRedDot(R.id.action_privacy, 0)
                }
                updatePrivacy()
            }
        }
    }

    private fun updateEditAndSortMenu(toolbar: COUIToolbar) {
        val edit = toolbar.menu.findItem(R.id.actionbar_edit)
        edit?.isVisible = (getViewModel()?.uiState?.value?.fileList?.isNotEmpty() == true)
    }

    private fun initToolbarNormalMode(toolbar: COUIToolbar) {
        withBaseVMActivity {
            it.supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(!isChildDisplay)
                setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
        toolbar.menu.clear()
        toolbar.isTitleCenterStyle = false
        toolbar.title = pageTitle
        toolbar.inflateMenu(R.menu.recycle_bin_file_list_option)

        updateEditAndSortMenu(toolbar)
        setToolbarMenuVisible(toolbar, !isChildDisplay)
    }

    private fun refreshNormalToolbar(toolbar: COUIToolbar) {
        updateEditAndSortMenu(toolbar)
        setToolbarMenuVisible(toolbar, !isChildDisplay)
    }

    private fun initToolbarWithEditMode(toolbar: COUIToolbar) {
        runOnBaseVMActivity {
            supportActionBar?.setDisplayHomeAsUpEnabled(false)
        }
        toolbar.apply {
            menu.clear()
            isTitleCenterStyle = true
            inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        withFragmentRecyclerView {
            layoutManager = LinearLayoutManager(context)
            it.isNestedScrollingEnabled = true
            it.layoutManager = layoutManager
            val animator = FileManagerDefaultItemAnimator()
            it.itemAnimator = animator
            animator.supportsChangeAnimations = false
            pageAdapter?.let { adapter ->
                it.adapter = adapter
                adapter.setRecycleViewLayoutManager(layoutManager)
            }
            it.isForceDarkAllowed = false
            it.addItemDecoration(RecycleBinItemDecoration(pageAdapter, context))
        }
        appBarLayout?.post(Runnable {
            if (!isAdded) {
                return@Runnable
            }
            withFragmentRecyclerView {
                it.clipToPadding = false
                val paddingBottom = if (it.paddingBottom == 0) {
                    MyApplication.appContext
                        .resources.getDimensionPixelSize(R.dimen.ftp_text_margin_bottom)
                } else {
                    it.paddingBottom
                }
                it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, paddingBottom)
            }
        })
        if (needLoadData) {
            onResumeLoadData()
        }
        if (actionCheckPermission().not()) {
            sortEntryView?.setFileCount(0)
        }
        TouchShareSupplier.attach(this, object : TouchShareNotSupportSupplier(CategoryHelper.CATEGORY_RECYCLE_BIN) {
            override fun notSupportTips() {
                CustomToast.showLong(com.filemanager.common.R.string.share_not_support_deleted_file_toast)
            }
        })
    }

    override fun startObserve(): Unit = withBaseVMActivity {
        runOnFragmentRecyclerView {
            post {
                setListModelChangeObserve()
                setUIDataChangeObserve()
                startScrollToPositionObserver()
                startObserveLoadState()
                startObserverRecycleBinChanged()
            }
        }
    }

    private fun startObserverRecycleBinChanged() {
        observerUirChanged(RecycleStore.Files.INTERNAL_CONTENT_URI) {
            withFragmentRecyclerView {
                recycleBinRefreshAction.refresh(it)
            }
        }
    }

    private fun startScrollToPositionObserver() {
        getViewModel()?.positionModel?.observe(this) { positionModel ->
            positionModel?.let {
                layoutManager?.scrollToPosition(it)
            }
        }
    }

    private fun setUIDataChangeObserve() {
        getViewModel()?.uiState?.observe(this) { uiModel ->
            onUiModelChanged(uiModel)
        }
    }

    private fun onUiModelChanged(uiModel: BaseUiModel<RecycleFileWrapper>) {
        Log.d(
            TAG,
            "setUIDataChangeObserve mUiState =" + uiModel.fileList.size
                    + "," + uiModel.selectedList.size + "," + uiModel.keyWord
                    + "," + uiModel.stateModel.toString()
        )
        if (uiModel.fileList.isEmpty()) {
            fileOperateController?.hideDialog()
            hideOrShowSortEntry(false)
            showEmptyView()
        }
        updateNavigationTool()
        val realFileSize = getViewModel()?.getRealFileSize() ?: 0
        sortEntryView?.setFileCount(realFileSize)
        if (uiModel.stateModel.listModel.value == KtConstants.LIST_SELECTED_MODE) {
            uiToolbar?.let {
                refreshSelectToolbar(it)
            }
            (uiModel.fileList as? ArrayList<RecycleFileWrapper>)?.let {
                pageAdapter?.setData(it, uiModel.selectedList, realFileSize)
            }
        } else {
            if (uiModel.fileList.isEmpty()) {
                showEmptyView()
            } else {
                fileEmptyController.hideFileEmptyView()
                hideOrShowSortEntry(true)
            }
            withBaseVMActivity {
                StatusBarUtil.setNavigationBarColor(it)
            }
            uiToolbar?.let {
                refreshNormalToolbar(it)
            }
            (uiModel.fileList as? ArrayList<RecycleFileWrapper>)?.let {
                pageAdapter?.run {
                    setKeyWord(uiModel.keyWord)
                    setData(it, uiModel.selectedList, realFileSize)
                }
            }
        }
    }

    private fun setListModelChangeObserve() {
        getViewModel()?.modeState?.listModel?.observe(this) {
            value -> onListModelChange(value)
        }
    }

    private fun onListModelChange(value: Int) {
        if (getViewModel()?.modeState?.initState == false) {
            uiToolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            return
        }
        Log.d(TAG, "addListDataChangeObserve listModel=$value")
        if (value == KtConstants.LIST_SELECTED_MODE) {
            pageAdapter?.let {
                it.setSelectEnabled(true)
                it.setChoiceModeAnimFlag(true)
            }
            uiToolbar?.let {
                changeActionModeAnim(
                    it,
                    {
                        initToolbarWithEditMode(it)
                        refreshSelectToolbar(it)
                    }
                )
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
            updateNavigationTool()
            (activity as? NavigationInterface)?.let {
                getViewModel()?.setNavigateItemAble(it)
            }
        } else {
            pageAdapter?.let {
                it.setSelectEnabled(false)
                it.setChoiceModeAnimFlag(false)
            }
            uiToolbar?.let {
                changeActionModeAnim(
                    it,
                    { initToolbarNormalMode(it) },
                    (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true)
                )
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
            getViewModel()?.loadData()
        }
    }

    private fun updateNavigationTool() {
        var isShowNaviTool = false
        (activity as? RecycleBinActivity)?.apply {
            isShowNaviTool =
                if (getViewModel()?.modeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    showNavigation(KtConstants.LIST_SELECTED_MODE)
                    true
                } else {
                    if (getViewModel()?.uiState?.value?.fileList.isNullOrEmpty()) {
                        hideNavigation()
                        false
                    } else {
                        showNavigation(KtConstants.LIST_NORMAL_MODE)
                        true
                    }
                }
        } ?: (activity as? NavigationInterfaceForMain)?.apply {
            isShowNaviTool =
                if (getViewModel()?.modeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    if (isNormalShow) {
                        showNavigation(NavigationType.RECYCLE_EDIT, false)
                        isNormalShow = false
                    }
                    true
                } else {
                    if (getViewModel()?.uiState?.value?.fileList.isNullOrEmpty()) {
                        hideNavigation()
                        false
                    } else {
                        if (isNormalShow.not()) {
                            showNavigation(NavigationType.RECYCLE_NORMAL, true)
                            isNormalShow = true
                        }
                        true
                    }
                }
        }
        updateRecycleListPadding(isShowNaviTool)
    }

    private fun updateRecycleListPadding(isShowNaviTool: Boolean = false) {
        val paddingBottomRes = if (isShowNaviTool) {
            activity?.resources?.getDimensionPixelSize(com.filemanager.common.R.dimen.recycle_bin_list_bottom_padding)
        } else {
            activity?.resources?.getDimensionPixelSize(R.dimen.ftp_text_margin_bottom)
        }
        runOnFragmentRecyclerView {
            val newPaddingBottom = paddingBottomRes ?: paddingBottom
            setPadding(paddingLeft, paddingTop, paddingRight, newPaddingBottom)
            runOnFragmentFastScroller {
                trackMarginBottom = newPaddingBottom
            }
        }
    }

    private fun startObserveLoadState() {
        val act = activity ?: return
        loadingController = LoadingController(act, this).apply {
            observe(getViewModel()?.dataLoadState) {
                (getViewModel()?.getRealFileSize() ?: 0) > 0
            }
        }
    }

    private fun refreshSelectToolbar(toolbar: COUIToolbar) {
        val uiState = getViewModel()?.uiState?.value
        val checkedCount = uiState?.selectedList?.size ?: 0
        val isSelectAll = (getViewModel()?.getRealFileSize() == uiState?.selectedList?.size)
        ToolbarUtil.updateToolbarTitle(toolbar, checkedCount, isSelectAll)
        (activity as? NavigationInterface)?.setNavigateItemAble(
            uiState?.selectedList?.isNotEmpty() ?: false,
            hasDrmFile(getViewModel()?.getSelectItems())
        )
    }

    override fun onResume() {
        super.onResume()
        getViewModel()?.getPrivacyPasswordShow()
        if (actionCheckPermission().not()) {
            updatePrivacy()
        }
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        val tempEmpty = getViewModel()?.uiState?.value?.fileList != null
                && getViewModel()?.uiState?.value?.fileList?.size == 0
        Log.d(TAG, "onResume tempEmpty:$tempEmpty")
        if (tempEmpty) {
            loadingController?.dismissLoading(true)
            showEmptyView()
        }
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    private fun showEmptyView() {
        withBaseVMActivity {
            val fragmentRootView = rootView ?: return@withBaseVMActivity
            fileEmptyController.showFileEmptyView(it, fragmentRootView)
            hasShowEmpty = true
            StatusBarUtil.setNavigationBarColor(it)
        }
        fileEmptyController.setFileEmptyTitle(com.filemanager.common.R.string.empty_file)
        val summaryTitle = MyApplication.appContext.getString(com.filemanager.common.R.string.recycle_file_retain_time_tips)
        fileEmptyController.setEmptySummaryVisibilityAndContent(View.VISIBLE, summaryTitle)
        hideOrShowSortEntry(false)
        Log.d(TAG, "showEmptyView")
    }

    private fun hideOrShowSortEntry(isShow: Boolean) {
        sortEntryView?.isVisible = isShow
        headerTipsCountView?.isVisible = isShow
        Log.d(TAG, "hideOrShowSortEntry isShow:$isShow")
    }

    override fun onResumeLoadData() {
        if (!isAdded) {
            return
        }
        checkShowPermissionEmpty()
        getViewModel()?.initLoader(LoaderViewModel.getLoaderController(this))
        val bundle = arguments ?: return
        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
            withBaseVMActivity {
                it.setSupportActionBar(uiToolbar)
                it.supportActionBar?.apply {
                    setDisplayHomeAsUpEnabled(!isChildDisplay)
                    setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
                }
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.recycle_bin_file_list_option, menu)
        uiToolbar?.let {
            setToolbarMenuVisible(it, !isChildDisplay)
        }
    }

    fun onMenuItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            android.R.id.home -> {
                activity?.onBackPressed()
                true
            }
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(activity, CategoryHelper.CATEGORY_RECYCLE_BIN)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, Constants.PAGE_RECYCLE_BIN)
                true
            }
            R.id.actionbar_edit -> {
                getViewModel()?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                true
            }
            R.id.actionbar_sort -> {
                onActionBarSortSelected()
                true
            }
            R.id.action_setting -> {
                Injector.injectFactory<ISetting>()?.startSettingActivity(activity)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, Constants.PAGE_RECYCLE_BIN)
                true
            }
            R.id.action_privacy -> {
                privacyClick()
                true
            }
            R.id.action_select_all -> {
                getViewModel()?.onClickSelectAllOrDeSelectAll(pageAdapter)
                true
            }
            com.filemanager.common.R.id.action_select_cancel -> {
                getViewModel().takeIf {
                    it?.modeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE
                }?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                true
            }
            else -> false
        }
    }

    private fun privacyClick() {
        Log.d(TAG, "action_privacy click")
        getViewModel()?.updatePrivacyPasswordShow()
        updatePrivacyPasswordMenu(uiToolbar)
        createChoicePrivacyOptionsDialog()
    }

    private fun onActionBarSortSelected() = withBaseVMActivity {
        StatisticsUtils.nearMeStatistics(it, StatisticsUtils.SEQUENCE_ACTION)
        val anchorView: View? = view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
        sortPopupController.showSortPopUp(
            it,
            tempSortType,
            anchorView,
            SortRecordModeFactory.getRecycleBinKey(),
            object : SelectItemListener {

                override fun onDismiss() {
                    sortEntryView?.rotateArrow()
                }

                override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                    if (flag) {
                        sortEntryView?.setSortOrder(sortMode, isDesc)
                        getViewModel()?.sortReload()
                    }
                }
            })
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        activity?.let {
            fileOperateController?.onNavigationItemSelected(it, item)
        }
        return false
    }

    override fun pressBack(): Boolean {
        return getViewModel()?.pressBack() ?: false
    }

    fun setTitle(pageTitle: String?) {
        this.pageTitle = pageTitle
    }

    override fun onItemClick(item: ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        item.takeIf {
            it.canLongPressOrClick()
        }?.selectionKey?.let {
            getViewModel()?.uiState?.value?.keyMap?.get(it)
        }?.let { file ->
            withBaseVMActivity { fileOperateController?.onFileClick(it, file, e) }
        }
        Log.w(TAG, "onItemClick ${item.selectionKey}")
        return true
    }

    override fun createViewModel(): RecycleBinViewModel =
        ViewModelProvider(this)[TAG, RecycleBinViewModel::class.java].apply {
            fileOperateController = RecycleFileOperatorController(lifecycle, this).also {
                // The old logic not delay to load data, so here we set mFromMedia to false
                it.setResultListener(FileOperatorListenerImpl(this, false))
            }
        }

    override fun onDragStart(e: MotionEvent): Boolean {
        return false
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        pageAdapter?.onConfigurationChanged()
        withBaseVMActivity {
            fileEmptyController.changeEmptyFileIcon()
            if (getViewModel()?.uiState?.value?.fileList?.isEmpty() == true) {
                showEmptyView()
            } else {
                hideOrShowSortEntry(true)
            }
        }
        sortPopupController.hideSortPopUp()
        fileOperateController?.onConfigurationChanged(newConfig)
    }

    fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.P_CHILD_DISPLAY, isChildDisplay)
        uiToolbar?.let {
            setToolbarMenuVisible(it, !isChildDisplay)
        }
        withBaseVMActivity {
            it.supportActionBar?.apply {
                if (getViewModel()?.isInSelectMode() == true) {
                    setDisplayHomeAsUpEnabled(true)
                    setHomeAsUpIndicator(com.support.snackbar.R.drawable.coui_menu_ic_cancel)
                } else {
                    setDisplayHomeAsUpEnabled(!isChildDisplay)
                }
            }
        }
    }

    override fun checkPermission() {
        withBaseVMActivity {
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.checkPermission(it)
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        getViewModel()?.run {
            if (modeState.listModel.value == KtConstants.LIST_SELECTED_MODE) {
                changeListMode(KtConstants.LIST_SELECTED_MODE)
            }
        }
        configList.forEach {
            if (it is ScreenSizeConfig) {
                updateLeftRightMargin()
            }
        }
    }

    private fun updateLeftRightMargin() {
        pageAdapter?.notifyDataSetChanged()
        headerLayout?.updateLayoutParams<LinearLayout.LayoutParams> {
            this.marginStart = FileImageVHUtils.getListLeftMargin()
            this.marginEnd = FileImageVHUtils.getListRightMargin()
        }
        headerTipsCountView?.updateLayoutParams<LinearLayout.LayoutParams> {
            this.marginStart = FileImageVHUtils.getListLeftMargin()
            this.marginEnd = FileImageVHUtils.getListRightMargin()
        }
        sortEntryView?.updateLeftRightMargin()
    }

    fun backToTop() {
        runOnFragmentRecyclerView { fastSmoothScrollToTop() }
    }

    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        super.updatePermissionEmptyMarginTop()
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_RECYCLE_BIN
    }

    fun exitSelectionMode() {
        getViewModel()?.pressBack()
    }

    fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        baseVMActivity?.let {
            FileImageVHUtils.changedListMargin(
                it,
                it.sideNavigationContainer?.drawerViewWidth ?: 0,
                if (isOpen) KtConstants.SIDE_NAVIGATION_OPEN else KtConstants.SIDE_NAVIGATION_CLOSE
            )
            updateLeftRightMargin()
        }
        return false
    }
}