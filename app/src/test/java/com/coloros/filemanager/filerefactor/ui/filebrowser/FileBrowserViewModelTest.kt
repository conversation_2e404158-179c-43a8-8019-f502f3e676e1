/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.filebrowser
 * * Version     : 1.0
 * * Date        : 2020/7/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.filerefactor.ui.filebrowser

import com.coloros.filemanager.BaseTest
import com.filemanager.common.base.BaseFileBean
import com.oplus.selectdir.filebrowser.SelectFileBrowserViewModel
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class SelectFileBrowserViewModelTest : BaseTest() {
    private val mSelectFileBrowserViewModel = SelectFileBrowserViewModel()

    @Before
    override fun setUp() {
        mContext = RuntimeEnvironment.application
        val file1 = BaseFileBean()
        file1.mData = "1"
        val file2 = BaseFileBean()
        file2.mData = "2"
        val file3 = BaseFileBean()
        file3.mData = "3"
        val list = ArrayList<BaseFileBean>()
        list.add(file1)
        list.add(file2)
        list.add(file3)
        mSelectFileBrowserViewModel.mUiState.value = SelectFileBrowserViewModel.FileBrowserUiModel(list, mSelectFileBrowserViewModel.mModeState,
                ArrayList(), null, HashMap())
    }


    @Test
    @Throws(Exception::class)
    fun should_clickToolbarSelectAll_when_view_model_fun_clickToolbarSelectAll() {
        mSelectFileBrowserViewModel.mUiState.value?.mSelectedList?.clear()
        mSelectFileBrowserViewModel.clickToolbarSelectAll()
        Assert.assertEquals(mSelectFileBrowserViewModel.mUiState.value?.mFileList?.size, mSelectFileBrowserViewModel.mUiState.value?.mSelectedList?.size)
        mSelectFileBrowserViewModel.clickToolbarSelectAll()
        Assert.assertEquals(0, mSelectFileBrowserViewModel.mUiState.value?.mSelectedList?.size)
        mSelectFileBrowserViewModel.mUiState.value?.mSelectedList?.add(mSelectFileBrowserViewModel.mUiState.value?.mFileList?.get(0)?.mData.hashCode())
        mSelectFileBrowserViewModel.clickToolbarSelectAll()
        Assert.assertEquals(mSelectFileBrowserViewModel.mUiState.value?.mFileList?.size, mSelectFileBrowserViewModel.mUiState.value?.mSelectedList?.size)
    }

    @After
    override fun tearDown() {
        mContext = null
    }

}