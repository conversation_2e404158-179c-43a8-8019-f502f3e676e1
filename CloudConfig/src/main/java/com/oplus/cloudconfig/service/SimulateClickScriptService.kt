/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SimulateClickScriptService.kt
 * Description:
 *     The cloud config service api for receive remote scripts collection file.
 *
 * Version: 1.0
 * Date: 2024-06-12
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Bixia<PERSON>.<EMAIL>    2024-06-12   1.0    Create this module
 *********************************************************************************/
package com.oplus.cloudconfig.service

import com.oplus.nearx.cloudconfig.anotation.Config
import com.oplus.nearx.cloudconfig.observable.Observable
import com.oplus.nearx.cloudconfig.stat.Const
import java.io.File

@Config(configCode = "simulate_click_script", type = Const.CONFIG_TYPE_FILE)
internal interface SimulateClickScriptService {

    fun getScriptFile(): Observable<File>
}