/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SelectionHeapUpView.kt
 * Description:
 *     The widget to heap up preview images
 *
 * Version: 1.0
 * Date: 2024-10-18
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-10-18   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.widget

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.annotation.DimenRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.addListener
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.uiutil.ShadowUtils
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.selection.data.HeadUpImageData
import com.oplus.filemanager.preview.selection.data.SelectionItem
import com.oplus.filemanager.preview.utils.logInfo
import kotlin.math.max

internal class SelectionHeapUpView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : ConstraintLayout(context, attrs) {

    private companion object {
        private const val TAG = "SelectionHeapUpView"

        private const val INDEX_1ST = 0
        private const val INDEX_2ND = 1
        private const val INDEX_3RD = 2

        private const val LAYOUT_LEVEL_TOP = 3
        private const val LAYOUT_LEVEL_MIDDLE = 2
        private const val LAYOUT_LEVEL_BOTTOM = 1
        private const val LAYOUT_LV_TOP_ROTATION = 0f
        private const val LAYOUT_LV_MIDDLE_ROTATION = -4f
        private const val LAYOUT_LV_BOTTOM_ROTATION = 4f

        private const val SELECTION_MULTI_WHOLE_BASE_SCALE = 0.85f

        private const val ANIM_SELECTION_CHANGE_DURATION = 300L
        private const val ANIM_TOP_ENTER_FROM_ROTATION = 4f
        private const val ANIM_TOP_1ST_ENTER_FROM_ROTATION = 0f
        private const val ANIM_TOP_1ST_ENTER_FROM_SCALE = 1.1f
        private const val ANIM_TOP_ENTER_FROM_SCALE = 1.2f
        private const val ANIM_TOP_ENTER_TO_SCALE = 1f
        private const val ANIM_ENTER_FROM_ALPHA = 0f
        private const val ANIM_ENTER_TO_ALPHA = 1f
        private const val ANIM_ENTER_FROM_TRANSLATION_X = 50f
        private const val ANIM_ENTER_TO_TRANSLATION_X = 0f

        @JvmStatic
        private fun getRotationByLevel(layoutLevel: Int): Float =
            when (layoutLevel) {
                LAYOUT_LEVEL_TOP -> LAYOUT_LV_TOP_ROTATION
                LAYOUT_LEVEL_MIDDLE -> LAYOUT_LV_MIDDLE_ROTATION
                LAYOUT_LEVEL_BOTTOM -> LAYOUT_LV_BOTTOM_ROTATION
                else -> throw IllegalArgumentException("$TAG: Incorrect layout level")
            }
    }

    private val heapUpViewMap = mutableMapOf<String, HeapUpViewItem>()
    private val itemElevation =
        context.resources.getDimensionPixelOffset(R.dimen.selection_heap_up_item_elevation)
    private val itemShadow =
        Color.argb(
            context.resources.getInteger(com.support.appcompat.R.integer.coui_shadow_color_lv2),
            0,
            0,
            0
        )

    private var onDeselectionEnd: (() -> Unit)? = null

    fun onDeselectionAnimationEnd(onEnd: () -> Unit) {
        onDeselectionEnd = onEnd
    }

    fun updateHeapUp(heapUpMap: Map<SelectionItem, HeadUpImageData>) {
        val newViewMap = mutableMapOf<String, HeapUpViewItem>()
        heapUpMap.forEach { (itemData, imageData) ->
            val path = itemData.fileBean.mData.takeUnless { it.isNullOrEmpty() } ?: run {
                Log.e(TAG, "updateHeapUp: path is empty, bean=${itemData.fileBean.hashCode()}")
                return@forEach
            }
            val viewItem =
                heapUpViewMap[path] ?: HeapUpViewItem(itemData, createHeapUpImageView(imageData))
            viewItem.itemData = itemData
            viewItem.updateImageData(imageData)
            newViewMap[path] = viewItem
        }
        updateLayoutLevel(newViewMap.values)
        applyViewItemsToGroup(newViewMap)
    }

    private fun createHeapUpImageView(
        imageData: HeadUpImageData
    ): IHeapUpImageView = when {
        imageData.isIcon -> PreviewIconItemView(context)
        imageData.isVideoType -> PreviewVideoItemView(context)
        else -> PreviewImageItemView(context)
    }

    fun updateImageData(imageData: HeadUpImageData) {
        val path = imageData.fileBean.mData.takeUnless { it.isNullOrEmpty() } ?: run {
            Log.e(TAG, "updateImageData: path is empty, bean=${imageData.fileBean.hashCode()}")
            return
        }
        val viewItem = heapUpViewMap[path] ?: run {
            Log.d(TAG, "updateImageData: ignore since no its view, ${imageData.fileBean.logInfo()}")
            return
        }
        viewItem.updateImageData(imageData)
        viewItem.applyLayoutInGroup()
        viewItem.updateScale(ANIM_TOP_ENTER_TO_SCALE)
    }

    private fun HeapUpViewItem.updateImageData(imageData: HeadUpImageData) {
        isIcon = imageData.isIcon
        itemView.imageView.scaleType = ImageView.ScaleType.FIT_CENTER
        itemView.setPreviewDrawable(imageData.drawable)
        imageSize = imageData.imageSize
    }

    private fun updateLayoutLevel(viewItems: Collection<HeapUpViewItem>) {
        val sortedItems = mutableListOf<HeapUpViewItem>().apply { addAll(viewItems) }
        sortedItems.sortByDescending { it.itemData.selectPriority }
        sortedItems.getOrNull(INDEX_1ST)?.layoutLevel = LAYOUT_LEVEL_TOP
        sortedItems.getOrNull(INDEX_2ND)?.layoutLevel = LAYOUT_LEVEL_MIDDLE
        sortedItems.getOrNull(INDEX_3RD)?.layoutLevel = LAYOUT_LEVEL_BOTTOM
    }

    private fun applyViewItemsToGroup(newViewMap: Map<String, HeapUpViewItem>) {
        val removeItems = mutableSetOf<HeapUpViewItem>()
        heapUpViewMap.values.forEach {
            if (!newViewMap.values.contains(it)) {
                removeItems.add(it)
            }
        }
        heapUpViewMap.clear()
        heapUpViewMap.putAll(newViewMap)
        removeItems.forEach {
            it.removeViewFromGroupWithAnim()
        }
        newViewMap.values.forEach {
            it.applyViewIntoGroupWithAnim()
        }
    }

    private fun HeapUpViewItem.applyLayoutInGroup() {
        ShadowUtils.setElevationToView(
            itemView.layoutView,
            ShadowUtils.SHADOW_LV2,
            itemElevation,
            itemShadow
        )
        val evaluation = itemView.layoutView.elevation
        val levelRatio = max(layoutLevel - 1, 0)
        itemView.layoutView.z = evaluation + levelRatio * itemElevation
        itemView.layoutView.elevation = evaluation + levelRatio * itemElevation
        itemView.layoutView.updateLayoutParams {
            if (isIcon) {
                applyIconLayoutParams()
            } else {
                applyPreviewLayoutParams()
                imageSize?.let { applyPreviewRatio(it.width, it.height) }
            }
        }
    }

    private fun HeapUpViewItem.applyViewIntoGroupWithAnim() {
        var initFrom = false
        if (!isAttached) {
            addView(itemView.layoutView)
            isAttached = true
            initFrom = true
        } else {
            animation?.cancel()
        }
        applyLayoutInGroup()
        val animHelper = SelectionAnimHelper(this)
        if (initFrom) {
            animHelper.fromAlpha(ANIM_ENTER_FROM_ALPHA)
            when (layoutLevel) {
                LAYOUT_LEVEL_TOP -> {
                    if (heapUpViewMap.size > 1) {
                        animHelper.fromRotation(ANIM_TOP_ENTER_FROM_ROTATION)
                        animHelper.fromTranslationX(ANIM_ENTER_FROM_TRANSLATION_X)
                        animHelper.toTranslationX(ANIM_ENTER_TO_TRANSLATION_X)
                    } else {
                        animHelper.fromRotation(ANIM_TOP_1ST_ENTER_FROM_ROTATION)
                    }
                    animHelper.fromScale(prepareScale(getAnimTopEnterFromScale()))
                }

                LAYOUT_LEVEL_MIDDLE -> {
                    animHelper.fromRotation(LAYOUT_LV_TOP_ROTATION)
                    animHelper.fromScale(prepareScale(ANIM_TOP_ENTER_TO_SCALE))
                }

                LAYOUT_LEVEL_BOTTOM -> {
                    animHelper.fromRotation(LAYOUT_LV_MIDDLE_ROTATION)
                    animHelper.fromScale(prepareScale(ANIM_TOP_ENTER_TO_SCALE))
                }
            }
        }
        val endRotation = getRotationByLevel(layoutLevel)
        animHelper.toRotation(endRotation)
        animHelper.toAlpha(ANIM_ENTER_TO_ALPHA)
        animHelper.toScale(prepareScale(ANIM_TOP_ENTER_TO_SCALE))
        animHelper.start()
    }

    private fun HeapUpViewItem.removeViewFromGroupWithAnim() {
        if (!isAttached) {
            return
        }
        animation?.cancel()
        val animHelper = SelectionAnimHelper(this)
        animHelper.toAlpha(ANIM_ENTER_FROM_ALPHA)
        if (layoutLevel == LAYOUT_LEVEL_TOP) {
            animHelper.toScale(prepareScale(getAnimTopEnterFromScale()))
            if (heapUpViewMap.isNotEmpty()) {
                animHelper.toTranslationX(ANIM_ENTER_FROM_TRANSLATION_X)
                animHelper.toRotation(ANIM_TOP_ENTER_FROM_ROTATION)
            }
        }
        animHelper.onEnd {
            removeView(itemView.layoutView)
            isAttached = false
            if (heapUpViewMap.isEmpty()) {
                onDeselectionEnd?.invoke()
                onDeselectionEnd = null
            }
        }
        animHelper.start()
    }

    private fun HeapUpViewItem.updateScale(scale: Float) {
        val applyScale = prepareScale(scale)
        animation?.toScale(applyScale) ?: run {
            itemView.layoutView.scaleX = applyScale
            itemView.layoutView.scaleY = applyScale
        }
    }

    private fun HeapUpViewItem.prepareScale(originScale: Float): Float {
        val baseScale = if (heapUpViewMap.size > 1) {
            SELECTION_MULTI_WHOLE_BASE_SCALE
        } else {
            1f
        }
        return baseScale * originScale
    }

    private fun getAnimTopEnterFromScale(): Float =
        if (heapUpViewMap.size > 1) ANIM_TOP_ENTER_FROM_SCALE else ANIM_TOP_1ST_ENTER_FROM_SCALE

    private inline fun View.updateLayoutParams(updateBlock: LayoutParams.() -> Unit) {
        val lp = layoutParams as? LayoutParams
        if (lp != null) {
            updateBlock(lp)
            layoutParams = lp
        }
    }

    private fun LayoutParams.applyPreviewRatio(width: Int, height: Int) {
        dimensionRatio = if (width >= height) {
            "H,$width:$height"
        } else {
            "W,$width:$height"
        }
    }

    private fun LayoutParams.applyPreviewLayoutParams() =
        applyHeapUpLayoutParams(R.dimen.preview_image_max_width, R.dimen.preview_image_max_height)

    private fun LayoutParams.applyIconLayoutParams() =
        applyHeapUpLayoutParams(
            R.dimen.selection_heap_up_icon_background_width,
            R.dimen.selection_heap_up_icon_background_height
        )

    private fun LayoutParams.applyHeapUpLayoutParams(
        @DimenRes maxWidthRes: Int,
        @DimenRes maxHeightRes: Int
    ) {
        width = LayoutParams.MATCH_CONSTRAINT
        height = LayoutParams.MATCH_CONSTRAINT
        topToTop = LayoutParams.PARENT_ID
        bottomToBottom = LayoutParams.PARENT_ID
        startToStart = LayoutParams.PARENT_ID
        endToEnd = LayoutParams.PARENT_ID
        matchConstraintMaxWidth = resources.getDimensionPixelOffset(maxWidthRes)
        matchConstraintMaxHeight = resources.getDimensionPixelOffset(maxHeightRes)
    }

    private data class HeapUpViewItem(
        var itemData: SelectionItem,
        val itemView: IHeapUpImageView
    ) {
        var isAttached: Boolean = false
        var isIcon: Boolean = true
        var layoutLevel: Int = 0
        var animation: SelectionAnimHelper? = null
        var imageSize: HeadUpImageData.ImageSizeInfo? = null
    }

    private inner class SelectionAnimHelper(
        private val item: HeapUpViewItem
    ) {
        private val moveEaseInterpolator = COUIMoveEaseInterpolator()

        private var fromScaleX = item.itemView.layoutView.scaleX
        private var fromScaleY = item.itemView.layoutView.scaleY
        private var fromAlpha = item.itemView.layoutView.alpha
        private var fromRotation = item.itemView.layoutView.rotation
        private var fromTranslationX = item.itemView.layoutView.translationX
        private var toScaleX = 1f
        private var toScaleY = 1f
        private var toAlpha = 1f
        private var toRotation = 1f
        private var toTranslationX = 0f
        private var updateScale = false
        private var updateAlpha = false
        private var updateRotation = false
        private var updateTranslationX = false
        private var animDuration = ANIM_SELECTION_CHANGE_DURATION
        private var onStart: (animator: Animator) -> Unit = {}
        private var onEnd: (animator: Animator) -> Unit = {}

        @Volatile
        private var runningAnimator: Animator? = null

        fun fromScale(scale: Float): SelectionAnimHelper = apply {
            if (runningAnimator != null) {
                return@apply
            }
            fromScaleX = scale
            fromScaleY = scale
            updateScale = true
        }

        fun fromAlpha(alpha: Float): SelectionAnimHelper = apply {
            if (runningAnimator != null) {
                return@apply
            }
            fromAlpha = alpha
            updateAlpha = true
        }

        fun fromRotation(rotation: Float): SelectionAnimHelper = apply {
            if (runningAnimator != null) {
                return@apply
            }
            fromRotation = rotation
            updateRotation = true
        }

        fun fromTranslationX(translationX: Float): SelectionAnimHelper = apply {
            if (runningAnimator != null) {
                return@apply
            }
            fromTranslationX = translationX
            updateTranslationX = true
        }

        fun toScale(scale: Float): SelectionAnimHelper = apply {
            if ((runningAnimator != null) && !updateScale) {
                return@apply
            }
            toScaleX = scale
            toScaleY = scale
            updateScale = true
        }

        fun toAlpha(alpha: Float): SelectionAnimHelper = apply {
            if ((runningAnimator != null) && !updateAlpha) {
                return@apply
            }
            toAlpha = alpha
            updateAlpha = true
        }

        fun toRotation(rotation: Float): SelectionAnimHelper = apply {
            if ((runningAnimator != null) && !updateRotation) {
                return@apply
            }
            toRotation = rotation
            updateRotation = true
        }

        fun toTranslationX(translationX: Float): SelectionAnimHelper = apply {
            if ((runningAnimator != null) && !updateTranslationX) {
                return@apply
            }
            toTranslationX = translationX
            updateTranslationX = true
        }

        @Suppress("unused")
        fun setDuration(duration: Long): SelectionAnimHelper = apply {
            animDuration = duration
        }

        @Suppress("unused")
        fun onStart(action: (animator: Animator) -> Unit): SelectionAnimHelper = apply {
            onStart = action
        }

        fun onEnd(action: (animator: Animator) -> Unit): SelectionAnimHelper = apply {
            onEnd = action
        }

        fun start() {
            if (runningAnimator != null) {
                return
            }
            val animator = ValueAnimator.ofFloat(0f, 1f)
            runningAnimator = animator
            animator.interpolator = moveEaseInterpolator
            animator.duration = animDuration
            animator.addUpdateListener {
                val progress = it.animatedValue as Float
                applyAnimProgress(progress)
            }
            animator.addListener(onEnd = ::endAnimation, onCancel = ::endAnimation)
            animator.addListener(onStart = onStart, onEnd = onEnd)
            applyAnimProgress(0f)
            animator.start()
            item.animation = this
        }

        private fun applyAnimProgress(progress: Float) {
            if (updateScale) {
                val scaleXStart = fromScaleX
                val scaleXDiff = toScaleX - fromScaleX
                val scaleYStart = fromScaleY
                val scaleYDiff = toScaleY - fromScaleY
                item.itemView.layoutView.scaleX = scaleXStart + (progress * scaleXDiff)
                item.itemView.layoutView.scaleY = scaleYStart + (progress * scaleYDiff)
            }
            if (updateAlpha) {
                val alphaStart = fromAlpha
                val alphaDiff = toAlpha - fromAlpha
                item.itemView.layoutView.alpha = alphaStart + (progress * alphaDiff)
            }
            if (updateRotation) {
                val rotationStart = fromRotation
                val rotationDiff = toRotation - fromRotation
                item.itemView.layoutView.rotation = rotationStart + (progress * rotationDiff)
            }
            if (updateTranslationX) {
                val translationXStart = fromTranslationX
                val translationXDiff = toTranslationX - fromTranslationX
                item.itemView.layoutView.translationX = translationXStart + (progress * translationXDiff)
                item.itemView.layoutView.translationX = translationXStart + (progress * translationXDiff)
            }
        }

        fun cancel() {
            val animator = runningAnimator ?: return
            animator.cancel()
            endAnimation(animator)
        }

        private fun endAnimation(animator: Animator) {
            if (runningAnimator != animator) {
                return
            }
            runningAnimator = null
            if (item.animation == this) {
                item.animation = null
            }
        }
    }

    private sealed interface IHeapUpImageView {

        val layoutView: View

        val imageView: AppCompatImageView

        fun setPreviewDrawable(drawable: Drawable?)
    }

    private abstract class AbsPreviewImageItemView @JvmOverloads constructor(
        context: Context,
        attrs: AttributeSet? = null
    ) : AppCompatImageView(context, attrs), IHeapUpImageView {

        override val layoutView: View
            get() = this@AbsPreviewImageItemView
        override val imageView: AppCompatImageView
            get() = this@AbsPreviewImageItemView

        override fun setPreviewDrawable(drawable: Drawable?) {
            setImageDrawable(drawable)
        }
    }

    private class PreviewImageItemView @JvmOverloads constructor(
        context: Context,
        attrs: AttributeSet? = null
    ) : AbsPreviewImageItemView(context, attrs) {

        init {
            setBackgroundResource(R.drawable.selection_item_cover_background)
        }
    }

    private class PreviewVideoItemView @JvmOverloads constructor(
        context: Context,
        attrs: AttributeSet? = null
    ) : AbsPreviewImageItemView(context, attrs) {

        init {
            setBackgroundResource(R.drawable.selection_icon_cover_background)
        }
    }

    private class PreviewIconItemView @JvmOverloads constructor(
        context: Context,
        attrs: AttributeSet? = null
    ) : FrameLayout(context, attrs), IHeapUpImageView {

        override val layoutView: View = this@PreviewIconItemView
        override val imageView: AppCompatImageView = AppCompatImageView(context)
        private val iconWidth =
            context.resources.getDimensionPixelSize(R.dimen.preview_default_file_icon_width)
        private val iconHeight =
            context.resources.getDimensionPixelSize(R.dimen.preview_default_file_icon_height)

        init {
            setBackgroundResource(R.drawable.selection_icon_cover_background)
        }

        override fun setPreviewDrawable(drawable: Drawable?) {
            imageView.setImageDrawable(drawable)
        }

        override fun onAttachedToWindow() {
            super.onAttachedToWindow()
            addView(imageView, iconWidth, iconHeight)
        }

        override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
            super.onLayout(changed, left, top, right, bottom)
            if (!changed) {
                return
            }
            val lp = imageView.layoutParams as LayoutParams
            if (lp.gravity != Gravity.CENTER) {
                lp.gravity = Gravity.CENTER
                imageView.layoutParams = lp
            }
        }
    }
}