/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : ExportAppSwitchApi.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/8/28
 * * Author      : muning
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.exportdmp

import android.content.Context
import android.util.Log
import androidx.annotation.WorkerThread
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.appswitch.IAppSwitchApi
import com.oplus.filemanager.interfaze.dmpsearch.IExportDmpSearchApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.function.Consumer

object ExportAppSwitchApi : IAppSwitchApi {

    const val TAG = "ExportAppSwitchApi"

    override fun isCurrentAppSwitchConfigFeatureOn(context: Context): Boolean {
        return false
    }

    override fun isCurrentThirdAppListenConfigFeatureOn(): Boolean {
        return false
    }

    override fun updateThirdAppListenConfig(thirdAppConfigJsonString: String) {
    }

    override fun initDmpAndAppSwitch(useService: Boolean) {
        Log.i(TAG, "initAppSwitchManager $useService")
        CoroutineScope(Dispatchers.Default).launch {
            Log.i(TAG, "initDmpAndAppSwitch start")
            initDmp()
            Log.i(TAG, "initDmpAndAppSwitch end")
        }
    }

    override fun trigDbSync(useWorkerThread: Boolean) {
    }

    @WorkerThread
    @Synchronized
    fun initDmp() {
        val dmpSearchApi = Injector.injectFactory<IExportDmpSearchApi>()
        dmpSearchApi?.initDmpSdk(MyApplication.appContext)
    }

    override fun getAppListConfigReady(): Boolean {
        return false
    }

    override fun setAppListConfigReadyCallback(consumer: Consumer<Boolean>?) {
    }
}