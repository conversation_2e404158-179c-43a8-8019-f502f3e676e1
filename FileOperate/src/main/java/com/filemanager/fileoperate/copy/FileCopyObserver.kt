/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileCopyObserver.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(Liu<PERSON><EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.copy

import android.animation.Animator
import android.animation.ValueAnimator
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.view.ContextThemeWrapper
import android.view.Gravity
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.progressbar.COUIHorizontalProgressBar
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.*
import com.filemanager.fileoperate.base.ACTION_CANCELLED
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.copy.DialogFactory.createSpaceNotEnoughDialog
import com.oplus.filemanager.dfm.DFMManager
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils

const val ERROR_PATH_NULL = 0
const val ERROR_IN_SAME_DIR = 1
const val ERROR_STORAGE_NOT_ENOUGH = 2
const val ERROR_BAD_SDCARD = 3
const val ERROR_COPY = 4
const val ERROR_STORAGE_NOT_ENOUGH_SAVE = 5

const val SHOW_BAD_SDCARD_NOTICE_MSG = 5
const val SHOW_REPLACE_OR_FAIL_DIALOG = 6

const val ERROR_COPY_DFM_DISCONNECT = 8
const val ERROR_CUT_DFM_DISCONNECT = 9
const val ERROR_DFS_STORAGE_CUT_NOT_ENOUGH = 10
const val ERROR_DFS_STORAGE_COPY_NOT_ENOUGH = 11

open class FileCopyObserver(context: ContextThemeWrapper) : BaseFileActionObserver(context) {

    companion object {
        private const val STORAGE_TYPE = "storage_type"
        private const val SDCARD = "sdcard"
        private const val PACKAGE_NAME = "package_name"
        private const val ACTION_BAD_STORAGE_NOTICE = "oppo.intent.action.BAD_STORAGE_NOTICE"
        private const val ACTION_BAD_STORAGE_NOTICE_OPLUS = "oplus.intent.action.BAD_STORAGE_NOTICE"
    }

    private var mReplaceDialog: FileReplaceDialog? = null
    private var mBadSdcardDialog: Dialog? = null
    private var spaceNotEnoughDialog: Dialog? = null
    protected open val TAG = "FileCopyObserver"
    private var remoteDeviceName: String? = DFMManager.getDFSDeviceName()

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        return when (result.first) {
            ACTION_FAILED -> {
                when (result.second) {
                    ERROR_PATH_NULL -> CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
                    ERROR_IN_SAME_DIR -> CustomToast.showLong(com.filemanager.common.R.string.paste_same_file_normal)
                    ERROR_BAD_SDCARD -> {
                        if (mBadSdcardDialog?.isShowing != true) {
                            mBadSdcardDialog = DialogFactory.createBadSdcardDialog(context)
                            mBadSdcardDialog?.show()
                        }
                    }
                    ERROR_COPY -> {
                        // Do nothing currently
                        // CustomToast.showShort(R.string.toast_copy_file_error)
                    }
                    SHOW_BAD_SDCARD_NOTICE_MSG -> showBadSdcardNotice(context)
                    is Pair<*, *> -> {
                        (result.second as Pair<*, *>).let {
                            when (it.first) {
                                ERROR_STORAGE_NOT_ENOUGH -> showSpaceNotEnoughNotice(context,
                                        it.second as String)
                                ERROR_IN_SAME_DIR -> CustomToast.showShort(it.second as String)
                                ERROR_DFS_STORAGE_COPY_NOT_ENOUGH -> CustomToast.showShort(it.second as String)
                                ERROR_DFS_STORAGE_CUT_NOT_ENOUGH -> CustomToast.showShort(it.second as String)
                                ERROR_STORAGE_NOT_ENOUGH_SAVE -> showSpaceNotEnoughNoticeSave(context, it.second as String)
                            }
                        }
                    }
                }
                false
            }
            SHOW_REPLACE_OR_FAIL_DIALOG -> {
                Log.d(TAG, "SHOW_REPLACE_OR_FAIL_DIALOG result.second = ${result.second}")
                dismissProgressDialog()
                mReplaceDialog?.dismiss()
                if (result.second is FileReplaceBean) {
                    val replaceBean = (result.second as FileReplaceBean)
                    mReplaceDialog = DialogFactory.createFileReplaceDialog(context, replaceBean)
                    mReplaceDialog?.show()
                }
                true
            }
            ACTION_DONE -> {
                Log.d(TAG, "ACTION_DONE result.second = ${result.second}")
                if ((context is Activity) && (result.second is String)) {
                    COUISnackBarUtils.show(context, com.filemanager.common.R.string.copy_success) {
                        val fileBrowser = Injector.injectFactory<IFileBrowser>()
                        fileBrowser?.toFileBrowserActivity(context, result.second as String)
                    }
                } else {
                    CustomToast.showLong(com.filemanager.common.R.string.copy_success)
                }
                false
            }
            ACTION_CANCELLED -> {
                Log.d(TAG, "ACTION_CANCELLED result.second = ${result.second}")
                when (result.second) {
                    ERROR_COPY_DFM_DISCONNECT -> CustomToast.showShort(com.filemanager.common.R.string.copy_failed_by_device_disconnect)
                    ERROR_CUT_DFM_DISCONNECT -> {
                        val toastString = if (remoteDeviceName.isNullOrEmpty() || !NewFunctionSwitch.isSupportDfmSearch) {
                            context.getString(com.filemanager.common.R.string.move_failed_by_device_disconnect)
                        } else {
                            context.getString(com.filemanager.common.R.string.toast_move_failed_by_device_name_disconnect, remoteDeviceName)
                        }
                        CustomToast.showShort(toastString)
                    }
                }
                false
            }
            else -> false
        }
    }

    override fun recycle() {
        Log.d(TAG, "recycle")
        mReplaceDialog?.dismiss()
        mBadSdcardDialog?.dismiss()
        mLifecycle = null
        mContext.clear()
    }

    private fun showBadSdcardNotice(context: Context) {
        Log.d(TAG, "showBadSdcardNotice")
        sendBadStorageNoticeCast(context, ACTION_BAD_STORAGE_NOTICE)
        if (SdkUtils.isAtLeastR()) {
            sendBadStorageNoticeCast(context, ACTION_BAD_STORAGE_NOTICE_OPLUS)
        }
    }

    private fun sendBadStorageNoticeCast(context: Context, action: String) {
        val intent = Intent(action)
        intent.putExtra(STORAGE_TYPE, SDCARD)
        intent.putExtra(PACKAGE_NAME, context.packageName)
        if (Utils.isNeededSdk27()) {
            intent.setPackage(Constants.SYSTEMUI_PACKAGE_NAME)
            CollectPrivacyUtils.collectInstalledAppList(Constants.SYSTEMUI_PACKAGE_NAME)
        }
        context.sendBroadcast(intent)
    }

    private fun showSpaceNotEnoughNotice(context: Context, storage: String) {
        if (storage.isEmpty()) {
            CustomToast.showShort(com.filemanager.common.R.string.storage_space_not_enough)
        } else {
            val msg = context.getString(
                com.filemanager.common.R.string.disk_space_not_enough, storage,
                context.getString(com.filemanager.common.R.string.unable_to_paste))
            if (FeatureCompat.sPhoneManagerStartInfo != null) {
                spaceNotEnoughDialog = createSpaceNotEnoughDialog(context, msg)
                spaceNotEnoughDialog?.show()
            } else {
                CustomToast.showShort(msg)
            }
        }
    }

    private fun showSpaceNotEnoughNoticeSave(context: Context, notice: String) {
        if (FeatureCompat.sPhoneManagerStartInfo != null) {
            spaceNotEnoughDialog = createSpaceNotEnoughDialog(context, notice)
            spaceNotEnoughDialog?.show()
        } else {
            CustomToast.showShort(notice)
        }
    }

    override fun updateProgressDialogExternalStorageEnd(
        context: ContextThemeWrapper,
        result: Pair<Any, Any>
    ) {
        CustomToast.sToast?.cancel()
        Log.d(BaseFileActionObserver.TAG, "ACTION_DONE_FOR_EXTERNAL_PATH")
        mProgressDialog?.window?.findViewById<COUIHorizontalProgressBar>(com.support.dialog.R.id.progress)
            ?.let { progressBar ->
                val animator = ValueAnimator.ofInt(EXTERNAL_STORAGE_COPY_PROGRESS, MAX_PROGRESS)
                    .setDuration(EXTERNAL_STORAGE_COPY_ANIM_MILLS)
                animator.addUpdateListener {
                    progressBar.progress = animator.animatedValue as Int
                }

                animator.addListener(object : Animator.AnimatorListener {
                    override fun onAnimationStart(animation: Animator) {}
                    override fun onAnimationEnd(animation: Animator) {
                        dismissProgressDialog()
                        onActionDone(result.first == ACTION_DONE, result.second)
                        val resultNew = Pair(ACTION_DONE, result.second)
                        onChanged(context, resultNew)
                    }

                    override fun onAnimationCancel(animation: Animator) {}
                    override fun onAnimationRepeat(animation: Animator) {}
                })
                animator.start()
            }
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || mReplaceDialog?.isShowing() ?: false
                || mBadSdcardDialog?.isShowing ?: false || spaceNotEnoughDialog?.isShowing ?: false
    }


    data class FileReplaceBean(var mIsReplace: Boolean, var mFileName: String?) {
        var chooseListener: FileReplaceDialog.OnChooseListener? = null
        fun recycle() {
            chooseListener = null
        }
    }
}

private object DialogFactory {

    fun createFileReplaceDialog(context: Context, replaceBean: FileCopyObserver.FileReplaceBean)
            : FileReplaceDialog? {
        return if (isContextValid(context)) FileReplaceDialog(context, replaceBean) else null
    }

    fun createBadSdcardDialog(context: Context): Dialog? {
        return if (isContextValid(context)) {
            val dialog: Dialog = COUIAlertDialogBuilder(context).setTitle(com.filemanager.common.R.string.sdcard_bad)
                    .setPositiveButton(com.filemanager.common.R.string.positive_ok) { _, _ -> }.show()
            val view = dialog.findViewById<View>(android.R.id.message)
            if (view is TextView) {
                view.gravity = Gravity.CENTER_HORIZONTAL
            }
            dialog
        } else null
    }

    fun createSpaceNotEnoughDialog(context: Context, msg: String): AlertDialog {
        val dialog = COUIAlertDialogBuilder(context).setTitle(msg)
                .setPositiveButton(com.filemanager.common.R.string.garbage_cleanup) { _, _ ->
                    KtAppUtils.startPhoneManager(context)
                }
                .setNegativeButton(com.filemanager.common.R.string.alert_dialog_cancel, null)
                .create()
        dialog.setCanceledOnTouchOutside(false)
        return dialog
    }

    private fun isContextValid(context: Context) = (context is Activity) && !((context.isFinishing || context.isDestroyed))
}