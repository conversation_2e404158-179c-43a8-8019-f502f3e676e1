/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WeChatUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/7 19:37
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.wechatsdk

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

object WeChatUtils {
    private const val TAG = "WeChatUtils"
    private const val TENCENT_DOCS_USER_NAME = "gh_252c5f06840b"
    private const val PREFIX_OPEN_DOCUMENT = "pages/detail/detail?url="

    const val APP_ID = "wx91ff24d1d01ff0de"

    var api: IWXAPI = WXAPIFactory.createWXAPI(MyApplication.sAppContext, APP_ID)

    private var isRegister = false

    private val registerReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            Log.d(TAG, "onReceive -> intent = ${intent?.extras}")
            api.registerApp(APP_ID)
        }
    }

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    @JvmStatic
    fun register(context: Context) {
        Log.d(TAG, "register-> isRegister = $isRegister")
        if (isRegister) {
            return
        }
        api.registerApp(APP_ID)
        if (SdkUtils.isAtLeastT()) {
            context.registerReceiver(
                registerReceiver,
                IntentFilter(ConstantsAPI.ACTION_REFRESH_WXAPP),
                Context.RECEIVER_NOT_EXPORTED
            )
        } else {
            context.registerReceiver(registerReceiver, IntentFilter(ConstantsAPI.ACTION_REFRESH_WXAPP))
        }
        isRegister = true
    }

    @JvmStatic
    fun unregister(context: Context) {
        Log.d(TAG, "unregister-> isRegister = $isRegister")
        if (!isRegister) {
            return
        }
        api.unregisterApp()
        runCatching {
            context.applicationContext.unregisterReceiver(registerReceiver)
            isRegister = false
        }.onFailure {
            Log.e(TAG, "unregister -> failure cause ${it.message}")
        }
    }

    @JvmStatic
    fun isWxInstalled(context: Context): Boolean {
        val packageInfo = runCatching {
            context.packageManager.getPackageInfo(Constants.WECHAT_PACKAGE_NAME, 0)
        }.getOrNull()
        val applicationInfo = runCatching {
            context.packageManager.getApplicationInfo(Constants.WECHAT_PACKAGE_NAME, 0)
        }.getOrNull()
        Log.d(TAG, "isWxInstalled -> packageInfo = $packageInfo ; applicationInfo = $applicationInfo")
        return (packageInfo != null) && (applicationInfo != null) && applicationInfo.enabled
    }

    @JvmStatic
    fun authorization(authUrl: String) {
        Log.d(TAG, "authorization start...")
        val req = WXLaunchMiniProgram.Req()
        req.userName = TENCENT_DOCS_USER_NAME
        req.path = authUrl
        req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE
        runCatching {
            api.sendReq(req)
        }.onFailure {
            Log.e(TAG, "authorization error cause ${it.message}")
        }
    }

    @JvmStatic
    fun openFile(fileUrl: String) {
        val req = WXLaunchMiniProgram.Req()
        req.userName = TENCENT_DOCS_USER_NAME
        req.path = "$PREFIX_OPEN_DOCUMENT${URLEncoder.encode(fileUrl, StandardCharsets.UTF_8.toString())}"
        req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE
        runCatching {
            api.sendReq(req)
        }.onFailure {
            Log.e(TAG, "openFile error cause ${it.message}")
        }
    }
}