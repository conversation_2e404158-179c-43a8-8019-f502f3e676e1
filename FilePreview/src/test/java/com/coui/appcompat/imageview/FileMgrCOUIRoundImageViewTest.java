package com.coui.appcompat.imageview;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.View;

import androidx.appcompat.widget.AppCompatImageView;

import com.coui.appcompat.roundRect.COUIRoundRectUtil;
import com.support.appcompat.R;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.annotation.Config;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyFloat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * FileMgrCOUIRoundImageView的单元测试类
 * 测试FileMgrCOUIRoundImageView的各种功能和行为
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 29)
public class FileMgrCOUIRoundImageViewTest {

    private Context context;
    private FileMgrCOUIRoundImageView imageView;
    @Mock private Drawable mockDrawable;  // 模拟的Drawable对象
    @Mock private Bitmap mockBitmap;     // 模拟的Bitmap对象
    @Mock private Canvas mockCanvas;     // 模拟的Canvas对象
    @Mock private COUIRoundRectUtil mockRoundRectUtil;  // 模拟的圆角矩形工具类
    private AutoCloseable mockitoCloseable;  // Mockito资源管理对象

    /**
     * 测试前的初始化方法
     * 创建测试环境和模拟对象
     */
    @Before
    public void setUp() {
        mockitoCloseable = MockitoAnnotations.openMocks(this);  // 初始化Mockito注解
        context = RuntimeEnvironment.getApplication();  // 获取Robolectric的Context
        imageView = new FileMgrCOUIRoundImageView(context);  // 创建测试对象
    }

    /**
     * 测试后的清理方法
     * 关闭Mockito资源
     */
    @After
    public void tearDown() throws Exception {
        mockitoCloseable.close();  // 关闭Mockito资源
    }

    /**
     * 测试只传入Context的构造函数
     */
    @Test
    public void testConstructorWithContext() {
        assertNotNull(imageView);  // 验证对象创建成功
    }

    /**
     * 测试传入Context和AttributeSet的构造函数
     */
    @Test
    public void testConstructorWithAttrs() {
        // 使用Robolectric的AttributeSetBuilder创建AttributeSet
        AttributeSet attrs = org.robolectric.Robolectric.buildAttributeSet()
            .addAttribute(R.attr.couiBorderRadius, "10dp")  // 添加圆角半径属性
            .addAttribute(R.attr.couiType, "0")             // 添加类型属性
            .addAttribute(R.attr.couiHasBorder, "false")    // 添加是否有边框属性
            .addAttribute(R.attr.couiHasDefaultPic, "true") // 添加是否有默认图片属性
            .addAttribute(R.attr.couiRoundImageViewOutCircleColor, "@android:color/white")  // 添加外圆颜色属性
            .build();
        
        FileMgrCOUIRoundImageView view = new FileMgrCOUIRoundImageView(context, attrs);
        assertNotNull(view);  // 验证带属性的对象创建成功
    }

    /**
     * 测试设置是否有边框的方法
     */
    @Test
    public void testSetHasBorder() {
        imageView.setHasBorder(true);  // 设置边框为true
        imageView.invalidate();        // 触发重绘
    }

    /**
     * 测试设置边框宽度的方法
     */
    @Test
    public void testSetBorderWidth() {
        int testWidth = 5;
        imageView.setBorderWidth(testWidth);  // 设置边框宽度
        imageView.invalidate();               // 触发重绘
    }

    /**
     * 测试设置外圆颜色的方法
     */
    @Test
    public void testSetOutCircleColor() {
        int testColor = Color.RED;
        imageView.setOutCircleColor(testColor);  // 设置外圆颜色
        imageView.invalidate();                  // 触发重绘
    }

    /**
     * 测试设置是否有默认图片的方法
     */
    @Test
    public void testSetHasDefaultPic() {
        imageView.setHasDefaultPic(false);  // 设置无默认图片
        imageView.invalidate();             // 触发重绘
    }

    /**
     * 测试设置圆角半径的方法
     */
    @Test
    public void testSetBorderRectRadius() {
        int testRadius = 20;
        imageView.setBorderRectRadius(testRadius);  // 设置圆角半径
        imageView.invalidate();                     // 触发重绘
    }

    /**
     * 测试设置视图类型的方法
     */
    @Test
    public void testSetType() {
        imageView.setType(0);  // 设置类型为圆形
        imageView.invalidate(); // 触发重绘
    }

    /**
     * 测试圆形类型下的测量方法
     */
    @Test
    public void testOnMeasureForCircleType() {
        imageView.setType(0);  // 设置类型为圆形
        imageView.measure(View.MeasureSpec.makeMeasureSpec(200, View.MeasureSpec.EXACTLY),
                View.MeasureSpec.makeMeasureSpec(300, View.MeasureSpec.EXACTLY));
        assertEquals(200, imageView.getMeasuredWidth());   // 验证宽度测量结果
        assertEquals(200, imageView.getMeasuredHeight());  // 验证高度测量结果
    }

    /**
     * 测试圆形类型的绘制方法
     */
    @Test
    public void testOnDrawWithCircleType() {
        imageView.setType(0);  // 设置类型为圆形
        imageView.invalidate(); // 触发重绘
    }

    /**
     * 测试圆角类型的绘制方法
     */
    @Test
    public void testOnDrawWithRoundType() {
        imageView.setType(1);  // 设置类型为圆角
        imageView.invalidate(); // 触发重绘
    }

    /**
     * 测试阴影类型的绘制方法
     */
    @Test
    public void testOnDrawWithShadowType() {
        imageView.setType(2);  // 设置类型为阴影
        imageView.invalidate(); // 触发重绘
    }

    /**
     * 测试刷新视图的方法
     */
    @Test
    public void testRefresh() {
        imageView.refresh();    // 调用刷新方法
        imageView.invalidate(); // 触发重绘
    }
}