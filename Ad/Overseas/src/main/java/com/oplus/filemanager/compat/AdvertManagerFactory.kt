/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AdvertManagerFactory
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/3/22 19:14
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/3/22       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat

import com.oplus.filemanager.compat.brand.fake.FakeHomePageAdMgr
import com.oplus.filemanager.compat.brand.fake.FakeSubPageAdMgr
import com.oplus.filemanager.compat.brand.oppo.OPPOHomePageAdMgr
import com.oplus.filemanager.compat.brand.oppo.OPPOSubPageAdMgr
import com.oplus.filemanager.compat.brand.realme.AdvertEntry.Companion.initRealmeHomeAd
import com.oplus.filemanager.compat.brand.realme.AdvertEntry.Companion.initRealmeSubAd
import com.oplus.filemanager.compat.interfaces.IHomePageAdMgr
import com.oplus.filemanager.compat.interfaces.ISubPageAdMgr
import com.oplus.filemanager.compat.proxy.HomePageAdMgrProxy
import com.oplus.filemanager.compat.proxy.SubPageAdMgrProxy

fun getHomePageAdMgr(brand: AdBrand): IHomePageAdMgr {
    val homePageAdMgr = when (brand) {
        AdBrand.OPPO -> OPPOHomePageAdMgr()
        AdBrand.REALME -> initRealmeHomeAd()
        AdBrand.NULL -> FakeHomePageAdMgr()
    }
    return HomePageAdMgrProxy(homePageAdMgr)
}

fun getSubPageAdMgr(brand: AdBrand): ISubPageAdMgr {
    val subPageAdMrg = when (brand) {
        AdBrand.OPPO -> OPPOSubPageAdMgr()
        AdBrand.REALME -> initRealmeSubAd()
        AdBrand.NULL -> FakeSubPageAdMgr()
    }
    return SubPageAdMgrProxy(subPageAdMrg)
}