/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemoteFileAdapter
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseFolderAnimAdapter
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.isActivityAndInvalid
import com.filemanager.common.view.TextViewSnippet
import com.oplus.dropdrag.SelectionTracker
import com.oplus.filemanager.category.remotedevice.viewholder.BaseRemoteFileVH
import com.oplus.filemanager.category.remotedevice.viewholder.RemoteFileGridVH
import com.oplus.filemanager.category.remotedevice.viewholder.RemoteFileLargeListVH
import com.oplus.filemanager.category.remotedevice.viewholder.RemoteFileListVH
import com.oplus.filemanager.interfaze.cloudconfig.ICloudConfigApi
import java.util.HashMap
import java.util.Locale

class RemoteFileAdapter(content: Context, lifecycle: Lifecycle) :
    BaseFolderAnimAdapter<RecyclerView.ViewHolder, BaseFileBean>(content), LifecycleObserver {
    companion object {
        const val TAG = "RemoteFileAdapter"
    }


    private var mKeyWord: String? = null
    private val mSizeCache = HashMap<String, String>()
    private var mThreadManager = ThreadManager(lifecycle)
    private val mImgRadius = MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.file_list_bg_radius)

    init {
        lifecycle.addObserver(this)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        super.onRemoveCallBack()
        mSizeCache.clear()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (KtConstants.SCAN_MODE_GRID == viewType) {
            RemoteFileGridVH(
                LayoutInflater.from(parent.context)
                    .inflate(RemoteFileGridVH.getLayoutId(), parent, false)
            )
        } else if (KtConstants.SCAN_MODE_LIST_LARGE == viewType) {
            RemoteFileLargeListVH.create(parent, mImgRadius)
        } else {
            RemoteFileListVH(
                LayoutInflater.from(parent.context)
                    .inflate(RemoteFileListVH.getLayoutId(), parent, false), mImgRadius
            ).apply {
                setUpdateViewList(object : RemoteFileListVH.UpdateViewListener {
                    override fun updateViewByAlias(textViewSnippet: TextViewSnippet, path: String) {
                        val cloudConfigApi = Injector.injectFactory<ICloudConfigApi>()
                        cloudConfigApi?.updateViewByAlias(textViewSnippet, path)
                    }
                })
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        var type = if ((mFiles.size == 0) && (position >= 0) && (position < mOldFiles.size)) {
            mOldFiles[position].mFileWrapperViewType ?: mScanViewModel
        } else if ((position >= 0) && (position < mFiles.size)) {
            mFiles[position].mFileWrapperViewType ?: mScanViewModel
        } else {
            mScanViewModel
        }
        if (KtConstants.SCAN_MODE_LIST == type && WindowUtils.supportLargeScreenLayout(mContext)) {
            type = KtConstants.SCAN_MODE_LIST_LARGE
        }
        return type
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (mContext.isActivityAndInvalid()) {
            Log.d(TAG, "onBindViewHolder: Activity is Destroyed!")
            return
        }
        if ((position < 0) || (position >= mFiles.size)) {
            return
        }
        if (holder.itemView.alpha == 0f) {
            holder.itemView.alpha = 1f
        }
        val file = mFiles[position]
        if (holder is BaseRemoteFileVH) {
            holder.loadData(
                mContext, getItemKey(file, position),
                file, mChoiceMode, mSelectionArray, this
            )
        } else if (holder is RemoteFileLargeListVH) {
            holder.loadData(
                mContext, getItemKey(file, position),
                file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
        }
        if (holder is RemoteFileListVH) {
            holder.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
        } else if (holder is RemoteFileLargeListVH) {
            holder.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
        }
        if (file.mIsDirectory) {
            val tag = DropTag(CategoryHelper.CATEGORY_FILE_REMOTE_MAC, DropTag.Type.ITEM_VIEW_FOLDER)
            tag.filePath = file.mData
            tag.isMac = true
            holder.itemView.tag = tag
        } else {
            holder.itemView.tag = null
        }
        holder.itemView?.let {
            if (DragUtils.getSelectedFiles()?.contains(file) == true && DragUtils.isDragging) {
                it.alpha = DragUtils.SELECTED_ITEMVIEW_ALPHA
            } else {
                it.alpha = 1f
            }
        }
    }

    fun setKeyWord(key: String) {
        mKeyWord = key
    }

    override fun getItemId(position: Int): Long {
        return getItemKeyByPosition(position)?.toLong() ?: SelectionTracker.NO_LONG_ITEM_ID
    }

    /**
     * getItemKey must return different code,position hashcode may return same as path hashcode
     */
    override fun getItemKey(item: BaseFileBean, position: Int): Int {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return position.hashCode()
        }

        return path.toLowerCase(Locale.getDefault()).hashCode()
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        if (mScanViewModel == KtConstants.SCAN_MODE_LIST) {
            setChoiceModeAnimFlag(flag)
        }
    }
}