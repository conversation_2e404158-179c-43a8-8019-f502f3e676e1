package com.coloros.filemanager.utils

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.wrapper.RecycleBinLiveData
import com.oplus.statistics.OplusTrack
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test

class StatisticsUtilsTest {
    lateinit var mContext: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mContext = mockk(relaxed = true)
    }

    @Test
    fun `should verify when on common without`() {
        StatisticsUtils.onCommon(null, "test")

        mockkStatic(OplusTrack::class)
        every { OplusTrack.onCommon(any(), any(), any(), any(), null) }.returns(true)
        StatisticsUtils.onCommon(mContext, "test")


        StatisticsUtils.onCommon(null, "test", "test")
        StatisticsUtils.onCommon(mContext, "test", "test")

        val map = HashMap<String, String>()
        StatisticsUtils.onCommon(null, "test", map)
        every { OplusTrack.onCommon(any(), any(), any(), any(), map) }.returns(true)
        StatisticsUtils.onCommon(mContext, "test", map)

        StatisticsUtils.onCommon(null, "test分组事件", "test", map)
        verify(inverse = true) { OplusTrack.onCommon(mContext, any(), "test分组事件", "test", map) }
        StatisticsUtils.onCommon(mContext, "test分组事件", "test", map)
        verify { OplusTrack.onCommon(mContext, any(), "test分组事件", "test", map) }

        unmockkStatic(OplusTrack::class)
    }


    @Test
    fun `should verify when on pause without`() {
        StatisticsUtils.onPause(null)

        mockkStatic(OplusTrack::class)
        every { OplusTrack.onPause(mContext) }.returns(Unit)
        StatisticsUtils.onPause(mContext)

        StatisticsUtils.setStatisticsEnable(false)
        StatisticsUtils.onPause(mContext)
        unmockkStatic(OplusTrack::class)
    }

    @Test
    fun `should verify when on resume without`() {
        StatisticsUtils.onResume(null)

        mockkStatic(OplusTrack::class)
        every { OplusTrack.onResume(mContext) }.returns(Unit)
        StatisticsUtils.onResume(mContext)

        StatisticsUtils.setStatisticsEnable(false)
        StatisticsUtils.onResume(mContext)
        unmockkStatic(OplusTrack::class)
    }

    @Test
    fun `should verify when near me statistics without string`() {
        mockkStatic(StatisticsUtils::class)
        every { StatisticsUtils.onCommon(mContext, "test") }.returns(Unit)
        StatisticsUtils.nearMeStatistics(mContext, "test")

        StatisticsUtils.setStatisticsEnable(false)
        StatisticsUtils.nearMeStatistics(mContext, "test")
        unmockkStatic(StatisticsUtils::class)
    }

    @Test
    fun `should verify when near me statistics without`() {
        StatisticsUtils.setStatisticsEnable(false)
        StatisticsUtils.nearMeStatistics(mContext, 111)

        StatisticsUtils.setStatisticsEnable(true)
        mockkStatic(StatisticsUtils::class)
        every { StatisticsUtils.onCommon(mContext, any()) }.returns(Unit)

        StatisticsUtils.nearMeStatistics(mContext, CategoryHelper.CATEGORY_AUDIO)
        StatisticsUtils.nearMeStatistics(mContext, CategoryHelper.CATEGORY_VIDEO)
        StatisticsUtils.nearMeStatistics(mContext, CategoryHelper.CATEGORY_IMAGE)
        StatisticsUtils.nearMeStatistics(mContext, CategoryHelper.CATEGORY_DOC)
        StatisticsUtils.nearMeStatistics(mContext, CategoryHelper.CATEGORY_APK)
        StatisticsUtils.nearMeStatistics(mContext, CategoryHelper.CATEGORY_DOWNLOAD)
        StatisticsUtils.nearMeStatistics(mContext, CategoryHelper.CATEGORY_COMPRESS)
        StatisticsUtils.nearMeStatistics(mContext, CategoryHelper.CATEGORY_BLUETOOTH)
        StatisticsUtils.nearMeStatistics(mContext, CategoryHelper.CATEGORY_QQ)
        StatisticsUtils.nearMeStatistics(mContext, CategoryHelper.CATEGORY_MICROMSG)
        every { StatisticsUtils.onCommon(mContext, any(), any() as Map<String, String?>) }.returns(Unit)
        StatisticsUtils.nearMeStatistics(mContext, -1)
        unmockkStatic(StatisticsUtils::class)
    }

    @Test
    fun `should verify when near me statistics compress preview caller without`() {
        StatisticsUtils.nearMeStatisticsCompressPreviewCaller(mContext, null)

        StatisticsUtils.setStatisticsEnable(false)
        StatisticsUtils.nearMeStatisticsCompressPreviewCaller(mContext, "test")

        StatisticsUtils.setStatisticsEnable(true)
        mockkStatic(OplusTrack::class)
        every { OplusTrack.onCommon(mContext, any(), any(), any()) }.returns(true)
        StatisticsUtils.nearMeStatisticsCompressPreviewCaller(mContext, "test")

        kotlin.runCatching {
            every { OplusTrack.onCommon(mContext, any(), any(), any()) }.throws(Exception("test-error"))
            StatisticsUtils.nearMeStatisticsCompressPreviewCaller(mContext, "test")
        }.onFailure {
        }
        unmockkStatic(OplusTrack::class)
    }

    @Test
    fun `should verify when near me statistics recycle bin delete count without`() {
        StatisticsUtils.setStatisticsEnable(false)
        StatisticsUtils.nearMeStatisticsRecycleBinDeleteCount(mContext, 0, 0)

        StatisticsUtils.setStatisticsEnable(true)
        StatisticsUtils.nearMeStatisticsRecycleBinDeleteCount(mContext, 0, 0)

        StatisticsUtils.setStatisticsEnable(true)
        mockkStatic(OplusTrack::class)
        every { OplusTrack.onCommon(mContext, any(), any(), any(), any<Map<String, String>>()) }.returns(true)

        StatisticsUtils.nearMeStatisticsRecycleBinDeleteCount(mContext, RecycleBinLiveData
                .RECYCLE_DELETE, 100)

        StatisticsUtils.nearMeStatisticsRecycleBinDeleteCount(null, RecycleBinLiveData
                .RECYCLE_DELETE, 100)

        StatisticsUtils.nearMeStatisticsRecycleBinDeleteCount(null, RecycleBinLiveData
                .RECYCLE_AUTO_CLEAN, 100)

        StatisticsUtils.nearMeStatisticsRecycleBinDeleteCount(mContext, RecycleBinLiveData
                .RECYCLE_AUTO_CLEAN, 100)
        unmockkStatic(OplusTrack::class)
    }

    @Test
    fun `should verify when near me statistics recycle bin file save date without`() {
        StatisticsUtils.setStatisticsEnable(false)
        StatisticsUtils.nearMeStatisticsRecycleBinFileSaveDate(mContext, 0, -1)

        StatisticsUtils.setStatisticsEnable(true)
        StatisticsUtils.nearMeStatisticsRecycleBinFileSaveDate(mContext, 0, -1)

        StatisticsUtils.nearMeStatisticsRecycleBinFileSaveDate(null, RecycleBinLiveData
                .RECYCLE_DELETE, 100)

        mockkStatic(OplusTrack::class)
        every { OplusTrack.onCommon(mContext, any(), any(), any(), any<Map<String, String>>()) }.returns(true)

        StatisticsUtils.nearMeStatisticsRecycleBinFileSaveDate(mContext, RecycleBinLiveData
                .RECYCLE_AUTO_CLEAN, 100)

        StatisticsUtils.nearMeStatisticsRecycleBinFileSaveDate(mContext, RecycleBinLiveData
                .RECYCLE_RESTORE, 100)

        StatisticsUtils.nearMeStatisticsRecycleBinFileSaveDate(null, RecycleBinLiveData
                .RECYCLE_RESTORE, 100)
        unmockkStatic(OplusTrack::class)
    }

    @Test
    fun `should verify when near me statistics success search without`() {
        StatisticsUtils.setStatisticsEnable(false)
        StatisticsUtils.nearMeStatisticsSuccessSearch(null, 0)

        StatisticsUtils.setStatisticsEnable(true)
        mockkStatic(OplusTrack::class)
        every { OplusTrack.onCommon(mContext, any(), any(), any<Map<String, String>>()) }.returns(true)

        StatisticsUtils.nearMeStatisticsSuccessSearch(mContext, CategoryHelper.CATEGORY_AUDIO)

        StatisticsUtils.nearMeStatisticsSuccessSearch(mContext, CategoryHelper.CATEGORY_VIDEO)

        StatisticsUtils.nearMeStatisticsSuccessSearch(mContext, CategoryHelper.CATEGORY_IMAGE)

        StatisticsUtils.nearMeStatisticsSuccessSearch(mContext, CategoryHelper.CATEGORY_DOC)

        StatisticsUtils.nearMeStatisticsSuccessSearch(mContext, CategoryHelper.CATEGORY_APK)

        StatisticsUtils.nearMeStatisticsSuccessSearch(mContext, CategoryHelper.CATEGORY_QQ)

        StatisticsUtils.nearMeStatisticsSuccessSearch(mContext, CategoryHelper.CATEGORY_MICROMSG)

        StatisticsUtils.nearMeStatisticsSuccessSearch(mContext, -1)
        unmockkStatic(OplusTrack::class)
    }

    @Test
    fun `should verify when statistics category memory size without`() {
        StatisticsUtils.setStatisticsEnable(false)
        StatisticsUtils.statisticsCategoryMemorySize(0, null)

        StatisticsUtils.setStatisticsEnable(true)
        val context = mockk<Context>(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
        mockkStatic(StatisticsUtils::class)
        every { StatisticsUtils.onCommon(context, any(), any<Map<String, String>>()) }.returns(Unit)

        StatisticsUtils.statisticsCategoryMemorySize(CategoryHelper.CATEGORY_APK, "test")

        StatisticsUtils.statisticsCategoryMemorySize(CategoryHelper.CATEGORY_AUDIO, "test")

        StatisticsUtils.statisticsCategoryMemorySize(CategoryHelper.CATEGORY_DOC, "test")

        StatisticsUtils.statisticsCategoryMemorySize(CategoryHelper.CATEGORY_VIDEO, "test")

        StatisticsUtils.statisticsCategoryMemorySize(CategoryHelper.CATEGORY_IMAGE, "test")

        StatisticsUtils.statisticsCategoryMemorySize(CategoryHelper.CATEGORY_COMPRESS, "test")
        StatisticsUtils.statisticsCategoryMemorySize(CategoryHelper.CATEGORY_COMPRESS, "test")
        unmockkStatic(StatisticsUtils::class)
    }

    @Test
    fun `should return Label_VALUE_TYPE when getLabelFileType if localType is Audio`() {
        mockkStatic(StatisticsUtils::class)
        val type = "1"
        every { StatisticsUtils.getLabelFileType(any()) } answers { callOriginal() }
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.AUDIO_TYPE))
        unmockkStatic(StatisticsUtils::class)
    }

    @Test
    fun `should return LABEL_VALUE_TYPE_DOC when getLabelFileType if localType is Doc`() {
        mockkStatic(StatisticsUtils::class)
        val type = "2"
        every { StatisticsUtils.getLabelFileType(any()) } answers { callOriginal() }
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.TXT_TYPE))
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.DOC_TYPE))
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.DOCX_TYPE))
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.XLS_TYPE))
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.PPTX_TYPE))
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.PDF_TYPE))
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.OFD_TYPE))
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.XLSX_TYPE))
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.PPT_TYPE))
        unmockkStatic(StatisticsUtils::class)
    }

    @Test
    fun `should return LABEL_VALUE_TYPE_COMPRESS when getLabelFileType if localType is compress`() {
        mockkStatic(StatisticsUtils::class)
        every { StatisticsUtils.getLabelFileType(any()) } answers { callOriginal() }
        val type = "3"
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.COMPRESSED_TYPE))
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.ZIP_TYPE))
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.RAR_TYPE))
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.JAR_TYPE))
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.P7ZIP_TYPE))
        unmockkStatic(StatisticsUtils::class)
    }

    @Test
    fun `should return LABEL_VALUE_TYPE_PIC when getLabelFileType if localType is image`() {
        mockkStatic(StatisticsUtils::class)
        every { StatisticsUtils.getLabelFileType(any()) } answers { callOriginal() }
        val type = "4"
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.IMAGE_TYPE))
        unmockkStatic(StatisticsUtils::class)
    }

    @Test
    fun `should return LABEL_VALUE_TYPE_VIDEO when getLabelFileType if localType is video`() {
        mockkStatic(StatisticsUtils::class)
        every { StatisticsUtils.getLabelFileType(any()) } answers { callOriginal() }
        val type = "5"
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.VIDEO_TYPE))
        unmockkStatic(StatisticsUtils::class)
    }

    @Test
    fun `should return LABEL_VALUE_TYPE_DIRECTORY when getLabelFileType if localType is dic`() {
        mockkStatic(StatisticsUtils::class)
        every { StatisticsUtils.getLabelFileType(any()) } answers { callOriginal() }
        val type = "6"
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.DIRECTORY_TYPE))
        unmockkStatic(StatisticsUtils::class)
    }

    @Test
    fun `should return LABEL_VALUE_TYPE_INSTALL_APK when getLabelFileType if localType is apk`() {
        mockkStatic(StatisticsUtils::class)
        every { StatisticsUtils.getLabelFileType(any()) } answers { callOriginal() }
        val type = "7"
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.APPLICATION_TYPE))
        unmockkStatic(StatisticsUtils::class)
    }

    @Test
    fun `should return LABEL_VALUE_TYPE_OTHER when getLabelFileType if localType is other`() {
        mockkStatic(StatisticsUtils::class)
        every { StatisticsUtils.getLabelFileType(any()) } answers { callOriginal() }
        val type = "8"
        assertEquals(type, StatisticsUtils.getLabelFileType(MimeTypeHelper.UNKNOWN_TYPE))
        unmockkStatic(StatisticsUtils::class)
    }
}