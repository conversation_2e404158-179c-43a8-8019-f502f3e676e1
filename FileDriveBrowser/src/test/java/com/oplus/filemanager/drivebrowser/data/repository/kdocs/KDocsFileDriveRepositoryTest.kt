/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : KDocsFileDriveRepositoryTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/30 15:04
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/30       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.data.repository.kdocs

import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.MimeTypeHelper.Companion.DOCX_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.DOC_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.IMAGE_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.KDOCS_DBT_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.KDOCS_KSHEET_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.KDOCS_OTL_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.OFD_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.PDF_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.PPTX_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.PPT_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.TXT_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.XLSX_TYPE
import com.filemanager.common.helper.MimeTypeHelper.Companion.XLS_TYPE
import com.oplus.filemanager.drivebrowser.data.api.KDocsFileDriveService
import com.oplus.filemanager.drivebrowser.data.model.AuthorizationPath
import com.oplus.filemanager.drivebrowser.data.model.AuthorizationState
import com.oplus.filemanager.drivebrowser.data.model.BaseResponse
import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsDocumentItem
import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsDocumentListsResponse
import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsFileEditUrlResp
import com.oplus.filemanager.drivebrowser.data.model.kdocs.KDocsToken
import com.oplus.filemanager.drivebrowser.data.repository.kdocs.KDocsFileDriveRepository.Companion.AUTH_EXPIRED
import com.oplus.filemanager.drivebrowser.di.NetworkModule
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.TestResult
import kotlinx.coroutines.test.runTest
import org.junit.Assert
import org.junit.Test
import kotlin.time.Duration.Companion.seconds

class KDocsFileDriveRepositoryTest {

    @Test
    fun testCheckAuthorizationState(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<KDocsFileDriveService>()
        coEvery { service.checkAuthorizationState() }.throws(Exception())
        val repository = KDocsFileDriveRepository(service)
        val authorizationErrorState = repository.checkAuthorizationState()
        Assert.assertFalse(authorizationErrorState)
        coEvery { service.checkAuthorizationState() }.returns(BaseResponse(data = AuthorizationState(null)))
        val authorizationNullState = repository.checkAuthorizationState()
        Assert.assertFalse(authorizationNullState)
        coEvery { service.checkAuthorizationState() }.returns(BaseResponse(data = AuthorizationState(true)))
        val authorizationState = repository.checkAuthorizationState()
        Assert.assertTrue(authorizationState)
    }

    @Test
    fun testGetAuthUrl(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<KDocsFileDriveService>()
        coEvery { service.buildAuthorizationPath() }.throws(Exception())
        val repository = KDocsFileDriveRepository(service)
        val authUrlErrorResult = repository.getAuthUrl()
        Assert.assertNotNull(authUrlErrorResult?.exception)
        coEvery { service.buildAuthorizationPath() }.returns(BaseResponse(data = null))
        val authUrlNullResult = repository.getAuthUrl()
        Assert.assertFalse(authUrlNullResult?.auth ?: false)
        coEvery { service.buildAuthorizationPath() }.returns(
            BaseResponse(
                data = AuthorizationPath(
                    true,
                    AUTHORIZATION_URL
                )
            )
        )
        val authUrlResult = repository.getAuthUrl()
        Assert.assertNotNull(authUrlResult)
        authUrlResult?.let {
            Assert.assertTrue(it.auth)
            Assert.assertEquals(AUTHORIZATION_URL, it.authUrl)
        }
    }

    @Test
    fun testSaveAuthorizationResult(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<KDocsFileDriveService>()
        coEvery { service.callbackAuth(any(), any()) }.throws(Exception())
        val repository = KDocsFileDriveRepository(service)
        val saveErrorResult = repository.saveAuthorizationResult(AUTH_CODE, AUTH_STATE)
        Assert.assertFalse(saveErrorResult.first)
        Assert.assertEquals("", saveErrorResult.second)
        coEvery { service.callbackAuth(any(), any()) }.returns(BaseResponse(code = -1, msg = ERROR_MSG))
        val saveInvalidResult = repository.saveAuthorizationResult(AUTH_CODE, AUTH_STATE)
        Assert.assertFalse(saveInvalidResult.first)
        Assert.assertEquals(ERROR_MSG, saveInvalidResult.second)
        coEvery { service.callbackAuth(any(), any()) }.returns(BaseResponse(code = 0))
        val saveSuccessResult = repository.saveAuthorizationResult(AUTH_CODE, AUTH_STATE)
        Assert.assertTrue(saveSuccessResult.first)
        Assert.assertEquals("", saveSuccessResult.second)
    }

    @Test
    fun testCancelAuth(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<KDocsFileDriveService>()
        coEvery { service.cancelAuth() }.throws(Exception())
        val repository = KDocsFileDriveRepository(service)
        val cancelErrorResult = repository.cancelAuth()
        Assert.assertFalse(cancelErrorResult.first)
        Assert.assertEquals("", cancelErrorResult.second)
        coEvery { service.cancelAuth() }.returns(BaseResponse(code = -1, msg = ERROR_MSG))
        val cancelInvalidResult = repository.cancelAuth()
        Assert.assertFalse(cancelInvalidResult.first)
        Assert.assertEquals(ERROR_MSG, cancelInvalidResult.second)
        coEvery { service.cancelAuth() }.returns(BaseResponse(code = 0))
        val cancelSuccessResult = repository.cancelAuth()
        Assert.assertTrue(cancelSuccessResult.first)
        Assert.assertEquals("", cancelSuccessResult.second)
    }

    @Test
    fun testGetWebViewAuthUrl(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<KDocsFileDriveService>()
        coEvery { service.buildWebViewAuthorizationPath(any()) }.throws(Exception())
        val repository = KDocsFileDriveRepository(service)
        val webViewAuthUrlErrorResult = repository.getWebViewAuthUrl()
        Assert.assertFalse(webViewAuthUrlErrorResult!!.auth)
        Assert.assertEquals("", webViewAuthUrlErrorResult.authUrl)
        coEvery { service.buildWebViewAuthorizationPath(any()) }.returns(BaseResponse(data = null))
        val webViewAuthUrlNullResult = repository.getWebViewAuthUrl()
        Assert.assertNull(webViewAuthUrlNullResult)
        coEvery { service.buildWebViewAuthorizationPath(any()) }.returns(
            BaseResponse(
                data = AuthorizationPath(
                    true,
                    AUTHORIZATION_URL
                )
            )
        )
        val webViewAuthUrl = repository.getWebViewAuthUrl()
        Assert.assertTrue(webViewAuthUrl!!.auth)
        Assert.assertEquals(AUTHORIZATION_URL, webViewAuthUrl.authUrl)
    }

    @Test
    fun testGetDocumentLists(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<KDocsFileDriveService>()
        val lists = mutableListOf<KDocsDocumentItem>()
        lists.add(KDocsDocumentItem())
        coEvery { service.getDocumentLists(any()) }.returns(
            BaseResponse(
                code = 0,
                data = KDocsDocumentListsResponse(
                    list = lists,
                    nextOffset = 1
                )
            )
        )
        val repository = KDocsFileDriveRepository(service)
        mockkObject(repository)
        every { repository.filterDocumentFile(any()) } returns true

        val documentLists = repository.getDocumentLists("desc", 1, 0, 20, FILE_ID)
        Assert.assertEquals(0, documentLists.code)
        Assert.assertEquals(1, documentLists.data?.lists?.size)
        Assert.assertEquals(false, documentLists.data?.isLastPage)
        Assert.assertEquals(1, documentLists.data?.nextPageOffset)
    }

    @Test
    fun testRenameDocument(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<KDocsFileDriveService>()
        coEvery { service.renameDocument(any()) }.throws(Exception())
        val repository = KDocsFileDriveRepository(service)
        val renameErrorResult = repository.renameDocument(FILE_ID, TITLE)
        Assert.assertTrue(renameErrorResult.code == -1)
        Assert.assertEquals(null, renameErrorResult.msg)

        // rename code not success
        coEvery { service.renameDocument(any()) }.returns(BaseResponse(code = -1, msg = ERROR_MSG))
        val renameInvalidResult = repository.renameDocument(FILE_ID, TITLE)
        Assert.assertFalse(renameInvalidResult.code == 0)
        Assert.assertEquals(ERROR_MSG, renameInvalidResult.msg)

        // rename success
        coEvery { service.renameDocument(any()) }.returns(BaseResponse(code = 0))
        val renameSuccessResult = repository.renameDocument(FILE_ID, TITLE)
        Assert.assertTrue(renameSuccessResult.code == 0)
    }

    @Test
    fun testRenameFolder(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<KDocsFileDriveService>()
        coEvery { service.renameDocument(any()) }.throws(Exception())
        val repository = KDocsFileDriveRepository(service)
        val renameErrorResult = repository.renameFolder(FOLDER_ID, TITLE)
        Assert.assertTrue(renameErrorResult.code == -1)
        Assert.assertEquals(null, renameErrorResult.msg)

        // rename code not success
        coEvery { service.renameDocument(any()) }.returns(BaseResponse(code = -1, msg = ERROR_MSG))
        val renameInvalidResult = repository.renameFolder(FOLDER_ID, TITLE)
        Assert.assertFalse(renameInvalidResult.code == 0)
        Assert.assertEquals(ERROR_MSG, renameInvalidResult.msg)

        // rename success
        coEvery { service.renameDocument(any()) }.returns(BaseResponse(code = 0))
        val renameSuccessResult = repository.renameFolder(FOLDER_ID, TITLE)
        Assert.assertTrue(renameSuccessResult.code == 0)
    }

    @Test
    fun testDeleteFile(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<KDocsFileDriveService>()
        coEvery { service.deleteFile(any()) }.throws(Exception())
        val repository = KDocsFileDriveRepository(service)
        val deleteErrorResult = repository.deleteFile(FILE_ID, LIST_TYPE)
        Assert.assertFalse(deleteErrorResult.first)
        Assert.assertEquals("", deleteErrorResult.second)

        // delete code not success
        coEvery { service.deleteFile(any()) }.returns(BaseResponse(code = -1, msg = ERROR_MSG))
        val deleteInvalidResult = repository.deleteFile(FILE_ID, LIST_TYPE)
        Assert.assertFalse(deleteInvalidResult.first)
        Assert.assertEquals(ERROR_MSG, deleteInvalidResult.second)

        // delete success
        coEvery { service.deleteFile(any()) }.returns(BaseResponse(code = 0))
        val deleteSuccessResult = repository.deleteFile(FILE_ID, LIST_TYPE)
        Assert.assertTrue(deleteSuccessResult.first)
    }

    @Test
    fun testDeleteFolder(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<KDocsFileDriveService>()
        coEvery { service.deleteFile(any()) }.throws(Exception())
        val repository = KDocsFileDriveRepository(service)
        val deleteErrorResult = repository.deleteFolder(FOLDER_ID)
        Assert.assertFalse(deleteErrorResult.first)
        Assert.assertEquals("", deleteErrorResult.second)

        // delete code not success
        coEvery { service.deleteFile(any()) }.returns(BaseResponse(code = -1, msg = ERROR_MSG))
        val deleteInvalidResult = repository.deleteFolder(FOLDER_ID)
        Assert.assertFalse(deleteInvalidResult.first)
        Assert.assertEquals(ERROR_MSG, deleteInvalidResult.second)

        // delete success
        coEvery { service.deleteFile(any()) }.returns(BaseResponse(code = 0))
        val deleteSuccessResult = repository.deleteFolder(FOLDER_ID)
        Assert.assertTrue(deleteSuccessResult.first)
    }

    @Test
    fun testGetEditUrl(): TestResult = runTest(timeout = testCaseTimeout) {
        val service = mockk<KDocsFileDriveService>()
        coEvery { service.getCustomEditUrl(any()) }.throws(Exception())
        val repository = KDocsFileDriveRepository(service)
        val editUrlErrorResult = repository.getEditUrl(FILE_ID)
        Assert.assertEquals(NetworkModule.buildKingDocsOpenUrl("", ""), editUrlErrorResult)

        // null edit url
        coEvery { service.getCustomEditUrl(any()) }.returns(BaseResponse(data = null))
        val editUrlNullResult = repository.getEditUrl(FILE_ID)
        Assert.assertEquals("", editUrlNullResult)

        // success edit url
        coEvery { service.getCustomEditUrl(any()) }.returns(
            BaseResponse(
                data = KDocsFileEditUrlResp(
                    token = KDocsToken(
                        K_DOCS_TOKEN
                    ), url = AUTHORIZATION_URL
                )
            )
        )
        val editUrlResult = repository.getEditUrl(FILE_ID)
        Assert.assertEquals(NetworkModule.buildKingDocsOpenUrl(K_DOCS_TOKEN, AUTHORIZATION_URL), editUrlResult)
    }

    @Test
    fun `should return right when call filterDocumentFile if folder`() {
        //given
        val item = mockk<KDocsDocumentItem>()
        every { item.ftype } returns KDocsDocumentItem.KDOCS_TYPE_FOLDER
        val service = mockk<KDocsFileDriveService>()
        val repository = KDocsFileDriveRepository(service)
        //when
        val result = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result, true)
    }

    @Test
    fun `should return right when call filterDocumentFile if share folder`() {
        //given
        val item = mockk<KDocsDocumentItem>()
        every { item.ftype } returns KDocsDocumentItem.KDOCS_TYPE_SHARE_FOLDER
        val service = mockk<KDocsFileDriveService>()
        val repository = KDocsFileDriveRepository(service)
        //when
        val result = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result, true)
    }

    @Test
    fun `should return right when call filterDocumentFile`() {
        //setup
        mockkObject(MimeTypeHelper)
        val item = mockk<KDocsDocumentItem>()
        every { item.ftype } returns "file"
        val service = mockk<KDocsFileDriveService>()
        val repository = KDocsFileDriveRepository(service)

        //given
        every { item.fname } returns "test.doc"
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns DOC_TYPE
        //when
        val result = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result, true)

        //given
        every { item.fname } returns "test.docx"
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns DOCX_TYPE
        //when
        val result1 = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result1, true)

        //given
        every { item.fname } returns "test.txt"
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns TXT_TYPE
        //when
        val result2 = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result2, true)

        //given
        every { item.fname } returns "test.xls"
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns XLS_TYPE
        //when
        val result3 = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result3, true)

        //given
        every { item.fname } returns "test.pptx"
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns PPTX_TYPE
        //when
        val result4 = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result4, true)

        //given
        every { item.fname } returns "test.pdf"
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns PDF_TYPE
        //when
        val result5 = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result5, true)

        //given
        every { item.fname } returns "test.xlsx"
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns XLSX_TYPE
        //when
        val result6 = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result6, true)

        //given
        every { item.fname } returns "test.ppt"
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns PPT_TYPE
        //when
        val result7 = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result7, true)

        //given
        every { item.fname } returns "test.ofd"
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns OFD_TYPE
        //when
        val result8 = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result8, true)

        //teardown
        unmockkObject(MimeTypeHelper)
    }

    @Test
    fun `should return false when call filterDocumentFile if image`() {
        //setup
        mockkObject(MimeTypeHelper)
        val item = mockk<KDocsDocumentItem>()
        every { item.ftype } returns "file"
        val service = mockk<KDocsFileDriveService>()
        val repository = KDocsFileDriveRepository(service)

        //given
        every { item.fname } returns "test.jpg"
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns IMAGE_TYPE
        //when
        val result = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result, false)

        //teardown
        unmockkObject(MimeTypeHelper)
    }

    @Test
    fun `should return true when call filterDocumentFile if otl`() {
        //setup
        mockkObject(MimeTypeHelper)
        val item = mockk<KDocsDocumentItem>()
        every { item.ftype } returns "file"
        val service = mockk<KDocsFileDriveService>()
        val repository = KDocsFileDriveRepository(service)

        //given
        every { item.fname } returns "test.otl"
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns KDOCS_OTL_TYPE
        //when
        val result = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result, true)

        //teardown
        unmockkObject(MimeTypeHelper)
    }

    @Test
    fun `should return true when call filterDocumentFile if dbt`() {
        //setup
        mockkObject(MimeTypeHelper)
        val item = mockk<KDocsDocumentItem>()
        every { item.ftype } returns "file"
        val service = mockk<KDocsFileDriveService>()
        val repository = KDocsFileDriveRepository(service)

        //given
        every { item.fname } returns "test.dbt"
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns KDOCS_DBT_TYPE
        //when
        val result = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result, true)

        //teardown
        unmockkObject(MimeTypeHelper)
    }

    @Test
    fun `should return true when call filterDocumentFile if ksheet`() {
        //setup
        mockkObject(MimeTypeHelper)
        val item = mockk<KDocsDocumentItem>()
        every { item.ftype } returns "file"
        val service = mockk<KDocsFileDriveService>()
        val repository = KDocsFileDriveRepository(service)

        //given
        every { item.fname } returns "test.ksheet"
        every { MimeTypeHelper.getTypeFromExtension(any()) } returns KDOCS_KSHEET_TYPE
        //when
        val result = repository.filterDocumentFile(item)
        //then
        Assert.assertEquals(result, true)

        //teardown
        unmockkObject(MimeTypeHelper)
    }

    @Test
    fun `should return true when call isFileAuth if code is 0`() {
        //given
        val service = mockk<KDocsFileDriveService>()
        val baseResponse = mockk<BaseResponse<Any>>()
        every { baseResponse.code } returns 0
        coEvery { service.getPermissionFile(any()) } returns baseResponse
        val repository = KDocsFileDriveRepository(service)
        //when
        val result = runBlocking {
            repository.isFileAuth("")
        }
        //then
        Assert.assertTrue(result)
    }

    @Test
    fun `should return false when call isFileAuth if code is AUTH_EXPIRED`() {
        //given
        val service = mockk<KDocsFileDriveService>()
        val baseResponse = mockk<BaseResponse<Any>>()
        every { baseResponse.code } returns AUTH_EXPIRED
        coEvery { service.getPermissionFile(any()) } returns baseResponse
        val repository = KDocsFileDriveRepository(service)
        //when
        val result = runBlocking {
            repository.isFileAuth("")
        }
        //then
        Assert.assertTrue(result.not())
    }

    companion object {
        private const val FILE_ID = "300000000GKvJqeggQNng"
        private const val FOLDER_ID = "300000000GKvJqeggQ"
        private const val TITLE = "铁松离合"
        private const val LIST_TYPE = "folder"
        private const val AUTH_CODE = "04MRUFDSOAKRSPHJ4J-QLW"
        private const val AUTH_STATE = "ggxUDizrENKNC6HAoXm0rE0EPyROkcdm"
        private const val ERROR_MSG = "invalid params"
        private const val K_DOCS_TOKEN = ".eyJleHAiOjE3MDgzMjU1NzAwMzcsImlkIjoiMjA4NzU0ODM1N"
        private const val AUTHORIZATION_URL =
            "https://owork-api-cn-test.wanyol.com/owork-server/mobile/mini-program/tencent-docs/v1/auth/build"

        private val testCaseTimeout = 30.seconds
    }
}