package com.oplus.filemanager.remotedevice.download

import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo
import com.filemanager.common.constants.RemoteDeviceConstants
import com.oplus.filemanager.interfaze.remotedevice.DownloadRemoteFileCallback
import com.oplus.filemanager.interfaze.remotedevice.OperateCallback
import com.oplus.filemanager.remotedevice.RemoteDeviceManager
import com.oplus.filemanager.remotedevice.ResultConstant
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue
import java.lang.reflect.Field

/**
 * DownloadFileCallbackWrapper 的单元测试类
 * 用于测试 DownloadFileCallbackWrapper 类的各种功能和行为
 */
class DownloadFileCallbackWrapperTest {

    // 使用 MockK 框架模拟 DownloadRemoteFileCallback 接口
    @MockK
    private lateinit var mockCallback: DownloadRemoteFileCallback

    // 被测试的 DownloadFileCallbackWrapper 实例
    private lateinit var wrapper: DownloadFileCallbackWrapper

    /**
     * 通过反射获取对象的私有字段值
     * @param fieldName 字段名称
     * @return 字段值
     */
    private fun <T> Any.getPrivateField(fieldName: String): T {
        val field = this::class.java.getDeclaredField(fieldName)
        field.isAccessible = true
        return field.get(this) as T
    }

    /**
     * 通过反射设置对象的私有字段值
     * @param fieldName 字段名称
     * @param value 要设置的值
     */
    private fun <T> Any.setPrivateField(fieldName: String, value: T) {
        val field = this::class.java.getDeclaredField(fieldName)
        field.isAccessible = true
        field.set(this, value)
    }

    /**
     * 测试前的初始化方法
     * 1. 初始化 MockK 注解
     * 2. 模拟 RemoteDeviceManager 对象
     * 3. 清除所有模拟对象的状态
     * 4. 设置 mockCallback 的默认行为
     * 5. 创建被测试的 wrapper 实例并初始化其状态
     */
    @Before
    fun setup() {
        MockKAnnotations.init(this)
        mockkObject(RemoteDeviceManager)
        clearAllMocks()
        every { mockCallback.onResult(any(), any()) } just Runs
        every { mockCallback.onProgress(any()) } just Runs
        every { mockCallback.onProgressEachFile(any(), any(), any()) } just Runs
        wrapper = DownloadFileCallbackWrapper(mockCallback)
        wrapper.setPrivateField("isConnected", true)
        wrapper.setPrivateField("lastProgress", -1)
        wrapper.getPrivateField<MutableMap<String?, Int>>("downloadFiles").clear()
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟对象的模拟状态
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试 onProgress 方法在设备未连接时的行为
     * 预期: 应触发断开通知并清空回调
     */
    @Test
    fun `OnProgressTests-当设备未连接时调用onProgress应触发断开通知`() {
        wrapper.setPrivateField("isConnected", false)

        wrapper.onProgress(50)

        verify { mockCallback.onResult(OperateCallback.OPERATE_BEGIN_DOWNLOAD, ResultConstant.RESULT_CODE_FAIL_NORMAL) }
        assertNull(wrapper.callback)
    }

    /**
     * 测试 onProgress 方法在进度增加时的行为
     * 预期: 应回调进度更新并更新 lastProgress
     */
    @Test
    fun `OnProgressTests-当进度增加时应回调进度更新`() {
        wrapper.setPrivateField("lastProgress", 30)
        wrapper.onProgress(50)

        verify { mockCallback.onProgress(50) }
        assertEquals(50, wrapper.getPrivateField<Int>("lastProgress"))
    }

    /**
     * 测试 onProgress 方法在进度未增加时的行为
     * 预期: 不应回调进度更新
     */
    @Test
    fun `OnProgressTests-当进度未增加时不应回调`() {
        wrapper.setPrivateField("lastProgress", 50)
        wrapper.onProgress(40)

        verify(exactly = 0) { mockCallback.onProgress(any()) }
    }

    /**
     * 测试 onProgressEachFile 方法在文件已下载完成时的行为
     * 预期: 应跳过处理不回调
     */
    @Test
    fun `OnProgressEachFileTests-当文件已下载完成时应跳过处理`() {
        val path = "test_path"
        wrapper.getPrivateField<MutableMap<String?, Int>>("downloadFiles")[path] = DownloadRemoteFileCallback.DOWNLOAD_COMPLETED

        wrapper.onProgressEachFile(path, 100L, 100L)

        verify(exactly = 0) { mockCallback.onProgressEachFile(any(), any(), any()) }
    }

    /**
     * 测试 onProgressEachFile 方法在文件下载失败时的行为
     * 预期: 应跳过处理不回调
     */
    @Test
    fun `OnProgressEachFileTests-当文件下载失败时应跳过处理`() {
        val path = "test_path"
        wrapper.getPrivateField<MutableMap<String?, Int>>("downloadFiles")[path] = DownloadRemoteFileCallback.DOWNLOAD_FAIL

        wrapper.onProgressEachFile(path, 100L, 100L)

        verify(exactly = 0) { mockCallback.onProgressEachFile(any(), any(), any()) }
    }

    /**
     * 测试 onProgressEachFile 方法在文件传输完成时的行为
     * 预期: 应标记为完成状态并回调
     */
    @Test
    fun `OnProgressEachFileTests-当文件传输完成时应标记为完成状态`() {
        val path = "test_path"
        wrapper.onProgressEachFile(path, 100L, 100L)

        val downloadFiles = wrapper.getPrivateField<MutableMap<String?, Int>>("downloadFiles")
        assertEquals(DownloadRemoteFileCallback.DOWNLOAD_COMPLETED, downloadFiles[path])
        verify { mockCallback.onProgressEachFile(path, DownloadRemoteFileCallback.DOWNLOAD_COMPLETED, 100.0f) }
    }

    /**
     * 测试 onProgressEachFile 方法在设备连接时的行为
     * 预期: 应标记为下载中状态并回调
     */
    @Test
    fun `OnProgressEachFileTests-当设备连接时应标记为下载中状态`() {
        val path = "test_path"
        wrapper.setPrivateField("isConnected", true)

        wrapper.onProgressEachFile(path, 50L, 100L)

        val downloadFiles = wrapper.getPrivateField<MutableMap<String?, Int>>("downloadFiles")
        assertEquals(DownloadRemoteFileCallback.DOWNLOADING, downloadFiles[path])
        verify { mockCallback.onProgressEachFile(path, DownloadRemoteFileCallback.DOWNLOADING, 50.0f) }
    }

    /**
     * 测试 onProgressEachFile 方法在设备断开时的行为
     * 预期: 应标记为失败状态并回调
     */
    @Test
    fun `OnProgressEachFileTests-当设备断开时应标记为失败状态`() {
        val path = "test_path"
        wrapper.setPrivateField("isConnected", false)

        wrapper.onProgressEachFile(path, 50L, 100L)

        val downloadFiles = wrapper.getPrivateField<MutableMap<String?, Int>>("downloadFiles")
        assertEquals(DownloadRemoteFileCallback.DOWNLOAD_FAIL, downloadFiles[path])
        verify { mockCallback.onProgressEachFile(path, DownloadRemoteFileCallback.DOWNLOAD_FAIL, 50.0f) }
    }

    /**
     * 测试 onProgressEachFile 方法处理 null 路径时的行为
     * 预期: 应将 null 路径转换为空字符串处理
     */
    @Test
    fun `OnProgressEachFileTests-当路径为null时应转换为空字符串处理`() {
        wrapper.onProgressEachFile(null, 50L, 100L)

        assertTrue(wrapper.getPrivateField<MutableMap<String?, Int>>("downloadFiles").containsKey(""))
    }

    /**
     * 测试 onResult 方法的行为
     * 预期: 应正确转发结果给回调
     */
    @Test
    fun `OnResultTests-onResult应正确转发结果`() {
        wrapper.onResult(ResultConstant.RESULT_CODE_SUCCESS)

        verify { mockCallback.onResult(OperateCallback.OPERATE_BEGIN_DOWNLOAD, ResultConstant.RESULT_CODE_SUCCESS) }
    }

    /**
     * 测试 onDeviceUpdate 方法在设备断开时的行为
     * 预期: 应更新状态并触发断开处理
     */
    @Test
    fun `OnDeviceUpdateTests-当设备状态更新为断开时应触发断开处理`() {
        wrapper.setPrivateField("deviceId", "device123")
        val deviceInfo = RemoteDeviceInfo(deviceId = "device123", deviceStatus = RemoteDeviceConstants.CONNECTED - 1)

        wrapper.onDeviceUpdate(listOf(deviceInfo))

        assertFalse(wrapper.getPrivateField<Boolean>("isConnected"))
        verify { mockCallback.onResult(OperateCallback.OPERATE_BEGIN_DOWNLOAD, ResultConstant.RESULT_CODE_FAIL_NORMAL) }
    }

    /**
     * 测试 onDeviceUpdate 方法在设备连接时的行为
     * 预期: 应更新连接状态
     */
    @Test
    fun `OnDeviceUpdateTests-当设备状态更新为连接时应更新状态`() {
        wrapper.setPrivateField("deviceId", "device123")
        val deviceInfo = RemoteDeviceInfo(deviceId = "device123", deviceStatus = RemoteDeviceConstants.CONNECTED)

        wrapper.onDeviceUpdate(listOf(deviceInfo))

        assertTrue(wrapper.getPrivateField<Boolean>("isConnected"))
    }

    /**
     * 测试 onDeviceUpdate 方法在不相关设备更新时的行为
     * 预期: 应忽略不相关的设备更新
     */
    @Test
    fun `OnDeviceUpdateTests-当设备列表中不包含当前设备时应忽略`() {
        wrapper.setPrivateField("deviceId", "device123")
        val originalStatus = wrapper.getPrivateField<Boolean>("isConnected")
        val deviceInfo = RemoteDeviceInfo(deviceId = "other_device", deviceStatus = RemoteDeviceConstants.CONNECTED)

        wrapper.onDeviceUpdate(listOf(deviceInfo))

        assertEquals(originalStatus, wrapper.getPrivateField<Boolean>("isConnected"))
    }

    /**
     * 测试 registerDeviceStatusListener 方法的行为
     * 预期: 应正确设置设备ID和添加监听器
     */
    @Test
    fun `registerDeviceStatusListener应正确设置设备ID和监听器`() {
        wrapper.registerDeviceStatusListener("device123")

        assertEquals("device123", wrapper.getPrivateField<String>("deviceId"))
        verify { RemoteDeviceManager.addDeviceStatusListener(wrapper) }
    }
}