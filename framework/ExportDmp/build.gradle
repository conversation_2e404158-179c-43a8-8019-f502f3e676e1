plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace 'com.oplus.filemanager.exportdmp'
}

dependencies {

    implementation project(':Common')
    implementation libs.koin.android

    //dmp搜索中台SDK的依赖
    implementation libs.oplus.dmp.sdk.export
}