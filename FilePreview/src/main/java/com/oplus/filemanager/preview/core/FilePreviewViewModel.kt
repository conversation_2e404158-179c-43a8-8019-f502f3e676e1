/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - FilePreviewViewModel.kt
 * Description:
 *     The common view model for preview file.
 *
 * Version: 1.0
 * Date: 2024-09-05
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-09-05   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.core

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.OnLoaderListener
import kotlinx.coroutines.launch

internal open class FilePreviewViewModel :
    SelectionViewModel<BaseFileBean, BaseUiModel<BaseFileBean>>() {

    private val stateModel = BaseStateModel(MutableLiveData<Int>(KtConstants.LIST_NORMAL_MODE))
    private val previewData by lazy { MutableLiveData<BaseFileBean>() }

    fun putPreviewFile(previewFile: BaseFileBean) {
        viewModelScope.launch {
            stateModel.listModel.value = KtConstants.LIST_SELECTED_MODE
            stateModel.initState = true
            previewData.value = previewFile
            uiState.value = BaseUiModel(
                fileList = listOf(previewFile),
                stateModel = stateModel,
                selectedList = arrayListOf(0),
                keyMap = hashMapOf(0 to previewFile)
            )
            dataLoadState.postValue(OnLoaderListener.STATE_DONE)
            loadData()
        }
    }

    fun pickPreviewFile(): BaseFileBean? = previewData.value

    fun loadPreviewFile(lifecycleOwner: LifecycleOwner, onLoaded: Observer<BaseFileBean?>) {
        lifecycleOwner.lifecycleScope.launch {
            loadAndObserve(lifecycleOwner, onLoaded)
        }
    }

    private fun loadAndObserve(lifecycleOwner: LifecycleOwner, onLoaded: Observer<BaseFileBean?>) {
        previewData.observe(lifecycleOwner, onLoaded)
    }

    fun releaseLoader(onLoaded: Observer<BaseFileBean?>) {
        previewData.removeObserver(onLoaded)
    }

    /**
     * 有文件时返回RealFileSize为2，防止诸如删除等操作执行处理时，将当前状态识别为处理全部文件
     */
    override fun getRealFileSize(): Int = if (previewData.value == null) 0 else 2

    override fun loadData() {
        // Default do nothing
    }

    override fun selectItems(keys: List<Int>): Boolean {
        // Default do nothing since only one file data and always be selected
        return true
    }

    override fun deselectItems(keys: List<Int>): Boolean {
        // Default do nothing since only one file data and always be selected
        return true
    }

    override fun changeListMode(listMode: Int) {
        // Default do nothing since only one file data and always be selected
        return
    }

    override fun enterSelectionMode(key: Int): Boolean {
        // Default do nothing since only one file data and always be selected
        return true
    }
}