/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - ItemKeyProvider.kt
 ** Description: Provides selection library access to stable selection keys identifying items
 ** Version: 1.0
 ** Date : 2020/05/15
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  Ji<PERSON>ei.<PERSON>@Apps.FileManager      2020/05/15    1.0     create
 ****************************************************************/
package com.oplus.dropdrag.recycleview

import androidx.annotation.Keep

@Keep
abstract class ItemKeyProvider<K> protected constructor() {
    /**
     * @return The selection key at the given adapter position, or null.
     */
    abstract fun getKey(position: Int): K?

    /**
     * @return the position corresponding to the selection key, or RecyclerView.NO_POSITION.
     */
    abstract fun getPosition(key: K): Int
}