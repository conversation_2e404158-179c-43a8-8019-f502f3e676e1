/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - OnDragInitiatedListener.kt
 * Description: On DragInitiated Listener
 * Version: 1.0
 * Date : 2020/05/15
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/05/15    1.0     create
 ***********************************************************************/
package com.oplus.dropdrag

import android.view.MotionEvent
import androidx.annotation.Keep

@Keep
interface OnDragStartListener {
    /**
     * callback for drag start
     * @param e the event associated with the click.
     * @return true if the event was handled.
     */
    fun onDragStart(e: MotionEvent): <PERSON><PERSON><PERSON>
}