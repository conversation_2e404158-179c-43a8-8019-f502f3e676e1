/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemoteFileBean
 * * Description : RemoteFileBean
 * * Version     : 1.0
 * * Date        : 2024/12/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download.bean

import android.os.Parcelable
import com.coui.appcompat.progressbar.COUILoadProgress
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.wrapper.PathFileWrapper
import kotlinx.parcelize.Parcelize
import java.io.File

@Parcelize
class DownloadRemoteFileBean(val originalPath: String, val size: Long) : PathFileWrapper(originalPath), Parcelable {

    companion object {
        /**
         * 下载状态
         */
        const val DOWNLOAD_DEFAULT = COUILoadProgress.DEFAULT_UP_OR_DOWN

        /**
         * 下载中
         */
        const val DOWNLOADING = COUILoadProgress.UPING_OR_DOWNING

        /**
         * 下载暂停等待中
         */
        const val DOWNLOAD_WAIT = COUILoadProgress.UP_OR_DOWN_WAIT

        /**
         * 下载失败
         */
        const val DOWNLOAD_FAIL = COUILoadProgress.UP_OR_DOWN_FAIL

        /**
         * 下载的最大进度
         */
        const val DOWNLOAD_MAX_PROGRESS = 100

        const val PROGRESS_100 = 100.0f
    }

    /**
     * 下载状态
     */
    var downloadStatus: Int = DOWNLOAD_DEFAULT

    /**
     * 下载进度
     */
    var downloadProgress: Int = 0

    init {
        this.mSize = size
        this.mMimeType = MimeTypeHelper.getMimeTypeFromPath(this.mDisplayName)
        mLocalType =  MimeTypeHelper.getTypeFromExtension(FileTypeUtils.getExtension(mDisplayName))
            ?: MimeTypeHelper.UNKNOWN_TYPE
    }

    constructor(data: Pair<String, Long>) : this(data.first, data.second)

    /**
     * 是否下载完成
     */
    fun isDownloadComplete(): Boolean {
        return (downloadStatus == DOWNLOADING) && (downloadProgress == DOWNLOAD_MAX_PROGRESS)
    }

    fun downloadComplete() {
        downloadStatus = DOWNLOADING
        downloadProgress = DOWNLOAD_MAX_PROGRESS
    }

    fun updateLocalPath(saveDir: String) {
        val localPath = File(saveDir, this.mDisplayName ?: "")
        mData = localPath.absolutePath
    }

    override fun toString(): String {
        return "DownloadRemoteFileBean(path=$mData downloadStatus=$downloadStatus, downloadProgress=$downloadProgress)"
    }
}