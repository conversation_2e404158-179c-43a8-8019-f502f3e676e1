/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: CompressHelper.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/3/10
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.compress

import androidx.annotation.WorkerThread
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.cpu.PerformanceManager
import com.filemanager.common.utils.Log
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

abstract class CompressHelper {
    protected lateinit var mSourceRootPath: String
    private var mCancelled = AtomicBoolean(false)
    private var mCompressedSize: Long = 0
    private var mCompressListener: OnCompressListener? = null

    companion object {
        private const val TAG = "CompressHelper"

        @JvmStatic
        protected val COPY_BUF_SIZE = 524288
    }

    @WorkerThread
    fun compress(sourceFiles: List<out BaseFileBean>, destFile: BaseFileBean, listener: OnCompressListener?) {
        if (sourceFiles.isNullOrEmpty() || destFile.mData.isNullOrEmpty()) {
            listener?.onCompressDone(false)
            return
        }
        mCompressListener = listener
        mCompressedSize = 0

        run outside@{
            if (sourceFiles.size == 1) {
                if (sourceFiles[0].mData.isNullOrEmpty().not()) {
                    mSourceRootPath = File(sourceFiles[0].mData).parentFile.absolutePath
                    Log.d(TAG, "Get the root path is: $mSourceRootPath")
                    if (mSourceRootPath.isNullOrEmpty()) {
                        return
                    } else if (mSourceRootPath.endsWith(File.separator).not()) {
                        mSourceRootPath = mSourceRootPath.plus(File.separator)
                    }
                    return@outside
                }
            } else {
                mSourceRootPath = getCommonPath(sourceFiles)
                Log.d(TAG, "Get the common path is: $mSourceRootPath")
            }
        }
        Log.d(TAG, "source rootPath=$mSourceRootPath")
        initImproveCpu()
        notifyProgress(0) // means start to work
        val result = internalCompress(sourceFiles, destFile)
        releaseImproveCpu()
        if (result) {
            mCompressListener?.onCompressDone(true)
        } else {
            if (isCancelled().not()) {
                mCompressListener?.onCompressDone(false)
            }
        }
        recycle()
    }

    private fun getCommonPath(paths: List<BaseFileBean>): String {
        if (paths.isEmpty()) return ""
        // 分割路径为列表，取第一个路径的目录部分作为基准
        val firstPathParts = paths.first().mData?.split("/") ?: emptyList()
        var commonParts = firstPathParts.toMutableList()

        for (path in paths) {
            val parts = path.mData?.split("/") ?: emptyList()
            // 找到最小公共前缀
            for (i in commonParts.indices) {
                if (i >= parts.size || commonParts[i] != parts[i]) {
                    commonParts = commonParts.subList(0, i)
                    break
                }
            }
        }
        // 返回并集路径
        return commonParts.joinToString("/") + "/"
    }

    fun cancelCompress() {
        if (isCancelled().not()) {
            mCancelled.set(true)
            mCompressListener?.onCompressCancelled()
        }
    }

    fun isCancelled() = mCancelled.get()

    protected abstract fun internalCompress(sourceFiles: List<out BaseFileBean>, destFile: BaseFileBean): Boolean

    protected fun getEntryRelativeName(f: File): String? {
        var entryName: String? = null
        f.absolutePath?.let {
            entryName = if (it.indexOf(mSourceRootPath) != -1) {
                if (it == mSourceRootPath) {
                    ""
                } else {
                    it.replaceFirst(mSourceRootPath, "")
                }
            } else {
                f.name
            }
            if (f.isDirectory && (entryName?.startsWith("/") == false)) {
                entryName += "/"
            }
        }
        return entryName
    }

    private fun initImproveCpu() {
        PerformanceManager.setSceneAction(PerformanceManager.UN_OR_COMPRESS_ACTION)
    }

    private fun releaseImproveCpu() {
        PerformanceManager.release()
    }

    protected fun notifyProgress(newSize: Long) {
        mCompressedSize += newSize
        mCompressListener?.onProgressUpdate(mCompressedSize)
    }

    private fun recycle() {
        mCompressListener = null
    }

    interface OnCompressListener {
        fun onProgressUpdate(currentSize: Long) {}
        fun onCompressDone(result: Boolean) {}
        fun onCompressCancelled() {}
    }
}