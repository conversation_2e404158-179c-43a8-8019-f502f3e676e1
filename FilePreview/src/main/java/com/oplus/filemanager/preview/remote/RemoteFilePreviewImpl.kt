/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemoteFilePreviewImpl.kt
 ** Description: RemoteFilePreviewImpl
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.preview.remote

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.FitCenter
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.EmojiUtils
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.category.remotedevice.CategoryRemoteDeviceApi
import com.oplus.filemanager.interfaze.remotedevice.IRemotePicListener
import com.oplus.filemanager.interfaze.remotedevice.IRemoteThumbnail
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.utils.ArchiveIconHelper
import com.oplus.filemanager.preview.utils.BorderedRoundedCornersTrans
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import com.oplus.filemanager.preview.utils.GlideExtend


internal class RemoteFilePreviewImpl(
    private val fragment: Fragment,
    private val lifecycleOwner: LifecycleOwner,
    private val previewModel: RemotePreviewViewModel
) : IRemoteFilePreview {

    private companion object {
        private const val TAG = "RemoteFilePreviewImpl"
        private const val SUCCESS = 1000
    }

    private val typeChecker = FilePreviewTypeCheckHelper(fragment.requireContext())
    private var defImagePlaceholder = KtThumbnailHelper.getClassifyResId(MimeTypeHelper.IMAGE_TYPE)
    private val onImageLoaded = Observer<BaseFileBean?> { displayOnContainer(it) }

    private var containerManager: IRemotePreviewContainerManager? = null

    override fun attachToContainer(containerManager: IRemotePreviewContainerManager) {
        this.containerManager = containerManager
        defImagePlaceholder = previewModel.pickPreviewFile()?.getPlaceholderRes()
            ?: KtThumbnailHelper.getClassifyResId(MimeTypeHelper.UNKNOWN_TYPE)
        containerManager.imageView.setImageResource(defImagePlaceholder)
        containerManager.defaultContainer.setFileIcon(defImagePlaceholder)
        containerManager.download.setOnClickListener { displayDownload(previewModel.pickPreviewFile()) }
        setDownloadEnable(containerManager)
        containerManager.rootView.setOnApplyWindowInsetsListener(rootWindowInsetsListener)
        containerManager.showAsLoading()
        previewModel.loadPreviewFile(lifecycleOwner, onImageLoaded)
    }

    private fun setDownloadEnable(containerManager: IRemotePreviewContainerManager) {
        val bean = previewModel.pickPreviewFile()
        bean?.let {
            val containIllegalChar = EmojiUtils.containsIllegalCharFileName(it.mDisplayName)
            containerManager.download.isEnabled = !containIllegalChar
        }
    }

    @SuppressLint("NewApi")
    private val rootWindowInsetsListener = View.OnApplyWindowInsetsListener { _, insets ->
        val bottom = insets.getInsets(WindowInsetsCompat.Type.systemBars()).bottom
        val paddingBottom = if (StatusBarUtil.checkIsGestureNavMode()) {
            0
        } else {
            bottom
        }
        containerManager?.download.let {
            val layoutParams = it?.layoutParams as ConstraintLayout.LayoutParams
            var marginBottom =
                it.context.resources.getDimension(R.dimen.preview_remote_download_margin_bottom)
            if (paddingBottom > 0) {
                marginBottom =
                    it.context.resources.getDimension(R.dimen.preview_remote_download_margin_bottom_navigation)
            }
            layoutParams.bottomMargin = marginBottom.toInt()
            it.layoutParams = layoutParams
        }
        insets
    }

    override fun release() {
        previewModel.releaseLoader(onImageLoaded)
        containerManager?.imageView?.let {
            GlideExtend.with(it.context)?.clear(it)
        }
        containerManager = null
    }

    private fun setDividerVisible(isVisible: Boolean?) {
        Log.i(TAG, "isVisible$isVisible")
        containerManager?.downloadDivider?.visibility =
            if (isVisible == true) View.VISIBLE else View.INVISIBLE
    }

    private fun displayOnContainer(imageBean: BaseFileBean?) {
        Log.i(TAG, "displayOnContainer imageBean: $imageBean")
        val imageView = containerManager?.imageView ?: return
        if (imageBean == null) {
            containerManager?.showAsFileContainer(true)
            return
        } else {
            if (imageBean is RemoteFileBean) {
                containerManager?.imageView?.post {
                    setDividerVisible(containerManager?.scroll?.isContentScrollable())
                }
                containerManager?.timeInfo?.text =
                    Utils.getDateAndTimeFormatLocal(imageView.context, imageBean.mDateModified)
                containerManager?.sizeInfo?.text = previewModel.updateSizeFormat(imageBean.mSize)
                containerManager?.locationInfo?.text = imageBean.originalPath
                containerManager?.remoteTitle?.text = imageBean.mDisplayName
                containerManager?.remoteTitle?.setTextViewStyle()
                val type = imageBean.getType()
                if (type == MimeTypeHelper.VIDEO_TYPE || type == MimeTypeHelper.IMAGE_TYPE) {
                    val context = imageView.context
                    val devicesId = previewModel.getDeviceId()
                    val picUri = imageBean.remotePicUri
                    //后续可能有时效性问题
                    if (picUri != null) {
                        loadUri(context, picUri, imageView)
                        return
                    }
                    val picFile = previewModel.createFile(context)
                    val newPicUri = previewModel.createUri(context, picFile)
                    val remoteWidth: Int =
                        context.resources.getDimensionPixelSize(com.oplus.filemanager.preview.R.dimen.preview_remote_max_width)
                    val remoteHeight =
                        context.resources.getDimensionPixelSize(com.oplus.filemanager.preview.R.dimen.preview_remote_max_height)
                    val packageName = context.packageName

                    Log.e(TAG, "displayOnContainer")
                    context.grantUriPermission(
                        "com.oplus.remotecontrol", newPicUri, Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                    )
                    Injector.injectFactory<IRemoteThumbnail>()?.getPicThumbnails(devicesId,
                        imageBean.originalPath,
                        newPicUri,
                        packageName,
                        remoteWidth,
                        remoteHeight,
                        object : IRemotePicListener {
                            override fun onResult(code: Int, picUri: Uri) {
                                Log.i(TAG, "code: $code")
                                context.revokeUriPermission(
                                    "com.oplus.remotecontrol",
                                    newPicUri,
                                    Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                                )
                                if (code == SUCCESS) {
                                    imageBean.remotePicUri = picUri
                                } else {
                                    containerManager?.showAsFileContainer(true)
                                    return
                                }
                                loadUri(context, picUri, imageView)
                            }
                        })
                } else {
                    containerManager?.showAsFileContainer(true)
                }
            }
        }
    }

    private fun displayDownload(remoteFileBean: BaseFileBean?) {
        if (remoteFileBean is RemoteFileBean) {
            val downloadPaths = ArrayList<Pair<String, Long>>()
            downloadPaths.add(Pair(remoteFileBean.originalPath, remoteFileBean.mSize))
            CategoryRemoteDeviceApi.jumpDownloadActivity(
                fragment.requireActivity(),
                previewModel.getDeviceId(),
                downloadPaths,
                MessageConstant.MSG_OPEN_REMOTE_FILE
            )
        }
    }

    private fun BaseFileBean.getPlaceholderRes(isUnknown: Boolean = false): Int {
        if (isUnknown) {
            return KtThumbnailHelper.getClassifyResId(MimeTypeHelper.UNKNOWN_TYPE)
        }
        val type = typeChecker.getMediaType(this@getPlaceholderRes)
        if (type == MimeTypeHelper.COMPRESSED_TYPE) {
            return ArchiveIconHelper.getArchiveIconId(this@getPlaceholderRes)
        }
        return KtThumbnailHelper.getClassifyResId(type)
    }

    private fun BaseFileBean.getType(isUnknown: Boolean = false): Int {
        return typeChecker.getMediaType(this@getType)
    }

    private fun loadUri(context: Context, picUri: Uri, imageView: ImageView) {
        Log.i(TAG, "displayOnContainer: picUri: $picUri")
        val roundCorners = BorderedRoundedCornersTrans(imageView.context)
        Glide.with(context).asBitmap().load(picUri).apply(RequestOptions())
            .transform(FitCenter(), roundCorners).placeholder(defImagePlaceholder)
            .addListener(object : RequestListener<Bitmap> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Bitmap>,
                    isFirstResource: Boolean
                ): Boolean {
                    Log.e(TAG, "displayOnContainer: onLoadFailed")
                    containerManager?.showAsFileContainer(true)
                    return false
                }

                override fun onResourceReady(
                    resource: Bitmap,
                    model: Any,
                    target: Target<Bitmap>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    Log.d(TAG, "displayOnContainer: onResourceReady")
                    containerManager?.showAsFileContainer(false)
                    val height = resource.height
                    val width = resource.width
                    setViewType(height, width)
                    return false
                }
            }).into(imageView)
    }

    fun setViewType(height: Int, width: Int) {
        if (MimeTypeHelper.VIDEO_TYPE == previewModel.pickPreviewFile()
                ?.getType() && fragment.context != null
        ) {
            val view: View = LayoutInflater.from(fragment.context)
                .inflate(R.layout.widget_video_type, null)
            val textView: AppCompatTextView =
                view.findViewById(R.id.videoType)
            textView.text = getExtForBean(previewModel.pickPreviewFile()) ?: ""
            val remoteContainer = containerManager?.remoteContainer
            val layoutParams = ConstraintLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT
            )
            layoutParams.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
            layoutParams.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
            if (remoteContainer != null) {
                val marginEnd =
                    fragment.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_8dp)
                val marginBottom =
                    fragment.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_8dp)
                layoutParams.marginEnd =
                    (remoteContainer.width - width) / 2 + view.width + marginEnd
                layoutParams.bottomMargin =
                    (remoteContainer.height - height) / 2 + view.height + marginBottom
                view.layoutParams = layoutParams
                remoteContainer.addView(view)
            }
        }
    }

    private fun getExtForBean(file: BaseFileBean?): String? {
        if (file == null) {
            return null
        }
        val remoteBean = file as RemoteFileBean
        val name =
            if (file.shouldShowAlternativeName()) file.alternativeName else remoteBean.mDisplayName
        return FileTypeUtils.getExtension(name)
    }
}
