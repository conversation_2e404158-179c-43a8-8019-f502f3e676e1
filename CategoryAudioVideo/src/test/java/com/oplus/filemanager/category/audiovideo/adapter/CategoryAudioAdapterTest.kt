package com.oplus.filemanager.category.audiovideo.adapter

import android.app.Activity
import android.content.Context
import android.os.Looper
import android.view.View
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.view.FootViewManager
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.dropdrag.SelectionTracker
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.category.audiovideo.R
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.dragselection.DragUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * CategoryAudioAdapter的单元测试类
 * 用于测试音频分类适配器的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])  // 将SDK版本从28改为29以解决错误
class CategoryAudioAdapterTest {

    // 测试用成员变量
    private lateinit var adapter: CategoryAudioAdapter
    private lateinit var mockContext: Context
    private lateinit var mockActivity: Activity
    private lateinit var mockLifecycle: Lifecycle
    private lateinit var mockMediaFileWrapper: MediaFileWrapper
    private lateinit var mockFootViewManager: FootViewManager<MediaFileWrapper>
    private val testLatch = CountDownLatch(1)

    /**
     * 测试前的初始化方法
     * 创建所有mock对象和测试适配器实例
     */
    @Before
    fun setUp() {
        // 创建mock对象
        mockContext = mockk(relaxed = true)
        mockActivity = mockk(relaxed = true)
        mockLifecycle = mockk(relaxed = true) {
            every { addObserver(any()) } answers { }
        }
        mockMediaFileWrapper = mockk(relaxed = true)
        mockFootViewManager = mockk(relaxed = true)

        // mock静态对象AdvertManager
        mockkObject(AdvertManager)
        every { AdvertManager.isAdEnabled() } returns false
        every { AdvertManager.getAdViewCount(any()) } returns 0

        // mock静态对象MyApplication
        mockkObject(MyApplication)
        every { MyApplication.sAppContext } returns mockContext

        // 创建测试适配器实例
        adapter = CategoryAudioAdapter(mockContext, CategoryHelper.CATEGORY_AUDIO, mockLifecycle)
    }

    /**
     * 测试后的清理方法
     * 解除所有mock对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试构造函数
     * 验证适配器实例是否创建成功
     * 验证生命周期观察者是否被添加
     */
    @Test
    fun testConstructor() {
        assertNotNull(adapter)
        verify { mockLifecycle.addObserver(any()) }
    }

    /**
     * 测试setData方法
     * 验证数据是否正确设置到适配器中
     */
    @Test
    fun testSetData() {
        val data = arrayListOf(mockMediaFileWrapper)
        val selectionArray = arrayListOf(0)

        adapter.setData(data, selectionArray)

        assertEquals(data, adapter.mFiles)
        assertEquals(selectionArray, adapter.mSelectionArray)
    }

    /**
     * 测试getItemId方法
     * 验证是否能正确返回文件ID
     * 验证无效位置是否返回NO_LONG_ITEM_ID
     */
    @Test
    fun testGetItemId() {
        val file = mockk<MediaFileWrapper>()
        every { file.mId } returns 123
        adapter.mFiles = arrayListOf(file)

        assertEquals(123L, adapter.getItemId(0))
        assertEquals(SelectionTracker.NO_LONG_ITEM_ID, adapter.getItemId(-1))
    }

    /**
     * 测试onDestroy方法
     * 验证生命周期结束时是否能正确执行清理操作
     */
    @Test
    fun testOnDestroy() {
        adapter.onDestroy()
    }
}