/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : FileDriveStateUtilsTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/29 17:44
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/29       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.PreferencesUtils
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils.DOCS_REFRESH_TIME
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils.KEY_KDOCS_AUTHORIZATION_CODE
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils.KEY_KDOCS_AUTHORIZATION_STATE
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils.KEY_KDOCS_AUTHORIZATION_STATUS
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils.KEY_TENCENT_DOCS_AUTHORIZATION_CODE
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils.KEY_TENCENT_DOCS_AUTHORIZATION_STATE
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils.KEY_TENCENT_DOCS_AUTHORIZATION_STATUS
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils.PREF_NAME_FILE_CLOUD_DOCS
import com.oplus.filemanager.room.model.DriveFileEntity.Companion.SOURCE_TYPE_TENCENT
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class FileDriveStateUtilsTest {

    @Before
    fun setup() {
        mockkStatic(PreferencesUtils::class)
    }

    @After
    fun teardown() {
        unmockkStatic(PreferencesUtils::class)
    }

    @Test
    fun isAuthed() {
        every {
            PreferencesUtils.getBoolean(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_STATUS)
        }.returns(true)
        val isAuthed = FileDriveStateUtils.isAuthed(CategoryHelper.CATEGORY_TENCENT_DOCS)
        Assert.assertTrue(isAuthed)

        every {
            PreferencesUtils.getBoolean(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_STATUS)
        }.returns(true)
        val isKDocsAuthed = FileDriveStateUtils.isAuthed(CategoryHelper.CATEGORY_K_DOCS)
        Assert.assertTrue(isKDocsAuthed)
    }

    @Test
    fun saveAuthStatus() {
        justRun {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_STATUS, any() as Boolean)
        }
        FileDriveStateUtils.saveAuthStatus(CategoryHelper.CATEGORY_TENCENT_DOCS, true)
        verify {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_STATUS, any() as Boolean)
        }
        verify(inverse = true) {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_STATUS, any() as Boolean)
        }
        justRun {
            PreferencesUtils.put(
                PREF_NAME_FILE_CLOUD_DOCS,
                KEY_KDOCS_AUTHORIZATION_STATE,
                any() as Boolean
            )
        }
        spyk<FileDriveStateUtils>()
        FileDriveStateUtils.saveAuthStatus(CategoryHelper.CATEGORY_K_DOCS, true)
        verify { FileDriveStateUtils.saveKDocsAuthorizationStatus(true) }
    }

    @Test
    fun saveTencentAuthorizationStatus() {
        justRun {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_STATUS, any() as Boolean)
        }
        FileDriveStateUtils.saveTencentAuthorizationStatus(true)
        verify {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_STATUS, true)
        }
    }

    @Test
    fun getTencentAuthorizationStatus() {
        every {
            PreferencesUtils.getBoolean(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_STATUS, false)
        }.returns(true)
        val isAuth = FileDriveStateUtils.getTencentAuthorizationStatus()
        Assert.assertTrue(isAuth)
    }

    @Test
    fun saveKDocsAuthorizationStatus() {
        justRun {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_STATUS, any() as Boolean)
        }
        FileDriveStateUtils.saveKDocsAuthorizationStatus(true)
        verify {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_STATUS, true)
        }
    }

    @Test
    fun getKDocsAuthorizationStatus() {
        every {
            PreferencesUtils.getBoolean(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_STATUS, false)
        }.returns(true)
        val isAuth = FileDriveStateUtils.getKDocsAuthorizationStatus()
        Assert.assertTrue(isAuth)
    }

    @Test
    fun saveAuthorizationValue() {
        justRun {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_CODE, any() as String)
        }
        justRun {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_STATE, any() as String)
        }
        FileDriveStateUtils.saveAuthorizationValue(CategoryHelper.CATEGORY_TENCENT_DOCS, TENCENT_CODE, TENCENT_STATE)
        verify { PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_CODE, TENCENT_CODE) }
        verify { PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_STATE, TENCENT_STATE) }
        justRun {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_CODE, any() as String)
        }
        justRun {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_STATE, any() as String)
        }
        FileDriveStateUtils.saveAuthorizationValue(CategoryHelper.CATEGORY_K_DOCS, KDOCS_CODE, KDOCS_STATE)
        verify { PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_CODE, KDOCS_CODE) }
        verify { PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_STATE, KDOCS_STATE) }
    }

    @Test
    fun saveTencentAuthorizationResult() {
        justRun {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_CODE, any() as String)
        }
        justRun {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_STATE, any() as String)
        }
        FileDriveStateUtils.saveAuthorizationValue(CategoryHelper.CATEGORY_TENCENT_DOCS, TENCENT_CODE, TENCENT_STATE)
        verify { PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_CODE, TENCENT_CODE) }
        verify { PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_STATE, TENCENT_STATE) }
    }

    @Test
    fun saveKDocsAuthorizationResult() {
        justRun {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_CODE, any() as String)
        }
        justRun {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_STATE, any() as String)
        }
        FileDriveStateUtils.saveAuthorizationValue(CategoryHelper.CATEGORY_K_DOCS, KDOCS_CODE, KDOCS_STATE)
        verify { PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_CODE, KDOCS_CODE) }
        verify { PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_STATE, KDOCS_STATE) }
    }

    @Test
    fun getAuthorizationStatus() {
        every { PreferencesUtils.getString(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_CODE) }.returns(KDOCS_STATE)
        every { PreferencesUtils.getString(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_STATE) }.returns(KDOCS_STATE)
        val kDocsStatus = FileDriveStateUtils.getAuthorizationStatus(CategoryHelper.CATEGORY_K_DOCS)
        Assert.assertEquals(KDOCS_CODE, kDocsStatus.first)
        Assert.assertEquals(KDOCS_STATE, kDocsStatus.second)
        every {
            PreferencesUtils.getString(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_CODE)
        }.returns(TENCENT_CODE)
        every {
            PreferencesUtils.getString(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_STATE)
        }.returns(TENCENT_STATE)
        val tencentStatus = FileDriveStateUtils.getAuthorizationStatus(CategoryHelper.CATEGORY_TENCENT_DOCS)
        Assert.assertEquals(TENCENT_CODE, tencentStatus.first)
        Assert.assertEquals(TENCENT_STATE, tencentStatus.second)
        val otherStatus = FileDriveStateUtils.getAuthorizationStatus(-1)
        Assert.assertEquals("", otherStatus.first)
        Assert.assertEquals("", otherStatus.second)
    }

    @Test
    fun clearAuthorizationStatus() {
        spyk<FileDriveStateUtils>()
        FileDriveStateUtils.clearAuthorizationStatus()
        verify { FileDriveStateUtils.clearTencentAuthorizationStatus() }
        verify { FileDriveStateUtils.clearKDocsAuthorizationStatus() }
    }

    @Test
    fun clearAuthorizationStatusByCategory() {
        spyk<FileDriveStateUtils>()
        FileDriveStateUtils.clearAuthorizationStatusByCategory(CategoryHelper.CATEGORY_TENCENT_DOCS)
        verify { FileDriveStateUtils.clearTencentAuthorizationStatus() }
        FileDriveStateUtils.clearAuthorizationStatusByCategory(CategoryHelper.CATEGORY_K_DOCS)
        verify { FileDriveStateUtils.clearKDocsAuthorizationStatus() }
    }

    @Test
    fun saveLastRefreshTime() {
        justRun {
            PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, "$SOURCE-$DOCS_REFRESH_TIME", DEFAULT_REFRESH_TIME)
        }
        FileDriveStateUtils.saveLastRefreshTime(SOURCE, DEFAULT_REFRESH_TIME)
        verify { PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, "$SOURCE-$DOCS_REFRESH_TIME", DEFAULT_REFRESH_TIME) }
    }

    @Test
    fun getLastRefreshTime() {
        every {
            PreferencesUtils.getLong(PREF_NAME_FILE_CLOUD_DOCS, "$SOURCE-$DOCS_REFRESH_TIME")
        }.returns(DEFAULT_REFRESH_TIME)
        val result = FileDriveStateUtils.getLastRefreshTime(SOURCE)
        Assert.assertEquals(DEFAULT_REFRESH_TIME, result)
        verify { PreferencesUtils.getLong(PREF_NAME_FILE_CLOUD_DOCS, "$SOURCE-$DOCS_REFRESH_TIME", any()) }
    }

    @Test
    fun testClearLastRefreshTime() {
        justRun { PreferencesUtils.remove(any(), key = any()) }
        FileDriveStateUtils.clearLastRefreshTime()
        verify(atLeast = 2) { PreferencesUtils.remove(any(), key = any()) }
    }

    @Test
    fun testClearLastRefreshTimeBySource() {
        justRun { PreferencesUtils.remove(any(), any()) }
        FileDriveStateUtils.clearLastRefreshTimeBySource(SOURCE_TYPE_TENCENT)
        verify { PreferencesUtils.remove(any(), any()) }
    }

    companion object {
        private const val TENCENT_CODE = ""
        private const val TENCENT_STATE = ""
        private const val KDOCS_CODE = ""
        private const val KDOCS_STATE = ""
        private const val SOURCE = "source"
        private const val DEFAULT_REFRESH_TIME = 100L
    }
}