/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date: 2020/8/29
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate

import android.media.MediaScannerConnection
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.utils.Log

/**
 * This class is used to notify media scanner in batches when the size of path list reach the special count,
 * avoid android.os.TransactionTooLargeException occurred when too much values send one time.<br/>
 * Bundle supports less than 200*1024 bytes parcel data.
 */
class NotifyMediaScannerBatchAction(private val mScanScene: String, private val mBatchCount: Int = BATCH_COUNT) {
    companion object {
        private const val TAG = "NotifyMediaScannerBatchAction"
        private const val BATCH_COUNT = 50
        private const val BATCH_TOTAL_SIZE = 20000
    }

    private val mPending: ArrayList<String> = ArrayList()
    private val allScanPathSet = HashSet<String>()
    private var mTotalSize: Int = 0
    var completeCallback: MediaScannerConnection.OnScanCompletedListener? = null

    fun add(pending: String) {
        Log.d(TAG, "add path: $pending")
        allScanPathSet.add(pending)
        if (!mPending.contains(pending)) {
            mPending.add(pending)
            mTotalSize += pending.length
            if ((mPending.size >= mBatchCount) || (mTotalSize >= BATCH_TOTAL_SIZE)) {
                Log.d(TAG, "ready to flush: ${mPending.size}, $mTotalSize")
                flush()
            }
        }
    }

    fun getAllScanPathSize(): Int {
        return allScanPathSet.size
    }

    fun flush() {
        Log.d(TAG, "start to flush: ${mPending.size}")
        if (mPending.isNotEmpty()) {
            MediaScannerCompat.sendMultiDirMediaScanner(mPending, mScanScene, completeCallback)
            mPending.clear()
            mTotalSize = 0
        }
    }
}