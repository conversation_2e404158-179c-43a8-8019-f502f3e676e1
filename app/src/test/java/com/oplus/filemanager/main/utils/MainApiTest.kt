/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: SuperLoaderTest.kt
 ** Description: SuperLoader unit test
 ** Version: 1.0
 ** Date: 2022/4/28
 ** Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.main.utils

import com.filemanager.common.utils.PermissionUtils
import com.oplus.filemanager.MainApi
import com.oplus.filemanager.main.ui.category.MainCategoryViewModel
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.Test

class MainApiTest {

    @Test
    fun `should execute init category items when load`() {
        mockkObject(MainApi)
        mockkObject(PermissionUtils)
        every { PermissionUtils.hasStoragePermission() } returns true
        val mainViewModel = mockk<MainCategoryViewModel>(relaxed = true)
        justRun { mainViewModel.loadMainCategoryItem(any()) }
        every { MainApi.loadMainCategoryItem(mainViewModel) } answers { callOriginal() }

        MainApi.loadMainCategoryItem(mainViewModel)

        verify { mainViewModel.loadMainCategoryItem(true) }
        unmockkObject(MainApi)
        unmockkObject(PermissionUtils)
    }
}