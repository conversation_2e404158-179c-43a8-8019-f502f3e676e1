/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AbsPreviewFragment.kt
 * Description:
 *     The common implementations for each file preview fragment.
 *
 * Version: 1.0
 * Date: 2024-09-19
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-09-19   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.core

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.annotation.CallSuper
import androidx.annotation.LayoutRes
import androidx.annotation.MenuRes
import androidx.annotation.StringRes
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.filepreview.IFilePreviewFragment
import com.oplus.filemanager.interfaze.filepreview.IFilePreviewFragment.OnClickFilePathListener
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.widget.PreviewFilePathItem
import com.oplus.filemanager.preview.widget.PreviewOperationsBar

internal abstract class AbsPreviewFragment<VM : FilePreviewViewModel>(
    @LayoutRes fragmentLayoutId: Int
) : Fragment(fragmentLayoutId), IFilePreviewFragment, IUiConfigChangeManager {

    /**
     * The log tag and view model tag for preview fragment
     */
    protected abstract val logTag: String

    /**
     * The view model class to obtain view model from [ViewModelProvider]
     */
    protected abstract val viewModelClass: Class<VM>

    /**
     * The operations bar widget id in fragment layout
     */
    protected abstract val operationsBarId: Int

    /**
     * The text res id of open button
     */
    @StringRes
    protected open val openButtonTextId: Int = com.filemanager.common.R.string.open

    /**
     * The menu res id of more menu
     */
    @MenuRes
    protected open val moreMenuResId: Int = R.menu.menu_preview_file_more_common

    protected val selectPathsCache = mutableMapOf<Int, List<String>?>()
    private val uiConfigChangeObservers = mutableSetOf<IUiConfigChangeObserver>()

    protected var previewModel: VM? = null
        private set(value) {
            field = value
            if (value != null) {
                lastPreviewModel = value
            }
        }

    protected var operationsBar: PreviewOperationsBar? = null
        private set

    protected var operationsController: PreviewOperationsController? = null
        private set

    protected var filePathItem: PreviewFilePathItem? = null
        private set

    private var toPreviewFileBean: BaseFileBean? = null
    private var clickFilePathListener: OnClickFilePathListener? = null
    private var operatorResultListener: IFileOperate.OperateResultListener? = null
    private var operateInterceptor: IFileOperate? = null
    private var lastPreviewModel: VM? = null

    final override fun setPreviewFile(context: Context, fileBean: BaseFileBean): Boolean {
        if (!isPreviewFileApproved(context, fileBean)) {
            return false
        }
        val viewModel = previewModel
        if (viewModel == null) {
            toPreviewFileBean = fileBean
        } else {
            putPreviewFileToViewModel(viewModel, fileBean)
            toPreviewFileBean = null
        }
        return true
    }

    final override fun setOperatorResultListener(listener: IFileOperate.OperateResultListener) {
        operatorResultListener = listener
        operationsController?.setOperatorResultListener(listener)
    }

    final override fun setOperateInterceptor(interceptor: IFileOperate) {
        operateInterceptor = interceptor
        operationsController?.setOperateInterceptor(interceptor)
    }

    /**
     * Override the put implement if necessary.
     */
    protected open fun putPreviewFileToViewModel(viewModel: VM, fileBean: BaseFileBean) {
        viewModel.putPreviewFile(fileBean)
    }

    /**
     * Check while preview file is approved to be previewed by this fragment.
     */
    protected abstract fun isPreviewFileApproved(context: Context, fileBean: BaseFileBean): Boolean

    @CallSuper
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val viewModel = ViewModelProvider(this)[logTag, viewModelClass]
        toPreviewFileBean?.let {
            putPreviewFileToViewModel(viewModel, it)
            toPreviewFileBean = null
        }
        previewModel = viewModel
        operationsBar = prepareOperationsBar(view, viewModel)
        filePathItem = onCreateFilePathItem(view)
        viewModel.loadPreviewFile(viewLifecycleOwner, ::applyFilePathIntoItem)
        onViewModelCreated(view, viewModel)
    }

    /**
     * Do some init actions after created view model.
     */
    protected abstract fun onViewModelCreated(view: View, viewModel: VM)

    /**
     * Find [PreviewFilePathItem] from fragment layout if has it.
     */
    protected open fun onCreateFilePathItem(view: View): PreviewFilePathItem? = null

    override fun onStart() {
        super.onStart()
        if ((filePathItem != null) && (clickFilePathListener == null)) {
            Log.d(logTag, "onStart: create default click file path listener")
            activity?.also {
                setOnClickFilePathListener(PreviewOnClickFilePathImpl(it))
            } ?: run {
                Log.e(logTag, "onStart: ERROR! Activity is null")
            }
        }
    }

    @CallSuper
    override fun onDestroyView() {
        super.onDestroyView()
        operationsBar?.release()
        operationsBar = null
        operationsController = null
        filePathItem = null
        previewModel = null
        uiConfigChangeObservers.clear()
    }

    private fun prepareOperationsBar(view: View, viewModel: FilePreviewViewModel): PreviewOperationsBar? {
        if (operationsBarId == -1) {
            return null
        }
        val bar = view.findViewById<PreviewOperationsBar>(operationsBarId)
        val controller = onCreatePreviewOperationsController(viewModel).apply {
            operatorResultListener?.let(::setOperatorResultListener)
            operateInterceptor?.let(::setOperateInterceptor)
        }
        controller.attachToOperationsBar(bar, openButtonTextId, moreMenuResId)
        operationsController = controller
        return bar
    }

    /**
     * Use default implementation of [PreviewOperationsController]
     * And override to replace with customized implementation if necessary.
     */
    protected open fun onCreatePreviewOperationsController(
        viewModel: FilePreviewViewModel
    ): PreviewOperationsController = PreviewOperationsController(this, viewModel, selectPathsCache)

    @CallSuper
    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (!UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            return
        }
        operationsController?.onConfigurationChanged(context?.resources?.configuration)
        onUpdateUIWhenConfigChange(configList)
        uiConfigChangeObservers.forEach {
            it.onUpdateUIWhenConfigChange(configList)
        }
    }

    /**
     * Update UI layout and so on when UI config has been changed if necessary.
     */
    protected open fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        // Default do nothing
    }

    override fun addUIConfigChangeObserver(observer: IUiConfigChangeObserver) {
        uiConfigChangeObservers.add(observer)
    }

    override fun removeUIConfigChangeObserver(observer: IUiConfigChangeObserver) {
        uiConfigChangeObservers.remove(observer)
    }

    @CallSuper
    override fun fromSelectPathResult(requestCode: Int, paths: List<String>?) {
        operationsController?.onSelectPathReturn(requestCode, paths)
    }

    override fun setOnClickFilePathListener(listener: OnClickFilePathListener?) {
        clickFilePathListener = listener
        applyFilePathIntoItem(previewModel?.pickPreviewFile())
    }

    private fun applyFilePathIntoItem(previewFile: BaseFileBean?) {
        val filePath = previewFile?.mData ?: return
        filePathItem?.setFilePath(filePath, clickFilePathListener)
    }

    override fun pressBack(): Boolean {
        // Default do nothing
        return false
    }

    override fun onDetach() {
        super.onDetach()
        releasePreview()
    }

    override fun getOperateContext(): Context = activity ?: MyApplication.appContext

    override fun getSelectFileItems(): List<BaseFileBean> =
        lastPreviewModel?.getSelectItems() ?: emptyList()

    override fun getCopyDestPath(): String =
        selectPathsCache[MessageConstant.MSG_EDITOR_COPY]?.firstOrNull() ?: ""

    override fun getCutDestPath(): String =
        selectPathsCache[MessageConstant.MSG_EDITOR_CUT]?.firstOrNull() ?: ""
}