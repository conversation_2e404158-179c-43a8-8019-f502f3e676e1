/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail
 ** Version: 1.0
 ** Date: 2021/5/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail

import androidx.annotation.Keep
import com.bumptech.glide.load.Key
import java.security.MessageDigest

/**
 * describe the unique file for Extracting thumbnail from doc, audio, etc
 *
 * The param contains the absolute path, last modify time and the size of file
 */
@Keep
class FileThumbnailSignature(
    private val mFilePath: String,
    private val mLastModified: Long,
    private val mSize: Long,
    private val salt: String = ""
) : Key {

    companion object {
        const val TAG = "FileThumbnailSignature"
    }

    private var mStringBuilder: StringBuilder = StringBuilder()
    private var mHashCode: Int = 0

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {
        mStringBuilder
            .clear()
            .append(mFilePath)
            .append(mLastModified)
            .append(mSize)
            .append(salt)

        val byteArray = mStringBuilder.toString().toByteArray()
        messageDigest.update(byteArray)
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as FileThumbnailSignature

        if (mFilePath != other.mFilePath) return false
        if (mLastModified != other.mLastModified) return false
        if (mSize != other.mSize) return false
        if (salt != other.salt) return false

        return true
    }

    override fun hashCode(): Int {
        if (mHashCode == 0) {
            mHashCode = mFilePath.hashCode()
            mHashCode = 31 * mHashCode + mLastModified.hashCode()
            mHashCode = 31 * mHashCode + mSize.hashCode()
            mHashCode = 31 * mHashCode + salt.hashCode()
        }
        return mHashCode
    }
}