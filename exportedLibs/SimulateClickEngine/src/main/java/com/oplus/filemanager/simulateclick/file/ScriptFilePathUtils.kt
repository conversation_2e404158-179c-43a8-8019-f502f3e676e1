/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScriptFilePathUtils.kt
 * Description:
 *     The utils to handle script paths.
 *
 * Version: 1.0
 * Date: 2024-06-13
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-06-13   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.file

import android.content.Context
import com.oplus.filemanager.simulateclick.SimulateClickManager
import java.io.File

internal object ScriptFilePathUtils {
    const val SCRIPTS_FILE_ROOT = "simulate_click_scripts"
    private const val DEBUG_FILE_ROOT = "debug"
    private const val DEBUG_REMOTE_FILE_NAME = "remote_simulate_click_scripts.json"

    @JvmStatic
    val isDebugPathEnabled: Boolean
        get() = SimulateClickManager.enableDebugScripts

    @JvmStatic
    fun getAppScriptDirName(packageName: String): String =
        packageName.replace('.', '_')

    @JvmStatic
    fun getAppScriptPackageName(dirName: String): String =
        dirName.replace('_', '.')

    @JvmStatic
    fun getAppScriptDirPath(packageName: String): String =
        "$SCRIPTS_FILE_ROOT/${getAppScriptDirName(packageName)}".replace('/', File.separatorChar)

    @JvmStatic
    fun getRemoteScriptFileOrDir(context: Context, path: String): File =
        File(context.filesDir, path)

    @JvmStatic
    fun getRemoteScriptRootDir(context: Context): File =
        getRemoteScriptFileOrDir(context, SCRIPTS_FILE_ROOT)

    @JvmStatic
    fun getDebugScriptFileOrDir(context: Context, path: String): File =
        File(File(context.getExternalFilesDir(null), DEBUG_FILE_ROOT), path)

    @JvmStatic
    fun getDebugRemoteFile(context: Context): File =
        getDebugScriptFileOrDir(context, DEBUG_REMOTE_FILE_NAME)
}