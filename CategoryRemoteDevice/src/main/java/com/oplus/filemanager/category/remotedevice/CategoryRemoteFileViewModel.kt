/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : CategoryRemoteFileViewModel
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice

import android.content.Context
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.MyApplication
import com.filemanager.common.back.PredictiveBackDetailPathChecker
import com.filemanager.common.back.PredictiveBackPathChecker
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.bean.remotedevice.Constants
import com.filemanager.common.bean.remotedevice.Constants.ERROR_CODE_PASSWORD_ERROR
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.controller.OnLoaderListener.Companion.STATE_CANCEL
import com.filemanager.common.controller.OnLoaderListener.Companion.STATE_START
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.interfaces.LoadingLoaderListener
import com.filemanager.common.path.FilePathHelper
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.FolderPackageUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NewFunctionSwitch
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.bean.remotedevice.Constants.REMOTE_PATH_PREFIX
import com.filemanager.common.constants.RemoteDeviceConstants
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.utils.EmojiUtils
import com.oplus.filemanager.category.remotedevice.loader.RemoteFileLoader
import com.oplus.filemanager.category.remotedevice.loader.RemoteLoadResult
import com.oplus.filemanager.category.remotedevice.path.RemotePathHelper
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException
import java.util.Locale

class CategoryRemoteFileViewModel :
    SelectionViewModel<BaseFileBean, CategoryRemoteFileViewModel.FileBrowserUiModel>() {

    companion object {
        private const val TAG = "CategoryRemoteFileViewModel"
        private const val REMOTE_FILE_LOADER_ID = 10
        private const val REFRESH_DELAY_TIME = 300L
        private const val HANDLER_MESSAGE_REFRESH = 1
        private const val REMOTE_FILE_SCAN_MODE_SP_KEY = "remote_file_scan_mode"


        @VisibleForTesting
        fun handleLoadComplete(
            viewModel: CategoryRemoteFileViewModel,
            loadData: List<out BaseFileBean>,
            keyMap: HashMap<Int, BaseFileBean>,
            errorCode: Int
        ) {
            loadData.let {
                viewModel.mModeState.mInitState = true
                viewModel.launch {
                    val selectedList = ArrayList<Int>()
                    if ((viewModel.mUiState.value?.mSelectedList?.size ?: 0) > 0) {
                        withContext(Dispatchers.IO) {
                            for (selectedFile in viewModel.mUiState.value!!.mSelectedList) {
                                if (keyMap.containsKey(selectedFile)) {
                                    selectedList.add(selectedFile)
                                }
                            }
                        }
                    }
                    if (viewModel.mPushPathInfo != null) {
                        viewModel.mPositionModel.value?.mCurrentPath?.apply {
                            Log.d(TAG, "handleLoadComplete push ${viewModel.mPushPathInfo}")
                            viewModel.mPathHelp?.push(viewModel.mPushPathInfo)
                        }
                        viewModel.mPushPathInfo = null
                    }
                    if (it.isEmpty() && (viewModel.mModeState.mListModel.value == KtConstants.LIST_SELECTED_MODE)) {
                        Log.d(TAG, "onLoadComplete mResultList is empty change to normal mode")
                        viewModel.mModeState.mListModel.value = KtConstants.LIST_NORMAL_MODE
                    }
                    viewModel.mUiState.value = FileBrowserUiModel(
                        loadData,
                        viewModel.mModeState,
                        selectedList,
                        viewModel.mPositionModel,
                        keyMap,
                        errorCode
                    )
                    Log.d(
                        TAG,
                        "handleLoadComplete mNeedScroll=${viewModel.mNeedScroll},mPositionModel=${viewModel.mPositionModel.value}"
                    )
                    if (viewModel.mNeedScroll) {
                        viewModel.mPositionModel.postValue(viewModel.mPositionModel.value)
                    }
                }
            }
        }
    }

    val mModeState = BaseStateModel(MutableLiveData<Int>(KtConstants.LIST_NORMAL_MODE))
    var mPathHelp: RemotePathHelper? = null
    var mNeedScroll = false

    //In order to distinguish between entering and exiting folders because of the folder animation
    var mIsFolderIn = true
    val mPositionModel = MutableLiveData<PositionModel>()
    var mPushPathInfo: FilePathHelper.PathInfo? = null
    val mBrowseModeState by lazy {
        val lastScanMode = ConfigSharedPreferenceUtils.getInt(REMOTE_FILE_SCAN_MODE_SP_KEY, 0)
        MutableLiveData<Int>(
            if (lastScanMode == 0) {
                KtConstants.SCAN_MODE_LIST
            } else {
                lastScanMode
            }
        )
    }
    val mRemoteFileLoaderCallBack = RemoteFileLoaderCallBack(this)
    var isFromDetail = false
    //这个currentPath只有在第一次加载时才会变动，后续用户点击进入其他页面，该值不变
    var mCurrentPath = ""
    //这个参数是当前获取到的根目录对应的别名，在获取
    var rootPathAlias: String? = null
    var createFolderPath = ""
    var pathPackages: HashMap<String, String>? = null

    var mCurrentPathInitedCode = MutableLiveData<Int>()
    var mCurrentPathIndex: Long = 0
    var mDeviceId: String = ""
    var deviceStatus: Int = RemoteDeviceConstants.UNDISCOVERED
    private var checker: PredictiveBackPathChecker? = null

    fun initLoader(mLoaderController: LoaderController?, path: String, pathAlias: String? = null) {
        Log.d(TAG, "initLoader inputPath $path, pathAlias $pathAlias, loaderController: $mLoaderController")
        if (mRemoteFileLoaderCallBack.getLoader() == null) {
            mPositionModel.value = PositionModel(path, 0, 0)
            mPathHelp?.pushTo(path, pathAlias ?: "")
            Log.d(TAG, "initLoader loader null, init")
            mLoaderController?.initLoader(REMOTE_FILE_LOADER_ID, mRemoteFileLoaderCallBack)
        } else {
            val loaderDivceId = mRemoteFileLoaderCallBack.getDeviceId()
            val loaderPath = mRemoteFileLoaderCallBack.getLoaderPath()
            val needUpdateDeviceId = (mDeviceId.isNotEmpty() && loaderDivceId != mDeviceId)
            val needUpdatePath = (path.isNotEmpty() && path != loaderPath)
            Log.d(TAG, "initLoader loader not null, just load pathFromPositionModel ${mPositionModel.value?.mCurrentPath}, mDeviceId $mDeviceId," +
                    ", loaderDivceId $loaderDivceId, loaderPath $loaderPath, updateId $needUpdateDeviceId, updatePath $needUpdatePath")
            if (path != mPositionModel.value?.mCurrentPath) {
                mPositionModel.value = PositionModel(path, 0, 0)
                mPathHelp?.pushTo(path, pathAlias ?: "")
                mRemoteFileLoaderCallBack.updatePathAndDeviceId(
                    null,
                    if (needUpdateDeviceId) mDeviceId else null
                )
                mRemoteFileLoaderCallBack.loadData(path, true)
            } else {
                mRemoteFileLoaderCallBack.updatePathAndDeviceId(
                    if (needUpdatePath) pathAlias else null,
                    if (needUpdateDeviceId) mDeviceId else null
                )
                if (needUpdatePath) {
                    mRemoteFileLoaderCallBack.loadData(path, true)
                } else {
                    mRemoteFileLoaderCallBack.loadData(path)
                }
            }
        }
    }


    /**
     * 第一次初始化Romote根目录路径。
     */
    fun getInitPathForRemote() {
        launch {
            withContext(Dispatchers.IO) {
                val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>() ?: return@withContext
                mDataLoadState.postValue(STATE_START)
                val pathResult = remoteDeviceApi.getCurrentLinkedUserPath(mDeviceId)
                val userPath = pathResult.rootPath
                val errorCode = pathResult.errorCode
                if (!userPath.isNullOrEmpty() && errorCode == Constants.ERROR_CODE_SUC) {
                    if (!userPath.startsWith(REMOTE_PATH_PREFIX)) {
                        val startWithSeperator = userPath.startsWith(File.separator)
                        if (startWithSeperator) {
                            mCurrentPath = REMOTE_PATH_PREFIX + userPath
                        } else {
                            mCurrentPath = REMOTE_PATH_PREFIX + File.separator + userPath
                        }
                    }
                    rootPathAlias = pathResult.rootPathAlias
                    Log.d(TAG, "getInitPathForRemote $mCurrentPath, rootPathAlias $rootPathAlias isDetail:$isFromDetail")
                    mCurrentPathInitedCode.postValue(Constants.ERROR_CODE_SUC)
                } else {
                    Log.w(TAG, "getInitPathForRemote path from sdk is null, get initPath failed")
                    mCurrentPathInitedCode.postValue(errorCode)
                    mDataLoadState.postValue(STATE_CANCEL)
                }
            }
        }
    }

    fun initPathHelper(currentPath: String) {
        Log.d(TAG, "initPathHelper currentPath $currentPath, mPathHelp $mPathHelp")
        if (mPathHelp == null) {
            Log.d(TAG, "initPathHelper new mPathHelper")
            mPathHelp = RemotePathHelper(currentPath)
        } else if (currentPath.contains(mPathHelp?.getRootPath() ?: "*", true).not()) {
            Log.d(TAG, "initPathHelper mPathHelper not contain root, updateRoot")
            mPathHelp!!.updateRootPath(currentPath)
        }
    }

    /**
     * 这个地方判定根目录是否需要显示别名
     */
    fun checkNeedShowRootPathAlias(): Boolean {
        val splitRootPaths = mCurrentPath.split(File.separator.toRegex()).dropWhile { it.isEmpty() }
        val lastIndex = splitRootPaths.size - 1
        val lastItemInRootPath = splitRootPaths[lastIndex]
        val result = if (rootPathAlias != null && lastItemInRootPath == rootPathAlias) {
            false
        } else if (rootPathAlias == null) {
            false
        } else {
            true
        }
        Log.d(TAG, "checkNeedShowRootPathAlias rootPathAlias $rootPathAlias, mCurrentPath $mCurrentPath, result $result")
        return result
    }

    class RemoteFileLoaderCallBack :
        LoadingLoaderListener<CategoryRemoteFileViewModel, RemoteFileLoader, RemoteLoadResult> {

        private var mLoadNewPath = true

        private var mCurrentPathIndex = 0L
        private var mDeviceId: String
        private var loaderCurrentPath: String

        constructor(viewModel: CategoryRemoteFileViewModel) : super(
            viewModel,
            viewModel.mDataLoadState
        ) {
            mCurrentPathIndex = viewModel.mCurrentPathIndex
            mDeviceId = viewModel.mDeviceId
            loaderCurrentPath = viewModel.mCurrentPath
            Log.d(
                TAG,
                "RemoteFileLoaderCallBack INIT mDeviceId $mDeviceId, mCurrentPath $loaderCurrentPath, mCurrentPathIndex $mCurrentPathIndex"
            )
        }

        internal fun loadData(path: String? = null, loadNewPath: Boolean = false) {
            mLoadNewPath = loadNewPath
            getLoader()?.apply {
                if (!path.isNullOrEmpty()) {
                    updatePathAndDeviceId(path)
                    if (loadNewPath) {
                        mCurrentPathIndex = 0
                    }
                    setPath(path)
                    setRemoteDeviceId(deviceId = mDeviceId)
                    setStartIndex(mCurrentPathIndex)
                }
                forceLoad()
            }
        }

        /**
         * 用同样的path加载更多
         */
        internal fun loadMoreData() {
            getLoader()?.apply {
                if (loaderCurrentPath.isEmpty()) {
                    return
                } else {
                    setPath(loaderCurrentPath)
                    setRemoteDeviceId(deviceId = mDeviceId)
                    setStartIndex(mCurrentPathIndex + 1)
                }
                forceLoad()
            }
        }


        internal fun isLoadNewPath() = mLoadNewPath

        override fun onCreateLoader(viewModel: CategoryRemoteFileViewModel?): RemoteFileLoader? {
            Log.d(TAG, "onCreateLoader")
            return if (viewModel != null) {
                RemoteFileLoader(MyApplication.sAppContext)
            } else null
        }

        override fun onLoadComplete(result: RemoteLoadResult?) {
            onLoadComplete(viewModel, result)
        }

        override fun onLoadComplete(
            viewModel: CategoryRemoteFileViewModel?,
            data: RemoteLoadResult?
        ) {
            Log.d(
                TAG,
                "onLoadComplete in browser: size=${data?.mResultList?.size}, mapSize : ${data?.mResultMap?.size}"
            )
            data?.let {
                if (viewModel != null) {
                    viewModel.pathPackages?.let {
                        for (file in data.mResultList) {
                            file.originPackage = it[file.mData]
                        }
                    }
                    handleLoadComplete(viewModel, data.mResultList, data.mResultMap, data.errorCode)
                    mLoadingState.value = OnLoaderListener.STATE_DONE
                } else {
                    Log.d(TAG, "onLoadComplete viewModel is null")
                }
            }
        }

        fun updatePathAndDeviceId(path: String? = null, deviceId: String? = null) {
            if (path != null) {
                loaderCurrentPath = path
            }
            if (deviceId != null) {
                mDeviceId = deviceId
            }
        }

        fun getDeviceId(): String {
            return mDeviceId
        }

        fun getLoaderPath(): String {
            return loaderCurrentPath
        }
    }

    fun sortReload() {
        mRemoteFileLoaderCallBack.loadData()
    }

    fun resetState() {
        changeListMode(KtConstants.LIST_NORMAL_MODE)
    }


    fun clickToolbarSelectAll() {
        if (getCanSelectSize() == mUiState.value?.mSelectedList?.size) {
            //取消全选
            mUiState.value?.mSelectedList?.clear()
            mUiState.value = mUiState.value
        } else {
            //全选
            mUiState.value?.mSelectedList?.clear()
            mUiState.value?.mFileList?.let {
                var key: Int? = null
                for (baseFileBean in it) {
                    val containIllegalChar = EmojiUtils.containsIllegalCharFileName(baseFileBean.mDisplayName)
                    val canNotSelect = containIllegalChar || baseFileBean.isDir()
                    if (baseFileBean.mFileWrapperLabel == null && !canNotSelect) {
                        key = baseFileBean.mData?.lowercase(Locale.ROOT)?.hashCode() ?: continue
                        mUiState.value?.mSelectedList?.add(key)
                    }
                }
            }
            mUiState.value = mUiState.value
        }
    }

    fun clickPathBar(index: Int) {
        if (mModeState.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
            changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
        val pathLeft = mPathHelp?.getPathLeft() ?: index + 1
        val index = pathLeft - index - 1
        val pathInfo = mPathHelp?.setTopPath(index)
        Log.d(TAG, "clickPathBar pathInfo=$pathInfo, index $index, pathLeft $pathLeft")
        pathInfo?.let {
            mIsFolderIn = false
            mPositionModel.value?.mCurrentPath = it.path
            previewClickedFileLiveData.value = null
            mNeedScroll = true
            resetPushPathInfo()
            mRemoteFileLoaderCallBack.loadData(it.path, true)
            syncDetailPredictiveBackState()
        }
    }

    /**
     * 跳转修改密码页面
     */
    fun jumpToNewDevicePage(activity: BaseVMActivity?, deviceId: String?) {
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>() ?: return
        if (activity != null) {
            remoteDeviceApi.jumpPasswordPage(activity, deviceId)
        }
    }

    fun onDirClick(
        activity: BaseVMActivity?,
        baseFile: BaseFileBean,
        firstVisibleItemPosition: Int,
        offset: Int
    ) {
        launch {
            if ((activity != null) && baseFile.mIsDirectory && baseFile is RemoteFileBean) {
                Log.d(TAG, "onDirClick $baseFile, path ${baseFile.mData}")
                baseFile.mData?.let {
                    mPositionModel.value?.mCurrentPath = it
                    mIsFolderIn = true
                    mNeedScroll = true
                    resetPushPathInfo()
                    mPushPathInfo = FilePathHelper.PathInfo(it, firstVisibleItemPosition, offset)
                    syncDetailPredictiveBackState()
                    //这里将文件夹的别名写入extra,代表这一级目录的的别名
                    mPushPathInfo?.extra = baseFile.alternativeName ?: ""
                    mRemoteFileLoaderCallBack.loadData(it, true)
                }
            }
        }
    }

    fun setCurrentFromOtherSide(path: String) {
        Log.d(TAG, "setCurrentFromOtherSide path $path")
        if (mModeState.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
            changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
        mPathHelp?.pushTo(path)
        mPositionModel.value?.mCurrentPath = path
        mIsFolderIn = true
        mNeedScroll = true
        resetPushPathInfo()
        mRemoteFileLoaderCallBack.loadData(path, true)
    }

    fun pressBack(): Boolean {
        mModeState.let {
            if (it.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
                changeListMode(KtConstants.LIST_NORMAL_MODE)
                return true
            } else {
                if (isFromDetail && mCurrentPath.isNotEmpty()) {
                    val path = mPositionModel.value?.mCurrentPath
                    if (!path.isNullOrEmpty() && mCurrentPath.contains(path)) {
                        return false
                    }
                }
                Log.d(TAG, "pressBack deviceStatus $deviceStatus")
                if (deviceStatus != RemoteDeviceConstants.CONNECTED) {
                    //如果这里当前页面断开链接状态，返回直接退出页面
                    return false
                }
                if (mUiState.value?.erroCode == ERROR_CODE_PASSWORD_ERROR || mCurrentPathInitedCode.value == ERROR_CODE_PASSWORD_ERROR) {
                    //如果是链接错误的页面，返回键直接退出页面
                    Log.d(TAG, "pressBack password error, just return false")
                    return false
                }
                val popedPathInfo = mPathHelp?.pop()
                if (popedPathInfo != null) {
                    val info = mPathHelp?.getTopPathInfo()
                    info?.path?.let {
                        val newPathBelowRootPath = it.contentEquals(REMOTE_PATH_PREFIX, true)
                        if (newPathBelowRootPath) {
                            Log.d(TAG, "pressBack is below rootPath, backpress not change path")
                            return false
                        }
                        Log.d(TAG, "pressBack $it")
                        mIsFolderIn = false
                        mPositionModel.value?.mCurrentPath = it
                        mPositionModel.value?.mPosition = info.position
                        mPositionModel.value?.mOffset = info.y
                        mNeedScroll = true
                        resetPushPathInfo()
                        mRemoteFileLoaderCallBack.loadData(it, true)
                        syncDetailPredictiveBackState()
                        return true
                    }
                }
                return false
            }
        }
    }

    private fun resetPushPathInfo() {
        mPushPathInfo = null
    }

    override fun loadData() {
        Log.d(TAG, "loadData")
        mRemoteFileLoaderCallBack.loadData()
    }

    fun clickScanModeItem(context: Context? = null) {
        if (mBrowseModeState.value == KtConstants.SCAN_MODE_LIST) {
            mBrowseModeState.value = KtConstants.SCAN_MODE_GRID
            StatisticsUtils.onCommon(
                context, StatisticsUtils.SCAN_MODE_SDCARD_SWITCH, hashMapOf(
                    StatisticsUtils.SCAN_MODE_SDCARD_SWITCH to "0"
                )
            )
        } else {
            mBrowseModeState.value = KtConstants.SCAN_MODE_LIST
            StatisticsUtils.onCommon(
                context, StatisticsUtils.SCAN_MODE_SDCARD_SWITCH, hashMapOf(
                    StatisticsUtils.SCAN_MODE_SDCARD_SWITCH to "1"
                )
            )
        }
        mBrowseModeState.value?.apply {
            ConfigSharedPreferenceUtils.putInt(REMOTE_FILE_SCAN_MODE_SP_KEY, this)
        }
    }

    class FileBrowserUiModel : BaseUiModel<BaseFileBean> {
        var mPositionModel: MutableLiveData<PositionModel>? = null
        var erroCode: Int? = null

        constructor(
            fileList: List<BaseFileBean>,
            stateModel: BaseStateModel,
            selectedList: ArrayList<Int>,
            positionModel: MutableLiveData<PositionModel>?,
            keyMap: HashMap<Int, BaseFileBean>,
            errorCode: Int
        ) : super(fileList, stateModel, selectedList, keyMap) {
            mPositionModel = positionModel
            erroCode = errorCode
        }
    }

    data class PositionModel(
        var mCurrentPath: String,
        var mPosition: Int,
        var mOffset: Int
    )

    override fun getRealFileSize(): Int {
        var size = 0
        mUiState.value?.mFileList?.apply {
            for (baseFileBean in this) {
                if (baseFileBean.mFileWrapperLabel == null) {
                    size++
                }
            }
        }
        Log.d(TAG, "getRealFileSize $size")
        return size
    }

    fun getCanSelectSize(): Int {
        var size = 0
        mUiState.value?.mFileList?.apply {
            for (baseFileBean in this) {
                val containIllegalChar = EmojiUtils.containsIllegalCharFileName(baseFileBean.mDisplayName)
                val canNotSelect = containIllegalChar || baseFileBean.isDir()
                if (!canNotSelect) {
                    size++
                }
            }
        }
        Log.d(TAG, "getCanSelectSize $size")
        return size
    }

    override fun getRecyclerViewScanMode(): com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE {
        return if (mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.LIST
        } else {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.GRID
        }
    }

    fun loadData(path: String?, loadNewPath: Boolean) {
        mRemoteFileLoaderCallBack.loadData(path, loadNewPath)
    }

    override fun onCreateFolderPath(path: String) {
        createFolderPath = path
    }

    fun loadFolderLogo() {
        if (NewFunctionSwitch.SUPPORT_FOLDER_LOGO && PrivacyPolicyController.hasAgreePrivacy()) {
            launch {
                withContext(Dispatchers.IO) {
                    pathPackages = FolderPackageUtil.getAllPathAndName()
                    mUiState.value?.mFileList?.let {
                        withContext(Dispatchers.Main) {
                            pathPackages?.let { ps ->
                                for (file in it) {
                                    file.originPackage = ps[file.mData]
                                }
                            }
                            mNeedScroll = false
                            mUiState.value = mUiState.value
                        }
                    }
                }
            }
        }
    }

    fun releaseDir(lifecycleOwner: LifecycleOwner, context: Context?) {
        if (context == null) return
        Log.i(TAG, "releaseDir")
        val file = File(context.cacheDir, "remotePic")
        val filePath = file.absolutePath
        lifecycleOwner.lifecycleScope.launch(Dispatchers.IO) {
            deleteRemoteDir(filePath)
        }
    }

    private fun deleteRemoteDir(directoryPath: String): Boolean {
        try {
            val directory = File(directoryPath)
            if (directory.exists()) {
                val files = directory.listFiles()
                if (files != null) {
                    for (file in files) {
                        if (file.isDirectory) {
                            deleteRemoteDir(file.absolutePath)
                        } else {
                            file.delete()
                        }
                    }
                }
            }
            return directory.delete()
        } catch (e: IOException) {
            Log.e(TAG, "write error", e)
        }
        return false
    }

    /**
     * 只处理详情界面(路径为非根目录)的预测性返回手势状态
     * 正常情况下的全部文件界面，在 FilePathHelper中已处理
     */
    private fun syncDetailPredictiveBackState() {
        if (checker == null) {
            val helper = mPathHelp ?: return
            checker = PredictiveBackDetailPathChecker(helper, mCurrentPath, true)
        }
        checker?.let {
            PredictiveBackUtils.checkEnableBackAnim(it)
        }
    }
}