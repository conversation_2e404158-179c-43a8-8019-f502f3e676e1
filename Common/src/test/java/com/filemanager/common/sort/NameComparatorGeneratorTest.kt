/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : NameComparatorGeneratorTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/1
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  hank.zhou      2022/9/1      1.0        create
 ***********************************************************************/
package com.filemanager.common.sort

import com.filemanager.common.base.BaseFileBean
import org.junit.Assert
import org.junit.Test

class NameComparatorGeneratorTest {
    private val mCompare = NameComparatorGenerator()

    @Test
    fun filterPreviousZeroTest() {
        Assert.assertEquals("", mCompare.filterPreviousZero("00000"))
        Assert.assertEquals("1", mCompare.filterPreviousZero("00001"))
        Assert.assertEquals("10", mCompare.filterPreviousZero("00010"))
        Assert.assertEquals("230", mCompare.filterPreviousZero("00230"))
        Assert.assertEquals("123", mCompare.filterPreviousZero("123"))
    }

    @Test
    fun compareNumberStringTest() {
        Assert.assertEquals(true, mCompare.compareNumberString("1111", "1110") > 0)
        Assert.assertEquals(true, mCompare.compareNumberString("1111", "01111") > 0)
        Assert.assertEquals(true, mCompare.compareNumberString("1234", "01111") > 0)
        Assert.assertEquals(true, mCompare.compareNumberString("000002", "1") > 0)
        Assert.assertEquals(true, mCompare.compareNumberString("0000010", "2") > 0)
        Assert.assertEquals(true, mCompare.compareNumberString("00000", "00000") == 0)
        Assert.assertEquals(true, mCompare.compareNumberString("0000", "00000") > 0)
    }

    @Test
    fun getNumberStringTest() {
        Assert.assertEquals("1111", mCompare.getNumberString("a1111", 1))
        Assert.assertEquals("1111", mCompare.getNumberString("a1111b", 1))
        Assert.assertEquals("1111", mCompare.getNumberString("ab1111b", 2))
        Assert.assertEquals("11", mCompare.getNumberString("ab11b11b", 2))
        Assert.assertEquals("23", mCompare.getNumberString("ab11b23b", 5))
    }

    @Test
    fun compareStringTest() {
        Assert.assertEquals(true, mCompare.compareString("a1111", "a1110") > 0)
        Assert.assertEquals(true, mCompare.compareString("a1111", "a1110b") > 0)
        Assert.assertEquals(true, mCompare.compareString("b1111", "a1110b") > 0)
        Assert.assertEquals(true, mCompare.compareString("a11b11", "a11a23") > 0)
        Assert.assertEquals(true, mCompare.compareString("a11b11", "a11b10") > 0)
        Assert.assertEquals(true, mCompare.compareString("a11b11", "a11b010") > 0)
        Assert.assertEquals(true, mCompare.compareString("a11b011", "a11b0010") > 0)
        Assert.assertEquals(true, mCompare.compareString("a11b011", "a11b0011") > 0)
        Assert.assertEquals(true, mCompare.compareString("aa", "a") > 0)
        Assert.assertEquals(true, mCompare.compareString("aaa", "aa") > 0)
        Assert.assertEquals(true, mCompare.compareString("aa0000", "aa00") < 0)
    }

    @Test
    fun checkIsDigitTest() {
        Assert.assertEquals(false, mCompare.checkIsDigit('a'))
        Assert.assertEquals(true, mCompare.checkIsDigit('1'))
        Assert.assertEquals(false, mCompare.checkIsDigit('@'))
        Assert.assertEquals(false, mCompare.checkIsDigit('+'))
        Assert.assertEquals(true, mCompare.checkIsDigit('2'))
        Assert.assertEquals(true, mCompare.checkIsDigit('3'))
    }

    @Test
    fun testGetComparator() {
        val compare = NameComparatorGenerator.getNameComparator(true)
        val file1 = BaseFileBean()
        val file2 = BaseFileBean()
        file1.mDisplayName = "test1"
        file2.mDisplayName = "test2"
        Assert.assertTrue(compare.compare(file1, file2) < 0)
        val compare2 = NameComparatorGenerator.getNameComparator(false)
        Assert.assertTrue(compare2.compare(file1, file2) > 0)
    }
}