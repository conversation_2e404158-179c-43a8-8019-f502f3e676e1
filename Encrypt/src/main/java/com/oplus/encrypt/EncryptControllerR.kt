/*********************************************************************
 * * Copyright (C), 2020, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/5/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.encrypt

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import androidx.annotation.MainThread
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.EncryptConstants
import com.filemanager.common.controller.FileEncryptController
import com.filemanager.common.utils.Log
import com.coloros.encryption.IEncryptProgressListener
import com.coloros.encryption.EncyptionInterface
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils

class EncryptControllerR(activity: BaseVMActivity) : FileEncryptController(activity) {
    private var mEncryptService: EncyptionInterface? = null
    private var mServiceConnectedCallback: ServiceConnectedCallback? = null
    private val mFileManagerEncryptionInterface by lazy {
        object : FileManagerEncryptionInterface {
            override fun encryptionTasks(sources: List<String?>?, imageTypes: IntArray?, scanState: Boolean, listener: FileManagerIEncryptProgressListener?): Int {
                return mEncryptService?.encryptionTasks(sources, imageTypes, scanState, object : IEncryptProgressListener.Stub() {

                    override fun onStarted() {
                        listener?.onStarted()
                    }

                    override fun onProgress(progress: Int) {
                        listener?.onProgress(progress)
                    }

                    override fun onFinished(result: Int, failedCount: Int) {
                        listener?.onFinished(result, failedCount)
                    }

                }) ?: 0
            }

            override fun setStopEncryption(state: Boolean) {
                mEncryptService?.setStopEncryption(state)
            }
        }
    }
    private val mConnection: ServiceConnection by lazy {
        object : ServiceConnection {
            override fun onServiceConnected(componentName: ComponentName, iBinder: IBinder) {
                mEncryptService = EncyptionInterface.Stub.asInterface(iBinder)?.apply {
                    mServiceConnectedCallback?.onServiceConnected(mFileManagerEncryptionInterface)
                }
                Log.d(TAG, "onServiceConnected mEncryptService is null: ${mEncryptService == null}")
            }

            override fun onServiceDisconnected(componentName: ComponentName) {
                Log.d(TAG, "onServiceDisconnected")
                mEncryptService = null
                mServiceConnectedCallback = null
            }

            override fun onBindingDied(componentName: ComponentName) {
                Log.d(TAG, "onBindingDied")
                mEncryptService = null
                mServiceConnectedCallback = null
            }

            override fun onNullBinding(componentName: ComponentName) {
                Log.d(TAG, "onNullBinding")
                mEncryptService = null
                mServiceConnectedCallback = null
            }
        }
    }


    @MainThread
    override fun runEncryptTask(callback: ServiceConnectedCallback) {
        mEncryptService?.run {
            callback.onServiceConnected(mFileManagerEncryptionInterface)
        } ?: kotlin.run {
            Log.d(TAG, "runEncryptTask: EncryptService need rebind")
            mServiceConnectedCallback = callback
            bindEncryptService()
        }
    }

    override fun ondestroy() {
        unbindEncryptService()
    }

    override fun bindEncryptService() {
        Log.d(TAG, "bindEncryptService")
        try {
            val intent = Intent()
            intent.action = EncryptConstants.FILE_ENCRYPTION_ACTION
            intent.setPackage(EncryptConstants.FILE_ENCRYPTION_PACKGE)
            CollectPrivacyUtils.collectInstalledAppList(EncryptConstants.FILE_ENCRYPTION_PACKGE)
            mActivity.bindService(intent, mConnection, Context.BIND_AUTO_CREATE)
        } catch (ex: Exception) {
            Log.d(TAG, "bindService error: " + ex.message)
        }
    }

    private fun unbindEncryptService() {
        Log.d(TAG, "unbindEncryptService")
        try {
            mActivity.unbindService(mConnection)
            mEncryptService = null
            mServiceConnectedCallback = null
        } catch (ex: Exception) {
            Log.d(TAG, "unbindService error: " + ex.message)
        }
    }
}