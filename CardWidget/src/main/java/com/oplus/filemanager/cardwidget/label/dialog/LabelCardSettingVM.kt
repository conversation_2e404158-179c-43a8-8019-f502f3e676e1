/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LabelCardSettingVM
 ** Description : 标签设置的ViewModel
 ** Version     : 1.0
 ** Date        : 2022/11/25
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue      2022/11/25      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.dialog

import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.cardwidget.label.LabelCardWidgetCodeUtils
import com.oplus.filemanager.cardwidget.label.data.LabelCardData
import com.oplus.filemanager.cardwidget.label.data.LabelData
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Objects

class LabelCardSettingVM : BaseViewModel() {

    companion object {
        private const val TAG = "LabelCardSettingVM"
        const val MAX_FILE_SIZE = 4
        const val NO_LABEL = 0L // 无标签时，标签的默认id

        fun isNoLabel(labelId: Long?): Boolean {
            return Objects.isNull(labelId) || (NO_LABEL == labelId)
        }
    }

    @VisibleForTesting
    var cardWidgetCode = ""
    @VisibleForTesting
    var originLabel = NO_LABEL
    @VisibleForTesting
    val widgetCodeUtils = LabelCardWidgetCodeUtils.instance

    var labelId: MutableLiveData<Long> = MutableLiveData()
    var labelName: MutableLiveData<String> = MutableLiveData()
    var fileList: MutableLiveData<Pair<Int, List<BaseFileBean>>> = MutableLiveData<Pair<Int, List<BaseFileBean>>>()
    var labelList: MutableLiveData<MutableList<LabelData>> = MutableLiveData<MutableList<LabelData>>()
    var isSingleMode = false

    fun setCardWidgetCode(cardTypeCode: Int, cardId: Int, hostId: Int) {
        cardWidgetCode = "$cardTypeCode&$cardId&$hostId"
        isSingleMode = false
        // 从sp获取标签id
        originLabel = getLabelId(cardWidgetCode)
        Log.e(TAG, "setCardWidgetCode card:$cardWidgetCode label:$originLabel")
        this.labelId.value = originLabel
        // 根据labelID获取数据
        loadLabelCardData(originLabel)
    }

    fun setWidgetCode(code: String) {
        cardWidgetCode = code
        isSingleMode = true
        // 从sp获取标签id
        originLabel = getLabelId(cardWidgetCode)
        Log.e(TAG, "setWidgetCode card:$cardWidgetCode label:$originLabel")
        this.labelId.value = originLabel
        // 根据labelID获取数据
        loadLabelCardData(originLabel)
    }

    /**
     * 加载标签卡片数据
     */
    @VisibleForTesting
    fun loadLabelCardData(id: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            val cardData = queryLabelCardData(id)
            labelId.postValue(cardData.labelId)
            labelName.postValue(cardData.labelName)
            fileList.postValue(Pair(cardData.fileCount, cardData.fileList ?: emptyList()))
        }
    }

    /**
     * 根据卡片code获取对应的标签id
     */
    @VisibleForTesting
    fun getLabelId(cardWidgetCode: String): Long {
        val cardData = widgetCodeUtils.getCacheByWidgetCode(cardWidgetCode)
        return cardData?.labelId ?: NO_LABEL
    }

    /**
     * 查询标签卡片数据
     */
    @VisibleForTesting
    fun queryLabelCardData(id: Long): LabelCardData {
        var fileLabel = FileLabelDBHelper.getFileLabelById(id)
        if (Objects.isNull(fileLabel)) { // 无标签
            Log.e(TAG, "queryLabelCardData no label")
            return LabelCardData(NO_LABEL, "", 0, emptyList())
        }
        val labelId = fileLabel?.id ?: id
        val labelName = fileLabel?.name ?: ""
        val fileList = FileLabelMappingDBHelper.getFileListByLabelId(labelId)
        if (fileList == null || fileList.isEmpty()) { // 空文件
            Log.e(TAG, "queryLabelCardData label $labelId files is empty")
            return LabelCardData(labelId, labelName, 0, emptyList())
        }
        val fileCount = fileList.size
        val list: List<BaseFileBean> = fileList.subList(0, Math.min(fileCount, MAX_FILE_SIZE)).map {
            val bean = BaseFileBean()
            PathFileWrapper(it.filePath).apply {
                bean.mDisplayName = mDisplayName
                bean.mSize = mSize
                bean.mDateModified = mDateModified
                bean.mLocalType = mLocalType
                bean.mData = mData
                bean.mMediaDuration = it.duration
            }
            bean
        }
        val currentSort = SortModeUtils.getSharedSortMode(MyApplication.sAppContext, SortRecordModeFactory.getLabelKey())
        val isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getLabelKey())
        val mLastSort = SortModeUtils.getSharedSortMode(MyApplication.sAppContext, SortModeUtils.BROWSER_LAST_SORT_RECORD)
        SortHelper.sortFiles(list, currentSort, mLastSort, false, isDesc)
        return LabelCardData(labelId, labelName, fileCount, list)
    }

    /**
     * 查询默认标签文件：当前第一个可用的标签
     */
    @VisibleForTesting
    fun queryDefaultLabel(): FileLabelEntity? {
        val allLabels = FileLabelDBHelper.getAllLabels()
        if (allLabels.isEmpty()) {
            return null
        }
        return allLabels[0]
    }

    /**
     * 保存标签和卡片的映射关系
     */
    fun saveLabelCardMapping() {
        val labelId = labelId.value ?: NO_LABEL
        if (originLabel == labelId) {
            Log.e(TAG, "saveLabelCardMapping 标签无修改")
            return
        }
        if (labelId == NO_LABEL) { // 选择了无标签
            Log.e(TAG, "saveLabelCardMapping 选择无标签，删除关联 $cardWidgetCode -> $originLabel")
            widgetCodeUtils.deleteCardLabelMapping(cardWidgetCode, originLabel)
            return
        }
        widgetCodeUtils.switchLabel(cardWidgetCode, labelId)
    }

    /**
     * 加载所有的标签
     */
    fun loadAllLabels() {
        viewModelScope.launch(Dispatchers.IO) {
            val list = queryAllLabels()
            labelList.postValue(list)
        }
    }

    /**
     * 查询所有的标签
     */
    @VisibleForTesting
    fun queryAllLabels(): MutableList<LabelData> {
        val allLabels = FileLabelDBHelper.getAllLabels()
        val list = mutableListOf<LabelData>()
        allLabels.forEach {
            list.add(LabelData(it.id, it.name))
        }
        return list
    }

    /**
     * 选择标签
     */
    fun selectLabel(id: Long) {
        Log.d(TAG, "selectLabel -> id = $id ; labelId = ${labelId.value}")
        if (labelId.value == id) {
            Log.e(TAG, "selectLabel 标签无修改")
            return
        }
        labelId.value = id
        // 更新数据
        loadLabelCardData(id)
    }
}