/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudFileActionRenameTest
 ** Description : CloudFileActionRename Unit Test
 ** Version     : 1.0
 ** Date        : 2024/01/10 19:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/01/10       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.operate.rename

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.oplus.filemanager.drivebrowser.data.model.BaseResponse
import com.oplus.filemanager.drivebrowser.data.repository.kdocs.KDocsFileDriveRepository
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.drivebrowser.ui.CloudDocsItem
import com.oplus.filemanager.provider.DriveFileDBHelper
import io.mockk.coEvery
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import kotlin.time.Duration.Companion.seconds

class CloudFileActionRenameTest {

    private lateinit var context: Context
    private lateinit var lifeOwner: LifecycleOwner
    private lateinit var cloudFile: CloudDocsItem
    private lateinit var networkService: FileDriveRepository
    private lateinit var renameAction: CloudFileActionRename
    private val dispatcher = StandardTestDispatcher()

    @Before
    fun setup() {
        context = mockk()
        mockkObject(MyApplication)
        every { context.applicationContext }.returns(context)
        every { appContext }.returns(context)
        lifeOwner = mockk()
        cloudFile = CloudDocsItem()
        cloudFile.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
        cloudFile.id = "123"
        cloudFile.mDisplayName = "测试"
        networkService = mockk<TencentFileDriveRepository>()
        renameAction = CloudFileActionRename(lifeOwner, cloudFile, networkService)
        mockkStatic(DriveFileDBHelper::class)
        mockkStatic(StatisticsUtils::class)
        justRun { StatisticsUtils.onCommon(any(), any(), any<Map<String, String>>()) }
        Dispatchers.setMain(dispatcher)
    }

    @After
    fun teardown() {
        unmockkObject(MyApplication)
        unmockkStatic(DriveFileDBHelper::class)
        unmockkStatic(StatisticsUtils::class)
        Dispatchers.resetMain()
    }


    @Test
    fun should_return_boolean_when_isFileExist() {
        Assert.assertTrue(renameAction.isFileExist(cloudFile))
    }

    @Test
    fun should_return_true_when_reallyExecuteAction_same_name() {
        cloudFile.mDisplayName = "新文件名"
        val result = renameAction.reallyExecuteAction(cloudFile, "新文件名")
        Assert.assertTrue(result)
    }

    @Test
    fun should_return_boolean_when_reallyExecuteAction_folder() {
        runTest(timeout = testCaseTimeout) {
            cloudFile.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
            val response = BaseResponse<String>()
            response.codeMsg(-1, "")
            coEvery { networkService.renameFolder(any(), any()) }.returns(response)
            var result = renameAction.reallyExecuteAction(cloudFile, "新文件名")
            Assert.assertFalse(result)

            val response1 = BaseResponse<String>()
            response1.codeMsg(0, "")
            coEvery { networkService.renameFolder(any(), any()) }.returns(response1)
            every { DriveFileDBHelper.updateDocumentName(any(), any()) }.returns(false)
            result = renameAction.reallyExecuteAction(cloudFile, "新文件名")
            Assert.assertFalse(result)

            val response2 = BaseResponse<String>()
            response2.codeMsg(0, "")
            coEvery { networkService.renameFolder(any(), any()) }.returns(response2)
            every { DriveFileDBHelper.updateDocumentName(any(), any()) }.returns(true)
            result = renameAction.reallyExecuteAction(cloudFile, "新文件名")
            Assert.assertTrue(result)
        }
    }

    @Test
    fun should_return_boolean_when_reallyExecuteAction_file() {
        runTest(timeout = testCaseTimeout) {
            cloudFile.mLocalType = MimeTypeHelper.IMAGE_TYPE
            val response = BaseResponse<String>()
            response.codeMsg(-1, "")
            coEvery { networkService.renameDocument(any(), any()) }.returns(response)
            var result = renameAction.reallyExecuteAction(cloudFile, "新文件名")
            Assert.assertFalse(result)

            val response1 = BaseResponse<String>()
            response1.codeMsg(0, "")
            coEvery { networkService.renameDocument(any(), any()) }.returns(response1)
            every { DriveFileDBHelper.updateDocumentName(any(), any()) }.returns(false)
            result = renameAction.reallyExecuteAction(cloudFile, "新文件名")
            Assert.assertFalse(result)

            val response2 = BaseResponse<String>()
            response2.codeMsg(0, "")
            coEvery { networkService.renameDocument(any(), any()) }.returns(response2)
            every { DriveFileDBHelper.updateDocumentName(any(), any()) }.returns(true)
            result = renameAction.reallyExecuteAction(cloudFile, "新文件名")
            Assert.assertTrue(result)
        }
    }

    @Test
    fun should_call_onCommon_when_statisticsRenameFail() {
        networkService = mockk<TencentFileDriveRepository>()
        renameAction = CloudFileActionRename(lifeOwner, cloudFile, networkService)
        renameAction.statisticsRenameFail("模拟失败")
        verify { StatisticsUtils.onCommon(any(), StatisticsUtils.RENAME_DRIVE_FILE_FAIL, any<Map<String, String>>()) }

        networkService = mockk<KDocsFileDriveRepository>()
        renameAction = CloudFileActionRename(lifeOwner, cloudFile, networkService)
        renameAction.statisticsRenameFail("模拟失败")
        verify(atLeast = 2) { StatisticsUtils.onCommon(any(), StatisticsUtils.RENAME_DRIVE_FILE_FAIL, any<Map<String, String>>()) }
    }

    @Test
    fun should_call_notifyObserver_when_afterRun() {
        val action = mockk<CloudFileActionRename>(relaxed = true)
        every { action.cloudFile }.returns(cloudFile)
        justRun { action.notifyObserver(any(), any()) }
        every { action.afterRun(any()) }.answers { callOriginal() }

        action.afterRun(true)
        verify { action.notifyObserver(ACTION_DONE, any()) }

        action.afterRun(false)
        verify { action.notifyObserver(ACTION_FAILED, any()) }
    }

    private companion object {
        private val testCaseTimeout = 30.seconds
    }
}