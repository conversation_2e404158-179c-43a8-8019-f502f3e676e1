/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ResultCodeDesc
 * * Description : ResultCode Desc
 * * Version     : 1.0
 * * Date        : 2025/05/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.framework.touchshare

import com.oplus.interconnect.share.sdk.data.ResultCode

object ResultCodeDesc {

    fun desc(code: Int): String {
        val desc = when (code) {
            ResultCode.ERROR_SHARE_UNKNOWN.value -> "unknown error"
            ResultCode.SUCCESS.value -> "success"
            ResultCode.FAILURE.value -> "failure"
            ResultCode.PARAMETER_ERROR.value -> "params error"
            ResultCode.PERMISSION_ERROR.value -> "permission error"
            ResultCode.UNAVAILABLE_ERROR.value -> "unavailable"
            ResultCode.IPC_ERROR.value -> "ipc error"
            ResultCode.USER_OPERATION_ERROR.value -> "user operate error"
            ResultCode.CONNECT_ERROR.value -> "connect error"
            ResultCode.INTERNAL_ERROR.value -> "internal error"
            ResultCode.EXPIRED_ERROR.value -> "expired error"
            else -> "unknown code"
        }
        return "($code:$desc)"
    }

    fun isSuccess(code: Int): Boolean {
        return ResultCode.SUCCESS.value == code
    }
}