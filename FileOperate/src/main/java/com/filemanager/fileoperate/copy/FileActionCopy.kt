/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileActionCopy.kt
 ** Description: Copy files function
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.copy

import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.checkDestStorageSpace
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.fileoperate.*
import com.filemanager.fileoperate.base.BaseFileActionObserver.Companion.MAX_PROGRESS
import com.filemanager.fileoperate.base.*
import com.filemanager.fileoperate.base.BaseFileActionObserver.Companion.EXTERNAL_STORAGE_COPY_PROGRESS
import com.oplus.filemanager.dfm.DFMManager
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.fileservice.IFileService.Companion.OPERATE_TYPE_COPY
import com.oplus.filemanager.interfaze.main.IMain
import java.io.File


open class FileActionCopy(
    lifecycle: LifecycleOwner,
    sourceFiles: List<BaseFileBean>,
    destFile: BaseFileBean
) : FileActionBaseCopyCut(lifecycle, sourceFiles, destFile) {
    override var TAG = "FileActionCopy"

    protected var mCurrentProgress = 0
    protected var mTotalFileLength: Long = 0

    // Result Count
    protected var mHasSkipFile = false
    protected var mHasSuccessFile = false

    protected var mIsOperateDatabase: Boolean = true
    private val mMediaScannerBatchAction by lazy {
        NotifyMediaScannerBatchAction(Utils.MEDIA_SCAN_COPY)
    }

    protected var mDealFileListener = object : FileCopyHelper.OnDealFileListener() {
        override var isCancel: () -> Boolean = { isCancelled() }

        override fun onProgressChanged(progress: Long) {
            onDealFileProgressChanged(progress)
        }

        override fun onSuccess(sourceFile: File, destFile: File, isDirectory: Boolean) {
            onDealFileSuccess(sourceFile, destFile, isDirectory)
        }

        // return true means skip this failed file, false means cancel copy file
        override fun onError(sourceFile: File, destFile: File): Boolean {
            return if (isCancelled()) false else onDealFileError(sourceFile, destFile)
        }
    }

    override fun exceptionDetection(): Any? = super.exceptionDetection() ?: kotlin.run {
        // calculate file total size
        for (file in mOperateFiles) {
            file.mData?.let { data ->
                FileCopyHelper.checkTotalSize(File(data)).let {
                    mTotalFileLength += it
                }
            }
        }
        Log.d(TAG, "exceptionDetection: totalLength:$mTotalFileLength")
        if (mTotalFileLength < 0) {
            return ERROR_PATH_NULL
        }
        // Check the empty size of the dest storage
        val storageState = checkDestStorageSpace(mDestFile, mTotalFileLength)
        return if (storageState.first) {
            Log.d(TAG, "exceptionDetection: storage is not enough")
            if (!KtUtils.checkIsDfmPath(mDestFile.mData)) {
                Pair(ERROR_STORAGE_NOT_ENOUGH, storageState.second)
            } else {
                val deviceName = DFMManager.getDFSDeviceName() ?: KtConstants.DFM_MOUNT_PATH_SUFFIX
                Pair(
                    ERROR_DFS_STORAGE_COPY_NOT_ENOUGH,
                    mContext.getString(
                        com.filemanager.common.R.string.copy_failed_by_device_not_enough,
                        deviceName
                    )
                )
            }
        } else null
    }

    override fun workRun(): Boolean {
        mIsOperateDatabase = Utils.isOperateDatabase(mContext, mDestFile.mData)
        return super.workRun()
    }

    override fun onSkipDealFile(sourceFile: File) {
        mHasSkipFile = true
        JavaFileHelper.fileTotalSize(sourceFile).let {
            mDealFileListener.addProgress(it)
        }
    }

    public override fun onDealFile(sourceFile: File, destFile: File, isDirectory: Boolean) {
        if (isOtgOrSDCard && hasShowKeepConnectToast.not()) {
            hasShowKeepConnectToast = true
            notifyObserver(
                SHOW_TOAST,
                appContext.resources.getString(com.filemanager.common.R.string.toast_copying_message)
            )
        }
        FileCopyHelper.copyFile(sourceFile, destFile, isDirectory, mDealFileListener)
        if (mIsOperateDatabase) {
            mMediaScannerBatchAction.add(destFile.absolutePath)
        }
    }

    override fun onDealFileResume() {
        notifyObserver(
            SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                mContext.getString(com.filemanager.common.R.string.copy_file_dialog_title), false, mCurrentProgress
        ))
    }

    override fun onDealAllFilesEnd() {
        if (mIsOperateDatabase) {
            mMediaScannerBatchAction.flush()
        }
        if (mHasSuccessFile) {
            if (isOtgOrSDCard.not()) {
                notifyObserver(ACTION_DONE, mDestFile.mData)
            } else {
                notifyObserver(ACTION_DONE_FOR_EXTERNAL_PATH, mDestFile.mData)
            }
            val fileServiceAction = Injector.injectFactory<IFileService>()
            fileServiceAction?.syncOperate(OPERATE_TYPE_COPY, hashSetOf(mDestFile.mData))
        } else {
            notifyObserver(ACTION_FAILED, ERROR_COPY)
        }
        StatisticsUtils.onCommon(mContext, StatisticsUtils.COPY_MENU_PRESSED)
    }

    protected open fun onDealFileSuccess(sourceFile: File, destFile: File, isDirectory: Boolean) {
        val sourceExtension = FileTypeUtils.getExtension(sourceFile.absolutePath)
        val targetExtension = FileTypeUtils.getExtension(destFile.absolutePath)
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.COPY_FILE,
            mapOf(
                StatisticsUtils.SOURCE_FILE_PATH to sourceFile.absolutePath,
                StatisticsUtils.SOURCE_FILE_TYPE to
                        if (isDirectory) StatisticsUtils.DIRECTORY_FILE_TYPE else sourceExtension,
                StatisticsUtils.TARGET_FILE_TYPE to
                        if (isDirectory) StatisticsUtils.DIRECTORY_FILE_TYPE else targetExtension
            )
        )
        mHasSuccessFile = true
        val sourceFileBean = mOperateFiles.find { sourceFile.absolutePath == it.mData }
        val localType = sourceFileBean?.mLocalType ?: MimeTypeHelper.UNKNOWN_TYPE
        val mimeType = sourceFileBean?.mMimeType ?: ""
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.onCopyFile(sourceFile.absolutePath, destFile.absolutePath, localType, mimeType)
    }

    private fun onDealFileProgressChanged(progress: Long) {
        mCurrentProgress = if (mTotalFileLength <= 0) {
            MAX_PROGRESS
        } else {
            (progress * MAX_PROGRESS / mTotalFileLength).toInt()
        }
        if (isOtgOrSDCard) {
            mCurrentProgress =
                (mCurrentProgress.toFloat() * EXTERNAL_STORAGE_COPY_PROGRESS / MAX_PROGRESS).toInt()
        }
        notifyObserver(UPDATE_PROGRESS, mCurrentProgress)
    }
}