plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/sdkCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")

android {
    namespace "com.oplus.dropdrag"

    defaultConfig {
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation libs.androidx.recyclerview
}

OBuildConfig {
    standAloneSdk = true
    groupId = prop_archivesGroupName
    sdkArtifactId = prop_artifactId_dragDrpo
    sdkExecuteTask = "publishAllPublicationsToReleaseRepository"
    moduleDescription = "The function module for drag drop aar."
}

apply from: rootProject.file("exportedLibs/scripts/aarCompile.gradle")
apply from: rootProject.file("exportedLibs/scripts/mappingCollect.gradle")

mappingCollect {
    publishTaskName = "publishAllPublicationsToReleaseRepository"
    buildType = "release"
    currentPrebuilt libs.oplus.filemanager.dragDrop
}