/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CategoryFilesService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 15:39
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********        2022/7/21       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.os.RemoteException
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.fileservice.bean.FileResponseStatusCode
import com.oplus.fileservice.filelist.scanner.AlbumSetScanner
import com.oplus.fileservice.filelist.scanner.ApkFilesScanner
import com.oplus.fileservice.filelist.scanner.AudioFilesScanner
import com.oplus.fileservice.filelist.scanner.BaseScanner
import com.oplus.fileservice.filelist.scanner.CompressFileScanner
import com.oplus.fileservice.filelist.scanner.DocumentFilesScanner
import com.oplus.fileservice.filelist.scanner.FilePathScanner
import com.oplus.fileservice.filelist.scanner.RecentFileScanner
import com.oplus.fileservice.filelist.scanner.RecycleBinScanner
import com.oplus.fileservice.filelist.scanner.SuperAppScanner
import com.oplus.fileservice.filelist.utils.obtainCategoryItem
import com.filemanager.common.utils.HansFreezeManager
import com.oplus.fileservice.utils.WebFileConstant.DEFAULT_PAGE_NO
import com.oplus.fileservice.utils.WebFileConstant.DEFAULT_PAGE_SIZE
import com.oplus.fileservice.utils.WebFileHelper

class CategoryFilesService : BaseService() {
    companion object {
        const val TAG = "CategoryFilesService"
        private const val DEFAULT_ID = CategoryHelper.CATEGORY_IMAGE
        private const val DEFAULT_CATEGORY_NAME = ""
        private const val CATEGORY_ID = "categoryId"
        private const val CATEGORY_NAME = "categoryName"
        private const val DIR_DEPTH_DOWNLOAD = 2
    }

    private var mCategoryId = DEFAULT_ID
    private var mCategoryName = ""
    private var mFileListServiceHandler: FileListServiceHandler? = null
    private var mStartTime = 0L
    private var baseScanner: BaseScanner? = null

    private val superApp: ISuperApp? by lazy {
        Injector.injectFactory<ISuperApp>()
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "onCreate()")
        mStartTime = System.currentTimeMillis()
    }

    override fun onBind(intent: Intent): IBinder? {
        Log.d(TAG, "onBind() getAction = ${intent.action} , getExtras =${intent.extras}")
        HansFreezeManager.instance.keepBackgroundRunning()
        val extras = intent.extras
        extras?.apply {
            mCategoryId = getInt(CATEGORY_ID, DEFAULT_ID)
            mPageNo = getInt(PAGE_NO, DEFAULT_PAGE_NO)
            mPageSize = getInt(PAGE_SIZE, DEFAULT_PAGE_SIZE)
            mSortOrder = getInt(SORT_ORDER, SortHelper.FILE_NAME_ORDER)
            mCategoryName = getString(CATEGORY_NAME, DEFAULT_CATEGORY_NAME)
            isNeedThumbnail = getBoolean(NEED_THUMBNAIL, DEFAULT_NEED_THUMBNAIL)
        }
        Log.d(TAG, "mCategoryId =$mCategoryId, categoryName=$mCategoryName, mPageNo = $mPageNo, mPageSize = $mPageSize, " +
                    "mSortOrder =$mSortOrder isNeedThumbnail=$isNeedThumbnail")
        var bundle: Bundle? = null
        if (mCategoryId == CategoryHelper.CATEGORY_RECYCLE_BIN) {
            if (!WebFileHelper.recycleSortOrderVerify(mSortOrder)) {
                bundle = getFileSortErrorBundle()
            }
        } else {
            if (!WebFileHelper.sortOrderVerify(mSortOrder)) {
                bundle = getFileSortErrorBundle()
            }
        }

        if (bundle != null) {
            return Messenger(Handler(
                Looper.getMainLooper()
            ) { msg: Message ->
                val replyMsg = Message.obtain()
                replyMsg.data = bundle
                try {
                    msg.replyTo.send(replyMsg)
                } catch (e: RemoteException) {
                    e.printStackTrace()
                }
                true
            }).binder
        }
        mFileListServiceHandler = FileListServiceHandler(getScanner(mCategoryId))
        return Messenger(mFileListServiceHandler).binder
    }

    private fun getFileSortErrorBundle(): Bundle {
        val bundle = Bundle()
        val result = FileResponseStatusCode.getSortInputErrorResponseData()
        bundle.putString("data", mGson.toJson(result))
        return bundle
    }

    private fun getScanner(categoryId: Int): BaseScanner? {
        when (categoryId) {
            CategoryHelper.CATEGORY_DOC -> baseScanner = DocumentFilesScanner(mPageNo, mPageSize, mSortOrder)
            CategoryHelper.CATEGORY_IMAGE -> baseScanner = AlbumSetScanner(mPageNo, mPageSize, isNeedThumbnail)
            CategoryHelper.CATEGORY_VIDEO, CategoryHelper.CATEGORY_AUDIO -> {
                baseScanner = AudioFilesScanner(mPageNo, mPageSize, mSortOrder, categoryType = categoryId, isNeedThumbnail)
            }

            CategoryHelper.CATEGORY_APK -> baseScanner = ApkFilesScanner(mPageNo, mPageSize, mSortOrder, isNeedThumbnail)
            CategoryHelper.CATEGORY_COMPRESS -> baseScanner = CompressFileScanner(mPageNo, mPageSize, mSortOrder)
            CategoryHelper.CATEGORY_FILL_RECENTLY -> baseScanner = RecentFileScanner(mPageNo, mPageSize, isNeedThumbnail)
            CategoryHelper.CATEGORY_FILE_ALL -> baseScanner =
                FilePathScanner(mInternalPath!!, mSortOrder, mPageNo, mPageSize, isNeedThumbnail)
            CategoryHelper.CATEGORY_RECYCLE_BIN -> baseScanner = RecycleBinScanner(mPageNo, mPageSize, mSortOrder, isNeedThumbnail)
            else -> {
                val superApp = superApp?.getCategoryItems(appContext)
                superApp?.let {
                    val categoryItem = obtainCategoryItem(superApp, categoryId, mCategoryName)
                    Log.d(TAG, "getScanner -> obtainSuperScanner -> categoryItem = $categoryItem")
                    categoryItem?.let {
                        val dirDepth = when (it.itemType) {
                            CategoryHelper.CATEGORY_DOWNLOAD -> DIR_DEPTH_DOWNLOAD
                            else -> SuperAppScanner.DEFAULT_DIR_DEPTH
                        }
                        baseScanner = SuperAppScanner(
                            categoryItem.fileList,
                            sortOrder = mSortOrder,
                            pageNo = mPageNo,
                            pageSize = mPageSize,
                            dirDepth = dirDepth,
                            isNeedThumbnail = isNeedThumbnail
                        )
                    }
                }
            }
        }
        return baseScanner
    }

    override fun onUnbind(intent: Intent?): Boolean {
        mFileListServiceHandler = null
        Log.d(TAG, "onUnbind() RunningTime = ${System.currentTimeMillis() - mStartTime}")
        HansFreezeManager.instance.cancelFrozenDelay()
        return false
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy()")
        baseScanner?.destroy()
    }
}