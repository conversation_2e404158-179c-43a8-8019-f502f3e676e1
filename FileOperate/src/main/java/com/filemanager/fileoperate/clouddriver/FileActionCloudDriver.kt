/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileActionCloudDriver.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.clouddriver

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.share.SendTask
import com.oplus.filemanager.interfaze.clouddrive.ICloudDrive
import java.lang.ref.WeakReference


class FileActionCloudDriver(
    lifecycle: LifecycleOwner,
    files: List<BaseFileBean>,
    context: Context
) : BaseFileAction<FileCloudDriverObserver>(lifecycle) {

    private val mOperateFile = ArrayList<BaseFileBean>()
    private var mWeakContext: WeakReference<Context>

    init {
        mOperateFile.addAll(files)
        mWeakContext = WeakReference(context)
    }

    override fun run(): Boolean {
        if (mWeakContext.get()?.let {
                KtAppUtils.checkAppEnabledWithDialog(
                    it,
                    Injector.injectFactory<ICloudDrive>()?.getCloudPackageName(it) ?: "",
                    com.filemanager.common.R.string.cloud_service_disable_message
                )
            } != true
        ) return false
        SendTask(mWeakContext, this, true).send(mOperateFile, null)
        mWeakContext.clear()
        return true
    }

    override fun onCancelled() {
        mWeakContext.clear()
        super.onCancelled()
    }
}