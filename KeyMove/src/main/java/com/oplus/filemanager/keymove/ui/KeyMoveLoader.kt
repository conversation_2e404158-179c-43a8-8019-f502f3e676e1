/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved.
 * * File: KeyMoveLoader
 * * Description: the loader for a key to move
 * * Version: 1.0
 * * Date : 2020/7/14
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/7/14       1.0         the loader for a key to move
 ****************************************************************/
package com.oplus.filemanager.keymove.ui

import android.content.Context
import android.database.Cursor
import android.provider.MediaStore
import com.oplus.filemanager.keymove.ui.model.ItemChild
import com.filemanager.common.base.FileTaskLoader
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper

class KeyMoveLoader(context: Context) : FileTaskLoader<KeyMoveLoader.KeyMoveLoadResult>(context) {
    companion object {
        private const val TAG = "KeyMoveLoader"
        const val IN_ONE_MONTH = 0
        const val BETWEEN_ONE_TO_THREE_MONTH = 1
        const val BETWEEN_THREE_TO_SIX_MONTH = 2
        const val BEFORE_SIX_MONTH = 3
        private const val MILLISECOND = 1000L
        private const val ONE_MONTH_TIME = 30 * 24 * 60 * 60 * MILLISECOND
        private const val THREE_MONTHS_TIME = 3 * ONE_MONTH_TIME
        private const val SIX_MONTHS_TIME = 2 * THREE_MONTHS_TIME
    }

    private var mLoadResult: KeyMoveLoadResult? = null
    private var mInternalPath = VolumeEnvironment.getInternalSdPath(context)

    override fun loadInBackground(): KeyMoveLoadResult? {
        return KeyMoveLoadResult(loadAlbums(),
                loadMedia(MimeTypeHelper.VIDEO_TYPE),
                loadMedia(MimeTypeHelper.AUDIO_TYPE),
                loadMedia(sql = getDocSql()),
                loadMedia(sql = getApkSql()))
    }

    override fun onStartLoading() {
        if ((mLoadResult?.getResultSize() ?: 0) > 0) {
            deliverResult(mLoadResult)
        }
        if (takeContentChanged() || (mLoadResult == null) || (mLoadResult?.getResultSize() == 0)) {
            forceLoad()
        }
    }

    override fun onStopLoading() {
        cancelLoad()
    }

    override fun forceLoad() {
        try {
            super.forceLoad()
        } catch (e: Exception) {
            Log.w(TAG, "forceLoad " + e.message)
        }

    }

    override fun deliverResult(data: KeyMoveLoadResult?) {
        if (mReset) {
            mLoadResult = null
            return
        }
        mLoadResult = data
        if (mStarted) {
            super.deliverResult(data)
        }
    }

    /**
     * attention to FileColumns.DATA may useless in the future
     */
    private fun loadAlbums(): HashMap<Int, ArrayList<ItemChild>> {
        val albums = HashMap<Int, ArrayList<ItemChild>>()
        val albumsBeforeSixMonths = ArrayList<ItemChild>()
        val albumsBetweenThreeToSixMonths = ArrayList<ItemChild>()
        val albumsBetweenOneToThreeMonths = ArrayList<ItemChild>()
        val albumsInOneMonth = ArrayList<ItemChild>()
        var cursor: Cursor? = null

        try {
            val currentTime = System.currentTimeMillis()
            cursor = context.contentResolver.query(MediaHelper.IMAGES_MEDIA_URI,
                    arrayOf(MediaStore.Files.FileColumns.DATA, MediaStore.Files.FileColumns.DATE_MODIFIED),
                    "_data LIKE '$mInternalPath%'",
                    null,
                    MediaStore.Files.FileColumns.DATE_MODIFIED + " ASC")
            if (null != cursor) {
                while (cursor.moveToNext()) {
                    val path: String = cursor.getString(0)
                    val time: Long = cursor.getLong(1) * MILLISECOND
                    val file = PathFileWrapper(path)
                    val reduceTime = currentTime - time
                    when {
                        reduceTime <= ONE_MONTH_TIME -> {
                            albumsInOneMonth.add(ItemChild(file))
                        }
                        reduceTime <= THREE_MONTHS_TIME -> {
                            albumsBetweenOneToThreeMonths.add(ItemChild(file))
                        }
                        reduceTime <= SIX_MONTHS_TIME -> {
                            albumsBetweenThreeToSixMonths.add(ItemChild(file))
                        }
                        else -> {
                            albumsBeforeSixMonths.add(ItemChild(file))
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "loadAlbums: ${e.message}")
        } finally {
            cursor?.close()
            albums[BEFORE_SIX_MONTH] = albumsBeforeSixMonths
            albums[BETWEEN_THREE_TO_SIX_MONTH] = albumsBetweenThreeToSixMonths
            albums[BETWEEN_ONE_TO_THREE_MONTH] = albumsBetweenOneToThreeMonths
            albums[IN_ONE_MONTH] = albumsInOneMonth
        }
        return albums
    }

    /**
     * attention to FileColumns.DATA may be useless in the future
     */
    private fun loadMedia(fileType: Int? = null, sql: String? = null): ArrayList<ItemChild> {
        val uri = when (fileType) {
            MimeTypeHelper.VIDEO_TYPE -> MediaHelper.VIDEO_MEDIA_URI
            MimeTypeHelper.AUDIO_TYPE -> MediaHelper.AUDIO_MEDIA_URI
            else -> MediaHelper.FILE_URI
        }
        val files = ArrayList<ItemChild>()
        var cursor: Cursor? = null
        try {
            cursor = if (sql == null) {
                context.contentResolver.query(uri,
                        arrayOf(MediaStore.Files.FileColumns.DATA),
                        "_data LIKE '$mInternalPath%'",
                        null,
                        "_display_name ASC")
            } else {
                context.contentResolver.query(uri,
                        arrayOf(MediaStore.Files.FileColumns.DATA),
                        sql,
                        null,
                        "_display_name ASC")
            }
            if (null != cursor && cursor.moveToFirst()) {
                val count = cursor.count
                Log.d(TAG, "loadMedia cur Count = $count")
                for (i in 0 until count) {
                    val path = cursor.getString(0)
                    if (null == path) {
                        cursor.moveToNext()
                        continue
                    }
                    val file = PathFileWrapper(path)
                    files.add(ItemChild(file))
                    cursor.moveToNext()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "loadMedia : ${e.message}")
        } finally {
            cursor?.close()
        }
        Log.d(TAG, "loadMedia files Count = " + files.size)
        return files
    }

    private fun getDocSql(): String {
        val docList = ArrayList<String?>(MimeTypeHelper.CATEGORY_DOC.toMutableList())
        val sql = StringBuilder("")
                .append(MediaStoreCompat.getMediaStoreSqlQuery(CategoryHelper.CATEGORY_DOC, docList))
                .append(" AND (")
                .append("_data LIKE '$mInternalPath%'")
                .append(")")
        Log.d(TAG, "getDocSql sql.toString() = $sql")
        return sql.toString()
    }

    private fun getApkSql(): String {
        val sql = StringBuilder("")
                .append(MediaStoreCompat.getMediaStoreSqlQuery(CategoryHelper.CATEGORY_APK, ArrayList()))
                .append(" AND (")
                .append("_data LIKE '$mInternalPath%'")
                .append(")")
        Log.d(TAG, "getApkSql sql.toString() = $sql")
        return sql.toString()
    }

    data class KeyMoveLoadResult(
            val mAlbums: HashMap<Int, ArrayList<ItemChild>>,
            val mVideos: ArrayList<ItemChild>,
            val mMusics: ArrayList<ItemChild>,
            val mDocuments: ArrayList<ItemChild>,
            val mApplications: ArrayList<ItemChild>
    ) {
        fun getResultSize(): Int {
            var albumSize = 0
            for (album in mAlbums) {
                albumSize += album.value.size
            }
            return albumSize.plus(mVideos.size)
                    .plus(mApplications.size)
                    .plus(mMusics.size)
                    .plus(mDocuments.size)
        }
    }
}