/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Rules
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/15
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/15      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.util

import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Md5Utils
import java.io.File

private const val FOLDER_NAME = "recent_card_widget"
private const val THUMBNAIL_SUFFIX = ".png"

/**
 * /data/user/0/com.xxx.filemanager/files/recent_card_widget
 * **/
var FOLDER = appContext.filesDir.absolutePath + File.separator + FOLDER_NAME

fun ruleForThumbnailFileName(path: String, modifiedDate: Long, size: Long): String {
    val sb = StringBuffer()
    sb.append(FOLDER).append(File.separator)
        .append(Md5Utils.toKey("${path}_${modifiedDate}_$size")).append(THUMBNAIL_SUFFIX)
    return sb.toString().also {
        Log.d("ruleForThumbnailFileName", "path=$path, result=$it")
    }
}

/**
 * 负一屏卡片中，是否支持显示缩略图，包括图片，视频，音频，安装包。
 * 因WPS的预览图加载，失败率高，标签卡片需求发布时移除此类型判断：
 * ((WpsManager.isSupportWpsThumbnail && MimeTypeHelper.isDocType(type)))
 */
fun isSupportThumbnailForCardWidget(type: Int): Boolean {
    return type == MimeTypeHelper.IMAGE_TYPE || type == MimeTypeHelper.VIDEO_TYPE
            || type == MimeTypeHelper.AUDIO_TYPE || type == MimeTypeHelper.APPLICATION_TYPE
}