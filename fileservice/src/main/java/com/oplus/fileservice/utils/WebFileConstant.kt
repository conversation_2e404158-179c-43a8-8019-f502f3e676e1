/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WebFileConstant
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/5 16:31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/5       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.utils

object WebFileConstant {
    const val DEFAULT_PAGE_NO = 1
    const val DEFAULT_PAGE_SIZE = 50

    /**
     * When the Page Size is 1000, it means that all file information of the specified directory is requested
     */
    const val DIR_ALL_FILES_PAGE_SIZE = 1000
    const val ONE_THOUSAND = 1000
    const val WAIT_TIME_OUT = 10000L
    const val RESULT_MAX_SIZE = 1024 * 1024
    const val WEB_DOWNLOAD_PATH = "/file/download?path="
    const val WEB_THUMBNAIL_DIRECTORY = "WebThumbnail"
    const val DATA = "data"
}