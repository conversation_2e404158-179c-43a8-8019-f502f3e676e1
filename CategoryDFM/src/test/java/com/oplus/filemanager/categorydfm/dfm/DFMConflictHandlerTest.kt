/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DFMConflictHandlerTest
 ** Description : DFM P2P Conflict handler Unit Test
 ** Version     : 1.0
 ** Date        : 2024/04/09 10:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/04/09       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.categorydfm.dfm

import android.content.DialogInterface
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import com.oplus.filemanager.categorydfm.utils.DFMConflictDialogFactory
import com.oplus.filemanager.dfm.DFMManager
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class DFMConflictHandlerTest {

    private lateinit var activity: AppCompatActivity
    private lateinit var handler: Handler
    private lateinit var conflictHandler: DFMConflictHandler
    private lateinit var looper: Looper

    @Before
    fun setup() {
        activity = mockk()
        handler = mockk()
        conflictHandler = mockk()
        every { conflictHandler.activity }.returns(activity)
        every { conflictHandler.handler }.returns(handler)
        every { conflictHandler.interruptDialogListener }.answers { callOriginal() }
        val lifecycle = mockk<Lifecycle>()
        justRun { lifecycle.addObserver(any()) }
        every { activity.lifecycle }.returns(lifecycle)
        mockkStatic(DFMManager::class)
        mockkStatic(Looper::class)
        looper = mockk()
        every { Looper.getMainLooper() }.returns(looper)
    }

    @After
    fun teardown() {
        unmockkStatic(DFMManager::class)
        unmockkStatic(Looper::class)
    }

    @Test
    fun should_call_openConnect_when_click_dialog() {
        val handler = DFMConflictHandler(activity)
        justRun { DFMManager.openConnect() }

        Assert.assertNotNull(handler.handler)

        handler.interruptDialogListener.onClick(mockk(), DialogInterface.BUTTON_NEUTRAL)
        verify(inverse = true) { DFMManager.openConnect() }

        handler.interruptDialogListener.onClick(mockk(), DialogInterface.BUTTON_NEGATIVE)
        verify(inverse = true) { DFMManager.openConnect() }


        handler.interruptDialogListener.onClick(mockk(), DialogInterface.BUTTON_POSITIVE)
        verify { DFMManager.openConnect() }
    }

    @Test
    fun should_call_register_when_onCreate() {
        val handler = DFMConflictHandler(activity)
        justRun { DFMManager.registerConflictListener(any()) }
        handler.onCreate(mockk())
        verify { DFMManager.registerConflictListener(handler) }
    }

    @Test
    fun should_call_onConnectConflict_when_accept_connect_conflict() {
        every { conflictHandler.accept(any(), any()) }.answers { callOriginal() }
        every { looper.thread }.returns(Thread.currentThread())
        every { conflictHandler.runOnUIThread(any()) }.answers { callOriginal() }
        justRun { conflictHandler.onConnectConflict(any()) }

        conflictHandler.accept(DFMManager.UNDEFINED_TYPE, 10)
        verify(inverse = true) { conflictHandler.onConnectConflict(10) }

        conflictHandler.accept(DFMManager.ACTION_CONNECT_CONFLICT, 10)
        verify { conflictHandler.onConnectConflict(10) }
    }

    @Test
    fun should_call_onP2PUsed_when_accept_p2p_used() {
        every { conflictHandler.accept(any(), any()) }.answers { callOriginal() }
        every { looper.thread }.returns(Thread.currentThread())
        every { conflictHandler.runOnUIThread(any()) }.answers { callOriginal() }
        justRun { conflictHandler.onP2PUsed() }

        conflictHandler.accept(DFMManager.UNDEFINED_TYPE, 10)
        verify(inverse = true) { conflictHandler.onP2PUsed() }

        conflictHandler.accept(DFMManager.ACTION_P2P_USED, 10)
        verify { conflictHandler.onP2PUsed() }
    }

    private fun test_onConnectConflict_with_mobile_type(conflict: Int, inverse: Boolean = false) {
        mockkStatic(DFMConflictDialogFactory::class)
        every { DFMConflictDialogFactory.showMobileScreencastInterruptDialog(any(), any()) }.returns(mockk())
        every { conflictHandler.onConnectConflict(any()) }.answers { callOriginal() }

        conflictHandler.onConnectConflict(conflict)
        verify(inverse = inverse) { DFMConflictDialogFactory.showMobileScreencastInterruptDialog(any(), any()) }

        unmockkStatic(DFMConflictDialogFactory::class)
    }

    @Test
    fun should_showMobileScreencastInterruptDialog_when_onConnectConflict_with_MOBILE_SELF_STUDY_CONFLICT() {
        test_onConnectConflict_with_mobile_type(DFMP2PConflictType.MOBILE_SELF_STUDY_CONFLICT, false)
    }

    @Test
    fun should_showMobileScreencastInterruptDialog_when_onConnectConflict_with_MOBILE_WFD_CONFLICT() {
        test_onConnectConflict_with_mobile_type(DFMP2PConflictType.MOBILE_WFD_CONFLICT, false)
    }

    @Test
    fun should_showMobileScreencastInterruptDialog_when_onConnectConflict_with_MOBILE_GOOGLECAST_CONFLICT() {
        test_onConnectConflict_with_mobile_type(DFMP2PConflictType.MOBILE_GOOGLECAST_CONFLICT, false)
    }

    @Test
    fun should_showMobileScreencastInterruptDialog_when_onConnectConflict_with_MOBILE_HAPPYCAST_CONFLICT() {
        test_onConnectConflict_with_mobile_type(DFMP2PConflictType.MOBILE_HAPPYCAST_CONFLICT, false)
    }

    @Test
    fun should_showMobileScreencastInterruptDialog_when_onConnectConflict_with_MOBILE_DLNA_CONFLICT() {
        test_onConnectConflict_with_mobile_type(DFMP2PConflictType.MOBILE_DLNA_CONFLICT, false)
    }

    @Test
    fun should_showMobileScreencastInterruptDialog_when_onConnectConflict_other_type() {
        test_onConnectConflict_with_mobile_type(-10, true)
    }

    private fun test_onConnectConflict_with_pad_type(conflict: Int, inverse: Boolean = false) {
        mockkStatic(DFMConflictDialogFactory::class)
        every { DFMConflictDialogFactory.showWirelessScreencastInterruptDialog(any(), any()) }.returns(mockk())
        every { conflictHandler.onConnectConflict(any()) }.answers { callOriginal() }

        conflictHandler.onConnectConflict(conflict)
        verify(inverse = inverse) { DFMConflictDialogFactory.showWirelessScreencastInterruptDialog(any(), any()) }

        unmockkStatic(DFMConflictDialogFactory::class)
    }

    @Test
    fun should_showWirelessScreencastInterruptDialog_when_onConnectConflict_with_PAD_CROSS_SCREEN_MIRROR_CONFLICT() {
        test_onConnectConflict_with_pad_type(DFMP2PConflictType.PAD_CROSS_SCREEN_MIRROR_CONFLICT, false)
    }

    @Test
    fun should_showWirelessScreencastInterruptDialog_when_onConnectConflict_with_PAD_APP_REPLY_CONFLICT() {
        test_onConnectConflict_with_pad_type(DFMP2PConflictType.PAD_APP_REPLY_CONFLICT, false)
    }

    @Test
    fun should_showWirelessScreencastInterruptDialog_when_onConnectConflict_other_type() {
        test_onConnectConflict_with_pad_type(-10, true)
    }

    private fun test_onConnectConflict_with_pc_type(conflict: Int, inverse: Boolean = false) {
        mockkStatic(DFMConflictDialogFactory::class)
        every { DFMConflictDialogFactory.showCrossScreenMirrorInterruptDialog(any(), any()) }.returns(mockk())
        every { conflictHandler.onConnectConflict(any()) }.answers { callOriginal() }

        conflictHandler.onConnectConflict(conflict)
        verify(inverse = inverse) { DFMConflictDialogFactory.showCrossScreenMirrorInterruptDialog(any(), any()) }

        unmockkStatic(DFMConflictDialogFactory::class)
    }

    @Test
    fun should_showCrossScreenMirrorInterruptDialog_when_onConnectConflict_with_PC_ONE_APP_REPLY_CONFLICT() {
        test_onConnectConflict_with_pc_type(DFMP2PConflictType.PC_ONE_APP_REPLY_CONFLICT, false)
    }

    @Test
    fun should_showCrossScreenMirrorInterruptDialog_when_onConnectConflict_with_PC_ONE_APP_REPLY_ONE_CONFLICT() {
        test_onConnectConflict_with_pc_type(DFMP2PConflictType.PC_ONE_APP_REPLY_ONE_CONFLICT, false)
    }

    @Test
    fun should_showCrossScreenMirrorInterruptDialog_when_onConnectConflict_with_PC_ONE_APP_REPLY_TWO_CONFLICT() {
        test_onConnectConflict_with_pc_type(DFMP2PConflictType.PC_ONE_APP_REPLY_TWO_CONFLICT, false)
    }

    @Test
    fun should_showCrossScreenMirrorInterruptDialog_when_onConnectConflict_with_PC_TWO_APP_REPLY_ONE_CONFLICT() {
        test_onConnectConflict_with_pc_type(DFMP2PConflictType.PC_TWO_APP_REPLY_ONE_CONFLICT, false)
    }

    @Test
    fun should_showCrossScreenMirrorInterruptDialog_when_onConnectConflict_with_PC_TWO_APP_REPLY_TWO_CONFLICT() {
        test_onConnectConflict_with_pc_type(DFMP2PConflictType.PC_TWO_APP_REPLY_TWO_CONFLICT, false)
    }

    @Test
    fun should_showCrossScreenMirrorInterruptDialog_when_onConnectConflict_with_PC_THREE_APP_REPLY_ONE_CONFLICT() {
        test_onConnectConflict_with_pc_type(DFMP2PConflictType.PC_THREE_APP_REPLY_ONE_CONFLICT, false)
    }

    @Test
    fun should_showCrossScreenMirrorInterruptDialog_when_onConnectConflict_other_type() {
        test_onConnectConflict_with_pc_type(-10, true)
    }

    @Test
    fun should_showTransferChannelOccupiedDialog_when_onP2PUsed() {
        mockkStatic(DFMConflictDialogFactory::class)
        every { DFMConflictDialogFactory.showTransferChannelOccupiedDialog(any(), any()) }.returns(mockk())
        every { conflictHandler.onP2PUsed() }.answers { callOriginal() }

        conflictHandler.onP2PUsed()
        verify { DFMConflictDialogFactory.showTransferChannelOccupiedDialog(any(), any()) }

        unmockkStatic(DFMConflictDialogFactory::class)
    }

    @Test
    fun should_run_when_runOnUIThread() {
        every { conflictHandler.runOnUIThread(any()) }.answers { callOriginal() }
        every { looper.thread }.returns(Thread.currentThread())
        val runnable = mockk<Runnable>()
        justRun { runnable.run() }
        conflictHandler.runOnUIThread(runnable)
        verify { runnable.run() }
    }

    @Test
    fun should_call_post_when_runOnUIThread_bgThread() {
        every { conflictHandler.runOnUIThread(any()) }.answers { callOriginal() }
        every { looper.thread }.returns(Thread())
        every { handler.post(any()) }.returns(true)
        val runnable = mockk<Runnable>()
        justRun { runnable.run() }
        conflictHandler.runOnUIThread(runnable)
        verify { handler.post(runnable) }
    }

    @Test
    fun should_call_unregister_when_onDestroy() {
        every { conflictHandler.onDestroy(any()) }.answers { callOriginal() }
        every { DFMManager.unregisterConflictListener(any()) }.answers { callOriginal() }
        conflictHandler.onDestroy(mockk())
        verify { DFMManager.unregisterConflictListener(conflictHandler) }
    }
}