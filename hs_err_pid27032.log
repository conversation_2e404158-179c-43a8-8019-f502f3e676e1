#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 230686720 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3550), pid=27032, tid=22324
#
# JRE version: Java(TM) SE Runtime Environment (17.0.8+9) (build 17.0.8+9-LTS-211)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\Oaps\OapsLib\build\20250901_11004799697631593167.compiler.options

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Sep  1 12:32:52 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 6.563906 seconds (0d 0h 0m 6s)

---------------  T H R E A D  ---------------

Current thread (0x0000016b4a00be60):  VMThread "VM Thread" [stack: 0x000000273fa00000,0x000000273fb00000] [id=22324]

Stack: [0x000000273fa00000,0x000000273fb00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x677d0a]
V  [jvm.dll+0x7d8c54]
V  [jvm.dll+0x7da3fe]
V  [jvm.dll+0x7daa63]
V  [jvm.dll+0x245c5f]
V  [jvm.dll+0x674bb9]
V  [jvm.dll+0x6694f2]
V  [jvm.dll+0x3031d6]
V  [jvm.dll+0x30a756]
V  [jvm.dll+0x359f9e]
V  [jvm.dll+0x35a1cf]
V  [jvm.dll+0x2da3e8]
V  [jvm.dll+0x2d87f5]
V  [jvm.dll+0x2d7dfc]
V  [jvm.dll+0x31b4cb]
V  [jvm.dll+0x7df26b]
V  [jvm.dll+0x7dffa4]
V  [jvm.dll+0x7e04bd]
V  [jvm.dll+0x7e0894]
V  [jvm.dll+0x7e0960]
V  [jvm.dll+0x788bba]
V  [jvm.dll+0x676b35]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]

VM_Operation (0x000000273f4fa180): G1CollectForAllocation, mode: safepoint, requested by thread 0x0000016b1d8b27f0


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000016b94031ac0, length=18, elements={
0x0000016b1d8b27f0, 0x0000016b4a012230, 0x0000016b4a012fb0, 0x0000016b4a026390,
0x0000016b4a026c50, 0x0000016b4a027510, 0x0000016b4a028de0, 0x0000016b4a034ee0,
0x0000016b4a039f70, 0x0000016b4a046060, 0x0000016b4a94f600, 0x0000016b4a95ff80,
0x0000016b4a964610, 0x0000016b4abd7ce0, 0x0000016b918d8960, 0x0000016b92394820,
0x0000016b90e63060, 0x0000016b91ac9d40
}

Java Threads: ( => current thread )
  0x0000016b1d8b27f0 JavaThread "main" [_thread_blocked, id=14532, stack(0x000000273f400000,0x000000273f500000)]
  0x0000016b4a012230 JavaThread "Reference Handler" daemon [_thread_blocked, id=6228, stack(0x000000273fb00000,0x000000273fc00000)]
  0x0000016b4a012fb0 JavaThread "Finalizer" daemon [_thread_blocked, id=28920, stack(0x000000273fc00000,0x000000273fd00000)]
  0x0000016b4a026390 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=17204, stack(0x000000273fd00000,0x000000273fe00000)]
  0x0000016b4a026c50 JavaThread "Attach Listener" daemon [_thread_blocked, id=25932, stack(0x000000273fe00000,0x000000273ff00000)]
  0x0000016b4a027510 JavaThread "Service Thread" daemon [_thread_blocked, id=8400, stack(0x000000273ff00000,0x0000002740000000)]
  0x0000016b4a028de0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=15136, stack(0x0000002740000000,0x0000002740100000)]
  0x0000016b4a034ee0 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=25812, stack(0x0000002740100000,0x0000002740200000)]
  0x0000016b4a039f70 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=12896, stack(0x0000002740200000,0x0000002740300000)]
  0x0000016b4a046060 JavaThread "Sweeper thread" daemon [_thread_blocked, id=1708, stack(0x0000002740300000,0x0000002740400000)]
  0x0000016b4a94f600 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=16068, stack(0x0000002740400000,0x0000002740500000)]
  0x0000016b4a95ff80 JavaThread "Notification Thread" daemon [_thread_blocked, id=20772, stack(0x0000002740500000,0x0000002740600000)]
  0x0000016b4a964610 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=21804, stack(0x0000002740700000,0x0000002740800000)]
  0x0000016b4abd7ce0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=20848, stack(0x0000002740900000,0x0000002740a00000)]
  0x0000016b918d8960 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=18800, stack(0x0000002741500000,0x0000002741600000)]
  0x0000016b92394820 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=22864, stack(0x0000002741b00000,0x0000002741c00000)]
  0x0000016b90e63060 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=14820, stack(0x0000002741c00000,0x0000002741d00000)]
  0x0000016b91ac9d40 JavaThread "C2 CompilerThread3" daemon [_thread_blocked, id=29020, stack(0x0000002741d00000,0x0000002741e00000)]

Other Threads:
=>0x0000016b4a00be60 VMThread "VM Thread" [stack: 0x000000273fa00000,0x000000273fb00000] [id=22324]
  0x0000016b49ffa9b0 WatcherThread [stack: 0x0000002740600000,0x0000002740700000] [id=8440]
  0x0000016b1d9613e0 GCTaskThread "GC Thread#0" [stack: 0x000000273f500000,0x000000273f600000] [id=29688]
  0x0000016b90360030 GCTaskThread "GC Thread#1" [stack: 0x0000002740a00000,0x0000002740b00000] [id=15456]
  0x0000016b903602e0 GCTaskThread "GC Thread#2" [stack: 0x0000002740b00000,0x0000002740c00000] [id=8224]
  0x0000016b90360590 GCTaskThread "GC Thread#3" [stack: 0x0000002740c00000,0x0000002740d00000] [id=2888]
  0x0000016b90360840 GCTaskThread "GC Thread#4" [stack: 0x0000002740d00000,0x0000002740e00000] [id=3364]
  0x0000016b90360af0 GCTaskThread "GC Thread#5" [stack: 0x0000002740e00000,0x0000002740f00000] [id=24512]
  0x0000016b90360da0 GCTaskThread "GC Thread#6" [stack: 0x0000002740f00000,0x0000002741000000] [id=20920]
  0x0000016b90327020 GCTaskThread "GC Thread#7" [stack: 0x0000002741000000,0x0000002741100000] [id=29420]
  0x0000016b903272d0 GCTaskThread "GC Thread#8" [stack: 0x0000002741100000,0x0000002741200000] [id=17496]
  0x0000016b90327580 GCTaskThread "GC Thread#9" [stack: 0x0000002741200000,0x0000002741300000] [id=27244]
  0x0000016b90327830 GCTaskThread "GC Thread#10" [stack: 0x0000002741300000,0x0000002741400000] [id=22196]
  0x0000016b90384a90 GCTaskThread "GC Thread#11" [stack: 0x0000002741400000,0x0000002741500000] [id=3036]
  0x0000016b91523650 GCTaskThread "GC Thread#12" [stack: 0x0000002740800000,0x0000002740900000] [id=4952]
  0x0000016b1d973000 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000273f600000,0x000000273f700000] [id=16268]
  0x0000016b1d9740c0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000273f700000,0x000000273f800000] [id=27520]
  0x0000016b903837c0 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000002741600000,0x0000002741700000] [id=20996]
  0x0000016b90383fd0 ConcurrentGCThread "G1 Conc#2" [stack: 0x0000002741700000,0x0000002741800000] [id=9756]
  0x0000016b49f430b0 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000273f800000,0x000000273f900000] [id=6932]
  0x0000016b915c18d0 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000002741800000,0x0000002741900000] [id=7448]
  0x0000016b915c32b0 ConcurrentGCThread "G1 Refine#2" [stack: 0x0000002741900000,0x0000002741a00000] [id=2140]
  0x0000016b915c15f0 ConcurrentGCThread "G1 Refine#3" [stack: 0x0000002741a00000,0x0000002741b00000] [id=7000]
  0x0000016b49f43780 ConcurrentGCThread "G1 Service" [stack: 0x000000273f900000,0x000000273fa00000] [id=29032]

Threads with active compile tasks:
C2 CompilerThread0     6579 3238   !   4       java.util.zip.ZipFile::getInputStream (275 bytes)
C2 CompilerThread1     6579 3260       4       org.jetbrains.org.objectweb.asm.ClassReader::readElementValue (1237 bytes)
C2 CompilerThread2     6579 3188       4       org.jetbrains.org.objectweb.asm.ClassReader::readMethod (1061 bytes)
C2 CompilerThread3     6579 3211   !   4       org.jetbrains.kotlin.metadata.ProtoBuf$ValueParameter::<init> (513 bytes)

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000016b1d8afa40] Threads_lock - owner thread: 0x0000016b4a00be60
[0x0000016b1d8af020] Heap_lock - owner thread: 0x0000016b1d8b27f0

Heap address: 0x0000000604800000, size: 8120 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000016b4b000000-0x0000016b4bbd0000-0x0000016b4bbd0000), size 12386304, SharedBaseAddress: 0x0000016b4b000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000016b4c000000-0x0000016b8c000000, reserved size: 1073741824
Narrow klass base: 0x0000016b4b000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32479M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8120M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 69632K, used 28599K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 35977K, committed 36288K, reserved 1114112K
  class space    used 4417K, committed 4544K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%|HS|  |TAMS 0x0000000604c00000, 0x0000000604800000| Complete 
|   1|0x0000000604c00000, 0x0000000605000000, 0x0000000605000000|100%| O|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   2|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|   3|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%| O|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|   4|0x0000000605800000, 0x0000000605c00000, 0x0000000605c00000|100%| O|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|   5|0x0000000605c00000, 0x0000000606000000, 0x0000000606000000|100%| O|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|   6|0x0000000606000000, 0x0000000606236000, 0x0000000606400000| 55%| O|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|   7|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|   8|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|   9|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  10|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  11|0x0000000607400000, 0x00000006075b7c90, 0x0000000607800000| 42%| S|CS|TAMS 0x0000000607400000, 0x0000000607400000| Complete 
|  12|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  13|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
| 114|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 115|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000, 0x0000000621400000| Untracked 
| 126|0x0000000624000000, 0x0000000624000000, 0x0000000624400000|  0%| F|  |TAMS 0x0000000624000000, 0x0000000624000000| Untracked 

Card table byte_map: [0x0000016b359c0000,0x0000016b369a0000] _byte_map_base: 0x0000016b3299c000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000016b1d962950, (CMBitMap*) 0x0000016b1d962910
 Prev Bits: [0x0000016b3f860000, 0x0000016b47740000)
 Next Bits: [0x0000016b37980000, 0x0000016b3f860000)

Polling page: 0x0000016b1d090000

Metaspace:

Usage:
  Non-class:     30.82 MB used.
      Class:      4.31 MB used.
       Both:     35.13 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      31.00 MB ( 48%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.44 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      35.44 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  15.47 MB
       Class:  11.59 MB
        Both:  27.06 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.56 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 148.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 565.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 3.
num_chunks_taken_from_freelist: 1344.
num_chunk_merges: 3.
num_chunk_splits: 1128.
num_chunks_enlarged: 1042.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=1693Kb max_used=1693Kb free=117474Kb
 bounds [0x0000016b2cd00000, 0x0000016b2cf70000, 0x0000016b34160000]
CodeHeap 'profiled nmethods': size=119104Kb used=7126Kb max_used=7126Kb free=111977Kb
 bounds [0x0000016b25160000, 0x0000016b25860000, 0x0000016b2c5b0000]
CodeHeap 'non-nmethods': size=7488Kb used=2952Kb max_used=3033Kb free=4535Kb
 bounds [0x0000016b2c5b0000, 0x0000016b2c8b0000, 0x0000016b2cd00000]
 total_blobs=3859 nmethods=3282 adapters=486
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 5.888 Thread 0x0000016b4a94f600 3281       3       org.jetbrains.kotlin.protobuf.GeneratedMessageLite$ExtendableMessage::verifyExtensionContainingType (22 bytes)
Event: 5.889 Thread 0x0000016b4a94f600 nmethod 3281 0x0000016b25855110 code [0x0000016b258552e0, 0x0000016b258555f8]
Event: 5.889 Thread 0x0000016b918d8960 3282       1       org.jetbrains.kotlin.load.kotlin.header.KotlinClassHeader::getData (5 bytes)
Event: 5.889 Thread 0x0000016b4a94f600 3283       1       org.jetbrains.kotlin.load.kotlin.header.KotlinClassHeader::getStrings (5 bytes)
Event: 5.889 Thread 0x0000016b4a039f70 3284       1       org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes::getLocalNameList (5 bytes)
Event: 5.889 Thread 0x0000016b4abd7ce0 3285       1       org.jetbrains.kotlin.metadata.jvm.JvmProtoBuf$StringTableTypes::getRecordList (5 bytes)
Event: 5.889 Thread 0x0000016b918d8960 nmethod 3282 0x0000016b2cea5f10 code [0x0000016b2cea60a0, 0x0000016b2cea6178]
Event: 5.889 Thread 0x0000016b4a94f600 nmethod 3283 0x0000016b2cea6210 code [0x0000016b2cea63a0, 0x0000016b2cea6478]
Event: 5.889 Thread 0x0000016b4a039f70 nmethod 3284 0x0000016b2cea6510 code [0x0000016b2cea66a0, 0x0000016b2cea6778]
Event: 5.889 Thread 0x0000016b4abd7ce0 nmethod 3285 0x0000016b2cea6810 code [0x0000016b2cea69a0, 0x0000016b2cea6a78]
Event: 5.889 Thread 0x0000016b4a94f600 3286       1       org.jetbrains.kotlin.resolve.CompilerDeserializationConfiguration::getReportErrorsOnPreReleaseDependencies (5 bytes)
Event: 5.889 Thread 0x0000016b4a039f70 3287       1       org.jetbrains.kotlin.resolve.CompilerDeserializationConfiguration::getSkipPrereleaseCheck (5 bytes)
Event: 5.889 Thread 0x0000016b4abd7ce0 3288       3       org.jetbrains.kotlin.load.kotlin.header.KotlinClassHeader::isPreRelease (10 bytes)
Event: 5.889 Thread 0x0000016b4a039f70 nmethod 3287 0x0000016b2cea6b10 code [0x0000016b2cea6ca0, 0x0000016b2cea6d78]
Event: 5.889 Thread 0x0000016b4a94f600 nmethod 3286 0x0000016b2cea6e10 code [0x0000016b2cea6fa0, 0x0000016b2cea7078]
Event: 5.889 Thread 0x0000016b4a039f70 3289       1       org.jetbrains.kotlin.resolve.CompilerDeserializationConfiguration::getAllowUnstableDependencies (5 bytes)
Event: 5.889 Thread 0x0000016b4abd7ce0 nmethod 3288 0x0000016b25855710 code [0x0000016b258558a0, 0x0000016b25855a78]
Event: 5.889 Thread 0x0000016b4a039f70 nmethod 3289 0x0000016b2cea7110 code [0x0000016b2cea72a0, 0x0000016b2cea7378]
Event: 5.890 Thread 0x0000016b4abd7ce0 3290       1       org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedMemberScope$OptimizedImplementation::access$getFunctionProtosBytes$p (5 bytes)
Event: 5.890 Thread 0x0000016b4abd7ce0 nmethod 3290 0x0000016b2cea7410 code [0x0000016b2cea75a0, 0x0000016b2cea7658]

GC Heap History (15 events):
Event: 0.550 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 520192K, used 24576K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 6891K, committed 7040K, reserved 1114112K
  class space    used 714K, committed 768K, reserved 1048576K
}
Event: 0.582 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 6946K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 6891K, committed 7040K, reserved 1114112K
  class space    used 714K, committed 768K, reserved 1048576K
}
Event: 1.268 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 31522K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 15294K, committed 15488K, reserved 1114112K
  class space    used 2018K, committed 2112K, reserved 1048576K
}
Event: 1.271 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 9050K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 15294K, committed 15488K, reserved 1114112K
  class space    used 2018K, committed 2112K, reserved 1048576K
}
Event: 2.214 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 45914K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 21380K, committed 21504K, reserved 1114112K
  class space    used 2889K, committed 2944K, reserved 1048576K
}
Event: 2.217 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 520192K, used 14475K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 21380K, committed 21504K, reserved 1114112K
  class space    used 2889K, committed 2944K, reserved 1048576K
}
Event: 2.939 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 69632K, used 47243K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 10 young (40960K), 2 survivors (8192K)
 Metaspace       used 27265K, committed 27456K, reserved 1114112K
  class space    used 3449K, committed 3520K, reserved 1048576K
}
Event: 2.944 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 69632K, used 17819K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 27265K, committed 27456K, reserved 1114112K
  class space    used 3449K, committed 3520K, reserved 1048576K
}
Event: 3.062 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 69632K, used 21915K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 28426K, committed 28608K, reserved 1114112K
  class space    used 3576K, committed 3648K, reserved 1048576K
}
Event: 3.070 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 69632K, used 18354K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 28426K, committed 28608K, reserved 1114112K
  class space    used 3576K, committed 3648K, reserved 1048576K
}
Event: 4.726 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 69632K, used 38834K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 32952K, committed 33152K, reserved 1114112K
  class space    used 4103K, committed 4224K, reserved 1048576K
}
Event: 4.730 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 69632K, used 21486K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 32952K, committed 33152K, reserved 1114112K
  class space    used 4103K, committed 4224K, reserved 1048576K
}
Event: 5.417 GC heap before
{Heap before GC invocations=7 (full 0):
 garbage-first heap   total 69632K, used 41966K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 35775K, committed 36032K, reserved 1114112K
  class space    used 4414K, committed 4544K, reserved 1048576K
}
Event: 5.429 GC heap after
{Heap after GC invocations=8 (full 0):
 garbage-first heap   total 69632K, used 26717K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 35775K, committed 36032K, reserved 1114112K
  class space    used 4414K, committed 4544K, reserved 1048576K
}
Event: 5.890 GC heap before
{Heap before GC invocations=8 (full 0):
 garbage-first heap   total 69632K, used 47197K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 1 survivors (4096K)
 Metaspace       used 35977K, committed 36288K, reserved 1114112K
  class space    used 4417K, committed 4544K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 5.279 Thread 0x0000016b1d8b27f0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000016b2ce28b30 relative=0x00000000000007f0
Event: 5.279 Thread 0x0000016b1d8b27f0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000016b2ce28b30 method=org.jetbrains.org.objectweb.asm.ClassReader.readUTF8(I[C)Ljava/lang/String; @ 11 c2
Event: 5.279 Thread 0x0000016b1d8b27f0 DEOPT PACKING pc=0x0000016b2ce28b30 sp=0x000000273f4fb320
Event: 5.279 Thread 0x0000016b1d8b27f0 DEOPT UNPACKING pc=0x0000016b2c6023a3 sp=0x000000273f4fb308 mode 2
Event: 5.301 Thread 0x0000016b1d8b27f0 DEOPT PACKING pc=0x0000016b253241be sp=0x000000273f4fb070
Event: 5.301 Thread 0x0000016b1d8b27f0 DEOPT UNPACKING pc=0x0000016b2c602b43 sp=0x000000273f4fa4e8 mode 0
Event: 5.540 Thread 0x0000016b1d8b27f0 DEOPT PACKING pc=0x0000016b257d3363 sp=0x000000273f4fbc70
Event: 5.540 Thread 0x0000016b1d8b27f0 DEOPT UNPACKING pc=0x0000016b2c602b43 sp=0x000000273f4fb178 mode 0
Event: 5.549 Thread 0x0000016b1d8b27f0 DEOPT PACKING pc=0x0000016b257d3363 sp=0x000000273f4fbc70
Event: 5.549 Thread 0x0000016b1d8b27f0 DEOPT UNPACKING pc=0x0000016b2c602b43 sp=0x000000273f4fb178 mode 0
Event: 5.550 Thread 0x0000016b1d8b27f0 DEOPT PACKING pc=0x0000016b257d3363 sp=0x000000273f4fbc70
Event: 5.550 Thread 0x0000016b1d8b27f0 DEOPT UNPACKING pc=0x0000016b2c602b43 sp=0x000000273f4fb178 mode 0
Event: 5.810 Thread 0x0000016b1d8b27f0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000016b2cd8f4e0 relative=0x00000000000002a0
Event: 5.810 Thread 0x0000016b1d8b27f0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000016b2cd8f4e0 method=java.util.HashMap.getNode(Ljava/lang/Object;)Ljava/util/HashMap$Node; @ 128 c2
Event: 5.810 Thread 0x0000016b1d8b27f0 DEOPT PACKING pc=0x0000016b2cd8f4e0 sp=0x000000273f4fba00
Event: 5.810 Thread 0x0000016b1d8b27f0 DEOPT UNPACKING pc=0x0000016b2c6023a3 sp=0x000000273f4fb968 mode 2
Event: 5.810 Thread 0x0000016b1d8b27f0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000016b2cd8f4e0 relative=0x00000000000002a0
Event: 5.810 Thread 0x0000016b1d8b27f0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000016b2cd8f4e0 method=java.util.HashMap.getNode(Ljava/lang/Object;)Ljava/util/HashMap$Node; @ 128 c2
Event: 5.810 Thread 0x0000016b1d8b27f0 DEOPT PACKING pc=0x0000016b2cd8f4e0 sp=0x000000273f4fba00
Event: 5.810 Thread 0x0000016b1d8b27f0 DEOPT UNPACKING pc=0x0000016b2c6023a3 sp=0x000000273f4fb968 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.676 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623cb6508}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int)'> (0x0000000623cb6508) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.691 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623ded7f8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623ded7f8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.695 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623e16640}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623e16640) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.717 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623ef57e8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623ef57e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.834 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623a1b258}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623a1b258) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.918 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006235df338}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000006235df338) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.010 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062329b500}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x000000062329b500) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.019 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623316140}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000623316140) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.038 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233d1ba0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006233d1ba0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.964 Thread 0x0000016b1d8b27f0 Implicit null exception at 0x0000016b2cd28172 to 0x0000016b2cd287cc
Event: 2.076 Thread 0x0000016b1d8b27f0 Implicit null exception at 0x0000016b2cd274f2 to 0x0000016b2cd27b6c
Event: 2.121 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622691e48}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622691e48) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.080 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062405cde8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x000000062405cde8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.139 Thread 0x0000016b1d8b27f0 Implicit null exception at 0x0000016b2cde2071 to 0x0000016b2cde2214
Event: 3.179 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006243a25d8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006243a25d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.180 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006243a6c60}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006243a6c60) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.180 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006243aaad0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006243aaad0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.903 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006242a00e0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006242a00e0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.904 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006242a99d0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006242a99d0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.955 Thread 0x0000016b1d8b27f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006214032d0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006214032d0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 4.323 Executing VM operation: HandshakeAllThreads done
Event: 4.323 Executing VM operation: Cleanup
Event: 4.323 Executing VM operation: Cleanup done
Event: 4.576 Executing VM operation: HandshakeAllThreads
Event: 4.576 Executing VM operation: HandshakeAllThreads done
Event: 4.635 Executing VM operation: HandshakeAllThreads
Event: 4.635 Executing VM operation: HandshakeAllThreads done
Event: 4.725 Executing VM operation: G1CollectForAllocation
Event: 4.743 Executing VM operation: G1CollectForAllocation done
Event: 5.042 Executing VM operation: HandshakeAllThreads
Event: 5.043 Executing VM operation: HandshakeAllThreads done
Event: 5.143 Executing VM operation: HandshakeAllThreads
Event: 5.143 Executing VM operation: HandshakeAllThreads done
Event: 5.355 Executing VM operation: G1CollectForAllocation
Event: 5.431 Executing VM operation: G1CollectForAllocation done
Event: 5.827 Executing VM operation: HandshakeAllThreads
Event: 5.828 Executing VM operation: HandshakeAllThreads done
Event: 5.878 Executing VM operation: ICBufferFull
Event: 5.878 Executing VM operation: ICBufferFull done
Event: 5.890 Executing VM operation: G1CollectForAllocation

Events (20 events):
Event: 4.551 loading class java/util/stream/IntPipeline$1 done
Event: 4.554 loading class java/util/stream/IntPipeline$1$1
Event: 4.554 loading class java/util/stream/IntPipeline$1$1 done
Event: 4.581 loading class java/text/StringCharacterIterator
Event: 4.581 loading class java/text/CharacterIterator
Event: 4.581 loading class java/text/CharacterIterator done
Event: 4.581 loading class java/text/StringCharacterIterator done
Event: 4.614 Thread 0x0000016b91b108f0 Thread exited: 0x0000016b91b108f0
Event: 4.689 Thread 0x0000016b924e14a0 Thread exited: 0x0000016b924e14a0
Event: 4.905 loading class java/util/RegularEnumSet$EnumSetIterator
Event: 4.905 loading class java/util/RegularEnumSet$EnumSetIterator done
Event: 5.278 Thread 0x0000016b92394820 Thread added: 0x0000016b92394820
Event: 5.331 Thread 0x0000016b90e63060 Thread added: 0x0000016b90e63060
Event: 5.336 Thread 0x0000016b91ac9d40 Thread added: 0x0000016b91ac9d40
Event: 5.336 Thread 0x0000016b91aca250 Thread added: 0x0000016b91aca250
Event: 5.351 Thread 0x0000016b91c20730 Thread added: 0x0000016b91c20730
Event: 5.354 Thread 0x0000016b91c20c40 Thread added: 0x0000016b91c20c40
Event: 5.707 Thread 0x0000016b91c20c40 Thread exited: 0x0000016b91c20c40
Event: 5.707 Thread 0x0000016b91c20730 Thread exited: 0x0000016b91c20730
Event: 5.707 Thread 0x0000016b91aca250 Thread exited: 0x0000016b91aca250


Dynamic libraries:
0x00007ff708300000 - 0x00007ff708310000 	E:\JAVA\bin\java.exe
0x00007ff827030000 - 0x00007ff827228000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8255d0000 - 0x00007ff825692000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff824d50000 - 0x00007ff825046000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8246c0000 - 0x00007ff8247c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff81fdc0000 - 0x00007ff81fddb000 	E:\JAVA\bin\VCRUNTIME140.dll
0x00007ff81fde0000 - 0x00007ff81fdf9000 	E:\JAVA\bin\jli.dll
0x00007ff826e80000 - 0x00007ff826f31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff826340000 - 0x00007ff8263de000 	C:\Windows\System32\msvcrt.dll
0x00007ff825e80000 - 0x00007ff825f1f000 	C:\Windows\System32\sechost.dll
0x00007ff825b10000 - 0x00007ff825c33000 	C:\Windows\System32\RPCRT4.dll
0x00007ff824a20000 - 0x00007ff824a47000 	C:\Windows\System32\bcrypt.dll
0x00007ff825050000 - 0x00007ff8251ed000 	C:\Windows\System32\USER32.dll
0x00007ff81dfe0000 - 0x00007ff81e27a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8248a0000 - 0x00007ff8248c2000 	C:\Windows\System32\win32u.dll
0x00007ff825da0000 - 0x00007ff825dcb000 	C:\Windows\System32\GDI32.dll
0x00007ff824a50000 - 0x00007ff824b69000 	C:\Windows\System32\gdi32full.dll
0x00007ff823400000 - 0x00007ff82340a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff824980000 - 0x00007ff824a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8253f0000 - 0x00007ff82541f000 	C:\Windows\System32\IMM32.DLL
0x00007ff81fd50000 - 0x00007ff81fd5c000 	E:\JAVA\bin\vcruntime140_1.dll
0x00007ffffb0c0000 - 0x00007ffffb14e000 	E:\JAVA\bin\msvcp140.dll
0x00007fffe0350000 - 0x00007fffe0f2e000 	E:\JAVA\bin\server\jvm.dll
0x00007ff826f40000 - 0x00007ff826f48000 	C:\Windows\System32\PSAPI.DLL
0x00007ff81e3c0000 - 0x00007ff81e3e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007fffeb340000 - 0x00007fffeb349000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff826e10000 - 0x00007ff826e7b000 	C:\Windows\System32\WS2_32.dll
0x00007ff823360000 - 0x00007ff823372000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff81c4f0000 - 0x00007ff81c4fa000 	E:\JAVA\bin\jimage.dll
0x00007ff821e10000 - 0x00007ff822011000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff80fef0000 - 0x00007ff80ff24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff824810000 - 0x00007ff824892000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff818d20000 - 0x00007ff818d45000 	E:\JAVA\bin\java.dll
0x00007fffef390000 - 0x00007fffef467000 	E:\JAVA\bin\jsvml.dll
0x00007ff826570000 - 0x00007ff826cde000 	C:\Windows\System32\SHELL32.dll
0x00007ff8222d0000 - 0x00007ff822a74000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff825fd0000 - 0x00007ff826323000 	C:\Windows\System32\combase.dll
0x00007ff8240f0000 - 0x00007ff82411b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff826d40000 - 0x00007ff826e0d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff825f20000 - 0x00007ff825fcd000 	C:\Windows\System32\SHCORE.dll
0x00007ff825d40000 - 0x00007ff825d9b000 	C:\Windows\System32\shlwapi.dll
0x00007ff8245f0000 - 0x00007ff824614000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff811d70000 - 0x00007ff811d89000 	E:\JAVA\bin\net.dll
0x00007ff81ede0000 - 0x00007ff81eeea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff823e50000 - 0x00007ff823eba000 	C:\Windows\system32\mswsock.dll
0x00007ff810fb0000 - 0x00007ff810fc6000 	E:\JAVA\bin\nio.dll
0x00007ff81bc20000 - 0x00007ff81bc38000 	E:\JAVA\bin\zip.dll
0x00007ff81c4d0000 - 0x00007ff81c4e0000 	E:\JAVA\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\JAVA\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;E:\JAVA\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\Oaps\OapsLib\build\20250901_11004799697631593167.compiler.options
java_class_path (initial): E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.22\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\kotlin-compiler-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.22\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\kotlin-script-runtime-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.22\20e2c5df715f3240c765cfc222530e2796542021\kotlin-daemon-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8514437120                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8514437120                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\JAVA
CLASSPATH=.;E:\JAVA\lib\dt.jar;E:\JAVA\lib\tools.jar;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\dotnet\;E:\git\Git\cmd;D:\Users\W9096060\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\dotnet\;E:\1.8java\lib\dt.jar;E:\1.8java\lib\tools.jar;E:\1.8java\bin;E:\JAVA\bin;E:\JAVA\lib;D:\Users\W9096060\AppData\Local\Microsoft\WindowsApps;
USERNAME=W9096060
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 19:56 hours

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 32479M (2633M free)
TotalPageFile size 47840M (AvailPageFile size 278M)
current process WorkingSet (physical memory assigned to process): 217M, peak: 228M
current process commit charge ("private bytes"): 274M, peak: 675M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211) for windows-amd64 JRE (17.0.8+9-LTS-211), built on Jun 14 2023 10:34:31 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
