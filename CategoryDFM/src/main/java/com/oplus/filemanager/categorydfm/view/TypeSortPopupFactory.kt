/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : TypeSortPopupFactory
 ** Description : 类型排序的PopupWindow创建的工厂类
 ** Version     : 1.0
 ** Date        : 2024/03/04 10:27
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/03/04       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.categorydfm.view

import android.content.Context
import android.view.View
import android.widget.AdapterView
import android.widget.PopupWindow
import androidx.annotation.VisibleForTesting
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.Log
import com.oplus.filemanager.categorydfm.R

object TypeSortPopupFactory {

    @VisibleForTesting
    var popupListWindow: COUIPopupListWindow? = null

    const val DOC_ITEM_MAX_COUNT = 7

    private const val TAG = "TypeSortPopupFactory"

    @JvmStatic
    fun showDocumentTypeSortPopup(context: Context, anchorView: View?, itemClickListener: PopupItemClickListener) {
        val items = context.resources.getStringArray(com.filemanager.common.R.array.dfm_doc_type).toList()

        /**
         * 从sp中获取
         */
        val dfmKey = SortRecordModeFactory.getDFMKey()
        val selectIndex = SortModeUtils.getDocSortType(dfmKey)
        show(context, anchorView, items, selectIndex, { parent, view, position, id ->
            // 存储到sp
            SortModeUtils.putDocSortType(dfmKey, position)
            val title = getSelectSortTitle(context, position)
            itemClickListener.onItemClick(position, title)
        }, {
            itemClickListener.onDismiss()
        })
    }


    @JvmStatic
    fun show(
        context: Context,
        anchorView: View?,
        items: List<String>,
        selectIndex: Int = 0,
        itemClickListener: AdapterView.OnItemClickListener,
        onDismissListener: PopupWindow.OnDismissListener
    ) {
        val popupItemList = mutableListOf<PopupListItem>()
        val builder = PopupListItem.Builder()
        items.forEachIndexed { index, item ->
            val listItem = builder.reset()
                .setTitle(item)
                .setIsChecked(index == selectIndex)
                .setIsEnable(true)
                .build()
            popupItemList.add(listItem)
        }
        if (popupListWindow == null) {
            popupListWindow = COUIPopupListWindow(context).apply {
                setMenuWidth(context.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_156dp))
                setDismissTouchOutside(true)
                setOnDismissListener(onDismissListener)
                setOnItemClickListener { parent, view, position, id ->
                    dismiss()
                    itemClickListener.onItemClick(parent, view, position, id)
                }
            }
        }
        popupListWindow?.apply {
            itemList = popupItemList
            show(anchorView)
        }
    }

    @JvmStatic
    fun release() {
        Log.d(TAG, "release")
        if (popupListWindow != null) {
            if (popupListWindow?.isShowing == true) {
                popupListWindow?.dismiss()
            }
            popupListWindow = null
        }
    }

    @JvmStatic
    fun getSelectSortTitle(context: Context): String {
        /**
         * 从sp中获取
         */
        val dfmKey = SortRecordModeFactory.getDFMKey()
        val selectIndex = SortModeUtils.getDocSortType(dfmKey)
        return getSelectSortTitle(context, selectIndex)
    }

    /**
     * 获取选中的sort 标题
     * 当选中全部时，显示“筛选”
     * 当选中其他项，显示内容为所选字段
     */
    @JvmStatic
    @VisibleForTesting
    fun getSelectSortTitle(context: Context, index: Int): String {
        val items = context.resources.getStringArray(com.filemanager.common.R.array.dfm_doc_type).toList()
        if (index == 0) {
            return context.resources.getString(com.filemanager.common.R.string.filtrate)
        }
        if (index < 0 || index >= items.size) {
            Log.w(TAG, "getSelectSortTitle index：$index invalidate, size:${items.size}")
            return ""
        }
        return items.get(index)
    }

    interface PopupItemClickListener : PopupWindow.OnDismissListener {

        fun onItemClick(position: Int, item: String)
    }
}