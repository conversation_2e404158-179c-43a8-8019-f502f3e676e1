/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WebApkLoader
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/26 14:06
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/26       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.loader

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import android.text.TextUtils
import com.filemanager.common.DiskLruCache
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.utils.BlacklistParser
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.KtCacheManager
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.fileservice.utils.WebFileConstant
import java.io.File
import java.io.IOException

class WebApkLoader : WebBaseUriLoader<MediaFileWrapper> {
    companion object {
        private const val TAG = "WebApkLoader"

        private const val INDEX_ID = 0
        private const val INDEX_DATA = 1
        private const val INDEX_DISPLAY_NAME = 2
        private const val INDEX_SIZE = 3
        private const val INDEX_DATE_MODIFIED = 4
        private const val INDEX_DATE_MIME_TYPE = 5
        private const val INDEX_DATE_MEDIA_FORMAT = 6
        private const val MAX_PACKAGE_NAME_SIZE = (1024 * 1024).toLong()

        @JvmStatic
        private val MEDIA_PROJECT = arrayOf(
            MediaStore.Files.FileColumns._ID,
            MediaStore.Files.FileColumns.DATA,
            MediaStore.Files.FileColumns.DISPLAY_NAME,
            MediaStore.Files.FileColumns.SIZE,
            MediaStore.Files.FileColumns.DATE_MODIFIED,
            MediaStore.Files.FileColumns.MIME_TYPE,
            MediaHelper.COLUMN_MEDIA_FORMAT
        )
    }

    private val mFilterSize: Int
    private var mIsNeedFilter: Boolean = false
    private val mExternalPath: String?
    private val mInternalPath: String?
    private var mSql: String? = null
    private var mInstalledMap: HashMap<String, Boolean>? = null
    private var mIsFirstCursor = true
    private var mDiskLruCache: DiskLruCache? = null
    private var mPackageMap: HashMap<String, String>? = null
    private var mSortOrderType = SortHelper.FILE_NAME_ORDER

    constructor(
        context: Context,
        uri: Uri?,
        sql: String?,
        sortOrderType: Int,
        isNeedFilter: Boolean
    ) : super(context) {
        mIsNeedFilter = isNeedFilter
        mInternalPath = VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)
        mExternalPath = VolumeEnvironment.getExternalSdPath(MyApplication.sAppContext)
        mFilterSize = BlacklistParser.getFilterSizeByType(CategoryHelper.CATEGORY_APK)
        mSql = sql
        mUri = uri
        mSortOrderType = sortOrderType
        super.initData()
    }

    override fun getUri(): Uri {
        return if (mUri != null) {
            mUri!!
        } else {
            MediaStore.Files.getContentUri("external")
        }
    }

    override fun getSelection(): String? {
        /*
        (SdkUtils.isAtLeastR() && (mSql?.contains("media_type=10002") == true)) is used to Compatible with old version settings,because of the sql 'media_type=10002' from settings is Abandoned in Android R,
        (SdkUtils.isAtLeastR() && (mSql?.contains("media_type=10002") == true)) need remove in the future.
        */
        return if (mSql.isNullOrEmpty() ||
            (SdkUtils.isAtLeastR() && (mSql?.contains("media_type=10002") == true))
        ) {
            MediaStoreCompat.getMediaStoreSqlQuery(CategoryHelper.CATEGORY_APK, ArrayList())
        } else {
            mSql
        }
    }

    override fun preHandleBeforeBackground() {
        super.preHandleBeforeBackground()
        if (mPackageMap == null) {
            mPackageMap = HashMap()
        }
        mIsFirstCursor = true
        mInstalledMap = KtAppUtils.getInstalledMap()
        mDiskLruCache = KtCacheManager.getDiskLruCache(
            MyApplication.sAppContext,
            "apk_cache",
            MAX_PACKAGE_NAME_SIZE
        )
    }

    override fun preHandleResultBackground(list: MutableList<MediaFileWrapper>): MutableList<MediaFileWrapper> {
        try {
            mDiskLruCache?.close()
        } catch (e: IOException) {
            Log.w(TAG, "preHandleResultBackground: mDiskLruCache close error, ${e.message}")
        }
        if ((mSortOrderType != SortHelper.FILE_TIME_REVERSE_ORDER) && (mSortOrderType != SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER)) {
            SortHelper.sortCategoryFiles(list, mSortOrderType, CategoryHelper.CATEGORY_APK, true)
        }
        return list
    }

    override fun getSortOrder(): String? {
        return getOrderWithType(mSortOrderType)
    }

    override fun getSelectionArgs(): Array<String>? {
        return null
    }

    override fun getProjection(): Array<String>? {
        return MEDIA_PROJECT
    }

    @Suppress("LongMethod")
    override fun createFromCursor(cursor: Cursor, uri: Uri?): MediaFileWrapper? {
        val id = cursor.getInt(INDEX_ID)
        val data = cursor.getString(INDEX_DATA)
        val displayName = cursor.getString(INDEX_DISPLAY_NAME)
        val size = cursor.getLong(INDEX_SIZE)
        var dateModified = cursor.getLong(INDEX_DATE_MODIFIED) * WebFileConstant.ONE_THOUSAND
        val mimeType = cursor.getString(INDEX_DATE_MIME_TYPE)
        if (dateModified == 0L) {
            Log.d(TAG, "dateModified is 0")
            dateModified = FileTimeUtil.getFileTime(data) ?: 0
        }
        val file = MediaFileWrapper(
            id,
            data,
            displayName,
            mimeType,
            size,
            dateModified,
            MediaHelper.FILE_URI
        )
        // data maybe not apk
        if (!FileTypeUtils.isApkType(data)) {
            return null
        }
        //file interface is slowly on Android R,So need not to call exists on Android R
        if (!SdkUtils.isAtLeastR() && (!JavaFileHelper.exists(file) || File(file.mData).isDirectory)) {
            return null
        } else {
            val isDir = if (cursor.isNull(INDEX_DATE_MEDIA_FORMAT)) {
                Log.d(TAG, "createFromCursor: format is null")
                if (!file.mData.isNullOrEmpty()) {
                    File(file.mData!!).isDirectory
                } else {
                    false
                }
            } else {
                val format = cursor.getInt(INDEX_DATE_MEDIA_FORMAT)
                (format == MediaHelper.MEDIA_FORMAT_DIR)
            }
            if (isDir) {
                return null
            }
        }
        var packName: String? = null
        val key = KtCacheManager.getCacheKey(file)
        if (!key.isNullOrEmpty()) {
            packName = mPackageMap?.get(key)
            if (TextUtils.isEmpty(packName)) {
                packName = KtCacheManager.getPackageNameDiskCache(file, mDiskLruCache)
                if (TextUtils.isEmpty(packName)) {
                    packName = KtCacheManager.getPackageNameAndSave(
                        MyApplication.sAppContext,
                        file,
                        mDiskLruCache
                    )
                }
            }
        }
        if (packName.isNullOrEmpty()) {
            Log.d(TAG, "Failed to parse apk: $key  ${file.mData}")
        } else {
            mPackageMap?.put(key!!, packName)
        }

        if (file.mLocalType != MimeTypeHelper.DRM_TYPE) {
            file.mLocalType = MimeTypeHelper.APPLICATION_TYPE
        }
        return if (mIsNeedFilter && (mFilterSize > 0) && (file.mSize < mFilterSize)) {
            null
        } else {
            file
        }
    }

    override fun getObserverUri(): Array<Uri>? {
        return null
    }
}