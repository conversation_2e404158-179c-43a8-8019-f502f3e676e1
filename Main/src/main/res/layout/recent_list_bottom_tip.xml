<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/recent_bottom_tip_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/empty_bottom_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/default_margin"
        android:gravity="center"
        android:paddingVertical="@dimen/main_recent_footer_vertical_padding"
        android:lineSpacingMultiplier="1.2"
        android:text="@string/display_file_in_recent_thirty_days"
        android:textAppearance="@style/couiTextBodyXS"
        android:textColor="?attr/couiColorLabelTertiary" />
</LinearLayout>
