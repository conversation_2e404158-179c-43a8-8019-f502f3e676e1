package com.oplus.filemanager.utils

import com.filemanager.common.thread.LockUtils
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import java.lang.reflect.Field
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

/**
 * TimeoutMonitor的单元测试类
 * 用于测试TimeoutMonitor类的各种功能和行为
 */
class TimeoutMonitorTest {

    /**
     * 在每个测试方法执行前初始化
     * 主要工作：
     * 1. 模拟LockUtils的静态方法
     * 2. 设置waitLock和releaseLock的默认行为
     */
    @Before
    fun setup() {
        // 初始化模拟静态方法
        mockkStatic(LockUtils::class)
        // 设置waitLock的默认行为 - 什么都不做
        every { LockUtils.waitLock(any(), any()) } answers { }
        // 设置releaseLock的默认行为 - 什么都不做
        every { LockUtils.releaseLock(any()) } answers { }
    }

    /**
     * 在每个测试方法执行后清理
     * 主要工作：
     * 1. 解除所有模拟对象
     */
    @After
    fun tearDown() {
        // 清理所有模拟和状态
        unmockkAll()
    }

    /**
     * 测试startMonitor方法在未收到结果时设置超时标志
     * 验证点：
     * 1. 当没有调用cancel方法时，isTimeout()应返回true
     */
    @Test(timeout = 2000)
    fun `startMonitor should set timeout when no result received`() {
        val monitor = TimeoutMonitor()
        monitor.startMonitor(100)
        // 验证超时标志被设置
        assertTrue(monitor.isTimeout())
    }

    /**
     * 测试startMonitor方法在超时前收到结果时不设置超时标志
     * 使用CountDownLatch实现线程同步
     * 验证点：
     * 1. 在超时前调用cancel方法，isTimeout()应返回false
     */
    @Test(timeout = 5000)
    fun `startMonitor should not set timeout when result received before timeout`() {
        // 创建两个CountDownLatch用于线程同步
        val enteredLatch = CountDownLatch(1)  // 表示已进入等待状态
        val releaseLatch = CountDownLatch(1)  // 表示可以释放等待

        // 重写waitLock行为实现线程同步
        every { LockUtils.waitLock(any(), any()) } answers {
            enteredLatch.countDown()  // 通知已进入等待
            releaseLatch.await(2, TimeUnit.SECONDS)  // 等待释放信号
        }
        every { LockUtils.releaseLock(any()) } answers {
            releaseLatch.countDown()  // 发送释放信号
        }

        val monitor = TimeoutMonitor()
        val thread = Thread { monitor.startMonitor(2000) }
        thread.start()

        // 确保已进入等待状态
        assertTrue(enteredLatch.await(1, TimeUnit.SECONDS))
        // 在超时前取消
        assertTrue(monitor.cancel(true))
        thread.join(1000)

        // 验证超时标志未被设置
        assertFalse(monitor.isTimeout())
    }

    /**
     * 测试cancel方法在isMonitor为false时的行为
     * 验证点：
     * 1. 当isMonitor为false时，cancel应直接返回true
     * 2. 不应调用releaseLock方法
     */
    @Test
    fun `cancel should return true immediately when isMonitor is false`() {
        val monitor = TimeoutMonitor()
        // 验证返回true
        assertTrue(monitor.cancel(false))
        // 验证未调用释放锁
        verify(exactly = 0) { LockUtils.releaseLock(any()) }
    }

    /**
     * 测试cancel方法在已经超时时的行为
     * 使用反射设置内部状态
     * 验证点：
     * 1. 当已经超时时，cancel应返回false
     * 2. 不应调用releaseLock方法
     */
    @Test
    fun `cancel should return false when already timed out`() {
        val monitor = TimeoutMonitor()
        // 使用反射设置超时状态为true
        setPrivateField(monitor, "hasTimeout", AtomicBoolean(true))

        // 验证返回false
        assertFalse(monitor.cancel(true))
        // 验证未调用释放锁
        verify(exactly = 0) { LockUtils.releaseLock(any()) }
    }

    /**
     * 测试cancel方法在未超时时的行为
     * 验证点：
     * 1. 当未超时时，cancel应返回true
     * 2. 应调用releaseLock方法
     */
    @Test
    fun `cancel should release lock and return true when not timed out`() {
        val monitor = TimeoutMonitor()
        // 验证返回true
        assertTrue(monitor.cancel(true))
        // 验证释放锁被调用一次
        verify(exactly = 1) { LockUtils.releaseLock(any()) }
    }

    /**
     * 辅助方法：通过反射设置对象的私有字段值
     * @param obj 目标对象
     * @param fieldName 字段名
     * @param value 要设置的值
     */
    private fun setPrivateField(obj: Any, fieldName: String, value: Any) {
        val field: Field = obj.javaClass.getDeclaredField(fieldName)
        field.isAccessible = true  // 设置可访问
        field.set(obj, value)  // 设置字段值
    }
}