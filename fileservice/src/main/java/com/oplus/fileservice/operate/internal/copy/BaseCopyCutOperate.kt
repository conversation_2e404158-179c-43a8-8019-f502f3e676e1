/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BaseCopyCutOperate
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/7 19:59
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/7       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.copy

import androidx.annotation.VisibleForTesting
import com.filemanager.common.fileutils.fetchFileName
import com.filemanager.common.utils.Log
import com.oplus.fileservice.operate.convert.OperateRequest
import com.oplus.fileservice.operate.internal.ConflictPolicy
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.InvalidOperation
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.InvalidSourceFileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.InvalidTargetFileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.OK
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.OperateFailure
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.internal.BaseFileOperate
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.NotEnoughSpace
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.PartOfFileOperateFailure
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.SameOperationDirectory
import com.oplus.fileservice.operate.internal.utils.initImproveCpu
import com.oplus.fileservice.operate.internal.utils.releaseImproveCpu
import java.io.File

abstract class BaseCopyCutOperate : BaseFileOperate() {

    override fun run(operateRequest: OperateRequest): OperationResult {
        FileCopyHelper.init()
        Log.d(TAG, "run -> operateRequest = $operateRequest")
        operateRequest.sourceFileInfo?.let { sourceInfos ->
            operateRequest.targetFileInfo?.let { targetInfo ->
                return innerPerform(sourceInfos, targetInfo, operateRequest.conflictPolicy)
            } ?: return OperationResult(false, InvalidTargetFileInfo)
        } ?: return OperationResult(false, InvalidSourceFileInfo)
    }

    private fun innerPerform(
        sourceInfos: List<FileInfo>,
        targetInfo: FileInfo,
        denyPolicy: ConflictPolicy
    ): OperationResult {
        val detectResult = exceptionDetection(sourceInfos, targetInfo)
        Log.d(TAG, "innerPerform -> detectResult = $detectResult")
        detectResult?.let {
            when (it) {
                ERROR_PATH_NULL -> return OperationResult(false, InvalidSourceFileInfo)
                ERROR_IN_SAME_DIR -> return OperationResult(false, SameOperationDirectory)
                is Pair<*, *> -> {
                    when (it.first) {
                        ERROR_STORAGE_NOT_ENOUGH -> return OperationResult(false, NotEnoughSpace)
                        ERROR_IN_SAME_DIR -> return OperationResult(false, SameOperationDirectory)
                    }
                }
            }
            return OperationResult(false, InvalidOperation)
        }
        initImproveCpu()
        val result = workRun(sourceInfos, targetInfo, denyPolicy)
        Log.d(TAG, "innerPerform -> result = $result")
        releaseImproveCpu()
        return result
    }

    protected open fun exceptionDetection(sourceInfos: List<FileInfo>, targetInfo: FileInfo): Any? {
        Log.d(TAG, "exceptionDetection")
        if (sourceInfos.isEmpty() || !isDirectory(targetInfo.data) || targetInfo.data.isNullOrEmpty()) {
            return ERROR_PATH_NULL
        }
        targetInfo.let {
            FileCopyHelper.checkInSameFolder(sourceInfos, it).let { result ->
                if (result.first) {
                    return Pair(ERROR_IN_SAME_DIR, result.second)
                }
            }
        }
        return null
    }

    protected open fun workRun(
        sourceInfos: List<FileInfo>,
        targetInfo: FileInfo,
        denyPolicy: ConflictPolicy
    ): OperationResult {
        Log.d(TAG, "workRun -> sourceInfos = $sourceInfos ; targetInfo = $targetInfo")
        val destDir: File? = targetInfo.data?.let { File(it) }
        var destFile: File
        var sourceFile: File
        val totalSize = sourceInfos.size
        var failCount = 0
        for (f in sourceInfos) {
            Log.d(TAG, "workRun -> isCancelled -> ${isCancelled()}")
            if (isCancelled()) {
                Log.d(TAG, "workRun -> isCancelled")
                break
            }
            if (f.displayName.isNullOrEmpty() || f.data.isNullOrEmpty()) {
                Log.d(TAG, "workRun -> ${f.displayName} filename or path is empty")
                continue
            }
            sourceFile = File(f.data!!)
            destFile = File(destDir, f.displayName)
            Log.i(TAG, "workRun -> destFile exist = ${destFile.exists()}")
            if (destFile.exists()) {
                when (denyPolicy) {
                    ConflictPolicy.KEEP_BOTH -> {
                        if (!keepBoth(targetInfo, sourceFile)) {
                            failCount++
                        }
                    }
                    ConflictPolicy.REPLACE -> {
                        if (!onDealFile(sourceFile, destFile)) {
                            failCount++
                        }
                    }
                    ConflictPolicy.SKIP -> onSkipDealFile(sourceFile)
                }
            } else {
                if (!onDealFile(sourceFile, destFile)) {
                    failCount++
                }
            }
        }
        onDealAllFilesEnd()
        return if (failCount in 1 until totalSize) {
            OperationResult(false, PartOfFileOperateFailure)
        } else if (failCount > 0) {
            OperationResult(true, OperateFailure)
        } else {
            OperationResult(true, OK)
        }
    }

    /**
     * When files conflict, resolve conflicting files by "skip".
     */
    protected abstract fun onSkipDealFile(sourceFile: File)

    /**
     * Handle file move/copy operations.
     */
    protected abstract fun onDealFile(sourceFile: File, destFile: File): Boolean

    /**
     * Executed after all files have been processed.
     */
    protected abstract fun onDealAllFilesEnd()

    override fun afterRun(result: OperationResult) {
        // do nothing
    }

    override fun recycle() {
        // do nothing
    }

    protected open fun onDealFileError(sourceFile: File, destFile: File): Boolean {
        return false
    }

    @VisibleForTesting
    fun keepBoth(targetInfo: FileInfo, sourceFile: File): Boolean {
        val newFileBean = fetchFileName(
            targetInfo,
            sourceFile.nameWithoutExtension,
            sourceFile.extension.let {
                if (it.isNotEmpty()) {
                    ".".plus(it)
                } else {
                    it
                }
            }
        )
        Log.d(TAG, "dealFileWhenExist: keep both, new file=${newFileBean?.mData}")
        return if ((newFileBean != null) && !newFileBean.mData.isNullOrEmpty()) {
            onDealFile(sourceFile, File(newFileBean.mData!!))
        } else {
            true
        }
    }

    private fun isDirectory(data: String?): Boolean {
        data?.let {
            return File(it).isDirectory
        } ?: return false
    }

    companion object {
        private const val TAG = "BaseCopyCutOperate"
        const val ERROR_PATH_NULL = 0
        private const val ERROR_IN_SAME_DIR = 1
        const val ERROR_STORAGE_NOT_ENOUGH = 2
    }
}