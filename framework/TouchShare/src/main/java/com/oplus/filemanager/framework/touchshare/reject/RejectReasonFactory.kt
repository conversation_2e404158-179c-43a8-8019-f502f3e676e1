/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RejectReasonFactory
 * * Description : 拒绝分享的原因
 * * Version     : 1.0
 * * Date        : 2025/05/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.framework.touchshare.reject

import com.oplus.filemanager.interfaze.touchshare.ITouchShareApi
import com.oplus.interconnect.share.sdk.data.RejectReason

object RejectReasonFactory {

    fun create(reason: Int): RejectReason {
        return when (reason) {
            ITouchShareApi.REJECT_REASON_UNKNOWN -> RejectReason.UNKNOWN
            ITouchShareApi.REJECT_REASON_NO_CONTENT -> RejectReason.NO_CONTENT
            ITouchShareApi.REJECT_REASON_CONDITION_LIMIT -> RejectReason.CONDITION_LIMIT
            ITouchShareApi.REJECT_REASON_USER_REJECT -> RejectReason.USER_REJECT
            else -> RejectReason.UNKNOWN
        }
    }
}