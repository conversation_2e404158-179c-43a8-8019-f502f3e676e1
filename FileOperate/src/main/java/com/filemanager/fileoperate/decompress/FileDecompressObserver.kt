/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileDecompressObserver.kt
 ** Description: Monitor file decompress
 ** Version: 1.0
 ** Date: 2020/3/9
 ** Author: LiHao(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.decompress

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.view.ContextThemeWrapper
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.COUIRotatingDialogBuilder
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.utils.COUISnackBarUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.base.BaseFileNameDialog
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser

const val SHOW_DECOMPRESS_PASSWORD_DIALOG = 3
const val DISMISS_DECOMPRESS_PASSWORD_DIALOG = 4
const val SHOW_DECOMPRESS_PASSWORD_ERROR = 5
const val NOTICE_DECOMPRESS_SPACE_NOT_ENOUGH = 8
const val NOTICE_DECOMPRESS_UNSUPPORT = 10
const val NOTICE_DECOMPRESS_READY_DATA_END = 11
const val NOTICE_FILE_NOT_EXISTS = 12
const val NOTICE_SOURCE_FILE_NOT_EXIST = 13
const val SHOW_VERIFY_PWD_LOADING_DIALOG = 14
const val DISMISS_VERIFY_PWD_LOADING_DIALOG = 15
const val RESHOW_DECOMPRESS_PASSWORD_DIALOG = 16

open class FileDecompressObserver(context: ContextThemeWrapper) : BaseFileActionObserver(context) {
    var mIsNeedSkipShowClickToast = false
    @VisibleForTesting
    var mFilePasswordDialog: FileDecompressPasswordDialog? = null
    @VisibleForTesting
    var loadingDialog: AlertDialog? = null
    private var spaceNotEnoughDialog: AlertDialog? = null

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        when (result.first) {
            // --file password start--
            SHOW_DECOMPRESS_PASSWORD_DIALOG -> {
                mFilePasswordDialog?.dismiss()
                if (result.second is PasswordDialogBean) {
                    mFilePasswordDialog = DialogFactory.createFilePasswordDialog(context, result.second as PasswordDialogBean)
                    mFilePasswordDialog?.show()
                }
            }
            SHOW_DECOMPRESS_PASSWORD_ERROR -> mFilePasswordDialog?.showPasswordErrorNotice()
            DISMISS_DECOMPRESS_PASSWORD_DIALOG -> mFilePasswordDialog?.dismiss()
            RESHOW_DECOMPRESS_PASSWORD_DIALOG -> {
                if (mFilePasswordDialog?.isShowing() == false) {
                    mFilePasswordDialog?.show()
                }
                mFilePasswordDialog?.setInputText(result.second as? String)
            }
            SHOW_VERIFY_PWD_LOADING_DIALOG -> {
                val listener = result.second
                if (listener is DialogInterface.OnCancelListener) {
                    loadingDialog = DialogFactory.showVerifyPwdLoadingDialog(context, listener)
                }
            }
            DISMISS_VERIFY_PWD_LOADING_DIALOG -> loadingDialog?.dismiss()
            // --file password end--

            NOTICE_FILE_NOT_EXISTS -> CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
            NOTICE_DECOMPRESS_READY_DATA_END -> {
                dismissProgressDialog()
            }
            NOTICE_DECOMPRESS_SPACE_NOT_ENOUGH -> {
                dismissProgressDialog()
                if (result.second is String) {
                    showSpaceNotEnoughNotice(context, result.second as String)
                }
                onActionDone(false)
                return true
            }
            NOTICE_DECOMPRESS_UNSUPPORT -> {
                CustomToast.showShort(com.filemanager.common.R.string.decompress_file_error)
                onActionDone(false)
                return true
            }
            NOTICE_SOURCE_FILE_NOT_EXIST ->  {
                CustomToast.showShort(com.filemanager.common.R.string.toast_file_not_exist)
                onActionDone(false)
                return true
            }
            ACTION_FAILED -> {
                CustomToast.showShort(com.filemanager.common.R.string.decompress_file_error)
                return false
            }
            ACTION_DONE -> {
                if ((context is Activity) && (result.second is String)) {
                    if (!mIsNeedSkipShowClickToast) {
                        COUISnackBarUtils.show(context, com.filemanager.common.R.string.decompress_success) {
                            P7ZipDecompressHelper.clearPassword()
                            val fileBrowser = Injector.injectFactory<IFileBrowser>()
                            fileBrowser?.toFileBrowserActivity(context, result.second as String)
                        }
                    }
                } else {
                    CustomToast.showShort(com.filemanager.common.R.string.decompress_success)
                }
                return false
            }
            else -> return false
        }
        return true
    }

    override fun onActionReShowDialog() {
        mFilePasswordDialog?.reShowDialog()
    }

    private fun showSpaceNotEnoughNotice(context: Context, storage: String) {
        if (storage.isEmpty()) {
            CustomToast.showShort(com.filemanager.common.R.string.storage_space_not_enough)
        } else {
            val msg = context.getString(
                com.filemanager.common.R.string.disk_space_not_enough, storage,
                    context.getString(com.filemanager.common.R.string.unable_to_decompress))
            if (FeatureCompat.sPhoneManagerStartInfo != null) {
                spaceNotEnoughDialog = DialogFactory.createSpaceNotEnoughDialog(context, msg)
                spaceNotEnoughDialog?.show()
            } else {
                CustomToast.showShort(msg)
            }
        }
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || mFilePasswordDialog?.isShowing() ?: false
                || mFilePasswordDialog?.isShowing() ?: false || spaceNotEnoughDialog?.isShowing ?: false
    }

    override fun recycle() {
        mFilePasswordDialog?.dismiss()
        mFilePasswordDialog = null
        loadingDialog?.dismiss()
        loadingDialog = null
        super.recycle()
    }

    open class PasswordDialogBean(var resultListener: BaseFileNameDialog.OnButtonClickListener?) {
        fun recycle() {
            resultListener = null
        }
    }

}

object DialogFactory {

    fun createFilePasswordDialog(context: Context, second: FileDecompressObserver.PasswordDialogBean): FileDecompressPasswordDialog {
        return FileDecompressPasswordDialog(context, second)
    }

    fun createSpaceNotEnoughDialog(context: Context, msg: String): AlertDialog {
        val dialog = COUIAlertDialogBuilder(context).setTitle(msg)
                .setPositiveButton(com.filemanager.common.R.string.garbage_cleanup) { _, _ ->
                    KtAppUtils.startPhoneManager(context)
                }
                .setNegativeButton(com.filemanager.common.R.string.alert_dialog_cancel, null)
                .create()
        dialog.setCanceledOnTouchOutside(false)
        return dialog
    }

    fun showVerifyPwdLoadingDialog(context: Context, cancelAction: DialogInterface.OnCancelListener?): AlertDialog {
        val dialog = COUIRotatingDialogBuilder(context).apply {
            setDialogTitle(com.filemanager.common.R.string.decompress_verify_password)
            setCancelButton(com.filemanager.common.R.string.alert_dialog_cancel) { dialog, _ -> dialog.cancel() }
        }.show()
        dialog.setOnCancelListener(cancelAction)
        dialog.setCanceledOnTouchOutside(false)
        return dialog
    }
}