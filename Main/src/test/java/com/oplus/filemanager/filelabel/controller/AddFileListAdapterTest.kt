/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.controller
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/5/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.filelabel.controller

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.Log
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AddFileListAdapterTest {

    @MockK
    private lateinit var holder: AddFileListAdapter.AddFileViewHolder

    @MockK
    private lateinit var adapter: AddFileListAdapter

    @Before
    fun before() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should return 4B when updateSizeFormat if size less than num`() {
        every { holder.updateSizeFormat(any()) } answers { callOriginal() }
        assertEquals("4B", holder.updateSizeFormat(4))
    }

    @Test
    fun `should return 1023B when updateSizeFormat if size less than num`() {
        every { holder.updateSizeFormat(any()) } answers { callOriginal() }
        assertEquals("1023B", holder.updateSizeFormat(1023))
    }

    @Test
    fun `should return 1KB when updateSizeFormat if size more than num`() {
        every { holder.updateSizeFormat(any()) } answers { callOriginal() }
        assertEquals("1.0KB", holder.updateSizeFormat(1024))
    }

    @Test
    fun `should return 1KB when updateSizeFormat if size is 1025`() {
        every { holder.updateSizeFormat(any()) } answers { callOriginal() }
        assertEquals("1.0KB", holder.updateSizeFormat(1025))
    }

    @Test
    fun `should return 1024KB when updateSizeFormat if size is 1048570`() {
        every { holder.updateSizeFormat(any()) } answers { callOriginal() }
        assertEquals("1024.0KB", holder.updateSizeFormat(1048570))
    }

    @Test
    fun `should return 1MB when updateSizeFormat if size is 1048576`() {
        every { holder.updateSizeFormat(any()) } answers { callOriginal() }
        assertEquals("1.0MB", holder.updateSizeFormat(1048576))
    }

    @Test
    fun `should return 1MB when updateSizeFormat if size is 1048577`() {
        every { holder.updateSizeFormat(any()) } answers { callOriginal() }
        assertEquals("1.0MB", holder.updateSizeFormat(1048577))
    }

    @Test
    fun `should return 1024MB when updateSizeFormat if size is 1073741820`() {
        every { holder.updateSizeFormat(any()) } answers { callOriginal() }
        assertEquals("1024.0MB", holder.updateSizeFormat(1073741820))
    }

    @Test
    fun `should return 1GB when updateSizeFormat if size is 1073741824`() {
        every { holder.updateSizeFormat(any()) } answers { callOriginal() }
        assertEquals("1.0GB", holder.updateSizeFormat(1073741824))
    }

    @Test
    fun `should return 1GB when updateSizeFormat if size is 1073741830`() {
        every { holder.updateSizeFormat(any()) } answers { callOriginal() }
        assertEquals("1.0GB", holder.updateSizeFormat(1073741830))
    }

    @Test
    fun `should return 3GB when updateSizeFormat if size is 3221225472`() {
        every { holder.updateSizeFormat(any()) } answers { callOriginal() }
        assertEquals("3.0GB", holder.updateSizeFormat(3221225472))
    }

    @Test
    fun `should return 1 when getItemKey if file is null`() {
        every { adapter.getItemKey(any(), any()) } answers { callOriginal() }
        assertEquals(1, adapter.getItemKey(BaseFileBean(), 1))
    }

    @Test
    fun `should return 10 when getItemKey if file is empty`() {
        every { adapter.getItemKey(any(), any()) } answers { callOriginal() }
        val file = BaseFileBean()
        file.mData = ""
        assertEquals(10, adapter.getItemKey(file, 10))
    }

    @Test
    fun `should return n when getItemKey if file is not empty`() {
        every { adapter.getItemKey(any(), any()) } answers { callOriginal() }
        val file = BaseFileBean()
        file.mData = "path"
        assertEquals(3433509, adapter.getItemKey(file, 1))
    }

    @Test
    fun `should execute log when initListChoiceModeAnimFlag`() {
        every { adapter.initListChoiceModeAnimFlag(any()) } answers { callOriginal() }
        mockkStatic(Log::class)
        adapter.initListChoiceModeAnimFlag(true)
        verify { Log.i(any(), any()) }
        unmockkStatic(Log::class)
    }
}