/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForFileBrowser
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.oaps.di

import androidx.annotation.Keep
import com.oplus.filemanager.interfaze.oaps.IOapsLib
import com.oplus.filemanager.oaps.OapsApi
import org.koin.dsl.module

@Keep
class AutoDIForOapsLib {

    val oapsLibModule = module {
        single<IOapsLib>(createdAtStart = true) {
            OapsApi
        }
    }
}