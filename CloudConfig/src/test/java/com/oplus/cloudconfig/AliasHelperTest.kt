package com.oplus.cloudconfig

import com.filemanager.common.MyApplication
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.KtAppUtils
import com.oplus.cloudconfig.bean.AliasBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * AliasHelper的单元测试类
 * 使用RobolectricTestRunner运行测试，配置SDK版本为29
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])  // 修改SDK版本为29以匹配应用要求
class AliasHelperTest {
    // 测试用的内部存储路径
    private val testInternalSdPath = "/storage/emulated/0"
    // 测试用的包名
    private val testPackageName = "com.test.package"
    // 测试用的别名
    private val testAlias = "TestAlias"
    
    /**
     * 测试前的初始化方法
     * 使用MockK框架模拟相关对象和静态方法
     */
    @Before
    fun setup() {
        // 模拟MyApplication单例对象
        mockkObject(MyApplication)
        // 模拟KtAppUtils单例对象
        mockkObject(KtAppUtils)
        // 模拟VolumeEnvironment的静态方法
        mockkStatic(VolumeEnvironment::class)
        
        // 设置模拟行为：当调用VolumeEnvironment.getInternalSdPath时返回测试路径
        every { VolumeEnvironment.getInternalSdPath(any()) } returns testInternalSdPath
        // 设置模拟行为：MyApplication的sAppContext返回一个模拟的Context
        every { MyApplication.sAppContext } returns mockk(relaxed = true)
    }
    
    /**
     * 测试后的清理方法
     * 解除所有模拟并清空AliasHelper中的别名映射
     */
    @After
    fun tearDown() {
        // 解除所有模拟
        unmockkAll()
        // 清空别名映射
        AliasHelper.mAliasMap.clear()
    }
    
    /**
     * 测试添加默认别名的情况
     * 当别名是默认别名时，应该被添加到映射中
     */
    @Test
    fun testAddAlias_withDefaultAlias_shouldAddToMap() {
        // Given 准备测试数据：创建一个默认别名的AliasBean
        val aliasBean = AliasBean(
            mPath = "test/path",
            mPackageName = testPackageName,
            mAlias = testAlias,
            mDefault = AliasHelper.IS_DEFAULT_ALIAS
        )
        // 设置模拟行为：getInstalledMap返回空Map
        every { KtAppUtils.getInstalledMap() } returns hashMapOf()
        
        // When 执行测试方法：添加别名
        AliasHelper.addAlias(listOf(aliasBean))
        
        // Then 验证结果：映射中应包含预期的键和值
        val expectedKey = "$testInternalSdPath${File.separator}test/path".toLowerCase(Locale.getDefault())
        assertTrue(AliasHelper.mAliasMap.containsKey(expectedKey))
        assertEquals(aliasBean, AliasHelper.mAliasMap[expectedKey])
    }
    
    /**
     * 测试添加已安装应用别名的情况
     * 当应用已安装时，别名应该被添加到映射中
     */
    @Test
    fun testAddAlias_withInstalledApp_shouldAddToMap() {
        // Given 准备测试数据：创建一个非默认但应用已安装的AliasBean
        val aliasBean = AliasBean(
            mPath = "test/path",
            mPackageName = testPackageName,
            mAlias = testAlias,
            mDefault = "0"
        )
        // 设置模拟行为：getInstalledMap返回包含测试包名的Map
        every { KtAppUtils.getInstalledMap() } returns hashMapOf(testPackageName to true)
        
        // When 执行测试方法：添加别名
        AliasHelper.addAlias(listOf(aliasBean))
        
        // Then 验证结果：映射中应包含预期的键和值
        val expectedKey = "$testInternalSdPath${File.separator}test/path".toLowerCase(Locale.getDefault())
        assertTrue(AliasHelper.mAliasMap.containsKey(expectedKey))
        assertEquals(aliasBean, AliasHelper.mAliasMap[expectedKey])
    }
    
    /**
     * 测试添加未安装应用别名的情况
     * 当应用未安装且不是默认别名时，不应该添加到映射中
     */
    @Test
    fun testAddAlias_withNotInstalledApp_shouldNotAddToMap() {
        // Given 准备测试数据：创建一个非默认且应用未安装的AliasBean
        val aliasBean = AliasBean(
            mPath = "test/path",
            mPackageName = testPackageName,
            mAlias = testAlias,
            mDefault = "0"
        )
        // 设置模拟行为：getInstalledMap返回空Map
        every { KtAppUtils.getInstalledMap() } returns hashMapOf()
        
        // When 执行测试方法：添加别名
        AliasHelper.addAlias(listOf(aliasBean))
        
        // Then 验证结果：映射应该为空
        assertTrue(AliasHelper.mAliasMap.isEmpty())
    }
    
    /**
     * 测试添加空别名的情况
     * 当AliasBean为空时，不应该添加到映射中
     */
    @Test
    fun testAddAlias_withEmptyAliasBean_shouldNotAddToMap() {
        // Given 准备测试数据：创建一个空的AliasBean
        val aliasBean = AliasBean()
        // 设置模拟行为：getInstalledMap返回包含测试包名的Map
        every { KtAppUtils.getInstalledMap() } returns hashMapOf(testPackageName to true)
        
        // When 执行测试方法：添加别名
        AliasHelper.addAlias(listOf(aliasBean))
        
        // Then 验证结果：映射应该为空
        assertTrue(AliasHelper.mAliasMap.isEmpty())
    }
    
    /**
     * 测试isEmpty方法
     * 当映射为空时，应该返回true
     */
    @Test
    fun testIsEmpty_whenMapEmpty_shouldReturnTrue() {
        // When 执行测试方法：检查映射是否为空
        val result = AliasHelper.isEmpty()
        
        // Then 验证结果：应该返回true
        assertTrue(result)
    }
    
    /**
     * 测试isEmpty方法
     * 当映射不为空时，应该返回false
     */
    @Test
    fun testIsEmpty_whenMapNotEmpty_shouldReturnFalse() {
        // Given 准备测试数据：向映射中添加一个别名
        AliasHelper.mAliasMap["test"] = AliasBean(
            mPath = "test/path",
            mPackageName = testPackageName,
            mAlias = testAlias,
            mDefault = AliasHelper.IS_DEFAULT_ALIAS
        )
        
        // When 执行测试方法：检查映射是否为空
        val result = AliasHelper.isEmpty()
        
        // Then 验证结果：应该返回false
        assertFalse(result)
    }
}