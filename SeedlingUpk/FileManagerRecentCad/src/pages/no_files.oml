<template>
    <card-template entry="desktop">
        <div class="cardContainer" onclick="clickCard">
            <div class="tipsContainer" >
                <text class="textTips title">{{ tips }}</text>
                <text class="textSummary summary">{{ summary }}</text>
            </div>
            <div class="cardTopContainer">
                <image class="icon" src="{{ icon }}"></image>
                <text class="cardTitle title">{{ cardTitle }}</text>
            </div>
        </div>
    </card-template>
</template>

<data>
{
    "uiData": {
        "icon": "{{$r('images.recent_icon')}}",
        "isRecent": true,
        "cardTitle": "{{$t('strings.recent_file')}}",
        "tips": "{{$t('strings.empty_file')}}",
        "summary": "{{$t('strings.empty_file_summary')}}",
        "actionUri": "nativeapp://oplus.intent.action.filemanager.OPEN_FILEMANAGER",
        "cardType": -1,
        "sourceAppName": ""
    }, 

    "uiEvent": { 
        "clickCard": {
            "type": "deeplink",
            "uri": "{{actionUri}}",
            "params": {
                "isFromRecentCardWidget": "{{isRecent}}",
                "cardType": "{{cardType}}",
                "sourceAppName": "{{sourceAppName}}"
            }
        }
    }
}
</data>
<style>
  @media screen and (dark-mode: true) { 
      .title {
          color: rgba(255, 255, 255, 0.9);
      }
      .summary {
          color: rgba(255, 255, 255, 0.54);
      }
  }
  @media screen and (dark-mode: false) { 
      .title {
          color: rgba(0, 0, 0, 0.9);
      }
      .summary {
          color: rgba(0, 0, 0, 0.54);
      }
  }
  @media screen and (dark-mode: false) {
      .cardContainer {
          background: linear-gradient(to bottom right,#EDEFF3,#DFE3E8);
      }
  }
  @media screen and (aspect-ratio: 2*4) and (dark-mode: true) {
      .cardContainer {
          background-image: url("assets/images/background_dark.png");
      }
  }
  @media screen and (aspect-ratio: 4*4) and (dark-mode: true) {
      .cardContainer {
          background-image: url("assets/images/background_dark_large.png");
      }
  }
    .cardContainer {
        position: absolute;
        width: 100%;
        height: 100%;
        background-position:center
    }

    .tipsContainer {
        position: absolute;
        width: 100%;
        height: 100%;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .cardTopContainer {
        width: 100%;
        height: 100%;
        padding: 16px;
        flex-direction: row;
        justify-content: flex-start;
    }

    .icon {
        width: 15px;
        height: 15px;
        object-fit: fill;
        border-radius: 3.32;
    }

    .cardTitle {
        width: 93px;
        height: 16px;
        font-family:OPlusSans 3.5;
        font-size:14px;
        max-lines: 1;
        margin-start: 6px;
        line-height: 16px;
        text-overflow: ellipsis;
        text-decoration:none;
        font-weight: 500;
        font-style: normal;
        text-align: left;
    }

    .textTips {
        font-family:OPlusSans 3.5;
        font-size:16.0px;
        max-lines: 2;
        line-height: 22px;
        margin-left: 16px;
        margin-right: 16px;
        text-overflow: ellipsis;
        text-decoration:none;
        font-weight: 500;
        font-style: normal
    }

    .textSummary {
        font-family: OPlus Sans SC 3.5;
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        letter-spacing: 0px;
        max-lines: 2;
        text-overflow: ellipsis;
        text-decoration:none;
        height: 16px;
        margin-top: 2px;
        margin-left: 16px;
        margin-right: 16px;
    }
</style>