/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved.
 * * COLOROS_EDIT
 * * File: KeyMoveGalleryFragment
 * * Description: a gallery for a key to move
 * * Version: 1.0
 * * Date : 2020/7/14
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/7/14       1.0         a gallery for a key to move
 ****************************************************************/
package com.oplus.filemanager.keymove.ui.keymovegallery

import android.content.Context
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import androidx.lifecycle.ViewModel
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.decoration.SpacesItemDecoration
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.OnGetUIInfoListener
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.utils.*
import com.filemanager.common.view.FileManagerRecyclerView
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.oplus.filemanager.keymove.R
import com.oplus.filemanager.keymove.adapter.KeyMoveGalleryAdapter
import com.oplus.filemanager.keymove.ui.AKeyToMoveActivity
import com.oplus.filemanager.keymove.ui.model.ListSecond
import com.oplus.filemanager.keymove.ui.model.Visitable
import kotlinx.coroutines.*

class KeyMoveGalleryFragment : BaseVMFragment<BaseViewModel>(), OnBackPressed, OnGetUIInfoListener,
        OnRecyclerItemClickListener, CoroutineScope by MainScope() {

    companion object {
        private const val TAG = "KeyMoveGalleryFragment"
    }

    private var mListSecond: ListSecond? = null
    private var mRootView: ViewGroup? = null
    private var mAppBarLayout: COUIDividerAppBarLayout? = null
    private var mRecyclerView: FileManagerRecyclerView? = null
    private var mLayoutManager: GridLayoutManager? = null
    private var mToolbar: COUIToolbar? = null
    private var mAdapter: KeyMoveGalleryAdapter? = null
    private var mItemDecoration: SpacesItemDecoration? = null

    override fun getLayoutResId(): Int {
        return R.layout.key_move_gallery
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            mActivity = activity as AKeyToMoveActivity
            mAdapter = KeyMoveGalleryAdapter(it).apply {
                setHasStableIds(true)
                setOnRecyclerItemClickListener(this@KeyMoveGalleryFragment)
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        (mActivity as? AKeyToMoveActivity)?.apply {
            if (this.mFromSystemConfigModeChange && this.mExistFragmentCount > 1) {
                this.mExistFragmentCount -= 1
                if (this.mExistFragmentCount == 1) {
                    this.mFromSystemConfigModeChange = false
                }
                onDestroy()
                return
            }
        }
        super.onViewCreated(view, savedInstanceState)
    }

    override fun initView(view: View) {
        mRootView = view.findViewById(R.id.coordinator_layout)
        mAppBarLayout = view.findViewById(R.id.appbar_layout)
        mToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        mRecyclerView = view.findViewById(R.id.recycler_view)
        initToolbar()
    }

    private fun initToolbar() {
        mRootView?.apply {
            setPadding(paddingLeft,
                COUIPanelMultiWindowUtils.getStatusBarHeight(mActivity), paddingRight, paddingBottom)
        }
        mActivity?.apply {
            setSupportActionBar(mToolbar)
            supportActionBar?.let {
                it.setDisplayHomeAsUpEnabled(true)
                it.setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        mToolbar?.apply {
            titleMarginStart = 0
            isTitleCenterStyle = false
            inflateMenu(com.filemanager.common.R.menu.file_list_selected_mode_menu)

            (menu.findItem(com.filemanager.common.R.id.action_select_all)?.actionView as? CheckBox)?.apply {
                setPadding(ViewHelper.dip2px(MyApplication.sAppContext, KtConstants.TOOL_BAR_CHECKBOX_MARGIN_SIZE), paddingTop, paddingRight, paddingBottom)
                setOnClickListener {
                    clickToolbarSelectAll()
                }
                isChecked = mListSecond?.getChosenState() == Visitable.ALL_BE_CHOSEN
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        mRecyclerView?.let { recyclerView ->
            val verticalSpacingSize = MyApplication.appContext.resources
                .getDimensionPixelSize(com.filemanager.common.R.dimen.weixin_grid_vertical_spacing)
            recyclerView.apply {
                mItemDecoration = SpacesItemDecoration(ItemDecorationFactory.GRID_ITEM_COUNT_4, verticalSpacingSize, false)
                addItemDecoration(mItemDecoration!!)
                mLayoutManager = GridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_4)
                isNestedScrollingEnabled = true
                clipToPadding = false
                layoutManager = mLayoutManager!!
                itemAnimator?.apply {
                    changeDuration = 0
                    addDuration = 0
                    removeDuration = 0
                    moveDuration = 0
                }
            }
            mAdapter.let {
                recyclerView.adapter = it
            }
            mToolbar?.post {
                updateGridSpanCount()
                if (isAdded) {
                    recyclerView.setPadding(recyclerView.paddingLeft, KtViewUtils.getRecyclerViewTopPadding(mAppBarLayout),
                        recyclerView.paddingRight, MyApplication.sAppContext.resources.getDimensionPixelSize(
                            com.filemanager.common.R.dimen.ftp_text_margin_bottom))
                }
            }
        }
    }

    override fun startObserve() {
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        mListSecond?.let { mAdapter?.setData(it.mChild) }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        if (!hidden) {
            mListSecond?.let { mAdapter?.setData(it.mChild) }
        } else {
            mListSecond = null
        }
        refreshToolbar()
        super.onHiddenChanged(hidden)
    }

    override fun onResumeLoadData() {
        mListSecond?.let { mAdapter?.setData(it.mChild) }
    }

    private fun clickToolbarSelectAll() {
        launch(Dispatchers.Main) {
            val list = withContext(Dispatchers.Default) {
                mListSecond?.setChosenState(if (mListSecond?.getChosenState() == Visitable.ALL_BE_CHOSEN) {
                    Visitable.NOTHING_BE_CHOSEN
                } else {
                    Visitable.ALL_BE_CHOSEN
                })
                return@withContext mListSecond?.mChild
            } ?: return@launch
            mAdapter?.setData(list)
            refreshToolbar()
        }
    }

    fun onMenuItemSelected(item: MenuItem?): Boolean {
        if ((null == mActivity) || (null == item) || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            Log.w(TAG, "onMenuItemSelected : false")
            return false
        }
        when (item.itemId) {
            android.R.id.home -> {
                launch {
                    mListSecond?.let {
                        if (mActivity is AKeyToMoveActivity) {
                            (mActivity as AKeyToMoveActivity).switchToChoiceFragment(
                                listSecond = ListSecond(it.mTitle, it.mChild))
                        }
                    }
                }
            }
        }
        return true
    }

    private fun refreshToolbar() {
        mToolbar?.apply {
            launch(Dispatchers.Main) {
                val item = menu?.findItem(com.filemanager.common.R.id.action_select_all)
                val selectAll = withContext(Dispatchers.Default) {
                    mListSecond?.getSelectedFile()?.size ?: 0 == mListSecond?.mChild?.size ?: 0
                }
                item?.setTitle(if (selectAll) {
                    com.filemanager.common.R.string.unselect_all
                } else {
                    com.filemanager.common.R.string.file_list_editor_select_all
                })
                (item?.actionView as? CheckBox)?.run {
                    isChecked = selectAll
                }
                val checkedCount = withContext(Dispatchers.Default) {
                    mListSecond?.getSelectedFile()?.size ?: 0
                }
                val res = MyApplication.sAppContext.resources
                var tempTitle = res.getString(com.filemanager.common.R.string.akey_to_move_preview_select_photo)
                if (checkedCount > 0) {
                    tempTitle = res.getQuantityString(com.filemanager.common.R.plurals.mark_selected_items_new, checkedCount, checkedCount)
                }
                title = tempTitle
            }
        }
    }

    override fun pressBack(): Boolean {
        if (mActivity is AKeyToMoveActivity) {
            (mActivity as AKeyToMoveActivity).switchToChoiceFragment()
            return true
        }
        return false
    }

    fun setSources(listSecond: ListSecond) {
        mListSecond = listSecond
    }

    override fun onItemClick(view: View, position: Int) {
        mListSecond?.mChild?.get(position)?.let {
            it.setChosenState(
                    if (it.getChosenState() == Visitable.ALL_BE_CHOSEN) {
                        Visitable.NOTHING_BE_CHOSEN
                    } else {
                        Visitable.ALL_BE_CHOSEN
                    })
            mAdapter?.setData(mListSecond!!.mChild)
        }
        refreshToolbar()
    }

    override fun onItemLongClick(view: View, position: Int) {
        onItemClick(view, position)
    }

    private fun updateGridSpanCount() {
        val spanCount = ItemDecorationFactory.getGridItemCount(activity, KtConstants.SCAN_MODE_GRID,
            ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM)
        mItemDecoration?.mSpanCount = spanCount
        mLayoutManager?.spanCount = spanCount
        mRecyclerView?.let {
            val itemSpace = MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.weixin_grid_vertical_spacing)
            mAdapter?.apply {
                setItemWidth(KtViewUtils.getGridItemWidth(mActivity!!, itemSpace, spanCount, itemSpace * 2))
                notifyDataSetChanged()
            }
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            updateGridSpanCount()
        }
    }

    override fun getViewModel(): ViewModel? = null

    override fun getRecyclerView() = mRecyclerView
}