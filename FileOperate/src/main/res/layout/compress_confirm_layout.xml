<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.COUIDividerAppBarLayout
            android:id="@+id/appbar"
            style="@style/CommonAppBarStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/color_transparent"
            android:clickable="true"
            android:focusable="true"
            android:paddingLeft="@dimen/dimen_0dp"
            android:paddingRight="@dimen/dimen_0dp"
            app:elevation="@dimen/toolbar_elevation"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.coui.appcompat.toolbar.COUIToolbar
                android:id="@+id/toolbar"
                style="@style/COUIToolBarInAppBarLayoutStyle"
                android:layout_width="match_parent"
                android:background="@null"
                app:supportTitleTextAppearance="@style/textAppearanceSecondTitle"
                app:titleCenter="false" />
        </com.google.android.material.appbar.COUIDividerAppBarLayout>

        <TextView
            android:id="@+id/name_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_10dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:text="@string/sort_by_name"
            android:textAppearance="@style/couiTextHeadlineXS"
            android:textColor="?attr/couiColorLabelPrimary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appbar" />

        <com.coui.appcompat.edittext.COUIInputView
            android:id="@+id/input_compress_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginEnd="@dimen/dimen_16dp"
            android:layout_marginBottom="18dp"
            app:couiEditLineColor="true"
            app:couiEnableError="true"
            app:couiEnableInputCount="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/name_tv" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/location_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/dimen_12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/input_compress_name">

            <TextView
                android:id="@+id/location_title_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_10dp"
                android:text="@string/dialog_status_file_position_new"
                android:textAppearance="@style/couiTextHeadlineXS"
                android:textColor="?attr/couiColorLabelPrimary"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/location_text"
                android:layout_width="@dimen/dimen_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_2dp"
                android:layout_marginBottom="@dimen/dimen_10dp"
                android:ellipsize="middle"
                android:maxLines="1"
                android:text=""
                android:textAppearance="@style/couiTextBodyM"
                android:textColor="?attr/couiColorLabelSecondary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/modify_text"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/location_title_tv" />

            <TextView
                android:id="@+id/modify_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@string/compress_modify"
                android:paddingVertical="@dimen/dimen_10dp"
                android:text="@string/compress_modify"
                android:textAppearance="@style/couiTextButtonM"
                android:textColor="?attr/couiColorLabelTheme"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/couiColorBackgroundElevated"
            android:minHeight="@dimen/operation_btn_background_height"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/location_layout">

            <View
                android:id="@+id/line_divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_background_height"
                android:layout_marginHorizontal="@dimen/dimen_16dp"
                android:background="?attr/couiColorDivider"
                android:forceDarkAllowed="false" />

            <com.coui.appcompat.button.COUIButton
                android:id="@+id/btn_ok"
                style="@style/Widget.COUI.Button.Large.ButtonNew"
                android:contentDescription="@string/dialog_ok"
                android:layout_gravity="center"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxWidth="@dimen/coui_single_larger_btn_width"
                android:text="@string/dialog_ok"
                app:layout_constraintWidth_max="@dimen/coui_single_larger_btn_width" />
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>