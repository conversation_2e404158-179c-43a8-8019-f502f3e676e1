/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - OnScriptDetectedCallback.kt
 * Description:
 *     The callback for detect simulate script script.
 *
 * Version: 1.0
 * Date: 2024-08-20
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-08-20   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.sdk

import androidx.annotation.Keep
import androidx.annotation.MainThread

@Keep
fun interface OnScriptDetectedCallback {

    /**
     * Async get the result about whether can detect script to execute skill
     *
     * @param hasScriptDetected true is has script to execute, false is none.
     */
    @MainThread
    fun onScriptDetected(hasScriptDetected: Boolean)
}