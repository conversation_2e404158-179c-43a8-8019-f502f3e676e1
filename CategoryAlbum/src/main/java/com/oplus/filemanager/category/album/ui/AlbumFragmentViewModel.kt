/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.album
 * * Version     : 1.0
 * * Date        : 2020/6/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.album.ui

import android.content.ContentValues
import android.content.Context
import androidx.activity.ComponentActivity
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.ALBUM_SCAN_MODE_SP_KEY
import com.filemanager.common.constants.KtConstants.PICKER_ALBUM_SCAN_MODE_SP_KEY
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.IPickerManager
import com.filemanager.common.interfaces.LoadingLoaderListener
import com.filemanager.common.interfaces.fileoprate.IFileActionObserver
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.interfaces.fileoprate.IFileOperateAction
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.wrapper.AlbumLoadResult
import com.filemanager.common.wrapper.ImageFileWrapper
import com.oplus.filemanager.interfaze.recyclebin.IRecycleBin
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

open class AlbumFragmentViewModel : SelectionViewModel<ImageFileWrapper, BaseUiModel<ImageFileWrapper>>(),
    IFileOperate {

    companion object {
        const val TAG = "AlbumFragmentViewModel"
        const val IMAGES_FRAGMENT_ID = 2
        const val NEED_CSHOT_STATE = 1 shl 0
    }

    val mModeState = BaseStateModel(MutableLiveData<Int>(KtConstants.LIST_NORMAL_MODE))
    var mContentValues: ContentValues? = null
    val mBrowseModeState by lazy {
        val lastScanMode = ConfigSharedPreferenceUtils.getInt(
            if (isPickerPage) {
                PICKER_ALBUM_SCAN_MODE_SP_KEY
            } else {
                ALBUM_SCAN_MODE_SP_KEY
            },
            0)
        MutableLiveData<Int>(
            if (lastScanMode == 0) {
                KtConstants.SCAN_MODE_GRID
            } else {
                lastScanMode
            }
        )
    }
    private val mImagesLoaderCallBack = ImagesLoaderCallBack(this)
    private var mOperateAction: IFileOperateAction<IFileActionObserver>? = null
    var pickerSelectedKeyList: MutableList<Int>? = null
    var isPickerPage = false
    var mPickerMultipleType: Array<String>? = null
    override fun loadData() {
        mImagesLoaderCallBack.getLoader()?.let { loader ->
            if (isPickerPage) {
                loader.mSortType = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getPickerAlbumKey())
                loader.isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getPickerAlbumKey())
            } else {
                loader.mSortType = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getAlbumKey())
                loader.isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getAlbumKey())
            }
        }
        mImagesLoaderCallBack.getLoader()?.forceLoad()
    }

    fun initLoader(mLoaderController: LoaderController?, contentValues: ContentValues) {
        if (mImagesLoaderCallBack.getLoader() == null) {
            mContentValues = contentValues
            mLoaderController?.initLoader(IMAGES_FRAGMENT_ID, mImagesLoaderCallBack)
        } else {
            loadData()
        }
    }

    class ImagesLoaderCallBack : LoadingLoaderListener<AlbumFragmentViewModel, AlbumLoader, AlbumLoadResult<Int>> {

        constructor(viewModel: AlbumFragmentViewModel) : super(viewModel, viewModel.mDataLoadState)

        override fun onCreateLoader(viewModel: AlbumFragmentViewModel?): AlbumLoader? {
            return if ((viewModel != null) && (viewModel.mContentValues != null)) {
                AlbumLoader(
                    MyApplication.sAppContext,
                    viewModel.mContentValues ?: ContentValues(),
                    viewModel.isPickerPage,
                    viewModel.mPickerMultipleType
                )
            } else null
        }

        override fun onLoadComplete(viewModel: AlbumFragmentViewModel?, d: AlbumLoadResult<Int>?) {
            Log.d(TAG, "AlbumViewModel onLoadFinished size" + d?.mResultList?.size)
            d?.let {
                if (viewModel != null) {
                    viewModel.mModeState.mInitState = true
                    viewModel.launch {
                        val selectedList = ArrayList<Int>()
                        if ((viewModel.mUiState.value?.mSelectedList?.size ?: 0) > 0) {
                            withContext(Dispatchers.IO) {
                                for (selectedFile in viewModel.mUiState.value!!.mSelectedList) {
                                    if (it.mResultMap.containsKey(selectedFile)) {
                                        selectedList.add(selectedFile)
                                    }
                                }
                            }
                        }
                        viewModel.pickerSelectedKeyList?.apply {
                            Log.d(TAG, "onLoadComplete pickerSelectedKeyList $size")
                            for (selectedFile in this) {
                                if (d.mResultMap.containsKey(selectedFile)) {
                                    selectedList.add(selectedFile)
                                }
                            }
                        }
                        if (it.mResultList.isEmpty() && (viewModel.mModeState.mListModel.value == KtConstants.LIST_SELECTED_MODE)) {
                            Log.d(TAG, "onLoadComplete mResultList is empty change to normal mode")
                            viewModel.mModeState.mListModel.value = KtConstants.LIST_NORMAL_MODE
                        }
                        if (it.mResultList.isNotEmpty() && it.mResultList.last().mFileWrapperViewType != BaseFileBean.TYPE_FILE_LIST_FOOTER) {
                            val footItem = ImageFileWrapper()
                            footItem.mFileWrapperViewType = BaseFileBean.TYPE_FILE_LIST_FOOTER
                            it.mResultList.add(footItem)
                            Log.d(TAG, "checkFootDataAdded add foot")
                        }
                        viewModel.mUiState.postValue(
                            BaseUiModel(
                                it.mResultList,
                                viewModel.mModeState,
                                selectedList,
                                it.mResultMap
                            )
                        )
                    }
                } else {
                    Log.d(TAG, "onLoadComplete viewModel is null")
                }
            }
        }
    }

    override fun getRealFileSize(): Int {
        mUiState.value?.mFileList?.let {
            return if (it.isNotEmpty() && (it.last().mFileWrapperViewType == BaseFileBean.TYPE_FILE_LIST_FOOTER)) {
                Log.d(TAG, "getRealFileSize ${it.size - 1}")
                it.size - 1
            } else {
                Log.d(TAG, "getRealFileSize ${it.size}")
                it.size
            }
        }
        return 0
    }

    override fun onDelete(activity: ComponentActivity): Boolean {
        Log.d(TAG, "onDelete start")
        val recycleBinAction = Injector.injectFactory<IRecycleBin>()
        mOperateAction =
            recycleBinAction?.getFileActionDelete(
                activity,
                getSelectItems(),
                mUiState.value?.mSelectedList?.size == getRealFileSize(),
                CategoryHelper.CATEGORY_IMAGE,
                NEED_CSHOT_STATE)
        mOperateAction?.execute(object : IFileActionObserver {

            override fun onActionDone(result: Boolean, data: Any?) {
                changeListMode(KtConstants.LIST_NORMAL_MODE)
                delayLoadData()
                if (activity is BaseVMActivity) {
                    activity.onRefreshData()
                }
            }

            override fun onActionCancelled() {
            }

            override fun onActionReloadData() {
                delayLoadData()
            }

            override fun onActionReShowDialog() {
            }

            override fun isShowDialog(): Boolean {
                return false
            }
        })
            ?: Log.w(TAG, "onDelete failed: action get null")
        return true
    }

    override fun isShowDialog(): Boolean {
        return mOperateAction?.isShowDialog() ?: false
    }

    fun pressBack(): Boolean {
        mModeState.let {
            return if (it.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
                changeListMode(KtConstants.LIST_NORMAL_MODE)
                true
            } else {
                false
            }
        }
    }

    fun clickToolbarSelectAll() {
        if (getRealFileSize() == mUiState.value?.mSelectedList?.size) {
            mUiState.value?.mSelectedList?.clear()
            mUiState.value = mUiState.value
        } else {
            mUiState.value?.mSelectedList?.clear()
            mUiState.value?.mFileList?.let { files ->
                files.filter {
                    it.mFileWrapperViewType == null
                }.forEach {
                    mUiState.value?.mSelectedList?.add(it.mData?.toLowerCase()?.hashCode() ?: 0)
                }
            }
            mUiState.value = mUiState.value
        }
    }

    fun clickScanModeItem(context: Context? = null) {
        if (mBrowseModeState.value == KtConstants.SCAN_MODE_LIST) {
            mBrowseModeState.value = KtConstants.SCAN_MODE_GRID
            StatisticsUtils.onCommon(
                context,
                StatisticsUtils.SCAN_MODE_PIC_SWITCH,
                hashMapOf(StatisticsUtils.SCAN_MODE_PIC_SWITCH to "0")
            )
        } else {
            mBrowseModeState.value = KtConstants.SCAN_MODE_LIST
            StatisticsUtils.onCommon(
                context,
                StatisticsUtils.SCAN_MODE_PIC_SWITCH,
                hashMapOf(StatisticsUtils.SCAN_MODE_PIC_SWITCH to "1")
            )
        }
        mBrowseModeState.value?.apply {
            ConfigSharedPreferenceUtils.putInt(
                if (isPickerPage) {
                PICKER_ALBUM_SCAN_MODE_SP_KEY
            } else {
                ALBUM_SCAN_MODE_SP_KEY
            }, this)
        }
    }

    override fun getRecyclerViewScanMode(): com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE {
        return if (mBrowseModeState.value == KtConstants.SCAN_MODE_GRID) {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.GRID
        } else {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.LIST
        }
    }

    fun onConfigurationChanged() {
        mOperateAction?.hideDialog()
    }

    fun sortReload() {
        mImagesLoaderCallBack.getLoader()?.setSort(-1)
        loadData()
    }

    fun setSort(type: Int) {
        mImagesLoaderCallBack.getLoader()?.setSort(type)
    }
    fun togglePickerSelectItem(file: BaseFileBean?, pickerManager: IPickerManager?) {
        if (file == null) {
            pickerSelectedKeyList?.forEach {
                pickerManager?.removeFile(it, BaseFileBean())
            }
            pickerSelectedKeyList?.clear()
            return
        }
        uiState.value?.apply {
            val key = file.mData?.toLowerCase()?.hashCode() ?: 0
            if (selectedList.contains(key).not()) {
                pickerSelectedKeyList?.add(key)
            } else {
                pickerSelectedKeyList?.remove(key)
            }
        }
    }
}