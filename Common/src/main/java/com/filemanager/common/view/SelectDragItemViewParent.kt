/***********************************************************
 ** Copyright (C), 2020-2025 Oplus. All rights reserved.
 ** File:  - SelectDragItemViewParent.kt
 ** Description: Drag animation build
 ** Version: 1.0
 ** Date : 2025/04/10
 ** Author: zhangyitong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  zhangyitong  2025/04/10    1.0        create
 ****************************************************************/
package com.filemanager.common.view

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.os.Looper
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import androidx.core.content.ContextCompat
import androidx.dynamicanimation.animation.FloatValueHolder
import com.bumptech.glide.disklrucache.DiskLruCache.Value
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.coui.appcompat.contextutil.COUIContextUtil
import com.filemanager.common.R
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_GRID
import com.filemanager.common.dragselection.FileDragDropScanner.Companion.STATUS_LIST
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.resources
import com.oplus.dropdrag.SelectionTracker
import org.koin.core.definition.indexKey
import kotlin.math.pow
import kotlin.math.sqrt

class SelectDragItemViewParent(
    private val context: Context,
    private val eventX: Float,
    private val eventY: Float,
    private var startDragAndDrop: IStartDragAndDrop? = null,
    private var viewMode: SelectionTracker.LAYOUT_TYPE?,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.editTextStyle
) : View(context, attrs, defStyleAttr) {
    companion object {
        private const val TAG = "SelectDragItemViewParent"
        const val DRAG_ITEM_OPACITY = 0.3f
        const val ALPHA_INDEX_VALUE = 2
        const val ALPHA = 255
        const val MASK_ALPHA = 0.1f
        const val SHADOW_ANIM_DURATION = 280L
        private const val PAINT_ALPHA_PERCENTAGE = 0.85f
        private const val PAINT_ALPHA_PERCENTAGE_NONE = 0f
        private const val ROTATION_VALUE = 2f
        private const val ADSORPTION_MAX_RESPONSE_VALUE = 50f
        private const val ANIMATION_DELAY_TIME = 350L
        private const val ALPHA_ANIMATION_DELAY_TIME = 450L
        private const val ANIMATION_RESPONSE_ONE = 0.45f
        private const val ANIMATION_RESPONSE_TWO = 0.4f
        private const val ANIMATION_RESPONSE_THREE = 0.3f
        private const val CALCULATED_DATA = 2
        private const val SHADOW_DX = 0f
        private const val SHADOW_DY = 10f
        private const val ANIMATION_FOLLOW_INTERVAL = 50
        private const val GRID_SHADOW_RADIUS: Float = 20f
        private const val SINGLE_ANIMATION_COUNT: Int = 7
        private const val DRAG_ANIMATION_COUNT: Int = 10
        private const val DROP_ANIMATION_COUNT: Int = 8
        private const val GROUN_BOUND = 0.2f
        private const val MOVE_BOUNCE = 0F
        private const val MOVE_RESPONSE = 0.1F
    }

    private var radiusValue = resources().getDimension(R.dimen.dimen_4dp)
    private var targetViewList: ArrayList<View>? = null
    private var animationItemList: ArrayList<ItemViewSpringAnimation>? = null
    private var viewType = 0
    private var fileCount = 0
    private val mainHandler = android.os.Handler(Looper.getMainLooper())
    private var animationCount = 0
    private var adsorptionMaxValue = context.resources.getDimensionPixelOffset(R.dimen.drag_item_adsorption_animation_max_value)
    private var lastUpdatePositionTime: Long = 0L
    private var offsetThreeDp: Float = context.resources.getDimensionPixelOffset(R.dimen.dimen_3dp).toFloat()
    private var offsetTwoDp: Float = context.resources.getDimensionPixelOffset(R.dimen.dimen_2dp).toFloat()
    var drag = false
    private var bitmapHashMap = HashMap<Int, Triple<Float, Float, View>>()

    private val roundRectBackground = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        isAntiAlias = true
        style = Paint.Style.FILL
        color = COUIContextUtil.getColor(context, com.support.appcompat.R.color.coui_color_background_with_card)
    }

    private val markPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        isAntiAlias = true
        style = Paint.Style.FILL
        alpha = 0
        color = Color.WHITE
    }

    init {
        isForceDarkAllowed = false
        viewType = if (viewMode == SelectionTracker.LAYOUT_TYPE.LIST) {
            STATUS_LIST
        } else {
            STATUS_GRID
        }
    }

    fun cancelUpAnimation() {
        animationItemList?.forEach { animation ->
            setDropAnimationArgs(animation)
            animation.updateAnimationTargetValue(1f, 1f, 1f, 0f, 0f, 0f)
            animation.cancelUpAnimation(animation.initialPositionX, animation.initialPositionY)
        }
    }

    fun updateTargetPosition(eventX: Float, eventY: Float) {
        if (eventX == this.eventX && eventY == this.eventY) return
        val listSize = animationItemList?.size ?: 0
        animationItemList?.forEachIndexed { index, animationItem ->
            animationItem.apply {
                val targetPositionX = eventX - (view.width * scaleWidthMultiple) / CALCULATED_DATA + displacementOffsetX
                val targetPositionY = eventY - (view.height * scaleHeightMultiple) / CALCULATED_DATA + displacementOffsetY
                updateAnimationTargetValue(targetPositionX, targetPositionY)
                setAnimationMoveResponse()
                val alpha = getAlphaValue(index, listSize - 1)
                updateAnimationTargetValue(
                    scaleWidthMultiple,
                    scaleHeightMultiple,
                    alpha,
                    GRID_SHADOW_RADIUS,
                    targetRotationValue,
                    MASK_ALPHA
                )
            }
        }
    }

    fun addTargetView(viewList: ArrayList<View>, state: Int = 0): HashMap<Int, Triple<Float, Float, View>> {
        drag = state == 0
        fileCount = viewList.size
        if (targetViewList == null) {
            targetViewList = ArrayList()
        }
        if (animationItemList == null) {
            animationItemList = ArrayList()
        }
        targetViewList?.clear()
        targetViewList?.addAll(viewList)
        targetViewList?.let {
            createAnimation()
        }
        return bitmapHashMap
    }

    @SuppressLint("ResourceAsColor")
    private fun createAnimation() {
        animationItemList?.clear()
        val topView = DragUtils.getShadowView()?.get()
        if (drag && topView != null) {
            targetViewList?.remove(topView)
            targetViewList?.add(topView)
        }
        targetViewList?.let { list ->
            bitmapHashMap.clear()
            val listSize = list.size - 1
            for ((index, view) in list.withIndex()) {
                val intArray = IntArray(CALCULATED_DATA)
                view.getLocationOnScreen(intArray)
                val itemViewSpringAnimation: ItemViewSpringAnimation = if (drag) {
                    ItemViewSpringAnimation(view).apply {
                        initDragAnimation(index, listSize, intArray, view)
                        if (index <= CALCULATED_DATA && !bitmapHashMap.containsKey(index)) {
                            viewBitmap?.let { bitmapHashMap[index] = Triple(scaleWidthMultiple, scaleHeightMultiple, list[listSize - index]) }
                        }
                    }
                } else {
                    ItemViewSpringAnimation(view).apply { initDropAnimation(index, view, intArray, listSize) }
                }
                view.alpha = DRAG_ITEM_OPACITY
                animationItemList?.add(itemViewSpringAnimation)
                startAnimation(itemViewSpringAnimation)
            }
        }
    }

    private fun ItemViewSpringAnimation.initDropAnimation(index: Int, view: View, intArray: IntArray, listSize: Int) {
        val (scaleWidthMultiple, scaleHeightMultiple) = setScaleMultiple()
        val alpha = getAlphaValue(index, listSize)
        setAnimationStartValue(
            scaleWidthMultiple,
            scaleHeightMultiple,
            alpha,
            ROTATION_VALUE,
            GRID_SHADOW_RADIUS
        )
        setAnimationStartValue(
            eventX - (view.width * scaleWidthMultiple) / CALCULATED_DATA + displacementOffsetX,
            eventY - (view.height * scaleHeightMultiple) / CALCULATED_DATA + displacementOffsetY
        )
        setInitSpringAnimationFinalValue(intArray[0].toFloat(), intArray[1].toFloat())
        setInitSpringAnimationFinalValue(1f, 1f, 1f, 0f, 0f)
    }

    private fun ItemViewSpringAnimation.initDragAnimation(index: Int, listSize: Int, intArray: IntArray, view: View) {
        var (rotationValue, displacementOffsetX, displacementOffsetY) = calculateRotationAndDisplacement(index, listSize)
        // 缩放比例
        var (scaleWidthMultiple, scaleHeightMultiple) = setScaleMultiple()
        setAnimationStartValue(1f, 1f, 1f, 0f, 0f)
        setAnimationStartValue(intArray[0].toFloat(), intArray[1].toFloat())
        setDisplacementOffset(displacementOffsetX, displacementOffsetY)
        val targetPositionX = eventX - (view.width * scaleWidthMultiple) / CALCULATED_DATA + displacementOffsetX
        val targetPositionY = eventY - (view.height * scaleHeightMultiple) / CALCULATED_DATA + displacementOffsetY
        setInitSpringAnimationFinalValue(targetPositionX, targetPositionY)
        val alpha = getAlphaValue(index, listSize)
        setInitSpringAnimationFinalValue(scaleWidthMultiple, scaleHeightMultiple, alpha, GRID_SHADOW_RADIUS, rotationValue)
    }

    private fun getAlphaValue(index: Int, listSize: Int): Float {
        if (listSize <= ALPHA_INDEX_VALUE) return PAINT_ALPHA_PERCENTAGE
        val alpha = if (index < listSize - ALPHA_INDEX_VALUE) {
            PAINT_ALPHA_PERCENTAGE_NONE
        } else {
            PAINT_ALPHA_PERCENTAGE
        }
        return alpha
    }

    private fun startAnimation(itemViewSpringAnimation: ItemViewSpringAnimation) {
        // 单个动画
        if (targetViewList?.size == 1) {
            itemViewSpringAnimation.setSpringForce(
                ANIMATION_RESPONSE_TWO,
                ANIMATION_RESPONSE_THREE,
                0f,
                ANIMATION_RESPONSE_THREE
            )
            itemViewSpringAnimation.setGroupForceBounce(ANIMATION_RESPONSE_TWO)
            itemViewSpringAnimation.startSingleAnimation()
            return
        }
        if (drag) {
            setDragAnimationArgs(itemViewSpringAnimation)
            itemViewSpringAnimation.startDragAnimation()
        } else {
            setDropAnimationArgs(itemViewSpringAnimation)
            itemViewSpringAnimation.startDropAnimation()
        }
    }

    private fun setDragAnimationArgs(itemViewSpringAnimation: ItemViewSpringAnimation) {
        itemViewSpringAnimation.setSpringForce(
            ANIMATION_RESPONSE_TWO,
            ANIMATION_RESPONSE_THREE,
            ANIMATION_RESPONSE_THREE,
            ANIMATION_RESPONSE_THREE
        )
        itemViewSpringAnimation.setGroupForceBounce(ANIMATION_RESPONSE_ONE)
    }

    private fun setDropAnimationArgs(itemViewSpringAnimation: ItemViewSpringAnimation) {
        itemViewSpringAnimation.setSpringForce(
            ANIMATION_RESPONSE_ONE,
            ANIMATION_RESPONSE_THREE,
            ANIMATION_RESPONSE_ONE,
            ANIMATION_RESPONSE_ONE
        )
        itemViewSpringAnimation.setGroupForceBounce(ANIMATION_RESPONSE_ONE)
    }

    private fun calculateRotationAndDisplacement(index: Int, listSize: Int): Triple<Float, Float, Float> {
        var rotationValue: Float
        var displacementOffsetX: Float
        var displacementOffsetY: Float
        if (index == 0 || index == listSize - CALCULATED_DATA) {
            rotationValue = 0f
            displacementOffsetX = 0f
            displacementOffsetY = 0f
        } else {
            when (index % CALCULATED_DATA) {
                0 -> {
                    rotationValue = -ROTATION_VALUE
                    displacementOffsetX = offsetThreeDp
                    displacementOffsetY = -offsetThreeDp
                }
                1 -> {
                    rotationValue = ROTATION_VALUE
                    displacementOffsetX = -offsetTwoDp
                    displacementOffsetY = -offsetThreeDp
                }

                else -> {
                    rotationValue = 0f
                    displacementOffsetX = 0f
                    displacementOffsetY = 0f
                }
            }
            if (listSize >= CALCULATED_DATA) {
                when (index) {
                    listSize -> {
                        rotationValue = -ROTATION_VALUE
                        displacementOffsetX = offsetThreeDp
                        displacementOffsetY = -offsetThreeDp
                    }
                    listSize - 1 -> {
                        rotationValue = ROTATION_VALUE
                        displacementOffsetX = -offsetTwoDp
                        displacementOffsetY = -offsetThreeDp
                    }
                }
                return Triple(rotationValue, displacementOffsetX, displacementOffsetY)
            } else if (listSize >= 1) {
                when (index) {
                    listSize -> {
                        rotationValue = ROTATION_VALUE
                        displacementOffsetX = -offsetTwoDp
                        displacementOffsetY = -offsetThreeDp
                    }
                }
                return Triple(rotationValue, displacementOffsetX, displacementOffsetY)
            }
        }
        return Triple(rotationValue, displacementOffsetX, displacementOffsetY)
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        animationItemList?.let {
            it.forEachIndexed { index, animationItem ->
                val view = animationItem.view
                canvasAnimation(view, canvas, animationItem, index == it.size - 1)
            }
        }
    }

    private fun canvasAnimation(view: View, canvas: Canvas, animationItem: ItemViewSpringAnimation, canvasMask: Boolean) {
        val viewPosition = IntArray(CALCULATED_DATA)
        view.getLocationOnScreen(viewPosition)
        val viewWidth = view.width * animationItem.scaleValueList[0]
        val viewHeight = view.height * animationItem.scaleValueList[1]
        val saveCount = canvas.saveLayerAlpha(
            animationItem.positionList[0] - viewWidth / CALCULATED_DATA,
            animationItem.positionList[1] - viewHeight / CALCULATED_DATA,
            animationItem.positionList[0] + viewWidth,
            animationItem.positionList[1] + viewHeight,
            (animationItem.alphaValue * ALPHA).toInt()
        )
        // 应用旋转
        canvas.rotate(
            animationItem.currentRotationValue,
            animationItem.positionList[0] + (viewWidth) / CALCULATED_DATA,
            animationItem.positionList[1] + (viewHeight) / CALCULATED_DATA
        )
        // 绘制矩形作为背景
        drawRoundRect(roundRectBackground, animationItem, canvas, view)
        animationItem.viewBitmap?.let {
            val rectF = RectF(
                animationItem.positionList[0],
                animationItem.positionList[1],
                animationItem.positionList[0] + viewWidth,
                animationItem.positionList[1] + viewHeight
            )
            canvas.drawBitmap(it, null, rectF, null)
        }
        if (canvasMask) {
            // 绘制颜色为百分之十的#000000的蒙层
            markPaint.alpha = (animationItem.maskAlphaValue * ALPHA).toInt()
            drawRoundRect(markPaint, animationItem, canvas, view)
        }
        canvas.restoreToCount(saveCount)
    }

    private fun drawRoundRect(paint: Paint, animationItem: ItemViewSpringAnimation, canvas: Canvas, view: View) {
        canvas.drawRoundRect(
            animationItem.positionList[0],
            animationItem.positionList[1],
            animationItem.positionList[0] + view.width * animationItem.scaleValueList[0],
            animationItem.positionList[1] + view.height * animationItem.scaleValueList[1],
            animationItem.radiusValue,
            animationItem.radiusValue,
            paint
        )
    }

    inner class ItemViewSpringAnimation(val view: View) {
        private val childView = View(context)
        var width = 0
        var height = 0
        var targetPositionX: Float = 0f
        var targetPositionY: Float = 0f
        var initialPositionX: Float = 0f
        var initialPositionY: Float = 0f
        var targetRotationValue: Float = 0f
        var scaleWidthMultiple = 0f
        var scaleHeightMultiple = 0f
        var displacementOffsetX = 0f
        var displacementOffsetY = 0f
        var animationTotalCount = 0

        // 位移动画 聚合/分散
        private var groupValueHolderX = FloatValueHolder(initialPositionX)
        private var groupValueHolderY = FloatValueHolder(initialPositionY)
        private var groupAnimationX = COUISpringAnimation(groupValueHolderX)
        private var groupAnimationY = COUISpringAnimation(groupValueHolderY)
        private val groupForceX = COUISpringForce()
        private val groupForceY = COUISpringForce()
        // 吸附动画
        private var adsorptionValueHolderY = FloatValueHolder(initialPositionY)
        private var adsorptionValueHolderX = FloatValueHolder(initialPositionX)
        private val adsorptionForceX = COUISpringForce()
        private val adsorptionForceY = COUISpringForce()
        private var adsorptionAnimationX = COUISpringAnimation(adsorptionValueHolderX)
        private var adsorptionAnimationY = COUISpringAnimation(adsorptionValueHolderY)
        // 缩放动画
        var scaleValueList = FloatArray(CALCULATED_DATA)
        private var scaleXAnimation = COUISpringAnimation(childView, COUISpringAnimation.SCALE_X, scaleWidthMultiple)
        private var scaleYAnimation = COUISpringAnimation(childView, COUISpringAnimation.SCALE_Y, scaleHeightMultiple)
        // 不透明度动画
        var alphaValue = 1f
        private var alphaAnimation = COUISpringAnimation(childView, COUISpringAnimation.ALPHA, PAINT_ALPHA_PERCENTAGE)
        // 旋转动画
        var currentRotationValue = 0f
        private var rotationAnimation = COUISpringAnimation(childView, COUISpringAnimation.ROTATION, ROTATION_VALUE)
        // 圆角动画
        var radiusValue = 0f
        private var radiusAnimation = COUISpringAnimation(childView, COUISpringAnimation.ROTATION,
            GRID_SHADOW_RADIUS
        )
        //透明度百分之十的蒙层
        private var  markAnimation = COUISpringAnimation(childView, COUISpringAnimation.ALPHA, PAINT_ALPHA_PERCENTAGE)
        var maskAlphaValue = 0f

        private var differenceX = 0f
        private var differenceY = 0f
        var positionList = ArrayList<Float>(CALCULATED_DATA)
        private val delayTask = Runnable {
            adsorptionAnimationY.cancel()
            adsorptionAnimationX.cancel()
            groupAnimationX.setStartValue(positionList[0])
            groupAnimationY.setStartValue(positionList[1])
            scaleXAnimation.start()
            scaleYAnimation.start()
            groupAnimationX.start()
            groupAnimationY.start()
            rotationAnimation.start()
            markAnimation.start()
        }
        private val alphaDelayTask = Runnable {
            alphaAnimation.start()
        }
        var viewBitmap: Bitmap? = null

        init {
            view.measure(MeasureSpec.makeMeasureSpec(view.width, MeasureSpec.EXACTLY), MeasureSpec.makeMeasureSpec(view.height, MeasureSpec.EXACTLY))
            width = view.width
            height = view.height
            setAnimationUpdateListener()
            if (view is ViewGroup) {
                viewBitmap = DragUtils.getViewBitmap(view, view, viewMode)
            }
        }

        fun setSpringForce(
            scaleResponse: Float,
            alphaResponse: Float,
            rotationResponse: Float,
            radiusResponse: Float
        ) {
            updateAnimationArgs(scaleXAnimation, scaleResponse)
            updateAnimationArgs(scaleYAnimation, scaleResponse)
            updateAnimationArgs(alphaAnimation, alphaResponse)
            updateAnimationArgs(rotationAnimation, rotationResponse)
            updateAnimationArgs(radiusAnimation, radiusResponse)
            updateAnimationArgs(markAnimation, alphaResponse)
        }

        fun setGroupForceBounce(groupResponse: Float) {
            groupForceX.setBounce(GROUN_BOUND)
            groupForceX.setResponse(groupResponse)
            groupForceY.setBounce(GROUN_BOUND)
            groupForceY.setResponse(groupResponse)
        }

        private fun setAnimationUpdateListener() {
            //监听位移变化
            setGroupAnimationListener()
            //监听吸附位移变化
            setAdsorptionAnimationListener()
            //监听宽度缩放变化
            setScaleAnimationListener()
            //监听旋转变化
            rotationAnimationListener()
            //监听透明度变化
            alphaAnimationListener()
            //监听圆角变化
            radiusAnimationListener()
            //监听蒙层透明度变化
            maskAnimationListener()
        }

        private fun animationFinish() {
            mainHandler.post {
                val isRunning = when(animationTotalCount) {
                    SINGLE_ANIMATION_COUNT -> singleAnimationIsRunning()
                    DRAG_ANIMATION_COUNT -> dragAnimationIsRunning()
                    DROP_ANIMATION_COUNT -> dropAnimationIsRunning()
                    else -> {false}
                }
                if (!isRunning) {
                    animationCount++
                }
                if (animationCount == targetViewList?.size) {
                    animationCount = 0
                    stopDragDropAnimation()
                }
            }
        }

        private fun maskAnimationListener() {
            markAnimation.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d("markAnimation", "markAnimation value is $value")
                maskAlphaValue = value
                invalidate()
            }
            markAnimation.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
        }

        private fun radiusAnimationListener() {
            radiusAnimation.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d("radiusAnimation", "radiusAnimation value is $value")
                radiusValue = value
                invalidate()
            }
            radiusAnimation.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
        }

        private fun alphaAnimationListener() {
            alphaAnimation.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d("alphaAnimation", "alphaAnimation value is $value")
                alphaValue = value
                invalidate()
            }
            alphaAnimation.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
        }

        private fun rotationAnimationListener() {
            rotationAnimation.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d(TAG, "rotationAnimation value is $value")
                currentRotationValue = value
                invalidate()
            }
            rotationAnimation.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
        }

        private fun setScaleAnimationListener() {
            scaleXAnimation.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d("adsorptionAnimationY", "scaleXAnimation value is $value")
                scaleValueList[0] = value
                invalidate()
            }
            //监听高度缩放变化
            scaleYAnimation.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d("adsorptionAnimationY", "scaleYAnimation value is $value")
                scaleValueList[1] = value
                invalidate()
            }
            scaleYAnimation.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
            scaleXAnimation.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
        }

        private fun setAdsorptionAnimationListener() {
            adsorptionAnimationY.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d("adsorptionAnimationY", "adsorptionAnimationY value is $value")
                positionList[1] = value
                invalidate()
            }
            adsorptionAnimationX.addUpdateListener { _, value, _ ->
                Log.d("adsorptionAnimationY", "adsorptionAnimationX value is $value")
                positionList[0] = value
                invalidate()
            }
            adsorptionAnimationX.addEndListener { couiDynamicAnimation, b, value, fl2 ->
                animationFinish()
            }
            adsorptionAnimationY.addEndListener { couiDynamicAnimation, b, value, fl2 ->
                animationFinish()
            }
        }

        private fun setGroupAnimationListener() {
            groupAnimationX.addUpdateListener { animation, value, velocity ->
                Log.d("groupAnimationX", "groupAnimationX value is $value")
                positionList[0] = value
                invalidate()
            }
            groupAnimationY.addUpdateListener { couiDynamicAnimation, value, fl2 ->
                Log.d("groupAnimationX", "groupAnimationY value is $value")
                positionList[1] = value
                invalidate()
            }
            groupAnimationX.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
            groupAnimationY.addEndListener { couiDynamicAnimation, b, fl, fl2 ->
                animationFinish()
            }
        }

        fun setAnimationStartValue(
            scaleWidthMultiple: Float,
            scaleHeightMultiple: Float,
            alphaValue: Float,
            rotation: Float,
            radius: Float
        ) {
            scaleValueList[0] = scaleWidthMultiple
            scaleValueList[1] = scaleHeightMultiple
            scaleXAnimation.setStartValue(scaleWidthMultiple)
            scaleYAnimation.setStartValue(scaleHeightMultiple)
            alphaAnimation.setStartValue(alphaValue)
            rotationAnimation.setStartValue(rotation)
            radiusAnimation.setStartValue(radius)
        }

        fun setAnimationStartValue(
            initialPositionX: Float,
            initialPositionY: Float,
        ) {
            this.initialPositionX = initialPositionX
            this.initialPositionY = initialPositionY
            positionList.add(initialPositionX)
            positionList.add(initialPositionY)
            groupAnimationX.setStartValue(initialPositionX)
            groupAnimationY.setStartValue(initialPositionY)
            adsorptionAnimationY.setStartValue(initialPositionY)
            adsorptionAnimationX.setStartValue(initialPositionX)
            if (drag) {
                markAnimation.setStartValue(0f)
            } else {
                markAnimation.setStartValue(MASK_ALPHA)
            }
        }

        fun setDisplacementOffset(offsetX: Float, offsetY: Float) {
            displacementOffsetX = offsetX
            displacementOffsetY = offsetY
        }

        fun setInitSpringAnimationFinalValue(
            scaleWidthMultiple: Float,
            scaleHeightMultiple: Float,
            currentAlphaValue: Float,
            currentRadiusValue: Float,
            rotationValue: Float
        ) {
            this.targetRotationValue = rotationValue

            //缩放比例
            scaleXAnimation.spring.finalPosition = scaleWidthMultiple
            scaleYAnimation.spring.finalPosition = scaleHeightMultiple

            // 透明度变化
            alphaAnimation.spring.finalPosition = currentAlphaValue

            // 圆角变化
            radiusAnimation.spring.finalPosition = currentRadiusValue

            // 旋转角度
            rotationAnimation.spring.finalPosition = rotationValue

            markAnimation.spring.finalPosition = if (drag) {
                MASK_ALPHA
            } else {
                0f
            }

            val hypotenuseValue = sqrt((differenceX).toDouble().pow(CALCULATED_DATA)
                    + (differenceY).toDouble().pow(CALCULATED_DATA))
            val adsorptionResponse = if (hypotenuseValue > adsorptionMaxValue) {
                ADSORPTION_MAX_RESPONSE_VALUE
            } else {
                (hypotenuseValue.toFloat().div(adsorptionMaxValue) * ADSORPTION_MAX_RESPONSE_VALUE)
            }
            adsorptionForceY.setResponse(adsorptionResponse)
            adsorptionForceX.setResponse(adsorptionResponse)
            groupAnimationX.setSpring(groupForceX)
            groupAnimationY.setSpring(groupForceY)
            adsorptionAnimationY.setSpring(adsorptionForceY)
            adsorptionAnimationX.setSpring(adsorptionForceX)
        }

        fun setInitSpringAnimationFinalValue(
            targetPositionX: Float,
            targetPositionY: Float,
        ) {
            this.targetPositionX = targetPositionX
            this.targetPositionY = targetPositionY
            differenceX = this.targetPositionX - this.initialPositionX
            differenceY = this.targetPositionY - this.initialPositionY
            adsorptionForceY.finalPosition = targetPositionY
            adsorptionForceX.finalPosition = targetPositionX
            groupForceX.finalPosition = targetPositionX
            groupForceY.finalPosition = targetPositionY
        }

        fun setAnimationMoveResponse() {
            updateAnimationArgs(groupAnimationX)
            updateAnimationArgs(groupAnimationY)
            updateAnimationArgs(scaleXAnimation)
            updateAnimationArgs(scaleYAnimation)
            updateAnimationArgs(alphaAnimation)
            updateAnimationArgs(radiusAnimation)
            updateAnimationArgs(rotationAnimation)
            updateAnimationArgs(markAnimation)
        }

        private fun updateAnimationArgs(animation: COUISpringAnimation, response: Float = MOVE_RESPONSE, bounce: Float = MOVE_BOUNCE) {
            animation.spring.setBounce(bounce)
            animation.spring.setResponse(response)
        }

        fun updateAnimationTargetValue(
            scaleWidthMultiple: Float,
            scaleHeightMultiple: Float,
            alphaValue: Float,
            radiusValue: Float,
            rotationValue: Float,
            maskAlphaValue: Float
        ) {
            mainHandler.removeCallbacks(delayTask)
            mainHandler.removeCallbacks(alphaDelayTask)
            scaleXAnimation.animateToFinalPosition(scaleWidthMultiple)
            scaleYAnimation.animateToFinalPosition(scaleHeightMultiple)
            if (alphaValue != this.alphaValue) {
                alphaAnimation.animateToFinalPosition(alphaValue)
            }
            radiusAnimation.animateToFinalPosition(radiusValue)
            if (currentRotationValue != this.targetRotationValue) {
                rotationAnimation.animateToFinalPosition(rotationValue)
            }
            if (maskAlphaValue != this.maskAlphaValue) {
                markAnimation.animateToFinalPosition(maskAlphaValue)
            }
        }

        fun updateAnimationTargetValue(
            targetPositionX: Float,
            targetPositionY: Float,
        ) {
            this.targetPositionX = targetPositionX
            this.targetPositionY = targetPositionY
            adsorptionAnimationX.cancel()
            adsorptionAnimationY.cancel()
            if (!groupAnimationX.isRunning && !groupAnimationY.isRunning) {
                groupAnimationX.setStartValue(positionList[0])
                groupAnimationY.setStartValue(positionList[1])
            }
            groupAnimationX.animateToFinalPosition(targetPositionX)
            groupAnimationY.animateToFinalPosition(targetPositionY)
        }

        fun cancelUpAnimation(
            targetPositionX: Float,
            targetPositionY: Float,
        ) {
            rotationAnimation.setStartValue(currentRotationValue)
            rotationAnimation.animateToFinalPosition(0f)
            this.targetPositionX = targetPositionX
            this.targetPositionY = targetPositionY
            adsorptionAnimationX.cancel()
            adsorptionAnimationY.cancel()
            groupAnimationX.setStartValue(positionList[0])
            groupAnimationY.setStartValue(positionList[1])
            groupAnimationX.animateToFinalPosition(targetPositionX)
            groupAnimationY.animateToFinalPosition(targetPositionY)
        }

        fun startSingleAnimation() {
            animationTotalCount = SINGLE_ANIMATION_COUNT
            scaleXAnimation.start()
            scaleYAnimation.start()
            alphaAnimation.start()
            radiusAnimation.start()
            groupAnimationX.setStartValue(positionList[0])
            groupAnimationY.setStartValue(positionList[1])
            groupAnimationX.start()
            groupAnimationY.start()
            markAnimation.start()
        }

        private fun singleAnimationIsRunning(): Boolean {
            return (scaleXAnimation.isRunning || scaleYAnimation.isRunning || alphaAnimation.isRunning
                    || radiusAnimation.isRunning || groupAnimationX.isRunning || groupAnimationY.isRunning || markAnimation.isRunning)
        }

        fun startDragAnimation() {
            animationTotalCount = DRAG_ANIMATION_COUNT
            radiusAnimation.start()
            adsorptionAnimationY.start()
            adsorptionAnimationX.start()
            mainHandler.postDelayed(delayTask, ANIMATION_DELAY_TIME)
            mainHandler.postDelayed(alphaDelayTask, ALPHA_ANIMATION_DELAY_TIME)
        }

        private fun dragAnimationIsRunning(): Boolean {
            return (scaleXAnimation.isRunning || scaleYAnimation.isRunning
                    || alphaAnimation.isRunning
                    || adsorptionAnimationY.isRunning || adsorptionAnimationX.isRunning
                    || radiusAnimation.isRunning
                    || groupAnimationX.isRunning || groupAnimationY.isRunning
                    || rotationAnimation.isRunning || markAnimation.isRunning)
        }

        fun startDropAnimation() {
            animationTotalCount = DROP_ANIMATION_COUNT
            scaleXAnimation.start()
            scaleYAnimation.start()
            alphaAnimation.start()
            radiusAnimation.start()
            rotationAnimation.start()
            groupAnimationX.start()
            groupAnimationY.start()
            markAnimation.start()
        }

        private fun dropAnimationIsRunning(): Boolean {
            return (scaleXAnimation.isRunning || scaleYAnimation.isRunning
                    || alphaAnimation.isRunning
                    || radiusAnimation.isRunning
                    || groupAnimationX.isRunning || groupAnimationY.isRunning
                    || rotationAnimation.isRunning || markAnimation.isRunning)
        }

        fun setScaleMultiple(): Pair<Float, Float> {
            MeasureSpec.makeMeasureSpec(view.width, MeasureSpec.EXACTLY)
            MeasureSpec.makeMeasureSpec(view.height, MeasureSpec.EXACTLY)
            val (width, height) = DragUtils.getItemSize(
                viewType,
                fileCount,
                view.measuredWidth,
                view.measuredHeight,
                context
            )
            scaleWidthMultiple = (width.toDouble()).div(view.measuredWidth.toDouble()).toFloat()
            scaleHeightMultiple = (height.toDouble()).div(view.measuredHeight.toDouble()).toFloat()
            scaleXAnimation.spring.finalPosition = scaleWidthMultiple
            scaleYAnimation.spring.finalPosition = scaleHeightMultiple
            return Pair(scaleWidthMultiple, scaleHeightMultiple)
        }
    }
    interface IStartDragAndDrop {
        fun startDragAndDrop()
    }

    fun stopDragDropAnimation() {
        startDragAndDrop?.startDragAndDrop()
    }
}