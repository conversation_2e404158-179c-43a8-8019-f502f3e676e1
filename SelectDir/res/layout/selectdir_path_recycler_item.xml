<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignWithParentIfMissing="true"
    android:layout_alignParentTop="true"
    android:background="@drawable/coui_preference_bg_selector"
    android:minHeight="?android:attr/listPreferredItemHeightSmall">

    <FrameLayout
        android:id="@+id/icon_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_marginStart="@dimen/default_margin"
        android:layout_centerVertical="true"
        android:layout_marginTop="@dimen/file_listview_icon_margin_top">

        <com.filemanager.common.view.FileThumbView
            android:id="@+id/file_list_item_icon"
            android:layout_width="@dimen/main_image_width"
            android:layout_height="@dimen/main_image_height"
            android:forceDarkAllowed="false" />
    </FrameLayout>

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/file_list_item_info_margin"
        android:layout_marginTop="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginEnd="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginBottom="@dimen/file_list_right_layout_margin_vertical"
        android:layout_toStartOf="@+id/jump_mark"
        android:layout_toEndOf="@id/icon_layout">

        <com.filemanager.common.view.TextViewSnippet
            android:id="@+id/file_list_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:ellipsize="middle"
            android:maxWidth="@dimen/file_list_item_info_selected_width_new_new"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textSize="@dimen/file_list_item_title_text_size" />

        <TextView
            android:id="@+id/mark_file_list_item_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/file_list_item_title"
            android:layout_marginTop="@dimen/file_list_item_detail_margin_top"
            android:adjustViewBounds="true"
            android:ellipsize="marquee"
            android:maxWidth="@dimen/file_list_item_info_selected_width_new_new"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textColor="@color/black_55_percent"
            android:textSize="@dimen/file_list_item_detail_text_size" />

        <com.filemanager.common.view.TextViewSnippet
            android:id="@+id/another_name_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/file_list_item_title"
            android:ellipsize="end"
            android:paddingTop="@dimen/file_list_item_another_name_padding_top"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textSize="@dimen/file_list_item_detail_text_size" />

    </RelativeLayout>

    <ImageView
        android:id="@+id/jump_mark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/default_margin"
        android:contentDescription="@null"
        android:src="@drawable/coui_btn_next" />

    <View
        android:id="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="0.33dp"
        android:layout_marginStart="@dimen/dimen_76dp"
        android:layout_marginEnd="@dimen/dimen_24dp"
        android:background="?attr/couiColorDivider"
        android:layout_alignParentBottom="true" />

</RelativeLayout>