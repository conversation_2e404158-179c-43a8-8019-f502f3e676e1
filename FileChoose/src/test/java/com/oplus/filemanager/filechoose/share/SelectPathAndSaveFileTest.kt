/*********************************************************************
 * * Copyright (C), 2010-2024 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SelectPathAndSaveFileTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/03/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.filechoose.share

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.PermissionUtils
import com.oplus.filemanager.filechoose.ui.share.SelectPathAndSaveFile
import com.oplus.filemanager.filechoose.ui.share.SelectPathAndSaveFile.Companion.OPLUS_INNER_SEND_ACTION
import com.oplus.filemanager.filechoose.ui.share.ShareActivity
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SelectPathAndSaveFileTest {

    private lateinit var shareActivity: ShareActivity
    private lateinit var intent: Intent
    private lateinit var supportFragmentManager: FragmentManager
    private lateinit var lifecycle: Lifecycle

    @Before
    fun setUp() {
        shareActivity = mockk()
        intent = mockk()
        supportFragmentManager = mockk()
        lifecycle = mockk()
        mockkStatic(IntentUtils::class)
        every { shareActivity.intent } returns intent
        every { shareActivity.supportFragmentManager } returns supportFragmentManager
        every { shareActivity.lifecycle } returns lifecycle
    }

    @After
    fun tearDown() {
        unmockkObject(shareActivity)
        unmockkObject(intent)
        unmockkObject(supportFragmentManager)
        unmockkObject(lifecycle)
        unmockkStatic(IntentUtils::class)
    }

    @Test
    fun `should call savedInstanceState method if call initData if come from onSavedInstanceState`() {
        //given
        val savedInstanceState = mockk<Bundle>()
        val selectPathAndSaveFile =
            spyk(SelectPathAndSaveFile(shareActivity), recordPrivateCalls = true)
        every { savedInstanceState.getString(any()) } returns ""
        every { savedInstanceState.getInt(any()) } returns 0
        every { savedInstanceState.getStringArrayList(any()) } returns arrayListOf()
        every { savedInstanceState.getBoolean(any()) } returns true
        every { savedInstanceState.getBoolean(any(), any()) } returns true
        //when
        selectPathAndSaveFile.initData(savedInstanceState)
        //then
        verify { savedInstanceState.getString(any()) }
        verify { savedInstanceState.getInt(any()) }
        verify { savedInstanceState.getStringArrayList(any()) }
        Assert.assertEquals(selectPathAndSaveFile.fromOplusDocument, true)
    }

    @Test
    fun `should call IntentUtils method if call initData if come from new task`() {
        //given
        val savedInstanceState = mockk<Bundle>()
        val selectPathAndSaveFile =
            spyk(SelectPathAndSaveFile(shareActivity), recordPrivateCalls = true)
        every { savedInstanceState.getBoolean(any()) } returns false
        every { IntentUtils.getParcelable(any(), any()) } returns mockk()
        every { IntentUtils.getBoolean(any(), any(), any()) } returns false
        every { IntentUtils.getInt(any(), any(), any()) } returns -1
        every { IntentUtils.getString(any(), any()) } returns ""
        every { IntentUtils.getStringArrayList(any(), any()) } returns arrayListOf()

        //when
        selectPathAndSaveFile.initData(savedInstanceState)
        //then
        verify { IntentUtils.getString(any(), any()) }
        verify { IntentUtils.getInt(any(), any(), any()) }
        verify { IntentUtils.getStringArrayList(any(), any()) }
        Assert.assertEquals(selectPathAndSaveFile.fromOplusDocument, false)
    }

    @Test
    fun `should call IntentUtils method if call initData if savedInstanceState is null`() {
        //given
        val savedInstanceState = null
        val selectPathAndSaveFile =
            spyk(SelectPathAndSaveFile(shareActivity), recordPrivateCalls = true)
        every { IntentUtils.getParcelable(any(), any()) } returns mockk()
        every { IntentUtils.getBoolean(any(), any(), any()) } returns false
        every { IntentUtils.getInt(any(), any(), any()) } returns -1
        every { IntentUtils.getString(any(), any()) } returns ""
        every { IntentUtils.getStringArrayList(any(), any()) } returns arrayListOf()

        //when
        selectPathAndSaveFile.initData(savedInstanceState)
        //then
        verify { IntentUtils.getString(any(), any()) }
        verify { IntentUtils.getInt(any(), any(), any()) }
        verify { IntentUtils.getStringArrayList(any(), any()) }
        Assert.assertEquals(selectPathAndSaveFile.fromOplusDocument, false)
    }

    @Test
    fun `should call delayFinish method if call openFragmentByAction if action is null`() {
        //given
        every { intent.action } returns null
        val selectPathAndSaveFile =
            spyk(SelectPathAndSaveFile(shareActivity), recordPrivateCalls = true)
        every { selectPathAndSaveFile.delayFinish(any()) } just runs
        every { selectPathAndSaveFile.handleOplusAction() } just runs
        every { selectPathAndSaveFile.handleOtherAction() } just runs
        //when
        selectPathAndSaveFile.openFragmentByAction()
        //then
        verify { selectPathAndSaveFile.delayFinish(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handleOplusAction() }
        verify(inverse = true) { selectPathAndSaveFile.handleOtherAction() }
    }

    @Test
    fun `should call handleOplusAction method if call openFragmentByAction if filePath is OPLUS_INNER_SEND_ACTION`() {
        //given
        every { intent.action } returns OPLUS_INNER_SEND_ACTION
        val ob = SelectPathAndSaveFile(shareActivity)
        ob.filePath = "sdcard/path"
        ob.fromSaveInstance = false
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { selectPathAndSaveFile.delayFinish(any()) } just runs
        every { selectPathAndSaveFile.handleOplusAction() } just runs
        every { selectPathAndSaveFile.handleOtherAction() } just runs
        //when
        selectPathAndSaveFile.openFragmentByAction()
        //then
        verify(inverse = true) { selectPathAndSaveFile.delayFinish(any()) }
        verify { selectPathAndSaveFile.handleOplusAction() }
        verify(inverse = true) { selectPathAndSaveFile.handleOtherAction() }
        Assert.assertEquals(selectPathAndSaveFile.fromSaveInstance, false)
        Assert.assertEquals(selectPathAndSaveFile.hasShowSaveFileDialog, true)
    }

    @Test
    fun `should call handleOtherAction method if call openFragmentByAction if filePath is Action_view`() {
        //given
        every { intent.action } returns Intent.ACTION_VIEW
        val ob = SelectPathAndSaveFile(shareActivity)
        ob.filePath = "sdcard/path"
        ob.fromSaveInstance = false
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { selectPathAndSaveFile.delayFinish(any()) } just runs
        every { selectPathAndSaveFile.handleOplusAction() } just runs
        every { selectPathAndSaveFile.handleOtherAction() } just runs
        //when
        selectPathAndSaveFile.openFragmentByAction()
        //then
        verify(inverse = true) { selectPathAndSaveFile.delayFinish(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handleOplusAction() }
        verify { selectPathAndSaveFile.handleOtherAction() }
        Assert.assertEquals(selectPathAndSaveFile.fromSaveInstance, false)
        Assert.assertEquals(selectPathAndSaveFile.hasShowSaveFileDialog, true)
    }

    @Test
    fun `should call nothing method if call openFragmentByAction if fromSaveInstance is true`() {
        //given
        every { intent.action } returns Intent.ACTION_VIEW
        val ob = SelectPathAndSaveFile(shareActivity)
        ob.fromSaveInstance = true
        ob.filePath = "sdcard/path"
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { selectPathAndSaveFile.delayFinish(any()) } just runs
        every { selectPathAndSaveFile.handleOplusAction() } just runs
        every { selectPathAndSaveFile.handleOtherAction() } just runs
        //when
        selectPathAndSaveFile.openFragmentByAction()
        //then
        verify(inverse = true) { selectPathAndSaveFile.delayFinish(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handleOplusAction() }
        verify(inverse = true) { selectPathAndSaveFile.handleOtherAction() }
        Assert.assertEquals(selectPathAndSaveFile.fromSaveInstance, false)
        Assert.assertEquals(selectPathAndSaveFile.hasShowSaveFileDialog, true)
    }

    @Test
    fun `should call delayFinish when call handleOplusAction if filePath is not null`() {
        //given
        val ob = SelectPathAndSaveFile(shareActivity)
        ob.filePath = null
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        mockkObject(PermissionUtils::class)
        every { PermissionUtils.hasStoragePermission(shareActivity) } returns false
        every {
            selectPathAndSaveFile.showSelectPathFragmentDialog(
                any(),
                any(),
                any(),
                any()
            )
        } just runs
        //when
        selectPathAndSaveFile.handleOplusAction()
        //then
        verify(inverse = true) { selectPathAndSaveFile.showSelectPathFragmentDialog(any(), any(), any(), any()) }
        unmockkStatic(PermissionUtils::class)
    }


    @Test
    fun `should call showSelectPathFragmentDialog when call handleOplusAction if destinationDirPath is not null`() {
        //given
        val ob = SelectPathAndSaveFile(shareActivity)
        ob.destinationDirPath = "sdcard/test"
        ob.filePath = "sdcard/testfile"
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        mockkObject(PermissionUtils::class)
        every { PermissionUtils.hasStoragePermission(shareActivity) } returns false
        every {
            selectPathAndSaveFile.showSelectPathFragmentDialog(
                any(),
                any(),
                any(),
                any()
            )
        } just runs
        //when
        selectPathAndSaveFile.handleOplusAction()
        //then
        verify { selectPathAndSaveFile.showSelectPathFragmentDialog(any(), any(), any(), any()) }
        unmockkStatic(PermissionUtils::class)
    }

    @Test
    fun `should call nothing when call onSelect is path is null`() {
        //given
        val code = 0
        val path = null
        val ob = SelectPathAndSaveFile(shareActivity)
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { selectPathAndSaveFile.saveFile(any(), any()) } just runs
        every { selectPathAndSaveFile.waringUser() } just runs
        every { selectPathAndSaveFile.handleActionSend(any()) } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.handleActionSendMultiple() } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.handleOplusInnerSendAction(any()) } returns Pair(
            true,
            arrayListOf()
        )
        every { selectPathAndSaveFile.handlerActionView() } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.destroySelectPathController() } just runs
        //when
        selectPathAndSaveFile.onSelect(code, path)
        //then
        verify(inverse = true) { selectPathAndSaveFile.saveFile(any(), any()) }
        verify(inverse = true) { selectPathAndSaveFile.waringUser() }
        verify(inverse = true) { selectPathAndSaveFile.handleActionSend(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handleActionSendMultiple() }
        verify(inverse = true) { selectPathAndSaveFile.handleOplusInnerSendAction(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handlerActionView() }
    }

    @Test
    fun `should call nothing when call onSelect is action is null`() {
        //given
        val code = 0
        val path = "sdcard/test"
        val ob = SelectPathAndSaveFile(shareActivity)
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { intent.action } returns null
        every { selectPathAndSaveFile.saveFile(any(), any()) } just runs
        every { selectPathAndSaveFile.waringUser() } just runs
        every { selectPathAndSaveFile.handleActionSend(any()) } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.handleActionSendMultiple() } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.handleOplusInnerSendAction(any()) } returns Pair(
            false,
            arrayListOf()
        )
        every { selectPathAndSaveFile.handlerActionView() } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.destroySelectPathController() } just runs
        //when
        selectPathAndSaveFile.onSelect(code, path)
        //then
        verify(inverse = true) { selectPathAndSaveFile.saveFile(any(), any()) }
        verify(inverse = true) { selectPathAndSaveFile.waringUser() }
        verify(inverse = true) { selectPathAndSaveFile.handleActionSend(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handleActionSendMultiple() }
        verify(inverse = true) { selectPathAndSaveFile.handleOplusInnerSendAction(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handlerActionView() }
    }


    @Test
    fun `should call waringUser and handleActionSend when call onSelect is action is ACTION_SEND`() {
        //given
        val code = 0
        val path = "sdcard/test"
        val ob = SelectPathAndSaveFile(shareActivity)
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { intent.action } returns Intent.ACTION_SEND
        every { intent.type } returns ""
        every { selectPathAndSaveFile.saveFile(any(), any()) } just runs
        every { selectPathAndSaveFile.waringUser() } just runs
        every { selectPathAndSaveFile.handleActionSend(any()) } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.handleActionSendMultiple() } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.handleOplusInnerSendAction(any()) } returns Pair(
            false,
            arrayListOf()
        )
        every { selectPathAndSaveFile.handlerActionView() } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.destroySelectPathController() } just runs
        //when
        selectPathAndSaveFile.onSelect(code, path)
        //then
        verify(inverse = true) { selectPathAndSaveFile.saveFile(any(), any()) }
        verify { selectPathAndSaveFile.waringUser() }
        verify { selectPathAndSaveFile.handleActionSend(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handleActionSendMultiple() }
        verify(inverse = true) { selectPathAndSaveFile.handleOplusInnerSendAction(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handlerActionView() }
    }

    @Test
    fun `should call saveFile and handleActionSend when call onSelect is action is ACTION_SEND and listUris is not empty`() {
        //given
        val code = 0
        val path = "sdcard/test"
        val ob = SelectPathAndSaveFile(shareActivity)
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { intent.action } returns Intent.ACTION_SEND
        every { intent.type } returns ""
        every { selectPathAndSaveFile.saveFile(any(), any()) } just runs
        every { selectPathAndSaveFile.waringUser() } just runs
        val mockK = mockk<ArrayList<Uri>>()
        every { mockK.isEmpty() } returns false
        every { selectPathAndSaveFile.handleActionSend(any()) } returns Pair(true, mockK)
        every { selectPathAndSaveFile.handleActionSendMultiple() } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.handleOplusInnerSendAction(any()) } returns Pair(
            false,
            arrayListOf()
        )
        every { selectPathAndSaveFile.handlerActionView() } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.destroySelectPathController() } just runs
        //when
        selectPathAndSaveFile.onSelect(code, path)
        //then
        verify { selectPathAndSaveFile.saveFile(any(), any()) }
        verify(inverse = true) { selectPathAndSaveFile.waringUser() }
        verify { selectPathAndSaveFile.handleActionSend(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handleActionSendMultiple() }
        verify(inverse = true) { selectPathAndSaveFile.handleOplusInnerSendAction(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handlerActionView() }
    }

    @Test
    fun `should call saveFile and handleActionSendMultiple when call onSelect is action is ACTION_SEND_MULTIPLE`() {
        //given
        val code = 0
        val path = "sdcard/test"
        val ob = SelectPathAndSaveFile(shareActivity)
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { intent.action } returns Intent.ACTION_SEND_MULTIPLE
        every { intent.type } returns ""
        every { selectPathAndSaveFile.saveFile(any(), any()) } just runs
        every { selectPathAndSaveFile.waringUser() } just runs
        val mockK = mockk<ArrayList<Uri>>()
        every { mockK.isEmpty() } returns false
        every { selectPathAndSaveFile.handleActionSend(any()) } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.handleActionSendMultiple() } returns Pair(true, mockK)
        every { selectPathAndSaveFile.handleOplusInnerSendAction(any()) } returns Pair(
            false,
            arrayListOf()
        )
        every { selectPathAndSaveFile.handlerActionView() } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.destroySelectPathController() } just runs
        //when
        selectPathAndSaveFile.onSelect(code, path)
        //then
        verify { selectPathAndSaveFile.saveFile(any(), any()) }
        verify(inverse = true) { selectPathAndSaveFile.waringUser() }
        verify(inverse = true) { selectPathAndSaveFile.handleActionSend(any()) }
        verify { selectPathAndSaveFile.handleActionSendMultiple() }
        verify(inverse = true) { selectPathAndSaveFile.handleOplusInnerSendAction(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handlerActionView() }
    }

    @Test
    fun `should call saveFile and handleOplusInnerSendAction when call onSelect is action is OPLUS_INNER_SEND_ACTION`() {
        //given
        val code = 0
        val path = "sdcard/test"
        val ob = SelectPathAndSaveFile(shareActivity)
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { intent.action } returns OPLUS_INNER_SEND_ACTION
        every { intent.type } returns ""
        every { selectPathAndSaveFile.saveFile(any(), any()) } just runs
        every { selectPathAndSaveFile.waringUser() } just runs
        val mockK = mockk<ArrayList<Uri>>()
        every { mockK.isEmpty() } returns false
        every { selectPathAndSaveFile.handleActionSend(any()) } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.handleActionSendMultiple() } returns Pair(true, mockK)
        every { selectPathAndSaveFile.handleOplusInnerSendAction(any()) } returns Pair(
            false,
            mockK
        )
        every { selectPathAndSaveFile.handlerActionView() } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.destroySelectPathController() } just runs
        //when
        selectPathAndSaveFile.onSelect(code, path)
        //then
        verify(inverse = true) { selectPathAndSaveFile.saveFile(any(), any()) }
        verify(inverse = true) { selectPathAndSaveFile.waringUser() }
        verify(inverse = true) { selectPathAndSaveFile.handleActionSend(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handleActionSendMultiple() }
        verify { selectPathAndSaveFile.handleOplusInnerSendAction(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handlerActionView() }
    }

    @Test
    fun `should call saveFile and handlerActionView when call onSelect is action is ACTION_VIEW`() {
        //given
        val code = 0
        val path = "sdcard/test"
        val ob = SelectPathAndSaveFile(shareActivity)
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { intent.action } returns Intent.ACTION_VIEW
        every { intent.type } returns ""
        every { selectPathAndSaveFile.saveFile(any(), any()) } just runs
        every { selectPathAndSaveFile.waringUser() } just runs
        val mockK = mockk<ArrayList<Uri>>()
        every { mockK.isEmpty() } returns false
        every { selectPathAndSaveFile.handleActionSend(any()) } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.handleActionSendMultiple() } returns Pair(true, arrayListOf())
        every { selectPathAndSaveFile.handleOplusInnerSendAction(any()) } returns Pair(
            false,
            arrayListOf()
        )
        every { selectPathAndSaveFile.handlerActionView() } returns Pair(true, mockK)
        every { selectPathAndSaveFile.destroySelectPathController() } just runs
        //when
        selectPathAndSaveFile.onSelect(code, path)
        //then
        verify { selectPathAndSaveFile.saveFile(any(), any()) }
        verify(inverse = true) { selectPathAndSaveFile.waringUser() }
        verify(inverse = true) { selectPathAndSaveFile.handleActionSend(any()) }
        verify(inverse = true) { selectPathAndSaveFile.handleActionSendMultiple() }
        verify(inverse = true) { selectPathAndSaveFile.handleOplusInnerSendAction(any()) }
        verify { selectPathAndSaveFile.handlerActionView() }
    }

    @Test
    fun `should call handleSendTextFile when call handleActionSend is intent type is TXT_TYPE`() {
        //given
        val filePath = "test"
        val ob = SelectPathAndSaveFile(shareActivity)
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { intent.type } returns "text/plant"
        mockkObject(MimeTypeHelper)
        every { MimeTypeHelper.getTypeByMimeType("text/plant") } returns MimeTypeHelper.TXT_TYPE
        every { IntentUtils.getString(any(), any()) } returns "test"
        every { selectPathAndSaveFile.handleSendTextFile(any(), any()) } just runs
        //when
        val result = selectPathAndSaveFile.handleActionSend(filePath)
        //then
        verify { selectPathAndSaveFile.handleSendTextFile(any(), any()) }
        Assert.assertEquals(result.first, false)
        Assert.assertEquals(result.second, arrayListOf<Uri>())
        unmockkObject(MimeTypeHelper)
    }

    @Test
    fun `should call none handleSendTextFile when call handleActionSend is intent type is APPLICATION_TYPE`() {
        //given
        val filePath = "test"
        val ob = SelectPathAndSaveFile(shareActivity)
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { intent.type } returns "text/plant"
        mockkObject(MimeTypeHelper)
        every { MimeTypeHelper.getTypeByMimeType("text/plant") } returns MimeTypeHelper.APPLICATION_TYPE
        every { IntentUtils.getParcelable(any(), any()) } returns mockk()
        //when
        val result = selectPathAndSaveFile.handleActionSend(filePath)
        //then
        verify(inverse = true) { selectPathAndSaveFile.handleSendTextFile(any(), any()) }
        Assert.assertEquals(result.first, true)
        unmockkObject(MimeTypeHelper)
    }

    @Test
    fun `should call none handleSendTextFile when call handleActionSend is intent type is TXT_TYPE and EXTRA_TEXT is null`() {
        //given
        val filePath = "test"
        val ob = SelectPathAndSaveFile(shareActivity)
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { intent.type } returns "text/plant"
        mockkObject(MimeTypeHelper)
        every { MimeTypeHelper.getTypeByMimeType("text/plant") } returns MimeTypeHelper.TXT_TYPE
        every { IntentUtils.getParcelable(any(), any()) } returns mockk()
        every { IntentUtils.getString(any(), any()) } returns null
        //when
        val result = selectPathAndSaveFile.handleActionSend(filePath)
        //then
        verify(inverse = true) { selectPathAndSaveFile.handleSendTextFile(any(), any()) }
        Assert.assertEquals(result.first, true)
        unmockkObject(MimeTypeHelper)
    }

    @Test
    fun `should call openFragmentByAction when call showSaveFileDialogIfNeed if hasShowSaveFileDialog is true`() {
        //given
        val ob = SelectPathAndSaveFile(shareActivity)
        ob.hasShowSaveFileDialog = false
        mockkObject(PrivacyPolicyController)
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { PrivacyPolicyController.hasAgreePrivacy() } returns true
        every { selectPathAndSaveFile.openFragmentByAction() } just runs
        //when
        selectPathAndSaveFile.showSaveFileDialogIfNeed()
        //then
        verify { selectPathAndSaveFile.openFragmentByAction() }
        unmockkObject(PrivacyPolicyController)
    }

    @Test
    fun `should call none openFragmentByAction when call showSaveFileDialogIfNeed if hasShowSaveFileDialog is true`() {
        //given
        val ob = SelectPathAndSaveFile(shareActivity)
        ob.hasShowSaveFileDialog = false
        mockkObject(PrivacyPolicyController)
        val selectPathAndSaveFile = spyk(ob, recordPrivateCalls = true)
        every { PrivacyPolicyController.hasAgreePrivacy() } returns false
        every { selectPathAndSaveFile.openFragmentByAction() } just runs
        //when
        selectPathAndSaveFile.showSaveFileDialogIfNeed()
        //then
        verify(inverse = true) { selectPathAndSaveFile.openFragmentByAction() }
        unmockkObject(PrivacyPolicyController)
    }
}