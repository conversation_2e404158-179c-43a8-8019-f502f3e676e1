/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RequestInterceptor
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/8 16:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.interceptors

import android.content.Context
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.drivebrowser.data.utils.BuildHeader
import com.oplus.filemanager.drivebrowser.data.utils.ServerConfig
import com.oplus.filemanager.drivebrowser.data.utils.SignInfo
import com.oplus.filemanager.drivebrowser.data.utils.formatUrlMap
import com.oplus.filemanager.drivebrowser.data.utils.generateKey
import com.oplus.filemanager.drivebrowser.data.utils.hmacSignInfoWithGenerateKey
import com.oplus.filemanager.drivebrowser.data.utils.toHex
import com.oplus.filemanager.drivebrowser.utils.UniqueIdUtils
import com.oplus.filemanager.interfaze.heytapaccount.IHeytapAccount
import okhttp3.HttpUrl
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import okio.Buffer
import org.jetbrains.annotations.VisibleForTesting
import java.net.SocketTimeoutException
import kotlin.random.Random

internal class RequestInterceptor(
    private val context: Context
) : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val url = request.url
        Log.d(TAG, "interceptor -> request url = $url")
        val path = url.encodedPath
        Log.d(TAG, "interceptor -> path = $path")
        val queryStrings = queryString(url)
        Log.d(TAG, "interceptor -> request queryList = $queryStrings")
        val bodyToString = obtainBodyToString(request)
        Log.d(TAG, "interceptor -> bodyToString = $bodyToString")
        val headers = runCatching {
            createRequestHeader(
                context,
                obtainAppId(),
                obtainAppSecretKey(),
                path,
                bodyToString,
                queryStrings
            )
        }.getOrNull()
        val builder: Request.Builder = request.newBuilder()
        headers?.forEach { (key, value) ->
            builder.addHeader(key, value)
        }
        val heytapAccountAction = Injector.injectFactory<IHeytapAccount>()
        val token = heytapAccountAction?.getUserToken(appContext) ?: ""
        builder.addHeader("token", token)
        val newRequest = builder.build()
        return onIntercept(chain, newRequest)
    }

    /**
     * 处理链接超时情况
     */
    private fun onIntercept(chain: Interceptor.Chain, request: Request): Response {
        try {
            return chain.proceed(request)
        } catch (e: SocketTimeoutException) {
            Log.e(TAG, e)
        }
        return chain.proceed(chain.request())
    }

    @VisibleForTesting
    fun queryString(url: HttpUrl): String {
        val queryList: Set<String> = url.queryParameterNames
        val iterator = queryList.iterator()
        val queryStrings: String
        val paramMap = hashMapOf<String, String>()
        queryList.forEach { _ ->
            val queryName = iterator.next()
            val queryParamValue = url.queryParameter(queryName)
            queryParamValue?.let {
                paramMap[queryName] = it
            }
        }
        queryStrings = formatUrlMap(paramMap, false, keyToLower = true)
        return queryStrings
    }

    private fun obtainBodyToString(request: Request): String {
        var bodyToString = ""
        request.body?.let { body ->
            bodyToString = requestBodyToString(body)
        }
        return bodyToString
    }

    private fun obtainAppId(): String {
        return ServerConfig.getInstance().apiKey
    }

    private fun obtainAppSecretKey(): String {
        return ServerConfig.getInstance().apiSecret
    }

    @VisibleForTesting
    fun createRequestHeader(
        context: Context,
        appId: String,
        appSecret: String,
        path: String,
        jsonStr: String,
        queryStrings: String?,
    ): HashMap<String, String> {
        val duid = UniqueIdUtils.getDeviceId()
        val header = BuildHeader.generateHeader(context, appId)
        header[BuildHeader.BIND_DEVICE] = duid
        val signInfo = SignInfo(appId, appSecret, header[BuildHeader.TIME_STAMP], path, jsonStr, queryStrings)
        val salt = Random.Default.nextBytes(SEED).toHex()
        val generateKey = generateKey(duid, salt.toByteArray(), context.packageName)
        val hmac = hmacSignInfoWithGenerateKey(signInfo, generateKey)
        Log.d(TAG, "createRequestHeader -> salt = $salt ; hmac = $hmac")
        header[BuildHeader.SALT] = salt
        header[BuildHeader.SIGN] = hmac
        return header
    }

    private fun requestBodyToString(requestBody: RequestBody): String {
        val buffer = Buffer()
        requestBody.writeTo(buffer)
        return buffer.readUtf8()
    }

    companion object {
        private const val TAG = "RequestInterceptor"
        private const val SEED = 32
    }
}