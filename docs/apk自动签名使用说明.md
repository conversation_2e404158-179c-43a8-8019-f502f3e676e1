# 自动签名插件源文档
>  https://hio.oppo.com/app/ozone/engineeringtools/gotoOzoneCircleKbItemDetail?tt_lang=zh-CN&tt_zone=%208&new_client=1&page=ozone&source=index&enc_kbi_id=298970&locatedCmtId=0&folder_id=458061

#  ~~apk_sign.gradle 配置自己的账号和密码~~
~~去在线3DES加密网站如https://tool.lmeee.com/jiami/des3 (官方文档提供的地址有些问题， 额外找了了个)，加密自己的密码~~
![img.png](img.png)

# ~~修改账号和密码~~
![img_3.png](img_3.png)

# sync完毕后，可直接执行对应的install task，直接安装apk；
![img.png](install_task.png)

# 停用该功能
去app module下的gradle文件，注释掉这一行即可
![img_2.png](img_2.png)

# 本地签名功能更新
切换obuildplugin中自带的osigner本地签名插件后，不再需要自己配置账号密码。
该插件将基于当前系统的ADC域LDAP登录账号，识别员工身份(工号)，并用于签名系统鉴权。
另外，apk_sign.gradle中也进行了改造，现在将优先尝试匹配signInfo.json中的签名配置，以对齐流水线上构建产物的签名。