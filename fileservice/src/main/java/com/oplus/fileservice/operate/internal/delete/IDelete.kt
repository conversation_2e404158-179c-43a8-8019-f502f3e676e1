/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : IDelete
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/23 10:19
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/6/23       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.delete

import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.convert.OperationResult

interface IDelete {
    fun delete(fileInfos: List<FileInfo>?): OperationResult
}