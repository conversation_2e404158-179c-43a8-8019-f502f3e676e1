/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : PCConnectControllerTest
 ** Description : PCConnectController Unit Test
 ** Version     : 1.0
 ** Date        : 2022/10/13
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2022/10/13      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.pcconnect

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.Assert
import org.junit.Test

class PCConnectControllerTest {

    @Test
    fun should_when_asActivity() {
        val controller = PCConnectController.sInstance
        val activity = mockk<FragmentActivity>()
        val fragment = mockk<Fragment>()
        every { fragment.activity }.returns(activity)
        var context = controller.asActivity(activity)
        Assert.assertEquals(context, activity)

        context = controller.asActivity(fragment)
        verify { fragment.activity }
        Assert.assertEquals(context, activity)

        context = controller.asActivity(mockk())
        Assert.assertEquals(context, null)
    }
}