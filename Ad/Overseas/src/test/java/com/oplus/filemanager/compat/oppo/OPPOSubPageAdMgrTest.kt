/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: OPPOSubPageAdMgrTest.kt
 ** Description: unit test
 ** Version: 1.0
 ** Date : 2022/7/26
 ** Author: YanShengNeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * YanShengNeng 2022/7/14      1.0    create
 ****************************************************************/
package com.oplus.filemanager.compat.oppo

import android.content.Context
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.compat.brand.oppo.AdvertController
import com.oplus.filemanager.compat.brand.oppo.AdvertViewPresenter
import com.oplus.filemanager.compat.brand.oppo.AlbumSetViewPresenter
import com.oplus.filemanager.compat.brand.oppo.AudioViewPresenter
import com.oplus.filemanager.compat.brand.oppo.BaseFileViewPresenter
import com.oplus.filemanager.compat.brand.oppo.OPPOSubPageAdMgr
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import org.mockito.Mockito
import org.powermock.reflect.Whitebox

@RunWith(JUnit4::class)
class OPPOSubPageAdMgrTest {
    @MockK
    lateinit var context: Context

    @MockK
    lateinit var activity: FragmentActivity

    @MockK
    lateinit var adapter: BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun verify_value_when_makeName() {
        val subPageAdMgr = OPPOSubPageAdMgr()
        Assert.assertEquals("name1", subPageAdMgr.makeName("name1"))
    }

    @Test
    fun do_nothing_when_requestSubAd() {
        val subPageAdMgr = OPPOSubPageAdMgr()
        subPageAdMgr.requestSubAd(activity, "name1", adapter, arrayListOf(), false)
    }


    @Test
    fun verify_value_when_requestCommonAd() {
        val lifecycle = Mockito.mock(Lifecycle::class.java)
        val fragmentActivity = Mockito.mock(FragmentActivity::class.java)
        val subPageAdMgr = Mockito.mock(OPPOSubPageAdMgr::class.java)
        val bean = MediaFileWrapper()
        val list = ArrayList<MediaFileWrapper>()
        bean.mFileWrapperViewType = BaseFileBean.TYPE_FILE_LIST_FOOTER
        list.add(bean)
        subPageAdMgr.requestCommonAd(fragmentActivity, adapter, list)

        bean.mFileWrapperViewType = BaseFileBean.TYPE_FILE_AD
        list.add(bean)
        bean.mFileWrapperViewType = BaseFileBean.TYPE_FILE_LIST_HEADER
        list.add(bean)

        Mockito.`when`(fragmentActivity.lifecycle).thenReturn(lifecycle)
        val recycleAdapter = Mockito.mock(BaseSelectionRecycleAdapter::class.java)
        val recyclerView = Mockito.mock(RecyclerView::class.java)
        Mockito.`when`(recycleAdapter.getRecyclerView()).thenReturn(recyclerView)
        Mockito.`when`(recyclerView.layoutManager).thenReturn(GridLayoutManager(context, 1))
        subPageAdMgr.requestCommonAd(fragmentActivity, adapter, list)
    }

    @Test
    fun assert_doNothing_when_scanModeChange() {
        val subPageAdMgr = Mockito.mock(OPPOSubPageAdMgr::class.java)
        subPageAdMgr.scanModeChange("name", false)
    }

    @Test
    fun assert_result_when_getEntryView() {
        val subPageAdMgr = Mockito.mock(OPPOSubPageAdMgr::class.java)
        Assert.assertNull(subPageAdMgr.getEntryView("name"))
    }

    @Test
    fun assert_value_when_refreshByScanModeChanged() {
        val subPageAdMgr = Mockito.mock(OPPOSubPageAdMgr::class.java)
        val advertViewPresenter = Mockito.mock(AdvertViewPresenter::class.java)

        Whitebox.setInternalState(subPageAdMgr, "mAdvertPresenter", advertViewPresenter)
        subPageAdMgr.refreshByScanModeChanged()
    }

    @Test
    fun assert_value_when_onAdvertDestroy() {
        val subPageAdMgr = Mockito.mock(OPPOSubPageAdMgr::class.java)

        subPageAdMgr.eventDestroy()

        val advertViewPresenter = Mockito.mock(AdvertViewPresenter::class.java)
        val advertController = Mockito.mock(AdvertController::class.java)
        Whitebox.setInternalState(subPageAdMgr, "mAdvertPresenter", advertViewPresenter)
        Whitebox.setInternalState(subPageAdMgr, "mAdvertController", advertController)
        subPageAdMgr.eventDestroy()
    }

    @Test
    fun `should execute refreshIfListChanged when requestCommonAd if presenter is not null`() {
        val subPageAdMgr = mockk<OPPOSubPageAdMgr>()
        val presenter = mockk<AudioViewPresenter>()
        every { subPageAdMgr.mAdvertPresenter } returns presenter
        val fileList = arrayListOf<MediaFileWrapper>()
        fileList.add(MediaFileWrapper())
        every { subPageAdMgr.requestCommonAd(any(), any(), fileList) } answers { callOriginal() }
        justRun { presenter.refreshIfListChanged() }
        subPageAdMgr.requestCommonAd(mockk(), mockk(), fileList)
        verify { presenter.refreshIfListChanged() }
    }

    @Test
    fun `should execute refreshIfListChanged when requestCommonAd if list size is 0`() {
        val subPageAdMgr = mockk<OPPOSubPageAdMgr>()
        val presenter = mockk<AudioViewPresenter>()
        every { subPageAdMgr.mAdvertPresenter } returns presenter
        val fileList = arrayListOf<MediaFileWrapper>()
        every { subPageAdMgr.requestCommonAd(any(), any(), fileList) } answers { callOriginal() }
        justRun { presenter.refreshIfListChanged() }
        subPageAdMgr.requestCommonAd(mockk(), mockk(), fileList)
        verify(inverse = true) { presenter.refreshIfListChanged() }
    }

    @Test
    fun `should not execute refreshIfListChanged when requestPhoneStorageAd if presenter is not null`() {
        val subPageAdMgr = mockk<OPPOSubPageAdMgr>()
        val presenter = mockk<AudioViewPresenter>()
        every { subPageAdMgr.mAdvertPresenter } returns presenter
        val fileList = arrayListOf<BaseFileBean>()
        fileList.add(BaseFileBean())
        every { subPageAdMgr.requestPhoneStorageAd(any(), any(), fileList) } answers { callOriginal() }
        justRun { presenter.refreshIfListChanged(fileList) }
        subPageAdMgr.requestPhoneStorageAd(mockk(), mockk(), fileList)
        verify { presenter.refreshIfListChanged(fileList) }
    }

    @Test
    fun `should execute refreshIfListChanged when requestSubAlbumSetAd if presenter is not null`() {
        val subPageAdMgr = mockk<OPPOSubPageAdMgr>()
        val presenter = mockk<AudioViewPresenter>()
        every { subPageAdMgr.mAdvertPresenter } returns presenter
        val fileList = arrayListOf<AlbumItem>()
        fileList.add(AlbumItem(1))
        every { subPageAdMgr.requestSubAlbumSetAd(any(), any(), fileList) } answers { callOriginal() }
        justRun { presenter.refreshIfListChanged() }
        subPageAdMgr.requestSubAlbumSetAd(mockk(), mockk(), fileList)
        verify { presenter.refreshIfListChanged() }
    }

    @Test
    fun `should not execute refreshIfListChanged when requestSubAlbumSetAd if presenter is null`() {
        val subPageAdMgr = mockk<OPPOSubPageAdMgr>()
        val presenter = mockk<AlbumSetViewPresenter>()
        every { subPageAdMgr.mAdvertPresenter } returns null
        val fileList = arrayListOf<AlbumItem>()
        fileList.add(AlbumItem(1))
        val adapter = mockk<BaseSelectionRecycleAdapter<*, AlbumItem>>()
        val mockLayoutManager = mockk<GridLayoutManager>()
        every { adapter.getRecyclerView() } returns mockk {
            every { layoutManager } returns mockLayoutManager
            justRun { addOnScrollListener(any()) }
            justRun { tag = any() }
        }
        every { subPageAdMgr.requestSubAlbumSetAd(activity, adapter, fileList) } answers { callOriginal() }
        every { subPageAdMgr.createAlbumSetViewPresenter(activity, adapter, fileList, mockLayoutManager) } returns presenter
        justRun { presenter.refreshIfListChanged() }
        justRun { subPageAdMgr.initAdvertController() }
        subPageAdMgr.requestSubAlbumSetAd(activity, adapter, fileList)
        verify(inverse = true) { presenter.refreshIfListChanged() }
        verify { subPageAdMgr.initAdvertController() }
    }

    @Test
    fun `should not execute refreshIfListChanged when requestSubAlbumSetAd if list size is 0`() {
        val subPageAdMgr = mockk<OPPOSubPageAdMgr>()
        val presenter = mockk<AudioViewPresenter>()
        every { subPageAdMgr.mAdvertPresenter } returns presenter
        val fileList = arrayListOf<AlbumItem>()
        every { subPageAdMgr.requestSubAlbumSetAd(any(), any(), fileList) } answers { callOriginal() }
        justRun { presenter.refreshIfListChanged() }
        subPageAdMgr.requestSubAlbumSetAd(mockk(), mockk(), fileList)
        verify(inverse = true) { presenter.refreshIfListChanged() }
    }

    @Test
    fun `should not execute refreshIfListChanged when requestPhoneStorageAd if presenter is null`() {
        val subPageAdMgr = mockk<OPPOSubPageAdMgr>()
        val presenter = mockk<BaseFileViewPresenter>()
        every { subPageAdMgr.mAdvertPresenter } returns null
        val fileList = arrayListOf<BaseFileBean>()
        fileList.add(BaseFileBean())
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>>()
        val mockLayoutManager = mockk<GridLayoutManager>()
        every { adapter.getRecyclerView() } returns mockk {
            every { layoutManager } returns mockLayoutManager
            justRun { addOnScrollListener(any()) }
            justRun { tag = any() }
        }
        every { subPageAdMgr.requestPhoneStorageAd(activity, adapter, fileList) } answers { callOriginal() }
        every { subPageAdMgr.createBaseFileViewPresenter(activity, adapter, fileList, mockLayoutManager) } returns presenter
        justRun { presenter.refreshIfListChanged() }
        justRun { subPageAdMgr.initAdvertController() }
        subPageAdMgr.requestPhoneStorageAd(activity, adapter, fileList)
        verify(inverse = true) { presenter.refreshIfListChanged() }
        verify { subPageAdMgr.initAdvertController() }
    }
}