/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : GlobalSearchDMPLoaderTest.kt
 * * Description : GlobalSearchDMPLoaderTest
 * * Version     : 1.0
 * * Date        : 2024/5/21
 * * Author      : W9059186
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui.loader

import android.content.Context
import android.util.SparseArray
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.UriFileWrapper
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.oplus.filemanager.category.globalsearch.bean.CategoryFileWrapper
import com.oplus.filemanager.category.globalsearch.bean.MoreFileWrapper
import com.oplus.filemanager.category.globalsearch.bean.SearchLabelWrapper
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchViewModel
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test

class GlobalSearchDMPLoaderTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
        mockkStatic(FeatureCompat::class)
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getInternalSdPath(context) } returns ""
        mockkStatic(CategoryHelper::class)
        every {
            CategoryHelper.setIgnoredPath(context, CategoryHelper.CATEGORY_DOC)
        } returns SparseArray(8)
        mockkObject(FileLabelDBHelper)
    }
    @After
    fun tearDown() {
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(CategoryHelper::class)
        unmockkObject(FileLabelDBHelper)
    }
    @Test
    fun `should addItem when call if method is addDMPSearchItem`() {
        // Given
        val globalSearchDMPLoader = GlobalSearchDMPLoader()
        val searchResultData = GlobalSearchViewModel.SearchResultData()
        searchResultData.listDirectory = mutableListOf()
        val file1 = mockk<UriFileWrapper>(relaxed = true)
        every { file1.mLocalType } returns MimeTypeHelper.DIRECTORY_TYPE
        val file2 = mockk<UriFileWrapper>(relaxed = true)
        every { file2.recallType } returns 0
        val file3 = mockk<UriFileWrapper>(relaxed = true)
        every { file3.recallType } returns 1
        val file4 = mockk<UriFileWrapper>(relaxed = true)
        every { file4.recallType } returns 2
        val file5 = mockk<UriFileWrapper>(relaxed = true)
        every { file5.recallType } returns 3
        val file6 = mockk<UriFileWrapper>(relaxed = true)
        every { file6.recallType } returns 4
        // When
        globalSearchDMPLoader.addDMPSearchItem(file1, searchResultData)
        println("file2.recallType${file2.recallType}")
        globalSearchDMPLoader.addDMPSearchItem(file2, searchResultData)
        globalSearchDMPLoader.addDMPSearchItem(file3, searchResultData)
        globalSearchDMPLoader.addDMPSearchItem(file4, searchResultData)
        globalSearchDMPLoader.addDMPSearchItem(file5, searchResultData)
        globalSearchDMPLoader.addDMPSearchItem(file6, searchResultData)
        // Then
        Assert.assertEquals(false, searchResultData.listDirectory == null)
    }

    @Test
    @Ignore
    fun `should return boolean when call if method is handleDmpSearch`() {
        // Given
        val globalSearchDMPLoader = GlobalSearchDMPLoader()
        globalSearchDMPLoader.setSearchKey("1")
        val searchResultData = GlobalSearchViewModel.SearchResultData()
        val keyMap = HashMap<Int, BaseFileBean>()
        val rangeMap = HashMap<Int, Pair<Int, Int>>(6)
        val searchApi = mockk<IDmpSearchApi>()

        //mockkStatic(SearchHelper::class)
        every { globalSearchDMPLoader.dmpSearchApi } returns searchApi
        // When
        val result = globalSearchDMPLoader.handleDmpSearch(searchResultData)
        /*Then
        Assert.assertEquals(false, result)*/
    }

    @Test
    fun `should addItem when call if method is handleNormalSearch`() {
        // Given
        val globalSearchDMPLoader = spyk(GlobalSearchDMPLoader())
        globalSearchDMPLoader.setSearchKey("1")
        val searchApi = mockk<IDmpSearchApi>()
        every { globalSearchDMPLoader.dmpSearchApi } returns searchApi
        every { searchApi.isDMPCursor(any()) } returns false
        /* mockkStatic(com.oplus.filemanager.dump.SearchHelper::class)
        every { com.oplus.filemanager.dump.SearchHelper.getHiFilter() } returns mutableListOf()*/
        val searchResultData = GlobalSearchViewModel.SearchResultData()
        /*When
        com.oplus.filemanager.dump.SearchHelper.initDMPSearch()*/
        globalSearchDMPLoader.handleNormalSearch(searchResultData)
        // Then
        Assert.assertEquals(false, searchResultData.listOldFMQuery != null)
    }

    @Test
    fun `should return cut to 5 items when call if method is genAllTabData`() {
        // Given
        val globalSearchDMPLoader = spyk(GlobalSearchDMPLoader())
        globalSearchDMPLoader.setSearchKey("1")
        val searchApi = mockk<IDmpSearchApi>()
        every { globalSearchDMPLoader.dmpSearchApi } returns searchApi
        every { searchApi.isDMPCursor(any()) } returns false
        every { searchApi.initDmpSdk(any()) } returns false
        val tabFiles: ArrayList<BaseFileBean> = ArrayList<BaseFileBean>()
        val allFiles = mutableListOf<BaseFileBean>()
        val directory1 = BaseFileBean().apply {
            this.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
            this.mData = "directory1"
        }
        val directory2 =  BaseFileBean().apply {
            this.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
            this.mData = "directory2"
        }
        val directory3 = BaseFileBean().apply {
            this.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
            this.mData = "directory3"
        }
        allFiles.add(directory1)
        allFiles.add(directory2)
        allFiles.add(directory3)

        val labelFiles: MutableList<BaseFileBean> = mutableListOf()
        val label1 = SearchLabelWrapper(FileLabelEntity(0, "生活", 0, 0, 0, 0, null, null))
        val label2 = SearchLabelWrapper(FileLabelEntity(0, "工作", 0, 0, 0, 0, null, null))
        val label3 = SearchLabelWrapper(FileLabelEntity(0, "重要", 0, 0, 0, 0, null, null))
        labelFiles.add(label1)
        labelFiles.add(label2)
        labelFiles.add(label3)
        // When
        searchApi.initDmpSdk(context)
        globalSearchDMPLoader.genAllTabData(tabFiles, allFiles, labelFiles, true)
        // Then
        Assert.assertEquals(true, tabFiles.size == 6)
        Assert.assertEquals(true, tabFiles[0] is CategoryFileWrapper)
        Assert.assertEquals(true, tabFiles[1] is SearchLabelWrapper)
        Assert.assertEquals(true, tabFiles[2] is SearchLabelWrapper)
        Assert.assertEquals(true, tabFiles[3].isDir())
        Assert.assertEquals(true, tabFiles[4].isDir())
        Assert.assertEquals(true, tabFiles[5] is MoreFileWrapper)
    }



    /*@Test
    fun `should addItem when call if method is handleRangeMap`() {
        // Given
        val globalSearchDMPLoader = GlobalSearchDMPLoader()
        globalSearchDMPLoader.setSearchKey("1")
        val rangeMap = HashMap<Int, Pair<Int, Int>>(6)
        val file = mockk<BaseFileBean>(relaxed = true)
        val files = mockk<ArrayList<BaseFileBean>>(relaxed = true)
        files.add(file)
        val searchResultData = GlobalSearchViewModel.SearchResultData()
        searchResultData.folderFiles = files
        // When
        globalSearchDMPLoader.handleRangeMap(ArrayList(), rangeMap, true, searchResultData)
        // Then
        println("second ${rangeMap[GlobalSearchActivity.TAB_ALL]?.second}")
        Assert.assertEquals(false, rangeMap[GlobalSearchActivity.TAB_ALL]?.second == 0)
    }*/

    /*@Test
    fun `should addItem when call if method is handleCategory`() {
        // Given
        val globalSearchDMPLoader = GlobalSearchDMPLoader()
        val keyMap = HashMap<Int, BaseFileBean>()
        val rangeMap = HashMap<Int, Pair<Int, Int>>(6)
        val file1 = mockk<UriFileWrapper>(relaxed = true)
        every { file1.mLocalType } returns MimeTypeHelper.DIRECTORY_TYPE
        val items = ArrayList<BaseFileBean>()
        items.add(file1)
        val searchResultData = GlobalSearchViewModel.SearchResultData()
        // When
        globalSearchDMPLoader.handleCategory(keyMap, items, rangeMap, true, searchResultData)
        // Then
        Assert.assertEquals(true, searchResultData.folderFiles.isEmpty())
    }*/

    @Test
    fun `should addItem when call if method is loadInBackground`() {
        // Given
        val globalSearchDMPLoader = spyk(GlobalSearchDMPLoader())
        globalSearchDMPLoader.setSearchKey("1")
        val searchApi = mockk<IDmpSearchApi>()
        every { globalSearchDMPLoader.dmpSearchApi } returns searchApi
        every { searchApi.initDmpSdk(any()) }
        every { FileLabelDBHelper.getFileLabelByNameFuzzy(any()) } returns null
        // When
        val result = globalSearchDMPLoader.loadInBackground()
        // Then
        Assert.assertEquals(true, result?.searchKey == "1")
    }
}