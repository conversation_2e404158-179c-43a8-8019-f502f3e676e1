package com.filemanager.thumbnail.doc

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.net.Uri
import com.bumptech.glide.Priority
import com.bumptech.glide.load.data.DataFetcher
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.utils.PreferencesUtils
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import java.io.File
import java.io.FileDescriptor
import java.io.FileInputStream
import java.io.IOException
import java.lang.reflect.Method
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * DocThumbnailFetcher的单元测试类
 * 用于测试文档缩略图获取器的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class DocThumbnailFetcherTest {

    private lateinit var context: Context
    private lateinit var mockThumbModel: DocThumbnail
    private lateinit var fetcher: DocThumbnailFetcher
    private lateinit var mockCallback: DataFetcher.DataCallback<in Bitmap>
    private lateinit var mockLoader: IDocThumbnailLoader

    private val width = 100
    private val height = 100

    /**
     * 测试前的初始化方法
     * 1. 初始化测试上下文
     * 2. 创建mock对象
     * 3. 设置DocThumbnailLoaderFactory的mock行为
     */
    @Before
    fun setUp() {
        context = RuntimeEnvironment.getApplication()
        mockThumbModel = mockk(relaxed = true)
        mockCallback = mockk(relaxed = true)
        mockLoader = mockk(relaxed = true)
        
        // 模拟静态方法
        mockkObject(DocThumbnailLoaderFactory)
        every { DocThumbnailLoaderFactory.getInstance(any()) } returns mockLoader
        
        fetcher = DocThumbnailFetcher(context, mockThumbModel, width, height)
    }

    /**
     * 测试后的清理方法
     * 解除所有mock对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试getDataClass方法
     * 验证返回的数据类是否为Bitmap
     */
    @Test
    fun `getDataClass should return Bitmap class`() {
        assertEquals(Bitmap::class.java, fetcher.getDataClass())
    }

    /**
     * 测试getDataSource方法
     * 验证返回的数据源是否为REMOTE
     */
    @Test
    fun `getDataSource should return REMOTE`() {
        assertEquals(com.bumptech.glide.load.DataSource.REMOTE, fetcher.getDataSource())
    }

    /**
     * 测试cleanup方法
     * 验证是否会回收bitmap资源
     */
    @Test
    fun `cleanup should recycle bitmap`() {
        val mockBitmap = mockk<Bitmap>(relaxed = true)
        // 使用反射设置私有字段
        val bitmapField = DocThumbnailFetcher::class.java.getDeclaredField("bitmap")
        bitmapField.isAccessible = true
        bitmapField.set(fetcher, mockBitmap)

        fetcher.cleanup()

        verify(exactly = 1) { mockBitmap.recycle() }
    }

    /**
     * 测试cancel方法
     * 验证:
     * 1. 是否设置取消标志位
     * 2. 是否调用loader的cancel方法
     */
    @Test
    fun `cancel should set cancelled flag and call loader cancel`() {
        val isCancelledField = DocThumbnailFetcher::class.java.getDeclaredField("isCancelled")
        isCancelledField.isAccessible = true

        assertFalse(isCancelledField.get(fetcher) as Boolean)

        fetcher.cancel()

        assertTrue(isCancelledField.get(fetcher) as Boolean)
        verify(exactly = 1) { mockLoader.cancel(mockThumbModel) }
    }

    /**
     * 测试loadData方法在loaderImpl为null时的行为
     * 验证是否会调用onLoadFailed回调
     */
    @Test
    fun `loadData should fail when loaderImpl is null`() {
        // 使用反射覆盖lazy初始化的值
        val loaderImplField = DocThumbnailFetcher::class.java.getDeclaredField("loaderImpl\$delegate")
        loaderImplField.isAccessible = true
        val delegate = loaderImplField.get(fetcher)
        // 对于Kotlin 1.5+，字段名可能不同，尝试替代方法
        try {
            val valueField = delegate.javaClass.getDeclaredField("value")
            valueField.isAccessible = true
            valueField.set(delegate, null)
        } catch (e: NoSuchFieldException) {
            // 尝试其他字段名
            try {
                val valueField = delegate.javaClass.getDeclaredField("_value")
                valueField.isAccessible = true
                valueField.set(delegate, null)
            } catch (e2: NoSuchFieldException) {
                // 如果无法直接访问字段，则重新mock工厂返回null
                clearAllMocks()
                mockkObject(DocThumbnailLoaderFactory)
                every { DocThumbnailLoaderFactory.getInstance(any()) } returns null
                fetcher = DocThumbnailFetcher(context, mockThumbModel, width, height)
            }
        }

        val mockCallback = mockk<DataFetcher.DataCallback<in Bitmap>>(relaxed = true)
        fetcher.loadData(Priority.NORMAL, mockCallback)

        verify(exactly = 1) { mockCallback.onLoadFailed(any<UnsupportedOperationException>()) }
    }

    /**
     * 测试loadData方法在取消状态下的行为
     * 验证是否会返回null
     */
    @Test
    fun `loadData should return null when cancelled`() {
        // 设置取消标志位
        val isCancelledField = DocThumbnailFetcher::class.java.getDeclaredField("isCancelled")
        isCancelledField.isAccessible = true
        isCancelledField.set(fetcher, true)

        fetcher.loadData(Priority.NORMAL, mockCallback)

        verify(exactly = 1) { mockCallback.onDataReady(null) }
    }

    /**
     * 测试loadData方法在所有检查通过时的行为
     * 验证是否会调用realLoadData方法
     */
    @Test
    fun `loadData should call realLoadData when all checks pass`() {
        every { mockThumbModel.isShortcut } returns false
        val mockLoaderImpl = mockk<IDocThumbnailLoader>(relaxed = true)
        // 使用反射覆盖lazy初始化的值
        val loaderImplField = DocThumbnailFetcher::class.java.getDeclaredField("loaderImpl\$delegate")
        loaderImplField.isAccessible = true
        val delegate = loaderImplField.get(fetcher)
        try {
            val valueField = delegate.javaClass.getDeclaredField("value")
            valueField.isAccessible = true
            valueField.set(delegate, mockLoaderImpl)
        } catch (e: NoSuchFieldException) {
            // 尝试其他字段名
            try {
                val valueField = delegate.javaClass.getDeclaredField("_value")
                valueField.isAccessible = true
                valueField.set(delegate, mockLoaderImpl)
            } catch (e2: NoSuchFieldException) {
                // 如果无法直接访问字段，则重新创建fetcher
                clearAllMocks()
                mockkObject(DocThumbnailLoaderFactory)
                every { DocThumbnailLoaderFactory.getInstance(any()) } returns mockLoaderImpl
                fetcher = DocThumbnailFetcher(context, mockThumbModel, width, height)
            }
        }

        fetcher.loadData(Priority.NORMAL, mockCallback)

        verify(exactly = 1) { mockLoaderImpl.loadThumbnail(any(), any(), any(), any()) }
    }

    /**
     * 测试onShortcutCheck方法对非快捷方式的处理
     * 验证对于非快捷方式是否返回true
     */
    @Test
    fun `onShortcutCheck should return true for non-shortcut`() {
        every { mockThumbModel.isShortcut } returns false
        // 使用反射调用私有方法
        val onShortcutCheckMethod = DocThumbnailFetcher::class.java.getDeclaredMethod(
            "onShortcutCheck",
            DocThumbnail::class.java,
            DataFetcher.DataCallback::class.java
        )
        onShortcutCheckMethod.isAccessible = true
        val result = onShortcutCheckMethod.invoke(fetcher, mockThumbModel, mockCallback) as Boolean
        assertTrue(result)
    }

    /**
     * 测试calculateSampleSize方法
     * 验证是否能正确计算采样率
     */
    @Test
    fun `calculateSampleSize should return correct sample size`() {
        // 创建测试图片文件
        val testImage = File.createTempFile("test", ".jpg")
        testImage.deleteOnExit()
        val testBitmap = Bitmap.createBitmap(200, 300, Bitmap.Config.ARGB_8888)
        testBitmap.compress(Bitmap.CompressFormat.JPEG, 100, testImage.outputStream())

        FileInputStream(testImage).use { fis ->
            val fd = fis.fd
            // 使用反射调用私有方法
            val calculateSampleSizeMethod = DocThumbnailFetcher::class.java.getDeclaredMethod(
                "calculateSampleSize",
                FileDescriptor::class.java
            )
            calculateSampleSizeMethod.isAccessible = true
            val sampleSize = calculateSampleSizeMethod.invoke(fetcher, fd) as Int
            // 预期结果: max(200/100, 300/100) = max(2, 3) = 3
            assertEquals(3, sampleSize)
        }
    }

    /**
     * 测试obtainThumbnail方法在尺寸匹配时的行为
     * 验证是否会返回原始bitmap
     */
    @Test
    fun `obtainThumbnail should return original bitmap when size matches`() {
        val testBitmap = Bitmap.createBitmap(100, 100, Bitmap.Config.ARGB_8888)
        mockkStatic(BitmapFactory::class)
        every { BitmapFactory.decodeFileDescriptor(any(), any(), any()) } returns testBitmap

        // 使用反射调用私有方法
        val obtainThumbnailMethod = DocThumbnailFetcher::class.java.getDeclaredMethod(
            "obtainThumbnail",
            FileDescriptor::class.java,
            Int::class.javaObjectType
        )
        obtainThumbnailMethod.isAccessible = true
        val mockFd = mockk<FileDescriptor>()
        every { mockFd.valid() } returns true
        val result = obtainThumbnailMethod.invoke(fetcher, mockFd, null) as Bitmap?

        assertEquals(testBitmap, result)
    }

    /**
     * 测试obtainThumbnail方法在解码失败时的行为
     * 验证是否会返回null
     */
    @Test
    fun `obtainThumbnail should return null when decoding fails`() {
        mockkStatic(BitmapFactory::class)
        every { BitmapFactory.decodeFileDescriptor(any(), any(), any()) } returns null

        // 使用反射调用私有方法
        val obtainThumbnailMethod = DocThumbnailFetcher::class.java.getDeclaredMethod(
            "obtainThumbnail",
            FileDescriptor::class.java,
            Int::class.javaObjectType
        )
        obtainThumbnailMethod.isAccessible = true
        val mockFd = mockk<FileDescriptor>()
        every { mockFd.valid() } returns true
        val result = obtainThumbnailMethod.invoke(fetcher, mockFd, null) as Bitmap?

        assertNull(result)
    }

    /**
     * 测试calculateSampleSize方法在失败时的行为
     * 验证是否会抛出IOException
     */
    @Test(expected = IOException::class)
    fun `calculateSampleSize should throw IOException on failure`() {
        val invalidFd = mockk<FileDescriptor>()
        every { invalidFd.valid() } returns true
        // 模拟BitmapFactory.decodeFileDescriptor抛出异常
        mockkStatic(BitmapFactory::class)
        every { BitmapFactory.decodeFileDescriptor(any(), any(), any()) } throws IOException("Test exception")
        // 使用反射调用私有方法
        val calculateSampleSizeMethod = DocThumbnailFetcher::class.java.getDeclaredMethod(
            "calculateSampleSize",
            FileDescriptor::class.java
        )
        calculateSampleSizeMethod.isAccessible = true
        try {
            calculateSampleSizeMethod.invoke(fetcher, invalidFd)
        } catch (e: java.lang.reflect.InvocationTargetException) {
            throw e.cause ?: e
        }
    }

    /**
     * 测试obtainThumbnail方法在失败时的行为
     * 验证是否会抛出IOException
     */
    @Test(expected = IOException::class)
    fun `obtainThumbnail should throw IOException on failure`() {
        mockkStatic(BitmapFactory::class)
        every { BitmapFactory.decodeFileDescriptor(any(), any(), any()) } throws IOException("Test OOM")

        // 使用反射调用私有方法
        val obtainThumbnailMethod = DocThumbnailFetcher::class.java.getDeclaredMethod(
            "obtainThumbnail",
            FileDescriptor::class.java,
            Int::class.javaObjectType
        )
        obtainThumbnailMethod.isAccessible = true
        val mockFd = mockk<FileDescriptor>()
        every { mockFd.valid() } returns true
        try {
            obtainThumbnailMethod.invoke(fetcher, mockFd, null)
        } catch (e: java.lang.reflect.InvocationTargetException) {
            throw e.cause ?: e
        }
    }

    /**
     * 测试realLoadData方法处理加载失败的情况
     * 验证是否会调用onLoadFailed回调
     */
    @Test
    fun `realLoadData should handle load failed callback`() {
        val exception = Exception("Test error")
        val mockCallbackInterface = slot<IDocThumbnailCallback>()
        every { mockLoader.loadThumbnail(any(), any(), any(), capture(mockCallbackInterface)) } answers {
            mockCallbackInterface.captured.onLoadFailed(exception)
        }

        // 使用反射调用私有方法
        val realLoadDataMethod = DocThumbnailFetcher::class.java.getDeclaredMethod(
            "realLoadData",
            IDocThumbnailLoader::class.java,
            DataFetcher.DataCallback::class.java
        )
        realLoadDataMethod.isAccessible = true
        realLoadDataMethod.invoke(fetcher, mockLoader, mockCallback)

        verify(exactly = 1) { mockCallback.onLoadFailed(any<Exception>()) }
    }
}