/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.select
 * * Version     : 1.0
 * * Date        : 2020/7/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir

import android.content.Context
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BasePathLoader
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.dfm.DFMManager
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import java.io.File
import java.util.Locale

open class SelectPathLoader(context: Context, path: String) :
    BasePathLoader<Int, BaseFileBean>(context) {
    private var mLoadPath: String? = path

    init {
        initData()
    }

    fun setPath(path: String) {
        mLoadPath = path
    }


    override fun createFromPath(
        volume: String,
        parentPath: String,
        path: String
    ): List<BaseFileBean>? {
        val file = PathFileWrapper(parentPath.plus(File.separator).plus(path))
        if (!AndroidDataHelper.allowEditAndroidData && AndroidDataHelper.isAndroidDataPath(parentPath.plus(File.separator).plus(path))) {
            return null
        }
        return arrayListOf(file)
    }

    override fun prepareHandleBackground() {
        if (KtUtils.checkIsDfmPath(mLoadPath)) {
            DFMManager.openP2pConnectAndWaitDFSReady()
        }
    }

    override fun preHandleResultBackground(list: List<BaseFileBean>): List<BaseFileBean> {
        val currentSort =
            SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getSelectPathKey())
        val isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getSelectPathKey())
        val mLastSort =
            SortModeUtils.getSharedSortMode(appContext, SortModeUtils.BROWSER_LAST_SORT_RECORD)
        Injector.injectFactory<IDocumentExtensionType>()
            ?.sortFiles(list, currentSort, mLastSort, true, isDesc)
        return list
    }

    override fun getVolume(): List<String>? {
        return null
    }

    override fun getPath(): Array<String> {
        return Array(1) { mLoadPath!! }
    }

    override fun getFilterList(): List<Int>? {
        return null
    }

    override fun getItemKey(item: BaseFileBean): Int? {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return null
        }

        return path.lowercase(Locale.getDefault()).hashCode()
    }
}