<?xml version="1.0" encoding="utf-8"?>
<com.filemanager.common.view.HoverSmoothRoundedConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/action_to_dfm"
    android:layout_width="@dimen/main_otg_and_sd_storage_width"
    android:layout_height="@dimen/main_storage_height"
    android:background="@drawable/bg_card_common"
    app:corner_radius="?attr/couiRoundCornerM"
    android:fillViewport="true"
    app:hover_radius="?attr/couiRoundCornerM"
    android:overScrollMode="never"
    app:default_background="true"
    app:solid_color="?attr/couiColorBackground">

    <com.oplus.filemanager.main.view.ScaleSizeTextView
        android:id="@+id/dfm_storage_title"
        android:textAppearance="@style/couiTextButtonM"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:textSize="16sp"
        android:text="@string/storage_external"
        android:textColor="?attr/couiColorLabelPrimary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/dfm_storage_space_desc"
        app:layout_constraintTop_toTopOf="parent" />

    <com.oplus.filemanager.main.view.FormatTextView
        android:id="@+id/dfm_storage_space_desc"
        android:textAppearance="@style/couiTextButtonS"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/storage_space_desc_margin_bottom"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="?attr/couiColorLabelPrimary"
        android:layout_marginEnd="@dimen/main_card_margin"
        app:layout_constraintBottom_toTopOf="@+id/dfm_storage_space_progress"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/dfm_storage_title" />

    <com.oplus.filemanager.main.view.HorizontalProgressBar
        android:id="@+id/dfm_storage_space_progress"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_8dp"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_20dp"
        android:layout_marginBottom="@dimen/dimen_18dp"
        android:forceDarkAllowed="false"
        android:secondaryProgress="100"
        app:couiHorizontalProgressBarBackgroundColor="?attr/couiColorDivider"
        app:couiHorizontalProgressBarProgressColor="?attr/couiColorPrimary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</com.filemanager.common.view.HoverSmoothRoundedConstraintLayout>