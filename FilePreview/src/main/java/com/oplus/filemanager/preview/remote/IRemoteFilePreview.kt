/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: IRemoteFilePreview.kt
 ** Description: IRemoteFilePreview
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.preview.remote

import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import com.coui.appcompat.button.COUIButton
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.core.IPreviewContainerManager
import com.oplus.filemanager.preview.widget.PreviewScrollView

internal interface IRemoteFilePreview {
    /**
     * Attach to container views and show image.
     */
    fun attachToContainer(containerManager: IRemotePreviewContainerManager)

    /**
     * Release when not use it anymore.
     */
    fun release()

    companion object {
        @JvmStatic
        fun obtain(
            fragment: Fragment,
            lifecycleOwner: LifecycleOwner,
            previewModel: RemotePreviewViewModel
        ): IRemoteFilePreview = RemoteFilePreviewImpl(fragment, lifecycleOwner, previewModel)
    }
}

internal interface IRemotePreviewContainerManager : IPreviewContainerManager {
    val infoContainer: ConstraintLayout
    val imageView: AppCompatImageView
    val download: COUIButton
    val timeInfo: AppCompatTextView
    val sizeInfo: AppCompatTextView
    val locationInfo: AppCompatTextView
    val remoteTitle: TextViewSnippet
    val remoteContainer: ConstraintLayout
    val downloadDivider: View
    val scroll: PreviewScrollView
    val rootView: ConstraintLayout
}