/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileListServiceHandler
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/22 11:30
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********        2022/7/22       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist

import android.os.Handler
import android.os.Message
import android.os.Looper
import android.os.HandlerThread
import android.os.Messenger
import android.os.RemoteException
import android.os.Bundle
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Log
import com.google.gson.GsonBuilder
import com.oplus.fileservice.CloseableCoroutineScope
import com.oplus.fileservice.bean.CategoryListData
import com.oplus.fileservice.bean.FileListData
import com.oplus.fileservice.bean.FileResponseStatusCode
import com.oplus.fileservice.bean.ResponseData
import com.oplus.fileservice.bean.WebFileItemBean
import com.oplus.fileservice.bean.WebScannerResult
import com.oplus.fileservice.filelist.scanner.BaseScanner
import com.oplus.fileservice.filelist.scanner.BaseUriScanner
import com.oplus.fileservice.filelist.scanner.FilePathScanner
import com.oplus.fileservice.filelist.scanner.ScannerCallback
import com.oplus.fileservice.utils.WebFileConstant
import com.oplus.fileservice.utils.WebGZipUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.android.asCoroutineDispatcher
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.coroutines.resume
import kotlinx.coroutines.launch

class FileListServiceHandler(scanner: BaseScanner?) :
    Handler(Looper.getMainLooper()) {
    companion object {
        private const val TAG = "FileListServiceHandler"
        private const val MESSAGE_REQUEST_ID = 0x0001
        private val sHandlerWorkThread = HandlerThread(TAG)
        private val sFileHandlerScope by lazy {
            if (sHandlerWorkThread.looper == null) {
                sHandlerWorkThread.start()
            }
            CloseableCoroutineScope(Handler(sHandlerWorkThread.looper).asCoroutineDispatcher())
        }
        const val SCANNER_SUCCESS = 0
        const val SCANNER_FAIL = 1
    }

    @Volatile
    private var mInternalPath: String? = null
        get() {
            if (field == null) {
                field = VolumeEnvironment.getInternalSdPath(appContext)
            }
            return field
        }
    private var mClientMessenger: Messenger? = null
    private var mScanner: BaseScanner? = scanner
    private val mGson = GsonBuilder().excludeFieldsWithoutExposeAnnotation().create()

    override fun handleMessage(msg: Message) {
        try {
            replyMessage(msg)
        } catch (e: RemoteException) {
            Log.e(TAG, "handleMessage RemoteException: ${e.message}")
        }
    }

    private fun replyMessage(msg: Message): Message {
        val replyMsg = Message.obtain()
        Log.d(TAG, "replyMessage -> msg = ${msg.what}")
        when (msg.what) {
            MESSAGE_REQUEST_ID -> {
                try {
                    mClientMessenger = msg.replyTo
                    getData(replyMsg)
                } catch (e: RemoteException) {
                    Log.e(TAG, "onBind -> replyTo error: ${e.message}")
                }
            }
            else -> super.handleMessage(msg)
        }
        return replyMsg
    }

    private fun getData(replyMsg: Message) {
        sFileHandlerScope.launch {
            val finalTime = System.currentTimeMillis()
            Log.d(TAG, "get currentThreadName = ${Thread.currentThread().name}")
            var scannerResult: WebScannerResult? = null
            val startTime = System.currentTimeMillis()
            val withTimeOut: Int? = withTimeoutOrNull(WebFileConstant.WAIT_TIME_OUT) {
                var cancelResult = SCANNER_FAIL
                scannerResult = mScanner?.scannerFiles()
                if (mScanner is BaseUriScanner) {
                    cancelResult = suspendCancellableCoroutine { continuation ->
                        (mScanner as BaseUriScanner).mScannerCallback =
                            object : ScannerCallback {
                                override fun scannerSuc(webScannerResult: WebScannerResult) {
                                    Log.d(
                                        TAG,
                                        "scannerSuc webFiles.size = ${webScannerResult.webFileBeans.size}"
                                    )
                                    scannerResult = webScannerResult
                                    if (continuation.isActive) {
                                        continuation.resume(SCANNER_SUCCESS)
                                    }
                                }

                                override fun scannerFail(any: Any?) {
                                    Log.d(TAG, "scannerFail")
                                    if (continuation.isActive) {
                                        continuation.resume(SCANNER_FAIL)
                                    }
                                }
                            }
                    }
                    Log.d(TAG, "cancelResult $cancelResult")
                }
                Log.d(TAG, "scannerFiles const time = ${System.currentTimeMillis() - startTime}")
                cancelResult
            }
            Log.d(TAG, "getData finish, result is null ${scannerResult == null}, withTimeOut = $withTimeOut")
            withContext(Dispatchers.Main) {
                var result: String = if (withTimeOut == null) {
                    mGson.toJson(FileResponseStatusCode.getTimeOutResponseData())
                } else {
                    if (scannerResult == null) {
                        mGson.toJson(FileResponseStatusCode.getParamErrorResponseData())
                    } else {
                        if (mScanner is FilePathScanner) {
                            val path = (mScanner as FilePathScanner).getPath()
                            Log.d(TAG, "getSpecifyPathResponseData -> path = $path")
                            mGson.toJson(getSpecifyPathResponseData(scannerResult!!, path))
                        } else {
                            mGson.toJson(getCategoryResponseData(scannerResult!!))
                        }
                    }
                }
                var gZipByte = WebGZipUtils.compress(result)
                Log.d(TAG, "gZipByte  size = ${gZipByte?.size}")
                gZipByte?.apply {
                    if (size > WebFileConstant.RESULT_MAX_SIZE) {
                        result =
                            mGson.toJson(FileResponseStatusCode.getResultOverSizedResponseData())
                        gZipByte = WebGZipUtils.compress(result)
                    }
                }
                gZipByte?.let { replyClient(replyMsg, it) }
            }
            Log.d(TAG, "GetData const time = ${System.currentTimeMillis() - finalTime}")
        }
    }

    private fun replyClient(replyMsg: Message, result: ByteArray) {
        Log.d(TAG, "replyClient")
        val data = Bundle()
        data.putByteArray(WebFileConstant.DATA, result)
        replyMsg.data = data
        try {
            mClientMessenger?.send(replyMsg)
        } catch (e: RemoteException) {
            Log.e(TAG, "replyClient -> send error = $e")
        }
    }

    private fun getCategoryResponseData(result: WebScannerResult): ResponseData<CategoryListData<WebFileItemBean>> {
        return ResponseData(
            FileResponseStatusCode.OK.code,
            CategoryListData(result.webFileBeans, result.pageNo, result.total, result.isAlbumSet),
            FileResponseStatusCode.OK.description
        )
    }

    private fun getSpecifyPathResponseData(result: WebScannerResult, path: Array<String>):
            ResponseData<FileListData<WebFileItemBean>> {
        Log.d(TAG, "getSpecifyPathResponseData -> path = ${path.first()}")
        val fileListPath = if (path.isNotEmpty() && path.first().isNotEmpty()) {
            path.first()
        } else {
            mInternalPath
        }
        return ResponseData(
            FileResponseStatusCode.OK.code,
            FileListData(result.webFileBeans, result.pageNo, result.total, fileListPath),
            FileResponseStatusCode.OK.description
        )
    }
}