/*
 * ********************************************************************
 *  * * Copyright (C), 2019 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  SelectFileBrowserViewModelTest.kt
 *  * * Description : SelectFileBrowserViewModelTest.kt
 *  * * Version     : 1.0
 *  * * Date        : 07-26-22 下午5:55
 *  * * Author      : v-wanghonglei
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.coloros.filemanager.viewmodel

import androidx.lifecycle.MutableLiveData
import com.filemanager.common.constants.KtConstants
import com.oplus.selectdir.filebrowser.SelectFileBrowserViewModel
import io.mockk.MockKAnnotations
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class SelectFileBrowserViewModelTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should verify when pressBack without`() {
        val mSelectFileBrowserViewModel = SelectFileBrowserViewModel()
        mSelectFileBrowserViewModel.isFromDetail = true
        mSelectFileBrowserViewModel.mCurrentPath = "a/b/c"
        mSelectFileBrowserViewModel.mModeState.mListModel =
            MutableLiveData<Int>(KtConstants.LIST_NORMAL_MODE)
        val mPositionModel = MutableLiveData<SelectFileBrowserViewModel.PositionModel>()
        mPositionModel.value?.mCurrentPath = "a/b/c"
        Assert.assertEquals(mSelectFileBrowserViewModel.pressBack(), false)
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}