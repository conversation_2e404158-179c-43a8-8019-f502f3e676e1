package com.oplus.filemanager.remotedevice.mock

import android.app.Activity
import android.content.Context
import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo
import com.filemanager.common.bean.remotedevice.RemoteFileData
import com.filemanager.common.bean.remotedevice.Constants
import com.filemanager.common.constants.RemoteDeviceConstants
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.remotedevice.*
import com.oplus.filemanager.interfaze.setting.ISetting
import io.mockk.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * MockRomoteDeviceManager的单元测试类
 * 用于测试模拟远程设备管理器的各项功能
 */
@ExperimentalCoroutinesApi
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class MockRomoteDeviceManagerTest {

    // 测试用的协程调度器
    private val testDispatcher = StandardTestDispatcher()
    // 测试用的协程作用域
    private val testScope = TestScope(testDispatcher)

    /**
     * 测试前置设置
     * 将主调度器设置为测试调度器
     */
    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
    }

    /**
     * 测试后置清理
     * 重置主调度器并销毁MockRomoteDeviceManager
     */
    @After
    fun tearDown() {
        Dispatchers.resetMain()
        MockRomoteDeviceManager.destroy()
    }

    /**
     * 测试获取设备列表功能
     * 验证是否能正确获取设备列表并回调
     */
    @Test
    fun testGetDeviceList() = testScope.runTest {
        // 创建mock回调
        val callback = mockk<(List<RemoteDeviceInfo>) -> Unit>(relaxed = true)
        // 调用获取设备列表方法
        MockRomoteDeviceManager.getDeviceList(callback)
        // 推进时间模拟延迟
        advanceTimeBy(500)
        // 验证回调被调用
        verify { callback.invoke(any()) }
        // 验证获取当前连接设备不为空
        val devices = MockRomoteDeviceManager.getCurrentLinkedRemoteDiceInfo()
        assertNotNull(devices)
    }

    /**
     * 测试添加和移除设备状态监听器
     * 验证监听器的添加和移除功能是否正常
     */
    @Test
    fun testAddAndRemoveDeviceStatusListener() {
        // 创建mock监听器
        val listener = mockk<IRemoteDeviceStatusListener>(relaxed = true)
        // 测试添加监听器
        MockRomoteDeviceManager.addDeviceStatusListener(listener)
        // 测试移除监听器
        MockRomoteDeviceManager.removeDeviceStatusListener(listener)
    }

    /**
     * 测试获取当前连接用户路径
     * 验证是否返回正确的根路径
     */
    @Test
    fun testGetCurrentLinkedUserPath() {
        // 测试仅获取缓存的情况
        var result = MockRomoteDeviceManager.getCurrentLinkedUserPath(null, true)
        assertEquals(Constants.ERROR_CODE_SUC, result.errorCode)
        assertEquals(MockRomoteDeviceManager.MOCK_ROOT_PATH, result.rootPath)

        // 测试实际获取路径的情况
        result = MockRomoteDeviceManager.getCurrentLinkedUserPath(null, false)
        assertEquals(Constants.ERROR_CODE_SUC, result.errorCode)
        assertEquals(MockRomoteDeviceManager.MOCK_ROOT_PATH, result.rootPath)
    }

    /**
     * 测试获取最近文件夹名称
     * 验证返回的名称不为空
     */
    @Test
    fun testGetRecentFolderName() {
        val name = MockRomoteDeviceManager.getRecentFolderName()
        assertNotNull(name)
    }

    /**
     * 测试获取当前连接的远程设备信息
     * 验证返回的设备信息不为空且状态为已连接
     */
    @Test
    fun testGetCurrentLinkedRemoteDeviceInfo() {
        val device = MockRomoteDeviceManager.getCurrentLinkedRemoteDiceInfo()
        assertNotNull(device)
        assertEquals(RemoteDeviceConstants.CONNECTED, device?.deviceStatus)
    }

    /**
     * 测试启动指引页面
     * 验证是否能正确跳转到设置页面
     */
    @Test
    fun testStartInstructionActivity() {
        // 创建mock Activity和Setting
        val activity = mockk<Activity>(relaxed = true)
        val setting = mockk<ISetting>(relaxed = true)
        // mock Injector
        mockkObject(Injector)
        every { Injector.injectFactory<ISetting>() } returns setting
        // 调用启动指引方法
        MockRomoteDeviceManager.startInstructionActivity(activity)
        // 验证跳转设置页面被调用
        verify { setting.startSettingActivity(activity) }
    }

    /**
     * 测试请求下载文件
     * 验证下载请求是否成功发起
     */
    @Test
    fun testRequestDownloadFile() {
        // 创建mock回调
        val callback = mockk<DownloadRemoteFileCallback>(relaxed = true)
        // 调用下载方法
        val result = MockRomoteDeviceManager.requestDownloadFile(
            MockRomoteDeviceManager.MOCK_DEVICE_ID,
            listOf("path1", "path2"),
            "/save/path",
            callback
        )
        // 验证返回的任务ID有效
        assertTrue { result >= 0 }
    }

    /**
     * 测试下载操作(取消/暂停/继续)
     * 验证下载操作是否能正常执行
     */
    @Test
    fun testDownloadOperations() {
        // 创建mock回调
        val callback = mockk<OperateCallback>(relaxed = true)
        // 测试取消下载
        MockRomoteDeviceManager.cancelDownloadFile(MockRomoteDeviceManager.MOCK_DEVICE_ID, 1, callback)
        // 测试暂停下载
        MockRomoteDeviceManager.pauseDownloadFile(MockRomoteDeviceManager.MOCK_DEVICE_ID, 1, callback)
        // 测试继续下载
        MockRomoteDeviceManager.continueDownloadFile(MockRomoteDeviceManager.MOCK_DEVICE_ID, 1, callback)
    }

    /**
     * 测试文件通道是否被占用
     * 验证返回结果是否正确
     */
    @Test
    fun testIsFileChannelOccupied() {
        assertFalse { MockRomoteDeviceManager.isFileChannelOccupied(MockRomoteDeviceManager.MOCK_DEVICE_ID) }
    }

    /**
     * 测试查询功能
     * 验证查询结果不为空
     */
    @Test
    fun testQuery() {
        // 调用查询方法
        val result = MockRomoteDeviceManager.query(MockRomoteDeviceManager.MOCK_DEVICE_ID, "test", 1, 10)
        // 验证返回结果不为空
        assertTrue { result.isNotEmpty() }
    }

    /**
     * 测试销毁功能
     * 验证是否能正常销毁资源
     */
    @Test
    fun testDestroy() {
        MockRomoteDeviceManager.destroy()
    }
}