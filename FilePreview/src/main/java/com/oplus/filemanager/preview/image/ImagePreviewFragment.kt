/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ImagePreviewFragment.kt
 * Description:
 *     The page for preview image file.
 *
 * Version: 1.0
 * Date: 2024-09-05
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-09-05   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.image

import android.content.Context
import android.view.View
import androidx.fragment.app.Fragment
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewFragment
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite
import com.oplus.filemanager.preview.widget.PreviewFilePathItem

internal class ImagePreviewFragment :
    AbsPreviewFragment<FilePreviewViewModel>(R.layout.fragment_preview_image) {

    private companion object {
        private const val TAG = "ImagePreviewFragment"
    }

    override val logTag: String = TAG
    override val fragmentInstance: Fragment = this
    override val viewModelClass: Class<FilePreviewViewModel> = FilePreviewViewModel::class.java
    override val operationsBarId: Int = R.id.preview_operations_bar

    private var filePreview: IImageFilePreview? = null

    override fun isPreviewFileApproved(context: Context, fileBean: BaseFileBean): Boolean =
        FilePreviewTypeCheckHelper(context).isImageFileType(fileBean, TAG)

    override fun onViewModelCreated(view: View, viewModel: FilePreviewViewModel) {
        filePreview = prepareFilePreview(view, viewModel)
    }

    override fun onCreateFilePathItem(view: View): PreviewFilePathItem {
        val item: PreviewFilePathItem = view.findViewById(R.id.preview_remote_location_info)
        item.setLabelGone()
        return item
    }

    override fun onDestroyView() {
        super.onDestroyView()
        filePreview?.release()
        filePreview = null
    }

    private fun prepareFilePreview(view: View, viewModel: FilePreviewViewModel): IImageFilePreview {
        val previewImpl = IImageFilePreview.obtain(viewLifecycleOwner, viewModel).apply {
            attachToContainer(ImagePreviewContainerManager(view))
        }
        return previewImpl
    }
}