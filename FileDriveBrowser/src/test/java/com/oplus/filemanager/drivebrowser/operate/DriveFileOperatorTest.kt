package com.oplus.filemanager.drivebrowser.operate

import android.content.Context
import android.content.Intent
import androidx.activity.ComponentActivity
import com.filemanager.common.MyApplication
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.DriveCache
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.oplus.filemanager.drivebrowser.data.repository.kdocs.KDocsFileDriveRepository
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.drivebrowser.ui.CloudDocsItem
import com.oplus.filemanager.drivebrowser.ui.DocumentPreviewActivity
import com.oplus.filemanager.interfaze.wechat.IWechat
import com.oplus.filemanager.provider.DriveFileDBHelper
import io.mockk.*
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.lang.reflect.Field
import java.util.function.Consumer
import kotlin.test.assertEquals
import kotlin.lazy

/**
 * DriveFileOperator的单元测试类
 * 用于测试DriveFileOperator中的各种云文件操作方法
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
@OptIn(ExperimentalCoroutinesApi::class)
class DriveFileOperatorTest {

    // 模拟的Context对象
    @MockK
    private lateinit var mockContext: Context

    // 模拟的Activity对象
    @MockK
    private lateinit var mockActivity: ComponentActivity

    // 模拟的腾讯云文档仓库
    @MockK
    private lateinit var mockTencentRepo: TencentFileDriveRepository

    // 模拟的金山文档仓库
    @MockK
    private lateinit var mockKDocsRepo: KDocsFileDriveRepository

    // 模拟的微信接口
    @MockK
    private lateinit var mockWechat: IWechat

    // 模拟的Consumer回调接口
    @MockK
    private lateinit var mockConsumer: Consumer<Int>

    // 模拟的Pair类型Consumer回调接口
    @MockK
    private lateinit var mockPairConsumer: Consumer<Pair<Int, String>>

    // 测试用的协程调度器
    private val testDispatcher = UnconfinedTestDispatcher()
    
    // 原始MyApplication实例备份
    private var originalMyAppInstance: Any? = null
    
    // MyApplication的instance字段
    private lateinit var myAppInstanceField: Field
    
    // 原始腾讯文档仓库委托备份
    private var originalTencentDelegate: Any? = null
    
    // 原始金山文档仓库委托备份
    private var originalKDocsDelegate: Any? = null
    
    // 原始应用上下文备份
    private var originalSAppContext: Any? = null

    /**
     * 测试前的准备工作
     * 1. 初始化MockK注解
     * 2. 设置模拟的MyApplication实例
     * 3. 设置协程调度器
     * 4. 模拟静态方法和对象
     * 5. 通过反射设置repository的模拟实例
     */
    @Before
    fun setup() {
        MockKAnnotations.init(this)
        
        // 初始化MyApplication实例
        try {
            val myAppClass = Class.forName("com.filemanager.common.MyApplication")
            myAppInstanceField = myAppClass.getDeclaredField("instance")
            myAppInstanceField.isAccessible = true
            originalMyAppInstance = myAppInstanceField.get(null)
            val myAppMock = mockk<MyApplication>(relaxed = true)
            every { myAppMock.appContext } returns mockContext
            myAppInstanceField.set(null, myAppMock)

            // 设置sAppContext
            val sAppContextField = myAppClass.getDeclaredField("sAppContext")
            sAppContextField.isAccessible = true
            originalSAppContext = sAppContextField.get(null)
            sAppContextField.set(null, mockContext)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        Dispatchers.setMain(testDispatcher)
        mockkStatic(Log::class)
        mockkStatic(StatisticsUtils::class)
        mockkObject(DriveCache)
        mockkStatic(DriveFileDBHelper::class)
        mockkObject(Injector)

        every { Injector.injectFactory<IWechat>() } returns mockWechat
        every { mockWechat.openFile(any()) } just Runs
        every { Log.d(any(), any()) } just Runs
        every { StatisticsUtils.onCommon(any(), any(), any<Map<String, String>>()) } just Runs
        every { DriveCache.putDeleteCache(any()) } just Runs
        every { DriveFileDBHelper.deleteDocument(any(), any()) } just Runs
        every { DriveFileDBHelper.deleteFolder(any(), any()) } just Runs

        // 通过反射设置repository委托实例
        try {
            val tencentDelegateField = DriveFileOperator::class.java.getDeclaredField("tencentRepository\$delegate")
            tencentDelegateField.isAccessible = true
            originalTencentDelegate = tencentDelegateField.get(DriveFileOperator)
            tencentDelegateField.set(DriveFileOperator, lazy { mockTencentRepo })

            val kDocsDelegateField = DriveFileOperator::class.java.getDeclaredField("kDocsRepository\$delegate")
            kDocsDelegateField.isAccessible = true
            originalKDocsDelegate = kDocsDelegateField.get(DriveFileOperator)
            kDocsDelegateField.set(DriveFileOperator, lazy { mockKDocsRepo })
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 测试后的清理工作
     * 1. 重置协程调度器
     * 2. 清除所有模拟
     * 3. 恢复原始MyApplication实例
     * 4. 恢复原始repository委托实例
     */
    @After
    fun tearDown() {
        Dispatchers.resetMain()
        clearAllMocks()
        unmockkAll()

        // 恢复原始MyApplication实例
        try {
            myAppInstanceField.set(null, originalMyAppInstance)
            
            // 恢复sAppContext
            val myAppClass = Class.forName("com.filemanager.common.MyApplication")
            val sAppContextField = myAppClass.getDeclaredField("sAppContext")
            sAppContextField.isAccessible = true
            sAppContextField.set(null, originalSAppContext)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        // 恢复原始repository委托实例
        try {
            val tencentDelegateField = DriveFileOperator::class.java.getDeclaredField("tencentRepository\$delegate")
            tencentDelegateField.isAccessible = true
            tencentDelegateField.set(DriveFileOperator, originalTencentDelegate)

            val kDocsDelegateField = DriveFileOperator::class.java.getDeclaredField("kDocsRepository\$delegate")
            kDocsDelegateField.isAccessible = true
            kDocsDelegateField.set(DriveFileOperator, originalKDocsDelegate)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 测试删除空列表的云文件
     * 预期结果：返回0，且不调用任何删除方法
     */
    @Test
    fun `test deleteCloudFiles with empty list should return zero`() = runTest {
        // When
        val result = DriveFileOperator.deleteCloudFiles(emptyList())

        // Then
        assertEquals(0, result)
        coVerify(exactly = 0) { mockTencentRepo.deleteFile(any(), any()) }
        coVerify(exactly = 0) { mockTencentRepo.deleteFolder(any()) }
        coVerify(exactly = 0) { mockKDocsRepo.deleteFile(any(), any()) }
        coVerify(exactly = 0) { mockKDocsRepo.deleteFolder(any()) }
    }
}