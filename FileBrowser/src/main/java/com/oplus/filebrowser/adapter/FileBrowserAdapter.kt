/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.adapter
 * * Version     : 1.0
 * * Date        : 2020/5/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseFolderAnimAdapter
import com.filemanager.common.base.BaseSelectionViewHolder
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.isActivityAndInvalid
import com.filemanager.common.view.TextViewSnippet
import com.filemanager.common.viewholder.BaseFileBrowserVH
import com.filemanager.common.viewholder.FileBrowserGridVH
import com.filemanager.common.viewholder.FileBrowserLargeListVH
import com.oplus.dropdrag.SelectionTracker
import com.oplus.filebrowser.FileBrowserActivity
import com.oplus.filebrowser.R
import com.filemanager.common.viewholder.FileBrowserListVH
import com.oplus.filemanager.ad.AdViewHolder
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.interfaze.cloudconfig.ICloudConfigApi
import java.util.*

private const val TAG = "FileBrowserAdapter"

class FileBrowserAdapter(content: Context, lifecycle: Lifecycle)
    : BaseFolderAnimAdapter<RecyclerView.ViewHolder, BaseFileBean>(content), LifecycleObserver {
    private var mKeyWord: String? = null
    private val mSizeCache = HashMap<String, String>()
    private var mThreadManager = ThreadManager(lifecycle)
    private val mImgRadius = MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.file_list_bg_radius)

    init {
        lifecycle.addObserver(this)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        super.onRemoveCallBack()
        mSizeCache.clear()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if ((BaseFileBean.TYPE_LABEL_FILE == viewType) || (BaseFileBean.TYPE_FILE_LIST_HEADER == viewType)) {
            LabelViewHolder(LayoutInflater.from(parent.context).inflate(R.layout.filebrowser_lable_item, parent, false))
        } else if (KtConstants.SCAN_MODE_GRID == viewType) {
            FileBrowserGridVH(LayoutInflater.from(parent.context).inflate(FileBrowserGridVH.getLayoutId(), parent, false))
        } else if (BaseFileBean.TYPE_FILE_AD == viewType) {
            AdViewHolder(LayoutInflater.from(parent.context).inflate(com.filemanager.common.R.layout.item_main_ad, parent, false))
        } else if (KtConstants.SCAN_MODE_LIST_LARGE == viewType) {
            FileBrowserLargeListVH.create(parent, mImgRadius)
        } else {
            FileBrowserListVH(LayoutInflater.from(parent.context).inflate(FileBrowserListVH.getLayoutId(), parent, false), mImgRadius).apply {
                setUpdateViewList(object : FileBrowserListVH.UpdateViewListener {
                    override fun updateViewByAlias(textViewSnippet: TextViewSnippet, path: String) {
                        val cloudConfigApi = Injector.injectFactory<ICloudConfigApi>()
                        cloudConfigApi?.updateViewByAlias(textViewSnippet, path)
                    }
                })
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        var type = if ((mFiles.size == 0) && (position >= 0) && (position < mOldFiles.size)) {
             mOldFiles[position].mFileWrapperViewType ?: mScanViewModel
        } else if ((position >= 0) && (position < mFiles.size)) {
            mFiles[position].mFileWrapperViewType ?: mScanViewModel
        } else {
            mScanViewModel
        }
        if (type == KtConstants.SCAN_MODE_LIST && WindowUtils.supportLargeScreenLayout(context)) {
            type = KtConstants.SCAN_MODE_LIST_LARGE
        }
        return  type
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (mContext.isActivityAndInvalid()) {
            Log.d(TAG, "onBindViewHolder: Activity is Destroyed!")
            return
        }
        if ((position < 0) || (position >= mFiles.size)) {
            return
        }
        if ((AdvertManager.isAdEnabled()) && (mFiles[position].mFileWrapperViewType == BaseFileBean.TYPE_FILE_AD)
            && (holder is AdViewHolder)) {
            val adMgr: AdvertManager? = (mContext as? FileBrowserActivity)?.mAdManager
            adMgr?.let {
                holder.addAdView(adMgr.getAdView(TAG))
            }
            return
        }
        if (holder.itemView.alpha == 0f) {
            holder.itemView.alpha = 1f
        }
        val file = mFiles[position]
        if (holder is BaseFileBrowserVH) {
            holder.loadData(mContext, getItemKey(file, position),
                mFiles[position], mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
        } else if (holder is LabelViewHolder) {
            holder.updateKey(SelectionTracker.SKIP_INT_ITEM_ID)
            if (file.mFileWrapperViewType == BaseFileBean.TYPE_FILE_LIST_HEADER) {
                if (file.mFileWrapperLabel != null) {
                    holder.mLabel?.text = mContext.getString(file.mFileWrapperLabel!!, file.mFileWrapperTypeNum)
                } else {
                    holder.mLabel?.text = ""
                }
            } else if (file.mFileWrapperViewType == BaseFileBean.TYPE_LABEL_FILE) {
                if (file.mFileWrapperLabel != null) {
                    holder.mLabel?.text = mContext.getString(file.mFileWrapperLabel!!) + " " + file.mFileWrapperTypeNum
                } else {
                    holder.mLabel?.text = ""
                }
            }
        }
        if (holder is FileBrowserListVH) {
            holder.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
        } else if (holder is FileBrowserLargeListVH) {
            holder.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
        }
        if (file.mIsDirectory) {
            val tag = if (DragUtils.getSelectedFiles()?.contains(file) == true) {
                DropTag(CategoryHelper.CATEGORY_FILE_BROWSER, DropTag.Type.ITEM_VIEW_NOTRESPONSE)
            } else {
                DropTag(CategoryHelper.CATEGORY_FILE_BROWSER, DropTag.Type.ITEM_VIEW_FOLDER)
            }
            tag.filePath = file.mData
            holder.itemView.tag = tag
        } else {
            holder.itemView.tag = null
        }
        holder.itemView?.let {
            if (DragUtils.getSelectedFiles()?.contains(file) == true && DragUtils.isDragging) {
                it.alpha = DragUtils.SELECTED_ITEMVIEW_ALPHA
            } else {
                it.alpha = 1f
            }
        }
    }

    fun setKeyWord(key: String) {
        mKeyWord = key
    }

    override fun getItemId(position: Int): Long {
        return getItemKeyByPosition(position)?.toLong() ?: SelectionTracker.NO_LONG_ITEM_ID
    }

    //getItemKey must return different code,position hashcode may return same as path hashcode
    override fun getItemKey(item: BaseFileBean, position: Int): Int {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return position.hashCode()
        }

        return path.toLowerCase(Locale.getDefault()).hashCode()
    }

    private class LabelViewHolder(convertView: View) : BaseSelectionViewHolder(convertView, false) {
        var mLabel: TextView? = null

        init {
            mLabel = convertView.findViewById(R.id.label_view)
        }
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        if (mScanViewModel == KtConstants.SCAN_MODE_LIST) {
            setChoiceModeAnimFlag(flag)
        }
    }
}
