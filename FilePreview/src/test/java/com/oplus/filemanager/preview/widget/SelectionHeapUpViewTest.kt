package com.oplus.filemanager.preview.widget

import android.content.Context
import android.graphics.drawable.Drawable
import android.view.View
import android.widget.FrameLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.selection.data.HeadUpImageData
import com.oplus.filemanager.preview.selection.data.SelectionItem
import io.mockk.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import java.lang.reflect.Method

/**
 * SelectionHeapUpView的单元测试类
 * 测试SelectionHeapUpView的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SelectionHeapUpViewTest {

    private lateinit var context: Context
    private lateinit var selectionHeapUpView: SelectionHeapUpView

    /**
     * 在每个测试方法执行前初始化测试环境
     */
    @Before
    fun setUp() {
        context = RuntimeEnvironment.application.applicationContext
        selectionHeapUpView = SelectionHeapUpView(context)
    }

    /**
     * 在每个测试方法执行后清理测试环境
     */
    @After
    fun tearDown() {
        clearAllMocks()
        selectionHeapUpView.onDeselectionAnimationEnd {} // 清除所有待处理的回调
    }

    /**
     * 测试当isIcon为true时，createHeapUpImageView应创建PreviewIconItemView
     */
    @Test
    fun `createHeapUpImageView should create PreviewIconItemView when isIcon is true`() {
        val imageData = HeadUpImageData(
            fileBean = mockk(relaxed = true),
            drawable = null,
            isIcon = true,
            isVideoType = false
        )

        val result = selectionHeapUpView.createHeapUpImageViewForTest(imageData)

        assertTrue(getClassName(result) == "PreviewIconItemView")
    }

    /**
     * 测试当isVideoType为true时，createHeapUpImageView应创建PreviewVideoItemView
     */
    @Test
    fun `createHeapUpImageView should create PreviewVideoItemView when isVideoType is true`() {
        val imageData = HeadUpImageData(
            fileBean = mockk(relaxed = true),
            drawable = null,
            isIcon = false,
            isVideoType = true
        )

        val result = selectionHeapUpView.createHeapUpImageViewForTest(imageData)

        assertTrue(getClassName(result) == "PreviewVideoItemView")
    }

    /**
     * 测试当isIcon和isVideoType都为false时，createHeapUpImageView应创建PreviewImageItemView
     */
    @Test
    fun `createHeapUpImageView should create PreviewImageItemView when neither isIcon nor isVideoType is true`() {
        val imageData = HeadUpImageData(
            fileBean = mockk(relaxed = true),
            drawable = null,
            isIcon = false,
            isVideoType = false
        )

        val result = selectionHeapUpView.createHeapUpImageViewForTest(imageData)

        assertTrue(getClassName(result) == "PreviewImageItemView")
    }

    /**
     * 测试updateHeapUp方法在给定有效数据时应添加视图
     */
    @Test
    fun `updateHeapUp should add views when given valid data`() {
        val fileBean: BaseFileBean = mockk(relaxed = true)
        every { fileBean.mData } returns "path1"

        val selectionItem = SelectionItem(fileBean, 1)
        val drawable: Drawable = mockk(relaxed = true)
        val imageData = HeadUpImageData(fileBean, drawable, false, false, false, null)

        val heapUpMap = mapOf(selectionItem to imageData)

        selectionHeapUpView.updateHeapUp(heapUpMap)

        assertEquals(1, selectionHeapUpView.getHeapUpViewMapSize())
    }

    /**
     * 测试updateHeapUp方法在路径为空时不应添加视图
     */
    @Test
    fun `updateHeapUp should not add views when path is empty`() {
        val fileBean: BaseFileBean = mockk(relaxed = true)
        every { fileBean.mData } returns ""

        val selectionItem = SelectionItem(fileBean, 1)
        val drawable: Drawable = mockk(relaxed = true)
        val imageData = HeadUpImageData(fileBean, drawable, false, false, false, null)

        val heapUpMap = mapOf(selectionItem to imageData)

        selectionHeapUpView.updateHeapUp(heapUpMap)

        assertEquals(0, selectionHeapUpView.getHeapUpViewMapSize())
    }

    /**
     * 测试updateHeapUp方法在路径为null时不应添加视图
     */
    @Test
    fun `updateHeapUp should not add views when path is null`() {
        val fileBean: BaseFileBean = mockk(relaxed = true)
        every { fileBean.mData } returns null

        val selectionItem = SelectionItem(fileBean, 1)
        val drawable: Drawable = mockk(relaxed = true)
        val imageData = HeadUpImageData(fileBean, drawable, false, false, false, null)

        val heapUpMap = mapOf(selectionItem to imageData)

        selectionHeapUpView.updateHeapUp(heapUpMap)

        assertEquals(0, selectionHeapUpView.getHeapUpViewMapSize())
    }

    /**
     * 测试updateImageData方法应更新已存在的视图
     */
    @Test
    fun `updateImageData should update existing view`() {
        val fileBean: BaseFileBean = mockk(relaxed = true)
        every { fileBean.mData } returns "path1"
        every { fileBean.hashCode() } returns 123

        val selectionItem = SelectionItem(fileBean, 1)
        val drawable1: Drawable = mockk(relaxed = true)
        val imageData1 = HeadUpImageData(fileBean, drawable1, false, false, false, null)

        val heapUpMap = mapOf(selectionItem to imageData1)
        selectionHeapUpView.updateHeapUp(heapUpMap)

        val drawable2: Drawable = mockk(relaxed = true)
        val imageSizeInfo = HeadUpImageData.ImageSizeInfo(100, 200)
        val imageData2 = HeadUpImageData(fileBean, drawable2, false, false, false, imageSizeInfo)

        selectionHeapUpView.updateImageData(imageData2)

        assertTrue(true)
    }

    /**
     * 测试updateImageData方法在视图不存在时不应崩溃
     */
    @Test
    fun `updateImageData should not crash when view does not exist`() {
        val fileBean: BaseFileBean = mockk(relaxed = true)
        every { fileBean.mData } returns "nonexistent_path"
        every { fileBean.hashCode() } returns 123

        val drawable: Drawable = mockk(relaxed = true)
        val imageData = HeadUpImageData(fileBean, drawable, false, false, false, null)

        selectionHeapUpView.updateImageData(imageData)

        assertTrue(true)
    }

    /**
     * 测试updateLayoutLevel方法应按selectPriority排序项目
     */
    @Test
    fun `updateLayoutLevel should sort items by selectPriority`() {
        val fileBean1: BaseFileBean = mockk(relaxed = true)
        every { fileBean1.mData } returns "path1"
        val fileBean2: BaseFileBean = mockk(relaxed = true)
        every { fileBean2.mData } returns "path2"
        val fileBean3: BaseFileBean = mockk(relaxed = true)
        every { fileBean3.mData } returns "path3"

        val selectionItem1 = SelectionItem(fileBean1, 1)
        val selectionItem2 = SelectionItem(fileBean2, 3)
        val selectionItem3 = SelectionItem(fileBean3, 2)

        val drawable: Drawable = mockk(relaxed = true)
        val imageData = HeadUpImageData(mockk(relaxed = true), drawable, false, false, false, null)

        val itemView1 = selectionHeapUpView.createHeapUpImageViewForTest(imageData)
        val itemView2 = selectionHeapUpView.createHeapUpImageViewForTest(imageData)
        val itemView3 = selectionHeapUpView.createHeapUpImageViewForTest(imageData)

        val viewItem1 = selectionHeapUpView.createHeapUpViewItemForTest(selectionItem1, itemView1)
        val viewItem2 = selectionHeapUpView.createHeapUpViewItemForTest(selectionItem2, itemView2)
        val viewItem3 = selectionHeapUpView.createHeapUpViewItemForTest(selectionItem3, itemView3)

        val viewItems = listOf(viewItem1, viewItem2, viewItem3)
        selectionHeapUpView.updateLayoutLevelForTest(viewItems)

        assertEquals(1, getLayoutLevel(viewItem1))
        assertEquals(3, getLayoutLevel(viewItem2))
        assertEquals(2, getLayoutLevel(viewItem3))
    }

    /**
     * 测试getRotationByLevel方法应返回正确的旋转角度
     */
    @Test
    fun `getRotationByLevel should return correct rotations`() {
        assertEquals(0f, SelectionHeapUpViewTestHelper.getRotationByLevelForTest(3), 0.01f)
        assertEquals(-4f, SelectionHeapUpViewTestHelper.getRotationByLevelForTest(2), 0.01f)
        assertEquals(4f, SelectionHeapUpViewTestHelper.getRotationByLevelForTest(1), 0.01f)
    }

    /**
     * 测试getRotationByLevel方法在传入无效层级时应抛出异常
     */
    @Test(expected = IllegalArgumentException::class)
    fun `getRotationByLevel should throw exception for invalid level`() {
        try {
            SelectionHeapUpViewTestHelper.getRotationByLevelForTest(999)
        } catch (e: Exception) {
            if (e is IllegalArgumentException) {
                throw e
            } else if (e is java.lang.reflect.InvocationTargetException) {
                throw e.targetException
            }
            throw e
        }
    }

    /**
     * 测试PreviewImageItemView应正确创建
     */
    @Test
    fun `PreviewImageItemView should be created correctly`() {
        val view = selectionHeapUpView.createPreviewImageItemViewForTest(context)
        assertNotNull(view)
        assertTrue(view is AppCompatImageView)
        assertTrue(isIHeapUpImageView(view))
    }

    /**
     * 测试PreviewVideoItemView应正确创建
     */
    @Test
    fun `PreviewVideoItemView should be created correctly`() {
        val view = selectionHeapUpView.createPreviewVideoItemViewForTest(context)
        assertNotNull(view)
        assertTrue(view is AppCompatImageView)
        assertTrue(isIHeapUpImageView(view))
    }

    /**
     * 测试PreviewIconItemView应正确创建
     */
    @Test
    fun `PreviewIconItemView should be created correctly`() {
        val view = selectionHeapUpView.createPreviewIconItemViewForTest(context)
        assertNotNull(view)
        assertTrue(view is FrameLayout)
        assertTrue(isIHeapUpImageView(view))
        assertNotNull(getImageViewFromIconItemView(view))
    }

    /**
     * 测试onDeselectionAnimationEnd方法应设置回调
     */
    @Test
    fun `onDeselectionAnimationEnd should set callback`() {
        var called = false
        selectionHeapUpView.onDeselectionAnimationEnd {
            called = true
        }

        selectionHeapUpView.invokeOnDeselectionEndForTest()
        assertTrue(called)
    }

    /**
     * 获取对象的类名
     */
    private fun getClassName(obj: Any): String {
        return obj.javaClass.simpleName
    }

    /**
     * 通过反射获取视图项的布局层级
     */
    private fun getLayoutLevel(viewItem: Any): Int {
        val field = viewItem.javaClass.getDeclaredField("layoutLevel")
        field.isAccessible = true
        return field.getInt(viewItem)
    }

    /**
     * 检查对象是否实现了IHeapUpImageView接口
     */
    private fun isIHeapUpImageView(view: Any): Boolean {
        // 通过检查类及其父类是否实现了IHeapUpImageView接口来判断
        // 需要检查完整的接口名称，包括内部类的情况
        var clazz: Class<*>? = view.javaClass
        while (clazz != null) {
            val interfaces = clazz.interfaces
            if (interfaces.any { it.name == "com.oplus.filemanager.preview.widget.SelectionHeapUpView\$IHeapUpImageView" } ||
                interfaces.any { it.name.endsWith("IHeapUpImageView") }) {
                return true
            }
            clazz = clazz.superclass
        }
        return false
    }

    /**
     * 从PreviewIconItemView中获取ImageView
     */
    private fun getImageViewFromIconItemView(view: Any): AppCompatImageView? {
        try {
            val field = view.javaClass.getDeclaredField("imageView")
            field.isAccessible = true
            return field.get(view) as AppCompatImageView
        } catch (e: Exception) {
            return null
        }
    }
}

/**
 * SelectionHeapUpView的测试辅助对象
 */
object SelectionHeapUpViewTestHelper {
    /**
     * 测试getRotationByLevel方法
     */
    fun getRotationByLevelForTest(layoutLevel: Int): Float {
        val method = Class.forName("com.oplus.filemanager.preview.widget.SelectionHeapUpView").getDeclaredMethod("getRotationByLevel", Int::class.java)
        method.isAccessible = true
        return method.invoke(null, layoutLevel) as Float
    }
}

/**
 * 扩展函数：测试createHeapUpImageView方法
 */
internal fun SelectionHeapUpView.createHeapUpImageViewForTest(imageData: HeadUpImageData): Any {
    val method = this.javaClass.getDeclaredMethod("createHeapUpImageView", HeadUpImageData::class.java)
    method.isAccessible = true
    return method.invoke(this, imageData)
}

/**
 * 扩展函数：测试updateLayoutLevel方法
 */
internal fun SelectionHeapUpView.updateLayoutLevelForTest(viewItems: Collection<Any>) {
    val method = this.javaClass.getDeclaredMethod("updateLayoutLevel", Collection::class.java)
    method.isAccessible = true
    method.invoke(this, viewItems)
}

/**
 * 扩展函数：获取heapUpViewMap的大小
 */
internal fun SelectionHeapUpView.getHeapUpViewMapSize(): Int {
    val field = this.javaClass.getDeclaredField("heapUpViewMap")
    field.isAccessible = true
    val map = field.get(this) as Map<*, *>
    return map.size
}

/**
 * 扩展函数：测试onDeselectionEnd回调
 */
internal fun SelectionHeapUpView.invokeOnDeselectionEndForTest() {
    val field = this.javaClass.getDeclaredField("onDeselectionEnd")
    field.isAccessible = true
    val callback = field.get(this) as? (() -> Unit)
    callback?.invoke()
}

/**
 * 扩展函数：创建HeapUpViewItem测试实例
 */
internal fun SelectionHeapUpView.createHeapUpViewItemForTest(itemData: SelectionItem, itemView: Any): Any {
    val clazz = this.javaClass.classLoader!!.loadClass("com.oplus.filemanager.preview.widget.SelectionHeapUpView\$HeapUpViewItem")
    // 修改构造函数参数类型为正确的接口类型
    val constructor = clazz.getDeclaredConstructor(SelectionItem::class.java, 
        Class.forName("com.oplus.filemanager.preview.widget.SelectionHeapUpView\$IHeapUpImageView"))
    constructor.isAccessible = true
    return constructor.newInstance(itemData, itemView)
}

/**
 * 扩展函数：创建PreviewImageItemView测试实例
 */
internal fun SelectionHeapUpView.createPreviewImageItemViewForTest(context: Context): Any {
    val clazz = this.javaClass.classLoader!!.loadClass("com.oplus.filemanager.preview.widget.SelectionHeapUpView\$PreviewImageItemView")
    val constructor = clazz.getDeclaredConstructor(Context::class.java)
    constructor.isAccessible = true
    return constructor.newInstance(context)
}

/**
 * 扩展函数：创建PreviewVideoItemView测试实例
 */
internal fun SelectionHeapUpView.createPreviewVideoItemViewForTest(context: Context): Any {
    val clazz = this.javaClass.classLoader!!.loadClass("com.oplus.filemanager.preview.widget.SelectionHeapUpView\$PreviewVideoItemView")
    val constructor = clazz.getDeclaredConstructor(Context::class.java)
    constructor.isAccessible = true
    return constructor.newInstance(context)
}

/**
 * 扩展函数：创建PreviewIconItemView测试实例
 */
internal fun SelectionHeapUpView.createPreviewIconItemViewForTest(context: Context): Any {
    val clazz = this.javaClass.classLoader!!.loadClass("com.oplus.filemanager.preview.widget.SelectionHeapUpView\$PreviewIconItemView")
    val constructor = clazz.getDeclaredConstructor(Context::class.java)
    constructor.isAccessible = true
    return constructor.newInstance(context)
}