<?xml version="1.0" encoding="utf-8"?>
<com.filemanager.common.view.SelectItemLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentTop="true"
    android:forceDarkAllowed="false"
    android:background="@drawable/select_list_item_background_selector"
    android:minHeight="?android:attr/listPreferredItemHeightSmall">

    <com.filemanager.common.view.FileThumbView
        android:id="@+id/file_list_item_icon"
        android:layout_width="@dimen/main_image_width"
        android:layout_height="@dimen/main_image_height"
        android:layout_marginStart="@dimen/default_margin"
        android:forceDarkAllowed="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/file_list_item_info_margin"
        android:layout_marginTop="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginEnd="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginBottom="@dimen/file_list_right_layout_margin_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/jump_mark"
        app:layout_constraintStart_toEndOf="@+id/file_list_item_icon"
        app:layout_constraintTop_toTopOf="parent">

        <com.filemanager.common.view.TextViewSnippet
            android:id="@+id/file_list_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:ellipsize="middle"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textSize="@dimen/file_list_item_title_text_size" />

        <TextView
            android:id="@+id/mark_file_list_item_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/file_list_item_title"
            android:layout_marginTop="@dimen/file_list_item_detail_margin_top"
            android:adjustViewBounds="true"
            android:ellipsize="marquee"
            android:maxWidth="@dimen/file_list_item_info_selected_width_new_new"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textColor="@color/black_55_percent"
            android:textSize="@dimen/file_list_item_detail_text_size" />

    </RelativeLayout>

    <ImageView
        android:id="@+id/jump_mark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/default_margin"
        android:contentDescription="@null"
        android:src="@drawable/coui_btn_next"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.coui.appcompat.checkbox.COUICheckBox
        android:id="@+id/listview_scrollchoice_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/sort_item_padding_right_out"
        android:clickable="false"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="0.33dp"
        android:layout_marginStart="@dimen/dimen_76dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:background="?attr/couiColorDivider"
        app:layout_constraintBottom_toBottomOf="parent" />
</com.filemanager.common.view.SelectItemLayout>