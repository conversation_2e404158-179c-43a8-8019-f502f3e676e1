/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DownloadRemoteFileService
 * * Description : 远程设备下载文件 Service
 * * Version     : 1.0
 * * Date        : 2024/12/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download.service

import android.content.Intent
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.category.remotedevice.disconect.DisconnectHelper
import com.oplus.filemanager.category.remotedevice.download.monitor.DownloadNotifyMonitor
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice

class DownloadRemoteFileService : LifecycleService() {

    companion object {
        private const val TAG = "DownloadRemoteFileService"
    }

    private var downloadDispatcher: RemoteFileDownloadDispatcher? = null


    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "onCreate $this")
        initDownloadDispatcher()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        Log.d(TAG, "onStartCommand $intent")
        if (RemoteFileDownloadDispatcher.ACTION_STOP_SERVICE == intent?.action) {
            stop()
            return START_NOT_STICKY
        }
        initDownloadDispatcher()
        intent?.let {
            downloadDispatcher?.onStartCommand(it)
        }

        return START_NOT_STICKY
    }

    private fun stop() {
        stopSelf()
        DownloadNotifyMonitor.showForeground = true
        downloadDispatcher?.cancelMonitor()
        downloadDispatcher = null
        Log.e(TAG, "stop service")
        //下载完成时，App端是否处于后台，如果是处于后台，则触发10分钟的断链任务
        checkNeedToTrigDisconnect()
    }

    private fun initDownloadDispatcher() {
        Log.d(TAG, "initDownloadDispatcher $downloadDispatcher")
        if (downloadDispatcher == null) {
            downloadDispatcher = RemoteFileDownloadDispatcher(this, lifecycleScope)
        }
    }

    private fun checkNeedToTrigDisconnect() {
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        val currentLinkInfo = remoteDeviceApi?.getCurrentLinkedRemoteDiceInfo()
        Log.d(TAG, "checkNeedToTrigDisconnect, isAppBackground ${MyApplication.appInBackground}, currentLinkInfo: $currentLinkInfo")
        if (MyApplication.appInBackground == true && currentLinkInfo != null) {
            //下载完成时，app在后台，同时当前remote_mac链接状态为已链接时，触发断链任务
            DisconnectHelper.checkAndEnqueWork()
        }
    }
}