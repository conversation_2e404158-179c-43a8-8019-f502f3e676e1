/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : TimeUtilsTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/12/12 15:50
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2023/12/12       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import android.os.SystemClock
import com.filemanager.common.utils.SdkUtils
import com.oplus.filemanager.drivebrowser.data.utils.TimeUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.time.Clock
import java.time.DateTimeException

class TimeUtilsTest {

    @Before
    fun setup() {
        mockkStatic(SdkUtils::class)
        mockkStatic(SystemClock::class)
    }

    @After
    fun teardown() {
        unmockkStatic(SdkUtils::class)
        unmockkStatic(SystemClock::class)
    }

    @Test
    fun should_return_long_when_obtainTimestamp() {
        every { SdkUtils.isAtLeastT() }.returns(false)
        var current = System.currentTimeMillis()
        var timestamp = TimeUtils.obtainTimestamp()
        Assert.assertTrue(timestamp - current < 1000L)

        every { SdkUtils.isAtLeastT() }.returns(true)
        val clock = mockk<Clock>()
        current = System.currentTimeMillis()
        var networkTime = current - TimeUtils.VALID_TIMESTAMP_INTERVAL - 1
        every { clock.millis() }.returns(networkTime)
        every { SystemClock.currentNetworkTimeClock() }.returns(clock)
        timestamp = TimeUtils.obtainTimestamp()
        Assert.assertTrue(timestamp - current < 1000L)

        networkTime += 2
        every { clock.millis() }.returns(networkTime)
        timestamp = TimeUtils.obtainTimestamp()
        Assert.assertEquals(networkTime, timestamp)

        every { SystemClock.currentNetworkTimeClock() }.throws(DateTimeException(""))
        current = System.currentTimeMillis()
        timestamp = TimeUtils.obtainTimestamp()
        Assert.assertTrue(timestamp - current < 1000L)
    }
}