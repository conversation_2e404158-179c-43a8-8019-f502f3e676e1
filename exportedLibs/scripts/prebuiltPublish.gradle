apply plugin: 'maven-publish'

class PrebuiltMavenExtensions {
    String url
    String username = null
    String password = null
    boolean allowInsecureProtocol = true

    boolean hasCredentials() {
        return username != null || password != null
    }
}

class PrebuiltPublishExtensions {
    final PrebuiltMavenExtensions mavenExt = new PrebuiltMavenExtensions()

    String groupId
    String artifactId
    String version
    PublishArtifact prebuiltArtifact

    void ensureConfigsAvailable() {
        ensureValueNotNull(PrebuiltPublishPlugin.EXTENSION_NAME, "prebuiltArtifact", prebuiltArtifact)
        ensureValueNotNull(PrebuiltPublishPlugin.EXTENSION_NAME, "groupId", groupId)
        ensureValueNotNull(PrebuiltPublishPlugin.EXTENSION_NAME, "artifactId", artifactId)
        ensureValueNotNull(PrebuiltPublishPlugin.EXTENSION_NAME, "version", version)
        ensureValueNotNull("targetMaven", "url", mavenExt.url)
    }

    @SuppressWarnings("unused")
    void targetMaven(Closure mavenClosure) {
        mavenClosure.delegate = mavenExt
        mavenClosure.call()
    }

    private static void ensureValueNotNull(String configName, String valueName, Object value) {
        if (value == null) {
            throw new IllegalArgumentException("No $valueName in $configName configs")
        }
    }
}

class PrebuiltPublishDeployer {
    private final Project target
    private final PrebuiltPublishExtensions publishExt

    PrebuiltPublishDeployer(Project target, PrebuiltPublishExtensions publishExt) {
        this.target = target
        this.publishExt = publishExt
        publishExt.ensureConfigsAvailable()
    }

    void deploy() {
        if (whetherAlreadyExistOnMaven()) {
            return
        }
        applyPublications()
    }

    /**
     * 仅在不存在于maven库时才启用发布至maven库，避免重复触发
     */
    private boolean whetherAlreadyExistOnMaven() {
        try {
            final conf = target.configurations.maybeCreate("tryResolve")
            conf.canBeResolved = true
            target.dependencies {
                "tryResolve"("${publishExt.groupId}:${publishExt.artifactId}:${publishExt.version}")
            }
            conf.resolve()
        } catch (Throwable e) {
            if (e.message.contains("Could not resolve")) {
                return false
            }
            throw e
        }
        return true
    }

    private void applyPublications() {
        target.publishing {
            publications {
                aar(MavenPublication) {
                    artifact publishExt.prebuiltArtifact
                    groupId = publishExt.groupId
                    artifactId = publishExt.artifactId
                    version = publishExt.version
                }
            }
            repositories {
                maven {
                    url publishExt.mavenExt.url
                    allowInsecureProtocol = publishExt.mavenExt.allowInsecureProtocol
                    if (publishExt.mavenExt.hasCredentials()) {
                        credentials {
                            username publishExt.mavenExt.username
                            password publishExt.mavenExt.password
                        }
                    }
                }
            }
        }
    }
}

class PrebuiltPublishPlugin implements Plugin<Project> {
    static final EXTENSION_NAME = "prebuiltPublish"

    @Override
    void apply(Project target) {
        final publishExt = target.extensions.create(EXTENSION_NAME, PrebuiltPublishExtensions)
        target.afterEvaluate {
            new PrebuiltPublishDeployer(target, publishExt).deploy()
        }
    }
}

project.plugins.apply(PrebuiltPublishPlugin)
