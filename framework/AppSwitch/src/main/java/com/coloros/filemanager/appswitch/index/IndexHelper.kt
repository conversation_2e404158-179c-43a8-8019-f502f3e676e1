/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : IndexHelper.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.index

import android.util.Log
import com.coloros.filemanager.appswitch.util.StaticUtil
import com.filemanager.common.bean.IndexOperationResult
import com.filemanager.common.bean.SearchDataBean
import com.filemanager.common.bean.SearchIndexBean
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import com.oplus.filemanager.provider.ThirdAppFileDBHelper
import com.oplus.filemanager.room.model.ThirdAppFileDataEntity
import java.util.UUID

class IndexHelper {

    companion object {
        const val TAG = "IndexHelper"
    }

    fun trigIndexInsert(entity: ThirdAppFileDataEntity): Boolean {
        val searchIndexBean = convertThirdAppFileDataEntityToSearchIndexBean(entity)
        val dmpSearchApi = Injector.injectFactory<IDmpSearchApi>()
        if (dmpSearchApi == null) {
            Log.i(TAG, "trigIndexInsert null, return false")
            return false
        }
        val documentResult = dmpSearchApi.addDocument(searchIndexBean)
        if (documentResult.isSuccess()) {
            val resultIndexBean = documentResult.indexBean
            //更新数据库中的数据
            val dbResult = ThirdAppFileDBHelper.updateIndentificationAndCheckSum(
                entity,
                resultIndexBean.identification,
                resultIndexBean.checkSum
            )
            val dbUpdateSuc = dbResult > 0
            if (!dbUpdateSuc) {
                StaticUtil.uploadDbOrDmpErrorStatic(entity, StaticUtil.DETECT_DB_INSERT_UPDATE_STEP2_ERROR)
            }
            Log.i(TAG, "trigIndexInsert update db result $dbUpdateSuc")
            return dbUpdateSuc
        } else {
            //这里后续要加埋点
            val errorMsg = IndexOperationResult.getErrorMessage(documentResult.errorCode)
            Log.e(TAG, "trigIndexInsert addDocument $searchIndexBean err, errorCode ${documentResult.errorCode}, errorMsg: $errorMsg")
            StaticUtil.uploadDbOrDmpErrorStatic(entity, StaticUtil.DETECT_DMP_INSERT_ERROR)
            return false
        }
    }

    fun trigIndexUpdate(entity: ThirdAppFileDataEntity): Boolean {
        val searchIndexBean = convertThirdAppFileDataEntityToSearchIndexBean(entity)
        val dmpSearchApi = Injector.injectFactory<IDmpSearchApi>()
        if (dmpSearchApi == null) {
            Log.i(TAG, "trigIndexInsert null, return false")
            return false
        }
        val documentResult = dmpSearchApi.updateDocument(searchIndexBean)
        if (documentResult.isSuccess()) {
            Log.i(TAG, "trigIndexUpdate update db result $documentResult")
            return true
        } else {
            val errorMsg = IndexOperationResult.getErrorMessage(documentResult.errorCode)
            Log.e(TAG, "trigIndexUpdate addDocument $searchIndexBean err, errorCode ${documentResult.errorCode}, errorMsg: $errorMsg")
            StaticUtil.uploadDbOrDmpErrorStatic(entity, StaticUtil.DETECT_DMP_UPDATE_ERROR)
            return false
        }
    }


    fun trigIndexDelete(identification: String): Boolean {
        val searchIndexBean = SearchIndexBean(identification)
        val dmpSearchApi = Injector.injectFactory<IDmpSearchApi>()
        if (dmpSearchApi == null) {
            Log.i(TAG, "trigIndexInsert null, return false")
            return false
        }
        val documentResult = dmpSearchApi.deleteDocument(searchIndexBean)
        if (documentResult.isSuccess()) {
            Log.i(TAG, "trigIndexDelete delete index result $documentResult")
            return true
        } else {
            //这里后续要加埋点
            val errorMsg = IndexOperationResult.getErrorMessage(documentResult.errorCode)
            Log.e(TAG, "trigIndexDelete deleteDocument $searchIndexBean err, errorCode ${documentResult.errorCode}, errorMsg: $errorMsg")
            return false
        }
    }

    private fun convertThirdAppFileDataEntityToSearchIndexBean(entity: ThirdAppFileDataEntity): SearchIndexBean {
        //这里判断跟新或新增逻辑，更新时identificationFromEntity不为空，新增时为空
        val identificationFromEntity = entity.mIndentification
        val identification = if (identificationFromEntity.isNullOrEmpty()) {
            UUID.randomUUID().toString()
        } else {
            identificationFromEntity
        }
        val checkSumFromEntity = entity.mIndexCheckSum
        val checkSum = if (identificationFromEntity.isNullOrEmpty()) SearchIndexBean.DEFAULT_CHECK_SUM else checkSumFromEntity
        val searchDataBean = SearchDataBean().apply {
            fileName = entity.mFileName
            sourcePackage = entity.mDectPackage
            sourceName = entity.mSourceName
            fileTime = entity.mFileSendTime ?: 0
            sourceType = entity.mSourceType
            fileSize = entity.mFileSize ?: 0L
            detectTime = entity.mDetectTime
        }
        val result = SearchIndexBean(identification, checkSum, searchDataBean)
        Log.i(TAG, "convertThirdAppFileDataEntityToSearchIndexBean input $entity, output $result")
        return result
    }
}