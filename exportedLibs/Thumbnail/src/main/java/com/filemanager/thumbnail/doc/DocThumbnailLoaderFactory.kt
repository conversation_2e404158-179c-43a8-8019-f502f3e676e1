/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - DocThumbnailLoaderFactory.kt
 * Description:
 *     The factory to get the instance for doc thumbnail loader
 *
 * Version: 1.0
 * Date: 2024-07-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Bixia<PERSON>.<EMAIL>    2024-07-29   1.0    Create this module
 *********************************************************************************/
package com.filemanager.thumbnail.doc

import android.content.Context
import android.graphics.Bitmap
import androidx.annotation.Keep
import androidx.annotation.VisibleForTesting
import com.bumptech.glide.load.model.ModelLoader
import com.bumptech.glide.load.model.ModelLoaderFactory
import com.bumptech.glide.load.model.MultiModelLoaderFactory
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.doc.wps.WpsDocThumbnailDelegate
import com.filemanager.thumbnail.doc.yozo.YoZoDocThumbnailLoader
import com.filemanager.thumbnail.utils.Log

/**
 * [DocThumbnailLoaderFactory] 本身继承实现 [ModelLoaderFactory] 用于Glide加载缩略图
 */
@Keep
class DocThumbnailLoaderFactory(context: Context) : ModelLoaderFactory<DocThumbnail, Bitmap> {

    private val appContext = context.applicationContext

    override fun build(multiFactory: MultiModelLoaderFactory): ModelLoader<DocThumbnail, Bitmap> {
        return DocThumbnailModelLoader(appContext)
    }

    override fun teardown() {
        //do nothing
    }

    /**
     * [DocThumbnailLoaderFactory] 以单例方式统一管理[IDocThumbnailLoader]的实例
     */
    companion object {
        private const val TAG = "DocThumbnailLoaderFactory"

        @Volatile
        private var availableInstance: IDocThumbnailLoader? = null
        @Volatile
        private var isUnSupport = false

        @Keep
        @Synchronized
        @JvmStatic
        fun getInstance(context: Context): IDocThumbnailLoader? {
            availableInstance?.let { return it }
            if (isUnSupport) {
                return null
            }
            val appContext = context.applicationContext
            val newInstance = createInstance(appContext)
            availableInstance = newInstance
            if (newInstance == null) {
                isUnSupport = true
            }
            return newInstance
        }

        @VisibleForTesting
        @JvmStatic
        internal fun createInstance(context: Context): IDocThumbnailLoader? {
            val pickSolution = listOf(
                WpsDocThumbSolution(context),
                YoZoThumbSolution(context)
            ).sortedByDescending {
                it.priority
            }.firstOrNull {
                it.isSupported()
            }
            val loaderImpl = pickSolution?.getLoader()
            if (loaderImpl == null) {
                Log.d(TAG, "createInstance: no available impl")
            }
            return loaderImpl
        }

        @Keep
        @Synchronized
        @JvmStatic
        fun release() {
            availableInstance?.release()
            availableInstance = null
            isUnSupport = false
        }
    }

    private sealed interface IDocThumbnailSolution {
        /**
         * 缩略图方案的使用优先级
         */
        val priority: Int

        /**
         * 是否支持当前方案
         */
        fun isSupported(): Boolean

        /**
         * 获取方案对应的缩略图加载实现
         */
        fun getLoader(): IDocThumbnailLoader?
    }

    private class WpsDocThumbSolution(
        private val context: Context,
        override val priority: Int = ThumbnailManager.WpsDocConfigs.usagePriority
    ) : IDocThumbnailSolution {
        private val loaderImpl by lazy { WpsDocThumbnailDelegate.createDocThumbnailLoader(context) }
        private val supportedStatus by lazy {
            ThumbnailManager.WpsDocConfigs.enableThumbnail && loaderImpl.isSupported()
        }

        override fun isSupported(): Boolean = supportedStatus

        override fun getLoader(): IDocThumbnailLoader? {
            if (supportedStatus) {
                Log.d(TAG, "createInstance: support WPS")
                return loaderImpl
            }
            return null
        }
    }

    private class YoZoThumbSolution(
        private val context: Context,
        override val priority: Int = ThumbnailManager.YoZoDocConfigs.usagePriority
    ) : IDocThumbnailSolution {
        private val loaderImpl by lazy { YoZoDocThumbnailLoader(context) }
        private val supportedStatus by lazy {
            ThumbnailManager.YoZoDocConfigs.enableThumbnail && loaderImpl.isSupported()
        }

        override fun isSupported(): Boolean = supportedStatus

        override fun getLoader(): IDocThumbnailLoader? {
            if (supportedStatus) {
                Log.d(TAG, "createInstance: support YoZo")
                return loaderImpl
            }
            return null
        }
    }
}