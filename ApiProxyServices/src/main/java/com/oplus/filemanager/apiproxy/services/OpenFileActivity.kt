/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OpenFileActivity
 ** Description : 文管对外提供打开文件的Activity
 ** Version     : 1.0
 ** Date        : 2024/06/21 11:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/06/21       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PathUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.fileoperate.open.FileActionOpen
import com.filemanager.fileoperate.open.FileOpenObserver
import com.oplus.filemanager.apiproxy.services.cache.SearchFileSizeCache
import com.oplus.filemanager.apiproxy.services.utils.DeeplinkUtils
import com.oplus.filemanager.interfaze.categoryremotedevice.ICategoryRemoteDeviceApi
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice

class OpenFileActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "OpenFileActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "onCreate")
        setContentView(R.layout.activity_open_file)
        openFile()
    }

    private fun openFile() {
        val filePath = DeeplinkUtils.getValueFromDeepLink(intent, KtConstants.FILE_PATH)
        if (filePath.isNullOrEmpty()) {
            Log.e(TAG, "openFile -> filePath is null")
            CustomToast.showLong(com.filemanager.common.R.string.toast_file_not_exist)
            finish()
            return
        }
        if (PathUtils.checkIsRemoteMacPath(filePath)) {
            openRemoteFile(filePath)
            return
        }
        val data = PathFileWrapper(filePath)
        FileActionOpen.Companion.Builder(this, data)
            .build()
            .execute(object : FileOpenObserver(this@OpenFileActivity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    Log.d(TAG, "openFile -> onActionDone result $result")
                    finish()
                }

                override fun onActionCancelled() {
                    super.onActionCancelled()
                    Log.d(TAG, "openFile -> onActionCancelled")
                    finish()
                }
            })
    }

    private fun openRemoteFile(filePath: String) {
        // 判断size是否正确
        val size = SearchFileSizeCache.getFileSize(filePath)
        Log.d(TAG, "openRemoteFile path:$filePath size:$size")
        if (size < 0L) {
            Log.e(TAG, "openRemoteFile file size is error")
            CustomToast.showLong(com.filemanager.common.R.string.toast_file_not_exist)
            finish()
            return
        }
        // 判断远程mac是否连接
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        val deviceId = remoteDeviceApi?.getCurrentLinkedRemoteDiceInfo()?.deviceId ?: ""
        if (deviceId.isEmpty()) {
            Log.e(TAG, "openRemoteFile device disconnect")
            CustomToast.showLong(com.filemanager.common.R.string.toast_file_not_exist)
            finish()
            return
        }
        // 跳转
        val sdkPath = PathUtils.convertUIPathToSdkPath(filePath)
        val categoryRemoteDeviceApi = Injector.injectFactory<ICategoryRemoteDeviceApi>()
        categoryRemoteDeviceApi?.jumpDownloadActivity(this, deviceId, arrayListOf(Pair(sdkPath, size)), MessageConstant.MSG_OPEN_REMOTE_FILE)
        StatisticsUtils.statisticsEntryLaunch(this, Constants.PKG_SPEECH_ASSIST, "", Constants.PAGE_REMOTE_FILE_DOWNLOAD)
        finish()
    }
}