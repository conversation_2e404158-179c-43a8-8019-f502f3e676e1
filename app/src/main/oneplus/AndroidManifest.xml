<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="HardcodedDebugMode">

    <permission android:name="com.oplus.filemanager.permission.DFM_PROVIDER"
        android:protectionLevel="signatureOrSystem" />

    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        tools:node="remove" />
    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE"
        tools:node="remove"/>
    <uses-permission android:name="com.coloros.encryption.READ_PROVIDER"
        tools:node="remove"/>
    <uses-permission android:name="com.coloros.encryption.WRITE_PROVIDER"
        tools:node="remove"/>
    <uses-permission android:name="oppo.permission.gallery.ACCESS_PROVIDER"
        tools:node="remove"/>
    <uses-permission android:name="com.oppo.permission.safe.SAU"
        tools:node="remove"/>
    <uses-permission android:name="com.oppo.permission.safe.SECURITY"
        tools:node="remove"/>
    <uses-permission android:name="com.oppo.permission.safe.PRIVATE"
        tools:node="remove"/>
    <uses-permission android:name="com.oppo.permission.safe.CONNECTIVITY"
        tools:node="remove"/>
    <uses-permission android:name="com.oplus.permission.safe.PRIVATE"/>
    <!--add for shortcut-->
    <uses-permission android:name="com.oplus.permission.safe.SAFE_MANAGER" />
    <application
        android:name="com.oplus.filemanager.FileManagerApplication"
        android:allowBackup="false"
        android:enableOnBackInvokedCallback="true"
        android:icon="@drawable/ic_launcher_filemanager"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestRawExternalStorageAccess="true"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <meta-data
            android:name="versionCommit"
            android:value="${versionCommit}" />
        <meta-data
            android:name="versionDate"
            android:value="${versionDate}" />
        <!--add for api-adapter-->
        <meta-data
            tools:replace="android:value"
            android:name="AppPlatformKey"
            android:value="ATBGAiEAnAFOLvZ1i0H5Z0IwDytxhx3WnMhSvGQMZVucCdlz4QUCIQDPDaEuWZfYBsdYh4oJAt3J4sxem5Bkq7ZJ0fDmyAPzPWGOMb1lcG9uYSxnZXQJAAAA" />

        <activity
            android:name="com.oplus.filemanager.category.album.ui.AlbumActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:launchMode="singleTop"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name="com.oplus.filemanager.category.albumset.ui.AlbumSetActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            tools:replace="android:permission"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oppo.intent.action.FILE_GALLERT_VIEW"
                    tools:node="remove"/>
                <category android:name="android.intent.category.DEFAULT" />

            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.FILE_GALLERT_VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.filemanager.category.apk.ui.ApkActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            tools:replace="android:exported,android:permission">
            <intent-filter>
                <action android:name="oppo.intent.action.FILE_APK_VIEW"
                    tools:node="remove"/>
                <category android:name="android.intent.category.DEFAULT" />

            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.FILE_APK_VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>
        <activity
            android:name="com.oplus.filemanager.category.audiovideo.ui.CategoryAudioActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            tools:replace="android:permission"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oppo.intent.action.FILE_MEDIA_VIEW"
                    tools:node="remove"/>

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.FILE_MEDIA_VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>
        <activity
            android:name="com.filemanager.categorycompress.ui.CategoryCompressActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            tools:replace="android:exported,android:permission">
            <intent-filter>
                <action android:name="oppo.intent.action.FILE_COMPRESS_VIEW"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.filemanager.category.document.ui.DocumentActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:label="@string/string_documents"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            tools:replace="android:exported,android:permission">
            <intent-filter>
                <action android:name="oppo.intent.action.FILE_DOCUMENT_VIEW"
                    tools:node="remove"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.FILE_DOCUMENT_VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>
        <activity
            android:name="com.oplus.filemanager.category.globalsearch.ui.GlobalSearchActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            tools:replace="android:exported,android:permission">
            <intent-filter>
                <action android:name="filemanager.intent.action.GLOBAL_SEARCH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- filemanager://deeplink.search?CATEGORY_TYPE=3&CATEGORY_KEY_WORD=xx-->
                <data
                    android:scheme="filemanager"
                    android:host="deeplink.search"
                    />
            </intent-filter>
        </activity>
        <activity
            android:name="com.filemanager.compresspreview.ui.CompressPreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oppo.intent.action.ACTION_COMPRESS_PREVIEW" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="content" />
                <data android:mimeType="application/x-7z-compressed" />
                <data android:mimeType="application/x-rar-compressed" />
                <data android:mimeType="application/rar" />
                <data android:mimeType="application/vnd.rar" />
                <data android:mimeType="application/zip" />
                <data android:mimeType="application/java-archive" />
                <data android:mimeType="text/uri-list" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.oplus.filebrowser.FileBrowserActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:launchMode="singleTop"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oppo.filemanager.intent.action.BROWSER_FILE"
                    tools:node="remove"/>
                <action android:name="oppo.intent.action.ACTION_FILEMANAGER_SAVE_PHONE"
                    tools:node="remove"/>

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.BROWSER_FILE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.oplus.filemanager.category.remotedevice.CategoryRemoteFileActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:launchMode="singleTop"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
        </activity>

        <activity
            android:name="com.oplus.filebrowser.otg.OtgFileBrowserActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:launchMode="singleTop"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            tools:replace="android:exported,android:permission">

        </activity>

        <activity
            android:name="com.oplus.filemanager.filechoose.ui.folderpicker.FolderPickerActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            tools:replace="android:exported,android:permission,android:theme">
            <intent-filter>
                <action android:name="oppo.intent.action.ACTION_FILEMANAGER_SAVE"
                    tools:node="remove"/>
                <action android:name="oplus.intent.action.ACTION_FILEMANAGER_SAVE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.filemanager.filechoose.ui.addFile.AddFileActivity"
            android:configChanges="orientation|keyboardHidden|mcc|mnc|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:uiOptions="splitActionBarWhenNarrow">
            <intent-filter>
                <action android:name="oplus.intent.action.ACTION_FILEMANAGER_PICKFILES" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.OPENABLE" />
                <data android:mimeType="*/*"></data>
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.filemanager.filechoose.ui.singlepicker.SinglePickerActivity"
            android:configChanges="orientation|keyboardHidden|mcc|mnc|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:uiOptions="splitActionBarWhenNarrow">
            <intent-filter>
                <action android:name="oppo.intent.action.ACTION_FILEMANAGER_PICK"
                    tools:node="remove"/>
                <action android:name="com.oppo.select.vcf"
                    tools:node="remove"/>
                <action android:name="com.oppo.select.cvs"
                    tools:node="remove"/>
                <action android:name="oppo.intent.action.ACTION_SELECT_MEMO"
                    tools:node="remove"/>
                <action android:name="oppo.intent.action.ACTION_FILEMANAGER_BROWSE"
                    tools:node="remove"/>
                <action android:name="oppo.intent.action.ACTION_FILEMANAGER_SELECT"
                    tools:node="remove"/>

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.GET_CONTENT"
                    tools:node="remove"/>

                <category android:name="android.intent.category.OPENABLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="oppo.file.browser/browser.all"
                    tools:node="remove"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.PICK"
                    tools:node="remove"/>

                <category android:name="android.intent.category.DEFAULT" />

                <data android:mimeType="oppo.file.browser/browser.all"
                    tools:node="remove"/>
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.ACTION_FILEMANAGER_SELECT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>
        <activity
            android:name="com.oplus.filemanager.filechoose.ui.filepicker.FilePickerActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:label="@string/activity_mark"
            android:taskAffinity="com.oneplus.filemanager.encrytion"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            tools:replace="android:taskAffinity,android:exported,android:permission">
            <intent-filter>
                <action android:name="oppo.intent.action.ACTION_FILE_SELECTION"
                    tools:node="remove"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.filemanager.keymove.ui.AKeyToMoveActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:label="@string/title_onetap_to_move"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            tools:replace="android:exported,android:permission">
            <intent-filter>
                <action android:name="com.oppo.filemanager.akeytomove.AKeyToMoveActivity"
                    tools:node="remove"/>

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.AKEY_TO_MOVE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.filemanager.recyclebin.ui.RecycleBinActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:label="@string/string_documents"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            tools:remove="android:permission"
            tools:replace="android:exported">

        </activity>
        <activity
            android:name="com.filemanager.setting.ui.SettingActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            tools:remove="android:permission"
            tools:replace="android:exported">

        </activity>
        <activity
            android:name="com.filemanager.setting.ui.about.SettingAboutActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            tools:remove="android:permission"
            tools:replace="android:exported">

        </activity>
        <activity
            android:name="com.filemanager.setting.ui.function.SettingFunctionActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment"
            android:uiOptions="splitActionBarWhenNarrow"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.FUNC_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data
                android:name="support_jump"
                android:value="true" />
        </activity>
        <activity
            android:name="com.filemanager.setting.ui.opensourcelicense.OpenSourceActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="false"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            tools:remove="android:permission"
            tools:replace="android:exported">

        </activity>
        <activity
            android:name="com.filemanager.superapp.ui.superapp.SuperAppActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            android:permission="com.oplus.permission.safe.SECURITY"
            android:launchMode="singleTop"
            >
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.SOURCES_DOC" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="text/*"/>
            </intent-filter>
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.SUPER_APP" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.oplus.filemanager.main.ui.QuickDocumentActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize|keyboard|navigation"
            android:exported="true"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:icon="@drawable/shortcut_document_blue"
            android:label="@string/string_documents"
            android:launchMode="standard"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- filemanager://deeplink.doc-->
                <data
                    android:scheme="filemanager"
                    android:host="deeplink.doc"
                    />
            </intent-filter>
        </activity>

        <activity
            android:name="com.oplus.filemanager.main.ui.ShortCutActivity"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            android:autoRemoveFromRecents="true"
            android:excludeFromRecents="true"
            >
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.SHORTCUT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.oplus.filemanager.main.ui.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize|keyboard|navigation"
            android:exported="true"
            android:screenOrientation="behind"
            android:launchMode="singleTop"
            android:theme="@style/AppNoTitleTheme.Splash"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="ACTION_FILEMANAGER_BROWSE_CHECK_PASSWORD" />
                <action android:name="oppo.intent.action.OPEN_FILEMANAGER"
                    tools:node="remove"/>
                <action android:name="oppo.intent.action.ACTION_FILEMANAGER_SAVE_MAIN"
                    tools:node="remove"/>

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.OPEN_FILEMANAGER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.QUICK_DOCUMENT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.superApp" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <meta-data
                android:name="oldComponentName"
                android:resource="@array/downLoadListOldComponentName" />
            <meta-data
                android:name="oldComponentNameVersion"
                android:value="1" />
            <!--add for shortcuts in desktop-->
            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/desktop_shortcuts" />
        </activity>

        <activity android:name="com.oplus.filemanager.main.ui.TransitionActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:exported="true"
            android:screenOrientation="behind"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:theme="@style/Theme.Transparent">

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.EXTERNAL_APP" />
                <action android:name="oplus.intent.action.filemanager.view_recycle_bin" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>


        <!-- Add for file share in Android N, 2017-04-04 -->
        <provider
            android:name="com.oplus.filemanager.provider.FilePathProvider"
            android:authorities="com.oneplus.filemanager"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path" />
        </provider>

        <activity
            android:name="com.oplus.filemanager.main.ui.UsbAttachedActivity"
            android:exported="false"
            android:screenOrientation="behind"
            android:theme="@android:style/Theme.NoDisplay"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan">

            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
            </intent-filter>

            <meta-data
                android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
                android:resource="@xml/device_filter" />
        </activity>

        <activity
            android:name="com.oplus.filemanager.filelabel.list.LabelFileListActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
            android:screenOrientation="behind"
            android:permission="com.oplus.permission.safe.ASSISTANT"
            android:theme="@style/AppNoTitleTheme.ActionMode"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            android:exported="true"
            >
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.LABEL_FILE_LIST" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <provider
            android:name="com.filemanager.recyclebin.db.RecycleProvider"
            android:authorities="com.oneplus.filemanager.recycleprovider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:node="replace"/>
        <provider
            android:name="com.oplus.filemanager.provider.FileManagerProvider"
            android:authorities="com.oneplus.filemanager.filemanagerprovider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:node="replace"/>
        <provider
            android:name="com.oplus.filemanager.provider.FileDataProvider"
            android:authorities="com.oneplus.filemanager.filedata"
            android:exported="false"
            android:multiprocess="false"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            tools:node="replace"
            tools:replace="android:exported,android:permission"></provider>
        <provider
            android:name="com.oplus.filemanager.category.globalsearch.provider.DFMProvider"
            android:authorities="com.oneplus.filemanager.dmpprovider"
            android:exported="true"
            android:multiprocess="false"
            android:permission="com.oplus.filemanager.permission.DFM_PROVIDER"
            tools:node="replace"
            />
        <provider
            android:name="com.oplus.filemanager.provider.MyFileProvider"
            android:authorities="com.oneplus.filemanager.fileprovider"
            android:exported="false"
            tools:node="replace"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path" />
        </provider>
        <provider
            android:authorities="com.oneplus.filemanager.provider.shortcutprovider"
            android:exported="true"
            android:permission="com.oplus.permission.safe.SECURITY"
            android:name="com.oplus.filemanager.provider.ShortCutProvider"
            tools:replace="android:authorities"/>
        <meta-data
            android:name="AppCode"
            android:value="20016" />
        <meta-data
            android:name="OppoPermissionKey"
            tools:node="remove" />
        <meta-data
            android:name="OplusPermissionKey"
            android:value="XIechWLeCAZ+ITAHwcFAjXqI6IpA+U4jRpdP+cUxdVznQGRFeqgOwiHJEt3o5PUtZQziDp8U7YhU8LmyXLItDqCzMa70o0uKvgmuSV+NBG4rnVeVvmlOFwjii8vd6mEjRzJodQeRCqqrHRYlJ11nWT95k1bL8PWy1sanY+mJARqYnLvPu1hHNzaEhUf5BxatWsU8PD3ILJx/N52Qh+xIb9vJDdU8pVsIJrEb5ywbmao8gOSznKTdfBpCNDy0y6qCRtdZEjz/m2owLABnIesw7odL0keeEsrg00Vj/pbyqy5hatRpIsmZykn0pRUgs9MVmJHlm3UGf0PrThFHQ+TGcA== 1 com.oneplus.filemanager C6E8150AA5BBAF523CA1E2D9E356008E1728A12FE20C3C7875A446AFB7C579F9 906"
            tools:replace="android:value" />
        <uses-library
            android:name="com.coloros.statistics"
            tools:node="remove" />
        <meta-data
            android:name="FixUidSdkVers"
            android:value="[30][31]" />
        <!--add for feature 3787712, true is support this function and false is not start-->
        <meta-data
            android:name="FolderPickSaveFile"
            android:value="true"/>
        <!--add for feature 3787712 end-->

        <meta-data
            android:name="com.oplus.pickle.to_tripartite"
            android:value="true" />
    </application>
</manifest>
