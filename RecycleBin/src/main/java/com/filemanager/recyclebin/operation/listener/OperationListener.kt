/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - OperationListener.java
 ** Description: Operation Listener
 ** Version: 1.0
 ** Date : 2020/03/10
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/03/10    1.0     create
 ****************************************************************/

package com.filemanager.recyclebin.operation.listener

import android.os.Message
import androidx.activity.ComponentActivity
import com.filemanager.common.utils.StaticHandler
import com.filemanager.recyclebin.operation.BaseOperation
import java.lang.ref.WeakReference

abstract class OperationListener(activity: ComponentActivity) : BaseOperation.ProgressListener {
    companion object {
        const val MESSAGE_DELAY_TIME = 100L
        const val MSG_SHOW_PROGRESS_DIALOG = 0
        const val MSG_UPDATE_PROGRESS_DIALOG = 1
    }

    protected val mWeakActivity = WeakReference(activity)
    val mMainHandler = MainHandler(activity, listener = this)

    open fun innerHandleMessage(message: Message, activity: ComponentActivity) {
    }

    open fun recycle() {
        mWeakActivity.clear()
        mMainHandler.recycle()
    }
}

class MainHandler(activity: ComponentActivity, listener: OperationListener) : StaticHandler<ComponentActivity>(activity) {
    private val mListener: WeakReference<OperationListener> = WeakReference(listener)
    override fun handleMessage(msg: Message, activity: ComponentActivity) {
        mListener.get()?.innerHandleMessage(msg, activity)
    }
}