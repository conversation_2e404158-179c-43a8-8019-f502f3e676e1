package com.oplus.filemanager.preview.archive

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.preview.core.PreviewOperationsController
import com.oplus.filemanager.preview.utils.FilePreviewTypeCheckHelper
import com.oplus.filemanager.preview.widget.PreviewFilePathItem
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * ArchivePreviewFragment的单元测试类
 * 用于测试ArchivePreviewFragment的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class ArchivePreviewFragmentTest {
    
    // 定义测试所需的mock对象和变量
    private lateinit var fragment: ArchivePreviewFragment
    private val mockContext = mockk<Context>()
    private val mockView = mockk<View>()
    private val mockViewModel = mockk<ArchivePreviewViewModel>(relaxed = true)
    private val mockFileBean = mockk<BaseFileBean>()
    private val mockPreviewImpl = mockk<IArchiveFilePreview>(relaxed = true)
    private val mockContainerManager = mockk<ArchivePreviewContainerManager>(relaxed = true)
    private val mockActivity = mockk<BaseVMActivity>(relaxed = true)
    private val mockPreviewFilePathItem = mockk<PreviewFilePathItem>(relaxed = true)
    private val mockOperationsController = mockk<ArchiveOperationsController>(relaxed = true)
    
    /**
     * 在每个测试方法执行前的初始化操作
     */
    @Before
    fun setUp() {
        // 创建待测试的Fragment实例
        fragment = ArchivePreviewFragment()
        
        // 模拟IArchiveFilePreview的伴生对象
        mockkObject(IArchiveFilePreview.Companion)
        // 设置IArchiveFilePreview.obtain()方法的模拟返回值
        every { IArchiveFilePreview.obtain(any(), any()) } returns mockPreviewImpl
        
        // 模拟FilePreviewTypeCheckHelper的构造方法
        mockkConstructor(FilePreviewTypeCheckHelper::class)
        // 设置isArchiveFileType方法的默认返回值
        every { anyConstructed<FilePreviewTypeCheckHelper>().isArchiveFileType(any(), any()) } returns true
        
        // 模拟Context对象，防止获取applicationContext时出错
        every { mockContext.applicationContext } returns mockContext
    }
    
    /**
     * 在每个测试方法执行后的清理操作
     */
    @After
    fun tearDown() {
        // 解除所有mock对象
        unmockkAll()
    }
    
    /**
     * 测试isPreviewFileApproved方法
     * 当FilePreviewTypeCheckHelper返回true时，应该返回true
     */
    @Test
    fun `isPreviewFileApproved should return true when FilePreviewTypeCheckHelper returns true`() {
        // 设置isArchiveFileType方法的模拟返回值
        every { anyConstructed<FilePreviewTypeCheckHelper>().isArchiveFileType(any(), any()) } returns true
        
        // 使用反射访问protected方法
        val method = ArchivePreviewFragment::class.java.getDeclaredMethod("isPreviewFileApproved", Context::class.java, BaseFileBean::class.java)
        method.isAccessible = true
        val result = method.invoke(fragment, mockContext, mockFileBean) as Boolean
        
        // 验证结果应为true
        assert(result)
    }
    
    /**
     * 测试isPreviewFileApproved方法
     * 当FilePreviewTypeCheckHelper返回false时，应该返回false
     */
    @Test
    fun `isPreviewFileApproved should return false when FilePreviewTypeCheckHelper returns false`() {
        // 设置isArchiveFileType方法的模拟返回值
        every { anyConstructed<FilePreviewTypeCheckHelper>().isArchiveFileType(any(), any()) } returns false
        
        // 使用反射访问protected方法
        val method = ArchivePreviewFragment::class.java.getDeclaredMethod("isPreviewFileApproved", Context::class.java, BaseFileBean::class.java)
        method.isAccessible = true
        val result = method.invoke(fragment, mockContext, mockFileBean) as Boolean
        
        // 验证结果应为false
        assert(!result)
    }
    
    /**
     * 测试putPreviewFileToViewModel方法
     * 当activity不是BaseVMActivity时，不应该调用viewModel的putPreviewFile方法
     */
    @Test
    fun `putPreviewFileToViewModel should not call viewModel putPreviewFile when activity is not BaseVMActivity`() {
        // 模拟一个不是BaseVMActivity的activity
        val mockFragmentActivity = mockk<Fragment>(relaxed = true)
        every { mockFragmentActivity.activity } returns mockk()
        // 使用反射设置fragmentInstance字段
        val field = ArchivePreviewFragment::class.java.getDeclaredField("fragmentInstance")
        field.isAccessible = true
        field.set(fragment, mockFragmentActivity)
        
        // 使用反射访问protected方法
        val method = ArchivePreviewFragment::class.java.getDeclaredMethod("putPreviewFileToViewModel", ArchivePreviewViewModel::class.java, BaseFileBean::class.java)
        method.isAccessible = true
        method.invoke(fragment, mockViewModel, mockFileBean)
        
        // 验证viewModel.putPreviewFile没有被调用
        verify(exactly = 0) { mockViewModel.putPreviewFile(any(), any()) }
    }
    
    /**
     * 测试onCreateFilePathItem方法
     * 应该找到对应的view并设置label为gone
     */
    @Test
    fun `onCreateFilePathItem should find view and set label gone`() {
        // 设置findViewById的模拟返回值
        every { mockView.findViewById<PreviewFilePathItem>(R.id.preview_remote_location_info) } returns mockPreviewFilePathItem
        
        // 使用反射访问protected方法
        val method = ArchivePreviewFragment::class.java.getDeclaredMethod("onCreateFilePathItem", View::class.java)
        method.isAccessible = true
        val result = method.invoke(fragment, mockView)
        
        // 验证返回的是正确的PreviewFilePathItem实例
        assert(result === mockPreviewFilePathItem)
        // 验证setLabelGone方法被调用了一次
        verify(exactly = 1) { mockPreviewFilePathItem.setLabelGone() }
    }
    
    /**
     * 测试onDestroyView方法
     * 应该释放filePreview并将其设置为null
     */
    @Test
    fun `onDestroyView should release filePreview and set it to null`() {
        // 使用反射设置filePreview字段
        val field = ArchivePreviewFragment::class.java.getDeclaredField("filePreview")
        field.isAccessible = true
        field.set(fragment, mockPreviewImpl)
        
        // 调用待测试方法
        fragment.onDestroyView()
        
        // 验证release方法被调用了一次
        verify(exactly = 1) { mockPreviewImpl.release() }
        // 验证filePreview字段被设置为null
        assert(field.get(fragment) == null)
    }
    
    /**
     * 测试onUpdateUIWhenConfigChange方法
     * 当view为null时应该直接返回
     */
    @Test
    fun `onUpdateUIWhenConfigChange should return early when view is null`() {
        // 创建一个真实的Fragment实例用于测试
        val testFragment = ArchivePreviewFragment()
        
        val configList = mutableListOf<IUIConfig>()
        
        // 使用反射访问protected方法
        val method = ArchivePreviewFragment::class.java.getDeclaredMethod("onUpdateUIWhenConfigChange", MutableCollection::class.java)
        method.isAccessible = true
        method.invoke(testFragment, configList)
        
        // 不应该抛出任何异常
    }
    
    /**
     * 测试onUpdateUIWhenConfigChange方法
     * 当布局参数不是ConstraintLayout参数时应该直接返回
     */
    @Test
    fun `onUpdateUIWhenConfigChange should return early when layout params are not ConstraintLayout Params`() {
        // 模拟ViewGroup和LayoutParams
        val mockArchiveCard = mockk<ViewGroup>()
        val mockLayoutParams = mockk<ViewGroup.LayoutParams>() // 不是ConstraintLayout.LayoutParams
        
        // 创建一个真实的Fragment实例用于测试
        val testFragment = ArchivePreviewFragment()
        
        // 使用反射设置fragment的view字段
        val viewField = Fragment::class.java.getDeclaredField("mView")
        viewField.isAccessible = true
        viewField.set(testFragment, mockArchiveCard)
        
        // 设置findViewById和layoutParams的模拟返回值
        every { mockArchiveCard.layoutParams } returns mockLayoutParams
        every { mockArchiveCard.findViewById<ViewGroup>(R.id.preview_archive_card) } returns mockArchiveCard
        
        val configList = mutableListOf<IUIConfig>()
        
        // 使用反射访问protected方法
        val method = ArchivePreviewFragment::class.java.getDeclaredMethod("onUpdateUIWhenConfigChange", MutableCollection::class.java)
        method.isAccessible = true
        method.invoke(testFragment, configList)
        
        // 不应该抛出任何异常
    }
    
    /**
     * 测试pressBack方法
     * 当previewModel为null时应该返回false
     */
    @Test
    fun `pressBack should return false when previewModel is null`() {
        // 调用待测试方法
        val result = fragment.pressBack()
        
        // 验证返回false
        assert(!result)
    }
}

/**
 * 模拟ArchivePreviewViewModel类
 * 因为它在测试中被引用但不在依赖项中提供
 */
internal class ArchivePreviewViewModel : FilePreviewViewModel() {
    var compressModel: ArchivePreviewViewModel? = null
    fun putPreviewFile(activity: BaseVMActivity, fileBean: BaseFileBean) {}
    fun pressBack(): Boolean = true
}

/**
 * 模拟IArchiveFilePreview接口
 * 因为它在测试中被引用但不在依赖项中提供
 */
internal interface IArchiveFilePreview {
    fun bindOperationsBar(operationsBar: View?)
    fun attachToContainer(containerManager: ArchivePreviewContainerManager)
    fun release()
    
    companion object {
        fun obtain(viewLifecycleOwner: androidx.lifecycle.LifecycleOwner, viewModel: ArchivePreviewViewModel): IArchiveFilePreview =
            mockk()
    }
}

/**
 * 模拟ArchivePreviewContainerManager类
 * 因为它在测试中被引用但不在依赖项中提供
 */
internal class ArchivePreviewContainerManager(
    fragment: ArchivePreviewFragment,
    viewModel: ArchivePreviewViewModel,
    view: View
)

/**
 * 模拟ArchiveOperationsController类
 * 因为它在测试中被引用但不在依赖项中提供
 */
internal class ArchiveOperationsController(
    fragment: Fragment,
    viewModel: FilePreviewViewModel,
    selectPathsCache: MutableMap<Int, List<String>?>
) : PreviewOperationsController(fragment, viewModel, selectPathsCache)