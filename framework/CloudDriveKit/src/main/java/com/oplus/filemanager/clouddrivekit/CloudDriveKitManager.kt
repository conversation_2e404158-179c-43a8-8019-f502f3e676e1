/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudDriveKitManager
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/3/11
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798        2024/1/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.clouddrivekit

import android.app.Application
import com.coui.appcompat.unitconversionutil.COUIUnitConversionUtils
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.utils.Log
import com.heytap.cloudkit.libcommon.config.CloudConfig
import com.heytap.cloudkit.libcommon.config.CloudEnv
import com.heytap.cloudkit.libcommon.config.CloudLogLevel
import com.heytap.cloudkit.libcommon.config.CloudRepeatFileConfig
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.heytap.cloudkit.libsync.ext.ICloudResponseCallback
import com.heytap.cloudkit.libsync.service.CloudSpaceInfo

object CloudDriveKitManager {
    private const val TAG = "CloudDriveKitManager"
    private const val APP_ID = "22029d4aafdb4e66"
    private const val APP_PKG_ID = "3714261a6b139b95"
    private const val APP_KEY = "8125955951b245a4b2bba684b57c27f4"
    private const val APP_CONFIG_KEY = "39938b65b63caa44b8028a4d33dfe2b0e0b6971441a60aaf2ff773e61a5859fa"
    private const val HOST = "heytapmobi.com"
    private const val PARALLEL_FILE_COUNT = 2
    private const val PARALLEL_SLICE_COUNT = 4
    private const val MAX_WAIT_FILE_COUNT = 100
    private const val MIN_AVAILABLE_LOCAL_SPACE = 2 * 1024 * 1024 * 1024L
    private const val NUM: Long = 1024L
    private const val DECIMAL_NUM: Float = 1024.0f
    private val SupportRegionList = listOf("cn")
    private var isInit: Boolean = false

    /**
     * 初始化云盘SDK
     */
    @JvmStatic
    fun initCloudkitSDK(application: Application) {
        val cloudConfig = CloudConfig.Builder(application)
            .setAppId(APP_ID)
            .setAppPkgId(APP_PKG_ID)
            .setAppKey(APP_KEY)
            .setAppSecretKey(APP_CONFIG_KEY)
            .setHost(HOST)
            .setEnv(CloudEnv.RELEASE)
            .setParallelFileCount(PARALLEL_FILE_COUNT)
            .setParallelSliceCount(PARALLEL_SLICE_COUNT)
            .setMaxWaitFileCount(MAX_WAIT_FILE_COUNT)
            .setMinAvailableLocalSpace(MIN_AVAILABLE_LOCAL_SPACE)
            .setRepeatFileInOneDayConfig(CloudRepeatFileConfig.getDefault())
            .setWriteLogFile(true)
            .setConsoleLogLevel(CloudLogLevel.LEVEL_DEBUG)
            .setCloudSupportRegionList(SupportRegionList)
            .build()
        CloudSyncManager.getInstance().init(cloudConfig, CloudAccountAgentImpl(appContext))
        isInit = true
    }

    /**
     * 判断云盘可以用空间是否已满
     */
    @JvmStatic
    fun isCloudSpaceFull(application: Application, callback: (Boolean?) -> Unit) {
        if (isInit.not()) {
            initCloudkitSDK(application)
        }
        CloudSyncManager.getInstance().getCloudSpaceInfo(object : ICloudResponseCallback<CloudSpaceInfo> {
            override fun onSuccess(result: CloudSpaceInfo) {
                Log.d(TAG, "getCloudSpaceInfo onSuccess $result")
                val couiUnitConversionUtils = COUIUnitConversionUtils(application)
                val usedUnitValue = couiUnitConversionUtils.getUnitValue(result.usedAmount + result.frozenAmount)
                val allUnitValue = couiUnitConversionUtils.getUnitValue(result.quota)
                Log.d(TAG, "getCloudSpaceInfo usedUnitValue:$usedUnitValue,allUnitValue:$allUnitValue")
                if (result.quota <= (result.usedAmount + result.frozenAmount)
                    || usedUnitValue.equals(allUnitValue)
                ) {
                    callback.invoke(true)
                } else {
                    callback.invoke(false)
                }
            }

            override fun onError(error: CloudKitError) {
                Log.d(TAG, "isCloudSpaceFull->onError error=$error")
                callback.invoke(null)
            }
        })
    }
}