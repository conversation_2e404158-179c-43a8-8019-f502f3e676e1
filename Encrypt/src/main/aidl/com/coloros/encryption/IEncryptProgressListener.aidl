/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - IEncryptProgressListener.java
 ** Description: interface for Encrypt Progress Listener
 ** Version: 1.0
 ** Date : 2019/05/07
 ** Author: <PERSON>af<PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  Ji<PERSON>ei.<PERSON>@Apps.FileEncryption      2019/05/07    1.0     create
 ****************************************************************/

package com.coloros.encryption;

interface IEncryptProgressListener {
    void onStarted();
    void onProgress(int progress);
    void onFinished(int result, int failedCount);
}
