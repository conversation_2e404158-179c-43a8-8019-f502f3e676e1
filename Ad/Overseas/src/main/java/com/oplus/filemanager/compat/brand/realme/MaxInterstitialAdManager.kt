/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MaxInterstitialAdManager
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/8/8 17:08
 ** Author      : ********
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********        2024/8/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.compat.brand.realme

import android.app.Activity
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxInterstitialAd
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.crashhandler.SdkCrashCatchUtils
import com.filemanager.common.utils.ConfigSharedPreferenceUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_CLICK
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_FILL
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_REQUEST
import com.filemanager.common.utils.StatisticsUtils.AD_EVENT_SHOW
import com.filemanager.common.utils.StatisticsUtils.AD_REQUEST_FAIL
import com.filemanager.common.utils.StatisticsUtils.EVENT_AD_LOAD_TAG
import com.filemanager.common.utils.StatisticsUtils.LABEL_INTERSTITIAL_ADS_CLICK
import com.filemanager.common.utils.StatisticsUtils.LABEL_INTERSTITIAL_ADS_DISPLAY
import com.filemanager.common.utils.StatisticsUtils.LABEL_INTERSTITIAL_ADS_FILL
import com.filemanager.common.utils.StatisticsUtils.REALME_FILE_EVENT
import com.filemanager.common.utils.StatisticsUtils.RECENT_INTERSTITIAL_ADS_CLICK
import com.filemanager.common.utils.StatisticsUtils.RECENT_INTERSTITIAL_ADS_DISPLAY
import com.filemanager.common.utils.StatisticsUtils.RECENT_INTERSTITIAL_ADS_FILL
import com.oplus.filemanager.utils.AdConstants
import com.oplus.filemanager.utils.AdConstants.KEY_INTERSTITIALAD_TIME
import com.oplus.filemanager.utils.AdHelper

class MaxInterstitialAdManager : InterstitialAdManager {
    private var interstitialAd: MaxInterstitialAd? = null
    private var activity: Activity? = null
    private var pos = 0

    private val maxAdListener = object : MaxAdListener {
        override fun onAdLoaded(maxAd: MaxAd) {
            if (activity != null) {
                interstitialAd?.showAd(activity)
                ConfigSharedPreferenceUtils.putLong(KEY_INTERSTITIALAD_TIME + pos, System.currentTimeMillis())
                val adShowTimes = ConfigSharedPreferenceUtils.getLong(AdConstants.KEY_INTERSTITIALAD_SHOW_TIMES + pos, 0)
                ConfigSharedPreferenceUtils.putLong(AdConstants.KEY_INTERSTITIALAD_SHOW_TIMES + pos, adShowTimes + 1)
                AdHelper.isInterstitialAdRequesting = false
            }
            Log.d(AdvertEntry.AD_TAG, "MaxInterstitial onAdLoaded maxAd:$maxAd")
        }

        override fun onAdDisplayed(maxAd: MaxAd) {
            val map = HashMap<String, String>()
            val key = if (pos == RealmeInterstitialAdMgr.INDEX_RECENT) {
                RECENT_INTERSTITIAL_ADS_DISPLAY
            } else {
                LABEL_INTERSTITIAL_ADS_DISPLAY
            }
            map[key] = AD_EVENT_SHOW
            StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
            Log.d(AdvertEntry.AD_TAG, "MaxInterstitial onAdDisplayed")
        }

        override fun onAdHidden(maxAd: MaxAd) {
            AdHelper.isInterstitialAdRequesting = false
            Log.d(AdvertEntry.AD_TAG, "MaxInterstitial onAdHidden")
        }

        override fun onAdClicked(maxAd: MaxAd) {
            val map = HashMap<String, String>()
            val key = if (pos == RealmeInterstitialAdMgr.INDEX_RECENT) {
                RECENT_INTERSTITIAL_ADS_CLICK
            } else {
                LABEL_INTERSTITIAL_ADS_CLICK
            }
            map[key] = AD_EVENT_CLICK
            StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
            Log.d(AdvertEntry.AD_TAG, "onAdClicked maxAd")
        }

        override fun onAdLoadFailed(unitId: String, error: MaxError) {
            AdHelper.isInterstitialAdRequesting = false

            Log.d(AdvertEntry.AD_TAG, "onAdLoadFailed:${error.code}")
            Log.d(AdvertEntry.AD_TAG, "onAdLoadFailed:${error.message}")

            val map = HashMap<String, String>()
            map[StatisticsUtils.INTERSTITIAL_ADS_REQ_FAIL] = AD_REQUEST_FAIL
            StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        }

        override fun onAdDisplayFailed(maxAd: MaxAd, error: MaxError) {
            AdHelper.isInterstitialAdRequesting = false
            Log.d(AdvertEntry.AD_TAG, "onAdDisplayFailed")
        }
    }

    override fun initInterstitialAd(posId: String) {
        if (!SdkCrashCatchUtils.isCanCallADApi()) {
            Log.i(AdvertEntry.AD_TAG, "initInterstitialAd, ad sdk crashed 3 times,disable request ad")
            return
        }
        interstitialAd = MaxInterstitialAd(posId, appContext)
        interstitialAd?.setListener(maxAdListener)
    }

    override fun requestInterstitialAd() {
        if (!SdkCrashCatchUtils.isCanCallADApi()) {
            Log.i(AdvertEntry.AD_TAG, "requestInterstitialAd, ad sdk crashed 3 times,disable request ad")
            return
        }
        interstitialAd?.loadAd()
        AdHelper.isInterstitialAdRequesting = true

        val map = HashMap<String, String>()
        map[StatisticsUtils.INTERSTITIAL_ADS_REQ] = AD_EVENT_REQUEST
        StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
    }

    override fun showInterstitialAd(activity: Activity, index: Int) {
        if (!SdkCrashCatchUtils.isCanCallADApi()) {
            Log.i(AdvertEntry.AD_TAG, "showInterstitialAd, ad sdk crashed 3 times,disable request ad")
            return
        }
        Log.d(AdvertEntry.AD_TAG, "show max ad interstitialAd:$interstitialAd")
        pos = index
        this.activity = activity

        Log.d(AdvertEntry.AD_TAG, "interstitialAd  isReady:" + interstitialAd?.isReady)
        if (interstitialAd?.isReady == true) {
            interstitialAd?.showAd(activity)
            this.activity = null
            ConfigSharedPreferenceUtils.putLong(KEY_INTERSTITIALAD_TIME + index, System.currentTimeMillis())
            val adShowTimes = ConfigSharedPreferenceUtils.getLong(AdConstants.KEY_INTERSTITIALAD_SHOW_TIMES + index, 0)
            ConfigSharedPreferenceUtils.putLong(AdConstants.KEY_INTERSTITIALAD_SHOW_TIMES + index, adShowTimes + 1)
            AdHelper.isInterstitialAdRequesting = false

            val map = HashMap<String, String>()
            val key = if (index == RealmeInterstitialAdMgr.INDEX_RECENT) {
                RECENT_INTERSTITIAL_ADS_FILL
            } else {
                LABEL_INTERSTITIAL_ADS_FILL
            }
            map[key] = AD_EVENT_FILL
            map[AD_EVENT_FILL] = AD_EVENT_FILL
            StatisticsUtils.onCommon(appContext, EVENT_AD_LOAD_TAG, REALME_FILE_EVENT, map)
        }
    }

    override fun destroy() {
        interstitialAd?.destroy()
        interstitialAd = null
        activity = null
    }
}