package com.coloros.filemanager.utils

import android.app.Activity
import android.content.Context
import android.content.pm.ActivityInfo.SCREEN_ORIENTATION_NOSENSOR
import android.content.pm.ActivityInfo.SCREEN_ORIENTATION_USER
import android.graphics.Point
import android.graphics.Rect
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.view.WindowMetrics
import androidx.core.graphics.Insets
import androidx.core.view.ViewCompat
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.view.FileManagerRecyclerView
import com.google.android.material.appbar.AppBarLayout
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class KtViewUtilsTest {
    @MockK
    lateinit var mActivity: Activity

    @MockK
    lateinit var mView: View

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        mActivity = mockk(relaxed = true)

        mView = mockk(relaxed = true)

        var context = mockk<Context>(relaxed = true) {
            every { applicationContext }.returns(this)
            every { resources }.returns(mockk(relaxed = true) {
                every { getDimensionPixelSize(any()) }.returns(110)
            })
        }

        MyApplication.init(context)
    }

    @Test
    fun `should verify when hidden anim top to bottom whit fade without`() {
        every { mView.animation }.returns(mockk(relaxed = true) {
            every { hasStarted() }.returns(true)
        })

        KtViewUtils.hiddenAnimTopToBottomWhitFade(mView)
        verify { mView.startAnimation(any()) }

        mView.animation.cancel()

    }

    @Test
    fun `should verify when show anim bottom to up with fade without`() {
        every { mView.animation }.returns(mockk(relaxed = true) {
            every { hasStarted() }.returns(true)
        })

        KtViewUtils.showAnimBottomToUpWithFade(mView)
        verify { mView.startAnimation(any()) }

        mView.animation.cancel()
    }

    @Test
    fun `should verify when get grid item width without`() {
        Assert.assertEquals(0, KtViewUtils.getGridItemWidth(null, 0, 0, 0))

        var point = Point(0, 0)
        mockkObject(KtViewUtils)
        every { KtViewUtils.getWindowSize(mActivity) }.returns(point)
        Assert.assertEquals(0, KtViewUtils.getGridItemWidth(mActivity, 0, 1, 0))

        point.x = 100
        every { KtViewUtils.getWindowSize(mActivity) }.returns(point)
        Assert.assertEquals(100, KtViewUtils.getGridItemWidth(mActivity, 0, 1, 0))
    }

    @Test
    fun `should verify when get window size without`() {
        Assert.assertNotNull(KtViewUtils.getWindowSize(null))

        Assert.assertNotNull(KtViewUtils.getWindowSize(mActivity))

    }

    @Test
    fun `should return 368 when nav on the right of screen`() {
        //Given
        mockkStatic(ViewCompat::class)
        val screenWidth = 500
        //when
        val windowManager = mockWindowWidth(screenWidth)
        val window = mockNavInsertLeftAndRight(0, 132)
        every { mActivity.windowManager } returns windowManager
        every { mActivity.window } returns window
        //Then
        Assert.assertEquals(368, KtViewUtils.getWindowSize(mActivity).x)
    }

    @Test
    fun `should return 368 when nav on the left of screen`() {
        //Given
        mockkStatic(ViewCompat::class)
        val screenWidth = 500
        //when
        val windowManager = mockWindowWidth(screenWidth)
        val window = mockNavInsertLeftAndRight(132, 0)
        every { mActivity.windowManager } returns windowManager
        every { mActivity.window } returns window
        //Then
        Assert.assertEquals(368, KtViewUtils.getWindowSize(mActivity).x)
    }

    @Test
    fun `should return 500 when nav not on the screen`() {
        //Given
        mockkStatic(ViewCompat::class)
        val screenWidth = 500
        //when
        val windowManager = mockWindowWidth(screenWidth)
        val window = mockNavInsertLeftAndRight(0, 0)
        every { mActivity.windowManager } returns windowManager
        every { mActivity.window } returns window
        //Then
        Assert.assertEquals(500, KtViewUtils.getWindowSize(mActivity).x)
    }

    private fun mockWindowWidth(screenWidth: Int): WindowManager {
        return mockk(relaxed = true) {
            every<WindowMetrics> { currentWindowMetrics } returns mockk() {
                every<Rect> { bounds } returns mockk(relaxed = true) {
                    every<Int> { width() } returns screenWidth
                }
            }
        }
    }

    private fun mockNavInsertLeftAndRight(left: Int, right: Int): Window {
        return mockk(relaxed = true) {
            every<View> { decorView } returns mockk() {
                every<View?> { rootView } returns mockk()
                every { ViewCompat.getRootWindowInsets(rootView) } returns mockk() {
                    every<Insets> { getInsets(any()) } returns Insets.of(left, 0, right, 0)
                }
            }
        }
    }

    @Test
    fun `should verify when get screen density without`() {
        Assert.assertEquals(0f, KtViewUtils.getScreenDensity(null))

        Assert.assertNotNull(KtViewUtils.getScreenDensity(mActivity))
    }

    @Test
    fun `should verify when get select model padding bottom without`() {
        var fileManagerRecyclerViewMockk = mockk<FileManagerRecyclerView>(relaxed = true)

        every { fileManagerRecyclerViewMockk.childCount }.returns(0)
        Assert.assertEquals(110, KtViewUtils.getSelectModelPaddingBottom(fileManagerRecyclerViewMockk, mView))

        every { fileManagerRecyclerViewMockk.childCount }.returns(1)
        every { mView.measuredHeight }.returns(0)
        Assert.assertEquals(330, KtViewUtils.getSelectModelPaddingBottom(fileManagerRecyclerViewMockk, mView))

        every { mView.measuredHeight }.returns(100)
        Assert.assertEquals(210, KtViewUtils.getSelectModelPaddingBottom(fileManagerRecyclerViewMockk, mView))
    }

    @Test
    fun `should verify when get recyclerView top padding without`() {
        Assert.assertEquals(110, KtViewUtils.getRecyclerViewTopPadding(null))

        var appBarLayout = mockk<AppBarLayout>(relaxed = true) {
            every { measuredHeight }.returns(0)
        }

        Assert.assertEquals(110, KtViewUtils.getRecyclerViewTopPadding(appBarLayout))

        every { appBarLayout.measuredHeight }.returns(100)
        Assert.assertEquals(210, KtViewUtils.getRecyclerViewTopPadding(appBarLayout))
    }

    @Test
    fun `should verify when update UI orientation without`() {
        mockkStatic(ModelUtils::class)
        every { ModelUtils.isTablet() } returns true
        mockkObject(UIConfigMonitor.Companion)
        var uiConfigMonitorMockK = mockk<UIConfigMonitor>(relaxed = true)
        every { UIConfigMonitor.instance }.returns(uiConfigMonitorMockK)
        every { uiConfigMonitorMockK.isScreenFold() }.returns(true)

        every { mActivity.requestedOrientation }.returns(SCREEN_ORIENTATION_USER)
        every { UIConfigMonitor.isZoomWindowShow() }.returns(false)
        KtViewUtils.updateUIOrientation(mActivity)

        every { uiConfigMonitorMockK.isScreenFold() }.returns(false)
        KtViewUtils.updateUIOrientation(mActivity)
        unmockkStatic(ModelUtils::class)
    }

    @Test
    fun `should verify update UI Orientation`() {
        mockkStatic(ModelUtils::class)
        every { ModelUtils.isTablet() } returns true
        // for MultiWindow begin
        var originOrientation = mActivity.requestedOrientation
        every { mActivity.isInMultiWindowMode } returns true
        KtViewUtils.updateUIOrientation(mActivity)
        Assert.assertEquals(originOrientation, mActivity.requestedOrientation)
        // for MultiWindow begin end

        // for ZoomWindow begin
        mockkObject(UIConfigMonitor.Companion)
        originOrientation = mActivity.requestedOrientation
        every { UIConfigMonitor.Companion.isZoomWindowShow() } returns true
        KtViewUtils.updateUIOrientation(mActivity)
        Assert.assertEquals(originOrientation, mActivity.requestedOrientation)
        // for ZoomWindow end

        // for not MultiWindow or ZoomWindow begin
        every { mActivity.isInMultiWindowMode } returns false
        every { UIConfigMonitor.Companion.isZoomWindowShow() } returns false
        val uiConfigMonitorMockK = mockk<UIConfigMonitor>(relaxed = true)
        every { UIConfigMonitor.instance }.returns(uiConfigMonitorMockK)

        // for isScreenFold begin
        every { uiConfigMonitorMockK.isScreenFold() }.returns(true)
        every { mActivity.requestedOrientation }.returns(SCREEN_ORIENTATION_NOSENSOR)
        KtViewUtils.updateUIOrientation(mActivity)
        Assert.assertEquals(SCREEN_ORIENTATION_NOSENSOR, mActivity.requestedOrientation)
        // for isScreenFold end

        // for not isScreenFold begin
        every { uiConfigMonitorMockK.isScreenFold() }.returns(false)
        every { mActivity.requestedOrientation }.returns(SCREEN_ORIENTATION_USER)
        KtViewUtils.updateUIOrientation(mActivity)
        Assert.assertEquals(SCREEN_ORIENTATION_USER, mActivity.requestedOrientation)
        // for not isScreenFold end
        // for not MultiWindow or ZoomWindow end
        unmockkStatic(ModelUtils::class)
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}