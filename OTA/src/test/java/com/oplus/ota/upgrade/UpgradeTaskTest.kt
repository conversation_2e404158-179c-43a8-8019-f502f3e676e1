package com.oplus.ota.upgrade

import android.app.Activity
import android.content.ContentProviderClient
import android.content.ContentResolver
import android.content.Context
import android.os.Bundle
import org.robolectric.RuntimeEnvironment
import com.coui.appcompat.dialog.COUIRotatingDialogBuilder
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import java.lang.reflect.Method

/**
 * UpgradeTask 类的单元测试类
 * 用于测试 OTA 升级任务的各种场景
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class UpgradeTaskTest {

    // 模拟的 Activity 对象
    private lateinit var mockActivity: Activity
    // 模拟的内容提供者客户端
    private lateinit var mockProviderClient: ContentProviderClient
    // 模拟的内容解析器
    private lateinit var mockContentResolver: ContentResolver
    // 测试上下文
    private lateinit var context: Context

    /**
     * 测试前的初始化方法
     * 创建所有需要的模拟对象和设置模拟行为
     */
    @Before
    fun setUp() {
        // 初始化模拟对象
        mockActivity = mockk(relaxed = true)
        mockProviderClient = mockk(relaxed = true)
        mockContentResolver = mockk(relaxed = true)
        context = RuntimeEnvironment.application

        // 模拟静态对象和方法
        mockkObject(MyApplication)
        every { MyApplication.sAppContext } returns context

        // 模拟日志类
        mockkStatic(Log::class)
        every { Log.v(any(), any()) } returns Unit
        every { Log.i(any(), any()) } returns Unit
        every { Log.w(any(), any()) } returns Unit

        // 模拟 Toast 显示
        mockkStatic(CustomToast::class)
        every { CustomToast.showShort(any<Int>()) } returns Unit

        // 模拟对话框构建器
        mockkConstructor(COUIRotatingDialogBuilder::class)
        every { anyConstructed<COUIRotatingDialogBuilder>().show() } returns mockk(relaxed = true)
    }

    /**
     * 测试后的清理方法
     * 解除所有模拟对象的绑定
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 通过反射调用 protected 的 doInBackground 方法
     * @param task 要测试的 UpgradeTask 实例
     * @param packageInfo 传入的包信息参数
     * @return 执行结果
     */
    private fun callDoInBackground(task: UpgradeTask, packageInfo: PackageInfo?): Boolean {
        val method: Method = UpgradeTask::class.java.getDeclaredMethod("doInBackground", Array<PackageInfo?>::class.java)
        method.isAccessible = true
        return method.invoke(task, arrayOf(packageInfo)) as Boolean
    }
    
    /**
     * 通过反射调用 protected 的 onPostExecute 方法
     * @param task 要测试的 UpgradeTask 实例
     * @param result 传入的执行结果参数
     */
    private fun callOnPostExecute(task: UpgradeTask, result: Boolean) {
        val method: Method = UpgradeTask::class.java.getDeclaredMethod("onPostExecute", Boolean::class.java)
        method.isAccessible = true
        method.invoke(task, result)
    }

    /**
     * 测试 doInBackground 方法在 packageInfo 为 null 时的行为
     * 预期返回 false
     */
    @Test
    fun `doInBackground should return false when packageInfo is null`() {
        // 创建测试对象
        val task = spyk(UpgradeTask(mockActivity))

        // 执行测试
        val result = callDoInBackground(task, null)

        // 验证结果
        assertFalse(result)
    }

    /**
     * 测试 onPostExecute 方法在结果为 false 时的行为
     * 预期会显示错误提示 Toast
     */
    @Test
    fun `onPostExecute should show toast when result is false`() {
        // 创建测试对象
        val task = spyk(UpgradeTask(mockActivity))

        // 执行测试
        callOnPostExecute(task, false)

        // 验证显示toast
        verify { CustomToast.showShort(any<Int>()) }
    }

    /**
     * 测试 onPostExecute 方法在结果为 true 时的行为
     * 预期不会显示错误提示 Toast
     */
    @Test
    fun `onPostExecute should not show toast when result is true`() {
        // 创建测试对象
        val task = spyk(UpgradeTask(mockActivity))

        // 执行测试
        callOnPostExecute(task, true)

        // 验证没有显示toast
        verify(exactly = 0) { CustomToast.showShort(any<Int>()) }
    }
}