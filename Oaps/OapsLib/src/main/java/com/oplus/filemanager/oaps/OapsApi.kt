/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.oaps.OapsApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/5/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.oaps

import android.app.Activity
import android.content.Context
import com.cdo.oaps.OapsConstants
import com.cdo.oaps.api.callback.Callback
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.constants.Constants
import com.oplus.filemanager.interfaze.oaps.OapsInitResultCallback
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.*
import com.oplus.filemanager.interfaze.oaps.IOapsLib
import java.lang.ref.WeakReference

object OapsApi : IOapsLib {
    private const val TAG = "OapsApi"

    override fun initOapsData(activity: Activity, callback: OapsInitResultCallback) {
        val screenSize = KtViewUtils.getWindowSize(activity)
        val screenParam: OapsUtils.DevScreenParam = OapsUtils.DevScreenParam(
            KtViewUtils.getScreenDensity(activity),
            screenSize.y,
            screenSize.x
        )
        val weakCallback = WeakReference(callback)
        ThreadManager.sThreadManager.execute(FileRunnable({
            Log.v(TAG, "initOapsData")
            weakCallback.get()?.onInitResult(true)
            if (OapsUtils.isOapsEnable()) {
                try {
                    OapsUtils.requestData(screenParam, null)
                } catch (e: Exception) {
                    Log.e(TAG, e.message)
                    weakCallback.get()?.onInitResult(false)
                }
            }
        }, TAG))
    }

    override fun isShowApkEntrance(): Boolean {
        return OapsUtils.isShowApkEntrance()
    }

    override fun isShowHomeEntrance(): Boolean {
        return OapsUtils.isShowHomeEntrance()
    }

    override fun startOaps(activity: Activity) {
        OapsUtils.startOaps(activity, OapsUtils.getOapsOpenUrl(
            OapsUtils.OAPS_HOME_ID,
            appContext.resources.getString(com.filemanager.common.R.string.recommended_apps)
        ), object : Callback() {
            override fun onResponse(response: Response) {
                val respCode = response.code
                Log.i(TAG, "Oaps Callback onResponse:$respCode")
                when (respCode) {
                    OapsConstants.SUCCESS ->  { // success
                        StatisticsUtils.onCommon(
                            appContext,
                            StatisticsUtils.OAPS_TO_APPSTORE_FROM_HOME_SUCC_COUNT
                        )
                        StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_APP_STORE, Constants.PAGE_MAIN)
                    }

                    else -> {
                        if (respCode != OapsUtils.LOCAL_ERROR) {
                            CustomToast.showShort(com.filemanager.common.R.string.temporarily_unable_to_jump_please_try_again_later)
                        }
                        StatisticsUtils.onCommon(
                            appContext,
                            StatisticsUtils.OAPS_TO_APPSTORE_FROM_HOME_FAIL_COUNT
                        )
                    }
                }
            }
        })
    }

    override fun openAppStoreDetail(context: Context, uninstallTab: Boolean) {
        OapsUtils.startOaps(context, OapsUtils.getOapsOpenUrl(
            OapsUtils.OAPS_APK_ID,
            context.getString(com.filemanager.common.R.string.recommended_apps)
        ), object : Callback() {
            override fun onResponse(response: Response) {
                val respCode = response.code
                Log.i(TAG, "Oaps Callback onResponse:$respCode")
                when (respCode) {
                    OapsConstants.SUCCESS -> {
                        Log.i(TAG, "Oaps Callback success")
                        StatisticsUtils.onCommon(
                            context, if (uninstallTab) {
                                StatisticsUtils.OAPS_TO_APPSTORE_FROM_APK_UNINS_SUCC_COUNT
                            } else {
                                StatisticsUtils.OAPS_TO_APPSTORE_FROM_APK_INS_SUCC_COUNT
                            }
                        )
                    }

                    else -> {
                        Log.w(TAG, "Oaps Callback failed")
                        if (respCode != OapsUtils.LOCAL_ERROR) {
                            CustomToast.showShort(com.filemanager.common.R.string.temporarily_unable_to_jump_please_try_again_later)
                        }
                        StatisticsUtils.onCommon(
                            context, if (uninstallTab) {
                                StatisticsUtils.OAPS_TO_APPSTORE_FROM_APK_UNINS_FAIL_COUNT
                            } else {
                                StatisticsUtils.OAPS_TO_APPSTORE_FROM_APK_INS_FAIL_COUNT
                            }
                        )
                    }
                }
            }
        })
    }

    override fun checkAppStoreEnabledWithDialog(context: Context): Boolean {
        return OapsUtils.checkAppStoreEnabled(context)
    }
}