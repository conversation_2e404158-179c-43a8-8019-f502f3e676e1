package com.oplus.filemanager.parentchild.view

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.oplus.filemanager.main.R
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config

/**
 * SideNavigationItemContainer的单元测试类
 * 用于测试侧边导航项容器的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29]) // 修改为SDK 29以满足应用最低要求
class SideNavigationItemContainerTest {

    private lateinit var context: Context
    private lateinit var container: SideNavigationItemContainer

    /**
     * 测试前的初始化方法
     * 创建测试环境和容器实例
     */
    @Before
    fun setUp() {
        context = RuntimeEnvironment.application
        // 创建容器实例
        container = SideNavigationItemContainer(context)

        // 添加必要的子视图到容器中，这些ID在源码中有引用
        val dragView = ImageView(context).apply { 
            id = R.id.drag_view
            visibility = View.GONE // 设置为GONE以确保shouldLayoutDrag为false
        }
        val optionIcon = ImageView(context).apply { id = R.id.option_icon }
        val optionTitle = TextView(context).apply { id = R.id.option_title }
        val optionSubTitle = TextView(context).apply { 
            id = R.id.option_subtitle
            text = "" // 确保文本为空
        }
        val lockIcon = ImageView(context).apply { 
            id = R.id.lock_icon
            visibility = View.GONE // 设置为GONE以确保shouldLayoutLockIcon为false
        }
        val widgetFrame = LinearLayout(context).apply { 
            id = R.id.option_widget
            // 不添加子视图，确保childCount为0
        }

        container.addView(dragView)
        container.addView(optionIcon)
        container.addView(optionTitle)
        container.addView(optionSubTitle)
        container.addView(lockIcon)
        container.addView(widgetFrame)
        
        // 手动初始化lateinit属性
        val dragViewField = container.javaClass.getDeclaredField("dragView").apply { isAccessible = true }
        dragViewField.set(container, dragView)
        
        val optionIconField = container.javaClass.getDeclaredField("optionIcon").apply { isAccessible = true }
        optionIconField.set(container, optionIcon)
        
        val optionTitleField = container.javaClass.getDeclaredField("optionTitle").apply { isAccessible = true }
        optionTitleField.set(container, optionTitle)
        
        val optionSubTitleField = container.javaClass.getDeclaredField("optionSubTitle").apply { isAccessible = true }
        optionSubTitleField.set(container, optionSubTitle)
        
        val lockIconField = container.javaClass.getDeclaredField("lockIcon").apply { isAccessible = true }
        lockIconField.set(container, lockIcon)
        
        val widgetFrameField = container.javaClass.getDeclaredField("widgetFrame").apply { isAccessible = true }
        widgetFrameField.set(container, widgetFrame)
    }

    /**
     * 测试后的清理方法
     * 重置容器状态
     */
    @After
    fun tearDown() {
        // 清理静态状态
        container.setEditState(false)
        container.setIsSelected(false, false)
        container.setIsHover(false, false)
    }

    /**
     * 测试仅使用Context参数的构造函数
     */
    @Test
    fun `constructor with context should create instance`() {
        val instance = SideNavigationItemContainer(context)
        assertNotNull(instance)
    }

    /**
     * 测试使用Context和AttributeSet参数的构造函数
     */
    @Test
    fun `constructor with context and attrs should create instance`() {
        val instance = SideNavigationItemContainer(context, null)
        assertNotNull(instance)
    }

    /**
     * 测试使用Context、AttributeSet和defStyleAttr参数的构造函数
     */
    @Test
    fun `constructor with context attrs and defStyleAttr should create instance`() {
        val instance = SideNavigationItemContainer(context, null, 0)
        assertNotNull(instance)
    }

    /**
     * 测试setEditState方法是否能正确更新状态并请求布局
     */
    @Test
    fun `setEditState should update editState and requestLayout`() {
        val editStateField = container.javaClass.getDeclaredField("editState").apply { isAccessible = true }
        assertFalse(editStateField.get(container) as Boolean)

        container.setEditState(true)
        assertTrue(editStateField.get(container) as Boolean)

        container.setEditState(false)
        assertFalse(editStateField.get(container) as Boolean)
    }

    /**
     * 测试当设置相同值时setEditState不会触发请求布局
     */
    @Test
    fun `setEditState with same value should not trigger requestLayout`() {
        container.setEditState(false) // 默认是false
        container.setEditState(true)
        val editStateField = container.javaClass.getDeclaredField("editState").apply { isAccessible = true }
        assertTrue(editStateField.get(container) as Boolean)
    }

    /**
     * 测试当stateEffectDrawable为null时setBackground方法的行为
     */
    @Test
    fun `setBackground should set background when stateEffectDrawable is null`() {
        val stateEffectDrawableField = container.javaClass.getDeclaredField("stateEffectDrawable").apply { isAccessible = true }
        stateEffectDrawableField.set(container, null)

        val drawable = ColorDrawable(android.graphics.Color.RED)
        container.background = drawable

        assertEquals(drawable, container.background)
    }

    /**
     * 测试setIsSelected方法是否能正确调用setTouchEnterStateLocked
     */
    @Test
    fun `setIsSelected should call setTouchEnterStateLocked`() {
        container.setIsSelected(true, true)
        container.setIsSelected(false, false)
        // 验证方法调用无异常
        assertTrue(true)
    }

    /**
     * 测试setIsHover方法是否能正确调用setHoverStateLocked
     */
    @Test
    fun `setIsHover should call setHoverStateLocked`() {
        container.setIsHover(true, true)
        container.setIsHover(false, false)
        // 验证方法调用无异常
        assertTrue(true)
    }

    /**
     * 测试onMeasure方法是否能正确测量子视图并设置尺寸
     */
    @Test
    fun `onMeasure should measure children and set dimensions`() {
        container.measure(
            View.MeasureSpec.makeMeasureSpec(1000, View.MeasureSpec.EXACTLY),
            View.MeasureSpec.makeMeasureSpec(100, View.MeasureSpec.EXACTLY)
        )

        assertEquals(1000, container.measuredWidth)
        assertTrue(container.measuredHeight > 0)
    }

    /**
     * 测试calculateTotalGapWidth方法是否能正确计算间隙宽度
     */
    @Test
    fun `calculateTotalGapWidth should return correct gap count`() {
        val method = container.javaClass.getDeclaredMethod("calculateTotalGapWidth", Boolean::class.java)
        method.isAccessible = true

        // 测试edit=false的情况
        val gapWithoutEdit = method.invoke(container, false) as Int
        assertTrue(gapWithoutEdit >= 0)

        // 测试edit=true的情况
        val gapWithEdit = method.invoke(container, true) as Int
        assertTrue(gapWithEdit >= 0)
    }

    /**
     * 测试checkViewState方法是否能正确更新布局标志
     */
    @Test
    fun `checkViewState should update layout flags`() {
        val method = container.javaClass.getDeclaredMethod("checkViewState")
        method.isAccessible = true

        method.invoke(container)

        val shouldLayoutWidget = container.javaClass.getDeclaredField("shouldLayoutWidget").apply { isAccessible = true }.get(container) as Boolean
        val shouldLayoutSubtitle = container.javaClass.getDeclaredField("shouldLayoutSubtitle").apply { isAccessible = true }.get(container) as Boolean
        val shouldLayoutLockIcon = container.javaClass.getDeclaredField("shouldLayoutLockIcon").apply { isAccessible = true }.get(container) as Boolean
        val shouldLayoutDrag = container.javaClass.getDeclaredField("shouldLayoutDrag").apply { isAccessible = true }.get(container) as Boolean

        assertFalse(shouldLayoutWidget)
        assertFalse(shouldLayoutSubtitle)
        assertFalse(shouldLayoutLockIcon)
        assertFalse(shouldLayoutDrag)
    }

    /**
     * 测试measureDragView方法是否能正确处理视图不可见的情况
     */
    @Test
    fun `measureDragView should handle visibility correctly`() {
        val method = container.javaClass.getDeclaredMethod(
            "measureDragView",
            Int::class.java,
            Int::class.java,
            Int::class.java
        )
        method.isAccessible = true

        val result = method.invoke(container, 100, 0, 0) as Int
        assertEquals(100, result)
    }

    /**
     * 测试measureOptionIcon方法是否能正确测量图标
     */
    @Test
    fun `measureOptionIcon should measure icon`() {
        val method = container.javaClass.getDeclaredMethod(
            "measureOptionIcon",
            Int::class.java,
            Int::class.java,
            Int::class.java
        )
        method.isAccessible = true

        val result = method.invoke(container, 100, 0, 0) as Int
        assertTrue(result <= 100)
    }

    /**
     * 测试measureLockIcon方法是否能正确处理视图不可见的情况
     */
    @Test
    fun `measureLockIcon should handle visibility correctly`() {
        val method = container.javaClass.getDeclaredMethod(
            "measureLockIcon",
            Int::class.java,
            Int::class.java,
            Int::class.java
        )
        method.isAccessible = true

        val result = method.invoke(container, 100, 0, 0) as Int
        assertEquals(100, result)
    }

    /**
     * 测试measureWidgetFrame方法是否能正确处理视图不可见的情况
     */
    @Test
    fun `measureWidgetFrame should handle visibility correctly`() {
        val method = container.javaClass.getDeclaredMethod(
            "measureWidgetFrame",
            Int::class.java,
            Int::class.java,
            Int::class.java
        )
        method.isAccessible = true

        val result = method.invoke(container, 100, 0, 0) as Int
        assertEquals(100, result)
    }

    /**
     * 测试onLayout方法是否能正常执行
     */
    @Test
    fun `onLayout should execute without exception`() {
        container.measure(
            View.MeasureSpec.makeMeasureSpec(1000, View.MeasureSpec.EXACTLY),
            View.MeasureSpec.makeMeasureSpec(100, View.MeasureSpec.EXACTLY)
        )
        container.layout(0, 0, 1000, 100)
        assertTrue(true)
    }

    /**
     * 测试onSizeChanged方法是否能正确更新布局背景矩形
     */
    @Test
    fun `onSizeChanged should update layoutBackgroundRect`() {
        // 直接调用受保护的方法不可行，改为通过触发size change事件来间接测试
        container.measure(
            View.MeasureSpec.makeMeasureSpec(200, View.MeasureSpec.EXACTLY),
            View.MeasureSpec.makeMeasureSpec(100, View.MeasureSpec.EXACTLY)
        )
        container.layout(0, 0, 200, 100)
        assertTrue(true)
    }

    /**
     * 测试isRtl方法是否能正确返回布尔值
     */
    @Test
    fun `isRtl should return boolean`() {
        val method = container.javaClass.getDeclaredMethod("isRtl")
        method.isAccessible = true

        val result = method.invoke(container) as Boolean
        assertFalse(result)
    }
}