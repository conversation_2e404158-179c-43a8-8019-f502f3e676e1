/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.audio
 ** Version: 1.0
 ** Date: 2021/7/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.audio

import androidx.annotation.Keep
import java.io.Serializable

/**
 * the object save to file and load from file
 */
@Keep
data class AudioThumbnailResultByteArray(
    val mBitmapByteArray: ByteArray? = null,
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = -981779706078975266L
    }
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as AudioThumbnailResultByteArray

        if (mBitmapByteArray != null) {
            if (other.mBitmapByteArray == null) return false
            if (!mBitmapByteArray.contentEquals(other.mBitmapByteArray)) return false
        } else if (other.mBitmapByteArray != null) return false

        return true
    }

    override fun hashCode(): Int {
        return mBitmapByteArray?.contentHashCode() ?: 0
    }
}