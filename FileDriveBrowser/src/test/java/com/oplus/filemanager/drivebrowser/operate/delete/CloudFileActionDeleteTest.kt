/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudFileActionDeleteTest
 ** Description : CloudFileActionDelete Unit Test
 ** Version     : 1.0
 ** Date        : 2024/01/11 09:55
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/01/11       1.0      create
 ***********************************************************************/

package com.oplus.filemanager.drivebrowser.operate.delete

import android.content.Context
import android.content.DialogInterface
import android.content.res.Resources
import android.os.Looper
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.DISMISS_PROGRESS
import com.filemanager.fileoperate.base.SHOW_PROGRESS
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.drivebrowser.ui.CloudDocsItem
import com.oplus.filemanager.provider.DriveFileDBHelper
import io.mockk.coEvery
import io.mockk.every
import io.mockk.just
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.slot
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import kotlin.time.Duration.Companion.seconds

class CloudFileActionDeleteTest {

    private lateinit var context: Context
    private lateinit var cloudFile: CloudDocsItem
    private lateinit var lifeOwner: LifecycleOwner
    private lateinit var networkService: FileDriveRepository
    private lateinit var deleteAction: CloudFileActionDelete
    private val dispatcher = StandardTestDispatcher()

    @Before
    fun setup() {
        context = mockk()
        val resource = mockk<Resources>()
        mockkObject(MyApplication)
        every { context.applicationContext }.returns(context)
        every { appContext }.returns(context)
        every { resource.getString(com.filemanager.common.R.string.dialog_deleting) }.returns("删除中...")
        every { context.resources }.returns(resource)

        lifeOwner = mockk()
        cloudFile = CloudDocsItem()
        cloudFile.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
        cloudFile.id = "123"
        cloudFile.mDisplayName = "测试"
        networkService = mockk<TencentFileDriveRepository>()
        deleteAction =
            CloudFileActionDelete(lifeOwner, "tencent", listOf(cloudFile), networkService)
        mockkStatic(DriveFileDBHelper::class)
        mockkStatic(Looper::class)
        val looper = mockk<Looper>()
        every { Looper.getMainLooper() }.returns(looper)
        every { looper.thread }.returns(Thread.currentThread())
        mockkStatic(StatisticsUtils::class)
        justRun { StatisticsUtils.onCommon(any(), any(), any<Map<String, String>>()) }

        Dispatchers.setMain(dispatcher)
    }

    @After
    fun teardown() {
        unmockkObject(MyApplication)
        unmockkStatic(DriveFileDBHelper::class)
        unmockkStatic(Looper::class)
        unmockkStatic(StatisticsUtils::class)
        Dispatchers.resetMain()
    }

    @Test
    fun should_notNull_when_initFileDeleteBean() {
        deleteAction = mockk()
        every { deleteAction.fileDeleteBean = any() }.answers { callOriginal() }
        every { deleteAction.fileDeleteBean }.answers { callOriginal() }
        every { deleteAction.initFileDeleteBean(any(), any()) }.answers { callOriginal() }
        justRun { deleteAction.notifyLockReleased() }
        justRun { deleteAction.cancel() }

        deleteAction.initFileDeleteBean(1, "tencent")
        val deleteBean = deleteAction.fileDeleteBean
        Assert.assertNotNull(deleteBean)

        deleteBean?.onClickListener?.onClick(mockk(), DialogInterface.BUTTON_NEGATIVE)
        verify { deleteAction.cancel() }

        deleteBean?.onClickListener?.onClick(mockk(), DialogInterface.BUTTON_NEUTRAL)
        verify { deleteAction.notifyLockReleased() }
    }

    @Test
    fun should_call_notifyLockReleased_when_onCanceled() {
        deleteAction = mockk()
        every { deleteAction.onCancelled() }.answers { callOriginal() }
        justRun { deleteAction.notifyObserver(any(), any(), any()) }
        justRun { deleteAction.notifyLockReleased() }
        deleteAction.onCancelled()
        verify { deleteAction.notifyLockReleased() }
    }

    @Test
    fun should_return_boolean_when_reallyExecuteAction_folder() {
        runTest(timeout = testCaseTimeout) {
            coEvery { networkService.deleteFolder(any()) }.returns(Pair(false, ""))
            var result = deleteAction.reallyExecuteAction(listOf(cloudFile))
            Assert.assertFalse(result)

            coEvery { networkService.deleteFolder(any()) }.returns(Pair(true, ""))
            justRun { DriveFileDBHelper.deleteFolder(any(), any()) }
            result = deleteAction.reallyExecuteAction(listOf(cloudFile))
            Assert.assertTrue(result)
        }
    }

    @Test
    fun should_return_boolean_when_reallyExecuteAction_file() {
        //setUp
        mockkObject(CloudFileActionDelete)
        //given
        every { CloudFileActionDelete.getDelayTime(any()) } returns 0
        runTest(timeout = testCaseTimeout) {
            cloudFile.mLocalType = MimeTypeHelper.IMAGE_TYPE
            coEvery { networkService.deleteFile(any(), any()) }.returns(Pair(false, ""))
            mockkObject(deleteAction)
            every { deleteAction.removeDBData(any()) } just runs
            var result = deleteAction.reallyExecuteAction(listOf(cloudFile))
            Assert.assertFalse(result)

            coEvery { networkService.deleteFile(any(), any()) }.returns(Pair(true, ""))
            justRun { DriveFileDBHelper.deleteDocument(any(), any()) }
            result = deleteAction.reallyExecuteAction(listOf(cloudFile))
            Assert.assertTrue(result)
            verify { deleteAction.removeDBData(any()) }
        }
        //teardown
        unmockkObject(CloudFileActionDelete)
        unmockkObject(deleteAction)
    }

    @Test
    fun should_statisticsDeleteFail_when_reallyExecuteAction_file_fail() {
        //setUp
        mockkObject(CloudFileActionDelete)
        //given
        every { CloudFileActionDelete.getDelayTime(any()) } returns 0

        runTest(timeout = testCaseTimeout) {
            cloudFile.mLocalType = MimeTypeHelper.IMAGE_TYPE
            mockkObject(deleteAction)
            every { deleteAction.removeDBData(any()) } just runs

            coEvery { networkService.deleteFile(any(), any()) }.returns(Pair(false, "模拟失败"))
            val result = deleteAction.reallyExecuteAction(listOf(cloudFile))
            Assert.assertFalse(result)
            verify {
                StatisticsUtils.onCommon(
                    any(),
                    StatisticsUtils.DELETE_DRIVE_FILE_FAIL,
                    any<Map<String, String>>()
                )
            }
            verify(inverse = true) { deleteAction.removeDBData(any()) }
        }
        //teardown
        unmockkObject(CloudFileActionDelete)
        unmockkObject(deleteAction)
    }

    @Test
    fun should_call_onCommon_when_statisticsDeleteFail() {
        networkService = mockk<TencentFileDriveRepository>()
        deleteAction =
            CloudFileActionDelete(lifeOwner, "tencent", listOf(cloudFile), networkService)
        deleteAction.statisticsDeleteFail("模拟失败")
        verify {
            StatisticsUtils.onCommon(
                any(),
                StatisticsUtils.DELETE_DRIVE_FILE_FAIL,
                any<Map<String, String>>()
            )
        }
    }

    @Test
    fun should_call_notifyObserver_showProgress_when_showProgressDialog() {
        deleteAction = mockk()
        justRun { deleteAction.notifyObserver(any(), any(), any()) }
        every { deleteAction.showProgressDialog() }.answers { callOriginal() }

        deleteAction.showProgressDialog()
        verify { deleteAction.notifyObserver(SHOW_PROGRESS, any(), any()) }
    }

    @Test
    fun should_call_notifyObserver_DISMISS_PROGRESS_when_cancelShowProgressDialog() {
        deleteAction = mockk()
        justRun { deleteAction.notifyObserver(any(), any(), any()) }
        every { deleteAction.cancelNotifyObserver(any()) }.answers { callOriginal() }
        every { deleteAction.cancelShowProgressDialog() }.answers { callOriginal() }

        deleteAction.cancelShowProgressDialog()
        verify { deleteAction.cancelNotifyObserver(SHOW_PROGRESS) }
        verify { deleteAction.notifyObserver(DISMISS_PROGRESS) }
    }

    @Test
    fun should_call_notify_when_afterRun() {
        mockkObject(deleteAction)
        justRun { deleteAction.notifyObserver(any(), any()) }

        deleteAction.afterRun(true)
        verify { deleteAction.notifyObserver(ACTION_DONE, null, 0) }

        deleteAction.isAuth = true
        deleteAction.afterRun(false)
        verify { deleteAction.notifyObserver(ACTION_FAILED, true, 0L) }
        unmockkObject(deleteAction)
    }

    @Test
    fun `should return right when call getDelayTime`() {
        //given
        val fileSize = arrayListOf(55, 33, 20, 5, 4, 2)
        val expected = arrayListOf(6000L, 4000L, 3000L, 3000L, 1500L, 1500L)
        //when
        fileSize.forEachIndexed { index, item ->
            val result = CloudFileActionDelete.getDelayTime(item)
            //then
            Assert.assertEquals(expected[index], result)
        }
    }

    @Test
    fun `should call right when call removeDBData`() {
        //setup
        mockkStatic(DriveFileDBHelper::class)
        //given
        val files = ArrayList<DriveFileWrapper>()
        val file1 = mockk<DriveFileWrapper>()
        val file2 = mockk<DriveFileWrapper>()
        val file3 = mockk<DriveFileWrapper>()
        val file4 = mockk<DriveFileWrapper>()
        files.add(file1)
        files.add(file2)
        files.add(file3)
        files.add(file4)
        every { file1.mIsDirectory } returns true
        every { file1.id } returns "id1"
        every { file2.mIsDirectory } returns true
        every { file2.id } returns "id2"
        every { file3.mIsDirectory } returns false
        every { file3.id } returns "id3"
        every { file4.mIsDirectory } returns false
        every { file4.id } returns "id4"
        every { DriveFileDBHelper.batchDeleteFolder(any(), any()) } just runs
        every { DriveFileDBHelper.deleteFolder(any(), any()) } just runs
        every { DriveFileDBHelper.batchDeleteDocument(any(), any()) } just runs
        every { DriveFileDBHelper.deleteDocument(any(), any()) } just runs
        val foldIds = arrayOf("id1", "id2").toList()
        val fileIds = arrayOf("id3", "id4").toList()

        val slot1 = slot<List<String>>()
        val slot2 = slot<List<String>>()

        //when
        deleteAction.removeDBData(files)
        //then
        verify { DriveFileDBHelper.batchDeleteFolder(any(), capture(slot1)) }
        verify { DriveFileDBHelper.batchDeleteDocument(any(), capture(slot2)) }
        Assert.assertEquals(foldIds, slot1.captured)
        Assert.assertEquals(fileIds, slot2.captured)

        //teardown
        unmockkStatic(DriveFileDBHelper::class)
    }

    private companion object {
        private val testCaseTimeout = 30.seconds
    }
}