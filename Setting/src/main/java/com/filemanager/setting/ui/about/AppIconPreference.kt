/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AppIconPreference.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/4/8
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  hank.zhou      2022/4/8      1.0        create
 ***********************************************************************/
package com.filemanager.setting.ui.about

import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.graphics.Rect
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import androidx.core.text.layoutDirection
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.poplist.COUIPopupWindow
import com.coui.appcompat.preference.COUIPreference
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.ClipboardUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.setting.R
import java.util.Locale

class AppIconPreference : COUIPreference {

    private var mAppIcon: ImageView? = null
    private var mAppVersion: TextView? = null

    /**
     * TextView 的长按后弹出的popupWindow
     */
    private var popupWindow: COUIPopupWindow? = null
    private val drawableRect = Rect()

    constructor(context: Context?) : this(context, null)

    constructor(context: Context?, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : this(context, attrs, defStyleAttr, 0)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) : super(
        context,
        attrs,
        defStyleAttr,
        defStyleRes
    )

    @SuppressLint("RestrictedApi")
    override fun onBindViewHolder(holder: PreferenceViewHolder) {
        super.onBindViewHolder(holder)
        mAppIcon = holder.findViewById(R.id.about_app_icon) as ImageView
        mAppVersion = holder.findViewById(R.id.about_app_version) as TextView

        mAppIcon?.apply {
            try {
                val drawable = MyApplication.sAppContext.packageManager.getApplicationIcon(MyApplication.sAppContext.packageName)
                setImageDrawable(drawable)
            } catch (e: PackageManager.NameNotFoundException) {
                //do nothing
            }
            (parent as? ViewGroup)?.apply {
                setOnClickListener(null)
                background = null
            }
        }

        mAppVersion?.apply {
            try {
                val info: PackageInfo? = MyApplication.sAppContext.packageManager?.getPackageInfo(MyApplication.sAppContext.packageName, 0)
                text = this.resources.getString(com.filemanager.common.R.string.settings_about_app_version, info?.versionName)
            } catch (e: PackageManager.NameNotFoundException) {
                //do nothing
            }
            setOnLongClickListener {
                if (popupWindow == null) {
                    initPopupWindow(drawableRect, this.text.toString())
                }
                showPopupWindow(drawableRect, this)
                true
            }
        }
    }

    /**
     * 初始化长按的弹窗[popupWindow]
     * @param drawableRect 本类中只是传入了一个初始化的Rect，
     *                     但是在该方法执行完成后，会通过getPadding方法拿到了背景drawable的边界信息
     * @param text 复制的文本
     */
    private fun initPopupWindow(drawableRect: Rect, text: String) {
        popupWindow = COUIPopupWindow(context).apply {
            contentView = LayoutInflater.from(context).inflate(
                com.support.component.R.layout.coui_component_popup_window_layout,
                null, false
            )
            ResourcesCompat.getDrawable(context.resources, com.support.poplist.R.drawable.coui_popup_window_bg, null)
                ?.apply {
                    getPadding(drawableRect)
                    setBackgroundDrawable(this)
                }
            contentView.findViewById<TextView>(com.support.component.R.id.popup_window_copy_body).apply {
                setText(com.filemanager.common.R.string.menu_file_list_copy)
                setOnClickListener {
                    //popupWindow中的按钮被单击后，将文本信息复制到剪切板
                    copyToClipboard(text)
                    dismiss()
                }
            }
            setDismissTouchOutside(true)
        }
    }

    /**
     * 弹出[popupWindow]
     */
    private fun showPopupWindow(drawableRect: Rect, view: View) {
        val offsetX = (drawableRect.left + drawableRect.right +
                context.resources.getDimensionPixelOffset(com.support.component.R.dimen.coui_component_copy_window_width) - view.measuredWidth) / 2
        val offsetY = view.measuredHeight + context.resources.getDimensionPixelOffset(
            com.support.component.R.dimen.coui_component_copy_window_height
        ) + context.resources.getDimensionPixelOffset(
            com.support.component.R.dimen.coui_component_copy_window_margin_bottom
        ) + drawableRect.top
        val xoff = if (Locale.getDefault().layoutDirection == View.LAYOUT_DIRECTION_RTL) {
            -view.measuredWidth - offsetX
        } else {
            -offsetX
        }
        popupWindow?.showAsDropDown(view, xoff, -offsetY)
    }

    /**
     * 复制到剪切板
     */
    private fun copyToClipboard(text: String) {
        ClipboardUtils.copyToClipboard(this.context, text)
        // showToast
        CustomToast.showShort(com.filemanager.common.R.string.copied)
    }
}