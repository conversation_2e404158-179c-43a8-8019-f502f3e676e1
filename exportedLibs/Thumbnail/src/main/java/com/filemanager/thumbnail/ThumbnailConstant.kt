package com.filemanager.thumbnail

import androidx.annotation.IntDef
import androidx.annotation.Keep

@Keep
object ThumbnailConstant {
    const val BUCKET_AUDIO_FILE_RESULT = "bucket_audio_file_result"

    const val YO_ZO_THUMB_SERVICE_1 = 1
    const val YO_ZO_THUMB_SERVICE_2 = 2
    const val YO_ZO_THUMB_SERVICE_3 = 3
    const val YO_ZO_THUMB_SERVICE_4 = 4
    const val YO_ZO_THUMB_SERVICE_5 = 5
    const val YO_ZO_THUMB_SERVICE_6 = 6

    internal const val DEFAULT_BASE_LOG_TAG = "FileManager"
    internal const val DEFAULT_WPS_DOC_THUMBNAIL_PRIORITY = 200
    internal const val DEFAULT_YO_ZO_DOC_THUMBNAIL_PRIORITY = 100
    internal const val DEFAULT_YO_ZO_PACKAGE = "andes.oplus.documentsreader"
    internal const val DEFAULT_YO_ZO_SERVICE_TIMEOUT = 2000L
    internal const val DEFAULT_YO_ZO_MIN_IDLE_SERVICE = 1
    internal const val DEFAULT_YO_ZO_REBIND_DELAY = 300L
    internal const val DEFAULT_YO_ZO_REBIND_MAX_COUNT = 3
    internal const val DEFAULT_YO_ZO_DOC_DIGEST_ALGORITHM = "SHA-1"
    internal const val DEFAULT_YO_ZO_DOC_DIGEST_MAX_SIZE = 100 * 1024 * 1024L // 100MB
    internal const val DEFAULT_YO_ZO_DOC_RETRY_COUNT = 3
}

@IntDef(
    ThumbnailConstant.YO_ZO_THUMB_SERVICE_1,
    ThumbnailConstant.YO_ZO_THUMB_SERVICE_2,
    ThumbnailConstant.YO_ZO_THUMB_SERVICE_3,
    ThumbnailConstant.YO_ZO_THUMB_SERVICE_4,
    ThumbnailConstant.YO_ZO_THUMB_SERVICE_5,
    ThumbnailConstant.YO_ZO_THUMB_SERVICE_6
)
@Retention(AnnotationRetention.SOURCE)
annotation class YoZoThumbServices