/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : DownloadRequestKtTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/30 18:59
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/30       1.0      create
 **********************************************************************/
package com.oplus.filemanager.drivebrowser.download

import android.os.Bundle
import com.filemanager.common.helper.CategoryHelper
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Test

class DownloadRequestKtTest {

    @Test
    fun testParseDownloadTaskInfoWhenRequestBundleIsNull() {
        val parseResult = parseDownloadTaskInfo(null)
        Assert.assertNull(parseResult)
    }

    @Test
    fun testParseDownloadTaskInfoWhenRequestBundleNoMatchType() {
        val bundle = mockk<Bundle>()
        every { bundle.getInt(KEY_SOURCE_TYPE) }.returns(CategoryHelper.CATEGORY_DOWNLOAD)
        val parseResult = parseDownloadTaskInfo(bundle)
        Assert.assertNull(parseResult)
    }

    @Test
    fun testParseDownloadTaskInfoWhenIsTencentType() {
        val bundle = mockk<Bundle>()
        every { bundle.getInt(KEY_SOURCE_TYPE) }.returns(CategoryHelper.CATEGORY_TENCENT_DOCS)
        every { bundle.getString(KEY_FILE_ID) }.returns(FILE_ID)
        every { bundle.getString(KEY_TARGET_PATH) }.returns(TARGET_PATH)
        every { bundle.getString(KEY_EXPORT_TYPE) }.returns(EXPORT_TYPE)
        val parseResult = parseDownloadTaskInfo(bundle)
        Assert.assertEquals(CategoryHelper.CATEGORY_TENCENT_DOCS, parseResult?.category)
        Assert.assertEquals(EXPORT_TYPE, parseResult?.exportType)
    }

    @Test
    fun testParseDownloadTaskInfoWhenIsKDocsType() {
        val bundle = mockk<Bundle>()
        every { bundle.getInt(KEY_SOURCE_TYPE) }.returns(CategoryHelper.CATEGORY_K_DOCS)
        every { bundle.getString(KEY_FILE_ID) }.returns(FILE_ID)
        every { bundle.getString(KEY_TARGET_PATH) }.returns(TARGET_PATH)
        val parseResult = parseDownloadTaskInfo(bundle)
        Assert.assertEquals(FILE_ID, parseResult?.fileId)
        Assert.assertEquals(TARGET_PATH, parseResult?.targetPath)
        Assert.assertEquals(CategoryHelper.CATEGORY_K_DOCS, parseResult?.category)
    }

    companion object {
        private const val FILE_ID = "300000000GKvJqeggQNng"
        private const val TARGET_PATH = "/storage/emulated/0/Download/"
        private const val EXPORT_TYPE = "doc"
    }
}