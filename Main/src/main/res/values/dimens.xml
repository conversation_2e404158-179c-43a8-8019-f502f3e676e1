<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="main_category_content_margin">8dp</dimen>
    <dimen name="main_category_phone_space_padding">11dp</dimen>
    <dimen name="main_category_source_padding_bottom">12dp</dimen>
    <dimen name="main_category_source_item_padding_vertical">12dp</dimen>
    <dimen name="main_category_source_item_padding_horizontal">12dp</dimen>
    <dimen name="main_category_view_padding_top">18dp</dimen>
    <dimen name="main_category_view_padding_bottom">12dp</dimen>
    <dimen name="main_category_phone_space_progress_height">40dp</dimen>
    <dimen name="main_category_phone_space_progress_radius">8dp</dimen>
    <dimen name="main_category_browser_min_height">60dp</dimen>
    <dimen name="main_category_browser_single_line_min_height">48dp</dimen>
    <dimen name="main_category_type_title_min_height">28dp</dimen>
    <dimen name="main_category_sub_desc_min_height">17dp</dimen>
    <dimen name="main_category_sub_desc_margin_top">3dp</dimen>
    <dimen name="main_category_item_min_height">68dp</dimen>
    <dimen name="main_category_item_min_width">50dp</dimen>
    <dimen name="main_category_app_item_min_height">61dp</dimen>
    <dimen name="main_category_app_item_min_width">72dp</dimen>
    <dimen name="main_category_app_item_h_margin">3dp</dimen>
    <dimen name="main_category_app_parent_padding_left">27dp</dimen>
    <dimen name="main_category_item_min_margin_top">12dp</dimen>
    <dimen name="main_category_app_item_background_radius">10dp</dimen>
    <dimen name="main_category_item_title_width">50dp</dimen>
    <dimen name="main_category_phone_space_min_height">80dp</dimen>
    <dimen name="main_category_phone_space_desc_max_width">200dp</dimen>
    <dimen name="main_category_phone_space_clean_max_width">100dp</dimen>
    <dimen name="main_category_horizontal_padding">30dp</dimen>
    <dimen name="main_recent_footer_vertical_padding">30dp</dimen>
    <dimen name="main_recent_header_margin_top">13dp</dimen>
    <dimen name="main_storage_position_min_height">20dp</dimen>
    <dimen name="main_storage_min_height">65dp</dimen>
    <dimen name="main_category_item_sub">3dp</dimen>
    <dimen name="main_category_item_icon_size_width">44dp</dimen>
    <dimen name="main_category_item_icon_size_height">44dp</dimen>
    <dimen name="main_tab_height">42dp</dimen>
    <dimen name="main_larger_title">22dp</dimen>
    <dimen name="recent_icon_size">24dp</dimen>
    <dimen name="recent_single_img_width">154dp</dimen>
    <dimen name="recent_single_img_height">100dp</dimen>
    <dimen name="recent_list_img_def_height">79dp</dimen>
    <dimen name="recent_folder_title_height">28dp</dimen>
    <dimen name="recent_img_list_top_margin">4dp</dimen>
    <dimen name="recent_file_list_top_margin">6dp</dimen>
    <dimen name="recent_file_list_bottom_margin">32dp</dimen>
    <dimen name="recent_list_item_expand_height">36dp</dimen>
    <dimen name="recent_list_bottom_tip">12sp</dimen>
    <dimen name="recent_folder_name_max_witdh">150dp</dimen>
    <dimen name="main_recent_img_size_width">74dp</dimen>
    <dimen name="main_recent_img_size_height">75dp</dimen>
    <dimen name="recent_list_title_vertical_padding">8dp</dimen>
    <dimen name="recent_list_child_title_size">14sp</dimen>
    <dimen name="recent_list_child_title_ver_padding">8dp</dimen>
    <dimen name="recent_child_content_title_margin">10dp</dimen>
    <dimen name="recent_list_ver_padding">20dp</dimen>
    <dimen name="recent_list_show_all_default_height">36dp</dimen>
    <dimen name="recent_file_item_height">65dp</dimen>
    <dimen name="recent_expand_item_margin_top">8dp</dimen>
    <dimen name="recent_expand_item_text_size">14sp</dimen>
    <dimen name="recent_group_divider_margin_top">30dp</dimen>
    <dimen name="recent_group_div_height">30dp</dimen>
    <dimen name="recent_file_item_bottm_padding">12dp</dimen>
    <dimen name="recent_file_item_tiltle_min_width">60dp</dimen>
    <dimen name="recnet_file_item_text_pading">1dp</dimen>
    <dimen name="recent_multimg_item_width">79dp</dimen>
    <dimen name="recent_group_div_line_height">0.7dp</dimen>
    <dimen name="recent_group_div_hight">41dp</dimen>
    <dimen name="recent_list_file_icon_height">40dp</dimen>
    <dimen name="main_app_Layout_translate_y">54dp</dimen>
    <dimen name="main_app_layout_expand_height">116dp</dimen>
    <dimen name="default_status_and_tool_bar_height">92dp</dimen>
    <dimen name="toolbar_margin_top">12dp</dimen>
    <dimen name="appbar_listview_padding_top">16dp</dimen>
    <dimen name="default_title_below_toolbar_height">68dp</dimen>
    <dimen name="main_menu_text_size">16sp</dimen>
    <dimen name="main_category_item_clicked_radius">50dp</dimen>
    <dimen name="main_menu_sub_text_size">12sp</dimen>
    <dimen name="splash_text_size">24sp</dimen>
    <dimen name="default_large_title_size">40dp</dimen>
    <!-- ripple -->
    <dimen name="color_text_ripple_bg_radius">5dp</dimen>
    <dimen name="color_text_ripple_bg_padding_horizontal">8dp</dimen>
    <dimen name="color_text_ripple_bg_padding_vertical">4dp</dimen>
    <dimen name="recent_title_item_height">36dp</dimen>
    <dimen name="title_alpha_rang_min_count_height">25dp</dimen>
    <dimen name="title_margin_top_change_offset">40dp</dimen>
    <dimen name="standard_scroll_height">100dp</dimen>

    <dimen name="main_category_app_item_text_size">12sp</dimen>
    <dimen name="main_category_app_bg_radius">4.67dp</dimen>
    <dimen name="main_category_super_img_size">40dp</dimen>
    <dimen name="main_category_super_img_padding">10dp</dimen>
    <dimen name="main_category_viewpager_padding_top">137.33dp</dimen>
    <dimen name="font_line_space_1">1dp</dimen>
    <dimen name="font_line_space_2">2dp</dimen>
    <dimen name="font_line_space_0">0sp</dimen>
    <dimen name="navigation_min_height">68dp</dimen>

    <dimen name="width_640">640dp</dimen>
    <dimen name="height_360">360dp</dimen>
    <dimen name="bottom_tab_navigation_padding_bottom">16dp</dimen>
    <dimen name="main_category_item_title_size_height">14dp</dimen>
    <dimen name="main_tool_bar_padding">175dp</dimen>
    <dimen name="default_margin_narrow">20dp</dimen>
    <dimen name="action_file_browser_padding_start">24dp</dimen>
    <dimen name="category_super_title_margin_top">8.3dp</dimen>
    <dimen name="main_supper_app_item_margin">12dp</dimen>

    <dimen name="main_category_title_text_size">14sp</dimen>
    <dimen name="main_phone_storage_width">194dp</dimen>
    <dimen name="main_other_storage_width">180dp</dimen>
    <dimen name="main_otg_and_sd_storage_width">185dp</dimen>
    <dimen name="main_remote_device_width">126dp</dimen>
    <dimen name="main_cloud_disk_storage_width">126dp</dimen>
    <dimen name="main_private_safe_width">126dp</dimen>
    <dimen name="main_cloud_disk_storage_min_width">80dp</dimen>
    <dimen name="main_storage_height">152dp</dimen>
    <dimen name="appbar_title_expanded_height">119dp</dimen>
    <dimen name="appbar_title_expanded_margin_start">24dp</dimen>
    <dimen name="appbar_title_expanded_margin_end">24dp</dimen>
    <dimen name="appbar_title_expanded_margin_top">56dp</dimen>
    <dimen name="appbar_title_expanded_margin_bottom">0dp</dimen>
    <dimen name="appbar_title_toolbar_height">57dp</dimen>
    <dimen name="coui_appbar_margin_top">0dp</dimen>

    <dimen name="main_card_margin">10dp</dimen>

    <dimen name="file_label_card_list_item_height">122dp</dimen>
    <dimen name="searchview_height">50dp</dimen>
    <dimen name="file_label_file_grid_height">54dp</dimen>
    <dimen name="file_label_file_grid_width">68dp</dimen>
    <dimen name="search_view_height">52dp</dimen>
    <dimen name="coui_appbar_title_expanded_with_search_height">109dp</dimen>
    <dimen name="coui_appbar_title_expanded_with_search_margin_bottom">12dp</dimen>
    <dimen name="main_twopanel_second_size">384dp</dimen>

    <dimen name="parent_min_width">280dp</dimen>
    <dimen name="parent_max_width">360dp</dimen>
    <dimen name="searchview_top_margin">-50dp</dimen>
    <dimen name="parent_label_item_height">52dp</dimen>

    <dimen name="storage_space_desc_margin_bottom">6dp</dimen>

    <!-- SearchView Bar -->
    <dimen name="search_height_range_min_height">50dp</dimen>
    <dimen name="search_alpha_range_min_count_height">30dp</dimen>
    <dimen name="search_width_range_min_count_height">75dp</dimen>
    <dimen name="with_search_title_margin_range_min_height">90dp</dimen>
    <dimen name="toolbar_title_alpha_range_max_count_height">25dp</dimen>
    <dimen name="list_to_ex_top_padding">12dp</dimen>
    <dimen name="list_to_ex_bottom_padding">32dp</dimen>
    <dimen name="list_item_left_padding">16dp</dimen>
    <dimen name="list_item_right_padding">16dp</dimen>

    <dimen name="expandable_list_item_size_width">16dp</dimen>
    <dimen name="expandable_list_item_size">24dp</dimen>
    <dimen name="coui_appbar_title_expanded_with_search_margin_top">54dp</dimen>

    <dimen name="oplus_doc_sidebar_icon_expanded_margin_start_medium">12dp</dimen>
    <dimen name="oplus_doc_sidebar_list_item_gap">8dp</dimen>
    <dimen name="oplus_doc_sidebar_list_item_margin">12dp</dimen>
    <dimen name="oplus_doc_sidebar_list_item_title_min_width">52dp</dimen>
    <dimen name="oplus_doc_sidebar_list_item_wrapper_corner_radius">12dp</dimen>
    <dimen name="oplus_doc_sidebar_list_item_wrapper_padding_horizontal">12dp</dimen>

    <dimen name="main_list_drag_view_normal_margin_start">-8dp</dimen>
    <dimen name="main_list_drag_view_edit_margin_top">-6dp</dimen>
    <dimen name="main_list_drag_view_edit_margin_start">32dp</dimen>
    <dimen name="main_list_delete_view_normal_margin_end">-24dp</dimen>
    <dimen name="main_list_switch_view_normal_margin_end">-38dp</dimen>
    <dimen name="main_list_delete_view_edit_margin_end">16dp</dimen>

    <dimen name="coui_sidebar_list_item_widget_start_margin_end">-12dp</dimen>
    <dimen name="coui_sidebar_list_item_widget_end_margin_end">12dp</dimen>
    <dimen name="coui_sidebar_list_item_icon_start_margin_start">12dp</dimen>
    <dimen name="coui_sidebar_list_item_icon_end_margin_start">44dp</dimen>

    <dimen name="toolbar_title_init_height">46dp</dimen>
    <dimen name="line_width_range_count_height">25dp</dimen>
    <dimen name="line_alpha_range_change_offset">10dp</dimen>
    <dimen name="divider_background_height">0.33dp</dimen>
    <dimen name="toolbar_title_collapse_margin_top">-39dp</dimen>
    <dimen name="toolbar_title_in_center_collapse_margin_top">-46dp</dimen>
    <dimen name="toolbar_title_final_height">26dp</dimen>
    <dimen name="toolbar_title_init_margin_bottom">12dp</dimen>
    <dimen name="toolbar_title_final_margin_bottom">13dp</dimen>
    <dimen name="toolbar_title_init_text_size">32dp</dimen>
    <dimen name="toolbar_title_final_text_size">18dp</dimen>
    <integer name="toolbar_title_init_font_variation">350</integer>
    <integer name="toolbar_title_final_font_variation">750</integer>
    <dimen name="toolbar_title_edit_mode_margin">35dp</dimen>
    <dimen name="toolbar_title_width_diff">90dp</dimen>
    <dimen name="todo_toolbar_title_max_width">300dp</dimen>
    <dimen name="recyclerview_default_padding_top">122dp</dimen>
    <dimen name="recent_recyclerview_padding_top">110dp</dimen>

    <item name="holder_view_item_scale" type="dimen" format="float">1.1</item>
</resources>