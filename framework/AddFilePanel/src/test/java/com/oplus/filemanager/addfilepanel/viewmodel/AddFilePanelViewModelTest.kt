package com.oplus.filemanager.addfilepanel.viewmodel

import android.content.Context
import android.database.MatrixCursor
import android.provider.MediaStore
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.Observer
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTypeUtils
import com.oplus.filemanager.addfilepanel.bean.AddFileBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.rules.TestWatcher
import org.junit.runner.Description
import java.io.File

/**
 * AddFilePanelViewModel 的单元测试类
 * 用于测试文件添加面板视图模型的各种功能
 */
@ExperimentalCoroutinesApi
class AddFilePanelViewModelTest {

    // 用于确保LiveData更新立即执行的规则
    @get:Rule
    val instantTaskRule = InstantTaskExecutorRule()

    // 自定义的协程测试规则
    @get:Rule
    val coroutineRule = CoroutineTestRule()

    // 测试用的协程调度器
    private val testDispatcher = StandardTestDispatcher()
    // 被测的ViewModel实例
    private lateinit var viewModel: AddFilePanelViewModel
    // 模拟的Context对象
    private val mockContext: Context = mockk(relaxed = true)
    // 观察者列表，用于管理测试中的LiveData观察者
    private val observers = mutableListOf<Observer<*>>()

    /**
     * 测试前的准备工作
     * 1. 设置主调度器为测试调度器
     * 2. 初始化ViewModel
     * 3. 模拟相关工具类
     */
    @Before
    fun setup() {
        // 设置主调度器为测试调度器
        Dispatchers.setMain(testDispatcher)
        // 初始化被测ViewModel
        viewModel = AddFilePanelViewModel()
        // 模拟MimeTypeHelper工具类
        mockkObject(MimeTypeHelper.Companion)
        // 模拟FileTypeUtils工具类
        mockkObject(FileTypeUtils)
    }

    /**
     * 测试后的清理工作
     * 1. 移除所有观察者
     * 2. 重置主调度器
     * 3. 解除所有模拟对象
     */
    @After
    fun tearDown() {
        // 移除所有观察者
        observers.forEach { observer ->
            viewModel.mAllFiles.removeObserver(observer as Observer<MutableList<AddFileBean>?>)
            viewModel.mAllSelectFiles.removeObserver(observer)
        }
        observers.clear()
        // 重置主调度器
        Dispatchers.resetMain()
        // 解除所有模拟对象
        unmockkAll()
    }

    /**
     * 测试加载所有文件时处理空游标的情况
     * 验证当查询返回空游标时，ViewModel能正确处理并返回空列表
     */
    @Test
    fun `loadAllFiles should handle null cursor`() = runTest {
        // 模拟返回空游标
        every { mockContext.contentResolver.query(any(), any(), any(), any(), any()) } returns null

        // 设置观察者
        val observer = mockk<Observer<MutableList<AddFileBean>?>>(relaxed = true)
        observers.add(observer)
        viewModel.mAllFiles.observeForever(observer)

        // 执行测试
        viewModel.loadAllFiles(mockContext)
        testDispatcher.scheduler.advanceUntilIdle()

        // 验证返回空列表
        verify { observer.onChanged(mutableListOf<AddFileBean>()) }
    }

    /**
     * 测试更新选中文件列表的功能
     * 验证能正确过滤出选中的文件
     */
    @Test
    fun `updateAllSelectFiles should filter selected files`() {
        // 准备测试数据
        val fileList = mutableListOf(
            AddFileBean().apply { mFileID = 101L },
            AddFileBean().apply { mFileID = 102L },
            AddFileBean().apply { mFileID = 103L }
        )
        viewModel.mAllFiles.value = fileList

        // 执行测试：更新选中文件列表(选择ID为101和103的文件)
        viewModel.updateAllSelectFiles(setOf(101L, 103L))

        // 验证结果：选中的文件数量应为2，且包含正确的文件ID
        assertEquals(2, viewModel.mAllSelectFiles.value?.size)
        assertEquals(101L, viewModel.mAllSelectFiles.value?.get(0)?.mFileID)
        assertEquals(103L, viewModel.mAllSelectFiles.value?.get(1)?.mFileID)
    }

    /**
     * 测试更新选中文件列表时处理空选择集的情况
     * 验证当没有选中任何文件时，返回空列表
     */
    @Test
    fun `updateAllSelectFiles should handle empty selection`() {
        // 准备测试数据
        val fileList = mutableListOf(AddFileBean().apply { mFileID = 101L })
        viewModel.mAllFiles.value = fileList

        // 执行测试：传入空的选择集
        viewModel.updateAllSelectFiles(emptySet())

        // 验证结果：选中的文件列表应为空
        assertEquals(0, viewModel.mAllSelectFiles.value?.size)
    }

    /**
     * 测试更新选中文件列表时处理源数据为空的情况
     * 验证当源文件列表为空时，能正确处理
     */
    @Test
    fun `updateAllSelectFiles should handle null source`() {
        // 设置空源数据
        viewModel.mAllFiles.value = null

        // 执行测试：尝试从空源数据中筛选文件
        viewModel.updateAllSelectFiles(setOf(101L))

        // 验证结果：选中的文件列表应为空
        assertEquals(0, viewModel.mAllSelectFiles.value?.size)
    }

    /**
     * 测试销毁时清理数据的功能
     * 验证调用onDestroy后，LiveData数据被清空
     */
    @Test
    fun `onDestroy should clear live data`() {
        // 设置初始数据
        viewModel.mAllFiles.value = mutableListOf(AddFileBean())
        viewModel.mAllSelectFiles.value = mutableListOf(AddFileBean())

        // 执行测试：调用销毁方法
        viewModel.onDestroy()

        // 验证结果：两个LiveData的值都应为null
        assertNull(viewModel.mAllFiles.value)
        assertNull(viewModel.mAllSelectFiles.value)
    }
}

/**
 * 自定义的协程测试规则
 * 用于在测试开始和结束时设置和重置主调度器
 */
class CoroutineTestRule : TestWatcher() {
    /**
     * 测试开始时设置主调度器为测试调度器
     */
    override fun starting(description: Description) {
        super.starting(description)
        Dispatchers.setMain(StandardTestDispatcher())
    }

    /**
     * 测试结束时重置主调度器
     */
    override fun finished(description: Description) {
        super.finished(description)
        Dispatchers.resetMain()
    }
}