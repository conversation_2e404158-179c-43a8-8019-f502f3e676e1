<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_transparent"
    android:layoutDirection="locale"
    android:padding="@dimen/list_drag_shadow_item_layout_padding">

    <ImageView
        android:id="@+id/dragshadow_item_image_three"
        android:layout_width="@dimen/img_drag_shadow_one_height"
        android:layout_height="@dimen/img_drag_shadow_one_height"
        android:alpha="0.85"
        android:layout_marginTop="@dimen/drag_shadow_margin_top"
        android:layout_marginEnd="@dimen/drag_shadow_margin_end"
        android:background="@drawable/dragshadow_layout_background">
    </ImageView>

    <ImageView
        android:id="@+id/dragshadow_item_image_two"
        android:layout_width="@dimen/img_drag_shadow_one_height"
        android:layout_height="@dimen/img_drag_shadow_one_height"
        android:alpha="0.85"
        android:layout_marginTop="@dimen/drag_shadow_margin_top"
        android:layout_marginEnd="@dimen/drag_shadow_margin_end"
        android:background="@drawable/dragshadow_layout_background">
    </ImageView>

    <ImageView
        android:id="@+id/dragshadow_item_image_one"
        android:layout_width="@dimen/img_drag_shadow_one_height"
        android:layout_height="@dimen/img_drag_shadow_one_height"
        android:alpha="0.85"
        android:layout_marginTop="@dimen/drag_shadow_margin_top"
        android:layout_marginEnd="@dimen/drag_shadow_margin_end"
        android:background="@drawable/dragshadow_layout_background">
    </ImageView>

    <include layout="@layout/dragshadow_reddot_layout" />

</FrameLayout>