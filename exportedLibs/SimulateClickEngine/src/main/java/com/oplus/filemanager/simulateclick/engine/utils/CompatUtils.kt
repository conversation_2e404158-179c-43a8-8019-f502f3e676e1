/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CompatUtils
 ** Description : CompatUtils
 ** Version     : 1.0
 ** Date        : 2024/09/13 17:40
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/09/13     1.0      create
 ***********************************************************************/
package com.oplus.filemanager.simulateclick.engine.utils

import java.util.function.Supplier

object CompatUtils {

    /**
     * 根据Android R版本适配接口
     */
    @JvmStatic
    fun <R> compactSApi(newApi: Supplier<R>, oldApi: Supplier<R>): R {
        return if (SdkUtils.isAtLeastS()) {
            newApi.get()
        } else {
            oldApi.get()
        }
    }
}