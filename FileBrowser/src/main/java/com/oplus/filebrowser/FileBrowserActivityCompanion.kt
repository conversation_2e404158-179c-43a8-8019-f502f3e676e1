/*********************************************************************
 * * Copyright (C), 2010-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : FileBrowserActivityCompanion
 * * Version     : 1.0
 * * Date        : 2024/04/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser

import android.content.Intent
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.IntentUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ResourceUtils
import java.io.File

class FileBrowserActivityCompanion(private val activity: FileBrowserActivity) {

    companion object {
        private const val TAG = "FileBrowserActivityCompanion"

        private const val ILLEGAL_SYMBOL = "../"
    }

    fun getCurrentPath(intent: Intent): String {
        val currentPath = IntentUtils.getString(intent, KtConstants.CURRENT_DIR)
        Log.d(TAG, "currentPath = $currentPath")
        //如果传入的是空，就显示根目录
        if (currentPath.isNullOrEmpty()) {
            Log.i(TAG, "null path, use root path")
            return VolumeEnvironment.getInternalSdPath(MyApplication.appContext)
        }
        if (currentPath.contains(ILLEGAL_SYMBOL)) {
            Log.i(TAG, "Illegal characters, use root path")
            return VolumeEnvironment.getInternalSdPath(MyApplication.appContext)
        }

        if (HiddenFileHelper.mIsNeedShowHiddenFile == false
            && HiddenFileHelper.isHiddenFileByPath(currentPath)
        ) {
            Log.i(TAG, "not allow show hide dir, use root path")
            return VolumeEnvironment.getInternalSdPath(MyApplication.appContext)
        }

        val currentFile = file(currentPath)
        //如果传入文件或者文件夹不存在，则也显示根目录
        return if (currentFile.exists()) {
            //如果是
            if (currentFile.isDirectory) {
                Log.d(TAG, "currentPath is isDirectory, just return self")
                formatFilePath(currentFile.absolutePath)
            } else {
                val formatParentPath = currentFile.parent?.let { formatFilePath(it) }
                Log.d(TAG, "currentPath is file, parent = $formatParentPath")
                formatParentPath ?: VolumeEnvironment.getInternalSdPath(MyApplication.appContext)
            }
        } else {
            Log.e(TAG, "file not exists, back root path")
            VolumeEnvironment.getInternalSdPath(MyApplication.appContext)
        }
    }

    private fun file(currentPath: String): File {
        return File(currentPath)
    }

    @VisibleForTesting
    fun formatFilePath(filePath: String): String {
        val separator = File.separator.toRegex()
        if (filePath.startsWith("storage${separator}emulated$separator")) {
            return "${File.separator.toRegex()}$filePath"
        }
        return filePath
    }

    fun getTitle(intent: Intent): String {
        val resId = IntentUtils.getInt(intent, Constants.TITLE_RES_ID, -1)
        var title = if (resId > 0) {
            ResourceUtils.getString(activity, resId)
        } else {
            IntentUtils.getString(intent, Constants.TITLE)
        }
        if (title.isNullOrEmpty()) {
            title = activity.resources.getString(com.filemanager.common.R.string.string_all_files)
        }
        return title
    }
}