/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.cardwidget.label.packer
 * * Description : 无文件标签卡片显示，显示标签名称，点击跳转到文管所属标签页面
 * * Version     : 1.0
 * * Date        : 2022/12/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.packer

import android.content.Context
import com.oplus.cardwidget.domain.pack.BaseDataPack
import com.oplus.filemanager.cardwidget.label.data.LabelCardData
import com.oplus.filemanager.cardwidget.util.StartActivityUtil
import com.oplus.filemanager.cardwidget.util.TextUtil
import com.oplus.smartenginehelper.dsl.DSLCoder

class EmptyFilesPacker(
    private val context: Context,
    private val labelCard: LabelCardData
) : BaseDataPack() {


    companion object {
        private const val LABEL_NAME = "label_name"
        private const val LABEL_NAME_ICON = "file_name_icon"

        private const val TO_ADD = "tips_go_to_add"
        private const val NO_FILE = "no_file_tip_tv"
        private const val CONTAINER_ID = "container"
    }

    override fun onPack(coder: DSLCoder): Boolean {
        coder.setOnClick(LABEL_NAME, StartActivityUtil().startLabelListShowAddFileDialog(labelCard.labelName, labelCard.labelId))
        coder.setOnClick(LABEL_NAME_ICON, StartActivityUtil().startLabelListShowAddFileDialog(labelCard.labelName, labelCard.labelId))
        coder.setOnClick(TO_ADD, StartActivityUtil().startLabelListShowAddFileDialog(labelCard.labelName, labelCard.labelId))
        coder.setOnClick(NO_FILE, StartActivityUtil().startLabelListShowAddFileDialog(labelCard.labelName, labelCard.labelId))
        coder.setOnClick(CONTAINER_ID, StartActivityUtil().startLabelListShowAddFileDialog(labelCard.labelName, labelCard.labelId))
        coder.setTextViewText(LABEL_NAME, TextUtil.getLabelNameForLabelCard(labelCard.labelName))
        TextUtil.setTextSize(coder, context, LABEL_NAME, TextUtil.TEXT_SIZE_14)
        TextUtil.setTextSize(coder, context, TO_ADD, TextUtil.TEXT_SIZE_14)
        TextUtil.setTextSize(coder, context, NO_FILE, TextUtil.TEXT_SIZE_16)
        return true
    }
}