/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : QQIntentProcess.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.process

import android.content.ComponentName
import android.content.Intent
import android.os.BadParcelableException
import android.os.Build
import android.os.Bundle
import android.util.Log
import com.filemanager.common.constants.ThirdAppConstants.QQ_PACKAGE
import com.tencent.mobileqq.filemanager.data.ForwardFileInfo
import java.lang.ClassCastException

class QQIntentProcess : DefaultIntentProcess() {

    companion object {
        const val TAG = "QQIntentProcess"

        const val EXTRA_FILE_INFO_ENTITY = "fileinfo"
    }

    override fun getKeyDataBeanFromIntent(inputIntent: Intent): IViewExactProcess.KeyDataBean {
        //后续调试QQ的时候需要重写这个方法
        val keyInfo = getKeyInfoFromIntent(inputIntent)
        val uid = getUidFromIntent(keyInfo)
        val fileName = getFileNameFromIntent(keyInfo)
        val fileSize = getFileSizeFromIntent(keyInfo)
        val result = IViewExactProcess.KeyDataBean()
        result.uid = uid
        result.fileName = fileName
        result.fileSize = IViewExactProcess.SizeValues(1, fileSize.toInt(), 0, 0)
        Log.i(TAG, "getKeyDataBeanFromIntent intent $inputIntent, result $result")
        return result
    }

    private fun getKeyInfoFromIntent(inputIntent: Intent): KeyInfoFromIntent {
        val intentData = inputIntent.data
        val extras = inputIntent.extras
        val sb = StringBuilder()
        val result = KeyInfoFromIntent()
        result.intentComponent =
            inputIntent.component ?: ComponentName(QQ_PACKAGE, "")
        if (extras != null) {
            extras.classLoader = ForwardFileInfo::class.java.classLoader
            val extrasKeys = extras.keySet()
            Log.i(TAG, "getUidFromIntent start extra keys ${extrasKeys.toList()}")
            if (extrasKeys != null) {
                for (key in extrasKeys) {
                    try {
                        extractedOtherField(key, extras, sb, result)
                    } catch (e: BadParcelableException) {
                        Log.e(TAG, "getEntityFromIntent error", e)
                    } catch (e: ClassCastException) {
                        Log.e(TAG, "getEntityFromIntent error", e)
                    }
                }
            }
        }
        Log.i(
            TAG,
            "getUidFromIntent end itent: $inputIntent intentData $intentData, extras: $extras, sb $sb, result: $result"
        )
        return result
    }

    private fun extractedOtherField(
        key: String?,
        extras: Bundle,
        sb: StringBuilder,
        result: KeyInfoFromIntent
    ) {
        if (EXTRA_FILE_INFO_ENTITY.contentEquals(key, true)) {
            val entity = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                extras.getParcelable(key, ForwardFileInfo::class.java)
            } else {
                extras.getParcelable(key)
            }
            if (entity != null) {
                sb.append("\n")
                result.fileName = entity.fileName
                result.fileSize = entity.size
                sb.append(entity.toString())
                sb.append("\n")
            }
        }
    }

    private fun getUidFromIntent(keyInfo: KeyInfoFromIntent): String {
        val result = keyInfo.intentComponent.toShortString() + "_fileName:" + keyInfo.fileName
        Log.i(TAG, "getUidFromIntent no real file, no cloud File, generate uuid $result as uid")
        return result
    }


    private fun getFileNameFromIntent(keyInfo: KeyInfoFromIntent): String {
        val result = keyInfo.fileName
        Log.i(TAG, "getFileNameFromIntent $result")
        return result
    }

    private fun getFileSizeFromIntent(keyInfo: KeyInfoFromIntent): Long {
        val result = keyInfo.fileSize
        Log.i(TAG, "getFileSizeFromIntent $result")
        return result
    }

    data class KeyInfoFromIntent(
        var intentComponent: ComponentName = ComponentName("", ""),
        var fileName: String = "",
        var fileExt: String = "",
        ///data/user/0/com.tencent.mm/MicroMsg/b6f4067484c9ace15403af66a35c771e/attachment/1_测试使用的.pptx
        var filePath: String = "",
        var fileSize: Long = -1L
    )
}