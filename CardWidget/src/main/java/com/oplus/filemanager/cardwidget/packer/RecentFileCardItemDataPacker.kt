/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentFileItemDataPacker
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/9
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/9      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.packer

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.annotation.VisibleForTesting
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.KtUtils
import com.oplus.cardwidget.domain.pack.BaseDataPack
import com.oplus.cardwidget.util.getCardType
import com.oplus.filemanager.cardwidget.R
import com.oplus.filemanager.cardwidget.activity.CardWidgetOpenFileActivity.Companion.INDEX
import com.oplus.filemanager.cardwidget.data.RecentFileCardData
import com.oplus.filemanager.cardwidget.data.RecentFileCardData.Companion.UI_STATE_LOADING_EMPTY
import com.oplus.filemanager.cardwidget.data.RecentFileCardData.Companion.UI_STATE_LOADING_SUCCESS
import com.oplus.filemanager.cardwidget.data.RecentFileCardData.Companion.UI_STATE_NO_PRIVACY
import com.oplus.filemanager.cardwidget.data.RecentFileCardData.Companion.UI_STATE_NO_STORAGE_PERMISSION
import com.oplus.filemanager.cardwidget.data.RecentFileCardItemData
import com.oplus.filemanager.cardwidget.ext.isSupportDuration
import com.oplus.filemanager.cardwidget.ext.writeFileProvider
import com.oplus.filemanager.cardwidget.util.ANDROID_LAUNCHER_PKG
import com.oplus.filemanager.cardwidget.util.ASSISTANT_SCREEN_PKG
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_2_COLOROS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_2_ONEPLUS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_4_COLOROS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_2_TO_4_ONEPLUS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_4_TO_4_COLOROS
import com.oplus.filemanager.cardwidget.util.CARD_TYPE_4_TO_4_ONEPLUS
import com.oplus.filemanager.cardwidget.util.CardWidgetThumbnailGenerator
import com.oplus.filemanager.cardwidget.util.OPEN_FILE_ACTION
import com.oplus.filemanager.cardwidget.util.OPPO_LAUNCHER_PKG
import com.oplus.filemanager.cardwidget.util.START_FILE_MANAGER_ACTION
import com.oplus.filemanager.cardwidget.util.isSupportThumbnailForCardWidget
import com.oplus.filemanager.cardwidget.util.ruleForThumbnailFileName
import com.oplus.smartenginehelper.dsl.DSLCoder
import com.oplus.smartenginehelper.entity.StartActivityClickEntity
import java.io.File

class RecentFileCardItemDataPacker(
    @get:VisibleForTesting val recentFile: RecentFileCardData,
    private val widgetCode: String,
    private val context: Context
) :
    BaseDataPack() {

    companion object {

        const val ID_PLACE_HOLDER = "#"
        const val ITEM_PARENT_ID = "item_#_container"
        const val ITEM_ICON_ID = "item_#_file_icon"
        const val ITEM_TEXT_ID = "item_#_file_name_text"
        const val ITEM_DURATION_ID = "file_#_duration_tv"

        const val NO_PERMISSION_TIP_TV_ID = "no_permission_tip_tv"
        const val NO_PERMISSION_TIP_CONTAINER = "no_permission_tip_container"
        const val NO_RECENT_FILE_TIP_TV_ID = "no_recent_file_tip_tv"
        const val NO_RECENT_FILE_TIP_CONTAINER = "no_recent_file_tip_container"
        const val LOADING_DRAWABLE_2_TO_2 = "@drawable/ic_loading_2_to_2"
        const val LOADING_DRAWABLE_2_TO_4 = "@drawable/ic_loading_2_to_4"
        const val LOADING_DRAWABLE_4_TO_4 = "@drawable/ic_loading_4_to_4"
        const val NO_DATA_DRAWABLE_2_TO_2 = "@drawable/no_recent_files_2_to_2"
        const val NO_DATA_DRAWABLE_2_TO_4 = "@drawable/no_recent_files_2_to_4"
        const val NO_DATA_DRAWABLE_4_TO_4 = "@drawable/no_recent_files_4_to_4"
        const val NO_PERMISSION_TIP_RES_ID = "@string/no_permission_tip_text"
        const val SECOND = 1000L
        const val TEXT_SIZE = "textSize"
        const val PX = "px"
    }

    override fun onPack(coder: DSLCoder): Boolean {
        when (recentFile.uiState) {
            UI_STATE_NO_STORAGE_PERMISSION, UI_STATE_NO_PRIVACY, UI_STATE_LOADING_EMPTY -> showNoPermissionOrEmptyDataUi(
                coder,
                recentFile.uiState
            )
            UI_STATE_LOADING_SUCCESS -> showLoadSuccessUi(coder)
        }
        return true
    }

    private fun showNoPermissionOrEmptyDataUi(coder: DSLCoder, uiState: Int) {
        val start = startFileManagerMainActivity()
        if (uiState == UI_STATE_NO_STORAGE_PERMISSION || uiState == UI_STATE_NO_PRIVACY) {
            coder.setCustomData(
                NO_PERMISSION_TIP_TV_ID, TEXT_SIZE,
                getTextSizeLargestToG2String(com.filemanager.common.R.dimen.font_size_12, context)
            )
            coder.setTextViewText(NO_PERMISSION_TIP_TV_ID, NO_PERMISSION_TIP_RES_ID)
            coder.setOnClick(NO_PERMISSION_TIP_CONTAINER, start)
        } else if (uiState == UI_STATE_LOADING_EMPTY) {
            coder.setCustomData(
                NO_RECENT_FILE_TIP_TV_ID, TEXT_SIZE,
                getTextSizeLargestToG2String(com.filemanager.common.R.dimen.font_size_12, context)
            )
            coder.setOnClick(NO_RECENT_FILE_TIP_CONTAINER, start)
        }
    }

    private fun showLoadSuccessUi(coder: DSLCoder) {
        recentFile.recentFiles.forEachIndexed { index, recentFileCardItemData ->
            showFileBackground(coder, ITEM_PARENT_ID.replace(ID_PLACE_HOLDER, index.toString()), recentFileCardItemData)
            showFileName(coder, ITEM_TEXT_ID.replace(ID_PLACE_HOLDER, index.toString()), recentFileCardItemData)
            showFileIcon(coder, ITEM_ICON_ID.replace(ID_PLACE_HOLDER, index.toString()), recentFileCardItemData)
            showDurationIfNeed(coder, ITEM_DURATION_ID.replace(ID_PLACE_HOLDER, index.toString()), recentFileCardItemData)
            setClickEventForRecentItem(coder, ITEM_PARENT_ID.replace(ID_PLACE_HOLDER, index.toString()), index)
        }
    }

    @VisibleForTesting
    fun getNoDataDrawable(cardType: Int): String {
        return when (cardType) {
            CARD_TYPE_2_TO_2_COLOROS, CARD_TYPE_2_TO_2_ONEPLUS -> NO_DATA_DRAWABLE_2_TO_2
            CARD_TYPE_2_TO_4_COLOROS, CARD_TYPE_2_TO_4_ONEPLUS -> NO_DATA_DRAWABLE_2_TO_4
            CARD_TYPE_4_TO_4_COLOROS, CARD_TYPE_4_TO_4_ONEPLUS -> NO_DATA_DRAWABLE_4_TO_4
            else -> NO_DATA_DRAWABLE_2_TO_2
        }
    }

    @VisibleForTesting
    fun getLoadingDrawable(cardType: Int): String {
        return when (cardType) {
            CARD_TYPE_2_TO_2_COLOROS, CARD_TYPE_2_TO_2_ONEPLUS -> LOADING_DRAWABLE_2_TO_2
            CARD_TYPE_2_TO_4_COLOROS, CARD_TYPE_2_TO_4_ONEPLUS -> LOADING_DRAWABLE_2_TO_4
            CARD_TYPE_4_TO_4_COLOROS, CARD_TYPE_4_TO_4_ONEPLUS -> LOADING_DRAWABLE_4_TO_4
            else -> LOADING_DRAWABLE_2_TO_2
        }
    }

    private fun showFileBackground(coder: DSLCoder, viewId: String, data: RecentFileCardItemData) {
        val type = widgetCode.getCardType()
        if (type == CARD_TYPE_4_TO_4_COLOROS || type == CARD_TYPE_4_TO_4_ONEPLUS) {
            return
        }
        coder.setBackground(viewId, data.bgResId)
    }

    private fun showFileName(coder: DSLCoder, viewId: String, data: RecentFileCardItemData) {
        coder.setCustomData(viewId, TEXT_SIZE, getTextSizeLargestToG2String(com.filemanager.common.R.dimen.font_size_12, context))
        coder.setTextViewText(viewId, data.name)
    }

    private fun showFileIcon(coder: DSLCoder, viewId: String, data: RecentFileCardItemData) {
        if (isSupportThumbnailForCardWidget(data.type)) {
            CardWidgetThumbnailGenerator.generate(data.type, data.path, data.dateModified, data.size)
            val file = File(ruleForThumbnailFileName(data.path, data.dateModified, data.size))
            if (file.exists() && file.length() > 0) {
                val uri = writeFileProvider(context, file, arrayOf(ASSISTANT_SCREEN_PKG, ANDROID_LAUNCHER_PKG, OPPO_LAUNCHER_PKG))
                coder.setImageViewResource(viewId, uri.toString())
            } else {
                coder.setImageViewResource(viewId, data.iconResId)
            }
        } else {
            coder.setImageViewResource(viewId, data.iconResId)
        }
    }

    private fun showDurationIfNeed(coder: DSLCoder, viewId: String, data: RecentFileCardItemData) {
        if (data.isSupportDuration()) {
            coder.setVisibility(viewId, View.VISIBLE)
            coder.setTextViewText(viewId, KtUtils.formatVideoTime(data.duration / SECOND))
        } else {
            coder.setVisibility(viewId, View.GONE)
        }
    }

    private fun startFileManagerMainActivity(): StartActivityClickEntity {
        val start = StartActivityClickEntity()
        start.setPackageName(MyApplication.sAppContext.packageName)
        start.setAction(START_FILE_MANAGER_ACTION)
        start.setCategory(Intent.CATEGORY_DEFAULT)
        start.addFlag(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        return start
    }

    private fun setClickEventForRecentItem(coder: DSLCoder, viewId: String, index: Int) {
        val start = StartActivityClickEntity()
        start.setPackageName(MyApplication.sAppContext.packageName)
        start.setAction(OPEN_FILE_ACTION)
        start.setCategory(Intent.CATEGORY_DEFAULT)
        start.setParams(INDEX, index.toString())
        start.addFlag(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        coder.setOnClick(viewId, start)
    }

    @VisibleForTesting
    fun getTextSizeLargestToG2String(dimenId: Int, context: Context?): String {
        val size: Float = getTextSizeLargest(dimenId, context, COUIChangeTextUtil.G2)
        return size.toString() + PX
    }

    @VisibleForTesting
    fun getTextSizeLargest(dimenId: Int, context: Context?, textLevel: Int): Float {
        var suitableFontSize = -1f
        if (context == null) {
            return suitableFontSize
        }
        val resources = context.resources
        val fontScale: Float
        val numTextSize: Float
        if (resources != null) {
            fontScale = resources.configuration.fontScale
            numTextSize = resources.getDimensionPixelSize(dimenId).toFloat()
            suitableFontSize = COUIChangeTextUtil.getSuitableFontSize(numTextSize, fontScale, textLevel)
        }
        return suitableFontSize
    }
}