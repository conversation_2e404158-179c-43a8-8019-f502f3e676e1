/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - DocThumbnailLoaderLifecycle.kt
 * Description:
 *     The lifecycle callback for doc thumbnail loader.
 *
 * Version: 1.0
 * Date: 2024-07-24
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON>ia<PERSON>.<EMAIL>    2024-07-24   1.0    Create this module
 *********************************************************************************/
package com.filemanager.thumbnail.doc

import android.app.Activity
import android.app.Application
import android.os.Bundle
import androidx.annotation.VisibleForTesting
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

internal object DocThumbnailLoaderLifecycle : Application.ActivityLifecycleCallbacks {

    @VisibleForTesting
    internal var activeCount = 0

    @Volatile
    private var initialized = false

    @JvmStatic
    fun init(application: Application) {
        if (initialized) {
            return
        }
        initialized = true
        activeCount = 0
        application.registerActivityLifecycleCallbacks(this)
    }

    override fun onActivityCreated(activity: Activity, outState: Bundle?) {
        activeCount++
    }

    override fun onActivityStarted(activity: Activity) {
        // Do nothing
    }

    override fun onActivityResumed(activity: Activity) {
        // Do nothing
    }

    override fun onActivityPaused(activity: Activity) {
        // Do nothing
    }

    override fun onActivityStopped(activity: Activity) {
        // Do nothing
    }

    override fun onActivitySaveInstanceState(activity: Activity, savedInstanceState: Bundle) {
        // Do nothing
    }

    override fun onActivityDestroyed(activity: Activity) {
        activeCount--
        if (activeCount > 0) {
            return
        }
        asyncExecute {
            // Release doc thumbnail loader if activity is destroyed.
            DocThumbnailLoaderFactory.release()
        }
    }

    @VisibleForTesting
    @JvmStatic
    internal fun asyncExecute(block: () -> Unit) {
        CoroutineScope(Dispatchers.Default).launch {
            block()
        }
    }
}