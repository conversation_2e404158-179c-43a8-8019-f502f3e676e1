package com.oplus.filemanager.preview.audio

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewContainerManager
import com.oplus.filemanager.preview.utils.PreviewScrollAreaHelper
import com.oplus.filemanager.preview.widget.PreviewFileIntroduceView
import com.oplus.filemanager.preview.widget.PreviewScrollView
import com.oplus.filemanager.preview.widget.PreviewOperationsBar
import io.mockk.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * AudioPreviewContainerManager的单元测试类
 * 用于测试音频预览容器管理器的各项功能
 */
class AudioPreviewContainerManagerTest {

    // 根视图
    private lateinit var rootView: View
    // 被测对象 - 音频预览容器管理器
    private lateinit var manager: AudioPreviewContainerManager

    // 依赖项的mock对象
    private lateinit var mockDefaultContainer: PreviewFileIntroduceView  // 默认容器视图
    private lateinit var mockDateTv: AppCompatTextView  // 日期文本视图
    private lateinit var mockSizeTv: AppCompatTextView  // 大小文本视图
    private lateinit var mockNameContainer: TextViewSnippet  // 名称容器
    private lateinit var mockAudioCard: View  // 音频播放卡片
    private lateinit var mockLoadingLayout: View  // 加载布局
    private lateinit var mockScrollAreaHelper: PreviewScrollAreaHelper  // 滚动区域帮助类
    private lateinit var mockContext: Context  // 上下文
    private lateinit var mockScrollView: PreviewScrollView  // 滚动视图
    private lateinit var mockOperationsBar: PreviewOperationsBar  // 操作栏

    /**
     * 测试前的初始化方法
     * 创建所有mock对象并设置初始状态
     */
    @Before
    fun setUp() {
        // 创建mock上下文
        mockContext = mockk(relaxed = true)
        
        // 创建mock视图对象
        mockDefaultContainer = mockk(relaxed = true)
        mockDateTv = mockk(relaxed = true)
        mockSizeTv = mockk(relaxed = true)
        mockNameContainer = mockk(relaxed = true)
        mockAudioCard = mockk(relaxed = true)
        mockLoadingLayout = mockk(relaxed = true)
        mockScrollView = mockk(relaxed = true)
        mockOperationsBar = mockk(relaxed = true)
        
        // 创建根视图mock并设置findViewById的返回值
        rootView = mockk(relaxed = true) {
            every { findViewById<PreviewFileIntroduceView>(R.id.preview_audio_def_info) } returns mockDefaultContainer
            every { findViewById<AppCompatTextView>(R.id.preview_remote_time_info) } returns mockDateTv
            every { findViewById<AppCompatTextView>(R.id.preview_remote_size_info) } returns mockSizeTv
            every { findViewById<TextViewSnippet>(R.id.preview_remote_title) } returns mockNameContainer
            every { findViewById<View>(R.id.preview_audio_play_card) } returns mockAudioCard
            every { findViewById<View>(R.id.loading_layout) } returns mockLoadingLayout
            every { findViewById<PreviewScrollView>(R.id.preview_audio_scroll_area) } returns mockScrollView
            every { findViewById<PreviewOperationsBar>(R.id.preview_operations_bar) } returns mockOperationsBar
        }

        // 创建mock滚动区域帮助类并设置方法行为
        mockScrollAreaHelper = mockk(relaxUnitFun = true) {
            every { hideDivider() } just Runs
            every { disableScrollViewportMinHeight() } just Runs
            every { enableScrollViewportMinHeight() } just Runs
            every { checkShowDivider() } just Runs
        }

        // 创建被测对象实例
        manager = AudioPreviewContainerManager(rootView)
        
        // 使用反射替换内部scrollAreaHelper为mock对象
        manager.javaClass.getDeclaredField("scrollAreaHelper").apply {
            isAccessible = true
            set(manager, mockScrollAreaHelper)
        }
    }

    /**
     * 测试后的清理方法
     * 清除所有mock状态
     */
    @After
    fun tearDown() {
        clearAllMocks()
    }

    /**
     * 测试构造函数是否正确初始化视图
     */
    @Test
    fun `constructor initializes views correctly`() {
        // 验证各视图是否被正确初始化
        assertNotNull(manager.defaultContainer)
        assertNotNull(manager.mDateTv)
        assertNotNull(manager.mSizeTv)
        assertNotNull(manager.nameContainer)
        // 验证视图是否与mock对象相同
        assertSame(mockDefaultContainer, manager.defaultContainer)
        assertSame(mockDateTv, manager.mDateTv)
        assertSame(mockSizeTv, manager.mSizeTv)
        assertSame(mockNameContainer, manager.nameContainer)
    }

    /**
     * 测试showAsLoading方法是否正确设置视图可见性和调用父类方法
     */
    @Test
    fun `showAsLoading sets correct visibility and calls super`() {
        manager.showAsLoading()

        // 验证是否调用了隐藏分割线
        verify(exactly = 1) { mockScrollAreaHelper.hideDivider() }
        // 验证音频卡片是否设置为不可见
        verify(exactly = 1) { mockAudioCard.visibility = View.INVISIBLE }
        // 验证默认容器是否设置为隐藏
        verify(exactly = 1) { mockDefaultContainer.visibility = View.GONE }
        // 验证加载布局是否设置为可见
        verify(exactly = 1) { mockLoadingLayout.visibility = View.VISIBLE }
        // 验证父类方法是否被调用(通过检查startLoadingTime)
        assertNotNull(manager.startLoadingTime)
    }

    /**
     * 测试showAsFileContainer在isDefault为true时的行为
     */
    @Test
    fun `showAsFileContainer with isDefault true shows default container`() {
        manager.showAsFileContainer(true)

        // 验证是否禁用了滚动视口最小高度
        verify(exactly = 1) { mockScrollAreaHelper.disableScrollViewportMinHeight() }
        // 验证是否隐藏了分割线
        verify(exactly = 1) { mockScrollAreaHelper.hideDivider() }
        // 验证默认容器是否可见
        verify(exactly = 1) { mockDefaultContainer.visibility = View.VISIBLE }
        // 验证音频卡片是否不可见
        verify(exactly = 1) { mockAudioCard.visibility = View.INVISIBLE }
        // 验证加载布局是否隐藏
        verify(exactly = 1) { mockLoadingLayout.visibility = View.GONE }
        // 验证父类方法是否被调用(通过检查startLoadingTime)
        assertNull(manager.startLoadingTime)
    }

    /**
     * 测试showAsFileContainer在isDefault为false时的行为
     */
    @Test
    fun `showAsFileContainer with isDefault false shows audio card`() {
        manager.showAsFileContainer(false)

        // 验证是否启用了滚动视口最小高度
        verify(exactly = 1) { mockScrollAreaHelper.enableScrollViewportMinHeight() }
        // 验证是否检查显示分割线
        verify(exactly = 1) { mockScrollAreaHelper.checkShowDivider() }
        // 验证默认容器是否隐藏
        verify(exactly = 1) { mockDefaultContainer.visibility = View.GONE }
        // 验证音频卡片是否可见
        verify(exactly = 1) { mockAudioCard.visibility = View.VISIBLE }
        // 验证加载布局是否隐藏
        verify(exactly = 1) { mockLoadingLayout.visibility = View.GONE }
        // 验证父类方法是否被调用(通过检查startLoadingTime)
        assertNull(manager.startLoadingTime)
    }

    /**
     * 测试onUpdateUIWhenConfigChange在音频卡片可见时的行为
     */
    @Test
    fun `onUpdateUIWhenConfigChange updates layout params when audioCard visible`() {
        // 设置mock布局参数
        val mockLayoutParams = mockk<ConstraintLayout.LayoutParams>(relaxed = true)
        every { mockAudioCard.layoutParams } returns mockLayoutParams
        every { mockAudioCard.visibility } returns View.VISIBLE
        every { mockAudioCard.resources.getDimensionPixelOffset(R.dimen.preview_audio_max_width) } returns 500

        manager.onUpdateUIWhenConfigChange(mutableListOf<IUIConfig>())

        // 验证是否检查显示分割线
        verify(exactly = 1) { mockScrollAreaHelper.checkShowDivider() }
    }

    /**
     * 测试onUpdateUIWhenConfigChange在布局参数不是ConstraintLayout时的行为
     */
    @Test
    fun `onUpdateUIWhenConfigChange does nothing when layout params not ConstraintLayout Params`() {
        // 设置非ConstraintLayout的布局参数
        val mockLayoutParams = mockk<LinearLayout.LayoutParams>(relaxed = true)
        every { mockAudioCard.layoutParams } returns mockLayoutParams
        every { mockAudioCard.visibility } returns View.VISIBLE

        manager.onUpdateUIWhenConfigChange(mutableListOf<IUIConfig>())

        // 验证没有调用检查显示分割线
        verify(exactly = 0) { mockScrollAreaHelper.checkShowDivider() }
    }

    /**
     * 测试onUpdateUIWhenConfigChange在音频卡片不可见时的行为
     */
    @Test
    fun `onUpdateUIWhenConfigChange does not call checkShowDivider when audioCard not visible`() {
        // 设置mock布局参数但音频卡片不可见
        val mockLayoutParams = mockk<ConstraintLayout.LayoutParams>(relaxed = true)
        every { mockAudioCard.layoutParams } returns mockLayoutParams
        every { mockAudioCard.visibility } returns View.GONE
        every { mockAudioCard.resources.getDimensionPixelOffset(R.dimen.preview_audio_max_width) } returns 500

        manager.onUpdateUIWhenConfigChange(mutableListOf<IUIConfig>())

        // 验证没有调用检查显示分割线
        verify(exactly = 0) { mockScrollAreaHelper.checkShowDivider() }
    }
}