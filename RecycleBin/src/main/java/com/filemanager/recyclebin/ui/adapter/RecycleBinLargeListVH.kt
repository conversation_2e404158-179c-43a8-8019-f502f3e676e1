/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RecycleBinLargeListVH
 * * Description : 最近删除的大屏的文件列表VH
 * * Version     : 1.0
 * * Date        : 2025/08/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.recyclebin.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.HiddenFileHelper.isNeedShowHiddenFile
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.view.TextViewSnippet
import com.filemanager.common.viewholder.FileBrowserLargeListVH
import com.filemanager.common.wrapper.RecycleFileWrapper
import com.filemanager.recyclebin.utils.RecycleBinUtils

class RecycleBinLargeListVH(convertView: View, imgRadius: Int = 0, isLabelFileList: Boolean = false) :
    FileBrowserLargeListVH(convertView, imgRadius, isLabelFileList) {


    companion object {

        @JvmStatic
        fun create(parent: ViewGroup, imgRadius: Int = 0, isLabelFileList: Boolean = false): RecycleBinLargeListVH {
            val context = parent.context
            val itemView = LayoutInflater.from(context).inflate(FileBrowserLargeListVH.getLayoutId(), parent, false)
            return RecycleBinLargeListVH(itemView, imgRadius, isLabelFileList).apply {
                showApkDetail = false
            }
        }
    }

    override fun setFileDetail(detailTv: TextView, anotherNameTv: TextViewSnippet, file: BaseFileBean) {
        if (file is RecycleFileWrapper) {
            detailTv.visibility = View.VISIBLE
            val recycleDate = RecycleBinUtils.getRecycleTimeLeftString(System.currentTimeMillis(), file.mRecycelDate)
            if (file.mIsDirectory) {
                val fileCount = JavaFileHelper.listFilesCount(file, isNeedShowHiddenFile().not())
                val countText = detailTv.context.resources.getQuantityString(R.plurals.text_x_items, fileCount, fileCount)
                detailTv.text = RecycleBinUtils.formatRecycleDetail(detailTv.context, listOf(recycleDate, countText))
            } else {
                detailTv.text = recycleDate
            }
        }
    }
}