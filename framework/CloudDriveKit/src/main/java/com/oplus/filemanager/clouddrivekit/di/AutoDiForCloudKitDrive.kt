/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDiForCloudKitDrive
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/3/11
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798        2024/1/8       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.clouddrivekit.di

import androidx.annotation.Keep
import com.oplus.filemanager.clouddrivekit.CloudDriveKitApi
import com.oplus.filemanager.interfaze.clouddrivekit.ICloudDriveKit
import org.koin.dsl.module

@Keep
class AutoDiForCloudKitDrive {

    val cloudDriveKitModule = module {
        single<ICloudDriveKit>(createdAtStart = true) {
            CloudDriveKitApi()
        }
    }
}