/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RulesTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/23
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/6/23      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.util

import com.filemanager.common.MyApplication
import com.filemanager.common.helper.MimeTypeHelper
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import java.io.File

class RulesTest {

    @Before
    fun setUp() {
        val file = File("/data/user/0/com.coloros.filemanager/")
        MyApplication.init(mockk(relaxed = true) {
            every { applicationContext }.returns(this)
            every { filesDir }.returns(file)
        })
        FOLDER = file.absolutePath + File.separator + "recent_card_widget"
    }

    @Ignore("test failed")
    @Test
    fun ruleForThumbnailFileNameTest() {
        Assert.assertEquals(RESULT, ruleForThumbnailFileName(FULL_PATH, DATE_MODIFIED, SIZE))
    }

    @Test
    fun isSupportThumbnailForCardWidgetTest() {
        Assert.assertEquals(true, isSupportThumbnailForCardWidget(MimeTypeHelper.IMAGE_TYPE))
        Assert.assertEquals(true, isSupportThumbnailForCardWidget(MimeTypeHelper.VIDEO_TYPE))
        Assert.assertEquals(true, isSupportThumbnailForCardWidget(MimeTypeHelper.AUDIO_TYPE))
        Assert.assertEquals(true, isSupportThumbnailForCardWidget(MimeTypeHelper.APPLICATION_TYPE))
        Assert.assertEquals(false, isSupportThumbnailForCardWidget(MimeTypeHelper.COMPRESSED_TYPE))
        Assert.assertEquals(false, isSupportThumbnailForCardWidget(MimeTypeHelper.DOC_TYPE))
        Assert.assertEquals(false, isSupportThumbnailForCardWidget(MimeTypeHelper.DOCX_TYPE))
        Assert.assertEquals(false, isSupportThumbnailForCardWidget(MimeTypeHelper.XLS_TYPE))
        Assert.assertEquals(false, isSupportThumbnailForCardWidget(MimeTypeHelper.XLSX_TYPE))
        Assert.assertEquals(false, isSupportThumbnailForCardWidget(MimeTypeHelper.PPT_TYPE))
        Assert.assertEquals(false, isSupportThumbnailForCardWidget(MimeTypeHelper.PPTX_TYPE))
        Assert.assertEquals(false, isSupportThumbnailForCardWidget(MimeTypeHelper.PDF_TYPE))
        Assert.assertEquals(false, isSupportThumbnailForCardWidget(MimeTypeHelper.OFD_TYPE))
    }

    companion object {
        var FULL_PATH = File.separator + "sdcard" + File.separator + "download" + File.separator + "demo.jpg"
        const val DATE_MODIFIED = 10000000000L
        const val SIZE = 1024L
        var RESULT = File("/data/user/0/com.coloros.filemanager/").absolutePath + File.separator +
                "recent_card_widget" + File.separator + "6b9d191d369f39f5f78b75c9409ac7c8"
    }
}