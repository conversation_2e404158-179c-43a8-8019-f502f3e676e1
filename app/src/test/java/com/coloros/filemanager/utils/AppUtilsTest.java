/************************************************************
 * Copyright 2010-2020 Oplus. All rights reserved.
 * FileName       : AppUtilsTest.java
 * Version Number : 1.0
 * Description    :
 * Author         : <PERSON><PERSON><PERSON><PERSON>
 * Date           : 2019-08-15
 * History        :( ID,     Date,         Author, Description)
 *                  v1.0,  2019-08-15, <PERSON><PERSON><PERSON><PERSON>, create
 ************************************************************/
package com.coloros.filemanager.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.drawable.Drawable;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.telephony.TelephonyManager;

import com.coloros.filemanager.BaseTest;
import com.filemanager.common.MyApplication;
import com.filemanager.common.utils.AppUtils;

import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.RobolectricTestRunner;

@Ignore("stupid test")
@RunWith(RobolectricTestRunner.class)
public class AppUtilsTest extends BaseTest {
    private Context mContext = null;
    private ConnectivityManager mConnectivityManager;
    private NetworkInfo mNetworkInfo;
    private TelephonyManager mTelephonyManager;
    private PackageManager mPackageManager;
    private PackageInfo mPackageInfo;
    private static final int NETWORK_TYPE_OTHER = 100;
    private static final String NET_NO = "NO";
    private static final String NET_WIFI = "WIFI";
    private static final String NET_2G = "2G";
    private static final String NET_3G = "3G";
    private static final String NET_4G = "4G";
    private static final String NET_OTHER = "UNKNOWN";

    @Before
    public void setUp() {
        mContext = mock(Context.class);
        mConnectivityManager = mock(ConnectivityManager.class);
        mNetworkInfo = mock(NetworkInfo.class);
        mTelephonyManager = mock(TelephonyManager.class);
        mPackageManager = mock(PackageManager.class);
        mPackageInfo = new PackageInfo();
        when(mContext.getApplicationContext()).thenReturn(mContext);
        when(mContext.getPackageManager()).thenReturn(mPackageManager);
        when(mContext.getPackageName()).thenReturn("test");
        try {
            when(mPackageManager.getPackageInfo("test", 0)).thenReturn(mPackageInfo);
        } catch (PackageManager.NameNotFoundException e) {
        }
        MyApplication.init(mContext, "");
    }

    @After
    public void tearDown() {
        mContext = null;
        mConnectivityManager = null;
        mNetworkInfo = null;
        mTelephonyManager = null;
        mPackageManager = null;
        mPackageInfo = null;
    }

    @Test
    public void should_verify_when_getVersionName_without() {
        mPackageInfo.versionName = "test";
        assertEquals("test", AppUtils.getVersionName());
        try {
            when(mPackageManager.getPackageInfo(mContext.getPackageName(), 0)).thenReturn(null);
        } catch (Exception e) {
        }

        assertNotNull(AppUtils.getVersionName());
    }

    @Test
    public void should_verify_when_getVersionCode_without() {
        mPackageInfo.versionCode = 1111;
        assertEquals(1111, AppUtils.getVersionCode());

        try {
            when(mPackageManager.getPackageInfo(mContext.getPackageName(), 0)).thenReturn(null);
        } catch (Exception e) {
        }
        assertEquals(0, AppUtils.getVersionCode());
    }

    @Test
    public void should_verify_when_getPackageName_without() {
        mPackageInfo.packageName = "test";
        assertEquals("test", AppUtils.getPackageName());
        try {
            when(mPackageManager.getPackageInfo(mContext.getPackageName(), 0)).thenReturn(null);
        } catch (Exception e) {
        }
        assertNotNull(AppUtils.getPackageName());
    }

    //getAppVersionNameByPkgName
    @Test
    public void should_verify_when_getAppVersionNameByPkgName_without() {
        mPackageInfo.versionName = "test";

        try {
            when(mPackageManager.getPackageInfo("test", 0)).thenReturn(mPackageInfo);
        } catch (Exception e) {
        }
        assertEquals("test", AppUtils.getAppVersionNameByPkgName("test"));

        try {
            when(mPackageManager.getPackageInfo("test", 0)).thenReturn(null);
        } catch (Exception e) {
        }
        assertEquals("", AppUtils.getAppVersionNameByPkgName("test"));
    }

    // getAppVersionCode
    @Test
    public void should_verify_when_getAppVersionCode_without() {
        mPackageInfo.versionCode = 1111;

        try {
            when(mPackageManager.getPackageInfo("test", 0)).thenReturn(mPackageInfo);
        } catch (Exception e) {
        }
        assertEquals(1111, AppUtils.getAppVersionCode("test"));

        try {
            when(mPackageManager.getPackageInfo("test", 0)).thenReturn(null);
        } catch (Exception e) {
        }
        assertEquals(0, AppUtils.getAppVersionCode("test"));
    }

    // checkApkInstalledByPackageName
    @Test
    public void should_verify_when_checkApkInstalledByPackageName_without() {
        assertFalse(AppUtils.checkApkInstalledByPackageName(null, "test"));
        assertFalse(AppUtils.checkApkInstalledByPackageName(mContext, null));

        try {
            when(mPackageManager.getPackageInfo("test", PackageManager.GET_ACTIVITIES)).thenReturn(null);
        } catch (Exception e) {
        }
        assertFalse(AppUtils.checkApkInstalledByPackageName(mContext, "test"));

        try {
            mPackageInfo.packageName = "test";
            when(mPackageManager.getPackageInfo("test", PackageManager.GET_ACTIVITIES)).thenReturn(mPackageInfo);
        } catch (Exception e) {
        }
        assertTrue(AppUtils.checkApkInstalledByPackageName(mContext, "test"));

        when(mContext.getPackageManager()).thenReturn(null);
        assertFalse(AppUtils.checkApkInstalledByPackageName(mContext, "test"));
    }


    @Test
    public void should_verify_when_CheckApkInstalledByPath_without() {
        assertTrue(AppUtils.checkApkInstalledByPath(null, null));
        assertTrue(AppUtils.checkApkInstalledByPath(mContext, null));
        String testPath = "D:/";

        when(mPackageManager.getPackageArchiveInfo(testPath, PackageManager.GET_ACTIVITIES)).thenReturn(mPackageInfo);
        assertFalse(AppUtils.checkApkInstalledByPath(mContext, testPath));

        when(mPackageManager.getPackageArchiveInfo(testPath, PackageManager.GET_ACTIVITIES)).thenReturn(null);
        assertTrue(AppUtils.checkApkInstalledByPath(mContext, testPath));

        try {
            when(mPackageManager.getPackageArchiveInfo(testPath, PackageManager.GET_ACTIVITIES)).thenReturn(null);
        } catch (Exception e) {
        }
        assertTrue(AppUtils.checkApkInstalledByPath(mContext, testPath));

        try {
            mPackageInfo.packageName = "test";
            when(mPackageManager.getPackageArchiveInfo(testPath, PackageManager.GET_ACTIVITIES)).thenReturn(mPackageInfo);
        } catch (Exception e) {
        }
        assertFalse(AppUtils.checkApkInstalledByPath(mContext, testPath));
    }

    @Test
    public void should_verify_when_IsAppInstalledByPkgNameg_without() {
        try {
            String testPackageName = "com.oppo.market";
            when(mContext.getPackageManager()).thenReturn(mPackageManager);
            when(mPackageManager.getPackageInfo(testPackageName, 0)).thenReturn(null);
            assertFalse(AppUtils.isAppInstalledByPkgName(mContext, testPackageName));

            when(mPackageManager.getPackageInfo(testPackageName, 0)).thenReturn(mPackageInfo);
            assertTrue(AppUtils.isAppInstalledByPkgName(mContext, testPackageName));
        } catch (Exception e) {

        }
    }


    @Test
    public void should_verify_when_etBuildConfigValue_without() {
        assertNotNull(AppUtils.getBuildConfigValue("APPLICATION_ID"));
        assertEquals("", AppUtils.getBuildConfigValue("test"));
    }

    @Test
    public void should_verify_when_GetNetworkState_with_context() {
        when(mContext.getSystemService(Context.CONNECTIVITY_SERVICE)).thenReturn(null);
        assertFalse(AppUtils.getNetworkState(mContext));

        when(mContext.getSystemService(Context.CONNECTIVITY_SERVICE)).thenReturn(mConnectivityManager);
        when(mConnectivityManager.getActiveNetworkInfo()).thenReturn(null);
        assertFalse(AppUtils.getNetworkState(mContext));

        when(mConnectivityManager.getActiveNetworkInfo()).thenReturn(mNetworkInfo);
        when(mNetworkInfo.isAvailable()).thenReturn(true);
        when(mNetworkInfo.isConnected()).thenReturn(true);
        assertTrue(AppUtils.getNetworkState(mContext));

        when(mNetworkInfo.isAvailable()).thenReturn(false);
        when(mNetworkInfo.isConnected()).thenReturn(true);
        assertFalse(AppUtils.getNetworkState(mContext));
    }

    @Test
    public void should_verify_when_GetPackageNameByPath_without() {
        String testFilePath = "D:/";
        when(mContext.getPackageManager()).thenReturn(mPackageManager);
        when(mPackageManager.getPackageArchiveInfo(testFilePath, PackageManager.GET_ACTIVITIES))
                .thenReturn(null);
        assertEquals("", AppUtils.getPackageNameByPath(mContext, testFilePath));

        when(mPackageManager.getPackageArchiveInfo(testFilePath, PackageManager.GET_ACTIVITIES))
                .thenReturn(mPackageInfo);
        assertNull(AppUtils.getPackageNameByPath(mContext, testFilePath));
    }

    @Test
    public void should_verify_when_simple_method_without() {
        assertEquals(android.os.Build.MODEL, AppUtils.getModel());
        assertEquals(android.os.Build.BRAND, AppUtils.getBrand());
        assertEquals(android.os.Build.MANUFACTURER, AppUtils.getMaker());
        assertEquals(android.os.Build.VERSION.RELEASE, AppUtils.getVersionRelease());
    }

    @Test
    public void should_verify_when_getBitmapFromDrawable_without() {
        Drawable drawable = mock(Drawable.class);
        assertNull(AppUtils.getBitmapFromDrawable(null));
        when(drawable.getIntrinsicWidth()).thenReturn(-1);
        assertNull(AppUtils.getBitmapFromDrawable(drawable));

        when(drawable.getIntrinsicWidth()).thenReturn(2);
        when(drawable.getIntrinsicHeight()).thenReturn(-1);
        assertNull(AppUtils.getBitmapFromDrawable(drawable));

        when(drawable.getIntrinsicWidth()).thenReturn(2);
        when(drawable.getIntrinsicHeight()).thenReturn(2);
        assertNotNull(AppUtils.getBitmapFromDrawable(drawable));
    }


    @Test
    public void should_verify_when_getAppInfoByPath_without() {
        when(mPackageManager.getPackageArchiveInfo("test", PackageManager.GET_ACTIVITIES))
                .thenReturn(null);
        assertNotNull(AppUtils.getAppInfoByPath(mContext, "test"));

    }

    private void telephonyPrepare() {
        when(mContext.getSystemService(Context.CONNECTIVITY_SERVICE)).thenReturn(mConnectivityManager);
        when(mConnectivityManager.getActiveNetworkInfo()).thenReturn(mNetworkInfo);
        when(mNetworkInfo.isAvailable()).thenReturn(true);
        when(mConnectivityManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI)).thenReturn(null);
    }
}