/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FilterTimeDataHelperTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/12/20
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/12/20       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.category.globalsearch.manager.filter

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.wrapper.RecycleFileWrapper
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterCondition
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants.FILTER_TIME_30_DAY
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants.FILTER_TIME_3_DAY
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants.FILTER_TIME_7_DAY
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants.FILTER_TIME_TODAY
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterTimeDataHelper
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import java.util.Calendar

class FilterTimeDataHelperTest {

    private lateinit var context: Context
    private lateinit var filterItemDataHelper: FilterTimeDataHelper

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
        filterItemDataHelper = FilterTimeDataHelper()
    }

    @Test
    @Ignore
    fun testFilterDataWithModifiedDate() {
        val christmas = Calendar.getInstance()
        christmas.set(YEAR, MONTH, Date)
        mockkStatic(Calendar::class)
        every { Calendar.getInstance() } returns christmas

        // filterItem id is FILTER_TIME_TODAY
        initDataList()
        fakeFilterItem.id = FILTER_TIME_TODAY
        resultDataList.add(todayFileBean)
        Assert.assertEquals(
            resultDataList,
            filterItemDataHelper.filterData(fakeFilterItem, dataList)
        )

        // filterItem id is FILTER_TIME_3_DAY
        fakeFilterItem.id = FILTER_TIME_3_DAY
        resultDataList.add(threeDaysFileBean)
        Assert.assertEquals(
            resultDataList,
            filterItemDataHelper.filterData(fakeFilterItem, dataList)
        )

        // filterItem id is FILTER_TIME_7_DAY
        fakeFilterItem.id = FILTER_TIME_7_DAY
        resultDataList.add(sevenDaysFileBean)
        Assert.assertEquals(
            resultDataList,
            filterItemDataHelper.filterData(fakeFilterItem, dataList)
        )

        // filterItem id is FILTER_TIME_30_DAY
        fakeFilterItem.id = FILTER_TIME_30_DAY
        resultDataList.add(thirtyDaysFileBean)
        Assert.assertEquals(
            resultDataList,
            filterItemDataHelper.filterData(fakeFilterItem, dataList)
        )

        // filterItem id is other
        fakeFilterItem.id = -1
        resultDataList.add(anotherDayFileBean)
        Assert.assertEquals(
            resultDataList,
            filterItemDataHelper.filterData(fakeFilterItem, dataList)
        )
    }

    @Test
    @Ignore
    fun testFilterDataWithRecycleDate() {
        val christmas = Calendar.getInstance()
        christmas.set(YEAR, MONTH, Date)
        mockkStatic(Calendar::class)
        every { Calendar.getInstance() } returns christmas

        // filterItem id is FILTER_TIME_TODAY
        initRecycleDataList()
        fakeFilterItem.id = FILTER_TIME_TODAY
        resultDataWrapperList.add(todayFileWrapper)
        Assert.assertEquals(
            resultDataWrapperList,
            filterItemDataHelper.filterData(fakeFilterItem, recycleDataList)
        )

        // filterItem id is FILTER_TIME_3_DAY
        fakeFilterItem.id = FILTER_TIME_3_DAY
        resultDataWrapperList.add(threeDaysFileWrapper)
        Assert.assertEquals(
            resultDataWrapperList,
            filterItemDataHelper.filterData(fakeFilterItem, recycleDataList)
        )

        // filterItem id is FILTER_TIME_7_DAY
        fakeFilterItem.id = FILTER_TIME_7_DAY
        resultDataWrapperList.add(sevenDaysFileWrapper)
        Assert.assertEquals(
            resultDataWrapperList,
            filterItemDataHelper.filterData(fakeFilterItem, recycleDataList)
        )

        // filterItem id is FILTER_TIME_30_DAY
        fakeFilterItem.id = FILTER_TIME_30_DAY
        resultDataWrapperList.add(thirtyDaysFileWrapper)
        Assert.assertEquals(
            resultDataWrapperList,
            filterItemDataHelper.filterData(fakeFilterItem, recycleDataList)
        )

        // filterItem id is other
        fakeFilterItem.id = -1
        resultDataWrapperList.add(anotherDayFileWrapper)
        Assert.assertEquals(
            resultDataWrapperList,
            filterItemDataHelper.filterData(fakeFilterItem, recycleDataList)
        )
    }

    /**
     * Initialize modified date data list with today, 3 days, 7 days, 30 days and another day.
     */
    private fun initDataList() {
        dataList.add(todayFileBean)
        dataList.add(threeDaysFileBean)
        dataList.add(sevenDaysFileBean)
        dataList.add(thirtyDaysFileBean)
        dataList.add(anotherDayFileBean)
    }

    /**
     * Initialize recycle date data list with today, 3 days, 7 days, 30 days and another day.
     */
    private fun initRecycleDataList() {
        recycleDataList.apply {
            add(todayFileWrapper)
            add(threeDaysFileWrapper)
            add(sevenDaysFileWrapper)
            add(thirtyDaysFileWrapper)
            add(anotherDayFileWrapper)
        }
    }

    @After
    fun tearDown() {
        unmockkAll()
    }

    companion object {
        private const val YEAR = 2021
        private const val MONTH = Calendar.DECEMBER
        private const val Date = 24
        private val fakeFilterItem =
            FilterItem(FILTER_TIME_TODAY, FilterCondition(FILTER_TIME_TODAY, ""), "")
        private val dataList = mutableListOf<BaseFileBean>()
        private val recycleDataList = mutableListOf<BaseFileBean>()
        private val resultDataList = mutableListOf<BaseFileBean>()
        private val resultDataWrapperList = mutableListOf<RecycleFileWrapper>()
        private val todayFileBean = BaseFileBean().apply {
            mDateModified = 1640318400000
        }
        private val threeDaysFileBean = BaseFileBean().apply {
            mDateModified = 1640145600000
        }
        private val sevenDaysFileBean = BaseFileBean().apply {
            mDateModified = 1639800000000
        }
        private val thirtyDaysFileBean = BaseFileBean().apply {
            mDateModified = 1637812800000
        }
        private val anotherDayFileBean = BaseFileBean().apply {
            mDateModified = 1636603200000
        }

        // today: 2021.12.24 Milliseconds: 1640318400000
        private val todayFileWrapper = RecycleFileWrapper().apply {
            mRecycelDate = 1640318400000
        }

        // 3 days: 2021.12.22 Milliseconds: 1640145600000
        private val threeDaysFileWrapper = RecycleFileWrapper().apply {
            mRecycelDate = 1640145600000
        }

        // 7 days: 2021.12.18 Milliseconds: 1639800000000
        private val sevenDaysFileWrapper = RecycleFileWrapper().apply {
            mRecycelDate = 1639800000000
        }

        // 30 days: 2021.11.25 Milliseconds: 1637812800000
        private val thirtyDaysFileWrapper = RecycleFileWrapper().apply {
            mRecycelDate = 1637812800000
        }

        // another day: 2021.11.11 Milliseconds: 1636603200000
        private val anotherDayFileWrapper = RecycleFileWrapper().apply {
            mRecycelDate = 1636603200000
        }
    }
}