/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - InputUtil.kt
 * Description:
 *     The utils to handle input string from scripts
 *
 * Version: 2.0
 * Date: 2024-05-23
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-05-23   2.0    Porting from LLMSimulateClick
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.utils

import com.oplus.filemanager.simulateclick.engine.Action
import com.oplus.filemanager.simulateclick.engine.CHECK_TYPE_EQUALS
import com.oplus.filemanager.simulateclick.engine.CHECK_TYPE_WECHAT_EQUALS
import com.oplus.filemanager.simulateclick.engine.INPUT_METHOD_PINYIN
import com.oplus.filemanager.simulateclick.engine.INPUT_METHOD_PINYIN_SPACE
import com.oplus.filemanager.simulateclick.engine.INPUT_METHOD_SHORT_PINYIN
import com.oplus.filemanager.simulateclick.engine.INPUT_METHOD_SHORT_PINYIN_SPACE
import com.oplus.filemanager.simulateclick.engine.INPUT_METHOD_STR_SPACE
import com.oplus.filemanager.simulateclick.engine.InputMethods
import com.oplus.filemanager.simulateclick.engine.SCRIPT_ARGS_PREFIX
import com.oplus.filemanager.simulateclick.engine.Step
import java.util.Locale

internal object InputUtil {

    private val argValuePattern = "\\\$[a-z A-Z]+\\d+".toPattern()

    @JvmStatic
    fun textParse(@InputMethods inputMethod: Int?, str: String): String = when (inputMethod) {
        INPUT_METHOD_STR_SPACE -> PinyinUtil.getSpaceStr(str)
        INPUT_METHOD_PINYIN -> PinyinUtil.getPinyin(str)
        INPUT_METHOD_PINYIN_SPACE -> PinyinUtil.getPinyinSpace(str)
        INPUT_METHOD_SHORT_PINYIN -> PinyinUtil.getShortPinyin(str)
        INPUT_METHOD_SHORT_PINYIN_SPACE -> PinyinUtil.getShortPinyinSpace(str)
        else -> str
    }

    @JvmStatic
    fun isEqualsIgnoreSpace(str: String, str2: String?): Boolean {
        if (str2.isNullOrEmpty()) {
            return false
        }
        val lowerStr = str.replace(" ".toRegex(), "").lowercase(Locale.getDefault())
        val lowerStr2 = str2.replace(" ".toRegex(), "").lowercase(Locale.getDefault())
        return lowerStr2.contains(lowerStr)
    }

    @JvmStatic
    fun isEqualsIgnoreSpace(str: String, str2: String?, action: Action?): Boolean =
        isEqualsIgnoreSpace(str, str2, action?.itemCheckType)

    @JvmStatic
    fun isEqualsIgnoreSpace(str: String, str2: String?, checkType: String?): Boolean {
        if (str2.isNullOrEmpty()) {
            return false
        }
        if (checkType == CHECK_TYPE_WECHAT_EQUALS) {
            val subText = str2.split(" ")
            if (subText.isNotEmpty()) {
                return isEqualsIgnoreSpace(str, subText[0], CHECK_TYPE_EQUALS)
            }
        }
        val lowerStr = str.replace(" ".toRegex(), "").lowercase(Locale.getDefault())
        val lowerStr2 = str2.replace(" ".toRegex(), "").lowercase(Locale.getDefault())
        return if (CHECK_TYPE_EQUALS == checkType) {
            lowerStr == lowerStr2
        } else {
            lowerStr2.contains(lowerStr)
        }
    }

    @JvmStatic
    fun isCheckTypeEquals(action: Action): Boolean =
        action.itemCheckType == CHECK_TYPE_EQUALS

    @JvmStatic
    fun replaceAllTags(list: MutableList<Step>?, hashMap: Map<String, String>): List<Step>? {
        if (list.isNullOrEmpty() || hashMap.isEmpty()) {
            return list
        }
        val it: MutableIterator<Step> = list.iterator()
        while (it.hasNext()) {
            val next = it.next()
            replaceAction(next.actions ?: mutableListOf(), hashMap)
        }
        return list
    }

    @JvmStatic
    fun replaceAction(actions: MutableList<Action>, hashMap: Map<String, String>) {
        val iterator: Iterator<Action> = actions.iterator()
        while (iterator.hasNext()) {
            val action = iterator.next()
            action.replaceParamArgs(hashMap, Action::actionInput, Action::actionInput::set)
            action.replaceParamArgs(hashMap, Action::widgetText, Action::widgetText::set)
            action.replaceParamArgs(hashMap, Action::actionSelect, Action::actionSelect::set)
            action.replaceParamArgs(hashMap, Action::selectIndex, Action::selectIndex::set)
            action.replaceParamArgs(hashMap, Action::childWidgetText, Action::childWidgetText::set)
        }
    }

    @Suppress("UtilMustStaticRule")
    private inline fun Action.replaceParamArgs(
        hashMap: Map<String, String>,
        paramGetter: Action.() -> String?,
        paramSetter: Action.(String?) -> Unit
    ) {
        val beforeParam = paramGetter()
        if (beforeParam?.contains(SCRIPT_ARGS_PREFIX) != true) {
            return
        }
        val paramArgs = getArgs(beforeParam)
        if (hashMap.containsKey(paramArgs)) {
            paramSetter(beforeParam.replace(paramArgs, hashMap[paramArgs] ?: paramArgs))
            queryStep = null
        }
    }

    @JvmStatic
    private fun getArgs(str: String?): String {
        str ?: return ""
        val matcher = argValuePattern.matcher(str)
        return if (matcher.find()) {
            matcher.group(0) ?: return ""
        } else ""
    }
}