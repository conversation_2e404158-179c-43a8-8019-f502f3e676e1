package com.oplus.filemanager.ad

import android.app.Activity
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import java.util.ArrayList

/**
 * SubPageAdMgr 的单元测试类
 * 用于测试 SubPageAdMgr 类的各种功能和行为
 */
class SubPageAdMgrTest {

    // 模拟的 Lifecycle 对象
    private lateinit var lifecycle: Lifecycle
    // 被测试的 SubPageAdMgr 实例
    private lateinit var subPageAdMgr: SubPageAdMgr

    /**
     * 在每个测试方法执行前的准备工作
     * 1. 创建模拟的 Lifecycle 对象
     * 2. 初始化 SubPageAdMgr 实例
     * 3. 模拟 Log 类的静态方法
     */
    @Before
    fun setUp() {
        lifecycle = mockk(relaxed = true)
        subPageAdMgr = SubPageAdMgr(lifecycle)
        
        // 模拟 Log.d() 方法，使其不执行实际日志记录
        mockkStatic(Log::class)
        every { Log.d(any()) } returns Unit
    }

    /**
     * 在每个测试方法执行后的清理工作
     * 解除所有模拟对象的绑定
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试 requestSubAd 方法的正常调用情况
     * 验证是否正确地调用了日志记录
     */
    @Test
    fun `requestSubAd normal call should log`() {
        val activity = mockk<Activity>()
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>>()
        val fileList = ArrayList<MediaFileWrapper>()

        subPageAdMgr.requestSubAd(activity, "testName", adapter, fileList, true)
        // 验证 Log.d() 方法被调用了一次
        verify(exactly = 1) { Log.d(any()) }
    }

    /**
     * 测试 requestSubAd 方法处理空文件列表的情况
     * 验证日志中是否包含文件列表信息
     */
    @Test
    fun `requestSubAd empty file list should handle`() {
        val activity = mockk<Activity>()
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>>()
        val emptyList = ArrayList<MediaFileWrapper>()

        subPageAdMgr.requestSubAd(activity, "emptyTest", adapter, emptyList, false)
        // 验证日志中包含文件列表信息
        verify(exactly = 1) { Log.d(match { it.contains("fileList is") }) }
    }

    /**
     * 测试 scanModeChange 方法在 hideFlag 为 true 时的日志记录
     */
    @Test
    fun `scanModeChange hideFlag true should log correctly`() {
        subPageAdMgr.scanModeChange("modeChange", true)
        verify(exactly = 1) { 
            Log.d(match { it.contains("hideAD is true") }) 
        }
    }

    /**
     * 测试 scanModeChange 方法在 hideFlag 为 false 时的日志记录
     */
    @Test
    fun `scanModeChange hideFlag false should log correctly`() {
        subPageAdMgr.scanModeChange("modeChange", false)
        verify(exactly = 1) { 
            Log.d(match { it.contains("hideAD is false") }) 
        }
    }

    /**
     * 测试 scanModeChange 方法处理空名称的情况
     */
    @Test
    fun `scanModeChange empty name should handle`() {
        subPageAdMgr.scanModeChange("", true)
        verify(exactly = 1) { 
            Log.d(match { it.contains("name is ") }) 
        }
    }

    /**
     * 测试 requestCommonAd 方法使用 FragmentActivity 的情况
     */
    @Test
    fun `requestCommonAd FragmentActivity call should log`() {
        val activity = mockk<FragmentActivity>()
        val adapter = mockk<BaseSelectionRecycleAdapter<*, MediaFileWrapper>>()
        val fileList = ArrayList<MediaFileWrapper>()

        subPageAdMgr.requestCommonAd(activity, adapter, fileList)
        verify(exactly = 1) { Log.d(any()) }
    }

    /**
     * 测试 requestSubAlbumSetAd 方法处理 AlbumItem 类型数据的情况
     */
    @Test
    fun `requestSubAlbumSetAd AlbumItem type should handle`() {
        val activity = mockk<Activity>()
        val adapter = mockk<BaseSelectionRecycleAdapter<*, AlbumItem>>()
        val fileList = ArrayList<AlbumItem>().apply {
            add(AlbumItem(1))
        }

        subPageAdMgr.requestSubAlbumSetAd(activity, adapter, fileList)
        verify(exactly = 1) { Log.d(any()) }
    }

    /**
     * 测试 requestPhoneStorageAd 方法处理 BaseFileBean 类型数据的情况
     */
    @Test
    fun `requestPhoneStorageAd BaseFileBean type should handle`() {
        val activity = mockk<Activity>()
        val adapter = mockk<BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, BaseFileBean>>()
        val fileList = ArrayList<BaseFileBean>().apply {
            add(BaseFileBean().apply {
                mDisplayName = "testFile"
            })
        }

        subPageAdMgr.requestPhoneStorageAd(activity, adapter, fileList)
        verify(exactly = 1) { Log.d(any()) }
    }

    /**
     * 测试 refreshIfListChanged 方法的日志记录
     */
    @Test
    fun `refreshIfListChanged should log`() {
        subPageAdMgr.refreshIfListChanged()
        verify(exactly = 1) { Log.d("refreshIfListChanged") }
    }

    /**
     * 测试 refreshByScanModeChanged 方法的日志记录
     */
    @Test
    fun `refreshByScanModeChanged should log`() {
        subPageAdMgr.refreshByScanModeChanged()
        verify(exactly = 1) { Log.d("refreshByScanModeChanged") }
    }
}