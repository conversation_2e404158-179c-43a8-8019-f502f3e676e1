/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - FilePreviewTypeCheckHelperTest.kt
 * Description:
 * The test cases for FilePreviewTypeCheckHelper
 *
 * Version: 1.0
 * Date: 2024-10-17
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-17   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.utils

import android.content.Context
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class FilePreviewTypeCheckHelperTest : Assert() {

    private lateinit var context: Context

    @Before
    fun setUp() {
        mockkStatic(MimeTypeHelper::class)
        mockkObject(MimeTypeHelper)
        every { MimeTypeHelper.getTypeFromDrm(any(), any(), any()) } answers { secondArg() }
        every { MimeTypeHelper.getTypeFromPath(any()) } returns MimeTypeHelper.UNKNOWN_TYPE
        every { MimeTypeHelper.getMediaType(any()) } returns MimeTypeHelper.UNKNOWN_TYPE
        context = mockk {
            every { applicationContext } returns mockk()
        }
    }

    @After
    fun tearDown() {
        unmockkStatic(MimeTypeHelper::class)
        unmockkObject(MimeTypeHelper)
    }

    @Test
    fun `should true when isApkFileType with apk file`() {
        // Given
        val fileBean = BaseFileBean().apply {
            mData = "test.apk"
            mLocalType = MimeTypeHelper.APPLICATION_TYPE
        }

        // When
        val result = FilePreviewTypeCheckHelper(context).isApkFileType(fileBean)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should true when isArchiveFileType with rar file`() {
        // Given
        val fileBean = BaseFileBean().apply {
            mData = "test.rar"
            mLocalType = MimeTypeHelper.COMPRESSED_TYPE
        }

        // When
        val result = FilePreviewTypeCheckHelper(context).isArchiveFileType(fileBean)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should true when isAudioFileType with mp3 file`() {
        // Given
        val fileBean = BaseFileBean().apply {
            mData = "test.mp3"
            mLocalType = MimeTypeHelper.AUDIO_TYPE
        }

        // When
        val result = FilePreviewTypeCheckHelper(context).isAudioFileType(fileBean)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should true when isImageFileType with jpg file`() {
        // Given
        val fileBean = BaseFileBean().apply {
            mData = "test.jpg"
            mLocalType = MimeTypeHelper.IMAGE_TYPE
        }

        // When
        val result = FilePreviewTypeCheckHelper(context).isImageFileType(fileBean)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should true when isVideoFileType with mkv file`() {
        // Given
        val fileBean = BaseFileBean().apply {
            mData = "test.mkv"
            mLocalType = MimeTypeHelper.VIDEO_TYPE
        }

        // When
        val result = FilePreviewTypeCheckHelper(context).isVideoFileType(fileBean)

        // Then
        assertTrue(result)
    }

    @Test
    fun `should true when isDocType with doc file`() {
        // Given
        val fileBean = BaseFileBean().apply {
            mData = "test.doc"
            mLocalType = MimeTypeHelper.DOC_TYPE
        }

        // When
        val result = FilePreviewTypeCheckHelper(context).isDocType(fileBean)

        // Then
        assertTrue(result)
    }
}