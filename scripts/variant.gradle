final isAppModule = project.plugins.hasPlugin("com.android.application")
final isLibModule = project.plugins.hasPlugin("com.android.library")
if (!(isAppModule || isLibModule)) {
    final errMsg = "${project.name}: variant.gradle must be applied to " +
            "an Android application or library module. Please apply " +
            "com.android.application or com.android.library first."
    System.err.println(errMsg)
    throw new GradleException(errMsg)
}

android {

    android {
        //B：品牌，如果应用不存在品牌差异，需要设置一个“ball”
        //P:产品，如果应用不存在轻量os版本差异，需要设置一个“pall”
        //地区：如果应用不存在地区差异，需要设置一个“regionall”
        //api版本：如果应用不存在colorOS版本裂化，需要设置一个“apilevelall”即可
        flavorDimensions = ["B", "P", "region", "apilevel"]
        productFlavors {
            //apk名称需要重命名为：“OPPO”
            oppo {
                dimension "B"
            }
            realme {
                dimension "B"
            }
            //apk名称需要重命名为：“OnePlus”
            oneplus {
                dimension "B"
            }
            pall {
                dimension "P"
            }
            domestic {
                dimension "region"
            }
            export {
                dimension "region"
                //默认情况，只编译核心语言，但是服务器编译，会动态修改，编译内销需要的所有语言
                if (prop_disableSubPackage.toBoolean()) {
                    println("app disable resource subpacakge")
                } else {
                    if (!prop_exp_resConfig.toString().isEmpty()) {
                        resConfigs prop_exp_resConfig
                    } else {
                        println("subpacakge config is empty, no subpackage")
                    }
                }
            }
            //apk名称需要重命名为：“GDPR”
            gdpr {
                dimension "region"
                //默认情况，只编译核心语言，但是服务器编译，会动态修改，编译内销需要的所有语言
                if (prop_disableSubPackage.toBoolean()) {
                    println("app disable resource subpacakge")
                } else {
                    if (!prop_exp_resConfig.toString().isEmpty()) {
                        resConfigs prop_exp_resConfig
                    } else {
                        println("subpacakge config is empty, no subpackage")
                    }
                }
            }
            apilevelall {
                dimension "apilevel"
            }
        }
    }
}