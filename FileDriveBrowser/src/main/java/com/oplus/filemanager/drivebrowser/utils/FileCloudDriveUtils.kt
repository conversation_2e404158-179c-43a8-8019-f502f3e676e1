/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileCloudDriveUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/7 16:53
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/7       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.stringResource
import com.oplus.filemanager.drivebrowser.R

fun getFileCloudDriveTitle(category: Int): String =
    if (category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
        stringResource(com.filemanager.common.R.string.tencent_docs)
    } else {
        stringResource(com.filemanager.common.R.string.kdocs)
    }