/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - PinyinUtil.kt
 * Description:
 *     The utils to convert pinyin string.
 *
 * Version: 2.0
 * Date: 2024-05-23
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-05-23   2.0    Porting from LLMSimulateClick
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.utils


/**
 * Just a stub at current to compat [com.oplus.filemanager.simulateclick.engine.Action.inputMethod]
 * Implement it if necessary in the future.
 */
internal object PinyinUtil {
    @JvmStatic
    fun getPinyin(str: String): String {
        return str
    }

    @JvmStatic
    fun getShortPinyin(str: String): String {
        return str
    }

    @JvmStatic
    fun getPinyinSpace(str: String): String {
        return str
    }

    @JvmStatic
    fun getShortPinyinSpace(str: String): String {
        return str
    }

    @JvmStatic
    fun getSpaceStr(str: String): String {
        return str
    }
}