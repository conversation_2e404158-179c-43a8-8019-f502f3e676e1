/***********************************************************
 ** Copyright (C), 2008-2025 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.doc
 ** Version: 1.0
 ** Date: 2023/9/19
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.appicon

import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import com.bumptech.glide.Priority
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.data.DataFetcher
import com.filemanager.thumbnail.utils.Log

/**
 * the real fetcher of Extracting doc's thumbnail
 */
internal class AppIconThumbnailFetcher(
    private val model: AppIconThumbnail,
    private val width: Int,
    private val height: Int
) : DataFetcher<Bitmap> {

    private companion object {
        private const val TAG = "AppIconThumbnailFetcher"
    }

    @Volatile
    private var isCancelled = false
    private var appBitmap: Bitmap? = null

    override fun getDataClass(): Class<Bitmap> {
        return Bitmap::class.java
    }

    override fun cleanup() {
        appBitmap = null
    }

    override fun getDataSource(): DataSource {
        return DataSource.REMOTE
    }

    override fun cancel() {
        isCancelled = true
        appBitmap = null
    }

    override fun loadData(priority: Priority, callback: DataFetcher.DataCallback<in Bitmap>) {
        if (isCancelled) {
            //return null when request has been cancelled
            callback.onDataReady(null)
            return
        }
        val manager = model.context.packageManager
        try {
            val icon = manager.getApplicationIcon(model.packageName)
            val bitmap = Bitmap.createBitmap(icon.intrinsicWidth, icon.intrinsicHeight, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)
            icon.setBounds(0, 0, canvas.width, canvas.height)
            icon.draw(canvas)
            callback.onDataReady(bitmap)
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "loadData e = $e")
            callback.onLoadFailed(e)
        }
    }
}
