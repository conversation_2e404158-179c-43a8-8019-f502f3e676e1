package com.oplus.dropdrag.recycleview

import android.view.MotionEvent
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * ItemDetailsLookup 类的单元测试类
 * 用于测试 ItemDetailsLookup 及其内部类 ItemDetails 的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class ItemDetailsLookupTest {

    // 测试用变量
    private lateinit var itemDetailsLookup: TestItemDetailsLookup
    private lateinit var mockEvent: MotionEvent
    private lateinit var mockItemDetails: ItemDetailsLookup.ItemDetails<String>
    private lateinit var mockView: View

    /**
     * 测试前置设置
     * 初始化测试对象和模拟对象
     */
    @Before
    fun setup() {
        itemDetailsLookup = TestItemDetailsLookup()
        mockEvent = mockk(relaxed = true)  // 创建模拟的 MotionEvent 对象
        mockItemDetails = mockk(relaxed = true)  // 创建模拟的 ItemDetails 对象
        mockView = mockk(relaxed = true)  // 创建模拟的 View 对象
    }

    /**
     * 测试后清理
     * 清除所有模拟对象
     */
    @After
    fun tearDown() {
        clearAllMocks()
    }

    /**
     * 测试 overItem 方法 - 当存在位置时返回 true
     */
    @Test
    fun `test overItem when position exists returns true`() {
        itemDetailsLookup.testItemDetails = mockItemDetails
        every { mockItemDetails.position } returns 1  // 模拟返回位置1

        assertTrue(itemDetailsLookup.overItem(mockEvent))
    }

    /**
     * 测试 overItem 方法 - 当没有位置时返回 false
     */
    @Test
    fun `test overItem when no position returns false`() {
        itemDetailsLookup.testItemDetails = mockItemDetails
        every { mockItemDetails.position } returns RecyclerView.NO_POSITION  // 模拟返回无效位置

        assertFalse(itemDetailsLookup.overItem(mockEvent))
    }

    /**
     * 测试 overItemWithSelectionKey 方法 - 当有选择键时返回 true
     */
    @Test
    fun `test overItemWithSelectionKey when has selection key returns true`() {
        itemDetailsLookup.testItemDetails = mockItemDetails
        every { mockItemDetails.position } returns 1  // 模拟返回位置1
        every { mockItemDetails.selectionKey } returns "key"  // 模拟返回选择键

        assertTrue(itemDetailsLookup.overItemWithSelectionKey(mockEvent))
    }

    /**
     * 测试 overItemWithSelectionKey 方法 - 当没有选择键时返回 false
     */
    @Test
    fun `test overItemWithSelectionKey when no selection key returns false`() {
        itemDetailsLookup.testItemDetails = mockItemDetails
        every { mockItemDetails.position } returns 1  // 模拟返回位置1
        every { mockItemDetails.selectionKey } returns null  // 模拟返回空选择键

        assertFalse(itemDetailsLookup.overItemWithSelectionKey(mockEvent))
    }

    /**
     * 测试 inItemDragRegion 方法 - 当在拖拽区域时返回 true
     */
    @Test
    fun `test inItemDragRegion when in drag region returns true`() {
        itemDetailsLookup.testItemDetails = mockItemDetails
        every { mockItemDetails.position } returns 1  // 模拟返回位置1
        every { mockItemDetails.inDragRegion(mockEvent) } returns true  // 模拟在拖拽区域

        assertTrue(itemDetailsLookup.inItemDragRegion(mockEvent))
    }

    /**
     * 测试 inItemDragRegion 方法 - 当不在拖拽区域时返回 false
     */
    @Test
    fun `test inItemDragRegion when not in drag region returns false`() {
        itemDetailsLookup.testItemDetails = mockItemDetails
        every { mockItemDetails.position } returns 1  // 模拟返回位置1
        every { mockItemDetails.inDragRegion(mockEvent) } returns false  // 模拟不在拖拽区域

        assertFalse(itemDetailsLookup.inItemDragRegion(mockEvent))
    }

    /**
     * 测试 inItemSelectRegion 方法 - 当在选择热点区域时返回 true
     */
    @Test
    fun `test inItemSelectRegion when in selection hotspot returns true`() {
        itemDetailsLookup.testItemDetails = mockItemDetails
        every { mockItemDetails.position } returns 1  // 模拟返回位置1
        every { mockItemDetails.inSelectionHotspot(mockEvent) } returns true  // 模拟在选择热点区域

        assertTrue(itemDetailsLookup.inItemSelectRegion(mockEvent))
    }

    /**
     * 测试 getItemPosition 方法 - 返回正确的位置
     */
    @Test
    fun `test getItemPosition returns correct position`() {
        itemDetailsLookup.testItemDetails = mockItemDetails
        every { mockItemDetails.position } returns 5  // 模拟返回位置5

        assertEquals(5, itemDetailsLookup.getItemPosition(mockEvent))
    }

    /**
     * 测试 getItemPosition 方法 - 当没有项目时返回 NO_POSITION
     */
    @Test
    fun `test getItemPosition when no item returns NO_POSITION`() {
        itemDetailsLookup.testItemDetails = null  // 设置没有项目

        assertEquals(RecyclerView.NO_POSITION, itemDetailsLookup.getItemPosition(mockEvent))
    }

    /**
     * 测试 ItemDetails 的 equals 方法 - 当键和位置相同时返回 true
     */
    @Test
    fun `test ItemDetails equals when same keys and position returns true`() {
        val item1 = TestItemDetails("key", 1)  // 创建测试项1
        val item2 = TestItemDetails("key", 1)  // 创建测试项2

        assertTrue(item1 == item2)
    }

    /**
     * 测试 ItemDetails 的 equals 方法 - 当键不同时返回 false
     */
    @Test
    fun `test ItemDetails equals when different keys returns false`() {
        val item1 = TestItemDetails("key1", 1)  // 创建测试项1
        val item2 = TestItemDetails("key2", 1)  // 创建测试项2

        assertFalse(item1 == item2)
    }

    /**
     * 测试 ItemDetails 的 equals 方法 - 当位置不同时返回 false
     */
    @Test
    fun `test ItemDetails equals when different positions returns false`() {
        val item1 = TestItemDetails("key", 1)  // 创建测试项1
        val item2 = TestItemDetails("key", 2)  // 创建测试项2

        assertFalse(item1 == item2)
    }

    /**
     * 测试 ItemDetails 的 hashCode 方法 - 返回位置右移8位的结果
     */
    @Test
    fun `test ItemDetails hashCode returns position shifted right by 8`() {
        val item = TestItemDetails("key", 256)  // 创建测试项
        assertEquals(1, item.hashCode())  // 256右移8位等于1
    }

    /**
     * 用于测试的 ItemDetailsLookup 实现类
     */
    private class TestItemDetailsLookup : ItemDetailsLookup<String>() {
        var testItemDetails: ItemDetails<String>? = null  // 测试用的 ItemDetails
        var testView: View? = null  // 测试用的 View

        /**
         * 获取项目详情
         */
        override fun getItemDetails(e: MotionEvent): ItemDetails<String>? {
            return testItemDetails
        }

        /**
         * 获取项目视图
         */
        override fun getItemView(e: MotionEvent): View? {
            return testView
        }
    }

    /**
     * 用于测试的 ItemDetails 实现类
     */
    private class TestItemDetails(
        override val selectionKey: String?,  // 选择键
        override val position: Int  // 位置
    ) : ItemDetailsLookup.ItemDetails<String>() {
        override fun inSelectionHotspot(e: MotionEvent): Boolean = false  // 不在选择热点区域
        override fun inDragRegion(e: MotionEvent): Boolean = false  // 不在拖拽区域
        override fun canLongPressOrClick(): Boolean = true  // 可以长按或点击
    }
}