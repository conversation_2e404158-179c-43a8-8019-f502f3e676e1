/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved.
 * * COLOROS_EDIT
 * * File: KeyMoveChoiceFragment
 * * Description: a fragment to choice datas for a key to move
 * * Version: 1.0
 * * Date : 2020/7/14
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/7/14       1.0         a fragment to choice datas for a key to move
 ****************************************************************/
package com.oplus.filemanager.keymove.ui.keymovechoice

import android.content.Context
import android.os.Bundle
import android.os.SystemClock
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.COUILinearLayoutManager
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.rotateview.COUIRotateView
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.base.FileManagerExpandableRecyclerView
import com.filemanager.common.constants.Constants
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.PrivacyPolicyController.Companion.RTL_SYMBOL
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.OnGetUIInfoListener
import com.filemanager.common.utils.*
import com.filemanager.common.utils.Utils.*
import com.filemanager.fileoperate.base.ACTION_CANCELLED
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.google.android.material.appbar.AppBarLayout
import com.oplus.filemanager.keymove.R
import com.oplus.filemanager.keymove.adapter.KeyMoveAchieveAdapter
import com.oplus.filemanager.keymove.adapter.KeyMoveAdapter
import com.oplus.filemanager.keymove.ui.AKeyToMoveActivity
import com.oplus.filemanager.keymove.ui.model.ListSecond
import com.oplus.filemanager.keymove.ui.operation.FileActionKeyMove
import com.oplus.filemanager.keymove.ui.operation.FileActionKeyMove.Companion.UPDATE_COUNT
import kotlinx.coroutines.*

class KeyMoveChoiceFragment : BaseVMFragment<KeyMoveChoiceViewModel>(), OnBackPressed,
    CoroutineScope by MainScope(), OnGetUIInfoListener {

    companion object {
        private const val TAG = "KeyMoveChoiceFragment"
    }

    private var mAdapter: KeyMoveAdapter? = null
    private var mMoveAdapter: KeyMoveAchieveAdapter? = null
    private var mViewModel: KeyMoveChoiceViewModel? = null
    private var mRootView: ViewGroup? = null
    private var mToolbar: COUIToolbar? = null
    private var mTitleTv: TextView? = null
    private var mSizeTv: TextView? = null
    private var mSpaceTv: TextView? = null
    private var mMoveButton: Button? = null
    private var mIsMoveAchieve: Boolean? = null
    private var mIsNeedUpgradeData = false
    private var mKeyMoveActionThread: FileActionKeyMove? = null
    private var mRecyclerView: FileManagerExpandableRecyclerView? = null
    private var mLayoutManager: COUILinearLayoutManager? = null
    private var mLastClickCheckBoxTime = -300L
    private val mExternalPath: String by lazy {
        VolumeEnvironment.getExternalSdPath(MyApplication.sAppContext)
    }
    private var mLoadingController: LoadingController? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            mActivity = activity as? AKeyToMoveActivity
            mAdapter = KeyMoveAdapter()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.key_move_choice
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        (mActivity as? AKeyToMoveActivity)?.apply {
            if (this.mFromSystemConfigModeChange && this.mExistFragmentCount > 1) {
                this.mExistFragmentCount -= 1
                if (this.mExistFragmentCount == 1) {
                    this.mFromSystemConfigModeChange = false
                }
                onDestroy()
                return
            }
        }
        super.onViewCreated(view, savedInstanceState)
    }

    override fun initView(view: View) {
        initToolBar(view)
        initMoveButton(view)
        initTextView(view)
        mRecyclerView = view.findViewById(R.id.recyclerview)
    }

    private fun initToolBar(view: View) {
        mRootView = view.findViewById(R.id.coordinator_layout)
        mRootView?.apply {
            setPadding(paddingLeft,
                COUIPanelMultiWindowUtils.getStatusBarHeight(mActivity), paddingRight, paddingBottom)
        }
        mToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        mActivity?.apply {
            setSupportActionBar(mToolbar)
            supportActionBar?.let {
                it.setDisplayHomeAsUpEnabled(true)
                it.setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
        view.findViewById<AppBarLayout>(com.oplus.selectdir.R.id.appbar)?.apply {
            StatusBarUtil.getStatusBarView(context)?.let {
                addView(it, 0, it.layoutParams)
            }
        }
    }

    private fun initMoveButton(view: View) {
        mMoveButton = view.findViewById(R.id.move_btn)
        mMoveButton?.setOnClickListener {
            Log.d(TAG, "mMoveButton onClick() mIsMoveAchieve=$mIsMoveAchieve")
            val curTime = SystemClock.elapsedRealtime()
            if (curTime - mLastClickCheckBoxTime <= Constants.QUICK_CLICK_TIME_GAP) {
                Log.d(TAG, "mMoveButton click too fast")
                return@setOnClickListener
            }
            when (mIsMoveAchieve) {
                false -> {
                    mKeyMoveActionThread?.cancel()
                }
                true -> {
                    (mActivity as? AKeyToMoveActivity)?.switchToGuideFragment()
                }
                else -> {
                    keyMove()
                }
            }
        }
    }

    private fun initTextView(view: View) {
        mTitleTv = view.findViewById(R.id.move_title_id)
        mSizeTv = view.findViewById(R.id.selected_size_id)
        mSpaceTv = view.findViewById(R.id.sdcard_space_id)
        updateSizeAndSpace()
        ViewHelper.setClassificationTextSize(MyApplication.sAppContext, mTitleTv)
        ViewHelper.setClassificationTextSize(MyApplication.sAppContext, mSizeTv)
        ViewHelper.setClassificationTextSize(MyApplication.sAppContext, mSpaceTv)
    }

    private fun initRecyclerView(savedInstanceState: Bundle?) {
        mRecyclerView?.let {
            mLayoutManager = COUILinearLayoutManager(this.context)
            it.isNestedScrollingEnabled = true
            it.clipToPadding = false
            it.isVerticalFadingEdgeEnabled = true
            it.setFadingEdgeLength(MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.list_fading_edge_height))
            it.layoutManager = mLayoutManager
            mAdapter?.let { adapter ->
                adapter.setHasStableIds(true)
                it.setAdapter(adapter)
            }
            it.setOnGroupClickListener { _, v, _, _ ->
                (v.findViewById(R.id.group_indicator_image) as COUIRotateView?)?.apply {
                    startRotateAnimation()
                }
                false
            }
            it.setOnChildClickListener { _, _, groupPosition, childPosition, _ ->
                mViewModel?.onItemClick(mActivity, groupPosition, childPosition)
                false
            }
            mToolbar?.post {
                if (isAdded) {
                    it.setPadding(it.paddingLeft, 0, it.paddingRight,
                            MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom))
                }
            }
        }
    }

    private fun updateSizeAndSpace() {
        launch {
            val size: Long = withContext(Dispatchers.Default) {
                mViewModel?.getTotalSelectedSize() ?: 0L
            }
            val availableSdSize = getStorageAvailableSize(mExternalPath)
            val title: String?
            val spaceDetail: String?
            val sizeDetail = if (isRtl()) {
                (mActivity?.getString(com.filemanager.common.R.string.string_already_chosen) + " "
                        + RTL_SYMBOL + byteCountToDisplaySize(size) + RTL_SYMBOL)
            } else {
                (mActivity?.getString(com.filemanager.common.R.string.string_already_chosen) + " "
                        + byteCountToDisplaySize(size))
            }
            if (size > availableSdSize) {
                title = mActivity?.getString(com.filemanager.common.R.string.string_sdcard_space_shortage)
                spaceDetail = mActivity?.getString(com.filemanager.common.R.string.string_clear_sdcard_space)
                mMoveButton?.isEnabled = false
                showStorageNotEnoughDialog()
            } else {
                title = mActivity?.getString(com.filemanager.common.R.string.string_select_data_transferred)
                spaceDetail = if (isRtl()) {
                    (mActivity?.getString(com.filemanager.common.R.string.string_sdcard_available_space) + " "
                            + RTL_SYMBOL + byteCountToDisplaySizeForUnit(availableSdSize) + RTL_SYMBOL)
                } else {
                    (mActivity?.getString(com.filemanager.common.R.string.string_sdcard_available_space) + " "
                            + byteCountToDisplaySizeForUnit(availableSdSize))
                }
                mMoveButton?.isEnabled = size > 0
            }
            mTitleTv?.text = title
            mSizeTv?.text = sizeDetail
            mSpaceTv?.text = spaceDetail
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        mViewModel = ViewModelProvider(this).get(KeyMoveChoiceViewModel::class.java)
        initRecyclerView(savedInstanceState)
    }

    override fun startObserve() {
        mAdapter?.setGroupCheckBoxClickListener(object : KeyMoveAdapter.ParentCOUICheckBoxListener {
            override fun onClick(view: View, groupPosition: Int) {
                Log.d(TAG, "GroupCheckBox onClick() groupPosition=$groupPosition")
                mLastClickCheckBoxTime = SystemClock.elapsedRealtime()
                mViewModel?.onGroupCheckBoxClick(groupPosition)
            }
        })
        mAdapter?.setChildCheckBoxClickListener(object : KeyMoveAdapter.ChildCOUICheckBoxListener {
            override fun onClick(view: View, groupPosition: Int, childPosition: Int) {
                Log.d(TAG, "ChildCheckBox onClick() groupPosition=$groupPosition,childPosition=$childPosition")
                mLastClickCheckBoxTime = SystemClock.elapsedRealtime()
                mViewModel?.onChildCheckBoxClick(groupPosition, childPosition)
            }
        })
        mRecyclerView?.post {
            if (isAdded && (mViewModel != null)) {
                mViewModel!!.mUiState.observe(this,  { fileUiModel ->
                    Log.d(TAG, "mUiState =" + fileUiModel.mGroupItems.size)
                    mAdapter?.setData(fileUiModel.mGroupItems, fileUiModel.mChildItems)
                    updateSizeAndSpace()
                })
                startObserveLoadState()
            }
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            mLoadingController = LoadingController(it, this).apply {
                observe(mViewModel?.mDataLoadState) {
                    mViewModel?.let { vm ->
                        vm.mUiState.value?.mGroupItems.isNullOrEmpty().not()
                    } ?: false
                }
            }
        }
	}

    override fun onResumeLoadData() {
        if (!isAdded) {
            return
        }
        mViewModel?.initLoader(LoaderViewModel.getLoaderController(mActivity ?: this), mIsNeedUpgradeData)
        mIsNeedUpgradeData = false
    }

	fun setIsNeedOnResumeLoadData() {
        mIsNeedUpgradeData = true
	}

    override fun pressBack(): Boolean {
        if (mActivity is AKeyToMoveActivity) {
            (mActivity as AKeyToMoveActivity).switchToGuideFragment()
            return true
        }
        return false
    }

    private fun showStorageNotEnoughDialog() {
        if ((mActivity == null) or !mActivity!!.isActive){
            Log.w(TAG, "showStorageNotEnoughDialog: mActivity should be not to use")
            return
        }
        val dialog: AlertDialog = COUIAlertDialogBuilder(mActivity!!)
                .setTitle(com.filemanager.common.R.string.string_clear_sdcard_space)
                .setPositiveButton(com.filemanager.common.R.string.positive_ok, null).show()
        val view = dialog.findViewById<View>(android.R.id.message)
        if (view is TextView) {
            view.gravity = Gravity.CENTER_HORIZONTAL
        }
    }

    fun setListSecond(listSecond: ListSecond) {
        mViewModel?.setListSecond(listSecond)
        updateSizeAndSpace()
    }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        onResumeLoadData()
    }

    override fun onDestroy() {
        cancel()
        super.onDestroy()
    }

    private fun keyMove() {
        mViewModel?.mUiState?.value?.mGroupItems?.let {
            launch {
                if (!checkStorageSpaceEnough()) {
                    return@launch
                }
                val selectedFiles = HashMap<String, ArrayList<BaseFileBean>>()
                val groupList = ArrayList<ItemMovingGroup>()
                withContext(Dispatchers.Default) {
                    for (item in it) {
                        selectedFiles[item.mTitle] = item.getSelectedFile()
                        groupList.add(
                            ItemMovingGroup(item.mTitle, 0,
                                (selectedFiles[item.mTitle]?.size ?: 0))
                        )
                    }
                    // Because of the bug of COUIExpandableRecyclerView, if click the last item, probabilistically
                    // produce a same item in RecyclerView
                    selectedFiles.remove("")
                    groupList.removeAt(groupList.lastIndex)
                }
                if (mActivity == null) {
                    Log.e(TAG, "keyMove: mActivity == null")
                    return@launch
                }
                mKeyMoveActionThread = FileActionKeyMove(mActivity!!, selectedFiles)
                val keyMoveObserver = object : BaseFileActionObserver(mActivity!!) {
                    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
                        return when (result.first) {
                            ACTION_CANCELLED -> {
                                mMoveButton?.text = MyApplication.sAppContext.getString(com.filemanager.common.R.string.action_complete)
                                mIsMoveAchieve = true
                                mTitleTv?.text = MyApplication.sAppContext.getString(com.filemanager.common.R.string.string_transfer_interruption)
                                mSpaceTv?.visibility = View.GONE
                                mMoveAdapter?.setAchieve()
                                val size = if (result.second is Long) {
                                    result.second as Long
                                } else {
                                    0L
                                }
                                val sizeString = MyApplication.sAppContext.getString(
                                    com.filemanager.common.R.string.string_transfer_interruption_description,
                                        byteCountToDisplaySize(size)
                                )
                                mSizeTv?.text = if (isRtl()) {
                                    RTL_SYMBOL + sizeString + RTL_SYMBOL
                                } else {
                                    sizeString
                                }
                                mSizeTv?.visibility = View.VISIBLE
                                StatisticsUtils.onCommon(activity, StatisticsUtils.MOVE_TO_SDCARD_FAIL)
                                true
                            }
                            UPDATE_COUNT -> {
                                if (result.second is Pair<*, *>) {
                                    val temp = result.second as Pair<*, *>
                                    for (group in groupList) {
                                        if (group.mTitle == temp.first) {
                                            group.achieveCount = temp.second as Int
                                            break
                                        }
                                    }
                                    mMoveAdapter?.setData(groupList)
                                }
                                true
                            }
                            ACTION_DONE -> {
                                mMoveButton?.text = MyApplication.sAppContext.getString(com.filemanager.common.R.string.action_complete)
                                mIsMoveAchieve = true
                                mTitleTv?.text = MyApplication.sAppContext.getString(com.filemanager.common.R.string.string_transfer_success)
                                mSpaceTv?.visibility = View.GONE
                                val size = MyApplication.sAppContext.getString(
                                    com.filemanager.common.R.string.string_transfer_completed,
                                        byteCountToDisplaySize(result.second as Long)
                                )
                                mSizeTv?.text =
                                        if (isRtl()) {
                                            RTL_SYMBOL + size + RTL_SYMBOL
                                        } else {
                                            size
                                        }
                                mSizeTv?.visibility = View.VISIBLE
                                mMoveAdapter?.setAchieve()
                                false
                            }
                            else -> false
                        }
                    }
                }
                mKeyMoveActionThread?.execute(keyMoveObserver)
                mIsMoveAchieve = false
                mMoveButton?.text = MyApplication.sAppContext.getString(com.filemanager.common.R.string.dialog_cancel)
                if (mMoveAdapter == null) {
                    mMoveAdapter = KeyMoveAchieveAdapter()
                }
                mRecyclerView?.setAdapter(mMoveAdapter)
                mMoveAdapter?.setData(groupList)
                mRecyclerView?.isEnabled = false

                mTitleTv?.text = mActivity?.getString(com.filemanager.common.R.string.string_being_transferred)
                mSizeTv?.visibility = View.GONE
                mSpaceTv?.visibility = View.GONE
            }
        }
    }

    private suspend fun checkStorageSpaceEnough(): Boolean {
        val size: Long = withContext(Dispatchers.Default) {
            mViewModel?.getTotalSelectedSize() ?: 0L
        }
        val availableSdSize = getStorageAvailableSize(mExternalPath)
        if (size > availableSdSize) {
            mMoveButton?.isEnabled = false
            showStorageNotEnoughDialog()
            return false
        }
        return true
    }

    override fun getViewModel() = mViewModel

    override fun getRecyclerView() = mRecyclerView

    data class ItemMovingGroup(val mTitle: String, var achieveCount: Int,
                               val totalCount: Int)
}