/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File:FileShareObserver.kt
 * * Description:the lifecycle observer need to bind with the livedata about showing dialog
 * * Version:1.0
 * * Date :2020.1.9
 * * Author:liuzeming
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * * liuzeming    20200109   1.0       the lifecycle observer need to bind with the livedata about showing dialog
 ****************************************************************/
package com.filemanager.fileoperate.share

import android.app.Dialog
import android.content.Context
import android.view.ContextThemeWrapper
import com.filemanager.common.constants.CommonConstants.MAX_SEND_COUNT
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import com.filemanager.common.view.AlertDialogFactory
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.R

const val EMPTY_FOLDER_STATE = 0
const val TOO_MANY_FILES_STATE = 1
const val CAN_NOT_SEND_DRM_FILES = 2
const val SEND_ERROR = 3
const val TOO_LARGE = 4
const val SEND_ERROR_DFM_DISCONNECTED = 5

open class FileShareObserver(context: ContextThemeWrapper) : BaseFileActionObserver(context) {
    companion object {
        private const val TAG = "FileShareObserver"
    }

    private var emptyFolderDialog: Dialog? = null
    private var overCountDialog: Dialog? = null
    private var overSizeDialog: Dialog? = null

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        return when (result.first) {
            ACTION_FAILED -> {
                when (result.second) {
                    EMPTY_FOLDER_STATE -> {
                        Log.w(TAG, "onChanged: EMPTY_FOLDER_STATE")
                        emptyFolderDialog = AlertDialogFactory.showSingleTitleDialog(context, com.filemanager.common.R.string.toast_send_file_error)
                    }
                    TOO_MANY_FILES_STATE -> {
                        overCountDialog = AlertDialogFactory.showSingleTitleDialog(context,
                            context.getString(com.filemanager.common.R.string.toast_send_beyond_count_new, MAX_SEND_COUNT))
                    }
                    CAN_NOT_SEND_DRM_FILES -> CustomToast.showShort(
                            com.filemanager.common.R.string.string_drm_unable_to_send
                    )
                    SEND_ERROR_DFM_DISCONNECTED -> CustomToast.showShort(com.filemanager.common.R.string.share_failed_by_device_disconnect)
                    SEND_ERROR -> CustomToast.showShort(com.filemanager.common.R.string.toast_share_failture)
                    TOO_LARGE -> {
                        overSizeDialog =
                            AlertDialogFactory.showSingleTitleDialog(context, com.filemanager.common.R.string.toast_send_info_size_exceed_limit)
                    }
                }
                false
            }
            else -> false
        }
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || emptyFolderDialog?.isShowing ?: false
                || overCountDialog?.isShowing ?: false || overSizeDialog?.isShowing ?: false
    }
}