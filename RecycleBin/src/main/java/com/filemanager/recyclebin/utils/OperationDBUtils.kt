/***********************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * File:  - OperationDBUtils.java
 * Description: tool flow for operating database
 * Version: 1.0
 * Date : 2024/10/23
 * Author: <EMAIL>
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2024/10/23    1.0     create
 ****************************************************************/
package com.filemanager.recyclebin.utils

import android.content.ContentUris
import android.content.ContentValues
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteFullException
import android.database.sqlite.SQLiteOpenHelper
import android.net.Uri
import android.text.TextUtils
import com.filemanager.common.MyApplication
import com.filemanager.common.RecycleStore
import com.filemanager.common.utils.Log
import com.filemanager.recyclebin.db.DatabaseOpenHelper
import com.filemanager.recyclebin.db.RecycleBinExportHelper
import com.filemanager.recyclebin.db.RecycleBinRecordHelper
import com.filemanager.recyclebin.db.RecycleProvider.Companion.RECYCLE_AUTHORITY
import com.filemanager.recyclebin.db.RecycleProvider.Companion.ensureUriMatchForInsert
import com.filemanager.recyclebin.db.RecycleProvider.Companion.matchUriForModify
import com.filemanager.recyclebin.retriever.DataRetriever

object OperationDBUtils {
    const val TAG = "OperationRecycleDBUtils"
    private const val INTERNAL_FILES = 1
    private const val INTERNAL_FILES_ID = 2
    private const val INTERNAL_RECYCLE_ROOT = 3
    internal const val STATUS_SUCCESS = 0
    internal const val STATUS_ERROR = -1
    internal const val STATUS_NOT_ENOUGH_SPACE = -2

    @JvmStatic
    fun createSQLiteOpenHelper(uri: Uri?): SQLiteOpenHelper? {
        var internalOpenHelper: SQLiteOpenHelper? = null
        if (uri == null || uri.pathSegments.size < 1) {
            return null
        }
        val volumeName = uri.pathSegments[0]
        if (RecycleStore.VOLUME_INTERNAL == volumeName) {
            internalOpenHelper = DatabaseOpenHelper(MyApplication.appContext)
        }
        return internalOpenHelper
    }

    @JvmStatic
    fun beginTransaction(db: SQLiteDatabase?): SQLiteDatabase? {
        if (db != null) {
            db.beginTransaction()
            return db
        } else {
            return null
        }
    }

    @JvmStatic
    fun delete(
        uri: Uri,
        userWhere: String?,
        userWhereArgs: Array<String>?,
        db: SQLiteDatabase?,
        internalOpenHelper: SQLiteOpenHelper?
    ): Int {
        db?.let {
            if (internalOpenHelper is DatabaseOpenHelper) {
                return deleteInternal(uri, userWhere, userWhereArgs, db, internalOpenHelper)
            }
        }
        return -1
    }

    @JvmStatic
    fun insert(
        uri: Uri,
        values: ContentValues,
        db: SQLiteDatabase?,
        internalOpenHelper: SQLiteOpenHelper?
    ): Uri? {
        db?.let {
            if (internalOpenHelper is DatabaseOpenHelper) {
                return insertInternal(uri, values, it, internalOpenHelper)
            }
        }
        return null
    }

    @JvmStatic
    fun commit(db: SQLiteDatabase?, uri: Uri): Int {
        runCatching {
            setTransactionSuccessful(db)
            endTransaction(db)
            MyApplication.appContext.contentResolver?.notifyChange(uri, null, 0)
            return STATUS_SUCCESS
        }.onFailure { e ->
            if (e is SQLiteFullException) {
                Log.d(TAG, "commit recycleOperate to SQLiteDatabase fail, error is SQLiteFullException")
                return STATUS_NOT_ENOUGH_SPACE
            }
            Log.d(TAG, "commit recycleOperate to SQLiteDatabase fail")
        }
        return STATUS_ERROR
    }

    @JvmStatic
    fun setTransactionSuccessful(db: SQLiteDatabase?) {
        if (db != null) {
            db.setTransactionSuccessful()
        } else {
            Log.d(TAG, "the database is closed and the transaction cannot be committed")
        }
    }

    @JvmStatic
    fun endTransaction(db: SQLiteDatabase?) {
        if (db != null) {
            db.endTransaction()
        } else {
            Log.d(TAG, "the database is closed and the transaction cannot be committed")
        }
        db?.close()
    }

    @JvmStatic
    private fun insertInternal(
        uri: Uri,
        initialValues: ContentValues?,
        db: SQLiteDatabase,
        internalOpenHelper: DatabaseOpenHelper
    ): Uri? {
        if (!ensureUriMatchForInsert(uri)) {
            Log.e(TAG, "insertInternal invalid Uri parameter")
            return null
        }
        initialValues ?: return null
        if (!RecycleBinRecordHelper.ensureNonNulFileColumns(initialValues)) {
            return null
        }

        val recyclePath = initialValues.getAsString(RecycleStore.Files.FileColumns.RECYCLE_PATH)
        if (TextUtils.isEmpty(recyclePath)) {
            Log.e(TAG, "insertInternal recyclePath empty")
            return null
        }

        val originPath = initialValues.getAsString(RecycleStore.Files.FileColumns.ORIGIN_PATH)
        if (TextUtils.isEmpty(originPath)) {
            Log.e(TAG, "insertInternal originPath empty")
            return null
        }

        RecycleBinRecordHelper.computerMediaTypeValues(initialValues, recyclePath)
        internalOpenHelper.let { RecycleBinRecordHelper.computerDataValues(it, initialValues, recyclePath) }
        RecycleBinExportHelper.clearExportedParams(initialValues)
        val rowId = db.insert("files", null, initialValues)
        if (rowId <= 0) {
            Log.w(TAG, "insertInternal insert failed")
            return null
        }
        val newUri = ContentUris.withAppendedId(uri, rowId)
        val deleteParam = uri.getQueryParameter(RecycleStore.PARAM_RECYCLE_DATA)
        // move file to recyclebin
        if (RecycleStore.DELETE_FLAG == deleteParam) {
            val store = DataRetriever.INSTANCE
            val statusCode = store.moveToRecycleBin(originPath, recyclePath)
            Log.w(
                TAG,
                "insertInternal moveToRecycleBin failed $originPath, statusCode: $statusCode"
            )
        }
        return newUri
    }

    @JvmStatic
    private fun deleteInternal(
        uri: Uri,
        userWhere: String?,
        userWhereArgs: Array<String>?,
        db: SQLiteDatabase,
        internalOpenHelper: DatabaseOpenHelper?
    ): Int {
        if (internalOpenHelper == null) {
            Log.e(TAG, "deleteInternal getDatabaseHelper failed")
            return 0
        }

        val tableAndWhere = getTableAndWhere(uri, userWhere)
        if (tableAndWhere == null) {
            Log.e(TAG, "deleteInternal geTable failed")
            return 0
        }
        val count = db.delete(tableAndWhere.table, tableAndWhere.where, userWhereArgs)
        if (count > 0 && !db.inTransaction()) {
            val volumeName = RecycleStore.Files.getVolumeName(uri)
            val notifyUri = Uri.parse("content://$RECYCLE_AUTHORITY/$volumeName")
            MyApplication.appContext.contentResolver?.notifyChange(notifyUri, null)
        }
        return count
    }

    @JvmStatic
    private fun getTableAndWhere(uri: Uri, userWhere: String?): TableAndWhere? {
        val match = matchUriForModify(uri)
        val out = TableAndWhere()
        var where = ""
        when (match) {
            INTERNAL_FILES_ID -> {
                out.table = "files"
                where = RecycleStore.Files.FileColumns.RECYCLE_ID + " = " + uri.pathSegments[2]
            }
            INTERNAL_FILES -> out.table = "files"
            else -> return null
        }
        // Add in the user requested WHERE clause, if needed
        if (userWhere.isNullOrEmpty()) {
            out.where = where
        } else if (where.isNotEmpty()) {
            out.where = "$where AND ($userWhere)"
        } else {
            out.where = userWhere
        }
        return out
    }

    private data class TableAndWhere(
        var table: String = "",
        var where: String = ""
    )
}