apply from: rootProject.file("exportedLibs/scripts/prebuiltPublish.gradle")

configurations.maybeCreate("default")
final aarArtifact = artifacts.add("default", file('snapshot-20210524.aar'))

/**
 * 更新aar文件时，务必同步更新以下版本号配置，否则无法发布aar至maven库.
 * Release maven库不支持直接发布，Snapshot maven库必须发布版本号为*-SNAPSHOT的，
 * 故此处版本号暂时先添加了-SNAPSHOT后缀
 */
prebuiltPublish {
    prebuiltArtifact = aarArtifact
    groupId = "com.wps.sdk"
    artifactId = "snapshot"
    version = "20210524-SNAPSHOT"

    targetMaven {
        url = prop_oppoMavenUrlSnapshots
        username = sonatypeUsername
        password = sonatypePassword
        allowInsecureProtocol = true
    }
}

tasks.register("clean", Delete) {
    delete project.buildDir
}
