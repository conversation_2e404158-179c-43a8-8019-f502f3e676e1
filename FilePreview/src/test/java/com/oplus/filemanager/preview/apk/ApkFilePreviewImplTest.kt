package com.oplus.filemanager.preview.apk

import android.content.Context
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite
import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * ApkFilePreviewImpl的单元测试类
 * 用于测试Apk文件预览功能的实现逻辑
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class ApkFilePreviewImplTest {

    // 使用MockK框架模拟LifecycleOwner对象
    @MockK
    private lateinit var mockLifecycleOwner: LifecycleOwner

    // 使用MockK框架模拟FilePreviewViewModel对象
    @MockK
    private lateinit var mockPreviewModel: FilePreviewViewModel

    // 使用RelaxedMockK框架模拟PreviewFileInfoSuite对象(宽松模拟)
    @RelaxedMockK
    private lateinit var mockPreviewFileInfoSuite: PreviewFileInfoSuite

    // 使用RelaxedMockK框架模拟View对象(宽松模拟)
    @RelaxedMockK
    private lateinit var mockView: View

    // 使用RelaxedMockK框架模拟日期TextView对象(宽松模拟)
    @RelaxedMockK
    private lateinit var mockDateTv: AppCompatTextView

    // 使用RelaxedMockK框架模拟大小TextView对象(宽松模拟)
    @RelaxedMockK
    private lateinit var mockSizeTv: AppCompatTextView

    // 使用RelaxedMockK框架模拟名称容器TextViewSnippet对象(宽松模拟)
    @RelaxedMockK
    private lateinit var mockNameContainer: TextViewSnippet

    // 使用RelaxedMockK框架模拟Context对象(宽松模拟)
    @RelaxedMockK
    private lateinit var mockContext: Context

    // 被测对象
    private lateinit var apkFilePreview: ApkFilePreviewImpl

    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 模拟MyApplication单例
     * 3. 设置模拟行为
     * 4. 创建被测对象实例
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mockkObject(MyApplication)
        // 设置MyApplication的模拟上下文
        every { MyApplication.sAppContext } returns mockContext
        // 设置预览模型加载文件的模拟行为
        every { mockPreviewModel.loadPreviewFile(any(), any()) } just Runs

        // 创建被测对象实例
        apkFilePreview = ApkFilePreviewImpl(mockLifecycleOwner, mockPreviewModel)
    }

    /**
     * 测试后的清理方法
     * 解除所有MockK模拟
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试setFileInfoStruct方法
     * 验证是否能正确初始化视图组件
     */
    @Test
    fun `test setFileInfoStruct should initialize views`() {
        // 设置模拟视图查找行为
        every { mockView.findViewById<AppCompatTextView>(R.id.preview_remote_time_info) } returns mockDateTv
        every { mockView.findViewById<AppCompatTextView>(R.id.preview_remote_size_info) } returns mockSizeTv
        every { mockView.findViewById<TextViewSnippet>(R.id.preview_remote_title) } returns mockNameContainer

        // 执行被测方法
        apkFilePreview.setFileInfoStruct(mockView)

        // 验证是否正确查找了各个视图组件
        verify { mockView.findViewById<AppCompatTextView>(R.id.preview_remote_time_info) }
        verify { mockView.findViewById<AppCompatTextView>(R.id.preview_remote_size_info) }
        verify { mockView.findViewById<TextViewSnippet>(R.id.preview_remote_title) }
    }

    /**
     * 测试attachToContainer方法
     * 验证首次附加容器时是否设置了默认图标并开始观察数据
     */
    @Test
    fun `test attachToContainer should set default icon and start observing when not observed`() {
        // 执行被测方法
        apkFilePreview.attachToContainer(mockPreviewFileInfoSuite)

        // 验证是否设置了默认图标
        verify { mockPreviewFileInfoSuite.setFileIcon(any<Int>()) }
        // 验证是否清空了文件消息
        verify { mockPreviewFileInfoSuite.setFileMessage(null) }
        // 验证是否调用了加载预览文件方法
        verify { mockPreviewModel.loadPreviewFile(mockLifecycleOwner, any()) }
    }

    /**
     * 测试attachToContainer方法
     * 验证已经观察过数据后再次附加容器时不会重复观察
     */
    @Test
    fun `test attachToContainer should not observe again if already observed`() {
        // 第一次调用(会触发观察)
        apkFilePreview.attachToContainer(mockPreviewFileInfoSuite)
        // 第二次调用(不应重复触发观察)
        apkFilePreview.attachToContainer(mockPreviewFileInfoSuite)

        // 验证加载预览文件方法只被调用了一次
        verify(exactly = 1) { mockPreviewModel.loadPreviewFile(any(), any()) }
    }
}