/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - RecycleFileManager.java
 * Description: interface for delete file, restore and delete file forever in recyclebin
 * Version: 1.0
 * Date : 2020/02/24
 * Author: Jiafei.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/02/24    1.0     create
 ****************************************************************/

package com.filemanager.recyclebin

import android.content.Context
import android.content.DialogInterface
import android.net.Uri
import android.view.Gravity
import androidx.activity.ComponentActivity
import androidx.annotation.VisibleForTesting
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.wrapper.RecycleBinLiveData
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.getBottomAlertDialogWindowAnimStyle
import com.filemanager.common.utils.Log
import com.filemanager.recyclebin.operation.BaseOperation.Companion.STATUS_ERROR
import com.filemanager.recyclebin.operation.BaseOperation.OperationParameters
import com.filemanager.recyclebin.operation.BaseOperation.OperationResult
import com.filemanager.recyclebin.operation.EraseOperation
import com.filemanager.recyclebin.operation.RecycleBinOperationListener
import com.filemanager.recyclebin.operation.RestoreOperation
import com.filemanager.recyclebin.operation.ScanOperation
import com.filemanager.recyclebin.operation.listener.EraseOperationListener
import com.filemanager.recyclebin.operation.listener.RestoreOperationListener
import com.filemanager.recyclebin.operation.listener.ScanOperationListener
import com.oplus.recyclebin.R
import java.lang.ref.WeakReference

class RecycleFileManager private constructor() {
    companion object {
        private const val TAG = "RecycleFileManager"
        val INSTANCE: RecycleFileManager by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            RecycleFileManager()
        }

        private fun innerEraseRecycle(context: ComponentActivity, parameters: OperationParameters, listener: RecycleBinOperationListener?) {
            val operation = EraseOperation(context, EraseOperationListener(context, listener))
            if (parameters.mUris != null) {
                if (operation.addUris(parameters.mUris)) {
                    operation.execute()
                }
            } else if (parameters.mBaseFiles != null) {
                if (operation.addFileBeans(parameters.mBaseFiles)) {
                    operation.execute()
                }
            } else {
                Log.w(TAG, "innerEraseRecycle invalid parameters")
            }
        }
    }

    /**
     * exported interface for delete file or folder
     * @param context context for getResouce,dialog create and so on
     * @param files Operation Files Count.
     * @param listener callback after operation completed.
     */
    fun recycleFiles(context: ComponentActivity?, files: ArrayList<BaseFileBean>?, listener: RecycleBinOperationListener?,
                     paramState: Int = 0, isSelectedAll: Boolean) {
        val parameters = RecycleFileParameters(mIsSelectedAll = isSelectedAll, mParamState = paramState)
        recycleFiles(context, files, listener, parameters)
    }

    /**
     * exported interface for delete file or folder
     * @param context context for getResource,dialog create and so on
     * @param fileBeans file list for delete.
     * @param listener callback after operation completed.
     * @param parameters recycle parameters.
     */
    fun recycleFiles(
        context: ComponentActivity?,
        fileBeans: ArrayList<BaseFileBean>?,
        listener: RecycleBinOperationListener?,
        parameters: RecycleFileParameters
    ): ScanOperationListener? {
        if ((context == null) || fileBeans.isNullOrEmpty()) {
            Log.w(TAG, "recycleFiles invalid parameters")
            val totalCount = fileBeans?.size ?: 0
            listener?.onOperationCompleted(RecycleBinLiveData.RECYCLE_RECYCLE, OperationResult(totalCount, totalCount, STATUS_ERROR))
            return null
        }
        val scanOperationListener = ScanOperationListener(context, listener, parameters)
        val operation = ScanOperation(context, scanOperationListener, parameters.mParamState)
        if (operation.addFileBeans(fileBeans)) {
            operation.execute()
        }
        return scanOperationListener
    }

    /**
     * exported interface for delete file or folder
     * @param context context for getResouce,dialog create and so on
     * @param paths  paths for delete.
     * @param listener callback after operation completed.
     */
    fun recyclePaths(context: ComponentActivity?, paths: ArrayList<String>?, listener: RecycleBinOperationListener?) {
        recyclePaths(context, paths, listener, 0, isShowConfigDialog = true)
    }

    /**
     * exported interface for delete file or folder
     * @param context context for getResouce,dialog create and so on
     * @param paths  paths for delete.
     * @param listener callback after operation completed.
     * @param category if delete record in MediaStore if catetory in audio,image,video,doc,apk,search and recent
     */
    fun recyclePaths(context: ComponentActivity?, paths: ArrayList<String>?, listener: RecycleBinOperationListener?,
                     category: Int, isShowConfigDialog: Boolean) {
        if ((context == null) || paths.isNullOrEmpty()) {
            Log.w(TAG, "recyclePaths invalid parameters")
            val totalCount = paths?.size ?: 0
            listener?.onOperationCompleted(RecycleBinLiveData.RECYCLE_RECYCLE, OperationResult(totalCount, totalCount, STATUS_ERROR))
            return
        }
        val parameters = RecycleFileParameters(mCategory = category, mIsShowConfigDialog = isShowConfigDialog)
        val operation = ScanOperation(context, ScanOperationListener(context, listener, parameters))
        if (operation.addPaths(paths)) {
            operation.execute()
        }
    }

    /**
     * exported interface for restore file or folder in recycle bin
     * @param[context] context for getResouce,dialog create and so on
     * @param[files]  file list for restore.
     * @param[listener] callback after operation completed.
     */
    fun restoreBaseFiles(
        context: ComponentActivity?,
        files: ArrayList<BaseFileBean>?,
        listener: RecycleBinOperationListener?,
        allSize: Int
    ) {
        if ((context == null) || files.isNullOrEmpty()) {
            Log.w(TAG, "restoreFiles invalid parameters")
            val totalCount = files?.size ?: 0
            listener?.onOperationCompleted(RecycleBinLiveData.RECYCLE_RESTORE, OperationResult(totalCount, totalCount, STATUS_ERROR))
            return
        }
        showRestoreConfirmDialog(context, files, allSize, listener)
    }

    private fun showRestoreConfirmDialog(
        activity: ComponentActivity?,
        files: ArrayList<BaseFileBean>?,
        allSize: Int = 0,
        listener: RecycleBinOperationListener?
    ) {
        val context = activity ?: kotlin.run {
            Log.w(TAG, "showRestoreConfirmDialog: context is null")
            return
        }
        val selectSize = files?.size ?: 0
        val positiveString = when {
            (selectSize == 1) -> context.getString(com.filemanager.common.R.string.menu_recycle_restore)
            (selectSize >= allSize) -> {
                context.getString(com.filemanager.common.R.string.restore_all)
            }
            else -> {
                context.getString(com.filemanager.common.R.string.menu_recycle_restore)
            }
        }
        val builder = COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setWindowGravity(Gravity.BOTTOM)
            .setTitle(getRestoreTitle(context, selectSize, allSize))
            .setPositiveButton(positiveString) { p0, p1 ->
                Log.d(TAG, "showRestoreConfirmDialog: positive button clicked")
                onConfirmRestore(activity, files, listener)
            }
            .setNegativeButton(com.filemanager.common.R.string.button_cancel_text, null)
        builder.show()
    }

    @VisibleForTesting
    fun getRestoreTitle(context: Context, selectSize: Int, allSize: Int): String {
        val title = when {
            (selectSize == 1) -> context.resources.getString(com.filemanager.common.R.string.restore_one_item_title)
            (selectSize >= allSize) -> context.getString(com.filemanager.common.R.string.restore_all_item_title)
            else -> context.resources.getQuantityString(com.filemanager.common.R.plurals.restore_some_item_title, selectSize, selectSize)
        }
        return title
    }

    private fun onConfirmRestore(context: ComponentActivity, files: ArrayList<BaseFileBean>?, listener: RecycleBinOperationListener?) {
        val operation = RestoreOperation(context, RestoreOperationListener(context, listener))
        if (operation.addFileBeans(files)) {
            operation.execute()
        }
    }


    /**
     * exported interface for restore file or folder in recycle bin
     * @param context context for getResouce,dialog create and so on
     * @param uris  uri list for restore.
     * @param listener callback after operation completed.
     */
    fun restoreUris(context: ComponentActivity?, uris: ArrayList<Uri>?, listener: RecycleBinOperationListener?) {
        if ((context == null) || uris.isNullOrEmpty()) {
            Log.w(TAG, "restoreUris invalid parameters")
            val totalCount = uris?.size ?: 0
            listener?.onOperationCompleted(RecycleBinLiveData.RECYCLE_RESTORE, OperationResult(totalCount, totalCount, STATUS_ERROR))
            return
        }

        val operation = RestoreOperation(context, RestoreOperationListener(context, listener))
        if (operation.addUris(uris)) {
            operation.execute()
        }
    }

    /**
     * note: query recycle information with path take too much time,so this interface is forbid invoked
     * exported interface for restore file or folder in recycle bin
     * @param context context for getResource,dialog create and so on
     * @param paths  path list for restore.
     * @param listener callback after operation completed.
     */
    private fun restorePaths(context: ComponentActivity?, paths: ArrayList<String>?, listener: RecycleBinOperationListener?) {
        if ((context == null) || paths.isNullOrEmpty()) {
            Log.w(TAG, "restorePaths invalid parameters")
            val totalCount = paths?.size ?: 0
            listener?.onOperationCompleted(RecycleBinLiveData.RECYCLE_RESTORE, OperationResult(totalCount, totalCount, STATUS_ERROR))
            return
        }

        val operation = RestoreOperation(context, RestoreOperationListener(context, listener))
        if (operation.addPaths(paths)) {
            operation.execute()
        }
    }

    /**
     * exported interface for delete file or folder in recycle bin
     * @param context context for getResouce,dialog create and so on
     * @param uris  uri list for delete.
     * @param listener callback after operation completed.
     */
    fun deleteUris(context: ComponentActivity?, uris: ArrayList<Uri>?, listener: RecycleBinOperationListener?) {
        if ((context == null) || uris.isNullOrEmpty()) {
            Log.w(TAG, "deleteUris invalid parameters")
            val totalCount = uris?.size ?: 0
            listener?.onOperationCompleted(RecycleBinLiveData.RECYCLE_DELETE, OperationResult(totalCount, totalCount, STATUS_ERROR))
            return
        }

        if (context.isFinishing || context.isDestroyed) {
            return
        }
        val parameters = RecycleFileParameters()
        var scanOperation = ScanOperation(context, ScanOperationListener(context, listener, parameters))
        if (scanOperation.addUris(uris)) {
            scanOperation.execute()
        }
    }

    /**
     * note: query recycle information with path take too much time,so this interface is forbid invoked
     * exported interface for delete file or folder in recycle bin
     * @param context context for getResouce,dialog create and so on
     * @param paths  path list for delete.
     * @param listener callback after operation completed.
     */
    private fun deletePaths(context: ComponentActivity?, paths: ArrayList<String>?, listener: RecycleBinOperationListener?) {
        if ((context == null) || paths.isNullOrEmpty()) {
            Log.w(TAG, "deletePaths invalid parameters")
            val totalCount = paths?.size ?: 0
            listener?.onOperationCompleted(RecycleBinLiveData.RECYCLE_DELETE, OperationResult(totalCount, totalCount, STATUS_ERROR))
            return
        }

        if (context.isFinishing || context.isDestroyed) {
            return
        }
        val parameters = RecycleFileParameters()
        var scanOperation = ScanOperation(context, ScanOperationListener(context, listener, parameters))
        if (scanOperation.addPaths(paths)) {
            scanOperation.execute()
        }
    }

    /**
     * exported interface for delete file or folder in recycle bin
     * @param context context for getResouce,dialog create and so on
     * @param fileBeans  filebean list for delete.
     * @param listener callback after operation completed.
     */
    fun deleteBaseFileBeans(context: ComponentActivity?, fileBeans: ArrayList<BaseFileBean>?, listener: RecycleBinOperationListener?) {
        if ((context == null) || fileBeans.isNullOrEmpty()) {
            Log.w(TAG, "deleteFiles invalid parameters")
            val totalCount = fileBeans?.size ?: 0
            listener?.onOperationCompleted(RecycleBinLiveData.RECYCLE_DELETE, OperationResult(totalCount, totalCount, STATUS_ERROR))
            return
        }

        if (context.isFinishing || context.isDestroyed) {
            Log.w(TAG, "context not enable")
            return
        }

        val parameters = OperationParameters(fileBeans)
        showEraseConfirmDialog(context, parameters, listener)
    }

    /**
     * show confirm dialog to alter user that file prompt
     * @param activity context for getResource,dialog create and so on
     * @param parameters  parameters for delete.
     * @param listener callback after operation completed.
     */
    private fun showEraseConfirmDialog(activity: ComponentActivity, parameters: OperationParameters,
                                       listener: RecycleBinOperationListener?) {
        val confirmListener = ConfirmDialogListener(activity, listener, parameters)
        COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Bottom)
                .setWindowGravity(Gravity.BOTTOM)
                .setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(activity))
                .setOnCancelListener(confirmListener)
                .setNeutralButton(activity.resources.getString(com.filemanager.common.R.string.menu_recycle_delete_forever), confirmListener)
                .setNegativeButton(com.filemanager.common.R.string.alert_dialog_no, confirmListener).show()
    }

    private class ConfirmDialogListener(activity: ComponentActivity, val mListener: RecycleBinOperationListener?, val mParameters: OperationParameters)
        : DialogInterface.OnClickListener, DialogInterface.OnCancelListener {
        private val mWeakActivity = WeakReference(activity)

        override fun onClick(dialog: DialogInterface?, which: Int) {
            Log.w(TAG, "onClick which: $which")
            when (which) {
                DialogInterface.BUTTON_NEUTRAL -> {
                    mWeakActivity.get()?.let {
                        innerEraseRecycle(it, mParameters, mListener)
                    }
                }
            }
            dialog?.dismiss()
        }

        override fun onCancel(dialog: DialogInterface?) {
            dialog?.dismiss()
        }
    }

    data class RecycleFileParameters constructor(
        val mIsShowConfigDialog: Boolean = true,
        val mParamState: Int = 0,
        val mCategory: Int = 0,
        val mIsSelectedAll: Boolean = false,
        val showProgress: Boolean = true // 是否显示进度
    )
}
