/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudDocsPagingSourceFactory
 ** Description : 创建本地数据库paging Source 的工厂
 ** Version     : 1.0
 ** Date        : 2024/01/08 15:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/01/08       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.source

import androidx.paging.PagingSource
import com.oplus.filemanager.drivebrowser.data.utils.CloudFileSortTypeUtils
import com.oplus.filemanager.provider.DriveFileDBHelper
import com.oplus.filemanager.provider.store.CloudFileDriveStore
import com.oplus.filemanager.room.model.DriveFileEntity

object CloudDocsPagingSourceFactory {

    /**
     * 创建 paging source
     * @param query
     */
    @JvmStatic
    fun create(query: CloudDocsQuery): PagingSource<Int, DriveFileEntity> {
        val order = getOrder(query.source, query.sortType)
        return DriveFileDBHelper.getDocuments(query.source, query.folderId, order, query.asc == 1)
    }

    /**
     * 获取排序类型
     */
    @JvmStatic
    fun getOrder(source: String, sortType: String): String {
        return if (source == DriveFileEntity.SOURCE_TYPE_TENCENT) { // 腾讯
            getTencentDocsOrderColumn(sortType)
        } else { // 金山
            getKingSoftDocsOrderColumn(sortType)
        }
    }

    /**
     * 获取腾讯文档对应的排序字段
     * @param sortType 排序类型
     */
    @JvmStatic
    fun getTencentDocsOrderColumn(sortType: String): String {
        return when (sortType) {
            // 打开时间
            CloudFileSortTypeUtils.TENCENT_DOCS_BROWSE -> CloudFileDriveStore.Columns.LAST_BROWSER_TIME
            // 修改时间
            CloudFileSortTypeUtils.TENCENT_DOCS_MODIFY -> CloudFileDriveStore.Columns.LAST_MODIFY_TIME
            // 标题
            CloudFileSortTypeUtils.TENCENT_DOCS_TITLE -> CloudFileDriveStore.Columns.NAME
            else -> CloudFileDriveStore.Columns.LAST_BROWSER_TIME
        }
    }

    /**
     * 获取金山文档文档对应的排序字段
     * @param sortType 排序类型
     */
    @JvmStatic
    fun getKingSoftDocsOrderColumn(sortType: String): String {
        return when (sortType) {
            // 修改时间
            CloudFileSortTypeUtils.KDOCS_MODIFY_TIME -> CloudFileDriveStore.Columns.LAST_MODIFY_TIME
            // 大小
            CloudFileSortTypeUtils.KDOCS_SIZE -> CloudFileDriveStore.Columns.SIZE
            // 标题
            CloudFileSortTypeUtils.KDOCS_NAME -> CloudFileDriveStore.Columns.NAME
            else -> CloudFileDriveStore.Columns.LAST_MODIFY_TIME
        }
    }
}