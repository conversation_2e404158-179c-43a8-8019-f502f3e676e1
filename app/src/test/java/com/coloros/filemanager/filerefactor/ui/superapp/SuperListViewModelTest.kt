/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.superapp
 * * Version     : 1.0
 * * Date        : 2020/9/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.filerefactor.ui.superapp

import com.coloros.filemanager.BaseTest
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test

@Ignore
class SuperListViewModelTest : BaseTest() {
    private val mSuperListViewModel = com.filemanager.superapp.ui.superapp.SuperListViewModel()

    @Test
    @Throws(Exception::class)
    fun get_current_span_count_when_call_fun_getCurrentSpanCount() {
        mSuperListViewModel.mTabPosition = Constants.TAB_ALL
        mSuperListViewModel.mBrowseModeState = KtConstants.SCAN_MODE_LIST
        Assert.assertEquals(1, mSuperListViewModel.getCurrentSpanCount())
        mSuperListViewModel.mBrowseModeState = KtConstants.SCAN_MODE_GRID
        Assert.assertEquals(3, mSuperListViewModel.getCurrentSpanCount())
        mSuperListViewModel.mTabPosition = Constants.TAB_IMAGE
        Assert.assertEquals(4, mSuperListViewModel.getCurrentSpanCount())
        mSuperListViewModel.mTabPosition = Constants.TAB_VIDEO
        Assert.assertEquals(4, mSuperListViewModel.getCurrentSpanCount())
        mSuperListViewModel.mTabPosition = Constants.TAB_DOCUMENT
        Assert.assertEquals(3, mSuperListViewModel.getCurrentSpanCount())
        mSuperListViewModel.mTabPosition = Constants.TAB_OTHER
        Assert.assertEquals(3, mSuperListViewModel.getCurrentSpanCount())

    }
}