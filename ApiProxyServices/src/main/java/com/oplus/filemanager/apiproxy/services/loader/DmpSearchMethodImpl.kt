/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DmpSearchMethodImpl
 ** Description : dmp Search method impl
 ** Version     : 1.0
 ** Date        : 2025/02/19 10:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2025/02/19       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services.loader

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.net.Uri
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.SearchDMPFileWrapper
import com.filemanager.common.utils.DmpUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.closeQuietly
import com.oplus.filemanager.apiproxy.services.data.SearchResultData
import com.oplus.filemanager.apiproxy.services.utils.SearchChecker
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi

class DmpSearchMethodImpl(context: Context) : SearchMethod(context) {

    companion object {
        private const val TAG = "DmpSearchMethodImpl"
    }

    private val dmpSearchApi = Injector.injectFactory<IDmpSearchApi>()


    /**
     * 中子搜索只支持多个字符搜索和搜索doc，excel,ppt几种类型
     */
    override fun isSupport(keyword: String, fileType: Int): Boolean {
        val isSimpleWord = DmpUtils.getSimpleWordValue(keyword)
        val isSupportType = SearchChecker.isSupportDmpSearch(fileType)
        Log.d(TAG, "isSupport isSimpleWord:$isSimpleWord isSupportType:$isSupportType")
        return !isSimpleWord && isSupportType
    }

    @Suppress("TooGenericExceptionCaught")
    override fun handleSearch(contentResolver: ContentResolver, keyword: String, fileType: Int): List<BaseFileBean> {
        Log.d(TAG, "handleSearch start")
        code = SearchChecker.CODE_OK
        val list = mutableListOf<BaseFileBean>()

        dmpSearchApi?.initClient()
        val cursor = dmpSearchApi?.getCursor(keyword)
        Log.i(TAG, "handleSearch cursor count ${cursor?.count}")
        cursor?.let {
            try {
                while (cursor.moveToNext()) {
                    val item = createFromCursor(cursor, null)
                    item?.let {
                        list.add(it)
                    }
                }
                code = SearchChecker.CODE_OK
            } catch (ex: Exception) {
                Log.e(TAG, "handleSearch exception", ex)
                code = SearchChecker.CODE_QUERY_ERROR
            } finally {
                cursor.closeQuietly()
            }
        }
        Log.d(TAG, "handleSearch end: ${list.size}")
        return list
    }


    fun createFromCursor(cursor: Cursor, uri: Uri?): BaseFileBean? {
        val index = cursor.getColumnIndex(SearchDMPFileWrapper.ABSOLUTE_PATH_INDEX)
        val absolutePath = cursor.getString(index)
        return if (isIgnoredPath(absolutePath)) {
            null
        } else {
            SearchDMPFileWrapper(cursor, uri)
        }
    }

    override fun classifyItem(list: List<BaseFileBean>, searchResult: SearchResultData) {
        list.forEach {
            searchResult.classifyDMPSearchItem(it, keyword)
        }
    }
}