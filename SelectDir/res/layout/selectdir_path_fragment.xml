<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coordinator_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:splitMotionEvents="false">

    <com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller
        android:id="@+id/fastScroller"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:common_track_marginBottom="@dimen/ftp_text_margin_bottom"
        app:common_track_marginEnd="@dimen/base_album_fastscroller_margin_end"
        app:common_track_marginTop="@dimen/base_album_recyclerview_padding_top">

        <com.filemanager.common.view.FileManagerRecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller>

    <com.google.android.material.appbar.COUIDividerAppBarLayout
        android:id="@+id/appbar"
        style="@style/CommonAppBarStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_transparent"
        android:clickable="true"
        android:focusable="true"
        android:paddingLeft="@dimen/selectdir_path_appbar_padding_start_and_end"
        android:paddingRight="@dimen/selectdir_path_appbar_padding_start_and_end"
        app:elevation="@dimen/toolbar_elevation">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:background="@null"
            android:layout_width="match_parent"
            style="@style/COUIToolBarInAppBarLayoutStyle"
            app:titleCenter="false"
            app:supportTitleTextAppearance="@style/textAppearanceSecondTitle"/>

        <com.filemanager.common.view.BrowserPathBar
            android:id="@+id/path_bar"
            android:layout_width="match_parent"
            android:minHeight="@dimen/dimen_36dp"
            android:layout_height="wrap_content"
            android:focusable="false" />

    </com.google.android.material.appbar.COUIDividerAppBarLayout>

    <FrameLayout
        android:id="@+id/select_button_parent"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:minHeight="@dimen/operation_btn_background_height"
        android:layout_gravity="bottom"
        android:background="?attr/couiColorBackgroundElevated">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_background_height"
            android:layout_gravity="top"
            android:alpha="0"
            android:background="?attr/couiColorDivider"
            android:id="@+id/button_divider"
            android:forceDarkAllowed="false" />

        <com.coui.appcompat.button.COUIButton
            android:id="@+id/select_button"
            style="@style/Widget.COUI.Button.Large.ButtonNew"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:contentDescription="@string/copy_selected_target"
            android:maxWidth="@dimen/coui_single_larger_btn_width"
            android:paddingVertical="@dimen/dimen_2dp"
            android:text="@string/copy_selected_target" />
    </FrameLayout>
    <ViewStub
        android:id="@+id/common_permission_empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/couiColorBackgroundElevated"
        android:layout="@layout/permission_common_view_layout" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>