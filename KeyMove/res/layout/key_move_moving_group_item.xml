<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:descendantFocusability="beforeDescendants"
    android:minHeight="?android:attr/listPreferredItemHeightSmall">

    <com.coui.appcompat.progressbar.COUILoadingView
        android:id="@+id/loading_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/default_margin"
        android:text="@null" />

    <ImageView
        android:id="@+id/result_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/default_margin"
        android:contentDescription="@null"
        android:scaleType="centerInside"
        android:src="@drawable/success_result" />

    <ImageView
        android:id="@+id/group_file_icon"
        android:layout_width="@dimen/main_image_width"
        android:layout_height="@dimen/main_image_height"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/default_margin"
        android:contentDescription="@null"
        android:focusable="false" />

    <LinearLayout
        android:id="@+id/group_title"
        android:layout_width="@dimen/akeytomove_choice_group_title_width"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/file_list_item_info_margin"
        android:layout_marginTop="@dimen/file_list_right_layout_margin_vertical"
        android:layout_marginBottom="@dimen/file_list_right_layout_margin_vertical"
        android:layout_toEndOf="@id/group_file_icon"
        android:orientation="vertical">

        <TextView
            android:id="@+id/group_file_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/akeytomove_list_item_text_margin"
            android:ellipsize="end"
            android:focusable="false"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceLarge" />

        <TextView
            android:id="@+id/group_file_detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/file_list_item_detail_margin_top"
            android:ellipsize="end"
            android:focusable="false"
            android:singleLine="true"
            android:textAppearance="?android:attr/textAppearanceSmall"
            android:textColor="@color/black_30_percent" />
    </LinearLayout>

</RelativeLayout>