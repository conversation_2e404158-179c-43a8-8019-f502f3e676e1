/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Response
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/1 17:06
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/1       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.convert

import com.oplus.fileservice.operate.internal.OperateStatusCode

open class Response

data class OperationResult(
    val success: Bo<PERSON>an,
    val statusCode: OperateStatusCode = OperateStatusCode.DoNothing,
    val description: String = ""
) : Response() {
    override fun toString(): String =
        "[Operation Result: success = $success - stateCode = $statusCode - Description = $description]"
}