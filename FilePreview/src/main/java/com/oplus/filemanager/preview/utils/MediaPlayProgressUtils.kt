/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - MediaPlayProgressUtils.kt
 * Description:
 *     The utils to handle media file playing progress.
 *
 * Version: 1.0
 * Date: 2024-10-09
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-10-09   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.utils

import android.view.View
import androidx.annotation.StringRes
import kotlin.math.roundToInt

private const val MILLISECONDS_PRE_SECOND = 1000L
private const val SECONDS_PRE_MINUTE = 60
private const val SINGLE_NUM_THRESHOLD = 10
private const val PROGRESS_POSITION_RATIO = 100

internal fun Long.toMediaPlayProgress(): Int = (this.toFloat() / PROGRESS_POSITION_RATIO).roundToInt()

internal fun Int.toMediaPlayPosition(): Long = (this * PROGRESS_POSITION_RATIO).toLong()

internal fun Long.formatToMediaPlayTime(): String {
    val secondsTime = (this.toFloat() / MILLISECONDS_PRE_SECOND).roundToInt()
    val minutesPart = secondsTime / SECONDS_PRE_MINUTE
    val secondsPart = secondsTime % SECONDS_PRE_MINUTE
    val minutesStr =
        if (minutesPart < SINGLE_NUM_THRESHOLD) "0$minutesPart" else "$minutesPart"
    val secondsStr =
        if (secondsPart < SINGLE_NUM_THRESHOLD) "0$secondsPart" else "$secondsPart"
    return "$minutesStr:$secondsStr"
}

internal fun View.setContentDescription(@StringRes stringId: Int) {
    contentDescription = context.getString(stringId)
}