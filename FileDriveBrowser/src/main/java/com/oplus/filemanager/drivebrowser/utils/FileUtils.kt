/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/8/10 10:23
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/8/10       1.0      create
 ***********************************************************************/
@file:JvmName("FileUtils")
package com.oplus.filemanager.drivebrowser.utils

import android.annotation.SuppressLint
import com.filemanager.common.utils.Log
import okhttp3.ResponseBody
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.RandomAccessFile
import java.lang.IllegalArgumentException
import java.security.MessageDigest

private const val TAG = "FileUtils"

private const val BYTE_ARRAY_SIZE = 4096

private const val OX_FF = 0xff

private const val BYTE_ARRAY_SIZE_10240 = 10240

private const val TEMP_DOWNLOAD_FILE_PREFIX = ".tmp_"

@Suppress("TooGenericExceptionCaught")
fun writeFileToDisk(responseBody: ResponseBody, targetPath: String, position: Long = 0L): Boolean {
    val file = File(targetPath)
    Log.d(TAG, "writeFileToDisk")
    if (file.exists()) {
        Log.d(TAG, "download -> file size = ${file.length()}")
    }
    var writeResult = false
    try {
        writeResult = writeFile(responseBody, file, position)
    } catch (e: Exception) {
        Log.e(TAG, "download -> error = $e")
    } finally {
        responseBody.close()
    }
    return writeResult
}

fun writeFile(responseBody: ResponseBody, file: File, position: Long = 0L): Boolean {
    Log.d(TAG, "writeFile")
    var writeFileResult = false
    file.parentFile?.let { parentFile ->
        if (!parentFile.exists()) {
            Log.d(TAG, "writeFile -> parentFile not exists")
            parentFile.mkdirs()
        }
        val tempFile = File(parentFile, TEMP_DOWNLOAD_FILE_PREFIX + file.name)
        val randomAccessFile = RandomAccessFile(tempFile, "rw")
        randomAccessFile.seek(position)
        val buffer = ByteArray(BYTE_ARRAY_SIZE)
        var len: Int
        var record = 0
        while (responseBody.byteStream().read(buffer).also { len = it } != -1) {
            randomAccessFile.write(buffer, 0, len)
            record += len
        }
        writeFileResult = true
        renameFileToTarget(tempFile, file)
        Log.d(TAG, "writeFile -> record = $record")
        responseBody.byteStream().close()
        randomAccessFile.close()
    } ?: Log.d(TAG, "fileParentFile is null")
    return writeFileResult
}

fun renameFileToTarget(sourceFile: File, targetFile: File): Boolean {
    return if (targetFile.exists()) {
        Log.d(TAG, "renameFileToTarget -> target file exists.")
        targetFile.delete()
        sourceFile.renameTo(targetFile)
    } else {
        Log.d(TAG, "renameFileToTarget -> target file not exists.")
        sourceFile.renameTo(targetFile)
    }
}

@SuppressLint("UnsafeHashAlgorithmDetector")
@Suppress("TooGenericExceptionCaught")
fun getFileMd5(path: String): String {
    Log.d(TAG, "getFileMd5")
    val calculateFail = "get file md5 fail"
    val startTime = System.currentTimeMillis()

    val digest: MessageDigest?
    try {
        val fis = FileInputStream(File(path))
        digest = MessageDigest.getInstance("MD5")
        val bytes = ByteArray(BYTE_ARRAY_SIZE_10240)

        try {
            var len: Int
            while (fis.read(bytes).also { len = it } != -1) {
                digest?.update(bytes, 0, len)
            }
        } catch (e: IllegalArgumentException) {
            try {
                fis.close()
            } catch (e: IOException) {
                Log.e(TAG, "getFileMd5 fis close error")
            }
        }
        fis.close()
    } catch (e: Exception) {
        Log.e(TAG, "getFileMd5 -> error = $e")
        return calculateFail
    }
    Log.d(TAG, "getFileMd5 -> const time = ${System.currentTimeMillis() - startTime}")
    return bytesToHexString(digest?.digest()) ?: calculateFail
}

fun getFileLastModifiedAppendLength(filePath: String): String {
    val file = File(filePath)
    return "${file.lastModified()}-${file.length()}"
}

private fun bytesToHexString(src: ByteArray?): String? {
    val sb = StringBuilder("")
    return src?.let { byteArray ->
        if (byteArray.isNotEmpty()) {
            byteArray.forEach { byte ->
                val a = byte.toInt() and OX_FF
                val herString = Integer.toHexString(a)
                if (herString.length < 2) {
                    sb.append(0)
                }
                sb.append(herString)
            }
            sb.toString()
        } else {
            null
        }
    }
}