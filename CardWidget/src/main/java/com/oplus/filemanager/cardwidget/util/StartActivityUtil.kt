/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.cardwidget.util
 * * Description : StartActivityClickEntity工具类，打开文管首页标签Tab及打开标签文件列表页
 * * Version     : 1.0
 * * Date        : 2022/12/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.util

import android.content.Intent
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.Log
import com.oplus.smartenginehelper.entity.StartActivityClickEntity
import org.json.JSONObject

class StartActivityUtil {
    private val TAG = "LabelCardStartActivityUtil"
    fun startFileManagerMainActivityLabelTab(showAddLabelDialog: Boolean = false): StartActivityClickEntity {
        Log.d(TAG ,"startFileManagerMainActivityLabelTab showAddLabelDialog:$showAddLabelDialog")
        val start = StartActivityClickEntity()
        start.setPackageName(MyApplication.sAppContext.packageName)
        start.setAction(START_FILE_MANAGER_ACTION)
        val paramsJson = JSONObject()
        paramsJson.put(Constants.KEY_IS_FROM_LABEL_CARD, true)
        if (showAddLabelDialog) {
            paramsJson.put(Constants.FROM_LABEL_CARD_SHOW_RENAME_LABEL_DIALOG, true)
        }
        start.setParams(Constants.KEY_IS_FROM_LABEL_CARD, paramsJson.toString())
        start.setCategory(Intent.CATEGORY_DEFAULT)
        start.addFlag(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        start.addFlag(Intent.FLAG_ACTIVITY_NEW_TASK)
        return start
    }

    fun startFileManagerLabelList(labelName: String, labelId: Long): StartActivityClickEntity {
        val start = StartActivityClickEntity()
        start.setPackageName(MyApplication.sAppContext.packageName)
        start.setAction(START_LABEL_FILE_LIST)
        val paramsJson = JSONObject()
        paramsJson.put(Constants.TITLE, labelName)
        paramsJson.put(Constants.LABEL_ID, labelId)
        start.setParams(Constants.TITLE_AND_LABEL_ID, paramsJson.toString())
        start.setCategory(Intent.CATEGORY_DEFAULT)
        start.addFlag(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        start.addFlag(Intent.FLAG_ACTIVITY_NEW_TASK)
        return start
    }

    fun startLabelListShowAddFileDialog(labelName: String, labelId: Long): StartActivityClickEntity {
        val start = StartActivityClickEntity()
        start.setPackageName(MyApplication.sAppContext.packageName)
        start.setAction(START_LABEL_FILE_LIST)
        val paramsJson = JSONObject()
        paramsJson.put(Constants.TITLE, labelName)
        paramsJson.put(Constants.LABEL_ID, labelId)
        paramsJson.put(Constants.SHOW_ADD_FILE_DIALOG, true)
        val labelJson = if (paramsJson.toString() != null) paramsJson.toString() else ""
        start.setParams(Constants.TITLE_AND_LABEL_ID, labelJson)
        start.setCategory(Intent.CATEGORY_DEFAULT)
        start.addFlag(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        start.addFlag(Intent.FLAG_ACTIVITY_NEW_TASK)
        return start
    }

    fun startLabelListDialog(widgetCode: String): StartActivityClickEntity {
        val start = StartActivityClickEntity()
        start.setPackageName(MyApplication.sAppContext.packageName)
        start.setAction(LABEL_SELECT_DIALOG)
        val paramsJson = JSONObject()
        paramsJson.put(Constants.TITLE, widgetCode)
        start.setParams(Constants.TITLE_AND_LABEL_ID, paramsJson.toString())
        start.setCategory(Intent.CATEGORY_DEFAULT)
        start.addFlag(Intent.FLAG_ACTIVITY_NO_ANIMATION)
        start.addFlag(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        start.addFlag(Intent.FLAG_ACTIVITY_NEW_TASK)
        return start
    }
}