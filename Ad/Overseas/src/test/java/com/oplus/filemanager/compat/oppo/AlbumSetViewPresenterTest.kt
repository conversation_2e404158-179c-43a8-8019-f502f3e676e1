/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: AlbumSetViewPresenterTest.kt
 ** Description: unit test
 ** Version: 1.0
 ** Date : 2022/7/26
 ** Author: YanShengNeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * YanShengNeng 2022/7/14      1.0    create
 ****************************************************************/
package com.oplus.filemanager.compat.oppo

import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.compat.brand.oppo.AlbumSetViewPresenter
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import org.mockito.Answers
import org.mockito.Mockito

@RunWith(JUnit4::class)
class AlbumSetViewPresenterTest {

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `assert result when createItem`() {
        val presenter = Mockito.mock(AlbumSetViewPresenter::class.java, Answers.CALLS_REAL_METHODS)
        val createItem = presenter.createItem()
        Assert.assertNotNull(createItem)
        Assert.assertEquals(BaseFileBean.TYPE_FILE_AD, createItem.wrapperType)
    }

    @Test
    fun `should return true when shouldDisplayByPath`() {
        val albumPresenter = mockk<AlbumSetViewPresenter>()
        every { albumPresenter.shouldDisplayByPath() } answers { callOriginal() }
        Assert.assertEquals(true, albumPresenter.shouldDisplayByPath())
    }
}