/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: AdvertInitHelperTest.kt
 ** Description: unit test
 ** Version: 1.0
 ** Date : 2022/8/9
 ** Author: wanghonglei
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * wanghonglei  2022/8/9      1.0    create
 ****************************************************************/
package com.coloros.filemanager.filechoose.adapter

import android.app.Activity
import android.content.Context
import android.content.res.Resources
import android.widget.TextView
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.filechoose.adapter.FolderPickerAdapter
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class FolderPickerAdapterTest {
    @MockK
    lateinit var context: Context

    @MockK
    lateinit var activity: Activity

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        val resourcesMock = mockk<Resources>()
        every { resourcesMock.getDimension(R.dimen.divider_background_height) }.returns(2.0f)
        context = mockk(relaxed = true) {
            every { resources }.returns(resourcesMock)
            every { applicationContext }.returns(this)
        }
        activity = mockk(relaxed = true)
        MyApplication.init(context)
    }

    @After
    fun after() {
        unmockkStatic(Utils::class)
        unmockkStatic(VolumeEnvironment::class)
    }

    @Ignore("test failed")
    @Test
    fun `should null when call doData`() {
        val holder = mockk<FolderPickerAdapter.ListViewHolder>(relaxed = true)
        val folderPickerAdapter = mockk<FolderPickerAdapter>(relaxed = true)
        mockkStatic(Utils::class)
        every { Utils.formatStorageDetail(any(), any()) } returns ("256")
        every { folderPickerAdapter.doData(any(), any()) } answers { callOriginal() }
        folderPickerAdapter.doData(holder, "C:/a")
        println((holder.mAnotherName as TextView).text)

        mockkStatic(VolumeEnvironment::class)
        every { folderPickerAdapter.mInternalPath } returns ("sdcard")
        every { folderPickerAdapter.doData(any(), any()) } answers { callOriginal() }
        folderPickerAdapter.doData(holder, "sdcard")
        println((holder.mTitle as TextView).text)
    }
}