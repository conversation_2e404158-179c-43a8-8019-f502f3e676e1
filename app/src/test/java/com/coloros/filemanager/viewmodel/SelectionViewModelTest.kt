/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SelectionViewModelTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/10/14
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2021/10/14        1.0      create
 ***********************************************************************/
package com.coloros.filemanager.viewmodel

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel

//@RunWith(PowerMockRunner::class)
//@PrepareForTest(SdkUtils::class, PCConnectAction::class, UIConfigMonitor::class)
class SelectionViewModelTest {

    /*
    private lateinit var selectionViewModel: SelectionViewModel<*, *>

    private lateinit var mUIConfigMonitor: UIConfigMonitor

    @Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    @Before
    fun setUpViewModel() {
        selectionViewModel = object : SelectionViewModel<FakeBaseFileBean, FakeUiModel>() {
            override fun getRealFileSize(): Int {
                return 0
            }

            override fun loadData() {
                // fake view model do nothing
            }

            override fun getRecyclerViewScanMode(): SelectionTracker.LAYOUT_TYPE {
                return SelectionTracker.LAYOUT_TYPE.GRID
            }

        }
        mUIConfigMonitor = UIConfigMonitor.instance
    }


    @Test
    fun `test whether In Select Mode`() {
        val selectedUiMode = BaseUiModel<FakeBaseFileBean>(
                emptyList(),
                BaseStateModel(MutableLiveData(LIST_SELECTED_MODE)),
                arrayListOf(),
                hashMapOf()
        )
        val normalUiMode = BaseUiModel<FakeBaseFileBean>(
                emptyList(),
                BaseStateModel(MutableLiveData(LIST_NORMAL_MODE)),
                arrayListOf(),
                hashMapOf()
        )
        selectionViewModel.mUiState.value = selectedUiMode
        assertEquals(true, selectionViewModel.isInSelectMode())
        selectionViewModel.mUiState.value = normalUiMode
        assertEquals(false, selectionViewModel.isInSelectMode())
    }

    @Test
    fun `test item whether is selected`() {
        assertEquals(false, selectionViewModel.isItemSelected(1))
        selectionViewModel.mUiState.value = emptySelectList
        assertEquals(false, selectionViewModel.isItemSelected(1))
        selectionViewModel.mUiState.value = selectList
        assertEquals(true, selectionViewModel.isItemSelected(1))
    }

    @Test
    fun `test obtain select key list`() {
        assertEquals(arrayListOf<Int>(), selectionViewModel.getSelectionKeyList())
        selectionViewModel.mUiState.value = emptySelectList
        assertEquals(arrayListOf<Int>(), selectionViewModel.getSelectionKeyList())
        val arrayList = arrayListOf(1)
        selectionViewModel.mUiState.value = selectList
        assertEquals(arrayList, selectionViewModel.getSelectionKeyList())
    }

    @Test
    fun `test the selected item`() {
        val defaultList = arrayListOf<FakeBaseFileBean>()
        val fakeBaseFileBase1 = FakeBaseFileBean()
        val fakeBaseFileBase2 = FakeBaseFileBean()
        val fakeBaseFileBase3 = FakeBaseFileBean()
        val defaultSelectList = BaseUiModel(
                emptyList(),
                BaseStateModel(MutableLiveData(LIST_NORMAL_MODE)),
                arrayListOf(1, 2, 3),
                hashMapOf(1 to fakeBaseFileBase1, 2 to fakeBaseFileBase2, 3 to fakeBaseFileBase3)
        )
        val fakeBaseFileBeanList = arrayListOf(fakeBaseFileBase1, fakeBaseFileBase2, fakeBaseFileBase3)
        assertEquals(defaultList, selectionViewModel.getSelectItems())

        val emptyHashMapSelectList = BaseUiModel(
                emptyList(),
                BaseStateModel(MutableLiveData(LIST_NORMAL_MODE)),
                arrayListOf(1, 2, 3),
                hashMapOf(4 to fakeBaseFileBase1, 5 to fakeBaseFileBase2, 6 to fakeBaseFileBase3)
        )
        selectionViewModel.mUiState.value = emptyHashMapSelectList
        assertEquals(defaultList, selectionViewModel.getSelectItems())
        selectionViewModel.mUiState.value = defaultSelectList
        assertEquals(fakeBaseFileBeanList, selectionViewModel.getSelectItems())
    }

    companion object {
        private val emptySelectList = BaseUiModel<FakeBaseFileBean>(
                emptyList(),
                BaseStateModel(MutableLiveData(LIST_SELECTED_MODE)),
                arrayListOf(),
                hashMapOf()
        )

        private val selectList = BaseUiModel<FakeBaseFileBean>(
                emptyList(),
                BaseStateModel(MutableLiveData(LIST_SELECTED_MODE)),
                arrayListOf(1),
                hashMapOf()
        )
    }
*/
}

class FakeBaseFileBean : BaseFileBean()

class FakeUiModel(
        fileList: List<FakeBaseFileBean>,
        stateModel: BaseStateModel,
        selectedList: ArrayList<Int>,
        keyMap: HashMap<Int, FakeBaseFileBean>
) : BaseUiModel<FakeBaseFileBean>(fileList, stateModel, selectedList, keyMap)