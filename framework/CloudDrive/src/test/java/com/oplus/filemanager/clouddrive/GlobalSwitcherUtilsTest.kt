/*********************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : GlobalSwitcherUtilsTest
 * Description :
 * Version     : 1.0
 * Date        : 2024/1/9 9:45
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * ternence        2024/1/9       1.0      create
 **********************************************************************/
package com.oplus.filemanager.clouddrive

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PropertyCompat
import com.oplus.filemanager.interfaze.heytapaccount.IHeytapAccount
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module

class GlobalSwitcherUtilsTest {

    private lateinit var context: Context

    private val heytapAccount = mockk<IHeytapAccount>()
    companion object {
        private const val CN = "CN"
    }

    private val koinApp = koinApplication {
        modules(module {
            single { heytapAccount }
        })
    }

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
        startKoin(koinApp)
    }

    @After
    fun teardown() {
        unmockkAll()
        stopKoin()
    }

    @Test
    fun testSupportCloudDiskWhenCloudDriveNotSupport() {
        mockkStatic(CloudDriveManager::class)
        every { CloudDriveManager.isSupportCloudDisk(context) }.returns(false)
        val support = GlobalSwitcherUtils.supportCloudDisk
        Assert.assertFalse(support)
        unmockkStatic(CloudDriveManager::class)
    }

    @Test
    fun testSupportCloudDiskWhenFailure() {
        mockkStatic(CloudDriveManager::class)
        every { CloudDriveManager.isSupportCloudDisk(context) }.throws(Exception())
        val support = GlobalSwitcherUtils.supportCloudDisk
        Assert.assertFalse(support)
        unmockkStatic(CloudDriveManager::class)
    }

    @Test
    fun testSupportCloudDiskWhenCloudDriveSupportAndNotExpRomButNotLogin() {
        mockkStatic(CloudDriveManager::class)
        every { CloudDriveManager.isSupportCloudDisk(context) }.returns(true)
        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.sIsExpRom }.returns(false)
        mockkStatic(PropertyCompat::class)
        every { heytapAccount.isLogin(context) }.returns(false)
        val support = GlobalSwitcherUtils.supportCloudDisk
        Assert.assertTrue(support)
        unmockkStatic(PropertyCompat::class)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(CloudDriveManager::class)
    }

    @Test
    fun testSupportCloudDiskWhenCloudDriveSupportAndNotExpRomAndLoginAndIsCN() {
        mockkStatic(CloudDriveManager::class)
        every { CloudDriveManager.isSupportCloudDisk(context) }.returns(true)
        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.sIsExpRom }.returns(false)
        mockkStatic(PropertyCompat::class)
        every { heytapAccount.isLogin(context) }.returns(true)
        every { heytapAccount.getUserCountry(context) }.returns(CN)
        val support = GlobalSwitcherUtils.supportCloudDisk
        Assert.assertTrue(support)
        unmockkStatic(PropertyCompat::class)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(CloudDriveManager::class)
    }

    @Test
    fun testSupportCloudDiskWhenCloudDriveSupportAndNotExpRomAndLoginButIsNotCN() {
        mockkStatic(CloudDriveManager::class)
        every { CloudDriveManager.isSupportCloudDisk(context) }.returns(true)
        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.sIsExpRom }.returns(false)
        mockkStatic(PropertyCompat::class)
        every { heytapAccount.isLogin(context) }.returns(true)
        every { heytapAccount.getUserCountry(context) }.returns("")
        val support = GlobalSwitcherUtils.supportCloudDisk
        Assert.assertFalse(support)
        unmockkStatic(PropertyCompat::class)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(CloudDriveManager::class)
    }

    @Test
    fun testSupportCloudDiskWhenCloudDriveSupportAndIsExpRomButNotLogin() {
        mockkStatic(CloudDriveManager::class)
        every { CloudDriveManager.isSupportCloudDisk(context) }.returns(true)
        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.sIsExpRom }.returns(true)
        mockkStatic(PropertyCompat::class)
        every { heytapAccount.isLogin(context) }.returns(false)
        val support = GlobalSwitcherUtils.supportCloudDisk
        Assert.assertFalse(support)
        unmockkStatic(PropertyCompat::class)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(CloudDriveManager::class)
    }

    @Test
    fun testSupportCloudDiskWhenCloudDriveSupportAndIsExpRomAndLoginAndIsCN() {
        mockkStatic(CloudDriveManager::class)
        every { CloudDriveManager.isSupportCloudDisk(context) }.returns(true)
        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.sIsExpRom }.returns(true)
        mockkStatic(PropertyCompat::class)
        every { heytapAccount.isLogin(context) }.returns(true)
        every { heytapAccount.getUserCountry(context) }.returns(CN)
        val support = GlobalSwitcherUtils.supportCloudDisk
        Assert.assertTrue(support)
        unmockkStatic(PropertyCompat::class)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(CloudDriveManager::class)
    }

    @Test
    fun testSupportCloudDiskWhenCloudDriveSupportAndIsExpRomAndLoginButIsNotCN() {
        mockkStatic(CloudDriveManager::class)
        every { CloudDriveManager.isSupportCloudDisk(context) }.returns(true)
        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.sIsExpRom }.returns(true)
        mockkStatic(PropertyCompat::class)
        every { heytapAccount.isLogin(context) }.returns(true)
        every { heytapAccount.getUserCountry(context) }.returns("")
        val support = GlobalSwitcherUtils.supportCloudDisk
        Assert.assertFalse(support)
        unmockkStatic(PropertyCompat::class)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(CloudDriveManager::class)
    }

    @Test
    fun testSupportCloudDiskWhenNotExpRom() {
        mockkStatic(CloudDriveManager::class)
        every { CloudDriveManager.isSupportCloudDisk(context) }.returns(true)
        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.sIsExpRom }.returns(false)
        mockkStatic(PropertyCompat::class)
        val support = GlobalSwitcherUtils.supportCloudDisk()
        Assert.assertTrue(support)
        unmockkStatic(PropertyCompat::class)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(CloudDriveManager::class)
    }

    @Test
    fun testSupportCloudDiskWhenInExpRom() {
        mockkStatic(CloudDriveManager::class)
        every { CloudDriveManager.isSupportCloudDisk(context) }.returns(true)
        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.sIsExpRom }.returns(true)
        mockkStatic(PropertyCompat::class)
        every { PropertyCompat.sIsPepProject }.returns(true)
        val support = GlobalSwitcherUtils.supportCloudDisk()
        Assert.assertTrue(support)
        unmockkStatic(PropertyCompat::class)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(CloudDriveManager::class)
    }
}