/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AlbumScanner
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/2/2 11:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/2/2       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.filelist.scanner

import androidx.annotation.VisibleForTesting
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.ImageFileWrapper
import com.filemanager.common.wrapper.WebAlbumLoadResult
import com.oplus.fileservice.bean.WebFileBean
import com.oplus.fileservice.bean.WebScannerResult
import com.oplus.fileservice.fileServiceScope
import com.oplus.fileservice.filelist.utils.getDateAndTimeByDefaultFormat
import com.oplus.fileservice.utils.WebMimeTypeHelper
import com.oplus.fileservice.utils.WebThumbCacheManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.apache.commons.io.FilenameUtils

class AlbumScanner(
    private val bucketKey: String,
    pageNo: Int,
    pageSize: Int,
    private var isNeedThumbnail: Boolean = true
) : BaseUriScanner() {

    private var mTotal = 0
    var mPageNo = pageNo

    var mPageSize = pageSize

    override fun scannerFiles(): WebScannerResult? {
        Log.d(TAG, "scannerFiles -> bucketKey = $bucketKey")
        val albumResult = MediaStoreCompat.getAllLocalAlbum(bucketKey, mPageNo, mPageSize, PRE_GENERATE_PAGES)
        handleQueryResult(albumResult)
        return null
    }

    private fun handleQueryResult(imageResults: WebAlbumLoadResult) {
        val resultList = imageResults.mResultList
        mTotal = imageResults.mTotal
        mPageNo = imageResults.mPageNo
        val resultWebFileBean = ArrayList<WebFileBean>()
        val preResultWebFileBean = ArrayList<WebFileBean>()
        Log.d(TAG, "transformImageFileToWebFile -> resultList Size = ${resultList.size}")
        for (imageFileWrapper in resultList) {
            Log.d(TAG, "handleQueryResult -> imageFileWrapper = $imageFileWrapper")
            val webFileBean = convertImageFileToWebFileBean(imageFileWrapper)
            if (resultWebFileBean.size < mPageSize) {
                resultWebFileBean.add(webFileBean)
            } else {
                preResultWebFileBean.add(webFileBean)
            }
        }

        fileServiceScope.launch(Dispatchers.IO) {
            if (isNeedThumbnail) {
                WebThumbCacheManager.setThumbnailData(resultWebFileBean)
            }
            Log.d(TAG, "mScannerCallback , mPageNo =$mPageNo, mTotal =$mTotal  result.size =${resultList.size}")
            mScannerCallback?.scannerSuc(WebScannerResult(mPageNo, mTotal, resultWebFileBean))
            WebThumbCacheManager.preGeneratedThumbnails(preResultWebFileBean)
        }
    }

    /**
     * Convert [ImageFileWrapper] to [WebFileBean].
     */
    @VisibleForTesting
    fun convertImageFileToWebFileBean(imageFileWrapper: ImageFileWrapper): WebFileBean {
        val webFileBean = WebFileBean()
        imageFileWrapper.apply {
            webFileBean.mFileName = FilenameUtils.getBaseName(mDisplayName)
            webFileBean.mFilePath = mData
            webFileBean.mFileId = getId()
            webFileBean.mFileSize = mSize
            webFileBean.mFileFormat = FilenameUtils.getExtension(mDisplayName)
            webFileBean.mFileType = WebMimeTypeHelper.getFileTypeFromExtension(webFileBean.mFileFormat)
            webFileBean.mLastModifyDate = getDateAndTimeByDefaultFormat(mDateModified)
            webFileBean.mDateModified = mDateModified
            webFileBean.mLocalType = mLocalType
        }
        return webFileBean
    }

    companion object {
        private const val TAG = "AlbumScanner"
        private const val PRE_GENERATE_PAGES = 2
    }
}