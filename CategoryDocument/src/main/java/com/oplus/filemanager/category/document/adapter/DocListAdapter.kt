/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:DocListAdapter.kt/
 * * Description:
 * * Version:1.0
 * * Date :2020/6/9
 * * Author:********
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/6/9,        v1.0,           Create
 ****************************************************************/
package com.oplus.filemanager.category.document.adapter

import android.app.Activity
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.annotation.VisibleForTesting
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.noMoreAction
import com.filemanager.common.viewholder.DocGroupTitleListVH
import com.filemanager.common.viewholder.FileBrowserGridVH
import com.filemanager.common.viewholder.FileBrowserLargeListVH
import com.filemanager.common.viewholder.FileDocGridVH
import com.filemanager.common.viewholder.NormalListVH
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.dropdrag.SelectionTracker
import com.oplus.filemanager.ad.AdViewHolder
import com.oplus.filemanager.ad.AdvertManager
import com.oplus.filemanager.category.document.ui.DocParentFragment
import java.lang.ref.WeakReference

class DocListAdapter : BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, MediaFileWrapper>, LifecycleObserver {
    companion object {
        private const val TAG = "DocListAdapter"
    }

    var mScanViewModel = KtConstants.SCAN_MODE_GRID
        set(value) {
            field = value
            if (value == KtConstants.SCAN_MODE_GRID && mContext is Activity) {
                mItemWith = ItemDecorationFactory.getGridItemWidthForDocWithWps(mContext as Activity)
            }
        }
    private var mItemWith: Int = 0
    var mEntryName: String? = null
    private var mIsRtl = false
    private val mUiHandler: Handler
    private var mOnRecyclerItemClickListener: OnRecyclerItemClickListener? = null
    private val mSizeCache = HashMap<String, String>()
    private var mThreadManager: ThreadManager
    private var mFragment: Fragment? = null
    @VisibleForTesting
    var displayLastOpenTime = false
        get() = field.noMoreAction()

    constructor(content: Context, fragment: WeakReference<Fragment?>, lifecycle: Lifecycle) : super(content) {
        mFragment = fragment.get()
        mIsRtl = Utils.isRtl()
        mUiHandler = Handler(Looper.getMainLooper())
        mThreadManager = ThreadManager(lifecycle)
        lifecycle.addObserver(this)
    }

    override fun getItemId(position: Int): Long {
        return getItemKeyByPosition(position)?.toLong() ?: SelectionTracker.NO_LONG_ITEM_ID
    }

    override fun onViewRecycled(holder: RecyclerView.ViewHolder) {
        super.onViewRecycled(holder)
        (holder as? NormalListVH)?.resetVHData()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            BaseFileBean.TYPE_FILE_AD -> {
                AdViewHolder(LayoutInflater.from(parent.context).inflate(com.filemanager.common.R.layout.item_main_ad, parent, false))
            }
            KtConstants.SCAN_MODE_GRID -> {
                FileBrowserGridVH(LayoutInflater.from(parent.context).inflate(FileBrowserGridVH.getLayoutId(), parent, false))
            }
            BaseFileBean.TYPE_DOC_GROUP_TITLE -> {
                DocGroupTitleListVH(LayoutInflater.from(parent.context).inflate(DocGroupTitleListVH.getLayoutId(), parent, false))
            }
            KtConstants.SCAN_MODE_LIST_LARGE -> FileBrowserLargeListVH.create(parent)
            else -> {
                NormalListVH(LayoutInflater.from(parent.context).inflate(NormalListVH.getLayoutId(), parent, false))
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if ((position < 0) || (position >= mFiles.size)) {
            return
        }
        if ((AdvertManager.isAdEnabled()) && (mFiles[position].mFileWrapperViewType == BaseFileBean.TYPE_FILE_AD)
            && (holder is AdViewHolder)) {
            val adMgr: AdvertManager? = (mFragment as? DocParentFragment)?.mAdManager
            adMgr?.let {
                holder.addAdView(adMgr.getAdView(mEntryName))
            }
            return
        }
        val file = mFiles[position]
        when (holder) {
            is NormalListVH -> {
                holder.setDisplayLastOpenTime(displayLastOpenTime)
                holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
                holder.updateDividerVisible(mFiles.size - 1, position)
                holder.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
            }
            is FileBrowserLargeListVH -> {
                holder.setDisplayLastOpenTime(displayLastOpenTime)
                holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
                holder.updateDividerVisible(mFiles.size - 1, position)
                holder.setSelected(clickPreviewFile?.mData?.equals(file.mData) ?: false)
            }
            is FileBrowserGridVH -> {
                holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
            }
            is FileDocGridVH -> {
                holder.adaptItemSize(mItemWith)
                holder.loadData(mContext, getItemKey(file, position), file, mChoiceMode, mSelectionArray, mSizeCache, mThreadManager, this)
            }
            is DocGroupTitleListVH -> holder.updateViewHolder(file)
        }
        if (DragUtils.getSelectedFiles()?.contains(file) == true && DragUtils.isDragging) {
            holder.itemView.alpha = DragUtils.SELECTED_ITEMVIEW_ALPHA
        } else {
            holder.itemView.alpha = 1f
        }
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
    }

    fun setOnRecyclerItemClickListener(listener: OnRecyclerItemClickListener) {
        mOnRecyclerItemClickListener = listener
    }

    fun setData(data: ArrayList<MediaFileWrapper>, selectionArray: ArrayList<Int>, lastOpenTimeOrder: Boolean?) {
        mIsRtl = Utils.isRtl()
        mFiles = data
        mSelectionArray = selectionArray
        checkDisplayOpenTime(lastOpenTimeOrder)
        checkComputingAndExecute {
            notifyDataSetChanged()
        }
    }

    /**
     * 检测是否显示打开时间，若是固定显示的，直接显示
     * 若是非固定显示的，根据排序类型确定是否显示打开时间
     */
    @VisibleForTesting
    fun checkDisplayOpenTime(lastOpenTimeOrder: Boolean?) {
        displayLastOpenTime = if (lastOpenTimeOrder == true) {
            true
        } else {
            val sort = SortModeUtils.getSharedSortMode(MyApplication.sAppContext, SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC))
            sort == SortHelper.FILE_LAST_OPEN_TIME_ORDER
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        mSizeCache.clear()
        mUiHandler.removeCallbacksAndMessages(null)
    }

    override fun initListChoiceModeAnimFlag(flag: Boolean) {
        if (mScanViewModel == KtConstants.SCAN_MODE_LIST) {
            setChoiceModeAnimFlag(flag)
        }
    }

    override fun getItemViewType(position: Int): Int {
        var type = mScanViewModel
        if (position >= 0 && position < mFiles.size) {
            val wrapperViewType = mFiles.get(position).mFileWrapperViewType
            if (wrapperViewType == BaseFileBean.TYPE_FILE_AD) {
                type = BaseFileBean.TYPE_FILE_AD
            } else if (wrapperViewType == BaseFileBean.TYPE_DOC_GROUP_TITLE) {
                type = BaseFileBean.TYPE_DOC_GROUP_TITLE
            }
        }
        if (KtConstants.SCAN_MODE_LIST == type && WindowUtils.supportLargeScreenLayout(mContext)) {
            type = KtConstants.SCAN_MODE_LIST_LARGE
        }
        return type
    }

    override fun getItemKey(item: MediaFileWrapper, position: Int): Int? = item.mId

}