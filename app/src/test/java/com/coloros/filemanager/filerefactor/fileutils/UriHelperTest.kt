package com.coloros.filemanager.filerefactor.fileutils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.MediaFileCompat
import com.filemanager.common.compat.OplusUsbEnvironmentCompat
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.Ignore
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [Build.VERSION_CODES.S])
class UriHelperTest {

    @MockK
    var mContext: Context? = null

    @MockK
    var type: Int? = null

    @MockK
    var path: String? = null

    @MockK
    var uriHelper: UriHelper? = null

    @MockK
    var mediaFileType: MediaFileCompat.MediaFileType? = null

    @Before
    fun setUp() {
        mContext = mockk(relaxed = true)
        type = mockk(relaxed = true)
        uriHelper = mockk(relaxed = true)
        mediaFileType = mockk(relaxed = true)
        mockkStatic(OplusUsbEnvironmentCompat::class)
        mockkObject(MimeTypeHelper::class)
        mockkStatic(MediaFileCompat::class)
    }

    @After
    fun tearDown() {
        unmockkStatic(OplusUsbEnvironmentCompat::class)
        unmockkObject(MimeTypeHelper::class)
        unmockkStatic(MediaFileCompat::class)
    }

    @Test
    fun test_getCategoryUri() {
        Assert.assertEquals(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI, UriHelper.geCategoryUri(
                CategoryHelper.CATEGORY_IMAGE
            )
        )
        Assert.assertEquals(
            MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, UriHelper.geCategoryUri(
                CategoryHelper.CATEGORY_AUDIO
            )
        )
        Assert.assertEquals(
            MediaStore.Video.Media.EXTERNAL_CONTENT_URI, UriHelper.geCategoryUri(
                CategoryHelper.CATEGORY_VIDEO
            )
        )
        Assert.assertEquals(
            MediaStore.Files.getContentUri("external"), UriHelper.geCategoryUri(
                CategoryHelper.CATEGORY_APK
            )
        )
    }

    @Ignore
    fun test_getFileUri() {
        val file = BaseFileBean()
        Assert.assertNull(UriHelper.getFileUri(file, Intent(), MimeTypeHelper.IMAGE_TYPE))

        file.mData = "test.jpg"
        every {
            OplusUsbEnvironmentCompat.getInternalPath(
                mContext
            )
        }.returns("path")
        every {
            MimeTypeHelper.getTypeFromDrm(
                mContext!!,
                type!!,
                "test.jpg"
            )
        }.returns(MimeTypeHelper.IMAGE_TYPE)
        every {
            MimeTypeHelper.getTypeFromPath(
                "test.jpg"
            )
        }.returns(MimeTypeHelper.IMAGE_TYPE)
        every { Whitebox.invokeMethod<Uri>(uriHelper, "innerGetFileUri", file, true) }.returns(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI
        )
        every {
            MediaFileCompat.getFileType(
                "test.jpg"
            )
        }.returns(mediaFileType)
        every {
            MediaFileCompat.isAudioFileType(
                mediaFileType!!.fileType
            )
        }.returns(false)
        every {
            MediaFileCompat.isImageFileType(
                mediaFileType!!.fileType
            )
        }.returns(true)
        Assert.assertNotNull(uriHelper?.getFileUri(file, Intent(), MimeTypeHelper.IMAGE_TYPE))
    }

    @Ignore
    fun test_getFileUriForDragDrop() {
        val file = BaseFileBean()
        Assert.assertNull(UriHelper.getFileUriForDragDrop(file, Intent(), null))

        file.mData = "test.jpg"
        Mockito.mockStatic(OplusUsbEnvironmentCompat::class.java).`when`<Any> {
            OplusUsbEnvironmentCompat.getInternalPath(
                mContext
            )
        }.thenReturn("path")
        every {
            MimeTypeHelper.getTypeFromDrm(
                mContext!!,
                type!!,
                "test.jpg"
            )
        }.returns(MimeTypeHelper.IMAGE_TYPE)
        every {
            MimeTypeHelper.getTypeFromPath(
                "test.jpg"
            )
        }.returns(MimeTypeHelper.IMAGE_TYPE)
        every { Whitebox.invokeMethod<Uri>(uriHelper, "innerGetFileUri", file, true) }.returns(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI
        )
        every {
            MediaFileCompat.getFileType(
                "test.jpg"
            )
        }.returns(mediaFileType)
        every {
            MediaFileCompat.isAudioFileType(
                mediaFileType!!.fileType
            )
        }.returns(false)
        every {
            MediaFileCompat.isImageFileType(
                mediaFileType!!.fileType
            )
        }.returns(true)
        Assert.assertNotNull(
            uriHelper?.getFileUriForDragDrop(
                file,
                Intent(),
                MimeTypeHelper.IMAGE_TYPE
            )
        )
    }
}