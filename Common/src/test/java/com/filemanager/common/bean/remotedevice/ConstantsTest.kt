package com.filemanager.common.bean.remotedevice

import org.junit.Assert.*
import org.junit.Test

/**
 * Constants类的单元测试类
 * 用于测试Constants中定义的各种错误状态判断方法
 */
class ConstantsTest {

    /**
     * 测试isResultSuc方法
     * 验证成功状态码(ERROR_CODE_SUC)应返回true
     */
    @Test
    fun `isResultSuc-成功状态应返回true`() {
        assertTrue(Constants.isResultSuc(Constants.ERROR_CODE_SUC))
    }

    /**
     * 测试isResultSuc方法
     * 验证非成功状态码应返回false
     */
    @Test
    fun `isResultSuc-非成功状态应返回false`() {
        assertFalse(Constants.isResultSuc(Constants.ERROR_CODE_FAILED_NORMAL))
        assertFalse(Constants.isResultSuc(Constants.ERROR_CODE_CANCEL))
        assertFalse(Constants.isResultSuc(Constants.ERROR_CODE_NO_NETWORK))
    }

    /**
     * 测试isSdkError方法
     * 验证SDK相关错误码(取消、普通失败、A类失败)应返回true
     */
    @Test
    fun `isSdkError-SDK错误状态应返回true`() {
        assertTrue(Constants.isSdkError(Constants.ERROR_CODE_CANCEL))
        assertTrue(Constants.isSdkError(Constants.ERROR_CODE_FAILED_NORMAL))
        assertTrue(Constants.isSdkError(Constants.ERROR_CODE_FAILED_A))
    }

    /**
     * 测试isSdkError方法
     * 验证非SDK错误码应返回false
     */
    @Test
    fun `isSdkError-非SDK错误状态应返回false`() {
        assertFalse(Constants.isSdkError(Constants.ERROR_CODE_SUC))
        assertFalse(Constants.isSdkError(Constants.ERROR_CODE_NO_NETWORK))
        assertFalse(Constants.isSdkError(Constants.ERROR_CODE_PASSWORD_ERROR))
    }

    /**
     * 测试isNoRootPathError方法
     * 验证根路径未找到错误码应返回true
     */
    @Test
    fun `isNoRootPathError-根路径错误应返回true`() {
        assertTrue(Constants.isNoRootPathError(Constants.ERROR_CODE_NO_ROOTPATH_FOUND))
    }

    /**
     * 测试isNoRootPathError方法
     * 验证非根路径错误码应返回false
     */
    @Test
    fun `isNoRootPathError-非根路径错误应返回false`() {
        assertFalse(Constants.isNoRootPathError(Constants.ERROR_CODE_SUC))
        assertFalse(Constants.isNoRootPathError(Constants.ERROR_CODE_NO_NETWORK))
        assertFalse(Constants.isNoRootPathError(Constants.ERROR_CODE_TIMEOUT))
    }

    /**
     * 测试isNetWorkError方法
     * 验证网络错误码应返回true
     */
    @Test
    fun `isNetWorkError-网络错误应返回true`() {
        assertTrue(Constants.isNetWorkError(Constants.ERROR_CODE_NO_NETWORK))
    }

    /**
     * 测试isNetWorkError方法
     * 验证非网络错误码应返回false
     */
    @Test
    fun `isNetWorkError-非网络错误应返回false`() {
        assertFalse(Constants.isNetWorkError(Constants.ERROR_CODE_SUC))
        assertFalse(Constants.isNetWorkError(Constants.ERROR_CODE_NO_ROOTPATH_FOUND))
        assertFalse(Constants.isNetWorkError(Constants.ERROR_CODE_PASSWORD_ERROR))
    }

    /**
     * 测试isPasswordError方法
     * 验证密码错误码应返回true
     */
    @Test
    fun `isPasswordError-密码错误应返回true`() {
        assertTrue(Constants.isPasswordError(Constants.ERROR_CODE_PASSWORD_ERROR))
    }

    /**
     * 测试isPasswordError方法
     * 验证非密码错误码应返回false
     */
    @Test
    fun `isPasswordError-非密码错误应返回false`() {
        assertFalse(Constants.isPasswordError(Constants.ERROR_CODE_SUC))
        assertFalse(Constants.isPasswordError(Constants.ERROR_CODE_NO_NETWORK))
        assertFalse(Constants.isPasswordError(Constants.ERROR_CODE_TIMEOUT))
    }

    /**
     * 测试isOtherError方法
     * 验证其他类型错误码应返回true
     * 包括: 无参数错误、超时错误、SDK查询异常、不支持错误、根路径不在缓存中错误等
     */
    @Test
    fun `isOtherError-其他错误类型应返回true`() {
        assertTrue(Constants.isOtherError(Constants.ERROR_CODE_NO_ARG))
        assertTrue(Constants.isOtherError(Constants.ERROR_CODE_TIMEOUT))
        assertTrue(Constants.isOtherError(Constants.ERROR_CODE_SDK_QUERY_EXCEPTION))
        assertTrue(Constants.isOtherError(Constants.ERROR_CODE_NO_SUPPORTED))
        assertTrue(Constants.isOtherError(Constants.ERROR_CODE_ROOTPATH_NOT_IN_CACHE))
        assertTrue(Constants.isOtherError(Constants.ERROR_CODE_PASSWORD_ERROR))
    }

    /**
     * 测试isOtherError方法
     * 验证非其他类型错误码应返回false
     * 包括: 成功状态、取消错误、普通失败错误、网络错误等
     */
    @Test
    fun `isOtherError-非其他错误类型应返回false`() {
        assertFalse(Constants.isOtherError(Constants.ERROR_CODE_SUC))
        assertFalse(Constants.isOtherError(Constants.ERROR_CODE_CANCEL))
        assertFalse(Constants.isOtherError(Constants.ERROR_CODE_FAILED_NORMAL))
        assertFalse(Constants.isOtherError(Constants.ERROR_CODE_NO_NETWORK))
    }

    /**
     * 测试isOtherError方法的边界值
     * 验证未定义的错误码(-1,100)应被视为其他错误返回true
     */
    @Test
    fun `isOtherError-边界值测试`() {
        assertTrue(Constants.isOtherError(-1))
        assertTrue(Constants.isOtherError(100))
    }
}