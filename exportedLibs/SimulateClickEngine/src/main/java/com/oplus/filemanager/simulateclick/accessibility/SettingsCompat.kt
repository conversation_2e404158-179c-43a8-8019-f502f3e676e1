/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SettingsCompat.kt
 * Description:
 *     Compat to access Settings in different ColorOS versions.
 *
 * Version: 1.0
 * Date: 2024-05-22
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-05-22   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.accessibility

import android.content.ContentResolver
import android.provider.Settings
import androidx.annotation.RequiresPermission
import androidx.annotation.VisibleForTesting
import com.oplus.compat.provider.SettingsNative
import com.oplus.filemanager.simulateclick.engine.utils.SdkUtils

internal class SettingsCompat private constructor() {

    object Secure {
        private val _impl: ISettingsWriter by lazy { obtainImpl() }

        @VisibleForTesting
        internal val impl: ISettingsWriter
            get() = _impl

        @JvmStatic
        internal fun obtainImpl(): ISettingsWriter = when {
            SdkUtils.isAtLeastOS13() -> AddOnSecureWriter
            else -> LegacySecureWriter
        }

        @RequiresPermission("com.oplus.permission.safe.SETTINGS")
        @JvmStatic
        fun putString(resolver: ContentResolver, name: String, value: String) {
            impl.putString(resolver, name, value)
        }
    }
}

@VisibleForTesting
internal interface ISettingsWriter {

    /**
     * Store a name/value pair into the database.
     * @param resolver to access the database with
     * @param name to store
     * @param value to associate with the name
     * @return true if the value was set, false on database errors
     * @see Settings.Secure.putString
     */
    fun putString(resolver: ContentResolver, name: String, value: String)
}

/**
 * Deprecated from Color OS 13.0
 */
private object LegacySecureWriter : ISettingsWriter {

    override fun putString(resolver: ContentResolver, name: String, value: String) {
        SettingsNative.Secure.putString(name, value)
    }
}

/**
 * Require Color OS 13.0
 */
private object AddOnSecureWriter : ISettingsWriter {

    override fun putString(resolver: ContentResolver, name: String, value: String) {
        Settings.Secure.putString(resolver, name, value)
    }
}