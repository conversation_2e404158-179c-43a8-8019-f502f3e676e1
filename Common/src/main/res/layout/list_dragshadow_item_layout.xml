<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_transparent"
    android:layoutDirection="locale"
    android:padding="@dimen/list_drag_shadow_item_layout_padding">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/list_dragshadow_item_cons"
        android:layout_width="@dimen/list_drag_shadow_item_layout_width"
        android:layout_height="@dimen/list_drag_shadow_item_layout_height"
        android:background="@drawable/list_dragshadow_layout_background"
        android:layout_marginTop="@dimen/drag_shadow_margin_top"
        android:layout_marginEnd="@dimen/drag_shadow_margin_end">

        <com.filemanager.common.view.FileThumbView
            android:id="@+id/dragshadow_item_cover"
            android:layout_width="@dimen/dimen_36dp"
            android:layout_height="@dimen/dimen_36dp"
            android:layout_marginStart="@dimen/list_drag_shadow_cover_margin_start"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@color/color_transparent"
            android:forceDarkAllowed="false" />

        <LinearLayout
            android:id="@+id/ll_item_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginTop="@dimen/list_drag_shadow_title_and_detail_margin_top"
            android:layout_marginEnd="@dimen/dimen_24dp"
            android:layout_marginBottom="@dimen/list_drag_shadow_title_and_detail_margin_top"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/dragshadow_item_cover"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <com.filemanager.common.view.TextViewSnippet
                android:id="@+id/dragshadow_item_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:maxLines="2"
                android:textAppearance="@style/couiTextHeadlineXS"
                android:textColor="?attr/couiColorLabelPrimary"
                android:textSize="@dimen/dimen_15dp"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/dragshadow_item_details"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/file_list_item_detail_margin_top"
                android:adjustViewBounds="true"
                android:ellipsize="end"
                android:maxLines="1"
                android:textAlignment="viewStart"
                android:textAppearance="@style/couiTextBodyXS"
                android:textColor="?attr/couiColorLabelSecondary"
                android:textSize="@dimen/dimen_12dp"/>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include layout="@layout/dragshadow_reddot_layout" />
</FrameLayout>