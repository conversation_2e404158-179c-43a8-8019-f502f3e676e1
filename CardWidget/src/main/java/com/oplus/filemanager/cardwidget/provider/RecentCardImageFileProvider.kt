/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentCardImageFileProvider.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/13
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/13      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.provider

import android.net.Uri
import android.os.ParcelFileDescriptor
import androidx.core.content.FileProvider
import com.filemanager.common.utils.Log
import java.io.File
import java.io.FileNotFoundException

class RecentCardImageFileProvider : FileProvider() {

    override fun onCreate(): Boolean {
        return false
    }

    @Throws(FileNotFoundException::class)
    override fun openFile(uri: Uri, mode: String): ParcelFileDescriptor? {
        var filePath = uri.path
        if (filePath != null) {
            filePath = filePath.replace(ROOT_DATA_DATA, DATA_USER_0)
        }
        Log.v(TAG, "openFile uri.path=${uri.path}, path=$filePath")
        val file = filePath?.let { File(it) }
        var parcel: ParcelFileDescriptor? = null
        try {
            parcel = ParcelFileDescriptor.open(file, ParcelFileDescriptor.MODE_READ_ONLY)
        } catch (e: FileNotFoundException) {
            Log.e(TAG, "Error finding $e, path=$filePath")
        }
        return parcel
    }

    companion object {
        private const val TAG = "ImageFileProvider"
        const val AUTHORITY = "com.oplus.filemanager.recent.cardwidget.image"
        const val ROOT_DATA_DATA = "/root/data/data/"
        const val DATA_USER_0 = "/data/user/0/"
    }
}