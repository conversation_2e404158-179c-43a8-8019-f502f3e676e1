package com.oplus.filemanager.pcconnect

import android.net.Uri
import android.view.MotionEvent
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseFileBean
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.util.ArrayList
import java.util.concurrent.atomic.AtomicBoolean

/**
 * PCConnectApi的单元测试类
 * 用于测试PCConnectApi中各个方法的正确性
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class PCConnectApiTest {

    // 定义测试所需的mock对象
    private lateinit var mockLifecycleOwner: LifecycleOwner
    private lateinit var mockFileBean: BaseFileBean
    private lateinit var mockMotionEvent: MotionEvent
    private lateinit var mockOnItemTouchListener: RecyclerView.OnItemTouchListener
    private lateinit var mockPCConnectController: PCConnectController
    private lateinit var mockPCConnectDataHelper: PCConnectDataHelper

    /**
     * 测试前的初始化方法
     * 创建所有需要的mock对象并设置默认行为
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        // 创建mock对象
        mockLifecycleOwner = mockk(relaxed = true)
        mockFileBean = mockk(relaxed = true)
        mockMotionEvent = mockk(relaxed = true)
        mockOnItemTouchListener = mockk(relaxed = true)

        // 模拟PCConnectController中的静态方法
        mockkObject(PCConnectController)
        every { PCConnectController.isPCConnectSupport() } returns false
        every { PCConnectController.isPadConnectSupport() } returns false
        every { PCConnectController.isDirExist() } returns true

        // 模拟PCConnectDataHelper中的静态方法
        mockkObject(PCConnectDataHelper)
        every { PCConnectDataHelper.isTouchEventFromPC(any()) } returns false

        // 安全地重置懒加载的拦截器
        try {
            val field = PCConnectApi::class.java.getDeclaredField("sItemTouchInterceptor")
            field.isAccessible = true
            field.set(PCConnectApi, null)
        } catch (e: NoSuchFieldException) {
            // 如果字段不存在则忽略
        }
    }

    /**
     * 测试后的清理方法
     * 解除所有mock
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试isScreenCast方法
     * 当PC连接不支持时应返回false
     */
    @Test
    fun `isScreenCast returns false when PC connect not supported`() {
        every { PCConnectController.isPCConnectSupport() } returns false

        val result = PCConnectApi.isScreenCast()

        assert(!result)
    }

    /**
     * 测试isScreenCast方法
     * 当PC连接支持且屏幕投射启用时应返回true
     */
    @Test
    fun `isScreenCast returns true when PC connect supported and screen cast enabled`() {
        val mockController = mockk<PCConnectController>()
        every { PCConnectController.isPCConnectSupport() } returns true
        every { PCConnectController.sInstance } returns mockController
        every { mockController.isPCScreenCast() } returns true

        val result = PCConnectApi.isScreenCast()

        assert(result)
    }

    /**
     * 测试isScreenCast方法
     * 当PC连接支持但屏幕投射禁用时应返回false
     */
    @Test
    fun `isScreenCast returns false when PC connect supported but screen cast disabled`() {
        val mockController = mockk<PCConnectController>()
        every { PCConnectController.isPCConnectSupport() } returns true
        every { PCConnectController.sInstance } returns mockController
        every { mockController.isPCScreenCast() } returns false

        val result = PCConnectApi.isScreenCast()

        assert(!result)
    }

    /**
     * 测试attachToLifecycle方法
     * 当多屏连接支持时应调用控制器的attachToLifecycle方法
     */
    @Test
    fun `attachToLifecycle calls controller when multi screen supported`() {
        val mockController = mockk<PCConnectController>()
        every { PCConnectController.isPCConnectSupport() } returns true
        every { PCConnectController.isPadConnectSupport() } returns false
        every { PCConnectController.sInstance } returns mockController
        every { mockController.attachToLifecycle(any()) } just runs

        PCConnectApi.attachToLifecycle(mockLifecycleOwner)

        verify(exactly = 1) { mockController.attachToLifecycle(mockLifecycleOwner) }
    }

    /**
     * 测试attachToLifecycle方法
     * 当多屏连接不支持时不应调用控制器方法
     */
    @Test
    fun `attachToLifecycle does not call controller when multi screen not supported`() {
        every { PCConnectController.isPCConnectSupport() } returns false
        every { PCConnectController.isPadConnectSupport() } returns false

        PCConnectApi.attachToLifecycle(mockLifecycleOwner)

        verify(exactly = 0) { PCConnectController.sInstance wasNot Called }
    }

    /**
     * 测试onFragmentHiddenChanged方法
     * 当多屏连接支持时应调用控制器的onFragmentHiddenChanged方法
     */
    @Test
    fun `onFragmentHiddenChanged calls controller when multi screen supported`() {
        val mockController = mockk<PCConnectController>()
        every { PCConnectController.isPCConnectSupport() } returns true
        every { PCConnectController.isPadConnectSupport() } returns false
        every { PCConnectController.sInstance } returns mockController
        every { mockController.onFragmentHiddenChanged(any(), any()) } just runs

        PCConnectApi.onFragmentHiddenChanged(mockLifecycleOwner, true)

        verify(exactly = 1) { mockController.onFragmentHiddenChanged(mockLifecycleOwner, true) }
    }

    /**
     * 测试onFragmentHiddenChanged方法
     * 当多屏连接不支持时不应调用控制器方法
     */
    @Test
    fun `onFragmentHiddenChanged does not call controller when multi screen not supported`() {
        every { PCConnectController.isPCConnectSupport() } returns false
        every { PCConnectController.isPadConnectSupport() } returns false

        PCConnectApi.onFragmentHiddenChanged(mockLifecycleOwner, true)

        verify(exactly = 0) { PCConnectController.sInstance wasNot Called }
    }

    /**
     * 测试onActivityResume方法
     * 当PC连接支持时应调用控制器的checkPCConnect方法
     */
    @Test
    fun `onActivityResume checks PC connect when PC supported`() {
        val mockController = mockk<PCConnectController>()
        every { PCConnectController.isPCConnectSupport() } returns true
        every { PCConnectController.isPadConnectSupport() } returns false
        every { PCConnectController.sInstance } returns mockController
        every { mockController.checkPCConnect() } just runs

        PCConnectApi.onActivityResume()

        verify(exactly = 1) { mockController.checkPCConnect() }
        verify(exactly = 0) { mockController.checkPadConnect() }
    }

    /**
     * 测试onActivityResume方法
     * 当Pad连接支持但PC不支持时应调用控制器的checkPadConnect方法
     */
    @Test
    fun `onActivityResume checks Pad connect when Pad supported but PC not supported`() {
        val mockController = mockk<PCConnectController>()
        every { PCConnectController.isPCConnectSupport() } returns false
        every { PCConnectController.isPadConnectSupport() } returns true
        every { PCConnectController.sInstance } returns mockController
        every { mockController.checkPadConnect() } just runs

        PCConnectApi.onActivityResume()

        verify(exactly = 1) { mockController.checkPadConnect() }
        verify(exactly = 0) { mockController.checkPCConnect() }
    }

    /**
     * 测试getMultiScreenConnectDirList方法
     * 应返回正确的多屏连接目录路径数组
     */
    @Test
    fun `getMultiScreenConnectDirList returns correct paths`() {
        val expectedPaths = arrayOf("/storage/emulated/0/HeyPad", "/storage/emulated/0/HeyPC")
        
        val result = PCConnectApi.getMultiScreenConnectDirList()
        
        assert(result.size == 2)
        assert(result.isNotEmpty())
    }

    /**
     * 测试isMultiScreenConnectSupport方法
     * 当PC连接支持时应返回true
     */
    @Test
    fun `isMultiScreenConnectSupport returns true when PC connect supported`() {
        every { PCConnectController.isPCConnectSupport() } returns true

        val result = PCConnectApi.isMultiScreenConnectSupport()

        assert(result)
    }

    /**
     * 测试isMultiScreenConnectSupport方法
     * 当Pad连接支持时应返回true
     */
    @Test
    fun `isMultiScreenConnectSupport returns true when Pad connect supported`() {
        every { PCConnectController.isPCConnectSupport() } returns false
        every { PCConnectController.isPadConnectSupport() } returns true

        val result = PCConnectApi.isMultiScreenConnectSupport()

        assert(result)
    }

    /**
     * 测试isMultiScreenConnectSupport方法
     * 当两者都不支持时应返回false
     */
    @Test
    fun `isMultiScreenConnectSupport returns false when neither supported`() {
        every { PCConnectController.isPCConnectSupport() } returns false
        every { PCConnectController.isPadConnectSupport() } returns false

        val result = PCConnectApi.isMultiScreenConnectSupport()

        assert(!result)
    }

    /**
     * 测试openFileOnRemote方法
     * 当PC连接不支持时应返回false
     */
    @Test
    fun `openFileOnRemote returns false when PC connect not supported`() {
        every { PCConnectController.isPCConnectSupport() } returns false

        val result = PCConnectApi.openFileOnRemote(mockFileBean, mockMotionEvent)

        assert(!result)
    }

    /**
     * 测试openFileOnRemote方法
     * 当PC连接支持且操作成功时应返回true
     */
    @Test
    fun `openFileOnRemote returns true when PC connect supported and operation succeeds`() {
        val mockController = mockk<PCConnectController>()
        every { PCConnectController.isPCConnectSupport() } returns true
        every { PCConnectController.sInstance } returns mockController
        every { mockController.openFileOnPCRemote(any(), any()) } returns true

        val result = PCConnectApi.openFileOnRemote(mockFileBean, mockMotionEvent)

        assert(result)
    }

    /**
     * 测试getItemTouchInterceptor方法
     * 当多屏连接支持时应返回拦截器实例
     */
    @Test
    fun `getItemTouchInterceptor returns interceptor when multi screen supported`() {
        every { PCConnectController.isPCConnectSupport() } returns true
        every { PCConnectController.isPadConnectSupport() } returns false

        val result = PCConnectApi.getItemTouchInterceptor()

        assert(result != null)
        assert(result is PCConnectItemTouchInterceptor)
    }

    /**
     * 测试getItemTouchInterceptor方法
     * 当多屏连接不支持时应返回null
     */
    @Test
    fun `getItemTouchInterceptor returns null when multi screen not supported`() {
        every { PCConnectController.isPCConnectSupport() } returns false
        every { PCConnectController.isPadConnectSupport() } returns false

        val result = PCConnectApi.getItemTouchInterceptor()

        assert(result == null)
    }

    /**
     * 测试checkViewCanLongPress方法
     * 当多屏连接不支持时应返回true
     */
    @Test
    fun `checkViewCanLongPress returns true when multi screen not supported`() {
        every { PCConnectController.isPCConnectSupport() } returns false
        every { PCConnectController.isPadConnectSupport() } returns false

        val result = PCConnectApi.checkViewCanLongPress(false)

        assert(result)
    }

    /**
     * 测试isPadScreenCast方法
     * 应委托给控制器的checkIsPadConnect方法
     */
    @Test
    fun `isPadScreenCast delegates to controller`() {
        val mockController = mockk<PCConnectController>()
        every { PCConnectController.sInstance } returns mockController
        every { mockController.checkIsPadConnect() } returns true

        val result = PCConnectApi.isPadScreenCast()

        assert(result)
        verify(exactly = 1) { mockController.checkIsPadConnect() }
    }

    /**
     * 测试grantUriPermissionForPad方法
     * 应委托给控制器的grantUriPermissionsForPad方法
     */
    @Test
    fun `grantUriPermissionForPad delegates to controller`() {
        val mockController = mockk<PCConnectController>()
        every { PCConnectController.sInstance } returns mockController
        val uriList = arrayListOf<Uri?>(mockk())
        every { mockController.grantUriPermissionsForPad(any()) } just runs

        PCConnectApi.grantUriPermissionForPad(uriList)

        verify(exactly = 1) { mockController.grantUriPermissionsForPad(uriList) }
    }

    /**
     * 测试isTouchFromPC方法
     * 应委托给数据帮助类的isTouchEventFromPC方法
     */
    @Test
    fun `isTouchFromPC delegates to data helper`() {
        every { PCConnectDataHelper.isTouchEventFromPC(any()) } returns true

        val result = PCConnectApi.isTouchFromPC(mockMotionEvent)

        assert(result)
        verify(exactly = 1) { PCConnectDataHelper.isTouchEventFromPC(mockMotionEvent) }
    }

    /**
     * 测试isMultiScreenDirExist方法
     * 应委托给控制器的isDirExist方法
     */
    @Test
    fun `isMultiScreenDirExist delegates to controller`() {
        every { PCConnectController.isDirExist() } returns true

        val result = PCConnectApi.isMultiScreenDirExist()

        assert(result)
        verify(exactly = 1) { PCConnectController.isDirExist() }
    }
}