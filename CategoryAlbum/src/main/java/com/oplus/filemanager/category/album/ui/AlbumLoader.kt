/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.album
 * * Version     : 1.0
 * * Date        : 2020/6/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.album.ui

import android.content.ContentValues
import android.content.Context
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.FileTaskLoader
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.AlbumLoadResult
import com.filemanager.common.wrapper.ImageFileWrapper
import com.oplus.filemanager.interfaze.main.IMain
import java.util.ArrayList
import java.util.Locale

class AlbumLoader(
    context: Context,
    contentValues: ContentValues,
    private var isPickerPage: Boolean = false,
    private var mPickerMultipleType: Array<String>? = null
) : FileTaskLoader<AlbumLoadResult<Int>>(context) {

    companion object {
        const val TAG = "ImageLoader"
    }

    private var mAlbumLoadResult: AlbumLoadResult<Int>? = null
    private var mContentValues: ContentValues = contentValues
    var mSortType = SortHelper.ALBUM_DEFAULT_ORDER
    var isDesc = true

    override fun loadInBackground(): AlbumLoadResult<Int> {
        val albumLoadResult = MediaStoreCompat.getLocalAlbum(mContentValues, mPickerMultipleType)
        /*排序*/
        val result = preHandleAlbumResultBackground(albumLoadResult)
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.findFileLabelIfHad(result.mResultList)
        return result
    }

    private fun preHandleAlbumResultBackground(result: AlbumLoadResult<Int>): AlbumLoadResult<Int> {
        val fileSize = result.mResultList.size
        val arrayList = ArrayList<ImageFileWrapper>()
        val sortList = ArrayList(result.mResultList)
        val imageHashMap = HashMap<Int, ImageFileWrapper>()
        Log.d(TAG, "preHandleAlbumResultBackground data size $fileSize")
        SortHelper.sortAlbumFiles(sortList, mSortType, isDesc)
        Log.d(TAG, "preHandleAlbumResultBackground after sort add label size ${sortList.size}")
        arrayList.addAll(sortList)
        arrayList.forEach { imageHashMap[getItemKey(it)] = it }
        Log.d(TAG, "preHandleAlbumResultBackground result size  ${arrayList.size}, fileCount =$fileSize")
        return AlbumLoadResult(arrayList, imageHashMap)
    }

    fun getItemKey(item: ImageFileWrapper): Int {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return item.getId()
        }
        return path.toLowerCase(Locale.getDefault()).hashCode()
    }

    override fun onStartLoading() {
        if (isPickerPage) {
            mSortType = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getPickerAlbumKey())
            isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getPickerAlbumKey())
        } else {
            mSortType = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getAlbumKey())
            isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getAlbumKey())
        }
        if ((mAlbumLoadResult?.mResultList?.size ?: 0) > 0) {
            deliverResult(mAlbumLoadResult)
        }

        if (takeContentChanged() || (mAlbumLoadResult == null) || (mAlbumLoadResult?.mResultList?.size == 0)) {
            forceLoad()
        }
    }

    override fun onStopLoading() {
        cancelLoad()
    }

    override fun forceLoad() {
        try {
            super.forceLoad()
        } catch (e: Exception) {
            Log.w(TAG, "forceLoad " + e.message)
        }

    }

    override fun deliverResult(data: AlbumLoadResult<Int>?) {
        if (mReset) {
            mAlbumLoadResult = null
            return
        }
        mAlbumLoadResult = data
        if (mStarted) {
            super.deliverResult(data)
        }
    }

    fun setSort(sort: Int) {
        mSortType = if (sort == -1) {
            if (isPickerPage) {
                SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getPickerAlbumKey())
            } else {
                SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getAlbumKey())
            }
        } else {
            sort
        }
    }
}