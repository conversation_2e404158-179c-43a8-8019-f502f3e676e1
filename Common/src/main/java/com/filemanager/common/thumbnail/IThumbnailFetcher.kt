/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : IThumbnailFetcher
 * * Description : 获取缩略图文件 接口
 * * Version     : 1.0
 * * Date        : 2025/05/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thumbnail

import android.util.Size
import com.filemanager.common.base.BaseFileBean

interface IThumbnailFetcher {

    /**
     * 加载缩率图
     * @param file 原始文件
     * @param thumbnailSize 缩略图大小
     */
    fun fetchThumbnail(file: BaseFileBean, thumbnailSize: Size): String
}