package com.filemanager.common.base

import android.content.Context
import android.graphics.Rect
import android.view.MotionEvent
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.MyApplication
import com.filemanager.common.dragselection.SlideUtils
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.view.GridThumbView
import com.oplus.dropdrag.recycleview.ItemDetailsLookup
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import java.lang.reflect.Field

/**
 * BaseSelectionViewHolder的单元测试类
 * 用于测试BaseSelectionViewHolder的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class BaseSelectionViewHolderTest {

    // 测试所需的成员变量
    private lateinit var context: Context
    private lateinit var itemView: View
    private lateinit var holder: TestViewHolder
    private lateinit var mockCheckBox: COUICheckBox
    private lateinit var mockDividerLine: View
    private lateinit var mockGridThumbView: GridThumbView
    
    /**
     * 测试前的初始化方法
     * 1. 初始化上下文环境
     * 2. 创建各种mock对象
     * 3. 设置测试桩(stub)
     */
    @Before
    fun setUp() {
        // 获取Robolectric提供的测试上下文
        context = RuntimeEnvironment.application
        
        // 使用mockk模拟MyApplication单例
        mockkObject(MyApplication)
        // 设置MyApplication的sAppContext返回测试上下文
        every { MyApplication.sAppContext } returns context
        
        // 模拟PCConnectAction和SlideUtils
        mockkObject(PCConnectAction)
        mockkObject(SlideUtils)
        // 设置SlideUtils的偏移量方法返回值
        every { SlideUtils.getCheckBoxLeftOffset(any()) } returns 10
        every { SlideUtils.getCheckBoxRightOffset(any()) } returns 10

        // 创建各种mock对象
        itemView = mockk(relaxed = true)  // 使用relaxed模式允许不明确的方法调用
        mockCheckBox = mockk(relaxed = true)
        mockDividerLine = mockk(relaxed = true)
        mockGridThumbView = mockk(relaxed = true)
        
        // 创建测试用的ViewHolder实例
        holder = TestViewHolder(itemView)
        // 设置mock对象
        holder.mCheckBox = mockCheckBox
        holder.dividerLine = mockDividerLine
    }

    /**
     * 测试后的清理方法
     * 解除所有mock对象
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试updateDividerVisible方法
     * 当是最后一个item时应该隐藏分割线
     */
    @Test
    fun `updateDividerVisible should hide divider when last item`() {
        holder.updateDividerVisible(5, 5)
        // 验证分割线是否被设置为GONE
        verify { mockDividerLine.visibility = View.GONE }
    }

    /**
     * 测试updateDividerVisible方法
     * 当不是最后一个item时应该显示分割线
     */
    @Test
    fun `updateDividerVisible should show divider when not last item`() {
        holder.updateDividerVisible(5, 3)
        // 验证分割线是否被设置为VISIBLE
        verify { mockDividerLine.visibility = View.VISIBLE }
    }

    /**
     * 测试updateKey方法
     * 应该正确设置key值
     */
    @Test
    fun `updateKey should set key correctly`() {
        holder.updateKey(100)
        // 验证key值是否被正确设置
        assertEquals(100, holder.getDetails().selectionKey)
    }

    /**
     * 测试isInDragRegion方法
     * 当PC投屏时应该返回false
     */
    @Test
    fun `isInDragRegion should return false when PC screen cast`() {
        // 设置PC投屏状态为true
        every { PCConnectAction.isScreenCast() } returns true
        // 验证返回结果应为false
        assertFalse(holder.isInDragRegion(mockk()))
    }

    /**
     * 测试isInDragRegion方法
     * 当不是PC投屏时应该调用实现方法
     */
    @Test
    fun `isInDragRegion should delegate to impl when not screen cast`() {
        // 设置PC投屏状态为false
        every { PCConnectAction.isScreenCast() } returns false
        // 验证返回结果应为true(TestViewHolder中isInDragRegionImpl返回true)
        assertTrue(holder.isInDragRegion(mockk()))
    }

    /**
     * 测试isInSelectRegion方法
     * 当PC投屏时应该返回false
     */
    @Test
    fun `isInSelectRegion should return false when PC screen cast`() {
        // 设置PC投屏状态为true
        every { PCConnectAction.isScreenCast() } returns true
        // 验证返回结果应为false
        assertFalse(holder.isInSelectRegion(mockk()))
    }

    /**
     * 测试isInSelectRegionImpl方法
     * 应该正确处理checkbox区域判断
     */
    @Test
    fun `isInSelectRegionImpl should handle checkbox region correctly`() {
        // 创建模拟的MotionEvent
        val event = mockk<MotionEvent>()
        // 设置触摸点的x坐标
        every { event.rawX } returns 100f
        
        // 设置checkbox的可见区域
        val checkBoxRect = Rect(50, 0, 150, 100)
        // 模拟getGlobalVisibleRect方法返回预设的矩形区域
        every { mockCheckBox.getGlobalVisibleRect(any()) } answers {
            val rect = firstArg<Rect>()
            rect.set(checkBoxRect)
            true
        }
        
        // 验证100在50-150范围内，应该返回true
        assertTrue(holder.isInSelectRegionImpl(event))
    }

    /**
     * 测试isInSelectRegionImpl方法
     * 当没有checkbox时应该返回false
     */
    @Test
    fun `isInSelectRegionImpl should return false when no checkbox`() {
        // 设置checkbox为null
        holder.mCheckBox = null
        // 验证返回结果应为false
        assertFalse(holder.isInSelectRegionImpl(mockk()))
    }

    /**
     * 测试canLongPressOrClick方法
     * 测试checkbox可见时的正常模式处理
     */
    @Test
    fun `canLongPressOrClick should handle normal mode when checkbox visible`() {
        // 设置PC投屏状态为true
        every { PCConnectAction.isScreenCast() } returns true
        // 设置checkbox可见
        every { mockCheckBox.visibility } returns View.VISIBLE
        
        // 设置checkbox和父视图的可见区域
        val checkBoxRect = Rect(50, 0, 150, 100)
        val parentRect = Rect(0, 0, 200, 200)
        every { mockCheckBox.getGlobalVisibleRect(any()) } answers {
            val rect = firstArg<Rect>()
            rect.set(checkBoxRect)
            true
        }
        every { itemView.getGlobalVisibleRect(any()) } answers {
            val rect = firstArg<Rect>()
            rect.set(parentRect)
            true
        }
        // 设置PCConnectAction的检查方法返回true
        every { PCConnectAction.checkViewCanLongPress(false) } returns true
        
        // 获取ItemDetails并验证结果
        val details = holder.getDetails()
        assertTrue(details.canLongPressOrClick())
    }

    /**
     * 测试canLongPressOrClick方法
     * 测试GridThumbView的选择模式处理
     */
    @Test
    fun `canLongPressOrClick should handle GridThumbView select mode`() {
        // 设置PC投屏状态为true
        every { PCConnectAction.isScreenCast() } returns true
        // 使用GridThumbView创建测试ViewHolder
        holder = TestViewHolder(mockGridThumbView)
        // 设置GridThumbView为选择模式
        every { mockGridThumbView.isSelectMode() } returns true
        // 设置PCConnectAction的检查方法返回true
        every { PCConnectAction.checkViewCanLongPress(true) } returns true
        
        // 获取ItemDetails并验证结果
        val details = holder.getDetails()
        assertTrue(details.canLongPressOrClick())
    }

    /**
     * 测试selectionKey属性
     * 应该返回当前设置的key值
     */
    @Test
    fun `selectionKey should return current key`() {
        // 设置key值
        holder.updateKey(100)
        // 获取ItemDetails并验证key值
        val details = holder.getDetails()
        assertEquals(100, details.selectionKey)
    }

    /**
     * 测试用的ViewHolder实现类
     * 继承BaseSelectionViewHolder用于测试
     */
    private inner class TestViewHolder(itemView: View) : 
        BaseSelectionViewHolder(itemView, true) {
        /**
         * 获取内部的ItemDetails对象
         */
        fun getDetails() = mDetails as ItemDetailsLookup.ItemDetails<Int>
        
        /**
         * 实现isInDragRegionImpl方法
         * 始终返回true用于测试
         */
        override fun isInDragRegionImpl(event: MotionEvent) = true
    }
}