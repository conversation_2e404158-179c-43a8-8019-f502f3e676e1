/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - HeadUpImageData.kt
 * Description:
 *     The preview image data for loaded heap up file
 *
 * Version: 1.0
 * Date: 2024-10-22
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-10-22   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.selection.data

import android.graphics.drawable.Drawable
import com.filemanager.common.base.BaseFileBean

internal data class HeadUpImageData(
    val fileBean: BaseFileBean,
    @Transient val drawable: Drawable?,
    val isIcon: Boolean,
    val isVideoType: Boolean,
    val isLoading: Boolean = true,
    val imageSize: ImageSizeInfo? = null
) {
    data class ImageSizeInfo(
        val width: Int,
        val height: Int
    )
}