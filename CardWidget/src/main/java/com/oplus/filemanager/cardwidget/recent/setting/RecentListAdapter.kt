/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentListAdapter
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/14 22:30
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.setting

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.cardlist.COUICardListHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.stringResource
import com.oplus.filemanager.cardwidget.R
import com.oplus.filemanager.cardwidget.label.dialog.OnItemClickListener
import com.oplus.filemanager.cardwidget.recent.setting.RecentPreviewItemDecoration.Companion.CATEGORY_LAST_POSITION
import com.oplus.filemanager.cardwidget.recent.setting.RecentPreviewItemDecoration.Companion.FIRST_POSITION
import com.oplus.filemanager.cardwidget.recent.data.RecentPreviewItem
import com.oplus.filemanager.cardwidget.recent.data.isSamePreviewItem

class RecentListAdapter(
    private val context: Context,
    var previewItem: RecentPreviewItem?,
    var list: List<RecentPreviewItem>
) : RecyclerView.Adapter<RecentListAdapter.ListViewHolder>() {

    private val layoutInflater: LayoutInflater by lazy {
        LayoutInflater.from(context)
    }

    private var selectIndex = -1

    var onItemClickListener: OnItemClickListener<RecentPreviewItem>? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ListViewHolder {
        val view = layoutInflater.inflate(R.layout.card_item_select_range, parent, false)
        return ListViewHolder(view)
    }

    override fun onBindViewHolder(holder: ListViewHolder, position: Int) {
        val label = list[position]
        holder.bindData(label, position, list.size)
        holder.setClickEvent(position, label)
        setItemCardBackground(position, holder, label)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    fun setLabelList(list: List<RecentPreviewItem>) {
        this.list = list
        findSelectIndex()
        notifyDataSetChanged()
    }

    private fun findSelectIndex() {
        var hasSelect = false
        this.list.forEachIndexed { index, recentPreviewItem ->
            if (previewItem?.isSamePreviewItem(recentPreviewItem) == true) {
                Log.d(TAG, "findSelectIndex -> recentPreviewItem = $recentPreviewItem")
                selectIndex = index
                hasSelect = true
            }
        }
        if (!hasSelect) {
            previewItem = RecentPreviewItem(
                CategoryHelper.CATEGORY_RECENT,
                stringResource(com.filemanager.common.R.string.label_add_recent_file_title)
            )
        }
    }

    private fun setItemCardBackground(
        position: Int,
        holder: ListViewHolder,
        label: RecentPreviewItem
    ) {
        when {
            position == FIRST_POSITION -> {
                COUICardListHelper.setItemCardBackground(
                    holder.itemView,
                    COUICardListHelper.FULL
                )
            }

            (label.type == CategoryHelper.CATEGORY_IMAGE) || (position == CATEGORY_LAST_POSITION + 1) -> {
                COUICardListHelper.setItemCardBackground(
                    holder.itemView,
                    COUICardListHelper.HEAD
                )
            }

            label.type == CategoryHelper.CATEGORY_COMPRESS -> {
                COUICardListHelper.setItemCardBackground(
                    holder.itemView,
                    COUICardListHelper.TAIL
                )
            }

            else -> {
                COUICardListHelper.setItemCardBackground(
                    holder.itemView,
                    COUICardListHelper.getPositionInGroup(itemCount, position)
                )
            }
        }
    }

    fun selectLabel(position: Int, label: RecentPreviewItem) {
        Log.d(TAG, "selectLabel -> position = $position ; label = $label ; previewItem = $previewItem")
        if (selectIndex == position) {
            return
        }
        val last = selectIndex
        previewItem = label
        selectIndex = position
        notifyItemChanged(last)
        notifyItemChanged(position)
    }

    class ListViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        @VisibleForTesting
        val nameTv: TextView = itemView.findViewById(R.id.label_name)

        @VisibleForTesting
        val radio: RadioButton = itemView.findViewById(R.id.label_radio)

        @VisibleForTesting
        val dividerLine: View = itemView.findViewById(R.id.divider_line)

        fun bindData(label: RecentPreviewItem, position: Int, size: Int) {
            val adapter = bindingAdapter as? RecentListAdapter ?: return
            nameTv.text = label.name
            radio.isChecked = adapter.previewItem?.isSamePreviewItem(label) ?: false
            dividerLine.visibility =
                if (position == FIRST_POSITION || label.type == CategoryHelper.CATEGORY_COMPRESS || position == (size - 1)) {
                    View.GONE
                } else {
                    View.VISIBLE
                }
        }

        fun setClickEvent(position: Int, label: RecentPreviewItem) {
            val adapter = bindingAdapter as? RecentListAdapter ?: return
            itemView.setOnClickListener {
                adapter.selectLabel(position, label)
                adapter.onItemClickListener?.onItemClick(it, position, label)
            }

            radio.setOnClickListener {
                adapter.selectLabel(position, label)
                adapter.onItemClickListener?.onItemClick(it, position, label)
            }
        }
    }

    companion object {
        private const val TAG = "RecentListAdapter"
    }
}