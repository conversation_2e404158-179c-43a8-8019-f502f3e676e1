package com.coloros.filemanager.utils

import android.content.ContentResolver
import android.content.Context
import android.database.ContentObserver
import android.view.MotionEvent
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.coloros.filemanager.BaseTest
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.pcconnect.PCConnectApi
import com.oplus.filemanager.pcconnect.PCConnectController
import com.oplus.filemanager.pcconnect.PCConnectDataHelper
import com.oplus.filemanager.pcconnect.PCConnectItemTouchInterceptor
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import junit.framework.TestCase.assertFalse
import org.junit.After
import org.junit.Assert
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito.mock
import org.robolectric.RobolectricTestRunner
import shadow.ShadowUtils
import java.io.File

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class PCConnectTest : BaseTest() {
    private lateinit var mPCConnectController: PCConnectController
    private lateinit var mPCConnectDataHelper: PCConnectDataHelper
    private lateinit var context: Context

    @Before
    override fun setUp() {
        context = mockk()
        mPCConnectController = PCConnectController.sInstance
        mPCConnectDataHelper = PCConnectDataHelper()
        mockkStatic(ModelUtils::class)
        mockkObject(MyApplication)
        mockkObject(PrivacyPolicyController.Companion)
        mockkObject(PermissionUtils)
        mockkObject(PCConnectController.Companion)
        mockkObject(PCConnectController)
        mockkObject(PCConnectDataHelper.Companion)
        mockkObject(PCConnectDataHelper)
        mockkObject(PCConnectApi)
        mockkObject(UIConfigMonitor.Companion)
        mockkStatic(MyApplication::class)
        every { context.applicationContext }.returns(context)
        every { MyApplication.sAppContext }.returns(context)
    }

    @After
    override fun tearDown() {
        unmockkStatic(ModelUtils::class)
        unmockkObject(PCConnectApi)
        unmockkObject(PCConnectController)
        unmockkObject(MyApplication)
        unmockkObject(PrivacyPolicyController.Companion)
        unmockkObject(PermissionUtils)
        unmockkObject(PCConnectDataHelper.Companion)
        unmockkObject(UIConfigMonitor.Companion)
        unmockkStatic(MyApplication::class)
    }

    @Test
    fun should_verify_pc_connect_support() {
        ShadowUtils.setUserId(-1)
        Assert.assertEquals(false, PCConnectController.isPCConnectSupport())
    }

    @Test
    fun should_verify_screen_cast() {
        mPCConnectController.mPCScreenCastState = PCConnectController.PC_SCREEN_STATE_NONE
        Assert.assertEquals(false, mPCConnectController.isPCScreenCast())
        mPCConnectController.mPCScreenCastState = PCConnectController.PC_SCREEN_STATE_CAST
        Assert.assertEquals(true, mPCConnectController.isPCScreenCast())
        mPCConnectController.mPCScreenCastState = PCConnectController.PC_SCREEN_STATE_SOCKET_CONNECTED
        Assert.assertEquals(true, mPCConnectController.isPCScreenCast())
    }

    @Test
    fun should_verify_open_file_on_remote() {
        ShadowUtils.setUserId(-1)
        Assert.assertEquals(false, mPCConnectController.openFileOnPCRemote(BaseFileBean(), null))
    }

    @Test
    fun should_verify_file_support_open_on_remote() {
        val file = BaseFileBean()
        Assert.assertEquals(false, mPCConnectDataHelper.isFileSupportOpen(file))
        file.mLocalType = MimeTypeHelper.IMAGE_TYPE
        Assert.assertEquals(true, mPCConnectDataHelper.isFileSupportOpen(file))
        file.mLocalType = MimeTypeHelper.VIDEO_TYPE
        Assert.assertEquals(true, mPCConnectDataHelper.isFileSupportOpen(file))
        file.mLocalType = MimeTypeHelper.TXT_TYPE
        Assert.assertEquals(true, mPCConnectDataHelper.isFileSupportOpen(file))
        file.mLocalType = MimeTypeHelper.DOCX_TYPE
        Assert.assertEquals(true, mPCConnectDataHelper.isFileSupportOpen(file))
        file.mLocalType = MimeTypeHelper.XLSX_TYPE
        Assert.assertEquals(true, mPCConnectDataHelper.isFileSupportOpen(file))
        file.mLocalType = MimeTypeHelper.PPTX_TYPE
        Assert.assertEquals(true, mPCConnectDataHelper.isFileSupportOpen(file))
        file.mLocalType = MimeTypeHelper.COMPRESSED_TYPE
        Assert.assertEquals(true, mPCConnectDataHelper.isFileSupportOpen(file))
        file.mLocalType = MimeTypeHelper.ZIP_TYPE
        Assert.assertEquals(true, mPCConnectDataHelper.isFileSupportOpen(file))
        file.mLocalType = MimeTypeHelper.AUDIO_TYPE
        Assert.assertEquals(true, mPCConnectDataHelper.isFileSupportOpen(file))
    }

    @Test
    fun should_verify_touch_by_pc() {
        val event = MotionEvent.obtain(
            0, 0, 0, 0F, 0F,
            0F, 0F, 0, 0F, 0F, 0, 0
        )
        every { PCConnectDataHelper.isTouchEventFromPC(any()) }.returns(false)
        Assert.assertEquals(false, PCConnectDataHelper.isTouchEventFromPC(event))
    }

    @Ignore
    @Test
    fun should_verify_can_view_long_press() {
        every { UIConfigMonitor.isZoomWindowShow() }.returns(false)
        val interceptor = PCConnectItemTouchInterceptor()
        Assert.assertEquals(true, interceptor.checkViewCanLongPress(true))
        Assert.assertEquals(true, interceptor.checkViewCanLongPress(false))
        every { UIConfigMonitor.isZoomWindowShow() }.returns(true)
        every { PCConnectDataHelper.isTouchEventFromPad(any()) }.returns(true)
        Assert.assertEquals(true, interceptor.checkViewCanLongPress(false))
        every { PCConnectController.sInstance }.returns(mockk {
            every { isPCScreenCast() }.returns(true)
            every { dragFileOnRemote(any(), any()) }.returns(Unit)
            every { resetDragFile() }.returns(Unit)
            every { cancelDragFileOnRemote() }.returns(Unit)
        })

        every { PCConnectDataHelper.isTouchEventFromPC(any()) }.returns(true)
        val recyclerView = mock(RecyclerView::class.java)
        var event = MotionEvent.obtain(0L, 0L, MotionEvent.ACTION_DOWN, 0F, 0F, 1)
        every { PCConnectDataHelper.isTouchEventFromPC(any()) }.returns(false)
        interceptor.onInterceptTouchEvent(recyclerView, event)

        every { PCConnectDataHelper.isTouchEventFromPC(any()) }.returns(true)
        interceptor.onInterceptTouchEvent(recyclerView, event)
        interceptor.mSentFileInfoToRemote = true
        event = MotionEvent.obtain(0L, 0L, MotionEvent.ACTION_MOVE, 0F, 0F, 1)
        interceptor.onInterceptTouchEvent(recyclerView, event)
        event = MotionEvent.obtain(0L, 0L, MotionEvent.ACTION_MOVE, 0.1F, 0.9F, 1)
        interceptor.onInterceptTouchEvent(recyclerView, event)

        event = MotionEvent.obtain(0L, 0L, MotionEvent.ACTION_UP, 0F, 0F, 1)
        interceptor.onTouchEvent(recyclerView, event)
        event = MotionEvent.obtain(0L, 0L, MotionEvent.ACTION_CANCEL, 0.1F, 0.9F, 1)
        interceptor.onTouchEvent(recyclerView, event)
    }

    @Test
    fun should_verify_can_view_long_press_whether_touch_from_pad() {
        val interceptor = PCConnectItemTouchInterceptor()
        every { UIConfigMonitor.isZoomWindowShow() }.returns(true)
        every { PCConnectController.sInstance }.returns(mockk {
            every { isPCScreenCast() }.returns(true)
            every { dragFileOnRemote(any(), any()) }.returns(Unit)
            every { resetDragFile() }.returns(Unit)
            every { cancelDragFileOnRemote() }.returns(Unit)
        })
        every { PCConnectDataHelper.isTouchEventFromPad(any()) }.returns(true)
        Assert.assertEquals(true, interceptor.checkViewCanLongPress(false))

        every { PCConnectDataHelper.isTouchEventFromPad(any()) }.returns(false)
        Assert.assertEquals(true, interceptor.checkViewCanLongPress(false))

        every { PCConnectDataHelper.isTouchEventFromPad(any()) }.returns(true)
        Assert.assertEquals(true, interceptor.checkViewCanLongPress(true))

        every { PCConnectDataHelper.isTouchEventFromPad(any()) }.returns(false)
        Assert.assertEquals(false, interceptor.checkViewCanLongPress(true))
    }

    @Test
    fun should_verify_move_file() {
        val file = File("./tmpFile")
        if (!file.exists()) {
            file.createNewFile()
        }
        val newFile = File("./tmpNewFile")
        if (newFile.exists()) {
            newFile.delete()
        }
        val result = PCConnectDataHelper.moveFile(file.absolutePath, newFile.absolutePath)
        Assert.assertEquals(true, result)
        newFile.delete()
    }

    @Test
    fun `should return false when catch exception or pad state is not 1`() {
        val result = try {
            mPCConnectController.checkIsPadConnect()
        } catch (e: Throwable) {
            false
        }
        Assert.assertEquals(false, result)
        val controller = spyk(mPCConnectController) {
            every { mPadScreenCastState } returns 0
        }
        Assert.assertEquals(false, controller.checkIsPadConnect())
    }

    @Test
    fun `should return false when user id equals -1 or 0`() {
        mockkStatic(Utils::class)
        every { Utils.getUserId() }.returns(-1)
        Assert.assertEquals(false, PCConnectController.isPadConnectSupport())

        every { Utils.getUserId() }.returns(0)
        Assert.assertEquals(false, PCConnectController.isPadConnectSupport())
        unmockkStatic(Utils::class)
    }

    @Test
    fun `should return multi screen true when support multi screen of pad or phone `() {
        every { PCConnectController.isPCConnectSupport() }.returns(false)
        every { PCConnectController.isPadConnectSupport() }.returns(true)
        Assert.assertEquals(true, PCConnectApi.isMultiScreenConnectSupport())

        every { PCConnectController.isPCConnectSupport() }.returns(true)
        every { PCConnectController.isPadConnectSupport() }.returns(false)
        Assert.assertEquals(true, PCConnectApi.isMultiScreenConnectSupport())

        every { PCConnectController.isPCConnectSupport() }.returns(true)
        every { PCConnectController.isPadConnectSupport() }.returns(true)
        Assert.assertEquals(true, PCConnectApi.isMultiScreenConnectSupport())
    }

    @Test
    fun `should return multi screen false when not support multi screen of pad and phone `() {
        every { PCConnectController.isPCConnectSupport() }.returns(false)
        every { PCConnectController.isPadConnectSupport() }.returns(false)
        Assert.assertEquals(false, PCConnectApi.isMultiScreenConnectSupport())
    }

    @Test
    fun `should return pad path when is support pc feature and not support pad feature`() {
        Assert.assertEquals(
            PCConnectController.HEY_PAD_PATH,
            PCConnectApi.getMultiScreenConnectDirList()[0]
        )
        Assert.assertEquals(
            PCConnectController.HEY_PC_PATH,
            PCConnectApi.getMultiScreenConnectDirList()[1]
        )
    }

    @Test
    fun `should return true when screen cast is 0 to 1 or 2`() {
        mPCConnectController.mOldPCScreenCastState = PCConnectController.PC_SCREEN_STATE_NONE
        mPCConnectController.mPCScreenCastState = PCConnectController.PC_SCREEN_STATE_SOCKET_CONNECTED
        Assert.assertEquals(true, mPCConnectController.checkPCScreenCastForOpenClient())
        mPCConnectController.mPCScreenCastState = PCConnectController.PC_SCREEN_STATE_CAST
        Assert.assertEquals(true, mPCConnectController.checkPCScreenCastForOpenClient())
    }

    @Test
    fun `should return false when screen cast is not 0`() {
        mPCConnectController.mOldPCScreenCastState = PCConnectController.PC_SCREEN_STATE_SOCKET_CONNECTED
        mPCConnectController.mPCScreenCastState = PCConnectController.PC_SCREEN_STATE_NONE
        Assert.assertEquals(false, mPCConnectController.checkPCScreenCastForOpenClient())
        mPCConnectController.mOldPCScreenCastState = PCConnectController.PC_SCREEN_STATE_SOCKET_CONNECTED
        Assert.assertEquals(false, mPCConnectController.checkPCScreenCastForOpenClient())
        mPCConnectController.mOldPCScreenCastState = PCConnectController.PC_SCREEN_STATE_CAST
        Assert.assertEquals(false, mPCConnectController.checkPCScreenCastForOpenClient())
    }

    @Test
    fun `should not execute has agree privacy when check pad screen if tablet is not`() {
        //Given
        every { ModelUtils.isTablet() } returns false
        every { PrivacyPolicyController.hasAgreePrivacy() } returns false
        val controller = spyk(mPCConnectController)
        justRun { controller.initPadConnectObserver() }
        justRun { controller.unregisterPadObserver() }
        //when
        controller.checkPadConnect()
        //Then
        verify(inverse = true) { controller.initPadConnectObserver() }
        verify(inverse = true) { controller.unregisterPadObserver() }
    }

    @Test
    fun `should execute has agree privacy when check pad screen if tablet is true`() {
        //Given
        every { ModelUtils.isTablet() } returns true
        every { PrivacyPolicyController.hasAgreePrivacy() } returns false
        val controller = spyk(mPCConnectController)
        every { controller.checkPadConnect() } answers { callOriginal() }
        //when
        controller.checkPadConnect()
        //Then
        verify { PrivacyPolicyController.hasAgreePrivacy() }
    }

    @Test
    fun `should execute init observer when check pad connect if has agree and has storage permission`() {
        //Given
        every { ModelUtils.isTablet() } returns true
        every { PrivacyPolicyController.hasAgreePrivacy() } returns true
        every { PermissionUtils.hasStoragePermission() } returns true
        val controller = spyk(mPCConnectController)
        justRun { controller.initPadConnectObserver() }
        //when
        controller.checkPadConnect()
        //Then
        verify { controller.initPadConnectObserver() }
    }

    @Test
    fun `should not execute init when check pad connect if not agree or not storage permission`() {
        //Given
        every { ModelUtils.isTablet() } returns true
        val controller = mockk<PCConnectController>()
        justRun { controller.initPadConnectObserver() }
        justRun { controller.unregisterPadObserver() }
        every { controller.checkPadConnect() } answers { callOriginal() }
        //when
        controller.checkPadConnect()
        every { PrivacyPolicyController.hasAgreePrivacy() } returns false
        every { PermissionUtils.hasStoragePermission() } returns true
        //Then
        verify(inverse = true) { controller.initPadConnectObserver() }

        //when
        every { PrivacyPolicyController.hasAgreePrivacy() } returns true
        every { PermissionUtils.hasStoragePermission() } returns false
        //Then
        verify(inverse = true) { controller.initPadConnectObserver() }

        //when
        every { PrivacyPolicyController.hasAgreePrivacy() } returns false
        every { PermissionUtils.hasStoragePermission() } returns false
        //Then
        verify(inverse = true) { controller.initPadConnectObserver() }
    }

    @Test
    fun `should execute has agree privacy when check pc screen if is support pc`() {
        //Given
        every { PrivacyPolicyController.hasAgreePrivacy() } returns false
        every { PCConnectController.isPCConnectSupport() } returns true
        val controller = spyk(mPCConnectController)
        justRun { controller.initPCConnectObserver() }
        //when
        controller.checkPCConnect()
        //Then
        verify(inverse = true) { controller.initPCConnectObserver() }
    }

    @Ignore
    @Test
    fun `should not execute has agree privacy when check pc screen if is not support pc`() {
        //Given
        every { PrivacyPolicyController.hasAgreePrivacy() } returns false
        every { PCConnectController.isPCConnectSupport() } returns false
        //when
        mPCConnectController.checkPCConnect()
        //Then
        verify(inverse = true) { PrivacyPolicyController.hasAgreePrivacy() }
    }

    @Test
    fun `should execute init pc observer when check pc screen if is support pc and has agree and has permission`() {
        //Given
        val controller = spyk(mPCConnectController)
        justRun { controller.initPCConnectObserver() }
        every { PrivacyPolicyController.hasAgreePrivacy() } returns true
        every { PermissionUtils.hasStoragePermission() } returns true
        every { PCConnectController.isPCConnectSupport() } returns true
        //When
        controller.checkPCConnect()
        //Then
        verify { controller.initPCConnectObserver() }
    }

    @Test
    fun `should not execute init pc observer when check pc screen if is not support pc and no agree or no permission`() {
        //Given
        val controller = spyk(mPCConnectController)
        justRun { controller.initPCConnectObserver() }
        every { PrivacyPolicyController.hasAgreePrivacy() } returns true
        //When
        controller.checkPCConnect()

        every { PCConnectController.isPCConnectSupport() } returns true
        every { PermissionUtils.hasStoragePermission() } returns false
        //Then
        verify(inverse = true) { controller.initPCConnectObserver() }

        every { PCConnectController.isPCConnectSupport() } returns false
        every { PermissionUtils.hasStoragePermission() } returns true
        //Then
        verify(inverse = true) { controller.initPCConnectObserver() }

        every { PCConnectController.isPCConnectSupport() } returns false
        every { PermissionUtils.hasStoragePermission() } returns false
        //Then
        verify(inverse = true) { controller.initPCConnectObserver() }
    }

    @Test
    fun `should remove data when on hidden change if is hide`() {
        //Given
        val owner = mockk<LifecycleOwner>()
        val dataHelper = spyk(mPCConnectDataHelper) {
            every { removeDataSource(owner) } just Runs
        }
        val controller = spyk(mPCConnectController) {
            every { mDataHelper } returns dataHelper
        }
        //When
        controller.onFragmentHiddenChanged(owner, true)
        //Then
        verify { dataHelper.removeDataSource(owner) }
    }

    @Test
    fun `should add data when on hidden change if is not hide`() {
        //Given
        val owner = mockk<LifecycleOwner>()
        val dataHelper = spyk(mPCConnectDataHelper) {
            every { addDataSource(owner) } just Runs
        }
        val controller = spyk(mPCConnectController) {
            every { mDataHelper } returns dataHelper
        }
        //When
        controller.onFragmentHiddenChanged(owner, false)
        //Then
        verify { dataHelper.addDataSource(owner) }
    }

    @Test
    fun `should return false when open file on pc remote if is not support pc connect`() {
        //When
        every { PCConnectController.isPCConnectSupport() } returns false
        //Then
        Assert.assertEquals(false, mPCConnectController.openFileOnPCRemote(mockk(), null))
    }

    @Test
    fun `should return false when open file on pc remote if `() {
        //Given
        val dataHelper = mockk<PCConnectDataHelper>()
        val controller = spyk(mPCConnectController) {
            every { mDataHelper } returns dataHelper
            every { isPCScreenCast() } returns true
        }
        //When
        every { PCConnectController.isPCConnectSupport() } returns true
        every { PCConnectDataHelper.isTouchEventFromPC(any()) }.returns(true)
        every { controller.isPCScreenCast() }.returns(false)
        //Then
        Assert.assertEquals(false, controller.openFileOnPCRemote(mockk(), null))

        every { controller.isPCScreenCast() }.returns(true)
        every { PCConnectDataHelper.isTouchEventFromPC(mockk()) } returns true
        every { dataHelper.isFileSupportOpen(any()) } returns false
        val file = BaseFileBean()
        file.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
        file.mDisplayName = "测试"
        //Then
        Assert.assertEquals(false, controller.openFileOnPCRemote(file, null))
    }

    @Test
    fun `should execute unregister observer when unregister pad observer if observer is not null`() {
        val padScreenObserver = mockk<ContentObserver>()
        val refreshObserver = mockk<ContentObserver>()
        val contentRes = mockk<ContentResolver> {
            justRun { unregisterContentObserver(padScreenObserver) }
            justRun { unregisterContentObserver(refreshObserver) }
        }
        every { MyApplication.sAppContext } returns mockk {
            every { contentResolver } returns contentRes
        }
        val controller = mockk<PCConnectController> {
            every { unregisterPadObserver() } answers { callOriginal() }
            every { mPadScreenCastStateObserver } returns padScreenObserver
            every { mPadRefreshObserver } returns refreshObserver
        }
        //when
        controller.unregisterPadObserver()
        //then
        verify {
            contentRes.unregisterContentObserver(padScreenObserver)
            contentRes.unregisterContentObserver(refreshObserver)
        }
    }

    @Test
    fun `should return true when isDirExist if all true`() {
        every { PCConnectController.checkPathExist(any()) } returns true
        assertTrue(PCConnectController.isDirExist())
    }

    @Test
    fun `should return true when isDirExist if pc not exist`() {
        every { PCConnectController.checkPathExist(PCConnectController.HEY_PAD_PATH) } returns true
        every { PCConnectController.checkPathExist(PCConnectController.HEY_PC_PATH) } returns false
        assertTrue(PCConnectController.isDirExist())
    }

    @Test
    fun `should return true when isDirExist if pad not exist`() {
        every { PCConnectController.checkPathExist(PCConnectController.HEY_PAD_PATH) } returns false
        every { PCConnectController.checkPathExist(PCConnectController.HEY_PC_PATH) } returns true
        assertTrue(PCConnectController.isDirExist())
    }

    @Test
    fun `should return false when isDirExist if all false`() {
        every { PCConnectController.checkPathExist(any()) } returns false
        assertFalse(PCConnectController.isDirExist())
    }
}