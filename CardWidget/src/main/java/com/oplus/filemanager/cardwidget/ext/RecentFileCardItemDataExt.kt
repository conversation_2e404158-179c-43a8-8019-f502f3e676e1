/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentFileCardItemDataExt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/30
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/30      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.ext

import com.filemanager.common.helper.MimeTypeHelper
import com.oplus.filemanager.cardwidget.data.RecentFileCardItemData

fun RecentFileCardItemData.isSupportDuration(): Boolean {
    return type == MimeTypeHelper.VIDEO_TYPE
}