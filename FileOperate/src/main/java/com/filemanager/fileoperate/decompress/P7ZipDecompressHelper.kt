/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: P7ZipDecompressHelper.kt
 ** Description: Decompress 7zip rar5.0 file
 ** Version: 1.0
 ** Date: 2021/12/10
 ** Author: hankzhou(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.decompress

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.ArrayMap
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.utils.Log
import com.p7zip.util.UnCompress7ZipUtils
import java.io.File
import java.io.InputStream
import java.io.OutputStream
import java.io.FileOutputStream
import java.io.IOException

class P7ZipDecompressHelper : DecompressHelper<P7ZipDecompressFile>() {
    companion object {
        private const val TAG = "P7ZipDecompressHelper"
        @VisibleForTesting
        const val P7ZIPSO_NAME = "libp7zip_1.so"
        private const val RET_SUCCESS = 0
        private const val RET_CANCELED = -1
        private const val STEP_COUNT = 4
        private const val DIR_INDEX = 1
        private const val ENCRYPT_INDEX = 2
        private const val FILE_SIZE_INDEX = 3
        private const val PROGRESS_DONE = 100
        private const val ERROR_SIZE = 2
        private const val BUFFER_COUNT = 1024
        @VisibleForTesting
        const val WAIT_PROGRESS = 10
        private const val WAIT_TIME = 4000L
        private val mPasswordMap = HashMap<String, String>()
        private val mCancelledFiles = ArrayList<String>()
        @VisibleForTesting
        val mLockObj = Object()

        init {
            kotlin.runCatching {
                initP7zipSo()
            }.onFailure {
                Log.e(TAG, "initP7zipSo exception", it)
            }
        }

        @VisibleForTesting
        fun initP7zipSo() {
            Log.d(TAG, "initP7zipSo start")
            if (!p7zipSoExist()) {
                copyP7ZipSoToData()
            }
            val file = MyApplication.sAppContext.getDir("libs", Context.MODE_PRIVATE)
            val soFile = File(file, P7ZIPSO_NAME)
            System.load(soFile.absolutePath)
            Log.d(TAG, "initP7zipSo end")
        }

        @VisibleForTesting
        fun p7zipSoExist(): Boolean {
            val file = MyApplication.sAppContext.getDir("libs", Context.MODE_PRIVATE)
            val soFile = File(file, P7ZIPSO_NAME)
            return soFile.exists()
        }

        @VisibleForTesting
        fun copyP7ZipSoToData() {
            Log.d(TAG, "copyP7ZipSoToData")
            var fis: InputStream? = null
            var fos: OutputStream? = null
            try {
                val manager = MyApplication.sAppContext.assets
                fis = manager.open(P7ZIPSO_NAME)
                val file = MyApplication.sAppContext.getDir("libs", Context.MODE_PRIVATE)
                val soFile = File(file, P7ZIPSO_NAME)
                fos = FileOutputStream(soFile)
                val buffer = ByteArray(BUFFER_COUNT)
                while (true) {
                    var readcount = fis?.read(buffer) ?: 0
                    if (readcount <= 0) {
                        break
                    }
                    fos?.write(buffer, 0, readcount)
                }
                fos?.flush()
            } catch (e: IOException) {
                Log.d(TAG, "copyP7ZipSoToData e = $e")
            } finally {
                try {
                    fis?.close()
                    fos?.close()
                } catch (e: IOException) {
                    Log.d(TAG, "copyP7ZipSoToData close e = $e")
                }
            }
        }

        fun savePassword(filepath: String, password: String) {
            if (!TextUtils.isEmpty(filepath) && !TextUtils.isEmpty(password)) {
                mPasswordMap.put(filepath, password)
            }
        }

        fun clearPassword() {
            mPasswordMap.clear()
        }

        fun getPassword(filepath: String): String? {
            return mPasswordMap[filepath]
        }

        @VisibleForTesting
        fun removeCancelledFile(filepath: String) {
            synchronized(mCancelledFiles) {
                mCancelledFiles.remove(filepath)
            }
        }

        @VisibleForTesting
        fun addCancelledFile(filepath: String) {
            synchronized(mCancelledFiles) {
                mCancelledFiles.add(filepath)
            }
        }

        @VisibleForTesting
        fun isNeedWait(): Boolean {
            return mCancelledFiles.size > 2
        }

        @VisibleForTesting
        fun notifyLockReleased() {
            try {
                synchronized(mLockObj) {
                    mLockObj.notify()
                }
            } catch (e: InterruptedException) {
                Log.d(TAG, "notifyLockReleased e = $e")
            }
        }

        @VisibleForTesting
        fun waitLockRelease(): Boolean {
            try {
                synchronized(mLockObj) {
                    mLockObj.wait()
                }
            } catch (e: InterruptedException) {
                return false
            }
            return true
        }
    }

    @VisibleForTesting
    var mListener: OnDecompressListener? = null
    @VisibleForTesting
    var mSourcePath: String? = null
    @VisibleForTesting
    var mInitProgress = 0

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    public override fun createDecompressDirFile(path: String): P7ZipDecompressFile {
        return P7ZipDecompressFile("", 0, true).easyCreateDirFile(path) as P7ZipDecompressFile
    }

    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    public override fun internalPreview(sourceFile: BaseFileBean): Pair<Int, MutableMap<String, MutableList<P7ZipDecompressFile>?>?> {
        Log.d(TAG, "internalPreview start")
        val sbCmd = StringBuilder("7z ")
        sbCmd.append("l ")
        sbCmd.append("'${sourceFile.mData}' ")
        val password = mPasswordMap.get(sourceFile.mData)
        if (!TextUtils.isEmpty(password)) {
            sbCmd.append(" -p$password ")
        }
        val lists = UnCompress7ZipUtils.getRarInfoList(sbCmd.toString())
        Log.d(TAG, "internalPreview start 2 lists.size = ${lists.size}")
        var compressFiles = ArrayMap<String, MutableList<P7ZipDecompressFile>?>()
        if (lists.size == ERROR_SIZE) {
            return Pair(PREVIEW_FAILED, null)
        }

        if (lists.size > MAX_COMPRESS_FILE_NUM_7Z * STEP_COUNT) {
            return Pair(PREVIEW_OVERCOUNT, null)
        }

        for (index in lists.indices step STEP_COUNT) {
            val data = lists[index] as String
            val isDir = Integer.parseInt(lists[index + DIR_INDEX] as String) == 1
            val fileSize = Integer.parseInt(lists[index + FILE_SIZE_INDEX] as String)
            val file = P7ZipDecompressFile(data, fileSize, isDir)
            createPreviewFileTree(file, compressFiles)
        }
        return Pair(PREVIEW_SUCCESS, compressFiles)
    }

    @VisibleForTesting
    public override fun internalDecompress(
        sourceFile: BaseFileBean,
        destParentFile: BaseFileBean,
        password: String?,
        selectFiles: MutableList<P7ZipDecompressFile>?,
        listener: OnDecompressListener?
    ): Boolean? {
        Log.d(TAG, "internalDecompress")

        mListener = listener
        mListener?.onDecompressing(1)
        mInitProgress = 1
        if (isNeedWait()) {
            val handler = Handler(Looper.getMainLooper())
            handler.postDelayed({ mListener?.onDecompressing(WAIT_PROGRESS) }, WAIT_TIME)
            mInitProgress = WAIT_PROGRESS
            if (!waitLockRelease()) {
                return false
            }
        }
        if (isCancelled()) {
            return false
        }
        mSourcePath = sourceFile.mData
        val filepath = sourceFile.mData
        val dirpath = destParentFile.mData
        val sbCmd = StringBuilder("7z ")
        sbCmd.append("x ")

        //input file path
        sbCmd.append("'$filepath' ")

        //output path
        sbCmd.append("'-o$dirpath' ")

        if (password.isNullOrEmpty().not()) {
            sbCmd.append("'-p$password' ")
        } else {
            val previewPassword = getPassword(sourceFile.mData!!)
            if (previewPassword.isNullOrEmpty().not()) {
                sbCmd.append("'-p$previewPassword' ")
            }
        }
        selectFiles?.let {
            if (it.size > 0) {
                for (p7file in it) {
                    sbCmd.append("'-i!${p7file.mData}' ")
                    Log.d(TAG, "internalDecompress p7file.mData =  ${p7file.mData}")
                }
            }
        }

        sbCmd.append("-aoa ")
        //sbCmd.append("l ")
        //sbCmd.append("'$filepath' ")
        UnCompress7ZipUtils.setHelper(this)
        var result = UnCompress7ZipUtils.executeCommand(sbCmd.toString())
        if (isCancelled().not()) {
            mListener?.onDecompressing(PROGRESS_DONE)
        } else {
            result = RET_CANCELED
        }
        mListener = null
        if (UnCompress7ZipUtils.getHelper() == this) {
            UnCompress7ZipUtils.setHelper(null)
        }
        if (result != RET_SUCCESS) {
            Log.d(
                TAG,
                "internalDecompress destParentFile.mData =  ${destParentFile.mData} result = $result"
            )
            deleteAllFileInDir(File(destParentFile.mData))
            if (result == RET_CANCELED) {
                mSourcePath?.let {
                    removeCancelledFile(it)
                }
                if (!isNeedWait()) {
                    notifyLockReleased()
                }
            }
        }
        return result == RET_SUCCESS
    }

    @VisibleForTesting
    fun deleteAllFileInDir(file: File) {
        if (file.isDirectory) {
            val lists = JavaFileHelper.listFiles(file)?.toList()
            lists?.let {
                for (child in lists) {
                    deleteAllFileInDir(child)
                }
            }
            file.delete()
        } else {
            file.delete()
        }
    }

    override fun internalIsEncrypted(sourceFile: BaseFileBean): Boolean {
        Log.d(TAG, "internalIsEncrypted start")
        val sbCmd = StringBuilder("7z ")
        sbCmd.append("l ")
        sbCmd.append("'${sourceFile.mData}' ")
        val lists = UnCompress7ZipUtils.getRarInfoList(sbCmd.toString())
        Log.d(TAG, "internalIsEncrypted lists size = ${lists.size}")
        if (lists.size == ERROR_SIZE) {
            return true
        }
        for (index in lists.indices step STEP_COUNT) {
            val isEncrypt = Integer.parseInt(lists[index + ENCRYPT_INDEX] as String) == 1
            Log.d(TAG, "internalIsEncrypted isEncrypt = $isEncrypt")
            if (isEncrypt) {
                return isEncrypt
            }
        }
        return false
    }

    override fun internalVerifyPassword(sourceFile: BaseFileBean, password: String?): Boolean {
        val sbCmd = StringBuilder("7z ")
        sbCmd.append("t ")
        sbCmd.append("'${sourceFile.mData}' ")
        sbCmd.append("'-p$password' ")
        sbCmd.append("-r ")
        val result = UnCompress7ZipUtils.executeCommand(sbCmd.toString())
        Log.d(TAG, "internalVerifyPassword result =  $result} ")
        return result == RET_SUCCESS
    }

    fun updateProgress(progress: Int) {
        if (progress > mInitProgress) {
            mListener?.onDecompressing(progress)
        }
    }

    override fun checkIsSupport(sourceFile: BaseFileBean): Boolean {
        return true
    }

    fun checkPreviewIsEncrypted(sourceFile: BaseFileBean): Boolean {
        Log.d(TAG, "checkPreviewIsEncrypted start")
        val sbCmd = StringBuilder("7z ")
        sbCmd.append("l ")
        sbCmd.append("'${sourceFile.mData}' ")
        val lists = UnCompress7ZipUtils.getRarInfoList(sbCmd.toString())
        Log.d(TAG, "checkPreviewIsEncrypted lists size = ${lists.size}")
        if (lists.size == ERROR_SIZE) {
            return true
        }
        return false
    }

    override fun cancelDecompress() {
        super.cancelDecompress()
        mListener = null
        UnCompress7ZipUtils.setHelper(null)

        if (!mSourcePath.isNullOrEmpty()) {
            mSourcePath?.let {
                addCancelledFile(it)
            }
        } else {
            notifyLockReleased()
        }
    }
}