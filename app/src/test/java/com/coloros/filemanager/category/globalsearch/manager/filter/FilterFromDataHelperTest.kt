/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FilterFromDataHelperTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/07/28
 ** Author      : v-wang<PERSON><PERSON><EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/07/28       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.category.globalsearch.manager.filter

import android.content.Context
import android.os.Bundle
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.FilterConstants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.PCConnectAction
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterCondition
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants.FILTER_FROM_DFM
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants.FILTER_FROM_OTG
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants.FILTER_FROM_SD
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterFromDataHelper
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import com.oplus.filemanager.dfm.DFMManager
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.io.File

class FilterFromDataHelperTest {

    @Before
    fun setUp() {
        mockkStatic(FilterFromDataHelper::class)
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(FeatureCompat::class)
        mockkObject(FilterFromDataHelper.Companion)
        mockkObject(PCConnectAction)
    }

    @Test
    fun getTransferPathtest() {
        Assert.assertTrue(
            FilterFromDataHelper.getTransferPath(KtUtils.FLAVOR_OPPO)
                .indexOf(Constants.CATEGORY_PATH_OPPO_SHARE[0]) != -1)
        Assert.assertTrue(
            FilterFromDataHelper.getTransferPath(KtUtils.FLAVOR_ONEPLUS)
                .indexOf(Constants.CATEGORY_PATH_ONEPLUS_SHARE[0]) != -1)
        Assert.assertTrue(
            FilterFromDataHelper.getTransferPath(KtUtils.FLAVOR_REALME)
                .indexOf(Constants.CATEGORY_PATH_REALME_SHARE[0]) != -1)
    }

    @Test
    fun `should verify when filterData`() {
        initFilterFromDataHelper()
        val dataList = ArrayList<BaseFileBean>()
        val file1 = BaseFileBean()
        file1.mData = "${File.separator}sdcard" + File.separator + "Download${File.separator}"
        val file2 = BaseFileBean()
        file2.mData = "${File.separator}sdcard" + File.separator + "Download${File.separator}Document${File.separator}"
        val file3 = BaseFileBean()
        file3.mData = "${File.separator}sdcard" + File.separator + "DCIM${File.separator}"
        dataList.add(file1)
        dataList.add(file2)
        dataList.add(file3)
        val condition = FilterCondition(FilterConstants.FILTER_FROM_DOWNLOAD, "")
        val filterItem = FilterItem(FilterConstants.FILTER_FROM_DOWNLOAD, condition, "", null)
        filterItem.id = FilterConstants.FILTER_FROM_DOWNLOAD
        val helper = spyk(FilterFromDataHelper())
        Assert.assertEquals(2, helper.filterData(filterItem, dataList).size)
        filterItem.id = FilterConstants.FILTER_FROM_TRANSFER

        every { FilterFromDataHelper.getTransferPath(any()) }.returns(arrayOf("transfer${File.separator}"))
        file1.mData = "${File.separator}sdcard" + File.separator + "transfer${File.separator}"
        file2.mData = "${File.separator}sdcard" + File.separator + "transfer${File.separator}Document${File.separator}"
        Assert.assertEquals(0, helper.filterData(filterItem, dataList).size)
        filterItem.id = FilterConstants.FILTER_FROM_BLUETOOTH
        Assert.assertEquals(0, helper.filterData(filterItem, dataList).size)
        file1.mData = "${File.separator}sdcard" + File.separator + "bluetooth${File.separator}"
        Assert.assertEquals(1, helper.filterData(filterItem, dataList).size)
        file2.mData = "${File.separator}sdcard" + File.separator + "Download${File.separator}Bluetooth${File.separator}"
        Assert.assertEquals(2, helper.filterData(filterItem, dataList).size)
        file1.mData = "${File.separator}sdcard" + File.separator + "bluetooth${File.separator}test"
        Assert.assertEquals(2, helper.filterData(filterItem, dataList).size)
        file2.mData = "${File.separator}sdcard" + File.separator + "Download${File.separator}Bluetooth${File.separator}test"
        Assert.assertEquals(2, helper.filterData(filterItem, dataList).size)
        file2.mData = "${File.separator}sdcard" + File.separator + "download${File.separator}bluetooth${File.separator}"
        Assert.assertEquals(2, helper.filterData(filterItem, dataList).size)
        every { PCConnectAction.isMultiScreenConnectSupport() }.returns(false)
        filterItem.id = FilterConstants.FILTER_FROM_PC_CONNECT
        Assert.assertEquals(3, helper.filterData(filterItem, dataList).size)
        every { PCConnectAction.isMultiScreenConnectSupport() }.returns(true)
        every { PCConnectAction.getMultiScreenConnectDirList() }.returns(arrayOf("connect${File.separator}"))
        Assert.assertEquals(0, helper.filterData(filterItem, dataList).size)
        file1.mData = "${File.separator}sdcard" + File.separator + "connect${File.separator}test"
        Assert.assertEquals(1, helper.filterData(filterItem, dataList).size)
        filterItem.id = FilterConstants.FILTER_FROM_CURRENT
        filterItem.packageName = "DCIM${File.separator}"
        Assert.assertEquals(1, helper.filterData(filterItem, dataList).size)
    }

    @Test
    fun `should verify when filterDynamicData`() {
        val context = mockk<Context>(relaxed = true)
        every { context.applicationContext }.returns(context)
        MyApplication.init(context)
        every { VolumeEnvironment.getInternalSdPath(context) }.returns("${File.separator}sdcard" + File.separator + "0" + File.separator)
        every { VolumeEnvironment.getExternalSdPath(context) }.returns("${File.separator}sdcard" + File.separator + "1" + File.separator)

        every { FeatureCompat.sIsSupportMultiApp }.returns(false)
        every { VolumeEnvironment.isSingleSdcard(context) } returns false
        every { VolumeEnvironment.isNeedLoadPath(context, any()) }.returns(false)

        mockkStatic(DFMManager::class)
        every { DFMManager.getCurrentStatus() } returns DFMManager.STATUS_META_DATA_OK
        every { DFMManager.openP2pConnectAndWaitDFSReady() } returns true
        every { DFMManager.checkStateSearchAvalable() } returns true
        every { DFMManager.getDFSDeviceName() } returns "OPPOPAD"
        every { DFMManager.getDFSDevice() } answers {
            val result = Bundle()
            result.putInt(KtConstants.DFM_DEVICE_TYPE, KtConstants.DFM_PHONE_TYPE)
            result
        }

        val dataList = ArrayList<BaseFileBean>()
        val file1 = BaseFileBean()
        file1.mData = "/mnt/dfs/" + "Download${File.separator}"
        val file2 = BaseFileBean()
        file2.mData = "${File.separator}sdcard" + File.separator + "0" + File.separator +  "Download${File.separator}Document${File.separator}"
        val file3 = BaseFileBean()
        file3.mData = "${File.separator}sdcard" + File.separator + "1" + File.separator +  "DCIM${File.separator}"
        val file4 = BaseFileBean()
        file4.mData = "${File.separator}sdcard" + File.separator + "919" + File.separator +  "DCIM${File.separator}"
        dataList.add(file1)
        dataList.add(file2)
        dataList.add(file3)
        dataList.add(file4)
        val condition = FilterCondition(FilterConstants.FILTER_FROM_DOWNLOAD, "")
        val filterItem = FilterItem(FilterConstants.FILTER_FROM_DOWNLOAD, condition, "", null)
        filterItem.id = FILTER_FROM_DFM
        val helper = spyk(FilterFromDataHelper())
        Assert.assertEquals(1, helper.filterData(filterItem, dataList).size)
        filterItem.id = FILTER_FROM_SD
        Assert.assertEquals(1, helper.filterData(filterItem, dataList).size)
        filterItem.id = FILTER_FROM_OTG
        Assert.assertEquals(1, helper.filterData(filterItem, dataList).size)
    }


    private fun initFilterFromDataHelper() {
        val context = mockk<Context>(relaxed = true)
        every { context.applicationContext }.returns(context)
        MyApplication.init(context)
        every { VolumeEnvironment.getInternalSdPath(context) }.returns("${File.separator}sdcard" + File.separator)
        every { VolumeEnvironment.getExternalSdPath(context) }.returns("${File.separator}sdcard" + File.separator)

        every { FeatureCompat.sIsSupportMultiApp }.returns(false)
        every { VolumeEnvironment.isNeedLoadPath(context, any()) }.returns(false)
    }

    @After
    fun tearDown() {
        unmockkStatic(FilterFromDataHelper::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(FeatureCompat::class)
        unmockkObject(FilterFromDataHelper.Companion)
        unmockkObject(PCConnectAction)
    }
}