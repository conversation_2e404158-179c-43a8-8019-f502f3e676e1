/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - EraseOperationTest.java
 * Description: Test case for Erase operation
 * Version: 1.0
 * Date : 2020/04/20
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/04/20    1.0     create
 ****************************************************************/

package com.coloros.filemanager.recyclebin.operation

import android.net.Uri
import androidx.activity.ComponentActivity
import com.coloros.filemanager.BaseTest
import com.filemanager.common.base.BaseFileBean
import com.filemanager.recyclebin.operation.EraseOperation
import com.filemanager.recyclebin.operation.listener.EraseOperationListener
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import shadow.ShadowViewHelper
import shadow.ShadowVolumeEnvironment

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
@Config(shadows = [ShadowViewHelper::class, ShadowVolumeEnvironment::class])
class EraseOperationTest : BaseTest() {
    private lateinit var mActivity: ComponentActivity

    @Before
    override fun setUp() {
        mActivity = Robolectric.buildActivity(ComponentActivity::class.java).get()
    }

    @After
    override fun tearDown() {
    }

    @Test
    fun should_vertify_when_addPaths_isNull() {
        val progressListener = EraseOperationListener(mActivity, null);
        val eraseOperation = EraseOperation(mActivity, progressListener)
        try {
            eraseOperation.addPaths(null)
            fail("addUris1 should throw UnsupportedOperationException")
        } catch (ex: Exception) {
        }

        try {
            eraseOperation.addPaths(ArrayList<String>())
            fail("addUris2 should throw UnsupportedOperationException")
        } catch (ex: Exception) {
        }
    }

    @Test
    fun should_vertify_when_addFileBeans_isNull() {
        val progressListener = EraseOperationListener(mActivity, null);
        val eraseOperation = EraseOperation(mActivity, progressListener)
        assertFalse(eraseOperation.addFileBeans(null))
        assertTrue(eraseOperation.addFileBeans(ArrayList<BaseFileBean>()))
        assertFalse(eraseOperation.addFileBeans(ArrayList<BaseFileBean>()))
    }

    @Test
    fun should_vertify_when_addUris_isNull() {
        val progressListener = EraseOperationListener(mActivity, null);
        val eraseOperation = EraseOperation(mActivity, progressListener)
        assertFalse(eraseOperation.addUris(null))
        assertTrue(eraseOperation.addUris(ArrayList<Uri>()))
        assertFalse(eraseOperation.addUris(ArrayList<Uri>()))
    }
}