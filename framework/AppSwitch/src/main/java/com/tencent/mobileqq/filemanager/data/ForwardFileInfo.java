/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : ForwardFileInfo.kt
 * * Description : 这个类只有在QQ中，聊天记录详情页面点击ppt，txt，doc等格式文件，从MultiForwardActivity进入
 * *               到QQTranslucentBrowserActivity时才会携带，内部有文件名称和大小的部分数据
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.tencent.mobileqq.filemanager.data;

import android.os.Parcel;
import android.os.Parcelable;
import android.util.Log;

import androidx.annotation.NonNull;

public class ForwardFileInfo implements Parcelable {

    public static final Creator<ForwardFileInfo> CREATOR = new Creator<ForwardFileInfo>() {
        @Override
        public ForwardFileInfo createFromParcel(Parcel in) {
            return new ForwardFileInfo(in);
        }

        @Override
        public ForwardFileInfo[] newArray(int size) {
            return new ForwardFileInfo[size];
        }
    };

    public static final String TAG = "ForwardFileInfo";

    public int mA;
    private long mB;
    private long mSize;
    private String mD;
    private String mE;
    private String mF;
    private int mG;
    private String mFileName;
    private long mI;
    private int mJ;
    private long mK;
    private String mL;
    private String mM;


    protected ForwardFileInfo(Parcel parcel) {
        try {
            this.mA = parcel.readInt();
            this.mB = parcel.readLong();
            this.mSize = parcel.readLong();
            this.mD = parcel.readString();
            this.mE = parcel.readString();
            this.mF = parcel.readString();
            this.mG = parcel.readInt();
            this.mFileName = parcel.readString();
            this.mI = parcel.readLong();
            this.mJ = parcel.readInt();
            this.mK = parcel.readLong();
            this.mL = parcel.readString();
            this.mM = parcel.readString();
        } catch (Exception e) {
            Log.e("HYW", "ForwardFileInfo from parcel error", e);
        }
    }

    public long getSize() {
        return mSize;
    }

    public String getFileName() {
        return mFileName;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        Log.i(TAG, "writeToParcel");
    }

    @Override
    public String toString() {
        return "ForwardFileInfo{"
                + "mA=" + mA
                + ", mB=" + mB
                + ", mC=" + mSize
                + ", mD='" + mD + '\''
                + ", mE='" + mE + '\''
                + ", mF='" + mF + '\''
                + ", mG=" + mG
                + ", mH='" + mFileName + '\''
                + ", mI=" + mI
                + ", mJ=" + mJ
                + ", mK=" + mK
                + ", mL='" + mL + '\''
                + ", mM='" + mM + '\''
                + '}';
    }
}
