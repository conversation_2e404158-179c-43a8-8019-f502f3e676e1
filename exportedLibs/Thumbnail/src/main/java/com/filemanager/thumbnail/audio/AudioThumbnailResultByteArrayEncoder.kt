/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.audio
 ** Version: 1.0
 ** Date: 2021/7/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.audio

import androidx.annotation.Keep
import com.bumptech.glide.load.Encoder
import com.bumptech.glide.load.Options
import com.filemanager.thumbnail.utils.Log
import java.io.*

/**
 * convert AudioThumbnailResultByteArray to ObjectOutputStream, and then cache the object to File
 */
@Keep
class AudioThumbnailResultByteArrayEncoder : Encoder<AudioThumbnailResultByteArray> {

    companion object {
        private const val TAG = "AudioThumbnailResultByteArrayEncoder"
    }

    override fun encode(data: AudioThumbnailResultByteArray, file: File, options: Options): Boolean {
        val os: OutputStream?
        return try {
            os = ObjectOutputStream(FileOutputStream(file))
            os.use {
                it.writeObject(data)
            }
            true
        } catch (e: Exception) {
            Log.d(TAG, "Failed to encode Bitmap: $e")
            false
        }
    }
}