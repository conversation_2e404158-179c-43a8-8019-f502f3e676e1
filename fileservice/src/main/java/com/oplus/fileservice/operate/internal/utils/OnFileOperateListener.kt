/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OnDealFileListener
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/24 11:10
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/6/24       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.utils

import java.io.File

abstract class OnFileOperateListener {

    private var currentProgress = 0L

    fun addProgress(progress: Long) {
        currentProgress += progress
    }

    abstract var isCancel: () -> Boolean

    /**
     * Called when the operation is successful.
     *
     * @param sourceFile Source File
     * @param destFile Target File
     */
    abstract fun onSuccess(sourceFile: File, destFile: File)

    /**
     * Called when the operation is failure.
     *
     * @param sourceFile Source File
     * @param destFile Target File
     */
    abstract fun onFailure(sourceFile: File, destFile: File): Boolean
}