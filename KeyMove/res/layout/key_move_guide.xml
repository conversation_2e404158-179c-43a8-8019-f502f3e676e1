<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/relative_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <include layout="@layout/appbar_with_divider_layout_secondary" />

    <RelativeLayout
        android:id="@+id/guide_center_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/move_tip_tv">

        <ImageView
            android:id="@+id/move_guide_img"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@null"
            android:visibility="gone"
            android:src="@drawable/akey_move_guide"
            android:layout_centerHorizontal="true"
            android:scaleType="fitCenter" />

    </RelativeLayout>

    <TextView
        android:id="@+id/move_title_tv"
        style="@style/akey_move_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/appbar_layout"
        android:layout_centerHorizontal="true"
        android:text="@string/text_onetap_to_move_sdcard" />

    <TextView
        android:id="@+id/move_tip_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/move_title_tv"
        android:layout_centerHorizontal="true"
        android:layout_marginHorizontal="@dimen/default_margin"
        android:layout_marginTop="@dimen/akeytomove_tip_margin_top"
        android:gravity="center_horizontal"
        android:text="@string/string_a_key_move_detailed_description"
        android:textColor="@color/black_55_percent"
        android:textSize="@dimen/akeytomove_tip_text_size" />
    <FrameLayout android:layout_height="@dimen/operation_btn_background_height"
        android:layout_width="match_parent"
        android:layout_alignParentBottom="true"
        android:background="?attr/couiColorBackgroundWithCard">
        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_background_height"
            android:layout_gravity="top"
            android:alpha="0"
            android:background="?attr/couiColorDivider"
            android:forceDarkAllowed="false" />
        <com.coui.appcompat.button.COUIButton
            style="@style/Widget.COUI.Button.Large"
            android:id="@+id/start_btn"
            android:layout_gravity="bottom|center_horizontal"
            android:layout_marginBottom="@dimen/operation_btn_margin_bottom"
            android:layout_width="@dimen/operation_btn_width"
            android:layout_height="@dimen/operation_btn_height"
            android:paddingHorizontal="@dimen/dimen_7dp"
            android:paddingVertical="@dimen/dimen_2dp"
            android:lineSpacingExtra="@dimen/akeytomove_line_spacing_extra"
            android:text="@string/string_a_key_moving_menue"
            android:textSize="@dimen/akeytomove_btn_text_size"/>
    </FrameLayout>

</RelativeLayout>