/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - LogPrintExtends.kt
 * Description:
 *     The extensions for print log.
 *
 * Version: 1.0
 * Date: 2024-09-24
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-09-24   1.0    Create this module
 *********************************************************************************/
package com.filemanager.thumbnail.utils

import android.net.Uri

private fun getNameExtension(uri: Uri?): String? {
    var extension: String? = null
    val fileName = uri?.toString()
    if (!fileName.isNullOrEmpty() && fileName.contains(".")) {
        val nameParts = fileName.split("\\.".toRegex()).toTypedArray()
        if (nameParts.isNotEmpty()) {
            extension = nameParts[nameParts.size - 1]
        }
    }
    return extension
}

internal fun Uri.logInfo(): String = "${hashCode()}_${getNameExtension(this)}"