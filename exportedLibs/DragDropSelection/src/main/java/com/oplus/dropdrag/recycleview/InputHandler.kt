/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - MotionInputHandler.kt
 ** Description: all input handler
 ** Version: 1.0
 ** Date : 2020/05/16
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/16    1.0     create
 ****************************************************************/

package com.oplus.dropdrag.recycleview

import android.view.MotionEvent
import android.view.SoundEffectConstants
import com.oplus.dropdrag.OnDragStartListener
import com.oplus.dropdrag.OnItemClickListener
import com.oplus.dropdrag.SelectionTracker
import com.oplus.dropdrag.SelectionTracker.Companion.DEBUG
import com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails
import com.oplus.dropdrag.recycleview.SelectionPredicates.SelectionPredicate
import com.oplus.dropdrag.utils.Log

internal abstract class MotionInputHandler<K>(
        selectionTracker: SelectionTracker<K>
) : ExtendGestureListener() {

    companion object {
        private const val TAG = "MotionInputHandler"
    }

    protected val mSelectionTracker: SelectionTracker<K> = selectionTracker

    fun selectItem(details: ItemDetails<K>): Boolean {
        Log.d(TAG, "selectItem position : " + details.position)
        details.selectionKey?.let {
            if (mSelectionTracker.selectItem(it)) {
                return true
            }
        }
        return false
    }

    fun deselectItem(details: ItemDetails<K>): Boolean {
        Log.d(TAG, "deselectItem position : " + details.position)
        details.selectionKey?.let {
            if (mSelectionTracker.deselectItem(it)) {
                return true
            }
        }
        return false
    }

    fun enterSelectionMode(details: ItemDetails<K>): Boolean {
        Log.d(TAG, "enterSelectionMode position : " + details.position)
        val key = details.selectionKey ?: return false
        if (mSelectionTracker.enterSelectMode(key)) {
            return true
        }
        return false
    }
}

/**
 * A MotionInputHandler that provides the high-level glue for touch driven selection. This class
 * works with {@link RecyclerView}, {@link GestureRouter}, and {@link SlideSelectionHelper} to
 * to implement the primary policies around touch input.
 */
internal class TouchInputHandler<K>(
    selectionTracker: SelectionTracker<K>,
    detailsLookup: ItemDetailsLookup<K>?,
    selectionPredicate: SelectionPredicate<K>,
    onDragInitiatedListener: OnDragStartListener?,
    onItemClickListener: OnItemClickListener<K>?,
    hapticPerformer: Runnable) : MotionInputHandler<K>(selectionTracker) {

    companion object {
        private const val TAG = "TouchInputHandler"
    }

    private val mDetailsLookup: ItemDetailsLookup<K>? = detailsLookup
    private val mSelectionPredicate: SelectionPredicate<K> = selectionPredicate
    private val mOnItemClickListener: OnItemClickListener<K>? = onItemClickListener
    private val mOnDragInitiatedListener: OnDragStartListener? = onDragInitiatedListener
    private val mHapticPerformer: Runnable = hapticPerformer

    override fun onDragStart(e: MotionEvent): Boolean {
        mHapticPerformer.run()

        if (!mSelectionTracker.isInSelectionMode()) {
            Log.d(TAG, "onDragStart NOT IN SELETION MODE")
            return false
        }
        // NOTE: "onDragStart" always be invoked if canDragDrop return true.
        // just need onDragStart in some case and not set details loopup
        if (mDetailsLookup == null) {
            Log.d(TAG, "onDragStart enter")
            return mOnDragInitiatedListener?.onDragStart(e) ?: false
        }

        val item = mDetailsLookup.getItemDetails(e) ?: return false
        // NOTE: slide selection have conflict with drag
        if (item.inDragRegion(e)) {
            if (DEBUG) {
                Log.d(TAG, "onDragStart: canDragDrop ${mSelectionTracker.canDragDrop()}")
            }

            val key = item.selectionKey ?: return false
            if (mSelectionTracker.canDragDrop() && mSelectionPredicate.canSetStateForKey(key, true)) {
                if (!mSelectionTracker.isItemSelected(key)) {
                    selectItem(item)
                }
            }

            return mOnDragInitiatedListener?.onDragStart(e) ?: false
        }
        return false
    }

    override fun onCancel(e: MotionEvent): Boolean {
        if (DEBUG) {
            Log.d(TAG, "onCancel")
        }
        val view = mDetailsLookup?.getItemView(e) ?: return false
        view.isPressed = false
        return false
    }

    override fun onUp(e: MotionEvent): Boolean {
        if (DEBUG) {
            Log.d(TAG, "onUp")
        }
        val view = mDetailsLookup?.getItemView(e) ?: return false
        view.isPressed = false
        return false
    }

    override fun onSingleTapUp(e: MotionEvent): Boolean {
        Log.d(TAG, "onSingleTapUp")
        val item = mDetailsLookup?.getItemDetails(e) ?: return false
        val view = mDetailsLookup.getItemView(e) ?: return false
        view.isPressed = false

        // Should really not be null at this point, but...
        if (DEBUG) {
            Log.d(TAG, "onSingleTapUp position: ${item.position}, key: ${item.selectionKey}")
        }

        if (mSelectionTracker.isInSelectionMode() && item.isClickArea(e)) {
            // do nothing when slide selection not finished
            if (mSelectionTracker.isRangeActive && item.inSelectionHotspot(e)) {
                Log.d(TAG, "onSingleTapUp current in slide selection ")
                return false
            }

            item.selectionKey?.let {
                view.playSoundEffect(SoundEffectConstants.CLICK)
                if (mSelectionTracker.isItemSelected(it)) {
                    Log.d(TAG, "onSingleTapUp deselectItem ")
                    mSelectionTracker.deselectItem(it)
                } else {
                    Log.d(TAG, "onSingleTapUp selectItem ")
                    selectItem(item)
                }
                Log.d(TAG, "onSingleTapUp Tap ")
                return true
            }
        }

        view.playSoundEffect(SoundEffectConstants.CLICK)
        return mOnItemClickListener?.onItemClick(item, e) ?: false
    }

    override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
        if (DEBUG) {
            Log.d(TAG, "onFling ")
        }

        if ((e1 == null) || (e2 == null)) {
            return super.onFling(e1, e2, velocityX, velocityY)
        }

        var view = mDetailsLookup?.getItemView(e1)
        view?.let {
            it.isPressed = false
        }

        view = mDetailsLookup?.getItemView(e2)
        view?.let {
            it.isPressed = false
        }
        return super.onFling(e1, e2, velocityX, velocityY)
    }

    override fun onShowPress(e: MotionEvent) {
        if (DEBUG) {
            Log.d(TAG, "onShowPress ")
        }
        super.onShowPress(e)
    }

    override fun onDoubleTapEvent(e: MotionEvent): Boolean {
        if (DEBUG) {
            Log.d(TAG, "onDoubleTapEvent ")
        }
        e?.let {
            val view = mDetailsLookup?.getItemView(it)
            view?.isPressed = false
        }
        return super.onDoubleTapEvent(e)
    }

    override fun onDown(e: MotionEvent): Boolean {
        e?.let {
            val view = mDetailsLookup?.getItemView(it)
            view?.isPressed = true
        }
        return super.onDown(e)
    }

    override fun onLongPress(e: MotionEvent) {
        Log.d(TAG, "onLongPress")
        val item = mDetailsLookup?.getItemDetails(e) ?: return
        val view = mDetailsLookup.getItemView(e) ?: return
        view.isPressed = false
        if (!item.canLongPressOrClick()) {
            Log.d(TAG, "onLongPress canLongPressSelect false")
            return
        }
        if (DEBUG) {
            Log.d(TAG, "onLongPress position: ${item.position}, key: ${item.selectionKey}")
        }
        // Note: only hanle LongPress to enter seletion mode.slide selection handle MOVE event
        // to switcher chech state
        val key = item.selectionKey ?: return
        if (!mSelectionTracker.isInSelectionMode()) {
            if (mSelectionPredicate.canSetStateForKey(key, true)) {
                mSelectionTracker.longPressIndex = item.position
                if (enterSelectionMode(item)) {
                    mHapticPerformer.run()
                }
            }
        } else {
            // handle the long press event and only hand selected state
            if (mSelectionPredicate.canSetStateForKey(key, true)) {
                if (mSelectionTracker.isItemSelected(key)) {
                    return
                } else {
                    selectItem(item)
                }
            }
        }
    }

    override fun onScroll(e1: MotionEvent?, e2: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
        if (DEBUG) {
            Log.d(TAG, "onScroll")
        }

        if ((e1 == null) || (e2 == null)) {
            return super.onScroll(e1, e2, distanceX, distanceY)
        }

        // NOTE: move too much and reach gap limit, GestureDetector will remove LONG TOUCH MESSAGE and onScroll be invoked
        //  here dimiss pressed state
        var view = mDetailsLookup?.getItemView(e1)
        view?.let {
            it.isPressed = false
        }

        view = mDetailsLookup?.getItemView(e2)
        view?.let {
            it.isPressed = false
        }

        return super.onScroll(e1, e2, distanceX, distanceY)
    }
}

/**
 * A MotionInputHandler that provides the high-level glue for mouse driven selection. This
 * class works with {@link RecyclerView}, {@link GestureRouter}, and {@link GestureSelectionHelper}
 * to implement the primary policies around mouse input.
 */
internal class MouseInputHandler<K>(
    selectionTracker: SelectionTracker<K>,
    detailsLookup: ItemDetailsLookup<K>?,
    selectionPredicate: SelectionPredicate<K>,
    onDragInitiatedListener: OnDragStartListener?,
    onItemClickListener: OnItemClickListener<K>?) : MotionInputHandler<K>(selectionTracker) {

    companion object {
        private const val TAG = "MouseInputHandler"
    }

    private val mDetailsLookup: ItemDetailsLookup<K>? = detailsLookup
    private val mSelectionPredicate: SelectionPredicate<K> = selectionPredicate
    private val mOnItemClickListener: OnItemClickListener<K>? = onItemClickListener
    private val mOnDragInitiatedListener: OnDragStartListener? = onDragInitiatedListener

    override fun onDragStart(e: MotionEvent): Boolean {
        if (!mSelectionTracker.isInSelectionMode()) {
            Log.d(TAG, "onDragStart NOT IN SELETION MODE")
            return false
        }
        // NOTE: "onDragStart" always be invoked if canDragDrop return true.
        // just need onDragStart in some case and not set details loopup
        if (mDetailsLookup == null) {
            Log.d(TAG, "onDragStart enter")
            return mOnDragInitiatedListener?.onDragStart(e) ?: false
        }

        val item = mDetailsLookup.getItemDetails(e) ?: return false
        // NOTE: slide selection have conflict with drag
        if (item.inDragRegion(e)) {
            if (DEBUG) {
                Log.d(TAG, "onDragStart: canDragDrop ${mSelectionTracker.canDragDrop()}")
            }

            val key = item.selectionKey ?: return false
            if (mSelectionTracker.canDragDrop() && mSelectionPredicate.canSetStateForKey(key, true)) {
                if (!mSelectionTracker.isItemSelected(key)) {
                    selectItem(item)
                }
            }

            return mOnDragInitiatedListener?.onDragStart(e) ?: false
        }
        return false
    }

    override fun onUp(e: MotionEvent): Boolean {
        if (DEBUG) {
            Log.d(TAG, "onUp")
        }
        val view = mDetailsLookup?.getItemView(e) ?: return false
        view.isPressed = false
        return false
    }

    override fun onSingleTapUp(e: MotionEvent): Boolean {
        Log.d(TAG, "onSingleTapUp")
        val item = mDetailsLookup?.getItemDetails(e) ?: return false
        val view = mDetailsLookup.getItemView(e) ?: return false
        view.isPressed = false

        // Should really not be null at this point, but...
        if (DEBUG) {
            Log.d(TAG, "onSingleTapUp position: ${item.position}, key: ${item.selectionKey}")
        }

        if (mSelectionTracker.isInSelectionMode() && item.isClickArea(e)) {
            // do nothing when slide selection not finished
            if (mSelectionTracker.isRangeActive && item.inSelectionHotspot(e)) {
                Log.d(TAG, "onSingleTapUp current in slide selection ")
                return false
            }

            item.selectionKey?.let {
                view.playSoundEffect(SoundEffectConstants.CLICK)
                if (mSelectionTracker.isItemSelected(it)) {
                    Log.d(TAG, "onSingleTapUp deselectItem ")
                    mSelectionTracker.deselectItem(it)
                } else {
                    Log.d(TAG, "onSingleTapUp selectItem ")
                    selectItem(item)
                }
                Log.d(TAG, "onSingleTapUp Tap ")
                return true
            }
        }

        view.playSoundEffect(SoundEffectConstants.CLICK)
        return mOnItemClickListener?.onItemClick(item, e) ?: false
    }

    override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
        if (DEBUG) {
            Log.d(TAG, "onFling ")
        }

        if ((e1 == null) || (e2 == null)) {
            return super.onFling(e1, e2, velocityX, velocityY)
        }

        var view = mDetailsLookup?.getItemView(e1)
        view?.let {
            it.isPressed = false
        }

        view = mDetailsLookup?.getItemView(e2)
        view?.let {
            it.isPressed = false
        }
        return super.onFling(e1, e2, velocityX, velocityY)
    }

    override fun onShowPress(e: MotionEvent) {
        if (DEBUG) {
            Log.d(TAG, "onShowPress ")
        }
        super.onShowPress(e)
    }

    override fun onDoubleTapEvent(e: MotionEvent): Boolean {
        if (DEBUG) {
            Log.d(TAG, "onDoubleTapEvent ")
        }
        e?.let {
            val view = mDetailsLookup?.getItemView(it)
            view?.isPressed = false
        }
        return super.onDoubleTapEvent(e)
    }

    override fun onDown(e: MotionEvent): Boolean {
        e?.let {
            val view = mDetailsLookup?.getItemView(it)
            view?.isPressed = true
        }
        return super.onDown(e)
    }

    override fun onLongPress(e: MotionEvent) {
        Log.d(TAG, "onLongPress")
        val item = mDetailsLookup?.getItemDetails(e) ?: return
        val view = mDetailsLookup.getItemView(e) ?: return
        view.isPressed = false
        if (!item.canLongPressOrClick()) {
            Log.d(TAG, "onLongPress canLongPressSelect false")
            return
        }
        if (DEBUG) {
            Log.d(TAG, "onLongPress position: ${item.position}, key: ${item.selectionKey}")
        }
        // Note: only hanle LongPress to enter seletion mode.slide selection handle MOVE event
        // to switcher chech state
        val key = item.selectionKey ?: return
        if (!mSelectionTracker.isInSelectionMode()) {
            if (mSelectionPredicate.canSetStateForKey(key, true)) {
                mSelectionTracker.longPressIndex = item.position
                enterSelectionMode(item)
            }
        } else {
            // handle the long press event and only hand selected state
            if (mSelectionTracker.canDragDrop() && mSelectionPredicate.canSetStateForKey(key, true)) {
                if (mSelectionTracker.isItemSelected(key)) {
                    return
                } else {
                    selectItem(item)
                }
            }
        }
    }

    override fun onScroll(e1: MotionEvent?, e2: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
        if (DEBUG) {
            Log.d(TAG, "onScroll")
        }

        if ((e1 == null) || (e2 == null)) {
            return super.onScroll(e1, e2, distanceX, distanceY)
        }

        // NOTE: move too long distance and reach the gap limit,
        // GestureDetector will remove LONG TOUCH MESSAGE and onScroll be invoked
        //  here dimiss pressed state
        var view = mDetailsLookup?.getItemView(e1)
        view?.let {
            it.isPressed = false
        }

        view = mDetailsLookup?.getItemView(e2)
        view?.let {
            it.isPressed = false
        }

        return super.onScroll(e1, e2, distanceX, distanceY)
    }
}
