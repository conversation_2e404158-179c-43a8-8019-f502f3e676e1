/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScreenDisplayFlagTest.kt
 * Description:
 * The test cases for ScreenDisplayFlag
 *
 * Version: 1.0
 * Date: 2024-06-06
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<EMAIL>    2024-06-06   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.value

import org.junit.Assert
import org.junit.Test

class ScreenDisplayFlagTest : Assert() {

    @Test
    fun `should to target flag when fromAlias with single alias`() {
        assertEquals(ScreenDisplayFlag.NORMAL, ScreenDisplayFlag("normal").flag)
        assertEquals(ScreenDisplayFlag.UNFOLD, ScreenDisplayFlag("unfold").flag)
        assertEquals(ScreenDisplayFlag.PAD, ScreenDisplayFlag("pad").flag)
        assertEquals(ScreenDisplayFlag.FLIP, ScreenDisplayFlag("flip").flag)
    }

    @Test
    fun `should to multi flags when fromAlias with multi alias`() {
        assertEquals(
            ScreenDisplayFlag.NORMAL or ScreenDisplayFlag.UNFOLD or ScreenDisplayFlag.PAD,
            ScreenDisplayFlag("normal|unfold|pad").flag
        )
    }

    @Test
    fun `should to target alias when toAlias with single flag`() {
        assertEquals("normal", ScreenDisplayFlag(ScreenDisplayFlag.NORMAL).toAlias())
        assertEquals("unfold", ScreenDisplayFlag(ScreenDisplayFlag.UNFOLD).toAlias())
        assertEquals("pad", ScreenDisplayFlag(ScreenDisplayFlag.PAD).toAlias())
        assertEquals("flip", ScreenDisplayFlag(ScreenDisplayFlag.FLIP).toAlias())
    }

    @Test
    fun `should to multi alias when toAlias with multi flags`() {
        assertEquals(
            "normal|unfold|pad|flip",
            ScreenDisplayFlag(
                ScreenDisplayFlag.NORMAL or ScreenDisplayFlag.UNFOLD
                        or ScreenDisplayFlag.PAD or ScreenDisplayFlag.FLIP
            ).toAlias()
        )
    }
}