/*********************************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDIForFileBrowser
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/05 16:29
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/06/05       1.0      create
 ***********************************************************************/
package com.oplus.filebrowser.di

import com.oplus.filebrowser.FileBrowserApi
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import org.koin.dsl.module

object AutoDIForFileBrowser {

    val fileBrowserModule = module {
        single<IFileBrowser>(createdAtStart = true) {
            FileBrowserApi
        }
    }
}