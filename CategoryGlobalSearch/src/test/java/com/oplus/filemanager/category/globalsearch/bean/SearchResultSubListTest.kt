package com.oplus.filemanager.category.globalsearch.bean

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.SearchDMPFileWrapper
import com.filemanager.common.base.SearchFileWrapper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchActivity
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.interfaze.main.IMain
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.clearAllMocks
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.After
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * SearchResultSubList类的单元测试类
 * 用于测试SearchResultSubList的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class SearchResultSubListTest {

    // 测试类中使用的成员变量
    private lateinit var searchResultSubList: SearchResultSubList
    private lateinit var mockIMain: IMain
    private lateinit var mockDocumentExtensionType: IDocumentExtensionType

    /**
     * 在每个测试方法执行前的初始化方法
     * 1. 清除所有mock对象
     * 2. 初始化SearchResultSubList实例
     * 3. 创建IMain和IDocumentExtensionType的mock对象
     */
    @Before
    fun setUp() {
        clearAllMocks()
        searchResultSubList = SearchResultSubList()
        mockIMain = mockk(relaxed = true)
        mockDocumentExtensionType = mockk(relaxed = true)
    }

    /**
     * 在每个测试方法执行后的清理方法
     * 清除所有mock对象
     */
    @After
    fun tearDown() {
        clearAllMocks()
    }

    /**
     * 测试getSubListByCategoryType方法
     * 验证根据分类类型获取对应文件列表的功能
     */
    @Test
    fun testGetSubListByCategoryType() {
        // Given - 准备测试数据：创建一个图片文件并添加到imageFiles列表
        val imageFile = BaseFileBean().apply { mLocalType = 1 }
        searchResultSubList.imageFiles.add(imageFile)

        // When - 执行测试：调用getSubListByCategoryType方法获取图片分类的文件列表
        val result = searchResultSubList.getSubListByCategoryType(CategoryHelper.CATEGORY_IMAGE)

        // Then - 验证结果：确认返回的列表包含我们添加的图片文件
        assertEquals(1, result.size)
        assertEquals(imageFile, result[0])
    }

    /**
     * 测试sortAndMergeSubItemList方法
     * 验证文件排序和合并功能
     */
    @Test
    fun testSortAndMergeSubItemList() {
        // Given - 准备测试数据：创建两个文件夹文件，设置不同的名称用于测试排序
        val file1 = BaseFileBean().apply { 
            mLocalType = 0
            mDateModified = 1000
            mDisplayName = "B"
        }
        val file2 = BaseFileBean().apply {
            mLocalType = 0
            mDateModified = 1000
            mDisplayName = "A"
        }
        searchResultSubList.folderFiles.addAll(listOf(file1, file2))

        // When - 执行测试：调用sortAndMergeSubItemList方法进行排序和合并
        val result = searchResultSubList.sortAndMergeSubItemList()

        // Then - 验证结果：确认文件已按名称排序
        assertEquals(2, result.size)
        assertEquals("A", result[0].mDisplayName)  // 验证排序后A在前
        assertEquals("B", result[1].mDisplayName)  // 验证排序后B在后
    }

    /**
     * 测试mergeNewResult方法在输入为null时的处理
     * 验证当传入null时不会修改现有数据
     */
    @Test
    fun testMergeNewResultWithNullInput() {
        // When - 执行测试：传入null调用mergeNewResult方法
        searchResultSubList.mergeNewResult(null)

        // Then - 验证结果：确认allTableFiles列表保持为空
        assertTrue(searchResultSubList.allTableFiles.isEmpty())
    }

    /**
     * 测试processFileLabelIfHad方法
     * 验证文件标签处理功能
     */
    @Test
    fun testProcessFileLabelIfHad() {
        // Given - 准备测试数据：添加一个文件到allTableFiles，并设置mock行为
        val file = BaseFileBean()
        searchResultSubList.allTableFiles.add(file)
        every { mockIMain.findFileLabelIfHad(any()) } returns Unit

        // When - 执行测试：调用processFileLabelIfHad方法
        searchResultSubList.processFileLabelIfHad(mockIMain)

        // Then - 验证结果：确认findFileLabelIfHad方法被正确调用
        verify(exactly = 1) { mockIMain.findFileLabelIfHad(searchResultSubList.allTableFiles) }
    }
}