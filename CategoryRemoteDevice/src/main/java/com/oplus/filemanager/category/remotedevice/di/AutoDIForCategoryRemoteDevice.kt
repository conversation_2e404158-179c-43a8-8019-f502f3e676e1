/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AutoDIForCategoryRemoteDevice
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.di

import com.oplus.filemanager.category.remotedevice.CategoryRemoteDeviceApi
import com.oplus.filemanager.interfaze.categoryremotedevice.ICategoryRemoteDeviceApi
import org.koin.dsl.module

object AutoDIForCategoryRemoteDevice {

    val remoteDeviceApi = module {
        single<ICategoryRemoteDeviceApi>(createdAtStart = true) {
            CategoryRemoteDeviceApi
        }
    }
}