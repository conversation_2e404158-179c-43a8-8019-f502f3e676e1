/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:/DocumentViewModel.kt
 * * Description:
 * * Version:
 * * Date :2020/6/9
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>          <data>        <version>     <desc>
 * *  ZeJiang.Duan      2020/6/9        1.0           Create
 ****************************************************************/
package com.oplus.filemanager.category.document.ui

import android.net.Uri
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.base.loader.UriLoadResult
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.interfaces.LoadingLoaderListener
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.ad.AdvertManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class DocumentViewModel : SelectionViewModel<MediaFileWrapper, BaseUiModel<MediaFileWrapper>>() {
    companion object {
        private const val TAG = "DocumentViewModel"

        @JvmField
        val DEF_ALL_EXT_ARRAY = java.util.ArrayList<String?>().apply {
            add(".doc")
            add(".docx")
            add(".xls")
            add(".xlsx")
            add(".ppt")
            add(".pptx")
            add(".pdf")
            add(".ofd")
            add(".txt")
        }
    }

    val mModeState = BaseStateModel(MutableLiveData<Int>(KtConstants.LIST_NORMAL_MODE))
    var mExtension: String? = null
    var mTabPosition: Int = 0
    var mUri: Uri? = null
    var mSql: String? = null
    var mDocArray: ArrayList<String>? = null
    var mNeedScroll = false
    var mScanModeValue: Int = KtConstants.SCAN_MODE_LIST
    val mPositionModel = MutableLiveData<Int>()
    var isLastOpenTimeOrder = false
    private val mDocumentLoaderCallBack = DocumentLoaderCallBack(this)

    override fun loadData() {
        mDocumentLoaderCallBack.getLoader()?.forceLoad()
    }

    fun initLoader(
        loaderController: LoaderController,
        tabPosition: Int,
        extension: String?,
        uri: Uri?,
        sql: String?,
        docArray: ArrayList<String>?,
        isLastOpenTime: Boolean
    ) {
        mTabPosition = tabPosition
        mExtension = extension
        mUri = uri
        mSql = sql
        mDocArray = docArray
        isLastOpenTimeOrder = isLastOpenTime
        if (mDocumentLoaderCallBack.getLoader() == null) {
            loaderController.initLoader(tabPosition, mDocumentLoaderCallBack)
        } else {
            loadData()
        }
    }

    fun sortReload() {
        mDocumentLoaderCallBack.getLoader()?.setSort(-1)
        loadData()
    }

    fun clickToolbarSelectAll() {
        if (getRealFileSize() == uiState.value?.selectedList?.size) {
            uiState.value?.selectedList?.clear()
        } else {
            uiState.value?.selectedList?.clear()
            uiState.value?.fileList?.let {
                for (baseFileBean in it) {
                    baseFileBean.id?.let { it1 -> uiState.value?.selectedList?.add(it1) }
                }
            }
        }
        uiState.value = uiState.value
    }

    fun pressBack(): Boolean {
        mModeState.let {
            return when (it.listModel.value) {
                KtConstants.LIST_SELECTED_MODE -> {
                    changeListMode(KtConstants.LIST_NORMAL_MODE)
                    true
                }
                else -> {
                    false
                }
            }

        }
    }

    class DocumentLoaderCallBack(viewModel: DocumentViewModel) :
        LoadingLoaderListener<DocumentViewModel, DocumentLoader, UriLoadResult<Int, MediaFileWrapper>>(
            viewModel,
            viewModel.dataLoadState
        ) {

        override fun onCreateLoader(viewModel: DocumentViewModel?): DocumentLoader? {
            return if (viewModel != null) {
                DocumentLoader(
                    appContext, viewModel.mUri, viewModel.mSql, viewModel.mTabPosition,
                    viewModel.mExtension, viewModel.mDocArray, viewModel.isLastOpenTimeOrder
                )
            } else null
        }

        override fun onLoadComplete(viewModel: DocumentViewModel?, result: UriLoadResult<Int, MediaFileWrapper>?) {
            Log.d(TAG, "DocumentLoaderCallBack onLoadComplete size ${result?.resultList?.size}")
            val resultList = result?.resultList?.filter {
                it.mData?.let { path ->
                    HiddenFileHelper.isDisplayFile(path)
                } ?: true
            }
            resultList?.let {
                if (viewModel != null) {
                    viewModel.mModeState.initState = true
                    viewModel.launch {
                        val selectedList = ArrayList<Int>()
                        if ((viewModel.uiState.value?.selectedList?.size ?: 0) > 0) {
                            withContext(Dispatchers.IO) {
                                for (selectedFile in viewModel.uiState.value!!.selectedList) {
                                    if (result.resultMap.containsKey(selectedFile)) {
                                        selectedList.add(selectedFile)
                                    }
                                }
                            }
                        }
                        if (it.isEmpty() && (viewModel.mModeState.listModel.value == KtConstants.LIST_SELECTED_MODE)) {
                            Log.d(TAG, "onLoadComplete mResultList is empty change to normal mode")
                            viewModel.mModeState.listModel.value = KtConstants.LIST_NORMAL_MODE
                        }
                        viewModel.uiState.postValue(
                            BaseUiModel(
                                it, viewModel.mModeState, selectedList,
                                result.resultMap
                            )
                        )
                        if (viewModel.mNeedScroll) {
                            viewModel.mPositionModel.value = 0
                            viewModel.mNeedScroll = false
                        }
                    }
                } else {
                    Log.d(TAG, "onLoadComplete viewModel is null")
                }
            }
        }
    }

    override fun getRealFileSize(): Int {
        var adViewCount = 0
        if (uiState.value != null && uiState.value?.fileList != null) {
            adViewCount = AdvertManager.getAdViewCount(uiState.value?.fileList as ArrayList<MediaFileWrapper>)
        }
        return (uiState.value?.fileList?.size?.minus(adViewCount)) ?: 0
    }

    override fun getRecyclerViewScanMode(): com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE {
        return if (mScanModeValue == KtConstants.SCAN_MODE_LIST) {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.LIST
        } else {
            com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.GRID
        }
    }
}