/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AccessibilitySettingsTest.kt
 * Description:
 *     The test cases for AccessibilitySettings
 *
 * Version: 1.0
 * Date: 2024-05-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-05-29   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.accessibility

import android.content.ComponentName
import android.content.Context
import android.provider.Settings
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class AccessibilitySettingsTest : Assert() {

    private companion object {
        private const val TEST_SERVICE_1 = "com.test.pkg1/com.test.pkg1.AS1"
        private const val TEST_SERVICE_2 = "com.test.pkg2/com.test.pkg1.AS2"
    }

    private lateinit var context: Context
    private lateinit var testService1: ComponentName
    private lateinit var testService2: ComponentName

    @Before
    fun setUp() {
        mockkStatic(ComponentName::class)
        context = mockk {
            every { contentResolver } returns mockk()
        }
        testService1 = mockk {
            every { flattenToString() } returns TEST_SERVICE_1
        }
        every { ComponentName.unflattenFromString(TEST_SERVICE_1) } returns testService1
        testService2 = mockk {
            every { flattenToString() } returns TEST_SERVICE_2
        }
        every { ComponentName.unflattenFromString(TEST_SERVICE_2) } returns testService2
    }

    @After
    fun tearDown() {
        unmockkStatic(ComponentName::class)
    }

    @Test
    fun `should get services when getEnabledServicesFromSettings if has multi enabled services`() {
        // SetUp
        mockkStatic(Settings.Secure::class)

        // Given
        every {
            Settings.Secure.getInt(any(), Settings.Secure.ACCESSIBILITY_ENABLED, any())
        } returns 1
        every {
            Settings.Secure.getString(any(), Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES)
        } returns "$TEST_SERVICE_1:$TEST_SERVICE_2"

        // When
        val results = AccessibilitySettings.getEnabledServicesFromSettings(context)

        // Then
        assertArrayEquals(arrayOf(testService1, testService2), results.toTypedArray())

        // TearDown
        unmockkStatic(Settings.Secure::class)
    }

    @Test
    fun `should get service when getEnabledServicesFromSettings if has single enabled service`() {
        // SetUp
        mockkStatic(Settings.Secure::class)

        // Given
        every {
            Settings.Secure.getInt(any(), Settings.Secure.ACCESSIBILITY_ENABLED, any())
        } returns 1
        every {
            Settings.Secure.getString(any(), Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES)
        } returns TEST_SERVICE_1

        // When
        val results = AccessibilitySettings.getEnabledServicesFromSettings(context)

        // Then
        assertArrayEquals(arrayOf(testService1), results.toTypedArray())

        // TearDown
        unmockkStatic(Settings.Secure::class)
    }

    @Test
    fun `should get empty when getEnabledServicesFromSettings if no enabled services`() {
        // SetUp
        mockkStatic(Settings.Secure::class)

        // Given
        every {
            Settings.Secure.getInt(any(), Settings.Secure.ACCESSIBILITY_ENABLED, any())
        } returns 0
        every {
            Settings.Secure.getString(any(), Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES)
        } returns null

        // When
        val results = AccessibilitySettings.getEnabledServicesFromSettings(context)

        // Then
        assertTrue(results.isEmpty())

        // TearDown
        unmockkStatic(Settings.Secure::class)
    }

    @Test
    fun `should add enabled when setAccessibilityServiceState if has other enabled service`() {
        // SetUp
        mockkStatic(SettingsCompat.Secure::class)

        // Given
        justRun { SettingsCompat.Secure.putString(any(), any(), any()) }
        val enabledServices = mutableSetOf(testService1)

        // When
        AccessibilitySettings
            .setAccessibilityServiceState(context, testService2, true, enabledServices)

        // Then
        verify {
            SettingsCompat.Secure.putString(
                any(),
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES,
                "$TEST_SERVICE_1:$TEST_SERVICE_2"
            )
        }

        // TearDown
        unmockkStatic(SettingsCompat.Secure::class)
    }

    @Test
    fun `should add enabled when setAccessibilityServiceState if no enabled service`() {
        // SetUp
        mockkStatic(SettingsCompat.Secure::class)

        // Given
        justRun { SettingsCompat.Secure.putString(any(), any(), any()) }
        val enabledServices = mutableSetOf<ComponentName>()

        // When
        AccessibilitySettings
            .setAccessibilityServiceState(context, testService1, true, enabledServices)

        // Then
        verify {
            SettingsCompat.Secure.putString(
                any(),
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES,
                TEST_SERVICE_1
            )
        }

        // TearDown
        unmockkStatic(SettingsCompat.Secure::class)
    }

    @Test
    fun `should remove enabled when setAccessibilityServiceState if has other enabled service`() {
        // SetUp
        mockkStatic(SettingsCompat.Secure::class)

        // Given
        justRun { SettingsCompat.Secure.putString(any(), any(), any()) }
        val enabledServices = mutableSetOf(testService1, testService2)

        // When
        AccessibilitySettings
            .setAccessibilityServiceState(context, testService2, false, enabledServices)

        // Then
        verify {
            SettingsCompat.Secure.putString(
                any(),
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES,
                TEST_SERVICE_1
            )
        }

        // TearDown
        unmockkStatic(SettingsCompat.Secure::class)
    }

    @Test
    fun `should remove enabled when setAccessibilityServiceState if no other enabled service`() {
        // SetUp
        mockkStatic(SettingsCompat.Secure::class)

        // Given
        justRun { SettingsCompat.Secure.putString(any(), any(), any()) }
        val enabledServices = mutableSetOf(testService1)

        // When
        AccessibilitySettings
            .setAccessibilityServiceState(context, testService1, false, enabledServices)

        // Then
        verify {
            SettingsCompat.Secure.putString(
                any(),
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES,
                ""
            )
        }

        // TearDown
        unmockkStatic(SettingsCompat.Secure::class)
    }

    @Test
    fun `should no change when setAccessibilityServiceState if enable already enabled service`() {
        // SetUp
        mockkStatic(SettingsCompat.Secure::class)

        // Given
        justRun { SettingsCompat.Secure.putString(any(), any(), any()) }
        val enabledServices = mutableSetOf(testService1)

        // When
        AccessibilitySettings
            .setAccessibilityServiceState(context, testService1, true, enabledServices)

        // Then
        verify(inverse = true) { SettingsCompat.Secure.putString(any(), any(), any()) }

        // TearDown
        unmockkStatic(SettingsCompat.Secure::class)
    }

    @Test
    fun `should no change when setAccessibilityServiceState if disable not enabled service`() {
        // SetUp
        mockkStatic(SettingsCompat.Secure::class)

        // Given
        justRun { SettingsCompat.Secure.putString(any(), any(), any()) }
        val enabledServices = mutableSetOf(testService1)

        // When
        AccessibilitySettings
            .setAccessibilityServiceState(context, testService2, false, enabledServices)

        // Then
        verify(inverse = true) { SettingsCompat.Secure.putString(any(), any(), any()) }

        // TearDown
        unmockkStatic(SettingsCompat.Secure::class)
    }
}