package com.oplus.filemanager.preview.audio

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.os.Looper
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.KtThumbnailHelper
import com.oplus.filemanager.preview.core.IPreviewContainerManager
import com.oplus.filemanager.preview.core.IWidgetDefFileIntroduce
import com.oplus.filemanager.preview.utils.MediaPlayerHelper
import com.oplus.filemanager.preview.utils.getFileUri
import com.oplus.filemanager.preview.widget.PreviewAudioCard
import io.mockk.*
import junit.framework.Assert.assertEquals
import junit.framework.Assert.assertNull
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.io.File

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class AudioFilePreviewImplTest {

    private lateinit var context: Context
    private lateinit var lifecycleOwner: LifecycleOwner
    private lateinit var previewModel: AudioPreviewViewModel
    private lateinit var audioFilePreviewImpl: AudioFilePreviewImpl
    private lateinit var audioContainer: PreviewAudioCard
    private lateinit var containerManager: IPreviewContainerManager
    private lateinit var defaultContainer: IWidgetDefFileIntroduce
    
    // Mocks for dependencies
    private val mockAudioPlayer = mockk<IPreviewAudioPlayer>(relaxed = true)
    private val mockLiveDataFile = MutableLiveData<BaseFileBean?>()
    private val mockLiveDataMetadata = MutableLiveData<AudioFileMetadata?>()
    
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true)
        lifecycleOwner = mockk(relaxed = true)
        every { lifecycleOwner.lifecycle.currentState } returns Lifecycle.State.RESUMED
        
        previewModel = mockk(relaxed = true)
        
        // Setup mocks for preview model
        every { previewModel.obtainAudioPlayer(any()) } returns mockAudioPlayer
        every { previewModel.loadPreviewFile(any(), any()) } answers {
            val observer = arg<Observer<BaseFileBean?>>(1)
            mockLiveDataFile.observeForever(observer)
        }
        every { previewModel.loadAudioMetadata(any(), any()) } answers {
            val observer = arg<Observer<AudioFileMetadata?>>(1)
            mockLiveDataMetadata.observeForever(observer)
        }
        
        audioContainer = mockk(relaxed = true)
        containerManager = mockk(relaxed = true)
        defaultContainer = mockk(relaxed = true)
        every { containerManager.defaultContainer } returns defaultContainer
        every { containerManager.startLoadingTime } returns System.currentTimeMillis()
        
        audioFilePreviewImpl = AudioFilePreviewImpl(context, lifecycleOwner, previewModel)
        
        // Reset live data values
        mockLiveDataFile.value = null
        mockLiveDataMetadata.value = null
    }
    
    @After
    fun tearDown() {
        clearAllMocks()
        mockLiveDataFile.removeObservers(lifecycleOwner)
        mockLiveDataMetadata.removeObservers(lifecycleOwner)
    }
    
    @Test
    fun `release should clean up resources`() {
        // Given
        val lifecycle = mockk<androidx.lifecycle.Lifecycle>(relaxed = true)
        every { lifecycleOwner.lifecycle } returns lifecycle
        
        audioFilePreviewImpl.attachToContainer(audioContainer, containerManager)
        
        // When
        audioFilePreviewImpl.release()
        
        // Then
        verify(exactly = 1) { previewModel.releaseLoader(any()) }
        verify(exactly = 1) { previewModel.releaseAudioMetadataLoader(any()) }
        verify(exactly = 1) { audioContainer.release() }
        verify(exactly = 1) { lifecycle.removeObserver(audioFilePreviewImpl) }
    }
    
    @Test
    fun `playAudioByPlayer with null file should log error and return`() {
        // Given
        mockkStatic(com.filemanager.common.utils.Log::class)
        every { com.filemanager.common.utils.Log.e(any<String>(), any<String>()) } just runs
        
        // Attach first to ensure proper initialization
        audioFilePreviewImpl.attachToContainer(audioContainer, containerManager)
        
        // When
        mockLiveDataFile.value = null
        
        // Then
        verify(atLeast = 1) { com.filemanager.common.utils.Log.e("AudioFilePreviewImpl", "playAudioByPlayer: ERROR! No video file to play") }
    }
    
    @Test
    fun `updateAudioCard with metadata should update UI`() {
        // Given
        val coverImage = mockk<Bitmap>()
        val metadata = AudioFileMetadata(artist = "Artist Name", coverImage = coverImage, fileName = "test.mp3")
        
        // Create fresh mocks to avoid previous interactions
        val freshAudioContainer = mockk<PreviewAudioCard>(relaxed = true)
        val freshContainerManager = mockk<IPreviewContainerManager>(relaxed = true)
        val freshDefaultContainer = mockk<IWidgetDefFileIntroduce>(relaxed = true)
        every { freshContainerManager.defaultContainer } returns freshDefaultContainer
        
        val freshAudioFilePreviewImpl = AudioFilePreviewImpl(context, lifecycleOwner, previewModel)
        freshAudioFilePreviewImpl.attachToContainer(freshAudioContainer, freshContainerManager)
        
        // When
        mockLiveDataMetadata.value = metadata
        
        // Then
        verify(exactly = 1) { freshAudioContainer.setAudioArtist("Artist Name") }
        verify(exactly = 1) { freshAudioContainer.setAudioCover(coverImage) }
    }
    
    @Test
    fun `updateAudioCard with null metadata should not update UI`() {
        // Given
        // Create fresh mocks to avoid previous interactions
        val freshAudioContainer = mockk<PreviewAudioCard>(relaxed = true)
        val freshContainerManager = mockk<IPreviewContainerManager>(relaxed = true)
        val freshDefaultContainer = mockk<IWidgetDefFileIntroduce>(relaxed = true)
        every { freshContainerManager.defaultContainer } returns freshDefaultContainer
        
        val freshAudioFilePreviewImpl = AudioFilePreviewImpl(context, lifecycleOwner, previewModel)
        freshAudioFilePreviewImpl.attachToContainer(freshAudioContainer, freshContainerManager)
        
        // When
        mockLiveDataMetadata.value = null
        
        // Then
        verify(atLeast = 1) { freshAudioContainer.setAudioArtist(null) }
        verify(atLeast = 1) { freshAudioContainer.setAudioCover(null) }
    }
    
    @Test
    fun `onPrepared should update UI state`() {
        // Given
        mockkStatic(com.filemanager.common.utils.Log::class)
        every { com.filemanager.common.utils.Log.d(any<String>(), any<String>()) } just runs
        audioFilePreviewImpl.attachToContainer(audioContainer, containerManager)
        
        // When
        audioFilePreviewImpl.onPrepared(true)
        
        // Then
        verify(exactly = 1) { audioContainer.setAudioPlayState(true) }
        verify(exactly = 1) { com.filemanager.common.utils.Log.d("AudioFilePreviewImpl", "onPrepared: isPlayStarted=true") }
    }
    
    @Test
    fun `onPlayStateChanged should update UI`() {
        // Given
        mockkStatic(com.filemanager.common.utils.Log::class)
        every { com.filemanager.common.utils.Log.d(any<String>(), any<String>()) } just runs
        audioFilePreviewImpl.attachToContainer(audioContainer, containerManager)
        
        // When
        audioFilePreviewImpl.onPlayStateChanged(false)
        
        // Then
        verify(exactly = 1) { audioContainer.setAudioPlayState(false) }
        verify(exactly = 1) { com.filemanager.common.utils.Log.d("AudioFilePreviewImpl", "onPlayStatChanged: isPlaying=false") }
    }
    
    @Test
    fun `onProgressUpdate should update progress bar`() {
        // Given
        audioFilePreviewImpl.attachToContainer(audioContainer, containerManager)
        
        // When
        audioFilePreviewImpl.onProgressUpdate(5000L, 10000L)
        
        // Then
        verify(exactly = 1) { audioContainer.setDuration(10000L) }
        verify(exactly = 1) { audioContainer.setProgress(5000L) }
    }
    
    @Test
    fun `onSeekPlayProgress should call player seekTo`() {
        // When
        audioFilePreviewImpl.onSeekPlayProgress(3000L, true)
        
        // Then
        verify(exactly = 1) { mockAudioPlayer.seekTo(3000L, true) }
    }
    
    @Test
    fun `onClickPlayButton with pause request should pause player`() {
        // When
        audioFilePreviewImpl.onClickPlayButton(true)
        
        // Then
        verify(exactly = 1) { mockAudioPlayer.pause() }
    }
    
    @Test
    fun `onClickPlayButton with resume request should resume player`() {
        // When
        audioFilePreviewImpl.onClickPlayButton(false)
        
        // Then
        verify(exactly = 1) { mockAudioPlayer.resume() }
    }
    
    @Test
    fun `onError should handle error properly`() {
        // Given
        mockkStatic(com.filemanager.common.utils.Log::class)
        every { com.filemanager.common.utils.Log.e(any<String>(), any<String>()) } just runs
        audioFilePreviewImpl.attachToContainer(audioContainer, containerManager)
        
        // When
        audioFilePreviewImpl.onError(1, 2, "extra info")
        
        // Then
        verify(exactly = 1) { com.filemanager.common.utils.Log.e("AudioFilePreviewImpl", "onError: type=1, code=2, extra=extra info") }
        verify(exactly = 1) { previewModel.releaseAudioPlayer() }
        verify(exactly = 1) { audioContainer.release() }
    }
    
    @Test
    fun `onResume should resume playback if was playing`() {
        // Given
        mockkStatic(com.filemanager.common.utils.Log::class)
        every { com.filemanager.common.utils.Log.d(any<String>(), any<String>()) } just runs
        every { mockAudioPlayer.isPlaying() } returns true
        audioFilePreviewImpl.onPause(lifecycleOwner) // Set isPlayingWhenUiOnPause to true
        audioFilePreviewImpl.attachToContainer(audioContainer, containerManager)
        
        // When
        audioFilePreviewImpl.onResume(lifecycleOwner)
        
        // Then
        verify(exactly = 1) { mockAudioPlayer.resume() }
    }
    
    @Test
    fun `onPause should pause playback and save state`() {
        // Given
        mockkStatic(com.filemanager.common.utils.Log::class)
        every { com.filemanager.common.utils.Log.d(any<String>(), any<String>()) } just runs
        every { mockAudioPlayer.isPlaying() } returns true
        audioFilePreviewImpl.attachToContainer(audioContainer, containerManager)
        
        // When
        audioFilePreviewImpl.onPause(lifecycleOwner)
        
        // Then
        verify(exactly = 1) { mockAudioPlayer.pause() }
    }
    
    @Test
    fun `onUpdateUIWhenConfigChange should delegate to container manager`() {
        // Given
        val configList = mutableListOf<IUIConfig>()
        
        // Ensure containerManager is attached
        audioFilePreviewImpl.attachToContainer(audioContainer, containerManager)
        
        // When
        audioFilePreviewImpl.onUpdateUIWhenConfigChange(configList)
        
        // Then
        verify(exactly = 1) { containerManager.onUpdateUIWhenConfigChange(configList) }
    }
    
    private inline fun <reified T> Any.getPrivateProperty(name: String): T? {
        val property = this::class.java.getDeclaredField(name)
        property.isAccessible = true
        return property.get(this) as? T
    }
    
    private fun Any.invokePrivateMethod(name: String, vararg args: Any?) {
        val method = this::class.java.declaredMethods.find { it.name == name }
        method?.isAccessible = true
        method?.invoke(this, *args)
    }
}