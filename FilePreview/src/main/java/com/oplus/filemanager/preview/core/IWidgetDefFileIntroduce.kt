/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - IWidgetDefFileIntroduce.kt
 * Description:
 *     The base interface about default icon preview ui
 *
 * Version: 1.0
 * Date: 2024-10-25
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-10-25   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.core

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.appcompat.widget.AppCompatImageView

interface IWidgetDefFileIntroduce {
    val fileIconView: AppCompatImageView
    fun setFileIcon(@DrawableRes iconRes: Int)
    fun setFileIcon(drawable: Drawable?)
    fun setFileIcon(bitmap: Bitmap?)
    fun setFileName(@StringRes nameRes: Int)
    fun setFileName(name: String?)
    fun setFileNameVisible(visible: Boolean)
    fun setFileMessage(@StringRes msgRes: Int)
    fun setFileMessage(msg: String?)
    fun setFilePathVisible(visible: Boolean) {
        //do nothing
    }
}