plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace 'com.oplus.filemanager.dmp'
}


dependencies {

    implementation project(':Common')
    implementation libs.koin.android

    //dmp搜索中台SDK的依赖
    /*implementation "com.oplus.dmp.sdk:connect:${prop_dmpSdkVersion}"*/
    implementation libs.oplus.dmp.sdk

}