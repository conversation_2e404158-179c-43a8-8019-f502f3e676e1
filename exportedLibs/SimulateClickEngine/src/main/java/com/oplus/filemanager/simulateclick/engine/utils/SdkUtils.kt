/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - SdkUtils.kt
 * Description:
 *     The utils to check SDK version
 *
 * Version: 1.0
 * Date: 2024-08-20
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-08-20   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.utils

import android.annotation.SuppressLint
import android.os.Build
import androidx.annotation.ChecksSdkIntAtLeast
import androidx.annotation.VisibleForTesting

internal object SdkUtils {

    /**
     * Get OSDK api-level version.
     */
    @JvmStatic
    val oplusOsVersion: Int
        get() = runCatching {
            OplusBuildWrapper.getOplusApiVersion()
        }.getOrDefault(OplusBuildWrapper.UNKNOWN)

    @SuppressLint("ObsoleteSdkInt")
    @ChecksSdkIntAtLeast(api = Build.VERSION_CODES.R)
    @JvmStatic
    fun isAtLeastR(): Boolean =
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.R

    @SuppressLint("ObsoleteSdkInt")
    @ChecksSdkIntAtLeast(api = Build.VERSION_CODES.S)
    @JvmStatic
    fun isAtLeastS(): Boolean =
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.S

    @ChecksSdkIntAtLeast(api = Build.VERSION_CODES.TIRAMISU)
    @JvmStatic
    fun isAtLeastT(): Boolean =
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU

    @ChecksSdkIntAtLeast(api = Build.VERSION_CODES.UPSIDE_DOWN_CAKE)
    @JvmStatic
    fun isAtLeastU(): Boolean =
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE

    @JvmStatic
    fun isAtLeastOS11Point3(): Boolean =
        oplusOsVersion >= OplusBuildWrapper.OS_11_3

    @JvmStatic
    fun isAtLeastOS12(): Boolean =
        oplusOsVersion >= OplusBuildWrapper.OS_12_0

    @JvmStatic
    fun isAtLeastOS13(): Boolean =
        oplusOsVersion >= OplusBuildWrapper.OS_13_0

    @VisibleForTesting
    internal object OplusBuildWrapper {
        private const val LEGACY_OS_BUILD_CLASS = "com.color.os.ColorBuild"
        private const val LEGACY_OS_VERSION_METHOD = "getColorOSVERSION"
        const val UNKNOWN: Int = 0
        const val OS_11_3: Int = 22
        const val OS_12_0: Int = 23
        const val OS_13_0: Int = 26

        @JvmStatic
        fun getOplusApiVersion(): Int = runCatching {
            getOsVersion()
        }.getOrDefault(getLegacyApiVersion())

        @JvmStatic
        private fun getOsVersion(): Int {
            return if (isAtLeastS()) {
                com.oplus.os.OplusBuild.getOplusOSVERSION()
            } else {
                com.heytap.addon.os.OplusBuild.getOplusOSVERSION()
            }
        }

        @JvmStatic
        private fun getLegacyApiVersion(): Int = runCatching {
            val osBuildClass = Class.forName(LEGACY_OS_BUILD_CLASS) ?: return UNKNOWN
            val osVerMethod =
                osBuildClass.getMethod(LEGACY_OS_VERSION_METHOD) ?: return UNKNOWN
            (osVerMethod.invoke(osBuildClass) as? Int) ?: UNKNOWN
        }.getOrDefault(UNKNOWN)
    }
}