/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Recycle
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 20:33
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/21       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.delete

import android.content.ContentValues
import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import android.net.Uri
import com.filemanager.common.RecycleStore
import com.filemanager.common.batch.SingleBatchAction
import com.filemanager.common.fileutils.MIN_RECYCLER_SIZE
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.recyclebin.retriever.DataRetriever
import com.filemanager.recyclebin.utils.OperationDBUtils
import com.heytap.addon.os.OplusUsbEnvironment
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.InvalidSourceFileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.OK
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.OperateFailure
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.RecycleDirectoryError
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.NotEnoughSpace
import java.io.File

class InternalRecycle(
    private val context: Context,
    private val isMediaType: Boolean = false
) : IDelete {

    private val mRecycleBulk: SingleBatchAction<String> by lazy {
        if (isMediaType) {
            RecycleMediaAction(context)
        } else {
            RecycleFileAction()
        }
    }

    @Suppress("TooGenericExceptionCaught")
    override fun delete(fileInfos: List<FileInfo>?): OperationResult {
        val pathList = arrayListOf<String>()
        fileInfos?.forEach { fileInfo ->
            fileInfo.data?.let { pathList.add(it) }
        } ?: return OperationResult(false, InvalidSourceFileInfo)

        if (pathList.isEmpty()) {
            return OperationResult(false, InvalidSourceFileInfo)
        }
        val store = DataRetriever.INSTANCE
        val spaceAvailable = Utils.getStorageAvailableSize(OplusUsbEnvironment.getInternalPath(context))
        if (spaceAvailable < MIN_RECYCLER_SIZE) {
            Log.w(TAG, "delete -> low space = $spaceAvailable")
            return OperationResult(false, NotEnoughSpace)
        }
        if (!store.ensureRecycleDirectory()) {
            Log.w(TAG, "delete -> ensureRecycleDirectory failed")
            return OperationResult(false, RecycleDirectoryError)
        }
        try {
            return recyclePaths(store, pathList)
        } catch (e: Exception) {
            Log.w(TAG, "delete failed ${e.message}")
        } finally {
            mRecycleBulk.flush()
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.onRecycleFilePaths(pathList)
        }
        return OperationResult(false, OperateFailure)
    }

    private fun recyclePaths(store: DataRetriever, paths: ArrayList<String>): OperationResult {
        var failedCount = 0
        var index = 0
        var sqliteOpenHelper: SQLiteOpenHelper? = null
        var db: SQLiteDatabase? = null

        kotlin.runCatching {
            paths.forEach { path ->
                if (store.isInternalPath(path)) {
                    if (db == null) {
                        sqliteOpenHelper = OperationDBUtils.createSQLiteOpenHelper(RecycleStore.Files.INTERNAL_CONTENT_URI)
                        db = OperationDBUtils.beginTransaction(sqliteOpenHelper?.writableDatabase)
                    }
                    if (!internalInnerRecycle(store, path, db, sqliteOpenHelper)) {
                        failedCount++
                        Log.w(TAG, "recyclePaths -> internalRecycle index = $index")
                    }
                } else {
                    if (!externalRecycle(store, path)) {
                        failedCount++
                        Log.w(TAG, "recyclePaths -> externalRecycle index = $index")
                    }
                }
                index++
            }
        }.onFailure {
            Log.d(TAG, "recyclePaths execution failed")
            failedCount++
        }
        OperationDBUtils.commit(db, RecycleStore.Files.INTERNAL_CONTENT_URI)
        sqliteOpenHelper = null
        db = null
        return if (failedCount > 0) {
            OperationResult(false, OperateFailure)
        } else {
            OperationResult(true, OK)
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun internalInnerRecycle(store: DataRetriever, path: String, db: SQLiteDatabase?, sqliteOpenHelper: SQLiteOpenHelper?): Boolean {
        try {
            if (!File(path).exists()) {
                Log.d(TAG, "internalRecycle -> $path not exist")
                mRecycleBulk.add(path)
                return true
            }
            val values = store.extractRequiredColumns(path) ?: return false
            internalStoreInsert(values, db, sqliteOpenHelper)?.let {
                mRecycleBulk.add(path)
                return true
            }
            val originPath = values.getAsString(RecycleStore.Files.FileColumns.ORIGIN_PATH)
            val recyclePath = values.getAsString(RecycleStore.Files.FileColumns.RECYCLE_PATH)
            store.moveToRecycleBin(recyclePath, originPath)
        } catch (e: Exception) {
            Log.e(TAG, "internalRecycle $path failed: ${e.message}")
        }
        return false
    }

    @Suppress("TooGenericExceptionCaught")
    private fun externalRecycle(store: DataRetriever, path: String): Boolean {
        try {
            if (store.deleteQuietly(path)) {
                mRecycleBulk.add(path)
                return true
            }
        } catch (e: Exception) {
            Log.e(TAG, "internalRecycle -> $path failed: $e")
        }
        return false
    }

    @Suppress("TooGenericExceptionCaught")
    private fun internalStoreInsert(values: ContentValues, db: SQLiteDatabase?, sqliteOpenHelper: SQLiteOpenHelper?): Uri? {
        var uri: Uri? = null
        try {
            val store = DataRetriever.INSTANCE
            val originPath = values.getAsString(RecycleStore.Files.FileColumns.ORIGIN_PATH)
            val recyclePath = values.getAsString(RecycleStore.Files.FileColumns.RECYCLE_PATH)
            val statusCode = store.moveToRecycleBin(originPath, recyclePath)
            if (statusCode == STATUS_SUCCESS) {
                uri = OperationDBUtils.insert(
                    RecycleStore.Files.INTERNAL_CONTENT_URI,
                    values,
                    db,
                    sqliteOpenHelper
                )
            }
            Log.d(TAG, "internalStoreInsert ->  $originPath, statusCode: $statusCode")
        } catch (ex: Exception) {
            uri = null
            Log.w(TAG, "internalStoreInsert -> error: $ex")
        }
        return uri
    }

    companion object {
        private const val TAG = "Recycle"

        /**
         * status code for all operation in recycleBin,if statusCode is Positive, represent failed count
         */
        // status code for success
        private const val STATUS_SUCCESS = 0
    }
}