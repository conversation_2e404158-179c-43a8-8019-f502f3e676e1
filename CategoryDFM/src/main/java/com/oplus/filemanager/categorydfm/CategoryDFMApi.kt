/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CategoryDFMApi
 ** Description : Category DFM api
 ** Version     : 1.0
 ** Date        : 2024/01/29 11:34
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/01/29       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.categorydfm

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewListFragmentCreator
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.categorydfm.dfm.DFMConflictHandler
import com.oplus.filemanager.categorydfm.ui.CategoryDfmActivity
import com.oplus.filemanager.categorydfm.ui.CategoryDfmParentFragment
import com.oplus.filemanager.dfm.DFMManager
import com.oplus.filemanager.interfaze.categorydfm.ICategoryDFMApi
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.main.IMain
import java.util.function.Consumer

object CategoryDFMApi : ICategoryDFMApi {

    private const val TAG = "CategoryDFMApi"

    override fun getFragment(activity: Activity): Fragment {
        val fragment = PreviewCombineFragment()
        fragment.setPreviewListFragmentCreator(object : IPreviewListFragmentCreator {
            override fun create(): IPreviewListFragment {
                return CategoryDfmParentFragment()
            }
        })
        return fragment
    }

    override fun onResumeLoadData(fragment: Fragment) {
        val jumpAllTab = fragment.arguments?.getBoolean(KtConstants.DFM_JUMP_ALL_TAB) ?: false
        Log.d(TAG, "onResumeLoadData jumpAllTab $jumpAllTab")
        if (fragment is PreviewCombineFragment) {
            if (jumpAllTab) {
                fragment.arguments?.remove(KtConstants.DFM_JUMP_ALL_TAB)
                (fragment.getPreviewFragment() as? CategoryDfmParentFragment)?.jumpAllTab()
            }
            fragment.onResumeLoadData()
        }
    }

    override fun onCreateOptionsMenu(fragment: Fragment, menu: Menu, inflater: MenuInflater) {
        if (fragment is PreviewCombineFragment) {
            fragment.onCreateOptionsMenu(menu, inflater)
        }
    }

    override fun onMenuItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onMenuItemSelected")
        if (fragment is PreviewCombineFragment) {
            return fragment.onMenuItemSelected(item)
        }
        return false
    }

    override fun pressBack(fragment: Fragment): Boolean {
        Log.d(TAG, "pressBack")
        if (fragment is PreviewCombineFragment) {
            return fragment.pressBack()
        }
        return false
    }

    override fun onNavigationItemSelected(fragment: Fragment, item: MenuItem): Boolean {
        Log.d(TAG, "onNavigationItemSelected $item")
        if (fragment is PreviewCombineFragment) {
            return fragment.onNavigationItemSelected(item)
        }
        return false
    }

    override fun setToolbarAndTabListener(
        fragment: Fragment,
        toolbar: COUIToolbar?,
        title: String,
        tabListener: TabActivityListener<MediaFileWrapper>
    ) {
        Log.d(TAG, "setToolbarAndTabListener")
    }

    override fun fromSelectPathResult(fragment: Fragment, requestCode: Int, paths: List<String>?) {
        Log.d(TAG, "fromSelectPathResult")
        if (fragment is PreviewCombineFragment) {
            fragment.fromSelectPathResult(requestCode, paths)
        }
    }

    override fun updateLabels(fragment: Fragment) {
        Log.d(TAG, "updateLabels")
    }

    override fun setIsHalfScreen(fragment: Fragment, category: Int, isHalfScreen: Boolean) {
        if (fragment is PreviewCombineFragment) {
            fragment.setIsHalfScreen(isHalfScreen)
        }
    }

    override fun permissionSuccess(fragment: Fragment) {
        Log.d(TAG, "permissionSuccess")
    }

    override fun setCurrentFilePath(fragment: Fragment, path: String?) {
        Log.d(TAG, "setCurrentFilePath")
    }

    override fun getCurrentPath(fragment: Fragment): String {
        Log.d(TAG, "getCurrentPath")
        return ""
    }

    override fun exitSelectionMode(fragment: Fragment) {
        if (fragment is PreviewCombineFragment) {
            fragment.exitSelectionMode()
        }
    }

    override fun onSideNavigationClicked(fragment: Fragment, isOpen: Boolean): Boolean {
        if (fragment is PreviewCombineFragment) {
            return fragment.onSideNavigationClicked(isOpen)
        }
        return false
    }

    override fun backToTop(fragment: Fragment) {
        Log.d(TAG, "backToTop")
        if (fragment is PreviewCombineFragment) {
            fragment.backToTop()
        }
    }

    override fun startDFM(
        activity: Activity,
        deviceName: String,
        path: String,
        fromDetail: Boolean
    ) {
        Log.i(TAG, "startDFM activity $activity, isSmallScreenPhone ${FeatureCompat.isSmallScreenPhone}")
        if (FeatureCompat.isSmallScreenPhone) {
            startDFMActivity(activity, deviceName, path, fromDetail)
        } else {
            startDFMFragment(activity, deviceName, path, fromDetail)
        }
    }

    override fun init(context: Context, needInit: Boolean) {
        Log.d(TAG, "init needInit:$needInit")
        if (!FeatureCompat.isSupportPCConnect) {
            Log.w(TAG, "init -> not support pc connect")
            return
        }
        runCatching {
            DFMManager.init(context, needInit)
        }.onFailure {
            exit()
            Log.d(TAG, "init error: ${it.message}")
        }
    }

    override fun openDfsP2PConnect() {
        Log.d(TAG, "openDfsP2pConnect")
        DFMManager.openDfsP2pConnect()
    }

    override fun exit() {
        Log.d(TAG, "exit")
        runCatching {
            DFMManager.exit()
        }.onFailure {
            //java.lang.IllegalArgumentException: Service not registered: com.oplus.dfs.service.DfsManager$1
            Log.e(TAG, "exit onFailure: ${it.message}")
        }
    }

    override fun getDFSDevice(consumer: Consumer<Bundle>): Bundle {
        Log.d(TAG, "getDFSDevice")
        return DFMManager.getDFSDevice(consumer)
    }

    override fun handleP2PConflict(activity: AppCompatActivity) {
        DFMConflictHandler(activity)
    }

    override fun getDFSMountPath(): String? {
        return DFMManager.getDFSMountPath()
    }

    private fun startDFMActivity(activity: Activity, deviceName: String, path: String, fromDetail: Boolean) {
        val intent = Intent().apply {
            putExtra(KtConstants.P_TITLE, deviceName)
            putExtra(KtConstants.CURRENT_PATH, path)
            putExtra(KtConstants.DFM_JUMP_ALL_TAB, fromDetail)
            setClass(activity, CategoryDfmActivity::class.java)
        }
        Log.i(TAG, "startDFMActivity $activity, deviceName $deviceName, path $path, fromDetail $fromDetail")
        activity.startActivity(intent)
    }

    private fun startDFMFragment(activity: Activity, deviceName: String, path: String, fromDetail: Boolean) {
        Log.i(
            TAG,
            "startDFMFragment $activity, deviceName $deviceName, path $path, fromDetail $fromDetail"
        )
        val bundle = Bundle().apply {
            putString(KtConstants.P_TITLE, deviceName)
            putString(KtConstants.CURRENT_PATH, path)
            putBoolean(KtConstants.DFM_JUMP_ALL_TAB, fromDetail)
            putBoolean(KtConstants.FROM_DETAIL, fromDetail)
        }
        if (checkActivityFromSearch(activity)) {
            processJumpDFMFragmentFromSearch(activity, path)
        } else {
            Injector.injectFactory<IMain>()?.startFragment(activity, CategoryHelper.CATEGORY_DFM, bundle)
        }
    }

    /**
     * 判断是否从搜索页面跳转DFM页面
     */
    private fun checkActivityFromSearch(activity: Activity): Boolean {
        val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
        val isGlobalSearchActivity =
            categoryGlobalSearchApi?.isGlobalSearchActivity(activity) ?: false
        val isGlobalSearchMoreActivity =
            categoryGlobalSearchApi?.isGlobalSearchMoreActivity(activity) ?: false
        val result = isGlobalSearchActivity || isGlobalSearchMoreActivity
        Log.i(TAG, "checkActivityFromSearch $activity, result $result")
        return result
    }

    /**
     * 在平板上跳转DFM的页面时，需要按照分布式文管跳转文件的方式，带入deviceID, from_device,
     * 需要clearTop将当前搜索页面出栈
     */
    private fun processJumpDFMFragmentFromSearch(activity: Activity, path: String) {
        val deviceId = KtUtils.getDfmDeviceIdFromPath(path)
        val intent = Intent().apply {
            putExtra(Constants.KEY_IS_FROM_DEVICE, true)
            putExtra(Constants.KEY_REMOTE_DEVICE_ID, deviceId)
            addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }
        Log.d(TAG, "processJumpDFMFragmentFromSearch devicesId $deviceId")
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.startMainActivity(activity, intent)
    }
}

