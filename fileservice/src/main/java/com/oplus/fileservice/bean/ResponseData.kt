/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ResponseData
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/4 16:01
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/4       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.bean

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

data class ResponseData<T>(
    @Expose @SerializedName("code") val code: Int,
    @Expose @SerializedName("data") val data: T,
    @Expose @SerializedName("description") val description: String
)

data class FileListData<T>(
    @Expose @SerializedName("result") val result: List<T>?,
    @Expose @SerializedName("pageNo") val pageNo: Int,
    @Expose @SerializedName("total") val total: Int,
    @Expose @SerializedName("currentPath") val currentPath: String?
)

data class CategoryListData<T>(
    @Expose @SerializedName("result") val result: List<T>?,
    @Expose @SerializedName("pageNo") val pageNo: Int,
    @Expose @SerializedName("total") val total: Int,
    @Expose @SerializedName("isAlbumSet") val isAlbumSet: Boolean
)