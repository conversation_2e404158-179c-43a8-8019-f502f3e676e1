package com.coloros.filemanager.utils

import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import com.filemanager.common.DiskLruCache
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.KtCacheManager
import com.filemanager.common.utils.Md5Utils
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class KtCacheManagerTest {

    @MockK
    lateinit var mContext: Context

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        mContext = mockk(relaxed = true)
    }


    @Test
    fun `should verify when single batch action without`() {
        mockkObject(KtCacheManager)
        var baseFileBean = BaseFileBean()

        Assert.assertNull(KtCacheManager.getCacheKey(baseFileBean))

        mockkStatic(Md5Utils::class)
        every { Md5Utils.toKey(any()) }.returns("test")
        baseFileBean.mData = "test"

        Assert.assertEquals("test", KtCacheManager.getCacheKey(baseFileBean))
    }

    @Test
    fun `should verify when add package name to disk without`() {
        var baseFileBean = BaseFileBean()

        KtCacheManager.addPackageNameToDisk(baseFileBean, "", null)

        var diskLruCache = mockk<DiskLruCache>()

        mockkObject(KtCacheManager)
        every { KtCacheManager.getCacheKey(any()) }.returns(null)
        KtCacheManager.addPackageNameToDisk(baseFileBean, "", diskLruCache)

        every { KtCacheManager.getCacheKey(any()) }.returns("test")
        var editor = mockk<DiskLruCache.Editor>(relaxed = true)
        every { diskLruCache.edit(any()) }.returns(editor)
        KtCacheManager.addPackageNameToDisk(baseFileBean, "test", diskLruCache)
        verify { editor.commit() }

        try {
            every { diskLruCache.edit(any()) }.throws(Exception("test-error"))
            KtCacheManager.addPackageNameToDisk(baseFileBean, "", diskLruCache)
        } catch (e: Exception) {

        }
    }

    @Test
    fun `should verify when get package name and save without`() {
        var baseFileBean = BaseFileBean()

        Assert.assertEquals("", KtCacheManager.getPackageNameAndSave(mContext, baseFileBean, null))

        var packageInfo = PackageInfo()
        packageInfo.packageName = "test"
        every { mContext.packageManager }.returns(mockk(relaxed = true) {
            every { getPackageArchiveInfo("test", PackageManager.GET_ACTIVITIES) }.returns(packageInfo)
        })

        baseFileBean.mData = "test"
        var diskLruCache = mockk<DiskLruCache>(relaxed = true)
        Assert.assertEquals("test", KtCacheManager.getPackageNameAndSave(mContext, baseFileBean, diskLruCache))

        try {
            every { mContext.packageManager }.throws(Exception("test-error"))
            Assert.assertEquals("", KtCacheManager.getPackageNameAndSave(mContext, baseFileBean, diskLruCache))
        } catch (e: Exception) {
        }
    }


    @Test
    fun `should verify when get package name disk cache without`() {
        var baseFileBean = BaseFileBean()
        Assert.assertEquals(null, KtCacheManager.getPackageNameDiskCache(baseFileBean, null))

        var diskLruCache = mockk<DiskLruCache>(relaxed = true)
        mockkObject(KtCacheManager)
        every { KtCacheManager.getCacheKey(any()) }.returns(null)
        Assert.assertEquals(null, KtCacheManager.getPackageNameDiskCache(baseFileBean, diskLruCache))

        every { KtCacheManager.getCacheKey(any()) }.returns("test")
        every { diskLruCache.get(any()) }.returns(mockk(relaxed = true) {
            every { getInputStream(0) }.returns(mockk(relaxed = true))
        })
        Assert.assertNotNull(KtCacheManager.getPackageNameDiskCache(baseFileBean, diskLruCache))

        try {
            every { diskLruCache.get(any()) }.throws(Exception("test-error"))
            Assert.assertEquals(null, KtCacheManager.getPackageNameDiskCache(baseFileBean, diskLruCache))
        } catch (e: Exception) {

        }

    }

    @Test
    fun `should verify when get disk lruCache without`() {
        mockkStatic(VolumeEnvironment::class)


        every { VolumeEnvironment.getDataDirPath(mContext, "KtCache") }.returns("@11123123test")
        Assert.assertNotNull(KtCacheManager.getDiskLruCache(mContext, "hm"))


        try {
            every { VolumeEnvironment.getDataDirPath(mContext, "KtCache") }.throws(Exception("test-error"))
            Assert.assertNull(KtCacheManager.getDiskLruCache(mContext, "hm"))
        } catch (e: Exception) {

        }
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}