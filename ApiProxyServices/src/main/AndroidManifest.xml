<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <application>
        <activity
            android:name=".OpenFileActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize|keyboard|navigation"
            android:exported="true"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:launchMode="standard"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:windowSoftInputMode="adjustPan"
            android:autoRemoveFromRecents="true"
            android:excludeFromRecents="true" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- filemanager://deeplink.open?file_path=xxx-->
                <data
                    android:scheme="filemanager"
                    android:host="deeplink.open"
                    />
            </intent-filter>
        </activity>

        <provider
            android:name=".SearchProvider"
            android:authorities="${applicationId}.search_doc"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="com.oplus.seedling.action.SEEDLING_SERVICE" />
            </intent-filter>
            <meta-data
                android:name="oplus.seedling.provider"
                android:value="config.json" />
        </provider>
    </application>

</manifest>