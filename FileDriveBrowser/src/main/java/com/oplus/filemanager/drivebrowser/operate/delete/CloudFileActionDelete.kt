/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudFileActionDelete
 ** Description : 云端文件删除的Action
 ** Version     : 1.0
 ** Date        : 2024/01/09 15:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/01/09       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.operate.delete

import android.content.DialogInterface
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.utils.DriveCache
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.stringResource
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.base.DISMISS_PROGRESS
import com.filemanager.fileoperate.base.SHOW_PROGRESS
import com.oplus.filemanager.drivebrowser.data.model.tencent.DeleteFileRequest
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.provider.DriveFileDBHelper
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking

class CloudFileActionDelete(
    lifecycle: LifecycleOwner,
    private val source: String,
    private val selectFiles: List<DriveFileWrapper>,
    private var networkService: FileDriveRepository
) : BaseFileAction<CloudFileDeleteObserver>(lifecycle) {

    companion object {
        private const val TAG = "CloudFileActionDelete"
        private const val DELAY_INTERNAL = 200L

        private const val MAX_COUNT = 50
        private const val SECONDARY_COUNT = 30
        private const val MIN_COUNT = 5

        private const val SIX_SECOND = 6 * 1000L
        private const val FOUR_SECOND = 4 * 1000L
        private const val THREE_SECOND = 3 * 1000L
        private const val TWO_SECOND = 1500L

        /**
         * 50个异常，延迟6秒；30个以上 延迟4秒；5个以上延迟3秒；2个以上1.5秒
         */
        fun getDelayTime(fileSize: Int): Long {
            return when {
                (fileSize >= MAX_COUNT) -> SIX_SECOND
                (fileSize >= SECONDARY_COUNT) -> FOUR_SECOND
                (fileSize >= MIN_COUNT) -> THREE_SECOND
                else -> TWO_SECOND
            }
        }
    }

    @VisibleForTesting
    val lockObj = Object()

    @VisibleForTesting
    var fileDeleteBean: CloudFileDeleteObserver.FileDeleteBean? = null

    @VisibleForTesting
    var isAuth = true

    @VisibleForTesting
    fun initFileDeleteBean(count: Int, source: String) {
        fileDeleteBean = CloudFileDeleteObserver.FileDeleteBean(count, source) { _, which ->
            Log.d(TAG, "onDialogClick $which")
            when (which) {
                DialogInterface.BUTTON_NEUTRAL -> notifyLockReleased()
                else -> cancel()
            }
        }
    }

    override fun onCancelled() {
        notifyLockReleased()
        super.onCancelled()
    }

    @VisibleForTesting
    fun notifyLockReleased() {
        Log.d(TAG, "notifyLockReleased")
        synchronized(lockObj) {
            lockObj.notify()
        }
    }

    override fun run(): Boolean {
        if (selectFiles.isEmpty()) {
            return false
        }
        val auth = runBlocking {
            networkService.isFileAuth(selectFiles[0].id)
        }
        if (auth.not()) {
            Log.i(TAG, "check not auth")
            isAuth = false
            return false
        }
        isAuth = true
        initFileDeleteBean(selectFiles.size, source)
        notifyObserver(SHOW_DELETE_CONFIRM_DIALOG, fileDeleteBean)
        while (!isCancelled()) {
            try {
                synchronized(lockObj) {
                    lockObj.wait()
                }
            } catch (e: InterruptedException) {
                Log.e(TAG, "run interrupted", e)
                return false
            }
            val canceled = isCancelled()
            Log.d(TAG, "Continue to execute: isCancelled=$canceled")
            if (canceled) {
                return false
            }
            return reallyExecuteAction(selectFiles)
        }
        return false
    }

    @VisibleForTesting
    fun reallyExecuteAction(files: List<DriveFileWrapper>): Boolean {
        Log.d(TAG, "reallyExecuteAction files = ${files.size}")
        // 显示loading 弹窗
        showProgressDialog()
        val result = runBlocking {
            // 请求网络删除
            val networkResult: Pair<Boolean, String>
            if (files.size > 1) {
                networkResult = networkService.deleteFiles(files)
                /*
                   1，由于批量删除是服务端是异步的，加个2秒延时，防止这边没删完，直接又去拉列表，导致异常
                   2，删除的过程中是有loading的，越多延迟越久，用户是可以理解的
                 */
                delay(getDelayTime(files.size))
            } else {
                val file = files[0]
                networkResult = if (file.mIsDirectory) {
                    networkService.deleteFolder(file.id)
                } else {
                    networkService.deleteFile(file.id, DeleteFileRequest.TYPE_ORIGIN)
                }
            }
            val remoteDelete = networkResult.first
            if (remoteDelete) {
                removeDBData(files)
            } else {
                statisticsDeleteFail(networkResult.second)
            }
            Log.i(TAG, "reallyExecuteAction result-> remoteDelete : $remoteDelete")
            remoteDelete
        }
        // 取消loading弹窗
        cancelShowProgressDialog()
        return result
    }

    @VisibleForTesting
    fun removeDBData(files: List<DriveFileWrapper>) {
        val foldIds = files.filter { it.mIsDirectory }.map { it.id }
        val fileIds = files.filter { it.mIsDirectory.not() }.map { it.id }
        if (foldIds.isNotEmpty()) {
            Log.i(TAG, "reallyExecuteAction foldIds = ${foldIds.size}")
            if (foldIds.size > 1) {
                DriveFileDBHelper.batchDeleteFolder(source, foldIds)
            } else {
                DriveFileDBHelper.deleteFolder(source, foldIds[0])
            }
        }

        if (fileIds.isNotEmpty()) {
            Log.i(TAG, "reallyExecuteAction fileIds = ${fileIds.size}")
            if (fileIds.size > 1) {
                DriveFileDBHelper.batchDeleteDocument(source, fileIds)
            } else {
                DriveFileDBHelper.deleteDocument(source, fileIds[0])
            }
        }
    }

    /**
     * 统计删除文件失败
     */
    @VisibleForTesting
    fun statisticsDeleteFail(msg: String) {
        val map = hashMapOf<String, String>()
        map[StatisticsUtils.FILE_SOURCE] = source
        map[StatisticsUtils.ERROR] = msg
        map[StatisticsUtils.COUNT] = "1"
        StatisticsUtils.onCommon(appContext, StatisticsUtils.DELETE_DRIVE_FILE_FAIL, map)
    }

    /**
     * 显示loading弹窗
     */
    @VisibleForTesting
    fun showProgressDialog() {
        notifyObserver(
            SHOW_PROGRESS,
            BaseFileActionObserver.ProgressDialogBean(stringResource(com.filemanager.common.R.string.dialog_deleting), true, 0),
            DELAY_INTERNAL
        )
    }

    /**
     * 取消显示删除进度弹窗
     */
    @VisibleForTesting
    fun cancelShowProgressDialog() {
        cancelNotifyObserver(SHOW_PROGRESS)
        notifyObserver(DISMISS_PROGRESS)
    }

    override fun afterRun(result: Boolean) {
        super.afterRun(result)
        if (result) {
            //主页 + 搜索中选择一个云文档删除，会走这里
            putDeleteCache(selectFiles)
            notifyObserver(ACTION_DONE)
        } else {
            notifyObserver(ACTION_FAILED, isAuth)
        }
    }

    private fun putDeleteCache(files: List<DriveFileWrapper>) {
        files.forEach {
            DriveCache.putDeleteCache(it.id)
        }
    }
}