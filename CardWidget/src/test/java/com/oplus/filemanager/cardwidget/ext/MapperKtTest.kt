/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MapperKtTest.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/19
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/19      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.ext

import com.filemanager.common.helper.MimeTypeHelper
import com.oplus.filemanager.recent.entity.recent.RecentFileEntity
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert.assertEquals
import org.junit.Test

class MapperKtTest {

    companion object {
        const val TYPE = MimeTypeHelper.IMAGE_TYPE
        const val FULL_PATH = "/sdcard/download/demo.jpg"
        const val NAME = "demo.jpg"
        const val DATE_MODIFIED = 10000000000L
        const val DURATION = 0L
        const val SIZE = 1024L
    }

    @Test
    fun mapperToRecentFileCardItemData() {
        val recentFile = mockk<RecentFileEntity>()
        every { recentFile.mLocalType } returns TYPE
        every { recentFile.mAbsolutePath } returns FULL_PATH
        every { recentFile.mDisplayName } returns NAME
        every { recentFile.mDateModified } returns DATE_MODIFIED
        every { recentFile.mDuration } returns DURATION
        every { recentFile.mSize } returns SIZE

        val mapResult = recentFile.mapperToRecentFileCardItemData()
        assertEquals(TYPE, mapResult.type)
        assertEquals(FULL_PATH, mapResult.path)
        assertEquals(NAME, mapResult.name)
        assertEquals(DATE_MODIFIED, mapResult.dateModified)
        assertEquals(SIZE, mapResult.size)
    }

    @Test
    fun getBgBackgroundFromType() {
        assertEquals(PIC_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.IMAGE_TYPE))
        assertEquals(VIDEO_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.VIDEO_TYPE))
        assertEquals(AUDIO_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.AUDIO_TYPE))
        assertEquals(DOC_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.DOCX_TYPE))
        assertEquals(DOC_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.DOC_TYPE))
        assertEquals(XLS_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.XLSX_TYPE))
        assertEquals(XLS_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.XLS_TYPE))
        assertEquals(PPT_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.PPTX_TYPE))
        assertEquals(PPT_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.PPT_TYPE))
        assertEquals(PDF_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.PDF_TYPE))
        assertEquals(PDF_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.OFD_TYPE))
        assertEquals(TXT_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.TXT_TYPE))
        assertEquals(APK_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.APPLICATION_TYPE))
        assertEquals(ARCHIVE_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.P7ZIP_TYPE))
        assertEquals(ARCHIVE_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.RAR_TYPE))
        assertEquals(ARCHIVE_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.ZIP_TYPE))
        assertEquals(ARCHIVE_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.JAR_TYPE))
        assertEquals(CALENDAR_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.VCS_TYPE))
        assertEquals(CALENDAR_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.ICS_TYPE))
        assertEquals(CONTACT_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.VCF_TYPE))
        assertEquals(CONTACT_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.CSV_TYPE))
        assertEquals(THEME_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.THEME_TYPE))
        assertEquals(EBOOK_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.EPUB_TYPE))
        assertEquals(EBOOK_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.EBK_TYPE))
        assertEquals(EBOOK_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.CHM_TYPE))
        assertEquals(HTML_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.HTML_TYPE))
        assertEquals(LYRIC_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.LRC_TYPE))
        assertEquals(SMS_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.VMSG_TYPE))
        assertEquals(UNKNOWN_ITEM_BG, getBgBackgroundFromType(MimeTypeHelper.UNKNOWN_TYPE))
        assertEquals(UNKNOWN_ITEM_BG, getBgBackgroundFromType(-1))
    }

    @Test
    fun getIconFromType() {
        assertEquals(IC_PIC, getIconFromType(MimeTypeHelper.IMAGE_TYPE))
        assertEquals(IC_VIDEO, getIconFromType(MimeTypeHelper.VIDEO_TYPE))
        assertEquals(IC_AUDIO, getIconFromType(MimeTypeHelper.AUDIO_TYPE))
        assertEquals(IC_DOC, getIconFromType(MimeTypeHelper.DOCX_TYPE))
        assertEquals(IC_DOC, getIconFromType(MimeTypeHelper.DOC_TYPE))
        assertEquals(IC_XLS, getIconFromType(MimeTypeHelper.XLSX_TYPE))
        assertEquals(IC_XLS, getIconFromType(MimeTypeHelper.XLS_TYPE))
        assertEquals(IC_PPT, getIconFromType(MimeTypeHelper.PPTX_TYPE))
        assertEquals(IC_PPT, getIconFromType(MimeTypeHelper.PPT_TYPE))
        assertEquals(IC_PDF, getIconFromType(MimeTypeHelper.PDF_TYPE))
        assertEquals(IC_OFD, getIconFromType(MimeTypeHelper.OFD_TYPE))
        assertEquals(IC_TXT, getIconFromType(MimeTypeHelper.TXT_TYPE))
        assertEquals(IC_APK, getIconFromType(MimeTypeHelper.APPLICATION_TYPE))
        assertEquals(IC_7Z, getIconFromType(MimeTypeHelper.P7ZIP_TYPE))
        assertEquals(IC_RAR, getIconFromType(MimeTypeHelper.RAR_TYPE))
        assertEquals(IC_ZIP, getIconFromType(MimeTypeHelper.ZIP_TYPE))
        assertEquals(IC_JAR, getIconFromType(MimeTypeHelper.JAR_TYPE))
        assertEquals(IC_CALENDAR, getIconFromType(MimeTypeHelper.VCS_TYPE))
        assertEquals(IC_CALENDAR, getIconFromType(MimeTypeHelper.ICS_TYPE))
        assertEquals(IC_CONTACT, getIconFromType(MimeTypeHelper.VCF_TYPE))
        assertEquals(IC_CONTACT, getIconFromType(MimeTypeHelper.CSV_TYPE))
        assertEquals(IC_THEME, getIconFromType(MimeTypeHelper.THEME_TYPE))
        assertEquals(IC_EBOOK, getIconFromType(MimeTypeHelper.EPUB_TYPE))
        assertEquals(IC_EBOOK, getIconFromType(MimeTypeHelper.EBK_TYPE))
        assertEquals(IC_EBOOK, getIconFromType(MimeTypeHelper.CHM_TYPE))
        assertEquals(IC_HTML, getIconFromType(MimeTypeHelper.HTML_TYPE))
        assertEquals(IC_LYRIC, getIconFromType(MimeTypeHelper.LRC_TYPE))
        assertEquals(IC_VMSG, getIconFromType(MimeTypeHelper.VMSG_TYPE))
        assertEquals(IC_UNKNOWN, getIconFromType(MimeTypeHelper.UNKNOWN_TYPE))
        assertEquals(IC_UNKNOWN, getIconFromType(-1))
    }

    @Test
    fun should_return_int_when_getIconResFromType() {
        assertEquals(com.filemanager.common.R.drawable.ic_file_image, getIconResFromType(MimeTypeHelper.IMAGE_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_video, getIconResFromType(MimeTypeHelper.VIDEO_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_audio, getIconResFromType(MimeTypeHelper.AUDIO_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_doc, getIconResFromType(MimeTypeHelper.DOCX_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_doc, getIconResFromType(MimeTypeHelper.DOC_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_excl, getIconResFromType(MimeTypeHelper.XLSX_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_excl, getIconResFromType(MimeTypeHelper.XLS_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_ppt, getIconResFromType(MimeTypeHelper.PPTX_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_ppt, getIconResFromType(MimeTypeHelper.PPT_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_pdf, getIconResFromType(MimeTypeHelper.PDF_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_ofd, getIconResFromType(MimeTypeHelper.OFD_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_txt, getIconResFromType(MimeTypeHelper.TXT_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_apk, getIconResFromType(MimeTypeHelper.APPLICATION_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_compress_7z, getIconResFromType(MimeTypeHelper.P7ZIP_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_compress_rar, getIconResFromType(MimeTypeHelper.RAR_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_compress_zip, getIconResFromType(MimeTypeHelper.ZIP_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_compress_jar, getIconResFromType(MimeTypeHelper.JAR_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_calendar_icon, getIconResFromType(MimeTypeHelper.VCS_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_calendar_icon, getIconResFromType(MimeTypeHelper.ICS_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_vcard_icon, getIconResFromType(MimeTypeHelper.VCF_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_vcard_icon, getIconResFromType(MimeTypeHelper.CSV_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_theme_icon, getIconResFromType(MimeTypeHelper.THEME_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_txt, getIconResFromType(MimeTypeHelper.EPUB_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_txt, getIconResFromType(MimeTypeHelper.EBK_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_txt, getIconResFromType(MimeTypeHelper.CHM_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_html_icon, getIconResFromType(MimeTypeHelper.HTML_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_lrc_icon, getIconResFromType(MimeTypeHelper.LRC_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_sms_icon, getIconResFromType(MimeTypeHelper.VMSG_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_other_icon, getIconResFromType(MimeTypeHelper.UNKNOWN_TYPE))
        assertEquals(com.filemanager.common.R.drawable.ic_file_other_icon, getIconResFromType(-1))
    }
}