#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 230686720 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3550), pid=16444, tid=3044
#
# JRE version: Java(TM) SE Runtime Environment (17.0.8+9) (build 17.0.8+9-LTS-211)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\Questionnaire\build\20250901_6990565302930332843.compiler.options

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Sep  1 11:27:18 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 6.671222 seconds (0d 0h 0m 6s)

---------------  T H R E A D  ---------------

Current thread (0x000002637e2e8b30):  VMThread "VM Thread" [stack: 0x000000a439500000,0x000000a439600000] [id=3044]

Stack: [0x000000a439500000,0x000000a439600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x677d0a]
V  [jvm.dll+0x7d8c54]
V  [jvm.dll+0x7da3fe]
V  [jvm.dll+0x7daa63]
V  [jvm.dll+0x245c5f]
V  [jvm.dll+0x674bb9]
V  [jvm.dll+0x6694f2]
V  [jvm.dll+0x3031d6]
V  [jvm.dll+0x30a756]
V  [jvm.dll+0x359f9e]
V  [jvm.dll+0x35a1cf]
V  [jvm.dll+0x2da3e8]
V  [jvm.dll+0x2d87f5]
V  [jvm.dll+0x2d7dfc]
V  [jvm.dll+0x31b4cb]
V  [jvm.dll+0x7df26b]
V  [jvm.dll+0x7dffa4]
V  [jvm.dll+0x7e04bd]
V  [jvm.dll+0x7e0894]
V  [jvm.dll+0x7e0960]
V  [jvm.dll+0x788bba]
V  [jvm.dll+0x676b35]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]

VM_Operation (0x000000a438ffa810): G1CollectForAllocation, mode: safepoint, requested by thread 0x00000263709426f0


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000263602074d0, length=22, elements={
0x00000263709426f0, 0x000002637e2ecec0, 0x000002637e2ee4d0, 0x000002637e308640,
0x000002637e308ff0, 0x000002637e30a140, 0x000002637e30b290, 0x000002637e30fc10,
0x000002637e313550, 0x000002637e315ed0, 0x0000026360195cd0, 0x000002636019edf0,
0x00000263609ee4b0, 0x00000263609ee9c0, 0x00000263609ed070, 0x00000263661388e0,
0x0000026366136a80, 0x0000026366136570, 0x0000026366136060, 0x000002636613d9e0,
0x0000026366139300, 0x0000026366139810
}

Java Threads: ( => current thread )
  0x00000263709426f0 JavaThread "main" [_thread_blocked, id=24956, stack(0x000000a438f00000,0x000000a439000000)]
  0x000002637e2ecec0 JavaThread "Reference Handler" daemon [_thread_blocked, id=14436, stack(0x000000a439600000,0x000000a439700000)]
  0x000002637e2ee4d0 JavaThread "Finalizer" daemon [_thread_blocked, id=12580, stack(0x000000a439700000,0x000000a439800000)]
  0x000002637e308640 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=23296, stack(0x000000a439800000,0x000000a439900000)]
  0x000002637e308ff0 JavaThread "Attach Listener" daemon [_thread_blocked, id=10424, stack(0x000000a439900000,0x000000a439a00000)]
  0x000002637e30a140 JavaThread "Service Thread" daemon [_thread_blocked, id=24212, stack(0x000000a439a00000,0x000000a439b00000)]
  0x000002637e30b290 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=21860, stack(0x000000a439b00000,0x000000a439c00000)]
  0x000002637e30fc10 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=14568, stack(0x000000a439c00000,0x000000a439d00000)]
  0x000002637e313550 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=20768, stack(0x000000a439d00000,0x000000a439e00000)]
  0x000002637e315ed0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=5348, stack(0x000000a439e00000,0x000000a439f00000)]
  0x0000026360195cd0 JavaThread "Notification Thread" daemon [_thread_blocked, id=14784, stack(0x000000a43a000000,0x000000a43a100000)]
  0x000002636019edf0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=25524, stack(0x000000a43a200000,0x000000a43a300000)]
  0x00000263609ee4b0 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=24236, stack(0x000000a43a300000,0x000000a43a400000)]
  0x00000263609ee9c0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=14528, stack(0x000000a43a400000,0x000000a43a500000)]
  0x00000263609ed070 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=8928, stack(0x000000a43a500000,0x000000a43a600000)]
  0x00000263661388e0 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=18576, stack(0x000000a439f00000,0x000000a43a000000)]
  0x0000026366136a80 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=22228, stack(0x000000a43b600000,0x000000a43b700000)]
  0x0000026366136570 JavaThread "C2 CompilerThread3" daemon [_thread_blocked, id=21252, stack(0x000000a43b700000,0x000000a43b800000)]
  0x0000026366136060 JavaThread "C2 CompilerThread4" daemon [_thread_blocked, id=14720, stack(0x000000a43b800000,0x000000a43b900000)]
  0x000002636613d9e0 JavaThread "C2 CompilerThread5" daemon [_thread_blocked, id=21264, stack(0x000000a43b900000,0x000000a43ba00000)]
  0x0000026366139300 JavaThread "C2 CompilerThread6" daemon [_thread_blocked, id=26920, stack(0x000000a43ba00000,0x000000a43bb00000)]
  0x0000026366139810 JavaThread "C2 CompilerThread7" daemon [_thread_blocked, id=14776, stack(0x000000a43bb00000,0x000000a43bc00000)]

Other Threads:
=>0x000002637e2e8b30 VMThread "VM Thread" [stack: 0x000000a439500000,0x000000a439600000] [id=3044]
  0x000002636019d090 WatcherThread [stack: 0x000000a43a100000,0x000000a43a200000] [id=22080]
  0x00000263709f0ad0 GCTaskThread "GC Thread#0" [stack: 0x000000a439000000,0x000000a439100000] [id=28052]
  0x000002636062ede0 GCTaskThread "GC Thread#1" [stack: 0x000000a43a600000,0x000000a43a700000] [id=19472]
  0x000002636062f090 GCTaskThread "GC Thread#2" [stack: 0x000000a43a700000,0x000000a43a800000] [id=7376]
  0x000002636062f340 GCTaskThread "GC Thread#3" [stack: 0x000000a43a800000,0x000000a43a900000] [id=3268]
  0x000002636062f5f0 GCTaskThread "GC Thread#4" [stack: 0x000000a43a900000,0x000000a43aa00000] [id=26456]
  0x00000263605a2020 GCTaskThread "GC Thread#5" [stack: 0x000000a43aa00000,0x000000a43ab00000] [id=17620]
  0x00000263605a22d0 GCTaskThread "GC Thread#6" [stack: 0x000000a43ab00000,0x000000a43ac00000] [id=27476]
  0x00000263605a2580 GCTaskThread "GC Thread#7" [stack: 0x000000a43ac00000,0x000000a43ad00000] [id=25576]
  0x00000263605a2830 GCTaskThread "GC Thread#8" [stack: 0x000000a43ad00000,0x000000a43ae00000] [id=25992]
  0x00000263605a2ae0 GCTaskThread "GC Thread#9" [stack: 0x000000a43ae00000,0x000000a43af00000] [id=27048]
  0x00000263605a2d90 GCTaskThread "GC Thread#10" [stack: 0x000000a43af00000,0x000000a43b000000] [id=22784]
  0x00000263607235d0 GCTaskThread "GC Thread#11" [stack: 0x000000a43b000000,0x000000a43b100000] [id=18888]
  0x0000026365aaeaa0 GCTaskThread "GC Thread#12" [stack: 0x000000a43b300000,0x000000a43b400000] [id=15136]
  0x0000026370a017d0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000a439100000,0x000000a439200000] [id=8084]
  0x0000026370a02980 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000a439200000,0x000000a439300000] [id=27360]
  0x00000263607245f0 ConcurrentGCThread "G1 Conc#1" [stack: 0x000000a43b100000,0x000000a43b200000] [id=21256]
  0x0000026360723880 ConcurrentGCThread "G1 Conc#2" [stack: 0x000000a43b200000,0x000000a43b300000] [id=15772]
  0x0000026370a1ea60 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000a439300000,0x000000a439400000] [id=27272]
  0x000002636539b600 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000a43b400000,0x000000a43b500000] [id=29388]
  0x000002636539a1e0 ConcurrentGCThread "G1 Refine#2" [stack: 0x000000a43b500000,0x000000a43b600000] [id=25556]
  0x000002637e2205e0 ConcurrentGCThread "G1 Service" [stack: 0x000000a439400000,0x000000a439500000] [id=14200]

Threads with active compile tasks:
C2 CompilerThread5     6703 3298   !   4       jdk.internal.loader.BuiltinClassLoader::findClassOnClassPathOrNull (64 bytes)
C2 CompilerThread6     6703 3300   !   4       java.util.concurrent.ConcurrentHashMap::computeIfAbsent (576 bytes)

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000002637093ee60] Threads_lock - owner thread: 0x000002637e2e8b30
[0x000002637093df30] Heap_lock - owner thread: 0x00000263709426f0

Heap address: 0x0000000604800000, size: 8120 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000002631f000000-0x000002631fbd0000-0x000002631fbd0000), size 12386304, SharedBaseAddress: 0x000002631f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000026320000000-0x0000026360000000, reserved size: 1073741824
Narrow klass base: 0x000002631f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32479M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8120M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 69632K, used 25168K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 36437K, committed 36672K, reserved 1114112K
  class space    used 4492K, committed 4608K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%|HS|  |TAMS 0x0000000604c00000, 0x0000000604800000| Complete 
|   1|0x0000000604c00000, 0x0000000604e10000, 0x0000000605000000| 51%| O|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   2|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605400000, 0x0000000605000000| Untracked 
|   3|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%| O|  |TAMS 0x0000000605800000, 0x0000000605400000| Untracked 
|   4|0x0000000605800000, 0x0000000605c00000, 0x0000000605c00000|100%| O|  |TAMS 0x0000000605c00000, 0x0000000605800000| Untracked 
|   5|0x0000000605c00000, 0x0000000606000000, 0x0000000606000000|100%| O|  |TAMS 0x0000000605d5e600, 0x0000000605c00000| Untracked 
|   6|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|   7|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|   8|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|   9|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  10|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  11|0x0000000607400000, 0x00000006076841f0, 0x0000000607800000| 62%| S|CS|TAMS 0x0000000607400000, 0x0000000607400000| Complete 
|  12|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  13|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
| 114|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 115|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000, 0x0000000621400000| Untracked 
| 126|0x0000000624000000, 0x0000000624000000, 0x0000000624400000|  0%| F|  |TAMS 0x0000000624000000, 0x0000000624000000| Untracked 

Card table byte_map: [0x0000026379a60000,0x000002637aa40000] _byte_map_base: 0x0000026376a3c000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000263709f10e0, (CMBitMap*) 0x00000263709f1120
 Prev Bits: [0x000002630f000000, 0x0000026316ee0000)
 Next Bits: [0x0000026316ee0000, 0x000002631edc0000)

Polling page: 0x000002636e8d0000

Metaspace:

Usage:
  Non-class:     31.20 MB used.
      Class:      4.39 MB used.
       Both:     35.58 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      31.31 MB ( 49%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.50 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      35.81 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  15.12 MB
       Class:  11.52 MB
        Both:  26.64 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 54.94 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 148.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 571.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 1359.
num_chunk_merges: 6.
num_chunk_splits: 1147.
num_chunks_enlarged: 1052.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=1790Kb max_used=1790Kb free=117377Kb
 bounds [0x0000026307ba0000, 0x0000026307e10000, 0x000002630f000000]
CodeHeap 'profiled nmethods': size=119104Kb used=7088Kb max_used=7088Kb free=112015Kb
 bounds [0x0000026300000000, 0x00000263006f0000, 0x0000026307450000]
CodeHeap 'non-nmethods': size=7488Kb used=2958Kb max_used=3085Kb free=4529Kb
 bounds [0x0000026307450000, 0x0000026307770000, 0x0000026307ba0000]
 total_blobs=3889 nmethods=3306 adapters=492
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 6.642 Thread 0x00000263609ee4b0 3302       3       org.jetbrains.kotlin.resolve.DescriptorUtils::getFqName (29 bytes)
Event: 6.643 Thread 0x00000263609ee4b0 nmethod 3302 0x00000263006ea190 code [0x00000263006ea360, 0x00000263006ea658]
Event: 6.652 Thread 0x00000263609ee9c0 3303       3       java.util.Arrays$ArrayList::size (6 bytes)
Event: 6.652 Thread 0x00000263609ee9c0 nmethod 3303 0x00000263006ea790 code [0x00000263006ea920, 0x00000263006eaa38]
Event: 6.652 Thread 0x00000263609ee4b0 3304       1       org.jetbrains.kotlin.resolve.calls.model.MutableResolvedCallAtom::getAtom (5 bytes)
Event: 6.653 Thread 0x00000263609ee4b0 nmethod 3304 0x0000026307d5ec90 code [0x0000026307d5ee20, 0x0000026307d5eef8]
Event: 6.653 Thread 0x00000263661388e0 3305       4       java.util.AbstractSet::<init> (5 bytes)
Event: 6.653 Thread 0x00000263661388e0 nmethod 3305 0x0000026307d5ef90 code [0x0000026307d5f100, 0x0000026307d5f178]
Event: 6.653 Thread 0x000002637e313550 3306       1       org.jetbrains.kotlin.types.DelegatingSimpleTypeImpl::getDelegate (5 bytes)
Event: 6.653 Thread 0x000002637e313550 nmethod 3306 0x0000026307d5f290 code [0x0000026307d5f420, 0x0000026307d5f4f8]
Event: 6.653 Thread 0x00000263609ed070 3307       1       org.jetbrains.kotlin.resolve.calls.tower.PSIKotlinCallImpl::getCallKind (5 bytes)
Event: 6.653 Thread 0x00000263609ee4b0 3308       3       java.lang.Enum::compareTo (44 bytes)
Event: 6.653 Thread 0x00000263609ed070 nmethod 3307 0x0000026307d5f590 code [0x0000026307d5f720, 0x0000026307d5f7f8]
Event: 6.654 Thread 0x00000263609ee4b0 nmethod 3308 0x00000263006eab10 code [0x00000263006ead00, 0x00000263006eb228]
Event: 6.654 Thread 0x00000263609ee4b0 3309       1       org.jetbrains.kotlin.resolve.calls.tower.PSICallResolver$ASTScopeTower::getLocation (5 bytes)
Event: 6.654 Thread 0x00000263609ee4b0 nmethod 3309 0x0000026307d5f890 code [0x0000026307d5fa20, 0x0000026307d5faf8]
Event: 6.654 Thread 0x00000263609ee4b0 3310       3       org.jetbrains.kotlin.resolve.calls.model.ResolutionPart::workCount (8 bytes)
Event: 6.654 Thread 0x000002637e313550 3311       3       kotlin.ranges.IntProgressionIterator::nextInt (51 bytes)
Event: 6.654 Thread 0x000002637e313550 nmethod 3311 0x00000263006eb410 code [0x00000263006eb5c0, 0x00000263006eb868]
Event: 6.654 Thread 0x00000263609ee4b0 nmethod 3310 0x00000263006eb990 code [0x00000263006ebba0, 0x00000263006ec138]

GC Heap History (15 events):
Event: 0.513 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 520192K, used 24576K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 6766K, committed 6912K, reserved 1114112K
  class space    used 700K, committed 768K, reserved 1048576K
}
Event: 0.515 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 7084K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 6766K, committed 6912K, reserved 1114112K
  class space    used 700K, committed 768K, reserved 1048576K
}
Event: 1.548 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 35756K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 15264K, committed 15488K, reserved 1114112K
  class space    used 2018K, committed 2112K, reserved 1048576K
}
Event: 1.551 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 8850K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 15264K, committed 15488K, reserved 1114112K
  class space    used 2018K, committed 2112K, reserved 1048576K
}
Event: 4.210 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 45714K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 21372K, committed 21504K, reserved 1114112K
  class space    used 2889K, committed 2944K, reserved 1048576K
}
Event: 4.213 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 520192K, used 14645K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 21372K, committed 21504K, reserved 1114112K
  class space    used 2889K, committed 2944K, reserved 1048576K
}
Event: 4.739 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 69632K, used 47413K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 10 young (40960K), 2 survivors (8192K)
 Metaspace       used 27345K, committed 27520K, reserved 1114112K
  class space    used 3460K, committed 3520K, reserved 1048576K
}
Event: 4.744 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 69632K, used 17856K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 27345K, committed 27520K, reserved 1114112K
  class space    used 3460K, committed 3520K, reserved 1048576K
}
Event: 4.836 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 69632K, used 21952K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 28806K, committed 29056K, reserved 1114112K
  class space    used 3620K, committed 3712K, reserved 1048576K
}
Event: 4.843 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 69632K, used 18496K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 28806K, committed 29056K, reserved 1114112K
  class space    used 3620K, committed 3712K, reserved 1048576K
}
Event: 5.429 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 69632K, used 38976K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 33421K, committed 33664K, reserved 1114112K
  class space    used 4158K, committed 4288K, reserved 1048576K
}
Event: 5.435 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 69632K, used 23747K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 33421K, committed 33664K, reserved 1114112K
  class space    used 4158K, committed 4288K, reserved 1048576K
}
Event: 6.024 GC heap before
{Heap before GC invocations=8 (full 0):
 garbage-first heap   total 69632K, used 48323K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 35655K, committed 35840K, reserved 1114112K
  class space    used 4417K, committed 4544K, reserved 1048576K
}
Event: 6.032 GC heap after
{Heap after GC invocations=9 (full 0):
 garbage-first heap   total 69632K, used 24156K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 35655K, committed 35840K, reserved 1114112K
  class space    used 4417K, committed 4544K, reserved 1048576K
}
Event: 6.665 GC heap before
{Heap before GC invocations=9 (full 0):
 garbage-first heap   total 69632K, used 44636K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 1 survivors (4096K)
 Metaspace       used 36437K, committed 36672K, reserved 1114112K
  class space    used 4492K, committed 4608K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 5.814 Thread 0x00000263709426f0 DEOPT PACKING pc=0x0000026307cc17d4 sp=0x000000a438ffb4a0
Event: 5.814 Thread 0x00000263709426f0 DEOPT UNPACKING pc=0x00000263074a23a3 sp=0x000000a438ffb488 mode 2
Event: 6.291 Thread 0x00000263709426f0 DEOPT PACKING pc=0x00000263006644e3 sp=0x000000a438ffbdf0
Event: 6.291 Thread 0x00000263709426f0 DEOPT UNPACKING pc=0x00000263074a2b43 sp=0x000000a438ffb2f8 mode 0
Event: 6.322 Thread 0x00000263709426f0 DEOPT PACKING pc=0x00000263006644e3 sp=0x000000a438ffbdf0
Event: 6.322 Thread 0x00000263709426f0 DEOPT UNPACKING pc=0x00000263074a2b43 sp=0x000000a438ffb2f8 mode 0
Event: 6.325 Thread 0x00000263709426f0 DEOPT PACKING pc=0x00000263006644e3 sp=0x000000a438ffbdf0
Event: 6.325 Thread 0x00000263709426f0 DEOPT UNPACKING pc=0x00000263074a2b43 sp=0x000000a438ffb2f8 mode 0
Event: 6.568 Thread 0x00000263709426f0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000026307cfd068 relative=0x0000000000003028
Event: 6.568 Thread 0x00000263709426f0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000026307cfd068 method=org.jetbrains.org.objectweb.asm.ClassReader.readMethod(Lorg/jetbrains/org/objectweb/asm/ClassVisitor;Lorg/jetbrains/org/objectweb/asm/Context;I)I @
Event: 6.569 Thread 0x00000263709426f0 DEOPT PACKING pc=0x0000026307cfd068 sp=0x000000a438ffb6c0
Event: 6.569 Thread 0x00000263709426f0 DEOPT UNPACKING pc=0x00000263074a23a3 sp=0x000000a438ffb630 mode 2
Event: 6.660 Thread 0x00000263709426f0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000026307cf2198 relative=0x0000000000000598
Event: 6.660 Thread 0x00000263709426f0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000026307cf2198 method=gnu.trove.TObjectHash.insertionIndex(Ljava/lang/Object;)I @ 30 c2
Event: 6.660 Thread 0x00000263709426f0 DEOPT PACKING pc=0x0000026307cf2198 sp=0x000000a438ffcb80
Event: 6.660 Thread 0x00000263709426f0 DEOPT UNPACKING pc=0x00000263074a23a3 sp=0x000000a438ffcaf0 mode 2
Event: 6.660 Thread 0x00000263709426f0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000026307cf2198 relative=0x0000000000000598
Event: 6.660 Thread 0x00000263709426f0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000026307cf2198 method=gnu.trove.TObjectHash.insertionIndex(Ljava/lang/Object;)I @ 30 c2
Event: 6.660 Thread 0x00000263709426f0 DEOPT PACKING pc=0x0000026307cf2198 sp=0x000000a438ffcb80
Event: 6.660 Thread 0x00000263709426f0 DEOPT UNPACKING pc=0x00000263074a23a3 sp=0x000000a438ffcaf0 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.595 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d6d660}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int)'> (0x0000000623d6d660) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.606 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623ea45e0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623ea45e0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.609 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623ecd690}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623ecd690) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.620 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623fac470}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623fac470) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.669 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623ad0488}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623ad0488) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.704 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623696848}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623696848) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.758 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623352b20}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000623352b20) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.764 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233ccd30}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006233ccd30) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.776 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c886b0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c886b0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 3.751 Thread 0x00000263709426f0 Implicit null exception at 0x0000026307bc6ef2 to 0x0000026307bc754c
Event: 3.894 Thread 0x00000263709426f0 Implicit null exception at 0x0000026307bca272 to 0x0000026307bca8ec
Event: 3.985 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000621d35b80}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000621d35b80) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.823 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006243b43e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x00000006243b43e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.865 Thread 0x00000263709426f0 Implicit null exception at 0x0000026307c96cd0 to 0x0000026307c96e08
Event: 4.893 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006243010a0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006243010a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.893 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006243059c8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006243059c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.894 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000624309838}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000624309838) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 5.440 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062402a6a0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000062402a6a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 5.442 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000624033f90}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000624033f90) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 5.481 Thread 0x00000263709426f0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062418ff40}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062418ff40) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 5.216 Executing VM operation: HandshakeAllThreads done
Event: 5.348 Executing VM operation: ICBufferFull
Event: 5.348 Executing VM operation: ICBufferFull done
Event: 5.429 Executing VM operation: G1CollectForAllocation
Event: 5.436 Executing VM operation: G1CollectForAllocation done
Event: 5.447 Executing VM operation: G1Concurrent
Event: 5.452 Executing VM operation: G1Concurrent done
Event: 5.456 Executing VM operation: G1Concurrent
Event: 5.456 Executing VM operation: G1Concurrent done
Event: 5.519 Executing VM operation: HandshakeAllThreads
Event: 5.519 Executing VM operation: HandshakeAllThreads done
Event: 5.679 Executing VM operation: HandshakeAllThreads
Event: 5.679 Executing VM operation: HandshakeAllThreads done
Event: 5.906 Executing VM operation: G1CollectForAllocation
Event: 6.033 Executing VM operation: G1CollectForAllocation done
Event: 6.570 Executing VM operation: HandshakeAllThreads
Event: 6.570 Executing VM operation: HandshakeAllThreads done
Event: 6.621 Executing VM operation: HandshakeAllThreads
Event: 6.621 Executing VM operation: HandshakeAllThreads done
Event: 6.665 Executing VM operation: G1CollectForAllocation

Events (20 events):
Event: 5.108 loading class java/util/ArrayList$SubList$1 done
Event: 5.127 loading class java/util/stream/IntPipeline$1
Event: 5.127 loading class java/util/stream/IntPipeline$1 done
Event: 5.131 loading class java/util/stream/IntPipeline$1$1
Event: 5.131 loading class java/util/stream/IntPipeline$1$1 done
Event: 5.222 loading class java/text/StringCharacterIterator
Event: 5.222 loading class java/text/CharacterIterator
Event: 5.222 loading class java/text/CharacterIterator done
Event: 5.222 loading class java/text/StringCharacterIterator done
Event: 5.443 loading class java/util/RegularEnumSet$EnumSetIterator
Event: 5.443 loading class java/util/RegularEnumSet$EnumSetIterator done
Event: 5.632 Thread 0x00000263609eda90 Thread exited: 0x00000263609eda90
Event: 5.665 Thread 0x000002636044d440 Thread exited: 0x000002636044d440
Event: 5.858 Thread 0x00000263661388e0 Thread added: 0x00000263661388e0
Event: 5.874 Thread 0x0000026366136a80 Thread added: 0x0000026366136a80
Event: 5.878 Thread 0x0000026366136570 Thread added: 0x0000026366136570
Event: 5.879 Thread 0x0000026366136060 Thread added: 0x0000026366136060
Event: 5.879 Thread 0x000002636613d9e0 Thread added: 0x000002636613d9e0
Event: 5.886 Thread 0x0000026366139300 Thread added: 0x0000026366139300
Event: 5.894 Thread 0x0000026366139810 Thread added: 0x0000026366139810


Dynamic libraries:
0x00007ff708300000 - 0x00007ff708310000 	E:\JAVA\bin\java.exe
0x00007ff827030000 - 0x00007ff827228000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8255d0000 - 0x00007ff825692000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff824d50000 - 0x00007ff825046000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8246c0000 - 0x00007ff8247c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff81fde0000 - 0x00007ff81fdf9000 	E:\JAVA\bin\jli.dll
0x00007ff81fdc0000 - 0x00007ff81fddb000 	E:\JAVA\bin\VCRUNTIME140.dll
0x00007ff826e80000 - 0x00007ff826f31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff826340000 - 0x00007ff8263de000 	C:\Windows\System32\msvcrt.dll
0x00007ff825e80000 - 0x00007ff825f1f000 	C:\Windows\System32\sechost.dll
0x00007ff825b10000 - 0x00007ff825c33000 	C:\Windows\System32\RPCRT4.dll
0x00007ff824a20000 - 0x00007ff824a47000 	C:\Windows\System32\bcrypt.dll
0x00007ff825050000 - 0x00007ff8251ed000 	C:\Windows\System32\USER32.dll
0x00007ff81dfe0000 - 0x00007ff81e27a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8248a0000 - 0x00007ff8248c2000 	C:\Windows\System32\win32u.dll
0x00007ff825da0000 - 0x00007ff825dcb000 	C:\Windows\System32\GDI32.dll
0x00007ff824a50000 - 0x00007ff824b69000 	C:\Windows\System32\gdi32full.dll
0x00007ff824980000 - 0x00007ff824a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff823400000 - 0x00007ff82340a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8253f0000 - 0x00007ff82541f000 	C:\Windows\System32\IMM32.DLL
0x00007ff81fd50000 - 0x00007ff81fd5c000 	E:\JAVA\bin\vcruntime140_1.dll
0x00007ffffb0c0000 - 0x00007ffffb14e000 	E:\JAVA\bin\msvcp140.dll
0x00007fffe0350000 - 0x00007fffe0f2e000 	E:\JAVA\bin\server\jvm.dll
0x00007ff826f40000 - 0x00007ff826f48000 	C:\Windows\System32\PSAPI.DLL
0x00007fffeb340000 - 0x00007fffeb349000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff81e3c0000 - 0x00007ff81e3e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff826e10000 - 0x00007ff826e7b000 	C:\Windows\System32\WS2_32.dll
0x00007ff823360000 - 0x00007ff823372000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff81c4f0000 - 0x00007ff81c4fa000 	E:\JAVA\bin\jimage.dll
0x00007ff821e10000 - 0x00007ff822011000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff80fef0000 - 0x00007ff80ff24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff824810000 - 0x00007ff824892000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff818d20000 - 0x00007ff818d45000 	E:\JAVA\bin\java.dll
0x00007fffef390000 - 0x00007fffef467000 	E:\JAVA\bin\jsvml.dll
0x00007ff826570000 - 0x00007ff826cde000 	C:\Windows\System32\SHELL32.dll
0x00007ff8222d0000 - 0x00007ff822a74000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff825fd0000 - 0x00007ff826323000 	C:\Windows\System32\combase.dll
0x00007ff8240f0000 - 0x00007ff82411b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff826d40000 - 0x00007ff826e0d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff825f20000 - 0x00007ff825fcd000 	C:\Windows\System32\SHCORE.dll
0x00007ff825d40000 - 0x00007ff825d9b000 	C:\Windows\System32\shlwapi.dll
0x00007ff8245f0000 - 0x00007ff824614000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff811d70000 - 0x00007ff811d89000 	E:\JAVA\bin\net.dll
0x00007ff81ede0000 - 0x00007ff81eeea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff823e50000 - 0x00007ff823eba000 	C:\Windows\system32\mswsock.dll
0x00007ff810fb0000 - 0x00007ff810fc6000 	E:\JAVA\bin\nio.dll
0x00007ff81bc20000 - 0x00007ff81bc38000 	E:\JAVA\bin\zip.dll
0x00007ff81c4d0000 - 0x00007ff81c4e0000 	E:\JAVA\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\JAVA\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;E:\JAVA\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\Questionnaire\build\20250901_6990565302930332843.compiler.options
java_class_path (initial): E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.22\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\kotlin-compiler-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.22\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\kotlin-script-runtime-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.22\20e2c5df715f3240c765cfc222530e2796542021\kotlin-daemon-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8514437120                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8514437120                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\JAVA
CLASSPATH=.;E:\JAVA\lib\dt.jar;E:\JAVA\lib\tools.jar;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\dotnet\;E:\git\Git\cmd;D:\Users\W9096060\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\dotnet\;E:\1.8java\lib\dt.jar;E:\1.8java\lib\tools.jar;E:\1.8java\bin;E:\JAVA\bin;E:\JAVA\lib;D:\Users\W9096060\AppData\Local\Microsoft\WindowsApps;
USERNAME=W9096060
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 18:50 hours

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 32479M (2656M free)
TotalPageFile size 47827M (AvailPageFile size 112M)
current process WorkingSet (physical memory assigned to process): 227M, peak: 240M
current process commit charge ("private bytes"): 282M, peak: 678M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211) for windows-amd64 JRE (17.0.8+9-LTS-211), built on Jun 14 2023 10:34:31 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
