/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemoteThumbnailFactory.kt
 ** Description: RemoteThumbnailFactory
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.oplus.filemanager.category.remotedevice.glide.thumbnail

import android.content.Context
import android.graphics.Bitmap
import androidx.annotation.Keep
import com.bumptech.glide.load.model.ModelLoader
import com.bumptech.glide.load.model.ModelLoaderFactory
import com.bumptech.glide.load.model.MultiModelLoaderFactory
import com.filemanager.common.base.RemoteFileBean

@Keep
class RemoteThumbnailFactory(context: Context) :
    ModelLoaderFactory<RemoteFileBean, Bitmap> {

    private val appContext = context.applicationContext

    override fun build(multiFactory: MultiModelLoaderFactory): ModelLoader<RemoteFileBean, Bitmap> {
        return RemoteThumbnailLoader(appContext)
    }

    override fun teardown() {
        //do nothing
    }
}
