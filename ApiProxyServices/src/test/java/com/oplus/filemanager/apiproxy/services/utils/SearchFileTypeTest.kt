package com.oplus.filemanager.apiproxy.services.utils

import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized
import org.junit.runners.Parameterized.Parameters
import java.util.Arrays

/**
 * SearchFileType 的单元测试类
 * 用于测试 SearchFileType 工具类的各种功能
 */
class SearchFileTypeTest {

    // 模拟 IDocumentExtensionType 接口
    private val mockExtensionType: IDocumentExtensionType = mockk()

    /**
     * 测试前的准备工作
     * 1. 模拟 Log 类的静态方法
     * 2. 模拟 Injector 的工厂方法
     */
    @Before
    fun setup() {
        // 模拟 Log 类的静态方法
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } returns Unit

        // 模拟 Injector 的工厂方法
        mockkObject(Injector)
        every { Injector.injectFactory<IDocumentExtensionType>() } returns mockExtensionType
    }

    /**
     * 测试后的清理工作
     * 解除所有模拟
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试 getDocAllSuffix 方法
     * 验证是否能正确返回所有文档后缀列表
     */
    @Test
    fun `getDocAllSuffix should return correct suffix list`() {
        // When 调用方法获取结果
        val result = SearchFileType.getDocAllSuffix()

        // Then 验证返回的后缀列表是否正确
        val expected = listOf(
            ".doc", ".xls", ".ppt", ".pdf", ".ofd", ".iwork", 
            ".xmind", ".visio", ".txt", ".cad", ".psd", ".ai", ".md"
        )
        assertEquals(expected, result)
    }

    /**
     * 测试 getFilterSuffixList 方法
     * 当类型为 ALL 时，应返回所有后缀列表
     */
    @Test
    fun `getFilterSuffixList should return all suffixes when type is ALL`() {
        // Given 设置模拟行为
        every { mockExtensionType.getAllSelectionArgs(any()) } returns arrayListOf(".doc", ".xls")

        // When 调用方法获取结果
        val result = SearchFileType.getFilterSuffixList(SearchFileType.TYPE_ALL_POSITION)

        // Then 验证返回结果和日志记录
        assertEquals(listOf(".doc", ".xls"), result)
        verify { 
            Log.d("SearchFileType", "getFilterSuffixList fileType:0 suffixList:[.doc, .xls, .ppt, .pdf, .ofd, .iwork, .xmind, .visio, .txt, .cad, .psd, .ai, .md]")
            Log.d("SearchFileType", "getFilterSuffixList fileType:0 list:[.doc, .xls]")
        }
    }

    /**
     * 测试 getFilterSuffixList 方法
     * 当类型为特定类型时，应返回单个后缀
     */
    @Test
    fun `getFilterSuffixList should return single suffix when type is specific`() {
        // Given 设置模拟行为
        every { mockExtensionType.getAllSelectionArgs(any()) } returns arrayListOf(".doc")

        // When 调用方法获取结果
        val result = SearchFileType.getFilterSuffixList(SearchFileType.TYPE_DOC_POSITION)

        // Then 验证返回结果和日志记录
        assertEquals(listOf(".doc"), result)
        verify { 
            Log.d("SearchFileType", "getFilterSuffixList fileType:1 suffixList:[.doc]")
            Log.d("SearchFileType", "getFilterSuffixList fileType:1 list:[.doc]")
        }
    }

    /**
     * 测试 getFilterSuffixList 方法
     * 当扩展类型为 null 时，应返回原始列表
     */
    @Test
    fun `getFilterSuffixList should return original list when extension type is null`() {
        // Given 设置模拟行为
        every { Injector.injectFactory<IDocumentExtensionType>() } returns null

        // When 调用方法获取结果
        val result = SearchFileType.getFilterSuffixList(SearchFileType.TYPE_PDF_POSITION)

        // Then 验证返回结果
        assertEquals(listOf(".pdf"), result)
        verify(exactly = 0) { mockExtensionType.getAllSelectionArgs(any()) }
    }

    /**
     * 测试 getDocSuffix 方法
     * 当 fileType 为 0 时，应抛出 IndexOutOfBoundsException
     */
    @Test(expected = IndexOutOfBoundsException::class)
    fun `getDocSuffix should throw IndexOutOfBounds for fileType 0`() {
        SearchFileType.getDocSuffix(0)
    }

    /**
     * 测试 getDocSuffix 方法
     * 当 fileType 为 14 时，应抛出 IndexOutOfBoundsException
     */
    @Test(expected = IndexOutOfBoundsException::class)
    fun `getDocSuffix should throw IndexOutOfBounds for fileType 14`() {
        SearchFileType.getDocSuffix(14)
    }

    /**
     * 参数化测试类 - 测试 getDocSuffix 方法
     * 用于测试各种有效的 fileType 输入
     */
    @RunWith(Parameterized::class)
    class GetDocSuffixTest(private val fileType: Int, private val expectedSuffix: String) {

        /**
         * 测试前的准备工作
         * 模拟 Log 类的静态方法
         */
        @Before
        fun setup() {
            mockkStatic(Log::class)
            every { Log.d(any(), any()) } returns Unit
        }

        /**
         * 测试后的清理工作
         * 解除所有模拟
         */
        @After
        fun tearDown() {
            unmockkAll()
        }

        /**
         * 测试 getDocSuffix 方法
         * 验证对于有效的 fileType 是否能返回正确的后缀
         */
        @Test
        fun `getDocSuffix should return correct suffix for valid fileType`() {
            // When 调用方法获取结果
            val result = SearchFileType.getDocSuffix(fileType)

            // Then 验证返回结果和日志记录
            assertEquals(expectedSuffix, result)
            verify { Log.d("SearchFileType", "getDocSuffix fileType:$fileType -> suffix:$expectedSuffix") }
        }

        companion object {
            /**
             * 提供测试数据
             * @return 返回 fileType 和预期后缀的集合
             */
            @JvmStatic
            @Parameters(name = "Valid fileType: {0} -> suffix: {1}")
            fun data(): Collection<Array<Any>> {
                return Arrays.asList(
                    arrayOf(1, ".doc"),
                    arrayOf(2, ".xls"),
                    arrayOf(3, ".ppt"),
                    arrayOf(4, ".pdf"),
                    arrayOf(5, ".ofd"),
                    arrayOf(6, ".iwork"),
                    arrayOf(7, ".xmind"),
                    arrayOf(8, ".visio"),
                    arrayOf(9, ".txt"),
                    arrayOf(10, ".cad"),
                    arrayOf(11, ".psd"),
                    arrayOf(12, ".ai"),
                    arrayOf(13, ".md")
                )
            }
        }
    }

    /**
     * 参数化测试类 - 测试 isSupportDmp 方法
     * 用于测试各种 fileType 是否支持 DMP
     */
    @RunWith(Parameterized::class)
    class IsSupportDmpTest(private val fileType: Int, private val expectedSupport: Boolean) {

        /**
         * 测试 isSupportDmp 方法
         * 验证对于给定的 fileType 是否能返回正确的支持状态
         */
        @Test
        fun `isSupportDmp should return correct support status`() {
            // When 调用方法获取结果
            val result = SearchFileType.isSupportDmp(fileType)

            // Then 验证返回结果
            assertEquals(expectedSupport, result)
        }

        companion object {
            /**
             * 提供测试数据
             * @return 返回 fileType 和预期支持状态的集合
             */
            @JvmStatic
            @Parameters(name = "Support DMP: fileType={0}, expected={1}")
            fun data(): Collection<Array<Any>> {
                return Arrays.asList(
                    arrayOf(0, true),
                    arrayOf(1, true),
                    arrayOf(2, true),
                    arrayOf(3, true),
                    arrayOf(4, false),
                    arrayOf(5, false),
                    arrayOf(6, false),
                    arrayOf(7, false),
                    arrayOf(8, false),
                    arrayOf(9, false),
                    arrayOf(10, false),
                    arrayOf(11, false),
                    arrayOf(12, false),
                    arrayOf(13, false)
                )
            }
        }
    }
}