/*********************************************************************
 * * Copyright (C), 2024, OPlus. All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/7/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.annotation.IdRes
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.core.view.doOnLayout
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseFolderAnimAdapter.Companion.FILE_BROWSER_FOLDER_ANIM_DELAY
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_DECORATION_FILE_BROWSER
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.interfaces.IPickerManager
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.OnPickerRecyclerItemClickListener
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.picker.IPickerFragment
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.HighlightUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PathUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.BrowserPathBar
import com.filemanager.common.view.FileManagerRecyclerView
import com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller
import com.filemanager.common.viewholder.listener.PickerNormalListTouchClickListener.Companion.CLICK_TO_SELECT
import com.filemanager.common.viewholder.listener.PickerNormalListTouchClickListener.Companion.CLICK_TO_SHOW
import com.filemanager.fileoperate.PikerFileOperateController
import com.oplus.filebrowser.adapter.PickerFileBrowserAdapter
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
open class PickerFileBrowserFragment : BaseVMFragment<FileBrowserViewModel>(), OnBackPressed, IPickerFragment,
    IFileOperate.OperateResultListener, OnPickerRecyclerItemClickListener {
    companion object {
        private const val TAG = "PickerFileBrowserFragment"
        private const val FILE_BROWSER_FOLDER_ANIM_TIME = 100L
        private const val LOADING_DELAY_TIME = 500L
        private const val NOTIFY_CHANGED_DELAY = 300L
    }
    internal var mPathBar: BrowserPathBar? = null
    private var fragmentRecyclerView: FileManagerRecyclerView? = null
    private var fragmentFastScroller: RecyclerViewFastScroller? = null
    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    internal var sortEntryView: SortEntryView? = null
    internal var mCurrentPath: String? = null
    private var mNeedLoadData = false
    internal var mTitle: String? = null
    private var isMultipleSelect = false
    private var mPickerMultipleType: Array<String>? = null
    private var mAdapter: PickerFileBrowserAdapter? = null
    internal var fragmentViewModel: FileBrowserViewModel? = null
    private var mLayoutManager: FileGridLayoutManager? = null
    private val mSpacesItemDecoration by lazy { ItemDecorationFactory.getGridItemDecoration(GRID_ITEM_DECORATION_FILE_BROWSER) }
    private val mFolderTransformAnimator by lazy { FolderTransformAnimator() }
    internal val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val mSortPopupController by lazy { SortPopupController(lifecycle) }
    private var mLoadingController: LoadingController? = null
    internal var pickerManager: IPickerManager? = null
    private val mFileOperateController by lazy {
        PikerFileOperateController(
            lifecycle,
            CategoryHelper.CATEGORY_FILE_BROWSER,
            fragmentViewModel!!,
            SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getPickerBrowserKey(mCurrentPath))
        ).also {
            it.setResultListener(this)
        }
    }
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }
    internal var isFromShortcutFolder = false
    private var hasShowEmpty: Boolean = false
    private var isFromDetail = false
    override fun getLayoutResId(): Int {
        return R.layout.picker_filebrowser_fragment
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            val bundle = arguments ?: return
            pickerManager = baseVMActivity as? IPickerManager
            mCurrentPath = bundle.getString(KtConstants.P_CURRENT_PATH)
            mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
            isMultipleSelect = bundle.getBoolean(KtConstants.P_MULTIPLE_SELECT, false)
            mPickerMultipleType = bundle.getStringArray(KtConstants.FILE_MIME_TYPE_MULTIPLE)
            mTitle = bundle.getString(KtConstants.P_TITLE)
            mAdapter = PickerFileBrowserAdapter(it, <EMAIL>, isMultipleSelect, mPickerMultipleType)
            mAdapter?.setHasStableIds(true)
            mAdapter?.isCreateOrAuthorize = pickerManager?.isCreateOrAuthorize() ?: false
            isFromShortcutFolder = bundle.getBoolean(KtConstants.FROM_SHORTCUT_FOLDER)
            isFromDetail = bundle.getBoolean(KtConstants.FROM_DETAIL)
        }
    }

    override fun initView(view: View) {
        rootView = view.findViewById(R.id.coordinator_layout)
        mPathBar = view.findViewById(com.oplus.selectdir.R.id.path_bar)
        fragmentFastScroller = view.findViewById(R.id.fastScroller)
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        mGridSpanAnimationHelper = fragmentRecyclerView?.let { GridSpanAnimationHelper(it) }
        initToolbar(view)
        sortEntryView = view.findViewById(com.filemanager.common.R.id.sort_entry_view)
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getPickerBrowserKey(mCurrentPath))
        sortEntryView?.setClickSortListener {
            performClickMenuEvent(R.id.navigation_sort)
        }
    }

    private fun initToolbar(view: View) {
        toolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        appBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        toolbar?.apply {
            title = mTitle
            titleMarginStart = 0
            isTitleCenterStyle = false
            val pickerAction = pickerManager?.getPickerAction()
            if (pickerAction == Intent.ACTION_OPEN_DOCUMENT_TREE ||
                pickerAction == Intent.ACTION_CREATE_DOCUMENT) {
                inflateMenu(R.menu.picker_open_file_tree_menu)
            } else {
                inflateMenu(R.menu.picker_file_browser_menu)
            }
            setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
            setNavigationOnClickListener {
                baseVMActivity?.onBackPressed()
            }
            setOnMenuItemClickListener { menu ->
                onMenuItemSelected(menu)
            }
        }
    }

    @SuppressLint("RestrictedApi")
    private fun performClickMenuEvent(@IdRes menuId: Int) {
        val activity = baseVMActivity ?: return
        val menu = ActionMenuItem(activity, 0, menuId, 0, 0, "")
        onMenuItemSelected(menu)
    }

    override fun onMenuItemSelected(item: MenuItem): Boolean {
        if (null == item || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            android.R.id.home -> {
                baseVMActivity?.onBackPressed()
                true
            }

            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startPickerGlobalSearch(activity, CategoryHelper.CATEGORY_FILE_BROWSER)
                true
            }
            R.id.navigation_sort -> {
                if (fragmentViewModel?.dataLoadState?.value == OnLoaderListener.STATE_START) {
                    Log.d(TAG, "onMenuItemSelected navigation_sort mFileLoadState = STATE_START")
                } else {
                    baseVMActivity?.let {
                        StatisticsUtils.nearMeStatistics(it, StatisticsUtils.SEQUENCE_ACTION)
                        OptimizeStatisticsUtil.pageSort(OptimizeStatisticsUtil.ALL_STORAGE)
                        val anchorView: View? = view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
                        mSortPopupController.showSortPopUp(
                            it,
                            -1,
                            anchorView,
                            SortRecordModeFactory.getPickerBrowserKey(mCurrentPath),
                            object : SelectItemListener {

                                override fun onDismiss() {
                                    sortEntryView?.rotateArrow()
                                }

                                override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                                    if (flag) {
                                        sortEntryView?.setSortOrder(sortMode, isDesc)
                                        fragmentViewModel?.sortReload()
                                    }
                                }
                            },
                            isPickerPage = true)
                    }
                }
                true
            }
            R.id.navigation_new_folder -> {
                if (actionCheckPermission().not()) {
                    baseVMActivity?.showSettingGuildDialog()
                    return true
                }
                fragmentViewModel?.mPositionModel?.value?.mCurrentPath?.also {
                    baseVMActivity?.let {
                        val viewPosition = mLayoutManager?.findFirstVisibleItemPosition() ?: 0
                        val offset = mLayoutManager?.findViewByPosition(viewPosition)?.top ?: fragmentRecyclerView?.paddingTop ?: 0
                        fragmentViewModel?.pickerCreateNewFolder(it, null, viewPosition, offset)
                    }
                }
                true
            }
            R.id.actionbar_scan_mode -> {
                fragmentViewModel?.clickScanModeItem(baseVMActivity)
                true
            }
            else -> false
        }
    }

    override fun onActionDone(opType: Int, result: Boolean, data: Any?) {
        super.onActionDone(opType, result, data)
        when (opType) {
            IFileOperate.OP_CREATE_FOLDER -> onResumeLoadData()
        }
    }
    override fun initData(savedInstanceState: Bundle?) {
        createViewModel()
        fragmentViewModel?.isFromShortcutFolder = isFromShortcutFolder
        fragmentViewModel?.pickerSelectedKeyList = pickerManager?.getSelectedKeys()
        fragmentRecyclerView?.let { recyclerView ->
            mLayoutManager = FileGridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        val viewType = mAdapter?.getItemViewType(position)
                        val isSingleLine =
                            (viewType == BaseFileBean.TYPE_FILE_LIST_HEADER)
                                    || (viewType == BaseFileBean.TYPE_LABEL_FILE)
                        return if (isSingleLine) spanCount else 1
                    }
                }
            }
            recyclerView.addItemDecoration(mSpacesItemDecoration)
            recyclerView.isNestedScrollingEnabled = true
            recyclerView.clipToPadding = false
            recyclerView.layoutManager = mLayoutManager!!
            recyclerView.itemAnimator = mFolderTransformAnimator
            recyclerView.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }
            mAdapter?.let {
                recyclerView.adapter = it
                it.setOnPickerRecyclerItemClickListener(this@PickerFileBrowserFragment)
            }
            appBarLayout?.doOnLayout {
                if (isAdded) {
                    fragmentFastScroller?.apply {
                        setPadding(paddingLeft, KtViewUtils.getRecyclerViewTopPadding(appBarLayout), paddingRight, paddingBottom)
                    }
                }
            }
            recyclerView.setLoadStateForScroll(this)
        }
        initPathBar()
        if (mNeedLoadData) {
            onResumeLoadData()
        }
        if (actionCheckPermission().not()) {
            sortEntryView?.setFileCount(0)
        }
        fragmentViewModel?.isFromDetail = isFromDetail
    }
    open fun createViewModel() {
        if (fragmentViewModel == null) {
            fragmentViewModel = ViewModelProvider(this)[FileBrowserViewModel::class.java].apply {
                isPickerMode = true
                mPickerMultipleType = pickerManager?.getFinalPickerMimeType()
            }
        }
    }
    open fun initPathBar() {
        mPathBar?.let {
            Log.d(TAG, "initPathBar mCurrentPath = $mCurrentPath")
            val currentPath = mCurrentPath ?: return
            if (currentPath.isNotEmpty()) {
                fragmentViewModel?.initPathHelper(currentPath)
                it.setPathHelper(fragmentViewModel?.mPathHelp)
                it.setOnPathClickListener(object : BrowserPathBar.OnPathClickListener {
                    override fun onPathClick(index: Int, path: String?) {
                        fragmentViewModel?.clickPathBar(index)
                    }
                }).setTextFocusChangeListener { currentFocusText ->
                    mTitle = currentFocusText
                    if (fragmentViewModel?.mNeedScroll == true) {
                        KtAnimationUtil.showUpdateToolbarTitleWithAnimate(toolbar, mTitle)
                    } else {
                        toolbar?.title = mTitle
                    }
                }.show()
                it.setIsFromShortcutFolder(isFromShortcutFolder)
                fragmentViewModel?.checkShortcutFolderRootPath(currentPath)?.let { fRootPAth ->
                    it.updateShortcutFolderRootPath(fRootPAth)
                }
                it.setCurrentPath(currentPath)
            }
        }
    }

    override fun startObserve() {
        fragmentRecyclerView?.post {
            val viewModule = fragmentViewModel ?: return@post
            if (!isAdded) {
                return@post
            }
            viewModule.uiState.observe(this) { fileUiModel ->
                onFileUiModelChanged(fileUiModel, viewModule)
            }
            viewModule.mPositionModel.observe(this) { positionModel ->
                onPositionModelChanged(viewModule, positionModel)
            }
            startScanModeObserver()
            startObserveLoadState()
            startCreateFolderObserve()
            startObservePickerSelected()
        }
    }

    private fun startObservePickerSelected() {
        pickerManager?.getSelectedLiveData()?.observe(this) {
            fragmentViewModel?.apply {
                pickerSelectedKeyList = pickerManager?.getSelectedKeys()
                val selectKeys = pickerSelectedKeyList?.filter {
                    uiState?.value?.keyMap?.get(it) != null
                }
                uiState?.value?.selectedList?.let {
                    it.clear()
                    it.addAll(ArrayList(selectKeys))
                }
                Log.d(TAG, "startObservePickerSelected selectKeys.size = ${selectKeys?.size}")
                uiState.value?.apply {
                    uiState.value = this
                }
            }
        }
    }

    private fun startCreateFolderObserve() {
        val viewModule = fragmentViewModel ?: return
        viewModule.createFolderPosition.observe(this) {
            if (!viewModule.mModeState.initState || it == -1) {
                return@observe
            }
            if (fragmentRecyclerView?.scrollState != RecyclerView.SCROLL_STATE_IDLE) {
                fragmentRecyclerView?.stopScroll()
            }
            Log.d(TAG, "startCreateFolderObserve position:$it, current scroll state:${fragmentRecyclerView?.scrollState}")
            val offset = KtViewUtils.getRecyclerViewTopOffset()
            mLayoutManager?.scrollToPositionWithOffset(it, -offset)
            lifecycleScope.launch(Dispatchers.Default) {
                kotlinx.coroutines.delay(NOTIFY_CHANGED_DELAY)
                withContext(Dispatchers.Main) {
                    HighlightUtil.highlightPosition = it
                    mAdapter?.notifyItemChanged(it, 0)
                    viewModule.createFolderPosition.value = -1
                }
            }
        }
    }

    private fun onPositionModelChanged(
        viewModule: FileBrowserViewModel,
        positionModel: FileBrowserViewModel.PositionModel
    ) {
        if (!viewModule.mModeState.initState) {
            return
        }
        HighlightUtil.cancelAnimation()
        mPathBar?.let {
            if (it.getCurrentPath() != positionModel.mCurrentPath) {
                it.setCurrentPath(positionModel.mCurrentPath)
            }
        }
        pickerManager?.updateFileBrowserPath(positionModel.mCurrentPath)
        appBarLayout?.postDelayed({
            if (positionModel.mOffset != 0) {
                Log.d(TAG, "itemAnimator set null")
                fragmentRecyclerView?.itemAnimator = null
            } else {
                if (fragmentRecyclerView?.itemAnimator == null) {
                    Log.d(TAG, "itemAnimator set mFolderTransformAnimator")
                    fragmentRecyclerView?.itemAnimator = mFolderTransformAnimator
                }
            }
            mLayoutManager?.scrollToPositionWithOffset(
                positionModel.mPosition,
                positionModel.mOffset
            )
            viewModule.mPositionModel.value?.mPosition = 0
            viewModule.mPositionModel.value?.mOffset = 0
            viewModule.mNeedScroll = false
        }, FILE_BROWSER_FOLDER_ANIM_DELAY)
    }

    private fun startObserveLoadState() {
        activity?.let {
            val bgColor = context?.resources?.getColor(com.support.appcompat.R.color.coui_color_background_with_card)
            mLoadingController = LoadingController(it, this).apply {
                observe(fragmentViewModel?.dataLoadState, rootView) {
                    val intercept = fragmentViewModel?.mFileBrowserCallBack?.isLoadNewPath()?.not() ?: false
                    intercept
                }
                bgColor?.let {
                    setBackgroundColor(it)
                }
                setDeleyShowTime(LOADING_DELAY_TIME)
                setShowAinimate(true)
                setDissapearAnimate(true)
                setShowLoadingTips(false)
                //这里将LoadingController和FolderTransformAnimator通过接口方式关联起来
                mFolderTransformAnimator.registerNeedSkipAnimator(this)
            }
        }
    }

    private fun onFileUiModelChanged(
        fileUiModel: FileBrowserViewModel.FileBrowserUiModel,
        viewModule: FileBrowserViewModel
    ) {
        Log.d(TAG, "UiModel mUiState =" + fileUiModel.fileList.size + ","
                + fileUiModel.selectedList.size + "," + fileUiModel.keyWord)
        sortEntryView?.setFileCount(fragmentViewModel?.getRealFileSize() ?: 0)
        HighlightUtil.cancelAnimation()
        if (fileUiModel.fileList.isEmpty()) {
            showEmptyView()
        } else {
            hideEmptyView()
        }
        toolbar?.let {
            refreshScanModeItemIcon(it)
        }
        if (fileUiModel.fileList !is ArrayList<BaseFileBean>) {
            return
        }
        val adapter = mAdapter ?: return
        mFolderTransformAnimator.mIsFolderInAnimation = viewModule.mIsFolderIn
        (fileUiModel.fileList as? ArrayList<BaseFileBean>)?.let { list ->
            adapter.setData(list, fileUiModel.selectedList, viewModule.mNeedScroll)
        }
    }

    private fun refreshScanModeItemIcon(toolbar: COUIToolbar, needSkipAnimation: Boolean = true) {
        toolbar.menu.findItem(R.id.actionbar_scan_mode)?.let {
            val desc: String
            val resId: Int = if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                desc = appContext.getString(com.filemanager.common.R.string.palace_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
            } else {
                desc = appContext.getString(com.filemanager.common.R.string.list_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
            }
            it.contentDescription = desc
            it.setIcon(resId)
        }
    }

    private fun showEmptyView() {
        if ((baseVMActivity != null) && (rootView != null)) {
            fragmentViewModel?.mPositionModel?.value?.mCurrentPath?.apply {
                mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!)
                mFileEmptyController.setFileEmptyTitle(com.filemanager.common.R.string.empty_file)
                sortEntryView?.visibility = View.INVISIBLE
                fragmentRecyclerView?.visibility = View.INVISIBLE
                hasShowEmpty = true
                Log.d(TAG, "showEmptyView")
            }
        }
    }

    private fun hideEmptyView() {
        mFileEmptyController.hideFileEmptyView()
        sortEntryView?.visibility = View.VISIBLE
        fragmentRecyclerView?.visibility = View.VISIBLE
    }

    override fun onResumeLoadData() {
        if (!isAdded) {
            return
        }
        if (checkShowPermissionEmpty(false)) {
            sortEntryView?.setFileCount(0)
            mPathBar?.visibility = View.GONE
            return
        }
        mPathBar?.visibility = View.VISIBLE
        val bundle = arguments ?: return
        val currentPath = mCurrentPath
        val oldIsFromShortcutFolder = isFromShortcutFolder
        isFromShortcutFolder = bundle.getBoolean(KtConstants.FROM_SHORTCUT_FOLDER)
        if (currentPath.isNullOrBlank()) {
            Log.w(TAG, "onResumeLoadData mCurrentPath is null or empty")
            if ((baseVMActivity != null) && (rootView != null)) {
                mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!)
            }
        } else {
            val lastPath = fragmentViewModel?.mPositionModel?.value?.mCurrentPath
            Log.d(TAG, "onResumeLoadData isFromShortcutFolder=$isFromShortcutFolder current:$currentPath lastPath:$lastPath")
            mCurrentPath = lastPath ?: currentPath
            mCurrentPath?.let { mCurrentPath ->
                mPathBar?.let {
                    if (mCurrentPath != it.getCurrentPath()) {
                        it.setCurrentPath(mCurrentPath)
                    }
                }
                pickerManager?.updateFileBrowserPath(mCurrentPath)
            }
            if (oldIsFromShortcutFolder && !isFromShortcutFolder) {
                Log.d(TAG, "change exit shortcut mode")
                resetIsFromShortcut()
                setCurrentFromOtherSide(mCurrentPath!!)
            }
            fragmentViewModel?.initLoader(LoaderViewModel.getLoaderController(this), mCurrentPath!!)
        }
        fragmentViewModel?.loadFolderLogo()
    }

    private fun startScanModeObserver() {
        mNeedSkipAnimation = true
        fragmentViewModel?.mBrowseModeState?.observe(this) { scanMode ->
            toolbar?.let {
                val needSkipAnimation = mNeedSkipAnimation
                if (needSkipAnimation) {
                    refreshScanModeAdapter(scanMode)
                } else {
                    fragmentRecyclerView?.let { recyclerView ->
                        recyclerView.mTouchable = false
                        recyclerView.stopScroll()
                    }
                    mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                        override fun onSpanChangeCallback() {
                            mLayoutManager?.scrollToPosition(0)
                            refreshScanModeAdapter(scanMode)
                        }
                    }, object : OnAnimatorEndListener {
                        override fun onAnimatorEnd() {
                            fragmentRecyclerView?.mTouchable = true
                        }
                    })
                }
                delay { refreshScanModeItemIcon(it, needSkipAnimation) }
            }
        }
    }

    private fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getGridItemCount(activity, scanMode,
            GRID_ITEM_DECORATION_FILE_BROWSER)
        mLayoutManager?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mAdapter?.apply {
            mScanViewModel = scanMode
            mFolderTransformAnimator.mSkipAddRemoveAnimation = true
            checkComputingAndExecute {
                notifyDataSetChanged()
            }
        }
    }

    fun resetIsFromShortcut() {
        arguments?.putBoolean(KtConstants.FROM_SHORTCUT_FOLDER, false)
        isFromShortcutFolder = false
        fragmentViewModel?.isFromShortcutFolder = false
        mPathBar?.setIsFromShortcutFolder(false)
        mCurrentPath?.let {
            mPathBar?.setCurrentPath(it, true)
        }
    }

    fun setCurrentFromOtherSide(currentPath: String) {
        mCurrentPath = currentPath
        val shortcutRootPath = fragmentViewModel?.checkShortcutFolderRootPath(currentPath)
        fragmentViewModel?.mPathHelp?.updateRootPath(currentPath, shortcutRootPath)
        fragmentViewModel?.setCurrentFromOtherSide(currentPath)
    }

    override fun pressBack(): Boolean {
        val activity = activity ?: return false
        val result = fragmentViewModel?.pressBack() ?: false
        return result
    }

    override fun onItemClick(view: View, position: Int, type: Int) {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if ((position < 0) or (position >= (uiModel.mFileList?.size ?: 0))) {
                Log.d(TAG, "onItemClick: position IndexOutOfBounds")
                return
            }
            val baseFile: BaseFileBean = uiModel.mFileList?.get(position) ?: return
            if (baseFile.mIsDirectory) {
                fragmentRecyclerView?.paddingTop?.let { paddingTop ->
                    val viewPosition = mLayoutManager?.findFirstVisibleItemPosition() ?: 0
                    val offset = mLayoutManager?.findViewByPosition(viewPosition)?.top ?: paddingTop
                    fragmentViewModel?.onDirClick(
                        baseVMActivity,
                        baseFile,
                        viewPosition,
                        offset - paddingTop
                    )
                }
            } else {
                if (pickerManager?.isCreateOrAuthorize() == true) {
                    //do nothing
                } else {
                    when (type) {
                        CLICK_TO_SELECT -> clickToSelect(baseFile)
                        CLICK_TO_SHOW -> clickToShow(baseFile)
                        else -> { }
                    }
                }
            }
        }
    }

    override fun onItemLongClick(view: View, position: Int) {}

    private fun clickToShow(baseFile: BaseFileBean) {
        //实现文件浏览逻辑
        activity?.let {
            mFileOperateController?.onFileClick(it, baseFile, null, null)
        }
    }
    private fun clickToSelect(baseFile: BaseFileBean) {
        if (pickerManager?.supportMultipleSelection() == true) {
            fragmentViewModel?.uiState?.value?.apply {
                val key = baseFile.mData?.toLowerCase()?.hashCode() ?: 0
                if (selectedList.contains(key).not()) {
                    if (pickerManager?.checkSelectedCounts() != true) {
                        return
                    }
                    pickerManager?.addFile(key, baseFile)
                } else {
                    pickerManager?.removeFile(key, baseFile)
                }
            }
            pickerManager?.onEditCountChange()
        } else {
            pickerManager?.pickerSingleFile(baseFile)
        }
    }
    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_FILE_BROWSER
    }
    override fun attachPCConnect(): Boolean {
        return false
    }
}