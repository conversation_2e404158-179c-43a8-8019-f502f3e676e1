plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace "com.oplus.filemanager.pcconnect"

    buildFeatures {
        aidl = true
    }
}

dependencies {
    compileOnly(libs.oplus.addon.sdk) { artifact { type = "aar" } }

    implementation libs.androidx.fragment.ktx

    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.recyclerview
    implementation libs.oplus.appcompat.scrollbar
    implementation libs.oplus.cast.sdk
    implementation libs.oplus.appprovider.settings
    implementation(libs.oplus.synergy.compat)
    implementation libs.koin.android

    implementation project(':Common')
}