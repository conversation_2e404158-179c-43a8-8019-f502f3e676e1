/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BuildHeader
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/8/2 14:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/8/2       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.utils

import android.content.Context
import android.os.Build
import androidx.annotation.VisibleForTesting
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.utils.AppUtils.getVersionCode
import com.oplus.filemanager.drivebrowser.utils.UniqueIdUtils
import java.util.Locale
import kotlin.collections.HashMap

object BuildHeader {
    @VisibleForTesting
    const val MODEL = "model"
    @VisibleForTesting
    const val OTA_VERSION = "otaVersion"
    @VisibleForTesting
    const val OPLUS_OS_VERSION = "oplusOSVersion"
    @VisibleForTesting
    const val ANDROID_VERSION = "androidVersion"
    @VisibleForTesting
    const val LANGUAGE = "uLang"
    @VisibleForTesting
    const val CLIENT_VERSION_CODE = "clientVersionCode"//app versioncode
    @VisibleForTesting
    const val APP_VERSION = "appVersion"//app versioncode
    @VisibleForTesting
    const val CLIENT_PACKAGE = "clientPackage"//app pkg name
    @VisibleForTesting
    const val APP_ID = "appKey"//app id
    @VisibleForTesting
    const val OPENID = "openId"//duid
    const val TIME_STAMP = "timestamp"
    @VisibleForTesting
    const val BRAND = "brand"//optional, default is oppo
    const val SIGN = "sign"
    const val BIND_DEVICE = "BIND_DEVICE_ID"
    const val X_APPKEY = "X-AppKey"
    const val SALT = "salt"

    fun generateHeader(
        appContext: Context,
        appId: String
    ): HashMap<String, String> {
        val header = HashMap<String, String>()
        val timestamp: Long = TimeUtils.obtainTimestamp()
        header[TIME_STAMP] = timestamp.toString()
        header[OPLUS_OS_VERSION] = PropertyCompat.sColorOSVersion
        header[ANDROID_VERSION] = PropertyCompat.sAndroidVersion
        header[MODEL] = PropertyCompat.sModel
        header[CLIENT_VERSION_CODE] = getVersionCode().toString()
        header[CLIENT_PACKAGE] = appContext.packageName
        header[BIND_DEVICE] = ""
        header[APP_ID] = appId
        header[X_APPKEY] = "d0aba585f7d741fd8138c304946ed32b"
        header[OPENID] = "12312"
        header[SIGN] = ""//this will assign value before do http request
        header[BRAND] = Build.BRAND ?: ""
        header[LANGUAGE] = Locale.getDefault().language
        header[OTA_VERSION] = PropertyCompat.sOTAInfo
        header[APP_VERSION] = getVersionCode().toString()
        header[SALT] = ""
        return header
    }
}