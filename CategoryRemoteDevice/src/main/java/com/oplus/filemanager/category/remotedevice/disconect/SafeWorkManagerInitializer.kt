/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SafeWorkManagerInitializer
 * * Description : 安全的 WorkManager 初始化器，处理存储空间不足的情况
 * * Version     : 1.0
 * * Date        : 2025/2/27
 * * Author      : zhangyitong
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.disconect

import android.content.Context
import android.database.sqlite.SQLiteDiskIOException
import android.database.sqlite.SQLiteException
import android.util.Log
import androidx.work.Configuration
import androidx.work.WorkManager
import com.filemanager.common.compat.OplusUsbEnvironmentCompat
import com.filemanager.common.utils.Utils

object SafeWorkManagerInitializer {
    private const val TAG = "SafeWorkManagerInit"
    private var isInitialized = false
    private var workManagerInstance: WorkManager? = null

    @JvmStatic
    fun safeInitializeWorkManager(context: Context): Boolean {
        if (isInitialized) {
            return workManagerInstance != null
        }

        if (Utils.storageAvailableSizeCanOperate(OplusUsbEnvironmentCompat.getInternalPath(context))) {
            Log.w(TAG, "Storage space is low, cannot initialize WorkManager")
            return false
        }

        // 尝试初始化 WorkManager
        val success = initializeWorkManager(context)
        isInitialized = true

        Log.d(TAG, "WorkManager initialization result: $success")
        return success
    }

    /**
     * 初始化 WorkManager
     */
    private fun initializeWorkManager(context: Context): Boolean {
        return runCatching {
            // 检查是否已经初始化，避免重复初始化
            if (isWorkManagerInitialized(context)) {
                Log.d(TAG, "WorkManager already initialized")
                workManagerInstance = WorkManager.getInstance(context)
                return true
            }

            // 手动初始化 WorkManager
            WorkManager.initialize(context, Configuration.Builder().build())
            workManagerInstance = WorkManager.getInstance(context)

            Log.d(TAG, "WorkManager initialized successfully with custom config")
            true
        }.onFailure { exception ->
            Log.e(TAG, "Failed to initialize WorkManager: ${exception.message}", exception)
            when (exception) {
                is SQLiteDiskIOException -> Log.e(TAG, "SQLite disk I/O error - storage space issue")
                is SQLiteException -> Log.e(TAG, "SQLite error - database corruption or storage issue")
                else -> Log.e(TAG, "Unknown error during WorkManager initialization")
            }
            workManagerInstance = null
        }.getOrElse { false }
    }

    /**
     * 检查 WorkManager 是否已经初始化
     */
    private fun isWorkManagerInitialized(context: Context): Boolean {
        return runCatching {
            WorkManager.getInstance(context)
            true
        }.getOrElse { false }
    }

    @JvmStatic
    fun getWorkManagerInstance(): WorkManager? {
        return workManagerInstance
    }

    @JvmStatic
    fun reset() {
        isInitialized = false
        workManagerInstance = null
    }
}
