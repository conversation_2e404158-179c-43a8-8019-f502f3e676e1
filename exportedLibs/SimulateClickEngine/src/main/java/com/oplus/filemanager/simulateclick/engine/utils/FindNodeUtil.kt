/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - FindNodeUtil.kt
 * Description:
 *     The utils to search widget node in page by AccessibilityNodeInfo
 *
 * Version: 2.0
 * Date: 2024-05-23
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-05-23   2.0    Porting from LLMSimulateClick
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.utils

import android.view.accessibility.AccessibilityNodeInfo

internal object FindNodeUtil {

    /**
     * 增加递归搜索的原因是在某些应用界面，比如拼多多主页，搜索特定id的控件如com.xunmeng.pinduoduo:id/pdd，
     * 使用findAccessibilityNodeInfosByViewId接口获取不到对应的node，需要通过遍历的方式查找
     */
    @JvmStatic
    fun findNodesById(
        accessibilityNodeInfo: AccessibilityNodeInfo?,
        widgetId: String
    ): List<AccessibilityNodeInfo> {
        accessibilityNodeInfo ?: return emptyList()
        val foundNodes = accessibilityNodeInfo.findAccessibilityNodeInfosByViewId(widgetId)
        return if (foundNodes.isNullOrEmpty()) {
            // 如果findAccessibilityNodeInfosByViewId返回空，则递归搜索
            val resultList = mutableListOf<AccessibilityNodeInfo>()
            findNodesByIdRecursively(accessibilityNodeInfo, widgetId, resultList)
            resultList
        } else {
            foundNodes
        }
    }

    /**
     * 递归搜索具有给定resource-id的节点
     */
    @JvmStatic
    private fun findNodesByIdRecursively(
        node: AccessibilityNodeInfo?,
        resourceId: String,
        resultList: MutableList<AccessibilityNodeInfo>
    ) {
        node ?: return
        if (resourceId == node.viewIdResourceName) {
            resultList.add(node)
        }
        for (i in 0 until node.childCount) {
            // getChild返回值可能为null
            findNodesByIdRecursively(node.getChild(i), resourceId, resultList)
        }
    }

    /**
     * [AccessibilityNodeInfo.findAccessibilityNodeInfosByText]不一定能找全指定[widgetText]的节点。
     * 故在需要时([directly] == false)，使用递归遍历的方式查找具有[widgetText]的节点。
     */
    @JvmStatic
    fun findNodesByText(
        accessibilityNodeInfo: AccessibilityNodeInfo?,
        widgetText: String?,
        directly: Boolean
    ): List<AccessibilityNodeInfo> {
        accessibilityNodeInfo ?: return emptyList()
        val resultList = mutableListOf<AccessibilityNodeInfo>()
        if (directly) {
            val directlyResult = accessibilityNodeInfo.findAccessibilityNodeInfosByText(widgetText)
            directlyResult?.let { resultList.addAll(it) }
        }
        if (resultList.isEmpty()) {
            findNodesByTextRecursively(accessibilityNodeInfo, widgetText, resultList)
        }
        return resultList
    }

    /**
     * 递归搜索具有给定[widgetText]的节点
     */
    @JvmStatic
    private fun findNodesByTextRecursively(
        node: AccessibilityNodeInfo?,
        widgetText: String?,
        resultList: MutableList<AccessibilityNodeInfo>
    ) {
        node ?: return
        if (isWidgetTextMatched(node, widgetText)) {
            resultList.add(node)
        }
        for (i in 0 until node.childCount) {
            // getChild返回值可能为null
            findNodesByTextRecursively(node.getChild(i), widgetText, resultList)
        }
    }

    @JvmStatic
    fun isWidgetTextMatched(node: AccessibilityNodeInfo, widgetText: String?): Boolean {
        val text = node.text?.toString() ?: ""
        val contentDescription = node.contentDescription?.toString() ?: ""
        return (text == widgetText) || (contentDescription == widgetText)
    }
}