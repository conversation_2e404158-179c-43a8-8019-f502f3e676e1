<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/default_height"
    android:gravity="center_horizontal|bottom"
    android:orientation="vertical">

    <com.coui.appcompat.progressbar.COUICompProgressIndicator
        android:id="@+id/iv_header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal|bottom"
        android:forceDarkAllowed="false"
        android:visibility="visible"
        app:couiLoadingType="large_animation"
        app:couiLottieLoadingViewHeight="@dimen/default_height"
        app:couiLottieLoadingViewWidth="@dimen/default_height" />
</LinearLayout>