/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DownloadNotifyManager
 * * Description : 远程设备下载文件 通知
 * * Version     : 1.0
 * * Date        : 2024/12/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download.notification

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.IconCompat
import com.filemanager.common.R
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.stringResource
import com.oplus.filemanager.category.remotedevice.download.service.DownloadTaskInfo
import com.oplus.filemanager.category.remotedevice.download.service.RemoteFileDownloadDispatcher
import com.oplus.filemanager.interfaze.remotedevice.DownloadTransferCard

class DownloadNotifyManager(private val context: Context) {

    companion object {
        private const val TAG = "DownloadNotifyManager"
        private const val NOTIFICATION_PROGRESS_MAX = 100
        private const val NOTIFICATION_DOWNLOADING_CHANNEL_ID = "remote-file-downloading-channel-id"
        private const val NOTIFICATION_DOWNLOAD_SUCCESS_CHANNEL_ID = "remote-file-download-success-channel-id"

        /**
         * 跳转到DownloadActivity
         * 传递参数是key为 P_task_id，value为可以转化为int值的String值
         */
        private const val DEEPLINK_DOWNLOAD = "filemanager://deeplink.remotedevice.download"

        /**
         * 跳转到远程电脑文件Activity
         * 传递参数：key为 file_path，value为目录
         */
        const val DEEPLINK_REMOTE_PC = "filemanager://deeplink.super.remotepc"

        const val NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID = 2000
        const val NOTIFICATION_DOWNLOAD_FAILURE_ID = 2001
        const val NOTIFICATION_DOWNLOAD_SUCCESS_ID = 2002
        const val NOTIFICATION_DOWNLOAD_PAUSE_ID = 2003

        /**
         * 跳转到远程电脑文件界面
         */
        @JvmStatic
        fun jumpRemotePCActivityIntent(path: String): Intent {
            val intent = Intent(Intent.ACTION_VIEW)
            val uri = Uri.parse(DEEPLINK_REMOTE_PC)
            intent.setData(uri)
            intent.putExtra(KtConstants.FILE_PATH, path)
            intent.putExtra(KtConstants.FROM, KtConstants.SELF)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            return intent
        }

        /**
         * 跳转到当前的下载文件界面
         */
        @JvmStatic
        fun jumpDownloadActivityIntent(taskId: Int): Intent {
            val intent = Intent(Intent.ACTION_VIEW)
            val uri = Uri.parse(DEEPLINK_DOWNLOAD)
            intent.setData(uri)
            intent.putExtra(KtConstants.P_TASK_ID, "$taskId")
            intent.putExtra(KtConstants.FROM, KtConstants.SELF)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            return intent
        }
    }

    /**
     * 创建通知的channel
     */
    fun createNotifyChannel(
        channelId: String = NOTIFICATION_DOWNLOADING_CHANNEL_ID,
        channelName: String = stringResource(R.string.remote_computer_file),
        importance: Int = NotificationManager.IMPORTANCE_HIGH,
        closeSound: Boolean = false
    ) {
        val notifyMgr = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        if (checkNotifyChannelExist(notifyMgr, channelId)) {
            return
        }
        val channel = NotificationChannel(channelId, channelName, importance)
        if (closeSound) {
            channel.setSound(null, null)
        }
        notifyMgr.createNotificationChannel(channel)
    }

    /**
     * 创建下载进度的通知
     */
    fun createDownloadingNotification(deviceId: String, taskId: Int, filePath: String, progress: Float, totalCount: Int): Notification {
        val openIntent = jumpDownloadActivityIntent(taskId)
        val intent = createPendingIntentActivity(context, openIntent)
        val priority = getNotifyPriority(NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID)
        return NotificationCompat.Builder(context, NOTIFICATION_DOWNLOADING_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_filemanager)
            .setContentTitle(context.resources.getString(R.string.downloading_dialog_title))
            .setContentText(context.resources.getQuantityString(R.plurals.download_file_count, totalCount, totalCount))
            .setContentIntent(intent)
            .setProgress(NOTIFICATION_PROGRESS_MAX, progress.toInt(), false)
            .setPriority(priority)
            .setAutoCancel(false)
            .setOnlyAlertOnce(true)
            .build()
    }

    fun createDownloadSuccessNotification(filePath: String, taskInfo: DownloadTaskInfo): Notification {
        val intent = if (taskInfo.pendingIntent != null) {
            Log.d(TAG, "createDownloadSuccessNotification pendingIntent not null")
            taskInfo.pendingIntent
        } else {
            val openIntent = jumpRemotePCActivityIntent(filePath)
            createPendingIntentActivity(context, openIntent)
        }
        val priority = getNotifyPriority(NOTIFICATION_DOWNLOAD_SUCCESS_ID)
        return NotificationCompat.Builder(context, NOTIFICATION_DOWNLOADING_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_filemanager)
            .setPriority(priority)
            .setContentTitle(context.getString(R.string.download_success_notify))
            .setAutoCancel(true)
            .addAction(R.drawable.ic_launcher_filemanager, context.getString(R.string.card_view), intent)
            .build()
    }

    fun createDownloadFailNotification(deviceId: String, taskId: Int, failMsg: String): Notification {
        val retryIntent = RemoteFileDownloadDispatcher.createContinueDownloadService(context, deviceId, taskId)
        val intent = createPendingIntent(context, retryIntent)
        val priority = getNotifyPriority(NOTIFICATION_DOWNLOAD_FAILURE_ID)
        return NotificationCompat.Builder(context, NOTIFICATION_DOWNLOADING_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_filemanager)
            .setContentTitle(context.getString(R.string.download_failure_toast))
//            .setContentText(failMsg)
            .setPriority(priority)
            .addAction(R.drawable.ic_launcher_filemanager, context.getString(R.string.retry_string), intent)
            .setAutoCancel(true)
            .build()
    }

    fun createDownloadPauseNotification(deviceId: String, taskId: Int): Notification {
        val retryIntent = RemoteFileDownloadDispatcher.createContinueDownloadService(context, deviceId, taskId)
        val intent = createPendingIntent(context, retryIntent)
        val priority = getNotifyPriority(NOTIFICATION_DOWNLOAD_PAUSE_ID)
        return NotificationCompat.Builder(context, NOTIFICATION_DOWNLOADING_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_filemanager)
            .setContentTitle(context.getString(R.string.download_paused))
            .setPriority(priority)
            .addAction(R.drawable.ic_launcher_filemanager, context.getString(R.string.cut_dialog_confirm_message_continue), intent)
            .setAutoCancel(true)
            .build()
    }


    fun showDownloadProgressNotification(deviceId: String, taskId: Int, filePath: String, progress: Float, totalCount: Int) {
        Log.d(TAG, "showDownloadProgressNotification：$progress")
        val notification = createDownloadingNotification(deviceId, taskId, filePath, progress, totalCount)
        notifyNotification(NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID, notification)
    }


    fun showDownloadSuccessNotification(filePath: String, task: DownloadTaskInfo) {
        Log.e(TAG, "showDownloadSuccessNotification")
        val notification = createDownloadSuccessNotification(filePath, task)
        notifyNotification(NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID, notification)
    }

    fun showDownloadFailureNotification(deviceId: String, taskId: Int, failMsg: String) {
        Log.e(TAG, "showDownloadFailureNotification")
        val notification = createDownloadFailNotification(deviceId, taskId, failMsg)
        notifyNotification(NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID, notification)
    }

    fun showDownloadPauseNotification(deviceId: String, taskId: Int) {
        Log.e(TAG, "showDownloadPauseNotification")
        val notification = createDownloadPauseNotification(deviceId, taskId)
        notifyNotification(NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID, notification)
    }

    @SuppressLint("MissingPermission")
    private fun notifyNotification(notificationId: Int, notification: Notification) {
        if (PermissionUtils.hasNotificationPermission(context)) {
            runCatching {
                Log.d(TAG, "notifyNotification -> $notificationId")
                NotificationManagerCompat.from(context).notify(notificationId, notification)
            }.onFailure {
                Log.e(TAG, "notifyNotification ->  $notificationId error cause ${it.message}")
            }
        }
    }

    fun cancelNotification(notificationId: Int) {
        runCatching {
            Log.d(TAG, "cancelNotification $notificationId")
            NotificationManagerCompat.from(context).cancel(notificationId)
        }.onFailure {
            Log.e(TAG, "cancelNotification:$notificationId -> error cause ${it.message}")
        }
    }


    /**
     * 获取通知的小图标，当只有一个文件时，显示该文件对应的图标；当文件个数超过1个，显示默认的文件图标
     * @param filePath 文件路径
     * @param totalCount 文件个数
     */
    private fun getNotifySmallIcon(filePath: String, totalCount: Int): IconCompat {
        val defaultDrawable = ContextCompat.getDrawable(context, R.drawable.ic_file_doc)
        val defaultIcon = IconCompat.createWithResource(context, R.drawable.ic_file_doc)
        val drawable = if (totalCount == 1) {
            val mimeType = MimeTypeHelper.getTypeFromPath(filePath)
            MimeTypeHelper.getIconByType(mimeType) ?: defaultDrawable
        } else if (totalCount == 2) {
            ContextCompat.getDrawable(context, R.drawable.ic_double_file_overlap)
        } else if (totalCount > 2) {
            ContextCompat.getDrawable(context, R.drawable.ic_many_file_overlap)
        } else {
            defaultDrawable
        }
        if (drawable == null) {
            return defaultIcon
        }
        val bitmap = DragUtils.drawableToBitmap(drawable) ?: return defaultIcon
        return IconCompat.createWithBitmap(bitmap)
    }

    private fun createPendingIntent(context: Context, intent: Intent): PendingIntent {
        return PendingIntent.getService(context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
    }

    private fun createPendingIntentActivity(context: Context, intent: Intent): PendingIntent {
        return PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    private fun checkNotifyChannelExist(notificationManager: NotificationManager, channelId: String): Boolean {
        notificationManager.notificationChannels?.map { channel ->
            if (channel.id == channelId) {
                return true
            }
        }
        return false
    }

    /**
     * 获取通知的优先级
     */
    private fun getNotifyPriority(notifyId: Int): Int {
        if (!DownloadTransferCard.showFluidCloud) { // 当前在下载界面
            return NotificationCompat.PRIORITY_DEFAULT
        }
        return when (notifyId) {
            NOTIFICATION_DOWNLOAD_FOREGROUND_SERVICE_ID -> NotificationCompat.PRIORITY_HIGH
            NOTIFICATION_DOWNLOAD_FAILURE_ID -> NotificationCompat.PRIORITY_HIGH
            NOTIFICATION_DOWNLOAD_SUCCESS_ID -> NotificationCompat.PRIORITY_HIGH
            NOTIFICATION_DOWNLOAD_PAUSE_ID -> NotificationCompat.PRIORITY_HIGH
            else -> NotificationCompat.PRIORITY_DEFAULT
        }
    }
}