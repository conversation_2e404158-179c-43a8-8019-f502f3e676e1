package com.coloros.filemanager.utils

import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import androidx.core.app.NotificationManagerCompat
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.PermissionUtils.PERMISSION_NOTIFICATION
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class PermissionUtilsTest {

    @MockK
    lateinit var mActivity: Activity

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        mActivity = mockk(relaxed = true)
        MyApplication.init(mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        })
    }


    @Test
    fun `should verify when request storage permission without`() {
        mockkStatic(SdkUtils::class)
        every { SdkUtils.isAtLeastR() }.returns(true)
        Assert.assertTrue(PermissionUtils.requestStoragePermission(mActivity))

        every { SdkUtils.isAtLeastR() }.returns(false)
        Assert.assertTrue(PermissionUtils.requestStoragePermission(mActivity))

        every { SdkUtils.isAtLeastR() }.throws(Exception("test-error"))
        Assert.assertFalse(PermissionUtils.requestStoragePermission(mActivity))
    }

    @Test
    fun `should verify when open privacy policy without`() {
        mockkStatic(Utils::class)
        every { Utils.isQuickClick() }.returns(true)
        PermissionUtils.openPrivacyPolicy(mActivity)

        every { Utils.isQuickClick() }.returns(false)
        mockkObject(KtAppUtils)
        every { KtAppUtils.mIsOnePlusOverSea }.returns(true)
        PermissionUtils.openPrivacyPolicy(mActivity)

        every { KtAppUtils.mIsOnePlusOverSea }.returns(false)
        every { KtAppUtils.checkAppEnabledWithDialog(mActivity, any(), any()) }.returns(false)
        PermissionUtils.openPrivacyPolicy(mActivity)

        every { KtAppUtils.checkAppEnabledWithDialog(mActivity, any(), any()) }.returns(true)
        mockkStatic(SdkUtils::class)
        every { SdkUtils.isAtLeastR() }.returns(true)
        PermissionUtils.openPrivacyPolicy(mActivity)

        every { SdkUtils.isAtLeastR() }.throws(Exception("test-error"))
        PermissionUtils.openPrivacyPolicy(mActivity)
    }

    @Test
    fun `should verify when open permission setting without`() {
        mockkStatic(SdkUtils::class)
        every { SdkUtils.isAtLeastR() }.returns(true)
        Assert.assertTrue(PermissionUtils.openPermissionSetting(mActivity))

        every { SdkUtils.isAtLeastR() }.returns(false)
        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.sIsExpRom }.returns(true)
        Assert.assertTrue(PermissionUtils.openPermissionSetting(mActivity))

        every { FeatureCompat.sIsExpRom }.returns(false)
        Assert.assertTrue(PermissionUtils.openPermissionSetting(mActivity))

        every { SdkUtils.isAtLeastR() }.throws(Exception("test-error"))
        Assert.assertFalse(PermissionUtils.openPermissionSetting(mActivity))
    }

    @Test
    fun `should verify when has get installed apps permission without`() {
        mockkStatic(FeatureCompat::class)
        every { FeatureCompat.sIsExpRom }.returns(false)
        Assert.assertTrue(PermissionUtils.hasGetInstalledAppsPermission())

        val context = appContext
        every { context.packageManager }.returns(mockk(relaxed = true) {
            every { getPermissionInfo(any(), 0) }.returns(mockk(relaxed = true))
        })
        every { context.checkSelfPermission(any()) }.returns(PackageManager.PERMISSION_DENIED)
        Assert.assertFalse(PermissionUtils.hasGetInstalledAppsPermission())
    }

    @Test
    fun `should verify when request get installed apps permission without`() {
        mockkStatic(SdkUtils::class)
        every { SdkUtils.isAtLeastR() }.returns(true)
        Assert.assertTrue(PermissionUtils.requestGetInstalledAppsPermission(mActivity))

        every { SdkUtils.isAtLeastR() }.returns(false)
        Assert.assertFalse(PermissionUtils.requestGetInstalledAppsPermission(mActivity))
    }

    @Test
    fun `should return true when call hasNotificationPermission if isAtLeastT if has notification`() {
        //setup
        mockkStatic(SdkUtils::class)
        //given
        every { SdkUtils.isAtLeastT() } returns true
        val context = mockk<Context>()
        every { context.checkSelfPermission(PERMISSION_NOTIFICATION) } returns PackageManager.PERMISSION_GRANTED
        //when
        val result = PermissionUtils.hasNotificationPermission(context)
        //then
        Assert.assertTrue(result)
        verify { context.checkSelfPermission(PERMISSION_NOTIFICATION) }
        //teardown
        unmockkStatic(SdkUtils::class)
    }

    @Test
    fun `should return true when call hasNotificationPermission if not isAtLeastT if has notification`() {
        //setup
        mockkStatic(SdkUtils::class)
        mockkStatic(NotificationManagerCompat::class)
        //given
        every { SdkUtils.isAtLeastT() } returns false
        val context = mockk<Context>()
        val notificationManagerCompat = mockk<NotificationManagerCompat>()
        every { NotificationManagerCompat.from(context) } returns notificationManagerCompat
        every { notificationManagerCompat.areNotificationsEnabled() } returns true
        //when
        val result = PermissionUtils.hasNotificationPermission(context)
        //then
        Assert.assertTrue(result)
        verify { notificationManagerCompat.areNotificationsEnabled() }
        //teardown
        unmockkStatic(SdkUtils::class)
        unmockkStatic(NotificationManagerCompat::class)
    }


    @After
    fun afterTests() {
        unmockkAll()
    }
}