package com.filemanager.compresspreview

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import android.os.Looper
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.fragment.app.Fragment
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.filemanager.compresspreview.ui.CompressPreviewActivity
import com.filemanager.compresspreview.ui.CompressPreviewFragment
import com.filemanager.common.filepreview.PreviewCombineFragment
import com.coui.appcompat.toolbar.COUIToolbar
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.Shadows
import org.robolectric.annotation.Config
import org.robolectric.annotation.LooperMode
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * CompressPreviewApi的单元测试类
 * 用于测试CompressPreviewApi接口实现类的各种功能
 */
@Config(manifest = Config.NONE, sdk = [28])
@LooperMode(LooperMode.Mode.PAUSED)
class CompressPreviewApiTest {

    // 模拟Activity对象
    @MockK
    private lateinit var mockActivity: Activity

    // 模拟基础Fragment对象
    @MockK
    private lateinit var mockFragment: Fragment

    // 模拟压缩预览Fragment对象
    @MockK
    private lateinit var mockCompressFragment: CompressPreviewFragment

    // 模拟预览组合Fragment对象
    @MockK
    private lateinit var mockCombineFragment: PreviewCombineFragment

    // 模拟基础文件对象
    @MockK
    private lateinit var mockFile: BaseFileBean

    // 模拟菜单项对象
    @MockK
    private lateinit var mockMenuItem: MenuItem

    // 模拟工具栏对象
    @MockK
    private lateinit var mockToolbar: COUIToolbar

    // 模拟标签页监听器对象
    @MockK
    private lateinit var mockTabListener: TabActivityListener<MediaFileWrapper>

    /**
     * 测试前的初始化方法
     * 初始化所有mock对象和静态mock
     */
    @Before
    fun setup() {
        MockKAnnotations.init(this)
        clearAllMocks()
        mockkStatic(Log::class)
        mockkObject(UriHelper)
        every { Log.d(any(), any()) } just Runs
        every { Log.e(any(), any(), any()) } just Runs
    }

    /**
     * 测试后的清理方法
     * 释放所有mock资源
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试getFragment方法
     * 验证是否能正确返回CompressPreviewFragment实例
     */
    @Test
    fun `getFragment should return CompressPreviewFragment`() {
        val fragment = CompressPreviewApi.getFragment(mockActivity)
        assertTrue(fragment is CompressPreviewFragment)
        verify { Log.d("CompressPreviewApi", "getFragment") }
    }

    /**
     * 测试onResumeLoadData方法
     * 验证是否能正确委托给CompressPreviewFragment
     */
    @Test
    fun `onResumeLoadData should delegate to CompressPreviewFragment`() {
        every { mockCompressFragment.onResumeLoadData() } just Runs
        CompressPreviewApi.onResumeLoadData(mockCompressFragment)
        verify(exactly = 1) { mockCompressFragment.onResumeLoadData() }
        verify { Log.d("CompressPreviewApi", "onResumeLoadData") }
    }

    /**
     * 测试onResumeLoadData方法
     * 验证当传入错误Fragment类型时不会调用委托方法
     */
    @Test
    fun `onResumeLoadData should not delegate to wrong fragment type`() {
        CompressPreviewApi.onResumeLoadData(mockFragment)
        verify(exactly = 0) { mockCompressFragment.onResumeLoadData() }
        verify { Log.d("CompressPreviewApi", "onResumeLoadData") }
    }

    /**
     * 测试onMenuItemSelected方法
     * 验证当CompressPreviewFragment返回true时的处理
     */
    @Test
    fun `onMenuItemSelected should delegate to CompressPreviewFragment when true`() {
        every { mockCompressFragment.onMenuItemSelected(any()) } returns true
        val result = CompressPreviewApi.onMenuItemSelected(mockCompressFragment, mockMenuItem)
        assertTrue(result)
        verify { Log.d("CompressPreviewApi", "onMenuItemSelected") }
    }

    /**
     * 测试onMenuItemSelected方法
     * 验证当CompressPreviewFragment返回false时的处理
     */
    @Test
    fun `onMenuItemSelected should delegate to CompressPreviewFragment when false`() {
        every { mockCompressFragment.onMenuItemSelected(any()) } returns false
        val result = CompressPreviewApi.onMenuItemSelected(mockCompressFragment, mockMenuItem)
        assertFalse(result)
        verify { Log.d("CompressPreviewApi", "onMenuItemSelected") }
    }

    /**
     * 测试onMenuItemSelected方法
     * 验证当传入错误Fragment类型时返回false
     */
    @Test
    fun `onMenuItemSelected should return false for wrong fragment`() {
        val result = CompressPreviewApi.onMenuItemSelected(mockFragment, mockMenuItem)
        assertFalse(result)
        verify { Log.d("CompressPreviewApi", "onMenuItemSelected") }
    }

    /**
     * 测试pressBack方法
     * 验证当CompressPreviewFragment返回true时的处理
     */
    @Test
    fun `pressBack should delegate to CompressPreviewFragment when true`() {
        every { mockCompressFragment.pressBack() } returns true
        val result = CompressPreviewApi.pressBack(mockCompressFragment)
        assertTrue(result)
        verify { Log.d("CompressPreviewApi", "pressBack") }
    }

    /**
     * 测试pressBack方法
     * 验证当CompressPreviewFragment返回false时的处理
     */
    @Test
    fun `pressBack should delegate to CompressPreviewFragment when false`() {
        every { mockCompressFragment.pressBack() } returns false
        val result = CompressPreviewApi.pressBack(mockCompressFragment)
        assertFalse(result)
        verify { Log.d("CompressPreviewApi", "pressBack") }
    }

    /**
     * 测试pressBack方法
     * 验证当传入错误Fragment类型时返回false
     */
    @Test
    fun `pressBack should return false for wrong fragment`() {
        val result = CompressPreviewApi.pressBack(mockFragment)
        assertFalse(result)
        verify { Log.d("CompressPreviewApi", "pressBack") }
    }

    /**
     * 测试fromSelectPathResult方法
     * 验证是否能正确委托给CompressPreviewFragment
     */
    @Test
    fun `fromSelectPathResult should delegate to CompressPreviewFragment`() {
        val paths = listOf("/path1", "/path2")
        every { mockCompressFragment.fromSelectPathResult(any(), any()) } just Runs
        CompressPreviewApi.fromSelectPathResult(mockCompressFragment, 100, paths)
        verify { mockCompressFragment.fromSelectPathResult(100, paths) }
        verify { Log.d("CompressPreviewApi", "fromSelectPathResult") }
    }

    /**
     * 测试backToTop方法
     * 验证是否能正确委托给CompressPreviewFragment
     */
    @Test
    fun `backToTop should delegate to CompressPreviewFragment`() {
        every { mockCompressFragment.backToTop() } just Runs
        CompressPreviewApi.backToTop(mockCompressFragment)
        verify { mockCompressFragment.backToTop() }
        verify { Log.d("CompressPreviewApi", "backToTop") }
    }

    /**
     * 测试exitSelectionMode方法
     * 验证是否能正确委托给CompressPreviewFragment
     */
    @Test
    fun `exitSelectionMode should delegate to CompressPreviewFragment`() {
        every { mockCompressFragment.exitSelectionMode() } just Runs
        CompressPreviewApi.exitSelectionMode(mockCompressFragment)
        verify { mockCompressFragment.exitSelectionMode() }
    }

    /**
     * 测试onSideNavigationClicked方法
     * 验证当PreviewCombineFragment返回true时的处理
     */
    @Test
    fun `onSideNavigationClicked should delegate to PreviewCombineFragment when true`() {
        every { mockCombineFragment.onSideNavigationClicked(any()) } returns true
        val result = CompressPreviewApi.onSideNavigationClicked(mockCombineFragment, true)
        assertTrue(result)
    }

    /**
     * 测试onSideNavigationClicked方法
     * 验证当PreviewCombineFragment返回false时的处理
     */
    @Test
    fun `onSideNavigationClicked should delegate to PreviewCombineFragment when false`() {
        every { mockCombineFragment.onSideNavigationClicked(any()) } returns false
        val result = CompressPreviewApi.onSideNavigationClicked(mockCombineFragment, true)
        assertFalse(result)
    }

    /**
     * 测试onSideNavigationClicked方法
     * 验证当传入错误Fragment类型时返回false
     */
    @Test
    fun `onSideNavigationClicked should return false for wrong fragment`() {
        val result = CompressPreviewApi.onSideNavigationClicked(mockFragment, true)
        assertFalse(result)
    }

    /**
     * 测试无操作方法
     * 验证所有无操作方法的默认行为
     */
    @Test
    fun `no-op methods should do nothing`() {
        CompressPreviewApi.onCreateOptionsMenu(mockFragment, mockk(), mockk())
        CompressPreviewApi.setToolbarAndTabListener(mockFragment, mockToolbar, "title", mockTabListener)
        CompressPreviewApi.updateLabels(mockFragment)
        CompressPreviewApi.setIsHalfScreen(mockFragment, 1, true)
        CompressPreviewApi.permissionSuccess(mockFragment)
        CompressPreviewApi.setCurrentFilePath(mockFragment, null)
        assertEquals("", CompressPreviewApi.getCurrentPath(mockFragment))
    }
}