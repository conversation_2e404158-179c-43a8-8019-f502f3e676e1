/*********************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : FileMTestRunListener
 * Description :
 * Version     : 1.0
 * Date        : 2023/08/01
 * Author      : W9002848
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * W9002848        2023/08/07       1.0      create
 **********************************************************************/
package com.coloros.filemanager

import com.coloros.filemanager.utils.TestAppUtils
import com.coloros.filemanager.utils.TestPermissionUtils
import com.oplus.autotest.olt.testlib.common.TestRunListener
import com.oplus.filemanager.main.ui.MainActivity
import org.junit.runner.Description

class FileMTestRunListener : TestRunListener() {

    override fun testRunStarted(description: Description?) {
        super.testRunStarted(description)
        // Initialize some operations, For example: screen bright screen unlock
        TestAppUtils.initTest()
        //Open MainActivity
        TestAppUtils.openActivity(MainActivity::class.java)
        //Apply permissions to gallery after start
        TestPermissionUtils.applyPermissions()
    }

}