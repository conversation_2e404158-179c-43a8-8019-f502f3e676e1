<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="fop_COUIAlertDialog.SingleEdit" parent="COUIAlertDialog.BottomAssignment">
        <item name="customContentLayout">@layout/fop_dialog_single_input_name_file</item>
    </style>
    <style name="fop_COUIAlertDialog_SingleEdit_Center" parent="COUIAlertDialog.Center">
        <item name="customContentLayout">@layout/fop_dialog_single_input_name_file</item>
    </style>

    <style name="StatementAndGuideTheme" parent="Theme.COUI">
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0</item>
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="selectSystemForceDarkType">0</item>
        <item name="android:windowExitAnimation">@anim/coui_fade_out_fast</item>
    </style>

    <style name="CommonAppBarStyle" parent="Widget.COUI.Toolbar">
        <item name="android:background">?attr/couiColorBackgroundWithCard</item>
    </style>

    <!-- Toolbar -->
    <style name="TitleStyle">
        <item name="android:textAppearance">@style/textAppearanceLargestTitle</item>
        <item name="android:textSize">@dimen/toolbar_title_init_text_size</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">OPlusSans 3.0</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:singleLine">true</item>
    </style>

    <style name="SubtitleStyle">
        <item name="android:textAppearance">@style/textAppearanceLargestTitle</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">#8C000000</item>
    </style>

    <style name="SettingAppBarStyle" parent="Widget.COUI.Toolbar">
        <item name="android:background">?attr/couiColorBackgroundWithCard</item>
    </style>

    <style name="Widget.COUI.EditTextView" parent="android:Widget.EditText">
        <item name="android:minHeight">@dimen/coui_dot_stroke_width</item>
        <item name="android:editTextColor">@color/coui_edit_text_color</item>
        <item name="android:textAppearance">@style/couiInputTextAppearance</item>
        <item name="android:paddingTop">0dp</item>
        <item name="android:paddingBottom">0dp</item>
        <item name="couiDefaultStrokeColor">?attr/couiColorDivider</item>
        <item name="couiDisabledStrokeColor">@color/coui_textinput_stroke_color_disabled</item>
        <item name="couiStrokeColor">?attr/couiColorPrimary</item>

        <item name="collapsedTextColor">?attr/couiColorSecondNeutral</item>
        <item name="couiStrokeWidth">@dimen/coui_textinput_stroke_width</item>
        <item name="couiFocusStrokeWidth">@dimen/coui_textinput_focus_stroke_width</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textColorHint">?attr/couiColorLabelTertiary</item>
    </style>
</resources>
