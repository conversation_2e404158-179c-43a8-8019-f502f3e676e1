/*********************************************************************
 * * Copyright (C), 2010-2024 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SelectPanelThemeModeTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/03/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir.utils

import com.oplus.selectdir.R
import org.junit.Assert
import org.junit.Test

class SelectPanelThemeModeTest {

    companion object {
        private val MODE_ARRAY = intArrayOf(
            SelectPanelThemeMode.MODE_BLUE,
            SelectPanelThemeMode.MODE_LIGHT_BLUE,
            SelectPanelThemeMode.MODE_DARK_BLUE,
            SelectPanelThemeMode.MODE_GREEN,
            SelectPanelThemeMode.MODE_LIGHT_GREEN,
            SelectPanelThemeMode.MODE_DARK_GREEN,
            SelectPanelThemeMode.MODE_RED,
            SelectPanelThemeMode.MODE_LIGHT_RED,
            SelectPanelThemeMode.MODE_DARK_RED,
            SelectPanelThemeMode.MODE_ORANGE,
            SelectPanelThemeMode.MODE_LIGHT_ORANGE,
            SelectPanelThemeMode.MODE_DARK_ORANGE,
            SelectPanelThemeMode.MODE_PURPLE,
            SelectPanelThemeMode.MODE_LIGHT_PURPLE,
            SelectPanelThemeMode.MODE_DARK_PURPLE,
            SelectPanelThemeMode.MODE_YELLOW,
            SelectPanelThemeMode.MODE_LIGHT_YELLOW,
            SelectPanelThemeMode.MODE_DARK_YELLOW,
            SelectPanelThemeMode.MODE_SKY_BLUE,
            SelectPanelThemeMode.MODE_LIGHT_SKY_BLUE,
            SelectPanelThemeMode.MODE_DARK_SKY_BLUE,
            -1
        )

        private val STYLE = intArrayOf(
            com.support.appcompat.R.style.Theme_COUI_Blue,
            com.support.appcompat.R.style.Theme_COUI_Light_Blue,
            com.support.appcompat.R.style.Theme_COUI_Dark_Blue,
            com.support.appcompat.R.style.Theme_COUI_Green,
            com.support.appcompat.R.style.Theme_COUI_Light_Green,
            com.support.appcompat.R.style.Theme_COUI_Dark_Green,
            com.support.appcompat.R.style.Theme_COUI_Red,
            com.support.appcompat.R.style.Theme_COUI_Light_Red,
            com.support.appcompat.R.style.Theme_COUI_Dark_Red,
            com.support.appcompat.R.style.Theme_COUI_Orange,
            com.support.appcompat.R.style.Theme_COUI_Light_Orange,
            com.support.appcompat.R.style.Theme_COUI_Dark_Orange,
            com.support.appcompat.R.style.Theme_COUI_Purple,
            com.support.appcompat.R.style.Theme_COUI_Light_Purple,
            com.support.appcompat.R.style.Theme_COUI_Dark_Purple,
            com.support.appcompat.R.style.Theme_COUI_Yellow,
            com.support.appcompat.R.style.Theme_COUI_Light_Yellow,
            com.support.appcompat.R.style.Theme_COUI_Dark_Yellow,
            com.support.appcompat.R.style.Theme_COUI_Skyblue,
            com.support.appcompat.R.style.Theme_COUI_Light_Skyblue,
            com.support.appcompat.R.style.Theme_COUI_Dark_Skyblue,
            com.support.appcompat.R.style.Theme_COUI_Blue
        )
    }

    @Test
    fun `should return right theme if set right mode`() {
        MODE_ARRAY.forEachIndexed { index, mode  ->
            val style = SelectPanelThemeMode.getTheme(mode)

            val exceptStyle = STYLE[index]
            Assert.assertEquals(style, exceptStyle)
        }
    }
}