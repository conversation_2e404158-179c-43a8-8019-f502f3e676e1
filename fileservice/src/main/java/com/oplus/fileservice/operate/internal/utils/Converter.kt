/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Converter
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/7 16:00
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/7       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.utils

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTypeUtils
import com.oplus.fileservice.operate.internal.FileInfo
import org.apache.commons.io.FilenameUtils

fun FileInfo.converterFileBean(): BaseFileBean {

    val baseFileBean = BaseFileBean()
    baseFileBean.mData = this.data
    baseFileBean.mLocalType = MimeTypeHelper.getTypeFromExtension(FileTypeUtils.getExtension(this.displayName))
        ?: MimeTypeHelper.UNKNOWN_TYPE
    return baseFileBean
}