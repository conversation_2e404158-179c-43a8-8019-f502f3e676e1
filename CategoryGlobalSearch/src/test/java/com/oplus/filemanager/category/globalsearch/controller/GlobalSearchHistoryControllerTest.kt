/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GlobalSearchHistoryControllerTest
 ** Description : GlobalSearchHistoryController Unit Test
 ** Version     : 1.0
 ** Date        : 2022/9/2
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2022/9/2      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.controller

import android.content.Context
import android.view.View
import com.filemanager.common.MyApplication
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchViewModel
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class GlobalSearchHistoryControllerTest {

    @Before
    fun setup() {
        mockkObject(MyApplication)
        val context: Context = mockk(relaxed = true)
        every { MyApplication.sAppContext }.returns(context)
    }

    @After
    fun after() {
        unmockkObject(MyApplication)
    }

    @Test
    fun should_when_addHistory() {
        val controller = mockk<GlobalSearchHistoryController>()
        val viewModel = mockk<GlobalSearchViewModel>()
        every { controller.isAdd }.answers { callOriginal() }
        every { controller.addHistory(any()) }.answers { callOriginal() }

        controller.addHistory("测试")
        Assert.assertFalse(controller.isAdd)

        every { controller.mViewModel }.returns(viewModel)
        every { viewModel.onQueryTextSubmit(any()) }.returns(true)
        controller.addHistory("测试")
        Assert.assertTrue(controller.isAdd)

        every { viewModel.onQueryTextSubmit(any()) }.returns(false)
        controller.addHistory("测试")
        Assert.assertFalse(controller.isAdd)
    }

    @Test
    fun should_when_shouldRemoveHistory() {
        val controller = mockk<GlobalSearchHistoryController>(relaxed = true)
        every { controller.shouldRemoveHistory(any()) }.answers { callOriginal() }
        every { controller.isAdd }.answers { callOriginal() }
        every { controller.isAdd = any() }.answers { callOriginal() }
        controller.isAdd = false
        val list = mutableListOf<View?>()
        Assert.assertFalse(controller.shouldRemoveHistory(list))
        Assert.assertFalse(controller.isAdd)

        controller.isAdd = true
        Assert.assertFalse(controller.shouldRemoveHistory(list))
        Assert.assertTrue(controller.isAdd)

        list.add(mockk<View>())
        Assert.assertTrue(controller.shouldRemoveHistory(list))
        verify { controller.isAdd = true }
        Assert.assertFalse(controller.isAdd)

        controller.isAdd = false
        Assert.assertFalse(controller.shouldRemoveHistory(list))
        Assert.assertFalse(controller.isAdd)
    }
}