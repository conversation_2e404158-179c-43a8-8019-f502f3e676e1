/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GlobalSearchNormalLoaderTest
 ** Description : GlobalSearchNormalLoaderTest Unit Test
 ** Version     : 1.0
 ** Date        : 2023/11/13
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  hank.zhou       2023/11/13      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui

import android.content.Context
import android.text.TextUtils
import android.util.SparseArray
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.NewFunctionSwitch
import com.oplus.filemanager.category.globalsearch.ui.loader.GlobalSearchNormalLoader
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class GlobalSearchNormalLoaderTest {

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        mockkObject(MyApplication)
        val context: Context = mockk(relaxed = true)
        every { MyApplication.sAppContext }.returns(context)
        mockkObject(NewFunctionSwitch)
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getInternalSdPath(context) }.returns("/sdcard/")
        mockkStatic(CategoryHelper::class)
        val ignorpath = SparseArray<String>()
        ignorpath.append(0, "test1")
        ignorpath.append(1, "test2")
        every { CategoryHelper.setIgnoredPath(context, CategoryHelper.CATEGORY_DOC) }.returns(ignorpath)
        mockkStatic(TextUtils::class)
        every { TextUtils.isEmpty(null) }.returns(true)
        every { TextUtils.isEmpty("") }.returns(true)
        every { TextUtils.isEmpty("test") }.returns(false)
        every { TextUtils.isEmpty("test1") }.returns(false)
        every { TextUtils.isEmpty("/sdcard/test") }.returns(false)
        every { TextUtils.isEmpty("/sdcard/test1") }.returns(false)
    }

    @Test
    fun should_return_true_when_path_is_empty() {
        val loader = GlobalSearchNormalLoader()
        Assert.assertEquals(loader.isIgnoredPath(null, null, null), true)
        Assert.assertEquals(loader.isIgnoredPath("", null, null), true)
        Assert.assertEquals(loader.isIgnoredPath("", "/sdcard/", null), true)
        val ignorpath = SparseArray<String>()
        ignorpath.append(0, "test1")
        Assert.assertEquals(loader.isIgnoredPath("", "/sdcard/", ignorpath), true)
    }

    @Test
    fun should_return_false_when_ignorpaths_is_empty() {
        val loader = GlobalSearchNormalLoader()
        val ignorpath = SparseArray<String>()
        Assert.assertEquals(loader.isIgnoredPath("test", null, null), false)
        Assert.assertEquals(loader.isIgnoredPath("test", null, ignorpath), false)
        Assert.assertEquals(loader.isIgnoredPath("test", "/sdcard/", ignorpath), false)
    }

    @After
    fun teardown() {
        unmockkObject(MyApplication)
        unmockkObject(NewFunctionSwitch)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(CategoryHelper::class)
        unmockkStatic(TextUtils::class)
    }
}