/***************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * Description:Unit test shadow class
 * All rights reserved
 *
 *
 * Version: 1.0
 * Date : 2019/7/22
 * <EMAIL>
 *
 * -------------------Revision History: ----------------------
 *     <author>    <data>     <version >     <desc>
 *   lanjiabo    2019/7/22   1.0            build this moudle
 ****************************************************************/
package shadow;

import android.os.SystemClock;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(SystemClock.class)
public class ShadowSystemClock {
    private static long sRealtime;
    @Implementation
    public static long elapsedRealtime() {
        return sRealtime;
    }

    public static void setRealtime(long realtime) {
        sRealtime = realtime;
    }
}
