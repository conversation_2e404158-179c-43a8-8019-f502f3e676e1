/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - MediaPlayProgressUtilsTest.kt
 * Description:
 * The test cases for MediaPlayProgressUtils
 *
 * Version: 1.0
 * Date: 2024-09-18
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-09-18   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.utils

import org.junit.Assert
import org.junit.Test

class MediaPlayProgressUtilsTest : Assert() {

    @Test
    fun `should as expected when formatVideoTime`() {
        // Given
        val testCases = listOf(
            0L to "00:00",
            1234L to "00:01",
            59999L to "01:00",
            60000L to "01:00",
            123456L to "02:03",
            126000L to "02:06",
            136000L to "02:16",
            1136000L to "18:56",
            1146000L to "19:06",
            3600000L to "60:00",
            3661000L to "61:01"
        )

        testCases.forEach { (input, expected) ->
            // When
            val result = input.formatToMediaPlayTime()

            // Then
            assertEquals(expected, result)
        }
    }
}