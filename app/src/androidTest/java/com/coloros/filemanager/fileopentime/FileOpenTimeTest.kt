/*********************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : FileOpenTimeTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/08/01
 * Author      : W9002848
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * W9002848        2023/08/01       1.0      create
 **********************************************************************/
package com.coloros.filemanager.fileopentime

import android.content.ContentValues
import android.net.Uri
import com.coloros.filemanager.fileopentime.FileOpenTimeTestUtils.checkFileOpenTime
import com.coloros.filemanager.fileopentime.FileOpenTimeTestUtils.cleanTestResource
import com.coloros.filemanager.fileopentime.FileOpenTimeTestUtils.createTestFileTxt
import com.coloros.filemanager.utils.TestConstants
import com.coloros.filemanager.utils.TestConstants.FILE_OPEN_TIME_METHOD
import com.coloros.filemanager.utils.TestConstants.FILE_TXT_NAME
import com.coloros.filemanager.utils.TestConstants.FILE_TXT_TYPE
import com.coloros.filemanager.utils.TestConstants.PATH_FILE_MANAGER_TEST
import com.coloros.filemanager.utils.TestDBHelpUtils
import com.oplus.autotest.olt.testlib.annotations.CaseId
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization
import com.oplus.autotest.olt.testlib.common.BasicData
import org.junit.After
import org.junit.AfterClass
import org.junit.Assert
import org.junit.Before
import org.junit.BeforeClass
import org.junit.Test

class FileOpenTimeTest {

    private val TAG = FileOpenTimeTest::class.java.simpleName
    private val time = System.currentTimeMillis()
    private val txtFileOpenTimePath = "$PATH_FILE_MANAGER_TEST${FILE_TXT_NAME}_$time$FILE_TXT_TYPE"

    companion object {
        private val targetContext = BasicData.getTargetContext()

        @JvmStatic
        @BeforeClass
        fun classSetUp() {
        }

        @JvmStatic
        @AfterClass
        fun classTearDown() {
        }
    }

    @Before
    fun setUp() {
        //删除测试数据，不删除测试文件夹
        cleanTestResource(txtFileOpenTimePath, false)
    }

    @After
    fun tearDown() {
        //删除测试数据，并删除测试文件夹
        cleanTestResource(txtFileOpenTimePath, true)
    }

    /**调用失败
     * 用例：当传入错误参数
     * 期望：验证底层不Crash
     * 实际：返回空数据，无Crash的异常情况
     */
    @Test
    @CasePrioritization("A")
    @CaseId("AT_APP_FileManager_04140")
    fun should_call_file_key_Fail_when_enter_error_parameter() {
        val fileOpenTimeKeyError = "file_time_1"
        val uri = Uri.parse(TestConstants.FILE_OPEN_TIME_URI)
        //return bundle
        val bundle = targetContext.contentResolver.call(uri, FILE_OPEN_TIME_METHOD, null, null)
        Assert.assertNotNull(
            "The bundle can't be empty! If is empty, the \"implementation project(':FileOpenTime')\" code is annotated!",
            bundle
        )
        //get bundle list data
        val listData = (bundle?.getParcelableArrayList<ContentValues>(fileOpenTimeKeyError, ContentValues::class.java))
        Assert.assertTrue("The listData should be empty!!", listData.isNullOrEmpty())
    }

    /**调用失败
     * 用例：当传入错误参数调用函数
     * 期望：验证底层不Crash
     * 实际：返回空数据，无Crash的异常的情况
     */
    @Test
    @CasePrioritization("A")
    @CaseId("AT_APP_FileManager_04141")
    fun should_call_method_Fail_when_enter_error_parameter_to_method() {
        val fileOpenTimeMethodError = "getAllFileName_1"
        val uri = Uri.parse(TestConstants.FILE_OPEN_TIME_URI)
        //return bundle
        val bundle = targetContext.contentResolver.call(uri, fileOpenTimeMethodError, null, null)
        assert(bundle == null){"The bundle is empty!"}
    }

    /**调用成功
     * 用例：新创建文件TXT类型
     * 期望：返回列表数据，存在新建文件，并打开文件时间 == 创建文件时间
     * 实际：同期望结果
     */
    @Test
    @CasePrioritization("S")
    @CaseId("AT_APP_FileManager_04142")
    fun should_equals_file_open_time_and_file_create_time_when_first_create() {
        //1、创建txt文件
        createTestFileTxt(txtFileOpenTimePath, false)

        //2、调用接口，获取数据列表
        var listData = TestDBHelpUtils.getRecentlyOpenListData(targetContext)
        assert(!listData.isNullOrEmpty()){"The list is not null or empty!!"}

        //3、校验数据列表中，存在txt文件路径，且打开文件时间 == 创建文件时间，并记录创建txt文件时间
        var pair = checkFileOpenTime(listData, txtFileOpenTimePath)
        var fileCreateTime = pair.first
        var fileOpenTime = pair.second

        assert(fileCreateTime == fileOpenTime){
            "The fileCreateTime:$fileCreateTime and fileOpenTime:$fileOpenTime should be equal!"
        }
    }
}

