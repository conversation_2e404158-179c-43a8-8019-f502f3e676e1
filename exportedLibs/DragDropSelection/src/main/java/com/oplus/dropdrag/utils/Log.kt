/***********************************************************
 * Copyright (C), 2008-2017 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File:
 * Description:
 * Version:
 * Date :
 * Author:
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
</desc></version></data></author> */
package com.oplus.dropdrag.utils

import android.util.Log

internal object Log {
    private const val SDK_TAG = "DragDropSelection"

    @JvmStatic
    var baseLogTag: String = "DragDropSelectionSdk"

    @JvmStatic
    var enableLog: Boolean = true
        set(value) {
            field = value
            logLevel = if (value) Log.VERBOSE else Log.WARN
        }

    private var logLevel = Log.VERBOSE

    @JvmStatic
    private fun logTag(tag: String? = null): String =
        if (tag.isNullOrEmpty()) "$baseLogTag::$SDK_TAG" else "$baseLogTag::$SDK_TAG::$tag"

    @JvmStatic
    fun println(priority: Int, tag: String?, msg: String) {
        if (priority >= logLevel) {
            Log.println(priority, logTag(tag), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun v(tag: String, msg: String) {
        if (logLevel <= Log.VERBOSE) {
            Log.v(logTag(tag), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun v(msg: String) {
        if (logLevel <= Log.VERBOSE) {
            Log.v(logTag(), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun d(tag: String, msg: String) {
        if (logLevel <= Log.DEBUG) {
            Log.d(logTag(tag), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun d(msg: String) {
        if (logLevel <= Log.DEBUG) {
            Log.d(logTag(), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun i(tag: String, msg: String) {
        if (logLevel <= Log.INFO) {
            Log.i(logTag(tag), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun i(msg: String) {
        if (logLevel <= Log.INFO) {
            Log.i(logTag(), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun w(tag: String, msg: String) {
        if (logLevel <= Log.WARN) {
            Log.w(logTag(tag), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun w(msg: String) {
        if (logLevel <= Log.WARN) {
            Log.w(logTag(), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun w(tag: String, msg: String, ex: Throwable?) {
        if (logLevel <= Log.WARN) {
            Log.w(logTag(tag), handleEmptyMsg(msg), ex)
        }
    }

    @JvmStatic
    fun e(tag: String, msg: String) {
        if (logLevel <= Log.ERROR) {
            Log.e(logTag(tag), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun e(msg: String) {
        if (logLevel <= Log.ERROR) {
            Log.e(logTag(), handleEmptyMsg(msg))
        }
    }

    @JvmStatic
    fun e(msg: String, ex: Throwable?) {
        if (logLevel <= Log.ERROR) {
            Log.e(logTag(), handleEmptyMsg(msg), ex)
        }
    }

    @JvmStatic
    fun e(tag: String, msg: String, ex: Throwable?) {
        if (logLevel <= Log.ERROR) {
            Log.e(logTag(tag), handleEmptyMsg(msg), ex)
        }
    }

    @JvmStatic
    private fun handleEmptyMsg(msg: String): String =
        if (msg.isEmpty()) "null or empty" else msg
}