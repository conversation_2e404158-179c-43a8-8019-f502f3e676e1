#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32744 bytes for ChunkPool::allocate
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:78), pid=14924, tid=14292
#
# JRE version: Java(TM) SE Runtime Environment (17.0.8+9) (build 17.0.8+9-LTS-211)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\OTA\build\20250901_12888370782525868993.compiler.options

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Sep  1 12:32:52 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 6.164497 seconds (0d 0h 0m 6s)

---------------  T H R E A D  ---------------

Current thread (0x0000018c205c99f0):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=14292, stack(0x0000004d87200000,0x0000004d87300000)]


Current CompileTask:
C2:   6164 2887       4       org.jetbrains.kotlin.metadata.ProtoBuf$Type::writeTo (354 bytes)

Stack: [0x0000004d87200000,0x0000004d87300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x677d0a]
V  [jvm.dll+0x7d8c54]
V  [jvm.dll+0x7da3fe]
V  [jvm.dll+0x7daa63]
V  [jvm.dll+0x245c5f]
V  [jvm.dll+0xaacc0]
V  [jvm.dll+0xab00c]
V  [jvm.dll+0xaab84]
V  [jvm.dll+0x7c246c]
V  [jvm.dll+0x1b8ec1]
V  [jvm.dll+0x1b45f9]
V  [jvm.dll+0x69ee8b]
V  [jvm.dll+0x687d60]
V  [jvm.dll+0x685854]
V  [jvm.dll+0x1a6d0c]
V  [jvm.dll+0x2aa7fc]
V  [jvm.dll+0x690303]
V  [jvm.dll+0x6882d6]
V  [jvm.dll+0x687920]
V  [jvm.dll+0x68582a]
V  [jvm.dll+0x1a6d0c]
V  [jvm.dll+0x2aa7fc]
V  [jvm.dll+0x690303]
V  [jvm.dll+0x6882d6]
V  [jvm.dll+0x687920]
V  [jvm.dll+0x68582a]
V  [jvm.dll+0x1a6d0c]
V  [jvm.dll+0x2aa7fc]
V  [jvm.dll+0x690303]
V  [jvm.dll+0x6882d6]
V  [jvm.dll+0x687920]
V  [jvm.dll+0x68582a]
V  [jvm.dll+0x1a6d0c]
V  [jvm.dll+0x2aa7fc]
V  [jvm.dll+0x690303]
V  [jvm.dll+0x6882d6]
V  [jvm.dll+0x687920]
V  [jvm.dll+0x68582a]
V  [jvm.dll+0x1a6d0c]
V  [jvm.dll+0x1a789b]
V  [jvm.dll+0x2aa7fc]
V  [jvm.dll+0x690303]
V  [jvm.dll+0x6882d6]
V  [jvm.dll+0x687920]
V  [jvm.dll+0x68582a]
V  [jvm.dll+0x1a6d0c]
V  [jvm.dll+0x1a789b]
V  [jvm.dll+0x2aa7fc]
V  [jvm.dll+0x690303]
V  [jvm.dll+0x6882d6]
V  [jvm.dll+0x687920]
V  [jvm.dll+0x68582a]
V  [jvm.dll+0x1a6d0c]
V  [jvm.dll+0x1a789b]
V  [jvm.dll+0x2aa7fc]
V  [jvm.dll+0x690303]
V  [jvm.dll+0x6882d6]
V  [jvm.dll+0x687920]
V  [jvm.dll+0x68582a]
V  [jvm.dll+0x1a6d0c]
V  [jvm.dll+0x1a789b]
V  [jvm.dll+0x2aa7fc]
V  [jvm.dll+0x690303]
V  [jvm.dll+0x6882d6]
V  [jvm.dll+0x687920]
V  [jvm.dll+0x68582a]
V  [jvm.dll+0x1a6d0c]
V  [jvm.dll+0x216fe8]
V  [jvm.dll+0x1a3af0]
V  [jvm.dll+0x2270be]
V  [jvm.dll+0x22535b]
V  [jvm.dll+0x78e7bc]
V  [jvm.dll+0x788bba]
V  [jvm.dll+0x676b35]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000018c20d580d0, length=22, elements={
0x0000018babec2340, 0x0000018bd8630400, 0x0000018bd8631180, 0x0000018bd864f5a0,
0x0000018bd864fe60, 0x0000018bd8650720, 0x0000018bd8650fe0, 0x0000018bd8651bd0,
0x0000018bd865d9e0, 0x0000018bd8675300, 0x0000018c1a172a30, 0x0000018c1a186fa0,
0x0000018c1a18e010, 0x0000018c206d9820, 0x0000018c2023c670, 0x0000018c205c99f0,
0x0000018c1fb82f30, 0x0000018c2031cc20, 0x0000018c2031d130, 0x0000018c20037300,
0x0000018c20cfe460, 0x0000018c20cfef50
}

Java Threads: ( => current thread )
  0x0000018babec2340 JavaThread "main" [_thread_in_vm, id=17644, stack(0x0000004d84d00000,0x0000004d84e00000)]
  0x0000018bd8630400 JavaThread "Reference Handler" daemon [_thread_blocked, id=7468, stack(0x0000004d85400000,0x0000004d85500000)]
  0x0000018bd8631180 JavaThread "Finalizer" daemon [_thread_blocked, id=24592, stack(0x0000004d85500000,0x0000004d85600000)]
  0x0000018bd864f5a0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=20640, stack(0x0000004d85600000,0x0000004d85700000)]
  0x0000018bd864fe60 JavaThread "Attach Listener" daemon [_thread_blocked, id=19104, stack(0x0000004d85700000,0x0000004d85800000)]
  0x0000018bd8650720 JavaThread "Service Thread" daemon [_thread_blocked, id=18752, stack(0x0000004d85800000,0x0000004d85900000)]
  0x0000018bd8650fe0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=17268, stack(0x0000004d85900000,0x0000004d85a00000)]
  0x0000018bd8651bd0 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=14452, stack(0x0000004d85a00000,0x0000004d85b00000)]
  0x0000018bd865d9e0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=24212, stack(0x0000004d85b00000,0x0000004d85c00000)]
  0x0000018bd8675300 JavaThread "Sweeper thread" daemon [_thread_blocked, id=18148, stack(0x0000004d85c00000,0x0000004d85d00000)]
  0x0000018c1a172a30 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=18976, stack(0x0000004d85d00000,0x0000004d85e00000)]
  0x0000018c1a186fa0 JavaThread "Notification Thread" daemon [_thread_blocked, id=16088, stack(0x0000004d85e00000,0x0000004d85f00000)]
  0x0000018c1a18e010 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=27260, stack(0x0000004d86100000,0x0000004d86200000)]
  0x0000018c206d9820 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=15844, stack(0x0000004d85f00000,0x0000004d86000000)]
  0x0000018c2023c670 JavaThread "C1 CompilerThread3" daemon [_thread_in_native, id=28224, stack(0x0000004d86e00000,0x0000004d86f00000)]
=>0x0000018c205c99f0 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=14292, stack(0x0000004d87200000,0x0000004d87300000)]
  0x0000018c1fb82f30 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=26360, stack(0x0000004d87300000,0x0000004d87400000)]
  0x0000018c2031cc20 JavaThread "C2 CompilerThread3" daemon [_thread_in_native, id=6060, stack(0x0000004d87400000,0x0000004d87500000)]
  0x0000018c2031d130 JavaThread "C2 CompilerThread4" daemon [_thread_in_native, id=18452, stack(0x0000004d87500000,0x0000004d87600000)]
  0x0000018c20037300 JavaThread "C2 CompilerThread5" daemon [_thread_in_native, id=20124, stack(0x0000004d87600000,0x0000004d87700000)]
  0x0000018c20cfe460 JavaThread "C2 CompilerThread6" daemon [_thread_in_native, id=25848, stack(0x0000004d87700000,0x0000004d87800000)]
  0x0000018c20cfef50 JavaThread "C2 CompilerThread7" daemon [_thread_in_native, id=18328, stack(0x0000004d87800000,0x0000004d87900000)]

Other Threads:
  0x0000018bd862c900 VMThread "VM Thread" [stack: 0x0000004d85300000,0x0000004d85400000] [id=3520]
  0x0000018bd84905f0 WatcherThread [stack: 0x0000004d86000000,0x0000004d86100000] [id=17184]
  0x0000018babf71330 GCTaskThread "GC Thread#0" [stack: 0x0000004d84e00000,0x0000004d84f00000] [id=22404]
  0x0000018c1a6979c0 GCTaskThread "GC Thread#1" [stack: 0x0000004d86300000,0x0000004d86400000] [id=16176]
  0x0000018c1a697c70 GCTaskThread "GC Thread#2" [stack: 0x0000004d86400000,0x0000004d86500000] [id=15480]
  0x0000018c1a697f20 GCTaskThread "GC Thread#3" [stack: 0x0000004d86500000,0x0000004d86600000] [id=22932]
  0x0000018c1a646f40 GCTaskThread "GC Thread#4" [stack: 0x0000004d86600000,0x0000004d86700000] [id=25212]
  0x0000018c1a6471f0 GCTaskThread "GC Thread#5" [stack: 0x0000004d86700000,0x0000004d86800000] [id=3168]
  0x0000018c1a6474a0 GCTaskThread "GC Thread#6" [stack: 0x0000004d86800000,0x0000004d86900000] [id=3136]
  0x0000018c1a647750 GCTaskThread "GC Thread#7" [stack: 0x0000004d86900000,0x0000004d86a00000] [id=17840]
  0x0000018c1a6d9640 GCTaskThread "GC Thread#8" [stack: 0x0000004d86a00000,0x0000004d86b00000] [id=22212]
  0x0000018c1a6d98f0 GCTaskThread "GC Thread#9" [stack: 0x0000004d86b00000,0x0000004d86c00000] [id=388]
  0x0000018c1a6db170 GCTaskThread "GC Thread#10" [stack: 0x0000004d86c00000,0x0000004d86d00000] [id=1016]
  0x0000018c1a6db6d0 GCTaskThread "GC Thread#11" [stack: 0x0000004d86d00000,0x0000004d86e00000] [id=17468]
  0x0000018c2034bb10 GCTaskThread "GC Thread#12" [stack: 0x0000004d86200000,0x0000004d86300000] [id=28552]
  0x0000018babf82f50 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000004d84f00000,0x0000004d85000000] [id=6744]
  0x0000018babf83870 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000004d85000000,0x0000004d85100000] [id=24276]
  0x0000018c1a6d9ea0 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000004d86f00000,0x0000004d87000000] [id=24536]
  0x0000018c1a6da150 ConcurrentGCThread "G1 Conc#2" [stack: 0x0000004d87000000,0x0000004d87100000] [id=26180]
  0x0000018bd8565a90 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000004d85100000,0x0000004d85200000] [id=24560]
  0x0000018c1f9e7f40 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000004d87100000,0x0000004d87200000] [id=12088]
  0x0000018bd85663c0 ConcurrentGCThread "G1 Service" [stack: 0x0000004d85200000,0x0000004d85300000] [id=16264]

Threads with active compile tasks:
C2 CompilerThread0     6194 2846   !   4       org.jetbrains.kotlin.metadata.ProtoBuf$Type::<init> (875 bytes)
C1 CompilerThread3     6194 2946   !   3       org.jetbrains.kotlin.storage.LockBasedStorageManager$MapBasedMemoizedFunction::invoke (410 bytes)
C2 CompilerThread1     6194 2887       4       org.jetbrains.kotlin.metadata.ProtoBuf$Type::writeTo (354 bytes)
C2 CompilerThread2     6194 2850   !   4       org.jetbrains.kotlin.metadata.ProtoBuf$Function::<init> (1410 bytes)
C2 CompilerThread3     6195 2873       4       org.jetbrains.kotlin.protobuf.GeneratedMessageLite$ExtendableMessage$ExtensionWriter::<init> (57 bytes)
C2 CompilerThread4     6195 2921   !   4       org.jetbrains.kotlin.metadata.ProtoBuf$Type$Argument::<init> (401 bytes)
C2 CompilerThread5     6195 2904       4       org.jetbrains.kotlin.metadata.jvm.deserialization.UtfEncodingKt::stringsToBytes (173 bytes)
C2 CompilerThread6     6195 2931       4       org.jetbrains.kotlin.metadata.ProtoBuf$Function::getSerializedSize (512 bytes)
C2 CompilerThread7     6195 2927       4       org.jetbrains.kotlin.protobuf.FieldSet::getSerializedSize (109 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000604800000, size: 8120 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000018bd9000000-0x0000018bd9bd0000-0x0000018bd9bd0000), size 12386304, SharedBaseAddress: 0x0000018bd9000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000018bda000000-0x0000018c1a000000, reserved size: 1073741824
Narrow klass base: 0x0000018bd9000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32479M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8120M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 69632K, used 29353K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 1 survivors (4096K)
 Metaspace       used 38274K, committed 38528K, reserved 1114112K
  class space    used 4673K, committed 4800K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%|HS|  |TAMS 0x0000000604c00000, 0x0000000604800000| Complete 
|   1|0x0000000604c00000, 0x0000000604caa600, 0x0000000605000000| 16%| O|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   2|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605400000, 0x0000000605000000| Untracked 
|   3|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%| O|  |TAMS 0x0000000605800000, 0x0000000605400000| Untracked 
|   4|0x0000000605800000, 0x0000000605c00000, 0x0000000605c00000|100%| O|  |TAMS 0x0000000605c00000, 0x0000000605800000| Untracked 
|   5|0x0000000605c00000, 0x0000000606000000, 0x0000000606000000|100%| O|  |TAMS 0x0000000605d5d600, 0x0000000605c00000| Untracked 
|   6|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|   7|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|   8|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|   9|0x0000000606c00000, 0x0000000607000000, 0x0000000607000000|100%| S|CS|TAMS 0x0000000606c00000, 0x0000000606c00000| Complete 
|  10|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  11|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  12|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  13|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
| 114|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 115|0x0000000621400000, 0x00000006214ca898, 0x0000000621800000| 19%| E|  |TAMS 0x0000000621400000, 0x0000000621400000| Complete 
| 126|0x0000000624000000, 0x0000000624400000, 0x0000000624400000|100%| E|CS|TAMS 0x0000000624000000, 0x0000000624000000| Complete 

Card table byte_map: [0x0000018bc3fe0000,0x0000018bc4fc0000] _byte_map_base: 0x0000018bc0fbc000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000018babf72860, (CMBitMap*) 0x0000018babf728a0
 Prev Bits: [0x0000018bc5fa0000, 0x0000018bcde80000)
 Next Bits: [0x0000018bcde80000, 0x0000018bd5d60000)

Polling page: 0x0000018ba9d40000

Metaspace:

Usage:
  Non-class:     32.81 MB used.
      Class:      4.56 MB used.
       Both:     37.38 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      32.94 MB ( 51%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       4.69 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      37.62 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  13.53 MB
       Class:  11.33 MB
        Both:  24.86 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 53.44 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 172.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 600.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 1469.
num_chunk_merges: 6.
num_chunk_splits: 1216.
num_chunks_enlarged: 1107.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=1448Kb max_used=1448Kb free=117719Kb
 bounds [0x0000018bbb310000, 0x0000018bbb580000, 0x0000018bc2770000]
CodeHeap 'profiled nmethods': size=119104Kb used=8008Kb max_used=8008Kb free=111095Kb
 bounds [0x0000018bb3770000, 0x0000018bb3f50000, 0x0000018bbabc0000]
CodeHeap 'non-nmethods': size=7488Kb used=2963Kb max_used=2998Kb free=4525Kb
 bounds [0x0000018bbabc0000, 0x0000018bbaec0000, 0x0000018bbb310000]
 total_blobs=4176 nmethods=3588 adapters=497
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 5.515 Thread 0x0000018c2023c670 nmethod 2883 0x0000018bb3d48190 code [0x0000018bb3d485c0, 0x0000018bb3d4a7e8]
Event: 5.517 Thread 0x0000018c206d9820 nmethod 2886 0x0000018bb3d4b490 code [0x0000018bb3d4ba20, 0x0000018bb3d4e098]
Event: 5.520 Thread 0x0000018c1a172a30 2888       1       org.jetbrains.kotlin.load.kotlin.header.KotlinClassHeader::getMetadataVersion (5 bytes)
Event: 5.520 Thread 0x0000018c1a172a30 nmethod 2888 0x0000018bbb452b90 code [0x0000018bbb452d20, 0x0000018bbb452df8]
Event: 5.521 Thread 0x0000018c2023c670 2889       1       org.jetbrains.kotlin.load.kotlin.JvmPackagePartSource::getClassName (5 bytes)
Event: 5.521 Thread 0x0000018c2023c670 nmethod 2889 0x0000018bbb452e90 code [0x0000018bbb453020, 0x0000018bbb4530f8]
Event: 5.521 Thread 0x0000018bd865d9e0 2890       3       org.jetbrains.kotlin.storage.LockBasedStorageManager$MapBasedMemoizedFunctionToNotNull::<init> (32 bytes)
Event: 5.521 Thread 0x0000018bd865d9e0 nmethod 2890 0x0000018bb3d4f010 code [0x0000018bb3d4f1e0, 0x0000018bb3d4f498]
Event: 5.522 Thread 0x0000018c1a172a30 2891       3       org.jetbrains.kotlin.storage.LockBasedStorageManager::createMemoizedFunction (27 bytes)
Event: 5.522 Thread 0x0000018c1a172a30 nmethod 2891 0x0000018bb3d4f590 code [0x0000018bb3d4f760, 0x0000018bb3d4fa38]
Event: 5.527 Thread 0x0000018c205c99f0 nmethod 2877 0x0000018bbb453190 code [0x0000018bbb4533e0, 0x0000018bbb453f40]
Event: 5.527 Thread 0x0000018c205c99f0 2887       4       org.jetbrains.kotlin.metadata.ProtoBuf$Type::writeTo (354 bytes)
Event: 5.529 Thread 0x0000018c1a172a30 2893       3       org.jetbrains.kotlin.load.kotlin.header.KotlinClassHeader::has (12 bytes)
Event: 5.529 Thread 0x0000018c1a172a30 nmethod 2893 0x0000018bb3d4fb90 code [0x0000018bb3d4fd20, 0x0000018bb3d4fe98]
Event: 5.529 Thread 0x0000018c1a172a30 2894       3       java.util.LinkedHashMap::entrySet (27 bytes)
Event: 5.529 Thread 0x0000018c2023c670 2895       3       java.util.LinkedHashMap$LinkedEntrySet::iterator (12 bytes)
Event: 5.529 Thread 0x0000018c206d9820 2896       3       java.util.LinkedHashMap$LinkedEntryIterator::<init> (11 bytes)
Event: 5.529 Thread 0x0000018c206d9820 nmethod 2896 0x0000018bb3d4ff10 code [0x0000018bb3d500c0, 0x0000018bb3d503f8]
Event: 5.529 Thread 0x0000018c1a172a30 nmethod 2894 0x0000018bb3d50510 code [0x0000018bb3d506e0, 0x0000018bb3d50af8]
Event: 5.529 Thread 0x0000018c2023c670 nmethod 2895 0x0000018bb3d50c90 code [0x0000018bb3d50e40, 0x0000018bb3d51218]

GC Heap History (12 events):
Event: 0.667 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 520192K, used 24576K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 7000K, committed 7104K, reserved 1114112K
  class space    used 727K, committed 768K, reserved 1048576K
}
Event: 0.671 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 7008K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 7000K, committed 7104K, reserved 1114112K
  class space    used 727K, committed 768K, reserved 1048576K
}
Event: 1.441 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 520192K, used 31584K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 15285K, committed 15552K, reserved 1114112K
  class space    used 2019K, committed 2112K, reserved 1048576K
}
Event: 1.443 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 9168K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 15285K, committed 15552K, reserved 1114112K
  class space    used 2019K, committed 2112K, reserved 1048576K
}
Event: 2.793 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 520192K, used 41936K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 21364K, committed 21504K, reserved 1114112K
  class space    used 2889K, committed 2944K, reserved 1048576K
}
Event: 2.797 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 520192K, used 14681K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 21364K, committed 21504K, reserved 1114112K
  class space    used 2889K, committed 2944K, reserved 1048576K
}
Event: 4.018 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 69632K, used 47449K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 10 young (40960K), 2 survivors (8192K)
 Metaspace       used 27122K, committed 27328K, reserved 1114112K
  class space    used 3435K, committed 3520K, reserved 1048576K
}
Event: 4.024 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 69632K, used 18016K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 27122K, committed 27328K, reserved 1114112K
  class space    used 3435K, committed 3520K, reserved 1048576K
}
Event: 4.201 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 69632K, used 22112K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 28248K, committed 28416K, reserved 1114112K
  class space    used 3566K, committed 3648K, reserved 1048576K
}
Event: 4.208 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 69632K, used 18475K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 28248K, committed 28416K, reserved 1114112K
  class space    used 3566K, committed 3648K, reserved 1048576K
}
Event: 5.361 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 69632K, used 43051K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 32605K, committed 32832K, reserved 1114112K
  class space    used 4048K, committed 4160K, reserved 1048576K
}
Event: 5.366 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 69632K, used 21012K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 32605K, committed 32832K, reserved 1114112K
  class space    used 4048K, committed 4160K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 5.028 Thread 0x0000018babec2340 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000018bbb414c80 relative=0x00000000000001c0
Event: 5.028 Thread 0x0000018babec2340 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000018bbb414c80 method=java.lang.Character.codePointAt(Ljava/lang/CharSequence;I)I @ 2 c2
Event: 5.028 Thread 0x0000018babec2340 DEOPT PACKING pc=0x0000018bbb414c80 sp=0x0000004d84dfcf30
Event: 5.028 Thread 0x0000018babec2340 DEOPT UNPACKING pc=0x0000018bbac123a3 sp=0x0000004d84dfcee8 mode 2
Event: 5.028 Thread 0x0000018babec2340 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000018bbb414c80 relative=0x00000000000001c0
Event: 5.028 Thread 0x0000018babec2340 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000018bbb414c80 method=java.lang.Character.codePointAt(Ljava/lang/CharSequence;I)I @ 2 c2
Event: 5.028 Thread 0x0000018babec2340 DEOPT PACKING pc=0x0000018bbb414c80 sp=0x0000004d84dfcf30
Event: 5.028 Thread 0x0000018babec2340 DEOPT UNPACKING pc=0x0000018bbac123a3 sp=0x0000004d84dfcee8 mode 2
Event: 5.028 Thread 0x0000018babec2340 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000018bbb418fe8 relative=0x0000000000000a28
Event: 5.028 Thread 0x0000018babec2340 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000018bbb418fe8 method=java.lang.Character.codePointAt(Ljava/lang/CharSequence;I)I @ 2 c2
Event: 5.028 Thread 0x0000018babec2340 DEOPT PACKING pc=0x0000018bbb418fe8 sp=0x0000004d84dfcfd0
Event: 5.028 Thread 0x0000018babec2340 DEOPT UNPACKING pc=0x0000018bbac123a3 sp=0x0000004d84dfcef0 mode 2
Event: 5.040 Thread 0x0000018babec2340 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018bbb41ebf8 relative=0x0000000000000078
Event: 5.040 Thread 0x0000018babec2340 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018bbb41ebf8 method=org.jetbrains.kotlin.parsing.AbstractKotlinParsing.tokenMatches(Lorg/jetbrains/kotlin/com/intellij/psi/tree/IElementType;Lorg/jetbrains/kotlin/com/intellij/psi/tree/IElement
Event: 5.040 Thread 0x0000018babec2340 DEOPT PACKING pc=0x0000018bbb41ebf8 sp=0x0000004d84dfa970
Event: 5.040 Thread 0x0000018babec2340 DEOPT UNPACKING pc=0x0000018bbac123a3 sp=0x0000004d84dfa8f0 mode 2
Event: 5.303 Thread 0x0000018babec2340 Uncommon trap: trap_request=0xffffffcc fr.pc=0x0000018bbb44a504 relative=0x0000000000000364
Event: 5.303 Thread 0x0000018babec2340 Uncommon trap: reason=intrinsic_or_type_checked_inlining action=make_not_entrant pc=0x0000018bbb44a504 method=java.util.Arrays.copyOf([Ljava/lang/Object;ILjava/lang/Class;)[Ljava/lang/Object; @ 35 c2
Event: 5.303 Thread 0x0000018babec2340 DEOPT PACKING pc=0x0000018bbb44a504 sp=0x0000004d84dfbfa0
Event: 5.303 Thread 0x0000018babec2340 DEOPT UNPACKING pc=0x0000018bbac123a3 sp=0x0000004d84dfbf30 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.758 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623c843b8}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int, int)'> (0x0000000623c843b8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.760 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623cdadf0}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, long, long)'> (0x0000000623cdadf0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.761 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623ce9408}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int)'> (0x0000000623ce9408) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.773 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623d92908}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623d92908) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.777 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623dbb750}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623dbb750) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.795 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623e9a768}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623e9a768) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.897 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x00000006239bd520}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006239bd520) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.000 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623580838}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000623580838) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.188 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x000000062323d510}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x000000062323d510) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.196 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x00000006232b7a90}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006232b7a90) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.224 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623372300}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623372300) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 2.304 Thread 0x0000018babec2340 Implicit null exception at 0x0000018bbb338b72 to 0x0000018bbb3391cc
Event: 2.535 Thread 0x0000018babec2340 Implicit null exception at 0x0000018bbb33c072 to 0x0000018bbb33c6ec
Event: 2.603 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x000000062275c2e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062275c2e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.248 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x00000006240d1f30}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x00000006240d1f30) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.357 Thread 0x0000018babec2340 Implicit null exception at 0x0000018bbb40a6f1 to 0x0000018bbb40a894
Event: 4.364 Thread 0x0000018babec2340 Implicit null exception at 0x0000018bbb40add3 to 0x0000018bbb40aef0
Event: 4.506 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x00000006210b9728}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006210b9728) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.506 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x00000006210bddb0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006210bddb0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 4.506 Thread 0x0000018babec2340 Exception <a 'java/lang/NoSuchMethodError'{0x00000006210c1c20}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006210c1c20) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 4.018 Executing VM operation: G1CollectForAllocation
Event: 4.024 Executing VM operation: G1CollectForAllocation done
Event: 4.201 Executing VM operation: G1CollectForAllocation
Event: 4.211 Executing VM operation: G1CollectForAllocation done
Event: 4.477 Executing VM operation: HandshakeAllThreads
Event: 4.483 Executing VM operation: HandshakeAllThreads done
Event: 4.502 Executing VM operation: HandshakeAllThreads
Event: 4.502 Executing VM operation: HandshakeAllThreads done
Event: 4.735 Executing VM operation: HandshakeAllThreads
Event: 4.735 Executing VM operation: HandshakeAllThreads done
Event: 4.833 Executing VM operation: ICBufferFull
Event: 4.833 Executing VM operation: ICBufferFull done
Event: 4.866 Executing VM operation: HandshakeAllThreads
Event: 4.866 Executing VM operation: HandshakeAllThreads done
Event: 5.360 Executing VM operation: G1CollectForAllocation
Event: 5.366 Executing VM operation: G1CollectForAllocation done
Event: 5.380 Executing VM operation: G1Concurrent
Event: 5.497 Executing VM operation: G1Concurrent done
Event: 5.501 Executing VM operation: G1Concurrent
Event: 5.502 Executing VM operation: G1Concurrent done

Events (20 events):
Event: 4.871 loading class java/util/stream/IntPipeline$1 done
Event: 4.874 loading class java/util/stream/IntPipeline$1$1
Event: 4.874 loading class java/util/stream/IntPipeline$1$1 done
Event: 4.926 Thread 0x0000018c2056bfe0 Thread exited: 0x0000018c2056bfe0
Event: 4.940 loading class java/io/InvalidObjectException
Event: 4.940 loading class java/io/InvalidObjectException done
Event: 4.941 Thread 0x0000018c2056bad0 Thread exited: 0x0000018c2056bad0
Event: 4.952 loading class java/util/AbstractList$SubList$1
Event: 4.952 loading class java/util/AbstractList$SubList$1 done
Event: 5.022 loading class java/util/ArrayList$SubList$1
Event: 5.022 loading class java/util/ArrayList$SubList$1 done
Event: 5.187 Thread 0x0000018c205c9b20 Thread exited: 0x0000018c205c9b20
Event: 5.187 Thread 0x0000018c1ffc33b0 Thread exited: 0x0000018c1ffc33b0
Event: 5.372 Thread 0x0000018c205c99f0 Thread added: 0x0000018c205c99f0
Event: 5.373 Thread 0x0000018c1fb82f30 Thread added: 0x0000018c1fb82f30
Event: 5.510 Thread 0x0000018c2031cc20 Thread added: 0x0000018c2031cc20
Event: 5.511 Thread 0x0000018c2031d130 Thread added: 0x0000018c2031d130
Event: 5.511 Thread 0x0000018c20037300 Thread added: 0x0000018c20037300
Event: 5.511 Thread 0x0000018c20cfe460 Thread added: 0x0000018c20cfe460
Event: 5.517 Thread 0x0000018c20cfef50 Thread added: 0x0000018c20cfef50


Dynamic libraries:
0x00007ff708300000 - 0x00007ff708310000 	E:\JAVA\bin\java.exe
0x00007ff827030000 - 0x00007ff827228000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8255d0000 - 0x00007ff825692000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff824d50000 - 0x00007ff825046000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8246c0000 - 0x00007ff8247c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff81fde0000 - 0x00007ff81fdf9000 	E:\JAVA\bin\jli.dll
0x00007ff826e80000 - 0x00007ff826f31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff826340000 - 0x00007ff8263de000 	C:\Windows\System32\msvcrt.dll
0x00007ff81fdc0000 - 0x00007ff81fddb000 	E:\JAVA\bin\VCRUNTIME140.dll
0x00007ff825e80000 - 0x00007ff825f1f000 	C:\Windows\System32\sechost.dll
0x00007ff825b10000 - 0x00007ff825c33000 	C:\Windows\System32\RPCRT4.dll
0x00007ff824a20000 - 0x00007ff824a47000 	C:\Windows\System32\bcrypt.dll
0x00007ff825050000 - 0x00007ff8251ed000 	C:\Windows\System32\USER32.dll
0x00007ff8248a0000 - 0x00007ff8248c2000 	C:\Windows\System32\win32u.dll
0x00007ff81dfe0000 - 0x00007ff81e27a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff825da0000 - 0x00007ff825dcb000 	C:\Windows\System32\GDI32.dll
0x00007ff824a50000 - 0x00007ff824b69000 	C:\Windows\System32\gdi32full.dll
0x00007ff824980000 - 0x00007ff824a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff823400000 - 0x00007ff82340a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8253f0000 - 0x00007ff82541f000 	C:\Windows\System32\IMM32.DLL
0x00007ff81fd50000 - 0x00007ff81fd5c000 	E:\JAVA\bin\vcruntime140_1.dll
0x00007ffffb0c0000 - 0x00007ffffb14e000 	E:\JAVA\bin\msvcp140.dll
0x00007fffe0350000 - 0x00007fffe0f2e000 	E:\JAVA\bin\server\jvm.dll
0x00007ff826f40000 - 0x00007ff826f48000 	C:\Windows\System32\PSAPI.DLL
0x00007fffeb340000 - 0x00007fffeb349000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff826e10000 - 0x00007ff826e7b000 	C:\Windows\System32\WS2_32.dll
0x00007ff81e3c0000 - 0x00007ff81e3e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff823360000 - 0x00007ff823372000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff81c4f0000 - 0x00007ff81c4fa000 	E:\JAVA\bin\jimage.dll
0x00007ff821e10000 - 0x00007ff822011000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff80fef0000 - 0x00007ff80ff24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff824810000 - 0x00007ff824892000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff818d20000 - 0x00007ff818d45000 	E:\JAVA\bin\java.dll
0x00007fffef390000 - 0x00007fffef467000 	E:\JAVA\bin\jsvml.dll
0x00007ff826570000 - 0x00007ff826cde000 	C:\Windows\System32\SHELL32.dll
0x00007ff8222d0000 - 0x00007ff822a74000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff825fd0000 - 0x00007ff826323000 	C:\Windows\System32\combase.dll
0x00007ff8240f0000 - 0x00007ff82411b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff826d40000 - 0x00007ff826e0d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff825f20000 - 0x00007ff825fcd000 	C:\Windows\System32\SHCORE.dll
0x00007ff825d40000 - 0x00007ff825d9b000 	C:\Windows\System32\shlwapi.dll
0x00007ff8245f0000 - 0x00007ff824614000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff811d70000 - 0x00007ff811d89000 	E:\JAVA\bin\net.dll
0x00007ff81ede0000 - 0x00007ff81eeea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff823e50000 - 0x00007ff823eba000 	C:\Windows\system32\mswsock.dll
0x00007ff810fb0000 - 0x00007ff810fc6000 	E:\JAVA\bin\nio.dll
0x00007ff81bc20000 - 0x00007ff81bc38000 	E:\JAVA\bin\zip.dll
0x00007ff81c4d0000 - 0x00007ff81c4e0000 	E:\JAVA\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\JAVA\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;E:\JAVA\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\OTA\build\20250901_12888370782525868993.compiler.options
java_class_path (initial): E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.22\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\kotlin-compiler-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.22\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\kotlin-script-runtime-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.22\20e2c5df715f3240c765cfc222530e2796542021\kotlin-daemon-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8514437120                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8514437120                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\JAVA
CLASSPATH=.;E:\JAVA\lib\dt.jar;E:\JAVA\lib\tools.jar;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\dotnet\;E:\git\Git\cmd;D:\Users\W9096060\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\dotnet\;E:\1.8java\lib\dt.jar;E:\1.8java\lib\tools.jar;E:\1.8java\bin;E:\JAVA\bin;E:\JAVA\lib;D:\Users\W9096060\AppData\Local\Microsoft\WindowsApps;
USERNAME=W9096060
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 19:56 hours

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 32479M (2632M free)
TotalPageFile size 47840M (AvailPageFile size 282M)
current process WorkingSet (physical memory assigned to process): 197M, peak: 197M
current process commit charge ("private bytes"): 251M, peak: 678M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211) for windows-amd64 JRE (17.0.8+9-LTS-211), built on Jun 14 2023 10:34:31 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
