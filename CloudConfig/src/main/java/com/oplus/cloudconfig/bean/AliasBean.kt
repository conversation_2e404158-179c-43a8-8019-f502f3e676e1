/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AliasBean.kt
 * * Description : CloudConfig Alias Bean
 * * Version     : 1.0
 * * Date        : 2020/9/7
 * * Author      : W9001165
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.cloudconfig.bean

import androidx.annotation.Keep
import com.oplus.nearx.cloudconfig.anotation.FieldIndex

@Keep
data class AliasBean(
        @FieldIndex(index = 1)
        val mPath: String = "",

        @FieldIndex(index = 2)
        val mPackageName: String = "",

        @FieldIndex(index = 3)
        val mAlias: String = "",

        @FieldIndex(index = 4)
        val mDefault: String = ""
) {
    fun isEmpty(): Boolean = mPath.isEmpty() || mPackageName.isEmpty() || mAlias.isEmpty() || mDefault.isEmpty()
    fun isNotEmpty(): Boolean = isEmpty().not()
}