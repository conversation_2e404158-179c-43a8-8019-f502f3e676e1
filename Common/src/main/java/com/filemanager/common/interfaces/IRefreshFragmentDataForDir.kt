/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: IRefreshFragmentDataForCreateDir.kt
 ** Description: Refresh all files page from the fragment
 ** Version: 1.0
 ** Date: 2024/12/27
 ** Author: zhangyitong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.interfaces

import com.filemanager.common.base.BaseFileBean

interface IRefreshFragmentDataForDir {
    fun refreshDataForDir(path: String, category: Int)

    fun renameToShortCutFolder(newName: String, file: BaseFileBean): String

    fun renameToLabel(newName: String, labelId: Long)

    fun onClickDir(path: String)
}