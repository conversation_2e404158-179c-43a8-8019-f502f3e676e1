/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - DeleteOperationTest.java
 * Description: Test case for delete Operation
 * Version: 1.0
 * Date : 2020/04/20
 * Author: <PERSON><PERSON><PERSON>.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/04/20    1.0     create
 ****************************************************************/

package com.coloros.filemanager.recyclebin.operation

import androidx.activity.ComponentActivity
import com.coloros.filemanager.BaseTest
import com.filemanager.common.base.BaseFileBean
import com.filemanager.recyclebin.operation.DeleteOperation
import com.filemanager.recyclebin.operation.listener.DeleteOperationListener
import org.junit.After
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import shadow.ShadowViewHelper
import shadow.ShadowVolumeEnvironment

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
@Config(shadows = [ShadowViewHelper::class, ShadowVolumeEnvironment::class])
class DeleteOperationTest : BaseTest() {
    private lateinit var mActivity: ComponentActivity

    @Before
    override fun setUp() {
        mActivity = Robolectric.buildActivity(ComponentActivity::class.java).get()
    }

    @After
    override fun tearDown() {
    }

    @Test
    fun should_vertify_when_addPaths_isNull() {
        val progressListener = DeleteOperationListener(mActivity, null);
        val deleteOperation = DeleteOperation(mActivity, progressListener)
        assertFalse(deleteOperation.addPaths(null))
        assertTrue(deleteOperation.addPaths(ArrayList<String>()))
        assertFalse(deleteOperation.addPaths(ArrayList<String>()))
    }

    @Test
    fun should_vertify_when_addFileBeans_isNull() {
        val progressListener = DeleteOperationListener(mActivity, null);
        val deleteOperation = DeleteOperation(mActivity, progressListener)
        assertFalse(deleteOperation.addFileBeans(null))
        assertTrue(deleteOperation.addFileBeans(ArrayList<BaseFileBean>()))
        assertFalse(deleteOperation.addFileBeans(ArrayList<BaseFileBean>()))
    }

}