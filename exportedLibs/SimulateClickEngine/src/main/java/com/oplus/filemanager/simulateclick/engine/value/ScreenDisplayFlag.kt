/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScreenDisplayFlag.kt
 * Description:
 *     The flag for configuring display type in Script.
 *
 * Version: 1.0
 * Date: 2024-06-04
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><EMAIL>    2024-06-04   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.value

import com.google.gson.GsonBuilder
import com.oplus.filemanager.simulateclick.engine.utils.convertAliasToFlagValue
import com.oplus.filemanager.simulateclick.engine.utils.convertFlagValueToAlias

internal data class ScreenDisplayFlag(override val flag: Int = DEFAULT) : IScriptFlagValue {

    constructor(aliasValue: String?) : this(fromAlias(aliasValue))

    override fun toAlias(): String = toAlias(flag)

    override fun toString(): String = "ScreenDisplayFlag(flag=${toAlias(flag)})"

    companion object {
        const val NORMAL = 0x0001
        const val UNFOLD = 0x0002
        const val PAD = 0x0004
        const val FLIP = 0x0008
        private const val DEFAULT = NORMAL or UNFOLD or PAD // Do not apply to flip when default

        private const val ALIAS_NORMAL = "normal" // Most of generic phone
        private const val ALIAS_UNFOLD = "unfold" // The large unfold screen of fold phone
        private const val ALIAS_PAD = "pad" // Tablet, Pad
        private const val ALIAS_FLIP = "flip" // The small secondary screen of flip phone

        @JvmStatic
        private fun fromAlias(aliasValue: String?): Int = convertAliasToFlagValue(
            aliasValue,
            DEFAULT,
            ALIAS_NORMAL to NORMAL,
            ALIAS_UNFOLD to UNFOLD,
            ALIAS_PAD to PAD,
            ALIAS_FLIP to FLIP
        )

        @JvmStatic
        private fun toAlias(flag: Int): String = convertFlagValueToAlias(
            flag,
            ALIAS_NORMAL to NORMAL,
            ALIAS_UNFOLD to UNFOLD,
            ALIAS_PAD to PAD,
            ALIAS_FLIP to FLIP
        )

        @JvmStatic
        fun registerJsonAdapter(gsonBuilder: GsonBuilder) {
            gsonBuilder.registerTypeAdapter(ScreenDisplayFlag::class.java, JsonAdapter())
        }
    }

    class JsonAdapter : ScriptJsonAdapter<ScreenDisplayFlag>() {
        override fun onWrite(value: ScreenDisplayFlag): String = value.toAlias()
        override fun onRead(jsonStr: String): ScreenDisplayFlag = ScreenDisplayFlag(jsonStr)
    }
}