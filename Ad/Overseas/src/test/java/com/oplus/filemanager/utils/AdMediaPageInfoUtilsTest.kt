package com.oplus.filemanager.utils

import com.filemanager.common.constants.AdResourceConstants
import io.mockk.every
import io.mockk.mockkObject
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import java.util.regex.Pattern
import java.util.UUID

/**
 * AdMediaPageInfoUtils工具类的单元测试类
 * 用于测试AdMediaPageInfoUtils中各个方法的正确性和健壮性
 */
class AdMediaPageInfoUtilsTest {

    /**
     * 在每个测试方法执行前运行的初始化方法
     * 用于模拟AdResourceConstants对象
     */
    @Before
    fun setup() {
        // 初始化模拟对象
        mockkObject(AdResourceConstants)
    }

    /**
     * 在每个测试方法执行后运行的清理方法
     * 用于清理所有模拟对象和状态
     */
    @After
    fun tearDown() {
        // 清理所有模拟对象和状态
        unmockkAll()
    }

    /**
     * 测试getMediaReqId方法
     * 验证是否能生成符合UUID格式的媒体请求ID
     */
    @Test
    fun `getMediaReqId should return valid UUID format`() {
        // 执行
        val result = AdMediaPageInfoUtils.getMediaReqId("test_pos")

        // 验证
        assertNotNull(result)
        assertTrue(Pattern.matches(UUID_REGEX, result))
    }

    /**
     * 测试getPageInfo方法
     * 验证对于已知页面类型是否能返回正确的PageInfo对象
     */
    @Test
    fun `getPageInfo should return correct PageInfo for known page types`() {
        // 准备测试数据
        val testCases = listOf(
            "apk_something" to 1001,
            "doc_something" to 1002,
            "audio_something" to 1003,
            "video_something" to 1004,
            "super_page" to 2001,
            "unknown_page" to 0
        )

        testCases.forEach { (pageName, expectedId) ->
            // 模拟依赖
            every { AdResourceConstants.getPageId(pageName) } returns expectedId

            // 执行
            val result = AdMediaPageInfoUtils.getPageInfo("test_pos", pageName)

            // 验证
            assertEquals(expectedId.toString(), result.pageId)
            assertEquals("", result.modelId)
            assertEquals("0", result.positionId)
        }
    }

    /**
     * 测试getPageInfo方法
     * 验证处理空页面名称时的行为
     */
    @Test
    fun `getPageInfo should handle empty page name`() {
        // 模拟依赖
        every { AdResourceConstants.getPageId("") } returns 0

        // 执行
        val result = AdMediaPageInfoUtils.getPageInfo("test_pos", "")

        // 验证
        assertEquals("0", result.pageId)
        assertEquals("", result.modelId)
        assertEquals("0", result.positionId)
    }

    /**
     * 测试getMediaPageInfo方法
     * 验证是否能返回正确的MediaPageInfo结构
     */
    @Test
    fun `getMediaPageInfo should return correct MediaPageInfo structure`() {
        // 模拟依赖
        val expectedPageId = 3001
        every { AdResourceConstants.getPageId("test_page") } returns expectedPageId

        // 执行
        val result = AdMediaPageInfoUtils.getMediaPageInfo("test_pos", "test_page")

        // 验证
        assertNotNull(result.mediaReqId)
        assertTrue(Pattern.matches(UUID_REGEX, result.mediaReqId))
        assertEquals(expectedPageId.toString(), result.pageId)
        assertEquals("", result.modelId)
        assertEquals("0", result.positionId)
    }

    /**
     * 测试getMediaPageInfo方法
     * 验证处理空位置ID时的行为
     */
    @Test
    fun `getMediaPageInfo should handle empty position ID`() {
        // 模拟依赖
        every { AdResourceConstants.getPageId("test_page") } returns 4001

        // 执行
        val result = AdMediaPageInfoUtils.getMediaPageInfo("", "test_page")

        // 验证
        assertNotNull(result.mediaReqId)
        assertTrue(Pattern.matches(UUID_REGEX, result.mediaReqId))
        assertEquals("4001", result.pageId)
        assertEquals("", result.modelId)
        assertEquals("0", result.positionId)
    }

    /**
     * 伴生对象
     * 包含测试中使用的常量
     */
    companion object {
        // UUID正则表达式，用于验证生成的UUID格式是否正确
        private const val UUID_REGEX = "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"
    }
}