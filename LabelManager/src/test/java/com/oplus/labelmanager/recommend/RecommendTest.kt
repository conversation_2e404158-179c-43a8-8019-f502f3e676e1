/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.labelmanager.recommend.aiunit
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.labelmanager.recommend

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import com.oplus.labelmanager.recommend.aiunit.AiUnitUtils
import com.oplus.labelmanager.recommend.aiunit.Label
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class RecommendTest {

    companion object {
        private const val TOP_5 = 5
    }

    @MockK
    lateinit var context: Context

    lateinit var mRecommend: Recommend
    @MockK
    lateinit var mAiUnitUtils: AiUnitUtils

    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        context = mockk(relaxed = true) {
            every { contentResolver }.returns(mockk(relaxed = true))
            every { applicationContext }.returns(this)
        }
        mRecommend = spyk()
        MyApplication.init(context)
        mockkObject(FileLabelDBHelper)
        mockkObject(FileLabelMappingDBHelper)
        mockkStatic(StatisticsUtils::class)
        mockkObject(AiUnitUtils.Companion)
        every { AiUnitUtils.getInstance() } returns mAiUnitUtils
    }

    @After
    fun after() {
        unmockkObject(FileLabelDBHelper)
        unmockkObject(FileLabelMappingDBHelper)
        unmockkStatic(StatisticsUtils::class)
        unmockkObject(AiUnitUtils.Companion)
    }

    @Test
    fun `should return nothing when getTopRecommendLabels all is empty`() {
        val fileList = arrayListOf<BaseFileBean>()
        every { mAiUnitUtils.getAIUnitResult(fileList) } returns arrayListOf()
        every { FileLabelDBHelper.getFileLabelByNames(any()) } returns arrayListOf()
        every { FileLabelDBHelper.getTopUsedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelDBHelper.getTopViewedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelMappingDBHelper.getTopMappingFileCountLabelIds() } returns arrayListOf()
        every { FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5) } returns arrayListOf()
        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }
        assertEquals(0, mRecommend.getTopRecommendLabels(fileList).size)

    }

    @Test
    fun `should return nothing when getTopRecommendLabels top ai unit is empty if getFileLabelByNames is not null`() {
        justRun { StatisticsUtils.onCommon(any(), any()) }
        val fileList = arrayListOf<BaseFileBean>()
        every { mAiUnitUtils.getAIUnitResult(fileList) } returns arrayListOf()
        val labelInLocal = arrayListOf<FileLabelEntity>()
        for (index in 1..3) {
            val labelEntity = FileLabelEntity(index.toLong(), "name$index", 1, 1, 0L, 0L)
            labelInLocal.add(labelEntity)
        }
        every { FileLabelDBHelper.getFileLabelByNames(any()) } returns labelInLocal
        every { FileLabelDBHelper.getTopUsedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelDBHelper.getTopViewedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelMappingDBHelper.getTopMappingFileCountLabelIds() } returns arrayListOf()
        every { FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5) } returns arrayListOf()
        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }

        assertEquals(0, mRecommend.getTopRecommendLabels(fileList).size)
        verify(inverse = true) { StatisticsUtils.onCommon(MyApplication.sAppContext, StatisticsUtils.LABEL_AI_SHOW) }
    }

    @Test
    fun `should return 3 when getTopRecommendLabels top ai unit is empty if getTopUsedLabels is not null`() {
        val usedList = arrayListOf<FileLabelEntity>()
        val resultEntity = FileLabelEntity(1, "name$", 1, 1, 0L, 0L)
        for (index in 1..3) {
            val labelEntity = FileLabelEntity(index.toLong(), "name$index", 1, 1, 0L, 0L)
            usedList.add(labelEntity)
        }
        val fileList = arrayListOf<BaseFileBean>()
        every { mAiUnitUtils.getAIUnitResult(fileList) } returns arrayListOf()
        every { FileLabelDBHelper.getFileLabelByNames(any()) } returns arrayListOf()
        every { FileLabelDBHelper.getTopUsedLabels(TOP_5) } returns usedList
        every { FileLabelDBHelper.getTopViewedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelMappingDBHelper.getTopMappingFileCountLabelIds() } returns arrayListOf()
        every { FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelDBHelper.getFileLabelById(any()) } returns resultEntity

        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }
        assertEquals(3, mRecommend.getTopRecommendLabels(fileList).size)
    }

    @Test
    fun `should return 1 when getTopRecommendLabels top ai unit is empty if getTopMappingFileCountLabelIds is not null`() {
        justRun { StatisticsUtils.onCommon(any(), any()) }
        val resultEntity = FileLabelEntity(1, "name$", 1, 1, 0L, 0L)
        val countLabelIds = arrayListOf<Long>()
        for (index in 1..3) {
            countLabelIds.add(index.toLong())
        }
        val fileList = arrayListOf<BaseFileBean>()
        every { mAiUnitUtils.getAIUnitResult(fileList) } returns arrayListOf()
        every { FileLabelDBHelper.getFileLabelByNames(any()) } returns arrayListOf()
        every { FileLabelDBHelper.getTopUsedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelDBHelper.getTopViewedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelMappingDBHelper.getTopMappingFileCountLabelIds() } returns countLabelIds
        every { FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelDBHelper.getFileLabelById(any()) } returns resultEntity

        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }
        assertEquals(1, mRecommend.getTopRecommendLabels(fileList).size)
    }

    @Test
    fun `should return 3 when getTopRecommendLabels top ai unit is empty if getTopMappingFileCountLabelIds is not null`() {
        justRun { StatisticsUtils.onCommon(any(), any()) }
        val resultEntity1 = FileLabelEntity(1, "name1", 1, 1, 0L, 0L)
        val resultEntity2 = FileLabelEntity(1, "name2", 1, 1, 0L, 0L)
        val resultEntity3 = FileLabelEntity(1, "name3", 1, 1, 0L, 0L)
        val countLabelIds = arrayListOf<Long>()
        for (index in 1..3) {
            countLabelIds.add(index.toLong())
        }
        val fileList = arrayListOf<BaseFileBean>()
        every { mAiUnitUtils.getAIUnitResult(fileList) } returns arrayListOf()
        every { FileLabelDBHelper.getFileLabelByNames(any()) } returns arrayListOf()
        every { FileLabelDBHelper.getTopUsedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelDBHelper.getTopViewedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelMappingDBHelper.getTopMappingFileCountLabelIds() } returns countLabelIds
        every { FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelDBHelper.getFileLabelById(any()) } returnsMany arrayListOf(resultEntity1, resultEntity2, resultEntity3)

        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }
        assertEquals(3, mRecommend.getTopRecommendLabels(fileList).size)
    }

    @Test
    fun `should return 5 when getTopRecommendLabels top ai unit is empty if getTopUsedLabels is not null`() {
        justRun { StatisticsUtils.onCommon(any(), any()) }
        val resultEntity = FileLabelEntity(1, "name$", 1, 1, 0L, 0L)
        val topViewLabels = arrayListOf<FileLabelEntity>()
        for (index in 1..3) {
            val labelEntity = FileLabelEntity(index.toLong(), "name$index", 1, 1, 0L, 0L)
            topViewLabels.add(labelEntity)
        }
        val topRecentPinnedLabels = arrayListOf<FileLabelEntity>()
        for (index in 4..7) {
            val labelEntity = FileLabelEntity(index.toLong(), "name$index", 1, 1, 0L, 0L)
            topRecentPinnedLabels.add(labelEntity)
        }
        val fileList = arrayListOf<BaseFileBean>()
        every { mAiUnitUtils.getAIUnitResult(fileList) } returns arrayListOf()
        every { FileLabelDBHelper.getFileLabelByNames(any()) } returns arrayListOf()
        every { FileLabelDBHelper.getTopUsedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelMappingDBHelper.getTopMappingFileCountLabelIds() } returns arrayListOf()
        every { FileLabelDBHelper.getTopViewedLabels(TOP_5) } returns topViewLabels
        every { FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5) } returns topRecentPinnedLabels
        every { FileLabelDBHelper.getFileLabelById(any()) } returns resultEntity

        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }
        assertEquals(3, mRecommend.getTopRecommendLabels(fileList).size)
    }

    @Test
    fun `should return 0 when getTopRecommendLabels if getFileLabelByNames size is 1`() {
        justRun { StatisticsUtils.onCommon(any(), any()) }
        val fileList = arrayListOf<BaseFileBean>()
        every { mAiUnitUtils.getAIUnitResult(fileList) } returns arrayListOf()
        val resultEntity = FileLabelEntity(1, "name$", 1, 1, 0L, 0L)
        val labelsInLocal = arrayListOf<FileLabelEntity>()
        for (index in 1..3) {
            val labelEntity = FileLabelEntity(index.toLong(), "name$index", 1, 1, 0L, 0L)
            labelsInLocal.add(labelEntity)
        }
        every { FileLabelDBHelper.getFileLabelByNames(any()) } returns labelsInLocal
        every { FileLabelDBHelper.getTopUsedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelMappingDBHelper.getTopMappingFileCountLabelIds() } returns arrayListOf()
        every { FileLabelDBHelper.getTopViewedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelDBHelper.getFileLabelById(any()) } returns resultEntity

        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }
        assertEquals(0, mRecommend.getTopRecommendLabels(fileList).size)
    }

    @Test
    fun `should return 0 when getTopRecommendLabels if getTopUsedLabels is null`() {
        justRun { StatisticsUtils.onCommon(any(), any()) }
        val fileList = arrayListOf<BaseFileBean>()
        every { mAiUnitUtils.getAIUnitResult(fileList) } returns arrayListOf()
        val resultEntity = FileLabelEntity(1, "name$", 1, 1, 0L, 0L)
        every { FileLabelDBHelper.getFileLabelByNames(any()) } returns arrayListOf()
        every { FileLabelDBHelper.getTopUsedLabels(TOP_5) } returns null
        every { FileLabelMappingDBHelper.getTopMappingFileCountLabelIds() } returns arrayListOf()
        every { FileLabelDBHelper.getTopViewedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelDBHelper.getFileLabelById(any()) } returns resultEntity

        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }
        assertEquals(0, mRecommend.getTopRecommendLabels(fileList).size)
    }

    @Test
    fun `should return 3 when getTopRecommendLabels if getTopMappingFileCountLabelIds size more than 5`() {
        val fileList = arrayListOf<BaseFileBean>()
        every { mAiUnitUtils.getAIUnitResult(fileList) } returns arrayListOf()
        val resultEntity = FileLabelEntity(1, "name$", 1, 1, 0L, 0L)
        val topViewLabels = arrayListOf<FileLabelEntity>()
        for (index in 1..8) {
            val labelEntity = FileLabelEntity(index.toLong(), "name$index", 1, 1, 0L, 0L)
            topViewLabels.add(labelEntity)
        }
        val topRecentPinnedLabels = arrayListOf<FileLabelEntity>()
        for (index in 4..7) {
            val labelEntity = FileLabelEntity(index.toLong(), "name$index", 1, 1, 0L, 0L)
            topRecentPinnedLabels.add(labelEntity)
        }
        val countLabelIds = arrayListOf<Long>()
        for (index in 1..6) {
            countLabelIds.add(index.toLong())
        }
        every { FileLabelDBHelper.getFileLabelByNames(any()) } returns arrayListOf()
        every { FileLabelDBHelper.getTopUsedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelMappingDBHelper.getTopMappingFileCountLabelIds() } returns countLabelIds
        every { FileLabelDBHelper.getTopViewedLabels(TOP_5) } returns topViewLabels
        every { FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5) } returns topRecentPinnedLabels
        every { FileLabelDBHelper.getFileLabelById(any()) } returns resultEntity

        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }
        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }
        assertEquals(3, mRecommend.getTopRecommendLabels(fileList).size)
    }

    @Test
    fun `should return 3 when getTopRecommendLabels if getFileLabelById return null`() {
        val fileList = arrayListOf<BaseFileBean>()
        every { mAiUnitUtils.getAIUnitResult(fileList) } returns arrayListOf()
        val topViewLabels = arrayListOf<FileLabelEntity>()
        for (index in 1..8) {
            val labelEntity = FileLabelEntity(index.toLong(), "name$index", 1, 1, 0L, 0L)
            topViewLabels.add(labelEntity)
        }
        val topRecentPinnedLabels = arrayListOf<FileLabelEntity>()
        for (index in 4..7) {
            val labelEntity = FileLabelEntity(index.toLong(), "name$index", 1, 1, 0L, 0L)
            topRecentPinnedLabels.add(labelEntity)
        }
        val countLabelIds = arrayListOf<Long>()
        for (index in 1..6) {
            countLabelIds.add(index.toLong())
        }
        every { FileLabelDBHelper.getFileLabelByNames(any()) } returns arrayListOf()
        every { FileLabelDBHelper.getTopUsedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelMappingDBHelper.getTopMappingFileCountLabelIds() } returns countLabelIds
        every { FileLabelDBHelper.getTopViewedLabels(TOP_5) } returns topViewLabels
        every { FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5) } returns topRecentPinnedLabels
        every { FileLabelDBHelper.getFileLabelById(any()) } returns null

        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }
        assertEquals(3, mRecommend.getTopRecommendLabels(fileList).size)
    }

    @Test
    fun `should return 3 when getTopRecommendLabels if getAIUnitResult return list`() {
        justRun { StatisticsUtils.onCommon(any(), any()) }
        val aiRecommendLabels = arrayListOf<Label>()
        for (index in 1..8) {
            val aiLabel = Label("aiLabel$index", (0.25 * index).toFloat())
            aiRecommendLabels.add(aiLabel)
        }
        val fileList = arrayListOf<BaseFileBean>()
        every { mAiUnitUtils.getAIUnitResult(fileList) } returns aiRecommendLabels
        val topViewLabels = arrayListOf<FileLabelEntity>()
        for (index in 1..8) {
            val labelEntity = FileLabelEntity(index.toLong(), "name$index", 1, 1, 0L, 0L)
            topViewLabels.add(labelEntity)
        }
        val topRecentPinnedLabels = arrayListOf<FileLabelEntity>()
        for (index in 4..7) {
            val labelEntity = FileLabelEntity(index.toLong(), "name$index", 1, 1, 0L, 0L)
            topRecentPinnedLabels.add(labelEntity)
        }
        val countLabelIds = arrayListOf<Long>()
        for (index in 1..6) {
            countLabelIds.add(index.toLong())
        }
        every { FileLabelDBHelper.getFileLabelByNames(any()) } returns arrayListOf()
        every { FileLabelDBHelper.getTopUsedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelMappingDBHelper.getTopMappingFileCountLabelIds() } returns countLabelIds
        every { FileLabelDBHelper.getTopViewedLabels(TOP_5) } returns topViewLabels
        every { FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5) } returns topRecentPinnedLabels
        every { FileLabelDBHelper.getFileLabelById(any()) } returns null

        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }
        assertEquals(3, mRecommend.getTopRecommendLabels(fileList).size)
        verify { StatisticsUtils.onCommon(MyApplication.sAppContext, StatisticsUtils.LABEL_AI_SHOW) }
    }

    @Test
    fun `should return false when checkAiLabelClick when click nothing`() {
        assertFalse(mRecommend.checkAiLabelClick(arrayListOf()))
    }

    @Test
    fun `should return true when checkAiLabelClick`() {
        justRun { StatisticsUtils.onCommon(any(), any()) }
        val aiRecommendLabels = arrayListOf<Label>()
        for (index in 1..8) {
            val aiLabel = Label("aiLabel$index", (0.25 * index).toFloat())
            aiRecommendLabels.add(aiLabel)
        }
        val fileList = arrayListOf<BaseFileBean>()
        every { mAiUnitUtils.getAIUnitResult(fileList) } returns aiRecommendLabels
        val topViewLabels = arrayListOf<FileLabelEntity>()
        val topRecentPinnedLabels = arrayListOf<FileLabelEntity>()
        val countLabelIds = arrayListOf<Long>()
        every { FileLabelDBHelper.getFileLabelByNames(any()) } returns arrayListOf()
        every { FileLabelDBHelper.getTopUsedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelMappingDBHelper.getTopMappingFileCountLabelIds() } returns countLabelIds
        every { FileLabelDBHelper.getTopViewedLabels(TOP_5) } returns topViewLabels
        every { FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5) } returns topRecentPinnedLabels
        every { FileLabelDBHelper.getFileLabelById(any()) } returns null
        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }
        mRecommend.getTopRecommendLabels(fileList)
        val clickLabels = arrayListOf<String>()
        clickLabels.add("aiLabel1")
        assertTrue(mRecommend.checkAiLabelClick(clickLabels))
    }

    @Test
    fun `should return false when checkAiLabelClick when ai label not click`() {
        justRun { StatisticsUtils.onCommon(any(), any()) }
        val aiRecommendLabels = arrayListOf<Label>()
        for (index in 1..8) {
            val aiLabel = Label("aiLabel$index", (0.25 * index).toFloat())
            aiRecommendLabels.add(aiLabel)
        }
        val fileList = arrayListOf<BaseFileBean>()
        every { mAiUnitUtils.getAIUnitResult(fileList) } returns aiRecommendLabels
        val topViewLabels = arrayListOf<FileLabelEntity>()
        val topRecentPinnedLabels = arrayListOf<FileLabelEntity>()
        val countLabelIds = arrayListOf<Long>()
        every { FileLabelDBHelper.getFileLabelByNames(any()) } returns arrayListOf()
        every { FileLabelDBHelper.getTopUsedLabels(TOP_5) } returns arrayListOf()
        every { FileLabelMappingDBHelper.getTopMappingFileCountLabelIds() } returns countLabelIds
        every { FileLabelDBHelper.getTopViewedLabels(TOP_5) } returns topViewLabels
        every { FileLabelDBHelper.getTopRecentPinnedLabels(TOP_5) } returns topRecentPinnedLabels
        every { FileLabelDBHelper.getFileLabelById(any()) } returns null
        every { mRecommend.getTopRecommendLabels(fileList) } answers { callOriginal() }
        mRecommend.getTopRecommendLabels(fileList)
        val clickLabels = arrayListOf<String>()
        clickLabels.add("aaa")
        assertFalse(mRecommend.checkAiLabelClick(clickLabels))
    }
}