/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : CompressParentFragment
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/11/4
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2022/11/4       1      create
 ***********************************************************************/
package com.filemanager.categorycompress.ui

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.tablayout.COUITab
import com.coui.appcompat.tablayout.COUITabLayout
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.categorycompress.R
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseFragmentAdapter
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewOperate
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.helper.uiconfig.type.ScreenFoldConfig
import com.filemanager.common.helper.uiconfig.type.ScreenOrientationConfig
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.OnGetUIInfoListener
import com.filemanager.common.interfaces.TabActivityListener
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.view.ViewPagerWrapperForPC
import com.filemanager.common.view.viewpager.RTLViewPager
import com.filemanager.common.wrapper.MediaFileWrapper
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.filemanager.interfaze.main.IMain
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Locale

class CompressParentFragment : BaseVMFragment<CompressParentViewModel>(), OnBackPressed, NavigationBarView.OnItemSelectedListener,
    COUITabLayout.OnTabSelectedListener, TabActivityListener<MediaFileWrapper>,
    IPreviewListFragment, OnGetUIInfoListener {

    companion object {
        private const val TAG = "CompressParentFragment"
        private const val ALL_POSITION = 0
        private const val ZIP_POSITION = 1
        private const val RAR_POSITION = 2
        private const val Z7_POSITION = 3
        private const val JAR_POSITION = 4
    }

    private var mToolbar: COUIToolbar? = null
    private var mRootLayout: ViewGroup? = null
    var mAppBarLayout: COUIDividerAppBarLayout? = null
    private var mTabView: COUITabLayout? = null
    private var mViewPager: RTLViewPager? = null
    private var mViewPagerWrapper: ViewPagerWrapperForPC? = null
    var sortEntryView: SortEntryView? = null

    private var mPosition: Int = 0
    private val mTabTitles = arrayOf(
        CategoryCompressViewModel.TYPE_ALL,
        CategoryCompressViewModel.TYPE_ZIP,
        CategoryCompressViewModel.TYPE_RAR,
        CategoryCompressViewModel.TYPE_7ZIP,
        CategoryCompressViewModel.TYPE_JAR
    )
    private var mPages: ArrayList<CategoryCompressFragment> = ArrayList()

    private var isChildDisplay = false
    private var mNeedLoadData = false
    private var mFileCount = 0L

    var mViewModel: CompressParentViewModel? = null
    private var tempSortType = -1
    private var tempSortDesc = -1

    private var previewOperate: IPreviewOperate? = null
    private var isEmpty: Boolean = false
    private var scrollHelper: DragScrollHelper? = null

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = false
        get() {
            return field.also {
                field = false
            }
        }

    override fun getLayoutResId(): Int {
        return R.layout.compress_parent_fragment
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        initArguments()
    }

    private fun initArguments() {
        val bundle = arguments ?: return
        isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
        mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
        mFileCount = bundle.getLong(KtConstants.P_CATEGORY_COUNT, 0L)
        tempSortType = bundle.getInt(Constants.TEMP_SORT_TYPE, -1)
        tempSortDesc = bundle.getInt(Constants.TEMP_SORT_DESC, -1)
    }

    @SuppressLint("RestrictedApi")
    override fun initView(view: View) {
        if (previewOperate?.isSupportPreview() != true) {
            mToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        }
        toolbar = mToolbar
        mAppBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        mTabView = view.findViewById(com.filemanager.common.R.id.tab_layout)
        mViewPager = view.findViewById(R.id.viewPager)
        mViewPagerWrapper = view.findViewById(R.id.view_pager_wrapper)
        mRootLayout = view.findViewById(R.id.coordinator_layout)
        sortEntryView = view.findViewById(com.filemanager.common.R.id.sort_entry_view)
        sortEntryView?.setClickSortListener {
            val menu = ActionMenuItem(view.context, 0, R.id.navigation_sort, 0, 0, "")
            onOptionsItemSelected(menu)
        }
        mViewPagerWrapper?.notifyMainViewPager = object : ((Boolean) -> Unit) {
            override fun invoke(enable: Boolean) {
                baseVMActivity?.let {
                    val mainAction = Injector.injectFactory<IMain>()
                    mainAction?.setViewPagerScrollEnabled(it, enable)
                }
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        val fragments = childFragmentManager.fragments
        for (i in mTabTitles.indices) {
            initFragment(i, fragments)
        }
        initViewPager()
        initToolbar()
        mViewModel = ViewModelProvider(this)[CompressParentViewModel::class.java]
        startScanModeObserver()
        startSideNavigationStatusObserver()
    }

    private fun startScanModeObserver() {
        mViewModel?.mBrowseModeState?.observe(this) {
            delay { refreshScanModeItemIcon(mNeedSkipAnimation.not()) }
        }
    }

    private fun startSideNavigationStatusObserver() {
        baseVMActivity?.sideNavigationStatus?.observe(this) { status ->
            Log.d(TAG, "sideNavigationStatus observe: $status")
            toolbar?.let {
                refreshScanModeItemIcon()
                setToolbarEditIcon(it, isChildDisplay)
            }
        }
    }

    private fun initToolbar() {
        mToolbar?.apply {
            menu.clear()
            isTitleCenterStyle = false
            title = getString(com.filemanager.common.R.string.string_compress)
            inflateMenu(R.menu.category_compress_menu)
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this, isChildDisplay)
            val edit = mToolbar?.menu?.findItem(R.id.actionbar_edit)
            edit?.isVisible = mFileCount > 0
        }

        baseVMActivity?.apply {
            setSupportActionBar(mToolbar)
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(!isChildDisplay)
                setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }

        if (previewOperate?.isSupportPreview() != true) {
            mRootLayout?.apply {
                setPadding(
                    paddingLeft,
                    COUIPanelMultiWindowUtils.getStatusBarHeight(appContext), paddingRight, paddingBottom
                )
            }
        }
        if ((mTabView != null) && (mViewPager != null)) {
            mTabView?.let {
                it.setupWithViewPager(mViewPager, false)
                it.addOnTabSelectedListener(this)
                it.isUpdateindicatorposition = true
            }
        }
    }

    private fun initFragment(position: Int, fragments: List<Fragment>) {
        var fragment = if (fragments.isEmpty()) null else fragments[position]
        if (fragment == null) {
            fragment = CategoryCompressFragment()
            val bundle = Bundle()
            bundle.putString(KtConstants.P_TAB_POSITION, mTabTitles[position])
            if (position == 0) {
                bundle.putBoolean(KtConstants.P_NEED_LOAD_DATA, mNeedLoadData)
            }
            bundle.putInt(Constants.TEMP_SORT_TYPE, tempSortType)
            bundle.putInt(Constants.TEMP_SORT_DESC, tempSortDesc)
            fragment.arguments = bundle
        }
        if (fragment is CategoryCompressFragment) {
            fragment.setToolbarNew(mToolbar)
            fragment.setTabActivityListener(this@CompressParentFragment)
            mPages.add(fragment)
        }
    }

    private fun initViewPager() {
        mViewPager?.let {
            it.offscreenPageLimit = mTabTitles.size
            it.adapter = ViewPagerFragmentStateAdapter()
            it.currentItem = 0
            it.overScrollMode = View.OVER_SCROLL_NEVER
        }
    }

    override fun startObserve() {
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        if (getCurrentFragment()?.isInSelectMode() == true) {
            inflater.inflate(com.filemanager.common.R.menu.menu_edit_mode, menu)
        } else {
            inflater.inflate(R.menu.category_compress_menu, menu)
        }
        mToolbar?.apply {
            refreshScanModeItemIcon()
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this, isChildDisplay)
            val edit = menu.findItem(R.id.actionbar_edit)
            edit?.isVisible = mFileCount > 0
        }
    }

    override fun onMenuItemSelected(item: MenuItem): Boolean {
        return onOptionsItemSelected(item)
    }

    private fun setToolbarMenuVisible(toolbar: COUIToolbar, visible: Boolean) {
        toolbar.menu.findItem(R.id.action_setting)?.isVisible = visible
    }

    private fun setToolbarEditIcon(toolbar: COUIToolbar, isChildDisplay: Boolean) {
        toolbar.menu.findItem(R.id.actionbar_edit)?.let {
            if (baseVMActivity?.sideNavigationStatus?.value == KtConstants.LIST_NORMAL_MODE
                && !isEmpty && isChildDisplay
            ) {
                it.setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_edit)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            } else {
                it.setIcon(null)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return getCurrentFragment()?.onMenuItemSelected(item) ?: super.onOptionsItemSelected(item)
    }

    private fun getPositionFragment(position: Int): CategoryCompressFragment? {
        return if (position < mPages.size) {
            mPages[mPosition]
        } else {
            null
        }
    }

    override fun getFragmentInstance(): Fragment {
        return this
    }

    override fun setFragmentArguments(arguments: Bundle?) {
        this.arguments = arguments
    }

    override fun setPreviewToolbar(toolbar: COUIToolbar?) {
        mToolbar = toolbar
    }

    override fun onResumeLoadData() {
        getCurrentFragment()?.onResumeLoadData()
        val bundle = arguments ?: return
        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
            baseVMActivity?.apply {
                setSupportActionBar(mToolbar)
                baseVMActivity?.supportActionBar?.apply {
                    setDisplayHomeAsUpEnabled(!isChildDisplay)
                    setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
                }
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
    }

    override fun pressBack(): Boolean {
        return (getCurrentFragment() as? OnBackPressed)?.pressBack() == true
    }

    override fun onDestroy() {
        super.onDestroy()
        mPages.clear()
        mTabView?.setupWithViewPager(null, false)
        mTabView = null
        mViewPager = null
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        return getCurrentFragment()?.onNavigationItemSelected(item) ?: false
    }

    override fun onTabSelected(tab: COUITab?) {
        tab?.let {
            mPosition = it.position
            refreshCurrentFragment()
            val opTab = when (mPosition) {
                ALL_POSITION -> "all"
                ZIP_POSITION -> "zip"
                RAR_POSITION -> "rar"
                Z7_POSITION -> "7z"
                JAR_POSITION -> "jar"
                else -> ""
            }
            OptimizeStatisticsUtil.pageTab(OptimizeStatisticsUtil.COMPRESS, opTab)
        }
    }

    fun refreshCurrentFragment() {
        mViewModel?.let {
            getCurrentFragment()?.onResumeLoadData()
        }
    }

    override fun onTabUnselected(tab: COUITab?) {
        Log.i(TAG, "onTabUnselected")
    }

    override fun onTabReselected(tab: COUITab?) {
        Log.i(TAG, "onTabReselected")
    }

    override fun initToolbarSelectedMode(
        needInit: Boolean,
        realFileSize: Int,
        selectedFileSize: Int,
        selectItems: ArrayList<MediaFileWrapper>
    ) {
        mToolbar?.let {
            if (needInit && (mTabView?.isInEditMode != true)) {
                it.menu.clear()
                it.isTitleCenterStyle = true
                it.inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
            }
            showEditMode()
            val isSelectAll = (realFileSize == selectedFileSize)
            ToolbarUtil.updateToolbarTitle(it, selectedFileSize, isSelectAll)
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble((selectedFileSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems))
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
        baseVMActivity?.supportActionBar?.setDisplayHomeAsUpEnabled(false)
    }

    private fun showEditMode() {
        mViewPager?.isUserInputEnabled = false
        mTabView?.isEnabled = false
        mViewPagerWrapper?.setEditMode(true)
    }

    override fun initToolbarNormalMode(needInit: Boolean, empty: Boolean) {
        isEmpty = empty
        if (!isAdded) {
            return
        }
        exitEditMode()
        mToolbar?.apply {
            if (needInit) {
                menu.clear()
                isTitleCenterStyle = false
                title = getString(com.filemanager.common.R.string.string_compress)
                inflateMenu(R.menu.category_compress_menu)
            }

            menu.findItem(R.id.actionbar_edit)?.isVisible = empty.not()
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this, isChildDisplay)
            previewOperate?.onToolbarMenuUpdated(menu)
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
        baseVMActivity?.apply {
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(!isChildDisplay)
                setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
            }
        }
    }

    private fun exitEditMode() {
        mViewPager?.isUserInputEnabled = true
        mTabView?.isEnabled = true
        mViewPagerWrapper?.setEditMode(false)
    }

    override fun refreshScanModeItemIcon(withAnimation: Boolean) {
        Log.d(TAG, "refreshScanModeItemIcon withAnimation=$withAnimation")
        mToolbar?.menu?.findItem(R.id.actionbar_scan_mode)?.let {
            val resources = appContext.resources
            val desc: String
            val resId: Int = if (mViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                desc = resources.getString(com.filemanager.common.R.string.palace_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
            } else {
                desc = resources.getString(com.filemanager.common.R.string.list_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
            }
            it.contentDescription = desc
            setScanModeStatue(it, desc, withAnimation, resId)
        }
    }

    override fun updateNeedSkipAnimation(withAnimation: Boolean) {
        mNeedSkipAnimation = withAnimation
    }

    private fun setScanModeStatue(
        toolbar: MenuItem,
        desc: String,
        needSkipAnimation: Boolean,
        resId: Int
    ) {
        if (baseVMActivity?.sideNavigationStatus?.value == KtConstants.LIST_SELECTED_MODE
            && !isEmpty && isChildDisplay
        ) {
            toolbar.setIcon(null)
            toolbar.setTitle(desc)
            toolbar.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
        } else {
            toolbar.setTitle(null)
            if (needSkipAnimation) {
                toolbar.setIcon(resId)
            } else {
                KtAnimationUtil.updateMenuItemWithFadeAnimate(toolbar, resId, baseVMActivity)
            }
            toolbar.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        mPages.forEach {
            it.onUIConfigChanged(configList)
        }
        configList.forEach {
            if ((it is ScreenFoldConfig) || (it is ScreenOrientationConfig)) {
                mViewPager?.setCurrentItem(mPosition, false)
                return
            }
        }
    }

    fun getCurrentFragment(): CategoryCompressFragment? {
        return getPositionFragment(mPosition)
    }

    fun getCurrentTabCompressType(): String {
        return if (mPosition < mTabTitles.size) {
            mTabTitles[mPosition]
        } else {
            CategoryCompressViewModel.TYPE_ALL
        }
    }

    fun disableViewPager() {
        mViewPager?.isUserInputEnabled = false
    }

    override fun fromSelectPathResult(requestCode: Int, paths: List<String>?) {
        getCurrentFragment()?.fromSelectPathResult(requestCode, paths)
    }

    override fun updatedLabel() {
        mViewModel?.let { getCurrentFragment()?.onResumeLoadData() }
    }

    override fun permissionSuccess() {}

    override fun setCurrentFromOtherSide(currentPath: String) {}

    override fun getCurrentPath(): String {
        return ""
    }

    override fun backToTop() {
        getCurrentFragment()?.let {
            it.getRecyclerView()?.fastSmoothScrollToTop()
        }
    }

    override fun getScanMode(): Int {
        return mViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
    }

    override fun setScanMode(mode: Int) {
        mViewModel?.mBrowseModeState?.value = mode
    }

    override fun isSelectionMode(): Boolean {
        return getCurrentFragment()?.isInSelectMode() ?: false
    }

    override fun setPreviewOperate(operate: IPreviewOperate) {
        previewOperate = operate
    }

    override fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.P_CHILD_DISPLAY, isChildDisplay)
        mToolbar?.let {
            setToolbarMenuVisible(it, !isChildDisplay)
            setToolbarEditIcon(it, isChildDisplay)
            refreshScanModeItemIcon()
        }
        baseVMActivity?.supportActionBar?.apply {
            if (getCurrentFragment()?.isInSelectMode() == true) {
                setDisplayHomeAsUpEnabled(true)
                setHomeAsUpIndicator(com.support.snackbar.R.drawable.coui_menu_ic_cancel)
            } else {
                setDisplayHomeAsUpEnabled(!isChildDisplay)
            }
        }
    }

    inner class ViewPagerFragmentStateAdapter : BaseFragmentAdapter(this) {
        override fun createFragment(position: Int): Fragment {
            return mPages[position]
        }

        override fun getItemCount(): Int {
            return mTabTitles.size
        }

        override fun getPageTitle(position: Int): CharSequence {
            val resources = appContext.resources
            return when (position) {
                0 -> resources.getString(com.filemanager.common.R.string.total)
                else -> resources.getStringArray(com.filemanager.common.R.array.category_compress_type)[position - 1].uppercase(Locale.ROOT)
            }
        }

        override fun getItemPosition(`object`: Any): Int {
            if (`object` is CategoryCompressFragment) {
                return mPages.indexOf(`object`)
            }
            return super.getItemPosition(`object`)
        }
    }

    override fun checkPermission() {
        baseVMActivity?.let {
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.checkPermission(it)
        }
    }

    override fun setPermissionEmptyVisible(visible: Int) {
        mPages.forEach {
            it.setPermissionEmptyVisible(visible)
        }
    }

    fun previewClickedFile(file: BaseFileBean?, clickPreviewFileLiveData: MutableLiveData<BaseFileBean?>?): Boolean {
        return previewOperate?.previewClickedFile(file, clickPreviewFileLiveData) ?: false
    }

    fun previewEditedFiles(files: List<BaseFileBean>?): Boolean {
        getCurrentFragment()?.mFileOperateController?.setPreviewOpen(isPreviewOpen())
        return previewOperate?.previewEditedFiles(files) ?: false
    }

    fun listEmptyFile() {
        previewOperate?.listEmptyFile()
    }

    fun isPreviewOpen(): Boolean {
        return previewOperate?.isPreviewOpen() ?: false
    }

    override fun getOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.OperateResultListener? =
        getCurrentFragment()?.mFileOperateController?.pickResultListener()

    override fun exitSelectionMode() {
        getCurrentFragment()?.exitSelectionMode()
    }

    override fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        return getCurrentFragment()?.onSideNavigationClicked(isOpen) ?: false
    }

    override fun updateLeftRightMargin() {
        getCurrentFragment()?.updateLeftRightMargin()
    }

    override fun isEmptyList(): Boolean {
        return getCurrentFragment()?.isEmptyList() ?: false
    }

    override fun handleDragScroll(event: DragEvent): Boolean {
        if (scrollHelper == null) {
            scrollHelper = DragScrollHelper(getRecyclerView())
        }
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    override fun resetScrollStatus() {
        scrollHelper?.resetDragStatus()
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        val selectedFiles = DragUtils.getSelectedFiles()
        val itemViewList = ArrayList<View>()
        val fileList = getCurrentFragment()?.getFileList()
        val size = fileList?.size ?: return null
        selectedFiles?.forEach { fileBean ->
            val indexOf = fileList.indexOf(fileBean) ?: return null
            if (indexOf >= 0 && indexOf < size) {
                val itemView =
                    getRecyclerView()?.findViewHolderForAdapterPosition(indexOf)?.itemView
                itemView?.let { itemViewList.add(it) }
            }
        }
        return itemViewList
    }

    override fun setNavigateItemAble() {
        activity?.lifecycle?.coroutineScope?.launch(Dispatchers.Main) {
            val selectItems = getCurrentFragment()?.getSelectedFileList()
            val selectItemSize = selectItems?.size ?: 0
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble((selectItemSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems))
        }
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_COMPRESS
    }

    override fun getViewModel(): ViewModel? {
        return null
    }

    override fun getRecyclerView(): RecyclerView? {
        return getCurrentFragment()?.getRecyclerView()
    }
}