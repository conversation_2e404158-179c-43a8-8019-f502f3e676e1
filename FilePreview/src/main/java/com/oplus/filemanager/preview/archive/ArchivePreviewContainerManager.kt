/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ArchivePreviewContainerManager.kt
 * Description:
 *     Manage container ui and loading ui for previewing archive file
 *
 * Version: 1.0
 * Date: 2024-10-25
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-10-25   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.archive

import android.view.View
import androidx.appcompat.widget.AppCompatTextView
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.Log
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewContainerManager
import com.oplus.filemanager.preview.utils.PreviewScrollAreaHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite

internal class ArchivePreviewContainerManager(
    fragment: ArchivePreviewFragment,
    archiveModel: ArchivePreviewViewModel,
    rootLayout: View,
) : AbsPreviewContainerManager(), IArchivePreviewContainerManager {

    private companion object {
        private const val TAG = "ArchivePreviewContainerManager"
    }

    override val defaultContainer: PreviewFileInfoSuite =
        rootLayout.findViewById(R.id.preview_archive_def_info)
    override val previewCardUi: IArchiveUiPreviewCard = ArchivePreviewCardUiImpl(
        fragment = fragment,
        archiveModel = archiveModel,
        previewCardLayout = rootLayout.findViewById(R.id.preview_archive_preview_layout)
    )
    override val defaultInfoUi: IArchiveUiDefaultInfo = ArchiveDefaultInfoUiImpl(defaultContainer)
    private val loadingLayout = rootLayout.findViewById<View>(R.id.loading_layout)
    override val mDateTv: AppCompatTextView = rootLayout.findViewById(R.id.preview_remote_time_info)
    override val mSizeTv: AppCompatTextView = rootLayout.findViewById(R.id.preview_remote_size_info)
    override val nameContainer: TextViewSnippet =
        rootLayout.findViewById(R.id.preview_remote_title)
    private val scrollAreaHelper = PreviewScrollAreaHelper(
        scrollView = rootLayout.findViewById(R.id.preview_audio_scroll_area),
        operationsBar = rootLayout.findViewById(R.id.preview_operations_bar)
    )

    override fun showAsLoading() {
        super.showAsLoading()
        Log.d(TAG, "showAsLoading")
        previewCardUi.setVisible(false)
        defaultInfoUi.setVisible(false)
        loadingLayout.visibility = View.VISIBLE
        scrollAreaHelper.hideDivider()
    }

    override fun showAsFileContainer(isDefault: Boolean) {
        Log.d(TAG, "showAsFileContainer: $isDefault")
        loadingLayout.visibility = View.GONE
        previewCardUi.setVisible(!isDefault)
        defaultInfoUi.setVisible(isDefault)
        if (isDefault) {
            scrollAreaHelper.disableScrollViewportMinHeight()
            scrollAreaHelper.hideDivider()
        } else {
            scrollAreaHelper.enableScrollViewportMinHeight(
                defaultContainer.resources
                    .getDimensionPixelOffset(R.dimen.preview_remote_scroll_layout_min_height)
            )
            scrollAreaHelper.checkShowDivider()
        }
        super.showAsFileContainer(isDefault)
    }

    override fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        scrollAreaHelper.checkShowDivider()
    }
}