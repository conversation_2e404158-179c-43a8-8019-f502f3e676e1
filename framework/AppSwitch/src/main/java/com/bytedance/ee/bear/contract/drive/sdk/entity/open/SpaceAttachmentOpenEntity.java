/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : SpaceAttachmentOpenEntity.java
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * *
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.bytedance.ee.bear.contract.drive.sdk.entity.open;

import android.os.Parcel;

public class SpaceAttachmentOpenEntity extends BaseSpaceOpenEntity {

    public static final Creator<SpaceAttachmentOpenEntity> CREATOR = new Creator<SpaceAttachmentOpenEntity>() {
        @Override
        public SpaceAttachmentOpenEntity createFromParcel(Parcel in) {
            return new SpaceAttachmentOpenEntity(in);
        }

        @Override
        public SpaceAttachmentOpenEntity[] newArray(int size) {
            return new SpaceAttachmentOpenEntity[size];
        }
    };

    protected SpaceAttachmentOpenEntity(Parcel parcel) {
        super(parcel);
    }
}
