/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : IntenProcessFactory.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.process

import com.filemanager.common.constants.ThirdAppConstants.DINGTALK_PACKAGE
import com.filemanager.common.constants.ThirdAppConstants.FEISHU_PACKAGE
import com.filemanager.common.constants.ThirdAppConstants.QQ_PACKAGE
import com.filemanager.common.constants.ThirdAppConstants.WECHAT_PACKAGE
import com.filemanager.common.constants.ThirdAppConstants.WEWORK_PACKAGE

class IntenProcessFactory private constructor() : IIntentProcessFactory {

    companion object {

        const val TAG = "IntenProcessFactory"



        private var instance: IntenProcessFactory? = null
            get() {
                if (field == null) {
                    field = IntenProcessFactory()
                }
                return field
            }

        @Synchronized
        fun get(): IntenProcessFactory {
            return instance!!
        }
    }


    override fun createIntentProcess(packageString: String): IntentProcess {
        return when (packageString) {
            FEISHU_PACKAGE -> FeishuIntentProcess()
            WECHAT_PACKAGE -> WechatIntentProcess()
            DINGTALK_PACKAGE -> DingTalkIntentProcess()
            WEWORK_PACKAGE -> WeworkIntentProcess()
            QQ_PACKAGE -> QQIntentProcess()
            else -> DefaultIntentProcess()
        }
    }
}