/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: ZipCompressHelper.kt
 ** Description: Compress file to zip
 ** Version: 1.0
 ** Date: 2020/3/10
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.compress

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.fileutils.getNewFileNameWhenFileExist
import com.filemanager.common.utils.Log
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException

class ZipCompressHelper : CompressHelper() {
    companion object {
        private const val TAG = "ZipCompressHelper"
    }

    override fun internalCompress(sourceFiles: List<out BaseFileBean>, destFile: BaseFileBean): Boolean {
        Log.d(TAG, "start zip compress: targetPath=${destFile.mData}")
        var fileOutputStream: FileOutputStream? = null
        var zos: ZipArchiveOutputStream? = null
        try {
            fileOutputStream = FileOutputStream(destFile.mData)
            zos = ZipArchiveOutputStream(fileOutputStream)
            val list = ArrayList<File>(sourceFiles.size)
            sourceFiles.forEach {
                list.add(File(it.mData))
            }
            val relativeNameList: MutableList<String> = mutableListOf()
            return addEntryToArchiveOutputStream(zos, list, relativeNameList, destFile)
        } catch (e: Exception) {
            Log.d(TAG, "internalDoCompress exception: ${e.message}")
        } finally {
            try {
                zos?.close()
            } catch (e1: java.lang.Exception) {
            }
            try {
                fileOutputStream?.close()
            } catch (e1: java.lang.Exception) {
            }
        }
        Log.d(TAG, "end zip compress")
        return false
    }

    private fun addEntryToArchiveOutputStream(
        zos: ZipArchiveOutputStream,
        files: List<File>,
        relativeNameList: MutableList<String>,
        destFile: BaseFileBean
    ): Boolean {
        var zipEntry: ZipArchiveEntry
        var processedRelativeName: String?
        try {
            files.forEach {
                if (isCancelled()) {
                    Log.d(TAG, "addEntryToArchiveOutputStream: Compress has been interrupted")
                    return false
                }
                // Bugfix: http://alm.adc.com/oppo/FileManager/_workitems/edit/385445/
                if (it.absolutePath == destFile.mData) {
                    Log.w(TAG, "addEntryToArchiveOutputStream: file is the compressing file[${it.name}]")
                    return@forEach
                }
                if (it.exists().not()) {
                    Log.d(TAG, "addEntryToArchiveOutputStream: file not exist[${it.absolutePath}]")
                    return@forEach
                }
                processedRelativeName = getProcessRelativeName(it, relativeNameList)
                if (processedRelativeName.isNullOrEmpty()) {
                    Log.d(TAG, "addEntryToArchiveOutputStream: path is empty[${it.absolutePath}]")
                    return@forEach
                }
                zipEntry = ZipArchiveEntry(processedRelativeName)
                if (zipEntry.isDirectory.not()) {
                    zipEntry.size = it.length()
                }
                zos.putArchiveEntry(zipEntry)
                processedRelativeName?.let {
                    relativeNameList.add(it)
                }
                // folder recursion to add entry to ArchiveOutputStream
                val zipResult = zipDirectoryOrFile(zipEntry, it, zos, relativeNameList, destFile)
                if (!zipResult) {
                    return false
                }
            }
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Filed to compress: ${e.message}")
        }
        return false
    }

    private fun zipDirectoryOrFile(
        zipEntry: ZipArchiveEntry,
        file: File,
        zos: ZipArchiveOutputStream,
        relativeNameList: MutableList<String>,
        destFile: BaseFileBean
    ): Boolean {
        if (zipEntry.isDirectory || file.isDirectory) {
            zos.closeArchiveEntry()
            JavaFileHelper.listFiles(file)?.let { list ->
                val arrayList: List<File> = list.toList()
                if (addEntryToArchiveOutputStream(zos, arrayList, relativeNameList, destFile).not()) {
                    return false
                }
            }
        } else {
            compressCopy(file, zos)
        }
        return true
    }

    private fun getProcessRelativeName(
        file: File,
        compressedRelativeNameList: MutableList<String>
    ): String? {
        var result: String? = null
        val relativeName = getEntryRelativeName(file)
        relativeName?.let {
            val extWithDot = if (it.contains(".")) {
                it.substringAfterLast(".").let { ext ->
                    Log.d(TAG, "getProcessRelativeName ext $ext")
                    if (ext.isNotEmpty()) {
                        ".".plus(ext)
                    } else {
                        ext
                    }
                }
            } else {
                ""
            }
            val nameWithoutExt = it.substringBeforeLast(".")
            result = if (file.isDirectory) {
                it
            } else {
                getNewFileNameWhenFileExist(
                    compressedRelativeNameList,
                    nameWithoutExt,
                    extWithDot
                )
            }
            Log.d(TAG, "getProcessRelativeName file $file, extWithDot $extWithDot, nameWithoutExt $nameWithoutExt," +
                    "relativeName $relativeName, lis $compressedRelativeNameList, result $result")
        }
        return result
    }

    @Throws(IOException::class)
    private fun compressCopy(fileBean: File, output: ZipArchiveOutputStream) {
        val buffer = ByteArray(COPY_BUF_SIZE)
        var tempSize = 0
        val fis = FileInputStream(fileBean)
        while (-1 != fis.read(buffer).also { tempSize = it } && isCancelled().not()) {
            output.write(buffer, 0, tempSize)
            notifyProgress(tempSize.toLong())
        }
        output.flush()
        output.closeArchiveEntry()
        try {
            fis.close()
        } catch (ioe: Exception) {
            Log.e(TAG, "compressCopy fis close error", ioe)
        }
    }
}