// Top-level build file where you can add configuration options common to all sub-projects/modules.
final javaVerProp = System.getProperty("java.version")
println("CI runtime info: java.version=$javaVerProp")
final javaVersion = javaVerProp.split("\\.")[0].toInteger()
final requireJava = prop_targetCompatibility.toInteger()
if (javaVersion < requireJava) {
    final errMsg = "Require to run gradle with java version at least $requireJava, " +
            "but current java version is $javaVersion. Please update project gralde JDK configs!"
    throw new IllegalStateException(errMsg)
}

buildscript {
    repositories {
        maven {
            url prop_oppoMavenUrl
            allowInsecureProtocol = true
            credentials {
                username sonatypeUsername
                password sonatypePassword
            }
        }
        maven {
            url prop_oppoTestMavenUrl
            allowInsecureProtocol = true
        }
        maven {
            url prop_oapm_oppoMavenUrl
            allowInsecureProtocol = true
        }
        maven {
            url prop_synergySdkMavenUrl
            allowInsecureProtocol = true
        }
        maven {
            url prop_sdkMavenUrlRelease
            allowInsecureProtocol = true
        }
        maven {
            url prop_oppoBrowserMavenUrl
            allowInsecureProtocol = true
            credentials {
                username sonatypeUsername
                password sonatypePassword
            }
        }
        maven {
            // accountSdk
            url prop_nexusReleaseUrl
            allowInsecureProtocol = true
            credentials {
                username sonatypeUsername
                password sonatypePassword
            }
        }
        maven {
            url prop_oppoMavenUrlSnapshot
            allowInsecureProtocol = true
            credentials {
                username sonatypeUsername
                password sonatypePassword
            }
        }
        maven {
            allowInsecureProtocol true
            url prop_maven2
        }
    }

    dependencies {
        //  commons-io:2.15.1、commons-logging:1.3.0、slf4j-api:2.0.10 编译依赖 r8 新版本， AGP 升级到 8.2.0 后可以去掉该配置
        classpath libs.android.tools.r8
        classpath libs.android.gradle.plugin
        classpath libs.oplus.oapm.plugin
        classpath libs.kotlin.gradle.plugin
        classpath libs.kotlin.serialization.plugin
        classpath libs.oplus.build.plugin
        classpath libs.google.protobuf.plugin
        classpath "com.autotest.opasm:CoverageInstPlugin:1.9.18-SNAPSHOT"
        classpath "com.compliance.tools:privacy_plugin:1.1.7"
    }
}

allprojects {
    repositories {
        maven {
            url prop_oppoMavenUrl
            allowInsecureProtocol = true
            credentials {
                username sonatypeUsername
                password sonatypePassword
            }
        }
        maven {
            url prop_oapm_oppoMavenUrl
            allowInsecureProtocol = true
        }
        maven {
            url prop_sdkMavenUrlRelease
            allowInsecureProtocol = true
        }
        maven {
            url prop_nexusReleaseUrl
            allowInsecureProtocol = true
            credentials {
                username sonatypeUsername
                password sonatypePassword
            }
        }
        maven {
            url prop_oppoMavenUrlSnapshot
            allowInsecureProtocol = true
            credentials {
                username sonatypeUsername
                password sonatypePassword
            }
        }
        //Jacoco覆盖率插桩: Maven BranchIO
        maven {
            url prop_oppoMavenUrlBranchIO
            allowInsecureProtocol = true
        }
        //Jacoco覆盖率插桩: Maven Snapshots
        maven {
            url prop_oppoMavenUrlSnapshots
            allowInsecureProtocol = true
        }
        maven {
            allowInsecureProtocol true
            url prop_maven2
        }
    }
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
    delete file("${rootDir}/reports")
    delete file("${rootDir}/UnitTestReport")
    delete file("${rootDir}/jacocoinfo.txt")
}

apply from: rootProject.file("scripts/rootPrjConfigs.gradle")