/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.audio
 ** Version: 1.0
 ** Date: 2021/7/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.audio

import androidx.annotation.Keep
import com.bumptech.glide.load.Options
import com.bumptech.glide.load.ResourceDecoder
import com.bumptech.glide.load.engine.Resource

/**
 * convert the transformed resource AudioThumbnailResultByteArray to stream, and the cache to file
 */
@Keep
class AudioThumbnailResultByteArrayResourceDecoder :
    ResourceDecoder<AudioThumbnailResultByteArray, AudioThumbnailResult> {

    override fun handles(source: AudioThumbnailResultByteArray, options: Options): Boolean {
        return true
    }

    override fun decode(
        source: AudioThumbnailResultByteArray,
        width: Int,
        height: Int,
        options: Options
    ): Resource<AudioThumbnailResult> {
        return AudioThumbnailResultResource(
            AudioThumbnailResult(
                AudioThumbnailResult.convertByteArrayToBitmap(source.mBitmapByteArray)
            )
        )
    }
}