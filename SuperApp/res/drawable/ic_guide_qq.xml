<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="328dp"
    android:height="275dp"
    android:viewportWidth="328"
    android:viewportHeight="275">
  <group>
    <clip-path
        android:pathData="M1,50h326v219.14h-326z"/>
    <path
        android:pathData="M98.46,-166.22L229.54,-166.22A29.96,29.96 0,0 1,259.5 -136.26L259.5,206.42A29.96,29.96 0,0 1,229.54 236.38L98.46,236.38A29.96,29.96 0,0 1,68.5 206.42L68.5,-136.26A29.96,29.96 0,0 1,98.46 -166.22z"
        android:fillColor="#E4E7EB"/>
    <path
        android:pathData="M98.46,-166.42L229.54,-166.42A30.16,30.16 0,0 1,259.7 -136.26L259.7,206.41A30.16,30.16 0,0 1,229.54 236.57L98.46,236.57A30.16,30.16 0,0 1,68.31 206.41L68.31,-136.26A30.16,30.16 0,0 1,98.46 -166.42z"
        android:strokeAlpha="0.12"
        android:strokeWidth="0.389602"
        android:fillColor="#00000000"
        android:strokeColor="#000000"/>
    <path
        android:pathData="M98.46,-160.6L229.54,-160.6A24.34,24.34 0,0 1,253.88 -136.26L253.88,206.42A24.34,24.34 0,0 1,229.54 230.76L98.46,230.76A24.34,24.34 0,0 1,74.12 206.42L74.12,-136.26A24.34,24.34 0,0 1,98.46 -160.6z"
        android:fillColor="#FAFAFA"/>
    <path
        android:pathData="M58.62,45.39h209.82v26.34h-209.82z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="163.53"
            android:startY="45.39"
            android:endX="163.53"
            android:endY="71.73"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="0.64" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M74,92.4C74,84.56 74,80.64 75.53,77.64C76.87,75.01 79.01,72.87 81.64,71.53C84.64,70 88.56,70 96.4,70H231.6C239.44,70 243.36,70 246.36,71.53C248.99,72.87 251.13,75.01 252.47,77.64C254,80.64 254,84.56 254,92.4V192.6C254,206.04 254,212.76 251.38,217.9C249.08,222.41 245.41,226.08 240.9,228.38C235.76,231 229.04,231 215.6,231H112.4C98.96,231 92.24,231 87.1,228.38C82.59,226.08 78.92,222.41 76.62,217.9C74,212.76 74,206.04 74,192.6V92.4Z"
        android:fillColor="#FEFEFE"/>
    <path
        android:pathData="M231.6,69.81H96.4H96.39C92.48,69.81 89.53,69.81 87.19,70C84.84,70.19 83.09,70.57 81.56,71.35C78.88,72.71 76.71,74.88 75.35,77.56C74.57,79.09 74.19,80.84 74,83.19C73.81,85.53 73.81,88.48 73.81,92.39V92.4V192.6V192.61C73.81,199.32 73.81,204.37 74.13,208.38C74.46,212.39 75.12,215.38 76.44,217.98C78.76,222.54 82.46,226.24 87.02,228.56C89.61,229.88 92.61,230.54 96.62,230.87C100.63,231.2 105.68,231.2 112.39,231.2H112.4H215.6H215.61C222.32,231.2 227.37,231.2 231.38,230.87C235.39,230.54 238.38,229.88 240.98,228.56C245.54,226.24 249.24,222.54 251.56,217.98C252.88,215.38 253.54,212.39 253.87,208.38C254.2,204.37 254.2,199.32 254.2,192.61V192.6V92.4V92.39C254.2,88.48 254.2,85.53 254,83.19C253.81,80.84 253.43,79.09 252.65,77.56C251.29,74.88 249.12,72.71 246.44,71.35C244.91,70.57 243.16,70.19 240.81,70C238.47,69.81 235.52,69.81 231.61,69.81H231.6Z"
        android:strokeAlpha="0.3"
        android:strokeWidth="0.389602"
        android:fillColor="#00000000"
        android:strokeColor="#D0D0D0"/>
    <path
        android:pathData="M120.93,217.66L207.07,217.66A1.87,1.87 0,0 1,208.94 219.53L208.94,219.53A1.87,1.87 0,0 1,207.07 221.41L120.93,221.41A1.87,1.87 0,0 1,119.06 219.53L119.06,219.53A1.87,1.87 0,0 1,120.93 217.66z"
        android:fillColor="#DFE2E5"/>
    <path
        android:pathData="M103.73,163L103.73,163A15,15 0,0 1,118.73 178L118.73,178A15,15 0,0 1,103.73 193L103.73,193A15,15 0,0 1,88.73 178L88.73,178A15,15 0,0 1,103.73 163z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M98.02,182.8V181.19C98.02,181.14 98,181.09 97.96,181.05C97.92,181.02 97.87,181 97.82,181H96.93C96.88,181 96.83,181.02 96.79,181.05C96.75,181.09 96.73,181.14 96.73,181.19V183.24C96.73,183.44 96.81,183.63 96.97,183.78C97.12,183.92 97.33,184 97.55,184H109.91C110.13,184 110.33,183.92 110.49,183.78C110.64,183.63 110.73,183.44 110.73,183.24V181.19C110.73,181.14 110.71,181.09 110.67,181.05C110.63,181.02 110.58,181 110.52,181H109.64C109.59,181 109.53,181.02 109.5,181.05C109.46,181.09 109.43,181.14 109.43,181.19V182.8H98.02Z"
        android:fillColor="#848484"/>
    <path
        android:pathData="M104.29,178.81V178.93L104.37,178.85L106.85,176.38L106.85,176.38C106.87,176.35 106.9,176.34 106.93,176.33C106.97,176.32 107,176.33 107.03,176.34L107.08,176.38L107.72,177.02C107.72,177.02 107.72,177.02 107.72,177.02C107.76,177.05 107.77,177.1 107.77,177.14C107.77,177.18 107.76,177.23 107.72,177.26C107.72,177.26 107.72,177.26 107.72,177.26L104.23,180.75C104.23,180.75 104.23,180.75 104.23,180.75C104.08,180.9 103.87,180.99 103.66,180.98C103.44,180.98 103.24,180.9 103.09,180.74L103.09,180.74L99.65,177.25L99.65,177.25C99.62,177.22 99.61,177.18 99.61,177.13C99.61,177.09 99.62,177.05 99.65,177.02C99.65,177.02 99.65,177.02 99.65,177.02L100.31,176.38L100.31,176.38C100.34,176.35 100.38,176.33 100.42,176.33C100.47,176.33 100.51,176.35 100.54,176.38C100.54,176.38 100.54,176.38 100.54,176.38L102.96,178.83L103.04,178.92V178.8V171.21C103.04,171.17 103.06,171.13 103.09,171.1C103.12,171.07 103.16,171.05 103.2,171.05H104.12C104.17,171.05 104.21,171.07 104.24,171.1C104.27,171.13 104.29,171.17 104.29,171.21V178.81Z"
        android:strokeWidth="0.100361"
        android:fillColor="#848484"
        android:strokeColor="#EBEDF0"/>
    <path
        android:pathData="M143.14,163L143.14,163A15,15 0,0 1,158.14 178L158.14,178A15,15 0,0 1,143.14 193L143.14,193A15,15 0,0 1,128.14 178L128.14,178A15,15 0,0 1,143.14 163z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M182.55,163L182.55,163A15,15 0,0 1,197.55 178L197.55,178A15,15 0,0 1,182.55 193L182.55,193A15,15 0,0 1,167.55 178L167.55,178A15,15 0,0 1,182.55 163z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M103.78,178.05m-19.84,0a19.84,19.84 0,1 1,39.67 0a19.84,19.84 0,1 1,-39.67 0"
        android:strokeWidth="3.60674"
        android:fillColor="#00000000"
        android:strokeColor="#006AFF"/>
    <path
        android:pathData="M103.73,101.78L103.84,101.78A16,16 0,0 1,119.84 117.78L119.84,117.9A16,16 0,0 1,103.84 133.9L103.73,133.9A16,16 0,0 1,87.73 117.9L87.73,117.78A16,16 0,0 1,103.73 101.78z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M143.2,102.78L143.2,102.78A15.05,15.05 0,0 1,158.25 117.84L158.25,117.84A15.05,15.05 0,0 1,143.2 132.89L143.2,132.89A15.05,15.05 0,0 1,128.14 117.84L128.14,117.84A15.05,15.05 0,0 1,143.2 102.78z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M182.45,102.78L182.75,102.78A14.91,14.91 0,0 1,197.65 117.69L197.65,117.84A14.91,14.91 0,0 1,182.75 132.74L182.45,132.74A14.91,14.91 0,0 1,167.55 117.84L167.55,117.69A14.91,14.91 0,0 1,182.45 102.78z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M182.6,102.78L182.6,102.78A15.05,15.05 0,0 1,197.65 117.84L197.65,117.84A15.05,15.05 0,0 1,182.6 132.89L182.6,132.89A15.05,15.05 0,0 1,167.55 117.84L167.55,117.84A15.05,15.05 0,0 1,182.6 102.78z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M221.86,102.78L222.16,102.78A14.91,14.91 0,0 1,237.07 117.69L237.07,117.84A14.91,14.91 0,0 1,222.16 132.74L221.86,132.74A14.91,14.91 0,0 1,206.96 117.84L206.96,117.69A14.91,14.91 0,0 1,221.86 102.78z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M222.01,102.78L222.01,102.78A15.05,15.05 0,0 1,237.07 117.84L237.07,117.84A15.05,15.05 0,0 1,222.01 132.89L222.01,132.89A15.05,15.05 0,0 1,206.96 117.84L206.96,117.84A15.05,15.05 0,0 1,222.01 102.78z"
        android:fillColor="#EBEDF0"/>
    <path
        android:pathData="M73.67,146.82L254.32,146.82"
        android:strokeWidth="0.376355"
        android:fillColor="#00000000"
        android:strokeColor="#E7E7E7"/>
  </group>
</vector>
