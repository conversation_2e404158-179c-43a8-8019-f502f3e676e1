package com.coloros.filemanager.filerefactor

import android.content.Context
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import com.filemanager.common.compat.MediaFileCompat
import com.filemanager.common.helper.FileWrapper
import com.filemanager.common.helper.MediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import io.mockk.*
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import io.mockk.impl.annotations.MockK
import org.junit.Ignore
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import java.io.File
import java.lang.Exception

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [Build.VERSION_CODES.S])
class MediaHelperTest {

    @MockK
    lateinit var mFileWrapper: FileWrapper

    @MockK
    lateinit var newFileWrapper: FileWrapper

    @MockK
    lateinit var oldFileWrapper: FileWrapper

    @MockK
    lateinit var mFile: File


    var mContext: Context? = null

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mContext = RuntimeEnvironment.application
        mFileWrapper = mockk(relaxed = true)
        newFileWrapper = mockk(relaxed = true)
        oldFileWrapper = mockk(relaxed = true)
        mFile = mockk(relaxed = true)
    }

    @After
    @Throws(Exception::class)
    fun tearDown() {
        mContext = null
        unmockkAll()
    }

    @Test
    @Throws(Exception::class)
    fun should_return_uri_when_get_uri_by_path_with_path() {
        Assert.assertNull(MediaHelper.getUriByPath(mContext, null))
        Assert.assertNull(MediaHelper.getUriByPath(mContext, ""))
    }

    @Test
    @Throws(Exception::class)
    fun should_return_uri_when_get_uri_with_file_wrapper() {
        mockkObject(MediaHelper::class)
        every { mFileWrapper.exists() } returns (true)
        every { mFileWrapper.type } returns (MimeTypeHelper.IMAGE_TYPE)
        every { mFileWrapper.absolutePath } returns ("test.jpg")
        Assert.assertNull(MediaHelper.getUri(mContext, mFileWrapper))

    }

    @Test
    @Throws(Exception::class)
    fun should_return_path_when_get_audio_file_path_with_uri() {
        Assert.assertNull(MediaHelper.getAudioFilePath(null, null))

        var uri = Mockito.mock(Uri::class.java)
        Assert.assertNull(MediaHelper.getAudioFilePath(mContext, uri))
    }

    @Test
    @Throws(Exception::class)
    fun should_return_when_delete_mediaDB_file_with_file_wrapper() {
        Assert.assertFalse(MediaHelper.deleteMediaDBFile(null, null))

        Assert.assertTrue(MediaHelper.deleteMediaDBFile(mContext, mFileWrapper))
    }

    @Test
    @Throws(Exception::class)
    fun should_return_when_update_folder_name_in_mediaDB_with_file_wrapper() {
        mockkStatic(Utils::class)
        mockkStatic(MediaFileCompat::class)
        Assert.assertFalse(MediaHelper.updateFolderNameInMediaDB(mContext, null, null))

        every { newFileWrapper.absolutePath }.returns("new file.jpg")
        every { Utils.isOperateDatabase(mContext, newFileWrapper.absolutePath) }.returns(false)
        Assert.assertFalse(
            MediaHelper.updateFolderNameInMediaDB(
                mContext,
                oldFileWrapper,
                newFileWrapper
            )
        )

        every { Utils.isOperateDatabase(mContext, newFileWrapper.absolutePath) }.returns(true)
        every { oldFileWrapper.type }.returns(MimeTypeHelper.DIRECTORY_TYPE)
        every { oldFileWrapper.absolutePath }.returns("old file.jpg")
        Assert.assertFalse(
            MediaHelper.updateFolderNameInMediaDB(
                mContext,
                oldFileWrapper,
                newFileWrapper
            )
        )

        every { oldFileWrapper.type }.returns(MimeTypeHelper.IMAGE_TYPE)
        every { MediaFileCompat.getMimeTypeForFile(oldFileWrapper.absolutePath) }.returns("image/*")
        every { MediaFileCompat.getMimeTypeForFile(newFileWrapper.absolutePath) }.returns("image/*")
        Assert.assertTrue(
            MediaHelper.updateFolderNameInMediaDB(
                mContext,
                oldFileWrapper,
                newFileWrapper
            )
        )

    }

    @Test
    fun should_return_when_check_file_exist_with_path() {
        mockkStatic(SdkUtils::class)
        every { SdkUtils.isAtLeastR() }.returns(true)
        Assert.assertTrue(MediaHelper.checkFileExist("test"))
        every { SdkUtils.isAtLeastR() }.returns(false)
        Assert.assertFalse(MediaHelper.checkFileExist("test"))
    }

    @Test
    fun should_return_checkArgumentVolumeName() {
        Assert.assertEquals(
            MediaStore.VOLUME_INTERNAL, Whitebox.invokeMethod<MediaHelper>(
                MediaHelper::class.java,
                "checkArgumentVolumeName",
                ""
            )
        )
        Assert.assertEquals(
            MediaStore.VOLUME_INTERNAL, Whitebox.invokeMethod<MediaHelper>(
                MediaHelper::class.java,
                "checkArgumentVolumeName",
                MediaStore.VOLUME_INTERNAL
            )
        )
        Assert.assertEquals(
            MediaStore.VOLUME_EXTERNAL, Whitebox.invokeMethod<MediaHelper>(
                MediaHelper::class.java,
                "checkArgumentVolumeName",
                MediaStore.VOLUME_EXTERNAL
            )
        )
        Assert.assertEquals(
            MediaStore.VOLUME_EXTERNAL_PRIMARY, Whitebox.invokeMethod<MediaHelper>(
                MediaHelper::class.java,
                "checkArgumentVolumeName",
                MediaStore.VOLUME_EXTERNAL_PRIMARY
            )
        )
        Assert.assertNotNull(
            Whitebox.invokeMethod<MediaHelper>(
                MediaHelper::class.java,
                "checkArgumentVolumeName",
                "unknown"
            )
        )
    }
}