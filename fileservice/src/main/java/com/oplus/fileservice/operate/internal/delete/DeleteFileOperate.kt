/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DeleteFileOperate
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/20 9:40
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/6/20       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.delete

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.filemanager.common.utils.Log
import com.oplus.fileservice.operate.convert.OperateRequest
import com.oplus.fileservice.operate.internal.FileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.DeleteOptionsInvalid
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.InvalidOperation
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.InvalidSourceFileInfo
import com.oplus.fileservice.operate.internal.OperateStatusCode.Companion.OperateFailure
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.internal.BaseFileOperate

class DeleteFileOperate(
    private val context: Context
) : BaseFileOperate() {

    override fun run(operateRequest: OperateRequest): OperationResult {
        Log.d(TAG, "run -> operateRequest = $operateRequest")
        if (operateRequest.sourceFileInfo.isNullOrEmpty()) {
            return OperationResult(false, InvalidSourceFileInfo)
        }
        return deleteFile(operateRequest)
    }

    override fun recycle() {
        // do nothing
    }

    override fun afterRun(result: OperationResult) {
        Log.d(TAG, "afterRun -> result = $result")
    }

    private fun deleteFile(operateRequest: OperateRequest): OperationResult {
        val deleteOption = operateRequest.option
        Log.d(TAG, "deleteFile -> isDeleteForever = $deleteOption")
        if (deleteOption == null || deleteOption !is Int) {
            return OperationResult(false, DeleteOptionsInvalid)
        } else {
            operateRequest.sourceFileInfo?.apply {
                val files = ArrayList<FileInfo>(this.size)
                files.addAll(this)
                val delete: IDelete? = obtainDeleteByOption(deleteOption)
                return delete?.delete(this) ?: OperationResult(false, InvalidOperation)
            }
            return OperationResult(false, OperateFailure)
        }
    }

    @VisibleForTesting
    fun obtainDeleteByOption(deleteOption: Int): IDelete? {
        val delete: IDelete? = when (deleteOption) {
            OPTION_DELETE -> InternalDelete(context)
            OPTION_RECYCLE -> InternalRecycle(context)
            OPTION_ERASE -> InternalErase(context)
            else -> null
        }
        return delete
    }

    companion object {
        private const val TAG = "DeleteFileOperate"
        private const val OPTION_DELETE = 1
        private const val OPTION_RECYCLE = 2
        private const val OPTION_ERASE = 3
    }
}