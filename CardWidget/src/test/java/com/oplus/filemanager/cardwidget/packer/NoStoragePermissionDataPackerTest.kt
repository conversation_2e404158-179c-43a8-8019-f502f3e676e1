/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : NoStoragePermissionDataPackerTest.kt
 ** Description : NoStoragePermissionDataPacker Unit Test
 ** Version     : 1.0
 ** Date        : 2023/11/23
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  chao.xue       2023/11/23      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.packer

import android.content.Context
import android.view.View
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.cardwidget.label.packer.NoStoragePermissionDataPacker
import com.oplus.filemanager.cardwidget.util.TextUtil
import com.oplus.smartenginehelper.dsl.DSLCoder
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test

class NoStoragePermissionDataPackerTest {

    private lateinit var context: Context
    private lateinit var coder: DSLCoder
    private lateinit var packer: NoStoragePermissionDataPacker

    @Before
    fun setup() {
        context = mockk()
        mockkObject(MyApplication)
        every { MyApplication.sAppContext }.returns(context)
        every { context.packageName }.returns("FileManager")
        coder = mockk()
        packer = NoStoragePermissionDataPacker(context)
    }

    @After
    fun teardown() {
        unmockkObject(MyApplication)
    }

    @Test
    fun should_call_setTextViewText_when_onPack() {
        mockkStatic(TextUtil::class)
        mockkStatic(Utils::class)
        justRun { TextUtil.setTextSize(any(), any(), any(), any()) }
        every { Utils.getDateFormat(any(), any()) }.returns("10:20:30")
        justRun { coder.setOnClick(any(), any()) }
        justRun { coder.setTextViewText(any(), any()) }
        justRun { coder.setVisibility(any(), any()) }

        packer.onPack(coder)
        verify { coder.setOnClick("container", any()) }
        verify { coder.setTextViewText("tips", "@string/manage_files_permission_title") }

        unmockkStatic(TextUtil::class)
        unmockkStatic(Utils::class)
    }
}