final isAppModule = project.plugins.hasPlugin("com.android.application")
final isLibModule = project.plugins.hasPlugin("com.android.library")
if (!(isAppModule || isLibModule)) {
    final errMsg = "${project.name}: unitTest.gradle must be applied to " +
            "an Android application or library module. Please apply " +
            "com.android.application or com.android.library first."
    System.err.println(errMsg)
    throw new GradleException(errMsg)
}

apply plugin: 'jacoco'

jacoco {
    toolVersion = libs.versions.jacoco.get()
}

android {
    defaultConfig {
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
        unitTests.all {
            jacoco {
                includeNoLocationClasses = true
                excludes = ['jdk.internal.*']
            }
            jvmArgs '-noverify'
            jvmArgs('--add-opens=java.base/java.lang=ALL-UNNAMED')
            jvmArgs('--add-opens=java.base/java.lang.reflect=ALL-UNNAMED')
            maxHeapSize = "4096m"
            maxParallelForks = Runtime.runtime.availableProcessors().intdiv(2) ?: 1
            if (!org.gradle.internal.os.OperatingSystem.current().isWindows()) {
                systemProperty 'robolectric.dependency.repo.url', prop_oplusMavenUrl
                systemProperty 'robolectric.dependency.repo.id', 'local'
                systemProperty 'robolectric.dependency.repo.username', sonatypeUsername
                systemProperty 'robolectric.dependency.repo.password', sonatypePassword
            }
            reports.html.destination = file("${rootDir}/UnitTestReport")
        }
    }
}

dependencies {
    testImplementation libs.junit.base
    testImplementation libs.bundles.test.mockito
    testImplementation libs.bundles.test.mockk
    testImplementation libs.kotlinx.coroutines.test
    testImplementation libs.robolectric.base
    testImplementation libs.bundles.test.powermock
    testImplementation libs.androidx.arch.core.testing
    testImplementation libs.koin.test
    testImplementation libs.koin.test.junit4
}

// Auto add compileOnly dependencies to testImplementation since necessary for compiling unit test
afterEvaluate {
    final rootPrjName = rootProject.name
    configurations.configureEach { conf ->
        final confName = conf.name
        if (confName.toLowerCase().endsWith("compileonly") &&
                !confName.startsWith("test") && !confName.startsWith("androidTest")) {
            final testConf = "test${confName.capitalize()}".replace("CompileOnly", "Implementation")
            conf.dependencies.each {
                if (it.group.startsWith(rootPrjName)) {
                    final modulePath = "${it.group}:${it.name}".replace(".", ":").replace(rootPrjName, "")
                    println("${project.path}: add $modulePath to $testConf since module has it in $confName")
                    project.dependencies."$testConf" project(modulePath)
                } else {
                    final dependency = "${it.group}:${it.name}:${it.version}"
                    println("${project.path}: add $dependency to $testConf since module has it in $confName")
                    project.dependencies."$testConf" dependency
                }
            }
        }
    }
}