/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : TouchShareManager
 * * Description : 碰一碰分享的Manager
 * * Version     : 1.0
 * * Date        : 2025/05/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.framework.touchshare

import android.content.Context
import android.content.Intent
import androidx.annotation.WorkerThread
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.thread.ThreadUtils
import com.filemanager.common.utils.Log
import com.oplus.filemanager.framework.touchshare.data.ShareCardOptionsFactory
import com.oplus.filemanager.framework.touchshare.data.ShareCardUIParams
import com.oplus.filemanager.framework.touchshare.data.ShareDataFactory
import com.oplus.filemanager.framework.touchshare.reject.RejectReasonFactory
import com.oplus.interconnect.share.sdk.Share
import com.oplus.interconnect.share.sdk.ShareAgent
import com.oplus.interconnect.share.sdk.ShareEventCallback
import com.oplus.interconnect.share.sdk.data.ShareEvent
import com.oplus.interconnect.share.sdk.data.SharedData
import java.util.Objects
import java.util.function.Consumer

object TouchShareManager {

    private const val TAG = "TouchShareManager"
    private const val ACTION_RECEIVE_SHARE_DATA = "com.oplus.interconnect.action.SHARED_DATA"
    private var agent: ShareAgent? = null

    private val shareClient: Share by lazy {
        create(context = MyApplication.appContext)
    }

    fun create(context: Context): Share {
        return Share.create(context)
    }

    /**
     * 碰一碰分享是否可用
     */
    fun isAvailable(): Boolean {
        val isSupport = shareClient.isTapShareAvailable()
        Log.w(TAG, "isAvailable :$isSupport")
        return isSupport
    }

    @WorkerThread
    fun registerTapShareEvent(consumer: Consumer<Boolean>): Boolean {
        if (!isAvailable()) {
            Log.e(TAG, "registerTapShareEvent is not available")
            return false
        }
        val future = shareClient.on(ShareEvent.TAP_SHARE, object : ShareEventCallback {
            override fun onAgentFound(shareAgent: ShareAgent) {
                Log.w(TAG, "registerTapShareEvent onAgentFound:$shareAgent")
                agent = shareAgent
                consumer.accept(true)
            }
        })
        val result = future.get()
        Log.d(TAG, "registerTapShareEvent result:${ResultCodeDesc.desc(result.resultCode)}")
        return ResultCodeDesc.isSuccess(result.resultCode)
    }

    @WorkerThread
    fun unregisterTapShareEvent(): Boolean {
        if (!isAvailable()) {
            Log.e(TAG, "unregisterTapShareEvent is not available")
            return false
        }
        val future = shareClient.off(ShareEvent.TAP_SHARE)
        val result = future.get()
        Log.d(TAG, "unregisterTapShareEvent result:${ResultCodeDesc.desc(result.resultCode)}")
        return ResultCodeDesc.isSuccess(result.resultCode)
    }

    /**
     * 分享
     * @param from 从哪个页面分享
     * @param files 分享的文件
     * @param uiParams 分享界面的参数
     */
    fun share(from: Int, files: List<BaseFileBean>, uiParams: ShareCardUIParams): Boolean {
        Log.d(TAG, "share from:$from files:${files.size} agent:$agent")
        val agent = agent ?: return false
        // 构建参数
        val data = ShareDataFactory.create(from, files)
        val cardOptions = ShareCardOptionsFactory.create(uiParams)
        Log.d(TAG, "share options:$cardOptions data:${data.getRecords()}")
        // 开始分享
        val future = agent.share(data, cardOptions)
        // 获取分享结果
        val result = future.get()
        Log.d(TAG, "share result:${ResultCodeDesc.desc(result.resultCode)}")
        return ResultCodeDesc.isSuccess(result.resultCode)
    }

    /**
     * 拒绝分享
     * @param code 拒绝分享的理由
     */
    fun rejectShare(code: Int): Boolean {
        Log.d(TAG, "reject $code agent:$agent")
        val agent = agent ?: return false
        // 构建拒绝理由
        val reason = RejectReasonFactory.create(code)
        // 开始拒绝
        val future = agent.rejectShare(reason)
        // 获取结果
        val result = future.get()
        Log.d(TAG, "reject result:${ResultCodeDesc.desc(result.resultCode)}")
        return ResultCodeDesc.isSuccess(result.resultCode)
    }

    /**
     * 恢复分享
     */
    fun resumeShare(from: Int, files: List<BaseFileBean>): Boolean {
        Log.d(TAG, "resumeShare from:$from, files:${files.size} agent:$agent")
        val agent = agent ?: return false
        // 构建参数
        val data = ShareDataFactory.create(from, files, true)
        // 开始分享
        val future = agent.resumeShare(data)
        // 获取结果
        val result = future.get()
        Log.d(TAG, "resumeShare result:${ResultCodeDesc.desc(result.resultCode)}")
        return ResultCodeDesc.isSuccess(result.resultCode)
    }

    private fun getSharedData(intent: Intent): SharedData? {
        val action = intent.action
        if (!Objects.equals(ACTION_RECEIVE_SHARE_DATA, action)) {
            Log.w(TAG, "getSharedData action:$action isn't action:$ACTION_RECEIVE_SHARE_DATA")
            return null
        }
        if (!isAvailable()) {
            Log.w(TAG, "getSharedData isn't available!!!")
            return null
        }
        val sharedData = shareClient.getSharedData(intent)
        return sharedData
    }

    /**
     * 获取分享数据中的文件列表信息
     */
    fun getShareFiles(intent: Intent): List<BaseFileBean> {
        return ThreadUtils.callOnAsyncThread {
            val sharedData = getSharedData(intent)
            ShareDataFactory.parse(sharedData)
        }
    }

    /**
     * 获取分享数据中的文件列表简略信息
     */
    fun getShareSimpleFile(intent: Intent): Pair<Int, BaseFileBean?> {
        return ThreadUtils.callOnAsyncThread {
            val sharedData = getSharedData(intent)
            ShareDataFactory.parseSimple(sharedData)
        }
    }

    /**
     * 是否触发了碰一碰
     */
    fun isTriggerTouch(): Boolean {
        return agent != null
    }

    /**
     * 释放资源
     */
    fun release() {
        Log.w(TAG, "release")
        agent = null
    }
}