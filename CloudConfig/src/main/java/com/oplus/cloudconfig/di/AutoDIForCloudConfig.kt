/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: AutoDIForCloudConfig.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2024/6/6
 ** Author: yangqichang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.cloudconfig.di

import androidx.annotation.Keep
import com.oplus.cloudconfig.CloudConfigApi
import com.oplus.filemanager.interfaze.cloudconfig.ICloudConfigApi
import org.koin.dsl.module

@Keep
class AutoDIForCloudConfig {

    val cloudConfigApi = module {
        single<ICloudConfigApi>(createdAtStart = true) {
            CloudConfigApi
        }
    }
}