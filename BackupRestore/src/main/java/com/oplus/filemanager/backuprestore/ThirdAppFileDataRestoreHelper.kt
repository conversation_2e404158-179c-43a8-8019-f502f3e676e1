/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: ThirdAppFileDataRestoreHelper.kt
 ** Description: 搬家恢复的功能逻辑类
 ** Version: 1.0
 ** Date: 2024/6/12
 ** Author: huangyuanwang
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.backuprestore

import android.util.Log
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.appswitch.IAppSwitchApi
import com.oplus.filemanager.provider.ThirdAppFileDBHelper
import com.oplus.filemanager.room.model.ThirdAppFileDataEntity

object ThirdAppFileDataRestoreHelper {

    const val TAG = "ThirdAppFileDataRestoreHelper"

    @JvmStatic
    fun compareAndRestoreThirdFileDataList(
        backupThirdAppDataList: List<ThirdAppFileDataEntity>,
        localThirdAppDataList: List<ThirdAppFileDataEntity>?
    ) {
        if (!localThirdAppDataList.isNullOrEmpty()) {
            val newThirdAppFileList =
                getNewBackupThirdAppDataList(backupThirdAppDataList, localThirdAppDataList)
            for (index in newThirdAppFileList.size - 1 downTo 0) {
                newThirdAppFileList[index].let {
                    preprocessBackupEntity(it)
                    ThirdAppFileDBHelper.insertThirdAppData(it)
                }
            }
        } else {
            for (index in backupThirdAppDataList.size - 1 downTo 0) {
                backupThirdAppDataList[index].let {
                    preprocessBackupEntity(it)
                    ThirdAppFileDBHelper.insertThirdAppData(it)
                }
            }
        }
        trigerDbSync()
    }

    @JvmStatic
    fun indexFieldEqual(one: ThirdAppFileDataEntity, two: ThirdAppFileDataEntity): Boolean {
        val nameEqual = one.mFileName == two.mFileName
        val packageEqual = one.mDectPackage == two.mDectPackage
        val sourceNameEqual = one.mSourceName == two.mSourceName
        val result = nameEqual && packageEqual && sourceNameEqual
        Log.i(TAG, "indexFieldEqual one $one, two $two, result $result")
        return result
    }

    @JvmStatic
    fun contentEqual(one: ThirdAppFileDataEntity, two: ThirdAppFileDataEntity): Boolean {
        val cloneOne = one.copy()
        val cloneTwo = two.copy()
        preprocessBackupEntity(cloneOne)
        preprocessBackupEntity(cloneTwo)
        val result = cloneOne == cloneTwo
        Log.i(TAG, "contentEqual one $one, two $two, result $result")
        return result
    }

    @JvmStatic
    fun checkContentEqualOrIndexEqual(
        one: ThirdAppFileDataEntity,
        two: ThirdAppFileDataEntity
    ): Boolean {
        val indexEqual = indexFieldEqual(one, two)
        val result = if (indexEqual) {
            true
        } else {
            val contentEqual = contentEqual(one, two)
            contentEqual
        }
        Log.i(TAG, "checkContentEqualOrIndexEqual one $one, two $two, result $result")
        return result
    }

    @JvmStatic
    fun getNewBackupThirdAppDataList(
        backupThirdAppDataList: List<ThirdAppFileDataEntity>,
        localThirdAppDataList: List<ThirdAppFileDataEntity>
    ): MutableList<ThirdAppFileDataEntity> {
        Log.d(TAG, "getNewBackupThirdAppDataList start")
        val newRestoreThirdAppList = mutableListOf<ThirdAppFileDataEntity>()
        backupThirdAppDataList.forEach { backupThirdEntity ->
            val needInsert = localThirdAppDataList.find { localThirdEntity ->
                checkContentEqualOrIndexEqual(backupThirdEntity, localThirdEntity)
            } == null
            if (needInsert) {
                newRestoreThirdAppList.add(backupThirdEntity)
            } else {
                Log.w(TAG, "mergeThirdAppDataList backupThirdEntity $backupThirdEntity no need insert, continue")
            }
        }
        Log.d(
            TAG, "getNewBackupThirdAppDataList end result ${
                newRestoreThirdAppList.map {
                    it.id.toString().plus(":").plus(it.mFileName).plus(":").plus(it.mSourceName)
                        .plus(":").plus(it.mDectPackage)
                }
            }"
        )
        return newRestoreThirdAppList
    }

    @JvmStatic
    private fun preprocessBackupEntity(inputEntity: ThirdAppFileDataEntity) {
        inputEntity.id = null
        inputEntity.mIndentification = null
        inputEntity.mIndexCheckSum = 0L
    }

    @JvmStatic
    fun trigerDbSync() {
        val appSwitchApi = Injector.injectFactory<IAppSwitchApi>()
        Log.i(TAG, "trigerDbSync")
        appSwitchApi?.trigDbSync(true)
    }
}