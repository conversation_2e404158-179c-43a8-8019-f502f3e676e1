package com.oplus.filemanager.filechoose.ui.folderpicker

import android.app.Activity
import android.content.Intent
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.PathLoadResult
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.path.FileBrowPathHelper
import com.filemanager.common.path.FilePathHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.fileoperate.createdir.FileActionCreateDir
import com.oplus.filemanager.filechoose.factor.LoaderFactor
import com.oplus.selectdir.filebrowser.SelectFileBrowserViewModel
import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * FolderPickerFragmentViewModel的单元测试类
 * 用于测试文件夹选择器视图模型的各种功能
 */
@ExperimentalCoroutinesApi
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class FolderPickerFragmentViewModelTest {

    // 使用InstantTaskExecutorRule确保LiveData的同步执行
    @get:Rule
    val instantTaskRule = InstantTaskExecutorRule()

    // 测试用的协程调度器
    private val testDispatcher = TestCoroutineDispatcher()

    // 模拟的LoaderController，使用RelaxedMockK自动提供默认实现
    @RelaxedMockK
    private lateinit var mockLoaderController: LoaderController

    // 模拟的Activity
    @MockK
    private lateinit var mockActivity: BaseVMActivity

    // 模拟的BaseFileBean
    @MockK
    private lateinit var mockBaseFileBean: BaseFileBean

    // 模拟的路径帮助类
    @MockK
    private lateinit var mockPathHelper: FileBrowPathHelper

    // 待测试的ViewModel实例
    private lateinit var viewModel: FolderPickerFragmentViewModel

    /**
     * 测试前的准备工作
     */
    @Before
    fun setup() {
        // 初始化MockK注解
        MockKAnnotations.init(this)
        // 设置主调度器为测试调度器
        Dispatchers.setMain(testDispatcher)
        // 模拟LoaderFactor对象
        mockkObject(LoaderFactor)
        // 模拟Utils类的静态方法
        mockkStatic(Utils::class)
        // 模拟Log类的静态方法
        mockkStatic(Log::class)
        // 设置Log.d()总是执行但不做任何操作
        every { Log.d(any(), any()) } just Runs
        // 设置快速点击检测总是返回false
        every { Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK) } returns false
    }

    /**
     * 测试后的清理工作
     */
    @After
    fun tearDown() {
        // 恢复主调度器
        Dispatchers.resetMain()
        // 清理测试协程
        testDispatcher.cleanupTestCoroutines()
        // 解除所有模拟
        unmockkAll()
    }

    /**
     * 测试initLoader方法 - 当loader为null时应该初始化loader
     */
    @Test
    fun `initLoader should initialize loader when loader is null`() {
        // Given - 准备测试数据
        val testPath = "/test/path"
        viewModel = spyk(FolderPickerFragmentViewModel())
        every { viewModel.mPathHelp } returns mockPathHelper
        every { mockPathHelper.pushTo(any()) } just Runs

        // When - 执行测试方法
        viewModel.initLoader(mockLoaderController, testPath)

        // Then - 验证结果
        verify { mockLoaderController.initLoader(any<Int>(), any<OnLoaderListener<PathLoadResult<Int, BaseFileBean>>>()) }
        assertNotNull(viewModel.mPositionModel.value)
        assertEquals(testPath, viewModel.mPositionModel.value?.mCurrentPath)
    }

    /**
     * 测试onItemClick方法 - 当位置超出范围时不应继续处理
     */
    @Test
    fun `onItemClick should not proceed when position out of bounds`() {
        // Given
        viewModel = spyk(FolderPickerFragmentViewModel())
        every { viewModel.mUiState.value } returns FolderPickerFragmentViewModel.FolderUiModel(
            mutableListOf(), false
        )

        // When
        viewModel.onItemClick(mockActivity, 1, 0, 0)

        // Then
        verify(exactly = 0) { viewModel.launch(any()) }
    }

    /**
     * 测试onItemClick方法 - 快速点击时不应继续处理
     */
    @Test
    fun `onItemClick should not proceed on quick click`() {
        // Given
        viewModel = spyk(FolderPickerFragmentViewModel())
        every { viewModel.mUiState.value } returns FolderPickerFragmentViewModel.FolderUiModel(
            mutableListOf(mockBaseFileBean), false
        )
        every { Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK) } returns true

        // When
        viewModel.onItemClick(mockActivity, 0, 0, 0)

        // Then
        coVerify(exactly = 0) { mockBaseFileBean.checkExist() }
    }

    /**
     * 测试onButtonClick方法 - 使用空文件名时应设置正确的结果
     */
    @Test
    fun `onButtonClick should set result with empty file name`() {
        // Given
        viewModel = spyk(FolderPickerFragmentViewModel())
        viewModel.mPositionModel.value = SelectFileBrowserViewModel.PositionModel(
            "/current/path", 0, 0
        )
        val slot = slot<Intent>()
        every { mockActivity.setResult(any(), capture(slot)) } just Runs
        every { mockActivity.finish() } just Runs

        // When
        viewModel.onButtonClick(mockActivity, "")

        // Then
        verify { mockActivity.setResult(Activity.RESULT_OK, any()) }
        verify { mockActivity.finish() }
        assertEquals("file:///current/path", slot.captured.getStringExtra("SAVE_PATH"))
    }

    /**
     * 测试FolderPickerCallBack的onLoadComplete方法 - 应正确更新UI状态
     */
    @Test
    fun `FolderPickerCallBack onLoadComplete should update UI state`() {
        // Given
        viewModel = spyk(FolderPickerFragmentViewModel())
        val callback = viewModel.mLoaderCallback
        val testData = PathLoadResult<Int, BaseFileBean>(
            mutableListOf(mockBaseFileBean),
            hashMapOf<Int, BaseFileBean>()
        )

        // When
        callback.onLoadComplete(viewModel, testData)

        // Then
        assertNotNull(viewModel.mUiState.value)
        assertTrue(viewModel.mUiState.value!!.mInitState)
        assertEquals(1, viewModel.mUiState.value!!.mFileList.size)
    }
}