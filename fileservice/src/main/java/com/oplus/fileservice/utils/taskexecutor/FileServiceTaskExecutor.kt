/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileServiceTaskExecutor
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/1 10:31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/1       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.utils.taskexecutor

import android.os.Handler
import android.os.Looper
import com.oplus.fileservice.utils.SerialExecutor
import java.util.concurrent.Executor

class FileServiceTaskExecutor(backgroundExecutor: Executor) : TaskExecutor {

    // Wrap it with a serial executor so we have ordering guarantees on commands being executed.
    private val mBackgroundExecutor: SerialExecutor = SerialExecutor(backgroundExecutor)

    private val mMainThreadHandler = Handler(Looper.getMainLooper())

    private val mMainThreadExecutor = Executor { runnable -> postToMainThread(runnable) }

    override fun postToMainThread(runnable: Runnable) {
        mMainThreadHandler.post(runnable)
    }

    override fun getMainThreadExecutor(): Executor = mMainThreadExecutor

    override fun executeOnBackgroundThread(runnable: Runnable) {
        mBackgroundExecutor.execute(runnable)
    }

    override fun getBackgroundExecutor(): SerialExecutor = mBackgroundExecutor
}