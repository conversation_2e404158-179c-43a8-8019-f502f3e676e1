/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DFMConflictDialogFactoryTest
 ** Description : DFM Conflict Dialog Factory Unit Test
 ** Version     : 1.0
 ** Date        : 2024/04/09 14:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/04/09       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.categorydfm.utils

import android.app.Activity
import android.content.DialogInterface
import com.filemanager.fileoperate.R
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test

class DFMConflictDialogFactoryTest {

    private lateinit var activity: Activity
    private lateinit var listener: DialogInterface.OnClickListener

    @Before
    fun setup() {
        activity = mockk()
        listener = DialogInterface.OnClickListener { dialog, which -> }
        mockkStatic(DFMConflictDialogFactory::class)
        every { DFMConflictDialogFactory.showFuncInterruptDialog(any(), any(), any()) }.returns(mockk())
        every { DFMConflictDialogFactory.showChannelOccupiedDialog(any(), any(), any(), any()) }.returns(mockk())
    }

    @After
    fun teardown() {
        unmockkStatic(DFMConflictDialogFactory::class)
    }

    @Test
    fun test_showMobileScreencastInterruptDialog() {
        every { DFMConflictDialogFactory.showMobileScreencastInterruptDialog(any(), any()) }.answers { callOriginal() }
        DFMConflictDialogFactory.showMobileScreencastInterruptDialog(activity, listener)
        verify { DFMConflictDialogFactory.showFuncInterruptDialog(activity, com.filemanager.common.R.string.mobile_screencast, listener) }
    }

    @Test
    fun test_showWirelessScreencastInterruptDialog() {
        every { DFMConflictDialogFactory.showWirelessScreencastInterruptDialog(any(), any()) }.answers { callOriginal() }
        DFMConflictDialogFactory.showWirelessScreencastInterruptDialog(activity, listener)
        verify { DFMConflictDialogFactory.showFuncInterruptDialog(activity, com.filemanager.common.R.string.wireless_screencast, listener) }
    }

    @Test
    fun test_showPersonalHotspotInterruptDialog() {
        every { DFMConflictDialogFactory.showPersonalHotspotInterruptDialog(any(), any()) }.answers { callOriginal() }
        DFMConflictDialogFactory.showPersonalHotspotInterruptDialog(activity, listener)
        verify { DFMConflictDialogFactory.showFuncInterruptDialog(activity, com.filemanager.common.R.string.personal_hotspot, listener) }
    }

    @Test
    fun test_showCrossScreenMirrorInterruptDialog() {
        every { DFMConflictDialogFactory.showCrossScreenMirrorInterruptDialog(any(), any()) }.answers { callOriginal() }
        DFMConflictDialogFactory.showCrossScreenMirrorInterruptDialog(activity, listener)
        verify { DFMConflictDialogFactory.showFuncInterruptDialog(activity, com.filemanager.common.R.string.cross_screen_mirror, listener) }
    }

    @Test
    fun test_showTransferChannelOccupiedDialog() {
        every { DFMConflictDialogFactory.showTransferChannelOccupiedDialog(any(), any()) }.answers { callOriginal() }
        DFMConflictDialogFactory.showTransferChannelOccupiedDialog(activity, listener)
        verify {
            DFMConflictDialogFactory.showChannelOccupiedDialog(
                activity,
                com.filemanager.common.R.string.transfer_channel_occupied,
                com.filemanager.common.R.string.transfer_channel_occupied_solve_tips,
                listener
            )
        }
    }

    @Test
    fun test_showScreencastChannelOccupiedDialog() {
        every { DFMConflictDialogFactory.showScreencastChannelOccupiedDialog(any(), any()) }.answers { callOriginal() }
        DFMConflictDialogFactory.showScreencastChannelOccupiedDialog(activity, listener)
        verify {
            DFMConflictDialogFactory.showChannelOccupiedDialog(
                activity,
                com.filemanager.common.R.string.screencast_channel_occupied,
                com.filemanager.common.R.string.screencast_channel_occupied_solve_tips,
                listener
            )
        }
    }
}