/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SelectDirViewModelTest
 ** Description : SelectDirViewModel Unit Test
 ** Version     : 1.0
 ** Date        : 2022/8/12
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2022/8/12      1.0        create
 ***********************************************************************/
package com.oplus.selectdir

import com.filemanager.common.path.FileBrowPathHelper
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Test

class SelectDirViewModelTest {

    @Test
    fun should_when_isChildPath() {
        val model = SelectDirViewModel()
        Assert.assertFalse(model.isChildPath())

        val pathHelper = mockk<FileBrowPathHelper>(relaxed = true)
        model.mPathHelp = pathHelper

        every { pathHelper.hasUp() }.returns(false)
        Assert.assertFalse(model.isChildPath())

        every { pathHelper.hasUp() }.returns(true)
        Assert.assertTrue(model.isChildPath())
    }
}