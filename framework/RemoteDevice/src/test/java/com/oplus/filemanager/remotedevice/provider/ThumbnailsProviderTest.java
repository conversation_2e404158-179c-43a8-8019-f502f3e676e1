package com.oplus.filemanager.remotedevice.provider;

import android.content.Context;
import android.content.ContentValues;
import android.content.pm.ProviderInfo;
import android.database.Cursor;
import android.net.Uri;
import android.os.ParcelFileDescriptor;
import android.os.Environment;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * ThumbnailsProvider的单元测试类
 * 用于测试ThumbnailsProvider的各种功能和行为
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 29)
public class ThumbnailsProviderTest {
    // 模拟的Context对象
    private Context mContext;
    // 被测试的ThumbnailsProvider实例
    private ThumbnailsProvider mProvider;
    // 测试用的Authority字符串
    private static final String TEST_AUTHORITY = "com.oplus.filemanager.test.provider";
    // 测试用的文件名
    private static final String TEST_FILE_NAME = "test.txt";
    // 测试用的显示名称
    private static final String TEST_DISPLAY_NAME = "display.txt";
    // 测试用的文件对象
    private File mTestFile;

    /**
     * 测试前的初始化方法
     * 创建模拟对象和测试环境
     */
    @Before
    public void setUp() {
        // 创建模拟的Context对象
        mContext = Mockito.mock(Context.class);
        // 设置模拟的PackageManager
        when(mContext.getPackageManager()).thenReturn(Mockito.mock(android.content.pm.PackageManager.class));
        // 创建被测试的ThumbnailsProvider实例
        mProvider = new ThumbnailsProvider();
        // 创建ProviderInfo对象并设置测试参数
        ProviderInfo info = new ProviderInfo();
        info.authority = TEST_AUTHORITY;
        info.grantUriPermissions = true;
        info.metaData = new android.os.Bundle();
        info.metaData.putInt("android.support.FILE_PROVIDER_PATHS", 0);
        // 调用attachInfo方法初始化Provider
        mProvider.attachInfo(mContext, info);
        
        // 在每个测试前清除静态缓存
        synchronized (ThumbnailsProvider.class) {
            HashMap<String, ThumbnailsProvider.PathStrategy> sCache = new HashMap<>();
            sCache.clear();
        }
    }

    /**
     * 测试后的清理方法
     * 删除测试过程中创建的文件
     */
    @After
    public void tearDown() throws Exception {
        if (mTestFile != null && mTestFile.exists()) {
            mTestFile.delete();
        }
    }

    /**
     * 测试onCreate方法
     * 验证Provider创建是否成功
     */
    @Test
    public void testOnCreate() {
        assertTrue(mProvider.onCreate());
    }

    /**
     * 测试attachInfo方法在没有权限时抛出异常
     * 验证安全机制是否正常工作
     */
    @Test(expected = SecurityException.class)
    public void testAttachInfoWithoutPermission() {
        ProviderInfo info = new ProviderInfo();
        info.authority = TEST_AUTHORITY;
        info.grantUriPermissions = false;
        mProvider.attachInfo(mContext, info);
    }

    /**
     * 测试getTypeAnonymous方法
     * 验证匿名访问时返回的MIME类型是否正确
     */
    @Test
    public void testGetTypeAnonymous() {
        Uri uri = Uri.parse("content://" + TEST_AUTHORITY + "/test");
        assertEquals("application/octet-stream", mProvider.getTypeAnonymous(uri));
    }

    /**
     * 测试insert方法抛出异常
     * 验证Provider是否禁止外部插入操作
     */
    @Test(expected = UnsupportedOperationException.class)
    public void testInsert() {
        mProvider.insert(Uri.parse("content://" + TEST_AUTHORITY + "/test"), new ContentValues());
    }

    /**
     * 测试update方法抛出异常
     * 验证Provider是否禁止外部更新操作
     */
    @Test(expected = UnsupportedOperationException.class)
    public void testUpdate() {
        mProvider.update(Uri.parse("content://" + TEST_AUTHORITY + "/test"), new ContentValues(), null, null);
    }
}