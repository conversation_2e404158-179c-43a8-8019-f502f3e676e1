package com.filemanager.setting.ui.privacy

import android.app.Activity
import android.content.Intent
import android.view.MenuItem
import android.view.View
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.WindowUtils
import com.filemanager.setting.R
import com.filemanager.setting.utils.FlexibleWindowUtil
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.oplus.flexiblewindow.FlexibleWindowManager

class SettingPrivacyActivity : BaseVMActivity() {

    companion object {
        const val TAG = "SettingPrivacyActivity"
        fun start(activity: Activity?) {
            try {
                activity?.apply {
                    val intent = Intent(activity, SettingPrivacyActivity::class.java)
                    FlexibleWindowUtil.startActivityForFlexibleWindow(activity, intent)
                }
            } catch (e: Exception) {
                Log.w(TAG, "start: ${e.message}")
            }
        }
    }
    private lateinit var mAppBarLayout: COUIDividerAppBarLayout
    private lateinit var mToolbar: COUIToolbar
    private lateinit var mContainerView: View

    override fun getLayoutResId(): Int {
        return R.layout.activity_setting
    }

    override fun initView() {
        registerVmChangedReceiver(null)
        mAppBarLayout = findViewById(R.id.appbar)
        mToolbar = findViewById(R.id.toolbar)
        mContainerView = findViewById(R.id.fragment_container)
        mToolbar.title = resources.getString(com.filemanager.common.R.string.privacy_string)
        mToolbar.isTitleCenterStyle = false
        setSupportActionBar(mToolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        initToolbar()
        FlexibleWindowUtil.setBarBackgroundColor(mToolbar, mAppBarLayout, window.decorView, resources.configuration, this)
        FlexibleWindowUtil.setActionCloseFlexibleActivity(window.decorView, this)
    }

    private fun getViewTopPadding(): Int {
        return StatusBarUtil.getStatusBarHeight() + mToolbar.height
    }

    private fun initToolbar() {
        val res = MyApplication.appContext.resources
        var appBarTopPadding = StatusBarUtil.getStatusBarHeight()
        if (FeatureCompat.isApplicableForFlexibleWindow && WindowUtils.isMiddleAndLargeScreen(this)
            && FlexibleWindowManager.isFlexibleActivitySuitable(resources.configuration)) {
            appBarTopPadding =
                res.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_16dp)
            mToolbar.post {
                mContainerView.setPadding(
                    mContainerView.paddingLeft,
                    mToolbar.height + res.getDimensionPixelOffset(com.filemanager.common.R.dimen.dimen_12dp),
                    mContainerView.paddingRight,
                    0
                )
                initFragment()
            }
        } else {
            mToolbar.post {
                mContainerView.setPadding(
                    mContainerView.paddingLeft,
                    getViewTopPadding(),
                    mContainerView.paddingRight,
                    0
                )
                initFragment()
            }
        }
        mAppBarLayout.setPadding(0, appBarTopPadding, 0, 0)
    }

    private fun initFragment() {
        var fragment = supportFragmentManager.findFragmentByTag(TAG)
        if ((fragment == null) || (fragment !is SettingPrivacyFragment)) {
            fragment = SettingPrivacyFragment()
        }
        val ft = supportFragmentManager.beginTransaction()
        ft.replace(R.id.fragment_container, fragment, TAG)
        ft.show(fragment)
        ft.commitAllowingStateLoss()
    }

    override fun startObserve() {
    }

    override fun initData() {
    }

    override fun refreshCurrentPage(action: String?, data: String?) {
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterVmChangedReceiver()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        super.onUIConfigChanged(configList)
        initToolbar()
        FlexibleWindowUtil.setBarBackgroundColor(mToolbar, mAppBarLayout, window.decorView, resources.configuration, this)
    }
}