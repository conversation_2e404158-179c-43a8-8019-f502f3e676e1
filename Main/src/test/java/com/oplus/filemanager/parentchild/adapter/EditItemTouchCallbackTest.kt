/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.MainApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.parentchild.adapter

import android.app.Activity
import android.content.Context
import android.view.View
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.main.adapter.MainCategoryAdapter
import com.oplus.filemanager.main.adapter.MainListItemAdapter
import io.mockk.MockKAnnotations
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkStatic
import org.junit.Before
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import kotlin.test.Test

@RunWith(JUnit4::class)
class EditItemTouchCallbackTest {

    // region Mocks
    private lateinit var mockParentRecyclerView: RecyclerView
    private lateinit var mockInnerRecyclerView: RecyclerView
    private lateinit var mockInnerViewHolder: RecyclerView.ViewHolder
    private lateinit var mockMainListItemViewHolder: MainListItemAdapter.ViewHolder
    private lateinit var mockMainCategoryViewHolder: MainCategoryAdapter.ViewHolder
    private lateinit var editItemTouchCallback: EditItemTouchCallback
    private lateinit var mockActivity: Activity
    private lateinit var mockContext: Context
    private lateinit var itemView: View

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        mockkStatic(Utils::class)

        // 初始化 Activity 和 Context
        mockActivity = mockk(relaxed = true)
        mockContext = mockk(relaxed = true) {
            every { applicationContext } returns mockk(relaxed = true)
        }
        MyApplication.init(mockContext)

        // 模拟父 RecyclerView
        mockParentRecyclerView = mockk(relaxed = true) {
            every { computeVerticalScrollOffset() } returns 0
            every { smoothScrollBy(any(), any()) } just Runs
        }

        // 模拟内部 RecyclerView 的父级为 mockParentRecyclerView
        mockInnerRecyclerView = mockk(relaxed = true) {
            every { parent } returns mockParentRecyclerView
        }
        itemView = mockk(relaxed = true)
        // 模拟内部 ViewHolder 的 itemView
        val mockInnerItemView: View = mockk(relaxed = true) {
            every { parent } returns mockInnerRecyclerView
            every { height } returns 100
            every { getLocationOnScreen(any()) } answers {
                val pos = firstArg<IntArray>()
                pos[0] = 0
                pos[1] = 500
            }
        }
        // 模拟内部 ViewHolder
        mockInnerViewHolder = mockk(relaxed = true)
        // 模拟 MainListItemAdapter.ViewHolder
        mockMainListItemViewHolder = mockk(relaxUnitFun = true) {
            every { editable } returns true
            every { onItemSelected() } just Runs
        }

        // 关键修复点：正确模拟 MainCategoryAdapter.ViewHolder
        mockMainCategoryViewHolder = mockk(relaxed = true) {  // 添加 relaxed = true
            every { isEdit } returns true
        }

        // 静态方法模拟
        every { Utils.getNavigationBarHeight(any()) } returns 48.dpToPx()
        every { mockActivity.resources.getDimensionPixelOffset(any()) } returns 56.dpToPx()

        // 初始化被测类
        editItemTouchCallback = EditItemTouchCallback(mockActivity, mockInnerViewHolder)
    }

    private fun Int.dpToPx(): Int = this * 3

    @Test
    fun `getMovementFlags for MainCategoryViewHolder should return all directions`() {
        val flags = editItemTouchCallback.getMovementFlags(mockk(), mockMainCategoryViewHolder)
        val expectedFlags = ItemTouchHelper.UP or ItemTouchHelper.DOWN or
                ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT
        assert(flags and expectedFlags == expectedFlags)
    }
}