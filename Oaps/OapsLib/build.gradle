plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace "com.oplus.oaps"
}

dependencies {
    implementation libs.google.protobuf.java

    implementation libs.oppo.marker.oaps.download

    implementation libs.koin.android

    implementation project(':Oaps:MultiChannel')

    implementation project(':Common')
}