/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.filemanager.categorycompress.ui.compress
 * * Version     : 1.0
 * * Date        : 2020/7/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager

import com.filemanager.categorycompress.ui.CategoryCompressViewModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.wrapper.MediaFileWrapper
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class CategoryCompressViewModelTest : BaseTest() {
    private val mCategoryCompressViewModel = CategoryCompressViewModel()

    @Before
    override fun setUp() {
        mContext = RuntimeEnvironment.application
        val file1 = MediaFileWrapper()
        file1.mId = 1
        val file2 = MediaFileWrapper()
        file2.mId = 2
        val file3 = MediaFileWrapper()
        file3.mId = 3
        val list = ArrayList<MediaFileWrapper>()
        list.add(file1)
        list.add(file2)
        list.add(file3)
        mCategoryCompressViewModel.mUiState.value = BaseUiModel(list, mCategoryCompressViewModel.mModeState,
                ArrayList(), HashMap())
    }

    @Test
    @Throws(Exception::class)
    fun should_clickToolbarSelectAll_when_view_model_fun_clickToolbarSelectAll() {
        mCategoryCompressViewModel.mUiState.value?.mSelectedList?.clear()
        mCategoryCompressViewModel.clickToolbarSelectAll()
        Assert.assertEquals(mCategoryCompressViewModel.mUiState.value?.mFileList?.size, mCategoryCompressViewModel.mUiState.value?.mSelectedList?.size)
        mCategoryCompressViewModel.clickToolbarSelectAll()
        Assert.assertEquals(0, mCategoryCompressViewModel.mUiState.value?.mSelectedList?.size)
        mCategoryCompressViewModel.mUiState.value?.mSelectedList?.add(mCategoryCompressViewModel.mUiState.value?.mFileList?.get(0)?.mId
                ?: 0)
        mCategoryCompressViewModel.clickToolbarSelectAll()
        Assert.assertEquals(mCategoryCompressViewModel.mUiState.value?.mFileList?.size, mCategoryCompressViewModel.mUiState.value?.mSelectedList?.size)
    }

    @After
    override fun tearDown() {
        mContext = null
    }

}