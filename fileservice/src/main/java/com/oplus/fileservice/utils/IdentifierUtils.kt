/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : IdentifierUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/1 14:17
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/1       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.utils

import android.content.Context
import androidx.annotation.WorkerThread
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import com.oplus.stdid.bean.StdIDInfo
import com.oplus.stdid.sdk.StdIDSDK
import java.lang.reflect.Method

object IdentifierUtils {
    private const val TAG = "IdentifierUtils"

    private var mIdProviderImpl: Any? = null
    private var mClass: Class<*>? = null
    private var mGetVAIDMethod: Method? = null

    @WorkerThread
    @JvmStatic
    fun init(context: Context) {
        Log.d(TAG, "init")
        StdIDSDK.init(context)
        initInvokeMethod()
    }

    @WorkerThread
    @JvmStatic
    fun getDUID(context: Context): String? {
        Log.i(TAG, "getDUID -> isSupport Std = ${StdIDSDK.isSupported()}")
        return if (StdIDSDK.isSupported()) {
            val duid = StdIDSDK.getStdIds(context, StdIDInfo.Type_DUID).DUID
            CollectPrivacyUtils.collectDUID(duid)
            duid
        } else {
            invokeMethod(context, mGetVAIDMethod)
        }
    }

    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    private fun invokeMethod(context: Context, method: Method?): String? {
        if ((mIdProviderImpl != null) && (method != null)) {
            try {
                val result = method.invoke(mIdProviderImpl, context)
                if (result != null) {
                    return result as String
                }
            } catch (e: Exception) {
                Log.e(TAG, "invokeMethod error: ${e.message}")
            }
        } else {
            Log.e(TAG, "invokeMethod mIdProviderImpl $mIdProviderImpl or method : $method is null!!")
        }
        return null
    }

    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    private fun initInvokeMethod() {
        Log.d(TAG, "initInvokeMethod")
        try {
            mIdProviderImpl = mClass?.newInstance()
            mGetVAIDMethod = mClass?.getMethod("getVAID", Context::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "initInvokeMethod error: ${e.message}")
        }
    }
}