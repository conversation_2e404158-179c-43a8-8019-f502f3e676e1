/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentFileCardItemDataExtTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/23
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/6/23      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.ext

import com.filemanager.common.helper.MimeTypeHelper
import com.oplus.filemanager.cardwidget.data.RecentFileCardItemData
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Test

class RecentFileCardItemDataExtTest {

    @Test
    fun isSupportDurationTest() {
        val data = mockk<RecentFileCardItemData>()
        every { data.type } returns MimeTypeHelper.VIDEO_TYPE
        Assert.assertEquals(true, data.isSupportDuration())

        every { data.type } returns MimeTypeHelper.AUDIO_TYPE
        Assert.assertEquals(false, data.isSupportDuration())
    }
}