/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchFileType
 ** Description : 搜索的文件类型
 ** Version     : 1.0
 ** Date        : 2024/06/17 11:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/06/17       1.0      create
 ***********************************************************************/

package com.oplus.filemanager.apiproxy.services.utils

import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType

object SearchFileType {

    private const val TAG = "SearchFileType"

    const val TYPE_ALL_POSITION = 0
    const val TYPE_DOC_POSITION = 1
    const val TYPE_DOCX_POSITION = 1
    const val TYPE_XLS_POSITION = 2
    const val TYPE_XLSX_POSITION = 2
    const val TYPE_PPT_POSITION = 3
    const val TYPE_PPTX_POSITION = 3
    const val TYPE_PDF_POSITION = 4
    const val TYPE_OFD_POSITION = 5
    const val TYPE_IWORK_POSITION = 6
    const val TYPE_XMIND_POSITION = 7
    const val TYPE_VISIO_POSITION = 8
    const val TYPE_TXT_POSITION = 9
    const val TYPE_CAD_POSITION = 10
    const val TYPE_PSD_POSITION = 11
    const val TYPE_AI_POSITION = 12
    const val TYPE_MD_POSITION = 13

    @JvmStatic
    fun getDocSuffix(fileType: Int): String {
        val suffixList = getDocAllSuffix()
        val suffix = suffixList[fileType - 1]
        Log.d(TAG, "getDocSuffix fileType:$fileType -> suffix:$suffix")
        return suffix
    }

    @JvmStatic
    fun getDocAllSuffix(): List<String> {
        val suffixList = arrayListOf<String>()
        suffixList.add(".doc")
        suffixList.add(".xls")
        suffixList.add(".ppt")
        suffixList.add(".pdf")
        suffixList.add(".ofd")
        suffixList.add(".iwork")
        suffixList.add(".xmind")
        suffixList.add(".visio")
        suffixList.add(".txt")
        suffixList.add(".cad")
        suffixList.add(".psd")
        suffixList.add(".ai")
        suffixList.add(".md")
        return suffixList
    }

    /**
     * 根据fileType 获取所有的后缀列表
     * @param fileType 文件类型 0~13
     * @return 文件后缀列表
     */
    @JvmStatic
    fun getFilterSuffixList(fileType: Int): List<String> {
        // 根据fileType获取后缀列表
        val suffixList = if (fileType == TYPE_ALL_POSITION) {
            getDocAllSuffix()
        } else {
            listOf(getDocSuffix(fileType))
        }
        Log.d(TAG, "getFilterSuffixList fileType:$fileType suffixList:$suffixList")
        val documentExtensionType = Injector.injectFactory<IDocumentExtensionType>() ?: return suffixList
        val filterExtList = documentExtensionType.getAllSelectionArgs(ArrayList(suffixList)).filterNotNull()
        Log.d(TAG, "getFilterSuffixList fileType:$fileType list:$filterExtList")
        return filterExtList
    }

    /**
     * 判断是否支持dmp
     */
    @JvmStatic
    fun isSupportDmp(fileType: Int): Boolean {
        val supportType = hashSetOf(TYPE_ALL_POSITION, TYPE_DOC_POSITION, TYPE_XLS_POSITION, TYPE_PPT_POSITION)
        return supportType.contains(fileType)
    }
}