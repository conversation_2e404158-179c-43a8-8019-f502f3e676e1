{"layout_width": "match_parent", "layout_height": "match_parent", "package": "com.coloros.filemanager", "background": "@drawable/ic_card_background", "id": "container", "type": "constraint", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "match_parent", "layout_height": "wrap_content", "layout_marginEnd": "@dimen/dimen_16dp", "layout_marginStart": "@dimen/dimen_16dp", "type": "constraint", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toTopOf": "parent", "layout_constraintBottom_toBottomOf": "parent", "id": "mid_item_container", "child": [{"layout_constraintEnd_toEndOf": "parent", "layout_width": "wrap_content", "layout_height": "wrap_content", "textSize": "@dimen/dimen_16dp", "layout_constraintTop_toTopOf": "parent", "type": "text", "textColor": "?attr/couiColorLabelPrimary", "layout_constraintStart_toStartOf": "parent", "textAppearance": "@style/couiTextHeadlineXS", "textAlignment": "center", "id": "tips", "fontFamily": "sans-serif-medium", "text": "TextView"}, {"paddingBottom": "4dp", "paddingTop": "4dp", "layout_constraintEnd_toEndOf": "parent", "layout_width": "wrap_content", "layout_height": "wrap_content", "textSize": "@dimen/font_size_14", "textColor": "?attr/couiColorLabelTheme", "textAppearance": "@style/couiTextButtonM", "fontFamily": "sans-serif-medium", "layout_marginTop": "@dimen/dimen_8dp", "layout_constraintStart_toStartOf": "parent", "layout_constraintTop_toBottomOf": "tips", "type": "text", "id": "links", "text": "TextView"}]}]}