/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CategoryAppService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/8 9:38
 ** Author      : W9060445
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9060445        2024/1/8       1.0      create
 ***********************************************************************/
package com.oplus.cloudconfig.service

import com.oplus.cloudconfig.bean.CategoryAppBean
import com.oplus.nearx.cloudconfig.anotation.Config
import com.oplus.nearx.cloudconfig.observable.Observable
import com.oplus.nearx.cloudconfig.stat.Const

@Config(configCode = "category_app", type = Const.CONFIG_TYPE_DATABASE)
interface CategoryAppService {

    fun getAllCategoryApp(): Observable<List<CategoryAppBean>>
}