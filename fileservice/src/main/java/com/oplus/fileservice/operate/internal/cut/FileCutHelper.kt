/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileCutHelper
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/8 14:45
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/8       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.cut

import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import java.io.File

object FileCutHelper {
    private const val TAG = "FileCutHelper"
    private val mInternalPath = VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)

    @Suppress("TooGenericExceptionCaught")
    fun cutFile(sourceFile: File, destFile: File, checkParentSuccess: (path: String) -> Boolean): Boolean {
        fun internalCutFile(sourceFile: File, destFile: File): Boolean {
            return try {
                if (!sourceFile.exists()) {
                    return checkParentSuccess.invoke(sourceFile.absolutePath)
                } else {
                    sourceFile.renameTo(destFile).let {
                        if (!it) {
                            Log.d(TAG, "internalCutFile: failed[source_exist=${sourceFile.exists()}," +
                                    " dest_exist=${destFile.exists()}]: ${sourceFile.absolutePath}")
                        }
                        it
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "internalCutFile: failed to rename file: ${destFile.name}, ${e.message}")
                false
            }
        }

        if (sourceFile.isDirectory) {
            if (destFile.exists()) {
                JavaFileHelper.listFiles(sourceFile)?.forEach {
                    if (!cutFile(it, File(destFile, it.name), checkParentSuccess)) {
                        return false
                    }
                }
                sourceFile.delete()
                return true
            } else {
                return internalCutFile(sourceFile, destFile)
            }
        } else {
            if (destFile.exists()) {
                Log.d(TAG, "internalCutFile: delete dest: ${destFile.absolutePath}")
                destFile.delete()
            }
            return internalCutFile(sourceFile, destFile)
        }
    }

    fun isDirectoriesSameDisk(source: List<BaseFileBean>, target: String): Boolean {
        val storageList = VolumeEnvironment.getVolumePathList()?.let { volumeList ->
            val specialNode = if (SdkUtils.isAtLeastR()) {
                arrayOf("Android/data", "Android/obb")
            } else {
                arrayOf("Android/obb")
            }
            val newList = mutableListOf<String>()
            volumeList.forEach {
                var tmpVolume = it
                var oldVolume = it
                if (it.endsWith(File.separator)) {
                    oldVolume = it.substring(0, it.length - 1)
                } else {
                    tmpVolume = it.plus(File.separator)
                }
                newList.add(oldVolume)
                specialNode.forEach { node ->
                    /**
                     * NOTE: Must add it into index 0, to make sure the special node
                     * can be checked first in the next logic
                     */
                    newList.add(0, tmpVolume.plus(node))
                }
            }
            newList
        } ?: return true

        fun getVolume(path: String?): String? {
            if (path.isNullOrEmpty().not()) {
                for (volume in storageList) {
                    if (path!!.startsWith(volume, true)) {
                        return volume
                    }
                }
            }
            return null
        }

        val destVolume = getVolume(target)
        if (destVolume.isNullOrEmpty().not()) {
            source.forEach {
                if (getVolume(it.mData) != destVolume) {
                    return false
                }
            }
        }
        return true
    }

    /**
     * Pair.first is size, second is mean has image in internal storage or not
     */
    fun checkSizeAndInternalStorageImage(file: File): Pair<Long, Boolean> {
        var size = 0L
        var hasImg = false

        fun innerCheckSizeAndImage(file: File) {
            size += file.length() // include the self of this dir
            if (file.isDirectory) {
                JavaFileHelper.listFiles(file)?.forEach {
                    innerCheckSizeAndImage(it)
                }
            } else if (!hasImg) {
                hasImg = (MimeTypeHelper.getTypeFromPath(file.name) == MimeTypeHelper.IMAGE_TYPE)
                        && file.absolutePath.startsWith(mInternalPath)
            }
        }

        innerCheckSizeAndImage(file)
        return Pair(size, hasImg)
    }
}