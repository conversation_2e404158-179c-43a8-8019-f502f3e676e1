/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScriptMatchUtils.kt
 * Description:
 *     The utils to match script with conditions.
 *
 * Version: 1.0
 * Date: 2024-06-05
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-06-05   1.0    Create this module
 *********************************************************************************/
@file:JvmName("ScriptMatchUtils")

package com.oplus.filemanager.simulateclick.engine.utils

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.oplus.filemanager.simulateclick.engine.Script
import com.oplus.filemanager.simulateclick.engine.UiDevice

/**
 * Use it to mock this utils by mockkStatic
 */
@VisibleForTesting
internal const val SCRIPT_MATCH_UTILS_CLASS_NAME =
    "com.oplus.filemanager.simulateclick.engine.utils.ScriptMatchUtils"

private const val APP_PKG_SPLIT = "@@"

internal fun Script.getAppPackageName(): String? =
    appPkg?.split(APP_PKG_SPLIT)?.getOrNull(0)

internal fun Script.matchHasSkill(skillName: String): Boolean {
    skills?.forEach {
        if (it.skillName == skillName) {
            return true
        }
    }
    return false
}

internal fun Script.matchAppVersion(context: Context, packageName: String): Boolean {
    val pkgInScript = getAppPackageName() ?: return false
    if (packageName != pkgInScript) {
        return false
    }
    val appVer = UiDevice.getAppVersionCode(context, packageName) ?: return false
    return appVer in appMinVersion..appMaxVersion
}

internal fun Script.matchScreenDisplay(context: Context): Boolean {
    val currentDisplay = UiDevice.getCurrentScreenDisplay(context)
    return screenDisplay.hasFlag(currentDisplay)
}

internal fun Script.matchScreenOrientation(context: Context): Boolean {
    val currentOrientation = UiDevice.getCurrentScreenOrientation(context)
    return screenOrientation.hasFlag(currentOrientation)
}

internal fun Script.matchScreenRotation(context: Context): Boolean {
    val currentRotation = UiDevice.getCurrentScreenRotation(context)
    return screenRotation.hasFlag(currentRotation)
}