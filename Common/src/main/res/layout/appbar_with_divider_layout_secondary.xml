<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.COUIDividerAppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/appbar_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?attr/couiColorBackgroundWithCard"
    android:clickable="true"
    android:importantForAccessibility="no"
    app:elevation="0dp">

    <com.coui.appcompat.toolbar.COUIToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        style="@style/COUIToolBarInAppBarLayoutStyle"
        app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

</com.google.android.material.appbar.COUIDividerAppBarLayout>