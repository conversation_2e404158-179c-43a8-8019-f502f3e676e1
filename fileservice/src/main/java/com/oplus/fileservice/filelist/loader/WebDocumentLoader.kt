/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WebDocumentLoader
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/26 16:05
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/26       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.loader

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import android.text.TextUtils
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MediaHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.DOC_TYPE_OFD
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.DOC_TYPE_PDF
import com.oplus.filemanager.category.document.docformat.DocFormatEnhancement.DOC_TYPE_TXT
import com.oplus.filemanager.category.document.ui.DocumentFilter
import com.oplus.filemanager.category.document.ui.DocumentViewModel
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.fileservice.utils.WebFileConstant
import java.util.Locale
import kotlin.collections.ArrayList

class WebDocumentLoader : WebBaseUriLoader<MediaFileWrapper> {
    companion object {
        private const val TAG = "WebDocumentLoader"
        private const val INDEX_ID = 0
        private const val INDEX_DATA = 1
        private const val INDEX_DISPLAY_NAME = 2
        private const val INDEX_SIZE = 3
        private const val INDEX_DATE_MODIFIED = 4
        private const val INDEX_DATE_MIME_TYPE = 5
        private val MEDIA_PROJECT = arrayOf(
            MediaStore.Files.FileColumns._ID,
            MediaStore.Files.FileColumns.DATA,
            MediaStore.Files.FileColumns.DISPLAY_NAME,
            MediaStore.Files.FileColumns.SIZE,
            MediaStore.Files.FileColumns.DATE_MODIFIED,
            MediaStore.Files.FileColumns.MIME_TYPE
        )


        fun getAllSelectionArgs(fileExts: ArrayList<String>?): ArrayList<String?> {
            val selectionArgs = ArrayList<String?>()
            if (fileExts.isNullOrEmpty()) {
                selectionArgs.addAll(DocumentViewModel.DEF_ALL_EXT_ARRAY)
            } else {
                for (fileExt in fileExts) {
                    selectionArgs.add(fileExt)
                    if (!DOC_TYPE_PDF.equals(fileExt, true)
                        && !DOC_TYPE_OFD.equals(fileExt, true)
                        && !DOC_TYPE_TXT.equals(fileExt, true)
                    ) {
                        selectionArgs.add("${fileExt}x")
                    }
                }
            }
            return selectionArgs
        }
    }

    /**
     * If the settings--storage-space call open the page,
     * the flag [mIsNeedFilter] will be set to false and only the files in the phone storage will be displayed.
     * Otherwise, the flag will be set to true, which is called internally by the File-Manager,
     * and need to check whether the file needs to be filtered
     */
    private var mIsNeedFilter: Boolean = Utils.getFilterState()
    private var mTabPosition: Int = 0
    private var mSql: String? = null
    private var mInternalPath: String? = null
    private var mSort: Int = -1
    private var mExtension: String? = null
    private var mDocArray: ArrayList<String>? = null
    private var mLowerCaseSearchKey: String? = null
    private var mBaseQuerySelection: String = ""
    private var mFilter: DocumentFilter? = null
    private var mIsInit = true

    constructor(
        context: Context,
        uri: Uri?,
        sql: String?,
        tabPosition: Int,
        extension: String?,
        docArray: ArrayList<String>?
    ) : super(context) {
        mInternalPath = VolumeEnvironment.getInternalSdPath(appContext)
        mSql = sql
        mTabPosition = tabPosition
        mUri = uri
        mExtension = extension
        mDocArray = docArray
        super.initData()
        initSelection()
        mSort = SortHelper.FILE_NAME_ORDER
        mFilter = DocumentFilter()
    }

    private fun initSelection() {
        val sql = StringBuilder()
        val documentExtensionType = Injector.injectFactory<IDocumentExtensionType>()
        when (mTabPosition) {
            //All Tab
            0 -> {
                if (TextUtils.isEmpty(mSql)) {
                    val sqlString = documentExtensionType?.getDocumentSqlQuery(
                        documentExtensionType.getAllSelectionArgs(mDocArray)
                    )
                    sql.append(sqlString)
                } else {
                    sql.append(mSql)
                }
            }
            //Other Tab
            else -> {
                mExtension?.let {
                    val fileExtensions = ArrayList<String>()
                    fileExtensions.add(".${it.lowercase(Locale.getDefault())}")
                    val sqlString = documentExtensionType?.getDocumentSqlQuery(
                        documentExtensionType.getAllSelectionArgs(fileExtensions)
                    )
                    sql.append(sqlString)
                }
            }
        }
        if (!mIsNeedFilter) {
            if (!TextUtils.isEmpty(sql)) {
                sql.append(" AND ")
            }
            if (!SdkUtils.isAtLeastR()) {
                /**Confirmed with Media colleagues (XiongBobo W9002523)
                 * that both VOLUME_NAME [MediaStore.VOLUME_EXTERNAL_PRIMARY] [MediaStore.VOLUME_EXTERNAL] are stored on the phone,
                 * so they are added.*/
                sql.append(" ( ")
                sql.append(MediaStore.Files.FileColumns.VOLUME_NAME + " = '" + MediaStore.VOLUME_EXTERNAL_PRIMARY + "'")
                sql.append(" or ")
                sql.append(MediaStore.Files.FileColumns.VOLUME_NAME + " = '" + MediaStore.VOLUME_EXTERNAL + "'")
                sql.append(" ) ")
            } else {
                sql.append(MediaStore.Files.FileColumns.DATA + " LIKE '%" + mInternalPath + "%'")
            }
        }
        mBaseQuerySelection = sql.toString()
    }


    fun setSort(sort: Int) {
        mSort = if (sort < 0) {
            SortModeUtils.getSharedSortMode(
                appContext,
                SortRecordModeFactory.getCategoryKey(CategoryHelper.CATEGORY_DOC)
            )
        } else {
            sort
        }
        mSortOrder = getSortOrder()
    }

    override fun getSortOrder(): String {
        return getOrderWithType(mSort)
    }

    override fun getUri(): Uri? {
        mUri?.let {
            MediaHelper.FILE_URI
        }
        return mUri
    }

    override fun getSelection(): String = mBaseQuerySelection

    override fun getSelectionArgs(): Array<String>? {
        return null
    }

    override fun getObserverUri(): Array<Uri>? {
        Log.w("Not yet implemented")
        return null
    }

    override fun getProjection(): Array<String> {
        return MEDIA_PROJECT
    }

    override fun preHandleBeforeBackground() {
        super.preHandleBeforeBackground()
        if (mIsInit) {
            mFilter?.updateFilterOptions(mSort)
            mIsNeedFilter = Utils.getFilterState()
            mIsInit = false
        }
    }

    override fun createFromCursor(cursor: Cursor, uri: Uri?): MediaFileWrapper? {
        val id = cursor.getInt(INDEX_ID)
        val data = cursor.getString(INDEX_DATA)
        val displayName = cursor.getString(INDEX_DISPLAY_NAME)
        val size = cursor.getLong(INDEX_SIZE)
        var dateModified = cursor.getLong(INDEX_DATE_MODIFIED) * WebFileConstant.ONE_THOUSAND
        val mimeType = cursor.getString(INDEX_DATE_MIME_TYPE)
        if (dateModified == 0L) {
            Log.d(TAG, "dateModified is 0")
            dateModified = FileTimeUtil.getFileTime(data) ?: 0
        }
        val mediaFileWrapper = MediaFileWrapper(
            id,
            data,
            displayName,
            mimeType,
            size,
            dateModified,
            MediaHelper.FILE_URI
        )
        if (TextUtils.isEmpty(mediaFileWrapper.mData) || TextUtils.isEmpty(mediaFileWrapper.mDisplayName)) {
            Log.d(TAG, "createFromCursor file is empty")
            return null
        }
        //file interface is slowly on Android R,So need not to call exists on Android R
        if (!SdkUtils.isAtLeastR() && !JavaFileHelper.exists(mediaFileWrapper)) {
            Log.d(TAG, "createFromCursor file not exists or isDirectory")
            return null
        }
        if (mIsNeedFilter && (mFilter?.checkIsFilterItem(mediaFileWrapper) == true)) {
            Log.d(TAG, "createFromCursor filter file")
            return null
        }
        if (mLowerCaseSearchKey.isNullOrEmpty() || mediaFileWrapper.mDisplayName!!.lowercase(
                Locale.getDefault()
            ).contains(mLowerCaseSearchKey!!.lowercase(Locale.getDefault()))
        ) {
            return mediaFileWrapper
        }
        return null
    }

    override fun preHandleResultBackground(list: MutableList<MediaFileWrapper>): MutableList<MediaFileWrapper> {
        if ((mSort != SortHelper.FILE_TIME_REVERSE_ORDER) && (mSort != SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER)) {
            SortHelper.sortCategoryFiles(list, mSort, CategoryHelper.CATEGORY_COMPRESS, true)
        }
        return list
    }
}