/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.factor
 * * Version     : 1.0
 * * Date        : 2020/2/7
 * * Author      : <EMAIL>
 * *
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.oplus.filemanager.keymove.factor

import com.filemanager.common.MyApplication
import com.oplus.filemanager.keymove.ui.KeyMoveLoader


class LoaderFactor {
    companion object {

        @JvmStatic
        fun getKeyMoveLoader(): KeyMoveLoader {
            return KeyMoveLoader(MyApplication.sAppContext)
        }
    }

}
