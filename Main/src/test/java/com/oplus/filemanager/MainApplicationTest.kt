package com.oplus.filemanager

import android.app.Activity
import android.app.Application
import android.content.Context
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.constants.CommonConstants.AD_SWITCH_STATUS
import com.filemanager.common.constants.CommonConstants.AD_SWITCH_UPLOAD_TIME
import com.filemanager.common.utils.AndroidDataHelper.PREF_ANDROID_DATA_ACCESS
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.ad.IAdvertApi
import com.oplus.filemanager.interfaze.appswitch.IAppSwitchApi
import com.oplus.filemanager.interfaze.categoryremotedevice.ICategoryRemoteDeviceApi
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import com.oplus.filemanager.interfaze.filecloudbrowser.IFileCloudBrowser
import com.oplus.filemanager.interfaze.heytapaccount.IHeytapAccount
import com.oplus.filemanager.interfaze.questionnaire.IQuestionnaire
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.main.ui.MainActivity
import com.oplus.filemanager.category.remotedevice.disconect.SafeWorkManagerInitializer
import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.impl.annotations.SpyK
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import java.lang.reflect.Method
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * MainApplication 的单元测试类
 * 测试 MainApplication 的核心功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class MainApplicationTest {

    // 使用SpyK注解创建被测应用的部分mock对象
    @SpyK
    private var application: MainApplication = spyk(MainApplication())

    // 使用RelaxedMockK创建宽松mock的Activity对象
    @RelaxedMockK
    private lateinit var mockActivity: Activity

    // 使用MockK创建各种接口的mock对象
    @MockK
    private lateinit var mockAdvertApi: IAdvertApi
    @MockK
    private lateinit var mockAppSwitchApi: IAppSwitchApi
    @MockK
    private lateinit var mockDmpApi: IDmpSearchApi
    @MockK
    private lateinit var mockFileCloudBrowser: IFileCloudBrowser
    @MockK
    private lateinit var mockHeytapAccount: IHeytapAccount
    @MockK
    private lateinit var mockQuestionnaire: IQuestionnaire
    @MockK
    private lateinit var mockSuperApp: ISuperApp
    @MockK
    private lateinit var mockCategoryRemoteApi: ICategoryRemoteDeviceApi
    @MockK
    private lateinit var mockLifecycleOwner: LifecycleOwner

    /**
     * 测试前的初始化方法
     * 1. 初始化MockK注解
     * 2. 清空MyApplication中的活动列表
     * 3. 重置计数器
     * 4. 初始化各种mock对象
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        MyApplication.activities.clear()
        MyApplication.activityResumedCounter = 0
        MyApplication.appInBackground = null
        MyApplication.openFileCauseBg = false
        MyApplication.isColdLaunch = true

        // mock静态工具类
        mockkObject(PreferencesUtils)
        mockkObject(FeatureCompat)
        mockkObject(ThreadManager.sThreadManager)
        mockkObject(PrivacyPolicyController)
        mockkObject(Injector)  // 修复Injector依赖
        mockkObject(OptimizeStatisticsUtil)  // 统一mock OptimizeStatisticsUtil
        MyApplication.init(RuntimeEnvironment.application, "")  // 初始化MyApplication实例
    }

    /**
     * 测试后的清理方法
     * 解除所有mock
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试onCreate方法是否正确初始化应用和生命周期观察者
     * 1. 先调用attachBaseContext初始化上下文
     * 2. 调用onCreate
     * 3. 验证initApp和initActivityCallback方法被调用
     * 4. 验证生命周期状态
     */
    @Test
    fun `onCreate should initialize app and lifecycle observer`() {
        // 先初始化attachBaseContext
        val context = RuntimeEnvironment.application
        val attachBaseContextMethod = MainApplication::class.java.getDeclaredMethod(
            "attachBaseContext", 
            Context::class.java
        ).apply { isAccessible = true }
        attachBaseContextMethod.invoke(application, context)

        application.onCreate()

        val initAppMethod = MainApplication::class.java.getDeclaredMethod("initApp")
            .apply { isAccessible = true }
        initAppMethod.invoke(application)

        val initActivityCallbackMethod = MainApplication::class.java.getDeclaredMethod("initActivityCallback")
            .apply { isAccessible = true }
        initActivityCallbackMethod.invoke(application)

        assertTrue(ProcessLifecycleOwner.get().lifecycle.currentState != Lifecycle.State.DESTROYED)
    }

    /**
     * 测试initQuestionnaire方法在未同意隐私政策时的行为
     * 1. 模拟未同意隐私政策
     * 2. 调用initQuestionnaire方法
     * 3. 验证绑定监听器但未初始化SDK
     */
    @Test
    fun `initQuestionnaire should bind listener when privacy not agreed`() {
        every { PrivacyPolicyController.hasAgreeUseNet() } returns false

        val method = MainApplication::class.java.getDeclaredMethod("initQuestionnaire")
            .apply { isAccessible = true }
        method.invoke(application)

        verify { PrivacyPolicyController.bindPrivacyPolicyListener(application) }
        verify(exactly = 0) { mockQuestionnaire.initSDK(any()) }
    }

    /**
     * 测试onAgreeResult方法在未同意时的行为
     * 1. 调用onAgreeResult(false)
     * 2. 验证未初始化问卷SDK
     */
    @Test
    fun `onAgreeResult should not initialize when not agreed`() {
        application.onAgreeResult(false, true)

        verify(exactly = 0) { mockQuestionnaire.initSDK(any()) }
    }

    /**
     * 测试initAppSwitchAndDmp方法在API不可用时的行为
     * 1. 模拟API不可用
     * 2. 调用initAppSwitchAndDmp方法
     * 3. 验证未调用相关初始化方法
     */
    @Test
    fun `initAppSwitchAndDmp should skip when apis unavailable`() {
        every { Injector.injectFactory<IAppSwitchApi>() } returns null
        every { Injector.injectFactory<IDmpSearchApi>() } returns null

        val method = MainApplication::class.java.getDeclaredMethod("initAppSwitchAndDmp")
            .apply { isAccessible = true }
        method.invoke(application)

        verify(exactly = 0) { mockAppSwitchApi.initDmpAndAppSwitch(any()) }
        verify(exactly = 0) { mockDmpApi.checkAndUpdateSwitchSpToDmp() }
    }

    /**
     * 测试addNewActivityToList方法添加唯一Activity的行为
     * 1. 创建两个不同的Activity mock
     * 2. 添加Activity到列表
     * 3. 验证列表大小和唯一性
     */
    @Test
    fun `addNewActivityToList should add unique activities`() {
        val activity1 = mockk<Activity>()
        val activity2 = mockk<Activity>()
        every { activity1.componentName.className } returns "com.example.Activity1"
        every { activity2.componentName.className } returns "com.example.Activity2"

        val method = MainApplication::class.java.getDeclaredMethod(
            "addNewActivityToList", 
            Activity::class.java, 
            MutableList::class.java
        ).apply { isAccessible = true }
        
        method.invoke(application, activity1, MyApplication.activities)
        method.invoke(application, activity2, MyApplication.activities)
        method.invoke(application, activity1, MyApplication.activities) // Duplicate

        assertEquals(2, MyApplication.activities.size)
    }
}