/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager
 * * Version     : 1.0
 * * Date        : 2020/7/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager

import com.filemanager.common.utils.Utils


object TestUtils {
    fun resetChangeModeClickTime() {
        val field = Utils::class.java.getDeclaredField("sChangeModelClickTime")
        field.setAccessible(true)
        field.set(null, -1000)
    }
}