/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemoteFileDownloadObserver
 * * Description : 远程设备下载文件 Observer
 * * Version     : 1.0
 * * Date        : 2024/12/09
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.oplus.filemanager.category.remotedevice.operate

import android.Manifest
import android.app.Activity
import android.content.Context
import androidx.activity.ComponentActivity
import androidx.appcompat.app.AlertDialog
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.SdkUtils
import com.filemanager.fileoperate.base.BaseFileActionObserver
import java.util.function.Consumer

open class RemoteFileDownloadObserver(val activity: ComponentActivity) : BaseFileActionObserver(activity) {
    companion object {
        private const val TAG = "RemoteFileDownloadObserver"
        const val NOTICE_DOWNLOAD_FILE_OVER_COUNT = 1 // 文件个数超过99
        const val NOTICE_DOWNLOAD_FILE_OVER_SIZE = 2 // 文件size超过1G
        const val NOTICE_OPEN_FILE_OVER_SIZE = 8 // 文件size超过1G
        const val NOTICE_DOWNLOAD_SPACE_NOT_ENOUGH = 3
        const val SHOW_DOWNLOAD_MOBILE_TRAFFIC_DIALOG = 4
        const val DISMISS_DOWNLOAD_MOBILE_TRAFFIC_DIALOG = 5
        const val UPDATE_DOWNLOAD_TOTAL_PROGRESS = 6 // 更新下载总进度
        const val UPDATE_DOWNLOAD_SINGLE_PROGRESS = 7 // 更新下载单个文件进度
        const val NOTICE_FILE_CHANNEL_OCCUPIED = 9 // 文件通道被占用，不能下载
        const val NOTICE_DOWNLOAD_FILE_PATH_OVER_LENGTH = 10 // 文件路径超过1K
        const val SHOW_SYSTEM_NOTIFY_PERMISSION_DIALOG = 11 // 请求系统通知权限弹窗
        const val SHOW_CUSTOM_NOTIFY_PERMISSION_DIALOG = 12 // 请求自定义通知权限弹窗
    }

    private var trafficDialog: AlertDialog? = null
    private var spaceNotEnoughDialog: AlertDialog? = null
    private var notifyPermissionDialog: AlertDialog? = null

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        when (result.first) {
            NOTICE_DOWNLOAD_FILE_OVER_COUNT -> { // 文件个数超过99
                CustomToast.showShort(com.filemanager.common.R.string.not_support_download_too_many_file)
                onActionDone(false)
                return true
            }

            NOTICE_DOWNLOAD_FILE_OVER_SIZE -> { // 文件大小 超过1G
                CustomToast.showShort(com.filemanager.common.R.string.not_support_download_too_large_file)
                onActionDone(false)
                return true
            }

            NOTICE_OPEN_FILE_OVER_SIZE -> { // 文件大小 超过1G
                CustomToast.showShort(com.filemanager.common.R.string.not_support_open_too_large_file)
                onActionDone(false)
                return true
            }

            NOTICE_DOWNLOAD_FILE_PATH_OVER_LENGTH -> { // 文件路径超过1K
                CustomToast.showShort(com.filemanager.common.R.string.not_support_download_too_many_file)
                onActionDone(false)
                return true
            }

            NOTICE_DOWNLOAD_SPACE_NOT_ENOUGH -> { // 存储空间不足
                spaceNotEnoughDialog = DialogFactory.createSpaceNotEnoughDialog(context as Activity)
                onActionDone(false)
                return true
            }

            SHOW_DOWNLOAD_MOBILE_TRAFFIC_DIALOG -> { // 使用流量提醒
                if (result.second is MobileTrafficDialogBean) {
                    val bean = result.second as MobileTrafficDialogBean
                    trafficDialog = DialogFactory.createMobileTrafficDialog(context as Activity, bean.size, bean.consumer)
                }
                return true
            }

            SHOW_SYSTEM_NOTIFY_PERMISSION_DIALOG -> { // 系统通知权限弹窗
                if (result.second is NotifyPermissionDialogBean) {
                    val bean = result.second as NotifyPermissionDialogBean
                    if (SdkUtils.isAtLeastT()) {
                        val baseActivity = activity as? BaseVMActivity
                        baseActivity?.launchPermission(Manifest.permission.POST_NOTIFICATIONS, bean.callback)
                    } else {
                        bean.callback.accept(true)
                    }
                }
                return true
            }

            SHOW_CUSTOM_NOTIFY_PERMISSION_DIALOG -> { // 自定义通知权限弹窗
                if (result.second is NotifyPermissionDialogBean) {
                    val bean = result.second as NotifyPermissionDialogBean
                    notifyPermissionDialog = DialogFactory.createCustomNotifyPermissionDialog(activity, bean.callback)
                }
                return true
            }

            NOTICE_FILE_CHANNEL_OCCUPIED -> { // 文件通道被占用，不能下载
                CustomToast.showShort(com.filemanager.common.R.string.file_is_transferring_and_retry_after_completed)
                onActionDone(false)
                return true
            }

            DISMISS_DOWNLOAD_MOBILE_TRAFFIC_DIALOG -> {
                dismissTrafficDialog()
                return true
            }
        }

        return super.onChanged(context, result)
    }

    data class MobileTrafficDialogBean(val size: String) {
        var consumer: Consumer<Boolean>? = null

        fun recycle() {
            consumer = null
        }
    }

    data class NotifyPermissionDialogBean(val callback: Consumer<Boolean>)

    override fun recycle() {
        dismissTrafficDialog()
        super.recycle()
    }

    private fun dismissTrafficDialog() {
        if (trafficDialog?.isShowing == true) {
            trafficDialog?.dismiss()
        }
        trafficDialog = null
    }

    override fun isShowDialog(): Boolean {
        return super.isShowDialog() || trafficDialog?.isShowing ?: false
                || spaceNotEnoughDialog?.isShowing ?: false || notifyPermissionDialog?.isShowing ?: false
    }
}