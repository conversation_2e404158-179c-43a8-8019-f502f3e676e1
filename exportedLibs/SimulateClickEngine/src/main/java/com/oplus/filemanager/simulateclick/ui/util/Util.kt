/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Util
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/7/5
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/8/3      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.simulateclick.ui.util

import android.text.TextUtils
import android.view.View
import android.widget.TextView
import java.util.Locale

object Util {

    const val TAG = "Util"
    private const val RTL_CHARACTER_AFTER_D = "\u202d"
    private const val RTL_CHARACTER_AFTER_C = "\u202c"
    private const val ELLIPSIS = "\u2026"

    @JvmStatic
    fun setDisplayNameTextByLine(
        mDisplayName: String?,
        titltTv: TextView,
        extrachar: Int = 0
    ): String? {
        return if (!mDisplayName.isNullOrEmpty()) {
            val lineWidth = titltTv.width.toFloat() - titltTv.paddingStart - titltTv.paddingEnd
            if ((titltTv.paint.measureText(mDisplayName) > lineWidth * titltTv.maxLines)) {
                setSuperMaxLinesDisplayName(mDisplayName, titltTv, extrachar)
            } else {
                setLessMaxLinesDisplayName(titltTv, mDisplayName)
            }
        } else {
            mDisplayName
        }
    }

    @JvmStatic
    private fun setSuperMaxLinesDisplayName(
        mDisplayName: String,
        titltTv: TextView,
        extrachar: Int = 0
    ): String? {
        if (!mDisplayName.isNullOrEmpty()) {
            val lineWidth = titltTv.width.toFloat() - titltTv.paddingStart - titltTv.paddingEnd
            if (titltTv.paint.measureText(mDisplayName) > lineWidth * titltTv.maxLines) {
                return subDisplayName(mDisplayName, extrachar, titltTv, lineWidth)
            }
        }
        return mDisplayName
    }

    @JvmStatic
    private fun subDisplayName(
        mDisplayName: String,
        extrachar: Int,
        titltTv: TextView,
        lineWidth: Float
    ): String? {
        val lastIndexOf: Int = mDisplayName.lastIndexOf(".")
        val suffixText = ELLIPSIS + mDisplayName.substring(lastIndexOf - extrachar)
        var prefixIndex = 0
        var startIndex = 0
        var endLineText = ""
        var realText = ""
        var currentLine = 0
        while (prefixIndex <= mDisplayName.length) {
            currentLine = 1
            prefixIndex++
            val prefixText = mDisplayName.substring(startIndex, prefixIndex)
            if (titltTv.paint.measureText(prefixText) > lineWidth) {
                realText = isNeedDirectionController(
                    mDisplayName.substring(
                        startIndex,
                        prefixIndex - 1
                    )
                ) + "\n"
                startIndex = prefixIndex - 1
                currentLine++
                if (currentLine == titltTv.maxLines) {
                    while (true) {
                        prefixIndex++
                        endLineText = mDisplayName.substring(startIndex, prefixIndex)
                        if (titltTv.paint.measureText(endLineText + suffixText) > lineWidth) {
                            endLineText = mDisplayName.substring(startIndex, prefixIndex - 1) + suffixText
                            return realText + isNeedDirectionController(endLineText)
                        }
                    }
                }
            }
        }
        return mDisplayName
    }

    @JvmStatic
    fun setLessMaxLinesDisplayName(titltTv: TextView, displayName: String?): String? {
        if (!displayName.isNullOrEmpty()) {
            val lineWidth = titltTv.width.toFloat() - titltTv.paddingStart - titltTv.paddingEnd
            var startIndex = 0
            var realText = ""
            var index = 1
            var currentRow = 0
            if (titltTv.paint.measureText(displayName) >= lineWidth && titltTv.maxLines > 1) {
                currentRow = 1
                while (index <= displayName.length) {
                    val displayNameWidth = titltTv.paint.measureText(displayName.substring(startIndex, index))
                    if (displayNameWidth >= lineWidth) {
                        realText += isNeedDirectionController(displayName.substring(startIndex, index - 1)) + "\n"
                        currentRow++
                        startIndex = index - 1
                        if (currentRow == titltTv.maxLines) {
                            return realText + isNeedDirectionController(displayName.substring(startIndex, displayName.length))
                        }
                        index--
                    }
                    index++
                }
            } else {
                return displayName
            }
        }
        return displayName
    }

    @JvmStatic
    fun isNeedDirectionController(str: String?): String? {
        val rtl = isRtl()
        return if (rtl) {
            RTL_CHARACTER_AFTER_D + str + RTL_CHARACTER_AFTER_C
        } else {
            str
        }
    }

    @JvmStatic
    fun isRtl(): Boolean {
        return TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL
    }
}