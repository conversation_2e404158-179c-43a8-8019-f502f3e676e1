/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GetSuperAppItemUseCase
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/15 11:04
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/15       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.usecase

import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.cardwidget.recent.data.RecentPreviewItem
import com.oplus.filemanager.interfaze.superapp.ISuperApp
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean

class GetSuperAppItemUseCase {
    operator fun invoke(): List<RecentPreviewItem> {
        val items = Injector.injectFactory<ISuperApp>()?.getCategoryItems(MyApplication.appContext) ?: arrayListOf()
        Log.d(TAG, "invoke -> items = $items")
        return convertSuperAppBeanToRecentItem(items)
    }

    private fun convertSuperAppBeanToRecentItem(categoryItemsBeans: List<MainCategoryItemsBean>): List<RecentPreviewItem> {
        val items = arrayListOf<RecentPreviewItem>()
        categoryItemsBeans.forEach { bean ->
            Log.d(TAG, "convertSuperAppBeanToRecentItem -> bean = ${bean.name}")
            val item = RecentPreviewItem(bean.itemType, bean.name)
            items.add(item)
        }
        return items
    }

    companion object {
        private const val TAG = "GetSuperAppItemUseCase"
    }
}