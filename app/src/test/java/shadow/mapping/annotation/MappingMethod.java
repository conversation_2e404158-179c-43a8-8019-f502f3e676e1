/************************************************************
 * Copyright 2010-2019 Oplus. All rights reserved.
 * FileName       : MappingMethod.java
 * Version Number : 1.0
 * Description    : Annotation method that needs to be mapped.
 * Author         : zhenggang
 * Date           : 2019-07-15
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019-07-15, zhenggang, create
 ************************************************************/

package shadow.mapping.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface MappingMethod {

    String body() default "";

    boolean add() default false;
}
