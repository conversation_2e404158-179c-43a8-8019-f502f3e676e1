/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: DefaultDropListener.kt
 ** Description: drop listener
 ** Version: 1.0
 ** Date: 2023/6/5
 ** Author: hank.zhou(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.dragselection

import android.annotation.SuppressLint
import android.app.Activity
import android.net.Uri
import android.view.DragEvent
import android.view.View
import android.widget.Toast
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.R
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.main.IMain
import kotlin.math.truncate

class DefaultDropListener(ac: Activity) : View.OnDragListener {

    private val activity = ac
    companion object {
        const val TAG = "DefaultDropListener"
        //内銷首頁
        const val MAIN_ACTIVITY = "com.oplus.filemanager.main.ui.MainActivity"
        //外銷oppo和realme的首頁
        const val MAIN_EXPORT_ACTIVITY = "com.oplus.filemanager.main.ui.ExportMainActivity"
        const val RECYCLEBIN_ACTIVITY = "com.filemanager.recyclebin.ui.RecycleBinActivity"
        const val SETTING_ACTIVITY = "com.filemanager.setting.ui.SettingActivity"
        const val SETTING_ABOUT_ACTIVITY = "com.filemanager.setting.ui.about.SettingAboutActivity"
        const val SETTING_FUNCTION_ACTIVITY = "com.filemanager.setting.ui.function.SettingFunctionActivity"
        const val SETTING_OPENSOURCE_ACTIVITY = "com.filemanager.setting.ui.opensourcelicense.OpenSourceActivity"
        const val SETTING_PRIVACY_ACTIVITY = "com.filemanager.setting.ui.privacy.SettingPrivacyActivity"
        const val SETTING_USER_INFORMATION_ACTIVITY = "com.filemanager.setting.ui.privacy.UserInformationListActivity"
        const val PHONE_STORAGE_ACTIVITY = "com.oplus.filebrowser.FileBrowserActivity"
        const val OTG_STORAGE_ACTIVITY = "com.oplus.filebrowser.otg.OtgFileBrowserActivity"
        const val SHARE_ACTIVITY = "com.oplus.filemanager.filechoose.ui.share.ShareActivity"
        const val FILE_PICK_ACTIVITY = "com.oplus.filemanager.filechoose.ui.filepicker.FilePickerActivity"
        const val FOLDER_PICK_ACTIVITY = "com.oplus.filemanager.filechoose.ui.folderpicker.FolderPickerActivity"
        const val SINGLE_PICK_ACTIVITY = "com.oplus.filemanager.filechoose.ui.singlepicker.SinglePickerActivity"
        const val AKEY_MOVE_ACTIVITY = "com.oplus.filemanager.keymove.ui.AKeyToMoveActivity"
        const val INDEX_CATEGORY = 0
        const val INDEX_RECENT = 1
        var selectListUris: ArrayList<Uri>? = null
        var selectListTexts: ArrayList<String>? = null
        var selectListHtmls: ArrayList<String>? = null
        var selectedItemViews: MutableLiveData<ArrayList<View>>? = null
        var viewMode: com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE? = null

        fun getSelectListUri(): ArrayList<Uri>? {
            return selectListUris
        }

        fun setSelectListUri(list: ArrayList<Uri>) {
            selectListUris = list
        }

        fun getSelectListText(): ArrayList<String>? {
            return selectListTexts
        }

        fun setSelectListText(list: ArrayList<String>) {
            selectListTexts = list
        }

        fun getSelectListHtml(): ArrayList<String>? {
            return selectListHtmls
        }

        fun setSelectListHtml(list: ArrayList<String>) {
            selectListHtmls = list
        }

        fun setSelectedItemViewList(list: ArrayList<View>) {
            if (selectedItemViews == null) {
                selectedItemViews = MutableLiveData()
            }
            selectedItemViews?.value = list
        }

        @SuppressLint("NotifyDataSetChanged")
        fun notifyRecyclerItemAlpha(isBeginDrag: Boolean) {
            Log.d(TAG, "notifyRecyclerDataChange isBeginDrag:$isBeginDrag start...")
            val itemViews = selectedItemViews?.value
            if (itemViews == null || itemViews.size == 0) {
                Log.w(TAG, "notifyRecyclerDataChange itemView is null")
                return
            }
            val recycleView = findRecycleView(itemViews.get(0))
            val layoutManager = recycleView?.layoutManager as? LinearLayoutManager
            layoutManager?.apply {
                Log.d(TAG, "layoutManager.childCount=${layoutManager.childCount}")
                for (index in 0..childCount) {
                    val view = getChildAt(index)
                    if (view != null && !isBeginDrag) {
                        view.alpha = 1f
                    }
                }
            }
            if (isBeginDrag) {
                recycleView?.setItemViewCacheSize(0)
            } else {
                recycleView?.setItemViewCacheSize(RecyclerView.NO_POSITION)
            }
            Log.d(TAG, "notifyRecyclerDataChange end")
        }

        private fun findRecycleView(itemView: View): RecyclerView? {
            val parent = itemView.parent ?: return null
            return if (parent is RecyclerView) {
                parent
            } else {
                findRecycleView(parent as View)
            }
        }
    }
    private val fragmentPageDropHelper = FragmentPageDropHelper(activity)

    override fun onDrag(view: View?, event: DragEvent?): Boolean {
        if (event?.action == DragEvent.ACTION_DRAG_ENDED && MacDragUtil.MacDragObject.isDraggingFromMac) {
            MacDragUtil.MacDragObject.isDraggingFromMac = false
        }
        return if (activity is DragDropInterface) {
            handleDragEvent(view, event)
            return true
        } else {
            when (event?.action) {
                DragEvent.ACTION_DROP -> handleDropAction(event)
                DragEvent.ACTION_DRAG_STARTED -> true
                DragEvent.ACTION_DRAG_ENTERED -> true
                DragEvent.ACTION_DRAG_LOCATION -> true
                DragEvent.ACTION_DRAG_EXITED -> true
                DragEvent.ACTION_DRAG_ENDED -> true
                else -> false
            }
        }
    }

    private fun handleDragEvent(view: View?, event: DragEvent?): Boolean {
        val isMainActivity = Injector.injectFactory<IMain>()?.isMainActivity(activity) ?: false
        if (isMainActivity) {
            return Injector.injectFactory<IMain>()?.handleDragEvent(activity, view, event) ?: true
        } else {
            var isScrolling = false
            if (activity is IDraggingActionOperate) {
                isScrolling = activity.handleDragEvent(event) == true
            }
            return fragmentPageDropHelper.handleDragEvent(activity, view, event, isScrolling)
        }
    }

    @VisibleForTesting
    fun handleDropAction(p1: DragEvent?): Boolean {
        Log.d(TAG, "handleDropAction p1 = $p1")
        val dragTag = p1?.localState as? String ?: DragUtils.dragTag
        p1?.let {
            if (isNoResponseDrop()) {
                Log.d(TAG, "handleDropAction no need response")
                return false
            }
            if (!isSupportDrop()) {
                Log.d(TAG, "handleDropAction not support")
                Toast.makeText(activity, R.string.drag_not_support_position, Toast.LENGTH_SHORT).show()
                return false
            }
            try {
                //内部拖拽接收不需要获取uri授权
                if (dragTag == null) {
                    activity.requestDragAndDropPermissions(p1)
                }
            } catch (e: SecurityException) {
                Log.d(TAG, "SecurityException ${e.message}")
                return false
            }
            val listUris = ArrayList<Uri>()
            val listTexts = ArrayList<String>()
            val listHtmls = ArrayList<String>()
            saveData(it, listUris, listTexts, listHtmls)
            Log.d(TAG, "handleDropAction listUris = ${listUris.size} listTexts = ${listTexts.size} listHtmls = ${listHtmls.size}")

            if (activity is TransformNextFragmentListener) {
                setSelectListUri(listUris)
                setSelectListText(listTexts)
                setSelectListHtml(listHtmls)
                activity.showSelectPathFragmentDialog(MessageConstant.MSG_SAVE)
            }
        }
        return true
    }

    private fun saveData(
        p: DragEvent,
        listUris: ArrayList<Uri>,
        listTexts: ArrayList<String>,
        listHtmls: ArrayList<String>
    ) {
        val count = p.clipData.itemCount
        for (i in 0 until count) {
            val uri = p.clipData.getItemAt(i).uri
            val text = p.clipData.getItemAt(i).text
            val html = p.clipData.getItemAt(i).htmlText
            if (uri != null) {
                listUris.add(uri)
            }
            if (text != null && text.isNotEmpty()) {
                listTexts.add(text.toString())
            }
            if (html != null && html.isNotEmpty()) {
                listHtmls.add(html)
            }
        }
    }
    @VisibleForTesting
    fun isSupportDrop(): Boolean {
        val activityName = activity.componentName.className
        when (activityName) {
            RECYCLEBIN_ACTIVITY -> return false
            SETTING_ACTIVITY -> return false
            SETTING_ABOUT_ACTIVITY -> return false
            SETTING_FUNCTION_ACTIVITY -> return false
            SETTING_OPENSOURCE_ACTIVITY -> return false
            SETTING_PRIVACY_ACTIVITY -> return false
            SETTING_USER_INFORMATION_ACTIVITY -> return false
            SHARE_ACTIVITY -> return false
        }
        return true
    }

    @VisibleForTesting
    fun isNoResponseDrop(): Boolean {
        val activityName = activity.componentName.className
        when (activityName) {
            FILE_PICK_ACTIVITY -> return true
            FOLDER_PICK_ACTIVITY -> return true
            SINGLE_PICK_ACTIVITY -> return true
            AKEY_MOVE_ACTIVITY -> return true
            MAIN_ACTIVITY -> return true
            MAIN_EXPORT_ACTIVITY -> return true
        }
        return false
    }
}