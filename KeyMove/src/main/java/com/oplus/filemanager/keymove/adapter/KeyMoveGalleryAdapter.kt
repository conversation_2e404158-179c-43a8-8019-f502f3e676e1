/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved.
 * * File: KeyMoveGalleryAdapter
 * * Description: the adapter for KeyMoveGallery
 * * Version: 1.0
 * * Date : 2020/7/24
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/7/24       1.0         the adapter for KeyMoveGallery
 ****************************************************************/
package com.oplus.filemanager.keymove.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.Log
import com.oplus.filemanager.keymove.R
import com.oplus.filemanager.keymove.ui.model.ItemChild

class KeyMoveGalleryAdapter(val mContext: Context) : RecyclerView.Adapter<KeyMoveGalleryAdapter.GalleryViewHolder>() {

    companion object {
        private const val TAG = "KeyMoveGalleryAdapter"
    }

    private var mFiles: ArrayList<ItemChild>? = null
    private var mWidth = 0
    private var mOnRecyclerItemClickListener: OnRecyclerItemClickListener? = null

    fun setData(list: ArrayList<ItemChild>) {
        mFiles = list
        notifyDataSetChanged()
    }

    fun setItemWidth(width: Int) {
        mWidth = width
    }

    fun setOnRecyclerItemClickListener(listener: OnRecyclerItemClickListener) {
        mOnRecyclerItemClickListener = listener
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GalleryViewHolder {
        val v = LayoutInflater.from(parent.context).inflate(R.layout.key_move_gallery_item, parent, false)
        return GalleryViewHolder(v)
    }

    override fun onBindViewHolder(holder: GalleryViewHolder, position: Int) {
        holder.bindTo(position)
    }

    inner class GalleryViewHolder(private val view: View) : RecyclerView.ViewHolder(view) {
        private val mCheckBox: COUICheckBox = view.findViewById(R.id.gallery_recycler_checkbox_item)
        private val mImg: ImageView = view.findViewById(R.id.gallery_recycler_img_item)
        private var mCheckBoxBg: ImageView? = view.findViewById(R.id.grid_checkbox_bg)

        fun bindTo(position: Int) {
            Log.d(TAG, "bindTo: mData.size = ${mFiles?.size}, position = $position")
            val model = mFiles?.get(position) ?: return
            view.tag = position
            itemView.layoutParams?.apply {
                width = mWidth
                height = mWidth
            }
            view.setOnClickListener {
                mOnRecyclerItemClickListener?.onItemClick(view, position)
            }
            mCheckBox.state = model.getChosenState()
            val file = model.mFile
            FileImageLoader.sInstance.clear(mContext, mImg)
            FileImageLoader.sInstance.displayDefault(file, mImg, 0)
            mImg.visibility = View.VISIBLE
            mCheckBox.visibility = View.VISIBLE
            if (mCheckBox.state == COUICheckBox.SELECT_ALL) {
                mCheckBoxBg?.visibility = View.INVISIBLE
            } else {
                mCheckBoxBg?.visibility = View.VISIBLE
            }
        }
    }

    override fun getItemCount(): Int {
        return mFiles?.size ?: 0
    }

    override fun getItemId(position: Int): Long {
        return getItemKey(position)?.toLong() ?: com.oplus.dropdrag.SelectionTracker.NO_LONG_ITEM_ID
    }

    private fun getItemKey(position: Int): Int? {
        val path = mFiles?.get(position)?.mFile?.mData
        if (path.isNullOrEmpty()) {
            return null
        }
        return path.toLowerCase().hashCode()
    }


}