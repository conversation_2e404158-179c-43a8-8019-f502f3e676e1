package com.oplus.filemanager.preview.remote

import android.util.Log
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.test.platform.app.InstrumentationRegistry
import com.oplus.filemanager.preview.R
import com.coui.appcompat.button.COUIButton
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite
import com.oplus.filemanager.preview.widget.PreviewScrollView
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * RemotePreviewContainerManager的单元测试类
 * 用于测试远程预览容器管理器的各项功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RemotePreviewContainerManagerTest {

    // 测试使用的根布局和待测管理器
    private lateinit var rootLayout: ConstraintLayout
    private lateinit var manager: RemotePreviewContainerManager

    /**
     * 测试前置设置
     * 1. 模拟Log类
     * 2. 创建模拟的根布局
     * 3. 初始化待测管理器
     */
    @Before
    fun setUp() {
        // 模拟Log类的静态方法
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } returns 0

        // 创建模拟的根布局
        rootLayout = mockk(relaxed = true)
        // 模拟布局中的各个视图组件
        mockViewComponents()
        // 初始化待测管理器
        manager = RemotePreviewContainerManager(rootLayout)
    }

    /**
     * 测试后置清理
     * 清除所有模拟对象
     */
    @After
    fun tearDown() {
        unmockkAll()
        clearAllMocks()
    }

    /**
     * 模拟布局中的各个视图组件
     * 为rootLayout的findViewById方法设置返回值
     */
    private fun mockViewComponents() {
        every { rootLayout.findViewById<ConstraintLayout>(R.id.preview_remote_info) } returns mockk(relaxed = true)
        every { rootLayout.findViewById<AppCompatImageView>(R.id.preview_remote_view) } returns mockk(relaxed = true)
        every { rootLayout.findViewById<COUIButton>(R.id.preview_download_button) } returns mockk(relaxed = true)
        every { rootLayout.findViewById<PreviewFileInfoSuite>(R.id.preview_remote_def_info) } returns mockk(relaxed = true)
        every { rootLayout.findViewById<ConstraintLayout>(R.id.preview_remote_container) } returns mockk(relaxed = true)
        every { rootLayout.findViewById<View>(R.id.loading_layout) } returns mockk(relaxed = true)
        every { rootLayout.findViewById<AppCompatTextView>(R.id.preview_remote_time_info) } returns mockk(relaxed = true)
        every { rootLayout.findViewById<AppCompatTextView>(R.id.preview_remote_size_info) } returns mockk(relaxed = true)
        every { rootLayout.findViewById<AppCompatTextView>(R.id.preview_remote_location_info) } returns mockk(relaxed = true)
        every { rootLayout.findViewById<TextViewSnippet>(R.id.preview_remote_title) } returns mockk(relaxed = true)
        every { rootLayout.findViewById<View>(R.id.divider_download) } returns mockk(relaxed = true)
        every { rootLayout.findViewById<PreviewScrollView>(R.id.preview_audio_scroll_area) } returns mockk(relaxed = true)
        every { rootLayout.findViewById<ConstraintLayout>(R.id.rootView) } returns mockk(relaxed = true)
    }

    /**
     * 测试showAsLoading方法
     * 验证加载状态下的UI显示是否正确
     */
    @Test
    fun testShowAsLoading() {
        // 调用待测方法
        manager.showAsLoading()

        // 验证各视图的可见性设置是否正确
        verify { 
            manager.remoteContainer.visibility = View.INVISIBLE
            manager.defaultContainer.visibility = View.GONE
            manager.infoContainer.visibility = View.VISIBLE
            manager.download.visibility = View.VISIBLE
        }
        // 验证日志输出是否正确
        verify { Log.d("FileManager::RemotePreviewContainerManager", "showAsLoading") }
    }

    /**
     * 测试showAsFileContainer方法(默认容器)
     * 验证显示默认文件容器时的UI状态
     */
    @Test
    fun testShowAsFileContainer_Default() {
        // 调用待测方法，参数为true表示使用默认容器
        manager.showAsFileContainer(true)

        // 验证各视图的可见性设置是否正确
        verify { 
            manager.download.visibility = View.VISIBLE
            manager.infoContainer.visibility = View.VISIBLE
            manager.remoteContainer.visibility = View.INVISIBLE
            manager.defaultContainer.visibility = View.VISIBLE
        }
        // 验证日志输出是否正确
        verify { Log.d("FileManager::RemotePreviewContainerManager", "impl showAsFileContainer: true") }
    }

    /**
     * 测试showAsFileContainer方法(非默认容器)
     * 验证显示非默认文件容器时的UI状态
     */
    @Test
    fun testShowAsFileContainer_NotDefault() {
        // 调用待测方法，参数为false表示不使用默认容器
        manager.showAsFileContainer(false)

        // 验证各视图的可见性设置是否正确
        verify { 
            manager.download.visibility = View.VISIBLE
            manager.infoContainer.visibility = View.VISIBLE
            manager.remoteContainer.visibility = View.VISIBLE
            manager.defaultContainer.visibility = View.GONE
        }
        // 验证日志输出是否正确
        verify { Log.d("FileManager::RemotePreviewContainerManager", "impl showAsFileContainer: false") }
    }

    /**
     * 测试视图组件初始化
     * 验证所有视图组件是否正确初始化
     */
    @Test
    fun testViewComponentsInitialized() {
        // 验证所有视图组件是否与布局中的对应视图匹配
        assertEquals(manager.infoContainer, rootLayout.findViewById(R.id.preview_remote_info))
        assertEquals(manager.imageView, rootLayout.findViewById(R.id.preview_remote_view))
        assertEquals(manager.download, rootLayout.findViewById(R.id.preview_download_button))
        assertEquals(manager.defaultContainer, rootLayout.findViewById(R.id.preview_remote_def_info))
        assertEquals(manager.remoteContainer, rootLayout.findViewById(R.id.preview_remote_container))
        assertEquals(manager.timeInfo, rootLayout.findViewById(R.id.preview_remote_time_info))
        assertEquals(manager.sizeInfo, rootLayout.findViewById(R.id.preview_remote_size_info))
        assertEquals(manager.locationInfo, rootLayout.findViewById(R.id.preview_remote_location_info))
        assertEquals(manager.remoteTitle, rootLayout.findViewById(R.id.preview_remote_title))
        assertEquals(manager.downloadDivider, rootLayout.findViewById(R.id.divider_download))
        assertEquals(manager.scroll, rootLayout.findViewById(R.id.preview_audio_scroll_area))
        assertEquals(manager.rootView, rootLayout.findViewById(R.id.rootView))
    }
}