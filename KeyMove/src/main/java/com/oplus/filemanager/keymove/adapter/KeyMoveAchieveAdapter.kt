/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved.
 * * File: KeyMoveAchieveAdapter
 * * Description: the adapter for key move when files are moving
 * * Version: 1.0
 * * Date : 2020/7/25
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/7/25       1.0         the adapter for key move when files are moving
 ****************************************************************/
package com.oplus.filemanager.keymove.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.expandable.COUIExpandableRecyclerView
import com.coui.appcompat.progressbar.COUILoadingView
import com.filemanager.common.MyApplication
import com.oplus.filemanager.keymove.R
import com.oplus.filemanager.keymove.ui.keymovechoice.KeyMoveChoiceFragment
import java.util.*

class KeyMoveAchieveAdapter : COUIExpandableRecyclerView.Adapter() {

    private var mGroupItems: ArrayList<KeyMoveChoiceFragment.ItemMovingGroup>? = null
    private var mIsAchieve: Boolean = false

    fun setData(list: ArrayList<KeyMoveChoiceFragment.ItemMovingGroup>) {
        mGroupItems = list
        notifyDataSetChanged()
    }

    fun setAchieve() {
        mIsAchieve = true
        notifyDataSetChanged()
    }

    override fun getGroup(groupPosition: Int): KeyMoveChoiceFragment.ItemMovingGroup? {
        return mGroupItems?.get(groupPosition)
    }

    override fun onBindGroupView(p0: Int, p1: Boolean, holder: RecyclerView.ViewHolder?) {
        (holder as KeyMovingViewHolder).bindTo(p0)
    }

    override fun onCreateGroupView(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val v = LayoutInflater.from(parent.context).inflate(R.layout.key_move_moving_group_item, parent, false)
        return KeyMovingViewHolder(v)
    }

    override fun onCreateChildView(p0: ViewGroup?, p1: Int): RecyclerView.ViewHolder? {
        return null
    }

    override fun getChildrenCount(p0: Int): Int {
        return 0
    }

    override fun onBindChildView(p0: Int, p1: Int, p2: Boolean, p3: RecyclerView.ViewHolder?) {
        return
    }

    override fun getChild(p0: Int, p1: Int): Any? {
        return null
    }

    override fun getGroupCount(): Int {
        return mGroupItems?.size ?: 0
    }

    inner class KeyMovingViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        private val mContext: Context = MyApplication.sAppContext
        private val mIcon: ImageView = view.findViewById(R.id.group_file_icon)
        private val mName: TextView = view.findViewById(R.id.group_file_name)
        private val mDetail: TextView = view.findViewById(R.id.group_file_detail)
        private val mLoadingView: COUILoadingView = view.findViewById(R.id.loading_view)
        private val mResultImg: ImageView = view.findViewById(R.id.result_image)

        fun bindTo(position: Int) {
            val model = getGroup(position) ?: return
            setIcon(model)
            mName.text = model.mTitle
            mDetail.text = (String.format(Locale.getDefault(), "%d", model.achieveCount)
                    + " / " + String.format(Locale.getDefault(), "%d", model.totalCount))
            mName.visibility = View.VISIBLE
            mIcon.visibility = View.VISIBLE
            mDetail.visibility = View.VISIBLE
            if (model.achieveCount == model.totalCount) {
                mLoadingView.visibility = View.GONE
                mResultImg.setImageResource(com.filemanager.common.R.drawable.success_result)
                mResultImg.visibility = View.VISIBLE
            }
            else {
                mLoadingView.visibility = View.VISIBLE
                mResultImg.visibility = View.GONE
                if (mIsAchieve) {
                    mLoadingView.visibility = View.GONE
                    mResultImg.setImageResource(com.filemanager.common.R.drawable.fail_result)
                    mResultImg.visibility = View.VISIBLE
                }
            }
        }

        fun setIcon(model: KeyMoveChoiceFragment.ItemMovingGroup) {
            when (model.mTitle) {
                mContext.getString(com.filemanager.common.R.string.string_photos) -> {
                    mIcon.setImageResource(com.filemanager.common.R.drawable.ic_file_image)
                }
                mContext.getString(com.filemanager.common.R.string.string_videos) -> {
                    mIcon.setImageResource(com.filemanager.common.R.drawable.ic_file_video)
                }
                mContext.getString(com.filemanager.common.R.string.string_audio) -> {
                    mIcon.setImageResource(com.filemanager.common.R.drawable.ic_file_audio)
                }
                mContext.getString(com.filemanager.common.R.string.string_documents) -> {
                    mIcon.setImageResource(com.filemanager.common.R.drawable.ic_file_doc)
                }
                mContext.getString(com.filemanager.common.R.string.string_apk) -> {
                    mIcon.setImageResource(com.filemanager.common.R.drawable.ic_file_apk)
                }
            }
        }
    }
}