package com.oplus.filemanager.filechoose.ui.singlepicker

import android.content.Context
import android.os.Build
import android.os.Bundle
import android.view.KeyEvent
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.Toolbar
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.view.FileManagerRecyclerView
import com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller
import com.oplus.filemanager.filechoose.R
import com.oplus.filemanager.filechoose.adapter.SinglePickerAdapter
import com.oplus.selectdir.filebrowser.SelectFileBrowserViewModel
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.lang.reflect.Field

/**
 * SinglePickerFragment的单元测试类
 * 用于测试SinglePickerFragment的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [Build.VERSION_CODES.Q])
class SinglePickerFragmentTest {

    // 定义测试中使用的mock对象
    private lateinit var fragment: SinglePickerFragment
    private lateinit var mockContext: Context
    private lateinit var mockActivity: BaseVMActivity
    private lateinit var mockViewModel: SinglePickerFragmentViewModel
    private lateinit var mockAdapter: SinglePickerAdapter
    private lateinit var mockRecyclerView: FileManagerRecyclerView
    private lateinit var mockFastScroller: RecyclerViewFastScroller
    private lateinit var mockToolbar: COUIToolbar
    private lateinit var mockSelectButton: COUIButton
    private lateinit var mockSelectDetail: TextView
    private lateinit var mockSelectInfoRoot: ConstraintLayout
    private lateinit var mockRootView: ViewGroup
    private lateinit var mockLayoutManager: GridLayoutManager
    private lateinit var mockFileEmptyController: FileEmptyController
    private lateinit var mockLoadingController: LoadingController
    private lateinit var mockFolderTransformAnimator: FolderTransformAnimator

    /**
     * 测试前的准备工作
     * 初始化所有mock对象并设置到被测试的fragment中
     */
    @Before
    fun setUp() {
        // 创建被测试的fragment的spy对象
        fragment = spyk(SinglePickerFragment())

        // 初始化所有mock对象
        mockContext = mockk(relaxed = true)
        mockActivity = mockk(relaxed = true)
        mockViewModel = mockk(relaxed = true)
        mockAdapter = mockk(relaxed = true)
        mockRecyclerView = mockk(relaxed = true)
        mockFastScroller = mockk(relaxed = true)
        mockToolbar = mockk(relaxed = true)
        mockSelectButton = mockk(relaxed = true)
        mockSelectDetail = mockk(relaxed = true)
        mockSelectInfoRoot = mockk(relaxed = true)
        mockRootView = mockk(relaxed = true)
        mockLayoutManager = mockk(relaxed = true)
        mockFileEmptyController = mockk(relaxed = true)
        mockLoadingController = mockk(relaxed = true)
        mockFolderTransformAnimator = mockk(relaxed = true)

        // 使用反射设置fragment的私有字段
        setField(fragment, "mActivity", mockActivity)
        fragment::class.java.getDeclaredField("mRecyclerView").apply {
            isAccessible = true
            set(fragment, mockRecyclerView)
        }
        fragment::class.java.getDeclaredField("mRecyclerViewFastScroller").apply {
            isAccessible = true
            set(fragment, mockFastScroller)
        }
        fragment::class.java.getDeclaredField("mAdapter").apply {
            isAccessible = true
            set(fragment, mockAdapter)
        }
        fragment::class.java.getDeclaredField("mLayoutManager").apply {
            isAccessible = true
            set(fragment, mockLayoutManager)
        }
        fragment::class.java.getDeclaredField("mRootView").apply {
            isAccessible = true
            set(fragment, mockRootView)
        }
        fragment::class.java.getDeclaredField("selectDetail").apply {
            isAccessible = true
            set(fragment, mockSelectDetail)
        }
        fragment::class.java.getDeclaredField("selectButton").apply {
            isAccessible = true
            set(fragment, mockSelectButton)
        }
        fragment::class.java.getDeclaredField("selectInfoRoot").apply {
            isAccessible = true
            set(fragment, mockSelectInfoRoot)
        }
        fragment::class.java.getDeclaredField("mViewModel").apply {
            isAccessible = true
            set(fragment, mockViewModel)
        }
        
        // 对于lazy属性，需要设置delegate字段
        try {
            fragment::class.java.getDeclaredField("mFileEmptyController\$delegate").apply {
                isAccessible = true
                // 创建一个返回mockFileEmptyController的mock Lazy
                val mockLazy = mockk<Lazy<FileEmptyController>>()
                every { mockLazy.value } returns mockFileEmptyController
                set(fragment, mockLazy)
            }
        } catch (e: NoSuchFieldException) {
            // 如果字段名不同，则直接设置字段
            setField(fragment, "mFileEmptyController", mockFileEmptyController)
        }
        
        fragment::class.java.getDeclaredField("mLoadingController").apply {
            isAccessible = true
            set(fragment, mockLoadingController)
        }
        
        try {
            fragment::class.java.getDeclaredField("mFolderTransformAnimator\$delegate").apply {
                isAccessible = true
                // 创建一个返回mockFolderTransformAnimator的mock Lazy
                val mockLazy = mockk<Lazy<FolderTransformAnimator>>()
                every { mockLazy.value } returns mockFolderTransformAnimator
                set(fragment, mockLazy)
            }
        } catch (e: NoSuchFieldException) {
            // 如果字段名不同，则直接设置字段
            setField(fragment, "mFolderTransformAnimator", mockFolderTransformAnimator)
        }

        // Mock toolbar属性
        every { fragment.toolbar } returns mockToolbar
    }

    /**
     * 辅助方法：递归查找并设置类层次结构中的字段
     * @param obj 要设置字段的对象
     * @param fieldName 字段名
     * @param value 要设置的值
     */
    private fun setField(obj: Any, fieldName: String, value: Any?) {
        var clazz: Class<*> = obj::class.java
        while (clazz != Any::class.java) {
            try {
                val field: Field = clazz.getDeclaredField(fieldName)
                field.isAccessible = true
                field.set(obj, value)
                return
            } catch (e: NoSuchFieldException) {
                clazz = clazz.superclass
            } catch (e: Exception) {
                throw RuntimeException("Failed to set field $fieldName", e)
            }
        }
        throw NoSuchFieldException("Field '$fieldName' not found in class hierarchy")
    }

    /**
     * 测试后的清理工作
     * 清除所有mock对象
     */
    @After
    fun tearDown() {
        clearAllMocks()
    }

    /**
     * 测试initContentView方法是否正确初始化视图和设置监听器
     */
    @Test
    fun `test initContentView initializes views and sets listeners`() {
        val view = mockk<View>(relaxed = true)
        
        // Mock findViewById调用
        every { view.findViewById<View>(R.id.coordinator_layout) } returns mockRootView
        every { view.findViewById<View>(R.id.select_info_root) } returns mockSelectInfoRoot
        every { view.findViewById<View>(R.id.select_detail) } returns mockSelectDetail
        every { view.findViewById<View>(R.id.select_button) } returns mockSelectButton
        every { view.findViewById<View>(R.id.fastScroller) } returns mockFastScroller
        every { view.findViewById<View>(R.id.recycler_view) } returns mockRecyclerView
        
        // Mock toolbar方法
        every { mockToolbar.visibility = any() } just runs
        every { mockToolbar.isTitleCenterStyle = any() } just runs
        every { mockToolbar.inflateMenu(any()) } just runs
        every { mockToolbar.setOnMenuItemClickListener(any()) } just runs

        fragment.initContentView(view)

        // 验证视图是否正确初始化
        verify { mockSelectButton.setOnClickListener(any()) }
        verify { mockAdapter.setOnRecyclerItemClickListener(fragment) }
        verify { mockAdapter.supportMultipleSelection(false) }
        verify { mockToolbar.visibility = View.VISIBLE }
        verify { mockToolbar.isTitleCenterStyle = true }
        verify { mockToolbar.inflateMenu(R.menu.single_dialog_option) }
        verify { mockToolbar.setOnMenuItemClickListener(fragment) }
    }

    /**
     * 测试onResumeLoadData方法在路径存在时调用initLoader
     */
    @Test
    fun `test onResumeLoadData calls initLoader when path exists`() {
        // 使用反射设置私有字段
        fragment::class.java.getDeclaredField("mCurrentPath").apply {
            isAccessible = true
            set(fragment, "/test/path")
        }
        every { fragment.isAdded } returns true
        every { fragment.activity } returns mockActivity
        
        val loaderController = mockk<LoaderController>()
        mockkObject(LoaderViewModel.Companion)
        every { LoaderViewModel.getLoaderController(any()) } returns loaderController

        fragment.onResumeLoadData()

        verify { mockViewModel.initLoader(loaderController, "/test/path", false) }
    }

    /**
     * 测试onResumeLoadData方法在未添加时不调用initLoader
     */
    @Test
    fun `test onResumeLoadData does not call initLoader when not added`() {
        // 使用反射设置私有字段
        fragment::class.java.getDeclaredField("mCurrentPath").apply {
            isAccessible = true
            set(fragment, "/test/path")
        }
        every { fragment.isAdded } returns false

        fragment.onResumeLoadData()

        verify(exactly = 0) { mockViewModel.initLoader(any(), any(), any()) }
    }

    /**
     * 测试pressBack方法在viewModel pressBack返回true时返回true
     */
    @Test
    fun `test pressBack returns true when viewModel pressBack is true`() {
        every { mockViewModel.pressBack() } returns true
        val mockPickerActivity = mockk<SinglePickerActivity>(relaxed = true)
        every { fragment.activity } returns mockPickerActivity

        val result = fragment.pressBack()

        assert(result)
        verify(exactly = 0) { mockPickerActivity.finishPickerDialog() }
    }

    /**
     * 测试pressBack方法在viewModel pressBack返回false时结束activity
     */
    @Test
    fun `test pressBack finishes activity when viewModel pressBack is false`() {
        every { mockViewModel.pressBack() } returns false
        val mockPickerActivity = mockk<SinglePickerActivity>(relaxed = true)
        every { fragment.activity } returns mockPickerActivity

        val result = fragment.pressBack()

        assert(!result)
        verify { mockPickerActivity.finishPickerDialog() }
    }

    /**
     * 测试onItemClick方法调用viewModel的onItemClick
     */
    @Test
    fun `test onItemClick calls viewModel onItemClick`() {
        val view = mockk<View>()
        val position = 5
        every { mockLayoutManager.findFirstVisibleItemPosition() } returns 2
        every { mockLayoutManager.findViewByPosition(2) } returns mockk<View>(relaxed = true) {
            every { top } returns 100
        }
        every { mockRecyclerView.paddingTop } returns 50
        every { fragment.activity } returns mockActivity

        fragment.onItemClick(view, position)

        verify { mockViewModel.onItemClick(mockActivity, position, 2, 50, false) }
    }

    /**
     * 测试onMenuItemClick方法处理取消动作
     */
    @Test
    fun `test onMenuItemClick handles cancel action`() {
        val menuItem = mockk<MenuItem>()
        every { menuItem.itemId } returns R.id.action_cancel
        val mockPickerActivity = mockk<SinglePickerActivity>(relaxed = true)
        every { fragment.activity } returns mockPickerActivity

        val result = fragment.onMenuItemClick(menuItem)

        assert(result)
        verify { mockPickerActivity.finishPickerDialog() }
    }

    /**
     * 测试onDestroyView方法取消注册动画器
     */
    @Test
    fun `test onDestroyView unregisters animator`() {
        fragment.onDestroyView()

        verify { mockFolderTransformAnimator.unRegisterNeddSkipAnimator() }
    }
}