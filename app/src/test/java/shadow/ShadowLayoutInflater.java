package shadow;

import android.content.Context;
import android.view.LayoutInflater;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(LayoutInflater.class)
public class ShadowLayoutInflater {
    private static LayoutInflater mLayoutInflater;
    @Implementation
    public static LayoutInflater from(Context context) {
        return mLayoutInflater;
    }

    public static void setmLayoutInflater(LayoutInflater layoutInflater) {
        mLayoutInflater = layoutInflater;
    }
}
