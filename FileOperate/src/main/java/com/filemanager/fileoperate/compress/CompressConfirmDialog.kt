/*********************************************************************
 * * Copyright (C), 2024, OPlus. All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/2/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.fileoperate.compress

import android.content.Context
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View.OnClickListener
import android.view.WindowManager
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import com.coui.appcompat.edittext.COUIInputView
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.CommonConstants.CUSTOM_NAME_LEN
import com.filemanager.common.constants.CommonConstants.NAME_BYTES_LEN
import com.filemanager.common.fileutils.fetchFileName
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.EmojiUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.RenameErrorTipUtil
import com.filemanager.common.utils.Utils
import com.filemanager.fileoperate.R
import com.filemanager.fileoperate.base.BaseFileNameDialog.Companion.FILE_NAME_FINAL_REGEX
import java.io.File
import java.nio.charset.StandardCharsets
import java.util.regex.Pattern

class CompressConfirmDialog : COUIBottomSheetDialog, TextWatcher {
    companion object {
        private const val TAG = "CompressConfirmDialog"
        const val DEFAULT_SAVE_PATH = "/MyFiles"
    }

    private var mInputView: COUIInputView? = null
    private var mOppoEditText: EditText? = null
    private var mClickButton: Button? = null
    private var mPathTextView: TextView? = null
    private val mFileNamePattern = Pattern.compile(FILE_NAME_FINAL_REGEX)
    private var mDialogType: CompressConfirmType? = null
    private var savePath: String = ""
    private val mIllegalFilter = InputFilter { source, _, _, dest, dstart, dend ->
        if (EmojiUtils.containsIllegalCharFileName(source)) {
            showNotice(RenameErrorTipUtil.ERROR_FILENAME_INPUT_ILLEGAL)
            if (dest != null) {
                return@InputFilter dest.subSequence(dstart, dend)
            }
        } else if (EmojiUtils.isContainEmoji(source)) {
            showNotice(RenameErrorTipUtil.ERROR_FILENAME_ILLEGAL)
            if (dest != null) {
                return@InputFilter dest.subSequence(dstart, dend)
            }
        }
        source
    }

    constructor(context: Context, defStyle: Int, type: CompressConfirmType = CompressConfirmType.COMPRESS) : super(context, defStyle) {
        mDialogType = type
        init(context)
    }

    fun init(context: Context) {
        contentView = LayoutInflater.from(context).inflate(R.layout.compress_confirm_layout, null)
        findViewById<COUIToolbar>(R.id.toolbar)?.apply {
            title = getToolbarTitleText(context)
            isTitleCenterStyle = true
            inflateMenu(R.menu.menu_panel_compress)
            menu.findItem(R.id.cancel).apply {
                setOnMenuItemClickListener {
                    dismiss()
                    true
                }
            }
        }
        mInputView = findViewById(R.id.input_compress_name)
        mPathTextView = findViewById<TextView>(R.id.location_text)
        mOppoEditText = mInputView?.editText
        mOppoEditText?.apply {
            showSoftInput(this)
            addInputFilter(this, mIllegalFilter)
            ViewHelper.setClassificationTextSize(context, this)
            addTextChangedListener(this@CompressConfirmDialog)
            setHint(com.filemanager.common.R.string.enter_file_name)
            ellipsize = TextUtils.TruncateAt.END
        }
        mClickButton = findViewById(R.id.btn_ok)
        setMaxCount()
    }

    private fun getToolbarTitleText(context: Context): String {
        return when (mDialogType) {
            CompressConfirmType.COMPRESS -> context.resources.getString(com.filemanager.common.R.string.menu_file_list_compress)
            CompressConfirmType.DECOMPRESS -> context.resources.getString(com.filemanager.common.R.string.menu_file_list_decompress)
            null -> {
                Log.e(TAG, "getToolbarTitleText->mDialogType is unknown")
                ""
            }
        }
    }

    fun setClickButtonListener(clickPositive: (saveName: String, savePath: String) -> Unit) {
        mClickButton?.setOnClickListener {
            val name = getInputValue()
            if (checkFileNameIllegal(name)) {
                showNotice(RenameErrorTipUtil.ERROR_FILENAME_ILLEGAL)
            } else {
                if (!TextUtils.isEmpty(getInputValue()) && mDialogType == CompressConfirmType.COMPRESS) {
                    val targetFile = File(savePath, FileActionCompress.getCompressFileName(name))
                    Log.d(TAG, "BUTTON_POSITIVE click: file=${targetFile.absolutePath}")
                    if (targetFile.exists()) {
                        showWarningNotice(NOTICE_COMPRESS_FILE_EXISTS)
                    } else {
                        clickPositive(name, savePath)
                    }
                } else if (!TextUtils.isEmpty(getInputValue()) && mDialogType == CompressConfirmType.DECOMPRESS) {
                    val targetFile = File(savePath, name)
                    if (targetFile.exists()) {
                        showWarningNotice(NOTICE_COMPRESS_FILE_EXISTS)
                    } else {
                        clickPositive(name, savePath)
                    }
                } else {
                    showWarningNotice(NOTICE_COMPRESS_FILENAME_EMPTY)
                }
            }
        }
    }

    fun setSavePath(myPath: String) {
        savePath = myPath
        mPathTextView?.apply {
            text = Utils.formatPathWithRTL(Utils.getVirtualPathString(MyApplication.sAppContext, myPath))
        }
    }

    private fun getInputValue(): String {
        return mOppoEditText?.text?.toString()?.trim()?.replace("\n", "")?.replace("\r", "") ?: ""
    }

    fun setFileName(fileName: String) {
        when (mDialogType) {
            CompressConfirmType.COMPRESS -> setCompressFileName(fileName)
            CompressConfirmType.DECOMPRESS -> setDecompressFileName(fileName)
            null -> Log.e(TAG, "setFileName->mDialogType is null")
        }
    }

    private fun setCompressFileName(fileName: String) {
        val ext = ".zip"
        val destFile = BaseFileBean()
        destFile.mData = savePath
        val newFile = fetchFileName(destFile, fileName, ext)
        val newFileName = newFile?.mDisplayName?.removeSuffix(ext)?.let {
            if (it.length >= CUSTOM_NAME_LEN) {
                it.subSequence(0, CUSTOM_NAME_LEN).toString()
            } else it
        } ?: fileName
        mOppoEditText?.apply {
            setText(newFileName)
            selectAll()
        }
    }

    private fun setDecompressFileName(fileName: String) {
        val newFileName = fileName.let {
            if (it.length >= CUSTOM_NAME_LEN) {
                it.subSequence(0, CUSTOM_NAME_LEN).toString()
            } else it
        }
        mOppoEditText?.apply {
            setText(newFileName)
            selectAll()
        }
    }

    private fun checkFileNameIllegal(name: String): Boolean {
        if ((name == ".") || (name == "..")) {
            return true
        }
        return mFileNamePattern.matcher(name).matches()
    }

    private fun setMaxCount() {
        mInputView?.maxCount = getNameLengthLimit()
    }

    private fun addInputFilter(editText: EditText, inputFilter: InputFilter) {
        val filters: Array<InputFilter> = editText.filters
        var tmpFilter: Array<InputFilter?>? = null
        var length = 0
        if (filters == null) {
            tmpFilter = arrayOfNulls(1)
        } else {
            length = filters.size
            tmpFilter = arrayOfNulls(length + 1)
            System.arraycopy(filters, 0, tmpFilter, 0, length)
        }
        tmpFilter[length] = inputFilter
        editText.filters = tmpFilter
    }

    private fun showSoftInput(editText: EditText) {
        editText.isFocusable = true
        editText.isFocusableInTouchMode = true
        editText.requestFocus()
        window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
    }

    private fun showNotice(type: Int) {
        val resources = context.resources ?: return
        val notice = RenameErrorTipUtil.getRenameErrorTips(type, resources)
        mInputView?.post { mInputView?.showError(notice) }
    }

    fun setModifySavePathListener(modifyClickListener: OnClickListener) {
        findViewById<TextView>(R.id.modify_text)?.setOnClickListener { v ->
            modifyClickListener.onClick(v)
        }
    }

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
    }

    override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
        if (count > 0) {
            val charsLen = s.toString().length
            val bytesLen = s.toString().toByteArray(StandardCharsets.UTF_8).size
            Log.d(TAG, "onTextChanged charLen = $charsLen, bytesLen = $bytesLen")
            handleInputLimit(charsLen, bytesLen, 0) {
                val inputCharLen = mOppoEditText?.text?.length ?: 0
                if (((start + count) <= inputCharLen) && (count >= before)) {
                    mOppoEditText?.text?.delete(start + before, start + count)
                }
            }
        }
        setPositiveButtonEnabled(s.isNotEmpty())
    }

    override fun afterTextChanged(s: Editable?) {
    }

    private fun setPositiveButtonEnabled(enable: Boolean) {
        mClickButton?.isEnabled = enable
    }

    private fun handleInputLimit(charsLen: Int, bytesLen: Int, initCharsLen: Int, limitOperate: () -> Unit) {
        val charLenExceedLimit = (initCharsLen <= getNameLengthLimit()) && (charsLen > getNameLengthLimit())
        val charByteLenExceedLimit = (initCharsLen >= getNameLengthLimit()) && (bytesLen > getNameByteLengthLimit())
        if (charLenExceedLimit || charByteLenExceedLimit) {
            limitOperate()
            showNotice(RenameErrorTipUtil.ERROR_FILE_NAME_TOO_LONG)
        }
    }

    fun getNameLengthLimit(): Int = CUSTOM_NAME_LEN
    fun getNameByteLengthLimit(): Int = NAME_BYTES_LEN

    private fun showWarningNotice(type: Int) {
        val resources = context.resources ?: return
        val notice = when (type) {
            NOTICE_COMPRESS_FILENAME_EMPTY -> resources.getString(com.filemanager.common.R.string.file_name_null)
            NOTICE_COMPRESS_FILE_EXISTS -> resources.getString(com.filemanager.common.R.string.toast_file_exist)
            RenameErrorTipUtil.ERROR_FILE_NAME_TOO_LONG -> resources.getString(com.filemanager.common.R.string.input_over_upper_limit)
            NOTICE_COMPRESS_FILE_NAME_CANT_START_WITH_POINT -> resources.getString(com.filemanager.common.R.string.error_has_dot_at_first)
            else -> resources.getString(com.filemanager.common.R.string.unsupported_input_the_char)
        }
        mInputView?.showError(notice)
    }
}

enum class CompressConfirmType {
    COMPRESS,
    DECOMPRESS
}