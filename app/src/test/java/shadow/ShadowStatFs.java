/***************************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * Description:Unit test shadow class
 * All rights reserved
 *
 *
 * Version: 1.0
 * Date : 2019/7/22
 * <EMAIL>
 *
 * -------------------Revision History: ----------------------
 *     <author>    <data>     <version >     <desc>
 *   lanjiabo    2019/7/22   1.0            build this moudle
 ****************************************************************/
package shadow;

import android.os.StatFs;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(StatFs.class)
public class ShadowStatFs {
    private static String mPath;
    public ShadowStatFs() {

    }

    public static void setPath(String path) {
        mPath = path;
    }

    @Implementation
    public long getBlockSizeLong() {
        long blockSizeLong;
        if (mPath == null) {
            throw new NullPointerException();
        }
        blockSizeLong = (long) mPath.length();
        return blockSizeLong;
    }

    @Implementation
    public long getAvailableBlocksLong() {
        long availableBlocksLong;
        if (mPath == null) {
            throw new NullPointerException();
        }
        availableBlocksLong = (long) mPath.length();
        return availableBlocksLong;
    }

    @Implementation
    public long getBlockCountLong() {
        long blockCountLong;
        if (mPath == null) {
            throw new NullPointerException();
        }
        blockCountLong = (10L * mPath.length());
        return blockCountLong;
    }
}
