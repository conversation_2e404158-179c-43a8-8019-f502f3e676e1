/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  com.coloros.filemanager.category.albumset.FolderNoteTest
 * * Description : FolderNote 的单元测试
 * * Version     : 1.0
 * * Date        : 2022/4/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.common.fileutils

import com.filemanager.common.utils.FolderNote
import io.mockk.every
import io.mockk.spyk
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.util.Locale
import org.junit.Assert.assertNull

class FolderNoteTest {
    private lateinit var storedLocale: Locale

    @Before
    fun before() {
        storedLocale = Locale.getDefault()
    }

    @After
    fun after() {
        Locale.setDefault(storedLocale)
    }

    @Test
    fun `should return traditional Chinese if local is TW and chinese when get note name`() {
        val note = spyk<FolderNote.Note> {
            every { note } returns "note"
            every { notezhrCN } returns "简体字"
            every { notezhrTW } returns "繁体字"
        }
        val localeTW = Locale("zh", "TW")
        Locale.setDefault(localeTW)
        Assert.assertEquals("繁体字", note.noteName)
        val localeHK = Locale("zh", "HK")
        Locale.setDefault(localeHK)
        Assert.assertEquals("繁体字", note.noteName)
    }

    @Test
    fun `should return simplified Chinese if local is CN and chinese when get note name`() {
        val note = spyk<FolderNote.Note> {
            every { note } returns "note"
            every { notezhrCN } returns "简体字"
            every { notezhrTW } returns "繁体字"
        }
        val locale = Locale("zh", "CN")
        Locale.setDefault(locale)
        Assert.assertEquals("简体字", note.noteName)
    }

    @Test
    fun `should return english if local is not CN when get note name`() {
        val note = spyk<FolderNote.Note> {
            every { note } returns "note"
            every { notezhrCN } returns "简体字"
            every { notezhrTW } returns "繁体字"
        }
        val locale = Locale("en", "US")
        Locale.setDefault(locale)
        Assert.assertEquals("note", note.noteName)
    }

    @Test
    fun `should return folder when get note if constructor with folder`() {
        val note = spyk(FolderNote.Note())
        assertNull("folder", note.note)
        assertNull(note.notezhrTW)
        assertNull(note.notezhrCN)
    }
}