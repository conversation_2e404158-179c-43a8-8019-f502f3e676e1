package com.filemanager.common.view.viewpager;

import android.content.Context;
import android.os.Parcel;
import android.os.Parcelable;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.filemanager.common.utils.Log;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.annotation.Config;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyFloat;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * RTLViewPager的单元测试类
 * 测试RTLViewPager的各种功能和行为
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 29)
public class RTLViewPagerTest {

    private RTLViewPager rtlViewPager;
    private Context context;

    @Mock
    private PagerAdapter mockAdapter;

    @Mock
    private ViewPager.OnPageChangeListener mockListener;

    /**
     * 在每个测试方法执行前初始化
     */
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        context = RuntimeEnvironment.application;
        rtlViewPager = new RTLViewPager(context);
    }

    /**
     * 在每个测试方法执行后清理
     */
    @After
    public void tearDown() {
        Mockito.reset(mockAdapter, mockListener);
    }

    /**
     * 测试只传入Context的构造函数
     */
    @Test
    public void testConstructor_withContext() {
        RTLViewPager viewPager = new RTLViewPager(context);
        assertNotNull(viewPager);
    }

    /**
     * 测试传入Context和AttributeSet的构造函数
     */
    @Test
    public void testConstructor_withContextAndAttributeSet() {
        RTLViewPager viewPager = new RTLViewPager(context, null);
        assertNotNull(viewPager);
    }

    /**
     * 测试默认用户输入是否启用
     */
    @Test
    public void testIsUserInputEnabled_defaultValue() {
        assertTrue(rtlViewPager.isUserInputEnabled());
    }

    /**
     * 测试设置用户输入启用状态
     */
    @Test
    public void testSetUserInputEnabled() {
        rtlViewPager.setUserInputEnabled(false);
        assertFalse(rtlViewPager.isUserInputEnabled());

        rtlViewPager.setUserInputEnabled(true);
        assertTrue(rtlViewPager.isUserInputEnabled());
    }

    /**
     * 测试当用户输入禁用时的触摸事件处理
     */
    @Test
    public void testOnTouchEvent_whenUserInputDisabled_shouldReturnFalse() {
        rtlViewPager.setUserInputEnabled(false);
        MotionEvent motionEvent = mock(MotionEvent.class);
        assertFalse(rtlViewPager.onTouchEvent(motionEvent));
    }

    /**
     * 测试当用户输入启用时的触摸事件处理
     */
    @Test
    public void testOnTouchEvent_whenUserInputEnabled_shouldCallSuper() {
        rtlViewPager.setUserInputEnabled(true);
        MotionEvent motionEvent = mock(MotionEvent.class);
        rtlViewPager.onTouchEvent(motionEvent);
    }

    /**
     * 测试触摸事件异常处理
     */
    @Test
    public void testOnTouchEvent_exceptionHandling() {
        RTLViewPagerHelper spyViewPager = spy(new RTLViewPagerHelper(context));
        doThrow(new RuntimeException("Test exception")).when(spyViewPager).superOnTouchEvent(any(MotionEvent.class));
        spyViewPager.setUserInputEnabled(true);
        MotionEvent motionEvent = mock(MotionEvent.class);
        assertFalse(spyViewPager.onTouchEvent(motionEvent));
    }

    /**
     * 测试当用户输入禁用时的拦截触摸事件处理
     */
    @Test
    public void testOnInterceptTouchEvent_whenUserInputDisabled_shouldReturnFalse() {
        rtlViewPager.setUserInputEnabled(false);
        MotionEvent motionEvent = mock(MotionEvent.class);
        assertFalse(rtlViewPager.onInterceptTouchEvent(motionEvent));
    }

    /**
     * 测试当用户输入启用时的拦截触摸事件处理
     */
    @Test
    public void testOnInterceptTouchEvent_whenUserInputEnabled_shouldCallSuper() {
        rtlViewPager.setUserInputEnabled(true);
        MotionEvent motionEvent = mock(MotionEvent.class);
        rtlViewPager.onInterceptTouchEvent(motionEvent);
    }

    /**
     * 测试拦截触摸事件异常处理
     */
    @Test
    public void testOnInterceptTouchEvent_exceptionHandling() {
        RTLViewPagerHelper spyViewPager = spy(new RTLViewPagerHelper(context));
        doThrow(new RuntimeException("Test exception")).when(spyViewPager).superOnInterceptTouchEvent(any(MotionEvent.class));
        spyViewPager.setUserInputEnabled(true);
        MotionEvent motionEvent = mock(MotionEvent.class);
        assertFalse(spyViewPager.onInterceptTouchEvent(motionEvent));
    }

    /**
     * 测试未指定高度时的测量处理
     */
    @Test
    public void testOnMeasure_withUnspecifiedHeight() {
        rtlViewPager.onMeasure(View.MeasureSpec.makeMeasureSpec(100, View.MeasureSpec.EXACTLY),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
    }

    /**
     * 测试RTL属性变化时方向相同的情况
     */
    @Test
    public void testOnRtlPropertiesChanged_sameDirection_noAction() {
        rtlViewPager.onRtlPropertiesChanged(View.LAYOUT_DIRECTION_LTR);
        assertEquals(View.LAYOUT_DIRECTION_LTR, getDirectionField(rtlViewPager));
    }

    /**
     * 测试反转空适配器
     */
    @Test
    public void testReverseAdapter_withNullAdapter() {
        assertNull(invokePrivateMethod(rtlViewPager, "reverseAdapter", new Class[]{PagerAdapter.class}, new Object[]{null}));
    }

    /**
     * 测试反转有效适配器
     */
    @Test
    public void testReverseAdapter_withValidAdapter() {
        PagerAdapter adapter = mock(PagerAdapter.class);
        when(adapter.getCount()).thenReturn(3);
        PagerAdapter reversed = (PagerAdapter) invokePrivateMethod(rtlViewPager, "reverseAdapter", new Class[]{PagerAdapter.class}, new Object[]{adapter});
        assertNotNull(reversed);
        assertTrue(isInstanceOf(reversed, "com.filemanager.common.view.viewpager.RTLViewPager$ReverseAdapter"));
    }

    /**
     * 测试非RTL模式下的位置反转
     */
    @Test
    public void testReversePosition_notRTL() {
        setDirectionField(rtlViewPager, View.LAYOUT_DIRECTION_LTR);
        int position = 2;
        assertEquals(position, (int) invokePrivateMethod(rtlViewPager, "reversePosition", new Class[]{int.class}, new Object[]{position}));
    }

    /**
     * 测试RTL模式判断
     */
    @Test
    public void testIsRTL() {
        setDirectionField(rtlViewPager, View.LAYOUT_DIRECTION_RTL);
        assertTrue((Boolean) invokePrivateMethod(rtlViewPager, "isRTL", new Class[]{}, new Object[]{}));

        setDirectionField(rtlViewPager, View.LAYOUT_DIRECTION_LTR);
        assertFalse((Boolean) invokePrivateMethod(rtlViewPager, "isRTL", new Class[]{}, new Object[]{}));
    }

    /**
     * 测试恢复保存状态
     */
    @Test
    public void testOnRestoreInstanceState_withSavedState() {
        Parcel parcel = Parcel.obtain();
        parcel.writeInt(View.LAYOUT_DIRECTION_RTL);
        parcel.writeParcelable(null, 0);
        parcel.setDataPosition(0);

        RTLViewPager.SavedState savedState = RTLViewPager.SavedState.CREATOR.createFromParcel(parcel);
        rtlViewPager.onRestoreInstanceState(savedState);
        assertEquals(View.LAYOUT_DIRECTION_RTL, getDirectionField(savedState));
    }

    /**
     * 测试添加页面变化监听器
     */
    @Test
    public void testAddOnPageChangeListener() {
        rtlViewPager.addOnPageChangeListener(mockListener);
        assertTrue(getListenerMapField(rtlViewPager).containsKey(mockListener));
    }

    /**
     * 测试移除页面变化监听器
     */
    @Test
    public void testRemoveOnPageChangeListener() {
        rtlViewPager.addOnPageChangeListener(mockListener);
        rtlViewPager.removeOnPageChangeListener(mockListener);
        assertFalse(getListenerMapField(rtlViewPager).containsKey(mockListener));
    }

    /**
     * 测试清除所有页面变化监听器
     */
    @Test
    public void testClearOnPageChangeListeners() {
        rtlViewPager.addOnPageChangeListener(mockListener);
        rtlViewPager.clearOnPageChangeListeners();
        assertTrue(getListenerMapField(rtlViewPager).isEmpty());
    }

    /**
     * 测试反转适配器在非RTL模式下的位置反转
     */
    @Test
    public void testReverseAdapter_reversePosition_notRTL() {
        RTLViewPager viewPager = new RTLViewPager(context);
        setDirectionField(viewPager, View.LAYOUT_DIRECTION_LTR);
        PagerAdapter reverseAdapter = createReverseAdapter(viewPager, mockAdapter);

        try {
            Method method = reverseAdapter.getClass().getMethod("reversePosition", int.class);
            assertEquals(2, (int) method.invoke(reverseAdapter, 2));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试反转适配器在RTL模式下的位置反转
     */
    @Test
    public void testReverseAdapter_reversePosition_RTL() {
        RTLViewPager viewPager = new RTLViewPager(context);
        setDirectionField(viewPager, View.LAYOUT_DIRECTION_RTL);
        PagerAdapter reverseAdapter = createReverseAdapter(viewPager, mockAdapter);
        when(mockAdapter.getCount()).thenReturn(5);

        try {
            Method method = reverseAdapter.getClass().getMethod("reversePosition", int.class);
            assertEquals(2, (int) method.invoke(reverseAdapter, 2));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试反转适配器的getItemPosition方法在位置未变化时的情况
     */
    @Test
    public void testReverseAdapter_getItemPosition_unchanged() {
        RTLViewPager viewPager = new RTLViewPager(context);
        setDirectionField(viewPager, View.LAYOUT_DIRECTION_RTL);
        PagerAdapter reverseAdapter = createReverseAdapter(viewPager, mockAdapter);
        when(mockAdapter.getItemPosition(any())).thenReturn(PagerAdapter.POSITION_UNCHANGED);

        try {
            Method method = reverseAdapter.getClass().getMethod("getItemPosition", Object.class);
            assertEquals(PagerAdapter.POSITION_NONE, (int) method.invoke(reverseAdapter, new Object()));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试SavedState的创建
     */
    @Test
    public void testSavedState_creator() {
        Parcel parcel = Parcel.obtain();
        parcel.writeInt(View.LAYOUT_DIRECTION_RTL);
        parcel.writeParcelable(null, 0);
        parcel.setDataPosition(0);

        RTLViewPager.SavedState created = RTLViewPager.SavedState.CREATOR.createFromParcel(parcel);
        assertEquals(View.LAYOUT_DIRECTION_RTL, getDirectionField(created));
    }

    /**
     * 测试SavedState的数组创建
     */
    @Test
    public void testSavedState_newArray() {
        RTLViewPager.SavedState[] array = RTLViewPager.SavedState.CREATOR.newArray(3);
        assertEquals(3, array.length);
    }

    // 以下是辅助方法，用于访问私有字段和方法

    /**
     * 获取对象的mDirection字段值
     */
    private int getDirectionField(Object obj) {
        try {
            Field field = obj.getClass().getDeclaredField("mDirection");
            field.setAccessible(true);
            return field.getInt(obj);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 设置对象的mDirection字段值
     */
    private void setDirectionField(Object obj, int value) {
        try {
            Field field = obj.getClass().getDeclaredField("mDirection");
            field.setAccessible(true);
            field.setInt(obj, value);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 调用对象的私有方法
     */
    private Object invokePrivateMethod(Object obj, String methodName, Class<?>[] paramTypes, Object[] args) {
        try {
            Method method = obj.getClass().getDeclaredMethod(methodName, paramTypes);
            method.setAccessible(true);
            return method.invoke(obj, args);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取对象的mListenerMap字段值
     */
    @SuppressWarnings("unchecked")
    private Map<ViewPager.OnPageChangeListener, ViewPager.OnPageChangeListener> getListenerMapField(Object obj) {
        try {
            Field field = obj.getClass().getDeclaredField("mListenerMap");
            field.setAccessible(true);
            return (Map<ViewPager.OnPageChangeListener, ViewPager.OnPageChangeListener>) field.get(obj);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 检查对象是否是某个类的实例
     */
    private boolean isInstanceOf(Object obj, String className) {
        Class<?> clazz = obj.getClass();
        while (clazz != null) {
            if (clazz.getName().equals(className)) {
                return true;
            }
            clazz = clazz.getSuperclass();
        }
        return false;
    }

    /**
     * 创建反转适配器实例
     */
    private PagerAdapter createReverseAdapter(RTLViewPager viewPager, PagerAdapter delegate) {
        try {
            Class<?> reverseAdapterClass = Class.forName("com.filemanager.common.view.viewpager.RTLViewPager$ReverseAdapter");
            return (PagerAdapter) reverseAdapterClass.getDeclaredConstructors()[0].newInstance(viewPager, delegate);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建反转页面变化监听器实例
     */
    private ViewPager.OnPageChangeListener createReverseOnPageChangeListener(RTLViewPager viewPager, ViewPager.OnPageChangeListener listener) {
        try {
            Class<?> reverseListenerClass = Class.forName("com.filemanager.common.view.viewpager.RTLViewPager$ReverseOnPageChangeListener");
            // 获取带有正确参数类型的构造函数
            return (ViewPager.OnPageChangeListener) reverseListenerClass.getDeclaredConstructors()[0].newInstance(viewPager, listener);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取SavedState的mDirection字段值
     */
    private int getDirectionField(RTLViewPager.SavedState savedState) {
        try {
            Field field = savedState.getClass().getDeclaredField("mDirection");
            field.setAccessible(true);
            return field.getInt(savedState);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

/**
 * RTLViewPager的测试辅助类
 * 提供对父类protected和private方法的访问
 */
class RTLViewPagerHelper extends RTLViewPager {
    public RTLViewPagerHelper(Context context) {
        super(context);
    }

    public int superGetCurrentItem() {
        return super.getCurrentItem();
    }

    public void superSetCurrentItem(int item, boolean smoothScroll) {
        super.setCurrentItem(item, smoothScroll);
    }

    public void superSetCurrentItem(int item) {
        super.setCurrentItem(item);
    }

    public Parcelable superOnSaveInstanceState() {
        return super.onSaveInstanceState();
    }

    public void superOnTouchEvent(MotionEvent ev) {
        super.onTouchEvent(ev);
    }

    public boolean superOnInterceptTouchEvent(MotionEvent ev) {
        return super.onInterceptTouchEvent(ev);
    }

    public void superSetOnPageChangeListener(OnPageChangeListener listener) {
        super.setOnPageChangeListener(listener);
    }

    public PagerAdapter superGetAdapter() {
        return super.getAdapter();
    }

    public int reversePosition(int position) {
        return (Integer) invokePrivateMethod(this, "reversePosition", new Class[]{int.class}, new Object[]{position});
    }

    public boolean isRTL() {
        return (Boolean) invokePrivateMethod(this, "isRTL", new Class[]{}, new Object[]{});
    }

    /**
     * 调用对象的私有方法
     */
    private Object invokePrivateMethod(Object obj, String methodName, Class<?>[] paramTypes, Object[] args) {
        try {
            Method method = obj.getClass().getDeclaredMethod(methodName, paramTypes);
            method.setAccessible(true);
            return method.invoke(obj, args);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}