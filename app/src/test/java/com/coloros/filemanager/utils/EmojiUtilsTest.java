/*
 * ********************************************************************
 *  * * Copyright (C), 2019 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  EmojiUtils.java
 *  * * Description : EmojiUtils.java
 *  * * Version     : 1.0
 *  * * Date        : 19-11-23 下午5:34
 *  * * Author      : <EMAIL>
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.coloros.filemanager.utils;

import com.coloros.filemanager.BaseTest;
import com.filemanager.common.utils.EmojiUtils;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

@Ignore("stupid test")
public class EmojiUtilsTest extends BaseTest {
    @Test
    public void should_verify_when_contains_emoji() {
        Assert.assertEquals(false, EmojiUtils.isContainEmoji(null));
        Assert.assertEquals(false, EmojiUtils.isContainEmoji("there is no emoji"));
        Assert.assertEquals(true, EmojiUtils.isContainEmoji("there is ☀⭐"));
    }

    // containsIllegalCharFileName
    @Test
    public void should_verify_when_contains_illegal_char_file_name() {
        Assert.assertFalse(EmojiUtils.containsIllegalCharFileName(null));
        Assert.assertFalse(EmojiUtils.containsIllegalCharFileName("test"));
    }

    // getBuilder
    @Test
    public void should_return_builder() {
        Assert.assertNotNull(EmojiUtils.getBuilder());
    }
}
