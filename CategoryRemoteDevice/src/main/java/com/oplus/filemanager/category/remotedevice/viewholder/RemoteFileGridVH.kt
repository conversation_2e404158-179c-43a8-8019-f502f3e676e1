/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemoteFileGridVH
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.viewholder

import android.content.Context
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.EmojiUtils
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ViewUtils
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.MiddleMultilineTextView

class RemoteFileGridVH(convertView: View) : BaseRemoteFileVH(convertView) {

    companion object {
        private const val TAG = "FileBrowserGridVH"
        private const val SECOND = 1000L
        private const val TWO = 2
        fun getLayoutId(): Int {
            return com.oplus.filemanager.category.remotedevice.R.layout.remote_grid_item
        }
    }

    private val mImg: FileThumbView = convertView.findViewById(R.id.file_grid_item_icon)
    private val mTitle: MiddleMultilineTextView = convertView.findViewById(R.id.title_tv)
    private val mDetail: TextView = convertView.findViewById(R.id.detail_tv)
    private val mRootView: RelativeLayout = convertView.findViewById(R.id.file_grid_item_layout)
    private var mVideoIcon: TextView = convertView.findViewById(R.id.file_duration_tv)
    private var apkIcon: ImageView = convertView.findViewById(R.id.apk_icon)
    private var mIconMarginBottom: Int

    private val mImgRadius =
        MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.scan_grid_bg_radius)
    private val docRadius =
        MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.dimen_4dp)
    private val videoIconHeight =
        MyApplication.appContext.resources.getDimensionPixelSize(com.oplus.filemanager.category.remotedevice.R.dimen.grid_video_icon_height)

    init {
        mCheckBox = convertView.findViewById(R.id.gridview_scrollchoice_checkbox)
        mIconMarginBottom =
            MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.file_grid_icon_margin_bottom)
    }

    override fun updateViewHolder(
        context: Context,
        key: Int?,
        file: BaseFileBean,
        choiceMode: Boolean,
        selectionArray: MutableList<Int>,
        adapter: BaseSelectionRecycleAdapter<*, *>
    ) {
        val path = file.mData
        val type = file.mLocalType
        if (path == null) {
            Log.d(TAG, "updateViewHolder path null")
            return
        }
        if (choiceMode) {
            mRootView.setBackgroundResource(R.drawable.file_browser_scan_grid_item_bg)
        } else {
            mRootView.background = null
        }
        mTitle.tag = path
        mDetail.tag = path
        setTitleText(file)
        processImageAndDetail(context, file, type, path, choiceMode)
        mCheckBox?.let {
            if (choiceMode) {
                if (selectionArray.contains(key)) {
                    it.state = COUICheckBox.SELECT_ALL
                    it.visibility = View.VISIBLE
                } else {
                    it.isEnabled = false
                    it.state = COUICheckBox.SELECT_NONE
                    it.visibility = View.VISIBLE
                    it.isEnabled = true
                }
            } else {
                it.state = COUICheckBox.SELECT_NONE
                it.jumpDrawablesToCurrentState()
                it.visibility = View.GONE
            }
        }
        processDerictoryFileAlpha(file, adapter, choiceMode)
        showVideoIconIfNeed(file, mVideoIcon)
        showApkIconIfNeed(file, apkIcon, true)
    }

    private fun setTitleText(file: BaseFileBean) {
        val bean = file as RemoteFileBean
        val showText = if (bean.shouldShowAlternativeName()) bean.alternativeName else bean.mDisplayName
        showText?.apply {
            mTitle.text = this
        }
        itemView.addOnLayoutChangeListener(object : View.OnLayoutChangeListener {
            override fun onLayoutChange(
                v: View?,
                l: Int,
                t: Int,
                r: Int,
                b: Int,
                oL: Int,
                oT: Int,
                oR: Int,
                oB: Int
            ) {
                itemView.removeOnLayoutChangeListener(this)
                showText?.apply {
                    mTitle.setMultiText(this)
                }
            }
        })
    }

    private fun processImageAndDetail(
        context: Context,
        file: BaseFileBean,
        type: Int,
        path: String,
        choiceMode: Boolean
    ) {
        FileImageVHUtils.updateFileGridImgSize(context, mImg, file)
        updateDetailTextViewPadding(context, choiceMode)
        val layoutParams = mImg.layoutParams as RelativeLayout.LayoutParams
        layoutParams.bottomMargin = mIconMarginBottom
        mImg.setStrokeStyle(FileThumbView.STROKE_NONE)
        setImageScaleType(type, context, path, layoutParams)
        if (type == MimeTypeHelper.APPLICATION_TYPE) {
            FileImageLoader.sInstance.clear(context, mImg)
            mImg.mErrorLoadTimes = 0
            mImg.setCallBack(object : FileThumbView.LoadCallBack {
                override fun onError() {
                    displayApplicationWithDetail(context, file, path)
                }
            })
            displayApplicationWithDetail(context, file, path)
        } else {
            val radius = ViewUtils.getIconRadius(type, docRadius, mImgRadius)
            FileImageLoader.sInstance.clear(context, mImg)
            FileImageLoader.sInstance.displayDefault(
                file,
                mImg,
                0,
                radius,
                FileImageLoader.THUMBNAIL_TYPE_SQUARE,
                false,
                isCoverError = true,
                isSmallDoc = true
            )
            mDetail.visibility = View.VISIBLE
            showDetail(context, file, mDetail, path, false)
        }
    }

    private fun setImageScaleType(
        type: Int,
        context: Context,
        path: String,
        layoutParams: RelativeLayout.LayoutParams
    ) {
        if (type == MimeTypeHelper.DRM_TYPE) {
            mImg.setDrmState(true)
            val typeString = MimeTypeHelper.getDrmMimeType(context, path)
            if (!TextUtils.isEmpty(typeString) && (typeString!!.startsWith("video/") || typeString.startsWith(
                    "image/"
                ))
            ) {
                mImg.setStrokeStyle(FileThumbView.STROKE_4DP)
                mImg.scaleType = ImageView.ScaleType.FIT_XY
            } else {
                mImg.scaleType = ImageView.ScaleType.FIT_CENTER
            }
        } else {
            mImg.setDrmState(false)
            when {
                (type == MimeTypeHelper.IMAGE_TYPE) || (type == MimeTypeHelper.VIDEO_TYPE) -> {
                    mImg.setStrokeStyle(FileThumbView.STROKE_4DP)
                    mImg.scaleType = ImageView.ScaleType.FIT_XY
                }
                (MimeTypeHelper.isDocType(type)) -> {
                    if (MimeTypeHelper.PPT_TYPE == type || type == MimeTypeHelper.PPTX_TYPE) {
                        mImg.scaleType = ImageView.ScaleType.FIT_END
                    } else {
                        mImg.scaleType = ImageView.ScaleType.FIT_CENTER
                    }
                }
                (type == MimeTypeHelper.APPLICATION_TYPE) -> {
                    layoutParams.bottomMargin = 0
                    mImg.scaleType = ImageView.ScaleType.FIT_CENTER
                }
                (type == MimeTypeHelper.DIRECTORY_TYPE) -> {
                    layoutParams.bottomMargin = 0
                    mImg.scaleType = ImageView.ScaleType.FIT_END
                }
                else -> mImg.scaleType = ImageView.ScaleType.FIT_CENTER
            }
        }
    }

    private fun processDerictoryFileAlpha(
        file: BaseFileBean,
        adapter: BaseSelectionRecycleAdapter<*, *>,
        choiceMode: Boolean
    ) {
        val containIllegalChar = EmojiUtils.containsIllegalCharFileName(file.mDisplayName)
        val canNotSelect = containIllegalChar || file.isDir()
        val needAlpha = choiceMode && canNotSelect
        val alpha = HiddenFileHelper.getAlphaWithHidden(needAlpha, adapter.mIsDarkModel)
        mTitle.alpha = alpha
        mDetail.alpha = alpha
        mImg.alpha = alpha
        mCheckBox?.alpha = alpha
    }

    private fun displayApplicationWithDetail(
        context: Context,
        file: BaseFileBean,
        path: String
    ) {
        FileImageLoader.sInstance.displayApplicationWithDetail(
            file, mImg,
            { applicationInfoDetail ->
                val tagDetail = mDetail.tag as? String
                if (applicationInfoDetail?.mPath == tagDetail && !TextUtils.isEmpty(
                        applicationInfoDetail?.mApkVersion
                    )
                ) {
                    mDetail.visibility = View.VISIBLE
                    mDetail.text = KtUtils.formatSize(file)
                } else {
                    showDetail(context, file, mDetail, path, false)
                }
            }, isGridMode = true
        )
    }

    private fun showVideoIconIfNeed(file: BaseFileBean, mVideoIcon: TextView) {
        if (MimeTypeHelper.VIDEO_TYPE == file.mLocalType) {
            mVideoIcon.visibility = View.VISIBLE
            val ext = getExtForBean(file)
            ext?.let {
                mVideoIcon.text = it
            }
        } else {
            mVideoIcon.visibility = View.GONE
        }
    }

    /**
     * 更新icon的显示的底部margin，保证icon相对图片的布局居中
     */
    private fun updateVideoIconLayoutButtonMargin() {
        val imglayoutParams = mImg.layoutParams as RelativeLayout.LayoutParams
        val imgMargingButtom = imglayoutParams.bottomMargin
        val imgHeight = imglayoutParams.height
        val videoIconMargingButton = imgMargingButtom + (imgHeight - videoIconHeight) / TWO

        val videoLayoutParams = mVideoIcon.layoutParams as RelativeLayout.LayoutParams
        videoLayoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM)
        videoLayoutParams.bottomMargin = videoIconMargingButton
        mVideoIcon.layoutParams = videoLayoutParams
    }

    private fun updateDetailTextViewPadding(context: Context, choiceMode: Boolean) {
        if (choiceMode) {
            val padding =
                context.resources.getDimensionPixelSize(com.oplus.filemanager.category.remotedevice.R.dimen.grid_detail_padding_when_selected)
            mDetail.setPadding(padding, 0, padding, 0)
        } else {
            mDetail.setPadding(0, 0, 0, 0)
        }
    }
}