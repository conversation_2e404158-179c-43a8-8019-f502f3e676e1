/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BRUtils.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/8/17
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/8/17      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.backuprestore

import com.filemanager.common.utils.Log
import java.io.ByteArrayOutputStream
import java.io.FileDescriptor
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.nio.charset.Charset

object BRUtils {

    private const val TAG = "BRUtils"
    private const val BYTE_SIZE = 1024

    @JvmStatic
    fun saveToFile(fd: FileDescriptor?, content: String) {
        var outStream: FileOutputStream? = null
        val buf = content.toByteArray(Charset.forName("UTF-8"))
        try {
            outStream = FileOutputStream(fd)
            outStream.write(buf, 0, buf.size)
            outStream.flush()
        } catch (e: IOException) {
            Log.e(TAG, "saveToFile failed.", e)
        } finally {
            if (outStream != null) {
                try {
                    outStream.close()
                } catch (e: IOException) {
                    Log.e(TAG, "saveToFile failed.", e)
                }
            }
        }
    }

    @Suppress("TooGenericExceptionCaught", "VarCouldBeVal")
    @JvmStatic
    fun getContentFromFile(fd: FileDescriptor?): String? {
        var `is`: InputStream? = null
        var bos: ByteArrayOutputStream? = null
        try {
            `is` = FileInputStream(fd)
            bos = ByteArrayOutputStream()
            var len = -1
            val buffer = ByteArray(BYTE_SIZE)
            while (`is`.read(buffer, 0, BYTE_SIZE).also { len = it } != -1) {
                bos.write(buffer, 0, len)
            }
            return bos.toString("utf-8")
        } catch (e: Exception) {
            Log.e(TAG, "getContentFromFile failed.", e)
        } finally {
            if (`is` != null) {
                try {
                    `is`.close()
                } catch (e: IOException) {
                    Log.e(TAG, "getContentFromFile failed.", e)
                }
            }
            if (bos != null) {
                try {
                    bos.close()
                } catch (e: IOException) {
                    Log.e(TAG, "getContentFromFile failed.", e)
                }
            }
        }
        return null
    }
}