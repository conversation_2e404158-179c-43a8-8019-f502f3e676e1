<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="@dimen/dimen_108dp"
        android:layout_height="@dimen/dimen_108dp">

        <com.filemanager.common.view.FileThumbView
            android:id="@+id/single_file_icon"
            android:layout_width="@dimen/dimen_108dp"
            android:layout_height="@dimen/dimen_108dp"
            android:layout_gravity="center"
            android:forceDarkAllowed="false"
            android:scaleType="fitCenter" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/video_type_tv"
            android:layout_width="@dimen/dimen_36dp"
            android:layout_height="wrap_content"
            android:background="@drawable/item_video_duration_view_bg"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:layout_gravity="bottom|center"
            android:textColor="?attr/couiColorLabelOnColor"
            android:textSize="@dimen/duration_text_size"
            android:visibility="gone"
            />

    </FrameLayout>


    <com.filemanager.common.view.TextViewSnippet
        android:id="@+id/single_file_title_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_8dp"
        android:layout_marginHorizontal="@dimen/download_file_desc_margin"
        android:ellipsize="middle"
        android:gravity="center_horizontal"
        android:maxLines="2"
        android:singleLine="false"
        android:textAppearance="?android:attr/textAppearanceLarge"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textSize="@dimen/font_size_20" />

    <TextView
        android:id="@+id/single_file_size_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/download_file_desc_margin"
        android:layout_marginTop="@dimen/dimen_4dp"
        android:adjustViewBounds="true"
        android:ellipsize="middle"
        android:gravity="center_horizontal"
        android:singleLine="true"
        android:textAppearance="?android:attr/textAppearanceSmall"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textSize="@dimen/font_size_14" />

</LinearLayout>