package com.oplus.filemanager.category.globalsearch.manager.filter

import android.util.ArrayMap
import com.filemanager.common.MyApplication
import io.mockk.every
import io.mockk.mockkObject
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * FilterConstants类的单元测试类
 * 用于测试FilterConstants中定义的常量和方法
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class FilterConstantsTest {

    /**
     * 在每个测试方法执行前的初始化方法
     * 1. 使用mockk框架模拟MyApplication单例对象
     * 2. 设置appContext返回Robolectric的运行时环境
     */
    @Before
    fun setup() {
        mockkObject(MyApplication)
        every { MyApplication.appContext } returns org.robolectric.RuntimeEnvironment.application
    }

    /**
     * 测试FilterConstants中定义的常量值是否正确
     * 验证所有定义的过滤条件常量是否符合预期值
     */
    @Test
    fun testFilterConstantsValues() {
        // 测试常量值是否正确
        assertEquals(-1, FilterConstants.FILTER_ITEM_ALL)  // 验证"全部"过滤项的常量值
        assertEquals(1, FilterConstants.FILTER_TIME)  // 验证时间过滤类型常量值
        assertEquals(1 shl 0, FilterConstants.FILTER_TIME_TODAY)  // 验证"今天"时间过滤常量值
        assertEquals(1 shl 1, FilterConstants.FILTER_TIME_3_DAY)  // 验证"3天内"时间过滤常量值
        assertEquals(1 shl 2, FilterConstants.FILTER_TIME_7_DAY)  // 验证"7天内"时间过滤常量值
        assertEquals(1 shl 3, FilterConstants.FILTER_TIME_30_DAY)  // 验证"30天内"时间过滤常量值
        assertEquals(2, FilterConstants.FILTER_FROM)  // 验证来源过滤类型常量值
        assertEquals(3, FilterConstants.FILTER_IMG)  // 验证图片过滤类型常量值
        assertEquals(4, FilterConstants.FILTER_VIDEO)  // 验证视频过滤类型常量值
        assertEquals(5, FilterConstants.FILTER_AUDIO)  // 验证音频过滤类型常量值
        assertEquals(6, FilterConstants.FILTER_DOC)  // 验证文档过滤类型常量值
        assertEquals(7, FilterConstants.FILTER_COMPRESS)  // 验证压缩文件过滤类型常量值
        assertEquals(8, FilterConstants.FILTER_DRIVE_FILE)  // 验证云盘文件过滤类型常量值
        assertEquals(9, FilterConstants.FILTER_THIRD_APP)  // 验证第三方应用过滤类型常量值
        assertEquals(10, FilterConstants.FILTER_THIRD_APP_AND_OLD_FROM)  // 验证第三方应用和旧来源过滤类型常量值
        assertEquals(11, FilterConstants.FILTER_REMOTE_FILE)  // 验证远程文件过滤类型常量值
    }

    /**
     * 测试getAllFilterConditions方法
     * 验证返回的过滤条件列表是否包含所有预期的过滤类型
     */
    @Test
    fun testGetAllFilterConditions() {
        // 测试获取所有过滤条件
        val expected = listOf(
            FilterConstants.FILTER_TIME,  // 时间过滤
            FilterConstants.FILTER_FROM,  // 来源过滤
            FilterConstants.FILTER_IMG,  // 图片过滤
            FilterConstants.FILTER_VIDEO,  // 视频过滤
            FilterConstants.FILTER_AUDIO,  // 音频过滤
            FilterConstants.FILTER_DOC,  // 文档过滤
            FilterConstants.FILTER_COMPRESS,  // 压缩文件过滤
            FilterConstants.FILTER_DRIVE_FILE,  // 云盘文件过滤
            FilterConstants.FILTER_THIRD_APP,  // 第三方应用过滤
            FilterConstants.FILTER_REMOTE_FILE  // 远程文件过滤
        )
        assertEquals(expected, FilterConstants.getAllFilterConditions())
    }

    /**
     * 测试getAllFilterConditionDesc方法
     * 验证返回的过滤条件描述映射是否符合预期:
     * 1. 返回类型是ArrayMap<Int, String>
     * 2. 包含所有过滤条件的描述
     * 3. 每个过滤条件都有对应的描述文本
     */
    @Test
    fun testGetAllFilterConditionDesc() {
        // 测试获取所有过滤条件描述
        val result = FilterConstants.getAllFilterConditionDesc()
        assert(result is ArrayMap<Int, String>)  // 验证返回类型是否正确
        assertEquals(10, result.size)  // 验证描述数量是否正确
        
        // 验证每个过滤条件都有对应的描述
        FilterConstants.getAllFilterConditions().forEach { filter ->
            assert(result.containsKey(filter))  // 确保每个过滤条件都有对应的描述
        }
    }
}