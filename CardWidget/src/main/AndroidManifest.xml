<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.oplus.filemanager.cardwidget">

    <application>
        <!-- appcard -->
        <meta-data
            android:name="com.oplus.ocs.card.AUTH_CODE"
            android:value="ADBEAiB0mohXgZlbmdKh3C1mcN1WeXG22s90q+8p/D8AP3i/6QIgScAErCEvWWiFP/uIlOovji+Fe5Vc0MTVq9sbC7mNH41r3W59"/>
        <!--
        最近卡片的资源已被删除，在石墨-》文件管理组目录下，放开此功能需要恢复相关资源
        https://odocs.myoas.com/file/zdkyBY4oX5iDQmA6 《最近卡片删除资源20230117.zip》
        <provider
            android:name="com.oplus.filemanager.cardwidget.provider.RecentFilesCardWidgetProvider"
            android:authorities="com.oplus.filemanager.recent.files.cardwidget"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="android.appcard.action.APPCARD_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.card.provider.array"
                android:resource="@array/recent_appcard_array" />

            <meta-data
                android:name="android.card.provider1"
                android:resource="@xml/recent_files_app_card_2_to_2" />
            <meta-data
                android:name="android.card.provider2"
                android:resource="@xml/recent_files_app_card_2_to_4" />
            <meta-data
                android:name="android.card.provider3"
                android:resource="@xml/recent_files_app_card_4_to_4" />
        </provider>
        -->

        <activity
            android:name=".activity.CardWidgetOpenFileActivity"
            android:excludeFromRecents="true"
            android:exported="true"
            android:label=""
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            android:launchMode="singleInstance">
            <intent-filter>
                <action android:name="com.oplus.filemanager.cardwidget.open.file" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".activity.OpenDeeplinkActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize|keyboard|navigation"
            android:exported="true"
            android:permission="com.oplus.permission.safe.PROTECT"
            android:theme="@style/AppNoTitleThemeTranslucent"
            android:windowSoftInputMode="adjustPan"
            android:autoRemoveFromRecents="true"
            android:excludeFromRecents="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- filemanager://deeplink.remotedevice.download
                跳转到DownloadActivity
                参数另外传递-->
                <data
                    android:scheme="filemanager"
                    android:host="deeplink.remotedevice.download"
                    />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- filemanager://deeplink.filebrowser.open
                    跳转到FileBrowserActivity
                    参数另外传递,-->
                <data
                    android:scheme="filemanager"
                    android:host="deeplink.filebrowser"
                    />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- filemanager://deeplink.super.remotepc
                    跳转到远程电脑文件 SuperActivity
                    参数另外传递,-->
                <data
                    android:scheme="filemanager"
                    android:host="deeplink.super.remotepc"
                    />
            </intent-filter>

        </activity>

        <provider
            android:name="com.oplus.filemanager.cardwidget.provider.RecentCardImageFileProvider"
            android:authorities="com.oplus.filemanager.recent.cardwidget.image"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path" />
            <meta-data
                android:name="color.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path" />
        </provider>

        <provider
            android:name="com.oplus.filemanager.cardwidget.label.LabelCardWidgetProvider"
            android:authorities="com.oplus.filemanager.label.files.cardwidget"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="android.appcard.action.APPCARD_UPDATE" />
            </intent-filter>

            <meta-data
                android:name="android.card.provider"
                android:resource="@xml/label_files_app_card_2_to_4" />
        </provider>

        <activity
            android:name="com.oplus.filemanager.cardwidget.label.LabelCardSettingActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:screenOrientation="behind"
            android:launchMode="singleInstance"
            android:theme="@style/AppNoTitleThemeTranslucent">
            <intent-filter>
                <action android:name="com.oplus.filemanager.card.label.settings" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".recent.setting.RecentCardSettingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
            android:exported="true"
            android:launchMode="singleInstance"
            android:permission="com.oplus.permission.safe.ASSISTANT"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleThemeTranslucent2">
            <intent-filter>
                <action android:name="com.oplus.filemanager.card.recent.settings" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <provider
            android:name=".recent.RecentMiddleCardWidgetProvider"
            android:authorities="com.oplus.filemanager.recent.middlecard"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="com.oplus.seedling.action.SEEDLING_CARD" />
            </intent-filter>
            <!--             注意：如果upk存在App的assets目录中，需要按照以下格式，配置meta-data标签, assets目录下UPK文件路径，数组，多个用;分割 -->
            <meta-data
                android:name="oplus.seedling.provider"
                android:value="FileManagerRecentCard-release-1.0.25-20241012.upk" />
        </provider>

        <provider
            android:name="com.oplus.filemanager.cardwidget.newfiles.NewFilesSeedlingCardWidgetProvider"
            android:authorities="com.oplus.filemanager.newfiles"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="com.oplus.seedling.action.SEEDLING_CARD" />
            </intent-filter>
        </provider>
        <provider
            android:name=".download.DownloadSeedlingCardProvider"
            android:authorities="com.oplus.filemanager.transfer.download"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="com.oplus.seedling.action.SEEDLING_CARD" />
            </intent-filter>
            <!--             注意：如果upk存在App的assets目录中，需要按照以下格式，配置meta-data标签, assets目录下UPK文件路径，数组，多个用;分割 -->
            <meta-data
                android:name="oplus.seedling.provider"
                android:value="TransferSeedling-test-1.0.1-20250108.upk" />
        </provider>
        <provider
            android:name="com.oplus.filemanager.provider.FilePathProvider"
            android:authorities="com.coloros.filemanager"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_path" />
        </provider>
    </application>
</manifest>