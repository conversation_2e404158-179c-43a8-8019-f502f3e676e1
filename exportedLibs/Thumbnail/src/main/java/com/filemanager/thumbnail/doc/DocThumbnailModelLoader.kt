/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.doc
 ** Version: 1.0
 ** Date: 2021/5/7
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.doc

import android.content.Context
import android.graphics.Bitmap
import com.bumptech.glide.load.Options
import com.bumptech.glide.load.model.ModelLoader
import com.bumptech.glide.signature.ObjectKey

/**
 * the model loader for doc to load InputStream from original file source
 */
internal class DocThumbnailModelLoader(
    private val context: Context
) : ModelLoader<DocThumbnail, Bitmap> {

    override fun buildLoadData(
        model: DocThumbnail,
        width: Int,
        height: Int,
        options: Options
    ): ModelLoader.LoadData<Bitmap> = ModelLoader.LoadData(
        ObjectKey(model),
        DocThumbnailFetcher(context, model, width, height)
    )

    override fun handles(model: DocThumbnail): Boolean = true
}