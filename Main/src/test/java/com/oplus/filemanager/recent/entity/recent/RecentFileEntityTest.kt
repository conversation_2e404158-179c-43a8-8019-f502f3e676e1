package com.oplus.filemanager.recent.entity.recent

import com.oplus.filemanager.room.model.RecentFilesEntity
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.apache.commons.io.FilenameUtils
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * RecentFileEntity 的单元测试类
 * 用于测试 RecentFileEntity 类的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class RecentFileEntityTest {

    private lateinit var recentFileEntity: RecentFileEntity

    /**
     * 在每个测试方法执行前初始化测试环境
     */
    @Before
    fun setUp() {
        recentFileEntity = RecentFileEntity()
    }

    /**
     * 在每个测试方法执行后清理测试环境
     */
    @After
    fun tearDown() {
        unmockkAll() // 清理所有mock对象
    }

    /**
     * 测试默认构造函数
     * 验证默认构造函数初始化的各个属性是否符合预期
     */
    @Test
    fun testDefaultConstructor() {
        assertEquals("", recentFileEntity.mAbsolutePath)
        assertEquals("", recentFileEntity.mRelativePath)
        assertEquals("", recentFileEntity.mVolumeName)
        assertEquals(0, recentFileEntity.mMediaType)
        assertEquals(0L, recentFileEntity.mLastModified)
        assertNull(recentFileEntity.mId)
        assertEquals(0, recentFileEntity.mType)
        assertEquals("", recentFileEntity.mParentDate)
        assertTrue(recentFileEntity.mIsParentExpand)
        assertEquals(RecentFileEntity.ACTION_ADD, recentFileEntity.mDbActionType)
        assertFalse(recentFileEntity.mIsFiltered)
        assertEquals(0, recentFileEntity.mOrientation)
        assertEquals(0L, recentFileEntity.mDuration)
    }

    /**
     * 测试视频类型文件的构造函数
     * 验证视频类型文件的属性设置是否正确
     */
    @Test
    fun testVideoTypeConstructor() {
        val entity = RecentFileEntity(
            "relative/path",
            "volume1",
            1234567890L,
            1024L,
            "test.mp4",
            "/absolute/path/test.mp4",
            90,
            FileMediaHelper.MEDIA_TYPE_VIDEO,
            10000L
        )

        assertEquals(MimeTypeHelper.VIDEO_TYPE, entity.mType)
        assertEquals(10000L, entity.mDuration)
        assertEquals(10000L, entity.mMediaDuration)
    }

    /**
     * 测试音频类型文件的构造函数
     * 验证音频类型文件的属性设置是否正确
     */
    @Test
    fun testAudioTypeConstructor() {
        val entity = RecentFileEntity(
            "relative/path",
            "volume1",
            1234567890L,
            1024L,
            "test.mp3",
            "/absolute/path/test.mp3",
            0,
            FileMediaHelper.MEDIA_TYPE_AUDIO
        )

        assertEquals(MimeTypeHelper.AUDIO_TYPE, entity.mType)
    }

    /**
     * 测试从 RecentFilesEntity 构造 RecentFileEntity
     * 验证从数据库实体类转换后的属性是否正确
     */
    @Test
    fun testRecentFilesEntityConstructor() {
        val recentFilesEntity = RecentFilesEntity(1L).apply {
            mAbsolutePath = "/test/path"
            mVolumeName = "testVolume"
            mRelativePath = "relative/path"
            mLastModified = 1234567890L
            mSize = 1024L
            mDisplayName = "test.jpg"
            mType = MimeTypeHelper.IMAGE_TYPE
            mAnotherName = "anotherName"
            mParentDate = "2023-01-01"
        }

        val entity = RecentFileEntity(recentFilesEntity)

        assertEquals(1L, entity.mId)
        assertEquals("/test/path", entity.mAbsolutePath)
        assertEquals("testVolume", entity.mVolumeName)
        assertEquals("relative/path", entity.mRelativePath)
        assertEquals(1234567890L, entity.mLastModified)
        assertEquals(1024L, entity.mSize)
        assertEquals("test.jpg", entity.mDisplayName)
        assertEquals(MimeTypeHelper.IMAGE_TYPE, entity.mType)
        assertEquals("anotherName", entity.mAnotherName)
        assertEquals("2023-01-01", entity.mParentDate)
    }

    /**
     * 测试 setSuperProperties 方法
     * 验证设置父类属性的逻辑是否正确
     */
    @Test
    fun testSetSuperProperties() {
        recentFileEntity.mAbsolutePath = "/test/path"
        recentFileEntity.mLastModified = 1234567890L
        recentFileEntity.mType = MimeTypeHelper.IMAGE_TYPE

        assertEquals("/test/path", recentFileEntity.mData)
        assertEquals(MimeTypeHelper.IMAGE_TYPE, recentFileEntity.mLocalType)
        assertFalse(recentFileEntity.mIsDirectory)
        assertEquals(1234567890L * 1000, recentFileEntity.mDateModified)
    }

    /**
     * 测试 equals 方法
     * 验证两个 RecentFileEntity 对象的相等性判断逻辑
     */
    @Test
    fun testEquals() {
        val entity1 = RecentFileEntity().apply {
            mAbsolutePath = "/path1"
            mRelativePath = "relative1"
            mVolumeName = "volume1"
            mMediaType = FileMediaHelper.MEDIA_TYPE_IMAGE
            mLastModified = 1234567890L
            mId = 1L
            mType = MimeTypeHelper.IMAGE_TYPE
            mAnotherName = "name1"
            mDuration = 1000L
            mParentDate = "2023-01-01"
            mIsParentExpand = true
        }

        val entity2 = RecentFileEntity().apply {
            mAbsolutePath = "/path1"
            mRelativePath = "relative1"
            mVolumeName = "volume1"
            mMediaType = FileMediaHelper.MEDIA_TYPE_IMAGE
            mLastModified = 1234567890L
            mId = 1L
            mType = MimeTypeHelper.IMAGE_TYPE
            mAnotherName = "name1"
            mDuration = 1000L
            mParentDate = "2023-01-01"
            mIsParentExpand = true
        }

        val entity3 = RecentFileEntity().apply {
            mAbsolutePath = "/path2"
        }

        assertTrue(entity1.equals(entity2))
        assertFalse(entity1.equals(entity3))
        assertFalse(entity1.equals(null))
        assertFalse(entity1.equals("string"))
    }

    /**
     * 测试 toString 方法
     * 验证对象的字符串表示是否符合预期格式
     */
    @Test
    fun testToString() {
        recentFileEntity.apply {
            mAbsolutePath = "/test/path"
            mVolumeName = "testVolume"
            mRelativePath = "relative/path"
            mLastModified = 1234567890L
            mSize = 1024L
            mDisplayName = "test.jpg"
        }

        val expected = "mAbsolutePath =/test/path;" +
                "mVolumeName =testVolume;" +
                "mRelativePath =testVolume;" +
                "mLastModified =1234567890;" +
                "mSize =1024;" +
                "mDisplayName =testVolume"

        assertEquals(expected, recentFileEntity.toString())
    }
}