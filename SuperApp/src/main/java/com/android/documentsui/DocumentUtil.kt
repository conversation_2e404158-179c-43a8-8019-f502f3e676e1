/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DocumentUtil.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/9
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  hank.zhou      2022/9/9      1.0        create
 ***********************************************************************/
package com.android.documentsui

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.os.Environment
import android.os.StatFs
import android.provider.DocumentsContract
import com.android.documentsui.base.DocumentInfo
import com.android.documentsui.base.DocumentStack
import com.android.documentsui.base.RootInfo
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.PKG_NAME_DOCUMENTS_UI_MAINLINE
import com.filemanager.common.constants.KtConstants.PKG_NAME_DOCUMENTS_UI_NOT_MAINLINE
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NewFunctionSwitch.SUPPORT_PRIVATE_DIRECTORY_ENTRY_PROMPT
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.view.GuideDocumentsUIView
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import java.io.File

object DocumentUtil {
    const val TAG = "DocumentUtil"
    const val AUTHORITY = "com.android.externalstorage.documents"
    const val PACKAGE_NAME = "com.google.android.documentsui"
    const val ROOT_ID = "primary"
    const val DOCUMENT_ID = "primary:"
    const val EXTRA_STACK = "com.android.documentsui.STACK"
    /*1图片 2视频 345音频 6文件*/
    val ALL_MIMETYPES = arrayOf(
        "image/*",
        "video/*",
        "audio/*",
        "application/ogg",
        "application/x-flac",
        "application/epub+zip"
    )

    @JvmStatic
    fun getDocumentStack(path: String): DocumentStack {
        val rootPath =  Environment.getExternalStorageDirectory().absolutePath
        val info = RootInfo()
        info.rootId = ROOT_ID
        info.authority = AUTHORITY
        info.documentId = DOCUMENT_ID
        info.availableBytes = getAvailableSize(rootPath)
        if (SUPPORT_PRIVATE_DIRECTORY_ENTRY_PROMPT) {
            // 支持类别刷选
            info.derivedMimeTypes = ALL_MIMETYPES
            info.queryArgs = "android:query-arg-mime-types"
            info.flags = DocumentsContract.Root.FLAG_SUPPORTS_SEARCH
        }

        val drinfo = DocumentInfo()
        drinfo.authority = AUTHORITY
        drinfo.documentId = DOCUMENT_ID
        if (path.startsWith(rootPath)) {
            val childPath = path.replace(rootPath, "")
            if (childPath.isNotEmpty()) {
                val infoList = ArrayList<DocumentInfo>()
                var childContent = ""
                val childNames = childPath.split(File.separator)
                infoList.add(drinfo)
                for (name in childNames) {
                    if (name.isNotEmpty()) {
                        val dinfo = DocumentInfo()
                        dinfo.authority = AUTHORITY
                        dinfo.displayName = name
                        dinfo.documentId = DOCUMENT_ID + childContent + name
                        childContent = childContent + name + File.separator
                        infoList.add(dinfo)
                    }
                }
                return DocumentStack(info, infoList)
            } else {
                return DocumentStack(info, drinfo)
            }
        } else {
            return DocumentStack(info, drinfo)
        }
    }

    @JvmStatic
    fun startDocumentsUIIntent(context: Context, intent: Intent) {
        val isMainlineProject = isMainlineProject()
        try {
            /** only domestic from android T will exit mainline project, documentsUI package name will be com.android.documentsui
             other rom version still in mainline project, documentsUI package name will be com.google.android.documentsui */
            if (isMainlineProject) {
                intent.setPackage(PKG_NAME_DOCUMENTS_UI_MAINLINE)
                CollectPrivacyUtils.collectInstalledAppList(PKG_NAME_DOCUMENTS_UI_MAINLINE)
            } else {
                intent.setPackage(PKG_NAME_DOCUMENTS_UI_NOT_MAINLINE)
                CollectPrivacyUtils.collectInstalledAppList(PKG_NAME_DOCUMENTS_UI_NOT_MAINLINE)
            }
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            Log.e(TAG, e.message)
            if (isMainlineProject.not()) {
                intent.setPackage(PKG_NAME_DOCUMENTS_UI_MAINLINE)
            } else {
                intent.setPackage(PKG_NAME_DOCUMENTS_UI_NOT_MAINLINE)
            }
            try {
                context.startActivity(intent)
            } catch (e: ActivityNotFoundException) {
                Log.e(TAG, e.message)
            }
        }
    }

    @JvmStatic
    fun getIntent(path: String): Intent {
        Log.d(TAG, "path $path")
        val rootUri = DocumentsContract.buildRootUri(AUTHORITY, ROOT_ID)
        val intent = Intent(Intent.ACTION_VIEW)
        intent.addCategory(Intent.CATEGORY_DEFAULT)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        intent.setDataAndType(rootUri, DocumentsContract.Root.MIME_TYPE_ITEM)
        intent.putExtra(EXTRA_STACK, getDocumentStack(path))
        if (SUPPORT_PRIVATE_DIRECTORY_ENTRY_PROMPT) {
            intent.putExtra(Intent.EXTRA_MIME_TYPES, ALL_MIMETYPES)
        }
        return intent
    }

    @JvmStatic
    private fun getAvailableSize(path: String?): Long {
        try {
            val stat = StatFs(path)
            val blockSize = stat.blockSizeLong
            val availableBlocks = stat.availableBlocksLong
            return blockSize * availableBlocks
        } catch (e: IllegalArgumentException) {
            Log.d(TAG, "getAvailableSize e = $e")
        }
        return 0
    }

    @JvmStatic
    private fun isMainlineProject(): Boolean {
        val isAtLeastAndroidT = SdkUtils.isAtLeastT()
        val region = AppUtils.getBuildConfigValue(GuideDocumentsUIView.FLAVOR_REGION)
        return !(isAtLeastAndroidT && GuideDocumentsUIView.DOMESTIC == region)
    }
}