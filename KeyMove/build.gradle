plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace "com.oplus.filemanager.keymove"

    sourceSets {
        main {
            res.srcDirs += ['res']
        }
    }
}

dependencies {
    implementation libs.androidx.appcompat
    implementation libs.androidx.fragment.ktx

    implementation libs.oplus.appcompat.core
    implementation libs.oplus.appcompat.rotateview
    implementation libs.oplus.appcompat.recyclerview
    implementation libs.oplus.appcompat.dialog
    implementation libs.oplus.appcompat.poplist
    implementation libs.oplus.appcompat.progressbar
    implementation libs.oplus.appcompat.toolbar
    implementation libs.oplus.appcompat.panel
    implementation libs.oplus.appcompat.expandable

    implementation project(':Common')
    implementation project(':Encrypt')
    implementation project(':FileOperate')
    implementation project(':SelectDir')
    implementation project(':LabelManager')
    implementation project(':FileBrowser')
    implementation project(':CategoryDocument')

    if (prop_use_prebuilt_drap_drop_lib.toBoolean()) {
        implementation libs.oplus.filemanager.dragDrop
    } else {
        implementation project(':exportedLibs:DragDropSelection')
    }
}