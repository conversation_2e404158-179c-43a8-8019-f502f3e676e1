/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PersonalPrivacyCategory
 * * Description: 个人信息明示清单详情分类
 * * Version: 1.0
 * * Date : 2024/08/23
 * * Author:W9060445
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     W9060445         2024/08/23      1.0            create
 ****************************************************************/
package com.filemanager.setting.utils

import com.oplus.filemanager.interfaze.privacy.ICollectPrivacyApi

object PersonalPrivacyCategory {

    /**
     * 判断单位是个
     */
    fun isCountUnitPiece(category: String): <PERSON><PERSON><PERSON> {
        return (category == ICollectPrivacyApi.AUDIO_DATA) || (category == ICollectPrivacyApi.VIDEO_DATA)
                || (category == ICollectPrivacyApi.DOCUMENT_DATA) || (category == ICollectPrivacyApi.COMPRESS_DATA)
                || (category == ICollectPrivacyApi.APK_DATA)
    }

    /**
     * 判断单位是次
     */
    fun isCountUnitTimes(category: String): Boolean {
        return (category == ICollectPrivacyApi.DUID) || (category == ICollectPrivacyApi.DEVICE_MODEL)
                || (category == ICollectPrivacyApi.DEVICE_BRAND) || (category == ICollectPrivacyApi.OS_VERSION)
                || (category == ICollectPrivacyApi.CONFIG_INFO) || (category == ICollectPrivacyApi.SIM_CARD_INFO)
                || (category == ICollectPrivacyApi.IMSI_INFO) || (category == ICollectPrivacyApi.IMEI)
                || (category == ICollectPrivacyApi.SN_CODE) || (category == ICollectPrivacyApi.OPEN_ID)
                || (category == ICollectPrivacyApi.ANDROID_ID) || (category == ICollectPrivacyApi.IDFA)
                || (category == ICollectPrivacyApi.APP_USAGE_RECORDS) || (category == ICollectPrivacyApi.IP_ADDRESS)
                || (category == ICollectPrivacyApi.INSTALLED_APP_LIST) || (category == ICollectPrivacyApi.APP_SIGN)
                || (category == ICollectPrivacyApi.TELECOM_OPERATOR) || (category == ICollectPrivacyApi.NETWORK_ENVIRONMENT)
    }

    /**
     * 判断单位是条
     */
    fun isCountUnitItem(category: String): Boolean {
        return (category == ICollectPrivacyApi.PHONE_NUMBER)
                || (category == ICollectPrivacyApi.EMAIL_ADDRESS) || (category == ICollectPrivacyApi.ERROR_LOG_REPORT)
                || (category == ICollectPrivacyApi.BURIED_POINT_INFO) || (category == ICollectPrivacyApi.FEEDBACK_CONTENT_ATTACHMENT)
    }

    /**
     * 判断单位是张
     */
    fun isCountUnitLeaf(category: String): Boolean {
        return (category == ICollectPrivacyApi.IMAGE_DATA)
    }

    /**
     * 判断内容是否为加密
     * @param category
     */
    fun isEncryptContent(category: String): Boolean {
        return category == ICollectPrivacyApi.PHONE_NUMBER
                || category == ICollectPrivacyApi.EMAIL_ADDRESS || category == ICollectPrivacyApi.DUID
                || category == ICollectPrivacyApi.DEVICE_MODEL || category == ICollectPrivacyApi.DEVICE_BRAND
                || category == ICollectPrivacyApi.OS_VERSION || category == ICollectPrivacyApi.CONFIG_INFO
                || category == ICollectPrivacyApi.SIM_CARD_INFO || category == ICollectPrivacyApi.IMSI_INFO
                || category == ICollectPrivacyApi.IMEI || category == ICollectPrivacyApi.SN_CODE
                || category == ICollectPrivacyApi.OPEN_ID || category == ICollectPrivacyApi.ANDROID_ID
                || category == ICollectPrivacyApi.IDFA || category == ICollectPrivacyApi.APP_USAGE_RECORDS
                || category == ICollectPrivacyApi.IP_ADDRESS || category == ICollectPrivacyApi.APP_SIGN
                || category == ICollectPrivacyApi.SOFT_PKG || category == ICollectPrivacyApi.TELECOM_OPERATOR
                || category == ICollectPrivacyApi.NETWORK_ENVIRONMENT
    }

    /**
     * 判断是否有实际内容
     */
    fun hasActualContent(category: String): Boolean {
        return category == ICollectPrivacyApi.PHONE_NUMBER
                || category == ICollectPrivacyApi.EMAIL_ADDRESS || category == ICollectPrivacyApi.DUID
                || category == ICollectPrivacyApi.DEVICE_MODEL || category == ICollectPrivacyApi.DEVICE_BRAND
                || category == ICollectPrivacyApi.OS_VERSION || category == ICollectPrivacyApi.CONFIG_INFO
                || category == ICollectPrivacyApi.SIM_CARD_INFO || category == ICollectPrivacyApi.IMSI_INFO
                || category == ICollectPrivacyApi.IMEI || category == ICollectPrivacyApi.SN_CODE
                || category == ICollectPrivacyApi.OPEN_ID || category == ICollectPrivacyApi.ANDROID_ID
                || category == ICollectPrivacyApi.IDFA || category == ICollectPrivacyApi.APP_USAGE_RECORDS
                || category == ICollectPrivacyApi.IP_ADDRESS || category == ICollectPrivacyApi.APP_SIGN
                || category == ICollectPrivacyApi.SOFT_PKG || category == ICollectPrivacyApi.TELECOM_OPERATOR
                || category == ICollectPrivacyApi.NETWORK_ENVIRONMENT
    }
}