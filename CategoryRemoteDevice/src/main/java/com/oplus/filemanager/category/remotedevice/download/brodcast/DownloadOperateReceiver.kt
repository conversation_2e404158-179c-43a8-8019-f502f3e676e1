/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DownloadOperateReceiver
 * * Description : DownloadOperate Receiver
 * * Version     : 1.0
 * * Date        : 2024/12/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download.brodcast

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.filemanager.common.utils.IntentUtils
import com.oplus.filemanager.interfaze.remotedevice.DownloadRemoteFileCallback

class DownloadOperateReceiver(private var callback: DownloadRemoteFileCallback?) : BroadcastReceiver() {

    companion object {
        const val ACTION_DOWNLOADING_PROGRESS = "action_downloading"
        const val ACTION_DOWNLOAD_OPERATE = "action_download_operate"
        const val PROGRESS = "progress"
        const val PATH = "path"
        const val RESULT_CODE = "result_code"
        const val OPERATE_CODE = "operate_code"

        fun sendEachFileDownloadingBroadcast(context: Context, path: String, progress: Float) {
            val intent = Intent()
            intent.action = ACTION_DOWNLOADING_PROGRESS
            intent.putExtra(PATH, path)
            intent.putExtra(PROGRESS, progress)
            sendLocalBroadcast(context, intent)
        }

        fun sendAllFileDownloadingBroadcast(context: Context, progress: Int) {
            val intent = Intent()
            intent.action = ACTION_DOWNLOADING_PROGRESS
            intent.putExtra(PROGRESS, progress)
            sendLocalBroadcast(context, intent)
        }

        fun setOperateBroadcast(context: Context, operateCode: Int, resultCode: Int) {
            val intent = Intent()
            intent.action = ACTION_DOWNLOAD_OPERATE
            intent.putExtra(RESULT_CODE, resultCode)
            intent.putExtra(OPERATE_CODE, operateCode)
            sendLocalBroadcast(context, intent)
        }


        private fun sendLocalBroadcast(context: Context, intent: Intent) {
            LocalBroadcastManager.getInstance(context).sendBroadcast(intent)
        }
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        val action = intent?.action ?: return
        when (action) {
            ACTION_DOWNLOADING_PROGRESS -> {
                val progress = IntentUtils.getInt(intent, PROGRESS, 0)
                callback?.onProgress(progress)
            }

            ACTION_DOWNLOAD_OPERATE -> {
                val operateCode = IntentUtils.getInt(intent, OPERATE_CODE, 0)
                val resultCode = IntentUtils.getInt(intent, RESULT_CODE, 0)
                callback?.onResult(operateCode, resultCode)
            }
        }
    }
}