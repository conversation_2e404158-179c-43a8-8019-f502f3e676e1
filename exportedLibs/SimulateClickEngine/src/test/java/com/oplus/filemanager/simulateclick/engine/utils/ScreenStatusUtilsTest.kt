/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScreenStatusUtilsTest.kt
 * Description:
 * The test cases for ScreenStatusUtils
 *
 * Version: 1.0
 * Date: 2024-06-06
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-06-06   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.utils

import android.content.Context
import android.hardware.display.DisplayManager
import android.view.Display
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.Assert
import org.junit.Test

class ScreenStatusUtilsTest : Assert() {

    @Test
    fun `should always false when isUnfold if is flip device`() {
        // SetUp
        mockkStatic(ScreenStatusUtils::class)

        // Given
        every { ScreenStatusUtils.isFlipDevice(any()) } returns true
        every { ScreenStatusUtils.getFoldState(any()) } returns ScreenStatusUtils.FOLD_OPEN

        // When
        val result = ScreenStatusUtils.isUnfold(mockk())

        // Then
        assertFalse(result)

        // TearDown
        unmockkStatic(ScreenStatusUtils::class)
    }

    @Test
    fun `should false when isUnfold if fold state is not open`() {
        // SetUp
        mockkStatic(ScreenStatusUtils::class)

        // Given
        every { ScreenStatusUtils.isFlipDevice(any()) } returns false
        every { ScreenStatusUtils.getFoldState(any()) } returns ScreenStatusUtils.FOLD_CLOSE

        // When
        val result = ScreenStatusUtils.isUnfold(mockk())

        // Then
        assertFalse(result)

        // TearDown
        unmockkStatic(ScreenStatusUtils::class)
    }

    @Test
    fun `should true when isUnfold if fold state is open`() {
        // SetUp
        mockkStatic(ScreenStatusUtils::class)

        // Given
        every { ScreenStatusUtils.isFlipDevice(any()) } returns false
        every { ScreenStatusUtils.getFoldState(any()) } returns ScreenStatusUtils.FOLD_OPEN

        // When
        val result = ScreenStatusUtils.isUnfold(mockk())

        // Then
        assertTrue(result)

        // TearDown
        unmockkStatic(ScreenStatusUtils::class)
    }

    @Test
    fun `should true when isFocusSecondaryDisplay if secondary screen on`() {
        // SetUp
        mockkStatic(SdkUtils::class, ScreenStatusUtils::class)

        // Given
        every { SdkUtils.isAtLeastT() } returns true
        every { ScreenStatusUtils.isFlipDevice(any()) } returns true
        val defaultDisplay = mockk<Display> {
            every { name } returns "deviceName"
            every { displayId } returns Display.DEFAULT_DISPLAY
            every { state } returns Display.STATE_OFF
        }
        val secondaryDisplay = mockk<Display> {
            every { name } returns "deviceName"
            every { displayId } returns 1
        }
        val context = mockk<Context> {
            every { getSystemService(DisplayManager::class.java) } returns mockk {
                every { getDisplay(Display.DEFAULT_DISPLAY) } returns defaultDisplay
                every {
                    getDisplays("android.hardware.display.category.ALL_INCLUDING_DISABLED")
                } returns arrayOf(defaultDisplay, secondaryDisplay)
            }
        }

        // When
        val result = ScreenStatusUtils.isFocusSecondaryDisplay(context)

        // Then
        assertTrue(result)

        // TearDown
        unmockkStatic(SdkUtils::class, ScreenStatusUtils::class)
    }

    @Test
    fun `should false when isFocusSecondaryDisplay if default screen on`() {
        // SetUp
        mockkStatic(SdkUtils::class, ScreenStatusUtils::class)

        // Given
        every { SdkUtils.isAtLeastT() } returns true
        every { ScreenStatusUtils.isFlipDevice(any()) } returns true
        val defaultDisplay = mockk<Display> {
            every { name } returns "deviceName"
            every { displayId } returns Display.DEFAULT_DISPLAY
            every { state } returns Display.STATE_ON
        }
        val secondaryDisplay = mockk<Display> {
            every { name } returns "deviceName"
            every { displayId } returns 1
        }
        val context = mockk<Context> {
            every { getSystemService(DisplayManager::class.java) } returns mockk {
                every { getDisplay(Display.DEFAULT_DISPLAY) } returns defaultDisplay
                every {
                    getDisplays("android.hardware.display.category.ALL_INCLUDING_DISABLED")
                } returns arrayOf(defaultDisplay, secondaryDisplay)
            }
        }

        // When
        val result = ScreenStatusUtils.isFocusSecondaryDisplay(context)

        // Then
        assertFalse(result)

        // TearDown
        unmockkStatic(SdkUtils::class, ScreenStatusUtils::class)
    }

    @Test
    fun `should false when isFocusSecondaryDisplay if not flip device`() {
        // SetUp
        mockkStatic(SdkUtils::class, ScreenStatusUtils::class)

        // Given
        every { SdkUtils.isAtLeastT() } returns true
        every { ScreenStatusUtils.isFlipDevice(any()) } returns false

        // When
        val result = ScreenStatusUtils.isFocusSecondaryDisplay(mockk())

        // Then
        assertFalse(result)

        // TearDown
        unmockkStatic(SdkUtils::class, ScreenStatusUtils::class)
    }
}