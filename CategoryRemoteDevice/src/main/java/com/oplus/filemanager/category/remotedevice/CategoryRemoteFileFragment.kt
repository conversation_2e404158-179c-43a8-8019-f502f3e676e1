/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : CategoryRemoteFileFragment
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice

import android.annotation.SuppressLint
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.view.DragEvent
import android.text.InputType
import android.view.Gravity
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.view.ViewStub
import android.view.WindowManager
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.annotation.IdRes
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.doOnLayout
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.edittext.COUICardSingleInputView
import com.coui.appcompat.edittext.COUIInputView
import com.coui.appcompat.input.COUIInputListSelectedItemLayout
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.responsiveui.config.UIConfig
import com.filemanager.common.MyApplication
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseFolderAnimAdapter
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.bean.remotedevice.Constants
import com.filemanager.common.bean.remotedevice.Constants.ERROR_CODE_NO_ROOTPATH_FOUND
import com.filemanager.common.bean.remotedevice.RemoteDeviceInfo
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.constants.RemoteDeviceConstants
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.dialog.RemoteDeviceDialogUtils
import com.filemanager.common.dragselection.DefaultSelectDelegate
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.MacDragUtil.MacDragObject.isDraggingFromMac
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewOperate
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.IRefreshFragmentDataForDir
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.OnGetRemoteDeviceInfoListener
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.EmojiUtils
import com.filemanager.common.utils.FileEmptyUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.SystemBarUtils
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.isNetworkAvailable
import com.filemanager.common.view.BrowserPathBar
import com.filemanager.common.view.FileManagerRecyclerView
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.dropdrag.SelectionTracker
import com.oplus.dropdrag.base.DefaultDetailsLookup
import com.oplus.dropdrag.base.DefaultKeyProvider
import com.oplus.dropdrag.recycleview.SelectionPredicates
import com.oplus.filemanager.category.remotedevice.adapter.RemoteFileAdapter
import com.oplus.filemanager.category.remotedevice.download.DownloadViewModel
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDeviceStatusListener
import com.oplus.filemanager.interfaze.remotedevice.IRemoteFileLoadCallback
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.remotedevice.RemoteDeviceManager
import com.filemanager.common.dialog.RemoteDialogHelper
import com.filemanager.common.helper.getBottomAlertDialogWindowAnimStyle
import com.filemanager.common.managers.SPManagerUtil.getValue
import com.filemanager.common.managers.SPManagerUtil.putValue
import com.filemanager.common.utils.CustomToast
import com.oplus.filemanager.interfaze.touchshare.TouchShareNotSupportSupplier
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

class CategoryRemoteFileFragment : RecyclerSelectionVMFragment<CategoryRemoteFileViewModel>(),
    OnBackPressed, NavigationBarView.OnItemSelectedListener, IPreviewListFragment,
    OnGetRemoteDeviceInfoListener, IRefreshFragmentDataForDir {

    companion object {
        private const val TAG = "CategoryRemoteFileFragment"
        private const val FILE_BROWSER_FOLDER_ANIM_TIME = 100L
        private const val NOTIFY_CHANGED_DELAY = 300L
        private const val REFRESH_DEVICE_STATUS_DELAY = 500L
        private const val LOADING_DELAY_TIME = 500L
        private const val USERS_PATH_ITEM_SIZE = 2
        private const val REMOTE_EMPTY_PASSED_STATUS_KEY = "-REMOTE_EMPTY_PASSED_STATUS"

        private const val ACTION_TO_NETWORK_SETTINGS = "android.settings.WIRELESS_SETTINGS"
        private const val MOCK_PASSWD = "********"
        private const val EMPTY_PASSWD = ""
        private const val NUM_THREE = 3
        private const val NUM_EIGHT = 8
        private const val TOP_ROUNDED_CORNER = 1
        private const val BOTTOM_ROUNDED_CORNER = 3
    }

    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    private var mToolbar: COUIToolbar? = null
    private var sortEntryView: SortEntryView? = null
    private var mTitle: String? = null
    private var mCurrentPath: String? = null
    private var mAdapter: RemoteFileAdapter? = null
    private var mLayoutManager: FileGridLayoutManager? = null
    private var mPathBar: BrowserPathBar? = null

    private var deviceConnectGuideVS: ViewStub? = null
    private var deviceConnectGuideLayout: View? = null
    private var deviceConnectIcon: ImageView? = null
    private var deviceConnectStatusTv: TextView? = null
    private var deviceConnectDesTv: TextView? = null
    private var deviceConnectGuideTv: TextView? = null
    private var refreshButton: COUIButton? = null
    private var connectButton: COUIButton? = null
    private var emptyContainer: FrameLayout? = null
    private var accountInputBoxContainer: ConstraintLayout? = null
    private var deviceCodeInputView: COUICardSingleInputView? = null
    private var devicePasswdInputView: COUICardSingleInputView? = null
    private var devicePasswdItem: View? = null
    private var mBottomAlertDialogBuilder: COUIAlertDialogBuilder? = null
    private var mBottomAlertDialog: AlertDialog? = null
    private var editPasswdIcon: View? = null
    private var isChangePasswd: Boolean = false
    private var isEmptyPasswd: Boolean = false

    private val mFolderTransformAnimator by lazy { FolderTransformAnimator() }
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val mSortPopupController by lazy { SortPopupController(lifecycle) }
    private var mFileOperateController: NormalFileOperateController? = null
    private var mLoadingController: LoadingController? = null
    private val mSpacesItemDecoration by lazy {
        ItemDecorationFactory.getGridItemDecoration(ItemDecorationFactory.GRID_ITEM_DECORATION_FILE_BROWSER)
    }
    private var isFromDetail = false
    private var mNeedLoadData = false
    private var isChildDisplay = false
    private var hasShowEmpty: Boolean = false
    private var deviceId: String? = null
    private var deviceName: String? = null
    private var deviceStatus: Int = RemoteDeviceConstants.UNDISCOVERED
    private var deviceIsSameAccount: Int = RemoteDeviceConstants.UN_SAME_ACCOUNT
    private var remoteDeviceStatusListener: IRemoteDeviceStatusListener? = null
    private var scrollHelper: DragScrollHelper? = null
    private val colorPrimary by lazy { COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelPrimary) }
    private val colorSecondary by lazy { COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelSecondary) }
    private val colorTertiary by lazy { COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelTertiary) }
    private var remoteDialogHelper: RemoteDialogHelper? = null
    private val connectButtonListener = OnClickListener {
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        val currentConnectedDevice = remoteDeviceApi?.getCurrentLinkedRemoteDiceInfo()
        val passwd = if (isChangePasswd) {
            devicePasswdInputView?.editText?.text?.toString() ?: ""
        } else ""
        val otherDeviceConnecting = RemoteDeviceManager.hasOtherConnectingDevice()
        if (otherDeviceConnecting) {
            remoteDialogHelper?.showConnectionFailDialog(RemoteDeviceDialogUtils.PHONE_CONNECTING_TO_ANOTHER)
            return@OnClickListener
        }
        if (currentConnectedDevice == null) {
            onDeviceStatusChanged(RemoteDeviceConstants.CONNECTING)
            connectDevice(passwd)
        } else {
            remoteDialogHelper?.showConnectionFailDialog(RemoteDeviceDialogUtils.PHONE_CONNECTED_TO_ANOTHER)
        }
    }
    /**
    * 远程控制电脑版本是否支持文管发起连接
    */
    val isSupportConnectFun by lazy { RemoteDeviceManager.getSupportConnect() }
    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }

    private var previewOperate: IPreviewOperate? = null
    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? =
        null
    private var rootWindowInsetsListener: View.OnApplyWindowInsetsListener? = null

    override fun getLayoutResId(): Int {
        return R.layout.remote_file_list_fragment
    }

    private fun getEmptyPasswdKey(deviceId: String?): String {
        return "$deviceId$REMOTE_EMPTY_PASSED_STATUS_KEY"
    }
    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            val bundle = arguments ?: return
            mCurrentPath = bundle.getString(KtConstants.P_CURRENT_PATH)
            mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
            isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
            deviceId = bundle.getString(KtConstants.P_REMOTE_DEVICE_ID, "")
            if (deviceId.isNullOrEmpty()) {
                deviceId = RemoteDeviceManager.getCurrentLinkeInfo()?.deviceId
            }
            deviceName = bundle.getString(KtConstants.P_REMOTE_DEVICE_NAME, "")
            if (deviceName.isNullOrEmpty()) {
                deviceName = RemoteDeviceManager.getCurrentLinkeInfo()?.deviceName
            }
            //链接状态可能是其他activity传入，也可能是界面重建之后之前保存状态的恢复
            deviceStatus = bundle.getInt(KtConstants.P_REMOTE_DEVICE_STATUS, RemoteDeviceConstants.UNDISCOVERED)
            //mTitle = bundle.getString(KtConstants.P_TITLE)
            mAdapter = RemoteFileAdapter(it, <EMAIL>)
            mAdapter?.setHasStableIds(true)
            isFromDetail = bundle.getBoolean(KtConstants.FROM_DETAIL)
            if (deviceStatus != RemoteDeviceConstants.CONNECTED) {
                mTitle = deviceName
            }
            deviceIsSameAccount = bundle.getInt(KtConstants.P_REMOTE_DEVICE_SAME_ACCOUNT, RemoteDeviceConstants.UN_SAME_ACCOUNT)
            isEmptyPasswd = context.getValue(getEmptyPasswdKey(deviceId), false)
            Log.d(
                TAG,
                "onAttach mCurrentPath $mCurrentPath, mNeedLoadData $mNeedLoadData, isChildDisplay $isChildDisplay, " +
                        "deviceId $deviceId, isFromDetail $isFromDetail, deviceStatus $deviceStatus, " +
                        "deviceIsSameAccount=$deviceIsSameAccount isEmptyPasswd=$isEmptyPasswd"
            )
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        if (isSupportConnectFun) {
            remoteDialogHelper = RemoteDialogHelper(view.context)
        }
        super.onViewCreated(view, savedInstanceState)
        fragmentViewModel?.isFromDetail = isFromDetail
        fragmentViewModel?.mDeviceId = deviceId ?: ""
        //当前链接状态写入viewModel，viewModel在处理返回逻辑时需要
        fragmentViewModel?.deviceStatus = deviceStatus
        //这里开始加载当前Path
        if (mCurrentPath.isNullOrEmpty() || mCurrentPath.equals(Constants.REMOTE_PATH_PREFIX)) {
            fragmentViewModel?.getInitPathForRemote()
        }
    }

    override fun initView(view: View) {
        rootView = view.findViewById(R.id.coordinator_layout)
        if (previewOperate?.isSupportPreview() != true) {
            rootView?.apply {
                setPadding(
                    paddingLeft,
                    COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity),
                    paddingRight,
                    paddingBottom
                )
            }
        }
        mPathBar = view.findViewById(R.id.path_bar)
        fragmentFastScroller = view.findViewById(R.id.fastScroller)
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        emptyContainer = view.findViewById(R.id.empty_container)
        mGridSpanAnimationHelper = fragmentRecyclerView?.let { GridSpanAnimationHelper(it) }

        initToolbar()
        sortEntryView = view.findViewById(com.filemanager.common.R.id.sort_entry_view)
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getRemoteMacKey())
        sortEntryView?.setClickSortListener {
            performClickMenuEvent(R.id.navigation_sort)
        }
        deviceConnectGuideVS = view.findViewById(R.id.device_connect_guide)
        //加载这里，修复直板机不显示可连接/正在连接状态和离线状态的问题
        if (deviceStatus == RemoteDeviceConstants.DISCOVERED || deviceStatus == RemoteDeviceConstants.CONNECTING) {
            showDeviceConnectable()
        } else if (deviceStatus == RemoteDeviceConstants.UNDISCOVERED) {
            showNetworkEmptyOrDeviceOffline()
        }
        scrollHelper = DragScrollHelper(getRecyclerView())
        setWindowInsetsListener()
    }

    private fun setWindowInsetsListener() {
        if (rootWindowInsetsListener == null) {
            rootWindowInsetsListener = View.OnApplyWindowInsetsListener { _, insets ->
                updateButtonBottomMargin(refreshButton)
                updateButtonBottomMargin(connectButton)
                insets
            }
        }
        rootView?.setOnApplyWindowInsetsListener(rootWindowInsetsListener)
    }

    private fun initToolbar() {
        appBarLayout = rootView?.findViewById(com.filemanager.common.R.id.appbar_layout)
        if (previewOperate?.isSupportPreview() != true) {
            mToolbar = rootView?.findViewById(com.filemanager.common.R.id.toolbar)
        }
        toolbar = mToolbar
        mToolbar?.apply {
            if (mTitle != null) {
                title = mTitle
            }
            titleMarginStart = 0
            isTitleCenterStyle = false
            if (deviceStatus == RemoteDeviceConstants.CONNECTED) {
                inflateMenu(R.menu.remote_file_menu)
                setToolbarMenuVisible(this, !isChildDisplay)
                setToolbarEditIcon(this, isChildDisplay)
            }
        }

        baseVMActivity?.apply {
            setSupportActionBar(mToolbar)
            setNormalHomeDisplay(mCurrentPath)
        }
    }


    /**
     * 触发menu的点击事件
     */
    @SuppressLint("RestrictedApi")
    private fun performClickMenuEvent(@IdRes menuId: Int) {
        val activity = baseVMActivity ?: return
        val menu = ActionMenuItem(activity, 0, menuId, 0, 0, "")
        onMenuItemSelected(menu)
    }

    private fun setToolbarMenuVisible(toolbar: COUIToolbar, visible: Boolean) {
        toolbar.menu.findItem(R.id.action_setting)?.isVisible = visible
    }

    private fun setToolbarEditIcon(toolbar: COUIToolbar, isChildDisplay: Boolean) {
        toolbar.menu.findItem(R.id.actionbar_edit)?.let {
            if (isChildDisplay) {
                it.setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_edit)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            } else {
                it.setIcon(null)
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            }
        }
    }

    private fun updateEditAndSortMenu(toolbar: COUIToolbar) {
        val edit = toolbar.menu.findItem(R.id.actionbar_edit)
        edit?.isVisible = fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true
    }

    private fun initToolbarNormalMode(toolbar: COUIToolbar) {
        setNormalHomeDisplay(fragmentViewModel?.mPositionModel?.value?.mCurrentPath)
        toolbar.menu.clear()
        toolbar.isTitleCenterStyle = false
        toolbar.title = mTitle
        baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        if (deviceStatus == RemoteDeviceConstants.CONNECTED) {
            toolbar.inflateMenu(R.menu.remote_file_menu)

            updateEditAndSortMenu(toolbar)
            setToolbarMenuVisible(toolbar, !isChildDisplay)
            setToolbarEditIcon(toolbar, isChildDisplay)
            previewOperate?.onToolbarMenuUpdated(toolbar.menu)
        }
    }

    private fun initToolbarWithEditMode(toolbar: COUIToolbar) {
        baseVMActivity?.supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(false)
        }
        toolbar.apply {
            menu.clear()
            isTitleCenterStyle = true
            if (deviceStatus == RemoteDeviceConstants.CONNECTED) {
                inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
            }
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
    }

    override fun createViewModel(): CategoryRemoteFileViewModel {
        val vm = ViewModelProvider(this)[CategoryRemoteFileViewModel::class.java]
        val sortMode = SortModeUtils.getSharedSortMode(
            MyApplication.appContext,
            SortRecordModeFactory.getRemoteMacKey()
        )
        mFileOperateController = NormalFileOperateController(
            lifecycle,
            CategoryHelper.CATEGORY_FILE_BROWSER,
            vm,
            sortMode
        ).also {
            it.setResultListener(FileOperatorListenerImpl(vm, false))
        }
        return vm
    }

    override fun initData(savedInstanceState: Bundle?) {
        fragmentRecyclerView?.let { recyclerView ->
            mLayoutManager =
                FileGridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1).apply {
                    spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                        override fun getSpanSize(position: Int): Int {
                            val viewType = mAdapter?.getItemViewType(position)
                            val isSingleLine =
                                (viewType == BaseFileBean.TYPE_FILE_LIST_HEADER)
                                        || (viewType == BaseFileBean.TYPE_LABEL_FILE)
                                        || (viewType == BaseFileBean.TYPE_FILE_AD)
                            return if (isSingleLine) spanCount else 1
                        }
                    }
                }
            recyclerView.addItemDecoration(mSpacesItemDecoration)
            recyclerView.isNestedScrollingEnabled = true
            recyclerView.clipToPadding = false
            recyclerView.layoutManager = mLayoutManager!!
            recyclerView.itemAnimator = mFolderTransformAnimator
            recyclerView.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }
            resetRecyclerViewHoriontalPadding(recyclerView)

            mAdapter?.let {
                recyclerView.adapter = it
            }
            appBarLayout?.doOnLayout {
                if (isAdded) {
                    val paddingBottom = if (recyclerView.paddingBottom == 0) {
                        MyApplication.appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    } else {
                        recyclerView.paddingBottom
                    }
                    recyclerView.setPadding(
                        recyclerView.paddingLeft,
                        KtViewUtils.getRecyclerViewTopPadding(appBarLayout),
                        recyclerView.paddingRight,
                        paddingBottom
                    )
                }
                updateEmptyContainerHeight(false)
            }
            recyclerView.setLoadStateForScroll(this)
        }
        initPathBar()
        if (mNeedLoadData) {
            Log.d(TAG, "initData onResumeLoadData")
            onResumeLoadData()
        }
        if (actionCheckPermission().not()) {
            sortEntryView?.setFileCount(0)
        }
        if (deviceStatus != RemoteDeviceConstants.CONNECTED) {
            //进入该页面时，如果远程设备是未连接，进行延迟刷新，获取最新的设备状态
            fragmentRecyclerView?.postDelayed({
                refreshRemoteDeviceStatus()
            }, REFRESH_DEVICE_STATUS_DELAY)
        }
        TouchShareSupplier.attach(this, object : TouchShareNotSupportSupplier(CategoryHelper.CATEGORY_POSITION_GROUP_SUB_LIST_REMOTE_MAC + 1) {
            override fun notSupportTips() {
                CustomToast.showLong(com.filemanager.common.R.string.share_non_local_file_need_download_toast)
            }
        })
    }

    private fun resetRecyclerViewHoriontalPadding(recyclerView: FileManagerRecyclerView) {
        (activity as? CategoryRemoteFileActivity)?.let {
            Log.d("resetRecyclerViewHoriontalPadding", UIConfigMonitor.getWindowType().name)
            if (UIConfigMonitor.getWindowType() == UIConfig.WindowType.LARGE) {
                recyclerView.setPadding(
                    MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.dp_16),
                    recyclerView.paddingTop,
                    MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.dp_16),
                    recyclerView.paddingBottom
                )
            } else {
                recyclerView.setPadding(0, recyclerView.paddingTop, 0, recyclerView.paddingBottom)
            }
        }
    }

    override fun permissionSuccess() {
        Log.d(TAG, "permissionSuccess onResumeLoadData")
        onResumeLoadData()
    }

    override fun getScanMode(): Int {
        return fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
    }

    override fun setScanMode(mode: Int) {
        fragmentViewModel?.mBrowseModeState?.value = mode
    }

    override fun isSelectionMode(): Boolean {
        return fragmentViewModel?.isInSelectMode() ?: false
    }

    override fun setPreviewOperate(operate: IPreviewOperate) {
        previewOperate = operate
    }

    private fun initPathBar() {
        mPathBar?.let {
            Log.d(TAG, "initPathBar mCurrentPath = $mCurrentPath")
            val currentPath = mCurrentPath ?: return
            if (currentPath.isNotEmpty()) {
                fragmentViewModel?.initPathHelper(currentPath)
                it.setPathHelper(fragmentViewModel?.mPathHelp)
                it.setSpecailPathProcess(object : BrowserPathBar.ISpecialPathProcess {
                    override fun shouldProcessSpecial(wholePath: String): Boolean {
                        return true
                    }

                    override fun getVisibleStringForSeparatePath(
                        wholePath: String,
                        separateList: Array<String>,
                        index: Int
                    ): String {
                        return getPathHelperShowString(wholePath, separateList, index)
                    }
                })
                it.setOnPathClickListener { index, path ->
                    Log.d(TAG, "onPathClick $index, path $path")
                    val isBelowRootPath = path?.let {
                        it.contentEquals(Constants.REMOTE_PATH_PREFIX, true)
                    } ?: false
                    if (isBelowRootPath) {
                        Log.d(TAG, "isBelowRemoteRootPath, click not processed")
                    } else {
                        fragmentViewModel?.clickPathBar(index)
                    }
                }.setTextFocusChangeListener { currentFocusText ->
                    mTitle = currentFocusText
                    if (fragmentViewModel?.mNeedScroll == true) {
                        KtAnimationUtil.showUpdateToolbarTitleWithAnimate(mToolbar, mTitle)
                    } else {
                        mToolbar?.title = mTitle
                    }
                }.show()
                it.setCurrentPath(currentPath)
            }
        }
    }

    private fun getPathHelperShowString(
        wholePath: String,
        separateList: Array<String>,
        index: Int
    ): String {
        Log.d(TAG, "getPathHelperShowString wholePath $wholePath, separateList ${separateList.toList()}, index $index")
        //转换为pathInfo的下标，这里列表的下标和pathInfo的下标两者是倒序关系
        val pathInfoIndex = fragmentViewModel?.mPathHelp?.getPathLeft()?.minus(index + 1)
        Log.d(TAG, "getPathHelperShowString indexSize ${fragmentViewModel?.mPathHelp?.getPathLeft()}, pathInfoIndex $pathInfoIndex")
        return if (pathInfoIndex != null && pathInfoIndex >= 0) {
            val pathInfoInStack = fragmentViewModel?.mPathHelp?.getPath(pathInfoIndex)
            //这里从extra中获取别名，将别名返回作为BrowsePathBar每个分离item中的显示内容
            val extraInPathInfo = pathInfoInStack?.extra ?: ""
            extraInPathInfo.ifEmpty {
                separateList[index]
            }
        } else {
            Log.d(TAG, "getPathHelperShowString pathInfoIndex $pathInfoIndex null or < 0, use orginal path ${separateList[index]}")
            separateList[index]
        }
    }

    override fun startObserve() {
        fragmentRecyclerView?.post {
            val viewModule = fragmentViewModel ?: return@post
            if (!isAdded) {
                return@post
            }
            viewModule.mModeState.listModel.observe(this) { listModel ->
                onListModelChanged(viewModule, listModel)
            }
            viewModule.uiState.observe(this) { fileUiModel ->
                Log.d(TAG, "")
                onFileUiModelChanged(fileUiModel, viewModule)
            }
            viewModule.mPositionModel.observe(this) { positionModel ->
                onPositionModelChanged(viewModule, positionModel)
                viewModule.previewClickedFileLiveData.value = null
            }
            startScanModeObserver()
            startObserveLoadState()
            viewModule?.previewClickedFileLiveData?.observe(this) {
                mAdapter?.setPreviewClickedFile(it)
            }
            startObserveCurrentPathInitState()
            registerRemoteDeviceStatusListener()
        }
    }

    private fun onListModelChanged(viewModule: CategoryRemoteFileViewModel, listModel: Int) {
        if (!viewModule.mModeState.initState) {
            mToolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            return
        }
        Log.d(TAG, "mListModel=$listModel")
        if (listModel == KtConstants.LIST_SELECTED_MODE) {
            (baseVMActivity as? NavigationInterface)?.apply {
                showNavigation()
                viewModule.setNavigateItemAble(this@apply)
            }
            mAdapter?.setSelectEnabled(true)
            previewEditedFiles(fragmentViewModel?.getSelectItems())
            fragmentRecyclerView?.let {
                val bottomView = baseVMActivity?.findViewById<View>(R.id.navigation_tool)
                val paddingBottom = KtViewUtils.getSelectModelPaddingBottom(it, bottomView)
                it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, paddingBottom)
                fragmentFastScroller?.apply { trackMarginBottom = paddingBottom }
            }
            mToolbar?.let {
                changeActionModeAnim(it, {
                    initToolbarWithEditMode(it)
                    refreshSelectToolbar(it)
                })
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
        } else {
            previewClickedFile(
                fragmentViewModel?.previewClickedFileLiveData?.value,
                fragmentViewModel?.previewClickedFileLiveData
            )
            mAdapter?.setSelectEnabled(false)
            fragmentRecyclerView?.let {
                val paddingBottom = MyApplication.appContext.resources
                    .getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, paddingBottom)
                fragmentFastScroller?.trackMarginBottom =
                    MyApplication.appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
            }
            mToolbar?.let {
                changeActionModeAnim(it, {
                    initToolbarNormalMode(it)
                    refreshScanModeItemIcon(it)
                }, (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true))
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
            (baseVMActivity as? NavigationInterface)?.apply {
                hideNavigation()
            }
        }
    }

    private fun onFileUiModelChanged(
        fileUiModel: CategoryRemoteFileViewModel.FileBrowserUiModel,
        viewModule: CategoryRemoteFileViewModel
    ) {
        Log.d(
            TAG, "UiModel mUiState =" + fileUiModel.fileList.size + ","
                    + fileUiModel.selectedList.size + "," + fileUiModel.keyWord + ", deviceStatus: $deviceStatus"
        )
        //这里加上这个是在界面重建时，这个Model回调会调用，如果在重建前界面状态为非连接状态，这里需要反馈在UI上
        if (deviceStatus == RemoteDeviceConstants.DISCOVERED || deviceStatus == RemoteDeviceConstants.CONNECTING) {
            showDeviceConnectable()
            return
        } else if (deviceStatus == RemoteDeviceConstants.UNDISCOVERED) {
            showNetworkEmptyOrDeviceOffline()
            return
        }
        sortEntryView?.setFileCount(fragmentViewModel?.getRealFileSize() ?: 0)
        if (fileUiModel.stateModel.listModel.value == KtConstants.LIST_SELECTED_MODE) {
            mToolbar?.let {
                refreshSelectToolbar(it)
            }
            (fileUiModel.fileList as? ArrayList<BaseFileBean>)?.let {
                mFolderTransformAnimator.mIsFolderInAnimation = viewModule.mIsFolderIn
                mAdapter?.setData(it, fileUiModel.selectedList, viewModule.mNeedScroll)
                previewEditedFiles(fragmentViewModel?.getSelectItems())
                Log.d(TAG, "onFileUiModelChanged setData ListMode")
            }
        } else {
            previewClickedFile(
                fragmentViewModel?.previewClickedFileLiveData?.value,
                fragmentViewModel?.previewClickedFileLiveData
            )
            if (fileUiModel.fileList.isEmpty()) {
                showEmptyView()
            } else {
                hideEmptyView()
            }
            mToolbar?.let {
                refreshScanModeItemIcon(it)
                updateEditAndSortMenu(it)
            }
            setNormalHomeDisplay(viewModule.mPositionModel.value?.mCurrentPath)
            if (fileUiModel.fileList !is ArrayList<BaseFileBean>) {
                return
            }
            val adapter = mAdapter ?: return
            adapter.setKeyWord(fileUiModel.keyWord)
            mFolderTransformAnimator.mIsFolderInAnimation = viewModule.mIsFolderIn
            (fileUiModel.fileList as? ArrayList<BaseFileBean>)?.let { list ->
                Log.d(TAG, "onFileUiModelChanged setData ListMode")
                adapter.setData(list, fileUiModel.selectedList, viewModule.mNeedScroll)
            }
        }
    }

    private fun onPositionModelChanged(
        viewModule: CategoryRemoteFileViewModel,
        positionModel: CategoryRemoteFileViewModel.PositionModel
    ) {
        if (!viewModule.mModeState.initState) {
            return
        }
        Log.d(TAG, "onPositionModelChanged ${positionModel.mCurrentPath}")
        mPathBar?.let {
            if (it.getCurrentPath() != positionModel.mCurrentPath) {
                it.setCurrentPath(positionModel.mCurrentPath)
            }
        }
        appBarLayout?.postDelayed({
            mLayoutManager?.scrollToPositionWithOffset(
                positionModel.mPosition,
                positionModel.mOffset
            )
            viewModule.mPositionModel.value?.mPosition = 0
            viewModule.mPositionModel.value?.mOffset = 0
            viewModule.mNeedScroll = false
        }, BaseFolderAnimAdapter.FILE_BROWSER_FOLDER_ANIM_DELAY)
    }

    override fun onDestroyView() {
        Log.i(TAG, "onDestroyView")
        //放在onDestroyView而不是onDestory中防止lifecycleOwner.lifecycleScope报错
        fragmentViewModel?.releaseDir(viewLifecycleOwner, MyApplication.appContext)
        //这里调用反注册关系，将loader和FolderTransformAnimator两者解除关系
        mFolderTransformAnimator.unRegisterNeddSkipAnimator()
        remoteDialogHelper?.destroy()
        super.onDestroyView()
    }

    override fun onDragStart(e: MotionEvent): Boolean {
        isDraggingFromMac = true
        Log.d(TAG, "onDragStart dragging:$DragUtils.isDragging")
        if (DragUtils.isDragging) return false
        fragmentDragScanner?.cancel(true)
        val view = fragmentRecyclerView?.findChildViewUnder(e.x, e.y) ?: return false
        var result = true
        fragmentRecyclerView?.post {
            val position = fragmentRecyclerView?.getChildAdapterPosition(view) ?: 0
            if (position == -1) {
                Log.e(TAG, "onDragStart position is -1")
                result = false
                return@post
            }
            val dragHoldDownFile = fragmentViewModel?.uiState?.value?.fileList?.get(position)
            val activityContext = this.activity ?: run { result = false; return@post }
            val selectList = fragmentViewModel?.getSelectItems() ?: run { result = false; return@post }
            val viewMode = fragmentViewModel?.getRecyclerViewScanMode()
            val dragHoldDrawable = if (viewMode == SelectionTracker.LAYOUT_TYPE.LIST) {
                view.findViewById<ImageView>(com.filemanager.common.R.id.file_list_item_icon).drawable
            } else {
                view.findViewById<ImageView>(com.filemanager.common.R.id.file_grid_item_icon).drawable
            }

            val itemViewList = ArrayList<View>()
            selectList.forEach { baseFileBean ->
                val fileList = fragmentViewModel?.uiState?.value?.fileList
                val indexOf = fileList?.indexOf(baseFileBean)
                if (indexOf != null && indexOf >= 0 && indexOf < fileList.size) {
                    val viewHolder = fragmentRecyclerView?.findViewHolderForAdapterPosition(indexOf)
                    if (viewHolder != null) {
                        itemViewList.add(viewHolder.itemView)
                    }
                }
            }
            DragUtils.createSelectedFileList(selectList)
            (baseVMActivity as? NavigationInterface)?.let { fragmentViewModel?.setNavigateItemAble(it) }
            fragmentDragScanner = RemoteFileDragDropScanner(
                activityContext,
                RemoteFileDragListener(
                    activityContext,
                    view,
                    dragHoldDownFile,
                    dragHoldDrawable,
                    getFragmentCategoryType(),
                    viewMode
                ).addSelectedView(itemViewList),
                viewMode,
                viewMode == SelectionTracker.LAYOUT_TYPE.GRID
            )
            fragmentDragScanner?.let {
                if (it.addData(selectList)) {
                    it.execute()
                }
            }
            Log.d(TAG, "onDragStart end")
        }
        return result
    }

    override fun onDestroy() {
        super.onDestroy()
        mPathBar?.setOnPathClickListener(null)
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
        unregisterRemoteDeviceStatusListener()
        rootView?.setOnApplyWindowInsetsListener(null)
    }

    private fun startScanModeObserver() {
        mNeedSkipAnimation = true
        fragmentViewModel?.mBrowseModeState?.observe(this) { scanMode ->
            mToolbar?.let {
                val needSkipAnimation = mNeedSkipAnimation
                Log.d(TAG,  "startScanModeObserver scanMode $scanMode, needSkipAnimation $needSkipAnimation")
                if (needSkipAnimation) {
                    refreshScanModeAdapter(scanMode)
                } else {
                    fragmentRecyclerView?.let { recyclerView ->
                        recyclerView.mTouchable = false
                        recyclerView.stopScroll()
                    }
                    mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                        override fun onSpanChangeCallback() {
                            Log.d(TAG,  "startScanModeObserver onSpanChangeCallback $scanMode, needSkipAnimation $needSkipAnimation")
                            mLayoutManager?.scrollToPosition(0)
                            refreshScanModeAdapter(scanMode)
                        }
                    }, object : OnAnimatorEndListener {
                        override fun onAnimatorEnd() {
                            fragmentRecyclerView?.mTouchable = true
                        }
                    })
                }
                delay { refreshScanModeItemIcon(it, needSkipAnimation) }
            }
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            refreshScanModeAdapter(scanMode)
            baseVMActivity?.let {
                mFileEmptyController.changeEmptyFileIcon()
            }
            mSortPopupController.hideSortPopUp()
            mFileOperateController?.onConfigurationChanged(context?.resources?.configuration)
            if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
            }
            fragmentRecyclerView?.let {
                resetRecyclerViewHoriontalPadding(it)
            }
            updateLeftRightMargin()
            updateWidthForButton(refreshButton)
            updateWidthForButton(connectButton)
            updateEmptyContainerHeight(false)
        }
    }

    override fun updateLeftRightMargin() {
        val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            mAdapter?.notifyDataSetChanged()
        }
        mPathBar?.updateLeftRightMargin()
        sortEntryView?.updateLeftRightMargin()
    }

    override fun isEmptyList(): Boolean {
        return hasShowEmpty
    }

    override fun handleDragScroll(event: DragEvent): Boolean {
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    override fun resetScrollStatus() {
        scrollHelper?.resetDragStatus()
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        val selectedFiles = DragUtils.getSelectedFiles()
        val itemViewList = ArrayList<View>()
        val fileList = fragmentViewModel?.uiState?.value?.fileList
        val size = fileList?.size ?: return null
        selectedFiles?.forEach { fileBean ->
            val indexOf = fileList.indexOf(fileBean) ?: return null
            if (indexOf >= 0 && indexOf < size) {
                val itemView =
                    getRecyclerView()?.findViewHolderForAdapterPosition(indexOf)?.itemView
                itemView?.let { itemViewList.add(it) }
            }
        }
        return itemViewList
    }

    override fun setNavigateItemAble() {
        activity?.lifecycle?.coroutineScope?.launch(Dispatchers.Main) {
            val selectItems = fragmentViewModel?.getSelectItems()
            val selectItemSize = selectItems?.size ?: 0
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble((selectItemSize > 0 && !DragUtils.isDragging), hasDrmFile(selectItems))
        }
    }

    override fun refreshDataForDir(path: String, category: Int) {}

    override fun renameToShortCutFolder(newName: String, file: BaseFileBean): String { return "" }

    override fun renameToLabel(newName: String, labelId: Long) {}

    override fun onClickDir(path: String) {
        val file = File(path)
        if (file.parent == fragmentViewModel?.mPositionModel?.value?.mCurrentPath) {
            val baseFile = RemoteFileBean()
            baseFile.mData = path
            baseFile.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
            baseFile.mIsDirectory = true
            clickToNextDir(baseFile)
        }
    }

    private fun clickToNextDir(baseFile: BaseFileBean) {
        fragmentRecyclerView?.paddingTop?.let { paddingTop ->
            val viewPosition = mLayoutManager?.findFirstVisibleItemPosition() ?: 0
            val offset = mLayoutManager?.findViewByPosition(viewPosition)?.top ?: paddingTop
            fragmentViewModel?.onDirClick(
                baseVMActivity,
                baseFile,
                viewPosition,
                offset - paddingTop
            )
        }
    }


    @SuppressLint("NotifyDataSetChanged")
    private fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getGridItemCount(
            activity, scanMode,
            ItemDecorationFactory.GRID_ITEM_DECORATION_FILE_BROWSER
        )
        mLayoutManager?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mAdapter?.apply {
            mScanViewModel = scanMode
            mFolderTransformAnimator.mSkipAddRemoveAnimation = true
            checkComputingAndExecute {
                notifyDataSetChanged()
            }
        }
    }

    private fun refreshScanModeItemIcon(toolbar: COUIToolbar, needSkipAnimation: Boolean = true) {
        toolbar.menu.findItem(R.id.actionbar_scan_mode)?.let {
            val desc: String
            val resId: Int =
                if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                    desc =
                        MyApplication.appContext.getString(com.filemanager.common.R.string.palace_view)
                    com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
                } else {
                    desc =
                        MyApplication.appContext.getString(com.filemanager.common.R.string.list_view)
                    com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
                }
            it.contentDescription = desc
            if (needSkipAnimation) {
                it.setIcon(resId)
            } else {
                KtAnimationUtil.updateMenuItemWithFadeAnimate(it, resId, baseVMActivity)
            }
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            mLoadingController = LoadingController(it, this).apply {
                //这里传入rootView修复在平板或中屏手机上，转圈不在Fragment中央而在整体屏幕中央的问题
                observe(fragmentViewModel?.dataLoadState, emptyContainer) {
                    val intercept = if (deviceStatus != RemoteDeviceConstants.CONNECTED) {
                        true
                    } else {
                        fragmentViewModel?.mRemoteFileLoaderCallBack?.isLoadNewPath()?.not()
                            ?: false
                    }
                    Log.d(TAG, "startObserveLoadState intercept $intercept")
                    intercept
                }
            }
            val bgColor = context?.resources?.getColor(com.support.appcompat.R.color.coui_color_background_with_card)
            bgColor?.let {
                mLoadingController?.setBackgroundColor(it)
            }
            mLoadingController?.setDeleyShowTime(LOADING_DELAY_TIME)
            mLoadingController?.setShowAinimate(true)
            mLoadingController?.setDissapearAnimate(true)
            mLoadingController?.setShowLoadingTips(false)
            //这里蒋LoadingController和FolderTransformAnimator通过接口方式关联起来
            mLoadingController?.let { controller ->
                mFolderTransformAnimator.registerNeedSkipAnimator(controller)
            }
        }
    }


    private fun startObserveCurrentPathInitState() {
        val viewModule = fragmentViewModel ?: return
        viewModule.mCurrentPathInitedCode.observe(this) { inited ->
            Log.d(TAG, "mCurrentPathInited changed $inited, pathFromModule ${viewModule.mCurrentPath}, deviceStatus $deviceStatus")
            if (deviceStatus != RemoteDeviceConstants.CONNECTED) {
                return@observe
            }
            if (inited == Constants.ERROR_CODE_SUC) {
                //这里需要区分重建逻辑
                val lastPath = viewModule.mPositionModel.value?.mCurrentPath
                Log.d(TAG, "startObserveCurrentPathInitState lastPath $lastPath, ")
                if (lastPath != null && lastPath != viewModule.mCurrentPath) {
                    //重建时的逻辑
                    mCurrentPath = lastPath
                } else {
                    //第一次进入时的逻辑
                    mCurrentPath = viewModule.mCurrentPath
                }
                initPathBar()
                initToolbar()
                mCurrentPath?.let {
                    fragmentViewModel?.initLoader(LoaderViewModel.getLoaderController(this), it, viewModule.rootPathAlias)
                    fragmentViewModel?.mRemoteFileLoaderCallBack?.updatePathAndDeviceId(
                        it,
                        deviceId
                    )
                    fragmentViewModel?.loadData(it, true)
                    //这里判断根目录是否需要显示别名，如果需要显示别名，触发一次setCurrentPath，重新刷新PathBar的View
                    if (fragmentViewModel?.checkNeedShowRootPathAlias() == true) {
                        mPathBar?.setCurrentPath(it, true)
                    }
                }
            } else {
                if (inited == Constants.ERROR_CODE_PASSWORD_ERROR) {
                    if (isSupportConnectFun) {
                        //如果是密码错误导致，检查是否同账号退出导致
                        checkPasswdErrorIsExitAccount()
                    } else {
                        //如果是密码错误导致，显示密码错误的提示页面
                        showEmptyView(Constants.ERROR_CODE_PASSWORD_ERROR)
                    }
                } else {
                    showEmptyView(ERROR_CODE_NO_ROOTPATH_FOUND)
                }
            }
            //如果从sdk侧获取的path为空，这里将PathBar不显示
            if ((inited == Constants.ERROR_CODE_SUC) && mCurrentPath?.isNotEmpty() == true) {
                mPathBar?.visibility = View.VISIBLE
            } else {
                mPathBar?.visibility = View.GONE
            }
        }
    }

    /**
     * 检测是否同账号退出导致的密码错误
     */
    private fun checkPasswdErrorIsExitAccount(): Boolean {
        val nowIsSameAccount = isSameAccount(deviceId)
        val preIsSameAccount = deviceIsSameAccount == RemoteDeviceConstants.SAME_ACCOUNT
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        remoteDeviceApi?.disconnect()
        onDeviceStatusChanged(RemoteDeviceConstants.DISCOVERED)
        Log.d(TAG, "nowIsSameAccount=$nowIsSameAccount preIsSameAccount=$preIsSameAccount")
        if (preIsSameAccount && nowIsSameAccount.not()) {
            Log.d(TAG, "checkPasswdErrorIsExitAccount exit sameAccount")
            mainHandler.post {
                saveEmptyPasswdStatus(true)
                remoteDialogHelper?.showExitAccountDialogDialog { _, _ ->
                    createCountEditBottomDialog(isAutoShowKeyboard = true)
                }
            }
            return true
        } else {
            mainHandler.post {
                remoteDialogHelper?.showPasswdErrorDialog { _, _ ->
                    createCountEditBottomDialog(isAutoShowKeyboard = true)
                }
            }
            return false
        }
    }

    /**
     * 存储正在连接状态
     */
    private fun saveConnectStatus(status: Boolean) {
        if (deviceStatus == RemoteDeviceConstants.CONNECTING && status.not()) {
            deviceStatus = RemoteDeviceConstants.DISCOVERED
        }
        deviceId?.let {
            Log.d(TAG, "save status connecting, deviceId=$it $status")
            RemoteDeviceManager.connectingStatus[it] = status
        }
    }

    /**
     * 存储空密码状态
     */
    private fun saveEmptyPasswdStatus(emptyStatus: Boolean) {
        Log.d(TAG, "saveEmptyPasswdStatus deviceId=$deviceId emptyStatus=$emptyStatus")
        isEmptyPasswd = emptyStatus
        if (emptyStatus) {
            connectButton?.isEnabled = false
            devicePasswdInputView?.editText?.setText(EMPTY_PASSWD)
        } else {
            connectButton?.isEnabled = true
        }
        context?.putValue(getEmptyPasswdKey(deviceId), emptyStatus)
    }

    private fun refreshSelectToolbar(toolbar: COUIToolbar) {
        val checkedCount = fragmentViewModel?.uiState?.value?.selectedList?.size ?: 0
        //这里根据UX定义，如果包含列表中包含可选的文件 + 可选文件个数大于0
        val isSelectAll =
            (fragmentViewModel?.getCanSelectSize() == fragmentViewModel?.uiState?.value?.selectedList?.size) && (checkedCount > 0)
        ToolbarUtil.updateToolbarTitle(toolbar, checkedCount, isSelectAll)
        val selectAllEnable = (fragmentViewModel?.getCanSelectSize() ?: 0) > 0
        ToolbarUtil.updateToolbarSelectAllEnable(toolbar, selectAllEnable)
        if (baseVMActivity is NavigationInterface) {
            (baseVMActivity as NavigationInterface).setNavigateItemAble(
                (fragmentViewModel?.uiState?.value?.selectedList?.isNotEmpty() == true && !DragUtils.isDragging)
                    ?: false, false
            )
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        if (fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true && deviceStatus == RemoteDeviceConstants.CONNECTED) {
            Log.d(TAG, "showEmptyView onResume")
            showEmptyView()
        }
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getRemoteMacKey())
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    private fun showEmptyView(inputErrorCode: Int? = null) {
        val errorCode = inputErrorCode ?: fragmentViewModel?.uiState?.value?.erroCode
        Log.d(TAG, "showEmptyView errorCode: $errorCode, inputErrorCode $inputErrorCode")
        var includePathBarArea = false
        if (errorCode != null) {
            val isNetWorkError = Constants.isNetWorkError(errorCode)
            val isSdkError = Constants.isSdkError(errorCode)
            val isNoRootPathError = Constants.isNoRootPathError(errorCode)
            val isPassWordError = Constants.isPasswordError(errorCode)
            if (isNetWorkError || isSdkError || isNoRootPathError || isPassWordError) {
                includePathBarArea = true
            }
        }
        fragmentRecyclerView?.doOnLayout {
            //这里需要doOnLayout是由于updateEmptyContainerHeight方法中计算高度需要layout之后才有值
            updateEmptyContainerHeight(includePathBarArea)
            showEmptyViewByErrorCode(errorCode)
        }
        sortEntryView?.visibility = View.INVISIBLE
        if (includePathBarArea) {
            mPathBar?.visibility = View.INVISIBLE
        } else {
            mPathBar?.visibility = View.VISIBLE
        }
        fragmentRecyclerView?.visibility = View.INVISIBLE
        hasShowEmpty = true
        listEmptyFile()
    }

    private fun showEmptyViewByErrorCode(errorCode: Int?) {
        if (errorCode != null) {
            val isSuc = Constants.isResultSuc(errorCode)
            if (isSuc) {
                //如果是成功的错误码，直接显示无文件的的空View
                showNormalEmptyFile()
            } else {
                val isNetWorkError = Constants.isNetWorkError(errorCode)
                val isSdkError = Constants.isSdkError(errorCode)
                val isNoRootPathError = Constants.isNoRootPathError(errorCode)
                val isPasswordError = Constants.isPasswordError(errorCode)
                if (isNetWorkError) {
                    //如果是网络错误码，使用网络的空View
                    showNetWorkError()
                } else if (isSdkError || isNoRootPathError) {
                    //如果是sdk报的错误吗或当前获取rootPath出错，那么使用加载失败的空View
                    showLoadFailed(errorCode)
                } else if (isPasswordError) {
                    if (isSupportConnectFun) {
                        showPasswordError()
                    } else {
                        showPasswordErrorOld()
                    }
                } else {
                    showNormalEmptyFile()
                }
            }
        } else {
            //默认使用无文件的空View
            showNormalEmptyFile()
        }
    }

    private fun showNormalEmptyFile() {
        Log.d(TAG, "showNormalEmptyFile")
        val activity = baseVMActivity
        val view = emptyContainer
        if ((activity != null) && (view != null)) {
            mFileEmptyController.showFileEmptyView(
                activity,
                view,
                addFileClickEvent = null
            )
            mFileEmptyController.setFileEmptyTitle(com.filemanager.common.R.string.empty_file)
            mFileEmptyController.setEmptyGuidContent(
                View.GONE,
                com.filemanager.common.R.string.menu_file_list_new_folder
            )
        }
    }

    private fun showNetWorkError() {
        Log.d(TAG, "showNetWorkError")
        val activity = baseVMActivity
        val view = emptyContainer
        if ((activity != null) && (view != null)) {
            mFileEmptyController.showFileEmptyView(
                activity,
                view,
                FileEmptyUtils.NO_CONNECTION_ANIMATION_JSON,
                com.filemanager.common.R.string.no_internet_connection
            )
            val summaryString =
                activity.getString(com.filemanager.common.R.string.check_network_setting)
            mFileEmptyController.setEmptySummaryVisibilityAndContent(View.VISIBLE, summaryString)
            val guideString = activity.getString(com.filemanager.common.R.string.set_button_text)
            mFileEmptyController.setEmptyGuidVisibilityAndContent(View.VISIBLE, guideString) {
                Log.d(TAG, "start activity to modify connection")
                val intent = Intent()
                intent.setAction(ACTION_TO_NETWORK_SETTINGS)
                baseVMActivity?.startActivity(intent)
            }
        }
    }

    private fun showLoadFailed(errorCode: Int) {
        Log.d(TAG, "showLoadFailed")
        val activity = baseVMActivity
        val view = emptyContainer
        if ((activity != null) && (view != null)) {
            mFileEmptyController.showFileEmptyView(
                activity,
                view,
                FileEmptyUtils.LOAD_FAILED_ANIMATION_JSON,
                com.filemanager.common.R.string.load_fail_please_retry
            )
            //mFileEmptyController.setFileEmptyTitle(com.filemanager.common.R.string.load_fail_please_retry)
            val guideString = activity.getString(com.filemanager.common.R.string.retry_string)
            mFileEmptyController.setEmptyGuidVisibilityAndContent(View.VISIBLE, guideString) {
                if (Constants.isSdkError(errorCode)) {
                    Log.d(TAG, "showLoadFailed sdk error retry load")
                    fragmentViewModel?.loadData()
                } else if (Constants.isNoRootPathError(errorCode)) {
                    Log.d(TAG, "showLoadFailed no rootPath retry get rootPath")
                    fragmentViewModel?.getInitPathForRemote()
                }
                mFileEmptyController.hideFileEmptyView()
            }
        }
        // 加载失败有时是因为同账号退出，刷新状态可检测是否账号退出导致
        refreshRemoteDeviceStatus()
    }

    private fun showPasswordError() {
        Log.d(TAG, "showPasswordError")
        onDeviceStatusChanged(RemoteDeviceConstants.DISCOVERED)
        RemoteDeviceManager.disconnect()
        remoteDialogHelper?.showPasswdErrorDialog { _, _ ->
            createCountEditBottomDialog(isAutoShowKeyboard = true)
        }
    }

    private fun showPasswordErrorOld() {
        Log.d(TAG, "showPasswordErrorOld")
        val activity = baseVMActivity
        val view = emptyContainer
        if ((activity != null) && (view != null)) {
            mFileEmptyController.showFileEmptyView(
                activity,
                view,
                FileEmptyUtils.LOAD_FAILED_ANIMATION_JSON,
                com.filemanager.common.R.string.loading_failed
            )
            val guideString = activity.getString(com.filemanager.common.R.string.set_button_text)
            mFileEmptyController.setEmptyGuidVisibilityAndContent(View.VISIBLE, guideString) {
                fragmentViewModel?.jumpToNewDevicePage(this.baseVMActivity, deviceId)
                mFileEmptyController.hideFileEmptyView()
            }
            val summaryString =
                activity.getString(com.filemanager.common.R.string.please_try_correct_password)
            mFileEmptyController.setEmptySummaryVisibilityAndContent(View.VISIBLE, summaryString)
        }
    }


    private fun hideEmptyView() {
        Log.d(TAG, "hideEmptyView")
        mFileEmptyController.hideFileEmptyView()
        sortEntryView?.visibility = View.VISIBLE
        mPathBar?.visibility = View.VISIBLE
        fragmentRecyclerView?.visibility = View.VISIBLE
    }


    override fun getFragmentInstance(): Fragment {
        return this
    }

    override fun setFragmentArguments(arguments: Bundle?) {
        this.arguments = arguments
    }

    override fun setPreviewToolbar(toolbar: COUIToolbar?) {
        mToolbar = toolbar
    }

    override fun onResumeLoadData() {
        Log.d(TAG, "onResumeLoadData deviceStatus $deviceStatus")
        if (!isAdded) {
            return
        }
        if (checkShowPermissionEmpty(false)) {
            sortEntryView?.setFileCount(0)
            mPathBar?.visibility = View.GONE
            return
        }
        if (deviceStatus == RemoteDeviceConstants.DISCOVERED || deviceStatus == RemoteDeviceConstants.CONNECTING) {
            showDeviceConnectable()
            return
        } else if (deviceStatus == RemoteDeviceConstants.UNDISCOVERED) {
            showNetworkEmptyOrDeviceOffline()
            return
        } else if (fragmentViewModel?.mCurrentPathInitedCode?.value == Constants.ERROR_CODE_PASSWORD_ERROR) {
            showEmptyView(Constants.ERROR_CODE_PASSWORD_ERROR)
            return
        }
        mPathBar?.visibility = View.VISIBLE
        val bundle = arguments ?: return
        val currentPath = bundle.getString(KtConstants.P_CURRENT_PATH)
        val lastPath = fragmentViewModel?.mPositionModel?.value?.mCurrentPath
        Log.d(
            TAG,
            "onResumeLoadData fromDetail:$isFromDetail current:$currentPath lastPath:$lastPath"
        )
        mCurrentPath = lastPath ?: currentPath
        mCurrentPath?.let { mCurrentPath ->
            mPathBar?.let {
                if (mCurrentPath != it.getCurrentPath()) {
                    it.setCurrentPath(mCurrentPath)
                }
            }
        }
        mCurrentPath?.let {
            //加入别名
            fragmentViewModel?.initLoader(LoaderViewModel.getLoaderController(this), it, fragmentViewModel?.rootPathAlias)
        }
        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
            baseVMActivity?.apply {
                setSupportActionBar(mToolbar)
                baseVMActivity?.supportActionBar?.apply {
                    setDisplayHomeAsUpEnabled(!isChildDisplay)
                    setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
                }
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
        fragmentViewModel?.loadFolderLogo()
    }

    override fun pressBack(): Boolean {
        val activity = activity ?: return false
        val result = fragmentViewModel?.pressBack() ?: false
        if (activity is CategoryRemoteFileActivity) {
            return result
        }
        if (!result && isFromDetail) {
            val mainAction = Injector.injectFactory<IMain>()
            mainAction?.backPreviousFragment(-1, activity)
            return true
        }
        return result
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return activity?.let {
            val selectFiles = mViewModel?.getSelectItems()
            handleSelectRemoteFile(it, item, selectFiles)
        } ?: false
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        if (deviceStatus != RemoteDeviceConstants.CONNECTED) {
            return
        }
        inflater.inflate(R.menu.remote_file_menu, menu)
        mToolbar?.apply {
            refreshScanModeItemIcon(this)
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this, isChildDisplay)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        arguments?.putString(
            KtConstants.P_CURRENT_PATH,
            fragmentViewModel?.mPositionModel?.value?.mCurrentPath ?: mCurrentPath
        )
        //重建时将当前的链接状态保存
        arguments?.putInt(
            KtConstants.P_REMOTE_DEVICE_STATUS,
            deviceStatus
        )
        super.onSaveInstanceState(outState)
    }

    @Suppress("LongMethod")
    override fun onMenuItemSelected(item: MenuItem): Boolean {
        if (null == item || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            android.R.id.home -> {
                baseVMActivity?.onBackPressed()
                true
            }

            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(
                    baseVMActivity,
                    CategoryHelper.CATEGORY_POSITION_GROUP_SUB_LIST_REMOTE_MAC,
                    null,
                    fragmentViewModel?.mPositionModel?.value?.mCurrentPath
                )
                OptimizeStatisticsUtil.pageSearch(OptimizeStatisticsUtil.ALL_STORAGE)
                StatisticsUtils.statisticsPageExposure(
                    activity,
                    "",
                    com.filemanager.common.constants.Constants.PAGE_SEARCH,
                    com.filemanager.common.constants.Constants.PAGE_REMOTE_FILE_LIST
                )
                true
            }

            R.id.actionbar_edit -> {
                if (actionCheckPermission().not()) {
                    baseVMActivity?.showSettingGuildDialog()
                    return true
                }
                if (fragmentViewModel?.dataLoadState?.value == OnLoaderListener.STATE_START) {
                    Log.d(TAG, "onMenuItemSelected actionbar_edit mFileLoadState = STATE_START")
                } else {
                    StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.FILE_BROWSER_EDIT)
                    OptimizeStatisticsUtil.pageEdit(OptimizeStatisticsUtil.ALL_STORAGE)
                    fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                }
                true
            }

            R.id.navigation_sort -> {
                if (fragmentViewModel?.dataLoadState?.value == OnLoaderListener.STATE_START
                    && (mAdapter?.itemCount ?: 0) == 0) {
                    Log.d(TAG, "onMenuItemSelected navigation_sort mFileLoadState = STATE_START")
                } else {
                    baseVMActivity?.let {
                        StatisticsUtils.nearMeStatistics(it, StatisticsUtils.SEQUENCE_ACTION)
                        OptimizeStatisticsUtil.pageSort(OptimizeStatisticsUtil.ALL_STORAGE)
                        val anchorView: View? = view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
                        mSortPopupController.showSortPopUp(
                            it,
                            0,
                            anchorView,
                            SortRecordModeFactory.getRemoteMacKey(),
                            object : SelectItemListener {

                                override fun onDismiss() {
                                    sortEntryView?.rotateArrow()
                                }

                                override fun onPopUpItemClick(
                                    flag: Boolean,
                                    sortMode: Int,
                                    isDesc: Boolean
                                ) {
                                    if (flag) {
                                        sortEntryView?.setSortOrder(sortMode, isDesc)
                                        fragmentViewModel?.sortReload()
                                    }
                                }
                            })
                    }
                }
                true
            }

            R.id.actionbar_scan_mode -> {
                fragmentViewModel?.clickScanModeItem(baseVMActivity)
                true
            }

            R.id.action_setting -> {
                StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.FILE_BROWSER_SETTING)
                OptimizeStatisticsUtil.pageSetting(OptimizeStatisticsUtil.ALL_STORAGE)
                Injector.injectFactory<ISetting>()?.startSettingActivity(activity)
                StatisticsUtils.statisticsPageExposure(
                    activity,
                    "",
                    com.filemanager.common.constants.Constants.PAGE_SETTING,
                    com.filemanager.common.constants.Constants.PAGE_REMOTE_FILE_LIST
                )
                true
            }

            com.filemanager.common.R.id.action_select_all -> {
                fragmentViewModel?.clickToolbarSelectAll()
                true
            }

            com.filemanager.common.R.id.action_select_cancel -> {
                if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                }
                true
            }
            else -> false
        }
    }

    override fun fromSelectPathResult(requestCode: Int, paths: List<String>?) {
        activity?.let { mFileOperateController?.onSelectPathReturn(it, requestCode, paths) }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            if (!isPreviewOpen() || requestCode != MessageConstant.MSG_EDITOR_COPY) {
                fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
            }
            fragmentViewModel?.loadData()
        }
    }

    override fun setCurrentFromOtherSide(currentPath: String) {
        mCurrentPath = currentPath
        fragmentViewModel?.mPathHelp?.updateRootPath(currentPath)
        fragmentViewModel?.setCurrentFromOtherSide(currentPath)
    }

    override fun onItemClick(
        item: com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails<Int>,
        e: MotionEvent
    ): Boolean {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if (uiModel.stateModel.listModel.value != KtConstants.LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.keyMap[item.selectionKey].apply {
                Log.d(TAG, "onItemClick baseFile=$this")
            } ?: return true
            if (baseFile.mIsDirectory) {
                fragmentRecyclerView?.paddingTop?.let { paddingTop ->
                    val viewPosition = mLayoutManager?.findFirstVisibleItemPosition() ?: 0
                    val offset = mLayoutManager?.findViewByPosition(viewPosition)?.top ?: paddingTop
                    fragmentViewModel?.onDirClick(
                        baseVMActivity,
                        baseFile,
                        viewPosition,
                        offset - paddingTop
                    )
                }
            } else {
                //这里调用预览逻辑
                val previewResult = previewClickedFile(baseFile, fragmentViewModel?.previewClickedFileLiveData)
                //非预览处理时，调用触发下载的逻辑
                if (!previewResult) {
                    activity?.let {
                        //没有预览时，调用打开逻辑
                        val remoteBean = baseFile as RemoteFileBean
                        val list = mutableListOf<RemoteFileBean>()
                        list.add(remoteBean)
                        downloadRemoteFile(it, list, true)
                    }
                }
            }
        }
        return true
    }

    private fun setNormalHomeDisplay(currentPath: String?) {
        baseVMActivity?.supportActionBar?.apply {
            Log.d(TAG, "setNormalHomeDisplay currentPath $currentPath")
            val isPathUers = isRemotePathUser(currentPath)
            if (isChildDisplay && deviceStatus != RemoteDeviceConstants.CONNECTED) {
                //父子屏下，如果设备是未连接，隐藏返回按钮
                setDisplayHomeAsUpEnabled(false)
            } else if (isPathUers) { // 在根目录
                setDisplayHomeAsUpEnabled(!isChildDisplay)
            } else {
                setDisplayHomeAsUpEnabled(true)
            }
            setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
        }
    }

    override fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.P_CHILD_DISPLAY, isChildDisplay)
        mToolbar?.let {
            setToolbarMenuVisible(it, !isChildDisplay)
            setToolbarEditIcon(it, isChildDisplay)
        }
        baseVMActivity?.supportActionBar?.apply {
            if (fragmentViewModel?.isInSelectMode() == true) {
                setDisplayHomeAsUpEnabled(true)
                setHomeAsUpIndicator(com.support.snackbar.R.drawable.coui_menu_ic_cancel)
            } else {
                setNormalHomeDisplay(fragmentViewModel?.mPositionModel?.value?.mCurrentPath)
            }
        }
    }

    override fun checkPermission() {
        baseVMActivity?.checkStoragePermission()
    }

    override fun backToTop() {
        fragmentRecyclerView?.fastSmoothScrollToTop()
    }

    override fun updatedLabel() {}

    override fun getCurrentPath(): String {
        return fragmentViewModel?.mPositionModel?.value?.mCurrentPath ?: ""
    }

    fun onRefreshData() {
        fragmentViewModel?.loadData()
    }

    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        super.updatePermissionEmptyMarginTop()
    }

    override fun getInstallPerMission() {
        Log.d(TAG, "getInstallPerMission")
        (baseVMActivity as BaseVMActivity).checkGetInstalledAppsPermission()
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    private fun previewClickedFile(
        file: BaseFileBean?,
        clickFileLiveData: MutableLiveData<BaseFileBean?>?
    ): Boolean {
        return previewOperate?.previewClickedFile(file, clickFileLiveData) ?: false
    }

    private fun previewEditedFiles(files: List<BaseFileBean>?): Boolean {
        mFileOperateController?.setPreviewOpen(isPreviewOpen())
        return previewOperate?.previewEditedFiles(files) ?: false
    }

    private fun listEmptyFile() {
        previewOperate?.listEmptyFile()
    }

    private fun isPreviewOpen(): Boolean {
        return previewOperate?.isPreviewOpen() ?: false
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_FILE_REMOTE_MAC
    }

    override fun getOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.OperateResultListener? =
        mFileOperateController?.pickResultListener()

    override fun exitSelectionMode() {
        if (isSelectionMode()) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val sideNavigationWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            sideNavigationWidth,
            ItemDecorationFactory.GRID_ITEM_DECORATION_FILE_BROWSER
        )
        return true
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            fragmentRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController =
                        SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }


    /**
     * 选中远程文件的事件：下载
     */
    private fun handleSelectRemoteFile(
        activity: ComponentActivity,
        item: MenuItem,
        selectFiles: ArrayList<BaseFileBean>?
    ): Boolean {
        if (selectFiles.isNullOrEmpty()) {
            return false
        }
        when (item.itemId) {
            com.filemanager.common.R.id.navigation_download -> downloadRemoteFile(activity, selectFiles.toList(), false)

            else -> return false
        }
        return true
    }

    private fun downloadRemoteFile(activity: ComponentActivity, downloadFiles: List<BaseFileBean>, isOpen: Boolean) {
        Log.d(TAG, "downloadCloudFile $downloadFiles")
        val downloadPaths = ArrayList<Pair<String, Long>>()
        downloadFiles.forEach {
            if (it is RemoteFileBean && !it.mIsDirectory) {
                downloadPaths.add(Pair(it.originalPath, it.mSize))
            }
        }
        val code = if (isOpen || downloadPaths.size == 1) {
            MessageConstant.MSG_OPEN_REMOTE_FILE
        } else {
            MessageConstant.MSG_DOWNLOAD_REMOTE_FILE
        }
        CategoryRemoteDeviceApi.jumpDownloadActivity(activity, deviceId ?: "", downloadPaths, code)
    }

    private fun refreshRemoteDeviceStatus() {
        Log.d(TAG, "refreshRemoteDeviceStatus")
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        remoteDeviceApi?.getDeviceList {}
    }

    private fun showDeviceConnectable() {
        Log.d(TAG, "showDeviceConnectable")
        DownloadViewModel.resetDownloadPath()
        initDeviceConnectGuideLayout(true)
        mToolbar?.title = deviceName
        val connectDes = MyApplication.appContext.resources.getString(
            com.filemanager.common.R.string.device_connected_tips,
            deviceName
        )
        deviceConnectIcon?.setImageResource(R.drawable.ic_remote_device_connectable)
        deviceConnectStatusTv?.setText(com.filemanager.common.R.string.device_can_be_connected)
        deviceConnectDesTv?.text = connectDes
        if (isSupportConnectFun) {
            deviceConnectGuideTv?.visibility = View.GONE
        } else {
            deviceConnectGuideTv?.setText(com.filemanager.common.R.string.connect_now)
            deviceConnectGuideTv?.setOnClickListener { _ ->
                val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
                val currentConnectedDevice = remoteDeviceApi?.getCurrentLinkedRemoteDiceInfo()
                if (currentConnectedDevice == null) {
                    connectDeviceOld()
                } else {
                    context?.let {
                        showConnectNewDeviceDialog(it, currentConnectedDevice.deviceName ?: "")
                    }
                }
            }
        }
        sortEntryView?.visibility = View.INVISIBLE
        mPathBar?.visibility = View.INVISIBLE
        fragmentRecyclerView?.visibility = View.INVISIBLE
    }

    private fun showConnectNewDeviceDialog(context: Context, currentConnectedDeviceName: String) {
        val title = MyApplication.appContext.resources.getString(
            com.filemanager.common.R.string.connect_new_device_title,
            deviceName
        )
        val message = MyApplication.appContext.resources.getString(
            com.filemanager.common.R.string.connect_new_device_msg,
            currentConnectedDeviceName
        )
        COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setWindowGravity(Gravity.BOTTOM)
            .setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(context))
            .setTitle(title)
            .setMessage(message)
            .setOnCancelListener { dialog ->
                dialog?.dismiss()
            }
            .setPositiveButton(com.filemanager.common.R.string.connect) { dialog, _ ->
                dialog?.dismiss()
                connectDeviceOld()
            }
            .setNegativeButton(com.filemanager.common.R.string.alert_dialog_no) { dialog, _ ->
                dialog?.dismiss()
            }.show()
    }

    private fun connectDeviceOld() {
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        baseVMActivity?.let {
            remoteDeviceApi?.connectDeviceOld(it, deviceId ?: "")
        }
    }

    private fun connectDevice(passwd: String) {
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        remoteDeviceApi?.connectDevice(deviceId ?: "", passwd) { result ->
            mainHandler.post {
                when (result) {
                    IRemoteFileLoadCallback.ERROR_CODE_PASSWORD_ERROR -> {
                        restoreConnectBtnStatus()
                        if (isSameAccount(deviceId)) {
                            deviceIsSameAccount = RemoteDeviceConstants.SAME_ACCOUNT
                            hideInputBoxContainer()
                            remoteDialogHelper?.showConnectionFailAfterRetryDialog()
                        } else {
                            remoteDialogHelper?.showPasswdErrorDialog { _, _ ->
                                createCountEditBottomDialog(isAutoShowKeyboard = true)
                            }
                        }
                    }

                    IRemoteFileLoadCallback.ERROR_CODE_CONNECTED_OTHER_PC -> {
                        restoreConnectBtnStatus()
                        Log.d(TAG, "This computer has been connected to other devices")
                        remoteDialogHelper?.showConnectionFailDialog(RemoteDeviceDialogUtils.PC_CONNECTED_TO_ANOTHER)
                    }

                    IRemoteFileLoadCallback.NOT_SUPPORT_FILE -> {
                        restoreConnectBtnStatus()
                        Log.w(TAG, "not support file")
                        withBaseVMActivity {
                            remoteDialogHelper?.showRemoteFMUnavailableDialog()
                        }
                    }

                    IRemoteFileLoadCallback.ERROR_LOCAL_CONNECTING -> {
                        restoreConnectBtnStatus()
                        Log.w(TAG, "The current device is connecting to other PC")
                        withBaseVMActivity {
                            remoteDialogHelper?.showConnectionFailDialog(RemoteDeviceDialogUtils.PHONE_CONNECTING_TO_ANOTHER)
                        }
                    }
                }

                // 刷新一下状态，同账号和非同账号UI不同, 若退出同账号后刷新UI
                refreshRemoteDeviceStatus()
            }
        }
    }

    private fun setConnBtnConnectingStatus() {
        connectButton?.isEnabled = false
        connectButton?.setText(com.filemanager.common.R.string.connecting)
        setPasswdInputBoxEnable(false)
        // 文本框变灰
        if (deviceIsSameAccount != RemoteDeviceConstants.SAME_ACCOUNT) {
            setDeviceConnectingColorGray()
        }
        hideEditPasswordIcon()
    }

    private fun restoreConnectBtnStatus() {
        Log.i(TAG, "restoreConnectBtnStatus deviceStatus=$deviceStatus")
        if (deviceStatus == RemoteDeviceConstants.DISCOVERED || deviceStatus == RemoteDeviceConstants.CONNECTING) {
            updateSideNavStatus(RemoteDeviceConstants.DISCOVERED)
        }
        if (deviceStatus == RemoteDeviceConstants.CONNECTING) {
            saveConnectStatus(false)
        }
        connectButton?.isEnabled = true
        connectButton?.setText(com.filemanager.common.R.string.connect)
        setPasswdInputBoxEnable(true)
        // 恢复文本框的颜色
        if (deviceIsSameAccount != RemoteDeviceConstants.SAME_ACCOUNT) {
            setDeviceCodeColorGray()
        }
        // 恢复编辑图标显示
        showEditPasswordIcon()
    }

    private fun showDeviceOffline() {
        Log.d(TAG, "showDeviceOffline")
        initDeviceConnectGuideLayout(false)
        mToolbar?.title = deviceName
        val oplusInterconnect = MyApplication.appContext.resources.getString(com.filemanager.common.R.string.oplus_interconnect)
        val connectDes = MyApplication.appContext.resources.getString(
            com.filemanager.common.R.string.device_offline_help_tips,
            oplusInterconnect
        )
        deviceConnectIcon?.setImageResource(R.drawable.ic_remote_device_offline)
        deviceConnectStatusTv?.setText(com.filemanager.common.R.string.device_offline)
        deviceConnectDesTv?.text = connectDes
        deviceConnectGuideTv?.visibility = View.VISIBLE
        deviceConnectGuideTv?.setText(com.filemanager.common.R.string.use_instruction)
        deviceConnectGuideTv?.setOnClickListener { _ ->
            startInstructionActivity()
        }
        hideInputBoxContainer()
        connectButton?.visibility = View.INVISIBLE
        sortEntryView?.visibility = View.INVISIBLE
        mPathBar?.visibility = View.INVISIBLE
        fragmentRecyclerView?.visibility = View.INVISIBLE
    }

    private fun startInstructionActivity() {
        baseVMActivity?.let {
            val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
            remoteDeviceApi?.startInstructionActivity(it)
        }
    }

    private fun initDeviceConnectGuideLayout(showConnectable: Boolean) {
        if (deviceConnectGuideLayout != null) {
            Log.d(TAG, "initDeviceConnectGuideLayout had initialized deviceStatus=$deviceStatus")
            deviceConnectGuideLayout?.isVisible = true
            // 判断当前是否正在连接状态
            if (deviceStatus == RemoteDeviceConstants.CONNECTING) {
                setConnBtnConnectingStatus()
            } else {
                restoreConnectBtnStatus()
            }
        } else {
            val view = deviceConnectGuideVS?.inflate()
            deviceConnectGuideLayout = view?.findViewById(R.id.remote_device_connect_guide_layout)
            deviceConnectIcon = deviceConnectGuideLayout?.findViewById(R.id.device_icon)
            deviceConnectStatusTv = deviceConnectGuideLayout?.findViewById(R.id.connect_status)
            deviceConnectDesTv = deviceConnectGuideLayout?.findViewById(R.id.status_des)
            deviceConnectGuideTv = deviceConnectGuideLayout?.findViewById(R.id.guide_tv)
            refreshButton = deviceConnectGuideLayout?.findViewById(R.id.refresh_button)
            if (isSupportConnectFun) {
                connectButton = deviceConnectGuideLayout?.findViewById(R.id.connect_button)
                deviceCodeInputView = deviceConnectGuideLayout?.findViewById(R.id.device_code)
                devicePasswdInputView = deviceConnectGuideLayout?.findViewById(R.id.device_passwd)
                editPasswdIcon = deviceConnectGuideLayout?.findViewById(R.id.remote_edit)
                accountInputBoxContainer = deviceConnectGuideLayout?.findViewById(R.id.account_input_box_container)
                view?.post {
                    initAccountInputBoxContainer()
                }
            }
            refreshButton?.setOnClickListener {
                refreshRemoteDeviceStatus()
            }
            connectButton?.setOnClickListener(connectButtonListener)
            updateWidthForButton(refreshButton)
            updateButtonBottomMargin(refreshButton)
            updateWidthForButton(connectButton)
            updateButtonBottomMargin(connectButton)

            deviceConnectGuideLayout?.bringToFront()
        }
        if (showConnectable) {
            refreshButton?.visibility = View.INVISIBLE
            connectButton?.visibility = View.VISIBLE
            if (deviceIsSameAccount == RemoteDeviceConstants.SAME_ACCOUNT) {
                hideInputBoxContainer()
            } else {
                showInputBoxContainer()
            }
        } else {
            refreshButton?.visibility = View.VISIBLE
            connectButton?.visibility = View.INVISIBLE
            hideInputBoxContainer()
        }
    }

    private fun initAccountInputBoxContainer() {
        deviceCodeInputView?.apply {
            // 设置圆角和背景色
            val view = findViewById<View>(com.support.input.R.id.single_card)
            if (view is COUIInputListSelectedItemLayout) {
                view.apply {
                    setPositionInGroup(TOP_ROUNDED_CORNER)
                    setBackgroundColor(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorContainer4))
                }
            }
            editText?.apply {
                isFocusable = false
                isFocusableInTouchMode = false
                isCursorVisible = false
                setTextColor(colorSecondary)
                setHintTextColor(colorTertiary)
            }
        }
        devicePasswdInputView?.apply {
            // 设置圆角和背景色
            val view = findViewById<View>(com.support.input.R.id.single_card)
            if (view is COUIInputListSelectedItemLayout) {
                devicePasswdItem = view
                view.apply {
                    setPositionInGroup(BOTTOM_ROUNDED_CORNER)
                    setBackgroundColor(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorContainer4))
                    setOnClickListener {
                        createCountEditBottomDialog(isAutoShowKeyboard = true)
                    }
                }
            }
            editText?.apply {
                // 设置密码框不显示明文
                inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
                // 不可点击，将点击事件穿透到父布局
                isFocusable = false
                isFocusableInTouchMode = false
                isClickable = false
                isLongClickable = false
                isCursorVisible = false
                isEnabled = false
                setTextColor(colorPrimary)
                setHintTextColor(colorTertiary)
            }
        }
        // 设置识别码
        val deviceCode = getDeviceCodeByChunk()
        deviceCodeInputView?.editText?.setText(deviceCode)
        // 设置密码
        if (isEmptyPasswd.not()) {
            devicePasswdInputView?.editText?.setText(MOCK_PASSWD)
        } else {
            devicePasswdInputView?.editText?.setText(EMPTY_PASSWD)
        }
        // 判断当前是否正在连接状态
        if (deviceStatus == RemoteDeviceConstants.CONNECTING) {
            setConnBtnConnectingStatus()
        }
    }

    /**
     * 设置密码框是否可点击
     */
    private fun setPasswdInputBoxEnable(enable: Boolean) {
        Log.d(TAG, "setPasswdInputBoxEnable $enable")
        devicePasswdItem?.isEnabled = enable
    }

    private fun setDeviceCodeColorGray() {
        deviceCodeInputView?.editText?.setTextColor(colorSecondary)
        devicePasswdInputView?.editText?.setTextColor(colorPrimary)
    }

    private fun setDeviceConnectingColorGray() {
        deviceCodeInputView?.editText?.setTextColor(colorTertiary)
        devicePasswdInputView?.editText?.setTextColor(colorTertiary)
    }

    private fun getDeviceCodeByChunk(): String {
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        return deviceId?.let {
            remoteDeviceApi?.getDeviceCode(it)?.chunked(NUM_THREE)?.joinToString(" ") ?: ""
        } ?: ""
    }

    private fun isSameAccount(deviceId: String?): Boolean {
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        return deviceId?.let {
            remoteDeviceApi?.getDeviceSameAccount(it) ?: false
        } ?: false
    }

    private fun createCountEditBottomDialog(isAutoShowKeyboard: Boolean = false) {
        fun AlertDialog.autoShowKeyboard() {
            fun EditText.callIme() {
                isFocusable = true
                requestFocus()
            }

            val view = findViewById<View>(com.filemanager.common.R.id.input_first)
            if (view is EditText) {
                view.callIme()
            } else if (view is COUIInputView) {
                view.editText.callIme()
            }
            window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
        }
        val context = context ?: return
        mBottomAlertDialogBuilder =
            COUIAlertDialogBuilder(
                context, com.filemanager.common.R.style.COUIAlertDialog_CountEdit
            ).apply {
                val formatString = getString(com.filemanager.common.R.string.mac_modify_input_psw)
                val oplusInterconnect = getString(com.filemanager.common.R.string.oplus_interconnect)
                setTitle(String.format(formatString, oplusInterconnect))
                setPositiveButton(com.filemanager.common.R.string.dialog_ok, null, false)
                setNegativeButton(com.filemanager.common.R.string.alert_dialog_no, null)
                setBlurBackgroundDrawable(true)
            }
        mBottomAlertDialog = mBottomAlertDialogBuilder!!.show()
        val positiveButton = mBottomAlertDialog?.getButton(DialogInterface.BUTTON_POSITIVE)
        positiveButton?.isEnabled = false
        positiveButton?.setOnClickListener(pwdDialogPositiveListener())
        val passwdInputView = mBottomAlertDialog?.findViewById<COUIInputView>(com.filemanager.common.R.id.input_first)
        passwdInputView?.editText?.addTextChangedListener(onTextChanged = { text, _, _, _ ->
            positiveButton?.isEnabled = text?.isNotEmpty() ?: false
        })
        mBottomAlertDialog?.takeIf { isAutoShowKeyboard }?.autoShowKeyboard()
    }

    private fun pwdDialogPositiveListener() = OnClickListener {
        val view = mBottomAlertDialog?.findViewById<COUIInputView>(com.filemanager.common.R.id.input_first)
        val passwdEdit = view?.editText?.text
        passwdEdit?.let {
            if (it.length < NUM_EIGHT) {
                view.showError(getString(com.filemanager.common.R.string.credentials_password_too_short))
            } else {
                devicePasswdInputView?.editText?.setText(it.toString())
                isChangePasswd = true
                saveEmptyPasswdStatus(false)
                mBottomAlertDialog?.dismiss()
            }
        } ?: mBottomAlertDialog?.dismiss()
    }

    private fun showInputBoxContainer() {
        accountInputBoxContainer?.visibility = View.VISIBLE
        if (deviceStatus != RemoteDeviceConstants.CONNECTING) {
            showEditPasswordIcon()
        } else {
            hideEditPasswordIcon()
        }
        if (isEmptyPasswd) {
            connectButton?.isEnabled = false
        }
    }

    private fun hideInputBoxContainer() {
        accountInputBoxContainer?.visibility = View.GONE
        hideEditPasswordIcon()
        connectButton?.isEnabled = true
    }

    private fun hideEditPasswordIcon() {
        editPasswdIcon?.visibility = View.INVISIBLE
    }

    private fun showEditPasswordIcon() {
        editPasswdIcon?.visibility = View.VISIBLE
    }

    private fun updateWidthForButton(btn: COUIButton?) {
        btn?.let {
            val layoutParams = it.layoutParams
            val context = it.context
            layoutParams.width =
                context.resources.getDimension(com.filemanager.common.R.dimen.fragment_primary_button_witch).toInt()
            it.updateLayoutParams<ConstraintLayout.LayoutParams> {
                val layoutLp = this as? ViewGroup.MarginLayoutParams
                layoutLp?.marginStart = 0
                layoutLp?.marginEnd = 0
            }
        }
    }

    private fun updateButtonBottomMargin(btn: COUIButton?) {
        btn?.let {
            //如果导航手势模式下没有taskbar，按钮的底部需要24dp+16dp(导航条空间)，其他情况为24dp
            val isNavGestureWithoutTaskBar = SystemBarUtils.isNavGestureWithoutTaskBar(rootView)
            val bottomMargin = if (isNavGestureWithoutTaskBar) {
                it.context.resources.getDimension(com.filemanager.common.R.dimen.dimen_40dp).toInt()
            } else {
                it.context.resources.getDimension(com.filemanager.common.R.dimen.dimen_24dp).toInt()
            }
            it.updateLayoutParams<ConstraintLayout.LayoutParams> {
                val layoutLp = this as? ViewGroup.MarginLayoutParams
                layoutLp?.bottomMargin = bottomMargin
            }
        }
    }

    private fun hideDeviceConnectGuideLayout() {
        Log.d(TAG, "hideDeviceConnectGuideLayout")
        if (deviceConnectGuideLayout != null) {
            deviceConnectGuideLayout?.isVisible = false
        }
        mToolbar?.title = mTitle
        sortEntryView?.visibility = View.VISIBLE
        mPathBar?.visibility = View.VISIBLE
    }

    private fun registerRemoteDeviceStatusListener() {
        if (remoteDeviceStatusListener == null) {
            remoteDeviceStatusListener = object : IRemoteDeviceStatusListener {
                override fun onDeviceAdd(list: List<RemoteDeviceInfo>) {}

                override fun onDeviceRemove(list: List<RemoteDeviceInfo>) {}

                override fun onDeviceUpdate(list: List<RemoteDeviceInfo>) {
                    var newDeviceInfo: RemoteDeviceInfo? = null
                    list.forEach {
                        if (it.deviceId?.equals(deviceId) == true) {
                            newDeviceInfo = it
                            return@forEach
                        }
                    }
                    newDeviceInfo?.let {
                        if (deviceStatus != RemoteDeviceConstants.CONNECTED && it.deviceStatus == RemoteDeviceConstants.CONNECTED) {
                            //设备状态从 可连接或已离线或正在连接 变成 已连接
                            onDeviceStatusChanged(RemoteDeviceConstants.CONNECTED)
                        } else if (deviceStatus == RemoteDeviceConstants.CONNECTED && it.deviceStatus != RemoteDeviceConstants.CONNECTED) {
                            //设备状态从 已连接 变成 可连接或已离线
                            onDeviceStatusChanged(it.deviceStatus)
                        } else if (deviceStatus == RemoteDeviceConstants.UNDISCOVERED && it.deviceStatus == RemoteDeviceConstants.DISCOVERED) {
                            //设备状态从 已离线 变成 可连接
                            onDeviceStatusChanged(RemoteDeviceConstants.DISCOVERED)
                        } else if (deviceStatus == RemoteDeviceConstants.DISCOVERED && it.deviceStatus == RemoteDeviceConstants.UNDISCOVERED) {
                            //设备状态从 可连接 变成 已离线
                            onDeviceStatusChanged(RemoteDeviceConstants.UNDISCOVERED)
                        } else if (deviceStatus == RemoteDeviceConstants.CONNECTING && it.deviceStatus == RemoteDeviceConstants.UNDISCOVERED) {
                            //设备状态从 正在连接 变成 已离线
                            onDeviceStatusChanged(RemoteDeviceConstants.UNDISCOVERED)
                        } else if (deviceIsSameAccount == RemoteDeviceConstants.SAME_ACCOUNT
                            && it.sameAccount == RemoteDeviceConstants.UN_SAME_ACCOUNT
                            && isSupportConnectFun
                        ) {
                            //设备从 同账号 状态变成 非同账号
                            deviceIsSameAccount = it.sameAccount
                            val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
                            remoteDeviceApi?.disconnect()
                            onDeviceStatusChanged(RemoteDeviceConstants.DISCOVERED)
                            mainHandler.post {
                                saveEmptyPasswdStatus(true)
                                remoteDialogHelper?.showExitAccountDialogDialog { _, _ ->
                                    createCountEditBottomDialog(isAutoShowKeyboard = true)
                                }
                            }
                        }
                        if (deviceIsSameAccount == RemoteDeviceConstants.UN_SAME_ACCOUNT
                            && it.sameAccount == RemoteDeviceConstants.SAME_ACCOUNT
                            && isSupportConnectFun
                        ) {
                            //设备从 非同账号 状态变成 同账号
                            if (deviceStatus == RemoteDeviceConstants.DISCOVERED || deviceStatus == RemoteDeviceConstants.CONNECTING) {
                                mainHandler.post {
                                    hideInputBoxContainer()
                                }
                            }
                            deviceIsSameAccount = it.sameAccount
                        }
                    }
                }
            }
        }
        remoteDeviceStatusListener?.let {
            val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
            remoteDeviceApi?.addDeviceStatusListener(it)
        }
    }

    private fun onDeviceStatusChanged(status: Int) {
        lifecycleScope.launch(Dispatchers.Main) {
            Log.d(TAG, "onDeviceStatusChanged status: $status")
            deviceStatus = status
            fragmentViewModel?.deviceStatus = status
            when (status) {
                RemoteDeviceConstants.CONNECTED -> {
                    hideDeviceConnectGuideLayout()
                    arguments?.putInt(KtConstants.P_REMOTE_DEVICE_STATUS, deviceStatus)
                    fragmentViewModel?.getInitPathForRemote()
                }
                RemoteDeviceConstants.DISCOVERED -> {
                    resetNotConnectedUI()
                    showDeviceConnectable()
                }
                RemoteDeviceConstants.UNDISCOVERED -> {
                    resetNotConnectedUI()
                    showNetworkEmptyOrDeviceOffline()
                }
                RemoteDeviceConstants.CONNECTING -> {
                    resetNotConnectedUI()
                    showDeviceConnectable()
                    setConnBtnConnectingStatus()
                    saveConnectStatus(true)
                    updateSideNavStatus(RemoteDeviceConstants.CONNECTING)
                }
            }
        }
    }

    private fun updateSideNavStatus(status: Int) {
        // 更新侧导航状态
        Log.d(TAG, "update sideNav status=$status")
        val mainApi = Injector.injectFactory<IMain>()
        deviceId?.let { devId ->
            parentFragment?.let { pf ->
                mainApi?.updateRemoteDeviceStatus(pf, devId, status)
            }
        }
    }

    /**
     * 这里根据当前是否连接网络判定是显示offline的view还是netWork的EmptyView
     */
    private fun showNetworkEmptyOrDeviceOffline() {
        DownloadViewModel.resetDownloadPath()
        if (context?.let { isNetworkAvailable(it) } == true) {
            hideEmptyView()
            mainHandler.post {
                remoteDialogHelper?.showDeviceOfflineDialog()
            }
            showDeviceOffline()
        } else {
            hideDeviceConnectGuideLayout()
            mainHandler.post {
                remoteDialogHelper?.showNoInternetDialog()
            }
            showEmptyView(Constants.ERROR_CODE_NO_NETWORK)
        }
    }

    private fun resetNotConnectedUI() {
        hideEmptyView()
        hidePreviewUI()
        if (isSelectionMode()) {
            mTitle = deviceName
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        } else {
            setNormalHomeDisplay(fragmentViewModel?.mPositionModel?.value?.mCurrentPath)
            toolbar?.menu?.clear()
        }
    }

    private fun hidePreviewUI() {
        previewOperate?.let {
            if (it.isPreviewOpen()) {
                it.closePreview()
            }
            toolbar?.menu?.findItem(com.filemanager.common.R.id.actionbar_preview)?.isVisible = false
        }
    }

    private fun unregisterRemoteDeviceStatusListener() {
        remoteDeviceStatusListener?.let {
            val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
            remoteDeviceApi?.removeDeviceStatusListener(it)
            remoteDeviceStatusListener = null
        }
    }

    override fun getRemoteDeviceStatus(): Int {
        return deviceStatus
    }


    /**
     * 重写初始化SelectionTracker方法，相比父类，多了withSelectionPredicates的调用，自定义那些可以选择，那些不能选择
     */
    override fun initSelectionTracker() {
        val recyclerView = fragmentRecyclerView ?: run {
            Log.w(TAG, "initSelectionTracker error: mRecyclerView is null")
            return
        }
        recyclerView.setMIsSupportDragSlide(true)
        runCatching {
            val keyProvider = DefaultKeyProvider(recyclerView)
            val detailsLookup = DefaultDetailsLookup(recyclerView)
            val trackerBuilder = com.oplus.dropdrag.RecycleSelectionBuilder(
                javaClass.name,
                recyclerView,
                DefaultSelectDelegate(fragmentViewModel),
                keyProvider,
                detailsLookup
            )
            trackerBuilder.withSlideSelection(true)
            trackerBuilder.withSlideSelectionStateListener(recyclerView)
            trackerBuilder.withOnDragStartListener(this)
            trackerBuilder.withOnItemClickListener(this)
            //这里是相比父类不同的点，自定义了SelectionPredicate的定义
            trackerBuilder.withSelectionPredicates(createMultiplesSelect())
            PCConnectAction.getItemTouchInterceptor()?.let {
                trackerBuilder.withOnItemTouchListener(it)
            }
            recyclerView.layoutManager?.let {
                if ((it is GridLayoutManager) && (it.spanCount > 1)) {
                    SelectionTracker.LAYOUT_TYPE.GRID
                } else SelectionTracker.LAYOUT_TYPE.LIST
            } ?: SelectionTracker.LAYOUT_TYPE.LIST
            trackerBuilder.build()
        }.onFailure { ex ->
            Log.w(TAG, "initSelectionTracker error: $ex")
        }
    }

    private fun createMultiplesSelect(): SelectionPredicates.SelectionPredicate<Int> {
        return object : SelectionPredicates.SelectionPredicate<Int>() {
            override fun canSetStateForKey(key: Int, nextState: Boolean): Boolean {
                Log.d(TAG, "canSetStateForKey key $key, nextState $nextState")
                fragmentViewModel?.uiState?.value?.let { uiModel ->
                    val baseFile = uiModel.keyMap[key].apply {
                        Log.d(TAG, "createMultiplesSelect baseFile=$this")
                    } ?: return true
                    // 这里判断是否可以设置为选中状态
                    val containIllegalChar = EmojiUtils.containsIllegalCharFileName(baseFile.mDisplayName)
                    val isDirectory = baseFile.mIsDirectory
                    val canNotSelect = containIllegalChar || isDirectory
                    //Log.d(TAG, "canSetStateForKey isDirectory $isDirectory, containIllegalChar $containIllegalChar")
                    return !canNotSelect
                }
                return true
            }

            override fun canSetStateAtPosition(position: Int, nextState: Boolean): Boolean {
                return true
            }

            override fun canSelectMultiple(): Boolean {
                return true
            }
        }
    }

    /**
     * 判断当前目录是否是根目录的上一级目录/remote_mac/Users
     * 交互要求远程mac文件在这一级的时候，中屏大屏上toolbar上不显示返回键，在直板机上显示
     */
    private fun isRemotePathUser(path: String?): Boolean {
        var isPathUsers = false
        if (path != null && path.startsWith(Constants.REMOTE_PATH_PREFIX)) {
            val splitPathNames =
                path.split(File.separator.toRegex()).dropWhile { it.isEmpty() }.toTypedArray()
            val size = splitPathNames.size
            isPathUsers = (size == USERS_PATH_ITEM_SIZE) && splitPathNames[1].equals("Users", true)
        }
        return isPathUsers
    }

    /**
     * 更新emptyContainer的高度，需要在doOnLayout中调用
     */
    private fun updateEmptyContainerHeight(includePathBar: Boolean) {
        this.context?.let {
            val rootViewHight = rootView?.measuredHeight ?: 0
            val toolbarHeight = mToolbar?.measuredHeight ?: 0
            val pathBarHeight = mPathBar?.measuredHeight ?: 0
            val pathBarLp = mPathBar?.layoutParams as LinearLayout.LayoutParams
            val marginTop = pathBarLp.topMargin
            Log.d(
                TAG, "updateEmptyContainerHeight rootViewHight $rootViewHight, " +
                        "toolbarHeight $toolbarHeight, pathBarHeight $pathBarHeight, marginTop $marginTop, includePathBar: $includePathBar"
            )
            var result = rootViewHight - toolbarHeight
            if (!includePathBar) {
                result = rootViewHight - toolbarHeight - (pathBarHeight + marginTop)
            }
            Log.d(TAG, "updateEmptyContainerHeight result $result")
            val lp = emptyContainer?.layoutParams as? ConstraintLayout.LayoutParams
            lp?.height = result
            emptyContainer?.layoutParams = lp
        }
    }
}
