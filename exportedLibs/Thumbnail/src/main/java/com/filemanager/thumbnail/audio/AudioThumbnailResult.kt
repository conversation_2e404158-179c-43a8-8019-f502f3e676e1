/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.coloros.filemanager.thumbnail.audio
 ** Version: 1.0
 ** Date: 2021/7/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.thumbnail.audio

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import androidx.annotation.Keep

/**
 * the final audio thumbnail object
 * we need to use AudioThumbnailResultByteArray to save in the file, because bitmap is not serializeable
 */
@Keep
data class AudioThumbnailResult(
    var mBitmap: Bitmap? = null
) {
    companion object {
        fun convertByteArrayToBitmap(byteArray: ByteArray?): Bitmap? {
            return if (byteArray != null) {
                BitmapFactory.decodeByteArray(byteArray, 0, byteArray.size)
            } else {
                null
            }
        }
    }
}