package com.coloros.filemanager.common.fileutils

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.JavaFileHelper
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.io.File

class JavaFileHelperTest {
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should verify when safe check without`() {

        Assert.assertEquals("test", JavaFileHelper.safeCheck({ "test" }, ""))

        Assert.assertEquals("", JavaFileHelper.safeCheck({ null }, ""))

        Assert.assertEquals("", JavaFileHelper.safeCheck({ throw Exception("test-error") }, ""))
    }


    @Test
    fun `should verify when exists without`() {
        var baseFileBean = BaseFileBean()
        baseFileBean.mData = "test44444"
        Assert.assertFalse(JavaFileHelper.exists(baseFileBean))

        try {
            every { baseFileBean.mData }.throws(Exception("test-error"))

            Assert.assertFalse(JavaFileHelper.exists(baseFileBean))
        } catch (e: Exception) {
        }

    }

    @Test
    fun `should verify when rename without`() {
        var baseFileBean = BaseFileBean()
        Assert.assertFalse(JavaFileHelper.renameTo(baseFileBean, ""))

        baseFileBean.mData = "D://test/123"
        Assert.assertFalse(JavaFileHelper.renameTo(baseFileBean, "test111112"))

        try {
            every { baseFileBean.mData }.throws(Exception("test-error"))
            Assert.assertFalse(JavaFileHelper.renameTo(baseFileBean, "test111112"))
        } catch (e: Exception) {
        }
    }

    @Test
    fun `should verify when delete without`() {
        var baseFileBean = BaseFileBean()
        baseFileBean.mData = "D://test/123"
        Assert.assertFalse(JavaFileHelper.delete(baseFileBean))

        try {
            every { baseFileBean.mData }.throws(Exception("test-error"))
            Assert.assertFalse(JavaFileHelper.delete(baseFileBean))
        } catch (e: Exception) {
        }
    }

    @Test
    fun `should verify when cut without`() {
        var sourceFile = BaseFileBean()
        var dest = BaseFileBean()
        Assert.assertFalse(JavaFileHelper.cut(sourceFile, dest))

        sourceFile.mData = "test54324"
        Assert.assertFalse(JavaFileHelper.cut(sourceFile, dest))

        dest.mData = "test654747"
        Assert.assertFalse(JavaFileHelper.cut(sourceFile, dest))

        try {
            every { sourceFile.mData }.throws(Exception("test-error"))
            Assert.assertFalse(JavaFileHelper.cut(sourceFile, dest))
        } catch (e: Exception) {
        }
    }

    @Test
    fun `should verify when copy without`() {
        var sourceFile = BaseFileBean()
        var dest = BaseFileBean()
        Assert.assertFalse(JavaFileHelper.copy(sourceFile, dest))

        sourceFile.mData = "test54324"
        Assert.assertFalse(JavaFileHelper.copy(sourceFile, dest))

        dest.mData = "test654747"
        Assert.assertFalse(JavaFileHelper.copy(sourceFile, dest))

        try {
            every { sourceFile.mData }.throws(Exception("test-error"))
            Assert.assertFalse(JavaFileHelper.copy(sourceFile, dest))
        } catch (e: Exception) {
        }
    }

    @Test
    fun `should verify when list files without`() {
        var file = BaseFileBean()
        Assert.assertNull(JavaFileHelper.listFileBeans(file))

        try {
            every { file.mData }.throws(Exception("test-error"))
            Assert.assertNotNull(JavaFileHelper.listFileBeans(file))
        } catch (e: Exception) {
        }
    }

    @Test
    fun `should verify when list java files without`() {
        var file = mockk<File>(relaxed = true)

        every { file.listFiles() }.returns(null)
        Assert.assertNull(JavaFileHelper.listFiles(file))

        every { file.listFiles() }.returns(Array(3) { File("123");File("") })
        Assert.assertNotNull(JavaFileHelper.listFiles(file))


        try {
            every { file.listFiles() }.throws(Exception("test-error"))
            Assert.assertNull(JavaFileHelper.listFiles(file))
        } catch (e: Exception) {
        }
    }

    @Test
    fun `should verify when list dir count without`() {
        var file = BaseFileBean()
        Assert.assertEquals(0, JavaFileHelper.listDirCount(file))

        file.mData = "test04095"
        Assert.assertEquals(0, JavaFileHelper.listDirCount(file))

        try {
            every { file.mData }.throws(Exception("test-error"))
            Assert.assertEquals(0, JavaFileHelper.listDirCount(file))
        } catch (e: Exception) {
        }
    }

    @Test
    fun `should verify when list files count without`() {
        var file = BaseFileBean()
        Assert.assertEquals(0, JavaFileHelper.listFilesCount(file))

        file.mData = "test04095"
        Assert.assertEquals(0, JavaFileHelper.listFilesCount(file))

        try {
            every { file.mData }.throws(Exception("test-error"))
            Assert.assertEquals(0, JavaFileHelper.listFilesCount(file))
        } catch (e: Exception) {
        }
    }


    @Test
    fun `should verify when mkdir without`() {
        var file = BaseFileBean()
        Assert.assertEquals(false, JavaFileHelper.mkdir(file))


        try {
            every { file.mData }.throws(Exception("test-error"))
            Assert.assertEquals(false, JavaFileHelper.mkdir(file))
        } catch (e: Exception) {
        }
    }

    @Test
    fun `should verify when file to tal size without`() {
        var file = BaseFileBean()

        Assert.assertEquals(0, JavaFileHelper.fileTotalSize(file))

        file.mData = "test-file1111"
        Assert.assertEquals(0, JavaFileHelper.fileTotalSize(file))


        Assert.assertEquals(0L, JavaFileHelper.fileTotalSize(File("test@11111")))
    }

    @Test
    fun `should verify when has older without`() {


        var list = arrayListOf<BaseFileBean>()

        Assert.assertFalse(JavaFileHelper.hasFolder(list))

        list.add(mockk(relaxed = true) {
            every { mIsDirectory }.returns(true)
        })
        Assert.assertTrue(JavaFileHelper.hasFolder(list))
    }

    @Test
    fun `should verify when can read without`() {
        var file = BaseFileBean()
        file.mData = "test12359"
        Assert.assertFalse(JavaFileHelper.canRead(file))

        try {
            every { file.mData }.throws(Exception("test-error"))
            Assert.assertFalse(JavaFileHelper.canRead(file))
        } catch (e: Exception) {
        }
    }


    @Test
    fun `should verify when can write without`() {
        var file = BaseFileBean()
        file.mData = "test12359"
        Assert.assertFalse(JavaFileHelper.canWrite(file))

        try {
            every { file.mData }.throws(Exception("test-error"))
            Assert.assertFalse(JavaFileHelper.canWrite(file))
        } catch (e: Exception) {
        }
    }


    @After
    fun afterTests() {
        unmockkAll()
    }
}