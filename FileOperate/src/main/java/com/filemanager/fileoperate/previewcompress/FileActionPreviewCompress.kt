/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileActionDecompress.kt
 ** Description: File decompress function
 ** Version: 1.0
 ** Date: 2020/3/9
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.previewcompress

import android.content.DialogInterface
import android.net.Uri
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.fileutils.checkDestStorageSpace
import com.filemanager.common.fileutils.copyToFileByUri
import com.filemanager.common.fileutils.deleteUriTempDirectory
import com.filemanager.common.fileutils.getCompressFileType
import com.filemanager.common.fileutils.getDisplayNameByString
import com.filemanager.common.fileutils.sUriToFileDirectoryPath
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.fileoperate.FileOperateUtil
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.base.BaseFileNameDialog
import com.filemanager.fileoperate.base.DISMISS_PROGRESS
import com.filemanager.fileoperate.base.SHOW_PROGRESS
import com.filemanager.fileoperate.compress.CompressHelperFactory
import com.filemanager.fileoperate.decompress.BaseDecompressFile
import com.filemanager.fileoperate.decompress.DISMISS_DECOMPRESS_PASSWORD_DIALOG
import com.filemanager.fileoperate.decompress.DISMISS_VERIFY_PWD_LOADING_DIALOG
import com.filemanager.fileoperate.decompress.DecompressHelper
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_FAILED
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_NOT_ENOUGH
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_NOT_EXIST
import com.filemanager.fileoperate.decompress.DecompressHelper.Companion.PREVIEW_NOT_EXIST_DFM_DISCONNECTED
import com.filemanager.fileoperate.decompress.FileDecompressObserver
import com.filemanager.fileoperate.decompress.P7ZipDecompressHelper
import com.filemanager.fileoperate.decompress.RESHOW_DECOMPRESS_PASSWORD_DIALOG
import com.filemanager.fileoperate.decompress.SHOW_DECOMPRESS_PASSWORD_DIALOG
import com.filemanager.fileoperate.decompress.SHOW_DECOMPRESS_PASSWORD_ERROR
import com.filemanager.fileoperate.decompress.SHOW_VERIFY_PWD_LOADING_DIALOG
import com.filemanager.fileoperate.decompress.VerifyPasswordQueue
import com.filemanager.fileoperate.decompress.ZipDecompressHelper
import java.io.File

class FileActionPreviewCompress : BaseFileAction<FilePreviewCompressObserver> {
    companion object {
        private const val TAG = "FileActionPreviewCompress"
        private const val DELAY_OPEN_FILE = 500L

        private const val STEP_DEFAULT = 0x00
        // 显示输入秘密的弹窗
        private const val STEP_SHOW_PASSWORD_DIALOG = 0x01
        // 验证密码
        private const val STEP_VERIFY_PASSWORD = 0x02
    }

    @VisibleForTesting
    var mSourceFile: BaseFileBean
    @VisibleForTesting
    var mDecompressHelper: DecompressHelper<*>? = null
    private var mPreviewResult: Pair<Int, Map<String, MutableList<out BaseDecompressFile>?>?>? = null
    private var mLifecycleOwner: LifecycleOwner? = null
    private var mDecompressObserver: FileDecompressObserver? = null
    private var mDestFile: BaseFileBean? = null
    private val mNeedShowCheckButton: Boolean
    @VisibleForTesting
    var passwordQueue = VerifyPasswordQueue()
    private var mLockObj = Object()
    @VisibleForTesting
    var verifyPwdStep = STEP_DEFAULT
    // 是否正在后台验证密码
    @VisibleForTesting
    @Volatile
    var isVerifyingPwd = false

    constructor(lifecycle: LifecycleOwner, sourceFile: BaseFileBean, decompressObserver: FileDecompressObserver? = null, destFile: BaseFileBean? = null, needShowCheckButton: Boolean = true) : super(lifecycle) {
        mSourceFile = sourceFile
        mDecompressObserver = decompressObserver
        mLifecycleOwner = lifecycle
        mDestFile = destFile
        mNeedShowCheckButton = needShowCheckButton
    }

    override fun run(): Boolean {
        if ((mSourceFile.mData?.isNotEmpty() == true) && !JavaFileHelper.exists(mSourceFile)) {
            Log.d(TAG, "Source file not exist")
            if (KtUtils.checkDfmFileAndDfmDisconnected(mSourceFile.mData)) {
                notifyObserver(PREVIEW_NOT_EXIST_DFM_DISCONNECTED)
            } else {
                notifyObserver(PREVIEW_NOT_EXIST)
            }
            return false
        } else if (mSourceFile.mData.isNullOrEmpty() && (mSourceFile.mLocalFileUri == null)) {
            Log.d(TAG, "Source is null")
            return false
        }
        notifyObserver(
            SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                mContext.getString(com.filemanager.common.R.string.dialog_open_compress_file), true), DELAY_OPEN_FILE)
        deleteUriTempDirectory()
        val compressType = getCompressFileType(mSourceFile)
        mDecompressHelper = CompressHelperFactory.getDecompressHelper(compressType)
        if (mDecompressHelper == null) {
            cancelNotifyObserver(SHOW_PROGRESS)
            notifyObserver(DecompressHelper.PREVIEW_UNSUPPORT)
            Log.d(TAG, "getDecompressHelper is null, compressType=$compressType")
            return false
        }
        /*
         when call from other app,it will send uri without mData,so need to copy to a temp file to decompress.
         */
        if (mSourceFile.mData.isNullOrEmpty() && (mDestFile != null)) {
            val result = copyToTempFileByUri(mSourceFile.mLocalFileUri!!, mDestFile!!)
            if (result.first && (result.second is BaseFileBean)) {
                mSourceFile.mData = result.second!!.mData
                mSourceFile.mSize = result.second!!.mSize
            } else {
                cancelNotifyObserver(SHOW_PROGRESS)
                mPreviewResult = Pair(result.third, null)
                Log.d(TAG, "checkDestStorageSpace false")
                return true
            }
        } else if (KtUtils.checkIsDfmPath(mSourceFile.mData) && compressType == CommonConstants.P7ZIP) {
            Log.d(TAG, "previewDecompress dfs 7z copyToTempFileByDfm")
            val result = FileOperateUtil.copyToTempFileByDfm(mSourceFile, BaseFileBean())
            if (result.first && (result.second is BaseFileBean)) {
                mSourceFile = result.second as BaseFileBean
                Log.d(TAG, "dfs compress copy success ${mSourceFile.mData},${mSourceFile.mSize}")
            } else {
                cancelNotifyObserver(SHOW_PROGRESS)
                mPreviewResult = Pair(result.third, null)
                Log.d(TAG, "checkDestStorageSpace false")
                return true
            }
        }
        if (compressType != CommonConstants.P7ZIP || checkPreviewNeedEncrypted()) {
            mPreviewResult = preview(passwordQueue.getVerifyPassword())

            if (mPreviewResult?.first == PREVIEW_FAILED && (mDecompressHelper is P7ZipDecompressHelper).not()) {
                mDecompressHelper = CompressHelperFactory.getDecompressHelper(CompressHelperFactory.P7ZIP)
                if (checkPreviewNeedEncrypted()) {
                    mPreviewResult = preview(passwordQueue.getVerifyPassword())
                }
            }
        }
        cancelNotifyObserver(SHOW_PROGRESS)
        return true
    }

    @VisibleForTesting
    fun preview(password: String?): Pair<Int, Map<String, MutableList<out BaseDecompressFile>?>?> {
        if (!TextUtils.isEmpty(password)) {
            P7ZipDecompressHelper.savePassword(mSourceFile.mData!!, password!!)
        }
        return mDecompressHelper!!.preview(mSourceFile)
    }

    override fun afterRun(result: Boolean) {
        if (result && (mPreviewResult != null)) {
            when (mPreviewResult!!.first) {
                PREVIEW_FAILED -> {
                    notifyObserver(PREVIEW_FAILED)
                }
                DecompressHelper.PREVIEW_SUCCESS -> {
                    if (mPreviewResult!!.second != null) {
                        notifyObserver(ACTION_DONE, Pair<String, Any>(mSourceFile.mData!!, mPreviewResult!!.second!!))
                    } else {
                        notifyObserver(ACTION_FAILED, PREVIEW_FAILED)
                    }
                }
                DecompressHelper.PREVIEW_OVERCOUNT, DecompressHelper.PREVIEW_OVERSIZE -> {
                    notifyObserver(mPreviewResult!!.first)
                }
                PREVIEW_NOT_ENOUGH -> {
                    CustomToast.showShort(com.filemanager.common.R.string.storage_space_not_enough)
                    notifyObserver(PREVIEW_NOT_ENOUGH)
                }
            }
        } else {
            notifyObserver(ACTION_FAILED, PREVIEW_FAILED)
        }
    }

    override fun recycle() {
        mLifecycleOwner = null
        mDecompressObserver = null
    }

    override fun onCancelled() {
        notifyLockReleased()
        super.onCancelled()
    }

    @WorkerThread
    private fun copyToTempFileByUri(uri: Uri, destFile: BaseFileBean): Triple<Boolean, BaseFileBean?, Int> {
        try {
            val fileDescriptor = MyApplication.sAppContext.contentResolver.openFileDescriptor(uri, "r")
            val size = fileDescriptor?.statSize ?: 0
            fileDescriptor?.close()
            val storageState = checkDestStorageSpace(destFile, size)
            Log.d(TAG, "copyToTempFileByUri storageState.first = ${storageState.first}")
            if (!storageState.first) {
                val disPlayName = getDisplayNameByString(uri.toString())
                if (disPlayName.indexOf(".") == -1) {
                    disPlayName.plus(".")
                }
                val tempFile = File(sUriToFileDirectoryPath).resolve(disPlayName).toPath()
                if (copyToFileByUri(uri, tempFile, (size > ZipDecompressHelper.COPY_BUF_SIZE))) {
                    val baseFileBean = BaseFileBean()
                    baseFileBean.mData = tempFile.toString()
                    baseFileBean.mSize = size
                    return Triple(true, baseFileBean, DecompressHelper.PREVIEW_SUCCESS)
                }
            } else {
                return Triple(false, null, PREVIEW_NOT_ENOUGH)
            }
        } catch (e: Exception) {
            Log.e(TAG, "copyFileWithUri e=$e")
        }
        return Triple(false, null, PREVIEW_FAILED)
    }

    @VisibleForTesting
    fun checkPreviewNeedEncrypted(): Boolean {
        android.util.Log.d(TAG, "checkPreviewNeedEncrypted start")
        if (mDecompressHelper != null && (mDecompressHelper is P7ZipDecompressHelper) &&
                 (mDecompressHelper as P7ZipDecompressHelper).checkPreviewIsEncrypted(mSourceFile)) {

            doVerifyPwdStep(STEP_SHOW_PASSWORD_DIALOG)
            while (!isCancelled()) {
                if (!waitLockRelease()) {
                    Log.e(TAG, "checkPreviewNeedEncrypted wait error")
                    return false
                }
                if (isCancelled()) {
                    Log.e(TAG, "checkPreviewNeedEncrypted cancel")
                    return false
                }
                break
            }
            Log.d(TAG, "checkPreviewNeedEncrypted finish")
        }
        cancelShowCalculateProgress()
        return true
    }

    @VisibleForTesting
    fun doVerifyPwdStep(step: Int) {
        Log.e(TAG, "doVerifyPwdStep ${Thread.currentThread().name} step:$step")
        verifyPwdStep = step
        when (step) {
            STEP_SHOW_PASSWORD_DIALOG -> {
                // 弹输入密码框,输入密码
                showPasswordDialog()
            }
            STEP_VERIFY_PASSWORD -> {
                // 验证密码loading弹窗
                showVerifyPwdLoading() {
                    // 回到输入密码弹窗
                    Log.e(TAG, "doVerifyPwdStep cancel verify password, reshow password dialog")
                    verifyPwdStep = STEP_SHOW_PASSWORD_DIALOG
                    notifyObserver(RESHOW_DECOMPRESS_PASSWORD_DIALOG, passwordQueue.getLastedPassword())
                    passwordQueue.cancel()
                }
                if (isVerifyingPwd) {
                    Log.e(TAG, "doVerifyPwdStep Current is verifying pwd, only show loading dialog")
                    return
                }
                isVerifyingPwd = true
                runOnAsyncThread {
                    // 验证密码
                    var verifyResult = false
                    while (passwordQueue.hasNext()) {
                        val password = passwordQueue.next()
                        verifyResult = verifyPassword(password)
                        Log.d(TAG, "doVerifyPwdStep verify $password result:$verifyResult 剩余:${passwordQueue.size()}")
                    }
                    isVerifyingPwd = false

                    if (verifyResult) {
                        Log.d(TAG, "doVerifyPwdStep verify success, step should is $STEP_VERIFY_PASSWORD current:$verifyPwdStep")
                        dismissVerifyPwdLoading(false)
                        if (verifyPwdStep == STEP_VERIFY_PASSWORD) {
                            notifyLockReleased()
                        }
                    } else {
                        Log.e(TAG, "doVerifyPwdStep verify password failed")
                        dismissVerifyPwdLoading(true)
                        notifyObserver(SHOW_DECOMPRESS_PASSWORD_ERROR)
                    }
                }
            }
        }
    }

    /**
     * 显示输入密码的弹窗
     */
    @VisibleForTesting
    fun showPasswordDialog() {
        cancelShowCalculateProgress()
        val passwordDialogBean = FileDecompressObserver.PasswordDialogBean(null)
        passwordDialogBean.resultListener = object : BaseFileNameDialog.OnButtonClickListener {
            override fun onClick(dialog: AlertDialog, buttonId: Int, inputValue: String?) {
                when (buttonId) {
                    DialogInterface.BUTTON_POSITIVE -> {
                        passwordQueue.addPassword(inputValue)
                        doVerifyPwdStep(STEP_VERIFY_PASSWORD)
                    }
                    else -> {
                        passwordQueue.cancelAll()
                        passwordDialogBean.recycle()
                        cancel()
                    }
                }
            }
        }
        notifyObserver(SHOW_DECOMPRESS_PASSWORD_DIALOG, passwordDialogBean)
    }

    /**
     * 验证密码
     */
    @VisibleForTesting
    fun verifyPassword(password: String?): Boolean {
        return mDecompressHelper!!.verifyPassword(mSourceFile, password)
    }

    /**
     * 显示验证密码loading弹窗
     */
    @VisibleForTesting
    fun showVerifyPwdLoading(cancelAction: DialogInterface.OnCancelListener) {
        Log.d(TAG, "showVerifyPwdLoading show loading dialog")
        notifyObserver(DISMISS_DECOMPRESS_PASSWORD_DIALOG)
        notifyObserver(SHOW_VERIFY_PWD_LOADING_DIALOG, cancelAction, DELAY_OPEN_FILE)
    }

    /**
     * 隐藏验证密码loading弹窗
     */
    @VisibleForTesting
    fun dismissVerifyPwdLoading(showPwdDialog: Boolean = false) {
        Log.e(TAG, "dismissVerifyPwdLoading hide loading dialog，show password dialog：$showPwdDialog")
        cancelNotifyObserver(SHOW_VERIFY_PWD_LOADING_DIALOG)
        notifyObserver(DISMISS_VERIFY_PWD_LOADING_DIALOG)
        if (showPwdDialog) {
            notifyObserver(RESHOW_DECOMPRESS_PASSWORD_DIALOG, passwordQueue.getLastedPassword())
        }
    }

    @VisibleForTesting
    fun cancelShowCalculateProgress() {
        cancelNotifyObserver(SHOW_PROGRESS)
        notifyObserver(DISMISS_PROGRESS)
    }

    @VisibleForTesting
    fun notifyLockReleased() {
        try {
            synchronized(mLockObj) {
                mLockObj.notify()
            }
        } catch (e: Exception) {
            Log.d(TAG, "notifyLockReleased: ${e.message}")
        }
    }

    @VisibleForTesting
    fun waitLockRelease(): Boolean {
        try {
            synchronized(mLockObj) {
                mLockObj.wait()
            }
        } catch (e: Exception) {
            return false
        }
        return true
    }
}