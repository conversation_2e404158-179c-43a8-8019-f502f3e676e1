/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileCloudDriverObserver.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.clouddriver

import android.content.Context
import android.view.ContextThemeWrapper
import com.filemanager.common.constants.CommonConstants.MAX_SEND_COUNT
import com.filemanager.common.utils.CustomToast
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.share.EMPTY_FOLDER_STATE
import com.filemanager.fileoperate.share.FileShareObserver
import com.filemanager.fileoperate.share.TOO_MANY_FILES_STATE
import com.filemanager.fileoperate.R

open class FileCloudDriverObserver(context: ContextThemeWrapper) : FileShareObserver(context) {

    companion object {
        const val TAG = "FileCloudDriverObserver"
    }

    override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
        return when (result.first) {
            ACTION_FAILED -> {
                when (result.second) {
                    TOO_MANY_FILES_STATE -> {
                        CustomToast.showShort(context.getString(com.filemanager.common.R.string.toast_upload_beyond_count_new, MAX_SEND_COUNT))
                    }
                    EMPTY_FOLDER_STATE -> CustomToast.showShort(com.filemanager.common.R.string.toast_upload_file_error)
                }
                false
            }
            else -> false
        }
    }
}