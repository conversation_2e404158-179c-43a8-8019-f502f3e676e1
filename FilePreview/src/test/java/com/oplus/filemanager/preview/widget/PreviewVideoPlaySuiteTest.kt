package com.oplus.filemanager.preview.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.PixelFormat
import android.graphics.RectF
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.test.platform.app.InstrumentationRegistry
import com.filemanager.common.utils.Log
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class PreviewVideoPlaySuiteTest {

    private lateinit var context: Context
    private lateinit var previewVideoPlaySuite: PreviewVideoPlaySuite

    @Before
    fun setUp() {
        context = InstrumentationRegistry.getInstrumentation().targetContext
        previewVideoPlaySuite = PreviewVideoPlaySuite(context)
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } returns Unit
    }

    @Test
    fun testSetVideoSize_Landscape() {
        val width = 1920
        val height = 1080
        previewVideoPlaySuite.setVideoSize(width, height)

        val videoSurface = previewVideoPlaySuite.videoSurface
        val lp = videoSurface.layoutParams as ConstraintLayout.LayoutParams
        assertEquals("H,1920:1080", lp.dimensionRatio)
    }

    @Test
    fun testSetVideoSize_Portrait() {
        val width = 1080
        val height = 1920
        previewVideoPlaySuite.setVideoSize(width, height)

        val videoSurface = previewVideoPlaySuite.videoSurface
        val lp = videoSurface.layoutParams as ConstraintLayout.LayoutParams
        assertEquals("W,1080:1920", lp.dimensionRatio)
    }

    @Test
    fun testOnClickVideo_ToggleVisibility() {
        previewVideoPlaySuite.videoPlayBar.visibility = View.VISIBLE
        previewVideoPlaySuite.videoSurface.performClick()
        assertEquals(View.INVISIBLE, previewVideoPlaySuite.videoPlayBar.visibility)

        previewVideoPlaySuite.videoSurface.performClick()
        assertEquals(View.VISIBLE, previewVideoPlaySuite.videoPlayBar.visibility)
    }
}