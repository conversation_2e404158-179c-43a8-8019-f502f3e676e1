/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DocumentDownloadService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/9 15:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/9       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.download

import android.content.Intent
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.utils.Log

class DocumentDownloadService : LifecycleService(), DocumentDownloadDispatcher.Callback {

    private var downloadDispatcher: DocumentDownloadDispatcher? = null
    private var isShutdown: Boolean = false

    override fun onCreate() {
        super.onCreate()
        initDownloadDispatcher()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        if (isShutdown) {
            Log.d(TAG, "onStartCommand -> re-initial dispatcher after service shut down")
            initDownloadDispatcher()
        }
        intent?.let {
            downloadDispatcher?.onStartCommand(it)
        }
        return START_NOT_STICKY
    }

    override fun stop() {
        Log.d(TAG, "stop")
        isShutdown = true
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
    }

    private fun initDownloadDispatcher() {
        downloadDispatcher = DocumentDownloadDispatcher(this, lifecycleScope)
        downloadDispatcher?.setCallback(this)
    }

    companion object {
        private const val TAG = "DocumentDownloadService"
    }
}