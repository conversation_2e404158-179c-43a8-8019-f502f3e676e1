package com.filemanager.common.base;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.filemanager.common.utils.Log;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLooper;

import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * FileAsyncTask的单元测试类
 * 使用Robolectric框架进行Android环境模拟测试
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 29)
public class FileAsyncTaskTest {

    @Mock
    private Executor mockExecutor;  // 模拟的线程执行器
    @Mock
    private Log mockLog;  // 模拟的日志工具

    private TestFileAsyncTask task;  // 被测试的异步任务实例
    private Handler handler;  // 主线程Handler

    /**
     * 测试前的初始化方法
     * 1. 初始化Mock对象
     * 2. 创建测试任务实例
     * 3. 暂停主线程Looper以便控制消息处理
     */
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        task = new TestFileAsyncTask();
        handler = new Handler(Looper.getMainLooper());
        ShadowLooper.pauseMainLooper();  // 暂停主线程Looper以便手动控制
    }

    /**
     * 测试初始状态是否为PENDING
     */
    @Test
    public void testGetStatus_initialState_shouldBePending() {
        assertEquals(FileAsyncTask.Status.PENDING, task.getStatus());
    }

    /**
     * 测试当任务状态不是PENDING时执行executeOnExecutor应返回自身
     */
    @Test
    public void testExecuteOnExecutor_whenNotPending_shouldReturnSelf() {
        task.executeOnExecutor(mockExecutor); // 先执行一次让状态变为RUNNING
        FileAsyncTask result = task.executeOnExecutor(mockExecutor);
        assertEquals(task, result);
    }

    /**
     * 测试当任务状态是PENDING时执行executeOnExecutor应改变状态为RUNNING
     */
    @Test
    public void testExecuteOnExecutor_whenPending_shouldChangeStatusToRunning() {
        task.executeOnExecutor(mockExecutor);
        assertEquals(FileAsyncTask.Status.RUNNING, task.getStatus());
    }

    /**
     * 测试执行executeOnExecutor时应调用onPreExecute方法
     */
    @Test
    public void testExecuteOnExecutor_shouldCallOnPreExecute() {
        task.executeOnExecutor(mockExecutor);
        assertTrue(task.onPreExecuteCalled);
    }

    /**
     * 测试cancel方法应设置取消标志
     */
    @Test
    public void testCancel_shouldSetCancelledFlag() {
        task.cancel(true);
        assertTrue(task.isCancelled());
    }

    /**
     * 测试当任务未被调用时postResultIfNotInvoked应发送结果
     */
    @Test
    public void testPostResultIfNotInvoked_whenNotInvoked_shouldPostResult() {
        task.mTaskInvoked.set(false);
        String result = "test";
        task.postResultIfNotInvoked(result);
        ShadowLooper.runUiThreadTasks();  // 执行主线程任务
        assertTrue(task.finishCalled);
    }

    /**
     * 测试postResult方法应发送消息到Handler
     */
    @Test
    public void testPostResult_shouldSendMessageToHandler() {
        String result = "test";
        task.postResult(result);
        ShadowLooper.runUiThreadTasks();
        assertTrue(task.finishCalled);
    }

    /**
     * 测试当任务未取消时publishProgress应发送进度消息
     */
    @Test
    public void testPublishProgress_whenNotCancelled_shouldSendMessage() {
        task.publishProgress("progress");
        ShadowLooper.runUiThreadTasks();
        assertTrue(task.onProgressUpdateCalled);
    }

    /**
     * 测试当任务已取消时publishProgress不应发送消息
     */
    @Test
    public void testPublishProgress_whenCancelled_shouldNotSendMessage() {
        task.cancel(true);
        task.publishProgress("progress");
        ShadowLooper.runUiThreadTasks();
        assertFalse(task.onProgressUpdateCalled);
    }

    /**
     * 测试当任务已取消时finish应调用onCancelled方法
     */
    @Test
    public void testFinish_whenCancelled_shouldCallOnCancelled() {
        task.cancel(true);
        task.finish("result");
        assertTrue(task.onCancelledCalled);
    }

    /**
     * 测试当任务未取消时finish应调用onPostExecute方法
     */
    @Test
    public void testFinish_whenNotCancelled_shouldCallOnPostExecute() {
        task.finish("result");
        assertTrue(task.onPostExecuteCalled);
    }

    /**
     * 用于测试的FileAsyncTask子类
     * 重写相关方法以便验证调用情况
     */
    private static class TestFileAsyncTask extends FileAsyncTask<Void, String, String> {
        boolean onPreExecuteCalled = false;  // onPreExecute是否被调用
        boolean onPostExecuteCalled = false;  // onPostExecute是否被调用
        boolean onProgressUpdateCalled = false;  // onProgressUpdate是否被调用
        boolean onCancelledCalled = false;  // onCancelled是否被调用
        boolean finishCalled = false;  // finish是否被调用

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            onPreExecuteCalled = true;
        }

        @Override
        protected void onPostExecute(String result) {
            super.onPostExecute(result);
            onPostExecuteCalled = true;
        }

        @Override
        protected void onProgressUpdate(String... values) {
            super.onProgressUpdate(values);
            onProgressUpdateCalled = true;
        }

        @Override
        protected void onCancelled(String result) {
            super.onCancelled(result);
            onCancelledCalled = true;
        }

        @Override
        protected String doInBackground(Void... params) {
            return "result";
        }

        @Override
        void finish(String result) {
            super.finish(result);
            finishCalled = true;
        }
    }
}