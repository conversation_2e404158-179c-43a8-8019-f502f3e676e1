/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OkHttpUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/8/11 10:16
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/8/11       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import android.content.Context
import com.filemanager.common.utils.Log
import com.oplus.filemanager.drivebrowser.data.interceptors.DownloadInterceptor
import com.oplus.filemanager.drivebrowser.data.interceptors.RequestInterceptor
import com.oplus.filemanager.drivebrowser.download.DownloadProgressListener
import okhttp3.ConnectionPool
import okhttp3.Dispatcher
import okhttp3.HttpUrl.Companion.toHttpUrl
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Protocol
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import java.io.IOException
import java.util.concurrent.TimeUnit

private const val TAG = "OkHttpUtils"
private val JSON_TYPE: MediaType? = "application/json".toMediaTypeOrNull()

private const val CONNECT_POOL_MAX_IDLE_COUNT = 2
private const val CONNECT_POOL_KEEP_ALIVE = 10L

private var okHttpClient = OkHttpClient.Builder()
    .dispatcher(Dispatcher())
    .connectionPool(ConnectionPool())
    .build()

fun doPut(url: String, jsonString: String?, headers: Map<String, String>): Response? {
    return doPut(url, jsonString, headers, 0, 0)
}

@Suppress("TooGenericExceptionCaught")
fun doPut(
    url: String,
    json: String?,
    headers: Map<String, String>?,
    readTimeout: Long,
    connectTimeout: Long
): Response? {
    val requestBody = json?.toRequestBody(JSON_TYPE) ?: "".toRequestBody(JSON_TYPE)
    val requestBuilder = Request.Builder().url(url)
    if (headers.isNullOrEmpty().not()) {
        headers?.entries?.forEach { header ->
            requestBuilder.addHeader(header.key, header.value)
        }
    }
    val request = requestBuilder.put(requestBody).build()
    if (readTimeout > 0) {
        okHttpClient = okHttpClient.newBuilder().readTimeout(readTimeout, TimeUnit.MILLISECONDS).build()
    }
    if (connectTimeout > 0) {
        okHttpClient = okHttpClient.newBuilder().connectTimeout(connectTimeout, TimeUnit.MILLISECONDS).build()
    }
    val call = okHttpClient.newCall(request)
    var response: Response? = null
    try {
        response = call.execute()
    } catch (e: Exception) {
        Log.e(TAG, "doPut -> error = $e")
    }
    return response
}

@Throws(IOException::class)
fun doGet(url: String, params: Map<String, String>?): Response? {
    return doGet(url, params, null, 0, 0)
}

@Throws(IOException::class)
fun doGet(
    url: String,
    params: Map<String, String>?,
    headers: Map<String, String>?,
    downloadProgressListener: DownloadProgressListener? = null,
    downloadController: DownloadController? = null
): Response? {
    return doGet(url, params, headers, 0, 0, downloadProgressListener, downloadController)
}

@Throws(IOException::class)
fun doGet(
    url: String,
    params: Map<String, String>?,
    readTimeout: Long,
    connectTimeout: Long,
    downloadProgressListener: DownloadProgressListener? = null
): Response? {
    return doGet(url, params, null, readTimeout, connectTimeout, downloadProgressListener)
}

@Suppress("LongParameterList", "TooGenericExceptionCaught")
fun doGetWithRequestInterceptor(
    context: Context,
    url: String,
    params: Map<String, String>?,
    headers: Map<String, String>?,
    readTimeout: Long,
    connectTimeout: Long,
    downloadProgressListener: DownloadProgressListener? = null,
    downloadController: DownloadController? = null
): Response? {
    var client = OkHttpClient.Builder()
        .protocols(listOf(Protocol.HTTP_1_1))
        .connectionPool(ConnectionPool(CONNECT_POOL_MAX_IDLE_COUNT, CONNECT_POOL_KEEP_ALIVE, TimeUnit.SECONDS))
        .build()
    val finalUrl: String
    try {
        val urlBuilder = url.toHttpUrl().newBuilder()
        if (params.isNullOrEmpty().not()) {
            params?.entries?.forEach { param ->
                urlBuilder.addQueryParameter(param.key, param.value)
            }
        }
        val requestBuilder = Request.Builder()
        finalUrl = urlBuilder.build().toString()
        requestBuilder.url(finalUrl)
        if (headers.isNullOrEmpty().not()) {
            headers?.entries?.forEach { header ->
                requestBuilder.addHeader(header.key, header.value)
            }
        }
        if (readTimeout > 0) {
            client = client.newBuilder().readTimeout(readTimeout, TimeUnit.MILLISECONDS).build()
        }
        if (connectTimeout > 0) {
            client = client.newBuilder().connectTimeout(readTimeout, TimeUnit.MILLISECONDS).build()
        }
        val logger = HttpLoggingInterceptor().apply { level = HttpLoggingInterceptor.Level.HEADERS }
        downloadProgressListener?.let {
            client = client.newBuilder()
                .addInterceptor(logger)
                .addInterceptor(DownloadInterceptor(it, downloadController!!))
                .addInterceptor(RequestInterceptor(context))
                .build()
        }
        val call = client.newCall(requestBuilder.build())
        return call.execute()
    } catch (e: Exception) {
        Log.e(TAG, "doGet -> error", e)
    }
    return null
}

@Suppress("LongParameterList", "TooGenericExceptionCaught")
fun doGet(
    url: String,
    params: Map<String, String>?,
    headers: Map<String, String>?,
    readTimeout: Long,
    connectTimeout: Long,
    downloadProgressListener: DownloadProgressListener? = null,
    downloadController: DownloadController? = null
): Response? {
    val finalUrl: String
    try {
        val urlBuilder = url.toHttpUrl().newBuilder()
        if (params.isNullOrEmpty().not()) {
            params?.entries?.forEach { param ->
                urlBuilder.addQueryParameter(param.key, param.value)
            }
        }
        val requestBuilder = Request.Builder()
        finalUrl = urlBuilder.build().toString()
        requestBuilder.url(finalUrl)
        if (headers.isNullOrEmpty().not()) {
            headers?.entries?.forEach { header ->
                requestBuilder.addHeader(header.key, header.value)
            }
        }
        if (readTimeout > 0) {
            okHttpClient = okHttpClient.newBuilder().readTimeout(readTimeout, TimeUnit.MILLISECONDS).build()
        }
        if (connectTimeout > 0) {
            okHttpClient = okHttpClient.newBuilder().connectTimeout(readTimeout, TimeUnit.MILLISECONDS).build()
        }
        downloadProgressListener?.let {
            okHttpClient = okHttpClient.newBuilder().addInterceptor(DownloadInterceptor(it, downloadController!!)).build()
        }
        val call = okHttpClient.newCall(requestBuilder.build())
        return call.execute()
    } catch (e: Exception) {
        Log.e(TAG, "doGet -> error = $e")
    }
    return null
}
