/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ScreenStatusUtils.kt
 * Description:
 *     The utils to detect screen status.
 *
 * Version: 1.0
 * Date: 2024-06-04
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-06-04   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.utils

import android.content.Context
import android.content.res.Configuration
import android.hardware.display.DisplayManager
import android.provider.Settings
import android.view.Display
import android.view.Surface
import androidx.annotation.VisibleForTesting
import com.oplus.coreapp.appfeature.AppFeatureProviderUtils

internal object ScreenStatusUtils {
    private const val TAG = "ScreenStatusUtils"

    private const val FEATURE_FLIP_DEVICE = "com.android.settings.flip_device"
    private const val FEATURE_TABLET = "oplus.hardware.type.tablet"

    private const val SETTINGS_SYSTEM_FOLDING_MODE = "oplus_system_folding_mode"

    @VisibleForTesting
    internal const val NOT_FOLDABLE = -1

    @VisibleForTesting
    internal const val FOLD_CLOSE = 0

    @VisibleForTesting
    internal const val FOLD_OPEN = 1

    private const val DISPLAY_CATEGORY = "android.hardware.display.category.ALL_INCLUDING_DISABLED"
    private const val DEFAULT_DISPLAY = 0
    private const val SECONDARY_DISPLAY = 1

    @JvmStatic
    fun isTablet(context: Context): Boolean {
        return hasFeature(context, FEATURE_TABLET, appFeature = false)
    }

    @JvmStatic
    fun isFlipDevice(context: Context): Boolean {
        return hasFeature(context, FEATURE_FLIP_DEVICE, appFeature = true)
    }

    @JvmStatic
    fun isUnfold(context: Context): Boolean {
        if (isFlipDevice(context)) {
            return false
        }
        return getFoldState(context) == FOLD_OPEN
    }

    @VisibleForTesting
    @JvmStatic
    internal fun getFoldState(context: Context): Int =
        Settings.Global.getInt(context.contentResolver, SETTINGS_SYSTEM_FOLDING_MODE, NOT_FOLDABLE)

    @JvmStatic
    fun isFocusSecondaryDisplay(context: Context): Boolean =
        getDisplayType(context) == SECONDARY_DISPLAY

    @JvmStatic
    private fun getDisplayType(context: Context): Int {
        if (!SdkUtils.isAtLeastT()) {
            return DEFAULT_DISPLAY
        }
        if (!isFlipDevice(context)) {
            Log.i(TAG, "getDisplayType: use default display since not flip")
            return DEFAULT_DISPLAY
        }
        val dm = context.getSystemService(DisplayManager::class.java) ?: return DEFAULT_DISPLAY
        val defaultDisplay = dm.getDisplay(Display.DEFAULT_DISPLAY)
        if (defaultDisplay.state != Display.STATE_OFF) {
            Log.i(TAG, "getDisplayType: focus default display")
            return DEFAULT_DISPLAY
        }
        for (d in dm.getDisplays(DISPLAY_CATEGORY)) {
            if (d.displayId != Display.DEFAULT_DISPLAY && d.name == defaultDisplay.name) {
                Log.i(TAG, "getDisplayType: focus second display:$d")
                return SECONDARY_DISPLAY
            }
        }
        return DEFAULT_DISPLAY
    }

    @JvmStatic
    fun isLandscape(context: Context): Boolean {
        return context.resources.configuration?.orientation == Configuration.ORIENTATION_LANDSCAPE
    }

    @JvmStatic
    fun getScreenRotation(context: Context): Int {
        val dm = context.getSystemService(DisplayManager::class.java) ?: return Surface.ROTATION_0
        return dm.getDisplay(Display.DEFAULT_DISPLAY).rotation
    }

    @JvmStatic
    private fun hasFeature(context: Context, key: String, appFeature: Boolean): Boolean {
        val result = kotlin.runCatching {
            if (!SdkUtils.isAtLeastR()) {
                context.packageManager.hasSystemFeature(key)
            } else if (appFeature) {
                AppFeatureProviderUtils.isFeatureSupport(context.contentResolver, key)
            } else {
                CompatUtils.compactSApi({
                    com.oplus.content.OplusFeatureConfigManager.getInstance().hasFeature(key)
                }, {
                    com.heytap.addon.content.OplusFeatureConfigManager.getInstance(context).hasFeature(key)
                })
            }
        }.onFailure {
            Log.w(TAG, "hasFeature failed: $key, $appFeature, ${it.message}")
        }.getOrDefault(false)
        Log.d(TAG, "hasFeature: appFeature=$appFeature, key=$key, result=$result")
        return result
    }
}