/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - DocThumbnailLoaderLifecycleTest.kt
 * Description:
 * The test cases for DocThumbnailLoaderLifecycle
 *
 * Version: 1.0
 * Date: 2024-08-06
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-08-06   1.0    Create this module
 *********************************************************************************/
package com.filemanager.thumbnail.doc

import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class DocThumbnailLoaderLifecycleTest : Assert() {

    @Before
    fun setUp() {
        mockkStatic(DocThumbnailLoaderFactory::class, DocThumbnailLoaderLifecycle::class)
        mockkObject(DocThumbnailLoaderLifecycle, DocThumbnailLoaderFactory)
        justRun { DocThumbnailLoaderFactory.release() }
        DocThumbnailLoaderLifecycle.activeCount = 0
        every {
            DocThumbnailLoaderLifecycle.asyncExecute(any())
        } answers { firstArg<() -> Unit>().invoke() }
    }

    @After
    fun tearDown() {
        unmockkStatic(DocThumbnailLoaderFactory::class, DocThumbnailLoaderLifecycle::class)
        unmockkObject(DocThumbnailLoaderLifecycle, DocThumbnailLoaderFactory)
        DocThumbnailLoaderLifecycle.activeCount = 0
    }

    @Test
    fun `should not release when onActivityDestroyed if still has active activity`() {
        // Given
        DocThumbnailLoaderLifecycle.onActivityCreated(mockk(), null)
        DocThumbnailLoaderLifecycle.onActivityCreated(mockk(), null)

        // When
        DocThumbnailLoaderLifecycle.onActivityDestroyed(mockk())

        // Then
        verify(inverse = true) { DocThumbnailLoaderFactory.release() }
    }

    @Test
    fun `should release when onActivityDestroyed if no active activity`() {
        // Given
        DocThumbnailLoaderLifecycle.onActivityCreated(mockk(), null)

        // When
        DocThumbnailLoaderLifecycle.onActivityDestroyed(mockk())

        // Then
        verify { DocThumbnailLoaderFactory.release() }
    }
}