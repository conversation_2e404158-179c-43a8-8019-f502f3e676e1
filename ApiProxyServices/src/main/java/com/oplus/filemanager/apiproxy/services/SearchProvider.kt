/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchProvider
 ** Description : 给泛在服务的提供搜索能力的Provider
 ** Version     : 1.0
 ** Date        : 2024/06/17 11:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/06/17       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.apiproxy.services

import android.content.ContentProvider
import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import android.os.CancellationSignal
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.log
import com.oplus.filemanager.apiproxy.services.cache.SearchFileSizeCache
import com.oplus.filemanager.apiproxy.services.cursor.SearchCursor
import com.oplus.filemanager.apiproxy.services.loader.SearchLoaderFactory
import com.oplus.filemanager.apiproxy.services.utils.SearchChecker
import com.oplus.filemanager.interfaze.dmpsearch.IDmpSearchApi
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

class SearchProvider : ContentProvider() {

    companion object {
        private const val TAG = "SearchProvider"

        /**
         * 搜索的分类类型
         */
        private const val KEY_CATEGORY = "key_category"
    }

    private var contentResolver: ContentResolver? = null

    private var dmpSdkInitSuc = false

    override fun onCreate(): Boolean {
        SearchFileSizeCache.clear()
        GlobalScope.launch {
            val start = System.currentTimeMillis()
            Log.d(TAG, "onCreate initDmpSearch from SearchProvider START")
            dmpSdkInitSuc = context?.let { initDmpSearch(it) } ?: false
            val end = System.currentTimeMillis()
            Log.d(TAG, "onCreate initDmpSearch from SearchProvider END delta ${end - start}")
        }
        initRemoteDevice(context)
        contentResolver = context?.contentResolver
        return true
    }

    private fun initRemoteDevice(context: Context?) {
        Log.d(TAG, "initRemoteDevice")
        val remoteApi: IRemoteDevice? = Injector.injectFactory<IRemoteDevice>()
        context?.let {
            remoteApi?.init(it)
        }
    }

    private fun initDmpSearch(context: Context): Boolean {
        return if (SdkUtils.isAtLeastU()) {
            val dmpSearchApi = Injector.injectFactory<IDmpSearchApi>()
            dmpSearchApi?.initDmpSdk(context) ?: false
        } else {
            false
        }
    }

    override fun query(uri: Uri, projection: Array<out String>?, queryArgs: Bundle?, cancellationSignal: CancellationSignal?): Cursor? {
        Log.e(TAG, "query from: $callingPackage -> dmpSdkInitSuc $dmpSdkInitSuc uri:$uri projection:$projection queryArgs:${queryArgs.log()}")
        val code = SearchChecker.check()
        if (!SearchChecker.isOK(code)) {
            return SearchCursor.create(code)
        }

        val context = context ?: return null
        val args = queryArgs ?: return null
        val category = args.getString(KEY_CATEGORY) ?: ""
        val loader = SearchLoaderFactory.getSearchLoader(context, category)
        return contentResolver?.let { loader.query(it, args) }
    }

    @Deprecated("用其他的query()替代")
    override fun query(
        uri: Uri,
        projection: Array<String>?,
        selection: String?,
        selectionArgs: Array<String>?,
        sortOrder: String?
    ): Cursor? {
        Log.e(TAG, "query implement replace by query(queryArgs)")
        return null
    }


    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<String>?): Int {
        Log.e(TAG, "delete not implement!!!")
        return 0
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        Log.e(TAG, "insert not implement!!!")
        return null
    }

    override fun update(uri: Uri, values: ContentValues?, selection: String?, selectionArgs: Array<String>?): Int {
        Log.e(TAG, "update not implement!!!")
        return 0
    }
}