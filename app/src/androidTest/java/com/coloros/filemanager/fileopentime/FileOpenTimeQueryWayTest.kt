/*********************************************************************
 * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : FileOpenTimeTest
 * Description :
 * Version     : 1.0
 * Date        : 2023/08/01
 * Author      : W9002848
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <data>       <version>  <desc>
 * W9002848        2023/08/01       1.0      create
 **********************************************************************/
package com.coloros.filemanager.fileopentime

import android.database.Cursor
import android.net.Uri
import com.coloros.filemanager.fileopentime.FileOpenTimeTestUtils.checkCursorData
import com.coloros.filemanager.fileopentime.FileOpenTimeTestUtils.cleanTestResource
import com.coloros.filemanager.fileopentime.FileOpenTimeTestUtils.createTestFileTxt
import com.coloros.filemanager.utils.TestAppUtils.openActivity
import com.coloros.filemanager.utils.TestConstants
import com.coloros.filemanager.utils.TestConstants.FILE_OPEN_TIME_URI
import com.coloros.filemanager.utils.TestConstants.FILE_TXT_NAME
import com.coloros.filemanager.utils.TestConstants.FILE_TXT_TYPE
import com.oplus.autotest.olt.testlib.annotations.CaseId
import com.oplus.autotest.olt.testlib.annotations.CasePrioritization
import com.oplus.autotest.olt.testlib.common.BasicData
import com.oplus.filemanager.main.ui.MainActivity
import org.junit.After
import org.junit.AfterClass
import org.junit.Before
import org.junit.BeforeClass
import org.junit.Test

class FileOpenTimeQueryWayTest {

    private val TAG = FileOpenTimeQueryWayTest::class.java.simpleName
    private val time = System.currentTimeMillis()
    private val txtFileOpenTimePath = "${TestConstants.PATH_FILE_MANAGER_TEST}${FILE_TXT_NAME}_$time$FILE_TXT_TYPE"

    companion object {
        private val targetContext = BasicData.getTargetContext()
        private var uri = Uri.parse(FILE_OPEN_TIME_URI)

        @JvmStatic
        @BeforeClass
        fun classSetUp() {
        }

        @JvmStatic
        @AfterClass
        fun classTearDown() {
        }
    }

    @Before
    fun setUp() {
        //Open MainActivity
        openActivity(MainActivity::class.java)
        //删除测试数据，不删除测试文件夹
        cleanTestResource(txtFileOpenTimePath, false)
    }

    @After
    fun tearDown() {
        //删除测试数据，并删除测试文件夹
        cleanTestResource(txtFileOpenTimePath, true)
    }
    
    /**通过query语句查询成功
     * 用例：新创建文件TXT类型, 并调用FileTimeUtil.addOrUpdateFileTime(path)更新文件
     * 期望：返回列表数据，存在新建文件，校验打开文件时间 > 创建文件时间
     * 实际：同期望结果
     */
    @Test
    @CasePrioritization("A")
    @CaseId("AT_APP_FileManager_04143")
    fun should_contentResolver_query_way1_success_when_first_create() {
        val queryStr = "SELECT * FROM FILE_OPEN_TIME WHERE FILE_PATH="
        //1、创建txt文件, 并同步系统媒体库，刷新file_open_time数据库
        createTestFileTxt(txtFileOpenTimePath)

        //2、调用查询接口，获取数据cursor
        val cursor = targetContext.contentResolver.query(
            uri,
            null,
            "$queryStr'$txtFileOpenTimePath'",
            null,
            null,
            null) as Cursor

        //3、校验查询数据
        checkCursorData(cursor, txtFileOpenTimePath)
        cursor.close()
    }

    /**通过query条件“=”查询成功
     * 用例：新创建文件TXT类型, 并调用FileTimeUtil.addOrUpdateFileTime(path)更新文件
     * 期望：返回列表数据，存在新建文件，校验打开文件时间 > 创建文件时间
     * 实际：同期望结果
     */
    @Test
    @CasePrioritization("A")
    @CaseId("AT_APP_FileManager_04144")
    fun should_contentResolver_query_way2_success_when_first_create() {
        //1、创建txt文件, 并同步系统媒体库，刷新file_open_time数据库
        createTestFileTxt(txtFileOpenTimePath)

        //2、调用查询接口，获取数据cursor
        val cursor = targetContext.contentResolver.query(
            uri,
            null,
            "=",
            arrayOf(txtFileOpenTimePath),
            null,
            null) as Cursor

        //3、校验查询数据
        checkCursorData(cursor, txtFileOpenTimePath)
        cursor.close()
    }

    /**通过query条件“like %.txt”查询成功
     * 用例：新创建文件TXT类型, 并调用FileTimeUtil.addOrUpdateFileTime(path)更新文件
     * 期望：返回列表数据，存在新建文件，校验打开文件时间 > 创建文件时间
     * 实际：同期望结果
     */
    @Test
    @CasePrioritization("A")
    @CaseId("AT_APP_FileManager_04145")
    fun should_contentResolver_query_way4_success_when_first_create() {
        //1、创建txt文件, 并同步系统媒体库，刷新file_open_time数据库
        createTestFileTxt(txtFileOpenTimePath)

        //2、调用查询接口，获取数据cursor
        val cursor = targetContext.contentResolver.query(
            uri,
            null,
            "like",
            arrayOf("%$FILE_TXT_TYPE"),
            null,
            null) as Cursor

        //3、校验查询数据
        checkCursorData(cursor, txtFileOpenTimePath, true)
        cursor.close()
    }

    /**通过query条件“in”查询成功
     * 用例：新创建文件TXT类型, 并调用FileTimeUtil.addOrUpdateFileTime(path)更新文件
     * 期望：返回列表数据，存在新建文件，校验打开文件时间 > 创建文件时间
     * 实际：同期望结果
     */
    @Test
    @CasePrioritization("A")
    @CaseId("AT_APP_FileManager_04146")
    fun should_contentResolver_query_way3_success_when_first_create() {
        //1、创建txt文件, 并同步系统媒体库，刷新file_open_time数据库
        createTestFileTxt(txtFileOpenTimePath)

        //2、调用查询接口，获取数据cursor
        val cursor = targetContext.contentResolver.query(
            uri,
            null,
            "in",
            arrayOf(txtFileOpenTimePath),
            null,
            null) as Cursor

        //3、校验查询数据
        checkCursorData(cursor, txtFileOpenTimePath)
        cursor.close()
    }
}