/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : StaticUtil
 ** Description : 技术埋点类
 ** Version     : 1.0
 ** Date        : 2024/06/22 15:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/21       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.appswitch.util

import android.content.Context
import android.util.Log
import com.coloros.filemanager.appswitch.AppSwitchManager
import com.coloros.filemanager.appswitch.process.IViewExactProcess
import com.coloros.filemanager.appswitch.viewextract.ViewExtractHelper
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.ThirdAppSearchUtil
import com.filemanager.common.utils.UserManagerUtils
import com.oplus.filemanager.room.model.ThirdAppFileDataEntity

object StaticUtil {

    const val TAG = "StaticUtil"

    const val DETECT_FAILED = 0
    const val DETECT_SUC = 1


    const val DETECT_ERRORCODE_DEFAULT = 0

    const val DETECT_VIEW_EXACTED_ERROR_UNKOWN = 1
    const val DETECT_VIEW_EXACTED_ERROR_NO_WINDOW_FOUND = 2
    const val DETECT_VIEW_EXACTED_ERROR_CALLBACK = 3
    const val DETECT_VIEW_EXACTED_ERROR_EMPTY_RESULT = 4


    const val DETECT_MAX_CNT_REACHED_ERROR = 10
    const val DETECT_REG_MATCH_ERROR = 20
    const val DETECT_DB_INSERT_UPDATE_STEP1_ERROR = 21
    const val DETECT_DB_INSERT_UPDATE_STEP2_ERROR = 22


    const val DETECT_DMP_INSERT_ERROR = 30
    const val DETECT_DMP_UPDATE_ERROR = 31



    private const val SUPPORT_CODE_SUC = 0
    private const val NO_SUPPORT_LIGHTOS = 1
    private const val NO_SUPPORT_NOT_SYSTEMUSER = 2
    private const val NO_SUPPORT_NO_VIEWEXACT = 3
    private const val NO_SUPPORT_NO_SCENCE_SERVICE = 4
    private const val NO_SUPPORT_NO_DMP = 5
    private const val NO_SUPPORT_CLOUD_OFF = 6
    private const val NO_SUPPORT_NO_SCENCE_SERVICE_SWITCH_OFF = 7


    @JvmStatic
    fun uploadViewExactErrorStatic(
        viewExactResult: ViewExtractHelper.ViewExtractResult,
        errorCode: Int
    ) {
        val result = OptimizeStatisticsUtil.ThirdAppDetectInfo(
            AppSwitchManager.currentThirdAppListenConfigParam?.version?.toString() ?: "",
            viewExactResult.packageName,
            viewExactResult.packageVersion.toString(),
            viewExactResult.preViewActivity,
            viewExactResult.viewExactActivity,
            errorCode,
            "",
            "",
            DETECT_FAILED
        )
        Log.i(TAG, "uploadViewExactErrorStatic viewExactResult $viewExactResult, errorCode $errorCode")
        OptimizeStatisticsUtil.detectThirdAppResult(result)
    }

    @JvmStatic
    fun uploadRegMatchErrorStatic(
        viewExactResult: ViewExtractHelper.ViewExtractResult,
        dataBean: IViewExactProcess.KeyDataBean,
        errorCode: Int
    ) {
        val result = OptimizeStatisticsUtil.ThirdAppDetectInfo(
            AppSwitchManager.currentThirdAppListenConfigParam?.version?.toString() ?: "",
            viewExactResult.packageName,
            viewExactResult.packageVersion.toString(),
            viewExactResult.preViewActivity,
            viewExactResult.viewExactActivity,
            errorCode,
            viewExactResult.viewExactString,
            dataBean.toString(),
            DETECT_FAILED
        )
        Log.i(TAG, "uploadRegMatchErrorStatic viewExactResult $viewExactResult, dataBean $dataBean, errorCode $errorCode")
        OptimizeStatisticsUtil.detectThirdAppResult(result)
    }

    @JvmStatic
    fun uploadDbOrDmpErrorStatic(
        dataBean: ThirdAppFileDataEntity,
        errorCode: Int
    ) {
        var dmpVersion = ""
        if (errorCode == DETECT_DMP_INSERT_ERROR || errorCode == DETECT_DMP_UPDATE_ERROR) {
            dmpVersion = AppUtils.getAppVersionCode(ThirdAppSearchUtil.DMP_PACKAGENAME).toString()
        }
        val result = OptimizeStatisticsUtil.ThirdAppDetectInfo(
            AppSwitchManager.currentThirdAppListenConfigParam?.version?.toString() ?: "",
            dataBean.mDectPackage,
            dataBean.mDectPackageVersion.toString(),
            dataBean.mPreviewActivity,
            dataBean.mSourceActivity,
            errorCode,
            dataBean.mMetaData,
            dataBean.toString(),
            DETECT_FAILED,
            dmpVersion
        )
        Log.i(TAG, "uploadDbOrDmpErrorStatic dataBean $dataBean, errorCode $errorCode")
        OptimizeStatisticsUtil.detectThirdAppResult(result)
    }

    @JvmStatic
    fun uploadDetectSucStatic(
        dataBean: ThirdAppFileDataEntity,
    ) {
        val result = OptimizeStatisticsUtil.ThirdAppDetectInfo(
            AppSwitchManager.currentThirdAppListenConfigParam?.version?.toString() ?: "",
            dataBean.mDectPackage,
            dataBean.mDectPackageVersion.toString(),
            dataBean.mPreviewActivity,
            dataBean.mSourceActivity,
            DETECT_ERRORCODE_DEFAULT,
            dataBean.mMetaData,
            dataBean.toString(),
            DETECT_SUC
        )
        Log.i(TAG, "uploadDetectSucStatic dataBean $dataBean")
        OptimizeStatisticsUtil.detectThirdAppResult(result)
    }


    @JvmStatic
    fun getNotSupportCode(context: Context, initSuc: Boolean, featureOn: Boolean): Int {
        var code = SUPPORT_CODE_SUC
        if (initSuc && featureOn) {
            code = SUPPORT_CODE_SUC
        } else if (initSuc) {
            code = NO_SUPPORT_CLOUD_OFF
        } else {
            val isSystemUser = UserManagerUtils.checkIsSystemUser(context)
            val viewExactSupport = ThirdAppSearchUtil.isViewExtractSupported()
            val scenceServiceSupport = ThirdAppSearchUtil.checkSmartSceneSupportAppSwitch(context)
            val scenceServiceSwitchOn = ThirdAppSearchUtil.checkSmartSceneSwitchKeyOn(context)
            val lightOS = FeatureCompat.sIsLightVersion
            Log.i(TAG, "getNotSupportCode viewExactSupport $viewExactSupport, scenceServiceSupport $scenceServiceSupport ," +
                    "isSysteUser $isSystemUser, lightOs $lightOS, scenceServiceSwitchOn $scenceServiceSwitchOn")
            if (lightOS) {
                code = NO_SUPPORT_LIGHTOS
            } else if (!viewExactSupport) {
                code = NO_SUPPORT_NO_VIEWEXACT
            } else if (!isSystemUser) {
                code = NO_SUPPORT_NOT_SYSTEMUSER
            } else if (!scenceServiceSupport) {
                code = NO_SUPPORT_NO_SCENCE_SERVICE
            } else if (!scenceServiceSwitchOn) {
                code = NO_SUPPORT_NO_SCENCE_SERVICE_SWITCH_OFF
            } else if (InitHelper.isInitFailed()) {
                code = NO_SUPPORT_NO_DMP
            }
        }
        Log.i(TAG, "getNotSupportCode $code")
        return code
    }

    @JvmStatic
    fun uploadInitSucStatic(context: Context, initSuc: Boolean, featureOn: Boolean) {
        val code = getNotSupportCode(context, initSuc, featureOn)
        var sceneVersionCode = ""
        if (code == NO_SUPPORT_NO_SCENCE_SERVICE) {
            sceneVersionCode = AppUtils.getAppVersionCode(ThirdAppSearchUtil.SCENESERVICE_PACKAGENAME).toString()
        }
        var dmpVersion = ""
        if (code == NO_SUPPORT_NO_DMP) {
            dmpVersion = AppUtils.getAppVersionCode(ThirdAppSearchUtil.DMP_PACKAGENAME).toString()
        }
        Log.i(TAG, "uploadInitSucStatic code $code, sceneVersionCode $sceneVersionCode, dmpVersion $dmpVersion")
        OptimizeStatisticsUtil.supportThirdAppResult(code, sceneVersionCode, dmpVersion)
    }
}