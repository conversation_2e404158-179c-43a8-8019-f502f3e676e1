package com.oplus.filemanager.drivebrowser.download

import android.content.Intent
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.utils.Log
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * DocumentDownloadService的单元测试类
 * 使用Robolectric框架进行Android环境模拟测试
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class DocumentDownloadServiceTest {

    // 测试用变量声明
    private lateinit var service: DocumentDownloadService
    private lateinit var mockDispatcher: DocumentDownloadDispatcher
    private lateinit var mockIntent: Intent

    /**
     * 测试前置设置方法
     * 在每个测试用例执行前运行
     */
    @Before
    fun setUp() {
        // 模拟Log类的静态方法
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } returns Unit

        // 创建模拟对象
        mockDispatcher = mockk(relaxed = true)  // 创建宽松的mock对象，所有方法都有默认实现
        mockIntent = mockk(relaxed = true)

        // 初始化被测服务
        service = DocumentDownloadService()
        service.onCreate()  // 触发服务的onCreate生命周期
    }

    /**
     * 测试后置清理方法
     * 在每个测试用例执行后运行
     */
    @After
    fun tearDown() {
        service.onDestroy()  // 触发服务的onDestroy生命周期
    }

    /**
     * 测试onCreate方法是否正确初始化下载调度器
     */
    @Test
    fun `test onCreate initializes download dispatcher`() {
        // 使用反射获取私有字段downloadDispatcher
        val field = DocumentDownloadService::class.java.getDeclaredField("downloadDispatcher")
        field.isAccessible = true  // 设置可访问私有字段
        assertNotNull(field.get(service))  // 验证dispatcher已初始化
    }

    /**
     * 测试onStartCommand方法处理null intent的情况
     */
    @Test
    fun `test onStartCommand with null intent`() {
        val result = service.onStartCommand(null, 0, 0)
        assertTrue(result == 2)  // 验证返回START_NOT_STICKY(值为2)
    }

    /**
     * 测试onStartCommand方法处理有效intent的情况
     */
    @Test
    fun `test onStartCommand with valid intent`() {
        // 使用反射注入mock dispatcher
        val field = DocumentDownloadService::class.java.getDeclaredField("downloadDispatcher")
        field.isAccessible = true
        field.set(service, mockDispatcher)
        
        service.onStartCommand(mockIntent, 0, 0)
        // 验证dispatcher的onStartCommand方法被调用
        verify { mockDispatcher.onStartCommand(mockIntent) }
    }

    /**
     * 测试服务关闭后重新初始化dispatcher的情况
     */
    @Test
    fun `test onStartCommand reinitializes dispatcher when shutdown`() {
        // 使用反射设置isShutdown为true
        val shutdownField = DocumentDownloadService::class.java.getDeclaredField("isShutdown")
        shutdownField.isAccessible = true
        shutdownField.set(service, true)
        
        // 使用反射清空dispatcher
        val dispatcherField = DocumentDownloadService::class.java.getDeclaredField("downloadDispatcher")
        dispatcherField.isAccessible = true
        dispatcherField.set(service, null)
        
        service.onStartCommand(mockIntent, 0, 0)
        
        // 验证dispatcher被重新初始化
        assertNotNull(dispatcherField.get(service))
    }

    /**
     * 测试stop方法的功能
     */
    @Test
    fun `test stop method`() {
        service.stop()
        // 使用反射验证isShutdown被设置为true
        val field = DocumentDownloadService::class.java.getDeclaredField("isShutdown")
        field.isAccessible = true
        assertTrue(field.get(service) as Boolean)
        // 验证日志输出
        verify { Log.d("DocumentDownloadService", "stop") }
    }

    /**
     * 测试initDownloadDispatcher方法的初始化逻辑
     */
    @Test
    fun `test initDownloadDispatcher sets callback`() {
        // 使用反射清空dispatcher
        val dispatcherField = DocumentDownloadService::class.java.getDeclaredField("downloadDispatcher")
        dispatcherField.isAccessible = true
        dispatcherField.set(service, null)
        
        // 使用反射调用私有方法initDownloadDispatcher
        val method = DocumentDownloadService::class.java.getDeclaredMethod("initDownloadDispatcher")
        method.isAccessible = true
        method.invoke(service)
        
        // 验证dispatcher被正确初始化
        assertNotNull(dispatcherField.get(service))
    }
}