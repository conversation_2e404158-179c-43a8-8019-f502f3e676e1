<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:forceDarkAllowed="false"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/app_icon_img"
                android:layout_width="@dimen/dimen_80dp"
                android:layout_height="@dimen/dimen_80dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/full_page_statement_margin_top"
                android:src="@drawable/ic_launcher_filemanager" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_12dp"
                android:textColor="@color/coui_color_label_primary"
                android:layout_gravity="center_horizontal"
                android:fontFamily="sans-serif-medium"
                android:text="@string/welcome"
                android:textSize="@dimen/font_size_16" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:fontFamily="sans-serif-medium"
                android:textColor="@color/coui_color_label_primary"
                android:text="@string/app_name"
                android:textSize="30sp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <com.filemanager.common.view.statement.COUIMaxHeightScrollViewNested
                android:id="@+id/scroll_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:layout_marginBottom="@dimen/dimen_12dp"
                android:fadingEdgeLength="@dimen/coui_full_page_statement_scroll_fade_length"
                android:requiresFadingEdge="vertical"
                android:scrollbars="vertical"
                app:scrollViewMaxHeight="150dp">

                <TextView
                    android:id="@+id/txt_statement"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="sans-serif-regular"
                    android:forceDarkAllowed="false"
                    android:lineSpacingMultiplier="1.2"
                    android:paddingHorizontal="@dimen/coui_full_page_statement_padding_horizontal"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/couiColorLabelSecondary"
                    android:textColorHighlight="@android:color/transparent"
                    android:textSize="10sp" />
            </com.filemanager.common.view.statement.COUIMaxHeightScrollViewNested>

            <TextView
                android:id="@+id/txt_statement_detail_link"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif-regular"
                android:forceDarkAllowed="false"
                android:lineSpacingMultiplier="1.2"
                android:paddingHorizontal="@dimen/coui_full_page_statement_padding_horizontal"
                android:textAlignment="viewStart"
                android:textColor="?attr/couiColorLabelSecondary"
                android:textColorHighlight="@android:color/transparent"
                android:textSize="10sp" />
        </LinearLayout>
    </ScrollView>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ScrollView
            android:id="@+id/scroll_button"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.coui.appcompat.button.COUIButton
                    android:id="@+id/btn_confirm"
                    style="?attr/couiButtonColorfulDefaultStyle"
                    android:layout_width="@dimen/coui_full_page_statement_button_width"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|center_horizontal"
                    android:layout_marginTop="@dimen/coui_full_page_statement_button_margin_top"
                    android:layout_marginBottom="@dimen/coui_full_page_statement_button_margin"
                    android:ellipsize="end"
                    android:text="@string/coui_allow_text" />

                <TextView
                    android:id="@+id/txt_exit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|center_horizontal"
                    android:layout_marginBottom="@dimen/coui_full_page_statement_exit_button_margin_bottom"
                    android:fontFamily="sans-serif-medium"
                    android:text="@string/coui_reject_text"
                    android:textColor="?attr/couiColorPrimaryTextOnPopup"
                    android:textSize="@dimen/coui_full_page_statement_button_text_size" />

            </LinearLayout>

        </ScrollView>

        <LinearLayout
            android:id="@+id/linear_button_big"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:gravity="center"
            android:layout_marginTop="@dimen/dimen_24dp"
            android:layout_marginBottom="@dimen/dimen_24dp"
            android:layout_marginHorizontal="@dimen/dimen_24dp"
            android:orientation="horizontal">

            <com.coui.appcompat.button.COUIButton
                android:id="@+id/btn_disagree_big"
                style="?attr/couiButtonColorfulWhiteStyle"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:ellipsize="end"
                android:text="@string/dont_agree" />

            <com.coui.appcompat.button.COUIButton
                android:id="@+id/btn_confirm_big"
                style="?attr/couiButtonColorfulDefaultStyle"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:text="@string/coui_allow_text" />

        </LinearLayout>

    </RelativeLayout>


</LinearLayout>