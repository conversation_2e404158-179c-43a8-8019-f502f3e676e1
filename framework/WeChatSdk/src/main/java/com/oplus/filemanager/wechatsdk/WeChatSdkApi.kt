/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WeChatSdkApi
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/1/2 14:43
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/1/2       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.wechatsdk

import android.content.Context
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.wechat.IWechat

object WeChatSdkApi : IWechat {

    private const val TAG = "WeChatSdkApi"

    override fun register(context: Context) {
        WeChatUtils.register(context)
    }

    override fun unregister(context: Context) {
        WeChatUtils.unregister(context)
    }

    override fun isWxInstalled(context: Context): Boolean {
        return WeChatUtils.isWxInstalled(context)
    }

    override fun authorization(authUrl: String) {
        runCatching {
            WeChatUtils.authorization(authUrl)
        }.onFailure {
            Log.e(TAG, "authorization -> failure cause ${it.message}")
        }
    }

    override fun openFile(fileUrl: String) {
        runCatching {
            WeChatUtils.openFile(fileUrl)
        }.onFailure {
            Log.e(TAG, "openFile -> failure cause ${it.message}")
        }
    }
}