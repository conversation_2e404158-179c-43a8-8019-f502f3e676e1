/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : SpaceThirdPartyOpenEntity.java
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/18
 * * Author      : huangyuanwang
 * * 飞书中intent中带入的数据结构，在查看云文档（我的空间，共享空间，收藏，离线等）中的云端上传的云文档，intent中的extras中会带入这个类的类
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.bytedance.ee.bear.contract.drive.sdk.entity.open;

import android.os.Parcel;

public class SpaceThirdPartyOpenEntity extends BaseSpaceOpenEntity {

    public static final Creator<SpaceThirdPartyOpenEntity> CREATOR = new Creator<SpaceThirdPartyOpenEntity>() {
        @Override
        public SpaceThirdPartyOpenEntity createFromParcel(Parcel in) {
            return new SpaceThirdPartyOpenEntity(in);
        }

        @Override
        public SpaceThirdPartyOpenEntity[] newArray(int size) {
            return new SpaceThirdPartyOpenEntity[size];
        }
    };

    private String mDocToken;
    private int mHostType;
    private int mIndex;

    public SpaceThirdPartyOpenEntity(Parcel parcel) {
        super(parcel);
        this.mIndex = parcel.readInt();
        this.mDocToken = parcel.readString();
        this.mHostType = parcel.readInt();
    }


    public void setDocToken(String docToken) {
        this.mDocToken = docToken;
    }

    public void setHostType(int hostType) {
        this.mHostType = hostType;
    }

    public void setIndex(int index) {
        this.mIndex = index;
    }
}
