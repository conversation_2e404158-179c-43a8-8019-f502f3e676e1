/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: BaseDecompressFile.kt
 ** Description:
 ** Version: 1.0
 ** Date: 2020/3/15
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.decompress

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Log
import net.lingala.zip4j.util.Zip4jUtil
import org.apache.commons.compress.archivers.jar.JarArchiveEntry
import org.apache.commons.io.FilenameUtils
import java.io.File
import java.util.zip.ZipEntry

open class BaseDecompressFile : BaseFileBean() {
    var childCount = 0

    protected fun parseData(isDir: Boolean) {
        mData?.let {
            mData = it.replace("\\\\".toRegex(), File.separator)
            val result = it.split(File.separator)
            if (result.isNotEmpty()) {
                for (i in (result.size - 1) downTo 0) {
                    if (result[i].isNullOrEmpty().not()) {
                        this.mDisplayName = result[i]
                        break
                    }
                }
            }
            if (isDir && !mData!!.endsWith(File.separator)) {
                mData += File.separator
            }
        }
        mLocalType = if (isDir) {
            MimeTypeHelper.DIRECTORY_TYPE
        } else {
            MimeTypeHelper.getTypeFromExtension(FileTypeUtils.getExtension(mDisplayName) ?: "")
                    ?: MimeTypeHelper.UNKNOWN_TYPE
        }
        // Not need to set mIsDirectory, it related to mLocalType
        Log.d("BaseDecompressFile", "[$mData]  [$mDisplayName]")
    }

    internal fun easyCreateDirFile(path: String): BaseDecompressFile {
        mLocalType = MimeTypeHelper.DIRECTORY_TYPE
        this.mData = path
        parseData(true)
        return this
    }
}

class ZipDecompressFile(var zipHeader: net.lingala.zip4j.model.FileHeader?) : BaseDecompressFile() {
    init {
        zipHeader?.let {
            mSize = it.uncompressedSize
            mDateModified = Zip4jUtil.dosToExtendedEpochTme(it.lastModifiedTime)
            mData = it.fileName
            parseData(it.isDirectory)
        }
    }

    var zipEntry: ZipEntry? = null

    constructor(zipEntry: ZipEntry?) : this(zipHeader = null) {
        this.zipEntry = zipEntry?.also {
            mSize = it.size
            mDateModified = it.lastModifiedTime?.toMillis() ?: 0
            mData = it.name
            parseData(it.isDirectory)
        }
    }
}

class RarDecompressFile(var rarHeader: de.innosystec.unrar.rarfile.FileHeader?) : BaseDecompressFile() {
    init {
        rarHeader?.let {
            mData = parsePath(it)
            mSize = it.fullUnpackSize
            mDateModified = it.mTime.time
            parseData(it.isDirectory)
        }
    }

    companion object {
        fun parsePath(rarHeader: de.innosystec.unrar.rarfile.FileHeader): String? {
            return if (rarHeader.isFileHeader && rarHeader.isUnicode) {
                rarHeader.fileNameW
            } else {
                rarHeader.fileNameString
            }.replace("\\\\".toRegex(), File.separator)
        }
    }
}

class JarDecompressFile(var jarArchiveEntry: JarArchiveEntry?) : BaseDecompressFile() {
    init {
        jarArchiveEntry?.let {
            mSize = it.size
            mDateModified = it.lastModifiedTime.toMillis()
            mData = it.name
            parseData(it.isDirectory)
        }
    }
}

class P7ZipDecompressFile(data: String, filesize: Int, isdir: Boolean) : BaseDecompressFile() {
    init {
        mData = data
        mSize = filesize.toLong()
        mDateModified = 0
        parseData(isdir)
    }
}