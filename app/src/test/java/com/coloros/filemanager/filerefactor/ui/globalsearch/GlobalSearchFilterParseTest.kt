/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.GlobalSearchFilterParseTest
 * * Version     : 1.0
 * * Date        : 2020/7/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.filerefactor.ui.globalsearch

import android.database.Cursor
import android.os.Build
import com.filemanager.common.constants.FilterConstants.FILTER_FROM_BLUETOOTH
import com.filemanager.common.constants.FilterConstants.FILTER_FROM_DOWNLOAD
import com.filemanager.common.helper.CategoryHelper
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterCondition
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConditionManager
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchActivity
import org.junit.*
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import shadow.ShadowContentResolver

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [Build.VERSION_CODES.S])
class GlobalSearchFilterParseTest {

    @Ignore("getSupportFilter is error")
    @Test
    fun should_verify_default_filter_condition_for_tab_all_and_apk() {
        val cursor = Mockito.mock(Cursor::class.java)
        Mockito.`when`(cursor.moveToNext()).thenReturn(false)
        ShadowContentResolver.setsCursor(cursor)
        FilterConditionManager.recycle()
        arrayListOf(
                GlobalSearchActivity.TAB_ALL,
                CategoryHelper.CATEGORY_APK
        ).forEach {
            val result = FilterConditionManager.getSupportFilter(it)
            Assert.assertNotNull(result)
            Assert.assertEquals(result!!.size, 2)
            Assert.assertEquals(result[0].id, FilterConstants.FILTER_TIME)
            Assert.assertEquals(result[1].id, FilterConstants.FILTER_FROM)
        }
    }

    @Ignore("getSupportFilter is error")
    @Test
    fun should_verify_default_filter_condition_for_tab_img_video_audio_doc_compress() {
        val cursor = Mockito.mock(Cursor::class.java)
        Mockito.`when`(cursor.moveToNext()).thenReturn(false)
        ShadowContentResolver.setsCursor(cursor)
        FilterConditionManager.recycle()
        verify_filter_condition(FilterConditionManager.getSupportFilter(CategoryHelper.CATEGORY_IMAGE), FilterConstants.FILTER_IMG)
        verify_filter_condition(FilterConditionManager.getSupportFilter(CategoryHelper.CATEGORY_VIDEO), FilterConstants.FILTER_VIDEO)
        verify_filter_condition(FilterConditionManager.getSupportFilter(CategoryHelper.CATEGORY_AUDIO), FilterConstants.FILTER_AUDIO)
        verify_filter_condition(FilterConditionManager.getSupportFilter(CategoryHelper.CATEGORY_DOC), FilterConstants.FILTER_DOC)
        verify_filter_condition(FilterConditionManager.getSupportFilter(CategoryHelper.CATEGORY_COMPRESS), FilterConstants.FILTER_COMPRESS)
    }

    private fun verify_filter_condition(result: List<FilterCondition>?, conditionId: Int) {
        Assert.assertNotNull(result)
        Assert.assertEquals(result!!.size, 3)
        Assert.assertEquals(result[0].id, FilterConstants.FILTER_TIME)
        Assert.assertEquals(result[1].id, FilterConstants.FILTER_FROM)
        Assert.assertEquals(result[2].id, conditionId)
    }

    @Ignore("need adjust")
    @Config(shadows = [ShadowContentResolver::class])
    fun should_verify_filter_condition_when_disable_time_condition() {
        val cursor = Mockito.mock(Cursor::class.java)
        Mockito.`when`<Boolean>(cursor.moveToNext()).thenReturn(true).thenReturn(false)
        Mockito.`when`<Int>(cursor.getInt(0)).thenReturn(FilterConstants.FILTER_TIME)
        Mockito.`when`<Int>(cursor.getInt(1)).thenReturn(0)
        ShadowContentResolver.setsCursor(cursor)
        FilterConditionManager.recycle()
        val result = FilterConditionManager.getSupportFilter(GlobalSearchActivity.TAB_ALL)
        Assert.assertTrue(result.isNullOrEmpty())
    }

    @Ignore("need adjust")
    @Config(shadows = [ShadowContentResolver::class])
    fun should_verify_filter_from_condition_when_only_include_download_and_bluetooth() {
        val cursor = Mockito.mock(Cursor::class.java)
        Mockito.`when`<Boolean>(cursor.moveToNext()).thenReturn(true).thenReturn(false)
        Mockito.`when`<Int>(cursor.getInt(0)).thenReturn(FilterConstants.FILTER_FROM)
        Mockito.`when`<Int>(cursor.getInt(1)).thenReturn(
                FILTER_FROM_DOWNLOAD or FILTER_FROM_BLUETOOTH)
        ShadowContentResolver.setsCursor(cursor)
        FilterConditionManager.recycle()
        val result = FilterConditionManager.getSupportFilter(GlobalSearchActivity.TAB_ALL)
        Assert.assertNotNull(result)
        Assert.assertEquals(1, result!!.size)
        Assert.assertEquals(FilterConstants.FILTER_FROM, result[0].id)
        Assert.assertEquals(result[0].items!!.size, 2)
        result[0].items!!.forEach { item ->
            Assert.assertTrue((item.id == FILTER_FROM_DOWNLOAD) ||
                    (item.id == FILTER_FROM_BLUETOOTH))
        }
    }
}
