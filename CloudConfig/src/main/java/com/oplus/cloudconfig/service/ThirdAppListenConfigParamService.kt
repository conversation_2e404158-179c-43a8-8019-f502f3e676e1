/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : ThirdAppListenConfigParamService.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/27
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.cloudconfig.service

import com.oplus.nearx.cloudconfig.anotation.Config
import com.oplus.nearx.cloudconfig.observable.Observable
import com.oplus.nearx.cloudconfig.stat.Const
import java.io.File

@Config(configCode = "third_app_listen_config_param", type = Const.CONFIG_TYPE_FILE)
interface ThirdAppListenConfigParamService {

    fun getFile(): Observable<File>
}