/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BaseFileOperate
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/29 17:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/6/29       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal

import com.filemanager.common.utils.Log
import com.oplus.fileservice.operate.convert.OperateRequest
import com.oplus.fileservice.operate.convert.OperationResult
import java.util.concurrent.atomic.AtomicBoolean

abstract class BaseFileOperate : Operation {

    private var mIsCancelled: AtomicBoolean? = AtomicBoolean(false)
    private var mOperateResult: OperationResult? = null

    override fun perform(operateRequest: OperateRequest): OperationResult {
        mOperateResult = run(operateRequest)
        internalAfterRun()
        return if (mOperateResult != null) {
            mOperateResult!!
        } else {
            OperationResult(false)
        }
    }

    override fun cancel(immediately: Boolean) {
        Log.d(TAG, "cancel -> immediately = $immediately")
        if ((mIsCancelled != null) && !isCancelled()) {
            Log.d(TAG, "cancel -> set state to true")
            mIsCancelled?.set(true)
            if (immediately) {
                internalRecycle()
            }
        }
    }

    /**
     * performs the operateRequest.
     */
    abstract fun run(operateRequest: OperateRequest): OperationResult

    fun isCancelled(): Boolean =
        (mIsCancelled?.get() == true) || Thread.currentThread().isInterrupted

    private fun internalAfterRun() {
        if (mIsCancelled?.get() == false) {
            mIsCancelled = null
            mOperateResult?.let { afterRun(it) }
        }
        internalRecycle()
    }

    private fun internalRecycle() {
        Log.d(TAG, "internalRecycle")
        realRecycle()
    }

    private fun realRecycle() {
        Log.d(TAG, "realRecycle")
        recycle()
    }

    /**
     * Recycling variables during execution if necessary。
     */
    abstract fun recycle()

    /**
     * Process the results of the execution.
     */
    abstract fun afterRun(result: OperationResult)

    companion object {
        private const val TAG = "BaseFileOperate"
    }
}