/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileDriveUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/14 20:58
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/14       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.PreferencesUtils
import com.oplus.filemanager.room.model.DriveFileEntity.Companion.SOURCE_TYPE_KINGSSOFT
import com.oplus.filemanager.room.model.DriveFileEntity.Companion.SOURCE_TYPE_TENCENT
import org.jetbrains.annotations.VisibleForTesting

object FileDriveStateUtils {

    @VisibleForTesting
    const val PREF_NAME_FILE_CLOUD_DOCS = "file_cloud_docs"

    @VisibleForTesting
    const val KEY_TENCENT_DOCS_AUTHORIZATION_STATUS = "tencent_docs_authorization_status"
    @VisibleForTesting
    const val KEY_KDOCS_AUTHORIZATION_STATUS = "kdocs_authorization_status"

    @VisibleForTesting
    const val KEY_TENCENT_DOCS_AUTHORIZATION_CODE = "tencent_docs_authorization_code"
    @VisibleForTesting
    const val KEY_TENCENT_DOCS_AUTHORIZATION_STATE = "tencent_docs_authorization_state"

    @VisibleForTesting
    const val KEY_KDOCS_AUTHORIZATION_CODE = "kdocs_authorization_code"
    @VisibleForTesting
    const val KEY_KDOCS_AUTHORIZATION_STATE = "kdocs_authorization_state"

    @VisibleForTesting
    const val DOCS_REFRESH_TIME = "docs_refresh_time"

    @JvmStatic
    fun isAuthed(category: Int): Boolean {
        return if (category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
            getTencentAuthorizationStatus()
        } else {
            getKDocsAuthorizationStatus()
        }
    }

    @JvmStatic
    fun saveAuthStatus(category: Int, auth: Boolean) {
        if (category == CategoryHelper.CATEGORY_TENCENT_DOCS) {
            saveTencentAuthorizationStatus(auth)
        } else {
            saveKDocsAuthorizationStatus(auth)
        }
    }

    @JvmStatic
    fun saveTencentAuthorizationStatus(auth: Boolean) {
        PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_STATUS, auth)
    }

    @JvmStatic
    fun getTencentAuthorizationStatus(): Boolean {
        return PreferencesUtils.getBoolean(PREF_NAME_FILE_CLOUD_DOCS, KEY_TENCENT_DOCS_AUTHORIZATION_STATUS, false)
    }

    @JvmStatic
    fun saveKDocsAuthorizationStatus(auth: Boolean) {
        PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_STATUS, auth)
    }

    @JvmStatic
    fun getKDocsAuthorizationStatus(): Boolean {
        return PreferencesUtils.getBoolean(PREF_NAME_FILE_CLOUD_DOCS, KEY_KDOCS_AUTHORIZATION_STATUS, false)
    }

    @JvmStatic
    fun saveAuthorizationValue(type: Int, code: String, state: String) {
        when (type) {
            CategoryHelper.CATEGORY_K_DOCS -> saveKDocsAuthorizationResult(code, state)

            CategoryHelper.CATEGORY_TENCENT_DOCS -> saveTencentAuthorizationResult(code, state)
        }
    }

    @JvmStatic
    fun saveTencentAuthorizationResult(code: String, state: String) {
        saveAuthorizationValue(KEY_TENCENT_DOCS_AUTHORIZATION_CODE, code)
        saveAuthorizationValue(KEY_TENCENT_DOCS_AUTHORIZATION_STATE, state)
    }

    @JvmStatic
    fun saveKDocsAuthorizationResult(code: String, state: String) {
        saveAuthorizationValue(KEY_KDOCS_AUTHORIZATION_CODE, code)
        saveAuthorizationValue(KEY_KDOCS_AUTHORIZATION_STATE, state)
    }

    @JvmStatic
    fun getAuthorizationStatus(category: Int): Pair<String, String> {
        return when (category) {
            CategoryHelper.CATEGORY_K_DOCS -> {
                val code = getAuthorizationValue(KEY_KDOCS_AUTHORIZATION_CODE)
                val state = getAuthorizationValue(KEY_KDOCS_AUTHORIZATION_STATE)
                Pair(code, state)
            }

            CategoryHelper.CATEGORY_TENCENT_DOCS -> {
                val code = getAuthorizationValue(KEY_TENCENT_DOCS_AUTHORIZATION_CODE)
                val state = getAuthorizationValue(KEY_TENCENT_DOCS_AUTHORIZATION_STATE)
                Pair(code, state)
            }

            else -> Pair("", "")
        }
    }

    @JvmStatic
    fun clearAuthorizationStatus() {
        clearTencentAuthorizationStatus()
        clearKDocsAuthorizationStatus()
    }

    @JvmStatic
    fun clearAuthorizationStatusByCategory(category: Int) {
        when (category) {
            CategoryHelper.CATEGORY_TENCENT_DOCS -> clearTencentAuthorizationStatus()

            CategoryHelper.CATEGORY_K_DOCS -> clearKDocsAuthorizationStatus()
        }
    }

    @JvmStatic
    fun clearTencentAuthorizationStatus() {
        saveTencentAuthorizationStatus(false)
        saveTencentAuthorizationResult("", "")
    }

    @JvmStatic
    fun clearKDocsAuthorizationStatus() {
        saveKDocsAuthorizationStatus(false)
        saveKDocsAuthorizationResult("", "")
    }

    @JvmStatic
    fun saveAuthorizationValue(key: String, value: String) {
        PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, key, value)
    }

    @JvmStatic
    fun getAuthorizationValue(key: String): String {
        return PreferencesUtils.getString(PREF_NAME_FILE_CLOUD_DOCS, key, "") ?: ""
    }

    /**
     * 设置文件刷新时间
     */
    @JvmStatic
    fun saveLastRefreshTime(source: String, time: Long) {
        PreferencesUtils.put(PREF_NAME_FILE_CLOUD_DOCS, "$source-$DOCS_REFRESH_TIME", time)
    }

    /**
     * 获取文件上次刷新时间
     */
    @JvmStatic
    fun getLastRefreshTime(source: String): Long {
        return PreferencesUtils.getLong(PREF_NAME_FILE_CLOUD_DOCS, "$source-$DOCS_REFRESH_TIME", 0L)
    }

    @JvmStatic
    fun clearLastRefreshTime() {
        clearLastRefreshTimeBySource(SOURCE_TYPE_TENCENT)
        clearLastRefreshTimeBySource(SOURCE_TYPE_KINGSSOFT)
    }

    @JvmStatic
    fun clearLastRefreshTimeBySource(source: String) {
        PreferencesUtils.remove(PREF_NAME_FILE_CLOUD_DOCS, "$source-$DOCS_REFRESH_TIME")
    }
}