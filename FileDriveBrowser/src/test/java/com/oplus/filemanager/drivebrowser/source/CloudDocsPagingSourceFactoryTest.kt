/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudDocsPagingSourceFactoryTest
 ** Description : CloudDocsPagingSourceFactory Unit Test
 ** Version     : 1.0
 ** Date        : 2024/01/09 10:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/01/09       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.source

import com.oplus.filemanager.drivebrowser.data.utils.CloudFileSortTypeUtils
import com.oplus.filemanager.provider.DriveFileDBHelper
import com.oplus.filemanager.provider.store.CloudFileDriveStore
import com.oplus.filemanager.room.model.DriveFileEntity
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test


class CloudDocsPagingSourceFactoryTest {

    @Before
    fun setup() {
        mockkStatic(DriveFileDBHelper::class)
    }

    @After
    fun teardown() {
        unmockkStatic(DriveFileDBHelper::class)
    }

    @Test
    fun should_notNull_when_create() {
        every { DriveFileDBHelper.getDocuments(any(), any(), any(), any()) }.returns(mockk())
        val query = CloudDocsQuery(DriveFileEntity.SOURCE_TYPE_TENCENT, "/", CloudFileSortTypeUtils.TENCENT_DOCS_TITLE, 1)
        Assert.assertNotNull(CloudDocsPagingSourceFactory.create(query))
    }

    @Test
    fun should_notNull_when_getOrder() {
        var order = CloudDocsPagingSourceFactory.getOrder(DriveFileEntity.SOURCE_TYPE_TENCENT, CloudFileSortTypeUtils.TENCENT_DOCS_TITLE)
        Assert.assertEquals(order, CloudFileDriveStore.Columns.NAME)

        order = CloudDocsPagingSourceFactory.getOrder(DriveFileEntity.SOURCE_TYPE_KINGSSOFT, CloudFileSortTypeUtils.KDOCS_MODIFY_TIME)
        Assert.assertEquals(order, CloudFileDriveStore.Columns.LAST_MODIFY_TIME)
    }

    @Test
    fun should_notNull_when_getTencentDocsOrderColumn() {
        var column = CloudDocsPagingSourceFactory.getTencentDocsOrderColumn(CloudFileSortTypeUtils.TENCENT_DOCS_BROWSE)
        Assert.assertEquals(column, CloudFileDriveStore.Columns.LAST_BROWSER_TIME)

        column = CloudDocsPagingSourceFactory.getTencentDocsOrderColumn(CloudFileSortTypeUtils.TENCENT_DOCS_MODIFY)
        Assert.assertEquals(column, CloudFileDriveStore.Columns.LAST_MODIFY_TIME)

        column = CloudDocsPagingSourceFactory.getTencentDocsOrderColumn(CloudFileSortTypeUtils.TENCENT_DOCS_TITLE)
        Assert.assertEquals(column, CloudFileDriveStore.Columns.NAME)

        column = CloudDocsPagingSourceFactory.getTencentDocsOrderColumn("type")
        Assert.assertEquals(column, CloudFileDriveStore.Columns.LAST_BROWSER_TIME)
    }

    @Test
    fun should_notNull_when_getKingSoftDocsOrderColumn() {
        var column = CloudDocsPagingSourceFactory.getKingSoftDocsOrderColumn(CloudFileSortTypeUtils.KDOCS_MODIFY_TIME)
        Assert.assertEquals(column, CloudFileDriveStore.Columns.LAST_MODIFY_TIME)

        column = CloudDocsPagingSourceFactory.getKingSoftDocsOrderColumn(CloudFileSortTypeUtils.KDOCS_SIZE)
        Assert.assertEquals(column, CloudFileDriveStore.Columns.SIZE)

        column = CloudDocsPagingSourceFactory.getKingSoftDocsOrderColumn(CloudFileSortTypeUtils.KDOCS_NAME)
        Assert.assertEquals(column, CloudFileDriveStore.Columns.NAME)

        column = CloudDocsPagingSourceFactory.getKingSoftDocsOrderColumn("type")
        Assert.assertEquals(column, CloudFileDriveStore.Columns.LAST_MODIFY_TIME)
    }
}