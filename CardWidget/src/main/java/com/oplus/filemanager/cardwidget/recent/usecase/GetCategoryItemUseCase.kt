/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GetCategoryItemUseCase
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/15 10:59
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/15       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.usecase

import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.R
import com.oplus.filemanager.cardwidget.recent.data.RecentPreviewItem
import com.filemanager.common.utils.stringResource

class GetCategoryItemUseCase {

    operator fun invoke(): List<RecentPreviewItem> {
        val items = arrayListOf<RecentPreviewItem>()
        items.add(RecentPreviewItem(CategoryHelper.CATEGORY_IMAGE, stringResource(R.string.string_photos)))
        items.add(RecentPreviewItem(CategoryHelper.CATEGORY_VIDEO, stringResource(R.string.string_videos)))
        items.add(RecentPreviewItem(CategoryHelper.CATEGORY_AUDIO, stringResource(R.string.string_audio)))
        items.add(RecentPreviewItem(CategoryHelper.CATEGORY_DOC, stringResource(R.string.string_documents)))
        items.add(RecentPreviewItem(CategoryHelper.CATEGORY_APK, stringResource(R.string.string_apk)))
        items.add(RecentPreviewItem(CategoryHelper.CATEGORY_COMPRESS, stringResource(R.string.string_compress)))
        return items
    }
}