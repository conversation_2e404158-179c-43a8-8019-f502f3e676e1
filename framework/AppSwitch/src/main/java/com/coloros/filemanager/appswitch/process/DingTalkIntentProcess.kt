/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : IndexHelper.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/8
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.process

import android.content.ComponentName
import android.content.Intent
import android.os.BadParcelableException
import android.os.Build
import android.os.Bundle
import android.util.Log
import com.alibaba.alimei.cspace.model.DentryModel
import com.coloros.filemanager.appswitch.bean.ThirdAppListenConfigParam
import com.filemanager.common.constants.ThirdAppConstants.DINGTALK_PACKAGE
import java.lang.ClassCastException

class DingTalkIntentProcess : DefaultIntentProcess() {

    companion object {
        const val TAG = "DingTalkIntentProcess"

        const val EXTRA_DENTRY_MODEL = "dentry_model"
        const val EXTRA_SPACE_SRC = "space_transfer_src"
        const val EXTRA_MESSAGE_ID = "message_id"
        const val EXTRA_CONVERSATION_ID = "conversation_id"

        const val EXTAR_SPACE_SRC_VALUE_SINGLECHAT = "chatsingle"
        const val EXTAR_SPACE_SRC_VALUE_ORGCHAT = "chatorg"
        const val EXTAR_SPACE_SRC_VALUE_GROUPCHAT = "chatgroup"
    }

    override fun getKeyDataBeanFromIntent(inputIntent: Intent): IViewExactProcess.KeyDataBean {
        val keyInfo = getKeyInfoFromIntent(inputIntent)
        val uid = getUidFromIntent(keyInfo)
        val fileName = getFileNameFromIntent(keyInfo)
        val sourceType = getSourceTypeFromIntent(keyInfo)
        val fileSize = getFileSizeFromIntent(keyInfo)
        val result = IViewExactProcess.KeyDataBean()
        result.uid = uid
        result.fileName = fileName
        result.sourceType = sourceType
        result.fileSize = fileSize
        Log.i(TAG, "getKeyDataBeanFromIntent intent $inputIntent, result $result")
        return result
    }

    private fun getUidFromIntent(keyInfo: KeyInfoFromIntent): String {
        val sb = StringBuilder()
        sb.append(keyInfo.intentComponent.toShortString()).append(":")
        sb.append(keyInfo.conversationId).append(":")
        sb.append(keyInfo.messageId).append(":")
        sb.append(keyInfo.spaceId).append(":")
        sb.append(keyInfo.id)
        val result = sb.toString()
        Log.e(TAG, "getUidFromIntent keyInfo $keyInfo, result $result as uid")
        return result
    }

    private fun getFileNameFromIntent(keyInfo: KeyInfoFromIntent): String {
        val result = keyInfo.fileName
        //默认本地的文件使用fileName，云端创建的云文档使用nodeName
        Log.i(TAG, "getFileNameFromIntent input KeyInfoFromIntent $keyInfo result $result")
        return result
    }


    private fun getSourceTypeFromIntent(keyInfo: KeyInfoFromIntent): Int {
        var result = -1
        if (keyInfo.spaceTransferSrc.contentEquals(EXTAR_SPACE_SRC_VALUE_SINGLECHAT, true)) {
            result = ThirdAppListenConfigParam.CheckActivityItem.SOURCE_TYPE_SINGLE_CHAT
        } else if (keyInfo.spaceTransferSrc.contentEquals(EXTAR_SPACE_SRC_VALUE_ORGCHAT, true)
            || keyInfo.spaceTransferSrc.contentEquals(EXTAR_SPACE_SRC_VALUE_GROUPCHAT, true)
        ) {
            result = ThirdAppListenConfigParam.CheckActivityItem.SOURCE_TYPE_GROUP_CHAT
        }
        Log.i(TAG, "getSourceTypeFromIntent keyInfo ${keyInfo.spaceTransferSrc}, result $result")
        //非云文档的不做处理，这里只有云文档的部分可以明确，其他的不明确无法从intent中获取有效信息，需要在下一个步骤图文提取框架的结果中来做处理
        return result
    }


    private fun getFileSizeFromIntent(keyInfo: KeyInfoFromIntent): IViewExactProcess.SizeValues? {
        var result: IViewExactProcess.SizeValues? = null
        if (keyInfo.fileSize != -1L) {
            result = IViewExactProcess.SizeValues(1, keyInfo.fileSize.toInt(), 0, 0)
        }
        Log.i(TAG, "getFileSizeFromIntent keyInfo ${keyInfo.fileSize}, result $result")
        return result
    }


    private fun getKeyInfoFromIntent(inputIntent: Intent): KeyInfoFromIntent {
        val intentData = inputIntent.data
        val extras = inputIntent.extras
        val sb = StringBuilder()
        val result = KeyInfoFromIntent()
        result.intentComponent = inputIntent.component ?: ComponentName(DINGTALK_PACKAGE, "")
        if (extras == null) {
            Log.i(TAG, "getKeyInfoFromIntent intent extra null, return")
            return result
        }
        extras.classLoader = DentryModel::class.java.classLoader
        val extrasKeys = extras.keySet()
        Log.i(TAG, "getUidFromIntent start extra keys ${extrasKeys.toList()}")
        if (extrasKeys.isNullOrEmpty()) {
            Log.i(TAG, "getKeyInfoFromIntent intent extra keyset null, return")
            return result
        }
        for (key in extrasKeys) {
            try {
                if (EXTRA_DENTRY_MODEL.contentEquals(key, true)) {
                    //聊天，单聊，群聊，云文档时 传入的本地文件带有这些参数
                    extractedDentryModel(extras, key, sb, result)
                } else {
                    //云文档时，传入的一些云端创建文档的数据
                    extractedOtherField(key, extras, sb, result)
                }
            } catch (e: BadParcelableException) {
                Log.e(TAG, "getEntityFromIntent error", e)
            } catch (e: ClassCastException) {
                Log.e(TAG, "getEntityFromIntent error", e)
            }
        }
        Log.i(
            TAG,
            "getUidFromIntent end itent: $inputIntent intentData $intentData, extras: $extras, sb $sb, result: $result"
        )
        return result
    }


    private fun extractedDentryModel(
        extras: Bundle,
        key: String?,
        sb: StringBuilder,
        result: KeyInfoFromIntent
    ) {
        val entity = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            extras.getParcelable(key, DentryModel::class.java)
        } else {
            extras.getParcelable(key)
        }
        if (entity != null) {
            sb.append("\n")
            sb.append("DentryModel fileName ${entity.name} id ${entity.id}, fileSize ${entity.size}, path ${entity.path}, ")
            sb.append("parentPath ${entity.parentPath}, spaceId ${entity.spaceId}, modifiedTime ${entity.modifiedTime}")
            result.fileName = entity.name ?: ""
            result.fileSize = entity.size
            result.filePath = entity.path ?: ""
            result.parentPath = entity.parentPath ?: ""
            result.modifiedTime = entity.modifiedTime
            result.id = entity.id
            result.spaceId = entity.spaceId
            sb.append("\n")
        }
    }

    private fun extractedOtherField(
        key: String?,
        extras: Bundle,
        sb: StringBuilder,
        result: KeyInfoFromIntent
    ) {
        val spaceTransferSrc: String?
        var messageId: Long = 0
        val conversationId: String?
        if (EXTRA_SPACE_SRC.contentEquals(key, true)) {
            spaceTransferSrc = extras.getString(key)
            sb.append("key $key, value $spaceTransferSrc\n")
            result.spaceTransferSrc = spaceTransferSrc ?: ""
        } else if (EXTRA_MESSAGE_ID.contentEquals(key, true)) {
            messageId = extras.getLong(key)
            sb.append("key $key, value $messageId\n")
            result.messageId = messageId
        } else if (EXTRA_CONVERSATION_ID.contentEquals(key, true)) {
            conversationId = extras.getString(key)
            sb.append("key $key, value ${conversationId}\n")
            result.conversationId = conversationId ?: ""
        }
    }


    data class KeyInfoFromIntent(
        var intentComponent: ComponentName = ComponentName("", ""),
        var fileName: String = "",
        var fileSize: Long = -1,
        var filePath: String = "",
        var modifiedTime: Long = 0,
        var id: Long = 0,
        var spaceId: String = "",
        var parentPath: String = "",
        var messageId: Long = 0,
        var conversationId: String = "",
        var spaceTransferSrc: String = ""
    )
}