#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes for Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:110), pid=8232, tid=1620
#
# JRE version:  (17.0.8+9) (build )
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\CloudConfig\build\20250901_15417706141390179710.compiler.options

Host: Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz, 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Sep  1 11:31:55 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 0.656430 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000255ae3425f0):  JavaThread "main" [_thread_in_vm, id=1620, stack(0x000000c611200000,0x000000c611300000)]

Stack: [0x000000c611200000,0x000000c611300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x677d0a]
V  [jvm.dll+0x7d8c54]
V  [jvm.dll+0x7da3fe]
V  [jvm.dll+0x7daa63]
V  [jvm.dll+0x245c5f]
V  [jvm.dll+0x7d4d5b]
V  [jvm.dll+0x61dcf6]
V  [jvm.dll+0x61dd5a]
V  [jvm.dll+0x62057d]
V  [jvm.dll+0x61e6b6]
V  [jvm.dll+0x369eb2]
V  [jvm.dll+0x1e1aab]
V  [jvm.dll+0x53dde5]
V  [jvm.dll+0x1f0c0a]
V  [jvm.dll+0x752b7d]
V  [jvm.dll+0x753f76]
V  [jvm.dll+0x7542a8]
V  [jvm.dll+0x23e01b]
V  [jvm.dll+0x23e26f]
V  [jvm.dll+0x55bfc8]
V  [jvm.dll+0x55ee97]
V  [jvm.dll+0x381871]
V  [jvm.dll+0x380ecf]
C  0x00000255bd05a60c

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.nio.charset.CharsetEncoder.isLegalReplacement([B)Z+68 java.base
j  java.nio.charset.CharsetEncoder.replaceWith([B)Ljava/nio/charset/CharsetEncoder;+53 java.base
j  java.nio.charset.CharsetEncoder.<init>(Ljava/nio/charset/Charset;FF[B)V+100 java.base
j  java.nio.charset.CharsetEncoder.<init>(Ljava/nio/charset/Charset;FF)V+12 java.base
j  sun.nio.cs.DoubleByte$Encoder.<init>(Ljava/nio/charset/Charset;[C[CZ)V+4 java.base
j  sun.nio.cs.GBK.newEncoder()Ljava/nio/charset/CharsetEncoder;+12 java.base
j  sun.nio.cs.StreamEncoder.<init>(Ljava/io/OutputStream;Ljava/lang/Object;Ljava/nio/charset/Charset;)V+4 java.base
j  sun.nio.cs.StreamEncoder.forOutputStreamWriter(Ljava/io/OutputStream;Ljava/lang/Object;Ljava/nio/charset/Charset;)Lsun/nio/cs/StreamEncoder;+7 java.base
j  java.io.OutputStreamWriter.<init>(Ljava/io/OutputStream;)V+11 java.base
j  java.io.PrintStream.<init>(ZLjava/io/OutputStream;)V+26 java.base
j  java.io.PrintStream.<init>(Ljava/io/OutputStream;Z)V+11 java.base
j  java.lang.System.newPrintStream(Ljava/io/FileOutputStream;Ljava/lang/String;)Ljava/io/PrintStream;+42 java.base
j  java.lang.System.initPhase1()V+92 java.base
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000255dab489e0, length=3, elements={
0x00000255ae3425f0, 0x00000255db267260, 0x00000255dab4a1f0
}

Java Threads: ( => current thread )
=>0x00000255ae3425f0 JavaThread "main" [_thread_in_vm, id=1620, stack(0x000000c611200000,0x000000c611300000)]
  0x00000255db267260 JavaThread "Reference Handler" daemon [_thread_blocked, id=20140, stack(0x000000c611900000,0x000000c611a00000)]
  0x00000255dab4a1f0 JavaThread "Finalizer" daemon [_thread_blocked, id=14952, stack(0x000000c611a00000,0x000000c611b00000)]

Other Threads:
  0x00000255db254510 VMThread "VM Thread" [stack: 0x000000c611800000,0x000000c611900000] [id=23920]
  0x00000255ae3f01f0 GCTaskThread "GC Thread#0" [stack: 0x000000c611300000,0x000000c611400000] [id=22196]
  0x00000255ae400ef0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000c611400000,0x000000c611500000] [id=23656]
  0x00000255ae402910 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000c611500000,0x000000c611600000] [id=18220]
  0x00000255daa35240 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000c611600000,0x000000c611700000] [id=26344]
  0x00000255daa35c60 ConcurrentGCThread "G1 Service" [stack: 0x000000c611700000,0x000000c611800000] [id=18624]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00000255ae33d480] Metaspace_lock - owner thread: 0x00000255ae3425f0

Heap address: 0x0000000604800000, size: 8120 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 32479M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8120M
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 520192K, used 0K [0x0000000604800000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 0 survivors (0K)
 Metaspace       used 2425K, committed 2432K, reserved 1114112K
  class space    used 191K, committed 192K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000, 0x0000000604800000| Untracked 
|   1|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|   2|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|   3|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|   4|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|   5|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|   6|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|   7|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|   8|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|   9|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  10|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  11|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  12|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  13|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
|  14|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  15|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  16|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  17|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  18|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  19|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  20|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  21|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Untracked 
|  22|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|  23|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000, 0x000000060a400000| Untracked 
|  24|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000, 0x000000060a800000| Untracked 
|  25|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000, 0x000000060ac00000| Untracked 
|  26|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000, 0x000000060b000000| Untracked 
|  27|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000, 0x000000060b400000| Untracked 
|  28|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000, 0x000000060b800000| Untracked 
|  29|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000, 0x000000060bc00000| Untracked 
|  30|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|  31|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000, 0x000000060c400000| Untracked 
|  32|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000, 0x000000060c800000| Untracked 
|  33|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000, 0x000000060cc00000| Untracked 
|  34|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000, 0x000000060d000000| Untracked 
|  35|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000, 0x000000060d400000| Untracked 
|  36|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000, 0x000000060d800000| Untracked 
|  37|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000, 0x000000060dc00000| Untracked 
|  38|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|  39|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000, 0x000000060e400000| Untracked 
|  40|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000, 0x000000060e800000| Untracked 
|  41|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000, 0x000000060ec00000| Untracked 
|  42|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000, 0x000000060f000000| Untracked 
|  43|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000, 0x000000060f400000| Untracked 
|  44|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000, 0x000000060f800000| Untracked 
|  45|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000, 0x000000060fc00000| Untracked 
|  46|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|  47|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000, 0x0000000610400000| Untracked 
|  48|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000, 0x0000000610800000| Untracked 
|  49|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000, 0x0000000610c00000| Untracked 
|  50|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000, 0x0000000611000000| Untracked 
|  51|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000, 0x0000000611400000| Untracked 
|  52|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000, 0x0000000611800000| Untracked 
|  53|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000, 0x0000000611c00000| Untracked 
|  54|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
|  55|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000, 0x0000000612400000| Untracked 
|  56|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000, 0x0000000612800000| Untracked 
|  57|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000, 0x0000000612c00000| Untracked 
|  58|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000, 0x0000000613000000| Untracked 
|  59|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000, 0x0000000613400000| Untracked 
|  60|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000, 0x0000000613800000| Untracked 
|  61|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000, 0x0000000613c00000| Untracked 
|  62|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
|  63|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000, 0x0000000614400000| Untracked 
|  64|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000, 0x0000000614800000| Untracked 
|  65|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000, 0x0000000614c00000| Untracked 
|  66|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000, 0x0000000615000000| Untracked 
|  67|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000, 0x0000000615400000| Untracked 
|  68|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000, 0x0000000615800000| Untracked 
|  69|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000, 0x0000000615c00000| Untracked 
|  70|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
|  71|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000, 0x0000000616400000| Untracked 
|  72|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000, 0x0000000616800000| Untracked 
|  73|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000, 0x0000000616c00000| Untracked 
|  74|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000, 0x0000000617000000| Untracked 
|  75|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000, 0x0000000617400000| Untracked 
|  76|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000, 0x0000000617800000| Untracked 
|  77|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000, 0x0000000617c00000| Untracked 
|  78|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
|  79|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000, 0x0000000618400000| Untracked 
|  80|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000, 0x0000000618800000| Untracked 
|  81|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000, 0x0000000618c00000| Untracked 
|  82|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000, 0x0000000619000000| Untracked 
|  83|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000, 0x0000000619400000| Untracked 
|  84|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000, 0x0000000619800000| Untracked 
|  85|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000, 0x0000000619c00000| Untracked 
|  86|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000, 0x000000061a000000| Untracked 
|  87|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000, 0x000000061a400000| Untracked 
|  88|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000, 0x000000061a800000| Untracked 
|  89|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000, 0x000000061ac00000| Untracked 
|  90|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000, 0x000000061b000000| Untracked 
|  91|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000, 0x000000061b400000| Untracked 
|  92|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000, 0x000000061b800000| Untracked 
|  93|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000, 0x000000061bc00000| Untracked 
|  94|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000, 0x000000061c000000| Untracked 
|  95|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000, 0x000000061c400000| Untracked 
|  96|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000, 0x000000061c800000| Untracked 
|  97|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000, 0x000000061cc00000| Untracked 
|  98|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000, 0x000000061d000000| Untracked 
|  99|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000, 0x000000061d400000| Untracked 
| 100|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000, 0x000000061d800000| Untracked 
| 101|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000, 0x000000061dc00000| Untracked 
| 102|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000, 0x000000061e000000| Untracked 
| 103|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000, 0x000000061e400000| Untracked 
| 104|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000, 0x000000061e800000| Untracked 
| 105|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000, 0x000000061ec00000| Untracked 
| 106|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000, 0x000000061f000000| Untracked 
| 107|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000, 0x000000061f400000| Untracked 
| 108|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000, 0x000000061f800000| Untracked 
| 109|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000, 0x000000061fc00000| Untracked 
| 110|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000, 0x0000000620000000| Untracked 
| 111|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000, 0x0000000620400000| Untracked 
| 112|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000, 0x0000000620800000| Untracked 
| 113|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000, 0x0000000620c00000| Untracked 
| 114|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 115|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000, 0x0000000621400000| Untracked 
| 116|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000, 0x0000000621800000| Untracked 
| 117|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000, 0x0000000621c00000| Untracked 
| 118|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000, 0x0000000622000000| Untracked 
| 119|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000, 0x0000000622400000| Untracked 
| 120|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000, 0x0000000622800000| Untracked 
| 121|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000, 0x0000000622c00000| Untracked 
| 122|0x0000000623000000, 0x0000000623000000, 0x0000000623400000|  0%| F|  |TAMS 0x0000000623000000, 0x0000000623000000| Untracked 
| 123|0x0000000623400000, 0x0000000623400000, 0x0000000623800000|  0%| F|  |TAMS 0x0000000623400000, 0x0000000623400000| Untracked 
| 124|0x0000000623800000, 0x0000000623800000, 0x0000000623c00000|  0%| F|  |TAMS 0x0000000623800000, 0x0000000623800000| Untracked 
| 125|0x0000000623c00000, 0x0000000623c00000, 0x0000000624000000|  0%| F|  |TAMS 0x0000000623c00000, 0x0000000623c00000| Untracked 
| 126|0x0000000624000000, 0x00000006240f5d58, 0x0000000624400000| 24%| E|  |TAMS 0x0000000624000000, 0x0000000624000000| Complete 

Card table byte_map: [0x00000255c6460000,0x00000255c7440000] _byte_map_base: 0x00000255c343c000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000255ae3f0800, (CMBitMap*) 0x00000255ae3f0840
 Prev Bits: [0x00000255c8420000, 0x00000255d0300000)
 Next Bits: [0x00000255d0300000, 0x00000255d81e0000)

Polling page: 0x00000255ac2b0000

Metaspace:

Usage:
  Non-class:      2.18 MB used.
      Class:    191.61 KB used.
       Both:      2.37 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       2.19 MB (  3%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     192.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       2.38 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  12.00 MB
       Class:  15.75 MB
        Both:  27.75 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 17179869184.00 GB
CDS: off
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 2.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 38.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 3.
num_chunk_merges: 0.
num_chunk_splits: 2.
num_chunks_enlarged: 0.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=0Kb max_used=0Kb free=119168Kb
 bounds [0x00000255bd790000, 0x00000255bda00000, 0x00000255c4bf0000]
CodeHeap 'profiled nmethods': size=119104Kb used=0Kb max_used=0Kb free=119104Kb
 bounds [0x00000255b5bf0000, 0x00000255b5e60000, 0x00000255bd040000]
CodeHeap 'non-nmethods': size=7488Kb used=386Kb max_used=386Kb free=7102Kb
 bounds [0x00000255bd040000, 0x00000255bd2b0000, 0x00000255bd790000]
 total_blobs=180 nmethods=0 adapters=153
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (0 events):
No events

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (20 events):
Event: 0.619 loading class java/io/PrintStream done
Event: 0.619 loading class java/io/BufferedOutputStream
Event: 0.619 loading class java/io/BufferedOutputStream done
Event: 0.620 loading class java/io/OutputStreamWriter
Event: 0.620 loading class java/io/Writer
Event: 0.620 loading class java/io/Writer done
Event: 0.620 loading class java/io/OutputStreamWriter done
Event: 0.620 loading class sun/security/action/GetPropertyAction
Event: 0.620 loading class sun/security/action/GetPropertyAction done
Event: 0.620 loading class sun/nio/cs/StreamEncoder
Event: 0.620 loading class sun/nio/cs/StreamEncoder done
Event: 0.620 loading class sun/nio/cs/DoubleByte$Encoder
Event: 0.620 loading class sun/nio/cs/ArrayEncoder
Event: 0.620 loading class sun/nio/cs/ArrayEncoder done
Event: 0.620 loading class java/nio/charset/CharsetEncoder
Event: 0.620 loading class java/nio/charset/CharsetEncoder done
Event: 0.620 loading class sun/nio/cs/DoubleByte$Encoder done
Event: 0.621 loading class sun/nio/cs/GBK$EncodeHolder
Event: 0.621 loading class sun/nio/cs/GBK$EncodeHolder done
Event: 0.624 loading class java/nio/ByteBuffer


Dynamic libraries:
0x00007ff708300000 - 0x00007ff708310000 	E:\JAVA\bin\java.exe
0x00007ff827030000 - 0x00007ff827228000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8255d0000 - 0x00007ff825692000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff824d50000 - 0x00007ff825046000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff8246c0000 - 0x00007ff8247c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff81fde0000 - 0x00007ff81fdf9000 	E:\JAVA\bin\jli.dll
0x00007ff81fdc0000 - 0x00007ff81fddb000 	E:\JAVA\bin\VCRUNTIME140.dll
0x00007ff826e80000 - 0x00007ff826f31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff826340000 - 0x00007ff8263de000 	C:\Windows\System32\msvcrt.dll
0x00007ff825e80000 - 0x00007ff825f1f000 	C:\Windows\System32\sechost.dll
0x00007ff825b10000 - 0x00007ff825c33000 	C:\Windows\System32\RPCRT4.dll
0x00007ff824a20000 - 0x00007ff824a47000 	C:\Windows\System32\bcrypt.dll
0x00007ff825050000 - 0x00007ff8251ed000 	C:\Windows\System32\USER32.dll
0x00007ff81dfe0000 - 0x00007ff81e27a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8248a0000 - 0x00007ff8248c2000 	C:\Windows\System32\win32u.dll
0x00007ff825da0000 - 0x00007ff825dcb000 	C:\Windows\System32\GDI32.dll
0x00007ff824a50000 - 0x00007ff824b69000 	C:\Windows\System32\gdi32full.dll
0x00007ff824980000 - 0x00007ff824a1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff823400000 - 0x00007ff82340a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8253f0000 - 0x00007ff82541f000 	C:\Windows\System32\IMM32.DLL
0x00007ff81fd50000 - 0x00007ff81fd5c000 	E:\JAVA\bin\vcruntime140_1.dll
0x00007ffffb0c0000 - 0x00007ffffb14e000 	E:\JAVA\bin\msvcp140.dll
0x00007fffe0350000 - 0x00007fffe0f2e000 	E:\JAVA\bin\server\jvm.dll
0x00007ff826f40000 - 0x00007ff826f48000 	C:\Windows\System32\PSAPI.DLL
0x00007ff81e3c0000 - 0x00007ff81e3e7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007fffeb340000 - 0x00007fffeb349000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff826e10000 - 0x00007ff826e7b000 	C:\Windows\System32\WS2_32.dll
0x00007ff823360000 - 0x00007ff823372000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff81c4f0000 - 0x00007ff81c4fa000 	E:\JAVA\bin\jimage.dll
0x00007ff821e10000 - 0x00007ff822011000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff80fef0000 - 0x00007ff80ff24000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff824810000 - 0x00007ff824892000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff818d20000 - 0x00007ff818d45000 	E:\JAVA\bin\java.dll
0x00007fffef390000 - 0x00007fffef467000 	E:\JAVA\bin\jsvml.dll
0x00007ff826570000 - 0x00007ff826cde000 	C:\Windows\System32\SHELL32.dll
0x00007ff8222d0000 - 0x00007ff822a74000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff825fd0000 - 0x00007ff826323000 	C:\Windows\System32\combase.dll
0x00007ff8240f0000 - 0x00007ff82411b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff826d40000 - 0x00007ff826e0d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff825f20000 - 0x00007ff825fcd000 	C:\Windows\System32\SHCORE.dll
0x00007ff8245f0000 - 0x00007ff824614000 	C:\Windows\SYSTEM32\profapi.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\JAVA\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;E:\JAVA\bin\server

VM Arguments:
java_command: org.jetbrains.kotlin.cli.jvm.K2JVMCompiler @E:\WorkProject\9\FileManager\CloudConfig\build\20250901_15417706141390179710.compiler.options
java_class_path (initial): E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-compiler-embeddable\1.9.22\9cd4dc7773cf2a99ecd961a88fbbc9a2da3fb5e1\kotlin-compiler-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-script-runtime\1.9.22\f8139a46fc677ec9badc49ae954392f4f5e7e7c7\kotlin-script-runtime-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\1.6.10\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\kotlin-reflect-1.6.10.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-daemon-embeddable\1.9.22\20e2c5df715f3240c765cfc222530e2796542021\kotlin-daemon-embeddable-1.9.22.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains.intellij.deps\trove4j\1.0.20200330\3afb14d5f9ceb459d724e907a21145e8ff394f02\trove4j-1.0.20200330.jar;E:\system\gradle-8.0.2\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8514437120                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8514437120                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=E:\JAVA
CLASSPATH=.;E:\JAVA\lib\dt.jar;E:\JAVA\lib\tools.jar;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\dotnet\;E:\git\Git\cmd;D:\Users\W9096060\AppData\Local\Android\Sdk\platform-tools;C:\Program Files\dotnet\;E:\1.8java\lib\dt.jar;E:\1.8java\lib\tools.jar;E:\1.8java\bin;E:\JAVA\bin;E:\JAVA\lib;D:\Users\W9096060\AppData\Local\Microsoft\WindowsApps;
USERNAME=W9096060
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 4 days 18:55 hours

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt

Memory: 4k page, system-wide physical 32479M (3642M free)
TotalPageFile size 47840M (AvailPageFile size 759M)
current process WorkingSet (physical memory assigned to process): 22M, peak: 22M
current process commit charge ("private bytes"): 604M, peak: 611M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.8+9-LTS-211) for windows-amd64 JRE (17.0.8+9-LTS-211), built on Jun 14 2023 10:34:31 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
