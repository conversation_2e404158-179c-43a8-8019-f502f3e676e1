<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <application>
    <activity
        android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
        android:screenOrientation="behind"
        android:theme="@style/AppNoTitleTheme.PreferenceFragment"
        android:uiOptions="splitActionBarWhenNarrow"
        android:windowSoftInputMode="adjustPan"
        android:permission="com.oplus.permission.safe.PROTECT"
        android:name="com.coloros.filemanager.export.UserAgreementActivity"
        android:exported="true">
        <intent-filter>
            <action android:name="com.coloros.filemanager.action.user.agreement"/>
            <category android:name="android.intent.category.DEFAULT" />
        </intent-filter>
    </activity>
    <activity
        android:permission="com.oplus.permission.safe.PROTECT"
        android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
        android:screenOrientation="behind"
        android:theme="@style/AppNoTitleTheme.PreferenceFragment"
        android:uiOptions="splitActionBarWhenNarrow"
        android:windowSoftInputMode="adjustPan"
        android:name="com.coloros.filemanager.export.PersonalInfoListActivity"
        android:exported="true" >
        <intent-filter>
            <action android:name="com.coloros.filemanager.action.personal.info"/>
            <category android:name="android.intent.category.DEFAULT" />
        </intent-filter>
    </activity>
    </application>
</manifest>