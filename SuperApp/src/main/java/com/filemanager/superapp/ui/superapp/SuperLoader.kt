/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.superapp
 * * Version     : 1.0
 * * Date        : 2020/4/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.superapp.ui.superapp

import android.content.Context
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BasePathLoader
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.HiddenFileHelper.isNeedShowHiddenFile
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.BlacklistParser
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.interfaze.main.IMain
import java.io.File
import java.util.Locale

class SuperLoader(context: Context, tabPosition: Int, paths: Array<String>, dirDepth: Int = DEFAULT_DIR_DEPTH) : BasePathLoader<Int, BaseFileBean>(context) {

    companion object {
        const val DEFAULT_DIR_DEPTH = 0
    }
    @VisibleForTesting
    var mSuperPath: Array<String> = paths
    @VisibleForTesting
    val mInternalPath: String? = VolumeEnvironment.getInternalSdPath(appContext)
    @VisibleForTesting
    val mExternalPath: String? = VolumeEnvironment.getExternalSdPath(appContext)
    @VisibleForTesting
    var mInternalPath999: String? = null
    @VisibleForTesting
    val mTempFileList = ArrayList<BaseFileBean>()
    @VisibleForTesting
    val mTabPosition: Int = tabPosition
    private val mDirDepth = dirDepth
    private var tempSort = -1
    private var tempDesc = -1

    init {
        if (FeatureCompat.sIsSupportMultiApp) {
            mInternalPath999 = KtConstants.LOCAL_VOLUME_MULTI_APP_PATH
            Log.d(TAG, "MultiAppPath: ${this.mInternalPath999}")
        }
        initData()
    }

    override fun createFromPath(volume: String, parentPath: String, path: String): List<BaseFileBean> {
        val parentPathWithSeparator = if (parentPath.endsWith(File.separator)) {
            parentPath
        } else {
            parentPath.plus(File.separator)
        }
        val file = PathFileWrapper(volume.plus(File.separator).plus(parentPathWithSeparator).plus(path))
        mTempFileList.clear()
        if (file.mIsDirectory) {
            try {
                if (mDirDepth > DEFAULT_DIR_DEPTH) {
                    mTempFileList.addAll(traverseAllFiles(file.mData, mDirDepth) {
                        filterFile(it)
                    })
                } else {
                    recursiveFileDirectory(file)
                }
            } catch (e: StackOverflowError) {
                Log.e(TAG, e.message)
            }
        } else {
            if (filterFile(file)) {
                mTempFileList.add(file)
            }
        }
        Log.d(TAG, "createFromPath volume:$volume parentPath:$parentPathWithSeparator path:$path -> result:${mTempFileList.size}")
        return mTempFileList
    }

    override fun preHandleResultBackground(list: List<BaseFileBean>): List<BaseFileBean> {
        // 去重
        val locale = Locale.getDefault()
        val result = list.distinctBy { it.mData?.lowercase(locale) }
        // 排序
        val documentExtensionType = Injector.injectFactory<IDocumentExtensionType>()
        if (tempSort != -1) {
            val lastSort = SortModeUtils.getSharedSortMode(MyApplication.sAppContext, SortModeUtils.BROWSER_LAST_SORT_RECORD)
            documentExtensionType?.sortFileIgnoreHeadLabel(result, tempSort, lastSort, tempDesc == 0)
        } else {
            val currentSort = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getSuperKey(mSuperPath))
            val isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getSuperKey(mSuperPath))
            val lastSort = SortModeUtils.getSharedSortMode(appContext, SortModeUtils.BROWSER_LAST_SORT_RECORD)
            documentExtensionType?.sortFileIgnoreHeadLabel(result, currentSort, lastSort, isDesc)
        }
        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.findFileLabelIfHad(result)
        return result
    }

    @VisibleForTesting
    fun recursiveFileDirectory(baseFileBean: BaseFileBean) {
        val fileList = JavaFileHelper.listFileBeans(baseFileBean, !isNeedShowHiddenFile())
        if (fileList != null) {
            for (file in fileList) {
                if (file.mIsDirectory) {
                    recursiveFileDirectory(file)
                } else {
                    if (filterFile(file)) {
                        mTempFileList.add(file)
                    }
                }
            }
        }
    }

    @VisibleForTesting
    fun filterFile(file: BaseFileBean): Boolean {
        val subFileName = file.mDisplayName
        if (TextUtils.isEmpty(subFileName)) {
            return false
        }

        if ((BlacklistParser.sFilterNoSuffixFile == BlacklistParser.FILTER_NO_SUFFIX_STATE)
                && BlacklistParser.isFilterNoSuffixFile(subFileName)) {
            return false
        }
        if (KtUtils.needFilterByPosition(mTabPosition, file.mLocalType)) {
            if (mTabPosition == Constants.TAB_DOCUMENT) {
                return Injector.injectFactory<IDocumentExtensionType>()?.isNeedDocFilter(file) != true
            }
            return false
        }

        if (mTabPosition == Constants.TAB_OTHER) {
            return Injector.injectFactory<IDocumentExtensionType>()?.isNeedDocFilter(file) ?: false
        }

        return true
    }

    override fun getVolume(): List<String> {
        val volume = ArrayList<String>()
        mInternalPath?.let {
            volume.add(it)
        }
        mExternalPath?.let {
            volume.add(it)
        }
        mInternalPath999?.let {
            volume.add(it)
        }
        return volume
    }

    override fun getPath(): Array<String> {
        return mSuperPath
    }

    override fun getFilterList(): List<Int>? {
        return null
    }

    override fun getItemKey(item: BaseFileBean): Int? {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return null
        }

        return path.lowercase(Locale.getDefault()).hashCode()
    }

    /**
     * @param path root path
     * @param maxDepth the maximum  of a directory tree to traverse. By default there is no limit
     */
    @VisibleForTesting
    fun traverseAllFiles(path: String?, maxDepth: Int = Int.MAX_VALUE, includeDir: Boolean = false,
                                 filter: ((PathFileWrapper) -> Boolean)? = null): List<PathFileWrapper> {

        val fileList = arrayListOf<PathFileWrapper>()

        path?.let { p ->
            File(p).walk().maxDepth(maxDepth)
                    .filter { includeDir || it.isFile }
                    .filter { isNeedShowHiddenFile() || HiddenFileHelper.isHiddenFile(it.name).not() }
                    .forEach {
                        val bean = PathFileWrapper(it.absolutePath)
                        if (filter?.invoke(bean) != false) {
                            fileList.add(bean)
                        }
                    }
        }

        return fileList
    }

    fun updatePath(path: Array<String>) {
        mSuperPath = path
    }

    fun setSort(sort: Int, d: Int) {
        tempSort = sort
        tempDesc = d
    }
}