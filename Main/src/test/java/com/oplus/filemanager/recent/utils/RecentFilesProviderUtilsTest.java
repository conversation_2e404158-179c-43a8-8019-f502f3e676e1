package com.oplus.filemanager.recent.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import android.content.Context;
import android.database.Cursor;
import android.net.Uri;

import com.oplus.filemanager.recent.entity.recent.FileFilterEntity;
import com.oplus.filemanager.recent.utils.RecentDataHelper;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.File;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * RecentFilesProviderUtils的单元测试类
 * 用于测试RecentFilesProviderUtils工具类的各种功能
 */
@RunWith(MockitoJUnitRunner.class)
public class RecentFilesProviderUtilsTest {

    // 模拟Context对象
    @Mock
    private Context mockContext;
    // 模拟ContentResolver对象
    @Mock
    private android.content.ContentResolver mockContentResolver;
    // 模拟Cursor对象
    @Mock
    private Cursor mockCursor;
    // 模拟File对象
    @Mock
    private File mockFile;

    // 用于跟踪Cursor是否已关闭的原子布尔值
    private final AtomicBoolean cursorClosed = new AtomicBoolean(false);

    /**
     * 测试前的初始化方法
     * 设置模拟对象的行为
     */
    @Before
    public void setUp() {
        // 当调用getContentResolver()时返回模拟的ContentResolver
        when(mockContext.getContentResolver()).thenReturn(mockContentResolver);
        // 重置所有模拟对象的状态
        reset(mockCursor, mockContentResolver, mockFile);
        // 初始化cursorClosed状态为false
        cursorClosed.set(false);
        
        // 当调用Cursor的close()方法时，将cursorClosed设为true
        doAnswer(invocation -> {
            cursorClosed.set(true);
            return null;
        }).when(mockCursor).close();
        
        // 设置模拟File的exists()方法总是返回true
        when(mockFile.exists()).thenReturn(true);
    }

    /**
     * 测试getFilterFiles方法在Cursor为null时的情况
     * 验证当查询返回null时，方法能正确处理并返回空列表
     */
    @Test
    public void testGetFilterFiles_NullCursor() {
        // 设置测试用的时间范围
        String startTime = "1000";
        String endTime = "2000";

        // 设置模拟ContentResolver的query方法总是返回null
        when(mockContentResolver.query(any(Uri.class), any(String[].class), anyString(), any(String[].class), anyString()))
                .thenReturn(null);

        // 调用被测试方法
        List<FileFilterEntity> result = RecentFilesProviderUtils.getFilterFiles(mockContext, startTime, endTime);

        // 验证结果不为null且为空列表
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试后的清理方法
     * 重置所有模拟对象的状态
     */
    @After
    public void tearDown() {
        // 重置所有模拟对象
        reset(mockCursor, mockContentResolver, mockFile);
        // 重置cursorClosed状态
        cursorClosed.set(false);
    }
}