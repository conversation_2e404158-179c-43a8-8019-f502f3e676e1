package com.oplus.filemanager.category.globalsearch.adapter

import android.content.Context
import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.oplus.filemanager.category.globalsearch.adapter.vh.CategoryItemVH
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config

/**
 * SearchSpaceItemDecoration 的单元测试类
 * 用于测试 RecyclerView 项间距装饰器的各种边界条件和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29], manifest = Config.NONE)
class SearchSpaceItemDecorationTest {

    // 测试类中使用的成员变量
    private lateinit var decoration: SearchSpaceItemDecoration  // 待测试的装饰器实例
    private lateinit var context: Context  // 测试上下文
    private lateinit var outRect: Rect  // 用于接收间距计算结果的矩形
    private lateinit var view: View  // 模拟的视图项
    private lateinit var parent: RecyclerView  // 模拟的RecyclerView父容器
    private lateinit var state: RecyclerView.State  // 模拟的RecyclerView状态

    /**
     * 测试前的初始化方法
     * 设置测试环境和模拟对象
     */
    @Before
    fun setUp() {
        MockKAnnotations.init(this)
        
        // 使用 Robolectric 的应用上下文而不是 mock MyApplication
        context = RuntimeEnvironment.application
        val resources = context.resources
        mockkObject(MyApplication)
        every { MyApplication.sAppContext } returns context
        
        // 创建实例时使用明确的值避免访问 MyApplication
        val horizontalMargin = resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp)
        val verticalMargin = resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_8dp)
        decoration = SearchSpaceItemDecoration(horizontalMargin, verticalMargin, context)
        outRect = Rect()
        view = mockk(relaxed = true)
        parent = mockk(relaxed = true)
        state = mockk(relaxed = true)
    }

    /**
     * 测试后的清理方法
     * 释放模拟对象资源
     */
    @After
    fun tearDown() {
        unmockkAll()
    }

    /**
     * 测试当视图持有者是 SearchLabelItemVH 类型时
     * 应该调用 offsetForSearchLabelItemVH 方法
     */
    @Test
    fun `getItemOffsets should call offsetForSearchLabelItemVH when viewHolder is SearchLabelItemVH`() {
        val viewHolder = mockk<GlobalSearchAdapter.SearchLabelItemVH>(relaxed = true)
        every { parent.getChildViewHolder(view) } returns viewHolder
        every { viewHolder.adapterPosition } returns 0
        every { parent.adapter } returns mockk<RecyclerView.Adapter<RecyclerView.ViewHolder>>(relaxed = true).apply {
            every { itemCount } returns 2
            every { getItemViewType(1) } returns GlobalSearchAdapter.VIEW_TYPE_FILE_OR_FOLDER
        }
        
        decoration.getItemOffsets(outRect, view, parent, state)
        
        verify(exactly = 1) { parent.getChildViewHolder(view) }
    }

    /**
     * 测试当视图持有者是 CategoryItemVH 类型时
     * 应该调用 offsetForCategoryItemVH 方法
     */
    @Test
    fun `getItemOffsets should call offsetForCategoryItemVH when viewHolder is CategoryItemVH`() {
        val viewHolder = mockk<CategoryItemVH>(relaxed = true)
        every { parent.getChildViewHolder(view) } returns viewHolder
        every { viewHolder.adapterPosition } returns 1
        every { parent.adapter } returns mockk<RecyclerView.Adapter<RecyclerView.ViewHolder>>(relaxed = true).apply {
            every { itemCount } returns 2
            every { getItemViewType(0) } returns GlobalSearchAdapter.VIEW_TYPE_FILE_OR_FOLDER
        }
        
        decoration.getItemOffsets(outRect, view, parent, state)
        
        verify(exactly = 1) { parent.getChildViewHolder(view) }
    }

    /**
     * 测试为 SearchLabelItemVH 设置正确的边距
     * 通过公共方法间接测试私有方法
     */
    @Test
    fun `getItemOffsets should set correct margins for SearchLabelItemVH`() {
        val viewHolder = mockk<GlobalSearchAdapter.SearchLabelItemVH>(relaxed = true)
        every { parent.getChildViewHolder(view) } returns viewHolder
        every { viewHolder.adapterPosition } returns 0
        every { viewHolder.oldPosition } returns RecyclerView.NO_POSITION
        every { parent.adapter } returns mockk<RecyclerView.Adapter<RecyclerView.ViewHolder>>(relaxed = true).apply {
            every { itemCount } returns 2
            every { getItemViewType(1) } returns GlobalSearchAdapter.VIEW_TYPE_FILE_OR_FOLDER
        }
        
        decoration.getItemOffsets(outRect, view, parent, state)
        
        assert(outRect.left == decoration.horizontalMargin)
        assert(outRect.right == decoration.horizontalMargin)
        assert(outRect.top == decoration.verticalMargin)
        assert(outRect.bottom == decoration.verticalMargin)
    }

    /**
     * 测试当 adapterPosition 为 NO_POSITION 时
     * 应该使用 oldPosition 来计算 SearchLabelItemVH 的边距
     */
    @Test
    fun `getItemOffsets should use oldPosition when adapterPosition is NO_POSITION for SearchLabelItemVH`() {
        val viewHolder = mockk<GlobalSearchAdapter.SearchLabelItemVH>(relaxed = true)
        every { parent.getChildViewHolder(view) } returns viewHolder
        every { viewHolder.adapterPosition } returns RecyclerView.NO_POSITION
        every { viewHolder.oldPosition } returns 1
        every { parent.adapter } returns mockk<RecyclerView.Adapter<RecyclerView.ViewHolder>>(relaxed = true).apply {
            every { itemCount } returns 3
            every { getItemViewType(2) } returns GlobalSearchAdapter.VIEW_TYPE_FILE_OR_FOLDER
        }
        
        decoration.getItemOffsets(outRect, view, parent, state)
        
        assert(outRect.left == decoration.horizontalMargin)
        assert(outRect.right == decoration.horizontalMargin)
        assert(outRect.top == decoration.verticalMargin)
        assert(outRect.bottom == decoration.verticalMargin)
    }

    /**
     * 测试当两个位置都是 NO_POSITION 时
     * 应该提前返回并不设置底部边距
     */
    @Test
    fun `getItemOffsets should return early when both positions are NO_POSITION for SearchLabelItemVH`() {
        val viewHolder = mockk<GlobalSearchAdapter.SearchLabelItemVH>(relaxed = true)
        every { parent.getChildViewHolder(view) } returns viewHolder
        every { viewHolder.adapterPosition } returns RecyclerView.NO_POSITION
        every { viewHolder.oldPosition } returns RecyclerView.NO_POSITION
        
        // 重置outRect确保初始状态为(0,0,0,0)
        outRect.set(0, 0, 0, 0)
        
        decoration.getItemOffsets(outRect, view, parent, state)
        
        // 根据当前实现，即使提前返回，也已经设置了基础的margin
        assert(outRect.left == decoration.horizontalMargin)
        assert(outRect.right == decoration.horizontalMargin)
        assert(outRect.top == decoration.verticalMargin)
        assert(outRect.bottom == 0)
    }

    /**
     * 测试当下一个项不是文件类型时
     * 不应该为 SearchLabelItemVH 添加底部边距
     */
    @Test
    fun `getItemOffsets should not add bottom margin when next item is not file type for SearchLabelItemVH`() {
        val viewHolder = mockk<GlobalSearchAdapter.SearchLabelItemVH>(relaxed = true)
        every { parent.getChildViewHolder(view) } returns viewHolder
        every { viewHolder.adapterPosition } returns 0
        every { viewHolder.oldPosition } returns RecyclerView.NO_POSITION
        every { parent.adapter } returns mockk<RecyclerView.Adapter<RecyclerView.ViewHolder>>(relaxed = true).apply {
            every { itemCount } returns 2
            every { getItemViewType(1) } returns GlobalSearchAdapter.VIEW_TYPE_LABEL
        }
        
        decoration.getItemOffsets(outRect, view, parent, state)
        
        assert(outRect.left == decoration.horizontalMargin)
        assert(outRect.right == decoration.horizontalMargin)
        assert(outRect.top == decoration.verticalMargin)
        assert(outRect.bottom == 0)
    }

    /**
     * 测试当前一个项是标签类型时
     * 应该重置 SearchLabelItemVH 的顶部边距
     */
    @Test
    fun `getItemOffsets should reset top margin when previous item is label type for SearchLabelItemVH`() {
        val viewHolder = mockk<GlobalSearchAdapter.SearchLabelItemVH>(relaxed = true)
        every { parent.getChildViewHolder(view) } returns viewHolder
        every { viewHolder.adapterPosition } returns 1
        every { viewHolder.oldPosition } returns RecyclerView.NO_POSITION
        every { parent.adapter } returns mockk<RecyclerView.Adapter<RecyclerView.ViewHolder>>(relaxed = true).apply {
            every { itemCount } returns 2
            every { getItemViewType(0) } returns GlobalSearchAdapter.VIEW_TYPE_LABEL
        }
        
        decoration.getItemOffsets(outRect, view, parent, state)
        
        assert(outRect.left == decoration.horizontalMargin)
        assert(outRect.right == decoration.horizontalMargin)
        assert(outRect.top == 0)
        assert(outRect.bottom == 0)
    }

    /**
     * 测试当前一个项是文件类型时
     * 应该为 CategoryItemVH 添加顶部边距
     */
    @Test
    fun `getItemOffsets should add top margin when previous item is file type for CategoryItemVH`() {
        val viewHolder = mockk<CategoryItemVH>(relaxed = true)
        every { parent.getChildViewHolder(view) } returns viewHolder
        every { viewHolder.adapterPosition } returns 1
        every { viewHolder.oldPosition } returns RecyclerView.NO_POSITION
        every { parent.adapter } returns mockk<RecyclerView.Adapter<RecyclerView.ViewHolder>>(relaxed = true).apply {
            every { itemCount } returns 2
            every { getItemViewType(0) } returns GlobalSearchAdapter.VIEW_TYPE_FILE_OR_FOLDER
        }
        
        decoration.getItemOffsets(outRect, view, parent, state)
        
        assert(outRect.top == decoration.verticalMargin)
    }

    /**
     * 测试当 adapterPosition 为 NO_POSITION 时
     * 应该使用 oldPosition 来计算 CategoryItemVH 的边距
     */
    @Test
    fun `getItemOffsets should use oldPosition when adapterPosition is NO_POSITION for CategoryItemVH`() {
        val viewHolder = mockk<CategoryItemVH>(relaxed = true)
        every { parent.getChildViewHolder(view) } returns viewHolder
        every { viewHolder.adapterPosition } returns RecyclerView.NO_POSITION
        every { viewHolder.oldPosition } returns 1
        every { parent.adapter } returns mockk<RecyclerView.Adapter<RecyclerView.ViewHolder>>(relaxed = true).apply {
            every { itemCount } returns 2
            every { getItemViewType(0) } returns GlobalSearchAdapter.VIEW_TYPE_FILE_OR_FOLDER
        }
        
        decoration.getItemOffsets(outRect, view, parent, state)
        
        assert(outRect.top == decoration.verticalMargin)
    }

    /**
     * 测试当两个位置都是 NO_POSITION 时
     * 应该提前返回并不设置任何边距
     */
    @Test
    fun `getItemOffsets should return early when both positions are NO_POSITION for CategoryItemVH`() {
        val viewHolder = mockk<CategoryItemVH>(relaxed = true)
        every { parent.getChildViewHolder(view) } returns viewHolder
        every { viewHolder.adapterPosition } returns RecyclerView.NO_POSITION
        every { viewHolder.oldPosition } returns RecyclerView.NO_POSITION
        
        // 重置outRect确保初始状态为(0,0,0,0)
        outRect.set(0, 0, 0, 0)
        
        decoration.getItemOffsets(outRect, view, parent, state)
        
        assert(outRect.left == 0)
        assert(outRect.right == 0)
        assert(outRect.top == 0)
        assert(outRect.bottom == 0)
    }

    /**
     * 测试所有文件类型的视图类型是否被正确识别
     * 应该为文件类型添加顶部边距
     */
    @Test
    fun `getItemOffsets should add top margin for file view types`() {
        // 测试所有文件类型的视图类型是否被正确识别
        val fileTypes = listOf(
            GlobalSearchAdapter.VIEW_TYPE_FILE_OR_FOLDER,
            GlobalSearchAdapter.VIEW_TYPE_DRIVE_FILE,
            GlobalSearchAdapter.VIEW_TYPE_THIRD_APP_FILE,
            GlobalSearchAdapter.VIEW_TYPE_REMOTE_FILE,
            GlobalSearchAdapter.LARGE_VIEW_TYPE_FILE_OR_FOLDER,
            GlobalSearchAdapter.LARGE_VIEW_TYPE_REMOTE_FILE,
            GlobalSearchAdapter.LARGE_VIEW_TYPE_THIRD_APP_FILE,
            GlobalSearchAdapter.LARGE_VIEW_TYPE_DRIVE_FILE
        )
        
        for (fileType in fileTypes) {
            val viewHolder = mockk<CategoryItemVH>(relaxed = true)
            every { parent.getChildViewHolder(view) } returns viewHolder
            every { viewHolder.adapterPosition } returns 1
            every { viewHolder.oldPosition } returns RecyclerView.NO_POSITION
            every { parent.adapter } returns mockk<RecyclerView.Adapter<RecyclerView.ViewHolder>>(relaxed = true).apply {
                every { itemCount } returns 2
                every { getItemViewType(0) } returns fileType
            }
            
            val testOutRect = Rect()
            decoration.getItemOffsets(testOutRect, view, parent, state)
            
            assert(testOutRect.top == decoration.verticalMargin) { "Failed for viewType: $fileType" }
        }
    }

    /**
     * 测试非文件类型的视图类型
     * 不应该为非文件类型添加顶部边距
     */
    @Test
    fun `getItemOffsets should not add top margin for non-file view types`() {
        // 测试非文件类型的视图类型
        val nonFileTypes = listOf(
            GlobalSearchAdapter.VIEW_TYPE_LABEL,
            -1
        )
        
        for (nonFileType in nonFileTypes) {
            val viewHolder = mockk<CategoryItemVH>(relaxed = true)
            every { parent.getChildViewHolder(view) } returns viewHolder
            every { viewHolder.adapterPosition } returns 1
            every { viewHolder.oldPosition } returns RecyclerView.NO_POSITION
            every { parent.adapter } returns mockk<RecyclerView.Adapter<RecyclerView.ViewHolder>>(relaxed = true).apply {
                every { itemCount } returns 2
                every { getItemViewType(0) } returns nonFileType
            }
            
            val testOutRect = Rect()
            decoration.getItemOffsets(testOutRect, view, parent, state)
            
            assert(testOutRect.top == 0) { "Failed for viewType: $nonFileType" }
        }
        
        // 特殊处理null情况
        val viewHolder = mockk<CategoryItemVH>(relaxed = true)
        every { parent.getChildViewHolder(view) } returns viewHolder
        every { viewHolder.adapterPosition } returns 1
        every { viewHolder.oldPosition } returns RecyclerView.NO_POSITION
        every { parent.adapter } returns mockk<RecyclerView.Adapter<RecyclerView.ViewHolder>>(relaxed = true).apply {
            every { itemCount } returns 2
            every { getItemViewType(0) } returns GlobalSearchAdapter.VIEW_TYPE_LABEL
        }
        
        val testOutRect = Rect()
        decoration.getItemOffsets(testOutRect, view, parent, state)
        
        assert(testOutRect.top == 0) { "Failed for viewType: null" }
    }
}