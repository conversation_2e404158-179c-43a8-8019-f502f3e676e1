/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved.
 * * COLOROS_EDIT
 * * File: ItemChild
 * * Description: the model for list item show in adapter
 * * Version: 1.0
 * * Date : 2020/7/17
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/7/17       1.0         the model for list item show in adapter
 ****************************************************************/
package com.oplus.filemanager.keymove.ui.model

import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.keymove.ui.model.Visitable

data class ItemChild(val mFile: BaseFileBean) : Visitable {

    @Volatile
    private var mChosenState = Visitable.NOTHING_BE_CHOSEN

    override fun getSelectedSize(): Long {
        return if (mChosenState == Visitable.ALL_BE_CHOSEN) {
            mFile.mSize
        } else {
            0L
        }
    }

    override fun getChosenState(): Int {
        return mChosenState
    }

    override fun getSize(): Long {
        return mFile.mSize
    }

    override fun setChosenState(state: Int) {
        mChosenState = state
    }

    override fun getSelectedFile(): ArrayList<BaseFileBean> {
        val list = ArrayList<BaseFileBean>()
        if (mChosenState == Visitable.ALL_BE_CHOSEN) {
            list.add(mFile)
        }
        return list
    }

}