/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : CategoryAppBean.kt
 * * Description : CategoryAppBean  Bean
 * * Version     : 1.0
 * * Date        : 2020/9/7
 * * Author      : W9060445
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.cloudconfig.bean

import androidx.annotation.Keep
import com.oplus.nearx.cloudconfig.anotation.FieldIndex

@Keep
data class CategoryAppBean(
    @FieldIndex(index = 1)
    val mPackageName: String = "",

    @FieldIndex(index = 2)
    val mPaths: String = "",
) {
    fun isEmpty(): Boolean = mPackageName.isEmpty() || mPaths.isEmpty()
    fun isNotEmpty(): Boolean = isEmpty().not()
}