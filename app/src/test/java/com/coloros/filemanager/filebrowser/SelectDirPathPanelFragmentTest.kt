/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SelectFileBrowserLoaderTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2021/01/26
 ** Author      : 17610071263
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  laiyu        2021/01/26       1.0      create
 ***********************************************************************/
package com.coloros.filemanager.filebrowser

import com.coloros.filemanager.BaseTest
import com.coui.appcompat.button.COUIButton
import com.filemanager.common.constants.MessageConstant
import com.oplus.selectdir.SelectDirPathPanelFragment
import io.mockk.spyk
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.powermock.reflect.Whitebox
import org.robolectric.RuntimeEnvironment

@Ignore
class SelectDirPathPanelFragmentTest : BaseTest() {

    @Before
    override fun setUp() {
        mContext = RuntimeEnvironment.application
    }

    @Test
    fun `should return value when actionType is MSG_EDITOR_COPY`() {
        val fragment = spyk(SelectDirPathPanelFragment(), recordPrivateCalls = true)
        Whitebox.setInternalState(fragment, "mActionCode", MessageConstant.MSG_EDITOR_COPY)
        val selectbutton: COUIButton? = Whitebox.getInternalState(fragment, "mSelectButton")
        Whitebox.invokeMethod<Any>(fragment, "updateButtonView")
        Assert.assertEquals(selectbutton?.text, mContext?.getString(com.filemanager.common.R.string.copy_selected_target))
    }

    @Test
    fun `should return value when actionType is MSG_EDITOR_COMPRESS`() {
        val fragment = spyk(SelectDirPathPanelFragment(), recordPrivateCalls = true)
        Whitebox.setInternalState(fragment, "mActionCode", MessageConstant.MSG_EDITOR_COMPRESS)
        val selectbutton: COUIButton? = Whitebox.getInternalState(fragment, "mSelectButton")
        Whitebox.invokeMethod<Any>(fragment, "updateButtonView")
        Assert.assertEquals(selectbutton?.text, mContext?.getString(com.filemanager.common.R.string.compress_selected_target))
    }

    @Test
    fun `should return value when actionType is MSG_EDITOR_CUT`() {
        val fragment = spyk(SelectDirPathPanelFragment(), recordPrivateCalls = true)
        Whitebox.setInternalState(fragment, "mActionCode", MessageConstant.MSG_EDITOR_CUT)
        val selectbutton: COUIButton? = Whitebox.getInternalState(fragment, "mSelectButton")
        Whitebox.invokeMethod<Any>(fragment, "updateButtonView")
        Assert.assertEquals(selectbutton?.text, mContext?.getString(com.filemanager.common.R.string.move_selected_target))
    }

    @Test
    fun `should return value when actionType is MSG_EDITOR_DECOMPRESS`() {
        val fragment = spyk(SelectDirPathPanelFragment(), recordPrivateCalls = true)
        Whitebox.setInternalState(fragment, "mActionCode", MessageConstant.MSG_EDITOR_DECOMPRESS)
        val selectbutton: COUIButton? = Whitebox.getInternalState(fragment, "mSelectButton")
        Whitebox.invokeMethod<Any>(fragment, "updateButtonView")
        Assert.assertEquals(selectbutton?.text, mContext?.getString(com.filemanager.common.R.string.decompress_selected_target))
    }

}