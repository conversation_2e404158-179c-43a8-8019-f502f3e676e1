/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ApkFilesScanner
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/23 15:09
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/23       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.scanner

import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.base.FileLoader
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.controller.LoaderController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.fileservice.bean.WebFileBean
import com.oplus.fileservice.bean.WebScannerResult
import com.oplus.fileservice.fileServiceScope
import com.oplus.fileservice.filelist.FileSelectPathService
import com.oplus.fileservice.filelist.loader.WebApkLoader
import com.oplus.fileservice.filelist.utils.getDateAndTimeByDefaultFormat
import com.oplus.fileservice.utils.WebFileConstant
import com.oplus.fileservice.utils.WebMimeTypeHelper
import com.oplus.fileservice.utils.WebThumbCacheManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.apache.commons.io.FilenameUtils

class ApkFilesScanner(pageNo: Int, pageSize: Int, sortOrderType: Int, private var isNeedThumbnail: Boolean = true) : BaseUriScanner() {
    companion object {
        const val TAG = "ApkFilesScanner"
    }

    @VisibleForTesting
    var mPageNo = pageNo

    @VisibleForTesting
    var mPageSize = pageSize
    private var mTotal = 0
    private var mSortOrderType = sortOrderType
    private val mLoaderController = LoaderController()
    override fun scannerFiles(): WebScannerResult? {
        mLoaderController.initLoader(TAG.hashCode(), ServiceApkCallBack(mSortOrderType, false))
        return null
    }

    inner class ServiceApkCallBack(sortOrderType: Int, isNeedFilter: Boolean) :
        OnLoaderListener<MutableList<MediaFileWrapper>> {
        private var mSortOrder = sortOrderType
        private var mIsNeedFilter = isNeedFilter
        override fun onCreateLoader(): FileLoader<MutableList<MediaFileWrapper>>? {
            return WebApkLoader(
                MyApplication.sAppContext,
                UriHelper.geCategoryUri(CategoryHelper.CATEGORY_APK),
                MediaStoreCompat.getMediaCountSqlQuery(CategoryHelper.CATEGORY_APK), mSortOrder,
                mIsNeedFilter
            )
        }

        override fun onLoadComplete(result: MutableList<MediaFileWrapper>?) {
            val webFileBeans = pageData(result)
            fileServiceScope.launch(Dispatchers.IO) {
                if (isNeedThumbnail) {
                    WebThumbCacheManager.setThumbnailData(webFileBeans)
                }
                Log.d(TAG, "mScannerCallback , mPageNo =$mPageNo, mTotal =$mTotal")
                mScannerCallback?.scannerSuc(WebScannerResult(mPageNo, mTotal, webFileBeans))
            }
        }
    }

    @VisibleForTesting
    fun pageData(result: List<MediaFileWrapper>?): ArrayList<WebFileBean> {
        mTotal = result?.size ?: 0
        if (mPageSize < 1) {
            mPageSize = WebFileConstant.DEFAULT_PAGE_SIZE
            Log.d(TAG, "Service modify mPageSize $mPageSize")
        }
        var pageTotal = mTotal / mPageSize
        if (mTotal % mPageSize > 0) {
            pageTotal++
        }

        if (mPageNo > pageTotal) {
            Log.d(FileSelectPathService.TAG, "mPageNo > pageTotal is true, mPageNo = $mPageNo")
            mPageNo = pageTotal
        }
        if (mPageNo < 1) {
            mPageNo = 1
        }
        val start = mPageSize * (mPageNo - 1)
        val end = if (mPageSize * mPageNo > mTotal) {
            mTotal
        } else {
            mPageSize * mPageNo
        }

        val resultFileList = result?.subList(start, end)
        val resultWebFileBean = ArrayList<WebFileBean>()
        if (resultFileList != null) {
            for (mediaFileWrapper in resultFileList) {
                mediaFileWrapper.apply {
                    val webFileBean = WebFileBean()
                    webFileBean.mFileName = FilenameUtils.getBaseName(mDisplayName)
                    webFileBean.mFilePath = mData
                    webFileBean.mFileId = mId ?: 0
                    webFileBean.mFileSize = mSize
                    webFileBean.mFileFormat = FileTypeUtils.getExtension(mDisplayName)
                    webFileBean.mFileType =
                        WebMimeTypeHelper.getFileTypeFromExtension(webFileBean.mFileFormat)
                    webFileBean.mLastModifyDate = getDateAndTimeByDefaultFormat(mDateModified)
                    webFileBean.mDateModified = mDateModified
                    webFileBean.mLocalType = mLocalType
                    resultWebFileBean.add(webFileBean)
                }
            }
        }
        return resultWebFileBean
    }
}