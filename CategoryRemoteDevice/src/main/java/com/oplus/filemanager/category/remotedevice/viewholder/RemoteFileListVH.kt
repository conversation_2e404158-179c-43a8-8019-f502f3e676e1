/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemoteFileListVH
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.viewholder

import android.app.Activity
import android.content.Context
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.setPadding
import androidx.core.view.updateLayoutParams
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.base.CheckBoxAnimateInput
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.FileWrapper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.EmojiUtils
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.ViewUtils
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.TextViewSnippet
import kotlin.math.max

class RemoteFileListVH : BaseRemoteFileVH {

    companion object {
        private const val TAG = "RemoteFileListVH"
        private const val SECOND = 1000L
        fun getLayoutId(): Int {
            return com.oplus.filemanager.category.remotedevice.R.layout.remote_list_item
        }
    }

    private var mImg: FileThumbView
    private var mJumpImg: ImageView
    private var mTitle: TextViewSnippet
    private var mDetail: TextView
    private var mAnotherName: TextViewSnippet
    private var mVideoIcon: TextView
    private var apkIcon: ImageView
    private var mImgRadius: Int = 0
    private var mIsLabelFileList: Boolean = false
    private val docRadius =
        MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.dimen_2dp)
    private var rootView: ConstraintLayout
    private var iconContainer: View

    constructor(convertView: View, imgRadius: Int = 0, isLabelFileList: Boolean = false) : super(
        convertView
    ) {
        rootView = convertView.findViewById(R.id.file_browser_item_list_root)
        mImg = convertView.findViewById(R.id.file_list_item_icon)
        mJumpImg = convertView.findViewById(R.id.jump_mark)
        mTitle = convertView.findViewById(R.id.file_list_item_title)
        mDetail = convertView.findViewById(R.id.mark_file_list_item_detail)
        mAnotherName = convertView.findViewById(R.id.another_name_view)
        mCheckBox = convertView.findViewById(R.id.listview_scrollchoice_checkbox)
        mVideoIcon = convertView.findViewById(R.id.file_duration_tv)
        mImgRadius = imgRadius
        mIsLabelFileList = isLabelFileList
        apkIcon = convertView.findViewById(R.id.apk_icon)
        iconContainer = convertView.findViewById(R.id.file_list_item_icon_container)
    }

    /**
     * 大小屏切换时，需要重新获取屏幕宽度，根据屏幕宽度确认文件夹的最大标题宽度
     */
    private fun getFolderTitleMaxSize(context: Context): Int = if (context is Activity) {
        KtViewUtils.getWindowSize(context).x - context.getResources()
            .getDimensionPixelOffset(R.dimen.file_list_adapter_folder_max_size)
    } else {
        MyApplication.sAppContext.resources.getDimensionPixelOffset(R.dimen.file_list_item_info_selected_width_new)
    }

    override fun updateViewHolder(
        context: Context,
        key: Int?,
        file: BaseFileBean,
        choiceMode: Boolean,
        selectionArray: MutableList<Int>,
        adapter: BaseSelectionRecycleAdapter<*, *>
    ) {
        val path = file.mData
        val type = file.mLocalType
        if (path == null) {
            Log.d(TAG, "updateViewHolder path null")
            return
        }
        updateLeftRightMargin()
        val folderTitleMaxSize = getFolderTitleMaxSize(context)
        var titleMaxWidth = adapter.mNormalTitleMaxSize
        if (type == MimeTypeHelper.DIRECTORY_TYPE) {
            if (folderTitleMaxSize > 0) {
                titleMaxWidth = folderTitleMaxSize
            }
            mAnotherName.tag = path
            mAnotherName.visibility = View.VISIBLE
            if (!FeatureCompat.sIsExpRom) {
                mUpdateViewListener?.updateViewByAlias(mAnotherName, path)
            }
            if (mAnotherName.text.isEmpty()) {
                mAnotherName.visibility = View.GONE
                titleMaxWidth = max(adapter.mNormalTitleMaxSize, folderTitleMaxSize)
            }
        } else {
            if (folderTitleMaxSize > 0) {
                /*显示文件夹及文件的列表中，因文件夹有别名需要显示（文件夹名称的宽度=当前屏幕宽度-180dp），
                在手机上及小屏上，文件的名称要比文件夹的大，取用一个228dp的固定值。
                当比文件夹名称大时（即大屏），文件名称采用文件夹的最大宽度*/
                titleMaxWidth = max(titleMaxWidth, folderTitleMaxSize)
            }
            mAnotherName.visibility = View.GONE
        }
        val bean = file as RemoteFileBean
        val titleText = if (bean.shouldShowAlternativeName()) bean.alternativeName else bean.mDisplayName
        mTitle.maxWidth = titleMaxWidth
        mTitle.tag = path
        mDetail.tag = path
        mTitle.text = titleText
        mTitle.setTextViewStyle()
        mTitle.post {
            val title = titleText ?: return@post
            val moreOne = mTitle.isMoreThanOneLine(title)
            setIconConstraintSet(moreOne)
        }
        showImageAndDetail(context, file, type, path)
        //这里需要将设置alpha放在checkbox的动效前，不然动效的alpha会重置，导致进入选择模式时，置灰的选项中checkbox的alpha值不对
        processDirectoryFileAlpha(file, adapter, choiceMode)
        showCheckboxAndJmpImg(file, adapter, choiceMode)
        showVideoIconIfNeed(file, mVideoIcon)
        showApkIconIfNeed(file, apkIcon, false)
        updateDividerVisible(adapter.getRealFileItemCount() - 1, position = position)
    }

    private fun showCheckboxAndJmpImg(
        file: BaseFileBean,
        adapter: BaseSelectionRecycleAdapter<*, *>,
        choiceMode: Boolean
    ) {
        val isDir = file.isDir()
        if (isDir) {
            mJumpImg.setImageResource(com.support.appcompat.R.drawable.coui_btn_next)
            mJumpImg.setTag(R.id.mark_dir, true)
        } else {
            mJumpImg.setImageResource(com.oplus.filemanager.category.remotedevice.R.drawable.remote_file_icon)
            mJumpImg.setTag(R.id.mark_dir, true)
        }
        mCheckBox?.let {
            if (KtAppUtils.mIsOnePlusOverSea) {
                adapter.setCheckBoxAnim(
                    CheckBoxAnimateInput(
                        true,
                        choiceMode,
                        null,
                        it,
                        layoutPosition,
                        true,
                        userDefault = false
                    )
                )
            } else {
                adapter.setCheckBoxAnim(
                    CheckBoxAnimateInput(
                        true,
                        choiceMode,
                        mJumpImg,
                        it,
                        layoutPosition,
                        true,
                        userDefault = false
                    )
                )
            }
        }
        //这里是为了修复在选择模式下，向下滑动列表右侧的电脑图标和checkbox重合同时显示的问题
        if (choiceMode) {
            mJumpImg.visibility = View.INVISIBLE
        } else {
            mJumpImg.visibility = View.VISIBLE
        }
    }

    private fun processDirectoryFileAlpha(
        file: BaseFileBean,
        adapter: BaseSelectionRecycleAdapter<*, *>,
        choiceMode: Boolean
    ) {
        val containIllegalChar = EmojiUtils.containsIllegalCharFileName(file.mDisplayName)
        val canNotSelect = containIllegalChar || file.isDir()
        val needAlpha = choiceMode && canNotSelect
        val alpha = HiddenFileHelper.getAlphaWithHidden(needAlpha, adapter.mIsDarkModel)
        mTitle.alpha = alpha
        mDetail.alpha = alpha
        mImg.alpha = alpha
        mCheckBox?.alpha = alpha
    }

    private fun showImageAndDetail(
        context: Context,
        file: BaseFileBean,
        type: Int,
        path: String
    ) {
        FileImageVHUtils.updateFileListImgSize(context, mImg, file)
        if (file.mLocalType == MimeTypeHelper.IMAGE_TYPE || file.mLocalType == MimeTypeHelper.VIDEO_TYPE) {
            mImg.setStrokeStyle(FileThumbView.STROKE_2DP)
        } else {
            mImg.setStrokeStyle(FileThumbView.STROKE_NONE)
        }
        mImg.setDrmState(false)
        showImageAndDetailInternal(type, context, file, path)
        mImg.scaleType = ImageView.ScaleType.FIT_CENTER
    }

    private fun showImageAndDetailInternal(
        type: Int,
        context: Context,
        file: BaseFileBean,
        path: String
    ) {
        if (type == MimeTypeHelper.APPLICATION_TYPE) {
            mImg.let {
                it.setBorderStyle(0F, 0F)
                FileImageLoader.sInstance.clear(context, it)
                FileImageLoader.sInstance.displayApplicationWithDetail(
                    file,
                    it,
                    { applicationInfoDetail ->
                        val tagDetail = mDetail.tag as? String
                        if (applicationInfoDetail?.mPath == tagDetail && !TextUtils.isEmpty(
                                applicationInfoDetail?.mApkVersion
                            )
                        ) {
                            mDetail.visibility = View.VISIBLE
                            applicationInfoDetail?.mPath?.apply {
                                val apkDetail = Utils.formatSize(FileWrapper(this))
                                if (!TextUtils.isEmpty(apkDetail)) {
                                    mDetail.text = Utils.getApplicationDetailFormat(
                                        context,
                                        applicationInfoDetail.mApkName,
                                        applicationInfoDetail.mApkVersion,
                                        FileWrapper(applicationInfoDetail.mPath)
                                    )
                                }
                            }
                        } else {
                            showDetail(context, file, mDetail, path, true)
                        }
                    })
            }
        } else {
            mImg.let {
                val padding = when (type) {
                    MimeTypeHelper.VIDEO_TYPE -> {
                        MyApplication.sAppContext.resources.getDimension(R.dimen.file_list_image_padding)
                            .toInt()
                    }

                    else -> 0
                }
                val radius = ViewUtils.getIconRadius(type, docRadius, mImgRadius)
                it.setPadding(padding)
                FileImageLoader.sInstance.clear(context, it)
                FileImageLoader.sInstance.displayDefault(
                    file,
                    it,
                    0,
                    radius,
                    loadDocThumbnail = false,
                    isCoverError = true,
                    isSmallDoc = true
                )
            }
            mDetail.apply {
                visibility = View.VISIBLE
                showDetail(context, file, this, path, true)
            }
        }
    }

    private fun setIconConstraintSet(isMoreThanOneLine: Boolean) {
        (mTitle.parent as? RelativeLayout)?.let { titleRL ->
            //1行且图标和标题顶部对齐，两行且图标和标题居中对齐，此时才需要更新
            if (isMoreThanOneLine && mImg.y == titleRL.y) {
                return
            }
            if (isMoreThanOneLine.not() && mImg.y != titleRL.y) {
                return
            }
        }

        val constraintSet = ConstraintSet()
        constraintSet.apply {
            clone(rootView)
            clear(R.id.file_list_item_icon, ConstraintSet.TOP)
            clear(R.id.file_list_item_icon, ConstraintSet.BOTTOM)
            if (isMoreThanOneLine) {
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.TOP,
                    R.id.rl_item_title,
                    ConstraintSet.TOP
                )
            } else {
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )
            }
            applyTo(rootView)
        }
    }

    /**
     * 列表页需要调用以下方法
     * CloudConfigAction.updateViewByAlias(textViewSnippet, path)
     * CloudConfigAction定义在 module: ModuleRouter 中，直接在此Common module中引用会导致循环引用
     * 故声明此接口，在调用module实现（FileBrowser,LabelManager）
     */
    interface UpdateViewListener {
        fun updateViewByAlias(textViewSnippet: TextViewSnippet, path: String)
    }

    private var mUpdateViewListener: UpdateViewListener? = null

    fun setUpdateViewList(listener: UpdateViewListener) {
        mUpdateViewListener = listener
    }

    private fun showVideoIconIfNeed(file: BaseFileBean, videoIcon: TextView) {
        if (MimeTypeHelper.VIDEO_TYPE == file.mLocalType) {
            videoIcon.visibility = View.VISIBLE
            val ext = getExtForBean(file)
            ext?.let {
                mVideoIcon.text = it
            }
        } else {
            videoIcon.visibility = View.GONE
        }
    }

    fun setSelected(isSelected: Boolean) {
        rootView.isSelected = isSelected
    }

    private fun updateLeftRightMargin() {
        iconContainer.updateLayoutParams<ConstraintLayout.LayoutParams> {
            this.marginStart = FileImageVHUtils.getListLeftMargin()
        }
        mJumpImg.updateLayoutParams<ConstraintLayout.LayoutParams> {
            this.marginEnd = FileImageVHUtils.getListRightMargin()
        }
    }
}