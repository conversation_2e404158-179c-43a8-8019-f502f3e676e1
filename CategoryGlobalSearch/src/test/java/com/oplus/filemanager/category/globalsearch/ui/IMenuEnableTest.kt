/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : IMenuEnableTest
 ** Description : IMenuEnableTest Unit Test
 ** Version     : 1.0
 ** Date        : 2023/2/21
 ** Author      : wanghonglei
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  wanghonglei     2023/2/21      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui

import android.content.Context
import android.os.Bundle
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationType
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.NewFunctionSwitch
import com.oplus.filemanager.category.globalsearch.ui.navigation.SearchMenuEnableImpl
import com.oplus.filemanager.dfm.DFMManager
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import kotlin.test.assertEquals

class IMenuEnableTest {

    @Before
    fun setup() {
        mockkObject(MyApplication)
        val context: Context = mockk(relaxed = true)
        every { MyApplication.appContext }.returns(context)

        mockkStatic(NewFunctionSwitch::class)
        every { NewFunctionSwitch.isSupportDfmSearch } returns true

        mockkStatic(DFMManager::class)
        every { DFMManager.getCurrentStatus() } returns DFMManager.STATUS_META_DATA_OK
        every { DFMManager.openP2pConnectAndWaitDFSReady() } returns true
        every { DFMManager.checkStateSearchAvalable() } returns true
        every { DFMManager.getDFSDeviceName() } returns "OPPOPAD"
        every { DFMManager.getDFSDevice() } answers {
            val result = Bundle()
            result.putInt(KtConstants.DFM_DEVICE_TYPE, KtConstants.DFM_PHONE_TYPE)
            result
        }
        mockkStatic(VolumeEnvironment::class)
        mockkStatic(AndroidDataHelper::class)
        every { VolumeEnvironment.getInternalSdPath(context) } returns "/storage/emulated/0"
        every { VolumeEnvironment.getExternalSdPath(context) } returns "/storage/emulated/1"
        every { VolumeEnvironment.isSingleSdcard(context) } returns false
        every { AndroidDataHelper.hasAndroidDataFile(any()) } returns false
    }

    @After
    fun after() {
        unmockkObject(MyApplication)
        unmockkStatic(FeatureCompat::class)
        unmockkStatic(DFMManager::class)
        unmockkStatic(VolumeEnvironment::class)
        unmockkStatic(KtUtils::class)
    }


    @Ignore
    fun `should verify when isMenuLabelEnable`() {
        val dynamicMenuEnable = SearchMenuEnableImpl {
            val dataList = ArrayList<BaseFileBean>()
            val file1 = BaseFileBean()
            file1.mData = "/mnt/dfs/xxx/Download/"
            val file2 = BaseFileBean()
            file2.mData = "/storage/emulated/0/Download/Document"
            val file3 = BaseFileBean()
            file3.mData = "/storage/emulated/1/DCIM/"
            val file4 = BaseFileBean()
            file4.mData = "/storage/emulated/919/DCIM/"
            dataList.add(file1)
            dataList.add(file2)
            dataList.add(file3)
            dataList.add(file4)
            dataList
        }
        val result1 = dynamicMenuEnable.isMenuEnable(
            NavigationType.DEFAULT,
            com.filemanager.common.R.id.navigation_label
        )
        assertEquals(false, result1)
        val emptyMenuEnable = SearchMenuEnableImpl {
            emptyList()
        }
        val result2 = emptyMenuEnable.isMenuEnable(
            NavigationType.DEFAULT,
            com.filemanager.common.R.id.navigation_label
        )
        assertEquals(false, result2)
        val noDynamicMenuEnable = SearchMenuEnableImpl {
            val dataList = ArrayList<BaseFileBean>()
            val file2 = BaseFileBean()
            file2.mData = "/storage/emulated/0/Download/Document"
            dataList.add(file2)
            dataList
        }
        val result3 = noDynamicMenuEnable.isMenuEnable(
            NavigationType.DEFAULT,
            com.filemanager.common.R.id.navigation_label
        )
        assertEquals(true, result3)
    }


    @Test
    fun `should verify when isMenuDeleteEnable`() {
        val dynamicMenuEnable = SearchMenuEnableImpl {
            val dataList = ArrayList<BaseFileBean>()
            val file1 = BaseFileBean()
            file1.mData = "/mnt/dfs/xxx/Download/"
            val file2 = BaseFileBean()
            file2.mData = "/storage/emulated/0/Download/Document"
            val file3 = BaseFileBean()
            file3.mData = "/storage/emulated/1/DCIM/"
            val file4 = BaseFileBean()
            file4.mData = "/storage/emulated/919/DCIM/"
            dataList.add(file1)
            dataList.add(file2)
            dataList.add(file3)
            dataList.add(file4)
            dataList
        }
        val result1 = dynamicMenuEnable.isMenuEnable(
            NavigationType.DEFAULT,
            com.filemanager.common.R.id.navigation_delete
        )
        assertEquals(true, result1)
        val emptyMenuEnable = SearchMenuEnableImpl {
            emptyList()
        }
        val result2 = emptyMenuEnable.isMenuEnable(
            NavigationType.DEFAULT,
            com.filemanager.common.R.id.navigation_delete
        )
        assertEquals(false, result2)
    }


    @Test
    fun `should verify when isMenuRenameEnable`() {
        val dynamicMenuEnable = SearchMenuEnableImpl {
            val dataList = ArrayList<BaseFileBean>()
            val file1 = BaseFileBean()
            file1.mData = "/mnt/dfs/xxx/Download/"
            val file2 = BaseFileBean()
            file2.mData = "/storage/emulated/0/Download/Document"
            val file3 = BaseFileBean()
            file3.mData = "/storage/emulated/1/DCIM/"
            val file4 = BaseFileBean()
            file4.mData = "/storage/emulated/919/DCIM/"
            dataList.add(file1)
            dataList.add(file2)
            dataList.add(file3)
            dataList.add(file4)
            dataList
        }
        val result1 = dynamicMenuEnable.isMenuEnable(
            NavigationType.FILE_DRIVE,
            com.filemanager.common.R.id.navigation_rename
        )
        assertEquals(false, result1)
        val emptyMenuEnable = SearchMenuEnableImpl {
            emptyList()
        }
        val result2 = emptyMenuEnable.isMenuEnable(
            NavigationType.FILE_DRIVE,
            com.filemanager.common.R.id.navigation_rename
        )
        assertEquals(false, result2)
        val oneMenuEnable = SearchMenuEnableImpl {
            val dataList = ArrayList<BaseFileBean>()
            val file1 = BaseFileBean()
            file1.mData = "/mnt/dfs/xxx/Download/"
            dataList.add(file1)
            dataList
        }
        val result3 = oneMenuEnable.isMenuEnable(
            NavigationType.FILE_DRIVE,
            com.filemanager.common.R.id.navigation_rename
        )
        assertEquals(true, result3)
    }

    @Test
    fun `should verify when isMenuDownloadEnable`() {
        val dynamicMenuEnable = SearchMenuEnableImpl {
            val dataList = ArrayList<BaseFileBean>()
            val file1 = BaseFileBean()
            file1.mData = "/mnt/dfs/xxx/Download/"
            val file2 = BaseFileBean()
            file2.mData = "/storage/emulated/0/Download/Document"
            val file3 = BaseFileBean()
            file3.mData = "/storage/emulated/1/DCIM/"
            val file4 = BaseFileBean()
            file4.mData = "/storage/emulated/919/DCIM/"
            dataList.add(file1)
            dataList.add(file2)
            dataList.add(file3)
            dataList.add(file4)
            dataList
        }
        val result1 = dynamicMenuEnable.isMenuEnable(
            NavigationType.FILE_DRIVE,
            com.filemanager.common.R.id.navigation_download
        )
        assertEquals(false, result1)
        val emptyMenuEnable = SearchMenuEnableImpl {
            emptyList()
        }
        val result2 = emptyMenuEnable.isMenuEnable(
            NavigationType.FILE_DRIVE,
            com.filemanager.common.R.id.navigation_download
        )
        assertEquals(false, result2)
        val oneMenuEnable = SearchMenuEnableImpl {
            val dataList = ArrayList<BaseFileBean>()
            val file1 = BaseFileBean()
            file1.mData = "/mnt/dfs/xxx/Download/"
            dataList.add(file1)
            dataList
        }
        val result3 = oneMenuEnable.isMenuEnable(
            NavigationType.FILE_DRIVE,
            com.filemanager.common.R.id.navigation_download
        )
        assertEquals(true, result3)
    }

    @Test
    fun `should verify when isMenuOtherEnable`() {
        val dynamicMenuEnable = SearchMenuEnableImpl {
            val dataList = ArrayList<BaseFileBean>()
            val file1 = BaseFileBean()
            file1.mData = "/mnt/dfs/xxx/Download/"
            val file2 = BaseFileBean()
            file2.mData = "/storage/emulated/0/Download/Document"
            val file3 = BaseFileBean()
            file3.mData = "/storage/emulated/1/DCIM/"
            val file4 = BaseFileBean()
            file4.mData = "/storage/emulated/919/DCIM/"
            dataList.add(file1)
            dataList.add(file2)
            dataList.add(file3)
            dataList.add(file4)
            dataList
        }
        val result1 = dynamicMenuEnable.isMenuEnable(
            NavigationType.DEFAULT,
            com.filemanager.common.R.id.navigation_cut
        )
        assertEquals(true, result1)
        val emptyMenuEnable = SearchMenuEnableImpl {
            emptyList()
        }
        val result2 = emptyMenuEnable.isMenuEnable(
            NavigationType.DEFAULT,
            com.filemanager.common.R.id.navigation_cut
        )
        assertEquals(false, result2)
    }
}