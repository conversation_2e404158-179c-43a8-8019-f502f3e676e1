/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ArchiveIconHelper.kt
 * Description:
 *     The helper to obtain archive file icon
 *
 * Version: 1.0
 * Date: 2024-10-15
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-10-15   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.utils

import androidx.annotation.DrawableRes
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.KtThumbnailHelper

internal object ArchiveIconHelper {

    private val compressTypes = setOf(
        MimeTypeHelper.JAR_TYPE,
        MimeTypeHelper.RAR_TYPE,
        MimeTypeHelper.P7ZIP_TYPE,
        MimeTypeHelper.COMPRESSED_TYPE
    )

    @JvmStatic
    @DrawableRes
    fun getArchiveIconId(archiveFile: BaseFileBean): Int {
        val type = MimeTypeHelper.getCompressedTypeByPath(archiveFile.mDisplayName)
        if (type in compressTypes) {
            return KtThumbnailHelper.getClassifyResId(type)
        }
        return R.drawable.ic_file_compress_zip
    }
}