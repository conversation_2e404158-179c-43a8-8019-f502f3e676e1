/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.filebrowser
 * * Version     : 1.0
 * * Date        : 2020/5/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filebrowser

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import androidx.annotation.IdRes
import androidx.appcompat.view.menu.ActionMenuItem
import androidx.collection.ArrayMap
import androidx.collection.arrayMapOf
import androidx.core.view.doOnLayout
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.coroutineScope
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.responsiveui.config.UIConfig
import com.coui.responsiveui.config.UIConfig.WindowType
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimationController
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseFolderAnimAdapter.Companion.FILE_BROWSER_FOLDER_ANIM_DELAY
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RecyclerSelectionVMFragment
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.LoaderViewModel
import com.filemanager.common.controller.LoadingController
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.controller.SortPopupController
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.decoration.ItemDecorationFactory.Companion.GRID_ITEM_DECORATION_FILE_BROWSER
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.filepreview.IPreviewListFragment
import com.filemanager.common.filepreview.IPreviewOperate
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.GridSpanAnimationHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.OnAnimatorEndListener
import com.filemanager.common.helper.OnSpanChangeCallback
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.interfaces.IRefreshFragmentDataForDir
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortEntryView
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.AndroidDataHelper.hasAndroidDataFile
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.DragScrollHelper
import com.filemanager.common.utils.HighlightUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.utils.PathUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.ToolbarUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.BrowserPathBar
import com.filemanager.common.view.FeedbackFloatingButton
import com.filemanager.common.view.FileManagerRecyclerView
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.filebrowser.adapter.FileBrowserAdapter
import com.oplus.filemanager.addfilepanel.AddFileClickListener
import com.oplus.filemanager.addfilepanel.AddFileController
import com.oplus.filemanager.addfilepanel.bean.AddFileBean
import com.oplus.filemanager.interfaze.categoryglobalsearch.ICategoryGlobalSearchApi
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.main.IMain
import com.oplus.filemanager.interfaze.setting.ISetting
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.commons.io.FilenameUtils
import java.io.File

class FileBrowserFragment : RecyclerSelectionVMFragment<FileBrowserViewModel>(), OnBackPressed,
        NavigationBarView.OnItemSelectedListener, IPreviewListFragment, IRefreshFragmentDataForDir, AddFileClickListener {
    companion object {
        private const val TAG = "FileBrowserFragment"
        private const val FILE_BROWSER_FOLDER_ANIM_TIME = 100L
        private const val NOTIFY_CHANGED_DELAY = 300L
        private const val ID_ADD_FOLDER = 0
        private const val ID_ADD_FILE = 1
        private const val LOADING_DELAY_TIME = 500L
        private const val LOAD_DATA_DELAYED_TIME = 500L
        private const val OPERATE_SUCCESS = 200
    }
    private var mGridSpanAnimationHelper: GridSpanAnimationHelper? = null
    private var mToolbar: COUIToolbar? = null
    private var sortEntryView: SortEntryView? = null
    private var mTitle: String? = null
    private var mCurrentPath: String? = null
    private var mAdapter: FileBrowserAdapter? = null
    private var mLayoutManager: FileGridLayoutManager? = null
    private var mPathBar: BrowserPathBar? = null
    private var createFolderFab: FeedbackFloatingButton? = null
    private val mFolderTransformAnimator by lazy { FolderTransformAnimator() }
    private val mFileEmptyController by lazy { FileEmptyController(lifecycle) }
    private val mSortPopupController by lazy { SortPopupController(lifecycle) }
    private var mFileOperateController: NormalFileOperateController? = null
    private var mLoadingController: LoadingController? = null
    private val mSpacesItemDecoration by lazy {
        ItemDecorationFactory.getGridItemDecoration(GRID_ITEM_DECORATION_FILE_BROWSER)
    }
    private var isFromDetail = false
    var isFromShortcutFolder = false
    private var mNeedLoadData = false
    private var isChildDisplay = false
    private var bySideRefreshScanMode = false
    private var hasShowEmpty: Boolean = false

    private var scrollHelper: DragScrollHelper? = null

    /**
     * if change mScanModeState but don't want to run animation
     * You can set [mNeedSkipAnimation] to true, this variable will be changed to false after using it once
     */
    private var mNeedSkipAnimation: Boolean = true
        get() {
            return field.also {
                field = false
            }
        }

    private var previewOperate: IPreviewOperate? = null
    private var sideNavigationGridAnimController: SideNavigationWithGridLayoutAnimationController? = null

    private var mAddPopupWindow: COUIPopupListWindow? = null
    private val mAddPopupItemList: MutableList<PopupListItem> = mutableListOf()
    private val mAddArrayMap: ArrayMap<Int, String> = arrayMapOf(
        ID_ADD_FOLDER to appContext.resources.getString(com.filemanager.common.R.string.menu_file_list_new_folder),
        ID_ADD_FILE to appContext.resources.getString(com.filemanager.common.R.string.label_files_add_file)
    )
    private var mAddFileController: AddFileController? = null
    private var mFileServiceAction: IFileService? = null


    override fun getLayoutResId(): Int {
        return R.layout.filebrowser_fragment
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            baseVMActivity = activity as BaseVMActivity
            val bundle = arguments ?: return
            mCurrentPath = bundle.getString(KtConstants.P_CURRENT_PATH)
            mNeedLoadData = bundle.getBoolean(KtConstants.P_NEED_LOAD_DATA, false)
            isChildDisplay = bundle.getBoolean(KtConstants.P_CHILD_DISPLAY, false)
            mTitle = bundle.getString(KtConstants.P_TITLE)
            mAdapter = FileBrowserAdapter(it, <EMAIL>)
            mAdapter?.setHasStableIds(true)
            isFromDetail = bundle.getBoolean(KtConstants.FROM_DETAIL)
            isFromShortcutFolder = bundle.getBoolean(KtConstants.FROM_SHORTCUT_FOLDER)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        fragmentViewModel?.isFromDetail = isFromDetail
        fragmentViewModel?.mCurrentPath = mCurrentPath ?: ""
    }

    override fun initView(view: View) {
        mPathBar = view.findViewById(com.oplus.selectdir.R.id.path_bar)
        fragmentFastScroller = view.findViewById(R.id.fastScroller)
        fragmentRecyclerView = view.findViewById(R.id.recycler_view)
        mGridSpanAnimationHelper = fragmentRecyclerView?.let { GridSpanAnimationHelper(it) }
        initToolbar(view)
        sortEntryView = view.findViewById(com.filemanager.common.R.id.sort_entry_view)
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getBrowserKey(mCurrentPath))
        sortEntryView?.setClickSortListener {
            performClickMenuEvent(R.id.navigation_sort)
        }
        initCreateFolderFab(view)
        fragmentRecyclerView?.let {
            scrollHelper = DragScrollHelper(it)
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initToolbar(view: View) {
        rootView = view.findViewById(R.id.coordinator_layout)
        appBarLayout = view.findViewById(com.filemanager.common.R.id.appbar_layout)
        appBarLayout?.let {
            it.setOnTouchListener { _, _ ->
                return@setOnTouchListener true
            }
        }
        if (previewOperate?.isSupportPreview() != true) {
            mToolbar = view.findViewById(com.filemanager.common.R.id.toolbar)
        }
        toolbar = mToolbar
        mToolbar?.apply {
            title = mTitle
            titleMarginStart = 0
            isTitleCenterStyle = false
            inflateMenu(R.menu.file_browser_menu)
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this)
            setSearchMenuStatus(baseVMActivity?.sideNavigationStatus?.value, isChildDisplay)
            initPopupWindow()
        }
        if (previewOperate?.isSupportPreview() != true) {
            rootView?.apply {
                setPadding(paddingLeft,
                    COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom)
            }
        }
        baseVMActivity?.apply {
            setSupportActionBar(mToolbar)
            setNormalHomeDisplay(mCurrentPath)
        }
    }

    private fun initCreateFolderFab(view: View) {
        // createFolderFab = view.findViewById(R.id.add_folder_fab)
        updateFloatButtonMargin()
        createFolderFab?.setFloatingButtonClickListener {
            performClickMenuEvent(R.id.navigation_new_folder)
        }
    }

    /**
     * 触发menu的点击事件
     */
    @SuppressLint("RestrictedApi")
    private fun performClickMenuEvent(@IdRes menuId: Int) {
        val activity = baseVMActivity ?: return
        val menu = ActionMenuItem(activity, 0, menuId, 0, 0, "")
        onMenuItemSelected(menu)
    }

    private fun setToolbarMenuVisible(toolbar: COUIToolbar, visible: Boolean) {
        toolbar.menu.findItem(R.id.action_setting)?.isVisible = visible
        updateAddItemVisible(toolbar)
    }

    private fun updateAddItemVisible(toolbar: COUIToolbar) {
        val visible = AndroidDataHelper.allowEditAndroidData || !AndroidDataHelper.isAndroidDataPath(getCurrentPath())
        toolbar.menu.findItem(R.id.action_add)?.isVisible = visible
    }

    private fun setToolbarEditIcon(toolbar: COUIToolbar) {
        toolbar.menu?.findItem(R.id.actionbar_edit)?.apply {
            icon = null
            setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
        }
    }

    private fun updateEditAndSortMenu(toolbar: COUIToolbar) {
        val edit = toolbar.menu.findItem(R.id.actionbar_edit)
        edit?.isVisible = fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true
    }

    private fun initToolbarNormalMode(toolbar: COUIToolbar) {
        setNormalHomeDisplay(fragmentViewModel?.mPositionModel?.value?.mCurrentPath)
        toolbar.menu.clear()
        toolbar.isTitleCenterStyle = false
        toolbar.title = mTitle
        toolbar.inflateMenu(R.menu.file_browser_menu)

        updateEditAndSortMenu(toolbar)
        setToolbarMenuVisible(toolbar, !isChildDisplay)
        setToolbarEditIcon(toolbar)
        previewOperate?.onToolbarMenuUpdated(toolbar.menu)
        toolbar.post {
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
    }

    private fun initToolbarWithEditMode(toolbar: COUIToolbar) {
        baseVMActivity?.supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(false)
        }
        toolbar.apply {
            menu.clear()
            isTitleCenterStyle = true
            inflateMenu(com.filemanager.common.R.menu.menu_edit_mode)
            menu.findItem(com.filemanager.common.R.id.action_select_cancel)
        }
        toolbar.post {
            baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
        }
    }

    override fun createViewModel(): FileBrowserViewModel {
        val vm = ViewModelProvider(this)[FileBrowserViewModel::class.java]
        val sortMode = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getBrowserKey(mCurrentPath))
        mFileOperateController = NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_FILE_BROWSER, vm, sortMode).also {
            it.setResultListener(FileOperatorListenerImpl(vm, false))
        }
        return vm
    }

    override fun initData(savedInstanceState: Bundle?) {
        fragmentViewModel?.isFromShortcutFolder = isFromShortcutFolder
        fragmentRecyclerView?.let { recyclerView ->
            mLayoutManager = FileGridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1).apply {
                spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        val viewType = mAdapter?.getItemViewType(position)
                        val isSingleLine =
                                (viewType == BaseFileBean.TYPE_FILE_LIST_HEADER)
                                || (viewType == BaseFileBean.TYPE_LABEL_FILE)
                                || (viewType == BaseFileBean.TYPE_FILE_AD)
                        return if (isSingleLine) spanCount else 1
                    }
                }
            }
            recyclerView.addItemDecoration(mSpacesItemDecoration)
            recyclerView.isNestedScrollingEnabled = true
            recyclerView.clipToPadding = false
            recyclerView.layoutManager = mLayoutManager!!
            recyclerView.itemAnimator = mFolderTransformAnimator
            recyclerView.itemAnimator?.apply {
                changeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                addDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                removeDuration = FILE_BROWSER_FOLDER_ANIM_TIME
                moveDuration = FILE_BROWSER_FOLDER_ANIM_TIME
            }
            resetRecyclerViewHoriontalPadding(recyclerView)

            mAdapter?.let {
                recyclerView.adapter = it
            }
            appBarLayout?.doOnLayout {
                if (isAdded) {
                    val paddingBottom = if (recyclerView.paddingBottom == 0) {
                        appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                    } else {
                        recyclerView.paddingBottom
                    }
                    recyclerView.setPadding(recyclerView.paddingLeft,
                        KtViewUtils.getRecyclerViewTopPadding(appBarLayout), recyclerView.paddingRight, paddingBottom)
                }
            }
            recyclerView.setLoadStateForScroll(this)
        }
        initPathBar()
        if (mNeedLoadData) {
            onResumeLoadData()
        }
        if (actionCheckPermission().not()) {
            sortEntryView?.setFileCount(0)
        }
        TouchShareSupplier.attach(this, mFileOperateController)
    }

    private fun resetRecyclerViewHoriontalPadding(recyclerView: FileManagerRecyclerView) {
        (activity as? FileBrowserActivity)?.let {
            Log.d("resetRecyclerViewHoriontalPadding", UIConfigMonitor.getWindowType().name)
            if (UIConfigMonitor.getWindowType() == UIConfig.WindowType.LARGE) {
                recyclerView.setPadding(
                    appContext.resources.getDimensionPixelSize(com.oplus.labelmanager.R.dimen.dp_16),
                    recyclerView.paddingTop,
                    appContext.resources.getDimensionPixelSize(com.oplus.labelmanager.R.dimen.dp_16),
                    recyclerView.paddingBottom
                )
            } else {
                recyclerView.setPadding(0, recyclerView.paddingTop, 0, recyclerView.paddingBottom)
            }
        }
    }

    override fun permissionSuccess() {
        onResumeLoadData()
    }

    override fun getScanMode(): Int {
        return fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
    }

    override fun setScanMode(mode: Int) {
        fragmentViewModel?.mBrowseModeState?.value = mode
    }

    override fun isSelectionMode(): Boolean {
        return fragmentViewModel?.isInSelectMode() ?: false
    }

    override fun setPreviewOperate(operate: IPreviewOperate) {
        previewOperate = operate
    }

    private fun initPathBar() {
        mPathBar?.let {
            Log.d(TAG, "initPathBar mCurrentPath = $mCurrentPath isFromShortcutFolder=$isFromShortcutFolder")
            val currentPath = mCurrentPath ?: return
            if (currentPath.isNotEmpty()) {
                fragmentViewModel?.initPathHelper(currentPath)
                it.setPathHelper(fragmentViewModel?.mPathHelp)
                it.setOnPathClickListener(object : BrowserPathBar.OnPathClickListener {
                    override fun onPathClick(index: Int, path: String?) {
                        fragmentViewModel?.clickPathBar(index)
                    }
                }).setTextFocusChangeListener(object : BrowserPathBar.OnTextFocusColorChangeListener {
                    override fun onFocusChange(currentFocusText: String) {
                        mTitle = currentFocusText
                        if (fragmentViewModel?.mNeedScroll == true) {
                            KtAnimationUtil.showUpdateToolbarTitleWithAnimate(mToolbar, mTitle)
                        } else {
                            mToolbar?.title = mTitle
                        }
                    }
                }).show()
                it.setIsFromShortcutFolder(isFromShortcutFolder)
                fragmentViewModel?.checkShortcutFolderRootPath(currentPath)?.let { fRootPAth ->
                    it.updateShortcutFolderRootPath(fRootPAth)
                }
                it.setCurrentPath(currentPath)
            }
        }
    }

    override fun startObserve() {
        fragmentRecyclerView?.post {
            val viewModule = fragmentViewModel ?: return@post
            if (!isAdded) {
                return@post
            }
            viewModule.mModeState.listModel.observe(this) { listModel ->
                onListModelChanged(viewModule, listModel)
            }
            viewModule.uiState.observe(this) { fileUiModel ->
                onFileUiModelChanged(fileUiModel, viewModule)
            }
            viewModule.mPositionModel.observe(this) { positionModel ->
                onPositionModelChanged(viewModule, positionModel)
                viewModule.previewClickedFileLiveData.value = null
            }
            startScanModeObserver()
            startObserveLoadState()
            startCreateFolderObserve()
            startSideNavigationStatusObserver()
            viewModule?.previewClickedFileLiveData?.observe(this) {
                mAdapter?.setPreviewClickedFile(it)
            }
        }
    }

    private fun onListModelChanged(viewModule: FileBrowserViewModel, listModel: Int) {
        if (!viewModule.mModeState.initState) {
            mToolbar?.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            return
        }
        Log.d(TAG, "mListModel=$listModel")
        if (listModel == KtConstants.LIST_SELECTED_MODE) {
            HighlightUtil.endAnimation()
            (baseVMActivity as? NavigationInterface)?.apply {
                showNavigation()
                viewModule.setNavigateItemAble(this@apply)
            }
            mAdapter?.setSelectEnabled(true)
            previewEditedFiles(fragmentViewModel?.getSelectItems())
            fragmentRecyclerView?.let {
                val bottomView = baseVMActivity?.findViewById<View>(R.id.navigation_tool)
                val paddingBottom = KtViewUtils.getSelectModelPaddingBottom(it, bottomView)
                it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, paddingBottom)
                fragmentFastScroller?.apply { trackMarginBottom = paddingBottom }
            }
            mToolbar?.let {
                changeActionModeAnim(it, {
                    initToolbarWithEditMode(it)
                    refreshSelectToolbar(it)
                })
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
            createFolderFab?.visibility = View.GONE
        } else {
            previewClickedFile(
                fragmentViewModel?.previewClickedFileLiveData?.value,
                fragmentViewModel?.previewClickedFileLiveData
            )
            mAdapter?.setSelectEnabled(false)
            fragmentRecyclerView?.let {
                val paddingBottom = appContext.resources
                    .getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
                it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, paddingBottom)
                fragmentFastScroller?.trackMarginBottom =
                    appContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
            }
            mToolbar?.let {
                changeActionModeAnim(it, {
                    initToolbarNormalMode(it)
                    refreshScanModeItemIcon(it)
                }, (it.getTag(com.filemanager.common.R.id.toolbar_animation_id) == true))
                it.setTag(com.filemanager.common.R.id.toolbar_animation_id, true)
            }
            (baseVMActivity as? NavigationInterface)?.apply {
                hideNavigation()
            }
            createFolderFab?.visibility = View.VISIBLE
        }
    }

    private fun onFileUiModelChanged(
        fileUiModel: FileBrowserViewModel.FileBrowserUiModel,
        viewModule: FileBrowserViewModel
    ) {
        Log.d(
            TAG, "UiModel mUiState =" + fileUiModel.fileList.size + ","
                    + fileUiModel.selectedList.size + "," + fileUiModel.keyWord
        )
        sortEntryView?.setFileCount(fragmentViewModel?.getRealFileSize() ?: 0)
        HighlightUtil.cancelAnimation()
        if (fileUiModel.stateModel.listModel.value == KtConstants.LIST_SELECTED_MODE) {
            mToolbar?.let {
                refreshSelectToolbar(it)
            }
            (fileUiModel.fileList as? ArrayList<BaseFileBean>)?.let {
                mFolderTransformAnimator.mIsFolderInAnimation = viewModule.mIsFolderIn
                mAdapter?.setData(it, fileUiModel.selectedList, viewModule.mNeedScroll)
                (baseVMActivity as? FileBrowserActivity)?.mAdManager?.refreshIfListChanged()
                previewEditedFiles(fragmentViewModel?.getSelectItems())
            }
            createFolderFab?.visibility = View.GONE
        } else {
            previewClickedFile(
                fragmentViewModel?.previewClickedFileLiveData?.value,
                fragmentViewModel?.previewClickedFileLiveData
            )
            if (fileUiModel.fileList.isEmpty()) {
                showEmptyView()
            } else {
                hideEmptyView()
            }
            mToolbar?.let {
                refreshScanModeItemIcon(it)
                updateEditAndSortMenu(it)
                updateAddItemVisible(it)
            }
            setNormalHomeDisplay(viewModule.mPositionModel.value?.mCurrentPath)
            if (fileUiModel.fileList !is ArrayList<BaseFileBean>) {
                return
            }
            val adapter = mAdapter ?: return
            adapter.setKeyWord(fileUiModel.keyWord)
            mFolderTransformAnimator.mIsFolderInAnimation = viewModule.mIsFolderIn
            (fileUiModel.fileList as? ArrayList<BaseFileBean>)?.let { list ->
                adapter.setData(list, fileUiModel.selectedList, viewModule.mNeedScroll)
            }
            val rootPath = Environment.getExternalStorageDirectory().absolutePath
            if (viewModule.mPositionModel.value?.mCurrentPath?.equals(rootPath) != true) {
                return
            }
            (baseVMActivity as? FileBrowserActivity)?.let { act ->
                (fileUiModel.fileList as? ArrayList<BaseFileBean>)?.let { list ->
                    act.mAdManager?.requestPhoneStorageAd(act, adapter, list)
                }
            }
        }
    }

    private fun onPositionModelChanged(
        viewModule: FileBrowserViewModel,
        positionModel: FileBrowserViewModel.PositionModel
    ) {
        if (!viewModule.mModeState.initState) {
            return
        }
        HighlightUtil.cancelAnimation()
        mPathBar?.let {
            if (it.getCurrentPath() != positionModel.mCurrentPath) {
                it.setCurrentPath(positionModel.mCurrentPath)
            }
        }
        appBarLayout?.postDelayed({
            if (positionModel.mOffset != 0) {
                Log.d(TAG, "itemAnimator set null")
                fragmentRecyclerView?.itemAnimator = null
            } else {
                if (fragmentRecyclerView?.itemAnimator == null) {
                    Log.d(TAG, "itemAnimator set mFolderTransformAnimator")
                    fragmentRecyclerView?.itemAnimator = mFolderTransformAnimator
                }
            }
            mLayoutManager?.scrollToPositionWithOffset(
                positionModel.mPosition,
                positionModel.mOffset
            )
            viewModule.mPositionModel.value?.mPosition = 0
            viewModule.mPositionModel.value?.mOffset = 0
            viewModule.mNeedScroll = false
        }, FILE_BROWSER_FOLDER_ANIM_DELAY)
    }

    private fun startCreateFolderObserve() {
        val viewModule = fragmentViewModel ?: return
        viewModule.createFolderPosition.observe(this) {
            if (!viewModule.mModeState.initState || it == -1) {
                return@observe
            }
            if (fragmentRecyclerView?.scrollState != RecyclerView.SCROLL_STATE_IDLE) {
                fragmentRecyclerView?.stopScroll()
            }
            Log.d(TAG, "startCreateFolderObserve position:$it, current scroll state:${fragmentRecyclerView?.scrollState}")
            val offset = KtViewUtils.getRecyclerViewTopOffset()
            mLayoutManager?.scrollToPositionWithOffset(it, -offset)
            lifecycleScope.launch(Dispatchers.Default) {
                delay(NOTIFY_CHANGED_DELAY)
                withContext(Dispatchers.Main) {
                    HighlightUtil.highlightPosition = it
                    mAdapter?.notifyItemChanged(it, 0)
                    viewModule.createFolderPosition.value = -1
                }
            }
        }
    }

    override fun onDestroyView() {
        Log.i(TAG, "onDestroyView")
        //这里调用反注册关系，将loader和FolderTransformAnimator两者解除关系
        mFolderTransformAnimator.unRegisterNeddSkipAnimator()
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        HighlightUtil.cancelAnimation()
        mPathBar?.setOnPathClickListener(null)
        sideNavigationGridAnimController?.destroy()
        sideNavigationGridAnimController = null
        mAddFileController = null
        mFileServiceAction = null
    }

    private fun startScanModeObserver() {
        mNeedSkipAnimation = true
        fragmentViewModel?.mBrowseModeState?.observe(this) { scanMode ->
            mToolbar?.let {
                val needSkipAnimation = mNeedSkipAnimation
                if (needSkipAnimation) {
                    refreshScanModeAdapter(scanMode)
                } else {
                    fragmentRecyclerView?.let { recyclerView ->
                        recyclerView.mTouchable = false
                        recyclerView.stopScroll()
                    }
                    mGridSpanAnimationHelper?.startLayoutAnimation(object : OnSpanChangeCallback {
                        override fun onSpanChangeCallback() {
                            mLayoutManager?.scrollToPosition(0)
                            refreshScanModeAdapter(scanMode)
                        }
                    }, object : OnAnimatorEndListener {
                        override fun onAnimatorEnd() {
                            fragmentRecyclerView?.mTouchable = true
                        }
                    })
                }
                delay { refreshScanModeItemIcon(it, needSkipAnimation) }
            }
        }
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (UIConfigMonitor.shouldUpdateUIWhenConfigChange(configList)) {
            val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
            refreshScanModeAdapter(scanMode)
           baseVMActivity?.let {
                mFileEmptyController.changeEmptyFileIcon()
            }
            mSortPopupController.hideSortPopUp()
            mFileOperateController?.onConfigurationChanged(context?.resources?.configuration)
            if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
            }
            fragmentRecyclerView?.let {
                resetRecyclerViewHoriontalPadding(it)
            }
            updateFloatButtonMargin()
            updateLeftRightMargin()
        }
    }

    override fun updateLeftRightMargin() {
        val scanMode = fragmentViewModel?.mBrowseModeState?.value ?: KtConstants.SCAN_MODE_LIST
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            mAdapter?.notifyDataSetChanged()
        }
        mPathBar?.updateLeftRightMargin()
        sortEntryView?.updateLeftRightMargin()
    }

    override fun isEmptyList(): Boolean {
        return hasShowEmpty
    }

    override fun handleDragScroll(event: DragEvent): Boolean {
        scrollHelper?.handleDragScroll(event)
        return scrollHelper?.getRecyclerViewScrollState() ?: false
    }

    override fun resetScrollStatus() {
        scrollHelper?.resetDragStatus()
    }

    override fun getSelectedItemView(): ArrayList<View>? {
        val selectedFiles = DragUtils.getSelectedFiles()
        val itemViewList = ArrayList<View>()
        val size = fragmentViewModel?.uiState?.value?.fileList?.size ?: return null
        selectedFiles?.forEach { fileBean ->
            val indexOf = fragmentViewModel?.uiState?.value?.fileList?.indexOf(fileBean) ?: return null
            if (indexOf >= 0 && indexOf < size) {
                val itemView =
                    mRecyclerView?.findViewHolderForAdapterPosition(indexOf)?.itemView
                itemView?.let { itemViewList.add(it) }
            }
        }
        return itemViewList
    }

    override fun setNavigateItemAble() {
        activity?.lifecycle?.coroutineScope?.launch(Dispatchers.Main) {
            val selectItems = fragmentViewModel?.getSelectItems()
            val selectItemSize = selectItems?.size ?: 0
            (baseVMActivity as? NavigationInterface)?.setNavigateItemAble(
                (selectItemSize > 0 && !DragUtils.isDragging),
                hasDrmFile(selectItems), hasAndroidData = hasAndroidDataFile(selectItems)
            )
        }
    }

    private fun updateFloatButtonMargin() {
        val activity = baseVMActivity ?: return
        createFolderFab?.updateLayoutParams<MarginLayoutParams> {
            marginEnd = if (UIConfigMonitor.getWindowType() == WindowType.LARGE) {
                activity.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_40dp)
            } else {
                activity.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_24dp)
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun refreshScanModeAdapter(scanMode: Int) {
        val spanCount = ItemDecorationFactory.getGridItemCount(activity, scanMode,
            GRID_ITEM_DECORATION_FILE_BROWSER)
        mLayoutManager?.spanCount = spanCount
        mSpacesItemDecoration.mSpanCount = spanCount
        mAdapter?.apply {
            mScanViewModel = scanMode
            mFolderTransformAnimator.mSkipAddRemoveAnimation = true
            checkComputingAndExecute {
                notifyDataSetChanged()
            }
            val rootPath = Environment.getExternalStorageDirectory().absolutePath
            if (fragmentViewModel?.mPositionModel?.value?.mCurrentPath?.equals(rootPath) == true) {
                (baseVMActivity as? FileBrowserActivity)?.mAdManager?.refreshByScanModeChanged()
            }
            setSearchMenuStatus(baseVMActivity?.sideNavigationStatus?.value, isChildDisplay)
            toolbar?.let { setToolbarEditIcon(it) }
        }
    }

    private fun refreshScanModeItemIcon(toolbar: COUIToolbar, needSkipAnimation: Boolean = true) {
        toolbar.menu.findItem(R.id.actionbar_scan_mode)?.let {
            val desc: String
            val resId: Int = if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
                desc = appContext.getString(com.filemanager.common.R.string.palace_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_grid
            } else {
                desc = appContext.getString(com.filemanager.common.R.string.list_view)
                com.filemanager.common.R.drawable.color_tool_menu_ic_mode_list
            }
            it.contentDescription = desc
            setSearchMenuStatus(baseVMActivity?.sideNavigationStatus?.value, isChildDisplay)
            setScanModeStatue(it, desc, needSkipAnimation, resId)
        }
    }

    private fun setScanModeStatue(
        it: MenuItem,
        desc: String,
        needSkipAnimation: Boolean,
        resId: Int
    ) {
        if (isChildDisplay) {
            if (fragmentViewModel?.uiState?.value?.fileList?.isNotEmpty() == true
                || (baseVMActivity?.sideNavigationStatus?.value == KtConstants.LIST_SELECTED_MODE)) {
                it.icon = null
                it.title = desc
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            } else {
                it.title = null
                if (needSkipAnimation) {
                    it.setIcon(resId)
                } else {
                    KtAnimationUtil.updateMenuItemWithFadeAnimate(it, resId, baseVMActivity)
                }
                it.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            }
        } else {
            it.icon = null
            it.title = desc
            it.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
        }
    }

    private fun setSearchMenuStatus(status: Int?, isChildDisplay: Boolean) {
        toolbar?.menu?.findItem(R.id.actionbar_search)?.apply {
            if ((status == KtConstants.LIST_SELECTED_MODE
                && fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST)
                && isChildDisplay) {
                icon = null
                setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
            } else {
                setIcon(com.filemanager.common.R.drawable.color_tool_menu_ic_search)
                setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS or MenuItem.SHOW_AS_ACTION_COLLAPSE_ACTION_VIEW)
            }
        }
    }

    private fun startSideNavigationStatusObserver() {
        baseVMActivity?.sideNavigationStatus?.observe(this) { status ->
            Log.d(TAG, "sideNavigationStatus observe: $status")
            toolbar?.let {
                setToolbarEditIcon(it)
                setSearchMenuStatus(status, isChildDisplay)
                if (bySideRefreshScanMode) {
                    refreshScanModeItemIcon(it, isChildDisplay)
                }
                bySideRefreshScanMode = true
            }
        }
    }

    private fun startObserveLoadState() {
        activity?.let {
            val bgColor = context?.resources?.getColor(com.support.appcompat.R.color.coui_color_background_with_card)
            mLoadingController = LoadingController(it, this).apply {
                //中屏下这里的loading转圈不在最中间，这里传入一个rootView，让loadingView处于fragment中间，而不是屏幕中间
                observe(fragmentViewModel?.dataLoadState, rootView) {
                    val intercept = fragmentViewModel?.mFileBrowserCallBack?.isLoadNewPath()?.not() ?: false
                    intercept
                }
                bgColor?.let {
                    setBackgroundColor(it)
                }
                setDeleyShowTime(LOADING_DELAY_TIME)
                setShowAinimate(true)
                setDissapearAnimate(true)
                setShowLoadingTips(false)
                //这里将LoadingController和FolderTransformAnimator通过接口方式关联起来
                mFolderTransformAnimator.registerNeedSkipAnimator(this)
            }
        }
    }

    private fun refreshSelectToolbar(toolbar: COUIToolbar) {
        val checkedCount = fragmentViewModel?.uiState?.value?.selectedList?.size ?: 0
        val isSelectAll = (fragmentViewModel?.getRealFileSize() == fragmentViewModel?.uiState?.value?.selectedList?.size)
        ToolbarUtil.updateToolbarTitle(toolbar, checkedCount, isSelectAll)
        var isEnable = fragmentViewModel?.uiState?.value?.selectedList?.isNotEmpty() ?: false
        isEnable = isEnable && !DragUtils.isDragging
        if (baseVMActivity is NavigationInterface) {
            (baseVMActivity as NavigationInterface).setNavigateItemAble(
                isEnable,
                hasDrmFile(fragmentViewModel?.getSelectItems()),
                hasAndroidDataFile(fragmentViewModel?.getSelectItems())
            )
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume hasShowEmpty:$hasShowEmpty")
        if (hasShowEmpty) return
        if (fragmentViewModel?.uiState?.value?.fileList?.isEmpty() == true) {
            showEmptyView()
        }
        sortEntryView?.setDefaultOrder(SortRecordModeFactory.getBrowserKey(mCurrentPath))
    }

    override fun onPause() {
        super.onPause()
        hasShowEmpty = false
    }

    private fun showEmptyView() {
        if ((baseVMActivity != null) && (rootView != null)) {
            fragmentViewModel?.mPositionModel?.value?.mCurrentPath?.apply {
                if (PathUtils.isDocumentsUIPath(this) ||
                    (AndroidDataHelper.isAndroidDataPath(this) && AndroidDataHelper.openAndroidData != true)
                ) {
                    val emptyMarginTop = mPathBar?.let { pathBar -> (pathBar.y + pathBar.measuredHeight).toInt() } ?: 0
                    mFileEmptyController.showGuideDocumentsUIView(baseVMActivity!!, rootView!!, this, emptyMarginTop, reloadDataFunction = {
                        Log.d(TAG, "showEmptyView reloadDataFunction")
                        onResumeLoadData()
                    })
                } else {
                    mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!)
                    mFileEmptyController.setFileEmptyTitle(com.filemanager.common.R.string.empty_file)
                    val des = if (AndroidDataHelper.openAndroidData == true &&
                        AndroidDataHelper.isAndroidDataPath(this)
                    ) {
                        ""
                    } else {
                        appContext.resources.getString(com.filemanager.common.R.string.add_content_tips)
                    }
                    mFileEmptyController.setEmptySummaryVisibilityAndContent(
                        View.VISIBLE,
                        des
                    )
                }
                sortEntryView?.visibility = View.INVISIBLE
                fragmentRecyclerView?.visibility = View.INVISIBLE
                createFolderFab?.visibility = View.GONE
                hasShowEmpty = true
                listEmptyFile()
                Log.d(TAG, "showEmptyView")
            }
        }
    }

    private fun hideEmptyView() {
        mFileEmptyController.hideFileEmptyView()
        sortEntryView?.visibility = View.VISIBLE
        fragmentRecyclerView?.visibility = View.VISIBLE
        createFolderFab?.visibility = View.VISIBLE
    }

    override fun getFragmentInstance(): Fragment {
        return this
    }

    override fun setFragmentArguments(arguments: Bundle?) {
        this.arguments = arguments
    }

    override fun setPreviewToolbar(toolbar: COUIToolbar?) {
        mToolbar = toolbar
    }

    override fun onResumeLoadData() {
        if (!isAdded) {
            return
        }
        if (checkShowPermissionEmpty(false)) {
            sortEntryView?.setFileCount(0)
            mPathBar?.visibility = View.GONE
            return
        }
        mPathBar?.visibility = View.VISIBLE
        val bundle = arguments ?: return
        val currentPath = bundle.getString(KtConstants.P_CURRENT_PATH)
        isFromDetail = bundle.getBoolean(KtConstants.FROM_DETAIL)
        val oldIsFromShortcutFolder = isFromShortcutFolder
        isFromShortcutFolder = bundle.getBoolean(KtConstants.FROM_SHORTCUT_FOLDER)
        if (currentPath.isNullOrBlank()) {
            Log.w(TAG, "onResumeLoadData mCurrentPath is null or empty")
            if ((baseVMActivity != null) && (rootView != null)) {
                mFileEmptyController.showFileEmptyView(baseVMActivity!!, rootView!!)
            }
        } else {
            val lastPath = fragmentViewModel?.mPositionModel?.value?.mCurrentPath
            Log.d(TAG, "onResumeLoadData fromDetail:$isFromDetail isFromShortcutFolder=$isFromShortcutFolder current:$currentPath lastPath:$lastPath")
            mCurrentPath = lastPath ?: currentPath
            mCurrentPath?.let { mCurrentPath ->
                mPathBar?.let {
                    if (mCurrentPath != it.getCurrentPath()) {
                        it.setCurrentPath(mCurrentPath)
                    }
                }
            }
            if (oldIsFromShortcutFolder && !isFromShortcutFolder) {
                Log.d(TAG, "change exit shortcut mode")
                resetIsFromShortcut()
                setCurrentFromOtherSide(mCurrentPath!!)
            }
            fragmentViewModel?.initLoader(LoaderViewModel.getLoaderController(this), mCurrentPath!!)
        }

        if (bundle.getBoolean(KtConstants.P_RESET_TOOLBAR, false)) {
           baseVMActivity?.apply {
               setSupportActionBar(mToolbar)
               baseVMActivity?.supportActionBar?.apply {
                   setDisplayHomeAsUpEnabled(!isChildDisplay || isFromDetail)
                   setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
               }
            }
            bundle.putBoolean(KtConstants.P_RESET_TOOLBAR, false)
        }
        fragmentViewModel?.loadFolderLogo()
        /**
         * 防止isPCScreenCast判断可能是错误状态，try initPCConnectObserver，
         * 确保isPCScreenCast获取状态正确
         */
        PCConnectAction.onActivityResume()
    }

    override fun pressBack(): Boolean {
        val activity = activity ?: return false
        val result = fragmentViewModel?.pressBack() ?: false
        if (activity is FileBrowserActivity) {
            return result
        }
        if (!result) {
            if (isFromDetail) {
                val mainAction = Injector.injectFactory<IMain>()
                mainAction?.backPreviousFragment(-1, activity)
                return true
            }
        }
        return result
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return activity?.let {
            mFileOperateController?.onNavigationItemSelected(it, item)
        } ?: false
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.file_browser_menu, menu)
        mToolbar?.apply {
            setToolbarMenuVisible(this, !isChildDisplay)
            setToolbarEditIcon(this)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        arguments?.putString(
            KtConstants.P_CURRENT_PATH,
            fragmentViewModel?.mPositionModel?.value?.mCurrentPath ?: mCurrentPath
        )
        super.onSaveInstanceState(outState)
    }

    @Suppress("LongMethod")
    override fun onMenuItemSelected(item: MenuItem): Boolean {
        if (null == item || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        return when (item.itemId) {
            android.R.id.home -> {
                baseVMActivity?.onBackPressed()
                true
            }
            R.id.actionbar_search -> {
                val categoryGlobalSearchApi = Injector.injectFactory<ICategoryGlobalSearchApi>()
                categoryGlobalSearchApi?.startGlobalSearch(
                    baseVMActivity,
                    CategoryHelper.CATEGORY_FILE_BROWSER,
                    null,
                    fragmentViewModel?.mPositionModel?.value?.mCurrentPath
                )
                OptimizeStatisticsUtil.pageSearch(OptimizeStatisticsUtil.ALL_STORAGE)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SEARCH, Constants.PAGE_FILE_BROWSER)
                true
            }
            R.id.actionbar_edit -> {
                if (actionCheckPermission().not()) {
                   baseVMActivity?.showSettingGuildDialog()
                    return true
                }
                if (fragmentViewModel?.dataLoadState?.value == OnLoaderListener.STATE_START) {
                    Log.d(TAG, "onMenuItemSelected actionbar_edit mFileLoadState = STATE_START")
                } else {
                    StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.FILE_BROWSER_EDIT)
                    OptimizeStatisticsUtil.pageEdit(OptimizeStatisticsUtil.ALL_STORAGE)
                    fragmentViewModel?.changeListMode(KtConstants.LIST_SELECTED_MODE)
                }
                true
            }
            R.id.navigation_sort -> {
                if (fragmentViewModel?.dataLoadState?.value == OnLoaderListener.STATE_START
                    && (mAdapter?.itemCount ?: 0) == 0) {
                    Log.d(TAG, "onMenuItemSelected navigation_sort mFileLoadState = STATE_START")
                } else {
                    baseVMActivity?.let {
                        StatisticsUtils.nearMeStatistics(it, StatisticsUtils.SEQUENCE_ACTION)
                        OptimizeStatisticsUtil.pageSort(OptimizeStatisticsUtil.ALL_STORAGE)
                        val anchorView: View? = view?.findViewById(com.filemanager.common.R.id.sort_entry_anchor)
                        mSortPopupController.showSortPopUp(
                            it,
                            -1,
                            anchorView,
                            SortRecordModeFactory.getBrowserKey(mCurrentPath),
                            object : SelectItemListener {

                                override fun onDismiss() {
                                    sortEntryView?.rotateArrow()
                                }

                                override fun onPopUpItemClick(flag: Boolean, sortMode: Int, isDesc: Boolean) {
                                    if (flag) {
                                        sortEntryView?.setSortOrder(sortMode, isDesc)
                                        fragmentViewModel?.sortReload()
                                    }
                                }
                            })
                    }
                }
                true
            }
            R.id.navigation_new_folder -> {
                if (actionCheckPermission().not()) {
                   baseVMActivity?.showSettingGuildDialog()
                    return true
                }
                fragmentViewModel?.mPositionModel?.value?.mCurrentPath?.also {
                    StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.FILE_BROWSER_NEW_FOLDER)
                   baseVMActivity?.let { act ->
                        val from = when (mTitle) {
                            act.getString(com.filemanager.common.R.string.string_all_files) -> OptimizeStatisticsUtil.CREATE_FOLDER_FROM_PHONE_STORAGE
                            act.getString(com.filemanager.common.R.string.storage_external) -> OptimizeStatisticsUtil.CREATE_FOLDER_FROM_SD_CARD
                            else -> -1
                        }
                        OptimizeStatisticsUtil.createFolder(from)
                    }
                    activity?.let { ac -> mFileOperateController?.onCreateFolder(ac, it) }
                }
                true
            }
            R.id.actionbar_scan_mode -> {
                fragmentViewModel?.clickScanModeItem(baseVMActivity)
                true
            }
            R.id.action_setting -> {
                StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.FILE_BROWSER_SETTING)
                OptimizeStatisticsUtil.pageSetting(OptimizeStatisticsUtil.ALL_STORAGE)
                StatisticsUtils.statisticsPageExposure(activity, "", Constants.PAGE_SETTING, Constants.PAGE_FILE_BROWSER)
                Injector.injectFactory<ISetting>()?.startSettingActivity(activity)
                true
            }
            R.id.action_select_all -> {
                fragmentViewModel?.clickToolbarSelectAll()
                true
            }
            com.filemanager.common.R.id.action_select_cancel -> {
                if (fragmentViewModel?.mModeState?.listModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                }
                true
            }
            R.id.action_add -> {
                mAddPopupWindow?.show(mToolbar?.menuView)
                true
            }
            else -> {
                false
            }
        }
    }

    override fun fromSelectPathResult(requestCode: Int, paths: List<String>?) {
        activity?.let { mFileOperateController?.onSelectPathReturn(it, requestCode, paths) }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
            fragmentViewModel?.loadData()
        }
    }

    override fun setCurrentFromOtherSide(currentPath: String) {
        mCurrentPath = currentPath
        val shortcutRootPath = fragmentViewModel?.checkShortcutFolderRootPath(currentPath)
        fragmentViewModel?.mPathHelp?.updateRootPath(currentPath, shortcutRootPath)
        fragmentViewModel?.setCurrentFromOtherSide(currentPath)
    }

    fun resetIsFromShortcut() {
        arguments?.putBoolean(KtConstants.FROM_SHORTCUT_FOLDER, false)
        isFromShortcutFolder = false
        fragmentViewModel?.isFromShortcutFolder = false
        mPathBar?.setIsFromShortcutFolder(false)
        mCurrentPath?.let {
            mPathBar?.setCurrentPath(it, true)
        }
    }

    override fun onItemClick(item: com.oplus.dropdrag.recycleview.ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        fragmentViewModel?.uiState?.value?.let { uiModel ->
            if (uiModel.stateModel.listModel.value != KtConstants.LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.keyMap[item.selectionKey].apply {
                Log.d(TAG, "onItemClick baseFile=$this")
            } ?: return true
            if (baseFile.mIsDirectory) {
                clickToNextDir(baseFile)
            } else {
                activity?.let {
                    val previewResult =
                        previewClickedFile(baseFile, fragmentViewModel?.previewClickedFileLiveData)
                    if (!previewResult) {
                        var mediaImgIds: ArrayList<String>? = null
                        // 判断当前点击是否是媒体库中的图片
                        lifecycleScope.launch(context = Dispatchers.IO) {
                            if (FileMediaHelper.isImgAndInMedia(baseFile)) {
                                // 获取当前视图中的图片文件，按照排序顺序生成一个media id列表，传给相册
                                mediaImgIds = FileMediaHelper.getMediaImgIds(
                                    baseFile, fragmentViewModel?.uiState?.value?.fileList)
                                withContext(Dispatchers.Main) {
                                    mFileOperateController?.onFileClick(it, baseFile, e, mediaImgIds)
                                }
                            } else {
                                withContext(Dispatchers.Main) {
                                    mFileOperateController?.onFileClick(it, baseFile, e, mediaImgIds)
                                }
                            }
                        }
                    }
                    baseFile.mData?.let { path ->
                        OptimizeStatisticsUtil.clickStorageFolderFile(path, "file", FilenameUtils.getExtension(path))
                    }
                }
            }
        }
        return true
    }
    private fun setNormalHomeDisplay(currentPath: String?) {
       baseVMActivity?.supportActionBar?.apply {
            val rootPath = Environment.getExternalStorageDirectory().absolutePath
            if (currentPath?.equals(rootPath) == true) { // 在根目录
                setDisplayHomeAsUpEnabled(!isChildDisplay || isFromDetail)
            } else {
                setDisplayHomeAsUpEnabled(true)
            }
            setHomeAsUpIndicator(com.support.appcompat.R.drawable.coui_back_arrow)
        }
    }

    override fun setIsHalfScreen(isHalfScreen: Boolean) {
        isChildDisplay = isHalfScreen
        arguments?.putBoolean(KtConstants.P_CHILD_DISPLAY, isChildDisplay)
        mToolbar?.let {
            setToolbarMenuVisible(it, !isChildDisplay)
            setToolbarEditIcon(it)
        }
       baseVMActivity?.supportActionBar?.apply {
            if (fragmentViewModel?.isInSelectMode() == true) {
                setDisplayHomeAsUpEnabled(true)
                setHomeAsUpIndicator(com.support.snackbar.R.drawable.coui_menu_ic_cancel)
            } else {
                setNormalHomeDisplay(fragmentViewModel?.mPositionModel?.value?.mCurrentPath)
            }
        }
    }

    override fun checkPermission() {
       baseVMActivity?.checkStoragePermission()
    }

    override fun backToTop() {
        fragmentRecyclerView?.fastSmoothScrollToTop()
    }

    override fun updatedLabel() {}

    override fun getCurrentPath(): String {
        return fragmentViewModel?.mPositionModel?.value?.mCurrentPath ?: ""
    }

    fun onRefreshData() {
        fragmentViewModel?.loadData()
    }

    override fun onClickDir(path: String) {
        val file = File(path)
        if (file.parent == fragmentViewModel?.mPositionModel?.value?.mCurrentPath) {
            val baseFile = BaseFileBean()
            baseFile.mData = path
            if (File(path).isDirectory) {
                baseFile.mLocalType = MimeTypeHelper.DIRECTORY_TYPE
            }
            clickToNextDir(baseFile)
        }
    }

    private fun clickToNextDir(baseFile: BaseFileBean) {
        fragmentRecyclerView?.paddingTop?.let { paddingTop ->
            val viewPosition = mLayoutManager?.findFirstVisibleItemPosition() ?: 0
            val offset = mLayoutManager?.findViewByPosition(viewPosition)?.top ?: paddingTop
            fragmentViewModel?.onDirClick(
                baseVMActivity,
                baseFile,
                viewPosition,
                offset - paddingTop
            )
        }
    }

    override fun createPermissionEmptyView(rootView: ViewGroup?) {
        super.createPermissionEmptyView(rootView)
        super.updatePermissionEmptyMarginTop()
    }

    override fun getInstallPerMission() {
        Log.d(TAG, "getInstallPerMission")
        (baseVMActivity as BaseVMActivity).checkGetInstalledAppsPermission()
    }

    override fun getPermissionEmptyViewStubId(): Int {
        return R.id.common_permission_empty
    }

    private fun previewClickedFile(file: BaseFileBean?, clickFileLiveData: MutableLiveData<BaseFileBean?>?): Boolean {
        return previewOperate?.previewClickedFile(file, clickFileLiveData) ?: false
    }

    private fun previewEditedFiles(files: List<BaseFileBean>?): Boolean {
        mFileOperateController?.setPreviewOpen(isPreviewOpen())
        return previewOperate?.previewEditedFiles(files) ?: false
    }

    private fun listEmptyFile() {
        previewOperate?.listEmptyFile()
    }

    private fun isPreviewOpen(): Boolean {
        return previewOperate?.isPreviewOpen() ?: false
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_FILE_BROWSER
    }

    override fun getOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.OperateResultListener? =
        mFileOperateController?.pickResultListener()

    override fun exitSelectionMode() {
        if (isSelectionMode()) {
            fragmentViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        if (fragmentViewModel?.mBrowseModeState?.value == KtConstants.SCAN_MODE_LIST) {
            return false
        }
        initSideNavigationWithGridLayoutAnimationController()
        if (sideNavigationGridAnimController == null) {
            return false
        }
        if (sideNavigationGridAnimController?.isDoingAnimation() == true) {
            return true
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val sideNavigationWidth = baseVMActivity?.sideNavigationContainer?.drawerViewWidth ?: 0
        sideNavigationGridAnimController?.doOpenOrCloseAnim(
            isOpen,
            windowWidth,
            sideNavigationWidth,
            GRID_ITEM_DECORATION_FILE_BROWSER
        )
        return true
    }

    private fun initSideNavigationWithGridLayoutAnimationController() {
        if (sideNavigationGridAnimController == null) {
            fragmentRecyclerView?.let { recyclerView ->
                baseVMActivity?.sideNavigationContainer?.let { side ->
                    sideNavigationGridAnimController = SideNavigationWithGridLayoutAnimationController(recyclerView, side)
                }
            }
        }
    }

    override fun refreshDataForDir(path: String, category: Int) {
        if (path == getCurrentPath() && (category == CategoryHelper.CATEGORY_FILE_BROWSER || category == CategoryHelper.CATEGORY_SDCARD_BROWSER)) {
            onResumeLoadData()
        }
    }

    override fun renameToShortCutFolder(newPath: String, file: BaseFileBean): String {
        val oldPath = file.mData
        if (oldPath?.let { getCurrentPath().contains(it) && newPath != oldPath && File(newPath).exists() } == true) {
            if (isFromDetail) {
                fragmentViewModel?.apply {
                    mCurrentPath = mCurrentPath.replace(oldPath, newPath)
                    mPositionModel.value?.mCurrentPath = getCurrentPath().replace(oldPath, newPath)
                    arguments?.putString(KtConstants.P_CURRENT_PATH, mPositionModel.value?.mCurrentPath)
                }
                setCurrentFromOtherSide(getCurrentPath())
                getCurrentPath().let { mCurrentPath ->
                    setPathView(mCurrentPath)
                }
            } else {
                val newCurrentPath = getCurrentPath().replace(oldPath, newPath)
                if (File(newCurrentPath).exists()) {
                    arguments?.putString(KtConstants.P_CURRENT_PATH, newCurrentPath)
                    newCurrentPath.let { mCurrentPath ->
                        setPathView(mCurrentPath)
                    }
                    fragmentViewModel?.initLoader(LoaderViewModel.getLoaderController(this), newCurrentPath)
                    fragmentViewModel?.mPositionModel?.value?.mCurrentPath = newCurrentPath
                }
            }
        }
        return getCurrentPath()
    }

    override fun renameToLabel(newName: String, labelId: Long) {}


    private fun setPathView(mCurrentPath: String) {
        mPathBar?.let {
            if (mCurrentPath != it.getCurrentPath()) {
                fragmentViewModel?.checkShortcutFolderRootPath(mCurrentPath)?.let { fRootPAth ->
                    it.updateShortcutFolderRootPath(fRootPAth)
                }
                it.setCurrentPath(mCurrentPath, true)
            }
        }
    }

    private fun initPopupWindow() {
        fun createListItems(): List<PopupListItem> {
            val builder = PopupListItem.Builder()
            val list = java.util.ArrayList<PopupListItem>()
            for (map in mAddArrayMap) {
                val item = builder.reset().setTitle(map.value).setId(map.key).build()
                list.add(item)
            }
            return list
        }

        mAddPopupWindow = COUIPopupListWindow(context).apply {
            val list: List<PopupListItem> = createListItems()
            mAddPopupItemList.clear()
            mAddPopupItemList.addAll(list)
            itemList = list
            setDismissTouchOutside(true)
            setOnDismissListener {
                Log.d(TAG, "mAddPopupWindow OnDismissListener")
            }
            setOnItemClickListener { _, _, position, _ ->
                if (actionCheckPermission().not()) {
                    baseVMActivity?.showSettingGuildDialog()
                    mAddPopupWindow?.dismiss()
                    return@setOnItemClickListener
                }
                when (mAddPopupItemList[position].id) {
                    ID_ADD_FOLDER -> performClickMenuEvent(R.id.navigation_new_folder)
                    ID_ADD_FILE -> showAddFileDialog()
                }
                mAddPopupWindow?.dismiss()
            }
        }

        if (mAddFileController == null) {
            mAddFileController = activity?.let { AddFileController() }
            mAddFileController?.setOnAddFileClickListener(this)
        }

        if (mFileServiceAction == null) {
            mFileServiceAction = Injector.injectFactory<IFileService>()
        }
    }

    /**弹出添加文件面板*/
    private fun showAddFileDialog() {
        if (mTitle == null) return
        mAddFileController?.mPath = fragmentViewModel?.mPositionModel?.value?.mCurrentPath ?: ""
        activity?.supportFragmentManager?.let { mAddFileController?.showAddFileDialog(mTitle!!, it, lifecycle) }
    }

    /**
     * 点击添加文件
     */
    override fun onAddFileClick(selectData: List<AddFileBean>) {
        if (Utils.isQuickClick()) {
            Log.w(TAG, "click too fast, try later")
            return
        }
        mAddFileController?.dismissAddFileDialog()
        lifecycleScope.launch(context = Dispatchers.IO) {
            val oldFiles = mutableListOf<File>()
            val newFile = File(fragmentViewModel?.mPositionModel?.value?.mCurrentPath)
            selectData.forEach {
                oldFiles.add(File(it.mData))
            }
            val code = mFileServiceAction?.cutTo(requireContext(), oldFiles, newFile)
            Log.d(TAG, "cut file result code=$code")

            withContext(context = Dispatchers.Main) {
                Handler(Looper.getMainLooper()).postDelayed({ onResumeLoadData() }, LOAD_DATA_DELAYED_TIME)
                if (code == OPERATE_SUCCESS) {
                    CustomToast.showShort(com.filemanager.common.R.string.label_add_files_successed)
                } else {
                    CustomToast.showShort(com.filemanager.common.R.string.drag_privacy_add_failed)
                }
            }
        }
    }
    override fun getSelectItems(): ArrayList<out BaseFileBean>? {
        return fragmentViewModel?.getSelectItems()
    }
}