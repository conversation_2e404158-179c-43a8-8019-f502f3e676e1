/***********************************************************
 * Copyright (C), 2010-2024, Oplus Mobile Comm Corp., Ltd.
 * File:  - SelectionViewModelTest.java
 * Description:
 * Version: 1.0
 * Date : 2024/02/27
 * Author: keweiwei
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * keweiwei      2024/02/27    1.0     create
 ****************************************************************/
package com.filemanager.common.base

import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.Log
import com.oplus.dropdrag.SelectionTracker
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.spyk
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test

class SelectionViewModelTest {
    private lateinit var selectionViewModel: TestSelectionViewModel

    private lateinit var navigationInterface: NavigationInterface

    @Before
    fun setUp() {
        selectionViewModel = spyk(TestSelectionViewModel())
        navigationInterface = mockk(relaxed = true)
        val context = mockk<Context>(relaxed = true)
        mockkObject(MyApplication)
        every { appContext }.returns(context)
        mockkStatic(:: hasDrmFile)
        mockkStatic(Log::class)
        mockkStatic(AndroidDataHelper::class)
        mockkStatic(VolumeEnvironment::class)
        every { VolumeEnvironment.getInternalSdPath(context) }.returns("test")
    }

    @After
    fun tearDown() {
        unmockkAll()
    }


    @Test
    fun `should right when call setNavigateItemAble`() {
        //given
        val uiState = mockk<MutableLiveData<BaseUiModel<BaseFileBean>>>()
        val uiModel = mockk<BaseUiModel<BaseFileBean>>()
        every { selectionViewModel.uiState } returns uiState
        val selectedList = mockk<ArrayList<Int>>()
        every {  uiState.value } returns uiModel
        every { uiModel.selectedList } returns selectedList
        val list = ArrayList<BaseFileBean>()
        every { selectionViewModel.getSelectItems() } returns list
        every { hasDrmFile(list) } returns false
        every { navigationInterface.setNavigateItemAble(false, false) } just runs
        every { Log.d(any(), any()) } just runs
        every { AndroidDataHelper.hasAndroidDataFile(any()) } returns false
        //when
        selectionViewModel.setNavigateItemAble(navigationInterface)
        //then
        verify { navigationInterface.setNavigateItemAble(false, false) }
    }
}

private class TestSelectionViewModel : SelectionViewModel<BaseFileBean, BaseUiModel<BaseFileBean>>() {
    override fun getRealFileSize(): Int {
        return 0
    }

    override fun loadData() {
    }

    override fun getRecyclerViewScanMode(): SelectionTracker.LAYOUT_TYPE {
        return SelectionTracker.LAYOUT_TYPE.GRID
    }
}