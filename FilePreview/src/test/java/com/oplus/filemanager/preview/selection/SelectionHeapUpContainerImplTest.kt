package com.oplus.filemanager.preview.selection

import android.os.SystemClock
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatTextView
import androidx.test.platform.app.InstrumentationRegistry
import com.filemanager.common.utils.Log
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.widget.SelectionHeapUpView
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * SelectionHeapUpContainerImpl 的单元测试类
 * 使用Robolectric测试框架在Android环境中运行测试
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])  // 将SDK版本从28改为29以解决错误
class SelectionHeapUpContainerImplTest {

    // 模拟的视图组件
    private lateinit var containerRoot: ViewGroup
    private lateinit var heapUpView: SelectionHeapUpView
    private lateinit var filesCountView: AppCompatTextView
    private lateinit var filesStatView: AppCompatTextView
    private lateinit var loadingView: View
    private lateinit var selectionHeapUpContainer: SelectionHeapUpContainerImpl

    /**
     * 在每个测试方法执行前的初始化操作
     * 1. 模拟Log类
     * 2. 创建所有需要的模拟视图对象
     * 3. 设置视图查找的模拟返回值
     * 4. 创建待测试的SelectionHeapUpContainerImpl实例
     */
    @Before
    fun setUp() {
        // 模拟Log类的静态方法
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } returns Unit

        // 创建各个视图的模拟对象
        containerRoot = mockk(relaxed = true)
        heapUpView = mockk(relaxed = true)
        filesCountView = mockk(relaxed = true)
        filesStatView = mockk(relaxed = true)
        loadingView = mockk(relaxed = true)

        // 设置findViewById的模拟返回值
        every { containerRoot.findViewById<SelectionHeapUpView>(R.id.selection_files_heap_up) } returns heapUpView
        every { containerRoot.findViewById<AppCompatTextView>(R.id.selection_files_count) } returns filesCountView
        every { containerRoot.findViewById<AppCompatTextView>(R.id.selection_files_stat_info) } returns filesStatView
        every { containerRoot.findViewById<View>(R.id.loading_layout) } returns loadingView

        // 创建待测试的实例
        selectionHeapUpContainer = SelectionHeapUpContainerImpl(containerRoot)
    }

    /**
     * 测试showAsLoading方法在参数为true时的行为
     * 1. 应该显示加载视图
     * 2. 应该隐藏其他视图
     * 3. 应该设置startLoadingTime
     * 4. 应该记录正确的日志
     */
    @Test
    fun `test showAsLoading when true should show loading and hide other views`() {
        // Given - 设置初始条件：加载视图当前不可见
        every { loadingView.visibility } returns View.GONE

        // When - 调用方法
        selectionHeapUpContainer.showAsLoading(true)

        // Then - 验证结果
        verify { loadingView.visibility = View.VISIBLE }  // 验证加载视图变为可见
        verify { heapUpView.visibility = View.INVISIBLE }  // 验证堆叠视图变为不可见
        verify { filesCountView.visibility = View.INVISIBLE }  // 验证文件计数视图变为不可见
        verify { filesStatView.visibility = View.INVISIBLE }  // 验证文件状态视图变为不可见
        assertEquals(true, selectionHeapUpContainer.startLoadingTime != null)  // 验证加载时间被设置
        verify { Log.d("SelectionHeapUpContainerImpl", "showAsLoading: true") }  // 验证日志记录
    }

    /**
     * 测试showAsLoading方法在参数为false时的行为
     * 1. 应该隐藏加载视图
     * 2. 应该显示其他视图
     * 3. 应该清空startLoadingTime
     * 4. 应该记录正确的日志
     */
    @Test
    fun `test showAsLoading when false should hide loading and show other views`() {
        // Given - 设置初始条件：加载视图当前可见，并先调用true设置加载时间
        every { loadingView.visibility } returns View.VISIBLE
        selectionHeapUpContainer.showAsLoading(true) // 通过调用方法设置startLoadingTime

        // When - 调用方法
        selectionHeapUpContainer.showAsLoading(false)

        // Then - 验证结果
        verify { loadingView.visibility = View.GONE }  // 验证加载视图变为不可见
        verify { heapUpView.visibility = View.VISIBLE }  // 验证堆叠视图变为可见
        verify { filesCountView.visibility = View.VISIBLE }  // 验证文件计数视图变为可见
        verify { filesStatView.visibility = View.VISIBLE }  // 验证文件状态视图变为可见
        assertNull(selectionHeapUpContainer.startLoadingTime)  // 验证加载时间被清空
        verify { Log.d("SelectionHeapUpContainerImpl", "showAsLoading: false") }  // 验证日志记录
    }

    /**
     * 测试showAsLoading方法在已经是加载状态时的行为
     * 1. 不应该重复设置视图可见性
     * 2. 不应该修改startLoadingTime
     */
    @Test
    fun `test showAsLoading when already loading should do nothing`() {
        // Given - 设置初始条件：加载视图当前可见，并记录原始加载时间
        every { loadingView.visibility } returns View.VISIBLE
        selectionHeapUpContainer.showAsLoading(true) // 通过调用方法设置startLoadingTime
        val originalTime = selectionHeapUpContainer.startLoadingTime

        // When - 再次调用方法
        selectionHeapUpContainer.showAsLoading(true)

        // Then - 验证结果
        verify(exactly = 0) { loadingView.visibility = View.VISIBLE }  // 验证没有重复设置可见性
        verify(exactly = 0) { heapUpView.visibility = View.INVISIBLE }
        verify(exactly = 0) { filesCountView.visibility = View.INVISIBLE }
        verify(exactly = 0) { filesStatView.visibility = View.INVISIBLE }
        assertEquals(originalTime, selectionHeapUpContainer.startLoadingTime)  // 验证加载时间未改变
    }

    /**
     * 测试showAsLoading方法在不是加载状态时的行为
     * 1. 不应该设置任何视图可见性
     * 2. startLoadingTime应该保持为null
     */
    @Test
    fun `test showAsLoading when not loading should do nothing`() {
        // Given - 设置初始条件：加载视图当前不可见
        every { loadingView.visibility } returns View.GONE

        // When - 调用方法
        selectionHeapUpContainer.showAsLoading(false)

        // Then - 验证结果
        verify(exactly = 0) { loadingView.visibility = View.GONE }  // 验证没有设置可见性
        verify(exactly = 0) { heapUpView.visibility = View.VISIBLE }
        verify(exactly = 0) { filesCountView.visibility = View.VISIBLE }
        verify(exactly = 0) { filesStatView.visibility = View.VISIBLE }
        assertNull(selectionHeapUpContainer.startLoadingTime)  // 验证加载时间保持为null
    }
}