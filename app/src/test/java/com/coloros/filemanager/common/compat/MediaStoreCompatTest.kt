package com.coloros.filemanager.common.compat

import android.content.Context
import android.os.Build
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.compat.compat30.GalleryCompatR
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.utils.SdkUtils
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [Build.VERSION_CODES.S])
class MediaStoreCompatTest {
    var mContext: Context? = null

    @MockK
    lateinit var mediaStoreCompat: MediaStoreCompat

    @Before
    fun setUp() {
        mContext = RuntimeEnvironment.application
        mediaStoreCompat = mockk(relaxed = true)
    }

    @After
    fun tearDown() {
        mContext = null
    }

    @Test
    fun test_getMediaCountSqlQuery() {
        Assert.assertNotNull(MediaStoreCompat.getMediaCountSqlQuery(CategoryHelper.CATEGORY_IMAGE))
        Assert.assertNotNull(MediaStoreCompat.getMediaCountSqlQuery(CategoryHelper.CATEGORY_DOC))
        Assert.assertNotNull(MediaStoreCompat.getMediaCountSqlQuery(CategoryHelper.CATEGORY_COMPRESS))
        Assert.assertNotNull(MediaStoreCompat.getMediaCountSqlQuery(CategoryHelper.CATEGORY_APK))
        Assert.assertNotNull(MediaStoreCompat.getMediaCountSqlQuery(-1))
    }

    @Ignore
    @Test
    fun testGetMediaCountSqlQueryByCategoryDoc() {
        mockkStatic(SdkUtils::class)
        every { SdkUtils.isAtLeastR() } returns false
        Assert.assertEquals(
            "media_type=10003 AND (" +
                    "_display_name LIKE '%.txt' OR " +
                    "_display_name LIKE '%.doc' OR " +
                    "_display_name LIKE '%.docx' OR " +
                    "_display_name LIKE '%.xls' OR " +
                    "_display_name LIKE '%.xlsx' OR " +
                    "_display_name LIKE '%.ppt' OR " +
                    "_display_name LIKE '%.pptx' OR " +
                    "_display_name LIKE '%.pdf')",
            MediaStoreCompat.getMediaCountSqlQuery(CategoryHelper.CATEGORY_DOC)
        )
    }

    @Test
    fun test_getMediaStoreSqlQuery() {
        val fileList: ArrayList<String?> = ArrayList()
        fileList.add("test")
        Assert.assertNotNull(
            MediaStoreCompat.getMediaStoreSqlQuery(
                CategoryHelper.CATEGORY_DOC,
                fileList
            )
        )
        Assert.assertNotNull(
            MediaStoreCompat.getMediaStoreSqlQuery(
                CategoryHelper.CATEGORY_APK,
                fileList
            )
        )
        Assert.assertNotNull(
            MediaStoreCompat.getMediaStoreSqlQuery(
                CategoryHelper.CATEGORY_COMPRESS,
                fileList
            )
        )
        Assert.assertNotNull(MediaStoreCompat.getMediaStoreSqlQuery(-1, fileList))
    }

    @Test
    fun test_getMyAlbumPath() {
        Assert.assertNotNull(
            Whitebox.invokeMethod<String>(
                mediaStoreCompat,
                "getMyAlbumPath",
                mContext!!
            )
        )
    }

    @Test
    fun test_getCShotFiles() {
        val file = BaseFileBean()
        val fileList: ArrayList<BaseFileBean> = ArrayList()
        fileList.add(file)
        MediaStoreCompat.getCShotFiles(fileList)
        Assert.assertEquals(fileList.size, 1)
    }

    @Test
    fun `should return right string when call addFilterSize`() {
        //given
        val stringBuilder = StringBuilder()
        stringBuilder.append("test")
        //when
        MediaStoreCompat.addFilterSize(stringBuilder)
        //then
        val except = "test AND (_size > 0)"
        Assert.assertTrue(stringBuilder.toString() == except)
    }

    @Test
    fun `should return right string when call addFilterType`() {
        //given
        val stringBuilder = StringBuilder()
        stringBuilder.append("test")
        //when
        MediaStoreCompat.addFilterType(stringBuilder)
        //then
        val except = "test AND (_display_name not like '%.psd') " +
                "AND (_display_name not like '%.dwg') AND (_display_name not like '%.dxf')"
        Assert.assertTrue(stringBuilder.toString() == except)
    }

    @Test
    fun `should call GalleryCompatR getAlbumSet when call getAlbumSet if isAtLeastR`() {
        //given
        mockkStatic(SdkUtils::class)
        mockkObject(GalleryCompatR)
        every { SdkUtils.isAtLeastR() } returns true
        val mock = mockk<ArrayList<AlbumItem>>()
        every { GalleryCompatR.getAlbumSet() } returns mock
        //when
        val result = MediaStoreCompat.getAlbumSet()
        //then
        Assert.assertEquals(result, mock)
        unmockkAll()
    }
}