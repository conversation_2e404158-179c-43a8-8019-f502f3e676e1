/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : Util.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/5/11
 * * Author      : onAppSwitchConfigChanged
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.appswitch.util

import android.util.Log

object Util {

    const val TAG = "Util"


    /**
     * 检查输入fileName是否符合文件后缀名列表规范，
     * @param fileName 输入的文件名称
     * @param suffixList 文件后缀名列表
     * @param includeNoSuffix 表明文件名称没有后缀时，是否纳入符合范围
     * @ouput 是否符合
     */
    @JvmStatic
    fun checkFileNameSuffixMeet(
        fileName: String,
        suffixList: List<String>?,
        includeNoSuffix: Boolean
    ): Boolean {
        Log.i(TAG, "checkFileNameSuffixMeet fileName $fileName, suffix $suffixList, includeNoSuffix $includeNoSuffix")
        if (suffixList.isNullOrEmpty()) {
            Log.i(TAG, "checkFileNameSuffixMeet suffixList is empty, return true")
            return true
        }
        val delimiter = arrayOf(".")
        val splitName = fileName.split(delimiters = delimiter, true, limit = 0)
        val hasSuffix = splitName.size > 1
        var result = false
        result = if (hasSuffix) {
            val targetSuffix = suffixList.find { fileName.contains(it, true) }
            (targetSuffix != null)
        } else {
            includeNoSuffix
        }
        Log.i(TAG, "checkFileNameSuffixMeet splitName $splitName hasSuffix $hasSuffix, result: $result")
        return result
    }
}