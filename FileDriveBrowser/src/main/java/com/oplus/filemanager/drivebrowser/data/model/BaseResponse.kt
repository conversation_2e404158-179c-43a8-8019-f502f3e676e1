/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Response
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/12/5 19:34
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/12/5       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.data.model

import com.google.gson.annotations.SerializedName

data class BaseResponse<R>(
    @SerializedName("code") var code: Int = -1,
    @SerializedName("msg") var msg: String? = null,
    @SerializedName("data") var data: R? = null
) {
    fun isFailed(): Boolean {
        return code != 0
    }

    fun codeMsg(code: Int, msg: String?) {
        this.code = code
        this.msg = msg
    }
}