plugins {
    id 'com.android.library'
}
apply from: rootProject.file("scripts/appCompile.gradle")
apply from: rootProject.file("scripts/unitTest.gradle")
apply from: rootProject.file("scripts/androidTest.gradle")

android {
    namespace "com.oplus.cloudconfig"
    resourcePrefix "cloudconfig_"

    buildFeatures {
        buildConfig = true
    }

    defaultConfig {
        buildConfigField("boolean", "CLOUD_CONFIG_TEST_ENV", "${prop_cloudConfig_testEnv.toBoolean()}")
    }
}

dependencies {
    implementation libs.androidx.appcompat
    implementation libs.apache.commons.io
    implementation libs.google.gson
    implementation libs.google.material

    implementation libs.heytap.nearx.protobuff
    implementation libs.heytap.nearx.track
    implementation libs.heytap.nearx.utils
    // for CloudConfig and FeedBack Module
    implementation libs.bundles.oplus.cloudconfig
    implementation libs.koin.android

    implementation project(':Common')

    if (prop_cloudConfig_testEnv.toBoolean()) {
        implementation libs.oplus.cloudconfig.testenv
    }
}