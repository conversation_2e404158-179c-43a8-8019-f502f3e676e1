package shadow;

import android.content.Context;
import android.text.TextUtils;

import com.filemanager.common.helper.VolumeEnvironment;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(VolumeEnvironment.class)
public class ShadowVolumeEnvironment {
    private static boolean mExternalSdMounted;

    private static String mExternalSdPath = "/sdcard/";

    private static String mInternalSdPath;

    private static boolean mInternalSdMounted;

    private static boolean mSingleSdcard;

    private static String mDataDirPath;

    private static boolean sIsSdcardPath;
    @Implementation
    public static boolean isExternalSdMounted(Context context) {
        return mExternalSdMounted;
    }

    @Implementation
    public static boolean isInternalSdMounted(Context context) {
        return mInternalSdMounted;
    }

    @Implementation
    public static String getExternalSdPath(Context context) {
        return mExternalSdPath;
    }

    @Implementation
    public static String getInternalSdPath(Context context) {
        return mInternalSdPath;
    }

    @Implementation
    public static boolean isSingleSdcard(Context context) {
        return mSingleSdcard;
    }

    @Implementation
    public static String getDataDirPath(Context context, String... paths) {
        return mDataDirPath;
    }

    @Implementation
    public static boolean isSdcardPath(Context context, String path) {
        return sIsSdcardPath;
    }

    @Implementation
    public static boolean isMountedByPath(Context context, String path) {
        if ((context == null) || (TextUtils.isEmpty(path))) {
            return false;
        }
        return true;
    }

    public static void setmIsExternalSdMounted(boolean isExternalSdMounted) {
        mExternalSdMounted = isExternalSdMounted;
    }

    public static void setmInternalSdPath(String internalSdPath) {
        mInternalSdPath = internalSdPath;
    }

    public static void setmExternalSdPath(String externalSdPath) {
        mExternalSdPath = externalSdPath;
    }

    public static void setmInternalSdMounted(boolean internalSdMounted) {
        mInternalSdMounted = internalSdMounted;
    }

    public static void setmSingleSdcard(boolean singleSdcard) {
        mSingleSdcard = singleSdcard;
    }

    public static void setmDataDirPath(String dataDirPath) {
        mDataDirPath = dataDirPath;
    }

    public static void setsIsSdcardPath(boolean isSdcardPath) {
        sIsSdcardPath = isSdcardPath;
    }

}
