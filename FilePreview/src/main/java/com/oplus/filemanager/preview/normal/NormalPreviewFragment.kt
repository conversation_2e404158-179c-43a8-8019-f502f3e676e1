/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - NormalPreviewFragment.kt
 * Description:
 *     The default file preview fragment for all file types.
 *
 * Version: 1.0
 * Date: 2024-10-17
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-10-17   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.preview.normal

import android.content.Context
import android.view.View
import androidx.fragment.app.Fragment
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.oplus.filemanager.preview.R
import com.oplus.filemanager.preview.core.AbsPreviewFragment
import com.oplus.filemanager.preview.core.FilePreviewViewModel
import com.oplus.filemanager.preview.utils.PreviewScrollAreaHelper
import com.oplus.filemanager.preview.widget.PreviewFileInfoSuite
import com.oplus.filemanager.preview.widget.PreviewFilePathItem

internal class NormalPreviewFragment :
    AbsPreviewFragment<FilePreviewViewModel>(R.layout.fragment_preview_normal) {

    private companion object {
        private const val TAG = "NormalPreviewFragment"
    }

    override val logTag: String = TAG
    override val fragmentInstance: Fragment = this
    override val viewModelClass: Class<FilePreviewViewModel> = FilePreviewViewModel::class.java
    override val operationsBarId: Int = R.id.preview_operations_bar

    private var filePreview: INormalFilePreview? = null
    private var scrollAreaHelper: PreviewScrollAreaHelper? = null

    override fun isPreviewFileApproved(context: Context, fileBean: BaseFileBean): Boolean = true

    override fun onViewModelCreated(view: View, viewModel: FilePreviewViewModel) {
        filePreview = prepareFilePreview(view, viewModel)
    }

    override fun onCreateFilePathItem(view: View): PreviewFilePathItem {
        val item: PreviewFilePathItem = view.findViewById(R.id.preview_remote_location_info)
        item.setLabelGone()
        return item
    }

    override fun onDestroyView() {
        super.onDestroyView()
        filePreview?.release()
        filePreview = null
    }

    private fun prepareFilePreview(
        view: View,
        viewModel: FilePreviewViewModel
    ): INormalFilePreview {
        val container = view.findViewById<PreviewFileInfoSuite>(R.id.preview_file_info)
        val previewImpl = INormalFilePreview.obtain(viewLifecycleOwner, viewModel)
        previewImpl.attachToContainer(container)
        previewImpl.setFileInfoStruct(view)
        scrollAreaHelper = PreviewScrollAreaHelper(
            scrollView = view.findViewById(R.id.preview_audio_scroll_area),
            operationsBar = view.findViewById(R.id.preview_operations_bar)
        )
        scrollAreaHelper?.enableScrollViewportMinHeight(
            container.resources
                .getDimensionPixelOffset(R.dimen.preview_remote_scroll_layout_min_height)
        )
        scrollAreaHelper?.checkShowDivider()
        return previewImpl
    }

    override fun onUpdateUIWhenConfigChange(configList: MutableCollection<IUIConfig>) {
        scrollAreaHelper?.checkShowDivider()
    }
}