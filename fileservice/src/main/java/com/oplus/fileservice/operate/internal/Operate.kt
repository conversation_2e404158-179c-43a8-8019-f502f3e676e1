/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Operate
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/6/29 16:12
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/6/29       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal

import android.content.Context
import com.oplus.fileservice.operate.convert.OperateRequest
import com.oplus.fileservice.operate.convert.OperationResult
import com.oplus.fileservice.operate.internal.copy.CopyFileOperate
import com.oplus.fileservice.operate.internal.create.CreateDirOperate
import com.oplus.fileservice.operate.internal.cut.CutFileOperate
import com.oplus.fileservice.operate.internal.delete.DeleteFileOperate
import com.oplus.fileservice.operate.internal.rename.RenameFileOperate
import com.oplus.fileservice.operate.internal.restore.RestoreFileOperate

const val OPERATE_RENAME = 1
const val OPERATE_CREATE_DIR = 2
const val OPERATE_COPY = 3
const val OPERATE_CUT = 4
const val OPERATE_DELETE = 5
const val OPERATE_RESTORE = 6

interface Operation {
    fun perform(operateRequest: OperateRequest): OperationResult

    fun cancel(immediately: Boolean = false)
}

object DoNothing : Operation {
    override fun perform(operateRequest: OperateRequest): OperationResult {
        return OperationResult(false)
    }

    override fun cancel(immediately: Boolean) {
    }
}

fun convertRequestToOperation(
    context: Context,
    operateType: Int
): Operation {
    return when (operateType) {
        OPERATE_RENAME -> RenameFileOperate(context)
        OPERATE_CREATE_DIR -> CreateDirOperate(context)
        OPERATE_COPY -> CopyFileOperate(context)
        OPERATE_CUT -> CutFileOperate(context)
        OPERATE_DELETE -> DeleteFileOperate(context)
        OPERATE_RESTORE -> RestoreFileOperate(context)
        else -> DoNothing
    }
}