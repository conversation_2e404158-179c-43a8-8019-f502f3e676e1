<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/appBarLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/coui_color_background_with_card"
    android:orientation="vertical"
    android:layout_marginTop="@dimen/coui_appbar_margin_top"
    app:layout_behavior="com.oplus.filemanager.main.behavior.PrimaryTitleBehavior"
    android:clickable="true"
    android:importantForAccessibility="no"
    app:elevation="@dimen/dimen_0dp">

    <TextView
        android:id="@+id/main_title"
        style="@style/TitleStyle"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/toolbar_title_init_height"
        android:layout_below="@+id/toolbar"
        android:layout_marginHorizontal="@dimen/dimen_16dp"
        android:layout_marginBottom="@dimen/dimen_12dp"
        android:gravity="center_vertical"
        android:textColor="@color/coui_color_label_primary"
        android:textFontWeight="600"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_default="wrap"
        android:focusable="true"/>

    <com.coui.appcompat.toolbar.COUIToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:focusable="true"
        android:minHeight="@dimen/toolbar_min_height"
        app:supportTitle=" " />

    <com.filemanager.common.sort.SortEntryView
        android:layout_below="@id/main_title"
        android:id="@+id/sort_entry_view"
        android:focusable="true"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sort_entry_height"
        android:visibility="gone" />

    <View
        android:layout_below="@+id/sort_entry_view"
        android:id="@+id/tab_divider_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/divider_background_height"
        android:layout_gravity="center_horizontal"
        android:layout_marginLeft="@dimen/common_margin"
        android:layout_marginRight="@dimen/common_margin"
        android:alpha="0"
        android:background="?attr/couiColorDivider"
        android:forceDarkAllowed="false"
        android:visibility="visible" />
</RelativeLayout>