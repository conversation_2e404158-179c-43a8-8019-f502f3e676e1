/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * File:  - AutoCleanRecycleOperation.java
 * Description: Operation to clean file or folder in Recyclebin or wild file or folder
 * Version: 1.0
 * Date : 2020/03/08
 * Author: Jiafei.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/03/08    1.0     create
 ****************************************************************/
package com.filemanager.recyclebin.operation

import android.app.Activity
import android.database.Cursor
import android.net.Uri
import androidx.activity.ComponentActivity
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.wrapper.RecycleBinLiveData.RECYCLE_AUTO_CLEAN
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.RecycleStore
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.recyclebin.operation.action.DoubleDeleteAction
import com.filemanager.recyclebin.retriever.DataRetriever
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.fileservice.IFileService.Companion.OPERATE_TYPE_DELETE_IN_RECYCLE_BIN
import com.oplus.filemanager.interfaze.main.IMain
import java.io.File
import kotlin.collections.ArrayList

class AutoCleanOperation private constructor(activity: ComponentActivity) :
        BaseOperation<Activity>(activity, null) {
    companion object {
        private const val TAG = "AutoCleanOperation"
        private const val TIME_TO_DELETE = 30 * 24 * 3600000L
        private val DELETE_PROJECTION = arrayOf(RecycleStore.Files.FileColumns.RECYCLE_ID,
            RecycleStore.Files.FileColumns.RECYCLE_PATH,
            RecycleStore.Files.FileColumns.RECYCLE_DATE,
            RecycleStore.Files.FileColumns.ORIGIN_PATH)
        private const val RECYCLE_ID_INDEX = 0
        private const val RECYCLE_PATH_INDEX = 1
        private const val RECYCLE_DATE_INDEX = 2
        private const val ORIGIN_PATH_INDEX = 3

        @JvmStatic
        fun doAutoClean(activity: ComponentActivity) {
            if (Utils.isOperating()) {
                return
            }
            val baseOperation = AutoCleanOperation(activity)
            baseOperation.execute()
        }

        private fun statisticsAutoCleanCount(cleanCount: Long) {
            if (cleanCount <= 0) {
                return
            }

            StatisticsUtils.nearMeStatisticsRecycleBinDeleteCount(
                appContext,
                    RECYCLE_AUTO_CLEAN, cleanCount)
        }
    }

    init {
        activity.runOnUiThread {
            activity.lifecycle.addObserver(this)
        }
    }

    override fun addFileBeans(files: ArrayList<BaseFileBean>?): Boolean {
        throw UnsupportedOperationException()
    }

    override fun addPaths(paths: ArrayList<String>?): Boolean {
        throw UnsupportedOperationException()
    }

    override fun addUris(uris: ArrayList<Uri>?): Boolean {
        throw UnsupportedOperationException()
    }

    override fun onTotalCountCallback(): Int {
        return 0
    }

    override fun onParametersCallback(): OperationParameters? {
        return null
    }

    override fun onPreExecute() {
        // forbid Utils.setOperatingFlag(true) be called in parent class
    }

    override fun onPostExecute(result: OperationResult) {
        // forbid Utils.setOperatingFlag(false) be called in parent class
        onDestroy()
    }

    override fun onDestroy() {
        mTarget.get()?.let {
            it.runOnUiThread {
                (it as? ComponentActivity)?.lifecycle?.removeObserver(this)
            }
        }
        super.onDestroy()
    }

    override fun doInBackground(vararg voids: Void): OperationResult {
        autoClean()
        return OperationResult(0, 0, STATUS_SUCCESS)
    }

    private fun autoClean() {
        try {
            val resolver = appContext.contentResolver
            resolver.query(RecycleStore.Files.INTERNAL_CONTENT_ROOT_URI,
                    DELETE_PROJECTION,
                    null,
                    null,
                    RecycleStore.Files.FileColumns.RECYCLE_DATE + " DESC ")?.use { cursor ->
                autoClean(cursor)
            }
        } catch (ex: Exception) {
            Log.w(TAG, "autoClean error: ${ex.message}")
        }
    }

    @Suppress("LongMethod")
    private fun autoClean(cursor: Cursor) {
        val store = DataRetriever.INSTANCE
        var recyclePath: String?
        val paths = getRecycleFileList()
        val deleteAction = DoubleDeleteAction(mColumnName1 = RecycleStore.Files.FileColumns.RECYCLE_ID,
                mColumnName2 = RecycleStore.Files.FileColumns.RECYCLE_PATH)
        val allFilePathForLabels = ArrayList<String>()
        var recycleId: String?
        var originPath: String?
        var recycleDate: Long
        var expireCount = 0L
        val now = System.currentTimeMillis()
        val startTime: Long = now - TIME_TO_DELETE
        while (cursor.moveToNext()) {
            recycleId = cursor.getString(RECYCLE_ID_INDEX)
            recyclePath = cursor.getString(RECYCLE_PATH_INDEX)
            recycleDate = cursor.getLong(RECYCLE_DATE_INDEX)
            originPath = cursor.getString(ORIGIN_PATH_INDEX)
            if (Utils.isOperating()) {
                Log.w(TAG, "autoclean doing something1 in recyclebin")
                break
            }

            if (recyclePath.isNullOrEmpty()) {
                // delete item in recycle provider
                deleteAction.add(recycleId, null)
                continue
            }

            // remove file in recyclebin from paths list to delete wild file
            val file = File(recyclePath)
            if (file.exists()) {
                paths.remove(recyclePath)
            }
            // clean Expired item
            if (recycleDate < startTime) {
                if (!store.deleteRecycle(recyclePath)) {
                    Log.d(TAG, "autoclean  recyclePath: $recyclePath")
                    continue
                }

                if (file.isDirectory) {
                    // delete child directory file and folder
                    val path = recyclePath + File.separator + "/%"
                    deleteAction.add(recycleId, path)
                } else {
                    // delete child directory file
                    deleteAction.add(recycleId, null)
                }
                allFilePathForLabels.add(originPath)
                expireCount++
                statisticsDurationDate(RECYCLE_AUTO_CLEAN, recycleDate)
            }
        }
        syncOperate(expireCount)
        statisticsAutoCleanCount(expireCount)
        deleteAction.flush()

        val mainAction = Injector.injectFactory<IMain>()
        mainAction?.onDeleteFilePaths(allFilePathForLabels)

        for (path in paths) {
            if (Utils.isOperating()) {
                Log.w(TAG, "autoclean doing something2 in recyclebin")
                break
            }
            store.deleteRecycle(path)
            Log.d(TAG, "autoclean wild file recyclePath: $path")
        }
    }

    private fun syncOperate(expireCount: Long) {
        if (expireCount > 0) {
            val fileServiceAction = Injector.injectFactory<IFileService>()
            fileServiceAction?.syncOperate(OPERATE_TYPE_DELETE_IN_RECYCLE_BIN, hashSetOf())
        }
    }

    private fun getRecycleFileList(): ArrayList<String> {
        val paths = ArrayList<String>()
        try {
            // get All file and folder to clean,include wild file and folder in new recyclebin root path and legacy path
            val rootPaths = RecycleStore.getAllRecycleDirectorys()
            for (rootPath in rootPaths) {
                val targetFile = PathFileWrapper(rootPath)
                val files = JavaFileHelper.listFileBeans(targetFile, false) ?: continue
                for (file in files.iterator()) {
                    if (file.mData.isNullOrEmpty()) {
                        continue
                    }
                    paths.add(file.mData!!)
                }
            }
        } catch (ex: Exception) {
            Log.w(TAG, "getRecycleFileList error: $ex")
        }
        return paths
    }
}