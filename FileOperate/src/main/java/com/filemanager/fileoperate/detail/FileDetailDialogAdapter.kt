/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File:FileDetailDialogAdapter.kt
 * * Description:
 * * Version:1.0
 * * Date :2020.1.9
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.detail

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.chip.COUIChip
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.Constants.LABEL_FILE_LIST_NAME
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.Utils.RTL_POSITION_DOUBLE
import com.filemanager.common.utils.noMoreAction
import com.filemanager.fileoperate.R
import com.filemanager.fileoperate.detail.FileDetailObserver.FileDetailBean.CREATOR.TYPE_DIRECTORY
import com.filemanager.fileoperate.detail.FileDetailObserver.FileDetailBean.CREATOR.TYPE_FILE
import com.filemanager.fileoperate.detail.FileDetailObserver.FileDetailBean.CREATOR.TYPE_MULTI_FILES
import com.google.android.material.chip.ChipGroup
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.main.IMain
import java.io.File

class FileDetailDialogAdapter(
    private val mContext: Context,
    private var mDialog: Dialog? = null,
    private val categoryType: Int? = null
) : BaseAdapter() {

    companion object {
        private const val TAG = "FileDetailDialogAdapter"
        const val OTG_LIST_PATH = "OTG_LIST_PATH"
        private const val OTG_ACTIVITY_NAME = "com.oplus.filebrowser.otg.OtgFileBrowserActivity"
        private const val FILE_BROWSER_ACTION = "oplus.intent.action.filemanager.BROWSER_FILE"
        private const val FILE_BROWSER_ACTIVITY_NAME = "com.oplus.filebrowser.FileBrowserActivity"
        private const val FILE_ALBUM_ACTIVITY_NAME = "com.oplus.filemanager.category.album.ui.AlbumActivity"
        private const val FILE_AUDIO_VIDEO_ACTIVITY_NAME = "com.oplus.filemanager.category.audiovideo.ui.CategoryAudioActivity"
        private const val FILE_DOCUMENT_ACTIVITY_NAME = "com.oplus.filemanager.category.document.ui.DocumentActivity"
        private const val FILE_APK_ACTIVITY_NAME = "com.oplus.filemanager.category.apk.ui.ApkActivity"
        private const val FILE_COMPRESS_ACTIVITY_NAME = "com.filemanager.categorycompress.ui.CategoryCompressActivity"
        private const val FILE_SUPERAPP_ACTIVITY_NAME = "com.filemanager.superapp.ui.superapp.SuperAppActivity"
        private const val FILE_FAVORITE_ACTIVITY_NAME = "com.oplus.filemanager.favorite.ui.CategoryFavoriteActivity"
        private const val LABEL_FILE_LIST_ACTIVITY_NAME = "com.oplus.filemanager.filelabel.list.LabelFileListActivity"
    }

    @VisibleForTesting
    var mNameRes: IntArray? = null
         get() = field.noMoreAction()
    private var mDetailBean: FileDetailObserver.FileDetailBean? = null

    private val mainAction: IMain? by lazy { Injector.injectFactory<IMain>() }

    @SuppressLint("NotifyDataSetChanged", "LongMethod")
    fun updateDetail(detailBean: FileDetailObserver.FileDetailBean, dialog: Dialog? = null) {
        mDetailBean = detailBean
        this.mDialog = dialog
        mNameRes = when (detailBean.type) {
            TYPE_FILE -> createFileItemNames(detailBean)
            TYPE_DIRECTORY -> createDirItemNames(detailBean)

            TYPE_MULTI_FILES -> {
                intArrayOf(
                    com.filemanager.common.R.string.dialog_status_size_new,
                    com.filemanager.common.R.string.dialog_status_contains_new
                )
            }
            else -> {
                null
            }
        }
        notifyDataSetChanged()
    }

    private fun createFileItemNames(detailBean: FileDetailObserver.FileDetailBean): IntArray {
        return if (detailBean.mIsRecycleBin) {
            if (detailBean.isHasLabels()) {
                intArrayOf(
                    com.filemanager.common.R.string.dialog_status_name_new,
                    com.filemanager.common.R.string.label,
                    com.filemanager.common.R.string.text_recycle_detail_delete_date_new,
                    com.filemanager.common.R.string.dialog_status_size_new,
                    com.filemanager.common.R.string.text_recycle_detail_origin_path_new
                )
            } else {
                intArrayOf(
                    com.filemanager.common.R.string.dialog_status_name_new,
                    com.filemanager.common.R.string.text_recycle_detail_delete_date_new,
                    com.filemanager.common.R.string.dialog_status_size_new,
                    com.filemanager.common.R.string.text_recycle_detail_origin_path_new
                )
            }
        } else {
            if (detailBean.isHasLabels()) {
                intArrayOf(
                    com.filemanager.common.R.string.dialog_status_name_new,
                    com.filemanager.common.R.string.label,
                    com.filemanager.common.R.string.modify_time,
                    com.filemanager.common.R.string.dialog_status_size_new,
                    com.filemanager.common.R.string.dialog_status_file_position_new
                )
            } else {
                if (detailBean.mDateModified == 0L) {
                    intArrayOf(
                        com.filemanager.common.R.string.dialog_status_name_new,
                        com.filemanager.common.R.string.dialog_status_size_new,
                        com.filemanager.common.R.string.dialog_status_file_position_new
                    )
                } else {
                    intArrayOf(
                        com.filemanager.common.R.string.dialog_status_name_new,
                        com.filemanager.common.R.string.modify_time,
                        com.filemanager.common.R.string.dialog_status_size_new,
                        com.filemanager.common.R.string.dialog_status_file_position_new
                    )
                }
            }
        }
    }

    private fun createDirItemNames(detailBean: FileDetailObserver.FileDetailBean): IntArray {
        return if (detailBean.mIsRecycleBin) {
            if (detailBean.isHasLabels()) {
                intArrayOf(
                    com.filemanager.common.R.string.dialog_status_name_new,
                    com.filemanager.common.R.string.label,
                    com.filemanager.common.R.string.text_recycle_detail_delete_date_new,
                    com.filemanager.common.R.string.dialog_status_size_new,
                    com.filemanager.common.R.string.dialog_status_contains_new
                )
            } else {
                intArrayOf(
                    com.filemanager.common.R.string.dialog_status_name_new,
                    com.filemanager.common.R.string.text_recycle_detail_delete_date_new,
                    com.filemanager.common.R.string.dialog_status_size_new,
                    com.filemanager.common.R.string.dialog_status_contains_new
                )
            }
        } else {
            if (detailBean.isHasLabels()) {
                intArrayOf(
                    com.filemanager.common.R.string.dialog_status_name_new,
                    com.filemanager.common.R.string.label,
                    com.filemanager.common.R.string.modify_time,
                    com.filemanager.common.R.string.dialog_status_size_new,
                    com.filemanager.common.R.string.dialog_status_contains_new
                )
            } else {
                if (detailBean.mDateModified == 0L) {
                    intArrayOf(
                        com.filemanager.common.R.string.dialog_status_name_new,
                        com.filemanager.common.R.string.dialog_status_size_new,
                        com.filemanager.common.R.string.dialog_status_contains_new
                    )
                } else {
                    intArrayOf(
                        com.filemanager.common.R.string.dialog_status_name_new,
                        com.filemanager.common.R.string.modify_time,
                        com.filemanager.common.R.string.dialog_status_size_new,
                        com.filemanager.common.R.string.dialog_status_contains_new
                    )
                }
            }
        }
    }

     fun onCreateViewHolder(viewGroup: ViewGroup, itemType: Int): ViewHolder {
         val view = LayoutInflater.from(mContext).inflate(R.layout.fop_detail_recycler_item, viewGroup, false)
        return ViewHolder(view)
    }

     fun onBindViewHolder(holder: ViewHolder, position: Int) {
        if (position in 0 until count) {
            mNameRes?.let {
                holder.mNameTv.setText(it[position])
                when (it[position]) {
                    com.filemanager.common.R.string.dialog_status_name_new -> showName(holder.mValueTv)
                    com.filemanager.common.R.string.modify_time -> showLastModified(holder.mValueTv)
                    com.filemanager.common.R.string.text_recycle_detail_delete_date_new -> showRecycleDate(holder.mValueTv)
                    com.filemanager.common.R.string.dialog_status_size_new -> showSize(holder.mValueTv)
                    com.filemanager.common.R.string.dialog_status_file_position_new -> showPath(holder.mValueTv)
                    com.filemanager.common.R.string.text_recycle_detail_origin_path_new -> showRecycleOriginPath(holder.mValueTv)
                    com.filemanager.common.R.string.dialog_status_contains_new -> showContains(holder.mValueTv)
                    com.filemanager.common.R.string.label -> showLabelsInfo(holder.mValueTv, holder.mLabelChipGroup)
                    else -> {
                    }
                }
            }
        }
    }

    private fun showLabelsInfo(valueTv: TextView, mLabelChipGroup: ChipGroup) {
        valueTv.visibility = View.GONE
        mLabelChipGroup.visibility = View.VISIBLE
        mLabelChipGroup.removeAllViews()
        mDetailBean?.mLabelIds?.forEachIndexed { index, id ->
            val chip: COUIChip = LayoutInflater.from(mContext)
                .inflate(com.filemanager.common.R.layout.item_label_file_deatil, mLabelChipGroup, false) as COUIChip
            COUIChangeTextUtil.adaptFontSize(chip, COUIChangeTextUtil.G4)
            val name = mDetailBean?.mLabelNames?.get(index) ?: ""
            chip.text = name
            mLabelChipGroup.addView(chip)
            chip.isChecked = mDetailBean?.mIsRecycleBin == false
            chip.isEnabled = mDetailBean?.mIsRecycleBin == false
            handleChipOnClick(chip, id, name)
        }
    }

    private fun handleChipOnClick(chip: COUIChip, id: Long, name: String) {
        chip.setOnClickListener {
            if (mContext is Activity) {
                val componentName = mContext.componentName.className
                val currentLabel = PreferencesUtils.getString(key = LABEL_FILE_LIST_NAME)
                if (componentName == LABEL_FILE_LIST_ACTIVITY_NAME && currentLabel == name) {
                    mDialog?.dismiss()
                    mContext.onBackPressed()
                } else {
                    //wwh找到对应displayName的sideCategoryType
                    mainAction?.startSubLabelListActivity(mContext, id, name, sideCategoryType = CategoryHelper.CATEGORY_LABEL_GROUP)
                }
            }
        }
    }

    private fun showName(valueTv: TextView) {
        mDetailBean?.mDisplayName?.let {
            valueTv.text = it
        }
    }

    private fun showLastModified(valueTv: TextView) {
        mDetailBean?.mDateModified?.let {
            val dateAndTime = Utils.getDateAndTimeFormatLocal(mContext, it)
            valueTv.text = dateAndTime
        }
    }

    private fun showRecycleDate(valueTv: TextView) {
        mDetailBean?.mRecycelDate?.let {
            val dateAndTime = Utils.getDateAndTimeFormatLocal(mContext, it)
            valueTv.text = dateAndTime
        }
    }

    private fun showSize(valueTv: TextView) {
        mDetailBean?.let {
            if (it.mCalculating) {
                valueTv.text = mContext.getString(com.filemanager.common.R.string.string_being_calculated)
            } else {
                valueTv.text = Utils.byteCountToDisplaySize(it.mTotalFileSize)
            }
        }
    }

    private fun showPath(valueTv: TextView) {
        mDetailBean?.mPath?.let { myPath ->
            val myDir = myPath.substring(0, myPath.lastIndexOf(File.separator))
            val isDfmPath = KtUtils.checkIsDfmPath(myDir)
            val path = Utils.formatPathWithRTL(Utils.getVirtualPathString(mContext, myDir))
            valueTv.text = path
            if (isDfmPath) {
                Log.d(TAG, "is dfm path, return")
                return
            }
            valueTv.setTextColor(COUIContextUtil.getAttrColor(mContext, com.support.appcompat.R.attr.couiColorContainerTheme))
            valueTv.setOnClickListener {
                if (mContext is Activity) {
                    onClickFilePath(mContext, myPath)
                }
            }
        }
    }

    private fun onClickFilePath(activity: Activity, myPath: String) {
        val name = activity.componentName.className
        Log.d(TAG, "onClickFilePath: act=$name")
        val fileBrowser = Injector.injectFactory<IFileBrowser>()
        val isFromShortcutFolder = fileBrowser?.isFromShortcutFolder(activity) ?: false
        if (isFromShortcutFolder) {
            mDialog?.dismiss()
            Log.d(TAG, "isFromShortcutFolder")
            fileBrowser?.exitShortcutFolder(activity)
            return
        }
        val main = mainAction
        if (main?.run { isParentChildActivity(activity) && !isRecentFragment(activity) } == true) {
            mDialog?.dismiss()
            if (categoryType != CategoryHelper.CATEGORY_FILE_PREVIEW) {
                activity.onBackPressed()
            }
            if (main.isStorageFragment(activity)) {
                Log.d(TAG, "isStorageFragment")
                return
            }
            Log.d(TAG, "onClickFilePath: toFileBrowserActivity")
            fileBrowser?.toFileBrowserActivity(
                activity,
                myPath.substring(0, myPath.lastIndexOf(File.separator)),
                true
            )
        } else {
            mDialog?.dismiss()
            if (categoryType != CategoryHelper.CATEGORY_FILE_PREVIEW) {
                activity.onBackPressed()
            }
            if (name == FILE_BROWSER_ACTIVITY_NAME || name == OTG_ACTIVITY_NAME) {
                return
            }
            Log.d(TAG, "onClickFilePath: enterFileBrowserActivity")
            enterFileBrowserActivity(myPath)
        }
    }

    private fun enterFileBrowserActivity(myPath: String) {
        val intent = Intent()
        val isOTGPath = VolumeEnvironment.isOTGPath(mContext, myPath)
        val isSdcardPath = VolumeEnvironment.isSdcardPath(mContext, myPath)
        if (isOTGPath || isSdcardPath) {
            val otgList: ArrayList<String> = ArrayList()
            intent.setClassName(mContext.packageName, OTG_ACTIVITY_NAME)
            otgList.add(myPath.substring(0, myPath.lastIndexOf(File.separator)))
            intent.putStringArrayListExtra(OTG_LIST_PATH, otgList)
            intent.putExtra(Constants.TITLE_RES_ID, com.filemanager.common.R.string.storage_otg)
            intent.putExtra(Constants.TITLE, mContext.getString(com.filemanager.common.R.string.storage_otg))
        } else {
            intent.action = FILE_BROWSER_ACTION
            intent.putExtra(KtConstants.P_CURRENT_PATH, myPath.substring(0, myPath.lastIndexOf(File.separator)))
        }
        intent.putExtra(KtConstants.FROM_DETAIL, true)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        mContext.startActivity(intent)
    }

    @VisibleForTesting
    fun isNotFromFileBrowserDir(name: String?): Boolean {
        return name == FILE_ALBUM_ACTIVITY_NAME || name == FILE_AUDIO_VIDEO_ACTIVITY_NAME || name == FILE_DOCUMENT_ACTIVITY_NAME
                || name == FILE_APK_ACTIVITY_NAME || name == FILE_COMPRESS_ACTIVITY_NAME || name == FILE_SUPERAPP_ACTIVITY_NAME
                || name == FILE_FAVORITE_ACTIVITY_NAME
    }


    private fun showRecycleOriginPath(valueTv: TextView) {
        mDetailBean?.mOriginPath?.let {
            val path = Utils.formatPathWithRTL(Utils.getVirtualPathString(mContext, it))
            valueTv.text = path
        }
    }

    private fun showContains(valueTv: TextView) {
        mDetailBean?.let {
            if (it.mCalculating) {
                valueTv.text = mContext.getString(com.filemanager.common.R.string.string_being_calculated)
            } else {
                valueTv.text = Utils.formatMessage(
                    mContext.resources.getQuantityString(
                        com.filemanager.common.R.plurals.detail_dialog_status_folders,
                        it.mDirCount, it.mDirCount
                    ), RTL_POSITION_DOUBLE
                )
                    .plus(
                        Utils.formatMessage(
                            mContext.resources.getQuantityString(
                                com.filemanager.common.R.plurals.detail_dialog_status_files,
                                it.mFileCount, it.mFileCount
                            ), RTL_POSITION_DOUBLE
                        )
                    )
            }
        }
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        var mNameTv: TextView = view.findViewById<View>(R.id.name_tv) as TextView
        var mValueTv: TextView = view.findViewById<View>(R.id.value_tv) as TextView
        var mLabelChipGroup: ChipGroup = view.findViewById<View>(R.id.detail_label_group) as ChipGroup
    }

    override fun getCount(): Int {
        return mNameRes?.size ?: 0
    }

    override fun getItem(position: Int): Int {
        return mNameRes?.get(position) ?: 0
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val viewHolder: ViewHolder = onCreateViewHolder(parent, 0)
        onBindViewHolder(viewHolder, position)
        return viewHolder.itemView
    }
}