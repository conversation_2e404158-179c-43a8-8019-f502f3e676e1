/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemoteDeviceDownloadVM
 * * Description : 远程设备下载文件 ViewModel
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.download

import android.app.PendingIntent
import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.coui.appcompat.progressbar.COUILoadProgress
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.constants.CommonConstants.DEFAULT_DOWNLOAD_PATH
import com.filemanager.common.constants.CommonConstants.ROOT_PATH
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.fileoperate.open.FileActionOpen
import com.filemanager.fileoperate.open.FileOpenObserver
import com.oplus.filemanager.category.remotedevice.download.bean.DownloadRemoteFileBean
import com.oplus.filemanager.category.remotedevice.download.notification.DownloadNotifyManager
import com.oplus.filemanager.category.remotedevice.download.service.RemoteFileDownloadDispatcher
import com.oplus.filemanager.category.remotedevice.operate.RemoteFileDownloadAction
import com.oplus.filemanager.category.remotedevice.operate.RemoteFileDownloadObserver
import com.oplus.filemanager.interfaze.remotedevice.DownloadRemoteFileCallback
import com.oplus.filemanager.interfaze.remotedevice.OperateCallback
import com.oplus.filemanager.remotedevice.ResultConstant
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

class DownloadViewModel : BaseViewModel() {
    companion object {
        private const val TAG = "DownloadViewModel"

        /**
         * 下载状态
         */
        const val DOWNLOAD_DEFAULT = COUILoadProgress.DEFAULT_UP_OR_DOWN
        const val DOWNLOADING = COUILoadProgress.UPING_OR_DOWNING
        const val DOWNLOAD_WAIT = COUILoadProgress.UP_OR_DOWN_WAIT
        const val DOWNLOAD_FAIL = COUILoadProgress.UP_OR_DOWN_FAIL

        /**
         * 重置下载路径
         */
        fun resetDownloadPath() {
            saveDownloadPath(CommonConstants.DEFAULT_DOWNLOAD_PATH)
        }

        /**
         * 保存下载的路径
         */
        fun saveDownloadPath(path: String) {
            PreferencesUtils.put(key = CommonConstants.DOWNLOAD_REMOTE_PC_SAVE_PATH, value = path)
        }
    }

    /**
     * 上一次保存的存储路径
     */
    private val lastDownloadPath by lazy {
        PreferencesUtils.getString(key = CommonConstants.DOWNLOAD_REMOTE_PC_SAVE_PATH, default = DEFAULT_DOWNLOAD_PATH) ?: DEFAULT_DOWNLOAD_PATH
    }


    /**
     * 下载文件要保存的路径
     */
    var savePath: MutableLiveData<String> = MutableLiveData(lastDownloadPath)

    /**
     * 总体的下载状态
     * first: 下载状态
     * second: 下载进度，
     */
    val totalDownloadProgress: MutableLiveData<Pair<Int, Int>> = MutableLiveData(Pair(DOWNLOAD_DEFAULT, 0))

    /**
     * 下载完成的文件
     */
    val downloadCompleteFile: MutableLiveData<String> = MutableLiveData()

    private var monitor: Boolean = false

    private val operateCallback = object : DownloadRemoteFileCallback {

        override fun onProgressEachFile(path: String?, status: Int, progress: Float) {
            if (!monitor) {
                return
            }
            if (DownloadRemoteFileCallback.isCompleted(status)) {
                downloadCompleteFile.postValue(path ?: "")
            }
        }

        override fun onProgress(ratio: Int) {
            if (!monitor) {
                return
            }
            totalDownloadProgress.postValue(Pair(DOWNLOADING, ratio))
        }

        override fun onResult(operateCode: Int, resultCode: Int) {
            Log.d(TAG, "onResult $operateCode -> $resultCode")
            if (operateCode == OperateCallback.OPERATE_BEGIN_DOWNLOAD) {
                Log.d(TAG, "start download result：$resultCode")
                if (ResultConstant.isFail(resultCode) || ResultConstant.RESULT_CODE_CANCEL == resultCode) { //下载失败或者取消
                    if (ResultConstant.RESULT_CODE_TOO_MANY_FILES == resultCode) { // 文件总路径超长
                        viewModelScope.launch(Dispatchers.Main) {
                            CustomToast.showShort(com.filemanager.common.R.string.not_support_download_too_many_file)
                        }
                    }
                    totalDownloadProgress.postValue(Pair(DOWNLOAD_FAIL, 1))
                } else if (ResultConstant.isPause(resultCode)) { // 暂停
                    totalDownloadProgress.postValue(Pair(DOWNLOAD_WAIT, totalDownloadProgress.value?.second ?: 0))
                } else if (ResultConstant.isContinue(resultCode)) { // 继续
                    totalDownloadProgress.postValue(Pair(DOWNLOADING, totalDownloadProgress.value?.second ?: 0))
                }
            }
        }
    }

    /**
     *
     * 将路径进行本地化，将路径签名的 /sdcard 替换为“全部文件”
     */
    fun i18nSavePath(context: Context, path: String): String {
        // 确保路径存在
        val file = File(path)
        if (!file.exists()) {
            Log.w(TAG, "i18nSavePath $path not exist, create it")
            file.mkdirs()
        }
        if (path.startsWith(ROOT_PATH)) {
            val relativePath = path.substring(ROOT_PATH.length)
            Log.d(TAG, "i18nSavePath $path -> $relativePath")
            return "${context.getString(com.filemanager.common.R.string.string_all_files)}$relativePath"
        }
        return path
    }

    /**
     * 获取多个文件路径的总大小
     */
    fun getFileTotalSize(paths: List<DownloadRemoteFileBean>): String {
        var totalSize = 0L
        paths.forEach { file ->
            if (!file.mIsDirectory) {
                totalSize += file.mSize
            }
        }
        return Utils.byteCountToDisplaySize(totalSize)
    }

    /**
     * 判断该文件是否已经存在本地
     * @param file 下载的文件
     * @param savePath 存储的目录
     */
    fun isExist(file: DownloadRemoteFileBean, savePath: String): Boolean {
        val fileName = file.mDisplayName ?: ""
        val localFile = File(savePath, fileName)
        return localFile.exists()
    }

    fun startDownload(
        activity: AppCompatActivity,
        deviceId: String,
        downloadFiles: List<DownloadRemoteFileBean>,
        code: Int,
        pendingIntent: PendingIntent?
    ) {
        Log.e(TAG, "startDownload $deviceId")
        RemoteFileDownloadAction(activity, deviceId, downloadFiles, savePath.value ?: "", code, pendingIntent).execute(object :
            RemoteFileDownloadObserver(activity) {
            override fun onChanged(context: Context, result: Pair<Any, Any>): Boolean {
                if (result.first == RemoteFileDownloadObserver.UPDATE_DOWNLOAD_TOTAL_PROGRESS) {
                    val pair = result.second
                    if (pair is Pair<*, *>) {
                        val state = (pair as Pair<Int, Float>).first
                        val progress = (pair as Pair<Int, Float>).second
                        totalDownloadProgress.value = Pair(state, (progress.toInt()))
                    }
                } else if (result.first == RemoteFileDownloadObserver.UPDATE_DOWNLOAD_SINGLE_PROGRESS) {
                    val pair = result.second
                    if (pair is Pair<*, *>) {
                        val status = (pair as Pair<String, Int>).second
                        if (DownloadRemoteFileCallback.isCompleted(status)) {
                            downloadCompleteFile.value = pair.first
                        }
                    }
                }
                return super.onChanged(context, result)
            }

            override fun onActionDone(result: Boolean, data: Any?) {
                super.onActionDone(result, data)
                if (!result) {
                    totalDownloadProgress.postValue(Pair(DOWNLOAD_FAIL, 1))
                }
            }
        })
        statisticsDownload(downloadFiles)
    }

    private fun statisticsDownload(downloadFiles: List<DownloadRemoteFileBean>) {
        val fileCount = downloadFiles.size
        var fileSize = 0L
        downloadFiles.forEach {
            fileSize += it.size
        }
        Log.d(TAG, "statisticsDownload fileCount:$fileCount fileSize:$fileSize")
        OptimizeStatisticsUtil.remoteFileOperateEvent(StatisticsUtils.REMOTE_VALUE_OPERATE_DOWNLOAD, fileCount, fileSize)
    }

    fun currentDownloadingTaskId(): Int {
        return RemoteFileDownloadDispatcher.getCurrentDownloadingTask()?.taskId ?: -1
    }

    fun pauseDownload(activity: AppCompatActivity, deviceId: String, taskId: Int) {
        Log.d(TAG, "pauseDownload $deviceId task:$taskId")
        RemoteFileDownloadDispatcher.registerDownloadCallback(operateCallback)
        val intent = RemoteFileDownloadDispatcher.createPauseDownloadService(activity, deviceId, taskId)
        RemoteFileDownloadDispatcher.startService(activity, intent)
    }

    fun continueDownload(activity: AppCompatActivity, deviceId: String, taskId: Int) {
        Log.d(TAG, "continueDownload $deviceId task:$taskId")
        RemoteFileDownloadDispatcher.registerDownloadCallback(operateCallback)
        val intent = RemoteFileDownloadDispatcher.createContinueDownloadService(activity, deviceId, taskId)
        RemoteFileDownloadDispatcher.startService(activity, intent)
    }

    fun cancelDownload(activity: AppCompatActivity, deviceId: String, taskId: Int) {
        Log.d(TAG, "cancelDownload $deviceId task:$taskId")
        RemoteFileDownloadDispatcher.registerDownloadCallback(operateCallback)
        val intent = RemoteFileDownloadDispatcher.createCancelDownloadService(activity, deviceId, taskId)
        RemoteFileDownloadDispatcher.startService(activity, intent)
    }

    /**
     * /storage/emulated/0/Download/Remote PC Control/4a2ecc05a846085e5420fc0fef4b701e(4).jpeg
     * /storage/emulated/0/Download/Remote PC Control/4a2ecc05a846085e5420fc0fef4b701e.jpeg
     */
    fun isSameFile(path: String, otherPath: String): Boolean {
        if (path == otherPath) { // 两个文件完全相等
            return true
        }
        val ext = FileTypeUtils.getExtension(path)
        val otherExt = FileTypeUtils.getExtension(otherPath)
        if (ext != otherExt) { // 比较文件后缀
            return false
        }
        // 比较父目录
        val file = File(path)
        val otherFile = File(otherPath)
        if (file.parent != otherFile.parent) {
            return false
        }
        // 比较文件名
        val name = file.nameWithoutExtension
        val otherName = otherFile.nameWithoutExtension
        var shortName = name
        var longName = otherName
        if (name.length > otherName.length) {
            shortName = otherName
            longName = name
        }
        if (!longName.startsWith(shortName)) {
            return false
        }
        val numberStr = longName.replace(shortName, "").replace("(", "").replace(")", "")
        Log.d(TAG, "isSameFile numberStr:$numberStr")
        var isNumber = false
        try {
            numberStr.toInt()
            isNumber = true
        } catch (e: NumberFormatException) {
            isNumber = false
        }
        return isNumber
    }

    fun openFile(activity: AppCompatActivity, file: BaseFileBean) {
        Log.d(TAG, "openFile $file")
        FileActionOpen.Companion.Builder(activity, file)
            .setIsFromRecent(false)
            .build()
            .execute(object : FileOpenObserver(activity) {
                override fun onActionDone(result: Boolean, data: Any?) {
                    Log.d(TAG, "FileActionOpen onActionDone $result")
                    if (result) {
                        activity.finish()
                    }
                }
            })
    }

    fun continueMonitorDownload() {
        if (!monitor) {
            monitor = true
            RemoteFileDownloadDispatcher.registerDownloadCallback(operateCallback)
        }
    }

    fun jumpCategoryRemoteFileActivity(activity: AppCompatActivity, downloadFiles: List<DownloadRemoteFileBean>) {
        Log.e(TAG, "jumpCategoryRemoteFileActivity")
        val intent = DownloadNotifyManager.jumpRemotePCActivityIntent(downloadFiles.get(0).mData ?: DEFAULT_DOWNLOAD_PATH)
        activity.startActivity(intent)
        activity.finish()
    }

    override fun onCleared() {
        super.onCleared()
        RemoteFileDownloadDispatcher.unregisterDownloadCallback(operateCallback)
    }
}