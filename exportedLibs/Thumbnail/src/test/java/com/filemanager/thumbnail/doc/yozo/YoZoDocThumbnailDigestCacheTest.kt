/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - YoZoDocThumbnailDigestCacheTest.kt
 * Description:
 *     The test cases for YoZoDocThumbnailDigestCache
 *
 * Version: 1.0
 * Date: 2024-08-12
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * B<PERSON>ia<PERSON>.<EMAIL>    2024-08-12   1.0    Create this module
 *********************************************************************************/
package com.filemanager.thumbnail.doc.yozo

import android.content.Context
import android.net.Uri
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.doc.DocThumbnail
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.io.ByteArrayInputStream

class YoZoDocThumbnailDigestCacheTest : Assert() {

    private lateinit var digestCache: YoZoDocThumbnailDigestCache

    @Before
    fun setUp() {
        mockkStatic(ThumbnailManager.YoZoDocConfigs::class)
        every { ThumbnailManager.YoZoDocConfigs.enableDocDigestCache } returns true
        every { ThumbnailManager.YoZoDocConfigs.docDigestAlgorithm } returns "SHA-1"
        every { ThumbnailManager.YoZoDocConfigs.maxDigestDocSize } returns 100 * 1024 * 1024L
        digestCache = YoZoDocThumbnailDigestCache(
            mutableMapOf(),
            mutableMapOf(),
            mutableMapOf()
        )
    }

    @After
    fun tearDown() {
        unmockkStatic(ThumbnailManager.YoZoDocConfigs::class)
    }

    @Test
    fun `should get cached thumbnail when pickThumbnailCache if has cache`() {
        // Given
        val docUri = mockk<Uri>()
        val doc = DocThumbnail(
            uri = docUri,
            lastModified = 123L,
            size = 1024L,
            docFilePath = "testFilePath"
        )
        val context = mockk<Context> {
            every { contentResolver } returns mockk {
                every {
                    openInputStream(docUri)
                } returns ByteArrayInputStream(TEST_DOC_CONTENTS.toByteArray())
            }
        }
        val thumbUri = mockk<Uri>()
        digestCache.putThumbnailCache(context, doc, thumbUri)

        // When
        val result = digestCache.pickThumbnailCache(context, doc)

        // Then
        assertEquals(thumbUri, result)
    }

    @Test
    fun `should get null when pickThumbnailCache if no cache`() {
        // Given
        val docUri = mockk<Uri>()
        val doc = DocThumbnail(
            uri = docUri,
            lastModified = 123L,
            size = 1024L,
            docFilePath = "testFilePath"
        )
        val context = mockk<Context> {
            every { contentResolver } returns mockk {
                every {
                    openInputStream(docUri)
                } returns ByteArrayInputStream(TEST_DOC_CONTENTS.toByteArray())
            }
        }

        // When
        val result = digestCache.pickThumbnailCache(context, doc)

        // Then
        assertNull(result)
    }

    @Test
    fun `should get null when pickThumbnailCache if too large doc`() {
        // Given
        val docUri = mockk<Uri>()
        val doc = DocThumbnail(
            uri = docUri,
            lastModified = 123L,
            size = 1024 * 1024 * 1024L,
            docFilePath = "testFilePath"
        )
        val context = mockk<Context> {
            every { contentResolver } returns mockk {
                every {
                    openInputStream(docUri)
                } returns ByteArrayInputStream(TEST_DOC_CONTENTS.toByteArray())
            }
        }
        val thumbUri = mockk<Uri>()
        digestCache.putThumbnailCache(context, doc, thumbUri)

        // When
        val result = digestCache.pickThumbnailCache(context, doc)

        // Then
        assertNull(result)
    }

    @Test
    fun `should get null when pickThumbnailCache if modified doc`() {
        // Given
        val docUri = mockk<Uri>()
        val docBefore = DocThumbnail(
            uri = docUri,
            lastModified = 123L,
            size = 1024L,
            docFilePath = "testFilePath"
        )
        val docAfter = DocThumbnail(
            uri = docUri,
            lastModified = 456L,
            size = 968L,
            docFilePath = "testFilePath"
        )
        val context = mockk<Context> {
            every { contentResolver } returns mockk {
                every {
                    openInputStream(docUri)
                } returns ByteArrayInputStream(TEST_DOC_CONTENTS.toByteArray())
            }
        }
        val thumbUri = mockk<Uri>()
        digestCache.putThumbnailCache(context, docBefore, thumbUri)

        // When
        val result = digestCache.pickThumbnailCache(context, docAfter)

        // Then
        assertNull(result)
    }

    private companion object {
        private const val TEST_DOC_CONTENTS = "1234567890-=qwertyuiop[]|asdfghjkl;'zxcvbnm,./"
    }
}