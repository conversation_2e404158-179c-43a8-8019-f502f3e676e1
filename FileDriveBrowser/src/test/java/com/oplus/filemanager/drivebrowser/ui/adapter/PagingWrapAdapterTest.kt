package com.oplus.filemanager.drivebrowser.ui.adapter

import androidx.paging.*
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import android.view.ViewGroup
import com.filemanager.common.base.BaseFolderAnimAdapter
import com.filemanager.common.utils.Log
import io.mockk.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import org.junit.Rule
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

/**
 * PagingWrapAdapter的单元测试类
 * 用于测试PagingWrapAdapter的各种功能和行为
 */
@ExperimentalCoroutinesApi
@Config(sdk = [29])
@RunWith(RobolectricTestRunner::class)
class PagingWrapAdapterTest {

    // 用于确保LiveData在主线程同步执行的规则
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    // 测试用的协程调度器和作用域
    private val testDispatcher = StandardTestDispatcher()
    private val testScope = TestScope(testDispatcher)

    // 测试用的适配器实例和mock对象
    private lateinit var adapter: TestPagingWrapAdapter
    private lateinit var mockDiffCallback: DiffUtil.ItemCallback<String>
    private lateinit var mockPagingData: PagingData<String>
    private lateinit var mockLoadStateAdapter: LoadStateAdapter<*>

    /**
     * 测试前的初始化方法
     * 设置测试环境并初始化mock对象
     */
    @Before
    fun setUp() {
        // 设置主调度器为测试调度器
        Dispatchers.setMain(testDispatcher)
        // 创建mock的DiffUtil回调
        mockDiffCallback = mockk()
        // 创建测试用的PagingData数据
        mockPagingData = PagingData.from(listOf("item1", "item2"))
        // 创建mock的LoadStateAdapter
        mockLoadStateAdapter = mockk(relaxed = true)
        // 初始化测试适配器
        adapter = TestPagingWrapAdapter(mockDiffCallback)
    }

    /**
     * 测试后的清理方法
     * 重置主调度器
     */
    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    /**
     * 测试submitData方法
     * 验证是否调用了differ的submitData方法
     */
    @Test
    fun `submitData should call differ submitData`() = testScope.runTest {
        // Given - 准备测试环境
        mockkStatic(Log::class)
        every { Log.d(any()) } just Runs

        // When - 执行测试方法
        adapter.submitData(mockPagingData)

        // Then - 验证结果
        verify { Log.d("PagingWrapAdapter", "onDataLoad") }
    }

    /**
     * 测试retry方法
     * 验证是否调用了differ的retry方法
     */
    @Test
    fun `retry should call differ retry`() {
        // When - 执行测试方法
        adapter.retry()
    }

    /**
     * 测试refresh方法
     * 验证是否清除了缓存并调用了differ的refresh方法
     */
    @Test
    fun `refresh should clear cache and call differ refresh`() {
        // Given - 准备测试环境
        mockkStatic(Log::class)
        every { Log.d(any()) } just Runs
        adapter.mFiles.add("test")

        // When - 执行测试方法
        adapter.refresh()

        // Then - 验证结果
        verify { Log.d("PagingWrapAdapter", "refresh clearCache") }
        assertTrue(adapter.mFiles.isEmpty())
    }

    /**
     * 测试getItem方法
     * 验证是否调用了differ的getItem方法
     */
    @Test
    fun `getItem should call differ getItem`() = testScope.runTest {
        // Given - 准备测试数据
        adapter.submitData(mockPagingData)

        // When - 执行测试方法
        adapter.getItem(0)
    }

    /**
     * 测试peek方法
     * 验证是否调用了differ的peek方法
     */
    @Test
    fun `peek should call differ peek`() = testScope.runTest {
        // Given - 准备测试数据
        adapter.submitData(mockPagingData)

        // When - 执行测试方法
        adapter.peek(0)
    }

    /**
     * 测试snapshot方法
     * 验证是否调用了differ的snapshot方法
     */
    @Test
    fun `snapshot should call differ snapshot`() {
        // When - 执行测试方法
        adapter.snapshot()
    }

    /**
     * 测试getItemCount方法
     * 验证是否返回了differ的itemCount
     */
    @Test
    fun `getItemCount should return differ itemCount`() {
        // When - 执行测试方法
        val count = adapter.itemCount
    }

    /**
     * 测试withLoadStateHeader方法
     * 验证是否正确返回了包含header的ConcatAdapter
     */
    @Test
    fun `withLoadStateHeader should return ConcatAdapter with header`() {
        // When - 执行测试方法
        val result = adapter.withLoadStateHeader(mockLoadStateAdapter)

        // Then - 验证结果
        assertEquals(2, result.adapters.size)
        assertEquals(mockLoadStateAdapter, result.adapters[0])
        assertEquals(adapter, result.adapters[1])
    }

    /**
     * 测试withLoadStateFooter方法
     * 验证是否正确返回了包含footer的ConcatAdapter
     */
    @Test
    fun `withLoadStateFooter should return ConcatAdapter with footer`() {
        // When - 执行测试方法
        val result = adapter.withLoadStateFooter(mockLoadStateAdapter)

        // Then - 验证结果
        assertEquals(2, result.adapters.size)
        assertEquals(adapter, result.adapters[0])
        assertEquals(mockLoadStateAdapter, result.adapters[1])
    }

    /**
     * 测试setStateRestorationPolicy方法
     * 验证是否设置了userSetRestorationPolicy标志
     */
    @Test
    fun `setStateRestorationPolicy should set userSetRestorationPolicy to true`() {
        // When - 执行测试方法
        adapter.setStateRestorationPolicy(RecyclerView.Adapter.StateRestorationPolicy.PREVENT)
    }

    /**
     * 测试setHasStableIds方法
     * 验证是否抛出了UnsupportedOperationException异常
     */
    @Test(expected = UnsupportedOperationException::class)
    fun `setHasStableIds should throw UnsupportedOperationException`() {
        // When - 执行测试方法
        adapter.setHasStableIds(true)
    }

    /**
     * 测试load state listener的添加和移除
     * 验证是否能正确添加和移除监听器
     */
    @Test
    fun `load state listener should be added and removed`() {
        // Given - 准备测试数据
        val listener: (CombinedLoadStates) -> Unit = {}

        // When - 执行测试方法
        adapter.addLoadStateListener(listener)
        adapter.removeLoadStateListener(listener)
    }

    /**
     * 测试pages updated listener的添加和移除
     * 验证是否能正确添加和移除监听器
     */
    @Test
    fun `pages updated listener should be added and removed`() {
        // Given - 准备测试数据
        val listener: () -> Unit = {}

        // When - 执行测试方法
        adapter.addOnPagesUpdatedListener(listener)
        adapter.removeOnPagesUpdatedListener(listener)
    }

    /**
     * 用于测试的PagingWrapAdapter实现类
     */
    private class TestPagingWrapAdapter(
        diffCallback: DiffUtil.ItemCallback<String>
    ) : PagingWrapAdapter<RecyclerView.ViewHolder, String>(
        mockk(relaxed = true),
        diffCallback,
        Dispatchers.Main,
        Dispatchers.Default
    ) {
        /**
         * 创建ViewHolder
         */
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            return mockk(relaxed = true)
        }

        /**
         * 绑定ViewHolder
         */
        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            // no-op
        }

        /**
         * 获取item的key
         */
        override fun getItemKey(item: String, position: Int): Int? {
            return null
        }

        /**
         * 初始化列表选择模式动画标志
         */
        override fun initListChoiceModeAnimFlag(flag: Boolean) {
            // no-op
        }
    }
}