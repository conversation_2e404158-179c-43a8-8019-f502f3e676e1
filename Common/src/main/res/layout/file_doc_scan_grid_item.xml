<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/file_grid_item_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.filemanager.common.view.FileThumbView
        android:id="@+id/file_grid_item_icon"
        android:layout_width="@dimen/file_grid_frame_size"
        android:layout_height="@dimen/file_grid_frame_size"
        android:layout_centerHorizontal="true"
        android:forceDarkAllowed="false"
        android:scaleType="fitCenter" />

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/file_doc_grid_margin_title"
        android:ellipsize="middle"
        android:gravity="center_horizontal"
        android:layout_centerHorizontal="true"
        android:singleLine="true"
        android:textAppearance="?android:attr/textAppearanceLarge"
         android:textColor="?attr/couiColorLabelPrimary"
        android:textSize="@dimen/font_size_12"
        android:layout_below="@id/file_grid_item_icon" />

    <com.coui.appcompat.checkbox.COUICheckBox
        android:id="@+id/gridview_scrollchoice_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="0dp"
        android:clickable="false"
        android:focusable="false"
        android:layout_alignBottom="@id/file_grid_item_icon"
        android:layout_alignEnd="@id/file_grid_item_icon" />

</RelativeLayout>