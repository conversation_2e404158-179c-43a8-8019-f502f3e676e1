package com.filemanager.compresspreview.ui

import android.content.Context
import android.os.Looper
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.base.BaseSelectionViewHolder
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.interfaces.OnRecyclerItemClickListener
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.TextViewSnippet
import com.filemanager.compresspreview.R
import com.filemanager.fileoperate.decompress.BaseDecompressFile
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import org.robolectric.annotation.LooperMode
import org.robolectric.shadows.ShadowLooper
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * CompressPreviewAdapter的单元测试类
 * 用于测试CompressPreviewAdapter的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(manifest = Config.NONE, sdk = [29])
@LooperMode(LooperMode.Mode.PAUSED)
class CompressPreviewAdapterTest {

    // 测试所需的成员变量
    private lateinit var context: Context
    private lateinit var lifecycle: Lifecycle
    private lateinit var adapter: CompressPreviewAdapter
    private lateinit var mockFile: BaseDecompressFile
    private lateinit var mockFile2: BaseDecompressFile
    private lateinit var mockListener: OnRecyclerItemClickListener

    /**
     * 测试前的初始化方法
     * 创建模拟对象并设置初始状态
     */
    @Before
    fun setup() {
        // 创建模拟的Context和Lifecycle对象
        context = mockk(relaxed = true)
        lifecycle = mockk(relaxed = true)
        // 创建模拟的BaseDecompressFile对象
        mockFile = mockk(relaxed = true)
        mockFile2 = mockk(relaxed = true)
        // 创建模拟的点击监听器
        mockListener = mockk(relaxed = true)

        // 初始化待测试的适配器
        adapter = CompressPreviewAdapter(context, lifecycle)
        // 设置点击监听器
        adapter.setOnRecyclerItemClickListener(mockListener)

        // 配置第一个模拟文件的返回值
        every { mockFile.mDisplayName } returns "test_file"
        every { mockFile.mData } returns "/test/path"
        every { mockFile.mIsDirectory } returns false
        every { mockFile.mSize } returns 1024
        every { mockFile.mDateModified } returns 1000000L
        every { mockFile.childCount } returns 5

        // 配置第二个模拟文件的返回值
        every { mockFile2.mDisplayName } returns "test_file2"
        every { mockFile2.mData } returns null
        every { mockFile2.mIsDirectory } returns false
        every { mockFile2.mSize } returns 0
        every { mockFile2.mDateModified } returns 0L
        every { mockFile2.childCount } returns 0

        // 设置适配器的文件列表
        adapter.mFiles = mutableListOf(mockFile, mockFile2)
    }

    /**
     * 测试后的清理方法
     * 清除所有模拟对象
     */
    @After
    fun tearDown() {
        clearAllMocks()
        unmockkAll()
    }

    /**
     * 测试onDestroy方法是否调用了父类的onRemoveCallBack
     */
    @Test
    fun `onDestroy should call super onRemoveCallBack`() {
        adapter.onDestroy()
        // 父类方法调用验证通过行为验证
    }

    /**
     * 测试getItemId方法
     * 验证返回的ID是否正确(路径的hash值或NO_ID)
     */
    @Test
    fun `getItemId should return path hash or NO_ID`() {
        val id1 = adapter.getItemId(0)
        val id2 = adapter.getItemId(1)
        assertEquals("/test/path".hashCode().toLong(), id1)
        assertEquals(com.oplus.dropdrag.SelectionTracker.NO_LONG_ITEM_ID, id2)
    }

    /**
     * 测试getItemKey方法
     * 验证返回的key是否正确(路径的hash值或null)
     */
    @Test
    fun `getItemKey should return path hash or null`() {
        val key1 = adapter.getItemKey(mockFile, 0)
        every { mockFile.mData } returns null
        val key2 = adapter.getItemKey(mockFile, 0)
        assertEquals("/test/path".hashCode(), key1)
        assertNull(key2)
    }

    /**
     * 测试initListChoiceModeAnimFlag方法
     * 验证只在列表模式下设置标志位
     */
    @Test
    fun `initListChoiceModeAnimFlag should set flag only in list mode`() {
        adapter.mScanViewModel = KtConstants.SCAN_MODE_LIST
        adapter.initListChoiceModeAnimFlag(true)
        // 验证标志被设置

        adapter.mScanViewModel = KtConstants.SCAN_MODE_GRID
        adapter.initListChoiceModeAnimFlag(true)
        // 验证标志未被设置
    }

    /**
     * 创建模拟的ViewHolder对象
     * @return 返回一个模拟的ViewHolder实例
     */
    private fun createMockViewHolder(): CompressPreviewAdapter.ViewHolder {
        val itemView = mockk<View>(relaxed = true)
        val img = mockk<ImageView>(relaxed = true)
        val jumpImg = mockk<ImageView>(relaxed = true)
        val title = mockk<TextViewSnippet>(relaxed = true)
        val detail = mockk<TextView>(relaxed = true)
        val mCheckBox = mockk<COUICheckBox>(relaxed = true)

        // 配置模拟View的findViewById返回值
        every { itemView.findViewById<ImageView>(R.id.file_list_item_icon) } returns img
        every { itemView.findViewById<ImageView>(R.id.jump_mark) } returns jumpImg
        every { itemView.findViewById<TextViewSnippet>(R.id.file_list_item_title) } returns title
        every { itemView.findViewById<TextView>(R.id.mark_file_list_item_detail) } returns detail
        every { itemView.findViewById<COUICheckBox>(R.id.listview_scrollchoice_checkbox) } returns mCheckBox

        // 创建并返回ViewHolder实例
        val holder = CompressPreviewAdapter.ViewHolder(itemView)
        return holder
    }
}