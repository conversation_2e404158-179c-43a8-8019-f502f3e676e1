/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - DocThumbnailLoaderFactoryTest.kt
 * Description:
 * The test cases for DocThumbnailLoaderFactory
 *
 * Version: 1.0
 * Date: 2024-08-14
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-08-14   1.0    Create this module
 *********************************************************************************/
package com.filemanager.thumbnail.doc

import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.doc.wps.WpsDocThumbnailDelegate
import com.filemanager.thumbnail.doc.yozo.YoZoDocThumbnailLoader
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkStatic
import io.mockk.unmockkConstructor
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class DocThumbnailLoaderFactoryTest : Assert() {

    private val initEnableWps = ThumbnailManager.WpsDocConfigs.enableThumbnail
    private val initWpsPriority = ThumbnailManager.WpsDocConfigs.usagePriority
    private val initEnableYoZo = ThumbnailManager.YoZoDocConfigs.enableThumbnail
    private val initYoZoPriority = ThumbnailManager.YoZoDocConfigs.usagePriority

    @Before
    fun setUp() {
        mockkStatic(WpsDocThumbnailDelegate::class)
        mockkConstructor(YoZoDocThumbnailLoader::class)
        ThumbnailManager.WpsDocConfigs.enableThumbnail = initEnableWps
        ThumbnailManager.WpsDocConfigs.usagePriority = initWpsPriority
        ThumbnailManager.YoZoDocConfigs.enableThumbnail = initEnableYoZo
        ThumbnailManager.YoZoDocConfigs.usagePriority = initYoZoPriority
    }

    @After
    fun tearDown() {
        unmockkStatic(WpsDocThumbnailDelegate::class)
        unmockkConstructor(YoZoDocThumbnailLoader::class)
        ThumbnailManager.WpsDocConfigs.enableThumbnail = initEnableWps
        ThumbnailManager.WpsDocConfigs.usagePriority = initWpsPriority
        ThumbnailManager.YoZoDocConfigs.enableThumbnail = initEnableYoZo
        ThumbnailManager.YoZoDocConfigs.usagePriority = initYoZoPriority
    }

    private fun setSupportWps(support: Boolean): IDocThumbnailLoader {
        val mockWpsLoader = mockk<IDocThumbnailLoader> {
            every { isSupported() } returns support
        }
        every { WpsDocThumbnailDelegate.createDocThumbnailLoader(any()) } returns mockWpsLoader
        return mockWpsLoader
    }

    private fun setSupportYoZo(support: Boolean) {
        every { anyConstructed<YoZoDocThumbnailLoader>().isSupported() } returns support
    }

    @Test
    fun `should get wps when createInstance if enabled and supported and prefer wps`() {
        // Given
        val mockWpsLoader = setSupportWps(true)
        setSupportYoZo(true)
        ThumbnailManager.WpsDocConfigs.enableThumbnail = true
        ThumbnailManager.WpsDocConfigs.usagePriority = 200
        ThumbnailManager.YoZoDocConfigs.enableThumbnail = true
        ThumbnailManager.YoZoDocConfigs.usagePriority = 100

        // When
        val result = DocThumbnailLoaderFactory.createInstance(mockk(relaxed = true))

        // Then
        assertEquals(mockWpsLoader, result)
    }

    @Test
    fun `should get YoZo when createInstance if enabled and unsupported and prefer wps`() {
        // Given
        setSupportWps(false)
        setSupportYoZo(true)
        ThumbnailManager.WpsDocConfigs.enableThumbnail = true
        ThumbnailManager.WpsDocConfigs.usagePriority = 200
        ThumbnailManager.YoZoDocConfigs.enableThumbnail = true
        ThumbnailManager.YoZoDocConfigs.usagePriority = 100

        // When
        val result = DocThumbnailLoaderFactory.createInstance(mockk(relaxed = true))

        // Then
        assertTrue(result is YoZoDocThumbnailLoader)
    }

    @Test
    fun `should get YoZo when createInstance if disabled and supported and prefer wps`() {
        // Given
        setSupportWps(true)
        setSupportYoZo(true)
        ThumbnailManager.WpsDocConfigs.enableThumbnail = false
        ThumbnailManager.WpsDocConfigs.usagePriority = 200
        ThumbnailManager.YoZoDocConfigs.enableThumbnail = true
        ThumbnailManager.YoZoDocConfigs.usagePriority = 100

        // When
        val result = DocThumbnailLoaderFactory.createInstance(mockk(relaxed = true))

        // Then
        assertTrue(result is YoZoDocThumbnailLoader)
    }

    @Test
    fun `should get YoZo when createInstance if enabled and supported and prefer YoZo`() {
        // Given
        setSupportWps(true)
        setSupportYoZo(true)
        ThumbnailManager.WpsDocConfigs.enableThumbnail = true
        ThumbnailManager.WpsDocConfigs.usagePriority = 100
        ThumbnailManager.YoZoDocConfigs.enableThumbnail = true
        ThumbnailManager.YoZoDocConfigs.usagePriority = 200

        // When
        val result = DocThumbnailLoaderFactory.createInstance(mockk(relaxed = true))

        // Then
        assertTrue(result is YoZoDocThumbnailLoader)
    }

    @Test
    fun `should get null when createInstance if both unsupported`() {
        // Given
        setSupportWps(false)
        setSupportYoZo(false)
        ThumbnailManager.WpsDocConfigs.enableThumbnail = true
        ThumbnailManager.WpsDocConfigs.usagePriority = 100
        ThumbnailManager.YoZoDocConfigs.enableThumbnail = true
        ThumbnailManager.YoZoDocConfigs.usagePriority = 200

        // When
        val result = DocThumbnailLoaderFactory.createInstance(mockk(relaxed = true))

        // Then
        assertNull(result)
    }
}