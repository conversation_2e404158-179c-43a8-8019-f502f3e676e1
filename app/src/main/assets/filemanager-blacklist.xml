<?xml version="1.0" encoding="UTF-8"?>
<filter-conf>
    <version>2017082820</version>
	<isOpen>1</isOpen>
	<filter-name>filemanager-blacklist</filter-name>
	<integer name="filter_unknown_no_suffix">0</integer>

    <!-- default music balcklist -->
    <string-array name="default_music_blacklist">
        <item>default_music_blacklist:0:none</item>
    </string-array>

    <!-- default videos balcklist -->
    <string-array name="default_videos_blacklist">
        <item>default_videos_blacklist:0:none</item>
    </string-array>

	<!-- default photos balcklist -->
	<string-array name="default_photo_blacklist">
	    <item>default_photo_blacklist:0:none</item>
        <item>/MCache/</item>
        <item>/nmgc_sdk_release/</item>
        <item>/gamecenter/</item>
        <item>/NearMeMarket/</item>
	</string-array>

	<!-- default doc balcklist -->
	<string-array name="default_doc_blacklist">
	    <item>default_doc_blacklist:0:none</item>
        <item>/oppo/ime/</item>
        <item>/Tencent/</item>
        <item>/tencent/</item>
        <item>/BaiduMap</item>
        <item>/admin</item>
		<item>/oppo_log</item>
		<item>/mtklog</item>
        <item>/MCache/</item>
        <item>/nmgc_sdk_release/</item>
		<item>/browser/feedbacklog/</item>
		<item>/ThemeStore/Log/</item>
		<item>/.camera/</item>
		<item>/TBS/.logTmp/</item>
		<item>/ColorOS/Browser/feedbacklog/</item>
		<item>/iQiyiPushSdkLog/</item>
		<item>/Android/data/cn.wps.moffice_eng/</item>
    </string-array>
    <string-array name="more_doc_blacklist">
        <item>/tbs/file_locks/</item>
        <item>/qqmusic/eup/</item>
        <item>/guopan/sdk/</item>
        <item>/iyidui/push_log/</item>
        <item>/jj/update/module/</item>
        <item>/mdmsdk/</item>
        <item>/HMETemp/</item>
        <item>/tp_debug_info/</item>
        <item>/youdao/Dict/log/</item>
        <item>/com.heytap.yoli/</item>
        <item>/Android/media/com.xunmeng.pinduoduo/</item>
        <item>/tencent/tbs/backup/com.xunmeng.pinduoduo/</item>
        <item>/com.ss.android.ugc.aweme/</item>
        <item>/alipay/com.cgbchina.xpt/applogic/</item>
        <item>/alipay/com.eg.android.AlipayGphone/applogic/</item>
        <item>/alipay/com.eg.android.AlipayGphone/openplatform/</item>
        <item>/alipay/com.eg.android.AlipayGphone/trafficLogic/</item>
        <item>/alipay/multimedia/</item>
        <item>/nebula/live_honor_medal_resource/</item>
        <item>/.gifshowWallPaper/</item>
        <item>/nebula/live_pk_game_resource/</item>
        <item>/nebula/.magic_gift/</item>
        <item>/nebula/.kuaishan_library_resource/</item>
        <item>/nebula/dirtylens_resource/</item>
        <item>/nebula/editor_face_magic_effect_resource/</item>
        <item>/nebula/live_enter_room_effect/</item>
        <item>/nebula/music_resource/</item>
        <item>/nebula/sticker_resource/</item>
        <item>/nebula/story_sticker_resource/</item>
        <item>/nebula/theme_resource/</item>
        <item>/nebula/.local_render_magic_gift/</item>
        <item>/nebula/.sticker_library_resource/</item>
        <item>/nebula/live_growth_red_packet_v2/</item>
        <item>/nebula/live_pet_model/</item>
        <item>/nebula/live_pk_game_in_pk/</item>
        <item>/nebula/live_quiz_sound/</item>
        <item>/nebula/live_red_pack_rain/</item>
        <item>/nebula/live_resource_file_test/</item>
        <item>/nebula/live_robot_audio/</item>
        <item>/nebula/live_voice_party/</item>
        <item>/gifshow/.kmoji_user_config/</item>
        <item>/gifshow/.material_library_resource/CANVAS/</item>
        <item>/gifshow/.upload_work_cache/</item>
        <item>/nebula/.hybrid/</item>
        <item>/nebula/data/</item>
        <item>/nebula/effect_resource/</item>
        <item>/nebula/keyconfig/</item>
        <item>/nebula/magic_finger_resource/</item>
        <item>/nebula/text_resource/</item>
        <item>/nebula/filter_resource/</item>
        <item>/nebula/.emoji_ttf/</item>
        <item>/nebula/.magic_emoji/</item>
        <item>/nebula/magic_emoji_resource/</item>
        <item>/nebula/.encoding_output_path/</item>
        <item>/nebula/.long_video/</item>
        <item>/nebula/.music/</item>
        <item>/nebula/.voice_cache/</item>
        <item>/nebula/.fonts/</item>
        <item>/Reader/.sysdir/</item>
        <item>/Reader/mt/</item>
        <item>/Reader/tmp/</item>
        <item>/Reader/logs/</item>
        <item>/Reader/skins/</item>
        <item>/Reader/plugins/</item>
        <item>/Reader/.a/</item>
        <item>/Reader/ting/info/</item>
        <item>/Reader/Images/</item>
        <item>/Reader/saveImage/</item>
        <item>/Reader/album/</item>
        <item>/Reader/cover/</item>
        <item>/Reader/TTS/</item>
        <item>/Reader/Fonts/</item>
        <item>/Reader/font/</item>
        <item>/QieZi/.caches/</item>
        <item>/QieZi/.log/</item>
        <item>/iqiyi/dlna/</item>
        <item>/QieZi/.thumbnails/</item>
        <item>/QieZi/audios/</item>
        <item>/QieZi/pictures/</item>
        <item>/QYReader/cover_pic/</item>
        <item>/youku/cacheData/</item>
        <item>/shuqi_youku/chaptercache/</item>
        <item>/youku/Medal/</item>
        <item>/youku/gifts/</item>
        <item>/youku/offlinedata/</item>
        <item>/youku/ZipResources/</item>
        <item>/youku/YoukuScreenShot/</item>
        <item>/shuqi_youku/fonts/</item>
        <item>/youku/fonts/</item>
        <item>/tt_video/ssvideo/</item>
        <item>/news_article_lite/.res/</item>
        <item>/news_article_lite/.ssvideo_cache/</item>
        <item>/tencent/tbs/com.tencent.tmgp.sgame/</item>
        <item>/tv.danmaku.bili/source/</item>
        <item>/tv.danmaku.bili/download/</item>
        <item>/Tencent/karaoke/video_thumb/</item>
        <item>/tencent/karaoke/webso/offline/</item>
        <item>/tencent/karaoke/image/</item>
        <item>/ting/errorLog/</item>
        <item>/ting/images/</item>
        <item>/ting/player_caching/files/</item>
        <item>/Pictures/Lark/</item>
        <item>/Lark/camera/</item>
        <item>/com.netease.cloudmusic/log/</item>
        <item>/com.netease.cloudmusic/nim/log/</item>
        <item>/netease/cloudmusic/Cache/ImageCache/</item>
        <item>/netease/cloudmusic/Cache/crop/</item>
        <item>/netease/cloudmusic/Cache/FlashSongs/</item>
        <item>/netease/cloudmusic/Cache/ShortVideo/</item>
        <item>/netease/cloudmusic/Cache/VideoCache/</item>
        <item>/netease/cloudmusic/loadingCover/</item>
        <item>/netease/cloudmusic/Cache/Recorder/</item>
        <item>/netease/cloudmusic/Cache/LTBeep/</item>
        <item>/netease/cloudmusic/Dj/</item>
        <item>/netease/cloudmusic/Recorder/</item>
        <item>/netease/cloudmusic/Alarm/</item>
        <item>/netease/cloudmusic/Cache/MLog/</item>
        <item>/netease/cloudmusic/Cache/NewApk/</item>
        <item>/netease/play/Cache/Gifts/</item>
        <item>/netease/play/Gifts/</item>
        <item>/Pictures/com.netease.cloudmusic/</item>
        <item>/netease/cloudmusic/Stacktrace/</item>
        <item>/netease/cloudmusic/Cache/UpgradeTemp/</item>
        <item>/netease/cloudmusic/Cache/Image/</item>
        <item>/netease/cloudmusic/Download/Image/</item>
        <item>/netease/cloudmusic/LocalMusic/Image/</item>
        <item>/netease/cloudmusic/Cache/BackgroundVideo/</item>
        <item>/netease/cloudmusic/MV/</item>
        <item>/netease/cloudmusic/Cache/Download/</item>
        <item>/netease/cloudmusic/MusicSheet/</item>
        <item>/netease/cloudmusic/SportFM/</item>
        <item>/netease/cloudmusic/cloudMusicRingtone/</item>
        <item>/netease/cloudmusic/Cache/Music1/</item>
        <item>/baidu/haokanminvideo/ugc_capture/ai_model/</item>
        <item>/baidu/haokanminvideo/ugc_capture/duar/</item>
        <item>/Quark/ucache/</item>
        <item>/com.quark.browser/ulog/</item>
        <item>/Quark/share_qrcode/</item>
        <item>/KmxsReader/image/</item>
        <item>/KmxsReader/plugin/</item>
        <item>/BaiduYuedu/.log/</item>
        <item>/Pictures/com.baidu.netdisk/helios/</item>
        <item>/BaiduYuedu/fonts/</item>
        <item>/tencent/QQmail/cache/</item>
        <item>/tencent/QQmail/compresscache/</item>
        <item>/tencent/QQmail/localcache/</item>
        <item>/tencent/QQmail/tmp/</item>
        <item>/tencent/QQmail/webviewext/</item>
        <item>/tencent/QQmail/log/</item>
        <item>/tencent/QQmail/qmlog/</item>
        <item>/tencent/QQmail/CachePhoto/</item>
        <item>/tencent/QQmail/imagecache/</item>
        <item>/tencent/QQmail/emailIcon/</item>
        <item>/tencent/QQmail/nickIcon/</item>
        <item>/tencent/QQmail/audiorecord/</item>
        <item>/tencent/QQmail/composemail/</item>
        <item>/tencent/QQmail/composenote/</item>
        <item>/tencent/QQmail/error_patch/</item>
        <item>/tencent/QQmail/kv/</item>
        <item>/tencent/QQmail/patch/</item>
        <item>/tencent/QQmail/popularizeapk/</item>
        <item>/tencent/QQmail/contact_tmp/</item>
        <item>/tencent/QQmail/native_pages/</item>
        <item>/tencent/QQmail/card_tmp/</item>
        <item>/tencent/QQmail/card_cache/</item>
        <item>/tencent/QQmail/screenshot/</item>
        <item>/comtop.im/ui/Cache/</item>
        <item>/comtop.im/log/</item>
        <item>/tencent/WeixinWork/MicroMsg/vusericon/</item>
        <item>/tencent/WeixinWork/data/</item>
        <item>/zapya/app/</item>
        <item>/zapya/misc/</item>
        <item>/zapya/doodle/</item>
        <item>/zapya/photo/</item>
        <item>/zapya/video/</item>
        <item>/zapya/music/</item>
        <item>/tencent/com.tencent.weishi/</item>
        <item>/Tencent/WeiShi/cache_logs/</item>
        <item>/Tencent/WeiShi/logs/</item>
        <item>/com.UCMobile/ulog/</item>
        <item>/tieba/post_image/</item>
        <item>/tieba/images/</item>
        <item>/photowonder/material/</item>
        <item>/tieba/takepic/</item>
        <item>/tieba/.cover_style/</item>
        <item>/tieba/.video/</item>
        <item>/tieba/tbVideo/</item>
        <item>/tieba/voice/</item>
        <item>/tencent/TencentVideo/CutPic/</item>
        <item>/tencent/TencentVideo/SavePic/</item>
        <item>/tencent/TencentVideo/TencentVideo/</item>
        <item>/tencent/TencentVideo/VideoShot/</item>
        <item>/Tencent/audio/</item>
        <item>/tencent/QQ_Images/</item>
        <item>/tencent/tbs/backup/com.tencent.mobileqq/</item>
        <item>/sina/weibo/storage/newsfeed/</item>
        <item>/sina/weibo/storage/pagecard_no_auto_clear/</item>
        <item>/sina/weibo/storage/biz_common/</item>
        <item>/sina/weibo/storage/story_ar/</item>
        <item>/sina/weibo/storage/story_draft/</item>
        <item>/sina/weibo/storage/story_musiccut/</item>
        <item>/sina/weibo/.database/</item>
        <item>/sina/weibo/.sensear/</item>
        <item>/sina/weibo/storage/weibo_detail/</item>
        <item>/sina/weibo/storage/weiyou_chat/</item>
        <item>/sina/weibo/storage/yizhibo_keep/</item>
        <item>/sina/weibo/storage/photoalbum_emotion/</item>
        <item>/Pictures/weibo/</item>
        <item>/sina/weibo/storage/photoalbum_save/weibo/</item>
        <item>/sina/weibo/storage/photoalbum_save/</item>
        <item>/sina/weibo/weibo/</item>
        <item>/sina/weibo/.weibo_chat/</item>
        <item>/sina/news/WDbook/</item>
        <item>/sina/news/font/</item>
        <item>/QQReader/skin/</item>
        <item>/QQReader/PlugIn/</item>
        <item>/QQReader/bkd/</item>
        <item>/QQReader/cover/</item>
        <item>/QQReader/user/</item>
        <item>/qqmusic/common_statics.log/</item>
        <item>/qqmusic/playLogTmp/</item>
        <item>/qqmusic/crashDump/</item>
        <item>/qqmusic/xlog/</item>
        <item>/qqmusic/log/</item>
        <item>/qqmusic/nlog/</item>
        <item>/qqmusic/playerlog/</item>
        <item>/qqmusiccar/log/</item>
        <item>/.UpBrowser/null/log/</item>
        <item>/QQBrowser/plugins/</item>
        <item>/Tencent/now/file/</item>
        <item>/com.tencent.mtt/files/</item>
        <item>/com.tencent.mtt_KcSdk/files/</item>
        <item>/QQBrowser/editcopyfiles/</item>
        <item>/KuwoMusic/.crash/</item>
        <item>/KuwoMusic/.data/OFFLINE_BACKLOG/</item>
        <item>/KuwoMusic/.data/OFFLINE_LOG/</item>
        <item>/KuwoMusic/.login/LOGIN_CACHE/</item>
        <item>/KuwoMusic/crashbak/</item>
        <item>/KuwoMusic/log/</item>
        <item>/cn.kuwo.player/source/sdk/file/source/a/log/</item>
        <item>/kugou/log/</item>
        <item>/kugou/v8skin/log/</item>
        <item>/kugou/config/</item>
        <item>/gifshow/.debug/</item>
        <item>/gifshow/theme_resource/</item>
        <item>/Browser/tbslog/</item>
        <item>/ColorOS/Browser/.debug/</item>
        <item>/ColorOS/Browser/.log/</item>
        <item>/browser/feedbacklog/</item>
        <item>/OPPO_Office/Log</item>
        <item>/qqmusic/fireEye</item>
        <item>/Documents/YunJiLog/</item>
        <item>/Documents/JNotesLog/</item>
    </string-array>

    <!-- default apks balcklist -->
    <string-array name="default_apks_blacklist">
        <item>default_apks_blacklist:0:none</item>
    </string-array>

    <!-- default zip balcklist -->
    <string-array name="default_zip_blacklist">
        <item>default_zip_blacklist:0:none</item>
    </string-array>

    <!-- default zip balcklist -->
    <string-array name="default_bluetooth_blacklist">
        <item>default_bluetooth_blacklist:0:none</item>
    </string-array>

    <!-- default seven list, current is for qq -->
    <string-array name="category_seven_eight_names">
        <item>QQ</item>
        <item>WeChat</item>
    </string-array>

    <!-- default seven list, current is for qq -->
    <string-array name="default_seven_list">
	    <item>default_seven_list:0:none</item>
        <item>Tencent/QQfile_recv/</item>
        <item>Tencent/QQ_Images/</item>
    </string-array>

    <!-- test for update from web <item>Tencent/QQ_Images/</item> -->
    <string-array name="default_eight_list">
        <item>default_eight_list:0:none</item>
        <item>Tencent/MicroMsg/WeiXin/</item>
        <item>Tencent/MicroMsg/Download/</item>
        <item>Tencent/MicroMsg/WeChat/</item>
    </string-array>

    <!-- default unknown files -->
    <string-array name="default_unknown_file">
        <item>.oga;.mid:酷狗音乐:com.kugou.android:0</item>
        <item>.doc;.docx;.pdf;.log;.dot;.dotx;.ppt;.pptx;.pps;.ppsx;.pot;.potx;.xls;.xlsx;.xlt;.xltx;.lrc:WPS Office:cn.wps.moffice_eng:2</item>
        <item>.torrent:迅雷:com.xunlei.downloadprovider:3</item>
    </string-array>

    <!-- default unknown files -->
    <string-array name="default_unknown_file_exp">
        <item>.oga;.mid:com.gaana:0</item>
        <item>.rmvb;.rm;.ac3:MX Player:com.mxtech.videoplayer.ad:1</item>
        <item>.doc;.docx;.log;.dot;.dotx;.lrc:WPS Office:cn.wps.moffice_eng:2</item>
        <item>.xls;.xlsx;.xlt;.xltx:Excel:com.microsoft.office.excel:4</item>
        <item>.ppt;.pptx;.pps;.ppsx;.pot;.potx:Microsoft PowerPoint:com.microsoft.office.powerpoint:5</item>
        <item>.pdf:Adobe Acrobat:com.adobe.reader:6</item>
    </string-array>

    <!-- file type splited by ,(as:mp4,wmv) -->
    <string-array name="extension_eight_list">
        <item>video/;Tencent/MicroMsg/;^[a-z0-9]{32}$;mp4</item>
   </string-array>

   <string-array name="extension_seven_list">
        <item>;Tencent/MobileQQ/shortvideo/;^[A-Z0-9]{32}$;*</item>
   </string-array>
    <string-array name="filter_recent_files">
        <item>db;dat;txt;log;xml;ind;prop</item>
    </string-array>
</filter-conf>