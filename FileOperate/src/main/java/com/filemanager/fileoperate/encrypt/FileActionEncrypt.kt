/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File: FileActionPrivacy.kt
 ** Description: Set file to privacy
 ** Version: 1.0
 ** Date: 2020/2/26
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.encrypt

import android.content.DialogInterface
import android.os.IBinder
import android.os.RemoteException
import androidx.lifecycle.LifecycleOwner
import com.coui.appcompat.dialog.COUISecurityAlertDialogBuilder
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.compat.OplusUsbEnvironmentCompat
import com.filemanager.common.cpu.PerformanceManager
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Utils.MEDIA_SCAN_ENCRYPTION
import com.filemanager.common.controller.FileEncryptController
import com.filemanager.common.utils.FileTraceUtil
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NewFunctionSwitch
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.SafeUtils
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.fileoperate.base.ACTION_DONE
import com.filemanager.fileoperate.base.ACTION_FAILED
import com.filemanager.fileoperate.base.BaseFileAction
import com.filemanager.fileoperate.base.BaseFileActionObserver
import com.filemanager.fileoperate.base.SHOW_PROGRESS
import com.filemanager.fileoperate.base.UPDATE_PROGRESS
import com.oplus.filemanager.interfaze.fileservice.IFileService
import com.oplus.filemanager.interfaze.fileservice.IFileService.Companion.OPERATE_TYPE_ENCRYPT
import com.oplus.filemanager.interfaze.main.IMain
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class FileActionEncrypt(
    lifecycle: LifecycleOwner,
    encryptService: FileEncryptController.FileManagerEncryptionInterface,
    fileList: List<BaseFileBean>
) : BaseFileAction<FileEncryptObserver>(lifecycle) {
    companion object {
        const val TAG = "FileActionEncrypt"
        private const val MEDIA_SCAN_COUNT = 50
        private const val DELAYED_TIME = 200L
        private const val BATCH_SIZE = 2000

        // Encryption result code
        private const val SUCCESS = 0
        private const val PERMISSION_FAILED = 1
        private const val OTHER_FAILED = 2
        private const val GUIDE_FAILED = 3

        private const val PREFERENCE_ENCRYPT_REMIND = "preference_encrypt_remind"
    }

    private var mOperateFiles = ArrayList<BaseFileBean>()
    private var mService: FileEncryptController.FileManagerEncryptionInterface? = encryptService
    private var mProgressListener: FileEncryptController.FileManagerIEncryptProgressListener? = null
    private var mFileOperateSyncPaths = hashSetOf<String?>()
    private var mLockObj = Object()
    private var totalFailedCount = 0
    private var startIndex = 0
    private var batchEncrypt = false

    init {
        initData(fileList)
    }

    private fun initData(fileList: List<BaseFileBean>) {
        mOperateFiles.addAll(fileList.filter {
            it.mIsDirectory.not()
        })
    }

    override fun run(): Boolean {
        if (mOperateFiles.isEmpty() || (mService == null)) {
            Log.d(TAG, "source file/directory is null or empty")
            return false
        }
        StatisticsUtils.onCommon(mContext, StatisticsUtils.MOVE_INTO_SAFE_PRESSED)
        startCpuPerformanceImprove()
        try {
            return doSendPrivacy()
        } finally {
            stopCpuPerformanceImprove()
            cancelNotifyObserver(SHOW_PROGRESS)
        }
    }

    override fun interceptAfterRun() {
        Log.d(TAG, "interceptAfterRun batchEncrypt = $batchEncrypt")
        if (batchEncrypt.not()) {
            super.interceptAfterRun()
        }
    }

    private fun doSendPrivacy(): Boolean {
        if (NewFunctionSwitch.SUPPORT_SECURITY_ENCRYPT && isNeedRemind()) {
            var flag = true
            showConfirmDialog()
            while (!isCancelled()) {
                if (!waitLockRelease()) {
                    Log.e(TAG, "doSendPrivacy wait error")
                    flag = false
                }
                break
            }
            Log.d(TAG, "doSendPrivacy wait finish $flag")
            if (!flag) {
                return false
            }
        }
        if (isCancelled()) {
            return false
        }

        changeEncryptionState(false)

        MediaStoreCompat.getCShotFiles(mOperateFiles)
        if (NewFunctionSwitch.SUPPORT_SECURITY_ENCRYPT) {
            notifyObserver(SHOW_PROGRESS, mOperateFiles.size)
        } else {
            notifyObserver(
                SHOW_PROGRESS, BaseFileActionObserver.ProgressDialogBean(
                    mContext.getString(com.filemanager.common.R.string.encryp_progress_text),
                    false), DELAYED_TIME)
        }
        val pathList = arrayListOf<String>()
        val imgList = arrayListOf<BaseFileBean>()
        var hasImgOrVideo = false
        mOperateFiles.forEach {
            it.mData?.let { path ->
                pathList.add(path)
                if (it.mLocalType == MimeTypeHelper.IMAGE_TYPE) {
                    imgList.add(it)
                }
                if (!hasImgOrVideo
                    && ((it.mLocalType == MimeTypeHelper.IMAGE_TYPE) || (it.mLocalType == MimeTypeHelper.VIDEO_TYPE))
                    && (path.startsWith(OplusUsbEnvironmentCompat.getInternalPath(mContext)))) {
                    hasImgOrVideo = true
                }
                mFileOperateSyncPaths.add(path)
            }
        }
        if (pathList.isEmpty()) {
            Log.d(TAG, "doSendPrivacy failed: path list empty")
            cancelNotifyObserver(SHOW_PROGRESS)
            encryptDone(OTHER_FAILED, mOperateFiles.size)
            return true
        }

        pathList.forEach { path ->
            StatisticsUtils.onCommon(
                mContext,
                StatisticsUtils.EVENT_ENCRYPTION_FILE,
                mapOf(StatisticsUtils.FILE_TYPE to FileTypeUtils.getExtension(path))
            )
        }
        Log.d(TAG, "doSendPrivacy: hasImgOrVideo = $hasImgOrVideo,  imgListSize = ${imgList.size}")
        batchDoEncryptionTasks(pathList, hasImgOrVideo, imgList)
        return true
    }

    private fun batchDoEncryptionTasks(
        pathList: ArrayList<String>,
        hasImgOrVideo: Boolean,
        imgList: ArrayList<BaseFileBean>
    ) {
        totalFailedCount = 0
        startIndex = 0
        batchEncrypt = false
        val pathSize = pathList.size
        if (pathSize > BATCH_SIZE) {
            batchEncrypt = true
        }
        val mediaScanState = pathList.size > MEDIA_SCAN_COUNT
        Log.d(TAG, "batchDoEncryptionTasks: hasImgOrVideo = $hasImgOrVideo,  imgListSize = ${imgList.size}")
        doEncryptionTasks(pathList, mediaScanState, hasImgOrVideo, imgList)
    }
    private fun doEncryptionTasks(
        pathList: ArrayList<String>,
        mediaScanState: Boolean,
        hasImgOrVideo: Boolean,
        imgList: ArrayList<BaseFileBean>
    ) {
        val pathSize = pathList.size
        val endIndex = minOf(startIndex + BATCH_SIZE, pathSize)
        val isLastBatch = endIndex == pathSize
        val batchIndex = (endIndex - 1) / BATCH_SIZE
        Log.d(TAG, "doEncryptionTasks pathSize = $pathSize, " +
                "mediaScanState = $mediaScanState, " +
                "totalSize = $pathSize, " +
                "isLastBatch = $isLastBatch, " +
                "startIndex = $startIndex, " +
                "endIndex = $endIndex, " +
                "batchIndex = $batchIndex"
        )
        val subList = pathList.subList(startIndex, endIndex)
        if (subList.isEmpty()) {
            return
        }
        val subListSize = subList.size
        val typeList = IntArray(pathList.size).apply { fill(-1) }
        var resultReturn: Int? = null
        mProgressListener = object : FileEncryptController.FileManagerIEncryptProgressListener {
            override fun onStarted() {
                Log.d(TAG, "encryptionTasks start")
                notifyObserver(UPDATE_PROGRESS, 0)
            }

            override fun onProgress(progress: Int) {
                val realProgress = ((batchIndex * (BATCH_SIZE / pathSize.toFloat()) + subListSize / pathSize.toFloat()) * progress).toInt()
                Log.d(TAG, "progress = $progress, realProgress = $realProgress")
                notifyObserver(UPDATE_PROGRESS, realProgress)
            }

            override fun asBinder(): IBinder? = null

            override fun onFinished(result: Int, failedCount: Int) {
                Log.d(TAG, "Encrypt finish: result=$result, failedCount=$failedCount, isLastBatch = $isLastBatch")
                trace(hasImgOrVideo, imgList)
                totalFailedCount += failedCount
                if (isLastBatch) {
                    if (mediaScanState) {
                        MediaScannerCompat.sendMediaScanner(null, MEDIA_SCAN_ENCRYPTION)
                    }
                    // Over size file not encrypt, so failedCount should include them
                    encryptDone(result, totalFailedCount, pathList)
                    batchEncrypt = false
                    interceptAfterRun()
                } else {
                    startIndex += BATCH_SIZE
                    MainScope().launch(Dispatchers.Default) {
                        delay(DELAYED_TIME)
                        doEncryptionTasks(pathList, mediaScanState, hasImgOrVideo, imgList)
                    }
                }
                resultReturn = result
            }
        }
        Log.d(TAG, "encryptionTasks ${subList.size}")
        mService?.encryptionTasks(subList, typeList, mediaScanState, mProgressListener)
        while (!isCancelled() && (resultReturn == null)) {
            // Wait encryption result
            Thread.sleep(DELAYED_TIME)
        }
    }

    private fun trace(hasImgOrVideo: Boolean, imgList: ArrayList<BaseFileBean>) {
        if (hasImgOrVideo) {
            SafeUtils.notifySyncGalleryDB(mContext)
        }
        if (imgList.isNotEmpty()) {
            FileTraceUtil.getInstance().traceAtThisMoment()
        }
        imgList.forEach {
            FileTraceUtil.getInstance().traceAction(
                FileTraceUtil.TraceAction.MARK_ENCRYPT, MimeTypeHelper.IMAGE_TYPE, it.mData)
        }
    }

    private fun encryptDone(
        result: Int,
        failedCount: Int,
        pathList: ArrayList<String>? = null
    ) {
        cancelNotifyObserver(SHOW_PROGRESS)
        when (result) {
            PERMISSION_FAILED -> {
                notifyObserver(ERROR_ENCRYPT_PERMISSION, result)
                return
            }
            GUIDE_FAILED -> {
                notifyObserver(ACTION_FAILED)
                return
            }
            SUCCESS, OTHER_FAILED -> {
                when {
                    failedCount > 0 -> {
                        notifyObserver(ACTION_FAILED, failedCount)
                    }
                    else -> {
                        if (result != OTHER_FAILED) {
                            val fileServiceAction = Injector.injectFactory<IFileService>()
                            fileServiceAction?.syncOperate(OPERATE_TYPE_ENCRYPT, mFileOperateSyncPaths)
                        }
                        notifyObserver(ACTION_DONE)
                        if (result != OTHER_FAILED) {
                            pathList?.let {
                                val mainAction = Injector.injectFactory<IMain>()
                                mainAction?.onDeleteFilePaths(it)
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onCancelled() {
        changeEncryptionState(true)
        super.onCancelled()
    }

    private fun changeEncryptionState(state: Boolean) {
        Log.d(TAG, "changeEncryptionState: $state, $mService")
        try {
            mService?.setStopEncryption(state)
        } catch (e: RemoteException) {
            Log.e(TAG, "changeEncryptionState: ${e.message}")
        }
    }

    private fun startCpuPerformanceImprove() {
    }

    private fun stopCpuPerformanceImprove() {
        if (!SdkUtils.isAtLeastR()) {
            PerformanceManager.release()
        }
    }

    override fun recycle() {
        mProgressListener = null
        mOperateFiles.clear()
        mService = null
    }

    private fun showConfirmDialog() {
        val listener = COUISecurityAlertDialogBuilder.OnSelectedListener { whichButton, isChecked ->
            if (whichButton == DialogInterface.BUTTON_NEGATIVE) {
                cancel()
                notifyLockReleased()
            } else if (whichButton == DialogInterface.BUTTON_POSITIVE) {
                saveRemind(!isChecked)
                notifyLockReleased()
            }
        }
        val dialogBean = FileSecurityEncryptObserver.DialogBean(mOperateFiles.size, listener)
        notifyObserver(SHOW_CONFIRM_DIALOG, dialogBean)
    }

    private fun isNeedRemind(): Boolean {
        return PreferencesUtils.getBoolean(key = PREFERENCE_ENCRYPT_REMIND, default = true)
    }

    private fun saveRemind(needRemind: Boolean) {
        PreferencesUtils.put(key = PREFERENCE_ENCRYPT_REMIND, value = needRemind)
    }

    @Suppress("TooGenericExceptionCaught")
    private fun notifyLockReleased() {
        try {
            synchronized(mLockObj) {
                mLockObj.notify()
            }
        } catch (e: Exception) {
            Log.d(TAG, "notifyLockReleased e = $e")
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun waitLockRelease(): Boolean {
        try {
            synchronized(mLockObj) {
                mLockObj.wait()
            }
        } catch (e: Exception) {
            Log.d(TAG, "waitLockRelease e = $e")
            return false
        }
        return true
    }
}