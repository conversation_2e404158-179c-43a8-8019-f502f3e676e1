/*********************************************************************
 * * Copyright (C), 2010-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : select folder and rename file
 * * Version     : 1.0
 * * Date        : 2023/5/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.selectdir

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Context.INPUT_METHOD_SERVICE
import android.content.DialogInterface
import android.content.res.Configuration
import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.text.TextWatcher
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ListView
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.button.SingleButtonWrap
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.edittext.COUIEditText
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.rotateview.COUIRotateView
import com.coui.appcompat.toolbar.COUIToolbar
import com.coui.responsiveui.config.UIConfig
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.EmojiUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.RenameErrorTipUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.view.BrowserPathBar
import com.oplus.selectdir.SelectPathController.Companion.SELECT_FILE_TYPE_FILL_NAME_LIST
import com.oplus.selectdir.view.COUICardSingleInputSaveView
import java.io.File
import java.nio.charset.StandardCharsets

class SelectDirPathRenamePanelFragment : SelectDirPathPanelFragment() {

    companion object {
        private const val TAG = "SelectDirPathRenamePanelFragment"
        private const val SELECT_POSITION = "select_position"
        private const val FILE_NAME_CONNECTOR = "_"
        private const val SPLIT_CHAR = ":"

        const val PARAMETER_IS_OVERRIDE = "is_override"
        const val PARAMETER_PANEL_TITLE = "title"
        const val PARAMETER_BTN_CONFIRM = "btn_confirm"
    }
    private var fileName: String? = null
    private var fileExts: ArrayList<String>? = null
    private var couiCardInputView: COUICardSingleInputSaveView? = null
    private var inputErrorView: View? = null
    private var nameEdit: COUIEditText? = null
    private var nameExt: TextView? = null
    private var nameFrame: View? = null
    private var selectPosition = 0
    private var fileNameFillMap: HashMap<String, String?>? = null
    private var oldSelectPosition = 0
    private var oldFillName: String? = null
    private var bottomArea: View? = null
    private var imgExt: COUIRotateView? = null
    private var onGlobalLayoutListener: ViewTreeObserver.OnGlobalLayoutListener? = null
    private var initFileNameLen = 0
    private var toolbar: COUIToolbar? = null
    private var verticalButtonWrap: SingleButtonWrap? = null
    private var waringFileExistsDialog: Dialog? = null


    override fun getLayoutResId(): Int {
        return R.layout.selectdir_path_rename_fragment
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        activity?.let {
            mActivity = it as? BaseVMActivity
            val bundle = arguments ?: return
            fileName = bundle.getString(KtConstants.FILE_NAME)
            fileExts = bundle.getStringArrayList(KtConstants.FILE_EXT)
            initFillNameMap(bundle)
            selectPosition = bundle.getInt(SELECT_POSITION, 0)
        }
    }

    /**
     * 获取从文件随心开中传入的填充名称列表，放入Map中
     */
    private fun initFillNameMap(bundle: Bundle) {
        val fillNameList = bundle.getStringArrayList(SELECT_FILE_TYPE_FILL_NAME_LIST)
        Log.d(TAG, "initFillNameMap:${fillNameList?.size}")
        fillNameList?.forEach { fileNameData ->
            Log.d(TAG, "initFillNameMap forEach:${fileNameData}")
            if (fileNameFillMap == null) {
                fileNameFillMap = HashMap()
            }
            val nameList = fileNameData.split(SPLIT_CHAR)
            if (nameList.size > 1) {
                val key = nameList[0]
                val value = nameList[1]
                if (key.isNotEmpty() && value.isNotEmpty()) {
                    fileNameFillMap?.put(key, value)
                }
            }
        }
    }

    override fun getRecyclerViewBottomPadding(): Int = MyApplication.appContext.resources.run {
        val extraMargin = getDimensionPixelOffset(com.filemanager.common.R.dimen.select_path_recycler_view_margin_bottom)
        return if (mViewModel?.isCurrentDfmRootPath() == true) {
            extraMargin
        } else {
            val minHeight = getDimensionPixelOffset(com.filemanager.common.R.dimen.operation_btn_background_height_rename)
            val measureWidth = bottomArea?.measuredHeight ?: 0
            (if (measureWidth > 0) measureWidth else minHeight) + extraMargin
        }
    }

    private val illegalFilter = InputFilter { source, _, _, dest, dstart, dend ->
        if (EmojiUtils.containsIllegalCharFileName(source)) {
            showInputError(RenameErrorTipUtil.getRenameErrorTips(RenameErrorTipUtil.ERROR_FILENAME_INPUT_ILLEGAL, resources))
            if (dest != null) {
                return@InputFilter dest.subSequence(dstart, dend)
            }
        } else if (EmojiUtils.isContainEmoji(source)) {
            showInputError(RenameErrorTipUtil.getRenameErrorTips(RenameErrorTipUtil.ERROR_FILENAME_ILLEGAL, resources))
            if (dest != null) {
                return@InputFilter dest.subSequence(dstart, dend)
            }
        }
        source
    }

    private fun addInputFilter(editText: EditText, inputFilter: InputFilter) {
        val filters: Array<InputFilter> = editText.filters
        var tmpFilter: Array<InputFilter?>? = null
        var length = 0
        if (filters == null) {
            tmpFilter = arrayOfNulls(1)
        } else {
            length = filters.size
            tmpFilter = arrayOfNulls(length + 1)
            System.arraycopy(filters, 0, tmpFilter, 0, length)
        }
        tmpFilter[length] = inputFilter
        editText.filters = tmpFilter
    }

    private val nameTextChangeWatcher = object : TextWatcher {
        override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
            if (count > 0) {
                val charsLen = s.toString().length
                val bytesLen = s.toString().toByteArray(StandardCharsets.UTF_8).size
                Log.d(TAG, "onTextChanged charLen = $charsLen, bytesLen = $bytesLen initFileNameLen $initFileNameLen")
                handleInputLimit(charsLen, bytesLen, initFileNameLen) {
                    val inputCharLen = nameEdit?.text?.length ?: 0
                    if (((start + count) <= inputCharLen) && (count >= before)) {
                        Log.d(TAG, "onTextChanged start = $start, before = $before, count = $count")
                        if (start == 0 && before == 0 && count > CommonConstants.CUSTOM_NAME_LEN) {
                            nameEdit?.text?.delete(CommonConstants.CUSTOM_NAME_LEN, count)
                            initFileNameLen = CommonConstants.CUSTOM_NAME_LEN
                        } else {
                            nameEdit?.text?.delete(start + before, start + count)
                        }
                    }
                }
                mSelectButton?.isEnabled = true
            } else {
                if (s.isBlank()) {
                    mSelectButton?.isEnabled = false
                }
            }
        }

        override fun afterTextChanged(editable: Editable?) {
        }
    }

    private fun handleInputLimit(charsLen: Int, bytesLen: Int, initCharsLen: Int, limitOperate: () -> Unit) {
        val charLenExceedLimit = (initCharsLen <= CommonConstants.CUSTOM_NAME_LEN) && (charsLen > CommonConstants.CUSTOM_NAME_LEN)
        val charByteLenExceedLimit = (initCharsLen >= CommonConstants.CUSTOM_NAME_LEN) && (bytesLen > CommonConstants.NAME_BYTES_LEN)
        Log.d(TAG, "charLenExceedLimit=$charLenExceedLimit  charByteLenExceedLimit=$charByteLenExceedLimit")
        if (charLenExceedLimit || charByteLenExceedLimit) {
            limitOperate()
            showInputError(RenameErrorTipUtil.getRenameErrorTips(RenameErrorTipUtil.ERROR_FILE_NAME_TOO_LONG, resources))
        }
    }

    private fun showInputError(msg: String) {
        couiCardInputView?.post { couiCardInputView?.showError(msg) }
    }

    override fun initContentView(view: View) {
        super.initContentView(view)
        couiCardInputView = view.findViewById(R.id.coui_card_input_view)
        inputErrorView = couiCardInputView?.findViewById(R.id.text_input_error)
        nameEdit = couiCardInputView?.editText
        nameExt = view.findViewById(R.id.name_ext)
        nameFrame = view.findViewById(R.id.name_ext_frame)
        imgExt = view.findViewById(R.id.ext_image)
        mPathBar?.setGradientColor(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackgroundElevated))
        initFileExts()
        nameEdit?.let {
            addInputFilter(it, illegalFilter)
            it.addTextChangedListener(nameTextChangeWatcher)
            val fillName = fileNameFillMap?.get(nameExt?.text)
            setFileNameToNameEdit(fillName)
            if (initFileNameLen == 0) {
                initFileNameLen = fileName?.length ?: 0
            }
            it.isFastDeletable = true
        }
        initSelectButton(view)
        bottomArea = view.findViewById(R.id.bottom_area)
        bottomArea?.let {
            it.setOnClickListener(null)
        }
        initToolBar(view)
    }

    private fun initSelectButton(view: View) {
        mSelectButton = view.findViewById<COUIButton>(R.id.select_button)?.apply {
            verticalButtonWrap = SingleButtonWrap(this, SingleButtonWrap.Type.Large)
            text = arguments?.getString(PARAMETER_BTN_CONFIRM) ?: context?.getString(com.filemanager.common.R.string.save_file)
            setOnClickListener {
                val dir = mViewModel?.mPositionModel?.value?.mCurrentPath
                val name = nameEdit?.text?.toString() + nameExt?.text
                val file = File(dir + File.separator + name)
                if (JavaFileHelper.safeCheck({ checkNewFileExists(file) }, false)) {
                    warningFileExits(dir, name)
                    return@setOnClickListener
                }
                mSelectPathDialogListener?.onSelect(mActionCode, listOf(dir + File.separator + name))
                mSelectPathDialogInterface?.performDismiss()
            }
        }
    }

    private fun warningFileExits(dir: String?, name: String) {
        val warningFileExits = arguments?.getBoolean(PARAMETER_IS_OVERRIDE, false) ?: false
        if (warningFileExits) {
            showWarningDialog(dir, name)
        } else {
            showInputError(getString(com.filemanager.common.R.string.toast_file_exist))
        }
    }

    private fun showWarningDialog(dir: String?, name: String) {
        val clickListener = DialogInterface.OnClickListener { dialog, which ->
            when (which) {
                DialogInterface.BUTTON_NEUTRAL -> {
                    mSelectPathDialogListener?.onSelect(mActionCode, listOf(dir + File.separator + name))
                    mSelectPathDialogInterface?.performDismiss()
                }
                else -> dialog.cancel()
            }
        }
        waringFileExistsDialog = context?.let {
            COUIAlertDialogBuilder(it)
                .setCancelable(false)
                .setTitle(com.filemanager.common.R.string.save_file_warning_exists_dialog_title)
                .setNeutralButton(com.filemanager.common.R.string.replace_yes, clickListener)
                .setNegativeButton(com.filemanager.common.R.string.alert_dialog_cancel, clickListener)
                .show()
        }
    }


    private fun initToolBar(view: View) {
        val panelTitle = arguments?.getString(PARAMETER_PANEL_TITLE)
        toolbar = view.findViewById<COUIToolbar>(R.id.rename_fragment_toolbar)?.apply {
            title = panelTitle ?: getString(com.filemanager.common.R.string.save_as)
            isTitleCenterStyle = true
            inflateMenu(R.menu.selectdir_close_option)
            if (hasStoragePermission.not()) {
                menu.findItem(R.id.action_new_folder).isEnabled = false
            }
            setOnMenuItemClickListener(this@SelectDirPathRenamePanelFragment)
            menu.findItem(R.id.action_new_folder).icon = null // 隐藏icon只显示文字
        }
    }

    private fun initFileExts() {
        fileExts?.let { exts ->
            if (exts.size > selectPosition) {
                nameExt?.text = exts[selectPosition]
            } else if (exts.size > 0) {
                nameExt?.text = exts[0]
            }
            if (exts.size <= 1) {
                imgExt?.visibility = View.GONE
                //只有一个或不足一个，无法点击
                return
            }
            imgExt?.visibility = View.VISIBLE
            nameFrame?.setOnClickListener {
                val listItems: MutableList<PopupListItem> = ArrayList()
                val builder = PopupListItem.Builder()
                for (i in exts.indices) {
                    val listItem = builder.reset()
                        .setTitle(exts[i])
                        .setIsEnable(true)
                        .setIsChecked(selectPosition == i)
                        .build()
                    listItems.add(listItem)
                }
                COUIPopupListWindow(activity).apply {
                    imgExt?.startExpandAnimation()
                    itemList = listItems
                    listView.choiceMode = ListView.CHOICE_MODE_SINGLE
                    this.inputMethodMode = PopupWindow.INPUT_METHOD_NOT_NEEDED
                    this.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_STATE_UNCHANGED
                    setDismissTouchOutside(true)
                    setOnDismissListener {
                        imgExt?.startCollapseAnimation()
                    }
                    setOnItemClickListener { _, _, i, _ ->
                        if (listItems[i].isEnable) {
                            nameExt?.text = listItems[i].title
                            selectPosition = i
                            changeFileNameIfNeed(listItems[i].title)
                            arguments?.putInt(SELECT_POSITION, selectPosition)
                            dismiss()
                        }
                    }
                    show(it, true)
                }
            }
        }
    }

    private fun changeFileNameIfNeed(type: String) {
        fileNameFillMap?:return
        if (selectPosition == oldSelectPosition) {
            //未变更，不处理
            return
        }
        val fillName = fileNameFillMap?.get(type)
        if (oldFillName == fillName) {
            //填充名之间没有切换，不处理
            return
        }
        //已经修改过的文件名，不再根据填充名称刷新文件名
        val currentName = nameEdit?.text?.toString()
        if (currentName == fileName || currentName == getNewName(fillName ?: oldFillName)) {
            setFileNameToNameEdit(fillName)
        }
        oldSelectPosition = selectPosition
        oldFillName = fillName
    }

    private fun setFileNameToNameEdit(fillName: String?) {
        Log.d(TAG, "setFileNameToNameEdit fill:$fillName  old:$oldFillName")
        if (fillName?.isNotEmpty() == true) {
            nameEdit?.setText(getNewName(fillName))
        } else {
            nameEdit?.setText(fileName)
        }
        oldFillName = fillName
    }

    private fun getNewName(fillName: String?): String {
        return "$fileName$FILE_NAME_CONNECTOR$fillName"
    }

    override fun onMenuItemClick(item: MenuItem?): Boolean {
        if ((null == item) || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        when (item.itemId) {
            R.id.action_new_folder -> createFolder()

            R.id.action_close -> mSelectPathDialogInterface?.performCancel(SELECT_DIALOG_FRAGMENT_TAG)

            else -> Log.d(TAG, "call onMenuItemSelected with unknown item")
        }
        return true
    }

    fun checkNewFileExists(file: File): Boolean {
        return file.exists()
    }

    override fun onDestroyView() {
        if (onGlobalLayoutListener != null) {
            rootView?.rootView?.viewTreeObserver?.removeOnGlobalLayoutListener(onGlobalLayoutListener)
        }
        verticalButtonWrap?.release()
        super.onDestroyView()
        mPathBar?.setOnPathClickListener(null)
    }

    @SuppressLint("VisibleForTests")
    override fun initPathBar(currentPath: String?) {
        mPathBar?.let {
            if (!TextUtils.isEmpty(currentPath)) {
                mViewModel?.initPathHelper(currentPath!!)
                it.setPathHelper(mViewModel?.mPathHelp)
                it.setOnPathClickListener(object : BrowserPathBar.OnPathClickListener {
                    override fun onPathClick(index: Int, path: String?) {
                        mViewModel?.clickPathBar(index)
                    }
                }).show()
                it.setCurrentPath(currentPath!!)
            }
        }
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)
            ?.setPanelBackgroundTintColor(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBackgroundElevated))
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)?.findViewById<View>(
            com.support.panel.R.id.coui_panel_content_layout
        )?.let {
            // 界面动态布局会导致背景色和控件颜色不一致
            it.background = bottomArea?.background
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        verticalButtonWrap?.onConfigurationChanged(newConfig)
    }

    override fun onUIConfigChanged(dialog: COUIBottomSheetDialog?) {
        if (!isAdded) {
            return
        }
        updateSmallLandLayout(dialog)
        //解决中屏时输入法挡住button
        if (UIConfigMonitor.getWindowType() == UIConfig.WindowType.MEDIUM) {
            setButtonMarin(false)
        }
    }

    /**
     * 小屏横竖屏配置界面
     */
    private fun updateSmallLandLayout(dialog: COUIBottomSheetDialog?) {
        Log.d(TAG, "onUIConfigChanged changeUI")
        val isSmallLand = WindowUtils.isLandscape(context) and !WindowUtils.isMiddleAndLargeScreen(context) // 小屏横屏
        hideDialogTop(dialog, isSmallLand)
        setButtonMarin(isSmallLand)
        setIntPutViewMargin(isSmallLand)
        rootView?.let {
            it.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                height = ViewGroup.MarginLayoutParams.MATCH_PARENT
                width = ViewGroup.MarginLayoutParams.MATCH_PARENT // 重新刷新宽高
            }
        }
        dialog?.findViewById<View>(com.support.panel.R.id.coui_panel_content_layout)?.let {
            // 界面动态布局会导致背景色和控件颜色不一致
            it.background = bottomArea?.background
        }
    }

    private fun hideDialogTop(dialog: COUIBottomSheetDialog?, isSmallLand: Boolean) {
        dialog?.findViewById<View>(com.support.panel.R.id.drag_img)?.let {
            // 横屏时高度不够，隐藏顶部空白image
            it.visibility = when {
                isSmallLand -> View.GONE
                else -> View.VISIBLE
            }
        }
        dialog?.findViewById<View>(com.support.panel.R.id.coui_panel_content_layout)?.let {
            // 界面动态布局会导致背景色和控件颜色不一致
            it.background = bottomArea?.background
        }
    }

    private fun setButtonMarin(isSmallLand: Boolean) {
        mSelectButton?.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            bottomMargin = when {
                isSmallLand -> resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp)
                //适配中屏按钮距离底部边距，避免被输入法弹出时挡住
                UIConfigMonitor.getWindowType() == UIConfig.WindowType.MEDIUM -> {
                    resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_48dp)
                }
                else -> resources.getDimensionPixelSize(com.filemanager.common.R.dimen.operation_btn_margin_bottom)
            }
        }
    }

    private fun setIntPutViewMargin(isSmallLand: Boolean) = couiCardInputView?.apply {
        updateLayoutParams<ViewGroup.MarginLayoutParams> {
            topMargin = when {
                isSmallLand -> com.filemanager.common.R.dimen.dimen_8dp
                else -> com.filemanager.common.R.dimen.dimen_16dp
            }.let(resources::getDimensionPixelSize)
            // 分屏模式竖屏时高度不够，减小间距
            bottomMargin = when {
                isSmallLand -> com.filemanager.common.R.dimen.dimen_16dp
                (context as Activity).isInMultiWindowMode -> com.filemanager.common.R.dimen.dimen_16dp
                else -> com.filemanager.common.R.dimen.dimen_38dp
            }.let(resources::getDimensionPixelSize)
        }
        editText?.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            height = when {
                isSmallLand -> resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_36dp)
                else -> ViewGroup.MarginLayoutParams.WRAP_CONTENT
            }
        }
        setEditVerticalPadding(!isSmallLand)
        if (isSmallLand) {
            // 竖屏切横屏时重置输入法弹出方式，保证横屏的弹出方式不受竖屏影响
            editText?.let {
                val inputManager =
                    activity?.getSystemService(INPUT_METHOD_SERVICE) as? InputMethodManager
                inputManager?.restartInput(it)
            }
        }
    }
}