/***********************************************************
 * * Copyright (C), 2010 - 2025 Oplus. All rights reserved..
 * * File: PrivacyPolicyUtils
 * * Description: PrivacyPolicyUtils
 * * Version: 1.0
 * * Date : 2025/4/22
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2025/4/22       1.0         PrivacyPolicyUtils
 ****************************************************************/
package com.filemanager.setting.ui

import android.app.Activity
import android.app.Dialog
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.os.Build
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.WindowManager
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.statement.COUIStatementClickableSpan
import com.coui.appcompat.statusbar.COUIStatusbarTintUtil
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.KtConstants.ACTION_USER_INFO
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.setting.ui.privacy.SettingPrivacyFragment.Companion.KEY_PAGE_TYPE
import com.filemanager.setting.ui.privacy.SettingPrivacyFragment.Companion.TYPE_PRIVACY_INFO
import com.filemanager.setting.utils.FlexibleWindowUtil
import java.util.Locale

object PrivacyPolicyUtils {
    private const val TAG = "PrivacyPolicyUtils"
    @JvmStatic
    fun addUsageInstructionsPrivacyPolicyLinkSpan(activity: Activity, id: Int): SpannableStringBuilder {
        val resources = activity.resources
        val statementString: String
        val linkString2 = resources.getString(com.filemanager.common.R.string.personal_information_protection_policy)
        statementString = String.format(
            Locale.getDefault(),
            resources.getString(id),
            linkString2
        )

        val spannableString = SpannableStringBuilder(statementString)
        val startIndex = statementString.indexOf(linkString2)
        if (startIndex > 0) {
            val endIndex = startIndex + linkString2.length
            spannableString.setSpan(object : COUIStatementClickableSpan(activity) {
                override fun onClick(widget: View) {
                    activity?.let {
                        if (FeatureCompat.sIsExpRom) {
                            PermissionUtils.openPrivacyPolicy(it)
                        } else {
                            startPrivacyActivity(it, TYPE_PRIVACY_INFO)
                        }
                    }
                    widget.clearFocus()
                    widget.isPressed = false
                    widget.isSelected = false
                }
            }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
            spannableString.setSpan(
                ForegroundColorSpan(activity.getColor(com.filemanager.common.R.color.text_color_black_alpha_60)),
                0, startIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }
        return spannableString
    }

    @JvmStatic
    private fun startPrivacyActivity(activity: Activity, type: Int) {
        try {
            val intent = Intent(ACTION_USER_INFO)
            intent.setPackage(activity.packageName)
            intent.putExtra(KEY_PAGE_TYPE, type)
            FlexibleWindowUtil.startActivityForFlexibleWindow(activity, intent)
        } catch (e: ActivityNotFoundException) {
            Log.d(TAG, "start: ${e.message}")
        }
    }

    @JvmStatic
    fun setDialogStatusBarTransparentAndBlackFont(context: Context, dialog: Dialog, isFullScreen: Boolean) {
        if (dialog.window != null) {
            val window = dialog.window
            val decorView = window?.decorView
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && isFullScreen) {
                decorView?.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            }
            var flag = decorView?.systemUiVisibility
            val versionCode = ViewHelper.getRomVersionCode()
            val white = false//context.resources.getBoolean(R.bool.is_status_white)
            if (versionCode >= ViewHelper.COLOR_OS_3_0 || versionCode == 0) {
                window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                if (COUIDarkModeUtil.isNightMode(dialog.context)) {
                    flag = flag?.and(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv())
                    flag = flag?.and(View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR.inv())
                } else {
                    flag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        if (!white) {
                            flag?.or(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR)
                        } else {
                            flag?.or(View.SYSTEM_UI_FLAG_LAYOUT_STABLE)
                        }
                    } else {
                        flag?.or(COUIStatusbarTintUtil.SYSTEM_UI_FLAG_OP_STATUS_BAR_TINT)
                    }
                }
                flag?.let {
                    decorView?.systemUiVisibility = it
                }
            }
        }
    }
}