/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LabelCardDataPackerTest
 ** Description : LabelCardDataPackerTest
 ** Version     : 1.0
 ** Date        : 2024/05/28 15:05
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei        2024/05/28       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.label.packer

import android.content.Context
import android.view.View
import com.oplus.filemanager.cardwidget.label.data.LabelCardData
import com.oplus.filemanager.cardwidget.label.packer.LabelCardDataPacker.Companion.ITEM_ICON_1
import com.oplus.filemanager.cardwidget.label.packer.LabelCardDataPacker.Companion.ITEM_ICON_2
import com.oplus.filemanager.cardwidget.label.packer.LabelCardDataPacker.Companion.ITEM_ICON_3
import com.oplus.filemanager.cardwidget.label.packer.LabelCardDataPacker.Companion.ITEM_ICON_4
import com.oplus.filemanager.cardwidget.label.packer.LabelCardDataPacker.Companion.ITEM_NAME_1
import com.oplus.filemanager.cardwidget.label.packer.LabelCardDataPacker.Companion.ITEM_NAME_2
import com.oplus.filemanager.cardwidget.label.packer.LabelCardDataPacker.Companion.ITEM_NAME_3
import com.oplus.filemanager.cardwidget.label.packer.LabelCardDataPacker.Companion.ITEM_NAME_4
import com.oplus.smartenginehelper.dsl.DSLCoder
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.Test

class LabelCardDataPackerTest {
    private val context: Context = mockk()
    private val labelCard: LabelCardData = mockk()
    private val coder: DSLCoder = mockk()

    @Test
    fun testDisplayVisibility_fileCount() {
        //given
        val labelCardDataPacker = LabelCardDataPacker(context, labelCard)
        every { labelCard.fileCount } returns 1
        every { coder.setVisibility(any(), any()) } just runs

        //when
        labelCardDataPacker.displayVisibility(coder)

        //then
        verify { coder.setVisibility(ITEM_NAME_4, View.INVISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_4, View.INVISIBLE) }
        verify { coder.setVisibility(ITEM_NAME_3, View.INVISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_3, View.INVISIBLE) }
        verify { coder.setVisibility(ITEM_NAME_2, View.INVISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_2, View.INVISIBLE) }
        verify { coder.setVisibility(ITEM_NAME_1, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_1, View.VISIBLE) }

        //given
        every { labelCard.fileCount } returns 2
        labelCardDataPacker.displayVisibility(coder)
        //then
        verify { coder.setVisibility(ITEM_NAME_4, View.INVISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_4, View.INVISIBLE) }
        verify { coder.setVisibility(ITEM_NAME_3, View.INVISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_3, View.INVISIBLE) }
        verify { coder.setVisibility(ITEM_NAME_2, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_2, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_NAME_1, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_1, View.VISIBLE) }

        //given
        every { labelCard.fileCount } returns 3
        labelCardDataPacker.displayVisibility(coder)
        //then
        verify { coder.setVisibility(ITEM_NAME_4, View.INVISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_4, View.INVISIBLE) }
        verify { coder.setVisibility(ITEM_NAME_3, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_3, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_NAME_2, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_2, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_NAME_1, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_1, View.VISIBLE) }

        //given
        every { labelCard.fileCount } returns 4
        labelCardDataPacker.displayVisibility(coder)
        //then
        verify { coder.setVisibility(ITEM_NAME_4, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_4, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_NAME_3, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_3, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_NAME_2, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_2, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_NAME_1, View.VISIBLE) }
        verify { coder.setVisibility(ITEM_ICON_1, View.VISIBLE) }
    }
}