<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools" >

    <application
        android:name="com.oplus.filemanager.FileManagerApplication"
        android:allowBackup="false"
        android:enableOnBackInvokedCallback="true"
        android:icon="@drawable/ic_launcher_filemanager"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:requestRawExternalStorageAccess="true"
        android:resizeableActivity="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme" >
        <activity
            android:name="com.oplus.filemanager.main.ui.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize|keyboard|navigation"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.Splash"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan"
            tools:node="remove" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:screenOrientation="behind"
            android:name="com.oplus.filemanager.main.ui.SplashActivity"
            android:exported="true"
            android:theme="@style/AppNoTitleTheme.Splash" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.oplus.filemanager.main.ui.ExportMainActivity"
            android:configChanges="orientation|keyboardHidden|screenLayout|screenSize|smallestScreenSize|keyboard|navigation"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.Splash"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustPan" >
            <intent-filter>
                <action android:name="ACTION_FILEMANAGER_BROWSE_CHECK_PASSWORD" />
                <action android:name="oppo.intent.action.OPEN_FILEMANAGER" />
                <action android:name="oppo.intent.action.ACTION_FILEMANAGER_SAVE_MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.OPEN_FILEMANAGER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.DRIVE_DOWNLOAD" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.QUICK_DOCUMENT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="oplus.intent.action.filemanager.superApp" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <meta-data
                android:name="oldComponentName"
                android:resource="@array/downLoadListOldComponentName" />
            <meta-data
                android:name="oldComponentNameVersion"
                android:value="1" />
            <!--add for shortcuts in desktop-->
            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/desktop_shortcuts_export" />
        </activity>
    </application>

</manifest>