/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: CollectCountUtils
 * * Description: 收集次数的工具类：显示xx次，xx个
 * * Version: 1.0
 * * Date : 2024/08/22
 * * Author:W9060445
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     W9060445        2024/08/22      1.0            create
 ****************************************************************/
package com.filemanager.setting.utils

import android.content.Context

object CollectCountUtils {

    /**
     * 根据传入的类型和次数，返回xx次，xx个，xx条，xx张
     * @param context
     * @param category 类型
     * @param count 次数
     *
     */
    @JvmStatic
    fun getCountStr(context: Context, category: String, count: Int): String {
        val pluralsRes = if (PersonalPrivacyCategory.isCountUnitPiece(category)) {
            com.filemanager.common.R.plurals.collect_count_piece
        } else if (PersonalPrivacyCategory.isCountUnitItem(category)) {
            com.filemanager.common.R.plurals.collect_count_item
        } else if (PersonalPrivacyCategory.isCountUnitLeaf(category)) {
            com.filemanager.common.R.plurals.collect_count_leaf
        } else {
            com.filemanager.common.R.plurals.collect_count_times
        }
        return context.resources.getQuantityString(pluralsRes, count, count)
    }
}