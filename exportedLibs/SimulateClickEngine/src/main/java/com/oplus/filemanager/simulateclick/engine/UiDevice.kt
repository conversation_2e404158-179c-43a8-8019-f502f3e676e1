/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - UiDevice.kt
 * Description:
 *     The utils to open app in device and get device info
 *
 * Version: 2.0
 * Date: 2024-05-24
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-05-24   2.0    Porting from LLMSimulateClick
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.view.Surface
import com.oplus.filemanager.simulateclick.engine.utils.ScreenStatusUtils
import com.oplus.filemanager.simulateclick.engine.value.ScreenDisplayFlag
import com.oplus.filemanager.simulateclick.engine.value.ScreenOrientationFlag
import com.oplus.filemanager.simulateclick.engine.value.ScreenRotationFlag
import com.oplus.filemanager.simulateclick.engine.utils.Log
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.jetbrains.annotations.VisibleForTesting

internal object UiDevice {
    private const val TAG = "UiDevice"

    @JvmStatic
    fun openTargetApp(
        context: Context,
        packageName: String,
        deeplink: String,
        openAgainDelay: Long?
    ): Unit = runBlocking {
        if (!isPackageExisted(context, packageName)) {
            Log.e(TAG, "openTargetApp: ERROR! package($packageName) is not exist")
            return@runBlocking
        }
        Log.d(TAG, "openTargetApp: packageName=$packageName")
        if (deeplink.isNotEmpty()) {
            val dpResult = openByDeepLink(context, deeplink)
            if (dpResult) {
                return@runBlocking
            }
        }
        val intent = context.packageManager
            .getLaunchIntentForPackage(packageName) ?: return@runBlocking
        val againIntent = constructIntent(intent)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
        runCatching {
            context.startActivity(intent)
        }.onFailure {
            Log.e(TAG, "openTargetApp: ERROR! $it")
        }
        if (openAgainDelay == null || openAgainDelay <= 0) {
            return@runBlocking
        }
        delay(openAgainDelay)
        Log.d(TAG, "openTargetApp: again, packageName=$packageName")
        runCatching {
            context.startActivity(againIntent)
        }.onFailure {
            Log.e(TAG, "openTargetApp: open again ERROR! $it")
        }
    }

    @VisibleForTesting
    @JvmStatic
    internal fun constructIntent(intent: Intent): Intent = Intent(intent)

    @VisibleForTesting
    @JvmStatic
    internal fun openByDeepLink(context: Context, deepLink: String): Boolean {
        var isIntentExisted = false
        runCatching {
            val uri = Uri.parse(deepLink)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            isIntentExisted = isIntentExisted(context, intent)
            if (isIntentExisted) {
                context.startActivity(intent)
                isIntentExisted = true
            }
        }.onFailure {
            isIntentExisted = false
            Log.e(TAG, "openByDeepLink: ERROR! $it")
        }
        return isIntentExisted
    }

    @VisibleForTesting
    @JvmStatic
    internal fun isPackageExisted(context: Context, packageName: String): Boolean {
        if (packageName.isEmpty()) {
            return false
        }
        var isExist = false
        runCatching {
            val packageInfo = context.packageManager
                .getPackageInfo(packageName, PackageManager.GET_ACTIVITIES)
            if (packageInfo?.applicationInfo != null) {
                isExist = true
            }
        }.onFailure {
            Log.w(TAG, "isPackageExisted: ERROR! $it")
        }
        return isExist
    }

    @JvmStatic
    private fun isIntentExisted(context: Context, intent: Intent): Boolean {
        val resolveInfo = runCatching {
            context.packageManager
                .queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
        }.onFailure {
            Log.w(TAG, "isIntentExisted: ERROR! $it")
        }.getOrNull()
        return resolveInfo?.isNotEmpty() ?: false
    }

    @JvmStatic
    fun getAppVersionCode(context: Context, packageName: String): Long? {
        if (packageName.isEmpty()) {
            return null
        }
        return kotlin.runCatching {
            context.packageManager
                .getPackageInfo(packageName, 0)
                .longVersionCode
        }.onFailure {
            if (it !is PackageManager.NameNotFoundException) {
                Log.e(TAG, "getAppVersionCode: ERROR! $it")
            }
        }.getOrNull()
    }

    @JvmStatic
    fun getCurrentScreenDisplay(context: Context): ScreenDisplayFlag = when {
        ScreenStatusUtils.isFocusSecondaryDisplay(context) -> ScreenDisplayFlag.FLIP
        ScreenStatusUtils.isTablet(context) -> ScreenDisplayFlag.PAD
        ScreenStatusUtils.isUnfold(context) -> ScreenDisplayFlag.UNFOLD
        else -> ScreenDisplayFlag.NORMAL
    }.let { ScreenDisplayFlag(it) }

    @JvmStatic
    fun getCurrentScreenOrientation(context: Context): ScreenOrientationFlag {
        val isLandscape = ScreenStatusUtils.isLandscape(context)
        return ScreenOrientationFlag(
            if (isLandscape) ScreenOrientationFlag.LANDSCAPE else ScreenOrientationFlag.PORTRAIT
        )
    }

    @JvmStatic
    fun getCurrentScreenRotation(context: Context): ScreenRotationFlag =
        when (ScreenStatusUtils.getScreenRotation(context)) {
            Surface.ROTATION_0 -> ScreenRotationFlag.ROTATION_0
            Surface.ROTATION_90 -> ScreenRotationFlag.ROTATION_90
            Surface.ROTATION_180 -> ScreenRotationFlag.ROTATION_180
            Surface.ROTATION_270 -> ScreenRotationFlag.ROTATION_270
            else -> ScreenRotationFlag.ROTATION_ALL
        }.let { ScreenRotationFlag(it) }
}