/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BaseScanner
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 19:21
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/21       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist.scanner

import com.oplus.fileservice.bean.WebScannerResult

abstract class BaseScanner {
    /**
     * scan files
     * @return may be null
     */
    abstract fun scannerFiles(): WebScannerResult?

    /**
     * destroy Scanner
     */
    abstract fun destroy()
}