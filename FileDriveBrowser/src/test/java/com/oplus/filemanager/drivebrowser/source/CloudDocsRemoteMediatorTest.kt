/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudDocsRemoteMediatorTest
 ** Description : CloudDocsRemoteMediator Unit Test
 ** Version     : 1.0
 ** Date        : 2024/01/09 10:55
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/01/09       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.source

import android.content.Context
import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadState
import androidx.paging.LoadType
import androidx.paging.PagingConfig
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.drivebrowser.data.model.BaseResponse
import com.oplus.filemanager.drivebrowser.data.repository.kdocs.KDocsFileDriveRepository
import com.oplus.filemanager.drivebrowser.data.repository.tencent.TencentFileDriveRepository
import com.oplus.filemanager.drivebrowser.domain.model.CloudDocumentItem
import com.oplus.filemanager.drivebrowser.domain.model.DocumentPageData
import com.oplus.filemanager.drivebrowser.domain.repository.FileDriveRepository
import com.oplus.filemanager.drivebrowser.utils.FileDriveStateUtils
import com.oplus.filemanager.drivebrowser.utils.ServerException
import com.oplus.filemanager.provider.DriveFileDBHelper
import com.oplus.filemanager.room.model.DriveFileEntity
import io.mockk.coEvery
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import retrofit2.HttpException
import retrofit2.Response
import java.io.IOException
import kotlin.time.Duration.Companion.seconds

@OptIn(kotlinx.coroutines.ExperimentalCoroutinesApi::class)
class CloudDocsRemoteMediatorTest {

    private lateinit var context: Context
    private lateinit var networkService: FileDriveRepository
    private val dispatcher = StandardTestDispatcher()
    private lateinit var remoteMediator: CloudDocsRemoteMediator
    private lateinit var query: CloudDocsQuery

    @Before
    fun setup() {
        context = mockk()
        mockkObject(MyApplication)
        every { appContext }.returns(context)
        every { context.applicationContext }.returns(context)
        query = CloudDocsQuery("tencent", "/", "name", 1)
        networkService = mockk<TencentFileDriveRepository>()
        remoteMediator = CloudDocsRemoteMediator(query, networkService)
        mockkStatic(FileDriveStateUtils::class)
        mockkStatic(DriveFileDBHelper::class)
        mockkStatic(StatisticsUtils::class)
        justRun { StatisticsUtils.onCommon(any(), any(), any<Map<String, String>>()) }
        Dispatchers.setMain(dispatcher)
    }

    @After
    fun teardown() {
        unmockkStatic(FileDriveStateUtils::class)
        unmockkStatic(DriveFileDBHelper::class)
        unmockkStatic(StatisticsUtils::class)
        Dispatchers.resetMain()
    }

    @Test
    fun should_return_boolean_when_isServerExceptionState() {
        val loadState = LoadState.NotLoading(true)
        Assert.assertFalse(CloudDocsRemoteMediator.isServerExceptionState(loadState))

        var errorState = LoadState.Error(NullPointerException())
        Assert.assertFalse(CloudDocsRemoteMediator.isServerExceptionState(errorState))

        errorState = LoadState.Error(ServerException(-1, "Test error"))
        Assert.assertTrue(CloudDocsRemoteMediator.isServerExceptionState(errorState))

        errorState = LoadState.Error(IOException())
        Assert.assertTrue(CloudDocsRemoteMediator.isServerExceptionState(errorState))
    }

    @OptIn(ExperimentalPagingApi::class)
    @Test
    fun should_return_Success_when_load() {
        runTest(timeout = testCaseTimeout) {
            val state = mockk<PagingState<Int, DriveFileEntity>>()
            every { state.lastItemOrNull() }.returns(null)
            every { state.config }.returns(PagingConfig(10, 10))
            every { state.isEmpty() }.returns(false)
            var result = remoteMediator.load(LoadType.PREPEND, state)
            Assert.assertTrue(result is RemoteMediator.MediatorResult.Success)
            if (result is RemoteMediator.MediatorResult.Success) {
                Assert.assertTrue(result.endOfPaginationReached)
            }

            result = remoteMediator.load(LoadType.APPEND, state)
            Assert.assertTrue(result is RemoteMediator.MediatorResult.Success)
            if (result is RemoteMediator.MediatorResult.Success) {
                Assert.assertTrue(result.endOfPaginationReached)
            }
        }
    }

    @OptIn(ExperimentalPagingApi::class)
    @Test
    fun should_return_request_network_when_load_refresh() {
        runTest(timeout = testCaseTimeout) {
            justRun { FileDriveStateUtils.saveLastRefreshTime(any(), any()) }
            justRun { DriveFileDBHelper.deleteDocuments(any(), any()) }
            val state = mockk<PagingState<Int, DriveFileEntity>>()
            every { state.pages }.returns(emptyList())
            every { state.isEmpty() }.returns(true)
            every { state.config }.returns(PagingConfig(10, 10))
            val resp = DocumentPageData()
            resp.lists = emptyList()
            resp.nextPageOffset = 20
            resp.isLastPage = false
            val baseResp = BaseResponse<DocumentPageData>()
            baseResp.data = resp
            baseResp.codeMsg(0, "")
            coEvery { networkService.getDocumentLists(any(), any(), any(), any(), any()) }.returns(baseResp)
            var result = remoteMediator.load(LoadType.REFRESH, state)
            Assert.assertTrue(result is RemoteMediator.MediatorResult.Success)
            if (result is RemoteMediator.MediatorResult.Success) {
                Assert.assertFalse(result.endOfPaginationReached)
            }

            resp.isLastPage = true
            result = remoteMediator.load(LoadType.REFRESH, state)
            Assert.assertTrue(result is RemoteMediator.MediatorResult.Success)
            if (result is RemoteMediator.MediatorResult.Success) {
                Assert.assertTrue(result.endOfPaginationReached)
            }
        }
    }

    @OptIn(ExperimentalPagingApi::class)
    @Test
    fun should_return_request_network_when_load_APPEND() {
        runTest(timeout = testCaseTimeout) {
            justRun { FileDriveStateUtils.saveLastRefreshTime(any(), any()) }
            val state = mockk<PagingState<Int, DriveFileEntity>>()
            every { state.lastItemOrNull() }.returns(DriveFileEntity(10))
            every { state.pages }.returns(emptyList())
            every { state.isEmpty() }.returns(true)
            every { state.config }.returns(PagingConfig(10, 10))
            val resp = DocumentPageData()
            resp.lists = emptyList()
            resp.nextPageOffset = 20
            resp.isLastPage = false
            val baseResp = BaseResponse<DocumentPageData>()
            baseResp.data = resp
            baseResp.codeMsg(0, "")
            coEvery { networkService.getDocumentLists(any(), any(), any(), any(), any()) }.returns(baseResp)
            var result = remoteMediator.load(LoadType.APPEND, state)
            Assert.assertTrue(result is RemoteMediator.MediatorResult.Success)
            if (result is RemoteMediator.MediatorResult.Success) {
                Assert.assertFalse(result.endOfPaginationReached)
            }

            resp.isLastPage = true
            result = remoteMediator.load(LoadType.APPEND, state)
            Assert.assertTrue(result is RemoteMediator.MediatorResult.Success)
            if (result is RemoteMediator.MediatorResult.Success) {
                Assert.assertTrue(result.endOfPaginationReached)
            }
        }
    }

    @OptIn(ExperimentalPagingApi::class)
    @Test
    fun should_return_error_when_load_APPEND_fail() {
        runTest(timeout = testCaseTimeout) {
            justRun { FileDriveStateUtils.saveLastRefreshTime(any(), any()) }
            val state = mockk<PagingState<Int, DriveFileEntity>>()
            every { state.lastItemOrNull() }.returns(DriveFileEntity(10))
            every { state.pages }.returns(emptyList())
            every { state.isEmpty() }.returns(true)
            every { state.config }.returns(PagingConfig(10, 10))
            val resp = DocumentPageData()
            resp.lists = emptyList()
            resp.nextPageOffset = 20
            resp.isLastPage = false
            val baseResp = BaseResponse<DocumentPageData>()
            baseResp.codeMsg(1001, "测试错误")
            coEvery { networkService.getDocumentLists(any(), any(), any(), any(), any()) }.returns(baseResp)
            var result = remoteMediator.load(LoadType.APPEND, state)
            Assert.assertTrue(result is RemoteMediator.MediatorResult.Error)
            if (result is RemoteMediator.MediatorResult.Error) {
                Assert.assertEquals(result.throwable.message, "测试错误")
            }

            baseResp.codeMsg(0, "")
            result = remoteMediator.load(LoadType.APPEND, state)
            Assert.assertTrue(result is RemoteMediator.MediatorResult.Error)
            if (result is RemoteMediator.MediatorResult.Error) {
                Assert.assertEquals(result.throwable.message, "no data")
            }
        }
    }

    @OptIn(ExperimentalPagingApi::class)
    @Test
    fun should_return_Error_when_load_refresh_Exception() {
        runTest(timeout = testCaseTimeout) {
            justRun { FileDriveStateUtils.saveLastRefreshTime(any(), any()) }
            val state = mockk<PagingState<Int, DriveFileEntity>>()
            every { state.pages }.returns(emptyList())
            every { state.config }.returns(PagingConfig(10, 10))
            coEvery { networkService.getDocumentLists(any(), any(), any(), any(), any()) }.throws(IOException())
            var result = remoteMediator.load(LoadType.REFRESH, state)
            Assert.assertTrue(result is RemoteMediator.MediatorResult.Error)

            val resp = mockk<Response<String>>()
            every { resp.code() }.returns(201)
            every { resp.message() }.returns("test")
            coEvery { networkService.getDocumentLists(any(), any(), any(), any(), any()) }.throws(HttpException(resp))
            result = remoteMediator.load(LoadType.REFRESH, state)
            Assert.assertTrue(result is RemoteMediator.MediatorResult.Error)
        }
    }

    @OptIn(ExperimentalPagingApi::class)
    @Test
    fun should_call_onCommon_when_handleError() {
        val result = remoteMediator.handleError(Exception(("测试失败")))
        Assert.assertTrue(result is RemoteMediator.MediatorResult.Error)
        verify { StatisticsUtils.onCommon(any(), StatisticsUtils.DRIVE_FILE_GET_LIST_FAIL, any<Map<String, String>>()) }
    }

    private fun mockList(count: Int): List<CloudDocumentItem> {
        val list = mutableListOf<CloudDocumentItem>()
        for (i in 0 until count) {
            val item = CloudDocumentItem()
            item.id = "${i * 2}"
            list.add(item)
        }
        return list
    }

    private fun mockEntryList(count: Int): List<DriveFileEntity> {
        val list = mutableListOf<DriveFileEntity>()
        for (i in 0 until count) {
            val item = DriveFileEntity(0L)
            item.fileId = "$i"
            list.add(item)
        }
        return list
    }

    @Test
    fun should_call_updateDocuments_when_saveCloudDocuments() {
        justRun { DriveFileDBHelper.deleteDocuments(any(), any()) }
        justRun { DriveFileDBHelper.updateDocuments(any(), any(), any(), any()) }
        var list = mockList(0)
        remoteMediator.saveCloudDocuments(list, LoadType.REFRESH)
        verify { DriveFileDBHelper.deleteDocuments(any(), any()) }

        remoteMediator.saveCloudDocuments(list, LoadType.PREPEND)
        verify(inverse = true) { DriveFileDBHelper.updateDocuments(any(), any(), any(), any()) }

        list = mockList(2)
        remoteMediator.saveCloudDocuments(list, LoadType.PREPEND)
        verify { DriveFileDBHelper.updateDocuments(any(), any(), any(), any()) }

        list = mockList(3)
        remoteMediator.saveCloudDocuments(list, LoadType.PREPEND)
        verify { DriveFileDBHelper.updateDocuments(any(), any(), any(), false) }

        list = mockList(3)
        remoteMediator.saveCloudDocuments(list, LoadType.REFRESH)
        verify { DriveFileDBHelper.updateDocuments(any(), any(), any(), true) }
    }

    @Test
    fun should_notNull_when_removeDuplicate() {
        var list = mockEntryList(0)
        var pages = mockEntryList(0)
        var result = remoteMediator.removeDuplicate(list, pages)
        Assert.assertTrue(result.isEmpty())

        list = mockEntryList(2)
        pages = mockEntryList(4)
        result = remoteMediator.removeDuplicate(list, pages)
        Assert.assertTrue(result.isEmpty())

        list = mockEntryList(5)
        pages = mockEntryList(2)
        result = remoteMediator.removeDuplicate(list, pages)
        Assert.assertEquals(result.size, 3)
    }

    @Test
    fun `should return true when call isAuthError if loadState is ServerException with kdoc AUTH_EXPIRED`() {
        //given
        val loadState = mockk<LoadState.Error>()
        val error = ServerException(KDocsFileDriveRepository.AUTH_EXPIRED, "")
        every { loadState.error } returns error
        //when
        val result = CloudDocsRemoteMediator.isAuthError(loadState)
        //then
        Assert.assertTrue(result)
    }

    @Test
    fun `should return true when call isAuthError if loadState is ServerException with tencent AUTH_EXPIRED`() {
        //given
        val loadState = mockk<LoadState.Error>()
        val error = ServerException(TencentFileDriveRepository.AUTH_EXPIRED, "")
        every { loadState.error } returns error
        //when
        val result = CloudDocsRemoteMediator.isAuthError(loadState)
        //then
        Assert.assertTrue(result)
    }

    @Test
    fun `should return false when call isAuthError if loadState is ServerException with other error`() {
        //given
        val loadState = mockk<LoadState.Error>()
        val error = ServerException(100, "")
        every { loadState.error } returns error
        //when
        val result = CloudDocsRemoteMediator.isAuthError(loadState)
        //then
        Assert.assertFalse(result)
    }

    @Test
    fun `should return false when call isAuthError if loadState is not error`() {
        //given
        val loadState = mockk<LoadState.NotLoading>()
        //when
        val result = CloudDocsRemoteMediator.isAuthError(loadState)
        //then
        Assert.assertFalse(result)
    }

    private companion object {
        private val testCaseTimeout = 30.seconds
    }
}