/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudFileSortTypeUtilsTest
 ** Description : CloudFileSortTypeUtils Unit Test
 ** Version     : 1.0
 ** Date        : 2024/01/12 16:31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/01/12       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.drivebrowser.utils

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.oplus.filemanager.drivebrowser.data.utils.CloudFileSortTypeUtils
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import org.junit.Assert
import org.junit.Test

class CloudFileSortTypeUtilsTest {

    @Test
    fun should_return_String_when_getSortType() {
        var sortType = CloudFileSortTypeUtils.getSortType(CategoryHelper.CATEGORY_TENCENT_DOCS, SortHelper.FILE_NAME_ORDER)
        Assert.assertEquals(sortType, CloudFileSortTypeUtils.TENCENT_DOCS_TITLE)

        sortType = CloudFileSortTypeUtils.getSortType(CategoryHelper.CATEGORY_K_DOCS, SortHelper.FILE_NAME_ORDER)
        Assert.assertEquals(sortType, CloudFileSortTypeUtils.KDOCS_NAME)
    }

    @Test
    fun should_return_string_when_getTencentDocSortType() {
        var sortType = CloudFileSortTypeUtils.getTencentDocSortType(SortHelper.FILE_LAST_OPEN_TIME_ORDER)
        Assert.assertEquals(sortType, CloudFileSortTypeUtils.TENCENT_DOCS_BROWSE)

        sortType = CloudFileSortTypeUtils.getTencentDocSortType(SortHelper.FILE_TIME_REVERSE_ORDER)
        Assert.assertEquals(sortType, CloudFileSortTypeUtils.TENCENT_DOCS_MODIFY)

        sortType = CloudFileSortTypeUtils.getTencentDocSortType(SortHelper.FILE_NAME_ORDER)
        Assert.assertEquals(sortType, CloudFileSortTypeUtils.TENCENT_DOCS_TITLE)

        sortType = CloudFileSortTypeUtils.getTencentDocSortType(SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER)
        Assert.assertEquals(sortType, CloudFileSortTypeUtils.TENCENT_DOCS_BROWSE)
    }

    @Test
    fun should_return_string_when_getKingSoftDocSortType() {
        var sortType = CloudFileSortTypeUtils.getKingSoftDocSortType(SortHelper.FILE_TIME_REVERSE_ORDER)
        Assert.assertEquals(sortType, CloudFileSortTypeUtils.KDOCS_MODIFY_TIME)

        sortType = CloudFileSortTypeUtils.getKingSoftDocSortType(SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER)
        Assert.assertEquals(sortType, CloudFileSortTypeUtils.KDOCS_SIZE)

        sortType = CloudFileSortTypeUtils.getKingSoftDocSortType(SortHelper.FILE_NAME_ORDER)
        Assert.assertEquals(sortType, CloudFileSortTypeUtils.KDOCS_NAME)

        sortType = CloudFileSortTypeUtils.getKingSoftDocSortType(SortHelper.FILE_LAST_OPEN_TIME_ORDER)
        Assert.assertEquals(sortType, CloudFileSortTypeUtils.KDOCS_MODIFY_TIME)
    }

    @Test
    fun should_return_string_when_getDefaultSortType() {
        val context = mockk<Context>()
        mockkObject(MyApplication)
        every { MyApplication.sAppContext }.returns(context)
        mockkStatic(SortModeUtils::class)
        every { SortModeUtils.getSharedSortMode(any(), any()) }.answers {
            val key = secondArg<String>()
            if (SortRecordModeFactory.isTencentDocs(key)) {
                SortHelper.FILE_LAST_OPEN_TIME_ORDER
            } else {
                SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER
            }
        }
        every { SortModeUtils.getSharedSortOrder(any()) }.returns(false)
        var defaultType = CloudFileSortTypeUtils.getDefaultSortType(CategoryHelper.CATEGORY_TENCENT_DOCS)
        Assert.assertEquals(defaultType.first, CloudFileSortTypeUtils.TENCENT_DOCS_BROWSE)
        Assert.assertEquals(defaultType.second, 1)

        every { SortModeUtils.getSharedSortOrder(any()) }.returns(true)
        defaultType = CloudFileSortTypeUtils.getDefaultSortType(CategoryHelper.CATEGORY_K_DOCS)
        Assert.assertEquals(defaultType.first, CloudFileSortTypeUtils.KDOCS_SIZE)
        Assert.assertEquals(defaultType.second, 0)

        unmockkObject(MyApplication)
        unmockkStatic(SortModeUtils::class)
    }
}