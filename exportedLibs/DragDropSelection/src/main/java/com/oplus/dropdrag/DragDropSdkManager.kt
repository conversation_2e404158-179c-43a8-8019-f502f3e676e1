/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - DragDropSdkManager.kt
 * Description:
 *     The manager to init SDK.
 *
 * Version: 1.0
 * Date: 2024-08-22
 * Author: <PERSON>ix<PERSON><PERSON>.<EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-08-22   1.0    Create this module
 *********************************************************************************/
package com.oplus.dropdrag

import android.content.Context
import androidx.annotation.Keep
import com.oplus.dropdrag.utils.Log
import kotlin.properties.Delegates

@Keep
object DragDropSdkManager {

    @JvmStatic
    internal val applicationContext: Context
        get() = appContextInstance

    @JvmStatic
    var enableLog: Boolean
        get() = Log.enableLog
        set(value) = Log::enableLog.set(value)

    @JvmStatic
    var baseLogTag: String
        get() = Log.baseLogTag
        set(value) = Log::baseLogTag.set(value)

    private var appContextInstance: Context by Delegates.notNull()

    @JvmStatic
    fun init(context: Context) {
        appContextInstance = context.applicationContext
    }
}