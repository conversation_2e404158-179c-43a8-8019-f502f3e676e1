/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.GlobalSearchFilterByExtensionTest
 * * Version     : 1.0
 * * Date        : 2020/7/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.coloros.filemanager.filerefactor.ui.globalsearch

import com.coloros.filemanager.BaseTest
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterCondition
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConstants
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterDataHelperFactory
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment

@Ignore("stupid test")
@RunWith(RobolectricTestRunner::class)
class GlobalSearchFilterByExtensionTest : BaseTest() {
    @Before
    override fun setUp() {
        mContext = RuntimeEnvironment.application
    }

    @After
    override fun tearDown() {
        super.tearDown()
    }

    @Test
    fun should_verify_extension_filter_by_png() {
        val filterItem = FilterItem(FilterConstants.FILTER_IMG_PNG,
                FilterCondition(FilterConstants.FILTER_IMG, ""), "")
        val bean1 = BaseFileBean()
        bean1.mDisplayName = "abc.png"
        val bean2 = BaseFileBean()
        bean2.mDisplayName = "abc.Png"
        val bean3 = BaseFileBean()
        bean3.mDisplayName = "abc.JPG"
        val bean4 = BaseFileBean()
        bean4.mDisplayName = "abc.pNg"
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
            add(bean4)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(3, resultList!!.size)
        Assert.assertFalse(resultList!!.contains(bean3))
    }

    @Test
    fun should_verify_extension_filter_by_jpg() {
        val filterItem = FilterItem(FilterConstants.FILTER_IMG_JPG,
                FilterCondition(FilterConstants.FILTER_IMG, ""), "")
        val bean1 = BaseFileBean()
        bean1.mDisplayName = "abc.jpg"
        val bean2 = BaseFileBean()
        bean2.mDisplayName = "abc.Png"
        val bean3 = BaseFileBean()
        bean3.mDisplayName = "abc.JPeG"
        val bean4 = BaseFileBean()
        bean4.mDisplayName = "abc.gif"
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
            add(bean4)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(2, resultList!!.size)
        Assert.assertFalse(resultList!!.contains(bean2))
        Assert.assertFalse(resultList!!.contains(bean4))
    }

    @Test
    fun should_verify_extension_filter_by_xls() {
        val filterItem = FilterItem(FilterConstants.FILTER_DOC_XLS,
                FilterCondition(FilterConstants.FILTER_DOC, ""), "")
        val bean1 = BaseFileBean()
        bean1.mDisplayName = "abc.jpg"
        val bean2 = BaseFileBean()
        bean2.mDisplayName = "abc.doc"
        val bean3 = BaseFileBean()
        bean3.mDisplayName = "abc.xls"
        val bean4 = BaseFileBean()
        bean4.mDisplayName = "abc.XlSx"
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
            add(bean4)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(2, resultList!!.size)
        Assert.assertFalse(resultList!!.contains(bean1))
        Assert.assertFalse(resultList!!.contains(bean2))
    }

    @Test
    fun should_verify_extension_filter_by_doc() {
        val filterItem = FilterItem(FilterConstants.FILTER_DOC_DOC,
                FilterCondition(FilterConstants.FILTER_DOC, ""), "")
        val bean1 = BaseFileBean()
        bean1.mDisplayName = "abc.jpg"
        val bean2 = BaseFileBean()
        bean2.mDisplayName = "abc.doc"
        val bean3 = BaseFileBean()
        bean3.mDisplayName = "abc.DoCX"
        val bean4 = BaseFileBean()
        bean4.mDisplayName = "abc.XlSx"
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
            add(bean4)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(2, resultList!!.size)
        Assert.assertFalse(resultList!!.contains(bean1))
        Assert.assertFalse(resultList!!.contains(bean4))
    }

    @Test
    fun should_verify_filter_result_by_ppt() {
        val filterItem = FilterItem(FilterConstants.FILTER_DOC_PPT,
                FilterCondition(FilterConstants.FILTER_DOC, ""), "")
        val bean1 = BaseFileBean()
        bean1.mDisplayName = "abc.ppt"
        val bean2 = BaseFileBean()
        bean2.mDisplayName = "abc.doc"
        val bean3 = BaseFileBean()
        bean3.mDisplayName = "abc.pptX"
        val bean4 = BaseFileBean()
        bean4.mDisplayName = "abc.XlSx"
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
            add(bean4)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(2, resultList!!.size)
        Assert.assertFalse(resultList!!.contains(bean2))
        Assert.assertFalse(resultList!!.contains(bean4))
    }

    @Test
    fun should_verify_filter_result_by_pdf() {
        val filterItem = FilterItem(FilterConstants.FILTER_DOC_PDF,
                FilterCondition(FilterConstants.FILTER_DOC, ""), "")
        val bean1 = BaseFileBean()
        bean1.mDisplayName = "abc.pdfx"
        val bean2 = BaseFileBean()
        bean2.mDisplayName = "abc.doc"
        val bean3 = BaseFileBean()
        bean3.mDisplayName = "abc.Pdf"
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(1, resultList!!.size)
        Assert.assertTrue(resultList!!.contains(bean3))
    }

    @Test
    fun should_verify_filter_result_by_mp3() {
        val filterItem = FilterItem(FilterConstants.FILTER_AUDIO_MP3,
                FilterCondition(FilterConstants.FILTER_AUDIO, ""), "")
        val bean1 = BaseFileBean()
        bean1.mDisplayName = "abc.mp4"
        val bean2 = BaseFileBean()
        bean2.mDisplayName = "abc.mp3"
        val bean3 = BaseFileBean()
        bean3.mDisplayName = "abc."
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(1, resultList!!.size)
        Assert.assertTrue(resultList!!.contains(bean2))
    }

    @Test
    fun should_verify_filter_result_by_avi() {
        val filterItem = FilterItem(FilterConstants.FILTER_VIDEO_AVI,
                FilterCondition(FilterConstants.FILTER_VIDEO, ""), "")
        val bean1 = BaseFileBean()
        bean1.mDisplayName = "abc.mp4"
        val bean2 = BaseFileBean()
        bean2.mDisplayName = "abc.av"
        val bean3 = BaseFileBean()
        bean3.mDisplayName = "abc.avi"
        val bean4 = BaseFileBean()
        bean4.mDisplayName = "abc.avi3"
        val dataList = arrayListOf<BaseFileBean>().apply {
            add(bean1)
            add(bean2)
            add(bean3)
            add(bean4)
        }
        val resultList = FilterDataHelperFactory.getFilterDataHelper(filterItem.parent.id)
                ?.filterData(filterItem, dataList)
        Assert.assertNotNull(resultList)
        Assert.assertEquals(1, resultList!!.size)
        Assert.assertTrue(resultList!!.contains(bean3))
    }
}
