package com.oplus.filemanager.oaps

import android.app.Activity
import android.content.Context
import android.graphics.Point
import com.cdo.oaps.OapsConstants
import com.cdo.oaps.api.callback.Callback
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.Constants
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.interfaze.oaps.OapsInitResultCallback
import io.mockk.*
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.test.runBlockingTest
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * OapsApi 的单元测试类
 * 用于测试 OapsApi 对象的各种功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class OapsApiTest {

    // 使用 MockK 框架模拟的 Activity 对象
    @MockK
    private lateinit var mockActivity: Activity

    // 使用 MockK 框架模拟的 Context 对象
    @MockK
    private lateinit var mockContext: Context

    // 使用 MockK 框架模拟的回调接口
    @MockK
    private lateinit var mockCallback: OapsInitResultCallback

    // 用于测试的真实 Activity 对象
    private lateinit var testActivity: Activity

    /**
     * 测试前的初始化方法
     * 1. 初始化 MockK 注解
     * 2. 创建测试用的 Activity
     * 3. 模拟各种静态方法和对象
     */
    @Before
    fun setUp() {
        // 初始化 MockK 注解
        MockKAnnotations.init(this)
        // 使用 Robolectric 创建测试 Activity
        testActivity = Robolectric.buildActivity(Activity::class.java).get()
        // 模拟 ThreadManager 的单例对象
        mockkObject(ThreadManager.sThreadManager)
        // 模拟 OapsUtils 的静态方法
        mockkStatic(OapsUtils::class)
        // 模拟 KtViewUtils 的静态方法
        mockkStatic(KtViewUtils::class)
        // 模拟 StatisticsUtils 的静态方法
        mockkStatic(StatisticsUtils::class)
        // 模拟 CustomToast 的静态方法
        mockkStatic(CustomToast::class)
        // 模拟 Log 的静态方法，所有日志调用都不执行实际操作
        mockkStatic(Log::class)
        every { Log.v(any<String>(), any<String>()) } just Runs
        every { Log.i(any<String>(), any<String>()) } just Runs
        every { Log.e(any<String>(), any<String>()) } just Runs
        every { Log.w(any<String>(), any<String>()) } just Runs
        // 模拟 MyApplication 对象
        mockkObject(MyApplication)
        every { MyApplication.appContext } returns testActivity.applicationContext
    }

    /**
     * 测试后的清理方法
     * 1. 解除所有模拟
     * 2. 清除所有模拟调用记录
     */
    @After
    fun tearDown() {
        unmockkAll()
        clearAllMocks()
    }

    /**
     * 测试 isShowApkEntrance 方法
     * 验证该方法返回 OapsUtils.isShowApkEntrance() 的结果
     */
    @Test
    fun `isShowApkEntrance should return OapsUtils result`() {
        // 设置模拟返回值
        every { OapsUtils.isShowApkEntrance() } returns true

        // 调用被测方法
        val result = OapsApi.isShowApkEntrance()

        // 验证返回结果
        assertTrue(result)
    }

    /**
     * 测试 isShowHomeEntrance 方法
     * 验证该方法返回 OapsUtils.isShowHomeEntrance() 的结果
     */
    @Test
    fun `isShowHomeEntrance should return OapsUtils result`() {
        // 设置模拟返回值
        every { OapsUtils.isShowHomeEntrance() } returns false

        // 调用被测方法
        val result = OapsApi.isShowHomeEntrance()

        // 验证返回结果
        assertFalse(result)
    }

    /**
     * 测试 startOaps 方法在响应成功时的行为
     * 1. 验证成功统计被记录
     * 2. 验证页面曝光统计被记录
     * 3. 验证没有显示错误提示
     */
    @Test
    fun `startOaps should record success stats when response is SUCCESS`() {
        // 创建模拟响应对象
        val mockResponse = mockk<Callback.Response>()
        // 设置响应码为成功
        every { mockResponse.code } returns OapsConstants.SUCCESS
        // 模拟获取URL
        every { OapsUtils.getOapsOpenUrl(any(), any()) } returns "test_url"
        // 捕获回调对象
        val callbackSlot = slot<Callback>()
        // 模拟启动Oaps
        every { OapsUtils.startOaps(any(), any(), capture(callbackSlot)) } just Runs
        // 模拟统计方法
        every { StatisticsUtils.onCommon(any(), any()) } just Runs
        every { StatisticsUtils.statisticsPageExposure(any(), any(), any(), any()) } just Runs

        // 调用被测方法
        OapsApi.startOaps(mockActivity)
        // 触发回调
        callbackSlot.captured.onResponse(mockResponse)

        // 验证成功统计被调用
        verify(exactly = 1) { 
            StatisticsUtils.onCommon(
                eq(testActivity.applicationContext),
                eq(StatisticsUtils.OAPS_TO_APPSTORE_FROM_HOME_SUCC_COUNT)
            ) 
        }
        // 验证页面曝光统计被调用
        verify(exactly = 1) { 
            StatisticsUtils.statisticsPageExposure(
                eq(mockActivity), 
                eq(""), 
                eq(Constants.PAGE_APP_STORE), 
                eq(Constants.PAGE_MAIN)
            ) 
        }
        // 验证没有显示错误提示
        verify(exactly = 0) { CustomToast.showShort(any<Int>()) }
    }

    /**
     * 测试 startOaps 方法在响应失败时的行为
     * 1. 验证失败统计被记录
     * 2. 验证显示了错误提示
     */
    @Test
    fun `startOaps should show toast and record fail stats when response is not SUCCESS or LOCAL_ERROR`() {
        // 创建模拟响应对象
        val mockResponse = mockk<Callback.Response>()
        // 设置响应码为500
        every { mockResponse.code } returns 500
        // 模拟获取URL
        every { OapsUtils.getOapsOpenUrl(any(), any()) } returns "test_url"
        // 捕获回调对象
        val callbackSlot = slot<Callback>()
        // 模拟启动Oaps
        every { OapsUtils.startOaps(any(), any(), capture(callbackSlot)) } just Runs
        // 模拟统计方法
        every { StatisticsUtils.onCommon(any(), any()) } just Runs
        // 模拟Toast显示
        every { CustomToast.showShort(any<Int>()) } just Runs

        // 调用被测方法
        OapsApi.startOaps(mockActivity)
        // 触发回调
        callbackSlot.captured.onResponse(mockResponse)

        // 验证失败统计被调用
        verify(exactly = 1) { 
            StatisticsUtils.onCommon(
                eq(testActivity.applicationContext),
                eq(StatisticsUtils.OAPS_TO_APPSTORE_FROM_HOME_FAIL_COUNT)
            ) 
        }
        // 验证显示了错误提示
        verify(exactly = 1) { CustomToast.showShort(any<Int>()) }
    }

    /**
     * 测试 openAppStoreDetail 方法在卸载标签页且响应成功时的行为
     * 验证卸载成功统计被记录
     */
    @Test
    fun `openAppStoreDetail should record uninstall success stats when uninstallTab true and response SUCCESS`() {
        // 创建模拟响应对象
        val mockResponse = mockk<Callback.Response>()
        // 设置响应码为成功
        every { mockResponse.code } returns OapsConstants.SUCCESS
        // 模拟获取URL
        every { OapsUtils.getOapsOpenUrl(any(), any()) } returns "test_url"
        // 捕获回调对象
        val callbackSlot = slot<Callback>()
        // 模拟启动Oaps
        every { OapsUtils.startOaps(any(), any(), capture(callbackSlot)) } just Runs
        // 模拟统计方法
        every { StatisticsUtils.onCommon(any(), any()) } just Runs
        // 模拟获取字符串资源
        every { mockContext.getString(any<Int>()) } returns "recommended_apps"

        // 调用被测方法
        OapsApi.openAppStoreDetail(mockContext, true)
        // 触发回调
        callbackSlot.captured.onResponse(mockResponse)

        // 验证卸载成功统计被调用
        verify(exactly = 1) { 
            StatisticsUtils.onCommon(
                eq(mockContext),
                eq(StatisticsUtils.OAPS_TO_APPSTORE_FROM_APK_UNINS_SUCC_COUNT)
            ) 
        }
    }

    /**
     * 测试 openAppStoreDetail 方法在非卸载标签页且响应失败时的行为
     * 1. 验证安装失败统计被记录
     * 2. 验证显示了错误提示
     */
    @Test
    fun `openAppStoreDetail should record install fail stats when uninstallTab false and response failed`() {
        // 创建模拟响应对象
        val mockResponse = mockk<Callback.Response>()
        // 设置响应码为500
        every { mockResponse.code } returns 500
        // 模拟获取URL
        every { OapsUtils.getOapsOpenUrl(any(), any()) } returns "test_url"
        // 捕获回调对象
        val callbackSlot = slot<Callback>()
        // 模拟启动Oaps
        every { OapsUtils.startOaps(any(), any(), capture(callbackSlot)) } just Runs
        // 模拟统计方法
        every { StatisticsUtils.onCommon(any(), any()) } just Runs
        // 模拟Toast显示
        every { CustomToast.showShort(any<Int>()) } just Runs
        // 模拟获取字符串资源
        every { mockContext.getString(any<Int>()) } returns "recommended_apps"

        // 调用被测方法
        OapsApi.openAppStoreDetail(mockContext, false)
        // 触发回调
        callbackSlot.captured.onResponse(mockResponse)

        // 验证安装失败统计被调用
        verify(exactly = 1) { 
            StatisticsUtils.onCommon(
                eq(mockContext),
                eq(StatisticsUtils.OAPS_TO_APPSTORE_FROM_APK_INS_FAIL_COUNT)
            ) 
        }
        // 验证显示了错误提示
        verify(exactly = 1) { CustomToast.showShort(any<Int>()) }
    }

    /**
     * 测试 checkAppStoreEnabledWithDialog 方法
     * 验证该方法返回 OapsUtils.checkAppStoreEnabled() 的结果
     */
    @Test
    fun `checkAppStoreEnabledWithDialog should return OapsUtils result`() {
        // 设置模拟返回值
        every { OapsUtils.checkAppStoreEnabled(any()) } returns true

        // 调用被测方法
        val result = OapsApi.checkAppStoreEnabledWithDialog(mockContext)

        // 验证返回结果
        assertTrue(result)
    }
}