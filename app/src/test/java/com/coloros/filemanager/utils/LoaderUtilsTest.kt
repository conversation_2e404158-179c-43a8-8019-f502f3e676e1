/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date: 2020/7/25
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.coloros.filemanager.utils

import com.coloros.filemanager.BaseTest
import com.filemanager.common.utils.LoaderUtils
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test

@Ignore
class LoaderUtilsTest : BaseTest() {

    @Test
    fun should_verify_when_contains_special_char() {
        Assert.assertEquals("", LoaderUtils.createFuzzySearch(""))
        Assert.assertEquals(" LIKE '%a//%' ESCAPE '/'", LoaderUtils.createFuzzySearch("a/"))
        Assert.assertEquals(" LIKE '%/%%' ESCAPE '/'", LoaderUtils.createFuzzySearch("%"))
        Assert.assertEquals(" LIKE '%/)%' ESCAPE '/'", LoaderUtils.createFuzzySearch(")"))
        Assert.assertEquals(" LIKE '%/(%' ESCAPE '/'", LoaderUtils.createFuzzySearch("("))
        Assert.assertEquals(" LIKE '%a/[%' ESCAPE '/'", LoaderUtils.createFuzzySearch("a["))
        Assert.assertEquals(" LIKE '%AA/]%' ESCAPE '/'", LoaderUtils.createFuzzySearch("AA]"))
        Assert.assertEquals(" LIKE '%/&%' ESCAPE '/'", LoaderUtils.createFuzzySearch("&"))
        Assert.assertEquals(" LIKE '%/_%' ESCAPE '/'", LoaderUtils.createFuzzySearch("_"))
        Assert.assertEquals("", LoaderUtils.createFuzzySearch(null))
        Assert.assertEquals(" LIKE '%abcd%' ESCAPE '/'", LoaderUtils.createFuzzySearch("abcd"))
    }


    @Test
    fun should_verify_when_pattern() {
        Assert.assertNotNull(LoaderUtils.getPattern("test/{."))
    }
}