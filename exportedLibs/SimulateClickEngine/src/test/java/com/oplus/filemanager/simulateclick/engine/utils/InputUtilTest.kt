/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - InputUtilTest.kt
 * Description:
 * The test cases for InputUtil
 *
 * Version: 1.0
 * Date: 2024-05-31
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * (author)                        (date)    (version)    (desc)
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-05-31   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.engine.utils

import com.oplus.filemanager.simulateclick.engine.Action
import com.oplus.filemanager.simulateclick.engine.CHECK_TYPE_EQUALS
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert
import org.junit.Test

class InputUtilTest : Assert() {

    @Test
    fun `should true when isEqualsIgnoreSpace if contains after remove space and to lower`() {
        // Given
        val str1 = "Test String"
        val str2 = "testString2"

        // When & Then
        assertTrue(InputUtil.isEqualsIgnoreSpace(str1, str2))
    }

    @Test
    fun `should false when isEqualsIgnoreSpace if has different content`() {
        // Given
        val str1 = "Test String1"
        val str2 = "testString2"

        // When & Then
        assertFalse(InputUtil.isEqualsIgnoreSpace(str1, str2))
    }

    @Test
    fun `should false when isEqualsIgnoreSpace if str2 is null`() {
        assertFalse(InputUtil.isEqualsIgnoreSpace("test", null))
    }

    @Test
    fun `should false when isCheckTypeEquals if type is not equals`() {
        // Given
        val action = mockk<Action>()
        every { action.itemCheckType } returns ""

        // When & Then
        assertFalse(InputUtil.isCheckTypeEquals(action))
    }

    @Test
    fun `should true when isCheckTypeEquals if type is equals`() {
        // Given
        val action = mockk<Action>()
        every { action.itemCheckType } returns CHECK_TYPE_EQUALS

        // When & Then
        assertTrue(InputUtil.isCheckTypeEquals(action))
    }
}