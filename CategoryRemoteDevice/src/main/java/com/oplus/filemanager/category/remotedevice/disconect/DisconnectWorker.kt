/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DisconnectWorker
 * * Description : 断开链接的实际的worker线程类
 * * Version     : 1.0
 * * Date        : 2025/2/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.remotedevice.disconect

import android.content.Context
import android.util.Log
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice

class DisconnectWorker(context: Context, params: WorkerParameters) : Worker(context, params) {

    companion object {
        const val TAG = "DisconnectWorker"
    }

    override fun doWork(): Result {
        Log.d(TAG, "doWork ID: ${this.id} start")
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        val currentLinkInfo = remoteDeviceApi?.getCurrentLinkedRemoteDiceInfo()
        val remoteDeviceId = currentLinkInfo?.deviceId
        val isFileDownloading = if (remoteDeviceId != null) {
            remoteDeviceApi.isFileChannelOccupied(remoteDeviceId)
        } else {
            false
        }
        if (isFileDownloading) {
            //正在下载中，不用断链
            Log.d(TAG, "doWork ID: ${this.id} fileDownloading, not disconnect")
        } else {
            if (currentLinkInfo != null) {
                remoteDeviceApi.disconnect()
            } else {
                //没有链接，不用断链
                Log.d(TAG, "doWork ID: ${this.id} no currentLinkInfo, not disconnect")
            }
        }
        Log.d(TAG, "doWork ID: ${this.id} end")
        return Result.success()
    }


    override fun onStopped() {
        Log.d(TAG, "onStopped ${this.id}")
        super.onStopped()
    }
}