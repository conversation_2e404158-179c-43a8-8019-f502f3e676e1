/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - AccessibilitySettings.kt
 * Description:
 *     To get or set system accessibility services config.
 *
 * Version: 1.0
 * Date: 2024-05-22
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<EMAIL>    2024-05-22   1.0    Create this module
 *********************************************************************************/
package com.oplus.filemanager.simulateclick.accessibility

import android.content.ComponentName
import android.content.Context
import android.provider.Settings
import com.oplus.filemanager.simulateclick.engine.utils.Log

/**
 * Refer to:
 * /frameworks/base/packages/SettingsLib/src/com/android/settingslib/accessibility/AccessibilityUtils.java
 */
internal object AccessibilitySettings {
    private const val TAG = "AccessibilitySettings"

    private const val ENABLED_ACCESSIBILITY_SERVICES_SEPARATOR = ':'

    @JvmStatic
    fun getEnabledServicesFromSettings(context: Context): Set<ComponentName> {
        val resolver = context.contentResolver
        val accessibilityEnabled = Settings.Secure.getInt(
            resolver,
            Settings.Secure.ACCESSIBILITY_ENABLED,
            0
        )
        if (accessibilityEnabled == 0) {
            return emptySet()
        }
        val enabledServicesSetting = Settings.Secure.getString(
            resolver,
            Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
        )
        if (enabledServicesSetting.isNullOrEmpty()) {
            return emptySet()
        }
        val enabledServices = mutableSetOf<ComponentName>()
        enabledServicesSetting.split(ENABLED_ACCESSIBILITY_SERVICES_SEPARATOR).forEach {
            val enabledService = ComponentName.unflattenFromString(it)
            if (enabledService != null) {
                enabledServices.add(enabledService)
            }
        }
        return enabledServices
    }

    @JvmStatic
    fun setAccessibilityServiceState(
        context: Context,
        toggledService: ComponentName,
        enabled: Boolean,
        enabledServices: MutableSet<ComponentName> = getEnabledServicesFromSettings(context).toMutableSet()
    ) {
        if (enabled) {
            if (enabledServices.contains(toggledService)) {
                Log.w(TAG, "setAccessibilityServiceState: $toggledService is already enabled")
                return
            }
            enabledServices.add(toggledService)
        } else {
            if (!enabledServices.contains(toggledService)) {
                Log.w(TAG, "setAccessibilityServiceState: $toggledService is already disabled")
                return
            }
            enabledServices.remove(toggledService)
        }
        val enabledServicesBuilder = StringBuilder()
        val lastItem = enabledServices.lastOrNull()
        enabledServices.forEach {
            enabledServicesBuilder.append(it.flattenToString())
            if (it != lastItem) {
                enabledServicesBuilder.append(ENABLED_ACCESSIBILITY_SERVICES_SEPARATOR)
            }
        }
        /*
         * Settings.Secure.ACCESSIBILITY_ENABLED will be auto updated by AccessibilityManagerService.
         * So just update Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES.
         */
        SettingsCompat.Secure.putString(
            context.contentResolver,
            Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES,
            enabledServicesBuilder.toString()
        )
    }
}