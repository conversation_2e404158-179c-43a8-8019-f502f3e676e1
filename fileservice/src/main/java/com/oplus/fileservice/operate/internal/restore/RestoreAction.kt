/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RestoreAction
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 11:35
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/21       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.internal.restore

import android.content.ContentResolver
import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import android.net.Uri
import com.filemanager.common.RecycleStore.Files.INTERNAL_CONTENT_URI
import com.filemanager.common.batch.DoubleBatchAction
import com.filemanager.common.compat.MediaScannerCompat
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.recyclebin.utils.OperationDBUtils

internal class RestoreAction(
    context: Context,
    private val uri: Uri = INTERNAL_CONTENT_URI,
    private val columnName: String
) : DoubleBatchAction<String, String>(DELETE_FILE_BATCH_COUNT, SCAN_FILE_BATCH_COUNT) {

    private val mWhereIdClause: StringBuilder = StringBuilder()
    private val mResolver: ContentResolver = context.applicationContext.contentResolver
    var sqliteOpenHelper: SQLiteOpenHelper? = null
    var db: SQLiteDatabase? = null

    override fun add(id: String?, scanPath: String?) {
        if (id?.isNotEmpty() == true) {
            if (mWhereIdClause.isNotEmpty()) {
                mWhereIdClause.append(",")
            }
            mWhereIdClause.append("?")
        }
        super.add(id, scanPath)
    }

    @Suppress("TooGenericExceptionCaught")
    override fun flushParams1() {
        val size = mPending1.size
        if (size <= 0) {
            return
        }
        Log.d(TAG, "flushParams1 delete recycle bin db data $size")
        try {
            if (db == null) {
                sqliteOpenHelper = OperationDBUtils.createSQLiteOpenHelper(uri)
                db = OperationDBUtils.beginTransaction(sqliteOpenHelper?.writableDatabase)
            }
            val foo = mPending1.toTypedArray()
            val rowsNum = OperationDBUtils.delete(uri, "$columnName IN ($mWhereIdClause)", foo, db, sqliteOpenHelper)
            Log.d(TAG, "flushParams1 size: $size rowsNum: $rowsNum")
        } catch (ex: Exception) {
            Log.w(TAG, "flushParams1 failed $ex")
        } finally {
            //make sure reset args only when success
            mWhereIdClause.setLength(0)
            mPending1.clear()
            OperationDBUtils.commit(db, INTERNAL_CONTENT_URI)
            sqliteOpenHelper = null
            db = null
        }
    }

    override fun flushParams2() {
        if (mPending2.size > 0) {
            Log.d(TAG, "flushParams2 notify MediaStore update ")
            MediaScannerCompat.sendMultiDirMediaScanner(mPending2, Utils.MEDIA_SCAN_RECYCLE_RESTORE)
            mPending2.clear()
        }
    }

    companion object {
        private const val TAG = "RestoreAction"
        private const val SCAN_FILE_BATCH_COUNT = 100
        private const val DELETE_FILE_BATCH_COUNT = 100
    }
}