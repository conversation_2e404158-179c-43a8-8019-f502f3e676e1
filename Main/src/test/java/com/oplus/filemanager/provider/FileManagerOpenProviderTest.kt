package com.oplus.filemanager.provider

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Bundle
import com.filemanager.common.utils.Injector
import com.oplus.filemanager.interfaze.fileopentime.IFileOpenTime
import com.oplus.filemanager.router.RouterUtil
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertNull
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import java.lang.reflect.Method

/**
 * FileManagerOpenProvider的单元测试类
 * 使用Robolectric框架进行Android环境下的单元测试
 * 主要测试FileManagerOpenProvider的各个方法功能
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class FileManagerOpenProviderTest {

    // 测试类中使用的成员变量
    private lateinit var provider: FileManagerOpenProvider  // 被测试的ContentProvider实例
    private lateinit var mockContext: Context  // 模拟的Context对象
    private lateinit var mockFileOpenTime: IFileOpenTime  // 模拟的IFileOpenTime接口
    private lateinit var mockCursor: Cursor  // 模拟的Cursor对象

    /**
     * 在每个测试方法执行前的初始化方法
     * 1. 清除所有mock对象
     * 2. 创建模拟对象
     * 3. 初始化被测试的provider实例
     */
    @Before
    fun setUp() {
        clearAllMocks()  // 清除所有mock对象
        mockContext = mockk(relaxed = true)  // 创建宽松的Context模拟对象
        mockFileOpenTime = mockk()  // 创建IFileOpenTime模拟对象
        mockCursor = mockk()  // 创建Cursor模拟对象
        
        mockkObject(Injector)  // 将Injector对象mock化
        mockkObject(RouterUtil)  // 将RouterUtil对象mock化
        
        provider = FileManagerOpenProvider()  // 初始化被测试的provider实例
    }

    /**
     * 在每个测试方法执行后的清理方法
     * 1. 解除所有mock对象
     * 2. 清除所有mock状态
     */
    @After
    fun tearDown() {
        unmockkAll()  // 解除所有mock对象
        clearAllMocks()  // 清除所有mock状态
    }

    /**
     * 测试onCreate方法
     * 验证onCreate方法是否返回true
     */
    @Test
    fun `test onCreate should return true`() {
        assertEquals(true, provider.onCreate())
    }

    /**
     * 测试query方法 - 有效授权情况
     * 1. 模拟Injector返回IFileOpenTime实例
     * 2. 模拟IFileOpenTime.query返回模拟Cursor
     * 3. 验证query方法是否委托给IFileOpenTime执行
     */
    @Test
    fun `test query with valid authority should delegate to IFileOpenTime`() {
        // Given - 准备测试数据
        val uri = Uri.parse("content://com.oplus.filemanager.FileManagerOpenProvider/test")
        every { Injector.injectFactory<IFileOpenTime>() } returns mockFileOpenTime  // 模拟Injector返回IFileOpenTime
        every { mockFileOpenTime.query(any(), any(), any(), any(), any()) } returns mockCursor  // 模拟query方法返回Cursor

        // When - 执行测试方法
        val result = provider.query(uri, null, null, null, null)

        // Then - 验证结果
        assertEquals(mockCursor, result)  // 验证返回的Cursor是否正确
        verify(exactly = 1) { mockFileOpenTime.query(uri, null, null, null, null) }  // 验证query方法是否被调用
    }

    /**
     * 测试query方法 - 无效授权情况
     * 验证当授权不匹配时是否返回null
     */
    @Test
    fun `test query with invalid authority should return null`() {
        // Given - 准备测试数据(无效的URI)
        val uri = Uri.parse("content://invalid.authority/test")

        // When - 执行测试方法
        val result = provider.query(uri, null, null, null, null)

        // Then - 验证结果
        assertNull(result)  // 验证返回null
    }

    /**
     * 测试getType方法
     * 验证getType方法是否始终返回null
     */
    @Test
    fun `test getType should return null`() {
        assertNull(provider.getType(Uri.parse("content://test")))
    }

    /**
     * 测试insert方法
     * 验证insert方法是否始终返回null
     */
    @Test
    fun `test insert should return null`() {
        assertNull(provider.insert(Uri.parse("content://test"), null))
    }

    /**
     * 测试delete方法
     * 验证delete方法是否始终返回0
     */
    @Test
    fun `test delete should return 0`() {
        assertEquals(0, provider.delete(Uri.parse("content://test"), null, null))
    }

    /**
     * 测试update方法
     * 验证update方法是否始终返回0
     */
    @Test
    fun `test update should return 0`() {
        assertEquals(0, provider.update(Uri.parse("content://test"), null, null, null))
    }

    /**
     * 测试call方法 - 空上下文情况
     * 验证当context为null时是否返回null
     */
    @Test
    fun `test call with null context should return null`() {
        // Given - 准备测试数据(不设置context)
        provider = FileManagerOpenProvider()

        // When - 执行测试方法
        val result = provider.call("test", null, null)

        // Then - 验证结果
        assertNull(result)  // 验证返回null
    }
}