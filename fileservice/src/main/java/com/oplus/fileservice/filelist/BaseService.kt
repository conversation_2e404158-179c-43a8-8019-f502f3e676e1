/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BaseService
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/21 11:41
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9010681        2022/7/21       1.0      create
 ***********************************************************************/

package com.oplus.fileservice.filelist

import android.app.Service
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.sort.SortHelper
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.oplus.fileservice.utils.WebFileConstant.DEFAULT_PAGE_NO
import com.oplus.fileservice.utils.WebFileConstant.DEFAULT_PAGE_SIZE

abstract class BaseService() : Service() {
    companion object {
        private const val TAG = "BaseService"
        const val PAGE_NO = "pageNo"
        const val PAGE_SIZE = "pageSize"
        const val SORT_ORDER = "sortOrder"

        /**
         * 是否需要缩略图
         */
        const val NEED_THUMBNAIL = "needThumbnail"
        const val DEFAULT_NEED_THUMBNAIL = true
    }

    @Volatile
    var mInternalPath: String? = null
        get() {
            if (field == null) {
                field = VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)
            }
            return field
        }

    val mGson: Gson = GsonBuilder().excludeFieldsWithoutExposeAnnotation().create()
    var mFilePath: String? = null
    var mPageNo = DEFAULT_PAGE_NO
    var mPageSize = DEFAULT_PAGE_SIZE
    var mSortOrder = SortHelper.FILE_NAME_ORDER
    var isNeedThumbnail = true
}