/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecentCard
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/11/13 16:11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/11/13       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.recent.data

import androidx.annotation.Keep
import com.filemanager.common.helper.CategoryHelper.CATEGORY_RECENT
import com.oplus.pantanal.seedling.bean.SeedlingCardSizeEnum

@Keep
data class RecentCard(
    var cardId: String,
    var cardSize: Int,
    var name: String = "",
    var type: Int = CATEGORY_RECENT
) {

    fun getSize(): Int {
        return if (this.isLargeCard()) {
            LARGE_CARD_SIZE
        } else {
            MIDDLE_CARD_SIZE
        }
    }

    private fun isLargeCard(): Boolean {
        return cardSize == SeedlingCardSizeEnum.FourXFour.sizeCode
    }

    companion object {
        const val DEFAULT_TYPE = -1
        private const val LARGE_CARD_SIZE = 10
        private const val MIDDLE_CARD_SIZE = 4
    }
}