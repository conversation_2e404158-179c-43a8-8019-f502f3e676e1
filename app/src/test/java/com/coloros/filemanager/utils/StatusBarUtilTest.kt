package com.coloros.filemanager.utils

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.os.Build
import android.provider.Settings
import android.view.Window
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.version.COUIVersionUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.StatusBarUtil
import io.mockk.MockKAnnotations
import io.mockk.Runs
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import io.mockk.verify
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class StatusBarUtilTest {

    @MockK
    lateinit var activity: BaseVMActivity

    @MockK
    lateinit var win: Window

    @MockK
    lateinit var res: Resources

    @MockK
    lateinit var mTheme: Resources.Theme

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        mTheme = mockk(relaxed = true)

        res = mockk {
            every { getColor(any(), mTheme) } returns 123
            every { getIdentifier(any(), any(), any()) } returns -1
            every { getBoolean(any()) } returns true
        }

        win = mockk(relaxed = true) {
            every { setDecorFitsSystemWindows(any()) } just Runs
            every { isNavigationBarContrastEnforced = any() } just Runs
            every { navigationBarColor = any() } just Runs
            every { decorView } returns mockk(relaxed = true) { every { systemUiVisibility = any() } just Runs }
            every { statusBarColor = any() } just Runs
        }

        activity = mockk {
            every { window } returns win
            every { isAdaptNavigationBar() } returns true
            every { resources } returns res
            every { theme } returns mTheme
            every { contentResolver } returns mockk()
        }

        val context: Context = mockk(relaxed = true) {
            every { applicationContext }.returns(this)
        }
        MyApplication.init(context)
    }

    @Test
    fun `should adapt navigation bar for os 12`() {
        //Given
        mockkStatic(SdkUtils::class) {
            every { SdkUtils.isAtLeastS() } returns true
            mockkStatic(StatusBarUtil::class) {
                every { StatusBarUtil.checkIsGestureNavMode(activity) } returns true
                //when
                StatusBarUtil.adaptNavigationBarForOS12(activity)
                //then
                verify { win.setDecorFitsSystemWindows(false) }
                verify { win.isNavigationBarContrastEnforced = false }
            }
        }

    }

    @Test
    fun `should not adapt navigation bar for os 12 when is not gesture nav mode`() {
        //Given
        mockkStatic(SdkUtils::class) {
            every { SdkUtils.isAtLeastS() } returns true
            mockkStatic(StatusBarUtil::class) {
                every { StatusBarUtil.checkIsGestureNavMode(activity) } returns false
                //when
                StatusBarUtil.adaptNavigationBarForOS12(activity)
                //then
                verify(inverse = true) { win.setDecorFitsSystemWindows(true) }
            }
        }
    }

    @Test
    fun `should not adapt navigation bar for os 12 when check if need adapt for os 12 is not`() {
        //Given
        mockkStatic(SdkUtils::class) {
            every { SdkUtils.isAtLeastS() } returns false
            mockkStatic(StatusBarUtil::class) {
                every { StatusBarUtil.checkIsGestureNavMode(activity) } returns true
                //when
                StatusBarUtil.adaptNavigationBarForOS12(activity)
                //then
                verify(inverse = true) { win.setDecorFitsSystemWindows(true) }
            }
        }
    }

    @Test
    fun `should not adapt navigation bar for os 12 when activity is not adapt`() {
        //Given
        mockkStatic(SdkUtils::class) {
            every { SdkUtils.isAtLeastS() } returns true
            mockkStatic(StatusBarUtil::class) {
                every { StatusBarUtil.checkIsGestureNavMode(activity) } returns true
                every { activity.isAdaptNavigationBar() } returns false
                //when
                StatusBarUtil.adaptNavigationBarForOS12(activity)
                //then
                verify(inverse = true) { win.setDecorFitsSystemWindows(true) }
            }
        }
    }

    @Test
    fun `should not adapt navigation bar for os 12 when is not gesture nav mode and activity is not adapt`() {
        //Given
        mockkStatic(SdkUtils::class) {
            every { SdkUtils.isAtLeastS() } returns true
            mockkStatic(StatusBarUtil::class) {
                every { StatusBarUtil.checkIsGestureNavMode(activity) } returns false
                every { activity.isAdaptNavigationBar() } returns false
                //when
                StatusBarUtil.adaptNavigationBarForOS12(activity)
                //then
                verify(inverse = true) { win.setDecorFitsSystemWindows(true) }
            }
        }
    }

    @Test
    fun `should reset adapting navigation bar for os 12`() {
        //Given
        mockkStatic(SdkUtils::class) {
            every { SdkUtils.isAtLeastS() } returns true
            mockkStatic(StatusBarUtil::class) {
                every { StatusBarUtil.checkIsGestureNavMode(activity) } returns true
                //when
                StatusBarUtil.resetAdaptingNavigationBarForOS12(activity)
                //then
                verify { win.setDecorFitsSystemWindows(true) }
                verify { win.isNavigationBarContrastEnforced = true }
            }
        }
    }

    @Test
    fun `should not reset adapting navigation bar for os 12 when is not gesture nav mode`() {
        //Given
        mockkStatic(SdkUtils::class) {
            every { SdkUtils.isAtLeastS() } returns true
            mockkStatic(StatusBarUtil::class) {
                every { StatusBarUtil.checkIsGestureNavMode(activity) } returns false
                //when
                StatusBarUtil.resetAdaptingNavigationBarForOS12(activity)
                //then
                verify(inverse = true) { win.setDecorFitsSystemWindows(true) }
            }
        }
    }

    @Test
    fun `should not reset adapting navigation bar for os 12 when check if need adapt for os 12 is not`() {
        //Given
        mockkStatic(SdkUtils::class) {
            every { SdkUtils.isAtLeastS() } returns false
            mockkStatic(StatusBarUtil::class) {
                every { StatusBarUtil.checkIsGestureNavMode(activity) } returns true
                //when
                StatusBarUtil.resetAdaptingNavigationBarForOS12(activity)
                //then
                verify(inverse = true) { win.setDecorFitsSystemWindows(true) }
            }
        }
    }

    @Test
    fun `should not reset adapting navigation bar for os 12 when activity is not adapt`() {
        //Given
        mockkStatic(SdkUtils::class) {
            every { SdkUtils.isAtLeastS() } returns true
            mockkStatic(StatusBarUtil::class) {
                every { StatusBarUtil.checkIsGestureNavMode(activity) } returns true
                every { activity.isAdaptNavigationBar() } returns false
                //when
                StatusBarUtil.resetAdaptingNavigationBarForOS12(activity)
                //then
                verify(inverse = true) { win.setDecorFitsSystemWindows(true) }
            }
        }
    }

    @Test
    fun `should not reset adapting navigation bar for os 12 when is not gesture nav mode and activity is not adapt`() {
        //Given
        mockkStatic(SdkUtils::class) {
            every { SdkUtils.isAtLeastS() } returns true
            mockkStatic(StatusBarUtil::class) {
                every { StatusBarUtil.checkIsGestureNavMode(activity) } returns false
                every { activity.isAdaptNavigationBar() } returns false
                //when
                StatusBarUtil.resetAdaptingNavigationBarForOS12(activity)
                //then
                verify(inverse = true) { win.setDecorFitsSystemWindows(true) }
            }
        }
    }

    @Test
    fun `should set navigation bar color`() {
        mockkStatic(SdkUtils::class) {
            every { SdkUtils.getSDKVersion() } returns Build.VERSION_CODES.LOLLIPOP
            mockkStatic(StatusBarUtil::class)
            mockkStatic(COUIDarkModeUtil::class)
            every { COUIDarkModeUtil.isNightMode(activity) } returns false
            StatusBarUtil.setNavigationBarColor(activity)
           // verify { win.navigationBarColor = 123 }

            every { SdkUtils.getSDKVersion() } returns Build.VERSION_CODES.KITKAT
            StatusBarUtil.setNavigationBarColor(activity)
            verify { StatusBarUtil.adaptNavigationBarForOS12(activity) }

            every { COUIDarkModeUtil.isNightMode(activity) } returns true
            StatusBarUtil.setNavigationBarColor(activity)
            Assert.assertTrue(activity.window.navigationBarColor == Color.TRANSPARENT)
        }
    }

    @Test
    fun `should get status bar view`() {
        mockkStatic(StatusBarUtil::class) {
            mockkStatic(COUIPanelMultiWindowUtils::class) {

                var context = mockk<Context> {
                    every { applicationContext } returns mockk()
                    every { resources } returns res
                    every { getColor(any()) } returns 123
                }

                every { COUIPanelMultiWindowUtils.getStatusBarHeight(context) } returns 123
                StatusBarUtil.getStatusBarView(context)
                StatusBarUtil.getStatusBarView(context, 0)
                StatusBarUtil.getStatusBarView(null)
            }
        }
    }


    @Test
    fun `should check gesture navigation bar mode`() {
        mockkStatic(StatusBarUtil::class) {
            mockkStatic(Settings.Secure::class) {

                every {
                    Settings.Secure.getInt(activity.contentResolver, "hide_navigationbar_enable", 0)
                } returns 2

                Assert.assertFalse(StatusBarUtil.checkIsGestureNavMode(activity))

                every {
                    Settings.Secure.getInt(activity.contentResolver, "hide_navigationbar_enable", 0)
                } returns 3

                Assert.assertFalse(StatusBarUtil.checkIsGestureNavMode(activity))

                every {
                    Settings.Secure.getInt(activity.contentResolver, "hide_navigationbar_enable", 0)
                } returns 4

                Assert.assertFalse(StatusBarUtil.checkIsGestureNavMode(activity))
            }
        }
    }

    @Test
    fun `should set status bar color and font size`() {
        mockkStatic(StatusBarUtil::class) {
            mockkStatic(SdkUtils::class)
            every { SdkUtils.getSDKVersion() } returns Build.VERSION_CODES.LOLLIPOP
            mockkStatic(COUIVersionUtil::class)
            every { COUIVersionUtil.getOSVersionCode() } returns COUIVersionUtil.COUI_3_0
            mockkStatic(COUIDarkModeUtil::class)


            every { COUIDarkModeUtil.isNightMode(activity) } returns true
            StatusBarUtil.setStatusBarTransparentAndBlackFont(activity)
            verify { win.addFlags(any()) }

            every { COUIVersionUtil.getOSVersionCode() } returns 0
            StatusBarUtil.setStatusBarTransparentAndBlackFont(activity)
            verify { win.addFlags(any()) }

            every { COUIDarkModeUtil.isNightMode(activity) } returns false
            StatusBarUtil.setStatusBarTransparentAndBlackFont(activity)
            verify { COUIDarkModeUtil.isNightMode(activity) }

            every { SdkUtils.getSDKVersion() } returns Build.VERSION_CODES.M
            StatusBarUtil.setStatusBarTransparentAndBlackFont(activity)

            every { activity.resources.getBoolean(any()) } returns false
            StatusBarUtil.setStatusBarTransparentAndBlackFont(activity)

            every { COUIVersionUtil.getOSVersionCode() } returns 4
            StatusBarUtil.setStatusBarTransparentAndBlackFont(activity)
        }
    }

    @After
    fun afterTests() {
        unmockkAll()
    }
}