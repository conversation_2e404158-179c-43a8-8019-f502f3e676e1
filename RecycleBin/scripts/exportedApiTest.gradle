if (!org.gradle.internal.os.OperatingSystem.current().isWindows()) {
    throw new GradleException("ERROR: exportedApiTest.gradle only support Windows at current!")
}

class StringUtils {
    static String removePrefix(String originStr, String prefix) {
        if (originStr.startsWith(prefix)) {
            return originStr.substring(prefix.length())
        }
        return originStr
    }

    static String removeSuffix(String originStr, String suffix) {
        if (originStr.endsWith(suffix)) {
            return originStr.substring(0, originStr.length() - suffix.length())
        }
        return originStr
    }
}

class AdbBundleParams {
    private final String bundleOption
    private final ArrayList<String> bundleParams = new ArrayList<>()

    AdbBundleParams(String bundleOption) {
        this.bundleOption = bundleOption
    }

    AdbBundleParams putBoolean(String key, boolean value) {
        bundleParams.add("$bundleOption $key:b:$value")
        return this
    }

    AdbBundleParams putString(String key, String value) {
        bundleParams.add("$bundleOption $key:s:$value")
        return this
    }

    AdbBundleParams putInt(String key, int value) {
        bundleParams.add("$bundleOption $key:i:$value")
        return this
    }

    AdbBundleParams putLong(String key, long value) {
        bundleParams.add("$bundleOption $key:l:$value")
        return this
    }

    AdbBundleParams putFloat(String key, float value) {
        bundleParams.add("$bundleOption $key:f:$value")
        return this
    }

    AdbBundleParams putDouble(String key, double value) {
        bundleParams.add("$bundleOption $key:d:$value")
        return this
    }

    String build() {
        final builder = new StringBuilder()
        final last = bundleParams.last()
        bundleParams.each {
            builder.append(it)
            if (it != last) {
                builder.append(" ")
            }
        }
        return builder.toString()
    }
}

@SuppressWarnings("unused")
class AdbBundleOutput {
    private static final ADB_BUNDLE_PREFIX = "Bundle[{"
    private static final ADB_BUNDLE_SUFFIX = "}]"
    private static final ADB_BUNDLE_ENTRY_SPLIT = ",\\s"
    private static final ADB_BUNDLE_VALUE_SPLIT = "="

    private final String extraPrefix
    private final Map<String, String> bundleValues = new LinkedHashMap<>()

    AdbBundleOutput() {
        this("Result: ")
    }

    AdbBundleOutput(String extraPrefix) {
        this.extraPrefix = extraPrefix
    }

    void parse(String adbOutput) {
        def bundleSeq = adbOutput.replace("\r", "").replace("\n", "")
        final prefix = "$extraPrefix$ADB_BUNDLE_PREFIX"
        bundleSeq = StringUtils.removePrefix(bundleSeq, prefix)
        final suffix = ADB_BUNDLE_SUFFIX
        bundleSeq = StringUtils.removeSuffix(bundleSeq, suffix)
        final bundleEntries = bundleSeq.split(ADB_BUNDLE_ENTRY_SPLIT)
        bundleEntries.each {
            final entry = it.split(ADB_BUNDLE_VALUE_SPLIT)
            bundleValues.put(entry[0], entry[1])
        }
    }

    boolean getAsBoolean(String key) {
        return bundleValues[key].toBoolean()
    }

    String getAsString(String key) {
        return bundleValues[key]
    }

    int getAsInt(String key) {
        return bundleValues[key].toInteger()
    }

    long getAsLong(String key) {
        return bundleValues[key].toLong()
    }

    float getAsFloat(String key) {
        return bundleValues[key].toFloat()
    }

    double getAsDouble(String key) {
        return bundleValues[key].toDouble()
    }

    void fillToParams(AdbBundleParams params) {
        fillToParams(params, null, null)
    }

    void fillToParams(AdbBundleParams params, Set<String> intKeys, Set<String> floatKeys) {
        def forceIntKeys = intKeys
        if (forceIntKeys == null) {
            forceIntKeys = new HashSet<>()
        }
        def forceFloatKeys = floatKeys
        if (forceFloatKeys == null) {
            forceFloatKeys = new HashSet<>()
        }
        bundleValues.each { key, value ->
            if (value.isLong()) {
                if (forceIntKeys.contains(key) && value.isInteger()) {
                    params.putInt(key, value.toInteger())
                } else {
                    params.putLong(key, value.toLong())
                }
            } else if (value.isDouble()) {
                if (forceFloatKeys.contains(key) && value.isFloat()) {
                    params.putFloat(key, value.toFloat())
                } else {
                    params.putDouble(key, value.toDouble())
                }
            } else if (value.toLowerCase() == "true" || value.toLowerCase() == "false") {
                params.putBoolean(key, value.toBoolean())
            } else {
                params.putString(key, value)
            }
        }
    }
}

ext {
    testDocPath = "/storage/emulated/0/Download/TEST_README.md"
    testDirPath = "/storage/emulated/0/Download/TestDir"
    testInDirDocPath = "/storage/emulated/0/Download/TestDir/TEST_IN_DIR_README.md"
}

def getLocalSdkPath() {
    final hasLocalProperties = project.rootProject.file('local.properties').exists()
    if (!hasLocalProperties) {
        return null
    }
    Properties localProperties = new Properties()
    localProperties.load(project.rootProject.file('local.properties').newDataInputStream())
    return localProperties.get("sdk.dir")
}

def execWindowsCmd(String script) {
    return execWindowsCmd(script, projectDir)
}

def execWindowsCmd(String script, File workspace) {
    final out = new ByteArrayOutputStream()
    exec {
        executable "cmd"
        args "/c", script
        standardOutput out
        errorOutput System.err
        workingDir workspace
        ignoreExitValue = true
    }
    return out.toString()
}

def findAdb() {
    final findResult = execWindowsCmd("where adb")
    if (findResult.endsWith("${File.separator}adb.exe")) {
        return findResult
    }
    final sdkPath = getLocalSdkPath()
    if (sdkPath == null || sdkPath.isEmpty()) {
        throw new GradleException("ERROR! Can not find adb")
    }
    return "${sdkPath}${File.separator}platform-tools${File.separator}adb.exe"
}

def execAdb(String adbCmd) {
    return execAdb(adbCmd, projectDir)
}

def execAdb(String adbCmd, File workspace) {
    String adb = null
    try {
        adb = ext.adbPath
    } catch (Throwable ignored) {
    }
    if (adb == null) {
        adb = findAdb()
        println("${project.path}: exportedApiTest: find adb: $adb")
        ext.adbPath = adb
    }
    println("${project.path}: exportedApiTest: exec adb $adbCmd")
    final script = "$adb $adbCmd"
    final result = execWindowsCmd(script, workspace)
    println("${project.path}: exportedApiTest: ---> $result")
    return result
}

def mkdirInDevice(String dirPath) {
    final findResult = execAdb("shell ls $dirPath")
    if (findResult.isEmpty()) {
        execAdb("shell mkdir -p $dirPath")
    }
}

def pushTestDocFile() {
    final inDeviceDirPath = ext.testDirPath
    final inDeviceDocPath = ext.testDocPath
    final inDeviceInDirDocPath = ext.testInDirDocPath
    final srcDocPath = ".${File.separator}scripts${File.separator}README.md"
    execAdb("shell rm -rf $inDeviceDirPath")
    execAdb("shell rm -rf $inDeviceDocPath")
    mkdirInDevice(inDeviceDirPath)
    // 直接复用README.md文件作为被测试文件
    execAdb("push $srcDocPath $inDeviceDocPath")
    execAdb("push $srcDocPath $inDeviceInDirDocPath")
    // 通过shell命令操作文件绕过了MediaStore，需要通知MediaStore刷新
    execAdb("shell am broadcast -a android.intent.action.MEDIA_SCANNER_SCAN_FILE -d file://$inDeviceDocPath")
    execAdb("shell am broadcast -a android.intent.action.MEDIA_SCANNER_SCAN_FILE -d file://$inDeviceInDirDocPath")
}

def moveTestDocToRecycle(String inDeviceDocPath, String recyclePath) {
    execAdb("shell mv $inDeviceDocPath $recyclePath")
    // 通过shell命令操作文件绕过了MediaStore，需要通知MediaStore刷新
    execAdb("shell am broadcast -a android.intent.action.MEDIA_SCANNER_SCAN_FILE -d file://$inDeviceDocPath")
}

/**
 * 参考RecycleBin/scripts/README.md中的“1. 获取回收站的被删除文件暂存目录”
 */
def getRecycleDirectory() {
    final script = "shell content call " +
            "--uri content://com.oplus.filemanager.recyclebin.db.exported.recycleprovider " +
            "--method getRecycleDirectory"
    final result = execAdb(script)
    final bundle = new AdbBundleOutput()
    bundle.parse(result)
    return bundle.getAsString("resultRecycleDir")
}

/**
 * 参考RecycleBin/scripts/README.md中的“2. 生成回收站数据记录的ContentValues”
 */
def obtainTestDocRecycleValues(String inDeviceDocPath) {
    final checkIsFileScript = "if [ -f $inDeviceDocPath ]; then echo true; else echo false; fi"
    final isFile = execAdb("shell $checkIsFileScript").toBoolean()
    final params = new AdbBundleParams("--extra")
    params.putString("originPath", inDeviceDocPath).putBoolean("isFile", isFile)
    if (isFile) {
        final testDocSize = execAdb("shell stat -c '%s' $inDeviceDocPath").toLong()
        params.putLong("fileSize", testDocSize)
    }
    final testDocLastModified = execAdb("shell stat -c '%Y' $inDeviceDocPath").toLong()
    params.putLong("lastModified", testDocLastModified * 1000)
    final script = "shell content call " +
            "--uri content://com.oplus.filemanager.recyclebin.db.exported.recycleprovider " +
            "--method obtainRecycleContentValues " + params.build()
    final result = execAdb(script)
    final bundle = new AdbBundleOutput()
    bundle.parse(result)
    return bundle
}

/**
 * 参考RecycleBin/scripts/README.md中的“3. 插入回收站数据记录至回收站数据库”
 */
def insertTestDocToRecycleDb(AdbBundleOutput testDocValues) {
    final params = new AdbBundleParams("--bind")
    testDocValues.fillToParams(params)
    final script = "shell content insert " +
            "--uri content://com.oplus.filemanager.recyclebin.db.exported.recycleprovider/internal/file " +
            params.build()
    execAdb(script)
}

def pushTestDocAndExecRecycle() {
    // Push测试文件到设备的正常目录中
    pushTestDocFile()
    // 创建回收站临时暂存目录
    final recycleDir = getRecycleDirectory()
    mkdirInDevice(recycleDir)
    final testDocs = new LinkedHashSet<String>()
    testDocs.add(ext.testDocPath)
    testDocs.add(ext.testDirPath)
    testDocs.each { inDeviceDocPath ->
        // 获取删除测试文件的回收站数据库记录
        final testDocValues = obtainTestDocRecycleValues(inDeviceDocPath)
        // 将设备内的测试文件移动到回收站数据库记录中指定的回收路径(自动重命名)
        moveTestDocToRecycle(inDeviceDocPath, testDocValues.getAsString("recycle_path"))
        // 插入回收站数据库记录完成模拟删除测试文件至回收站
        insertTestDocToRecycleDb(testDocValues)
    }
}

def isAppInstalled(String packageName) {
    final result = execAdb("shell pm list packages | findstr $packageName")
    return !result.isEmpty()
}

/**
 * 从设备中pull已安装的APK到临时目录，卸载APK然后再重装之前pull出来的APK。
 * 该方法要求先执行[isAppInstalled]判定APK是否已安装
 * @param packageName 待重装的APP包名
 */
def reinstallApp(String packageName) {
    final installPath = StringUtils.removePrefix(execAdb("shell pm path $packageName"), "package:")
    final tmpApkDirPath = "$buildDir${File.separator}api_test"
    final tmpApkDir = new File(tmpApkDirPath)
    tmpApkDir.mkdirs()
    execAdb("pull $installPath", tmpApkDir)
    final temApkFile = new File(tmpApkDir, "base.apk")
    final tmpApkPath = temApkFile.absolutePath
    if (!temApkFile.exists()) {
        throw new GradleException("ERROR: Faild to pull temp apk from $installPath to $tmpApkPath")
    }
    execAdb("uninstall $packageName")
    execAdb("install $tmpApkPath")
    delete tmpApkDirPath
}

/**
 * 清除文管APP数据、授权状态和回收站临时暂存目录
 * 因为所有文件访问权限无法通过adb直接清除，用卸载重装APK的方式来间接清除
 */
def clearAppAndRecycleData() {
    final recycleDir = getRecycleDirectory()
    if (isAppInstalled("com.coloros.filemanager")) {
        reinstallApp("com.coloros.filemanager")
    }
    if (isAppInstalled("com.oneplus.filemanager")) {
        reinstallApp("com.oneplus.filemanager")
    }
    execAdb("shell rm -rf $recycleDir")
}

def ensureRootAdbDevice() {
    execAdb("root")
    // 已第二次adb root的输出结果来判断root状态，防止第一次执行root操作导致输出结果不一样
    final result = execAdb("root")
    if (result.contains("adbd is already running as root")) {
        return
    }
    throw new GradleException("ERROR: Only support root device. Pls connect a root device first.")
}

def ensureHasInstallTargetApp() {
    // 根据authorities查找provider来判断是否安装了能被测试的文管APP
    final authorities = "com.oplus.filemanager.recyclebin.db.exported.recycleprovider"
    final result = execAdb("shell dumpsys package providers | findstr $authorities")
    if (result.isEmpty()) {
        throw new GradleException("ERROR: Can not find target file manager app. Pls install first")
    }
}

def ensureHasUnlockedScreen() {
    final result = execAdb("shell dumpsys window policy | findstr screenState")
    if (result.contains("SCREEN_STATE_OFF")) {
        throw new GradleException("ERROR: Require unlock screen. Pls unlock device screen first.")
    }
}

def ensureTestDeviceState() {
    // 确保测试前adb已连接设备且为root状态，非root状态adb没有操作ExportedRecycleProvider的权限
    ensureRootAdbDevice()
    // 确保测试前已安装可被测试的文管APP
    ensureHasInstallTargetApp()
    // 确保测试前已解锁屏幕，部分ExportedRecycleProvider操作在锁屏状态下会被系统拦截
    ensureHasUnlockedScreen()
}

/**
 * 执行一次ExportedRecycleProvider API测试，测试前不清除现有文管APP数据和授权状态
 */
tasks.register("testRecycleBinExportedApi") {
    it.group = "o test"
    it.doFirst {
        ensureTestDeviceState()
    }
    it.doLast {
        pushTestDocAndExecRecycle()
    }
}

/**
 * 执行一次ExportedRecycleProvider API测试，且测试前清除现有文管APP数据和授权状态
 */
tasks.register("testRecycleBinExportedApiWithResetApp") {
    it.group = "o test"
    it.doFirst {
        ensureTestDeviceState()
    }
    it.doLast {
        clearAppAndRecycleData()
        pushTestDocAndExecRecycle()
    }
}