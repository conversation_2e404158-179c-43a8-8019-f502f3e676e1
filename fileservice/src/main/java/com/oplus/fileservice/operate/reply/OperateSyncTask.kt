/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OperateSyncTask
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/13 14:55
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/7/13       1.0      create
 ***********************************************************************/
package com.oplus.fileservice.operate.reply

import android.os.DeadObjectException
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Log
import com.oplus.fileservice.impl.FileServiceRunnable
import com.oplus.oworksdk.MsgSyncClient
import com.oplus.oworksdk.OworkSyncClientBuilder
import com.oplus.oworksdk.callback.SyncMessageResponseListener
import com.oplus.oworksdk.callback.SyncStateCallback
import java.util.UUID
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import kotlin.collections.HashSet

/**
 * Synchronize device file operations to remote.
 */
class OperateSyncTask(
    private val operateType: Int,
    private val paths: HashSet<String?>
) : FileServiceRunnable("$PREFIX$operateType") {

    private val countDown = CountDownLatch(1)

    override fun runInternal() {
        Log.d(TAG, "runInternal -> operateType = $operateType ; path = $paths")
        val msgSyncClient =
            OworkSyncClientBuilder.getInstance()?.build(MyApplication.sAppContext.applicationContext) as? MsgSyncClient
        if (msgSyncClient == null) {
            Log.d(TAG, "runInternal -> msgSyncClient is null.")
            return
        }
        try {
            msgSyncClient.open()
        } catch (e: DeadObjectException) {
            Log.d(TAG, "DeadObjectException ${e.message}")
        }
        msgSyncClient.registSyncStateCallback(object : SyncStateCallback {
            override fun onClose() {
                Log.d(TAG, "runInternal -> register SyncState onClose")
                countDown.countDown()
            }

            override fun onOpen() {
                Log.d(TAG, "runInternal -> register SyncState onOpen")
                Log.d(TAG, "runInternal -> ready = ${msgSyncClient.getConnectState()}")
                /**
                 * For test when the switch is open, send the message.
                 */
                if (msgSyncClient.getConnectState() < CONNECT_WAIT_WEB) {
                    Log.d(TAG, "syncResult -> sync client is not ready.")
                    countDown.countDown()
                    return
                }
                val id = "${System.currentTimeMillis()}_${UUID.randomUUID()}"
                val syncInfo = generateReplyInfoById(id, operateType, paths)
                msgSyncClient.syncSingle(id, syncInfo, object : SyncMessageResponseListener {
                    override fun onFinished(state: Int) {
                        Log.d(TAG, "syncMap -> onFinished = $state")
                        countDown.countDown()
                    }
                })
            }
        })

        try {
            countDown.await(SYNC_RESULT_TIMEOUT, TimeUnit.SECONDS)
        } catch (e: InterruptedException) {
            Log.e(TAG, "runInternal -> await error = ${e.message}")
        }
    }

    companion object {
        private const val TAG = "OperateSyncTask"
        private const val PREFIX = "operate-sync-"

        private const val WEB_CONNECTED = 3
        private const val CONNECT_WAIT_WEB = 2
        private const val SYNC_RESULT_TIMEOUT = 10L
    }
}