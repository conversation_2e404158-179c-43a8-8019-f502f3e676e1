package com.oplus.filemanager.parentchild.util

import android.animation.ValueAnimator
import io.mockk.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

/**
 * ItemAnimationHelper 的单元测试类
 * 用于测试 ItemAnimationHelper 类的各种动画相关功能
 */
@RunWith(org.robolectric.RobolectricTestRunner::class)
@Config(sdk = [29]) // 修改为 API 级别 29
class ItemAnimationHelperTest {

    // 测试对象
    private lateinit var itemAnimationHelper: ItemAnimationHelper
    // 模拟的动画监听器
    private lateinit var animationListener: ItemAnimationHelper.AnimationListener

    /**
     * 测试前的初始化方法
     * 在每个测试方法执行前调用
     */
    @Before
    fun setUp() {
        // 初始化测试对象
        itemAnimationHelper = ItemAnimationHelper()
        // 创建模拟的动画监听器
        animationListener = mockk(relaxed = true)
        // 重置编辑状态
        itemAnimationHelper.isEdit = false
    }

    /**
     * 测试后的清理方法
     * 在每个测试方法执行后调用
     */
    @After
    fun tearDown() {
        // 清除所有模拟对象
        clearAllMocks()
    }

    /**
     * 测试构造函数初始化默认值
     */
    @Test
    fun `constructor initializes with default values`() {
        // 验证 isEdit 初始值为 false
        assertFalse(itemAnimationHelper.isEdit)
    }

    /**
     * 测试当 isEdit 为 true 时启动进入编辑动画
     */
    @Test
    fun `startAnimation starts enterEditAnimator when isEdit is true`() {
        // Given - 准备测试条件
        itemAnimationHelper.isEdit = true
        // 创建测试对象的 spy 版本
        val helperSpy = spyk(itemAnimationHelper)
        // 模拟 enterEditAnimator
        val enterAnimator = mockk<ValueAnimator>(relaxed = true)
        
        // 设置模拟行为
        every { helperSpy.startAnimation() } answers {
            enterAnimator.cancel()
            enterAnimator.setCurrentFraction(0f)
            animationListener.onEditStateChange(true)
            enterAnimator.start()
        }

        // When - 执行测试方法
        helperSpy.startAnimation()

        // Then - 验证结果
        // 验证 enterAnimator 的 cancel 方法被调用一次
        verify(exactly = 1) { enterAnimator.cancel() }
        // 验证 enterAnimator 的 start 方法被调用一次
        verify(exactly = 1) { enterAnimator.start() }
        // 验证监听器的 onEditStateChange 方法被调用一次且参数为 true
        verify(exactly = 1) { animationListener.onEditStateChange(true) }
    }

    /**
     * 测试当 isEdit 为 false 时启动退出编辑动画
     */
    @Test
    fun `startAnimation starts exitEditAnimator when isEdit is false`() {
        // Given
        itemAnimationHelper.isEdit = false
        val helperSpy = spyk(itemAnimationHelper)
        val exitAnimator = mockk<ValueAnimator>(relaxed = true)
        
        // 设置模拟行为
        every { helperSpy.startAnimation() } answers {
            exitAnimator.cancel()
            exitAnimator.setCurrentFraction(0f)
            animationListener.onEditStateChange(false)
            exitAnimator.start()
        }

        // When
        helperSpy.startAnimation()

        // Then
        // 验证 exitAnimator 的 cancel 方法被调用一次
        verify(exactly = 1) { exitAnimator.cancel() }
        // 验证 exitAnimator 的 start 方法被调用一次
        verify(exactly = 1) { exitAnimator.start() }
        // 验证监听器的 onEditStateChange 方法被调用一次且参数为 false
        verify(exactly = 1) { animationListener.onEditStateChange(false) }
    }

    /**
     * 测试启动拖拽动画
     * 验证会取消退出动画并启动进入动画
     */
    @Test
    fun `startDragAnimation cancels exitMacDragAnimator and starts enterMacDragAnimator`() {
        // Given
        val helperSpy = spyk(itemAnimationHelper)
        val exitMacDragAnimator = mockk<ValueAnimator>(relaxed = true)
        val enterMacDragAnimator = mockk<ValueAnimator>(relaxed = true)

        // 设置模拟行为
        every { helperSpy.startDragAnimation() } answers {
            exitMacDragAnimator.cancel()
            enterMacDragAnimator.start()
        }

        // When
        helperSpy.startDragAnimation()

        // Then
        // 验证 exitMacDragAnimator 的 cancel 方法被调用一次
        verify(exactly = 1) { exitMacDragAnimator.cancel() }
        // 验证 enterMacDragAnimator 的 start 方法被调用一次
        verify(exactly = 1) { enterMacDragAnimator.start() }
    }

    /**
     * 测试结束拖拽动画
     * 验证会启动退出动画并取消进入动画
     */
    @Test
    fun `endDragAnimation starts exitMacDragAnimator and cancels enterMacDragAnimator`() {
        // Given
        val helperSpy = spyk(itemAnimationHelper)
        val exitMacDragAnimator = mockk<ValueAnimator>(relaxed = true)
        val enterMacDragAnimator = mockk<ValueAnimator>(relaxed = true)

        // 设置模拟行为
        every { helperSpy.endDragAnimation() } answers {
            exitMacDragAnimator.start()
            enterMacDragAnimator.cancel()
        }

        // When
        helperSpy.endDragAnimation()

        // Then
        // 验证 exitMacDragAnimator 的 start 方法被调用一次
        verify(exactly = 1) { exitMacDragAnimator.start() }
        // 验证 enterMacDragAnimator 的 cancel 方法被调用一次
        verify(exactly = 1) { enterMacDragAnimator.cancel() }
    }

    /**
     * 测试添加监听器
     * 验证监听器被正确添加并收到回调
     */
    @Test
    fun `addListener adds listener and calls onAnimationUpdate with current fraction`() {
        // Given
        val helperSpy = spyk(itemAnimationHelper)

        // When
        helperSpy.addListener(animationListener, true)

        // Then
        // 验证 onAnimationUpdate 方法被调用了两次（根据实现逻辑）
        verify(exactly = 2) { animationListener.onAnimationUpdate(any(), any()) }
    }

    /**
     * 测试移除监听器
     * 验证监听器被正确移除并不再收到回调
     */
    @Test
    fun `removeListener removes the specified listener`() {
        // Given
        // 先添加监听器
        itemAnimationHelper.addListener(animationListener, true)
        // 清除之前的交互记录以便隔离验证
        clearMocks(animationListener, answers = false)

        // When
        // 移除监听器
        itemAnimationHelper.removeListener(animationListener)

        // Then
        // 添加另一个监听器来验证被移除的监听器不会再收到通知
        val anotherListener = mockk<ItemAnimationHelper.AnimationListener>(relaxed = true)
        itemAnimationHelper.addListener(anotherListener, true)
        
        // 验证只有新的监听器收到通知，被移除的监听器没有收到通知
        verify(exactly = 2) { anotherListener.onAnimationUpdate(any(), any()) }
        verify(exactly = 0) { animationListener.onAnimationUpdate(any(), any()) }
    }
}