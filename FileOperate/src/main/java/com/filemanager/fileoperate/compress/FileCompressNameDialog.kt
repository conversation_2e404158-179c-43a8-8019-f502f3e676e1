/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FileCompressDialog.kt
 ** Description: Input file name when file compress
 ** Version: 1.0
 ** Date: 2020/3/9
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.fileoperate.compress

import android.content.Context
import android.content.DialogInterface
import android.text.TextUtils
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.constants.CommonConstants.CUSTOM_NAME_LEN
import com.filemanager.common.fileutils.fetchFileName
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.RenameErrorTipUtil
import com.filemanager.fileoperate.R
import com.filemanager.fileoperate.base.BaseFileNameDialog
import java.io.File

class FileCompressNameDialog(val context: Context, val compressBean: FileCompressObserver.NameDialogBean) : BaseFileNameDialog(context) {
    companion object {
        private const val TAG = "FileCompressNameDialog"
    }

    private var mCompressBean: FileCompressObserver.NameDialogBean? = compressBean

    override fun show() {
        mDialogBuilder = COUIAlertDialogBuilder(context, KtViewUtils.getDialogStyle()).apply {
            setTitle(com.filemanager.common.R.string.compress_file)
            setMessage(com.filemanager.common.R.string.compress_prompt)
            setNegativeButton(com.filemanager.common.R.string.dialog_cancel) { _, _ -> callbackWhenCloseDialog() }
            setOnCancelListener { callbackWhenCloseDialog() }
            setPositiveButton(com.filemanager.common.R.string.confirm, null)
            setWindowGravity(getBottomAlertDialogWindowGravity(context))
            setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(context))
            setBlurBackgroundDrawable(true)
        }
        mDialog = mDialogBuilder!!.show()
        initViews {
            if (!TextUtils.isEmpty(getInputValue())) {
                val targetFile = File(mCompressBean!!.destFile.mData, FileActionCompress.getCompressFileName(getInputValue()))
                Log.d(TAG, "BUTTON_POSITIVE click: file=${targetFile.absolutePath}")
                if (targetFile.exists()) {
                    showWarningNotice(NOTICE_COMPRESS_FILE_EXISTS)
                } else {
                    mDialog?.let {
                        mCompressBean?.resultListener?.onClick(it,
                            DialogInterface.BUTTON_POSITIVE, getInputValue())
                        mCompressBean = null
                        dismiss()
                    }
                }
            } else {
                showWarningNotice(NOTICE_COMPRESS_FILENAME_EMPTY)
            }
        }
        mOppoEditText?.setHint(com.filemanager.common.R.string.enter_file_name)
        initFileName()
    }

    private fun callbackWhenCloseDialog() {
        mDialog?.let { dialog ->
            compressBean.resultListener?.onClick(dialog)
            mCompressBean = null
            mOppoEditText?.let { editText ->
                hideSoftInput(editText)
            }
        }
    }

    private fun initFileName() {
        if (checkInputText()) {
            return
        }
        if (mCompressBean!!.sourceFiles.isNullOrEmpty().not()) {
            var firstFile = mCompressBean!!.sourceFiles[0]
            var fileName = firstFile.mDisplayName
            if (fileName.isNullOrEmpty()) {
                return
            }
            if (firstFile.mIsDirectory.not()) {
                val splitDotPosition: Int = fileName!!.lastIndexOf(".")
                if ((splitDotPosition > 0) && (splitDotPosition < fileName.length)) {
                    fileName = fileName.substring(0, splitDotPosition)
                }
            }
            val ext = ".zip"
            val newFile = fetchFileName(mCompressBean!!.destFile, fileName, ext)
            fileName = newFile?.mDisplayName?.removeSuffix(ext)?.let {
                if (it.length >= CUSTOM_NAME_LEN) {
                    it.subSequence(0, CUSTOM_NAME_LEN).toString()
                } else it
            }
            setInitFileNameInfo(newFile?.mDisplayName?.length ?: 0)
            mOppoEditText?.run {
                setText(fileName)
                selectAll()
            }
        }
    }

    override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
        (FileActionCompress.getCompressFileName(s.toString()) ?: "").let {
            super.onTextChanged(it, start, before, count)
        }
    }

    override fun assembleFileName(): String {
        return FileActionCompress.getCompressFileName(mOppoEditText?.text?.toString()?.trim()) ?: ""
    }

    private fun showWarningNotice(type: Int) {
        val resources = context.resources ?: return
        val notice = when (type) {
            NOTICE_COMPRESS_FILENAME_EMPTY -> resources.getString(com.filemanager.common.R.string.file_name_null)
            NOTICE_COMPRESS_FILE_EXISTS -> resources.getString(com.filemanager.common.R.string.toast_file_exist)
            RenameErrorTipUtil.ERROR_FILE_NAME_TOO_LONG -> resources.getString(com.filemanager.common.R.string.input_over_upper_limit)
            NOTICE_COMPRESS_FILE_NAME_CANT_START_WITH_POINT -> resources.getString(com.filemanager.common.R.string.error_has_dot_at_first)
            else -> resources.getString(com.filemanager.common.R.string.unsupported_input_the_char)
        }
        mInputView?.showError(notice)
    }
}