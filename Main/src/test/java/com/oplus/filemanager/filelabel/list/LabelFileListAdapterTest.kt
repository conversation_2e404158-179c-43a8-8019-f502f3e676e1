/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.filelabel.list
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/5/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.filelabel.list

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.WindowUtils
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class LabelFileListAdapterTest {

    @MockK
    private lateinit var adapter: LabelFileListAdapter

    @Before
    fun before() {
        MockKAnnotations.init(this)
        mockkStatic(WindowUtils::class)
        every { WindowUtils.supportLargeScreenLayout(any()) }.returns(false)
        every { adapter.mContext }.returns(mockk())
    }

    @After
    fun after() {
        unmockkStatic(WindowUtils::class)
    }

    @Test
    fun `should return 1 when getItemKey if file is null`() {
        every { adapter.getItemKey(any(), any()) } answers { callOriginal() }
        assertEquals(1, adapter.getItemKey(BaseFileBean(), 1))
    }

    @Test
    fun `should return 10 when getItemKey if file is empty`() {
        every { adapter.getItemKey(any(), any()) } answers { callOriginal() }
        val file = BaseFileBean()
        file.mData = ""
        assertEquals(10, adapter.getItemKey(file, 10))
    }

    @Test
    fun `should return n when getItemKey if file is not empty`() {
        every { adapter.getItemKey(any(), any()) } answers { callOriginal() }
        val file = BaseFileBean()
        file.mData = "path"
        assertEquals(3433509, adapter.getItemKey(file, 1))
    }

    @Test
    fun `should return 0 when getItemCount`() {
        every { adapter.mFiles } returns arrayListOf()
        every { adapter.itemCount } answers { callOriginal() }
        assertEquals(0, adapter.itemCount)
    }

    @Test
    fun `should return 1 when getItemCount`() {
        every { adapter.mFiles } returns arrayListOf(BaseFileBean())
        every { adapter.itemCount } answers { callOriginal() }
        assertEquals(1, adapter.itemCount)
    }

    @Test
    fun `should return 1 when getItemViewType if position is 0`() {
        val file = BaseFileBean()
        file.mFileWrapperViewType = 1
        every { adapter.mOldFiles } returns arrayListOf()
        every { adapter.mFiles } returns arrayListOf(file)
        every { adapter.getItemViewType(any()) } answers { callOriginal() }
        assertEquals(1, adapter.getItemViewType(0))
    }

    @Test
    fun `should return 2 when getItemViewType if position is 0`() {
        val file = BaseFileBean()
        every { adapter.mOldFiles } returns arrayListOf()
        every { adapter.mFiles } returns arrayListOf(file)
        every { adapter.mScanViewModel } returns KtConstants.SCAN_MODE_GRID
        every { adapter.getItemViewType(any()) } answers { callOriginal() }
        assertEquals(KtConstants.SCAN_MODE_GRID, adapter.getItemViewType(0))
    }
}