/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : StorageUiStateTest
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/4/21 10:08
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/4/21       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.main.ui.uistate

import com.filemanager.common.constants.KtConstants
import org.junit.Assert
import org.junit.Test

class StorageUiStateTest {

    @Test
    fun testStorageUiStateEquals() {
        val storageUiState = StorageUiState()
        val fakeUiState = Any()
        Assert.assertFalse(storageUiState == fakeUiState)
        Assert.assertTrue(storageUiState == storageUiState)
        Assert.assertFalse(storageUiState.hashCode() == fakeUiState.hashCode())
        val phoneStorage = Storage.PhoneStorage(TOTAL_SIZE, AVAILABLE_SIZE)
        val otgStorage = Storage.OTGStorage(listOf(), Pair(TOTAL_SIZE, AVAILABLE_SIZE), KtConstants.STATE_MOUNTED)
        val cloudDiskStorage = Storage.CloudDiskStorage(isSingedIn = false)
        val storageItems = mutableListOf<Storage>()
        storageItems.add(phoneStorage)
        storageItems.add(otgStorage)
        storageUiState.storageItems = storageItems
        val fakeStorageUiState = StorageUiState()
        val fakeStorageItems = mutableListOf<Storage>()
        fakeStorageItems.add(phoneStorage)
        fakeStorageUiState.storageItems = fakeStorageItems
        Assert.assertFalse(storageUiState == fakeStorageUiState)
        fakeStorageItems.add(cloudDiskStorage)
        fakeStorageUiState.storageItems = fakeStorageItems
        Assert.assertFalse(storageUiState == fakeStorageUiState)
        fakeStorageItems.remove(cloudDiskStorage)
        val cloudDiskStorageWithSingedIn = Storage.CloudDiskStorage(isSingedIn = true)
        fakeStorageItems.add(cloudDiskStorageWithSingedIn)
        fakeStorageUiState.storageItems = fakeStorageItems
        Assert.assertFalse(storageUiState == fakeStorageUiState)
        fakeStorageItems.remove(cloudDiskStorageWithSingedIn)
        fakeStorageItems.add(otgStorage)
        fakeStorageUiState.storageItems = fakeStorageItems
        Assert.assertFalse(storageUiState == fakeStorageUiState)
        fakeStorageUiState.storageItems = storageItems
        Assert.assertFalse(storageUiState == fakeStorageUiState)
    }

    companion object {
        private const val TOTAL_SIZE = 1000L
        private const val AVAILABLE_SIZE = 500L
    }
}