<?xml version="1.0" encoding="utf-8"?>
<com.filemanager.common.view.HoverSmoothRoundedConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/action_to_private_safe"
    android:layout_width="@dimen/main_private_safe_width"
    android:layout_height="@dimen/main_storage_height"
    android:background="@drawable/bg_card_common"
    app:corner_radius="?attr/couiRoundCornerM"
    app:default_background="true"
    app:hover_radius="?attr/couiRoundCornerM"
    app:solid_color="?attr/couiColorBackground">

    <TextView
        android:id="@+id/private_safe_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dimen_16dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:layout_marginTop="@dimen/dimen_16dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:text="@string/private_safe"
        android:textSize="16sp"
        android:textAppearance="@style/couiTextButtonM"
        android:textColor="?attr/couiColorLabelPrimary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/private_safe_image"
        android:layout_width="@dimen/dimen_18dp"
        android:layout_height="@dimen/dimen_18dp"
        android:layout_marginEnd="@dimen/dimen_16dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/private_safe_title"
        app:layout_goneMarginEnd="@dimen/dimen_14dp" />

        <TextView
            android:id="@+id/private_safe_space_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="top"
            android:layout_marginTop="@dimen/dimen_4dp"
            android:layout_marginStart="@dimen/dimen_16dp"
            android:layout_marginEnd="@dimen/dimen_14dp"
            app:layout_constraintEnd_toEndOf="@id/private_safe_image"
            app:layout_constraintStart_toStartOf="@id/private_safe_title"
            app:layout_constraintTop_toBottomOf="@id/private_safe_title"
            android:ellipsize="end"
            android:maxLines="2"
            android:minHeight="@dimen/dimen_28dp"
            android:text="@string/private_safe_desc"
            android:textSize="12sp"
            android:textAppearance="@style/couiTextBodyXS"
            android:textColor="@color/coui_color_label_secondary" />

    <ImageView
        android:id="@+id/iv_private_safe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_private_safe"
        android:layout_marginBottom="@dimen/dimen_16dp"
        android:contentDescription="@null"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/private_safe_space_desc"
        app:layout_constraintVertical_bias="1"
        app:layout_constraintStart_toStartOf="@id/private_safe_title"
        app:layout_constraintBottom_toBottomOf="parent" />
</com.filemanager.common.view.HoverSmoothRoundedConstraintLayout>