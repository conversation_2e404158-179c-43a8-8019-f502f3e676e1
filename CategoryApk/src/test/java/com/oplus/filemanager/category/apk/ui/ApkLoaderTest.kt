package com.oplus.filemanager.category.apk.ui

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import com.filemanager.common.DiskLruCache
import com.filemanager.common.base.loader.BaseUriLoader
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MediaHelper
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.KtCacheManager
import com.filemanager.common.wrapper.MediaFileWrapper
import com.oplus.filemanager.interfaze.main.IMain
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.Injector
import io.mockk.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * ApkLoader的单元测试类
 * 用于测试ApkLoader类的各种功能和行为
 */
@RunWith(RobolectricTestRunner::class)
@Config(sdk = [29])
class ApkLoaderTest {

    // 测试用变量声明
    private lateinit var apkLoader: ApkLoader
    private val mockContext: Context = mockk()  // 模拟的Context对象
    private val mockCursor: Cursor = mockk()    // 模拟的Cursor对象
    private val mockDiskLruCache: DiskLruCache = mockk()  // 模拟的DiskLruCache对象
    private val mockFileWrapper: MediaFileWrapper = mockk()  // 模拟的MediaFileWrapper对象
    private val mockIMain: IMain = mockk()  // 模拟的IMain接口对象

    /**
     * 测试前的初始化方法
     * 用于设置测试环境和模拟对象
     */
    @Before
    fun setUp() {
        // 模拟静态对象
        mockkObject(KtAppUtils)
        mockkObject(KtCacheManager)
        mockkObject(FileTimeUtil)
        mockkObject(Injector)

        // 设置模拟对象的行为
        every { mockContext.applicationContext } returns mockContext
        every { KtCacheManager.getDiskLruCache(any(), any(), any()) } returns mockDiskLruCache
        every { KtAppUtils.getInstalledMap() } returns hashMapOf()
        every { Injector.injectFactory<IMain>() } returns mockIMain
    }

    /**
     * 测试后的清理方法
     * 用于释放资源和取消模拟
     */
    @After
    fun tearDown() {
        unmockkAll()  // 取消所有模拟
    }

    /**
     * 测试getUri方法 - 使用自定义Uri的情况
     * 验证当传入自定义Uri时，getUri方法返回正确的Uri
     */
    @Test
    fun testGetUri_WithCustomUri() {
        val testUri = mockk<Uri>()  // 创建测试用的Uri
        apkLoader = ApkLoader(mockContext, testUri, null, 0, false)
        assertEquals(testUri, apkLoader.getUri())  // 验证返回的Uri与传入的Uri相同
    }

    /**
     * 测试getUri方法 - 使用null Uri的情况
     * 验证当传入null Uri时，getUri方法返回默认的MediaStore Uri
     */
    @Test
    fun testGetUri_WithNullUri() {
        apkLoader = ApkLoader(mockContext, null, null, 0, false)
        assertEquals(MediaStore.Files.getContentUri("external"), apkLoader.getUri())  // 验证返回默认Uri
    }

    /**
     * 测试getSelection方法 - 使用自定义SQL的情况
     * 验证当传入自定义SQL时，getSelection方法返回正确的SQL
     */
    @Test
    fun testGetSelection_WithCustomSql() {
        val testSql = "test_sql"  // 测试用的SQL语句
        apkLoader = ApkLoader(mockContext, null, testSql, 0, false)
        assertEquals(testSql, apkLoader.getSelection())  // 验证返回的SQL与传入的SQL相同
    }

    /**
     * 测试getSelection方法 - 使用null SQL的情况
     * 验证当传入null SQL时，getSelection方法返回非空的默认SQL
     */
    @Test
    fun testGetSelection_WithNullSql() {
        apkLoader = ApkLoader(mockContext, null, null, 0, false)
        assertTrue(apkLoader.getSelection()?.isNotEmpty() == true)  // 验证返回的SQL不为空
    }

    /**
     * 测试createFromCursor方法 - 处理非APK文件的情况
     * 验证当Cursor包含非APK文件时，方法返回null
     */
    @Test
    fun testCreateFromCursor_WithNonApkFile() {
        apkLoader = ApkLoader(mockContext, null, null, 0, false)
        // 设置Cursor返回非APK文件的数据
        every { mockCursor.getInt(0) } returns 1
        every { mockCursor.getString(1) } returns "test.txt"
        every { mockCursor.getString(2) } returns "test.txt"
        every { mockCursor.getLong(3) } returns 0L
        every { mockCursor.getLong(4) } returns 0L
        every { mockCursor.getString(5) } returns null
        assertNull(apkLoader.createFromCursor(mockCursor, null))  // 验证返回null
    }

    /**
     * 测试createFromCursor方法 - 处理目录的情况
     * 验证当Cursor包含目录时，方法返回null
     */
    @Test
    fun testCreateFromCursor_WithDirectory() {
        apkLoader = ApkLoader(mockContext, null, null, 0, false)
        // 设置Cursor返回目录的数据
        every { mockCursor.getInt(0) } returns 1
        every { mockCursor.getString(1) } returns "test.apk"
        every { mockCursor.getString(2) } returns "test.apk"
        every { mockCursor.getLong(3) } returns 0L
        every { mockCursor.getLong(4) } returns 0L
        every { mockCursor.getString(5) } returns null
        every { mockCursor.isNull(6) } returns false
        every { mockCursor.getInt(6) } returns MediaHelper.MEDIA_FORMAT_DIR
        assertNull(apkLoader.createFromCursor(mockCursor, null))  // 验证返回null
    }

    /**
     * 测试preHandleBeforeBackground方法
     * 验证方法是否正确调用了KtAppUtils和KtCacheManager的相关方法
     */
    @Test
    fun testPreHandleBeforeBackground() {
        mockkStatic("com.filemanager.common.MyApplication")
        every { com.filemanager.common.MyApplication.appContext } returns mockContext
        apkLoader = ApkLoader(mockContext, null, null, 0, false)
        apkLoader.preHandleBeforeBackground()  // 执行测试方法
        verify { KtAppUtils.getInstalledMap() }  // 验证是否调用了getInstalledMap
        verify { KtCacheManager.getDiskLruCache(any(), any(), any()) }  // 验证是否调用了getDiskLruCache
    }
}