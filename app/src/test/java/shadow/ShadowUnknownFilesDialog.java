/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: ShadowUnknownFilesDialog.java
 ** Description:
 ** Version: 1.0
 ** Date : 2019/9/03
 ** Author: JiaBo Lan
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * JiaBo.Lan 2019/9/03      1.0     create
 ****************************************************************/
package shadow;

import android.content.Context;
import android.net.Uri;

import com.filemanager.fileoperate.open.UnknownFilesDialog;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(UnknownFilesDialog.class)
public class ShadowUnknownFilesDialog {
    @Implementation
    public void showUnknownFilesDialog(final Context context, final Uri uri, final boolean openFlag) {
    }
}
